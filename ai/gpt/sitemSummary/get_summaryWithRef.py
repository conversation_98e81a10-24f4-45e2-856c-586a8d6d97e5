import psycopg2
import requests
from multiprocessing import Pool


def get_API_token() -> str:
    client_id = "sb-1a817f6b-f648-447a-af21-eaa44c1fbc97!b93303|azure-openai-service-i057149-xs!b16730"
    client_secret = "6eef4618-ab0d-409e-aef5-ae01afdebce2$HNLTMW-JR35P3vrqe-Jm6G6ZjbU4eu-PoROBHqbql3I="

    params = {"grant_type": "client_credentials"}
    resp = requests.post(f"https://calm-build-poc-llm.authentication.sap.hana.ondemand.com/oauth/token",
                         auth=(client_id, client_secret),
                         params=params)
    token = resp.json()["access_token"]
    return token


def get_completion(msg):
    auth_token = get_API_token()
    svc_url = "https://azure-openai-serv-i057149.cfapps.sap.hana.ondemand.com"
    headers = {
        "Authorization": f"Bearer {auth_token}",
        "Content-Type": "application/json"
    }
    request_body = {
        "deployment_id": "gpt-4-turbo",
        "temperature": 1,

        "messages": [
            {"role": "system",
             "content": "Assume you are an SAP expert, particularly adept at helping customers convert an SAP ERP 6.0 "
                        "system to SAP S/4HANA. You are familiar with two proprietary terms. The first, SAP Notes, "
                        "which describe a software issue and its solution, including the symptoms, the cause of the "
                        "error, and the SAP release and support package level in which the error occurs. An SAP Note "
                        "may also include workarounds and links to support packages that solve the problem."
                        "An SAP Note may also reference to other SAP Notes and help documents The "
                        "second, Simplification Item, helps customers prepare for the transition to SAP S/4HANA and "
                        "SAP BW/4HANA by providing a description of all relevant changes that might have an impact "
                        "when converting from SAP ERP to SAP S/4HANA. A Simplification Item typically includes an ID, "
                        "Title, Business Impact SAP Note, and Activities. I will provide you with a Simplification "
                        "Item, including its corresponding Business Impact SAP Note, Activities, and other "
                        "information, and some Simplification Item may have no activity. Your task is to first "
                        "understand this Simplification Item, then give a summarize about this item only based on the "
                        "information I give."},
            {"role": "user", "content": msg}
        ]
    }
    response = requests.post(f"{svc_url}/api/v1/completions", headers=headers, json=request_body)
    return response.json()['choices'][0]['message']['content']


def find_no_summary_items():
    try:
        connection = psycopg2.connect(
            user="184024b4abe8",
            password="cce6e3ded99172321cf1dd7e681ae92",
            host="127.0.0.1",
            port="8861",
            database="CxgapCTAJQku"
        )

        cursor = connection.cursor()
        postgres_query = """ select "GUID", "itemInfo" from SITEM_SUMMARY_WITH_REF where "isNew" = false """

        cursor.execute(postgres_query)

        items = cursor.fetchall()
        print(f"find {len(items)} items have no summary")
        return items

    except (Exception, psycopg2.Error) as error:
        if (connection):
            print("Failed to find record from SITEM_SUMMARY_WITH_REF table", error)

    finally:
        if (connection):
            cursor.close()
            connection.close()
            print("PostgreSQL connection is closed")


def update_summary(guid, summary):
    try:
        connection = psycopg2.connect(
            user="184024b4abe8",
            password="cce6e3ded99172321cf1dd7e681ae92",
            host="127.0.0.1",
            port="8861",
            database="CxgapCTAJQku"
        )

        cursor = connection.cursor()
        postgres_query = """ update SITEM_SUMMARY_WITH_REF set "itemSummary" = %s, "isNew" = true where "GUID" = %s """

        cursor.execute(postgres_query, (summary, guid))
        connection.commit()
        print(f"update summary for {guid} success")

    except (Exception, psycopg2.Error) as error:
        if (connection):
            print("Failed to update record from SITEM_SUMMARY_WITH_REF table", error)

    finally:
        if (connection):
            cursor.close()
            connection.close()
            print("PostgreSQL connection is closed")


def get_sitem_summary(item):
    guid = item[0]
    summary = get_completion(item[1])
    print(f"get Summary Done for GUID: {guid}")
    update_summary(guid, summary)
    return guid, summary


if __name__ == '__main__':
    data = find_no_summary_items()
    with Pool() as p:
        final_result = p.map(get_sitem_summary, data)
    print(f"update finish for {len(final_result)} items")
