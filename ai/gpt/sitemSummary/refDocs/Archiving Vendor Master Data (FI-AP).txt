Archiving Vendor Master Data (FI-AP) | SAP Help PortalHomeSAP ERPFinancial Accounting (FI)Archiving Financial Accounting Data (FI)Archiving Vendor Master Data (FI-AP)Financial Accounting (FI)6.0 EHP8 LatestAvailable Versions: 6.0 EHP8 Latest  6.0 EHP7 Latest  6.0 EHP6 on HANA Latest * 6.0 EHP6 Latest * This product version is out of mainstream maintenance. The documentation is no longer regularly updated.For more information, see the Product Availability Matrix (PAM)EnglishAvailable Languages: English  German (Deutsch) This documentSearch Scopes:All SAP productsThis productThis documentAll of these words:Any of these words:This exact word or phrase:None of these words:ClearSearchAdvanced SearchTable of ContentsFavoriteTo mark this page as a favorite, you need to log in with your SAP ID.If you do not have an SAP ID, you can create one for free from the login page.Log onDownload PDFThe following PDF options are available for this document:Create Custom PDFShareTable of Contents Financial Accounting (FI)  Archiving Financial Accounting Data (FI)  Dependencies in Financial Accounting (FI)  Archiving Authorizations (FI)  Available Archiving Programs (FI)  Archiving Deferred Taxes Using FI_DEFTAX  Archiving Financial Accounting Documents (FI-GL, FI-AR, FI-AP)  Archiving FI Transaction Figures (FI-GL, FI-AR, FI-AP)  Archiving G/L Account Master Data (FI-GL)  Archiving Customer Master Data (FI-AR, SD)  Archiving Vendor Master Data (FI-AP)  Variant Settings for Writing (FI-AP)  Checks (FI-AP)  ILM-related Information for FI_ACCPAYB  Archiving Banks Using FI_BANKS  Archiving FI Check Data (FI-AR, FI-AP, FI-BL)  Archiving Payment Requests  Archiving Electronic Bank Data (FI-BL)  Archiving Cash Journal Documents (FI-BL)  Archiving Reorganization Plans  Archiving Payment Release List Items Using FI_FPRL  Archiving FI Asset Data (FI-AA)  Archiving Totals Records and Line Items (FI-SL)  Archiving Funds Management Data (FI-FM)  Highlighting results for "Archiving Vendor Master Data" Archiving Vendor Master Data (FI-AP) On this pageDefinitionUseStructureIntegration
Definition
 Archiving object FI_ACCPAYB is used to archive and delete vendor master data.

Use
 Vendor master data is stored and archived in three different areas:
 General data
 FI data (for a specific company code)
 MM data (purchasing organization-specific)

 To archive master data,  you must set the deletion flag in the master record . You can set this flag for a complete vendor or for individual company codes or purchasing organizations.  
 For more information about how to set the deletion flag, see
Archiving and Deleting Vendor Master Records
.

 In addition to the deletion flag, a series of other prerequisites must be met so that you can archive vendor master data. For more information about the checks performed by the system during archiving, see  Checks (FI-AP)
.


Structure
Tables
 Archiving object FI_ACCPAYB is used to archive data from different tables. For more information about how to display the table names for individual archiving objects, see  Tables and Archiving Objects
.


Function/Report Assignment
 Function

 Report


 Write

 FI_ACCPAYB_WRI


 Delete

 FI_ACCPAYB_DEL





Integration
Displaying Archived Vendor Master Data
 To display individual documents for archiving object FI_ACCPAYB using the
Archive Information System
 , you require an information structure that has been created based on one of the following standard SAP field catalogs:

 
 SAP_FI_ACCPAY_1 (vendor master data FI)

 
 SAP_FI_ACCPAY_2 (vendor master data SD)


 Each information structure must be active and configured correctly.

 For more information about information structures, see
Using the Archive Information System
 .

Note
 You can use archiving object FI_ACCPAB in  Information Lifecycle Management
. You must have activated the corresponding business functions to do this. The system then also shows the
ILM Actions group box. You can use these actions to carry out archiving, for example, where the retention periods stored in the Information Retention Manager can be evaluated. Additionally, you can create snapshots (copies) of data or destroy data that matches the requirements. For more information, see  ILM Enhancements for Data Archiving
,
ILM Actions in the Write Program
.



On this pageDefinitionUseStructureIntegrationWas this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedback Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedbackCopyrightDisclaimerPrivacy StatementLegal DisclosureTrademarkTerms of UseCookie PreferencesAccessibility and SustainabilitySystem StatusAsk a question about the SAP Help PortalFind us onFollow us on FacebookSAP on FacebookFollow us on YouTubeSAP on YouTubeFollow us on LinkedInSAP on LinkedInFollow us on InstagramSAP on InstagramShare

