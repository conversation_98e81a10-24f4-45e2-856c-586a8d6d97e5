Data Transfer Intermediate Storage (DTIS) | SAP Help PortalHomeSAP BW/4HANASAP BW/4HANAData AcquisitionLoading DataData Transfer ProcessData Transfer Intermediate Storage (DTIS)SAP BW/4HANA1.0 SP08Available Versions: 2023 SPS01  2023  2021 SPS08  2021 SPS07  2021 SPS06  2021 SPS05  2021 SPS04  2021 SPS03  2021 SPS02  2021 SPS01  2021  2.0 SP15  2.0 SP14  2.0 SP13  2.0 SP12  2.0 SP11  2.0 SP10  2.0 SP09  2.0 SP08  2.0 SP07  2.0 SP06  2.0 SP05  2.0 SP04  2.0 SP03  2.0 SP02  2.0 SP01  2.0  1.0 SPS20  1.0 SP12  1.0 SP11  1.0 SP10  1.0 SP09  1.0 SP08  1.0 SP07  1.0 SP06  1.0 SP05  1.0 SP04  1.0 SP03  1.0 SP02  1.0 SP01  1.0 EnglishAvailable Languages: English  German (Deutsch) ProductionStates:DraftProductionThis documentSearch Scopes:All SAP productsThis productThis documentAll of these words:Any of these words:This exact word or phrase:None of these words:ClearSearchAdvanced SearchTable of ContentsFavoriteThis page has been removed from your favorites.Download PDFThe following PDF options are available for this document:Full DocumentCreate Custom PDFFeedback You can now add comments and questions right into SAP documentation and start a conversation with SAP authors and experts. Don't show me againFeedbackShareEdit in IXIA CCMS Web You can now go directly to IXIA CCMS Web to edit the topic. Simply select Edit in IXIA CCMS Web link under the More… menu. Don't show me againMoreMetadata Analytics Subscribe Edit in IXIA CCMS WebTable of Contents SAP BW∕4HANA  Overview  Data Modeling  Data Acquisition  Source System  Working with Source Systems  DataSource  Working with DataSources  Data Provision Using Source Systems  Transformation  Creating Transformations  InfoSource  Creating InfoSources  Loading Data  Data Transfer Process  Data Transfer Intermediate Storage (DTIS)  Handling Data Records with Errors  Monitor  Creating a Data Transfer Process  Manual Data Entry  Analysis  Agile Information Access: BW Workspace  Configuration  Operation  Administration  Interfaces  Application Server for ABAP  Security Guide SAP BW∕4HANA  Data Transfer Intermediate Storage (DTIS)

The data transfer intermediate storage (DTIS) is a table with technical key. This is generated when a data transfer process (DTP) is activated, or when a SAP HANA
                           transformation is generated for each DTP source object upon generation.
                        
Only one data transfer intermediate storage is generated for an object that is used as a source in multiple DTPs.
The DTIS is used in the following cases:


For error handling
If error handling is activated, the DTIS is used as an error stack


To allow sorting on sources that cannot be read sorted
For sorting, the key is used that you define in the Extraction Grouped by setting (semantic grouping).
                              


To allow execution of the transformation in SAP HANA if the source does not support this
SAP HANA DataSources and the DataStore object (advanced) for example support executio of the transformation in SAP HANA. Filer
                                 or ODP DataSources on the other hand do not support SAP HANA execution.
                              


The DTIS is based on the data source; it thus stores records from the source.
The system decides whether or not to create a DTIS depending on the DTP source, the DTP target, the transformation, and the
                           settings in the data transfer process. It is created for example for file or ODP source systems that grouped extraction should
                           be performed for and/or for which the transformations should be executed in SAP HANA.
                        
All records extracted from the source are written to the DITS. They are handled according to the request status, as follows:


Red: If a DTP request has the status red, all data records in the DTIS are retained.


Green: If a DTP request has the status red, all data records are deleted apart from those with errors.


Deleted When a DTP request is deleted, the associated data records in the data transfer intermediate storage are also deleted.




Data transfer intermediate storage as error stack
The data transfer intermediate storage (DTIS) is used as a persistence layer for error handling in SAP BW∕4HANA (error stack). If error handling is activated for the DTP, records with errors are written at runtime to the data transfer
                                 intermediate storage. You use the data transfer intermediate storage to update the data to the target destination once the
                                 error is resolved.
                              
If error handling is activated, and there are data records with errors, you can call the data transfer intermediate storage
                                 and display and edit the data records in question. The authorizations for DTIS maintenance are checked using authorization
                                 object S_RS_DTP. You require activity 23 (Maintain DTP Definition). You can call the data transfer intermediate storage in
                                 the following ways:
                              


You can call it in the data transfer process editor by choosing Open DTIS Maintenance from the Update tab. The data records displayed in the DTIS are then restricted to the current requests for this DTP.
                                    


You can call it from the Request Monitor by choosing Error Stack.
                                    


With an error DTP, you can update the data records to the target manually or by means of a process chain. Once the data records
                                 have been successfully updated, they are deleted from the data transfer temporary store. If there are still any erroneous
                                 data records, they are written to the data transfer temporary store again in a new error DTP request.
                              



CommentsThere are no comments on this page. Leave a comment Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedback Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedbackCopyrightDisclaimerPrivacy StatementLegal DisclosureTrademarkTerms of UseCookie PreferencesAccessibility and SustainabilitySystem StatusAsk a question about the SAP Help PortalFind us onFollow us on FacebookSAP on FacebookFollow us on YouTubeSAP on YouTubeFollow us on LinkedInSAP on LinkedInFollow us on InstagramSAP on InstagramShare

