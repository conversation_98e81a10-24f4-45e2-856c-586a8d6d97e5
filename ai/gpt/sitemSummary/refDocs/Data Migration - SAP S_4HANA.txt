Data Migration | SAP Help PortalHomeSAP S/4HANAData MigrationData Migration2023 LatestAvailable Versions: 2025  2023 FPS02 (Oct 2024)  2023 Latest  2023 Latest  2022 Latest  2022 FPS02 (May 2023)  2022 FPS01 (Feb 2023)  2022 (Oct 2022)  2021 Latest  2021 FPS02 (May 2022)  2021 FPS01 (Feb 2022)  2021 (Oct 2021)  2020 Latest  2020 FPS02 (May 2021)  2020 FPS01 (Feb 2021)  2020 (Oct 2020)  1909 Latest  1909 FPS02 (May 2020)  1909 FPS01 (Feb 2020)  1909 (Sep 2019)  1809 Latest * 1809 FPS02 (May 2019) * 1809 FPS01 (Jan 2019) * 1809 (Sep 2018) * 1709 Latest * 1709 FPS02 (May 2018) * 1709 FPS01 (Jan 2018) * 1709 (Sep 2017) * 1610 Latest * 1610 SPS03 (Oct 2017) ** This product version is out of mainstream maintenance. The documentation is no longer regularly updated.For more information, see the Product Availability Matrix (PAM)EnglishAvailable Languages: English  German (Deutsch) ProductionStates:TestProductionDraftThis documentSearch Scopes:All SAP productsThis documentAll of these words:Any of these words:This exact word or phrase:None of these words:ClearSearchAdvanced SearchTable of ContentsFavoriteThis page has been removed from your favorites.Download PDFThe following PDF options are available for this document:Create Custom PDFFeedback You can now add comments and questions right into SAP documentation and start a conversation with SAP authors and experts. Don't show me againFeedbackShareEdit in IXIA CCMS Web You can now go directly to IXIA CCMS Web to edit the topic. Simply select Edit in IXIA CCMS Web link under the More… menu. Don't show me againMoreMetadata Analytics Subscribe Edit in IXIA CCMS WebTable of Contents Data Migration  Migrate Your Data - Migration Cockpit  Migration Objects for SAP S/4HANA  Data Migration Status  Situation Handling for Data Migration  Video Library for Data Migration  Migration Cockpit (Transaction LTMC) - Deprecated  Data MigrationOn this pageLanding PageQuick InfoNote AnalyzerLicense Note SAP S/4HANA

SAP enables data migration to SAP S/4HANA for customers coming from any legacy system. SAP S/4HANA customers can take advantage of
reliable migration approaches built into SAP S/4HANA using the SAP S/4HANA Migration Cockpit together with a set of predefined data
migration objects.

Landing Page

The following image map gives you an overview of useful links to resources about data migration. Depending on your level of
expertise, go to the category that is relevant for you. Choose an element to follow the link to the resource. As a beginner, we
recommend that you start with the material linked in Getting Started.



Quick Info
Use these SAP Help Portal aliases to access the following sections of our product assistance:


Type this in your browser...
To jump to...



http://help.sap.com/S4_OP_DM
this very topic: Data Migration


http://help.sap.com/S4_OP_MO
the entry topic: Available Migration Objects


http://help.sap.com/S4_OP_DM_STATUS
the entry topic: Data Migration Status



Note Analyzer
We recommend that you regularly check if all correction notes for the proper use of the SAP S/4HANA migration cockpit are implemented, including
the installation of add-on DMIS and the respective components in SAP S/4HANA. To check, run transaction
CNV_NA_MC. Also see SAP Note 3016862 .

License Note SAP S/4HANA
In addition to the standard options of using either a full-use version of SAP HANA (i.e. SAP HANA, enterprise edition) or the SAP tools
provided as Runtime Software with SAP HANA, runtime edition, it is possible to utilize a 3rd party ETL tool to populate the staging
tables for the SAP S/4HANA migration cockpit. 
In this case, the staging tables will be created by the SAP S/4HANA migration cockpit in a dedicated schema, either in the same
database tenant where SAP S/4HANA is installed or a separate dedicated database tenant. Such a schema can then be configured as a
remote database connection enabling connectivity and filled by a 3rd party ETL tool solely for the purpose of consumption by the
S/4HANA migration cockpit. 
For the avoidance of doubt, the tables may not be used for any kind of processing by SAP HANA outside of SAP S/4HANA migration cockpit,
including, but not limited to, calculation view modeling, custom SAP HANA Extended Application Services (XS) applications, and other
processing by SAP HANA engines. Furthermore, the use of such 3rd party tools to populate staging tables shall be limited to the
timeframe during which a migration project is active in the SAP S/4HANA system installed in the same SAP HANA database tenant.

On this pageLanding PageQuick InfoNote AnalyzerLicense Note SAP S/4HANACommentsThere are no comments on this page. Leave a comment Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedback Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedbackCopyrightDisclaimerPrivacy StatementLegal DisclosureTrademarkTerms of UseCookie PreferencesAccessibility and SustainabilitySystem StatusAsk a question about the SAP Help PortalFind us onFollow us on FacebookSAP on FacebookFollow us on YouTubeSAP on YouTubeFollow us on LinkedInSAP on LinkedInFollow us on InstagramSAP on InstagramShare

