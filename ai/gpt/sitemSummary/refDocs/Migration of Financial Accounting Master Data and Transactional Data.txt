Purpose | SAP Help PortalHomeData Migration with SAP S/4HANA Migration CockpitMigration of Financial Accounting Master Data and Transactional DataGeneral InformationPurposeMigration of Financial Accounting Master Data and Transactional DataSHIPAvailable Versions: SHIP  DEV ProductionStates:DraftProductionThis documentSearch Scopes:All SAP productsThis documentAll of these words:Any of these words:This exact word or phrase:None of these words:ClearSearchAdvanced SearchTable of ContentsFavoriteThis page has been removed from your favorites.Download PDFThe following PDF options are available for this document:Create Custom PDFFeedback You can now add comments and questions right into SAP documentation and start a conversation with SAP authors and experts. Don't show me againFeedbackShareEdit in IXIA CCMS Web You can now go directly to IXIA CCMS Web to edit the topic. Simply select Edit in IXIA CCMS Web link under the More… menu. Don't show me againMoreMetadata Analytics Subscribe Edit in IXIA CCMS WebTable of ContentsGeneral Information Purpose  Standard Migration Scenario  Migration Objects in Accounting  Legacy Data Transfer Options  Cut-Off Date for Migration of Accounting Data  Preparations in the Legacy System Preparations in the SAP S/4HANA or SAP S/4HANA Cloud System Migration of Financial Accounting Data Subsequent ActivitiesAppendix Purpose
This guide describes how to migrate financial accounting master data and transactional data from an existing customer system to an SAP S/4HANA or
SAP S/4HANA Cloud system, using the file upload approach in the SAP S/4HANA Migration Cockpit. For ease of reference, financial accounting
master data and transactional data will be referred to as data in this document.
As an option, data transfer to SAP S/4HANA and SAP S/4HANA Cloud with staging tables is also possible via the SAP S/4HANA Migration Cockpit. For
more information, refer to the product assistance Data Migration (SAP S/4HANA) or Data Migration (SAP S/4HANA Cloud).
CommentsThere are no comments on this page. Leave a comment Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedback Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedbackCopyrightDisclaimerPrivacy StatementLegal DisclosureTrademarkTerms of UseCookie PreferencesAccessibility and SustainabilitySystem StatusAsk a question about the SAP Help PortalFind us onFollow us on FacebookSAP on FacebookFollow us on YouTubeSAP on YouTubeFollow us on LinkedInSAP on LinkedInFollow us on InstagramSAP on InstagramShare

