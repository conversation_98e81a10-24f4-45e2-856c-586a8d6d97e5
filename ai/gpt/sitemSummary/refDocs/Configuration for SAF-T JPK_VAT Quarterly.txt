Configuration for SAF-T Quarterly VAT Records and Return (SAF-T JPK_VAT) | SAP Help PortalHomeSAP S/4HANA CloudPolandGeneral Functions...Document and Reporting ComplianceSAF-TSAF-T Quarterly VAT Records and Return (SAF-T JPK_VAT)Configuration for SAF-T Quarterly VAT Records and Return (SAF-T JPK_VAT)Poland2402.1Available Versions: 2408 Latest  2402.2  2402.1  2402  2308.4  2308.3  2308.2  2308.1  2308  2302.4 * 2302.3 * 2302.2 * 2302.1 * 2302 * 2208.3 * 2208.2 * 2208.1 * 2208 * 2202.4 * 2202.3 * 2202.2 * 2202.1 * 2202 * 2111.1 * 2111 * 2108.1 * 2108 * 2105 * 2102 ** This product version is out of mainstream maintenance. The documentation is no longer regularly updated.For more information, see the Product Availability Matrix (PAM)ProductionStates:ProductionDraftTestThis documentSearch Scopes:All SAP productsThis productThis documentAll of these words:Any of these words:This exact word or phrase:None of these words:ClearSearchAdvanced SearchTable of ContentsFavoriteThis page has been removed from your favorites.Download PDFThe following PDF options are available for this document:Create Custom PDFFeedback You can now add comments and questions right into SAP documentation and start a conversation with SAP authors and experts. Don't show me againFeedbackShareEdit in IXIA CCMS Web You can now go directly to IXIA CCMS Web to edit the topic. Simply select Edit in IXIA CCMS Web link under the More… menu. Don't show me againMoreMetadata Analytics Subscribe Edit in IXIA CCMS WebTable of Contents Poland  General Functions  Master Data  Document and Reporting Compliance  Electronic Customer Invoices (B2G)  Electronic Supplier Invoices (B2G)  Electronic Customer Invoices (B2B)  Electronic Supplier Invoices (B2B)  EC Sales List  G/L Account Balances and Line Items  Supplier Balances and Line Items  Customer Balances and Line Items  Overdue Invoices  VAT Return  SAF-T  SAF-T Invoices (SAF-T JPK_FA)  SAF-T Accounting Ledgers (SAF-T JPK_KR)  SAF-T Monthly VAT Records and Return (SAF-T JPK_VAT)  SAF-T Quarterly VAT Records and Return (SAF-T JPK_VAT)  Configuration for SAF-T Quarterly VAT Records and Return (SAF-T JPK_VAT)  SAF-T Inventory (SAF-T JPK_MAG)  SAF-T Bank Statements (SAF-T JPK_WB)  Read Access Logging Configurations in SAF-T for Poland  Balance Sheet/Income Statement for Poland Posting Period  Balance Sheet/Income Statement for Poland Quarterly  Balance Sheet/Income Statement for Poland Yearly  Cash Flow Statement (Old)  Cash Flow Statement (New)  Fixed Asset Forms Poland  Withholding Tax Reporting  Excessive Delays  Tax Reporting and Tax Fulfillment Date  Finance  Sales  Configuration for SAF-T Quarterly VAT Records and Return (SAF-T JPK_VAT)


You, as the key user for configuration, can check and make changes to configuration settings or create new ones if needed in your configuration environment. For more information, see Configuration Environment of SAP S/4HANA Cloud. To configure the SAF-T quarterly JPK_VAT report, in your configuration environment, use the search function to open and make settings in the following activities:


Setting Up Your Statutory Reporting configuration activity
For detailed information about the steps involved in setting up your statutory reports, see Setting Up Your Statutory Reporting.
Find below the details of the default configuration provided for the report. Note that, you may change the default settings based on your requirements:


Reporting entity: PL_RPG_ENT

Report category: PL_SAFT_VAT_QUARTERLY

Periodicity of the report category:








Field
Sample Value




From
01.01.2017


Offset
25


Fiscal Year Variant
K0


Notification Period (Days)
0


Is Adhoc
No


TimeUnit
Days





Properties of reporting activities








Field
Sample Value




Activity Key

PL_SAFT_VAT_JPK_V7K_ND_V1_S
PL_SAFT_VAT_JPK_V7K_V1_S


Valid From
01.01.2020


Use As Of
01.01.2020


Use Until
31.12.2021


Offset (Days)
0


Notify
0


Submission Mode
M - Only Manual Submission is Possible












Field
Sample Value




Activity Key

PL_SAFT_VAT_JPK_V7K_ND_V2_S
PL_SAFT_VAT_JPK_V7K_V2_S


Valid From
01.01.2022


Use As Of
01.01.2022


Use Until
<empty>


Offset (Days)
0


Notify
0


Submission Mode
M - Only Manual Submission is Possible





Validity of organizational unit:








Valid From
Comment




01.01.2017
 





Organizational units assigned to reporting entity:









Organizational Unit
Sample Value
Leading




COMPANYCODE
2610
Yes






Define Generic Settings for SAF-T configuration activity
Your system is pre-configured with settings that allow you to create the Poland SAF-T reports. In the Define Generic Settings for SAF-T configuration step, make changes to predelivered configuration settings or create new settings if needed.


Company Code: The system displays the company codes that comprise the reporting entity for which you must submit this report. For test runs, you can change the proposed values, but for a real run, keep the proposed values as they are.

Start Date and End Date: The start and end date of the validity for the settings you make here.

Main/Alt Account: Set the type of Chart of Accounts to be used for your company code.

GL View: Set whether the reporting is done under the New G/L (value GL View) or the Classic G/L (value Entry View).

REGON: The business number (National Economy Register number or Statistical Identification Number)

District, Sub District: The place where the entity is located (part of the address). This is the company office's district (Powiat) and sub-district (Gmina).

Tax Office Code: The code of the tax office to which the company submits SAF-T reports.

Email Address: The email address for the person who handles related SAF-T reporting at your company.


Map Tax Codes to SAF-T Elements configuration activity
Set up mapping and define parameters for tax codes relevant for SAF-T reporting.

Define Special Mappings for SAF-T Elements configuration activity
In case of special SAF-T reporting cases, you can define additional parameters for tax codes.

If you use time-dependent taxes (TDT) for Poland, make settings as follows:


After time-dependent tax calculation has been activated, you can create new tax codes in the Define Tax Codes for Sales and Purchases configuration activity.
For more information, see Defining Tax Codes for Time-Dependent Taxes .

Maintain the tax account determination for the new tax codes in the Automatic Account Determination configuration activity.



CommentsThere are no comments on this page. Leave a comment Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedback Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedbackCopyrightDisclaimerPrivacy StatementLegal DisclosureTrademarkTerms of UseCookie PreferencesAccessibility and SustainabilitySystem StatusAsk a question about the SAP Help PortalFind us onFollow us on FacebookSAP on FacebookFollow us on YouTubeSAP on YouTubeFollow us on LinkedInSAP on LinkedInFollow us on InstagramSAP on InstagramShare

