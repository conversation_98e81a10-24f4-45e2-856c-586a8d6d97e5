




Capabilities and Features  | SAP Data Services









































Loading...


Javascript must be enabled for the correct page display











Skip to Content



Call us atChina400-619-0727United States******-872-1727Or see our complete list of local country numbersChat nowGet live help and chat with an SAP representative.Contact usSend us your comments, questions, or feedback.





Contact Us

Contact us



Chat now




Contact us

 








HomeAll ProductsSAP Business Technology Platform SAP Data ServicesFeatures/SAP Business Technology Platform /SAP Data ServicesSAP Data ServicesFeaturesOverviewFeaturesTechnical InformationGet Started 







SAP Data Services FeaturesRequest a demoRequest a quote



Join us to explore the future of data, AI, and planning.

Watch now










Data IntegrationIntegrate, connect, and process structured or unstructured critical data from SAP or third-party sources within Big Data or enterprise solutions deployed on premise or in the cloud.
Show moreGain access to your data anytime, anywhereSupport databases, applications, files, transports, and unstructured content across 31 languages by connecting to new or popular data sources. You can also tap into our partners’ expertise to access mainframe data sources and address captured data changes for certain sources.

Integrate tightly with SAP applicationsBenefit from native access to SAP Business Suite applications. SAP Data Services understands the application model and accesses relevant metadata. Plus, it reads data based on programming languages, such as ABAP, IDocs, BAPI, RFC, and SAP extractors.

Interconnect with SAP Data HubOrchestrate data flows from SAP Data Services through the SAP Data Intelligence solution. This approach provides computing node and connector options.
Stay connected to third-party solutionsReceive native support for third-party data sources, including Microsoft SQL Server, IBM DB2, IBM Informix, Oracle, HP Vertica, MySQL, and Netezza. You can also support adapters for Apache Hive, HTTP, JDBC, JMS, MongoDB, and OData. Furthermore, Big Data and cloud support can be expanded with Microsoft Azure, Big Data Services, Impala, Cassandra, OData, and Apache Hive.

Optimize SAP Data Services for SAP HANAApply a built-in, intelligent optimizer that converts data flow with a choice of approaches: extract, load, and transform (ELT); a push down of all operations to the target database; or extract, transform, and load (ETL). With the ELT approach, SAP Data Services leverages the processing power of SAP HANA for maximum performance.

Fine-tune data replication and transformationTake advantage of multiple and unique data capture mechanisms with replicate, transform, and load (RTL) concepts.



Data QualityTransform, cleanse, match, and consolidate data by understanding the impact of quality problems on all downstream systems and applications.Show moreProvide a user-friendly experienceAccelerate time to deployment by designing, testing, debugging, editing, maintaining, running, and monitoring data integration and data quality from one interface, with one product, on one environment, and through one sign-on. 
Deliver ready-to-use transformationsInterpret, standardize, correct, enrich, match, and consolidate your customer and operational information assets by extending existing capabilities with user-defined changes and customer functions. 
Build trust in your dataReveal a single version of the truth by defining and standardizing your intelligence with built-in address and data cleansing features that uncover quality issues, expose hidden problems, and identify untapped relationships.



Data ProfilingImprove performance and scale from one server to many to meet high-volume data needs with parallel processing, grid computing, and bulk data loading. Show moreEnable unlimited scalability and high availabilityDesign data services that run in a top-down, right-to-left manner to automate queries with multiple parallel threads. You can improve performance when executing the same job, whether in multiple parallel processes on one server or with multiple servers.
Treat your data as a high-value assetSecure data access with a public key and maintain data relevance and relationships while keeping sensitive information confidential, anonymous, and compliant. You can support encryption, decryption, and masking as part of the regular transformation of the ETL process. 



Processing of Text DataUnlock meaning from unstructured text data to increase business insights by supporting a wide variety of SAP and third-party information sources.
Show moreExtract and understand your unstructured textsPull insights from key entities, facts, relationships, sentiment, tokens, and linguistic artifacts by distilling unstructured documents from 220 different file and text formats, as well as semistructured data in 31 languages.



Enable smart data integration and smart data quality in SAP HANAData services enabled by SAP HANAMigrate, integrate, cleanse, and process data in SAP HANA with SAP HANA smart data integration and SAP HANA smart data quality software.

Learn more


Explore the latest release highlights and product road mapLearn about new featuresCheck out the most recent functions and enhancements within SAP Data Services.

Explore the latest releaseProduct road mapView our road map to explore your current options, as well as planned innovations and future features and functions to enhance your data intelligence capabilities.
View the road map





Cookie Preferences



Quick LinksSustainability ManagementSmall and Midsize EnterprisesSAP Trust CenterSAP InsightsSAP CommunityDeveloperSupport PortalAbout SAPCompany InformationWorldwide DirectoryInvestor RelationsCareersNews and PressEventsCustomer StoriesNewsletterSite InformationPrivacyTerms of UseLegal DisclosureCopyrightTrademarkSitemapText ViewCookie PreferencesContact usChina400-619-0727United States******-872-1727Or see our complete list of local country numbersContact usChat nowFind us on











Back to top


















































