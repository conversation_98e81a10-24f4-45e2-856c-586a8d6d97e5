MM - Purchasing info record with conditions | SAP Help PortalHomeSAP S/4HANA CloudMigration Objects for SAP S/4HANA CloudMigration Objects for SAP S/4HANA Cloud2308.4Available Versions: 2308.4  2308.3  2308.2  2308.1  2308  2302.4 * 2302.3 * 2302.2 * 2302.1 * 2302 * 2208.3 * 2208.2 * 2208.1corr * 2208.1 * 2208 * 2202.4 * 2202.3 * 2202.2 * 2202.1 * 2202 * 2111.1 * 2111 * 2108.1 * 2108 * 2105 * 2102 ** This product version is out of mainstream maintenance. The documentation is no longer regularly updated.For more information, see the Product Availability Matrix (PAM)EnglishAvailable Languages: English  Chinese Simplified (简体中文)  French (Français)  German (Deutsch)  Japanese (日本語)  Portuguese for Brazil (Português do Brasil)  Russian (Русский)  Spanish (Español) ProductionStates:DraftTestProductionThis documentSearch Scopes:All SAP productsThis productThis documentAll of these words:Any of these words:This exact word or phrase:None of these words:ClearSearchAdvanced SearchFavoriteThis page has been removed from your favorites.Download PDFThe following PDF options are available for this document:Create Custom PDFFeedback You can now add comments and questions right into SAP documentation and start a conversation with SAP authors and experts. Don't show me againFeedbackShareEdit in IXIA CCMS Web You can now go directly to IXIA CCMS Web to edit the topic. Simply select Edit in IXIA CCMS Web link under the More… menu. Don't show me againMoreMetadata Analytics Subscribe Edit in IXIA CCMS WebTable of Contents MM - Purchasing info record with conditionsOn this pagePurposeIn ScopeOut of ScopeSupported FeaturesPrerequisitesMapping InstructionsTasksPost-ProcessingFurther Information


Available Migration Objects in SAP S/4HANA Cloud.




Restriction

For restrictions relevant for RITA (Registration for Indirect Taxation Abroad), please see SAP Note 2907372 .




Purpose









Business Object Component/Area

Purchasing (MM-PUR)



Business Object Type

Master data



Business Object Definition

A source of information for the procurement of a certain product from a certain vendor.



Migration Approach

Staging Table



Business Catalog Needed for Creation

SAP_MM_BC_IR_PROCESS_PC



Custom Field Support

Not applicable








In Scope
Migration to SAP S/4HANA Cloud is supported.


General Data; only to create info record with product specific or product group

             The following Info Categories are supported:
             
Standard
Consignment
Subcontracting
Pipeline
For purchasing info records to product groups, only the standard and subcontracting purchasing info categories are supported.


General Text
Purchasing Organization Data - when you create an info record, you can choose the following options:
            

Purchasing info record which is only related to a purchasing organization. That means the price is the same for all plants.

Purchasing info record which is related to a plant. That means the price can be different for each plant.

Conditions - the condition type indicates, for example, whether during pricing, the system applies a price, a discount, a surcharge, or other pricing elements, such as freight costs and sales taxes. For each of these pricing elements, there is a condition type defined in the system.
Scales
Purchasing Organization Text





Out of Scope

Update Info Record is not supported. If an info record with a certain vendor and with a specific product exists or with a product group, it is not possible to enhance this record with purchasing organization data.





Supported Features
The following structures or features are supported in SAP S/4HANA Cloud:

General Data
General Text
Purchasing Organization Data
Conditions
Scales
Purchasing Organization Text





Prerequisites
The following objects must have already been maintained or migrated:


Product

Supplier





Mapping Instructions
Migration with identical combinations of product group and vendor leads to different purchasing info record numbers. The combination of product number and vendor is always unique. Migration always generates new info record numbers. External numbering is not supported.

Condition Unit
The condition unit determines whether the condition for a product is based on a percentage or an amount in a particular currency. The use of the condition unit depends on the condition type.
Examples:


If you create a condition that includes prices, you have to enter the currency that applies to this condition (for example, USD for US dollars).

If you create a condition based on percentage discounts or surcharges, leave the field empty or enter the percent sign. The system automatically enters a percent sign as the condition unit if you leave the field empty.



Mapping Structure Hierarchy











Level
Name




1
General Data (S_EINA), mandatory
 
 
 


2
 
General Text (S_EINA_TEXT)
 
 


2
 
Purchasing Organization Data (S_EINE)
 
 


3
 
 
Conditions (S_COND)
 


4
 
 
 
Scales (S_SCALES)


3
 
 
Purchasing Organization Text (S_EINE_TEXT)
 









Tasks


Navigate to the Data Migration to SAP S/4HANA from Staging (2Q2) scope item on SAP Signavio Process Navigator.

Download the test script from the Accelerators section.

Follow the procedure described in the 2Q2 test script.





Post-Processing

How to Validate Your Data in the System
Use the Data Migration Status app to validate your data in the system. Alternatively, use the following SAP Fiori app:









App:



Mass Changes to Purchasing Info Records (F2667)
Manage Purchasing Info Records (F1982)







Open the SAP Fiori apps reference library by choosing the app name above. Select your product and your product version. Then, open the Implementation Information tab and expand the Configuration section. There, you can find a list of business catalogs that can enable you to use this app. Choose the business catalog that fits your needs, and add it to your business role.





Further Information


For this migration object, SAP delivers one or more prefilled sample XML templates that help you in filling out your data migration template. Use this link to navigate to the Data Migration Template Samples for SAP S/4HANA Cloud (for customers and partners only).

SAP Knowledge Base Article 2776897  – SAP S/4HANA Migration Cockpit: Collective KBA Migration object "Purchasing info record"/ "Purchasing info record- extend existing record"




Available Migration Objects in SAP S/4HANA Cloud.


On this pagePurposeIn ScopeOut of ScopeSupported FeaturesPrerequisitesMapping InstructionsTasksPost-ProcessingFurther InformationCommentsThere are no comments on this page. Leave a comment Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedback Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedbackCopyrightDisclaimerPrivacy StatementLegal DisclosureTrademarkTerms of UseCookie PreferencesAccessibility and SustainabilitySystem StatusAsk a question about the SAP Help PortalFind us onFollow us on FacebookSAP on FacebookFollow us on YouTubeSAP on YouTubeFollow us on LinkedInSAP on LinkedInFollow us on InstagramSAP on InstagramShare

