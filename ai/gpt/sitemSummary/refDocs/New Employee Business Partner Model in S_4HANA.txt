New Employee Business Partner Model in S/4HANA | SAP Help PortalHomeSAP S/4HANACross-Application Enterprise Business FunctionsNew Employee Business Partner Model in S/4HANACross-Application Enterprise Business Functions2023 LatestAvailable Versions: 2023 Latest  2023 Latest  2022 Latest  2022 FPS02 (May 2023)  2022 FPS01 (Feb 2023)  2022 (Oct 2022)  2021 Latest  2021 FPS02 (May 2022)  2021 FPS01 (Feb 2022)  2021 (Oct 2021)  2020 Latest  2020 FPS02 (May 2021)  2020 FPS01 (Feb 2021)  2020 (Oct 2020)  1909 Latest  1909 FPS02 (May 2020)  1909 FPS01 (Feb 2020)  1909 (Sep 2019)  1809 Latest * 1809 FPS02 (May 2019) * 1809 FPS01 (Jan 2019) * 1809 (Sep 2018) * 1709 Latest * 1709 FPS02 (May 2018) * 1709 FPS01 (Jan 2018) * 1709 (Sep 2017) * 1610 Latest * 1610 SPS03 (Oct 2017) ** This product version is out of mainstream maintenance. The documentation is no longer regularly updated.For more information, see the Product Availability Matrix (PAM)EnglishAvailable Languages: English  German (Deutsch) This documentSearch Scopes:All SAP productsThis documentAll of these words:Any of these words:This exact word or phrase:None of these words:ClearSearchAdvanced SearchTable of ContentsFavoriteTo mark this page as a favorite, you need to log in with your SAP ID.If you do not have an SAP ID, you can create one for free from the login page.Log onDownload PDFThe following PDF options are available for this document:Create Custom PDFShareTable of Contents Cross-Application Enterprise Business Functions  SAP All-in-One Roles  Shared Services Framework  Integration of CRM and S/4HANA  Integration of SCM and S/4HANA  CATS classic: cProjects Details  CATS Classic for Concurrent Employment  CATS regular: cProjects Details  SAP for Defense & Security  Reporting Using XBRL Standards  Information Lifecycle Management: Tax Auditing  ILM-Based Deletion of Business Partner Master Data  ILM-Based Deletion of Customer and Supplier Master Data  Periodic Key Replacement for Payment Card Encryption  Integration of IPM and S/4HANA  SAP Geographical Enablement Framework for SAP S/4HANA  New Employee Business Partner Model in S/4HANA  External Resource in the New Employee Business Partner Model in S/4HANA  Workforce Integration in SAP S/4HANA Using SAP Master Data Integration Service  New Employee Business Partner Model in S/4HANAOn this pageUseIntegrationPrerequisitesFeaturesRelated Links  UseTechnical Data Technical Name of Business Function  /SHCM/EE_BP_1  Type of Business Function  Enterprise Business Function  Available From SAP S/4HANA 1809  Application Component  Cross-Application HCM Objects in S/4HANA (CA-HR-S4)  Required Business Function  Not relevant    You can use this business function to activate the new business partner (BP) model, which leads to new separate BP instances, BP roles and BP
identification types as well as a new BP relationship category and a new address usage type. In the new BP model, each Employment
(P/PERNR) is reflected by a separate BP with
role Employment and is directly linked to the corresponding BP in role Employee.
After having activated the business function, every employee is assigned to at least one BP in role Employment and exactly one in role
Employee. The BP in role Employment reflects a concrete contract (PERNR) and the BP in role
Employee reflects the natural person (= Central Person = CP).This separation enables you to update Employee BPs with a defined current
main employment, workplace address and according business user. For each BP in role Employment, you can flexibly decide which
employment-relevant roles shall be assigned (for example, Vendor). Thus, you can individually update the Employee and Employment role,
the according workplace address and the corresponding business user, and therefore reflect concurrent employment. Furthermore, this
allows you to use individual vendor-specific roles for the Employment instance and to synchronize the current main employment (private
address and bank details) in the Employee role as well as makes it easier for you to reflect global employment scenarios leading to a
change of the main contract.
The introduction of this new model solves the following issues you might have had with applying the previous BP model:



Missing relationship between BP in role Employee and BP in role Vendor


Missing ability to properly reflect concurrent and global employment of BPs in role Employee


Missing flexibility regarding BP handling in role Employee and thus missing data accuracy


Missing functionality to specify a main personnel number for BPs in role Employee



Due to its advantages over the old BP model, we recommend activating the business function in order to apply the new BP model in your
system.

Comparison: Old and New BP Model

Characteristics of old BP models (standard model, models VMODE1 and VMODE2)
Characteristics of new BP model



No Employment role
Existing Employment role


One common BP instance both for role Employee and Vendor exists per employee (exception: VMODE2); this instance
must always be synchronized with the assigned personnel number in the Vendor master data
(LFB1-PERNR)
Employee and Vendor are always separated; for every personnel number, separate BP Employment instances in
different roles are possible


Employee BP is represented by the lowest personnel number that was active during BP creation (although this
personnel number might now be inactive and another one is active), which might lead to the display/synchronization
of outdated data for the Employee BP
Current main employment is synchronized with the Employee BP, which results in an appropriate, up-to-date
reflection of private address, workplace address and bank details


Indirect relationship between Employee/Vendor BP and HR employee via vendor master data
(LFB1-PERNR)
Direct relationship between Employee BP instances and the corresponding Employment BP with Vendor role
instances


 
Direct relationship between Employee BP instances and the corresponding Employment BP with Customer role
instances


  Integration All existing accesses to BP data in the context of HR still work after the activation of the business function. If you use CDS views or APIs to
access BP data, you will receive the same results as before. All BP instances keep their semantics and roles. You can use your stored
BP instances as usual and can simply add new roles to existing or newly created BP instances.

Note
The activation of this business function is a requirement for the use of the business function Workforce Integration in SAP S/4HANA Using SAP Master Data Integration Service.
  Prerequisites


You have installed the following components as of the version mentioned:



Type of Component


Component


Required for the Following Features Only





Software Component


S4CORE 105








You have activated the switch /SHCM/EE_BP_1 in SAP S/4HANA. 


You have implemented the SAP Note 2991508 .

  Features

Adapted BP synchronization
The two existing BP synchronization reports /SHCM_RH_SYNC_BUPA_EMPL_SINGLE and
/SHCM/RH_SYNC_BUPA_FROM_EMPL have been adapted. If the business function is on, they
create/update BP instances both in the role Employee and in the role Employment as well as create the required
BUT050 associations. Additional role assignments are no longer executed by these
reports. They only store the information on adapted Employee and Employment roles of personnel numbers/BP instances to provide
them for the role handling process. For more information, see the related report documentations in transaction
SE38.
Also, HR master data for external resources (e.g. contingent workers) can be maintained. There are additional BP relationship
categories and role types available, as well as an additional BP identification type. For detailed information on external
resources in the new BP model, refer to External Resource in the New Employee Business Partner Model in S/4HANA.

Flexible role handling
The two new reports /SHCM/R_EMPL_HDLE_BPRLES_DELTA and
/SHCM/R_EMPL_HANDLE_BPROLES enable you to flexibly add certain roles to a BP instance
in the role Employment. For more information, see the related report documentations in transaction
SE38.

Additional BP identification types
There are two new BP identification types:



HCM032: Personnel Number (Main Employment)
This identification type is used for BP instances in the role Employee to reflect the personnel number specifying the
main employment (main private address and workplace communication data).


HCM033: Personnel Number (Employment)
This identification type is used for BP instances in role Employment and External Employment to link to the personnel
number specifying the employment (which provides data like address and bank details).



For more information, see the chapter Follow-Up Activities for Human Resources (Compatibility Packs) of the
current Installation Guide (SAP S/4HANA 2021, Implement  Installation Guide) and the corresponding Customizing documentations in the IMG under Personnel Management  SAP S/4HANA for Human Resources  Synchronize Business Partners with an Active Business Partner Integration.

Additional BP relationship category
There is a new BP relationship category:



HCM001: Has Employment
This relationship category is used to link a BP in the role Employee with a BP instance in the role Employment.



Additional role type
There is a new role type:



BUP010: Employment
This role type is always associated with BUP003 (Employee). This role is
created to reflect the data of employment.



For more information, see the chapter Follow-Up Activities for Human Resources (Compatibility Packs) of the
current Installation Guide (SAP S/4HANA 2022, Implement  Installation Guide) and the corresponding Customizing documentations in the IMG under Personnel Management  SAP S/4HANA for Human Resources  Synchronize Business Partners with an Active Business Partner Integration.

Additional address usage type
There is a new address usage type:



HCM003: Address of Main Employment
This address usage type is used to maintain the private address belonging to the main employment of the Employee
BP.





Related Links 

SAP S/4HANA, Implement  Installation Guide
https://blogs.sap.com/2021/08/16/new-employee-business-partner-data-model-in-sap-s-4hana-2020-on-premise/

 On this pageUseIntegrationPrerequisitesFeaturesRelated Links Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedback Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedbackCopyrightDisclaimerPrivacy StatementLegal DisclosureTrademarkTerms of UseCookie PreferencesAccessibility and SustainabilitySystem StatusAsk a question about the SAP Help PortalFind us onFollow us on FacebookSAP on FacebookFollow us on YouTubeSAP on YouTubeFollow us on LinkedInSAP on LinkedInFollow us on InstagramSAP on InstagramShare

