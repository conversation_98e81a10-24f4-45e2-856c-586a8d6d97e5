





SAP Security Optimization Services Portfolio

























How you can contact us:  Technical Assistance Request product support from SAP Non-Technical Assistance Request non-product support or provide feedback on SAP Support Portal site



Your browser does not support JavaScript. Some components may not be visible.



        Contact Us
    

×


                How you can contact us:
                
            




















Technical Assistance
Request product support from SAP





















Non-Technical Assistance
Request non-product support or provide feedback on SAP Support Portal site



















SAP Security Optimization Services Portfolio



SAP Security Optimization Service Portfolio ensures smooth operation of your SAP solution by taking action proactively, before severe security problems occur.











Best Practices



Keeping the security and availability of your SAP solution high is a tremendous value to your business. Analysis will:


decrease the risk of a system intrusion.
ensure the confidentiality of your business data.
ensure the authenticity of your users.
substantially reduce the risk of costly downtime due to wrong user interaction.

This SAP Security Optimization Services Portfolio topics:






Security Overview



This area is best if you are interested in general SAP Security Optimization Services and want an overview.







Security Topics Area



This area gives you an entry point into different topic areas such as Security Patch Management or Security Configuration Analysis.






Security Services, Tools and Information



This area provides you with an overview and links to further information on service offerings, including information and best practices, tools and self-services, remote and on-site service offerings, and more systematic engagement models.







Advisories
SAP Security Notes Advisory (February 2024)
Security Notes Webinar (pdf) (February 2024)
Security Notes Webinar, latest month only (pdf)
or landing page at
Help Portal - Support Content

How to register














Security Overview




                                                Security of your SAP Solution
                                            
                                                Secure Operations Map
                                            
                                                Enterprise Support Academy
                                               
SAP Solution Security Overview


To get an overview on the status of the security of your SAP solution, the recommended first steps are:


Have a look at the Security chapter of the SAP EarlyWatch Alert (EWA) report of your key systems and analyze the root cause of the findings. SAP Note 863362 describes the security checks in the EWA.
The SAP EarlyWatch Alert is an automatic diagnostic service that monitors the essential administrative areas of an SAP system. You can view the SAP EarlyWatch Alert at https://me.sap.com/app/ewaviewer 
Being a weekly service, SAP recommends that you consume the corresponding reports in the cloud-based SAP EarlyWatch Alert Workspace application once per week at https://me.sap.com/app/ewaworkspace
The SAP EarlyWatch Alert Workspace is the central landing page which gives a comprehensive overview on your system landscape regarding stability, configuration, hardware utilization, performance and security. You can view the alerts in the SAP EarlyWatch Alert Solution Finder at https://me.sap.com/app/ewasolutionfinder 
You can add a security status card to the SAP EarlyWatch Alert Workspace which summarizes the security related alerts as shown by the EWA Solution Finder.
In any case you require the authorization “Display Security Alerts in SAP EarlyWatch Alert Workspace” to access the security related alerts or the security status card.
Related blogs:
Hide & Snooze SAP EarlyWatch Alerts, June 23, 2023 
Choose your SAP EarlyWatch Alerts, Feb 24, 2023
 Best Practice – SAP EarlyWatch Alert, November 29, 2022
 Displaying Security Alerts in the SAP EarlyWatch Alert Workspace, Oct 01, 2019
 Using the SAP EarlyWatch Alert Solution Finder Effectively, April 17, 2019
 Cookbook for SAP EarlyWatch Alert Workspace, June 14, 2018
 SAP EarlyWatch Alert Workspace – gain an overview on your system landscape health, August 15, 2017
Consider additional tools like the Security Optimization Service, System Recommendations (respective the Recommended Notes in the SAP Maintenance Planner) or Change Diagnostics and Configuration Validation.

SAP Note 2253549 describes how to use our SAP Security Baseline as a starting point to define a corporate security baseline specific to your environment based on SAP recommendations.
As a MaxAttention or ActiveEmbedded Customer, go for a SAP Security Engagement which will provide you with an expert guided analysis and approach for your SAP landscape.
As an Enterprise Support Customer: Make use of the SAP Enterprise Support Value Map for Security.

More information on our tools, services and recommendations can be found in our SAP CoE Security Services Master Slide Decks:

SAP CoE Security Services – Overview
SAP CoE Security Services – Secure Operations Map
SAP CoE Security Services – Checking Security Configuration and Authorization
SAP CoE Security Services – Security Patch Process
SAP CoE Security Services – HANA Security Remote Service Content



Getting an overview on the Secure Operations Map


















The Secure Operations Map:

is a Reference Model to structure the broad area of security for content, discussions and as a basis for a 360° view on security – i.e. you can't “order” a block, but you can allocate discussions, needs and solutions to specific security areas.
focuses on the Operational Aspects of security – i.e. on the tasks and considerations which a customer or service provider has to take into account to maintain and operate their systems and landscapes in a secure manner.
is further interpreted in the concrete Context of SAP Systems, although the model also could be applied to non-SAP realms.

The tracks of the Secure Operations Map cover the following topics:






Environment
The Environment layer looks at the non-SAP technical environment of SAP cloud offerings, solutions and systems.

Network Security
It is important to have additional protection and monitoring mechanisms embedded into the underlying network infrastructure. Potential attacks need to be countered already through zoning concepts and network components like routers, firewalls or web application filters. Security-critical activities need to be monitored and countered by intrusion detection and prevention systems.
Note that communication security measures in SAP offerings and solutions as well as SAP infrastructure components like SAProuter or SAP Cloud Connector are not part of this block. They are handled in “Security Hardening” (e.g. SAP Cloud Connector, SAProuter), “Authentication & Single Sign-On” (e.g. RFC Gateway, SNC, TLS) or “Roles & Authorizations”.
Operating System & Database Security
When OS and DB are insufficiently configured or users are able to bypass access controls at that level, the protection of applications running on top is at risk. Corresponding security controls in focus include file system level permissions, database user security, tenant separation or data-at-rest encryption methods.
Note that databases from SAP are not part of this building block. They are addressed as specific projections on the Secure Operations Map, as several buildings apply with more details and SAP-specific content.
Client Security
Adversaries may attack client systems to get an entry point, inject bogus data in the traffic or subject the client to weird behavior, if not properly protected. This building block is about client-side controls such as secure maintenance, configuration, control and monitoring of the client or execution rules for browsers.
SAP clients like SAPGUI or SAP Business Client are not considered here but in the “Security Hardening” building block.








System
The System layer addresses the SAP platform layer which provides the foundation for all applications operated upon it. The integrity and robustness of this platform is key to ensure that application layer controls (e.g. the authorization systems) cannot be circumvented by lower level vulnerabilities (e.g. SQL injections made possible via insecure code).

Security Hardening
Security Hardening foremost deals with suitable secure settings of relevant system parameters and other configurations.
It also includes activation of security features and functionalities, which may be switched off initially for backward compatibility and migration purposes or which need specific setup and configuration (e.g. UCON)
It also includes hardening of SAP frontend components like SAPGUI or SAP Business Client and of SAP infrastructure components like SAProuter or SAP Cloud Connector.
Secure SAP Code
SAP is continuously doing a high investment to develop and deliver secure code to its customers. Nevertheless, security updates for already delivered code are required regularly due to new attacks and newly identified vulnerabilities.
SAP provides these security updates to its customers via Support Packages / Releases and via SAP Security Notes – published monthly on the SAP Security Patch Day.
Customers need to establish a corresponding Security Maintenance process to ensure a regular and suitable consumption of these security updates.
Security Monitoring & Forensics
With today's powerful attacks and complex landscapes, proactive security is absolutely required but not sufficient. It needs to be enhanced by reactive security mechanisms, which are able to identify security weaknesses and breaches and thus allow to properly counter them. This includes review and validation activities as well as life monitoring of system operations and triggering of appropriate countermeasures in case of an attack or suspicious system behaviors.
Logs and support are also required for forensics in retrospect of identified or suspected attacks. Also this needs preparation. If one only starts to look for evidence, when something seems to have happened, it may be too late to activate what would have been required to have this evidence.








Application
The Application layer is about controls that are available in SAP standard applications and non-standard applications built by customers. Here, protective measures are discussed on users and privileges level as well as proper application design.

User & Identity Management
This building block includes the lifecycle management of user accounts in systems and landscapes, proper provisioning, maintenance and revocation, including the approval, assignment and revocation of authorizations to/from specific users. Technical and emergency users are handled here as well as the topic of federation in hybrid environments.
Authorization design, role building and handling of Segregation of Duties are not handled here but in “Roles & Authorizations”.
Authentication and Single Sign-On
Authentication deals with the verification of the true identity of a claimed user. It may be as simple as a password, may include multi-factor mechanisms and may also deal with trusted system connections in which one systems relies on the correct authentication by another system.
Single Sign-On establishes an infrastructure, in which a user authenticates himself once in a landscape to then get access to several systems without the need for repeated additional authentication.
As communication security mechanisms like TLS for http-based connections or SNC for RFC connections support the authentic communication between systems and with clients, these mechanisms are included here as well.
Roles & Authorizations
This building block is about everything around roles and authorizations, including the proper definition, distribution and maintenance of authorizations as well as the alignment and combination of roles to business roles across systems in hybrid landscapes. Control of compliance and Segregation of Duties is also covered here.
The assignment and revocation of roles to/for specific users is not handled here but in “User & Identity Management”.
Custom Code Security
The first step in Custom Code Security is proper Custom Code Management: Unnecessary custom code should be removed, required custom code should be maintained in a proper Custom Code Lifecycle Management.
Custom Code Lifecycle Management should cover the whole lifecycle from secure architecture & design via secure development – including but not restricted to the use of code security scanners – up to secure deployment, security maintenance and finally custom code retirement.










Process
The Process layer extends the pure security view with compliance aspects.  While security focuses on operating robust SAP applications preventing intentional and unintentional malfunctions and compromise of confidentiality, regulatory compliance deals with the correct behavior of applications with regards to policies and legal demands coming from the various jurisdictions SAP systems are operated in.

Regulatory Process Compliance
In this building block, application functions are considered for their potential capabilities to significantly violate legal requirements when not used properly.  Additional controls are then investigated that help keep the risk of such violations under control – prominent examples are mechanisms such as double invoice checks or special tax statement control. Typical regulation that is addressed by such procedures is HIPAA, Basel II+III or SoX, just to name a few.
Data Privacy & Protection
The topics in this building block focus on proper handling and protection mechanisms applicable directly to data belonging to individuals that are specifically protected by newer DPP legislation such as the European GDPR and similar requirements that demand capabilities such as blocking & deletion, consent management, right of access and validation etc.
Even though such mechanisms are not solely related to DPP demands, this building block also includes strong confidentiality measures like field tokenizing or encryption at rest.
Audit & Fraud Management
While regulation must be followed for legal reasons, company often require additional capabilities to detect fraudulent behavior and make sure the controls in place are working effectively. This building block discusses solutions that allow auditing and fraud detection to run smoothly and provide correct data on the covered applications.








Organization
Similar to the Environment layer, this Organization layer is also important to set the environment for SAP systems and SAP cloud solutions. It sets the stage and gives needs and requirements as input to be considered.

Awareness
General security awareness is an important pre-condition to achieve security. Not everyone has to be a security expert – but everyone needs to contribute on his part and also needs to identify when security expertise should be called. Ignoring or even countering or circumventing security regulations and mechanisms can endanger a whole landscape. “Awareness” thus also is directly linked to user-friendliness and ease of handling of any security mechanisms or configuration.
Security Governance
Security Governance addresses everything regarding general organization, procedures and regulations including those, which may directly or indirectly impact the setup, configuration, integration and operation of SAP cloud solutions, systems and landscapes.
Risk Management
This comprises all elements on identifying, handling, mitigating and resolving risks including services or SAP solutions in this area.






SAP Security Engagement and Security in the Enterprise Support Academy


A SAP Security Engagement is a joint approach of SAP Active Global Support with a customer over a period of several months to improve and increase the security of the customers SAP landscape with focus on the most relevant security topic areas in the specific situation. It is an offering especially available to MaxAttention, Safeguarding and ActiveEmbedded customers.
Such a SAP Security Engagement typically starts with a Security Workshop to discuss the topic areas to be tackled during the engagement. During a subsequent engagement period SAP accompanies the customer to ensure that not only the Security Workshop delivered good results but during the engagement period also provable positive impact to the security of the customers SAP landscape gets achieved. Finally a Security Verification Workshop wraps-up the results of the engagement phase, verifies the achieved security impact and summarizes recommended next steps for the time after this SAP Security Engagement.
For Enterprise Support customers a lot of information and services including several Expert Guided Implementation Sessions (EGI) is available in our Enterprise Support Academy and our SAP Enterprise Support Value Map for Security.



Your browser does not support JavaScript. Some components may not be visible.










Security Topics Area




                                                Maintenance & Patch Management
                                            
                                                Configuration Analysis
                                            
                                                Additional Resources
                                               
Security Maintenance/Security Patch Management


Although SAP is investing a lot to deliver its products with secure code — see the white paper Secure Software Development at SAP — there still remains the need to also deliver security corrections to already released products due to new flaws identified or new attack patterns becoming known. The security maintenance of installed SAP software is therefore key to continuously protect also against new types of attacks or newly identified potential weaknesses.
Based on feedback from customers, partners and SAP user groups, SAP has launched a regular SAP Security Patch Day, scheduled for the second Tuesday of every month — which is by purpose synchronized with the Security Patch Day of other major software vendors. At these patch days, SAP publishes software corrections as Security Notes solely focused on security to protect against potential weaknesses or attacks. Use Security Notes in SAP for Me -> All SAP Security Notes to get the complete list of all Security Notes. The recommendation is to implement these corrections as soon as possible. Several tools are available to help identifying, selecting and implementing those corrections.



You can find a document describing the recommended procedure for each patch day in the current presentation SAP CoE Security Services - Security Patch Process and in the older documents Arbeitspapier SAP Security Patch Day (German) respective Working Paper SAP Security Patch Day (English) within the Media Library.
There you find the latest version of the presentation from the Security Notes Webinar and the package with the SAP Security Notes Advisory as well. Both documents get updated monthly.
Overall the generally recommended procedure for each patch day is as follows:

Check the updated list of Security Notes

Use the tool System Recommendations in SAP Solution Manager to check which security notes are relevant for the various systems of your system landscape
Use available tools like the Note Assistant — transaction SNOTE — to apply individual ABAP Security Notes or the Maintenance Planner to plan the implementation of ABAP Support Packages or Java Patches.



Security configuration analysis


These services and tools ensure the maintenance of security configuration settings and changes by periodically reviewing security relevant configuration settings of all systems and installed software components.
You can find a document describing the features and recommended procedures in the presentation SAP CoE Security Services - Check Configuration & Authorization.





EarlyWatch Alert (EWA)



The well-known EarlyWatch Alert report displays the most critical recommendations from SAP on security. This enables SAP customers to identify problems and take the required measures quick and easily. See SAP Note 863362 for further details on security checks in the EWA.






Security Optimization Service



The Security Optimization Service is designed to check the security of your SAP system. This service comprises a system analysis and the resulting recommendations for system settings. It addresses system and customizing settings that impact your system security. It focuses on internal and external system security. To improve the internal security, many critical authorizations of the basis are checked. Moreover, you can verify the findings in your system anytime as described in the document SAP Security Optimization Service – Verifying the Findings. External security is improved by checking the accessibility of your system and the authentication methods used.






Configuration Validation



The Configuration Validation enables you to determine whether the systems in your landscape are configured consistently and in accordance with your requirements. You can check the current configuration of a system in your landscape using a defined target state (target system) or compare it with an existing system.






The SOS covers topics presented in whitepapers like Secure Configuration SAP NetWeaver Application Server ABAP.
Scope of the Security Optimization Self Service for the SAP NetWeaver Application Server ABAP:

Basis administration check
User management check
Super users check
Password check
Spool and printer authorization check
Background authorization check
Batch input authorization check
Transport control authorization check
Role management authorization check
Profile parameter check
SAP GUI Single Sign-On (SSO) check
Certificate Single Sign-On (SSO) check
External authentication check

Find the complete list of checks in the following documents in the Media Library:

SOS - ABAP Checks
SOS - JAVA Checks
SOS - HANA Checks

You can use these document to compare the checks of the SOS with the checklist wich you already use to validate security configuration.
For HANA you find a description of the available services and an overview about the checks in the presentation HANA Security Remote Service Content.
In addition you can view examples showing a formatted report:

SOS - Sample Report for ABAP
SOS - Sample Report for JAVA
SOS - Sample Report for HANA
SOS - Sample Report for SAP BTP

In the Media Library you find the document How to run the SOS on SAP Solution Manager 7.2. 
To prepare the session a questionnaire should be filled out. As part of the questionnaire you can add additional custom checks on critical authorizations. You will find an example of the questionnaire in the documents Security Optimization Self-Service - Questionnaire PDF (for viewing only) and Security Optimization Self-Service - Questionnaire ZIP/DOC (for uploading into a session) in the Media Library.
The SAP Security Optimization Service is available as a Guided Self Service for ABAP based systems and as a remote service for ABAP and Java systems. In case of an "ABAP on HANA" installation you get the HANA checks automatically as a part of the SOS for ABAP. Currently we do not offer a separate SOS for HANA for a pure HANA database.
SAP Note 1484124 describes the prerequisites to run the Guided Self Service for ABAP based systems.
It can be used at any time. It is most recommended during the end of going live phase. The service is also very useful when preparing for internal and external audits. It can be rerun to make sure that the applied changes in the system configuration were successful and that no new vulnerabilities appeared.


Additional Resources


You find the documentation including examples and best-practice on SCN:




Change Diagnostics


Configuration Validation




You find the complete recording of the corresponding TechEd session from 2013 in the Media Library:




Cross-System Security Validation (Presentation)


Cross-System Security Validation (Exercises)


Cross-System Security Validation (Recording)




Your browser does not support JavaScript. Some components may not be visible.






Related Links

Security Notes


Security Notes in SAP for Me


SAP Security Patch Day


Security Patch Process FAQ











System Recommendations


Maintenance Planner 


Note Assistant



















Security Services, Tools and Information


Best practices-based security services



SAP enables its customers to protect their business processes through a comprehensive security portfolio turned into services. We take the security chapter of the EarlyWatch Alert as a starting point to offer detailed services mainly around the Security Optimization Service ant the Security Notes which are published on SAP Support Portal and shown in the application System Recommendations of the SAP Solution Manager.
Combining the results of these services with the existing Security Policy of the company we define the company's SAP Security Baseline. This document is then used do define Target Systems for the application Configuration Validation in the SAP Solution Manager. You can either use the cross-system BW reports of that application directly or you can pass results to another reporting infrastructure like a Management Dashboard within the SAP Solution Manager, to Business Objects, to GRC Process Control or any other reporting system.



References



Among others, the following sources are used within the security services:

SAP Note 863362 - EarlyWatch Alert (EWA) report - Security chapter or same content in the SAP Support Wiki
Change Diagnostics
Configuration Validation
Security Audit Log (ABAP)
SIS264 Securing Remote Access within SAP NetWeaver AS ABAP
Protecting SAP Applications Against Common Attacks
Secure Configuration SAP NetWeaver Application Server ABAP
SAP Security Recommendations: Securing Remote Function Calls (RFC)
Governance, Risk, and Compliance — Access Control
Governance, Risk, and Compliance — Process Control
SAP NetWeaver Identity Management












Media Library



Search:TitleTypeChanged
_SAP Security Notes AdvisoryZIP2024-02_Security Notes Webinar and 
Security Notes Webinar, latest month only
or landing page at Help Portal - Support ContentPDF2024-02RFC Gateway and Message Server SecurityPDF2019-06SAP CoE Security Services - Check Configuration & AuthorizationPDF2020-01SAP CoE Security Services - OverviewPDF2020-01SAP CoE Security Services - Secure Operations MapPDF2020-01SAP CoE Security Services - Security Patch ProcessPDF2023-03SAP CoE Security Services - Security Baseline Template Version 1.9 (including ConfigVal Package version 1.9_CV-5)ZIP2018-08SAP CoE Security Services - Security Baseline Template Version 2.4.1 
(including ConfigVal and Dashboard Builder Package 2.4_CV-1)
see KBA 2253549ZIP2023-02Security Baseline Template Version 2.4 Policies for FRUNGitHub2023-04Arbeitspapier - SAP Security Patch Day (German)PDF2012-08Working Paper - SAP Security Patch Day (English)PDF2012-08Configuration Validation WIKI (current version see online version)PDF2021-10Note 863362 - Security checks in SAP EarlyWatch Alert, EarlyWatch and GoingLive sessions
or 
SAP Help about EWA - Security
Note
or 
SAP Help
2022-07EarlyWatch Alert Report - Sample of Security ChapterPDF2020-05
Factsheet Security EngagementPDF2017-10
HANA Security Remote Service ContentPDF2016-10RFC Security v1.2 (from 2004-2008)PDF2008-07SAP Cloud Platform - Neo Environment - Security
PDF2018-01SEC204 – Live on Stage: Monthly Security Patch Webinar about System Recommendations on SAP Solution Manager 7.2PDF2016-11SIS261 Cross-System Security Validation using SAP Solution Manager 7.1 (Exercises)PDF2014-11SCI262 Cross-System Security Validation Using SAP Solution Manager 7.1PDF2014-11SCI262 Cross-System Security Validation Using SAP Solution Manager 7.1 (Recording)MP42014-11SIS264 Securing Remote Access within SAP NetWeaver AS ABAPPDF2012-11SOS: Get List of ALL Detected UsersPDF2010-05
Security Optimization Self Service - OverviewPDF2010-05How to run the SOS on SAP Solution Manager 7.2PDF2020-12SAP Continuous Quality Check (CQC) for Security Optimization ServicePDF2023-05Security Optimization Self-Service - Questionnaire (for viewing only)PDF2023-09Security Optimization Self-Service - Questionnaire (for uploading into session)ZIP/DOC2023-09Security Optimization Self-Service - Sample Report for ABAPPDF2021-08Security Optimization Self-Service - Sample Report for HANAPDF2020-05Security Optimization Self-Service - Sample Report for JAVAPDF2016-03Security Optimization Service - Sample Report for SAP BTPPDF2022-05Security Optimization Service - ABAP ChecksPDF2020-04Security Optimization Service - HANA ChecksPDF2020-04Security Optimization Service - JAVA ChecksPDF2020-04Security Optimization Service - SummaryPDF2010-05Security Optimization Service - Watcher GuidePPT2010-06Verify Users AuthorizationPDF2012-08






Your browser does not support JavaScript. Some components may not be visible.











How is your experience with this page?     








Cookie Preferences
















