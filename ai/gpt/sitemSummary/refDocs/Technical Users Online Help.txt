




Online help technical communication user
































Online help:Technical communication users


General



The technical communication user (formerly called Support Hub user) is created for technical purposes only, for system-to-system connections between your landscape (most commonly in your SAP Solution Manager) and the SAP Support backbone.
You cannot use it to log on to SAP websites, e.g. the SAP for Me portal, SAP Store, SAP Learning Hub, or SAP Community.
Please note:

Technical communication users can only be administered through the Technical Users application, not through the User Management application for S-user IDs.
Only super or user administrators have access to the Technical Users application to create, activate or delete technical communication users.
If you need to change a technical communication user’s password, always use the Change Password function offered in the Technical Users application.




Enter the application










Creating a technical communication user



The technical communication user creation is a two-step process:


Creating the user

Activating the newly created user





To create a user:




Click the Request User button.
Fill out the form
Field Customer (if your company has several customer numbers): Select the customer number the new user shall be assigned to.
Field Description: Enter a description that makes it easy to associate the new user with its purpose. For easy identification we recommend using the installation number followed by the system ID the user shall be used for (for example **********-SID).
Field Email: Enter the e-mail address of a person that can be contacted regarding the new user.
Field Language: Select a language from the list.
Field Department (optional): Select a department from the list or specify a new one.


Submit the form.

The new user account will be created within one business day.




To activate a user:




Select the newly created user from the list of all technical communication users.
Hint: Users that have already been activated in the past are easily recognized through a checkmark in the Active for Data Transfer column of the list. Use the filter feature in the upper-right corner to identify inactive users.
Click the Activate button.
Enter a valid password. It must meet all off the following requirements:
Be between 8 and 40 characters long
Include at least one uppercase letter (A-Z)
Include at least one lowercase letter (a-z)
Include at least one number (0-9)
Include at least one special character from the following:
! \ " @ $ % & / ( { [ ] } ) + - * = ? ' ~ # _ . , ; : < >
Not contain any blanks

Not start with ? or !
Not start with 3 identical characters
Be different from the last 5 passwords
Not have been changed on the same day


Confirm the password by re-entering it, then click OK.

The user will be activated within a few minutes.












Working with technical communication users


Changing a technical communication user's password



To change the password for a technical communication user, proceed as follows:

Mark the respective row in the list of users.
Click the Change Password button in the footer action bar. The Change Password dialog will be shown.
Enter a valid password. It must meet all off the following requirements:
Be between 8 and 40 characters long
Include at least one uppercase letter (A-Z)
Include at least one lowercase letter (a-z)
Include at least one number (0-9)
Include at least one special character from the following:
! \ " @ $ % & / ( { [ ] } ) + - * = ? ' ~ # _ . , ; : < >
Not contain any blanks

Not start with ? or !
Not start with 3 identical characters
Be different from the last 5 passwords
Not have been changed on the same day


Re-enter the password.
Click OK to confirm or Cancel to abort.




Unlocking a technical communication user



Technical user IDs can get locked due to repeated logon attempts with an incorrect password. In the list of all users, locked users are easily identifiable through a padlock icon in the Active for Data Transfer column. To unlock them:

Mark the respective row in the list of users.

Click the Unlock button in the footer action bar.





Creating an SAP Passport



If you want to exchange data with the SAP Support infrastructure using client certificate authentication, you can download a PFX (Personal Information Exchange) file via the Technical User application and import it into your system using transaction STRUST.

From the list of all technical communication users select your preferred user for that system.
Note that the user must be active for data transfer.
Click the Create SAP Passport button.
Enter a password to secure the PFX. (This password will be required when importing the PFX in STRUST later.)
Alternatively, you can provide the Certificate Signing Request (CSR).
Click OK.
Click on the Download SAP Passport button and save the PFX file to your local computer.
Follow the steps outlined in SAP Knowledge Base Article 2805811 to import the PFX file into your system using transaction STRUST.













Deleting a technical communication user




From the list of all technical communication users select the one you want to delete.
Click the Delete button.

The user will be deleted within one business day.












Useful links and troubleshooting tips




KBA 2532813 – Technical Communication User
KBA 2662129 – Difference between S user and Support Hub User
KBA 2704804 – What's the difference between a Technical Communication User (Solution Manager) and a regular S-User ID? (FAQ)
KBA 2174416 – Creation and activation of users in the Technical Communication User application
KBA 2785863 – How many Technical Communication Users should be created for SAP Solution Manager?
KBA 2393376 – How to reset the password for a Technical Communication User
KBA 2438876 – How to change contact details for a Technical Communication User
KBA 2805811 – How to enable client certificate authentication for technical communication users

If you face issues regarding technical communication users, check out the following KBAs or report an incident under component XX-SER-SAPSMP-USR:


KBA 2541908 – Troubleshooting Guide for Technical Communication User (Guided Answers)
KBA 2434373 – User IDs created in the Technical Communication User application are not visible in User Management
KBA 2392726 – How to unlock a Technical Communication User
KBA 2809994 – "No Data" in Technical Communication User Application


























