





Security Whitepapers

























How you can contact us:  Technical Assistance Request product support from SAP Non-Technical Assistance Request non-product support or provide feedback on SAP Support Portal site



Your browser does not support JavaScript. Some components may not be visible.



        Contact Us
    

×


                How you can contact us:
                
            




















Technical Assistance
Request product support from SAP





















Non-Technical Assistance
Request non-product support or provide feedback on SAP Support Portal site



















Security Whitepapers



To help you increase the security of your SAP systems, SAP provides you with Security Whitepapers. The objective of this series is to give you concise, easy-to-understand and easy-to-implement information on how to improve the security of your IT systems. The series covers various aspects of security including recommendations for system configuration as well as guidance and support for the implementation of SAP security fixes. 




TitleDescriptionDownloadDate
Language
SAP Business Technology Platform in GxPDiscover how SAP helps enterprises in the life sciences industry address the challenges of integrating and extending processes while paying careful attention to industry and government regulations. Find out how SAP Business Technology Platform and its built-in services can help you create 21st-century applications.      PDF2022-02EnglishSAP HANA  Security WhitepaperThis whitepaper gives IT security experts an overview of what they need to understand about SAP HANA in order to comply with security-relevant regulations and policies and to protect their SAP HANA implementation and the data within from unauthorized access.
see https://www.sap.com/hanasecurity       PDF2021-03EnglishThe Secure Software Development Lifecycle at SAPLearn how SAP has implemented a secure software development lifecycle (secure SDL) for software development projects. Discover how secure SDL provides a framework for training, tools, and processes.Link2020-09EnglishSAP's Standards, Processes, and Guidelines for Protecting Data and InformationThis document describes how SAP helps to ensure that the software systems, information, and data of its customers are fully protected.PDF2016-08EnglishManaging Security with SAP Solution ManagerExplore the various aspects of building, setting up, and operating a secure system landscape and the ways in which SAP Solution Manager supports these tasks as an IT services and operations management tool.PDF2015-06EnglishSAP Security Recommendations: Securing Remote Function Calls (RFC)SAP reviewed and improved the security controls used by Remote Function Calls (RFC). RFC is an SAP-proprietary communication protocol. Most SAP customers run business-critical system communication using RFC technology. Keeping business data that is processed via RFC secure is as important to SAP and its customers as ensuring uninterrupted business operations.PDF2023-03EnglishSecurity Services von SAP Active Global SupportMit einer globalen Support-Organisation unterstützt SAP seine Kunden dabei, die Qualität und Zuverlässigkeit ihrer Anwendungen sicherzustellen - und zwar über den gesamten Lebenszyklus hinweg. Mehr als 2.000 Service- und Support-Mitarbeiter in über 40 Ländern sorgen bei SAP Active Global Support dafür, dass die unternehmerischen Geschäftsprozesse der SAP-Kunden möglichst reibungsfrei, geschützt und sicher laufen.PDF2012-06DeutschSicheres Cloud Computing mit SAPWer sich für Software as a Service (SaaS) entscheidet, gibt sensible Informationen und IT-Infrastrukturen in fremde Hände. Den sicheren Betrieb und Schutz übernimmt der Cloud-Anbieter. Umso wichtiger ist es, dessen Sicherheits- und Datenschutzverfahren genau unter die Lupe zu nehmen. Genau diese Frage adressiert das vorliegende Dokument.PDF2012-02DeutschSecure Configuration of SAP NetWeaver Application Server Using ABAPThe document provides an overview about the most important configuration activities that should be performed for the ABAP server of an SAP NetWeaver-based system. The general scope of this document is the protection of SAP ABAP systems from unauthorized access within the internal corporate network. For Internet scenarios additional security measures have to be considered and implemented.PDF2012-01EnglishProtecting SAP Applications Against Common AttacksThis paper explains the measures SAP strongly recommends that its customers apply to enhance the level of security with respect to certain common attack types. The paper describes, in detail, vulnerabilities and the possible exploit patterns and how to protect applications against them. Furthermore, it provides guidance on how to make custom-developed applications more secure.PDF2011-11
English













How is your experience with this page?     








Cookie Preferences
















