Material inventory balance | SAP Help PortalHomeSAP S/4HANAMigration Objects for SAP S/4HANAMigration Objects for SAP S/4HANA2023 LatestAvailable Versions: 2023 FPS02 (Oct 2024)  2023 Latest  2023 Latest  2022 Latest  2022 FPS02 (May 2023)  2022 FPS01 (Feb 2023)  2022 (Oct 2022)  2021 Latest  2021 FPS02 (May 2022)  2021 FPS01 (Feb 2022)  2021 (Oct 2021)  2020 Latest  2020 FPS02 (May 2021)  2020 FPS01 (Feb 2021)  2020 (Oct 2020)  1909 Latest  1909 FPS02 (May 2020)  1909 FPS01 (Feb 2020)  1909 (Sep 2019) EnglishAvailable Languages: English  German (Deutsch) ProductionStates:TestDraftProductionThis documentSearch Scopes:All SAP productsThis documentAll of these words:Any of these words:This exact word or phrase:None of these words:ClearSearchAdvanced SearchFavoriteThis page has been removed from your favorites.Download PDFThe following PDF options are available for this document:Create Custom PDFFeedback You can now add comments and questions right into SAP documentation and start a conversation with SAP authors and experts. Don't show me againFeedbackShareEdit in IXIA CCMS Web You can now go directly to IXIA CCMS Web to edit the topic. Simply select Edit in IXIA CCMS Web link under the More… menu. Don't show me againMoreMetadata Analytics Subscribe Edit in IXIA CCMS WebTable of Contents 
Material inventory balance
On this pagePurposeIn ScopeOut of ScopeSupported FeaturesPrerequisitesMapping InstructionsTasksPost-ProcessingFurther Information

Available Migration Objects in SAP S/4HANA.

Purpose



Object Alias



INVENTORY_2





Business Object Component/Area


Inventory Management (MM-IM)




Business Object Type


Transactional data




Business Object Definition


A detailed list of all objects in stock at a specific time, resulting from a physical inventory (stocktaking process).




Migration Approach


Staging Table




Custom Field Support


Not applicable




In Scope
Migration to SAP S/4HANA is supported.


Material document header


Material document item


The following types of transactions/events are supported: GM_Code 05: Other goods receipt




The following stock types are supported:


‘X’ - Stock in quality inspection


‘S’ - Blocked stock


‘ ’ - Unrestricted use stock


Note
For the special stock indicators O and V, the stock can be posted only in Unrestricted use stock.


The following special stock indicators are supported:



‘K’ - Vendor consignment




‘W’ - Customer consignment 




‘V’ - Returnable packaging with customer




‘O’ - Parts provided to vendor




‘E’ - Orders on hand




‘Q’ - Project Stock





Note
For Vendor consignment stock, enter the special stock indicator K and the vendor.For Customer consignment stock, enter the special stock indicator W and the customer.For Returnable packaging stock, enter the special stock indicator V and the customer.For Parts provided to vendor stock, provide enter the special stock indicator O and the vendor.For Orders on hand, enter the special stock indicator E and the sales order with the corresponding item.For Project stock, enter the special stock indicator Q and the WBS element.For Materials that are managed serially, enter one item with the quantity 1 for each serial number.For Materials that are batch managed, enter the corresponding batch. If the batch doesn't exist, the system creates it in the goods movement posting.

Out of Scope


The following types of transactions and events aren’t supported:



GM_Code 01: Goods receipt for purchase orders.



GM_Code 02: Goods receipt for production orders



GM_Code 03: Goods issue



GM_Code 04: Transfer postings



GM_Code 06: Reversal of goods movements



GM_Code 07: Subsequent adjustment of a subcontract order




Material document EWM ref.


Service parts for SPM


Create item data for catch weight management


Material document print control


Storage locations with an assignment to an external warehouse aren’t supported

Posting to handling unit managed storage location is not allowed (error message M7 478 appears)


Supported Features
The following structures or features are supported in SAP S/4HANA:


Material Document Header Data


Assign Code to Transaction for Goods Movement


Material Document Items


Serial Number


The following structures and features aren’t supported:


Material Document EWM Ref.


Service Parts for SPM


Create Item Data for Catch Weight Management


Material Document Print Control



Prerequisites
The following objects need to have been maintained or migrated:


MM - Purchase order (only open PO)



SD - Sales order (only open SO)





Before starting with the initial data transfer, you have to define an overall migration key date for each company code. This migration key date is used for all migration objects that are posted to financial accounting, except for Fixed asset (incl. balances and transactions) or Fixed asset - Master data (depending on system configuration). 


Use the FINS_MIG_CTRL_1  transaction (Define Settings for Legacy Data Transfer) to maintain these settings:


Set the Migration Key Date, which is used as posting date, for each required company code


Set the Legacy Data Transfer Status:In Preparation: migration key date is still unclear (migration of transactional data isn’t possible)Ongoing: required for the initial data transfer (migration of transactional data is possible)Completed: after the initial data transfer is complete (migration of transactional data is possible)




If you want to create inspections lots during stock posting you must consider the QM not active flag for Movement Type 561 in your configuration. If this indicator is set for Movement Type 561, no inspection lots are created for this stock posting.


The following objects have already been maintained or migrated:



Product: If you don't provide the external amount for the stock load, the system calculates the value with the material prices defined in the material master in the accounting data. If that's the case, please ensure that the price data provided in the material master isn’t accurate.



Batch unique at material and client level (otherwise the system creates the batch automatically during stock posting). Note
If you're using single batch valuation, you have to migrate the batches with a valid valuation category assigned to them before you can post the inventory. Automatic creation of the batch and the valuation category during inventory posting isn’t permitted.



Supplier/Customer (only relevant for special stock: K – vendor consignment, V – Returnable packaging with customer, and O - Parts provided to vendor)



WBS element (only relevant for special stock Q - Project stock)



MM - Purchasing info record with conditions with the Consignment info category (relevant only for vendor consignment stock)





Mapping Instructions

General Information
You can use the Technical Document Number as a technical unique key to create material documents with several items.
You can use the Technical Item Number field in the Material Document Item sheet to determine the number of items posted according to the unique identifier entered in the Material Document Header sheet. We recommend that you structure documents with a maximum of 1000 records each. 
The Technical Item Number field in the  Material Document Item sheet and the Technical Document Number in the Material Document Header sheet aren’t mapped to a target field.


Mapping of the Serial Number field
If you want to post a stock for a material that has a serial number profile assigned in the master data, there are two ways to determine the serial number that corresponds to the provided quantity:


You can use the Create Serial Nos Automatically indicator on the template. The system then automatically creates the serial numbers according to the given quantity. In this case, leave the Serial Number field empty.


You can provide the Serial Number on the template. Please note that, in this case, you have to provide one item with quantity 1 for each serial number.




Mapping of the Batch Number field in the Material Document Item sheet
You want to post stock for a material that is defined by its valuation category as a material valuated in single batches. If this is the case, you have to enter a valid batch. The valuation category has to be assigned in the batch.
You want to post stock for a material that is defined by its valuation category as a material valuated in split valuation. If this is the case, you have to enter a valid valuation type.


Mapping of the External Amount in Local Currency field in the Material Document Item sheet
The valuation of the stock to be entered depends on two factors:


The price and the type of price control defined in the accounting data of the material master record


Whether you enter a value for the quantity entered:
For a material valuated at a standard price, the initial entry of inventory data is valuated on the basis of the standard price. If you enter an alternative value in the External Amount in Local Currency field, the system posts the difference to a price difference account.
For a material valuated at a moving average price, the initial entry of inventory data is valuated as follows:


If you enter a value when entering initial data, the quantity entered is valuated at this price. If the quotient of the value and the quantity of the initial data differs from the moving average price, the moving average price changes when initial data is entered.


If you don't enter a value when entering initial data, the quantity entered is valuated at the moving average price. In this case, the moving average price doesn’t change.




Note
It is possible that after the migration there are differences in the total stock value. The reason for this is rounding, which can be the case if the material is controlled with Moving average price (V) for Price Control and if a price unit greater than 1 is used. However, rounding effects can be reduced by using the External Amount in Local Currency field of the migration template. In case there are still differences after the migration, use the Price Change (MR21) transaction to correct wrong values.
Example

Find an example below in which the value after migration is not as expected.



Stock value in source system: 101 PCE with a value of EUR 12.46, and a Moving average price of EUR 12.34 per 100 PCE.



Data in the migration template: The Moving average price is maintained at EUR 12.34 per 100 PCE. During migration, two items for the same material are provided. One item with 1 PCE, and one item with 100 PCE. Nothing is provided in the External Amount in Local Currency field.



Stock value in target system: 101 PCE with a value of EUR 12.12.


Explanation


First item: Goods receipt for 1 PCE. As the stock is zero and as nothing is provided in the External Amount in Local Currency field, the value is calculated from the Moving average price.
1 PCE * EUR 12.34 / 100 PCE = value of EUR 0.12


Second item: Goods receipt for 100 PCE. As there is stock and as nothing is provided in the External Amount in Local Currency field, the value is calculated value-proportional from the existing stock.
100 PCE * stock value EUR 0.12 / stock quantity 1 PCE = value of EUR 12.00

This results in a total stock value in the target system of 101 PCE with a value of EUR 12.12.
Example

Find an example below in which the value after migration is as expected.



Stock value in source system: 101 PCE with a value of EUR 12.46, and a Moving average price of EUR 12.34 per 100 PCE.



Data in the migration template: The Moving average price is maintained at EUR 12.34 per 100 PCE. During migration, two items for the same material are provided. One item with 1 PCE, and one item with 100 PCE. The External Amount in Local Currency field is filled out.



Stock value in target system: 101 PCE with a value of EUR 12.46.


Explanation


First item: Goods receipt for 1 PCE with an External Amount in Local Currency of EUR 0.12. This results in a value of EUR 0.12


Second item: Goods receipt for 100 PCE with an External Amount in Local Currency of EUR 12.34. This results in a value of EUR 12.34

This results in a total stock value in the target system of 101 PCE with a value of EUR 12.46.



Sales Price Valuation
If you've licensed SAP S/4HANA retail for merchandise management and
if you've activated sales price valuation in the valuation area, please note that the
sales price valuation will only be updated when a valid sales price condition exists
in the system. Sales price conditions can be migrated using the Condition record for pricing (general template) migration object.


Mapping Structure Hierarchy


Level
Name



1
Material Document Header (S_MARA), mandatory
 


2
 
Material Document Item (S_MARD), mandatory




Tasks


Navigate to the Data Migration to SAP S/4HANA from Staging (2Q2)
solution process on SAP Signavio Process Navigator.


Download the test script from the Accelerators
section.


Follow the procedure described in the 2Q2 test script.



Post-Processing

How to Validate Your Data in the System
Use the Data Migration Status app to validate
your data in the system. Alternatively, use the following SAP Fiori app:



App:

Trial Balance (F0996)Material Document List (w. Empties) (/BEV1/NEMB51)Stock - Multiple Materials (F1595)Display Warehouse Stock (MB52)Display List of Stock Values (MB5L)Display Stock Overview (MMBE)Material Inventory Values - Balance Summary (F1422)Material Documents Overview (F1077)Stock - Single Material (F1076)




Open the SAP Fiori apps reference library by choosing the app
name above. Select your product and your product version. Then, open the
Implementation Information tab and expand the
Configuration section. There, you can find a list of business
catalogs that can enable you to use this app. Choose the business catalog that fits your
needs, and add it to your business role.
Note
After migrating inventory data, the serial number can’t be displayed in the Material Documents Overview app that is linked from the Data Migration Status app. To see the serial number, use the Material Document List app.
You also have the option of validating your data in the back end using the following transaction: Transaction:Stock Overview  (MMBE)List of Stock Values: Balances  (MB5L)Material Doc. List  (MB51) with Goods Movement Type561List of Warehouse Stocks on Hand (MB52)


Further Information


SAP Knowledge Base Article 2813720  – SAP S/4HANA Migration cockpit: Collective KBA for migration object "Material inventory balance"


SAP Knowledge Base Article 3072564  – System throws QA495 error when creating inspections lots during stock posting.




Available Migration Objects in SAP S/4HANA.

On this pagePurposeIn ScopeOut of ScopeSupported FeaturesPrerequisitesMapping InstructionsTasksPost-ProcessingFurther InformationCommentsThere are no comments on this page. Leave a comment Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedback Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedbackCopyrightDisclaimerPrivacy StatementLegal DisclosureTrademarkTerms of UseCookie PreferencesAccessibility and SustainabilitySystem StatusAsk a question about the SAP Help PortalFind us onFollow us on FacebookSAP on FacebookFollow us on YouTubeSAP on YouTubeFollow us on LinkedInSAP on LinkedInFollow us on InstagramSAP on InstagramShare

