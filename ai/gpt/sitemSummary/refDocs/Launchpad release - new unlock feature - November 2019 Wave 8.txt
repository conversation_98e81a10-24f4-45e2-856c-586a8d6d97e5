 
Release Notes Wave 8 / 201 9 
SAP ONE Support  Launchpad , SAP Support Portal, and SAP ONE Support A pplications   
  
Release Notes Wave 8/201 9 – SAP ONE Support Launchpad and SAP ONE Support Applications  
2 
 Content  
Disclaimer  ................................ ................................ ................................ ................................ ................  3 
Introduction  ................................ ................................ ................................ ................................ .............  4 
This May Interest You  ................................ ................................ ................................ ..............................  4 
New Applications and Tiles  ................................ ................................ ................................ .....................  4 
New Default Configuration of Tile Group System Operations and Maintenance  ...............................  4 
New SAP Enterprise Support Reporting Cockpit  ................................ ................................ .................  5 
New Application Schedule a Manager  ................................ ................................ ................................  7 
Incident Management  ................................ ................................ ................................ .............................  8 
Legacy Incidents – Act Now to Archive Them!  ................................ ................................ ....................  8 
Review Incident Before Submitting to SAP Support  ................................ ................................ ...........  8 
Enhancements for the Support Log Analyzer  ................................ ................................ ......................  9 
Support Assistant  ................................ ................................ ................................ ................................  9 
Other Changes  ................................ ................................ ................................ ................................ ... 10 
SAP Notes  and Knowledge Base Articles (KBAs)  ................................ ................................ ...................  11 
SAP Notes and SAP Knowledge Base Articles now available in Chinese, Spanish and French  ..........  11 
New Document Type in My SAP Notes & KBAs Expert Search ................................ ..........................  11 
User Management  ................................ ................................ ................................ ................................ . 11 
New Features in the Technical Communication User Section  ................................ ..........................  11 
Small Enhancements and Bug Fixes  ................................ ................................ ................................ .. 12 
Advance Notice: Changes to Cloud Administrator Default Authorizations  ................................ ...... 12 
Installation Management  ................................ ................................ ................................ ......................  12 
System Data Management  ................................ ................................ ................................ ....................  13 
System Data  Application  ................................ ................................ ................................ ...................  13 
System Overview  Application: New Columns  ................................ ................................ ....................  13 
My Landscape  Application: New Card Status of Support Connectivity  ................................ .............  14 
Cloud Availability Center  ................................ ................................ ................................ .......................  15 
Service Messages  ................................ ................................ ................................ ................................ ... 16 
Adjusted Auto -Forwarding Process  ................................ ................................ ................................ ... 16 
HANA Enterprise Cloud (H EC) ................................ ................................ ................................ ...............  16 
Service Requests for Multiple Systems  ................................ ................................ .............................  16 
Database Information in Landscape Application  ................................ ................................ ..............  17 
Twitter Feed on SAP Support Portal Homepage  ................................ ................................ ...................  17 
Appendix  ................................ ................................ ................................ ................................ ................  18 
Release Dates  ................................ ................................ ................................ ................................ .... 18 
Piloting Program for the SAP ONE Support Launchpad  ................................ ................................ .... 19 Release Notes Wave 8/201 9 – SAP ONE Support Launchpad and SAP ONE Support Applications  
3 
  
 
 
 
 
 
 
 
 
 
 
 
 
Disclaimer  
The information in this document is confidential and proprietary to SAP and may not be disclosed 
without the permission of SAP. This document i s not subject to your license agr eement or any other 
service or subscription agreement with SAP. SAP has no obligatio ns to pursue any course of business 
outlined in this document or any related presentation, or to develop or release any functionality 
mentioned therein.  This document, or any related presentation and SAP’s strategy and possible 
future developments, product s and/or platforms directions and functionality are all subject to 
change and may be changed by SAP at any time for any reason without notice.  The information in 
this document is not a commitment, promise or legal obligation to deliver any material, code o r 
functionality.  This document is provided withou t a warranty of any kind, either express or implied, 
including but not limited to, the implied warranties of merchantability, fitness for a particular 
purpose, or non -infringement.  This document is for infor mational purposes and may not be 
incorporated into a contract . SAP assumes no responsibility for errors or omissions in this document, 
except if such damages were caused by SAP intentionally or grossly negligent.  
All forward -looking  statements are subject to various risks and uncertainties that could cause actual 
results to differ materially from expectations.  Readers are cautioned not to place undue reliance on 
these forward -looking statements, which speak only as of their dates, an d they should not be relied 
upon in making purchasing decisions.  
  Release Notes Wave 8/201 9 – SAP ONE Support Launchpad and SAP ONE Support Applications  
4 
 Introduction  
The document describes the new features available since  
the last wave of the SAP ONE Support Launch pad, the SAP 
Support Portal,  and the various redesigned s ervice and 
support applications such as Incident Form, Software 
Download Cen ter, Search, User Management, License 
Keys, SAP Notes etc . 
New Applications  and Tiles  
New Default Configuration of Tile Group System 
Operations and Maintenance  
The tiles Landscape  and System Data Overview Report , 
which point ed to legacy applications  on the soon -to-be-
retired service.sap.com platform , have been removed 
from the default confi guration of the tile group System 
Operations and Maintenance . The functionality of the 
respective legacy applications has been migrated to the 
application System Overview .  
In addition, the tile My Landscape  has been added to the default configuration. System data 
management can now be performed via the tiles System Data , System Overview  and My Landscape . 
 
This change only affects new users. The tile configuration for existing users remains untouched.  
If you want to reset the tile groups on your homepage to their default configuration, navigate to the 
tile catalog  and click the Reset  button in the bottom -right corner.  
This May  Interest You  
The 2020 release dates for the SAP 
ONE Support Launchpad and its 
applications have been announced . 
support.sap.com/ about  
Post January 1, 2020, the SAP Notes 
download and upload process will 
stop working unless Note Assistant 
is enabled in ABAP systems to work 
with digitally signed SAP Notes.   
youtube.com/watch?v=4WQ5qeV5Z6s  
Download of legacy incidents from 
the service.sap.com platform is only 
supported until Decembe r 31, 201 9. 
blogs.sap.com/?p=780432  Release Notes Wave 8/201 9 – SAP ONE Support Launchpad and SAP ONE Support Applications  
5 
 Note: Clicking Reset  does not affect the My H ome  group.  
New SAP Enterprise Support Reporting Cockpit  
After a successful pilot phase , the new SAP Enterprise Support reporting cockpit  application will be 
released for  all customers on December 1st, 2019, shortly after the Wave 8 / 2019 transport . 
SAP Enterprise Support reporting cockpit is an interactive dashboard analy zing and documenting the 
status of your SAP solution, support services and achievements hereunder bas ed on solution 
monitoring capabilities, usage KPIs, consumption of SAP Enterprise Support  offerings, incident status 
and other support -related metrics.  
 
You can interact, personalize, and design your tailored SAP Enterprise Support report  by adding or 
removing data sections, statistics and tables . Drill down into detailed views across products, systems, 
services, incidents , and status. Save your settings a s your individual variant for future reuse or print 
the result as a report.  
The version released in the SAP ONE Support L aunchpad represents the first phase in a staged 
approach and includes following cloud solutions:  
▪ SAP SuccessFactors  
▪ SAP C/4HANA  
▪ SAP S/4 HANA  
SAP Enterprise Support reporting cockpit will gradually replace the existing PDF -based SAP Enterprise 
Support report in the SAP ONE Support Launchpad.  
The content is constantly being improved and extended. New data sections as well as further cloud 
solutions and on -premise products will be integrated in upcoming releases.  
Release Notes Wave 8/201 9 – SAP ONE Support Launchpad and SAP ONE Support Applications  
6 
  
Find more information  in our SAP Community blog . 
To access the SAP Enterprise Support reporting cockpit , you need the authorization Display SAP 
Enterprise Support reporting cockpit . Furthermor e, for data stored in the sections Purchased Products  
and SuccessFactors Licenses , you need the authorization Access License Utilization for Cloud .  
Both can be granted by your cloud or super administrators for the installations you are interested in  
To get help, refer to the documentation or start the guided tour about 
the application by clicking the help icon in the upper -right corner.  For 
UI or data issues use the “ Report an issue ” button in the application 
and create a ticket under component  SV-SCS-ESR. 
To share feedback, or if you are interested in being listed as a reference 
customer , contact <EMAIL> . 
The new application can be launched through the tile Enterprise 
Support Reporting Cockpit . To add it to your homepage, navigate to the 
tile catalog  via the “Hamburger icon” in the upp er-left corner of the launchpad homepage where you 
can find it in the group Newly Released  at the top of the catalog.  
A dedicated page in the SAP Support Portal at support.sap.com/esrc  provides more information and 
serves as a n additional  springboard into the application.  
Release Notes Wave 8/201 9 – SAP ONE Support Launchpad and SAP ONE Support Applications  
7 
  
New Application Schedule a Manager   
NOTE: This application is currently released for pilot customers  only .  
Schedule a Manag er is a new real -time support channel, giving customers the ability to schedule a 
15-minute call with a n SAP  Product Support manager. Its aim is to prevent or manage potential 
service exceptions.  
What qualifies an incident for Schedule a Manager ? 
The Sched ule a Manager  button will appear in an incident in the SAP ONE Support Launchpad when 
the following conditions are met:  
▪ The incident has been on priority high for at least 2 days after its creation . 
▪ A manager from the corresponding product area is available for scheduling via prompted 
calendar functionalities . 
▪ The incident is within SAP Product Support (Note: Schedule a Manager  is not offered for 
incidents processed within SAP Development Support) . 
If an incident qualifies for the service, the butto n will be active. To book a session click on Schedule a 
Manager  and then click on Book a Session . 
Release Notes Wave 8/201 9 – SAP ONE Support Launchpad and SAP ONE Support Applications  
8 
  
Starting with a pilot phase, the service will be available on a limited number of products. Customers 
can check for availability in their solution via the S AP ONE Support Launchpad. If available, the 
Schedule a Manager  button will be active.  
Availability of the service will be extended as the service is deployed further throughout 2020.  
Incident Management  
Legacy Incidents  – Act Now to Archive Them!  
The Download Legacy Incidents  application  allows you to download incidents that were closed prior 
to January 1, 2014, from the service.sap.com legacy platform  to your local compute r. Due to the 
imminent  decommission ing of that platform the download will no longer be supported after January 
1, 2020 . Act now if you would like to archive your company’s old incidents.  
Review Incident Before Submitting to SAP Support  
Customers are now given the opportunity to review the content of the incident, prior to submission, 
allowing them to refine the information provided where required.  
By clicking on Edit within the element that needs changing, the user is taken back to the 
corresponding incid ent form section where changes can be made and then reviewed again prior to 
submitting to SAP Support.  
Release Notes Wave 8/201 9 – SAP ONE Support Launchpad and SAP ONE Support Applications  
9 
  
The soft validation that is provided during the incident creation is also provided within the review to 
help you share additional information if possible.  
Enhancements for the Support Log Analyzer  
The Support  Log Analyzer allows customers to add any support -related text files they want (log, 
traces, configuration files, etc.) and scan them against our growing database of known issues . It is 
also available as a self -service tool, outside of the incident form, known as the Support Log Assistant  
– Self Service Log Analyzer . 
For more information on the Support Log Analyzer , see the Wave 5 and Wave 6 r elease notes  at 
support.sap.com/about . For an updated list of supported components, please check out knowledge 
base article  2805235  – Suggested files to  attach during the Incident Creation process - List of 
available components . 
For more information on the self-service tool, see our SAP Community wiki on the Support Log 
Assistant . 
In this wave, the following enhancements have been implemented:  
▪ Attached zipped file content can be extracted and analyzed in the same way as files are 
analy zed when attached separately.  
▪ Increased visibility of the Analy ze File link has been pro vided to make it clearer to customers 
that files are attached which can be analy zed by the Support Log Analyzer.  
▪ The text layout for the  Suggested Files  information pop -up has been improved , making the 
information easier to read.  
Support Assistant  
Incident Solution Matching recommendations are now available in the Support Assistant workflow, 
displayed on the right -hand side, once the final two questions asked by the Support Assistant have 
been answered.  
Release Notes Wave 8/201 9 – SAP ONE Support Launchpad and SAP ONE Support Applications  
10 
 Other Changes  
▪ When selecting a system from the  All Systems  tab, the “Data Protection” filter name and 
corresponding column name in the results list have been renamed to “EU Data Processing.”  
▪ Where customers provide steps to reproduce in the details text, a check box is provided for 
the customers to co nfirm in advance that the step can be followed by SAP Support, during a 
remote connection session : 
 
▪ The Product  dropdown in the System Information  section has been improved to provide an 
information button, giving you information relating to the selection  if the expected product is 
not available in the dropdown. Advices are also provided on what should be done to make 
the product available.  
 
Release Notes Wave 8/201 9 – SAP ONE Support Launchpad and SAP ONE Support Applications  
11 
 ▪ In the component selection,  component acronyms in brackets next to the component name  
improve the readabili ty of the  SAP Suggestions  and Recently Used  results list s. 
SAP Notes  and Knowledge Base Articles (KBAs)  
SAP Notes and SAP Knowledge Base Articles now available in Chinese, Spanish and 
French  
SAP offers automatic translation for SAP Notes and SAP Knowledge Base Articles (KBAs) in the SAP 
ONE Support Launchpad. Customers get fast, machine -generated translations at the click of a button 
and at no additional cost. Now, in addition to Japanese and Portuguese (Brazil), we have added 
machine translations for Chinese, Spanish and French. Read more . 
New Document Type in My SAP Notes & KBAs Expert S earch  
The new document type SAP TopSolution  has been added as a filter value to the Expert Search in the 
My SAP Notes & KBAs  application. SAP TopSolutions are the most important SAP Notes and KB Ss of a  
primary application area  (or sub -area), measured by the number of confirmed support incidents they 
were attached to as a solution.  
 
Of course , like with  other filter criteria,  you can set up filters for future reuse and get notifications 
sent to you ever y time a new SAP TopSolution is released (see https://blogs.sap.com/?p=481297 ).  
User Management  
New Features  in the Technical Communication User  Section  
▪ Technical user IDs that got locked due to repeated logon attempts with an incorrect 
password can now be unlocked in the Technical Communication User  application. Instead of 
Release Notes Wave 8/201 9 – SAP ONE Support Launchpad and SAP ONE Support Applications  
12 
 changing the user’s password, like in the past, simply mark the respective  row in the list of 
users and click the Unlock  button in the footer action bar. Locked users are easily identifiable 
through a new icon in the Active for Data Transfer  column.  
▪ The list of technical communication users, IDs used for system -to-system connec tions 
between your landscape and the SAP Support backbone, can now be downloaded to a local 
comma -separated values (CSV) file.  
 
▪ The last login date is now shown in the list.  
Small Enhancements and Bug Fixes  
▪ In the list of newly requested users, the column Password  has been removed . This 
information is no longer required due to the new email -based user ID generation process  
that went live in early 2019.  
▪ In the Request User  form, the field Customer  is prefilled  if only one customer number exists.  
▪ A bug where  in the Important Contacts  list the corresponding  installation number was not 
shown for cloud administrators has been corrected.  
Advance Notice: Changes to Cloud Administrator Default Authorizations  
Starting January 23, 2020, the default authorization profile for new cloud administrators will be 
changed: The authorization Edit all Login Data  will no longer be granted to new cloud administrators.  
Please note that this change will not aff ect existing cloud administrators . If you want them to lose 
this permission, a user administrator must remove it from their authorization profile.  
Installation Management  
In the Request Installation  form, the product selection now features a hierarchical rather than a plain 
list, which makes selecting the correct product much easier:  
Release Notes Wave 8/201 9 – SAP ONE Support Launchpad and SAP ONE Support Applications  
13 
  
System Data Management  
System Data  Application  
The following changes have been implemented in the System Data  applic ation:  
▪ To avoid misunderstandings, the name of the filter Product Version  in the applications left-
hand list of systems (“master list) has been changed to Leading Product Version . 
Note that this filter lets you narrow down the list of all systems to those systems that have 
the specified product version as a leading  product version, not any product version.  
▪ System data updates through Landscape Information Services (LIS) are now also detected 
and identified as automated update s (before : only updates via SAP Solution Manager ), and 
this information is shown accordingly.  
▪ For consistency reasons,  leading zeroes for installation number s have been removed  in the 
application’s left -hand (master) list of systems . Both the master list as well as the system 
detail page on the right now show installation numbers as “ 20659001 , not “ 0020659001 ”. 
▪ For consistency reasons, like in the systems’ detail pages the official product version  name s 
are shown in the master list, not the ir technical name s. 
System Overview  Application: New Columns  
Two new columns Installation Product  and Software Class  have been added  to lists of systems in the 
System Overview  application . They are also included in local XLS file if you download the list to your 
computer . 
Release Notes Wave 8/201 9 – SAP ONE Support Launchpad and SAP ONE Support Applications  
14 
  
My Landscape  Application:  New Card Status of Support Connectivity   
SAP developed a new cloud extension for SAP Solution Manager allowing customers to synchronize 
their landscape data as well as the SAP EarlyWatch Alert (EWA) data w ith SAP. The landscape data is 
required for Maintenance Planner, and the EWA data for the SAP EarlyWatch Alert Workspace  
application in the SAP ONE Support Launchpad.  
This cloud extension can be used by customers having a small, standard SAP landscape and not using 
any ALM or IT Service Management functionality in SAP Solution Manager.  
With the current release of SAP ONE Support Launchpad, customers will find a new card Status of 
Support Connectivity  in the My Landscape  application . The card shows the data collection status for 
the customer’s on -premise landscape:  
Release Notes Wave 8/201 9 – SAP ONE Support Launchpad and SAP ONE Support Applications  
15 
   
Maintenance Planner and SAP EarlyWatch Alert are essential for best support:  
▪ Maintenance Planner is a solution hosted by SAP that helps you plan and maintain systems in 
your landscape. You can plan complex activities such as installing a new system or updating 
existing systems.  
▪ SAP EarlyWatch Alert is an automatic service monitoring the essential administrative areas of 
an SAP system. Alerts indicate critical situations and give solutions to  improve performance 
and stability.  
Both services require data for the systems sent to SAP. The new card Status of Support Connectivity  
gives an overview about the data collection status and the rating based on this data.  
Once they click on the card , users can see detailed information in the corresponding application. For 
systems not yet sending data to SAP, the user will be assisted in proper ly configur ing data 
synchronization.  
Note: Full landscape management and other Application Lifecycle Manageme nt functionality is 
available with SAP Solution Manager only.  
Cloud Availability Center  
The following enhancements and bug fixes have been implemented:  
▪ On the tenant page, layout adjustments of the page header enhance readability.  
▪ A bug where the detailed SLA date for each month was off by one day for certain time  zones 
has been corrected.  
▪ A bug where the dates for Event Start and Notification Sent were not specified  in the 
download version (XLS file) of the notification report has been corrected . 
Release Notes Wave 8/201 9 – SAP ONE Support Launchpad and SAP ONE Support Applications  
16 
 Service Messages  
Adjusted Auto -Forwarding  Process  
Since Wave 5/2018, h osting partners can set up auto -forwarding of SAP EarlyWatch Alert (EWA) 
messages to their end customers. Once this has been set up, SAP will automatically forward any 
update of the service message to those users of the customer specified by the partner.  
In the past, reports were forwarded to recipients as e-mail attachment . For enhanced security , the 
new email template contains links to document s safely stored  on the SAP Document Service . To 
follow these links  and access service messages , e-mail recipient s need to get a P-user ID (unless they 
already have an S -user ID) . 
In contrast to S -users, P -users are not require d to own any SAP product. You can apply for a P-user ID 
simply by providing a valid e -mail address  at community.sap.com  (click the Login/Sign -up button, 
then Register ). 
Note: If you already set up auto -forwarding in the past (using the old  e-mail template), you need to 
click the Deactivate auto -forwarding  button to reset the content of template. Afterwards, re-enter all 
recipients in the To field and click Save . 
HANA Enterprise Cloud (HEC)  
Service Requests  for Multiple Systems  
The Service Request  application  now allows you to request a service for multiple systems , making the 
request of services more efficient . This feature is offered for services  that do not require system -
specific input parameter s. Once y ou submit the request, one service request will be automatically 
created for each system . They  will be combined under one Bundle ID. Individual processing and 
scheduling is still possible. Applicable services offer multi -system selection and scheduling  features . Release Notes Wave 8/201 9 – SAP ONE Support Launchpad and SAP ONE Support Applications  
17 
  
The Search  now also covers the complete service request h eader information. As a result , it is now 
possible to search e.g. for an external ID (e.g. incident number ). 
Database Information in Landscape  Application  
For the SAP HANA database, the Database  tab now shows information about the SQL port.  
Twitter Feed on SAP Support Portal Homepage  
Note: This feature will go live shortly after the November 16, 2019, release date.  
To give you informative and engaging support -related content in real -time , a Twitter feed has been 
implemented for the SAP Support Portal homepage.  
Release Notes Wave 8/201 9 – SAP ONE Support Launchpad and SAP ONE Support Applications  
18 
  
You can select to follow either @sapsupporthelp or @sapsfcloudhelp twee ts.  
Appendix  
Release Dates  
The 2019  release da tes for the SAP support applications are:  
▪ Wave 1: Thursday January 17, 2019  
▪ Wave 2: Saturday February 23, 2019  
▪ Wave 3: Thursday April 11, 2019  
▪ Wave 4: Saturday May 25, 2019  
▪ Wave 5: Thursday July 11, 2019  
▪ Wave 6: Saturday August 17, 2019  
▪ Wave 7: Thursday September 19, 2019  
▪ Wave 8: Saturday November 16, 2019  
In 2020, the release dates are:  
▪ Wave 1: Thursday January 16, 2020  
▪ Wave 2: Saturday February 15, 2020  
▪ Wave 3: Thursday April 2, 2020  
▪ Wave 4: Saturday May 23, 2020  
▪ Wave 5: Thursday July 9, 2020  
▪ Wave 6: Saturday August 22, 2020  
▪ Wave 7: Thursday October 15, 2020  
▪ Wave 8: Saturday November 21, 2020  
Release Notes Wave 8/201 9 – SAP ONE Support Launchpad and SAP ONE Support Applications  
19 
 Piloting Program for the SAP ONE Support Launchpad  
We invite interested customers and partners to a special piloting program for the SAP ONE Support 
Launc hpad. In this program, we offer roll -out sessions where we present new functionality that has 
become available since the previous release, give an outlook and insight on what we are currently 
working on, and collect and discuss feedback and ideas with part icipants. Sessions are held every 6 -8 
weeks. All interested parties can participate without obligations.  
In case you would like to be involved and invited to future sessions, simply send an email to 
<EMAIL>  containing your name, S -user ID, e -mail address, and name of 
your company.  