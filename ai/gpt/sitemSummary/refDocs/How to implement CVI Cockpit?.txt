


How to implement CVI Cockpit? - CVI Cockpit and TCI for CVI - Guided Answers



Available ValuesInformation Message Strip CloseInformation Message Strip ClosePositive ActionNegative ActionMoreEmphasized
Loading ...

HomeCVI Cockpit and TCI for CVIclose-command-fieldTimelineHow to implement CVI Cockpit? How to implement CVI Cockpit?moreBack one step.How to implement CVI Cockpit?Start overTo address the migration for ERP/ECC Customers, SAP has delivered a standard centralized guided Cockpit, that would have seamless integration to all the required process components, in the intended sequence. This would enable customer/consultant community to perform CVI Synchronization in a hassle-free manner, assisted by a cognitive Cockpit, and get a holistic view of end-to-end conversion process.
For implementing CVI Cockpit (transaction code CVI_COCKPIT) you should consider Note 2951811 - Transport-based correction instructions (TCIs) for customer vendor integration (CVI) on ECC (SAP_APPL). SAP has introduced this TCI Note to facilitate a simpler and quicker process of implementing relevant CVI related SAP Notes. The details of this TCI Note can be found in the CVI Cookbook (Section 7.3.1). The following decision tree is a guide to evaluate how to implement the CVI Cockpit. CVI Cockpit can be introduced with OR without implementing the TCI Note into your ERP system. Additional steps might be required based on your ERP version and support package level.
In order to ease the implementation of a big number of CVI related SAP Notes, SAP has collected CVI relevant corrections into a Transport-Based Correction Instruction note (TCI Note). The implementation of the CVI TCI SAP Note is recommended in early stages of an CVI project as preparation for an SAP S/4HANA conversion project.
For more information please refer to the SAP S/4 HANA CVI Cookbook.Do you want to apply TCI note 2951811 - Transport-based correction instructions (TCIs) for customer vendor integration (CVI) on ECC (SAP_APPL) for CVI_COCKPIT?Yes, I want to implement TCI Note 2951811 - Transport-based correction instructions (TCIs) for customer vendor integration (CVI) on ECC (SAP_APPL) No, I want to continue without applying the TCI Note 2951811 - Transport-based correction instructions (TCIs) for customer vendor integration (CVI) on ECC (SAP_APPL) open-command-fieldcommentprintshare-2Is this content helpful?icon-face-happyicon-face-bad SubmitMessage Strip InformationYour feedback is anonymous, we do not collect any personal data.Terms of UseCopyright and TrademarksLegal DisclosurePrivacy

