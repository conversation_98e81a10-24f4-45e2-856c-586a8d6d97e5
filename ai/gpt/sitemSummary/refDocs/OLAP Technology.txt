OLAP Technology | SAP Help PortalHomeSupport ContentBusiness Warehouse – Analytic Engine (OLAP) and PlanningOLAP TechnologyBusiness Warehouse – Analytic Engine (OLAP) and PlanningProductionStates:DraftProductionAdditional Content This documentSearch Scopes:All SAP productsThis productThis documentAll of these words:Any of these words:This exact word or phrase:None of these words:ClearSearchAdvanced SearchTable of ContentsFavoriteThis page has been removed from your favorites.Download PDFThe following PDF options are available for this document:Create Custom PDFFeedback You can now add comments and questions right into SAP documentation and start a conversation with SAP authors and experts. Don't show me againFeedbackShareMoreMetadata Analytics Subscribe Table of Contents Business Warehouse – Analytic Engine (OLAP) and Planning  OLAP Technology  BW OLAP Layer  BW OLAP Services  BW InfoProvider Layer  BW running on HANA and SAP BW/4HANA  BW APIs to OLAP  Other BW OT Topics  OLAP Media Center  BW-PLA - Planning  OLAP TechnologyOn this pageOLAP LayerBW OLAP ServicesInfoProvider LayerBW running on HANA Database and SAP BW/4HANABW APIs to OLAPOther OT TopicsAnalytic Manager / OLAP Engine
Note that the terms Analytic Manager(Processor) and OLAP Engine are synonyms for to the same functionality which
  provides you with analytical OLAP functions and services. It is optimized for analyzing and reporting on large
  datasets. This page contains detailed information to the Analytic Manager and some other related topics like F4 help
  and the interface to the data base (also called DataManager). It should help to get a better understanding of the
  basic concepts and give you a guideline how to analyze problems in these areas.
The content of this site is in large part valid for both, Netweaver BW systems (including S/4HANA embedded BW) and
  BW/4Hana systems. If this isn't the case we added a corresponding explanation to the affected topic/sub-page.
OLAP Layer

OLAP Core Features / Calculation of Formulas,
      Exception Aggregation, Constant Selection, Virtual Key Figures, Formula Variables ...
Query Performance and Memory
      Consumption / Query Performance and Memory Consumption
Embedded Reporting on ABAP CDS
      Views and CDS Based Query
      Examples / BW Query scenarios with ABAP and S/4HANA CDS Views
OLAP Engine and SAC BW Live
      Connection / SAC related OLAP Topics
OLAP Engine and Analysis for Office /
    AO related OLAP Topics
Support Tools /
    RSRT, RSTT, RSECADMIN, ..

BW OLAP Services

MasterData&F4 Help /
    Input Help, F4 Modes, Special Characteristics, Master Data Read Services
Report to Report Interface / Report to
      Report Interface
Variable Processing / Customer Exit Variables,
      Refresh Variables, ...
Hierarchy Handling / Presentation
      Hierarchies
Currency Translation / Currency
      Translation
Unit Conversion / Unit Conversion
OLAP Authorizations / OLAP Authorization


InfoProvider Layer

Database Interface  / Transaction LISTCUBE,
      RSDRI_INFOPROV_READ, Compression
Multiprovider / MultiProvider, SPO, Pruning

HANA Provider / Composite Provider
      (HCPR), advanced DSO (ADSO), Open ODS View (ODSV) ...
Virtual Providers / InfoCubes based on Function
      Modules or DTP for Direct Access
Transient Objects / Transient Providers and Queries, Transient Objects based on CDS Views
Inventory Providers / Cubes/ADSOs with non-cumulative Key Figures
Query as InfoProvider / Query as InfoProvider
      (QPROV)
InfoSets / InfoSets

BW running on HANA Database and SAP BW/4HANA

Data Modeling / Hana Optimized Cube,
      CompositeProvider, Datastore Object (Advanced), Open ODS View
OLAP Analytical Engine / Hana
      Pushdown of features like Exception Aggregation

BW APIs to OLAP

MDX / MDX Interface, MDX Processor
 
Easy Query / Easy Query
OData / BW OData Queries

Other OT Topics

OLAP Cache / OLAP Cache
BW Aggregates / OLAP Aggregates
 
SAP BW/4HANA Model Transfer
Analysis Process Designer (APD)
On this pageOLAP LayerBW OLAP ServicesInfoProvider LayerBW running on HANA Database and SAP BW/4HANABW APIs to OLAPOther OT TopicsCommentsThere are no comments on this page. Leave a comment Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedback Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedbackAdditional Content This is additional content provided by SAP but not considered the Documentation as defined by the applicable SAP General Terms and Conditions. Don't show this on page loadGot itCopyrightDisclaimerPrivacy StatementLegal DisclosureTrademarkTerms of UseCookie PreferencesAccessibility and SustainabilitySystem StatusAsk a question about the SAP Help PortalFind us onFollow us on FacebookSAP on FacebookFollow us on YouTubeSAP on YouTubeFollow us on LinkedInSAP on LinkedInFollow us on InstagramSAP on InstagramShare

