Available Migration Objects | SAP Help PortalHomeSAP S/4HANA CloudData MigrationAvailable Migration ObjectsData Migration2402.1Available Versions: 2408 Latest  2402.2  2402.1  2402  2308.4  2308.3  2308.2  2308.1  2308  2302.4 * 2302.3 * 2302.2 * 2302.1 * 2302 * 2208.3 * 2208.2 * 2208.1 * 2208 * 2202.4 * 2202.3 * 2202.2 * 2202.1 * 2202 * 2111.1 * 2111 * 2108.1 * 2108 * 2105 * 2011 DE** This product version is out of mainstream maintenance. The documentation is no longer regularly updated.For more information, see the Product Availability Matrix (PAM)EnglishAvailable Languages: English  Chinese Simplified (简体中文)  French (Français)  German (Deutsch)  Japanese (日本語)  Portuguese for Brazil (Português do Brasil)  Russian (Русский)  Spanish (Español) ProductionStates:TestProductionDraftThis documentSearch Scopes:All SAP productsThis productThis documentAll of these words:Any of these words:This exact word or phrase:None of these words:ClearSearchAdvanced SearchTable of ContentsFavoriteThis page has been removed from your favorites.Download PDFThe following PDF options are available for this document:Create Custom PDFFeedback You can now add comments and questions right into SAP documentation and start a conversation with SAP authors and experts. Don't show me againFeedbackShareEdit in IXIA CCMS Web You can now go directly to IXIA CCMS Web to edit the topic. Simply select Edit in IXIA CCMS Web link under the More… menu. Don't show me againMoreMetadata Analytics Subscribe Edit in IXIA CCMS WebTable of Contents Data Migration  Migrate Your Data - Migration Cockpit  Available Migration Objects  Data Migration Status  Situation Handling for Data Migration  Video Library for Data Migration  Migrate Your Data - Migration Cockpit (Old) - Deprecated  Available Migration Objects


You can use the following table to gain an overview of all migration objects available for SAP S/4HANA Cloud. It’s sorted in alphabetical order by Migration Object Name. Choose a migration object name to navigate to the documentation for the migration object.


Tip

Filter the Migration Approach column to only see migration objects relevant for you. Therefore, choose Filter in the respective column, and select the required approach, Direct Transfer or Staging Table.
If you want to see less or more information, choose Hide/Show Columns, and select the respective checkboxes for the columns you would like to hide or show, for example, the Predecessor Object or Scope Item column.




Hide/Show ColumnsMigration Object NameBusiness Object TypeBusiness CatalogMigration ApproachCustom Field SupportComponentPredecessor ObjectScope ItemDownload DataSelect a file type to downloadXLSXCSVSelect the CSV delimiter you want to use for your fileSemi-Colon (;)Comma (,)Pipe Line (|)Caret (^)Which data do you want to download?Download filtered data on this pageDownload all data on all pagesDownload Download Migration Object NameBusiness Object TypeBusiness CatalogMigration ApproachCustom Field SupportComponentPredecessor ObjectScope Item Filter: [No Selection]Select AllNo values match your filter.Master dataMaster DataTransactional data Filter: [No Selection]Select AllNo values match your filter.Not applicableSAP_BR_MAINTENANCE_PLANNERSAP_CA_BC_BNK_PCSAP_CA_BC_CONSENT_MGMT_PCSAP_CA_BC_RSH_CUSTPROJ_PSP_PCSAP_CMD_BC_BP_DISP_PCSAP_CMD_BC_BP_MAINT_PCSAP_CMD_BC_CUST_MAINT_PCSAP_CMD_BC_PR_MAINTSAP_CMD_BC_PR_MAINT_PCSAP_CMD_BC_SUPP_MAINT_PCSAP_CRM_BC_SERV_PROCORDER_PCSAP_EAM_BC_MEADOC_MCSAP_EAM_BC_MEAPT_MCSAP_EAM_BC_MESP_MNG_PCSAP_EAM_BC_MNTITEM_MNG_PCSAP_EAM_BC_MP_MNG_PCSAP_EAM_BC_MP_ORD_DSP_PCSAP_EAM_BC_MPLAN_MCSAP_EAM_BC_MPLANIT_MCSAP_EAM_BC_NTF_MCSAP_EAM_BC_ORD_MNG_PCSAP_EAM_BC_TL_MCSAP_EAM_BC_TO_MCSAP_EAM_BC_TO_MNG_PCSAP_EAM_BC_TSKL_MNG_PCSAP_EHS_BC_ENV_DT_CLS_MNG_PCSAP_EHS_BC_ENV_MNG_CHPHYS_PCSAP_EHS_BC_ENV_MNG_CMPLREQ_PCSAP_EHS_BC_ENV_MNG_CSCEN_PCSAP_EHS_BC_ENV_MNG_DEV_PCSAP_EHS_BC_ENV_MNG_LOC_PCSAP_EHS_BC_ENV_MNG_LOCCL_PCSAP_EHS_BC_ENV_OPR_DATACOL_PCSAP_EHS_BC_ENV_TASK_MON_PCSAP_EHS_BC_IM_INC_MGMT_PCSAP_EHS_BC_IM_MNG_LOC_PCSAP_EHS_BC_MOC_CREQ_MNG_PCSAP_FICA_BC_DOC_POST_PCSAP_FICA_BC_DUN_MASS_PCSAP_FICA_BC_INSTPL_PROC_PCSAP_FICA_BC_INTRST_PROC_PCSAP_FICA_BC_MD_CA_MAINT_PCSAP_FICA_BC_MD_PC_MAINT_PCSAP_FICA_BC_PRMS2P_MAINT_PCSAP_FICA_BC_SCRTY_DEP_PROC_PCSAP_FIN_BC_AA_DOC_PROC_PCSAP_FIN_BC_AA_MDAT_LDT_PCSAP_FIN_BC_AA_MDAT_REG_PCSAP_FIN_BC_AA_MDAT_USE_OBJ_PCSAP_FIN_BC_AP_IMPORT_RU_PCSAP_FIN_BC_APAR_OPER_PR_RU_PCSAP_FIN_BC_AR_DDM_PROC_PCSAP_FIN_BC_AR_SEPA_EDIT_PCSAP_FIN_BC_ARO_MANAGESAP_FIN_BC_CA_CURR_PCSAP_FIN_BC_CM_BNK_PCSAP_FIN_BC_CM_OPS_BASIC_PCSAP_FIN_BC_CR_CRED_ACC_PCSAP_FIN_BC_FARR_RAIPRC_PCSAP_FIN_BC_GL_JE_PROC_PCSAP_FIN_BC_GL_REPORTING_BR_PCSAP_FIN_BC_JVA_CMD_PCSAP_FIN_BC_OH_MD_CCA_PCSAP_FIN_BC_OH_MD_OPA_PCSAP_FIN_BC_OH_MD_PCA_PCSAP_FIN_BC_RECM_CONTRACT_PCSAP_FIN_BC_REIP_LOCATION_PCSAP_FIN_BC_TRM_LGCYDT_PCSAP_FIN_BC_TRM_MFT1_PCSAP_LCM_BC_ADMINISTRATOR_PCSAP_LE_BC_JIT_CUST_MAINT_PCSAP_LO_BC_HU_MANAGE_PCSAP_LO_BC_VC_MODELINGSAP_LO_BC_VC_MODELING_PCSAP_LO_BC_WTY_MANAGECLAIM_PCSAP_MM_BC_CC_CRTE_PCSAP_MM_BC_CC_MAN_PCSAP_MM_BC_IM_MAT_DOC_API_PCSAP_MM_BC_IR_PROCESS_PCSAP_MM_BC_MPS_PROCESS_PCSAP_MM_BC_PO_MANAGE_PCSAP_MM_BC_PRCG_PROCESS_PCSAP_MM_BC_PURCH_DOC_DSP_PCSAP_MM_BC_SA_PROCESS_MCSAP_OIL_BC_FLOG_CTNMSTR_PCSAP_OIL_BC_FLOG_SUPLRITEM_PCSAP_OIL_BC_PVOL_CAPTURE_PCSAP_PLM_BC_CLF_MCSAP_PLM_BC_CM_MCSAP_PLM_BC_DIR_EXTD_MCSAP_PLM_BC_DIR_PCSAP_PLM_BC_MBOMSAP_PLM_BC_MBOM_PCSAP_PLM_BC_SALES_ORDER_BOM_PCSAP_PS_BC_PROJ_CONTRL_MCSAP_PS_BC_PROJ_FIN_ANLYTC_MCSAP_PSM_BC_EMRKFNDS_API_PCSAP_PSM_BC_MD_BDGTSP_PCSAP_PSM_BC_MD_GRNTSP_PCSAP_PSP_BC_CUSTOMER_PROJ_PCSAP_PSP_BC_MIG_PROFNL_PROJ_PCSAP_PSS_BC_PC_CCM_CRR_MON_PCSAP_PSS_BC_PC_CCM_MAN_PCSAP_PSS_BC_PC_FND_PMON_PCSAP_PSS_BC_PC_FND_RMON_PCSAP_PSS_BC_PC_FND_SMMON_PCSAP_PSS_BC_PC_PKG_DG_MGMT_PCSAP_PSS_BC_PC_PMA_ANALYZE_PCSAP_PSS_BC_PC_PROD_CRR_MON_PCSAP_PSS_BC_PC_RM_CRR_MON_PCSAP_PSS_BC_PC_SUBST_MAN_PCSAP_PSS_BC_PC_TECH_NAMES_PCSAP_PSS_BC_PC_UPKG_DG_MGMT_PCSAP_QM_BC_BASIC_DATA_PCSAP_QM_BC_CERTIFICATE_PLNG_PCSAP_QM_BC_INSP_METHODS_PCSAP_QM_BC_INSP_PLANNING_PCSAP_QM_BC_MSTR_INSP_CHARC_PCSAP_QM_BC_QM_PROCUREMENT_PCSAP_RFM_BC_ASSTMT_MAINT_PCSAP_RFM_BC_MRCHDSCAT_MNTN_PCSAP_RFM_BC_SITE_MAINT_PCSAP_RFM_BC_SITE_MNGGRP_PCSAP_S4CRM_BC_SERV_CONTR_PCSAP_S4CRM_BC_SOLN_ORD_PROC_PCSAP_SCM_BC_BATCH_MGMT_MCSAP_SCM_BC_EWM_MD_WHSE_PCSAP_SCM_BC_EWM_MON_PCSAP_SCM_BC_IM_BATCH_SEARCH_PCSAP_SCM_BC_KNBN_CTRLC_MGMT_MCSAP_SCM_BC_PO_BATCH_SEARCH_PCSAP_SCM_BC_PPROC_ENG_MCSAP_SCM_BC_PROC_ENG_MCSAP_SCM_BC_PRODN_ORD_CTRL_MCSAP_SCM_BC_PRODN_ORD_MPLN_MCSAP_SCM_BC_SD_BATCH_SEARCH_PCSAP_SD_BC_CC_CRTE_PCSAP_SD_BC_CC_EC_CRTE_PCSAP_SD_BC_CC_EC_MNG_PCSAP_SD_BC_CC_MAN_PCSAP_SD_BC_CC_RS_CRTE_PCSAP_SD_BC_CC_RS_MNG_PCSAP_SD_BC_CONTR_PROC_MCSAP_SD_BC_MD_MANAGE_MCSAP_SD_BC_PRICE_MANAGE_MCSAP_SD_BC_SLS_SCHEDULE_PCSAP_SD_BC_SO_PROC_MCSAP_SLL_BC_CLS_CMMDTYCODE_PCSAP_SLL_BC_CLS_LEGCTRL_PCSAP_TC_CMD_BP_COMMONSAP_TC_SCM_PP_BE_APPSSAP_TC_SCM_PP_COMMON Filter: [No Selection]No values match your filter.Direct TransferStaging Table Filter: [No Selection]No values match your filter.Not applicableYes Filter: [No Selection]Select AllNo values match your filter.AP-MD-BPCA-AROCA-CLCA-CPD-SSCA-DMSCA-FL-SGCA-FL-SRVCA-GTF-CONCA-IAM-MOCCA-JVACA-PVLCM-GFCOCRM-S4-SOL-SLOCRM-S4-SRV-CTRCRM-S4-SRV-SVOEHS-SUS-CIEHS-SUS-DGEHS-SUS-EMEHS-SUS-FNDEHS-SUS-IMEHS-SUS-PMAEHS-SUS-SDSFIFI-AAFI-APFI-ARFI-CAFI-LOC-FIFI-LOC-FI-RUFI-RAFIN-FSCM-CLMFIN-FSCM-TRMFIN-FSCM-TRM-TMFIN-FSCM-TRM-TM-TFFT-ITR-CLSIS-OIL-PRALE-JIT-S2CLOLO-ABLO-BMLO-HU-BFLO-MDLO-MD-BP-CMLO-MD-BP-VMLO-RFM-MD-LSTLO-RFM-MD-MCLO-RFM-MD-SITLO-VCHLO-WTYMMMM-IMMM-PURMM-PUR-MPSPLM-WUI-OBJ-ECNPMPPPP-BDPP-BD-RTGPP-KABPP-PIPP-PI-PORPP-SFCPPM-SCL-STRPSM-FMPSM-FM-MDPSM-GM-GTE-MDQMRE-FXSCM-EWMSDSD-SLS Filter: [No Selection]Select AllNo values match your filter.Asset retirement obligation - Master dataAVC - Configuration profile (restricted)AVC - Dependency netAVC - Object dependenciesAVC - Variant table structureBankBatch unique at material and client levelCharacteristicClassCO - Activity typeCO - Cost centerCO - Internal order (restricted)CO - Profit centerCommercial projectCondition record for pricing (general template)Condition record for pricing in sales (restricted)CustomerDG - Assessment for unpackaged product (content-based)DG - Assessment for unpackaged product (text-based)DG - Technical names for substanceDocument info recordECM - Change masterEHS - Calculation definitionEHS - Chemical/Physical propertyEHS - Compliance requirementEHS - Compliance scenarioEHS - Data classifierEHS - Data collectionEHS - Deviation incidentEHS - LocationEHS - Location aggregationEHS - Location classifierEnterprise projectExchange rateFI - G/L account balance and open/line itemFI - SEPA mandateFI-CA - Cash security depositFI-CA - Contract accountFI-CA - Contract partnerFI-CA - Dunning historyFI-CA - Installment planFI-CA - Open itemFI-CA - PaymentFixed asset - Master dataFixed asset - Usage objectImport customs declaration (Russia)JIT - CustomerJIT - Supply controlJVA - Joint operating agreementJVA - Joint venture master dataJVA - Joint venture partnerLegal transactionMaster recipeMaterial BOMMaterial inventory balanceMM - Purchase contractMM - Purchase order (only open PO)MM - Purchase scheduling agreementMM - Purchasing info record with conditionsNot applicableObject classification - General templatePC - Analytical compositionPC - Polymer compositionPC - Product compliance infoPC - Raw material compliance infoPC - SubstancePM - EquipmentPM - Equipment task listPM - Functional locationPM - Functional location task listPM - General maintenance task listPM - Maintenance itemPM - Maintenance notificationPM - Maintenance planPM - Measurement documentPM - Measuring pointPP - Process order (only open PO)PP - Production order (only open PO)PP-KAB - Production supply areaPRA - Check input process rulePRA - ContractPRA - Delivery networkPRA - Delivery network allocation profilePRA - Delivery network downstream nodePRA - Division order interestPRA - DOI MP WC cross referencePRA - extend existing customer by PRA dataPRA - extend existing supplier by PRA dataPRA - Joint venturePRA - Marketing group assignmentPRA - Measurement pointPRA - Measurement point allocation profilePRA - MP-WC to contract cross referencePRA - MP/WC transporter cross referencePRA - Ownership leasePRA - Unit venture tractPRA - Valuation formulaPRA - Well and well completionProductProduct hierarchyProduction versionPSM - Functional areaPSM - FundPSM - GrantPSM - Sponsored classPSM - Sponsored programQM - Inspection methodQM - Master inspection characteristicQM - Selected setQM - Selected set codeQM/PM - Catalog code group/codeReal estate - Occupancy groupReal estate - Usage enablement groupReal estate contractRFM - Merchandise categoryRFM - Merchandise category hierarchy nodeRFM - Site masterRFM - Supplying siteRoutingSD - Sales contractSD - Sales order (only open SO)SD - Sales scheduling agreementService contractService order (only open SRVO)SupplierTRM - Bank guaranteeTRM - Commercial paperTRM - Foreign exchange spot/forward transaction - contractTRM - Foreign exchange swap - contractTRM - FX optionTRM - Interest rate instrumentTRM - Interest rate swapWarehouse storage binWork center/Resource Filter: [No Selection]Select AllNo values match your filter.16T19M1A81B61BM1E11E31EZ1F11FM1GB1GF1HO1IH1IL1J21KA1MP1MR1NT1RM1T61W81WV1X11X31X71X91XB1XD1XF1XV1Y21YF1YI1YT21D21Q21R21T22P22T22Z2AR2DP2EM2F22HU2NZ2O22OI2QI2R72RW2SB2TT2U32UG2UJ2XU31G31H31J31L33F33G3BR3D23F43F73FC3FP3G83I13I33I53KK3L73MO3NR3OK3QE3UK3UL3VQ3VR3VS3W33YE41F42K43R49D49E49F4AG4AH4B34B44GT4GX4GY4HH4HI4LZ4MM4OL4VT4WM4XD4YX56E5FJ5HR5IT5NM5OJ5VX5VY5VZ5W064F6AV6BA6MT6YU71S78SBD3BD9BDABDDBDGBDHBDKBDNBDQBDTBDWBEV (not available)BFABFBBFHBH1BH2BJ2BJ5BJ8BJEBJHBJKBJNBJQBKABKJBKKBKLBKPBKXBKZBLFBLLBMCBMDBMKBMLBMRBMYBNXI9IJ11J12J13J14J44J45J54J55J58J59J60J62J77J78J82Not applicableO1QO1RO2KO3RO44O45O54O58O59O60O62O77O78O9MOAROD9ODGODPODQOF1OF4OFAOFBOHHOHIOHROKLOMCOMDOMKOMLONTONXONZOSBOT6OVTOVXOVYOVZOW0OWMOWVOX1OX3OXFOYFPEZPFPPJ2PMR (not available)PUJMigration Object NameBusiness Object TypeBusiness CatalogMigration ApproachCustom Field SupportComponentPredecessor ObjectScope ItemNo content displayedAsset retirement obligation - Master dataMaster dataSAP_FIN_BC_ARO_MANAGEStaging TableNot applicableCA-AROCO - Cost center
56E
5IT
Asset retirement obligation - PostingsTransactional dataSAP_FIN_BC_ARO_MANAGEStaging TableNot applicableCA-ARO
Asset retirement obligation - Master data
FI - G/L account balance and open/line item

56E
5IT
AVC - Assign global dependency to characteristicMaster dataSAP_LO_BC_VC_MODELING_PCStaging TableNot applicableLO-VCH
Characteristic
AVC - Object dependencies

22T
AVC - Assign global dependency to characteristic valueMaster dataSAP_LO_BC_VC_MODELING_PCStaging TableNot applicableLO-VCH
Characteristic
AVC - Object dependencies

22T
AVC - Characteristics groupMaster dataSAP_LO_BC_VC_MODELING_PCStaging TableNot applicableLO-VCH
Characteristic
Object classification - General template
Product
Class
AVC - Configuration profile (restricted)

22T
AVC - Configuration profile (restricted)Master dataSAP_LO_BC_VC_MODELING_PCStaging TableNot applicableLO-VCH
Product
AVC - Object dependencies
ECM - Change master
Object classification - General template

1YT
21D
22T
AVC - ConstraintMaster dataSAP_LO_BC_VC_MODELING_PCStaging TableNot applicableLO-VCH
ECM - Change master
Characteristic
AVC - Dependency net

1YT
21D
22T
AVC - Dependency netMaster dataSAP_LO_BC_VC_MODELING_PCStaging TableNot applicableLO-VCHNot applicable
1YT
21D
22T
AVC - Material variantMaster dataSAP_LO_BC_VC_MODELING_PCStaging TableNot applicableLO-VCH
Object classification - General template
AVC - Configuration profile (restricted)
Product

1YT
21D
22T
AVC - Object dependenciesMaster dataSAP_LO_BC_VC_MODELING_PCStaging TableNot applicableLO-VCHCharacteristic
1YT
21D
22T
AVC - Variant table entryMaster dataSAP_LO_BC_VC_MODELING_PCStaging TableNot applicableLO-VCH
AVC - Variant table structure
Characteristic

1YT
21D
22T
AVC - Variant table structureMaster dataSAP_LO_BC_VC_MODELING_PCStaging TableNot applicableLO-VCHCharacteristic
1YT
21D
22T
BankMaster DataSAP_FIN_BC_CM_BNK_PCDirect TransferNot applicableFINot applicableNot applicableBankMaster dataSAP_CA_BC_BNK_PCStaging TableNot applicableFINot applicable
BFA
BFB
J11
J12
J13
J14
J59
J60
J77
J78
O59
O60
O77
O78
OFA
OFB
Batch unique at material and client levelMaster dataSAP_SCM_BC_BATCH_MGMT_MCStaging TableNot applicableLO-BM
Characteristic
Supplier
Product
Object classification - General template
Class

BD9
BDA
BDG
BDH
BDQ
BDW
BFB
BJ5
BJE
BKJ
BKL
BKX
BKZ
BLF
J59
J60
J78
O59
O60
OFB
Business partnerMaster dataSAP_TC_CMD_BP_COMMONDirect TransferNot applicableAP-MD-BPNot applicableNot applicableBusiness solution orderTransactional dataSAP_S4CRM_BC_SOLN_ORD_PROC_PCStaging TableYesCRM-S4-SOL-SLO
Service contract
Customer
Product
Condition record for pricing in sales (restricted)

4GT
Change request and activity for SAP Management of ChangeTransactional dataSAP_EHS_BC_MOC_CREQ_MNG_PCStaging TableNot applicableCA-IAM-MOCNot applicable
4YX
CharacteristicMaster dataSAP_PLM_BC_CLF_MCDirect TransferNot applicableCA-CLNot applicableNot applicableCharacteristicMaster dataSAP_PLM_BC_CLF_MCStaging TableNot applicableCA-CL
QM - Selected set
ECM - Change master
Document info record
RFM - Merchandise category

5FJ
BD9
BDA
BDD
BDG
BDH
BDQ
BJ5
BJE
BKJ
BKL
BKX
BKZ
BLF
OD9
ODG
ODQ
OKL
ClassMaster dataSAP_LO_BC_VC_MODELINGDirect TransferNot applicableCA-CLNot applicableNot applicableClassMaster dataSAP_PLM_BC_CLF_MCStaging TableNot applicableCA-CL
Characteristic
Document info record

BD9
BDA
BDD
BDG
BDH
BDQ
BJ5
BJE
BKJ
BKL
BKX
BKZ
BLF
OD9
ODG
ODQ
OKL
Class hierarchyTransactional dataSAP_PLM_BC_CLF_MCStaging TableNot applicableCA-CLClass
4HH
4HI
4VT
4WM
BH1
BH2
BJ2
OHH
OHI
OVT
OWM
CO - Activity typeMaster dataSAP_FIN_BC_OH_MD_CCA_PCStaging TableNot applicableCONot applicable
J11
J54
J55
O54
CO - Cost centerMaster dataSAP_FIN_BC_OH_MD_CCA_PCDirect TransferNot applicableCONot applicableNot applicableCO - Cost centerMaster dataSAP_FIN_BC_OH_MD_CCA_PCStaging TableNot applicableCO
PSM - Fund
PSM - Grant
JVA - Joint venture master data
CO - Profit center

J11
J12
J13
J14
J54
J55
J58
J59
J60
O54
O58
O59
O60
CO - Internal order (restricted)Transactional dataSAP_FIN_BC_OH_MD_OPA_PCStaging TableNot applicableCO
Fixed asset - Master data
SD - Sales order (only open SO)
CO - Cost center
CO - Profit center

BEV (not available)
CO - Profit centerMaster dataSAP_FIN_BC_OH_MD_PCA_PCDirect TransferNot applicableCONot applicableNot applicableCO - Profit centerMaster dataSAP_FIN_BC_OH_MD_PCA_PCStaging TableNot applicableCONot applicable
J11
J12
J13
J14
J54
J55
J58
J59
J60
O54
O58
O59
O60
CO - Statistical key figureMaster dataSAP_FIN_BC_OH_MD_CCA_PCStaging TableNot applicableCONot applicable
J54
O54
Commercial projectMaster dataSAP_PSP_BC_MIG_PROFNL_PROJ_PCStaging TableYesCA-CPD-SS
CO - Cost center
Customer
Product
Supplier
CO - Profit center

16T
1A8
1IL
J11
J12
J13
J14
Condition contractTransactional data
SAP_MM_BC_CC_CRTE_PC
SAP_MM_BC_CC_MAN_PC
SAP_SD_BC_CC_EC_MNG_PC
SAP_SD_BC_CC_MAN_PC
SAP_SD_BC_CC_RS_CRTE_PC
SAP_SD_BC_CC_RS_MNG_PC
SAP_SD_BC_CC_EC_CRTE_PC
SAP_SD_BC_CC_CRTE_PC
Staging TableNot applicableLO-AB
Condition record for pricing (general template)
Customer
Product
Service contract
Condition record for pricing in sales (restricted)
Supplier
CO - Cost center

1B6
2R7
2TT
Condition record for batch determination (for all areas)Master data
SAP_SCM_BC_IM_BATCH_SEARCH_PC
SAP_SCM_BC_PO_BATCH_SEARCH_PC
SAP_SCM_BC_SD_BATCH_SEARCH_PC
Staging TableNot applicableLO-BM
Characteristic
Class
Customer
Product

BLF
Condition record for pricing (general template)Master data
SAP_PSP_BC_CUSTOMER_PROJ_PC
SAP_SD_BC_PRICE_MANAGE_MC
SAP_CA_BC_RSH_CUSTPROJ_PSP_PC
SAP_CMD_BC_BP_DISP_PC
SAP_MM_BC_PRCG_PROCESS_PC
Staging TableNot applicableSD
Customer
Product
Supplier

BD3
BD9
BDA
BDD
BDG
BDH
BDK
BDN
BDQ
BDW
BKA
BKJ
BKL
BKX
BKZ
J11
OD9
ODG
ODQ
OKL
Condition record for pricing in purchasing (restricted)Master dataSAP_MM_BC_PRCG_PROCESS_PCStaging TableNot applicableMM-PUR
Product
Supplier

1XF
OXF
Condition record for pricing in sales (restricted)Master data
SAP_PSP_BC_CUSTOMER_PROJ_PC
SAP_CA_BC_RSH_CUSTPROJ_PSP_PC
SAP_SD_BC_PRICE_MANAGE_MC
Staging TableNot applicableSDProduct
BD3
BD9
BDA
BDD
BDG
BDH
BDK
BDN
BDQ
BDW
BKA
BKJ
BKL
BKX
BKZ
J11
OD9
ODG
ODQ
OKL
ConsentTransactional dataSAP_CA_BC_CONSENT_MGMT_PCStaging TableNot applicableCA-GTF-CONNot applicable
1KA
Contract number in accounting documents (Russia)Transactional dataSAP_FIN_BC_APAR_OPER_PR_RU_PCStaging TableNot applicableFI-LOC-FI-RU
Customer
Supplier

3MO
J59
J60
O59
O60
CustomerMaster dataSAP_CMD_BC_CUST_MAINT_PCDirect TransferNot applicableLO-MD-BP-CMNot applicableNot applicableCustomerMaster dataSAP_CMD_BC_CUST_MAINT_PCStaging TableYesSDBank
BD3
BD9
BDA
BDD
BDG
BDH
BDK
BDN
BDQ
BDT
BDW
BFB
BJE
BKA
BKJ
BKL
BKP
BKX
BKZ
J11
J12
J13
J14
J45
J54
J55
J58
J59
J62
J78
O45
O54
O58
O59
O62
O78
OD9
ODG
ODQ
OFB
OKL
Customer - extend existing record by credit management dataMaster dataSAP_FIN_BC_CR_CRED_ACC_PCStaging TableNot applicableSDCustomer
BD3
BD9
BDA
BDD
BDG
BDH
BDK
BDN
BDQ
BDT
BDW
BFB
BJE
BKA
BKJ
BKL
BKP
BKX
BKZ
J11
J12
J13
J14
J45
J54
J55
J58
J59
J62
J78
O45
O54
O58
O59
O62
O78
OD9
ODG
ODQ
OFB
OKL
Customer - extend existing record by multiple addressesMaster dataSAP_CMD_BC_CUST_MAINT_PCStaging TableNot applicableSDCustomer
BD3
BD9
BDA
BDD
BDG
BDH
BDK
BDN
BDQ
BDT
BDW
BFB
BJE
BKA
BKJ
BKL
BKP
BKX
BKZ
J11
J12
J13
J14
J45
J54
J55
J58
J59
J62
J78
O45
O54
O58
O59
O62
O78
OD9
ODG
ODQ
OFB
OKL
Customer - extend existing record by new org levelsMaster dataSAP_CMD_BC_CUST_MAINT_PCStaging TableYesSD
Customer
FI-CA - Contract partner
Supplier

BD3
BD9
BDA
BDD
BDG
BDH
BDK
BDN
BDQ
BDT
BDW
BFB
BJE
BKA
BKJ
BKL
BKP
BKX
BKZ
J11
J12
J13
J14
J45
J54
J55
J58
J59
J62
J78
O45
O54
O58
O59
O62
O78
OD9
ODG
ODQ
OFB
OKL
Customer - extend existing record by Thailand branch codeMaster dataSAP_CMD_BC_CUST_MAINT_PCStaging TableNot applicableSDCustomer
BD3
BD9
BDA
BDD
BDG
BDH
BDK
BDN
BDQ
BDT
BDW
BFB
BJE
BKA
BKJ
BKL
BKP
BKX
BKZ
J11
J12
J13
J14
J45
J54
J55
J58
J59
J62
J78
O45
O54
O58
O59
O62
O78
OD9
ODG
ODQ
OFB
OKL
DG - Assessment for packaged productMaster dataSAP_PSS_BC_PC_PKG_DG_MGMT_PCStaging TableNot applicableEHS-SUS-DG
DG - Assessment for unpackaged product (text-based)
DG - Assessment for unpackaged product (content-based)

3FC
DG - Assessment for unpackaged product (content-based)Master dataSAP_PSS_BC_PC_UPKG_DG_MGMT_PC Staging TableNot applicableEHS-SUS-DG
DG - Technical names for substance
PC - Product compliance info

3FC
DG - Assessment for unpackaged product (text-based)Master dataSAP_PSS_BC_PC_UPKG_DG_MGMT_PC Staging TableNot applicableEHS-SUS-DGPC - Product compliance info
3FC
DG - Technical names for substanceMaster dataSAP_PSS_BC_PC_TECH_NAMES_PCStaging TableNot applicableEHS-SUS-DGPC - Substance
3FC
Document info recordMaster dataSAP_PLM_BC_DIR_PCStaging TableNot applicableCA-DMS
Product
Customer
PM - Equipment
PM - Functional location
PM - Maintenance plan
Supplier
Enterprise project
Commercial project

22P
ECM - Change masterMaster dataSAP_PLM_BC_CM_MCStaging TableNot applicablePLM-WUI-OBJ-ECN
Material BOM
Document info record
Product

64F
EFD - Reinf report (Brazil)Transactional dataSAP_FIN_BC_GL_REPORTING_BR_PCStaging TableNot applicableFI-LOC-FINot applicable
1J2
PJ2
EHS - Calculation definitionMaster dataSAP_EHS_BC_ENV_MNG_CSCEN_PCStaging TableNot applicableEHS-SUS-EM
EHS - Location
EHS - Location aggregation
EHS - Data classifier
EHS - Data collection
EHS - Location classifier
EHS - Chemical/Physical property

4XD
EHS - Chemical/Physical propertyMaster dataSAP_EHS_BC_ENV_MNG_CHPHYS_PCStaging TableNot applicableEHS-SUS-FNDNot applicable
4XD
EHS - Compliance requirementMaster dataSAP_EHS_BC_ENV_MNG_CMPLREQ_PCStaging TableNot applicableEHS-SUS-CI
EHS - Location
EHS - Location classifier
EHS - Chemical/Physical property

4XD
EHS - Compliance scenarioMaster dataSAP_EHS_BC_ENV_MNG_CSCEN_PCStaging TableNot applicableEHS-SUS-EM
EHS - Location aggregation
EHS - Calculation definition
EHS - Location
EHS - Data collection
EHS - Compliance requirement
EHS - Chemical/Physical property
EHS - Location classifier
EHS - Data classifier

4XD
EHS - Data classifierMaster dataSAP_EHS_BC_ENV_DT_CLS_MNG_PCStaging TableNot applicableEHS-SUS-EMNot applicable
4XD
EHS - Data collectionMaster dataSAP_EHS_BC_ENV_OPR_DATACOL_PCStaging TableNot applicableEHS-SUS-EM
EHS - Data classifier
EHS - Location

4XD
EHS - Deviation incidentMaster dataSAP_EHS_BC_ENV_MNG_DEV_PCStaging TableNot applicableEHS-SUS-IM
EHS - Location
EHS - Compliance requirement

4XD
EHS - IncidentTransactional dataSAP_EHS_BC_IM_INC_MGMT_PCStaging TableNot applicableEHS-SUS-IMEHS - Location
3FP
PFP
EHS - LocationMaster dataSAP_EHS_BC_IM_MNG_LOC_PCStaging TableNot applicableEHS-SUS-FND
EHS - Location classifier
EHS - Chemical/Physical property

3FP
4XD
PFP
EHS - Location aggregationMaster dataSAP_EHS_BC_ENV_MNG_CSCEN_PCStaging TableNot applicableEHS-SUS-EM
EHS - Data classifier
EHS - Location

4XD
EHS - Location classifierMaster dataSAP_EHS_BC_ENV_MNG_LOCCL_PCStaging TableNot applicableEHS-SUS-CINot applicable
4XD
EHS - Location hierarchyMaster dataSAP_EHS_BC_ENV_MNG_LOC_PCStaging TableNot applicableEHS-SUS-FNDEHS - Location
3FP
4XD
PFP
EHS - Location hierarchy (deprecated)Master data
SAP_EHS_BC_IM_MNG_LOC_PC
SAP_EHS_BC_ENV_MNG_LOC_PC
Staging TableNot applicableEHS-SUS-FNDEHS - Location
3FP
4XD
PFP
EHS - TaskTransactional dataSAP_EHS_BC_ENV_TASK_MON_PCStaging TableNot applicableEHS-SUS-EM
EHS - Location
EHS - Deviation incident
EHS - Compliance requirement
EHS - Compliance scenario

4XD
Enterprise projectMaster dataSAP_PS_BC_PROJ_CONTRL_MCStaging TableYesPPM-SCL-STR
CO - Cost center
JVA - Joint venture master data
CO - Profit center

1NT
1YF
ONT
OYF
Exchange rateMaster dataSAP_FIN_BC_CA_CURR_PCStaging TableNot applicableFINot applicable
J11
J12
J13
J14
J58
O58
FI - Accounts payable open itemTransactional dataSAP_FIN_BC_GL_JE_PROC_PCStaging TableYesFI-AP
Supplier
Exchange rate
PSM - Functional area
PSM - Fund
PSM - Grant
CO - Profit center

J60
O60
FI - Accounts receivable open itemTransactional dataSAP_FIN_BC_GL_JE_PROC_PCStaging TableYesFI-AR
Customer
Exchange rate
PSM - Functional area
PSM - Fund
PSM - Grant
CO - Profit center

J59
O59
FI - Bank account balanceTransactional dataSAP_FIN_BC_CM_OPS_BASIC_PCStaging TableNot applicableFIN-FSCM-CLMNot applicable
BFB
J78
O78
OFB
FI - Cash memo recordTransactional dataSAP_FIN_BC_CM_OPS_BASIC_PCStaging TableNot applicableFIN-FSCM-CLMCO - Profit center
BFB
OFB
FI - G/L account balance and open/line itemTransactional dataSAP_FIN_BC_GL_JE_PROC_PCStaging TableYesFI
SD - Sales order (only open SO)
CO - Cost center
Product
PSM - Functional area
PSM - Fund
PSM - Grant
CO - Profit center
JVA - Joint venture master data

J58
O58
FI - Historical balanceTransactional dataSAP_FIN_BC_GL_JE_PROC_PCStaging TableNot applicableFI
CO - Cost center
CO - Profit center
JVA - Joint venture master data

J58
J59
J60
O58
O59
O60
FI - SEPA mandateMaster dataSAP_FIN_BC_AR_SEPA_EDIT_PCStaging TableNot applicableFI
Customer
Bank
FI-CA - Contract partner

19M
2DP
O9M
ODP
FI-CA - Cash security depositTransactional dataSAP_FICA_BC_SCRTY_DEP_PROC_PCStaging TableNot applicableFI-CA
FI-CA - Contract partner
FI-CA - Contract account

2UJ
PUJ
FI-CA - Contract accountMaster dataSAP_FICA_BC_MD_CA_MAINT_PCStaging TableYesFI-CAFI-CA - Contract partner
2AR
OAR
FI-CA - Contract partnerMaster dataSAP_CMD_BC_BP_MAINT_PCStaging TableYesFI-CABank
2AR
OAR
FI-CA - Direct debit mandateMaster dataSAP_FIN_BC_AR_DDM_PROC_PCStaging TableNot applicableFI
Bank
FI-CA - Contract partner

2DP
FI-CA - Dunning historyTransactional dataSAP_FICA_BC_DUN_MASS_PCStaging TableNot applicableFI-CA
FI-CA - Contract partner
FI-CA - Contract account
FI-CA - Open item

2UJ
PUJ
FI-CA - Installment planTransactional dataSAP_FICA_BC_INSTPL_PROC_PCStaging TableNot applicableFI-CA
FI-CA - Contract partner
FI-CA - Contract account
FI-CA - Open item

2UJ
PUJ
FI-CA - Interest historyTransactional dataSAP_FICA_BC_INTRST_PROC_PCStaging TableNot applicableFI-CA
FI-CA - Contract account
FI-CA - Contract partner
FI-CA - Open item
FI-CA - Cash security deposit
FI-CA - Dunning history
FI-CA - Installment plan

2UJ
PUJ
FI-CA - Open itemTransactional dataSAP_FICA_BC_DOC_POST_PCStaging TableYesFI-CA
FI-CA - Contract account
FI-CA - Contract partner
CO - Cost center
CO - Profit center

2AR
OAR
FI-CA - PaymentTransactional dataSAP_FICA_BC_DOC_POST_PCStaging TableYesFI-CA
SD - Sales order (only open SO)
FI-CA - Open item
Exchange rate
CO - Cost center
CO - Profit center

2AR
OAR
FI-CA - Promise to payTransactional dataSAP_FICA_BC_PRMS2P_MAINT_PCStaging TableNot applicableFI-CA
FI-CA - Contract partner
FI-CA - Contract account
FI-CA - Open item
FI-CA - Payment

2UJ
PUJ
FI-CA - Provider contractMaster dataSAP_FICA_BC_MD_PC_MAINT_PCStaging TableYesFI-CA
FI-CA - Contract partner
FI-CA - Contract account
Product

2AR
OAR
Field Logistics - Container (restricted)Master dataSAP_OIL_BC_FLOG_CTNMSTR_PCStaging TableYesCA-FL-SG
Product
Supplier
PM - Equipment

6BA
Field logistics - Supplier itemTransactional dataSAP_OIL_BC_FLOG_SUPLRITEM_PCStaging TableYesCA-FL-SRVMM - Purchase order (only open PO)
4AH
Fixed asset - Master dataMaster data
SAP_FIN_BC_AA_MDAT_REG_PC
SAP_FIN_BC_AA_MDAT_LDT_PC
Staging TableYesFI-AA
Product
CO - Cost center
CO - Activity type
Supplier
Fixed asset - Usage object
CO - Profit center

1GB
1GF
1IH
33F
33G
BFH
J62
O62
Fixed asset - PostingsTransactional dataSAP_FIN_BC_AA_DOC_PROC_PCStaging TableNot applicableFI-AAFixed asset - Master data
1GB
1GF
1IH
33F
33G
BFH
J62
O62
Fixed asset - Usage objectMaster dataSAP_FIN_BC_AA_MDAT_USE_OBJ_PCStaging TableNot applicableFI-AANot applicable
1GB
1GF
1IH
33F
33G
BFH
J62
O62
Import customs declaration (Russia)Transactional dataSAP_FIN_BC_AP_IMPORT_RU_PCStaging TableNot applicableFI-LOC-FI-RU
MM - Purchase order (only open PO)
Product

1EZ
1F1
1HO
2XU
BD9
BDD
BDW
BJE
BKJ
BKZ
BMK
BML
J45
O45
OD9
OF1
OMK
OML
PEZ
JIT - CustomerMaster dataSAP_LE_BC_JIT_CUST_MAINT_PCStaging TableNot applicableLE-JIT-S2CCustomer
2EM
JIT - Packing group specificationMaster dataSAP_LE_BC_JIT_CUST_MAINT_PCStaging TableNot applicableLE-JIT-S2CJIT - Supply control
2EM
JIT - Supply controlMaster dataSAP_LE_BC_JIT_CUST_MAINT_PCStaging TableNot applicableLE-JIT-S2C
JIT - Customer
Production version

2EM
JVA - Cost center suspenseMaster dataSAP_FIN_BC_JVA_CMD_PCStaging TableNot applicableCA-JVAJVA - Joint venture master data
3F7
JVA - Joint operating agreementMaster dataSAP_FIN_BC_JVA_CMD_PCStaging TableNot applicableCA-JVAJVA - Joint venture partner
3F7
JVA - Joint venture master dataMaster dataSAP_FIN_BC_JVA_CMD_PCStaging TableNot applicableCA-JVA
JVA - Joint operating agreement
JVA - Joint venture partner

3F7
JVA - Joint venture master data (new)Master dataSAP_FIN_BC_JVA_CMD_PCStaging TableNot applicableCA-JVA
JVA - Joint operating agreement
JVA - Joint venture partner

3F7
JVA - Joint venture partnerMaster dataSAP_FIN_BC_JVA_CMD_PCStaging TableNot applicableCA-JVANot applicable
3F7
JVA - Project suspenseMaster dataSAP_FIN_BC_JVA_CMD_PCStaging TableNot applicableCA-JVAJVA - Joint venture master data
3F7
JVA - Venture suspenseMaster dataSAP_FIN_BC_JVA_CMD_PCStaging TableNot applicableCA-JVAJVA - Joint venture master data
3F7
Legal documentTransactional dataSAP_LCM_BC_ADMINISTRATOR_PCStaging TableNot applicableCM-GF
Document info record
Legal transaction

1XV
Legal transactionTransactional dataSAP_LCM_BC_ADMINISTRATOR_PCStaging TableNot applicableCM-GF
MM - Purchase contract
MM - Purchase order (only open PO)
Real estate contract
SD - Sales contract
SD - Sales order (only open SO)
Customer
Supplier

1XV
LO - Handling unitTransactional dataSAP_LO_BC_HU_MANAGE_PCStaging TableNot applicableLO-HU-BF
Product
Material inventory balance
Batch unique at material and client level

4MM
Master recipeMaster dataSAP_SCM_BC_PPROC_ENG_MCStaging TableNot applicablePP-PI
Material BOM
Product
Production version
Work center/Resource
MM - Purchasing info record with conditions
MM - Purchase scheduling agreement
Supplier

3W3
BJ8
Material - Forecast planningMaster dataSAP_CMD_BC_PR_MAINT_PCStaging TableNot applicableMMProduct
J44
O44
Material BOMMaster dataSAP_PLM_BC_MBOM_PCStaging TableNot applicablePP-BDProduct
4HH
4HI
4VT
4WM
BH1
BJ2
BJ5
BJE
BJH
BJN
OHH
OHI
OVT
OWM
Material inventory balanceTransactional dataSAP_MM_BC_IM_MAT_DOC_API_PCStaging TableNot applicableMM-IM
MM - Purchase order (only open PO)
SD - Sales order (only open SO)
Batch unique at material and client level
Customer
Product
Supplier
MM - Purchasing info record with conditions

BJ5
BJE
BJH
BJK
BLL
BMC
BMK
BML
BMR
BMY
J45
O45
OMC
OMK
OML
Material listing and exclusionMaster dataSAP_SD_BC_MD_MANAGE_MCStaging TableNot applicableSD
Customer
Product

BD3
MM - Model product specificationMaster dataSAP_MM_BC_MPS_PROCESS_PCStaging TableNot applicableMM-PUR-MPSProduct
22Z
MM - Purchase contractTransactional dataNot applicableStaging TableNot applicableMM-PUR
Supplier
Product
Fixed asset - Master data
CO - Cost center
Commercial project
MM - Purchasing info record with conditions
SD - Sales order (only open SO)
CO - Profit center

BMD
BMY
J13
OMD
MM - Purchase order (only open PO)Transactional data
SAP_PS_BC_PROJ_FIN_ANLYTC_MC
SAP_MM_BC_PURCH_DOC_DSP_PC
SAP_MM_BC_PO_MANAGE_PC
Staging TableYesMM-PUR
Product
Supplier
Material BOM
MM - Purchase contract
Fixed asset - Master data
CO - Cost center
Commercial project
MM - Purchasing info record with conditions
SD - Sales order (only open SO)
CO - Profit center

BJK
BMD
BMK
BMR
BMY
BNX
J13
J14
J45
J82
O45
OMD
OMK
ONX
MM - Purchase scheduling agreementTransactional dataSAP_MM_BC_SA_PROCESS_MCDirect TransferNot applicableMM-PURNot applicableNot applicableMM - Purchase scheduling agreementTransactional dataSAP_MM_BC_SA_PROCESS_MCStaging TableNot applicableMM-PUR
Supplier
Product
Fixed asset - Master data
CO - Cost center
CO - Profit center
Commercial project
MM - Purchasing info record with conditions
SD - Sales order (only open SO)

BMR
MM - Purchasing info recordMaster dataSAP_MM_BC_IR_PROCESS_PCDirect TransferNot applicableMM-PURNot applicableNot applicableMM - Purchasing info record with conditionsMaster dataSAP_MM_BC_IR_PROCESS_PCStaging TableNot applicableMM-PUR
Supplier
Product

41F
BMR
J13
J14
J45
O45
MM - Purchasing info record- extend existing recordMaster dataSAP_MM_BC_IR_PROCESS_PCStaging TableNot applicableMM-PUR
Product
MM - Purchasing info record with conditions
Supplier

41F
BMR
J13
J14
J45
O45
MM - Source listTransactional dataNot applicableStaging TableNot applicableMM-PUR
MM - Purchase scheduling agreement
Supplier
Product
MM - Purchase contract

BMD
BMR
OMD
Object classification (general template)Master dataSAP_PLM_BC_CLF_MCDirect TransferNot applicableCA-CLNot applicableNot applicableObject classification - Code group (QPGR)Master dataSAP_QM_BC_BASIC_DATA_PCStaging TableNot applicableCA-CL
Characteristic
Class
QM/PM - Catalog code group/code

1E1
1FM
1MP
1MR
PMR (not available)
Object classification - Document (DRAW)Master dataSAP_PLM_BC_DIR_EXTD_MCStaging TableNot applicableCA-CL
Document info record
Characteristic
Class

22P
Object classification - General templateMaster data
SAP_QM_BC_MSTR_INSP_CHARC_PC
SAP_QM_BC_INSP_METHODS_PC
SAP_RFM_BC_SITE_MNGGRP_PC
SAP_QM_BC_BASIC_DATA_PC
SAP_SCM_BC_BATCH_MGMT_MC
SAP_CMD_BC_PR_MAINT_PC
SAP_CMD_BC_CUST_MAINT_PC
SAP_SCM_BC_PROC_ENG_MC
SAP_CMD_BC_SUPP_MAINT_PC
SAP_EAM_BC_MPLAN_MC
SAP_SD_BC_MD_MANAGE_MC
SAP_MM_BC_IR_PROCESS_PC
SAP_PLM_BC_CLF_MC
SAP_PLM_BC_CM_MC
SAP_PLM_BC_DIR_EXTD_MC
SAP_EAM_BC_TO_MC
Staging TableNot applicableCA-CL
QM - Inspection method
PM - Maintenance item
Product
PM - Maintenance plan
QM - Master inspection characteristic
MM - Purchasing info record with conditions
QM/PM - Catalog code group/code
QM - Selected set
Characteristic
RFM - Site master
Class
RFM - Supplying site
Customer
Document info record
Supplier
Work center/Resource
PM - Equipment
PM - Functional location

4HH
4HI
4VT
4WM
5FJ
BH1
BH2
BJ2
BJ5
BJE
BJH
BLF
J13
J44
J45
O44
O45
OHH
OHI
OVT
OWM
Object classification - Inspection characteristic (QPMK)Master dataSAP_QM_BC_MSTR_INSP_CHARC_PCStaging TableNot applicableCA-CL
Characteristic
Class
QM - Master inspection characteristic

1E1
1FM
1MP
Object classification - Inspection method (QMTB)Master dataSAP_QM_BC_INSP_METHODS_PCStaging TableNot applicableCA-CL
Characteristic
Class
QM - Inspection method

1E1
1FM
1MP
1MR
PMR (not available)
Object classification - Material (MARA)Master dataSAP_CMD_BC_PR_MAINT_PCStaging TableNot applicableCA-CL
Characteristic
Product
Class

BD9
BJ5
BJH
J11
J44
J45
O44
O45
OD9
Object classification - Purchasing info record (EINA)Master data
SAP_PLM_BC_CLF_MC
SAP_MM_BC_IR_PROCESS_PC
Staging TableNot applicableCA-CL
Characteristic
Class
MM - Purchasing info record with conditions

41F
BMR
J13
J14
J45
O45
Object classification - Selected set (QPAM)Master dataSAP_QM_BC_BASIC_DATA_PCStaging TableNot applicableCA-CL
Characteristic
Class
QM - Selected set

1E1
1FM
1MP
1MR
PMR (not available)
Order BOMMaster data
SAP_PLM_BC_SALES_ORDER_BOM_PC
SAP_PLM_BC_MBOM_PC
Staging TableNot applicablePP-BD
Product
SD - Sales order (only open SO)
Supplier
Document info record
ECM - Change master

BJE
PC - Analytical compositionMaster data
SAP_PSS_BC_PC_FND_PMON_PC
SAP_PSS_BC_PC_FND_RMON_PC
Staging TableNot applicableEHS-SUS-FND
PC - Polymer composition
PC - Substance
PC - Raw material compliance info
PC - Product compliance info

31G
31H
31J
3FC
3G8
3VQ
3VR
4OL
4XD
5OJ
6AV
6MT
PC - Confidential business informationMaster dataSAP_PSS_BC_PC_FND_PMON_PCStaging TableNot applicableEHS-SUS-FND
PC - Polymer composition
PC - Product compliance info
PC - Substance
PC - Analytical composition

31G
31H
31J
3FC
3G8
3VQ
3VR
4OL
5OJ
6AV
6MT
PC - Material-based compositionMaster dataSAP_PSS_BC_PC_FND_PMON_PCStaging TableNot applicableEHS-SUS-FND
PC - Polymer composition
PC - Raw material compliance info
PC - Substance
PC - Product compliance info

31G
31H
31J
3FC
3G8
3VQ
3VR
4OL
5OJ
6AV
6MT
PC - Physical-chemical propertyMaster data
SAP_PSS_BC_PC_FND_PMON_PC
SAP_PSS_BC_PC_FND_RMON_PC
SAP_PSS_BC_PC_SUBST_MAN_PC
Staging TableNot applicableEHS-SUS-FND
PC - Substance
PC - Raw material compliance info
PC - Product compliance info

31G
31H
31J
3FC
3G8
4XD
PC - Polymer compositionMaster dataSAP_PSS_BC_PC_SUBST_MAN_PCStaging TableNot applicableEHS-SUS-FNDPC - Substance
31G
31H
31J
3FC
3G8
3VQ
3VR
4OL
5OJ
6AV
6MT
PC - Product compliance infoMaster dataSAP_PSS_BC_PC_FND_PMON_PCStaging TableNot applicableEHS-SUS-FND
PC - Raw material compliance info
Product
PC - Substance

31H
31J
3FC
3G8
4XD
PC - Raw material compliance infoMaster dataSAP_PSS_BC_PC_FND_RMON_PCStaging TableNot applicableEHS-SUS-FND
Product
PC - Substance
Supplier

31G
PC - Safety-related propertyMaster data
SAP_PSS_BC_PC_FND_RMON_PC
SAP_PSS_BC_PC_FND_PMON_PC
SAP_PSS_BC_PC_SUBST_MAN_PC
Staging TableNot applicableEHS-SUS-FND
PC - Substance
PC - Raw material compliance info
PC - Product compliance info

31G
31H
31J
3FC
3G8
PC - SubstanceMaster dataSAP_PSS_BC_PC_SUBST_MAN_PCStaging TableNot applicableEHS-SUS-FNDNot applicable
31G
31H
4XD
PM - EquipmentMaster data
SAP_EAM_BC_TO_MNG_PC
SAP_EAM_BC_TO_MC
Staging TableYesPM
Batch unique at material and client level
Fixed asset - Master data
PM - Functional location
CO - Cost center
Work center/Resource

4HH
4HI
4VT
4WM
BH1
BH2
BJ2
OHH
OHI
OVT
OWM
PM - Equipment BOMMaster data
SAP_EAM_BC_TO_MNG_PC
SAP_EAM_BC_TO_MC
Staging TableNot applicablePM
Product
PM - Equipment

4HH
4HI
4VT
4WM
BJ2
OHH
OHI
OVT
OWM
PM - Equipment task listMaster data
SAP_EAM_BC_TSKL_MNG_PC
SAP_EAM_BC_TL_MC
Staging TableNot applicablePM
Product
PM - Equipment
PM - Functional location
MM - Purchase contract
MM - Purchasing info record with conditions

4HH
4HI
4VT
4WM
BJ2
OHH
OHI
OVT
OWM
PM - Functional locationMaster dataSAP_EAM_BC_TO_MCDirect TransferNot applicablePMNot applicableNot applicablePM - Functional locationMaster data
SAP_EAM_BC_TO_MC
SAP_EAM_BC_TO_MNG_PC
Staging TableYesPM
Fixed asset - Master data
CO - Cost center
Work center/Resource

4HH
4HI
4VT
4WM
BH1
BH2
BJ2
OHH
OHI
OVT
OWM
PM - Functional location BOMMaster data
SAP_EAM_BC_TO_MC
SAP_EAM_BC_TO_MNG_PC
Staging TableNot applicablePM
Product
PM - Functional location

4HH
4HI
4VT
4WM
BJ2
OHH
OHI
OVT
OWM
PM - Functional location task listMaster data
SAP_EAM_BC_TL_MC
SAP_EAM_BC_TSKL_MNG_PC
Staging TableNot applicablePM
Product
PM - Functional location
MM - Purchase contract
MM - Purchasing info record with conditions
PM - Equipment

4HH
4HI
4VT
4WM
BJ2
OHH
OHI
OVT
OWM
PM - General maintenance task listMaster dataSAP_BR_MAINTENANCE_PLANNERDirect TransferNot applicablePMNot applicableNot applicablePM - General maintenance task listMaster data
SAP_EAM_BC_TL_MC
SAP_EAM_BC_TSKL_MNG_PC
Staging TableNot applicablePM
PM - Equipment
PM - Functional location
Product
MM - Purchase contract
MM - Purchasing info record with conditions

4HH
4HI
4VT
4WM
BJ2
OHH
OHI
OVT
OWM
PM - Maintenance itemMaster data
SAP_EAM_BC_MPLANIT_MC
SAP_EAM_BC_MNTITEM_MNG_PC
Staging TableYesPM
PM - Equipment task list
PM - Functional location task list
PM - General maintenance task list
Work center/Resource
PM - Equipment
PM - Functional location

4HI
BJ2
OHI
PM - Maintenance notificationMaster dataSAP_EAM_BC_NTF_MCStaging TableYesPM
PM - Equipment
PM - Functional location
Product
QM/PM - Catalog code group/code
Work center/Resource

4HH
4HI
4VT
4WM
BJ2
OHH
OHI
OVT
OWM
PM - Maintenance orderTransactional data
SAP_EAM_BC_ORD_MNG_PC
SAP_EAM_BC_MP_ORD_DSP_PC
Staging TableYesPM
CO - Profit center
Customer
PM - Maintenance notification
PM - Equipment
PM - Functional location
Product
Document info record
Fixed asset - Master data
CO - Cost center
Supplier
Work center/Resource
CO - Internal order (restricted)
PP - Process order (only open PO)
PP - Production order (only open PO)

3YE
43R
4HH
4HI
4VT
4WM
BH1
BH2
BJ2
O3R
OHH
OHI
OVT
OWM
PM - Maintenance planMaster data
SAP_EAM_BC_MP_MNG_PC
SAP_EAM_BC_MPLAN_MC
Staging TableYesPM
PM - Measurement document
PM - Measuring point
PM - Equipment task list
PM - Functional location task list
PM - General maintenance task list
Work center/Resource
PM - Equipment
PM - Functional location
PM - Maintenance item

4HI
BJ2
OHI
PM - Measurement documentMaster dataSAP_EAM_BC_MEADOC_MCStaging TableNot applicablePMPM - Measuring point
4HH
4HI
4VT
4WM
BJ2
OHH
OHI
OVT
OWM
PM - Measuring pointMaster data
SAP_EAM_BC_MESP_MNG_PC
SAP_EAM_BC_MEAPT_MC
Staging TableNot applicablePM
Characteristic
QM/PM - Catalog code group/code
PM - Equipment
PM - Functional location

4HH
4HI
4VT
4WM
BJ2
OHH
OHI
OVT
OWM
PMA - Customer compliance assessmentMaster data
SAP_PSS_BC_PC_FND_PMON_PC
SAP_PSS_BC_PC_CCM_CRR_MON_PC
SAP_PSS_BC_PC_PROD_CRR_MON_PC
SAP_PSS_BC_PC_CCM_MAN_PC
SAP_PSS_BC_PC_PMA_ANALYZE_PC
Staging TableNot applicableEHS-SUS-PMA
PC - Product compliance info
Customer
Product

31H
31J
PMA - Simple compliance assessmentMaster data
SAP_PSS_BC_PC_FND_PMON_PC
SAP_PSS_BC_PC_PMA_ANALYZE_PC
SAP_PSS_BC_PC_PROD_CRR_MON_PC
SAP_PSS_BC_PC_FND_SMMON_PC
SAP_PSS_BC_PC_FND_RMON_PC
SAP_PSS_BC_PC_RM_CRR_MON_PC
Staging TableNot applicableEHS-SUS-PMA
PC - Raw material compliance info
PC - Product compliance info

31G
31H
31J
PMA - Supplier information assessmentMaster data
SAP_PSS_BC_PC_PMA_ANALYZE_PC
SAP_PSS_BC_PC_PROD_CRR_MON_PC
SAP_PSS_BC_PC_RM_CRR_MON_PC
SAP_PSS_BC_PC_FND_PMON_PC
SAP_PSS_BC_PC_FND_SMMON_PC
SAP_PSS_BC_PC_FND_RMON_PC
Staging TableNot applicableEHS-SUS-PMA
PC - Raw material compliance info
PC - Product compliance info

31G
31H
31J
PP - Material BOMMaster dataSAP_PLM_BC_MBOMDirect TransferNot applicablePP-BDNot applicableNot applicablePP - Process order (only open PO)Transactional dataSAP_SCM_BC_PRODN_ORD_MPLN_MCStaging TableNot applicablePP-PI-POR
Material BOM
Enterprise project
Master recipe
Product
Production version
SD - Sales order (only open SO)
CO - Profit center

3L7
3OK
3UK
3UL
PP - Production order (only open PO)Transactional dataSAP_SCM_BC_PRODN_ORD_CTRL_MCStaging TableNot applicablePP-SFC
Product
SD - Sales order (only open SO)
Material BOM
Enterprise project
Production version
Routing
CO - Profit center

BJ5
BJE
PP - Production versionMaster dataSAP_TC_SCM_PP_COMMON Direct TransferNot applicablePPNot applicableNot applicablePP - RoutingMaster dataSAP_TC_SCM_PP_BE_APPSDirect TransferNot applicablePP-BD-RTGNot applicableNot applicablePP-KAB - Kanban control cycleMaster dataSAP_SCM_BC_KNBN_CTRLC_MGMT_MCStaging TableNot applicablePP-KAB
CO - Cost center
PP-KAB - Production supply area
Product
MM - Purchase scheduling agreement
SD - Sales scheduling agreement
Supplier
Work center/Resource
Warehouse storage bin
Production version

1E3
4B3
4B4
PP-KAB - Production supply areaMaster dataSAP_SCM_BC_KNBN_CTRLC_MGMT_MCStaging TableYesPP-KABNot applicable
1E3
4B3
4B4
PRA - Account entry controlMaster dataNot applicableStaging TableNot applicableIS-OIL-PRA
PRA - Division order interest
PRA - Joint venture
PRA - extend existing supplier by PRA data
PRA - Contract

5NM
PRA - Allocation cross referenceMaster dataNot applicableStaging TableNot applicableIS-OIL-PRA
PRA - Well and well completion
PRA - Contract

5NM
PRA - Check input process ruleMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - extend existing customer by PRA data
5NM
PRA - Check input translationMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - extend existing customer by PRA data
5NM
PRA - Chemical analysis dataMaster dataNot applicableStaging TableNot applicableIS-OIL-PRA
PRA - extend existing supplier by PRA data
PRA - Well and well completion

5NM
PRA - ContractMaster dataNot applicableStaging TableNot applicableIS-OIL-PRA
PRA - extend existing customer by PRA data
PRA - Division order interest
PRA - Joint venture
PRA - extend existing supplier by PRA data

5NM
PRA - Contract volumeTransactional dataNot applicableStaging TableNot applicableIS-OIL-PRA
PRA - extend existing customer by PRA data
PRA - Delivery network
PRA - Measurement point
PRA - Well and well completion
PRA - Contract
Product
PRA - Marketing group assignment
PRA - MP-WC to contract cross reference
PRA - DOI MP WC cross reference

5NM
PRA - Delivery networkMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Joint venture
5NM
PRA - Delivery network allocation profileMaster dataNot applicableStaging TableNot applicableIS-OIL-PRA
PRA - Delivery network
Product
PRA - Delivery network downstream node

5NM
PRA - Delivery network downstream nodeMaster dataNot applicableStaging TableNot applicableIS-OIL-PRA
PRA - Delivery network
PRA - Measurement point
PRA - Well and well completion

5NM
PRA - Division order interestMaster dataNot applicableStaging TableNot applicableIS-OIL-PRA
PRA - extend existing customer by PRA data
PRA - Joint venture
PRA - extend existing supplier by PRA data
PRA - Ownership lease

5NM
PRA - DOI accountingMaster dataNot applicableStaging TableNot applicableIS-OIL-PRA
CO - Cost center
PRA - extend existing customer by PRA data
PRA - Division order interest
PRA - Joint venture

5NM
PRA - DOI MP WC cross referenceMaster dataNot applicableStaging TableNot applicableIS-OIL-PRA
PRA - Division order interest
PRA - Joint venture
PRA - Well and well completion
Product

5NM
PRA - extend existing customer by PRA dataMaster dataNot applicableStaging TableNot applicableIS-OIL-PRACustomer
5NM
PRA - extend existing supplier by PRA dataMaster dataNot applicableStaging TableNot applicableIS-OIL-PRASupplier
5NM
PRA - Joint ventureMaster dataNot applicableStaging TableNot applicableIS-OIL-PRA
JVA - Joint venture master data
PRA - extend existing customer by PRA data

5NM
PRA - Journal entry open balanceTransactional dataNot applicableStaging TableNot applicableIS-OIL-PRA
CO - Cost center
PRA - extend existing customer by PRA data
PRA - Division order interest
PRA - Joint venture
PRA - extend existing supplier by PRA data
PRA - Well and well completion
PRA - Contract

5NM
PRA - Marketing group assignmentMaster dataNot applicableStaging TableNot applicableIS-OIL-PRA
PRA - extend existing customer by PRA data
PRA - Division order interest
PRA - Joint venture
PRA - extend existing supplier by PRA data
Product

5NM
PRA - Measurement pointMaster dataNot applicableStaging TableNot applicableIS-OIL-PRACO - Cost center
5NM
PRA - Measurement point allocation profileMaster dataNot applicableStaging TableNot applicableIS-OIL-PRA
PRA - Delivery network
PRA - Measurement point
Product
PRA - Delivery network allocation profile
PRA - Delivery network downstream node

5NM
PRA - Measurement point volumeTransactional dataNot applicableStaging TableNot applicableIS-OIL-PRA
PRA - extend existing customer by PRA data
PRA - Delivery network
PRA - Measurement point
PRA - MP/WC transporter cross reference
Product
PRA - Contract
PRA - Delivery network downstream node
PRA - Measurement point allocation profile

5NM
PRA - MP-WC to contract cross referenceMaster dataNot applicableStaging TableNot applicableIS-OIL-PRA
PRA - extend existing customer by PRA data
PRA - Well and well completion
PRA - MP/WC transporter cross reference
PRA - Contract

5NM
PRA - MP/WC transporter cross referenceMaster dataNot applicableStaging TableNot applicableIS-OIL-PRA
PRA - extend existing customer by PRA data
PRA - Well and well completion

5NM
PRA - Ownership leaseMaster dataNot applicableStaging TableNot applicableIS-OIL-PRANot applicable
5NM
PRA - Remitter to DOI cross referenceMaster dataNot applicableStaging TableNot applicableIS-OIL-PRA
CO - Cost center
PRA - Check input process rule
PRA - extend existing customer by PRA data
PRA - Division order interest

5NM
PRA - Settlement diversityMaster dataNot applicableStaging TableNot applicableIS-OIL-PRA
PRA - Division order interest
PRA - Joint venture
PRA - extend existing supplier by PRA data
PRA - Well and well completion
PRA - Contract
PRA - Valuation formula

5NM
PRA - Settlement statement DOI cross referenceMaster dataNot applicableStaging TableNot applicableIS-OIL-PRA
PRA - extend existing customer by PRA data
PRA - Division order interest
PRA - Joint venture
PRA - Contract
Product

5NM
PRA - State tax rateMaster dataNot applicableStaging TableNot applicableIS-OIL-PRANot applicable
5NM
PRA - Tax calculation dataMaster dataNot applicableStaging TableNot applicableIS-OIL-PRANot applicable
5NM
PRA - Unit venture tractMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Division order interest
5NM
PRA - Valuation cross referenceMaster dataNot applicableStaging TableNot applicableIS-OIL-PRA
PRA - Division order interest
PRA - Joint venture
PRA - Unit venture tract
PRA - Well and well completion
PRA - Contract
PRA - Valuation formula

5NM
PRA - Valuation formulaMaster dataNot applicableStaging TableNot applicableIS-OIL-PRA
PRA - extend existing customer by PRA data
PRA - Division order interest
PRA - Joint venture
PRA - Unit venture tract
PRA - Well and well completion
PRA - Contract
Product

5NM
PRA - WC/DN theoretical calculation methodMaster dataNot applicableStaging TableNot applicableIS-OIL-PRA
PRA - Delivery network
PRA - Well and well completion
PRA - Delivery network downstream node

5NM
PRA - Well and well completionMaster dataNot applicableStaging TableNot applicableIS-OIL-PRA
CO - Cost center
PRA - extend existing customer by PRA data

5NM
PRA - Well completion tax classificationMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Well and well completion
5NM
PRA - Well completion testMaster dataNot applicableStaging TableNot applicableIS-OIL-PRA
PRA - Well and well completion
PRA - Delivery network downstream node

5NM
PRA - Well completion volumeTransactional dataNot applicableStaging TableNot applicableIS-OIL-PRA
PRA - extend existing customer by PRA data
PRA - Well and well completion
PRA - MP/WC transporter cross reference
Product

5NM
Pricing condition (sales)Master dataSAP_SD_BC_PRICE_MANAGE_MC Direct TransferNot applicableSDNot applicableNot applicableProductMaster dataSAP_CMD_BC_PR_MAINTDirect TransferNot applicableLO-MDNot applicableNot applicableProductMaster dataSAP_CMD_BC_PR_MAINT_PCStaging TableYesLO
RFM - Merchandise category
Supplier
PP-KAB - Production supply area
CO - Profit center

1RM
3BR
3I1
BD9
BJ5
BJH
J11
J45
O45
OD9
ODQ
OKL
Product - extend existing record by new org levelsMaster dataSAP_CMD_BC_PR_MAINT_PCStaging TableYesLO
Product
PP-KAB - Production supply area
CO - Profit center

1RM
3BR
3I1
BD9
BJ5
BJH
J11
J45
O45
OD9
ODQ
OKL
Product - extend existing record with long textMaster dataSAP_CMD_BC_PR_MAINT_PCStaging TableNot applicableLOProduct
1RM
BD9
BJ5
BJH
J11
J45
O45
OD9
ODQ
OKL
Product classification - Commodity codeMaster dataSAP_SLL_BC_CLS_CMMDTYCODE_PCStaging TableNot applicableFT-ITR-CLSProduct
2U3
BDT
Product classification - Legal controlMaster dataSAP_SLL_BC_CLS_LEGCTRL_PCStaging TableNot applicableFT-ITR-CLSProduct
1W8
Product consumptionMaster dataSAP_CMD_BC_PR_MAINT_PCStaging TableNot applicableMMProduct
J44
O44
Product hierarchyMaster dataSAP_CMD_BC_PR_MAINT_PCStaging TableNot applicableLONot applicable
BD9
OD9
Product hierarchy assignmentMaster dataSAP_CMD_BC_PR_MAINT_PCStaging TableNot applicableLO
Product
Product hierarchy

BD9
OD9
Production versionMaster data
SAP_SCM_BC_PPROC_ENG_MC
SAP_SCM_BC_PROC_ENG_MC
Staging TableNot applicablePP
Material BOM
Routing
Product
PM - Equipment task list
PM - Functional location task list
PM - General maintenance task list
Master recipe

1BM
1E3
1Y2
1YT
21D
21T
2QI
2UG
31L
BJ5
BJE
BJH
BJK
BJN
BJQ
Production volume captureTransactional dataSAP_OIL_BC_PVOL_CAPTURE_PCStaging TableNot applicableCA-PVLNot applicable
4AG
PSM - Earmarked fundsTransactional dataSAP_PSM_BC_EMRKFNDS_API_PCStaging TableNot applicablePSM-FM
CO - Cost center
PSM - Functional area
PSM - Fund
PSM - Grant

4GY
78S
PSM - Functional areaMaster dataSAP_PSM_BC_MD_BDGTSP_PCStaging TableNot applicablePSM-FM-MDNot applicable
3QE
6YU
PSM - FundMaster dataSAP_PSM_BC_MD_BDGTSP_PCStaging TableYesPSM-FMNot applicable
3QE
6YU
PSM - GrantMaster dataSAP_PSM_BC_MD_GRNTSP_PCStaging TableYesPSM-GM-GTE-MD
PSM - Sponsored class
PSM - Sponsored program
PSM - Fund

4GX
71S
PSM - SponsorMaster dataSAP_PSM_BC_MD_GRNTSP_PCStaging TableNot applicablePSM-GM-GTE-MD
Customer
PSM - Sponsored class

4GX
71S
PSM - Sponsored classMaster dataSAP_PSM_BC_MD_GRNTSP_PCStaging TableNot applicablePSM-GM-GTE-MDNot applicable
4GX
71S
PSM - Sponsored programMaster dataSAP_PSM_BC_MD_GRNTSP_PCStaging TableNot applicablePSM-GM-GTE-MDNot applicable
4GX
71S
QM - Certificate profile incl. assignmentMaster dataSAP_QM_BC_CERTIFICATE_PLNG_PCStaging TableNot applicableQM
Characteristic
Product
QM - Master inspection characteristic

1MP
QM - Inspection methodMaster dataSAP_QM_BC_INSP_METHODS_PCStaging TableNot applicableQM
QM/PM - Catalog code group/code
QM - Selected set
QM - Selected set code

1E1
1FM
1MP
1MR
PMR (not available)
QM - Inspection planMaster dataSAP_QM_BC_INSP_PLANNING_PCStaging TableNot applicableQM
QM - Inspection method
QM - Master inspection characteristic
QM/PM - Catalog code group/code
QM - Selected set
QM - Selected set code

1E1
1FM
1MP
QM - Master inspection characteristicMaster dataSAP_QM_BC_MSTR_INSP_CHARC_PCDirect TransferNot applicableQMNot applicableNot applicableQM - Master inspection characteristicMaster dataSAP_QM_BC_MSTR_INSP_CHARC_PCStaging TableNot applicableQM
QM - Inspection method
QM/PM - Catalog code group/code
QM - Selected set
QM - Selected set code

1E1
1FM
1MP
QM - Quality info recordMaster dataSAP_QM_BC_QM_PROCUREMENT_PCStaging TableNot applicableQM
Product
Supplier

1FM
QM - Selected setMaster dataSAP_QM_BC_BASIC_DATA_PCStaging TableNot applicableQMQM/PM - Catalog code group/code
1E1
1FM
1MP
1MR
PMR (not available)
QM - Selected set codeMaster data
SAP_QM_BC_INSP_PLANNING_PC
SAP_QM_BC_BASIC_DATA_PC
Staging TableNot applicableQM
QM/PM - Catalog code group/code
QM - Selected set

1E1
1FM
1MP
1MR
PMR (not available)
QM/PM - Catalog code group/codeMaster dataSAP_QM_BC_BASIC_DATA_PCStaging TableNot applicableQMNot applicable
1E1
1FM
1MP
1MR
PMR (not available)
Real estate - Occupancy groupMaster dataSAP_FIN_BC_REIP_LOCATION_PCStaging TableYesRE-FX
CO - Cost center
Real estate - Usage enablement group

5VY
5VZ
5W0
OVY
OVZ
OW0
Real estate - Usage enablement groupMaster dataSAP_FIN_BC_REIP_LOCATION_PCStaging TableYesRE-FX
CO - Cost center
Fixed asset - Master data

5VX
OVX
Real estate contractTransactional dataSAP_FIN_BC_RECM_CONTRACT_PCStaging TableYesRE-FX
Customer
CO - Cost center
PSM - Functional area
PSM - Fund
PSM - Grant
Supplier
Real estate - Usage enablement group
Real estate - Occupancy group
CO - Profit center

1T6
21Q
21R
2SB
3F4
5VX
5VY
5W0
O1Q
O1R
OF4
OSB
OT6
OVX
OVY
OW0
Revenue accounting contractTransactional dataSAP_FIN_BC_FARR_RAIPRC_PCStaging TableNot applicableFI-RA
Condition record for pricing (general template)
Customer
CO - Cost center
Product
PSM - Functional area
Service order (only open SRVO)
CO - Profit center

3KK
3VS
RFM - Assortment moduleMaster dataSAP_RFM_BC_ASSTMT_MAINT_PCStaging TableNot applicableLO-RFM-MD-LST
Product
RFM - Site master
Customer

3I5
RFM - MCHN Characteristic value restrictionMaster dataSAP_RFM_BC_MRCHDSCAT_MNTN_PCStaging TableNot applicableLO-RFM-MD-MC
Characteristic
RFM - Merchandise category hierarchy node

5FJ
RFM - Merchandise categoryMaster dataSAP_RFM_BC_MRCHDSCAT_MNTN_PCStaging TableNot applicableLO-RFM-MD-MC
Characteristic
RFM - Merchandise category hierarchy node

5FJ
RFM - Merchandise category characteristic value restrictionMaster dataSAP_RFM_BC_MRCHDSCAT_MNTN_PCStaging TableNot applicableLO-RFM-MD-MC
Characteristic
RFM - Merchandise category
RFM - Merchandise category hierarchy node

5FJ
RFM - Merchandise category hierarchy nodeMaster dataSAP_RFM_BC_MRCHDSCAT_MNTN_PCStaging TableNot applicableLO-RFM-MD-MCCharacteristic
5FJ
RFM - Merchandise category hierarchy node assignmentMaster dataSAP_RFM_BC_MRCHDSCAT_MNTN_PCStaging TableNot applicableLO-RFM-MD-MCRFM - Merchandise category hierarchy node
5FJ
RFM - Merchandise category reference articleMaster dataSAP_RFM_BC_MRCHDSCAT_MNTN_PCStaging TableNot applicableLO-RFM-MD-MC
Product
RFM - Merchandise category

5FJ
RFM - Product assignment to distribution centerMaster dataSAP_RFM_BC_ASSTMT_MAINT_PCStaging TableNot applicableLO-RFM-MD-LST
Product
RFM - Site master
Customer

3I5
RFM - Site masterMaster dataSAP_RFM_BC_SITE_MAINT_PCStaging TableNot applicableLO-RFM-MD-SIT
Supplier
Customer
RFM - Merchandise category
CO - Profit center

3I3
RFM - Supplying siteMaster dataSAP_RFM_BC_SITE_MAINT_PCStaging TableNot applicableLO-RFM-MD-SIT
RFM - Merchandise category
RFM - Site master

3I3
RNPT - Traceability of goods (Russia)Transactional dataSAP_FIN_BC_AP_IMPORT_RU_PCStaging TableNot applicableFI-LOC-FI-RUImport customs declaration (Russia)
BD9
BMC
J45
O45
OD9
OMC
RoutingMaster dataSAP_SCM_BC_PROC_ENG_MCStaging TableNot applicablePP
Material BOM
Work center/Resource
Product
AVC - Object dependencies
MM - Purchasing info record with conditions
MM - Purchase scheduling agreement
Supplier
PM - Equipment

BJ5
BJE
BJH
BJK
BJN
BJQ
SD - Condition record for free goodsMaster dataSAP_SD_BC_MD_MANAGE_MCStaging TableNot applicableSD
Customer
Product

BD3
BKA
SD - Condition Record for Material determinationMaster dataSAP_SD_BC_MD_MANAGE_MCStaging TableNot applicableSDProduct
BD9
OD9
SD - Customer MaterialMaster dataSAP_SD_BC_MD_MANAGE_MCStaging TableYesSD
Product
Characteristic
Customer
Class

BD9
OD9
SD - Sales contractTransactional dataSAP_SD_BC_CONTR_PROC_MCStaging TableNot applicableSD
Customer
Product
Condition record for pricing (general template)

I9I
SD - Sales order (only open SO)Transactional dataSAP_SD_BC_SO_PROC_MCStaging TableYesSD
Condition record for pricing (general template)
Customer
Product
Condition record for pricing in sales (restricted)
Characteristic
Class
Object classification - General template
AVC - Configuration profile (restricted)

1YT
BD3
BD9
BDA
BDD
BDG
BDH
BDK
BDN
BDQ
BDW
BKA
BKJ
BKK
BKL
BKX
BKZ
J11
OD9
ODG
ODQ
OKL
SD - Sales scheduling agreementTransactional dataSAP_SD_BC_SLS_SCHEDULE_PCStaging TableNot applicableSD-SLS
Condition record for pricing (general template)
Customer
Product

3NR
4LZ
SDS - Assessment for unpackaged productMaster data
SAP_PSS_BC_PC_FND_PMON_PC
SAP_PSS_BC_PC_FND_RMON_PC
Staging TableNot applicableEHS-SUS-SDS
Document info record
PC - Raw material compliance info
PC - Product compliance info

3VR
SDS - Shipment historyTransactional dataSAP_PSS_BC_PC_FND_PMON_PCStaging TableNot applicableEHS-SUS-SDS
Customer
PC - Product compliance info

3VQ
3VR
Service contractTransactional dataSAP_S4CRM_BC_SERV_CONTR_PCStaging TableYesCRM-S4-SRV-CTR
FI - SEPA mandate
Customer
PM - Equipment
PM - Functional location
Product
Condition record for pricing in sales (restricted)
CO - Profit center

3MO
Service order (only open SRVO)Transactional dataSAP_CRM_BC_SERV_PROCORDER_PCStaging TableYesCRM-S4-SRV-SVO
Service contract
Customer
Product
Condition record for pricing in sales (restricted)

3D2
Service productMaster dataSAP_CMD_BC_PR_MAINT_PCStaging TableYesLO
Supplier
CO - Profit center

22Z
42K
O2K
SupplierMaster dataSAP_CMD_BC_SUPP_MAINT_PCDirect TransferNot applicableLO-MD-BP-VMNot applicableNot applicableSupplierMaster dataSAP_CMD_BC_SUPP_MAINT_PCStaging TableYesMMBank
BD3
BDK
BDN
BFB
BJK
J11
J12
J13
J14
J45
J54
J55
J58
J60
J62
J78
O45
O54
O58
O60
O62
O78
OFB
Supplier - extend existing record by multiple addressesMaster dataSAP_CMD_BC_SUPP_MAINT_PCStaging TableNot applicableMMSupplier
BD3
BDK
BDN
BFB
BJK
J11
J12
J13
J14
J45
J54
J55
J58
J60
J62
J78
O45
O54
O58
O60
O62
O78
OFB
Supplier - extend existing record by new org levelsMaster dataSAP_CMD_BC_SUPP_MAINT_PCStaging TableYesMM
Supplier
Customer
FI-CA - Contract partner

BD3
BDK
BDN
BFB
BJK
J11
J12
J13
J14
J45
J54
J55
J58
J60
J62
J78
O45
O54
O58
O60
O62
O78
OFB
Supplier - extend existing record by Thailand branch codeMaster dataSAP_CMD_BC_SUPP_MAINT_PCStaging TableNot applicableMMSupplier
BD3
BDK
BDN
BFB
BJK
J11
J12
J13
J14
J45
J54
J55
J58
J60
J62
J78
O45
O54
O58
O60
O62
O78
OFB
TRM - Bank guaranteeTransactional dataSAP_FIN_BC_TRM_MFT1_PCStaging TableNot applicableFIN-FSCM-TRMNot applicable
2NZ
2O2
ONZ
TRM - Commercial paperTransactional dataSAP_FIN_BC_TRM_MFT1_PCStaging TableNot applicableFIN-FSCM-TRM-TMNot applicable
1WV
1X7
OWV
TRM - Foreign exchange collar - contractTransactional dataSAP_FIN_BC_TRM_MFT1_PCStaging TableNot applicableFIN-FSCM-TRMNot applicable
1X1
1X9
OX1
TRM - Foreign exchange spot/forward transaction - contractTransactional dataSAP_FIN_BC_TRM_MFT1_PCStaging TableNot applicableFIN-FSCM-TRM-TMNot applicable
1X1
1X9
OX1
TRM - Foreign exchange swap - contractTransactional dataSAP_FIN_BC_TRM_MFT1_PCStaging TableNot applicableFIN-FSCM-TRM-TMNot applicable
1X1
1X9
OX1
TRM - FX optionTransactional dataSAP_FIN_BC_TRM_MFT1_PCStaging TableNot applicableFIN-FSCM-TRM-TMNot applicable
1X1
1X9
OX1
TRM - Interest rate instrumentTransactional dataSAP_FIN_BC_TRM_MFT1_PCStaging TableNot applicableFIN-FSCM-TRMNot applicable
1WV
1X7
OWV
TRM - Interest rate swapTransactional dataSAP_FIN_BC_TRM_MFT1_PCStaging TableNot applicableFIN-FSCM-TRM-TMNot applicable
1X3
1XB
OX3
TRM - Letter of creditTransactional dataSAP_FIN_BC_TRM_MFT1_PCStaging TableNot applicableFIN-FSCM-TRM-TM-TFNot applicable
49D
49E
49F
TRM - Position valueTransactional dataSAP_FIN_BC_TRM_LGCYDT_PCStaging TableNot applicableFIN-FSCM-TRM-TM
TRM - Bank guarantee
TRM - Commercial paper
TRM - FX option
TRM - Foreign exchange swap - contract
TRM - Foreign exchange spot/forward transaction - contract
TRM - Interest rate swap
TRM - Interest rate instrument

1WV
1X1
1X3
1X7
1X9
1XB
1XD
1YI
2F2
2HU
2NZ
2O2
2OI
2RW
ONZ
OWV
OX1
OX3
Warehouse fixed bin assignmentMaster data
SAP_SCM_BC_EWM_MD_WHSE_PC
SAP_SCM_BC_EWM_MON_PC
Staging TableNot applicableSCM-EWM
Product
Warehouse storage bin

3BR
Warehouse stockMaster data
SAP_SCM_BC_EWM_MD_WHSE_PC
SAP_SCM_BC_EWM_MON_PC
Staging TableNot applicableSCM-EWM
Warehouse storage bin
Batch unique at material and client level
Product
MM - Purchasing info record with conditions

3BR
Warehouse storage binMaster data
SAP_SCM_BC_EWM_MD_WHSE_PC
SAP_SCM_BC_EWM_MON_PC
Staging TableNot applicableSCM-EWMNot applicable
3BR
Warehouse storage bin sortingMaster data
SAP_SCM_BC_EWM_MD_WHSE_PC
SAP_SCM_BC_EWM_MON_PC
Staging TableNot applicableSCM-EWMWarehouse storage bin
3BR
Warranty claimTransactional dataSAP_LO_BC_WTY_MANAGECLAIM_PCStaging TableYesLO-WTY
Product
Supplier
PM - Equipment
PM - Functional location

5HR
OHR
Work center/ResourceMaster data
SAP_SCM_BC_PPROC_ENG_MC
SAP_SCM_BC_PROC_ENG_MC
Staging TableNot applicablePPCO - Cost center
4HH
4HI
4VT
4WM
BH1
BJ2
BJ5
BJE
BJH
BJK
BJN
BJQ
OHH
OHI
OVT
OWM
Page Size: 25102550100AllShowing 25 of 275Previous123...11Next




Caution

Data protection legislation may require that personal data be deleted after the data has served its originally defined purpose and is also no longer subject to additional legal data retention requirements. If data protection legislation is applicable in your case, then migrating personal data that should have been deleted could be interpreted as the processing of personal data without any legally justified purpose.



Note

For some migration objects, there are additions to the migration object name. These additions include "restricted" and "deprecated". Restricted means that not all fields and structures of the relevant business processes are covered by this migration object. Deprecated means that there’s a newer version of this migration object available. Deprecated objects will be deleted after a couple of releases. Make sure that you always read the migration object documentation for these objects carefully. Also see SAP Note 2698032  for more details on deprecated migration objects.



Note

Migration objects are built for initial migration of your data to your SAP S/4HANA or SAP S/4HANA Cloud system. This means that you can create data with a migration object, but you can't change or update existing data with it.




Further Information



             Use these SAP Help Portal aliases to access the following sections of our product assistance: 
             







Type this in your browser...
To jump to...




http://help.sap.com/S4_CE_MO
this very topic: Available Migration Objects


http://help.sap.com/S4_CE_DM
the landing page for data migration


http://help.sap.com/S4_CE_DM_STATUS
the entry topic: Data Migration Status






If you want to view information about the differences between the current release and the previous release, see SAP S/4HANA Cloud – Release Comparison of Migration Object Templates (for customers and partners only).

If you want to migrate historical financial transactions into your SAP S/4HANA Cloud using the SAP S/4HANA migration cockpit, see SAP Note 2943035 .

For information regarding mapping of unit of measures, see SAP Knowledge Base Article 2907822 .




CommentsThere are no comments on this page. Leave a comment Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedback Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedbackCopyrightDisclaimerPrivacy StatementLegal DisclosureTrademarkTerms of UseCookie PreferencesAccessibility and SustainabilitySystem StatusAsk a question about the SAP Help PortalFind us onFollow us on FacebookSAP on FacebookFollow us on YouTubeSAP on YouTubeFollow us on LinkedInSAP on LinkedInFollow us on InstagramSAP on InstagramShare

