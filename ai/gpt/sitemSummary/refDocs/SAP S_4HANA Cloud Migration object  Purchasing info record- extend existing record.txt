Available Migration Objects | SAP Help PortalHomeSAP S/4HANA CloudMigration Objects for SAP S/4HANA CloudMigration Objects for SAP S/4HANA Cloud2308.4Available Versions: 2308.4  2308.3  2308.2  2308.1  2308  2302.4 * 2302.3 * 2302.2 * 2302.1 * 2302 * 2208.3 * 2208.2 * 2208.1corr * 2208.1 * 2208 * 2202.4 * 2202.3 * 2202.2 * 2202.1 * 2202 * 2111.1 * 2111 * 2108.1 * 2108 * 2105 * 2102 ** This product version is out of mainstream maintenance. The documentation is no longer regularly updated.For more information, see the Product Availability Matrix (PAM)EnglishAvailable Languages: English  Chinese Simplified (简体中文)  French (Français)  German (Deutsch)  Japanese (日本語)  Portuguese for Brazil (Português do Brasil)  Russian (Русский)  Spanish (Español) ProductionStates:DraftProductionTestThis documentSearch Scopes:All SAP productsThis productThis documentAll of these words:Any of these words:This exact word or phrase:None of these words:ClearSearchAdvanced SearchFavoriteThis page has been removed from your favorites.Download PDFThe following PDF options are available for this document:Create Custom PDFFeedback You can now add comments and questions right into SAP documentation and start a conversation with SAP authors and experts. Don't show me againFeedbackShareEdit in IXIA CCMS Web You can now go directly to IXIA CCMS Web to edit the topic. Simply select Edit in IXIA CCMS Web link under the More… menu. Don't show me againMoreMetadata Analytics Subscribe Edit in IXIA CCMS WebTable of Contents Available Migration Objects


You can use the following table to gain an overview of all migration objects available for SAP S/4HANA Cloud. It’s sorted in alphabetical order by Migration Object Name. Choose a migration object name to navigate to the documentation for the migration object.
Use the search field in the Migration Object Name column to search for a specific migration object.
Choose Filter to apply further filters for Master data and/or Transactional data in the Business Object Type column, for the required business catalogs in the Business Catalog column, for Custom Field Support, and for the application component in the Component column to narrow down the results list. If you want to see more or less information, choose Show/hide columns, and select the respective checkboxes for the columns you would like to show or hide.


Hide/Show ColumnsMigration Object NameBusiness Object TypeBusiness CatalogMigration ApproachCustom Field SupportComponentDownload DataSelect a file type to downloadXLSXCSVSelect the CSV delimiter you want to use for your fileSemi-Colon (;)Comma (,)Pipe Line (|)Caret (^)Which data do you want to download?Download filtered data on this pageDownload all data on all pagesDownload Download Migration Object NameBusiness Object TypeBusiness CatalogMigration ApproachCustom Field SupportComponent Filter: [No Selection]No values match your filter.Master dataTransactional data Filter: [No Selection]Select AllNo values match your filter.Not applicableSAP_CA_BC_BNK_PCSAP_CA_BC_CONSENT_MGMT_PCSAP_CA_BC_RSH_CUSTPROJ_PSP_PCSAP_CMD_BC_BP_DISP_PCSAP_CMD_BC_BP_MAINT_PCSAP_CMD_BC_CUST_MAINT_PCSAP_CMD_BC_PR_MAINT_PCSAP_CMD_BC_SUPP_MAINT_PCSAP_CRM_BC_SERV_PROCORDER_PCSAP_EAM_BC_MEADOC_MCSAP_EAM_BC_MEAPT_MCSAP_EAM_BC_MESP_MNG_PCSAP_EAM_BC_MNTITEM_MNG_PCSAP_EAM_BC_MP_MNG_PCSAP_EAM_BC_MP_ORD_DSP_PCSAP_EAM_BC_MPLAN_MCSAP_EAM_BC_MPLANIT_MCSAP_EAM_BC_NTF_MCSAP_EAM_BC_ORD_MNG_PCSAP_EAM_BC_TL_MCSAP_EAM_BC_TO_MCSAP_EAM_BC_TO_MNG_PCSAP_EAM_BC_TSKL_MNG_PCSAP_EHS_BC_ENV_DT_CLS_MNG_PCSAP_EHS_BC_ENV_MNG_CHPHYS_PCSAP_EHS_BC_ENV_MNG_CMPLREQ_PCSAP_EHS_BC_ENV_MNG_CSCEN_PCSAP_EHS_BC_ENV_MNG_DEV_PCSAP_EHS_BC_ENV_MNG_LOC_PCSAP_EHS_BC_ENV_MNG_LOCCL_PCSAP_EHS_BC_ENV_OPR_DATACOL_PCSAP_EHS_BC_ENV_TASK_MON_PCSAP_EHS_BC_IM_INC_MGMT_PCSAP_EHS_BC_IM_MNG_LOC_PCSAP_EHS_BC_MOC_CREQ_MNG_PCSAP_FICA_BC_DOC_POST_PCSAP_FICA_BC_DUN_MASS_PCSAP_FICA_BC_INSTPL_PROC_PCSAP_FICA_BC_INTRST_PROC_PCSAP_FICA_BC_MD_CA_MAINT_PCSAP_FICA_BC_MD_PC_MAINT_PCSAP_FICA_BC_PRMS2P_MAINT_PCSAP_FICA_BC_SCRTY_DEP_PROC_PCSAP_FIN_BC_AA_DOC_PROC_PCSAP_FIN_BC_AA_MDAT_LDT_PCSAP_FIN_BC_AA_MDAT_REG_PCSAP_FIN_BC_AA_MDAT_USE_OBJ_PCSAP_FIN_BC_AP_IMPORT_RU_PCSAP_FIN_BC_APAR_OPER_PR_RU_PCSAP_FIN_BC_AR_DDM_PROC_PCSAP_FIN_BC_AR_SEPA_EDIT_PCSAP_FIN_BC_ARO_MANAGESAP_FIN_BC_CA_CURR_PCSAP_FIN_BC_CM_OPS_BASIC_PCSAP_FIN_BC_CR_CRED_ACC_PCSAP_FIN_BC_FARR_RAIPRC_PCSAP_FIN_BC_GL_JE_PROC_PCSAP_FIN_BC_GL_REPORTING_BR_PCSAP_FIN_BC_JVA_CMD_PCSAP_FIN_BC_OH_MD_CCA_PCSAP_FIN_BC_OH_MD_OPA_PCSAP_FIN_BC_OH_MD_PCA_PCSAP_FIN_BC_RECM_CONTRACT_PCSAP_FIN_BC_REIP_LOCATION_PCSAP_FIN_BC_TRM_LGCYDT_PCSAP_FIN_BC_TRM_MFT1_PCSAP_LCM_BC_ADMINISTRATOR_PCSAP_LE_BC_JIT_CUST_MAINT_PCSAP_LO_BC_HU_MANAGE_PCSAP_LO_BC_VC_MODELING_PCSAP_LO_BC_WTY_MANAGECLAIM_PCSAP_MM_BC_CC_CRTE_PCSAP_MM_BC_CC_MAN_PCSAP_MM_BC_IM_MAT_DOC_API_PCSAP_MM_BC_IR_PROCESS_PCSAP_MM_BC_MPS_PROCESS_PCSAP_MM_BC_PO_MANAGE_PCSAP_MM_BC_PRCG_PROCESS_PCSAP_MM_BC_PURCH_DOC_DSP_PCSAP_MM_BC_SA_PROCESS_MCSAP_OIL_BC_FLOG_CTNMSTR_PCSAP_OIL_BC_FLOG_SUPLRITEM_PCSAP_OIL_BC_PVOL_CAPTURE_PCSAP_PLM_BC_CLF_MCSAP_PLM_BC_CM_MCSAP_PLM_BC_DIR_EXTD_MCSAP_PLM_BC_DIR_PCSAP_PLM_BC_MBOM_PCSAP_PS_BC_PROJ_CONTRL_MCSAP_PS_BC_PROJ_FIN_ANLYTC_MCSAP_PSM_BC_MD_BDGTSP_PCSAP_PSM_BC_MD_GRNTSP_PCSAP_PSP_BC_CUSTOMER_PROJ_PCSAP_PSP_BC_MIG_PROFNL_PROJ_PCSAP_PSS_BC_PC_CCM_CRR_MON_PCSAP_PSS_BC_PC_CCM_MAN_PCSAP_PSS_BC_PC_FND_PMON_PCSAP_PSS_BC_PC_FND_RMON_PCSAP_PSS_BC_PC_FND_SMMON_PCSAP_PSS_BC_PC_PKG_DG_MGMT_PCSAP_PSS_BC_PC_PMA_ANALYZE_PCSAP_PSS_BC_PC_PROD_CRR_MON_PCSAP_PSS_BC_PC_RM_CRR_MON_PCSAP_PSS_BC_PC_SUBST_MAN_PCSAP_PSS_BC_PC_TECH_NAMES_PCSAP_PSS_BC_PC_UPKG_DG_MGMT_PCSAP_QM_BC_BASIC_DATA_PCSAP_QM_BC_CERTIFICATE_PLNG_PCSAP_QM_BC_INSP_METHODS_PCSAP_QM_BC_INSP_PLANNING_PCSAP_QM_BC_MSTR_INSP_CHARC_PCSAP_QM_BC_QM_PROCUREMENT_PCSAP_RFM_BC_ASSTMT_MAINT_PCSAP_RFM_BC_MRCHDSCAT_MNTN_PCSAP_RFM_BC_SITE_MAINT_PCSAP_RFM_BC_SITE_MNGGRP_PCSAP_S4CRM_BC_SERV_CONTR_PCSAP_S4CRM_BC_SOLN_ORD_PROC_PCSAP_SCM_BC_BATCH_MGMT_MCSAP_SCM_BC_EWM_MD_WHSE_PCSAP_SCM_BC_EWM_MON_PCSAP_SCM_BC_IM_BATCH_SEARCH_PCSAP_SCM_BC_KNBN_CTRLC_MGMT_MCSAP_SCM_BC_PO_BATCH_SEARCH_PCSAP_SCM_BC_PPROC_ENG_MCSAP_SCM_BC_PROC_ENG_MCSAP_SCM_BC_PRODN_ORD_CTRL_MCSAP_SCM_BC_PRODN_ORD_MPLN_MCSAP_SCM_BC_SD_BATCH_SEARCH_PCSAP_SD_BC_CC_CRTE_PCSAP_SD_BC_CC_EC_CRTE_PCSAP_SD_BC_CC_EC_MNG_PCSAP_SD_BC_CC_MAN_PCSAP_SD_BC_CC_RS_CRTE_PCSAP_SD_BC_CC_RS_MNG_PCSAP_SD_BC_CONTR_PROC_MCSAP_SD_BC_MD_MANAGE_MCSAP_SD_BC_PRICE_MANAGE_MCSAP_SD_BC_SLS_SCHEDULE_PCSAP_SD_BC_SO_PROC_MCSAP_SLL_BC_CLS_CMMDTYCODE_PCSAP_SLL_BC_CLS_LEGCTRL_PC Filter: [No Selection]No values match your filter.Staging Table Filter: [No Selection]No values match your filter.Not applicableYes Filter: [No Selection]Select AllNo values match your filter.CA-AROCA-CLCA-CPDCA-DMSCA-FL-SGCA-FL-SRVCA-GTF-CONCA-IAM-MOCCA-JVACA-PVLCM-GFCOCRM-S4-SOL-SLOCRM-S4-SRV-CTRCRM-S4-SRV-SVOEHS-SUS-CIEHS-SUS-DGEHS-SUS-EMEHS-SUS-FNDEHS-SUS-IMEHS-SUS-PMAEHS-SUS-SDSFIFI-AAFI-APFI-ARFI-CAFI-LOC-FIFI-LOC-FI-RUFI-RAFIN-FSCM-CLMFIN-FSCM-TRMFIN-FSCM-TRM-TMFIN-FSCM-TRM-TM-TFFT-ITR-CLSIS-OIL-PRALE-JITLE-JIT-S2CLOLO-ABLO-BMLO-HU-BFLO-RFM-MD-LSTLO-RFM-MD-MCLO-RFM-MD-SITLO-VCHLO-WTYMMMM-IMMM-PURMM-PUR-MPSPLM-WUI-OBJ-ECNPMPPPP-BDPP-KABPP-PIPP-PI-PORPP-SFCPPM-PROPSM-FMPSM-FM-MDPSM-GM-GTE-MDQMRE-FXSCM-EWMSDSD-SLSMigration Object NameBusiness Object TypeBusiness CatalogMigration ApproachCustom Field SupportComponentNo content displayedAsset retirement obligation - Master dataMaster dataSAP_FIN_BC_ARO_MANAGEStaging TableNot applicableCA-AROAsset retirement obligation - PostingsTransactional dataSAP_FIN_BC_ARO_MANAGEStaging TableNot applicableCA-AROAVC - Assign global dependency to characteristicMaster dataSAP_LO_BC_VC_MODELING_PCStaging TableNot applicableLO-VCHAVC - Assign global dependency to characteristic valueMaster dataSAP_LO_BC_VC_MODELING_PCStaging TableNot applicableLO-VCHAVC - Characteristics groupMaster dataSAP_LO_BC_VC_MODELING_PCStaging TableNot applicableLO-VCHAVC - Configuration profile (restricted)Master dataSAP_LO_BC_VC_MODELING_PCStaging TableNot applicableLO-VCHAVC - ConstraintMaster dataSAP_LO_BC_VC_MODELING_PCStaging TableNot applicableLO-VCHAVC - Dependency netMaster dataSAP_LO_BC_VC_MODELING_PCStaging TableNot applicableLO-VCHAVC - Material variantMaster dataSAP_LO_BC_VC_MODELING_PCStaging TableNot applicableLO-VCHAVC - Material variant (deprecated)Master dataSAP_LO_BC_VC_MODELING_PCStaging TableNot applicableLO-VCHAVC - Object dependenciesMaster dataSAP_LO_BC_VC_MODELING_PCStaging TableNot applicableLO-VCHAVC - Variant table entryMaster dataSAP_LO_BC_VC_MODELING_PCStaging TableNot applicableLO-VCHAVC - Variant table structureMaster dataSAP_LO_BC_VC_MODELING_PCStaging TableNot applicableLO-VCHBankMaster dataSAP_CA_BC_BNK_PCStaging TableNot applicableFIBatch unique at material and client levelMaster dataSAP_SCM_BC_BATCH_MGMT_MCStaging TableNot applicableLO-BMBusiness solution orderTransactional dataSAP_S4CRM_BC_SOLN_ORD_PROC_PCStaging TableYesCRM-S4-SOL-SLOChange request and activity for SAP Management of ChangeTransactional dataSAP_EHS_BC_MOC_CREQ_MNG_PCStaging TableNot applicableCA-IAM-MOCCharacteristicMaster dataSAP_PLM_BC_CLF_MCStaging TableNot applicableCA-CLClassMaster dataSAP_PLM_BC_CLF_MCStaging TableNot applicableCA-CLClass hierarchyTransactional dataSAP_PLM_BC_CLF_MCStaging TableNot applicableCA-CLCO - Activity typeMaster dataSAP_FIN_BC_OH_MD_CCA_PCStaging TableNot applicableCOCO - Cost centerMaster dataSAP_FIN_BC_OH_MD_CCA_PCStaging TableNot applicableCOCO - Internal order (restricted)Transactional dataSAP_FIN_BC_OH_MD_OPA_PCStaging TableNot applicableCOCO - Profit centerMaster dataSAP_FIN_BC_OH_MD_PCA_PCStaging TableNot applicableCOCO - Profit center (deprecated)Master dataSAP_FIN_BC_OH_MD_PCA_PCStaging TableNot applicableCOCO - Statistical key figureMaster dataSAP_FIN_BC_OH_MD_CCA_PCStaging TableNot applicableCOCommercial projectMaster dataSAP_PSP_BC_MIG_PROFNL_PROJ_PCStaging TableYesCA-CPDCondition contractTransactional data
SAP_MM_BC_CC_CRTE_PC
SAP_MM_BC_CC_MAN_PC
SAP_SD_BC_CC_MAN_PC
SAP_SD_BC_CC_EC_MNG_PC
SAP_SD_BC_CC_EC_CRTE_PC
SAP_SD_BC_CC_CRTE_PC
SAP_SD_BC_CC_RS_MNG_PC
SAP_SD_BC_CC_RS_CRTE_PC
Staging TableNot applicableLO-ABCondition record for batch determination (for all areas)Master data
SAP_SCM_BC_SD_BATCH_SEARCH_PC
SAP_SCM_BC_IM_BATCH_SEARCH_PC
SAP_SCM_BC_PO_BATCH_SEARCH_PC
Staging TableNot applicableLO-BMCondition record for pricing (general template)Master data
SAP_SD_BC_PRICE_MANAGE_MC
SAP_CMD_BC_BP_DISP_PC
SAP_PSP_BC_CUSTOMER_PROJ_PC
SAP_CA_BC_RSH_CUSTPROJ_PSP_PC
SAP_MM_BC_PRCG_PROCESS_PC
Staging TableNot applicableSDCondition record for pricing in purchasing (restricted)Master dataSAP_MM_BC_PRCG_PROCESS_PCStaging TableNot applicableMM-PURCondition record for pricing in sales (restricted)Master data
SAP_SD_BC_PRICE_MANAGE_MC
SAP_PSP_BC_CUSTOMER_PROJ_PC
SAP_CA_BC_RSH_CUSTPROJ_PSP_PC
Staging TableNot applicableSDConsentTransactional dataSAP_CA_BC_CONSENT_MGMT_PCStaging TableNot applicableCA-GTF-CONContract number in accounting documents (Russia)Transactional dataSAP_FIN_BC_APAR_OPER_PR_RU_PCStaging TableNot applicableFI-LOC-FI-RUCustomerMaster dataSAP_CMD_BC_CUST_MAINT_PCStaging TableYesSDCustomer - extend exist. record by credit mgmt data (dep.)Master dataSAP_FIN_BC_CR_CRED_ACC_PCStaging TableNot applicableSDCustomer - extend existing record by credit management dataMaster dataSAP_FIN_BC_CR_CRED_ACC_PCStaging TableNot applicableSDCustomer - extend existing record by multiple addressesMaster dataSAP_CMD_BC_CUST_MAINT_PCStaging TableNot applicableSDCustomer - extend existing record by new org levelsMaster dataSAP_CMD_BC_CUST_MAINT_PCStaging TableYesSDCustomer - extend existing record by Thailand branch codeMaster dataSAP_CMD_BC_CUST_MAINT_PCStaging TableNot applicableSDDG - Assessment for packaged productMaster dataSAP_PSS_BC_PC_PKG_DG_MGMT_PCStaging TableNot applicableEHS-SUS-DGDG - Assessment for unpackaged product (content-based)Master dataSAP_PSS_BC_PC_UPKG_DG_MGMT_PC Staging TableNot applicableEHS-SUS-DGDG - Assessment for unpackaged product (text-based)Master dataSAP_PSS_BC_PC_UPKG_DG_MGMT_PC Staging TableNot applicableEHS-SUS-DGDG - Technical names for substanceMaster dataSAP_PSS_BC_PC_TECH_NAMES_PCStaging TableNot applicableEHS-SUS-DGDocument info recordMaster dataSAP_PLM_BC_DIR_PCStaging TableNot applicableCA-DMSECM - Change masterMaster dataSAP_PLM_BC_CM_MCStaging TableNot applicablePLM-WUI-OBJ-ECNEFD - Reinf report (Brazil)Transactional dataSAP_FIN_BC_GL_REPORTING_BR_PCStaging TableNot applicableFI-LOC-FIEHS - Calculation definitionMaster dataSAP_EHS_BC_ENV_MNG_CSCEN_PCStaging TableNot applicableEHS-SUS-EMEHS - Chemical/Physical propertyMaster dataSAP_EHS_BC_ENV_MNG_CHPHYS_PCStaging TableNot applicableEHS-SUS-FNDEHS - Compliance requirementMaster dataSAP_EHS_BC_ENV_MNG_CMPLREQ_PCStaging TableNot applicableEHS-SUS-CIEHS - Compliance scenarioMaster dataSAP_EHS_BC_ENV_MNG_CSCEN_PCStaging TableNot applicableEHS-SUS-EMEHS - Data classifierMaster dataSAP_EHS_BC_ENV_DT_CLS_MNG_PCStaging TableNot applicableEHS-SUS-EMEHS - Data collectionMaster dataSAP_EHS_BC_ENV_OPR_DATACOL_PCStaging TableNot applicableEHS-SUS-EMEHS - Deviation incidentMaster dataSAP_EHS_BC_ENV_MNG_DEV_PCStaging TableNot applicableEHS-SUS-IMEHS - IncidentTransactional dataSAP_EHS_BC_IM_INC_MGMT_PCStaging TableNot applicableEHS-SUS-IMEHS - LocationMaster dataSAP_EHS_BC_IM_MNG_LOC_PCStaging TableNot applicableEHS-SUS-FNDEHS - Location aggregationMaster dataSAP_EHS_BC_ENV_MNG_CSCEN_PCStaging TableNot applicableEHS-SUS-EMEHS - Location classifierMaster dataSAP_EHS_BC_ENV_MNG_LOCCL_PCStaging TableNot applicableEHS-SUS-CIEHS - Location hierarchyMaster data
SAP_EHS_BC_ENV_MNG_LOC_PC
SAP_EHS_BC_IM_MNG_LOC_PC
Staging TableNot applicableEHS-SUS-FNDEHS - TaskTransactional dataSAP_EHS_BC_ENV_TASK_MON_PCStaging TableNot applicableEHS-SUS-EMEnterprise projectMaster dataSAP_PS_BC_PROJ_CONTRL_MCStaging TableYesPPM-PROExchange rateMaster dataSAP_FIN_BC_CA_CURR_PCStaging TableNot applicableFIFI - Accounts payable open itemTransactional dataSAP_FIN_BC_GL_JE_PROC_PCStaging TableYesFI-APFI - Accounts receivable open itemTransactional dataSAP_FIN_BC_GL_JE_PROC_PCStaging TableYesFI-ARFI - Bank account balanceTransactional dataSAP_FIN_BC_CM_OPS_BASIC_PCStaging TableNot applicableFIN-FSCM-CLMFI - Cash memo recordTransactional dataSAP_FIN_BC_CM_OPS_BASIC_PCStaging TableNot applicableFIN-FSCM-CLMFI - G/L account balance and open/line itemTransactional dataSAP_FIN_BC_GL_JE_PROC_PCStaging TableYesFIFI - Historical balanceTransactional dataSAP_FIN_BC_GL_JE_PROC_PCStaging TableNot applicableFIFI - Historical balance (deprecated)Transactional dataSAP_FIN_BC_GL_JE_PROC_PCStaging TableNot applicableFIFI - SEPA mandateMaster dataSAP_FIN_BC_AR_SEPA_EDIT_PCStaging TableNot applicableFIFI-CA - Cash security depositTransactional dataSAP_FICA_BC_SCRTY_DEP_PROC_PCStaging TableNot applicableFI-CAFI-CA - Contract accountMaster dataSAP_FICA_BC_MD_CA_MAINT_PCStaging TableYesFI-CAFI-CA - Contract partnerMaster dataSAP_CMD_BC_BP_MAINT_PCStaging TableYesFI-CAFI-CA - Direct debit mandateMaster dataSAP_FIN_BC_AR_DDM_PROC_PCStaging TableNot applicableFIFI-CA - Dunning historyTransactional dataSAP_FICA_BC_DUN_MASS_PCStaging TableNot applicableFI-CAFI-CA - Installment planTransactional dataSAP_FICA_BC_INSTPL_PROC_PCStaging TableNot applicableFI-CAFI-CA - Interest historyTransactional dataSAP_FICA_BC_INTRST_PROC_PCStaging TableNot applicableFI-CAFI-CA - Open itemTransactional dataSAP_FICA_BC_DOC_POST_PCStaging TableYesFI-CAFI-CA - PaymentTransactional dataSAP_FICA_BC_DOC_POST_PCStaging TableYesFI-CAFI-CA - Promise to payTransactional dataSAP_FICA_BC_PRMS2P_MAINT_PCStaging TableNot applicableFI-CAFI-CA - Provider contractMaster dataSAP_FICA_BC_MD_PC_MAINT_PCStaging TableYesFI-CAField Logistics - Container (restricted)Master dataSAP_OIL_BC_FLOG_CTNMSTR_PCStaging TableYesCA-FL-SGField logistics - Supplier itemTransactional dataSAP_OIL_BC_FLOG_SUPLRITEM_PCStaging TableYesCA-FL-SRVFixed asset - Master dataMaster data
SAP_FIN_BC_AA_MDAT_LDT_PC
SAP_FIN_BC_AA_MDAT_REG_PC
Staging TableYesFI-AAFixed asset - PostingsTransactional dataSAP_FIN_BC_AA_DOC_PROC_PCStaging TableNot applicableFI-AAFixed asset - Usage objectMaster dataSAP_FIN_BC_AA_MDAT_USE_OBJ_PCStaging TableNot applicableFI-AAImport customs declaration (Russia)Transactional dataSAP_FIN_BC_AP_IMPORT_RU_PCStaging TableNot applicableFI-LOC-FI-RUJIT - CustomerMaster dataSAP_LE_BC_JIT_CUST_MAINT_PCStaging TableNot applicableLE-JIT-S2CJIT - Packing group specificationMaster dataSAP_LE_BC_JIT_CUST_MAINT_PCStaging TableNot applicableLE-JIT-S2CJIT - Supply controlMaster dataSAP_LE_BC_JIT_CUST_MAINT_PCStaging TableNot applicableLE-JIT-S2CJIT - Supply control (deprecated)Master dataSAP_LE_BC_JIT_CUST_MAINT_PCStaging TableNot applicableLE-JITJVA - Cost center suspenseMaster dataSAP_FIN_BC_JVA_CMD_PCStaging TableNot applicableCA-JVAJVA - Joint operating agreementMaster dataSAP_FIN_BC_JVA_CMD_PCStaging TableNot applicableCA-JVAJVA - Joint venture master dataMaster dataSAP_FIN_BC_JVA_CMD_PCStaging TableNot applicableCA-JVAJVA - Joint venture partnerMaster dataSAP_FIN_BC_JVA_CMD_PCStaging TableNot applicableCA-JVAJVA - Project suspenseMaster dataSAP_FIN_BC_JVA_CMD_PCStaging TableNot applicableCA-JVAJVA - Venture suspenseMaster dataSAP_FIN_BC_JVA_CMD_PCStaging TableNot applicableCA-JVALegal documentTransactional dataSAP_LCM_BC_ADMINISTRATOR_PCStaging TableNot applicableCM-GFLegal transactionTransactional dataSAP_LCM_BC_ADMINISTRATOR_PCStaging TableNot applicableCM-GFLO - Handling unitTransactional dataSAP_LO_BC_HU_MANAGE_PCStaging TableNot applicableLO-HU-BFMaster recipeMaster dataSAP_SCM_BC_PPROC_ENG_MCStaging TableNot applicablePP-PIMaterial - Forecast planningMaster dataSAP_CMD_BC_PR_MAINT_PCStaging TableNot applicableMMMaterial BOMMaster dataSAP_PLM_BC_MBOM_PCStaging TableNot applicablePP-BDMaterial inventory balanceTransactional dataSAP_MM_BC_IM_MAT_DOC_API_PCStaging TableNot applicableMM-IMMaterial listing and exclusionMaster dataSAP_SD_BC_MD_MANAGE_MCStaging TableNot applicableSDMM - Model product specificationMaster dataSAP_MM_BC_MPS_PROCESS_PCStaging TableNot applicableMM-PUR-MPSMM - Purchase contractTransactional dataNot applicableStaging TableNot applicableMM-PURMM - Purchase order (only open PO)Transactional data
SAP_MM_BC_PO_MANAGE_PC
SAP_MM_BC_PURCH_DOC_DSP_PC
SAP_PS_BC_PROJ_FIN_ANLYTC_MC
Staging TableYesMM-PURMM - Purchase scheduling agreementTransactional dataSAP_MM_BC_SA_PROCESS_MCStaging TableNot applicableMM-PURMM - Purchasing info record with conditionsMaster dataSAP_MM_BC_IR_PROCESS_PCStaging TableNot applicableMM-PURMM - Purchasing info record- extend existing recordMaster dataSAP_MM_BC_IR_PROCESS_PCStaging TableNot applicableMM-PURMM - Source listTransactional dataNot applicableStaging TableNot applicableMM-PURObject classification - Code group (QPGR)Master dataSAP_QM_BC_BASIC_DATA_PCStaging TableNot applicableCA-CLObject classification - Document (DRAW)Master dataSAP_PLM_BC_DIR_EXTD_MCStaging TableNot applicableCA-CLObject classification - General templateMaster data
SAP_QM_BC_BASIC_DATA_PC
SAP_CMD_BC_SUPP_MAINT_PC
SAP_CMD_BC_PR_MAINT_PC
SAP_CMD_BC_CUST_MAINT_PC
SAP_EAM_BC_MPLAN_MC
SAP_PLM_BC_DIR_EXTD_MC
SAP_PLM_BC_CM_MC
SAP_EAM_BC_TO_MC
SAP_PLM_BC_CLF_MC
SAP_MM_BC_IR_PROCESS_PC
SAP_QM_BC_INSP_METHODS_PC
SAP_QM_BC_MSTR_INSP_CHARC_PC
SAP_SD_BC_MD_MANAGE_MC
SAP_SCM_BC_PROC_ENG_MC
SAP_RFM_BC_SITE_MNGGRP_PC
SAP_SCM_BC_BATCH_MGMT_MC
Staging TableNot applicableCA-CLObject classification - Inspection characteristic (QPMK)Master dataSAP_QM_BC_MSTR_INSP_CHARC_PCStaging TableNot applicableCA-CLObject classification - Inspection method (QMTB)Master dataSAP_QM_BC_INSP_METHODS_PCStaging TableNot applicableCA-CLObject classification - Material (MARA)Master dataSAP_CMD_BC_PR_MAINT_PCStaging TableNot applicableCA-CLObject classification - Purchasing info record (EINA)Master data
SAP_MM_BC_IR_PROCESS_PC
SAP_PLM_BC_CLF_MC
Staging TableNot applicableCA-CLObject classification - Selected set (QPAM)Master dataSAP_QM_BC_BASIC_DATA_PCStaging TableNot applicableCA-CLPC - Analytical compositionMaster data
SAP_PSS_BC_PC_FND_RMON_PC
SAP_PSS_BC_PC_FND_PMON_PC
Staging TableNot applicableEHS-SUS-FNDPC - Confidential business informationMaster dataSAP_PSS_BC_PC_FND_PMON_PCStaging TableNot applicableEHS-SUS-FNDPC - Material-based compositionMaster dataSAP_PSS_BC_PC_FND_PMON_PCStaging TableNot applicableEHS-SUS-FNDPC - Physical-chemical propertyMaster data
SAP_PSS_BC_PC_FND_RMON_PC
SAP_PSS_BC_PC_FND_PMON_PC
SAP_PSS_BC_PC_SUBST_MAN_PC
Staging TableNot applicableEHS-SUS-FNDPC - Polymer compositionMaster dataSAP_PSS_BC_PC_SUBST_MAN_PCStaging TableNot applicableEHS-SUS-FNDPC - Product compliance infoMaster dataSAP_PSS_BC_PC_FND_PMON_PCStaging TableNot applicableEHS-SUS-FNDPC - Product compliance info (deprecated)Master dataSAP_PSS_BC_PC_FND_PMON_PCStaging TableNot applicableEHS-SUS-FNDPC - Raw material compliance infoMaster dataSAP_PSS_BC_PC_FND_RMON_PCStaging TableNot applicableEHS-SUS-FNDPC - Raw material compliance info (deprecated)Master dataSAP_PSS_BC_PC_FND_RMON_PCStaging TableNot applicableEHS-SUS-FNDPC - Safety-related propertyMaster data
SAP_PSS_BC_PC_FND_PMON_PC
SAP_PSS_BC_PC_SUBST_MAN_PC
SAP_PSS_BC_PC_FND_RMON_PC
Staging TableNot applicableEHS-SUS-FNDPC - SubstanceMaster dataSAP_PSS_BC_PC_SUBST_MAN_PCStaging TableNot applicableEHS-SUS-FNDPM - EquipmentMaster data
SAP_EAM_BC_TO_MC
SAP_EAM_BC_TO_MNG_PC
Staging TableYesPMPM - Equipment BOMMaster data
SAP_EAM_BC_TO_MC
SAP_EAM_BC_TO_MNG_PC
Staging TableNot applicablePMPM - Equipment task listMaster data
SAP_EAM_BC_TL_MC
SAP_EAM_BC_TSKL_MNG_PC
Staging TableNot applicablePMPM - Functional locationMaster data
SAP_EAM_BC_TO_MC
SAP_EAM_BC_TO_MNG_PC
Staging TableYesPMPM - Functional location BOMMaster data
SAP_EAM_BC_TO_MC
SAP_EAM_BC_TO_MNG_PC
Staging TableNot applicablePMPM - Functional location task listMaster data
SAP_EAM_BC_TL_MC
SAP_EAM_BC_TSKL_MNG_PC
Staging TableNot applicablePMPM - General maintenance task listMaster data
SAP_EAM_BC_TL_MC
SAP_EAM_BC_TSKL_MNG_PC
Staging TableNot applicablePMPM - Maintenance itemMaster data
SAP_EAM_BC_MNTITEM_MNG_PC
SAP_EAM_BC_MPLANIT_MC
Staging TableYesPMPM - Maintenance notificationMaster dataSAP_EAM_BC_NTF_MCStaging TableYesPMPM - Maintenance orderTransactional data
SAP_EAM_BC_MP_ORD_DSP_PC
SAP_EAM_BC_ORD_MNG_PC
Staging TableYesPMPM - Maintenance order (deprecated)Transactional dataNot applicableStaging TableYesPMPM - Maintenance planMaster data
SAP_EAM_BC_MPLAN_MC
SAP_EAM_BC_MP_MNG_PC
Staging TableYesPMPM - Measurement documentMaster dataSAP_EAM_BC_MEADOC_MCStaging TableNot applicablePMPM - Measuring pointMaster data
SAP_EAM_BC_MESP_MNG_PC
SAP_EAM_BC_MEAPT_MC
Staging TableNot applicablePMPMA - Customer compliance assessmentMaster data
SAP_PSS_BC_PC_CCM_MAN_PC
SAP_PSS_BC_PC_CCM_CRR_MON_PC
SAP_PSS_BC_PC_FND_PMON_PC
SAP_PSS_BC_PC_PMA_ANALYZE_PC
SAP_PSS_BC_PC_PROD_CRR_MON_PC
Staging TableNot applicableEHS-SUS-PMAPMA - Simple compliance assessmentMaster data
SAP_PSS_BC_PC_RM_CRR_MON_PC
SAP_PSS_BC_PC_FND_PMON_PC
SAP_PSS_BC_PC_FND_RMON_PC
SAP_PSS_BC_PC_FND_SMMON_PC
SAP_PSS_BC_PC_PROD_CRR_MON_PC
SAP_PSS_BC_PC_PMA_ANALYZE_PC
Staging TableNot applicableEHS-SUS-PMAPMA - Supplier information assessmentMaster data
SAP_PSS_BC_PC_FND_PMON_PC
SAP_PSS_BC_PC_RM_CRR_MON_PC
SAP_PSS_BC_PC_PROD_CRR_MON_PC
SAP_PSS_BC_PC_PMA_ANALYZE_PC
SAP_PSS_BC_PC_FND_RMON_PC
SAP_PSS_BC_PC_FND_SMMON_PC
Staging TableNot applicableEHS-SUS-PMAPP - Process order (only open PO)Transactional dataSAP_SCM_BC_PRODN_ORD_MPLN_MCStaging TableNot applicablePP-PI-PORPP - Production order (only open PO)Transactional dataSAP_SCM_BC_PRODN_ORD_CTRL_MCStaging TableNot applicablePP-SFCPP-KAB - Kanban control cycleMaster dataSAP_SCM_BC_KNBN_CTRLC_MGMT_MCStaging TableNot applicablePP-KABPP-KAB - Production supply areaMaster dataSAP_SCM_BC_KNBN_CTRLC_MGMT_MCStaging TableYesPP-KABPRA - Account entry controlMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Allocation cross referenceMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Check input process ruleMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Check input translationMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Chemical analysis dataMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - ContractMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Contract volumeTransactional dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Delivery networkMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Delivery network allocation profileMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Delivery network downstream nodeMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Division order interestMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - DOI accountingMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - DOI MP WC cross referenceMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - extend existing customer by PRA dataMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - extend existing supplier by PRA dataMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Joint ventureMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Journal entry open balanceTransactional dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Marketing group assignmentMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Measurement pointMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Measurement point allocation profileMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Measurement point volumeTransactional dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - MP-WC to contract cross referenceMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - MP/WC transporter cross referenceMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Ownership leaseMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Remitter to DOI cross referenceMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Settlement diversityMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Settlement statement DOI cross referenceMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - State tax rateMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Tax calculation dataMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Unit venture tractMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Valuation cross referenceMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Valuation formulaMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - WC/DN theoretical calculation methodMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Well and well completionMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Well completion tax classificationMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Well completion testMaster dataNot applicableStaging TableNot applicableIS-OIL-PRAPRA - Well completion volumeTransactional dataNot applicableStaging TableNot applicableIS-OIL-PRAProductMaster dataSAP_CMD_BC_PR_MAINT_PCStaging TableYesLOProduct - extend existing record by new org levelsMaster dataSAP_CMD_BC_PR_MAINT_PCStaging TableYesLOProduct - extend existing record with long textMaster dataSAP_CMD_BC_PR_MAINT_PCStaging TableNot applicableLOProduct classification - Commodity codeMaster dataSAP_SLL_BC_CLS_CMMDTYCODE_PCStaging TableNot applicableFT-ITR-CLSProduct classification - Legal controlMaster dataSAP_SLL_BC_CLS_LEGCTRL_PCStaging TableNot applicableFT-ITR-CLSProduct consumptionMaster dataSAP_CMD_BC_PR_MAINT_PCStaging TableNot applicableMMProduct hierarchyMaster dataSAP_CMD_BC_PR_MAINT_PCStaging TableNot applicableLOProduct hierarchy assignmentMaster dataSAP_CMD_BC_PR_MAINT_PCStaging TableNot applicableLOProduction versionMaster data
SAP_SCM_BC_PPROC_ENG_MC
SAP_SCM_BC_PROC_ENG_MC
Staging TableNot applicablePPProduction volume captureTransactional dataSAP_OIL_BC_PVOL_CAPTURE_PCStaging TableNot applicableCA-PVLPSM - Functional areaMaster dataSAP_PSM_BC_MD_BDGTSP_PCStaging TableNot applicablePSM-FM-MDPSM - FundMaster dataSAP_PSM_BC_MD_BDGTSP_PCStaging TableYesPSM-FMPSM - GrantMaster dataSAP_PSM_BC_MD_GRNTSP_PCStaging TableNot applicablePSM-GM-GTE-MDPSM - SponsorMaster dataSAP_PSM_BC_MD_GRNTSP_PCStaging TableNot applicablePSM-GM-GTE-MDPSM - Sponsored classMaster dataSAP_PSM_BC_MD_GRNTSP_PCStaging TableNot applicablePSM-GM-GTE-MDPSM - Sponsored programMaster dataSAP_PSM_BC_MD_GRNTSP_PCStaging TableNot applicablePSM-GM-GTE-MDQM - Certificate profile incl. assignmentMaster dataSAP_QM_BC_CERTIFICATE_PLNG_PCStaging TableNot applicableQMQM - Inspection methodMaster dataSAP_QM_BC_INSP_METHODS_PCStaging TableNot applicableQMQM - Inspection planMaster dataSAP_QM_BC_INSP_PLANNING_PCStaging TableNot applicableQMQM - Master inspection characteristicMaster dataSAP_QM_BC_MSTR_INSP_CHARC_PCStaging TableNot applicableQMQM - Quality info recordMaster dataSAP_QM_BC_QM_PROCUREMENT_PCStaging TableNot applicableQMQM - Quality info record (deprecated)Master dataSAP_QM_BC_QM_PROCUREMENT_PCStaging TableNot applicableQMQM - Selected setMaster dataSAP_QM_BC_BASIC_DATA_PCStaging TableNot applicableQMQM - Selected set codeMaster data
SAP_QM_BC_BASIC_DATA_PC
SAP_QM_BC_INSP_PLANNING_PC
Staging TableNot applicableQMQM/PM - Catalog code group/codeMaster dataSAP_QM_BC_BASIC_DATA_PCStaging TableNot applicableQMReal estate - Occupancy groupMaster dataSAP_FIN_BC_REIP_LOCATION_PCStaging TableYesRE-FXReal estate - Usage enablement groupMaster dataSAP_FIN_BC_REIP_LOCATION_PCStaging TableYesRE-FXReal estate contractTransactional dataSAP_FIN_BC_RECM_CONTRACT_PCStaging TableYesRE-FXRevenue accounting contractTransactional dataSAP_FIN_BC_FARR_RAIPRC_PCStaging TableNot applicableFI-RARFM - Assortment moduleMaster dataSAP_RFM_BC_ASSTMT_MAINT_PCStaging TableNot applicableLO-RFM-MD-LSTRFM - MCHN Characteristic value restrictionMaster dataSAP_RFM_BC_MRCHDSCAT_MNTN_PCStaging TableNot applicableLO-RFM-MD-MCRFM - Merchandise categoryMaster dataSAP_RFM_BC_MRCHDSCAT_MNTN_PCStaging TableNot applicableLO-RFM-MD-MCRFM - Merchandise category characteristic value restrictionMaster dataSAP_RFM_BC_MRCHDSCAT_MNTN_PCStaging TableNot applicableLO-RFM-MD-MCRFM - Merchandise category hierarchy nodeMaster dataSAP_RFM_BC_MRCHDSCAT_MNTN_PCStaging TableNot applicableLO-RFM-MD-MCRFM - Merchandise category hierarchy node assignmentMaster dataSAP_RFM_BC_MRCHDSCAT_MNTN_PCStaging TableNot applicableLO-RFM-MD-MCRFM - Merchandise category reference articleMaster dataSAP_RFM_BC_MRCHDSCAT_MNTN_PCStaging TableNot applicableLO-RFM-MD-MCRFM - Product assignment to distribution centerMaster dataSAP_RFM_BC_ASSTMT_MAINT_PCStaging TableNot applicableLO-RFM-MD-LSTRFM - Site masterMaster dataSAP_RFM_BC_SITE_MAINT_PCStaging TableNot applicableLO-RFM-MD-SITRFM - Supplying siteMaster dataSAP_RFM_BC_SITE_MAINT_PCStaging TableNot applicableLO-RFM-MD-SITRNPT - Traceability of goods (Russia)Transactional dataSAP_FIN_BC_AP_IMPORT_RU_PCStaging TableNot applicableFI-LOC-FI-RURoutingMaster dataSAP_SCM_BC_PROC_ENG_MCStaging TableNot applicablePPSD - Condition record for free goodsMaster dataSAP_SD_BC_MD_MANAGE_MCStaging TableNot applicableSDSD - Condition Record for Material determinationMaster dataSAP_SD_BC_MD_MANAGE_MCStaging TableNot applicableSDSD - Customer MaterialMaster dataSAP_SD_BC_MD_MANAGE_MCStaging TableYesSDSD - Sales contractTransactional dataSAP_SD_BC_CONTR_PROC_MCStaging TableNot applicableSDSD - Sales order (only open SO)Transactional dataSAP_SD_BC_SO_PROC_MCStaging TableYesSDSD - Sales scheduling agreementTransactional dataSAP_SD_BC_SLS_SCHEDULE_PCStaging TableNot applicableSD-SLSSDS - Assessment for unpackaged productMaster data
SAP_PSS_BC_PC_FND_RMON_PC
SAP_PSS_BC_PC_FND_PMON_PC
Staging TableNot applicableEHS-SUS-SDSSDS - Shipment historyTransactional dataSAP_PSS_BC_PC_FND_PMON_PCStaging TableNot applicableEHS-SUS-SDSService contractTransactional dataSAP_S4CRM_BC_SERV_CONTR_PCStaging TableYesCRM-S4-SRV-CTRService order (only open SRVO)Transactional dataSAP_CRM_BC_SERV_PROCORDER_PCStaging TableYesCRM-S4-SRV-SVOService productMaster dataSAP_CMD_BC_PR_MAINT_PCStaging TableYesLOSupplierMaster dataSAP_CMD_BC_SUPP_MAINT_PCStaging TableYesMMSupplier - extend existing record by multiple addressesMaster dataSAP_CMD_BC_SUPP_MAINT_PCStaging TableNot applicableMMSupplier - extend existing record by new org levelsMaster dataSAP_CMD_BC_SUPP_MAINT_PCStaging TableYesMMSupplier - extend existing record by Thailand branch codeMaster dataSAP_CMD_BC_SUPP_MAINT_PCStaging TableNot applicableMMTRM - Bank guaranteeTransactional dataSAP_FIN_BC_TRM_MFT1_PCStaging TableNot applicableFIN-FSCM-TRMTRM - Commercial paperTransactional dataSAP_FIN_BC_TRM_MFT1_PCStaging TableNot applicableFIN-FSCM-TRM-TMTRM - Foreign exchange collar - contractTransactional dataSAP_FIN_BC_TRM_MFT1_PCStaging TableNot applicableFIN-FSCM-TRMTRM - Foreign exchange spot/forward transaction - contractTransactional dataSAP_FIN_BC_TRM_MFT1_PCStaging TableNot applicableFIN-FSCM-TRM-TMTRM - Foreign exchange swap - contractTransactional dataSAP_FIN_BC_TRM_MFT1_PCStaging TableNot applicableFIN-FSCM-TRM-TMTRM - FX optionTransactional dataSAP_FIN_BC_TRM_MFT1_PCStaging TableNot applicableFIN-FSCM-TRM-TMTRM - Interest rate instrumentTransactional dataSAP_FIN_BC_TRM_MFT1_PCStaging TableNot applicableFIN-FSCM-TRMTRM - Interest rate swapTransactional dataSAP_FIN_BC_TRM_MFT1_PCStaging TableNot applicableFIN-FSCM-TRM-TMTRM - Letter of creditTransactional dataSAP_FIN_BC_TRM_MFT1_PCStaging TableNot applicableFIN-FSCM-TRM-TM-TFTRM - Position valueTransactional dataSAP_FIN_BC_TRM_LGCYDT_PCStaging TableNot applicableFIN-FSCM-TRM-TMWarehouse fixed bin assignmentMaster data
SAP_SCM_BC_EWM_MD_WHSE_PC
SAP_SCM_BC_EWM_MON_PC
Staging TableNot applicableSCM-EWMWarehouse stockMaster data
SAP_SCM_BC_EWM_MD_WHSE_PC
SAP_SCM_BC_EWM_MON_PC
Staging TableNot applicableSCM-EWMWarehouse storage binMaster data
SAP_SCM_BC_EWM_MON_PC
SAP_SCM_BC_EWM_MD_WHSE_PC
Staging TableNot applicableSCM-EWMWarehouse storage bin sortingMaster data
SAP_SCM_BC_EWM_MON_PC
SAP_SCM_BC_EWM_MD_WHSE_PC
Staging TableNot applicableSCM-EWMWarranty claimTransactional dataSAP_LO_BC_WTY_MANAGECLAIM_PCStaging TableYesLO-WTYWork center/ResourceMaster data
SAP_SCM_BC_PROC_ENG_MC
SAP_SCM_BC_PPROC_ENG_MC
Staging TableNot applicablePPPage Size: 25102550100AllShowing 25 of 261Previous123...11Next




Caution

Data protection legislation may require that personal data be deleted after the data has served its originally defined purpose and is also no longer subject to additional legal data retention requirements. If data protection legislation is applicable in your case, then migrating personal data that should have been deleted could be interpreted as the processing of personal data without any legally justified purpose.



Note

For some migration objects, there are additions to the migration object name. These additions include "restricted" and "deprecated". Restricted means that not all fields and structures of the relevant business processes are covered by this migration object. Deprecated means that there’s a newer version of this migration object available. Deprecated objects will be deleted after a couple of releases. Make sure that you always read the migration object documentation for these objects carefully. Also see SAP Note 2698032  for more details on deprecated migration objects.



Note

Migration objects are built for initial migration of your data to your SAP S/4HANA or SAP S/4HANA Cloud system. This means that you can create data with a migration object, but you can't change or update existing data with it.



Note

The roles listed here are templates. You can use them to create your own roles according to your company's security guidelines.




Further Information



             Use these SAP Help Portal aliases to access the following sections of our product assistance: 
             







Type this in your browser...
To jump to...




http://help.sap.com/S4_CE_MO
this very topic: Available Migration Objects


http://help.sap.com/S4_CE_DM
the landing page for data migration


http://help.sap.com/S4_CE_DM_STATUS
the entry topic: Data Migration Status






If you want to view information about the differences between the current release and the previous release, see SAP S/4HANA Cloud – Release Comparison of Migration Object Templates (for customers and partners only).
The CO - Cost rate migration object is no longer supported in SAP S/4HANA Cloud. You can upload activity prices using the upload function of the new Manage Cost Rates - Plan SAP Fiori app. For more information and support, refer to the SAP Fiori apps reference library.

If you want to migrate historical financial transactions into your SAP S/4HANA Cloud using the SAP S/4HANA migration cockpit, see SAP Note 2943035 .

For information regarding mapping of unit of measures, see SAP Knowledge Base Article 2907822 .




CommentsThere are no comments on this page. Leave a comment Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedback Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedbackCopyrightDisclaimerPrivacy StatementLegal DisclosureTrademarkTerms of UseCookie PreferencesAccessibility and SustainabilitySystem StatusAsk a question about the SAP Help PortalFind us onFollow us on FacebookSAP on FacebookFollow us on YouTubeSAP on YouTubeFollow us on LinkedInSAP on LinkedInFollow us on InstagramSAP on InstagramShare

