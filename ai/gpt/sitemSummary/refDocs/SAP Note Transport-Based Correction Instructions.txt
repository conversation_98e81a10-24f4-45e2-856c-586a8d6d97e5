SAP Note Transport-Based Correction Instructions | SAP Help PortalHomeSAP NetWeaver AS for ABAP 7.51 innovation packageSoftware LogisticsNote AssistantSAP Note Transport-Based Correction InstructionsSoftware LogisticsSP00Available Versions: SP18  SP17  SP16  SP15  SP14  SP13  SP12  SP11  SP10  SP09  SP08  SP07  SP06  SP05  SP04  SP03  SP02  SP01  SP00 EnglishAvailable Languages: English  German (Deutsch) ProductionStates:DraftProductionThis documentSearch Scopes:All SAP productsThis productThis documentAll of these words:Any of these words:This exact word or phrase:None of these words:ClearSearchAdvanced SearchTable of ContentsFavoriteThis page has been removed from your favorites.Download PDFThe following PDF options are available for this document:Create Custom PDFFeedback You can now add comments and questions right into SAP documentation and start a conversation with SAP authors and experts. Don't show me againFeedbackShareMoreMetadata Analytics Subscribe Table of Contents Software Logistics  Transport Scenarios for SAP HANA Content  Software Maintenance for ABAP for SAP HANA  Change and Transport System  Support Package Manager  SAP Add-On Installation Tool  Note Assistant  SAP Notes  Activating Note Assistant  Registering Manually Implemented SAP Notes  Working with SAP Notes  Implementation Status and Processing Status of SAP Notes  Troubleshooting  SAP Note Transport-Based Correction Instructions  Benefits and Limitations  Enabling Note Assistant for SAP Note Transport-Based Correction Instructions  Consumption of SAP Note Transport-Based Correction Instructions  SAP NetWeaver Download Service  Authorizations and Roles  Configuration  SAP Note Transport-Based Correction Instructions


This guide describes a new way to deliver ABAP correction instructions to customers in a
                           			flexible manner: SAP Note transport-based correction instructions (TCI).
                        
The guide addresses the following audience:


System Administrators


Technology Consultants


Up to now, corrections have been delivered in the following ways:


SAP Notes automatic correction instructions


SAP Notes manual correction instructions


Support packages (SP)


Note
In this document, manual and automated correction instructions are not differentiated
                              				and are both called CI.
                           

The following table gives an overview over the standard delivery methods and the new
                           			TCI:
                        


Table 2: Comparision: Standard delivery methods and TCI








SAP Note with Correction Instruction


SAP Note transport-based correction instruction


Support Package








Contains specific, single corrections


CI can be valid for an SP range and can depend on other
                                                										CIs
                                             


Can be implemented automatically for all supported
                                                										objects
                                             


Note
Objects that are not supported must be implemented manually,
                                             									which requires developer knowledge.
                                          





Contains corrections of only one specific functional area,
                                                										which is an encapsulated sub-component
                                             


Can contain multiple single corrections, which reduces CI
                                                										dependencies and update artifacts
                                             


Can be valid for an SP range and can have dependency on other
                                                										TCIs and CIs
                                             


Contains an ABAP transport similar to SP deliveries, but is
                                                										delivered together with SAP Notes as a new type of CIs
                                             


Simplified and accelerated consumption of corrections






Contains all corrections that were made after a previous SP
                                                										shipment
                                             


Corrects the release or the previous SP


Is imported automatically







TCIs support all ABAP objects that have a transport connection. A single TCI consists of
                           			exactly one ABAP transport request and a set of installation attributes, for example,
                           			software component version, minimal SP, languages.
                        
The validity of a TCI is defined by the software component version and a range of SP
                           			levels. If you import in your system an SP that exceeds the validity range of an
                           			implemented TCI, all corrections of the TCI are imported with the SP, and the TCI
                           			becomes obsolete.
                        
Note
A mixture of different software component versions or code lines is not supported.
                              				For this reason, you receive individual TCIs for each software component
                              				version.
                           

Example
A correction for the software component SAP_BASIS 7.00, SAP_BASIS 7.01, SAP_BASIS 7.02 is
                           			delivered in one SAP Note and three TCIs.
                        
The concept described above is depicted in the following figure:






CommentsThere are no comments on this page. Leave a comment Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedback Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedbackCopyrightDisclaimerPrivacy StatementLegal DisclosureTrademarkTerms of UseCookie PreferencesAccessibility and SustainabilitySystem StatusAsk a question about the SAP Help PortalFind us onFollow us on FacebookSAP on FacebookFollow us on YouTubeSAP on YouTubeFollow us on LinkedInSAP on LinkedInFollow us on InstagramSAP on InstagramShare

