

	Managing mandatory address checks for CPD (and oth... - SAP Community











































SAP Community







Products and Technology







Enterprise Resource Planning







ERP Blogs by SAP







Managing mandatory address checks for CPD (and oth...














    	Enterprise Resource Planning Blogs by SAP
    

    	Get insights and updates about cloud ERP and RISE with SAP, SAP S/4HANA and SAP S/4HANA Cloud, and more enterprise management capabilities with SAP blog posts.
    

















All communityThis categoryBlogKnowledge baseUsersManaged tags




Enter a search wordTurn off suggestions
Enter a search wordTurn off suggestions
Enter a user name or rankTurn off suggestions
Enter a search wordTurn off suggestions
Enter a search wordTurn off suggestions



cancel



Turn on suggestions







		Showing results for 



			Search instead for 



		Did you mean: 
























 
 















							Managing mandatory address checks for CPD (and other One-Time Account) Customer or Vendor when moving to SAP S/4HANA
							
						






















former_member589723


			Explorer
		






Options



Subscribe to RSS Feed




Mark as New
Mark as Read




Bookmark
Subscribe




Printer Friendly Page
Report Inappropriate Content









‎03-10-2021
7:46 AM













	
			3
		

	Kudos












			
				
					
					
						Imagine this scenario, you are in the middle of processing your customer or vendor records through the business partner CVI conversion in your SAP ERP system. All this is in preparation for your system conversion to SAP S/4HANA.

When running the CVI synchronization with the MDS load cockpit, the CVI checking process churns out error messages, each related to one-time customer or vendor records complaining about missing address data in these records. Why is this happening?

To find out why, and how you can now resolve this, please read on.

Background

The use of customer or vendor master records in SAP require a relatively high level of mandatory data field entry and maintenance for each master data record. When the number of customer or vendor master data records run into the tens of thousands, the effort and time to maintain the mandatory data fields for each record can be challenging.

Furthermore, for a customer or vendor that you might transact with only once or twice, having to maintain a large amount of data fields just for the one (or two) time use can become questionable.

Therefore, to enable transacting with such rarely used customers or vendors, SAP provides the option of using one-time customer or vendor account groups, which only require minimal data field entry.

There are 2 standard SAP CPD (Conto-pro-Diverse) one-time account groups:

CPD - one-time account group with internal no assignment
CPDA - one-time account group with external no assignment


SAP One-time Account Groups (in SAP ERP)
For customer or vendor records created with these one-time account groups, the bulk of data fields, including address related fields are not mandatory entry and can be left blank.

Problem Statement

However, in SAP S/4HANA, the Country and Language fields are by default mandatory for any business partner record, whether it be customer or supplier, even for records belonging to one-time account groups.

As a result, if you are performing Business Partner CVI processing of your existing customer or vendor records into business partner records in SAP ERP, existing one-time customers or vendors records to be processed will be flagged with error during the CVI process. This is because the CVI conversion process checks all customer or vendor records for the mandatory address data fields in preparation for the conversion to SAP S/4HANA.

This happens when MDS_LOAD_COCKPIT is being executed. The synchronization for such records will fail, resulting in a PPO error message: AM216 "Incomplete address; enter country". 

Which would mean you would have to maintain the missing address data fields first in order to resolve this error.

Solution

To circumvent this unwanted check for one-time vendor or customer records, SAP has introduced the option of suppressing this mandatory check during the CVI conversion process.

This is available as of SAP ERP EHP 8 Support Pack 15, and can also be implemented via the SAP note 2949716 - CPD Account Group Enhancements in Master Data Consistency Check Report.

Refer to this SAP note, and the documented pre-requisite SAP note required for the implementation of this new feature - 2955243 - CPD Account Group Enhancements: Domain and Activate Field Check Suppression.

When implemented, the following configuration steps for Master Data Synchronization can be set up to suppress the Address check for one-time account customer or vendor during the CVI processing.

The configuration navigation path is:

SPRO Customizing-> Cross-Application Components-> Master Data Synchronization-> Customer/Vendor Integration-> Activate Field Check Suppression:

Navigation path in Customizing
In the customizing step Activate Field Check Suppression, Add Entry and select “Address Checks for all C/V One-Time Accounts (CPD etc.)”:

List of available options for Field Suppression
Then activate the selection to suppress the field check:

Select and activate suppression check
This configuration setting then activates the bypass of the mandatory address field checking during CVI processing.

Correspondingly, in SAP S/4HANA, to bypass the address check for one-time account customer or supplier business partners,  you can implement the following SAP note to suppress the check for such business partners records in SAP S/4HANA:-

2917481 - Skip Address Checks for One Time Customer/Supplier in SAP S/4HANA. This feature is also standard as of SAP S/4HANA 2020.

With the implementation of this SAP note, you will have the option to make the customizing setting to skip address checks for vendor and customer one-time accounts. The following screens illustrate the presence of this indicator for a one-time customer account group:

As shown above, the Skip Address Check, though  present, is disabled from use. This is because the One-Time Account indicator is not selected. This is to ensure that the Skip Address check will only be possible for one time customer or vendor account groups.

Once the One-Time Account indicator is selected, the Skip Address Check indicator will also be selectable a shown above.

This behavior is similar for vendor one-time account group as shown above.

With this indicator set, you will then be able to bypass the mandatory default checks for Country Code and Language in SAP S/4HANA when creating customers or supplier business partners with the one-time account groups.

Note: This blog post is relevant for SAP S/4HANA on-premise and Private Cloud Edition deployments. I hope this blog post was helpful for you. If so, click on "like" or "share". I'm looking forward to your feedback and thoughts or clarification questions you may have in the comment section below.

 

Brought to you by the SAP S/4HANA RIG and Customer Care team. 

Helpful links:

See all questions and answers about SAP S/4HANA 
Follow SAP S/4HANA for more blogs and updates
Ask a Question about SAP S/4HANA
Visit your community topic page for SAP S/4HANA

 
					
				
			
			
			
			
			
			
			
			
		




SAP Managed Tags:
SAP S/4HANA,
APP PLATFORM Business Partner 












SAP S/4HANA
SAP S/4HANA












APP PLATFORM Business Partner
Software Product Function






View products (2)




							Labels:
						



Product Updates






cpdCVI conversionS4HANA One-time customersap s4hana rig

































		5 Comments
	
 



 
 






















venu_ch8


			Active Contributor
		










Mark as Read
Mark as New




Bookmark




Permalink
Print




Report Inappropriate Content








‎03-10-2021
2:42 PM












	
			1
		

	Kudo












			
				
					
					
						Good document..
					
				
			
			
			
			
			
			
			
			
		























 
 






















asha_prasanna3


			Explorer
		










Mark as Read
Mark as New




Bookmark




Permalink
Print




Report Inappropriate Content








‎04-24-2021
12:29 PM












	
			0
		

	Kudos












			
				
					
					
						Hi, thanks for the article, find it useful.

I have my next question- what is the best method to process the open items (transactions) of One time vendor and customer during the conversion process.

Should we bring forward the name and address data from BSEC table for these records while converting the open items from legacy into S4H ?

Any inputs will be appreciated. Thanks.
					
				
			
			
			
			
			
			
			
			
		























 
 






















matmiche


			Discoverer
		










Mark as Read
Mark as New




Bookmark




Permalink
Print




Report Inappropriate Content








‎01-05-2022
3:30 PM












	
			0
		

	Kudos












			
				
					
					
						Hello all,
as preparation of the business partner (CVI) I would like to set the field country as mandatory for one time accounts / CPD in ECC.
Unfortunately this is not possible via the screen control (TA OVT0).

Does anyone have an idea? Thanks
					
				
			
			
			
			
			
			
			
			
		























 
 






















former_member785177


			Member
		










Mark as Read
Mark as New




Bookmark




Permalink
Print




Report Inappropriate Content








‎02-08-2022
7:48 PM












	
			0
		

	Kudos












			
				
					
					
						Hi, why I can't find the "Activate Field Check Suppression" in my SPRO, really need your help, thank you.
					
				
			
			
			
			
			
			
			
			
		























 
 






















pankaj_adhikari


			Active Participant
		










Mark as Read
Mark as New




Bookmark




Permalink
Print




Report Inappropriate Content








‎04-26-2023
4:25 PM












	
			0
		

	Kudos












Hi EuI m on S/4 Hana 2020 & made changes as per this blog & SAP Note-2917481 but it didn't worked for me. Activated 'Skip Address Check' for account group through SM30-V_T077K but still Tcode-BP doesn't allows me to extend ONETIME vendor to another company code without adding address.Most of the fields related to address are 'Suppress' in account group but still shows mandatory at BP.Any other options or anything I missed?RegardsPankaj
























 



						You must be a registered user to add a comment. If you've already registered, sign in. Otherwise, register and sign in.
					

Comment






 Labels in this area





Artificial Intelligence (AI)
1


Business Trends
363


Business Trends​
15


Digital Transformation with Cloud ERP (DT)
1


Event Information
461


Event Information​
19


Expert Insights
114


Expert Insights​
117


Life at SAP
418


Life at SAP​
1


Product Updates
4,688


Product Updates​
161


Roadmap and Strategy
1


Technology Updates
1,505


Technology Updates​
71





 


 




Related Content






BKP - Simplified Customer Returns Process with Automatic Product Inspection
in Enterprise Resource Planning Blogs by SAP  yesterday


How to design sales prices in SAP S/4HANA and be ready for future innovations
in Enterprise Resource Planning Blogs by SAP  Friday


User Experience in SAP S/4HANA Cloud Public Edition: New Microlearning Available
in Enterprise Resource Planning Blogs by SAP  Thursday


How to design sales prices in SAP S/4HANA and be ready for future innovations
in Enterprise Resource Planning Blogs by SAP  a week ago


Readiness for Universal Parallel Accounting
in Enterprise Resource Planning Blogs by SAP  a week ago







 





 




Popular Blog Posts










Useful documents on SCN






by 

Nancy


• Product and Topic Expert



134440 Views
123 comments
220 kudos


01-06-2015








Evolution of ABAP






by 

karl_kessler


• Product and Topic Expert



26101 Views
42 comments
196 kudos


09-01-2022








Analytics in S/4HANA - real shape of embedded analytics and beyond embedded analytics






by 

Masaaki


• Advisor



98995 Views
32 comments
182 kudos


06-08-2019









 



 Top kudoed authors

 



User

			Count
		












FabianAckermann









			7
		










Gerhard_Welker









			6
		










Adeem









			5
		










Marco_Valencia









			5
		










Chr_Vogler









			4
		










MarceGiovanetti









			4
		










Saumitra









			3
		









brennen_fischer12









			3
		










Ying









			3
		










Axel









			2
		




View all
 








































Auto-suggest helps you quickly narrow down your search results by suggesting possible matches as you type.