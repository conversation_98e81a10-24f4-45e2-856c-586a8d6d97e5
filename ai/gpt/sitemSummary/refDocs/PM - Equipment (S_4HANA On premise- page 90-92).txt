Implementation Guide | PUBLIC
Document Version: SAP S/4HANA 2021 – 2023-09-27
Migration Objects for SAP S/4HANA© 2023 SAP SE or an SAP affiliate  company. All rights reserved.
THE BEST RUN  

1.316   PM - Equipment
Available Migration Objects [page 2]  in SAP S/4HANA.
 Tip
This migration object supports custom fields.  For more information, refer to the Custom Fields  section
below.
Purpose
Business Object Component/Area Plant Maintenance (PM)
Business Object T ype Master data
1296 PUBLICMigration Objects for SAP S/4HANA
Available Migration ObjectsBusiness Object Definition An individual, physical object that is maintained as an auton-
omous unit.
Migration Approach File/Staging T able
Custom Field Support Y es
In Scope
Migration to SAP S/4HANA is supported.
Starting with SAP S/4HANA release 1809, alternative labeling is supported for the Functional Location  field  in
the equipment structure.
Supported Features
The following structures/features are supported in SAP S/4HANA:
•Equipment General Data
•Equipment Manufacturer Data
•Equipment Customer Warranty
•Equipment Vendor Warranty
•Equipment Location
•Equipment Organization
•Equipment Structure
•Equipment Serial Data
•Equipment Partners
Prerequisites
The following objects need to have already been maintained or migrated:
•Batch unique at material and client level [page 1123]
•Batch unique at plant level [page 1127]
•Fixed asset (incl. balances and transactions) [page 1684]  or Fixed asset - Master data [page 1691]
(depending on system configuration)
•PM - Functional location [page 1322]
Superordinate Equipment : T o use the Superordinate Equipment  field,  make sure that this value will be
implemented in the PM - Functional location [page 1322]  first.
•CO - Cost center [page 1371]
•PS - WBS element [page 1629]
Migration Objects for SAP S/4HANA
Available Migration Objects PUBLIC 1297•Work center/Resource [page 1631]
Validity Period and Dependencies
Business objects that reference this object must be created within a validity period that matches this object.
If a successor object is created with a validity period starting before this object, this will cause an error
message during the creation or migration of the successor object.
Y ou therefore have to check such time dependencies before you create or migrate your objects. This avoids
error messages later and inconsistent data in your database.
Mapping Instructions
Mapping Structure Hierarchy
Level Name
1 Master Data ( S_EQUI ), mandatory
2 Partners ( S_IHPA )
Custom Fields
This migration object is supported by the Custom Fields  app. If you enhance your business object with custom
fields  in the app, these custom fields  will automatically be reflected  in the XML template of the corresponding
migration object. See SAP S/4HANA Migration Cockpit: Support of Custom Fields  for further details. Every
time you add, change or delete a custom field,  the migration project must be updated and a new migration
XML template must be downloaded and used. Thus, if custom fields  are added or changed by an application
expert during migration testing, this must be communicated to and coordinated with your data migration team.
T o check whether custom fields  exist for a migration object, choose Check for Custom Fields  on the Migration
Object  screen. If custom fields  exist, the system updates the migration object with the custom fields.  Also see
the Custom Fields  section in the SAP S/4HANA documentation.
Supported business context:
•Equipment
Internal /External Number Range
This migration object supports both internal and external numbering.
If you want to use an external number range, leave the Indicator: Use Internal Number Range  field  empty in the
template. Otherwise, enter an X if it's internal.
Warranty Fields
Warranty fields  can be displayed only in the SAP Fiori app.
1298 PUBLICMigration Objects for SAP S/4HANA
Available Migration ObjectsPartner Function
On the UI, the partner function is displayed as language-dependent. For data migration, you have to maintain
the language-independent database value. Refer to the following example.
 Example
English Migration
VN LF
SH WE
CP AP
BP RE
ER ZM
Tasks
1. Navigate to the Data Migration to SAP S/4HANA from Staging (2Q2)
  scope item on SAP Best Practices
Explorer.
2. Choose your SAP S/4HANA  release from the Version  field.
3. Download the test script from the Details  section.
4. Follow the procedure described in the 2Q2 test script.
Post-Processing
How to Validate Y our Data in the System
App:Display T echnical Object
Business Role:Maintenance Planner  (SAP_BR_MAINTENANCE_PLANNER )
Find more information about SAP Fiori apps in the SAP Fiori apps reference library . This information covers
required business roles and includes links to the respective app documentation.
Y ou also have the option of validating your data in the back end using the following transaction:
T ransaction IE01 Create Equipment
T ransaction IE02 Change Equipment
T ransaction IE03 Display Equipment
Migration Objects for SAP S/4HANA
Available Migration Objects PUBLIC 1299Further Information
SAP Knowledge Base Article 2878950 
  – Migration Cockpit: Collective KBA for Migration PM-Object
Equipment
Available Migration Objects [page 2]  in SAP S/4HANA.
