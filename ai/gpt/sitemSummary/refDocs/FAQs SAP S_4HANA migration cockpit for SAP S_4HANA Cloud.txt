



FAQs - SAP S/4HANA Migration Cockpit for SAP S/4HANA Cloud



































Javascript must be enabled for the correct page display











Skip to Content




Call us atChina400-619-0727United States+1-800-872-1727Or see our complete list of local country numbersChat nowGet live help and chat with an SAP representative.Contact usSend us your comments, questions, or feedback.




Contact Us

Contact us



Chat now




Contact us






HomeFAQs - SAP S/4HANA Migration Cockpit for SAP S/4HANA Cloud

FAQs - SAP S/4HANA Migration Cockpit for SAP S/4HANA CloudFind out how the SAP S/4HANA Migration Cockpit Staging Tables approach can help you to migrate your data to SAP S/4HANA. This FAQ is related to SAP S/4HANA Cloud only. Read this FAQ to learn how to start and lead the migration project successfully. Download the Document8190%PUBLICSAP S/4HANA Migration Cockpit -Migrate Your Data AppMigrate Data Using Staging TablesSAP S/4HANA CloudFrequently Asked Questions (FAQs)2 / 8© 2023 SAP SE or an SAP affiliate company. All rights reserved. See Legal Notice on www.sap.com/legal-notice for use terms, disclaimers, disclosures, or restrictions related to SAP Materials for general audiences.ContentsTOP 5 Questions ................................................................................................................ 3What is the file size limit of the data migration template with the staging option? Is there anew size limit for file upload now with the new Fiori UI? ........................................................... 3How to migrate Business Partners (Customer, Supplier)? ........................................................ 3Do you have recommendation on how to set up the migration project? .................................. 3Can I migrate historical data with SAP S/4HANA migration cockpit? ....................................... 3How can I improve the performance of the data migration?...................................................... 3MIGRATE DATA USING STAGING TABLES .................................................................... 4Where can I find more information about SAP S/4HANA migration cockpit for SAP S/4HANACloud/ Migrate your data app?..................................................................................................... 4Do we need to buy a license for Migrate Data Using Staging Tables approach?..................... 4Does the sequence of the migration automatically guide you to the next migration object? . 4Can we use both files as well as staging tables for the same migration object in a singleproject? ......................................................................................................................................... 5With the data migration with staging option, is there an option to connect to a third-partysystem and load the data directly instead of using excel files? ................................................ 5Once the data uploaded data through the migration cockpit, is there any tool tovalidate/download loaded data? .................................................................................................. 5For every migration project, the system will create staging tables for every selectedmigration object. Is that correct? ................................................................................................ 5With new releases of SAP S/4HANA migration cockpit (Migrate your data app) for SAPS/4HANA Cloud, will we still be able to use the template of the migration object fromprevious releases (for example: Customer Object, Supplier object or CPM Projecttemplate)? ..................................................................................................................................... 6Technically, Migrate Data Using Staging Tables approach is used for everything. Both localand remote schema include file upload and staging tables, but in case of SAP S/4HANA,you cannot really populate the staging tables as there is no way to get to the database. Isthat correct? .................................................................................................................................. 6The local DB connection corresponds to the staging area being located locally in theS/4HANA Cloud system. Is it right to understand that for the local DB connection customerscan only use the XML or CSV File templates and nothing else? ............................................... 6What if the customer has there standalone HANA DB system, can that be connected to theremote DB connection without the SCP Staging (2Q2 Scope Item) in place? .......................... 6What can I do with the option called “Create Correction File”? ................................................ 7Where can I find news about the SAP S/4HANA migration cockpit and data migrationcontent (migration objects)? ........................................................................................................ 7What if the XML template is corrupted after inserting or deleting data? .................................. 7Where can I find information on the corresponding SAP table/technical field name for eachfield in the XML Template? ........................................................................................................... 8© 2023 SAP SE or an SAP affiliate company. All rights reserved. See Legal Notice on www.sap.com/legal-notice for use terms, disclaimers, disclosures, or restrictions related to SAP Materials for general audiences.TOP 5 QuestionsWhat is the file size limit of the data migration template with the stagingoption? Is there a new size limit for file upload now with the new Fiori UI?For SAP S/4HANA Cloud the default size limit for each uploaded XML or CSV file is 100MB. If required, youcan zip several files together. The combined size of all the XML or CSV files to be added to the zip file mustnot exceed 160MB. Please note that with the CSV file format you can save more data than with XML due toits structure.The size limit of files is a technical limit and cannot be influenced by the SAP S/4HANA migration cockpit (i.e.the limitation comes with the http requests and not with migration cockpit).With the Migrate your data app (Fiori UI), you should always take into consideration that the records uploadedvia XML Excel templates are available in the staging tables and not within separate cluster tables for each fileanymore. However, users now have the possibility to upload several files into one staging table – and fromthere on all data is processed.For more details see:• KBA 2719524 - SAP S/4HANA Migration Cockpit: XML template size limits• Additional Information About Migration TemplatesHow to migrate Business Partners (Customer, Supplier)?Business Partner is a leading object in SAP S/4HANA, all customers and suppliers should be migrated asBusiness partners. Firstly migrate data for the Customer migration object. Use the migration object Supplier –extend existing record by new org levels to add the supplier dataThis process is described in KBA 2748819 – Migrate Business Partners with Supplier and Customer rolesIn case of any issues, refer to KBA 2848224 - Migration Cockpit: Collective KBA for Business Partner(Customer, Supplier)Do you have recommendation on how to set up the migration project?There is a list of recommendations of how you can set up your project in our overview presentation.Can I migrate historical data with SAP S/4HANA migration cockpit?No. Migration cockpit is the tool for the new implementation which implies the initial data load. The onlymigration object which is available as part of migration cockpit is FI – Historical balance migration object seeSAP Note 2943035. The individual customer-specific evaluation makes sense. Solutions like BusinessWarehouse or side-by-side extensibility can be considered.How can I improve the performance of the data migration?Check the recommendations about performance improvement in our overview presentation.You can also adjust the maximum number of background jobs that are used for your project on the JobManagement screen. The default number of background jobs for a project is 8. By default, the jobs aredistributed equally among the migration objects. If required, you can also adjust the number of backgroundjobs for specific migration objects. See SAP Help Portal: Job Management.© 2023 SAP SE or an SAP affiliate company. All rights reserved. See Legal Notice on www.sap.com/legal-notice for use terms, disclaimers, disclosures, or restrictions related to SAP Materials for general audiences.MIGRATE DATA USING STAGING TABLESWhere can I find more information about SAP S/4HANA migration cockpitfor SAP S/4HANA Cloud/ Migrate your data app?• SAP S/4HANA Cloud Data Migration Landing page: http://help.sap.com/S4_CE_DM• SAP S/4HANA Migration Cockpit Community: https://community.sap.com/topics/s4hana-migration-cockpitThere are also some SAP Notes and KBAs:• Collective SAP Note and FAQ for SAP S/4HANA Migration Cockpit (Cloud) 2538700• w/ SAP S/4HANA Migration Cockpit (Cloud) - Sample data migration templates 2470789The external slide deck which can be shared to customers:• https://www.sap.com/documents/2020/07/7a263781-a67d-0010-87a3-c30de2ffd8ff.htmlAlso, customers can find publicly available information on the SAP Help Portal:• Migrate Your Data - Migration Cockpit• Video Library for Data MigrationUsers can get an introduction to data migration with SAP S/4HANA. A one-time registration in thehttp://www.sap.com/account.html is required:• Introducing the SAP S/4HANA Migration CockpitDo we need to buy a license for Migrate Data Using Staging Tablesapproach?If you use the database connection option “Local SAP S/4HANA Database Schema” when creating the project,no additional license or subscription is needed.If you use the remote SAP HANA database schema, you need to set up a secondary database connection inyour SCP account and HANA DBaaS, for which additional costs may apply. You can find more details inKBA 2733253. For more information about additional costs that might apply or information about licensecosts for the use of SAP ETL tools, please contact your sales contact or customer care representative.Does the sequence of the migration automatically guide you to the nextmigration object?No – the customer decides which migration object will be next. The SAP S/4HANA migration cockpit providesonly information about predecessor and successor objects. The information about predecessor and successormigration objects is also available in the SAP Help Portal:Short link for SAP S/4HANA Cloud:• http://help.sap.com/S4_CE_MOWhen you create the migration project and add the object (for example, Product), the migration cockpitproposes you to add the predecessor objects to the project.© 2023 SAP SE or an SAP affiliate company. All rights reserved. See Legal Notice on www.sap.com/legal-notice for use terms, disclaimers, disclosures, or restrictions related to SAP Materials for general audiences.Can we use both files as well as staging tables for the same migrationobject in a single project?Yes. With the Fiori UI, users can use XML/CSV files and other ETL Tools (from third-party or from SAP) toupload data. The data is stored in staging tables as a container. The standard case will be using files with thelocal database connection.With the data migration with staging option, is there an option to connectto a third-party system and load the data directly instead of using excelfiles?Connecting a third-party system directly to the local SAP HANA Database is legally not allowed. Nevertheless,by choosing remote SAP HANA Database Schema, you can use an SAP HANA tenant/schema on SAP BTP(Business Technology Platform), where you can have more options to load data into the staging tables.Once the data uploaded data through the migration cockpit, is there anytool to validate/download loaded data?After migrating the data, it is the customers’ responsibility to validate the data. This is an important topic in theoverall migration project plan. You can check the migration results in the Migration Object Instances view anddownload them. You can also cAdditionally, you will find some information in the logs (e.g. number of successfully migrated instances)For every migration project, the system will create staging tables forevery selected migration object. Is that correct?Yes. This is done automatically by the SAP S/4HANA migration cockpit.







Cookie Preferences
Is this information helpful?



Quick LinksSustainability ManagementSmall and Midsize EnterprisesSAP Trust CenterSAP InsightsSAP CommunityDeveloperSupport PortalAbout SAPCompany InformationWorldwide DirectoryInvestor RelationsCareersNews and PressEventsCustomer StoriesNewsletterSite InformationPrivacyTerms of UseLegal DisclosureCopyrightTrademarkSitemapText ViewCookie PreferencesContact usChina400-619-0727United States+1-800-872-1727Or see our complete list of local country numbersContact usChat nowFind us on










Back to top








































