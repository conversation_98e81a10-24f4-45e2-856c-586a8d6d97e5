



















































Floating-point arithmetic may give inaccurate result in Excel - Microsoft 365 Apps | Microsoft Learn













Skip to main content








 

                We use optional cookies to improve your experience on our websites, such as through social media connections, and to display personalized advertising based on your online activity. If you reject optional cookies, only cookies necessary to provide you the services will be used. You may change your selection by clicking “Manage Cookies” at the bottom of the page. Privacy Statement Third-Party Cookies



Accept
Reject
Manage cookies




This browser is no longer supported.
Upgrade to Microsoft Edge to take advantage of the latest features, security updates, and technical support.


Download Microsoft Edge					

More info about Internet Explorer and Microsoft Edge					




















Learn















Discover












								Documentation
							
In-depth articles on Microsoft developer tools and technologies








								Training
							
Personalized learning paths and courses








								Credentials
							
Globally recognized, industry-endorsed credentials








								Q&A
							
Technical questions and answers moderated by Microsoft








								Code Samples
							
Code sample library for Microsoft developer tools and technologies








								Assessments
							
Interactive, curated guidance and recommendations








								Shows
							
Thousands of hours of original programming from Microsoft experts









Featured assessment

							It's your AI learning journey
						
Wherever you are in your AI journey, Microsoft Learn meets you where you are and helps you deepen your skills.










Product documentation










						ASP.NET
					




						Azure
					




						Dynamics 365
					




						Microsoft 365
					




						Microsoft Edge
					




						Microsoft Entra
					




						Microsoft Graph
					




						Microsoft Intune
					




						Microsoft Purview
					




						Microsoft Teams
					




						.NET
					




						Power Apps
					




						Power Automate
					




						Power BI
					




						Power Platform
					




						PowerShell
					




						SQL
					




						Sysinternals
					




						Visual Studio
					




						Windows
					




						Windows Server
					




				View all products
			




Featured assessment

							It's your AI learning journey
						
Wherever you are in your AI journey, Microsoft Learn meets you where you are and helps you deepen your skills.










Development languages










						C++
					




						DAX
					




						Java
					




						OData
					




						OpenAPI
					




						Power Query M
					




						VBA
					







Featured assessment

							It's your AI learning journey
						
Wherever you are in your AI journey, Microsoft Learn meets you where you are and helps you deepen your skills.










Topics










						Artificial intelligence
					




						Compliance
					




						DevOps
					




						Platform engineering
					




						Security
					







Featured assessment

							It's your AI learning journey
						
Wherever you are in your AI journey, Microsoft Learn meets you where you are and helps you deepen your skills.
























					Suggestions will filter as you type
				
























			Sign in
		





















							Profile
						



							Settings
						




						Sign out
					















Learn



















					Suggestions will filter as you type
				























			Sign in
		





















							Profile
						



							Settings
						




						Sign out
					







 













Learn

















Learn





Documentation





Training





Credentials





Q&A





Code Samples





Assessments





Shows





More








						Documentation
					




						Training
					




						Credentials
					




						Q&A
					




						Code Samples
					




						Assessments
					




						Shows
					















					Suggestions will filter as you type
				




















					Suggestions will filter as you type
				












Search







 Sign in 
























Profile


Settings



Sign out





 


				Microsoft 365
				
			






Solutions and architecture








						Get started
					




						Set up your infrastructure for hybrid work
					




						Set up secure collaboration
					




						Deploy threat protection
					




						Data privacy and data protection
					




						Microsoft 365 for smaller businesses and campaigns
					







Apps and services








						Microsoft Teams
					




						Microsoft 365 admin center
					




						Microsoft 365 Apps
					




						Microsoft Purview
					




						Microsoft 365 security
					




						SharePoint
					




						OneDrive
					




						All apps and services
					







Training








						Training for IT Pros
					




						Microsoft 365 certifications
					




						Microsoft 365 learning pathways
					







Resources








						Microsoft 365 support
					




						FastTrack
					




						Troubleshooting
					




						Microsoft 365 tech community
					




						Resources for developers
					







More








Solutions and architecture








						Get started
					




						Set up your infrastructure for hybrid work
					




						Set up secure collaboration
					




						Deploy threat protection
					




						Data privacy and data protection
					




						Microsoft 365 for smaller businesses and campaigns
					







Apps and services








						Microsoft Teams
					




						Microsoft 365 admin center
					




						Microsoft 365 Apps
					




						Microsoft Purview
					




						Microsoft 365 security
					




						SharePoint
					




						OneDrive
					




						All apps and services
					







Training








						Training for IT Pros
					




						Microsoft 365 certifications
					




						Microsoft 365 learning pathways
					







Resources








						Microsoft 365 support
					




						FastTrack
					




						Troubleshooting
					




						Microsoft 365 tech community
					




						Resources for developers
					














				Free Account
			


 






Table of contents						



Exit focus mode










Search







					Suggestions will filter as you type
				



Office Products TroubleshootingAccessActivationEnd of SupportExcel.SpecialCells(xlCellTypeBlanks) VBA function doesn't workA formula returns "#VALUE!" ErrorAccess data connections are slow to refreshAlgorithm is used by the XIRR() functionAn active process continues to runApply permissions to separate rangesAutoComplete may not workAutoRecover functions in ExcelBlank pages are unexpectedly printedBorder missing when pasting table in email for MacCalculate ages before 1/1/1900Calculate interpolation step valueCan't add refedit control to VBA userformCan't create a PivotTable in Excel 2013Can't export to Excel from SharePoint OnlineCan't modify oData connection in PowerPivotCan't open IRM protected workbookCan't paste any attributes into a workbook in another instanceCan't remove error tracing arrowCan't use object linking and embeddingCenter Across Columns does not appear on the toolbarChart trendline formula is inaccurateCHIINV statistical functions"Class not registered" error when updating PowerPivot dataClean up workbooks with less memoryColumns and rows are labeled numericallyCOM add-ins are not displayedCommand bars of add-ins aren't displayed or removedCommand can't be used on multiple selectionsConditional formatting is set incorrectly with VBAConnection to ODBC driver fails in PowerPivotConvert degrees/minutes/seconds anglesConvert Excel column numbersCreate a dynamic defined rangeCreate a monthly calendarCreate and user two-input data tablesCreate application-level event handlersCustom function may not calculate expected valueData collected by Excel Baseline DiagnosticExcel data doesn't retain formatting in mail mergeExcel workbook shows old property valuesDate format is incorrect when converting a CSV text file with macrosDelete cells with a "For Each...Next" loopDelete every other row on a worksheetDetermine the current mode of calculationDetermine whether a year is a leap yearDifference between STDEVPA and STDEVP functionsDifferences between 1900 and 1904 date systemDigital signatures and code signingDisplay more digits in trendline equation coefficientsDsum and Dcount functions not workingEmbedded macros are blocked from runningError when saving a fileError when sending commands in ExcelError when you copy formulas over large areaError when you import data to a workbookExcel 2016 for Mac crashesExcel cannot complete this taskExcel doesn't recognize data as Date type dataExcel doesn't shut down after calling Quit methodExcel incorrectly assumes 1900 is a leap yearExcel opening blankExcel stops working after ********* is installedExcel takes long time to insert or delete rowsExport a text file with both comma and quote delimitersExternal links may be calculated when you open a workbookF4 keyboard shortcut doesn't work in ExcelFALSE result with the ISBLANK() functionFile updated without user interactionFirst sheet name displays different languageFloating-point arithmetic gives inaccurate resultsForce Excel to open in a new instance by defaultFormat Cells settingsFormatted text is limited to 240 characters per lineFormula errors when list separator isn't setFormulas to count text occurrences in ExcelGROWTH statistical functionsHeavy Excel workloads run slowly in multi-session environmentsHide sheets and use xlVeryHidden constant in a macroHow column widths are determinedHow Excel works with two-digit year numbersHow to obtain the latest Excel ViewerHyperlink to mht file fails to loadInquire COM add-in options don't respondINTERCEPT statistical functionsInvalid signature error in Excel workbooksIssue when saving Excel workbooksIssue when sorting a range that has merged cellsIssue when starting Excel for MacIssue when updating external linksIssue with updating a data connection or change PivotTable fieldsIssue with using different print quality for sheetsLarge Address Aware capability changeLast digits are changed to zerosLegacy UML features are missingLimitations for working with arraysLink a Visio drawing to a specific regionLOGEST statistical functionsLong numbers are displayed incorrectlyLoop through a list of data by using macrosMacro to extract data from a chartMacros run slowlyMake Paste Options button disappearMargins do not fit page size when printingMemory usage in the 32-bit edition of ExcelMisleading labels in the output of Analysis ToolPak t-Test toolsNetwork Mapped Drive hyperlinks resolve as UNCObject Model calls fails from WorkbookOpen eventMicrosoft 365 users can't open or synchronize filesOperation Must Use an Updatable QueryParts of Excel turn white or grayPaster special option is missing or not workingPercentage format changes to NumericPowerPivot controls disabled with non-default Excel file formatPrevent files from opening automaticallyProgrammatically copy all range namesProgrammatically print all embedded chartsProgrammatically save a file to a network driveQuery and update data with ADO from ASPRecalculation with a SUMIF formula takes a longer timeRegistry keys that control the File Repair featureRepresenting ties by using RANKRSQ statistical functionsRun a macro with certain cells changeRun-time Error 1004 with legend entries in chartsRun-time error '-2147467259 (80004005)' when setting a chart propertySample macro to insert/delete rows or columnsSample user-defined function to hide formula errorsSAP add-ins don't display controls after update to version 1806Security settings for Dynamic Data ExchangeSet page setup attributes for sheetsSet up and use the RTD functionStartup message about updating linked workbooksSTEYX statistical functionsToo many different cell formats in ExcelTransfer data to Excel from Visual BasicTroubleshoot available resources issuesTurn off Function Argument ToolTipsUnable to cast COM objectUse a worksheet function in a VB macroUse defined names to update a chart rangeUse left, right, mid, and len functions in VBUse logical AND or OR in a SUM+IF statementUse macro to apply cell shading format to other rowsUse OnEntry macro to create a running total in cell commentUse saved property to determine if workbook is changedUse shared workbooks with different versionsUse startup foldersUser info in @mentions doesn't resolveVBA writes to cells slowly when ActiveX controls are invisibleWorkbook isn't activated when you run a macroWorkbooks not openWrap text doesn't adjust row heightInfoPathInstallationMobileOffice for MacOffice Suite IssuesOneNotePerformancePlannerPowerPointProjectPublisherSettingsThird party add-insVisioWordOffice Developer



Download PDF























										Learn
									




										Microsoft 365
									




										Troubleshooting
									











Learn



Microsoft 365



Troubleshooting










Read in English





Add












Table of contents





Read in English





Add





Print






Twitter





LinkedIn





Facebook





Email












Table of contents




Floating-point arithmetic may give inaccurate results in Excel




Article														

07/22/2022



																	3 contributors
															



Applies to:
Excel 2010, Excel 2013, Excel for Microsoft 365, Microsoft Excel for Mac 2011, Excel for Mac for Microsoft 365









Feedback





In this article



Summary



More Information





Summary
This article discusses how Microsoft Excel stores and calculates floating-point numbers. This may affect the results of some numbers or formulas because of rounding or data truncation.
Overview
Microsoft Excel was designed around the IEEE 754 specification to determine how it stores and calculates floating-point numbers. IEEE is the Institute of Electrical and Electronics Engineers, an international body that, among other things, determines standards for computer software and hardware. The 754 specification is a very widely adopted specification that describes how floating-point numbers should be stored in a binary computer. It is popular because it allows floating-point numbers to be stored in a reasonable amount of space and calculations to occur relatively quickly. The 754 standard is used in the floating-point units and numeric data processors of nearly all of today's PC-based microprocessors that implement floating-point math, including the Intel, Motorola, Sun, and MIPS processors.
When numbers are stored, a corresponding binary number can represent every number or fractional number. For example, the fraction 1/10 can be represented in a decimal number system as 0.1. However, the same number in binary format becomes the following repeating binary decimal:
0001100110011100110011 (and so on)
This can be infinitely repeated. This number cannot be represented in a finite (limited) amount of space. Therefore, this number is rounded down by approximately -2.8E-17 when it is stored.
However, there are some limitations of the IEEE 754 specification that fall into three general categories:

Maximum/minimum limitations
Precision
Repeating binary numbers

More Information
Maximum/Minimum Limitations
All computers have a maximum and a minimum number that can be handled. Because the number of bits of memory in which the number is stored is finite, it follows that the maximum or minimum number that can be stored is also finite. For Excel, the maximum number that can be stored is 1.79769313486232E+308 and the minimum positive number that can be stored is 2.2250738585072E-308.
Cases in which we adhere to IEEE 754

Underflow: Underflow occurs when a number is generated that is too small to be represented. In IEEE and Excel, the result is 0 (with the exception that IEEE has a concept of -0, and Excel does not).
Overflow: Overflow occurs when a number is too large to be represented. Excel uses its own special representation for this case (#NUM!).

Cases in which we do not adhere to IEEE 754

Denormalized numbers: A denormalized number is indicated by an exponent of 0. In that case, the entire number is stored in the mantissa and the mantissa has no implicit leading 1. As a result, you lose precision, and the smaller the number, the more precision is lost. Numbers at the small end of this range have only one digit of precision.
Example: A normalized number has an implicit leading 1. For instance, if the mantissa represents 0011001, the normalized number becomes 10011001 because of the implied leading 1. A denormalized number does not have an implicit leading one, so in our example of 0011001, the denormalized number remains the same. In this case, the normalized number has eight significant digits (10011001) while the denormalized number has five significant digits (11001) with leading zeroes being insignificant.
Denormalized numbers are basically a workaround to allow numbers smaller than the normal lower limit to be stored. Microsoft does not implement this optional portion of the specification because denormalized numbers by their very nature have a variable number of significant digits. This can allow significant error to enter into calculations.

Positive/Negative Infinities: Infinities occur when you divide by 0. Excel does not support infinities, rather, it gives a #DIV/0! error in these cases.

Not-a-Number (NaN): NaN is used to represent invalid operations (such as infinity/infinity, infinity-infinity, or the square root of -1). NaNs allow a program to continue past an invalid operation. Excel instead immediately generates an error such as #NUM! or #DIV/0!.


Precision
A floating-point number is stored in binary in three parts within a 65-bit range: the sign, the exponent, and the mantissa.
 



Expand table



The sign
The exponent
The mantissa




1 sign bit
11 bits exponent
1 implied bit + 52 bits fraction



The sign stores the sign of the number (positive or negative), the exponent stores the power of 2 to which the number is raised or lowered (the maximum/minimum power of 2 is +1,023 and -1,022), and the mantissa stores the actual number. The finite storage area for the mantissa limits how close two adjacent floating point numbers can be (that is, the precision).
The mantissa and the exponent are both stored as separate components. As a result, the amount of precision possible may vary depending on the size of the number (the mantissa) being manipulated. In the case of Excel, although Excel can store numbers from 1.79769313486232E308 to 2.2250738585072E-308, it can only do so within 15 digits of precision. This limitation is a direct result of strictly following the IEEE 754 specification and is not a limitation of Excel. This level of precision is found in other spreadsheet programs as well.
Floating-point numbers are represented in the following form, where exponent is the binary exponent:
X = Fraction * 2^(exponent - bias)
Fraction is the normalized fractional part of the number, normalized because the exponent is adjusted so that the leading bit is always a 1. This way, it does not have to be stored, and you get one more bit of precision. This is why there is an implied bit. This is similar to scientific notation, where you manipulate the exponent to have one digit to the left of the decimal point; except in binary, you can always manipulate the exponent so that the first bit is a 1, because there are only 1s and 0s.
Bias is the bias value used to avoid having to store negative exponents. The bias for single-precision numbers is 127 and 1,023 (decimal) for double-precision numbers. Excel stores numbers using double-precision.
Example using very large numbers
Enter the following into a new workbook:
adoc




Copy






A1: 1.2E+200
B1: 1E+100
C1: =A1+B1 

The resulting value in cell C1 would be 1.2E+200, the same value as cell A1. In fact if you compare cells A1 and C1 using the IF function, for example IF(A1=C1), the result will be TRUE. This is caused by the IEEE specification of storing only 15 significant digits of precision. To be able to store the calculation above, Excel would require at least 100 digits of precision.
Example using very small numbers
Enter the following into a new workbook:
adoc




Copy






A1: 0.000123456789012345
B1: 1
C1: =A1+B1 

The resulting value in cell C1 would be 1.00012345678901 instead of 1.000123456789012345. This is caused by the IEEE specification of storing only 15 significant digits of precision. To be able to store the calculation above, Excel would require at least 19 digits of precision.
Correcting precision errors
Excel offers two basic methods to compensate for rounding errors: the ROUND function and the Precision as displayed or
Set precision as displayed workbook option.
Method 1: The ROUND function
Using the previous data, the following example uses the ROUND function to force a number to five digits. This lets you successfully compare the result to another value.
adoc




Copy






A1: 1.2E+200
B1: 1E+100
C1: =ROUND(A1+B1,5) 

This results in 1.2E+200.
D1: =IF(C1=1.2E+200, TRUE, FALSE)
This results in the value TRUE.
Method 2: Precision as displayed
In some cases, you may be able to prevent rounding errors from affecting your work by using the Precision as displayed option. This option forces the value of each number in the worksheet to be the displayed value. To turn on this option, follow these steps.

On the File menu, click Options, and then click the Advanced category.
In the When calculating this workbook section, select the workbook that you want, and then select the Set precision as displayed check box.

For example, if you choose a number format that shows two decimal places, and then you turn on the Precision as displayed option, all accuracy beyond two decimal places is lost when you save your workbook. This option affects the active workbook including all worksheets. You cannot undo this option and recover the lost data. We recommend that you save your workbook before you enable this option.
Repeating binary numbers and calculations that have near-zero results
Another confusing problem that affects the storage of floating point numbers in binary format is that some numbers that are finite, non-repeating numbers in decimal base 10, are infinite, repeating numbers in binary. The most common example of this is the value 0.1 and its variations. Although these numbers can be represented perfectly in base 10, the same number in binary format becomes the following repeating binary number when it is stored in the mantissa:
000110011001100110011 (and so on)
The IEEE 754 specification makes no special allowance for any number. It stores what it can in the mantissa and truncates the rest. This results in an error of about -2.8E-17, or 0.000000000000000028 when it is stored.
Even common decimal fractions, such as decimal 0.0001, cannot be represented exactly in binary. (0.0001 is a repeating binary fraction that has a period of 104 bits). This is similar to why the fraction 1/3 cannot be exactly represented in decimal (a repeating 0.33333333333333333333).
For example, consider the following simple example in Microsoft Visual Basic for Applications:
VB




Copy






   Sub Main()
      MySum = 0
      For I% = 1 To 10000
         MySum = MySum + 0.0001
      Next I%
      Debug.Print MySum
   End Sub

This will PRINT 0.999999999999996 as output. The small error in representing 0.0001 in binary propagates to the sum.
Example: Adding a negative number

Enter the following into a new workbook:
A1: =(43.1-43.2)+1

Right-click cell A1, and then click Format Cells. On the Number tab, click Scientific under Category. Set the Decimal places to 15.


Instead of displaying 0.9, Excel displays 0.899999999999999. Because (43.1-43.2) is calculated first, -0.1 is stored temporarily and the error from storing -0.1 is introduced into the calculation.
Example when a value reaches zero

In Excel 95 or earlier, enter the following into a new workbook:
A1: =1.333******-1.333-1.225

Right-click cell A1, and then click Format Cells. On the Number tab, click Scientific under Category. Set the Decimal places to 15.


Instead of displaying 0, Excel 95 displays -2.22044604925031E-16.
Excel 97, however, introduced an optimization that attempts to correct for this problem. Should an addition or subtraction operation result in a value at or very close to zero, Excel 97 and later will compensate for any error introduced as a result of converting an operand to and from binary. The example above when performed in Excel 97 and later correctly displays 0 or 0.000000000000000E+00 in scientific notation.
For more information about floating-point numbers and the IEEE 754 specification, please see the following World Wide Web sites:

https://www.ieee.org
https://steve.hollasch.net/cgindex/coding/ieeefloat.html











Feedback

Was this page helpful?





Yes





No






Provide product feedback





Feedback





Coming soon: Throughout 2024 we will be phasing out GitHub Issues as the feedback mechanism for content and replacing it with a new feedback system. For more information see: https://aka.ms/ContentUserFeedback.		
Submit and view feedback for


This product





This page






View all page feedback








Additional resources












English (United States)



California Consumer Privacy Act (CCPA) Opt-Out Icon





Your Privacy Choices








Theme























Light					



















Dark					



















High contrast					








Manage cookies
Previous Versions
Blog
Contribute
Privacy
Terms of Use
Trademarks
© Microsoft 2024






Additional resources




In this article






















English (United States)



California Consumer Privacy Act (CCPA) Opt-Out Icon





Your Privacy Choices








Theme























Light					



















Dark					



















High contrast					








Manage cookies
Previous Versions
Blog
Contribute
Privacy
Terms of Use
Trademarks
© Microsoft 2024




