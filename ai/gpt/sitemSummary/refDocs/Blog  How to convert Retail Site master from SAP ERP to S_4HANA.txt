

	How to convert Retail Site master from SAP ERP to ... - SAP Community











































SAP Community







Products and Technology







Enterprise Resource Planning







ERP Blogs by SAP







How to convert Retail Site master from SAP ERP to ...














    	Enterprise Resource Planning Blogs by SAP
    

    	Get insights and updates about cloud ERP and RISE with SAP, SAP S/4HANA and SAP S/4HANA Cloud, and more enterprise management capabilities with SAP blog posts.
    

















All communityThis categoryBlogKnowledge baseUsersManaged tags




Enter a search wordTurn off suggestions
Enter a search wordTurn off suggestions
Enter a user name or rankTurn off suggestions
Enter a search wordTurn off suggestions
Enter a search wordTurn off suggestions



cancel



Turn on suggestions







		Showing results for 



			Search instead for 



		Did you mean: 
























 
 















							How to convert Retail Site master from SAP ERP to S/4HANA
							
						






















former_member608371


			Explorer
		






Options



Subscribe to RSS Feed




Mark as New
Mark as Read




Bookmark
Subscribe




Printer Friendly Page
Report Inappropriate Content









‎05-21-2019
9:34 AM













	
			8
		

	Kudos












			
				
					
					
						When you are doing a system conversion to SAP S/4HANA, and you are using sites in your source system. You may face to the questions like, how do site master collaborate with BP master in S/4HANA? What are the necessary steps for the migration of site master data from SAP ERP to SAP S/4HANA? Any restrictions for the site master conversion procedure?

 
Harmonization of Site Master and Business Partner
With those questions, let’s start from harmonization of site master and business partner.

In SAP S/4HANA, the business partner (BP) is the single point of entry used to centrally managing master data for business partners, customers (C), and vendors (V), unlike in SAP ERP, where C/V master data can be handled separately. The specific transaction codes used to maintain C/V master data separately in SAP Business Suite are not available on SAP S/4HANA. Note 2265093 S4TWL - Business Partner Approach.

The site master is affected by this data model harmonization because a site always has the role of a customer and can optionally also have the role of a vendor. It means a customer master record and optionally a vendor master record is assigned to a site.

The integration of C/Vs with BP data (by means of CVI) is not supported on SAP ERP for C/Vs assigned to a site, but it is mandatory on SAP S/4HANA. Integration requires migration of C/Vs assigned to a site during the SAP S/4HANA conversion where BPs are created for these C/Vs. This step is illustrated in the following picture.



As a result of the business partner approach, some site-related processes are different since SAP S/4HANA 1610 and above compared to SAP ERP:

Maintenance of site master

Maintenance of BP (C/V) data via central BP maintenance.
BP maintenance integrated in site maintenance dialog.
Pre-selection of BP roles relevant for site.
Elimination of redundancies between site and C/V (e.g. address data, departments, receiving points).


Distribution of site master

Data Replication Framework (DRF) as common framework for data distribution (BP and site).
DRF allows for new distribution scenarios (direct and pooled distribution) and filter settings besides manual distribution.


Archiving of site master

Archiving marker for site includes archiving marker for BP (C/V).
Archiving of BP only possible after archiving of site.



 
Site Master Conversion to S/4HANA
Retail site master conversion is only possible to S/4HANA OP 1610 or above. When you are ready to conduct site conversion to S/4HANA, there are three phases during the whole procedure: Preparing for conversion, Conversion, Preparing operations after conversion.

 

Preparing for conversion:

BP Conversion and Preparation for Conversion to SAP S/4HANA: Although the creation of a leading BP for the C/V must be performed before the actual SAP S/4HANA conversion. C/Vs assigned to sites are excluded from this step. No BPs will be created for C/Vs assigned to sites.

For more information about BP conversion and the BP pre-check, please see the central consulting SAP Note 2265093.


Preparation for Site migration.

Before conversion to S/4HANA take place, we recommend a cleanup of site data and of Customizing related to sites and the C/Vs assigned to sites.
 
Second, the following (Customizing) activities are required in preparation for the migration of site data to the BP approach(Configuration details refer to XXXX_v2.1_SiteMasterConversionGuide.pdf in SAP Note 2310884😞




Activate CVI synchronization options.
Activate PPO requests and creation of post processing orders.
Define BP number ranges and groupings.
Define number assignment C/V to BP.
Define BP role for direction C/V to BP and BP role category for direction BP to C/V.
Maintain field assignment for C/V integration (if relevant data is used for C/V assigned to sites).
Implement BAdIs (if required or desired): Adjust BP number of new BPs created for C/Vs assigned to sites (BADI_CHANGE_BP_NUMBER, method CHANGE_BP_NUMBER). Adjust ID of new BP role for site (BADI_CHANGE_BP_NUMBER, method CHANGE_BP_ROLE_NAME_BPSITE).



Third, If a site has both a customer and supplier role, one common BP is created for the C and V assigned to the site. In order to create a BP with central data shared between the C and V role, there must be no conflicts in this data between an existing C and V that are assigned to the same site. If conflicts exist, the data must be consolidated prior to BP creation during conversion to SAP S/4HANA in order to avoid data loss or issues with BP creation. Table in document XXXX_v2.1_SiteMasterConversionGuide.pdf in SAP Note 2310884 describes the potential conflicts that need to be resolved prior to SAP S/4HANA conversion.
 
Fourth, a mandatory pre-check is performed at the beginning of the SAP S/4HANA conversion process in order to check whether the prerequisites for the site migration to BP are fulfilled.
For information about the messages issued during the pre-check, see the XXXX_Table_Pre-Checks.pdf, which is also attached to SAP Note 2310884.
The checks of the pre-check for site migration are only performed for C/Vs assigned to sites and only for other entities that are relevant for these C/Vs, such as C/V account groups. The relevant SAP Notes for executing the pre-check by using the preparation report are:




SAP Notes with pre-checks and preparation report that are only relevant for the conversion target release SAP S/4HANA 1610 using the previous pre-check framework: SAP Note 2310871 / SAP Note 2336853
SAP Note with pre-checks and preparation report that are relevant for the conversion target releases SAP S/4HANA 1709 and higher, using the new Simplification Item Check (SIC) framework: SAP Note 2502552
The following SAP Notes are only required for the implementation of the BAdIs that are called during the pre-check and during the migration to rename BPs and new BP roles: SAP Note 2315639



SAP Notes for the implementation of methods that call the BAdI methods:  SAP Note 2348529 / SAP Note 2456668




SAP Note 2310884: Consulting SAP Note for the pre-check



 
Last, The CVI Customizing check report CVI_UPGRADE_CHECK_RESOLVE checks the necessary CVI and BP Customizing settings for each client and proposes a solution for missing or wrong Customizing entries. You can use this report to check the Customizing settings for C/Vs assigned to Retail sites when called using transaction code SM_CVI_FS_CHECK_CUS.
SAP Note 2451504 / SAP Note 2344034 / SAP Note 2265093
*Since Mar 2020, the customizing check report CVI_UPGRADE_CHECK_RESOLVE has been replace by new report CVI_CUSTOMIZING_RESOLVE. SAP note 2891522
 

Conversion

Before the technical conversion process starts, a mandatory pre-check is performed on the source release to check whether the prerequisites for the site migration to the BP approach are fulfilled. This pre-check should already have been executed during the conversion preparation phase to facilitate conversion preparation. SAP Note 2185960

If Retail sites have been created in the system, conversion is only possible to SAP S/4HANA 1610 or higher.
 

XPRA Stands for eXecution of PRogram After import. During the conversion phase, XPRAs are executed to convert data from the source release to a state required in the target release on all clients. The following XPRAs are relevant for the site.

The XPRA converts existing site profiles (table TWRF2): WFIL_XPRA_S4MIG_TWRF2
The XPRA converts the tables for site transport (table SITE_DISTR_TAB): WFIL_XPRA_S4_ADJUST_DISTR_TABS
 

During the conversion phase, a site migration report is executed to create BPs for C/Vs assigned to sites and for the contact persons of these C/Vs on all clients. SAP Note 2429142

BP Naming: determine the number of the new BP created for each site.
BP Role for Site: A new BP role with role category BPSITE is created by the migration report on each client.
Contact person: During site migration, for each contact person of a C/V, such a BP contact person is created and assigned to the new BP. SAP Note 2366114 / 2371673
Address Data: The migration report creates a separate address data record for the V assigned to a site. The site itself still shares the address data record with the C assigned to it on SAP S/4HANA like in SAP ERP (that is, T001W-ADRNR and KNA1-ADRNR are the same)



Preparing Operations after Conversion

Resolving Site Conversion Issues. We recommend that you perform the following activities after conversion in order to check that BPs have been created successfully:

Check the migration log. Displaying the log by using report UPGBA_DISPLAY_REPORT_LOG with log ID MIGRATION_CV_BP
Run the check report for site migration. Report PERFORM_CHK_SMBP_FOR_S4_SIC or PERFORM_CHK_SMBP_FOR_S4
Analyze the reason for reported issues and fixed them, then start the conversion of sites with missing BPs again.
Convert the sites with missing BPs by using migration report WFIL_CSR_S4MIG_CV_BP.
Check the migration log again and run the check report for site migration again.


Additional Customizing for site

Site Profiles
BP Role Groupings



 

Conversion of Distributed System Landscapes

If site data is distributed within a system landscape, that is, the same sites exist on several systems or clients and the site data is kept consistent between these systems or clients via data distribution, we recommend that you ensure that BPs with same numbers are created for the same sites on the individual systems or clients.
In order to support phased conversion of an existing system landscape to SAP S/4HANA, the distribution of site data is also supported in a mixed landscape with both SAP ERP and SAP S/4HANA systems.

 
Post-Conversion Operations
Once you have converted site master data to S/4HANA, there are some changes to the sites as a result of the harmonization of the data model, like customizing, site maintenance, BP maintenance, Site Distribution, and Site Archiving.

 

Customizing:

Site Profile
Enable Standard Customer/Vendor as a Source for Site Update
Maintain Tables for Plant Transport
On SAP S/4HANA, departments are handled as part of the C role of the BP
Customizing activities for the Data Replication Framework (DRF) are required for the distribution of sites on SAP S/4HANA
New delivery Customizing has been introduced and the existing delivery Customizing has been adjusted with regard to the site master due to the introduction of the BP approach.




Site Maintenance

The C/V buttons integrated in site maintenance are replaced by one BP button. A BP with the required C/V roles according to the site profile must be assigned to a site before the site can be saved. The BP must also have a role with role category BPSITE assigned to it.
Since the address data (valid for the site with BP/C/V) is maintained with the BP as leading entity on SAP S/4HANA, the Address tab in the site maintenance as available in SAP ERP is no longer available with SAP S/4HANA.
Departments (table WRF4) and receiving points (table WRF12) are only handled via the C role of the BP on SAP S/4HANA. Therefore, it is not possible to maintain departments and receiving points directly from the site maintenance and the corresponding buttons in the task bar have been removed. SAP Note 2374568 / 2387393
The maintenance of additional addresses and geolocation data (table WFIL_ADD_ADR) for a site is not supported on SAP S/4HANA. SAP Note 2391784




BP Maintenance

The display and maintenance of BPs assigned to sites is possible in the BP maintenance dialog integrated in the site maintenance and in the stand-alone BP maintenance dialog (transaction BP) on SAP S/4HANA.
The BP allows the maintenance of multiple addresses, address usages and time-dependency for both addresses and address usages.
The enhancement concept for C/V available in SAP ERP is not supported with the BP maintenance on SAP S/4HANA.



 

Site Distribution

The site distribution in SAP ERP by means of transaction WBTIMEX, which provides site transfer in push or pull mode, is replaced by the distribution of sites using the Data Replication Framework (DRF) on SAP S/4HANA, which provides push transfer only. Transactions WBTIMEX and WBTIMEX_CUST are not available with SAP S/4HANA.
DRF provides automatic distribution mode and manual distribution mode. Note 2472030



 

Site Archiving

Transaction WB06 is still used to set the archiving indicator for a site.
Actual archiving of the BP-C/V assigned to a site is only possible after archiving of the site.
Sites that are marked for archiving are excluded from distribution.
Departments (table WRF4) and receiving points (table WRF12) are not part of site archiving on SAP S/4HANA.



 

From this blog, you will get a full picture about how to convert retail site master from SAP ERP to S/4HANA OP 1610 and above. You need to carry out a series of necessary operations during three phases, and it makes you more clear what you should do at each step. It also describes the impact of the business partner approach on operations that involve site master data.
					
				
			
			
			
			
			
			
			
			
		




SAP Managed Tags:
Retail,
SAP S/4HANA 












Retail
Industry












SAP S/4HANA
SAP S/4HANA






View products (2)




							Labels:
						



Technology Updates






S4HANA RIGsap s4hana rig

































		2 Comments
	
 



 
 






















jmoralescalvo


			Explorer
		










Mark as Read
Mark as New




Bookmark




Permalink
Print




Report Inappropriate Content








‎09-16-2020
6:12 PM












	
			0
		

	Kudos












			
				
					
					
						This is such a great blog. Thank you. Since this was written I would add note 2891522 as the report now has changed to CVI_CUSTOMIZING_RESOLVE.

Also, I wanted to know if you have encountered if the customer and vendor number of the retail site defer from each other. For example, store 1901 has customer D1901, but vendor A1901. This thows an error in the PERFORM_CHK_SMBP_FOR_S4_SIC report: Customer number D1901 and vendor number A1901 of site 1901 are different.

Thanks for any advice!
					
				
			
			
			
			
			
			
			
			
		























 
 






















former_member392470


			Explorer
		










Mark as Read
Mark as New




Bookmark




Permalink
Print




Report Inappropriate Content








‎08-31-2023
3:54 PM












	
			0
		

	Kudos












			
				
					
					
						Hello Expert ,

 

As we are also try to convert Same business scenario as per below , could you please let me know what solution has been implement for this

Also, I wanted to know if you have encountered if the customer and vendor number of the retail site defer from each other. For example, store 1901 has customer D1901, but vendor A1901. This thows an error in the PERFORM_CHK_SMBP_FOR_S4_SIC report: Customer number D1901 and vendor number A1901 of site 1901 are different.

Thanks ,

Sonali
					
				
			
			
			
			
			
			
			
			
		























 



						You must be a registered user to add a comment. If you've already registered, sign in. Otherwise, register and sign in.
					

Comment






 Labels in this area





Artificial Intelligence (AI)
1


Business Trends
363


Business Trends​
15


Digital Transformation with Cloud ERP (DT)
1


Event Information
461


Event Information​
19


Expert Insights
114


Expert Insights​
117


Life at SAP
418


Life at SAP​
1


Product Updates
4,688


Product Updates​
161


Roadmap and Strategy
1


Technology Updates
1,505


Technology Updates​
71





 


 




Related Content






The Substitute of MM50 in SAP S/4HANA Cloud Public Edition
in Enterprise Resource Planning Blogs by SAP  Friday


Continuous Influence Session SAP S/4HANA Cloud, private edition: Results Review Cycle for Q4 2023
in Enterprise Resource Planning Blogs by SAP  a week ago


VIES VAT Online Validation with Create Communication Scenario
in Enterprise Resource Planning Q&A  a week ago


Consume international trade data of products from an external system
in Enterprise Resource Planning Blogs by SAP  2 weeks ago


Asset Management in SAP S/4HANA Cloud Public Edition 2402
in Enterprise Resource Planning Blogs by SAP  2 weeks ago







 





 




Popular Blog Posts










Useful documents on SCN






by 

Nancy


• Product and Topic Expert



134440 Views
123 comments
220 kudos


01-06-2015








Evolution of ABAP






by 

karl_kessler


• Product and Topic Expert



26101 Views
42 comments
196 kudos


09-01-2022








Analytics in S/4HANA - real shape of embedded analytics and beyond embedded analytics






by 

Masaaki


• Advisor



98995 Views
32 comments
182 kudos


06-08-2019









 



 Top kudoed authors

 



User

			Count
		












FabianAckermann









			7
		










Gerhard_Welker









			6
		










Adeem









			5
		










Marco_Valencia









			5
		










Chr_Vogler









			4
		










MarceGiovanetti









			4
		










Saumitra









			3
		









brennen_fischer12









			3
		










Ying









			3
		










Axel









			2
		




View all
 








































Auto-suggest helps you quickly narrow down your search results by suggesting possible matches as you type.