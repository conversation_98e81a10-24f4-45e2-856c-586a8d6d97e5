Technical Operations | SAP Help PortalHomeSupport ContentTechnical OperationsTechnical OperationsProductionStates:DraftProductionAdditional Content This documentSearch Scopes:All SAP productsThis productThis documentAll of these words:Any of these words:This exact word or phrase:None of these words:ClearSearchAdvanced SearchTable of ContentsFavoriteThis page has been removed from your favorites.Download PDFThe following PDF options are available for this document:Create Custom PDFFeedback You can now add comments and questions right into SAP documentation and start a conversation with SAP authors and experts. Don't show me againFeedbackShareMoreMetadata Analytics Subscribe Table of Contents Technical Operations  Business Intelligence Monitoring Home  Dashboard Builder / Technical Analytics home  Diagnostics Home  End-User Experience Monitoring  Extractor Framework Home  Hybris Monitoring Home  Integration Monitoring Home  Job Monitoring Home  MAI Alert Inbox Home  Managing the size of SAP BW data in a SAP Solution Manager.  Monitoring & Alerting Infrastructure (MAI).  Public Cloud Operations Home  Rapid Content Delivery  Root Cause Analysis Home  SAP HANA Operations with SAP Solution Manager Home  SAP Solution Manager Emergency Monitoring  Technical Administration home  Technical Operations











                Application Operations in SAP Solution Manager 7.2 provides System and Application Management
                capabilities for central monitoring, alerting, analytics, and administration of SAP centric cloud and
                on-premise solutions.
 


                    This wiki content has been moved to the SAP Solution Manager 7.2 expert portal.  Any child pages of this wiki are
                    no longer maintained.

















We are
            the team that ensures the smooth operation of your entire system landscape.

The detailed topic pages providing information related to SAP Solution Manager version 7.1 as well as
            7.2
  










What do
              you want to do?


Monitoring & Alerting Infrastructure
                  (MAI)
System Monitoring
Diagnostics Agent home
User Experience Monitoring
Technical Administration
non-SAP Monitoring
Integration Monitoring

Interface and Connection
                    Monitoring
Process Integration
                    Monitoring
Message Flow Monitoring

BPM Workflow
                  Monitoring
Hybris Monitoring


SAP HANA
                  Monitoring
BI Monitoring
Job Monitoring
Solution Manager
                  Emergency Monitoring
Root Cause Analysis
Exception Management
Configuration Validation
Dashboard Builder / Technical Analytics
HANA Enterprise
                  Cloud Operations
Public Cloud Operations
Test SAP Solution Manager Internet Demo System






Further
              Information

Extractor Framework Information
Unified Alert Inbox
Alert Consumer BAdI Interface
Solution Manager Setup Wiki
System Management with LMDB
Introscope Wiki
SAP Enterprise Support Academy
Rapid Content Delivery
BW size in SAP Solution Manager



















CommentsThere are no comments on this page. Leave a comment Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedback Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedbackCopyrightDisclaimerPrivacy StatementLegal DisclosureTrademarkTerms of UseCookie PreferencesAccessibility and SustainabilitySystem StatusAsk a question about the SAP Help PortalFind us onFollow us on FacebookSAP on FacebookFollow us on YouTubeSAP on YouTubeFollow us on LinkedInSAP on LinkedInFollow us on InstagramSAP on InstagramShare

