

	Business Partner Authorization Tips and Tricks - SAP Community











































SAP Community







Products and Technology







Enterprise Resource Planning







ERP Blogs by SAP







Business Partner Authorization Tips and Tricks














    	Enterprise Resource Planning Blogs by SAP
    

    	Get insights and updates about cloud ERP and RISE with SAP, SAP S/4HANA and SAP S/4HANA Cloud, and more enterprise management capabilities with SAP blog posts.
    

















All communityThis categoryBlogKnowledge baseUsersManaged tags




Enter a search wordTurn off suggestions
Enter a search wordTurn off suggestions
Enter a user name or rankTurn off suggestions
Enter a search wordTurn off suggestions
Enter a search wordTurn off suggestions



cancel



Turn on suggestions







		Showing results for 



			Search instead for 



		Did you mean: 
























 
 















							Business Partner Authorization Tips and Tricks
							
						























d_marmeliuc


			Advisor
		






Options



Subscribe to RSS Feed




Mark as New
Mark as Read




Bookmark
Subscribe




Printer Friendly Page
Report Inappropriate Content









‎10-16-2020
3:17 AM













	
			41
		

	Kudos












			
				
					
					
						Hello again!

 

In the spirit of helping customers in their move to SAP S/4HANA and to Business Partner I have often found myself being asked many questions in regards to the authorization part of the Business Partner and how to tackle it.

 

Based on that I compiled a short guide on how to approach these questions of the Business Partner and some useful scenarios.

 

! Kindly seek the help and guidance of your security consultant when creating and generating new roles as if this activity is not done and tested correctly it can have grave consequences.!

 

First let’s start with the Roles, but before we do that, I would like to point out that what print-screens will be shown below will be of Template Roles and Standard SAP fields.  Please extrapolate to your own unique scenarios.

 

Got to transaction PFCG
Go on the Role field and press F4
On the “Roles” tab type in *Business*

 

 

Select a role like “SAP_CA_BP_DISPLAY_FS” and now click on Display

 

 

 

Go to the “Authorizations” tab and click on Display Authorization Data
From here we can see Object class and all the Authorization Objects we need

 

 

From a logic perspective the role we are on if attributed to a user would only allow the user to have a display only role when accessing the BP transaction and that is based on a multitude of objects that have the value of Display:

 

 

For details on each individual object please deep dive in the link below:

https://help.sap.com/viewer/864321b9b3dd487d94c70f6a007b0397/7.31.25/en-US/08e44ae047ff4f518c84dcf76...

 

 

 

Right now we covered the foundation upon which we will build our roles for our unique situations and for that I made up some likely scenarios below:

 

I want to have separate roles for my Customer Team and my Vendor team

 

 

In order to achieve this functionality we will use our template role above and create 6 additional roles: 3 for the Customer Team(admin,change and display) and 3 for Vendor Team (admin,change and display).

Once we have the roles created we can go in the authorization object B_BUPA_RLT make them custom:

 

Customer Display:

We are adjusting the BP Role to only include the 000000, FLCU01 and FLCU00 with the activity of Display

 

 

Customer Create/Change

We are adjusting the BP Role to only include the 000000, FLCU01 and FLCU00 with the activity of either Change or Create depending on the Role.

 

 

 

The same process is applied for the Vendor Team, but instead of FLCU01 and FLCU00 we will have FLVN01 and FLVN00 and of course different activity types based on each specific role.

 

 

 

 

Besides the roles for each team I need certain fields to behave differently depending on who is viewing it

 

For this activity we will be using help of the Authorization Object B_BUPA_FDG and a customizing activity under the SPRO->Cross Application Component->SAP Business Partner->Business Partner->Basic Settings->Authorization Management->Define Field Groups Relevant to Authorizations

 

 

 

For the exercise at hand I have defined field group 9 which is Bank Details, as I want this field to be visible only for the Create and Change roles I created above, and hidden for the Display role.

 

As we did the customizing, lets now adjust the role of Customer Display, under the B_BUPA_FDG object with the following setting:

 

 

The setting basically exclude field group 9(Bank Details) from the list of fields available to be displayed while having this Customer Display role on your user.

 

 

I have specific Business Partner/Account Group that I do not want certain employees to touch, as I have a specific team which handles that specific account group

 

This scenario is even trickier as it involves customizing, master data and authorization change.

 

Let’s first start with the customizing for which we will go to SPRO->Cross Application Component->SAP Business Partner->Business Partner->Basic Settings->Authorization Management->Maintain Authorization Groups

 

 

Now we are going to create an Authorization Group like “TEST” as seen below and we will save our work.

 

 

 

 

 

Now we move to the master data part in which we choose our “specific” Business Partner we perform a change on field AUGRP which is on the Control Tab of the Business Partner under the Control Parameters view with the “TEST” value:

 

 

 

 

For this exercise we will change just one BP but for an entire Account Group you can use the MASS transaction to change multiple BPs with the field in question.

 

 

 

The last piece of the puzzle is to change the authorization on our Role, on the B_BUPA_GRP object:

 

Before:

 

After

 

With the change above it means that the user with the Display Role will only be able to see Business Partners that do not have the TEST Authorization Group.

 

 

If you also want to limit the F4 search help when searching for a specific Business Partner please read and implement the note: https://launchpad.support.sap.com/#/notes/2441447

 

Please take the above as just an example of what can be done. Of course certain situations and scenarios may see a combination of different aspects, but with the above information as a baseline I am sure you will be able to achieve it and combine field, authorization groups and roles to best suit your needs.

 

Another important activity when tackling roles and authorization is to test one specific activity under 1 user. That means you work your way up from scenario number 1 to 3 and beyond, testing and validating each part of the process.

Do not start customizing a role with multiple authorization on it as you may find yourself spending days on a activity that can be accomplished in minutes, as roles can overwrite each other and you will be back to square 1 ?

 

Hope you liked the ideas plus the tips and tricks above.

 

As usual thank you and see you later.
					
				
			
			
			
			
			
			
			
			
		




SAP Managed Tags:
SAP S/4HANA,
Security 












SAP S/4HANA
SAP S/4HANA












Security
Topic






View products (2)




							Labels:
						



Technology Updates






bpbusiness partnerbusiness partner authorizationEmployee BPS4HANAS4HANA MovementS4HANA RIG

































		6 Comments
	
 



 
 




































Mark as Read
Mark as New




Bookmark




Permalink
Print




Report Inappropriate Content








‎06-23-2021
3:39 PM












	
			1
		

	Kudo












			
				
					
					
						Hello Sir,

Thank you very much for giving all the details.
					
				
			
			
			
			
			
			
			
			
		























 
 




































Mark as Read
Mark as New




Bookmark




Permalink
Print




Report Inappropriate Content








‎08-31-2021
12:27 PM












	
			1
		

	Kudo












			
				
					
					
						Just stumbled across this, great detail and easy to follow. Very helpful article, thanks for sharing Dorin!
					
				
			
			
			
			
			
			
			
			
		























 
 






















former_member768454


			Participant
		










Mark as Read
Mark as New




Bookmark




Permalink
Print




Report Inappropriate Content








‎11-16-2021
9:44 PM












	
			0
		

	Kudos












			
				
					
					
						Greetings,
I would need more detailed information, I should be able not to display any support teams no longer used on ITSM but without deleting anything at the level of BP etc etc

it can be done ?

 

TKS
					
				
			
			
			
			
			
			
			
			
		























 
 






















lauraburg


			Member
		










Mark as Read
Mark as New




Bookmark




Permalink
Print




Report Inappropriate Content








‎03-02-2023
8:05 AM












	
			0
		

	Kudos












			
				
					
					
						Good morning,

We are using for our customer the HCM integration and we are using the following reports as daily jobs:

/SHCM/RH_SYNC_BUPA_FROM_EMPL

/SHCM/R_EMPL_HANDLE_BPROLES

After first running these reports in order to synchronize employee data to business partner data, 2 new business partners are created. (1 as BP employee, 1 as BP employment + vendor) These BP are assigned in separate BP groupings.

The customer wishes that these business partner synchronized from employees receive automatically in the field "authorization group" the value 0002 (visibility very restricted), so that just the HR users are able to see these BPs; this we have covered already in the authorization concept by managing the object B_BUPA_GRP.

I couldn't figure out how to fill in automatically the field "authorization group" with the value 0002 when running the synchronization reports. I have tried with MASS transaction, but I need a viable solution for the productive system that should also run as a job.

Any ideas/suggestions?

Thank you

Laura
					
				
			
			
			
			
			
			
			
			
		























 
 






















cnys1992


			Newcomer
		










Mark as Read
Mark as New




Bookmark




Permalink
Print




Report Inappropriate Content








‎05-12-2023
10:55 AM












	
			0
		

	Kudos












			
				
					
					
						Hi Laura,

You can add this via development in the class /SHCM/B_EE_BP_SYNC and method "MODIFY_ALL" which is called during report /SHCM/RH_SYNC_BUPA_FROM_EMPL during BP creation/update.

You have to update the parameter CS_BP_DATA with the required value in the field "authorization group"

Kr,

Carolien
					
				
			
			
			
			
			
			
			
			
		























 
 






















andreaweber


			Member
		










Mark as Read
Mark as New




Bookmark




Permalink
Print




Report Inappropriate Content








‎07-03-2023
2:24 PM












	
			1
		

	Kudo












			
				
					
					
						Hallo,

 

vielen Dank für die Information.

 

Wir haben noch ein weiteres Problem: Wir möchten gerne die Ergebnisliste aus der Suchhilfe im Nachhinein modifizieren. Hintergrund ist, dass rudimentäre Partnerdaten zwar für jeden angezeigt werden sollen (z.B. Nummer, Name), dass aber Daten wie Geburtsdatum oder PLZ/Ort in der Anzeige ge-xxx-t bzw. anonymisiert sein sollen.

 

Das Berechtigungsszenario für Suchhilfe haben wir aktiviert und F4 ausgeprägt und uns via Enhancement drauf geschaltet. Das geht auch, jedoch scheint man dies für jede Suchausprägung (Partner nach Adresse, Partner allgemein, Partner nach GP-Rolle, ... usw) extra machen zu müssen.

 

Gibt es eine Möglichkeit, das Enhancement vorgelagert einzubauen?

 

Vielen Dank

Andrea
					
				
			
			
			
			
			
			
			
			
		























 



						You must be a registered user to add a comment. If you've already registered, sign in. Otherwise, register and sign in.
					

Comment






 Labels in this area





Artificial Intelligence (AI)
1


Business Trends
363


Business Trends​
15


Digital Transformation with Cloud ERP (DT)
1


Event Information
461


Event Information​
19


Expert Insights
114


Expert Insights​
117


Life at SAP
418


Life at SAP​
1


Product Updates
4,688


Product Updates​
161


Roadmap and Strategy
1


Technology Updates
1,505


Technology Updates​
71





 


 




Related Content






We want to update BP General data, Item, Sales and company code using single API Call
in Enterprise Resource Planning Q&A  yesterday


MM-SD SCHEDULE AGREEMENT INTEGRATION
in Enterprise Resource Planning Blogs by Members  yesterday


Data Quality Management( DQM ) Derivation in SAP Master Data Central Governance
in Enterprise Resource Planning Blogs by Members  Monday


What's New? Overview of Changes in Extensibility Objects in SAP S/4HANA Cloud Public Edition
in Enterprise Resource Planning Blogs by SAP  Friday


How to design sales prices in SAP S/4HANA and be ready for future innovations
in Enterprise Resource Planning Blogs by SAP  Friday







 





 




Popular Blog Posts










Useful documents on SCN






by 

Nancy


• Product and Topic Expert



134441 Views
123 comments
220 kudos


01-06-2015








Evolution of ABAP






by 

karl_kessler


• Product and Topic Expert



26101 Views
42 comments
196 kudos


09-01-2022








Analytics in S/4HANA - real shape of embedded analytics and beyond embedded analytics






by 

Masaaki


• Advisor



98995 Views
32 comments
182 kudos


06-08-2019









 



 Top kudoed authors

 



User

			Count
		












FabianAckermann









			7
		










Gerhard_Welker









			6
		










Adeem









			5
		










Marco_Valencia









			5
		










Chr_Vogler









			4
		










MarceGiovanetti









			4
		










Saumitra









			3
		









brennen_fischer12









			3
		










Ying









			3
		










Axel









			2
		




View all
 








































Auto-suggest helps you quickly narrow down your search results by suggesting possible matches as you type.