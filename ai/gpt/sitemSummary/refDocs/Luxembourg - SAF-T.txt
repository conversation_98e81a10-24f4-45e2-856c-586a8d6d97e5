SAF-T | SAP Help PortalHomeSAP S/4HANA CloudLuxembourgGeneral FunctionsDocument and Reporting ComplianceSAF-TLuxembourg2402.1Available Versions: 2408 Latest  2402.2  2402.1  2402  2308.4  2308.3  2308.2  2308.1  2308  2302.4 * 2302.3 * 2302.2 * 2302.1 * 2302 * 2208.3 * 2208.2 * 2208.1 * 2208 * 2202.4 * 2202.3 * 2202.2 * 2202.1 * 2202 * 2111.1 * 2111 * 2108.1 * 2108 * 2105 * 2102 * 1708 * 1705 ** This product version is out of mainstream maintenance. The documentation is no longer regularly updated.For more information, see the Product Availability Matrix (PAM)EnglishAvailable Languages: English  French (Français)  German (Deutsch) ProductionStates:TestProductionDraftThis documentSearch Scopes:All SAP productsThis productThis documentAll of these words:Any of these words:This exact word or phrase:None of these words:ClearSearchAdvanced SearchTable of ContentsFavoriteThis page has been removed from your favorites.Download PDFThe following PDF options are available for this document:Create Custom PDFFeedback You can now add comments and questions right into SAP documentation and start a conversation with SAP authors and experts. Don't show me againFeedbackShareEdit in IXIA CCMS Web You can now go directly to IXIA CCMS Web to edit the topic. Simply select Edit in IXIA CCMS Web link under the More… menu. Don't show me againMoreMetadata Analytics Subscribe Edit in IXIA CCMS WebTable of Contents Luxembourg  General Functions  Master Data  Document and Reporting Compliance  Electronic Customer Invoices  Electronic Supplier Invoices  Balance of Payments - STATEC Form 26  EC Sales List  Annual VAT Return  Periodic VAT Return  SAF-T  Configuration for SAF-T  Read Access Logging Configurations in SAF-T Luxembourg  Finance  Sales  SAF-TOn this pagePrerequisitesGenerating the SAF-T Report
          Related Information
         


         You can use the Run Statutory Reports app to generate the SAF-T report to create your on request audit file.
         

Note

This report replaces the existing report in compatibility mode. All the XML files generated by the report in compatibility mode are still available and can be downloaded for validation by the tax authorities. If files need to be regenerated, this needs to be done using the SAF-T Luxembourg report, as follows:


For reporting tasks where the processing has been completed, only one activity (the old one) is shown as completed.

For reporting tasks where the activity is in process (a run was created but the submission was not completed), the older activity is shown as outdated and the new activity is available for use. No runs can be created using the old activity.

For reporting tasks where no activity has been started, only the new activity is shown.



Pro Rata VAT
If you calculate pro rata VAT for Luxembourg, the system inserts the following data in the generated SAF-T file for each transaction in the TaxInformation subsection in the XML file:


The deductible VAT amount calculated using the pro rata coefficient.

The non-deductible VAT amount calculated using the pro rata coefficient; which is added to the tax base amount.

For more information about the pro rata VAT, see Pro Rata VAT.
Alternative Fiscal Year Variant
The SAF-T file must contain data for a given calendar year. If you use a fiscal year variant in which the fiscal year is not equal to the calendar year for the leading ledger of your company code, you must also define a non-leading ledger to which you assign a fiscal year variant in which the fiscal year is equal to the calendar year. You can define the non-leading ledger in the Assign Fiscal Year Variants to Ledgers and Company Codes configuration activity in your configuration environment as it is explained in section Prerequisites below, in bullet point 2.
For more information about the alternative fiscal year variants and on how to set them up, see Alternative Fiscal Year Variant.


Prerequisites


The key user responsible for configuration has made the necessary settings for generating the SAF-T report as described in Configuration for SAF-T.

The SAF-T solution supports file generation based on a fiscal year. However, the tax authorities in Luxembourg require that you generate the SAF-T file for an entire calendar year. Thus, you must ensure that you use the default fiscal year variant K4, which means that your fiscal year matches the calendar year. For more information, see Setting Up an Alternative Fiscal Year Variant.





Generating the SAF-T Report


On the Run Statutory Reports screen, click the Add Ad-hoc Reporting Task button to add a reporting task for the SAF-T report.


Note

Since the SAF-T report is generated on an ad-hoc basis, you need to add a reporting task for the reporting period.


On the Add Ad-hoc Reporting Task pop-up dialog box, specify the report name LU_SAFT.

Select the due date, reporting period, and reporting year for the SAF-T report and click OK.
A new ad-hoc reporting task is created for the SAF-T report.

Select the SAF-T reporting task.

Choose New Run.
You can enter further filtering options, such as the posting date, and a header comment.


Note

You cannot change the company code for the report. If you want to test the report with a different company code, create an additional reporting entity for that.

Choose a run option. You can choose to generate the report run immediately or schedule the runs for a later point in time. For report runs that may take a long time to process, you can choose to run the reports in the background.

Choose Run.
The app generates the run and displays it in section Generated on the screen. You can display the generated data by selecting the row of the generated run.
If the run was successful, you can review the data in the XML file on the Legal Reporting tab, by selecting the link in the Document Name column. The file is named SAF-T File FAIA. You can also download the generated document to your local computer and submit it to the tax authorities as required.
After the file has been generated, submitted to the tax authorities, and the authorities approved it, change the status of the reporting run by choosing the row of the run and then select the Update Submission Status button. Change the status manually to Accepted by Government.







          Related Information
         

Pro Rata VAT


Alternative Fiscal Year Variant



On this pagePrerequisitesGenerating the SAF-T Report
          Related Information
         CommentsThere are no comments on this page. Leave a comment Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedback Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedbackCopyrightDisclaimerPrivacy StatementLegal DisclosureTrademarkTerms of UseCookie PreferencesAccessibility and SustainabilitySystem StatusAsk a question about the SAP Help PortalFind us onFollow us on FacebookSAP on FacebookFollow us on YouTubeSAP on YouTubeFollow us on LinkedInSAP on LinkedInFollow us on InstagramSAP on InstagramShare

