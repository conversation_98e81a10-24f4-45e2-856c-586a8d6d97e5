Implementation Guide | PUBLIC
Document Version: SAP S/4HANA 2021 – 2023-09-27
Migration Objects for SAP S/4HANA© 2023 SAP SE or an SAP affiliate  company. All rights reserved.
THE BEST RUN  
1 Available Migration Objects
Y ou can use the following table to gain an overview of all migration objects available for SAP S/4HANA. It’s
sorted in alphabetical order by Migration Object Name . Choose a migration object name to navigate to the
corresponding documentation for the migration object.
Use the search field  in the Migration Object Name  column to search for a specific  migration object.
Choose Filter  to apply further filters  for Master data  and/or Transactional data  in the Business Object Type
column, for Custom Field Support , and for the application component in the Component  column to narrow
down the results list. If you want to see more or less information, choose Show/hide columns , and select the
respective checkboxes for the columns you would like to show or hide.
 Tip
Filter the Migration Approach  column to only see migration objects relevant for you. Therefore, choose Filter
in the respective column, and select the required approach, Direct Transfer  or File/Staging T able .
Migration Object
Name Business Object Type Migration Approach Custom Field Support Component
EHS - Deviation inci-
dent [page 1648]Master data File/Staging T able Not applicableEHS-SUS-IM
EHS - Incident [page
1677]T ransactional data File/Staging T able Not applicableEHS-SUS-IM
PRA - Ownership lease
[page 1805]Master data File/Staging T able Not applicableIS-OIL-PRA
PMA - Supplier in-
formation assessment
[page 1440]Master data File/Staging T able Not applicableEHS-SUS-PMA
PRA - Contract volume
[page 1850]T ransactional data File/Staging T able Not applicableIS-OIL-PRA
PRA - Marketing group
assignment [page
1853]Master data File/Staging T able Not applicableIS-OIL-PRA
RFM - Product assign-
ment to distribution
center [page 1872]Master data File/Staging T able Not applicableLO-RFM-MD-LST
RFM - Assortment
module [page 1870]Master data File/Staging T able Not applicableLO-RFM-MD-LST
PRA - Delivery network
allocation profile  [page
1814]Master data File/Staging T able Not applicableIS-OIL-PRA
2 PUBLICMigration Objects for SAP S/4HANA
Available Migration ObjectsMigration Object
Name Business Object Type Migration Approach Custom Field Support Component
PRA - Well completion
test [page 1825]Master data File/Staging T able Not applicableIS-OIL-PRA
PRA - Delivery network
downstream node
[page 1816]Master data File/Staging T able Not applicableIS-OIL-PRA
PRA - Measurement
point volume [page
1830]T ransactional data File/Staging T able Not applicableIS-OIL-PRA
PRA - Measurement
point allocation profile
[page 1820]Master data File/Staging T able Not applicableIS-OIL-PRA
PRA - WC/DN theoreti-
cal calculation method
[page 1827]Master data File/Staging T able Not applicableIS-OIL-PRA
Warranty Claim [page
1635]T ransactional data File/Staging T able Y esLO-WTY
PMA - Simple com-
pliance assessment
[page 1432]Master data File/Staging T able Not applicableEHS-SUS-PMA
PP - Production order
(only open PO) [page
1467]T ransactional data File/Staging T able Not applicablePP-SFC
PP-KAB - Kanban con-
trol cycle [page 1522]Master data File/Staging T able Not applicablePP-KAB
Field Logistics -
Container (restricted)
[page 1309]Master data File/Staging T able Y esCA-FL-SG
Field logistics - Sup-
plier item [page 1311]T ransactional data File/Staging T able Y esCA-FL-SRV
PC - Safety-related
property [page 1459]Master data File/Staging T able Not applicableEHS-SUS-FND
SDS - Shipment his-
tory [page 1457]T ransactional data File/Staging T able Not applicableEHS-SUS-SDS
SDS - Assessment for
unpackaged product
[page 1453]Master data File/Staging T able Not applicableEHS-SUS-SDS
PP-KAB - Production
supply area [page 1469]Master data File/Staging T able Y esPP-KAB
Fixed asset - Postings
[page 1754]T ransactional data File/Staging T able Not applicableFI-AA
Fixed asset - Usage ob-
ject [page 1762]Master data File/Staging T able Not applicableFI-AA
Migration Objects for SAP S/4HANA
Available Migration Objects PUBLIC 3Migration Object
Name Business Object Type Migration Approach Custom Field Support Component
Fixed asset - Master
data [page 1691]Master data File/Staging T able Y esFI-AA
FI - Historical balance
[page 1301]T ransactional data File/Staging T able Not applicableFI
DG - Assessment for
unpackaged product
(content-based) [page
1247]Master data File/Staging T able Not applicableEHS-SUS-DG
DG - Assessment
for packaged product
[page 1250]Master data File/Staging T able Not applicableEHS-SUS-DG
Change Request and
Activity (Management
of Change) [page 1682]T ransactional data File/Staging T able Not applicableCA-IAM-MOC
PM - Equipment [page
1296]Master data File/Staging T able Y esPM
PM - Functional loca-
tion [page 1322]Master data File/Staging T able Y esPM
MM - Purchase order
(only open PO) [page
1539]T ransactional data File/Staging T able Y esMM-PUR
EHS - T ask [page 1672]T ransactional data File/Staging T able Not applicableEHS-SUS-EM
Object classification  -
Inspection characteris-
tic (QPMK) [page 1193]Master data File/Staging T able Not applicableCA-CL
Object classification  -
Selected set (QPAM)
[page 1185]Master data File/Staging T able Not applicableCA-CL
Object classification
- Document (DRAW)
[page 1156]Master data File/Staging T able Not applicableCA-CL
Object classification  -
Material (MARA) [page
1164]Master data File/Staging T able Not applicableCA-CL
Object classification  -
Code group (QPGR)
[page 1189]Master data File/Staging T able Not applicableCA-CL
Object classification
- Inspection method
(QMTB) [page 1181]Master data File/Staging T able Not applicableCA-CL
RFM - Merchandise
category reference ar-
ticle [page 1867]Master data File/Staging T able Not applicableLO-RFM-MD-MC
4 PUBLICMigration Objects for SAP S/4HANA
Available Migration ObjectsMigration Object
Name Business Object Type Migration Approach Custom Field Support Component
RFM - Merchandise
category characteristic
value restriction [page
1858]Master data File/Staging T able Not applicableLO-RFM-MD-MC
EHS - Data classifier
[page 1642]Master data File/Staging T able Not applicableEHS-SUS-EM
EHS - Location classi-
fier [page 1646]Master data File/Staging T able Not applicableEHS-SUS-CI
EHS - Chemical/Phys-
ical property [page
1658]Master data File/Staging T able Not applicableEHS-SUS-FND
EHS - Compliance sce-
nario [page 1668]Master data File/Staging T able Not applicableEHS-SUS-EM
Customer - extend ex-
isting record by mul-
tiple addresses [page
1219]Master data File/Staging T able Not applicableSD
Bank [page 1121]Master data File/Staging T able Not applicableFI
Class [page 1149]Master data File/Staging T able Not applicableCA-CL
Revenue accounting
contract [page 1306]T ransactional data File/Staging T able Not applicableFI-RA
SD - Sales order (only
open SO) [page 1572]T ransactional data File/Staging T able Y esSD
Supplier - extend exist-
ing record by new org
levels [page 1607]Master data File/Staging T able Y esMM
MM - Purchasing info
record- extend existing
record [page 1527]Master data File/Staging T able Not applicableMM-PUR
Product - extend exist-
ing record with long
text [page 1490]Master data File/Staging T able Not applicableLO
Product - extend exist-
ing record by new org
levels [page 1477]Master data File/Staging T able Y esLO
Customer - extend ex-
isting record by new
org levels [page 1222]Master data File/Staging T able Y esSD
DG - T echnical names
for substance [page
1255]Master data File/Staging T able Not applicableEHS-SUS-DG
Migration Objects for SAP S/4HANA
Available Migration Objects PUBLIC 5Migration Object
Name Business Object Type Migration Approach Custom Field Support Component
LO - Handling unit
[page 1779]T ransactional data File/Staging T able Not applicableLO-HU-BF
Service product [page
1493]Master data File/Staging T able Y esLO
PC - Physical-chemical
property [page 1449]Master data File/Staging T able Not applicableEHS-SUS-FND
Object classification  -
Purchasing info record
(EINA) [page 1160]Master data File/Staging T able Not applicableCA-CL
RFM - Merchandise
category [page 1855]Master data File/Staging T able Not applicableLO-RFM-MD-MC
RFM - Merchandise
category hierarchy
node [page 1860]Master data File/Staging T able Not applicableLO-RFM-MD-MC
RFM - Merchandise
category hierarchy
node assignment
[page 1863]Master data File/Staging T able Not applicableLO-RFM-MD-MC
RFM - MCHN Char-
acteristic value restric-
tion [page 1865]Master data File/Staging T able Not applicableLO-RFM-MD-MC
JIT - Communication
group [page 1409]Master data File/Staging T able Not applicableLE-JIT-S2P
JIT - Control cycle
[page 1519]Master data File/Staging T able Y esLE-JIT-S2P
SD - Condition Record
for Material determina-
tion [page 1398]Master data File/Staging T able Not applicableSD
Master recipe [page
1463]Master data File/Staging T able Not applicablePP-PI
EHS - Data collection
[page 1656]Master data File/Staging T able Not applicableEHS-SUS-EM
EHS - Compliance re-
quirement [page 1663]Master data File/Staging T able Not applicableEHS-SUS-CI
EHS - Calculation defi-
nition  [page 1643]Master data File/Staging T able Not applicableEHS-SUS-EM
EHS - Location aggre-
gation [page 1640]Master data File/Staging T able Not applicableEHS-SUS-EM
FI - SEPA mandate
[page 1569]Master data File/Staging T able Not applicableFI
ECM - Change master
[page 1137]Master data File/Staging T able Not applicablePLM-WUI-OBJ-ECN
6 PUBLICMigration Objects for SAP S/4HANA
Available Migration ObjectsMigration Object
Name Business Object Type Migration Approach Custom Field Support Component
PM - Measurement
document [page 1277]Master data File/Staging T able Not applicablePM
QM - Inspection plan
[page 1343]Master data File/Staging T able Not applicableQM
Service order (only
open SRVO) [page
1875]T ransactional data File/Staging T able Y esCRM-S4-SRV-SVO
JVA - Joint operating
agreement [page 1355]Master data File/Staging T able Not applicableCA-JVA
JVA - Joint venture
partner [page 1358]Master data File/Staging T able Not applicableCA-JVA
JVA - Cost center sus-
pense [page 1360]Master data File/Staging T able Not applicableCA-JVA
JVA - Venture sus-
pense [page 1363]Master data File/Staging T able Not applicableCA-JVA
JVA - Project suspense
[page 1366]Master data File/Staging T able Not applicableCA-JVA
JVA - Joint venture
master data [page
1368]Master data File/Staging T able Not applicableCA-JVA
CO - Business process
[page 1198]Master data File/Staging T able Not applicableCO-OM
FI - G/L account - ex-
tend existing record by
new org levels [page
1329]Master data File/Staging T able Not applicableFI
FI - G/L account [page
1332]Master data File/Staging T able Not applicableFI
CO - Statistical key fig-
ure [page 1200]Master data File/Staging T able Not applicableCO
Product [page 1497]Master data File/Staging T able Y esLO
FI - Accounts payable
open item [page 1418]T ransactional data File/Staging T able Y es FI-AP
Material listing and ex-
clusion [page 1378]Master data File/Staging T able Not applicableSD
DG - Assessment
for unpackaged prod-
uct (text-based) [page
1253]Master data File/Staging T able Not applicableEHS-SUS-DG
Migration Objects for SAP S/4HANA
Available Migration Objects PUBLIC 7Migration Object
Name Business Object Type Migration Approach Custom Field Support Component
FI - Accounts receiva-
ble open item [page
1425]T ransactional data File/Staging T able Y es FI-AR
FI - G/L account bal-
ance and open/line
item [page 1334]T ransactional data File/Staging T able Y esFI
CO - Cost rate [page
1117]Master data File/Staging T able Not applicable CO
CO - Activity type
[page 1380]Master data File/Staging T able Not applicable CO
FI - Bank account bal-
ance [page 1119]T ransactional data File/Staging T able Not applicable FI
FI - Cash memo record
[page 1400]T ransactional data File/Staging T able Not applicable FIN-FSCM-CLM
Batch unique at ma-
terial and client level
[page 1123]Master data File/Staging T able Not applicable LO-BM
Batch unique at plant
level [page 1127]T ransactional data File/Staging T able Not applicable LO-BM
Material BOM [page
1131]Master data File/Staging T able Not applicable PP-BD
PM - Equipment BOM
[page 1287]Master data File/Staging T able Not applicable PM
PM - Functional loca-
tion BOM [page 1317]Master data File/Staging T able Not applicable PM
PC - Product compli-
ance info [page 1674]Master data File/Staging T able Not applicable EHS-SUS-FND
VC - Material variant
[page 1396]Master data File/Staging T able Not applicableLO-VC
PC - Raw material
compliance info [page
1660]Master data File/Staging T able Not applicable EHS-SUS-FND
Class hierarchy [page
1152]T ransactional data File/Staging T able Not applicable CA-CL
8 PUBLICMigration Objects for SAP S/4HANA
Available Migration ObjectsMigration Object
Name Business Object Type Migration Approach Custom Field Support Component
Characteristic [page
1140]Master data File/Staging T able Not applicable CA-CL
Object classification
- General template
[page 1169]Master data File/Staging T able Not applicable CA-CL
PC - Substance [page
1578]Master data File/Staging T able Not applicable EHS-SUS-FND
Condition contract
[page 1202]T ransactional data File/Staging T able Not applicable LO-AB
Consent [page 1212] T ransactional data File/Staging T able Not applicable CA-GTF-CON
CO - Cost center [page
1371]Master data File/Staging T able Not applicable CO
Customer - extend
existing record by
Credit Management
data [page 1233]Master data File/Staging T able Not applicable SD
Customer - extend
existing record by
Thailand branch code
[page 1230]Master data File/Staging T able Not applicable SD
Customer [page 1238] Master data File/Staging T able Y es SD
SD - Customer Mate-
rial [page 1235]Master data File/Staging T able Not applicable SD
Document info record
[page 1258]Master data File/Staging T able Not applicable CA-DMS
PM - Equipment (dep-
recated) [page 1293]Master data File/Staging T able Y es PM
Exchange rate [page
1300]Master data File/Staging T able Not applicable FI
Fixed asset (incl. bal-
ances and transac-
tions) [page 1684]Master data File/Staging T able Not applicable FI-AA
PM - Functional lo-
cation (deprecated)
[page 1319]Master data File/Staging T able Y es PM
Migration Objects for SAP S/4HANA
Available Migration Objects PUBLIC 9Migration Object
Name Business Object Type Migration Approach Custom Field Support Component
QM/PM - Catalog code
group/code [page 1544]Master data File/Staging T able Not applicable QM
QM - Inspection
method [page 1340]Master data File/Staging T able Not applicable QM
EFD - Reinf report
(Brazil) [page 1285]T ransactional data File/Staging T able Not applicableFI-LOC-FI
QM - Selected set
[page 1565]Master data File/Staging T able Not applicable QM
EHS - Location hierar-
chy [page 1651]Master data File/Staging T able Not applicableEHS-SUS-FND
JIT - Packing group
specification  [page
1413]Master data File/Staging T able Not applicableLE-JIT-S2C
JIT - Supply control
[page 1416]Master data File/Staging T able Not applicableLE-JIT
EHS - Location [page
1653]Master data File/Staging T able Not applicableEHS-SUS-FND
QM - Selected set code
[page 1567]Master data File/Staging T able Not applicable QM
QM - Master inspec-
tion characteristic
[page 1406]Master data File/Staging T able Not applicable QM
CO - Internal order (re-
stricted) [page 1346]T ransactional data File/Staging T able Not applicable CO
Legal transaction
[page 1375]T ransactional data File/Staging T able Not applicable CM-GF
PM - Maintenance item
[page 1383]Master data File/Staging T able Y es PM
PM - Maintenance noti-
fication  [page 1281]Master data File/Staging T able Y esPM
PM - Maintenance plan
[page 1385]Master data File/Staging T able Y esPM
PM - Equipment task
list [page 1289]Master data File/Staging T able Not applicable PM
10 PUBLICMigration Objects for SAP S/4HANA
Available Migration ObjectsMigration Object
Name Business Object Type Migration Approach Custom Field Support Component
PM - Functional loca-
tion task list [page
1314]Master data File/Staging T able Not applicable PM
PM - General mainte-
nance task list [page
1326]Master data File/Staging T able Not applicable PM
JIT - Customer [page
1411]Master data File/Staging T able Not applicableLE-JIT
Material inventory bal-
ance [page 1349]T ransactional data File/Staging T able Not applicable MM-IM
PM - Measuring point
[page 1279]Master data File/Staging T able Not applicable PM
Condition record for
pricing (general tem-
plate) [page 1206]Master data File/Staging T able Not applicable SD
Condition record for
pricing in purchasing
(restricted) [page 1402]Master data File/Staging T able Not applicable MM-PUR
Condition record for
pricing in sales (re-
stricted) [page 1554]Master data File/Staging T able Not applicable SD
SD - Condition record
for free goods [page
1560]Master data File/Staging T able Not applicable SD
Material - Forecast
Planning [page 1392]Master data File/Staging T able Not applicable MM
Product classification
- Commodity code
[page 1389]Master data File/Staging T able Not applicable SLL-ITR-CLS
PM - Maintenance or-
der [page 1261]T ransactional data File/Staging T able Y esPM
Product classification
- Legal control [page
1394]Master data File/Staging T able Not applicableSLL-ITR-CLS
Product consumption
[page 1475]Master data File/Staging T able Not applicableMM
Migration Objects for SAP S/4HANA
Available Migration Objects PUBLIC 11Migration Object
Name Business Object Type Migration Approach Custom Field Support Component
Production version
[page 1516]Master data File/Staging T able Not applicable PP
CO - Profit  center
[page 1472]Master data File/Staging T able Not applicable CO
PS - Network [page
1524]T ransactional data File/Staging T able Not applicable PS
PS - Project [page 1526] T ransactional data File/Staging T able Not applicable PS
PS - WBS element
[page 1629]T ransactional data File/Staging T able Not applicable PS
MM - Purchase con-
tract [page 1214]T ransactional data File/Staging T able Not applicable MM-PUR
MM - Purchase order
(only open PO) (depre-
cated) [page 1534]T ransactional data File/Staging T able Not applicable MM-PUR
MM - Purchase sched-
uling agreement [page
1552]T ransactional data File/Staging T able Not applicable MM-PUR
MM - Purchasing info
record with conditions
[page 1531]Master data File/Staging T able Not applicable MM-PUR
SD - Sales scheduling
agreement [page 1562]T ransactional data File/Staging T able Not applicableSD-SLS
Routing [page 1547] Master data File/Staging T able Not applicable PP
SD - Sales contract
[page 1558]T ransactional data File/Staging T able Not applicable SD
Software/Hardware
constraint [page 1580]T ransactional data File/Staging T able Not applicable PLM-ESD-ESC
MM - Source list [page
1575]T ransactional data File/Staging T able Not applicable MM-PUR
Supplier - extend exist-
ing record by Thailand
branch code [page
1617]Master data File/Staging T able Not applicable MM
Supplier [page 1619] Master data File/Staging T able Y es MM
12 PUBLICMigration Objects for SAP S/4HANA
Available Migration ObjectsMigration Object
Name Business Object Type Migration Approach Custom Field Support Component
VC - Configuration  pro-
file [page 1209]Master data File/Staging T able Not applicableLO-VC
Work center/Resource
[page 1631]Master data File/Staging T able Not applicable PP
TRM - Commercial pa-
per [page 1581]T ransactional data File/Staging T able Not applicableFIN-FSCM-TRM-TM
TRM - Deposit at no-
tice [page 1584]T ransactional data File/Staging T able Not applicableFIN-FSCM-TRM-TM
TRM - FX option [page
1587]T ransactional data File/Staging T able Not applicableFIN-FSCM-TRM-TM
TRM - Foreign ex-
change swap - contract
[page 1590]T ransactional data File/Staging T able Not applicableFIN-FSCM-TRM-TM
TRM - Foreign ex-
change collar - con-
tract [page 1593]T ransactional data File/Staging T able Not applicableFIN-FSCM-TRM
TRM - Foreign ex-
change spot/forward
transaction - contract
[page 1596]T ransactional data File/Staging T able Not applicableFIN-FSCM-TRM-TM
TRM - Interest rate in-
strument [page 1599]T ransactional data File/Staging T able Not applicableFIN-FSCM-TRM
TRM - Position value
[page 1604]T ransactional data File/Staging T able Not applicableFIN-FSCM-TRM-TM
VC - Characteristics
group [page 1146]Master data File/Staging T able Not applicableLO-VC
PRA - Allocation cross
reference [page 1782]Master data File/Staging T able Not applicableIS-OIL-PRA
PRA - Chemical analy-
sis data [page 1784]Master data File/Staging T able Not applicableIS-OIL-PRA
PRA - MP-WC to con-
tract cross reference
[page 1786]Master data File/Staging T able Not applicableIS-OIL-PRA
PRA - Remitter to DOI
cross reference [page
1787]Master data File/Staging T able Not applicableIS-OIL-PRA
PRA - Check input
process rule [page
1789]Master data File/Staging T able Not applicableIS-OIL-PRA
PRA - Check input
translation [page 1791]Master data File/Staging T able Not applicableIS-OIL-PRA
Migration Objects for SAP S/4HANA
Available Migration Objects PUBLIC 13Migration Object
Name Business Object Type Migration Approach Custom Field Support Component
PRA - Account entry
control [page 1793]Master data File/Staging T able Not applicableIS-OIL-PRA
PRA - Journal entry
open balance [page
1795]T ransactional data File/Staging T able Not applicableIS-OIL-PRA
PRA - Extend existing
customer by PRA data
[page 1797]Master data File/Staging T able Not applicableIS-OIL-PRA
PRA - Division order in-
terest [page 1799]Master data File/Staging T able Not applicableIS-OIL-PRA
PRA - DOI accounting
[page 1801]Master data File/Staging T able Not applicableIS-OIL-PRA
PRA - DOI MP WC
cross reference [page
1803]Master data File/Staging T able Not applicableIS-OIL-PRA
PRA - Joint venture
[page 1807]Master data File/Staging T able Not applicableIS-OIL-PRA
PRA - Unit venture
tract [page 1809]Master data File/Staging T able Not applicableIS-OIL-PRA
PRA - Extend existing
supplier by PRA data
[page 1811]Master data File/Staging T able Not applicableIS-OIL-PRA
PRA - Well and well
completion [page 1823]Master data File/Staging T able Not applicableIS-OIL-PRA
PRA - MP/WC trans-
porter cross reference
[page 1829]Master data File/Staging T able Not applicableIS-OIL-PRA
PRA - Well completion
volume [page 1833]T ransactional data File/Staging T able Not applicableIS-OIL-PRA
PRA - Contract [page
1835]Master data File/Staging T able Not applicableIS-OIL-PRA
PRA - Valuation for-
mula [page 1837]Master data File/Staging T able Not applicableIS-OIL-PRA
PRA - Settlement di-
versity [page 1839]Master data File/Staging T able Not applicableIS-OIL-PRA
PRA - Settlement
statement DOI cross
reference [page 1841]Master data File/Staging T able Not applicableIS-OIL-PRA
PRA - State tax rate
[page 1842]Master data File/Staging T able Not applicableIS-OIL-PRA
PRA - T ax calculation
data [page 1844]Master data File/Staging T able Not applicableIS-OIL-PRA
14 PUBLICMigration Objects for SAP S/4HANA
Available Migration ObjectsMigration Object
Name Business Object Type Migration Approach Custom Field Support Component
PRA - Valuation cross
reference [page 1846]Master data File/Staging T able Not applicableIS-OIL-PRA
PRA - Well completion
tax classification  [page
1848]Master data File/Staging T able Not applicableIS-OIL-PRA
PRA - Measurement
point [page 1818]Master data File/Staging T able Not applicableIS-OIL-PRA
PRA - Delivery network
[page 1812]Master data File/Staging T able Not applicableIS-OIL-PRA
HCM - Employee [page
1765]Master data File/Staging T able Not applicablePA-PA-XX-MIG
HCM - Object [page
1772]Master data File/Staging T able Not applicablePA-PA-XX-MIG
HCM - Relationship
[page 1776]Master data File/Staging T able Not applicablePA-PA-XX-MIG
FI - SEPA mandate
[page 254]Master dataDirect T ransfer - ERPNot applicableFI-AR
RE-FX - Architectural
object [page 537]Master dataDirect T ransfer - ERPNot applicableRE-FX-BD
Batch unique at ma-
terial and client level
[page 120]Master dataDirect T ransfer - ERPNot applicableLO-BM
Batch unique at plant
level [page 116]Master dataDirect T ransfer - ERPNot applicableLO-BM
FI - Accounts paya-
ble open item (classic
G/L) [page 227]T ransactional data Direct T ransfer - ERP Not applicableFI
FI - Accounts payable
open item (new G/L)
[page 231]T ransactional data Direct T ransfer - ERP Not applicableFI
RE-FX - Building [page
541]Master dataDirect T ransfer - ERPNot applicableRE-FX-BD
RE-FX - Business entity
[page 546]Master dataDirect T ransfer - ERPNot applicableRE-FX
Business partner
[page 127]Master dataDirect T ransfer - ERPNot applicableAP-MD-BP
Catalog code group
and code [page 130]Master dataDirect T ransfer - ERPNot applicableQM
Characteristic [page
134]Master dataDirect T ransfer - ERPNot applicableCA-CL
Class [page 138] Master dataDirect T ransfer - ERPNot applicableCA-CL
Migration Objects for SAP S/4HANA
Available Migration Objects PUBLIC 15Migration Object
Name Business Object Type Migration Approach Custom Field Support Component
Condition contract
[page 142]Master dataDirect T ransfer - ERPNot applicableLO-AB
Customer [page 146] Master dataDirect T ransfer - ERPNot applicableSD
SD - Customer mate-
rial [page 567]Master dataDirect T ransfer - ERPNot applicableSD
SD - Customer quota-
tion [page 563]T ransactional dataDirect T ransfer - ERPNot applicableSD
ECM - Change master
[page 201]Master dataDirect T ransfer - ERPNot applicablePLM-PLC
PM - Equipment [page
398]Master dataDirect T ransfer - ERPNot applicablePM
PM - Equipment BOM
[page 422]Master dataDirect T ransfer - ERPNot applicablePM
PM - Equipment task
list [page 403]Master dataDirect T ransfer - ERPNot applicablePM
Fixed asset (including
balance) [page 259]T ransactional dataDirect T ransfer - ERPNot applicableFI-AA
CO - Activity type
group [page 157]Master dataDirect T ransfer - ERPNot applicableCO
CO - Activity type
[page 154]Master dataDirect T ransfer - ERPNot applicableCO
CO - Cost rate [page
178]Master dataDirect T ransfer - ERPNot applicableCO-OM
CO - Business process
[page 161]Master dataDirect T ransfer - ERPNot applicableCO-OM-ABC
CO - Business process
group [page 164]Master dataDirect T ransfer - ERPNot applicableCO-OM-ABC
CO - Cost center [page
168]Master dataDirect T ransfer - ERPNot applicableCO
CO - Cost center group
[page 172]Master dataDirect T ransfer - ERPNot applicableCO
CO - Cost element
group [page 175]Master dataDirect T ransfer - ERPNot applicableCO
CO - Internal order
[page 182]T ransactional dataDirect T ransfer - ERPNot applicableCO
CO - Profit  center
[page 186]Master dataDirect T ransfer - ERPNot applicableCO
CO - Profit  center
group [page 190]Master dataDirect T ransfer - ERPNot applicableCO
16 PUBLICMigration Objects for SAP S/4HANA
Available Migration ObjectsMigration Object
Name Business Object Type Migration Approach Custom Field Support Component
CO - Secondary cost
element [page 194]Master dataDirect T ransfer - ERPNot applicableCO
CO - Statistical key fig-
ure [page 197]Master dataDirect T ransfer - ERPNot applicableCO
FI - G/L account bal-
ance (classic G/L)
[page 212]T ransactional dataDirect T ransfer - ERPNot applicableFI
FI - G/L account bal-
ance (new G/L) [page
216]T ransactional dataDirect T ransfer - ERPNot applicableFI
FI - Bank account
[page 244]Master dataDirect T ransfer - ERPNot applicableFI
Bank [page 123] Master dataDirect T ransfer - ERPNot applicableFI
FI - Accounts receiva-
ble open item (classic
GL) [page 235]T ransactional dataDirect T ransfer - ERPNot applicableFI
FI - Accounts receiv-
able open item (new
G/L) [page 239]T ransactional dataDirect T ransfer - ERPNot applicableFI
Exchange rate [page
205]Master dataDirect T ransfer - ERPNot applicableFI
FI - G/L account [page
208]Master dataDirect T ransfer - ERPNot applicableFI
FI - House bank [page
247]Master dataDirect T ransfer - ERPNot applicableFI
FI - G/L open item
(classic G/L) [page
220]T ransactional dataDirect T ransfer - ERPNot applicableFI
FI - Ledger group-spe-
cific  open item [page
250]T ransactional dataDirect T ransfer - ERPNot applicableFI
FI - G/L open item
(new G/L) [page 223]T ransactional dataDirect T ransfer - ERPNot applicableFI
TRM - Additional flow
[page 579]T ransactional dataDirect T ransfer - ERPNot applicableFIN-FSCM-TRM-TM
TRM - Bank guarantee
[page 583]T ransactional dataDirect T ransfer - ERPNot applicableFIN-FSCM-TRM-TM
TRM - Business part-
ner role [page 588]Master dataDirect T ransfer - ERPNot applicableFIN-FSCM-TRM-TM
TRM - Commercial pa-
per [page 592]T ransactional dataDirect T ransfer - ERPNot applicableFIN-FSCM-TRM-TM
Migration Objects for SAP S/4HANA
Available Migration Objects PUBLIC 17Migration Object
Name Business Object Type Migration Approach Custom Field Support Component
TRM - Condition detail
[page 265]T ransactional dataDirect T ransfer - ERPNot applicableFIN-FSCM-TRM-TM
TRM - Facility [page
269]T ransactional dataDirect T ransfer - ERPNot applicableFIN-FSCM-TRM-TM
TRM - Fixed term de-
posit [page 274]T ransactional dataDirect T ransfer - ERPNot applicableFIN-FSCM-TRM-TM
TRM - Foreign ex-
change collar contract
[page 284]T ransactional dataDirect T ransfer - ERPNot applicableFIN-FSCM-TRM-TM
TRM - FX option [page
279]T ransactional dataDirect T ransfer - ERPNot applicableFIN-FSCM-TRM-TM
TRM - Foreign ex-
change spot/forward
contract [page 597]T ransactional dataDirect T ransfer - ERPNot applicableFIN-FSCM-TRM-TM
TRM - Foreign ex-
change swap contract
[page 290]T ransactional dataDirect T ransfer - ERPNot applicableFIN-FSCM-TRM-TM
TRM - Interest rate in-
strument [page 602]T ransactional dataDirect T ransfer - ERPNot applicableFIN-FSCM-TRM-TM
TRM - Investment cer-
tificate  [page 296]T ransactional dataDirect T ransfer - ERPNot applicableFIN-FSCM-TRM-TM
TRM - Letter of credit
[page 301]T ransactional dataDirect T ransfer - ERPNot applicableFIN-FSCM-TRM-TM
TRM - Main flow  detail
[page 306]T ransactional dataDirect T ransfer - ERPNot applicableFIN-FSCM-TRM-TM
TRM - Interest rate
swap [page 310]T ransactional dataDirect T ransfer - ERPNot applicableFIN-FSCM-TRM-TM
TRM - Payment detail
[page 315]T ransactional dataDirect T ransfer - ERPNot applicableFIN-FSCM-TRM-TM
TRM - Security bond
[page 607]T ransactional dataDirect T ransfer - ERPNot applicableFIN-FSCM-TRM-TM
TRM - Security class
[page 612]T ransactional dataDirect T ransfer - ERPNot applicableFIN-FSCM-TRM-TM
TRM - Stock [page
616]T ransactional dataDirect T ransfer - ERPNot applicableFIN-FSCM-TRM-TM
PM - Functional loca-
tion [page 408]Master dataDirect T ransfer - ERPNot applicablePM
PM - Functional loca-
tion BOM [page 413]Master dataDirect T ransfer - ERPNot applicablePM
PM - Functional loca-
tion task list [page 417]Master dataDirect T ransfer - ERPNot applicablePM
18 PUBLICMigration Objects for SAP S/4HANA
Available Migration ObjectsMigration Object
Name Business Object Type Migration Approach Custom Field Support Component
Object classification
(general template)
[page 394]Master dataDirect T ransfer - ERPNot applicableCA
PM - General mainte-
nance task list [page
426]Master dataDirect T ransfer - ERPNot applicablePM
QM - Selected set code
[page 529]Master dataDirect T ransfer - ERPNot applicableQM
QM - Selected set
[page 526]Master dataDirect T ransfer - ERPNot applicableQM
QM - Inspection
method [page 511]Master dataDirect T ransfer - ERPNot applicableQM
QM - Inspection plan
[page 515]Master dataDirect T ransfer - ERPNot applicableQM
JVA - Joint venture
master data (step 1)
[page 75]Master dataDirect T ransfer - ERPNot applicableCA-JVA
JVA - Cost center sus-
pense [page 319]Master dataDirect T ransfer - ERPNot applicableCA-JVA
JVA - Joint operating
agreement (step 1)
[page 327]Master dataDirect T ransfer - ERPNot applicableCA-JVA
JVA - Joint venture
partner [page 330]Master dataDirect T ransfer - ERPNot applicableCA-JVA
JVA - Order suspense
[page 323]Master dataDirect T ransfer - ERPNot applicableCA-JVA
JVA - Project suspense
[page 334]Master dataDirect T ransfer - ERPNot applicableCA-JVA
JVA - Venture sus-
pense [page 337]Master dataDirect T ransfer - ERPNot applicableCA-JVA
RE-FX - Land [page
550]Master dataDirect T ransfer - ERPNot applicableRE-FX-BD
PM - Maintenance item
[page 452]Master dataDirect T ransfer - ERPNot applicablePM
PM - Maintenance noti-
fication  [page 444]T ransactional dataDirect T ransfer - ERPNot applicablePM
PM - Maintenance or-
der [page 439]T ransactional dataDirect T ransfer - ERPNot applicablePM
PM - Maintenance plan
[page 448]Master dataDirect T ransfer - ERPNot applicablePM
Migration Objects for SAP S/4HANA
Available Migration Objects PUBLIC 19Migration Object
Name Business Object Type Migration Approach Custom Field Support Component
QM - Master inspec-
tion characteristic
[page 519]Master dataDirect T ransfer - ERPNot applicableQM
Product [page 498] Master dataDirect T ransfer - ERPNot applicableLO
PP - Material BOM
[page 476]Master dataDirect T ransfer - ERPNot applicablePP-BD
MM - Material com-
modity [page 350]Master dataDirect T ransfer - ERPNot applicableLO-MD-MM
MM - Material con-
sumption [page 353]Master dataDirect T ransfer - ERPNot applicableMM
MM - Material inven-
tory balance [page
341]T ransactional dataDirect T ransfer - ERPNot applicableMM-IM
PP - Material MRP as-
signment [page 456]Master dataDirect T ransfer - ERPNot applicablePP-MRP
PM - Measurement
document [page 435]Master dataDirect T ransfer - ERPNot applicablePM-EQM-SF-MPC
PM - Measuring point
[page 431]Master dataDirect T ransfer - ERPNot applicablePM
PP - Production
resource/tool [page
467]Master dataDirect T ransfer - ERPNot applicablePP
PP - Planned inde-
pendent requirement
[page 480]T ransactional dataDirect T ransfer - ERPNot applicablePP-MRP
Pricing condition (pur-
chasing and sales)
[page 495]Master dataDirect T ransfer - ERPNot applicable SD
MM-PUR
PP - Production order
[page 463]T ransactional dataDirect T ransfer - ERPNot applicablePP
PP - Production ver-
sion [page 483]Master dataDirect T ransfer - ERPNot applicablePP
PS - Project [page
506]T ransactional dataDirect T ransfer - ERPNot applicablePS
PS - Network [page
503]T ransactional dataDirect T ransfer - ERPNot applicablePS
MM - Purchase order
[page 381]T ransactional dataDirect T ransfer - ERPNot applicableMM-PUR
MM - Purchase requi-
sition (only open PR)
[page 386]T ransactional dataDirect T ransfer - ERPNot applicableMM-PUR
20 PUBLICMigration Objects for SAP S/4HANA
Available Migration ObjectsMigration Object
Name Business Object Type Migration Approach Custom Field Support Component
MM - Purchase con-
tract [page 357]T ransactional dataDirect T ransfer - ERPNot applicableMM-PUR
MM - Purchasing info
record [page 362]Master dataDirect T ransfer - ERPNot applicableMM-PUR
QM - Quality info re-
cord [page 522]Master dataDirect T ransfer - ERPNot applicableQM
MM - Quota arrange-
ment [page 390]T ransactional dataDirect T ransfer - ERPNot applicableMM-PUR
RE-FX - Real estate
contract [page 532]T ransactional dataDirect T ransfer - ERPNot applicableRE-FX
RE-FX - Rental object
[page 555]Master dataDirect T ransfer - ERPNot applicableRE-FX
PP - Routing [page
471]Master dataDirect T ransfer - ERPNot applicablePP
SD - Sales contract
[page 570]T ransactional dataDirect T ransfer - ERPNot applicableSD
SD - Sales inquiry
[page 559]T ransactional dataDirect T ransfer - ERPNot applicableSD
SD - Sales order (only
open SO) [page 575]T ransactional dataDirect T ransfer - ERPNot applicableSD
PP - Sales order BOM
[page 487]Master dataDirect T ransfer - ERPNot applicablePP-BD
MM - Purchase sched-
uling agreement [page
366]T ransactional dataDirect T ransfer - ERPNot applicableMM-PUR
MM - Service entry
sheet [page 370]T ransactional dataDirect T ransfer - ERPNot applicableMM
MM - Service master
[page 374]Master dataDirect T ransfer - ERPNot applicableMM-SRV
MM - Source list [page
377]Master dataDirect T ransfer - ERPNot applicableMM-PUR
VC - Configuration  pro-
file [page 621]Master dataDirect T ransfer - ERPNot applicableLO-VC
VC - Material variant
[page 346]Master dataDirect T ransfer - ERPNot applicableLO-VC
VC - Object depend-
ency and dependency
net [page 625]Master dataDirect T ransfer - ERPNot applicableLO-VC
Supplier [page 629] Master dataDirect T ransfer - ERPNot applicableMM
Migration Objects for SAP S/4HANA
Available Migration Objects PUBLIC 21Migration Object
Name Business Object Type Migration Approach Custom Field Support Component
PP - Work breakdown
structure BOM [page
491]Master dataDirect T ransfer - ERPNot applicablePP
PP - Work center [page
459]Master dataDirect T ransfer - ERPNot applicablePM
JVA - Joint operating
agreement (step 2)
[page 72]Master data Direct T ransfer - ERP Not applicableCA-JVA
JVA - Joint venture
master data (step 2)
[page 79]Master data Direct T ransfer - ERP Not applicableCA-JVA
TRM - Cap/Floor [page
91]T ransactional data Direct T ransfer - ERP Not applicableFIN-FSCM-TRM-TM
TRM - Deposit at no-
tice [page 96]T ransactional data Direct T ransfer - ERP Not applicableFIN-FSCM-TRM-TM
Document info record
(DMS) [page 53]Master data Direct T ransfer - ERP Not applicableCA-DMS
VC - Variant table
structure [page 113]Master data Direct T ransfer - ERP Not applicableLO-VC
VC - Variant table entry
[page 110]T ransactional data Direct T ransfer - ERP Not applicableLO-VC
JIT - Customer [page
69]Master data Direct T ransfer - ERP Not applicableLE-JIT
EHS - Location [page
58]Master data Direct T ransfer - ERP Not applicableEHS-SUS-FND
EHS - Location hierar-
chy [page 62]Master data Direct T ransfer - ERP Not applicableEHS-SUS-FND
VC - Assign global
dependency to charac-
teristic [page 103]Master data Direct T ransfer - ERP Not applicableCA-CL
Product classification
(legal control) [page
83]Master data Direct T ransfer - ERP Not applicableSLL-ITR-CLS
RE-FX - Business part-
ner role [page 86]Master data Direct T ransfer - ERP Not applicableAP-MD-BP
VC - Interface design
(characteristic group)
[page 106]Master data Direct T ransfer - ERP Not applicableLO-VC
VC - Assign global
dependency to class
[page 65]Master data Direct T ransfer - ERP Not applicableCA-CL
PP - Master Recipe
[page 42]Master data Direct T ransfer - ERP Not applicable PP-PI-MD-MRC
22 PUBLICMigration Objects for SAP S/4HANA
Available Migration ObjectsMigration Object
Name Business Object Type Migration Approach Custom Field Support Component
FSCM - Credit man-
agement role [page
49]Master data Direct T ransfer - ERP Not applicable FIN-FSCM-C
PP - Work center hier-
archy [page 46]Master data Direct T ransfer - ERP Not applicable PP-BD-WKC
Class hierarchy [page
34]Master data Direct T ransfer - ERP Not applicable LO-MD-MM
SD - Material listing
and exclusion [page
37]Master data Direct T ransfer - ERP Not applicable SD-MD-MM-LIS
LO - Handling unit
[page 30]Master data Direct T ransfer - ERP Not applicable LO-HU
AFS - Article [page
676]Master dataDirect T ransfer - AFSNot applicable IS-AFS-MM
LO-FSH
Batch unique at plant
level [page 731]Master dataDirect T ransfer - AFSNot applicableLO-BM
AFS - Dimensional
characteristic [page
694]Master dataDirect T ransfer - AFSNot applicableCA-CL
AFS - Non-dimensional
characteristic [page
702]Master dataDirect T ransfer - AFSNot applicableCA-CL
AFS - Configuration
class for material grid
[page 686]Master dataDirect T ransfer - AFSNot applicableIS-AFS-BD
Customer [page 781] Master dataDirect T ransfer - AFSNot applicableSD
AFS - Distribution
curve [page 682]Master dataDirect T ransfer - AFSNot applicableIS-AFS-BD
AFS - Distribution
curve condition [page
689]Master dataDirect T ransfer - AFSNot applicableIS-AFS-BD
ECM - Change master
[page 789]Master dataDirect T ransfer - AFSNot applicablePLM-PLC
PM - Equipment [page
882]Master dataDirect T ransfer - AFSNot applicablePM
Fixed asset (including
balance) [page 796]T ransactional dataDirect T ransfer - AFSNot applicableFI-AA
CO - Activity type
group [page 738]Master dataDirect T ransfer - AFSNot applicableCO
CO - Activity type
[page 734]Master dataDirect T ransfer - AFSNot applicableCO
Migration Objects for SAP S/4HANA
Available Migration Objects PUBLIC 23Migration Object
Name Business Object Type Migration Approach Custom Field Support Component
CO - Cost rate [page
741]Master dataDirect T ransfer - AFSNot applicableCO-OM
CO - Business process
[page 745]Master dataDirect T ransfer - AFSNot applicableCO-OM-ABC
CO - Business process
group [page 749]Master dataDirect T ransfer - AFSNot applicableCO-OM-ABC
CO - Cost center [page
752]Master dataDirect T ransfer - AFSNot applicableCO
CO - Cost center group
[page 756]Master dataDirect T ransfer - AFSNot applicableCO
CO - Cost element
group [page 760]Master dataDirect T ransfer - AFSNot applicableCO
CO - Internal order
[page 763]T ransactional dataDirect T ransfer - AFSNot applicableCO
CO - Profit  center
[page 767]Master dataDirect T ransfer - AFSNot applicableCO
CO - Profit  center
group [page 771]Master dataDirect T ransfer - AFSNot applicableCO
CO - Secondary cost
element [page 774]Master dataDirect T ransfer - AFSNot applicableCO
CO - Statistical key fig-
ure [page 778]Master dataDirect T ransfer - AFSNot applicableCO
FI - G/L account bal-
ance (classic G/L)
[page 826]T ransactional dataDirect T ransfer - AFSNot applicableFI
FI - G/L account bal-
ance (new G/L) [page
830]T ransactional dataDirect T ransfer - AFSNot applicableFI
FI - Bank account
[page 819]Master dataDirect T ransfer - AFSNot applicableFI
Bank [page 728] Master dataDirect T ransfer - AFSNot applicableFI
FI - Accounts receiva-
ble open item (classic
GL) [page 802]T ransactional dataDirect T ransfer - AFSNot applicableFI
FI - Accounts receiv-
able open item (new
G/L) [page 807]T ransactional dataDirect T ransfer - AFSNot applicableFI
Exchange rate [page
793]Master dataDirect T ransfer - AFSNot applicableFI
FI - G/L account [page
822]Master dataDirect T ransfer - AFSNot applicableFI
24 PUBLICMigration Objects for SAP S/4HANA
Available Migration ObjectsMigration Object
Name Business Object Type Migration Approach Custom Field Support Component
FI - House bank [page
842]Master dataDirect T ransfer - AFSNot applicableFI
FI - G/L open item
(classic G/L) [page
834]T ransactional dataDirect T ransfer - AFSNot applicableFI
FI - Ledger group-spe-
cific  open item [page
845]T ransactional dataDirect T ransfer - AFSNot applicableFI
FI - G/L open item
(new G/L) [page 838]T ransactional dataDirect T ransfer - AFSNot applicableFI
FI - Accounts paya-
ble open item (classic
G/L) [page 811]T ransactional dataDirect T ransfer - AFSNot applicableFI
FI - Accounts payable
open item (new G/L)
[page 815]T ransactional dataDirect T ransfer - AFSNot applicableFI
Object classification
(general template)
[page 849]Master dataDirect T ransfer - AFSNot applicableCA
PP - Material BOM
[page 886]Master dataDirect T ransfer - AFSNot applicablePP-BD
MM - Material inven-
tory balance [page
853]T ransactional dataDirect T ransfer - AFSNot applicableMM-IM
AFS - Planned inde-
pendent requirement
at material level [page
710]T ransactional dataDirect T ransfer - AFSNot applicablePP-MRP
AFS - Planned inde-
pendent requirement
at SKU level [page
698]T ransactional dataDirect T ransfer - AFSNot applicablePP-MRP
PP - Planned inde-
pendent requirement
[page 896]T ransactional dataDirect T ransfer - AFSNot applicablePP-MRP
Pricing condition (pur-
chasing and sales)
[page 916]Master dataDirect T ransfer - AFSNot applicable SD
MM-PUR
PP - Production order
[page 903]T ransactional dataDirect T ransfer - AFSNot applicablePP
PP - Production ver-
sion [page 899]Master dataDirect T ransfer - AFSNot applicablePP
MM - Purchase order
[page 863]T ransactional dataDirect T ransfer - AFSNot applicableMM-PUR
Migration Objects for SAP S/4HANA
Available Migration Objects PUBLIC 25Migration Object
Name Business Object Type Migration Approach Custom Field Support Component
MM - Purchase con-
tract [page 858]T ransactional dataDirect T ransfer - AFSNot applicableMM-PUR
MM - Purchasing info
record [page 869]Master dataDirect T ransfer - AFSNot applicableMM-PUR
AFS - Quantity distri-
bution profile  [page
707]Master dataDirect T ransfer - AFSNot applicableIS-AFS-PP-MAN
PP - Routing [page
891]Master dataDirect T ransfer - AFSNot applicablePP
SD - Sales contract
[page 920]T ransactional dataDirect T ransfer - AFSNot applicableSD
SD - Sales order [page
925]T ransactional dataDirect T ransfer - AFSNot applicableSD
PP - Sales order BOM
[page 907]Master dataDirect T ransfer - AFSNot applicablePP-BD
MM - Purchase sched-
uling agreement [page
873]T ransactional dataDirect T ransfer - AFSNot applicableMM-PUR
AFS - Season [page
714]Master dataDirect T ransfer - AFSNot applicableIS-AFS-SD
AFS - Segmentation
default value [page
717]Master dataDirect T ransfer - AFSNot applicableIS-AFS-BD
AFS - Segmentation
strategy [page 721]Master dataDirect T ransfer - AFSNot applicableIS-AFS-BD
AFS - Segmentation
structure [page 724]Master dataDirect T ransfer - AFSNot applicableIS-AFS-BD
MM - Source list [page
878]Master dataDirect T ransfer - AFSNot applicableMM-PUR
Supplier [page 930] Master dataDirect T ransfer - AFSNot applicableMM
PP - Work center [page
912]Master dataDirect T ransfer - AFSNot applicablePM
Document info record
(DMS) [page 656]Master data Direct T ransfer - AFS Not applicableCA-DMS
Class [page 652] Master data Direct T ransfer - AFS Not applicableCA-CL
FI - SEPA mandate
[page 662]Master data Direct T ransfer - AFS Not applicableFI-AR
VC - Assign global
dependency to charac-
teristic [page 669]Master data Direct T ransfer - AFS Not applicableCA-CL
26 PUBLICMigration Objects for SAP S/4HANA
Available Migration ObjectsMigration Object
Name Business Object Type Migration Approach Custom Field Support Component
VC - Assign global
dependency to class
[page 673]Master data Direct T ransfer - AFS Not applicableCA-CL
VC - Object depend-
ency and dependency
net [page 666]Master data Direct T ransfer - AFS Not applicableLO-VC
AFS - Purchase grid
condition record [page
641]Master data Direct T ransfer - AFS Not applicableIS-AFS-BD
AFS - Sales grid condi-
tion record [page 645]Master data Direct T ransfer - AFS Not applicableIS-AFS-SD
AFS - Value-added
service [page 648]Master data Direct T ransfer - AFS Not applicableIS-AFS-SD
Class hierarchy [page
637]Master data Direct T ransfer - AFS Not applicable LO-MD-MM
Warehouse fixed  bin
assignments [page
940]Master dataDirect T ransfer - EWMNot applicableSCM-EWM
Warehouse storage bin
[page 943]Master dataDirect T ransfer - EWMNot applicableSCM-EWM
Warehouse storage bin
sorting [page 950]Master dataDirect T ransfer - EWMNot applicableSCM-EWM
Warehouse product
[page 946]Master dataDirect T ransfer - EWMNot applicableSCM-EWM
Warehouse stock
[page 953]Master dataDirect T ransfer - EWMNot applicableSCM-EWM
Warehouse application
log settings [page 959]Master dataDirect T ransfer - EWMNot applicableSCM-EWM
Physical inventory set-
tings [page 963]Master dataDirect T ransfer - EWMNot applicableSCM-EWM
Shipping and receiving
settings [page 966]Master dataDirect T ransfer - EWMNot applicableSCM-EWM
Production supply area
[page 971]Master dataDirect T ransfer - EWMNot applicableSCM-EWM
Resource and user
maintenance [page
975]Master dataDirect T ransfer - EWMNot applicableSCM-EWM
Resource group and
queue settings [page
979]Master dataDirect T ransfer - EWMNot applicableSCM-EWM
Resource execution
settings [page 982]Master dataDirect T ransfer - EWMNot applicableSCM-EWM
Migration Objects for SAP S/4HANA
Available Migration Objects PUBLIC 27Migration Object
Name Business Object Type Migration Approach Custom Field Support Component
Work center determi-
nations [page 986]Master dataDirect T ransfer - EWMNot applicableSCM-EWM
Work center depend-
ent settings [page
990]Master dataDirect T ransfer - EWMNot applicableSCM-EWM
Material flow  system
[page 994]Master dataDirect T ransfer - EWMNot applicableSCM-EWM
Route [page 998] Master dataDirect T ransfer - EWMNot applicableSCM-EWM
Carrier profile  [page
1002]Master dataDirect T ransfer - EWMNot applicableSCM-EWM
Zone [page 1005] Master dataDirect T ransfer - EWMNot applicableSCM-EWM
Zones hierarchy [page
1008]Master dataDirect T ransfer - EWMNot applicableSCM-EWM
Condition records
(warehouse depend-
ent) [page 1015]Master dataDirect T ransfer - EWMNot applicableSCM-EWM
Condition records
(warehouse independ-
ent) [page 1011]Master dataDirect T ransfer - EWMNot applicableSCM-EWM
Miscellaneous EWM
master data settings
[page 1022]Master dataDirect T ransfer - EWMNot applicableSCM-EWM
RFID settings [page
1027]Master dataDirect T ransfer - EWMNot applicableSCM-EWM
Sample-drawing pro-
cedure [page 1019]Master dataDirect T ransfer - EWMNot applicableSCM-EWM
Wave templates [page
1031]Master dataDirect T ransfer - EWMNot applicableSCM-EWM
Quality inspection
rules [page 1034]Master dataDirect T ransfer - EWMNot applicableSCM-EWM
Quality inspection
rules (cross-ware-
house) [page 1038]Master dataDirect T ransfer - EWMNot applicableSCM-EWM
Warehouse handling
unit [page 1042]Master dataDirect T ransfer - EWMNot applicableSCM-EWM
Warehouse storage bin
user status [page 1047]Master dataDirect T ransfer - EWMNot applicableSCM-EWM
CRM - Activities [page
936]T ransactional dataDirect T ransfer - CRMNot applicableCRM-BTX-ACT
eSPP - Location [page
1083]Master data Direct T ransfer - eSPP Not applicable SCM-BAS-MD
28 PUBLICMigration Objects for SAP S/4HANA
Available Migration ObjectsMigration Object
Name Business Object Type Migration Approach Custom Field Support Component
eSPP - Product [page
1085]Master data Direct T ransfer - eSPP Not applicable SCM-BAS-MD
eSPP - MRP Area
[page 1088]Master data Direct T ransfer - eSPP Not applicable SCM-BAS-MD
eSPP - Location Prod-
uct [page 1090]Master data Direct T ransfer - eSPP Not applicable SCM-BAS-MD
eSPP - T ransportation
Lane [page 1092]Master data Direct T ransfer - eSPP Not applicable SCM-BAS-MD
eSPP - Bill of Distribu-
tion (BoD) [page 1094]Master data Direct T ransfer - eSPP Not applicable SCM-BAS-MD
eSPP - Product BoD
Assignment [page
1097]Master data Direct T ransfer - eSPP Not applicable SCM-BAS-MD
HCM - Object [page
1099]Master data Direct T ransfer - ERP Not applicable PA-PA-XX-MIG
HCM - Employee [page
1105]Master data Direct T ransfer - ERP Not applicable PA-PA-XX-MIG
HCM - Relationship
[page 1111]Master data Direct T ransfer - ERP Not applicable PA-PA-XX-MIG
 Caution
Data protection legislation may require that personal data is deleted once the data has served its originally
defined  purpose and is also no longer subject to additional legal data retention requirements. If data
protection legislation is applicable in your case, then migrating personal data, which should have been
deleted could be interpreted as the processing of personal data without any legally justified  purpose.
 Note
For some migration objects, there are additions to the migration object name. These additions include
"restricted" and "deprecated". Restricted means that not all fields  and structures of the relevant business
processes are covered by this migration object, deprecated means that there’s a newer version of this
migration object available. Deprecated objects will be deleted after a couple of releases. Make sure you
always read the migration object documentation for these objects carefully. Also see SAP Note 2698032
for more details on deprecated migration objects.
 Note
Migration objects are built for initial migration of your data to your SAP S/4HANA or SAP S/4HANA Cloud
system. This means that you can create data with a migration object, but you can't change or update
existing data with it.
 Note
The predelivered data migration objects are built for SAP S/4HANA Cloud and the respective SAP Best
Practices Content. Y ou can use these objects also for SAP S/4HANA and the respective SAP Best
Migration Objects for SAP S/4HANA
Available Migration Objects PUBLIC 29Practices Content. Using SAP S/4HANA, the delivered standard migration objects are templates that can
be enhanced according to your needs. The enhancement is only limited by the functionality of the tool
and the capabilities of the underlying API to load the data. Starting with SAP S/4HANA 1610 FPS2, you
can enhance customer projects based on these delivered standard migration objects or you can create
your own objects using the SAP S/4HANA migration object modeler (transaction LTMOM ). For further
information, please see SAP Note 2481235
 .
Further Information
•Use these SAP Help Portal aliases to access the following sections of our product assistance:
Type this in your browser... T o jump to...
http:/ /help.sap.com/S4_OP_MO this very topic: Available Migration Objects
http:/ /help.sap.com/S4_OP_DM the landing page for data migration
•If you want to view information about the differences  between the current release and the previous
release, see SAP S/4HANA – Release Comparison of Migration Object T emplates  (for the File/Staging
T able  migration approach only, and for customers and partners only).
•If you’re using the Fixed asset (incl. balances and transactions) [page 1684]  migration object, we provide
background information on asset classes and available fields  per asset class here  (for customers and
partners only).
•For information regarding mapping of unit of measures, see SAP Knowledge Base Article 2907822
 .

