{"Request": {"Number": "2283087", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 323, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018267512017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002283087?language=E&token=DC059D51945A94D11FBA12AF1BB48CAA"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002283087", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002283087/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2283087"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 31}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.12.2023"}, "SAPComponentKey": {"_label": "Component", "value": "PLM-ECC-CAT"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Engineering Control Center interface to CATIA V5"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Product Lifecycle Management", "value": "PLM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PLM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Engineering Control Center (SAP ECTR)", "value": "PLM-ECC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PLM-ECC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Engineering Control Center interface to CATIA V5", "value": "PLM-ECC-CAT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PLM-ECC-CAT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2283087 - Patch notes - SAP ECTR interface to CATIA 1.1.x"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are interested in changes or new features in the SAP ECTR interface to CATIA 1.1.x.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>ECTRV5, patch notes, Engineering Control Center, ECTR 5.1, ECTR 5.2, ECTR for S/4 1.1</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Additional information</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Version ********</p>\r\n<p>Bug fix: When using DLNames, the change status of a file was not recognized correctly.</p>\r\n<p>Version ********</p>\r\n<p>Bug fix: When using DLNames, the change status of a file was not recognized correctly.</p>\r\n<p>Version ********</p>\r\n<p>Bug fix: When CATDrawing documents were sent to the ASV, unmodified CATDrawing documents could have been modified in the CATIA session. This behavior has been corrected.<br />Bug fix: Translation of \"BOM Relevance\" corrected in some languages.</p>\r\n<p>Version ********</p>\r\n<p>New function: Support for CATIA V5-6R2023.<br />New function: Support for Windows 11.<br />New function: Several documents can be assigned to predefined document info records via drag and drop and then imported together. It is also possible to assign a material.<br />New function: Support for the \"Item Number\" and \"Item Group\" parameters in the dialog box for BOM parameter maintenance.<br />New function: The height and width of the BOM parameter maintenance dialog box can be configured.<br />New function: CATIAV5 parameters can be displayed in the BOM parameter maintenance dialog box.<br />New function: Support for configuration override from SAP ECTR. For details, see the configuration documentation.<br />Change: Cloning: More precise determination of the main return value.<br />Change: Cloning: Input XML file is checked for duplicate use of a target file name.<br />Change: The default templates for title blocks have been moved from the configuration directory to the template directory.<br />Change: Revision of the communication between SAP ECTR and the integration. It is no longer necessary to configure a key stroke.<br />Change: The setting for transferring CATIA instance IDs when creating/updating material BOMs is now activated by default.<br />Change: Two options of the BOM parameter maintenance dialog box have been moved from the configuration file pdmconfig.xml to the configuration file UIAdvancedBOMWindow.xml. These are AlterQuantityForAdvancedBom and AdditionalUnitsOfMeasureForAdvancedBOM.<br />Change: The default configuration has been moved to the folder \"basis\" of the installation.<br />Bug fix: Fixed a crash if an element was selected in the import error dialog.<br />Bug fix: During the update of a selected node in the ASV, the status of the selected node itself is now also updated, not just that of the lower-level nodes.<br />Bug fix: Corrected missing values for default.txt in configuration dumper.<br />Bug fix: Cloning: A single % in input file names no longer leads to an error in the cloner.<br />Bug fix: Cloner: Renamed calculations and results for analysis documents are now always copied back to the additional directory.<br />Bug fix: Stabilization of the \"Save as New Document\" command when using the option SaveAsReplacesInContext. If the replacement was deactivated in the context of an assembly, the system may have incorrectly used files with the same name from the temporary directory for new files and files that existed only in the CATIA session.</p>\r\n<p>Version 1.1.17.1</p>\r\n<p>Bug fix: Unwanted display of ASV deactivated for several commands.</p>\r\n<p>Version 1.1.16.1</p>\r\n<p>Bug fix: Unwanted display of ASV deactivated for several commands.<br />Bug fix: When reopening locally modified and saved documents with the wizard, the system might not display any documents. This has been fixed.</p>\r\n<p>Version 1.1.15.1</p>\r\n<p>Bug fix: Unwanted display of ASV deactivated for several commands.</p>\r\n<p>Version 1.1.17.0</p>\r\n<p>New function: Support for CATIA V5-6R2022.<br />New function: Support of user commands for executing user exits as standalone commands in the UI.<br />Change: The processing of commands from the ASV can now be configured.<br />Bug fix: Error when updating the ASV is corrected if the display of changes in the ASV was deactivated for the command \"Save as New Document\".<br />Bug fix: Crash when displaying cyclic assemblies in ASV has been corrected.<br />Bug fix: Cloner: Fixed missing update of links from renamed analysis documents to calculations and results in case of load strategy single.<br />Bug fix: When reopening locally modified and saved documents with the wizard, the system might not display any documents. This has been fixed.<br />Bug fix: A change in pdmconfig.xml during operation caused the integration to crash. This has been fixed.<br />Bug fix: The system now also issues an error for the standard toolbar if you try to insert a CATPart with a higher release than the started CATIA.<br />Bug fix: English is used as a fallback language for the texts if no valid language is specified in the configuration and the current operating system language is not supported.<br />Bug fix: The attempt to replace originals with a document that is saved with a higher CATIA release than the started CATIA now results in an error message.</p>\r\n<p>Version ********</p>\r\n<p>New function: Support of new user exit category PDMExit for execution of ECTR macros.<br />New function: Support of new user exit type CMDExit for execution of batch scripts.<br />New function: Transfer of meta information to CATIA (for example, part number) when cloning. Optionally, attributes can be updated during cloning in CATIA.<br />New function: Quantity and unit can optionally be configured as properties in BOM parameter maintenance.<br />New function: You can use the option \"SaveAsReplacesInContext\" to deactivate the replacement of a DIR during the command \"Save as New Document\".<br />Change: For the application fpbridge.exe, there is a separate configuration file for the configuration of the delivery logging.<br />Bug fix: Fixed an error in the handling of markup characters in the return HTML of the cloner.<br />Bug fix: The overwriting of the CATIAV5 environment name using pdmconfig.xml works again, including the saving of the value to a DIR.<br />Bug fix: Correction when overriding a default user exit for a command-specific user exit.<br />Bug fix: Correction of an endless loop if a user exit was triggered from the ASV.<br />Bug fix: Fixed an issue which led to a crash when processing very deeply nested component structures.</p>\r\n<p>Version 1.1.15.0</p>\r\n<p>New function: Support for CATIA V5-6R2021.<br />New function: Option to define the storage status of a document in SAP after the import. By default, the documents are in edit mode.<br />New function: Option to define the storage status of a document in SAP with the command 'Save as New Document'. By default, the documents are in edit mode.<br />New function: Option to define the storage status of a document in SAP with the command 'Save as New Version'. By default, the documents are in edit mode.<br />Change: The automatic refresh of the application structure overview also supports the command \"Unlock Originals in SAP PLM\".<br />Change: The option \"AutomaticCheckinOnNewVersion\" has been replaced by the option \"NewVersionSaveStrategy\".<br />Change: Restructuring of the internal processing of the cloner.<br />Bug fix: Fixed an incorrect calculation for waiting for an application to be started by StartApplicationCat.<br />Bug fix: An unnecessary error message was displayed if the \"Display\" command was triggered in the ASV before an assembly had been loaded from SAP.</p>\r\n<p>Version 1.1.14.0</p>\r\n<p>New function: Support for the import of files by assigning a predefined document info record using \"drag and drop\" to a file in the ASV.<br />New function: In the extras menu, the command \"Display Product Structures of the Selection\" is available to display the current structure in the application structure overview starting from the currently selected element.<br />New function: Support of ECTR option plm.shutdown.onApplication.exit to trigger ECTR close when closing integration (<a target=\"_blank\" href=\"mailto:Influence@SAP\">Influence@SAP</a> Request 222629).<br />New function: Introduction of the command \"Save and Close\" (<a target=\"_blank\" href=\"mailto:Influence@SAP\">Influence@SAP</a> Request 207735).<br />Change: The metadata transfer during cloning is now activated using the entry CreateMetainformation in the configuration file cloneppconfig.xml.<br />Change: After you complete the commands \"Import\", \"Save as New Version\", \"Replace Version\", and \"Save as New Document\", ECTR is in the foreground.<br />Change: It is now possible to configure CATIA V5 to be exited along with the integration window.<br />Bug fix: Calling the ASV with an empty CATIAV5 session lead to a termination of the SAP Engineering Control Center Interface to CATIA V5 and the blocking of CATIA V5.<br />Bug fix: The ASV is now updated if \"Replace Version\" is executed for the root document and version numbers are activated in the file name.<br />Bug fix: The check-in for assemblies is possible again if SetComponentIDForComponents is activated.<br />Bug fix: An activated cache clearing (SINGLE or ALL) could lead to the fact that a child added to a product was no longer saved during cloning in the standard system. This has now been corrected.<br />Bug fix: Clone++ returns an error message if the used configuration files contain syntax errors.</p>\r\n<p>Version 1.1.13.0</p>\r\n<p>Change: It is now possible to replace CATParts with CATProducts and vice versa using the \"Replace Original\" command in the application.<br />Change: The automated refresh of the application structure overview can now also be configured for the commands \"Save as New Version\" (with activated version number in the file name), \"Replace Version\", \"Add Component\", \"Replace Component\", or \"Refresh Original\".<br />Change: Cloning: Cgr (*.cgr\") and V4 model files (*.model\") can now be copied to a new directory. Renaming still results in an error.<br />Change: The behavior for the display of a CAD document tree in ASV with regard to referenced drawing documents has been changed. If, for example, level 2 contains a drawing document but the required level is set to 1, the system only displays it after the parent node has been expanded. <br />Change: Due to an internal restructuring, the previewSearchLastImage option has been moved from pdmconfig.xml to cadconfig.xml.<br />Change: Adjustments to the file structure of the startup program for starting CATIA and the integration.<br />Change: The drawing header script now logs in to the \"SAP Engineering Control Center\" log directory in the subfolder applications\\cat.<br />Change: VPMDB files can be ignored during the scan with the cadconfig.xml option treatVPMDBFilesAsBrokenLink so that, for example, an import of files with links to them is possible.<br />Change: Information about the relationship type is sent to the application structure view for design tables and MMLs (<a target=\"_blank\" href=\"mailto:Influence@SAP\">Influence@SAP</a> Request 240349).<br />Change: If the dialog for processing suppression items is called in the bill of material of a document, this document can now be processed automatically.<br />Change: Design tables can now be processed using the application structure view or be stored in SAP.<br />Bug fix: During the creation of a new version of a document from the ASV, dependent documents that had already been opened in CAD were closed. This was incorrect. It has now been corrected. These documents are still open at the end.<br />Bug fix: Missing translation added in error dialog during versioning of several documents.<br />Bug fix: CATIA no longer remains locked if ALT-F4 or Escape is pressed while a command is executed.</p>\r\n<p>Version ********</p>\r\n<p>New function: Support for CATIA V5-6R2020.<br />New function: The command \"Load Original from SAP PLM and add to current CAD assembly\" now supports the document types cgr and model. These can be inserted into an assembly loaded into CAD.<br />Change: CheckInByBrokenLink has been replaced by ContinueOperationWhenDetectingBrokenLinks. All operations can now be continued automatically if a broken link is detected.<br />Change: When you create a new version or copy of a master document, only the dependent documents that are changed by SAP and that can be displayed in CAD are opened in CAD.<br />Change: To avoid a follow-on error in the CATIA backup management, the CATIA undo/redo cache is deleted after each memory operation with the integration. This can also be deactivated if required.<br />Bug fix: Links to scenes in drawing documents are captured correctly again (as of CATIA V5-6R2016).</p>\r\n<p>Version 1.1.11.0</p>\r\n<p>New function: Changes to file names triggered by the commands \"Save as New Document\", \"Save as New Version\" or \"Import\" result in an update of their entries in the application structure overview.<br />New function: As of ECTR 5.2.2, in the application structure overview, you can use the tree manager to trigger a complete structure entry in the CAD and display it the application structure overview.<br />New function: Information about the suppression of items in the material BOM can be configured in the CATIAV5 integration and then be transferred to SAP.<br />New function: You can configure that it shall not be possible to save a document in SAP if broken links exist.<br />Change: The installation prerequisites have been updated. For more information, see the installation documentation.<br />Change: When you execute the command \"Save as New Document\", you can now decide whether to directly discard the source document.<br />Bug fix: After you expand a node in the ASV, the selection is set for this node.</p>\r\n<p>Version 1.1.10.0</p>\r\n<p>New function: The drawings loaded for a 3D can be displayed as nodes in the application structure overview under the 3D in the CATIA session.<br />New function: The command \"Save as New document\" is supported in the ASV.<br />New function: The command \"Refresh Application Structure Overview\" now supports user exits.<br />New function: When you save a master document as a new document, dependent documents can now be provided in CAD and PLM sessions and be saved in PLM.<br />New function: For the application structure overview, you can define whether CATParts should not be transferred to draft mode during the structure analysis.<br />New function: If ECTR is exited, the integration can also be exited.<br />New function: Sending an instance ID to the table DMUCAD can be enabled as an option.<br />New function: The partial loading of an assembly is supported by the integration. (Influence@SAP Request 201649, 217002).<br />Change: S-FILENAME, S-FILEPATH, S-EXTENSION, and S-FULLPATH accelerated for GetValue rules during import.<br />Change: Inconsistency removed for composite rule check name and GetValue rule entry for S-FILENAME. S-FILENAME now returns the file name without a path in the GetValue rules. S-FULLPATH has been newly added to the GetValue rules. The old GetValue entry for S-FILENAME now returns S-FULLPATH.<br />Change: You can now set the scan depth of the application structure overview using the \"Preferences\" menu of the ECTR. The changes take effect immediately.<br />Change: When you rename construction tables within components, the file attribute \"Read Only\" will be removed from now on. This is required to ensure that, for a construction table that has been included more than once, all links within the assembly are replaced correctly.<br />Change: If there are new documents in the CATIA session and the ASV is used, a complete structure analysis is performed for this assembly and the contained new documents are copied to the Windows temporary directory.<br />Change: For an improved performance, CATIA CGR cache files will now be copied using an ECTR plug-in.<br />Bug fix: If an error occurs during \"Replace Version\", the CATIA session is restored completely.<br />Bug fix: In the application structure overview, all children are now displayed again for all instances of a document when you use the scan depth \"All\".<br />Bug fix: The behavior with regard to the handling of new documents in the CATIA session has been aligned for all CATIA document categories on \"Save as New Document\".<br />Bug fix: The issue of a crash that occurred when the command \"Replace Version\" was called via the application structure overview and the application was exited immediately afterwards has been fixed.<br />Bug fix: When performing the structure analysis for the application structure overview, CATParts are now transferred to draft mode only on demand.</p>\r\n<p>Version *******</p>\r\n<p>Bug fix:&#x00A0;Correction of the display in the application structure view for multiple installation and complete structure explosion.</p>\r\n<p>Version *******</p>\r\n<p>New function: Support for CATIA V5-6 R2019.<br />New function: Support of SAP ECTR for SAP S4HANA 1.1.<br />New function: In the ASV, the command \"Replace version\" is supported.<br />New function: In the ASV the column \"Quantity\" can now be displayed to show the quantity of an installation in the structure.<br />Change: New symbols for CATIA V5 documents in ECTR so that high-resolution symbols are supported.<br />Change: Non-master sample DTypes for document categories that do not always reference your master reference.<br />Bug fix: Stabilization of the processing of callbacks of the application structure overview.<br />Bug fix: Cyclical links are now partly recognized during the cloning, and depending on the settings, are removed or the process is terminated.<br />Bug fix: Improved speed when you display the document structure in the application structure overview.<br />Bug fix: Setting the orange in CATIA now also works with deactivated CATIA cache.<br />Bug fix: An invalid set of rules in the import process now triggers only one error for each call.</p>\r\n<p>Version 1.1.8.0</p>\r\n<p>New function: The orange selection in CATIA can be transferred from ASV.<br />New function: Support of SAP Engineering Control Center 5.2.<br />New function: An error message that is issued when you start CATIA using cenitFLEX+ can be suppressed.<br />Change: The refresh button of the ASV behaves in the same way for selected nodes as for opening the corresponding plus symbol.<br />Change: The refresh button of the ASV deletes the ASV, if no documents are loaded in the CAD session and no node is selected.<br />Change: The refresh button of the ASV no longer switches the active window in the CAD in the standard system if a node is selected and the corresponding document is opened in a separate window. You can use a configuration to restore the former behavior.<br />Change: Configuration entries in the format ${ectr_env_...} are no longer supported. Only ${env_...} may be used.<br />Change: Starting with this version, it is no longer required to register the CATIA communication component of the integration in the system.<br />Change: Insertion is terminated if no independent save is available, and you reject the automatic save.<br />Change: Error messages are described in more detail in the clone module if an error can be recognized before the execution, for example, if a CATDrawing is to be inserted in a CATPart. The parameter AbortOnError now also has an effect in these cases.<br />Change: The insert command checks whether an insertion is possible only after the part selection dialog.<br />Change: The command for replacing a component checks whether a replacement is possible in the selected document only after the part selection dialog.<br />Change: The command for replacing a version checks whether a replacement is possible in the selected document only after the version selection dialog. If the root or no element is selected the root document can be replaced by another version. This now also works for analysis, drawing, and process documents. This behavior is the same as for closing and reloading a different version of the same document.<br />Change: Due to the adjustment of the output type, error messages at the start of CAD applications can now be displayed better. Prerequisite SAP ECTR 5.1.14.0.<br />Bug fix: The selection of several instances of a component no longer leads to an error for commands that permit only a single element in the selection.<br />Bug fix: The command \"Replace version\" can now also be used with active DLName.</p>\r\n<p>Version 1.1.7.0</p>\r\n<p>New function: The function for saving the structure in order to save the intermediate status of a CAD structure can now be configured as a command.<br />New function: The cloning module now supports the transfer of metadata to ECTR V5.<br />New function: When you create a new version of a master document, dependent documents can now be provided in CAD and PLM sessions and be saved in PLM.<br />Change: The \"Save\" command now also takes into account the option CheckForReadOnlyDocuments for the identification of locally modified documents.<br />Change: The transfer of SAP attributes to CATIA has been made more robust.<br />Change: When you use the \"Replace Version\" function, the system now displays an improved dialog for the version selection.<br />Change: API adjustment for the update of documents in the CAD session with newer data from SAP.<br />Change: The CATScript for the creation of the drawing frame has been moved to .\\cat\\basis\\sys\\win\\scripts.<br />Bug fix: You can now also configure a user exit before sending documents to the application structure overview.<br />Bug fix: Correction for a termination that occurs when you switch between the toolbar and the application structure overview for the \"New Version\" command.<br />Bug fix: The \"New Version\" command now evaluates all selections for CATDrawing documents correctly.<br />Bug fix: Improved error message for the case that all documents are already in SAP at the time of \"Import\".<br />Bug fix: When you trigger a save process from the toolbar and several windows are open, the system reactivates the last active window.<br />Bug fix: Increased stability during the communication with SAP ECTR if a large number of files exist in the SAP ECTR working directory.</p>\r\n<p>Version *******</p>\r\n<p>New function: Support for CATIA V5-6 R2018.<br />New function: Support of the working directory change of ECTR.<br />New function: Cloning now supports the creation of documents with a new UUID.<br />New function: Optionally, you can activate a dialog for the start-up of checked-in documents.<br />New function: You can configure a structural check-in to the ASV.<br />New function: After the import of documents, there is an option to display a dialog in which details for each document can be displayed if an error occurs.<br />New function: You can make a setting that the drawing header script only updates the contents of the text fields contained in the drawing. <br />Change: When switching over to another CATIA version, you no longer have to execute \"CNext.exe -regserver\" prior to the switchover.<br />Change: The system now always displays the error dialog when you use an invalid set of rules for the import.<br />Change: The icon of the application can now be defined as a dark icon (new default).<br />Bug fix: Replacing the version of a document if the version number is in the file name now works via integration.<br />Bug fix: When you clone using the load strategy \"all\", you can activate a last saving step to determine and save changed documents.<br />Bug fix: An internal error causing cloning not to start sometimes has been fixed.<br />Bug fix: Cloning did not start if no release or environment was maintained for the DIS.<br />Bug fix: The test criterion for the recognition of a corrupt drawing has been adjusted.<br />Bug fix: The creation of nonmasters based on a template loaded locally works again.</p>\r\n<p>Version 1.1.5.0</p>\r\n<p dir=\"ltr\">New function: Under certain conditions, you can perform the versioning process for several documents. The Customizing documentation contains the relevant notes and settings.<br />New function: Clone++ has been improved to correctly recognize a running CATIA and issue a warning (if necessary) or start a new CATIA so that the cloning is performed in the correct product context.<br />New function: You can define additional search paths for the configuration. Should the application find identical configurations, the configuration that was last found wins.<br />Change: In the case of a license violation, the application now displays an error message.<br />Change: If new files exist in the CATIA session, some commands now ask the user for permission to automatically save these files.<br />Bug fix: The memory leak that occurred when dealing with parameters has been eliminated.<br />Bug fix: StartApplicationCat now reports an error if a configuration is incorrect.<br />Bug fix: For the \"Import\" command, the application now also executes one user exit for each processed file for all files if the command is started from the application structure overview.</p>\r\n<p>Version 1.1.4.1 <br />&#x3000; <br />New function: Clonepp returns an HTML page to ECTR to be displayed in the case of an error. <br />New function: The startup program for CATIA now supports a mixed operation of standard and project solutions with cenitFLEX+. <br />Change: To determine the folder of the title block templates, the application now always evaluates the CATIA environment variable TB_templateDir for the path first. The installation directory transferred during the call is now a fallback option. <br />Bug fix: Correction for the update of cache CGRs for a DIR. <br />Bug fix: Cloning did not work if PLM_USE_SAPCONFIG was set to a non-empty value, not being TRUE. <br />Bug fix: Optimization of the recognition of the backup status of CATIA documents in specific situations. <br />Bug fix: Correction with regard to saving documents in SAP if no primary original had been assigned to the DIR corresponding to the document yet. <br />Bug fix: The execution of the title block script for non-drawings no longer deactivates the CATIA backup management. <br />&#x3000; <br />Version ******* <br />&#x3000; <br />New function: Support for CATIA V5-6 R2017. <br />New function: The application now supports the use of a configuration which was provided by SAP ECTR. <br />New function: You can configure directories with CATIA documents that are considered as libraries. <br />New function: You can now configure file types for which specific user exits are to be executed. <br />Change: You can now insert models and CGR files. <br />Change: \"Insert\" and \"Replace\" have configuration parameters using which you can define or restrict permitted document categories. <br />Change: \"Replace\" now offers to automatically save the data in the session of loaded new or modified documents instead of terminating. <br />Change: Improved error message/note when a CATIA V4 model is sent to the ASV. <br />Change: Configurable display of localized error/warning messages in Clone++. <br />Change: The command \"Save and Display\" now already saves modified documents during structure analysis instead of before the actual check-in. <br />Change: SAP ECTR is no longer started by the integration should the application not be running. <br />Bug fix: The command \"Save and Display\" now executes only user exits for documents that will actually be checked in. <br />Bug fix: The command \"Save and Display\" now displays only a warning message if no document is checked in. <br />Bug fix: The commands \"Save\" and \"Save and Display\" of the ASV now display a warning message again if no document of the selection could be applied. <br />Bug fix: When attempting to write a parameter for which only read access exists, the program no longer terminates. <br />&#x3000; <br />Version 1.1.3.2 <br />&#x3000; <br />Bug fix: When attempting to scan an assembly with a large number of subordinate components, the program no longer terminates.</p>\r\n<p>Version 1.1.3.1<br /><br />Change: The command \"Save\" now displays an error message if no document can be stored in SAP within the selection.</p>\r\n<p>&#x00A0;</p>\r\n<p>Version: 1.1.3.0</p>\r\n<p>New function: Default rule sets via the file pdmmetainformationsettings.xml for the mapping of attributes by the import command.<br />New function: The parameter ParameterScanDepth in cadconfig.xml controls the quantity of P parameters to be searched for the mapping. The standard setting of TREE is sufficient in the standard case.<br />New function: A new standard parameter S-MATERIAL is available for determining the basic material. UseMaterialFrom in cadconfig.xml is used to influence the location from which to read a basic material.<br />New function: Separation of the behavior of StartApplicationCat for opening a DIS in ECTR and launching the application via the start menu of ECTR. The latter is controlled using the option LaunchApplication.<br />New function: When starting CATIA, the release and environment shown by default in the environment selection field can be defined in the file start_application_cat.xml.<br />New function: Support for MMLs in Clone++<br />New function: The references to broken links and structures that have not been entered in full can be displayed using the entries ShowBrokenLinkWarning and ShowIncompleteScanWarning</p>\r\n<p>Change: In the selection list for the release and environment of the environment selection window of StartApplicationCat, the sequence is taken from the file start_application_cat.xml.<br />Change: The data to be mapped when storing documents in SAP is now determined using attributes-to-sap.xml instead of pdmmapping.xml. The file pdmmapping.xml is obsolete.<br />Change: User request to enable automatic saving for insert and replace commands.<br />Change: Sequence of user requests in replace commands refined.<br />Change: The check performed upon CheckIn, Save and Import for whether children already exist in SAP is controlled using the option CheckChildrenOnCheckin in pdmconfig.xml. The check is performed in the standard system.<br />Change: The check routines within StartApplicationCat with regard to CATIA instances that are already running have been optimized.<br />Change: It is no longer possible to perform integration with a CATIA that is not defined within start_application_cat.xml.<br />Change: The local generation of Visual Enterprise neutral formats is no longer part of template configuration. If required, this must be configured retroactively as per the Customizing documentation.<br />Change: The check for existing file names during the import to SAP is no longer performed in the standard system.</p>\r\n<p>Bug fix: Unique attribute transfer with simultaneous import of documents with same name except for the file extension.<br />Bug fix: Correction of incorrect translations and rotations when using components.<br />Bug fix: Minor optimization for evaluation of mapping result.<br />Bug fix: If an invalid configuration file exist in the CAD area, the system now issues an error message.<br />Bug fix: A Windows batch scrip can now be started from StartApplicationCat without errors.<br />Bug fix: Replacement of components in Clone++.<br />Bug fix: Crash in Chinese user environment.<br />Bug fix: Error messages adjusted.</p>\r\n<p>Known restrictions: \"Save as New Document\" and \"Save as New Version\" cannot be used for CATIA V4 models and CGRs because this may lead to inconsistencies within CATIA.<br />Known restrictions: Due to limitations of CATIA with an active CATIA CGR cache, a part may be loaded in the session in invisible form and may thus not be visible to the user. These \"invisible\" parts can result in error messages in the SAP ECTR interface to CATIA V5. The only workaround for this CATIA restriction is to reboot CATIA V5.<br />Known restrictions: The clone options \"Replace Components\", \"Insert Components\", and \"Delete Components\" are not supported for CATDrawing, CATProcess, and CATAnalysis.<br />Known restrictions: If originals are discarded using \"Reset Storage Location and Discard Work\", CATProcess files are not updated automatically.<br />Known restrictions: Performance when creating the application structure with a scan depth greater than 1 may be worse than when creating the application structure for the entire structure.<br />Known restrictions: The import does not support CATParts as \"depdendent documents\".<br />Known restrictions: CCP links are not supported for axis systems.<br />Known restrictions: It is not possible to insert CGRs and V4 models using the \"Insert\" command.<br />Known restrictions: Unicode characters are not supported for the location of the log file. The complete path including the file name must comprise only ASCII characters.<br />Known restrictions: Selecting a broken link in CATIA behaves in the same way as selecting the header node of the active CATIA document.<br />Known restrictions: The incomplete loading (future SAP ECTR option) of a product from SAP ECTR is not currently supported.<br />Known restrictions: Loading the configuration for SAP ECTR from SAP (SAP ECTR roadmap for future release) is not currently supported. This function will be supported in a future release of SAP ECTR integration for CATIA V5.<br />Known restrictions: Use of the function &#x201C;plm.control.partnameRules.resolveInSap.CAT=true&#x201D; leads to an incorrect error message if all documents affected by the import already exist in SAP.</p>\r\n<p>&#x00A0;</p>\r\n<p>Version: 1.1.2.0</p>\r\n<p>New function: Support for CATIA V5-6 R2016.<br />New function: If a file to be processed would overwrite a file in the temporary directory, it is temporarily renamed. This can now be deactivated.<br />New function: The \"Save as New Version\" command is included in the context menu of ASV.<br />New function: Update ASV, nodes in ASV are reloaded on a layer-by-layer basis (controlled by the parameter for structure scan depth).<br />New function: Status bar display when starting CATIA and for communication between ECTR and integration.<br />New function: ASV now supports the \"Open\" command and the display of selections in CATIA.<br />New function: Automatic saving of the last window position of the integration can be configured.</p>\r\n<p>Change: Depth of the structure scan can be configured in ASV.<br />Change: In ASV, window contents beneath a node are displayed with the relevant window name.<br />Change: Change of useDLNames and reconciliateProcesses of environment variables to configuration file.<br />Change: The user exit configuration supports environment variables and ECTR variables.<br />Change: The \"Save as New Document\" command executes only a flat copy.<br />Change: The \"Save and Display\" command no longer switches the entire document structure to change mode.<br />Change: The \"Save and Display\" command analyzes the CAD structure recursively only to a level that is neither checked out nor modified locally at the file level. It then skips all underlying documents.<br />Change: Numeric CATIA parameters and properties are now mapped on a language-independent basis.<br />Change: The reference measurement for mechanical CATIA product properties is meters instead of millimeters.<br />Change: The CATIA parameters available for mapping have been reduced to CATIA tree parameters in the standard system. The scan can be added again in the configuration. If you want to map the CATIA material, the parameter \"ParameterScanDepth\" must be set from \"TREE\" to \"ALL\".</p>\r\n<p>Bug fix: Error in insert command corrected.<br />Bug fix: The import command can now be terminated again in the first dialog box.<br />Bug fix: Error messages adjusted.<br />Bug fix: Reconciliation process for (repeat) opening of documents (configurable).<br />Bug fix: Performance improvement when determining changed documents in CATIA.<br />Bug fix: Performance improvement when determining the CATIA currently running.<br />Bug fix: Performance improvement when loading documents.<br />Bug fix: Optimization of accesses to CATIA.<br />Bug fix: Integration is more stable for incorrect configurations.<br />Bug fix: Standardized error messages for actions with an open CATIA desktop.</p>\r\n<p>Known restrictions: When deleting documents, the system issues a prompt for the CAD version and environment.<br />Known restrictions: If the originals are discarded using the command \"Unlock Originals in SAP PLM\", the associated CATProces files are not updated automatically.<br />Known restrictions: The commands for saving the original as a new document in SAP PLM and creating the original as a new document version in SAP PLM should not be used with CATIA V4 models and CGRs because this leads to data inconsistency within CATIAs.<br />Known restrictions: Due to CATIA restrictions, if the CGR cache is active, parts may be retained in memory even though they are no longer loaded visibly. These \"invisible\" files can result in error messages in the SAP ECTR interface to CATIA V5.<br />Known restrictions: For technical reasons, cloning does not support the \"Insert Component\", \"Replace Component\", and \"Delete Component \" commands for CATProcess, CATAnalysis, and CATDrawings.<br />Known restrictions: For a setup depth greater than 1, the speed of setting up the application structure overview may be longer than for setting up the entire structure.<br />Known restrictions: Currently, the system does not recognize any MMLs when analyzing the CAD structure if they are used as a Boolean operation within a CATPart.</p>\r\n<p>&#x00A0;</p>\r\n<p>Version: 1.1.1.1</p>\r\n<p>Bug fix: Error during error output (incorrect message text) for errors from CATIA.</p>\r\n<p>&#x00A0;</p>\r\n<p>Version: 1.1.1.0</p>\r\n<p>New function: You can use the configuration to override the values for the CAD system version and CAD system environment.<br />New function: Enhanced options for the execution of user exits.</p>\r\n<p>Change: The toolbar commands for DIR creation now use stored templates from ECTR and no longer use the active CATIA document. As a result, the creation of new documents from CATIA is consistent with their creation using SAP ECTR. You can use the configuration to restore the former behavior.<br />Change: During an import, only the selected documents are created in the active ECTR folder. The behavior is configurable.<br />Change: The environment variable \"ApplicationWindowTitle\" is no longer used.<br />Change: The reset of check-out now also works for documents that are checked in. The system discards any changes to the local documents.<br />Change: Logging configuration is now contained in the directory customize\\integration.</p>\r\n<p>Bug fix: Correct determination of the CATIA cache for CGRs for active DLNames.<br />Bug fix: Results and computations are no longer displayed in the application structure window.<br />Bug fix: More meaningful error message when you try to create a drawing without a master.<br />Bug fix: Process for closing and reopening windows changed for various \"cancel check-out\" options.<br />Bug fix: Command for saving the original as a new document in SAP PLM enhanced for analysis documents..<br />Bug fix: Command for saving the original as a new document in SAP PLM also works for documents with dependent computation and analysis documents.<br />Bug fix: Suppression of output of Windows environment on the console.<br />Bug fix: On slow computers, the system issues the message: \"A command is already being processed in CATIA. Execute your command again later.\"<br />Bug fix: Stabilization of communication for integration with ECTR for commands outside of the standard system.<br />Bug fix: Correction of error in determination of the logging configuration file.<br />Bug fix: Corrections for the \"Insert Component\" and \"Replace Component\" commands to prevent document synchronization from being executed twice.<br />Bug fix: The CAD properties FILENAME, FULLFILENAME, and EXTENSION are now available for mapping to SAP.</p>\r\n<p>Known restrictions: When deleting documents, the system issues a prompt for the CAD version and environment.<br />Known restrictions: If the originals are discarded using the command \"Unlock Originals in SAP PLM\", the associated CATProces files are not updated automatically.<br />Known restrictions: Last position of the toolbar is not saved.<br />Known restrictions: When the CATIA data is opened again automatically, the \"Cancel Checkout\" command creates an additional empty window.<br />Known restrictions: The commands for saving the original as a new document in SAP PLM and creating the original as a new document version in SAP PLM should not be used with CATIA V4 models and CGRs because this leads to data inconsistency within CATIAs.<br />Known restrictions: Due to CATIA restrictions, if the CGR cache is active, parts may be retained in memory even though they are no longer loaded visibly. These \"invisible\" files can result in error messages in the SAP ECTR interface to CATIA V5.<br />Known restrictions: For technical reasons, cloning does not support the \"Insert Component\", \"Replace Component\", and \"Delete Component \" commands for CATProcess, CATAnalysis, and CATDrawings.</p>\r\n<p>&#x00A0;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (C5310090)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (C5061975)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002283087/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002283087/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002283087/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002283087/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002283087/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002283087/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002283087/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002283087/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002283087/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2989766", "RefComponent": "PLM-ECC-CAT", "RefTitle": "SAP Engineering Control Center to CATIA V5", "RefUrl": "/notes/2989766 "}, {"RefNumber": "3093890", "RefComponent": "PLM-ECC-CAT", "RefTitle": "ECTR: Upgrade of CATIA Direct Integration (CDI)", "RefUrl": "/notes/3093890 "}, {"RefNumber": "2951216", "RefComponent": "PLM-ECC", "RefTitle": "Mainstream maintenance plan for CAD integrations by CENIT AG", "RefUrl": "/notes/2951216 "}, {"RefNumber": "2572868", "RefComponent": "PLM-ECC-CAT", "RefTitle": "Update Information - SAP ECTR interface to CATIA *******", "RefUrl": "/notes/2572868 "}, {"RefNumber": "2572830", "RefComponent": "PLM-ECC-CAT", "RefTitle": "Installation Prerequisites of SAP Engineering Control Center to CATIA V5 - 1.1.18", "RefUrl": "/notes/2572830 "}, {"RefNumber": "2465368", "RefComponent": "PLM-ECC-CAT", "RefTitle": "Installation Prerequisites of SAP Engineering Control Center to CATIA V5 - 1.1.3.0", "RefUrl": "/notes/2465368 "}, {"RefNumber": "2439996", "RefComponent": "PLM-ECC-CAT", "RefTitle": "Update Information - SAP ECTR interface to CATIA 1.1.3.0", "RefUrl": "/notes/2439996 "}, {"RefNumber": "2378990", "RefComponent": "PLM-ECC-CAT", "RefTitle": "Update Information - SAP ECTR interface to CATIA 1.1.2.0", "RefUrl": "/notes/2378990 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ECTR", "From": "510", "To": "510", "Subsequent": ""}, {"SoftwareComponent": "ECTR", "From": "520", "To": "520", "Subsequent": ""}, {"SoftwareComponent": "ECTR_CATIAV5", "From": "1.1", "To": "1.1", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}