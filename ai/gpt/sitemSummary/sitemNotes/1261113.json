{"Request": {"Number": "1261113", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 385, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000007373542017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001261113?language=E&token=F83AAB047A892795049E92FABAA3D2C1"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001261113", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1261113"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "13.10.2008"}, "SAPComponentKey": {"_label": "Component", "value": "FS-CML-AC-PI"}, "SAPComponentKeyText": {"_label": "Component", "value": "Posting Interface"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Services", "value": "FS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Consumer and Mortgage Loans", "value": "FS-CML", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS-CML*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Accounting", "value": "FS-CML-AC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS-CML-AC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Posting Interface", "value": "FS-CML-AC-PI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS-CML-AC-PI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1261113 - BUC: Enhancement of BAdI FVD_LOAN_POST with AUFNR"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Note 899513 and others (see the Related Notes section of this note) deliver the BAdI FVD_LOAN_POST; in some cases, you can use this BAdI to fill certain fields on a customer-specific basis. After you implement this note, you can change the field AUFNR (order number).</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>LOAN_POST, SE18, SE19, FVD_LOAN_POST, AUFNR, BSSBSEG, ACCIT</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Previously, this function was not provided.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Implement the advance corrections or import the relevant Support Package.<br /><br />If you want to implement the advance correction, you must also perform the following steps:<br /></p> <UL><LI>In the interface of the BAdI FVD_LOAN_POST, copy the method CHANGE_PRCTR_IN_BSEG to CHANGE_AUFNR_IN_BSEG (transaction SE18). Description: Fill order number</LI></UL> <p></p> <UL><LI>In the parameters of the method CHANGE_AUFTR_IN_BSEG, rename C_PRCTR as C_AUFNR. Change the associated type to BSSBSEG-AUFNR and the description to \"New Order Number\".</LI></UL> <p></p> <UL><LI>Activate your changes.</LI></UL> <p><br />If you create a separate implementation (transaction SE19) and design it accordingly, you can now define your own values.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D028266"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D036589)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001261113/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001261113/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001261113/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001261113/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001261113/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001261113/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001261113/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001261113/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001261113/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "987866", "RefComponent": "FS-CML-AC-PI", "RefTitle": "Posting: Providing posted data using BAPI", "RefUrl": "/notes/987866"}, {"RefNumber": "977894", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: Suppress FI posting", "RefUrl": "/notes/977894"}, {"RefNumber": "976287", "RefComponent": "FS-CML-AC", "RefTitle": "BUC: Suppressing empty error log", "RefUrl": "/notes/976287"}, {"RefNumber": "970390", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: Interaction of CML / NewGL / leading ledger", "RefUrl": "/notes/970390"}, {"RefNumber": "964221", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: Adding the PRCTR/PPRCTR fields to FVD_LOAN_POST", "RefUrl": "/notes/964221"}, {"RefNumber": "962243", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: Additions to the FVD_LOAN_POST BAdI", "RefUrl": "/notes/962243"}, {"RefNumber": "948538", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: Adding GSBER and PROJK to FVD_LOAN_POST", "RefUrl": "/notes/948538"}, {"RefNumber": "940831", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: Changing LZBKZ, LANDL", "RefUrl": "/notes/940831"}, {"RefNumber": "938823", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: Enhancing BAdI FVD_LOAN_POST", "RefUrl": "/notes/938823"}, {"RefNumber": "935633", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: Change FIPOS, FISTL, GEBER, KBLNR, KBLPOS, FKBER", "RefUrl": "/notes/935633"}, {"RefNumber": "925457", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: Change to BKTXT, RMVCT, SGTXT, ANBWA", "RefUrl": "/notes/925457"}, {"RefNumber": "916653", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: BAdI for trading partner in FI document", "RefUrl": "/notes/916653"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1131502", "RefComponent": "FS-CML-AC-PI", "RefTitle": "POS: KOSTL included in BAdI FVD_LOAN_POST", "RefUrl": "/notes/1131502"}, {"RefNumber": "1060313", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: Addition of SEGMENT / PSEGMENT to FVD_LOAN_POST", "RefUrl": "/notes/1060313"}, {"RefNumber": "1048831", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: Enhancing BLART in the FVD_LOAN_POST BAdI", "RefUrl": "/notes/1048831"}, {"RefNumber": "1046114", "RefComponent": "FS-CML-AC-PI", "RefTitle": "POS: Enhancement of BAdI FVD_LOAN_POST", "RefUrl": "/notes/1046114"}, {"RefNumber": "1009041", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: MWSKZ enhancement for FVD_LOAN_POST BAdI", "RefUrl": "/notes/1009041"}, {"RefNumber": "1004610", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: Add XBLNR to FVD_LOAN_POST BAdI", "RefUrl": "/notes/1004610"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1876679", "RefComponent": "FS-CML", "RefTitle": "BUC: Supplement to SAP Note 1090657", "RefUrl": "/notes/1876679 "}, {"RefNumber": "925457", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: Change to BKTXT, RMVCT, SGTXT, ANBWA", "RefUrl": "/notes/925457 "}, {"RefNumber": "1048831", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: Enhancing BLART in the FVD_LOAN_POST BAdI", "RefUrl": "/notes/1048831 "}, {"RefNumber": "987866", "RefComponent": "FS-CML-AC-PI", "RefTitle": "Posting: Providing posted data using BAPI", "RefUrl": "/notes/987866 "}, {"RefNumber": "964221", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: Adding the PRCTR/PPRCTR fields to FVD_LOAN_POST", "RefUrl": "/notes/964221 "}, {"RefNumber": "962243", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: Additions to the FVD_LOAN_POST BAdI", "RefUrl": "/notes/962243 "}, {"RefNumber": "940831", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: Changing LZBKZ, LANDL", "RefUrl": "/notes/940831 "}, {"RefNumber": "970390", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: Interaction of CML / NewGL / leading ledger", "RefUrl": "/notes/970390 "}, {"RefNumber": "938823", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: Enhancing BAdI FVD_LOAN_POST", "RefUrl": "/notes/938823 "}, {"RefNumber": "916653", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: BAdI for trading partner in FI document", "RefUrl": "/notes/916653 "}, {"RefNumber": "1131502", "RefComponent": "FS-CML-AC-PI", "RefTitle": "POS: KOSTL included in BAdI FVD_LOAN_POST", "RefUrl": "/notes/1131502 "}, {"RefNumber": "1060313", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: Addition of SEGMENT / PSEGMENT to FVD_LOAN_POST", "RefUrl": "/notes/1060313 "}, {"RefNumber": "1046114", "RefComponent": "FS-CML-AC-PI", "RefTitle": "POS: Enhancement of BAdI FVD_LOAN_POST", "RefUrl": "/notes/1046114 "}, {"RefNumber": "1009041", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: MWSKZ enhancement for FVD_LOAN_POST BAdI", "RefUrl": "/notes/1009041 "}, {"RefNumber": "1004610", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: Add XBLNR to FVD_LOAN_POST BAdI", "RefUrl": "/notes/1004610 "}, {"RefNumber": "977894", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: Suppress FI posting", "RefUrl": "/notes/977894 "}, {"RefNumber": "976287", "RefComponent": "FS-CML-AC", "RefTitle": "BUC: Suppressing empty error log", "RefUrl": "/notes/976287 "}, {"RefNumber": "948538", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: Adding GSBER and PROJK to FVD_LOAN_POST", "RefUrl": "/notes/948538 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "935633", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: Change FIPOS, FISTL, GEBER, KBLNR, KBLPOS, FKBER", "RefUrl": "/notes/935633 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-FINSERV", "From": "200", "To": "200", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-FINSERV 200", "SupportPackage": "SAPKGPFB17", "URL": "/supportpackage/SAPKGPFB17"}, {"SoftwareComponentVersion": "EA-FINSERV 500", "SupportPackage": "SAPKGPFC21", "URL": "/supportpackage/SAPKGPFC21"}, {"SoftwareComponentVersion": "EA-FINSERV 600", "SupportPackage": "SAPKGPFD15", "URL": "/supportpackage/SAPKGPFD15"}, {"SoftwareComponentVersion": "EA-FINSERV 603", "SupportPackage": "SAPK-60304INEAFINSRV", "URL": "/supportpackage/SAPK-60304INEAFINSRV"}, {"SoftwareComponentVersion": "EA-FINSERV 604", "SupportPackage": "SAPK-60402INEAFINSRV", "URL": "/supportpackage/SAPK-60402INEAFINSRV"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "EA-FINSERV", "NumberOfCorrin": 1, "URL": "/corrins/0001261113/201"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EA-FINSERV", "ValidFrom": "200", "ValidTo": "200", "Number": "1157558 ", "URL": "/notes/1157558 ", "Title": "BUC: Update FVD_LOAN_POST BAdI to MANST", "Component": "FS-CML-AC-PI"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "500", "ValidTo": "500", "Number": "1157558 ", "URL": "/notes/1157558 ", "Title": "BUC: Update FVD_LOAN_POST BAdI to MANST", "Component": "FS-CML-AC-PI"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "600", "ValidTo": "600", "Number": "1157558 ", "URL": "/notes/1157558 ", "Title": "BUC: Update FVD_LOAN_POST BAdI to MANST", "Component": "FS-CML-AC-PI"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "603", "ValidTo": "603", "Number": "1157558 ", "URL": "/notes/1157558 ", "Title": "BUC: Update FVD_LOAN_POST BAdI to MANST", "Component": "FS-CML-AC-PI"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}