{"Request": {"Number": "70228", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 603, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014496612017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000070228?language=E&token=089D75320C41C2D23ED668FB2F3F6EC7"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000070228", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000070228/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "70228"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 22}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.06.2017"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-ADDON"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade Add-On Components"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade Add-On Components", "value": "BC-UPG-ADDON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-ADDON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "70228 - Add-ons: Conditions and upgrade planning"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><br />This SAP Note contains fundamental basic conditions for ABAP add-ons. Add-ons are additional software components that are not part of the underlying ABAP server products/SAP system such as SAP ERP 6.0. There is a difference between modifying add-ons and add-ons that do not modify. Since modifying add-ons change objects from a prerequisite software component, the effects described in this SAP Note are more extensive than for add-ons that do not modify.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p><br />Add-on, ABAP, software component, installation, upgrade, update, Enhancement Package, switch upgrade, retrofit, modification, modifying, modification adjustment, upgrade path, release, software lifecycle, Software Lifecycle Management</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><br />The installation of an ABAP add-on involves basic, permanent restrictions for the underlying server product/SAP system with regard to future activities such as upgrade and maintenance.<br /><br />Before the installation of an ABAP add-on, you should first read this SAP Note and consider the resulting consequences for your system landscape carefully.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Table of contents</strong></p>\r\n<ol>1. Add-on basic conditions</ol><ol>2. Add-on lifecycle management</ol><ol>3. Add-on postprocessing steps</ol>\r\n<p><br /><br /></p>\r\n<ol>1. Add-on basic conditions</ol>\r\n<p><br />Before the installation of an ABAP add-on, take note of the following points:</p>\r\n<ul>\r\n<li><strong><strong>No uninstallation (for exceptions, see SAP Note <a target=\"_blank\" href=\"/notes/2011192\">2011192</a>)</strong></strong><br />The uninstallation of add-ons from SAP systems/SAP server products is not available in the standard system. This means that the active deletion of the function installed by the add-on later on is not usually possible. <br />However, it is possible for the functionality from the add-on to be transferred to another add-on or a component of the ABAP server product and for the original add-on to be flagged as uninstalled in the system (retrofit). In addition, as of Version 0053 of the SAP Add-On Installation Tool (transaction code SAINT), not only can you install ABAP add-ons, but you can also uninstall them again in certain circumstances - please read SAP Note <a target=\"_blank\" href=\"/notes/2011192\">2011192</a> for more information.<br />The restrictions described in the other points referring to the upgrade and maintenance of your entire SAP system result from this.</li>\r\n</ul>\r\n<ul>\r\n<li>Compatibility of upgrade paths<br />Before you install an add-on on a server product, you must check that the release strategy of the add-on to be installed is compatible with the relevant server product, as well as with other add-ons that have already been installed. Note that add-ons may not support every possible (server) upgrade path.<br />Before installing the add-on, check the Product Availability Matrix (PAM) on SAP Service Marketplace (<strong><a target=\"_blank\" href=\"http://support.sap.com/pam\">http://service.sap.com/pam</a></strong>) to see which upgrade paths are supported. You might also find information about this in the release strategy note of the relevant add-on. If at least one add-on does not support the required upgrade, a future upgrade of the server product is no longer possible (<strong>SAP Note <a target=\"_blank\" href=\"/notes/33040\">33040</a></strong>).<br />In addition, note that add-on deliveries are usually delayed and are carried out after server product deliveries.<br />For more documents on the SAP release strategy, see SAP Service Marketplace (<strong><a target=\"_blank\" href=\"http://support.sap.com/releasestrategy\">http://support.sap.com/releasestrategy</a></strong>).</li>\r\n</ul>\r\n<ul>\r\n<li>Modifying add-ons<br />Some add-ons modify underlying components of the SAP system/SAP server product. (in an ERP system, for example SAP_APPL). In the release strategy note, check whether it concerns a modifying add-on since there are a number of additional restrictions in this case:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If you have already imported more Support Packages for the underlying, modifying component into your SAP system than are specified in the add-on specific release strategy note in the section \"Required Components and Support Packages\", you must check whether Conflict Resolution Transports (CRTs) of the add-on are already available. CRTs are available on SAP Service Marketplace.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The delivery of CRTs takes place later. If a CRT you require is not yet available, you cannot perform the installation at present. For more information on CRTs, see <strong>SAP Note <a target=\"_blank\" href=\"/notes/53902\">53902</a></strong>.</li>\r\n<li>A modifying add-on cannot be uninstalled.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Equivalent Support Packages in the update/upgrade<br />If you are scheduling an upgrade or the import of an Enhancement Package, note that you should not import the latest Support Package Stack in the start release before the upgrade to ensure that the required, equivalent Support Package is already available in the target release. Including the equivalent support package prevents a loss of functionality and a loss of data (worst case scenario) (<strong>SAP Note <a target=\"_blank\" href=\"/notes/832594\">832594</a></strong>).<br />For more information, see SAP Service Marketplace (<strong><a target=\"_blank\" href=\"http://support.sap.com/sp-stacks\">http://support.sap.com/sp-stacks</a> </strong>-&gt; SP Stack Information -&gt; General Support Package Stack Information -&gt; SP Stack Strategy).</li>\r\n</ul>\r\n<ul>\r\n<li>Combination of add-ons<br />For more information, see the SAP Notes about the release strategy of the relevant add-ons. Usually, you should not install modifying add-ons together.</li>\r\n</ul>\r\n<ul>\r\n<li>No installation of add-ons for SAP XI / SAP PI.<br />We recommend that you do not install add-ons on SAP XI or SAP PI.  Due to an installation of an add-on on XI/PI, a later upgrade of SAP XI / SAP PI may lead to errors.<br />The Product Availability Matrix (PAM) on SAP Service Marketplace contains information about the prerequisite product versions: <strong><a target=\"_blank\" href=\"http://support.sap.com/pam\">http://support.sap.com/pam</a></strong></li>\r\n</ul>\r\n<ol>2. Add-on lifecycle management</ol>\r\n<p><br />For the installation or upgrade of an add-on, take note of the following points:</p>\r\n<ul>\r\n<li>Order/download of SAP software<br />Add-on media is not automatically sent to all customers. Request the add-on data carrier (CD/DVD) from your responsible SAP local subsidiary, or load it from SAP Service Marketplace. The following SAP Notes contain more detailed information:<br /><strong>SAP Note <a target=\"_blank\" href=\"/notes/925690\">925690</a></strong> (ordering)<br /><strong>SAP Note <a target=\"_blank\" href=\"/notes/83458\">83458</a></strong> (download)</li>\r\n</ul>\r\n<ul>\r\n<li>General documentation<br />For SAP standard products, you can find the relevant installation and upgrade documentation on SAP Service Marketplace under: <strong><a target=\"_blank\" href=\"http://service.sap.com/instguides\">http://service.sap.com/instguides</a></strong></li>\r\n</ul>\r\n<ul>\r\n<li>Tools for installation /upgrade/maintenance<br />The installation and upgrade tools are already placed as \"Software Logistics Toolset\". You can find the related documentation on SAP Service Marketplace under<br /><strong><a target=\"_blank\" href=\"http://service.sap.com/instguides\">http://service.sap.com/instguides</a></strong> -&gt; Alphabetical Index \"S\" -&gt; Software Logistics Toolset</li>\r\n</ul>\r\n<ul>\r\n<li>A large number of Lifecycle Management tools require a stack XML file created with the maintenance optimizer (MOPZ). You can find the related documentation on SAP Service Marketplace under:<br /><strong><a target=\"_blank\" href=\"http://support.sap.com/mopz\">http://support.sap.com/mopz</a></strong></li>\r\n</ul>\r\n<ol>3. Add-on postprocessing steps</ol>\r\n<p><br />In the following, activities that may be relevant after an add-on is installed or upgraded are described: Refer to the add-on specific SAP Notes for the steps that you must carry out in your case.</p>\r\n<ul>\r\n<li>Installation of additional languages<br />For information about subsequently installing further languages in your system, please see <strong>SAP Note <a target=\"_blank\" href=\"/notes/195442\">195442</a></strong>.<br />Refer to the release strategy note of the add-on for information about the availability of languages.</li>\r\n</ul>\r\n<ul>\r\n<li>Delivery Customizing<br />Delivery Customizing is imported into client 000 and may have to be copied to other clients.<br /><strong>SAP Note <a target=\"_blank\" href=\"/notes/337623\">337623</a></strong> contains more detailed information.</li>\r\n</ul>\r\n<ul>\r\n<li>Importing Support Packages<br />You can import additional Support Packages after the installation. For this, refer to the information on SAP Service Marketplace under:<br /><strong><a target=\"_blank\" href=\"http://service.sap.com/instguidesnw\">http://service.sap.com/instguidesnw</a></strong> -&gt; SAP NetWeaver [release] -&gt; Maintenance</li>\r\n</ul></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D020457)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D025360)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000070228/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000070228/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000070228/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000070228/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000070228/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000070228/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000070228/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000070228/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000070228/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "925690", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/925690"}, {"RefNumber": "83458", "RefComponent": "BC-UPG-OCS", "RefTitle": "@19@OCS Info: Patch download from SAP Service Marketplace", "RefUrl": "/notes/83458"}, {"RefNumber": "832594", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Importing Support Packages before an upgrade", "RefUrl": "/notes/832594"}, {"RefNumber": "53902", "RefComponent": "BC-UPG-OCS", "RefTitle": "Conflicts between Support Packages and add-ons", "RefUrl": "/notes/53902"}, {"RefNumber": "337623", "RefComponent": "BC-CUS", "RefTitle": "Customizing after installation or upgrade", "RefUrl": "/notes/337623"}, {"RefNumber": "33040", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Options in exchange upgrade with add-on", "RefUrl": "/notes/33040"}, {"RefNumber": "2011192", "RefComponent": "BC-UPG-OCS-SPA", "RefTitle": "Uninstallation of ABAP add-ons", "RefUrl": "/notes/2011192"}, {"RefNumber": "195442", "RefComponent": "BC-CTS-LAN", "RefTitle": "Language import and Support Packages", "RefUrl": "/notes/195442"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2920938", "RefComponent": "FI-TV-ODT-MTR", "RefTitle": "Using transaction SAINT to uninstall SAP Fiori OData component SRA004 1.0", "RefUrl": "/notes/2920938 "}, {"RefNumber": "3422160", "RefComponent": "CA-DS4", "RefTitle": "Uninstalling Add-On DSiM BW/4HANA 200", "RefUrl": "/notes/3422160 "}, {"RefNumber": "3418514", "RefComponent": "TM-LOC-RU", "RefTitle": "Uninstalling ERP Add-On TMRU", "RefUrl": "/notes/3418514 "}, {"RefNumber": "3410465", "RefComponent": "XX-PROJ-CDP-017", "RefTitle": "Uninstalling Add-On BCON 602", "RefUrl": "/notes/3410465 "}, {"RefNumber": "3407920", "RefComponent": "BC-UPG-ADDON", "RefTitle": "SOAR (ARO) Uninstallation", "RefUrl": "/notes/3407920 "}, {"RefNumber": "3400631", "RefComponent": "XX-PROJ-CDP-TEST-713", "RefTitle": "Uninstalling Add-On HEMCRM", "RefUrl": "/notes/3400631 "}, {"RefNumber": "3400697", "RefComponent": "XX-PROJ-CDP-TEST-713", "RefTitle": "Uninstalling Add-On HEMECC", "RefUrl": "/notes/3400697 "}, {"RefNumber": "3390357", "RefComponent": "IS-DRY", "RefTitle": "Uninstalling MSGFELIX", "RefUrl": "/notes/3390357 "}, {"RefNumber": "3355373", "RefComponent": "CRM-IU-S-PCT", "RefTitle": "Uninstalling add-on CALC", "RefUrl": "/notes/3355373 "}, {"RefNumber": "3103674", "RefComponent": "OPU-GW-COR", "RefTitle": "Uninstalling GW_CORE", "RefUrl": "/notes/3103674 "}, {"RefNumber": "3338478", "RefComponent": "IS-CTS-FIO", "RefTitle": "Uninstalling SAP S/4HANA Life Sciences-UI, product Add-On (ISLSUI)", "RefUrl": "/notes/3338478 "}, {"RefNumber": "3337278", "RefComponent": "IS-CTS", "RefTitle": "Uninstalling SAP S/4HANA Life Sciences, product Add-On (ISLS)", "RefUrl": "/notes/3337278 "}, {"RefNumber": "3103719", "RefComponent": "OPU-GW-COR", "RefTitle": "Uninstalling IW_BEP", "RefUrl": "/notes/3103719 "}, {"RefNumber": "3328095", "RefComponent": "XX-PROJ-CDP-488", "RefTitle": "Uninstalling Add-On SAAP", "RefUrl": "/notes/3328095 "}, {"RefNumber": "3312457", "RefComponent": "XX-PROJ-CDP-683", "RefTitle": "Uninstalling Add-On RER", "RefUrl": "/notes/3312457 "}, {"RefNumber": "3312057", "RefComponent": "MM-IV-HUB-CIM", "RefTitle": "Uninstalling HUBERPI2", "RefUrl": "/notes/3312057 "}, {"RefNumber": "3237964", "RefComponent": "CA-EPC", "RefTitle": "Uninstalling Add-On EPROJCON", "RefUrl": "/notes/3237964 "}, {"RefNumber": "3292693", "RefComponent": "XX-PROJ-CDP-490", "RefTitle": "Uninstallation Information Note for Add-On: DSFW", "RefUrl": "/notes/3292693 "}, {"RefNumber": "3277608", "RefComponent": "TM-ADP-CSL-L2A", "RefTitle": "Uninstalling add-on TML2A 100", "RefUrl": "/notes/3277608 "}, {"RefNumber": "3276939", "RefComponent": "TM-ADP-CSL-NAO", "RefTitle": "Uninstalling add-on TMNAO 100", "RefUrl": "/notes/3276939 "}, {"RefNumber": "3277625", "RefComponent": "TM-ADP-CSL-OTC", "RefTitle": "Uninstalling add-on TMO2C 200", "RefUrl": "/notes/3277625 "}, {"RefNumber": "3277115", "RefComponent": "TM-ADP-CSL", "RefTitle": "Uninstalling Add-on TMCSL 200", "RefUrl": "/notes/3277115 "}, {"RefNumber": "3255637", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy and Maintenance Information for the ABAP add-on CPRAUI", "RefUrl": "/notes/3255637 "}, {"RefNumber": "3239266", "RefComponent": "XX-PROJ-CDP-372", "RefTitle": "Uninstalling Add-on FSTPS", "RefUrl": "/notes/3239266 "}, {"RefNumber": "3222372", "RefComponent": "IS-PHA-IHLS", "RefTitle": "Uninstalling Add-On LSCH", "RefUrl": "/notes/3222372 "}, {"RefNumber": "3221018", "RefComponent": "XX-PROJ-CDP-TEST-247", "RefTitle": "Uninstalling Add-On AARE", "RefUrl": "/notes/3221018 "}, {"RefNumber": "3217902", "RefComponent": "MM-PUR-HUB-CTR", "RefTitle": "Uninstalling HUBS4IC", "RefUrl": "/notes/3217902 "}, {"RefNumber": "3075684", "RefComponent": "FS-FBS-CML-LWP", "RefTitle": "Uninstalling Add-On LOANSWPU", "RefUrl": "/notes/3075684 "}, {"RefNumber": "3069809", "RefComponent": "FS-FBS-CML-LWP", "RefTitle": "Uninstalling Add-On LOANSWP", "RefUrl": "/notes/3069809 "}, {"RefNumber": "3101353", "RefComponent": "OPU-BSC-TGW", "RefTitle": "Uninstallation of the add-on IW_PGW 100 using transaction SAINT", "RefUrl": "/notes/3101353 "}, {"RefNumber": "3095545", "RefComponent": "MOB-APP-CRM-DSD", "RefTitle": "Uninstalling add-on MOBDSDCI", "RefUrl": "/notes/3095545 "}, {"RefNumber": "3089095", "RefComponent": "MOB-APP-ERP-DSD", "RefTitle": "Uninstalling add-on MOBDSDEI", "RefUrl": "/notes/3089095 "}, {"RefNumber": "3084610", "RefComponent": "OPU-ASA-FG", "RefTitle": "ASAFGEE - Technical Requirements for Connectivity", "RefUrl": "/notes/3084610 "}, {"RefNumber": "2975691", "RefComponent": "CEC-MKT-ITC", "RefTitle": "Uninstalling of hybris Marketing 1.1 or 1.2 (SAP_CUAN 110 and 120)", "RefUrl": "/notes/2975691 "}, {"RefNumber": "2958200", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy and Maintenance Information for the ABAP add-on UIGRCGTS", "RefUrl": "/notes/2958200 "}, {"RefNumber": "2954022", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy and Maintenance Information for the ABAP add-on DMIS 2020", "RefUrl": "/notes/2954022 "}, {"RefNumber": "2946819", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy and Maintenance Information for the ABAP add-on ACMFPUI", "RefUrl": "/notes/2946819 "}, {"RefNumber": "2946818", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy and Maintenance Information for the ABAP add-on ACMFP", "RefUrl": "/notes/2946818 "}, {"RefNumber": "2927114", "RefComponent": "OPU-ASA-EE", "RefTitle": "Release strategy and Maintenance Information for the ABAP add-on ASAFGEE", "RefUrl": "/notes/2927114 "}, {"RefNumber": "2927040", "RefComponent": "OPU-ASA-EE", "RefTitle": "Release strategy and Maintenance Information for the ABAP add-on ASANWEE", "RefUrl": "/notes/2927040 "}, {"RefNumber": "2850986", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy and Maintenance Information for the ABAP add-on PACG", "RefUrl": "/notes/2850986 "}, {"RefNumber": "2760565", "RefComponent": "GRC-FIO-RM", "RefTitle": "Uninstallation of UIGRRM01 100: SAP Fiori 1.0 for SAP Risk Management", "RefUrl": "/notes/2760565 "}, {"RefNumber": "2760579", "RefComponent": "GRC-FIO-SPC", "RefTitle": "Uninstallation of UIGRPC01 100: SAP Fiori 1.0 for SAP Process Control", "RefUrl": "/notes/2760579 "}, {"RefNumber": "2750685", "RefComponent": "GRC-ACP", "RefTitle": "Uninstalling Access Control 4.0 PlugIn Component VIRSANH", "RefUrl": "/notes/2750685 "}, {"RefNumber": "2750701", "RefComponent": "GRC-ACP", "RefTitle": "Uninstalling Access Control 4.0 PlugIn Component VIRSA", "RefUrl": "/notes/2750701 "}, {"RefNumber": "2706776", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy and Maintenance Information for the ABAP add-on POCB4H", "RefUrl": "/notes/2706776 "}, {"RefNumber": "2706813", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy and Maintenance Information for the ABAP add-on CB4HANA", "RefUrl": "/notes/2706813 "}, {"RefNumber": "2690979", "RefComponent": "CA-GBT", "RefTitle": "Uninstallation of Global Batch Traceability (GBT)", "RefUrl": "/notes/2690979 "}, {"RefNumber": "2630240", "RefComponent": "BC-CTS-TMS-CTR", "RefTitle": "Uninstalling CTS_PLUG 200", "RefUrl": "/notes/2630240 "}, {"RefNumber": "2571634", "RefComponent": "MOB-APP-PAP", "RefTitle": "Deinstallation des Add-Ons MOB PAYAPPROVAL INT 2.0.0 (LWMFI401) mit der Transaktion SAINT", "RefUrl": "/notes/2571634 "}, {"RefNumber": "2544275", "RefComponent": "CA-TRA-IN", "RefTitle": "GST - ITR : Uninstallation ITR ( Indain Tax Reform )", "RefUrl": "/notes/2544275 "}, {"RefNumber": "2564073", "RefComponent": "IS-DRY", "RefTitle": "Release strategy for the ABAP add-On SAP Dairy Management by msg for SAP S/4HANA", "RefUrl": "/notes/2564073 "}, {"RefNumber": "2554679", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy and Maintenance Information for the ABAP add-on FS-RI for SAP S/4HANA", "RefUrl": "/notes/2554679 "}, {"RefNumber": "2536453", "RefComponent": "GRC-ACP", "RefTitle": "Uninstalling VIRSAHR - SAP Access Control", "RefUrl": "/notes/2536453 "}, {"RefNumber": "2536230", "RefComponent": "GRC-ACP", "RefTitle": "Uninstalling VIRSANH - SAP Access Control", "RefUrl": "/notes/2536230 "}, {"RefNumber": "2529018", "RefComponent": "SD-SLS-WOM", "RefTitle": "WOM: Uninstallation WOM 200 (Cross Channel Order Management)", "RefUrl": "/notes/2529018 "}, {"RefNumber": "2512218", "RefComponent": "MOB-APP-ORD", "RefTitle": "Uninstalling LWMSD002", "RefUrl": "/notes/2512218 "}, {"RefNumber": "2503641", "RefComponent": "OPU-DUE", "RefTitle": "Uninstallation of DUET add-ons with transaction SAINT", "RefUrl": "/notes/2503641 "}, {"RefNumber": "2481113", "RefComponent": "XX-PROJ-CDP-597", "RefTitle": "Uninstalling CPRA Add-ons", "RefUrl": "/notes/2481113 "}, {"RefNumber": "1922973", "RefComponent": "IS-DRY", "RefTitle": "Release strategy for the ABAP add-on MSGFELIX", "RefUrl": "/notes/1922973 "}, {"RefNumber": "2469687", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for the ABAP Add-on product SAP Marketing (SAP_CUAN + UICUAN)", "RefUrl": "/notes/2469687 "}, {"RefNumber": "2438631", "RefComponent": "MDM-FN-API-ABA", "RefTitle": "Uninstallation of add-on MDM TECH 710 700", "RefUrl": "/notes/2438631 "}, {"RefNumber": "2437183", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling SLCE addon", "RefUrl": "/notes/2437183 "}, {"RefNumber": "2424906", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstallation of LCAPPS 2005_700", "RefUrl": "/notes/2424906 "}, {"RefNumber": "2420945", "RefComponent": "BC-MOB-DOE", "RefTitle": "Uninstallation of the add-on SUPDOE with transaction SAINT", "RefUrl": "/notes/2420945 "}, {"RefNumber": "2409846", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstallation WFMCORE 200", "RefUrl": "/notes/2409846 "}, {"RefNumber": "2408541", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of Fiori based Front End  Software Components", "RefUrl": "/notes/2408541 "}, {"RefNumber": "2403053", "RefComponent": "SCM-BAS-INT", "RefTitle": "Uninstalling SCM_BASIS 713 and SCM_BASIS 714 as Add-On to ERP", "RefUrl": "/notes/2403053 "}, {"RefNumber": "2360806", "RefComponent": "SCM-EWM-IF", "RefTitle": "Uninstalling SAP EWM 9.3, SAP EWM 9.4 and SAP EWM 9.5 as Add-On to SAP ERP", "RefUrl": "/notes/2360806 "}, {"RefNumber": "2276816", "RefComponent": "PA-SFI-TM", "RefTitle": "Uninstalling add-on SFIHCM03", "RefUrl": "/notes/2276816 "}, {"RefNumber": "2375320", "RefComponent": "PA-SFI-TM", "RefTitle": "Uninstalling add-on SFIHCM02", "RefUrl": "/notes/2375320 "}, {"RefNumber": "2375289", "RefComponent": "PA-SFI-TM", "RefTitle": "Uninstalling add-on SFIHCM01", "RefUrl": "/notes/2375289 "}, {"RefNumber": "2381952", "RefComponent": "LO-INT-COD", "RefTitle": "Uninstalling software component CODERINT 600", "RefUrl": "/notes/2381952 "}, {"RefNumber": "2373714", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for the ABAP add-on UTWSI 100", "RefUrl": "/notes/2373714 "}, {"RefNumber": "2227939", "RefComponent": "LOD-TEM-IMD", "RefTitle": "Deinstallation of SAP Cloud for Travel and Expense add-ons OTM_EXTR, ODTHCMER, ODTGEN, ODTFINCO, ODTFINCC, TEMODFI and DCFLPROC", "RefUrl": "/notes/2227939 "}, {"RefNumber": "2362137", "RefComponent": "MOB-APP-CAA", "RefTitle": "Uninstalling GBSRM001", "RefUrl": "/notes/2362137 "}, {"RefNumber": "2357898", "RefComponent": "CA-IAM-QIM", "RefTitle": "Quality Issue Management 1.0: Deinstallation", "RefUrl": "/notes/2357898 "}, {"RefNumber": "2355120", "RefComponent": "CA-IAM-MOC", "RefTitle": "Management of Change 1.0: Uninstallation when converting to SAP S/4HANA", "RefUrl": "/notes/2355120 "}, {"RefNumber": "2333985", "RefComponent": "MOB-APP-OST", "RefTitle": "Uninstalling LWMSD001", "RefUrl": "/notes/2333985 "}, {"RefNumber": "2342708", "RefComponent": "MDM-FN-API-ABA", "RefTitle": "Uninstallation of add-on MDM Technology 710 731 and MDM Technology 710 700", "RefUrl": "/notes/2342708 "}, {"RefNumber": "2319813", "RefComponent": "IS-DP-DMP", "RefTitle": "Uninstallation of add-on FMFMS 604", "RefUrl": "/notes/2319813 "}, {"RefNumber": "2319803", "RefComponent": "IS-DP-DMP", "RefTitle": "Uninstallation of add-on PSFMS 600", "RefUrl": "/notes/2319803 "}, {"RefNumber": "2307624", "RefComponent": "EHS-BD-TLS", "RefTitle": "Uninstalling EHS Web-Interface 2.6", "RefUrl": "/notes/2307624 "}, {"RefNumber": "2298456", "RefComponent": "EHS-SRC", "RefTitle": "Uninstalling SAP Product and REACH Compliance (SPRC) 2.0 or Compliance for Products (CfP) 2.2 -  /TDAG/CPCL_S4_AOF_PLUGIN", "RefUrl": "/notes/2298456 "}, {"RefNumber": "2303283", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for the ABAP add-on REVREC 120", "RefUrl": "/notes/2303283 "}, {"RefNumber": "2244901", "RefComponent": "FS-BA-AN-REG", "RefTitle": "Uninstallation of RRA (Regulatory Reporting Analyzer by iBS)", "RefUrl": "/notes/2244901 "}, {"RefNumber": "2234715", "RefComponent": "MOB-APP-SRS", "RefTitle": "Uninstalling SAP Mobile OData add-on LWMSIM01  for SAP RealSpend 1.0.0", "RefUrl": "/notes/2234715 "}, {"RefNumber": "2221394", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for the ABAP add-on PRAGENR", "RefUrl": "/notes/2221394 "}, {"RefNumber": "1809841", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for the ABAP add-on RTLDDF", "RefUrl": "/notes/1809841 "}, {"RefNumber": "2200413", "RefComponent": "CRM-BF-DCN", "RefTitle": "Uninstalling CRMGWS", "RefUrl": "/notes/2200413 "}, {"RefNumber": "2178306", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Banking Services From SAP 9.0 - Release strategy for the ABAP Software Component Versions", "RefUrl": "/notes/2178306 "}, {"RefNumber": "2162719", "RefComponent": "MOB-APP-TER", "RefTitle": "Uninstalling GBTRV001", "RefUrl": "/notes/2162719 "}, {"RefNumber": "2156226", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for the ABAP add-on PRAOKR", "RefUrl": "/notes/2156226 "}, {"RefNumber": "2149065", "RefComponent": "LO-BM-GBT", "RefTitle": "Uninstalling Add-On GBTRINT", "RefUrl": "/notes/2149065 "}, {"RefNumber": "2124793", "RefComponent": "MOB-APP-TEA", "RefTitle": "Uninstalling GBTRV002", "RefUrl": "/notes/2124793 "}, {"RefNumber": "2101734", "RefComponent": "BC-UPG-ADDON", "RefTitle": "SAP IBP: Release strategy for the ABAP add-ons", "RefUrl": "/notes/2101734 "}, {"RefNumber": "2097791", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for the ABAP add-on FB4UUI", "RefUrl": "/notes/2097791 "}, {"RefNumber": "2061909", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for the ABAP add-on MSPLSCM", "RefUrl": "/notes/2061909 "}, {"RefNumber": "2042183", "RefComponent": "IS-OIL-DS-OGSD", "RefTitle": "OGSD 7.0 Installation / Delta Upgrade on SAP ECC 600", "RefUrl": "/notes/2042183 "}, {"RefNumber": "2050165", "RefComponent": "PLM-ECC", "RefTitle": "ECTRWUI: Release strategy for ABAP Add-on ECTRWUI", "RefUrl": "/notes/2050165 "}, {"RefNumber": "2049249", "RefComponent": "PLM-ECC", "RefTitle": "ECTR: Release strategy for ABAP Add-on ECTR", "RefUrl": "/notes/2049249 "}, {"RefNumber": "2040404", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for the ABAP add-on ARBSNCI1", "RefUrl": "/notes/2040404 "}, {"RefNumber": "2032145", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Deinstallation UICRM001 100", "RefUrl": "/notes/2032145 "}, {"RefNumber": "1657804", "RefComponent": "EPM-DSM-BWC", "RefTitle": "Release strategy for ABAP add-on DISCLMG", "RefUrl": "/notes/1657804 "}, {"RefNumber": "1776739", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for SAP Screen Personas 1.0/ 2.0", "RefUrl": "/notes/1776739 "}, {"RefNumber": "1915308", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for the ABAP add-on FSAPPL 450", "RefUrl": "/notes/1915308 "}, {"RefNumber": "1812556", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for the ABAP add-on RTLPROMO", "RefUrl": "/notes/1812556 "}, {"RefNumber": "1784199", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for SAP Commercial Project Management", "RefUrl": "/notes/1784199 "}, {"RefNumber": "1814875", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for ABAP add-on LWMFI002", "RefUrl": "/notes/1814875 "}, {"RefNumber": "1814461", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy ABAP add-on LWMCR005", "RefUrl": "/notes/1814461 "}, {"RefNumber": "1835547", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for the ABAP add-on ISHMOBIL", "RefUrl": "/notes/1835547 "}, {"RefNumber": "1841471", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for ABAP add-ons for interface components", "RefUrl": "/notes/1841471 "}, {"RefNumber": "1762893", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for ABAP add-on FSPOT", "RefUrl": "/notes/1762893 "}, {"RefNumber": "1792146", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for the ABAP add-on SCM APO", "RefUrl": "/notes/1792146 "}, {"RefNumber": "1377262", "RefComponent": "IS-OIL-DS-OGSD", "RefTitle": "OGSD 6.1 Installation / Delta Upgrade on SAP ECC 600", "RefUrl": "/notes/1377262 "}, {"RefNumber": "1259649", "RefComponent": "BC-UPG-ADDON", "RefTitle": "HR-CEE 110_604:Installation on ECC 600 EhP4-7/Delta upgrade", "RefUrl": "/notes/1259649 "}, {"RefNumber": "1793743", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for the ABAP add-on SCMEWM", "RefUrl": "/notes/1793743 "}, {"RefNumber": "1492733", "RefComponent": "BC-UPG-ADDON", "RefTitle": "GRC NFE 10.0 installation on NW 7.02/7.0 3", "RefUrl": "/notes/1492733 "}, {"RefNumber": "1793555", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy and Maintenance Information for the ABAP add-on IDXAT2", "RefUrl": "/notes/1793555 "}, {"RefNumber": "1819008", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for ABAP add-on CTSCM", "RefUrl": "/notes/1819008 "}, {"RefNumber": "1819010", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy and maintenance information for ABAP add-on CTERP", "RefUrl": "/notes/1819010 "}, {"RefNumber": "1813733", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for ABAP add-ons SMFND & SMERP 400_620", "RefUrl": "/notes/1813733 "}, {"RefNumber": "1720483", "RefComponent": "BC-UPG-ADDON", "RefTitle": "TEMPLATE: Release strategy and Maintenance Information for the ABAP add-on [Add-on]", "RefUrl": "/notes/1720483 "}, {"RefNumber": "394519", "RefComponent": "EHS", "RefTitle": "Installation of SAP EH&S 2.7B/4.5B", "RefUrl": "/notes/394519 "}, {"RefNumber": "402455", "RefComponent": "XX-PROJ-SDP-004", "RefTitle": "SDP: Delta-Upgrade of AMSDPAPO 302 to AMSDPAPO 303", "RefUrl": "/notes/402455 "}, {"RefNumber": "402771", "RefComponent": "XX-PROJ-SDP-004", "RefTitle": "SDP: Delta-Upgrade of DIVA 461 to DIVA 462", "RefUrl": "/notes/402771 "}, {"RefNumber": "508533", "RefComponent": "XX-PART-ADP", "RefTitle": "Installation of /ASP/ 2.00 w/ SAINT", "RefUrl": "/notes/508533 "}, {"RefNumber": "392841", "RefComponent": "EHS", "RefTitle": "Installation of SAP EH&S 2.7B/4.6B", "RefUrl": "/notes/392841 "}, {"RefNumber": "393855", "RefComponent": "EHS", "RefTitle": "Installing SAP EH&S 2.7B/4.6C", "RefUrl": "/notes/393855 "}, {"RefNumber": "422999", "RefComponent": "XX-PROJ-SDP-004", "RefTitle": "SDP: Delta-Upgrade of AMSDPAPO 303 to AMSDPAPO 304", "RefUrl": "/notes/422999 "}, {"RefNumber": "537738", "RefComponent": "XX-PROJ-JP-PHM", "RefTitle": "Installation of KJCPH 1.0A (Transaction SAINT)", "RefUrl": "/notes/537738 "}, {"RefNumber": "566485", "RefComponent": "XX-PROJ-CRM-PHA", "RefTitle": "CRMPHA 3.02 (Server) Add. inform. to installation/upgrade", "RefUrl": "/notes/566485 "}, {"RefNumber": "601846", "RefComponent": "FS-CD", "RefTitle": "Additional info about installation of SAP Insurance 4.71", "RefUrl": "/notes/601846 "}, {"RefNumber": "601886", "RefComponent": "FS-CD", "RefTitle": "Addtl info for R/3 4.7x110 SR1 upgrade with Insurance 4.71", "RefUrl": "/notes/601886 "}, {"RefNumber": "603660", "RefComponent": "FS-CM", "RefTitle": "Additional information about installing SAP FS-CM-GKV 4.71", "RefUrl": "/notes/603660 "}, {"RefNumber": "605649", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Installing WFMCORE 100 (transaction SAINT)", "RefUrl": "/notes/605649 "}, {"RefNumber": "710791", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Add. information on installing FS-CM-GKV 4.72 R/3 E 470x200", "RefUrl": "/notes/710791 "}, {"RefNumber": "710789", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Add. info on upgrade to R/3 Enterprise 470x200 w. Insurance", "RefUrl": "/notes/710789 "}, {"RefNumber": "710788", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Installing the INSURANCE 4.72 add-on on R/3E 470x200", "RefUrl": "/notes/710788 "}, {"RefNumber": "753543", "RefComponent": "XX-PROJ-RE-LUM", "RefTitle": "XRE-LUM 300: Installation and upgrade", "RefUrl": "/notes/753543 "}, {"RefNumber": "716342", "RefComponent": "FS-PM", "RefTitle": "Additional info on installation FSPM 1.00 R/3 WEB/AS 6.20", "RefUrl": "/notes/716342 "}, {"RefNumber": "721669", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Add. info on R/3 Enterprise 47x110 SR1 - IS-DFPS 471 upgrade", "RefUrl": "/notes/721669 "}, {"RefNumber": "768737", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Installing add-ons/delta upgrade HR-VADM 205", "RefUrl": "/notes/768737 "}, {"RefNumber": "710792", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Add. info. on upgrading R/3 Enterprise 470x200 w. FS-CM-GKV", "RefUrl": "/notes/710792 "}, {"RefNumber": "923523", "RefComponent": "FS-RI", "RefTitle": "Installing FS-RI 600 in SAP ECC 600", "RefUrl": "/notes/923523 "}, {"RefNumber": "793099", "RefComponent": "FS-PM", "RefTitle": "Enhancement to installation FSPM 2.00 WEB/AS R/3 6.20", "RefUrl": "/notes/793099 "}, {"RefNumber": "879817", "RefComponent": "FS-PM", "RefTitle": "Additional info about installing FSPM 3.00 WEB/ACE R/3 6.40", "RefUrl": "/notes/879817 "}, {"RefNumber": "768999", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Additions to R/3 upgrade Enterprise 470x200 SR1 with HR-VADM", "RefUrl": "/notes/768999 "}, {"RefNumber": "768998", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Additional in for the installation of HR-VADM 300", "RefUrl": "/notes/768998 "}, {"RefNumber": "923524", "RefComponent": "FS-RI", "RefTitle": "Upgrade to SAP ECC 600 with FS-RI 600", "RefUrl": "/notes/923524 "}, {"RefNumber": "930901", "RefComponent": "FS-PM", "RefTitle": "Enhancements for FSPM 3.10 NW04s installations", "RefUrl": "/notes/930901 "}, {"RefNumber": "979374", "RefComponent": "FS-CML", "RefTitle": "Installation: Consumer Loans Postbank Add-On", "RefUrl": "/notes/979374 "}, {"RefNumber": "666494", "RefComponent": "XX-PROJ-JP-CHM", "RefTitle": "Installation KJCHM 471 on R/3 Enterprise 47x110", "RefUrl": "/notes/666494 "}, {"RefNumber": "531151", "RefComponent": "XX-PROJ-JP-EIM", "RefTitle": "KEPS-MM 10A : Installation Guide", "RefUrl": "/notes/531151 "}, {"RefNumber": "634254", "RefComponent": "XX-PROJ-JP-EIM", "RefTitle": "KEPS-MM 20A : Installation Guide", "RefUrl": "/notes/634254 "}, {"RefNumber": "53902", "RefComponent": "BC-UPG-OCS", "RefTitle": "Conflicts between Support Packages and add-ons", "RefUrl": "/notes/53902 "}, {"RefNumber": "775257", "RefComponent": "XX-PROJ-JP-EB1", "RefTitle": "Installation KJEB1 471 on R/3 Enterprise 47x200", "RefUrl": "/notes/775257 "}, {"RefNumber": "809571", "RefComponent": "XX-PROJ-JP-EPS", "RefTitle": "Installation/upgrade R/3 Enterprise 470x200 with KJEPS 471", "RefUrl": "/notes/809571 "}, {"RefNumber": "613608", "RefComponent": "XX-PROJ-JP-CCS", "RefTitle": "Installation KJCCSJ 4.71 on R/3 Enterprise 47x110", "RefUrl": "/notes/613608 "}, {"RefNumber": "878525", "RefComponent": "XX-PROJ-JP-EPS", "RefTitle": "Installation KJEPS 501 on SAP ECC 5.0", "RefUrl": "/notes/878525 "}, {"RefNumber": "703090", "RefComponent": "XX-PROJ-JP-EPS", "RefTitle": "Installation KJEPS 4.71 on R/3 Enterprise 47x110", "RefUrl": "/notes/703090 "}, {"RefNumber": "994195", "RefComponent": "IS-REA", "RefTitle": "IS-REA: Upgrading IS-REA 4.61 to IS-REA 6.10", "RefUrl": "/notes/994195 "}, {"RefNumber": "947415", "RefComponent": "IS-REA", "RefTitle": "IS-REA: Upgrading IS-REA 4.61 to IS-REA 5.10", "RefUrl": "/notes/947415 "}, {"RefNumber": "610368", "RefComponent": "XX-PROJ-JP-PHM", "RefTitle": "Installation KJCPH 2.0A on R/3 Enterprise 47x110", "RefUrl": "/notes/610368 "}, {"RefNumber": "610417", "RefComponent": "XX-PROJ-JP-PHM", "RefTitle": "R/3 Enterprise 47x110 SR1 upgrade with KJCPH 2.0A", "RefUrl": "/notes/610417 "}, {"RefNumber": "994534", "RefComponent": "IS-REA", "RefTitle": "IS-REA: Upgrading IS-REA 5.10 to IS-REA 6.10", "RefUrl": "/notes/994534 "}, {"RefNumber": "994353", "RefComponent": "IS-REA", "RefTitle": "IS-REA: Upgrading IS-REA 4.71 to IS-REA 6.10", "RefUrl": "/notes/994353 "}, {"RefNumber": "994150", "RefComponent": "IS-REA", "RefTitle": "IS-REA 6.10: Installation note", "RefUrl": "/notes/994150 "}, {"RefNumber": "634219", "RefComponent": "XX-PROJ-JP-EIM", "RefTitle": "Add. info on R/3 Enterprise 4.70 upgrade with KEPS-MM", "RefUrl": "/notes/634219 "}, {"RefNumber": "762707", "RefComponent": "XX-PROJ-AID", "RefTitle": "Additions to the AUTOID NODE 200 installation", "RefUrl": "/notes/762707 "}, {"RefNumber": "795227", "RefComponent": "XX-CSC-SE-ISU", "RefTitle": "Add. info on installing ISUSE 472 R/3 Enterprise 470x200", "RefUrl": "/notes/795227 "}, {"RefNumber": "715131", "RefComponent": "XX-PROJ-NACOS", "RefTitle": "NACOS 4.64: Updates to the NACOS 4.64 upgrade (SAINT)", "RefUrl": "/notes/715131 "}, {"RefNumber": "938045", "RefComponent": "IS-REA", "RefTitle": "IS-REA 4.61: Installation note", "RefUrl": "/notes/938045 "}, {"RefNumber": "691479", "RefComponent": "IS-REA", "RefTitle": "IS-REA 4.71: Installation instructions", "RefUrl": "/notes/691479 "}, {"RefNumber": "882097", "RefComponent": "IS-REA", "RefTitle": "IS-REA 5.10: Installation note", "RefUrl": "/notes/882097 "}, {"RefNumber": "33040", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Options in exchange upgrade with add-on", "RefUrl": "/notes/33040 "}, {"RefNumber": "814242", "RefComponent": "IS-REA", "RefTitle": "IS-REA 4.71 SR1: Installation note", "RefUrl": "/notes/814242 "}, {"RefNumber": "947414", "RefComponent": "IS-REA", "RefTitle": "IS-REA: Upgrading IS-REA 4.61 to IS-REA 4.71", "RefUrl": "/notes/947414 "}, {"RefNumber": "558238", "RefComponent": "XX-PROJ-LAM-CRM", "RefTitle": "LAM-CRM 3.20 additional information on the installtn/upgrade", "RefUrl": "/notes/558238 "}, {"RefNumber": "456628", "RefComponent": "XX-CSC-RU-HR", "RefTitle": "Errors during Installation of HR-CIS ADDON", "RefUrl": "/notes/456628 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}