{"Request": {"Number": "1097599", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1266, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016379222017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001097599?language=E&token=07513C6ACAF78213A24CD807CA84FEB8"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001097599", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001097599/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1097599"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "29.10.2007"}, "SAPComponentKey": {"_label": "Component", "value": "BC-INS-AS4"}, "SAPComponentKeyText": {"_label": "Component", "value": "Installation and Upgrade AS/400"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Installation Tools (SAP Note 1669327)", "value": "BC-INS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-INS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Installation and Upgrade AS/400", "value": "BC-INS-AS4", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-INS-AS4*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1097599 - iSeries: Info and recommendations on kernel libraries (640)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You are searching for recommendations concerning the R/3 kernel libraries of your R/3 system.<br />You have installed more than one SAP system on an iSeries, and you require information about the various options available when installing kernel libraries.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>AS/400 kernel, iSeries kernel R3&lt;REL&gt;OPT library, LODSAPKRN, APYR3KRN, RMVR3KRN, APYR3FIX</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><OL>1. <B>What is a kernel?</B><br /><br />An SAP kernel on an iSeries is found in a program library.<br />The library contains ILE programs (*PGM) and ILE service programs (*SRVPGM) as well as PASE programs (executable AIX binary files) in the database file O4AFFILE (*FILE). All programs of a kernel have been optimized, and offer the best possible performance. These are also referred to as optimized kernels.<br />The subdirectory \"run\" in the directory /usr/sap/&lt;SID&gt;/SYS/exe contains links to the ILE objects in the library. The PASE objects are unpacked to the directory /usr/sap/&lt;SID&gt;/SYS/exe/run when the kernel is imported with APYR3KRM (see also Note 751132). These links and the programs in this directory are used by R/3 under AS/400, for example when starting programs.<br /></OL> <OL>2. <B>What do I name the libraries?</B><br /><br />You can assign any name to the kernel library. Names are suggested according to the format R3640&lt;CP&gt;OPT in which &lt;CP&gt; denotes the character properties of the kernel (A for ASCII, U for Unicode), that is, R3640AOPT or R3640UOPT.<br /></OL> <OL>3. <B>Should I only use one kernel for several SAP systems?</B><br /><br />We strongly recommend against this because the utility programs for importing program corrections (APYR3FIX) do not recognize this configuration.<br /></OL> <OL>4. <B>Should I use a separate kernel for each SAP system?</B><br /><br />You can install new kernel libraries either during the installation or during the upgrade. This means that each SAP system uses its own kernel.<br />The time needed to install separate kernels is offset by the fact that you can import and test program corrections for each SAP system, without affecting the other SAP systems.<br />This also means that when the systems are upgraded, the new kernel is imported and the old kernel can be deleted because no other SAP systems are using the old kernel.</OL> <OL>5. <B>Using APYR3FIX to import kernel corrections</B><br /><br />The command APYR3FIX transfers save files from SAP to the customer system and then uses the commands UPDPGM or UPDSRVPGM to transfer the corrections to your kernel library.<br />Since the transfer may take a long time and the kernel library cannot be used by other jobs on the iSeries while this is happening, you must stop the SAP system or work with a copy of the kernel library.</OL> <UL><UL><LI><B>Creating a copy:</B> Save the kernel library in a save file (using the command SAVLIB) and then use the command LODSAPKRN to resave the kernel library under a different name. Other copy methods (using the command RSTLIB or CPYLIB) result in the loss of object authorizations, which can, however, be corrected under QSECOFR using the command FIXR3OWNS LIB (&lt;new_lib&gt;) OBJ(*ALL).</LI></UL></UL> <UL><UL><LI><B>Importing corrections first to the copy:</B> Enter the name of the copy in the KRNLIB parameter of the APYR3FIX command.</LI></UL></UL> <UL><UL><LI><B>Using the copy for the SAP system:</B> First use the command RMVR3KRN to remove the original kernel. For this, you have to stop the SAP system. Now, delete or rename the original and give the copy the required name (RNMOBJ). Use the command APYR3KRN to activate the copied, corrected kernel. Restart the SAP system.<br /></LI></UL></UL> <OL>6. <B>What are the standard names for SAP kernels?</B><br /><br />Many SAP tools for kernel processing require a standard name (for example parameter SAVLIB in the LODSAPKRN or APYR3FIX commands). These names follow a simple format:<br />R3&lt;rel&gt;&lt;cp&gt;OPT&#x00A0;&#x00A0;- when downloading a kernel (LODSAPKRN)<br />GEN&lt;rel&gt;&lt;cp&gt;OPT - when importing patches (APYR3FIX)<br />Enter the SAP release in the abbreviation &lt;rel&gt;, in this case, 640. The abbreviation &lt;cp&gt; denotes the character properties of the kernel (A for ASCII, U for Unicode).<br />Here are some examples:<br />R3620AOPT&#x00A0;&#x00A0;- for LODSAPKRN in SAP Release 620 ASCII<br />GEN640UOPT - for APYR3FIX in SAP Release 640 UNICODE</OL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D042520)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001097599/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001097599/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001097599/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001097599/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001097599/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001097599/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001097599/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001097599/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001097599/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "49701", "RefComponent": "BC-INS-AS4", "RefTitle": "iSeries: Info and recommendations for kernel libraries (46D)", "RefUrl": "/notes/49701"}, {"RefNumber": "1097751", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Information and recommendations for kernel libraries", "RefUrl": "/notes/1097751"}, {"RefNumber": "1097600", "RefComponent": "BC-INS-AS4", "RefTitle": "iSeries: Info and recommendations on kernel libraries (700)", "RefUrl": "/notes/1097600"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1097751", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Information and recommendations for kernel libraries", "RefUrl": "/notes/1097751 "}, {"RefNumber": "1097600", "RefComponent": "BC-INS-AS4", "RefTitle": "iSeries: Info and recommendations on kernel libraries (700)", "RefUrl": "/notes/1097600 "}, {"RefNumber": "49701", "RefComponent": "BC-INS-AS4", "RefTitle": "iSeries: Info and recommendations for kernel libraries (46D)", "RefUrl": "/notes/49701 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "620", "To": "640", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}