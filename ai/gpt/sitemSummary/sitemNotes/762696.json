{"Request": {"Number": "762696", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 349, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000762696?language=E&token=68FDE9A360B09F20B1F23CC683CDD99D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000762696", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000762696/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "762696"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 17}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "17.12.2012"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SER-EWA"}, "SAPComponentKeyText": {"_label": "Component", "value": "EarlyWatch Alert"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Support Services", "value": "SV-SMG-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "EarlyWatch Alert", "value": "SV-SMG-SER-EWA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SER-EWA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "762696 - Grey rating for Earlywatch Alert"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>An Earlywatch Alert report for an ABAP based system was rated grey.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>EWA, missing function modules, Solution Manager, Earlywatch Alert, gray rating, SDCC, SDCCN , EW_ALERT</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This note is valid only up to ST-SER 700_2008_2. For current EarlyWatch Alert versions please see SAP Note 1801236 instead.<br />An Earlywatch Alert report was rated grey, because important information is missing or can not be interpreted correctly.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>A report gets a grey&#x00A0;&#x00A0;rating if key information is missing, so that the values that were measured can not be put in a context.<br /><br />For a <B>Java based system</B> this is the case if</p> <UL><LI>the system can not be recognized as a NON-ABAP system<br /></LI></UL> <p>For an <B>ABAP based system</B> this is the case if</p> <UL><LI>the product can not be recognised</LI></UL> <UL><LI>in the system where the session is performed</LI></UL> <UL><UL><LI>one of the following function modules is missing completely from the collection</LI></UL></UL> <UL><UL><LI>one of the following function modules contains no data<br /></LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SYSTEM_INFO (Basic system and database info to intialize the session)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;TH_SERVER_LIST (Servers and instances of a system)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPWL_TCODE_AGGREGATION_I_U_W&#x00A0;&#x00A0; ( Information about active users)<br /><br />For <B>either system type</B> no rating can be set if the user executing the session does not have the correct authorisations.<br /><br />The most likely causes for this are listed below.<br /></p> <OL>1. Please ensure that when you define a system ( SMSY), in the 'selection of main instances' the correct selection is made, so that the nature of the system is reflected accurately.This is a prerequisite to ensure&#x00A0;&#x00A0;that during the execution of the session the correct check tree can be opened, and the product, as well as the ABAP or Java nature of the system are correctly recognised.<br /><br /></OL> <OL>2. Sessions without any rating<br />a)&#x00A0;&#x00A0;Automatically generated EWA sessions don't get a rating at all, instead there is an icon with a document and glasses ( see attachment). Sessions also appear to be 'sent to SAP' but do not arrive at SAP.<br /><br />To avoid this please ensure that the user controlling background job SM:EXEC SERVICES has the correct authorizations. See SAP Note 1415702 for details.<br /><br />b) SAP Solution Manager 7.0 EhP1 SP 24 only:<br />Due to a change in error handling it may occur that Early Watch Alert reports are completely empty, although the job SM:EXEC SERVICES finished successfully and the job log reports successful processing of the session. See SAP Note 1496931 for details and SAP Note 700518 for advice on error analysis.<br /></OL> <OL>3. Q:An EWA report for a <B>NON-ABAP system</B> states that the above function modules are missing. But for a NON-ABAP system these can never be collected. What happened?<br /><br />The EWA relevant information for NON-ABAP system is fed into a data collection in SDCCN in the SAP Solution Manager. Several data sources contribute information . SAP Note 1332428 provides more information.<br /><br />A1:Only relevant for SAP Solution Manager systems which are using either  SP08&#x00A0;&#x00A0;for ST-SER ST-SER 700_2010_1&#x00A0;&#x00A0;or ST-SER ST-SER 700_2010_1 plus Service Content Update ( AGS_UPDATE):<br /><br />A correction will be provided via SAP Note 1614783.<br /><br />A2:Only relevant for SAP Solution Manager with ST-SER release ST-SER 700_2008_1 (or older) and ST-PI 2008_1_700 (no SP):<br />A program error results in in errors like \"SYSTEM_INFO missing\". A correction is provided in SAP Note 1293644.<br /><br />A3:Only relevant for SAP Solution Manager systems with ST-PI&#x00A0;&#x00A0;2005_1*:<br />Please update ST-PI (see SAP Note 539977) , to achieve the improvement described in SAP Note 1171211.<br /></OL> <OL>4. Q:In the EarlyWatch Alert (EWA), errors may occur during the recognition of the SAP product version for some products. As a consequence the product version is displayed incorrectly on the cover sheet of the report document, or the report is nearly empty.<br /><br />A: SAP Note 1322899 describes the problem and which actions can be taken.<br /></OL> <OL>5. Q:The section \"Missing Function Modules\" in the chapter \"Service Preparation\" of the EarlyWatch Alert (EWA) report lists missing downloadmodules for servers on which actually no SAP application server instance runs (for example, LiveCache server).<br /><br />A: Review SAP Note 1389277.</OL> <OL>6. Interrupted data collection<br /><br />a) This could happen if</OL> <UL><UL><LI>SDCC is used in the satellite system</LI></UL></UL> <UL><UL><LI>this system is listed in several different landscapes in SAP Solution Manager</LI></UL></UL> <UL><UL><LI>the data collections for all sessions in the various landscapes are scheduled for the same day.<br /></LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As the Automatic Session Manager in SDCC has to use the same scheduling rules for all sessions, it is possible that the jobs SASM_&lt;sessno&gt;_COLL_TRANS clash, especially in large systems, where the data collection takes longer.<br /><br />To avoid this, SDCCN should be used instead of SDCC. More information about SDCCN is in SAP Note 763561.<br /><br />If SDCCN can not be activated, the Earlywatch Alerts in systems which are integrated into more than one landscape should be scheduled for different days.<br /><br />Also in SDCC -&gt; Automatic Session Manager -&gt; Settings -&gt; Customizing the entry for ' Earliest date for session data processing' should be set to 0.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;b) As described in note 624427, in some systems using SDCC the Automatic Session Manager suffers from a bug, which causes several jobs SASM_&lt;sessno&gt;_COLL_TRANS to be scheduled for the same session.<br /><br />As the ASM has to apply the same scheduling rules to each job, they can be very close together, and may clash.<br /><br />If&#x00A0;&#x00A0;the note has already been applied, a setting in SDCC -&gt; Automatic Session Manager -&gt; Settings -&gt; Customizing should be checked:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"Allow data collection before Monday of the week &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;when the session is scheduled\"&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;should NOT be selected.<br />In combination with<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\" Earliest date for session data processing&#x00A0;&#x00A0;&gt;0\"<br />it could lead to the SASM_&lt;sessno&gt;_COLL_TRANS getting scheduled outside the timeframe the ASM can check. This could lead to multiple jobs per session.<br /></p> <OL>7. Function modules named SAPWL_TCODE_AGGREGATION* may also be missing due to invalid service definitions.<br />The service definitions should be exchanged completely, as described in note 727998.<br /></OL> <OL>8. Some function modules could also be missing if data collection reports used by SDCC(N) have been deleted. In this case please use the code correction from note 932861, or implement SP04 for ST-PI 2005_1*<br /></OL> <OL>9. Interrupted data transfer<br />If the data collections in the sending system do contain all function modules, please check the transfer section of the SDCC session log. Network problems could have interrupted the data transfer, so that the function modules are missing in the session in the Solution Manager, or the SAP Service System.<br /><br />You can find more information about the connections to a SAP Solution Manager system in the SAP Solution Manager installation guide.<br />These can be found at service.sap.com/solutionmanager.<br /><br />More information about data transfers to SAP can be found in SAP note 216952 (SDCC&#x00A0;&#x00A0;FAQ) or SAP note 763561 ( SDCCN FAQ)<br /></OL> <OL>10. Some function modules do not contain information because of system settings.</OL> <UL><LI>Function modules SAPWL*<br />Notes 12103 and 917558 should be reviewed. If these notes do not resolve the problems, a customer message can be opened in component BC-CCM-MON-TUN.</LI></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-SMG-SDD (Service Data Download)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D038895)"}, {"Key": "Processor                                                                                           ", "Value": "I019960"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000762696/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000762696/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000762696/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000762696/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000762696/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000762696/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000762696/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000762696/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000762696/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "932861", "RefComponent": "SV-SMG-SDD", "RefTitle": "Trigger regeneration of collection reports", "RefUrl": "/notes/932861"}, {"RefNumber": "917558", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "EarlyWatch Alert does not contain ST03 statistics as of 7.0", "RefUrl": "/notes/917558"}, {"RefNumber": "763561", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCCN)  - FAQ", "RefUrl": "/notes/763561"}, {"RefNumber": "712511", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/712511"}, {"RefNumber": "624427", "RefComponent": "SV-SMG-SDD", "RefTitle": "Multiple scheduling of collective jobs for sessions", "RefUrl": "/notes/624427"}, {"RefNumber": "216952", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCC) - FAQ", "RefUrl": "/notes/216952"}, {"RefNumber": "1801236", "RefComponent": "SV-SMG-SER", "RefTitle": "Rating Rules: <PERSON> Rating of an Alert Based Session", "RefUrl": "/notes/1801236"}, {"RefNumber": "1322899", "RefComponent": "SV-SMG-SER", "RefTitle": "EWA incomplete after importing EHP/product incorrect", "RefUrl": "/notes/1322899"}, {"RefNumber": "727998", "RefComponent": "SV-SMG-SDD", "RefTitle": "Complete Replacement of Service Definitions of the Service Data Control Center", "RefUrl": "/notes/727998"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "727998", "RefComponent": "SV-SMG-SDD", "RefTitle": "Complete Replacement of Service Definitions of the Service Data Control Center", "RefUrl": "/notes/727998 "}, {"RefNumber": "763561", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCCN)  - FAQ", "RefUrl": "/notes/763561 "}, {"RefNumber": "216952", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCC) - FAQ", "RefUrl": "/notes/216952 "}, {"RefNumber": "1322899", "RefComponent": "SV-SMG-SER", "RefTitle": "EWA incomplete after importing EHP/product incorrect", "RefUrl": "/notes/1322899 "}, {"RefNumber": "932861", "RefComponent": "SV-SMG-SDD", "RefTitle": "Trigger regeneration of collection reports", "RefUrl": "/notes/932861 "}, {"RefNumber": "624427", "RefComponent": "SV-SMG-SDD", "RefTitle": "Multiple scheduling of collective jobs for sessions", "RefUrl": "/notes/624427 "}, {"RefNumber": "917558", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "EarlyWatch Alert does not contain ST03 statistics as of 7.0", "RefUrl": "/notes/917558 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST-SER", "From": "310", "To": "620_2006_2", "Subsequent": ""}, {"SoftwareComponent": "ST-SER", "From": "700_2005_2", "To": "700_2008_1", "Subsequent": ""}, {"SoftwareComponent": "ST-SER", "From": "701_2008_2", "To": "701_2008_2", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}