{"Request": {"Number": "540021", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 287, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015257712017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000540021?language=E&token=DD88155AF95BE9B2B1C4889645633C77"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000540021", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000540021/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "540021"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 65}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.04.2006"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "540021 - Add. information on migrating/updating to Oracle 9.2.0: UNIX"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Current additions and corrections for the current upgrade guide &#x00A0;&#x00A0;&#x00A0;&#x00A0;'Upgrade to Oracle Version 9.2.0: UNIX'<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0; Document Version 1.10 - December 18, 2002</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>runInstaller, RUNINSTALLER, response file, Database Upgrade Assistant (DBUA), database migration, database upgrade Oracle Release 9.2.0 (9i Rel.2), *******, *******<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Required corrections and important additions to the upgrade guide &#x00A0;&#x00A0;&#x00A0;&#x00A0;'Upgrade to Oracle Version 9.2.0: UNIX'<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0; Document Version 1.10 - December 18,2002<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><B>IMPORTANT: This note always contains the latest information available about the procedure for upgrading an Oracle database from Version 8i (8.1.7) to Oracle 9i Rel.2 (9.2.0). Therefore, make sure that the current version of this note is installed in your system before starting the upgrade!</B><br /><br />The procedure for upgrading an Oracle database to Version 9.2.0 for UNIX systems is described in the 'Upgrade to Oracle Version 9.2.0: UNIX' guide. This guide is available in the SAP Service Marketplace at http://service.sap.com/instguides (choose 'Database Upgrades, then 'Oracle').<br /><br />Always use the current version of the guide when you upgrade. This note only refers to the latest version 1.10 of December 18, 2002.<br /><br /><br /><STRONG>Contents<br /></STRONG></p> <b>I Descriptions<br /><br /></b><br /> <b>II Additions to the guide<br /></b><br /> <OL>1. Operating system requirements<br /></OL> <OL>2. Important information for BEFORE the upgrade<br /></OL> <OL>3. Important information for DURING the upgrade<br /></OL> <OL>4. Important information for AFTER the upgrade<br /></OL> <OL>5. Required/available patch sets<br /></OL> <OL>6. Importing individual patches<br /></OL> <OL>7. Required/available patches<br /></OL> <OL>8. Important: Workarounds for Oracle errors<br /></OL> <OL>9. Oracle initializing parameters<br /></OL> <OL>10. SAP profile parameters<br /></OL> <OL>11. Environment variables for &lt;sapsid&gt;adm<br /></OL> <OL>12. SAP tools for database administration<br /><br /><br /><br /></OL> <b>III&#x00A0;&#x00A0;Errors in the guide<br /></b><br /> <OL>1. dba_registry<br /><br /><br /><br /></OL> <b>I Terms<br /></b><br /> <p>The following terms apply in this note:</p> <UL><LI>&lt;DBSID&gt; is the database system ID.</LI></UL> <UL><LI>The old Oracle home directory<br /> &#x00A0;&#x00A0;/oracle/&lt;DBSID&gt;/817_32<BR/><br />or<br /> &#x00A0;&#x00A0;/oracle/&lt;DBSID&gt;/817_64<br />is termed &lt;old_ora_home&gt;.</LI></UL> <UL><LI>&lt;ora_home&gt; is the Oracle home directory of the newly installed software:<br /> &#x00A0;&#x00A0;/oracle/&lt;DBSID&gt;/920_32<BR/><br />or<br /> &#x00A0;&#x00A0;/oracle/&lt;DBSID&gt;/920_64</LI></UL> <UL><LI>&lt;nls_lang&gt; is the output of script CHECKS.SQL from the SAP directory of the first RDBMS CD. As described in the guide, this script was executed before installing the new database software.</LI></UL> <UL><LI>&lt;LIBRARYPATH&gt; is the environment variable assigned to your operating system that determines the search path for shared libraries:<br /> &#x00A0;&#x00A0;AIX&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; LIBPATH<br />&#x00A0;&#x00A0;Tru64 UNIX, Solaris, Linux&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;LD_LIBRARY_PATH<br />&#x00A0;&#x00A0;HP-UX&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; SHLIB_PATH<br /><br /></LI></UL> <b>II Additions to the guide<br /></b><br /> <OL>1. Operating system requirements<br /></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Platform-specific requirements:<br /> <OL>2. Important information for BEFORE the upgrade<br /></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B> Installation time for patch sets/patches</B><br />Install the latest Oracle patch set and associated available patches <B>before</B> carrying out the upgrade.<br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Current OUI Version</B><br />The following applies to Oracle patch sets &lt;=*******:<br />Install an updated version of the Oracle Universal Installer (OUI) as described in Note 601965. To install patches or patch sets, use the updated version only (2.2.0.16 or higher)!<br /><br />The following applies to Oracle Patchset 9.2.0.4:<br />To install Oracle patch set 9.2.0.4, you require OUI Version ********.0 at least (see patch set README). <br />To make it easier, this OUI Version is contained in this patch set. We recommend installing the OUI Version ********.0 contained in this patch set before installing the patch set itself, even if OUI ********.0 had already been downloaded and installed as a separate component.<br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Oracle Dictionary Consistency Check</B> <br />Before you upgrade from Oracle 8.1.7 to Oracle 9.2, check the following:<br /> SQL&gt;connect / as sysdba<br />SQL&gt;select count(*) from props$ where name = 'NLS_NCHAR_CHARACTERSET';<br />If the result is &gt; 1, there is an inconsistency in the Oracle Dictionary. This inconsistency would cause the u0801070.sql upgrade script with ORA-600 [16686],[947],[AL16UTF16],... to fail. For this reason, before continuing the upgrade, create a customer message so that Oracle Support can solve the problem.<br />Additional information: This problem may occur in databases that were created with Oracle 7.x. For more details, see the Oracle Bug #2964360.<br /><br />Also make sure that the prerequisites for Note 768537 have been met,<br />and if so, repair the Oracle Dictionary as it describes. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<OL>3. Important information for DURING the upgrade<br /></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In section 4.1.6 (\"Copying and adapting the SQL*Net Files\")<br />The SQL*Net file protocol.ora is no longer supported as of Oracle 9.2. If the SQL*Net files are copied from the previous location to a new location, and a 'protocol.ora' file exists in the previous location,the entries from protocol.ora need to be copied to the sqlnet.ora file.<br /> <OL>4. Important information for AFTER the upgrade</OL> <UL><LI>Section 5.8 of the guide describes how to copy the old Oracle NLS files,<br /> &#x00A0;&#x00A0;&lt;old_ora_home&gt;/ocommon/NLS_723/admin/data<BR/> &#x00A0;&#x00A0;&lt;old_ora_home&gt;/ocommon/NLS_733/admin/data<BR/> &#x00A0;&#x00A0;&lt;old_ora_home&gt;/ocommon/NLS_805/admin/data<BR/> &#x00A0;&#x00A0;&lt;old_ora_home&gt;/ocommon/NLS_806/admin/data<br />to the corresponding subdirectories of &lt;ora_home&gt;, that is<br /> &#x00A0;&#x00A0;&lt;ora_home&gt;/ocommon/NLS_723/admin/data<BR/> &#x00A0;&#x00A0;&lt;ora_home&gt;/ocommon/NLS_733/admin/data<BR/> &#x00A0;&#x00A0;&lt;ora_home&gt;/ocommon/NLS_805/admin/data<BR/> &#x00A0;&#x00A0;&lt;ora_home&gt;/ocommon/NLS_806/admin/data</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Even though you can delete &lt;old_ora_home&gt; if there are no errors,<br />do <B>not</B> delete &lt;old_ora_home&gt; <B>yet</B>.<br />We recommend the following procedure instead:</p> <UL><UL><LI>Rename &lt;old_ora_home&gt; to &lt;old_ora_home&gt;-deleteme, for example. This ensures that no parts of the old software are used any longer.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Proceed as described in section 5.17 of the guide:</p> <UL><UL><LI>Operate and test the system in this configuration for some time.</LI></UL></UL> <UL><UL><LI>Once everything functions properly, change the name of the directory back to &lt;old_ora_home&gt;.</LI></UL></UL> <UL><UL><LI>You can uninstall the old Oracle software using the Oracle Universal Installer. As user ora&lt;dbsid&gt;, call runInstaller and carry out the required actions. You can then delete the &lt;old_ora_home&gt; directory.<br /></LI></UL></UL> <OL>5. Required/available patch sets<br /></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For current Oracle 9. 2 patch sets, see Note 539921. Always import the latest patch set.<br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Oracle patch sets are cumulative, which means you only need to install <B>the current</B> patch set.<br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Install the current patch set immediately after installing the new database software, that is <B>before</B> upgrading the database. This ensures that the updated upgrade scripts are used.<br /> <OL>6. Importing individual patches<br /></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You may need to correct errors urgently for which there is no correction in any <B>patch set</B>. In this case, it may be necessary to import interim patches or individual patches in addition to the current patch set.<br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Such patches are installed using the Oracle tool OPatch. This program is written in Perl, and Note 306408 contains instructions for use.<br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Install all required patches immediately after installing the new database software and after installing the current patch set (<B>before </B> upgrading the database). This ensures that the updated upgrade script is used.<br /> <OL>7. Required/available patches<br /></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;An individual or interim patch always refers to a particular patch set. The patches available for a particular patch set can be found in the SAP Service Marketplace Software Center (http://service.sap.com/swcenter-3pmain) and downloaded from there: Database Patches ---&gt;<BR/> &#x00A0;&#x00A0;Oracle&#x00A0;&#x00A0;---&gt;<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;Oracle 32-Bit|Oracle 64-Bit ---&gt;<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Oracle &lt;Patch set version&gt; ---&gt;<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&lt;Hardware platform&gt; ---&gt;<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Patch set and available patches (list) <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B> It is important to refer to the notes specified for the patches.</B><br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Before installing patches, install an updated version of the Oracle Universal Installer (OUI) as described in Note 601965. Otherwise, the Oracle inventories may be corrupted when you install patches.<br />The current OUI Version must be at least ********.0 (see above).<br /> <OL>8. Important: Workarounds for Oracle errors<br /></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Oracle 9.2. 0: Unsorted result despite ORDER<br /><br />Note 594513 describes an Oracle error that may cause incorrect data in certain circumstances. Oracle Bug 2751254 was opened to deal with this problem. Refer to that note for information.<br /><br />This error was corrected in Oracle 9.2.0.4.<br /><br />There is a workaround for Oracle &lt; 9.2.0.4:<br />In init&lt;DBSID&gt;.ora, set event 10181. To do this, add the line:<br /> &#x00A0;&#x00A0;event=\"10181 trace name context forever, level 1\"<br />Note that this event line must follow the other event lines if there are any (without a blank line, comment line or similar in between).<br /><br />Now stop and restart the Oracle database.<br /> <OL>9. Oracle initialization parameters<br /></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In Oracle 9.2, the <BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;_b_tree_bitmap_plans = TRUE<BR/>initialization parameter is set by default. This parameter may lead to very unusual execution plans with very long runtimes. Unfortunately, the parameter is not visible in the default setting. For this reason, it is absolutely necessary to add <BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;_b_tree_bitmap_plans = FALSE<BR/>as a default setting in the initialization file.<br /> <OL>10. SAP profile parameters<br /></OL> <OL>11. Environment variables for &lt;sapsid&gt;adm<br /></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In addition to the settings described in the guide, set the following environment variables for &lt;sapsid&gt;adm: <BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;NLS_TIME_TZ_FORMAT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;= HH.MI.SSXFF AM<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;NLS_TIMESTAMP_TZ_FORMAT = DD-MON-RR HH.MI.SSXFF AM<BR/> <BR/>The corresponding profiles need to be edited. If the C-shell is being used, enter <BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;setenv NLS_TIME_TZ_FORMAT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; \"HH.MI.SSXFF AM\"<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;setenv NLS_TIMESTAMP_TZ_FORMAT&#x00A0;&#x00A0;\"DD-MON-RR HH.MI.SSXFF AM\"<BR/> <BR/>otherwise (Bourne shell, Korn shell, BASH, for example) enter <BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;NLS_TIME_TZ_FORMAT=\"HH.MI.SSXFF AM\"<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;export NLS_TIME_TZ_FORMAT<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;NLS_TIMESTAMP_TZ_FORMAT=\"DD-MON-RR HH.MI.SSXFF AM\"<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;export NLS_TIMESTAMP_TZ_FORMAT<BR/> These environment variables need to be set if the SAP Kernel uses an Oracle client, Version 9.2 (currently, these are the Extended Kernels in particular), or if the SAP Kernel uses an Oracle client of a lower version.<br /><br />Those environment variables prevent NLS incompatibilities, which can occur during SAP upgrades in particular.<br /> <OL>12. SAP tools for database administration<br /></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Install the current versions of SAPDBA and BR*Tools as described in Note 12741. Make sure that you install the programs that belong to Oracle 920.<br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Do not install the tools from the RDBMS CD: Using these may cause Oracle error ORA-12705. <OL>13. Oracle error messages during upgrade:<br /></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Numerous error messages that occur within the upgrade scripts are not critical. For information about this, refer to the list of error codes that can be ignored in Note 582427.<br /><br /> <b>III&#x00A0;&#x00A0;Errors in the guide<br /></b><br /> <OL>1. dba_registry<br /></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Section 3. 9 of the guide (\"Checking before Invalid Database Objects\") recommends executing the SQL command<br /> &#x00A0;&#x00A0;select comp_id, version, status from dba_registry;in the source release (8.1.7). However, since dba_registry does not exist in the source release, this leads to an error message.<br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution:<br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Do not execute the command.<br /> <OL>2. dbs_ora_tnsname<br /></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In section 6. 2 of the guide (\"Setting the environment variables for &lt;sapsid&gt;adm\"), step 3 describes how to set the 'dbs_ora_tsname' environment variable. This variable name is wrong. The correct name is 'dbs_ora_tnsname'. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<OL>3. Section 3.8, subsection \"USE\" (Extending the SYSTEM tablespace) recommends that you extend the size of the SYSTEM tablespace by at least 80 MB. This subsection should be as follows:</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"To prepare for the database upgrade, you must extend the system tablespace. Generally, you need at least 150 MB of free space in your system tablespace to carry out the upgrade. If space problems occur during the upgrade, you can restart the upgrade script after you solve the space problems.\" <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In the subsection \"Procedure\" in Section 3. 8 (Extending SYSTEM tablespace) there is also a command for extending the data file. This command should be as follows: <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ALTER TABLESPACE system ADD DATAFILE SIZE 150M AUTOEXTEND ON NEXT 10M MAXSIZE UNLIMITED;</div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D041703)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D030484)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000540021/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000540021/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000540021/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000540021/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000540021/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000540021/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000540021/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000540021/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000540021/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "768537", "RefComponent": "BC-DB-ORA", "RefTitle": "ora-00600 [koxsihread1] during after upgrade to Oracle 9", "RefUrl": "/notes/768537"}, {"RefNumber": "706132", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP Note: Problems with Oracle 9i", "RefUrl": "/notes/706132"}, {"RefNumber": "662219", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40 ORACLE", "RefUrl": "/notes/662219"}, {"RefNumber": "643409", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/643409"}, {"RefNumber": "637683", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/637683"}, {"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852"}, {"RefNumber": "601965", "RefComponent": "BC-DB-ORA", "RefTitle": "Inventory Corruption by OPatch", "RefUrl": "/notes/601965"}, {"RefNumber": "599110", "RefComponent": "BC-DB-ORA", "RefTitle": "Data corruption using JDBC and NCLOBs", "RefUrl": "/notes/599110"}, {"RefNumber": "599092", "RefComponent": "BC-DB-ORA", "RefTitle": "NullPointerException in PreparedStatement.clearParameters()", "RefUrl": "/notes/599092"}, {"RefNumber": "594513", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9.2.0: Result not sorted in spite of ORDER BY", "RefUrl": "/notes/594513"}, {"RefNumber": "592657", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-12701/ORA-12705/ORA-12709", "RefUrl": "/notes/592657"}, {"RefNumber": "587896", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/587896"}, {"RefNumber": "582427", "RefComponent": "BC-DB-ORA", "RefTitle": "Error due to inconsistent Oracle DDIC", "RefUrl": "/notes/582427"}, {"RefNumber": "581049", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle generates a large number of useless trace files", "RefUrl": "/notes/581049"}, {"RefNumber": "580318", "RefComponent": "BC-DB-ORA", "RefTitle": "Database crash: ORA-600[1100] and ORA-600[kcbget_37]", "RefUrl": "/notes/580318"}, {"RefNumber": "579540", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-00381, unable to start the Oracle 9.x database", "RefUrl": "/notes/579540"}, {"RefNumber": "573784", "RefComponent": "BC-DB-ORA", "RefTitle": "Work procs. terminate when started on HP/UX & Oracle 9.2.0", "RefUrl": "/notes/573784"}, {"RefNumber": "567301", "RefComponent": "BC-DB-ORA", "RefTitle": "Upgrade from 8.1.7 to 9.2 fails with ORA-3114 and ORA-7445", "RefUrl": "/notes/567301"}, {"RefNumber": "539970", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/539970"}, {"RefNumber": "539922", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/539922"}, {"RefNumber": "539921", "RefComponent": "BC-DB-ORA", "RefTitle": "Current patch set for Oracle 9.2.0", "RefUrl": "/notes/539921"}, {"RefNumber": "521230", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Client software 9i or lower on UNIX", "RefUrl": "/notes/521230"}, {"RefNumber": "513536", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/513536"}, {"RefNumber": "502532", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/502532"}, {"RefNumber": "406140", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/406140"}, {"RefNumber": "395521", "RefComponent": "BC-DB-ORA", "RefTitle": "runInstaller problems and solutions", "RefUrl": "/notes/395521"}, {"RefNumber": "356370", "RefComponent": "BC-OP-TRU64", "RefTitle": "Kernel parameters for HP Tru64 UNIX 5.x", "RefUrl": "/notes/356370"}, {"RefNumber": "355779", "RefComponent": "BC-DB-ORA", "RefTitle": "SAP work process hangs", "RefUrl": "/notes/355779"}, {"RefNumber": "355776", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle9i: Patch set installation", "RefUrl": "/notes/355776"}, {"RefNumber": "355774", "RefComponent": "BC-DB-ORA", "RefTitle": "Incorrect results from the database server", "RefUrl": "/notes/355774"}, {"RefNumber": "355773", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Technology Network (OTN)", "RefUrl": "/notes/355773"}, {"RefNumber": "350251", "RefComponent": "BC-DB-ORA-INS", "RefTitle": "Creating a New or 2nd Oracle SID with runInstaller", "RefUrl": "/notes/350251"}, {"RefNumber": "306408", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9.2.0: Using OPatch to install patches", "RefUrl": "/notes/306408"}, {"RefNumber": "180605", "RefComponent": "BW-SYS-DB", "RefTitle": "Oracle database parameter settings for BW", "RefUrl": "/notes/180605"}, {"RefNumber": "180430", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "Installing the ORACLE client software for UNIX", "RefUrl": "/notes/180430"}, {"RefNumber": "12741", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Current versions of BR*Tools", "RefUrl": "/notes/12741"}, {"RefNumber": "124361", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle parameterization (R/3 >= 4.x, Oracle 8.x/9.x)", "RefUrl": "/notes/124361"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "12741", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Current versions of BR*Tools", "RefUrl": "/notes/12741 "}, {"RefNumber": "539921", "RefComponent": "BC-DB-ORA", "RefTitle": "Current patch set for Oracle 9.2.0", "RefUrl": "/notes/539921 "}, {"RefNumber": "306408", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9.2.0: Using OPatch to install patches", "RefUrl": "/notes/306408 "}, {"RefNumber": "662219", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40 ORACLE", "RefUrl": "/notes/662219 "}, {"RefNumber": "124361", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle parameterization (R/3 >= 4.x, Oracle 8.x/9.x)", "RefUrl": "/notes/124361 "}, {"RefNumber": "521230", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Client software 9i or lower on UNIX", "RefUrl": "/notes/521230 "}, {"RefNumber": "180430", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "Installing the ORACLE client software for UNIX", "RefUrl": "/notes/180430 "}, {"RefNumber": "180605", "RefComponent": "BW-SYS-DB", "RefTitle": "Oracle database parameter settings for BW", "RefUrl": "/notes/180605 "}, {"RefNumber": "592657", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-12701/ORA-12705/ORA-12709", "RefUrl": "/notes/592657 "}, {"RefNumber": "582427", "RefComponent": "BC-DB-ORA", "RefTitle": "Error due to inconsistent Oracle DDIC", "RefUrl": "/notes/582427 "}, {"RefNumber": "356370", "RefComponent": "BC-OP-TRU64", "RefTitle": "Kernel parameters for HP Tru64 UNIX 5.x", "RefUrl": "/notes/356370 "}, {"RefNumber": "355773", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Technology Network (OTN)", "RefUrl": "/notes/355773 "}, {"RefNumber": "768537", "RefComponent": "BC-DB-ORA", "RefTitle": "ora-00600 [koxsihread1] during after upgrade to Oracle 9", "RefUrl": "/notes/768537 "}, {"RefNumber": "395521", "RefComponent": "BC-DB-ORA", "RefTitle": "runInstaller problems and solutions", "RefUrl": "/notes/395521 "}, {"RefNumber": "601965", "RefComponent": "BC-DB-ORA", "RefTitle": "Inventory Corruption by OPatch", "RefUrl": "/notes/601965 "}, {"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852 "}, {"RefNumber": "706132", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP Note: Problems with Oracle 9i", "RefUrl": "/notes/706132 "}, {"RefNumber": "567301", "RefComponent": "BC-DB-ORA", "RefTitle": "Upgrade from 8.1.7 to 9.2 fails with ORA-3114 and ORA-7445", "RefUrl": "/notes/567301 "}, {"RefNumber": "350251", "RefComponent": "BC-DB-ORA-INS", "RefTitle": "Creating a New or 2nd Oracle SID with runInstaller", "RefUrl": "/notes/350251 "}, {"RefNumber": "355774", "RefComponent": "BC-DB-ORA", "RefTitle": "Incorrect results from the database server", "RefUrl": "/notes/355774 "}, {"RefNumber": "355776", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle9i: Patch set installation", "RefUrl": "/notes/355776 "}, {"RefNumber": "594513", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9.2.0: Result not sorted in spite of ORDER BY", "RefUrl": "/notes/594513 "}, {"RefNumber": "599092", "RefComponent": "BC-DB-ORA", "RefTitle": "NullPointerException in PreparedStatement.clearParameters()", "RefUrl": "/notes/599092 "}, {"RefNumber": "599110", "RefComponent": "BC-DB-ORA", "RefTitle": "Data corruption using JDBC and NCLOBs", "RefUrl": "/notes/599110 "}, {"RefNumber": "581049", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle generates a large number of useless trace files", "RefUrl": "/notes/581049 "}, {"RefNumber": "580318", "RefComponent": "BC-DB-ORA", "RefTitle": "Database crash: ORA-600[1100] and ORA-600[kcbget_37]", "RefUrl": "/notes/580318 "}, {"RefNumber": "579540", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-00381, unable to start the Oracle 9.x database", "RefUrl": "/notes/579540 "}, {"RefNumber": "355779", "RefComponent": "BC-DB-ORA", "RefTitle": "SAP work process hangs", "RefUrl": "/notes/355779 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}