{"Request": {"Number": "834661", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 438, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000004551762017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=773F6764A22F6D6C9B2A8C17FACB5197"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "834661"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.05.2014"}, "SAPComponentKey": {"_label": "Component", "value": "FS-BP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Partner"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Services", "value": "FS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Partner", "value": "FS-BP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS-BP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "834661 - BP_TR2: Order/task field not ready for input"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You execute the business partner conversion of the phase II and have customer-specific objects that are relevant for a conversion. On the selection screen of the RFTBUH05 program, you want to select an existing transport request in the production mode before executing the program.<br />However, the \"Order/task\" field is not ready for input.<br />The same symptom can occur also with the programs RFTBUH03, RFTBUH04 and RFTBUH06.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>UPART02, conversion, business partner</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>During the business partner conversion, the following programs of customer-specific DDIC objects can be determined.</p> <UL><LI>RFTBUH02_1 Generation of lists of data elements to be converted from domains</LI></UL> <UL><LI>RFTBUH05 Copying data elements</LI></UL> <UL><LI>RFTBUH03 Exchanging domains in data elements</LI></UL> <UL><LI>RFTBUH06 Changing the data elements</LI></UL> <UL><LI>RFTBUH04 Changing the foreign key</LI></UL> <p><br />If the RFTBUH02_1 program determined data elements that are relevant for the conversion, this is noted by an identifier in the BPUM_CTL table. The following programs RFTBUH03 to RFTBUH06 verify the identification in production-mode and modify the attributes on the selection screen for the field \"Order/Task\". This becomes the required entry field and is ready for input.<br />No conversion-relevant data elements were determined, and no entry can be made in the field \"Order/Task\".</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Implement the correction specified below using Note Assistant or import the specified Support Package.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "C5029681"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D042568)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "877345", "RefComponent": "FS-BP", "RefTitle": "BP_TR2: Require an order number for order/task field", "RefUrl": "/notes/877345"}, {"RefNumber": "398888", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Composite note and FAQs about bus partner conversion", "RefUrl": "/notes/398888"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "398888", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Composite note and FAQs about bus partner conversion", "RefUrl": "/notes/398888 "}, {"RefNumber": "877345", "RefComponent": "FS-BP", "RefTitle": "BP_TR2: Require an order number for order/task field", "RefUrl": "/notes/877345 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-FINSERV", "From": "110", "To": "110", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "200", "To": "200", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "BANK/CFM", "From": "463_20", "To": "463_20", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-FINSERV 110", "SupportPackage": "SAPKGPFA21", "URL": "/supportpackage/SAPKGPFA21"}, {"SoftwareComponentVersion": "EA-FINSERV 200", "SupportPackage": "SAPKGPFB10", "URL": "/supportpackage/SAPKGPFB10"}, {"SoftwareComponentVersion": "BANK/CFM 463_20", "SupportPackage": "SAPKIPBJ27", "URL": "/supportpackage/SAPKIPBJ27"}, {"SoftwareComponentVersion": "EA-FINSERV 500", "SupportPackage": "SAPKGPFC08", "URL": "/supportpackage/SAPKGPFC08"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "EA-FINSERV", "NumberOfCorrin": 3, "URL": "/corrins/**********/201"}, {"SoftwareComponent": "BANK/CFM", "NumberOfCorrin": 1, "URL": "/corrins/**********/59"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 4, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "BANK/CFM", "ValidFrom": "463_20", "ValidTo": "463_20", "Number": "591007 ", "URL": "/notes/591007 ", "Title": "SAP BP Conversion:Composite SAP note various error correctns", "Component": "FS-BP"}, {"SoftwareComponent": "BANK/CFM", "ValidFrom": "463_20", "ValidTo": "463_20", "Number": "736512 ", "URL": "/notes/736512 ", "Title": "BP_TR2: Update of the business partner conversion 2004", "Component": "FS-BP"}, {"SoftwareComponent": "BANK/CFM", "ValidFrom": "463_20", "ValidTo": "463_20", "Number": "785156 ", "URL": "/notes/785156 ", "Title": "BP_TR2: Update of the business partner conversion 2004", "Component": "FS-BP"}, {"SoftwareComponent": "BANK/CFM", "ValidFrom": "463_20", "ValidTo": "463_20", "Number": "832236 ", "URL": "/notes/832236 ", "Title": "BP_TR2: Runtime error DYNPRO_MSG_IN_HELP", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "110", "ValidTo": "110", "Number": "591007 ", "URL": "/notes/591007 ", "Title": "SAP BP Conversion:Composite SAP note various error correctns", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "110", "ValidTo": "110", "Number": "736512 ", "URL": "/notes/736512 ", "Title": "BP_TR2: Update of the business partner conversion 2004", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "110", "ValidTo": "110", "Number": "785156 ", "URL": "/notes/785156 ", "Title": "BP_TR2: Update of the business partner conversion 2004", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "110", "ValidTo": "110", "Number": "832236 ", "URL": "/notes/832236 ", "Title": "BP_TR2: Runtime error DYNPRO_MSG_IN_HELP", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "200", "ValidTo": "200", "Number": "736512 ", "URL": "/notes/736512 ", "Title": "BP_TR2: Update of the business partner conversion 2004", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "200", "ValidTo": "200", "Number": "785156 ", "URL": "/notes/785156 ", "Title": "BP_TR2: Update of the business partner conversion 2004", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "200", "ValidTo": "200", "Number": "832236 ", "URL": "/notes/832236 ", "Title": "BP_TR2: Runtime error DYNPRO_MSG_IN_HELP", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "500", "ValidTo": "500", "Number": "736512 ", "URL": "/notes/736512 ", "Title": "BP_TR2: Update of the business partner conversion 2004", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "500", "ValidTo": "500", "Number": "785156 ", "URL": "/notes/785156 ", "Title": "BP_TR2: Update of the business partner conversion 2004", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "500", "ValidTo": "500", "Number": "832236 ", "URL": "/notes/832236 ", "Title": "BP_TR2: Runtime error DYNPRO_MSG_IN_HELP", "Component": "FS-BP"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}