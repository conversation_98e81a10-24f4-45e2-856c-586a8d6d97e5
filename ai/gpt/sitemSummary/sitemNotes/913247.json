{"Request": {"Number": "913247", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 227, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016040002017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000913247?language=E&token=B24AD887EA020FDE6D862897F700B634"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000913247", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000913247/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "913247"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.12.2008"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "913247 - Performance problems due to open changes"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The system reads an inexplicably high number of blocks in an SQL statement. This increases the CPU consumption.<br /><br />Wait situations may occur on \"latch: cache buffers chains\" and/or \"latch: undo global data\" (Note 767414).<br /><br />The portion of \"consistent changes\" in \"session logical reads\" (that is, the portion in undo accesses) is relatively high in V$SYSSTAT and V$SESSTAT.<br /><br />For specific sessions, the value of CONSISTENT_CHANGES in V$SESS_IO&#x00A0;&#x00A0;is high and increases further.<br /><br />At the same time, there are open transactions that made changes to entries of the underlying table.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>If a data record in a block of a table is changed by an INSERT, UPDATE or DELETE, this change is not globally visible as long as no COMMIT is executed. If another transaction accesses data in the same block in this type of situation, the last committed status of the block (before the change) must first be reconstructed from the current (not globally visible) status of the block. For this purpose, a Consistent Read-Image (CR) of the block is created on the basis of the current block status and the rollback information that still exists. This process requires (a minimum of) additional time concerning CPU consumption and block accesses. For example, the access to the rollback information causes an additional memory block access (\"Buffer Get\").<br /><br />Provided that read operations and open changes rarely concern the same block, the additional time involved is minimal and can be ignored. However, if there are a great many open changes in a block, access performance may decrease considerably. This type of situation is characterized by an inexplicably high number of Buffer Gets and high CPU consumption of the reading process. However, bear in mind that there are numerous other, much more commonly occurring causes of these symptoms (see Notes 712624 and 766349), so that the converse situation involving a high number of Buffer Gets and high CPU consumption does not usually apply to this problem with the open changes.<br /><br />If a questionable access is executed, the number of times the undo data is accessed until now compared to the entire number of logical reads can be determined using the following SELECT for the relevant Oracle session (&lt;sid&gt; = ID of the Oracle session involved).<br /><br />SELECT<br />&#x00A0;&#x00A0;TO_CHAR(SID, 999999990) SESSION_ID,<br />&#x00A0;&#x00A0;TO_CHAR(BLOCK_GETS + CONSISTENT_GETS, 999999999990) LOGICAL_READS,<br />&#x00A0;&#x00A0;TO_CHAR(CONSISTENT_CHANGES, 999999999990) UNDO_ACCESSES,<br />&#x00A0;&#x00A0;TO_CHAR(DECODE(BLOCK_GETS + CONSISTENT_GETS, 0, 0,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;CONSISTENT_CHANGES / (BLOCK_GETS + CONSISTENT_GETS) * 100),<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;990.99) PERCENT<br />FROM<br />&#x00A0;&#x00A0;V$SESS_IO<br />WHERE<br />&#x00A0;&#x00A0;SID = &lt;sid&gt;;<br /><br />The number of undo accesses in the intervening period can be determined by multiple executions and calculating the difference.<br /><br />The problems described above are triggered when the following factors are combined:</p> <UL><LI>You carry out several changes that have not been committed for a long time.</LI></UL> <UL><LI>At the same time, there are read accesses to the same table.</LI></UL> <UL><LI>The data to be read often comes from the same blocks as the open changes (for example, because the table is only very small so that all the data records are contained in a few blocks).</LI></UL> <UL><LI>The number of Buffer Gets for each execution performed by the selections is inexplicably high.<br /></LI></UL> <p>One area that is potentially affected by this problem is the parallel number range buffering by means of the NRIVSHADOW table in accordance with Note 599157. The table is usually very small (few entries for each work process), several SELECTs and UPDATEs are performed, and depending on the application process used, it can take a long time until changes are committed.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>To ease the problem, you can reduce the number of parallel open changes in the individual Oracle blocks. For this purpose, the following approaches are possible:</p> <OL>1. Increasing the COMMIT frequency in the application</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;From an application point of view, make sure that COMMITs are executed more frequently. In the case of background jobs, split the data volume to be processed into several smaller units that can be committed separately. <OL>2. Reducing the data records for each Oracle block</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Especially in the case of small tables (such as NRIVSHADOW), we recommend that you considerably reduce the number of data records for each Oracle block. The lower the number of data records in a block, the more likely it is that the number of open changes is low in the block as well. On the downside, more space is required in the table, but this disadvantage is hardly relevant for small tables. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;By increasing the PCTFREE storage parameter, you can ensure that the individual table blocks and index blocks can only include a small number of data records: <UL><LI>Table:<br /><br />ALTER TABLE \"&lt;table_name&gt;\" PCTFREE 99 PCTUSED 1,</LI></UL> <UL><LI>Related indexes:<br /><br />ALTER INDEX \"&lt;index_name&gt;\" REBUILD ONLINE PCTFREE 99,</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Afterwards, you can convert the table (for example, in transaction SE14 -&gt; \"Extras\" -&gt; \"Force Conversion\") so that the changed settings become active. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You now only have to create new statistics for the tables affected (for example, using transaction DB20). <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Always carry out the described changes in a non-productive environment with comparable data and load characteristics first, and check the results there. Proceeding in an improper manner can result in serious side effects (for example, if you use PCTFREE=99 for a large table).</div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D030484)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D021978)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000913247/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000913247/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000913247/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000913247/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000913247/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000913247/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000913247/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000913247/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000913247/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "767414", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle latches", "RefUrl": "/notes/767414"}, {"RefNumber": "766349", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle SQL optimization", "RefUrl": "/notes/766349"}, {"RefNumber": "712624", "RefComponent": "BC-DB-ORA", "RefTitle": "High CPU consumption by Oracle", "RefUrl": "/notes/712624"}, {"RefNumber": "618868", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle performance", "RefUrl": "/notes/618868"}, {"RefNumber": "599157", "RefComponent": "BC-SRV-NUM", "RefTitle": "Number ranges: New buffering method", "RefUrl": "/notes/599157"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "618868", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle performance", "RefUrl": "/notes/618868 "}, {"RefNumber": "712624", "RefComponent": "BC-DB-ORA", "RefTitle": "High CPU consumption by Oracle", "RefUrl": "/notes/712624 "}, {"RefNumber": "766349", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle SQL optimization", "RefUrl": "/notes/766349 "}, {"RefNumber": "767414", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle latches", "RefUrl": "/notes/767414 "}, {"RefNumber": "599157", "RefComponent": "BC-SRV-NUM", "RefTitle": "Number ranges: New buffering method", "RefUrl": "/notes/599157 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}