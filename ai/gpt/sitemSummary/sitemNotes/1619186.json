{"Request": {"Number": "1619186", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 233, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017289562017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001619186?language=E&token=16051B7F58C787662F849A9C919BD220"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001619186", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001619186/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1619186"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.08.2011"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-BD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Basic Data"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Data", "value": "RE-FX-BD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-BD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1619186 - FAQ: Master data"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The note contains links to consulting notes and notes to<br />support the error search in the RE-FX master data.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RE80, AO, architectural object</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>For further information, see the \"Reference to related Notes\" section.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D002072)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I035786)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001619186/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001619186/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001619186/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001619186/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001619186/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001619186/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001619186/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001619186/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001619186/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "939971", "RefComponent": "RE-FX", "RefTitle": "FAQ and consulting notes for RE-FX", "RefUrl": "/notes/939971"}, {"RefNumber": "1784653", "RefComponent": "RE-FX-BD", "RefTitle": "RE80: Objects with validity in the past", "RefUrl": "/notes/1784653"}, {"RefNumber": "1748131", "RefComponent": "RE-FX-BD", "RefTitle": "Assigning business entities to architectural object", "RefUrl": "/notes/1748131"}, {"RefNumber": "1694856", "RefComponent": "XX-PROJ-GEOE", "RefTitle": "SAP GEO.e - Geographcial enablement of SAP", "RefUrl": "/notes/1694856"}, {"RefNumber": "1678180", "RefComponent": "RE-FX-BD", "RefTitle": "Additional texts: Customer-specific fields", "RefUrl": "/notes/1678180"}, {"RefNumber": "1642150", "RefComponent": "RE-FX", "RefTitle": "Generating reminder dates for usage objects or contracts", "RefUrl": "/notes/1642150"}, {"RefNumber": "1618971", "RefComponent": "RE-FX-BP", "RefTitle": "FAQ: Partner", "RefUrl": "/notes/1618971"}, {"RefNumber": "1583036", "RefComponent": "RE-FX-BD", "RefTitle": "BAdI for fixtures and fittings characteristics", "RefUrl": "/notes/1583036"}, {"RefNumber": "1581640", "RefComponent": "RE-FX-BD", "RefTitle": "Worklist for objects with deletion flag also", "RefUrl": "/notes/1581640"}, {"RefNumber": "1553181", "RefComponent": "RE-FX-IS", "RefTitle": "Info system: Fixtures and fittings for master data", "RefUrl": "/notes/1553181"}, {"RefNumber": "1552146", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1552146"}, {"RefNumber": "1522702", "RefComponent": "RE-FX-BD", "RefTitle": "Fixtures and fittings characteristics and a telephone number", "RefUrl": "/notes/1522702"}, {"RefNumber": "1492062", "RefComponent": "RE-FX-CN", "RefTitle": "Changing the person responsible for reminder dates", "RefUrl": "/notes/1492062"}, {"RefNumber": "1485205", "RefComponent": "RE-FX", "RefTitle": "BAdI BADI_RE_IS_RM, method GET_RECORD, DISPLAY_RECORD", "RefUrl": "/notes/1485205"}, {"RefNumber": "1485145", "RefComponent": "RE-FX-BD", "RefTitle": "Architectural object: Creating usage object for architecture", "RefUrl": "/notes/1485145"}, {"RefNumber": "1483324", "RefComponent": "RE-FX-BD", "RefTitle": "Process reminder dates for usage objects", "RefUrl": "/notes/1483324"}, {"RefNumber": "1470846", "RefComponent": "RE-FX-BD", "RefTitle": "RE80: BAdI for changing detail data", "RefUrl": "/notes/1470846"}, {"RefNumber": "1468973", "RefComponent": "RE-FX-BD", "RefTitle": "Preventing manual change of total measurements", "RefUrl": "/notes/1468973"}, {"RefNumber": "1434508", "RefComponent": "RE-FX-BD", "RefTitle": "Fixtures and fittings characteristics for land", "RefUrl": "/notes/1434508"}, {"RefNumber": "1371399", "RefComponent": "RE-FX-CN", "RefTitle": "Reminder: Memos for reminder rule", "RefUrl": "/notes/1371399"}, {"RefNumber": "1326893", "RefComponent": "RE-FX-BD", "RefTitle": "Rental object: Creating a usage view from architecture", "RefUrl": "/notes/1326893"}, {"RefNumber": "1315287", "RefComponent": "RE-FX-BD", "RefTitle": "Search option in tree when assigning fixtures+fittings char", "RefUrl": "/notes/1315287"}, {"RefNumber": "1136424", "RefComponent": "RE-FX-BD", "RefTitle": "Marking objects that are not valid for current today", "RefUrl": "/notes/1136424"}, {"RefNumber": "1005831", "RefComponent": "RE-FX-BD", "RefTitle": "BAdI for customer-specific authorization check for RE obj", "RefUrl": "/notes/1005831"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1784653", "RefComponent": "RE-FX-BD", "RefTitle": "RE80: Objects with validity in the past", "RefUrl": "/notes/1784653 "}, {"RefNumber": "1748131", "RefComponent": "RE-FX-BD", "RefTitle": "Assigning business entities to architectural object", "RefUrl": "/notes/1748131 "}, {"RefNumber": "939971", "RefComponent": "RE-FX", "RefTitle": "FAQ and consulting notes for RE-FX", "RefUrl": "/notes/939971 "}, {"RefNumber": "1553181", "RefComponent": "RE-FX-IS", "RefTitle": "Info system: Fixtures and fittings for master data", "RefUrl": "/notes/1553181 "}, {"RefNumber": "1642150", "RefComponent": "RE-FX", "RefTitle": "Generating reminder dates for usage objects or contracts", "RefUrl": "/notes/1642150 "}, {"RefNumber": "1552146", "RefComponent": "RE-FX-BD", "RefTitle": "Workflow linkage of change documents during creation", "RefUrl": "/notes/1552146 "}, {"RefNumber": "1694856", "RefComponent": "XX-PROJ-GEOE", "RefTitle": "SAP GEO.e - Geographcial enablement of SAP", "RefUrl": "/notes/1694856 "}, {"RefNumber": "1678180", "RefComponent": "RE-FX-BD", "RefTitle": "Additional texts: Customer-specific fields", "RefUrl": "/notes/1678180 "}, {"RefNumber": "1618971", "RefComponent": "RE-FX-BP", "RefTitle": "FAQ: Partner", "RefUrl": "/notes/1618971 "}, {"RefNumber": "1468973", "RefComponent": "RE-FX-BD", "RefTitle": "Preventing manual change of total measurements", "RefUrl": "/notes/1468973 "}, {"RefNumber": "1485205", "RefComponent": "RE-FX", "RefTitle": "BAdI BADI_RE_IS_RM, method GET_RECORD, DISPLAY_RECORD", "RefUrl": "/notes/1485205 "}, {"RefNumber": "1371399", "RefComponent": "RE-FX-CN", "RefTitle": "Reminder: Memos for reminder rule", "RefUrl": "/notes/1371399 "}, {"RefNumber": "1136424", "RefComponent": "RE-FX-BD", "RefTitle": "Marking objects that are not valid for current today", "RefUrl": "/notes/1136424 "}, {"RefNumber": "1522702", "RefComponent": "RE-FX-BD", "RefTitle": "Fixtures and fittings characteristics and a telephone number", "RefUrl": "/notes/1522702 "}, {"RefNumber": "1005831", "RefComponent": "RE-FX-BD", "RefTitle": "BAdI for customer-specific authorization check for RE obj", "RefUrl": "/notes/1005831 "}, {"RefNumber": "1581640", "RefComponent": "RE-FX-BD", "RefTitle": "Worklist for objects with deletion flag also", "RefUrl": "/notes/1581640 "}, {"RefNumber": "1583036", "RefComponent": "RE-FX-BD", "RefTitle": "BAdI for fixtures and fittings characteristics", "RefUrl": "/notes/1583036 "}, {"RefNumber": "1315287", "RefComponent": "RE-FX-BD", "RefTitle": "Search option in tree when assigning fixtures+fittings char", "RefUrl": "/notes/1315287 "}, {"RefNumber": "1326893", "RefComponent": "RE-FX-BD", "RefTitle": "Rental object: Creating a usage view from architecture", "RefUrl": "/notes/1326893 "}, {"RefNumber": "1434508", "RefComponent": "RE-FX-BD", "RefTitle": "Fixtures and fittings characteristics for land", "RefUrl": "/notes/1434508 "}, {"RefNumber": "1470846", "RefComponent": "RE-FX-BD", "RefTitle": "RE80: BAdI for changing detail data", "RefUrl": "/notes/1470846 "}, {"RefNumber": "1485145", "RefComponent": "RE-FX-BD", "RefTitle": "Architectural object: Creating usage object for architecture", "RefUrl": "/notes/1485145 "}, {"RefNumber": "1483324", "RefComponent": "RE-FX-BD", "RefTitle": "Process reminder dates for usage objects", "RefUrl": "/notes/1483324 "}, {"RefNumber": "1492062", "RefComponent": "RE-FX-CN", "RefTitle": "Changing the person responsible for reminder dates", "RefUrl": "/notes/1492062 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}