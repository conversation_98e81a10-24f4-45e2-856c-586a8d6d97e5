{"Request": {"Number": "929353", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 280, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016069622017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000929353?language=E&token=B467BF532E0987D12EB88995736A8EE7"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000929353", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000929353/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "929353"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.04.2007"}, "SAPComponentKey": {"_label": "Component", "value": "CA-ESS-WD"}, "SAPComponentKeyText": {"_label": "Component", "value": "ESS Web Dynpro"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Employee Self-Service", "value": "CA-ESS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-ESS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "ESS Web Dynpro", "value": "CA-ESS-WD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-ESS-WD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "929353 - Changes to the Home and Area page customizing for LWE"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Firing Exit plug not allowed from NW04s SP 06, Personal Information scenarios in Life and work events not working,</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Exit plug, URL navigation, Absolute URL access, Life and work events, LWE, Persinfo, GP steps,</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>XSS Service warpper was using direct URL navigation. The support for this was withdrawn from NW04s SP06 causing a dump while the LWE were accessed.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The use of the XssSericeWrapper was discontinued.<br /><br />Architectural changes:<br />----------------------<br />The LWE processes were country scenario independent before this note. Now the processes are country specific. The SAP standard delivery will now contain processes that are based on the US country model. The personal information scenarios that are called will be specific to the US country version of ESS.<br /><br />The callable objects that were previously being connected to the XssServiceAdapter Webdynpro applicaiton will with this change point directly to the country specific Webdynpro applications.<br /><br />A new DC is introduced for LWE US. It is sap.com~ess~us~lwegp. This new DC replaces the DC sap.com~ess~lwegp.<br /><br />The R/3 changes are attached via a .SAR file along with this note. Incase the .SAR file import fails please follow the instrucations below.<br /><br />The following are the changes in the Home and Area page customizing :-<br />***************************<br />New entries:<br />____________<br /><br />IMG node access: Cross-applicaiton components -&gt; Homepage framework -&gt; Services -&gt; Define Services<br /><br />Table: V_T7XSSSERSRV<br /><br />Address<br />--------<br />Service Key: EMPLOYEE_PERSINFO_ADDRESS_US_05<br />Service Type: WebDynpro application<br />Link Resource: EMPLOYEE_ADDRESS_US_SERVICE05<br /><br />Bank<br />----<br />Service Key: EMPLOYEE_PERSINFO_BANK_US_05<br />Service Type: WebDynpro application<br />Link Resource: EMPLOYEE_BANK_US_SERVICE05<br /><br />Family/Dependents<br />-----------------<br />Service Key: EMPLOYEE_PERSINFO_FAMMEMBER_US_05<br />Service Type: WebDynpro application<br />Link Resource: EMPLOYEE_FAMMEMBER_US_SERVICE05<br /><br />Personal Data<br />-------------<br />Service Key: EMPLOYEE_PERSINFO_PERSDATA_US_05<br />Service Type: WebDynpro application<br />Link Resource: EMPLOYEE_PERSDATA_US_SERVICE05<br /><br />*****************************<br /><br />Modified Entries:<br />_________________<br /><br />IMG node access: Cross-applicaiton components -&gt; Homepage framework -&gt; Resources -&gt; Define Resources<br /><br />Table:V_T7XSSSERRES<br /><br />My First Days<br />-------------<br />Resource Key: EMPLOYEE_LIFEWORK_FIRSTDAYS_SERVICE<br /><br />New process.template.id: CECE64A19C8F11DAA5C1000E7FA67AEB<br />Old process.template.id: CE140F51C11711D9824C000BCD45EE55<br /><br />Benefits<br />--------<br />Resource Key: EMPLOYEE_LIFEWORK_BENEFITS_SERVICE<br /><br />New process.template.id: 98A7ADD19EC611DACE99000E7FA67AEB<br />Old process.template.id: 15101B90D8F311D9BDED000BCD45EE55<br /><br />Birth/Adoption<br />--------------<br /><br />Resource Key: EMPLOYEE_LIFEWORK_BIRTH_SERVICE<br /><br />New process.template.id: D6BCF4119ECE11DAACAB000E7FA67AEB<br />Old process.template.id: 4AA9B291D8F611D9B0CC000BCD45EE55<br /><br />Change Employment Status<br />------------------------<br /><br />Resource Key: EMPLOYEE_LIFEWORK_CHANGESTATUS_SERVICE<br />New process.template.id: E12529E19EFB11DABD46000E7FA67AEB<br />Old process.template.id: 949642D0D98A11D9AD70000BCD45EE55<br /><br />Divorce<br />------------------------<br /><br />Resource Key: EMPLOYEE_LIFEWORK_DIVORCE_SERVICE<br />New process.template.id: A63B3B20A3A211DAB62C000E7FA67AEB<br />Old process.template.id: BA955601D8F011D983D5000BCD45EE55<br /><br />Marriage<br />--------------------<br /><br />Resource Key: EMPLOYEE_LIFEWORK_MARRIAGE_SERVICE<br />New process.template.id: 29134A21A8E811DAA4C4000E7FA67AEB<br />Old process.template.id: 0DB88542C11911D9A01B000BCD45EE55<br /><br />Terminate Employment<br />--------------------<br /><br />Resource Key: EMPLOYEE_LIFEWORK_TERMINATEEMP_SERVICE<br />New process.template.id: 395D8671A8F711DA9B05000E7FA67AEB <br />Old process.template.id: 3FFF8341BD5511D9BB4C000BCD45EE55<br /><br />These changes will point to the new processes developed for the US country version. Please refer to the note 929447 for adopting Life and work events for countries other than US.<br /><br />With these changes Processes under Life and work event area page of Employee Self-Service Role can be accessed.<br /><br />However to access processes from the run time of Guided Procedure role, please find processes under the folder 'Life and Work Events - US'.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I031727)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000929353/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000929353/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000929353/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000929353/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000929353/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000929353/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000929353/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000929353/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000929353/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "P7HK005846.sar", "FileSize": "6", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000204132006&iv_version=0003&iv_guid=8ABBBADC9BFAFD47B6D7B7FC15A687C7"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "929447", "RefComponent": "CA-ESS-WD", "RefTitle": "Adopting Life and work events for countries other than US", "RefUrl": "/notes/929447"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "929447", "RefComponent": "CA-ESS-WD", "RefTitle": "Adopting Life and work events for countries other than US", "RefUrl": "/notes/929447 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-HR", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-HR 600", "SupportPackage": "SAPKGPHD04", "URL": "/supportpackage/SAPKGPHD04"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1043136", "RefTitle": "US specific LWE - Correction to the Home/Area customizing", "RefUrl": "/notes/0001043136"}]}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}