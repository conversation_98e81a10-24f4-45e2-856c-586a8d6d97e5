{"Request": {"Number": "1149742", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 318, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016482282017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001149742?language=E&token=23C0733CD97350ECDB5A25AE3809CE34"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001149742", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001149742/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1149742"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 63}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "08.10.2018"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SER"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Support Services"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Support Services", "value": "SV-SMG-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1149742 - SAP (CQC) Going Live Support"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>SAP CQC Going Live Support Service (CQC GLS) or SAP Going Live Support Service (GLS) is scheduled for your system. You need to know the prerequisites for the delivery of SAP Going Live Support.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Enterprise Support, Premium Support, Continuous Quality Checks, CQC, Support GoingLive, SGL, Preparation, Going Live Support, GLS</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Several prerequisites exist for the delivery of the SAP Going Live Support.<br />For the delivery of an SAP Going Live Support Service some analysis data is required which needs to be collected from the target system. The data collection is partly tool-based which requires a minimum release of the SAP Support plug-ins ST-A/PI or ST-PI. For the delivery in the managed systems specific authorizations are necessary. Additionally the SAP Solution Manager is used for the service delivery and also requires a minimum release level of certain components.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This SAP Note should help you to prepare your system environment for the GLS related monitoring and analysis.<br /><br /><strong>General preparation process for SAP Support Services</strong><br />First, you need to follow the general guidelines of the preparation process for SAP Support Services in SAP Note 91488. Please check also SAP Note 1498779 for preparing a delivery for Java-based systems. In addition, important specifics for the GLS can be found below.<br /><br /><strong>Context information needed for GLS delivery for Enterprise Support and MaxAttention/ActiveEmbedded</strong><br />Provide the following information to SAP prior to the service:</p>\r\n<ul>\r\n<li>The dates on which system monitoring should take place,</li>\r\n<li>Additional information (e.g. about important transactions, reports and interfaces) in case you require the session to be carried out with a specific focus,</li>\r\n<li>Information about open problems in the managed system(s) as well as Solution Manager (e.g. addressed within customer messages),</li>\r\n<li>Customer contact information in order to discuss observed problems and monitoring results, e.g. in a daily conference call during the GLS delivery.</li>\r\n</ul>\r\n<p><strong>Technical prerequisites in the managed system</strong></p>\r\n<ul>\r\n<li>Have the latest ST-PI installed, <strong>at least</strong></li>\r\n<ul>\r\n<li>Basis Release 6.20: ST-PI 2008_1_620 SP 5,</li>\r\n<li>Basis Release 6.40: ST-PI 2008_1_640 SP 5,</li>\r\n<li>Basis Release 7.00: ST-PI 2008_1_700 SP 5,</li>\r\n<li>Basis Release 7.10: ST-PI 2008_1_710 SP 5,</li>\r\n<li>Basis Release 7.40: ST-PI 2008_1_740 SP 0.</li>\r\n</ul>\r\n<li>In case the managed system runs on Basis Release 7.40 please read SAP Note 2163374 and&#160;follow this procedure:<br />- Check whether report /SSA/CAT exists and has no syntax errors<br />- If /SSA/CAT does not exist proceed with the following steps otherwise nothing is to be done.<br />1) Call RSA1 in your managed system<br />2) Run report RTCCTOOL, press button&#160; Addon&amp;Upgrade assistant, choose \"Procedure after addon implementation\" = Uncomment.<br />Note that the first step will trigger&#160;a background activity&#160;and the respective job (BI_TCO_ACTIVATION) has to be finished before performing step 2).</li>\r\n<li>Have the most recent ST-A/PI installed (at least ST-A/PI 01Q).</li>\r\n<li>In case of BW have either</li>\r\n<ul>\r\n<li>at least ST-A/PI 01R OR</li>\r\n<li>ST-A/PI 01Q and SAP Notes <a target=\"_blank\" href=\"/notes/1886979\" title=\"1886979  - CX_SY_ARITHMETIC_OVERFLOW when reading BW process chain data\">1886979 - CX_SY_ARITHMETIC_OVERFLOW when reading BW process chain data </a>and <a target=\"_blank\" href=\"/notes/1972227\" title=\"1972227  - COMPUTE_BCD_OVERFLOW when reading BW process chain data\">1972227 - COMPUTE_BCD_OVERFLOW when reading BW process chain data </a>implemented.</li>\r\n</ul>\r\n<li>Access to the productive systems and, if necessary, related components (e.g. Mobile Client for a CRM system, if required) that are in scope of the service.</li>\r\n<li>User IDs for the productive systems and related components that have all authorizations required for analysis (minimum authorization profile provided as per SAP Note 1405975).</li>\r\n<li>System access via http connection or equivalent (e.g. WTS) for all Java/doublestack systems</li>\r\n<li>In case of Kernel 720, check SAP Note 1938570 \"Unrealistic average response times after kernel upgrade\" to make sure that NRIV buffer operations are not counted as dialog steps in the ST03N statistics.</li>\r\n</ul>\r\n<p><strong>Prerequisites in the Solution Manager</strong></p>\r\n<ul>\r\n<li>Read the general information provided in SAP Notes 1170668 and 1172939,</li>\r\n<li>In case Service Content Update (see SAP Note 1143775)&#160;is used: Have ST-SER 2010_1 and the most recent Service Content Update installed</li>\r\n<li>In case Service Content Update is not used: Have at least ST-SER 2010_1 SP13 installed,</li>\r\n<li>SAP Solution Manager contains a Solution that is registered at SAP,</li>\r\n<li>Managed system(s) are available via Logical Components in the Solution,</li>\r\n<li>Access to the Solution Manager with all authorizations required for analysis (see SAP Note 872800),</li>\r\n<li>SAP Early Watch Alert is scheduled on a weekly basis in Solution Manager,</li>\r\n<li>Working&#160;RFC connection of type \"READ\" to each system in scope. Ensure, that</li>\r\n<ul>\r\n<li>the connection can be tested successfully</li>\r\n<li>the connection is to the client which is the focus of the service</li>\r\n<li>the user&#160;of the RFC connection has&#160;the authorizations&#160;described in SAP Notes 1572183 (SAP Solution Manager lower than 7.1 SP09) or 1830640 (SAP Solution Manager as of SP09), in particular object '&#160;S_ADMI_FCD', id '&#160;S_ADMI_FCD' and field 'ST0R'.</li>\r\n</ul>\r\n<li>Implement SAP Notes 1693213 and 1713093 to avoid incorrect display of graphical charts in the service report.</li>\r\n<li>Implement SAP Note 1739631 to avoid synchronization issues with the Service Delivery functionality in SAP Solution Manager.</li>\r\n<li>Activate option \"Send Alert to SAP\" (Work Center \"System Monitoring\" --&gt;&#160;Setup&#160;--&gt; Configuration for all solutions). See SAP Note 1793264 for more details.</li>\r\n</ul>\r\n<p><strong>Additional prerequisites for the analysis of systems with SAP HANA database</strong></p>\r\n<ul>\r\n<li>a general guideline for preparing support services for SAP HANA scenarios is provided in SAP Note 1892593,</li>\r\n<li>in SAP Solution Manager: either ST-SER 2010_1 SP17 (or higher) OR ST-SER 2010_1 with the respective Service Content Update.</li>\r\n</ul>\r\n<p><br /><strong>Additional prerequisites for the analysis of JAVA-based/doublestack systems</strong></p>\r\n<ul>\r\n<li>Systems of Production Landscape are available in SAP Solution Manager Diagnostics as part of the SAP Solution Manager and the systems are enabled for End-To-End (E2E) Analysis applications<br />&#160;&#160;&#160;&#160;--&gt; see SAP Note 1172939 section 'VI. Technical service preparation in Solution Manager Diagnostics (Java)' for details</li>\r\n<li>Access to SAP Solution Manager Diagnostics</li>\r\n<ul>\r\n<li>HTTP Connection or equivalent (e.g. WTS) is open for SAP Solution Manager Diagnostics and Wily Introscope WebView</li>\r\n<li>Access to transaction SOLMAN_WORKCENTER</li>\r\n<li>Assignment of Root-Cause-Analysis Workcenter to support user</li>\r\n<li>See also SAP Notes 1160651 and 1340423.</li>\r\n</ul>\r\n</ul>\r\n<p><br /><strong>Additional prerequisites for SAP Business Objects Business Intelligence 4.x (BI 4.x)</strong></p>\r\n<ul>\r\n<li>SMD and Wily are up and running (see SAP Note 1540591),</li>\r\n<li>SAP Solution Manager 7.1 or higher,</li>\r\n<li>EarlyWatch Alert (EWA) set up and running,</li>\r\n<li>the Analysis session of the CQC for Implementation was performed.</li>\r\n</ul>\r\n<p><br /><strong>Note:</strong></p>\r\n<ul>\r\n<li>GLS is not delivered for BOE Enterpise XI 3.x systems.</li>\r\n</ul>\r\n<p>&#160;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D042328)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D042328)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001149742/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001149742/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001149742/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001149742/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001149742/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001149742/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001149742/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001149742/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001149742/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488"}, {"RefNumber": "872800", "RefComponent": "SV-SMG-SER", "RefTitle": "Roles for SAP Solution Manager: SAP service provider", "RefUrl": "/notes/872800"}, {"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455"}, {"RefNumber": "662441", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/662441"}, {"RefNumber": "569116", "RefComponent": "SV-SMG-SER", "RefTitle": "Release strategy for Solution Manager Service Tools (ST-SER)", "RefUrl": "/notes/569116"}, {"RefNumber": "539977", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for add-on ST-PI", "RefUrl": "/notes/539977"}, {"RefNumber": "394616", "RefComponent": "SV-SMG-INS", "RefTitle": "Release strategy for SAP Solution Manager", "RefUrl": "/notes/394616"}, {"RefNumber": "2163374", "RefComponent": "SV-SMG-SDD", "RefTitle": "Syntaxerrors during installation of addon ST-A/PI due to unknown/undefined DDIC objects", "RefUrl": "/notes/2163374"}, {"RefNumber": "1972227", "RefComponent": "SV-SMG-SDD", "RefTitle": "COMPUTE_BCD_OVERFLOW when reading BW process chain data", "RefUrl": "/notes/1972227"}, {"RefNumber": "1938570", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Unrealistic average response times after kernel upgrade", "RefUrl": "/notes/1938570"}, {"RefNumber": "1892593", "RefComponent": "SV-SMG-SER", "RefTitle": "Preparing Support Services for SAP HANA Scenarios", "RefUrl": "/notes/1892593"}, {"RefNumber": "1886979", "RefComponent": "SV-SMG-SDD", "RefTitle": "CX_SY_ARITHMETIC_OVERFLOW when reading BW process chain data", "RefUrl": "/notes/1886979"}, {"RefNumber": "1830640", "RefComponent": "SV-SMG-AUT", "RefTitle": "Authorizations for SAP Solution Manager RFC users 7.1 SP09 - 7.2 SP01 set", "RefUrl": "/notes/1830640"}, {"RefNumber": "1793264", "RefComponent": "SV-SMG-SVD-SSP", "RefTitle": "Advanced Remote Service Delivery - Customer info", "RefUrl": "/notes/1793264"}, {"RefNumber": "1739631", "RefComponent": "SV-SMG-SVD", "RefTitle": "Service delivery session information not updated accordingly", "RefUrl": "/notes/1739631"}, {"RefNumber": "1713093", "RefComponent": "SV-SMG-SVD-SWB", "RefTitle": "DSA: Stacked bar chart maximum value not set correctly", "RefUrl": "/notes/1713093"}, {"RefNumber": "1693213", "RefComponent": "SV-SMG-SVD-SWB", "RefTitle": "Empty columns wrongly displayed in graphics", "RefUrl": "/notes/1693213"}, {"RefNumber": "1657403", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1657403"}, {"RefNumber": "1572183", "RefComponent": "SV-SMG-AUT", "RefTitle": "Authorizations for SAP Solution Manager RFC users", "RefUrl": "/notes/1572183"}, {"RefNumber": "1498779", "RefComponent": "SV-BO-REQ", "RefTitle": "Preparing Support Services for Systems with a Java Stack", "RefUrl": "/notes/1498779"}, {"RefNumber": "1405975", "RefComponent": "SV-SMG-SER", "RefTitle": "Minimum Authorization Profile for Remote Service Delivery", "RefUrl": "/notes/1405975"}, {"RefNumber": "1367586", "RefComponent": "SV-SMG-SDD", "RefTitle": "Monitoring report /SDF/MON generates syslog entries", "RefUrl": "/notes/1367586"}, {"RefNumber": "1340423", "RefComponent": "SV-SMG", "RefTitle": "SAP Solution Manager: Work center is not assigned", "RefUrl": "/notes/1340423"}, {"RefNumber": "1272343", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1272343"}, {"RefNumber": "1172939", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1172939"}, {"RefNumber": "1170668", "RefComponent": "SV-SMG-SVD", "RefTitle": "The role of SAP Solution Manager in Remote Service Delivery", "RefUrl": "/notes/1170668"}, {"RefNumber": "1167901", "RefComponent": "BC-MID-RFC", "RefTitle": "Trusted-Trusting instead of destination with password", "RefUrl": "/notes/1167901"}, {"RefNumber": "1160651", "RefComponent": "SV-SMG", "RefTitle": "Work Centers: How to Customize (Solution Manager)", "RefUrl": "/notes/1160651"}, {"RefNumber": "1132350", "RefComponent": "BC-CST-UP", "RefTitle": "Incomplete update records", "RefUrl": "/notes/1132350"}, {"RefNumber": "1076804", "RefComponent": "BC-CST-UP", "RefTitle": "Incomplete update records", "RefUrl": "/notes/1076804"}, {"RefNumber": "1061383", "RefComponent": "SV-SMG-DIA", "RefTitle": "End-to-End Diagnostics SP13", "RefUrl": "/notes/1061383"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1886979", "RefComponent": "SV-SMG-SDD", "RefTitle": "CX_SY_ARITHMETIC_OVERFLOW when reading BW process chain data", "RefUrl": "/notes/1886979 "}, {"RefNumber": "1972227", "RefComponent": "SV-SMG-SDD", "RefTitle": "COMPUTE_BCD_OVERFLOW when reading BW process chain data", "RefUrl": "/notes/1972227 "}, {"RefNumber": "1830640", "RefComponent": "SV-SMG-AUT", "RefTitle": "Authorizations for SAP Solution Manager RFC users 7.1 SP09 - 7.2 SP01 set", "RefUrl": "/notes/1830640 "}, {"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455 "}, {"RefNumber": "539977", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for add-on ST-PI", "RefUrl": "/notes/539977 "}, {"RefNumber": "394616", "RefComponent": "SV-SMG-INS", "RefTitle": "Release strategy for SAP Solution Manager", "RefUrl": "/notes/394616 "}, {"RefNumber": "1739631", "RefComponent": "SV-SMG-SVD", "RefTitle": "Service delivery session information not updated accordingly", "RefUrl": "/notes/1739631 "}, {"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488 "}, {"RefNumber": "662441", "RefComponent": "SV-SMG-INS", "RefTitle": "Solution Manager: SAP Notes for Support Packages", "RefUrl": "/notes/662441 "}, {"RefNumber": "1572183", "RefComponent": "SV-SMG-AUT", "RefTitle": "Authorizations for SAP Solution Manager RFC users", "RefUrl": "/notes/1572183 "}, {"RefNumber": "1498779", "RefComponent": "SV-BO-REQ", "RefTitle": "Preparing Support Services for Systems with a Java Stack", "RefUrl": "/notes/1498779 "}, {"RefNumber": "1405975", "RefComponent": "SV-SMG-SER", "RefTitle": "Minimum Authorization Profile for Remote Service Delivery", "RefUrl": "/notes/1405975 "}, {"RefNumber": "1657403", "RefComponent": "SV-SMG-SVD", "RefTitle": "SAP Support Remote Services for SAP BusinessObjects IDD/EIM", "RefUrl": "/notes/1657403 "}, {"RefNumber": "1077981", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Upgrade Assessment Preparation Note", "RefUrl": "/notes/1077981 "}, {"RefNumber": "1713093", "RefComponent": "SV-SMG-SVD-SWB", "RefTitle": "DSA: Stacked bar chart maximum value not set correctly", "RefUrl": "/notes/1713093 "}, {"RefNumber": "1693213", "RefComponent": "SV-SMG-SVD-SWB", "RefTitle": "Empty columns wrongly displayed in graphics", "RefUrl": "/notes/1693213 "}, {"RefNumber": "569116", "RefComponent": "SV-SMG-SER", "RefTitle": "Release strategy for Solution Manager Service Tools (ST-SER)", "RefUrl": "/notes/569116 "}, {"RefNumber": "1404075", "RefComponent": "SV-SMG-SDD", "RefTitle": "/SDF/MON causes CALL_FUNCTION_NOT_FOUND dumps", "RefUrl": "/notes/1404075 "}, {"RefNumber": "1132350", "RefComponent": "BC-CST-UP", "RefTitle": "Incomplete update records", "RefUrl": "/notes/1132350 "}, {"RefNumber": "1340423", "RefComponent": "SV-SMG", "RefTitle": "SAP Solution Manager: Work center is not assigned", "RefUrl": "/notes/1340423 "}, {"RefNumber": "1392411", "RefComponent": "XX-SER-GEN", "RefTitle": "Remote Access to SRM 5.0 - 7.0 solutions", "RefUrl": "/notes/1392411 "}, {"RefNumber": "1367586", "RefComponent": "SV-SMG-SDD", "RefTitle": "Monitoring report /SDF/MON generates syslog entries", "RefUrl": "/notes/1367586 "}, {"RefNumber": "872800", "RefComponent": "SV-SMG-SER", "RefTitle": "Roles for SAP Solution Manager: SAP service provider", "RefUrl": "/notes/872800 "}, {"RefNumber": "1076804", "RefComponent": "BC-CST-UP", "RefTitle": "Incomplete update records", "RefUrl": "/notes/1076804 "}, {"RefNumber": "1170668", "RefComponent": "SV-SMG-SVD", "RefTitle": "The role of SAP Solution Manager in Remote Service Delivery", "RefUrl": "/notes/1170668 "}, {"RefNumber": "1272343", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP UNICODE Downtime Assessment preparation note", "RefUrl": "/notes/1272343 "}, {"RefNumber": "1061383", "RefComponent": "SV-SMG-DIA", "RefTitle": "End-to-End Diagnostics SP13", "RefUrl": "/notes/1061383 "}, {"RefNumber": "1167901", "RefComponent": "BC-MID-RFC", "RefTitle": "Trusted-Trusting instead of destination with password", "RefUrl": "/notes/1167901 "}, {"RefNumber": "1160651", "RefComponent": "SV-SMG", "RefTitle": "Work Centers: How to Customize (Solution Manager)", "RefUrl": "/notes/1160651 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST-SER", "From": "700_2008_1", "To": "700_2008_1", "Subsequent": ""}, {"SoftwareComponent": "ST-SER", "From": "701_2008_2", "To": "701_2010_1", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}