{"Request": {"Number": "3020849", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 256, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001262532021"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003020849?language=E&token=020E56D9F985DBAE997C1C97E6BA8CFA"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003020849", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3020849"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "03.09.2021"}, "SAPComponentKey": {"_label": "Component", "value": "LO-CMM-BF"}, "SAPComponentKeyText": {"_label": "Component", "value": "Commodity Management - Basic Functions"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Logistics - General", "value": "LO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Commodity Management in Logistics", "value": "LO-CMM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO-CMM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Commodity Management - Basic Functions", "value": "LO-CMM-BF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO-CMM-BF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "3020849 - S4TWL - CM: CCTR CPE Commodity Code Harmonization"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p dir=\"ltr\">You are doing a system&#160;upgrade of SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case. During the upgrade, you have received a return code ERROR_SKIPPABLE (7) from consistency check class&#160;CLS4SIC_LOG_CMM_PRC_CMMDTY_HAR.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>central contract, central procurement, commodities, commodity management, commodity quantity, commodity weight,&#160;CLS4SIC_LOG_CMM_PRC_CMMDTY_HAR,</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<div class=\"OutlineElement Ltr  BCX0 SCXW27120900\">\r\n<p class=\"Paragraph  BCX0 SCXW27120900\"><strong>Description&#160;</strong></p>\r\n</div>\r\n<div class=\"OutlineElement Ltr  BCX0 SCXW27120900\">\r\n<p class=\"Paragraph  BCX0 SCXW27120900\">This simplification item is about the harmonization of commodity codes between central purchase contract and commodity management. In addition to the following guidelines, please also raise a support ticket to software component&#160;MM-PUR-GF-CPE with reference to this SAP Note.</p>\r\n</div>\r\n<div class=\"OutlineElement Ltr  BCX0 SCXW27120900\">\r\n<p class=\"Paragraph  BCX0 SCXW27120900\"><strong>Business Process related information</strong></p>\r\n</div>\r\n<div class=\"OutlineElement Ltr  BCX0 SCXW27120900\">\r\n<p>\"Commodity Code\" in the Central Contract is an identifier for a group of raw materials defined in customizing table MMPUR_CMDT_CODES.</p>\r\n<p>The target of this simplification item is to replace these commodity codes with the commodities of Commodity Management defined in customizing table TBAC_PHYSCOMM.</p>\r\n<p>The source identifier \"Commodity Code\" permits a string length of up to 50 characters, whereas the target identifier \"Commodity\" can have a maximum of 18 characters only. If a commodity code exceeds 18 characters, you must manually shorten the length of the identifier accordingly.</p>\r\n<p>In addition, for the target Customizing, you must manually define a unit of measure during the migration,</p>\r\n<p>This replacement allows an easier integration of the business area Commodity Management with Central Procurement, or vice versa.</p>\r\n</div>\r\n<div class=\"OutlineElement Ltr  BCX0 SCXW27120900\">\r\n<p><strong>Relevancy Check</strong></p>\r\n<p>This note is relevant for the following use cases:</p>\r\n<ul>\r\n<li>For SAP S/4HANA on Premise customers, who upgrade to S/4HANA 2021</li>\r\n<li>You have already been used central purchase contract functionalities and have defined commodity codes within the customizing table MMPUR_CMDT_CODES MMPUR.</li>\r\n<li>You have maintained commodity quantity data within central purchase contracts. For SAP S/4HANA on Premise customers, this can be checked by using transaction SE16 and checking, whether database table EKPO_WEIGHTS has any content.</li>\r\n<li>If the prior mentioned requirements apply, you must execute the migration steps stated previously before the upgrade to S/4HANA on Premise 2022.</li>\r\n</ul>\r\n</div>\r\n<div class=\"OutlineElement Ltr  BCX0 SCXW27120900\">\r\n<p><strong>Required and Recommended Actions</strong></p>\r\n<p>After the technical upgrade in the target release,&#160;the migration is to be performed in 3 steps. To guide you through the migration process, an assistance report is provided, which can be accessed in the following ways:</p>\r\n<ul>\r\n<li>SAP S/4HANA on Premise customers can perform this report by using transaction CMM_MIGRTN_COMMCODE.</li>\r\n<li>SAP S/4HANA on Premise customers can execute the report CMM_MIGRATE_COMMODITY_CODE via transaction SE38.</li>\r\n<li>SAP S/4HANA on Premise customers can access the Fiori Launchpad tile &#8220;Commodity Code Migration Assistant&#8221;, which can be found in business catalog &#8220;Commodity Management &#8211; Commodity Code Migration&#8221;.</li>\r\n<li>For SAP S/4HANA on Premise customers:<br />Business catalog SAP_CMM_BC_CMMDTY_MIGRTN and business catalog group SAP_CMM_BCG_CMMDTY_MIGRTN are part of the business role SAP_BR_CENTRAL_PURCHASER.</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\"><strong>1. Customizing Data Migration</strong></p>\r\n<p style=\"padding-left: 30px;\">For the migration of the customizing entries, perform the following steps:</p>\r\n<ol style=\"padding-left: 30px;\">\r\n<ul>\r\n<li style=\"padding-left: 30px;\">Open the Fiori tile &#8220;Commodity Code Migration Assistant&#8221;. If this tile is not present on your Fiori Launchpad, use &#8220;App Finder&#8221; to search for it.</li>\r\n<li style=\"padding-left: 30px;\">A table control is presented with a list of available commodity codes within your system. This list contains the &#8220;Migration Status&#8221; flag, which indicates whether a commodity code has already been migrated. If a commodity code has already been migrated, the data cannot be adjusted anymore within this report.</li>\r\n<li style=\"padding-left: 30px;\">If either the commodity or the mapping needs to be adjusted, use the previously mentioned self-service configuration UIs for the adjustment.</li>\r\n<li style=\"padding-left: 30px;\">If the commodity code length is below or equal to 18 characters, the system will propose the commodity identifier. You can adjust this commodity identifier if required. If the commodity code length exceeds 18 characters, you must provide a commodity identifier with less or equal to 18 characters.</li>\r\n<li style=\"padding-left: 30px;\">Enter a unit of measure for every commodity.</li>\r\n<li style=\"padding-left: 30px;\">Select the &#8220;Migrate Customizing&#8221; button within the toolbar. If the system asks for a customizing transport, provide a propriate one.</li>\r\n<li style=\"padding-left: 30px;\">If problems occurred during the customizing migration see the message log.</li>\r\n</ul>\r\n</ol>\r\n<p style=\"padding-left: 30px;\">The customizing changes must be transported from your quality tenant to your productive tenant.</p>\r\n<p style=\"padding-left: 30px;\"><strong>2. Application Data Migration</strong></p>\r\n<p style=\"padding-left: 30px;\">As a next step application data can be migrated within the quality tenant for testing purposes and in addition within your productive tenant. SAP strongly recommends to execute this step during times where users do not actively edit central contracts.. To execute the application data migration, proceed as follows:</p>\r\n<ol start=\"1\" style=\"padding-left: 30px;\">\r\n<ul>\r\n<li style=\"padding-left: 30px;\">Access the assistant with the Fiori tile &#8220;Commodity Code Migration Assistant&#8221; and select the &#8220;Migrate Application Data\" button.</li>\r\n<li style=\"padding-left: 30px;\">Now, the system shows a list of messages. If errors occurred during processing, check the errors. Repeat executing the migrate function until no error messages occurred. If an object was locked by another user, re-execute this step.</li>\r\n</ul>\r\n</ol>\r\n<p style=\"padding-left: 30px;\">With the migration of the application data, the new solution has not been activated yet on the user interface. You will still see the old commodity code fields within the central contract but, if editing the commodity quantity records, the system will keep the values in sync.</p>\r\n<p style=\"padding-left: 30px;\">Note:<br />Ensure that all central contracts are migrated from commodity code to commodity before activating the feature toggle.</p>\r\n<p style=\"padding-left: 30px;\"><strong>3. Activation of &#8220;Commodity&#8221; for S/4HANA On Premise Customers</strong></p>\r\n<p style=\"padding-left: 30px;\">To finally activate the new solution, you can use the &#8220;Activate Commodity&#8221; button within the assistant, Alternatively, directly run transaction SFW5 &#8220;Switch Framework: Display Business Function Status&#8221;. Expand the &#8220;LIGHTWEIGHT_FUNCTIONS&#8221; node and search for business function &#8220;LOG_CMM_PRC_BF_MIGRATION&#8221;. Activate this business function from the context menu.</p>\r\n<p style=\"padding-left: 30px;\">After this activation, you will see the columns for commodity and commodity description in the commodity quantity facet of the central contract, and the columns for the commodity code will be disappeared. On the condition amount object page for CPE conditions, within the screen section &#8220;Commodity Details&#8221;, the &#8220;commodity code&#8221; field will be replaced by &#8220;commodity&#8221;. From now on, with the database table for commodity quantity (EKPO_WEIGHTS), the column for commodity codes will not be updated anymore.</p>\r\n<div></div>\r\n</div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D066663"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D050691)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0003020849/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003020849/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003020849/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003020849/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003020849/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003020849/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003020849/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003020849/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003020849/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "106", "To": "106", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}