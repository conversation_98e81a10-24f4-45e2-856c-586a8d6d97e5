{"Request": {"Number": "1667603", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 640, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000009901902017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001667603?language=E&token=C7D500C04AA073FBF57B9564F80A4392"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001667603", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001667603/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1667603"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.01.2013"}, "SAPComponentKey": {"_label": "Component", "value": "FI-GL-REO"}, "SAPComponentKeyText": {"_label": "Component", "value": "General Ledger Reorganization"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Accounting", "value": "FI-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Reorganization", "value": "FI-GL-REO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-REO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1667603 - Archiving in reorganization"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>In the new general ledger accounting, you use the reorganization and want to archive completed reorganization plans. However, you have not found an archiving object for the reorganization in transaction SARA.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Archiving object, FAGL_REORG, FIN_GL_REORG1, FIN_GL_REORG_SEG, PSM_FM_REASSIGN</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Previously, this function did not exist.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions or import the attached Support Package. If you implement the correction instructions, note the manual steps.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I802955)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I803939)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001667603/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001667603/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001667603/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001667603/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001667603/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001667603/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001667603/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001667603/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001667603/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1627018", "RefComponent": "FI-GL-REO", "RefTitle": "Composite SAP Note for segment reorganization", "RefUrl": "/notes/1627018"}, {"RefNumber": "1471153", "RefComponent": "FI-GL-REO", "RefTitle": "Composite note for profit center and FM reorganization", "RefUrl": "/notes/1471153"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1471153", "RefComponent": "FI-GL-REO", "RefTitle": "Composite note for profit center and FM reorganization", "RefUrl": "/notes/1471153 "}, {"RefNumber": "1627018", "RefComponent": "FI-GL-REO", "RefTitle": "Composite SAP Note for segment reorganization", "RefUrl": "/notes/1627018 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "606", "To": "606", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 605", "SupportPackage": "SAPKH60509", "URL": "/supportpackage/SAPKH60509"}, {"SoftwareComponentVersion": "SAP_APPL 606", "SupportPackage": "SAPKH60604", "URL": "/supportpackage/SAPKH60604"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 2, "URL": "/corrins/0001667603/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_APPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Application&nbsp;&nbsp; |<br/>| Release 605&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKH60508&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 606&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKH60601 - SAPKH60603&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>All new development objects are assigned to the package FAGL_REORGANIZATION in Enhancement Package 5 and to the package  FAGL_REORGANIZATION_FW in Enhancement Package 6, unless specifically stated otherwise.</P> <OL>1. Create the data element FAGL_R_ARCH_RES_TIME:</OL> <P>Call transaction SE11 and create the data type FAGL_R_ARCH_RES_TIME. The following additional settings are to be made:</P> <UL><LI>Short description: Residence Time Before Archiving</LI></UL> <UL><LI>Domain: NUMC04</LI></UL> <UL><LI>Field Label Short: Res. Time</LI></UL> <UL><LI>Other field labels: Residence Time</LI></UL> <OL>2. Create the table FAGL_R_ARCH_DEF:</OL> <P>Call transaction SE11 and create the table FAGL_R_ARCH_DEF. The following additional settings are to be made:</P> <UL><LI>Description: Definition of Residence Time for Archiving</LI></UL> <UL><LI>Delivery Class: C</LI></UL> <UL><LI>Table View Maint.:&nbsp;&nbsp;Display/Maintenance Allowed with Restrictions</LI></UL> <UL><LI>Fields:</LI></UL> <UL><UL><LI>Field, Key, Initial, Data element, Search help</LI></UL></UL> <UL><UL><LI>MANDT, 'X', 'X', MANDT, H_T000</LI></UL></UL> <UL><UL><LI>PLAN_TYPE, 'X', 'X', FAGL_R_PLAN_TYPE, FAGL_R_PLTY_ACTIVE</LI></UL></UL> <UL><UL><LI>RESIDENCE_TIME, ' ', ' ', FAGL_R_ARCH_RES_TIME</LI></UL></UL> <UL><LI>Technical Settings: Data class = APPL2, Size category=0, Buffering not allowed</LI></UL> <UL><LI>Enhancement Category: Can be enhanced (character-type)</LI></UL> <OL>3. Create the view for the table FAGL_R_ARCH_DEF:</OL> <P>Call transaction SE11 and create the view V_FAGL_R_ARCHDEF. The following additional settings are to be made:</P> <UL><LI>Description: Definition of Residence Times</LI></UL> <UL><LI>Access: Read, change, delete and insert.</LI></UL> <UL><LI>Display/Maintenance Allowed</LI></UL> <OL>4. Create the function group FAGL_R_ARCH_CUST:</OL> <P>Call transaction SE37 and choose \"Goto -&gt; Function Groups -&gt; Create  Group\" to create FAGL_R_ARCH_CUST. The text is \"Archiving: Table Maintenance (Generated)\". Activate the function group.</P> <OL>5. Create the maintenance view: V_FAGL_R_ARCHDEF</OL> <P>Call transaction SE54 and generate the objects for the view V_FAGL_R_ARCHDEF. The following additional settings are to be made:</P> <UL><LI>Authorization Group: FC14</LI></UL> <UL><LI>Function group: FAGL_R_ARCH_CUST</LI></UL> <UL><LI>Maintenance type: One step under screen 10 with \"Standard recording routine\"</LI></UL> <OL>6. Create the Customizing transaction FAGL_R_ARCHDEF:</OL> <P>Call transaction SE93 and create the transaction code FAGL_R_ARCHDEF. The settings to be made are as follows:</P> <UL><LI>Text: Definition of Residence Time</LI></UL> <UL><LI>Transaction: SM30 with \"Skip initial screen\"</LI></UL> <UL><LI>Classification: Inherit GUI attributes</LI></UL> <UL><LI>Default values: VIEWNAME=V_FAGL_R_ARCHDEF, UPDATE=X</LI></UL> <P><br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_APPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Application&nbsp;&nbsp; |<br/>| Release 605&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKH60508&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 606&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKH60601 - SAPKH60603&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>All new development objects are assigned to the package FAGL_REORGANIZATION in Enhancement Package 5 and to the package  FAGL_REORGANIZATION_FW in Enhancement Package 6, unless specifically stated otherwise.</P> <OL>1. Create the new archiving object FAGL_REORG:</OL> <P>Call transaction AOBJ and create a new entry with the object name  FAGL_REORG. Make the following settings in the relevant section:</P> <UL><LI>Initial screen:</LI></UL> <UL><UL><LI>Text: \"Archiving of Reorganization Plans\"</LI></UL></UL> <UL><UL><LI>Application Area: FI</LI></UL></UL> <UL><UL><LI>Appl. component General Ledger Reorganization</LI></UL></UL> <UL><UL><LI>Write Program: FAGL_R_ARCH_WRITE</LI></UL></UL> <UL><UL><LI>Delete Program: FAGL_R_ARCH_DELETE</LI></UL></UL> <UL><UL><LI>Reload program: FAGL_R_ARCH_RELOAD</LI></UL></UL> <UL><UL><LI>No additional indicators are set.</LI></UL></UL> <UL><LI>Structure definition:</LI></UL> <UL><UL><LI>The first entry is the segment FAGL_R_PL for a blank parent segment. All  additional segment entries are assigned to the parent segment FAGL_R_PL.  Enter the following segments in this sequence: FAGL_R_PL_T, FAGL_R_PL_ACCASS, FAGL_R_PL_ASSIGN, FAGL_R_PL_COUNT, FAGL_R_PL_DERH,  FAGL_R_PL_OBJECT, FAGL_R_PL_OBJLST, FAGL_R_PL_RES_G, FAGL_R_PL_RES_S,  FAGL_R_APAR, FAGL_R_APAR_VAL, FAGL_R_BLNCE, FAGL_R_BLNCE_VAL, FAGL_R_JBDISPTCH, FAGL_R_JBLAST, FAGL_R_JBOBJTY, FAGL_R_JBPARTN,  FAGL_R_JBPROCEED, FAGL_R_MAP_CUST, FAGL_R_MAP_SAP, FAGL_R_OI_TRACK0, FAGL_R_OI_TRACK1, FAGL_R_PER_TRACK</LI></UL></UL> <UL><LI>Do not make an entry in \"Tables from Which you Only Delete Entries\".</LI></UL> <UL><LI>Do not make an entry in \"Maintain network graphic\".</LI></UL> <UL><LI>Customizing Settings:</LI></UL> <UL><UL><LI>Logical File Name =&nbsp;&nbsp;ARCHIVE_DATA_FILE, Maximum Size in MB =100, Maximum  Number of Data Objects = 0, Commit Counter=10, Relevant variants for test and production run; Start Automatically</LI></UL></UL> <UL><LI>Do not make entries in \"Archiving Classes Used\".</LI></UL> <UL><LI>Use the reload program as a read program.</LI></UL> <UL><LI>Customizing transaction: FAGL_R_ARCHDEF</LI></UL> <UL><LI>Do not make an entry in \"Info Tables for Archive Files\".</LI></UL> <UL><LI>Do not make an entry in \"Exit routine assignment in generation\".</LI></UL> <OL>2. Create the following new message texts:</OL> <P>In the message class FAGL_REORGANIZATION, new messages are created in the block from 670 to 687.<br/>670 === Archiving ====<br/>671 &amp;1% of the objects were processed (&amp;2 of &amp;3)<br/>672 Object was archived completely<br/>673 Problems while archiving the object<br/>674 Status of the object does not allow archiving<br/>675 Archiving is not permitted during the residence time<br/>676 Object is incomplete<br/>677 Object is completely deleted<br/>678 Problems while deleting the object<br/>679 Object was reloaded successfully<br/>680 Problems while reloading the object<br/>681 Object already exists; reload is not possible<br/>682 Reload of the object was skipped<br/>683 A residence time of less than 1 day is not permitted<br/>684 It is not recommended to use a residence time of less than &amp;1 days<br/>685 Only possible to archive reorganization plans that have been closed<br/>686 Reorganization plan type &amp;1 is incorrect<br/>687 Define a residence time in Customizing</P> <OL>3. Create the enhancement exit for the archiving object FI_DOCUMNT.</OL> <P>Call transaction SE19 and implement the following settings in the relevant enhancement spot:</P> <UL><LI>Enhancement Spot ARC_ADD_TABLE</LI></UL> <UL><UL><LI>Enhancement Implementation: FAGL_R_ARCH_FIDOC_REG</LI></UL></UL> <UL><UL><LI>Text: Reorganization: Enhance FI Beleg Archiving - Registration</LI></UL></UL> <UL><UL><LI>BAdI implementation: FAGL_R_ARCH_FIDOC_REG</LI></UL></UL> <UL><UL><LI>Class: CL_FAGL_R_ARCH_FIDOC with the text: Reorg: Enhance FI Document Archiving</LI></UL></UL> <UL><LI>Enhancement Spot ARC_FI_DOCUMNT</LI></UL> <UL><UL><LI>Enhancement Implementation: FAGL_R_ARCH_FIDOC</LI></UL></UL> <UL><UL><LI>Text: Reorganization: Enhance FI Document Archiving</LI></UL></UL> <UL><UL><LI>BAdI implementation: FAGL_R_ARCH_FIDOC</LI></UL></UL> <UL><UL><LI>Class: CL_FAGL_R_ARCH_FIDOC</LI></UL></UL> <P><br/><br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 2, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "1667603 ", "URL": "/notes/1667603 ", "Title": "Archiving in reorganization", "Component": "FI-GL-REO"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}