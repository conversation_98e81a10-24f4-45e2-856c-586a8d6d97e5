{"Request": {"Number": "1637148", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 506, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000009720012017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001637148?language=E&token=8D42CF165DDFF9E8FB64C76EEE920B70"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001637148", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001637148/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1637148"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 16}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "30.07.2014"}, "SAPComponentKey": {"_label": "Component", "value": "BW-PLA-IP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Integrated Planning"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Planning", "value": "BW-PLA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-PLA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Integrated Planning", "value": "BW-PLA-IP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-PLA-IP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1637148 - BW on HANA: Activation of Planning Application Kit"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>A program correction is required to activate SAP HANA-based deep in-memory integration and also the Planning Application Kit (PAK) for SAP NetWeaver BW 7.30 or 7.31 from Support Package 05 to Support Package 10 as part of BW planning. For SAP NetWeaver BW 7.4, PAK is supported only as of Support Package 05.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>PAK, Business Planning, HANA, PE, Planning Engine, Performance, HANA optimized planning</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>SAP NetWeaver BW 7.30<br/><br/>Import Support Package 10 for SAP NetWeaver BW 7.30 (SAPKW73010) into your BW system. The Support Package is available when SAP Note 1810084 &quot;SAPBWNews NW 7.30 BW ABAP SP10&quot;, which describes this Support Package in more detail, is released for customers.<br/><br/>&#x00A0;&#x00A0;&#x00A0; SAP NetWeaver BW 7.31 (SAP NW BW 7.0 Enhancement Package 3)<br/><br/>Import Support Package 09 for SAP NetWeaver BW 7.31 (SAPKW73109) into your BW system. The Support Package is available when SAP Note 1847231 &quot;SAPBWNews NW 7.31 BW ABAP SP9&quot;, which describes this Support Package in more detail, is released for customers.<br/><br/>Revision&#x00A0;67 Hana und h&#x00F6;her</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ul>\r\n<li>SAP NetWeaver BW 7.30</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">Import Support Package 11 for SAP NetWeaver BW 7.30 (SAPKW73011) into your BW system. The Support Package is available when <strong>SAP Note 1878293</strong> &quot;SAPBWNews NW BW 7.30 ABAP SP11&quot;, which describes this Support Package in more detail, is released for customers.</p>\r\n<ul>\r\n<li>SAP NetWeaver BW 7.31</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">Import Support Package 11 for SAP NetWeaver BW 7.31 (SAPKW73111) into your BW system. The Support Package is available when <strong>SAP Note 1914639</strong> &quot;SAPBWNews NW BW 7.31/7.03 ABAP SP11&quot;, which describes this Support Package in more detail, is released for customers.</p>\r\n<ul>\r\n<li>SAP NetWeaver BW 7.4</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">Import Support Package 5 or higher for SAP NetWeaver BW 7.4 (SAPKW74005) into your BW system.</p>\r\n<p>In urgent cases, you can implement the correction instructions as an advance correction. For lower Support Package levels, you can only implement the correction manually because, afterwards, you should implement the SAP Notes listed in SAP Note 1846493. However, to minimize the manual activities and possible sources of error associated with implementing the SAP Notes, we strongly recommend that you import at least Support Package 10 .</p>\r\n<p>To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note contains the words &quot;Preliminary version&quot;.</p>\r\n<p>You must first read SAP Note 1668882, which provides information about transaction SNOTE.</p>\r\n<p><strong>&#x00A0;</strong></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D027341)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D029075)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001637148/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001637148/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001637148/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001637148/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001637148/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001637148/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001637148/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001637148/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001637148/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1875460", "RefComponent": "BC-UPG-ADDON", "RefTitle": "ACCTPMC 100: Installation and Support Packages", "RefUrl": "/notes/1875460"}, {"RefNumber": "1861395", "RefComponent": "HAN-DB-ENG-PLE", "RefTitle": "Revision 60 Enhancements for Planning Functions", "RefUrl": "/notes/1861395"}, {"RefNumber": "1852756", "RefComponent": "HAN-DB-ENG-PLE", "RefTitle": "Revision 54 Enhancements for Planning Functions", "RefUrl": "/notes/1852756"}, {"RefNumber": "1846493", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "Important SAP Notes for SAP BW 7.3x, powered by HANA", "RefUrl": "/notes/1846493"}, {"RefNumber": "1820266", "RefComponent": "HAN-DB-ENG-PLE", "RefTitle": "Revision 50 Enhancements for Planning Functions", "RefUrl": "/notes/1820266"}, {"RefNumber": "1799168", "RefComponent": "BW-PLA-IP", "RefTitle": "Release DSO Plannig", "RefUrl": "/notes/1799168"}, {"RefNumber": "1774550", "RefComponent": "BW-PLA-IP", "RefTitle": "BW-IP (PAK): Lifecycle management of PE sessions in HANA", "RefUrl": "/notes/1774550"}, {"RefNumber": "1769188", "RefComponent": "HAN-DB-ENG-PLE", "RefTitle": "Revision 38 Enhancements for Planning Functions 2", "RefUrl": "/notes/1769188"}, {"RefNumber": "1735590", "RefComponent": "BW-PLA-IP", "RefTitle": "Q&A on DSO Planning", "RefUrl": "/notes/1735590"}, {"RefNumber": "1637199", "RefComponent": "BW-PLA-IP", "RefTitle": "Using the planning applications KIT (PAK)", "RefUrl": "/notes/1637199"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1694205", "RefComponent": "BW-PLA-IP", "RefTitle": "BW-IP: Preliminary clarification of Planning Application Kit (PAK) message", "RefUrl": "/notes/1694205 "}, {"RefNumber": "1637199", "RefComponent": "BW-PLA-IP", "RefTitle": "Using the planning applications KIT (PAK)", "RefUrl": "/notes/1637199 "}, {"RefNumber": "1875460", "RefComponent": "BC-UPG-ADDON", "RefTitle": "ACCTPMC 100: Installation and Support Packages", "RefUrl": "/notes/1875460 "}, {"RefNumber": "1735590", "RefComponent": "BW-PLA-IP", "RefTitle": "Q&A on DSO Planning", "RefUrl": "/notes/1735590 "}, {"RefNumber": "1799168", "RefComponent": "BW-PLA-IP", "RefTitle": "Release DSO Plannig", "RefUrl": "/notes/1799168 "}, {"RefNumber": "1769188", "RefComponent": "HAN-DB-ENG-PLE", "RefTitle": "Revision 38 Enhancements for Planning Functions 2", "RefUrl": "/notes/1769188 "}, {"RefNumber": "1774550", "RefComponent": "BW-PLA-IP", "RefTitle": "BW-IP (PAK): Lifecycle management of PE sessions in HANA", "RefUrl": "/notes/1774550 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "740", "To": "740", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW 730", "SupportPackage": "SAPKW73011", "URL": "/supportpackage/SAPKW73011"}, {"SoftwareComponentVersion": "SAP_BW 731", "SupportPackage": "SAPKW73111", "URL": "/supportpackage/SAPKW73111"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BW", "NumberOfCorrin": 1, "URL": "/corrins/0001637148/30"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 6, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1628435 ", "URL": "/notes/1628435 ", "Title": "BW-IP performance: In-memory planning for BW on SAP HANA (1)", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1633212 ", "URL": "/notes/1633212 ", "Title": "BW-IP performance: In-memory planning for BW on SAP HANA (2)", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1638791 ", "URL": "/notes/1638791 ", "Title": "BW-IP performance: In-memory planning for BW on SAP HANA (3)", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1641403 ", "URL": "/notes/1641403 ", "Title": "BW-IP performance: In-memory planning for BW on SAP HANA (4)", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1644357 ", "URL": "/notes/1644357 ", "Title": "BW-IP performance: In-memory planning for BW on SAP HANA (5)", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1649514 ", "URL": "/notes/1649514 ", "Title": "BW-IP performance: In-memory planning for BW on SAP HANA (6)", "Component": "BW-PLA-IP"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}