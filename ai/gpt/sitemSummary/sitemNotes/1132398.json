{"Request": {"Number": "1132398", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 424, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000006740362017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001132398?language=E&token=F972770052827758B24C98777AB9B119"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001132398", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001132398/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1132398"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Correction of legal function"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.04.2008"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-LC-JP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Japan"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Real Estate Localization", "value": "RE-FX-LC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Japan", "value": "RE-FX-LC-JP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC-JP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1132398 - Additional postings with fixed unit prices"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>When running the standard Service Charge Settlement (SCS) for fixed unit prices (localization for Japan), the rental objects are not debited and the cost elements are not credited with the fixed unit price.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Additional posting, RE-FX Japan, Localization, Service Charge Settlement, fixed unit prices.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Missing posting to balance the accounts.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Additional pre-step posting before excuting the SCS.<br /><br />The pre-Step transaction 'Distribute Consumption Costs Based on Fixed Unit Prices' is modified to provide the additional postings.<br />For this the maintenance of the fixed unit prices is extended with the maintenance of the cost elements to which these postings should be created.<br />An additional customizing is added to define the flow types per company code and service charge key responsible for the account determinations in the additional pre-step posting.<br />This customizing makes possible to determine the cost and tax accounts.<br /><br />You have to create the following DDIC objects:<br /><br />1)<br />Data Elements:<br /><B>REXCJPCENAME</B><br /><U>Short Description</U>: Cost Element Name<br /><U>Domain</U>: CHAR40<br /><U>Field Label</U>:<br />Short&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;10&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CE Name<br />Medium&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;17&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Cost Element Name<br />Long&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;20&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Name of Cost ELement<br />Heading&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;40&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Name of Cost Element<br /><U>Documentation</U>:</p> <UL><LI>Short Text: Cost Element Name</LI></UL> <UL><LI>Definition: Specifies the name of the cost element.</LI></UL> <p><br /><B>REXCJPCEOBJTYPE</B><br /><U>Short Description</U>: Cost Element Object Type<br /><U>Domain</U>: J_OBART<br /><U>Field Label</U>:<br />Short&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;10&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CE Obj.Typ<br />Medium&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;20&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Cost Element Obj.Typ<br />Long&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;27&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Object Type of Cost Element<br />Heading&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;27&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Object Type of Cost Element<br /><U>Documentation</U>:</p> <UL><LI>Short Text: Cost Element Object Type</LI></UL> <UL><LI>Definition: Determines the type of the cost element.</LI></UL> <UL><LI>Use: It can have the following values:</LI></UL> <UL><UL><LI>Cost Center</LI></UL></UL> <UL><UL><LI>WBS Element</LI></UL></UL> <UL><UL><LI>Order</LI></UL></UL> <p><br /><br /><B>REXCJPCOSTELEMENTID</B><br /><U>Short Description</U>: Cost Element ID<br /><U>Domain</U>: CHAR24<br /><U>Field Label</U>:<br />Short&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;10&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CE ID<br />Medium&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;15&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Cost Element ID<br />Long&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;20&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ID of Cost Element<br />Heading&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 24&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ID of Cost Element<br /><U>Documentation</U>:</p> <UL><LI>Short Text: Cost Element ID</LI></UL> <UL><LI>Definition: Specifies the unique ID of the possible cost&#x00A0;&#x00A0;elements.</LI></UL> <UL><LI>Use: The cost elements are as follows:</LI></UL> <UL><UL><LI>Cost Center</LI></UL></UL> <UL><UL><LI>WBS Element</LI></UL></UL> <UL><UL><LI>Order</LI></UL></UL> <UL><LI>Example:US01\\0000040300 is a cost element defined in controlling area US01 and cost center 0000040300.</LI></UL> <p><br />2)<br /><U>New DDIC Table:</U><br />Transparent Table <B>TIVXCJPFLWTYP</B><br />Short Description: Flow Type Defined for Service Charge Keys<br /><br />Field Key Initial Values Data Element Check Table<br />MANDT X X  MANDT<br />BUKRS X X  BUKRS<br />SNKSL X X  RESCSCKEY<br />FLOWTYPE X X  RECDFLOWTYPE TIVCDFLOWTYPE<br /><br />Technical Settings:<br /> - Data Class: Appl2<br /> - Siye Category: 0<br /> - Buffering not allowed<br /><br />3)<br /><U><B>New Views:</B></U><br />Help view:<br /><B>H_REXCJPCEOBJTYP</B><br /><U>Short Description</U>: Relevant Object Types<br /><U>Table/Join conditions</U><br />Tables:<br /> - TIVCAOBJTYPE<br /> - TBO00<br /> - TBO01<br />Join Conditions:<br /> TBO00 OBART = TIVCAOBJTYPE OBJTYPE<br /> TBO00 OBART = TBO01 OBART<br /><br /><U>View Fields:</U><br />View Field Table  Field Key Data element Mod<br />OBJTYPE TIVCAOBJTYPE OBJTYPE X RECAOBJTYPE<br />XOBJTYPE TBO01  OBART_LD  RECAXOBJTYPE X<br />XLOBJTYPE TBO01  TXT20  RECAXLOBJTYPE X<br /><br /><U>Selection Conditions:</U><br />TIVCAOBJTYPE OBSOLETE EQ ' ' AND<br />TIVCAOBJTYPE OBJTYPE EQ 'KS' OR<br />TIVCAOBJTYPE OBJTYPE EQ 'OR' OR<br />TIVCAOBJTYPE OBJTYPE EQ 'PR' OR<br />TIVCAOBJTYPE OBJTYPE EQ 'PD'<br /><br /><U>Maintain status</U>: read only<br /><br />Maintenance view:<br /><B>V_TIVXCJPFLWTYP</B><br /><U>Short Description</U>: TIVXCJPFLWTYP<br /><U>Table/Join conditions</U><br />Tables:<br /> - TIVXCJPFLWTYP<br /><br /><U>View Fields:</U><br />View Field Table  Field Key Data element Mod<br />MANDT TIVXCJPFLWTYP MANDT X MANDT<br />BUKRS TIVXCJPFLWTYP BUKRS X BUKRS<br />SNKSL TIVXCJPFLWTYP SNKSL X RESCSCKEY<br />FLOWTYPE TIVXCJPFLWTYP FLOWTYPE X RECDFLOWTYPE<br /><br /><U>Maintain status</U>: read, change, delete, insert<br /> - Delivery class: C<br /> - Display/Maintenance allowed<br /><br />4)<br />Start Table Maintenance Generator for view <B>V_TIVXCJPFLWTYP</B><br />Authorization Group&#x00A0;&#x00A0;&amp;NC&amp;<br />Function group&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; TIVXCJPFLWTYP<br />Package&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GLO_REFX_JP<br /><br /><U>Maintenance Screens</U><br />Maintenance type: one step<br />Maint. Screen No.&#x00A0;&#x00A0;&#x00A0;&#x00A0;Overview screen&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Single screen&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0<br /><br />5) Create the following Search Helps:<br />Elementary search help:<br /><B>REXCJPCEOBJTYPE_ELEM</B><br /><U>Short description</U> :&#x00A0;&#x00A0; Relevant Object Types<br /><U>Selection method</U>: H_REXCJPCEOBJTYP<br /><U>Srch. help exit</U> :F4UT_OPTIMIZE_COLWIDTH<br /><br />Parameter Imp Exp Data Element<br />OBJTYPE X X RECAOBJTYPE<br />XOBJTYPE  X RECAXOBJTYPE<br />XLOBJTYPE X RECAXLOBJTYPE<br /><br />Collective Search Helps:<br /><B>REXCJPCEOBJTYPE</B><br /><U>Short description</U> :&#x00A0;&#x00A0; Object Type<br /><U>Srch. help exit</U> :F4UT_OBJTYPE_DISPLAY<br /><br />Parameter Imp Exp Data Element<br />OBJTYPE  X RECAOBJTYPE<br />XOBJTYPE X RECAXOBJTYPE<br /><br />Included search helps: with proposed parameter assignment<br /> - REXCJPOBJTYPE_ELEM<br /><br /><B>REXCJPCEOBJNR</B><br /><U>Short description</U> :&#x00A0;&#x00A0; Object Number for Cost Elements (CC/WBS/Order)<br /><U>Srch. help exit</U> :F4UT_CEOBJ_DISPLAY<br /><br />Parameter Imp Exp Data Element<br />FROMDATE X DATAB<br />TODATE X DATBI<br />KOKRS  X KOKRS<br />KOSTL  X KOSTL<br />POSID  X PS_POSID<br />PSPID  X PS_PSPID<br />AUFNR  X AUFNR<br />OBJNR  X J_OBJNR<br />NAME  X CHAR40<br />OBJTYPE  X REXCJPOBJTYPE<br />XOBJTYPE  X RECAXOBJTYPE<br />CEID  X REXCJPCOSTELEMENTID<br /><br />Included search helps: with proposed parameter assignment<br /> - KOST<br /> - COBL_EX_PRPM<br /> - COBL_EX_ORDE<br /><br />6)<br />DDIC: Table and Structure modifications<br /><B>REXCJPFIXEDUNITPRICE_TAB:</B><br />New elements (marked with *) are added to the structure:<br />Component Type   Search Help<br />...<br />TAXCALC REXCJPTAXCALC<br />*CEOBJTYPE REXCJPCEOBJTYPE<br />*CEID REXCJPCOSTELEMENTID REXCJPCEOBJNR<br />*CENAME REXCJPCENAME<br />*CEOBJNR J_OBJNR<br />XOBJTYPE RECAXOBJTYPE  REXCJPOBJTYPE<br />*XCEOBJTYPE RECAXOBJTYPE  REXCJPCEOBJTYPE<br />...<br /><br /><U>Search helps for the fields</U>:<br />Component CEID:<br />Search Help param Table Name   Field Name<br />AUFNR  <br />CEID  REXCJPFIXEDUNITPRICE_TAB CEID<br />FROMDATE  REXCJPFIXEDUNITPRICE_TAB VALIDFROM<br />KOKRS  <br />KOSTL  <br />NAME  REXCJPFIXEDUNITPRICE_TAB CENAME<br />OBJNR  REXCJPFIXEDUNITPRICE_TAB CEOBJNR<br />OBJTYPE  REXCJPFIXEDUNITPRICE_TAB CEOBJTYPE<br />POSID  <br />PSPID  <br />TODATE  <br />XOBJTYPE  REXCJPFIXEDUNITPRICE_TAB XCEOBJTYPE<br /><br />Component XCEOBJTYPE:<br />Search Help param Table Name   Field Name<br />OBJTYPE  REXCJPFIXEDUNITPRICE_TAB CEOBJTYPE<br />XOBJTYPE  REXCJPFIXEDUNITPRICE_TAB XCEOBJTYPE<br /><br /><B>REXCJPFIXEDUNITPRICEDYNP</B>:<br /><U>Search helps for the fields</U>:<br />Component CEID: remove check from the \"Inherited from the include\" checkbox<br />Search Help param Table Name   Field Name<br />AUFNR  <br />CEID  REXCJPFIXEDUNITPRICEDYNP CEID<br />FROMDATE  REXCJPFIXEDUNITPRICEDYNP VALIDFROM<br />KOKRS  <br />KOSTL  <br />NAME  REXCJPFIXEDUNITPRICEDYNP CENAME<br />OBJNR  REXCJPFIXEDUNITPRICEDYNP CEOBJNR<br />OBJTYPE  REXCJPFIXEDUNITPRICEDYNP CEOBJTYPE<br />POSID  <br />PSPID  <br />TODATE  REXCJPFIXEDUNITPRICEDYNP VALIDTO<br />XOBJTYPE  REXCJPFIXEDUNITPRICEDYNP XCEOBJTYPE<br /><br />Component XCEOBJTYPE: check the \"Inherited from the include\" checkbox<br />Search Help param Table Name   Field Name<br />OBJTYPE  REXCJPFIXEDUNITPRICEDYNP CEOBJTYPE<br />XOBJTYPE  REXCJPFIXEDUNITPRICEDYNP XCEOBJTYPE<br /><br /><U><B>REXCJPAPPORTIONMENT</B></U>:<br />Add new component:<br />CEOBJNR type J_OBJNR<br /><br /><B>REXCJPREOBJCONS</B>:<br />Add new component:<br />CEOBJNR type J_OBJNR<br /><br /><B>REXCJPSCCOSTSEXT</B>:<br />Add new component:<br />PROCESSID type RECAPROCESSID<br /><br /><B>VIXCJPFXDUNPRICE</B>:<br />Modify the search help assignments<br />Search Help Assgn. to Fields<br /><br />Field Name&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; CEID<br />Search Help Name&#x00A0;&#x00A0; REXCJPCEOBJNR<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Src.hlp.pr Assignment table&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Assgn.Fld<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;CEID&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; VIXCJPFXDUNPRICE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; CEID<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; FROMDATE&#x00A0;&#x00A0; VIXCJPFXDUNPRICE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; VALIDFROM<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;NAME&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; VIXCJPFXDUNPRICE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; CENAME<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;OBJNR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;VIXCJPFXDUNPRICE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; CEOBJNR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;OBJTYPE&#x00A0;&#x00A0;&#x00A0;&#x00A0;VIXCJPFXDUNPRICE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; CEOBJTYPE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; XOBJTYPE&#x00A0;&#x00A0; VIXCJPFXDUNPRICE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; XCEOBJTYPE<br /><br />Field Name&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; CENAME<br />Search Help Name&#x00A0;&#x00A0; REXCJPCEOBJNR<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Src.hlp.pr Assignment table&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Assgn.Fld<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; FROMDATE&#x00A0;&#x00A0; VIXCJPFXDUNPRICE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; VALIDFROM<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;NAME&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; VIXCJPFXDUNPRICE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; CENAME<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;OBJNR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;VIXCJPFXDUNPRICE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; OBJNR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;TODATE&#x00A0;&#x00A0;&#x00A0;&#x00A0; VIXCJPFXDUNPRICE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; VALIDTO<br /><br />Field Name&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; CEOBJNR<br />Search Help Name&#x00A0;&#x00A0; REXCJPCEOBJNR<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Src.hlp.pr Assignment table&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Assgn.Fld<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; FROMDATE&#x00A0;&#x00A0; VIXCJPFXDUNPRICE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; VALIDFROM<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;NAME&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; VIXCJPFXDUNPRICE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; CENAME<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;OBJNR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;VIXCJPFXDUNPRICE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; CEOBJNR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;TODATE&#x00A0;&#x00A0;&#x00A0;&#x00A0; VIXCJPFXDUNPRICE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; VALIDTO<br /><br />Field Name&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; XCEOBJTYPE<br />Search Help Name&#x00A0;&#x00A0; REXCJPCEOBJTYPE<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Src.hlp.pr Assignment table&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Assgn.Fld<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;OBJTYPE&#x00A0;&#x00A0;&#x00A0;&#x00A0;VIXCJPFXDUNPRICE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; CEOBJTYPE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; XOBJTYPE&#x00A0;&#x00A0; VIXCJPFXDUNPRICE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; XCEOBJTYPE<br /><br /><B>VIXCJPSCCOSTSEXT</B>:<br />Add new component:<br />PROCESSID type RECAPROCESSID<br /><br />7) Create the following messages in message class REXCJP<br />414&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Additional postings have been already executed<br />415&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Flow type can not be determined for company &amp; and service charge key &amp;<br />416&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Different cost element can not be defined for same key fields (&amp; &amp;&lt;&gt;&amp; &amp;)<br />417&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Cost element can not be determined for Rental Object &amp;<br />418&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Cost element type can only be 'CTR', 'WBS' and 'ORD'<br />419&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Cost element is not specified<br />420&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Specified cost element (&amp; &amp;) does not exist<br /><br />Add the following long texts to the specific messages:<br />415:<br />Diagnosis<br />Flow type could not&#x00A0;&#x00A0;be determined for company code&#x00A0;&#x00A0;and service charge key .<br /><br />System Response<br />Additional posting documents will not be created.<br /><br />Procedure<br />Define flow type for the given company code and service charge key in table TIVXCJPFLWTYP in customizing.<br /><br />416:<br />Diagnosis<br />The defined cost element (Cost Center, WBS Element, Order) differs from the other cost element defined for the same keys. This field needs to contain the same entry as the other entries with the same key.<br /><br />Procedure<br />Modify the cost element in the currently processed line. If you want to modify the cost element in all entries with the same key field, then you have to modify them at once.<br /><br />417:<br />Diagnosis<br />Cost element (Cost Center, WBS Element or Order) can not be determined for Rental Object&#x00A0;&#x00A0;from the customization of fixed unit prices.<br /><br />System Response<br />Posting can not be executed without the cost element. Posting procedure will stop.<br /><br />Procedure<br />Check the cost element configuration for the relevant rental object or higher level participation group, business entity.<br /><br />8)<br />In report RFREXCJPSCSCONSCOSTDISTR<br />Add the following button to the RESULT_STATUS GUI status:<br /> - Function key F8: BTN_POST<br />  Function Text&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Post Additional Doc.<br />  Icon Name&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ICON_SYSTEM_SAVE<br />  Icon Text&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Post Additional Documents<br />  Info. Text&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Post Additional Documents<br />  Fastpath<br /> - Application toolbar: Items 1 - 7: Add BTN_POST here<br /><br />Add the following Text Symbol:<br /> POM - Posting Messages<br /><br />9)<br />In transaction SIMGH add the following IMG activity to the IMG structure RE-FX Localization for Japan:<br />- \"RE-FX Localization for Japan\"<br /> - \"Service Charge Settlement based on Fixed Unit Prices\"<br /> - on the same level as \"Define Rounding Type...\" add the following activity:<br />ID <B>V_TIVXCJPFLWTYP</B><br />Name&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Assign Flow Types to Service Charge Keys<br /><br /><U>Document tab page:</U><br />Document Class&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; SIMG<br />Document Name&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;V_TIVXCJPFLWTYP<br /> <br />Document:<br /><U>Use</U><br /><br />In this IMG activity, you assign the flow types to service charge keys with the relevant company code. Flow types are used to determine cost and text account numbers for additional postings in the <B>Pre-Step</B> of service charge settlement based on fixed unit prices.<br /><B>(Note: define this as a link to executable report Pre-Step</B> <B>)</B><br /><U>Requirements</U><br /><br />You have defined</p> <UL><LI>Service charge keys in the IMG activity <B>Define Service Charge Keys (Note: define this as a link to IMG ActivityDefine Service Charge Keys</B> <B>)</B></LI></UL> <UL><LI>Flow types in the IMG activity <B>Define Flow Types(Note: define this as a link to IMG Activity Define Flow Types</B> <B>)</B>  </LI></UL> <p><br /><U>Attribute tab page:</U><br />ID&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;V_TIVXCJPFLWTYP<br />Name&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Assign Flow Types to Service Charge Keys<br /><br /><br />ASAP Roadmap ID&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 207<br />Mandatory/Optional 1<br />Critical/Non-Critical 1<br /><br />Country-Dependency: Only valid for Specified Countries:<br />  Add JP to the table<br /><br />Assigned Application Components:<br /> Add ALN0000031 to the table<br /><br /><U>Maint. Objects tab page:</U><br />ID&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;V_TIVXCJPFLWTYP<br />Name&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Assign Flow Types to Service Charge Keys<br />Cust. Object Obj. Desc.     Ty. Tran<br />V_TIVXCJPFLWTYP Flow Type Defined for Service Charge Keys V SM30<br /><br /><br />Activate everything and that's all folks!<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "I033769"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON> (I033780)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001132398/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001132398/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001132398/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001132398/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001132398/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001132398/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001132398/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001132398/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001132398/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "928175", "RefComponent": "RE-FX-LC-JP", "RefTitle": "RE-FX Country Version for Japan", "RefUrl": "/notes/928175"}, {"RefNumber": "1302595", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Costdistribution - Error REXCJP414 during Addit. Posting", "RefUrl": "/notes/1302595"}, {"RefNumber": "1156145", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Documentation for additional SCS postings (Japan):correction", "RefUrl": "/notes/1156145"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1302595", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Costdistribution - Error REXCJP414 during Addit. Posting", "RefUrl": "/notes/1302595 "}, {"RefNumber": "1156145", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Documentation for additional SCS postings (Japan):correction", "RefUrl": "/notes/1156145 "}, {"RefNumber": "928175", "RefComponent": "RE-FX-LC-JP", "RefTitle": "RE-FX Country Version for Japan", "RefUrl": "/notes/928175 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-APPL 600", "SupportPackage": "SAPKGPAD12", "URL": "/supportpackage/SAPKGPAD12"}, {"SoftwareComponentVersion": "EA-APPL 602", "SupportPackage": "SAPK-60202INEAAPPL", "URL": "/supportpackage/SAPK-60202INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 603", "SupportPackage": "SAPK-60301INEAAPPL", "URL": "/supportpackage/SAPK-60301INEAAPPL"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "EA-APPL", "NumberOfCorrin": 6, "URL": "/corrins/0001132398/229"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 6, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 2, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1035805 ", "URL": "/notes/1035805 ", "Title": "SCS: Error correction due to Code Inspection", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1132398 ", "URL": "/notes/1132398 ", "Title": "Additional postings with fixed unit prices", "Component": "RE-FX-LC-JP"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}