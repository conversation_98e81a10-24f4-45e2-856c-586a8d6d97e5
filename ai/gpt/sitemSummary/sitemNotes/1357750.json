{"Request": {"Number": "1357750", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 387, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016820962017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001357750?language=E&token=6798684FF67BA6911B50D2B18BB82879"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001357750", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001357750/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1357750"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "30.07.2009"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-MI"}, "SAPComponentKeyText": {"_label": "Component", "value": "Migration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Migration", "value": "RE-FX-MI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-MI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1357750 - Migration: Limited partner assignments"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>After the migration of Real Estate general contracts (known as \"tenant rental agreements\" within migration), the processing of these contracts may result in the error message \"Postings [until/from 99.99.9999]: assignment of business partner XXXXXXXXXX to contract is limited in time\".</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>REMICL, VICN01, VZZKOPO, VZGPO, VITMPY, VIBPOBJREL, RETMFI003, 6D 419</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>In the Real Estate general contract, you can assign additional business partners with customer or vendor role types in addition to the required main contractual partner. Unlike the main contractual partner, the assignment of these business partners to the contract can be limited in time.<br />If the role type of the main contractual partner is not entered in the default settings for the contract type, a main contractual partner is not required in contracts for this contract type.<br />A prerequisite for using a business partner in a condition is that the business partner is assigned to the contract independent of time-related availability. In certain circumstances (condition start is before assignment start of partner to contract, condition end for condition limited in time is after assignment end of partner to contract), the system issues the warning \"The condition validity (AAAA) is too long for partner XXXXXXXXXX\", which is not relevant for the activation of the contract and the generation of the cash flow.<br />In the contracts of flexible Real Estate Management (\"external contract\" contract category), the business partner of the condition is no longer assigned directly but using the posting term. To ensure that no errors occur during the data retrieval for the relevant posting system, the following conditions must be met when the contract is activated:</p> <UL><LI>The posting term is not limited in time. A limitation is made indirectly using the contract term data or the validity periods of the assigned conditions.</LI></UL> <UL><LI>Time-based changes to attributes of the posting term (in this case, changing the customer/vendor business partner to be used) are made by generating time slots.</LI></UL> <UL><LI>The assigned business partner must be assigned to the contract in the validity interval of the contract time slot.</LI></UL><UL><LI>A business partner must be assigned to each time slot of a posting term.</LI></UL> <p><br />These conditions are not dependent on the validity period of the condition(s) assigned to the term.<br />Due to this different organizational solution in Classic RE and flexible Real Estate Management, the system may convert data incorrectly during the migration if</p> <UL><LI>the assignment of business partners to the contract is limited in time (\"Valid from\", \"Valid to\"), and</LI></UL> <UL><LI>the conditions are limited in time (\"Valid to\") and are incomplete in their time-based assignment.</LI></UL> <p></p> <b>Example:</b><br /> <p>There is a (vendor) tenant rental agreement (valid from 01.01.2008, open-ended) with the following partner assignment:</p> <UL><LI>Landlord with vendor account AAA (open-ended, as main contractual partner)</LI></UL> <UL><LI>Contract partner with vendor account BBB (01.08.2008 - 31.07.2009)</LI></UL> <UL><LI>Contract partner with vendor account CCC (01.08.2009 - 31.07.2010)</LI></UL> <p><br />The contract has a condition 9999 with the following validities and partner assignments (monthly frequency):</p> <UL><LI>01.08.2008 - 31.07.2009 with partner BBB</LI></UL> <UL><LI>01.08.2009 - 31.07.2010 with partner CCC</LI></UL> <p><br />This means that the validity periods of the conditions correspond to the validity periods of the assigned partners (in \"Classic RE\" this is not necessarily required).<br />The resulting cash flow (without consideration of due dates) begins on 01.08.2008 with the vendor belonging to partner BBB. On 01.08.2009, this changes to the vendor of partner CCC, and the cash flow ends on 31.07.2010.<br /><br />In the migration, the system generates a posting term for both condition items, which has the following validity periods (time slots) for the assigned contract partner:</p> <UL><LI>__._-.____ - 31.07.2009 partner BBB</LI></UL> <UL><LI>01.08.2009 - __.__.____ partner CCC</LI></UL> <p><br />A check of the contract returns the following error for the posting term:</p> <UL><LI>Postings [to 31.07.2009]: Assignment of business partner BBB to contract is limited in time.</LI></UL> <UL><LI>Postings [from 01.08.2009]: Assignment of business partner CCC to contract is limited in time.</LI></UL> <p><br />This error must not be suppressed using the migration tool because different customer-specific solutions are possible according to each situation.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Only customer-specific solutions are possible. Note <B>1079141</B> describes an interface that can be used within migration.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D034884"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D002072)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001357750/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001357750/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001357750/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001357750/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001357750/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001357750/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001357750/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001357750/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001357750/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "828160", "RefComponent": "RE-FX", "RefTitle": "Migration from Classic RE to RE-FX", "RefUrl": "/notes/828160"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "828160", "RefComponent": "RE-FX", "RefTitle": "Migration from Classic RE to RE-FX", "RefUrl": "/notes/828160 "}, {"RefNumber": "1370776", "RefComponent": "RE-FX-MI", "RefTitle": "Migr: Posting terms, limited partner/dummy partner", "RefUrl": "/notes/1370776 "}, {"RefNumber": "1370931", "RefComponent": "RE-FX-MI", "RefTitle": "Migr: Posting terms, limited partner/main contr. partner", "RefUrl": "/notes/1370931 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}