{"Request": {"Number": "445988", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 271, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015389172017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000445988?language=E&token=3BE5875ED32ECD4EF8A05B2374659CB9"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000445988", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000445988/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "445988"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.11.2001"}, "SAPComponentKey": {"_label": "Component", "value": "CRM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Customer Relationship Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Customer Relationship Management", "value": "CRM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "445988 - Time stamps are not converted into date + time"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><UL><LI>The time stamp fields (date and time) are displayed unconverted</LI></UL> <UL><LI>A short dump occurs in Transactions CRMM_PPR and CRMM_PPR_TOPN</LI></UL><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>TZNTSTMPS, domain.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Prerequisite:<br />You have imported R/3 Support Package 05 of WebAs 6.10 (software component SAP_BASIS) and your CRM or EBP System has a R/3 Support Package status prior to 06 (software component BBPCRM).<br />Cause of the error:<br />Domain TZNTSTMPS was changed in Basis Support Package 05 for 6.10.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><UL><LI>Import R/3 Support Package 06 for CRM or EBP or:</LI></UL> <UL><LI>Import the SAPSERV transport available for this note (you find this in the directory /specific/crm/CRM30/Note.445988, files: R046539.PIC (data file), K046539.PIC (command file)), or:</LI></UL> <UL><LI>In the following data elements, replace domain TZNTSTMPS manually with domain BCOS_TSTMP:</LI></UL> <p></p> <OL>1. BDM_ABPTIM</OL> <OL>2. CGPL_ACTUALFINISH</OL> <OL>3. CGPL_ACTUALSTART</OL> <OL>4. CGPL_CONSTRAINT_TIME</OL> <OL>5. CGPL_PLANFINISH</OL> <OL>6. CGPL_PLANSTART</OL> <OL>7. COMT_AUTH_TIMESTAMP</OL> <OL>8. COMT_IL_VALID_FROM</OL> <OL>9. COMT_IL_VALID_TO</OL> <OL>10. COMT_TIMESTAMP</OL> <OL>11. COMT_VALID_FROM</OL> <OL>12. COMT_VALID_TO</OL> <OL>13. CRMT_ACTUAL_TS_FROM</OL> <OL>14. CRMT_ACTUAL_TS_TO</OL> <OL>15. CRMT_APO_RELEASE_CONFIRMED</OL> <OL>16. CRMT_APO_RELEASE_CREATED</OL> <OL>17. CRMT_APO_RELEASE_TRANSMITTED</OL> <OL>18. CRMT_APO_VALFR</OL> <OL>19. CRMT_APO_VALTO</OL> <OL>20. CRMT_BAPIAPOSCHEDTIME</OL> <OL>21. CRMT_BAPIAPOSCHEDTIMENOW</OL> <OL>22. CRMT_BILLING_DATE</OL> <OL>23. CRMT_BILL_DATE</OL> <OL>24. CRMT_CALCULATION_DATE</OL> <OL>25. CRMT_CANC_DATE</OL> <OL>26. CRMT_CANC_RECEIVE</OL> <OL>27. CRMT_CANC_REQUEST</OL> <OL>28. CRMT_CHANGED_AT</OL> <OL>29. CRMT_CONTACT_FROM</OL> <OL>30. CRMT_CONTACT_TO</OL> <OL>31. CRMT_CREATED_AT</OL> <OL>32. CRMT_DATE_FROM</OL> <OL>33. CRMT_DATE_HORIZON</OL> <OL>34. CRMT_DATE_TIMESTAMP_FROM</OL> <OL>35. CRMT_DATE_TIMESTAMP_TO</OL> <OL>36. CRMT_DATE_TO</OL> <OL>37. CRMT_DATE_VALID_FROM</OL> <OL>38. CRMT_DATE_VALID_TO</OL> <OL>39. CRMT_DLV_GROUP_DATE</OL> <OL>40. CRMT_END_DATE</OL> <OL>41. CRMT_INVCR_DATE</OL> <OL>42. CRMT_ISA_CLOSE_TIME</OL> <OL>43. CRMT_ISA_START_TIME</OL> <OL>44. CRMT_MAN_FIRSTDUEDATE</OL> <OL>45. CRMT_MKT_CHANGED_AT</OL> <OL>46. CRMT_MKT_CREATED_AT</OL> <OL>47. CRMT_MKT_EXT_EXPORT_LAST_DATE</OL> <OL>48. CRMT_MKT_EXT_IMPORT_LAST_DATE</OL> <OL>49. CRMT_ORDER_DATE</OL> <OL>50. CRMT_PERIOD_DATE</OL> <OL>51. CRMT_PLANNED_TS_FROM</OL> <OL>52. CRMT_PLANNED_TS_TO</OL> <OL>53. CRMT_PRP_CREATE_DATE</OL> <OL>54. CRMT_PRP_LAST_CHANGE_DATE</OL> <OL>55. CRMT_PRP_TS_DATE_HIGH</OL> <OL>56. CRMT_PRP_TS_DATE_LOW</OL> <OL>57. CRMT_PRP_TS_HIGH</OL> <OL>58. CRMT_PRP_TS_LOW</OL> <OL>59. CRMT_PRP_TS_TIME_HIGH</OL> <OL>60. CRMT_PRP_TS_TIME_LOW</OL> <OL>61. CRMT_PRP_VALID_TO</OL> <OL>62. CRMT_PRP_VERIFY_DATE</OL> <OL>63. CRMT_ROUTING_GROUP_CHANGED_AT</OL> <OL>64. CRMT_ROUTING_GROUP_CREATED_AT</OL> <OL>65. CRMT_SCHEDLIN_LTIME_CONFIRMED</OL> <OL>66. CRMT_SETTL_FIMA</OL> <OL>67. CRMT_SETTL_FROM</OL> <OL>68. CRMT_SETTL_FROM_FIMA</OL> <OL>69. CRMT_SETTL_TO</OL> <OL>70. CRMT_START_DATE</OL> <OL>71. CRMT_TIMESTAMP</OL> <OL>72. CRMT_TM_TIME</OL> <OL>73. CRMT_TRANS_TIME</OL> <OL>74. CRMT_VALID_FROM</OL> <OL>75. CRMT_VALID_TO</OL> <OL>76. CRMT_VERIFY_DATE</OL> <OL>77. CS_TSTAMP</OL> <OL>78. EV_FR_TIME</OL> <OL>79. EV_TO_TIME</OL> <OL>80. IBVERSION</OL> <OL>81. IB_IBIB_UPTIM</OL> <OL>82. IB_VALFR</OL> <OL>83. IB_VALTO</OL> <OL>84. ICRTS</OL> <OL>85. IUPTS</OL> <OL>86. KMS_TMSTMP</OL> <OL>87. PLMT_RELEASED_ON</OL> <OL>88. PLMT_VALIDFROM</OL> <OL>89. PLMT_VALIDTO</OL> <OL>90. PRCT_BASE_TIME_FROM</OL> <OL>91. PRCT_BASE_TIME_TO</OL> <OL>92. PRCT_TIME_ACCESS</OL> <OL>93. PRCT_TIME_FROM</OL> <OL>94. PRCT_TIME_TO</OL> <OL>95. SMO8_LASTT</OL> <OL>96. SMO8_NEXTT</OL> <OL>97. SMO8_TSTMP</OL> <OL>98. SMWMSTARTT</OL> <OL>99. SMWMSTOPT</OL> <p><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Note: First of all, save all data elements in Transaction SE11 without activating them. After saving the last data element, activate this and choose all additional data elements proposed for activation for activation as well.) <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SRM-EBP-INB (Inbox)"}, {"Key": "Other Components", "Value": "SRM-EBP-PRC (Procurement Card)"}, {"Key": "Other Components", "Value": "SRM-EBP-REP (Reporting)"}, {"Key": "Other Components", "Value": "SRM-EBP (SRM)"}, {"Key": "Other Components", "Value": "SRM-EBP-SHP (Shopping Cart)"}, {"Key": "Other Components", "Value": "SRM-CAT (Catalogs)"}, {"Key": "Other Components", "Value": "SRM-EBP-STA (Status)"}, {"Key": "Other Components", "Value": "SRM-EBP-APM (Application Monitors)"}, {"Key": "Other Components", "Value": "SRM-EBP-BID (Bid Invitation)"}, {"Key": "Other Components", "Value": "CRM-RPL (Resource Planning for Personnel Resources)"}, {"Key": "Other Components", "Value": "SRM-EBP-QUO (Quotation, Bid)"}, {"Key": "Other Components", "Value": "SRM-EBP-CPO (Component Planning PM/CS Orders)"}, {"Key": "Other Components", "Value": "SRM-EBP-POR (Local Purchase Order)"}, {"Key": "Other Components", "Value": "SRM-EBP-INV (Invoicing)"}, {"Key": "Other Components", "Value": "SRM-EBP-CUS (Customizing)"}, {"Key": "Other Components", "Value": "SRM-EBP-WFL (Workflow)"}, {"Key": "Other Components", "Value": "SRM-EBP-CGS (Confirmation Goods/Services)"}, {"Key": "Other Components", "Value": "CRM-BTX (Business Transactions)"}, {"Key": "Other Components", "Value": "CRM-ISA (Internet Sales)"}, {"Key": "Other Components", "Value": "CRM-ISE (Internet Service)"}, {"Key": "Other Components", "Value": "CRM-CIC (Interaction Center WinClient)"}, {"Key": "Other Components", "Value": "CRM-MD-ORG (Organizational Management)"}, {"Key": "Other Components", "Value": "CRM-MD (Master Data)"}, {"Key": "Other Components", "Value": "CRM-BF (Basic Functions)"}, {"Key": "Other Components", "Value": "CRM-MD-BP (Business Partners)"}, {"Key": "Other Components", "Value": "CRM-MD-PRO (Products)"}, {"Key": "Other Components", "Value": "CRM-MD-PCT (Product Catalog)"}, {"Key": "Other Components", "Value": "AP-PRC-CON (Condition Technique)"}, {"Key": "Other Components", "Value": "CRM-MD-INB (Installed Bases)"}, {"Key": "Other Components", "Value": "CRM-MD-SDB (Solution Database)"}, {"Key": "Other Components", "Value": "AP-PRC-PR (Pricing)"}, {"Key": "Other Components", "Value": "CRM-MKT-SEG (Segmentation)"}, {"Key": "Other Components", "Value": "CRM-BF-TAX (Tax Determination)"}, {"Key": "Other Components", "Value": "CRM-BF-TM (Text Management)"}, {"Key": "Other Components", "Value": "CRM-BF-PD (Partner Processing)"}, {"Key": "Other Components", "Value": "CRM-BF-PC (Payment Cards)"}, {"Key": "Other Components", "Value": "CRM-BF-CFG (Product Configuration)"}, {"Key": "Other Components", "Value": "CRM-BTX-BF-AL (Application Log)"}, {"Key": "Other Components", "Value": "CRM-BTX-BF-ATP (Availability Check)"}, {"Key": "Other Components", "Value": "CRM-BF-IIA (Interactive Intelligent Agent)"}, {"Key": "Other Components", "Value": "CRM-BTX-ACT (Activity Management)"}, {"Key": "Other Components", "Value": "CRM-BTX-OPP (Opportunities)"}, {"Key": "Other Components", "Value": "CRM-BTX-SLO (Sales Transaction)"}, {"Key": "Other Components", "Value": "CRM-BTX-SVO (Service Order)"}, {"Key": "Other Components", "Value": "SRM-EBP-MOB (Mobile Procurement)"}, {"Key": "Other Components", "Value": "CRM-BTX-BF (Basic Functions for Business Transactions)"}, {"Key": "Other Components", "Value": "CRM-BTX-BF-CU (Customizing for Business Transactions)"}, {"Key": "Other Components", "Value": "CRM-BTX-BF-CFG (Product Configuration)"}, {"Key": "Other Components", "Value": "CRM-BTX-ANA (Operative Reporting and BW Analyses)"}, {"Key": "Other Components", "Value": "CRM-BF-AR (Archiving)"}, {"Key": "Other Components", "Value": "CRM-BTX-BF-BA (BAPIs for Business Transactions)"}, {"Key": "Other Components", "Value": "CRM-BTX-BF-IF (R/3 Interface for Business Transactions)"}, {"Key": "Other Components", "Value": "CRM-ISA-BCS (Business-to-Consumer Sales)"}, {"Key": "Other Components", "Value": "CRM-ISA-BBS (Business-to-Business Sales)"}, {"Key": "Other Components", "Value": "CRM-BTX-BF-AU (Authorization Check)"}, {"Key": "Other Components", "Value": "CRM-BTX-CTR (Contracts)"}, {"Key": "Other Components", "Value": "CRM-BTX-CTR-CCO (Sales Contract)"}, {"Key": "Other Components", "Value": "CRM-MKT-MPL (Marketing Planner)"}, {"Key": "Other Components", "Value": "CRM-MKT-MPL-ST (Basic Functions)"}, {"Key": "Other Components", "Value": "CRM-MKT-MPL-ST-KFP (Key Figure Planning)"}, {"Key": "Other Components", "Value": "CRM-MKT (Marketing)"}, {"Key": "Other Components", "Value": "CRM-ISE-SRE (Service Request)"}, {"Key": "Other Components", "Value": "CRM-ISE-IIA (Interactive Intelligent Agent)"}, {"Key": "Other Components", "Value": "CRM-ISE-FAQ (Frequently Asked Questions)"}, {"Key": "Other Components", "Value": "CRM-BTX-CTR-SCO (Service Contract)"}, {"Key": "Other Components", "Value": "CRM-LAM-BTX-CTR (Leasing: Offer and Contract)"}, {"Key": "Other Components", "Value": "CRM-BTX-COM (Complaints/Returns/In-house Repairs)"}, {"Key": "Other Components", "Value": "CRM-MKT-SEG-ITP (Interface Third Party)"}, {"Key": "Other Components", "Value": "CRM-MKT-SEG-IEX (Interface Execution)"}, {"Key": "Other Components", "Value": "CRM-MKT-PRP (Product Proposals)"}, {"Key": "Other Components", "Value": "CRM-MKT-MPL-ST-DOC (Documents)"}, {"Key": "Other Components", "Value": "CRM-MKT-MPL-MSP (Microsoft Project-Interface)"}, {"Key": "Other Components", "Value": "CRM-MKT-MPL-ST-ERP (ERP-Interface)"}, {"Key": "Other Components", "Value": "CRM-MKT-SEG-ATT (Attributes)"}, {"Key": "Other Components", "Value": "CRM-MKT-SEG-PTM (Profile Templates)"}, {"Key": "Other Components", "Value": "CRM-MKT-SEG-PRF (Profiles)"}, {"Key": "Other Components", "Value": "CRM-MKT-SEG-TGR (Target Groups)"}, {"Key": "Other Components", "Value": "CRM-BF-IL (Interlinkages)"}, {"Key": "Other Components", "Value": "CRM-BF-CM (Credit Management)"}, {"Key": "Other Components", "Value": "CRM-MKT-PRP-CRS (Cross-Selling)"}, {"Key": "Other Components", "Value": "CRM-BF-CAT (Catalogs)"}, {"Key": "Other Components", "Value": "CRM-MKT-PRP-TNP (Top n Products)"}, {"Key": "Other Components", "Value": "CRM-MKT-ML (Direct Mailing)"}, {"Key": "Other Components", "Value": "CRM-RPL-SRV-WFM (Interface to Scheduling Engine)"}, {"Key": "Other Components", "Value": "CRM-BTX-SLO-QUT (Customer Inquiries / Quotations)"}, {"Key": "Other Components", "Value": "CRM-RPL-SRV-RPT (Resource Planning Tool)"}, {"Key": "Other Components", "Value": "CRM-BF-ACI (Actions/Output Determination)"}, {"Key": "Other Components", "Value": "SRM-EBP-CON (Contract Management)"}, {"Key": "Other Components", "Value": "CRM-BTX-BF-BPL (Billing Plan)"}, {"Key": "Other Components", "Value": "SRM-EBP-INT (Plug-In Interfaces)"}, {"Key": "Other Components", "Value": "CRM-BTX-BF-FIN (Use CRM-LAM instead)"}, {"Key": "Other Components", "Value": "CRM-IT-BTX (obsolete - please use CRM-BTX-PRV)"}, {"Key": "Other Components", "Value": "MP-APP-MOM (MarketSet Order Management)"}, {"Key": "Other Components", "Value": "CRM-CIC-COM-TEL (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "CRM-CIC-COM-EMA (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "CRM-CIC-BP (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "CRM-BF-DAT (Date Management)"}, {"Key": "Other Components", "Value": "CRM-CIC-BP-FUN (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "CRM-CIC-ABO (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "CRM-CIC-SCR (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "CRM-CIC-ALM (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "CRM-CIC-PRO (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "CRM-CIC-PRO-INF (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "CRM-CIC-PRO-PRO (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "CRM-CIC-CAM (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "CRM-CIC-CAM-CAL (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "CRM-CIC-BP-SEA (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "CRM-CIC-REP-LOG (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "CRM-CIC-BTX (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "CRM-CIC-BTX-ACT (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "CRM-CIC-BTX-SLO (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "CRM-CIC-BTX-SVO (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "CRM-CIC-HIS (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "CRM-CIC-HIS-IHI (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "CRM-CIC-HIS-CFA (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "CRM-CIC-IIA (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "CRM-CIC-REP (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "SRM-EBP-TEC-ITS (ITS and Web files)"}, {"Key": "Other Components", "Value": "SRM-BW-EBP (Analytics Enterprise Buyer)"}, {"Key": "Other Components", "Value": "SRM-EBP-TEC-MW (Middleware)"}, {"Key": "Other Components", "Value": "CRM-ANA (CRM Analytics)"}, {"Key": "Other Components", "Value": "CRM-ANA-EXT (Extractors)"}, {"Key": "Other Components", "Value": "CRM-ANA-MSA-WBI (Workbookimport from BW)"}, {"Key": "Other Components", "Value": "CRM-ANA-MKT-SEG (Application Integration)"}, {"Key": "Other Components", "Value": "CRM-MD-PPR (Partner/Product Range)"}, {"Key": "Other Components", "Value": "CRM-CIC-BRT (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "CRM-IT-PRO (obsolete - please use CRM-BTX-PRV)"}, {"Key": "Other Components", "Value": "CRM-BTX-BF-ACI (Actions/Output Determination for Business Transactions)"}, {"Key": "Other Components", "Value": "CRM-BTX-BF-STA (Status Management for Business Transactions)"}, {"Key": "Other Components", "Value": "SRM-EBP-TEC-PFM (Performance)"}, {"Key": "Other Components", "Value": "CRM-ISA-CAT (Internet Sales Catalog)"}, {"Key": "Other Components", "Value": "CRM-BTX-LEA (Lead Management)"}, {"Key": "Other Components", "Value": "SRM-EBP-TEC-UPG (Upgrade)"}, {"Key": "Other Components", "Value": "CA-AUD (PLM Audit Management)"}, {"Key": "Other Components", "Value": "SRM-EBP-TEC-INS (Installation)"}, {"Key": "Other Components", "Value": "CRM-CIC-FRW (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "SRM-EBP-TEC-PM (Product information)"}, {"Key": "Other Components", "Value": "CRM-MKT-MPL-ST-BI (BI Interface)"}, {"Key": "Other Components", "Value": "CRM-MKT-MPL-ST-MSA (MSA Interface)"}, {"Key": "Other Components", "Value": "CRM-CIC-BRO (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "CRM-MKT-SEG-MSA (MSA-Interface Target Groups / Attributes)"}, {"Key": "Other Components", "Value": "CRM-BE (Billing)"}, {"Key": "Other Components", "Value": "CRM-CIC-COM (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "CRM-BTX-SCO (Service Confirmation)"}, {"Key": "Other Components", "Value": "CRM-BTX-COI (Controlling Integration)"}, {"Key": "Other Components", "Value": "CRM-ISE-WBF (Web Forms)"}, {"Key": "Other Components", "Value": "CRM-ISE-UAD (User Administration)"}, {"Key": "Other Components", "Value": "CRM-MD-BP-BAG (Business Agreements)"}, {"Key": "Other Components", "Value": "CRM-MD-BP-CCP (Business Partner Cockpit)"}, {"Key": "Other Components", "Value": "CRM-MD-BP-XIF (External Interface Business Partner)"}, {"Key": "Other Components", "Value": "SRM-CMT (Content Management)"}, {"Key": "Other Components", "Value": "SRM-EBP-ADM-USR (User Administration)"}, {"Key": "Other Components", "Value": "SRM-EBP-ADM-XBP (External Business Partner)"}, {"Key": "Other Components", "Value": "SRM-EBP-ADM-ORG (Organizational Management)"}, {"Key": "Other Components", "Value": "SRM-EBP-CA-ACC (Account Assignment)"}, {"Key": "Other Components", "Value": "SRM-EBP-CA-TAX (Tax)"}, {"Key": "Other Components", "Value": "SRM-EBP-CA-XML (XML Communication)"}, {"Key": "Other Components", "Value": "CRM-BF-EEW (Easy Enhancement Workbench)"}, {"Key": "Other Components", "Value": "CRM-BE-DL (Billing Due List)"}, {"Key": "Other Components", "Value": "CRM-BE-BD (Billing Document)"}, {"Key": "Other Components", "Value": "CRM-BE-FI (Transfer to Accounting)"}, {"Key": "Other Components", "Value": "CRM-BE-BW (Connection to BW)"}, {"Key": "Other Components", "Value": "CRM-BE-OC (Action Processing)"}, {"Key": "Other Components", "Value": "CRM-MD-PRO-XIF (External Interface Product)"}, {"Key": "Other Components", "Value": "CRM-MD-CON-XIF (External Interfaces Conditions/Pricing)"}, {"Key": "Other Components", "Value": "CRM-BF-SVY (Survey Suite)"}, {"Key": "Other Components", "Value": "CRM-BF-COM (Content Management)"}, {"Key": "Other Components", "Value": "CRM-ISA-CFG (Product Configuration Internet Sales)"}, {"Key": "Other Components", "Value": "CRM-BF-OFI (Obsolete, use: CRM-MD-ORG)"}, {"Key": "Other Components", "Value": "CRM-CIC-HIS-INB (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "CRM-CIC-INB (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "CRM-BE-XIF (External Interface Invoice)"}, {"Key": "Other Components", "Value": "CRM-BTX-BF-XIF (External Interface Business Transaction)"}, {"Key": "Other Components", "Value": "SRM-SUS (Supplier Self-Services)"}, {"Key": "Other Components", "Value": "CRM-MD-SDB-XIF (External Interface Solution Database)"}, {"Key": "Other Components", "Value": "CRM-BTX-BF-PR (Pricing Interface)"}, {"Key": "Other Components", "Value": "CRM-CIC-COM-ITP (obsolete: use CRM-CIC für SAPGUI Interaction Center)"}, {"Key": "Other Components", "Value": "BC (Basis Components)"}, {"Key": "Other Components", "Value": "CRM-AUD (Audit Management (CRM connection))"}, {"Key": "Other Components", "Value": "CRM-ISA-AUC (Web Auction)"}, {"Key": "Other Components", "Value": "CRM-ISA-LWC (Live Web Collaboration)"}, {"Key": "Other Components", "Value": "CRM-ISA-TEC (Technical Infrastructure)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D033006)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000445988/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000445988/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000445988/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000445988/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000445988/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000445988/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000445988/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000445988/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000445988/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "440551", "RefComponent": "AP-MD-BP", "RefTitle": "Data cleansing: format of the time stamps", "RefUrl": "/notes/440551"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "440551", "RefComponent": "AP-MD-BP", "RefTitle": "Data cleansing: format of the time stamps", "RefUrl": "/notes/440551 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "300", "To": "300", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "610", "Subsequent": ""}, {"SoftwareComponent": "SAP_ABA", "From": "50A", "To": "50A", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "300", "To": "300", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "BBPCRM 300", "SupportPackage": "SAPKU30006", "URL": "/supportpackage/SAPKU30006"}, {"SoftwareComponentVersion": "SAP_ABA 50A", "SupportPackage": "SAPKA50A09", "URL": "/supportpackage/SAPKA50A09"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}