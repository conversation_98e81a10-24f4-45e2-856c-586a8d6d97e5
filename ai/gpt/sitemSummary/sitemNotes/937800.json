{"Request": {"Number": "937800", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 458, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016084162017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000937800?language=E&token=B61B38DAFCEC04567028DC3AFD99003B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000937800", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000937800/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "937800"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "07.12.2006"}, "SAPComponentKey": {"_label": "Component", "value": "XAP-EM-INT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Emissions Management Integration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Collaborative Cross Applications", "value": "XAP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XAP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Emissions Management (SAP xEM)", "value": "XAP-EM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XAP-EM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Emissions Management Integration", "value": "XAP-EM-INT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XAP-EM-INT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "937800 - xEM integration"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note describes the xEM ERP05 integration.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Emissions Management<br />Plant security<br />SAP xEM</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Prerequisite is an xEM installation (xEM 2.0 SP7).<br />IS-DFPS 610</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The xEM 'Emission Management' was further developed as a component for plant security.<br />EA-DFPS 610 is required for the integration.<br /><br />To be able to use the functions, the following settings are required in ERP and xEM.<br />ERP settings:<br />==================<br />Customizing:<br />Title element 'Plant security'<br />- Plant Maintenance and Customer Service<br />-- Technical Objects<br />--- General Data<br />---- Set View Profiles for Technical Objects<br />----- Activity and layout of views<br />------ 60 Additional data 1 --&gt; Icons and texts of views 'Plant security'<br /><br />Also maintain the view profiles for functional locations in the same way.<br /><br />Maintain the view profile assignment:<br />-- Functional location<br />-- Equipment<br />for a type, for example, for type 'M'.<br /><br />Check whether the switch is active. Interface IF_EX_BADI_EM_INTEGRATION<br />Method IS_ACTIVE<br />METHOD if_ex_badi_em_integration~is_active.<br /><br />&#x00A0;&#x00A0;active = 'X'.<br />-<br />ENDMETHOD.<br /><br />Set up the RFC connections (transaction SM59) for the xEM-server.<br />Set up the HTTP connections for external server: Type 'G', enter the following web services:<br />EM_Central 'EM emissions management - central'<br />EM_DISP_DETAIL 'EM- emissions management - display the detail data'<br />EM_LOCAL 'EM - Emissions management - - local'<br />In the technical settings, maintain the target server for each web service.<br /><br />Maintain the logical ports centrally and locally:<br />Transaction: lpconfig<br /><br /><br />If the following error occurs in ERP:<br />SOAP:111 Unallowed RFC-XML Tag (SOAP_EINVALDOC)<br />--&gt; Solution: The settings for the logical ports do not match.<br /><br /><br />xEM settings:<br />==================<br /><br />In xEM, the following Java Web services must be deployed:<br /><br />EmFacilityGetDetail<br />===================<br />- Web service to query plant detail data, use during distribution and during display of data on PM tab or in WM report.<br /><br />EmFacilityGetF4<br />===============<br />- Web service for the F4 help in PM.<br /><br />EmFacilityGetList<br />=================<br />- Web service that is required for the distribution.<br /><br />EMFacilitySetDetail<br />===================<br />- Web service to post plant data on decentralized systems (may only be deployed on decentralized systems, not required on the central xEM).<br /><br />EMSDPChecklistImporter<br />======================<br /><br />- Web service to import checklist values on the central xEM.<br /><br /><br />xEM RFC settings:<br />======================<br />RFC settings on xEM for<br /><br /> * WM integration<br /> * PM integration<br /><br /><br />These are available in xEM under: Administration (xEM 2.0 Support Package 6) or configuration/infrastructure/RFC destinations (xEM 2.0 SP7+ )<br /><br />Here a connection-enabled R/3 system is defined. A \"Connection-enabled R/3 system\" can be assigned to several RFC types.<br />The following types are required for plant security:<br /><br /> Type  Description<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;___&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;____________<br /><br /> * ALM Sync --&gt; Query of the PM status, Query/assignment of &#x00A0;&#x00A0; Equi/TP in the xEM integration.<br /><br /> * EAM Notification --&gt; Option to create a PM notification.<br /><br /> * WM integration --&gt; Query/Assignment of warehouse number, storage type, storage section in the xEM integration.<br /><br /> * EAM order &#x00A0;&#x00A0; --&gt; Option to create a PM &#x00A0;&#x00A0; order.<br /><br />Note 1:<br />============<br />The RFC types can only be assigned to a connection to R/3. If an RFC type was already assigned to a connection, this RFC type no longer appears in the drop down menu of the elements to be assigned.<br /><br />Note 2:<br />============<br />The user who was used to parameterize the RFC connection must have the relevant authorizations in R/3 to create a PM order, for example.<br /><br />Note<br />=========<br />xEM exists in the specification \"xEM emissions management\" and in the specification \"xEM plant security\". Ensure that the correct initial data (phrases, switch settings, and so on) are used during the installation of the xEM system. The initial data is contained in Init.dat, which is imported during the system installation. --&gt;See Note: 958384.<br /><br /><br />Setting for navigation from xEM to the PM iViews:<br />==================================================<br /><br />Use the URL http://&lt;host&gt;:&lt;port&gt;/webdynpro/dispatcher/local/EmissionsManagement/TDProperties<br />as an authorized user to enter the settings. The setting to jump from the xEM PM tab to the PM iViews has the following name:<br /><br />facility.pm_ext_ref_url (you may have to scroll in the table)<br /><br />The value to be entered must be as follows:<br />http://&lt;erphost&gt;:&lt;Port&gt;/sap/bc/webdynpro/sap/mt_assetdetail_app<br />After you click 'Save', the xEM can be started again in the browser, the settings were transferred, and you can jump from the PM tab of the creation to the iView of PM is possible, as long as a piece of equipment or a functional location was assigned to the xEM plant using the integration.<br /><br />TIP: To ensure the correct entry at http://&lt;erphost&gt;:&lt;Port&gt;/sap/..., proceed as follows:<br />Logon at the ERP to be integrated (only client 100 is allowed)<br />-Call transaction SE 80.<br />--Package: RPLM_MT_UI<br />---Web Dynpro- Web Dynpro Applications - MT_ASSETDETAIL_APP.<br /><br />When you double-click MT_ASSETDETAIL_APP, the property view opens. In the 'Administration Data' area (below in the view) the URL for the iViews is specified. This URL must be entered as facility.pm_ext_ref_url in xEM.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "PM-EQM-SF-EM (Emission Management Integration)"}, {"Key": "Responsible                                                                                         ", "Value": "D001309"}, {"Key": "Processor                                                                                           ", "Value": "D001309"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000937800/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000937800/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000937800/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000937800/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000937800/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000937800/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000937800/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000937800/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000937800/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "958384", "RefComponent": "XAP-EM", "RefTitle": "Installation of xEM 2.0 SP7 for plant security", "RefUrl": "/notes/958384"}, {"RefNumber": "957603", "RefComponent": "XAP-EM", "RefTitle": "SAP xEM 2.0 SP7 Release Notes", "RefUrl": "/notes/957603"}, {"RefNumber": "1161250", "RefComponent": "XAP-EM-INT", "RefTitle": "Env. Compl. Integration (Release 2.0 SP12 PL01 and higher)", "RefUrl": "/notes/1161250"}, {"RefNumber": "1073313", "RefComponent": "XAP-EM-INT", "RefTitle": "xEM Integration (as of xEM 2.0 Support Package 9 and higher)", "RefUrl": "/notes/1073313"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1161250", "RefComponent": "XAP-EM-INT", "RefTitle": "Env. Compl. Integration (Release 2.0 SP12 PL01 and higher)", "RefUrl": "/notes/1161250 "}, {"RefNumber": "957603", "RefComponent": "XAP-EM", "RefTitle": "SAP xEM 2.0 SP7 Release Notes", "RefUrl": "/notes/957603 "}, {"RefNumber": "1073313", "RefComponent": "XAP-EM-INT", "RefTitle": "xEM Integration (as of xEM 2.0 Support Package 9 and higher)", "RefUrl": "/notes/1073313 "}, {"RefNumber": "958384", "RefComponent": "XAP-EM", "RefTitle": "Installation of xEM 2.0 SP7 for plant security", "RefUrl": "/notes/958384 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "DFPS", "From": "610", "To": "610", "Subsequent": ""}, {"SoftwareComponent": "XEM", "From": "200", "To": "200", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}