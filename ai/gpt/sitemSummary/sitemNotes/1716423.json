{"Request": {"Number": "1716423", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 429, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017436422017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001716423?language=E&token=2649C854D6EAAE09112E43C9C3360482"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001716423", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001716423/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1716423"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 34}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.04.2023"}, "SAPComponentKey": {"_label": "Component", "value": "CA-UI5-COR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Core and Runtime"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAPUI5", "value": "CA-UI5", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-UI5*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Core and Runtime", "value": "CA-UI5-COR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-UI5-COR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1716423 - SAPUI5 Browser Support"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This central SAPUI5 note lists restrictions and additional information to the Product Availability Matrix web browser platform release information of SAPUI5 and the libraries of SAPUI5.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Browser Support for SAPUI5,&#160;User Interface, browser, client, Internet Explorer, IE, Chrome, Firefox, Safari, iOS, Android, Portal, UI, Mobile Web Application, Web application, mobile browser</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><strong>SAPUI5</strong> is based on CSS3, HTML5 and Javascript. Only browsers with HTML5 capabilities are supported in general.<br /><br /></p>\r\n<p><strong>SAPUI5 version specific information</strong></p>\r\n<p>SAPUI5 version specific information can be found in the SAPUI5 SDK <a target=\"_blank\" href=\"https://sapui5.hana.ondemand.com/#\">demokit</a>&#160;under \"Documentation\" --&gt;\"Read me first\" --&gt; \"Browser and Platform Support\":</p>\r\n<p>Latest version of SAPUI5 browser support information: (<a target=\"_blank\" href=\"https://sapui5.hana.ondemand.com/#/topic/74b59efa0eef48988d3b716bd0ecc933\">direct link</a>) .</p>\r\n<p>For a specific version, choose your version under \"Change Version\".</p>\r\n<p>For more information, see&#160;<a target=\"_blank\" href=\"https://sapui5.hana.ondemand.com/versionoverview.html\">https://sapui5.hana.ondemand.com/versionoverview.html</a>&#160;.</p>\r\n<p><strong>On Premise Product Availability Matrix:</strong></p>\r\n<p>The SAPUI5 Product Availability Matrix (<a target=\"_blank\" href=\"https://apps.support.sap.com/sap/support/pam\">PAM</a>) is also contained in the SAP products \"SAP NetWeaver\" and \"SAP Fiori Front-end Server\":</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\" style=\"width: 651px; height: 220px;\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p>SAPUI5</p>\r\n<p>Version</p>\r\n</td>\r\n<td>\r\n<p>Software Component</p>\r\n<p>User Interface technology</p>\r\n</td>\r\n<td>\r\n<p>Minimum<br />SP</p>\r\n</td>\r\n<td>\r\n<p>initially available in</p>\r\n</td>\r\n<td>SAP Fiori Frontend Server</td>\r\n</tr>\r\n<tr>\r\n<td>1.38.xx</td>\r\n<td>7.50</td>\r\n<td>04</td>\r\n<td>AS ABAP 7.50</td>\r\n<td>2.0</td>\r\n</tr>\r\n<tr>\r\n<td>1.44.xx</td>\r\n<td>7.51</td>\r\n<td>01</td>\r\n<td>AS ABAP&#160;7.51</td>\r\n<td>3.0</td>\r\n</tr>\r\n<tr>\r\n<td>1.52.xx</td>\r\n<td>7.52</td>\r\n<td>02</td>\r\n<td>AS ABAP&#160;7.52</td>\r\n<td>4.0</td>\r\n</tr>\r\n<tr>\r\n<td>1.60.xx</td>\r\n<td>7.53</td>\r\n<td>02</td>\r\n<td>S/4H1809</td>\r\n<td>5.0</td>\r\n</tr>\r\n<tr>\r\n<td>1.71.xx</td>\r\n<td>7.54</td>\r\n<td>02</td>\r\n<td>S/4H1909</td>\r\n<td>6.0</td>\r\n</tr>\r\n<tr>\r\n<td>1.84.xx</td>\r\n<td>7.55</td>\r\n<td>02</td>\r\n<td>S/4H2020</td>\r\n<td>\r\n<div>2020&#160;for S/4HANA&#160;</div>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>1.96.xx</td>\r\n<td>7.56</td>\r\n<td>02</td>\r\n<td>S/4H2021</td>\r\n<td>\r\n<div>2021&#160;for S/4HANA&#160;</div>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>1.108.xx</td>\r\n<td>7.57</td>\r\n<td>02</td>\r\n<td>S/4H2022</td>\r\n<td>\r\n<div>2022&#160;for S/4HANA</div>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>SAP assumes that you have applied the latest patches for a given NetWeaver SPS.&#160;SAP assumes you have installed the latest updates for the browser.</p>\r\n<p>For additional information about Fiori Front-end Server, see note&#160;<a target=\"_blank\" href=\"/notes/2217489\">2217489</a>&#160;-&#160;Maintenance and Update Strategy for SAP Fiori Front-End&#160;Server.</p>\r\n<p>&#160;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Known Restrictions</strong></p>\r\n<p>For \"Device-Specific Policies\" (Reference Devices) please refer to the SAPUI5 Documentation for the specific SAPUI5 version or NetWeaver Product Version.</p>\r\n<p>Fiori applications or other SAPUI5 based applications&#160;may support different&#160;combinations of operating systems, browsers and devices. <br />Please refer to central note <a target=\"_blank\" href=\"/notes/1935915\">1935915</a> - Fiori for Business Suite: Browser / Devices / OS Information.</p>\r\n<p>Each individual SAP Product may have addtional restrictions or extensions. Refer to the additional product web browser informations and release notes.</p>\r\n<p>SAPUI5 consists of a number of <strong>controls</strong>&#160;<strong>libraries</strong> each realizing a part of the user interface. For individual SAPUI5 control libraries, e.g. used in custom development, additional restrictions may apply. For additional information about SAPUI5 library specific restrictions to browser, operating systems and reference devices, please refer to the&#160;SAPUI5 Documentation for the specific SAPUI5 version or NetWeaver Product Version.</p>\r\n<p>For browser specific restrictions please refer to central browser notes listed in the References section.</p>\r\n<p>&#160;</p>\r\n<p>&#160;</p>\r\n<p>&#160;</p>\r\n<p>&#160;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CA-UI5-CTR (SAP UI5 Controls)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D037012)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D019413)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001716423/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001716423/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001716423/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001716423/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001716423/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001716423/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001716423/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001716423/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001716423/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "990034", "RefComponent": "BC-WD-JAV", "RefTitle": "Browser: Support Policy for Mozilla Firefox Browsers", "RefUrl": "/notes/990034"}, {"RefNumber": "2217489", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/2217489"}, {"RefNumber": "1935915", "RefComponent": "CA-UI5-COR", "RefTitle": "Fiori for Business Suite: Browser / Devices / OS Information", "RefUrl": "/notes/1935915"}, {"RefNumber": "1917162", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1917162"}, {"RefNumber": "1769258", "RefComponent": "CA-UI5-COR", "RefTitle": "UI develoment toolkit for HTML5 - Restrictions", "RefUrl": "/notes/1769258"}, {"RefNumber": "1759682", "RefComponent": "CA-UI5-COR", "RefTitle": "UI Add-On for SAP NetWeaver: Central Note", "RefUrl": "/notes/1759682"}, {"RefNumber": "1728946", "RefComponent": "BC-WD-ABA", "RefTitle": "Browser: Browser Support Strategy for NetWeaver", "RefUrl": "/notes/1728946"}, {"RefNumber": "1708020", "RefComponent": "EP-PIN-AI", "RefTitle": "Support of UI5 based on ABAP applications in the portal", "RefUrl": "/notes/1708020"}, {"RefNumber": "1705067", "RefComponent": "EP-PIN-NAV-MFP", "RefTitle": "Known issues for Portal on Device", "RefUrl": "/notes/1705067"}, {"RefNumber": "1672817", "RefComponent": "BC-WD-ABA", "RefTitle": "Browser: Microsoft Legacy Edge and Internet Explorer Support Policy Note", "RefUrl": "/notes/1672817"}, {"RefNumber": "1634749", "RefComponent": "BC-WD-ABA", "RefTitle": "Safari browser for end user and administrators", "RefUrl": "/notes/1634749"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2991500", "RefComponent": "MOB-FC", "RefTitle": "Attempting to open Fiori application on Android mobile \"App could not be opened because the SAP UI5 component could not be loaded.\"", "RefUrl": "/notes/2991500 "}, {"RefNumber": "2993884", "RefComponent": "XX-SER-MCC", "RefTitle": "SAP CRM with IE11 Support dates and roadmap for browser compatibility", "RefUrl": "/notes/2993884 "}, {"RefNumber": "2497462", "RefComponent": "CA-FE-FAL", "RefTitle": "How to check the supported device types (phone, tablet, desktop) for a specific standard Fiori application", "RefUrl": "/notes/2497462 "}, {"RefNumber": "2569081", "RefComponent": "CA-UI5-COR", "RefTitle": "IE browser support in SAPUI5 after 2021", "RefUrl": "/notes/2569081 "}, {"RefNumber": "2847618", "RefComponent": "CA-FLP-FE-UI", "RefTitle": "Is there a preferred browser for Fiori Launchpad support?", "RefUrl": "/notes/2847618 "}, {"RefNumber": "2642612", "RefComponent": "CA-UI5-COR", "RefTitle": "Error in Inspector(F12) only with Microsoft Edge Browser 'Unable to get property 'handleEvent' of undefined or null reference'", "RefUrl": "/notes/2642612 "}, {"RefNumber": "2466245", "RefComponent": "EP-PIN-NAV-FFP", "RefTitle": "SAP Mobile Browser Support for fiori FLP", "RefUrl": "/notes/2466245 "}, {"RefNumber": "3400437", "RefComponent": "SCM-IBP-ANA", "RefTitle": "SAP IBP OD 2408 - Supported Web Browsers", "RefUrl": "/notes/3400437 "}, {"RefNumber": "3400381", "RefComponent": "SCM-IBP-ANA", "RefTitle": "SAP IBP OD 2405 - Supported Web Browsers", "RefUrl": "/notes/3400381 "}, {"RefNumber": "3359638", "RefComponent": "SCM-IBP-ANA", "RefTitle": "SAP IBP OD 2402 - Supported Web Browsers", "RefUrl": "/notes/3359638 "}, {"RefNumber": "3312567", "RefComponent": "SCM-IBP-ANA", "RefTitle": "SAP IBP OD 2311 - Supported Web Browsers", "RefUrl": "/notes/3312567 "}, {"RefNumber": "3278355", "RefComponent": "SCM-IBP-ANA", "RefTitle": "SAP IBP OD 2308 - Supported Web Browsers", "RefUrl": "/notes/3278355 "}, {"RefNumber": "3278226", "RefComponent": "SCM-IBP-ANA", "RefTitle": "SAP IBP OD 2305 - Supported Web Browsers", "RefUrl": "/notes/3278226 "}, {"RefNumber": "3238907", "RefComponent": "SCM-IBP-ANA", "RefTitle": "SAP IBP OD 2302 - Supported Web Browsers", "RefUrl": "/notes/3238907 "}, {"RefNumber": "3199709", "RefComponent": "SCM-IBP-ANA", "RefTitle": "SAP IBP OD 2211 - Supported Web Browsers", "RefUrl": "/notes/3199709 "}, {"RefNumber": "3163664", "RefComponent": "SCM-IBP-ANA", "RefTitle": "SAP IBP OD 2208 - Supported Web Browsers", "RefUrl": "/notes/3163664 "}, {"RefNumber": "3127892", "RefComponent": "SCM-IBP-ANA", "RefTitle": "SAP IBP OD 2205 - Supported Web Browsers", "RefUrl": "/notes/3127892 "}, {"RefNumber": "3118482", "RefComponent": "CA-ML-CAI-CON", "RefTitle": "SAP Conversational AI Clients : Browser Support for Internet Explorer and Internet Explorer mode in MS Edge", "RefUrl": "/notes/3118482 "}, {"RefNumber": "3069232", "RefComponent": "SCM-IBP-ANA", "RefTitle": "SAP IBP OD 2111 - Supported Web Browsers", "RefUrl": "/notes/3069232 "}, {"RefNumber": "3069221", "RefComponent": "SCM-IBP-ANA", "RefTitle": "SAP IBP OD 2202 - Supported Web Browsers", "RefUrl": "/notes/3069221 "}, {"RefNumber": "3047969", "RefComponent": "SCM-IBP-ANA", "RefTitle": "SAP IBP OD 2108 - Supported Web Browsers", "RefUrl": "/notes/3047969 "}, {"RefNumber": "3017634", "RefComponent": "SCM-IBP-ANA", "RefTitle": "SAP IBP OD 2105 - Supported Web Browsers", "RefUrl": "/notes/3017634 "}, {"RefNumber": "2977799", "RefComponent": "SCM-IBP-ANA", "RefTitle": "SAP IBP OD 2102 - Supported Web Browsers", "RefUrl": "/notes/2977799 "}, {"RefNumber": "2937849", "RefComponent": "SCM-IBP-ANA", "RefTitle": "SAP IBP OD 2011 - Supported Web Browsers", "RefUrl": "/notes/2937849 "}, {"RefNumber": "2897354", "RefComponent": "SCM-IBP-XLS", "RefTitle": "SAP IBP EXCEL ADD-ON 2008 - Open Source Legal Notification (OSLN)", "RefUrl": "/notes/2897354 "}, {"RefNumber": "2897353", "RefComponent": "SCM-IBP-ANA", "RefTitle": "SAP IBP OD 2008 - Supported Web Browsers", "RefUrl": "/notes/2897353 "}, {"RefNumber": "2711932", "RefComponent": "LOD-CPS", "RefTitle": "System Requirements for SAP Variant Configuration and Pricing", "RefUrl": "/notes/2711932 "}, {"RefNumber": "2879359", "RefComponent": "SCM-IBP-ANA", "RefTitle": "SAP IBP OD 2005 - Supported Web Browsers", "RefUrl": "/notes/2879359 "}, {"RefNumber": "2722235", "RefComponent": "MFG-DM", "RefTitle": "Browser and Platform Support Matrix for SAP Digital Manufacturing", "RefUrl": "/notes/2722235 "}, {"RefNumber": "2902778", "RefComponent": "TM-CF", "RefTitle": "SAP TM - Supported Web Browsers in releases 9.5 and 9.6", "RefUrl": "/notes/2902778 "}, {"RefNumber": "2836392", "RefComponent": "SCM-IBP-ANA", "RefTitle": "SAP IBP OD 2002 - Supported Web Browsers", "RefUrl": "/notes/2836392 "}, {"RefNumber": "2810228", "RefComponent": "SCM-IBP-ANA", "RefTitle": "SAP IBP OD 1911 - Supported Web Browsers", "RefUrl": "/notes/2810228 "}, {"RefNumber": "2756519", "RefComponent": "SCM-IBP-ANA", "RefTitle": "SAP IBP OD 1908 - Supported Web Browsers", "RefUrl": "/notes/2756519 "}, {"RefNumber": "2135948", "RefComponent": "SCM-IBP-XLS-UI", "RefTitle": "Install the S&OP / SAP IBP, Add-In for Microsoft Excel: Supported Configurations / Prerequisites", "RefUrl": "/notes/2135948 "}, {"RefNumber": "2748865", "RefComponent": "SCM-IBP-ANA", "RefTitle": "SAP IBP OD 1902 - Supported Web Browsers", "RefUrl": "/notes/2748865 "}, {"RefNumber": "1672817", "RefComponent": "BC-WD-ABA", "RefTitle": "Browser: Microsoft Legacy Edge and Internet Explorer Support Policy Note", "RefUrl": "/notes/1672817 "}, {"RefNumber": "2226276", "RefComponent": "HAN-AS-XS-ADM", "RefTitle": "HANA XS applications' authentication fails due to IE9,10 incompatibility", "RefUrl": "/notes/2226276 "}, {"RefNumber": "1972127", "RefComponent": "SV-SMG-MON-JBI-JOB", "RefTitle": "SP11: Job Monitoring prerequisites", "RefUrl": "/notes/1972127 "}, {"RefNumber": "1909902", "RefComponent": "SV-SMG-MON-JBI-JOB", "RefTitle": "Job Mon: Prerequisities for SP10", "RefUrl": "/notes/1909902 "}, {"RefNumber": "1935915", "RefComponent": "CA-UI5-COR", "RefTitle": "Fiori for Business Suite: Browser / Devices / OS Information", "RefUrl": "/notes/1935915 "}, {"RefNumber": "1894045", "RefComponent": "SCM-EWM-LM-WL", "RefTitle": "Best practices for installing SAP EWM Labor Demand Planning", "RefUrl": "/notes/1894045 "}, {"RefNumber": "1705067", "RefComponent": "EP-PIN-NAV-MFP", "RefTitle": "Known issues for Portal on Device", "RefUrl": "/notes/1705067 "}, {"RefNumber": "1708020", "RefComponent": "EP-PIN-AI", "RefTitle": "Support of UI5 based on ABAP applications in the portal", "RefUrl": "/notes/1708020 "}, {"RefNumber": "1634749", "RefComponent": "BC-WD-ABA", "RefTitle": "Safari browser for end user and administrators", "RefUrl": "/notes/1634749 "}, {"RefNumber": "1759682", "RefComponent": "CA-UI5-COR", "RefTitle": "UI Add-On for SAP NetWeaver: Central Note", "RefUrl": "/notes/1759682 "}, {"RefNumber": "1787381", "RefComponent": "CA-UI5-TOL", "RefTitle": "sap internal use only:UI5 Rep based on BSP Coll Note SP03+04", "RefUrl": "/notes/1787381 "}, {"RefNumber": "1769258", "RefComponent": "CA-UI5-COR", "RefTitle": "UI develoment toolkit for HTML5 - Restrictions", "RefUrl": "/notes/1769258 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAPUI5_TOOLS", "From": "100", "To": "100", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}