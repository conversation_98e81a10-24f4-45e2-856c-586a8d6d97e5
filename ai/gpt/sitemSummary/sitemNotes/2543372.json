{"Request": {"Number": "2543372", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 431, "Error": {}, "SAPNote": {"_type": "00200720410000000235", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002543372?language=E&token=D9B18C3675F345B54325A3C107B71653"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002543372", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002543372/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2543372"}, "Type": {"_label": "Type", "value": "SAP Knowledge Base Article"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "How To"}, "Priority": {"_label": "Priority", "value": "Normal"}, "Status": {"_label": "Release Status", "value": "Released to Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.10.2023"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-NA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Note Assistant"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "2543372 - How to implement a Transport-based Correction Instruction"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>How to implement Transport-based Correction Instructions (TCI)</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Environment\">Environment</h3>\r\n<h3 data-toc-skip class=\"section\" id=\"Reproducing the Issue\">Reproducing the Issue</h3>\r\n<h3 data-toc-skip class=\"section\" id=\"Cause\">Cause</h3>\r\n<h3 data-toc-skip class=\"section\" id=\"Resolution\">Resolution</h3>\r\n<p>To implement&#160;Transport-based Correction Instructions, it must first be enabled (also known as bootstrapping).</p>\r\n<ul>\r\n<li>If<strong> TCI enablement</strong> is not active in the system, Follow SAP Note <a target=\"_blank\" href=\"/notes/2187425\">2187425</a> to enable TCI on the system.</li>\r\n<li>To confirm the TCI enablement is active, simply check in SNOTE -&gt; <strong>Goto</strong> menu, if the option \"Upload TCI\" exist the TCI Enablement is active.</li>\r\n</ul>\r\n<ol>\r\n<li>Download the TCI Note and the corresponding TCI SAR archive for the software component version that is appropriate for your system<br />To download the TCI SAR archive:</li>\r\n<ol style=\"list-style-type: lower-alpha;\">\r\n<li>Open the TCI Note on SAP for Me and select the <strong>Corrections</strong> tab.<br />(Please note that&#160;SAP Note 2576306 is only used as example and can be any TCI Note)<br /><img class=\"img-responsive\" alt=\"SAR_Download_A.PNG\" height=\"116\" id=\"00109B36BCAE1ED9BFE43873F04A60D5\" src=\"data:image/png;base64,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\" title=\"SAR_Download_A.PNG\" width=\"856\" /><br />&#160;</li>\r\n<li>Under the <strong>Correction Instructions</strong> section, select the appropriate component (SAP_BASIS is only used as an example)<br /><img class=\"img-responsive\" alt=\"SAR_Download_B.PNG\" height=\"127\" id=\"00109B36D6A21ED9BFE43BE1A59620D7\" src=\"data:image/png;base64,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\" title=\"SAR_Download_B.PNG\" width=\"856\" /><br />&#160;</li>\r\n<li>Then select the <strong>Component Version</strong> that is appropriate for your system (SAP_BASIS 700 is only used as an example)<br /><img class=\"img-responsive\" alt=\"SAR_Download_C.PNG\" height=\"358\" id=\"0090FAAA5DF01ED9BFE44605C84480EE\" src=\"data:image/png;base64,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\" title=\"SAR_Download_C.PNG\" width=\"209\" /><br />&#160;</li>\r\n<li>Then click the <strong>Download</strong> link to download the&#160;TCI SAR archive<br /><img class=\"img-responsive\" alt=\"SAR_Download_D.PNG\" height=\"130\" id=\"00109B36D6621ED9BFE44EEF0F53E0D7\" src=\"data:image/png;base64,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\" title=\"SAR_Download_D.PNG\" width=\"227\" /><br />&#160;</li>\r\n</ol>\r\n<li>Upload the TCI SAR archive on the system. On transaction <strong>SNOTE,</strong> choose <strong>Goto&#160;</strong>&gt; <strong><strong>Upload TCI<br /></strong></strong>\r\n<p>(<strong>Note</strong>: Alternatively, you can also upload the TCI SAR archive (for example, K700005CPSAPBASIS.SAR) through SPAM or SAINT from front end to your system. To do so, call transaction SPAM or SAINT in <strong>client 000</strong> and proceed as follows:<br />&#160;- In the transaction SPAM, choose <strong>Support Package</strong> &gt; <strong>Load Packages</strong> &gt; <strong>SAR Archive&#160;From Frond End</strong><br />&#160;- In the transaction SAINT, choose <strong>Installation Package</strong> &gt; <strong>Load Package</strong> &gt;&#160;<strong>SAR Archive</strong>&#160;<strong>From Front End</strong>)</p>\r\n</li>\r\n<li>Upload the&#160;TCI Note on the system. On transaction <strong>SNOTE,</strong> choose <strong>Goto</strong> &gt; <strong>Upload&#160;SAP Note</strong></li>\r\n<li>Implement the TCI Note. On transaction <strong>SNOTE,&#160;</strong>select the TCI Note then choose <strong>SAP Note</strong> &gt; <strong>Implement SAP Note. Caution: </strong>Please<strong> do not </strong>try implement the TCI package via transaction SPAM as even if the TCI package is imported successfully as the import will not be captured in the transport request.</li>\r\n<li>Release and import the created transport request to the follow-up systems in the landscape (if more tr.request were created, import them in one queue). <br />It will transport also the objects of the TCI SPs,&#160;no TCI upload, SPAM usage will be required on the target systems. (Prerequisite: SPAM version is v71 or higher)</li>\r\n</ol>\r\n<h3 data-toc-skip class=\"section\" id=\"See Also\">See Also</h3>\r\n<p>SAP Help Portal:&#160;SAP Note Transport-Based Correction Instructions<br /><a target=\"_blank\" href=\"https://help.sap.com/viewer/9d6aa238582042678952ab3b4aa5cc71/7.51.0/en-US/81a0376ed9b64194b8ecff6f02f32652.html\">https://help.sap.com/viewer/9d6aa238582042678952ab3b4aa5cc71/7.51.0/en-US/81a0376ed9b64194b8ecff6f02f32652.html</a></p>\r\n<p><a target=\"_blank\" href=\"/notes/2489679\">2489679</a> - How to identify TCI Note <br /><a target=\"_blank\" href=\"/notes/2187425\">2187425</a> - Information about SAP Note Transport based Correction Instructions (TCI)</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Keywords\">Keywords</h3>\r\n<p>consume, SPAM, TCI, SAINT, Implement, How to, Transport Based Correction Instruction, upload, download, import</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I819804)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I827739)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002543372/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002543372/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002543372/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002543372/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002543372/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002543372/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002543372/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002543372/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002543372/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2489679", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/2489679"}, {"RefNumber": "2187425", "RefComponent": "BC-UPG-NA", "RefTitle": "Information about SAP Note Transport based Correction Instructions (TCI)", "RefUrl": "/notes/2187425"}, {"RefNumber": "", "RefComponent": "", "RefTitle": "SAP Note Transport-Based Correction Instructions", "RefUrl": "https://help.sap.com/viewer/9d6aa238582042678952ab3b4aa5cc71/7.51.0/en-US/81a0376ed9b64194b8ecff6f02f32652.html"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3212023", "RefComponent": "BC-UPG-NA", "RefTitle": "Snote Error: Not all required corrections were implemented", "RefUrl": "/notes/3212023 "}, {"RefNumber": "2958702", "RefComponent": "BC-INS-TC-CNT", "RefTitle": "Short dump CONVT_NO_NUMBER during SAP_BW_BASIS_COPY_REFRESH_CONFIG / SAP_BASIS_COPY_REFRESH_EXPORT", "RefUrl": "/notes/2958702 "}, {"RefNumber": "2903843", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Implementation state \"cannot be implemented\" when trying to import a Migration cockpit related TCI Note", "RefUrl": "/notes/2903843 "}, {"RefNumber": "2577885", "RefComponent": "BC-UPG-NA", "RefTitle": "\"Not allowed Support Package is already Applied\" when trying to import the TCI Bootstrap note", "RefUrl": "/notes/2577885 "}, {"RefNumber": "3443612", "RefComponent": "EHS-SUS-EM", "RefTitle": "SAP S/4HANA Migration Cockpit: March 2024 - Central correction Note for content issues for SAP S/4HANA 2023", "RefUrl": "/notes/3443612 "}, {"RefNumber": "3441673", "RefComponent": "EHS-SUS-IM", "RefTitle": "SAP S/4HANA Migration Cockpit: March 2024 - Central correction Note for content issues for SAP S/4HANA 2022", "RefUrl": "/notes/3441673 "}, {"RefNumber": "3439229", "RefComponent": "EHS-SUS-EM", "RefTitle": "SAP S/4HANA Migration Cockpit: February 2024 - Central correction Note for content issues for SAP S/4HANA 2023 - II", "RefUrl": "/notes/3439229 "}, {"RefNumber": "3439257", "RefComponent": "EHS-SUS-EM", "RefTitle": "SAP S/4HANA Migration Cockpit: February 2024 - Central correction Note for content issues for SAP S/4HANA 2022", "RefUrl": "/notes/3439257 "}, {"RefNumber": "3439250", "RefComponent": "EHS-SUS-EM", "RefTitle": "SAP S/4HANA Migration Cockpit: February 2024 - Central correction Note for content issues for SAP S/4HANA 2021", "RefUrl": "/notes/3439250 "}, {"RefNumber": "3438092", "RefComponent": "CA-GTF-MIG", "RefTitle": "S/4HANA Migration Cockpit: Documentation of JIT - Supply control can't accessed", "RefUrl": "/notes/3438092 "}, {"RefNumber": "3438546", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: February 2024 - Central correction Note for content issues for SAP S/4HANA 2023 - IV", "RefUrl": "/notes/3438546 "}, {"RefNumber": "3438540", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: February 2024 - Central correction Note for content issues for SAP S/4HANA 2022 - II", "RefUrl": "/notes/3438540 "}, {"RefNumber": "3438539", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: February 2024 - Central correction Note for content issues for SAP S/4HANA 2021", "RefUrl": "/notes/3438539 "}, {"RefNumber": "3437292", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: February 2024 - Central correction Note for content issues for SAP S/4HANA 2023 - III", "RefUrl": "/notes/3437292 "}, {"RefNumber": "3428490", "RefComponent": "EHS-SUS-EM", "RefTitle": "SAP S/4HANA Migration Cockpit: February 2024 - Central correction Note for content issues for SAP S/4HANA 2023", "RefUrl": "/notes/3428490 "}, {"RefNumber": "3424344", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: JANUARY 2024 - Central correction Note for content issues for SAP S/4HANA 2021", "RefUrl": "/notes/3424344 "}, {"RefNumber": "3424271", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: JANUARY 2024 - Central correction Note for content issues for SAP S/4HANA 2020", "RefUrl": "/notes/3424271 "}, {"RefNumber": "3424210", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: JANUARY 2024 - Central correction Note for content issues for SAP S/4HANA 1909", "RefUrl": "/notes/3424210 "}, {"RefNumber": "3422275", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: JANUARY 2024 - Central correction Note for content issues for SAP S/4HANA 2022", "RefUrl": "/notes/3422275 "}, {"RefNumber": "3416856", "RefComponent": "EHS-SUS-EM", "RefTitle": "SAP S/4HANA Migration Cockpit: December 2023 - Central correction Note for content issues for SAP S/4HANA 2023", "RefUrl": "/notes/3416856 "}, {"RefNumber": "3416855", "RefComponent": "EHS-SUS-EM", "RefTitle": "SAP S/4HANA Migration Cockpit: December 2023 - Central correction Note for content issues for SAP S/4HANA 2022", "RefUrl": "/notes/3416855 "}, {"RefNumber": "3416829", "RefComponent": "EHS-SUS-EM", "RefTitle": "SAP S/4HANA Migration Cockpit: December 2023 - Central correction Note for content issues for SAP S/4HANA 2021", "RefUrl": "/notes/3416829 "}, {"RefNumber": "3393257", "RefComponent": "EHS-SUS-EM", "RefTitle": "SAP S/4HANA Migration Cockpit: EHS Calculation Definition Result Header correction", "RefUrl": "/notes/3393257 "}, {"RefNumber": "3357868", "RefComponent": "EHS-SUS-FND", "RefTitle": "SAP S/4HANA Migration Cockpit: Call to function module GUID_CONVERT_ALT failed", "RefUrl": "/notes/3357868 "}, {"RefNumber": "3398814", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Bank - Navigate to Manage Banks - Master Data (F6437) instead of deprecated app Manage Banks for Cash Manager (F1574) in Data Migration Status", "RefUrl": "/notes/3398814 "}, {"RefNumber": "3397943", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Change master - Missing ID mapping for the Change number field", "RefUrl": "/notes/3397943 "}, {"RefNumber": "3365599", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Routing - Add missing long text to messages CNV_DMC_SIN 381, 382, 383, 384, 385, 386, 387, 388", "RefUrl": "/notes/3365599 "}, {"RefNumber": "3066402", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: G/L account balance and open/line item -  Transaction type 0xx not defined", "RefUrl": "/notes/3066402 "}, {"RefNumber": "3358874", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: PP-KAB - Kanban control cycle - Fix CVT_SCWM_NLPLA, CVT_SCWM_VLPLA, CVT_LGTYP_CC and some message texts", "RefUrl": "/notes/3358874 "}, {"RefNumber": "3401944", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: NOVEMBER 2023 - Central correction Note for content issues for SAP S/4HANA 2022- II", "RefUrl": "/notes/3401944 "}, {"RefNumber": "3401876", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: NOVEMBER 2023 - Central correction Note for content issues for SAP S/4HANA 2021 - II", "RefUrl": "/notes/3401876 "}, {"RefNumber": "3401458", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: NOVEMBER 2023 - Central correction Note for content issues for SAP S/4HANA 2020 - II", "RefUrl": "/notes/3401458 "}, {"RefNumber": "3400241", "RefComponent": "EHS-SUS-EM", "RefTitle": "SAP S/4HANA Migration Cockpit: November 2023 - Central correction Note for content issues for SAP S/4HANA 2021", "RefUrl": "/notes/3400241 "}, {"RefNumber": "3400259", "RefComponent": "EHS-SUS-EM", "RefTitle": "SAP S/4HANA Migration Cockpit: November 2023 - Central correction Note for content issues for SAP S/4HANA 2022", "RefUrl": "/notes/3400259 "}, {"RefNumber": "3398244", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: November 2023 - Central correction Note for content issues for SAP S/4HANA 2023", "RefUrl": "/notes/3398244 "}, {"RefNumber": "3382093", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: September 2023 - Central correction Note for content issues for SAP S/4HANA 2022", "RefUrl": "/notes/3382093 "}, {"RefNumber": "3382062", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: September 2023 - Central correction Note for content issues for SAP S/4HANA 2021", "RefUrl": "/notes/3382062 "}, {"RefNumber": "3365904", "RefComponent": "EHS-SUS-FND", "RefTitle": "SAP S/4HANA Migration Cockpit: August 2023 - Central correction Note for content issues for SAP S/4HANA 2022", "RefUrl": "/notes/3365904 "}, {"RefNumber": "3365915", "RefComponent": "EHS-SUS-FND", "RefTitle": "SAP S/4HANA Migration Cockpit: August 2023 - Central correction Note for content issues for SAP S/4HANA 2021", "RefUrl": "/notes/3365915 "}, {"RefNumber": "3364196", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: FI - G/L account & extend: Increase length on field inflation key from 2 to 8 characters", "RefUrl": "/notes/3364196 "}, {"RefNumber": "3358037", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Accounts payable open item or Accounts receivable open item- Vendor/Customer has no bank details with indicator xxxx", "RefUrl": "/notes/3358037 "}, {"RefNumber": "3358011", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: FI - G/L account balance and open/line item - error message KE109: Value \"xxxx\" is not allowed for characteristic \"WBS Element\"", "RefUrl": "/notes/3358011 "}, {"RefNumber": "3365022", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: August 2023 - Central correction Note for content issues for SAP S/4HANA 2022", "RefUrl": "/notes/3365022 "}, {"RefNumber": "3364959", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: August 2023 - Central correction Note for content issues for SAP S/4HANA 2021", "RefUrl": "/notes/3364959 "}, {"RefNumber": "3364988", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: August 2023 - Central correction note for content issues for SAP S/4HANA 2020", "RefUrl": "/notes/3364988 "}, {"RefNumber": "3364987", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: August 2023 - Central correction Note for content issues for SAP S/4HANA 1909", "RefUrl": "/notes/3364987 "}, {"RefNumber": "3351907", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: July 2023 - Central correction Note for content issues for SAP S/4HANA 2022", "RefUrl": "/notes/3351907 "}, {"RefNumber": "3350961", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: July 2023 - Central correction Note for content issues for SAP S/4HANA 2021", "RefUrl": "/notes/3350961 "}, {"RefNumber": "3342407", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: June 2023 - Central correction Note for content issues for SAP S/4HANA 1909", "RefUrl": "/notes/3342407 "}, {"RefNumber": "3342337", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: June 2023 - Central correction Note for content issues for SAP S/4HANA 2022", "RefUrl": "/notes/3342337 "}, {"RefNumber": "3342313", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: June 2023 - Central correction Note for content issues for SAP S/4HANA 2021", "RefUrl": "/notes/3342313 "}, {"RefNumber": "3342308", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: June 2023 - Central correction Note for content issues for SAP S/4HANA 2020", "RefUrl": "/notes/3342308 "}, {"RefNumber": "3338864", "RefComponent": "EHS-SUS-FND", "RefTitle": "SAP S/4HANA Migration Cockpit: May 2023 - Central correction Note for content issues for SAP S/4HANA 2022 - II", "RefUrl": "/notes/3338864 "}, {"RefNumber": "3338773", "RefComponent": "EHS-SUS-FND", "RefTitle": "SAP S/4HANA Migration Cockpit: May 2023 - Central correction Note for content issues for SAP S/4HANA 2021 - II", "RefUrl": "/notes/3338773 "}, {"RefNumber": "3338832", "RefComponent": "EHS-SUS-FND", "RefTitle": "SAP S/4HANA Migration Cockpit: May 2023 - Central correction Note for content issues for SAP S/4HANA 2020 - II", "RefUrl": "/notes/3338832 "}, {"RefNumber": "3333865", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: May 2023 - Central correction Note for content issues for SAP S/4HANA 2022", "RefUrl": "/notes/3333865 "}, {"RefNumber": "3333804", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: May 2023 - Central correction Note for content issues for SAP S/4HANA 2021", "RefUrl": "/notes/3333804 "}, {"RefNumber": "3333838", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: May 2023 - Central correction Note for content issues for SAP S/4HANA 2020", "RefUrl": "/notes/3333838 "}, {"RefNumber": "3333811", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: May 2023 - Central correction Note for content issues for SAP S/4HANA 1909", "RefUrl": "/notes/3333811 "}, {"RefNumber": "3333810", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: May 2023 - Central correction Note for content issues for SAP S/4HANA 1809", "RefUrl": "/notes/3333810 "}, {"RefNumber": "3326741", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Fixed asset (incl. balances and transactions) - field \"Amount Posted\" in sheet/table \"Transactions (Transf. Dur. FY)\" set to optional", "RefUrl": "/notes/3326741 "}, {"RefNumber": "3327488", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: April 2023 - Central correction Note for content issues for SAP S/4HANA 2020 - II", "RefUrl": "/notes/3327488 "}, {"RefNumber": "3323353", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: April 2023 - Central correction Note for content issues for SAP S/4HANA 2022", "RefUrl": "/notes/3323353 "}, {"RefNumber": "3323417", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: April 2023 - Central correction Note for content issues for SAP S/4HANA 2021", "RefUrl": "/notes/3323417 "}, {"RefNumber": "3323426", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: April 2023 - Central correction Note for content issues for SAP S/4HANA 2020", "RefUrl": "/notes/3323426 "}, {"RefNumber": "3316373", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Business Partner Credit Management - migration failed with error CVI_EI 039", "RefUrl": "/notes/3316373 "}, {"RefNumber": "3310798", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Material number conversion issue with external representation", "RefUrl": "/notes/3310798 "}, {"RefNumber": "3313958", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: March 2023 - Central correction Note for content issues for SAP S/4HANA 2022", "RefUrl": "/notes/3313958 "}, {"RefNumber": "3313913", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: March 2023 - Central correction Note for content issues for SAP S/4HANA 2021", "RefUrl": "/notes/3313913 "}, {"RefNumber": "3313920", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: March 2023 - Central correction Note for content issues for SAP S/4HANA 2020", "RefUrl": "/notes/3313920 "}, {"RefNumber": "3301283", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: February 2023 - Central correction Note for content issues for SAP S/4HANA 2022", "RefUrl": "/notes/3301283 "}, {"RefNumber": "3301282", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: February 2023 - Central correction Note for content issues for SAP S/4HANA 2021", "RefUrl": "/notes/3301282 "}, {"RefNumber": "3301305", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: February 2023 - Central correction Note for content issues for SAP S/4HANA 2020", "RefUrl": "/notes/3301305 "}, {"RefNumber": "3301297", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: February 2023 - Central correction Note for content issues for SAP S/4HANA 1909", "RefUrl": "/notes/3301297 "}, {"RefNumber": "3299933", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Fixed asset (incl. balances and transactions) - set local fields \"Date Validity Ends\" and \"Date for Beginning of Valid.\" in sheet/table \"Local - Time-Dependent\" to obsolete", "RefUrl": "/notes/3299933 "}, {"RefNumber": "3295265", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Fixed asset (incl. balances and transactions) - field \"Amount Posted\" in sheet/table \"Transactions (Transf. Dur. FY)\" set to optional", "RefUrl": "/notes/3295265 "}, {"RefNumber": "3296696", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: January 2023 - Central correction Note for content issues for SAP S/4HANA 2021", "RefUrl": "/notes/3296696 "}, {"RefNumber": "3296704", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: January 2023 - Central correction Note for content issues for SAP S/4HANA 2020", "RefUrl": "/notes/3296704 "}, {"RefNumber": "3296650", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: January 2023 - Central correction Note for content issues for SAP S/4HANA 1909", "RefUrl": "/notes/3296650 "}, {"RefNumber": "3296695", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: January 2023 - Central correction Note for content issues for SAP S/4HANA 1809", "RefUrl": "/notes/3296695 "}, {"RefNumber": "3291767", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: January 2023 - Central correction Note for content issues for SAP S/4HANA 2022", "RefUrl": "/notes/3291767 "}, {"RefNumber": "3291483", "RefComponent": "CA-LT-MC", "RefTitle": "Important Corrections for Data Migration Objects of SAP S/4HANA 2022 FPS0 and FPS1 delivery", "RefUrl": "/notes/3291483 "}, {"RefNumber": "3262606", "RefComponent": "CA-GTF-MIG", "RefTitle": "long text truncation in maintenance order", "RefUrl": "/notes/3262606 "}, {"RefNumber": "3275382", "RefComponent": "CA-GTF-MIG", "RefTitle": "SIF_TR_DA_NOTICE - remove (deprecated)", "RefUrl": "/notes/3275382 "}, {"RefNumber": "3262018", "RefComponent": "CA-GTF-MIG", "RefTitle": "long text truncation in maintenance order", "RefUrl": "/notes/3262018 "}, {"RefNumber": "3261932", "RefComponent": "CA-GTF-MIG", "RefTitle": "Generation issues for Object SIF_FIRA_CONTR", "RefUrl": "/notes/3261932 "}, {"RefNumber": "3249666", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Business Partner UKMS - integration failed in DRF key mapping", "RefUrl": "/notes/3249666 "}, {"RefNumber": "3276342", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: November 2022 - Central correction Note for content issues for SAP S/4HANA 2022", "RefUrl": "/notes/3276342 "}, {"RefNumber": "3259341", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: October 2022 - Central correction Note for content issues for SAP S/4HANA 2021", "RefUrl": "/notes/3259341 "}, {"RefNumber": "3259330", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: October 2022 - Central correction Note for content issues for SAP S/4HANA 2020", "RefUrl": "/notes/3259330 "}, {"RefNumber": "3259309", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: November 2022 - Central correction Note for content issues for SAP S/4HANA 2021", "RefUrl": "/notes/3259309 "}, {"RefNumber": "3235962", "RefComponent": "CA-GTF-MIG", "RefTitle": "Master Inspection Characteristic (MIC) name is not converted to uppercase", "RefUrl": "/notes/3235962 "}, {"RefNumber": "3236170", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: August 2022 - Central correction Note for content issues for SAP S/4HANA 2021 - III", "RefUrl": "/notes/3236170 "}, {"RefNumber": "3236158", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: August 2022 - Central correction Note for content issues for SAP S/4HANA 2020 - II", "RefUrl": "/notes/3236158 "}, {"RefNumber": "3236212", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: August 2022 - Central correction Note for content issues for SAP S/4HANA 1909", "RefUrl": "/notes/3236212 "}, {"RefNumber": "3235481", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Product Extend - Dump when providing S_MLGN sheet (Warehouse Number Data)", "RefUrl": "/notes/3235481 "}, {"RefNumber": "3235950", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: August 2022 - Central correction Note for content issues for SAP S/4HANA 2021 - II", "RefUrl": "/notes/3235950 "}, {"RefNumber": "3232242", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: PM - Maintenance notification - Aviod error \"Enter detection method group &1 in the translation rule MAP_DETECTNGRP (&2)\"", "RefUrl": "/notes/3232242 "}, {"RefNumber": "3233118", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Object classification - General template - Can't classify customer with a char key", "RefUrl": "/notes/3233118 "}, {"RefNumber": "3233114", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Characteristic - No measurement unit is assigned to ISO code XXX", "RefUrl": "/notes/3233114 "}, {"RefNumber": "3234047", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: August 2022 - Central correction Note for content issues for SAP S/4HANA 2021", "RefUrl": "/notes/3234047 "}, {"RefNumber": "3234074", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: August 2022 - Central correction Note for content issues for SAP S/4HANA 2020", "RefUrl": "/notes/3234074 "}, {"RefNumber": "3224799", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: July 2022 - Central correction Note for content issues for SAP S/4HANA 1909", "RefUrl": "/notes/3224799 "}, {"RefNumber": "3224852", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: July 2022 - Central correction Note for content issues for SAP S/4HANA 2020", "RefUrl": "/notes/3224852 "}, {"RefNumber": "3224829", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: July 2022 - Central correction Note for content issues for SAP S/4HANA 2021", "RefUrl": "/notes/3224829 "}, {"RefNumber": "3224715", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: G/L Balance and AP/AR Open Items: transaction currency and local / company code currency are mandatory and zero amount will be populated by exchange rate", "RefUrl": "/notes/3224715 "}, {"RefNumber": "3193289", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: April 2022 - Central correction Note for content issues for SAP S/4HANA 2021", "RefUrl": "/notes/3193289 "}, {"RefNumber": "3155247", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: February 2022 - Central correction Note for content issues for SAP S/4HANA 2021", "RefUrl": "/notes/3155247 "}, {"RefNumber": "3155246", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: February 2022 - Central correction Note for content issues for SAP S/4HANA 2020", "RefUrl": "/notes/3155246 "}, {"RefNumber": "3138509", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: JANUARY 2022 - Central correction Note for content issues for SAP S/4HANA 2020", "RefUrl": "/notes/3138509 "}, {"RefNumber": "3135387", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: DECEMBER 2021 - Central correction Note for content issues for SAP S/4HANA 2021 - II", "RefUrl": "/notes/3135387 "}, {"RefNumber": "3133101", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: DECEMBER 2021 - Central correction Note for content issues for SAP S/4HANA 2021", "RefUrl": "/notes/3133101 "}, {"RefNumber": "3133192", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: DECEMBER 2021 - Central correction Note for content issues for SAP S/4HANA 2020", "RefUrl": "/notes/3133192 "}, {"RefNumber": "3125606", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: NOVEMBER 2021 - Central correction Note for content issues for SAP S/4HANA 2021", "RefUrl": "/notes/3125606 "}, {"RefNumber": "3120781", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: NOVEMBER 2021 - Central correction Note for content issues for SAP S/4HANA 2020", "RefUrl": "/notes/3120781 "}, {"RefNumber": "3094302", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: SEPTEMBER 2021 - Central correction Note for content issues for SAP S/4HANA 1909", "RefUrl": "/notes/3094302 "}, {"RefNumber": "3093257", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: SEPTEMBER 2021 - Central correction Note for content issues for SAP S/4HANA 2020", "RefUrl": "/notes/3093257 "}, {"RefNumber": "3083860", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: FI - G/L account  -  reference to non existing house bank and missing fields subtype and bank reconciliation account", "RefUrl": "/notes/3083860 "}, {"RefNumber": "3073695", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: JULY 2021 - Central correction Note for content issues for SAP S/4HANA 2020", "RefUrl": "/notes/3073695 "}, {"RefNumber": "3069448", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: JUNE 2021 - Central correction Note for content issues for SAP S/4HANA 1909 - II", "RefUrl": "/notes/3069448 "}, {"RefNumber": "3066224", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: JUNE 2021 - Central correction Note for content issues for SAP S/4HANA 1909", "RefUrl": "/notes/3066224 "}, {"RefNumber": "3066267", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: JUNE 2021 - Central correction Note for content issues for SAP S/4HANA 1809", "RefUrl": "/notes/3066267 "}, {"RefNumber": "3066214", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: JUNE 2021 - Central correction Note for content issues for SAP S/4HANA 2020", "RefUrl": "/notes/3066214 "}, {"RefNumber": "3052782", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: MAY 2021 - Central correction Note for content issues for SAP S/4HANA 2020", "RefUrl": "/notes/3052782 "}, {"RefNumber": "3052826", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: MAY 2021 - Central correction Note for content issues for SAP S/4HANA 1909", "RefUrl": "/notes/3052826 "}, {"RefNumber": "3052793", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: MAY 2021 - Central correction Note for content issues for SAP S/4HANA 1809", "RefUrl": "/notes/3052793 "}, {"RefNumber": "3011797", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Fixed asset (incl. balances and transactions) - mapping source value with comma - SAP S/4HANA 1809", "RefUrl": "/notes/3011797 "}, {"RefNumber": "2967749", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: G/L Balance and AP/AR Open Items: Currency amt value cannot be converted, integer part can have max 11 digits - SAP S/4HANA 1809", "RefUrl": "/notes/2967749 "}, {"RefNumber": "2965273", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: SEPTEMBER 2020 - Central correction Note for content issues for SAP S/4HANA 1909", "RefUrl": "/notes/2965273 "}, {"RefNumber": "2964758", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: SEPTEMBER 2020 - Central correction Note for content issues for SAP S/4HANA 1809", "RefUrl": "/notes/2964758 "}, {"RefNumber": "2964549", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Customer and Customer extend: Fix - Home city and credit card token check in SAP S/4HANA 1809", "RefUrl": "/notes/2964549 "}, {"RefNumber": "2537549", "RefComponent": "CA-GTF-MIG", "RefTitle": "Collective SAP Note and FAQ for SAP S/4HANA Migration cockpit - File/Staging (on premise / S4CORE)", "RefUrl": "/notes/2537549 "}, {"RefNumber": "2940912", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: JUNE 2020 - Central correction Note for content issues for SAP S/4HANA 1809", "RefUrl": "/notes/2940912 "}, {"RefNumber": "2940867", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: JUNE 2020 - Central correction Note for content issues for SAP S/4HANA 1909", "RefUrl": "/notes/2940867 "}, {"RefNumber": "2931282", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: MAY 2020 - Central correction Note for content issues for SAP S/4HANA 1909", "RefUrl": "/notes/2931282 "}, {"RefNumber": "2925779", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Material wrong plant specific maintenance status", "RefUrl": "/notes/2925779 "}, {"RefNumber": "2920868", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: MAY 2020 - Central correction Note for content issues for SAP S/4HANA 1809", "RefUrl": "/notes/2920868 "}, {"RefNumber": "2904343", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: MARCH 2020 - Central correction Note for content issues for SAP S/4HANA 1909", "RefUrl": "/notes/2904343 "}, {"RefNumber": "2903384", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: MARCH 2020 - Central correction Note for content issues for SAP S/4HANA 1809 - II", "RefUrl": "/notes/2903384 "}, {"RefNumber": "2901002", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: MARCH 2020 - Central correction Note for content issues for SAP S/4HANA 1809", "RefUrl": "/notes/2901002 "}, {"RefNumber": "2898294", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: FEBRUARY 2020 - Central correction Note for content issues for SAP S/4HANA 1909 - II", "RefUrl": "/notes/2898294 "}, {"RefNumber": "2891675", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: FEBRUARY 2020 - Central correction Note for content issues for SAP S/4HANA 1809", "RefUrl": "/notes/2891675 "}, {"RefNumber": "2891712", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: FEBRUARY 2020 - Central correction Note for content issues for SAP S/4HANA 1909", "RefUrl": "/notes/2891712 "}, {"RefNumber": "2884242", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Sales order: Fix - multiple conditions in SAP S/4HANA 1809", "RefUrl": "/notes/2884242 "}, {"RefNumber": "2860042", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: NOVEMBER 2019 - Central correction Note for content issues for SAP S/4HANA 1909", "RefUrl": "/notes/2860042 "}, {"RefNumber": "2793418", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: JULY 2019 - Central correction Note for content issues for SAP S/4HANA 1809 (II)", "RefUrl": "/notes/2793418 "}, {"RefNumber": "2738426", "RefComponent": "BC-INS-TC-CNT", "RefTitle": "Automated Configuration of new Support Backbone Communication", "RefUrl": "/notes/2738426 "}]}}, "Validity": {"_label": "Products", "_columnNames": {"Product": "Products"}, "Items": [{"Product": "SAP ERP 6.0"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "9 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 4.1, "Quality-Votes": 10, "RatingQualityDetails": {"Stars-1": 1, "Stars-2": 0, "Stars-3": 0, "Stars-4": 5, "Stars-5": 4}, "RatingQuestions": [{"Key": "URF_QA1", "Title": "This article is inaccurate", "Description": ""}, {"Key": "URF_QA2", "Title": "This article is confusing", "Description": ""}, {"Key": "URF_QA3", "Title": "This article doesn't solve my problem", "Description": ""}, {"Key": "URF_QA4", "Title": "I do not like the feature described", "Description": ""}]}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}