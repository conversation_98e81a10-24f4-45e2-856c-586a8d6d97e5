{"Request": {"Number": "1173395", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 262, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016525442017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001173395?language=E&token=45EA68E22F327159190773C4434EDF3E"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001173395", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001173395/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1173395"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 40}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.07.2023"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-SDB"}, "SAPComponentKeyText": {"_label": "Component", "value": "MaxDB"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "MaxDB", "value": "BC-DB-SDB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-SDB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1173395 - FAQ: SAP MaxDB and liveCache configuration"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note is a collection of questions that are often asked by colleagues and customers regarding the SAP MaxDB configuration. It provides answers and refers you to other information sources. This SAP Note does not claim to be complete.</p>\r\n<ol>1: Is there a size limit for an SAP MaxDB database?</ol><ol>2: Are there limits for the number of simultaneous sessions of an SAP MaxDB database?</ol><ol>3: Why should the SAP MaxDB run on a 64-bit platform?</ol><ol>4: How large should the data volumes of an SAP MaxDB database (OLTP/BW) or of a liveCache (SCM) be?</ol><ol>5: Must I change a data volume configuration that does not correspond to the recommendations in this SAP Note?</ol><ol>6: Is there a limit to the number of data volumes?</ol><ol>7: Where do I find information about the configuration of SAP MaxDB volumes of the type \"File\"?</ol><ol>8: Is it advisable to configure all data volumes in the same LUN (logical unit number)?</ol><ol>9: How should I set the access authorizations for volumes?</ol><ol>10: If a new data volume is added, is the data automatically distributed evenly on all volumes?</ol><ol>11: Should data volumes and log volumes be on separate hard disks?</ol><ol>12: How should I set the database parameter MaxCPUs (MAXCPU) for DUAL core CPUs?</ol><ol>13: Can I dynamically assign additional CPUs to the database during live operation?</ol><ol>14: How large should I configure the IO buffer cache?</ol><ol>15: How do I distribute the server tasks to several UKTs?</ol><ol>16: Where do I find information about SAP MaxDB database parameters?</ol><ol>17: Where do I find information about SAP MaxDB and storage systems?</ol>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>FAQ, Frequently Asked Questions, DUAL core, CPU, central processing unit, MAXCPU, MaxCPUs, data volume, log volume, MaxUserTask</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You want to use an SAP MaxDB as of Version 7.7 and have questions about SAP MaxDB configuration.<br /><br />For more information about the SAP liveCache configuration, see SAP Note <a target=\"_blank\" href=\"/notes/719652\">719652</a>. For more FAQ Notes about SAP MaxDB/liveCache, see <a target=\"_blank\" href=\"https://wiki.scn.sap.com/wiki/x/GkM\">here</a>.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ol>\r\n<li>\r\n<div style=\"margin-right: 0px;\">Is there a size limit for an SAP MaxDB database?<br /><br />In the SAP MaxDB standard layout (database parameter: <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">VOLUMENO_BIT_COUNT</span> or <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;ConverterVolumeIDLayout= 8&#xFEFF;</em></span>), a maximum of 255 data volumes can be configured. A data volume can have a maximum size of 128 GB.<br /><br />For information about the maximum overall size of the data volumes, see SAP Note <a target=\"_blank\" href=\"/notes/2179510\">2179510</a>.<br /><br />For further information, refer to the SAP MaxDB documentation (SAP Note <a target=\"_blank\" href=\"/notes/767598\">767598</a>). See the glossary entries <em>Data Volume</em> and <em>Volume</em>, for example.<br /><br /></div>\r\n</li>\r\n<li>\r\n<div style=\"margin-right: 0px;\">Are there limits for the number of simultaneous sessions of an SAP MaxDB database?<br /><br />No, there are no limits. To configure the number of database sessions that can be logged onto the database simultaneously, use the database parameter <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;MaxUserTasks&#xFEFF;</em></span> (<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">MAXUSERTASKS</span>).<br /><br /></div>\r\n</li>\r\n<ul>\r\n<li>\r\n<div style=\"margin-right: 0px;\">OLTP<br />You should configure the number of database users in OLTP systems to at least 2 x <em>&lt;number_sap_processes&gt;</em> + 4.</div>\r\n</li>\r\n<li>\r\n<div style=\"margin-right: 0px;\">BW<br />You should configure the number of database users in OLTP systems to at least 3 x <em>&lt;number_sap_processes&gt;</em> + 4.</div>\r\n</li>\r\n<li>\r\n<div style=\"margin-right: 0px;\">Java applications:<br />In the connection pool, the maximum number of connections to the database is determined for each J2EE instance (for NetWeaver 7.1, the default value is 70). The number of parallel user sessions (database parameter MaxUserTasks (MAXUSERTASKS)) is calculated from the total of the connections (connection pool) of all J2EE instances + 4.</div>\r\n</li>\r\n<li>\r\n<div style=\"margin-right: 0px;\">SAP liveCache<br />For SAP liveCache systems in SCM systems &lt;= 4.1, the value for the database parameter MaxUserTasks (MAXUSERTASKS) is calculated in accordance with the formula <em>2 x &lt;number_of_ sap_processes&gt; + 4</em>.<br />For SAP liveCache systems in SCM systems &gt;= 5.0, the formula <em>3 x &lt;number_of_sap_processes&gt; + 4</em> applies.<br />See also SAP Note <a target=\"_blank\" href=\"/notes/757914\">757914</a>.<br /><br /></div>\r\n</li>\r\n</ul>\r\n<li>\r\n<div style=\"margin-right: 0px;\">Why should the SAP MaxDB run on a 64-bit platform?<br /><br />For more information, see SAP Note <a target=\"_blank\" href=\"/notes/1013441\">1013441</a>.<br /><br /></div>\r\n</li>\r\n<li>\r\n<div style=\"margin-right: 0px;\">How large should I configure the data volumes of an SAP MaxDB database (OLTP/BW) or a liveCache (SCM)?<br /><br />The optimum use of the I/O system is critical for I/O performance. Therefore, it is useful to evenly distribute the volumes across the available I/O channels.<br /><br />The number of data volumes affects the parallelism of the I/O.<br /><br />The maximum size of a data volume is 128 GB. In the case of a planned database size &gt; 15 TB, we expressly recommend that you create each data volume with the maximum size of 128 GB.<br /><br /><span style=\"text-decoration: underline;\">Microsoft Windows:<br /></span>On Microsoft Windows, the asynchronous I/O of the operating system is used until SAP MaxDB Version 7.7.<br /><br /><span style=\"text-decoration: underline;\">Unix:<br /></span>On UNIX, the parallelism with which the SAP MaxDB/liveCache database transfers the I/O requests to the operating system is determined by the number of configured I/O threads.</div>\r\n</li>\r\n<ul>\r\n<li>\r\n<div style=\"margin-right: 0px;\">SAP MaxDB version lower than Version 7.7<br />The number of I/O threads results from the number of volumes * number of I/O threads for each volume (_IOPROCS_PER_DEV).</div>\r\n</li>\r\n<li>\r\n<div style=\"margin-right: 0px;\">SAP MaxDB Version 7.7 or higher<br />The number of I/O threads results from volumes * (total of low/med/high queues for each volume), but can also be limited by the database parameter MaxIOPoolWorkers.<br />A number of data volumes that was configured too high increases the number of threads. As a result, you may reach the limits of the operating system resources.<br /><br /></div>\r\n</li>\r\n</ul>\r\n<ol style=\"list-style-type: lower-alpha;\">\r\n<li>\r\n<div style=\"margin-right: 0px;\"><span style=\"text-decoration: underline;\">OLTP and BW systems:<br /></span>We recommend that you use the following calculation formula for determining the size of SAP MaxDB data volumes: The square root of the system size in gigabytes (GB), rounded up<br />Examples:<br />10 GB: 4 data volumes<br />50 GB: 8 data volumes<br />100 GB: 10 data volumes<br />200 GB: 15 data volumes<br />500 GB: 23 data volumes<br /> 1 TB: 32 data volumes<br /><br /></div>\r\n</li>\r\n<li>\r\n<div style=\"margin-right: 0px;\">SAP liveCache systems (SCM):<br />SAP recommends that you configure the data volumes of an SAP liveCache database to be at least twice as large as the data cache (I/O buffer cache) or three times as large as the data cache to ensure sufficient space for history pages on the data volumes in addition to the application data.<br />It is best if all of the data volumes have the same size.<br /><br /></div>\r\n</li>\r\n</ol>\r\n<li>Must I change a data volume configuration that does not correspond to the recommendations in this note?<br /><br />No. You should not change existing configurations. If serious I/O performance problems occur, you must analyze these problems in detail to determine the actual cause.<br /><br /></li>\r\n<li>Is there a limit for the number of data volumes?<br /><br />In the SAP MaxDB standard layout, you can configure a maximum of 255 data volumes (254 + 1 reserve).<br /><br />If your configuration is already close to the upper limit, you should create a larger new volume in online mode and then delete a small volume etc. In this way, you can make further free space available on a step-by-step basis without infringing upon the maximum number of volumes. Alternatively, you can change the configuration with a backup (SAVE data) followed by a RESTORE data with initialization. However, in this case, you have a downtime.&#x00A0;<br /><br /></li>\r\n<li>Where do I find information about the configuration of SAP MaxDB volumes of the type \"File\"?<br /><br />The speed with which the database system can read data from the data volumes and can write data to the volumes, greatly influences the performance of the database. To ensure good performance when you operate the database later, see SAP Note&#x00A0;<a target=\"_blank\" href=\"/notes/993848\">993848</a> (\"Direct I/O mount options for LiveCache/MaxDB\") for information about creating and configuring volumes of the type \"File\".<br /><br /></li>\r\n<li>Is it advisable to configure all data volumes in the same LUN (Logical Unit Number)?<br /><br />It is advisable to distribute the data volumes across several LUNs. From experience, approximately 5 LUNs are configured for each LUN.<br /><br /></li>\r\n<li>How should I set the access authorizations for volumes?<br /><br />For information about the directory structure, see <a target=\"_blank\" href=\"https://wiki.scn.sap.com/wiki/x/I4U8Gg\">here</a>.<br /><br /></li>\r\n<li>If a new data volume is added, is the data automatically distributed evenly on all volumes?<br /><br />As of database version 7.7.06.09, a mechanism of this kind can be activated by means of the database parameter <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;EnableDataVolumeBalancing&#xFEFF;</em></span>.<br />If the database parameter <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;EnableDataVolumeBalancing&#xFEFF;</em></span> is set to the value YES (which differs from the default value in 7.7), all data is implicitly distributed evenly to all data volumes following the addition of a new data volume or the deletion of a data volume.<br /><br /><span style=\"text-decoration: underline;\">SAP MaxDB Version &lt; 7.9.08.25:</span><br />In SAP MaxDB versions below 7.9.08.25, the volumes are only balanced out if the difference between the average fill level of all volumes (including the newly added volumes) and the average fill volume of the volumes occupied with data (without the new volume) exceeds a threshold value of 10% (PTS 1242644).<br /><br /><span style=\"text-decoration: underline;\">SAP MaxDB Version &gt;= 7.9.08.25:</span><br />If a new volume is added, the balancing out of the volumes is implicitly triggered regardless of the size of the database and the size of the newly added system.<br /><br />A restart implicitly triggers the even distribution of data, too.<br />Redistributing the data increases the IO load of the system.<br /><br /></li>\r\n<li>Should data volumes and log volumes be on separate hard disks?<br /><br />See SAP Note <a target=\"_blank\" href=\"/notes/869267\">869267</a>: FAQ SAP MaxDB log area<br /><br /></li>\r\n<li>How should I set the database parameter MaxCPUs (MAXCPU) for DUAL core CPUs?<br /><br />Since dual core CPUs actually have two cores with separate execution units (a separate L1 cache and sometimes even a separate L2 cache), you should use the number of cores as a basis when calculating  <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;MaxCPUs&#xFEFF;</em></span> (<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;MAXCPU&#xFEFF;</em></span>).<br />For information about setting the database parameter <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;MaxCPUs&#xFEFF;</em></span> (<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;MAXCPU&#xFEFF;</em></span>), see also FAQ Note <a target=\"_blank\" href=\"/notes/936058\">936058</a> about the SAP MaxDB runtime environment.<br /><br /></li>\r\n<li>Can I dynamically assign additional CPUs to the database in live operation?<br /><br />As of SAP MaxDB Version 7.8, you can use the database parameter <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;UseableCPUs&#xFEFF;</em></span> to dynamically add additional CPUs to the database or to reduce the number of CPUs to be used. The maximum number of CPUs to be used continues to be controlled by the database parameter <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;MaxCPUs&#xFEFF;</em></span> (<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;MAXCPU&#xFEFF;</em></span>) (PTS 1147916).<br /><br /></li>\r\n<li>How large should I configure the IO buffer cache?<br /><br />The IO buffer cache is configured using the database parameter <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;CacheMemorySize&#xFEFF;</em></span> (<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;CACHE_SIZE&#xFEFF;</em></span>).<br />The IO buffer cache includes the converter cache and the data cache of an SAP MaxDB database.<br /><br />The size of the IO buffer cache generally has the greatest influence on database performance. The larger the dimensions of the IO buffer cache, the fewer time-consuming hard disk accesses have to be executed. The size of the IO buffer cache that is to be set strongly depends on the application and on the data volume that is to be processed in day-to-day business. In a system that is up and running, it is best if all data can be processed without hard disk accesses, that is, in the data cache. However, this is not usually possible in the OLTP environment and BW environment.<br /><br />When using the SAP liveCache technology, it should be possible to hold all data that is to be processed in the IO buffer cache. The IO buffer cache for SAP liveCache technology should generally be configured using the results of the Quick Sizer (<a target=\"_blank\" href=\"https://me.sap.com/notes/85524/E\">85524 - Sizing SAP Business Solutions (e.g. with the Quick Sizer) - SAP for Me</a>).&#x00A0;<br /><br />The following applies to the IO buffer cache: the larger, the better (provided that sufficient physical memory is available).<br />Note that the database also allocates heap memory in addition to the IO buffer cache. You can determine the overall memory consumption of an SAP MaxDB database using the information from the system table <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">MEMORYALLOKATORSTATISTICS</span>. For more information about this, see SAP Note&#x00A0;<a target=\"_blank\" href=\"/notes/1128916\">1128916</a> FAQ: SAP MaxDB/liveCache heap management.<br /><br />The data cache hit ratio is determined from the number of successful and failed accesses to the data cache. This hit ratio indicates whether the size of the data cache is well configured. The data cache hit ratio does not provide sufficient information if exceptional applications were running at the time of the analysis. During year-end closing (for example), the hit ratio may deteriorate because this data does not have to be held in the cache permanently. Directly after restarting the database, the data cache hit ratio does not indicate whether the system is well configured either because all data must first be loaded into the cache.<br /><br />For SAP systems, the following setting for the size of the I/O buffer cache has been tried and tested by many OLTP customers and BW customers:<br />OLTP NON-UNICODE: 1% of the data volume<br />OLTP UNICODE: 2% of the data volume<br />BW NON-UNICODE: 2% of the data volume<br />BW UNICODE: 4% of the data volume<br /><br />Comment:<br />Performance problems that are reported with a poor hit ratio in the Database Analyzer must be analyzed in detail. In most cases, increasing the size of the data cache does not solve the problem. In the analysis, you must use the command monitor (for example) to determine access strategies that may be insufficient, and that can be corrected by an additional index or by changing the application.<br /><br /></li>\r\n<li>Can I configure the database so that the server tasks run in more than one thread?<br /><br />In the default configuration, all of the server tasks run in one thread together with user tasks. The task cluster configuration is recognizable.<br />The database parameter <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;TaskCluster02&#xFEFF;</em></span> (<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;_TASKCLUSTER_02&#xFEFF;</em></span>)&#x00A0;specifies the number of server tasks:<br />For example: 35*sv</li>\r\n<ol style=\"list-style-type: lower-alpha;\">\r\n<li>SAP MaxDB lower than Version 7.8<br />In lower SAP MaxDB versions, server tasks can be distributed to several UKTs using the database parameter setting <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;EnableMultipleServerTaskUKT = YES&#xFEFF;</em></span> (or <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;ALLOW_MULTIPLE_SERVERTASK_UKTS = YES&#xFEFF;</em></span>).<br />Before setting any database parameters, make sure that you refer to SAP Note <a target=\"_blank\" href=\"/notes/1294726\">1294726</a>. We recommend that you distribute server tasks only after the system has been analyzed in detail.<br /><br /></li>\r\n<li>SAP MaxDB Version 7.8 or higher<br /><br />In SAP MaxDB versions lower than Version 7.8, a more precise configuration of the server tasks was not possible. As of SAP MaxDB Version 7.8, this is possible. However, customers should not change the default setting without reason, and not without a detailed analysis of the system resources.<br />For background information about server tasks and their configuration as of SAP MaxDB Version 7.8, see SAP Note <a target=\"_blank\" href=\"/notes/1672994\">1672994</a>.<br /><br /></li>\r\n</ol>\r\n<li>Where do I find information about SAP MaxDB database parameters?<br /><br />Information about the SAP MaxDB database parameters is available in SAP Note <a target=\"_blank\" href=\"/notes/1139904\">1139904</a>: FAQ SAP MaxDB/liveCache database parameter.<br /><br /></li>\r\n<li>Where do I find information about SAP MaxDB and storage systems?<br /><br />Refer to SAP Note <a target=\"_blank\" href=\"/notes/912905\">912905</a>: \"FAQ: Storage systems used with SAP MaxDB\".<br /><br />For further configuration recommendations, see <a target=\"_blank\" href=\"https://wiki.scn.sap.com/wiki/x/WGY\">here</a>.</li>\r\n</ol></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-LVC (liveCache)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D036661)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D019124)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001173395/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173395/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173395/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173395/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173395/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173395/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173395/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173395/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173395/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "993848", "RefComponent": "BC-DB-LVC", "RefTitle": "Direct I/O mount options for liveCache/MaxDB", "RefUrl": "/notes/993848"}, {"RefNumber": "936058", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB Runtime Environment", "RefUrl": "/notes/936058"}, {"RefNumber": "912905", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: Storage systems used with SAP MaxDB", "RefUrl": "/notes/912905"}, {"RefNumber": "869267", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB Log area", "RefUrl": "/notes/869267"}, {"RefNumber": "822239", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB interfaces", "RefUrl": "/notes/822239"}, {"RefNumber": "767598", "RefComponent": "BC-DB-SDB", "RefTitle": "Available SAP MaxDB documentation", "RefUrl": "/notes/767598"}, {"RefNumber": "757914", "RefComponent": "BC-DB-SDB", "RefTitle": "\"-9807 Task Limit\"", "RefUrl": "/notes/757914"}, {"RefNumber": "719652", "RefComponent": "BC-DB-LVC", "RefTitle": "Setting initial parameters for liveCache 7.5, 7.6, 7.7", "RefUrl": "/notes/719652"}, {"RefNumber": "2929926", "RefComponent": "BC-DB-LVC-CCM", "RefTitle": "Additional information about “Data Cache Usage” alert (SAP liveCache only)", "RefUrl": "/notes/2929926"}, {"RefNumber": "2179510", "RefComponent": "BC-DB-SDB", "RefTitle": "Maximum size of SAP MaxDB/liveCache database", "RefUrl": "/notes/2179510"}, {"RefNumber": "1672994", "RefComponent": "BC-DB-SDB", "RefTitle": "SAP MaxDB: configuration of server tasks", "RefUrl": "/notes/1672994"}, {"RefNumber": "1459538", "RefComponent": "BC-DB-LVC", "RefTitle": "Configuration of MaxCPUs in virtual environments", "RefUrl": "/notes/1459538"}, {"RefNumber": "1294726", "RefComponent": "BC-DB-LVC", "RefTitle": "SAP MaxDB/liveCache crashes during Read Ahead", "RefUrl": "/notes/1294726"}, {"RefNumber": "1139904", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB/liveCache database parameters", "RefUrl": "/notes/1139904"}, {"RefNumber": "1128916", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB/liveCache heap management", "RefUrl": "/notes/1128916"}, {"RefNumber": "1013441", "RefComponent": "BC-DB-SDB", "RefTitle": "Upgrade required: Advantages for MaxDB on 64-bit platforms", "RefUrl": "/notes/1013441"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3154151", "RefComponent": "BC-DB-LVC", "RefTitle": "Process Chain fails with rc = -10709", "RefUrl": "/notes/3154151 "}, {"RefNumber": "2705677", "RefComponent": "BC-DB-SDB", "RefTitle": "How to resize data volume(s) of SAP MaxDB database?", "RefUrl": "/notes/2705677 "}, {"RefNumber": "2929926", "RefComponent": "BC-DB-LVC-CCM", "RefTitle": "Additional information about “Data Cache Usage” alert (SAP liveCache only)", "RefUrl": "/notes/2929926 "}, {"RefNumber": "2856233", "RefComponent": "BC-DB-SDB", "RefTitle": "Problems after update to SAP MaxDB/SAP liveCache Version 7.9.10.02 or higher", "RefUrl": "/notes/2856233 "}, {"RefNumber": "2552731", "RefComponent": "BC-OP-LNX-ALIBABA", "RefTitle": "SAP Applications on Alibaba Cloud: Supported Products and IaaS VM Types", "RefUrl": "/notes/2552731 "}, {"RefNumber": "822239", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB interfaces", "RefUrl": "/notes/822239 "}, {"RefNumber": "869267", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB Log area", "RefUrl": "/notes/869267 "}, {"RefNumber": "1128916", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB/liveCache heap management", "RefUrl": "/notes/1128916 "}, {"RefNumber": "936058", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB Runtime Environment", "RefUrl": "/notes/936058 "}, {"RefNumber": "1459538", "RefComponent": "BC-DB-LVC", "RefTitle": "Configuration of MaxCPUs in virtual environments", "RefUrl": "/notes/1459538 "}, {"RefNumber": "1139904", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB/liveCache database parameters", "RefUrl": "/notes/1139904 "}, {"RefNumber": "1672994", "RefComponent": "BC-DB-SDB", "RefTitle": "SAP MaxDB: configuration of server tasks", "RefUrl": "/notes/1672994 "}, {"RefNumber": "912905", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: Storage systems used with SAP MaxDB", "RefUrl": "/notes/912905 "}, {"RefNumber": "1294726", "RefComponent": "BC-DB-LVC", "RefTitle": "SAP MaxDB/liveCache crashes during Read Ahead", "RefUrl": "/notes/1294726 "}, {"RefNumber": "993848", "RefComponent": "BC-DB-LVC", "RefTitle": "Direct I/O mount options for liveCache/MaxDB", "RefUrl": "/notes/993848 "}, {"RefNumber": "719652", "RefComponent": "BC-DB-LVC", "RefTitle": "Setting initial parameters for liveCache 7.5, 7.6, 7.7", "RefUrl": "/notes/719652 "}, {"RefNumber": "1013441", "RefComponent": "BC-DB-SDB", "RefTitle": "Upgrade required: Advantages for MaxDB on 64-bit platforms", "RefUrl": "/notes/1013441 "}, {"RefNumber": "757914", "RefComponent": "BC-DB-SDB", "RefTitle": "\"-9807 Task Limit\"", "RefUrl": "/notes/757914 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}