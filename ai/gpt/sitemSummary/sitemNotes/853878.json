{"Request": {"Number": "853878", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 408, "Error": {}, "SAPNote": {"_type": "00200720410000000532", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000004819472017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000853878?language=E&token=4556CF8C565F83F979781FB05C653936"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000853878", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "853878"}, "Type": {"_label": "Type", "value": "SAP Security Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "07.10.2009"}, "SAPComponentKey": {"_label": "Component", "value": "BC-MID-ICF"}, "SAPComponentKeyText": {"_label": "Component", "value": "Internet Communication Framework"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Middleware", "value": "BC-MID", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-MID*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Internet Communication Framework", "value": "BC-MID-ICF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-MID-ICF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "853878 - HTTP WhiteList Check (security)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>A XSS attack is possible by supplying a bogus themeRoot to a HTTP application, for example any BSP application, that will then reference the external themeRoot, without been able to verify that the pointed at theme is actually safe.<br /><br />Problem Description: There are some situations that a framework or application on the ABAP server received information from the outside that it would then potentially use in the next steps of its interaction with the browser. The problem is that this externally received information could actually point to website that is not trusted. For example: we have the following start-up URL:<br /> /myApplication?use-css-file=http://company.portal.com/ourTheme.css<br />Typically, the application \"myApplication\" now takes that string specified for \"use-css\" and render it out in inside the HTML page send to the browser to load this specified style sheet. Thus, in the HTML, we see a sequence similar to:<br /> &lt;html&gt;<br /> &lt;header&gt;<br />  &lt;css src=\" http://company.portal.com/ourTheme.css\"&gt;<br />This will cause the browser to load this alternative CSS file from the company portal.<br /><br />However, CSS files can also contain and execute JavaScript files. Let us assume someone leaves such a start-up URL somewhere in the Internet, or in an email, with the following format:<br /> /myApplication?use-css-file=http://unknown.com/full.JavaScript.css<br />If this application \"myApplication\" now takes that string specified for \"use-css\" and render it out in the HTML to the browser, then the user that clicked on that link will load a CSS file that could potentially execute unknown JavaScript code.<br /><br />How does the application \"myApplication\" know that the value specified for \"use-css\" is actually in a valid range (points to a trusted server or a server in a trusted domain)? For this, this whitelist infrastructure allows the application to verify that the value of \"use-css\" to be in a trusted range.<br /><br />How does this whitelist works? By itself, the whitelist is just a passive infrastructure; it does not do any security relevant work.&#x00A0;&#x00A0;It is just a store where applications or frameworks can check that the received data fits to a set of configured data to be in a valid range. However, the responsibility lies with each application or framework to check each type of parameter that can be set from external and then used to control the rendering to the browser. The application uses the API call to verify that the received data is valid.<br /><br />A similar example would be an exit URL to which the application will navigate after it has completed successfully its work:<br /> /myApplication?exit-target=http://competitor.com/we.are.better.html<br />If such an URL is constructed and left in the Internet, it will cause the application to navigate to unknown targets when it has completed.<br /><br />In all of these scenarios, it is the responsibility of the functionality provider, to validate all received input before it is used. For central framework provided functionality, the framework itself must validate these specific URL parameters to be in a valid range against the whitelist. For BSP as a framework, these parameters which can be set externally (sap-exiturl, sap-themeRoot, style_sheet), are validated before been used.<br /><br />Note: if a BSP application itself also has additional parameters with similar behaviour, it is the responsibility of the application to validate these parameters.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>XSS in CSS Themes</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>It is not possible to verify an externally received themeRoot URL.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>IMPORTANT: A minor correction instruction has been added to this note to add addition security feature by validating that also the URL does not contain any carriage return and/or linefeed sequences. In addition, the exception raise code was completed. For this, the additional Note Assistent corrections can be applied to 620SP54-56, 640SP14 and 700SP05.<br /><br />In 620SP58, 640SP16 and 700SP07: Updated HTTP table to also have a unique sort sequence in the key, so as to allow multiple entries<br />that point to the same host, but with different URLs.<br /><br />A white list infrastructure has now been added the HTTP framework (620SP54, 640SP14 and 700SP03). In this whitelist, it is possible to configure patterns that are matched against URLs from external sources as verification that we wish to accept these URLs.<br /><br />The whitelist is stored in table HTTP_WHITELIST, and can be updated with transaction SE16.<br /><br />Typical examples could be considered for mathing:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;http://myHost.myDomain.myExt:1080/myUrl<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;http://myHost.myDomain.myExt/myUrl&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; (default port 80)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;https://myHost.myDomain.myExt/myUrl&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(default port 443)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;http://myHost.myDomain.myExt:1080&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(default root url)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;http://myHost.myDomain.myExt&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(default root url)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;/myUrl&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(no host data specified)<br /><br />Each record in the table has the following fields:<br /><br />- entry_type: specific the type of URL matched against this entry. Typical example could be a CSS off-box reference.<br />-sort_seq: Makes for unique keys, and does testing according to this sequence.<br />-host: Host name that checked against incoming host. Empty if host name is not to be checked for a specific entry. Wildcard (*) can be used.<br />-protocol: Protocol to be checked. Usually http or https. Can be empty to not check.<br />-port: Port number to check. Must be digits only, leave empty not to check.<br />-url: to check against incoming URL. Wildcard (*) can be used.<br /><br />If a wildcard '*' is specified, it is matched against incoming string. If the field is empty, it is not checked (for this line), for example assume protocol = '' (empty string). This effectively implies that the protocol is not checked. All URLs of the form \"http[s]://...\" or \"/...\" will pass, as it is not checked whether a protocol is set. If protocol = https, then only pass URLs of the form \"https://...\".<br /><br />A typical example, let us assume that we have portal server providing us with URLs pointing to its themes (CSS files to use). At a first level of security one would consider to use a whitelist filter of:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; protocol=*, host=*.myDomain.myExt, port=*, url=<br />This would effectively accept any references to other machines in the same domain.<br /><br />However, let us assume a scenario where we would like to limit this loading from exactly our portal server. Thus we have:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; protocol=*, host=myPortal.myDomain.myExt, port=*, url=<br />But this still opens many different types of holes. One example, let us assume on the same portal server there is also another webserver installed. The portal server is safe, but the other webserver might get compromised. Then these URLs will still pass the whitelist. Thus for paranoid security always consider to specify protocol and port data as well:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;protocol=http,&#x00A0;&#x00A0;host=myPortal.myDomain.myExt, port=1080, url=/*<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;protocol=https, host=myPortal.myDomain.myExt, port=1443, url=/*<br /><br />By default, the whitelist table is empty and no checking is done. It is highly recommended configure this table with the exact data from portal servers in the landscape that will pass references for use.<br /><br />For developers: See function CL_HTTP_UTILITY=&gt;CHECK_HTTP_WHITELIST to use the whitelist. Checks should be done by supply the URL and the entry type against which to check. Important: should the check fails, the exception CX_HTTP_WHITELIST is raised.<br />Constants for entry type:<br />&#x00A0;&#x00A0;CL_HTTP_UTILITY=&gt;HTTP_WHITELIST_EP_CSS_URL<br />&#x00A0;&#x00A0;CL_HTTP_UTILITY=&gt;HTTP_WHITELIST_SAP_EXITURL<br />&#x00A0;&#x00A0;CL_HTTP_UTILITY=&gt;HTTP_WHITELIST_WDA_RESUME_URL<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-BSP (Business Server Pages)"}, {"Key": "Externally Reported", "Value": "Yes"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D027140)"}, {"Key": "Processor                                                                                           ", "Value": "Alma Volk-Gruener (D034348)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000853878/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000853878/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000853878/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000853878/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000853878/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000853878/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000853878/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000853878/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000853878/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "891232", "RefComponent": "BC-BSP", "RefTitle": "BSP Security Relevant Changes", "RefUrl": "/notes/891232"}, {"RefNumber": "887322", "RefComponent": "BC-BSP", "RefTitle": "Whitelist checks of sap-exit URL", "RefUrl": "/notes/887322"}, {"RefNumber": "612670", "RefComponent": "BC-SEC", "RefTitle": "SSO for local BSP calls from SAPGUI HTML control", "RefUrl": "/notes/612670"}, {"RefNumber": "1274793", "RefComponent": "BC-MID-ICF", "RefTitle": "BSP: HTTP Allowlist Check fails for port entries", "RefUrl": "/notes/1274793"}, {"RefNumber": "1261193", "RefComponent": "BC-SEC-AUT", "RefTitle": "Composite SAP Note : HCM Authorizations Documentation", "RefUrl": "/notes/1261193"}, {"RefNumber": "1245560", "RefComponent": "CA-GTF-TS-XSS", "RefTitle": "Composite SAP Note : XSS Documentation", "RefUrl": "/notes/1245560"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2032237", "RefComponent": "BC-CST", "RefTitle": "Using CHECK_HTTP_WHITELIST for server-relative URLs", "RefUrl": "/notes/2032237 "}, {"RefNumber": "1245560", "RefComponent": "CA-GTF-TS-XSS", "RefTitle": "Composite SAP Note : XSS Documentation", "RefUrl": "/notes/1245560 "}, {"RefNumber": "1261193", "RefComponent": "BC-SEC-AUT", "RefTitle": "Composite SAP Note : HCM Authorizations Documentation", "RefUrl": "/notes/1261193 "}, {"RefNumber": "612670", "RefComponent": "BC-SEC", "RefTitle": "SSO for local BSP calls from SAPGUI HTML control", "RefUrl": "/notes/612670 "}, {"RefNumber": "1274793", "RefComponent": "BC-MID-ICF", "RefTitle": "BSP: HTTP Allowlist Check fails for port entries", "RefUrl": "/notes/1274793 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "620", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "710", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62054", "URL": "/supportpackage/SAPKB62054"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62057", "URL": "/supportpackage/SAPKB62057"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62058", "URL": "/supportpackage/SAPKB62058"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64015", "URL": "/supportpackage/SAPKB64015"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64016", "URL": "/supportpackage/SAPKB64016"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70007", "URL": "/supportpackage/SAPKB70007"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70006", "URL": "/supportpackage/SAPKB70006"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70003", "URL": "/supportpackage/SAPKB70003"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 6, "URL": "/corrins/0000853878/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 6, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "CVSS": {"_label": "CVSS", "CVSS_Score": {"_label": "CVSS Score                                        ", "value": "0"}, "CVSS_Vector": {"_label": "CVSS Vector                                       ", "vectorValue": "", "vectorVersion": "3.0", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Attack Vector (AV)", "Value": ""}, {"Key": "Attack Complexity (AC)", "Value": ""}, {"Key": "Privileges Required (PR)", "Value": ""}, {"Key": "User Interaction (UI)", "Value": ""}, {"Key": "<PERSON><PERSON> (S)", "Value": ""}, {"Key": "Confidentiality Impact (C)", "Value": ""}, {"Key": "Integrity Impact (I)", "Value": ""}, {"Key": "Availability Impact (A)", "Value": ""}]}}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}