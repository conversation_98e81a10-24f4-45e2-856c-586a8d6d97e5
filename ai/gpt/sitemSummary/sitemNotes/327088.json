{"Request": {"Number": "327088", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 387, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001355022017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000327088?language=E&token=71378FC8584EB5AEA77E3804DAF44D73"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000327088", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000327088/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "327088"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 31}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.01.2012"}, "SAPComponentKey": {"_label": "Component", "value": "FI-AA-AA-C"}, "SAPComponentKeyText": {"_label": "Component", "value": "Transactions"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Asset Accounting", "value": "FI-AA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "FI-AA-AA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AA-AA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Transactions", "value": "FI-AA-AA-C", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AA-AA-C*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "327088 - Definition of the delivered transfer variants"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note describes the transfer variants delivered by SAP as of Release 4.6C and the most important Customizing settings of the transaction types used with them.<br />Note that the delivered transaction types should only be changed by an experienced consultant. You can find more information under point&#x00A0;&#x00A0;5 'Adjusting the available transaction types' in the 'Solution' section of this note.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>TABW, TABWG, BWASL, BWAGRP, post, ABT1</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. Definition of the transfer variants</OL> <p><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As of Release 4. 0A, the following transfer variants have been delivered: <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Transfer variant 1 - Gross transfer<br />Transfer variant 2 - Net transfer<br />Transfer variant 3 - Transfer with revaluation <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As of Release 4. 6A, the following transfer variant was delivered in addition, which, however, has not been created for the cross-company code transfer but internally for the intracompany transfer: <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Transfer variant 4 - Transfer (Transaction ABUMN) <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As of this release, there are also transfer variants 5 and 6. These variants are required for settling AuC to active assets. They should never be changed because the transfer variant cannot be overridden manually in the settlement transactions. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As of Release 4. 6C SRII (Support Package 15 and higher), SAP delivers the two new tansfer variants 7 und 8. These variants are also delivered in Release 4.6B via Hot Package (&gt; Support Package unspecified &gt; May 2001): <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Transfer variant 7 - Gross variant (affiliated)<br />Transfer variant 8 - Gross variant (non-affiliated) <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;These variants differ from variant 1 by the fact that they allow a transfer during the fiscal year (in other words, with a reference date other than the first day of the fiscal year). The new acquisition transaction types in these variants belong to transaction type groups that are marked as \"retirement\" instead of \"acquisition\" in period control, so that the same period control is used for acquisition and retirement. There is one variant each for transfers with affiliated companies and for transfers without affiliated companies. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<p></p> <OL><OL>a) Transfer variant 1 - Gross transfer<br /></OL></OL> <p>Rel.type&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; TransMeth. Retmt tr.type&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Acq.trans.type<br />--------------------------------------------------------------------<br />|&#x00A0;&#x00A0;&#x00A0;&#x00A0;1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 230&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 153&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br />|&#x00A0;&#x00A0;&#x00A0;&#x00A0;2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 300&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;310&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br />--------------------------------------------------------------------<br /></p> <OL><OL>b) Transfer variant 2 - Net transfer</OL></OL> <p><br /> Rel.type&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; TransMeth. Retmt tr.type&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Acq.trans.type<br /> --------------------------------------------------------------------<br /> |&#x00A0;&#x00A0;&#x00A0;&#x00A0;1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 2&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 230&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 157&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br /> |&#x00A0;&#x00A0;&#x00A0;&#x00A0;2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 300&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;310&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br /> --------------------------------------------------------------------<br /></p> <OL><OL>c) Transfer variant 3 - Transfer with revaluation</OL></OL> <p><br /> Rel.type&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; TransMeth. Retmt tr.type&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Acq.trans.type<br /> --------------------------------------------------------------------<br /> |&#x00A0;&#x00A0;&#x00A0;&#x00A0;1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 3&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 230&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 157&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br /> |&#x00A0;&#x00A0;&#x00A0;&#x00A0;2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 300&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;310&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br /> --------------------------------------------------------------------<br /></p> <OL><OL>d) Transfer variant 4 - Transfer (Transaction ABUMN)</OL></OL> <p><br /> Rel.type&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; TransMeth. Retmt tr.type&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Acq.trans.type<br /> --------------------------------------------------------------------<br /> |&#x00A0;&#x00A0;&#x00A0;&#x00A0;2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 4&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 300&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;310&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br /> --------------------------------------------------------------------<br /><br />Transfer variant 7 - Gross variant (affiliated)<br /><br /> Rel.type&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; TransMeth. Retmt tr.type&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Acq.trans.type<br /> --------------------------------------------------------------------<br /> |&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 230&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 145&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br /> |&#x00A0;&#x00A0;&#x00A0;&#x00A0; 2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 300&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 310&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br /> --------------------------------------------------------------------<br /><br />Transfer variant 8 - Gross variant (non-affiliated)<br /><br /> Rel.type&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; TransMeth. Retmt tr.type&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Acq.trans.type<br /> --------------------------------------------------------------------<br /> |&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 210&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 147&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br /> |&#x00A0;&#x00A0;&#x00A0;&#x00A0; 2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 300&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 310&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br /> --------------------------------------------------------------------<br /></p> <OL>2. Dealing with current-year acquisitions and prior-year acquisitions as well as affiliated companies:</OL> <p></p> <OL><OL>a) Affiliated companies:</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The standard transfer variants have been created for transfers between affiliated companies and only contain prior-year acquisition transaction types. However, implicitly, the prior-year acquisitions transaction types also contain transaction types for the transfer of current-year acquisitions. <p><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Current-year acquisitions:<br />This transaction type is required if both prior-year acquisitions and acquisitions of the current fiscal year are managed for a fixed asset, and if you want to post a complete retirement or a complete transfer. The system then uses this transaction type for postings concerning the newly acquired asset part. <p><br />|---------------------------------------------------------------------<br />|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|Prior-year acq.|Current-year acq.|Transfer variant&#x00A0;&#x00A0;|<br />|---------------------------------------------------------------------<br />|&#x00A0;&#x00A0;Gross&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;153 (19)&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;150 (14)&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|<br />|---------------------------------------------------------------------<br />|&#x00A0;&#x00A0;Net&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;157 (17)&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;151 (10)&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br />| Revaluation&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0;157 (17)&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;151 (10)&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 3&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br />|---------------------------------------------------------------------<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(The relevant transaction type groups are written in brackets.) <p></p> <OL><OL>b) Non-affiliated companies:</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Copy the standard tranfer variants and enter the prior-year acquisition transaction types listed below into the new transfer variants. <p><br />|---------------------------------------------------------------------<br />|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|Prior-year acq.|Current-year acq.|Transfer variant&#x00A0;&#x00A0;|<br />|---------------------------------------------------------------------<br />|&#x00A0;&#x00A0;Gross&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;158 (19)&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;154 (14)&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Z1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br />|---------------------------------------------------------------------<br />|&#x00A0;&#x00A0;Net&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;156 (17)&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;155 (10)&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Z2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br />| Revaluation&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0;156 (17)&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;155 (10)&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Z3 &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|<br />|---------------------------------------------------------------------<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(The relevant transaction type groups are written in brackets.) <p></p> <OL>3. Definition of the transaction types:</OL> <p><br /></p> <OL><OL>a) Definition of the acquisition transaction types:<br />--------------------------------------<br />150 Gross interco.transf.acq. curr-yr.acq. affil.co.<br />151 Net interco.transf.acq. curr-yr.acq. frm affil.co.<br />153 Gross interco.transf.acquis. prior-yr frm affil.co<br />154 Gross interco.transf.acquis. of current-yr acquis.<br />155 Net interco.transf.acquis. of current-yr acquis.<br />156 Net interco.transf.acquis. of prior-yr acquis.<br />157 Net interco.transf.acquis. prior-yr acq. affil.co.<br />158 Gross interco.transf. acquis. of prior-yr acquis.<br />145 Gross interco.transf.acquis. prior-yr frm affil.co<br />146 Gross interco.transf.acq. curr-yr.acq. affil.co.<br />147 Gross interco.transf. acquis. of prior-yr acquis.<br />148 Gross interco.transf.acquis. of current-yr acquis.</OL></OL> <p><br />-----------------------------------------------------------------------<br />Transaction type&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;| 158 | 157 | 156 | 155 | 154 | 153 | 151 | 150 |<br />-----------------------------------------------------------------------<br />Transaction type grp&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;19 |&#x00A0;&#x00A0;17 |&#x00A0;&#x00A0;17 |&#x00A0;&#x00A0;10 |&#x00A0;&#x00A0;14 |&#x00A0;&#x00A0;19 |&#x00A0;&#x00A0;10 | 14 |<br />Past transact.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; | &#x00A0;&#x00A0;&#x00A0;&#x00A0;|<br />Capitalize fixed asset|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|<br />Affiliated&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;| X&#x00A0;&#x00A0;|<br />Cons. Transact. type&#x00A0;&#x00A0;| 120 | 125 | 120 | 120 | 120 | 125 | 125 | 125 |<br />Debit/credit indicator&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;S&#x00A0;&#x00A0;|&#x00A0;&#x00A0;S&#x00A0;&#x00A0;|&#x00A0;&#x00A0;S&#x00A0;&#x00A0;|&#x00A0;&#x00A0;S&#x00A0;&#x00A0;|&#x00A0;&#x00A0;S&#x00A0;&#x00A0;|&#x00A0;&#x00A0;S&#x00A0;&#x00A0;|&#x00A0;&#x00A0;S&#x00A0;&#x00A0;|  S&#x00A0;&#x00A0;|<br />Offset transactn type&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; | &#x00A0;&#x00A0;&#x00A0;&#x00A0;|<br />Document type&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;AA |&#x00A0;&#x00A0;AA |&#x00A0;&#x00A0;AA |&#x00A0;&#x00A0;AA |&#x00A0;&#x00A0;AA |&#x00A0;&#x00A0;AA |&#x00A0;&#x00A0;AA |&#x00A0;&#x00A0;AA |<br />Acquisition in same yr | 154 | 151 | 155 |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; | 150 |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br />History sheet group|&#x00A0;&#x00A0;10 |&#x00A0;&#x00A0;10 |&#x00A0;&#x00A0;10 |&#x00A0;&#x00A0;10 |&#x00A0;&#x00A0;10 |&#x00A0;&#x00A0;10 |&#x00A0;&#x00A0;10 |&#x00A0;&#x00A0;10 |<br />Relevant to budget&#x00A0;&#x00A0; |&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0; X |&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|<br />-----------------------------------------------------------------------<br /><br />-----------------------------------------------<br />Transaction type&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;| 145 | 146 | 147 | 148 |<br />-----------------------------------------------<br />Transaction type grp&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;42 |&#x00A0;&#x00A0;41 |&#x00A0;&#x00A0;42 |&#x00A0;&#x00A0;41 |<br />Past transaction&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br />Capitalize fixed asset|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|<br />Affil.co.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br />Cons. Transact. type&#x00A0;&#x00A0;| 125 | 125 | 125 | 125 |<br />Debit/credit indicator&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;S&#x00A0;&#x00A0;|&#x00A0;&#x00A0;S&#x00A0;&#x00A0;|&#x00A0;&#x00A0;S&#x00A0;&#x00A0;|&#x00A0;&#x00A0;S&#x00A0;&#x00A0;|<br />Offset transactn type&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br />Document type&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;AA |&#x00A0;&#x00A0;AA ||&#x00A0;&#x00A0;AA |&#x00A0;&#x00A0;AA |<br />Acquisition in same yr | 146 |&#x00A0;&#x00A0;&#x00A0;&#x00A0; | 148 |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br />History sheet grp&#x00A0;&#x00A0; |&#x00A0;&#x00A0;10 |&#x00A0;&#x00A0;10 |&#x00A0;&#x00A0;10 |&#x00A0;&#x00A0;10 |<br />Relevant to budget&#x00A0;&#x00A0; |&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0; X |&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|<br /><br /><br /></p> <OL><OL>b) Definition of the retirement and transfer transaction types:<br />-------------------------------------------------------<br /><br />230 Retirement to affiliated company with revenue<br />275 Retmt. of curr-yr acquis. to affil. co. w/ revenue<br />------------------------------------------------------<br />300 Retirmt transfer of prior-yr acquis. frm cap.asset<br />310 Acquirg transfer of prior-yr acquis. frm cap.asset<br />320 Retirmt transfer of curr-yr acquis.<br />330 Acquiring transfer of curr-yr acquis.</OL></OL> <p><br />|-----------------------------------||-----------|-----------|<br />| Transaction type&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;| 230 | 275 || 300 | 310 | 320 | 330 |<br />|-----------------------------------||-----------|-----------|<br />| Transaction type group&#x00A0;&#x00A0; |&#x00A0;&#x00A0;20 |&#x00A0;&#x00A0;25 ||&#x00A0;&#x00A0;30 |&#x00A0;&#x00A0;31 |&#x00A0;&#x00A0;32 |&#x00A0;&#x00A0;33 |<br />| Past transaction&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;||&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br />|Capitalize fixed asset|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;||&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|<br />|Deactivate fixed asset|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;||&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br />| Post aut. gain/loss&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;||&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br />|Retirement w revenue |&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;||&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br />| Affil. co.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;||&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br />| Cons. transaction type| 145 | 145 || 170 | 170 | 120 | 120 |<br />| Debit/credit indicator&#x00A0;&#x00A0;|&#x00A0;&#x00A0;H&#x00A0;&#x00A0;|&#x00A0;&#x00A0;H&#x00A0;&#x00A0;||&#x00A0;&#x00A0;H&#x00A0;&#x00A0;|&#x00A0;&#x00A0;S&#x00A0;&#x00A0;|&#x00A0;&#x00A0;H&#x00A0;&#x00A0;|&#x00A0;&#x00A0;S&#x00A0;&#x00A0;|<br />| Offset transaction type&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; || 310 | 300 | 330 | 320 |<br />| Document type&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;AA |&#x00A0;&#x00A0;AA ||&#x00A0;&#x00A0;AA |&#x00A0;&#x00A0;AA |&#x00A0;&#x00A0;AA |&#x00A0;&#x00A0;AA |<br />| Repay inv.supp.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;||&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br />| Acquisition in same yr&#x00A0;&#x00A0;| 275 |&#x00A0;&#x00A0;&#x00A0;&#x00A0; || 320 | 330 |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br />| History sheet group|&#x00A0;&#x00A0;20 |&#x00A0;&#x00A0;25 ||&#x00A0;&#x00A0;30 |&#x00A0;&#x00A0;31 |&#x00A0;&#x00A0;32 |&#x00A0;&#x00A0;33 |<br />| Adopting dep.start date&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;||&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|<br />| Relevant to budget&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;||&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|<br />|-----------------------------------||-----------------------|<br /><br /></p> <OL>4. Transaction type groups employed:<br />-------------------------------<br />19 Acquis. of prior-yr acquis.- gross<br />17 Acquis. of prior-yr acquis.- net<br />14 Current-year acquis.- gross<br />10 Current-year acquis.- net<br />41 Acquis. (Transfer of transfer period control)<br />42 Acquis. - gross (Transfer of transfer period control)<br />-------------------------------<br />25 Retirement of curr-yr acquisition<br />20 Retirement<br />-------------------------------<br />33 Acquiring transfer of curr-yr acquis.<br />32 Retirmt transfer of curr-yr acquis.<br />31 Acquiring transfer of prior-yr acquis.<br />30 Retirmt transfer of prior-yr acquis.<br />-------------------------------</OL> <p><br />-----------------------------------------------|-----------------|----|<br />Trans.group |&#x00A0;&#x00A0;19 |&#x00A0;&#x00A0;17 |&#x00A0;&#x00A0;14 |&#x00A0;&#x00A0;10 |&#x00A0;&#x00A0;25 |&#x00A0;&#x00A0;20 | 33&#x00A0;&#x00A0;|&#x00A0;&#x00A0;32 |&#x00A0;&#x00A0;31 | 30 |<br />-----------------------------------------------|----------------------|<br />Period grp&#x00A0;&#x00A0;|&#x00A0;&#x00A0;1&#x00A0;&#x00A0;|&#x00A0;&#x00A0;1&#x00A0;&#x00A0;|&#x00A0;&#x00A0;1&#x00A0;&#x00A0;|&#x00A0;&#x00A0;1&#x00A0;&#x00A0;|&#x00A0;&#x00A0;3&#x00A0;&#x00A0;|&#x00A0;&#x00A0;3&#x00A0;&#x00A0;|&#x00A0;&#x00A0;4&#x00A0;&#x00A0;|&#x00A0;&#x00A0;4&#x00A0;&#x00A0;|&#x00A0;&#x00A0;4&#x00A0;&#x00A0;|&#x00A0;&#x00A0;4 |<br />FYtotal sg&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;+&#x00A0;&#x00A0;|&#x00A0;&#x00A0;+&#x00A0;&#x00A0;|&#x00A0;&#x00A0;+&#x00A0;&#x00A0;|&#x00A0;&#x00A0;+&#x00A0;&#x00A0;|&#x00A0;&#x00A0;*&#x00A0;&#x00A0;|&#x00A0;&#x00A0;*&#x00A0;&#x00A0;|&#x00A0;&#x00A0;*&#x00A0;&#x00A0;|&#x00A0;&#x00A0;*&#x00A0;&#x00A0;|&#x00A0;&#x00A0;*&#x00A0;&#x00A0;| * |<br />Val.field&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; | AHK |&#x00A0;&#x00A0;&#x00A0;&#x00A0; | AHK | AHK | AHK | AHK | AHK | AHK |AHK |<br />Cur.yr.acq&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0; | &#x00A0;&#x00A0; |<br />SetAcqDat&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|<br />Prop.vals.&#x00A0;&#x00A0; |&#x00A0;&#x00A0;1&#x00A0;&#x00A0;|&#x00A0;&#x00A0;0&#x00A0;&#x00A0;|&#x00A0;&#x00A0;1&#x00A0;&#x00A0;|&#x00A0;&#x00A0;0&#x00A0;&#x00A0;|&#x00A0;&#x00A0;2&#x00A0;&#x00A0;|&#x00A0;&#x00A0;2&#x00A0;&#x00A0;|&#x00A0;&#x00A0;1&#x00A0;&#x00A0;|&#x00A0;&#x00A0;2&#x00A0;&#x00A0;|&#x00A0;&#x00A0;1&#x00A0;&#x00A0;|&#x00A0;&#x00A0;2 |<br />Class&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;1&#x00A0;&#x00A0;|&#x00A0;&#x00A0;1&#x00A0;&#x00A0;|&#x00A0;&#x00A0;1&#x00A0;&#x00A0;|&#x00A0;&#x00A0;1&#x00A0;&#x00A0;|&#x00A0;&#x00A0;2&#x00A0;&#x00A0;|&#x00A0;&#x00A0;2&#x00A0;&#x00A0;|&#x00A0;&#x00A0;3&#x00A0;&#x00A0;|&#x00A0;&#x00A0;4&#x00A0;&#x00A0;|&#x00A0;&#x00A0;3&#x00A0;&#x00A0;|&#x00A0;&#x00A0;4 |<br />Not used&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |ANSWL|&#x00A0;&#x00A0;&#x00A0;&#x00A0; |ANSWL|ANSWL|ANSWL|ANSWL|ANSWL|ANSWL|ANSWL<br />ClassCheck|&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0;|<br />Type&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;1&#x00A0;&#x00A0;|&#x00A0;&#x00A0;1&#x00A0;&#x00A0;|&#x00A0;&#x00A0;1&#x00A0;&#x00A0;|&#x00A0;&#x00A0;1&#x00A0;&#x00A0;|&#x00A0;&#x00A0;1&#x00A0;&#x00A0;|&#x00A0;&#x00A0;1&#x00A0;&#x00A0;|&#x00A0;&#x00A0;1&#x00A0;&#x00A0;|&#x00A0;&#x00A0;1&#x00A0;&#x00A0;|&#x00A0;&#x00A0;1&#x00A0;&#x00A0;|&#x00A0;&#x00A0;1 |<br />Ord depr.&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; | &#x00A0;&#x00A0; |<br />Spec. dep.&#x00A0;&#x00A0;|&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0;|<br />Unplnd.dep |&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0;|<br />Transf.Res&#x00A0;&#x00A0; |&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;X&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0; | &#x00A0;&#x00A0; |<br />-----------------------------------------------|-----------------|----|<br /><br />Transaction type groups 41 and 42 only differ from transaction type groups 14 and 19 with regard to the periodcontrol. These new groups use period control \"Retirement\" instead of \"Acquisition\".<br /></p> <OL>5. Adjusting the available transaction types:</OL> <OL><OL>a) To enter a new acquisition transaction type into an existing prior-year acquisition transaction type, use the correction program ZACORR66 from Note 173523 in releases prior to Release 4.6C.</OL></OL> <OL><OL>b) The transaction types existing in your system are not overwritten during the release upgrade. You can copy the current definition of the transaction types from client 000 to your productive client (see also Note 310346).</OL></OL> <OL><OL>c) Transaction type 157 and the respective transaction type group 17 can be created or modified with the attached correction program ZACORR124.</OL></OL> <OL><OL>d) Transaction type 156 can be generated with the correction program ZACORR126, or the transaction type group can be set to 17.</OL></OL> <OL><OL>e) You can import transaction types 150 to 159 and transaction type groups 17 to 19 into your system from the SAP service system sapserv3.</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Copy the files for this note from sapserv3 in the directory ~ftp/general/R3server/abap/note.0327088 and import them into client 000 of your system. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For more information regarding the procedure for preliminary transports to customers refer to Note 13719. <p></p> <OL>6. Transfer method</OL> <p>The transfer method must be specified in the transfer variant.<br />This is particularly important if fixed assets are transferred<br />in which the sending asset has fewer depreciation areas than the receiving asset.<br />To ensure that all depreciation areas for the receiving asset are supplied with values, you should select a transfer method with the adoption of values in dependent depreciation areas. This means that, for example, for transfer variant 1 (gross variant), transfer method 4 \"Gross method with transfer of values to dependent areas\" should be selected. When this transfer method is used, depreciation areas that do not exist for the sending asset are also supplied with values if these areas have a corresponding parameter for the adoption of values.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "MODE"}, {"Key": "Transaction codes", "Value": "ABT1"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D020849)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D021319)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000327088/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000327088/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000327088/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000327088/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000327088/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000327088/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000327088/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000327088/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000327088/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "560915", "RefComponent": "FI-AA-AA-C", "RefTitle": "Net transfer: Depreciation on target asset", "RefUrl": "/notes/560915"}, {"RefNumber": "556717", "RefComponent": "FI-AA-AA-C", "RefTitle": "Intracomp transfer/transfer: spec depreciatn not transfrd", "RefUrl": "/notes/556717"}, {"RefNumber": "45967", "RefComponent": "FI-AA-AA-C", "RefTitle": "Gross acquisition", "RefUrl": "/notes/45967"}, {"RefNumber": "403060", "RefComponent": "FI-AA-AA-C", "RefTitle": "Transfer Customizing reports errors without changes", "RefUrl": "/notes/403060"}, {"RefNumber": "366298", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/366298"}, {"RefNumber": "339528", "RefComponent": "FI-AA-AA-C", "RefTitle": "Transfer gross: depreciation to target asset", "RefUrl": "/notes/339528"}, {"RefNumber": "310346", "RefComponent": "FI-AA-AA-C", "RefTitle": "ABT1 Defining transfer variant gross", "RefUrl": "/notes/310346"}, {"RefNumber": "300273", "RefComponent": "CO-OM-OPA-F", "RefTitle": "Settlmt AuC: various errors due to transfer variant", "RefUrl": "/notes/300273"}, {"RefNumber": "173523", "RefComponent": "FI-AA-AA-C", "RefTitle": "ABT1 Update termination", "RefUrl": "/notes/173523"}, {"RefNumber": "157957", "RefComponent": "FI-AA-AA-C", "RefTitle": "AA641 for complete retirement after transfer gross", "RefUrl": "/notes/157957"}, {"RefNumber": "139899", "RefComponent": "FI-AA-AA-B", "RefTitle": "AA629 when posting MR01/MRHR/MIRO invoice receipt", "RefUrl": "/notes/139899"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "122439", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/122439"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1846281", "RefComponent": "FI-AA-AA-C", "RefTitle": "During settlement/transfer some depreciation areas are not updated", "RefUrl": "/notes/1846281 "}, {"RefNumber": "2207525", "RefComponent": "FI-AA", "RefTitle": "Unexpected results by FI-AA transfer (ABUMN or ABT1N)", "RefUrl": "/notes/2207525 "}, {"RefNumber": "2968092", "RefComponent": "FI-AA-AA-C", "RefTitle": "Useful life cannot set as 0 for the new receiver asset created during transfer posting", "RefUrl": "/notes/2968092 "}, {"RefNumber": "3311379", "RefComponent": "FI-AA-IS", "RefTitle": "Derived depreciation area is not showing in some assets.", "RefUrl": "/notes/3311379 "}, {"RefNumber": "3061966", "RefComponent": "FI-AA-AA-C", "RefTitle": "Error AAPO118 when posting intercompany asset transfer via ABT1N", "RefUrl": "/notes/3061966 "}, {"RefNumber": "2982331", "RefComponent": "FI-AA-AA-C", "RefTitle": "AAPO118 Post Transfer (Across Company Codes within same country)", "RefUrl": "/notes/2982331 "}, {"RefNumber": "2014386", "RefComponent": "FI-AA-AA-C", "RefTitle": "Error AC616 during the intercompany asset transfer ABT1N", "RefUrl": "/notes/2014386 "}, {"RefNumber": "2867935", "RefComponent": "FI-AA-AA-C", "RefTitle": "ABUMN: error AA450 You cannot post with transaction type 3XX", "RefUrl": "/notes/2867935 "}, {"RefNumber": "1921298", "RefComponent": "FI-AA-AA-C", "RefTitle": "Error message AA416/AA417 occurred in partially transfer with \"Amount posted\" in T-code: ABUMN", "RefUrl": "/notes/1921298 "}, {"RefNumber": "2022044", "RefComponent": "FI-AA-AA-B", "RefTitle": "ABT1N: Intercompany transfer with cross-system depreciation areas (book and group/parallel area)", "RefUrl": "/notes/2022044 "}, {"RefNumber": "1849333", "RefComponent": "FI-AA-AA", "RefTitle": "Copy of Field Group 18 (Deactiv. area) during a Transfer (field ANLB-XAFBE filled?)", "RefUrl": "/notes/1849333 "}, {"RefNumber": "300273", "RefComponent": "CO-OM-OPA-F", "RefTitle": "Settlmt AuC: various errors due to transfer variant", "RefUrl": "/notes/300273 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "173523", "RefComponent": "FI-AA-AA-C", "RefTitle": "ABT1 Update termination", "RefUrl": "/notes/173523 "}, {"RefNumber": "157957", "RefComponent": "FI-AA-AA-C", "RefTitle": "AA641 for complete retirement after transfer gross", "RefUrl": "/notes/157957 "}, {"RefNumber": "139899", "RefComponent": "FI-AA-AA-B", "RefTitle": "AA629 when posting MR01/MRHR/MIRO invoice receipt", "RefUrl": "/notes/139899 "}, {"RefNumber": "556717", "RefComponent": "FI-AA-AA-C", "RefTitle": "Intracomp transfer/transfer: spec depreciatn not transfrd", "RefUrl": "/notes/556717 "}, {"RefNumber": "560915", "RefComponent": "FI-AA-AA-C", "RefTitle": "Net transfer: Depreciation on target asset", "RefUrl": "/notes/560915 "}, {"RefNumber": "339528", "RefComponent": "FI-AA-AA-C", "RefTitle": "Transfer gross: depreciation to target asset", "RefUrl": "/notes/339528 "}, {"RefNumber": "403060", "RefComponent": "FI-AA-AA-C", "RefTitle": "Transfer Customizing reports errors without changes", "RefUrl": "/notes/403060 "}, {"RefNumber": "310346", "RefComponent": "FI-AA-AA-C", "RefTitle": "ABT1 Defining transfer variant gross", "RefUrl": "/notes/310346 "}, {"RefNumber": "45967", "RefComponent": "FI-AA-AA-C", "RefTitle": "Gross acquisition", "RefUrl": "/notes/45967 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46D", "To": "46D", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C36", "URL": "/supportpackage/SAPKH46C36"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 1, "URL": "/corrins/0000327088/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}