{"Request": {"Number": "1739631", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 248, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000010336432017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001739631?language=E&token=7A502E3E0138D2E3B87101D167A9FD11"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001739631", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001739631/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1739631"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "25.01.2013"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SVD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Service Delivery (and Planning)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Delivery (and Planning)", "value": "SV-SMG-SVD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SVD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1739631 - Service delivery session information not updated accordingly"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You are using Service Delivery functionality in SAP Solution Manager. If you found nothing is synchronized from CRM side to Solution Manager when you have updated session data like Planned date, System ID, Installation number at CRM side and resend it to Solution Manager sucessfully (You should get a message about whether you get new serviceplan data or not). At same time, SDCCN is also not synchronized.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>DSWP, SOLMAN_WORKCENTER, Service Session, Service Delivery, RDSWPCISERVICEPLAN</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is due to a program error</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Implement the attached corrections to your SAP Solution Manager system by using the Note Assistant (transaction SNOTE), or import the relevant Support Package.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I068523)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I071214)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001739631/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001739631/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001739631/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001739631/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001739631/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001739631/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001739631/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001739631/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001739631/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1860939", "RefComponent": "SV-SMG-OP", "RefTitle": "Alert based delivery: Wrong status of final notification", "RefUrl": "/notes/1860939"}, {"RefNumber": "1775242", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.1 SP8 - Basic functions", "RefUrl": "/notes/1775242"}, {"RefNumber": "1734341", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.1 SP7 - Basic functions", "RefUrl": "/notes/1734341"}, {"RefNumber": "1722332", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.1 SP6 - Basic functions", "RefUrl": "/notes/1722332"}, {"RefNumber": "1652693", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.1 Support Package 05 - Basic Functionality", "RefUrl": "/notes/1652693"}, {"RefNumber": "1635809", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.0 SP28 and higher - basic functions", "RefUrl": "/notes/1635809"}, {"RefNumber": "1633725", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.1 SP4 - basic functions", "RefUrl": "/notes/1633725"}, {"RefNumber": "1468591", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager - basic functions SP25 - SP27", "RefUrl": "/notes/1468591"}, {"RefNumber": "1149742", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP (CQC) Going Live Support", "RefUrl": "/notes/1149742"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1775242", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.1 SP8 - Basic functions", "RefUrl": "/notes/1775242 "}, {"RefNumber": "1635809", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.0 SP28 and higher - basic functions", "RefUrl": "/notes/1635809 "}, {"RefNumber": "1633725", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.1 SP4 - basic functions", "RefUrl": "/notes/1633725 "}, {"RefNumber": "1149742", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP (CQC) Going Live Support", "RefUrl": "/notes/1149742 "}, {"RefNumber": "1652693", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.1 Support Package 05 - Basic Functionality", "RefUrl": "/notes/1652693 "}, {"RefNumber": "1722332", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.1 SP6 - Basic functions", "RefUrl": "/notes/1722332 "}, {"RefNumber": "1734341", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.1 SP7 - Basic functions", "RefUrl": "/notes/1734341 "}, {"RefNumber": "1860939", "RefComponent": "SV-SMG-OP", "RefTitle": "Alert based delivery: Wrong status of final notification", "RefUrl": "/notes/1860939 "}, {"RefNumber": "1468591", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager - basic functions SP25 - SP27", "RefUrl": "/notes/1468591 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST", "From": "400", "To": "400", "Subsequent": ""}, {"SoftwareComponent": "ST", "From": "710", "To": "710", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "ST 400", "SupportPackage": "SAPKITL440", "URL": "/supportpackage/SAPKITL440"}, {"SoftwareComponentVersion": "ST 400", "SupportPackage": "SAPKITL441", "URL": "/supportpackage/SAPKITL441"}, {"SoftwareComponentVersion": "ST 710", "SupportPackage": "SAPKITL709", "URL": "/supportpackage/SAPKITL709"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "ST", "NumberOfCorrin": 11, "URL": "/corrins/0001739631/162"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 11, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1739631 ", "URL": "/notes/1739631 ", "Title": "Service delivery session information not updated accordingly", "Component": "SV-SMG-SVD"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}