{"Request": {"Number": "797124", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 213, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015805632017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000797124?language=E&token=D56C6A06D1C3DB571CC8C3181966A7E4"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000797124", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000797124/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "797124"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 33}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.10.2023"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-NET"}, "SAPComponentKeyText": {"_label": "Component", "value": "Network connection"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Network connection", "value": "XX-SER-NET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-NET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "797124 - LOP - Semi Automatic Line Opener Program"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The Semi Automatic&#160;Line Opener Program (LOP) enables SAP to open service connections to your systems independently but in a controlled way. Installation of the LOP is optional but it can help to reduce the problem resolution time significantly. SAP employees requiring access to your systems can create opening requests, provided that you explicitely gave SAP permission to do so for the systems in question. The LOP checks periodically via&#160;https requests&#160;against the SAP Service&#160;Backbone&#160;for such&#160;opening request. If requests&#160;are found, the LOP temporary opens the connection between your SAProuter and SAP and you can choose to be notified by e-mail that the network connection to a dedicated system has been made available.&#160;<strong>This can take up to 10 minutes in some cases before the support engineer sees an open system.</strong></p>\r\n<p>LOP functionality was directly integrated into the Solution Manager with 7.10 SP10 in Q4 2013. For those unable to use the Solution Manager based approach LOP is also available as Java application, replacing the previous standalone program which runs on Windows platforms only.</p>\r\n<p>SAP recommends to coordinate in advance the use of the Semi-Automatic Opening procedure (SAO) and/or the Semi-Automatic Line Opener Program (LOP) in your company with the responsible security or cybersecurity department. Since the use of the SAO procedure and/or the LOP might deprives you of the possibility of the case by case control/release of remote connections to your systems.</p>\r\n<p>We recommend to use the LOP functionality within Solution Manager - a documentation can be found attached to this SAP Note. This will cover better operational aspects such as monitoring, automatic start during system initialization, more scalability, platform independency and more customizing options.<br />With 7.10 SP13&#160;we introduced an improved version of LOP. If you run on SP12 or earlier, please see SAP note 1989343 which offers a bugfix for error handling and SAP note 2076207 for the latest version that is part of SP13 onwards. 7.20 already contains these improvements.</p>\r\n<p><strong>&#65279;&#65279;CHANGE:</strong> The LOP behavior change on <strong>15./16. February 2020</strong> will mainly be targeting SAP cloud and connectivity between customer and SAP. The amendments concern technical connectivity methods, which do not require a manual opening like it&#8217;s necessary for VPN connectivity for example. Please find additional information in the attached PDF called SAO-LOP-behavior-change.pdf.</p>\r\n<p><strong>CHANGE December 2020:</strong>&#160;Due to shutdown of old OSS/O7P infrastructure (oss001.wdf.sap.corp), users of the standalone version of the LOP are required to update the Java application. Users of the Solution Manager report MUST NOT use the \"Use RFCPING to SAPOSS destination instead of NIPING to route\" function as this no longer will work (SAPOSS destination has been fully faded out and is not usable anymore). Further information can be found here:&#160;<a target=\"_blank\" href=\"https://support.sap.com/remote-support/saprouter/lop.html\">https://support.sap.com/remote-support/saprouter/lop.html</a></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Semi Automatic Line Opener Program, LOP</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><br /><strong>If you want to use the standalone version of the LOP (Semi Automatic Line Opener Program) please perform the following steps:</strong></p>\r\n<p><br />Steps to install LOP</p>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"https://support.sap.com/remote-support/saprouter/lop.html\">https://support.sap.com/remote-support/saprouter/lop.html</a></li>\r\n</ul>\r\n<p><br />In order to communicate with the Service&#160;Backbone the LOP needs to authenticate itself. This can be done by using valid user credentials (S-User-Id and password).</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><br /><strong>Known problems and solutions</strong><br /><br /></p>\r\n<p><strong>[Problem]</strong><br />When you tried to&#160;register LOP, you received an error 'Sorry, you're not authorized to register a Line Opener Program (LOP) for this system's SAPRouter because the SAPRouter belongs to another customer'.<br /><br /><strong>[Cause]</strong><br />Your SAProuter does not belong to your customer number and therefore you have no authorization to&#160;register LOP.<br /><br /><strong>[Solution]</strong><br />In the screen 'Service Connections - System ***' displayed by clicking a System ID, click the 'Show System' button at the bottom of the screen. Of course you can also display it in the System Data application <a target=\"_blank\" href=\"https://support.sap.com/system-data\">https://support.sap.com/system-data</a>.<br /><br />At the button of the 'System', you find the SAP-Router data with the customer number. It must be your customer number.<br /><br />If not, establish your own SAProuter and send a Customer message with the component XX-SER-NET-NEW. Only after the new entry is maintained by SAP Network Team, you can update the System data in <a target=\"_blank\" href=\"https://support.sap.com/system-data\">https://support.sap.com/system-data</a>.<br /><br /></p>\r\n<p><br /><strong>[Problem]</strong><br />You tried to install LOP for Windows platforms but the installation process fails.<br /><br /><strong>[Cause]</strong><br />LOP for Windows platforms&#160;is no longer supported.<br /><br /><strong>[Solution]</strong><br />Switch to the latest Java based LOP.</p>\r\n<p><strong>[Problem/Cause]</strong></p>\r\n<p>You are using NAT outgoing of your network and mapped SAPServ to another IP address. The LOP fails as it tries to connect to the public IP of the corresponding SAPServ. The information comes from the webservice it calls, joining the configured local routestring with the public IP of the SAPServ.</p>\r\n<p><strong>[Solution]</strong><br />Modify the local routestring in the LOP configuration on SAP for Me to include your internal IP of the SAPServ and add the keyword $NAT$ in front of it.</p>\r\n<p>Example:<br /><em>Before:</em> /H/your-saprouter/S/3299<br /><em>After:</em> $NAT$/H/your-saprouter/S/3299/H/your-internal-IP-for-SAPServX</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D020206)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D057892)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000797124/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797124/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797124/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797124/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797124/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797124/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797124/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797124/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797124/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "SAO-LOP-behavior-change.pdf", "FileSize": "280", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200001652762004&iv_version=0033&iv_guid=00109B36D5DA1EDE88C796D1C67B8AB6"}, {"FileName": "certificates.zip", "FileSize": "1114", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200001652762004&iv_version=0033&iv_guid=00109B36DB3E1EDE9B84862DC62A86AA"}, {"FileName": "LineOpener_SolMan_71SP13andnewer.docx", "FileSize": "502", "MimeType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200001652762004&iv_version=0033&iv_guid=00109B36D5BA1EEE9B84A6354F3F97DD"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "895754", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/895754"}, {"RefNumber": "376997", "RefComponent": "BW", "RefTitle": "BW-customer incidents with priority 1 (very high)", "RefUrl": "/notes/376997"}, {"RefNumber": "375631", "RefComponent": "BW", "RefTitle": "Which Support Package is recommended for BW-customers?", "RefUrl": "/notes/375631"}, {"RefNumber": "1504102", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1504102"}, {"RefNumber": "112458", "RefComponent": "BW-BEX", "RefTitle": "Trace log in the Business Explorer", "RefUrl": "/notes/112458"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2231445", "RefComponent": "PPM-PRO", "RefTitle": "How to create the perfect case for PPM component and subcomponents", "RefUrl": "/notes/2231445 "}, {"RefNumber": "1757997", "RefComponent": "SRM", "RefTitle": "Line Opener Program for SAP SRM", "RefUrl": "/notes/1757997 "}, {"RefNumber": "2529867", "RefComponent": "SD-BF-AC", "RefTitle": "How to create perfect case for R/3 ATP issues", "RefUrl": "/notes/2529867 "}, {"RefNumber": "2768981", "RefComponent": "XX-SER-NET", "RefTitle": "Semi-Automatic Service Line Opener Program (LOP) mandatory update", "RefUrl": "/notes/2768981 "}, {"RefNumber": "1637249", "RefComponent": "CA-MDG", "RefTitle": "MDG: Information for efficient Support Incident Processing", "RefUrl": "/notes/1637249 "}, {"RefNumber": "376997", "RefComponent": "BW", "RefTitle": "BW-customer incidents with priority 1 (very high)", "RefUrl": "/notes/376997 "}, {"RefNumber": "796308", "RefComponent": "PA-ER", "RefTitle": "Remote connections and how to debug in customer systems", "RefUrl": "/notes/796308 "}, {"RefNumber": "375631", "RefComponent": "BW", "RefTitle": "Which Support Package is recommended for BW-customers?", "RefUrl": "/notes/375631 "}, {"RefNumber": "112458", "RefComponent": "BW-BEX", "RefTitle": "Trace log in the Business Explorer", "RefUrl": "/notes/112458 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}