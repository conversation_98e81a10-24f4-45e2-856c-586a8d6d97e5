{"Request": {"Number": "361643", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 533, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014930852017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000361643?language=E&token=CB0DAE39E98E8C8AE08ECDC489A01300"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000361643", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000361643/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "361643"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 33}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "07.12.2004"}, "SAPComponentKey": {"_label": "Component", "value": "SRM-EBP-APP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Approval"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Supplier Relationship Management", "value": "SRM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SRM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SRM", "value": "SRM-EBP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SRM-EBP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Approval", "value": "SRM-EBP-APP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SRM-EBP-APP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "361643 - Problems with workflow/history display (applet)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The display of the approval (workflow) or graphical display of the history is incorrect or does not exist.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SAP B2B Procurement, business-to-business, business to business, Ecommerce, e-commerce, electronic commerce, e-business, Ebusiness, Internet, Web, New Dimension, workflow, applet, workflow display, workflow diagram, history, EBP, BBP</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error in the Java applet.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>In this note, the general procedure is described for when problems occur during the workflow display or history display.</p> <OL>1. Version history of the note</OL> <UL><LI>sapserv3 and the attachment to this note only contain one - the current - version of the applet. This version is updated when a new correction is released.</LI></UL> <UL><LI>Caution: As of 2004, the current version of the applet (file Visnet.jar) has only been available as an attachment to this note. The files on sapserv3 are no longer updated!</LI></UL> <UL><LI></LI></UL> <UL><UL><LI><B>BBP 1.0</B> 01/25/2001</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BBP 1. 0 05/11/2001 <B>&lt;=== current</B></p> <UL><UL><LI><B>BBP 2.0 and higher:</B></LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;01/26/2001<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;02/02/2001<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;04/19/2001<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;05/11/2001<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;05/21/2001<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;07/30/2001<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;09/06/2001<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;09/21/2001<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;10/25/2001<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;02/06/2002<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;02/19/2002<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;07/09/2002<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;07/25/2003&#x00A0;&#x00A0;Heading \"Other approvers\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;02/10/2004 Scrolling when displaying other approvers, illegible display when displaying other approvers several times.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;03/02/2004 Problem in 7-step workflow<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;11/29/2004 Question mark appears in the 0-step workflow.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;12/07/2004 <B>&lt;=== current</B>: Incorrect display with inserted approver.</p> <OL>2. Before you create a problem message, download the latest version of the applet.</OL> <OL>3. For information on how to import SAPServ corrections, refer to Note 13719.</OL> <UL><LI>Up to Release 1.0B:</LI></UL> <UL><UL><LI>You need all *.class files from directory sapserv3/general/R3server/abap/note.0361643/10x.</LI></UL></UL> <UL><LI>As of Release 2.0A (EBP 2.0A, 2.0B, 2.0C, 3.0, 3.5, 4.0, 5.0; SRM 2.0, 3.0, 4.0):</LI></UL> <UL><UL><LI>You only need the visnet.jar file which has been attached to this note. Any other files should only be used after contacting Development Support.</LI></UL></UL> <UL><UL><LI>Caution: If you have a very complex graph, you may need to download version visnet1.jar or visnet2.jar from sapserv3 directory sapserv3/general/R3server/abap/note.0361643/20x and save it as \"visnet.jar\". These versions show more nodes and links than the standard version, but are no longer maintained and do not contain the current corrections.</LI></UL></UL> <UL><UL><LI>See also the note for the recommended BBP workflow design.</LI></UL></UL> <OL>4. For service TS_TS10008000H, search for the MIME directory in the catalog structure of the WGate. Make a backup of the old files and save the files downloaded from the SAPServ in the \"classes\" directory instead of the old files.</OL> <OL>5. Delete the temporary files of your browser and exit it after this.</OL> <OL>6. Test the applet.<br /><br />If the tests were successful and the problem was solved, bear in mind that the downloaded version is overwritten again if it is published in Transaction SE80 for service TS_TS10008000H.<br /><br />If the problem is not solved, click the right mouse button just next to the applet window and copy the HTML source code into the Online Service System message you enter. Make sure that the applet tag exists in this source code. If this is not the case, try to click closer to the applet in order to copy the source code of the correct frame (that is, the one with the applet tag) into the Online Service System message.</OL> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SRM-EBP-WFL (Workflow)"}, {"Key": "Transaction codes", "Value": "SE80"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D024239)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000361643/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000361643/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000361643/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000361643/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000361643/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000361643/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000361643/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000361643/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000361643/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Visnet.jar", "FileSize": "85", "MimeType": "application/java-archive", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700007407782001&iv_version=0033&iv_guid=2ABD909BD26ADE429226A281CB3974BB"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "645080", "RefComponent": "SRM-EBP-WFL", "RefTitle": "Heading for display of further approvers in the applet", "RefUrl": "/notes/645080"}, {"RefNumber": "529711", "RefComponent": "SRM-EBP-APP", "RefTitle": "Truncated texts in applet workflow", "RefUrl": "/notes/529711"}, {"RefNumber": "524170", "RefComponent": "SRM-EBP-APP", "RefTitle": "Signed version of the Workflow Java Applet", "RefUrl": "/notes/524170"}, {"RefNumber": "493050", "RefComponent": "SRM-EBP-APP", "RefTitle": "Truncated texts in applet", "RefUrl": "/notes/493050"}, {"RefNumber": "492765", "RefComponent": "SRM-EBP-APP", "RefTitle": "Problem in applet when displaying end node for rejection", "RefUrl": "/notes/492765"}, {"RefNumber": "423087", "RefComponent": "SRM-EBP-APP", "RefTitle": "Display time in approval applet", "RefUrl": "/notes/423087"}, {"RefNumber": "417547", "RefComponent": "SRM-EBP-APP", "RefTitle": "Testing the approval preview", "RefUrl": "/notes/417547"}, {"RefNumber": "406783", "RefComponent": "SRM-EBP-APP", "RefTitle": "Error at workflow display with deadline (applet)", "RefUrl": "/notes/406783"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1233039", "RefComponent": "SRM-EBP-TEC-ITS", "RefTitle": "Approval preview: <PERSON><PERSON><PERSON> with more than two reviewers", "RefUrl": "/notes/1233039"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1233039", "RefComponent": "SRM-EBP-TEC-ITS", "RefTitle": "Approval preview: <PERSON><PERSON><PERSON> with more than two reviewers", "RefUrl": "/notes/1233039 "}, {"RefNumber": "423087", "RefComponent": "SRM-EBP-APP", "RefTitle": "Display time in approval applet", "RefUrl": "/notes/423087 "}, {"RefNumber": "406783", "RefComponent": "SRM-EBP-APP", "RefTitle": "Error at workflow display with deadline (applet)", "RefUrl": "/notes/406783 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "645080", "RefComponent": "SRM-EBP-WFL", "RefTitle": "Heading for display of further approvers in the applet", "RefUrl": "/notes/645080 "}, {"RefNumber": "417547", "RefComponent": "SRM-EBP-APP", "RefTitle": "Testing the approval preview", "RefUrl": "/notes/417547 "}, {"RefNumber": "524170", "RefComponent": "SRM-EBP-APP", "RefTitle": "Signed version of the Workflow Java Applet", "RefUrl": "/notes/524170 "}, {"RefNumber": "529711", "RefComponent": "SRM-EBP-APP", "RefTitle": "Truncated texts in applet workflow", "RefUrl": "/notes/529711 "}, {"RefNumber": "492765", "RefComponent": "SRM-EBP-APP", "RefTitle": "Problem in applet when displaying end node for rejection", "RefUrl": "/notes/492765 "}, {"RefNumber": "493050", "RefComponent": "SRM-EBP-APP", "RefTitle": "Truncated texts in applet", "RefUrl": "/notes/493050 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46D", "To": "46D", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "610", "Subsequent": ""}, {"SoftwareComponent": "SRM_SERVER", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SRM_SERVER", "From": "550", "To": "550", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "20B", "To": "20C", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "300", "To": "300", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "400", "To": "400", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "BBPCRM 20B", "SupportPackage": "SAPKU20B14", "URL": "/supportpackage/SAPKU20B14"}, {"SoftwareComponentVersion": "BBPCRM 20B", "SupportPackage": "SAPKU20B09", "URL": "/supportpackage/SAPKU20B09"}, {"SoftwareComponentVersion": "BBPCRM 20C", "SupportPackage": "SAPKU20C03", "URL": "/supportpackage/SAPKU20C03"}, {"SoftwareComponentVersion": "BBPCRM 20C", "SupportPackage": "SAPKU20C02", "URL": "/supportpackage/SAPKU20C02"}, {"SoftwareComponentVersion": "BBPCRM 20C", "SupportPackage": "SAPKU20C06", "URL": "/supportpackage/SAPKU20C06"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}