{"Request": {"Number": "1673041", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 476, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000009933812017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001673041?language=E&token=7860834ED802529395D3BDBD90B5D8D1"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001673041", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1673041"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.02.2012"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-LC-NL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Netherlands"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Real Estate Localization", "value": "RE-FX-LC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Netherlands", "value": "RE-FX-LC-NL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC-NL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1673041 - RE-FX NL: Additional point for highly populated cities"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>In the Netherlands, companies leasing out their rental objects annually calculate the rent adjustment with the adjustment method: representative list of rents.<br /><br />According to legal regulations, extra points can be added in case a rental object is located in a highly-populated city. So that the system can add these extra points, you need to make settings in Customizing and in the master data. Note that you can include the extra points by recalculating the total number of points of a rental object only when there is no valid contract for the given rental object (that is, for example, before you lease it out, or the contract is terminated).</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Real Estate Management<br />Rent Adjustment<br />The Netherlands<br />Highly Populated City<br />Extra points</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Your enterprise operates at SAP Enhancement Package 5 for SAP ERP 6.0<br />or higher.<br /><br />You have activated the Real Estate Localization for the Netherlands as<br />it is described in note 1328779.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br /><br /><B><B>Implementation</B></B><br />As a general rule, SAP recommends that you install a solution by applying a Support Package. However, if you need to install a solution earlier, use the Note Assistant to implement the correction instruction. You can find more information about the Note Assistant in SAP Service Marketplace, under service.sap.com/note-assistant.<br /><br />The description of the solution is in the attached document Rent_Adjustment.pdf. To implement the solution, you must carry out manual activities (see attachment) and implement the correction instructions that are valid for your release.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I033770)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I033770)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001673041/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001673041/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001673041/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001673041/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001673041/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001673041/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001673041/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001673041/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001673041/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Rent_Adjustment.pdf", "FileSize": "87", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000037122012&iv_version=0001&iv_guid=1A210E490EEB0643B588927B814A1060"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1328779", "RefComponent": "RE-FX-LC-NL", "RefTitle": "RE-FX Country Version for Netherlands", "RefUrl": "/notes/1328779"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1328779", "RefComponent": "RE-FX-LC-NL", "RefTitle": "RE-FX Country Version for Netherlands", "RefUrl": "/notes/1328779 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "606", "To": "606", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-APPL 605", "SupportPackage": "SAPK-60509INEAAPPL", "URL": "/supportpackage/SAPK-60509INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 605", "SupportPackage": "SAPK-60508INEAAPPL", "URL": "/supportpackage/SAPK-60508INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 606", "SupportPackage": "SAPK-60603INEAAPPL", "URL": "/supportpackage/SAPK-60603INEAAPPL"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "EA-APPL", "NumberOfCorrin": 1, "URL": "/corrins/0001673041/229"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; EA-APPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP R/3 Enterpr...|<br/>| Release 605&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-60503INEAAPPL - SAPK-60507INEAAPPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 606&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-60601INEAAPPL - SAPK-60602INEAAPPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/><B>1. With transaction SE11 add the following data elements to package GLO_REFX_NL_DDIC:</B><br/><U>REXCNLAREACALCREL</U><br/>Short text: Characteristic Relevant to Total Area Calculation<br/>Domain: RECABOOL<br/>Short: 10 Area Rel.<br/>Medium: 15 Area Relevant<br/>Long: 20 Total Area Relevant<br/>Heading: 15 Area Relevant<br/><br/><U>REXCNLEXTRAPOINTCHARA</U><br/>Short text: F&amp;F Characteristic for Extra Points<br/>Domain: REBDFIXFITCHARACT<br/>Short: 10 Char.ExtrP<br/>Medium: 18 Char. Extra Points<br/>Long: 22 Char. for Extra Points<br/>Heading: 10 Char.ExtrP<br/><br/><U>REXCNLEXTRAPOINTFR</U><br/>Short text: Valid From<br/>Domain: RECADATE<br/>Short: 10 Valid From<br/>Medium: 15 Valid From<br/>Long: 20 Valid From<br/>Heading: 10 Valid From<br/><br/><U>REXCNLEXTRAPOINTREL</U><br/>Short text: Extra points for big cities relevancy<br/>Domain: RECABOOL<br/>Short: 10 Ex.points<br/>Medium: 15 Extra points<br/>Long: 20 Extra points relev.<br/>Heading: 12 Extra points<br/><br/><U>REXCNLEXTRAPOINTTO</U><br/>Short text: Valid To<br/>Domain: RECADATEEND<br/>Short: 10 Valid To<br/>Medium: 15 Valid To<br/>Long: 20 Valid To<br/>Heading: 10 Valid To<br/><br/><U>REXCNLMEAS_ROMVALUE</U><br/>Short text: Measurement Type for RO Value per Square Meter<br/>Domain: REBDMEAS<br/>Short: 10 MTValue/M2<br/>Medium: 15 MT Value per M2<br/>Long: 27 Meas. Type for Value Per M2<br/>Heading: 10 MTValue/M2<br/><br/><U>REXCNLMEAS_ROVALUE</U><br/>Short text: Measurement Type for RO Value<br/>Domain: REBDMEAS<br/>Short: 10 MTValue<br/>Medium: 15 Meas. Type Value<br/>Long: 23 Meas. Type for RO Value<br/>Heading: 10 MT Value<br/><br/><U>REXCNLPTSVALUE</U><br/>Short text: Number of Extra Points<br/>Domain: REBDMEASVALUE<br/>Short: 10 ExtraPoint<br/>Medium: 15 Extra Points<br/>Long: 22 Number of Extra Points<br/>Heading: 10 ExtraPoint<br/><br/><U>REXCNLWOZMVALUEFROM</U><br/>Short text: Value Range<br/>Domain: REBDMEASVALUE<br/>Short: 10 ValueRange<br/>Medium: 15 Value Range<br/>Long: 20 Value Range<br/>Heading: 10 ValueRange<br/><br/><U>REXCNLWOZMVALUEFROM</U><br/>Short text: Value Range<br/>Domain: REBDMEASVALUE<br/>Short: 10 ValueRange<br/>Medium: 15 Value Range<br/>Long: 20 Value Range<br/>Heading: 10 ValueRange<br/><br/><B>2. With transaction SE11 create the following tables:</B><br/><U>TIVXCNLHPCITY</U><br/>Short text: Highly populated cities<br/>Delivery calss 'C', Display/maintenance allowed<br/>Fields:<br/>Field  Key Initial Data element<br/>MANDT  X X  MANDT<br/>CITY1  X X  AD_CITY1<br/>VALIDFROM X X  REXCNLEXTRAPOINTFR<br/>VALIDTO     REXCNLEXTRAPOINTTO<br/><br/><U>TIVXCNLWOZBAS</U><br/>Short text: Define basic settings for extra point calculation<br/>Delivery calss 'C', Display/maintenance allowed<br/>Fields:<br/>Field  Key Initial Data element<br/>MANDT  X X  MANDT<br/>BUKRS  X X  BUKRS<br/>VALIDFROM X X  REXCNLEXTRAPOINTFR<br/>VALIDTO     REXCNLEXTRAPOINTTO<br/>MEAS_ROVALUE    REXCNLMEAS_ROVALUE<br/>MEAS_ROMVALUE   REXCNLMEAS_ROMVALUE<br/>EXTRAPOINTCHARA   REXCNLEXTRAPOINTCHARA<br/><br/><U>TIVXCNLWOZPTS</U><br/>Short text: Extra points per Rental object value/M2<br/>Delivery calss 'C', Display/maintenance allowed<br/>Fields:<br/>Field   Key Initial Data element<br/>MANDT   X X  MANDT<br/>BUKRS   X X  BUKRS<br/>VALIDFROM  X X  REXCNLEXTRAPOINTFR<br/>WOZMVALUEFROM X X  REXCNLWOZMVALUEFROM<br/>WOZMVALUEUNIT    REBDMEASUNIT<br/>VALIDTO      REXCNLEXTRAPOINTTO<br/>PTSVALUE      REXCNLPTSVALUE<br/>PTSVALUEUNIT     REBDMEASUNIT<br/><br/><B>3. With transaction SE11 create the following structures:</B><br/><U>REXCC_NL_HPCITY</U><br/>Short text: Highly populated cities (TIVXCNLHPCITY)<br/>Fields:<br/>Field  Data element<br/>.INCLUDE  TIVXCNLHPCITY<br/><br/><U>REXCC_NL_HPCITY_X</U><br/>Short text: Highly populated cities and Additional Fields (TIVXCNLHPCITY<br/>Fields:<br/>Field  Data element<br/>.INCLUDE  REXCC_NL_HPCITY<br/><br/><U>REXCC_NL_WOZBAS</U><br/>Short text: Basic settings for highly populated cities (TIVXCNLWOZBAS)<br/>Fields:<br/>Field  Data element<br/>.INCLUDE  TIVXCNLWOZBAS<br/><br/><U>REXCC_NL_WOZBAS_X</U><br/>Short text: Basic settings for highly populated cities and Additional Fi<br/>Fields:<br/>Field  Data element<br/>.INCLUDE  REXCC_NL_WOZBAS<br/><br/><U>REXCC_NL_WOZPTS</U><br/>Short text: Basic settings for highly populated cities (TIVXCNLWOZBAS)<br/>Fields:<br/>Field  Data element<br/>.INCLUDE  TIVXCNLWOZBAS<br/><br/><U>REXCC_NL_WOZPTS_X</U><br/>Short text: Extra points per WOZM and Additional Fields (TIVXCNLWOZPTS)<br/>Fields:<br/>Field  Data element<br/>.INCLUDE  REXCC_NL_WOZPTS<br/><br/><U>REXCC_NL_WOZBAS_X</U><br/>Short text: Extra points per WOZM (TIVXCNLWOZPTS)<br/>Fields:<br/>Field  Data element<br/>.INCLUDE  TIVXCNLWOZPTS<br/><br/><B>4. With transaction SE11 create the following table types:</B><br/><U>RE_T_NL_HPCITY</U><br/><B>Short text: </B>Highly populated cities (TIVXCNLHPCITY)<br/><B>Line type: </B>REXCC_NL_HPCITY<br/><br/><U>RE_T_NL_HPCITY_X</U><br/>Short text: Highly populated cities and Additional Fields (TIVXCNLHPCITY<br/>Line type: REXCC_NL_HPCITY_X<br/><br/><U>RE_T_NL_WOZBAS</U><br/>Short text: Basic settings for highly populated cities (TIVXCNLWOZBAS)<br/>Line type: REXCC_NL_WOZBAS<br/><br/><U>RE_T_NL_WOZBAS_X</U><br/>Short text: Basic settings for highly populated cities and Additional Fi<br/>Line type: REXCC_NL_WOZBAS_X<br/><br/><U>RE_T_NL_WOZPTS</U><br/>Short text: Extra points per WOZM (TIVXCNLWOZPTS)<br/>Line type: REXCC_NL_WOZPTS<br/><br/><U>RE_T_NL_WOZPTS_X</U><br/>Short text: Extra points per WOZM and Additional Fields (TIVXCNLWOZPTS)<br/>Line type: REXCC_NL_WOZPTS_X<br/><br/><B>5. With transaction SE11 extend the following table:</B><br/><U>TIVXCNLCHARARD</U><br/>FIELD  Data element<br/>AREACALCREL REXCNLAREACALCREL<br/>Please regenerate maintanence view V_TIVXCNLCHARARD to make the new field editable.<br/><br/><B>5. With transaction SE11 generate the following maitnenance views:</B><br/><U>V_TIVXCNLHPCITY</U><br/>Short text: Highly Populated Cities<br/>Table: TIVXCNLHPCITY<br/>View field Table   Field<br/>MANDT  TIVXCNLHPCITY MANDT<br/>CITY1  TIVXCNLHPCITY CITY1<br/>VALIDFROM TIVXCNLHPCITY VALIDFROM<br/>VALIDTO  TIVXCNLHPCITY VALIDTO<br/>Please generate maintanence view into function group 0REXCNL_HPCITY.<br/><br/><U>V_TIVXCNLWOZBAS</U><br/>Short text: Basic Settings for Highly Polulated Cities<br/>Table: TIVXCNLWOZBAS<br/>View field  Table   Field<br/>MANDT   TIVXCNLWOZBAS MANDT<br/>BUKRS   TIVXCNLWOZBAS BUKRS<br/>VALIDFROM  TIVXCNLWOZBAS VALIDFROM<br/>VALIDTO   TIVXCNLWOZBAS VALIDTO<br/>MEAS_ROVALUE  TIVXCNLWOZBAS MEAS_ROVALUE<br/>MEAS_ROMVALUE TIVXCNLWOZBAS MEAS_ROMVALUE<br/>EXTRAPOINTCHARA TIVXCNLWOZBAS EXTRAPOINTCHARA<br/>Please generate maintanence view into function group 0REXCNL_WOZBAS.<br/><br/><U>V_TIVXCNLWOZPTS</U><br/>Short text: Value Ranges for Extra Points<br/>Table: TIVXCNLWOZPTS<br/>View field  Table   Field<br/>MANDT   TIVXCNLWOZPTS MANDT<br/>BUKRS   TIVXCNLWOZPTS BUKRS<br/>WOZMVALUEFROM TIVXCNLWOZPTS WOZMVALUEFROM<br/>WOZMVALUEUNIT TIVXCNLWOZPTS WOZMVALUEUNIT<br/>PTSVALUE   TIVXCNLWOZPTS PTSVALUE<br/>PTSVALUE   UNIT TIVXCNLWOZPTS PTSVALUEUNIT<br/>VALIDFROM  TIVXCNLWOZPTS VALIDFROM<br/>VALIDTO   TIVXCNLWOZPTS VALIDTO<br/>Please generate maintanence view into function group 0REXCNL_WOZPTS.<br/><br/><br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 5, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EA-APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1568491 ", "URL": "/notes/1568491 ", "Title": "Peformance issues with Fixtures and fittings", "Component": "RE-FX-LC-NL"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1577606 ", "URL": "/notes/1577606 ", "Title": "BAPI_RE_RO_CHANGE does not update the Dutch RO fields", "Component": "RE-FX-LC-NL"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1597385 ", "URL": "/notes/1597385 ", "Title": "RE-FX NL: Rounding of Fixtures and Fittings", "Component": "RE-FX-LC-NL"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "605", "ValidTo": "606", "Number": "1669104 ", "URL": "/notes/1669104 ", "Title": "RE-FX NL: BAPI_RE_RO_CHANGE sets the No. of Fix./Fitt. to 1", "Component": "RE-FX-LC-NL"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1757214", "RefTitle": "Wrong extra point calculation with time dependent F&Fs", "RefUrl": "/notes/0001757214"}]}}}}}