{"Request": {"Number": "1297986", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 276, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016715442017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001297986?language=E&token=E99D2F1D8DF0DA6F05D36BFA31C51B7C"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001297986", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001297986/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1297986"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 12}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.04.2018"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-MSS"}, "SAPComponentKeyText": {"_label": "Component", "value": "SQL Server in SAP NetWeaver Products"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SQL Server in SAP NetWeaver Products", "value": "BC-DB-MSS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-MSS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1297986 - Backup and Restore strategy for MS SQL Server"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<ul>\r\n<li>During an onsite or remote safeguarding SAP service (TFC/TIC/SMA) a customer is informed that his backup and restore concept is missing completely or requires improvement. He asks for additional information about SAP recommendations in this regard;<br /><br />or<br /><br /></li>\r\n<li>An incident with a DB had happened that caused a system downtime exceeding the time frame permitted by the business which reveals an issue in the current backup/restore strategy.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>DB backup restore recovery high availability corruption cluster MSCS WSFC</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>A backup and restore concept for the whole solution needs be developed or revised.<br />The purpose of this note is not to provide a ready-to-use backup strategy rather to attract attention to points that are often missed or misunderstood. This note takes into account incidents that have occurred in real life scenarios.<br /><br />The note is applicable not only to SAP products based on R/3 and the Netweaver platform but rather to any SAP product using MS SQL Server of versions:</p>\r\n<ul>\r\n<li>Microsoft SQL Server 2000 (8.0)</li>\r\n<li>Microsoft SQL Server 2005 (9.0)</li>\r\n<li>Microsoft SQL Server 2008 (10.0) and 2008 R2 (10.5)</li>\r\n<li>Microsoft SQL Server 2012 (11.0)</li>\r\n<li>Microsoft SQL Server 2014 (12.0)</li>\r\n<li>Microsoft SQL Server 2016 (13.0) and more recent versions.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The backup strategy should be balanced in terms of costs and potential losses. A project manager responsible for the system should evaluate all potential financial losses and, in the worst case scenario, total data loss. As a rule financial losses are made up of direct costs for recovering data (additional man-days for external technical and application consultants, operators entering data manually from papers etc.) and indirect losses caused by the incapacity to run the business due to system unavailability and possible penalties to authorities e.g. in case a company omitted to disclose accounting data in time.&#160;Additionally to the fixed costs per incident, losses may be calculated per hour of system downtime, depending on criticality of the system for&#160;the business.<br />When estimating the potential losses, take into account that a reconstruction of a system takes as a rule several days or even weeks if no valid DB backup existed. All the time should be counted as system downtime. In contrast to it, even with a cheap backup solution without high availability features a system can be restored within a few hours. However, to achieve this in a short time frame a good orchestration of all involved parties is necessary, that's why a proper testing of the restore procedure is a must.<br /><br />The financial considerations have a direct influence on the technical architecture of the whole system and in particular on the backup/restore concept. Common sense says the backup Total Cost of Ownership (TCO) should not exceed the potential losses in the worst case. More than often the opposite is done: TCO of a backup solution is overestimated while the costs of a system outage and data loss are seriously underestimated.<br /><br />After doing an estimation, availability requirements should be prepared and discussed with the system architect in the early stages of the project.<br /><br />A database backup must satisfy two basic requirements:</p>\r\n<ol>\r\n<li>it must provide a copy of data at a specific point of time in the past in case of logical damage (user error, hardware fault);</li>\r\n<li>it must provide a way to restore data in case of a natural disaster in the computing center.</li>\r\n</ol>\r\n<p><br />The first requirement makes it necessary to store full DB backups along with the transaction log backups for quite a long period. SAP recommends to perform full backups daily and to retain them for 31 days. In fact, data corruption because of technical reasons (DB corruption, see SAP Note <a target=\"_blank\" href=\"/notes/1597910\">1597910</a>) or human errors may pass unnoticed for some time. As a rule, data corruptions become apparent at a period-end closing at latest. In the most cases a period-end closing is performed once a month (31 days). Since the exact point in time when the data corruption occurred is not known as a rule, the retention period is chosen to be as long as 31 days. While a restore of a 31-day old backup is often not necessary, it guarantees intact data in the worst conceivable case.<br /><br />The second basic requirement makes it necessary to distribute the operative DB and the backup media geographically. Catastrophic events happen less frequently than technical faults or user errors, nevertheless we recommend to keep backup media at a distance from the main data storage. Indeed, if the backup tapes are stored next to the main DB server and the room in this computer center burns out, the tapes will be damaged too, causing a total data loss. Then the expenses for a backup infrastructure would have been in vain. In the regions with no risk of a vast natural disaster (wildfire, earthquake, flood) it may be sufficient to store the backup media in a separate building. In a risk region the backup media should be stored at a distance of several kilometers or even farther.<br /><br />A reliable backup/restore concept must take into account the incoming and outgoing connections of the system. This means, if a solution consists of more than one system and there is data exchange between the systems, special post-restore actions must be taken to keep the systems synchronized.<br /><br />Priorities must be set for emergency scenarios and often more than one decision can be made: take for example a serious corruption. Basically two strategies are possible:</p>\r\n<ul>\r\n<li>\"get the system running at any cost and ignore inconsistencies in the historical data\" or</li>\r\n<li>\"provide 100% data reliability and ignore long system downtime\".</li>\r\n</ul>\r\n<p>Depending on the business role of an impacted system either of the strategies may be reasonable, but this has to be defined beforehand.<br /><br />The very basic backup strategy SAP recommends is as follows:</p>\r\n<ol>\r\n<li>Recovery model for databases should be set to FULL or BULK-LOGGED. It should be ensured, that the recovery model is never switched to SIMPLE, even for a short time and that the transaction log is never truncated manually (See FAQ Note <a target=\"_blank\" href=\"/notes/1420452\">1420452</a>).</li>\r\n<li>Make a full DB backup daily on a separate tape (set of tapes). If a tape drive is attached directly to the DB server, the tape should be taken from the device and stored in a separate room.</li>\r\n<li>Make a transaction log backup with an interval which is shorter than the accepted data loss. SAP recommends a regular log backup in a time interval between 1 and 10 minutes, depending on your workload. During the day subsequent backups are written onto the same tape. If a tape drive is attached directly to the DB server, the tape should be taken from the device in the end of the day and moved to a separate building.</li>\r\n<li>Perform a test restore regularly. This is the very crucial element of the strategy. For more details refer to \"Misconception 6\" below in this note.</li>\r\n</ol>\r\n<p>This strategy is implemented in the DBA calendar in transaction DB13 for ABAP-based SAP products. It can be implemented with low costs but it may not satisfy all possible requirements. That's why it should be adapted or totally reworked in order to achieve desired system and solution availability.&#160;<br /><br />Below you find a list of common misconceptions about backup/restore issues observed in real life.<br /><br /><em>Misconception 1:</em><br /><strong>\"We cannot afford restoring a DB in any case because it would mean unacceptable data loss\". This is sometimes used as an excuse for not doing/checking/retaining backups at all!</strong><br />Fact:<br />Even with classical backups in its basic configuration it is possible to keep data loss guaranteed under several minutes. The maximum data loss in case of a hardware failure is less than the transaction log backup interval which can be set to not less than 1 minute as a rule. In case if the failure has not destroyed the transaction log, data loss can be avoided completely even if the database cannot be open (See FAQ Note <a target=\"_blank\" href=\"/notes/1420452\">1420452</a> question 3 \"How do I perform a tail-log backup if the database is not online\"). Sometimes even this small data loss is unacceptable, for instance, if the system is a part of a complex system landscape with high rate of data exchange. An incomplete restore would create an out-of-sync state between the systems, see more information in SAP note <a target=\"_blank\" href=\"/notes/434645\">434645</a>. In such cases additional technical solutions should be implemented, for instance, AlwaysOn Availability Groups, refer to SAP Note <a target=\"_blank\" href=\"/notes/1772688\">1772688</a> for more information. However, these cannot eliminate a necessity to perform common backups, see misconception 3.<br /><br /><em>Misconception 2:</em><br /><strong>\"We cannot afford restoring a DB in any case because it would mean an unacceptably long system downtime\". This is sometimes used as an excuse for not doing/checking/retaining DB backups at all!</strong><br />Fact:<br />This is true, that a restore from tapes or from a network storage may take hours, which may be unacceptable. However, it is not a reason to skip the backup entirely.&#160;&#160;If the requirement of unplanned downtime is really as strict as half an hour for a single incident (shorter downtime is often possible but can hardly be guaranteed), additional techniques like a snapshot backup, transaction log shipping (SAP Note <a target=\"_blank\" href=\"/notes/1101017\">1101017</a>) and AlwaysOn Availability Groups (SAP Note <a target=\"_blank\" href=\"/notes/1772688\">1772688</a>)&#160;should be implemented to achieve effective high availability of the whole system. Avoid using Database Mirroring&#160;(SAP Note <a target=\"_blank\" href=\"/notes/965908\">965908</a>)&#160;with SQL Server 2012 and higher releases, since it <a target=\"_blank\" href=\"https://msdn.microsoft.com/en-us/library/ms189852(v=sql.110).aspx\">is flagged as deprecated</a> and might be removed in a future release. If you're running SQL Server 2012 or higher, you should consider implementing AlwaysOn Availability Groups. However,&#160;these options&#160;cannot eliminate a necessity to perform common backups, see misconception 3.<br /><br /><em>Misconception 3:</em><br /><strong>\"We have a high availability solution for the DB service (and probably disk mirroring), that protects us against all failures. There is no need to make backups.\"</strong><br />Fact:<br />Nowadays Microsoft provides a \"high availability failover solution\" called Windows Server Failover Clustering&#160;or WSFC (previously known as Microsoft Cluster System or MSCS). Many concentrate only on wording \"high availability\" and do not realize that this relates to services only, but not to data. There is only one single copy of data in&#160;WSFC architecture and if it is lost or corrupted,&#160;WSFC cannot help to make a SAP system available. A disk subsystem must be protected with additional measures in order to provide true high availability for the whole solution. As a rule it is clear for system architects so it is done more or less carefully. The spectrum of protection spans from standard RAID-5 disk array to synchronous mirroring onto a remote storage. Those measures can help in cases of a single disk failure (RAID-5) up to utter destruction of the primary data center (disk mirroring), respectively. However, neither of the techniques can help in case of a database corruption nor in case of a user error. In fact, a data corruption often occurs in physical memory on the active cluster node. At the moment when the data page is written to the disk storage, it will be mirrored byte-to-byte on hardware level to the remote disk resulting in two copies of corrupted data. The same applies to user or application errors. If an ABAP report deleting data is called improperly, the necessary data will be deleted from the database both on local and on remote mirrored disks.<br />A presence of a cluster does not reduce importance of common DB backups, even if it is additionally supplied with disk mirroring (do not mix it up with database mirroring).<br />WSFC&#160;alone does not provide effective high availability in all probable incidents. In its standard configuration with a single storage&#160;WSFC does not provide data protection against natural disasters.<br /><br /><em>Misconception 4:</em><br /><strong>\"Only productive systems must be backed up regularly\".</strong><br />Fact:<br />A development system contains as important data as a productive system. Indeed, before GoLive of a project all actual works like designing, programming, customizing etc is being done in the development system. It usually costs hundreds or even thousands man-days. If the development system's database gets lost in the middle of a project, the financial losses will be made up of personnel costs for all the time and additionally the losses because of a project delay.<br />After handing over to production the criticality of the development system reduces but still remains high enough for backups.<br /><br /><em>Misconception 5:</em><br /><strong>\"We make an \"offline backup\", this is the most reliable kind of backups and is easy to perform\".</strong><br />Fact:<br />There is no such concept as \"offline backup\" for Microsoft SQL Server at all. This misconception comes from other DBMS where a standard backup procedure is nothing more than copying of data files onto backup media, often directly with OS tools. While it is easy to perform when the DB is offline, a consistent file copy of a running database requires a specific procedure to be strictly followed. As a consequence, \"online\"- backups were error-prone and sometimes appear not to be usable.<br />In SQL Server DB backups are always made using the native T-SQL command \"BACKUP DATABASE\". This command does all actions that guarantee logical consistency of a backup set (as a rule it is a single file or single tape). It does not copy whole data files, rather used data pages only as well as log pages that include redo and rollback information. So, in Microsoft SQL Server a single full DB backup set guarantees a consistent DB restore even if all subsequent transaction log backups are missing. The BACKUP DATABASE command is executed when the SQL Server is running and the DB is up, a SAP system may or may not be running at this time, this does not matter at all. From there any backup in SQL Server is always \"online\". What people erroneously call \"offline backup\" on SQL Server can be one of two cases:</p>\r\n<ul>\r\n<li>The SAP system is stopped but DB server is running and the DB backup is made with command \"BACKUP DATABASE\" or with a third-party backup software which calls this T-SQL command. This is an allowed option, we just need to point out that a shutdown of the SAP system is not needed and in fact it is not an \"offline\" backup.</li>\r\n<li>SQL Server is stopped and the backup is made by copying the database files (data and transaction log files) using OS tools like NTBACKUP or even with a manual/scripted file copy. This is absolutely unacceptable because such a \"backup\" can be \"restored\" though (in fact: attached), but it will not accept the subsequent transaction log backups. So the data is restored only to the point in time when the file copy was done and the data loss will comprise many hours or even days.</li>\r\n</ul>\r\n<p>If this method is being practiced as an addition to valid backups, there is no risk, those files are just useless and waste space. However if this is the only way to back up the DB, this should be addressed with very high priority.<br />The file copy takes usually much longer because data files and especially transaction log files have a lot of unused space in them. While the usual backup excludes empty pages from a backup set, the file copy cannot do so. For the same reason much more space is necessary for storing of such \"backups\".<br /><br /><em>Misconception 6:</em><br /><strong>The backup/restore concept degenerates into a pure backup concept.</strong><br />It is another case of an unjustified optimism about reliability of system components. This is often observed in environments using very expensive high-end equipment.<br />The misconception manifests in one or many of following symptoms:</p>\r\n<ul>\r\n<li>a test restore has never been performed by the current system administrators or it was done more than half a year ago;</li>\r\n<li>system administrators do not know how long a test restore takes for a specific scenario;</li>\r\n<li>there is no detailed documentation about the restore procedure or it contains only technical actions without taking into account coordination within business departments (e.g. for discussing a possible data loss);</li>\r\n<li>No person is defined who makes the final decision about a database restore and coordinates all preliminary and follow-up activities.</li>\r\n</ul>\r\n<p>Negative consequences of this misconception are as follows:</p>\r\n<ul>\r\n<li>A system administrator feels unconfident about restoring the DB. When it is really the time to start a restore, he still defers or tries to avoid this at all costs. As a result, the system remains unusable much longer than necessary.</li>\r\n<li>Mistakes are made during the process of restoring the DB or during follow-up activities which delays&#160;&#160;productive usage of the system.</li>\r\n</ul>\r\n<p>Fact:<br />Even absolutely reliable hardware does not protect against user errors, or against other external influences. That's why sometimes a restore of the productive system is necessary so the staff must be trained and prepared for that. A clear understanding must exist who is responsible for specific actions during a restore process until the handover of the system to business users. It must be also known how long each step takes taking into account the current DB size.<br />Conclusion:&#160;&#160;The central point of a backup/restore concept is a <strong>restore</strong>. This event occurs very seldom in contrast to a backup, but this is really the only reason why DB backups are made at all.<br /><br /><em>Misconception 7:</em><br /><strong>Full DB backups are not retained for the recommended period of 31 days.<br /></strong>This misconception is observed pretty often because reasons for such long retaining of old backups are not obvious and long retention period increases total costs of a backup solution.</p>\r\n<p>Fact:<br />The main purpose of an old backup is a case of DB corruption. The backup procedure does not check consistency of DB objects, so one or several recent backup sets may contain corrupted data pages.<br />In such a case one of the previous backups can be restored and the DB can be recovered to the present time (i.e. without data loss) using transaction log backups. In contrast to a common misunderstanding, restoring from e.g. a two weeks old backup does not necessarily mean the two last weeks will be lost. The recovery takes of course longer, but it is in any case a better option than to lose everything.<br />Older backups can be also used to restore a historical state of a DB at a specific point in time. This must not necessarily be done on the productive server! Another example of using an older backup is a necessity to restore a few tables. The DB is fully restored on separate hardware, the needed tables are exported fully or partially and imported back into the productive system. This may be necessary in order to get back data that has been deleted because of a user error or during repair activities (see option \"REPAIR_ALLOW_DATA_LOSS\" in SAP Note <a target=\"_blank\" href=\"/notes/142731\">142731</a>). Importing of historical data from an old backup without stopping the productive system presents a good option for critical systems.<br />Because of all these reasons it is absolutely necessary to store multiple full DB backups for longer time along with the uninterrupted chain of transaction log backups. Usually 31 days appears to be sufficient.<br /><br /></p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (C5024907)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON> (I002675)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001297986/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001297986/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001297986/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001297986/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001297986/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001297986/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001297986/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001297986/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001297986/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "965908", "RefComponent": "BC-DB-MSS", "RefTitle": "SQL Server Database Mirroring and SAP Applications", "RefUrl": "/notes/965908"}, {"RefNumber": "81692", "RefComponent": "BC-DB-MSS", "RefTitle": "Suspect database", "RefUrl": "/notes/81692"}, {"RefNumber": "44449", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/44449"}, {"RefNumber": "437160", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/437160"}, {"RefNumber": "434645", "RefComponent": "BC", "RefTitle": "Point-in-time recovery: What must I be aware of?", "RefUrl": "/notes/434645"}, {"RefNumber": "193816", "RefComponent": "BC-DB-MSS", "RefTitle": "Restore with SQL Server", "RefUrl": "/notes/193816"}, {"RefNumber": "1597910", "RefComponent": "BC-DB-MSS", "RefTitle": "Handling of database corruptions on SQL Server", "RefUrl": "/notes/1597910"}, {"RefNumber": "1420452", "RefComponent": "BC-DB-MSS", "RefTitle": "FAQ: Restore and recovery with MS SQL Server", "RefUrl": "/notes/1420452"}, {"RefNumber": "1101017", "RefComponent": "BC-DB-MSS", "RefTitle": "Log shipping on SQL Server 2005", "RefUrl": "/notes/1101017"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2561598", "RefComponent": "BC-DB-MSS", "RefTitle": "SQL Server - \"Verify backup when finished\" option", "RefUrl": "/notes/2561598 "}, {"RefNumber": "2213869", "RefComponent": "BC-DB-MSS", "RefTitle": "SQL Server Setup Check shows different result when Recovery Mode <> 'FULL'", "RefUrl": "/notes/2213869 "}, {"RefNumber": "193816", "RefComponent": "BC-DB-MSS", "RefTitle": "Restore with SQL Server", "RefUrl": "/notes/193816 "}, {"RefNumber": "81692", "RefComponent": "BC-DB-MSS", "RefTitle": "Suspect database", "RefUrl": "/notes/81692 "}, {"RefNumber": "1101017", "RefComponent": "BC-DB-MSS", "RefTitle": "Log shipping on SQL Server 2005", "RefUrl": "/notes/1101017 "}, {"RefNumber": "1597910", "RefComponent": "BC-DB-MSS", "RefTitle": "Handling of database corruptions on SQL Server", "RefUrl": "/notes/1597910 "}, {"RefNumber": "965908", "RefComponent": "BC-DB-MSS", "RefTitle": "SQL Server Database Mirroring and SAP Applications", "RefUrl": "/notes/965908 "}, {"RefNumber": "1420452", "RefComponent": "BC-DB-MSS", "RefTitle": "FAQ: Restore and recovery with MS SQL Server", "RefUrl": "/notes/1420452 "}, {"RefNumber": "434645", "RefComponent": "BC", "RefTitle": "Point-in-time recovery: What must I be aware of?", "RefUrl": "/notes/434645 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}