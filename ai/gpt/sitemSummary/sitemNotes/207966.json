{"Request": {"Number": "207966", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 282, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001093012017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000207966?language=E&token=B12FCBB9BF7FBB2AF97D0C564E9D4917"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000207966", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000207966/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "207966"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 20}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.04.2001"}, "SAPComponentKey": {"_label": "Component", "value": "SD-BIL-GF"}, "SAPComponentKeyText": {"_label": "Component", "value": "Basic Functions"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Sales and Distribution", "value": "SD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Billing", "value": "SD-BIL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD-BIL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "SD-BIL-GF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD-BIL-GF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "207966 - Cancell.billing doc.: Statistics are not updated"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>During the transaction 'Cancel billing document', a cancellation document is created for which the statistics are not updated.<br /><br />Now, transaction type 'V Change' is used for the document instead of 'H Create'.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>VF02, VF03, VF11, VFRB<br />S262, 2LIS_01_S262, S1, S2, S3, BW, BIW, Busines Information Warehouse<br />SIS, STAFO, statistics, S060, statistics update, cancellation SIS, RMCSS262<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Cause:</p> <UL><LI>You implemented Note 193221 or</LI></UL> <UL><LI>You imported R/3 Support Package SAPKH31I43</LI></UL> <UL><LI>You imported R/3 Support Package SAPKH40B36</LI></UL> <UL><LI>You imported R/3 Support Package SAPKH45B15.<br /></LI></UL><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Implement the attached correction.<br /><br />For a subsequent update of the statistics of existing documents, you have two options:</p> <OL>1. You perform a statistical setup for all billing documents for a period starting with the point in time where the cause (see above) occured and ending with the implementation of this correction.</OL> <UL><LI>Advantage:<br />This way, all documents are taken into account.</LI></UL> <UL><LI>Disadvantage:<br />Update structure S060 for volume based rebates is not taken into account.</LI></UL> <OL>2. You use attached report ZZCORVIS. Under the prerequisite that the cancellation document no longer contains any inconsistent price conditions (also see Note 198719).<br />Advantage:<br />You can update individual documents subsequently including structure S060.</OL> <UL><LI>Disadvantage<br />Since it is not possible to store in a document that it was already updated, during a run of this report, the system may also update the statistics of a cancellation document if an update already occured.</LI></UL> <p><br />Addition:<br />For the subsequent update of the statistics of the cancellation documents of subsequent settlements (purchasing),&#x00A0;&#x00A0;consider related Note 216306.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SD-IS (Information System)"}, {"Key": "Other Components", "Value": "BW-BCT-SD (BW only - Sales and Distribution)"}, {"Key": "Other Components", "Value": "MM-PUR-VM-SET (Subsequent Settlement)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D002635)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000207966/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000207966/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000207966/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000207966/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000207966/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000207966/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000207966/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000207966/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000207966/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "79234", "RefComponent": "SD-BIL", "RefTitle": "Incorrect statistics update in the billing document", "RefUrl": "/notes/79234"}, {"RefNumber": "40147", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 3.0", "RefUrl": "/notes/40147"}, {"RefNumber": "385939", "RefComponent": "SD-IS", "RefTitle": "SIS/BW: Changed data after correction reports in SD/LE", "RefUrl": "/notes/385939"}, {"RefNumber": "356359", "RefComponent": "SD-BIL-GF", "RefTitle": "Transaction type when canceling billing document", "RefUrl": "/notes/356359"}, {"RefNumber": "216306", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Missng updatng of income customer billing doc when cancellng", "RefUrl": "/notes/216306"}, {"RefNumber": "204130", "RefComponent": "SD-IS", "RefTitle": "SIS update: Collective note", "RefUrl": "/notes/204130"}, {"RefNumber": "198719", "RefComponent": "SD-BIL-GF", "RefTitle": "Cancellation of billing doc: Conditions are missing", "RefUrl": "/notes/198719"}, {"RefNumber": "193221", "RefComponent": "SD-BIL-GF", "RefTitle": "Incorrect authorization check for cancellation", "RefUrl": "/notes/193221"}, {"RefNumber": "130930", "RefComponent": "CO-PA-ACT", "RefTitle": "Inc.sals ord.active but no lne itm,corr.prog.trmnts", "RefUrl": "/notes/130930"}, {"RefNumber": "117698", "RefComponent": "SD-IS", "RefTitle": "Statistics update in billing document incorrect", "RefUrl": "/notes/117698"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "204130", "RefComponent": "SD-IS", "RefTitle": "SIS update: Collective note", "RefUrl": "/notes/204130 "}, {"RefNumber": "356359", "RefComponent": "SD-BIL-GF", "RefTitle": "Transaction type when canceling billing document", "RefUrl": "/notes/356359 "}, {"RefNumber": "40147", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 3.0", "RefUrl": "/notes/40147 "}, {"RefNumber": "216306", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Missng updatng of income customer billing doc when cancellng", "RefUrl": "/notes/216306 "}, {"RefNumber": "193221", "RefComponent": "SD-BIL-GF", "RefTitle": "Incorrect authorization check for cancellation", "RefUrl": "/notes/193221 "}, {"RefNumber": "198719", "RefComponent": "SD-BIL-GF", "RefTitle": "Cancellation of billing doc: Conditions are missing", "RefUrl": "/notes/198719 "}, {"RefNumber": "385939", "RefComponent": "SD-IS", "RefTitle": "SIS/BW: Changed data after correction reports in SD/LE", "RefUrl": "/notes/385939 "}, {"RefNumber": "130930", "RefComponent": "CO-PA-ACT", "RefTitle": "Inc.sals ord.active but no lne itm,corr.prog.trmnts", "RefUrl": "/notes/130930 "}, {"RefNumber": "117698", "RefComponent": "SD-IS", "RefTitle": "Statistics update in billing document incorrect", "RefUrl": "/notes/117698 "}, {"RefNumber": "79234", "RefComponent": "SD-BIL", "RefTitle": "Incorrect statistics update in the billing document", "RefUrl": "/notes/79234 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "31I", "To": "31I", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 31I", "SupportPackage": "SAPKH31I51", "URL": "/supportpackage/SAPKH31I51"}, {"SoftwareComponentVersion": "SAP_HR 31I", "SupportPackage": "SAPKE31I51", "URL": "/supportpackage/SAPKE31I51"}, {"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B58", "URL": "/supportpackage/SAPKH40B58"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B37", "URL": "/supportpackage/SAPKH45B37"}, {"SoftwareComponentVersion": "SAP_APPL 46B", "SupportPackage": "SAPKH46B25", "URL": "/supportpackage/SAPKH46B25"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C15", "URL": "/supportpackage/SAPKH46C15"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 2, "URL": "/corrins/0000207966/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "30E", "ValidTo": "31I", "Number": "193221 ", "URL": "/notes/193221 ", "Title": "Incorrect authorization check for cancellation", "Component": "SD-BIL-GF"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}