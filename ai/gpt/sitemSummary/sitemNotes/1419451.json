{"Request": {"Number": "1419451", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 397, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000008368182017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001419451?language=E&token=BE9FF2677E1EF5D248C9BAF8556B7EE0"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001419451", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001419451/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1419451"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "03.03.2010"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-ET-WJR"}, "SAPComponentKeyText": {"_label": "Component", "value": "BEx Web Java Runtime"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Explorer", "value": "BW-BEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Enduser Technology", "value": "BW-BEX-ET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-ET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BEx Web Java Runtime", "value": "BW-BEX-ET-WJR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-ET-WJR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1419451 - RSWR_BOOKMARK_DELETE: Report to delete the Bookmarks"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>&#x00A0;&#x00A0;There is no option for the mass deletion of the bookmarks.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>&#x00A0;&#x00A0;RSWR_BOOKMARK_DELETE, bookmark deletion.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>&#x00A0;&#x00A0;This is due to a program error.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>&#x00A0;&#x00A0;The report RSWR_BOOKMARK_DELETE can be used for the mass deletion of the Bookmarks. The user has the option to select the bookmarks to be deleted under four conditions:<br /><br />&#x00A0;&#x00A0;1. Delete all the Bookmarks of a particular type(either for the selected templates or all templates),<br />&#x00A0;&#x00A0;2. Delete all the Bookmarks which have not been used since the key date(either for the selected templates or all templates),<br />&#x00A0;&#x00A0;3. Delete the Bookmarks by entering the Bookmark IDs directly,<br />&#x00A0;&#x00A0;4. Delete all the Bookmarks created by an user(s), with option to also filter it further based on the last used date or template name.<br /><br />Make sure to follow the manual activities mentioned as a part of the note after implementing it.</p> <UL><LI>SAP NetWeaver BW 7.00<br /><br />Import Support Package 24 for SAP NetWeaver BW 7.00(SAPKW70024) into your BW system. The Support Package will be available as soon as <B>SAP note 1407598</B> with the short text \"SAPBINews NW BI 7.0 ABAP SP24\", which describes this Support Package in more detail, is released for customers.</LI></UL> <UL><LI>SAP NetWeaver BW 7.01 (SAP NW BW7.0 EnhP 1)<br /><br />Import Support Package 07 for SAP NetWeaver BW 7.01 (SAPKW70107) into your BW system. The Support Package will be available as soon as <B>SAP Note</B><B><B> 1369294</B></B> with the short text \"SAPBINews NW7.01 BI ABAP SP07\", which describes this Support Package in more detail, is released for customers.</LI></UL> <UL><LI>SAP NetWeaver BW 7.02 (SAP NW BW7.0 EnhP 2)<br /><br />Import Support Package 03 for SAP NetWeaver BW 7.02 (SAPKW70203) into your BW system. The Support Package will be available as soon as <B>SAP note 1407600</B> with the short text \"SAPBINews NW7.02 BI ABAP SP03\", which describes this Support Package in more detail, is released for customers.</LI></UL> <UL><LI>SAP NetWeaver BW 7.11<br /><br />Import Support Package 05 for SAP NetWeaver BW 7.11 (SAPKW71105) into your BW system. The Support Package will be available as soon as <B>SAP Note 1392433</B> with the short text \"SAPBINews NW7.11 BI SP05\", which describes this Support Package in more detail, is released for customers.</LI></UL> <UL><LI>SAP NetWeaver BW 7.20<br /><br />Import Support Package 03 for SAP NetWeaver BW 7.20 (SAPKW72003) into your BW system. The Support Package will be available as soon as S<B>AP note 1407599</B> with the short text \"SAPBINews NW7.20 BI SP03\", which describes this Support Package in more detail, is released for customers.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br /><br />In urgent cases you can use the correction instructions.<br /><br />Beforehand, definitely check <B>SAP Note 875986</B> for transaction SNOTE.<br /><br />This note might already be available before the Support Package is released. In this case, however, the short text still contains the terms\"preliminary version\".<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BEX-ET-WJR-RT (Web Runtime and API commands)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I034467)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I045226)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001419451/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001419451/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001419451/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001419451/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001419451/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001419451/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001419451/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001419451/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001419451/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478"}, {"RefNumber": "1446964", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "RSWR_BOOKMARK_DELETE: Performance optimization", "RefUrl": "/notes/1446964"}, {"RefNumber": "1408383", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.0: Improvements in Bookmark Maintenance", "RefUrl": "/notes/1408383"}, {"RefNumber": "1407600", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.02 ABAP SP 03", "RefUrl": "/notes/1407600"}, {"RefNumber": "1407599", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1407599"}, {"RefNumber": "1407598", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.00 ABAP SP 24", "RefUrl": "/notes/1407598"}, {"RefNumber": "1392433", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.11 ABAP SP 05", "RefUrl": "/notes/1392433"}, {"RefNumber": "1369294", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.01 ABAP SP 07", "RefUrl": "/notes/1369294"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478 "}, {"RefNumber": "1369294", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.01 ABAP SP 07", "RefUrl": "/notes/1369294 "}, {"RefNumber": "1407598", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.00 ABAP SP 24", "RefUrl": "/notes/1407598 "}, {"RefNumber": "1407599", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.20 ABAP SP 03", "RefUrl": "/notes/1407599 "}, {"RefNumber": "1392433", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.11 ABAP SP 05", "RefUrl": "/notes/1392433 "}, {"RefNumber": "1407600", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.02 ABAP SP 03", "RefUrl": "/notes/1407600 "}, {"RefNumber": "1408383", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.0: Improvements in Bookmark Maintenance", "RefUrl": "/notes/1408383 "}, {"RefNumber": "1446964", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "RSWR_BOOKMARK_DELETE: Performance optimization", "RefUrl": "/notes/1446964 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "711", "To": "720", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "701", "To": "701", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW 700", "SupportPackage": "SAPKW70024", "URL": "/supportpackage/SAPKW70024"}, {"SoftwareComponentVersion": "SAP_BW 700", "SupportPackage": "SAPKW70024", "URL": "/supportpackage/SAPKW70024"}, {"SoftwareComponentVersion": "SAP_BW_VIRTUAL_COMP 701", "SupportPackage": "SAPK-70115INVCBWTECH", "URL": "/supportpackage/SAPK-70115INVCBWTECH"}, {"SoftwareComponentVersion": "SAP_BW_VIRTUAL_COMP 701", "SupportPackage": "SAPK-70114INVCBWTECH", "URL": "/supportpackage/SAPK-70114INVCBWTECH"}, {"SoftwareComponentVersion": "SAP_BW 701", "SupportPackage": "SAPKW70107", "URL": "/supportpackage/SAPKW70107"}, {"SoftwareComponentVersion": "SAP_BW 701", "SupportPackage": "SAPKW70107", "URL": "/supportpackage/SAPKW70107"}, {"SoftwareComponentVersion": "SAP_BW 702", "SupportPackage": "SAPKW70204", "URL": "/supportpackage/SAPKW70204"}, {"SoftwareComponentVersion": "SAP_BW 702", "SupportPackage": "SAPKW70203", "URL": "/supportpackage/SAPKW70203"}, {"SoftwareComponentVersion": "SAP_BW 711", "SupportPackage": "SAPKW71105", "URL": "/supportpackage/SAPKW71105"}, {"SoftwareComponentVersion": "SAP_BW 720", "SupportPackage": "SAPKW72003", "URL": "/supportpackage/SAPKW72003"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BW", "NumberOfCorrin": 2, "URL": "/corrins/0001419451/30"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_BW&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Business Inform...|<br/>| Release 700&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKW70018 - SAPKW70023&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 711&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKW71101 - SAPKW71104&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 701&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKW70103 - SAPKW70106&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 702&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKW70201 - SAPKW70202&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 720&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKW72001 - SAPKW72002&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/><B>Caution</B>: You have to perform this manual post-implementation step manually and separately in each system <B>after</B> you have imported the Note to implement.<br/><br/>1. Create the following text symbols:<br/><br/>SYM&nbsp;&nbsp; TEXT<br/>001 &nbsp;&nbsp;No Bookmark found for the given selection.<br/>002 &nbsp;&nbsp;Do you want to continue? <br/>003 &nbsp;&nbsp;Deletion of Bookmarks <br/>004 &nbsp;&nbsp; entries selected for deletion from the database. <br/>005 &nbsp;&nbsp;The selected Bookmarks will be permanently deleted from the database. <br/>006 &nbsp;&nbsp;It is not possible to revert those changes. Are you really sure to proceed? <br/>TBK &nbsp;&nbsp;Direct Bookmark Input <br/>TDT &nbsp;&nbsp;Date based selection <br/>TSL &nbsp;&nbsp;Selections for Bookmark deletion <br/>TTY &nbsp;&nbsp;Bookmark type based selection <br/>TUR &nbsp;&nbsp;Owner based selection <br/><br/>2. Create the following Selection texts:<br/><br/>NAME&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;TEXT<br/>B_DATE &nbsp;&nbsp;Delete Bookmarks Older Than<br/>B_ID &nbsp;&nbsp;Delete Bookmark ID<br/>B_TYPE &nbsp;&nbsp;Delete Bookmark Type<br/>B_USER &nbsp;&nbsp;Delete Bookmarks Owned By<br/>DATE &nbsp;&nbsp;Last used on<br/>DATE_U &nbsp;&nbsp;Delete Bookmarks Older Than<br/>P_TYPE &nbsp;&nbsp;Bookmark Type<br/>S_ID &nbsp;&nbsp;Bookmark ID(s)<br/>S_TMPL &nbsp;&nbsp;Web Template<br/>S_TMPL_D&nbsp;&nbsp;Web Template<br/>S_TMPL_U&nbsp;&nbsp;Web Template<br/>S_USR &nbsp;&nbsp;User Name<br/><br/>3. Create the GUI status 'BOOKMARK' with the description 'Initial Screen'<br/><br/>4. Create the following item in the Application Toolbar:<br/><br/>a. Item 1: Function Code&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DELETE<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Function Text&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Delete<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Icon Name&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ICON_DELETE<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Info. Text&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Delete chosen Bookmarks<br/><br/>5. Create the GUI title as follows:<br/><br/>Title Number&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Title<br/>SELECTED BOOKMARKS&nbsp;&nbsp; Selected Bookmarks<br/><br/><br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "720", "Number": "1419451 ", "URL": "/notes/1419451 ", "Title": "RSWR_BOOKMARK_DELETE: Report to delete the Bookmarks", "Component": "BW-BEX-ET-WJR"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1516796", "RefTitle": "RSWR_BOOKMARK_DELETE doesn't delete personalization entries", "RefUrl": "/notes/0001516796"}]}}}}}