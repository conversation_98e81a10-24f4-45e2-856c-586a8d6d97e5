{"Request": {"Number": "2487913", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1224, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000019045862017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002487913?language=E&token=BE147DA7F317FFE34C8E3D64572696EB"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002487913", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002487913/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2487913"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.07.2017"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-GEN"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP_BW Add-On Components: BI_CONT & BI_CONT_XT."}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP_BW Add-On Components: BI_CONT & BI_CONT_XT.", "value": "BW-BCT-GEN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-GEN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2487913 - How to deactivate Industry Business Function /IMO/BW_CONTENT"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><span style=\"font-size: 14px; font-family: Arial, Helvetica, sans-serif; white-space: normal; word-spacing: 0px; text-transform: none; float: none; font-weight: normal; color: #333333; font-style: normal; orphans: 2; widows: 2; display: inline !important; letter-spacing: normal; background-color: #fafafa; text-indent: 0px; font-variant-ligatures: normal; font-variant-caps: normal; -webkit-text-stroke-width: 0px; text-decoration-style: initial; text-decoration-color: initial;\">You have installed the SAP NetWeaver BW and BI Content Addon as an embedded BW in your operating system e.g. in&#160;SFIN or ECC.</span></p>\r\n<p><span style=\"font-size: 14px; font-family: Arial, Helvetica, sans-serif; white-space: normal; word-spacing: 0px; text-transform: none; float: none; font-weight: normal; color: #333333; font-style: normal; orphans: 2; widows: 2; display: inline !important; letter-spacing: normal; background-color: #fafafa; text-indent: 0px; font-variant-ligatures: normal; font-variant-caps: normal; -webkit-text-stroke-width: 0px; text-decoration-style: initial; text-decoration-color: initial;\">You have activated the Business Function /IMO/BW_CONTENT in that system and need to deactivate this Business Function, because e.g. you want to convert your&#160;system to S/4 and&#160;convertion is not possible with BF /IMO/BW_CONTENT activ.</span></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p><span style=\"font-size: 14px; font-family: Arial, Helvetica, sans-serif; white-space: normal; word-spacing: 0px; text-transform: none; float: none; font-weight: normal; color: #333333; font-style: normal; orphans: 2; widows: 2; display: inline !important; letter-spacing: normal; background-color: #fafafa; text-indent: 0px; font-variant-ligatures: normal; font-variant-caps: normal; -webkit-text-stroke-width: 0px; text-decoration-style: initial; text-decoration-color: initial;\">Business Function /IMO/BW_CONTENT, Business Function Set /IMO/CONTENT, Business Function, Switch, S/4</span></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The Business Function /IMO/BW_Content has been wrongly delivered as Industry Business Function, therefore this Business Function has been set to obsolete and replaced by Business Function /IMO/BWCONTENT. In some cases the activated Business Function might cause problem, e.g. see above. More details see SAP Note<a target=\"_blank\" href=\"/notes/2257232\"> 2257232</a></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>You can deactivate the business function /IMO/BW_CONTENT using the following steps:</p>\r\n<ol style=\"font-size: 14px; font-family: Arial, Helvetica, sans-serif; white-space: normal; word-spacing: 0px; text-transform: none; font-weight: normal; color: #333333; font-style: normal; orphans: 2; widows: 2; letter-spacing: normal; background-color: #fafafa; text-indent: 0px; font-variant-ligatures: normal; font-variant-caps: normal; -webkit-text-stroke-width: 0px; text-decoration-style: initial; text-decoration-color: initial; -webkit-tap-highlight-color: transparent; text-size-adjust: none;\">&#160;\r\n<li style=\"-webkit-tap-highlight-color: transparent; text-size-adjust: none;\">Before you execute the&#160;following steps, create a backup.&#160;<strong style=\"-webkit-tap-highlight-color: transparent; text-size-adjust: none;\">An error during execution may lead to loss of data.</strong></li>\r\n<li style=\"-webkit-tap-highlight-color: transparent; text-size-adjust: none;\">Deactivate the business function /IMO/BW_CONTENT using transaction SFW5 in the development system. Please make sure via the check function that there are no messages that prevent the deactivation. For a detailed description on how to deactivate the Business Function see the attached Document. Activate the changes.</li>\r\n<li style=\"-webkit-tap-highlight-color: transparent; text-size-adjust: none;\">Carry out the following modification in the method GET_EXP_MODE_BF of the class CL_SFW5_EXPERT_MANAGEMENT using transaction SE80:<br style=\"-webkit-tap-highlight-color: transparent; text-size-adjust: none;\" />\r\n<p style=\"-webkit-tap-highlight-color: transparent; text-size-adjust: none;\">METHOD get_exp_mode_bf.<br style=\"-webkit-tap-highlight-color: transparent; text-size-adjust: none;\" />* &#160;expert_bf&#160;= expert_mode_bf. &#160; &#160; \"&lt;&lt;&lt;&lt; DELETE<br style=\"-webkit-tap-highlight-color: transparent; text-size-adjust: none;\" /><strong style=\"-webkit-tap-highlight-color: transparent; text-size-adjust: none;\">&#160; expert_bf&#160;= abap_true.</strong>&#160; &#160; &#160; &#160; &#160; &#160; &#160;\"&lt;&lt;&lt;&lt; INSERT<br style=\"-webkit-tap-highlight-color: transparent; text-size-adjust: none;\" />ENDMETHOD.</p>\r\n</li>\r\n<li style=\"-webkit-tap-highlight-color: transparent; text-size-adjust: none;\">Activate the code and transport the modification (point&#160;3).</li>\r\n<li style=\"-webkit-tap-highlight-color: transparent; text-size-adjust: none;\">Transport the system settings using transaction SFW5 (System Settings -&gt; Transport). This transport must not be imported in the same queue together with the transport from step 4.</li>\r\n<li style=\"-webkit-tap-highlight-color: transparent; text-size-adjust: none;\">Cancel the modification (point 3) and transport this change (separate queue).</li>\r\n</ol>\r\n<p style=\"-webkit-tap-highlight-color: transparent; text-size-adjust: none;\"><span style=\"font-size: 14px; font-family: Arial, Helvetica, sans-serif; white-space: normal; word-spacing: 0px; text-transform: none; float: none; font-weight: normal; color: #333333; font-style: normal; orphans: 2; widows: 2; display: inline !important; letter-spacing: normal; background-color: #fafafa; text-indent: 0px; font-variant-ligatures: normal; font-variant-caps: normal; -webkit-text-stroke-width: 0px; text-decoration-style: initial; text-decoration-color: initial;\">If you want, that SAP HANA-optimized BW Content is visible and installable&#160;in the BW Content Installation Transaction RSORBCT in your system, activate the new Enterprise Business Function /IMO/BWCONTENT in TA SFW5 (see SAP Note <a target=\"_blank\" href=\"/notes/2257232\">2257232</a>). After that you will be able to find the SAP HANA-optimized Content in transaction RSORBCT, when you&#160;select e.g. an DataStore object. Nothing else is influenced by the BFs, therefore it is not critical to turn the obsolete Industry BF&#160; /IMO/BW_CONTENT off and turn the new Enterprise Business Function /IMO/BWCONTENT on.</span></p>\r\n<p style=\"-webkit-tap-highlight-color: transparent; text-size-adjust: none;\"><span style=\"font-size: 14px; font-family: Arial, Helvetica, sans-serif; white-space: normal; word-spacing: 0px; text-transform: none; float: none; font-weight: normal; color: #333333; font-style: normal; orphans: 2; widows: 2; display: inline !important; letter-spacing: normal; background-color: #fafafa; text-indent: 0px; font-variant-ligatures: normal; font-variant-caps: normal; -webkit-text-stroke-width: 0px; text-decoration-style: initial; text-decoration-color: initial;\">Concerning the usage of BI_CONT with/within SAP S/4HANA please see note <a target=\"_blank\" href=\"/notes/2289424\">2289424</a>&#160;</span></p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D028763)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D028763)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002487913/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002487913/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002487913/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002487913/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002487913/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002487913/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002487913/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002487913/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002487913/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Deactivate BF IMO BW_CONTENT.docx", "FileSize": "177", "MimeType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125800001548602017&iv_version=0002&iv_guid=6EAE8B27FE511ED795E655E105FAA0C6"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2257232", "RefComponent": "BW-BCT-GEN", "RefTitle": "Activate Business Function /IMO/BWCONTENT", "RefUrl": "/notes/2257232"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2257232", "RefComponent": "BW-BCT-GEN", "RefTitle": "Activate Business Function /IMO/BWCONTENT", "RefUrl": "/notes/2257232 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BI_CONT", "From": "737", "To": "737", "Subsequent": ""}, {"SoftwareComponent": "BI_CONT", "From": "747", "To": "747", "Subsequent": ""}, {"SoftwareComponent": "BI_CONT", "From": "757", "To": "757", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}