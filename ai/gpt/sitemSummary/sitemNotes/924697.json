{"Request": {"Number": "924697", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 553, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005411332017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000924697?language=E&token=8B8F57A080E55A3E08BF33963F2A8466"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000924697", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000924697/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "924697"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.10.2006"}, "SAPComponentKey": {"_label": "Component", "value": "SCM-APO-MD-VM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Version Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Supply Chain Management", "value": "SCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Advanced Planning and Optimization", "value": "SCM-APO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-APO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Master Data", "value": "SCM-APO-MD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-APO-MD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Version Management", "value": "SCM-APO-MD-VM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-APO-MD-VM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "924697 - Dump 'DBIF_DSQL2_OBJ_UNKNOWN' while deleting active version"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>While deleting the active planning version (000) using the report '/SAPAPO/ACT_VERS_DEL', a runtime error \"DBIF_DSQL2_OBJ_UNKNOWN\" occurs.<br /><br />The report /SAPAPO/ACT_VERS_DEL uses<br />the /sapapo/atp_all_sd_orders_del function module to initialize around<br />all SD order tables. This initialization occurs on a cross-client basis<br />and is not correct for the report /SAPAPO/ACT_VERS_DEL because<br />this report should only be executed for the deletion of the active version in the current client. Function module /SAPAPO/ATP_ALL_SDORD_DELMANDT should be used instead.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>/SAPAPO/MVM_VERS_DEL, /SAPAPO/ATP_ALL_SD_ORDERS_DEL, /SAPAPO/MVM,<br />/SAPAPO/ATP_ALL_SDORD_DELMANDT</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Program error<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Please implement first the note 900730 and then the attached correction instructions in your system or import the corresponding support package.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "DownPort/UpPort-WF", "Value": "UpPort check done, symptom repaired"}, {"Key": "Responsible                                                                                         ", "Value": "D032099"}, {"Key": "Processor                                                                                           ", "Value": "D001627"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000924697/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000924697/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000924697/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000924697/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000924697/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000924697/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000924697/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000924697/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000924697/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "900730", "RefComponent": "SCM-APO-ATP", "RefTitle": "/SAPAPO/ATP_ALL_SD_ORDERS_DEL deletes across clients", "RefUrl": "/notes/900730"}, {"RefNumber": "684406", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/684406"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "900730", "RefComponent": "SCM-APO-ATP", "RefTitle": "/SAPAPO/ATP_ALL_SD_ORDERS_DEL deletes across clients", "RefUrl": "/notes/900730 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APO", "From": "30A", "To": "30A", "Subsequent": ""}, {"SoftwareComponent": "SAP_APO", "From": "310", "To": "310", "Subsequent": ""}, {"SoftwareComponent": "SCM", "From": "400", "To": "400", "Subsequent": ""}, {"SoftwareComponent": "SCM", "From": "410", "To": "410", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APO 30A", "SupportPackage": "SAPKY30A30", "URL": "/supportpackage/SAPKY30A30"}, {"SoftwareComponentVersion": "SAP_APO 310", "SupportPackage": "SAPKY31025", "URL": "/supportpackage/SAPKY31025"}, {"SoftwareComponentVersion": "SCM 400", "SupportPackage": "SAPKY40019", "URL": "/supportpackage/SAPKY40019"}, {"SoftwareComponentVersion": "SCM 410", "SupportPackage": "SAPKY41010", "URL": "/supportpackage/SAPKY41010"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SCM", "NumberOfCorrin": 2, "URL": "/corrins/0000924697/418"}, {"SoftwareComponent": "SAP_APO", "NumberOfCorrin": 2, "URL": "/corrins/0000924697/32"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 22, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APO", "ValidFrom": "30A", "ValidTo": "30A", "Number": "410438 ", "URL": "/notes/410438 ", "Title": "Schnelles Löschen von <PERSON>en", "Component": "SCM-APO-MD-VM"}, {"SoftwareComponent": "SAP_APO", "ValidFrom": "30A", "ValidTo": "30A", "Number": "460376 ", "URL": "/notes/460376 ", "Title": "Complete deletion of planning versions", "Component": "SCM-APO-MD-VM"}, {"SoftwareComponent": "SAP_APO", "ValidFrom": "30A", "ValidTo": "30A", "Number": "499118 ", "URL": "/notes/499118 ", "Title": "Deletion of report variants", "Component": "SCM-APO-SNP-BF"}, {"SoftwareComponent": "SAP_APO", "ValidFrom": "30A", "ValidTo": "30A", "Number": "542818 ", "URL": "/notes/542818 ", "Title": "Planning version management: Delete active version", "Component": "SCM-APO-MD-VM"}, {"SoftwareComponent": "SAP_APO", "ValidFrom": "30A", "ValidTo": "30A", "Number": "614314 ", "URL": "/notes/614314 ", "Title": "Interval fixing is ignored", "Component": "SCM-APO-MD-VM"}, {"SoftwareComponent": "SAP_APO", "ValidFrom": "30A", "ValidTo": "30A", "Number": "638908 ", "URL": "/notes/638908 ", "Title": "Simsessions are not deleted", "Component": "SCM-APO-MD-VM"}, {"SoftwareComponent": "SAP_APO", "ValidFrom": "30A", "ValidTo": "30A", "Number": "640543 ", "URL": "/notes/640543 ", "Title": "Planning version management: deleting report variants", "Component": "SCM-APO-MD-VM"}, {"SoftwareComponent": "SAP_APO", "ValidFrom": "30A", "ValidTo": "30A", "Number": "640552 ", "URL": "/notes/640552 ", "Title": "Planning version mgt: Background jobs for planning area", "Component": "SCM-APO-MD-VM"}, {"SoftwareComponent": "SAP_APO", "ValidFrom": "30A", "ValidTo": "30A", "Number": "757388 ", "URL": "/notes/757388 ", "Title": "Plan.version:Duplicate source code in DM_DELETE_VERSIONLCDB", "Component": "SCM-APO-MD-VM"}, {"SoftwareComponent": "SAP_APO", "ValidFrom": "30A", "ValidTo": "30A", "Number": "900730 ", "URL": "/notes/900730 ", "Title": "/SAPAPO/ATP_ALL_SD_ORDERS_DEL deletes across clients", "Component": "SCM-APO-ATP"}, {"SoftwareComponent": "SAP_APO", "ValidFrom": "30A", "ValidTo": "310", "Number": "460348 ", "URL": "/notes/460348 ", "Title": "Complete deletion of planning versions", "Component": "SCM-APO-MD-VM"}, {"SoftwareComponent": "SAP_APO", "ValidFrom": "310", "ValidTo": "310", "Number": "499118 ", "URL": "/notes/499118 ", "Title": "Deletion of report variants", "Component": "SCM-APO-SNP-BF"}, {"SoftwareComponent": "SAP_APO", "ValidFrom": "310", "ValidTo": "310", "Number": "614314 ", "URL": "/notes/614314 ", "Title": "Interval fixing is ignored", "Component": "SCM-APO-MD-VM"}, {"SoftwareComponent": "SAP_APO", "ValidFrom": "310", "ValidTo": "310", "Number": "638908 ", "URL": "/notes/638908 ", "Title": "Simsessions are not deleted", "Component": "SCM-APO-MD-VM"}, {"SoftwareComponent": "SAP_APO", "ValidFrom": "310", "ValidTo": "310", "Number": "900730 ", "URL": "/notes/900730 ", "Title": "/SAPAPO/ATP_ALL_SD_ORDERS_DEL deletes across clients", "Component": "SCM-APO-ATP"}, {"SoftwareComponent": "SCM", "ValidFrom": "400", "ValidTo": "400", "Number": "614314 ", "URL": "/notes/614314 ", "Title": "Interval fixing is ignored", "Component": "SCM-APO-MD-VM"}, {"SoftwareComponent": "SCM", "ValidFrom": "400", "ValidTo": "400", "Number": "638908 ", "URL": "/notes/638908 ", "Title": "Simsessions are not deleted", "Component": "SCM-APO-MD-VM"}, {"SoftwareComponent": "SCM", "ValidFrom": "400", "ValidTo": "400", "Number": "659784 ", "URL": "/notes/659784 ", "Title": "Create/delete a version without liveCache", "Component": "SCM-APO-MD-VM"}, {"SoftwareComponent": "SCM", "ValidFrom": "400", "ValidTo": "400", "Number": "669026 ", "URL": "/notes/669026 ", "Title": "Dump during the deletion of a planning version", "Component": "SCM-APO-MD-VM"}, {"SoftwareComponent": "SCM", "ValidFrom": "400", "ValidTo": "400", "Number": "683948 ", "URL": "/notes/683948 ", "Title": "Dump during deletion of a planning version", "Component": "SCM-APO-MD-VM"}, {"SoftwareComponent": "SCM", "ValidFrom": "400", "ValidTo": "400", "Number": "686558 ", "URL": "/notes/686558 ", "Title": "Abnormal termination of a planning version copy to existing", "Component": "SCM-APO-MD-PDS"}, {"SoftwareComponent": "SCM", "ValidFrom": "400", "ValidTo": "400", "Number": "719563 ", "URL": "/notes/719563 ", "Title": "Time series anchors not deleted", "Component": "SCM-APO-MD-VM"}, {"SoftwareComponent": "SCM", "ValidFrom": "400", "ValidTo": "400", "Number": "900730 ", "URL": "/notes/900730 ", "Title": "/SAPAPO/ATP_ALL_SD_ORDERS_DEL deletes across clients", "Component": "SCM-APO-ATP"}, {"SoftwareComponent": "SCM", "ValidFrom": "410", "ValidTo": "410", "Number": "719563 ", "URL": "/notes/719563 ", "Title": "Time series anchors not deleted", "Component": "SCM-APO-MD-VM"}, {"SoftwareComponent": "SCM", "ValidFrom": "410", "ValidTo": "410", "Number": "736984 ", "URL": "/notes/736984 ", "Title": "Planning version: Planning version copy log", "Component": "SCM-APO-MD-VM"}, {"SoftwareComponent": "SCM", "ValidFrom": "410", "ValidTo": "410", "Number": "772729 ", "URL": "/notes/772729 ", "Title": "Time series does not exist", "Component": "SCM-APO-FCS"}, {"SoftwareComponent": "SCM", "ValidFrom": "410", "ValidTo": "410", "Number": "828326 ", "URL": "/notes/828326 ", "Title": "Influence planning version initialization on alert monitor", "Component": "SCM-BAS-AMO"}, {"SoftwareComponent": "SCM", "ValidFrom": "410", "ValidTo": "410", "Number": "840860 ", "URL": "/notes/840860 ", "Title": "RSP objects are not deleted", "Component": "SCM-APO-MD-VM"}, {"SoftwareComponent": "SCM", "ValidFrom": "410", "ValidTo": "410", "Number": "843585 ", "URL": "/notes/843585 ", "Title": "Version copy: Long runtimes when deleting time series", "Component": "SCM-APO-FCS"}, {"SoftwareComponent": "SCM", "ValidFrom": "410", "ValidTo": "410", "Number": "849683 ", "URL": "/notes/849683 ", "Title": "Copying a planning version: Deleting SNP variants", "Component": "SCM-APO-MD-VM"}, {"SoftwareComponent": "SCM", "ValidFrom": "410", "ValidTo": "410", "Number": "900730 ", "URL": "/notes/900730 ", "Title": "/SAPAPO/ATP_ALL_SD_ORDERS_DEL deletes across clients", "Component": "SCM-APO-ATP"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}