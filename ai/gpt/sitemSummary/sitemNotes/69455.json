{"Request": {"Number": "69455", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 648, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014495352017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000069455?language=E&token=185840888CA0E8E5614BC84035FE2E9A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000069455", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000069455/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "69455"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 291}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.11.2023"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SDD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Service Data Download"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Data Download", "value": "SV-SMG-SDD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SDD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "69455 - Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>As preparation for a SAP EarlyWatch/GoingLive session you want to implement the application-specific tools from SAP Support (Application monitor ST14, RTCCTOOL, ST12 and application specific SDCC data collectors).</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>ST-A/PI \"Service Tools for Applications Plug-In\",<br />SAP GoingLive, SAP EarlyWatch, Service preparation, RTCCTOOL,<br />Application monitor, Logistics Monitor, ST14,<br />Analyis&amp;Service Tools Launchpad, ST13<br />Single transaction analysis, ABAP Trace for EarlyWatch/GoingLive, ST12,<br />ST-A/PI data collectors for SDCCN, Online collectors</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>As of SAP release 4.0B, ST14 monitor, transactions ST12 and ST13, program RTCCTOOL and application specific data collectors for Solution Manager and SDCC are delivered in one addon ST-A/PI \"Service Tools for Applications Plug-In\".<br />Note that the ST14 monitor and transactions ST12 and ST13 are not officially documented and are only released for use by SAP or certified Service consultants during SAP Service Sessions (for example SAP GoingLive Check or Solution Management Optimization Services). They are only available in English.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Contents</strong></p>\r\n<p>&#160;</p>\r\n<p>I.&#160;&#160;&#160;&#160; Implementation of addon ST-A/PI (for SAP releases &gt;= 4.0B)<br />II.&#160;&#160;&#160; Implementation of supportpackages for addon ST-A/PI<br />III.&#160;&#160; Question&amp;Answer section</p>\r\n<p>As of 12th Sept 2023, new certificates 01V for RTCCTOOL digital content verificationhave become available.<br />PSE file SAP_AGS_OLCNT_VERIFY.pse gets usually updated automatically.&#160;Only if RTCCTOOL functionality would be called with a low privilege user, this update might fail and an error &#8220;<strong>Update of PSE failed</strong>&#8221; would be shown. In that case it should be sufficient just to run RTCCTOOL with a&#160;<strong>sysadmin user</strong>.</p>\r\n<p><strong>I. Implementation of addon ST-A/PI (for SAP releases &gt;= 4.0B)</strong></p>\r\n<p>1. Select the proper ST-A/PI release</p>\r\n<p><strong>Note:</strong></p>\r\n<p>1) As of&#160;6th Nov 2023 a new addon support package <strong>ST-A/PI 01V* SP03 </strong>has become available.<br />[Note that support packages are downloaded in SAP Support Lauchpad via '<strong>Support packages&amp;patches</strong>', whereas&#160;addon installations for the release have to be downloaded via '<strong>Installations/Upgrades</strong>'.]</p>\r\n<p>2) As of 2020 new deliveries <strong>for lower basis release 640</strong> are discontinued. These customer should implement ST-A/PI 01T_640 SP3.<br />As of 2014 new deliveries for lower basis releases &lt;= 620 are discontinued. These customers should implement ST-A/PI 01Q* SP02 plus any note corrections recommended by RTCCTOOL.</p>\r\n<p>Use the matrix below to find the exact release (only 1) which is valid for your system. The release only depends on the SAP basis release. Application-dependent coding is included in comments (*) and is uncommented automatically during import.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p>SAP release<br />(SAP_BASIS)</p>\r\n</td>\r\n<td>further decisions based on installed<br />software components (table CVERS)</td>\r\n<td>Addon<br />Release&#160;&#160;&#160;&#160;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160; 7.31<br />&#160; 7.40<br />&#160;&#160;7.50<br />&#160; 7.51<br />&#160; 7.52<br />&#160; 7.53<br />&#160; 7.54<br />&#160; 7.55<br />&#160; 7.56<br />&#160; 7.57<br />&#160; 7.6x</p>\r\n</td>\r\n<td>&#160;for all systems on basis 7.31-7.90</td>\r\n<td>&#160;01V_731</td>\r\n</tr>\r\n<tr>\r\n<td>&#160; 7.10<br />&#160; 7.11<br />&#160; 7.20<br />&#160; 7.30</td>\r\n<td>&#160;for all systems on basis 7.10-7.30<br />&#160;&#160;&#160; (Netweaver 2007)</td>\r\n<td>&#160;01V_710</td>\r\n</tr>\r\n<tr>\r\n<td>&#160; 7.00<br />&#160; 7.01<br />&#160; 7.02</td>\r\n<td>&#160;for all systems on basis 7.00-7.02</td>\r\n<td>&#160;01V_700</td>\r\n</tr>\r\n<tr>\r\n<td>&#160; 6.40</td>\r\n<td>&#160;for all systems on basis 640/Netweaver04</td>\r\n<td>&#160;01T_640</td>\r\n</tr>\r\n<tr>\r\n<td>&#160; 6.20</td>\r\n<td>&#160;for all systems on 6.20 basis</td>\r\n<td>&#160;01Q_620</td>\r\n</tr>\r\n<tr>\r\n<td>&#160; 6.10</td>\r\n<td>&#160;for all systems on 6.10 basis</td>\r\n<td>&#160;01Q_610</td>\r\n</tr>\r\n<tr>\r\n<td>&#160; 4.6D</td>\r\n<td>&nbsp;</td>\r\n<td>&#160;01Q_46D</td>\r\n</tr>\r\n<tr>\r\n<td>&#160; 4.6C</td>\r\n<td>&nbsp;</td>\r\n<td>&#160;01Q_46C</td>\r\n</tr>\r\n<tr>\r\n<td>&#160; 4.6B</td>\r\n<td>&nbsp;</td>\r\n<td>&#160;01Q_46B</td>\r\n</tr>\r\n<tr>\r\n<td>&#160; 4.5B</td>\r\n<td>&nbsp;</td>\r\n<td>&#160;01Q_45B</td>\r\n</tr>\r\n<tr>\r\n<td>&#160; 4.0B</td>\r\n<td>&nbsp;</td>\r\n<td>&#160;01Q_40B</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br /><br />2. Implementation</p>\r\n<p>a) Verify SPAM/SAINT patch level<br />The only prerequisite for ST-A/PI is a minimum SPAM/SAINT level<br />&#160;&#160; -&#160;39&#160;&#160; for basis 700-702<br />&#160; &#160;- 11&#160; &#160;for basis 700-730<br />&#160; &#160;- 20&#160; &#160;for basis &gt;= 731</p>\r\n<p>b) Download of ST-A/PI</p>\r\n<p>Open&#160;<a target=\"_blank\" href=\"http://support.sap.com/supporttools\">http://support.sap.com/supporttools</a> and click on 'Installations/Upgrades' below 'ST-A/PI' in the right-hand info. Select the proper addon release and 'Installation'. When the addon is displayed in the left area, click on 'Title' and&#160; download the file to your frontend PC. File size is 34-69 MB.<br />Also ST-A/PI addons for SAP Basis 700 and higher should be downloaded in the above way (only its support packages have to be donwloaded via maintenance optimizer).</p>\r\n<p>c) Implementation with SAINT<br />ST-A/PI is&#160;easy to&#160;apply and does not interfere with productive coding. It can be implemented anytime and there is no necessity to logoff users. Warning popups regarding open BW data extraction requests can be ignored because the addon does not contain any BI data structures.<br />Call transaction SAINT in client 000. Choose 'Installation Package-&gt;Load Package-&gt;From frontend' to upload the addon file. (*)<br />The addon should now appear in the table control 'Installable addons', on the entry screen or after pressing the 'Start' button. EBP/CRM 4.00 and CRM 5.00-7.14 customers have to use the special addon manager via the menu. Mark the line with 'ST-A/PI' and choose 'Continue' several times to start the installation process. Installation takes about 15 minutes. Confirm with 'Finish'.<br />After implementation it is recommended to run program RTCCTOOL \"Service preparation check\".</p>\r\n<p>(*) Tip: For subsequent systems which share the same transport directory, it is quicker to press the 'Load' button in SAINT and F3 'Back', i.e. upload from application server instead of from frontend.<br />Wait until installation finishes before starting the import into the next system.</p>\r\n<p>d) Procedure after addon implementation</p>\r\n<p>As of ST-A/PI 01P* this step has been automated by an XPRA.</p>\r\n<p>ST-A/PI contains analysis coding in comments (*) for application code like ERP/S/4 or smaller software components (like BW, PI, SRM_SERVER, IBU Banking, FM/EA-PS, SEM-BW or IS-Utilities) or commented checks that require certain (basis) supportpackage levels (e.g. SDCC data collectors for MaxDB, DB2, DB6, Oracle). Conditions when the coding has to be uncommented are delivered together with the addon. From ST-A/PI 01P* this uncomment runs automatically as an XPRA during import. Runtime for the XPRA is 1-3 min. The uncomment procedure can also be started manually by starting report RTCCTOOL from SE38. If any analysis coding is to be uncommented RTCCTOOL will recommend this (takes two clicks from inside RTCCTOOL).</p>\r\n<p>When changing from old ST-A/PI &lt;= 01Q* to ST-A/PI &gt;= 01R* a migration of table contents is triggered once (/SSF/PTAB-&gt;/SSF/BTAB). Reason is better performance and smaller data size (factor up to 15) due to digital compression in /SSF/BTAB. Usual runtime is &lt; 1 min. However on&#160;a few&#160;customer systems that are heavily using the BPMon Business Process Monitoring functionality in SAP Solution Manager, table conversion can take 30 min or even more. Check the table size of /SSF/PTAB, conversion speed is roughly 1 GB per&#160;8 min. <br />Conversion runs&#160;in an asynchronous dialog process [In the unlikely case /SSF/PTAB contains single packed objects larger&#160;than 500 MB&#160;an additional&#160;background job may be triggered]. During system upgrade conversion is suppressed, in this case it is triggered automatically after upgrade \"on first usage\" of any ST-A/PI functionality. <br />Until the conversion is finished, ST-A/PI functionalities cannot be used, the processes will sleep/cancel after 5 min until conversion has finished. For very few large customers with heavy BPMon usage this may delay data collection. On the other hand those are the same customers that will most benefit from the data compression in the new table /SSF/BTAB.</p>\r\n<p>3. Upgrade behaviour</p>\r\n<p>If you are already on the newest ST-A/PI release 01U* *and* the target basis would require the very same ST-A/PI (e.g. upgrade basis&#160; 740-&gt;751) then</p>\r\n<p style=\"padding-left: 30px;\">a) choose \"<strong>KEEP</strong>\".</p>\r\n<p>Else as of ST-A/PI 01N* provide either the normal addon installation or - the exchange upgrade package:</p>\r\n<p style=\"padding-left: 30px;\">b) If your upgrade source system is&#160;<br />&#160;&#160; ERP &gt;= 6.0 or S/4 Hana<br />&#160;&#160; CRM/SCM/SRM &gt;= 7.0<br />&#160;&#160; NetWeaver &gt;= 7.0&#160;(except upgrades to/from basis 7.10-7.30)<br />then the normal <strong>addon installation</strong> is required.</p>\r\n<p style=\"padding-left: 30px;\">c) In other - rather rare - cases the <strong>exchange upgrade</strong> package is the proper choice. The upgrade exchange packages are<br />&#160;&#160; 01V_700&#160;&#160; SAPK-01VA3INSSA<br />&#160;&#160; 01V_710&#160;&#160; SAPK-01VB3INSSA<br />&#160;&#160; 01V_731&#160;&#160; SAPK-01VC3INSSA</p>\r\n<p>In case of doubt it is never wrong to provide even both installation and exchange upgrade package, the upgrade will pick the right one. <br />Put the upgrade package(s) into the EPS inbox prior to the IS_SELECT phase. In IS_SELECT choose \"<strong>Upgrade with SAINT Package</strong>\".</p>\r\n<p>[For the upgrade behaviour of prior ST-A/PI releases &lt;= 01M*</p>\r\n<p style=\"padding-left: 30px;\">d) see SAP note 1570913.</p>\r\n<p>In the very rare case of a upgrade to a basis older than 640</p>\r\n<p style=\"padding-left: 30px;\">e) choose \"DELETE\":<br />This case should be rare. Since no upgrade packages are provided, choose \"Passive deletion\" (= \"Delete the Addon. The tables are not deleted on the DB except if they are empty\") whenever the upgrade asks you about<br />ST-A/PI. Like this the addon ST-A/PI gets lost just like standard coding during system upgrade.<br />After upgrade you may find messages like \"Table /SSF/.. exists without DDIC reference\" concerning service tool tables in the CHK_POSTUP phase or in DB02. These are harmless and most easily solved by implementing<br />ST-A/PI on the new release. Then run report RTCCTOOL and follow the recommendation 'Procedure after upgrade' (takes 2 clicks from inside the tool).]</p>\r\n<p><br />4. Combination with other add-ons<br />All combinations of ST-A/PI with all other addons are released. No conflicts with any other support package or addon.</p>\r\n<p>5. Impacted functionalities<br />To prevent any possibility of conflicts or interference with productive transactions, ST-A/PI objects are in independent separate namespaces /SSF/+/SSA/ (transaction codes ST12-ST14 and program RTCCTOOL are the only exception to this rule).<br />Therefore implementation of ST-A/PI cannot affect your business transactions and does not require a functional retest. The only areas that are updated are data collection for SAP Solution Manager (e.g. EarlyWatch, BPM in SolMan), transactions ST12-ST14 and RTCCTOOL.<br />As explained in section 2d) the table conversion /SSF/PTAB-&gt;/SSF/BTAB never runs during system upgrade with SUM/R3up and thus does not impact downtime.</p>\r\n<p>6. Addon content info<br />The <strong>RTCCTOOL \"Service preparation check\"</strong> checks the availability of required tools for a SAP service session. Run the report from SE38. The report connects to SAP Support Portal or SAPNet R/3 and fetches a list of recommendations and notes for your system configuration. It checks whether they are implemented successfully and presents a list of missing addons and notes. The list combines all necessary information such as SAP Notes, implementation status, short description &amp; implementation text and transport logs. Helpful procedures regarding servicetools before/after upgrade are integrated into the tool and it can provide help for transport implementation.<br />As of ST-A/PI 01T* SP01 RTCCTOOL no longer creates its own RFC destination SAPNET_RTCC (copy of SAPOSS). Instead it reuses standard connectivity to SAP using HTTP destination SAP-SUPPORT_PORTAL to SAP Support Backbone. If those would not work RTCCTOOL automatically tries indirect alternatives: via your Solution Manager system, via FRUN, via RFC destinations entered in SDCCN.<br />As of ST-A/PI 01L* the RTCCTOOL will also run in background during SDCC data collection. Service preparation recommendations are then visible also in the EWAlert report (from ST-SER 2008_2). A \"Settings\" popup allows to show/hide additional recommendations for specific services (like e.g. Business Process Monitoring or Data volume management). Activate those settings on request from SAP Support to prepare for an upcoming service.</p>\r\n<p>As of April 2010 RTCCTOOL recommendations come with <strong>digital signatures</strong> attached. As of ST-A/PI 01M* you can activate digital verification of these signatures to make sure only authentic non-manipulated recommendations signed by SAP can be shown in RTCCTOOL. A PSE file SAP_AGS_OLCNT_VERIFY.pse will be created in DIR_INSTANCE/sec and an entry SAPAGS in transaction STRUST in client 000 (on SAP basis &gt;= 620). The necessary certificates will be downloaded, verified and put into the same PSE file automatically. Fresh certificates may be added about once per year. Only digital verification is done, no cryptography. As of ST-A/PI 01U* SP01 this step also runs automated \"on first usage\".<br /><strong>Online input for data collection</strong>: As of ST-A/PI 01N* the same RTCCTOOL update mechanism delivers online input for certain data collectors that are generic, but their degree of freedom is limited in order to be secure. E.g. the DVM (data volume management) analysis tool receives an up-to-date list of what table(-field)s to analyze. The most advanced of these are SDCCN collector for Oracle and for HANA&#160;that can do selects on&#160;system tables based on online input. Examples for input are the automated Oracle parameter check (SAP note 1171650), the&#160;Oracle mini checks (SAP note 1695354) or the HANA mini checks (SAP note&#160;2517313). The input SQL statements are a) digitally verified to come from SAP, b) security-checked to be strictly read-only (\"set transaction read-only\", syntaxcheck in brackets where 1=0&#160; etc.), c) are logged (tx ST13-&gt;ONLINE_COLLECTORS_EXE_LOG). Refresh period for this online input is 7 days, but it is also refreshed when you choose \"List-&gt;Refresh from SAP\" in RTCCTOOL.<br /><strong>Online collectors for BPMon/BPAnalytics data collection: </strong>As of ST-A/PI 01R* SP02 \"Online monitors for BPMon/BPAnalytics\" are available. They are also based on same RTCCTOOL update mechanism from SAP Support Backbone. For these you can decide via transaction ST13-&gt;TBI_REPORTS_ONLINE_MONITORS_ADMI whether they should be updated automatically, or only manually plus on ST-A/PI update. As they are guaranteed read-only and digitally signed, we regard them as generally safe and recommend to choose \"Update automatically\" to have always the newest and best version.<br />Online monitors are written in an restricted ABAP-like read-only language. See next paragraph for more details.<br /><strong>Online collectors for SDCCN and SAP SolMan data collection</strong>: As of ST-A/PI 01T* SP0 online collectors collect data for SDCCN data collection (e.g. EarlyWatch, EWAlert) or for SolMan (e.g. SolMan workcenter for DVM). <br />This kind of online input is written in a restricted ABAP-like read-only language. Technically it is never passed to ABAP runtime. Instead it is a collector in addon ST-A/PI that receives it as input, parses it and processes it statement-wise. This design allows to assure a strict read-only, and to control what commands are allowed. Currently these are database selects SELECT,&#160;calls to allow-listed read-only function modules and subsequent processing on internal data (calculations, conditionals IF, statements on internal tables). Digital content verification assures the content is from SAP (must be switched on in report RTCCTOOL as a precondition).<br />For convenience and to receive always the latest content and corrections, we recommend to allow automatic update. Drawbacks of this are theoretically:<br />-&gt; a flawed correction might temporarily lead to missing data for an active monitor. We expect such cases to be resolved again quickly.<br />-&gt; a functional correction to the where-clause (e.g. adding \"where type = A or B\") might slow down a join select if DB chooses another access path.<br />However both cases should occur very rarely.&#160;<br /><br />In Service preparation check <strong>RTCCTOOL</strong>&#160;via menu <strong>Goto-&gt;Online collectors </strong>you can decide whether to allow or disable online collectors.&#160;<br />From ST-A/PI 01U* SP01 the \"Allow online collectors\" setting can be transported.<br />In tx ST13 -&gt;&#160;ONLINE_COLLECTORS_DEMO you see in detail how online collectors work, how online content commands are processed statement-wise by command form routines.<br />In tx ST13 -&gt;&#160;ONLINE_COLLECTORS_EXE_LOG you see all online collector versions that have been executed in your system at least once.<br />In&#160;RTCCTOOL List-&gt;Other utilities-&gt;\"Locks&amp;performance of online coll. calls\" you can check runtimes of online collectors, or automatic locks on collectors in case of two consecutive dumps.<br />SAP note&#160;2530502 contains up-to-date info what data are collected by online collectors.</p>\r\n<p><strong>RSECNOTE </strong>\"SAP Security Notes Check\"&#160;(SAP note 888889) checked security notes. However the tool was replaced by system recommendations (see SAP note 1890782).<br /><br />The <strong>ST14 application monitor</strong> is mainly used during SAP GoingLive session. Analysis batch jobs collect performance-relevant key figures such as document statistics and customizing settings, the analysis results can be viewed as a tree and downloaded to a service session. The addons contains analyses for R/3 standard (SD PP MM CO Retail FI PS TR CA Data management FI-Funds Management), for APO and APO R/3 backend, for EBP/CRM and for industry solutions Banking, Utilities, Healthcare, IS-Public Sector Strategic Enterprise Management. From transaction <strong>ST13 \"Analysis&amp;Servicetools launchpad\"</strong> the ITS trace viewer tool or analysis reports for EBP, APO Livecache, R/3 variant configuration or CO/FI can be started.Transaction <strong>ST12 \"ABAP trace for EarlyWatch/GoingLive\"</strong> allows to run ABAP traces like transaction SE30, but offers enhanced possibilities to switch on the trace (trace activation for another user, for incoming RFC; kernelpatch is required for this) or to evaluate the ABAP trace (buttom-up/top-down call hierarchy, internal table names etc.). ST12 is described in note 755977.<br />ST-A/PI also contains new data collectors for transaction SDCC which are relevant for SAP EarlyWatch/GoingLive/EarlyWatch Alert downloads. The addon contains <strong>SDCC data collectors</strong> for BW, SCM, CRM, XI, Security checks and databases.</p>\r\n<p><br /><strong>II. Implementation of supportpackages for addon ST-A/PI</strong></p>\r\n<p><br /><strong>Note: <strong>As of&#160;6th Nov 2023 a new addon support package SP03&#160;<strong>for the current version&#160;</strong>ST-A/PI 01V* has become available.</strong><br /></strong>For the previous 01U*&#160;version (&gt;= basis 700) support packages up to level SP03 are available.<strong><br /></strong>For ST-A/PI 01T_640 which is valid for customers on basis 640 a support package SP03 is available.<br />For the 01Q*&#160;version, which is the valid version for customers &lt;= basis 620,&#160;a supportpackage SP02 is available.</p>\r\n<p>a) Download of the ST-A/PI supportpackage<br />Path to the download is&#160;<a target=\"_blank\" href=\"http://support.sap.com/supporttools\">http://support.sap.com/supporttools</a> and click on 'Support packages' below 'ST-A/PI'&#160;on the right-hand.<br />[Note that Addon installation and support packages are NOT found on the same download page but are different SWDC pages.].<br />File size is 29-52 MB. Supportpackages for SAP Basis 700 and higher have to be put to download basket and released via maintenance optimizer.</p>\r\n<p>b) Implementation<br />Upload the support package to transaction SPAM, define a queue for 'ST-A/PI' and import. Import takes about 8 min.</p>\r\n<p>************************************************************************</p>\r\n<p><br /><strong>III. Questions&amp;Answers section</strong></p>\r\n<p>&#160;</p>\r\n<p><strong>Q1:</strong> about ST-A/PI releases ?<br /><strong>A1:</strong> As of version 01P* all ST-A/PI releases depend only on the SAP basis release.</p>\r\n<p><strong>Q2:</strong> how to delete an invalid ST-A/PI release from the SAINT 'Installable addons' overview ?<br /><strong>A2:</strong> In transaction SEPS choose Goto-&gt;Inbox and delete the addon file.</p>\r\n<p><strong>Q3:</strong> about SPAM/SAINT update ?<br /><strong>A3:</strong> To check the SPAM/SAINT level call transaction SPAM and look at the version in the title line. To implement a SPAM/SAINT update, open in your web browser <a target=\"_blank\" href=\"http://support.sap.com/patches\">http://support.sap.com/patches</a>, choose \"Browse our download catalog\" -&gt; \"Additional components\", drill down on 'SPAM/SAINT UPDATE' and download the files for the your basis release. In your system call transaction SPAM, choose 'Support package -&gt;Load package-&gt;From frontend' to upload the file. Choose 'Support Package-&gt; Import SPAM update' to start the import.</p>\r\n<p><strong>Q4:</strong> old servicetools transport implemented after addon ST-A/PI ?<br /><strong>A4:</strong> If you implement an old servicetool transport after implementation of ST-A/PI, either because supportpackagelevels for ST-PI are not met (note 560630) or by error, execute the following afterwards:<br />Run report /SSF/SAO_UTILS from SE38. On the selection screen choose 'Redirect entry points (ST14, RTCCTOOL)' and press 'Execute'.</p>\r\n<p><strong>Q5:</strong> on Solution Manager system ?<br /><strong>A5:</strong> The addon ST-A/PI should be implemented on the Solution Manager as well as on the satellite systems.<br /><br /><strong>Q6:</strong> when calling report RTCCTOOL only a not very detailed 5 lines output shows up like for example<br />&#160;&#160;&#160;&#160;&#160; \"Existence Text for Required TCC Tools\"<br />&#160;&#160;&#160;&#160;&#160; \"Version 03-12-1998\"<br />&#160;&#160;&#160;&#160;&#160; \"Note 091488 missing\"<br />&#160;&#160;&#160;&#160;&#160; \"Note 116095 missing\"&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; ?<br /><strong>A6:</strong> This is a very old version of RTCCTOOL which exists in basis standard as of 4.6A but which is no longer valid nor helpful.<br />Implement the above addon to get a valid version of RTCCTOOL.</p>\r\n<p><strong>Q7:</strong>&#160;Error during Dictionary distribution or Import phase:&#160;\"Inconsistency DD &lt;-&gt; DB\", \"SSF/..\" does not exist in nametab\".<br /><strong>A7:</strong>&#160;This may occur on the first ST-A/PI implementation after a combined upgrade with unicode conversion from basis &lt;= 620. On the first error press \"Continue\" in SAINT to proceed to the Main Import phase where SAINT stops again. Now run the newly imported utility report /SSF/SSF_TAB_ACT from SE38. After this SAINT finishes without error.</p>\r\n<p><strong>Q8:</strong> deactivate single ST14 analysis subroutine ?<br /><strong>A8:</strong> An ST14 analysis job calls a sequence of analysis subroutines. If one subroutine fails, proceed ad follows: from the short dump get the subroutine name (e.g. AFI_FI_SCAN_T685). Run report /SSA/AXM from SE38. On the selection screen, enter the first 3 letters of the subroutine name into parameter P_APPL and execute. In the left section 'ST14 customizing' choose 'Mapping: Assign analy. subroutines to GRPIDs' and press the button 'Change'. In the following tablecontrol deselect the 'Active' checkbox for analysis subroutine that should be skipped and press 'Save'.<br />Then schedule a new analysis from ST14.</p>\r\n<p><strong>Q9:</strong> Procedure for customers<br />a) using an internet VPN/SNC connection to sapserv1 or sapserv2<br />b) without RFC connection to sapserv?<br /><strong>A9:</strong><br />a) Customers with internet VPN/SNC connection cannot download transport files from sapserv via FTP. However, the most important service tools are available in SAP&#160;Support Portal&#160;<a target=\"_blank\" href=\"http://support.sap.com/supporttools\">http://support.sap.com/supporttools</a>. These are especially the Addons for service preparation ST-PI (from note 116095) and ST-A/PI (this note 69455). Also many transport files for database monitors are available as attachment to SAP notes in SAP Services Marketplace. RFC connection to SAP is possible with VPN/SNC, so RTCCTOOL and ST14 download work as usual.<br />b) Customers without RFC connection to SAP can also get the tools for service preparation from SAP Services Marketplace. However, the service preparation check has to be done manually from note 91488 because RTCCTOOL does not work without RFC connection to SAP. Also the 'Procedure after implementation of addon ST-A/PI' (this note) is different: instead of starting RTCCTOOL, these customers should run report /SSF/SAO_UTILS from SE38 with option 'Uncomment/Recomment analysis coding for additional components'.<br />To transfer ST14 analyses from such a system, the textfile download (see Q10 below) should used.</p>\r\n<p><strong>Q10:</strong> Textfile download for ST14 analyses ?<br /><strong>A10:</strong> Choose transaction ST14, Utilities-&gt;Analysis browser. From the menu of the analysis browser you have the possibility to export a chosen 'GUID based' analysis to your frontend, or to upload a textfile with such an analysis to the system. Since the GUI menu is not completely translated to English in some of the current ST-A/PI addons, here the path: It is the first menu&#160; in the analysis browser and normally reads<br />&#160; Download &gt; Download<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Textfile download &gt; Export to frontend<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Import from frontend</p>\r\n<p><strong>Q11:</strong> Error analysis for SDCC data collectors in ST-A/PI ?<br /><strong>A11: </strong>As of ST-A/PI 01U* SP1 error handling for those has been improved. A dump in one collector does not cause missing data for other collectors anymore. <br />The data collector that caused the dump can be determined from via transaction ST13-&gt;SDCCVIEWER -&gt; display download, expand nodes XAD-&gt;DATACOLL_MSG_LOG-&gt;SUBROUTINE_LOGS -&gt; collector without \"Completed\" flag. Or for online collectors:&#160;NXS-&gt;RECOMMENDATION-&gt;RUNTIME_LOG, look for log message \"Dump/Abortion\". History about dumps is also kept in RTCCTOOL-&gt;List-&gt;Other utilities-&gt;Locks&amp;Performance of online coll. calls -&gt; Column \"Not finished\" or button \"Last 5 calls\". There you can also delete locks on online collectors.<br />Information about dumping ST-A/PI collectors will also be collected in SDCC downloads and is monitored SAP internally.</p>\r\n<p>[In older ST-A/PI releases two error situations were possible:<br />a) short dumps in one data collector<br />b) customer forgot to uncomment data collectors<br />In case a) an error message would appear in the download message log in transaction SDCC for the function module SSF_COLLECT_DATA_FOR_SDCC. In such a case also the data from other SDCC data collectors in ST-A/PI are missing.<br />Proceed as follows:<br />-&gt; repeat the SDCC data collection max. two more times, until the error message has disappeared from the SDCC message log. The erroneous data collector is locked after two short dumps, so the third execution should run through and contain data from the other SDCC data collectors in ST-A/PI.<br />-&gt; open a customer message on&#160;SV-SMG-MON-BPM to inform the developer about the short dump.<br />To detect locked data collectors in a download, use the \"Viewer for ST-A/PI data in the SDCC download\". It can be reached via transaction ST13-&gt;SDCCVIEWER, or transaction DCT_STAPI_V in the SAP internal service system CTP. Click on the nodes &lt;context&gt;-&gt;XAD-&gt;DATACOLL_MSG_LOG-&gt;SUBROUTINE_LOGS if available.<br />A message 'Lock entry DEACT exists' means that the collector was locked due to short dumps. To unlock a data collector, use ST13-&gt;PROJBROWSER.<br />An error of type b) can happen if the customer forgets the 'Procedure after implementation of addon ST-A/PI' described some pages above.<br />To detect an error case b) in the download, look into the same SUBROUTINE_LOGS as for error case a), but look for a message 'Lock entry CMTD exists'. In such a case the customer simply has to run report RTCCTOOL and follow the recommendation to uncomment the analysis coding (takes two clicks from inside RTCCTOOL). Info about the ST-A/PI release in the download can be found in the same viewer under path DATA_PER_R3SYSTEM-&gt;DCA-&gt;GENERAL.]</p>\r\n<p><strong>Q12:</strong> Known problems with SDCC data collectors in ST-A/PI ?<br /><strong>A12:</strong> The following data collectors of the older 01F* version may cause dumps during data collection in certain system configurations:<br />&#160;&#160;&#160; Form DDB_ORA_GET_UNDO_STATS<br />This can occur on an APO/SCM system with Oracle database. The error is caused by other data collectors that run before and do not reset the DB connection. Therefore the solution is to deactivate the following two data collectors:<br />&#160;&#160;&#160; Form DAP_LC_SEL_CONFIGURATION<br />&#160;&#160;&#160; Form DAP_LC_SEL_CLASSCONTAINERS_FCT<br />To deactivate them, run report /SSA/DXM from SE38.<br />Enter 'DAP' for parameter P_PROJID.<br />Select 'Declare subroutines for SDCC download' and press 'Change'.<br />Look for the two subroutines in the list and deselect the 'Active' checkbox for these two subroutines. Then press 'Save'.<br />Then the two data collectors will not be called anymore by SDCC. These errors will be corrected in the next version of ST-A/PI.</p>\r\n<p><strong>Q13:</strong> Transport names of current addons<br /><strong>A13:</strong> Transport name&#160;&#160;&#160;&#160;&#160; Addon&#160;&#160;&#160;&#160;&#160; Release<br />&#160;&#160;&#160; SAPKITAR1J&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; ST-A/PI&#160;&#160;&#160; 01Q_40B<br />&#160;&#160;&#160; SAPKITAR2J&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; ST-A/PI&#160;&#160;&#160; 01Q_45B<br />&#160;&#160;&#160; SAPKITAB1J&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &#160;ST-A/PI&#160;&#160;&#160; 01Q_46B<br />&#160;&#160;&#160; SAPKITAB2J&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; ST-A/PI&#160;&#160;&#160; 01Q_46C<br />&#160;&#160;&#160; SAPKITAB3J&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; ST-A/PI&#160;&#160;&#160; 01Q_46D<br />&#160;&#160;&#160; SAPKITAB4J&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; ST-A/PI&#160;&#160;&#160; 01Q_610<br />&#160;&#160;&#160; SAPKITAB5J&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; ST-A/PI&#160;&#160;&#160; 01Q_620<br />&#160;&#160;&#160; SAPKITAB6U&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; ST-A/PI&#160;&#160;&#160; 01T_640<br />&#160;&#160;&#160; SAPKITAB7Y&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; ST-A/PI&#160;&#160;&#160; 01U_700<br />&#160;&#160;&#160; SAPKITAB8Y&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; ST-A/PI&#160;&#160;&#160; 01U_710<br />&#160;&#160;&#160; SAPKITAB9Y&#160; &#160; &#160; &#160; &#160; ST-A/PI&#160;&#160;&#160; 01U_731<br />&#160; &#160; SAPKITABA3&#160; &#160; &#160; &#160; &#160; ST-A/PI&#160; &#160; 01V_700<br />&#160; &#160; SAPKITABB3&#160; &#160; &#160; &#160; &#160; ST-A/PI&#160; &#160; 01V_710<br />&#160; &#160; SAPKITABC3&#160; &#160; &#160; &#160; &#160; ST-A/PI&#160; &#160; 01V_731</p>\r\n<p><strong>Q14:</strong> \"Last run of report RTCCTOOL\" in EWAlert too old<br /><strong>A14:</strong> May occur if the satellite system is on ST-A/PI &gt;=01L but Solution Manager does not yet have the ST-SER 701_2008_2 (both as of December 2008). RTCCTOOL was enhanced as of ST-A/PI 01L to deliver the full recommendations to the EWAlert, but the older Solution Manager release does not yet understand this.<br />As a workaround run report RTCCTOOL from Tx SE38 and choose Goto-&gt;Old RTCCTOOL from the menu. This runs the old tool, which should now also return green status lights for everything that's up to date. The next EWA ( with your current ST-SER) takes this info from the old tool into account.<br />As a permanent solution, update your Solution Manager to EhP1 (ST 400 SP18, ST-SER 701_2008_2).<br /><br />Q15: Unicode conversion - Invalid (non ASCII-7) chars in /SSF/DTAB<br />A15: /SSF/DTAB contains mainly application or pre-upgrade analyses from transaction ST14. Please verify that no SAP Service is currently still ongoing. As soon as the service is done, the data can be deleted from /SSF/DTAB.<br />To delete them, Call transaction ST14-&gt;Utilities-&gt; Delete old analyses-&gt;Set a date in the future.<br />Q16: ST-A/PI not offered for reimplementation in SAINT after upgrade<br />A16: This may occur if you have chosen \"Passive deletion\" instead of \"Keep with vendor key\" in an upgrade with minor or no change in basis release (e.g. CRM 500 to 701, SCM 500 to 701; see above).<br />Check with transaction SE16. Display table PAT03 with criteria<br />ADDON_ID = ST-A/PI and ADDON_REL like 01N*.<br />If you find an entry<br />&#160; PATCH&#160;&#160;&#160;&#160;&#160;&#160;&#160; SAPKITAxxx<br />&#160; STATUS&#160;&#160;&#160;&#160;&#160;&#160;&#160; I<br />&#160; CONFIRMED&#160;&#160;&#160; X<br />&#160; ADDON ID&#160;&#160;&#160;&#160;&#160; ST-A/PI<br />&#160; ADDON REL&#160;&#160;&#160;&#160; &lt;the release you want to reimplement e.g. 01N_700CRM&gt;<br />then open a customer message on BC-UPG-ADDON with reference to this note and Q16 and request that this addon should be allowed for reinstallation.</p>\r\n<p><strong>Q17:</strong> Key check error in upgrade RUN_RDDIT006_PRE for /SSF/PTAB keys<br /><strong>A17:</strong> This may occur if you KEEP an older ST-A/PI version 01K* or lower during the upgrade. As above you should instead update to the new 01N* as target release.<br />Alternatively you may also manually delete some E071K entries. Determine the transport for your ST-A/PI release, e.g. SAPKITAB7C for ST-A/PI 01K_BCO700. Then use SE16 to display table entries for table E071K. Enter filter criteria TRKORR = transport name (e.g. SAPKITAB7C) and TABKEY = *_* (as pattern). Execute.<br />This should select about the following 16 entries:<br />TRKORR&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; PGMID OBJECT OBJNAME<br />SAPKITAB7C&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; R3TR&#160; TABU&#160;&#160; /SSF/PTAB</p>\r\n<p>with the following values for field TABKEY:<br />TCABOCOUNT_ENTRIES&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; *<br />TCABOHINT_MODE&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; *<br />TCABOINPUT_STAT4&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; *<br />TCABOINPUT_STAT6&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; *<br />TCABOTAANA_VAR&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; *<br />TCABOTABLE_TAANA&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; *<br />TCCMMCUST_BOLL&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; *<br />TCCMMCUST_FICA&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; *<br />TCCMMCUST_MAIN&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; *<br />TCCMMPERF_TABS&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; *<br />TCEBOVH_PHASES&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; *<br />TCEIMVH_PHASES&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; *<br />TCRTCSETTINGS&#160; LAST_SAPNET_REFRESH&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; *<br />TCRTCSETTINGS&#160; SETTINGS_ENTERED&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; *<br />TCSTXSE30_CUST&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; *<br />TCSTXSQLR_CUST&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; *<br />Select the entries and choose \"Delete all\". Deleting those entries from E071K will do no harm.</p>\r\n<p><strong>Q18:</strong> Full refresh of RTCCTOOL recommendations customizing ?<br /><strong>A18:</strong>&#160; Run report RTCCTOOL from SE38. From the menu choose List-&gt;Other utilities -&gt; Forced full refresh from SAPNet.<br />[If this menu point&#160;is not available on older releases, do the following instead:<br />Run report /SSA/NXS from SE38. Choose [Clear all local recommendations to force a full refresh] and press F8 Execute.<br />Run RTCCTOOL from SE38. Choose List-&gt;Refresh from SAPNet. This takes just a few seconds more than usual.<br />Go completely out of RTCCTOOL and once more restart RTCCTOL from SE38. Then the fully refreshed recommendations should appear.<br />As of ST-A/PI 01N* SP01 choose \"List-&gt;Other utilities-&gt;Forced full refresh from SAPNet\".]</p>\r\n<p><strong>Q19:</strong> Vendor keys for ST-A/PI ?<br /><strong>A19:</strong> usually you should not need vendor keys to&#160;keep ST-A/PI. However, vendor keys for the SAPehpi installer/SUM and for other upgrades with SAPup are</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Target Basis</td>\r\n<td>Key for SAPehpi/SUM</td>\r\n<td>Key for SAPup</td>\r\n</tr>\r\n<tr>\r\n<td>700</td>\r\n<td>2050448</td>\r\n<td>3037227</td>\r\n</tr>\r\n<tr>\r\n<td>701</td>\r\n<td>1271425</td>\r\n<td>2552828</td>\r\n</tr>\r\n<tr>\r\n<td>702</td>\r\n<td>1671774</td>\r\n<td>2069453</td>\r\n</tr>\r\n<tr>\r\n<td>703</td>\r\n<td>1679426</td>\r\n<td>3944055</td>\r\n</tr>\r\n<tr>\r\n<td>730</td>\r\n<td>1807471</td>\r\n<td>2960991</td>\r\n</tr>\r\n<tr>\r\n<td>731</td>\r\n<td>1684309</td>\r\n<td>2760676</td>\r\n</tr>\r\n<tr>\r\n<td>732</td>\r\n<td>2085170</td>\r\n<td>2822245</td>\r\n</tr>\r\n<tr>\r\n<td>740</td>\r\n<td>809161</td>\r\n<td>2902571</td>\r\n</tr>\r\n<tr>\r\n<td>750</td>\r\n<td>728339</td>\r\n<td>\r\n<p>2319360</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>751</td>\r\n<td>735735</td>\r\n<td>\r\n<p>3167604</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>752</td>\r\n<td>1136596</td>\r\n<td>\r\n<p>2836732</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>753</td>\r\n<td>1537201</td>\r\n<td>\r\n<p>2635905</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>754</td>\r\n<td>364969</td>\r\n<td>\r\n<p>2305033</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>&#160;</strong></p>\r\n<p><strong>Q20:&#160;</strong>\"SET TRANSACTION' SQL calls are no longer permitted/not allowed in this context\" ?<br /><strong>A20:</strong>&#160;Such errors may occur during Service data collection if you have SAP basis &gt;= 753 and ST-A/PI below 01T_731 SP1. These are due to change with basis 753 (SAP note 2560888) affecting the security check for online content described above. To solve update ST-A/PI to &gt;=&#160;&#160;01T_731 SP1.</p>\r\n<p><strong>Q21:</strong> Logon screen when executing RTCCTOOL Service preparation check ?<br /><strong>A21:&#160;</strong>RFC logon screens should not appear since the SAP Support backbone systems for RFC ABAP connectivity have been completely shut down. HTML logon screen to&#160;SAP Support backbone system can popup if no valid S-User in entered. Solution is to specify a valid S-User into HTTP destination SAP-SUPPORT_PORTAL. See https://support.sap.com/backbone-update, or SAP note&#160;2934203 for basis &lt;= 731.</p>\r\n<p><strong>Q22</strong>: About Online SELECT SQL input for HANA and Oracle ?<br /><strong>A22</strong>: To assure those statements are read only, \"SET TRANSACTION READ ONLY\" is done, a \"Select * from (&#8230;)\" is wrapped around the statement, and Explain is checked first.<br />For HANA:<br />explain plan set STATEMENT_NAME = 'SDCC_SSA_ZHDBSSASDCC'<br />select ZZHDBSSA1.* from (&#8230;)<br />Secondary DB connection R/3*ZHDBSSASDCC is used.<br />For Oracle:<br />select /*ZORASSASDCC..*/ * from (&#8230;)<br />To get the field list on Oracle a global temporary Select-view ORA_SSA_SDCC_HELPER is temporarily created and then deleted again.<br />Secondary DB connection R/3*ZORASSASDCC is used.<br /><strong><br />Q23</strong>: What kind of errors might occur during SDCCN data collection and how are they automatically handled ?<br /><strong>A23</strong>: Three types of data collectors come with addon ST-A/PI:<br />&#160;1) Data collection subroutines (form routines in /SSA/D* programs)<br />&#160;2)&#160;Online SELECT SQL input for HANA and Oracle (SQL statements contain ZZHDBSSA1/ZORASSASDCC, secondary DB connection&#160;R/3*ZHDBSSASDCC/R/3*ZORASSASDCC )<br />&#160;3) Online collectors (they are processed by program /SSA/EXC)<br />In general if one collector&#160;fails, it is not critical. Only the data from this one collector will be missing. One of many checks in EarlyWatch Alert may not be filled.<br />For 1) or 3) ABAP dumps may occur if the error could not be caught. You should see program starting with /SSA/D* or program /SSA/EXC in the callstack of the dump. <br />After two consecutive dumps a collector is automatically locked by the framework. It can be unlocked in tx /ssf/pb for case 1), or program RTCCTOOL-&gt;List-&gt;Other utilities-&gt;\"Locks&amp;performance of onl. collector calls\" for case 3).<br />For 2) a failed online SELECT SQL usually does not lead to dump since exceptions are always caught. Only a syslog message may be logged in tx SM21 by the called ADBC framework.&#160;<br />As of ST-A/PI 01U* SP01 and basis &gt;= 740 every data collector get an own stricter timeout limit in case the system setting would be less strict. For 1) and 2) time limit is max. 1 h, for 3) max. half an hour runtime.<br />To report an issue please open a prio low/medium incident on SV-SMG-MON-BPM. For case 2) and 3) a fix can come with the next online recommendation refresh from SAP.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-BO (Backoffice Service Delivery)"}, {"Key": "Other Components", "Value": "SV-SMG-MON-BPM (Business Process Operations)"}, {"Key": "Transaction codes", "Value": "SE38"}, {"Key": "Transaction codes", "Value": "SE16"}, {"Key": "Transaction codes", "Value": "SE14"}, {"Key": "Transaction codes", "Value": "SE12"}, {"Key": "Transaction codes", "Value": "ST14"}, {"Key": "Transaction codes", "Value": "ST12"}, {"Key": "Transaction codes", "Value": "ST13"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D027971)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D036502)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000069455/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000069455/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000069455/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000069455/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000069455/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000069455/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000069455/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000069455/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000069455/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "990000", "RefComponent": "FIN-SEM-BPS-BP", "RefTitle": "BW-BPS: Performance monitoring and analysis", "RefUrl": "/notes/990000"}, {"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488"}, {"RefNumber": "85750", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Syntax error in SAPLSTUW after Hot Package 18", "RefUrl": "/notes/85750"}, {"RefNumber": "804713", "RefComponent": "SV-SMG-SDD", "RefTitle": "Download information from ST-A/PI missing in BW sessions", "RefUrl": "/notes/804713"}, {"RefNumber": "781680", "RefComponent": "SV-SMG-SDD", "RefTitle": "SDCC/ SDCCN - Problems with function modules", "RefUrl": "/notes/781680"}, {"RefNumber": "763561", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCCN)  - FAQ", "RefUrl": "/notes/763561"}, {"RefNumber": "617604", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Services for SAP PI/XI", "RefUrl": "/notes/617604"}, {"RefNumber": "216952", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCC) - FAQ", "RefUrl": "/notes/216952"}, {"RefNumber": "207223", "RefComponent": "SV-SMG-SDD", "RefTitle": "SAP EarlyWatch Alert Processed at SAP", "RefUrl": "/notes/207223"}, {"RefNumber": "187939", "RefComponent": "SV-SMG-SDD", "RefTitle": "SAP Servicetools Update (RTCCTOOL)", "RefUrl": "/notes/187939"}, {"RefNumber": "1784032", "RefComponent": "SV-SMG-SDD", "RefTitle": "Extension of EWA data by EWM data", "RefUrl": "/notes/1784032"}, {"RefNumber": "1634757", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Service 'Performance Optimization'", "RefUrl": "/notes/1634757"}, {"RefNumber": "1595305", "RefComponent": "SV-SMG-MON-BPM", "RefTitle": "Measuring runtimes for RF devices in SAP EWM", "RefUrl": "/notes/1595305"}, {"RefNumber": "1587961", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1587961"}, {"RefNumber": "1572951", "RefComponent": "SV-SMG-SDD", "RefTitle": "Dump DATASET_CANT_OPEN in RTCCTOOL", "RefUrl": "/notes/1572951"}, {"RefNumber": "1510956", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "EWA: section 'ST-PI and ST-A/PI Plug-Ins' - SPs for ST-A/PI", "RefUrl": "/notes/1510956"}, {"RefNumber": "1473755", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1473755"}, {"RefNumber": "1469638", "RefComponent": "BW-SYS", "RefTitle": "DYNPRO_NOT_FOUND when running ST13 -> BW-TOOLS or /SSA/BWT", "RefUrl": "/notes/1469638"}, {"RefNumber": "142005", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Termination of RSSTAT90 after upgrade", "RefUrl": "/notes/142005"}, {"RefNumber": "1393207", "RefComponent": "BI-BIP-INS", "RefTitle": "Configuring SAP BusinessObjects Edge 3.1 Monitoring", "RefUrl": "/notes/1393207"}, {"RefNumber": "1392822", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1392822"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "133735", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "SAPLSTUW: IMPORT_ALIGNMENT_MISMATCH during import of 'STAPAR", "RefUrl": "/notes/133735"}, {"RefNumber": "133017", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/133017"}, {"RefNumber": "1310683", "RefComponent": "SV-SMG-SER", "RefTitle": "Prerequisites BWA Remote Configuration/Verification Check", "RefUrl": "/notes/1310683"}, {"RefNumber": "1298431", "RefComponent": "BW-PLA-IP", "RefTitle": "GoingLive Session - BI Performance Benchmark Check", "RefUrl": "/notes/1298431"}, {"RefNumber": "1168369", "RefComponent": "SV-SMG-SER", "RefTitle": "ST-A/PI 01K: Missing PI 7.10 performance data", "RefUrl": "/notes/1168369"}, {"RefNumber": "1149742", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP (CQC) Going Live Support", "RefUrl": "/notes/1149742"}, {"RefNumber": "1121318", "RefComponent": "LO-VC", "RefTitle": "Analysis of the performance of dependency knowledge in VC", "RefUrl": "/notes/1121318"}, {"RefNumber": "1108861", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1108861"}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904"}, {"RefNumber": "1083365", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Upgrade behaviour of Addon ST-A/PI", "RefUrl": "/notes/1083365"}, {"RefNumber": "1077981", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Upgrade Assessment Preparation Note", "RefUrl": "/notes/1077981"}, {"RefNumber": "1074601", "RefComponent": "SV-SMG-SDD", "RefTitle": "E2E Diagnostics Workload BI - Upload Performance", "RefUrl": "/notes/1074601"}, {"RefNumber": "1071193", "RefComponent": "SV-SMG", "RefTitle": "Availability of Remote Services for CRM 5.2", "RefUrl": "/notes/1071193"}, {"RefNumber": "1060962", "RefComponent": "SV-SMG-SDD", "RefTitle": "E2E Diagnostics Workload BI", "RefUrl": "/notes/1060962"}, {"RefNumber": "1048032", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1048032"}, {"RefNumber": "1035990", "RefComponent": "BW-PLA-IP", "RefTitle": "GoingLive services for integrated planning - check tool", "RefUrl": "/notes/1035990"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3417684", "RefComponent": "SV-SMG-SDD", "RefTitle": "Managed system transaction SDCCN showing Red flag with message - Migration to SAP Support backbone", "RefUrl": "/notes/3417684 "}, {"RefNumber": "3278527", "RefComponent": "SV-SCS-S4R", "RefTitle": "Dropdown list empty for Business Process Analytics KPIs in report RC_VALUE_DISCOVERY_COLL_DATA", "RefUrl": "/notes/3278527 "}, {"RefNumber": "3138046", "RefComponent": "SV-FRN-APP-CSA", "RefTitle": "False Alert ABAP PSE certificates expiring in Focused Run", "RefUrl": "/notes/3138046 "}, {"RefNumber": "3078385", "RefComponent": "SV-SMG-MON-BPM-MON", "RefTitle": "ST22 dump for  SDF/SAPLE2E_EFWKE_COLLECTORS\" had to be terminated in include /SSA/EBO", "RefUrl": "/notes/3078385 "}, {"RefNumber": "2424507", "RefComponent": "BC-UPG-MP", "RefTitle": "Missing ST-A/PI in stack.xml", "RefUrl": "/notes/2424507 "}, {"RefNumber": "2936485", "RefComponent": "BC-OP-NT-AZR", "RefTitle": "Even after setting the PPG, the performance (ABAP meter) is still not good - INTERNAL ONLY", "RefUrl": "/notes/2936485 "}, {"RefNumber": "2931465", "RefComponent": "BC-OP-NT-AZR", "RefTitle": "When to use Proximity Placement Groups on Azure to Reduce Network Latency – 3 Tier NetWeaver or S/4HANA architecture", "RefUrl": "/notes/2931465 "}, {"RefNumber": "2947886", "RefComponent": "SV-SMG-CCM", "RefTitle": "Getting started with Custom Code Analytics based on EWA", "RefUrl": "/notes/2947886 "}, {"RefNumber": "2737826", "RefComponent": "XX-SER-NET", "RefTitle": "SAP is going to close its proprietary RFC communication for automated data exchange between Customers & SAP Support Backbone (SAPOSS) by July 2020.", "RefUrl": "/notes/2737826 "}, {"RefNumber": "2128984", "RefComponent": "SV-SMG-MON-BPM", "RefTitle": "Short dump CX_SY_CONVERSION_NO_NUMBER for ST12.", "RefUrl": "/notes/2128984 "}, {"RefNumber": "2861126", "RefComponent": "SV-SMG-DVM", "RefTitle": "Warning message for object /SSA/ABO when applying DVM SAP Note 2709351", "RefUrl": "/notes/2861126 "}, {"RefNumber": "2858926", "RefComponent": "SV-SMG-DVM", "RefTitle": "PERFORM_NOT_FOUND runtime error in program SAPLDVM_CQC", "RefUrl": "/notes/2858926 "}, {"RefNumber": "2437842", "RefComponent": "SV-SMG-MON-ALR", "RefTitle": "Solman Alerting: update of ST-PI and ST-A/PI software components is required", "RefUrl": "/notes/2437842 "}, {"RefNumber": "2231445", "RefComponent": "PPM-PRO", "RefTitle": "How to create the perfect case for PPM component and subcomponents", "RefUrl": "/notes/2231445 "}, {"RefNumber": "2725530", "RefComponent": "SV-SMG-SDD", "RefTitle": "\"Existence Text for Required TCC Tools\" when running RTCCTOOL", "RefUrl": "/notes/2725530 "}, {"RefNumber": "2693614", "RefComponent": "SV-PERF", "RefTitle": "Transaction ST12 does not exist", "RefUrl": "/notes/2693614 "}, {"RefNumber": "2639152", "RefComponent": "SV-SMG-SDD", "RefTitle": "ASSIGN_TYPE_CONFLICT Runtime Errors in ABAP Program /SSF/PACK while generating SOS report", "RefUrl": "/notes/2639152 "}, {"RefNumber": "2150523", "RefComponent": "CO-PC-PCP", "RefTitle": "CK40N performance problems - collective information", "RefUrl": "/notes/2150523 "}, {"RefNumber": "2216153", "RefComponent": "BC-UPG-OCS", "RefTitle": "Error \"No active nametab exists for /SSF/*\" in phase IMPORT_PROPER while importing Addon ST-A/PI in SPAM/SAINT", "RefUrl": "/notes/2216153 "}, {"RefNumber": "2511516", "RefComponent": "SV-SMG-DIA-SRV-EFW", "RefTitle": "\"Abap Dump : PERFORM_NOT_FOUND - CX_SY_DYN_CALL_ILLEGAL_FORM - /SDF/SAPLE2E_EFWKE_COLLECT_7X\" Solution Manager 7.1", "RefUrl": "/notes/2511516 "}, {"RefNumber": "2299435", "RefComponent": "SCM-APO-SPP", "RefTitle": "How to create the perfect incident for Service Parts Planning (SCM-APO-SPP)", "RefUrl": "/notes/2299435 "}, {"RefNumber": "2529867", "RefComponent": "SD-BF-AC", "RefTitle": "How to create perfect case for R/3 ATP issues", "RefUrl": "/notes/2529867 "}, {"RefNumber": "2528415", "RefComponent": "SV-SMG-INS-CFG-SYP", "RefTitle": "Solution Manager 7.2: SAPNET_RTCC : The RFC isn’t using Load Balancing", "RefUrl": "/notes/2528415 "}, {"RefNumber": "2151502", "RefComponent": "SCM-ICH-MD", "RefTitle": "How to create the perfect case for SCM-ICH component and subcomponents", "RefUrl": "/notes/2151502 "}, {"RefNumber": "2827332", "RefComponent": "SV-SMG-SER", "RefTitle": "Online Collector Data missing in Service Data", "RefUrl": "/notes/2827332 "}, {"RefNumber": "2740667", "RefComponent": "XX-SER-NET", "RefTitle": "SAP is going to close its proprietary RFC communication for automated data exchange between Customers & SAP Support Backbone (SAPOSS) by July 2020.", "RefUrl": "/notes/2740667 "}, {"RefNumber": "2189708", "RefComponent": "BW-B4H-LM", "RefTitle": "SAP BW/4HANA Add-On Handling and Usage", "RefUrl": "/notes/2189708 "}, {"RefNumber": "1601951", "RefComponent": "SV-SMG-SER", "RefTitle": "Self Service 'SQL Statement Tuning' - Prerequisites and FAQ", "RefUrl": "/notes/1601951 "}, {"RefNumber": "1035990", "RefComponent": "BW-PLA-IP", "RefTitle": "GoingLive services for integrated planning - check tool", "RefUrl": "/notes/1035990 "}, {"RefNumber": "781680", "RefComponent": "SV-SMG-SDD", "RefTitle": "SDCC/ SDCCN - Problems with function modules", "RefUrl": "/notes/781680 "}, {"RefNumber": "1083365", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Upgrade behaviour of Addon ST-A/PI", "RefUrl": "/notes/1083365 "}, {"RefNumber": "1784032", "RefComponent": "SV-SMG-SDD", "RefTitle": "Extension of EWA data by EWM data", "RefUrl": "/notes/1784032 "}, {"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488 "}, {"RefNumber": "1149742", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP (CQC) Going Live Support", "RefUrl": "/notes/1149742 "}, {"RefNumber": "1595305", "RefComponent": "SV-SMG-MON-BPM", "RefTitle": "Measuring runtimes for RF devices in SAP EWM", "RefUrl": "/notes/1595305 "}, {"RefNumber": "1298431", "RefComponent": "BW-PLA-IP", "RefTitle": "GoingLive Session - BI Performance Benchmark Check", "RefUrl": "/notes/1298431 "}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904 "}, {"RefNumber": "617604", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Services for SAP PI/XI", "RefUrl": "/notes/617604 "}, {"RefNumber": "1587961", "RefComponent": "SV-SMG-DIA-APP-EM", "RefTitle": "ST-PI Development", "RefUrl": "/notes/1587961 "}, {"RefNumber": "812329", "RefComponent": "PS", "RefTitle": "Project Statistics Report (ST13, ST14)", "RefUrl": "/notes/812329 "}, {"RefNumber": "207223", "RefComponent": "SV-SMG-SDD", "RefTitle": "SAP EarlyWatch Alert Processed at SAP", "RefUrl": "/notes/207223 "}, {"RefNumber": "1484124", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Security Optimization Self Service - Prerequisites", "RefUrl": "/notes/1484124 "}, {"RefNumber": "1077981", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Upgrade Assessment Preparation Note", "RefUrl": "/notes/1077981 "}, {"RefNumber": "160777", "RefComponent": "SV-PERF", "RefTitle": "SAP Remote Services for SAP BI/BW", "RefUrl": "/notes/160777 "}, {"RefNumber": "1510956", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "EWA: section 'ST-PI and ST-A/PI Plug-Ins' - SPs for ST-A/PI", "RefUrl": "/notes/1510956 "}, {"RefNumber": "1473755", "RefComponent": "SV-SMG-SER", "RefTitle": "Re-definition of KPI7 in \"ES Customer Advisory Council\"", "RefUrl": "/notes/1473755 "}, {"RefNumber": "1634757", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Service 'Performance Optimization'", "RefUrl": "/notes/1634757 "}, {"RefNumber": "1121318", "RefComponent": "LO-VC", "RefTitle": "Analysis of the performance of dependency knowledge in VC", "RefUrl": "/notes/1121318 "}, {"RefNumber": "1310683", "RefComponent": "SV-SMG-SER", "RefTitle": "Prerequisites BWA Remote Configuration/Verification Check", "RefUrl": "/notes/1310683 "}, {"RefNumber": "763561", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCCN)  - FAQ", "RefUrl": "/notes/763561 "}, {"RefNumber": "1572951", "RefComponent": "SV-SMG-SDD", "RefTitle": "Dump DATASET_CANT_OPEN in RTCCTOOL", "RefUrl": "/notes/1572951 "}, {"RefNumber": "216952", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCC) - FAQ", "RefUrl": "/notes/216952 "}, {"RefNumber": "1393207", "RefComponent": "BI-BIP-INS", "RefTitle": "Configuring SAP BusinessObjects Edge 3.1 Monitoring", "RefUrl": "/notes/1393207 "}, {"RefNumber": "1469638", "RefComponent": "BW-SYS", "RefTitle": "DYNPRO_NOT_FOUND when running ST13 -> BW-TOOLS or /SSA/BWT", "RefUrl": "/notes/1469638 "}, {"RefNumber": "1392822", "RefComponent": "SV-SMG-SER", "RefTitle": "Webservice Access via GRMG", "RefUrl": "/notes/1392822 "}, {"RefNumber": "804713", "RefComponent": "SV-SMG-SDD", "RefTitle": "Download information from ST-A/PI missing in BW sessions", "RefUrl": "/notes/804713 "}, {"RefNumber": "1168369", "RefComponent": "SV-SMG-SER", "RefTitle": "ST-A/PI 01K: Missing PI 7.10 performance data", "RefUrl": "/notes/1168369 "}, {"RefNumber": "990000", "RefComponent": "FIN-SEM-BPS-BP", "RefTitle": "BW-BPS: Performance monitoring and analysis", "RefUrl": "/notes/990000 "}, {"RefNumber": "1074601", "RefComponent": "SV-SMG-SDD", "RefTitle": "E2E Diagnostics Workload BI - Upload Performance", "RefUrl": "/notes/1074601 "}, {"RefNumber": "1060962", "RefComponent": "SV-SMG-SDD", "RefTitle": "E2E Diagnostics Workload BI", "RefUrl": "/notes/1060962 "}, {"RefNumber": "1071193", "RefComponent": "SV-SMG", "RefTitle": "Availability of Remote Services for CRM 5.2", "RefUrl": "/notes/1071193 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "133735", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "SAPLSTUW: IMPORT_ALIGNMENT_MISMATCH during import of 'STAPAR", "RefUrl": "/notes/133735 "}, {"RefNumber": "187939", "RefComponent": "SV-SMG-SDD", "RefTitle": "SAP Servicetools Update (RTCCTOOL)", "RefUrl": "/notes/187939 "}, {"RefNumber": "85750", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Syntax error in SAPLSTUW after Hot Package 18", "RefUrl": "/notes/85750 "}, {"RefNumber": "142005", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Termination of RSSTAT90 after upgrade", "RefUrl": "/notes/142005 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "616", "To": "616", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APO", "From": "310", "To": "310", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46A", "To": "46D", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "740", "To": "740", "Subsequent": "X"}, {"SoftwareComponent": "SCM", "From": "410", "To": "410", "Subsequent": ""}, {"SoftwareComponent": "SCM", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SCM", "From": "510", "To": "510", "Subsequent": ""}, {"SoftwareComponent": "SCM", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SCM", "From": "701", "To": "701", "Subsequent": ""}, {"SoftwareComponent": "SCM", "From": "702", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SCM", "From": "712", "To": "712", "Subsequent": "X"}, {"SoftwareComponent": "BBPCRM", "From": "300", "To": "300", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "310", "To": "310", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "350", "To": "350", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "510", "To": "510", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "520", "To": "520", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "701", "To": "701", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "702", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "712", "To": "712", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}