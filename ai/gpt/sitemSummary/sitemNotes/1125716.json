{"Request": {"Number": "1125716", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 429, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000006697702017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001125716?language=E&token=2272D345EC67F68903E80F085D0E4597"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001125716", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001125716/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1125716"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.07.2009"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-LC-JP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Japan"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Real Estate Localization", "value": "RE-FX-LC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Japan", "value": "RE-FX-LC-JP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC-JP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1125716 - Payment Charges Report Japan - BP Role/Role Category"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>No data are given in the result list if there are no business partners with the business partner roles 'TR0602' and 'TR0801'.<br />The users create their own business partner roles, which leads to the situation that the program does not select any data.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RE-FX, Localization, Japan, Payment Charges Report, Business Partner Role and Role category</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>In the Payment Charges Report of the Flexible Real Estate Localization for Japan, the program does not validate against the role category of the Business partner, but against its role.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The validation of the business partner should happen against the role categories:<br />- 'TR0602 - Landlord with Vendor Account' and<br />- 'TR0801 - Broker'<br />and not against the roles<br />- 'TR0602 - Landlord with Vendor Account' and<br />- 'TR0801 - Broker'.<br /><br />The following steps in the customizing are prerequisites for the solution:</p> <OL>1. In the 'Define BP Roles' IMG activity of the Business partner customizing:</OL> <UL><LI>The new role for 'Landlord' shall be assigned to the standard role category 'TR0602 - Landlord with Vendor Account',</LI></UL> <UL><LI>while the new role for 'Agent' to the role category 'TR0801 - Broker'.</LI></UL> <OL>2. In the IMG activity 'Make Settings for Roles per Object Type' of the customizing for Business Partner.</OL> <UL><LI>It is needed that the newly created role for 'Landlord' is assigned to the contract as required role</LI></UL> <UL><LI>Make sure that only one role is assigned to the contract type as required.</LI></UL> <UL><LI>It is also needed that the newly created role for 'Agent' is assigned to the contract (but not as required) in the IMG activity 'Make Settings for Roles per Object Type' of the customizing for Business Partner.</LI></UL> <OL>3. Add also the new role for the landlord to your contract type in the IMG Activity 'Define Contract Types' in the customizing for Contracts.</OL> <p>If the contracts use these newly created roles, the report will work correctly on them after the correction instructions had been carried out.<br /><br />Before you carry out the correction instruction do the following manual steps:<br /><br />1. Start the transaction SE91 Message Maintenance and create the following messages in the message class REXCJP:<br /><br /> 154: 'No business partner role found in customizing'<br /> 155: 'No landlord or agent belongs as BP to the selection   parameters'<br /> 156: 'Roles for landlords and agents are missing in    customizing'<br /><br />2. For the messages Nr. 154, 155 and 156 enter a longtext too and create a link to the appropiate activity in the longtext:<br /><br /> a.) Nr. 154:<br /> -------------<br /> &amp;CAUSE&amp;<br /> No Business Partner Roles are defined in Customizing.<br /><br /> &amp;WHAT_TO_DO&amp;<br /> Define partner roles in the IMG Activity 'Define BP Roles'.<br /> -------------<br /><br /> In the text mark the name of the IMG Activities, choose in Menu 'Instert --&gt; Link'. In the dialog box that appears, enter:<br /><br /> Document Class  : Implementation Guide chapter (SIMG)<br /> Chapter  : SIMG<br /> (No title)  : REFXREBUPA_TB003  <br /> Name in the document : 'Define BP Roles'<br /><br /> b.) Nr. 155:<br /> ------------------<br /> &amp;CAUSE&amp;<br /> No landlords with role belonging to the role category 'Landlord with Vendor account' (TR0602) were found assigned to any   contract.<br /><br /> No agents with role belonging to the role category 'Agent' or 'Broker' (TR0801) were found assigned to any contract.<br /><br /> &amp;WHAT_TO_DO&amp;<br /> 1. Define business partner roles in the IMG Activity 'Define BP Roles'&#x00A0;&#x00A0;with the role categories TR0602 and TR0801.<br /> 2. Create business partners in the transaction BP - 'Maintain Business Partners' with the newly created business partner  roles.<br /> 3. Assign the new business partner roles to the contract type in the IMG Activity 'Make Settings for Roles per Object Type'<br /> 4. Also assign the new business partner roles to the contract type in the IMG Activity 'Define Contract Types'<br /> 5. Assign these partners to your real estate contracts in the transaction RECN - 'Process Contract'.<br /> ------------------<br /> To have the list numbered, select all lines below &amp;WHAT_TO_DO&amp;, and in the paragraph format drop drown list choose 'N1 Numbered  list level 1'.<br /><br /> In the text mark the name of the IMG Activities or Transactions, choose in Menu 'Instert --&gt; Link'. In the dialog box that   appears, enter:<br /><br /> I.) Point 1.<br /> Document Class  : Implementation Guide chapter (SIMG)<br /> Chapter  : SIMG<br /> (No title)  : REFXREBUPA_TB003  <br /> Name in the document : 'Define BP Roles'<br /><br /> II.) Point 2.<br /> Document Class  : Transaction link<br /> (No title)  : BP  <br /> Name in the document : 'Maintain Business Partners'<br /><br /> III.) Point 3.<br /> Document Class  : Implementation Guide chapter (SIMG)<br /> Chapter  : SIMG<br /> (No title)  : REFXV_TIVBPOBJROLE  <br /> Name in the document : 'Make Settings for Roles per Object &#x00A0;&#x00A0; Type'<br /><br /> IV.) Point 4.<br /> Document Class  : Implementation Guide chapter (SIMG)<br /> Chapter  : SIMG<br /> (No title)  : REFXV_TIVCNCCTADD  <br /> Name in the document : 'Define Contract Types'<br /><br /> V.) Point 5.<br /> Document Class  : Transaction link<br /> (No title)  : RECN  <br /> Name in the document : 'Process Contract'<br /><br /><br /> <br /> c.) Nr. 156:<br /> --------------<br /> &amp;CAUSE&amp;<br /> Roles for landlords and agents are missing in customizing<br /><br /> &amp;WHAT_TO_DO&amp;<br /> Define the roles in the IMG Activity 'Define BP Roles'.<br /> --------------<br /><br /><br /> In the text mark the name of the IMG Activities, choose in Menu 'Instert --&gt; Link'. In the dialog box that appears, enter:<br /><br /> Document Class  : Implementation Guide chapter (SIMG)<br /> Chapter  : SIMG<br /> (No title)  : REFXREBUPA_TB003  <br /> Name in the document : 'Define BP Roles'<br /><br />3. Change the long text of the message Nr. 146:<br /> --------------<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&amp;CAUSE&amp;<br /> No contract data can be found for these selection parameters<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&amp;WHAT_TO_DO&amp;<br /> The system cannot find any contract data for the selection  parameters you entered.<br /><br /> This can happen due to one of the following reasons:<br /></p> <UL><LI> There are no lease-in contracts created in the system for this client</LI></UL> <UL><LI> There is no contract created for the selection parameters you  specified</LI></UL> <UL><LI> The business partner roles are not belonging to the role&#x00A0;&#x00A0;&#x00A0;&#x00A0;categories Landlord (TR0602) or Agent (TR0801).</LI></UL> <p> ----------------<br /><br />4. Delete the message Nr. 143 form the message class REXCJP using the transaction SE91 Message Maintenance.<br /><br />5. Finally carry out the correction instructions.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-TRANSL-NOTE-JA (Translation of SAP Notes into Japanese)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON> (I033780)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON> (I033780)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001125716/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001125716/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001125716/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001125716/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001125716/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001125716/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001125716/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001125716/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001125716/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "928175", "RefComponent": "RE-FX-LC-JP", "RefTitle": "RE-FX Country Version for Japan", "RefUrl": "/notes/928175"}, {"RefNumber": "1383363", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Agent commission in the payment charges report", "RefUrl": "/notes/1383363"}, {"RefNumber": "1372797", "RefComponent": "RE-FX-LC-JP", "RefTitle": "REXCJPPAYMENTREP: BP other then landlord/agent is selected", "RefUrl": "/notes/1372797"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1383363", "RefComponent": "RE-FX-LC-JP", "RefTitle": "Agent commission in the payment charges report", "RefUrl": "/notes/1383363 "}, {"RefNumber": "1372797", "RefComponent": "RE-FX-LC-JP", "RefTitle": "REXCJPPAYMENTREP: BP other then landlord/agent is selected", "RefUrl": "/notes/1372797 "}, {"RefNumber": "928175", "RefComponent": "RE-FX-LC-JP", "RefTitle": "RE-FX Country Version for Japan", "RefUrl": "/notes/928175 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-APPL 600", "SupportPackage": "SAPKGPAD12", "URL": "/supportpackage/SAPKGPAD12"}, {"SoftwareComponentVersion": "EA-APPL 602", "SupportPackage": "SAPK-60202INEAAPPL", "URL": "/supportpackage/SAPK-60202INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 603", "SupportPackage": "SAPK-60301INEAAPPL", "URL": "/supportpackage/SAPK-60301INEAAPPL"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "EA-APPL", "NumberOfCorrin": 8, "URL": "/corrins/0001125716/229"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 8, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 2, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1031686 ", "URL": "/notes/1031686 ", "Title": "Changes after downgrade of the RE-FX Japan", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1125716 ", "URL": "/notes/1125716 ", "Title": "Payment Charges Report Japan - BP Role/Role Category", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1125716 ", "URL": "/notes/1125716 ", "Title": "Payment Charges Report Japan - BP Role/Role Category", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1125716 ", "URL": "/notes/1125716 ", "Title": "Payment Charges Report Japan - BP Role/Role Category", "Component": "RE-FX-LC-JP"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "604", "ValidTo": "604", "Number": "1125716 ", "URL": "/notes/1125716 ", "Title": "Payment Charges Report Japan - BP Role/Role Category", "Component": "RE-FX-LC-JP"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}