{"Request": {"Number": "98198", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 365, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000098198?language=E&token=1C7499646217973629AC5A0C1B0BC491"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000098198", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000098198/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "98198"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "00.00.0000"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade - general"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "98198 - Error in upgrade phase TOOLIMPD1, TOOLIMPD2"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The upgrade terminates in phase TOOLIMPD1 or TOOLIMPD2 with distribution error (error in log file DS&lt;Date&gt;.&lt;SID&gt;)</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p> - PREPARE - TOOLIMPD1, TOOLIMPD2 - Distribution program<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>If you upgrade from a source release &gt; 4.0A to a higher release, a termination can occur in rare cases in one of the phases TOOLIMPD1, TOOLIMPD2 (a possible cause is for example that the tablespace PSAPBTABD has overflowed; in that case, increase the tablespace).<br /><br />If the log file (ELG) indicates a distribution error in this phase (error in the log file DS&lt;Date&gt;.&lt;SID&gt;), use the proposed solution.<br /><br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Log on to the system, and execute report RDDGENBB in Transaction SE38 with variant RESTA before you repeat the phase.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D025323)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D028310)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000098198/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000098198/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000098198/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000098198/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000098198/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000098198/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000098198/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000098198/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000098198/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "961512", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/961512"}, {"RefNumber": "91710", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions upgrade to 4.5A", "RefUrl": "/notes/91710"}, {"RefNumber": "91709", "RefComponent": "BC-UPG-RDM", "RefTitle": "Addition upgrade to 4.0B", "RefUrl": "/notes/91709"}, {"RefNumber": "826093", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/826093"}, {"RefNumber": "737696", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/737696"}, {"RefNumber": "637683", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/637683"}, {"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852"}, {"RefNumber": "587896", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/587896"}, {"RefNumber": "484876", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP Web AS 6.20", "RefUrl": "/notes/484876"}, {"RefNumber": "435140", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information on upgrading to Basis 4.6D (APO 3.1x", "RefUrl": "/notes/435140"}, {"RefNumber": "420371", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info on upgrading to Basis 4.6C SR2 (NDI upgrade)", "RefUrl": "/notes/420371"}, {"RefNumber": "401717", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info on upgrading to SAP Web AS 6.10 (Basis)", "RefUrl": "/notes/401717"}, {"RefNumber": "390062", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to 4.6C SR2", "RefUrl": "/notes/390062"}, {"RefNumber": "335029", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to the upgrade to Basis 4.6D (NDI Upgrade)", "RefUrl": "/notes/335029"}, {"RefNumber": "329622", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions: Upgrading to Basis 4.6C SR1 (NDI upgrade)", "RefUrl": "/notes/329622"}, {"RefNumber": "327285", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade to 4.6C SR1", "RefUrl": "/notes/327285"}, {"RefNumber": "179373", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info for upgrade to Rel.4.6B", "RefUrl": "/notes/179373"}, {"RefNumber": "151833", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to FCS 4.5B", "RefUrl": "/notes/151833"}, {"RefNumber": "147337", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info for upgrade to Release 4.0B SR", "RefUrl": "/notes/147337"}, {"RefNumber": "117668", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/117668"}, {"RefNumber": "116251", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions upgrade to Basis 4.5A (comp.system)", "RefUrl": "/notes/116251"}, {"RefNumber": "1108700", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1108700"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "484876", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP Web AS 6.20", "RefUrl": "/notes/484876 "}, {"RefNumber": "435140", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information on upgrading to Basis 4.6D (APO 3.1x", "RefUrl": "/notes/435140 "}, {"RefNumber": "335029", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to the upgrade to Basis 4.6D (NDI Upgrade)", "RefUrl": "/notes/335029 "}, {"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852 "}, {"RefNumber": "390062", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to 4.6C SR2", "RefUrl": "/notes/390062 "}, {"RefNumber": "327285", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade to 4.6C SR1", "RefUrl": "/notes/327285 "}, {"RefNumber": "329622", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions: Upgrading to Basis 4.6C SR1 (NDI upgrade)", "RefUrl": "/notes/329622 "}, {"RefNumber": "401717", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info on upgrading to SAP Web AS 6.10 (Basis)", "RefUrl": "/notes/401717 "}, {"RefNumber": "179373", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info for upgrade to Rel.4.6B", "RefUrl": "/notes/179373 "}, {"RefNumber": "420371", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info on upgrading to Basis 4.6C SR2 (NDI upgrade)", "RefUrl": "/notes/420371 "}, {"RefNumber": "147337", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info for upgrade to Release 4.0B SR", "RefUrl": "/notes/147337 "}, {"RefNumber": "116251", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions upgrade to Basis 4.5A (comp.system)", "RefUrl": "/notes/116251 "}, {"RefNumber": "91709", "RefComponent": "BC-UPG-RDM", "RefTitle": "Addition upgrade to 4.0B", "RefUrl": "/notes/91709 "}, {"RefNumber": "91710", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions upgrade to 4.5A", "RefUrl": "/notes/91710 "}, {"RefNumber": "151833", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to FCS 4.5B", "RefUrl": "/notes/151833 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}