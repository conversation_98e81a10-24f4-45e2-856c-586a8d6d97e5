{"Request": {"Number": "1364960", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 290, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000008046492017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001364960?language=E&token=D4AE3AC8C11E5A9B1055238B5D4B5235"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001364960", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001364960/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1364960"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 12}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.12.2010"}, "SAPComponentKey": {"_label": "Component", "value": "PA-PD-PM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Objective Setting and Appraisals"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Personnel Management", "value": "PA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Personnel Development", "value": "PA-PD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PA-PD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Objective Setting and Appraisals", "value": "PA-PD-PM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PA-PD-PM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1364960 - Improvements to the Goals Functionality"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note contains changes done to improve:<br />.- Performance<br />.- Usability<br />.- Error Handling<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Performance Management<br />Corporate Goals<br />Core Values<br />Team Goals<br />Goals</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Please apply the support package indicated in this note, or above, or the corrections instructions of this note together with the following manual instructions:<br /><br />The implementation of note 1332333 is a prerequisite&#x00A0;&#x00A0;for this note.<br /><br />1) Import the SAPCAR file attached to this note.<br /><br />2) Manual instructions to be done before applying the correction instructions:<br /><br />2.1) Structure HAP_S_WD_PMP_NAV_ITEM<br />Add the field 'DELETE_TOOLTIP' data type 'STRING' (the data type field becomes maintainable once the button 'Predefined Type' on top of the table was pressed) at the end of the existing structure and activate the structure again.<br /><br />2.2) Function group HRHAP_GOALS<br />Go to package PAOC_HAP_PA_CASC_GOALS_UI in transaction SE80, mark the package and right-click on it. Select 'Create-&gt;Function group'. Enter 'HRHAP_GOALS' for funtion Group and 'goals' for short text and press 'SAVE'. Activate.<br /><br />3) Manual instructions to be done after applying the correction instructions:<br /><br />3.1) Go to transaction SE19, select 'Create Implementation' and mark 'New BadI', enter Enhancement Spot 'HRHAP00_GOAL_PERIOD' and press button 'Create Impl.'. Enter 'HRHAP00GOAL_PERIOD_GEN' as Enhancement Implementation name and 'Create Period for Flexible Process' as Short Text and press 'OK'. Enter Package 'PAOC_HAP_PA_CASC_GOALS_UI' and press 'Save'.<br />In the following popup you need to add a line containing the name of the implementation ('HRHAP00_GOAL_PERIOD_GEN'), the class name 'CL_HAP_GOAL_PERIOD_GEN' for the implementation class. and the BadI definition 'HRHAP00_GOAL_PERIOD'. Press again 'Enter'. Repeat adding a line containing the values 'HRHAP00_GOAL_PERIOD_PMP'), 'CL_HAP_GOAL_PERIOD_PMP' and 'HRHAP00_GOAL_PERIOD'.<br /><br />Finally you need to activate the implementations.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D031342)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D035419)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001364960/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001364960/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001364960/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001364960/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001364960/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001364960/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001364960/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001364960/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001364960/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "EH4K025193.sar", "FileSize": "13", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000426862009&iv_version=0012&iv_guid=65F278B3E7B36044A65961F850269013"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1523527", "RefComponent": "PA-PD-PM", "RefTitle": "Goals transferred for only one appraisal template", "RefUrl": "/notes/1523527"}, {"RefNumber": "1429868", "RefComponent": "PA-PD-PM", "RefTitle": "Objectives: Mid-year cascading of objectives in flexible UI", "RefUrl": "/notes/1429868"}, {"RefNumber": "1415459", "RefComponent": "PA-PD-PM", "RefTitle": "Team goals: Subordinate manager can make an appraisal", "RefUrl": "/notes/1415459"}, {"RefNumber": "1410156", "RefComponent": "PA-PD-PM", "RefTitle": "Goal Application: Cascading team goals to retirees", "RefUrl": "/notes/1410156"}, {"RefNumber": "1404450", "RefComponent": "PA-PD-PM", "RefTitle": "Navigation bar for organizational goals", "RefUrl": "/notes/1404450"}, {"RefNumber": "1382269", "RefComponent": "PA-PD-PM", "RefTitle": "Team Goals in the Predefined Process", "RefUrl": "/notes/1382269"}, {"RefNumber": "1377827", "RefComponent": "PA-PD-PM", "RefTitle": "Goals: The names of the managers are not showing up", "RefUrl": "/notes/1377827"}, {"RefNumber": "1372570", "RefComponent": "PA-PD-PM", "RefTitle": "Adding missing translations", "RefUrl": "/notes/1372570"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1368978", "RefComponent": "PA-PD-PM", "RefTitle": "Goal Application: Cascading of Team goals", "RefUrl": "/notes/1368978"}, {"RefNumber": "1367005", "RefComponent": "PA-PD-PM", "RefTitle": "Goal function: Secondary DB index for HRP5029", "RefUrl": "/notes/1367005"}, {"RefNumber": "1366993", "RefComponent": "PA-PD-PM", "RefTitle": "Goal function: Texts", "RefUrl": "/notes/1366993"}, {"RefNumber": "1332333", "RefComponent": "PA-PD-PM", "RefTitle": "Goal function (Main note)", "RefUrl": "/notes/1332333"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1410156", "RefComponent": "PA-PD-PM", "RefTitle": "Goal Application: Cascading team goals to retirees", "RefUrl": "/notes/1410156 "}, {"RefNumber": "1523527", "RefComponent": "PA-PD-PM", "RefTitle": "Goals transferred for only one appraisal template", "RefUrl": "/notes/1523527 "}, {"RefNumber": "1429868", "RefComponent": "PA-PD-PM", "RefTitle": "Objectives: Mid-year cascading of objectives in flexible UI", "RefUrl": "/notes/1429868 "}, {"RefNumber": "1368978", "RefComponent": "PA-PD-PM", "RefTitle": "Goal Application: Cascading of Team goals", "RefUrl": "/notes/1368978 "}, {"RefNumber": "1366993", "RefComponent": "PA-PD-PM", "RefTitle": "Goal function: Texts", "RefUrl": "/notes/1366993 "}, {"RefNumber": "1415459", "RefComponent": "PA-PD-PM", "RefTitle": "Team goals: Subordinate manager can make an appraisal", "RefUrl": "/notes/1415459 "}, {"RefNumber": "1404450", "RefComponent": "PA-PD-PM", "RefTitle": "Navigation bar for organizational goals", "RefUrl": "/notes/1404450 "}, {"RefNumber": "1382269", "RefComponent": "PA-PD-PM", "RefTitle": "Team Goals in the Predefined Process", "RefUrl": "/notes/1382269 "}, {"RefNumber": "1377827", "RefComponent": "PA-PD-PM", "RefTitle": "Goals: The names of the managers are not showing up", "RefUrl": "/notes/1377827 "}, {"RefNumber": "1372570", "RefComponent": "PA-PD-PM", "RefTitle": "Adding missing translations", "RefUrl": "/notes/1372570 "}, {"RefNumber": "1367005", "RefComponent": "PA-PD-PM", "RefTitle": "Goal function: Secondary DB index for HRP5029", "RefUrl": "/notes/1367005 "}, {"RefNumber": "1332333", "RefComponent": "PA-PD-PM", "RefTitle": "Goal function (Main note)", "RefUrl": "/notes/1332333 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-HRGXX", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-HRGXX 604", "SupportPackage": "SAPK-60412INEAHRGXX", "URL": "/supportpackage/SAPK-60412INEAHRGXX"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "EA-HRGXX", "NumberOfCorrin": 1, "URL": "/corrins/0001364960/5370"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EA-HRGXX", "ValidFrom": "604", "ValidTo": "604", "Number": "1332339 ", "URL": "/notes/1332339 ", "Title": "Goal function: Code", "Component": "PA-PD-PM"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}