{"Request": {"Number": "902573", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 388, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005181452017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000902573?language=E&token=73948332CF3A1F4109E045778B69C2D9"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000902573", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000902573/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "902573"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "13.12.2005"}, "SAPComponentKey": {"_label": "Component", "value": "CRM-PCF"}, "SAPComponentKeyText": {"_label": "Component", "value": "obsolete: People Centric UI Framework"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Customer Relationship Management", "value": "CRM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "obsolete: People Centric UI Framework", "value": "CRM-PCF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM-PCF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "902573 - Correction CRM BSP Framework, SP06 (08)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Correction CRM BSP Framework, SP06 (08)</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Correction CRM BSP Framework, SP06 (08)</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Symptom 1: The SAVE button of the multiselect resultlist (SRME) was not always active. It has only got active if the user selects one row in the list. The user could not save the changes directly since the SAVE button is inactive by default.<br /><br />Symtom 2: The focus was not correctly set in the result list after the page was adopted when the focus might not be within the range of displayed pages. This issue applies to SRES and SRMS types of result lists.<br /><br />Symptom 3: Some texts have been maintained in wrong language. Due to that fact these texts are not available in language German. To correct this problem some manual steps are necessary which are given below.<br /><br />In addition of implementing the attached correction instructions, you have to do the below given manual steps in the given order:</p> <OL>1. Start transaction SOTR_EDIT, chose Language 'German' in the form Text.</OL> <OL>2. Maintain in the Alias input field the value 'CRM_BSP_FRAME_GENERIC/HITLIST_ITEM' and press the button Change.</OL> <OL>3. In the Text form maintain in the Lngth input field the value '20' and in the Text input field the value 'Position'.</OL> <OL>4. Press the button Save.</OL> <OL>5. Press the button Back (F3).</OL> <OL>6. Maintain in the Alias input field the value 'CRM_BSP_FRAME_GENERIC/HITLIST_ITEM_LT' and press the button Change.</OL> <OL>7. In the Text form maintain in the Lngth input field the value '40' and in the Text input field the value 'Position in Ergebnisliste'.</OL> <OL>8. Press the button Save.</OL> <OL>9. Press the button Back (F3).</OL> <OL>10. Maintain in the Alias input field the value 'CRM_BSP_FRAME_GENERIC/HITLIST_ITEMS' and press the button Change.</OL> <OL>11. In the Text form maintain in the Lngth input field the value '20' and in the Text input field the value 'Positionen'.</OL> <OL>12. Press the button Save.</OL> <OL>13. Press the button Back (F3).</OL> <OL>14. Maintain in the Alias input field the value 'CRM_BSP_FRAME_GENERIC/HITLIST_ITEM_SEL' and press the button Change.</OL> <OL>15. In the Text form maintain in the Lngth input field the value '40' and in the Text input field the value 'Position selektiert'.</OL> <OL>16. Press the button Save.</OL> <OL>17. Press the button Back (F3).</OL> <OL>18. Maintain in the Alias input field the value 'CRM_BSP_FRAME_GENERIC/HITLIST_ITEMS_LT' and press the button Change.</OL> <OL>19. In the Text form maintain in the Lngth input field the value '40' and in the Text input field the value 'Positionen in Ergebnisliste'.</OL> <OL>20. Press the button Save.</OL> <OL>21. Press the button Back (F3).</OL> <OL>22. Maintain in the Alias input field the value 'CRM_BSP_FRAME_GENERIC/HITLIST_ITEMS_SEL' and press the button Change.</OL> <OL>23. In the Text form maintain in the Lngth input field the value '40' and in the Text input field the value 'Positionen selektiert'.</OL> <OL>24. Press the button Save.</OL> <p>-</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CA-GTF-PCF (People Centric UI Framework)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023949)"}, {"Key": "Processor                                                                                           ", "Value": "I027070"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000902573/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000902573/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000902573/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000902573/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000902573/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000902573/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000902573/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000902573/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000902573/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "select_old.htm", "FileSize": "10", "MimeType": "text/html", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000681342005&iv_version=0007&iv_guid=227F0C4C68F8BD42941B99064B907673"}, {"FileName": "select.htm", "FileSize": "8", "MimeType": "text/html", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000681342005&iv_version=0007&iv_guid=A74ABCFFF91F2748BDFCF59713637248"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "949447", "RefComponent": "CRM-BF", "RefTitle": "mySAP CRM 2005 SP Stack 05", "RefUrl": "/notes/949447"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "949447", "RefComponent": "CRM-BF", "RefTitle": "mySAP CRM 2005 SP Stack 05", "RefUrl": "/notes/949447 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_ABA", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_ABA 700", "SupportPackage": "SAPKA70007", "URL": "/supportpackage/SAPKA70007"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_ABA", "NumberOfCorrin": 1, "URL": "/corrins/0000902573/44"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 21, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "868404 ", "URL": "/notes/868404 ", "Title": "Correction CRM BSP Framework, SP05", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "868693 ", "URL": "/notes/868693 ", "Title": "Correction CRM BSP Framework, SP05 (1)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "871213 ", "URL": "/notes/871213 ", "Title": "Correction CRM BSP Framework, SP05 (2)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "871793 ", "URL": "/notes/871793 ", "Title": "Correction CRM BSP Framework, SP05 (3)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "871918 ", "URL": "/notes/871918 ", "Title": "Correction CRM BSP Framework, SP05 (4)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "872178 ", "URL": "/notes/872178 ", "Title": "Correction CRM BSP Framework, SP05 (5)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "872453 ", "URL": "/notes/872453 ", "Title": "Correction CRM BSP Framework, SP05 (6)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "873034 ", "URL": "/notes/873034 ", "Title": "Correction CRM BSP Framework, SP05 (7)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "873662 ", "URL": "/notes/873662 ", "Title": "Correction CRM BSP Framework, SP05 (8)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "879650 ", "URL": "/notes/879650 ", "Title": "Correction CRM BSP Framework, SP05 (10)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "880178 ", "URL": "/notes/880178 ", "Title": "Basis-relevant PCUI changes", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "881385 ", "URL": "/notes/881385 ", "Title": "BSP-relevant changes in the PCUI (2)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "881541 ", "URL": "/notes/881541 ", "Title": "Correction CRM BSP Framework, SP05 (12)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "884465 ", "URL": "/notes/884465 ", "Title": "BSP-relevant changes in the CRM PCUI (3)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "885050 ", "URL": "/notes/885050 ", "Title": "Correction CRM BSP Framework, SP06 (01)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "887916 ", "URL": "/notes/887916 ", "Title": "Correction CRM BSP Framework, SP06 (2)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "890821 ", "URL": "/notes/890821 ", "Title": "Correction CRM BSP Framework, SP06 (04)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "890843 ", "URL": "/notes/890843 ", "Title": "Correction CRM BSP Framework, SP06 (05)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "895343 ", "URL": "/notes/895343 ", "Title": "Correction CRM BSP Framework, SP06 (06)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "900260 ", "URL": "/notes/900260 ", "Title": "CRM5.0 Tree OIC messages: 849614 and 914460 (SP06)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "901864 ", "URL": "/notes/901864 ", "Title": "Correction CRM BSP Framework, SP06 (07)", "Component": "CRM-PCF"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}