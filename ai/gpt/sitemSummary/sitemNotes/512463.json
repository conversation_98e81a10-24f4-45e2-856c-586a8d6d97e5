{"Request": {"Number": "512463", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 315, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000002437092017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000512463?language=E&token=26F02C5E3ADE4E766C7A2BCC4B2CC731"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000512463", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000512463/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "512463"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Performance"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "21.11.2003"}, "SAPComponentKey": {"_label": "Component", "value": "CRM-MD-PCT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Product Catalog"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Customer Relationship Management", "value": "CRM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Master Data", "value": "CRM-MD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM-MD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Product Catalog", "value": "CRM-MD-PCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM-MD-PCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "512463 - CRM product catalog - Individual replication"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>If you carry out minor or time-critical changes of the product catalog, the Web contents of the product catalog could only be updated via the update replication up to now. However, this may lead to reconciliation problems if several persons update the catalog at the same time or if the update using the update replication took too much time.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Product catalog, replication</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>An initial replication of the product catalog must be carried out so that you can carry out the report.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>New report for the replication of individual areas or products from a catalog. With this report, you can replicate the following in a targeted way:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;individual catalog items<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;individual catalog areas<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;or catalog areas with all subordinate areas<br /><br />Correction instructions:<br /><br /><STRONG>For Release 3.0 and Release 3.1</STRONG><br />implement note 545680 first. Then you can download a transport from sapservx under the path<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/home/<USER>/general/SAPMarkets/note.512463<br /><br />(K001921.HIC and R001921.HIC) and import this into your system. This contains all objects required for the report.<br />For questions concerning the access of sapservx, see note 13719.<br /><br /><STRONG>For Release 2.0C</STRONG><br />Implement the source code corrections according to the attached correction instructions.<br />The correction consists of:</p> <UL><LI>Function module COM_PCAT_PRDCT_ID_SHLP</LI></UL> <UL><LI>Search help COM_PCAT_ITEM_ID</LI></UL> <UL><LI>Program COM_PCAT_IMS_FEED_SINGLE</LI></UL> <UL><LI>Transaction COMM_PCAT_IMS_SINGLE<br /></LI></UL> <OL>1. Create function module COM_PCAT_PRDCT_ID_SHLP in function group COM_PCAT_UI_TOOLS. Short text: 'Exit search help COM_PCAT_ITEM_ID'. Search help COM_PCAT_ITEM_ID Maintain the interface:<br />Table parameters (not optional): SHLP_TAB TYPE&#x00A0;&#x00A0;SHLP_DESCR_TAB_T<BR/> RECORD_TAB LIKE&#x00A0;&#x00A0;SEAHLPRES<BR/>Changing-Parameter (all with pass value): SHLP TYPE&#x00A0;&#x00A0;SHLP_DESCR_T<BR/> CALLCONTROL TYPE&#x00A0;&#x00A0;DDSHF4CTRL</OL> <OL>2. Create elementary search help COM_PCAT_ITEM_ID (SE11).</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Short description: Product catalog items. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Definition of the search help:<br />Dialog type = dialog with value restriction<br />Search help exit = COM_PCAT_PRDCT_ID_SHLP<br />Parameter <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Parameter</TH><TH ALIGN=LEFT> IMP/EXP/LPos/SPos/SAnz</TH><TH ALIGN=LEFT> Data element</TH></TR> <TR><TD>PRODUCT_ID</TD><TD> x / x / 3 / 3 /</TD><TD> COMT_PRODUCT_ID</TD></TR> <TR><TD>SHORT_TEXT</TD><TD> x /&#x00A0;&#x00A0; / 2 / 0 /</TD><TD> COMT_PRSHTEXTX</TD></TR> <TR><TD>SHTEXT_LARGE</TD><TD> x /&#x00A0;&#x00A0; / 0 / 2 /</TD><TD> COMT_PRSHTEXTG</TD></TR> <TR><TD>PCAT_ID</TD><TD> x / / 0 / 4 / x</TD><TD> COMT_PCAT_ID</TD></TR> <TR><TD>PCAT_CTY_ID</TD><TD> x / / 0 / 5 / x</TD><TD> COMT_PCAT_CTY_ID</TD></TR> <TR><TD>LANGU</TD><TD> &#x00A0;&#x00A0; /&#x00A0;&#x00A0; / 1 / 1 /</TD><TD> Default SPRAS SPR</TD></TR> </TABLE> <p></p> <OL>3. Create program COM_PCAT_IMS_FEED_SINGLE with the title 'Product catalog - individual replication' in development class COM_PRDCAT. Copy the text symbols from program COM_PCAT_IMS_FEED_UPDA to this program.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Change texts 012 to 014 as specified below. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Text symbol<br />012 Start reading selected area / products for variant =&gt;<br />013 Start processing areas/items for variant =&gt;<br />014 Error when processing product catalog items <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Add the following new texts.<br />120 Neither areas nor products specified! =&gt; no replication<br />121 Replication products: Do not select more than one area! =&gt; no replication<br />122 Subareas are not selected.<br />123 Package size indexing: only positive values are allowed<br />124 Start processing product catalog areas<br />125 Error when processing product catalog areas! =&gt;<br />126 No change pointers to be deleted found!<br />127 No catalog areas found for the selection criteria =&gt; End of program<br />128 No catalog items according to selection criteria and variant =&gt;<br />129 Start reading change pointers<br />130 Start optimizing of changed index categories<br />999 Internal error - Contact the Hotline =&gt; <OL>4. Implement the corrections according to the attached correction instructions.</OL> <OL>5. Activate function module COM_PCAT_PRDCT_ID_SHLP and then search help COM_PCAT_ITEM_ID.</OL> <OL>6. Activate report COMM_PCAT_IMS_FEED_SINGLE.</OL> <OL>7. Maintain the selection texts for the report<br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Name</TH><TH ALIGN=LEFT> Text</TH><TH ALIGN=LEFT> Dict.</TH></TR> <TR><TD>PCAT_ID</TD><TD> Product catalog</TD><TD> x</TD></TR> <TR><TD>P_IDXPC</TD><TD> Package size for indexing</TD><TD> x</TD></TR> <TR><TD>P_VARIAN</TD><TD> Variant (blank = all)</TD></TR> <TR><TD>P_XCHILD</TD><TD> with subareas</TD></TR> <TR><TD>SO_CTY</TD><TD> Catalog area</TD><TD> x</TD></TR> <TR><TD>SO_PRDCT</TD><TD> Product ID</TD><TD> x</TD></TR> </TABLE></OL> <p></p> <OL>8. Activate the report again.</OL> <OL>9. Maintain transaction COMM_PCAT_IMS_SINGLE with the following values:<br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Field</TH><TH ALIGN=LEFT> Value</TH></TR> <TR><TD>Develpmnt class</TD><TD> COM_PRDCAT</TD></TR> <TR><TD>transaction text</TD><TD> Individual replication product catalog</TD></TR> <TR><TD>Program</TD><TD> COM_PCAT_IMS_FEED_SINGLE</TD></TR> <TR><TD>Selection screen</TD><TD> 1000</TD></TR> <TR><TD>Professional User Trans.</TD><TD> x</TD></TR> <TR><TD>SAP-GUI for HTML</TD><TD> x</TD></TR> <TR><TD>SAP-GUI for Windows</TD><TD> x</TD></TR> </TABLE></OL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D035884)"}, {"Key": "Processor                                                                                           ", "Value": "I013284"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000512463/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000512463/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000512463/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000512463/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000512463/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000512463/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000512463/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000512463/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000512463/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "600985", "RefComponent": "CRM-MD-PCT", "RefTitle": "Parallel Catalog Replication", "RefUrl": "/notes/600985 "}, {"RefNumber": "597319", "RefComponent": "CRM-MD-PCT", "RefTitle": "Individl replication and automatically assigned areas (Perf)", "RefUrl": "/notes/597319 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BBPCRM", "From": "20C", "To": "20C", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "300", "To": "300", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "310", "To": "310", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "BBPCRM 20C", "SupportPackage": "SAPKU20C18", "URL": "/supportpackage/SAPKU20C18"}, {"SoftwareComponentVersion": "BBPCRM 310", "SupportPackage": "SAPKU31004", "URL": "/supportpackage/SAPKU31004"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "BBPCRM", "NumberOfCorrin": 1, "URL": "/corrins/0000512463/63"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}