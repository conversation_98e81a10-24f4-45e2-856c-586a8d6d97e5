{"Request": {"Number": "140741", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 442, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000655702017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000140741?language=E&token=A60F82129AA10FFFB31F3EEE3AD0755B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000140741", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000140741/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "140741"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Performance"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.06.1999"}, "SAPComponentKey": {"_label": "Component", "value": "SD-BIL-RB"}, "SAPComponentKeyText": {"_label": "Component", "value": "Rebate Processing"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Sales and Distribution", "value": "SD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Billing", "value": "SD-BIL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD-BIL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Rebate Processing", "value": "SD-BIL-RB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD-BIL-RB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "140741 - Volume-based rebate:Data enhmnt.S060 Rel.4.* slow"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>When you upgrade to Release 4.0A (or a later release) the sales volumes or accruals for rebate agreements are 0.<br />&#x00A0;&#x00A0;or:<br />When you update to Release 4.0A (or a later release), no cumulative values exist for conditions with condition update.<br />&#x00A0;&#x00A0;or:<br />Performance problems occur during reorganization of statistical data in S060.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Volume-based rebate, rebate agreement, sales volume, S060, condition update<br />Programs RV15C001, SDBONTO2, SDBONTO4<br />Transactions VBO2, VBO3<br />Error message VK300<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>In Release 4.0A, statistics structure S060 was changed. After a release upgrade to Release 4.0A or a later release, you must set up the dataset in table S060 again. To do this, use report SDBONTO4 (see IMG).<br />Proceed as follows:</p> <OL>1. Update KONP (--&gt; report SDBONTO1)</OL> <OL>2. Update KONV (--&gt; report SDBONTO2)</OL> <OL>3. Update S060 (--&gt; report SDS060RB)</OL> <p>You can call up or plan the individual steps from report SDBONTO4.<br />For large datasets (a large number of billing documents) very long runtimes may occur in steps 2 and 3.<br />In addition the version of report SDBONTO2 of report 122863 (contained<br />in Hot Packages 10 to 16) may cause problems.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>In this case, proceed as follows:</p> <b>Step 1</b><br /> <p>Use the normal path (call up from SDBONTO4).<br />If the program reports error VF803 refer to Note 157717.</p> <b>Step 2</b><br /> <p>Should NOT be performed from report SDBONTO4. For runtime improvement, the attached version of report SDBONTO2 has two advantages:</p> <OL>1. The source text was improved concerning runtime.</OL> <OL>2. The area of billing documents to be updated can be restricted. This has two advantages:</OL> <OL><OL>a) You can exclude billing documents you do not need to update (for example if you already settled all relevant volume-based rebate agreements) from processing.</OL></OL> <OL><OL>b) You can devide up billing documents in small portions which can be updated parallely in several jobs.<br /></OL></OL> <b>Possible errors in the job log:</b><br /> <p>VF806&#x00A0;&#x00A0;\"Condition record for condition type &amp; was not yet updated\"<br /></p> <OL><OL>a) Either report SDBONTO1 was&#x00A0;&#x00A0;started incorrectly or not at all.<br />==&gt; Start the report again as described in step 1 and then again<br />SDBONTO2 (step 2).</OL></OL> <OL><OL>b) Or the condition records available in the report do no longer exist in the system (deleted or archived).<br />==&gt; Consequently, the revenues of these conditions are also no longer<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;required and you can ignore the error.</OL></OL> <p><br />V1014 \"Material &amp;1, condition &amp;2: Unit of measure is not allowed\"<br /><br />==&gt; The document cannot be updated. You should correct the cause of the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;error and then perform step 2 (report SDBONTO2) again.<br /><br />BM302 \"Internal unit &amp;, language &amp; is not maintained\"<br /><br />==&gt; This error message has no effect on the update.<br /></p> <b>Note in addition:</b><br /> <UL><LI>If you use the version of report SDBONTO2 of Note 122863 (contained in Hot Packages 10 to 16), you should better replace it by the attached current version.</LI></UL> <UL><LI>IMPORTANT: If you decide not to replace the source code of report SDBONTO2 in the original report directly but to create a new report for the new version (with a new name), you should deactivate fixed point arithmetic in this new report (as it is done in the original one). The easiest way to do this is, in this case, to copy report SDBONTO2 first and to replace the source code then. In this way you can make sure that the new report has the same attributes and text elements.</LI></UL><UL><LI>You should deactivate logging of processed records in the update run, since in this logging, too many unnecessary main memory operations would be performed.</LI></UL> <b>Step 3</b><br /> <p>Should also NOT be performed from report SDBONTO4. For more information on report SDS060RB, see Note 116638.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SD-BF-PR (Pricing)"}, {"Key": "Responsible                                                                                         ", "Value": "D021957"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000140741/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000140741/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000140741/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000140741/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000140741/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000140741/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000140741/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000140741/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000140741/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "196145", "RefComponent": "SD-BIL-RB", "RefTitle": "Volume-based rebate: Upgrade from 3.0/3.1 to 4.X", "RefUrl": "/notes/196145"}, {"RefNumber": "179482", "RefComponent": "SD-BIL-RB", "RefTitle": "Volume-based rebate: Upgrade from 3.0/3.1 to 4.X", "RefUrl": "/notes/179482"}, {"RefNumber": "157717", "RefComponent": "SD-BIL-RB", "RefTitle": "Volume-based rebate: Improvement of report SDBONTO1", "RefUrl": "/notes/157717"}, {"RefNumber": "122863", "RefComponent": "SD-BIL-RB", "RefTitle": "Rebate:Data enhancement S060 in Rel. 4.* is slow", "RefUrl": "/notes/122863"}, {"RefNumber": "116638", "RefComponent": "SD-BIL-RB", "RefTitle": "Upgrade of info. structure S060 in Release 4.*", "RefUrl": "/notes/116638"}, {"RefNumber": "1060629", "RefComponent": "SD-BIL-RB", "RefTitle": "SDS060RB/ENH_REBATE_S469RB:Reorg. of data relevant to rebate", "RefUrl": "/notes/1060629"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1060629", "RefComponent": "SD-BIL-RB", "RefTitle": "SDS060RB/ENH_REBATE_S469RB:Reorg. of data relevant to rebate", "RefUrl": "/notes/1060629 "}, {"RefNumber": "196145", "RefComponent": "SD-BIL-RB", "RefTitle": "Volume-based rebate: Upgrade from 3.0/3.1 to 4.X", "RefUrl": "/notes/196145 "}, {"RefNumber": "179482", "RefComponent": "SD-BIL-RB", "RefTitle": "Volume-based rebate: Upgrade from 3.0/3.1 to 4.X", "RefUrl": "/notes/179482 "}, {"RefNumber": "157717", "RefComponent": "SD-BIL-RB", "RefTitle": "Volume-based rebate: Improvement of report SDBONTO1", "RefUrl": "/notes/157717 "}, {"RefNumber": "116638", "RefComponent": "SD-BIL-RB", "RefTitle": "Upgrade of info. structure S060 in Release 4.*", "RefUrl": "/notes/116638 "}, {"RefNumber": "122863", "RefComponent": "SD-BIL-RB", "RefTitle": "Rebate:Data enhancement S060 in Rel. 4.* is slow", "RefUrl": "/notes/122863 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B17", "URL": "/supportpackage/SAPKH40B17"}, {"SoftwareComponentVersion": "SAP_HR 40B", "SupportPackage": "SAPKE40B17", "URL": "/supportpackage/SAPKE40B17"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B01", "URL": "/supportpackage/SAPKH45B01"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 1, "URL": "/corrins/0000140741/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}