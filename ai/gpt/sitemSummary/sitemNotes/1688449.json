{"Request": {"Number": "1688449", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 961, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000010030482017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001688449?language=E&token=CDF7C37391592C14FA0ABC4A623A3D91"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001688449", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001688449/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1688449"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Currentness", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.03.2012"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-CH-IS-H"}, "SAPComponentKeyText": {"_label": "Component", "value": "Hospital"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Switzerland", "value": "XX-CSC-CH", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-specific component", "value": "XX-CSC-CH-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hospital", "value": "XX-CSC-CH-IS-H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH-IS-H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1688449 - IS-H CH:Med.Statistic new - SwissDRG survey"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1688449&TargetLanguage=EN&Component=XX-CSC-CH-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/1688449/D\" target=\"_blank\">/notes/1688449/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The Federal Statistical Office BFS has published a new interface concept for medical statistics, valid from the year of survey 2012. Therefore, the previous program &quot;Medical Statistics (Minimum/Newborn/Psych.-DS/)&quot;(RNWCHSMED)from then (for the data as of 2012) an interface that is no longer valid.<br />Until now, the &quot;SwissDRG survey&quot; from SwissDRG AG could not be made available.</p><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>RNWCHSMED, medical statistics, MX, MB, MN, MP, MD, SwissDRG survey</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>Prerequisite:</p> <UL><LI>Country Version Switzerland</LI></UL> <p>Reason:<br />Changed legal requirements</p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><p>The new program &quot;Medical Statistics New (Acc. BFS 2012)&quot;(RNWCHMEDSTAT) replaces the old program (RNWCHSMED). It is also able to carry out a SwissDRG survey.<br />Note the extensive manual activities. You may be able to wait for the patch that contains the new program completely.<br />Read the program documentation. Among other things, it informs you about the unconditional Customizing settings.<br />Note that some fields, data records, or entire files must be filled using BAdIs.<br />The attachment to this SAP Note contains documentation about the program, for example, and various methods that you can also import.</p></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "IS-H (Hospital)"}, {"Key": "Owner                                                                                    ", "Value": "<PERSON> (C5025082)"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON> (C5025082)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001688449/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "RNWCHMEDSTAT_Doku.zip", "FileSize": "5", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000197872012&iv_version=0002&iv_guid=046056BBCAA31943835712AD80E23EBB"}, {"FileName": "IF_EX_ISH_CH_BFS_Doku.zip", "FileSize": "1", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000197872012&iv_version=0002&iv_guid=995FB2935A45A64B9B252443C4779ED5"}, {"FileName": "ISH_CH_BFS_Doku.zip", "FileSize": "1", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000197872012&iv_version=0002&iv_guid=6894520A6740284BA10F2BEF21FEF268"}, {"FileName": "RNWCHMEDSTAT_Textsymbole.zip", "FileSize": "84", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000197872012&iv_version=0002&iv_guid=0E146877BC81D1419B3D837F0DAA14E4"}, {"FileName": "RNWCHMEDSTAT_Selektionstexte.zip", "FileSize": "54", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000197872012&iv_version=0002&iv_guid=2CC85B17D197274A905F7DA7C06BDEC4"}, {"FileName": "MODIFY_DRGMEDI_DS_Doku.zip", "FileSize": "1", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000197872012&iv_version=0002&iv_guid=859F5DBD41DA694AAA11E8A5829B9B7B"}, {"FileName": "MODIFY_DRGKOSTEN_DS_Doku.zip", "FileSize": "1", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000197872012&iv_version=0002&iv_guid=DD376EBFE7D0FE4382B8F0B90FAA38F5"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "1723894", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Med. New Statistics - RNWCHMEDSTAT - Severity", "RefUrl": "/notes/1723894"}, {"RefNumber": "1713966", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Med. New Statistics - RNWCHMEDSTAT - Correct. (2)", "RefUrl": "/notes/1713966"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "1723894", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Med. New Statistics - RNWCHMEDSTAT - Severity", "RefUrl": "/notes/1723894 "}, {"RefNumber": "1713966", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Med. New Statistics - RNWCHMEDSTAT - Correct. (2)", "RefUrl": "/notes/1713966 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "IS-H", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "616", "To": "616", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": [{"SoftwareComponentVersion": "IS-H 600", "SupportPackage": "SAPK-60031INISH", "URL": "/supportpackage/SAPK-60031INISH"}, {"SoftwareComponentVersion": "IS-H 602", "SupportPackage": "SAPK-60220INISH", "URL": "/supportpackage/SAPK-60220INISH"}, {"SoftwareComponentVersion": "IS-H 603", "SupportPackage": "SAPK-60321INISH", "URL": "/supportpackage/SAPK-60321INISH"}, {"SoftwareComponentVersion": "IS-H 603", "SupportPackage": "SAPK-60320INISH", "URL": "/supportpackage/SAPK-60320INISH"}, {"SoftwareComponentVersion": "IS-H 604", "SupportPackage": "SAPK-60421INISH", "URL": "/supportpackage/SAPK-60421INISH"}, {"SoftwareComponentVersion": "IS-H 604", "SupportPackage": "SAPK-60420INISH", "URL": "/supportpackage/SAPK-60420INISH"}, {"SoftwareComponentVersion": "IS-H 605", "SupportPackage": "SAPK-60512INISH", "URL": "/supportpackage/SAPK-60512INISH"}, {"SoftwareComponentVersion": "IS-H 605", "SupportPackage": "SAPK-60513INISH", "URL": "/supportpackage/SAPK-60513INISH"}, {"SoftwareComponentVersion": "IS-H 606", "SupportPackage": "SAPK-60605INISH", "URL": "/supportpackage/SAPK-60605INISH"}, {"SoftwareComponentVersion": "IS-H 606", "SupportPackage": "SAPK-60604INISH", "URL": "/supportpackage/SAPK-60604INISH"}]}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 5, "URL": "/corrins/0001688449/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": "\r\n<p><code><br />\r\n<br />\r\n------------------------------------------------------------------------<br />\r\n|Manual post-implementation steps                                                   |<br />\r\n------------------------------------------------------------------------<br />\r\n|VALID FOR                                                            |<br />\r\n|Software Component   IS-H                          IS-Hospital       |<br />\r\n| Release 600          SAPK-60027INISH - SAPK-60030INISH               |<br />\r\n| Release 602          SAPK-60216INISH - SAPK-60219INISH               |<br />\r\n| Release 603          SAPK-60316INISH - SAPK-60320INISH               |<br />\r\n| Release 604          SAPK-60416INISH - SAPK-60420INISH               |<br />\r\n| Release 605          SAPK-60507INISH - SAPK-60512INISH               |<br />\r\n| Release 606          SAPK-60601INISH - SAPK-60604INISH               |<br />\r\n------------------------------------------------------------------------<br /></code></p>\r\n<div style=\"margin-left: 2em\"><code>1. Adjust text elements and documentation of the report RNWCHMEDSTAT</code></div>\r\n<ul>\r\n<li><code>Extract the attached files RNWCHMEDSTAT_Doku.zip, RNWCHMEDSTAT_Selektionstexte.zip, RNWCHMEDSTAT_TextSymbole.zip.<br />\r\n<br />\r\n(Note that you cannot download the attached files using OSS, but only from SAP Service Marketplace (see also SAP Notes 480180 and 13719 for information about importing attachments).</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Go to transaction SE38.</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>In the \\\"Program\\\" input field, enter the name of the report RNWCHMEDSTAT and choose \\\"Display\\\".</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>In the menu, choose \"Goto -&gt; Text Elements -&gt; Selection Texts\".</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Choose \"Change\" and change the selection texts of the report as described in the unpacked file RNWCHMEDSTAT_Selection Text.doc.</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Save and activate the selection texts.</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Go to transaction SE38.</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>In the \\\"Program\\\" input field, enter the name of the report RNWCHMEDSTAT and choose \\\"Display\\\".</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>In the menu, choose \"Goto -&gt; Text Elements -&gt; Text Symbols\".</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Choose \"Change\" and change the text symbols of the report as described in the unpacked file RNWCHMEDSTAT_TextSymbole.doc.</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Save and activate the text symbols.</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Call transaction SE61.</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Select the document class for \\\"Report, FunctionGr.,Log.DB\\\".</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Enter the name of the report RNWCHMEDSTAT in the \\\"Report/Module Pool\\\" input field and choose \\\"Change\\\". If the document does not yet exist, choose Create.</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Delete all previous documentation for the report.</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>In the menu, choose Document -&gt; Upload and then the format \\\"ITF\\\".</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>In the input field \"ITF File\", enter the extracted file RNWCHMEDSTAT_Doku.itf and choose \\\"Transfer\\\".</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Save and activate the report documentation.</code></li>\r\n</ul>\r\n<div style=\"margin-left: 2em\"><code>2. Create transaction NWCHMEDSTAT</code></div>\r\n<ul>\r\n<li><code>Call transaction SE93 (\"Transaction Maintenance\").</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Enter the transaction code NWCHMEDSTAT and choose \"Create\".</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>In the following dialog box, enter the short text \"IS-H CH: Medical Statistics New\" and select \\\"Program and selection screen (report transaction)\\\" as the start object from the selection group. Confirm your entry with \\\"Continue(Enter)\\\".</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>In the following window, enter the program name RNWCHMEDSTAT under \"Program\".</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>In addition, enter the object N_EINR_TCO as the object. Then choose the \\\"Values\\\" button and then enter the value \\\"NWCHMEDSTAT\\\" for the field \\\"TCD\\\". Confirm your entry with \\\"Continue(Enter)\\\".</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Under \\\"GUI Support\\\", select the three checkboxes for \\\"SAP GUI for HTML\\\", \\\"SAP GUI for Java\\\", and \\\"SAP GUI for Windows\\\".</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Save your entry. When you do this, the system asks you for the package. Enter the package NCH1.</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>After you successfully save and create the transaction, exit the dialog.</code></li>\r\n</ul>\r\n<p><code><br />\r\n<br />\r\n------------------------------------------------------------------------<br />\r\n|Manual Activity                                                    |<br />\r\n------------------------------------------------------------------------<br />\r\n|VALID FOR                                                            |<br />\r\n|Software Component   IS-H                          IS-Hospital       |<br />\r\n| Release 600          SAPK-60027INISH - SAPK-60030INISH               |<br />\r\n| Release 602          SAPK-60216INISH - SAPK-60219INISH               |<br />\r\n| Release 603          SAPK-60316INISH - SAPK-60319INISH               |<br />\r\n| Release 604          SAPK-60416INISH - SAPK-60419INISH               |<br />\r\n| Release 605          SAPK-60507INISH - SAPK-60511INISH               |<br />\r\n------------------------------------------------------------------------<br /></code></p>\r\n<div style=\"margin-left: 2em\"><code>1. Create a new structure</code></div>\r\n<ul>\r\n<li><code>Create the structure RNWCH_SDRG_BFS_MEDIC</code></li>\r\n</ul>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Call transaction SE11.</code></li>\r\n</ul>\r\n</div>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Enter RNWCH_SDRG_BFS_MEDIC in the \"Data type\" field and choose \"Create\".</code></li>\r\n</ul>\r\n</div>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>In the subsequent dialog box, select \"Structure\" and choose \"Continue\".</code></li>\r\n</ul>\r\n</div>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Enter 'Structure Swiss-DRG - BFS Data - Drugs' in the 'Short Description' field.</code></li>\r\n</ul>\r\n</div>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Choose the \"Components\" tab page and add the following fields:</code></li>\r\n</ul>\r\n</div>\r\n<p><code>                     <u>Component Component Type</u><br />\r\nATC_CODE CHAR07<br />\r\nADD_INFO CHAR18<br />\r\nROUTE_ADMIN CHAR05<br />\r\nCUM_DOSE CHAR15<br />\r\nUNIT CHAR05</code></p>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Save and activate the structure.<br />\r\nAs the enhancement category, select \\\"Can be enhanced (character-type\\\").<br />\r\nAssign the structure to the package NCH1.</code></li>\r\n</ul>\r\n</div>\r\n<div style=\"margin-left: 2em\"><code>1. Create new table types</code></div>\r\n<ul>\r\n<li><code>Create the table type ISH_T_RNWCH_SDRG_BFS_MEDIC</code></li>\r\n</ul>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Call transaction SE11.</code></li>\r\n</ul>\r\n</div>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Enter ISH_T_RNWCH_SDRG_BFS_MEDIC in the \"Data type\" field and choose \"Create\".</code></li>\r\n</ul>\r\n</div>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>In the subsequent dialog box, select \"Table type\" and choose \"Continue\".</code></li>\r\n</ul>\r\n</div>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Enter \"Table Type for BFS Data - Drugs\" in the \"Short Description\" field.</code></li>\r\n</ul>\r\n</div>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Choose the \"Line Type\" tab page and enter RNWCH_SDRG_BFS_MEDIC in the \"Line Type\" field.</code></li>\r\n</ul>\r\n</div>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Save and activate the table type.<br />\r\nAssign the structure to the package NCH1.</code></li>\r\n</ul>\r\n</div>\r\n<div style=\"margin-left: 2em\"><code>2. Create a new structure</code></div>\r\n<ul>\r\n<li><code>Create the structure RNWCH_SDRG_MED_DATA.</code></li>\r\n</ul>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Call transaction SE11.</code></li>\r\n</ul>\r\n</div>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Enter 'RNWCH_SDRG_MED_DATA' in the 'Data type' field and choose 'Create'.</code></li>\r\n</ul>\r\n</div>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>In the subsequent dialog box, select \"Structure\" and choose \"Continue\".</code></li>\r\n</ul>\r\n</div>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Enter 'Structure Swiss-DRG - Relevant Medical Data' in the 'Short Description' field.</code></li>\r\n</ul>\r\n</div>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Choose the \"Components\" tab page and add the following fields:</code></li>\r\n</ul>\r\n</div>\r\n<p><code>                     <u>Component Component Type</u><br />\r\nMANDT MANDT<br />\r\nEINRI EINRI<br />\r\nFALNR FALNR<br />\r\nBETNUM INSTNR<br />\r\nNOGACODE ISH_KHNUM<br />\r\nKANTON NCH_TARCDE<br />\r\nKENNZ CHAR1<br />\r\nNEW DATE CHAR1<br />\r\nPSYDAT CHAR1<br />\r\nFALKOSTDAT CHAR1<br />\r\nKANTONDAT CHAR1<br />\r\nNNAME NNAME_PAT<br />\r\nVNAME, VNAME_PAT<br />\r\nGSCHL GSCHLE<br />\r\nGBDAT, RI_GBDAT<br />\r\nALTEINTR CHAR3<br />\r\nPSTLZ, ISH_PSTLZO<br />\r\nNATIO, RI_NATIO<br />\r\nBEHANDART CHAR1<br />\r\nBEHANDKLAS ISH_PAYCL<br />\r\nMAIN COST ISH_KOSTL<br />\r\nKOSTTRAG KH_KOSTR<br />\r\nGEBNR NCH_GEBNR<br />\r\nMULTIPLE ISH_MEHRL<br />\r\nBRANG LFDMEHR<br />\r\nLAENG CHAR_02<br />\r\nGREIN ISH_EINH<br />\r\nGEBDATM, RI_GBDAT<br />\r\nSTALT1 NCH_GESTA1<br />\r\nSTALT2 NCH_GESTA2<br />\r\nANZSW NCH_ANZSCH<br />\r\nANZLBGEB ISH_CHILD<br />\r\nANZTDGEB NCH_ANZTOT<br />\r\nANZABBR NCH_ANZABB<br />\r\nFAMST, RI_FAMST<br />\r\nAUFVOREINTR AUFENT<br />\r\nTLERW PARTIAL<br />\r\nVOERW VOLLERW<br />\r\nNIERW NIEERW<br />\r\nHAUSH HAUSARB<br />\r\nAUSBI FOREIGN<br />\r\nREHAB, REHAB<br />\r\nRENTE RENTE<br />\r\nSCHUZ SCHUZ<br />\r\nUNERW UNERW<br />\r\nSCHUL TRAINING IMAGE<br />\r\nINSTEIN EINWI<br />\r\nFREIW FREIW<br />\r\nFREIH FREIENTZ<br />\r\nNo. Days CHAR04<br />\r\nBHAND BEHANDL<br />\r\nNEW NEW EURO<br />\r\nDEPOT DEPOT<br />\r\nADEPR ANTIDEP<br />\r\nTRNQU, TRANQU<br />\r\nHYPNO HYPNO<br />\r\nAEPIL ANTIEPI<br />\r\nLITHM LITHIUM<br />\r\nSUBST SUCHTSUB<br />\r\nAVERS SEARCH TAV<br />\r\nAPARK ANTIPARK<br />\r\nOTHER APHRM<br />\r\nMEDKL, MEDKL<br />\r\nENTAUSTR AUSTRI<br />\r\nORDER EXTRA. EXP.<br />\r\nAUFNACHAUSTR_PR AUFAUS<br />\r\nBEHANDAUSTR BNAUS<br />\r\nBEHANDAUSTR_PR CHAR01<br />\r\nAUFNGEW CHAR04<br />\r\nHEADER CHAR02<br />\r\nDRGSTAT CHAR01<br />\r\nLOCAL SPITAL CHAR04<br />\r\nRVNUM_PR, ISH_RVNUM<br />\r\nKRVERS_PR CHAR04<br />\r\nT_MEDIC_PR, ISH_T_RNWCH_SDRG_BFS_MEDIC</code></p>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Save and activate the structure.<br />\r\nSelect the enhancement category \\\"Can Be Enhanced (Deep)\".<br />\r\nAssign the structure to the package NCH1.</code></li>\r\n</ul>\r\n</div>\r\n<div style=\"margin-left: 2em\"><code>1. Document maintenance</code></div>\r\n<ul>\r\n<li><code>Create data element supplement 319 for the data element NXFELD</code></li>\r\n</ul>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Call transaction SE61.</code></li>\r\n</ul>\r\n</div>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Select the document class \\\"Data Element Supplement (DZ)\\\" and enter the value \\\"NXFELD\\\" in the \"Data Element\" input field and the value \\\"319\\\" in the \"Addition\" input field.</code></li>\r\n</ul>\r\n</div>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Choose \\\"Create\\\".</code></li>\r\n</ul>\r\n</div>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Under &amp;USE&amp;, enter the following text:</code></li>\r\n</ul>\r\n</div>\r\n<p><code>                    If you select this checkbox, the case number is also output in the file.<br />\r\nThis option should only be activated for test purposes.</code></p>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Save the changes actively.</code></li>\r\n</ul>\r\n</div>\r\n<p><code><br />\r\n<br />\r\n------------------------------------------------------------------------<br />\r\n|Manual Activity                                                    |<br />\r\n------------------------------------------------------------------------<br />\r\n|VALID FOR                                                            |<br />\r\n|Software Component   IS-H                          IS-Hospital       |<br />\r\n| Release 600          SAPK-60027INISH - SAPK-60030INISH               |<br />\r\n| Release 602          SAPK-60216INISH - SAPK-60219INISH               |<br />\r\n| Release 603          SAPK-60316INISH - SAPK-60320INISH               |<br />\r\n| Release 604          SAPK-60416INISH - SAPK-60420INISH               |<br />\r\n| Release 605          SAPK-60507INISH - SAPK-60512INISH               |<br />\r\n------------------------------------------------------------------------<br /></code></p>\r\n<div style=\"margin-left: 2em\"><code>1. Extract the attached files</code></div>\r\n<ul>\r\n<li><code>Extract the attached files MODIFY_DRGKOSTEN_DS_Doku.zip, MODIFY_DRGMEDI_DS_Doku.zip, IF_EX_ISH_CH_BFS_Doku.zip, and ISH_CH_BFS_Doku.zip.<br />\r\n<br />\r\n(Note that you cannot download the attached files using OSS, but only from SAP Service Marketplace (see also SAP Notes 480180 and 13719 for information about importing attachments).</code></li>\r\n</ul>\r\n<div style=\"margin-left: 2em\"><code>2. Create new methods of the BAdI ISH_CH_BFS</code></div>\r\n<ul>\r\n<li><code>Go to transaction SE18.</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Select \\\"BAdI Name\\\" and enter the value ISH_CH_BFS in the input field. Choose \\\"Change\\\".</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Choose the \\\"Interface\\\" tab page.</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>In the menu bar, choose \"Goto -&gt; Interface\".</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Scroll to the last method and enter the following values in the first free line:</code></li>\r\n</ul>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Method: MODIFY_DRGKOSTEN_DS</code></li>\r\n</ul>\r\n</div>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Level: Instance Method</code></li>\r\n</ul>\r\n</div>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Description: Data Transformation for SwissDRG Costs Data</code></li>\r\n</ul>\r\n</div>\r\n<ul>\r\n<li><code>Select the method MODIFY_DRGKOSTEN_DS and choose \\\"Parameters\\\".</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Enter the following parameters for the new method:</code></li>\r\n</ul>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Parameter: I_FALNR</code></li>\r\n</ul>\r\n</div>\r\n<p><code>                    Type: Importing</code></p>\r\n<p><code>                    Pass Value: Select this field.</code></p>\r\n<p><code>                    Typing Method: Types</code></p>\r\n<p><code>                    Associated Type: NFAL-FALNR</code></p>\r\n<p><code>                    Description: IS-H: Case number</code></p>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Parameter: I_MINIMAL</code></li>\r\n</ul>\r\n</div>\r\n<p><code>                    Type: Importing</code></p>\r\n<p><code>                    Pass Value: Select this field.</code></p>\r\n<p><code>                    Typing Method: Types</code></p>\r\n<p><code>                    Associated Type: NWCHBFS-STRING</code></p>\r\n<p><code>                    Description: Minimum Data Record</code></p>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Parameter: C_KOSTEN</code></li>\r\n</ul>\r\n</div>\r\n<p><code>                    Type: Changing</code></p>\r\n<p><code>                    Pass Value: Select this field.</code></p>\r\n<p><code>                    Typing Method: Types</code></p>\r\n<p><code>                    Associated Type: NWCHBFS-STRING</code></p>\r\n<p><code>                    Description: SwissDRG cost data</code></p>\r\n<ul>\r\n<li><code>Save and activate these changes.</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>In the menu bar, choose \"Goto -&gt; Documentation -&gt; By Component\".</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Delete all existing documentation.</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Now choose \\\"Document-&gt;Upload\\\".</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>In the following dialog box (\"Upload\"), select the format \\\"ITF\\\".</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Now import the extracted file MODIFY_DRGKOSTEN_DS_Doku.itf</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Save the changes actively.</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Choose \"Back\" (F3) to return to the Class Builder.</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Choose \"Methods\" to return to the overview of the methods.</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Scroll to the last method and enter the following values in the first free row:</code></li>\r\n</ul>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Method: MODIFY_DRGMEDI_DS</code></li>\r\n</ul>\r\n</div>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Level: Instance Method</code></li>\r\n</ul>\r\n</div>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Description: Data Transformation for SwissDRG Drug Data</code></li>\r\n</ul>\r\n</div>\r\n<ul>\r\n<li><code>Select the method MODIFY_DRGMEDI_DS and choose \\\"Parameters\\\".</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Enter the following parameters for the new method:</code></li>\r\n</ul>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Parameter: I_FALNR</code></li>\r\n</ul>\r\n</div>\r\n<p><code>                    Type: Importing</code></p>\r\n<p><code>                    Pass Value: Select this field.</code></p>\r\n<p><code>                    Typing Method: Types</code></p>\r\n<p><code>                    Associated Type: NFAL-FALNR</code></p>\r\n<p><code>                    Description: IS-H: Case number</code></p>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Parameter: I_MINIMAL</code></li>\r\n</ul>\r\n</div>\r\n<p><code>                    Type: Importing</code></p>\r\n<p><code>                    Pass Value: Select this field.</code></p>\r\n<p><code>                    Typing Method: Types</code></p>\r\n<p><code>                    Associated Type: NWCHBFS-STRING</code></p>\r\n<p><code>                    Description: Minimum Data Record</code></p>\r\n<div style=\"margin-left: 2em\">\r\n<ul>\r\n<li><code>Parameter: CT_MEDI</code></li>\r\n</ul>\r\n</div>\r\n<p><code>                    Type: Changing</code></p>\r\n<p><code>                    Pass Value: Select this field.</code></p>\r\n<p><code>                    Typing Method: Types</code></p>\r\n<p><code>                    Associated Type: ISH_CH_YT_EDI_STRING</code></p>\r\n<p><code>                    Description: SwissDRG drug data</code></p>\r\n<ul>\r\n<li><code>Save and activate these changes.</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>In the menu bar, choose \"Goto -&gt; Documentation -&gt; By Component\".</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Delete all existing documentation.</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Now choose \\\"Document-&gt;Upload\\\".</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>In the following dialog box (\"Upload\"), select the format \\\"ITF\\\".</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Now import the extracted file MODIFY_DRGMEDI_DS_Doku.itf</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Save the changes actively.</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Choose \"Back\" (F3) to return to the Class Builder.</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Choose \"Methods\" to return to the overview of the methods.</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>In the menu bar, choose \"Goto -&gt; Documentation -&gt; Interface\".</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Delete all existing documentation.</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Now choose \\\"Document-&gt;Upload\\\".</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>In the following dialog box (\"Upload\"), select the format \\\"ITF\\\".</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Now import the extracted file IF_EX_ISH_CH_BFS_Doku.itf</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Save the changes actively.</code></li>\r\n</ul>\r\n<div style=\"margin-left: 2em\"><code>1. Change Documentation of BAdI ISH_CH_BFS</code></div>\r\n<ul>\r\n<li><code>Call transaction SE61.</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>In the selection field Document Class, select the value Implementation Guide Chapter (SIMG).</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>In the input field \"Chapter\", enter the value ISH_CH_BFS. Choose \\\"Change\\\".</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>In the menu bar, choose \"Goto -&gt; Change Editor\" to switch to the new line editor.</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Delete all existing documentation.</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Now choose \\\"Document-&gt;Upload\\\".</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>In the following \"Upload\" dialog box, select the format \\\"ITF\\\".</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>In the input field ITF File, enter the extracted file ISH_CH_BFS_Doku.itf and choose Transfer.</code></li>\r\n</ul>\r\n<ul>\r\n<li><code>Save and activate this change.</code></li>\r\n</ul>\r\n<code><br /></code>"}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 5, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 3, "state": "Error"}, "Prerequisites": {"_label": "Prerequisite", "value": 4, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1604608 ", "URL": "/notes/1604608 ", "Title": "IS-H CH: SwissDRG - MCD Data Determination", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1660655 ", "URL": "/notes/1660655 ", "Title": "IS-H CH: SwissDRG - DRG Procedures for MCD/BFS Data", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1688449 ", "URL": "/notes/1688449 ", "Title": "IS-H CH: New Med.Statistics - SwissDRG Survey", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1691689 ", "URL": "/notes/1691689 ", "Title": "IS-H CH: Swiss-DRG Revenue Accrual (Div. Adjustments)", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1604608 ", "URL": "/notes/1604608 ", "Title": "IS-H CH: SwissDRG - MCD Data Determination", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1660655 ", "URL": "/notes/1660655 ", "Title": "IS-H CH: SwissDRG - DRG Procedures for MCD/BFS Data", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1688449 ", "URL": "/notes/1688449 ", "Title": "IS-H CH: New Med.Statistics - SwissDRG Survey", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1691689 ", "URL": "/notes/1691689 ", "Title": "IS-H CH: Swiss-DRG Revenue Accrual (Div. Adjustments)", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1604608 ", "URL": "/notes/1604608 ", "Title": "IS-H CH: SwissDRG - MCD Data Determination", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1660655 ", "URL": "/notes/1660655 ", "Title": "IS-H CH: SwissDRG - DRG Procedures for MCD/BFS Data", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1688449 ", "URL": "/notes/1688449 ", "Title": "IS-H CH: New Med.Statistics - SwissDRG Survey", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1691689 ", "URL": "/notes/1691689 ", "Title": "IS-H CH: Swiss-DRG Revenue Accrual (Div. Adjustments)", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "605", "ValidTo": "605", "Number": "1604608 ", "URL": "/notes/1604608 ", "Title": "IS-H CH: SwissDRG - MCD Data Determination", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "605", "ValidTo": "605", "Number": "1660655 ", "URL": "/notes/1660655 ", "Title": "IS-H CH: SwissDRG - DRG Procedures for MCD/BFS Data", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "605", "ValidTo": "605", "Number": "1688449 ", "URL": "/notes/1688449 ", "Title": "IS-H CH: New Med.Statistics - SwissDRG Survey", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "605", "ValidTo": "605", "Number": "1691689 ", "URL": "/notes/1691689 ", "Title": "IS-H CH: Swiss-DRG Revenue Accrual (Div. Adjustments)", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "606", "ValidTo": "606", "Number": "1660655 ", "URL": "/notes/1660655 ", "Title": "IS-H CH: SwissDRG - DRG Procedures for MCD/BFS Data", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "606", "ValidTo": "606", "Number": "1688449 ", "URL": "/notes/1688449 ", "Title": "IS-H CH: New Med.Statistics - SwissDRG Survey", "Component": "XX-CSC-CH-IS-H"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1688449&TargetLanguage=EN&Component=XX-CSC-CH-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/1688449/D\" target=\"_blank\">/notes/1688449/D</a>."}}}}