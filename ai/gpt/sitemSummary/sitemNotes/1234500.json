{"Request": {"Number": "1234500", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 573, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016565242017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001234500?language=E&token=AB2715C77730DE228A4A5FA7EFB87F3D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001234500", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001234500/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1234500"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.07.2012"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-UMP"}, "SAPComponentKeyText": {"_label": "Component", "value": "OBSOLETE and INACTIVE: Usage measurement of products"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "OBSOLETE and INACTIVE: Usage measurement of products", "value": "SV-SMG-UMP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-UMP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1234500 - Composite SAP Note: Information about automatic update"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p><br />You receive messages in your SAPoffice inbox or you receive express messages with the subject \"New AutoUpdates available\" with various individual error messages.<br /><br />For example:<br />RFC destination CUPOSS not available<br />generation of CUPOSS failed! Please maintain SAPOSS or SAPSNOTE first!<br /><br />When working correctly, the system does not issue a message until there are new updates.<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p><br />0SM_CUP, AUCUPOSS, CUPOSS, SAPOSS, CUP, customer usage, E2E_CU_LOCAL_EXTRACTOR, $CUP, master template, AUTOUPDATE, AUUSERS, AISUSER, AUCUPOSS, AURETRY, AUDELAY, New updates available<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>As of Release 7.1 Support Package (SP) 3 (7.01 SP 28), SAP Solution Manager provides an automatic update function that regularly checks for new updates and provides you with the option to accept or reject these, if required.<br /><br />This mechanism uses a copy of the RFC connection SAPOSS or SAPSNOTE. This copy is named CUPOSS. Instead of the default user OSS_RFC and the password CPIC, it contains the S-user of the customer and the password of the customer.<br />When the automatic update is started for the first time, the system checks whether this connection is already set up. If not, this is displayed in an error message.<br />If the connection CUPOSS already exists but is not working correctly, there may be different reasons:<br />The original (SAPOSS or SAPSNOTE) does not work correctly or<br />the copied RFC connection CUPOSS contains an incorrect S-user, an incorrect password, or some other incorrect configurations.<br />Proceed as described in the solution.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>1.) Ensure that the RFC connection SAPOSS or SAPSNOTE is working correctly. For this, proceed as described in SAP Note 797001.<br />This step is independent of the automatic update function and is crucial for the Correction Workbench (transaction SNOTE).<br /><br />2.) In transaction SM59, copy the functioning RFC connection SAPOSS or SAPSNOTE to a new RFC connection CUPOSS and change the user and password.<br />The new user must be your S-user, with which you access the SAP Service Marketplace and with which you have download authorizations.<br />Enter the password of the S-user in the RFC connection.<br /><br />Choose \"Connection Test\" to check the functionality of the RFC connection.<br /><br />As an alternative, import ST7.1 SP05 or ST7.01 SP29. An improved user guide that provides support for setting up and testing the connections is implemented in this.<br /><br />When working correctly, the system no longer issues an error message until there are new updates.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D041044)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D038767)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001234500/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001234500/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001234500/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001234500/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001234500/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001234500/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001234500/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001234500/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001234500/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "797001", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/797001"}, {"RefNumber": "1653636", "RefComponent": "SV-SMG-SER-ESR", "RefTitle": "InfoCube 0SM_CUP is not available in version A--2nd", "RefUrl": "/notes/1653636"}, {"RefNumber": "1651778", "RefComponent": "SV-SMG-SER-ESR", "RefTitle": "InfoCube 0SM_CUP is not available in version A--1st", "RefUrl": "/notes/1651778"}, {"RefNumber": "1623672", "RefComponent": "SV-SMG-UMP", "RefTitle": "CUP: The extractor E2E_CU_LOCAL_EXTRACTOR returns an error", "RefUrl": "/notes/1623672"}, {"RefNumber": "1158395", "RefComponent": "BW", "RefTitle": "SAPBINews NW7.10 BI ABAP Support Package 07", "RefUrl": "/notes/1158395"}, {"RefNumber": "1136884", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.00 ABAP SP 19", "RefUrl": "/notes/1136884"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "812386", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "RFC connections to SAPNet R/3 front end", "RefUrl": "/notes/812386 "}, {"RefNumber": "1623672", "RefComponent": "SV-SMG-UMP", "RefTitle": "CUP: The extractor E2E_CU_LOCAL_EXTRACTOR returns an error", "RefUrl": "/notes/1623672 "}, {"RefNumber": "1653636", "RefComponent": "SV-SMG-SER-ESR", "RefTitle": "InfoCube 0SM_CUP is not available in version A--2nd", "RefUrl": "/notes/1653636 "}, {"RefNumber": "1651778", "RefComponent": "SV-SMG-SER-ESR", "RefTitle": "InfoCube 0SM_CUP is not available in version A--1st", "RefUrl": "/notes/1651778 "}, {"RefNumber": "1136884", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.00 ABAP SP 19", "RefUrl": "/notes/1136884 "}, {"RefNumber": "1158395", "RefComponent": "BW", "RefTitle": "SAPBINews NW7.10 BI ABAP Support Package 07", "RefUrl": "/notes/1158395 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "710", "To": "710", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}