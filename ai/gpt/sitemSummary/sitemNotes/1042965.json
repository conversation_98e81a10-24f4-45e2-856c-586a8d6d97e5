{"Request": {"Number": "1042965", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 292, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000006251732017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001042965?language=E&token=50186CC146565A4435212EF084746AFF"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001042965", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001042965/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1042965"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "25.07.2007"}, "SAPComponentKey": {"_label": "Component", "value": "CRM-FRW-AFP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Please use CA-WUI-APF instead"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Customer Relationship Management", "value": "CRM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Please use CA-WUI instead", "value": "CRM-FRW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM-FRW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Please use CA-WUI-APF instead", "value": "CRM-FRW-AFP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM-FRW-AFP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1042965 - Launch transactions get an own authorization object"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The launch transactions can call the web ui of a remote system (e.g. an SAP ECC system). It can happen that the authority check within the remote system fails. In consequence the logical link of the navigation bar seems not to work.<br />This note introduces a new authorization object within the source system (e.g. an SAP CRM system) of the launch transaction. This authorization object is checked before the navigation bar is displayed. Therefore entries without the appropriate authorization are not displayed and the user does not see any entries that nevertheless won't work.<br />Please keep in mind that this new authorization object does not perform calls into the remote systems for performance reasons. In consequence the customer needs a synchronized set of authorizations.<br />The same symptom is true for BI reports. </p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>LTX, transaction launcher, launch transaction<br />BI, BW, business intelligence, business warehouse<br />navigation bar, navbar, CRM WebClient, L-shape, CRM application frame<br />authorization, authorization trace, authorization check</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>It's recommended to get this correction as part of support package CRMUIF 5.1 SP07.<br />In case that you have CRMUIF 5.2 you already have the authorization object as part of the system. The authorization proposals shipped by SAP are part of CRMUIF 5.2 SP01. <br />The newly introduced authorization object consists of two different fields: \"Logical link type\" and \"logical link parameter\".<br />The logical link type can have the values A to D. But only the values C (launch transaction) and D (BI report) are of interest in the context of this authorization object.<br />The logical link parameter gets the concrete ids of the launch transactions and the BI reports as value. In case that you do not want to restrict the authorization on the level of individual IDs you can use an asterisk as wildcard. <br />In case that you want to import this note as preliminary correction please process these steps:</p> <UL><LI>Download the attachment \"transport_files_of_A6FK011409.zip\". This zipped file contains both the data file and the co-file (see also note 13719). This import will add the new authorization object C_LL_TGT to your system. You can have a look at this authorization object within transaction SU21, authorization class AAAB (Cross-application Authorization Objects).</LI></UL> <UL><LI>Activate your changes and then import the code corrections of this note.</LI></UL> <UL><LI>Upload the proposed authorization values according to note 368496. Please use the attached file SU22DATA.TXT as data source.</LI></UL> <UL><UL><LI>Type of application: External Service</LI></UL></UL> <UL><UL><LI>Type of External Service: UIU_COMP</LI></UL></UL> <UL><UL><LI>External Service: UIU_COMP_UICMP_LTX_MainWindow_LAUNCH_TRANSAC*</LI></UL></UL> <p> </p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D022659)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D025909)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001042965/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001042965/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001042965/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001042965/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001042965/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001042965/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001042965/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001042965/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001042965/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "transport_files_of_A6FK011409.zip", "FileSize": "4", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000084652007&iv_version=0003&iv_guid=A1C09155354A39438C50F0A51347B074"}, {"FileName": "SU22DATA.TXT", "FileSize": "1", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000084652007&iv_version=0003&iv_guid=96CBC99CF13C694B8FFC598967028F6C"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1539556", "RefComponent": "BC-SEC-AUT", "RefTitle": "FAQ | Administration of authorization default values", "RefUrl": "/notes/1539556"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1112698", "RefComponent": "CRM-FRW-BI", "RefTitle": "SU22 proposals of BI report component", "RefUrl": "/notes/1112698"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1539556", "RefComponent": "BC-SEC-AUT", "RefTitle": "FAQ | Administration of authorization default values", "RefUrl": "/notes/1539556 "}, {"RefNumber": "1112698", "RefComponent": "CRM-FRW-BI", "RefTitle": "SU22 proposals of BI report component", "RefUrl": "/notes/1112698 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "CRMUIF", "From": "510", "To": "510", "Subsequent": ""}, {"SoftwareComponent": "CRMUIF", "From": "520", "To": "520", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "CRMUIF 510", "SupportPackage": "SAPK-51007INCRMUIF", "URL": "/supportpackage/SAPK-51007INCRMUIF"}, {"SoftwareComponentVersion": "CRMUIF 510", "SupportPackage": "SAPK-51008INCRMUIF", "URL": "/supportpackage/SAPK-51008INCRMUIF"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "CRMUIF", "NumberOfCorrin": 3, "URL": "/corrins/0001042965/4415"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 2, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "CRMUIF", "ValidFrom": "510", "ValidTo": "510", "Number": "974189 ", "URL": "/notes/974189 ", "Title": "Exception when no default view is customized", "Component": "CRM-FRW-AFP"}, {"SoftwareComponent": "CRMUIF", "ValidFrom": "510", "ValidTo": "510", "Number": "1042965 ", "URL": "/notes/1042965 ", "Title": "Launch transactions get an own authorization object", "Component": "CRM-FRW-AFP"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1054374", "RefTitle": "Missing entries in the navigation bar", "RefUrl": "/notes/0001054374"}]}}}}}