{"Request": {"Number": "90835", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 232, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014536192017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000090835?language=E&token=638584E35755E359C926DC3053A35562"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000090835", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000090835/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "90835"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 26}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.04.2023"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-SAPSMP-CON"}, "SAPComponentKeyText": {"_label": "Component", "value": "Contents"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Support Portal", "value": "XX-SER-SAPSMP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-SAPSMP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Contents", "value": "XX-SER-SAPSMP-CON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-SAPSMP-CON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "90835 - SAP Case Escalation Procedure"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You need to speed up the solution for a Customer Case that you have submitted to SAP Support.</p>\r\n<p>Please see the solution below and/or try our new guided answer on&#160;<a target=\"_blank\" href=\"https://ga.support.sap.com/dtp/viewer/index.html#/tree/3303/actions/52327\" title=\"https://ga.support.sap.com/dtp/viewer/index.html#/tree/3303/actions/52327\">Working with SAP Product Support</a>.</p>\r\n<p>&#160;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Escalation, Escalate, Customer Case, CIM, Critical Incident Management</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The following note applies to all SAP customers.</p>\r\n<p>It is designed to inform you of the formal case escalation process to follow when your problem/issue is not a production down situation (&#8220;Very High&#8221; priority); but there is a significant enough business impact to justify an increased focus on case processing.<em></em></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>To escalate your case please ensure that it has already been raised from &#8220;Low&#8221; or &#8220;Medium&#8221; priority to &#8220;High&#8221; priority.&#160; For &#8220;High&#8221; priority cases please be sure you have provided the following information regarding the business impact:</p>\r\n<p>Production Systems</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Is there an acceptable workaround?</p>\r\n<p>Is core business functionality severely affected?</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; What is anticipated financial loss to the business due to the issue in question?</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; How many users are affected?</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; How does this issue affect the GoLive date?</p>\r\n<p>&#160;</p>\r\n<p>Pre-Production (Dev/Test/UAT) Systems:<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Is there an acceptable workaround?</p>\r\n<p>What is the anticipated date of release to the user community?</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Will this issue delay the scheduled release date to the user community?</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; What is anticipated financial loss to the business due to the issue in question?</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; How many users are affected?</p>\r\n<p>When escalating more than one cases please prioritize starting with the most critical.</p>\r\n<p>Please include all forms of contact for the person who will work the case (work/mobile phone, email) along with hours of availability.</p>\r\n<p>After updating the case notes please call the CIC hotline to request escalation of your case.&#160; CIC hotline phones numbers are found in SAP Note 560499.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "I039043"}, {"Key": "Processor                                                                                           ", "Value": "Peter 3 Kovacs (I071862)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000090835/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000090835/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000090835/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000090835/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000090835/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000090835/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000090835/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000090835/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000090835/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "560499", "RefComponent": "XX-SER-SAPSMP-SUP", "RefTitle": "Customer Interaction Center: Hotline - Email - Chat", "RefUrl": "/notes/560499"}, {"RefNumber": "376997", "RefComponent": "BW", "RefTitle": "BW-customer incidents with priority 1 (very high)", "RefUrl": "/notes/376997"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3366534", "RefComponent": "XX-SER-FORME", "RefTitle": "Customer Interaction Center Webcast - Case Management", "RefUrl": "/notes/3366534 "}, {"RefNumber": "2287393", "RefComponent": "LOD-SF-PLT", "RefTitle": "Support Processes and Incident Handling Guidelines - SuccessFactors Cloud", "RefUrl": "/notes/2287393 "}, {"RefNumber": "2752144", "RefComponent": "LOD-ANA-ADM", "RefTitle": "SAP Analytics Cloud (SAC) & SAP Digital Boardroom customer cases with priority 1 (very high) terms and conditions", "RefUrl": "/notes/2752144 "}, {"RefNumber": "2682601", "RefComponent": "XX-SER-FORME", "RefTitle": "How to raise the priority of an existing case to Very High by contacting the CIC team", "RefUrl": "/notes/2682601 "}, {"RefNumber": "2609887", "RefComponent": "XX-SER", "RefTitle": "Where can SAP Service Level Agreement (SLA) information be found?", "RefUrl": "/notes/2609887 "}, {"RefNumber": "2498754", "RefComponent": "BI-RA-AO-XLA", "RefTitle": "Analysis Office customer cases with priority 1 (very high)", "RefUrl": "/notes/2498754 "}, {"RefNumber": "2496335", "RefComponent": "BI-LUM-DER", "RefTitle": "Lumira Designer cases with priority 1 (very high)", "RefUrl": "/notes/2496335 "}, {"RefNumber": "2637823", "RefComponent": "XX-PART-GKS-CUS-STW", "RefTitle": "Use of components XX-PART-GKS-CUS* - Messages for GK programs without SAP certification", "RefUrl": "/notes/2637823 "}, {"RefNumber": "2257993", "RefComponent": "BI-BIP-ADM", "RefTitle": "SAP BusinessObjects Business Intelligence platform hotfixes: Limitations and prerequisites", "RefUrl": "/notes/2257993 "}, {"RefNumber": "560499", "RefComponent": "XX-SER-SAPSMP-SUP", "RefTitle": "Customer Interaction Center: Hotline - Email - Chat", "RefUrl": "/notes/560499 "}, {"RefNumber": "376997", "RefComponent": "BW", "RefTitle": "BW-customer incidents with priority 1 (very high)", "RefUrl": "/notes/376997 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}