{"Request": {"Number": "1735590", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 412, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017462132017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001735590?language=E&token=B2FE0D07F8C85A61D22A2695ED821FF2"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001735590", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001735590/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1735590"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "01.09.2014"}, "SAPComponentKey": {"_label": "Component", "value": "BW-PLA-IP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Integrated Planning"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Planning", "value": "BW-PLA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-PLA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Integrated Planning", "value": "BW-PLA-IP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-PLA-IP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1735590 - Q&A on DSO Planning"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to leverage SAP BW PAK based DSO planning</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>DSO planning, PAK,</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You are on AP BW 7.30 SP8 or SAP BW 7.31 SP5 and want to leverage new DSO planning feature</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ul>\r\n<li>Q: What is the minimal software requirements to use DSO planning?</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;A: SAP BW 7.30 SP8 or SAP BW 7.31 SP5</p>\r\n<ul>\r\n<li>Q: How do I activate DSO planning in the system?</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>A1) If you are on 7.30 SP8 or 7.31 SP5/6&#160;&#160;and import note 1799168. For classic BW-IP without HANA you can also set the RSADMIN parameter RSPLS_DSO_PLANNING to X. With 7.30 SP9/7.31 SP7 this is not required. For HANA optimized usage also check note 1793917.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>A2) Create a Data Store Object of type 'direct update' and switch on the planning mode (in settings).</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<ul>\r\n<li>What are current workarounds which need to be adapted later?</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Q1) How can I leverage the new disaggregation copy for key figures of standard aggregation 'No aggregation( X if more than one values occur)?</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;A1) To leverage disaggregation copy in the query you need&#160;to apply SAP NetWeaver 7.0 BW Front End for GUI 720 Front-End Patch (FEP) 900 (or higher) and for SAP NetWeaver 7.0 BW Front End for GUI 730 Front-End Patch (FEP) 200&#160;. Details described in note 1785156.</p>\r\n<ul>\r\n<li>What are known limitations with respect to normal real time InfoCube based planning?</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Q1) How many characterisitcs can be included - as many as in the InfoCube?</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;A1)The number of key fields of a planning enabled DSO on a classical database (i.e. other than Hana) is limited to 16 (as for usual DSOs as well). With Hana this limitation does not exist but those DSOs cannot be ported to another DB. So also no classical content for any SAP supported database can be build with more than 16 keys.</p>\r\n<ul>\r\n<ul>\r\n<li>Q2) Can I have additonal characterisitcs in the data part?</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;A2) No - this is not allowed and would require a new feature (attribute planning).</p>\r\n<ul>\r\n<ul>\r\n<li>Q3) Can I create arbitrary aggreagation levels as in the case of an InfoCube?</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;A3) No. All keys of the DSO must be either contained in the aggregation level directly or must be filled by a derivation (characteristic relationship) from the other characteristics contained in the aggregation level.</p>\r\n<ul>\r\n<ul>\r\n<li>Q4) Can I use the option PROCESS_CHANGED_DATA for commands in BEx Analyzer or BEx Web? (This option makes a planning operate just on changed data)</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;A4) Since this option requires information from the delta buffer, which does not exist in DSO case, it is not yet supported.</p>\r\n<ul>\r\n<ul>\r\n<li>Q5) Does DSO planning also work Hana optimized (note 1637199)?</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;A5) Yes. Hana optimized planning on DSOs is now supported with SP8. It needs note 1799168 and Hana 1.0 SP5 (Revision 46) and 1637148.</p>\r\n<ul>\r\n<li>What are factors influencing the performance?</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Q1) Does the sequence of characteristics influence the performance</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>A1) Yes. As in the case of a usual table with a primary index the most selective characteristic should be placed at the beginning of the table.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Are there any query limitations?</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Q1) Must I specify also UNIT and CURRENCY characteristics?</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>A1)Yes since they are also part of the key. They must be either unique through the filter or part of the grid</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Q1) Does zero suppression work with DSO planning</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>A2) In general as good as for cube. However keep in mind for NO2 key figures (which are not allowed) also suppressed records are taking into account during aggregation. So hidden zero values can lead to unexpected undefined aggregation level on sum levels</li>\r\n<li>\r\n<p>Q3) Can one use physical deletion on DSO</p>\r\n</li>\r\n<li>\r\n<p>A3) Yes with SAP BW 7.30 SP8, 7.31 SP5 or 7.4 SP5 for classic IP runtime<br />For PAK we need SAP BW 7.30 SP9, 7.31 SP7 or 7.4 SP5 and Revision 67</p>\r\n</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Can read from and load into planning DSOs?</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Q1) Can I use WHM staging tools?</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>A1) You can read from planning DSO as from any other provider. However, no BW native detla process is supported from this direct update DSO. Here one can leverage a standard DSO as intermediate target to produce the deltas. In addition you can populate additional key fields like change date by using derive method of characterisitc relationships, planning sequence on save or the logging BADI. If you have NO2 key figures only reading from the lowest level is possible. You cannot load into DSO, for this you can use the APD (transaction RSANWB)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Q2) Can I use the APD to read from and load into planning DSO</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>A2) Yes you can read from and load into a planning DSO using transaction RSANWB. However if you have NO2 key figures only load and reading on the lowest level is allowed.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<ul>\r\n<li>Q:Can I change the default value NONEX for aggregated values of NO2 key figures shown when chidren to not have the same value</li>\r\n<ul>\r\n<li>A: Yes you can use the customizing view RSADMINCV4 in tansaction SM30 to the transaction&#160;SAP Referenz IMG -&gt; SAP&#160;Netweaver -&gt; Business Intelligenz -&gt; Settings for Reporting and Analysis -&gt; General Settings for Reporting and Analysis -&gt; Display of Exeptional Values in a Query Result. Change there the value for error. If this does not fit the need since a error should be displayed differently also the RSADMIN parameter RSR_FORMAT_NOP_VALEXCP can be set.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D029075)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D029075)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001735590/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001735590/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001735590/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001735590/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001735590/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001735590/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001735590/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001735590/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001735590/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1873685", "RefComponent": "BW-PLA-IP", "RefTitle": "DSO Planning - Error with key figure 1ROWCOUNT", "RefUrl": "/notes/1873685"}, {"RefNumber": "1811231", "RefComponent": "BW-PLA-IP", "RefTitle": "BW-IP: DSO Planning - Time dependent navigation attributes", "RefUrl": "/notes/1811231"}, {"RefNumber": "1799168", "RefComponent": "BW-PLA-IP", "RefTitle": "Release DSO Plannig", "RefUrl": "/notes/1799168"}, {"RefNumber": "1793917", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1793917"}, {"RefNumber": "1785156", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Query Designer: NO2 - Dissagregation: Cells", "RefUrl": "/notes/1785156"}, {"RefNumber": "1718885", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "InfoObject: NO2 aggregation for key figures", "RefUrl": "/notes/1718885"}, {"RefNumber": "1637199", "RefComponent": "BW-PLA-IP", "RefTitle": "Using the planning applications KIT (PAK)", "RefUrl": "/notes/1637199"}, {"RefNumber": "1637148", "RefComponent": "BW-PLA-IP", "RefTitle": "BW on HANA: Activation of Planning Application Kit", "RefUrl": "/notes/1637148"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2465366", "RefComponent": "BW-PLA-IP", "RefTitle": "Limited Performance by planning on direct update DSO in SAP BW  7.40", "RefUrl": "/notes/2465366 "}, {"RefNumber": "1637199", "RefComponent": "BW-PLA-IP", "RefTitle": "Using the planning applications KIT (PAK)", "RefUrl": "/notes/1637199 "}, {"RefNumber": "1637148", "RefComponent": "BW-PLA-IP", "RefTitle": "BW on HANA: Activation of Planning Application Kit", "RefUrl": "/notes/1637148 "}, {"RefNumber": "1873685", "RefComponent": "BW-PLA-IP", "RefTitle": "DSO Planning - Error with key figure 1ROWCOUNT", "RefUrl": "/notes/1873685 "}, {"RefNumber": "1799168", "RefComponent": "BW-PLA-IP", "RefTitle": "Release DSO Plannig", "RefUrl": "/notes/1799168 "}, {"RefNumber": "1811231", "RefComponent": "BW-PLA-IP", "RefTitle": "BW-IP: DSO Planning - Time dependent navigation attributes", "RefUrl": "/notes/1811231 "}, {"RefNumber": "1718885", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "InfoObject: NO2 aggregation for key figures", "RefUrl": "/notes/1718885 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "731", "To": "731", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}