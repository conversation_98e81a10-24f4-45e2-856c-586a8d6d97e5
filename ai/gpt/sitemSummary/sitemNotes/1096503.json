{"Request": {"Number": "1096503", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 349, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000006515252017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001096503?language=E&token=65BC755C698932DDC5886C22766B3EAE"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001096503", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001096503/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1096503"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.09.2007"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PART-ISHMED"}, "SAPComponentKeyText": {"_label": "Component", "value": "Clinical System i.s.h.med"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Partner solutions", "value": "XX-PART", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Clinical System i.s.h.med", "value": "XX-PART-ISHMED", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART-ISHMED*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1096503 - Technical Adaptation"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This Note contains technical adaptations.<br />You do not need to implement this Note urgently unless it is a prerequisite for another note (follow-up note).</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>This problem is caused by a program error.<br />The following Notes are prequisite to this Note.</p> <UL><LI>1080577</LI></UL> <UL><LI>1075750</LI></UL> <UL><LI>1071848</LI></UL> <UL><LI>1085127</LI></UL> <UL><LI>1085152</LI></UL><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>We recommend that you only implement this Note if required (follow-up note).&#x00A0;&#x00A0;Otherwise, these technical adaptations are automatically available once you implement SAP ECC Industry Extension Healthcare 6.0 Support Package 12.<br /><br />Before you implement this Note, you must first make the following manual changes:<br /></p> <b>Enhance the structure RN1_CONN_HNDLCONFIG_HASH:</b><br /> <UL><LI>Call transaction SE11.</LI></UL> <UL><LI>Enter the value RN1_CONN_HNDLCONFIG_HASH in the \"Data Type\" input field, and choose \"Change\".</LI></UL> <UL><LI>Enter the component name BUSINESSPARTNER and the component type N1CORDTRTGP in the first free row.</LI></UL> <UL><LI>Save and activate the structure.</LI></UL> <p></p> <b>Enhance the table type ISHMED_T_CONN_HNDLCONFIG_HASH:</b><br /> <UL><LI>Call transaction SE11.</LI></UL> <UL><LI>Enter the value ISHMED_T_CONN_HNDLCONFIG_HASH in the \"Data Type\" input field, and choose \"Change\".</LI></UL> <UL><LI>Got to the \"Key\" tab page.</LI></UL> <UL><LI>Enter the further key \"BUSINESSPARTNER\" under \"Key Components\".</LI></UL> <UL><LI>Save and activate the table type.</LI></UL> <p><br />See the source code corrections</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-PART-ISHMED-ORD (Service Management i.s.h.med)"}, {"Key": "Responsible                                                                                         ", "Value": "********"}, {"Key": "Processor                                                                                           ", "Value": "********"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001096503/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001096503/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001096503/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001096503/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001096503/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001096503/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001096503/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001096503/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001096503/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1100019", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical Adaptation", "RefUrl": "/notes/1100019"}, {"RefNumber": "1085152", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical Adaptation", "RefUrl": "/notes/1085152"}, {"RefNumber": "1085127", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clinical orders: Configuration of clinical appl. component", "RefUrl": "/notes/1085127"}, {"RefNumber": "1080577", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical Adaptation", "RefUrl": "/notes/1080577"}, {"RefNumber": "1075750", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical adaptations", "RefUrl": "/notes/1075750"}, {"RefNumber": "1071848", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical Adaptation", "RefUrl": "/notes/1071848"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1100019", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical Adaptation", "RefUrl": "/notes/1100019 "}, {"RefNumber": "1085152", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical Adaptation", "RefUrl": "/notes/1085152 "}, {"RefNumber": "1085127", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clinical orders: Configuration of clinical appl. component", "RefUrl": "/notes/1085127 "}, {"RefNumber": "1075750", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical adaptations", "RefUrl": "/notes/1075750 "}, {"RefNumber": "1080577", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical Adaptation", "RefUrl": "/notes/1080577 "}, {"RefNumber": "1071848", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical Adaptation", "RefUrl": "/notes/1071848 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-H", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "602", "To": "602", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "IS-H 600", "SupportPackage": "SAPK-60012INISH", "URL": "/supportpackage/SAPK-60012INISH"}, {"SoftwareComponentVersion": "IS-H 602", "SupportPackage": "SAPK-60201INISH", "URL": "/supportpackage/SAPK-60201INISH"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 1, "URL": "/corrins/0001096503/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 3, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "897533 ", "URL": "/notes/897533 ", "Title": "Connectivity: Sending clinical order", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1024444 ", "URL": "/notes/1024444 ", "Title": "IS-H: create patient with appointment and visit not possible", "Component": "IS-H-PM-OCM"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1085152 ", "URL": "/notes/1085152 ", "Title": "Technical Adaptation", "Component": "XX-PART-ISHMED"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}