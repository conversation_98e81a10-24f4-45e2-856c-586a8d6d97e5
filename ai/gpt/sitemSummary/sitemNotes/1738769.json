{"Request": {"Number": "1738769", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 354, "Error": {}, "SAPNote": {"_type": "00200720410000000235", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001738769?language=E&token=B26777DA86E6FDFBE8B3B2D060CFCF13"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001738769", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001738769/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1738769"}, "Type": {"_label": "Type", "value": "SAP Knowledge Base Article"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Problem"}, "Priority": {"_label": "Priority", "value": "Normal"}, "Status": {"_label": "Release Status", "value": "Released to Customer"}, "ReleasedOn": {"_label": "Released On", "value": "08.12.2020"}, "SAPComponentKey": {"_label": "Component", "value": "BC-JAS-COR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Enterprise Runtime, Core J2EE Framework"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "1738769 - core/AbstractManager java.lang.NoClassDefFoundError"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>After applying the latest SAP_JTECHF or SAP_JTECHS&#160;patch on your system you are unable to access applications like Webnynpro console or NWA. When checking the default trace files the following can be seen:</p>\r\n<p><em>Application error occurred during request processing. Details:</em><br /><em>&#160; java.lang.NoClassDefFoundError:</em><br /><em>com/sap/tc/webdynpro/services/sal/core/AbstractManager&#160;:&#160;cannot</em><br /><em>initialize class because prior initialization attempt failed</em><br /><em>Exception id: [0000000XXXXXXXX0000XXXXX0000XXXX000000XXXXX00]</em></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Environment\">Environment</h3>\r\n<ul>\r\n<li>SAP NetWeaver 7.0</li>\r\n<li>SAP enhancement package 1 for SAP NetWeaver 7.0</li>\r\n<li>SAP enhancement package 2 for SAP NetWeaver 7.0</li>\r\n<li>SAP enhancement package 3 for SAP NetWeaver 7.0</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Reproducing the Issue\">Reproducing the Issue</h3>\r\n<p>Attempt to connect to;</p>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"http://&lt;host&gt;:&lt;port&gt;/webdynpro/dispatcher/sap.com/tc&#126;wd&#126;tools/WebDynproConsole\">http://&lt;host&gt;:&lt;port&gt;/webdynpro/dispatcher/sap.com/tc&#126;wd&#126;tools/WebDynproConsole</a></li>\r\n</ul>\r\n<p>or</p>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"http://&lt;host&gt;:&lt;port&gt;/nwa\">http://&lt;host&gt;:&lt;port&gt;/nwa</a></li>\r\n</ul>\r\n<p>You receive an error like;</p>\r\n<p>Error message:<br />Application error occurred during request processing. Details:<br />&#160; java.lang.NoClassDefFoundError:<br />com/sap/tc/webdynpro/services/sal/core/AbstractManager : cannot<br />initialize class because prior initialization attempt failed<br />Exception id: [<em>0000000XXXXXXXX0000XXXXX0000XXXX000000XXXXX00]</em></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Cause\">Cause</h3>\r\n<p>This issue is caused by components&#160;SAP_JTECHS and SAP_JTECHF&#160;being at inconsistent levels</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Resolution\">Resolution</h3>\r\n<ol start=\"1\">\r\n<li>Check your ComponentInfo via <a target=\"_blank\" href=\"http://&lt;host&gt;:&lt;port&gt;/sap/monitoring/ComponentInfo\">http://&lt;host&gt;:&lt;port&gt;/sap/monitoring/ComponentInfo</a></li>\r\n<li>Ensure components&#160;SAP_JTECHS and&#160;SAP_JTECHF are at a consistent release/sp level and apply the latest patch available</li>\r\n<li>If not, deploy the component and/or patch as per Note <a target=\"_blank\" href=\"http://service.sap.com/&#126;form/handler?_APP=01100107900000000342&amp;_EVENT=DISPL_TXT&amp;_NNUM=1395627\">#1395627 - Import a patch for WebDynproRuntime Java<sup><img class=\"img-responsive\" alt=\"''\" height=\"7\" src=\"https://wiki.wdf.sap.corp/wiki/images/icons/linkext7.gif\" width=\"7\" /></sup></a></li>\r\n<li>Apply the latest patch of SAP J2EE ENGINE CORE SCA</li>\r\n</ol>\r\n<h3 data-toc-skip class=\"section\" id=\"Keywords\">Keywords</h3>\r\n<p>java.lang.NoClassDefFoundError, com/sap/tc/webdynpro/services/sal/core/AbstractManager,</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-JAS-DPL-TLS (Engine Deploy Tool - Please use BC-JAS-DPL)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I004237)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I004237)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001738769/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001738769/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001738769/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001738769/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001738769/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001738769/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001738769/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001738769/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001738769/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1636252", "RefComponent": "BC-CST", "RefTitle": "Installing a 7.20 kernel in SAP Web AS 7.00/7.01/7.10/7.11", "RefUrl": "/notes/1636252 "}]}}, "Validity": {"_label": "Products", "_columnNames": {"Product": "Products"}, "Items": [{"Product": "SAP NetWeaver 7.0"}, {"Product": "SAP enhancement package 1 for SAP NetWeaver 7.0"}, {"Product": "SAP enhancement package 2 for SAP NetWeaver 7.0"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": [{"Key": "URF_QA1", "Title": "This article is inaccurate", "Description": ""}, {"Key": "URF_QA2", "Title": "This article is confusing", "Description": ""}, {"Key": "URF_QA3", "Title": "This article doesn't solve my problem", "Description": ""}, {"Key": "URF_QA4", "Title": "I do not like the feature described", "Description": ""}]}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}