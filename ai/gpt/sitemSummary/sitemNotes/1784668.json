{"Request": {"Number": "1784668", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 265, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017549832017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001784668?language=E&token=72626EE5280E0EE5B5145990B5790C4D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001784668", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001784668/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1784668"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 13}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.07.2013"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-LVC"}, "SAPComponentKeyText": {"_label": "Component", "value": "liveCache"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "liveCache", "value": "BC-DB-LVC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-LVC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1784668 - Problems with SCM after upgrade to 7.20 SAP kernel"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>After upgrading from 7.00 to 7.20 SAP kernel you got errors from the demand planning in SCM.<br />You load a selection in interactive demand planning (transaction /SAPAPO/SDP94) and receive error messages like the following:</p> <UL><LI>Time bucket does not exist</LI></UL> <UL><LI>Error for COM routine using application program (return code 40.010)</LI></UL> <UL><LI>Error reading data - Planning book cannot be processed further</LI></UL> <p><br />You run the report /SAPAPO/TS_LCM_CONS_CHECK for any planning area and receive error message \"No valid storage buckets profile exists\".<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>ASCII IO FOR ASCII, /SAPAPO/TS_LCM_CONS_CHECK, SAPTS_READ_TG</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><br />This issue can happen in the following combinations:<br /><br />SCM 5.0:</p> <UL><UL><LI>SAP liveCache &lt; *********</LI></UL></UL> <UL><UL><LI>SAP MaxDB SQLDBC client 7.7 ff.</LI></UL></UL> <p>SCM 5.1:</p> <UL><UL><LI>SAP liveCache &lt; *********</LI></UL></UL> <UL><UL><LI>SAP MaxDB SQLDBC client 7.7 ff.</LI></UL></UL> <p>SCM 7.0:</p> <UL><UL><LI>SAP liveCache &lt; *********</LI></UL></UL> <UL><UL><LI>SAP MaxDB SQLDBC client 7.7 ff.</LI></UL></UL> <p><br />To verify that the issue is caused by the SAP liveCache kernel, perform the following steps:</p> <OL>1. Use transaction SE38 to run report /SAPAPO/TS_LCM_CONS_CHECK.</OL> <OL>2. Pick any planning area.</OL> <OL>3. Turn on the debugger and set a breakpoint at function module \"/SAPAPO/OM_TS_TGRID_READ\".</OL> <OL>4. Step forward until the end of the function module.</OL> <OL>5. Display the content of EV_TGRID_EXISTS and table ET_TGRID.</OL> <p><br />If EV_TGRID_EXISTS is a character like '#' instead of 'X' and there are entries in table ET_TGRID, the issue is caused by the kernel.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>To solve the problems with SAP kernel &gt;= 7.20 upgrade your SAP liveCache (PTS 1174680). Use note 998966: Importing liveCache Version.<br /><br />SCM 5.0:</p> <UL><UL><LI>SAP liveCache &gt;= *********</LI></UL></UL> <p>SCM 5.1:</p> <UL><UL><LI>SAP liveCache &gt;= *********</LI></UL></UL> <p>SCM 7.0:</p> <UL><UL><LI>SAP liveCache &gt;= *********</LI></UL></UL> <p><br /><B><NOBR><B>Workaround</B></NOBR></B><br /><br />Use one of the following workarounds for all SCM versions:</p> <UL><LI>Install a SAP MaxDB SQLDBC client &gt; ********* (note 649814) and set the profile parameter dbs/ada/variable_output = 0 (using transaction RZ10). If possible prefer this workaround.</LI></UL> <UL><LI>Go back to 7.00 SAP kernel.</LI></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SCM-APO-FCS (Demand Planning)"}, {"Key": "Other Components", "Value": "BC-DB-LCA (liveCache Applications)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON> (D025086)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D024848)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001784668/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001784668/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001784668/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001784668/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001784668/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001784668/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001784668/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001784668/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001784668/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "998966", "RefComponent": "BC-DB-LVC", "RefTitle": "Implementation of SAP liveCache Version 7.7", "RefUrl": "/notes/998966"}, {"RefNumber": "649814", "RefComponent": "BC-DB-SDB", "RefTitle": "Installation/update of SAP MaxDB/liveCache client software", "RefUrl": "/notes/649814"}, {"RefNumber": "1633731", "RefComponent": "BC-CST", "RefTitle": "Usage of the 7.20 Downward-Compatible Kernel", "RefUrl": "/notes/1633731"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2658492", "RefComponent": "SCM-APO-FCS-BF", "RefTitle": "Error /SAPAPO/TSM235 in Report /SAPAPO/TS_LCM_CONS_CHECK", "RefUrl": "/notes/2658492 "}, {"RefNumber": "1633731", "RefComponent": "BC-CST", "RefTitle": "Usage of the 7.20 Downward-Compatible Kernel", "RefUrl": "/notes/1633731 "}, {"RefNumber": "649814", "RefComponent": "BC-DB-SDB", "RefTitle": "Installation/update of SAP MaxDB/liveCache client software", "RefUrl": "/notes/649814 "}, {"RefNumber": "998966", "RefComponent": "BC-DB-LVC", "RefTitle": "Implementation of SAP liveCache Version 7.7", "RefUrl": "/notes/998966 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SCM", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SCM", "From": "510", "To": "510", "Subsequent": ""}, {"SoftwareComponent": "SCM", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}