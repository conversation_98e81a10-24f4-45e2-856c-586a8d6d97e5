{"Request": {"Number": "1479893", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 475, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000008755722017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001479893?language=E&token=ACA631345D1FCAD9C72BCB956D59E6CC"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001479893", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001479893/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1479893"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "2010/08/04"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-OT-DBIF"}, "SAPComponentKeyText": {"_label": "Component", "value": "Interface to Database"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Explorer", "value": "BW-BEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "OLAP Technology", "value": "BW-BEX-OT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Interface to Database", "value": "BW-BEX-OT-DBIF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT-DBIF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1479893 - BW LISTCUBE improvements"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p><br />Transaction LISTCUBE has experienced some improvements as a central tool for analyzing raw data and the problems associated with it.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p><br />BW, LISTCUBE, improvements, analysis by experts, error, raw data<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><br /></p> <UL><LI>The LISTCUBE report</LI></UL> <p><br />The initial selection screen of transaction LISTCUBE allows the specification of a program name in BW 7.X.&#x00A0;&#x00A0;If something is specified here, a program of this name is generated.&#x00A0;&#x00A0;You can then start it using transaction SE38.&#x00A0;&#x00A0;The advantage of such a program is that variants can be created there.&#x00A0;&#x00A0;That could also have been done before but, because the names were always generated, stored variants would never have been found again.<br /><br />If you still have problems with the data of an InfoProvider, specifying a LISTCUBE report (the established naming convention is Z_LC_&lt;provider name&gt;) and a variant that was created there (for example, SAP_BAD for the problem case and SAP_GOOD for the correct case) in the message that belongs to the problem is helpful.<br /><br /></p> <UL><LI>Selection dialog box in the case of too many fields</LI></UL> <p><br />If the InfoProvider contains numerous fields, some fields must be selected because otherwise the generated selection screen is too large  The selection dialog box currently always has to be refilled.&#x00A0;&#x00A0;Two new icons (above right) allow you to save the currently selected fields in a text file on your PC or to import changed field lists from the PC.<br /><br />Therefore, it is a good idea to select all fields and write them to a file.&#x00A0;&#x00A0;Then open the file using your text editor and remove the fields that you do not want to be selected and save them under a new name.  Afterwards, remove all selections in the selection dialog box and import the new file into the selection dialog box.<br /><br />During the import, the fields that are referred to in the file are also selected for the existing fields.<br /><br />Only the first part of the line is of interest during the import.  Everything after \"---\" is only for information.&#x00A0;&#x00A0;As of the first empty character, the rest is ignored.&#x00A0;&#x00A0;You can also use the usual wildcard character in ABAP.&#x00A0;&#x00A0;Therefore, \"0CAL*\" selects all the calendar characteristics (0CALMONTH, 0CALWEEK, 0CALDAY, and so on) that can be selected in the provider.<br /><br />Use the full potential of your text editor here.<br /></p> <UL><LI>Number of selected fields</LI></UL> <p><br />Furthermore, the title line of the selection dialog box contains information about how many fields are already selected, how many can be selected (the maximum is always 72), and how many fields in total are available.<br /><br /></p> <UL><LI>Preventing the writing of data to files or tables</LI></UL> <p><br />Transaction LISTCUBE provides the option of writing data to a file or a table directly.&#x00A0;&#x00A0;The files always contain the suffix CSV and, for the tables, the field names of the target table have to be selected correctly.&#x00A0;&#x00A0;Here, there have been repeated complaints concerning missing safety measures.<br /><br />Since LISTCUBE is an analysis transaction and the writing of data extracts is the function of the open hub, there is now the option to completely prevent the writing of data to files and tables&#x00A0;&#x00A0;In the LISTCUBE report (see above), this is not possible and if the data is displayed on the ALV grid, the option to export data as Excel or ASCII files exists.&#x00A0;&#x00A0;Therefore, there is no reason to retain the \"file table\" transfer.<br /><br />If you no longer want this, set the RSADMIN parameter BW_LISTCUBE_FTW_DISALLOW to any value (only the existence of the parameter is tested, not the value).&#x00A0;&#x00A0;The best way is with the established program SAP_RSADMIN_MAINTAIN.<br /><br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p></p> <UL><LI>SAP NetWeaver BW 7.00<br /><br />Import Support Package 25 for SAP NetWeaver BW 7.00 (SAPKW70025) into your BW system. The Support Package is available when <B>Note 1468668</B> \"SAPBINews NW BI 7.0 ABAP SP25\", which describes this Support Package in more detail, is released for customers.</LI></UL> <UL><LI>SAP NetWeaver BW 7.01 (SAP NW BW 7.0 Enhancement Package 1)<br /><br />Import Support Package 08 for SAP NetWeaver BW 7.01 (SAPKW70108) into your BW system. The Support Package is available when <B>Note 1471463</B> \"SAPBINews NW7.01 BW ABAP SP08\", which describes this Support Package in more detail, is released for customers.</LI></UL> <UL><LI>SAP NetWeaver BW 7.02 (SAP NW BW 7.0 Enhancement Package 2)<br /><br />Import Support Package 05 for SAP NetWeaver BW 7.02 (SAPKW70205) into your BW system. The Support Package is available when <B>Note 1450990</B> \"SAPBINews NW BI 7.02 ABAP SP05\", which describes this Support Package in more detail, is released for customers.</LI></UL> <UL><LI>SAP NetWeaver BW 7.11<br /><br />Import Support Package 05 for SAP NetWeaver BW 7.11 (SAPKW71105) into your BW system. The Support Package is available when <B>Note 1392433</B> \"SAPBINews NW BI 7.11 ABAP SP05\", which describes this Support Package in more detail, is released for customers.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br />In urgent cases, you can implement the correction instructions as an advance correction.<br /><br /><B>You must first read Note 875986, which provides information about transaction SNOTE.</B><br /><br />To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note contains the words \"Preliminary version\".<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D032185)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D032185)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001479893/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001479893/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001479893/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001479893/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001479893/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001479893/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001479893/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001479893/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001479893/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1793847", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW LISTCUBE reports invalid file filter", "RefUrl": "/notes/1793847"}, {"RefNumber": "1718378", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Directory Traversal in Query Snapshot", "RefUrl": "/notes/1718378"}, {"RefNumber": "1591837", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "How to analyze query results", "RefUrl": "/notes/1591837"}, {"RefNumber": "1575722", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Directory traversal in BW", "RefUrl": "/notes/1575722"}, {"RefNumber": "1548125", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Interesting Facts about Non-Cumulatives", "RefUrl": "/notes/1548125"}, {"RefNumber": "1471463", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.01 ABAP SP 08", "RefUrl": "/notes/1471463"}, {"RefNumber": "1468668", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.00 ABAP SP 25", "RefUrl": "/notes/1468668"}, {"RefNumber": "1450990", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.02 ABAP SP 05", "RefUrl": "/notes/1450990"}, {"RefNumber": "1392433", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.11 ABAP SP 05", "RefUrl": "/notes/1392433"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2471394", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Incorrect/Unexpected Values displayed in Transaction LISTCUBE", "RefUrl": "/notes/2471394 "}, {"RefNumber": "1548125", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Interesting Facts about Non-Cumulatives", "RefUrl": "/notes/1548125 "}, {"RefNumber": "1793847", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW LISTCUBE reports invalid file filter", "RefUrl": "/notes/1793847 "}, {"RefNumber": "1591837", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "How to analyze query results", "RefUrl": "/notes/1591837 "}, {"RefNumber": "1471463", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.01 ABAP SP 08", "RefUrl": "/notes/1471463 "}, {"RefNumber": "1468668", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.00 ABAP SP 25", "RefUrl": "/notes/1468668 "}, {"RefNumber": "1450990", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.02 ABAP SP 05", "RefUrl": "/notes/1450990 "}, {"RefNumber": "1392433", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.11 ABAP SP 05", "RefUrl": "/notes/1392433 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "711", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "701", "To": "701", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "711", "To": "711", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW 700", "SupportPackage": "SAPKW70025", "URL": "/supportpackage/SAPKW70025"}, {"SoftwareComponentVersion": "SAP_BW 700", "SupportPackage": "SAPKW70025", "URL": "/supportpackage/SAPKW70025"}, {"SoftwareComponentVersion": "SAP_BW_VIRTUAL_COMP 701", "SupportPackage": "SAPK-70118INVCBWTECH", "URL": "/supportpackage/SAPK-70118INVCBWTECH"}, {"SoftwareComponentVersion": "SAP_BW 701", "SupportPackage": "SAPKW70108", "URL": "/supportpackage/SAPKW70108"}, {"SoftwareComponentVersion": "SAP_BW 701", "SupportPackage": "SAPKW70108", "URL": "/supportpackage/SAPKW70108"}, {"SoftwareComponentVersion": "SAP_BW 702", "SupportPackage": "SAPKW70205", "URL": "/supportpackage/SAPKW70205"}, {"SoftwareComponentVersion": "SAP_BW 702", "SupportPackage": "SAPKW70205", "URL": "/supportpackage/SAPKW70205"}, {"SoftwareComponentVersion": "SAP_BW 711", "SupportPackage": "SAPKW71106", "URL": "/supportpackage/SAPKW71106"}, {"SoftwareComponentVersion": "SAP_BW 730", "SupportPackage": "SAPKW73001", "URL": "/supportpackage/SAPKW73001"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BW", "NumberOfCorrin": 2, "URL": "/corrins/0001479893/30"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "701", "Number": "1261069 ", "URL": "/notes/1261069 ", "Title": "Termination in LISTCUBE characteristic selection dialog box", "Component": "BW-BEX-OT-DBIF"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}