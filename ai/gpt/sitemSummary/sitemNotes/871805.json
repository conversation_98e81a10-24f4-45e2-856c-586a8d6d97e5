{"Request": {"Number": "871805", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 241, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015938102017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000871805?language=E&token=78556BFA37F412838D5CD2459647EEC7"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000871805", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000871805/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "871805"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "21.03.2007"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-DFS"}, "SAPComponentKeyText": {"_label": "Component", "value": "BI Content Defense & Public Security"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BI Content Defense & Public Security", "value": "BW-BCT-DFS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-DFS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "871805 - Customizing - Enhancement of LIS-Extract-Structures"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Customizing - Enhancement of LIS-Extract-Structures<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>DFPS<br />customizing<br />LIS<br />LIS Extract Structures<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>To be able to use the DFPS-specific enhancements of LIS DataSources you need to carry out the following action items.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>In addition to the standard activation and maintenance of LIS-DataSources you need to carry out the following steps:<br /><br />1. Activiation of additional fields<br />In transaction LBWE the following DFPS-specific fields have to be added to the extract structures of the corresponding DataSources:<br /><br /> 1.1. Extractor 2LIS_08TRTLP<br /> <br /> RIC &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CON_ID&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MPO_MATNR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;OBJID_COM<br /> OBJIDPROV&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; OBJIDREC&#x00A0;&#x00A0;&#x00A0;&#x00A0;OPERATION_ID&#x00A0;&#x00A0;&#x00A0;&#x00A0;PROVIDER_MAT<br /> PROVIDER_PER&#x00A0;&#x00A0;&#x00A0;&#x00A0;LGORTZ<br /> <br /> 1.2. Extractor 2LIS_02_ITM<br /> <br /> ADVCODE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BANFN&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; BNFPO&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; CON_ID<br /> MPO_MATNR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;OBJID&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; OBJID_COM&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; OPERATION_ID<br /> PLANNING_GUID&#x00A0;&#x00A0; PRIO_REQ&#x00A0;&#x00A0;&#x00A0;&#x00A0;RELOC_SEQ_ID&#x00A0;&#x00A0;&#x00A0;&#x00A0;PROVIDER_MAT<br /> PRIO_URG&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RIC&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RELOC_ID&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PROVIDER_PER<br /> <br /> 1.3. Extractor 2LIS_03_BF <br /><br /> BANFN&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BNFPO&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; CON_ID&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MPO_MATNR<br /> OBJID&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;OBJID_COM&#x00A0;&#x00A0; OPERATION_ID&#x00A0;&#x00A0;&#x00A0;&#x00A0;PLANNING_GUID<br /> PROVIDER_MAT&#x00A0;&#x00A0;&#x00A0;&#x00A0;RIC&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RELOC_ID&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RELOC_SEQ_ID <br /> PROVIDER_PER<br /><br /> 1.4. Extractor 2LIS_17_I3HDR <br /><br /> ARM_SETUP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CALL_SIGN&#x00A0;&#x00A0; CHNGBY&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CHNGDAT<br /> CREABY&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CREADAT&#x00A0;&#x00A0;&#x00A0;&#x00A0;EXTERNAL_ID&#x00A0;&#x00A0;&#x00A0;&#x00A0; FL_DURATION_UNIT<br /> FLIGHT_DURATION FORCE_CNT&#x00A0;&#x00A0; ISFLIGHT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;LATEST_TAKE_OFF<br /> KRYPTOSTATUS&#x00A0;&#x00A0;&#x00A0;&#x00A0;LANDING_LOC LANDING_DATE&#x00A0;&#x00A0;&#x00A0;&#x00A0;LANDING_TNZONE <br /> LOGSYS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MISSION&#x00A0;&#x00A0;&#x00A0;&#x00A0;LAUNCHING_PAD&#x00A0;&#x00A0; MODEL_ID<br /> NUM_PERSONS&#x00A0;&#x00A0;&#x00A0;&#x00A0;OBJNR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; STEXT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; OPERATION_ID_GUID<br /> OPERATION_CNT&#x00A0;&#x00A0; START_DATE&#x00A0;&#x00A0;TARGET_LOC&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;OPERATN_ID_OBJID  <br /> F_OBJNR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ZUL_TNZONE&#x00A0;&#x00A0;START_TNZONE&#x00A0;&#x00A0;&#x00A0;&#x00A0;OPERATN_ID_OTYPE<br /> START_TNZONE&#x00A0;&#x00A0;&#x00A0;&#x00A0;TIME_OVER_TARGET&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;OPERATN_ID_PLVAR<br /><br />2. Activation of Business Transaction Events (BTE)<br />In transaction FIBG the BTEs have to be set up properly. Carry out the following steps:<br /><br /> 2.1. Call transaction FIBF.<br /> 2.2. Menu Settings -&gt; Identification -&gt; SAP-Applications<br /> 2.3. Check the Checkbox for application BW.<br /> 2.4. Call transaction FIBF again.<br /> 2.5. Menu Settings -&gt; P/S-Function Modules -&gt; #of an SAP-Appl.<br /> 2.6. Add /ISDFPS/BI_LIS_APPEND_DFPS to event BW204010.<br /><br />3. Activation of the necessary sourcecode<br />In function module ISDFPS/BI_LIS_APPEND_DFPS the commented code has to be decommented. Call transaction SE37 and open the function module for changes. Decomment the code.<br /><br />4. Setup the mapping between DataSources and InfoSource<br />In the Business Information Warehouse the LIS-DataSource have to be mapped to the corresponding InfoSources. Use the following connections between DataSources and InfoSources:<br /><br />DataSource&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;InfoSource<br />--------------------------------<br />2LIS_08TRTLP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&lt;--&gt;&#x00A0;&#x00A0;0DF_IS_DFS_26 <br />2LIS_02_ITM&#x00A0;&#x00A0;&#x00A0;&#x00A0; &lt;--&gt;&#x00A0;&#x00A0;0DF_IS_DFS_38 <br />2LIS_03_BX&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&lt;--&gt;&#x00A0;&#x00A0;0DF_IS_DFS_44 <br />2LIS_03_BF&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&lt;--&gt;&#x00A0;&#x00A0;0DF_IS_DFS_40 <br />2LIS_17_I3HDR&#x00A0;&#x00A0; &lt;--&gt;&#x00A0;&#x00A0;0DF_IS_DFS_41 <br />2LIS_17_13OPER&#x00A0;&#x00A0;&lt;--&gt;&#x00A0;&#x00A0;0DF_IS_DFS_42 <br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D026032)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D033934)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000871805/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000871805/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000871805/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000871805/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000871805/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000871805/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000871805/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000871805/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000871805/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "871640", "RefComponent": "BW-BCT-DFS", "RefTitle": "Collection of Customizing-Notes for DFPS-BCT 7.0.2", "RefUrl": "/notes/871640"}, {"RefNumber": "1731186", "RefComponent": "BW-BCT-DFS-MM", "RefTitle": "Duplicate field symbol in FUGR /ISDFPS/BI_EXTRACTORS", "RefUrl": "/notes/1731186"}, {"RefNumber": "1466017", "RefComponent": "BW-BCT-DFS-MM", "RefTitle": "Extractor 2LIS_03_BF creates the same records several times", "RefUrl": "/notes/1466017"}, {"RefNumber": "1359522", "RefComponent": "BW-BCT-DFS", "RefTitle": "Mapping of fields in DFPS appends to LIS DataSources", "RefUrl": "/notes/1359522"}, {"RefNumber": "1171353", "RefComponent": "PM-IS-LIS", "RefTitle": "PM order statuses are ignored by LIS-BI extraction", "RefUrl": "/notes/1171353"}, {"RefNumber": "1145225", "RefComponent": "IS-DFS-PM", "RefTitle": "Error during extraction 2LIS_17_I3HDR", "RefUrl": "/notes/1145225"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2502571", "RefComponent": "BW-BCT-DFS-MM", "RefTitle": "/ISDFPS/BI_2LIS_08TRTLP extractor, Dump \"GETWA_NOT_ASSIGNED\"", "RefUrl": "/notes/2502571 "}, {"RefNumber": "586163", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Composite SAP Note for SAP ERP Inventory Management in SAP BW", "RefUrl": "/notes/586163 "}, {"RefNumber": "1731186", "RefComponent": "BW-BCT-DFS-MM", "RefTitle": "Duplicate field symbol in FUGR /ISDFPS/BI_EXTRACTORS", "RefUrl": "/notes/1731186 "}, {"RefNumber": "1466017", "RefComponent": "BW-BCT-DFS-MM", "RefTitle": "Extractor 2LIS_03_BF creates the same records several times", "RefUrl": "/notes/1466017 "}, {"RefNumber": "1359522", "RefComponent": "BW-BCT-DFS", "RefTitle": "Mapping of fields in DFPS appends to LIS DataSources", "RefUrl": "/notes/1359522 "}, {"RefNumber": "1171353", "RefComponent": "PM-IS-LIS", "RefTitle": "PM order statuses are ignored by LIS-BI extraction", "RefUrl": "/notes/1171353 "}, {"RefNumber": "1145225", "RefComponent": "IS-DFS-PM", "RefTitle": "Error during extraction 2LIS_17_I3HDR", "RefUrl": "/notes/1145225 "}, {"RefNumber": "871640", "RefComponent": "BW-BCT-DFS", "RefTitle": "Collection of Customizing-Notes for DFPS-BCT 7.0.2", "RefUrl": "/notes/871640 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BI_CONT", "From": "703", "To": "703", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}