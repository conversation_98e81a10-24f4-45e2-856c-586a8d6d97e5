{"Request": {"Number": "504875", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 519, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015189042017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000504875?language=E&token=291F2F00E57310521D822ABDE2D5E884"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000504875", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000504875/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "504875"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Performance"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.11.2022"}, "SAPComponentKey": {"_label": "Component", "value": "BC-SRV-NUM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Number Range Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basis Services/Communication Interfaces", "value": "BC-SRV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SRV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Number Range Management", "value": "BC-SRV-NUM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SRV-NUM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "504875 - Buffering of number ranges"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note contains information about performance problems that occur with number ranges.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Performance number range object SNUM SNRO buffering main memory number range NRIV</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The wrong buffering type is set.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Main memory buffering</strong></p>\r\n<p>You may only set the main memory buffering if no legal requirement applies to the number assignment. You can display the buffer contents of the main memory using transaction SM56.<br /><br />Only use this for financial accounting documents after consulting the user department.<br /><br />Advantages:<br />- A lock is temporarily set on the NRIV table when a new buffer is requested. (see Note 920234).<br />- Avoiding performance problems<br /><br />Disadvantages:<br />- Gaps may still exist<br />- Gaps cannot be documented<br />- The numbers are not assigned chronologically.</p>\r\n<p><br /> <strong>Local buffering with Work Process ID (as of Release 4.0B)</strong></p>\r\n<p>Use only if main memory buffering is not possible.<br />You must manually install the 'local file + process ID' technique depending on the release and below certain Support Packages.<br /><br />Advantages:<br />- Since numbers are assigned under a lock, the number assignment does not contain any gaps.<br />- Gaps can be documented using the report RSSNR0A1.<br /><br />Disadvantages:<br />- The numbers are not assigned chronologically.<br />- Gaps can only occur in very rare cases (see Note 175047).<br />- Gaps may occur at the end of a fiscal year if not all numbers in the NRIV_LOKAL table were used.<br /><br />The total number of numbers selected should not be too low. The optimal size is extremely variable and depends on the application process.<br /><br />During batch parallel processing (for example, billing), the advantages of this buffering method are lost as soon as the set of numbers for a batch run has to be loaded from NRIV. Batch jobs that run parallel must wait until the first batch run has been completed before they can be loaded.<br />You can prevent loading during a batch run by having a very large number of buffered numbers. The disadvantage of this is that the number consumption increases greatly. A large number pool is made available to each work process, which may not be used completely.<br /><br />This problem is solved by the parallel buffering (see Note 599157)<br /><br /></p>\r\n<p><strong>Parallel buffering</strong></p>\r\n<p>On the surface, parallel buffering behaves like the enhanced local buffering (see also Note 840901 for exceptions) and has two additional <br /><br />advantages:<br />- There can be parallel background processing without performance problems:<br /><br />- It can be implemented in all customer installations:<br /><br />The local buffering using work process_id could not be implemented in all customer installations due to a restriction of the instance name. Its length could not exceed 17, but SAP allows instance names to have a length of up to 20.</p>\r\n<p><br /> <br /> <strong>Local Buffering</strong></p>\r\n<p>You should no longer use this number assignment logic because it has been replaced by local buffering with a work process ID (as of Release 4.0B).<br /><br /></p>\r\n<p><strong>No buffering</strong></p>\r\n<p>No buffering should only be set if required for legal reasons.<br /><br />Advantages:<br />- The number assignment does not contain any gaps.<br />- The numbers are assigned chronologically.<br /><br />Disadvantages:<br />- Performance problems may occur, since a number is assigned in each case and the table remains locked until the next COMMIT or ROLLBACK.<br />- You cannot request numbers in parallel.<br /><br /><br />Additional notes<br /><br />179224 Document number assignment for unbuffered number ranges<br />599157 Number ranges: new buffering method<br />840901 Parallel buffering and pseudo ascending number assignment</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "SNRO"}, {"Key": "Transaction codes", "Value": "SNUM"}, {"Key": "Transaction codes", "Value": "SM56"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D019862)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I035059)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000504875/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000504875/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000504875/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000504875/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000504875/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000504875/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000504875/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000504875/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000504875/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1524347", "RefComponent": "SD-BIL-IV", "RefTitle": "Buffering of billing documents - invoice creation", "RefUrl": "/notes/1524347"}, {"RefNumber": "1524325", "RefComponent": "SD-BIL-IV", "RefTitle": "Poor performance due to locks on table NRIV - Buffering of billing documents", "RefUrl": "/notes/1524325"}, {"RefNumber": "920234", "RefComponent": "BC-CST-NU", "RefTitle": "Mechanism to fill the number range buffer", "RefUrl": "/notes/920234"}, {"RefNumber": "840901", "RefComponent": "BC-SRV-NUM", "RefTitle": "Parallel buffering and pseudo-ascending number assignment", "RefUrl": "/notes/840901"}, {"RefNumber": "836889", "RefComponent": "FIN-BAC-GL", "RefTitle": "Gaps in document number assignment", "RefUrl": "/notes/836889"}, {"RefNumber": "810757", "RefComponent": "FI-GL-GL-X", "RefTitle": "FAQ FI-GL-GL-X Data Consistency Check Composite Note", "RefUrl": "/notes/810757"}, {"RefNumber": "691916", "RefComponent": "FIN-BAC-GL", "RefTitle": "Performance of GL document number assignment", "RefUrl": "/notes/691916"}, {"RefNumber": "678501", "RefComponent": "BC-SRV-NUM", "RefTitle": "System standstill, locks on NRIV", "RefUrl": "/notes/678501"}, {"RefNumber": "653235", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/653235"}, {"RefNumber": "599157", "RefComponent": "BC-SRV-NUM", "RefTitle": "Number ranges: New buffering method", "RefUrl": "/notes/599157"}, {"RefNumber": "572905", "RefComponent": "BC-SRV-NUM", "RefTitle": "NR: Unbuffered number ranges", "RefUrl": "/notes/572905"}, {"RefNumber": "551750", "RefComponent": "CO-PA", "RefTitle": "TIME_OUT due to block for NRIV after long select on CE4", "RefUrl": "/notes/551750"}, {"RefNumber": "453979", "RefComponent": "CRM-BE-BD", "RefTitle": "Performance CRM Billing - composite SAP note", "RefUrl": "/notes/453979"}, {"RefNumber": "179224", "RefComponent": "BC-SRV-NUM", "RefTitle": "Doc.no.assignment for unbuffered number ranges", "RefUrl": "/notes/179224"}, {"RefNumber": "1522367", "RefComponent": "FI-GL-GL-A", "RefTitle": "Document number gap reason and analysing method", "RefUrl": "/notes/1522367"}, {"RefNumber": "1457522", "RefComponent": "SCM-EWM-PI", "RefTitle": "PI documents: Number range buffering and performance", "RefUrl": "/notes/1457522"}, {"RefNumber": "1445374", "RefComponent": "PSM-FM-PO-EF", "RefTitle": "Number range buffering for earmarked funds (IRW_BELEG)", "RefUrl": "/notes/1445374"}, {"RefNumber": "1398444", "RefComponent": "FI-GL-GL-A", "RefTitle": "Buffering the document number assignment for RF_BELEG", "RefUrl": "/notes/1398444"}, {"RefNumber": "1244898", "RefComponent": "BC-SRV-NUM", "RefTitle": "NR: Reorganization of local buffers for intervals", "RefUrl": "/notes/1244898"}, {"RefNumber": "1239139", "RefComponent": "CRM-BE-BD", "RefTitle": "Buffering of Number Ranges in CRM Billing", "RefUrl": "/notes/1239139"}, {"RefNumber": "1030400", "RefComponent": "BC-SRV-NUM", "RefTitle": "NR: Change of buffering type", "RefUrl": "/notes/1030400"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3200907", "RefComponent": "BC-SRV-NUM", "RefTitle": "Buffering Number Ranges: performance and buffering methods", "RefUrl": "/notes/3200907 "}, {"RefNumber": "2689405", "RefComponent": "XX-SER-MCC", "RefTitle": "FAQ: SAP S/4HANA Performance Best Practices - Collective Note", "RefUrl": "/notes/2689405 "}, {"RefNumber": "2386639", "RefComponent": "FS-BP", "RefTitle": "Business Partner Number Range Skip", "RefUrl": "/notes/2386639 "}, {"RefNumber": "3110617", "RefComponent": "BC-SRV-NUM", "RefTitle": "Analyze the number range scenario with kernel snapshot analyzer", "RefUrl": "/notes/3110617 "}, {"RefNumber": "2955996", "RefComponent": "BC-SRV-NUM", "RefTitle": "Locks in table NRIV_LOKAL", "RefUrl": "/notes/2955996 "}, {"RefNumber": "2921116", "RefComponent": "SLL-LEG-CUS-FTZ", "RefTitle": "KBA: Error message /SAPSLL/CORE_LEG 507 raised when creating daily admission.", "RefUrl": "/notes/2921116 "}, {"RefNumber": "2193462", "RefComponent": "BC-SRV-NUM", "RefTitle": "Message no. NR031 (<PERSON><PERSON><PERSON> is too small, see long text)", "RefUrl": "/notes/2193462 "}, {"RefNumber": "2319594", "RefComponent": "SV-SMG-SUP", "RefTitle": "Why are there missing values in number range for incidents and change documents? - Solution Manager", "RefUrl": "/notes/2319594 "}, {"RefNumber": "1843002", "RefComponent": "BC-SRV-NUM", "RefTitle": "Gaps and jumps in number range assignment", "RefUrl": "/notes/1843002 "}, {"RefNumber": "2565512", "RefComponent": "IS-U-IN-PC", "RefTitle": "INV: Buffering the document number assignment for invoice print documents", "RefUrl": "/notes/2565512 "}, {"RefNumber": "2474833", "RefComponent": "XX-PROJ-FI-CA", "RefTitle": "FI-CA: Enabling number of jobs in mass activities > 999", "RefUrl": "/notes/2474833 "}, {"RefNumber": "2042606", "RefComponent": "XX-PART-IPS", "RefTitle": "IP and DMP numbers not assigned continuously", "RefUrl": "/notes/2042606 "}, {"RefNumber": "1934331", "RefComponent": "PM-WOC-JC", "RefTitle": "Fangschaltung für Belegnummerlücken im Nummernkreisobjekt RF_BELEG im FI", "RefUrl": "/notes/1934331 "}, {"RefNumber": "1522489", "RefComponent": "MM-IV-GF-UPD", "RefTitle": "Error prevention mechanism for database commits that are not intended", "RefUrl": "/notes/1522489 "}, {"RefNumber": "1030400", "RefComponent": "BC-SRV-NUM", "RefTitle": "NR: Change of buffering type", "RefUrl": "/notes/1030400 "}, {"RefNumber": "1239139", "RefComponent": "CRM-BE-BD", "RefTitle": "Buffering of Number Ranges in CRM Billing", "RefUrl": "/notes/1239139 "}, {"RefNumber": "1679514", "RefComponent": "XX-PART-DTM", "RefTitle": "Material, customer, vendor numbers not assigned continuously", "RefUrl": "/notes/1679514 "}, {"RefNumber": "1445374", "RefComponent": "PSM-FM-PO-EF", "RefTitle": "Number range buffering for earmarked funds (IRW_BELEG)", "RefUrl": "/notes/1445374 "}, {"RefNumber": "1522367", "RefComponent": "FI-GL-GL-A", "RefTitle": "Document number gap reason and analysing method", "RefUrl": "/notes/1522367 "}, {"RefNumber": "1398444", "RefComponent": "FI-GL-GL-A", "RefTitle": "Buffering the document number assignment for RF_BELEG", "RefUrl": "/notes/1398444 "}, {"RefNumber": "678501", "RefComponent": "BC-SRV-NUM", "RefTitle": "System standstill, locks on NRIV", "RefUrl": "/notes/678501 "}, {"RefNumber": "1457522", "RefComponent": "SCM-EWM-PI", "RefTitle": "PI documents: Number range buffering and performance", "RefUrl": "/notes/1457522 "}, {"RefNumber": "453979", "RefComponent": "CRM-BE-BD", "RefTitle": "Performance CRM Billing - composite SAP note", "RefUrl": "/notes/453979 "}, {"RefNumber": "599157", "RefComponent": "BC-SRV-NUM", "RefTitle": "Number ranges: New buffering method", "RefUrl": "/notes/599157 "}, {"RefNumber": "179224", "RefComponent": "BC-SRV-NUM", "RefTitle": "Doc.no.assignment for unbuffered number ranges", "RefUrl": "/notes/179224 "}, {"RefNumber": "810757", "RefComponent": "FI-GL-GL-X", "RefTitle": "FAQ FI-GL-GL-X Data Consistency Check Composite Note", "RefUrl": "/notes/810757 "}, {"RefNumber": "836889", "RefComponent": "FIN-BAC-GL", "RefTitle": "Gaps in document number assignment", "RefUrl": "/notes/836889 "}, {"RefNumber": "691916", "RefComponent": "FIN-BAC-GL", "RefTitle": "Performance of GL document number assignment", "RefUrl": "/notes/691916 "}, {"RefNumber": "551750", "RefComponent": "CO-PA", "RefTitle": "TIME_OUT due to block for NRIV after long select on CE4", "RefUrl": "/notes/551750 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "46A", "To": "46D", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "640", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "701", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "711", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "730", "To": "730", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "740", "To": "740", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C49", "URL": "/supportpackage/SAPKB46C49"}, {"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C48", "URL": "/supportpackage/SAPKB46C48"}, {"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C51", "URL": "/supportpackage/SAPKB46C51"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62053", "URL": "/supportpackage/SAPKB62053"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64013", "URL": "/supportpackage/SAPKB64013"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64020", "URL": "/supportpackage/SAPKB64020"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}