{"Request": {"Number": "1247592", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 351, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016591682017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001247592?language=E&token=F45F15CF6008C848D5A51046A2BA560D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001247592", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001247592/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1247592"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.04.2010"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-RO-IS-U"}, "SAPComponentKeyText": {"_label": "Component", "value": "Utilities"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Romania", "value": "XX-CSC-RO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-RO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-Spec. Component", "value": "XX-CSC-RO-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-RO-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Utilities", "value": "XX-CSC-RO-IS-U", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-RO-IS-U*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1247592 - FPCJ,FP05, cash receipt limit"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Due to the Decision No.2.185 from November 30, 2004 new legislative requirement shall be provided. Add the limit (maximum) amount, which is payable through cash journal.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>FP05, FPCJ, /SAPCE/FKRO_EVENT_0240, /SAPCE/FKRO_EVENT_0242, /SAPCE/FKRO_EVENT_6070.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>I. Overview<br />-----------<br /><br />Customizing of Payment limits is available in customizing table /SAPCE/FKRO_PLIM. The limits must be set for each Company Code. Payment Limit Amount is in local currency of Company according Company Code. Example of customizing:<br /><br />1 Cash limits for a payment for a single vendor/entity | 5.000,00<br />2 Sum of all payments for all jurid. entities /one day | 10.000,00<br />3 Limit of partial payments for vendors invoices | 5.000,00<br />4 Other limit     | 5.000,00<br /><br />All checks are processed only for companies (according Company Code) with country = 'RO' (Romania).<br /></p> <b>Payment Lot- transaction FP05</b><br /> <p>1. Check for Cash limits for a payment for a single vendor/entity.</p> <UL><LI>Check is active if one from <U>Selection Values</U> is defined like Business partner number.</LI></UL> <UL><LI>Check is active if the business partner number is not empty.</LI></UL> <p><br />2. Sum of all payments for all jurid. entities /one day.</p> <UL><LI>Check is active if one from <U>Selection Values</U> is defined like Business partner number.</LI></UL> <UL><LI>Check process the items, which have filled the business partner number.</LI></UL> <UL><LI>Check is active only for non residential customers (company or group).</LI></UL> <UL><LI>Check runs on the payment lot only by activities Closing Payment Lot or Posting payment lot.</LI></UL> <UL><LI>Sum total of payments is computed only for documents type 'CH', 'CV' and 'CT'.</LI></UL> <p><br />3 Limit of partial payments for vendors invoices.</p> <UL><LI>Check is irelevant for payment lots.</LI></UL> <p></p> <b>Cash Desk - transaction FPCJ</b><br /> <p>1. Check for Cash limits for a payment for a single vendor/entity.</p> <UL><LI>Check is active if one from <U>Selection Values</U> is defined like Business partner number.</LI></UL> <UL><LI>Check is active if the business partner number is not empty.</LI></UL> <p><br />2. Sum of all payments for all jurid. entities /one day.</p> <UL><LI>Check process the items, which have filled the business partner number.</LI></UL> <UL><LI>Check is active only for non residential customers (company or group).</LI></UL> <UL><LI>Check runs on the payment lot only by activities Closing Payment Lot or Posting payment lot.</LI></UL> <UL><LI>Sum total of payment amounts for check is computed only for documents type 'CH', 'CV' and 'CT'.</LI></UL> <p>3 Limit of partial payments for vendors invoices.</p> <UL><LI>Sum total of payment amounts for check is computed only for documents type 'CH', 'CV' and 'CT'.</LI></UL> <UL><LI>Solution remove open items for clearing, whith unsuccessful check and don't write any error message.</LI></UL> <p> <br /><br />II. Installation<br />-----------------<br />Version 4.6C<br />Download and import transport file FPCJ_PF05_cash_limit_CZUK902178.CAR from attachment.<br /><br />Version &gt; 4.6C<br />Install current AOP add-on SAP CEEISUT.<br /><br /><br />III. Customizing<br />-----------------<br />Activate event modules on the relevant events:<br />/SAPCE/FKRO_EVENT_0061<br />/SAPCE/FKRO_EVENT_0240<br />/SAPCE/FKRO_EVENT_0242<br />/SAPCE/FKRO_EVENT_6021<br />/SAPCE/FKRO_EVENT_6070<br /><br />Fill customizing table /SAPCE/FKRO_PLIM.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I049136)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I049136)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001247592/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001247592/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001247592/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001247592/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001247592/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001247592/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001247592/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001247592/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001247592/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "FPCJ_PF05_cash_limit_CZUK902178.CAR", "FileSize": "61", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000493972008&iv_version=0010&iv_guid=558CF74FE4A6124DBDCCEC3A373F163A"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1482590", "RefComponent": "XX-CSC-RO-IS-U", "RefTitle": "IS-U RO Localization: FPCJ report error", "RefUrl": "/notes/1482590"}, {"RefNumber": "1459646", "RefComponent": "XX-CSC-RO-IS-U", "RefTitle": "IS-U RO Localization: Cash payment limits error", "RefUrl": "/notes/1459646"}, {"RefNumber": "1014660", "RefComponent": "XX-CSC-RO-IS-U", "RefTitle": "Romanian specific functionality for IS-UT 600 (collective)", "RefUrl": "/notes/1014660"}, {"RefNumber": "1012275", "RefComponent": "XX-CSC-RO-IS-U", "RefTitle": "Romanian specific functionality IS-U 4.72 (collective)", "RefUrl": "/notes/1012275"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1012275", "RefComponent": "XX-CSC-RO-IS-U", "RefTitle": "Romanian specific functionality IS-U 4.72 (collective)", "RefUrl": "/notes/1012275 "}, {"RefNumber": "1014660", "RefComponent": "XX-CSC-RO-IS-U", "RefTitle": "Romanian specific functionality for IS-UT 600 (collective)", "RefUrl": "/notes/1014660 "}, {"RefNumber": "1482590", "RefComponent": "XX-CSC-RO-IS-U", "RefTitle": "IS-U RO Localization: FPCJ report error", "RefUrl": "/notes/1482590 "}, {"RefNumber": "1459646", "RefComponent": "XX-CSC-RO-IS-U", "RefTitle": "IS-U RO Localization: Cash payment limits error", "RefUrl": "/notes/1459646 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-U/CCS", "From": "464", "To": "464", "Subsequent": ""}, {"SoftwareComponent": "CEEISUT", "From": "472", "To": "472", "Subsequent": ""}, {"SoftwareComponent": "CEEISUT", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "CEEISUT", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "CEEISUT 472", "SupportPackage": "SAPK-47221INCEEISUT", "URL": "/supportpackage/SAPK-47221INCEEISUT"}, {"SoftwareComponentVersion": "CEEISUT 600", "SupportPackage": "SAPK-60014INCEEISUT", "URL": "/supportpackage/SAPK-60014INCEEISUT"}, {"SoftwareComponentVersion": "CEEISUT 604", "SupportPackage": "SAPK-60402INCEEISUT", "URL": "/supportpackage/SAPK-60402INCEEISUT"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}