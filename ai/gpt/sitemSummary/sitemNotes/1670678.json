{"Request": {"Number": "1670678", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 226, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001670678?language=E&token=BA8F4AD9831028F56228BA341542562E"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001670678", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001670678/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1670678"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 14}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "07.01.2013"}, "SAPComponentKey": {"_label": "Component", "value": "BC-FES-GUI"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP GUI for Windows"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Frontend Services (SAP Note 1322184)", "value": "BC-FES", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-FES*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP GUI for Windows", "value": "BC-FES-GUI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-FES-GUI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1670678 - New features in SAP GUI for Windows 7.30"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Which advantages does SAP GUI for Windows 7.30 offer in comparison with the previous release 7.20?<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SAP GUI, SAPGUI, features, new features, 730</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p></p> <b>New features in SAP GUI for Windows 7.30 Patchlevel 2:</b><br /> <UL><LI>Support for Windows Server 2012 (note 66971)</LI></UL> <UL><LI>Improvements related to keyboard navigation:</LI></UL> <UL><UL><LI>Shortcut equivalents for CTRL+C, CTRL+X, CTRL+V for lefthanded persons (note 1769598)</LI></UL></UL> <UL><UL><LI>Alt+Ctrl+Home/END jumps to first / last elememt of whole screen (note 1769660)</LI></UL></UL> <UL><LI>The Corbu style icons can now also be used for the High Contrast mode in Signature Design. The overall contrast of these icons is better suited for users who need a high contrast.</LI></UL> <b></b><br /> <b>New features in SAP GUI for Windows 7.30 Patchlevel 1:</b><br /> <UL><LI>Caching of server SAP Logon configuration files: When using server based SAP Logon configuration files (see the SAP GUI Administration Guide) a new feature allows to cache the latest version of these files locally. In case of connectivity issues to the server the configuration files will be taken from the cache allowing users to proceed with their work (see SAP note 1426178).</LI></UL> <UL><LI>SAP Logon language as default language on the logon screens: The language defined in SAP Logon can be used as the default language for the texts on the logon screens and is automatically selected in the \"Logon Language\" field. Prerequisite for using this feature is a new SAP Kernel version (see note 1608882). More information can be found in SAP note 1727111.</LI></UL> <UL><LI>Custom Colors for SAP Signature Design: It is possible to define own \"Hue Shifts\" for SAP Signature Design resulting in more color templates to be used for various systems. This feature is only available as of Windows 7 (see SAP note 1740921 for more information).</LI></UL> <b></b><br /> <b>New features in SAP GUI for Windows 7.30 (shipped on 26th of June 2012):</b><br /> <b></b><br /> <b>Support:</b><br /> <UL><LI><B>Longer lifetime</B>: SAP GUI for Windows 7.30 is build with Visual Studio 2010 (which is supported until July 2015) - therefore SAP GUI for Windows 7.30 can be supported longer than release 7.20.</LI></UL> <b></b><br /> <b>Visual Appearance</b><br /> <UL><LI><B>New SAP GUI design \"Corbu\"</B>: The new \"Corbu\" design comes with a reduced color palette and enables a good integration into NWBC and SAP Portal in regards to look and feel.</LI></UL> <UL><LI><B>New icons</B>: In the Corbu design by default a set of new icons is active. This set of icons can also be activated for SAP Signature Design (by means of a switch in the SAP GUI options).</LI></UL> <UL><LI><B>Support for different color schemes in SAP Signature Design: </B>The color scheme can be changed per system and even per client in order to distinguish different systems more easily. SAP offers 5 different color schemes (blue, red, green, purple and gold). Additionally a system administrator can define a default color scheme per system.</LI></UL> <p><br /><B>Usability</B></p> <UL><LI><B>Customizable tab order: U</B>sers can change the tab sequence on application screens according to their needs.</LI></UL> <UL><LI><B>Comments for connection entries in SAP Logon</B>: In SAP Logon users and administrators can define comments per connection entry / shortcut / favorite. This will help users identify the purpose of each entry.</LI></UL> <UL><LI><B>SAP Logon: Navigate to connection entry</B>: In SAP Logon you can now easily navigate from a favorite entry to the related connection entry.</LI></UL> <UL><LI><B>Floating docking containers</B>: Docking containers (like in transaction SE80) can be moved as preferred by the user. The user settings can be persisted to enable users to change the layout of some screens according to their preference.</LI></UL> <UL><LI><B>Usability improvements in ALV</B>: Some operations in ALV (like the block copy) have been improved.</LI></UL> <UL><LI><B>Usability improvements for the security configuration</B>: The security center in SAP GUI options dialog is now larger and can be resized. Additionally users can search and filter for rules. Additionally the dialog that is presented to a user in case a security relevant event is triggered (like the starting of an external program on the PC) has been significantly simplified.</LI></UL> <UL><LI><B>Improved \"Loaded DLLs\" dialog</B>: The \"Loaded DLLs\" dialog is now resizable, you can sort and the order of loaded modules is enhanced.</LI></UL> <UL><LI><B>SAP GUI options dialog supports different font sizes</B>: In release 7.20 the font size settings did not have any effect on the SAP GUI options dialog. Now the SAP GUI font size is also used in SAP GUI options dialog.</LI></UL> <p><br /><B>Security</B></p> <UL><LI><B>Indication of security status: </B>Once a connection to an SAP System has been established SAP GUI shows whether the connection is secure or not (indicated by a lock icon in the SAP GUI status bar).</LI></UL> <p><br /><B>Customization</B></p> <UL><LI><B>Branding:</B> Administrators can add a logo in the title bar of SAP Signature design to allow customer specific branding on SAP GUI screens.</LI></UL> <UL><LI><B>Improved customization of SAP GUI during the installation</B>: The general rule is that HKCU has precedence over HKLM configurations. Therefore the administrator can consistently use HKLM to distribute default values.</LI></UL> <UL><LI><B>Customizable permissions in the SAP GUI options dialog</B>: An administrator can define which sections / values can be changed by the users and which ones cannot.</LI></UL> <UL><LI><B>Hide unwanted SAP Logon view modes</B>: The administrator can hide the buttons for any of the view modes in SAP Logon (Tree View, Explorer View or List View). See SAP Note 1710864 for more information.</LI></UL> <b></b><br /> <p><B>Internationalization Support</B></p> <UL><LI><B>Improved RTL version of Textedit Control</B>: A version of the Textedit Control that is better suited to RTL (right to left) languages like Hebrew and Arabic has been developed.<br /></LI></UL> <b>Installation</b><br /> <UL><LI><B>Reparation of Front-end software installations using NWSAPSetup</B>: \"NWSAPSetup /repair\" checks for discrepancies in files, services, registry-keys, and other artifacts deployed using NWSAPSetup and repairs the installation of SAP front-end components based on the outcome of these checks.<br /></LI></UL> <b>Documentation</b><br /> <UL><LI>SAP GUI for Windows 7.30 is delivered with an SAP GUI administration guide that describes how to configure and administer SAP GUI for Windows.<br /></LI></UL> <b>Removed Features</b><br /> <UL><LI><B>SAP Logon based update</B>: The SAP Logon based update mechanism (when starting SAP Logon SAP GUI checks for updates on the installation source and applies the updates) has been removed, because a superior mechanism is available (the SAP Workstation Update - WUS).</LI></UL> <UL><LI><B><B>SAPPhone Server</B></B>: The SAPPhone Server and the SAPPhone Status Update control are no longer delivered together with SAP GUI for Windows 7.30. For more information see SAP note 171201.</LI></UL> <UL><LI><B>BW 3.5 Addon</B>: The Frontend Add-On for BW 3.5 is no longer shipped with SAP GUI for Windows 7.30 (see note 1410878 for more information)</LI></UL> <UL><LI><B>PS: Export Interfaces</B>: The component PS: Export Interfaces is not delivered anymore with SAP GUI.</LI></UL> <UL><LI><B>Balanced scorecard and Sales Planning</B>: The SEM components \"Balanced Scorecard\" and \"Sales Planning\" are not part of the delivery of SAP GUI for Windows 7.30. For more information see SAP note 1665318.<br /></LI></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D031909)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D030047)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001670678/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001670678/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001670678/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001670678/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001670678/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001670678/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001670678/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001670678/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001670678/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "66971", "RefComponent": "BC-FES-GUI", "RefTitle": "Supported SAP GUI platforms", "RefUrl": "/notes/66971"}, {"RefNumber": "1769660", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1769660"}, {"RefNumber": "1769598", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1769598"}, {"RefNumber": "1740921", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP GUI 7.30: Define custom SAP Signature colors", "RefUrl": "/notes/1740921"}, {"RefNumber": "1727111", "RefComponent": "BC-FES-GUI", "RefTitle": "Language preassignment on SAP LOGON logon screen", "RefUrl": "/notes/1727111"}, {"RefNumber": "171201", "RefComponent": "BC-SRV-COM-TEL", "RefTitle": "SAPphone server: Versions and TAPI service provider", "RefUrl": "/notes/171201"}, {"RefNumber": "1710864", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP Logon: Possibility to disable views via registry", "RefUrl": "/notes/1710864"}, {"RefNumber": "1665318", "RefComponent": "FIN-SEM", "RefTitle": "SEM UI components and SAP Presentation CD/DVD", "RefUrl": "/notes/1665318"}, {"RefNumber": "1652772", "RefComponent": "BW-SYS-GUI", "RefTitle": "BI 7.x in 730 DVD - Installation details", "RefUrl": "/notes/1652772"}, {"RefNumber": "1652771", "RefComponent": "BW-SYS-GUI", "RefTitle": "BI 7.x in 730 DVD - Removal of the BW 3.5 Frontend Tools", "RefUrl": "/notes/1652771"}, {"RefNumber": "1608882", "RefComponent": "BC-ABA-SC", "RefTitle": "Logon screen and SAPLOGON language", "RefUrl": "/notes/1608882"}, {"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519"}, {"RefNumber": "1426178", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP Logon (Pad): create/distribute server configuration file", "RefUrl": "/notes/1426178"}, {"RefNumber": "1410878", "RefComponent": "BW-BEX-ET", "RefTitle": "Maintenance for BW 3.5 front-end add-ons", "RefUrl": "/notes/1410878"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1844773", "RefComponent": "PS-ST-INT-EXT", "RefTitle": "Exported MPX file has problem when opening with MS Project 2010 / 2013", "RefUrl": "/notes/1844773 "}, {"RefNumber": "66971", "RefComponent": "BC-FES-GUI", "RefTitle": "Supported SAP GUI platforms", "RefUrl": "/notes/66971 "}, {"RefNumber": "1727111", "RefComponent": "BC-FES-GUI", "RefTitle": "Language preassignment on SAP LOGON logon screen", "RefUrl": "/notes/1727111 "}, {"RefNumber": "1652772", "RefComponent": "BW-SYS-GUI", "RefTitle": "BI 7.x in 730 DVD - Installation details", "RefUrl": "/notes/1652772 "}, {"RefNumber": "1652771", "RefComponent": "BW-SYS-GUI", "RefTitle": "BI 7.x in 730 DVD - Removal of the BW 3.5 Frontend Tools", "RefUrl": "/notes/1652771 "}, {"RefNumber": "1690662", "RefComponent": "BC-SEC-SNC", "RefTitle": "Option: Blocking unencrypted SAPGUI/RFC connections", "RefUrl": "/notes/1690662 "}, {"RefNumber": "1608882", "RefComponent": "BC-ABA-SC", "RefTitle": "Logon screen and SAPLOGON language", "RefUrl": "/notes/1608882 "}, {"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519 "}, {"RefNumber": "1410878", "RefComponent": "BW-BEX-ET", "RefTitle": "Maintenance for BW 3.5 front-end add-ons", "RefUrl": "/notes/1410878 "}, {"RefNumber": "1426178", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP Logon (Pad): create/distribute server configuration file", "RefUrl": "/notes/1426178 "}, {"RefNumber": "1740921", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP GUI 7.30: Define custom SAP Signature colors", "RefUrl": "/notes/1740921 "}, {"RefNumber": "1710864", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP Logon: Possibility to disable views via registry", "RefUrl": "/notes/1710864 "}, {"RefNumber": "171201", "RefComponent": "BC-SRV-COM-TEL", "RefTitle": "SAPphone server: Versions and TAPI service provider", "RefUrl": "/notes/171201 "}, {"RefNumber": "1665318", "RefComponent": "FIN-SEM", "RefTitle": "SEM UI components and SAP Presentation CD/DVD", "RefUrl": "/notes/1665318 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BC-FES-GUI", "From": "7.30", "To": "7.30", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}