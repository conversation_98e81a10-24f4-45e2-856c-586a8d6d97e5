{"Request": {"Number": "19706", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 208, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014340572017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000019706?language=E&token=15F1918BC85FAB60825706764C263058"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000019706", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000019706/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "19706"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 22}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "31.07.2014"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CCM-PRN-SPO"}, "SAPComponentKeyText": {"_label": "Component", "value": "Spool System"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Computer Center Management System (CCMS)", "value": "BC-CCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Print and Output Management", "value": "BC-CCM-PRN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-PRN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Spool System", "value": "BC-CCM-PRN-SPO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-PRN-SPO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "19706 - Tuning the Spooler"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The response speed or throughput of the SAP spooler is unsatisfactory.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>..</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The spooler configuration is often inappropriately set.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This note contains recommendations and measures for SAP Systems in which spooler performance is particularly important.<br/><br/>The reason for this note is that the R/3 spooler can be configured and implemented in a large variety of ways. Because it aims to offer convenience and robustness, it can reach printers in all sorts of ways and can also serve unstable or unreliable host spoolers. However, this means that internal alternative routes and protection and recovery measures become necessary, and these may conflict with performance.<br/><br/>Organizational measures, coupled with care in making settings, are necessary for systems with high demands placed on spooler throughput and/or spooler runtimes.<br/><br/>Note 16307 explains where specific times are used during printing. Focus your attention on the time interval between transaction start and paper output.</p>\r\n<p><strong>Allocation of Printers to Spool Work Processes<br/></strong></p>\r\n<p>The first step is to examine your output devices. Evaluate them according to required response time and volume.<br/><br/>Now divide your printers into three groups. Place printers of the same type in the same group.<br/><br/>Group 1 (Productive printer):<br/>All printers with the shortest possible response time.<br/>For example: Goods receipt/issue sheets, delivery notes, patient entry sheets,...<br/><br/>Group 2 (Mass printer):<br/>Printers with large volumes.<br/>For example: Analyses, monthly lists, direct mailing...<br/><br/>Group 3:<br/>Printers with small and moderate volumes, and without severe time demands.<br/>For example: Small lists printed at the workstation...<br/><br/>If you have distributed your SAP System across several computers, you should provide the spool service on each computer.<br/><br/>If you have three or more spool servers, assign each group to one or more spool server. No spool work process may serve several groups. Group 1 devices should NEVER be defined with access type &#39;U&#39;, &#39;S&#39; or &#39;G&#39; (&#39;F&#39; in old releases).<br/><br/>If there are two spool servers in the system, Group 1 must receive its own spool server. Access type &#39;U&#39;, &#39;S&#39; or &#39;G&#39; must not be used in it. This should also be avoided in the spool servers for the other two groups.<br/><br/>With only one spool WP, that is, a central instance without other application servers, all requests inevitably have to be processed by one spool server.<br/>. Likewise, this must NOT use access type &#39;U&#39;, &#39;S&#39; or &#39;G&#39;.<br/><br/></p>\r\n<p><strong>Number of Spool Work Processes<br/></strong></p>\r\n<p>These groups should be distributed on more than one spool work process, depending on the size of the group and the required response time. This is particularly important for group 1. If you find that there are bottlenecks in your system, it can be a good idea to set up instances on a host which only deal with spool functionality (remember you need two dialog work processes as well as the spool work process).<br/><br/>As of Release 4.0A, there are no more restrictions concerning the number of spool work processes per instance. This enables a much more flexible structure for the spool service. Please also see Notes 108799 and 118057 in this regard.</p>\r\n<p><strong>Access Methods<br/></strong></p>\r\n<p>Access type &#39;L&#39;: The spool work process uses a command (such as &#39;lp&#39;, &#39;lpr&#39;,..) to pass spool requests to the host spooler of its own machine. These commands are quick, providing the host spooler queues do not overflow. However, they normally copy the data around again. A command (such as &#39;lpq&#39;, &#39;lpstat&#39;,..) is used to query the host spooler. With network problems, these commands become slow because they (aim to) determine the current status. If the runtimes become two long, you can use the print configuration of Transaction SPAD to deactivate the queries.<br/><br/>Access type &#39;C&#39;: Procedure calls are used to communicate with the host spooler on the computer. However, UNIX spoolers generally do not have an API. The time conditions are the same as those for &#39;L&#39;. This access method is only available under Windows/NT and AS/400. Access method &#39;U&#39;:<br/><br/>A TCP/IP connection to the host spooler is set up. The host spooler normally runs on a PC. Experience has shown that, with normal operation, it is impossible to ensure that all printer PCs always accept/respond to data as soon as the spool WP wants to talk with them. Thus, the spool WP is forced to wait again and again.<br/><br/>Access method &#39;S&#39;: A different protocol is used on the TCP/IP connection. Timing problems, however, are about the same as for &#39;U&#39;.<br/><br/>Refer to Notes 128105 an 821519 for instructions on how to use access type &#39;G&#39;.<br/><br/>A characteristic of network access types &#39;U&#39; and &#39;S&#39; is that any network problems directly cause an interruption of the spool work process, which hinders the process from processing the print requests. This interferes with the processing of print requests, with the consequence that printers which are not driven by a network access type but still linked to the same spool work process are also affected. If network connections are established via a WAN, you cannot expect a fast response to print requests, and the spool work process is slowed down further. Unlike access method &#39;U&#39;, access method &#39;S&#39; uses a handshake procedure. The result of this is that the response time over a WAN is at least twice as long as it is with access method &#39;U&#39;. If a network access method is required, &#39;U&#39; is to be preferred if only performance aspects are to be considered. (SAPSprint can be run with access method &#39;U&#39; as well as with access method &#39;S&#39;). However, &#39;U&#39; complicates the error analysis as a result of cryptic file names for the print jobs. It is hardly possible to make an assignment to spool requests in the SAP system. Furthermore, some functions are available only with the extendable log of the access method &#39;S&#39;. Normally, given the present speed of the network, the performance-related disadvantage of access method &#39;S&#39; should not be very significant. If there are interruptions in the transfer, the connection generally should be checked because, in this case, acceptable operation is possible neither with access method &#39;U&#39; nor with access method &#39;S&#39;.</p>\r\n<p><strong>Conditions for Printers with a Fast Response (Group 1):<br/></strong></p>\r\n<p>Devices with short response times must NEVER be defined with access type &#39;U&#39; or &#39;S&#39;. When a problem occurs (e.g. network problems, PC is switched off etc.), a single printer linked to a work process by access type &#39;U&#39; disturbs all the connected printers. All printers in this group MUST be linked with access type &#39;L&#39; or &#39;C&#39; (depending on the operating system). If they are not linked to the server, they must be defined in the host spooler as &quot;remote printers&quot; and forwarded via the the host spooler.<br/><br/></p>\r\n<p><strong>Host Spooler Requests<br/></strong></p>\r\n<p>If host spooler inquiries take a long time - perhaps because the &#39;lpq&#39; or &#39;lpstat&#39; command is slow or because PCs are not responding - the spool WP can lose A LOT of time. To avoid this problem, use transaction SPAD to deactivate &quot;Host spooler enquiries&quot; for all printers which are giving problems.<br/><br/>Disadvantage: In R/3, spool requests are considered &quot;finished&quot; the moment they are successfully handed over to the host spooler.</p>\r\n<p><strong>Network Printers<br/></strong></p>\r\n<p>A printer is converted into a network printer when a small network node processor is installed, either as a plugin card or small peripheral device. A small network node processor of this type usually implements a TCP/IP protocol stack and a Berkeley-compatible &quot;lpd&quot;. (Example of a product of this type currently on the market: The HP-Jet-direct-cards.)<br/>Advantage of conversion: The network node processor is always active (when the printer is switched on).<br/>Disadvantage of conversion: Normally these printers do not have a large buffer (1 MB max.), so that, beyond certain volume levels, data transfer comes to a standstill and the spoolWP practically has to wait for the printer to finish printing. In the worst cases, large print tasks may cause connection problems, as SpoolWP will not wait long enough for the printer.<br/>(for comparison: a PC with SAPLPD and also printers such as the QMS Crown immediately accept everything and buffer it on the magnetic disk.)<br/><br/>Small network printers linked to the spool WP with access type &#39;U&#39; are performance killers! Wherever possible, you should define them via a local host spool system which you can then address via access types &#39;L&#39; or &#39;C&#39;.</p>\r\n<p><strong>Other Measures<br/></strong></p>\r\n<p>In general, spool request data is written and read much quicker if it is stored in files rather than in database table TST03. The advantages and disadvantages are described in Notes 11070, 10551 and 20176. Profile parameter: &quot;rspo/store_location&quot;.<br/><br/>Make sure that the spooler database does not become too full. Program RSPO0041 is described in Notes 16083 and 41547.<br/><br/>Quite a few printers can be slow in various emulation modes. It may be worthwhile abandoning continuous lines (note 15594). (See Note 15594).<br/><br/>As of Release 3.0A, all users should use the <br/>Option: Use the option for deleting after the output to keep the number of spool requests that are simultaneously in the system to a minimum.<br/><br/>Regardless of the configuration of the R/3 spool system, you can improve spool performance to a certain extent by the way in which you generate spool requests in applications. Performance is better if you create a few large spool requests instead of several smaller ones. The amount of data transferred and the overheads involved with establishing links to the external spool system are reduced. If you can configure your applications in this way (for example, via message control), this should improve the performance of your spooler system.</p>\r\n<p><strong>Further Information<br/></strong></p>\r\n<p>Note 65109: Long delays when printing during load<br/>Note 53047: Warning: Queries to print requests are too slow<br/>Note 16083: Reorganization jobs<br/>Note 16307: Processing times when printing<br/>Note 11214: SPAD: Changes have no effect<br/>Note 11070: Space requirements of TemSe and spooler<br/>Note 10551: Table TST03 (tablespace PSAPPROTD) size increasing<br/>Note 07140: Problem printing very large lists<br/>Note 108799: How many spool work processes per instance<br/>Note 118057: Flexible structure of the R/3 spool service<br/><br/>Spooler chapter in the SAP System Administration documentation.<br/><br/>Online help at many places in transaction SPAD.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D025322)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I035059)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000019706/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000019706/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000019706/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000019706/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000019706/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000019706/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000019706/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000019706/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000019706/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "7140", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/7140"}, {"RefNumber": "68511", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "How the print request gets to the spool work process", "RefUrl": "/notes/68511"}, {"RefNumber": "65109", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "Long delays when printing during overload", "RefUrl": "/notes/65109"}, {"RefNumber": "53047", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "Warning: Queries to print requests are too slow", "RefUrl": "/notes/53047"}, {"RefNumber": "504952", "RefComponent": "BC-CCM-PRN", "RefTitle": "Composite SAP Note for spool and print", "RefUrl": "/notes/504952"}, {"RefNumber": "39412", "RefComponent": "BC-CST", "RefTitle": "How many work processes should be configured?", "RefUrl": "/notes/39412"}, {"RefNumber": "16307", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "Processing times when printing", "RefUrl": "/notes/16307"}, {"RefNumber": "16083", "RefComponent": "BC-CCM-BTC", "RefTitle": "Standard jobs, reorganization jobs", "RefUrl": "/notes/16083"}, {"RefNumber": "12195", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/12195"}, {"RefNumber": "11214", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/11214"}, {"RefNumber": "11070", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "Space requirements of TemSe and spooler", "RefUrl": "/notes/11070"}, {"RefNumber": "10551", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "Table TST03 grows larger", "RefUrl": "/notes/10551"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "16083", "RefComponent": "BC-CCM-BTC", "RefTitle": "Standard jobs, reorganization jobs", "RefUrl": "/notes/16083 "}, {"RefNumber": "39412", "RefComponent": "BC-CST", "RefTitle": "How many work processes should be configured?", "RefUrl": "/notes/39412 "}, {"RefNumber": "10551", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "Table TST03 grows larger", "RefUrl": "/notes/10551 "}, {"RefNumber": "11070", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "Space requirements of TemSe and spooler", "RefUrl": "/notes/11070 "}, {"RefNumber": "68511", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "How the print request gets to the spool work process", "RefUrl": "/notes/68511 "}, {"RefNumber": "53047", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "Warning: Queries to print requests are too slow", "RefUrl": "/notes/53047 "}, {"RefNumber": "504952", "RefComponent": "BC-CCM-PRN", "RefTitle": "Composite SAP Note for spool and print", "RefUrl": "/notes/504952 "}, {"RefNumber": "16307", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "Processing times when printing", "RefUrl": "/notes/16307 "}, {"RefNumber": "65109", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "Long delays when printing during overload", "RefUrl": "/notes/65109 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}