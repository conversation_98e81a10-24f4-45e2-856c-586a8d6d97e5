{"Request": {"Number": "145854", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 924, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014663052017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000145854?language=E&token=F9D55AC05F476AF81526E992722779DF"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000145854", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000145854/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "145854"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 103}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.06.2001"}, "SAPComponentKey": {"_label": "Component", "value": "IS-OIL-BC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Please use component BC-UPG-ADDON"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oil", "value": "IS-OIL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-OIL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Please use component BC-UPG-ADDON", "value": "IS-OIL-BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-OIL-BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "145854 - Order of IS-OIL notes 4.0b on basis R/3 4.0B (LCP)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>**********************************************************************<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&gt;&gt;&gt; I M P O R T A N T &lt;&lt;&lt;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; From LCP 54 onwards, LCPs contain only HR modifications.&#x00A0;&#x00A0;&#x00A0;&#x00A0; *<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Please read Note 334872 for further details.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Due to the fact that IS-OIL and HR are de-coupled this&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;note will no longer be maintained. Please continue&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;maintainence of your systems as described in Notes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;145850 and 334872.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*<br />**********************************************************************<br /><br />**********************************************************************<br />* WARNING: This is an IS-OIL-specific note. If you DON'T have IS-Oil *<br />* installed on your system, this note does not apply to you. If this *<br />* note is applied and you do not have IS-Oil installed, you could&#x00A0;&#x00A0;&#x00A0;&#x00A0;*<br />* cause serious damage to your system.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *<br />**********************************************************************<br /><br />**********************************************************************<br />* This is an IS-OIL-specific NOTE FOR CUSTOMERS USING LEGAL CHANGE&#x00A0;&#x00A0; *<br />* PATCHES (LCP) instead of R/3 Support Packages (SP).&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*<br />* If you apply SPs to your system, see note 145850.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*<br />* For details about LCPs see SAPNet note 86241 and notes cited&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *<br />* therein.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *<br />**********************************************************************<br /><br />!!!!!&#x00A0;&#x00A0;NEW&#x00A0;&#x00A0;&#x00A0;&#x00A0; NEW&#x00A0;&#x00A0;&#x00A0;&#x00A0;NEW&#x00A0;&#x00A0;&#x00A0;&#x00A0;NEW&#x00A0;&#x00A0;&#x00A0;&#x00A0;NEW&#x00A0;&#x00A0;&#x00A0;&#x00A0; NEW&#x00A0;&#x00A0;&#x00A0;&#x00A0;NEW&#x00A0;&#x00A0;&#x00A0;&#x00A0;NEW&#x00A0;&#x00A0; !!!!!!<br />!&#x00A0;&#x00A0;FEBRUARY 2000:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; !<br />!&#x00A0;&#x00A0;This note has been enhanced for each transport by the&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;!<br />!&#x00A0;&#x00A0;following information:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; !<br />!&#x00A0;&#x00A0;&#x00A0;&#x00A0;* 'X' means, that additional manual steps have to be performed&#x00A0;&#x00A0; !<br />!&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; after the implementation of the transport (please refer to&#x00A0;&#x00A0;&#x00A0;&#x00A0;!<br />!&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; the corresponding note)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; !<br />!&#x00A0;&#x00A0;&#x00A0;&#x00A0;* '-' means, that no additional manual steps are neccessary&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;!<br />!!!!!&#x00A0;&#x00A0;NEW&#x00A0;&#x00A0;&#x00A0;&#x00A0; NEW&#x00A0;&#x00A0;&#x00A0;&#x00A0;NEW&#x00A0;&#x00A0;&#x00A0;&#x00A0;NEW&#x00A0;&#x00A0;&#x00A0;&#x00A0;NEW&#x00A0;&#x00A0;&#x00A0;&#x00A0; NEW&#x00A0;&#x00A0;&#x00A0;&#x00A0;NEW&#x00A0;&#x00A0;&#x00A0;&#x00A0;NEW&#x00A0;&#x00A0; !!!!!!<br /><br />To avoid conflicts between different oil corrections, we have written this common note, which describes the IS-OIL corrections available for those systems, which have installed IS-OIL 4.0B (based on R/3 4.0B) and are using LCPs.<br />This SAPNet Note is updated on a regularly basis (normally once a week). there might exist further brand new OIL corrections not yet mentioned in this Note (even at the direct update time). This is caused by the following problem:<br />The creation of corrections is a sequential process in our support system. Whenever a correction is created and released after a correction, which has not yet been released for customers it is not possible to update the sequence note directly.<br />In case you are not directly affected by such a current correction (for example: the correction has not been created because of a SAPNet Request raised by you) it is recommended to wait with the import of the correction until it is mentioned in this sequence note.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>IS-Oil, Industry Solution, SAPSERV, Is-OIL correction transports, LCP</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The transports/corrections mentioned here can only be applied to IS-OIL<br />systems based on Core R/3 Release 4.0B.<br />If in the following list a transport request A is replaced by<br />transport request B, this means:<br />&#x00A0;&#x00A0; 1. Ignore transport A and do NOT import it.<br />&#x00A0;&#x00A0; 2. The right sequence for the import of transport B is given<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;in the list and it must NOT be imported at the point where<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;transport A is mentioned.<br />&#x00A0;&#x00A0; 3. If you have already imported transport A just proceed with<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;importing further corrections and don&#x00B4;t forget to import B.<br /><br />If in the following list a transport request of a LCP-CRT (e.g.<br />SAPKI40L15) is mentioned, proceed in the following way:<br />&#x00A0;&#x00A0;1. If not already carried out, apply those Legal Change Patches which<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; were released since the last LCP-CRT was installed and which had<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; no conflicts with IS-Oil. (i.e. no LCP-CRT exist for these Legal<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Change Patches).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Legal Change Patches without LCP-CRTs do not conflict with IS-Oil<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; and therefore they only have to be applied in the correct Legal<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Change Patch sequence.<br />&#x00A0;&#x00A0;2. Apply the Legal Change Patches that corresponds to the LCP-CRT<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; mentioned in the list together with the LCP-CRT (one (!)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Patch-Queue containing both together)<br /><br /><br />NEW PROCEDURE FOR APPLYING CORRECTIONS<br />--------------------------------------<br />To improve the process of applying corrections we have included all single corrections released between LCP-CRT n and LCP-CRT n+1 into LCP-CRT n+1. Therefore, it is recommended to apply ONLY LCPs and LCP-CRTs and NOT single corrections. Single corrections should only be applied in exceptional circumstances, i.e. that the correction is not yet available as part of a LCP-CRT.<br /><br />The possible situations are explained with the following examples:<br /><br />1. Example A (You don`t have to apply any special corrections):<br />&#x00A0;&#x00A0; Just apply the LCPs together with the LCP-CRTs with the transaction<br />&#x00A0;&#x00A0; SPAM. Do NOT apply any single correction.<br /><br />2. Example B (You must apply a `special correction` and cannot wait<br />&#x00A0;&#x00A0; until the next LCP-CRT n+1 is released)<br />&#x00A0;&#x00A0; As prerequisite you must install all LCPs and LCP-CRTs up to the<br />&#x00A0;&#x00A0; last released LCP-CRT n. Afterwards it is mandatory to apply all<br />&#x00A0;&#x00A0; corrections, which have been released after this LCP-CRT n, up to<br />&#x00A0;&#x00A0; the required `special correction` as mentioned in this sequence<br />&#x00A0;&#x00A0; note. When LCP-CRT n+1 is released later on, just proceed by<br />&#x00A0;&#x00A0; applying LCP n+1 and the corresponding LCP-CRT n+1.<br /><br />Additional Remark:<br />------------------<br />Special single corrections may have the need to perform manual steps in<br />addition to the import of the correction transport. If this is the case<br />it is described in the SAPNet Note mentioned together with the<br />transport request in the list below.<br />This is also valid for applying IS-OIL CRT&#x00B4;s for R/3 Support Packages<br />or Legal Change Patches. These CRT&#x00B4;s also do not include the manual<br />steps necessary for the included single corrections.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;transport&#x00A0;&#x00A0;&#x00A0;&#x00A0;date&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;appl.area&#x00A0;&#x00A0; note<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;==========&#x00A0;&#x00A0;==================&#x00A0;&#x00A0;==========&#x00A0;&#x00A0;=========</p> <UL><LI>LCRT11:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;!!!! CAUTION: Read SAPNet Note 149949 BEFORE applying LCP 11 !!!!<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI40L11&#x00A0;&#x00A0;27 Apr 1999&#x00A0;&#x00A0;17:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT11&#x00A0;&#x00A0;149949</p> <UL><LI>SOEK000030&#x00A0;&#x00A0;30 Apr 1999&#x00A0;&#x00A0;13:50&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261&#x00A0;&#x00A0;repl. by SOEK000040</LI></UL> <UL><LI>SOEK000025&#x00A0;&#x00A0; 30 Apr 1999&#x00A0;&#x00A0;16:57&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0150318&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000040&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 May 1999&#x00A0;&#x00A0;09:30&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261&#x00A0;&#x00A0;repl. by SOEK000118</LI></UL> <UL><LI>SOEK000065&#x00A0;&#x00A0; 12 May 1999&#x00A0;&#x00A0;10:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0151721&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000084&#x00A0;&#x00A0; 12 May 1999&#x00A0;&#x00A0;15:29&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0152285&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000058&#x00A0;&#x00A0; 17 May 1999&#x00A0;&#x00A0;14:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0149493&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000100&#x00A0;&#x00A0; 18 May 1999&#x00A0;&#x00A0;10:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0152777&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000118&#x00A0;&#x00A0;18 May 1999&#x00A0;&#x00A0;16:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261&#x00A0;&#x00A0;repl. by SOEK000182</LI></UL> <UL><LI>LCP-CRT 12:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;!!!! CAUTION: Read SAPNet Note 154963 BEFORE applying LCP 12 !!!!<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI40L12&#x00A0;&#x00A0;02 Jun 1999&#x00A0;&#x00A0;08:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT12&#x00A0;&#x00A0; 154963</p> <UL><LI>LCP-CRT 13:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;!!!! CAUTION: Read SAPNet Note 154963 BEFORE applying LCP 13 !!!!<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI40L13&#x00A0;&#x00A0;02 Jun 1999&#x00A0;&#x00A0;08:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT13&#x00A0;&#x00A0; 154963</p> <UL><LI>SOEK000184&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Jun 1999&#x00A0;&#x00A0;11:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0155337&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK000192&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Jun 1999&#x00A0;&#x00A0;11:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0155337&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK000182&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Jun 1999&#x00A0;&#x00A0;11:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261&#x00A0;&#x00A0;repl. by SOEK000201</LI></UL> <UL><LI>SOEK000048&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Jun 1999&#x00A0;&#x00A0;16:16&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0154538&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK000190&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Jun 1999&#x00A0;&#x00A0;14:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0154790&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK000178&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Jun 1999&#x00A0;&#x00A0;16:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0154841&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK000152&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Jun 1999&#x00A0;&#x00A0;10:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0154470&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK000199&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Jun 1999&#x00A0;&#x00A0;14:33&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0155661&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK000169&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Jun 1999&#x00A0;&#x00A0;14:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0152478&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK000172&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Jun 1999&#x00A0;&#x00A0;14:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0154537&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK000174&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Jun 1999&#x00A0;&#x00A0;14:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0154539&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK000201&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Jun 1999&#x00A0;&#x00A0;16:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261&#x00A0;&#x00A0;repl. by SOEK000393</LI></UL> <UL><LI>SOEK000203&#x00A0;&#x00A0; 10 Jun 1999&#x00A0;&#x00A0;10:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0156403&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000207&#x00A0;&#x00A0; 10 Jun 1999&#x00A0;&#x00A0;15:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0156455&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000197&#x00A0;&#x00A0; 10 Jun 1999&#x00A0;&#x00A0;16:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0147813&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000213&#x00A0;&#x00A0; 11 Jun 1999&#x00A0;&#x00A0;11:51&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0153222&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000218&#x00A0;&#x00A0;11 Jun 1999&#x00A0;&#x00A0;13:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0156697&#x00A0;&#x00A0;repl. by SOEK000229</LI></UL> <UL><LI>SOEK000194&#x00A0;&#x00A0; 14 Jun 1999&#x00A0;&#x00A0;09:12&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0155914&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000227&#x00A0;&#x00A0; 14 Jun 1999&#x00A0;&#x00A0;11:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MRN&#x00A0;&#x00A0;0156906&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK000211&#x00A0;&#x00A0; 14 Jun 1999&#x00A0;&#x00A0;13:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0156700&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000229&#x00A0;&#x00A0; 14 Jun 1999&#x00A0;&#x00A0;15:18&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0156697&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000216&#x00A0;&#x00A0; 15 Jun 1999&#x00A0;&#x00A0;08:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0156993&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000245&#x00A0;&#x00A0; 15 Jun 1999&#x00A0;&#x00A0;11:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0157044&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000255&#x00A0;&#x00A0; 16 Jun 1999&#x00A0;&#x00A0;12:41&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0156599&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK000249&#x00A0;&#x00A0; 16 Jun 1999&#x00A0;&#x00A0;12:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MRN&#x00A0;&#x00A0;0157494&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK000176&#x00A0;&#x00A0; 16 Jun 1999&#x00A0;&#x00A0;16:33&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0157569&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000056&#x00A0;&#x00A0; 17 Jun 1999&#x00A0;&#x00A0;09:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0151361&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000261&#x00A0;&#x00A0; 17 Jun 1999&#x00A0;&#x00A0;13:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0157768&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000256&#x00A0;&#x00A0; 17 Jun 1999&#x00A0;&#x00A0;14:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0157801&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000269&#x00A0;&#x00A0; 18 Jun 1999&#x00A0;&#x00A0;11:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0157975&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000267&#x00A0;&#x00A0; 18 Jun 1999&#x00A0;&#x00A0;13:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0157920&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000275&#x00A0;&#x00A0; 24 Jun 1999&#x00A0;&#x00A0;10:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0158344&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000393&#x00A0;&#x00A0;15 Jul 1999&#x00A0;&#x00A0;17:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261&#x00A0;&#x00A0;repl. by SOEK003441</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;!!!! CAUTION: Read SAPNet Note 167595 BEFORE applying LCP 14 !!!!</p> <UL><LI>SAPKI40L14&#x00A0;&#x00A0; 30 Aug 1999&#x00A0;&#x00A0;10:30&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT14</LI></UL> <UL><LI>SAPKI40L15&#x00A0;&#x00A0; 30 Aug 1999&#x00A0;&#x00A0;10:35&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT15</LI></UL> <UL><LI>SAPKI40L16&#x00A0;&#x00A0; 30 Aug 1999&#x00A0;&#x00A0;10:38&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT16</LI></UL> <UL><LI>SAPKI40L17&#x00A0;&#x00A0; 30 Aug 1999&#x00A0;&#x00A0;10:40&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT17</LI></UL> <UL><LI>SAPKI40L18&#x00A0;&#x00A0; 30 Aug 1999&#x00A0;&#x00A0;10:42&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT18</LI></UL> <UL><LI>SAPKI40L19&#x00A0;&#x00A0; 30 Aug 1999&#x00A0;&#x00A0;10:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT19</LI></UL> <UL><LI>SAPKI40L20&#x00A0;&#x00A0; 30 Aug 1999&#x00A0;&#x00A0;11:02&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT20</LI></UL> <UL><LI>SOEK001537&#x00A0;&#x00A0; 30 Aug 1999&#x00A0;&#x00A0;12:13&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0102306&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK001631&#x00A0;&#x00A0; 30 Aug 1999&#x00A0;&#x00A0;13:48&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0170066&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000711&#x00A0;&#x00A0; 31 Aug 1999&#x00A0;&#x00A0;09:02&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0165201&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK001713&#x00A0;&#x00A0; 31 Aug 1999&#x00A0;&#x00A0;12:57&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0170314&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK001721&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Sep 1999&#x00A0;&#x00A0;16:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0170803&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK001752&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Sep 1999&#x00A0;&#x00A0;16:27&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0170822&#x00A0;&#x00A0; X</LI></UL> <UL><LI>SOEK001971&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Sep 1999&#x00A0;&#x00A0;18:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0170815&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK001411&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Sep 1999&#x00A0;&#x00A0;11:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0168765&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK001719&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Sep 1999&#x00A0;&#x00A0;11:26&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0170219&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK002083&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 Sep 1999&#x00A0;&#x00A0;10:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0171065&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK002111&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 Sep 1999&#x00A0;&#x00A0;10:41&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0171086&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK001621&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 Sep 1999&#x00A0;&#x00A0;13:48&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0171053&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK001918&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Sep 1999&#x00A0;&#x00A0;10:20&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0170668&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK002313&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Sep 1999&#x00A0;&#x00A0;15:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0171391&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK001838&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Sep 1999&#x00A0;&#x00A0;13:35&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0170620&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK002085&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Sep 1999&#x00A0;&#x00A0;16:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0171037&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK002311&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Sep 1999&#x00A0;&#x00A0;16:41&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0171736&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK001974&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Sep 1999&#x00A0;&#x00A0;17:03&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0170871&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK001587&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Sep 1999&#x00A0;&#x00A0;08:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0169295&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK002403&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Sep 1999&#x00A0;&#x00A0;15:40&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDRP 0171871&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK002405&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Sep 1999&#x00A0;&#x00A0;17:02&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0172009&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK002408&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Sep 1999&#x00A0;&#x00A0;13:40&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0172063&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK002484&#x00A0;&#x00A0; 10 Sep 1999&#x00A0;&#x00A0;09:10&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0166548&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK002540&#x00A0;&#x00A0; 13 Sep 1999&#x00A0;&#x00A0;11:07&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0172527&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK002638&#x00A0;&#x00A0; 13 Sep 1999&#x00A0;&#x00A0;16:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0172681&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK002339&#x00A0;&#x00A0; 13 Sep 1999&#x00A0;&#x00A0;17:04&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0172412&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK001709&#x00A0;&#x00A0; 13 Sep 1999&#x00A0;&#x00A0;17:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0170287&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK002668&#x00A0;&#x00A0; 13 Sep 1999&#x00A0;&#x00A0;17:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0172759&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK002490&#x00A0;&#x00A0; 13 Sep 1999&#x00A0;&#x00A0;20:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0172738&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK001976&#x00A0;&#x00A0; 14 Sep 1999&#x00A0;&#x00A0;15:59&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 0170968&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK001053&#x00A0;&#x00A0; 14 Sep 1999&#x00A0;&#x00A0;16:06&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0167025&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK002919&#x00A0;&#x00A0; 14 Sep 1999&#x00A0;&#x00A0;17:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0173139&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK002946&#x00A0;&#x00A0; 15 Sep 1999&#x00A0;&#x00A0;09:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0173205&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK002915&#x00A0;&#x00A0; 16 Sep 1999&#x00A0;&#x00A0;09:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0173079&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003065&#x00A0;&#x00A0; 17 Sep 1999&#x00A0;&#x00A0;10:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0173532&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003069&#x00A0;&#x00A0; 17 Sep 1999&#x00A0;&#x00A0;11:40&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0173715&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003071&#x00A0;&#x00A0; 17 Sep 1999&#x00A0;&#x00A0;12:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0173720&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003063&#x00A0;&#x00A0; 17 Sep 1999&#x00A0;&#x00A0;13:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0173492&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003154&#x00A0;&#x00A0; 17 Sep 1999&#x00A0;&#x00A0;14:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0173754&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003161&#x00A0;&#x00A0; 20 Sep 1999&#x00A0;&#x00A0;18:16&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 0174020&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK003193&#x00A0;&#x00A0; 21 Sep 1999&#x00A0;&#x00A0;16:04&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0174228&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003151&#x00A0;&#x00A0; 22 Sep 1999&#x00A0;&#x00A0;13:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0174462&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003187&#x00A0;&#x00A0; 22 Sep 1999&#x00A0;&#x00A0;14:44&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0174144&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003396&#x00A0;&#x00A0; 23 Sep 1999&#x00A0;&#x00A0;10:48&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0174437&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003392&#x00A0;&#x00A0; 23 Sep 1999&#x00A0;&#x00A0;14:20&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0174648&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003060&#x00A0;&#x00A0; 23 Sep 1999&#x00A0;&#x00A0;18:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDRP 0173484&#x00A0;&#x00A0; X</LI></UL> <UL><LI>SOEK003400&#x00A0;&#x00A0; 24 Sep 1999&#x00A0;&#x00A0;15:59&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0175059&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003398&#x00A0;&#x00A0; 24 Sep 1999&#x00A0;&#x00A0;17:19&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0174710&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003410&#x00A0;&#x00A0; 27 Sep 1999&#x00A0;&#x00A0;13:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0175037&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003454&#x00A0;&#x00A0; 28 Sep 1999&#x00A0;&#x00A0;16:06&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0175501&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003394&#x00A0;&#x00A0; 29 Sep 1999&#x00A0;&#x00A0;08:38&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0174656&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003456&#x00A0;&#x00A0; 29 Sep 1999&#x00A0;&#x00A0;09:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0175646&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003412&#x00A0;&#x00A0; 29 Sep 1999&#x00A0;&#x00A0;10:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0175140&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003450&#x00A0;&#x00A0; 30 Sep 1999&#x00A0;&#x00A0;09:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0175463&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003476&#x00A0;&#x00A0; 30 Sep 1999&#x00A0;&#x00A0;09:03&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0175840&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003464&#x00A0;&#x00A0; 30 Sep 1999&#x00A0;&#x00A0;13:41&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0175777&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003480&#x00A0;&#x00A0; 30 Sep 1999&#x00A0;&#x00A0;14:42&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0175917&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003491&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Oct 1999&#x00A0;&#x00A0;17:06&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0176218&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK003489&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Oct 1999&#x00A0;&#x00A0;17:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0176214&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK002917&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Oct 1999&#x00A0;&#x00A0;15:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0173130&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK003402&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Oct 1999&#x00A0;&#x00A0;10:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0174746&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK003291&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Oct 1999&#x00A0;&#x00A0;11:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0175494&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK003502&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Oct 1999&#x00A0;&#x00A0;11:34&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0176589&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK003510&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Oct 1999&#x00A0;&#x00A0;16:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0176741&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK003441&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Oct 1999&#x00A0;&#x00A0;09:42&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261&#x00A0;&#x00A0;repl. by SOEK003579</LI></UL> <UL><LI>SOEK003514&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Oct 1999&#x00A0;&#x00A0;11:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDR&#x00A0;&#x00A0;0176785&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK003518&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Oct 1999&#x00A0;&#x00A0;21:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MRN&#x00A0;&#x00A0;0176909&#x00A0;&#x00A0; X</LI></UL> <UL><LI>SOEK003406&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Oct 1999&#x00A0;&#x00A0;08:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0176647&#x00A0;&#x00A0; X</LI></UL> <UL><LI>SOEK003522&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Oct 1999&#x00A0;&#x00A0;10:48&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0176746&#x00A0;&#x00A0;repl. by SOEK003660</LI></UL> <UL><LI>SOEK003539&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Oct 1999&#x00A0;&#x00A0;11:12&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0177556&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK003542&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Oct 1999&#x00A0;&#x00A0;14:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0177563&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK003508&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Oct 1999&#x00A0;&#x00A0;15:18&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0176988&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK003439&#x00A0;&#x00A0; 11 Oct 1999&#x00A0;&#x00A0;16:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0175342&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003564&#x00A0;&#x00A0; 12 Oct 1999&#x00A0;&#x00A0;15:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0178164&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003566&#x00A0;&#x00A0; 12 Oct 1999&#x00A0;&#x00A0;15:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0177949&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003549&#x00A0;&#x00A0; 13 Oct 1999&#x00A0;&#x00A0;17:34&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0177825&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003579&#x00A0;&#x00A0;14 Oct 1999&#x00A0;&#x00A0;10:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261&#x00A0;&#x00A0;repl. by SOEK004315</LI></UL> <UL><LI>SOEK003516&#x00A0;&#x00A0; 14 Oct 1999&#x00A0;&#x00A0;13:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0176858&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK002309&#x00A0;&#x00A0; 14 Oct 1999&#x00A0;&#x00A0;14:48&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0171364&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003571&#x00A0;&#x00A0; 14 Oct 1999&#x00A0;&#x00A0;18:14&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0177866&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003660&#x00A0;&#x00A0;25 Oct 1999&#x00A0;&#x00A0;15:35&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0176746&#x00A0;&#x00A0;repl. by SOEK004023</LI></UL> <UL><LI>SOEK004023&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Nov 1999&#x00A0;&#x00A0;16:47&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0176746&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004124&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Nov 1999&#x00A0;&#x00A0;14:16&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0181652&#x00A0;&#x00A0;repl. by SOEK004773</LI></UL> <UL><LI>SOEK003996&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Nov 1999&#x00A0;&#x00A0;15:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0182651&#x00A0;&#x00A0; -</LI></UL> <UL><LI>&#x00A0;&#x00A0;&#x00A0;&#x00A0;!!!! CAUTION: Read SAPNet Note 180084 BEFORE applying LCP 21&#x00A0;&#x00A0;!!!!</LI></UL> <UL><LI>SAPKI40L21&#x00A0;&#x00A0; 12 Nov 1999&#x00A0;&#x00A0;15:30&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT21</LI></UL> <UL><LI>SAPKI40L23&#x00A0;&#x00A0; 12 Nov 1999&#x00A0;&#x00A0;15:31&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT23</LI></UL> <UL><LI>SAPKI40L24&#x00A0;&#x00A0; 12 Nov 1999&#x00A0;&#x00A0;15:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT24</LI></UL> <UL><LI>SAPKI40L25&#x00A0;&#x00A0; 12 Nov 1999&#x00A0;&#x00A0;15:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT25</LI></UL> <UL><LI>SAPKI40L26&#x00A0;&#x00A0; 12 Nov 1999&#x00A0;&#x00A0;15:33&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT26</LI></UL> <UL><LI>SAPKI40L27&#x00A0;&#x00A0; 12 Nov 1999&#x00A0;&#x00A0;15:33&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT27</LI></UL> <UL><LI>SAPKI40L28&#x00A0;&#x00A0; 12 Nov 1999&#x00A0;&#x00A0;15:34&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT28</LI></UL> <UL><LI>SOEK004215&#x00A0;&#x00A0; 15 Nov 1999&#x00A0;&#x00A0;12:04&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0184148&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004175&#x00A0;&#x00A0; 15 Nov 1999&#x00A0;&#x00A0;13:19&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0183374&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004217&#x00A0;&#x00A0; 15 Nov 1999&#x00A0;&#x00A0;14:34&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0184209&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004207&#x00A0;&#x00A0; 15 Nov 1999&#x00A0;&#x00A0;15:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0183811&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK001629&#x00A0;&#x00A0; 15 Nov 1999&#x00A0;&#x00A0;16:28&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0169833&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004219&#x00A0;&#x00A0; 16 Nov 1999&#x00A0;&#x00A0;09:47&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0183036&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004223&#x00A0;&#x00A0; 16 Nov 1999&#x00A0;&#x00A0;11:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0184295&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003736&#x00A0;&#x00A0; 16 Nov 1999&#x00A0;&#x00A0;13:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0181457&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003822&#x00A0;&#x00A0; 16 Nov 1999&#x00A0;&#x00A0;13:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0181711&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003874&#x00A0;&#x00A0; 16 Nov 1999&#x00A0;&#x00A0;13:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0182178&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004209&#x00A0;&#x00A0; 16 Nov 1999&#x00A0;&#x00A0;13:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0183761&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003537&#x00A0;&#x00A0; 16 Nov 1999&#x00A0;&#x00A0;15:35&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDR&#x00A0;&#x00A0;0177535&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004233&#x00A0;&#x00A0; 17 Nov 1999&#x00A0;&#x00A0;10:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0184644&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004255&#x00A0;&#x00A0; 18 Nov 1999&#x00A0;&#x00A0;16:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0185010&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004262&#x00A0;&#x00A0; 18 Nov 1999&#x00A0;&#x00A0;18:16&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0185062&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003535&#x00A0;&#x00A0; 19 Nov 1999&#x00A0;&#x00A0;09:38&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0177592&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003554&#x00A0;&#x00A0; 19 Nov 1999&#x00A0;&#x00A0;10:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0185159&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004241&#x00A0;&#x00A0; 19 Nov 1999&#x00A0;&#x00A0;11:31&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0185187&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004257&#x00A0;&#x00A0; 19 Nov 1999&#x00A0;&#x00A0;11:38&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0185034&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003600&#x00A0;&#x00A0; 19 Nov 1999&#x00A0;&#x00A0;13:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0178808&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK001633&#x00A0;&#x00A0; 19 Nov 1999&#x00A0;&#x00A0;13:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0170087&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004272&#x00A0;&#x00A0; 19 Nov 1999&#x00A0;&#x00A0;15:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0185231&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004268&#x00A0;&#x00A0; 19 Nov 1999&#x00A0;&#x00A0;16:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0185245&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK004237&#x00A0;&#x00A0; 22 Nov 1999&#x00A0;&#x00A0;08:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0184640&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003656&#x00A0;&#x00A0; 22 Nov 1999&#x00A0;&#x00A0;10:02&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0180418&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003672&#x00A0;&#x00A0; 22 Nov 1999&#x00A0;&#x00A0;13:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0180696&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK003706&#x00A0;&#x00A0; 22 Nov 1999&#x00A0;&#x00A0;13:19&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0185453&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003714&#x00A0;&#x00A0; 22 Nov 1999&#x00A0;&#x00A0;13:20&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0181099&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003699&#x00A0;&#x00A0; 22 Nov 1999&#x00A0;&#x00A0;14:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0181008&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003609&#x00A0;&#x00A0; 22 Nov 1999&#x00A0;&#x00A0;15:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0179143&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003637&#x00A0;&#x00A0; 22 Nov 1999&#x00A0;&#x00A0;20:44&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0179813&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003544&#x00A0;&#x00A0; 22 Nov 1999&#x00A0;&#x00A0;20:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0179868&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003721&#x00A0;&#x00A0; 23 Nov 1999&#x00A0;&#x00A0;09:59&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0181239&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003618&#x00A0;&#x00A0; 23 Nov 1999&#x00A0;&#x00A0;10:03&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0179319&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003708&#x00A0;&#x00A0; 23 Nov 1999&#x00A0;&#x00A0;10:06&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0185620&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003710&#x00A0;&#x00A0; 23 Nov 1999&#x00A0;&#x00A0;10:07&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0185617&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003684&#x00A0;&#x00A0; 23 Nov 1999&#x00A0;&#x00A0;10:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0180759&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003704&#x00A0;&#x00A0; 23 Nov 1999&#x00A0;&#x00A0;11:27&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0181061&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK003731&#x00A0;&#x00A0;23 Nov 1999&#x00A0;&#x00A0;12:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0181435&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004290&#x00A0;&#x00A0;23 Nov 1999&#x00A0;&#x00A0;13:13&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0185685&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004264&#x00A0;&#x00A0;24 Nov 1999&#x00A0;&#x00A0;08:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0184012&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004229&#x00A0;&#x00A0;24 Nov 1999&#x00A0;&#x00A0;10:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0184532&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004295&#x00A0;&#x00A0;24 Nov 1999&#x00A0;&#x00A0;13:59&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0185895&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004304&#x00A0;&#x00A0;25 Nov 1999&#x00A0;&#x00A0;09:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0186151&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004315&#x00A0;&#x00A0;25 Nov 1999&#x00A0;&#x00A0;15:57&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261&#x00A0;&#x00A0;repl. by SOEK005022</LI></UL> <UL><LI>SOEK003466&#x00A0;&#x00A0;25 Nov 1999&#x00A0;&#x00A0;17:18&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0175799&#x00A0;&#x00A0;&#x00A0;&#x00A0;X</LI></UL> <UL><LI>SOEK004320&#x00A0;&#x00A0;26 Nov 1999&#x00A0;&#x00A0;08:02&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0179557&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004312&#x00A0;&#x00A0;25 Nov 1999&#x00A0;&#x00A0;16:18&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0186341&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004393&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Dec 1999&#x00A0;&#x00A0;11:48&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0188842&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SAPKI40L29&#x00A0;&#x00A0; 20 Dec 1999&#x00A0;&#x00A0;15:30&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT29</LI></UL> <UL><LI>SAPKI40L30&#x00A0;&#x00A0; 20 Dec 1999&#x00A0;&#x00A0;15:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT30</LI></UL> <UL><LI>SOEK004638&#x00A0;&#x00A0;21 Dec 1999&#x00A0;&#x00A0;13:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0190248&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004658&#x00A0;&#x00A0;22 Dec 1999&#x00A0;&#x00A0;11:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0191149&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004656&#x00A0;&#x00A0;22 Dec 1999&#x00A0;&#x00A0;14:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0191154&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004668&#x00A0;&#x00A0;23 Dec 1999&#x00A0;&#x00A0;11:30&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0191413&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004666&#x00A0;&#x00A0;23 Dec 1999&#x00A0;&#x00A0;11:33&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0188270&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004205&#x00A0;&#x00A0;27 Dec 1999&#x00A0;&#x00A0;14:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0183597&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004368&#x00A0;&#x00A0;28 Dec 1999&#x00A0;&#x00A0;06:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0187744&#x00A0;&#x00A0;&#x00A0;&#x00A0;X</LI></UL> <UL><LI>SOEK004691&#x00A0;&#x00A0;28 Dec 1999&#x00A0;&#x00A0;16:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0191913&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004664&#x00A0;&#x00A0;29 Dec 1999&#x00A0;&#x00A0;10:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0191401&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004670&#x00A0;&#x00A0;29 Dec 1999&#x00A0;&#x00A0;12:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0191429&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004697&#x00A0;&#x00A0;29 Dec 1999&#x00A0;&#x00A0;12:33&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0192081&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004699&#x00A0;&#x00A0;29 Dec 1999&#x00A0;&#x00A0;16:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0192152&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004651&#x00A0;&#x00A0;29 Dec 1999&#x00A0;&#x00A0;16:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0191151&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004695&#x00A0;&#x00A0;30 Dec 1999&#x00A0;&#x00A0;09:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0192046&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004676&#x00A0;&#x00A0;30 Dec 1999&#x00A0;&#x00A0;09:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0191696&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004693&#x00A0;&#x00A0;30 Dec 1999&#x00A0;&#x00A0;13:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0192014&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004701&#x00A0;&#x00A0;30 Dec 1999&#x00A0;&#x00A0;15:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0192304&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004709&#x00A0;&#x00A0;31 Dec 1999&#x00A0;&#x00A0;08:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0192367&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004713&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Jan 2000&#x00A0;&#x00A0;13:33&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0192520&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004720&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Jan 2000&#x00A0;&#x00A0;11:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0192864&#x00A0;&#x00A0;repl. by SOEK004920</LI></UL> <UL><LI>SOEK004642&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Jan 2000&#x00A0;&#x00A0;13:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0189441&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004720&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Jan 2000&#x00A0;&#x00A0;11:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0192864&#x00A0;&#x00A0;repl. by SOEK004920</LI></UL> <UL><LI>SOEK004734&#x00A0;&#x00A0;10 Jan 2000&#x00A0;&#x00A0;11:49&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0193227&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004728&#x00A0;&#x00A0;10 Jan 2000&#x00A0;&#x00A0;13:13&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0193225&#x00A0;&#x00A0;&#x00A0;&#x00A0;-</LI></UL> <UL><LI>SOEK004689&#x00A0;&#x00A0; 10 Jan 2000&#x00A0;&#x00A0;13:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0191923&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004736&#x00A0;&#x00A0; 10 Jan 2000&#x00A0;&#x00A0;16:18&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0193229&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004703&#x00A0;&#x00A0; 10 Jan 2000&#x00A0;&#x00A0;16:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0192273&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004738&#x00A0;&#x00A0; 10 Jan 2000&#x00A0;&#x00A0;16:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0193231&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004748&#x00A0;&#x00A0; 10 Jan 2000&#x00A0;&#x00A0;16:30&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0193559&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004711&#x00A0;&#x00A0; 11 Jan 2000&#x00A0;&#x00A0;09:38&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDRP 0192527&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004758&#x00A0;&#x00A0; 11 Jan 2000&#x00A0;&#x00A0;09:41&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0193619&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004741&#x00A0;&#x00A0; 11 Jan 2000&#x00A0;&#x00A0;10:09&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0193381&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004732&#x00A0;&#x00A0; 11 Jan 2000&#x00A0;&#x00A0;16:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0193194&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004681&#x00A0;&#x00A0; 12 Jan 2000&#x00A0;&#x00A0;09:51&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0191705&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004763&#x00A0;&#x00A0; 12 Jan 2000&#x00A0;&#x00A0;10:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0193770&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004722&#x00A0;&#x00A0; 12 Jan 2000&#x00A0;&#x00A0;13:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0193001&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK004769&#x00A0;&#x00A0; 12 Jan 2000&#x00A0;&#x00A0;13:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0193968&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004773&#x00A0;&#x00A0; 12 Jan 2000&#x00A0;&#x00A0;14:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0181652&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004743&#x00A0;&#x00A0;13 Jan 2000&#x00A0;&#x00A0;11:19&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDRP 0193407&#x00A0;&#x00A0;repl. by SOEK006578</LI></UL> <UL><LI>SOEK004771&#x00A0;&#x00A0; 13 Jan 2000&#x00A0;&#x00A0;16:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0194386&#x00A0;&#x00A0; -</LI></UL><p><br />!!!&#x00A0;&#x00A0;For the following transports 'X' indicates that manual steps<br />!!!&#x00A0;&#x00A0;are necessary as described in the corresponding note.<br />!!!&#x00A0;&#x00A0;(this note (145854) will be updated soon to show this information<br />!!!&#x00A0;&#x00A0; for all transports listed here. Nevertheless the<br />!!!&#x00A0;&#x00A0; information whether manual steps are necessary is always<br />!!!&#x00A0;&#x00A0; contained in the corresponding note)<br /></p> <UL><LI>SOEK004784&#x00A0;&#x00A0; 17 Jan 2000&#x00A0;&#x00A0;11:09&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0194642&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004793&#x00A0;&#x00A0; 17 Jan 2000&#x00A0;&#x00A0;15:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0195018&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004672&#x00A0;&#x00A0; 20 Jan 2000&#x00A0;&#x00A0;16:29&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0191432&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004515&#x00A0;&#x00A0; 20 Jan 2000&#x00A0;&#x00A0;17:10&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0189648&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK004780&#x00A0;&#x00A0; 20 Jan 2000&#x00A0;&#x00A0;18:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0194327&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004724&#x00A0;&#x00A0; 21 Jan 2000&#x00A0;&#x00A0;11:06&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0193023&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004811&#x00A0;&#x00A0; 21 Jan 2000&#x00A0;&#x00A0;11:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0196052&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004815&#x00A0;&#x00A0; 21 Jan 2000&#x00A0;&#x00A0;11:41&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0196075&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004793&#x00A0;&#x00A0; 17 Jan 2000&#x00A0;&#x00A0;15:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0195018&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004839&#x00A0;&#x00A0; 26 Jan 2000&#x00A0;&#x00A0;11:31&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0196890&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004782&#x00A0;&#x00A0; 27 Jan 2000&#x00A0;&#x00A0;13:13&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0194512&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004847&#x00A0;&#x00A0; 28 Jan 2000&#x00A0;&#x00A0;11:44&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDRP 0197470&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004857&#x00A0;&#x00A0; 28 Jan 2000&#x00A0;&#x00A0;11:44&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0197466&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004874&#x00A0;&#x00A0; 31 Jan 2000&#x00A0;&#x00A0;14:51&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0197769&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004884&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Feb 2000&#x00A0;&#x00A0;07:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0197908&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004880&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Feb 2000&#x00A0;&#x00A0;09:02&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MRN&#x00A0;&#x00A0;0197770&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004850&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Feb 2000&#x00A0;&#x00A0;11:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0197182&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004892&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Feb 2000&#x00A0;&#x00A0;14:42&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0198085&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004882&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Feb 2000&#x00A0;&#x00A0;15:18&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0197834&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004746&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Feb 2000&#x00A0;&#x00A0;15:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0197905&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004821&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Feb 2000&#x00A0;&#x00A0;07:48&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0196251&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004843&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Feb 2000&#x00A0;&#x00A0;09:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0189505&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004843&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Feb 2000&#x00A0;&#x00A0;09:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0189505&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004861&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Feb 2000&#x00A0;&#x00A0;15:10&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0199227&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004760&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Feb 2000&#x00A0;&#x00A0;15:40&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0195009&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004859&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Feb 2000&#x00A0;&#x00A0;17:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0199325&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004920&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Feb 2000&#x00A0;&#x00A0;09:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0192864&#x00A0;&#x00A0;repl. by LCRT 39 - 44</LI></UL> <UL><LI>SOEK004900&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Feb 2000&#x00A0;&#x00A0;11:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0198515&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004922&#x00A0;&#x00A0; 11 Feb 2000&#x00A0;&#x00A0;08:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0199541&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004932&#x00A0;&#x00A0; 11 Feb 2000&#x00A0;&#x00A0;09:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0200316&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004940&#x00A0;&#x00A0; 14 Feb 2000&#x00A0;&#x00A0;16:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0200816&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004936&#x00A0;&#x00A0; 15 Feb 2000&#x00A0;&#x00A0;08:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDRP 0200706&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004952&#x00A0;&#x00A0; 15 Feb 2000&#x00A0;&#x00A0;14:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0197738&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004945&#x00A0;&#x00A0; 16 Feb 2000&#x00A0;&#x00A0;08:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0200957&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004646&#x00A0;&#x00A0; 16 Feb 2000&#x00A0;&#x00A0;09:03&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0098642&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004958&#x00A0;&#x00A0; 16 Feb 2000&#x00A0;&#x00A0;09:13&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0201122&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004938&#x00A0;&#x00A0; 16 Feb 2000&#x00A0;&#x00A0;10:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0200940&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004969&#x00A0;&#x00A0; 16 Feb 2000&#x00A0;&#x00A0;13:49&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0201309&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004886&#x00A0;&#x00A0; 16 Feb 2000&#x00A0;&#x00A0;15:18&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0201437&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004912&#x00A0;&#x00A0; 17 Feb 2000&#x00A0;&#x00A0;08:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0201282&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004971&#x00A0;&#x00A0; 17 Feb 2000&#x00A0;&#x00A0;09:57&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0201383&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004908&#x00A0;&#x00A0; 17 Feb 2000&#x00A0;&#x00A0;10:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0199746&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004967&#x00A0;&#x00A0; 18 Feb 2000&#x00A0;&#x00A0;10:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0201305&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004978&#x00A0;&#x00A0; 18 Feb 2000&#x00A0;&#x00A0;10:49&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0201913&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004986&#x00A0;&#x00A0; 21 Feb 2000&#x00A0;&#x00A0;15:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0202267&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004795&#x00A0;&#x00A0; 22 Feb 2000&#x00A0;&#x00A0;17:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0196306&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005002&#x00A0;&#x00A0; 22 Feb 2000&#x00A0;&#x00A0;17:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0193130&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005008&#x00A0;&#x00A0; 22 Feb 2000&#x00A0;&#x00A0;17:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0202626&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005018&#x00A0;&#x00A0; 24 Feb 2000&#x00A0;&#x00A0;19:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDR&#x00A0;&#x00A0;0203077&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005042&#x00A0;&#x00A0; 29 Feb 2000&#x00A0;&#x00A0;12:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0204005&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005022&#x00A0;&#x00A0;29 Feb 2000&#x00A0;&#x00A0;13:49&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261&#x00A0;&#x00A0;repl. by SOEK005836</LI></UL> <UL><LI>SOEK004896&#x00A0;&#x00A0; 29 Feb 2000&#x00A0;&#x00A0;14:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0198229&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005052&#x00A0;&#x00A0; 29 Feb 2000&#x00A0;&#x00A0;16:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0204158&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005065&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Mar 2000&#x00A0;&#x00A0;18:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0204830&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005063&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 Mar 2000&#x00A0;&#x00A0;09:31&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0204741&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005016&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 Mar 2000&#x00A0;&#x00A0;13:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0203283&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005071&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Mar 2000&#x00A0;&#x00A0;09:09&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0205175&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005044&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Mar 2000&#x00A0;&#x00A0;13:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0205561&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004332&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Mar 2000&#x00A0;&#x00A0;14:41&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0186505&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005081&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Mar 2000&#x00A0;&#x00A0;15:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0205759&#x00A0;&#x00A0; -</LI></UL> <UL><LI>&#x00A0;&#x00A0;&#x00A0;&#x00A0;!!!! CAUTION: Read SAPNet Note 210204 BEFORE applying LCP 38&#x00A0;&#x00A0;!!!!</LI></UL> <UL><LI>SAPKI40L31&#x00A0;&#x00A0; 31 Mar 2000&#x00A0;&#x00A0;11:26&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT31</LI></UL> <UL><LI>SAPKI40L32&#x00A0;&#x00A0; 31 Mar 2000&#x00A0;&#x00A0;11:27&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT32</LI></UL> <UL><LI>SAPKI40L33&#x00A0;&#x00A0; 31 Mar 2000&#x00A0;&#x00A0;11:28&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT33</LI></UL> <UL><LI>SAPKI40L35&#x00A0;&#x00A0; 31 Mar 2000&#x00A0;&#x00A0;11:29&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT35</LI></UL> <UL><LI>SAPKI40L36&#x00A0;&#x00A0; 31 Mar 2000&#x00A0;&#x00A0;11:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT36</LI></UL> <UL><LI>SAPKI40L37&#x00A0;&#x00A0; 31 Mar 2000&#x00A0;&#x00A0;11:33&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT37</LI></UL> <UL><LI>SAPKI40L38&#x00A0;&#x00A0; 31 Mar 2000&#x00A0;&#x00A0;11:41&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT38</LI></UL> <UL><LI>SOEK005074&#x00A0;&#x00A0; 10 Mar 2000&#x00A0;&#x00A0;08:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0205300&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005154&#x00A0;&#x00A0; 31 Mar 2000&#x00A0;&#x00A0;14:20&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0209689&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005394&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Apr 2000&#x00A0;&#x00A0;17:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 0211421&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005180&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Apr 2000&#x00A0;&#x00A0;09:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0207504&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005246&#x00A0;&#x00A0; 27 Mar 2000&#x00A0;&#x00A0;16:28&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0209999&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004813&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Apr 2000&#x00A0;&#x00A0;10:42&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0196054&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005250&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Apr 2000&#x00A0;&#x00A0;10:27&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0210030&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005420&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Apr 2000&#x00A0;&#x00A0;14:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 0213093&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005412&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Apr 2000&#x00A0;&#x00A0;16:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0212670&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005424&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Apr 2000&#x00A0;&#x00A0;16:49&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0213049&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005432&#x00A0;&#x00A0; 11 Apr 2000&#x00A0;&#x00A0;12:03&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 0213579&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005446&#x00A0;&#x00A0; 11 Apr 2000&#x00A0;&#x00A0;16:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0213939&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005416&#x00A0;&#x00A0; 11 Apr 2000&#x00A0;&#x00A0;08:27&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0213030&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005409&#x00A0;&#x00A0; 11 Apr 2000&#x00A0;&#x00A0;11:50&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0212492&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005398&#x00A0;&#x00A0; 12 Apr 2000&#x00A0;&#x00A0;16:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0213757&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005458&#x00A0;&#x00A0; 13 Apr 2000&#x00A0;&#x00A0;10:57&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 0214346&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005464&#x00A0;&#x00A0; 13 Apr 2000&#x00A0;&#x00A0;17:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0214666&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005011&#x00A0;&#x00A0; 14 Apr 2000&#x00A0;&#x00A0;09:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0215139&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005476&#x00A0;&#x00A0; 14 Apr 2000&#x00A0;&#x00A0;17:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 0214996&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005341&#x00A0;&#x00A0; 17 Apr 2000&#x00A0;&#x00A0;13:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0210687&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005096&#x00A0;&#x00A0; 17 Apr 2000&#x00A0;&#x00A0;13:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDRP 0206287&#x00A0;&#x00A0; X</LI></UL> <UL><LI>SOEK005422&#x00A0;&#x00A0;18 Apr 2000&#x00A0;&#x00A0;11:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0215608&#x00A0;&#x00A0;repl. by SOEK007540</LI></UL> <UL><LI>SOEK005470&#x00A0;&#x00A0; 18 Apr 2000&#x00A0;&#x00A0;15:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0214650&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005512&#x00A0;&#x00A0; 19 Apr 2000&#x00A0;&#x00A0;15:07&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0215522&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005494&#x00A0;&#x00A0; 19 Apr 2000&#x00A0;&#x00A0;15:12&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0215460&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005488&#x00A0;&#x00A0; 19 Apr 2000&#x00A0;&#x00A0;15:31&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0215633&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005501&#x00A0;&#x00A0; 19 Apr 2000&#x00A0;&#x00A0;16:26&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0215705&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005517&#x00A0;&#x00A0; 19 Apr 2000&#x00A0;&#x00A0;20:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 0216007&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005102&#x00A0;&#x00A0; 20 Apr 2000&#x00A0;&#x00A0;09:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0208587&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK005490&#x00A0;&#x00A0; 20 Apr 2000&#x00A0;&#x00A0;10:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0215636&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005506&#x00A0;&#x00A0; 20 Apr 2000&#x00A0;&#x00A0;10:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0216067&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005480&#x00A0;&#x00A0; 20 Apr 2000&#x00A0;&#x00A0;15:13&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0215068&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005530&#x00A0;&#x00A0; 20 Apr 2000&#x00A0;&#x00A0;15:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0216249&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005521&#x00A0;&#x00A0; 25 Apr 2000&#x00A0;&#x00A0;12:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0216045&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005532&#x00A0;&#x00A0; 25 Apr 2000&#x00A0;&#x00A0;12:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0216413&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005524&#x00A0;&#x00A0; 25 Apr 2000&#x00A0;&#x00A0;13:02&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0216079&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005544&#x00A0;&#x00A0; 25 Apr 2000&#x00A0;&#x00A0;14:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 0216522&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005556&#x00A0;&#x00A0; 26 Apr 2000&#x00A0;&#x00A0;17:31&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0216894&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005468&#x00A0;&#x00A0; 27 Apr 2000&#x00A0;&#x00A0;10:34&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0214794&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK005564&#x00A0;&#x00A0; 27 Apr 2000&#x00A0;&#x00A0;10:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0217126&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005442&#x00A0;&#x00A0; 28 Apr 2000&#x00A0;&#x00A0;10:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0213813&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK005168&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 May 2000&#x00A0;&#x00A0;12:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDRP 0215984&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005301&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 May 2000&#x00A0;&#x00A0;13:02&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0211071&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005568&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 May 2000&#x00A0;&#x00A0;14:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0300199&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005554&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 May 2000&#x00A0;&#x00A0;11:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0300406&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005558&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 May 2000&#x00A0;&#x00A0;11:13&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0300150&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005589&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 May 2000&#x00A0;&#x00A0;12:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0300374&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005585&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 May 2000&#x00A0;&#x00A0;13:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0300298&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005587&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 May 2000&#x00A0;&#x00A0;15:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0300629&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005616&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 May 2000&#x00A0;&#x00A0;14:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 0300151&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005620&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 May 2000&#x00A0;&#x00A0;14:57&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0300849&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004904&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 May 2000&#x00A0;&#x00A0;15:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0198627&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005583&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 May 2000&#x00A0;&#x00A0;15:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0301014&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005606&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 May 2000&#x00A0;&#x00A0;08:33&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0300869&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005604&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 May 2000&#x00A0;&#x00A0;14:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0300650&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005574&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 May 2000&#x00A0;&#x00A0;17:04&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0217379&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005602&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 May 2000&#x00A0;&#x00A0;17:42&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0300647&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005641&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 May 2000&#x00A0;&#x00A0;06:44&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0301601&#x00A0;&#x00A0; X</LI></UL> <UL><LI>SOEK005618&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 May 2000&#x00A0;&#x00A0;14:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MRN&#x00A0;&#x00A0;0300784&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005657&#x00A0;&#x00A0; 11 May 2000&#x00A0;&#x00A0;09:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0302327&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005659&#x00A0;&#x00A0; 11 May 2000&#x00A0;&#x00A0;09:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0302311&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005217&#x00A0;&#x00A0; 11 May 2000&#x00A0;&#x00A0;10:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0301021&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005677&#x00A0;&#x00A0; 11 May 2000&#x00A0;&#x00A0;11:47&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0302746&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005486&#x00A0;&#x00A0; 11 May 2000&#x00A0;&#x00A0;20:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0303128&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005696&#x00A0;&#x00A0; 12 May 2000&#x00A0;&#x00A0;13:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0303089&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005698&#x00A0;&#x00A0; 15 May 2000&#x00A0;&#x00A0;11:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0303220&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005636&#x00A0;&#x00A0; 15 May 2000&#x00A0;&#x00A0;11:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0301385&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005622&#x00A0;&#x00A0; 15 May 2000&#x00A0;&#x00A0;15:19&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0303471&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005611&#x00A0;&#x00A0; 16 May 2000&#x00A0;&#x00A0;14:51&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0300744&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005715&#x00A0;&#x00A0; 16 May 2000&#x00A0;&#x00A0;17:40&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0303827&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK005717&#x00A0;&#x00A0; 17 May 2000&#x00A0;&#x00A0;08:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0303797&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005745&#x00A0;&#x00A0; 19 May 2000&#x00A0;&#x00A0;06:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0304354&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005770&#x00A0;&#x00A0; 22 May 2000&#x00A0;&#x00A0;11:40&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0305064&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005753&#x00A0;&#x00A0; 24 May 2000&#x00A0;&#x00A0;09:47&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0304626&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK005778&#x00A0;&#x00A0; 24 May 2000&#x00A0;&#x00A0;10:04&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0305891&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005024&#x00A0;&#x00A0; 24 May 2000&#x00A0;&#x00A0;10:50&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0203605&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005776&#x00A0;&#x00A0; 24 May 2000&#x00A0;&#x00A0;11:13&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0305704&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005782&#x00A0;&#x00A0; 25 May 2000&#x00A0;&#x00A0;11:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0305918&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005345&#x00A0;&#x00A0; 30 May 2000&#x00A0;&#x00A0;15:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0306286&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK005864&#x00A0;&#x00A0; 30 May 2000&#x00A0;&#x00A0;16:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0306286&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK005821&#x00A0;&#x00A0; 31 May 2000&#x00A0;&#x00A0;11:26&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0306295&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005875&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Jun 2000&#x00A0;&#x00A0;10:14&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0307949&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005877&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Jun 2000&#x00A0;&#x00A0;12:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0308026&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005885&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Jun 2000&#x00A0;&#x00A0;08:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0308460&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005893&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Jun 2000&#x00A0;&#x00A0;09:18&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0308633&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005889&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Jun 2000&#x00A0;&#x00A0;11:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0308544&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005905&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Jun 2000&#x00A0;&#x00A0;11:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0309329&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005905&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Jun 2000&#x00A0;&#x00A0;11:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0309329&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005836&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Jun 2000&#x00A0;&#x00A0;14:02&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261&#x00A0;&#x00A0;repl. by SOEK005864</LI></UL> <UL><LI>SOEK005909&#x00A0;&#x00A0; 13 Jun 2000&#x00A0;&#x00A0;09:07&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0309618&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005907&#x00A0;&#x00A0; 13 Jun 2000&#x00A0;&#x00A0;11:10&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0168468&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005762&#x00A0;&#x00A0; 13 Jun 2000&#x00A0;&#x00A0;15:51&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0306761&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK005802&#x00A0;&#x00A0; 13 Jun 2000&#x00A0;&#x00A0;15:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0306559&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005887&#x00A0;&#x00A0; 14 Jun 2000&#x00A0;&#x00A0;09:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0308868&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005918&#x00A0;&#x00A0; 14 Jun 2000&#x00A0;&#x00A0;09:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0310043&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005869&#x00A0;&#x00A0; 14 Jun 2000&#x00A0;&#x00A0;10:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0307732&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005926&#x00A0;&#x00A0; 14 Jun 2000&#x00A0;&#x00A0;17:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0310158&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005928&#x00A0;&#x00A0; 14 Jun 2000&#x00A0;&#x00A0;16:07&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0310287&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005930&#x00A0;&#x00A0; 14 Jun 2000&#x00A0;&#x00A0;17:27&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0310095&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005940&#x00A0;&#x00A0; 15 Jun 2000&#x00A0;&#x00A0;09:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0310427&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005942&#x00A0;&#x00A0; 15 Jun 2000&#x00A0;&#x00A0;10:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0309533&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005911&#x00A0;&#x00A0; 16 Jun 2000&#x00A0;&#x00A0;15:12&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0309707&#x00A0;&#x00A0; -</LI></UL><UL><LI>&#x00A0;&#x00A0;&#x00A0;&#x00A0;!!!! CAUTION: Read SAPNet Note 317518 BEFORE applying LCP 38&#x00A0;&#x00A0;!!!!</LI></UL> <UL><LI>SAPKI40L39&#x00A0;&#x00A0; 13 Jul 2000&#x00A0;&#x00A0;16:41&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT39</LI></UL> <UL><LI>SAPKI40L40&#x00A0;&#x00A0; 13 Jul 2000&#x00A0;&#x00A0;16:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT40</LI></UL> <UL><LI>SAPKI40L41&#x00A0;&#x00A0; 13 Jul 2000&#x00A0;&#x00A0;16:44&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT41</LI></UL> <UL><LI>SAPKI40L42&#x00A0;&#x00A0; 13 Jul 2000&#x00A0;&#x00A0;16:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT42</LI></UL> <UL><LI>SAPKI40L44&#x00A0;&#x00A0; 13 Jul 2000&#x00A0;&#x00A0;16:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT44</LI></UL> <UL><LI>SOEK006280&#x00A0;&#x00A0; 14 Jul 2000&#x00A0;&#x00A0;08:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HIM&#x00A0;&#x00A0;0317717&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005952&#x00A0;&#x00A0; 14 Jul 2000&#x00A0;&#x00A0;09:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0310631&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005959&#x00A0;&#x00A0; 14 Jul 2000&#x00A0;&#x00A0;09:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0310851&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006233&#x00A0;&#x00A0; 14 Jul 2000&#x00A0;&#x00A0;09:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0316453&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006239&#x00A0;&#x00A0; 14 Jul 2000&#x00A0;&#x00A0;10:10&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0316839&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005956&#x00A0;&#x00A0; 14 Jul 2000&#x00A0;&#x00A0;10:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0310788&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK006237&#x00A0;&#x00A0; 14 Jul 2000&#x00A0;&#x00A0;10:16&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDR&#x00A0;&#x00A0;0316998&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006243&#x00A0;&#x00A0; 14 Jul 2000&#x00A0;&#x00A0;10:41&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0316891&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK006201&#x00A0;&#x00A0; 14 Jul 2000&#x00A0;&#x00A0;11:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0315433&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006159&#x00A0;&#x00A0; 14 Jul 2000&#x00A0;&#x00A0;14:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0314778&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006148&#x00A0;&#x00A0; 14 Jul 2000&#x00A0;&#x00A0;16:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK006230&#x00A0;&#x00A0; 14 Jul 2000&#x00A0;&#x00A0;17:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0316218&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006228&#x00A0;&#x00A0; 17 Jul 2000&#x00A0;&#x00A0;07:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDR&#x00A0;&#x00A0;0316386&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006251&#x00A0;&#x00A0; 17 Jul 2000&#x00A0;&#x00A0;09:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0316708&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006139&#x00A0;&#x00A0; 17 Jul 2000&#x00A0;&#x00A0;11:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0314232&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006219&#x00A0;&#x00A0; 17 Jul 2000&#x00A0;&#x00A0;11:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0316516&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006157&#x00A0;&#x00A0; 17 Jul 2000&#x00A0;&#x00A0;12:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0318040&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006327&#x00A0;&#x00A0; 18 Jul 2000&#x00A0;&#x00A0;15:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0318434&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006331&#x00A0;&#x00A0; 18 Jul 2000&#x00A0;&#x00A0;17:09&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0318436&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006247&#x00A0;&#x00A0; 19 Jul 2000&#x00A0;&#x00A0;09:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0316883&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006291&#x00A0;&#x00A0; 19 Jul 2000&#x00A0;&#x00A0;10:04&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0318750&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006363&#x00A0;&#x00A0; 19 Jul 2000&#x00A0;&#x00A0;12:14&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0318688&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006325&#x00A0;&#x00A0; 19 Jul 2000&#x00A0;&#x00A0;14:04&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0318351&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006367&#x00A0;&#x00A0; 19 Jul 2000&#x00A0;&#x00A0;16:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDR&#x00A0;&#x00A0;0318966&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006361&#x00A0;&#x00A0; 19 Jul 2000&#x00A0;&#x00A0;17:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0318652&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006293&#x00A0;&#x00A0; 20 Jul 2000&#x00A0;&#x00A0;13:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0318839&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006245&#x00A0;&#x00A0; 21 Jul 2000&#x00A0;&#x00A0;08:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0316902&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006369&#x00A0;&#x00A0; 21 Jul 2000&#x00A0;&#x00A0;13:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0317951&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006284&#x00A0;&#x00A0; 21 Jul 2000&#x00A0;&#x00A0;13:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0317019&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006164&#x00A0;&#x00A0; 21 Jul 2000&#x00A0;&#x00A0;15:26&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0314967&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006213&#x00A0;&#x00A0; 21 Jul 2000&#x00A0;&#x00A0;16:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0315730&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006329&#x00A0;&#x00A0; 21 Jul 2000&#x00A0;&#x00A0;16:28&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0107344&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005913&#x00A0;&#x00A0; 24 Jul 2000&#x00A0;&#x00A0;10:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0309717&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005944&#x00A0;&#x00A0; 24 Jul 2000&#x00A0;&#x00A0;11:10&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0310523&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006383&#x00A0;&#x00A0; 24 Jul 2000&#x00A0;&#x00A0;11:16&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0319796&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006286&#x00A0;&#x00A0; 24 Jul 2000&#x00A0;&#x00A0;14:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0319760&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006333&#x00A0;&#x00A0; 24 Jul 2000&#x00A0;&#x00A0;15:57&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0318560&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006372&#x00A0;&#x00A0; 25 Jul 2000&#x00A0;&#x00A0;08:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0319203&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003665&#x00A0;&#x00A0; 25 Jul 2000&#x00A0;&#x00A0;11:29&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0180596&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006389&#x00A0;&#x00A0; 25 Jul 2000&#x00A0;&#x00A0;15:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0320192&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006387&#x00A0;&#x00A0; 26 Jul 2000&#x00A0;&#x00A0;13:44&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0320098&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006223&#x00A0;&#x00A0; 26 Jul 2000&#x00A0;&#x00A0;15:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0320496&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006401&#x00A0;&#x00A0; 28 Jul 2000&#x00A0;&#x00A0;08:20&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0321127&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004683&#x00A0;&#x00A0; 28 Jul 2000&#x00A0;&#x00A0;09:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0184259&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006221&#x00A0;&#x00A0; 28 Jul 2000&#x00A0;&#x00A0;10:16&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDRP 0321135&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006376&#x00A0;&#x00A0; 28 Jul 2000&#x00A0;&#x00A0;11:18&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0318202&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006393&#x00A0;&#x00A0; 28 Jul 2000&#x00A0;&#x00A0;15:40&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0320810&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006145&#x00A0;&#x00A0; 31 Jul 2000&#x00A0;&#x00A0;11:27&#x00A0;&#x00A0;&#x00A0;&#x00A0;MAP/MCOE 0314379&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006413&#x00A0;&#x00A0; 31 Jul 2000&#x00A0;&#x00A0;16:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0321836&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006323&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Aug 2000&#x00A0;&#x00A0;14:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0318349&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006456&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 Aug 2000&#x00A0;&#x00A0;13:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0316979&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006397&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 Aug 2000&#x00A0;&#x00A0;16:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0322765&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006452&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Aug 2000&#x00A0;&#x00A0;09:27&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0322646&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006405&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Aug 2000&#x00A0;&#x00A0;09:50&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0321161&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006486&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Aug 2000&#x00A0;&#x00A0;14:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0322720&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006450&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Aug 2000&#x00A0;&#x00A0;15:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0323387&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006515&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Aug 2000&#x00A0;&#x00A0;15:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;TAS&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0323181&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006517&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Aug 2000&#x00A0;&#x00A0;09:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;TAS&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0323129&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006440&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Aug 2000&#x00A0;&#x00A0;11:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0321691&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006520&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Aug 2000&#x00A0;&#x00A0;15:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0323761&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006530&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Aug 2000&#x00A0;&#x00A0;16:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0324180&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006538&#x00A0;&#x00A0; 10 Aug 2000&#x00A0;&#x00A0;09:40&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MRN&#x00A0;&#x00A0;0324334&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006484&#x00A0;&#x00A0; 10 Aug 2000&#x00A0;&#x00A0;17:03&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0322262&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006547&#x00A0;&#x00A0; 15 Aug 2000&#x00A0;&#x00A0;08:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MRN&#x00A0;&#x00A0;0324964&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK006555&#x00A0;&#x00A0; 15 Aug 2000&#x00A0;&#x00A0;13:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0325412&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006534&#x00A0;&#x00A0;15 Aug 2000&#x00A0;&#x00A0;13:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;BC&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0322879&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006536&#x00A0;&#x00A0; 16 Aug 2000&#x00A0;&#x00A0;08:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MRN&#x00A0;&#x00A0;0189339&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006551&#x00A0;&#x00A0; 16 Aug 2000&#x00A0;&#x00A0;08:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0325472&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK006512&#x00A0;&#x00A0; 16 Aug 2000&#x00A0;&#x00A0;13:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0325179&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006564&#x00A0;&#x00A0;18 Aug 2000&#x00A0;&#x00A0;12:10&#x00A0;&#x00A0;&#x00A0;&#x00A0;TAS&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0326517&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006553&#x00A0;&#x00A0; 18 Aug 2000&#x00A0;&#x00A0;14:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0325399&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006562&#x00A0;&#x00A0; 18 Aug 2000&#x00A0;&#x00A0;14:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0326623&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006576&#x00A0;&#x00A0; 21 Aug 2000&#x00A0;&#x00A0;11:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0326861&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006558&#x00A0;&#x00A0; 22 Aug 2000&#x00A0;&#x00A0;10:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0327413&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006558&#x00A0;&#x00A0; 22 Aug 2000&#x00A0;&#x00A0;10:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0327413&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006578&#x00A0;&#x00A0; 22 Aug 2000&#x00A0;&#x00A0;14:06&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDRP 0327242&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006580&#x00A0;&#x00A0; 22 Aug 2000&#x00A0;&#x00A0;14:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0326895&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006591&#x00A0;&#x00A0; 25 Aug 2000&#x00A0;&#x00A0;10:10&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0327863&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006619&#x00A0;&#x00A0; 28 Aug 2000&#x00A0;&#x00A0;09:03&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0328151&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006621&#x00A0;&#x00A0;29 Aug 2000&#x00A0;&#x00A0;15:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;TAS&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0328266&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006626&#x00A0;&#x00A0; 30 Aug 2000&#x00A0;&#x00A0;09:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDRP 0329200&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006143&#x00A0;&#x00A0; 30 Aug 2000&#x00A0;&#x00A0;15:42&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0329203&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006628&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Sep 2000&#x00A0;&#x00A0;12:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0329466&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006549&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Sep 2000&#x00A0;&#x00A0;12:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0325052&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006634&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Sep 2000&#x00A0;&#x00A0;11:07&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0330214&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006632&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Sep 2000&#x00A0;&#x00A0;15:12&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0330194&#x00A0;&#x00A0; X</LI></UL> <UL><LI>SOEK006645&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Sep 2000&#x00A0;&#x00A0;12:20&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0111268&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006623&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Sep 2000&#x00A0;&#x00A0;11:20&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0328204&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006643&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Sep 2000&#x00A0;&#x00A0;13:33&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0098534&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006584&#x00A0;&#x00A0; 11 Sep 2000&#x00A0;&#x00A0;11:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0327364&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006654&#x00A0;&#x00A0; 11 Sep 2000&#x00A0;&#x00A0;12:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0331952&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006648&#x00A0;&#x00A0; 12 Sep 2000&#x00A0;&#x00A0;11:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0331304&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006641&#x00A0;&#x00A0; 12 Sep 2000&#x00A0;&#x00A0;11:51&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0332287&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006658&#x00A0;&#x00A0; 12 Sep 2000&#x00A0;&#x00A0;12:48&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0332092&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006582&#x00A0;&#x00A0; 13 Sep 2000&#x00A0;&#x00A0;19:09&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0328086&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006691&#x00A0;&#x00A0; 14 Sep 2000&#x00A0;&#x00A0;10:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0332989&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006652&#x00A0;&#x00A0; 15 Sep 2000&#x00A0;&#x00A0;08:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0332993&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006697&#x00A0;&#x00A0; 20 Sep 2000&#x00A0;&#x00A0;15:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0333978&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006689&#x00A0;&#x00A0; 20 Sep 2000&#x00A0;&#x00A0;16:44&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0332960&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006717&#x00A0;&#x00A0; 21 Sep 2000&#x00A0;&#x00A0;16:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0334795&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006710&#x00A0;&#x00A0; 22 Sep 2000&#x00A0;&#x00A0;09:27&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0334723&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006725&#x00A0;&#x00A0; 25 Sep 2000&#x00A0;&#x00A0;13:33&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0335256&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005997&#x00A0;&#x00A0; 25 Sep 2000&#x00A0;&#x00A0;15:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0312369&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006701&#x00A0;&#x00A0; 25 Sep 2000&#x00A0;&#x00A0;16:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0334462&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006727&#x00A0;&#x00A0; 26 Sep 2000&#x00A0;&#x00A0;12:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDRP 0334920&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006732&#x00A0;&#x00A0; 27 Sep 2000&#x00A0;&#x00A0;14:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0335955&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006734&#x00A0;&#x00A0; 28 Sep 2000&#x00A0;&#x00A0;13:07&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0336030&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006572&#x00A0;&#x00A0; 28 Sep 2000&#x00A0;&#x00A0;14:06&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0336373&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006745&#x00A0;&#x00A0; 28 Sep 2000&#x00A0;&#x00A0;14:09&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0336103&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006751&#x00A0;&#x00A0; 28 Sep 2000&#x00A0;&#x00A0;14:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0336440&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006751&#x00A0;&#x00A0; 28 Sep 2000&#x00A0;&#x00A0;14:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0336440&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006756&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Oct 2000&#x00A0;&#x00A0;07:49&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0336109&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006544&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Oct 2000&#x00A0;&#x00A0;11:27&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0336950&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006754&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Oct 2000&#x00A0;&#x00A0;11:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0336664&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006777&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Oct 2000&#x00A0;&#x00A0;08:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MRN&#x00A0;&#x00A0;0338202&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006781&#x00A0;&#x00A0; 11 Oct 2000&#x00A0;&#x00A0;10:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0337217&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006788&#x00A0;&#x00A0; 12 Oct 2000&#x00A0;&#x00A0;12:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0339524&#x00A0;&#x00A0; -</LI></UL><UL><LI>SAPKI40L45&#x00A0;&#x00A0; 31 Oct 2000&#x00A0;&#x00A0;12:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT45</LI></UL> <UL><LI>SAPKI40L46&#x00A0;&#x00A0; 31 Oct 2000&#x00A0;&#x00A0;15:34&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT46</LI></UL> <UL><LI>SAPKI40L48&#x00A0;&#x00A0; 31 Oct 2000&#x00A0;&#x00A0;15:34&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT48</LI></UL> <UL><LI>SAPKI40L49&#x00A0;&#x00A0; 31 Oct 2000&#x00A0;&#x00A0;15:33&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT49</LI></UL> <UL><LI>SAPKI40L50&#x00A0;&#x00A0; 31 Oct 2000&#x00A0;&#x00A0;12:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT50</LI></UL> <UL><LI>SAPKI40L51&#x00A0;&#x00A0; 31 Oct 2000&#x00A0;&#x00A0;12:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT51</LI></UL> <UL><LI>SAPKI40L52&#x00A0;&#x00A0; 31 Oct 2000&#x00A0;&#x00A0;12:57&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT52</LI></UL> <UL><LI>SAPKI40L53&#x00A0;&#x00A0; 31 Oct 2000&#x00A0;&#x00A0;12:59&#x00A0;&#x00A0;&#x00A0;&#x00A0;LCRT53</LI></UL> <p><br />**********************************************************************<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&gt;&gt;&gt; I M P O R T A N T &lt;&lt;&lt;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; From LCP 54 onwards, LCPs contain only HR modifications.&#x00A0;&#x00A0;&#x00A0;&#x00A0; *<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Please read Note 334872 for further details.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Due to the fact that IS-OIL and HR are de-coupled this&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;note will no longer be maintained. Please continue&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;maintainence of your systems as described in Notes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;145850 and 334872.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*<br />**********************************************************************</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-UPG-ADDON (Upgrade Add-On Components)"}, {"Key": "Transaction codes", "Value": "SUCH"}, {"Key": "Transaction codes", "Value": "SPAM"}, {"Key": "Responsible                                                                                         ", "Value": "C3239103"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000145854/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000145854/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000145854/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000145854/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000145854/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000145854/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000145854/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000145854/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000145854/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "98642", "RefComponent": "IS-OIL-BC", "RefTitle": "Order of IS-OIL notes 2.0d & 1.0d on R/3 3.1H (SP)", "RefUrl": "/notes/98642"}, {"RefNumber": "98534", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Abend O1 544 in TD Delivery Confirmation", "RefUrl": "/notes/98534"}, {"RefNumber": "93571", "RefComponent": "IS-OIL", "RefTitle": "Sequence of corrections - IS-Oil / IS-MINE Policy", "RefUrl": "/notes/93571"}, {"RefNumber": "86241", "RefComponent": "PY", "RefTitle": "Legal Change Patches / Support Packages for HR", "RefUrl": "/notes/86241"}, {"RefNumber": "77407", "RefComponent": "IS-OIL-BC", "RefTitle": "CRTs for IS-Oil", "RefUrl": "/notes/77407"}, {"RefNumber": "67261", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Reports to analyze and correct stock qties in IS-OIL systems", "RefUrl": "/notes/67261"}, {"RefNumber": "53136", "RefComponent": "IS-OIL-BC", "RefTitle": "Support Packages and IS-Oil / IS-MINE / IS-CWM - information", "RefUrl": "/notes/53136"}, {"RefNumber": "528008", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "MOIJNF01_COPY_REF_NOM_LINES", "RefUrl": "/notes/528008"}, {"RefNumber": "527924", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Customizing the message type for TD message O9(747)", "RefUrl": "/notes/527924"}, {"RefNumber": "525712", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External Details Button rises error M7 001", "RefUrl": "/notes/525712"}, {"RefNumber": "524350", "RefComponent": "IS-OIL", "RefTitle": "Balancing Workplace IMG Activity Documentation", "RefUrl": "/notes/524350"}, {"RefNumber": "522365", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "TAS incompletion log appears for a non TAS relevant order", "RefUrl": "/notes/522365"}, {"RefNumber": "522282", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "ERS creates inconsistent fee table entries (OIANF/OIAFE)", "RefUrl": "/notes/522282"}, {"RefNumber": "521389", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/521389"}, {"RefNumber": "519739", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "(O4TE) Ticket Create -  showing closed noms", "RefUrl": "/notes/519739"}, {"RefNumber": "519282", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IBS OIL&GAS LOCALIZATION BRAZIL LOST IS-OIL DATA DURING IV", "RefUrl": "/notes/519282"}, {"RefNumber": "519268", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IBS OIL&GAS LOCALIZATION BRAZIL THIRD NOTA FISCAL", "RefUrl": "/notes/519268"}, {"RefNumber": "519259", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Customizing the message type for TD message class O9(078).", "RefUrl": "/notes/519259"}, {"RefNumber": "519228", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IBS OIL&GAS LOCALIZATION BRAZIL ST Calculation Interstate", "RefUrl": "/notes/519228"}, {"RefNumber": "518883", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong proposal of Delivery Costs for Weight Based Products", "RefUrl": "/notes/518883"}, {"RefNumber": "518617", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Problem with OILNOM01", "RefUrl": "/notes/518617"}, {"RefNumber": "518541", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Material valuation type not copied to settlement rule object", "RefUrl": "/notes/518541"}, {"RefNumber": "517713", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Corrections to FM OIK_TAS_DATA_MAINTAIN", "RefUrl": "/notes/517713"}, {"RefNumber": "517617", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Exclude Event date indicator functionality is not working", "RefUrl": "/notes/517617"}, {"RefNumber": "517380", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Coll. Note - MCHA and MCHB checks do not allow load.conf.", "RefUrl": "/notes/517380"}, {"RefNumber": "517333", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/517333"}, {"RefNumber": "516995", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Buffer overflow on OIKPEXORD", "RefUrl": "/notes/516995"}, {"RefNumber": "514381", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "E1OILBH-SHPPPT (Transportation planning point) not filled", "RefUrl": "/notes/514381"}, {"RefNumber": "514195", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/514195"}, {"RefNumber": "513542", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "OIL: O4NC Event Log-not available in create mode for default", "RefUrl": "/notes/513542"}, {"RefNumber": "513514", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Missing export parameter in Function OII_OTWS_GET_REF_SET", "RefUrl": "/notes/513514"}, {"RefNumber": "512517", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "OIL: O4NV  Need to change Shipper due is diplay only.", "RefUrl": "/notes/512517"}, {"RefNumber": "511255", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect valuation for exchange receipts", "RefUrl": "/notes/511255"}, {"RefNumber": "510675", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "OIL: Rename functionality changes entries in database", "RefUrl": "/notes/510675"}, {"RefNumber": "510529", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "To update VBKD-OIT<PERSON>LE when VBKD-INCO1 is changed.", "RefUrl": "/notes/510529"}, {"RefNumber": "508909", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External details fields not getting updated in LIKP", "RefUrl": "/notes/508909"}, {"RefNumber": "508655", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "OIL: With reference to note n°487703 - 2", "RefUrl": "/notes/508655"}, {"RefNumber": "508499", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD-O4H1/STO enabling new batch creation in receiving plant", "RefUrl": "/notes/508499"}, {"RefNumber": "508312", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Consideration for unlimited indicator LIPS-UEBTK in Delivery", "RefUrl": "/notes/508312"}, {"RefNumber": "508237", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Error message O1 033 at changing contract QS header", "RefUrl": "/notes/508237"}, {"RefNumber": "508153", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Billing index update problems", "RefUrl": "/notes/508153"}, {"RefNumber": "508135", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "TIMEOUT Error while reading the quotations", "RefUrl": "/notes/508135"}, {"RefNumber": "508109", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "O4F1 short dump in Document selection F4 due to W message", "RefUrl": "/notes/508109"}, {"RefNumber": "507291", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Quantity unit in RMCSS003", "RefUrl": "/notes/507291"}, {"RefNumber": "505973", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Invoice getting blocked on ERS or Invoice Verification.", "RefUrl": "/notes/505973"}, {"RefNumber": "504889", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong delivery costs proposed for invoice verification", "RefUrl": "/notes/504889"}, {"RefNumber": "504601", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Vehicle status change from 6 to 5 in O4L4.", "RefUrl": "/notes/504601"}, {"RefNumber": "503839", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "OIL: Rename function in O4NC or O4NV", "RefUrl": "/notes/503839"}, {"RefNumber": "503655", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "OIL:O4NC Event Log- not available in create mode for defaul", "RefUrl": "/notes/503655"}, {"RefNumber": "503197", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "D-SCM-EX:Call off qty > schedule qty", "RefUrl": "/notes/503197"}, {"RefNumber": "502650", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Already cleared fees shouldn't be displayed", "RefUrl": "/notes/502650"}, {"RefNumber": "502622", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Message no. OB 007 when calling off from purchase contract", "RefUrl": "/notes/502622"}, {"RefNumber": "500915", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fee copy rules cannot be maintained for SD documents", "RefUrl": "/notes/500915"}, {"RefNumber": "500499", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Bad performance of ROIAMMA3/ROIAMMAT if no documents found", "RefUrl": "/notes/500499"}, {"RefNumber": "500411", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Split invoicing adds up the invoiced quantity.", "RefUrl": "/notes/500411"}, {"RefNumber": "500211", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Problems in OIAFE with postings different years (LIV)", "RefUrl": "/notes/500211"}, {"RefNumber": "498507", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Status handling in TD-Scheduling for zero quantity items", "RefUrl": "/notes/498507"}, {"RefNumber": "498506", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "O4D2 batch input wrong exclusion of FCODE DELE", "RefUrl": "/notes/498506"}, {"RefNumber": "498128", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Deletion of SD delivery through TPI Interface or Txn O4PO", "RefUrl": "/notes/498128"}, {"RefNumber": "498094", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: VF01 using different curencies E-KE 476", "RefUrl": "/notes/498094"}, {"RefNumber": "498005", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Material Group in Load ID determination", "RefUrl": "/notes/498005"}, {"RefNumber": "497803", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect VKDFS entry proposed in diff. invoice cancelation", "RefUrl": "/notes/497803"}, {"RefNumber": "495893", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Using FCODE OIDX with a SD doc. cat. <> C gives no error", "RefUrl": "/notes/495893"}, {"RefNumber": "495860", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Entry of text id in configuration of TPI interface", "RefUrl": "/notes/495860"}, {"RefNumber": "495547", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "BW, FI-SL, CO-PA and SIS quantities missing in SD invoices", "RefUrl": "/notes/495547"}, {"RefNumber": "494571", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "F&A tables not adjusted properly for multiple F&A conditions", "RefUrl": "/notes/494571"}, {"RefNumber": "492894", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "PO history incorrect after GR & IR for mult. acct assg", "RefUrl": "/notes/492894"}, {"RefNumber": "492265", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Netting fails with O1 546 for cross-company code LIVs", "RefUrl": "/notes/492265"}, {"RefNumber": "492151", "RefComponent": "IS-OIL", "RefTitle": "VF04: Nota fiscal simulation does not consider BOM's", "RefUrl": "/notes/492151"}, {"RefNumber": "491979", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Inactive F&A condition results in incorrect billing due list", "RefUrl": "/notes/491979"}, {"RefNumber": "490801", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "O3AB: Fee repricing causes wrong formula description", "RefUrl": "/notes/490801"}, {"RefNumber": "490648", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Dumps DYNPRO_SEND_IN_BACKGROUND using ROIKPIPR", "RefUrl": "/notes/490648"}, {"RefNumber": "489894", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MIRO Invoice Repricing uses incorrect pricing date", "RefUrl": "/notes/489894"}, {"RefNumber": "489034", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Set Screen error while creating sales order from a contract", "RefUrl": "/notes/489034"}, {"RefNumber": "488559", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Netting proposal not selecting all documents", "RefUrl": "/notes/488559"}, {"RefNumber": "488493", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MRRS is dumping due to fiscal year issue", "RefUrl": "/notes/488493"}, {"RefNumber": "488197", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Already cleared fees are proposed during MR01", "RefUrl": "/notes/488197"}, {"RefNumber": "488163", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "One OIGSM; Cancel ASTM; Batch in 851", "RefUrl": "/notes/488163"}, {"RefNumber": "487973", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Performance problem in material master - Oil view", "RefUrl": "/notes/487973"}, {"RefNumber": "487370", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Short dump at O4G1-Balance Loading after loading correction", "RefUrl": "/notes/487370"}, {"RefNumber": "487328", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "UOM conversion not drawn from the MM contract or info record", "RefUrl": "/notes/487328"}, {"RefNumber": "487243", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Update of MBEW during gain/loss does not occur", "RefUrl": "/notes/487243"}, {"RefNumber": "486504", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "S067 updated incorrectly during billing", "RefUrl": "/notes/486504"}, {"RefNumber": "486076", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Display accounting doc.: weird text on push button", "RefUrl": "/notes/486076"}, {"RefNumber": "485940", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Performance Improvements for O3A7 with Posting Date", "RefUrl": "/notes/485940"}, {"RefNumber": "485680", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect Values in Exchange Statement with Credit Memos", "RefUrl": "/notes/485680"}, {"RefNumber": "484852", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Incorrect error in Subcontracting Subsequent Adjustment", "RefUrl": "/notes/484852"}, {"RefNumber": "484461", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Stock transfer order checking issuing valuation", "RefUrl": "/notes/484461"}, {"RefNumber": "483461", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "LIA item amount changes when using some currencies", "RefUrl": "/notes/483461"}, {"RefNumber": "483325", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Far East license tracking and transfer movements", "RefUrl": "/notes/483325"}, {"RefNumber": "482457", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "OIEXGNUM lost in table VKDFS in billing due list run", "RefUrl": "/notes/482457"}, {"RefNumber": "482323", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong quantity in the QS after rejecting a sales order", "RefUrl": "/notes/482323"}, {"RefNumber": "482204", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect entries in M_VMCFA for differential invoices", "RefUrl": "/notes/482204"}, {"RefNumber": "481675", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Dlvy qty changed;Rounding;VBRP-CHARG;O9 340;QCI temperat.", "RefUrl": "/notes/481675"}, {"RefNumber": "481379", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "LIV ERS not create fee credit memo for prior year (part2)", "RefUrl": "/notes/481379"}, {"RefNumber": "480639", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Incorrect stock qty/value in material master", "RefUrl": "/notes/480639"}, {"RefNumber": "460234", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Excise License Validiy Dates", "RefUrl": "/notes/460234"}, {"RefNumber": "459951", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Autom. compartment allocation before exchange assignment", "RefUrl": "/notes/459951"}, {"RefNumber": "458906", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 66-68 incl. 4.0B", "RefUrl": "/notes/458906"}, {"RefNumber": "458705", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: VPRS Re-Determination when non TDP material", "RefUrl": "/notes/458705"}, {"RefNumber": "458242", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "ALE for Customer Master / IS/Oil Data (OILDEB)", "RefUrl": "/notes/458242"}, {"RefNumber": "458144", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Selection lost on scroll in Exchange contract copy", "RefUrl": "/notes/458144"}, {"RefNumber": "458055", "RefComponent": "IS-OIL-DS", "RefTitle": "Checks for IS-Oil fields in access sequence", "RefUrl": "/notes/458055"}, {"RefNumber": "455631", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "O3B3 Exg doesn't get sender address", "RefUrl": "/notes/455631"}, {"RefNumber": "455317", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Wrong cursor position in the OIL 40B SO overview screen", "RefUrl": "/notes/455317"}, {"RefNumber": "455166", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Rounding 2step;Abend MSEGO2;Don't change temp;Wrong sign G/L", "RefUrl": "/notes/455166"}, {"RefNumber": "454030", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Wrong text on Txns O4P7 and O4P8", "RefUrl": "/notes/454030"}, {"RefNumber": "454007", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Group conditions and other cond cumulated value with 431", "RefUrl": "/notes/454007"}, {"RefNumber": "453243", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Transport Unit compartment data customer defined screen", "RefUrl": "/notes/453243"}, {"RefNumber": "453208", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Short Dump in 40b after application of new kernel", "RefUrl": "/notes/453208"}, {"RefNumber": "452122", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Nota fiscal compl. does not consider user condition types", "RefUrl": "/notes/452122"}, {"RefNumber": "451521", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Support extended IDOCs during IDOC creation", "RefUrl": "/notes/451521"}, {"RefNumber": "451483", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange number is cleared when changing material document", "RefUrl": "/notes/451483"}, {"RefNumber": "451394", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees in delivery multiplied by thousand", "RefUrl": "/notes/451394"}, {"RefNumber": "451134", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Mass update of contract prices and external ED pricing key", "RefUrl": "/notes/451134"}, {"RefNumber": "448454", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "O4TF: LIST OF TICKETS A<PERSON><PERSON><PERSON>LE INCLUDES DELETED TICKETS", "RefUrl": "/notes/448454"}, {"RefNumber": "448020", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Double update of IDOCS in parallel inbound & Idoc locking", "RefUrl": "/notes/448020"}, {"RefNumber": "447778", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Missing delivery note and bill of loading in a material doc.", "RefUrl": "/notes/447778"}, {"RefNumber": "447012", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "OIL:Change tkt with same name as deleted tkt/NOMIT not displ", "RefUrl": "/notes/447012"}, {"RefNumber": "446350", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External Details in the goods movement 301, 309 and 311.", "RefUrl": "/notes/446350"}, {"RefNumber": "446038", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Program termination during BW contract data upload", "RefUrl": "/notes/446038"}, {"RefNumber": "445741", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Report Find Shipments", "RefUrl": "/notes/445741"}, {"RefNumber": "445150", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Unable to change pricing date on fee screen in billing", "RefUrl": "/notes/445150"}, {"RefNumber": "443575", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Excise duty balance calc incorrect when gain/loss", "RefUrl": "/notes/443575"}, {"RefNumber": "443421", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Archiving of shipments", "RefUrl": "/notes/443421"}, {"RefNumber": "442657", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong german texts in sales documents", "RefUrl": "/notes/442657"}, {"RefNumber": "442303", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Billing index update problems", "RefUrl": "/notes/442303"}, {"RefNumber": "441786", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Display Error in SAPMOIGS-3200 when calling from 3700", "RefUrl": "/notes/441786"}, {"RefNumber": "440799", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Purchase orders without any costs can't be processed via ERS", "RefUrl": "/notes/440799"}, {"RefNumber": "440005", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Wrong value for MSEG-MENGE for cancellation", "RefUrl": "/notes/440005"}, {"RefNumber": "439036", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "BW wrong load; delete OIGSM; load interface; delete dcmt.", "RefUrl": "/notes/439036"}, {"RefNumber": "438767", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Invoice print performance and memory problem", "RefUrl": "/notes/438767"}, {"RefNumber": "438549", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fee copy controls missing in billing to order copy control", "RefUrl": "/notes/438549"}, {"RefNumber": "438227", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Billing: fee total condition differs from fee sum", "RefUrl": "/notes/438227"}, {"RefNumber": "438098", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong display of values in SAPF124", "RefUrl": "/notes/438098"}, {"RefNumber": "436699", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "LIAs are not properly deriving Profit Center", "RefUrl": "/notes/436699"}, {"RefNumber": "434924", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 63-65 incl. 4.0B", "RefUrl": "/notes/434924"}, {"RefNumber": "433834", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Internal work areas defined in RVINVB00, RVINVB10 are small.", "RefUrl": "/notes/433834"}, {"RefNumber": "432574", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Quantity schedule on a purchase order is wrong  (TD related)", "RefUrl": "/notes/432574"}, {"RefNumber": "431050", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Returns not Updating License Quantity Tracking", "RefUrl": "/notes/431050"}, {"RefNumber": "430515", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Item category redetermination not working", "RefUrl": "/notes/430515"}, {"RefNumber": "429059", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Short dump when creating Call-off against Contract", "RefUrl": "/notes/429059"}, {"RefNumber": "429002", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "CO-PA charact. OIFWE: not filled properly in Invoice", "RefUrl": "/notes/429002"}, {"RefNumber": "428885", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/428885"}, {"RefNumber": "428884", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/428884"}, {"RefNumber": "428275", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "KE4S fails when loading invoice with fee total condition", "RefUrl": "/notes/428275"}, {"RefNumber": "427847", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong fee copy at good receipt cancellation", "RefUrl": "/notes/427847"}, {"RefNumber": "426588", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Shortdump in a sales order with no line items.", "RefUrl": "/notes/426588"}, {"RefNumber": "425216", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Batch missing; Vehicle balance; BW /0; Abend mvmt o b; OIGSH", "RefUrl": "/notes/425216"}, {"RefNumber": "424163", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Cancel goods issue: no entry in OIAQB created", "RefUrl": "/notes/424163"}, {"RefNumber": "423668", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "STSI Load Confirmation posts additional qtys for TDP matl", "RefUrl": "/notes/423668"}, {"RefNumber": "423369", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Shortdump while changing sales order with no line items.", "RefUrl": "/notes/423369"}, {"RefNumber": "422007", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL TDP BILLING MISSING CO LINES", "RefUrl": "/notes/422007"}, {"RefNumber": "421873", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Delivery weight & volume not updated properly in delivery", "RefUrl": "/notes/421873"}, {"RefNumber": "421035", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Rollback while deleting archived stock entries", "RefUrl": "/notes/421035"}, {"RefNumber": "420585", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Compartment Planning disabling NOT possible", "RefUrl": "/notes/420585"}, {"RefNumber": "420095", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL TDP GI for delivery note missing OIVBELN + OIPOSNR", "RefUrl": "/notes/420095"}, {"RefNumber": "419887", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Impossible to change exchange starting date", "RefUrl": "/notes/419887"}, {"RefNumber": "418672", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "851/852 for real batch; STO VBFA; batch in conversion; Lov=0", "RefUrl": "/notes/418672"}, {"RefNumber": "417679", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Handling type disappears when PO changed", "RefUrl": "/notes/417679"}, {"RefNumber": "417277", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Short dump & BDC error with netting document & BTCI", "RefUrl": "/notes/417277"}, {"RefNumber": "417113", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "O4PO: new created delivery w/o tank id entries in oik37", "RefUrl": "/notes/417113"}, {"RefNumber": "416930", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Cancelation of note 391987 (no agreed redesign of feerepric)", "RefUrl": "/notes/416930"}, {"RefNumber": "416919", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Short dump while calling BAPI_SALESORDER_CREATEFROMDAT2", "RefUrl": "/notes/416919"}, {"RefNumber": "416839", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Performance improvement loading and delivery confirmation", "RefUrl": "/notes/416839"}, {"RefNumber": "415989", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 61-62 incl. 4.0B", "RefUrl": "/notes/415989"}, {"RefNumber": "415005", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "data element OIG_AEDTMF field labels are wrong", "RefUrl": "/notes/415005"}, {"RefNumber": "414989", "RefComponent": "IS-OIL", "RefTitle": "Enable internal flag in ticket screen", "RefUrl": "/notes/414989"}, {"RefNumber": "414893", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Field Control for transport zone & Mode of Transport", "RefUrl": "/notes/414893"}, {"RefNumber": "414289", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "O5NX Batch input error due to LEAVE TO TRANSACTION statement", "RefUrl": "/notes/414289"}, {"RefNumber": "414197", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "O5NG batch input error due to LEAVE TO TRANSACTION statement", "RefUrl": "/notes/414197"}, {"RefNumber": "413748", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Redetermine Sold-to party when creating new SD contract", "RefUrl": "/notes/413748"}, {"RefNumber": "413635", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Manual pricing conditions are repriced for oil BoM", "RefUrl": "/notes/413635"}, {"RefNumber": "412543", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Oil vers. of note 410014: No check of pricing ref. material", "RefUrl": "/notes/412543"}, {"RefNumber": "412494", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/412494"}, {"RefNumber": "412012", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exg.data/fees aren't copied at cred-memo creation/cancel.", "RefUrl": "/notes/412012"}, {"RefNumber": "411898", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Cancel credit memo raises message", "RefUrl": "/notes/411898"}, {"RefNumber": "411413", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "VA01 errors when removing tank assignment", "RefUrl": "/notes/411413"}, {"RefNumber": "411207", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Shipment Planning corrections", "RefUrl": "/notes/411207"}, {"RefNumber": "409524", "RefComponent": "IS-OIL-DS", "RefTitle": "QA cleanup TODO/SAMT for support systems", "RefUrl": "/notes/409524"}, {"RefNumber": "409269", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "F&A Formula is not deleted to the customizing table", "RefUrl": "/notes/409269"}, {"RefNumber": "408614", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "MCOE: No Validation for ship. point and route on order entry", "RefUrl": "/notes/408614"}, {"RefNumber": "407197", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Billed order appears again in VF04", "RefUrl": "/notes/407197"}, {"RefNumber": "406895", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/406895"}, {"RefNumber": "406580", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Error when changing storage location in delivery", "RefUrl": "/notes/406580"}, {"RefNumber": "406284", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "MR01 in foreign currency ends in message M8 *********** 085", "RefUrl": "/notes/406284"}, {"RefNumber": "406047", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Mvt type 851/852-LIS, no value; VL09; QCI in mining", "RefUrl": "/notes/406047"}, {"RefNumber": "403275", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Shortdump when posting netting document with many items", "RefUrl": "/notes/403275"}, {"RefNumber": "403030", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "After implementation of 392231 inbound process short dumps", "RefUrl": "/notes/403030"}, {"RefNumber": "402561", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Error in sales order for more then one item in TAS", "RefUrl": "/notes/402561"}, {"RefNumber": "402296", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Error occurs in LIA account determination", "RefUrl": "/notes/402296"}, {"RefNumber": "402177", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Shortdmp \"Field symbol not assigned\" when leaving fee dialog", "RefUrl": "/notes/402177"}, {"RefNumber": "401810", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Error V0010 occur after repeat the allocation step in O3A2", "RefUrl": "/notes/401810"}, {"RefNumber": "401341", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Zero quantity line is not calculated during delivery", "RefUrl": "/notes/401341"}, {"RefNumber": "400863", "RefComponent": "IS-OIL", "RefTitle": "OIL: Problems deleting E&P Hierarchies", "RefUrl": "/notes/400863"}, {"RefNumber": "400585", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/400585"}, {"RefNumber": "400419", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Formula Maintenance ends in duplicate record", "RefUrl": "/notes/400419"}, {"RefNumber": "400071", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Corrections to OIK_CHANGE_ORDER", "RefUrl": "/notes/400071"}, {"RefNumber": "399825", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exclude items from ERS is not correct", "RefUrl": "/notes/399825"}, {"RefNumber": "399436", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL TDP Sales excise duty revenue adjustment", "RefUrl": "/notes/399436"}, {"RefNumber": "399019", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Travel time fields not filled from shimpent", "RefUrl": "/notes/399019"}, {"RefNumber": "398955", "RefComponent": "IS-OIL", "RefTitle": "ISO conversion leads to blank unit of measures", "RefUrl": "/notes/398955"}, {"RefNumber": "398954", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Shipment Planning corrections", "RefUrl": "/notes/398954"}, {"RefNumber": "398764", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Incorrect weights and volumes in delivery", "RefUrl": "/notes/398764"}, {"RefNumber": "398747", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No error at O4PO if delivery is assigned to shipment", "RefUrl": "/notes/398747"}, {"RefNumber": "398658", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Append OILTCURR from table TCURR deleted with 4.6C", "RefUrl": "/notes/398658"}, {"RefNumber": "397831", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 57-60 incl. 4.0B", "RefUrl": "/notes/397831"}, {"RefNumber": "397767", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MRRL Duplicate error messages in the ERS run output", "RefUrl": "/notes/397767"}, {"RefNumber": "397101", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Error in rebate agreement with zero document", "RefUrl": "/notes/397101"}, {"RefNumber": "396242", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Dump in TD bulk shp in OIB2_TD_GET_MAT_TEMP by NO_RECORD exc", "RefUrl": "/notes/396242"}, {"RefNumber": "395885", "RefComponent": "IS-OIL-DS-SSR", "RefTitle": "OIL: SSR Field conversions retrieves wrong value", "RefUrl": "/notes/395885"}, {"RefNumber": "395164", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/395164"}, {"RefNumber": "393987", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "IS-OIL: Clear sales office and group in SO entry", "RefUrl": "/notes/393987"}, {"RefNumber": "393394", "RefComponent": "IS-OIL-DS-SSR", "RefTitle": "OTWS error on SCP screen of Business Location", "RefUrl": "/notes/393394"}, {"RefNumber": "392985", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR21: Double values passed to CO, accounting items missing", "RefUrl": "/notes/392985"}, {"RefNumber": "392666", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "SAPSQL_ARRAY_INSERT_DUPREC in ME_UPDATE_DOCUMENT", "RefUrl": "/notes/392666"}, {"RefNumber": "392647", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "EKAB updated wrongly during goods receipt in MB01 or MIGO", "RefUrl": "/notes/392647"}, {"RefNumber": "392391", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Missing gain/loss posting at goods receipt/issue", "RefUrl": "/notes/392391"}, {"RefNumber": "392236", "RefComponent": "IS-OIL", "RefTitle": "ISO conversion leads to blank unit of measures", "RefUrl": "/notes/392236"}, {"RefNumber": "392231", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No error at O4PO if delivery is assigned to shipment", "RefUrl": "/notes/392231"}, {"RefNumber": "392082", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Wrong data format in forwarded Idoc OILLDD & OILLDC", "RefUrl": "/notes/392082"}, {"RefNumber": "392074", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "FDI indicator invisible at delivery note return order", "RefUrl": "/notes/392074"}, {"RefNumber": "391987", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "GR Repricing does not reprice exchange fees", "RefUrl": "/notes/391987"}, {"RefNumber": "391477", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Invoice split dur.int-comp.billng:location assgnmt", "RefUrl": "/notes/391477"}, {"RefNumber": "391448", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Create acc. doc. w/ BTCI: no exchange no. field", "RefUrl": "/notes/391448"}, {"RefNumber": "390711", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Cancelling invoice passes wrong quantity to LIS", "RefUrl": "/notes/390711"}, {"RefNumber": "390503", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Billing docs with zero qty for already billed SD documents.", "RefUrl": "/notes/390503"}, {"RefNumber": "388915", "RefComponent": "IS-OIL", "RefTitle": "ISO conversion leads to blank unit of measures", "RefUrl": "/notes/388915"}, {"RefNumber": "388748", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees in invoice verification should not be parked", "RefUrl": "/notes/388748"}, {"RefNumber": "386718", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "QS message O1 012 customizable by table T160M", "RefUrl": "/notes/386718"}, {"RefNumber": "385889", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees or taxes missing on netting statement", "RefUrl": "/notes/385889"}, {"RefNumber": "385417", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Error when changing stor.loc. in delivery", "RefUrl": "/notes/385417"}, {"RefNumber": "384950", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/384950"}, {"RefNumber": "384877", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Bad performance at VF04/VF06 because of too much enqueues", "RefUrl": "/notes/384877"}, {"RefNumber": "384804", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MAP: Value without quotation in second level analys", "RefUrl": "/notes/384804"}, {"RefNumber": "383918", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "BSW factor is not appearing in oil calc. at load confirm.", "RefUrl": "/notes/383918"}, {"RefNumber": "382846", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees missing on invoice print", "RefUrl": "/notes/382846"}, {"RefNumber": "382255", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD: Display purchase requisition from documentflow", "RefUrl": "/notes/382255"}, {"RefNumber": "381907", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD-F: Conversion of UoM for shipment costing", "RefUrl": "/notes/381907"}, {"RefNumber": "381772", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Incorrect screen to display Consignment Pricing", "RefUrl": "/notes/381772"}, {"RefNumber": "381741", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Mtr rdg; Part.conf.interf.; Load reserv.; Del.conf.<PERSON>ot", "RefUrl": "/notes/381741"}, {"RefNumber": "381616", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Goods receipt: Wrong internal material payables", "RefUrl": "/notes/381616"}, {"RefNumber": "381516", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Short dump in VF04 on Nota Fiscal Complementar", "RefUrl": "/notes/381516"}, {"RefNumber": "381462", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "F4 help on shipment workbench shows all drivers", "RefUrl": "/notes/381462"}, {"RefNumber": "380993", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Wrong sorting in formula term item tab XOICQ8 and XOICQ9", "RefUrl": "/notes/380993"}, {"RefNumber": "380744", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Cancelling invoice with fees: no accounting document", "RefUrl": "/notes/380744"}, {"RefNumber": "380451", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong/Repetitive warning message O1 811 in MR01 simulation", "RefUrl": "/notes/380451"}, {"RefNumber": "379875", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong GI price", "RefUrl": "/notes/379875"}, {"RefNumber": "379751", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Excise Duty Screen appears empty in the Incompl.Log", "RefUrl": "/notes/379751"}, {"RefNumber": "379423", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Doc. item qty assign. in case of weight-volume-deviations", "RefUrl": "/notes/379423"}, {"RefNumber": "378406", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "No gain/loss post. at GR/GI with diff. sub/baseproduct", "RefUrl": "/notes/378406"}, {"RefNumber": "378285", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Correction for short dump due to TABLE_INVALID_INDEX", "RefUrl": "/notes/378285"}, {"RefNumber": "377118", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Quantity missing in logical inventory", "RefUrl": "/notes/377118"}, {"RefNumber": "376579", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MAP:Conditions with qty scale basis become inactive in docs", "RefUrl": "/notes/376579"}, {"RefNumber": "376541", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Performance improvement Euro-conversion LIKP", "RefUrl": "/notes/376541"}, {"RefNumber": "375158", "RefComponent": "BC-UPG-ADDON", "RefTitle": "IS-OIL: Additional Info - SPs 54-56 incl. 4.0B", "RefUrl": "/notes/375158"}, {"RefNumber": "374985", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR01 delivery cost sequence as per GR sequence", "RefUrl": "/notes/374985"}, {"RefNumber": "374846", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error in changing ´plant´ of order", "RefUrl": "/notes/374846"}, {"RefNumber": "373605", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Error in dynamic UoM enhancement (F&A fees)", "RefUrl": "/notes/373605"}, {"RefNumber": "372540", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Inconsistency in additional quantities for delivery", "RefUrl": "/notes/372540"}, {"RefNumber": "372080", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Error in dynamic UoM enhancement", "RefUrl": "/notes/372080"}, {"RefNumber": "371447", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Consideration of gain/losses at the LIA posting", "RefUrl": "/notes/371447"}, {"RefNumber": "370775", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "CONVERSION FACTOR ERROR", "RefUrl": "/notes/370775"}, {"RefNumber": "370710", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: RGJNOUXD incorrect transaction currency", "RefUrl": "/notes/370710"}, {"RefNumber": "370570", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Incorrect valuation of vendor consignment stock", "RefUrl": "/notes/370570"}, {"RefNumber": "370169", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Missing order item in TPI outbound idoc", "RefUrl": "/notes/370169"}, {"RefNumber": "370160", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Performance problem on reads to DD07L slow down 04G1.", "RefUrl": "/notes/370160"}, {"RefNumber": "369927", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Quant. conv. factors from info record only if they exist", "RefUrl": "/notes/369927"}, {"RefNumber": "369591", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Display ED licenses during sales order", "RefUrl": "/notes/369591"}, {"RefNumber": "368957", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "EKBZ update excise duty addback", "RefUrl": "/notes/368957"}, {"RefNumber": "368749", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MBST:Wrong F&A condition value displayed in PO history", "RefUrl": "/notes/368749"}, {"RefNumber": "367704", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Goods issue: Double S036 update", "RefUrl": "/notes/367704"}, {"RefNumber": "367022", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Redistribution of a Order planned by Ext TPS", "RefUrl": "/notes/367022"}, {"RefNumber": "366778", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Inconsistency in prof.segm.number for intercompany billing", "RefUrl": "/notes/366778"}, {"RefNumber": "366622", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Posting of exchange reversal fees to SL not possible", "RefUrl": "/notes/366622"}, {"RefNumber": "365908", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS OIL: PI with freeze book indicator", "RefUrl": "/notes/365908"}, {"RefNumber": "365843", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "No record OIGSVMQO1;GI of STO for new valtyp; 195869", "RefUrl": "/notes/365843"}, {"RefNumber": "365694", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Minus(-) Temperature is not processed.", "RefUrl": "/notes/365694"}, {"RefNumber": "365418", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "G_OIKIMPORT struct not populated in SD_SALES_ITEM_MAINTAIN", "RefUrl": "/notes/365418"}, {"RefNumber": "364919", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Inbound shipment interface abends on delivery create error", "RefUrl": "/notes/364919"}, {"RefNumber": "364900", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "TankID is lost, if items are deleted+re-created in s/o", "RefUrl": "/notes/364900"}, {"RefNumber": "364780", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Inbound interface abends on deleted order", "RefUrl": "/notes/364780"}, {"RefNumber": "364777", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Negative quantities in quantity schedule", "RefUrl": "/notes/364777"}, {"RefNumber": "364644", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Shipment change on TPI planning screen deletes carrier", "RefUrl": "/notes/364644"}, {"RefNumber": "364320", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Delivery update termin./ excise duty cond. missing", "RefUrl": "/notes/364320"}, {"RefNumber": "364092", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "TSW Ticketing: Load Confirmation", "RefUrl": "/notes/364092"}, {"RefNumber": "364063", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/364063"}, {"RefNumber": "364026", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Problems at the datacopy from MM contract to po- TDP related", "RefUrl": "/notes/364026"}, {"RefNumber": "363888", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Differential invoice and taxes", "RefUrl": "/notes/363888"}, {"RefNumber": "363310", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Dump at the creation of an iv. list (OIC_DITAB is not def.)", "RefUrl": "/notes/363310"}, {"RefNumber": "363140", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "IS-Oil MRN field OIFWE not initialized properly", "RefUrl": "/notes/363140"}, {"RefNumber": "363131", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Error proc.while assign. v.mtr with del.flag to Transp.Unit", "RefUrl": "/notes/363131"}, {"RefNumber": "363115", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Error M7 234 for movement involving vendor consignment", "RefUrl": "/notes/363115"}, {"RefNumber": "362910", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Proposal of valuation type in batch field", "RefUrl": "/notes/362910"}, {"RefNumber": "362516", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "VL02: update error for purchase assignment", "RefUrl": "/notes/362516"}, {"RefNumber": "362410", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "T063 entries missing with function code OIDE", "RefUrl": "/notes/362410"}, {"RefNumber": "362077", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Financial documents not found in netting", "RefUrl": "/notes/362077"}, {"RefNumber": "361878", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Event doc type not found (msg E220)", "RefUrl": "/notes/361878"}, {"RefNumber": "361335", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Error O1 573 during purch. ass. with group cond.", "RefUrl": "/notes/361335"}, {"RefNumber": "361284", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR01/MRHG: Delivery Cost Credit Memo error between years", "RefUrl": "/notes/361284"}, {"RefNumber": "361254", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Partner det. in call-off from ship-to with SAP/TAS interfa", "RefUrl": "/notes/361254"}, {"RefNumber": "360432", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Error OB 011 create PO with ref to requisition", "RefUrl": "/notes/360432"}, {"RefNumber": "360233", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Open hours check fails for Time Frames", "RefUrl": "/notes/360233"}, {"RefNumber": "358259", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "HT default from Process order during GI", "RefUrl": "/notes/358259"}, {"RefNumber": "357343", "RefComponent": "IS-OIL-DS-QCI", "RefTitle": "Session locked by qty conversion for 0 density/rel. density", "RefUrl": "/notes/357343"}, {"RefNumber": "357274", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect tax code in acc.doc. for diff.invoice", "RefUrl": "/notes/357274"}, {"RefNumber": "356722", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "IS-OIL: Adaption of preprocessing Program EWUMMPOA", "RefUrl": "/notes/356722"}, {"RefNumber": "356471", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "CASE II ROIGMS00: Inconsistencies between SAP and terminal", "RefUrl": "/notes/356471"}, {"RefNumber": "356301", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Borrow/Loan exchange posting to Purchase Acct. Mgt.", "RefUrl": "/notes/356301"}, {"RefNumber": "356080", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External Details missing when plant entered manually", "RefUrl": "/notes/356080"}, {"RefNumber": "356007", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Set loading date/time in freight assignment (O4L1)", "RefUrl": "/notes/356007"}, {"RefNumber": "355654", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Base Location lookup  for customer exit not working", "RefUrl": "/notes/355654"}, {"RefNumber": "355588", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error handling in transaction O4PO", "RefUrl": "/notes/355588"}, {"RefNumber": "355564", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: ED Revaluation if base UoM not equal ED UoM", "RefUrl": "/notes/355564"}, {"RefNumber": "355142", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "O4F2: Update terminated on vehicle change in bulk shipment", "RefUrl": "/notes/355142"}, {"RefNumber": "354546", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MAP: Price not found, if non-posted day = 'X'", "RefUrl": "/notes/354546"}, {"RefNumber": "353978", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Ship-to party incorrectly displayed", "RefUrl": "/notes/353978"}, {"RefNumber": "353230", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Authority check fails for quantity schedule", "RefUrl": "/notes/353230"}, {"RefNumber": "352656", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Delivery w/ purchase assignment: update termination", "RefUrl": "/notes/352656"}, {"RefNumber": "352264", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Populate LIPS-LGMNG with qty in base UoM for GI reversal", "RefUrl": "/notes/352264"}, {"RefNumber": "351945", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Calc. sales tax acc. to new price cond. when price control 5", "RefUrl": "/notes/351945"}, {"RefNumber": "351931", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Abend O1 544 on Delivery confirmation", "RefUrl": "/notes/351931"}, {"RefNumber": "351836", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: No repricing for oil BOM header", "RefUrl": "/notes/351836"}, {"RefNumber": "351807", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "LIPS upd missing; Delete NAST", "RefUrl": "/notes/351807"}, {"RefNumber": "351666", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Input of init.value not allowed for field V_T685A-OIREPORT", "RefUrl": "/notes/351666"}, {"RefNumber": "351652", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Update termination during goods issue", "RefUrl": "/notes/351652"}, {"RefNumber": "351626", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Missing IS-Oil fields in CO-PA KENC table", "RefUrl": "/notes/351626"}, {"RefNumber": "351551", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Received error message when add a new output item", "RefUrl": "/notes/351551"}, {"RefNumber": "351482", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - LCP 45-53 incl. 4.0B", "RefUrl": "/notes/351482"}, {"RefNumber": "351475", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 45-53 incl. 4.0B", "RefUrl": "/notes/351475"}, {"RefNumber": "350797", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "G_OIKIMPORT structure not populated in SD_ITEM_MAINTAIN", "RefUrl": "/notes/350797"}, {"RefNumber": "350766", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Search help VMVL ID T causes problems", "RefUrl": "/notes/350766"}, {"RefNumber": "350742", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Tank-ID screen in TD-Deliver Confirmation comes blank", "RefUrl": "/notes/350742"}, {"RefNumber": "350486", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "IS-OIL : TSW : Nominations not printing", "RefUrl": "/notes/350486"}, {"RefNumber": "350212", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Update termination in shipment delete due to credit check", "RefUrl": "/notes/350212"}, {"RefNumber": "339524", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Enable stock transfer when Han.Type&VT have diff. ED Status", "RefUrl": "/notes/339524"}, {"RefNumber": "338865", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Split invoicing not working for accruals", "RefUrl": "/notes/338865"}, {"RefNumber": "338202", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Characteristic derivation PSPID -> OIFPBL does not work", "RefUrl": "/notes/338202"}, {"RefNumber": "337560", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Credit memo request with reference to invoice.", "RefUrl": "/notes/337560"}, {"RefNumber": "337217", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Status Handling enhancement during Scheduling", "RefUrl": "/notes/337217"}, {"RefNumber": "336962", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: MR21/MR22 allow postings at negativ stock level", "RefUrl": "/notes/336962"}, {"RefNumber": "336950", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: wrong repricing in TD after note", "RefUrl": "/notes/336950"}, {"RefNumber": "336664", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Quantity conversion error in rebate agreement", "RefUrl": "/notes/336664"}, {"RefNumber": "336440", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Corrections Loc.Brazil 09/2000", "RefUrl": "/notes/336440"}, {"RefNumber": "336373", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-Oil: TDP Handling Type not copied at PO creation", "RefUrl": "/notes/336373"}, {"RefNumber": "336131", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External details WAOIDEVB entry missing in T063", "RefUrl": "/notes/336131"}, {"RefNumber": "336109", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Short dump due to formula & average fee condition type", "RefUrl": "/notes/336109"}, {"RefNumber": "336103", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "GR against purchase order fails with msg M7301", "RefUrl": "/notes/336103"}, {"RefNumber": "336030", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Short dump VF01 - TABLE_INVALID_INDEX", "RefUrl": "/notes/336030"}, {"RefNumber": "335955", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "GR: Field RM07M-LFSNR is not filled from MKPF-XBLNR", "RefUrl": "/notes/335955"}, {"RefNumber": "335717", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange abstract reprices sales fees incorrectly", "RefUrl": "/notes/335717"}, {"RefNumber": "335256", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees in invoice verification should not be parked", "RefUrl": "/notes/335256"}, {"RefNumber": "334920", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Problem in Archiving of Customer without BDRP data", "RefUrl": "/notes/334920"}, {"RefNumber": "334872", "RefComponent": "BC-UPG-OCS", "RefTitle": "Separation LCPs HR/Hot Packages in Release 4.0B", "RefUrl": "/notes/334872"}, {"RefNumber": "334795", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Error in conversion factor in rundown report", "RefUrl": "/notes/334795"}, {"RefNumber": "334462", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Sort order doc.flow; Rounding load interface; reserv. batch", "RefUrl": "/notes/334462"}, {"RefNumber": "333978", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "duplicates in netting document list for exchange", "RefUrl": "/notes/333978"}, {"RefNumber": "333273", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "TSW Modifications to fix Batch/Valuations", "RefUrl": "/notes/333273"}, {"RefNumber": "332993", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Formula Price calculation in relation to quantity", "RefUrl": "/notes/332993"}, {"RefNumber": "332989", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Update of nomination failed", "RefUrl": "/notes/332989"}, {"RefNumber": "332960", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/332960"}, {"RefNumber": "332654", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL / IS-MINE / IS-CWM: Overview of SAP Notes", "RefUrl": "/notes/332654"}, {"RefNumber": "332287", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "S3VBRKWR: SQL error 4031 when accessing table OICQ7", "RefUrl": "/notes/332287"}, {"RefNumber": "332092", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "LIA does not transfer Trading partner to FI", "RefUrl": "/notes/332092"}, {"RefNumber": "331952", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Rundown engine calculations for transport system.", "RefUrl": "/notes/331952"}, {"RefNumber": "331304", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Error displaying Ext. Dtls. in Goods Issue Docs.", "RefUrl": "/notes/331304"}, {"RefNumber": "330214", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Opening balance on exchange statement missing", "RefUrl": "/notes/330214"}, {"RefNumber": "330194", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange statement stops printing", "RefUrl": "/notes/330194"}, {"RefNumber": "329466", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fee details can not be processed at goods receipt", "RefUrl": "/notes/329466"}, {"RefNumber": "329203", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Material Master / ALE / oil specific data", "RefUrl": "/notes/329203"}, {"RefNumber": "329200", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Data Archiving Problem : OII_METER_CUSTOMER_INDEX_GET", "RefUrl": "/notes/329200"}, {"RefNumber": "328266", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ASTM tolerance checks ignored during STSI IDOC processing", "RefUrl": "/notes/328266"}, {"RefNumber": "328204", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "GR reversals missing in movements-based netting", "RefUrl": "/notes/328204"}, {"RefNumber": "328151", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Netting abends saying accounting doc not found", "RefUrl": "/notes/328151"}, {"RefNumber": "328086", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Short dump due to negative net price in contract display", "RefUrl": "/notes/328086"}, {"RefNumber": "327863", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "table parameters missing for call RV_DELIVERY_ADD etc.", "RefUrl": "/notes/327863"}, {"RefNumber": "327364", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Incorrect quantity in SIS info structures", "RefUrl": "/notes/327364"}, {"RefNumber": "327242", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Data Archiving Problem", "RefUrl": "/notes/327242"}, {"RefNumber": "326970", "RefComponent": "IS-OIL", "RefTitle": "Missing setting for change pointers", "RefUrl": "/notes/326970"}, {"RefNumber": "326895", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Open hours check fails for Time Frames", "RefUrl": "/notes/326895"}, {"RefNumber": "326861", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Update termination in SAPLOIIU", "RefUrl": "/notes/326861"}, {"RefNumber": "326517", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "VA01 TAS relevancy checks hinder order entry performance", "RefUrl": "/notes/326517"}, {"RefNumber": "325472", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect rebate scale basis after running SDBONTO2", "RefUrl": "/notes/325472"}, {"RefNumber": "325412", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL:Material movement with n lines, account mod", "RefUrl": "/notes/325412"}, {"RefNumber": "325399", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Field KNVV-OIINEX . is not an input field", "RefUrl": "/notes/325399"}, {"RefNumber": "325179", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Short dump in display sales order : VA03", "RefUrl": "/notes/325179"}, {"RefNumber": "325052", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-Oil: Sales - Automatic update of Excise Duty values", "RefUrl": "/notes/325052"}, {"RefNumber": "324964", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Classification for business location", "RefUrl": "/notes/324964"}, {"RefNumber": "324806", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Reversal Documents reported in Vertex with the Wrong Sign", "RefUrl": "/notes/324806"}, {"RefNumber": "324518", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Renaming customer exit function to oil namespace", "RefUrl": "/notes/324518"}, {"RefNumber": "324334", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "F4 Help for projects within business location", "RefUrl": "/notes/324334"}, {"RefNumber": "324180", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Abend in VA02 after notes 316902 and 322765", "RefUrl": "/notes/324180"}, {"RefNumber": "323761", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Problem with exchange number when creating sales contracts", "RefUrl": "/notes/323761"}, {"RefNumber": "323387", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Characteristics for postings to CO-PA from shpmnt", "RefUrl": "/notes/323387"}, {"RefNumber": "323181", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Update termination in shipment creation", "RefUrl": "/notes/323181"}, {"RefNumber": "323129", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "LID internal numbering not checking OIKLID for duplicates", "RefUrl": "/notes/323129"}, {"RefNumber": "322879", "RefComponent": "IS-OIL-BC", "RefTitle": "SAPMM07M,..: Too many DATA control blocks / generation error", "RefUrl": "/notes/322879"}, {"RefNumber": "322765", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "BOM comp.qty;rebr. batch PtL;check batch STO;scroll on 3700", "RefUrl": "/notes/322765"}, {"RefNumber": "322720", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Impossible to display quantity schedule in contract", "RefUrl": "/notes/322720"}, {"RefNumber": "322646", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Ticket delivery date < Ordr delivery date", "RefUrl": "/notes/322646"}, {"RefNumber": "322262", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Enable scroll bars/min. resolution in OIL entry order screen", "RefUrl": "/notes/322262"}, {"RefNumber": "322240", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/322240"}, {"RefNumber": "321836", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Tracking of tax exemption licenses", "RefUrl": "/notes/321836"}, {"RefNumber": "321691", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect amount in Inv.verif. with GR cancellations", "RefUrl": "/notes/321691"}, {"RefNumber": "321161", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange statement: MM reversal quantity missing", "RefUrl": "/notes/321161"}, {"RefNumber": "321135", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Transp. functionality f. Operational Time Window Sets (OTWS)", "RefUrl": "/notes/321135"}, {"RefNumber": "321127", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Condition base value not moved in routine 409", "RefUrl": "/notes/321127"}, {"RefNumber": "320810", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Incorrect weight/volume in batch split main item", "RefUrl": "/notes/320810"}, {"RefNumber": "320496", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Period close using up all database locks", "RefUrl": "/notes/320496"}, {"RefNumber": "320192", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: VL02, two HPM screens appears during batch input", "RefUrl": "/notes/320192"}, {"RefNumber": "320098", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "LIA movement in wrong exchange statement section", "RefUrl": "/notes/320098"}, {"RefNumber": "319796", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Shipment Load ID valid-to < valid from date", "RefUrl": "/notes/319796"}, {"RefNumber": "319760", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "E1OILSC-MAX_VOL incorrect in OILSHL shipment d/load IDOC", "RefUrl": "/notes/319760"}, {"RefNumber": "319758", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/319758"}, {"RefNumber": "319203", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "To avoid ASTM pop-up during batch input.", "RefUrl": "/notes/319203"}, {"RefNumber": "318966", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Function OII_GET_SOLD_TO_FOR_SHIP_TO short dumps", "RefUrl": "/notes/318966"}, {"RefNumber": "318839", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Handling Type in Sales Order Display Screen 8025", "RefUrl": "/notes/318839"}, {"RefNumber": "318707", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "OILLDD IDOC posts w/out ASTM conv is density out of range", "RefUrl": "/notes/318707"}, {"RefNumber": "318688", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Errors in Tank ID handing in VA01/VA02", "RefUrl": "/notes/318688"}, {"RefNumber": "318652", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "IV ignores GR cancelation for items with split conditions", "RefUrl": "/notes/318652"}, {"RefNumber": "318607", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Wrong excise duty posting to accounting", "RefUrl": "/notes/318607"}, {"RefNumber": "318560", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Error during automatic order creation against GR", "RefUrl": "/notes/318560"}, {"RefNumber": "318436", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Short dump in invoice verification", "RefUrl": "/notes/318436"}, {"RefNumber": "318434", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "QS Messages customizable by table T160M", "RefUrl": "/notes/318434"}, {"RefNumber": "318351", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Incorrect quantity in rebate agreement", "RefUrl": "/notes/318351"}, {"RefNumber": "318349", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: ED Revaluation / Post Difference not possible", "RefUrl": "/notes/318349"}, {"RefNumber": "318202", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "IV: doubled items and fixed amount conditions", "RefUrl": "/notes/318202"}, {"RefNumber": "318040", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "'AW' partners not available in EXIT_SAPLOIK7_130 user exit", "RefUrl": "/notes/318040"}, {"RefNumber": "317951", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Error message when creating Intercompany invoice", "RefUrl": "/notes/317951"}, {"RefNumber": "317717", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "GR Reversal / Excise Duty / wrong Doc. Currency amounts", "RefUrl": "/notes/317717"}, {"RefNumber": "317519", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - LCP 39-44 incl. 4.0B", "RefUrl": "/notes/317519"}, {"RefNumber": "317518", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 39-44 incl. 4.0B", "RefUrl": "/notes/317518"}, {"RefNumber": "317019", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External details not copied to batch split items", "RefUrl": "/notes/317019"}, {"RefNumber": "316998", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "ROIIP-RCBLK,DOBLK not update db & SCP blank screen", "RefUrl": "/notes/316998"}, {"RefNumber": "316979", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Divide by zero error during invoice verification", "RefUrl": "/notes/316979"}, {"RefNumber": "316902", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Close TD BOM component according to main item", "RefUrl": "/notes/316902"}, {"RefNumber": "316891", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Flexible Locking of Driver Vehicle Assignment", "RefUrl": "/notes/316891"}, {"RefNumber": "316839", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "RD report - handling of RDCONV = 0", "RefUrl": "/notes/316839"}, {"RefNumber": "316708", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "IDOC Control record incorrect in IDOC_INPUT_OILDVA", "RefUrl": "/notes/316708"}, {"RefNumber": "316516", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "VL01/VL02/ME32/ME33 errors because OIA05/OIA06 unsorted", "RefUrl": "/notes/316516"}, {"RefNumber": "316453", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect exchange statement print out when no activities", "RefUrl": "/notes/316453"}, {"RefNumber": "316386", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "CALL_FUNCTION_CONFLICT_LENG", "RefUrl": "/notes/316386"}, {"RefNumber": "316218", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect account postings in GR with multiples POs", "RefUrl": "/notes/316218"}, {"RefNumber": "315973", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "S036 rebuild picks up some transactions twice", "RefUrl": "/notes/315973"}, {"RefNumber": "315968", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "IS-Oil reference note", "RefUrl": "/notes/315968"}, {"RefNumber": "315730", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect freight posting for exchange related GR", "RefUrl": "/notes/315730"}, {"RefNumber": "315433", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Mov.based Netting errors for cancelled invoice ver.", "RefUrl": "/notes/315433"}, {"RefNumber": "315091", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Advanced functionality for Shipment Planning", "RefUrl": "/notes/315091"}, {"RefNumber": "314967", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Saving of return order terminates", "RefUrl": "/notes/314967"}, {"RefNumber": "314778", "RefComponent": "IS-OIL-DS-QCI", "RefTitle": "IS OIL: QCI: Chemicals and TAS; BSW conversion; BAdI methods", "RefUrl": "/notes/314778"}, {"RefNumber": "314379", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Shortdump in S3VBRKWR and increase performance", "RefUrl": "/notes/314379"}, {"RefNumber": "314232", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Update termination when saving delivery", "RefUrl": "/notes/314232"}, {"RefNumber": "312369", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Long runtimes when archiving with FI_DOCUMNT", "RefUrl": "/notes/312369"}, {"RefNumber": "312246", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Parallel OILLDD processing - advanced development", "RefUrl": "/notes/312246"}, {"RefNumber": "312121", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MM_MATBEL: bad performance and incorrect check for netting", "RefUrl": "/notes/312121"}, {"RefNumber": "311935", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Goods receipt: calculation of base product price", "RefUrl": "/notes/311935"}, {"RefNumber": "311456", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Short dump creating contract with quantity schedule", "RefUrl": "/notes/311456"}, {"RefNumber": "310851", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Incorrect Quantity for BOM Header in Load Bulk on Activation", "RefUrl": "/notes/310851"}, {"RefNumber": "310788", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No provision to extend Idoc OILDVA01 Driver/Veh", "RefUrl": "/notes/310788"}, {"RefNumber": "310671", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "External ref number not preserved in transaction O4PP", "RefUrl": "/notes/310671"}, {"RefNumber": "310631", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Error with Prior to Load ;PTL Not scheduled for load.", "RefUrl": "/notes/310631"}, {"RefNumber": "310523", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Shipment Load ID re-determination", "RefUrl": "/notes/310523"}, {"RefNumber": "310427", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Brazil: Cancellation of documents from TD-transfers", "RefUrl": "/notes/310427"}, {"RefNumber": "310287", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Wrong default for date/ time on the time frame screen", "RefUrl": "/notes/310287"}, {"RefNumber": "310158", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD-Master User Screen Settings not customizable", "RefUrl": "/notes/310158"}, {"RefNumber": "310095", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Wrong positioning in shpt scheduling - doc./compart. details", "RefUrl": "/notes/310095"}, {"RefNumber": "309717", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "ED qty in special ledger / ED inventory during transfer", "RefUrl": "/notes/309717"}, {"RefNumber": "309707", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "No account posting in sub-contract PO with Oil material", "RefUrl": "/notes/309707"}, {"RefNumber": "309618", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "IS-OIL Output determination for Delivery Confirmation", "RefUrl": "/notes/309618"}, {"RefNumber": "309533", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Performance problems in differential invoice", "RefUrl": "/notes/309533"}, {"RefNumber": "308790", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD - Document flow;Branch to mat.doc,Incorrect GJAHR", "RefUrl": "/notes/308790"}, {"RefNumber": "308026", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect Txn Curr  Amount Displayed on Netting Proposal", "RefUrl": "/notes/308026"}, {"RefNumber": "307732", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Automatic document item quantity assignment", "RefUrl": "/notes/307732"}, {"RefNumber": "306559", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Inconsistent date used in order item create", "RefUrl": "/notes/306559"}, {"RefNumber": "306295", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Shipment number selection based on vehicle details", "RefUrl": "/notes/306295"}, {"RefNumber": "305918", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Air buoyancy indicator handling via STSI Load Confirmation", "RefUrl": "/notes/305918"}, {"RefNumber": "305891", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "VA01 allows order type which is not allowed for this SA", "RefUrl": "/notes/305891"}, {"RefNumber": "305704", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "OIK03 table entry not deleted after manual POST GI", "RefUrl": "/notes/305704"}, {"RefNumber": "305064", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Variable PO doc. type in purch. assignment", "RefUrl": "/notes/305064"}, {"RefNumber": "304626", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Bad performance of ERS fee processing", "RefUrl": "/notes/304626"}, {"RefNumber": "303827", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Enable Group Conditions 4 and 5 in IS-OIL 4.0B", "RefUrl": "/notes/303827"}, {"RefNumber": "303797", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Euro conversion for fee rates in invoice verification", "RefUrl": "/notes/303797"}, {"RefNumber": "303471", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "LID Flowlog report display incorrect", "RefUrl": "/notes/303471"}, {"RefNumber": "303220", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Tank ID defaulting in orders incorrect on change", "RefUrl": "/notes/303220"}, {"RefNumber": "303128", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: AUM posting with incorrect plant", "RefUrl": "/notes/303128"}, {"RefNumber": "303089", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Update termination when saving dlv. with oil BoM", "RefUrl": "/notes/303089"}, {"RefNumber": "302746", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Error in Creation of Idoc OILORD", "RefUrl": "/notes/302746"}, {"RefNumber": "302327", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "IS OIL OUTPUT DETERMINATION", "RefUrl": "/notes/302327"}, {"RefNumber": "302311", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Unable to Use Valuation Class in LIA Acct. Determination", "RefUrl": "/notes/302311"}, {"RefNumber": "301601", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Part.Conf.STO; Tolerance gain STO; Batch in interf.; uexit", "RefUrl": "/notes/301601"}, {"RefNumber": "301385", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Population of ship-to in TAS shipment IDoc", "RefUrl": "/notes/301385"}, {"RefNumber": "301021", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Inconsistent date used in order item create", "RefUrl": "/notes/301021"}, {"RefNumber": "301014", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Incorrect TPP check in IDOC_OUTPUT_OILTPI50", "RefUrl": "/notes/301014"}, {"RefNumber": "300869", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Split Indicator required in SD&MM Documents", "RefUrl": "/notes/300869"}, {"RefNumber": "300849", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ABAP ROIKPGIS", "RefUrl": "/notes/300849"}, {"RefNumber": "300784", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Table \"S???\" is not listed in the ABAP/4 Dictionary", "RefUrl": "/notes/300784"}, {"RefNumber": "300744", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Material freight classes/freight codes in shpt costing", "RefUrl": "/notes/300744"}, {"RefNumber": "300650", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect market quote found after pricing date change in SO", "RefUrl": "/notes/300650"}, {"RefNumber": "300647", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Mode of transport indicator not enabled for rail", "RefUrl": "/notes/300647"}, {"RefNumber": "300629", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Unnecessary messages in Tank defaulting", "RefUrl": "/notes/300629"}, {"RefNumber": "300611", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Minor corrections hydrocarbon inventory management", "RefUrl": "/notes/300611"}, {"RefNumber": "300406", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Batch split / ED license not copied to mat. doc.", "RefUrl": "/notes/300406"}, {"RefNumber": "300374", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Actual goods issue date in TAS interface (pick-up)", "RefUrl": "/notes/300374"}, {"RefNumber": "300298", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Multiple Change pointers generated for OILLID", "RefUrl": "/notes/300298"}, {"RefNumber": "300199", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Tables OIKLIDR/OIK01 become inconsistent for shipments", "RefUrl": "/notes/300199"}, {"RefNumber": "300151", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Hold data not enabled in IS-Oil overview screen", "RefUrl": "/notes/300151"}, {"RefNumber": "300150", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Exd license not valid (problem with the valid from/to date)", "RefUrl": "/notes/300150"}, {"RefNumber": "217379", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: incorrect quantity in GR for replenishment del.", "RefUrl": "/notes/217379"}, {"RefNumber": "217126", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Parallel OILLDD inbound processing", "RefUrl": "/notes/217126"}, {"RefNumber": "216894", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Object Key of change pointer (OILLID-OIGV) is wrong ?", "RefUrl": "/notes/216894"}, {"RefNumber": "216522", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Screen sequence control entry:T185 SAPMV45B OIDX...", "RefUrl": "/notes/216522"}, {"RefNumber": "216413", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Incorrect quantity proposed in sales order", "RefUrl": "/notes/216413"}, {"RefNumber": "216249", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Status messages not returned to TPI", "RefUrl": "/notes/216249"}, {"RefNumber": "216079", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Low performance when creat. deliveries for big contracts", "RefUrl": "/notes/216079"}, {"RefNumber": "216067", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Additional customer exits", "RefUrl": "/notes/216067"}, {"RefNumber": "216045", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Incorrect packing status", "RefUrl": "/notes/216045"}, {"RefNumber": "216007", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "NO CURRENCY/EX<PERSON>ANGE RATE FIELD ON DYNPRO SAPMV45A 4301", "RefUrl": "/notes/216007"}, {"RefNumber": "215984", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Minor errors on Plant Site Control Parameters", "RefUrl": "/notes/215984"}, {"RefNumber": "215705", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Quotation Integrity Check - Date Range Requirement", "RefUrl": "/notes/215705"}, {"RefNumber": "215636", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "New fee pricing date is not stored in the PO", "RefUrl": "/notes/215636"}, {"RefNumber": "215633", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Checking base product for a line that has been deleted.", "RefUrl": "/notes/215633"}, {"RefNumber": "215608", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Error Handling in TAS Inbound Process", "RefUrl": "/notes/215608"}, {"RefNumber": "215522", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Tables for sec. level analysis were not sorted", "RefUrl": "/notes/215522"}, {"RefNumber": "215460", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "TD Purchase Assignment Scr: F4 help on PO docs", "RefUrl": "/notes/215460"}, {"RefNumber": "215139", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Order time frame check vs OTWS incorrect", "RefUrl": "/notes/215139"}, {"RefNumber": "215068", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Movement based netting corrections", "RefUrl": "/notes/215068"}, {"RefNumber": "214996", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "VA03 - Handling Type field Invisible in Screen 8025", "RefUrl": "/notes/214996"}, {"RefNumber": "214794", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "QS error O1017 not customizable by table T160M", "RefUrl": "/notes/214794"}, {"RefNumber": "214666", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR8M/MR08 M8607 - Error when reversing invoice", "RefUrl": "/notes/214666"}, {"RefNumber": "214650", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "OILORD02: Segment E1EDP01-ACTION not filled correctly", "RefUrl": "/notes/214650"}, {"RefNumber": "214346", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Fields available for input in Display Sales Order", "RefUrl": "/notes/214346"}, {"RefNumber": "213939", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR21 posts inconsistent FI-documents with no items", "RefUrl": "/notes/213939"}, {"RefNumber": "213813", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Create sales order with reference to invoice", "RefUrl": "/notes/213813"}, {"RefNumber": "213757", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Err. PTL, Rebr. to non-batch, Avail.with warning, Veh.Recon", "RefUrl": "/notes/213757"}, {"RefNumber": "213579", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Valuation type not displayed as label", "RefUrl": "/notes/213579"}, {"RefNumber": "213093", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Manual entry of payment term in order header level", "RefUrl": "/notes/213093"}, {"RefNumber": "213049", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "TSW: Partner Role Detail Screen Tabcontrol size", "RefUrl": "/notes/213049"}, {"RefNumber": "213030", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "PO History leads to wrong IR Fee Document due to OIAFE", "RefUrl": "/notes/213030"}, {"RefNumber": "212670", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Error in goods issue for oil BoMs", "RefUrl": "/notes/212670"}, {"RefNumber": "212492", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Item proposal and item redetermination", "RefUrl": "/notes/212492"}, {"RefNumber": "211421", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Delivery Note and Bill of Lading not Appearing in MB02", "RefUrl": "/notes/211421"}, {"RefNumber": "211071", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Order time frame maintenenace not consistent", "RefUrl": "/notes/211071"}, {"RefNumber": "210687", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "No VBTYP in TAS relevancy check for contract creation", "RefUrl": "/notes/210687"}, {"RefNumber": "210205", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 31-38 incl. 4.0B", "RefUrl": "/notes/210205"}, {"RefNumber": "210204", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - LCP 31-38 incl. 4.0B", "RefUrl": "/notes/210204"}, {"RefNumber": "210030", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR1M:Wrong Update of planned cost with diffrent Inv.Cycle", "RefUrl": "/notes/210030"}, {"RefNumber": "209999", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "SMOD entries OIGMEN01 and OIGMEN02 not available", "RefUrl": "/notes/209999"}, {"RefNumber": "209771", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Wrong invoice amount for account assigned PO items", "RefUrl": "/notes/209771"}, {"RefNumber": "209689", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Check Load ID Determination", "RefUrl": "/notes/209689"}, {"RefNumber": "207657", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Back button crea. entr.in chg Output Det.", "RefUrl": "/notes/207657"}, {"RefNumber": "207609", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "VI11, VI12: Selection of TD shipments don't work properly", "RefUrl": "/notes/207609"}, {"RefNumber": "207504", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR01: Del. costs by Vendor can be invoiced multiple times", "RefUrl": "/notes/207504"}, {"RefNumber": "206287", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "ROIIP-RCBLK and ROIIP-DOBLK missing in Loc. data-> TPS ctrl", "RefUrl": "/notes/206287"}, {"RefNumber": "205759", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Incorrect error message in goods issue log", "RefUrl": "/notes/205759"}, {"RefNumber": "205561", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Refer mat.doc. for correction GR; No check val.typ", "RefUrl": "/notes/205561"}, {"RefNumber": "205300", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees by outline agreement in MR01 does not work", "RefUrl": "/notes/205300"}, {"RefNumber": "204741", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "shortdump when recal. subtotals for rebates", "RefUrl": "/notes/204741"}, {"RefNumber": "204005", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Load ID customer screen handling", "RefUrl": "/notes/204005"}, {"RefNumber": "203605", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Low performance when reading sales contracts", "RefUrl": "/notes/203605"}, {"RefNumber": "203283", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Termination in TAS interface for pick-up's", "RefUrl": "/notes/203283"}, {"RefNumber": "203077", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "GUI Status OIIPBLCT missing", "RefUrl": "/notes/203077"}, {"RefNumber": "202626", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Function Group OIFT missing", "RefUrl": "/notes/202626"}, {"RefNumber": "202267", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Pur. Assignm.Scr:F4 on the PO doc.does not copy the Item No.", "RefUrl": "/notes/202267"}, {"RefNumber": "201913", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Function call conflict in OIK_TPI_ORDERS_CREATE", "RefUrl": "/notes/201913"}, {"RefNumber": "201437", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Avoid program dump when exceptions are raised", "RefUrl": "/notes/201437"}, {"RefNumber": "201383", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "PO Creation - Field Status Problem", "RefUrl": "/notes/201383"}, {"RefNumber": "201309", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Delivery cost credit memo error when crossing year", "RefUrl": "/notes/201309"}, {"RefNumber": "201305", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Euro conversion for fee rates in goods receipt", "RefUrl": "/notes/201305"}, {"RefNumber": "201282", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Receive (new) batch at loading; ASTM error after mvmt o brd", "RefUrl": "/notes/201282"}, {"RefNumber": "201122", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Incorrect goods receipt qty.in 2step transfer", "RefUrl": "/notes/201122"}, {"RefNumber": "200957", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error in handling of EXIT_SAPLOIKS_001", "RefUrl": "/notes/200957"}, {"RefNumber": "200940", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Program to correct wrong quantity schedules (ROIACM00)", "RefUrl": "/notes/200940"}, {"RefNumber": "200816", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD allocation of res. to shipment => short dump", "RefUrl": "/notes/200816"}, {"RefNumber": "200706", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "GUI status OIIPBLCT missing", "RefUrl": "/notes/200706"}, {"RefNumber": "200316", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error in incompletion for mandatory Tank ID", "RefUrl": "/notes/200316"}, {"RefNumber": "199746", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Transaction termination VI200. Item status is missing", "RefUrl": "/notes/199746"}, {"RefNumber": "199541", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Endless loop in IDOC_OUTPUT_OILTPI50", "RefUrl": "/notes/199541"}, {"RefNumber": "199325", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Error message OE 018 during TAS pickup IDOC processing", "RefUrl": "/notes/199325"}, {"RefNumber": "199227", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Load ID customer screen handling", "RefUrl": "/notes/199227"}, {"RefNumber": "198627", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Incorrect shipping point in O4PO", "RefUrl": "/notes/198627"}, {"RefNumber": "198515", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Shortdump when assigning contract to item in sales order", "RefUrl": "/notes/198515"}, {"RefNumber": "198229", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Deleting partners from shipment", "RefUrl": "/notes/198229"}, {"RefNumber": "198085", "RefComponent": "IS-OIL-DS", "RefTitle": "HP upgrade merge error", "RefUrl": "/notes/198085"}, {"RefNumber": "197908", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Wrong date calculation in F&A Pricing after 1999.", "RefUrl": "/notes/197908"}, {"RefNumber": "197905", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Wrong exd posting when currency is set to 1 decimal place", "RefUrl": "/notes/197905"}, {"RefNumber": "197834", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Split excise duty fails for stock transfer order", "RefUrl": "/notes/197834"}, {"RefNumber": "197770", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Ship-to OIFWE in CO-PA not filled for billing trsf.", "RefUrl": "/notes/197770"}, {"RefNumber": "197769", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Statistics update for value items", "RefUrl": "/notes/197769"}, {"RefNumber": "197738", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Brazil: Tax code customizing table changes", "RefUrl": "/notes/197738"}, {"RefNumber": "197470", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Consignment stock fields missing after 4.0B SP1", "RefUrl": "/notes/197470"}, {"RefNumber": "197466", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Message ME218 Net Price has to be greater than 0", "RefUrl": "/notes/197466"}, {"RefNumber": "197182", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Quantity missing from OILSHI01 if CPLID not '1'", "RefUrl": "/notes/197182"}, {"RefNumber": "196890", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Ship-to information not pulled into contracts", "RefUrl": "/notes/196890"}, {"RefNumber": "196306", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Error handling in TAS FM IDOC_INPUT_OILLDD", "RefUrl": "/notes/196306"}, {"RefNumber": "196075", "RefComponent": "IS-OIL-DS", "RefTitle": "Oil-specific messages missing in class VF", "RefUrl": "/notes/196075"}, {"RefNumber": "196054", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/196054"}, {"RefNumber": "196052", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error OC605 occurs incorrectly", "RefUrl": "/notes/196052"}, {"RefNumber": "195707", "RefComponent": "IS-OIL-DS", "RefTitle": "Slow response to delivery create", "RefUrl": "/notes/195707"}, {"RefNumber": "195018", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Underdelivery tolerance 0% not working", "RefUrl": "/notes/195018"}, {"RefNumber": "195009", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Goods receipt with fees into neg. stock errors", "RefUrl": "/notes/195009"}, {"RefNumber": "194642", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Not possible to post LIA for 0 valued material", "RefUrl": "/notes/194642"}, {"RefNumber": "194512", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Load balancing:qty difference in GR & goods issue documents", "RefUrl": "/notes/194512"}, {"RefNumber": "194386", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Archiving DBIF_RSQL_INVALID_CURSOR", "RefUrl": "/notes/194386"}, {"RefNumber": "194327", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Incorrect OIK37 updates via TPI", "RefUrl": "/notes/194327"}, {"RefNumber": "193968", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "O4F1 : Document Weight not assigned to Shipment.", "RefUrl": "/notes/193968"}, {"RefNumber": "193770", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Oil BoM header cannot be deleted", "RefUrl": "/notes/193770"}, {"RefNumber": "193619", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange number missing in netting index table", "RefUrl": "/notes/193619"}, {"RefNumber": "193559", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Rounding Errors in OIGI_CREATE_SHIPMENT_RFC fix", "RefUrl": "/notes/193559"}, {"RefNumber": "193407", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "OII_ARCHIVE_CHECK_CUST_SUBOBJS not found release 31H", "RefUrl": "/notes/193407"}, {"RefNumber": "193381", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Netting does not generate a BTCI session", "RefUrl": "/notes/193381"}, {"RefNumber": "193231", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "ROIKFCL1 functionality missing after Service Pack", "RefUrl": "/notes/193231"}, {"RefNumber": "193229", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Use cust.exit on KNA1 change pointer for updates to SCP OTWS", "RefUrl": "/notes/193229"}, {"RefNumber": "193227", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Abort A222 in OIK_TAS_DATA_POST", "RefUrl": "/notes/193227"}, {"RefNumber": "193225", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "F&A Time UoM routine 004,005 Year Begnining Problem", "RefUrl": "/notes/193225"}, {"RefNumber": "193194", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Goods Receipt Repricing / Excise Duty posting", "RefUrl": "/notes/193194"}, {"RefNumber": "193130", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "TPI: Trip planning", "RefUrl": "/notes/193130"}, {"RefNumber": "193023", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Handling of ETAs for planned shipments", "RefUrl": "/notes/193023"}, {"RefNumber": "193001", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "LIA accounting document missing", "RefUrl": "/notes/193001"}, {"RefNumber": "192527", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "No reference to EXIT_SAPLOIIQ_001", "RefUrl": "/notes/192527"}, {"RefNumber": "192520", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Contracts assigned to an exchange agreement", "RefUrl": "/notes/192520"}, {"RefNumber": "192304", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Rounding Errors in OIGI_CREATE_SHIPMENT_RFC", "RefUrl": "/notes/192304"}, {"RefNumber": "192273", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Collected changes for TPI", "RefUrl": "/notes/192273"}, {"RefNumber": "192046", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No KNA1 change pointer for updates to SCP OTWS", "RefUrl": "/notes/192046"}, {"RefNumber": "192014", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Error OC709 occurs on TPI OTWS entry", "RefUrl": "/notes/192014"}, {"RefNumber": "191923", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect statistical conditions in differential invoice", "RefUrl": "/notes/191923"}, {"RefNumber": "191705", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "TAX REVALUATION WITH MULTIPLE VALUATIONS AND TRANSIT-STOCK", "RefUrl": "/notes/191705"}, {"RefNumber": "191696", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "OILSHI01 IDoc keeps status 64 after processing", "RefUrl": "/notes/191696"}, {"RefNumber": "191432", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Tank ID assignment at order change incorrect", "RefUrl": "/notes/191432"}, {"RefNumber": "191429", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "PBL OTWS entries missing from TPI Location IDoc", "RefUrl": "/notes/191429"}, {"RefNumber": "191413", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Receipts are missing in movement based netting", "RefUrl": "/notes/191413"}, {"RefNumber": "191401", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Action code in OILTPI50 populated incorrectly", "RefUrl": "/notes/191401"}, {"RefNumber": "191154", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "TPI IDocs fail with mixed user defaults", "RefUrl": "/notes/191154"}, {"RefNumber": "191151", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Missing TPI functionality", "RefUrl": "/notes/191151"}, {"RefNumber": "191149", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Abort A222 in OIK_TAS_DATA_POST", "RefUrl": "/notes/191149"}, {"RefNumber": "190248", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Generic price reference plant valuation", "RefUrl": "/notes/190248"}, {"RefNumber": "189648", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Tank ID not available on Shipment Outbound IDocs", "RefUrl": "/notes/189648"}, {"RefNumber": "189505", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Handl.type not def. on change of plant or mat.at material", "RefUrl": "/notes/189505"}, {"RefNumber": "189441", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Performance in billing / unexpected invoice split", "RefUrl": "/notes/189441"}, {"RefNumber": "189339", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "ORA 1562 on OIFPBL in program RVV05IVB", "RefUrl": "/notes/189339"}, {"RefNumber": "188842", "RefComponent": "MM-IM-GF", "RefTitle": "MB03: display external details fails (WA OIDE A)", "RefUrl": "/notes/188842"}, {"RefNumber": "188640", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Contract update terminates if no fee exists", "RefUrl": "/notes/188640"}, {"RefNumber": "188270", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees for goods receipt missing in fee history", "RefUrl": "/notes/188270"}, {"RefNumber": "187744", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TRUOM; ELIKZandLoss; checkMM-LVORM; Missing GI; SetTDaction", "RefUrl": "/notes/187744"}, {"RefNumber": "187195", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Field overflow in LIA transaction", "RefUrl": "/notes/187195"}, {"RefNumber": "187013", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Base Location not consistent between Sales and Purchase", "RefUrl": "/notes/187013"}, {"RefNumber": "186505", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/186505"}, {"RefNumber": "186491", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Search help shows only shipment cost relevant shpm.", "RefUrl": "/notes/186491"}, {"RefNumber": "186408", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Parallel OILLDD inbound processing", "RefUrl": "/notes/186408"}, {"RefNumber": "186341", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Max inventory/Min inventory related WL entries", "RefUrl": "/notes/186341"}, {"RefNumber": "186151", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Shipment inbound process update task", "RefUrl": "/notes/186151"}, {"RefNumber": "185895", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange statement: UoM of F&A condition missing", "RefUrl": "/notes/185895"}, {"RefNumber": "185685", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Bad performance/Short dump for ROIAMMA3", "RefUrl": "/notes/185685"}, {"RefNumber": "185245", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Custmr mastr recrd:Distrbtn of deltd partnr funct.", "RefUrl": "/notes/185245"}, {"RefNumber": "185231", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Incorrect delivered quantity in schedule lines", "RefUrl": "/notes/185231"}, {"RefNumber": "185062", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Unexpected Error-messages after Planning Engine run", "RefUrl": "/notes/185062"}, {"RefNumber": "185010", "RefComponent": "IS-OIL-DS", "RefTitle": "MM_EKKO: archiving run excludes contracts", "RefUrl": "/notes/185010"}, {"RefNumber": "184640", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees by PO picks up previous years GR", "RefUrl": "/notes/184640"}, {"RefNumber": "184532", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Error mess V1227 New pric/ formulae on MM side", "RefUrl": "/notes/184532"}, {"RefNumber": "184295", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Abort message OE 559 in Order Create", "RefUrl": "/notes/184295"}, {"RefNumber": "184259", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Incorrect qty.for batch split main item", "RefUrl": "/notes/184259"}, {"RefNumber": "184209", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "TSW: Missing R/3 Documents in Rundown possible", "RefUrl": "/notes/184209"}, {"RefNumber": "184148", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "SOEK000331: Wrong conversion between sales and base UoM", "RefUrl": "/notes/184148"}, {"RefNumber": "184012", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Movement based netting shows wrong sign", "RefUrl": "/notes/184012"}, {"RefNumber": "183761", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "No fee redetermination in MM contract or order", "RefUrl": "/notes/183761"}, {"RefNumber": "183374", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Timeframe function missing on screen 8024", "RefUrl": "/notes/183374"}, {"RefNumber": "183036", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Message OE 224: STSI calloff can not be deleted manually", "RefUrl": "/notes/183036"}, {"RefNumber": "181955", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange statement for cancelled invoice", "RefUrl": "/notes/181955"}, {"RefNumber": "181711", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Wrong data initialisation in OIK_TD_SHIPMENT_LOAD_PREPARE", "RefUrl": "/notes/181711"}, {"RefNumber": "181652", "RefComponent": "IS-OIL-DS", "RefTitle": "IS-OIL Application Test 4.0B", "RefUrl": "/notes/181652"}, {"RefNumber": "181457", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Load ID assigned with Valid-to = 00:00:0000", "RefUrl": "/notes/181457"}, {"RefNumber": "181437", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Language conversion incorrect for menu option", "RefUrl": "/notes/181437"}, {"RefNumber": "181409", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Negative fees incorrect in movement based netting", "RefUrl": "/notes/181409"}, {"RefNumber": "180596", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Sales tax incorrect on differential invoice creation", "RefUrl": "/notes/180596"}, {"RefNumber": "180084", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - LCP 21-28 incl. 4.0B", "RefUrl": "/notes/180084"}, {"RefNumber": "179868", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "OTWS data not available in views v_oiiotwbl, v_oiiotwkn", "RefUrl": "/notes/179868"}, {"RefNumber": "179813", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Sales UOM conversion wrong in MC for condition record", "RefUrl": "/notes/179813"}, {"RefNumber": "179653", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Short dump in invoice list processing", "RefUrl": "/notes/179653"}, {"RefNumber": "179557", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Pricing date> sy-datum, quotation error routine not trigger.", "RefUrl": "/notes/179557"}, {"RefNumber": "178164", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Screen 200 missing in program", "RefUrl": "/notes/178164"}, {"RefNumber": "178046", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Movement based netting issues", "RefUrl": "/notes/178046"}, {"RefNumber": "177949", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Taxes Brail Oil: New exception  tables sales,puchase,t-fer", "RefUrl": "/notes/177949"}, {"RefNumber": "177866", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Index(VKDFS) is upd. incorr. when cancel diff. invoice", "RefUrl": "/notes/177866"}, {"RefNumber": "177825", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Order distribution action code", "RefUrl": "/notes/177825"}, {"RefNumber": "177563", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Save tank for delivery on shipment inbound", "RefUrl": "/notes/177563"}, {"RefNumber": "177556", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "change communication structure for delete IDoc processing", "RefUrl": "/notes/177556"}, {"RefNumber": "177535", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Cannot cancel from Plant SCP sub-screen", "RefUrl": "/notes/177535"}, {"RefNumber": "176988", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Update termination in worklist engine", "RefUrl": "/notes/176988"}, {"RefNumber": "176909", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "OIPBL missing during sales order call-off", "RefUrl": "/notes/176909"}, {"RefNumber": "176858", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Differential invoice creation in batch processing", "RefUrl": "/notes/176858"}, {"RefNumber": "176785", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Missing SCP component check", "RefUrl": "/notes/176785"}, {"RefNumber": "176741", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Return data from user screen overwritten (OIGD)", "RefUrl": "/notes/176741"}, {"RefNumber": "176647", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Dens.of chem.prod.;Round.HPM;Veh. recon.;O4G1-Err Msg:V0104", "RefUrl": "/notes/176647"}, {"RefNumber": "176589", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "PBL lookup for TPS parameters fails for OILSH01", "RefUrl": "/notes/176589"}, {"RefNumber": "176108", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "LOAD_NO_SPACE_FOR_TABLE for RBDAPP01 logical msg OILLDD", "RefUrl": "/notes/176108"}, {"RefNumber": "175799", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Correction Trip planning", "RefUrl": "/notes/175799"}, {"RefNumber": "175501", "RefComponent": "IS-OIL-DS", "RefTitle": "IS-OIL: Customer master ALE / KNVV records overwritten", "RefUrl": "/notes/175501"}, {"RefNumber": "175494", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Customer Subscreen not accessible in SAPMOIGV", "RefUrl": "/notes/175494"}, {"RefNumber": "175463", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong quantities in CO-PA after cancelling invoice", "RefUrl": "/notes/175463"}, {"RefNumber": "175342", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Brazil taxes: Error on Sales return - ROB", "RefUrl": "/notes/175342"}, {"RefNumber": "175140", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Abend in batch billing", "RefUrl": "/notes/175140"}, {"RefNumber": "175059", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Post parked documents: message 00348", "RefUrl": "/notes/175059"}, {"RefNumber": "175037", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Lia account determination with valuation class", "RefUrl": "/notes/175037"}, {"RefNumber": "175029", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/175029"}, {"RefNumber": "174746", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Qty conversion error when using product proposal", "RefUrl": "/notes/174746"}, {"RefNumber": "174656", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "IS-OIL: Zero qty in PO history of EXG assigned PO", "RefUrl": "/notes/174656"}, {"RefNumber": "174437", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "TDP VALUE CORRECTION WHEN CANCEL AN PO INVOICE", "RefUrl": "/notes/174437"}, {"RefNumber": "173720", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Tables are not archived with archiving object OIG_SHPMNT", "RefUrl": "/notes/173720"}, {"RefNumber": "173715", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD customer function for vehicle overload checks", "RefUrl": "/notes/173715"}, {"RefNumber": "173532", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Order/Contr.: TAS data not filled after delete/cre. item", "RefUrl": "/notes/173532"}, {"RefNumber": "173484", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "SOC Assignment in SD document not working", "RefUrl": "/notes/173484"}, {"RefNumber": "173205", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Selected BOL is not moved into screen field", "RefUrl": "/notes/173205"}, {"RefNumber": "173139", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "TPI: Fix for Time frame settings handling", "RefUrl": "/notes/173139"}, {"RefNumber": "173130", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/173130"}, {"RefNumber": "173079", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Zero billing date is filled in the VKDFS table", "RefUrl": "/notes/173079"}, {"RefNumber": "172759", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "output determination: header or detail(scheduling)", "RefUrl": "/notes/172759"}, {"RefNumber": "172738", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Functionality for Time frame settings at IMG", "RefUrl": "/notes/172738"}, {"RefNumber": "172681", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "TSW : correction for marine transport system in Nomination", "RefUrl": "/notes/172681"}, {"RefNumber": "172527", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ASTM Calculation doesn't work correctly with ASTM Interface", "RefUrl": "/notes/172527"}, {"RefNumber": "172412", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Confirm status; Error message E752; Gain/loss mvmt o b", "RefUrl": "/notes/172412"}, {"RefNumber": "172212", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD compartment user screens: Selected compartments lost", "RefUrl": "/notes/172212"}, {"RefNumber": "172063", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Mb netting: Wrong invoice cycle determined", "RefUrl": "/notes/172063"}, {"RefNumber": "172009", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Delete shipment + Nast entry", "RefUrl": "/notes/172009"}, {"RefNumber": "171871", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "IS-Oil BDRP Sales Hours not read", "RefUrl": "/notes/171871"}, {"RefNumber": "171736", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Nota fiscal with multiple items / Brazil Oil", "RefUrl": "/notes/171736"}, {"RefNumber": "171391", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/171391"}, {"RefNumber": "171364", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Incorrect Qty in base UoM in Inv.verification", "RefUrl": "/notes/171364"}, {"RefNumber": "171086", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "TPI: Collective corrections after Acceptance test", "RefUrl": "/notes/171086"}, {"RefNumber": "171065", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Collective corrections after Acceptance test", "RefUrl": "/notes/171065"}, {"RefNumber": "171053", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Posting dates in one period; Zero lines in loading", "RefUrl": "/notes/171053"}, {"RefNumber": "171037", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Billing cancellation error.", "RefUrl": "/notes/171037"}, {"RefNumber": "170968", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Fieldsselection texts missing in RM07MMAT", "RefUrl": "/notes/170968"}, {"RefNumber": "170926", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "OIK37 number ranges maintenance", "RefUrl": "/notes/170926"}, {"RefNumber": "170871", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL BRAZILIAN TAX SOLUTION FOR TRANSFER 833/835", "RefUrl": "/notes/170871"}, {"RefNumber": "170822", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Missing copy of some client independent object items", "RefUrl": "/notes/170822"}, {"RefNumber": "170803", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Outbound IDoc OILORD02: E1EDP01-ANTLF not filled", "RefUrl": "/notes/170803"}, {"RefNumber": "170668", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Bills of material in exchanges", "RefUrl": "/notes/170668"}, {"RefNumber": "170620", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: EXG fee UoM missing in HPM quantity conversion", "RefUrl": "/notes/170620"}, {"RefNumber": "170314", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Error in redetermination of netting statement", "RefUrl": "/notes/170314"}, {"RefNumber": "170287", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Rounding errors in invoice verification", "RefUrl": "/notes/170287"}, {"RefNumber": "170066", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fixed value fees are incorrect in the invoice", "RefUrl": "/notes/170066"}, {"RefNumber": "169833", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MAP: Error in Pricing date during GR repricing", "RefUrl": "/notes/169833"}, {"RefNumber": "169295", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Wrong pick qty. after TRX VL16", "RefUrl": "/notes/169295"}, {"RefNumber": "169283", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: TAS index for GI not deleted if delivery deleted", "RefUrl": "/notes/169283"}, {"RefNumber": "169228", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Error during DELETE OIKLIDR", "RefUrl": "/notes/169228"}, {"RefNumber": "169160", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Corrections in SVPs: 3.1H/SVP3 4.0B/SVP1", "RefUrl": "/notes/169160"}, {"RefNumber": "168765", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Fields not updated for oil BoM header", "RefUrl": "/notes/168765"}, {"RefNumber": "168381", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Billing index entries missing after RVV05IVB", "RefUrl": "/notes/168381"}, {"RefNumber": "168313", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Customizing control for message O1 023", "RefUrl": "/notes/168313"}, {"RefNumber": "167025", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Dynamic F&A repository selection screen", "RefUrl": "/notes/167025"}, {"RefNumber": "166548", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exg. statement: reversal printed in wrong period", "RefUrl": "/notes/166548"}, {"RefNumber": "166169", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees lost during cancellation of billing document", "RefUrl": "/notes/166169"}, {"RefNumber": "165871", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Auto compartment planning with compatibilities", "RefUrl": "/notes/165871"}, {"RefNumber": "164252", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Delete NAST entries for delete shipments", "RefUrl": "/notes/164252"}, {"RefNumber": "163448", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Quantity schedule: Conversion error", "RefUrl": "/notes/163448"}, {"RefNumber": "160349", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/160349"}, {"RefNumber": "158344", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD: Gain on 2-step trf. and GR to mult. store locations", "RefUrl": "/notes/158344"}, {"RefNumber": "157975", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Not possible to maintain posting keys NTB,NTK...", "RefUrl": "/notes/157975"}, {"RefNumber": "157920", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "VKDFS table is not filled properly during invoice generation", "RefUrl": "/notes/157920"}, {"RefNumber": "157801", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Loss in transfer/STO and change of valuation type", "RefUrl": "/notes/157801"}, {"RefNumber": "157768", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect subtotal calculation type with F&A condn", "RefUrl": "/notes/157768"}, {"RefNumber": "157569", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Errors in repricing in invoice verification", "RefUrl": "/notes/157569"}, {"RefNumber": "157562", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/157562"}, {"RefNumber": "157494", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Location ID in Credit note and Credit note request", "RefUrl": "/notes/157494"}, {"RefNumber": "157044", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Exit from step-loop 00043 in scheduling", "RefUrl": "/notes/157044"}, {"RefNumber": "156993", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD User Exit 007 before creation of MM document", "RefUrl": "/notes/156993"}, {"RefNumber": "156906", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Abort on Location Archiving Deletion program", "RefUrl": "/notes/156906"}, {"RefNumber": "156700", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong UoM in automatically created purchase order", "RefUrl": "/notes/156700"}, {"RefNumber": "156697", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "If taxes are 0,accounting error in differential Inv", "RefUrl": "/notes/156697"}, {"RefNumber": "156599", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Document item quantity assign. missing O9540", "RefUrl": "/notes/156599"}, {"RefNumber": "156403", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Bug in routine XKOMV_BEWERTEN", "RefUrl": "/notes/156403"}, {"RefNumber": "156237", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD 'Delete event' in maintain completed shipment", "RefUrl": "/notes/156237"}, {"RefNumber": "155914", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Printing exchange statements using RSNAST00", "RefUrl": "/notes/155914"}, {"RefNumber": "155337", "RefComponent": "IS-OIL-DS", "RefTitle": "IS-Oil: Material Master Screen Seqence Selection", "RefUrl": "/notes/155337"}, {"RefNumber": "154841", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD temperature UoM in popup/multiple MM docs per transaction", "RefUrl": "/notes/154841"}, {"RefNumber": "154790", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Allow non-movement delivery items in TD shipments", "RefUrl": "/notes/154790"}, {"RefNumber": "154538", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Error in holiday determination in F&A pricing", "RefUrl": "/notes/154538"}, {"RefNumber": "154200", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Bad runtime while MSEG-access in quantity schedule", "RefUrl": "/notes/154200"}, {"RefNumber": "154005", "RefComponent": "CA-JVA", "RefTitle": "More than one downpayment clearing", "RefUrl": "/notes/154005"}, {"RefNumber": "153222", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "external details at stock transfer order", "RefUrl": "/notes/153222"}, {"RefNumber": "153121", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Scrolling SAPMOIGS3700; take over dates after user exit", "RefUrl": "/notes/153121"}, {"RefNumber": "152777", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/152777"}, {"RefNumber": "152678", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Cost sharing on document item level", "RefUrl": "/notes/152678"}, {"RefNumber": "151721", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fee edit/copy control fields are missing", "RefUrl": "/notes/151721"}, {"RefNumber": "151361", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Missing exg. number on billing index for diff. inv.", "RefUrl": "/notes/151361"}, {"RefNumber": "150951", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Missing overload check in compartment allocation details", "RefUrl": "/notes/150951"}, {"RefNumber": "150548", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Errors in IMG for shipment costing", "RefUrl": "/notes/150548"}, {"RefNumber": "150318", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Split excise duty may cause wrong IR postings", "RefUrl": "/notes/150318"}, {"RefNumber": "150132", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: credit management: value update problem", "RefUrl": "/notes/150132"}, {"RefNumber": "149949", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - LCP 11, 4.0B", "RefUrl": "/notes/149949"}, {"RefNumber": "147813", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "ME31: external details not copied", "RefUrl": "/notes/147813"}, {"RefNumber": "146147", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MAP-Customer price list does not work for item lev.", "RefUrl": "/notes/146147"}, {"RefNumber": "145850", "RefComponent": "IS-OIL-BC", "RefTitle": "Order of IS-OIL notes 4.0b on basis R/3 4.0B (SP)", "RefUrl": "/notes/145850"}, {"RefNumber": "143644", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Error with UoM processing in TD Vehicle creation", "RefUrl": "/notes/143644"}, {"RefNumber": "143233", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD missing gain; add tracking; ASTM +/- qty; no OIGSM", "RefUrl": "/notes/143233"}, {"RefNumber": "142981", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD ASTM and 0 qty; reason code; excise duty value", "RefUrl": "/notes/142981"}, {"RefNumber": "142387", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Change proposal at load/delivery confirmation", "RefUrl": "/notes/142387"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "111268", "RefComponent": "PP-REM-ADE", "RefTitle": "MF4A, MF4U: Display material documents, return", "RefUrl": "/notes/111268"}, {"RefNumber": "107344", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD partner-specific output determination", "RefUrl": "/notes/107344"}, {"RefNumber": "102306", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong values in statistics update", "RefUrl": "/notes/102306"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "396242", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Dump in TD bulk shp in OIB2_TD_GET_MAT_TEMP by NO_RECORD exc", "RefUrl": "/notes/396242 "}, {"RefNumber": "522365", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "TAS incompletion log appears for a non TAS relevant order", "RefUrl": "/notes/522365 "}, {"RefNumber": "181652", "RefComponent": "IS-OIL-DS", "RefTitle": "IS-OIL Application Test 4.0B", "RefUrl": "/notes/181652 "}, {"RefNumber": "314778", "RefComponent": "IS-OIL-DS-QCI", "RefTitle": "IS OIL: QCI: Chemicals and TAS; BSW conversion; BAdI methods", "RefUrl": "/notes/314778 "}, {"RefNumber": "400863", "RefComponent": "IS-OIL", "RefTitle": "OIL: Problems deleting E&P Hierarchies", "RefUrl": "/notes/400863 "}, {"RefNumber": "458906", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 66-68 incl. 4.0B", "RefUrl": "/notes/458906 "}, {"RefNumber": "434924", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 63-65 incl. 4.0B", "RefUrl": "/notes/434924 "}, {"RefNumber": "210204", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - LCP 31-38 incl. 4.0B", "RefUrl": "/notes/210204 "}, {"RefNumber": "351482", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - LCP 45-53 incl. 4.0B", "RefUrl": "/notes/351482 "}, {"RefNumber": "448020", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Double update of IDOCS in parallel inbound & Idoc locking", "RefUrl": "/notes/448020 "}, {"RefNumber": "364644", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Shipment change on TPI planning screen deletes carrier", "RefUrl": "/notes/364644 "}, {"RefNumber": "357343", "RefComponent": "IS-OIL-DS-QCI", "RefTitle": "Session locked by qty conversion for 0 density/rel. density", "RefUrl": "/notes/357343 "}, {"RefNumber": "375158", "RefComponent": "BC-UPG-ADDON", "RefTitle": "IS-OIL: Additional Info - SPs 54-56 incl. 4.0B", "RefUrl": "/notes/375158 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "332654", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL / IS-MINE / IS-CWM: Overview of SAP Notes", "RefUrl": "/notes/332654 "}, {"RefNumber": "322765", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "BOM comp.qty;rebr. batch PtL;check batch STO;scroll on 3700", "RefUrl": "/notes/322765 "}, {"RefNumber": "86241", "RefComponent": "PY", "RefTitle": "Legal Change Patches / Support Packages for HR", "RefUrl": "/notes/86241 "}, {"RefNumber": "334872", "RefComponent": "BC-UPG-OCS", "RefTitle": "Separation LCPs HR/Hot Packages in Release 4.0B", "RefUrl": "/notes/334872 "}, {"RefNumber": "323761", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Problem with exchange number when creating sales contracts", "RefUrl": "/notes/323761 "}, {"RefNumber": "397831", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 57-60 incl. 4.0B", "RefUrl": "/notes/397831 "}, {"RefNumber": "421873", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Delivery weight & volume not updated properly in delivery", "RefUrl": "/notes/421873 "}, {"RefNumber": "327364", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Incorrect quantity in SIS info structures", "RefUrl": "/notes/327364 "}, {"RefNumber": "53136", "RefComponent": "IS-OIL-BC", "RefTitle": "Support Packages and IS-Oil / IS-MINE / IS-CWM - information", "RefUrl": "/notes/53136 "}, {"RefNumber": "77407", "RefComponent": "IS-OIL-BC", "RefTitle": "CRTs for IS-Oil", "RefUrl": "/notes/77407 "}, {"RefNumber": "93571", "RefComponent": "IS-OIL", "RefTitle": "Sequence of corrections - IS-Oil / IS-MINE Policy", "RefUrl": "/notes/93571 "}, {"RefNumber": "145850", "RefComponent": "IS-OIL-BC", "RefTitle": "Order of IS-OIL notes 4.0b on basis R/3 4.0B (SP)", "RefUrl": "/notes/145850 "}, {"RefNumber": "421035", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Rollback while deleting archived stock entries", "RefUrl": "/notes/421035 "}, {"RefNumber": "510529", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "To update VBKD-OIT<PERSON>LE when VBKD-INCO1 is changed.", "RefUrl": "/notes/510529 "}, {"RefNumber": "508909", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External details fields not getting updated in LIKP", "RefUrl": "/notes/508909 "}, {"RefNumber": "390503", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Billing docs with zero qty for already billed SD documents.", "RefUrl": "/notes/390503 "}, {"RefNumber": "454007", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Group conditions and other cond cumulated value with 431", "RefUrl": "/notes/454007 "}, {"RefNumber": "487370", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Short dump at O4G1-Balance Loading after loading correction", "RefUrl": "/notes/487370 "}, {"RefNumber": "67261", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Reports to analyze and correct stock qties in IS-OIL systems", "RefUrl": "/notes/67261 "}, {"RefNumber": "443421", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Archiving of shipments", "RefUrl": "/notes/443421 "}, {"RefNumber": "527924", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Customizing the message type for TD message O9(747)", "RefUrl": "/notes/527924 "}, {"RefNumber": "519259", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Customizing the message type for TD message class O9(078).", "RefUrl": "/notes/519259 "}, {"RefNumber": "184259", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Incorrect qty.for batch split main item", "RefUrl": "/notes/184259 "}, {"RefNumber": "416839", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Performance improvement loading and delivery confirmation", "RefUrl": "/notes/416839 "}, {"RefNumber": "317019", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External details not copied to batch split items", "RefUrl": "/notes/317019 "}, {"RefNumber": "504601", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Vehicle status change from 6 to 5 in O4L4.", "RefUrl": "/notes/504601 "}, {"RefNumber": "458705", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: VPRS Re-Determination when non TDP material", "RefUrl": "/notes/458705 "}, {"RefNumber": "504889", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong delivery costs proposed for invoice verification", "RefUrl": "/notes/504889 "}, {"RefNumber": "312369", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Long runtimes when archiving with FI_DOCUMNT", "RefUrl": "/notes/312369 "}, {"RefNumber": "322879", "RefComponent": "IS-OIL-BC", "RefTitle": "SAPMM07M,..: Too many DATA control blocks / generation error", "RefUrl": "/notes/322879 "}, {"RefNumber": "424163", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Cancel goods issue: no entry in OIAQB created", "RefUrl": "/notes/424163 "}, {"RefNumber": "300629", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Unnecessary messages in Tank defaulting", "RefUrl": "/notes/300629 "}, {"RefNumber": "440799", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Purchase orders without any costs can't be processed via ERS", "RefUrl": "/notes/440799 "}, {"RefNumber": "400071", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Corrections to OIK_CHANGE_ORDER", "RefUrl": "/notes/400071 "}, {"RefNumber": "156237", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD 'Delete event' in maintain completed shipment", "RefUrl": "/notes/156237 "}, {"RefNumber": "411898", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Cancel credit memo raises message", "RefUrl": "/notes/411898 "}, {"RefNumber": "525712", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External Details Button rises error M7 001", "RefUrl": "/notes/525712 "}, {"RefNumber": "494571", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "F&A tables not adjusted properly for multiple F&A conditions", "RefUrl": "/notes/494571 "}, {"RefNumber": "403275", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Shortdump when posting netting document with many items", "RefUrl": "/notes/403275 "}, {"RefNumber": "381741", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Mtr rdg; Part.conf.interf.; Load reserv.; Del.conf.<PERSON>ot", "RefUrl": "/notes/381741 "}, {"RefNumber": "406047", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Mvt type 851/852-LIS, no value; VL09; QCI in mining", "RefUrl": "/notes/406047 "}, {"RefNumber": "418672", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "851/852 for real batch; STO VBFA; batch in conversion; Lov=0", "RefUrl": "/notes/418672 "}, {"RefNumber": "439036", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "BW wrong load; delete OIGSM; load interface; delete dcmt.", "RefUrl": "/notes/439036 "}, {"RefNumber": "481675", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Dlvy qty changed;Rounding;VBRP-CHARG;O9 340;QCI temperat.", "RefUrl": "/notes/481675 "}, {"RefNumber": "488163", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "One OIGSM; Cancel ASTM; Batch in 851", "RefUrl": "/notes/488163 "}, {"RefNumber": "518617", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Problem with OILNOM01", "RefUrl": "/notes/518617 "}, {"RefNumber": "355588", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error handling in transaction O4PO", "RefUrl": "/notes/355588 "}, {"RefNumber": "489034", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Set Screen error while creating sales order from a contract", "RefUrl": "/notes/489034 "}, {"RefNumber": "500411", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Split invoicing adds up the invoiced quantity.", "RefUrl": "/notes/500411 "}, {"RefNumber": "503197", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "D-SCM-EX:Call off qty > schedule qty", "RefUrl": "/notes/503197 "}, {"RefNumber": "505973", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Invoice getting blocked on ERS or Invoice Verification.", "RefUrl": "/notes/505973 "}, {"RefNumber": "518883", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong proposal of Delivery Costs for Weight Based Products", "RefUrl": "/notes/518883 "}, {"RefNumber": "335955", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "GR: Field RM07M-LFSNR is not filled from MKPF-XBLNR", "RefUrl": "/notes/335955 "}, {"RefNumber": "447778", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Missing delivery note and bill of loading in a material doc.", "RefUrl": "/notes/447778 "}, {"RefNumber": "398764", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Incorrect weights and volumes in delivery", "RefUrl": "/notes/398764 "}, {"RefNumber": "197466", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Message ME218 Net Price has to be greater than 0", "RefUrl": "/notes/197466 "}, {"RefNumber": "482204", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect entries in M_VMCFA for differential invoices", "RefUrl": "/notes/482204 "}, {"RefNumber": "412012", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exg.data/fees aren't copied at cred-memo creation/cancel.", "RefUrl": "/notes/412012 "}, {"RefNumber": "413635", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Manual pricing conditions are repriced for oil BoM", "RefUrl": "/notes/413635 "}, {"RefNumber": "445150", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Unable to change pricing date on fee screen in billing", "RefUrl": "/notes/445150 "}, {"RefNumber": "508153", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Billing index update problems", "RefUrl": "/notes/508153 "}, {"RefNumber": "482457", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "OIEXGNUM lost in table VKDFS in billing due list run", "RefUrl": "/notes/482457 "}, {"RefNumber": "442303", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Billing index update problems", "RefUrl": "/notes/442303 "}, {"RefNumber": "210205", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 31-38 incl. 4.0B", "RefUrl": "/notes/210205 "}, {"RefNumber": "351475", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 45-53 incl. 4.0B", "RefUrl": "/notes/351475 "}, {"RefNumber": "180084", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - LCP 21-28 incl. 4.0B", "RefUrl": "/notes/180084 "}, {"RefNumber": "487243", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Update of MBEW during gain/loss does not occur", "RefUrl": "/notes/487243 "}, {"RefNumber": "483461", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "LIA item amount changes when using some currencies", "RefUrl": "/notes/483461 "}, {"RefNumber": "518541", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Material valuation type not copied to settlement rule object", "RefUrl": "/notes/518541 "}, {"RefNumber": "392647", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "EKAB updated wrongly during goods receipt in MB01 or MIGO", "RefUrl": "/notes/392647 "}, {"RefNumber": "484852", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Incorrect error in Subcontracting Subsequent Adjustment", "RefUrl": "/notes/484852 "}, {"RefNumber": "487973", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Performance problem in material master - Oil view", "RefUrl": "/notes/487973 "}, {"RefNumber": "492894", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "PO history incorrect after GR & IR for mult. acct assg", "RefUrl": "/notes/492894 "}, {"RefNumber": "498128", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Deletion of SD delivery through TPI Interface or Txn O4PO", "RefUrl": "/notes/498128 "}, {"RefNumber": "514381", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "E1OILBH-SHPPPT (Transportation planning point) not filled", "RefUrl": "/notes/514381 "}, {"RefNumber": "517617", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Exclude Event date indicator functionality is not working", "RefUrl": "/notes/517617 "}, {"RefNumber": "513542", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "OIL: O4NC Event Log-not available in create mode for default", "RefUrl": "/notes/513542 "}, {"RefNumber": "513514", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Missing export parameter in Function OII_OTWS_GET_REF_SET", "RefUrl": "/notes/513514 "}, {"RefNumber": "512517", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "OIL: O4NV  Need to change Shipper due is diplay only.", "RefUrl": "/notes/512517 "}, {"RefNumber": "511255", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect valuation for exchange receipts", "RefUrl": "/notes/511255 "}, {"RefNumber": "510675", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "OIL: Rename functionality changes entries in database", "RefUrl": "/notes/510675 "}, {"RefNumber": "508655", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "OIL: With reference to note n°487703 - 2", "RefUrl": "/notes/508655 "}, {"RefNumber": "508312", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Consideration for unlimited indicator LIPS-UEBTK in Delivery", "RefUrl": "/notes/508312 "}, {"RefNumber": "508237", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Error message O1 033 at changing contract QS header", "RefUrl": "/notes/508237 "}, {"RefNumber": "508109", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "O4F1 short dump in Document selection F4 due to W message", "RefUrl": "/notes/508109 "}, {"RefNumber": "443575", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Excise duty balance calc incorrect when gain/loss", "RefUrl": "/notes/443575 "}, {"RefNumber": "503839", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "OIL: Rename function in O4NC or O4NV", "RefUrl": "/notes/503839 "}, {"RefNumber": "503655", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "OIL:O4NC Event Log- not available in create mode for defaul", "RefUrl": "/notes/503655 "}, {"RefNumber": "502650", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Already cleared fees shouldn't be displayed", "RefUrl": "/notes/502650 "}, {"RefNumber": "502622", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Message no. OB 007 when calling off from purchase contract", "RefUrl": "/notes/502622 "}, {"RefNumber": "500915", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fee copy rules cannot be maintained for SD documents", "RefUrl": "/notes/500915 "}, {"RefNumber": "500499", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Bad performance of ROIAMMA3/ROIAMMAT if no documents found", "RefUrl": "/notes/500499 "}, {"RefNumber": "500211", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Problems in OIAFE with postings different years (LIV)", "RefUrl": "/notes/500211 "}, {"RefNumber": "498506", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "O4D2 batch input wrong exclusion of FCODE DELE", "RefUrl": "/notes/498506 "}, {"RefNumber": "498094", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: VF01 using different curencies E-KE 476", "RefUrl": "/notes/498094 "}, {"RefNumber": "498005", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Material Group in Load ID determination", "RefUrl": "/notes/498005 "}, {"RefNumber": "495860", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Entry of text id in configuration of TPI interface", "RefUrl": "/notes/495860 "}, {"RefNumber": "495547", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "BW, FI-SL, CO-PA and SIS quantities missing in SD invoices", "RefUrl": "/notes/495547 "}, {"RefNumber": "490801", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "O3AB: Fee repricing causes wrong formula description", "RefUrl": "/notes/490801 "}, {"RefNumber": "488559", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Netting proposal not selecting all documents", "RefUrl": "/notes/488559 "}, {"RefNumber": "487328", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "UOM conversion not drawn from the MM contract or info record", "RefUrl": "/notes/487328 "}, {"RefNumber": "517380", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Coll. Note - MCHA and MCHB checks do not allow load.conf.", "RefUrl": "/notes/517380 "}, {"RefNumber": "522282", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "ERS creates inconsistent fee table entries (OIANF/OIAFE)", "RefUrl": "/notes/522282 "}, {"RefNumber": "485940", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Performance Improvements for O3A7 with Posting Date", "RefUrl": "/notes/485940 "}, {"RefNumber": "507291", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Quantity unit in RMCSS003", "RefUrl": "/notes/507291 "}, {"RefNumber": "528008", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "MOIJNF01_COPY_REF_NOM_LINES", "RefUrl": "/notes/528008 "}, {"RefNumber": "517713", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Corrections to FM OIK_TAS_DATA_MAINTAIN", "RefUrl": "/notes/517713 "}, {"RefNumber": "524350", "RefComponent": "IS-OIL", "RefTitle": "Balancing Workplace IMG Activity Documentation", "RefUrl": "/notes/524350 "}, {"RefNumber": "482323", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong quantity in the QS after rejecting a sales order", "RefUrl": "/notes/482323 "}, {"RefNumber": "481379", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "LIV ERS not create fee credit memo for prior year (part2)", "RefUrl": "/notes/481379 "}, {"RefNumber": "480639", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Incorrect stock qty/value in material master", "RefUrl": "/notes/480639 "}, {"RefNumber": "459951", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Autom. compartment allocation before exchange assignment", "RefUrl": "/notes/459951 "}, {"RefNumber": "458242", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "ALE for Customer Master / IS/Oil Data (OILDEB)", "RefUrl": "/notes/458242 "}, {"RefNumber": "458144", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Selection lost on scroll in Exchange contract copy", "RefUrl": "/notes/458144 "}, {"RefNumber": "451394", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees in delivery multiplied by thousand", "RefUrl": "/notes/451394 "}, {"RefNumber": "451483", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange number is cleared when changing material document", "RefUrl": "/notes/451483 "}, {"RefNumber": "451521", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Support extended IDOCs during IDOC creation", "RefUrl": "/notes/451521 "}, {"RefNumber": "454030", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Wrong text on Txns O4P7 and O4P8", "RefUrl": "/notes/454030 "}, {"RefNumber": "455166", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Rounding 2step;Abend MSEGO2;Don't change temp;Wrong sign G/L", "RefUrl": "/notes/455166 "}, {"RefNumber": "455631", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "O3B3 Exg doesn't get sender address", "RefUrl": "/notes/455631 "}, {"RefNumber": "455317", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Wrong cursor position in the OIL 40B SO overview screen", "RefUrl": "/notes/455317 "}, {"RefNumber": "448454", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "O4TF: LIST OF TICKETS A<PERSON><PERSON><PERSON>LE INCLUDES DELETED TICKETS", "RefUrl": "/notes/448454 "}, {"RefNumber": "364919", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Inbound shipment interface abends on delivery create error", "RefUrl": "/notes/364919 "}, {"RefNumber": "365418", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "G_OIKIMPORT struct not populated in SD_SALES_ITEM_MAINTAIN", "RefUrl": "/notes/365418 "}, {"RefNumber": "368749", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MBST:Wrong F&A condition value displayed in PO history", "RefUrl": "/notes/368749 "}, {"RefNumber": "491979", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Inactive F&A condition results in incorrect billing due list", "RefUrl": "/notes/491979 "}, {"RefNumber": "490648", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Dumps DYNPRO_SEND_IN_BACKGROUND using ROIKPIPR", "RefUrl": "/notes/490648 "}, {"RefNumber": "489894", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MIRO Invoice Repricing uses incorrect pricing date", "RefUrl": "/notes/489894 "}, {"RefNumber": "488493", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MRRS is dumping due to fiscal year issue", "RefUrl": "/notes/488493 "}, {"RefNumber": "488197", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Already cleared fees are proposed during MR01", "RefUrl": "/notes/488197 "}, {"RefNumber": "485680", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect Values in Exchange Statement with Credit Memos", "RefUrl": "/notes/485680 "}, {"RefNumber": "484461", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Stock transfer order checking issuing valuation", "RefUrl": "/notes/484461 "}, {"RefNumber": "483325", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Far East license tracking and transfer movements", "RefUrl": "/notes/483325 "}, {"RefNumber": "519739", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "(O4TE) Ticket Create -  showing closed noms", "RefUrl": "/notes/519739 "}, {"RefNumber": "519228", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IBS OIL&GAS LOCALIZATION BRAZIL ST Calculation Interstate", "RefUrl": "/notes/519228 "}, {"RefNumber": "519268", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IBS OIL&GAS LOCALIZATION BRAZIL THIRD NOTA FISCAL", "RefUrl": "/notes/519268 "}, {"RefNumber": "519282", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IBS OIL&GAS LOCALIZATION BRAZIL LOST IS-OIL DATA DURING IV", "RefUrl": "/notes/519282 "}, {"RefNumber": "508135", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "TIMEOUT Error while reading the quotations", "RefUrl": "/notes/508135 "}, {"RefNumber": "516995", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Buffer overflow on OIKPEXORD", "RefUrl": "/notes/516995 "}, {"RefNumber": "498507", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Status handling in TD-Scheduling for zero quantity items", "RefUrl": "/notes/498507 "}, {"RefNumber": "337560", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Credit memo request with reference to invoice.", "RefUrl": "/notes/337560 "}, {"RefNumber": "508499", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD-O4H1/STO enabling new batch creation in receiving plant", "RefUrl": "/notes/508499 "}, {"RefNumber": "200940", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Program to correct wrong quantity schedules (ROIACM00)", "RefUrl": "/notes/200940 "}, {"RefNumber": "492265", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Netting fails with O1 546 for cross-company code LIVs", "RefUrl": "/notes/492265 "}, {"RefNumber": "497803", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect VKDFS entry proposed in diff. invoice cancelation", "RefUrl": "/notes/497803 "}, {"RefNumber": "337217", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Status Handling enhancement during Scheduling", "RefUrl": "/notes/337217 "}, {"RefNumber": "495893", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Using FCODE OIDX with a SD doc. cat. <> C gives no error", "RefUrl": "/notes/495893 "}, {"RefNumber": "326517", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "VA01 TAS relevancy checks hinder order entry performance", "RefUrl": "/notes/326517 "}, {"RefNumber": "323129", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "LID internal numbering not checking OIKLID for duplicates", "RefUrl": "/notes/323129 "}, {"RefNumber": "318707", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "OILLDD IDOC posts w/out ASTM conv is density out of range", "RefUrl": "/notes/318707 "}, {"RefNumber": "318040", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "'AW' partners not available in EXIT_SAPLOIK7_130 user exit", "RefUrl": "/notes/318040 "}, {"RefNumber": "420585", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Compartment Planning disabling NOT possible", "RefUrl": "/notes/420585 "}, {"RefNumber": "351931", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Abend O1 544 on Delivery confirmation", "RefUrl": "/notes/351931 "}, {"RefNumber": "492151", "RefComponent": "IS-OIL", "RefTitle": "VF04: Nota fiscal simulation does not consider BOM's", "RefUrl": "/notes/492151 "}, {"RefNumber": "486076", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Display accounting doc.: weird text on push button", "RefUrl": "/notes/486076 "}, {"RefNumber": "384804", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MAP: Value without quotation in second level analys", "RefUrl": "/notes/384804 "}, {"RefNumber": "486504", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "S067 updated incorrectly during billing", "RefUrl": "/notes/486504 "}, {"RefNumber": "440005", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Wrong value for MSEG-MENGE for cancellation", "RefUrl": "/notes/440005 "}, {"RefNumber": "315968", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "IS-Oil reference note", "RefUrl": "/notes/315968 "}, {"RefNumber": "460234", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Excise License Validiy Dates", "RefUrl": "/notes/460234 "}, {"RefNumber": "459740", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Autom. compartment allocation before exchange assignment", "RefUrl": "/notes/459740 "}, {"RefNumber": "452122", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Nota fiscal compl. does not consider user condition types", "RefUrl": "/notes/452122 "}, {"RefNumber": "458055", "RefComponent": "IS-OIL-DS", "RefTitle": "Checks for IS-Oil fields in access sequence", "RefUrl": "/notes/458055 "}, {"RefNumber": "446350", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External Details in the goods movement 301, 309 and 311.", "RefUrl": "/notes/446350 "}, {"RefNumber": "438549", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fee copy controls missing in billing to order copy control", "RefUrl": "/notes/438549 "}, {"RefNumber": "360432", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Error OB 011 create PO with ref to requisition", "RefUrl": "/notes/360432 "}, {"RefNumber": "446038", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Program termination during BW contract data upload", "RefUrl": "/notes/446038 "}, {"RefNumber": "453208", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Short Dump in 40b after application of new kernel", "RefUrl": "/notes/453208 "}, {"RefNumber": "453243", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Transport Unit compartment data customer defined screen", "RefUrl": "/notes/453243 "}, {"RefNumber": "431050", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Returns not Updating License Quantity Tracking", "RefUrl": "/notes/431050 "}, {"RefNumber": "451134", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Mass update of contract prices and external ED pricing key", "RefUrl": "/notes/451134 "}, {"RefNumber": "392666", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "SAPSQL_ARRAY_INSERT_DUPREC in ME_UPDATE_DOCUMENT", "RefUrl": "/notes/392666 "}, {"RefNumber": "409524", "RefComponent": "IS-OIL-DS", "RefTitle": "QA cleanup TODO/SAMT for support systems", "RefUrl": "/notes/409524 "}, {"RefNumber": "445741", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Report Find Shipments", "RefUrl": "/notes/445741 "}, {"RefNumber": "409269", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "F&A Formula is not deleted to the customizing table", "RefUrl": "/notes/409269 "}, {"RefNumber": "447012", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "OIL:Change tkt with same name as deleted tkt/NOMIT not displ", "RefUrl": "/notes/447012 "}, {"RefNumber": "441786", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Display Error in SAPMOIGS-3200 when calling from 3700", "RefUrl": "/notes/441786 "}, {"RefNumber": "433834", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Internal work areas defined in RVINVB00, RVINVB10 are small.", "RefUrl": "/notes/433834 "}, {"RefNumber": "402177", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Shortdmp \"Field symbol not assigned\" when leaving fee dialog", "RefUrl": "/notes/402177 "}, {"RefNumber": "442657", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong german texts in sales documents", "RefUrl": "/notes/442657 "}, {"RefNumber": "438767", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Invoice print performance and memory problem", "RefUrl": "/notes/438767 "}, {"RefNumber": "428275", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "KE4S fails when loading invoice with fee total condition", "RefUrl": "/notes/428275 "}, {"RefNumber": "438098", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong display of values in SAPF124", "RefUrl": "/notes/438098 "}, {"RefNumber": "438227", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Billing: fee total condition differs from fee sum", "RefUrl": "/notes/438227 "}, {"RefNumber": "436699", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "LIAs are not properly deriving Profit Center", "RefUrl": "/notes/436699 "}, {"RefNumber": "432574", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Quantity schedule on a purchase order is wrong  (TD related)", "RefUrl": "/notes/432574 "}, {"RefNumber": "356301", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Borrow/Loan exchange posting to Purchase Acct. Mgt.", "RefUrl": "/notes/356301 "}, {"RefNumber": "429059", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Short dump when creating Call-off against Contract", "RefUrl": "/notes/429059 "}, {"RefNumber": "369591", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Display ED licenses during sales order", "RefUrl": "/notes/369591 "}, {"RefNumber": "429002", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "CO-PA charact. OIFWE: not filled properly in Invoice", "RefUrl": "/notes/429002 "}, {"RefNumber": "427847", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong fee copy at good receipt cancellation", "RefUrl": "/notes/427847 "}, {"RefNumber": "430515", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Item category redetermination not working", "RefUrl": "/notes/430515 "}, {"RefNumber": "367022", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Redistribution of a Order planned by Ext TPS", "RefUrl": "/notes/367022 "}, {"RefNumber": "376541", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Performance improvement Euro-conversion LIKP", "RefUrl": "/notes/376541 "}, {"RefNumber": "215608", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Error Handling in TAS Inbound Process", "RefUrl": "/notes/215608 "}, {"RefNumber": "365694", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Minus(-) Temperature is not processed.", "RefUrl": "/notes/365694 "}, {"RefNumber": "392082", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Wrong data format in forwarded Idoc OILLDD & OILLDC", "RefUrl": "/notes/392082 "}, {"RefNumber": "370169", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Missing order item in TPI outbound idoc", "RefUrl": "/notes/370169 "}, {"RefNumber": "374846", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error in changing ´plant´ of order", "RefUrl": "/notes/374846 "}, {"RefNumber": "378285", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Correction for short dump due to TABLE_INVALID_INDEX", "RefUrl": "/notes/378285 "}, {"RefNumber": "393394", "RefComponent": "IS-OIL-DS-SSR", "RefTitle": "OTWS error on SCP screen of Business Location", "RefUrl": "/notes/393394 "}, {"RefNumber": "388915", "RefComponent": "IS-OIL", "RefTitle": "ISO conversion leads to blank unit of measures", "RefUrl": "/notes/388915 "}, {"RefNumber": "392236", "RefComponent": "IS-OIL", "RefTitle": "ISO conversion leads to blank unit of measures", "RefUrl": "/notes/392236 "}, {"RefNumber": "417679", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Handling type disappears when PO changed", "RefUrl": "/notes/417679 "}, {"RefNumber": "414289", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "O5NX Batch input error due to LEAVE TO TRANSACTION statement", "RefUrl": "/notes/414289 "}, {"RefNumber": "414197", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "O5NG batch input error due to LEAVE TO TRANSACTION statement", "RefUrl": "/notes/414197 "}, {"RefNumber": "411413", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "VA01 errors when removing tank assignment", "RefUrl": "/notes/411413 "}, {"RefNumber": "381462", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "F4 help on shipment workbench shows all drivers", "RefUrl": "/notes/381462 "}, {"RefNumber": "415005", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "data element OIG_AEDTMF field labels are wrong", "RefUrl": "/notes/415005 "}, {"RefNumber": "399019", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Travel time fields not filled from shimpent", "RefUrl": "/notes/399019 "}, {"RefNumber": "398955", "RefComponent": "IS-OIL", "RefTitle": "ISO conversion leads to blank unit of measures", "RefUrl": "/notes/398955 "}, {"RefNumber": "414989", "RefComponent": "IS-OIL", "RefTitle": "Enable internal flag in ticket screen", "RefUrl": "/notes/414989 "}, {"RefNumber": "423668", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "STSI Load Confirmation posts additional qtys for TDP matl", "RefUrl": "/notes/423668 "}, {"RefNumber": "403030", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "After implementation of 392231 inbound process short dumps", "RefUrl": "/notes/403030 "}, {"RefNumber": "417113", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "O4PO: new created delivery w/o tank id entries in oik37", "RefUrl": "/notes/417113 "}, {"RefNumber": "402561", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Error in sales order for more then one item in TAS", "RefUrl": "/notes/402561 "}, {"RefNumber": "420095", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL TDP GI for delivery note missing OIVBELN + OIPOSNR", "RefUrl": "/notes/420095 "}, {"RefNumber": "426588", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Shortdump in a sales order with no line items.", "RefUrl": "/notes/426588 "}, {"RefNumber": "425216", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Batch missing; Vehicle balance; BW /0; Abend mvmt o b; OIGSH", "RefUrl": "/notes/425216 "}, {"RefNumber": "423369", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Shortdump while changing sales order with no line items.", "RefUrl": "/notes/423369 "}, {"RefNumber": "392985", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR21: Double values passed to CO, accounting items missing", "RefUrl": "/notes/392985 "}, {"RefNumber": "422007", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL TDP BILLING MISSING CO LINES", "RefUrl": "/notes/422007 "}, {"RefNumber": "406580", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Error when changing storage location in delivery", "RefUrl": "/notes/406580 "}, {"RefNumber": "419887", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Impossible to change exchange starting date", "RefUrl": "/notes/419887 "}, {"RefNumber": "417277", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Short dump & BDC error with netting document & BTCI", "RefUrl": "/notes/417277 "}, {"RefNumber": "416930", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Cancelation of note 391987 (no agreed redesign of feerepric)", "RefUrl": "/notes/416930 "}, {"RefNumber": "354546", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MAP: Price not found, if non-posted day = 'X'", "RefUrl": "/notes/354546 "}, {"RefNumber": "402296", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Error occurs in LIA account determination", "RefUrl": "/notes/402296 "}, {"RefNumber": "413748", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Redetermine Sold-to party when creating new SD contract", "RefUrl": "/notes/413748 "}, {"RefNumber": "368957", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "EKBZ update excise duty addback", "RefUrl": "/notes/368957 "}, {"RefNumber": "416919", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Short dump while calling BAPI_SALESORDER_CREATEFROMDAT2", "RefUrl": "/notes/416919 "}, {"RefNumber": "412543", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Oil vers. of note 410014: No check of pricing ref. material", "RefUrl": "/notes/412543 "}, {"RefNumber": "415989", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 61-62 incl. 4.0B", "RefUrl": "/notes/415989 "}, {"RefNumber": "414893", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Field Control for transport zone & Mode of Transport", "RefUrl": "/notes/414893 "}, {"RefNumber": "308790", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD - Document flow;Branch to mat.doc,Incorrect GJAHR", "RefUrl": "/notes/308790 "}, {"RefNumber": "393987", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "IS-OIL: Clear sales office and group in SO entry", "RefUrl": "/notes/393987 "}, {"RefNumber": "401810", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Error V0010 occur after repeat the allocation step in O3A2", "RefUrl": "/notes/401810 "}, {"RefNumber": "401341", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Zero quantity line is not calculated during delivery", "RefUrl": "/notes/401341 "}, {"RefNumber": "411207", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Shipment Planning corrections", "RefUrl": "/notes/411207 "}, {"RefNumber": "398954", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Shipment Planning corrections", "RefUrl": "/notes/398954 "}, {"RefNumber": "211421", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Delivery Note and Bill of Lading not Appearing in MB02", "RefUrl": "/notes/211421 "}, {"RefNumber": "350212", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Update termination in shipment delete due to credit check", "RefUrl": "/notes/350212 "}, {"RefNumber": "408614", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "MCOE: No Validation for ship. point and route on order entry", "RefUrl": "/notes/408614 "}, {"RefNumber": "339524", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Enable stock transfer when Han.Type&VT have diff. ED Status", "RefUrl": "/notes/339524 "}, {"RefNumber": "207657", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Back button crea. entr.in chg Output Det.", "RefUrl": "/notes/207657 "}, {"RefNumber": "300744", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Material freight classes/freight codes in shpt costing", "RefUrl": "/notes/300744 "}, {"RefNumber": "379423", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Doc. item qty assign. in case of weight-volume-deviations", "RefUrl": "/notes/379423 "}, {"RefNumber": "189505", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Handl.type not def. on change of plant or mat.at material", "RefUrl": "/notes/189505 "}, {"RefNumber": "317717", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "GR Reversal / Excise Duty / wrong Doc. Currency amounts", "RefUrl": "/notes/317717 "}, {"RefNumber": "300150", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Exd license not valid (problem with the valid from/to date)", "RefUrl": "/notes/300150 "}, {"RefNumber": "197905", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Wrong exd posting when currency is set to 1 decimal place", "RefUrl": "/notes/197905 "}, {"RefNumber": "321135", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Transp. functionality f. Operational Time Window Sets (OTWS)", "RefUrl": "/notes/321135 "}, {"RefNumber": "206287", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "ROIIP-RCBLK and ROIIP-DOBLK missing in Loc. data-> TPS ctrl", "RefUrl": "/notes/206287 "}, {"RefNumber": "202267", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Pur. Assignm.Scr:F4 on the PO doc.does not copy the Item No.", "RefUrl": "/notes/202267 "}, {"RefNumber": "194512", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Load balancing:qty difference in GR & goods issue documents", "RefUrl": "/notes/194512 "}, {"RefNumber": "376579", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MAP:Conditions with qty scale basis become inactive in docs", "RefUrl": "/notes/376579 "}, {"RefNumber": "193229", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Use cust.exit on KNA1 change pointer for updates to SCP OTWS", "RefUrl": "/notes/193229 "}, {"RefNumber": "179557", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Pricing date> sy-datum, quotation error routine not trigger.", "RefUrl": "/notes/179557 "}, {"RefNumber": "177866", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Index(VKDFS) is upd. incorr. when cancel diff. invoice", "RefUrl": "/notes/177866 "}, {"RefNumber": "309707", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "No account posting in sub-contract PO with Oil material", "RefUrl": "/notes/309707 "}, {"RefNumber": "363310", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Dump at the creation of an iv. list (OIC_DITAB is not def.)", "RefUrl": "/notes/363310 "}, {"RefNumber": "378406", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "No gain/loss post. at GR/GI with diff. sub/baseproduct", "RefUrl": "/notes/378406 "}, {"RefNumber": "364026", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Problems at the datacopy from MM contract to po- TDP related", "RefUrl": "/notes/364026 "}, {"RefNumber": "207504", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR01: Del. costs by Vendor can be invoiced multiple times", "RefUrl": "/notes/207504 "}, {"RefNumber": "383918", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "BSW factor is not appearing in oil calc. at load confirm.", "RefUrl": "/notes/383918 "}, {"RefNumber": "363131", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Error proc.while assign. v.mtr with del.flag to Transp.Unit", "RefUrl": "/notes/363131 "}, {"RefNumber": "334462", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Sort order doc.flow; Rounding load interface; reserv. batch", "RefUrl": "/notes/334462 "}, {"RefNumber": "213757", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Err. PTL, Rebr. to non-batch, Avail.with warning, Veh.Recon", "RefUrl": "/notes/213757 "}, {"RefNumber": "176647", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Dens.of chem.prod.;Round.HPM;Veh. recon.;O4G1-Err Msg:V0104", "RefUrl": "/notes/176647 "}, {"RefNumber": "154841", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD temperature UoM in popup/multiple MM docs per transaction", "RefUrl": "/notes/154841 "}, {"RefNumber": "309717", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "ED qty in special ledger / ED inventory during transfer", "RefUrl": "/notes/309717 "}, {"RefNumber": "322262", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Enable scroll bars/min. resolution in OIL entry order screen", "RefUrl": "/notes/322262 "}, {"RefNumber": "310095", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Wrong positioning in shpt scheduling - doc./compart. details", "RefUrl": "/notes/310095 "}, {"RefNumber": "352264", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Populate LIPS-LGMNG with qty in base UoM for GI reversal", "RefUrl": "/notes/352264 "}, {"RefNumber": "351945", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Calc. sales tax acc. to new price cond. when price control 5", "RefUrl": "/notes/351945 "}, {"RefNumber": "172212", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD compartment user screens: Selected compartments lost", "RefUrl": "/notes/172212 "}, {"RefNumber": "407197", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Billed order appears again in VF04", "RefUrl": "/notes/407197 "}, {"RefNumber": "366778", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Inconsistency in prof.segm.number for intercompany billing", "RefUrl": "/notes/366778 "}, {"RefNumber": "300784", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Table \"S???\" is not listed in the ABAP/4 Dictionary", "RefUrl": "/notes/300784 "}, {"RefNumber": "189339", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "ORA 1562 on OIFPBL in program RVV05IVB", "RefUrl": "/notes/189339 "}, {"RefNumber": "176909", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "OIPBL missing during sales order call-off", "RefUrl": "/notes/176909 "}, {"RefNumber": "406284", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "MR01 in foreign currency ends in message M8 *********** 085", "RefUrl": "/notes/406284 "}, {"RefNumber": "400419", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Formula Maintenance ends in duplicate record", "RefUrl": "/notes/400419 "}, {"RefNumber": "361254", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Partner det. in call-off from ship-to with SAP/TAS interfa", "RefUrl": "/notes/361254 "}, {"RefNumber": "399436", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL TDP Sales excise duty revenue adjustment", "RefUrl": "/notes/399436 "}, {"RefNumber": "399825", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exclude items from ERS is not correct", "RefUrl": "/notes/399825 "}, {"RefNumber": "392391", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Missing gain/loss posting at goods receipt/issue", "RefUrl": "/notes/392391 "}, {"RefNumber": "364780", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Inbound interface abends on deleted order", "RefUrl": "/notes/364780 "}, {"RefNumber": "392231", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No error at O4PO if delivery is assigned to shipment", "RefUrl": "/notes/392231 "}, {"RefNumber": "398747", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No error at O4PO if delivery is assigned to shipment", "RefUrl": "/notes/398747 "}, {"RefNumber": "397101", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Error in rebate agreement with zero document", "RefUrl": "/notes/397101 "}, {"RefNumber": "381516", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Short dump in VF04 on Nota Fiscal Complementar", "RefUrl": "/notes/381516 "}, {"RefNumber": "398658", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Append OILTCURR from table TCURR deleted with 4.6C", "RefUrl": "/notes/398658 "}, {"RefNumber": "317518", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 39-44 incl. 4.0B", "RefUrl": "/notes/317518 "}, {"RefNumber": "397767", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MRRL Duplicate error messages in the ERS run output", "RefUrl": "/notes/397767 "}, {"RefNumber": "361335", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Error O1 573 during purch. ass. with group cond.", "RefUrl": "/notes/361335 "}, {"RefNumber": "395885", "RefComponent": "IS-OIL-DS-SSR", "RefTitle": "OIL: SSR Field conversions retrieves wrong value", "RefUrl": "/notes/395885 "}, {"RefNumber": "199227", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Load ID customer screen handling", "RefUrl": "/notes/199227 "}, {"RefNumber": "174656", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "IS-OIL: Zero qty in PO history of EXG assigned PO", "RefUrl": "/notes/174656 "}, {"RefNumber": "323387", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Characteristics for postings to CO-PA from shpmnt", "RefUrl": "/notes/323387 "}, {"RefNumber": "391448", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Create acc. doc. w/ BTCI: no exchange no. field", "RefUrl": "/notes/391448 "}, {"RefNumber": "312246", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Parallel OILLDD processing - advanced development", "RefUrl": "/notes/312246 "}, {"RefNumber": "392074", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "FDI indicator invisible at delivery note return order", "RefUrl": "/notes/392074 "}, {"RefNumber": "391987", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "GR Repricing does not reprice exchange fees", "RefUrl": "/notes/391987 "}, {"RefNumber": "331952", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Rundown engine calculations for transport system.", "RefUrl": "/notes/331952 "}, {"RefNumber": "334795", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Error in conversion factor in rundown report", "RefUrl": "/notes/334795 "}, {"RefNumber": "355654", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Base Location lookup  for customer exit not working", "RefUrl": "/notes/355654 "}, {"RefNumber": "370775", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "CONVERSION FACTOR ERROR", "RefUrl": "/notes/370775 "}, {"RefNumber": "174437", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "TDP VALUE CORRECTION WHEN CANCEL AN PO INVOICE", "RefUrl": "/notes/174437 "}, {"RefNumber": "174746", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Qty conversion error when using product proposal", "RefUrl": "/notes/174746 "}, {"RefNumber": "320496", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Period close using up all database locks", "RefUrl": "/notes/320496 "}, {"RefNumber": "364092", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "TSW Ticketing: Load Confirmation", "RefUrl": "/notes/364092 "}, {"RefNumber": "391477", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Invoice split dur.int-comp.billng:location assgnmt", "RefUrl": "/notes/391477 "}, {"RefNumber": "390711", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Cancelling invoice passes wrong quantity to LIS", "RefUrl": "/notes/390711 "}, {"RefNumber": "380451", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong/Repetitive warning message O1 811 in MR01 simulation", "RefUrl": "/notes/380451 "}, {"RefNumber": "381616", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Goods receipt: Wrong internal material payables", "RefUrl": "/notes/381616 "}, {"RefNumber": "384877", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Bad performance at VF04/VF06 because of too much enqueues", "RefUrl": "/notes/384877 "}, {"RefNumber": "386718", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "QS message O1 012 customizable by table T160M", "RefUrl": "/notes/386718 "}, {"RefNumber": "377118", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Quantity missing in logical inventory", "RefUrl": "/notes/377118 "}, {"RefNumber": "380744", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Cancelling invoice with fees: no accounting document", "RefUrl": "/notes/380744 "}, {"RefNumber": "385889", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees or taxes missing on netting statement", "RefUrl": "/notes/385889 "}, {"RefNumber": "388748", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees in invoice verification should not be parked", "RefUrl": "/notes/388748 "}, {"RefNumber": "336373", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-Oil: TDP Handling Type not copied at PO creation", "RefUrl": "/notes/336373 "}, {"RefNumber": "385417", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Error when changing stor.loc. in delivery", "RefUrl": "/notes/385417 "}, {"RefNumber": "335256", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees in invoice verification should not be parked", "RefUrl": "/notes/335256 "}, {"RefNumber": "316891", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Flexible Locking of Driver Vehicle Assignment", "RefUrl": "/notes/316891 "}, {"RefNumber": "351652", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Update termination during goods issue", "RefUrl": "/notes/351652 "}, {"RefNumber": "177563", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Save tank for delivery on shipment inbound", "RefUrl": "/notes/177563 "}, {"RefNumber": "177556", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "change communication structure for delete IDoc processing", "RefUrl": "/notes/177556 "}, {"RefNumber": "200706", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "GUI status OIIPBLCT missing", "RefUrl": "/notes/200706 "}, {"RefNumber": "192014", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Error OC709 occurs on TPI OTWS entry", "RefUrl": "/notes/192014 "}, {"RefNumber": "191432", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Tank ID assignment at order change incorrect", "RefUrl": "/notes/191432 "}, {"RefNumber": "177825", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Order distribution action code", "RefUrl": "/notes/177825 "}, {"RefNumber": "191401", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Action code in OILTPI50 populated incorrectly", "RefUrl": "/notes/191401 "}, {"RefNumber": "373605", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Error in dynamic UoM enhancement (F&A fees)", "RefUrl": "/notes/373605 "}, {"RefNumber": "363888", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Differential invoice and taxes", "RefUrl": "/notes/363888 "}, {"RefNumber": "351836", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: No repricing for oil BOM header", "RefUrl": "/notes/351836 "}, {"RefNumber": "379751", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Excise Duty Screen appears empty in the Incompl.Log", "RefUrl": "/notes/379751 "}, {"RefNumber": "382846", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees missing on invoice print", "RefUrl": "/notes/382846 "}, {"RefNumber": "380993", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Wrong sorting in formula term item tab XOICQ8 and XOICQ9", "RefUrl": "/notes/380993 "}, {"RefNumber": "382255", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD: Display purchase requisition from documentflow", "RefUrl": "/notes/382255 "}, {"RefNumber": "379875", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong GI price", "RefUrl": "/notes/379875 "}, {"RefNumber": "381772", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Incorrect screen to display Consignment Pricing", "RefUrl": "/notes/381772 "}, {"RefNumber": "381907", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD-F: Conversion of UoM for shipment costing", "RefUrl": "/notes/381907 "}, {"RefNumber": "372080", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Error in dynamic UoM enhancement", "RefUrl": "/notes/372080 "}, {"RefNumber": "370570", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Incorrect valuation of vendor consignment stock", "RefUrl": "/notes/370570 "}, {"RefNumber": "371447", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Consideration of gain/losses at the LIA posting", "RefUrl": "/notes/371447 "}, {"RefNumber": "188842", "RefComponent": "MM-IM-GF", "RefTitle": "MB03: display external details fails (WA OIDE A)", "RefUrl": "/notes/188842 "}, {"RefNumber": "374985", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR01 delivery cost sequence as per GR sequence", "RefUrl": "/notes/374985 "}, {"RefNumber": "369927", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Quant. conv. factors from info record only if they exist", "RefUrl": "/notes/369927 "}, {"RefNumber": "315091", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Advanced functionality for Shipment Planning", "RefUrl": "/notes/315091 "}, {"RefNumber": "372540", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Inconsistency in additional quantities for delivery", "RefUrl": "/notes/372540 "}, {"RefNumber": "336103", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "GR against purchase order fails with msg M7301", "RefUrl": "/notes/336103 "}, {"RefNumber": "170620", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: EXG fee UoM missing in HPM quantity conversion", "RefUrl": "/notes/170620 "}, {"RefNumber": "335717", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange abstract reprices sales fees incorrectly", "RefUrl": "/notes/335717 "}, {"RefNumber": "150132", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: credit management: value update problem", "RefUrl": "/notes/150132 "}, {"RefNumber": "370710", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: RGJNOUXD incorrect transaction currency", "RefUrl": "/notes/370710 "}, {"RefNumber": "350486", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "IS-OIL : TSW : Nominations not printing", "RefUrl": "/notes/350486 "}, {"RefNumber": "370160", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Performance problem on reads to DD07L slow down 04G1.", "RefUrl": "/notes/370160 "}, {"RefNumber": "366622", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Posting of exchange reversal fees to SL not possible", "RefUrl": "/notes/366622 "}, {"RefNumber": "154005", "RefComponent": "CA-JVA", "RefTitle": "More than one downpayment clearing", "RefUrl": "/notes/154005 "}, {"RefNumber": "367704", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Goods issue: Double S036 update", "RefUrl": "/notes/367704 "}, {"RefNumber": "362077", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Financial documents not found in netting", "RefUrl": "/notes/362077 "}, {"RefNumber": "326970", "RefComponent": "IS-OIL", "RefTitle": "Missing setting for change pointers", "RefUrl": "/notes/326970 "}, {"RefNumber": "364900", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "TankID is lost, if items are deleted+re-created in s/o", "RefUrl": "/notes/364900 "}, {"RefNumber": "365908", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS OIL: PI with freeze book indicator", "RefUrl": "/notes/365908 "}, {"RefNumber": "363115", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Error M7 234 for movement involving vendor consignment", "RefUrl": "/notes/363115 "}, {"RefNumber": "356722", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "IS-OIL: Adaption of preprocessing Program EWUMMPOA", "RefUrl": "/notes/356722 "}, {"RefNumber": "362910", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Proposal of valuation type in batch field", "RefUrl": "/notes/362910 "}, {"RefNumber": "365843", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "No record OIGSVMQO1;GI of STO for new valtyp; 195869", "RefUrl": "/notes/365843 "}, {"RefNumber": "364777", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Negative quantities in quantity schedule", "RefUrl": "/notes/364777 "}, {"RefNumber": "362410", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "T063 entries missing with function code OIDE", "RefUrl": "/notes/362410 "}, {"RefNumber": "352656", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Delivery w/ purchase assignment: update termination", "RefUrl": "/notes/352656 "}, {"RefNumber": "364320", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Delivery update termin./ excise duty cond. missing", "RefUrl": "/notes/364320 "}, {"RefNumber": "361878", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Event doc type not found (msg E220)", "RefUrl": "/notes/361878 "}, {"RefNumber": "350797", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "G_OIKIMPORT structure not populated in SD_ITEM_MAINTAIN", "RefUrl": "/notes/350797 "}, {"RefNumber": "361284", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR01/MRHG: Delivery Cost Credit Memo error between years", "RefUrl": "/notes/361284 "}, {"RefNumber": "356471", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "CASE II ROIGMS00: Inconsistencies between SAP and terminal", "RefUrl": "/notes/356471 "}, {"RefNumber": "360233", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Open hours check fails for Time Frames", "RefUrl": "/notes/360233 "}, {"RefNumber": "351626", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Missing IS-Oil fields in CO-PA KENC table", "RefUrl": "/notes/351626 "}, {"RefNumber": "362516", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "VL02: update error for purchase assignment", "RefUrl": "/notes/362516 "}, {"RefNumber": "363140", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "IS-Oil MRN field OIFWE not initialized properly", "RefUrl": "/notes/363140 "}, {"RefNumber": "146147", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MAP-Customer price list does not work for item lev.", "RefUrl": "/notes/146147 "}, {"RefNumber": "310671", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "External ref number not preserved in transaction O4PP", "RefUrl": "/notes/310671 "}, {"RefNumber": "319760", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "E1OILSC-MAX_VOL incorrect in OILSHL shipment d/load IDOC", "RefUrl": "/notes/319760 "}, {"RefNumber": "316453", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect exchange statement print out when no activities", "RefUrl": "/notes/316453 "}, {"RefNumber": "315973", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "S036 rebuild picks up some transactions twice", "RefUrl": "/notes/315973 "}, {"RefNumber": "315730", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect freight posting for exchange related GR", "RefUrl": "/notes/315730 "}, {"RefNumber": "300650", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect market quote found after pricing date change in SO", "RefUrl": "/notes/300650 "}, {"RefNumber": "358259", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "HT default from Process order during GI", "RefUrl": "/notes/358259 "}, {"RefNumber": "312121", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MM_MATBEL: bad performance and incorrect check for netting", "RefUrl": "/notes/312121 "}, {"RefNumber": "350742", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Tank-ID screen in TD-Deliver Confirmation comes blank", "RefUrl": "/notes/350742 "}, {"RefNumber": "357274", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect tax code in acc.doc. for diff.invoice", "RefUrl": "/notes/357274 "}, {"RefNumber": "356007", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Set loading date/time in freight assignment (O4L1)", "RefUrl": "/notes/356007 "}, {"RefNumber": "356080", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External Details missing when plant entered manually", "RefUrl": "/notes/356080 "}, {"RefNumber": "351666", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Input of init.value not allowed for field V_T685A-OIREPORT", "RefUrl": "/notes/351666 "}, {"RefNumber": "355142", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "O4F2: Update terminated on vehicle change in bulk shipment", "RefUrl": "/notes/355142 "}, {"RefNumber": "336962", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: MR21/MR22 allow postings at negativ stock level", "RefUrl": "/notes/336962 "}, {"RefNumber": "351551", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Received error message when add a new output item", "RefUrl": "/notes/351551 "}, {"RefNumber": "355564", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: ED Revaluation if base UoM not equal ED UoM", "RefUrl": "/notes/355564 "}, {"RefNumber": "350766", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Search help VMVL ID T causes problems", "RefUrl": "/notes/350766 "}, {"RefNumber": "351807", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "LIPS upd missing; Delete NAST", "RefUrl": "/notes/351807 "}, {"RefNumber": "336131", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External details WAOIDEVB entry missing in T063", "RefUrl": "/notes/336131 "}, {"RefNumber": "353978", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Ship-to party incorrectly displayed", "RefUrl": "/notes/353978 "}, {"RefNumber": "353230", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Authority check fails for quantity schedule", "RefUrl": "/notes/353230 "}, {"RefNumber": "338865", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Split invoicing not working for accruals", "RefUrl": "/notes/338865 "}, {"RefNumber": "310523", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Shipment Load ID re-determination", "RefUrl": "/notes/310523 "}, {"RefNumber": "325399", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Field KNVV-OIINEX . is not an input field", "RefUrl": "/notes/325399 "}, {"RefNumber": "326895", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Open hours check fails for Time Frames", "RefUrl": "/notes/326895 "}, {"RefNumber": "326861", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Update termination in SAPLOIIU", "RefUrl": "/notes/326861 "}, {"RefNumber": "318688", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Errors in Tank ID handing in VA01/VA02", "RefUrl": "/notes/318688 "}, {"RefNumber": "319796", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Shipment Load ID valid-to < valid from date", "RefUrl": "/notes/319796 "}, {"RefNumber": "316516", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "VL01/VL02/ME32/ME33 errors because OIA05/OIA06 unsorted", "RefUrl": "/notes/316516 "}, {"RefNumber": "311456", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Short dump creating contract with quantity schedule", "RefUrl": "/notes/311456 "}, {"RefNumber": "302311", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Unable to Use Valuation Class in LIA Acct. Determination", "RefUrl": "/notes/302311 "}, {"RefNumber": "318966", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Function OII_GET_SOLD_TO_FOR_SHIP_TO short dumps", "RefUrl": "/notes/318966 "}, {"RefNumber": "316902", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Close TD BOM component according to main item", "RefUrl": "/notes/316902 "}, {"RefNumber": "324180", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Abend in VA02 after notes 316902 and 322765", "RefUrl": "/notes/324180 "}, {"RefNumber": "318607", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Wrong excise duty posting to accounting", "RefUrl": "/notes/318607 "}, {"RefNumber": "310158", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD-Master User Screen Settings not customizable", "RefUrl": "/notes/310158 "}, {"RefNumber": "316386", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "CALL_FUNCTION_CONFLICT_LENG", "RefUrl": "/notes/316386 "}, {"RefNumber": "338202", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Characteristic derivation PSPID -> OIFPBL does not work", "RefUrl": "/notes/338202 "}, {"RefNumber": "336109", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Short dump due to formula & average fee condition type", "RefUrl": "/notes/336109 "}, {"RefNumber": "325472", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect rebate scale basis after running SDBONTO2", "RefUrl": "/notes/325472 "}, {"RefNumber": "336950", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: wrong repricing in TD after note", "RefUrl": "/notes/336950 "}, {"RefNumber": "336664", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Quantity conversion error in rebate agreement", "RefUrl": "/notes/336664 "}, {"RefNumber": "318560", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Error during automatic order creation against GR", "RefUrl": "/notes/318560 "}, {"RefNumber": "336440", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Corrections Loc.Brazil 09/2000", "RefUrl": "/notes/336440 "}, {"RefNumber": "336030", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Short dump VF01 - TABLE_INVALID_INDEX", "RefUrl": "/notes/336030 "}, {"RefNumber": "334920", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Problem in Archiving of Customer without BDRP data", "RefUrl": "/notes/334920 "}, {"RefNumber": "317519", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - LCP 39-44 incl. 4.0B", "RefUrl": "/notes/317519 "}, {"RefNumber": "329203", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Material Master / ALE / oil specific data", "RefUrl": "/notes/329203 "}, {"RefNumber": "325052", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-Oil: Sales - Automatic update of Excise Duty values", "RefUrl": "/notes/325052 "}, {"RefNumber": "300611", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Minor corrections hydrocarbon inventory management", "RefUrl": "/notes/300611 "}, {"RefNumber": "333978", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "duplicates in netting document list for exchange", "RefUrl": "/notes/333978 "}, {"RefNumber": "157975", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Not possible to maintain posting keys NTB,NTK...", "RefUrl": "/notes/157975 "}, {"RefNumber": "318839", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Handling Type in Sales Order Display Screen 8025", "RefUrl": "/notes/318839 "}, {"RefNumber": "333273", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "TSW Modifications to fix Batch/Valuations", "RefUrl": "/notes/333273 "}, {"RefNumber": "332993", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Formula Price calculation in relation to quantity", "RefUrl": "/notes/332993 "}, {"RefNumber": "328086", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Short dump due to negative net price in contract display", "RefUrl": "/notes/328086 "}, {"RefNumber": "332989", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Update of nomination failed", "RefUrl": "/notes/332989 "}, {"RefNumber": "316998", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "ROIIP-RCBLK,DOBLK not update db & SCP blank screen", "RefUrl": "/notes/316998 "}, {"RefNumber": "332092", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "LIA does not transfer Trading partner to FI", "RefUrl": "/notes/332092 "}, {"RefNumber": "98534", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Abend O1 544 in TD Delivery Confirmation", "RefUrl": "/notes/98534 "}, {"RefNumber": "331304", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Error displaying Ext. Dtls. in Goods Issue Docs.", "RefUrl": "/notes/331304 "}, {"RefNumber": "332287", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "S3VBRKWR: SQL error 4031 when accessing table OICQ7", "RefUrl": "/notes/332287 "}, {"RefNumber": "201437", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Avoid program dump when exceptions are raised", "RefUrl": "/notes/201437 "}, {"RefNumber": "328204", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "GR reversals missing in movements-based netting", "RefUrl": "/notes/328204 "}, {"RefNumber": "330214", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Opening balance on exchange statement missing", "RefUrl": "/notes/330214 "}, {"RefNumber": "330194", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange statement stops printing", "RefUrl": "/notes/330194 "}, {"RefNumber": "329466", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fee details can not be processed at goods receipt", "RefUrl": "/notes/329466 "}, {"RefNumber": "329200", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Data Archiving Problem : OII_METER_CUSTOMER_INDEX_GET", "RefUrl": "/notes/329200 "}, {"RefNumber": "328266", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ASTM tolerance checks ignored during STSI IDOC processing", "RefUrl": "/notes/328266 "}, {"RefNumber": "156599", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Document item quantity assign. missing O9540", "RefUrl": "/notes/156599 "}, {"RefNumber": "107344", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD partner-specific output determination", "RefUrl": "/notes/107344 "}, {"RefNumber": "328151", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Netting abends saying accounting doc not found", "RefUrl": "/notes/328151 "}, {"RefNumber": "327863", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "table parameters missing for call RV_DELIVERY_ADD etc.", "RefUrl": "/notes/327863 "}, {"RefNumber": "324334", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "F4 Help for projects within business location", "RefUrl": "/notes/324334 "}, {"RefNumber": "309533", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Performance problems in differential invoice", "RefUrl": "/notes/309533 "}, {"RefNumber": "200957", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error in handling of EXIT_SAPLOIKS_001", "RefUrl": "/notes/200957 "}, {"RefNumber": "300298", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Multiple Change pointers generated for OILLID", "RefUrl": "/notes/300298 "}, {"RefNumber": "302327", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "IS OIL OUTPUT DETERMINATION", "RefUrl": "/notes/302327 "}, {"RefNumber": "327242", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Data Archiving Problem", "RefUrl": "/notes/327242 "}, {"RefNumber": "193407", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "OII_ARCHIVE_CHECK_CUST_SUBOBJS not found release 31H", "RefUrl": "/notes/193407 "}, {"RefNumber": "321691", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect amount in Inv.verif. with GR cancellations", "RefUrl": "/notes/321691 "}, {"RefNumber": "171871", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "IS-Oil BDRP Sales Hours not read", "RefUrl": "/notes/171871 "}, {"RefNumber": "323181", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Update termination in shipment creation", "RefUrl": "/notes/323181 "}, {"RefNumber": "325179", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Short dump in display sales order : VA03", "RefUrl": "/notes/325179 "}, {"RefNumber": "324518", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Renaming customer exit function to oil namespace", "RefUrl": "/notes/324518 "}, {"RefNumber": "322646", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Ticket delivery date < Ordr delivery date", "RefUrl": "/notes/322646 "}, {"RefNumber": "325412", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL:Material movement with n lines, account mod", "RefUrl": "/notes/325412 "}, {"RefNumber": "324964", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Classification for business location", "RefUrl": "/notes/324964 "}, {"RefNumber": "324806", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Reversal Documents reported in Vertex with the Wrong Sign", "RefUrl": "/notes/324806 "}, {"RefNumber": "321681", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "XXX", "RefUrl": "/notes/321681 "}, {"RefNumber": "322720", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Impossible to display quantity schedule in contract", "RefUrl": "/notes/322720 "}, {"RefNumber": "321161", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange statement: MM reversal quantity missing", "RefUrl": "/notes/321161 "}, {"RefNumber": "316979", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Divide by zero error during invoice verification", "RefUrl": "/notes/316979 "}, {"RefNumber": "318349", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: ED Revaluation / Post Difference not possible", "RefUrl": "/notes/318349 "}, {"RefNumber": "321836", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Tracking of tax exemption licenses", "RefUrl": "/notes/321836 "}, {"RefNumber": "314379", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Shortdump in S3VBRKWR and increase performance", "RefUrl": "/notes/314379 "}, {"RefNumber": "321127", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Condition base value not moved in routine 409", "RefUrl": "/notes/321127 "}, {"RefNumber": "316218", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect account postings in GR with multiples POs", "RefUrl": "/notes/316218 "}, {"RefNumber": "318202", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "IV: doubled items and fixed amount conditions", "RefUrl": "/notes/318202 "}, {"RefNumber": "320810", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Incorrect weight/volume in batch split main item", "RefUrl": "/notes/320810 "}, {"RefNumber": "320098", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "LIA movement in wrong exchange statement section", "RefUrl": "/notes/320098 "}, {"RefNumber": "314967", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Saving of return order terminates", "RefUrl": "/notes/314967 "}, {"RefNumber": "320192", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: VL02, two HPM screens appears during batch input", "RefUrl": "/notes/320192 "}, {"RefNumber": "203077", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "GUI Status OIIPBLCT missing", "RefUrl": "/notes/203077 "}, {"RefNumber": "317951", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Error message when creating Intercompany invoice", "RefUrl": "/notes/317951 "}, {"RefNumber": "180596", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Sales tax incorrect on differential invoice creation", "RefUrl": "/notes/180596 "}, {"RefNumber": "319203", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "To avoid ASTM pop-up during batch input.", "RefUrl": "/notes/319203 "}, {"RefNumber": "318652", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "IV ignores GR cancelation for items with split conditions", "RefUrl": "/notes/318652 "}, {"RefNumber": "214996", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "VA03 - Handling Type field Invisible in Screen 8025", "RefUrl": "/notes/214996 "}, {"RefNumber": "318351", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Incorrect quantity in rebate agreement", "RefUrl": "/notes/318351 "}, {"RefNumber": "318436", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Short dump in invoice verification", "RefUrl": "/notes/318436 "}, {"RefNumber": "318434", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "QS Messages customizable by table T160M", "RefUrl": "/notes/318434 "}, {"RefNumber": "315433", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Mov.based Netting errors for cancelled invoice ver.", "RefUrl": "/notes/315433 "}, {"RefNumber": "314232", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Update termination when saving delivery", "RefUrl": "/notes/314232 "}, {"RefNumber": "316708", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "IDOC Control record incorrect in IDOC_INPUT_OILDVA", "RefUrl": "/notes/316708 "}, {"RefNumber": "316839", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "RD report - handling of RDCONV = 0", "RefUrl": "/notes/316839 "}, {"RefNumber": "311935", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Goods receipt: calculation of base product price", "RefUrl": "/notes/311935 "}, {"RefNumber": "310851", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Incorrect Quantity for BOM Header in Load Bulk on Activation", "RefUrl": "/notes/310851 "}, {"RefNumber": "310631", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Error with Prior to Load ;PTL Not scheduled for load.", "RefUrl": "/notes/310631 "}, {"RefNumber": "213939", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR21 posts inconsistent FI-documents with no items", "RefUrl": "/notes/213939 "}, {"RefNumber": "149949", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - LCP 11, 4.0B", "RefUrl": "/notes/149949 "}, {"RefNumber": "143233", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD missing gain; add tracking; ASTM +/- qty; no OIGSM", "RefUrl": "/notes/143233 "}, {"RefNumber": "310788", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No provision to extend Idoc OILDVA01 Driver/Veh", "RefUrl": "/notes/310788 "}, {"RefNumber": "310427", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Brazil: Cancellation of documents from TD-transfers", "RefUrl": "/notes/310427 "}, {"RefNumber": "310287", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Wrong default for date/ time on the time frame screen", "RefUrl": "/notes/310287 "}, {"RefNumber": "307732", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Automatic document item quantity assignment", "RefUrl": "/notes/307732 "}, {"RefNumber": "306559", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Inconsistent date used in order item create", "RefUrl": "/notes/306559 "}, {"RefNumber": "309618", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "IS-OIL Output determination for Delivery Confirmation", "RefUrl": "/notes/309618 "}, {"RefNumber": "308026", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect Txn Curr  Amount Displayed on Netting Proposal", "RefUrl": "/notes/308026 "}, {"RefNumber": "199746", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Transaction termination VI200. Item status is missing", "RefUrl": "/notes/199746 "}, {"RefNumber": "306295", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Shipment number selection based on vehicle details", "RefUrl": "/notes/306295 "}, {"RefNumber": "305918", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Air buoyancy indicator handling via STSI Load Confirmation", "RefUrl": "/notes/305918 "}, {"RefNumber": "305704", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "OIK03 table entry not deleted after manual POST GI", "RefUrl": "/notes/305704 "}, {"RefNumber": "305891", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "VA01 allows order type which is not allowed for this SA", "RefUrl": "/notes/305891 "}, {"RefNumber": "203605", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Low performance when reading sales contracts", "RefUrl": "/notes/203605 "}, {"RefNumber": "304626", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Bad performance of ERS fee processing", "RefUrl": "/notes/304626 "}, {"RefNumber": "305064", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Variable PO doc. type in purch. assignment", "RefUrl": "/notes/305064 "}, {"RefNumber": "303797", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Euro conversion for fee rates in invoice verification", "RefUrl": "/notes/303797 "}, {"RefNumber": "303827", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Enable Group Conditions 4 and 5 in IS-OIL 4.0B", "RefUrl": "/notes/303827 "}, {"RefNumber": "303471", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "LID Flowlog report display incorrect", "RefUrl": "/notes/303471 "}, {"RefNumber": "301021", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Inconsistent date used in order item create", "RefUrl": "/notes/301021 "}, {"RefNumber": "301385", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Population of ship-to in TAS shipment IDoc", "RefUrl": "/notes/301385 "}, {"RefNumber": "303220", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Tank ID defaulting in orders incorrect on change", "RefUrl": "/notes/303220 "}, {"RefNumber": "157768", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect subtotal calculation type with F&A condn", "RefUrl": "/notes/157768 "}, {"RefNumber": "303089", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Update termination when saving dlv. with oil BoM", "RefUrl": "/notes/303089 "}, {"RefNumber": "303128", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: AUM posting with incorrect plant", "RefUrl": "/notes/303128 "}, {"RefNumber": "302746", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Error in Creation of Idoc OILORD", "RefUrl": "/notes/302746 "}, {"RefNumber": "301601", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Part.Conf.STO; Tolerance gain STO; Batch in interf.; uexit", "RefUrl": "/notes/301601 "}, {"RefNumber": "300647", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Mode of transport indicator not enabled for rail", "RefUrl": "/notes/300647 "}, {"RefNumber": "217379", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: incorrect quantity in GR for replenishment del.", "RefUrl": "/notes/217379 "}, {"RefNumber": "301014", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Incorrect TPP check in IDOC_OUTPUT_OILTPI50", "RefUrl": "/notes/301014 "}, {"RefNumber": "198627", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Incorrect shipping point in O4PO", "RefUrl": "/notes/198627 "}, {"RefNumber": "300869", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Split Indicator required in SD&MM Documents", "RefUrl": "/notes/300869 "}, {"RefNumber": "300151", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Hold data not enabled in IS-Oil overview screen", "RefUrl": "/notes/300151 "}, {"RefNumber": "215984", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Minor errors on Plant Site Control Parameters", "RefUrl": "/notes/215984 "}, {"RefNumber": "300849", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ABAP ROIKPGIS", "RefUrl": "/notes/300849 "}, {"RefNumber": "300374", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Actual goods issue date in TAS interface (pick-up)", "RefUrl": "/notes/300374 "}, {"RefNumber": "300406", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Batch split / ED license not copied to mat. doc.", "RefUrl": "/notes/300406 "}, {"RefNumber": "300199", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Tables OIKLIDR/OIK01 become inconsistent for shipments", "RefUrl": "/notes/300199 "}, {"RefNumber": "213813", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Create sales order with reference to invoice", "RefUrl": "/notes/213813 "}, {"RefNumber": "217126", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Parallel OILLDD inbound processing", "RefUrl": "/notes/217126 "}, {"RefNumber": "216067", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Additional customer exits", "RefUrl": "/notes/216067 "}, {"RefNumber": "214794", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "QS error O1017 not customizable by table T160M", "RefUrl": "/notes/214794 "}, {"RefNumber": "216249", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Status messages not returned to TPI", "RefUrl": "/notes/216249 "}, {"RefNumber": "215068", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Movement based netting corrections", "RefUrl": "/notes/215068 "}, {"RefNumber": "216894", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Object Key of change pointer (OILLID-OIGV) is wrong ?", "RefUrl": "/notes/216894 "}, {"RefNumber": "216522", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Screen sequence control entry:T185 SAPMV45B OIDX...", "RefUrl": "/notes/216522 "}, {"RefNumber": "211071", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Order time frame maintenenace not consistent", "RefUrl": "/notes/211071 "}, {"RefNumber": "216079", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Low performance when creat. deliveries for big contracts", "RefUrl": "/notes/216079 "}, {"RefNumber": "216413", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Incorrect quantity proposed in sales order", "RefUrl": "/notes/216413 "}, {"RefNumber": "216045", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Incorrect packing status", "RefUrl": "/notes/216045 "}, {"RefNumber": "201309", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Delivery cost credit memo error when crossing year", "RefUrl": "/notes/201309 "}, {"RefNumber": "197834", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Split excise duty fails for stock transfer order", "RefUrl": "/notes/197834 "}, {"RefNumber": "215522", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Tables for sec. level analysis were not sorted", "RefUrl": "/notes/215522 "}, {"RefNumber": "215460", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "TD Purchase Assignment Scr: F4 help on PO docs", "RefUrl": "/notes/215460 "}, {"RefNumber": "215636", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "New fee pricing date is not stored in the PO", "RefUrl": "/notes/215636 "}, {"RefNumber": "215633", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Checking base product for a line that has been deleted.", "RefUrl": "/notes/215633 "}, {"RefNumber": "215705", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Quotation Integrity Check - Date Range Requirement", "RefUrl": "/notes/215705 "}, {"RefNumber": "216007", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "NO CURRENCY/EX<PERSON>ANGE RATE FIELD ON DYNPRO SAPMV45A 4301", "RefUrl": "/notes/216007 "}, {"RefNumber": "210687", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "No VBTYP in TAS relevancy check for contract creation", "RefUrl": "/notes/210687 "}, {"RefNumber": "214650", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "OILORD02: Segment E1EDP01-ACTION not filled correctly", "RefUrl": "/notes/214650 "}, {"RefNumber": "214666", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR8M/MR08 M8607 - Error when reversing invoice", "RefUrl": "/notes/214666 "}, {"RefNumber": "215139", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Order time frame check vs OTWS incorrect", "RefUrl": "/notes/215139 "}, {"RefNumber": "214346", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Fields available for input in Display Sales Order", "RefUrl": "/notes/214346 "}, {"RefNumber": "213030", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "PO History leads to wrong IR Fee Document due to OIAFE", "RefUrl": "/notes/213030 "}, {"RefNumber": "212492", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Item proposal and item redetermination", "RefUrl": "/notes/212492 "}, {"RefNumber": "207609", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "VI11, VI12: Selection of TD shipments don't work properly", "RefUrl": "/notes/207609 "}, {"RefNumber": "213579", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Valuation type not displayed as label", "RefUrl": "/notes/213579 "}, {"RefNumber": "213049", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "TSW: Partner Role Detail Screen Tabcontrol size", "RefUrl": "/notes/213049 "}, {"RefNumber": "212670", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Error in goods issue for oil BoMs", "RefUrl": "/notes/212670 "}, {"RefNumber": "213093", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Manual entry of payment term in order header level", "RefUrl": "/notes/213093 "}, {"RefNumber": "210030", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR1M:Wrong Update of planned cost with diffrent Inv.Cycle", "RefUrl": "/notes/210030 "}, {"RefNumber": "209999", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "SMOD entries OIGMEN01 and OIGMEN02 not available", "RefUrl": "/notes/209999 "}, {"RefNumber": "205300", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees by outline agreement in MR01 does not work", "RefUrl": "/notes/205300 "}, {"RefNumber": "209689", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Check Load ID Determination", "RefUrl": "/notes/209689 "}, {"RefNumber": "209771", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Wrong invoice amount for account assigned PO items", "RefUrl": "/notes/209771 "}, {"RefNumber": "175037", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Lia account determination with valuation class", "RefUrl": "/notes/175037 "}, {"RefNumber": "205759", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Incorrect error message in goods issue log", "RefUrl": "/notes/205759 "}, {"RefNumber": "197769", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Statistics update for value items", "RefUrl": "/notes/197769 "}, {"RefNumber": "205561", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Refer mat.doc. for correction GR; No check val.typ", "RefUrl": "/notes/205561 "}, {"RefNumber": "155914", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Printing exchange statements using RSNAST00", "RefUrl": "/notes/155914 "}, {"RefNumber": "204741", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "shortdump when recal. subtotals for rebates", "RefUrl": "/notes/204741 "}, {"RefNumber": "196890", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Ship-to information not pulled into contracts", "RefUrl": "/notes/196890 "}, {"RefNumber": "198229", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Deleting partners from shipment", "RefUrl": "/notes/198229 "}, {"RefNumber": "204005", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Load ID customer screen handling", "RefUrl": "/notes/204005 "}, {"RefNumber": "203283", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Termination in TAS interface for pick-up's", "RefUrl": "/notes/203283 "}, {"RefNumber": "196306", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Error handling in TAS FM IDOC_INPUT_OILLDD", "RefUrl": "/notes/196306 "}, {"RefNumber": "202626", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Function Group OIFT missing", "RefUrl": "/notes/202626 "}, {"RefNumber": "193130", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "TPI: Trip planning", "RefUrl": "/notes/193130 "}, {"RefNumber": "187195", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Field overflow in LIA transaction", "RefUrl": "/notes/187195 "}, {"RefNumber": "201305", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Euro conversion for fee rates in goods receipt", "RefUrl": "/notes/201305 "}, {"RefNumber": "201913", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Function call conflict in OIK_TPI_ORDERS_CREATE", "RefUrl": "/notes/201913 "}, {"RefNumber": "201383", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "PO Creation - Field Status Problem", "RefUrl": "/notes/201383 "}, {"RefNumber": "201282", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Receive (new) batch at loading; ASTM error after mvmt o brd", "RefUrl": "/notes/201282 "}, {"RefNumber": "197738", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Brazil: Tax code customizing table changes", "RefUrl": "/notes/197738 "}, {"RefNumber": "201122", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Incorrect goods receipt qty.in 2step transfer", "RefUrl": "/notes/201122 "}, {"RefNumber": "195018", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Underdelivery tolerance 0% not working", "RefUrl": "/notes/195018 "}, {"RefNumber": "194386", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Archiving DBIF_RSQL_INVALID_CURSOR", "RefUrl": "/notes/194386 "}, {"RefNumber": "193559", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Rounding Errors in OIGI_CREATE_SHIPMENT_RFC fix", "RefUrl": "/notes/193559 "}, {"RefNumber": "192304", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Rounding Errors in OIGI_CREATE_SHIPMENT_RFC", "RefUrl": "/notes/192304 "}, {"RefNumber": "200816", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD allocation of res. to shipment => short dump", "RefUrl": "/notes/200816 "}, {"RefNumber": "199541", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Endless loop in IDOC_OUTPUT_OILTPI50", "RefUrl": "/notes/199541 "}, {"RefNumber": "200316", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error in incompletion for mandatory Tank ID", "RefUrl": "/notes/200316 "}, {"RefNumber": "198515", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Shortdump when assigning contract to item in sales order", "RefUrl": "/notes/198515 "}, {"RefNumber": "195009", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Goods receipt with fees into neg. stock errors", "RefUrl": "/notes/195009 "}, {"RefNumber": "199325", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Error message OE 018 during TAS pickup IDOC processing", "RefUrl": "/notes/199325 "}, {"RefNumber": "193381", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Netting does not generate a BTCI session", "RefUrl": "/notes/193381 "}, {"RefNumber": "196075", "RefComponent": "IS-OIL-DS", "RefTitle": "Oil-specific messages missing in class VF", "RefUrl": "/notes/196075 "}, {"RefNumber": "198085", "RefComponent": "IS-OIL-DS", "RefTitle": "HP upgrade merge error", "RefUrl": "/notes/198085 "}, {"RefNumber": "197182", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Quantity missing from OILSHI01 if CPLID not '1'", "RefUrl": "/notes/197182 "}, {"RefNumber": "197770", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Ship-to OIFWE in CO-PA not filled for billing trsf.", "RefUrl": "/notes/197770 "}, {"RefNumber": "197908", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Wrong date calculation in F&A Pricing after 1999.", "RefUrl": "/notes/197908 "}, {"RefNumber": "197470", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Consignment stock fields missing after 4.0B SP1", "RefUrl": "/notes/197470 "}, {"RefNumber": "154538", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Error in holiday determination in F&A pricing", "RefUrl": "/notes/154538 "}, {"RefNumber": "156403", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Bug in routine XKOMV_BEWERTEN", "RefUrl": "/notes/156403 "}, {"RefNumber": "157569", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Errors in repricing in invoice verification", "RefUrl": "/notes/157569 "}, {"RefNumber": "170287", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Rounding errors in invoice verification", "RefUrl": "/notes/170287 "}, {"RefNumber": "173205", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Selected BOL is not moved into screen field", "RefUrl": "/notes/173205 "}, {"RefNumber": "193023", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Handling of ETAs for planned shipments", "RefUrl": "/notes/193023 "}, {"RefNumber": "196052", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error OC605 occurs incorrectly", "RefUrl": "/notes/196052 "}, {"RefNumber": "194327", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Incorrect OIK37 updates via TPI", "RefUrl": "/notes/194327 "}, {"RefNumber": "189648", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Tank ID not available on Shipment Outbound IDocs", "RefUrl": "/notes/189648 "}, {"RefNumber": "164252", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Delete NAST entries for delete shipments", "RefUrl": "/notes/164252 "}, {"RefNumber": "172009", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Delete shipment + Nast entry", "RefUrl": "/notes/172009 "}, {"RefNumber": "195707", "RefComponent": "IS-OIL-DS", "RefTitle": "Slow response to delivery create", "RefUrl": "/notes/195707 "}, {"RefNumber": "194642", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Not possible to post LIA for 0 valued material", "RefUrl": "/notes/194642 "}, {"RefNumber": "193231", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "ROIKFCL1 functionality missing after Service Pack", "RefUrl": "/notes/193231 "}, {"RefNumber": "193968", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "O4F1 : Document Weight not assigned to Shipment.", "RefUrl": "/notes/193968 "}, {"RefNumber": "193001", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "LIA accounting document missing", "RefUrl": "/notes/193001 "}, {"RefNumber": "193770", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Oil BoM header cannot be deleted", "RefUrl": "/notes/193770 "}, {"RefNumber": "191705", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "TAX REVALUATION WITH MULTIPLE VALUATIONS AND TRANSIT-STOCK", "RefUrl": "/notes/191705 "}, {"RefNumber": "193194", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Goods Receipt Repricing / Excise Duty posting", "RefUrl": "/notes/193194 "}, {"RefNumber": "193619", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange number missing in netting index table", "RefUrl": "/notes/193619 "}, {"RefNumber": "192527", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "No reference to EXIT_SAPLOIIQ_001", "RefUrl": "/notes/192527 "}, {"RefNumber": "191923", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect statistical conditions in differential invoice", "RefUrl": "/notes/191923 "}, {"RefNumber": "193225", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "F&A Time UoM routine 004,005 Year Begnining Problem", "RefUrl": "/notes/193225 "}, {"RefNumber": "193227", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Abort A222 in OIK_TAS_DATA_POST", "RefUrl": "/notes/193227 "}, {"RefNumber": "192273", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Collected changes for TPI", "RefUrl": "/notes/192273 "}, {"RefNumber": "189441", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Performance in billing / unexpected invoice split", "RefUrl": "/notes/189441 "}, {"RefNumber": "192520", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Contracts assigned to an exchange agreement", "RefUrl": "/notes/192520 "}, {"RefNumber": "176858", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Differential invoice creation in batch processing", "RefUrl": "/notes/176858 "}, {"RefNumber": "186491", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Search help shows only shipment cost relevant shpm.", "RefUrl": "/notes/186491 "}, {"RefNumber": "185231", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Incorrect delivered quantity in schedule lines", "RefUrl": "/notes/185231 "}, {"RefNumber": "191696", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "OILSHI01 IDoc keeps status 64 after processing", "RefUrl": "/notes/191696 "}, {"RefNumber": "192046", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No KNA1 change pointer for updates to SCP OTWS", "RefUrl": "/notes/192046 "}, {"RefNumber": "191151", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Missing TPI functionality", "RefUrl": "/notes/191151 "}, {"RefNumber": "191429", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "PBL OTWS entries missing from TPI Location IDoc", "RefUrl": "/notes/191429 "}, {"RefNumber": "187744", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TRUOM; ELIKZandLoss; checkMM-LVORM; Missing GI; SetTDaction", "RefUrl": "/notes/187744 "}, {"RefNumber": "184012", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Movement based netting shows wrong sign", "RefUrl": "/notes/184012 "}, {"RefNumber": "188270", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees for goods receipt missing in fee history", "RefUrl": "/notes/188270 "}, {"RefNumber": "191413", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Receipts are missing in movement based netting", "RefUrl": "/notes/191413 "}, {"RefNumber": "191154", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "TPI IDocs fail with mixed user defaults", "RefUrl": "/notes/191154 "}, {"RefNumber": "191149", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Abort A222 in OIK_TAS_DATA_POST", "RefUrl": "/notes/191149 "}, {"RefNumber": "190248", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Generic price reference plant valuation", "RefUrl": "/notes/190248 "}, {"RefNumber": "147813", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "ME31: external details not copied", "RefUrl": "/notes/147813 "}, {"RefNumber": "183761", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "No fee redetermination in MM contract or order", "RefUrl": "/notes/183761 "}, {"RefNumber": "188640", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Contract update terminates if no fee exists", "RefUrl": "/notes/188640 "}, {"RefNumber": "187013", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Base Location not consistent between Sales and Purchase", "RefUrl": "/notes/187013 "}, {"RefNumber": "186341", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Max inventory/Min inventory related WL entries", "RefUrl": "/notes/186341 "}, {"RefNumber": "186408", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Parallel OILLDD inbound processing", "RefUrl": "/notes/186408 "}, {"RefNumber": "175799", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Correction Trip planning", "RefUrl": "/notes/175799 "}, {"RefNumber": "186151", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Shipment inbound process update task", "RefUrl": "/notes/186151 "}, {"RefNumber": "185895", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange statement: UoM of F&A condition missing", "RefUrl": "/notes/185895 "}, {"RefNumber": "184532", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Error mess V1227 New pric/ formulae on MM side", "RefUrl": "/notes/184532 "}, {"RefNumber": "185685", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Bad performance/Short dump for ROIAMMA3", "RefUrl": "/notes/185685 "}, {"RefNumber": "179868", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "OTWS data not available in views v_oiiotwbl, v_oiiotwkn", "RefUrl": "/notes/179868 "}, {"RefNumber": "184640", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees by PO picks up previous years GR", "RefUrl": "/notes/184640 "}, {"RefNumber": "181711", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Wrong data initialisation in OIK_TD_SHIPMENT_LOAD_PREPARE", "RefUrl": "/notes/181711 "}, {"RefNumber": "185245", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Custmr mastr recrd:Distrbtn of deltd partnr funct.", "RefUrl": "/notes/185245 "}, {"RefNumber": "185062", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Unexpected Error-messages after Planning Engine run", "RefUrl": "/notes/185062 "}, {"RefNumber": "185010", "RefComponent": "IS-OIL-DS", "RefTitle": "MM_EKKO: archiving run excludes contracts", "RefUrl": "/notes/185010 "}, {"RefNumber": "175059", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Post parked documents: message 00348", "RefUrl": "/notes/175059 "}, {"RefNumber": "181457", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Load ID assigned with Valid-to = 00:00:0000", "RefUrl": "/notes/181457 "}, {"RefNumber": "181955", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange statement for cancelled invoice", "RefUrl": "/notes/181955 "}, {"RefNumber": "169833", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MAP: Error in Pricing date during GR repricing", "RefUrl": "/notes/169833 "}, {"RefNumber": "181409", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Negative fees incorrect in movement based netting", "RefUrl": "/notes/181409 "}, {"RefNumber": "179653", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Short dump in invoice list processing", "RefUrl": "/notes/179653 "}, {"RefNumber": "178046", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Movement based netting issues", "RefUrl": "/notes/178046 "}, {"RefNumber": "166548", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exg. statement: reversal printed in wrong period", "RefUrl": "/notes/166548 "}, {"RefNumber": "184295", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Abort message OE 559 in Order Create", "RefUrl": "/notes/184295 "}, {"RefNumber": "184148", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "SOEK000331: Wrong conversion between sales and base UoM", "RefUrl": "/notes/184148 "}, {"RefNumber": "183374", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Timeframe function missing on screen 8024", "RefUrl": "/notes/183374 "}, {"RefNumber": "183036", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Message OE 224: STSI calloff can not be deleted manually", "RefUrl": "/notes/183036 "}, {"RefNumber": "184209", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "TSW: Missing R/3 Documents in Rundown possible", "RefUrl": "/notes/184209 "}, {"RefNumber": "179813", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Sales UOM conversion wrong in MC for condition record", "RefUrl": "/notes/179813 "}, {"RefNumber": "181437", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Language conversion incorrect for menu option", "RefUrl": "/notes/181437 "}, {"RefNumber": "168313", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Customizing control for message O1 023", "RefUrl": "/notes/168313 "}, {"RefNumber": "177535", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Cannot cancel from Plant SCP sub-screen", "RefUrl": "/notes/177535 "}, {"RefNumber": "171364", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Incorrect Qty in base UoM in Inv.verification", "RefUrl": "/notes/171364 "}, {"RefNumber": "175463", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong quantities in CO-PA after cancelling invoice", "RefUrl": "/notes/175463 "}, {"RefNumber": "176108", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "LOAD_NO_SPACE_FOR_TABLE for RBDAPP01 logical msg OILLDD", "RefUrl": "/notes/176108 "}, {"RefNumber": "177949", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Taxes Brail Oil: New exception  tables sales,puchase,t-fer", "RefUrl": "/notes/177949 "}, {"RefNumber": "175342", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Brazil taxes: Error on Sales return - ROB", "RefUrl": "/notes/175342 "}, {"RefNumber": "178164", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Screen 200 missing in program", "RefUrl": "/notes/178164 "}, {"RefNumber": "176988", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Update termination in worklist engine", "RefUrl": "/notes/176988 "}, {"RefNumber": "176785", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Missing SCP component check", "RefUrl": "/notes/176785 "}, {"RefNumber": "175494", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Customer Subscreen not accessible in SAPMOIGV", "RefUrl": "/notes/175494 "}, {"RefNumber": "176741", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Return data from user screen overwritten (OIGD)", "RefUrl": "/notes/176741 "}, {"RefNumber": "176589", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "PBL lookup for TPS parameters fails for OILSH01", "RefUrl": "/notes/176589 "}, {"RefNumber": "175140", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Abend in batch billing", "RefUrl": "/notes/175140 "}, {"RefNumber": "175501", "RefComponent": "IS-OIL-DS", "RefTitle": "IS-OIL: Customer master ALE / KNVV records overwritten", "RefUrl": "/notes/175501 "}, {"RefNumber": "173484", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "SOC Assignment in SD document not working", "RefUrl": "/notes/173484 "}, {"RefNumber": "173532", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Order/Contr.: TAS data not filled after delete/cre. item", "RefUrl": "/notes/173532 "}, {"RefNumber": "173715", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD customer function for vehicle overload checks", "RefUrl": "/notes/173715 "}, {"RefNumber": "173720", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Tables are not archived with archiving object OIG_SHPMNT", "RefUrl": "/notes/173720 "}, {"RefNumber": "171086", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "TPI: Collective corrections after Acceptance test", "RefUrl": "/notes/171086 "}, {"RefNumber": "172759", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "output determination: header or detail(scheduling)", "RefUrl": "/notes/172759 "}, {"RefNumber": "153222", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "external details at stock transfer order", "RefUrl": "/notes/153222 "}, {"RefNumber": "173079", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Zero billing date is filled in the VKDFS table", "RefUrl": "/notes/173079 "}, {"RefNumber": "170871", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL BRAZILIAN TAX SOLUTION FOR TRANSFER 833/835", "RefUrl": "/notes/170871 "}, {"RefNumber": "172681", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "TSW : correction for marine transport system in Nomination", "RefUrl": "/notes/172681 "}, {"RefNumber": "171736", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Nota fiscal with multiple items / Brazil Oil", "RefUrl": "/notes/171736 "}, {"RefNumber": "173139", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "TPI: Fix for Time frame settings handling", "RefUrl": "/notes/173139 "}, {"RefNumber": "167025", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Dynamic F&A repository selection screen", "RefUrl": "/notes/167025 "}, {"RefNumber": "170968", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Fieldsselection texts missing in RM07MMAT", "RefUrl": "/notes/170968 "}, {"RefNumber": "172738", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Functionality for Time frame settings at IMG", "RefUrl": "/notes/172738 "}, {"RefNumber": "172412", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Confirm status; Error message E752; Gain/loss mvmt o b", "RefUrl": "/notes/172412 "}, {"RefNumber": "172527", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ASTM Calculation doesn't work correctly with ASTM Interface", "RefUrl": "/notes/172527 "}, {"RefNumber": "171037", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Billing cancellation error.", "RefUrl": "/notes/171037 "}, {"RefNumber": "172063", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Mb netting: Wrong invoice cycle determined", "RefUrl": "/notes/172063 "}, {"RefNumber": "171053", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Posting dates in one period; Zero lines in loading", "RefUrl": "/notes/171053 "}, {"RefNumber": "169295", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Wrong pick qty. after TRX VL16", "RefUrl": "/notes/169295 "}, {"RefNumber": "168765", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Fields not updated for oil BoM header", "RefUrl": "/notes/168765 "}, {"RefNumber": "170668", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Bills of material in exchanges", "RefUrl": "/notes/170668 "}, {"RefNumber": "171065", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Collective corrections after Acceptance test", "RefUrl": "/notes/171065 "}, {"RefNumber": "169283", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: TAS index for GI not deleted if delivery deleted", "RefUrl": "/notes/169283 "}, {"RefNumber": "170803", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Outbound IDoc OILORD02: E1EDP01-ANTLF not filled", "RefUrl": "/notes/170803 "}, {"RefNumber": "170822", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Missing copy of some client independent object items", "RefUrl": "/notes/170822 "}, {"RefNumber": "170926", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "OIK37 number ranges maintenance", "RefUrl": "/notes/170926 "}, {"RefNumber": "169228", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Error during DELETE OIKLIDR", "RefUrl": "/notes/169228 "}, {"RefNumber": "151721", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fee edit/copy control fields are missing", "RefUrl": "/notes/151721 "}, {"RefNumber": "156700", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong UoM in automatically created purchase order", "RefUrl": "/notes/156700 "}, {"RefNumber": "170314", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Error in redetermination of netting statement", "RefUrl": "/notes/170314 "}, {"RefNumber": "170066", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fixed value fees are incorrect in the invoice", "RefUrl": "/notes/170066 "}, {"RefNumber": "102306", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong values in statistics update", "RefUrl": "/notes/102306 "}, {"RefNumber": "169160", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Corrections in SVPs: 3.1H/SVP3 4.0B/SVP1", "RefUrl": "/notes/169160 "}, {"RefNumber": "157920", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "VKDFS table is not filled properly during invoice generation", "RefUrl": "/notes/157920 "}, {"RefNumber": "168381", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Billing index entries missing after RVV05IVB", "RefUrl": "/notes/168381 "}, {"RefNumber": "166169", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees lost during cancellation of billing document", "RefUrl": "/notes/166169 "}, {"RefNumber": "165871", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Auto compartment planning with compatibilities", "RefUrl": "/notes/165871 "}, {"RefNumber": "163448", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Quantity schedule: Conversion error", "RefUrl": "/notes/163448 "}, {"RefNumber": "156697", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "If taxes are 0,accounting error in differential Inv", "RefUrl": "/notes/156697 "}, {"RefNumber": "150318", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Split excise duty may cause wrong IR postings", "RefUrl": "/notes/150318 "}, {"RefNumber": "158344", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD: Gain on 2-step trf. and GR to mult. store locations", "RefUrl": "/notes/158344 "}, {"RefNumber": "157801", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Loss in transfer/STO and change of valuation type", "RefUrl": "/notes/157801 "}, {"RefNumber": "151361", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Missing exg. number on billing index for diff. inv.", "RefUrl": "/notes/151361 "}, {"RefNumber": "157494", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Location ID in Credit note and Credit note request", "RefUrl": "/notes/157494 "}, {"RefNumber": "156993", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD User Exit 007 before creation of MM document", "RefUrl": "/notes/156993 "}, {"RefNumber": "157044", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Exit from step-loop 00043 in scheduling", "RefUrl": "/notes/157044 "}, {"RefNumber": "156906", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Abort on Location Archiving Deletion program", "RefUrl": "/notes/156906 "}, {"RefNumber": "152678", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Cost sharing on document item level", "RefUrl": "/notes/152678 "}, {"RefNumber": "154790", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Allow non-movement delivery items in TD shipments", "RefUrl": "/notes/154790 "}, {"RefNumber": "155337", "RefComponent": "IS-OIL-DS", "RefTitle": "IS-Oil: Material Master Screen Seqence Selection", "RefUrl": "/notes/155337 "}, {"RefNumber": "154200", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Bad runtime while MSEG-access in quantity schedule", "RefUrl": "/notes/154200 "}, {"RefNumber": "150951", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Missing overload check in compartment allocation details", "RefUrl": "/notes/150951 "}, {"RefNumber": "153121", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Scrolling SAPMOIGS3700; take over dates after user exit", "RefUrl": "/notes/153121 "}, {"RefNumber": "142981", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD ASTM and 0 qty; reason code; excise duty value", "RefUrl": "/notes/142981 "}, {"RefNumber": "150548", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Errors in IMG for shipment costing", "RefUrl": "/notes/150548 "}, {"RefNumber": "143644", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Error with UoM processing in TD Vehicle creation", "RefUrl": "/notes/143644 "}, {"RefNumber": "142387", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Change proposal at load/delivery confirmation", "RefUrl": "/notes/142387 "}, {"RefNumber": "111268", "RefComponent": "PP-REM-ADE", "RefTitle": "MF4A, MF4U: Display material documents, return", "RefUrl": "/notes/111268 "}, {"RefNumber": "98642", "RefComponent": "0 IS-OIL-BC", "RefTitle": "Order of IS-OIL notes 2.0d & 1.0d on R/3 3.1H (SP)", "RefUrl": "/notes/98642 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "IS-OIL", "From": "40B", "To": "40B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}