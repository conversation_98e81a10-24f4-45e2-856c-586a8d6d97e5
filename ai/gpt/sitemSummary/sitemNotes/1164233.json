{"Request": {"Number": "1164233", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 470, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000007021862017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=9D9A7CE7DC21C5279085A238AFB200FA"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1164233"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.05.2008"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-LC-IT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Italy"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Real Estate Localization", "value": "RE-FX-LC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Italy", "value": "RE-FX-LC-IT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC-IT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1164233 - ICI cockpit does not update coefficients for D-buildings"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p><B>RE-FX Italy ICI Cockpit (REXCITICI)</B><br /><br />The table TIVCITCALCDBD - D-Buildings coefficients is updated each year with the for that year valid value provided by law.<br /><br />Based on this the D-Building value is calculated as follows:<br /><B>D-Building value</B> = sum of (Annual costs * Annual coefficients) values, for all years where annual costs are entered.<br /><br />The annual costs can be entered on the ICI Master Data screen of the Architectural Object (AO) in a popup window. After entering the annual costs, the ICI Master Data calculates the D-Building value and stores it in the database.<br />It also changes the AO value of the ICI record, based on the D-building records belonging to the AO.<br /><br /><B>No recalculation</B><br />This calculation should run</p> <UL><LI>when the user moves in the ICI master data from one record to another in the cockpit,</LI></UL> <UL><LI>if the user presses the \"Recalculation\" button. (Only the coefficients have to be reread from the customizing).</LI></UL> <p>However, the recalculation only happens in the ICI master data, if the user opens the popup for the D-Building Maintenance.<br /><br /><B>Further errors in this topic:</B></p> <UL><LI>In some cases the internal table containing the D-Building data of the object in the ICI Master data gets deleted:</LI></UL><UL><UL><LI>If a calculation type was changed to 'FD' - Building type 'D'</LI></UL></UL> <UL><UL><LI>If the record with calculation type 'FD' is locked and the D-Building Data maintenance popup is opened</LI></UL></UL> <UL><LI>The popup is always changeable. For locked records it should be only in display mode, just like for AOs in display mode.</LI></UL> <p></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RE-FX Italy, ICI, ICI Cockpit D-Building Coefficient, Recalculation, ICI Master Data, REXCITICI, REBDAO</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The recalculation of the D-Building was not part of the ICI cockpit.<br /><br />After installing this note, the missing functionality is added to the cockpit and the master data and the D-Building Value will be updated.<br /><br />Also the the popup for maintaining the D-Building data was working incorrectly and had to be changed in this note.<br /><br />In details these changes are:<br /><br /><B>ICI Master Data:</B></p> <UL><LI>When moving to an ICI record within an AO, if the record is not locked, the AO value will be recalculated, based on the customizing values of the coefficient.</LI></UL> <UL><LI>When moving to a locked ICI record, no AO Value recalculation is carried out, in accordance with the further parts of the program.</LI></UL> <UL><LI>The D-Building data can only be deleted by the user via the popup.</LI></UL> <p><br /><B>D-Building Popup in the ICI master data:</B></p> <UL><LI>From a record with a different calculation type as 'FD', the popup is not accessible any more</LI></UL> <UL><LI>From a locked record with calculation type 'FD', the popup is accessible, but is displayed in read-only mode, showing all D-Building records that had been entered to the AO before.</LI></UL> <UL><LI>From a not locked record with calculation type 'FD', the popup is accessible, and displayed in change mode, showing all D-Building records that had been entered to the AO before, and allowing the entry of further records or the deletion of any of the existing records or the modification of any of the records.</LI></UL> <p><br /><B>ICI cockpit:</B></p> <UL><LI>The ICI cockpit handles (reads, changes and stores in database) now the D-Building data too, but changes are only possible via the 'Recalculation' function, where the possible changes in the customizing are taken into account.</LI></UL> <UL><LI>The D-Building records are updated, but the AO value of the selected ICI records is updated only if the record is not locked.</LI></UL><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Carry out the correction instruction.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON> (I033780)"}, {"Key": "Processor                                                                                           ", "Value": "D021316"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "872301", "RefComponent": "RE-FX-LC-IT", "RefTitle": "RE-FX Country Version for Italy", "RefUrl": "/notes/872301"}, {"RefNumber": "1476863", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI: negative balance calculation", "RefUrl": "/notes/1476863"}, {"RefNumber": "1472920", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Incorrect calculation for ICI record", "RefUrl": "/notes/1472920"}, {"RefNumber": "1468964", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Incorrect month of ownership in ICI record after recalc.", "RefUrl": "/notes/1468964"}, {"RefNumber": "1171224", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI data recalculation after changes in City parameters", "RefUrl": "/notes/1171224"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1476863", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI: negative balance calculation", "RefUrl": "/notes/1476863 "}, {"RefNumber": "1472920", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Incorrect calculation for ICI record", "RefUrl": "/notes/1472920 "}, {"RefNumber": "1468964", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Incorrect month of ownership in ICI record after recalc.", "RefUrl": "/notes/1468964 "}, {"RefNumber": "1171224", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI data recalculation after changes in City parameters", "RefUrl": "/notes/1171224 "}, {"RefNumber": "872301", "RefComponent": "RE-FX-LC-IT", "RefTitle": "RE-FX Country Version for Italy", "RefUrl": "/notes/872301 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-APPL 600", "SupportPackage": "SAPKGPAD14", "URL": "/supportpackage/SAPKGPAD14"}, {"SoftwareComponentVersion": "EA-APPL 602", "SupportPackage": "SAPK-60204INEAAPPL", "URL": "/supportpackage/SAPK-60204INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 603", "SupportPackage": "SAPK-60303INEAAPPL", "URL": "/supportpackage/SAPK-60303INEAAPPL"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "EA-APPL", "NumberOfCorrin": 3, "URL": "/corrins/**********/229"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 6, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "948957 ", "URL": "/notes/948957 ", "Title": "ALV: Selected rows are not taken into account", "Component": "RE-FX-IS"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1017502 ", "URL": "/notes/1017502 ", "Title": "Incorrect change documents", "Component": "RE-FX"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1138671 ", "URL": "/notes/1138671 ", "Title": "ICI Carry Over in Cockpit - Incorrect Months of ownership", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1138928 ", "URL": "/notes/1138928 ", "Title": "Unnecessary splits of ICI records", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1157680 ", "URL": "/notes/1157680 ", "Title": "Error messages with ICI records locked for recalculation", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "604", "Number": "1125593 ", "URL": "/notes/1125593 ", "Title": "ICI: historical objects, uninhabitable buildings, exemptions", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1138671 ", "URL": "/notes/1138671 ", "Title": "ICI Carry Over in Cockpit - Incorrect Months of ownership", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1138928 ", "URL": "/notes/1138928 ", "Title": "Unnecessary splits of ICI records", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1157680 ", "URL": "/notes/1157680 ", "Title": "Error messages with ICI records locked for recalculation", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1138671 ", "URL": "/notes/1138671 ", "Title": "ICI Carry Over in Cockpit - Incorrect Months of ownership", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1138928 ", "URL": "/notes/1138928 ", "Title": "Unnecessary splits of ICI records", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1157680 ", "URL": "/notes/1157680 ", "Title": "Error messages with ICI records locked for recalculation", "Component": "RE-FX-LC-IT"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}