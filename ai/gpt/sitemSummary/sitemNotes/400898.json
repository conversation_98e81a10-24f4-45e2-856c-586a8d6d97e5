{"Request": {"Number": "400898", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 430, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001803352017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000400898?language=E&token=7FCD6F1984A5C07FE0EB53867C25A12B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000400898", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000400898/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "400898"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 12}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "31.05.2001"}, "SAPComponentKey": {"_label": "Component", "value": "MM-PUR-VM-SET"}, "SAPComponentKeyText": {"_label": "Component", "value": "Subsequent Settlement"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Purchasing", "value": "MM-PUR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Vendor-Material Relationships and Conditions", "value": "MM-PUR-VM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Subsequent Settlement", "value": "MM-PUR-VM-SET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM-SET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "400898 - Recompilation bus.vol. data, incomes: sel. by arrangemt type"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>To correct the error, the reports for the recompilation of business volume data and income must allow the rebate arrangements to be selected according to arrangement type.<br />However, this is not provided.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Subsequent settlement (purchasing), volume-based rebate, recompilation of business volume data, recompilation of income, reports RWMBON07, RWMBON08, RWMBON37, RWMBON38.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Refer to the repair instructions or import the Support Package.<br /><br />In addition, you must extend the interface of function module MM_ARRANG_READ under Release 3.1 and 4.0B.<br /><br />Release 3.1I:<br />Tables interface<br />Tables parameter&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Reference structure&#x00A0;&#x00A0;Optional<br />T_BOART&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;EKORG_RAN&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;X<br />IDENT2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;IDENT_RAN&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;X<br /><br />Release 4.0B:<br />Tables interface<br />Tables parameter&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Reference structure&#x00A0;&#x00A0;Optional<br />T_BOART&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;EKORG_RAN&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;X<br /><br />Select table parameter T_T6B1T of function module MM_BOART_READ_ALL under Release 3.1I as optional.<br /><br />For reports RWMBON07, RWMBON08 and as of Release 4.5 for reports RWMBON37, RWMBON38, in each case, create the following selection text and text elements.<br />S_BOART&#x00A0;&#x00A0; Arrangement type<br />S_IDENT1&#x00A0;&#x00A0;Settlement calendar<br />S_IDENT2&#x00A0;&#x00A0;Arrangement calendar<br />040&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Arrangement type<br />041&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Settlement calendar<br />042&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Arrangement calendar<br /><br />Under Release 3.1I, create the messages<br />MN661 'Settlement document is missing (internal error)' and<br />MN663 'Inconsistencies on the database (internal error)'.<br /><br />Under Release 4.0B , create message<br />MN661 \"Settlement document is missing (internal error)\".<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023678)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000400898/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000400898/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000400898/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000400898/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000400898/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000400898/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000400898/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000400898/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000400898/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "425755", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Sel. rebate arrangement by arrangmt type, arrangmt calendar", "RefUrl": "/notes/425755"}, {"RefNumber": "40147", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 3.0", "RefUrl": "/notes/40147"}, {"RefNumber": "400893", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Detailed statement, message MN514", "RefUrl": "/notes/400893"}, {"RefNumber": "152725", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 4.5", "RefUrl": "/notes/152725"}, {"RefNumber": "104668", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 4.0", "RefUrl": "/notes/104668"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "104668", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 4.0", "RefUrl": "/notes/104668 "}, {"RefNumber": "152725", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 4.5", "RefUrl": "/notes/152725 "}, {"RefNumber": "40147", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 3.0", "RefUrl": "/notes/40147 "}, {"RefNumber": "425755", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Sel. rebate arrangement by arrangmt type, arrangmt calendar", "RefUrl": "/notes/425755 "}, {"RefNumber": "400893", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Detailed statement, message MN514", "RefUrl": "/notes/400893 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "31I", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 31I", "SupportPackage": "SAPKH31I84", "URL": "/supportpackage/SAPKH31I84"}, {"SoftwareComponentVersion": "SAP_HR 31I", "SupportPackage": "SAPKE31I84", "URL": "/supportpackage/SAPKE31I84"}, {"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B64", "URL": "/supportpackage/SAPKH40B64"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B44", "URL": "/supportpackage/SAPKH45B44"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 3, "URL": "/corrins/0000400898/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 6, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "30F", "ValidTo": "31I", "Number": "114111 ", "URL": "/notes/114111 ", "Title": "Recompilation of incomes from settlement documents RWMBON07", "Component": "MM-PUR-VM-SET"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "40A", "ValidTo": "40B", "Number": "114111 ", "URL": "/notes/114111 ", "Title": "Recompilation of incomes from settlement documents RWMBON07", "Component": "MM-PUR-VM-SET"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "40A", "ValidTo": "40B", "Number": "120610 ", "URL": "/notes/120610 ", "Title": "Data loss recompilation vendor volume data with short dump", "Component": "MM-PUR-VM-SET"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "40A", "ValidTo": "40B", "Number": "128279 ", "URL": "/notes/128279 ", "Title": "Automatic deletion business volume data during recompilation", "Component": "MM-PUR-VM-SET"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "40B", "ValidTo": "40B", "Number": "153054 ", "URL": "/notes/153054 ", "Title": "Recompltn of busnss volume data deletes all busnss vlme data", "Component": "MM-PUR-VM-SET"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "40B", "ValidTo": "40B", "Number": "176433 ", "URL": "/notes/176433 ", "Title": "Income recompilation, income deleted completely", "Component": "MM-PUR-VM-SET"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "40B", "ValidTo": "40B", "Number": "319666 ", "URL": "/notes/319666 ", "Title": "Deadlock with subsequent business volume recompilation", "Component": "MM-PUR-VM-SET"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}