{"Request": {"Number": "1088904", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 364, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016362752017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001088904?language=E&token=4121AC97BB5BD73095A397201DD63B07"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001088904", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001088904/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1088904"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 28}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.04.2018"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-RDM"}, "SAPComponentKeyText": {"_label": "Component", "value": "README: Upgrade Supplements"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "README: Upgrade Supplements", "value": "BC-UPG-RDM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-RDM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1088904 - Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Errors in the upgrade or update procedure or in the upgrade guides; preparations for the upgrade or update; additional information to the upgrade guide.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Update, upgrade, SUM, Software Update Manager, SAPup, SAP ERP Central Component 6.00, ECC 6.00</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You want to carry out an update or upgrade to&#160;SAP ECC 6.0 SR3 ABAP using the SUM 1.0, or the SAPup tool if your software component SAP_BASIS is lower than 7.0.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><br /><strong>CAUTION:</strong><br />This note is updated regularly!<br />Therefore, you should read it again immediately before starting the upgrade.<br /><br /></p>\r\n<p><strong>What information can I expect from this note?</strong></p>\r\n<p>This note describes problems that may occur during the system upgrade and provides information on how to solve them. This usually takes the form of references to other notes.<br />The main purpose of this note is to prevent data loss, upgrade shutdowns and long runtimes.<br />It deals with database-independent problems only.<br /><br /></p>\r\n<p><strong>Which additional notes do I require in preparation for the upgrade?</strong></p>\r\n<p>You need to refer to the relevant database-specific note below:<br />Database .................................................. Note number<br />_______________________________________________________________________<br />SAP MaxDB........................................................817463<br />IBM DB2 for i5/OS (NetWeaver-specific)..........................1122346<br />IBM DB2 for i5/OS (Business Suite-specific).....................1133823<br />IBM DB2 for Linux, UNIX and Windows..............................819876<br />IBM DB2 for z/OS.................................................815202<br />MS SQL Server....................................................825146<br />Oracle...........................................................819655<br /><br />You may also need to refer to the following important SAP Notes:<br />Short text ................................................ Note number<br />_______________________________________________________________________<br />Repairs for upgrade to SAP NW 7.0 AS ABAP........................813658<br />Corrections for SAPup for SAP NW 7.0 AS ABAP.....................821032<br />OCS: Known problems with Support Packages in SAP NW 7.0 AS ABAP..822379Short text ................................................ Note number<br /><br /><br />If you use the Software Update Manager 1.0 for an update or upgrade to SAP ECC 6.0 SR3 ABAP, you can find the latest SUM guide, the latest central SAP Note (including database-relevant SAP Notes), and further information such as SAP Community information on SUM at: <a target=\"_blank\" href=\"http://support.sap.com/sltoolset\">http://support.sap.com/sltoolset</a> -&gt; System Maintenance.</p>\r\n<p>&#160;</p>\r\n<p><strong>Contents</strong></p>\r\n<p>I/ ...... SAPup Keyword<br />II/ ..... Important General Information<br />III/ .... Corrections to the Guide<br />IV/ ..... Errors on the CD-ROM<br />V/ ...... Preparing the Upgrade<br />VI/...... Problems During the PREPARE and Upgrade Phases<br />VII/ .... Problems After the Upgrade<br />VIII/ ... Chronological Summary<br /><br /></p>\r\n<p><strong>I/ SAPup Keyword</strong></p>\r\n<p>In case your SAP_BASIS is lower than 7.0 and you use SAPup:</p>\r\n<p>The SAPup keyword is: 23299262<br />This must be entered in phase KEY_CHK.</p>\r\n<p>&#160;</p>\r\n<p><strong>II/ Important General Information</strong></p>\r\n<p><strong>**************************************************</strong></p>\r\n<p>The former tool SAPup was replaced by the Software Update Manager (SUM). See also SAP Note 1589311 and the blog:<br /><a target=\"_blank\" href=\"https://blogs.sap.com/2012/11/07/software-update-manager-sum-introducing-the-tool-for-software-maintenance/\">https://blogs.sap.com/2012/11/07/software-update-manager-sum-introducing-the-tool-for-software-maintenance/</a>.</p>\r\n<p>Use the Software Update Manager 1.0 for an update or upgrade to SAP ECC 6.0 SR3 ABAP when your source SAP_BASIS is 7.0 or higher. Note: If your SAP_BASIS is lower than 7.0, you must use the SAPup tool.</p>\r\n<p>You can find the latest SUM guide, the latest central SAP Note (including database-relevant SAP Notes), and further information such as SAP Community information on SUM at: <a target=\"_blank\" href=\"http://support.sap.com/sltoolset\">http://support.sap.com/sltoolset</a> -&gt; System Maintenance.</p>\r\n<p><strong>**************************************************</strong></p>\r\n<p>&#160;</p>\r\n<p><br />------------------------&lt; D032354 MAR/10/00 &gt;------------------------<br /><strong>Upgrade to CPRXRPM 450_700</strong><br />If your system contains add-on component CPRXRPM (displayed in phase IS_SELECT), you must include Support Package SAPK-45006INCPRXRPM in the upgrade. Otherwise, you will lose data in the XPRAS phase.<br />For more information, see Note <strong>1253309</strong>.<br /><br />-----------------------&lt; D026340 SEP/16/08 &gt;--------------------------<br /><strong>Limitation with Source Release SAP R/3 3.1I</strong><br />Consider the following constraint if you are upgrading an SAP R/3 3.1I system and you want to bind SAP ehancement package 3 for SAP ERP 6.0 on the target release in one step:<br />The upgrade program <strong>does not support</strong> binding of SAP enhancement packages for SAP ERP 6.0 for this source release.<br />You need to upgrade your system to SAP ERP 6.0 SR3 first, and then install SAP enhancement package 3 for SAP ERP 6.0 on your upgraded system in a second step.<br /><br />-----------------------&lt; D025323 MAY/24/02 &gt;--------------------------<br /><strong>Corrections and Repairs for the Upgrade</strong><br />Before the upgrade, it is vital that you check whether the following is available for your specific upgrade:</p>\r\n<ul>\r\n<li>A new version of SAPup. For more information, see <strong>Note 821032</strong>.</li>\r\n</ul>\r\n<ul>\r\n<li>Repairs to the ABAP upgrade programs. For more information, see <strong>Note 813658</strong>.</li>\r\n</ul>\r\n<p><strong>It is ESSENTIAL that you apply these two Notes</strong>.<br /><br />-----------------------&lt; D034302 DEC/03/03 &gt;--------------------------<br /><strong>Windows only: Execute program R3dllins.exe</strong><br />The 6.40 kernel is compiled with the new version of MS compiler and requires additional libraries for operation. To prevent problems during and after the upgrade, you must execute program R3dllins.exe on your central host, all application hosts, and on the remote shadow host, if you are planning to use one.<br />You can find the program on the Upgrade Master CD in the NT\\I386\\NTPATCH folder. It must be executed before you start PREPARE and directly from the NTPATCH folder (it can be shared). Copying and executing the program will not work.<br /><br />-----------------------&lt; D028310 JUL/19/02 &gt;--------------------------<br /><strong>Problems with the Shadow Instance.</strong><br />The following Notes contain information about problems with the shadow instance:</p>\r\n<ul>\r\n<li><strong>525677</strong>: Problems when starting the shadow instance</li>\r\n</ul>\r\n<ul>\r\n<li><strong>430318</strong>: Remote shadow instance on a different operating system</li>\r\n</ul>\r\n<p><br />------------------------&lt; D042621 FEB/02/05 &gt;-------------------------<br /><strong>LSWM now part of SAP_BASIS</strong><br />As of SAP Web AS 6.20, LSMW is part of SAP_BASIS. If you are using LSMW and your source release is based on SAP Web AS 6.10 or lower, do not implement LSMW after the upgrade.<br />For more information, see <strong>Note 673066</strong>.<br /><br />-----------------------&lt; D020904 MAR/15/07 &gt;---------------------------<br /><strong>MSCS: Upgrade of the ABAP system only, or Upgrade of the ABAP system with subsequent Java Add-In installation:</strong><br />In an MSCS configuration, you must split the ABAP central instance into the ABAP central instance (ASCS) and central instance, as described in <strong>SAP Note 1011190</strong>, if one of the following cases apply:</p>\r\n<ul>\r\n<li>You have upgraded your SAP NetWeaver '04 based ABAP system to SAP NetWeaver 7.0 based ABAP server system</li>\r\n</ul>\r\n<ul>\r\n<li>You have upgraded your SAP NetWeaver '04 based ABAP system to SAP NetWeaver 7.0 based ABAP server system, and now want to install a Java Add-In. The split must done before the Java Add-In installation.</li>\r\n</ul>\r\n<p>The split is required for the use of the enqueue replication server. If you run an MSCS configuration without enqueue replication server, which is the traditional MSCS configuration (where the clustered central instance&#160;&#160;runs the message server and enqueue work process), you will loose the enqueue lock table during a failover. The enqueue replication server configuration prevents this loss.<br />If you want to migrate an ABAP+Java system that is already upgraded to SAP NetWeaver 7.0 to the new configuration with enqueue replication server you must perform a homogeneous system copy.<br /><br />---------------------&lt;changed D028310 30/APR/08 &gt;---------------------<br />------------------------&lt; D022030 18/APR/08 &gt;-------------------------<br /><strong>New ASU Toolbox - Prerequisites</strong><br />The Application-Specific Upgrade (ASU) Toolbox has been enhanced. To be able to use the tool, you must update your ST-PI add-on to the following minimum versions:<br />ST-PI 2005_1_[46C,620,640] SP08, 2005_1_700 SP06 and 2005_1_710 SP04<br />During the upgrade, you are asked to include the equivalence Support Package level for ST-PI in phase BIND_PATCH. This eqivalence Support Package includes software parts that the Upgrade Accelerator part of theASU Toolbox needs to perform activities after the downtime. If you ignore the&#160;&#160;warning, you will not be able to finish the upgrade.<br />For more information, see Note <strong>1000009</strong>.<br /><br />---------------------------------------------------------------------<br /><br /><br /><br /></p>\r\n<p><strong>III/ Corrections to the Guides</strong></p>\r\n<p><br />------------------------&lt; D035318 18/MAR/2010 &gt;-------------------------<br /><strong>Combined Upgrade &amp; Unicode Conversion - Wrong Reference</strong><br />In section \"About This Document\", there is a wrong reference regarding the procedure \"Combined Upgrade &amp; Unicode Conversion (CU&amp;UC)\".<br />For more information about CU&amp;UC, see SAP Note <strong>928729</strong>.<br /><br />------------------------&lt; D038245 10/MAR/09 &gt;---------------------------<br /><strong>Limited Length of the DDIC Password</strong><br />During the Configuration Roadmap step, you need to enter the DDIC password. The upgrade guide does not mention that the length of the DDIC password is limited to 10 characters.<br /><br />----------------------&lt; D019369 10/MAR/09 &gt;---------------------------<br /><strong>Migration of Workload Statistics Data</strong><br />The report and table names in the guide are wrong. For the proper names and procedure, see SAP Notes <strong>1005238</strong> and <strong>1006116</strong>.<br /><br />-----------------------&lt; D038245 23/OCT/08 &gt;--------------------------<br /><strong>Section: Saving Files for Follow-Up Upgrades</strong><br />Do not use file PATBIND.SAV. Using file PATBIND.SAV for a follow-up upgrade leads to a segmentation fault in phase BIND_PATCH.<br /><br />-----------------------&lt; D033903 15/SEP/08 &gt;--------------------------<br /><strong>SDK Version 1.4.x for Upgrade Assistant</strong><br />The upgrade assistant only supports Java Software Development Kit (SDK) 1.4.x. It does not support version 1.5 or higher.<br /><br />-----------------------&lt; D001330 06/AUG/08 &gt;--------------------------<br /><strong>Making Entries for the Parameter Input Module</strong><br />Note that the maximum length of the mount directory path is 42 characters (not 50 as mentioned in the guide).<br /><br />---------------------&lt; D039661 APR/23/08 &gt;----------------------------<br /><strong>Downloading Enhancement Packages - Missing Note Reference</strong><br />The following reference is missing in section \"Downloading Enhancement Packages\":<br />If you want to use the Software Lifecycle Manager in the Maintenance Optimizer, see SAP Note <strong>1137683</strong>.<br /><br />------------------------&lt; D022030 APR/18/08 &gt;-------------------------<br /><strong>Application-Specific Upgrade Toolbox - Wrong Note Reference</strong><br />In section \"Application-Specific Upgrade Toolbox\" the referenced Note is wrong. For more information about the ASU toolbox, see Note <strong>1000009</strong>.<br /><br /><br />----------------------------------------------------------------------<br /><br /><br /><br /></p>\r\n<p><strong>IV/ Errors on the CD-ROM</strong></p>\r\n<p><br /><br /><br />---------------------------------------------------------------------<br /><br /></p>\r\n<p><strong>V/ Preparing the Upgrade</strong></p>\r\n<p><br />---------------------&lt; D001330 18/FEB/10 &gt;----------------------------<br /><strong>For Source Releases SAP R/3 4.6C and SAP R/3 4.6D Only: Prevent Loss of your BSVV objects during the upgrade</strong><br />If are upgrading your SAP system with the source SAP R/3 4.6C or SAP R/3 4.6D and have created customer-defined objects such as R3TR BSVV Z* before the upgrade, you might lose parts of your BSVV objects during the upgrade.<br />To solve this issue, proceed as described in SAP Note <strong>1432102</strong> sta rting the upgrade.<br /><br />---------------------&lt; D050445 10/MAR/09 &gt;----------------------------<br /><strong>HA on Windows only: Apply Note 828432 Before Starting the Upgrade</strong><br />For source release systems on SAP NetWeaver '04 only: If you run a high availability system on Windows and your kernel patch level is lower than169, you need to patch your kernel to at least PL 169 and then apply SAP Note <strong>828432</strong> prior to starting the upgrade.<br /><br />------------------------&lt; I815310 21/OCT/08 &gt;--------------------------<br /><strong>Source Rel. 4.6C SP128 for IS-OIL: Include SP14 of IS-OIL 600</strong><br />If your source release system includes IS-OIL 4.6C Support Package 128 and you try to include the equivalent Support Package 13 of IS-OIL 600 into the upgrade, you might get an error for tables OIUX3_TX_RP_EDT1 and OIUX3_TX_RP_EDT2 in the LONGPOST.LOG file.<br />To prevent this error, you need to include Support Package 14 of the target release into the upgrade.<br /><br />------------------------&lt; D023536 08/JUL/08 &gt;--------------------------<br /><strong>Avoid Conversion of Table BSEG</strong><br />As the table BSEG has been wrongly delivered, this might lead to unnecessary conversion of table BSEG. For more information, see Note <strong>1227677</strong>.<br /><br />------------------------&lt; D044675 17/JAN/08 &gt;--------------------------<br /><strong>Upgrade from Source Release 4.6C: Including a minimum SP level</strong><br />If your source release system includes SAP_HR 46C Support Package level D1, data loss may occur during the upgrade. To prevent any data loss, include at least Support Package 26 of the target release into the upgrade, although the upgrade program does not request it.<br />Alternatively, update your system to SAP_HR 46C Support Package level D2 before the upgrade.<br /><br />----------------------&lt;changed D001658 10/JUL/07 &gt;---------------------<br />------------------------&lt; D001330 03/JUL/07 &gt;--------------------------<br /><strong>Parameter for the Central Syslog rslg/collect*</strong><br />There may be conflicts with parameter values for the central syslog \"rslg/collect*\" of the default profile.<br />If the parameters are set with 39&lt;no.&gt; and 40&lt;no.&gt;, you must change them to 14&lt;no.&gt; and 15&lt;no.&gt;. Make sure that the ports are not used by other applications. If so, choose some other number.<br />For more information, see <strong>Note 1069225</strong>.<br /><br />----------------------&lt; D023250 09/MAY/07 &gt;---------------------------<br /><strong>Preparations for FI-CA</strong><br />If you are using FI-CA integrated with Funds Management, prepare your system as described in <strong>SAP Note 834815</strong>. This will prevent activation errors during the upgrade.<br /><br />-------------------&lt; D003327 05/FEB/07 &gt;-------------------------------<br /><strong>Back up customer-specific entries in table EDIFCT</strong><br />If you have maintained customer-specific entries in table EDIFCT, they may get lost during the upgrade. If you do not want to lose these entries, export them before the upgrade and reimport them after the upgrade.<br />For more information, see <strong>Note 865142</strong>.<br /><br />------------------------&lt; D044675 18/DEC/06 &gt;------------------------<br /><strong>New SPAM/SAINT Version on Source Release</strong><br />Before you include Support Package SAPKH60007 in the upgrade, you must apply at least the following SPAM/SAINT versions on your source release:</p>\r\n<ul>\r\n<li>Source Rel. SAP R/3 4.x: SPAM/SAINT version 0047</li>\r\n</ul>\r\n<ul>\r\n<li>Source Rel. SAP R/3 Enterprise, SAP ECC. 5.0: SPAM/SAINT version 0022</li>\r\n</ul>\r\n<p>If you use an older version, you will receive conflict messages for SAP_APPL SP 07 with ECC-DIMP, object list SAPKGES01G, during PREPARE. In this case, you can repeat the phase after applying the SPAM/SAINT update.<br /><br />-----------------------&lt; D028310 28/AUG/06 &gt;--------------------------<br /><strong>Update SAP Kernel on Source Release</strong><br />If you want to convert logical cluster tables incrementally using transaction ICNV during the upgrade, you must make sure that the level of the SAP kernel on the source release is high enough.<br />For more information, see <strong>SAP Note 946659</strong>.<br /><br />-----------------------&lt; D022188 24/APR/06 &gt;--------------------------<br /><strong>Prepare table COEP for the upgrade</strong><br />If your source release is SAP R/3 4.6C or below and table COEP contains many data, the conversion of this table during the upgrade can lead to aprolonged downtime.<br />For more information about reducing downtime, see <strong>Note 937389</strong>.<br /><br />----------------------&lt; D037027 12/DEC/05 &gt;---------------------------<br /><strong>SD Texts: Check Text Customizing on Source Release</strong><br />Before the upgrade, check your text Customizing in transaction VOTX as described in Note <strong>900607</strong>.<br /><br /><br />-----------------------&lt; D038006 02/NOV/05 &gt;--------------------------<br /><strong>Source Release on Kernel 6.40 with SuSE SLES 9 IA64 / RedHat 4.0 IA64</strong><br />If you are using the above combination on your source release and have set environment variable LD_ASSUME_KERNEL 2.4.21 according to SAP Note 797084, proceed as follows:<br />Before you start the upgrade, you need to delete the environment variable LD_ASSUME_KERNEL 2.4.21 and deactivate component icman by setting the following instance profile entry: rdisp/start_icman=false<br />Restart the system.<br />After the upgrade, delete the instance profile entry again.<br /><br />----------------------&lt;changed D022030 20/JUN/06 &gt;---------------------<br />------------------------&lt; I0276299 21/SEP/05 &gt;-------------------------<br /><strong>Checking Application Log</strong><br />Before the upgrade, check your application log according to Note 196113. This avoids short dumps in the XPRAS_UPG phase, where you receive runtime error SAPSQL_ARRAY_INSERT_DUPREC for table BALHDR.<br />For more information, see <strong>Note 196113</strong>.<br /><br />--------------------------&lt; D034302 16/AUG/05 &gt;-----------------------<br /><strong>Windows only: Last profile text line must end with line feedback</strong><br />Check that the last text line in all profiles ends with a line feedback.To check this, open a profile file with a text editor, go down with the cursor and ensure that the last line is empty. Otherwise there could be problems in the KX_SWITCH phase as some tools will not be able to handle the profiles properly.<br /><br />------------------------&lt; D031149 13/JUL/05 &gt;-------------------------<br /><strong>Source Rel. ECC DIMP 5.0: MPN Conversion Exit</strong><br />In release ECC DIMP 5.0 - up to and including CRT02 - the MPN conversion exit is delivered as \"active\" by default. The conversion exit is set to active, if the checkbox \"Active conversion exit\" in the following customizing transaction is flagged: Logistics - General --&gt; Interchangeability of Parts --&gt; Set Up Conversion Exit for Material Numbers.<br />If the MPN conversion exit should not be used in the system, it is very important that it is deactivated before the upgrade (in Source release ECC DIMP 5.0).<br />For more information on how to proceed, see <strong>Note 854523</strong>.<br />The problem is solved with CRT03 of release ECC DIMP 5.0.<br /><br />-----------------------&lt; D028310 NOV/03/04 &gt;-------------------------<br /><strong>Phase CONFCHK_IMP on Distributed Systems</strong><br />Phase CONFCHK_IMP offers you a list of operating systems to select from. This list only contains one entry \"Linux\" which is valid for both Linux and Linux IA64.<br /><br />------------------------&lt; D030022 14/OCT/04 &gt;-------------------------<br /><strong>Source Rel. 3.1I: Loss of Address Data</strong><br />If you are using component \"Plant Maintenance\" (PM-WOC-MN) and convert the addresses with report&#160;&#160;RSXADR05 before the upgrade from Source Release 3.1I, make sure to read <strong>Note 360929</strong>.<br />Otherwise you may lose address data during the upgrade.<br /><br />------------------------&lt; D031049 14/OCT/04 &gt;-------------------------<br /><strong>Source Rel. 4.0B: Project-Related Incoming Orders</strong><br />If you are using the preliminary solution for project-related incoming orders published with <strong>Note 118780</strong>, you have to modify data elements before the upgrade.<br />For more information, see <strong>Note 369542</strong>.<br /><br />------------------------&lt; D024329 14/OCT/04 &gt;-------------------------<br /><strong>Component PS-ST-WBS: Preliminary Inclusion of Field KIMSK</strong><br />If you do not want the upgrade to overwrite field KIMSK, you can include the field in the table of modifiable fields before the upgrade.<br />For more information, see <strong>Note 185315</strong>.<br /><br />----------------------&lt; D033898 06/OCT/04 &gt;---------------------------<br /><strong>Source Release 4.6C and below: SMODILOG Entries</strong><br />In Releases 4.6C and below, when you created customer-specific parameter effectivities, the system did not make SMODILOG entries. In order not to lose these customer-specific parameter effectivities during the upgrade, proceed as described in <strong>Note 741280</strong>.<br /><br />----------------------&lt; D038245 09/SEP/04 &gt;---------------------------<br /><strong>Source Release Extension Set 1.10: Exchange containers</strong><br />If your system was installed with SAP R/3 Enterprise Ext. Set 1.10 (based on SAP Web AS 6.20) and you are using a database that uses different containers for saving data (Oracle, Informix and DB2 UDB for UNIX and Windows), refer to <strong>note 674070</strong> before the upgrade.<br />Otherwise, the exchange containers (tablespaces/dbspaces) cannot be emptied during the upgrade and cannot be deleted after the upgrade.<br /><br />------------------------&lt; D038245 19/APR/04 &gt;------------------------<br /><strong>Unicode Systems: Downward Compatible Kernel 6.40</strong><br />If you are using the normal kernel for Release 6.20 with your Unicode system, PREPARE issues the error message: Could not open the ICU common library.<br />Before you start PREPARE, install the Downward Compatible Kernel for Release 6.40. Until this kernel is available, proceed as described in <strong>Note 716378</strong>.<br /><br />------------------------&lt; D032986 22/SEP/05 &gt;------------------------<br /><strong>Upgrading with reintegrated add-ons (retrofit)</strong><br />For SAP ERP Core Component 6.0 (ECC 6.0), more add-ons were reintegrated into the main ECC system or the Extension Sets.<br />The following Notes contain additional information on processing these add-ons before, during and after the upgrade.</p>\r\n<ul>\r\n<li><strong>Note 838002</strong> Add-ons (non-IS) integrated in SAP ECC 600</li>\r\n</ul>\r\n<ul>\r\n<li><strong>Note 838003</strong> Industry Add-ons integrated in SAP ECC 600</li>\r\n</ul>\r\n<p><br />--------------------------&lt; D032986 27/JAN/04 &gt;-----------------------<br /><strong>Upgrading with PI/PI-A</strong><br />For information on how to upgrade your system with PI/PI-A plug-ins, see <strong>Note 700779</strong>.<br /><br />--------------------------&lt; D025323 24/APR/03 &gt;-----------------------<br /><strong>Upgrade on AIX: saposcol</strong><br />Refer to <strong>Note 526694</strong> before the upgrade.<br /><br />--------------------------&lt; D019926 DEC/10/02 &gt;-----------------------<br /><strong>Upgrading with AIX 5.1</strong><br />If you want to upgrade with AIX 5.1, see <strong>Note 502532</strong> before starting PREPARE.<br /><br />-----------------------&lt; D025323 FEB/20/02 &gt;--------------------------<br /><strong>Source Releases on UNIX 32-bit or AIX 64-bit</strong><br />In some cases, you may have to upgrade the operating system to 64-bit before the actual upgrade.<br />When you upgrade from AIX 4.3 64-bit, you must perform some additional actions before upgrading.<br />For more information, see <strong>Notes 496963</strong> and <strong>499708</strong>.<br /><br />-----------------------------------------------------------------------<br /><br /><br /><br /></p>\r\n<p><strong>VI/ Problems During the PREPARE and Upgrade Phases</strong></p>\r\n<p><br />This section addresses known problems that cannot be avoided using preventive measures. These problems will only occur under very specific circumstances.</p>\r\n<p><strong>Problems During the PREPARE Phases</strong></p>\r\n<p><br />-----------------------&lt; D035061 13/JUN/13 &gt;--------------------------<br /><strong>Upgrade incl. EHP3: PI_BASIS package not found in EHP_INCLUSION</strong><br />You perform an upgrade to SAP ECC 6.0 SR3 and you include in this upgrade the installation of enhancement package 3.<br />During the upgrade, SAPup asks you in phase EHP_INCLUSION for the package PI_BASIS 2006_1_700, but this package is not available.<br />In this case, we recommend that you include EHP4 in the upgrade because this enhancement package has already the missing packages.<br />Otherwise you follow the instructions below with the idea that the upgrade passes phase EHP_INCLUSION with a lower SP level and does not reach the desired level until the phases IS_SELECT and BIND_PATCH.</p>\r\n<ol>1. Using the Maintenace Optimizer, you perform as planned a maintenance transaction to the desired EHP and SP level. Download all required packages to the server.</ol><ol>2. You perform an additional maintenance transaction with a lower SP level for EHP3, so that PI_BASIS can stay on the release 2005_1_700. Download at least the packages for PI_BASIS 2005_1_700, and the stack configuration file. The ABAP packages need to be extracted to DIR_TRANS/EPS/in.</ol><ol>3. You start the upgrade as usual, including the fix archive, and the current SAPup version that you can use without a newer patch.</ol><ol>4. In the extension module of SAPup you choose the following options:</ol><ol><ol>a) Phase UPLOAD_REQUEST:</ol></ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;\"Do you want to add (further) AddOn-CDs?\" -&gt; NO</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Do you want to search for new packages in directory \"/usr/sap/trans/EPS/in\" -&gt; SEARCH</p>\r\n<ol><ol>b) Phase EHP_INCLUSION:</ol></ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;\"Do you want to include an Enhancement Package?\"-&gt; YES</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;\"Do you want to add (further) AddOn-CDs?\" -&gt; NO</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Do you want to search for new packages in directory &lt;EPS/in&gt;? -&gt; SEARCH</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Please specify the stack configuration file (absolute path) -&gt; Here you need to enter the path to the stack configuration file from Step 2.</p>\r\n<ol><ol>c) Phase IS_SELECT:</ol></ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;At this point in time, the decision for PI_BASIS can be changed to \"Upgrade with CD\". As mountpoint, you enter the subdirectory UPGR_700 from the installation DVD.</p>\r\n<ol><ol>d) Phase BIND_PATCH:</ol></ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;If all packages are found, the desired SP level will be offered as default. If not, enter the SP level from the initially planned maintenance transaction (see step 1) and check if any missing packages are reported. These might be missing in EPS inbox.</p>\r\n<p><br /><br />-----------------------&lt; D038245 22/APR/09 &gt;--------------------------<br /><strong>AIX only: Required C++ Runtime Level</strong><br />When you start PREPARE, it checks whether you have the required C++ runtime level. You may get an error message like this:<br />ERROR: Your AIX 5.0 C++ runtime has version 7.0.0.x, but you need at least 8.0.0.5!<br />You can ignore this error and proceed with the upgrade.<br /><br />-----------------------&lt; D038245 23/OCT/08 &gt;--------------------------<br /><strong>Segmentation Fault in Phase BIND_PATCH</strong><br />If you have used file PATBIND.SAV to use the Support Package selection of a previous upgrade, you receive an error in phase BIND_PATCH.<br />To solve the problem, delete file PATBIND.SAV from the save subdirectory and repeat the phase.<br /><br />--------------------------&lt; D034302 03/APR/08 &gt;-----------------------<br /><strong>Windows only: Error when starting PREPARE</strong><br />When you start prepare.bat, the following error message displayed:<br />CScript Error: Can't find script engine \"JScript\" for script &lt;path&gt;\\prepare.js<br />To solve this problem, execute the following command in the command prompt:<br />regsvr32 /s %SystemRoot%\\System32\\jscript.dll<br /><br />--------------------------&lt; D023536 10/MAR/08 &gt;-----------------------<br /><strong>Source Release 3.1I and 4.0B: Error in phase TOOLIMPD2</strong><br />During the tool import in PREPARE, the following error message may appear in phase TOOLIMPD2:<br />&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;<br />DISTRIBUTION ERRORS and RETURN CODE in DS050107.O57<br />&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;<br />2EEDA616 Error when determining storage parameters<br />2EEDA616 Error when determining storage parameters<br />2EEDA616 Error when determining storage parameters<br />2EEDA616 Error when determining storage parameters<br />2EEDA616 Error when determining storage parameters<br />2EEDA616 Error when determining storage parameters<br />1 ETP111 exit code&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; : \"0\"<br />To eliminate the error, repeat the phase. For more information, see Note <strong>809493</strong>.<br /><br />--------------------------&lt; D038245 28/FEB/08 &gt;-----------------------<br /><strong>Windows only: MSSERV_INTERN port numbers</strong><br />During PREPARE, the upgrade tool requests a port number for the MSSERV_INTERN port. If your system is running on Windows, you cannot use port numbers within the following range: 6665 - 6669.<br /><br />-----------------------&lt; D028310 JUL/26/04 &gt;--------------------------<br /><strong>Source Release 3.1I and 4.0B: Error in Phase TR_CMDIMPORT_FDTASKS </strong><br />If there are nametab entries with a non-unique UUID, you get an error message in phase TR_CMDIMPORT_FDTASKS. Proceed as described in <strong>Note 705485</strong>.<br /><br />-----------------------&lt; D038245 APR/11/02 &gt;--------------------------<br /><strong>Termination in the TOOLIMPD3 phase</strong><br />The TOOLIMPD3 phase terminates in the PREPARE module import. The following message appears in the log file: ABAP runtime error CALL_FUNCTION_NO_RECEIVER<br />Receiving data for unknown CPIC link XXXXXX.<br />Repeat the phase and continue with the upgrade.<br /><br />-----------------------------------------------------------------------<br /><br /><br /></p>\r\n<p><strong>Problems During the Upgrade Phases</strong></p>\r\n<p><br /><br />--------------------&lt; D030182 05/AUG/10 &gt; ------------------------------<br />Phase: JOB_RCIFSTKUREN2<br />Note: 1483213<br />Description: Oracle Only: After this phase, on DB platform Oracle you may encounter an inconsistency with primary DB index of table<br />\"CIFSTKUCOUNT\".<br />Solution: Include the corresponding SP mentioned in SAP Note <strong>1483213</strong> into the upgrade.<br />If this SP is not yet released, and inconsistency with the DB index exists, rename index with execution of funtion module DB_RENAME_INDEX (transaction SE37, only on DB platform Oracle):<br />Parameters:<br />TABNAME:&#160;&#160;&#160;&#160;&#160;&#160; CIFSTKUCOUNT<br />INDNAME_NEW:&#160;&#160; CIFSTKUCOUNT&#126;0<br />INDNAME_OLD:&#160;&#160; CIFSTKUCNT&#126;0<br />To avoid errors with index information on Oracle systems, apply SAP Not <strong>1483213</strong>.<br /><br /><br />-------------------------&lt; D037517 18/FEB/10 &gt; -------------------------<br /><strong>Phase MAIN_SHADOW/START_SHDI_FIRST</strong><br />Description: An RFC error occurs (RFC LOGIN FAILED)<br />Solution:</p>\r\n<ol>1. In the SAPup.par file, set the value of the /UNIX/sleep_after_shdsys_start parameter to 200.</ol><ol>2. Restart the upgrade program and repeat the phase.</ol>\r\n<p><br />--------------------------&lt; D034302 16/AUG/05 &gt;-----------------------<br />Phase: KX_SWITCH (Windows only)<br />Description: Upgrade stops with an error message which asks you to check file NTPARVAL.LOG. In this file, messages like the following are displayed: fopen(...): Invalid argument<br />Check that the last text line in all profiles ends with a line feedback. To check the profile, open a profile file with a text editor, go down with the cursor, and ensure that the last line is empty. If it is not, add an empty line at the end of the file and save it.<br /><br />---------------------&lt;changed D003551 OCT/04/05 &gt;---------------------<br />--------------------------&lt; D021867 AUG/10/04 &gt;-----------------------<br />Phase: JOB_RDDNTPUR<br />Description: In the longpost-log file, you may get the error message: 3PETG447 Table and runtime object \"TA22EQU_WAO\" exist without DDIC reference.<br />or 3PETG447 Table and runtime object \"TVERTREE\" exist without DDIC reference<br />You can ignore this message.<br /><br />--------------------------&lt; I002675 OCT/07/05 &gt;-----------------------<br />Phase: SHADOW_IMPORT_INC<br />Note: 884809<br />Description: The Upgrade fails in phase SHADOW_IMPORT_INC. Check if the log files display error messages as stated in the above note. If so, proceed as described in the note.<br /><br />----------------------&lt; D027360 AUG/06/08 &gt;--------------------------<br />Phase: XPRAS_UPG<br />Description: If SAP_AP Support Package 13 is included into the upgrade, the phase may fail due to a syntax error in program /SAPCND/SAPLCUS_GEN_ON_THE_FLY.<br />To avoid this error, proceed as described in SAP Note <strong>1230048</strong>.<br /><br />--------------------------&lt; D023536 14/MAR/08 &gt;------------------------<br />Phase: XPRAS_UPG (sourse release &lt; 4.6C only)<br />Description: In certain cases the shortdump&#160;&#160;SAPSQL_ARRAY_INSERT_DUPREC in program \"SAPLSBAL_DB_INTERNAL\" may occur. If you encounter this error, repeat the phase. If the error persists, proceed as described in SAP Note <strong>196113</strong>.<br /><br />--------------------------&lt; D025323 MAY/23/02 &gt;-----------------------<br />Phase: CHK_POSTUP<br />Note: 197886<br />Description: If you have imported Notes 178631, 116095 or 69455 into your system before the upgrade, error messages for objects without DDIC reference appear in the log LONGPOST.LOG. Proceed as described in the Note.<br /><br />----------------------------------------------------------------------<br />Phase: CHK_POSTUP<br />Note: 996953<br />Description: Log file LONGPOST.LOG asks you to migrate a CMOD project although no project exists.<br /><br />----------------------&lt; D038245 FEB/28/06 &gt;--------------------------<br />Phase: MODPROFP_UPG<br />Description: When you are performing a dual stack upgrade, in phase MODPROFP_UPG the ABAP system is started together with the Java system part for the first time. The phase may fail if the SAP J2EE Engine does not start fast enough.<br />Check manually if the SAP J2EE Engine is running and if so, choose \"repeat\" to repeat the upgrade phase.<br /><br />----------------------------------------------------------------------<br /><br /><br /><br /><br /></p>\r\n<p><strong>VII/ Problems After the Upgrade</strong></p>\r\n<p><br />This section addresses known problems that cannot be avoided using preventive measures. These problems will only occur under specific circumstances.<br /><br />-----------------------&lt; D043830 18/JUN/10 &gt;--------------------------<br /><strong>Installation of Dialog Instances</strong><br />After the upgrade of the central instance, you need to install your dialog instances for your target release.<br />Before doing that, make sure you save all the profile values as recommendation values. For more information about installing a dialog instance, see the ERP installation guide.<br />After the installation of your new dialog instances, adjust the profile manually.<br /><br />-----------------------&lt; D034302 30/JAN/09 &gt;-------------------------<br /><strong>Inactive Table /BI0/PTCTUSERNM and View /BI0/MTCTUSERNM</strong><br />If after the upgrade the transaction <strong>db02</strong> shows that the table /BI0/PTCTUSERNM and the view /BI0/MTCTUSERNM do not exist in the database, activate these objects manually using transaction <strong>se11</strong>.<br /><br />-----------------------&lt; D014623 08/07/08 &gt;--------------------------<br /><strong>Check the Time Zone Settings After the Upgrade</strong><br />Check the time zone settings in the upgraded system as described in Note <strong>741734</strong>. The time zone of the operating system must be the same as the time zone of the SAP system in all clients.<br /><br />-----------------------&lt; D000587 25/FEB/08 &gt;--------------------------<br /><strong>Error when Converting Print Parameters</strong><br />After the upgrade, in some clients the printing parameters have either gone or been replaced by other parameters. For more information, see Note <strong>1142364</strong>.<br />The problem is solved with SAP Basis Support Package 16.<br /><br />-----------------------&lt; D033258 DEC/12/05 &gt;-------------------------<br /><strong>Addition to the Release Notes</strong><br />The depreciation posting run and the periodic posting run for APC values must be converted to document number ranges that use an internal document number assignment.<br />For more information, see <strong>Note 890976</strong>.<br /><br />--------------------&lt;enhanced D038245/JUNE/05 &gt;----------------------<br />-----------------------&lt; D003551 02/MAY/05 &gt;-------------------------<br /><strong>Check Primary Index of Table MEMGMT_DEPLOYMNT</strong><br />After the upgrade, check that the primary index of table MEMGMT_DEPLOYMNT exists. If not, create the index manually.<br />If you still get the message that MEMGMT_DEPLOYMNT00 is an unknown object in the dictionary, drop it on the database level.<br /><br />------------------------&lt; D020815 AUG/23/02 &gt;-------------------<br /><strong>SPAU: Names of interface methods are truncated</strong><br />Some methods (ABAP objects) that were modified and overwritten by the upgrade can be displayed in transaction SPAU with their names shortened to 30 characters.<br />As a result, the system may also incorrectly sort methods in SPAU under \"Deleted objects\".<br />Caution: Deleted objects are not displayed in the standard selection in SPAU. It is easily possible to overlook these!<br />For more information about the correction, see <strong>Note 547773</strong>.<br /><br />----------------------------------------------------------------------<br /><strong>Linux: Importing the new saposcol version</strong><br />For more information, see <strong>Note 19227</strong>.<br /><br />----------------------------------------------------------------------<br /><strong>ReliantUNIX: saposcol version 32-bit or 64-bit</strong><br />For more information, see <strong>Note 148926</strong>.<br /><br />----------------------------------------------------------------------<br /><strong>Solaris: saposcol version 32-bit or 64- bit</strong><br />For more information, see <strong>Note 162980</strong>.<br /><br />----------------------------------------------------------------------<br /><br /><br /><br /><br /></p>\r\n<p><strong>VIII/ Chronological Summary</strong></p>\r\n<p><br />Date.....Topic..Short description<br />-----------------------------------------------------------------------<br />JUN/13/13...VI..Upgrade incl. EHP3: PI_BASIS package not found in EHP_INCLUSION<br />AUG/05/10...VI..Phase: JOB_RCIFSTKUREN2<br />JUN/18/10..VII..Installation of Dialog Instances<br />MAR/18/10..III..Combined Upgrade &amp; Unicode Conversion - Wrong Reference<br />FEB/18/10...VI..Phase MAIN_SHADOW/START_SHDI_FIRST<br />FEB/18/10....V..Prevent Loss of your BSVV Objects During the Upgrade<br />APR/22/09...VI..AIX only: Required C++ Runtime Level<br />MAR/10/09....V..HA on Windows only: Apply Note 828432 Before the Upgrade<br />MAR/10/09..III..Limited Length of the DDIC Password<br />MAR/10/09...II..Upgrade to CPRXRPM 450_700<br />MAR/10/09..III..Migration of Workload Statistics Data<br />JAN/30/09..VII..Inactive Table /BI0/PTCTUSERNM and View /BI0/MTCTUSERNM<br />OCT/23/08..III..Section: Saving Files for Follow-Up Upgrades<br />OCT/23/08...VI..Segmentation Fault in Phase BIND_PATCH<br />OCT/21/08....V..Source Rel. IS-OIL 4.6C SP128: Include IS-OIL 600 SP14<br />SEP/16/08...II..Limitation with Source Release SAP R/3 3.1I<br />SEP/15/08..III..SDK Version 1.4.x for Upgrade Assistant<br />AUG/06/08..III..Making Entries for the Parameter Input Module<br />AUG/06/08...VI...Phase XPRAS_UPG: Program /SAPCND/SAPLCUS_GEN_ON_THE_FLY<br />JUL/08/08..VII..Check the Time Zone Settings After the Upgrade<br />JUL/08/08....V..Avoid Conversion of Table BSEG<br />APR/23/08..III..Downloading Enhancement Packages: Missing Note Reference<br />APR/18/08...II..New ASU Toolbox - Prerequisites<br />APR/18/08..III..Application-Specific Upgrade Toolbox - Wrong Note<br />APR/03/08...VI..Windows only: Error when starting PREPARE<br />MAR/14/08...VI..Phase XPRAS_UPG - shortdump&#160;&#160;SAPSQL_ARRAY_INSERT_DUPREC<br />MAR/10/08...VI..Source Release 3.1I and 4.0B: Error in phase TOOLIMPD2<br />FEB/28/08...VI..Windows only: MSSERV_INTERN port numbers<br />FEB/25/08..VII..Error when Converting Print Parameters<br />JAN/17/08....V..Source Release SAP R/3 4.6C: Including a min. SP level<br />JUL/03/07....V..Parameter for the Central Syslog rslg/collect*<br />MAY/09/07....V..Preparations for FI-CA<br />MAR/15/07...II..MSCS Configuration<br />FEB/28/07...VI..Phase CHK_POSTUP - CMOD/SMOD migration<br />FEB/05/07....V..Back up customer-specific entries in table EDIFCT<br />DEC/18/06....V..New SPAM/SAINT Version on Source Release<br />AUG/28/06....V..Update SAP Kernel on Source Release<br />APR/24/06....V..Prepare table COEP for the upgrade<br />FEB/28/06...VI..Phase MODPROFP_UPG fails - check J2EE Engine<br />DEC/12/05....V..SD Texts: Check Text Customizing on Source Release<br />DEC/12/05..VII..Addition to the Release Notes<br />NOV/02/05....V..Kernel 6.40 with SuSE SLES 9 IA64 / RedHat 4.0 IA64<br />OCT/07/05...VI..Phase SHADOW_IMPORT_INC<br />SEP/22/05....V..Upgrading with reintegrated add-ons (retrofit)<br />SEP/21/05....V..Checking Application Log<br />AUG/16/05...VI..Windows only: KX_SWITCH - Check file NTPARVAL.LOG.<br />AUG/16/05....V..Windows only: Last profile text line - line feedback<br />JUL/13/05....V..Source Rel. ECC DIMP 5.0: MPN Conversion Exit<br />MAY/02/05..VII..Check Primary Index of Table MEMGMT_DEPLOYMNT<br />FEB/02/05...II..LSWM now part of SAP_BASIS<br />OCT/14/04....V..Component PS-ST-WBS: Prel. Inclusion of Field KIMSK<br />OCT/14/04....V..Source Rel. 4.0B: Project-Related Incoming Orders<br />OCT/14/04....V..Source Rel. 3.1I: Loss of Address Data<br />OCT/06/04....V..Source Release 4.6C and below: SMODILOG Entries<br />SEP/09/04....V..Source Release Extension Set 1.10: Exchange containers<br />AUG/10/04...VI..Phase JOB_RDDNTPUR: TA22EQU_WAO without reference<br />JUL/26/04...VI..Start-Rel. 3.1I / 4.0B: Phase TR_CMDIMPORT_FDTASKS<br />APR/19/04....V..Unicode Systems: Downward Compatible Kernel 6.40<br />JAN/27/04....V..Upgrading with PI/PI-A<br />DEC/03/03...II..Windows only: Execute program R3dllins.exe<br />APR/24/03....V..Upgrade on AIX: saposcol<br />DEC/10/02....V..Upgrading with AIX 5.1<br />AUG/23/02..VII..SPAU: Names of interface methods are truncated<br />JUL/19/02...II..Problems with the shadow instance<br />MAY/24/02...II..Corrections and repairs for the upgrade<br />MAY/23/02...VI..Phase CHK_POSTUP - objects without DDIC reference<br />APR/11/02...VI..Termination in the TOOLIMPD3 phase<br />FEB/20/02....V..Source releases on UNIX 32-bit or AIX 64-bit<br />OCT/19/00..VII..Linux:&#160;&#160;Importing the new saposcol version<br />FEB/16/00..VII..saposcol version 32-bit or 64-bit on Reliant UNIX<br />FEB/16/00..VII..saposcol version 32-bit or 64-bit on Solaris<br />----------------------------------------------------------------------</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D031330)"}, {"Key": "Processor                                                                                           ", "Value": "I069357"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001088904/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001088904/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001088904/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001088904/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001088904/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001088904/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001088904/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001088904/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001088904/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "996953", "RefComponent": "BC-DWB-CEX", "RefTitle": "SPAU entry exists for nonexistent CMOD project", "RefUrl": "/notes/996953"}, {"RefNumber": "946659", "RefComponent": "BC-UPG-TLS", "RefTitle": "ICNV for cluster tables and extension of TIMESTAMP", "RefUrl": "/notes/946659"}, {"RefNumber": "938706", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "ME21N: Default values disappear after upgrade", "RefUrl": "/notes/938706"}, {"RefNumber": "937389", "RefComponent": "FI-GL-GL-RP", "RefTitle": "COEP line item display incorrect after upgrade", "RefUrl": "/notes/937389"}, {"RefNumber": "900607", "RefComponent": "SD-BF-TP", "RefTitle": "Checklist: SD texts when upgrading to 4.70 and higher", "RefUrl": "/notes/900607"}, {"RefNumber": "890976", "RefComponent": "FI-AA-AA-E", "RefTitle": "Converting closing report to internal doc number assgnmnt", "RefUrl": "/notes/890976"}, {"RefNumber": "884809", "RefComponent": "BC-DB-MSS", "RefTitle": "SQL Error 11 - General network error", "RefUrl": "/notes/884809"}, {"RefNumber": "865142", "RefComponent": "BC-MID-ALE", "RefTitle": "Customer-specific entries in EDIFCT are deleted", "RefUrl": "/notes/865142"}, {"RefNumber": "858271", "RefComponent": "EHS", "RefTitle": "Upgrade to EH&S in ERP 2005/ERP 6.0", "RefUrl": "/notes/858271"}, {"RefNumber": "854523", "RefComponent": "IS-ADEC-MPN-MD", "RefTitle": "MPN conversion exit is delivered as 'active' by default", "RefUrl": "/notes/854523"}, {"RefNumber": "838003", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Industry add-ons integrated into SAP ECC 6.0", "RefUrl": "/notes/838003"}, {"RefNumber": "838002", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Add-ons (non-IS) integrated into SAP ECC 600", "RefUrl": "/notes/838002"}, {"RefNumber": "834815", "RefComponent": "XX-PROJ-FI-CA", "RefTitle": "Upgrade to SAP ECC 6.00 with FM", "RefUrl": "/notes/834815"}, {"RefNumber": "828432", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/828432"}, {"RefNumber": "821032", "RefComponent": "BC-UPG-RDM", "RefTitle": "Corrections for SAPup release 7.00", "RefUrl": "/notes/821032"}, {"RefNumber": "813658", "RefComponent": "BC-UPG-RDM", "RefTitle": "Repairs for upgrades to products based on SAP NW 2004s AS", "RefUrl": "/notes/813658"}, {"RefNumber": "797084", "RefComponent": "BC-OP-LNX-SUSE", "RefTitle": "SUSE LINUX Enterprise Server 9: Installation notes", "RefUrl": "/notes/797084"}, {"RefNumber": "741734", "RefComponent": "BC-SRV-TIM-TZ", "RefTitle": "Incorrect times due to the time zone settings", "RefUrl": "/notes/741734"}, {"RefNumber": "741280", "RefComponent": "LO-ECH", "RefTitle": "OS60: Updating parameter effectivities", "RefUrl": "/notes/741280"}, {"RefNumber": "716378", "RefComponent": "BC-UPG-PRP", "RefTitle": "Missing libraries during Unicode upgrade", "RefUrl": "/notes/716378"}, {"RefNumber": "705485", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/705485"}, {"RefNumber": "700779", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Upgrading SAP ECC 500/600 with PI/PI-A/SLL_PI", "RefUrl": "/notes/700779"}, {"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455"}, {"RefNumber": "674070", "RefComponent": "BC-UPG-PRP", "RefTitle": "Tables in the substitution container after an upgrade", "RefUrl": "/notes/674070"}, {"RefNumber": "673066", "RefComponent": "BC-SRV-DX-LSM", "RefTitle": "LSMW: Upgrade to SAP Enterprise 4.7 (or Basis 6.20)", "RefUrl": "/notes/673066"}, {"RefNumber": "626272", "RefComponent": "BC-UPG", "RefTitle": "Termination in the JOB_RSTLANUPG phase", "RefUrl": "/notes/626272"}, {"RefNumber": "557314", "RefComponent": "BC-ABA-TO", "RefTitle": "As of Release 610: TVARV replaced with TVARVC", "RefUrl": "/notes/557314"}, {"RefNumber": "547773", "RefComponent": "BC-DWB-CEX", "RefTitle": "SPAU: interface method names are truncated", "RefUrl": "/notes/547773"}, {"RefNumber": "526694", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "saposcol based on AIX perfstat library", "RefUrl": "/notes/526694"}, {"RefNumber": "525677", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/525677"}, {"RefNumber": "502532", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/502532"}, {"RefNumber": "499708", "RefComponent": "BC-UPG", "RefTitle": "Additional information on upgrading to Basis 620 with AIX", "RefUrl": "/notes/499708"}, {"RefNumber": "496963", "RefComponent": "BC-UPG-RDM", "RefTitle": "32-bit source release in upgrades to 620", "RefUrl": "/notes/496963"}, {"RefNumber": "430318", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/430318"}, {"RefNumber": "369542", "RefComponent": "PS-REV-IO", "RefTitle": "CJA1: Upgrade of Release 4.0 preliminary solution to >= 4.5", "RefUrl": "/notes/369542"}, {"RefNumber": "360929", "RefComponent": "PM-WOC-MN", "RefTitle": "Loss of address data on execution of RSXADR05", "RefUrl": "/notes/360929"}, {"RefNumber": "197886", "RefComponent": "BC-UPG", "RefTitle": "Upgrade: messages in CHK_POSTUP: BDL*, STSL*, BAM*, SQLR*", "RefUrl": "/notes/197886"}, {"RefNumber": "196113", "RefComponent": "BC-SRV-BAL", "RefTitle": "SAPSQL_ARRAY_INSERT_DUPREC, BALHDR", "RefUrl": "/notes/196113"}, {"RefNumber": "19227", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "Retrieving the latest saposcol", "RefUrl": "/notes/19227"}, {"RefNumber": "185315", "RefComponent": "PS-ST-WBS", "RefTitle": "SFAC:Advance inclusion of KIMSK into field select.", "RefUrl": "/notes/185315"}, {"RefNumber": "178631", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/178631"}, {"RefNumber": "162980", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "saposcol for 64-bit Solaris", "RefUrl": "/notes/162980"}, {"RefNumber": "148926", "RefComponent": "BC-OP-FTS", "RefTitle": "saposcol version 32 or 64 bit on ReliantUNIX", "RefUrl": "/notes/148926"}, {"RefNumber": "1253309", "RefComponent": "PPM-PFM", "RefTitle": "Changes to tables ACO_USER and ACO_ROLE after RPM XPRA", "RefUrl": "/notes/1253309"}, {"RefNumber": "1230048", "RefComponent": "AP-PRC-CON", "RefTitle": "Syntax error for XPRAS_UPD for SAP_AP SP13 bound to upgrade", "RefUrl": "/notes/1230048"}, {"RefNumber": "1227677", "RefComponent": "FI-GL", "RefTitle": "Supply BSEG for SAPKH60014 due to problem in SR3", "RefUrl": "/notes/1227677"}, {"RefNumber": "1225752", "RefComponent": "IS-AFS", "RefTitle": "BAPIs valid for usage with SAP Apparel and Footwear 6.3", "RefUrl": "/notes/1225752"}, {"RefNumber": "1170069", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1170069"}, {"RefNumber": "116095", "RefComponent": "SV-SMG-SDD", "RefTitle": "Solution Tools Plug-In (TCC Basis Tools and Trace Tools)", "RefUrl": "/notes/116095"}, {"RefNumber": "1133823", "RefComponent": "BC-UPG-RDM", "RefTitle": "Upgrade to SAP Business Suite 2005 SR3: IBM i5/OS", "RefUrl": "/notes/1133823"}, {"RefNumber": "1069225", "RefComponent": "BC-CST-DP", "RefTitle": "NO HW ID RECEIVED BY MSSG SERVER", "RefUrl": "/notes/1069225"}, {"RefNumber": "1068662", "RefComponent": "MM-PUR-REQ-GUI", "RefTitle": "ME51N: Default values disappear after you upgrade", "RefUrl": "/notes/1068662"}, {"RefNumber": "1011190", "RefComponent": "BC-OP-NT", "RefTitle": "MSCS:Splitting the Central Instance After Upgrade to 7.0/7.1", "RefUrl": "/notes/1011190"}, {"RefNumber": "1006116", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Migration of workload statistics data to NW2004s (2)", "RefUrl": "/notes/1006116"}, {"RefNumber": "1005238", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Migration of workload statistics data to NW2004s", "RefUrl": "/notes/1005238"}, {"RefNumber": "1000009", "RefComponent": "SV-SMG-ASU", "RefTitle": "ASU Toolbox 2008", "RefUrl": "/notes/1000009"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455 "}, {"RefNumber": "1000009", "RefComponent": "SV-SMG-ASU", "RefTitle": "ASU Toolbox 2008", "RefUrl": "/notes/1000009 "}, {"RefNumber": "19227", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "Retrieving the latest saposcol", "RefUrl": "/notes/19227 "}, {"RefNumber": "1011190", "RefComponent": "BC-OP-NT", "RefTitle": "MSCS:Splitting the Central Instance After Upgrade to 7.0/7.1", "RefUrl": "/notes/1011190 "}, {"RefNumber": "674070", "RefComponent": "BC-UPG-PRP", "RefTitle": "Tables in the substitution container after an upgrade", "RefUrl": "/notes/674070 "}, {"RefNumber": "865142", "RefComponent": "BC-MID-ALE", "RefTitle": "Customer-specific entries in EDIFCT are deleted", "RefUrl": "/notes/865142 "}, {"RefNumber": "797084", "RefComponent": "BC-OP-LNX-SUSE", "RefTitle": "SUSE LINUX Enterprise Server 9: Installation notes", "RefUrl": "/notes/797084 "}, {"RefNumber": "1005238", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Migration of workload statistics data to NW2004s", "RefUrl": "/notes/1005238 "}, {"RefNumber": "1006116", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Migration of workload statistics data to NW2004s (2)", "RefUrl": "/notes/1006116 "}, {"RefNumber": "838002", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Add-ons (non-IS) integrated into SAP ECC 600", "RefUrl": "/notes/838002 "}, {"RefNumber": "813658", "RefComponent": "BC-UPG-RDM", "RefTitle": "Repairs for upgrades to products based on SAP NW 2004s AS", "RefUrl": "/notes/813658 "}, {"RefNumber": "821032", "RefComponent": "BC-UPG-RDM", "RefTitle": "Corrections for SAPup release 7.00", "RefUrl": "/notes/821032 "}, {"RefNumber": "834815", "RefComponent": "XX-PROJ-FI-CA", "RefTitle": "Upgrade to SAP ECC 6.00 with FM", "RefUrl": "/notes/834815 "}, {"RefNumber": "1133823", "RefComponent": "BC-UPG-RDM", "RefTitle": "Upgrade to SAP Business Suite 2005 SR3: IBM i5/OS", "RefUrl": "/notes/1133823 "}, {"RefNumber": "673066", "RefComponent": "BC-SRV-DX-LSM", "RefTitle": "LSMW: Upgrade to SAP Enterprise 4.7 (or Basis 6.20)", "RefUrl": "/notes/673066 "}, {"RefNumber": "858271", "RefComponent": "EHS", "RefTitle": "Upgrade to EH&S in ERP 2005/ERP 6.0", "RefUrl": "/notes/858271 "}, {"RefNumber": "1253309", "RefComponent": "PPM-PFM", "RefTitle": "Changes to tables ACO_USER and ACO_ROLE after RPM XPRA", "RefUrl": "/notes/1253309 "}, {"RefNumber": "937389", "RefComponent": "FI-GL-GL-RP", "RefTitle": "COEP line item display incorrect after upgrade", "RefUrl": "/notes/937389 "}, {"RefNumber": "946659", "RefComponent": "BC-UPG-TLS", "RefTitle": "ICNV for cluster tables and extension of TIMESTAMP", "RefUrl": "/notes/946659 "}, {"RefNumber": "1227677", "RefComponent": "FI-GL", "RefTitle": "Supply BSEG for SAPKH60014 due to problem in SR3", "RefUrl": "/notes/1227677 "}, {"RefNumber": "1230048", "RefComponent": "AP-PRC-CON", "RefTitle": "Syntax error for XPRAS_UPD for SAP_AP SP13 bound to upgrade", "RefUrl": "/notes/1230048 "}, {"RefNumber": "1225752", "RefComponent": "IS-AFS", "RefTitle": "BAPIs valid for usage with SAP Apparel and Footwear 6.3", "RefUrl": "/notes/1225752 "}, {"RefNumber": "496963", "RefComponent": "BC-UPG-RDM", "RefTitle": "32-bit source release in upgrades to 620", "RefUrl": "/notes/496963 "}, {"RefNumber": "1068662", "RefComponent": "MM-PUR-REQ-GUI", "RefTitle": "ME51N: Default values disappear after you upgrade", "RefUrl": "/notes/1068662 "}, {"RefNumber": "938706", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "ME21N: Default values disappear after upgrade", "RefUrl": "/notes/938706 "}, {"RefNumber": "547773", "RefComponent": "BC-DWB-CEX", "RefTitle": "SPAU: interface method names are truncated", "RefUrl": "/notes/547773 "}, {"RefNumber": "741734", "RefComponent": "BC-SRV-TIM-TZ", "RefTitle": "Incorrect times due to the time zone settings", "RefUrl": "/notes/741734 "}, {"RefNumber": "369542", "RefComponent": "PS-REV-IO", "RefTitle": "CJA1: Upgrade of Release 4.0 preliminary solution to >= 4.5", "RefUrl": "/notes/369542 "}, {"RefNumber": "116095", "RefComponent": "SV-SMG-SDD", "RefTitle": "Solution Tools Plug-In (TCC Basis Tools and Trace Tools)", "RefUrl": "/notes/116095 "}, {"RefNumber": "900607", "RefComponent": "SD-BF-TP", "RefTitle": "Checklist: SD texts when upgrading to 4.70 and higher", "RefUrl": "/notes/900607 "}, {"RefNumber": "838003", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Industry add-ons integrated into SAP ECC 6.0", "RefUrl": "/notes/838003 "}, {"RefNumber": "700779", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Upgrading SAP ECC 500/600 with PI/PI-A/SLL_PI", "RefUrl": "/notes/700779 "}, {"RefNumber": "1069225", "RefComponent": "BC-CST-DP", "RefTitle": "NO HW ID RECEIVED BY MSSG SERVER", "RefUrl": "/notes/1069225 "}, {"RefNumber": "197886", "RefComponent": "BC-UPG", "RefTitle": "Upgrade: messages in CHK_POSTUP: BDL*, STSL*, BAM*, SQLR*", "RefUrl": "/notes/197886 "}, {"RefNumber": "557314", "RefComponent": "BC-ABA-TO", "RefTitle": "As of Release 610: TVARV replaced with TVARVC", "RefUrl": "/notes/557314 "}, {"RefNumber": "996953", "RefComponent": "BC-DWB-CEX", "RefTitle": "SPAU entry exists for nonexistent CMOD project", "RefUrl": "/notes/996953 "}, {"RefNumber": "716378", "RefComponent": "BC-UPG-PRP", "RefTitle": "Missing libraries during Unicode upgrade", "RefUrl": "/notes/716378 "}, {"RefNumber": "884809", "RefComponent": "BC-DB-MSS", "RefTitle": "SQL Error 11 - General network error", "RefUrl": "/notes/884809 "}, {"RefNumber": "196113", "RefComponent": "BC-SRV-BAL", "RefTitle": "SAPSQL_ARRAY_INSERT_DUPREC, BALHDR", "RefUrl": "/notes/196113 "}, {"RefNumber": "890976", "RefComponent": "FI-AA-AA-E", "RefTitle": "Converting closing report to internal doc number assgnmnt", "RefUrl": "/notes/890976 "}, {"RefNumber": "741280", "RefComponent": "LO-ECH", "RefTitle": "OS60: Updating parameter effectivities", "RefUrl": "/notes/741280 "}, {"RefNumber": "854523", "RefComponent": "IS-ADEC-MPN-MD", "RefTitle": "MPN conversion exit is delivered as 'active' by default", "RefUrl": "/notes/854523 "}, {"RefNumber": "360929", "RefComponent": "PM-WOC-MN", "RefTitle": "Loss of address data on execution of RSXADR05", "RefUrl": "/notes/360929 "}, {"RefNumber": "185315", "RefComponent": "PS-ST-WBS", "RefTitle": "SFAC:Advance inclusion of KIMSK into field select.", "RefUrl": "/notes/185315 "}, {"RefNumber": "626272", "RefComponent": "BC-UPG", "RefTitle": "Termination in the JOB_RSTLANUPG phase", "RefUrl": "/notes/626272 "}, {"RefNumber": "499708", "RefComponent": "BC-UPG", "RefTitle": "Additional information on upgrading to Basis 620 with AIX", "RefUrl": "/notes/499708 "}, {"RefNumber": "526694", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "saposcol based on AIX perfstat library", "RefUrl": "/notes/526694 "}, {"RefNumber": "162980", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "saposcol for 64-bit Solaris", "RefUrl": "/notes/162980 "}, {"RefNumber": "148926", "RefComponent": "BC-OP-FTS", "RefTitle": "saposcol version 32 or 64 bit on ReliantUNIX", "RefUrl": "/notes/148926 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}