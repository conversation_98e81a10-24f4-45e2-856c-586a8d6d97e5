{"Request": {"Number": "865142", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 451, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015926842017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000865142?language=E&token=D8519627FD205090C334008747DE407D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000865142", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000865142/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "865142"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Customizing"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "25.01.2012"}, "SAPComponentKey": {"_label": "Component", "value": "BC-MID-ALE"}, "SAPComponentKeyText": {"_label": "Component", "value": "Integration Technology ALE"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Middleware", "value": "BC-MID", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-MID*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Integration Technology ALE", "value": "BC-MID-ALE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-MID-ALE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "865142 - Customer-specific entries in EDIFCT are deleted"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You used transaction WE57 to maintain entries in the table EDIFCT. These entries may be lost due to an upgrade.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>WE57, EDIFCT, customer entries</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The table EDIFCT was delivered with delivery class 'E' and without a reserved customer namespace. Changing this attribute in the customer system does not have any meaning for the upgrade process because the table EDIFCT is part of the central basis.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Before the upgrade, you must export the customer-defined entries and you must reimport them after the upgrade.<br />You can use transaction WE57 to fill the EDIFCT entries in a transport request.<br /><br />Import the following Support Packages:<br /><br />SAP_BASIS 620 SAPKB62072<br />SAP_BASIS 640 SAPKB64030<br />SAP_BASIS 700 SAPKB70027<br />SAP_BASIS 701 SAPKB70112<br />SAP_BASIS 702 SAPKB70211<br />SAP_BASIS 731 SAPKB73103<br /><br />After you do this, the problem no longer occurs.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D033595)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D033595)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000865142/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000865142/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000865142/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000865142/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000865142/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000865142/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000865142/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000865142/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000865142/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "975861", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 6.0 ABAP", "RefUrl": "/notes/975861"}, {"RefNumber": "961513", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 5.5 SR2 ABAP", "RefUrl": "/notes/961513"}, {"RefNumber": "961511", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/961511"}, {"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410"}, {"RefNumber": "960783", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 7.0 Support Rel. 2 ABAP", "RefUrl": "/notes/960783"}, {"RefNumber": "947991", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/947991"}, {"RefNumber": "939677", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP NetWeaver 7.1 AS ABAP", "RefUrl": "/notes/939677"}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971"}, {"RefNumber": "913849", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/913849"}, {"RefNumber": "913848", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/913848"}, {"RefNumber": "905029", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/905029"}, {"RefNumber": "890202", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/890202"}, {"RefNumber": "826488", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/826488"}, {"RefNumber": "826487", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/826487"}, {"RefNumber": "788218", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/788218"}, {"RefNumber": "775047", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40 SR1", "RefUrl": "/notes/775047"}, {"RefNumber": "737696", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/737696"}, {"RefNumber": "661640", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40", "RefUrl": "/notes/661640"}, {"RefNumber": "637683", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/637683"}, {"RefNumber": "587896", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/587896"}, {"RefNumber": "1293744", "RefComponent": "BC-UPG-RDM", "RefTitle": "Central Note: Upgrade Systems on SAP NetWeaver 7.0 EHP1 SR1", "RefUrl": "/notes/1293744"}, {"RefNumber": "1292070", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP CRM 7.0 SR1 ABAP", "RefUrl": "/notes/1292070"}, {"RefNumber": "1292069", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP 6.0 EHP4 SR1 ABAP", "RefUrl": "/notes/1292069"}, {"RefNumber": "1276895", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. about Upgrading to SAP Solution Manager 7.0 EHP1", "RefUrl": "/notes/1276895"}, {"RefNumber": "1156969", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP CRM 7.0 ABAP", "RefUrl": "/notes/1156969"}, {"RefNumber": "1156968", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to EHP 4 for SAP ERP 6.0 ABAP", "RefUrl": "/notes/1156968"}, {"RefNumber": "1156185", "RefComponent": "BC-UPG-RDM", "RefTitle": "Central Note: Upgrade to Systems on SAP NetWeaver 7.1 EHP 1", "RefUrl": "/notes/1156185"}, {"RefNumber": "1146578", "RefComponent": "BC-UPG-RDM", "RefTitle": "Central Note: Upgrade to Systems on SAP NetWeaver 7.0 EHP1", "RefUrl": "/notes/1146578"}, {"RefNumber": "1108861", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1108861"}, {"RefNumber": "1099841", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 7.0 Support Rel. 3 ABAP", "RefUrl": "/notes/1099841"}, {"RefNumber": "1095506", "RefComponent": "BC-UPG-RDM", "RefTitle": "Upgrade to SAP NetWeaver 7.1 for banking services from SAP", "RefUrl": "/notes/1095506"}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904"}, {"RefNumber": "1071404", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1071404"}, {"RefNumber": "1061649", "RefComponent": "BC-UPG-RDM", "RefTitle": "Upgrade to SAP NetWeaver Process Integration 7.1", "RefUrl": "/notes/1061649"}, {"RefNumber": "1039395", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1039395"}, {"RefNumber": "1019585", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1019585"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904 "}, {"RefNumber": "1095506", "RefComponent": "BC-UPG-RDM", "RefTitle": "Upgrade to SAP NetWeaver 7.1 for banking services from SAP", "RefUrl": "/notes/1095506 "}, {"RefNumber": "1146578", "RefComponent": "BC-UPG-RDM", "RefTitle": "Central Note: Upgrade to Systems on SAP NetWeaver 7.0 EHP1", "RefUrl": "/notes/1146578 "}, {"RefNumber": "1099841", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 7.0 Support Rel. 3 ABAP", "RefUrl": "/notes/1099841 "}, {"RefNumber": "1156968", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to EHP 4 for SAP ERP 6.0 ABAP", "RefUrl": "/notes/1156968 "}, {"RefNumber": "1293744", "RefComponent": "BC-UPG-RDM", "RefTitle": "Central Note: Upgrade Systems on SAP NetWeaver 7.0 EHP1 SR1", "RefUrl": "/notes/1293744 "}, {"RefNumber": "1292069", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP 6.0 EHP4 SR1 ABAP", "RefUrl": "/notes/1292069 "}, {"RefNumber": "1061649", "RefComponent": "BC-UPG-RDM", "RefTitle": "Upgrade to SAP NetWeaver Process Integration 7.1", "RefUrl": "/notes/1061649 "}, {"RefNumber": "1156185", "RefComponent": "BC-UPG-RDM", "RefTitle": "Central Note: Upgrade to Systems on SAP NetWeaver 7.1 EHP 1", "RefUrl": "/notes/1156185 "}, {"RefNumber": "1292070", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP CRM 7.0 SR1 ABAP", "RefUrl": "/notes/1292070 "}, {"RefNumber": "939677", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP NetWeaver 7.1 AS ABAP", "RefUrl": "/notes/939677 "}, {"RefNumber": "1276895", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. about Upgrading to SAP Solution Manager 7.0 EHP1", "RefUrl": "/notes/1276895 "}, {"RefNumber": "960783", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 7.0 Support Rel. 2 ABAP", "RefUrl": "/notes/960783 "}, {"RefNumber": "1156969", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP CRM 7.0 ABAP", "RefUrl": "/notes/1156969 "}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971 "}, {"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410 "}, {"RefNumber": "661640", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40", "RefUrl": "/notes/661640 "}, {"RefNumber": "961513", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 5.5 SR2 ABAP", "RefUrl": "/notes/961513 "}, {"RefNumber": "975861", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 6.0 ABAP", "RefUrl": "/notes/975861 "}, {"RefNumber": "775047", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40 SR1", "RefUrl": "/notes/775047 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "620", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62072", "URL": "/supportpackage/SAPKB62072"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64030", "URL": "/supportpackage/SAPKB64030"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70027", "URL": "/supportpackage/SAPKB70027"}, {"SoftwareComponentVersion": "SAP_BASIS 701", "SupportPackage": "SAPKB70112", "URL": "/supportpackage/SAPKB70112"}, {"SoftwareComponentVersion": "SAP_BASIS 702", "SupportPackage": "SAPKB70211", "URL": "/supportpackage/SAPKB70211"}, {"SoftwareComponentVersion": "SAP_BASIS 731", "SupportPackage": "SAPKB73103", "URL": "/supportpackage/SAPKB73103"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}