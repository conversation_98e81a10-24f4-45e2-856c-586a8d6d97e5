{"Request": {"Number": "444641", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 282, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015387932017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000444641?language=E&token=5C16816414157882295244AF042B5452"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000444641", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000444641/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "444641"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 28}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "13.08.2003"}, "SAPComponentKey": {"_label": "Component", "value": "SCM-APO-INT-SLS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Sales"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Supply Chain Management", "value": "SCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Advanced Planning and Optimization", "value": "SCM-APO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-APO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Interfaces", "value": "SCM-APO-INT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-APO-INT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Sales", "value": "SCM-APO-INT-SLS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-APO-INT-SLS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "444641 - Correction of incorrect sales order requirements with APO"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The /sapapo/sdrqcr21 report corrects incorrect sales order requirements in R/3 and APO.These inconsistencies can occur due to program errors or by deleting queue entries.<br /><br />Use the report if the /sapapo/cif_deltareport3 report cannot correct the inconsistencies.<br /><br />If you are using the backorder processing (BOP), you can use this report to check the consistency of the SD job tables (//posmapn, //ordadm_i, //schedlin, ...).<br />BOP relies on these tables being consistent. The deltareport3 does not include these checks.<br />Functions:<br />1) Order and delivery requirements are first gathered in APO and R/3.When requirements are determined in the R/3 System, a reorganization may be executed in line with the functions of the R/3 report sdrqcr21 (table vbbe).<br />2) The requirements are then compared at field level (MBDAT, open quantity, confirmed quantity, plan quantity, plant, etc.)and the corresponding create/update/delete events sent.<br />3) If the option to test SD order tables is selected, the system checks that these exist and are consistent.<br />4) If the report is executed in test mode, neither the table vbbe in the R/3 System with the selectable result of the reorganization is updated, nor are any events sent. If the report is not executed in test mode, the vbbe table may be updated with the result of the reorganization. In addition, events for creating, updating and deleting between SAP APO and SAP R/3 are sent according to the inconsistencies found.<br />5) Both the output of the result and the processing of the events can be controlled using different parameters.<br />Caution:If this report is not executed in test mode, this must only occur at a time when no change are made to sales and distribution documents in the system.<br />For more details, see the report documentation and the F1 help for the individual entry and selection parameters as soon as the report is available in your APO system.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SDRQCR21, VBBE, note 25444, /SAPAPO/SDRQCR21</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Workaround for missing functions.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The report is delivered in the standard version as of the following releases:<br />&#x00A0;&#x00A0;R/3:&#x00A0;&#x00A0;R/3 plug-in 2002.2<br />&#x00A0;&#x00A0;APO:&#x00A0;&#x00A0;APO 3.0A Support Package 22<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;APO 3.10 Support Package11<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;APO 4.0 (APO Opal)<br />This note can only be implemented in advance using the attached source code corrections:<br />- The APO part of the report is valid for APO 3.0A and APO 3.10.<br />- The R/3 part of the report can, however, be implemented for the R/3 releases 4.6B and 4.6C.<br />- As Attachments for this note there are two transports:<br />444641AP for the import into the APO-system, 444641R3 for the import into the R/3-system.<br />- Please for the import of the Attachments you consider note 13719.<br />- So that the report can be executed without errors, must after that the import of the transports also still the following note must be implemented, provided that it is existing not already about the mentioned release and Support Package:<br />&#x00A0;&#x00A0;|-----------------------------------------------------------|<br />&#x00A0;&#x00A0;| Note&#x00A0;&#x00A0;&#x00A0;&#x00A0;||&#x00A0;&#x00A0;&#x00A0;&#x00A0;R/3&#x00A0;&#x00A0;&#x00A0;&#x00A0;| PI 2002.1 | PI 2001.2 |&#x00A0;&#x00A0;&#x00A0;&#x00A0;APO&#x00A0;&#x00A0;&#x00A0;&#x00A0;|<br />&#x00A0;&#x00A0;| No.:&#x00A0;&#x00A0;&#x00A0;&#x00A0; || 4.6B| 4.6C| 4.6B| 4.6C| 4.6B| 4.6C| 3.0A| 3.10|<br />&#x00A0;&#x00A0;|----------||-----------------------------------------------|<br />&#x00A0;&#x00A0;| 522237&#x00A0;&#x00A0;|| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| F31 | F41 | --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| 21&#x00A0;&#x00A0;| 07&#x00A0;&#x00A0;|<br />&#x00A0;&#x00A0;|-----------------------------------------------------------|<br /><br /><br />Note the following for the report functions:<br />- The report does not change any data directly in APO, instead it sends corresponding events (create/update/delete) from R/3 to APO for correction. To ensure that these events can be created, sent and processed correctly and, consequently, the inconsistencies can be eliminated also, the following notes must be implemented.(The Support Package, as of which the correction is contained in the standard system, is specified in the table in each case, or an 'na' is indicated if the correction is not available to date in a Support Package and a '--' means the correction is not relevant.)<br />&#x00A0;&#x00A0;|-----------------------------------------------------------|<br />&#x00A0;&#x00A0;| Note&#x00A0;&#x00A0;&#x00A0;&#x00A0;||&#x00A0;&#x00A0;&#x00A0;&#x00A0;R/3&#x00A0;&#x00A0;&#x00A0;&#x00A0;| PI 2002.1 | PI 2001.2 |&#x00A0;&#x00A0;&#x00A0;&#x00A0;APO&#x00A0;&#x00A0;&#x00A0;&#x00A0;|<br />&#x00A0;&#x00A0;| No.:&#x00A0;&#x00A0;&#x00A0;&#x00A0; || 4.6B| 4.6C| 4.6B| 4.6C| 4.6B| 4.6C| 3.0A| 3.10|<br />&#x00A0;&#x00A0;|----------||-----------------------------------------------|<br />&#x00A0;&#x00A0;| 541129&#x00A0;&#x00A0;|| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| 09&#x00A0;&#x00A0;|<br />&#x00A0;&#x00A0;| 537518&#x00A0;&#x00A0;|| 44&#x00A0;&#x00A0;| 35&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;|<br />&#x00A0;&#x00A0;| 533835&#x00A0;&#x00A0;|| 44&#x00A0;&#x00A0;| 35&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;|<br />&#x00A0;&#x00A0;| 530320&#x00A0;&#x00A0;|| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| 21&#x00A0;&#x00A0;| 08&#x00A0;&#x00A0;|<br />&#x00A0;&#x00A0;| 524376&#x00A0;&#x00A0;|| 43&#x00A0;&#x00A0;| 34&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| 21&#x00A0;&#x00A0;| 07&#x00A0;&#x00A0;|<br />&#x00A0;&#x00A0;| 511608&#x00A0;&#x00A0;|| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| 21&#x00A0;&#x00A0;| 06&#x00A0;&#x00A0;|<br />&#x00A0;&#x00A0;| 505961&#x00A0;&#x00A0;|| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| 20&#x00A0;&#x00A0;| 05&#x00A0;&#x00A0;|<br />&#x00A0;&#x00A0;| 503745&#x00A0;&#x00A0;|| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| 20&#x00A0;&#x00A0;| 05&#x00A0;&#x00A0;|<br />&#x00A0;&#x00A0;| 486671&#x00A0;&#x00A0;|| 38&#x00A0;&#x00A0;| 29&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| 20&#x00A0;&#x00A0;| 03&#x00A0;&#x00A0;|<br />&#x00A0;&#x00A0;| 454358&#x00A0;&#x00A0;|| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| 19&#x00A0;&#x00A0;| 03&#x00A0;&#x00A0;|<br />&#x00A0;&#x00A0;| 451834&#x00A0;&#x00A0;|| 36&#x00A0;&#x00A0;| 28&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;|<br />&#x00A0;&#x00A0;| 441932&#x00A0;&#x00A0;|| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| --&#x00A0;&#x00A0;| 18&#x00A0;&#x00A0;| 03&#x00A0;&#x00A0;|<br />&#x00A0;&#x00A0;|-----------------------------------------------------------|<br />- For performance reasons, you need to check the consistency of SD job tables and to clean up entries for closed requirements.Use the /sapapo/sdorder_del APO report for this.However, here you must first implement the most current version with all notes from composite SAP note 553476 because this was finally revised completely.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SD-BF-AC (Availability Check)"}, {"Key": "Other Components", "Value": "SCM-APO-ATP (Global Available-to-Promise)"}, {"Key": "Transaction codes", "Value": "CLEAR"}, {"Key": "Transaction codes", "Value": "HIER"}, {"Key": "Transaction codes", "Value": "TIME"}, {"Key": "Transaction codes", "Value": "MODE"}, {"Key": "Transaction codes", "Value": "COLOR"}, {"Key": "Transaction codes", "Value": "BP"}, {"Key": "Transaction codes", "Value": "SM31"}, {"Key": "Responsible                                                                                         ", "Value": "I800242"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D021241)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000444641/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000444641/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000444641/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000444641/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000444641/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000444641/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000444641/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000444641/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000444641/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "444641R3.ZIP", "FileSize": "26", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700010652262001&iv_version=0028&iv_guid=36C3B2B81105834395FA039464B0E686"}, {"FileName": "444641AP.ZIP", "FileSize": "66", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700010652262001&iv_version=0028&iv_guid=B69A833AC0B02C4CB86B87B0F4A2973F"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "832888", "RefComponent": "SCM-APO-INT-SLS", "RefTitle": "Incorrect or unpostable delivery requirements in APO/SCM", "RefUrl": "/notes/832888"}, {"RefNumber": "607742", "RefComponent": "SCM-APO-INT-SLS", "RefTitle": "Composite SAP note: Report /sapapo/sdrqcr21", "RefUrl": "/notes/607742"}, {"RefNumber": "588192", "RefComponent": "SCM-APO-INT-SLS", "RefTitle": "/sapapo/sdrqcr21: Inconsist. with partially delivered item", "RefUrl": "/notes/588192"}, {"RefNumber": "575958", "RefComponent": "SCM-TEC", "RefTitle": "Composite SAP note: Avoiding data inconsistencies in APO", "RefUrl": "/notes/575958"}, {"RefNumber": "565289", "RefComponent": "SCM-APO-INT-SLS", "RefTitle": "Improved performance of /sapapo/sdrqcr21 report", "RefUrl": "/notes/565289"}, {"RefNumber": "562045", "RefComponent": "SCM-APO-INT-SLS", "RefTitle": "/sapapo/sdrqcr21: Supplement for note 444641 and R/3 4.5B", "RefUrl": "/notes/562045"}, {"RefNumber": "553476", "RefComponent": "SCM-APO-INT-SLS", "RefTitle": "Composite SAP note: Report /sapapo/sdorder_del", "RefUrl": "/notes/553476"}, {"RefNumber": "541129", "RefComponent": "SCM-APO-INT-SLS-IB", "RefTitle": "Allowing deletion of VMI order without product and location", "RefUrl": "/notes/541129"}, {"RefNumber": "537518", "RefComponent": "SCM-APO-INT-SLS", "RefTitle": "Check instructions for RBA main items not transf. into APO", "RefUrl": "/notes/537518"}, {"RefNumber": "533835", "RefComponent": "SCM-APO-INT-SLS", "RefTitle": "R/3 line setup information lost in APO tables", "RefUrl": "/notes/533835"}, {"RefNumber": "530320", "RefComponent": "SCM-APO-INT-SLS", "RefTitle": "Material change in sales order not transferred to APO", "RefUrl": "/notes/530320"}, {"RefNumber": "524376", "RefComponent": "SCM-APO-INT-SLS", "RefTitle": "Incorrect BOP results after manual requested quantity change", "RefUrl": "/notes/524376"}, {"RefNumber": "522237", "RefComponent": "SCM-APO-INT-SLS", "RefTitle": "Missing object reference after initial data transfer", "RefUrl": "/notes/522237"}, {"RefNumber": "511608", "RefComponent": "SCM-APO-INT-SLS-IB", "RefTitle": "Duplicate records in the field catalog", "RefUrl": "/notes/511608"}, {"RefNumber": "510912", "RefComponent": "SCM-APO-ATP-BOP", "RefTitle": "BOP: Tips & Tricks", "RefUrl": "/notes/510912"}, {"RefNumber": "505961", "RefComponent": "SCM-APO-INT-SLS", "RefTitle": "Field catalog transfer to APO: Higher-level item missing", "RefUrl": "/notes/505961"}, {"RefNumber": "503745", "RefComponent": "SCM-APO-INT-SLS", "RefTitle": "Main items have duplicate request schedule lines", "RefUrl": "/notes/503745"}, {"RefNumber": "486671", "RefComponent": "SCM-APO-INT-SLS", "RefTitle": "Determining the ATP category in the APO inbound processing", "RefUrl": "/notes/486671"}, {"RefNumber": "454358", "RefComponent": "SCM-APO-INT-SLS-IB", "RefTitle": "ATPCAT initial in table /SAPAPO/ORDADM_I", "RefUrl": "/notes/454358"}, {"RefNumber": "451834", "RefComponent": "SCM-APO-INT-SLS", "RefTitle": "ATP category is not updated correctly in APO", "RefUrl": "/notes/451834"}, {"RefNumber": "441932", "RefComponent": "SCM-APO-INT-SLS", "RefTitle": "Updating: Mapping for order incorrect", "RefUrl": "/notes/441932"}, {"RefNumber": "25444", "RefComponent": "SD-BF-AC", "RefTitle": "SDRQCR21: Recovery of sales and delivery requirements", "RefUrl": "/notes/25444"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "607742", "RefComponent": "SCM-APO-INT-SLS", "RefTitle": "Composite SAP note: Report /sapapo/sdrqcr21", "RefUrl": "/notes/607742 "}, {"RefNumber": "553476", "RefComponent": "SCM-APO-INT-SLS", "RefTitle": "Composite SAP note: Report /sapapo/sdorder_del", "RefUrl": "/notes/553476 "}, {"RefNumber": "454358", "RefComponent": "SCM-APO-INT-SLS-IB", "RefTitle": "ATPCAT initial in table /SAPAPO/ORDADM_I", "RefUrl": "/notes/454358 "}, {"RefNumber": "441932", "RefComponent": "SCM-APO-INT-SLS", "RefTitle": "Updating: Mapping for order incorrect", "RefUrl": "/notes/441932 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "25444", "RefComponent": "SD-BF-AC", "RefTitle": "SDRQCR21: Recovery of sales and delivery requirements", "RefUrl": "/notes/25444 "}, {"RefNumber": "832888", "RefComponent": "SCM-APO-INT-SLS", "RefTitle": "Incorrect or unpostable delivery requirements in APO/SCM", "RefUrl": "/notes/832888 "}, {"RefNumber": "575958", "RefComponent": "SCM-TEC", "RefTitle": "Composite SAP note: Avoiding data inconsistencies in APO", "RefUrl": "/notes/575958 "}, {"RefNumber": "510912", "RefComponent": "SCM-APO-ATP-BOP", "RefTitle": "BOP: Tips & Tricks", "RefUrl": "/notes/510912 "}, {"RefNumber": "565289", "RefComponent": "SCM-APO-INT-SLS", "RefTitle": "Improved performance of /sapapo/sdrqcr21 report", "RefUrl": "/notes/565289 "}, {"RefNumber": "588192", "RefComponent": "SCM-APO-INT-SLS", "RefTitle": "/sapapo/sdrqcr21: Inconsist. with partially delivered item", "RefUrl": "/notes/588192 "}, {"RefNumber": "562045", "RefComponent": "SCM-APO-INT-SLS", "RefTitle": "/sapapo/sdrqcr21: Supplement for note 444641 and R/3 4.5B", "RefUrl": "/notes/562045 "}, {"RefNumber": "511608", "RefComponent": "SCM-APO-INT-SLS-IB", "RefTitle": "Duplicate records in the field catalog", "RefUrl": "/notes/511608 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APO", "From": "30A", "To": "30A", "Subsequent": ""}, {"SoftwareComponent": "SAP_APO", "From": "310", "To": "310", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APO 30A", "SupportPackage": "SAPKY30A22", "URL": "/supportpackage/SAPKY30A22"}, {"SoftwareComponentVersion": "SAP_APO 310", "SupportPackage": "SAPKY31011", "URL": "/supportpackage/SAPKY31011"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}