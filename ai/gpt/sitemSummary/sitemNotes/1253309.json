{"Request": {"Number": "1253309", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 339, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000007327162017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001253309?language=E&token=D56E8EEF60005F19090978C722A789F5"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001253309", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001253309/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1253309"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.04.2009"}, "SAPComponentKey": {"_label": "Component", "value": "PPM-PFM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Portfolio Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Portfolio and Project Management", "value": "PPM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PPM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Portfolio Management", "value": "PPM-PFM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PPM-PFM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1253309 - Changes to tables ACO_USER and ACO_ROLE after RPM XPRA"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p><br />You upgrade to CPRXRPM 450. After you do this, the authorizations do not refer to the corresponding groups. The entries in the table ACO_USER have an initial value in the field OBJECT_ID.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p><br />SAP xApp Resource and Portfolio Management, SAP xRPM, RPM, XPRA, XPRAS, ACO_USER, ACO_ROLE, object_id, cFolder, cProject, CPROJECTS, /RPM/XPRA_450_ITEM_HEADER<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><br />This problem is caused by a program error.<br /><br />This error affects only customers who do not use SAP xApp Resource and Portfolio Management (the table /RPM/ITEM_H does not contain any entries or does not exist before the upgrade).<br /><br />Due to the error, the RPM XPRA program overwrites the tables ACO_USER and ACO_RULE during the upgrade.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br />Implement the corrections.<br /></p> <OL>1. Note that these corrections are provided in Support Package SAPK-45006INCPRXRPM. If, during the upgrade to CPRXRPM 450 in the BIND_PATCH phase of the prepare, you import Support Package SAPK-45006INCPRXRPM, the corrections attached to this note are implemented before the XPRA phase. This ensures that the tables ACO_USER and ACO_ROLE are processed correctly during the RPM XPRA run. Step 2 below no longer applies.<br /></OL> <OL>2. For customers who did not use an RPM (CPRXRPM) before the upgrade (for example, in source release CPROJECTS 310) and who do not want to import the Support Package SAPK-45006INCPRXRPM at the same time:</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You must stop the upgrade before the XPRAS_UPG phase. Note 48184 describes how to do this. Then, use the report RSXPRAUP to deallocate the XPRA with the name /RPM/XPRA_450_ITEM_HEADER as described in detail in Note 122597. The transport request that is relevant for this XPRA on the selection screen of the report RSXPRAUP is SAPK-450GGINCPRXRPM. The indicator must be set to \"N\". After the upgrade, you must no longer execute the XPRA /RPM/XPRA_450_ITEM_HEADER.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For the action, use the user DDIC. All other users are still locked at the time of XPRAS_UPG and the report RSXPRAUP requires you to use the user DDIC for the execution.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "PLM-CFO (Collaboration Folders)"}, {"Key": "Other Components", "Value": "CA-DMS (Document management)"}, {"Key": "Other Components", "Value": "CRM-MKT-MPL (Marketing Planner)"}, {"Key": "Other Components", "Value": "PPM-PRO (Project Management)"}, {"Key": "Other Components", "Value": "XX-PROJ-IMS-UPGR (Application Specific Upgrade Tools)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D037966)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D035275)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001253309/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001253309/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001253309/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001253309/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001253309/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001253309/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001253309/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001253309/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001253309/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "961512", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/961512"}, {"RefNumber": "961511", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/961511"}, {"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410"}, {"RefNumber": "960783", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 7.0 Support Rel. 2 ABAP", "RefUrl": "/notes/960783"}, {"RefNumber": "947991", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/947991"}, {"RefNumber": "925240", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/925240"}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971"}, {"RefNumber": "913849", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/913849"}, {"RefNumber": "905029", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/905029"}, {"RefNumber": "826488", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/826488"}, {"RefNumber": "826093", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/826093"}, {"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092"}, {"RefNumber": "1279993", "RefComponent": "PPM-PFM", "RefTitle": "Tables ACO_USER & ACO_ROLE are inconsistent after RPM XPRA", "RefUrl": "/notes/1279993"}, {"RefNumber": "122597", "RefComponent": "BC-UPG", "RefTitle": "Ignoring errors in the phase XPRAS_...", "RefUrl": "/notes/122597"}, {"RefNumber": "1108861", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1108861"}, {"RefNumber": "1108700", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1108700"}, {"RefNumber": "1103101", "RefComponent": "PPM-PFM", "RefTitle": "Manual Migration. xRPM2.0->xRPM4.5. (xRPM4.5 SP1 customers)", "RefUrl": "/notes/1103101"}, {"RefNumber": "1099841", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 7.0 Support Rel. 3 ABAP", "RefUrl": "/notes/1099841"}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904"}, {"RefNumber": "1075658", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Exchange upgrade to Basis 700 with CPRXRPM 450_700", "RefUrl": "/notes/1075658"}, {"RefNumber": "1071404", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1071404"}, {"RefNumber": "1039395", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1039395"}, {"RefNumber": "1010762", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1010762"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092 "}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904 "}, {"RefNumber": "1075658", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Exchange upgrade to Basis 700 with CPRXRPM 450_700", "RefUrl": "/notes/1075658 "}, {"RefNumber": "1099841", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 7.0 Support Rel. 3 ABAP", "RefUrl": "/notes/1099841 "}, {"RefNumber": "122597", "RefComponent": "BC-UPG", "RefTitle": "Ignoring errors in the phase XPRAS_...", "RefUrl": "/notes/122597 "}, {"RefNumber": "960783", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 7.0 Support Rel. 2 ABAP", "RefUrl": "/notes/960783 "}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971 "}, {"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410 "}, {"RefNumber": "1279993", "RefComponent": "PPM-PFM", "RefTitle": "Tables ACO_USER & ACO_ROLE are inconsistent after RPM XPRA", "RefUrl": "/notes/1279993 "}, {"RefNumber": "1103101", "RefComponent": "PPM-PFM", "RefTitle": "Manual Migration. xRPM2.0->xRPM4.5. (xRPM4.5 SP1 customers)", "RefUrl": "/notes/1103101 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "CPROJECTS", "From": "310_620", "To": "310_640", "Subsequent": ""}, {"SoftwareComponent": "CPRXRPM", "From": "400", "To": "400", "Subsequent": ""}, {"SoftwareComponent": "CPRXRPM", "From": "450_700", "To": "450_700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "CPRXRPM 450_700", "SupportPackage": "SAPK-45006INCPRXRPM", "URL": "/supportpackage/SAPK-45006INCPRXRPM"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "CPRXRPM", "NumberOfCorrin": 1, "URL": "/corrins/0001253309/381"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}