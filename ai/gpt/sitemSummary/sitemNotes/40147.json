{"Request": {"Number": "40147", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1112, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014410712017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000040147?language=E&token=8F8AB696618B3B58854E4F048A711895"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000040147", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000040147/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "40147"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 77}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.03.2002"}, "SAPComponentKey": {"_label": "Component", "value": "MM-PUR-VM-SET"}, "SAPComponentKeyText": {"_label": "Component", "value": "Subsequent Settlement"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Purchasing", "value": "MM-PUR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Vendor-Material Relationships and Conditions", "value": "MM-PUR-VM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Subsequent Settlement", "value": "MM-PUR-VM-SET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM-SET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "40147 - Collective note: Subsequent settlement (Purch.) Release 3.0"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>-- Collective note: Problems and errors with&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;--<br />--&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;subsequent settlement (Purchasing)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; --<br />--&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0A through 3.1I&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;--<br />--&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Version: December 04, 2001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;--</p> <b>***********************************************************************</b><br /> <b>* European Monetary Union (important - Release 3.1I only):&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 439493 Incorrect document currency with billing via billing&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;interface (incorrect posting in Financial Accounting,&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;currency exchanged)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 452365 Rebate arrangement currency not copied to condition records&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(undesired currency in condition record)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 437429 Message SG105 List settlement documents, recompiling incomes *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(incomes in Financial Accounting and/or S074 incorrect)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 400432 Expiring currencies, rebate arrangement maintenance.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(puchasing, sales)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 398739 Incorrect conversion of EMU currencies - Part 2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>***********************************************************************</b><br /> <b>* Notes you must implement beforehand since it might be complicated&#x00A0;&#x00A0; *</b><br /> <b>* to correct the consequences:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 422649 Condition records were deleted in error&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 398739 Incorrect conversion of EMU currencies - Part 2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This note must be implemented (even by customers not&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;affected by the currency changeover)!&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* In Release 3.0, you must implement Note&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 303047 RMCENEUA - Error in selection by info structures&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>* Otherwise, the wrong incomes might be settled!&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* In Release 3.0, you must implement Note&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 175341 Subsequent Settlement: Multiple income updates when changing *</b><br /> <b>* customer billing document. Otherwise, the wrong incomes might be&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* settled!&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 216306 Subsequent settlement: Missing income updates of customer&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;billing document&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* Release 3.1I, occurs between Support Package SAPKH31I43 and&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>* SAPKH31I50 (inclusive) or after implementing Note 193221.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 180434 Subsequent settlement: Validation of relevant documents&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;before archiving&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 179864 PURCHIS - commitments - problems during IR or GR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>* Releases 3.0C through 3.1I&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 151398 PURCHIS - double update for delivery costs from several POs&#x00A0;&#x00A0;*</b><br /> <b>* Releases 3.0 through 3.1I&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 120610 Subsequent settlement: Data loss compilation of vendor&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;business volume data for short dump&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>* Releases 3.0F through 3.1I&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 206060 Indicator 'Subsequent settlement', different vendor data&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>* See Note 214146&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* Release 3.0F through 3.1I&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>***********************************************************************</b><br /> <p><br />This note refers to all Releases 3.0A to 3.1I including the industry-specific solution IS Retail. For Release 4.0B, refer to Note 104668, for Release 4.5B, refer to Note 152725, and for Release 4.6, refer to Note 183379.<br /></p> <b>General Information</b><br /> <p>In part 1, this note is intended to point out the most frequent problems and errors related to subsequent settlement (purchasing). It is meant to help analyze and solve frequent problems.<br />In part 2, there is a list of all notes that relate to known problems with subsequent settlement.</p> <b>Handling</b><br /> <p>If you have problems with subsequent settlement (purchasing), you can try to solve the problem by referring to this note. Check whether one of the notes from part 1 refers to your problem. Choose the appropriate note and carry out the checks specified in the note. If necessary see references to notes in part 2 to correct the errors.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Subsequent settlement, volume-based rebate, collective note.<br />Transactions MEB1, MEB2, MEB3, MEB4, MEB6, MEB8, MEB9, MCE+, MEBA<br />Programs SAPMV13A, SAPMNB01, RWMBON01, RWMBON03, RWMBON04, RWMBON06, RWMBON08, RMCE0900<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><OL>1. Notes on problem solution/consulting</OL> <p><br />167284 Subsequent settlement. General notes (FAQs and Customizing)<br /><br />75655&#x00A0;&#x00A0;Error message VK358: PurchOrg &amp; with CoCode &amp; is different<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; from CoCode &amp; in the agreement.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Underlying problem:</p> <UL><UL><LI>You are using several company codes, so your purchasing organization is not assigned to one single company code (see Note 153694).</LI></UL></UL> <p><br />153694 Subsequent settlement: Settlement type credit-side or debit-side<br /></p> <UL><UL><LI>Note on the relevance of the settlement type</LI></UL></UL> <p><br />72199 Subsequent settlement: Problem analysis when updating vendor<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;business volume<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Frequent symptoms:</p> <UL><UL><LI>Vendor business volumes for a document are not updated.</LI></UL></UL> <UL><UL><LI>Vendor business volumes for a document are updated, however the scale basis or condition basis has value 0.</LI></UL></UL> <p><br />77258 Subsequent settlement: Required fields settlement document<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Frequent symptoms:</p> <UL><UL><LI>When settling an agreement, the following error messages are generated:</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;00055 \"Required entry not made\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;00344 \"Batch input data for screen SAPMM08R xxxx does not exist\"<br /><br />113031 Subsequent settlement: Taxes<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Notes:</p> <UL><UL><LI>Tax treatment (calculation) when processing subsequent settlement</LI></UL></UL> <p><br />80233 Debit-side settlement: Customizing, error messages<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Frequent symptoms:</p> <UL><UL><LI>Error messages in message class VF</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Notes:</p> <UL><UL><LI>Procedure during SD Customizing in terms of subsequent settlement</LI></UL></UL> <p><br />73214 Subsequent settlement: Subsequent compilation of vendor business<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;volume<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Frequent symptoms:</p> <UL><UL><LI>Nothing is updated during the subsequent compilation of vendor business volumes.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Notes:</p> <UL><UL><LI>Procedure when using the function</LI></UL></UL> <UL><UL><LI>Functional restrictions</LI></UL></UL> <p><br />381831 Consignment processing and subsequent settlement<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Frequent symptoms:</p> <UL><UL><LI>No update of business volume for settlement documents in consignment processing</LI></UL></UL> <p><br />333914 Subsequent settlement: Performance (composite note)<br /></p> <OL>2. Notes on known problems</OL> <p><br />Refer to Composite Note 67481 for debit-side settlement which groups all problems in connection with the general billing interface.<br />Refer to the following notes in relation to subsequent settlement (volume-based rebate) in purchasing.<br />These notes are arranged by the release up to which they are valid. For example, for Release 3.0F all notes from sections 3.0F, 3.1G and higher should be referred to, as far as they apply to Release 3.0F.<br /><br />Valid until further notice:<br /><br />74020 Updating and posting provisions for accrued income<br /><br />60174 Arrangement and settlement calendar AJ, AM (incorrect special<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;rules)<br /><br />Valid up to and including Release 3.1I:<br /><br />118080 Subsequent settlement: Message MN319 during detailed statement<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0F through 3.1I<br /><br />117988 Subsequent settlement: messages MN514 and MN515<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0F through 3.1I<br /><br />111366 Subsequent settlement: Error messages MN164 and MN310<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.00 and 3.1I<br /><br />113315 VK894: Screen generation error for rebate<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0D and 3.1I<br /><br />107699 Subsequent settlement: Error MN305 during extension<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Release 3.1I<br /><br />105687 ME41: Volume rebate indicator from vendor master<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.00 and 3.1I<br /><br />103695 Inconsistent deletion of period-specific conditions<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0F through 3.1I<br /><br />100695 Subsequent settlement: No update during cancellation<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.00 through 3.1I<br /><br />Valid up to and including Release 3.1H:<br /><br />97483 Subsquent settlement: Too many items in settlement document<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0F through 3.1H<br /><br />96375 Subsequent settlement: Error vendor business volumes for<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;subsequent debit/credit<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.00 through 3.1H<br /><br />96224 Subsequent settlement: recompilation, Message MN380<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.1G through 3.1H<br /><br />96131 Subsequent settlement: default account assigments not copied<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.00 through 3.1H<br /><br />94381 Incorrect currency translation for detailed statement<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0C through 3.1H<br /><br />90661 Subsequent settlement: Delete purchase order item<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0F through 3.1H<br /><br />89671 Incorrect update incomes for debt-side settlement account.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0F through 3.1H<br /><br />89027 Update termination after settlement document cancellation<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0F through 3.1H<br /><br />85831 Subsequent settlement: Incorrect price determination<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0F through 3.1H<br /><br />84256 Extension copies deleted condition records<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0D through 3.1H<br /><br />78695 Termination when checking settlement material<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0A through 3.1H<br /><br />76917 Condition info: Syntax error in program RV13A<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Release 3.1H<br /><br />76713 Subsequent settlement: Foreign payments<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0A through 3.1H<br /><br />66054 Subsequent settlement - no update<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.00 through 3.0F<br /><br />Valid up to and including Release 3.1G:<br /><br />77509 Subsequent settlement: Error FF718 in business volume comparison<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;and agreement<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0F through 3.1G<br /><br />75100 SD-FI: Transfer of incorrect tax indicators<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(72173 Incorrect tax code for several taxes)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0D through 3.1G<br /><br />Caution:</p> <UL><LI>Only implement the source code from Note 75100!</LI></UL> <UL><LI>Only for debit-side settlement type!</LI></UL> <p><br />73183 Subsequent settlement - meaning of error message MN042<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0A through 3.1G<br /><br />69800 Subsequent settlement: Error message - FF758<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0A through 3.1G<br /><br />Valid up to and including Release 3.0F:<br /><br />62882 LIS - Subsequent settlement - Field catalog EKOK<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0A through 3.0F<br /><br />Valid up to and including Release 3.0E:<br /><br />67669 Subsequent settlement: Expand field catalog<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0A through 3.0E<br /><br />62426 Accounting document contains incorrect incomes<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0A through 3.0E<br /><br />60397 View cluster: Problem maintaining a sub-level<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(create or change access sequence)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0D through 3.0E<br /><br />49528 Subsequent settlement itemization error MN I 355<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0C through 3.0E<br /><br />49067 Conversion problem when updating vendor business volume<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0C through 3.0E<br /><br />48968 Incorrect update during credit memo cancellation<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0C through 3.0E<br /><br />48497 Subsequent settlement: Item text, allocation number<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0A through 3.0E<br /><br />48245 No provisions posting for accrued income goods receipt<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0A through 3.0E<br /><br />48078 Subsequent settlement and tax jurisdiction code<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0C through 3.0E<br /><br />48063 Subsequent settlement: Error message FF758<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0A through 3.1G<br /><br />47987 Subsequent statistics structure, incorrect data<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0D and 3.0E<br /><br />47199 Double update for subsequent statistics structure<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0D and 3.0E<br /><br />Valid up to and including Release 3.0D:<br /><br />49575 Subsequent Settlement itemization error MN I 355<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Release 3.0D<br /><br />48679 Error setting reference fields in Financial Accounting (RW012)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0C through 3.0D<br /><br />46612 Incorrect itemization arrangements purchasing<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0C through 3.0D<br /><br />46128 Incorrect itemization arrangements purchasing<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0C through 3.0D<br /><br />45057 Error when creating with reference to arrangement in purchasing<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0A through 3.0D<br /><br />41380 Error when checking organization data for arrangement<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Release 3.0D<br /><br />41353 No purchasing group when creating arrangement<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0A through 3.0D<br /><br />41086 Arrangements in purchasing cannot be saved<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0A through 3.0D<br /><br />41072 Subsequent statistics structure arrangements purchasing<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Release 3.0D<br /><br /><br />Valid up to and including Release 3.0C:<br /><br />45579 Vendor business volume update incomplete<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Release 3.0C<br /><br />39574 Income update<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Release 3.0C<br /><br />39557 Document balance credit memo<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.00 through 3.0C<br /><br />39327 Vendor not relevant to volume rebate<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0A through 3.0C<br /><br />39172 Incorrect payment method check in maintenance arrangements<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0A through 3.0C<br /><br />34584 Error when creating purchasing organization arrangement without<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;company code<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 3.0A through 3.0C<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023678)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D023678)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000040147/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000040147/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000040147/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000040147/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000040147/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000040147/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000040147/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000040147/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000040147/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "98843", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseq.settlement:00015(value of field is negative)", "RefUrl": "/notes/98843"}, {"RefNumber": "98586", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Analysis reports, repair reports, Release 3.0", "RefUrl": "/notes/98586"}, {"RefNumber": "97483", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsq.settlement:too many items in settlement dcmnt", "RefUrl": "/notes/97483"}, {"RefNumber": "97157", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Tax code with settlement on debit-side (Release 3.0F)", "RefUrl": "/notes/97157"}, {"RefNumber": "96375", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error w/ vendor business volume from subsequent debit/credit", "RefUrl": "/notes/96375"}, {"RefNumber": "96224", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Business volume data recompilation, message MN380", "RefUrl": "/notes/96224"}, {"RefNumber": "96131", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseq.settlement:default accnt assgmnts not copied", "RefUrl": "/notes/96131"}, {"RefNumber": "95760", "RefComponent": "MM-SRV-SR", "RefTitle": "Subsqent settlmnt:Update entry of services performd", "RefUrl": "/notes/95760"}, {"RefNumber": "94381", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect currency translation f.detailed statement", "RefUrl": "/notes/94381"}, {"RefNumber": "92661", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Unjustified error message MN515", "RefUrl": "/notes/92661"}, {"RefNumber": "90661", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: delete purchase order item", "RefUrl": "/notes/90661"}, {"RefNumber": "89671", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorr.update incomes f. debt-side sttlmnt account.", "RefUrl": "/notes/89671"}, {"RefNumber": "89057", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Importance of error message MN428", "RefUrl": "/notes/89057"}, {"RefNumber": "85831", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect price determination", "RefUrl": "/notes/85831"}, {"RefNumber": "84256", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Extension copies deleted condition records", "RefUrl": "/notes/84256"}, {"RefNumber": "83771", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Outline agreement:not subj. to volume-based rebate", "RefUrl": "/notes/83771"}, {"RefNumber": "81603", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subs.settlement: Maintenance of settlement groups", "RefUrl": "/notes/81603"}, {"RefNumber": "80233", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Debit-side settlement: Customizing, error messages", "RefUrl": "/notes/80233"}, {"RefNumber": "78695", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Termination when checking settlement material", "RefUrl": "/notes/78695"}, {"RefNumber": "77509", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error FF718 in business volume comparison and agreement", "RefUrl": "/notes/77509"}, {"RefNumber": "77258", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Required fields settlement document", "RefUrl": "/notes/77258"}, {"RefNumber": "76917", "RefComponent": "SD-MD-CM", "RefTitle": "Condition info: Syntax error in program RV13A...", "RefUrl": "/notes/76917"}, {"RefNumber": "76713", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: Foreign payments", "RefUrl": "/notes/76713"}, {"RefNumber": "75655", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error message VK358: Purchasing organization ....", "RefUrl": "/notes/75655"}, {"RefNumber": "74020", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Updating and posting provisions for accrued income", "RefUrl": "/notes/74020"}, {"RefNumber": "73214", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseq.settl.:Retrospec.compltn/recompltn of busin.vol.data", "RefUrl": "/notes/73214"}, {"RefNumber": "73183", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Importance of error message MN042", "RefUrl": "/notes/73183"}, {"RefNumber": "72199", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Problem analysis update business volume data", "RefUrl": "/notes/72199"}, {"RefNumber": "69800", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: Error message - FF758", "RefUrl": "/notes/69800"}, {"RefNumber": "67669", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Extending field catalog for subsequent settlement", "RefUrl": "/notes/67669"}, {"RefNumber": "66054", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Information structures for subsequent settlement not updated", "RefUrl": "/notes/66054"}, {"RefNumber": "62882", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "LIS - Subsequent settlement - Field catalog EKOK", "RefUrl": "/notes/62882"}, {"RefNumber": "62426", "RefComponent": "MM-PUR-VM", "RefTitle": "Accounting document contains incorrect income", "RefUrl": "/notes/62426"}, {"RefNumber": "60174", "RefComponent": "BC-SRV-ASF-CAL", "RefTitle": "Factory calendar AM (incorrect special rules)", "RefUrl": "/notes/60174"}, {"RefNumber": "503040", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlement by credit memo: message MN227", "RefUrl": "/notes/503040"}, {"RefNumber": "49575", "RefComponent": "MM-PUR-VM", "RefTitle": "Subsequent settlement itemization error MN I 355", "RefUrl": "/notes/49575"}, {"RefNumber": "49528", "RefComponent": "MM-PUR-VM", "RefTitle": "Subsequent settlement itemization error MN I 355", "RefUrl": "/notes/49528"}, {"RefNumber": "49067", "RefComponent": "MM-PUR-VM", "RefTitle": "Update Vol. business done with vendor Conversn prob", "RefUrl": "/notes/49067"}, {"RefNumber": "48968", "RefComponent": "MM-PUR-VM", "RefTitle": "Incorrect update during credit memo cancellation", "RefUrl": "/notes/48968"}, {"RefNumber": "48679", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error setting reference fields in FI (RW012)", "RefUrl": "/notes/48679"}, {"RefNumber": "48497", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Item text, allocation no.", "RefUrl": "/notes/48497"}, {"RefNumber": "48245", "RefComponent": "MM-PUR-VM", "RefTitle": "No provisions posting for accrued income goods rcpt", "RefUrl": "/notes/48245"}, {"RefNumber": "48078", "RefComponent": "MM-PUR-VM", "RefTitle": "Subsequent settlement and tax jurisdiction code", "RefUrl": "/notes/48078"}, {"RefNumber": "48063", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: Error message FF758", "RefUrl": "/notes/48063"}, {"RefNumber": "47987", "RefComponent": "MM-PUR-VM", "RefTitle": "Subsequent statistics structure, incorrect data", "RefUrl": "/notes/47987"}, {"RefNumber": "47199", "RefComponent": "MM-PUR-VM", "RefTitle": "Double update for subsequent statistics structure", "RefUrl": "/notes/47199"}, {"RefNumber": "46612", "RefComponent": "MM-PUR-VM", "RefTitle": "Itemization arrangements purchasing incorrect", "RefUrl": "/notes/46612"}, {"RefNumber": "46128", "RefComponent": "MM-PUR-VM", "RefTitle": "Itemization arrangements purchasing is incorrect", "RefUrl": "/notes/46128"}, {"RefNumber": "45579", "RefComponent": "MM-PUR-VM", "RefTitle": "Subsequent settlement - Update error", "RefUrl": "/notes/45579"}, {"RefNumber": "452365", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Rebate arrangement currency not copied to condition records", "RefUrl": "/notes/452365"}, {"RefNumber": "45057", "RefComponent": "MM-PUR-VM", "RefTitle": "Error when creating with ref. to arrangem. in purch.", "RefUrl": "/notes/45057"}, {"RefNumber": "439493", "RefComponent": "SD-BIL-IV", "RefTitle": "Expiring currencies: Incorrect document currency", "RefUrl": "/notes/439493"}, {"RefNumber": "438324", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Addition to Note 400432", "RefUrl": "/notes/438324"}, {"RefNumber": "437447", "RefComponent": "MM-IS-PU", "RefTitle": "RMCENEUA: Short dump MOVE_TO_LIT_NOTALLOWED", "RefUrl": "/notes/437447"}, {"RefNumber": "437429", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message SG105 List settlement documents, setting up incomes", "RefUrl": "/notes/437429"}, {"RefNumber": "422649", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error in validity display (agrmt maintenance)- deletion", "RefUrl": "/notes/422649"}, {"RefNumber": "41380", "RefComponent": "MM-PUR-VM", "RefTitle": "Error when checking organizat. data for arrangement", "RefUrl": "/notes/41380"}, {"RefNumber": "41353", "RefComponent": "MM-PUR-VM", "RefTitle": "Error: No purchasing grp when creating arrangement", "RefUrl": "/notes/41353"}, {"RefNumber": "41086", "RefComponent": "MM-PUR-VM", "RefTitle": "Arrangements in purchasing cannot be saved", "RefUrl": "/notes/41086"}, {"RefNumber": "41072", "RefComponent": "MM-PUR-VM", "RefTitle": "Subsequent statistics setup arrangements purchasing", "RefUrl": "/notes/41072"}, {"RefNumber": "400898", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Recompilation bus.vol. data, incomes: sel. by arrangemt type", "RefUrl": "/notes/400898"}, {"RefNumber": "400893", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Detailed statement, message MN514", "RefUrl": "/notes/400893"}, {"RefNumber": "400432", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Expiring currencies, rebate arrangmnt maint. (purch., sales)", "RefUrl": "/notes/400432"}, {"RefNumber": "398739", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect conversion of EMU currencies - Part 2", "RefUrl": "/notes/398739"}, {"RefNumber": "39574", "RefComponent": "MM-PUR-VM", "RefTitle": "Subsequent settlement - income update", "RefUrl": "/notes/39574"}, {"RefNumber": "39557", "RefComponent": "MM-PUR-VM", "RefTitle": "Subsequent settlement - document balance credit memo", "RefUrl": "/notes/39557"}, {"RefNumber": "394673", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Cond. record status incorrectly set when creating with ref.", "RefUrl": "/notes/394673"}, {"RefNumber": "39327", "RefComponent": "MM-PUR", "RefTitle": "ME21 Vendor not relevant to volume rebate", "RefUrl": "/notes/39327"}, {"RefNumber": "39172", "RefComponent": "MM-PUR-VM", "RefTitle": "Incorr.payment method check when maint.arrangements", "RefUrl": "/notes/39172"}, {"RefNumber": "381831", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Consignment processing and subsequent settlement", "RefUrl": "/notes/381831"}, {"RefNumber": "375119", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Updating provisions for accrued inc. of parked credit memos", "RefUrl": "/notes/375119"}, {"RefNumber": "367295", "RefComponent": "MM-PUR-GF-PR", "RefTitle": "Missing tax code in subsequent settlement", "RefUrl": "/notes/367295"}, {"RefNumber": "34584", "RefComponent": "MM-PUR-VM", "RefTitle": "Error when creating purch. org. w/o company code", "RefUrl": "/notes/34584"}, {"RefNumber": "336231", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Importance of messages MN634, MN635 and MN636", "RefUrl": "/notes/336231"}, {"RefNumber": "333914", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: performance collective note", "RefUrl": "/notes/333914"}, {"RefNumber": "328145", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subs. updating of bus. vol. for deleted purchase order items", "RefUrl": "/notes/328145"}, {"RefNumber": "328142", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect conversion of EMU currencies - part 1", "RefUrl": "/notes/328142"}, {"RefNumber": "327921", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect condition granter accepted in arrangmnt maintnance", "RefUrl": "/notes/327921"}, {"RefNumber": "324279", "RefComponent": "SD-BIL-RB", "RefTitle": "Rebate: Displ of change docs f conditions is too slow", "RefUrl": "/notes/324279"}, {"RefNumber": "305806", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Provisions for income in price determination", "RefUrl": "/notes/305806"}, {"RefNumber": "303047", "RefComponent": "MM-PUR-GF-LIS", "RefTitle": "RMCENEUA - Error in selection by info structures", "RefUrl": "/notes/303047"}, {"RefNumber": "216306", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Missng updatng of income customer billing doc when cancellng", "RefUrl": "/notes/216306"}, {"RefNumber": "214146", "RefComponent": "MM-PUR-PO", "RefTitle": "Vendor master at plant level: No subs. settlement possible", "RefUrl": "/notes/214146"}, {"RefNumber": "207966", "RefComponent": "SD-BIL-GF", "RefTitle": "Cancell.billing doc.: Statistics are not updated", "RefUrl": "/notes/207966"}, {"RefNumber": "206060", "RefComponent": "MM-PUR-PO", "RefTitle": "Ind. 'Subseq. settlement', deviating vendor data", "RefUrl": "/notes/206060"}, {"RefNumber": "201492", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Performance improvement list output recompilation", "RefUrl": "/notes/201492"}, {"RefNumber": "200703", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: Error in Standard Customizing", "RefUrl": "/notes/200703"}, {"RefNumber": "200188", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message NAA234: Rebate arrangement maintenance", "RefUrl": "/notes/200188"}, {"RefNumber": "191134", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseq. settlmt accoun.:Short dump DATE_AFTER_RANGE", "RefUrl": "/notes/191134"}, {"RefNumber": "190902", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Income always 0 (incorrect rounding)", "RefUrl": "/notes/190902"}, {"RefNumber": "180434", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Validation of relevant documents before archiving", "RefUrl": "/notes/180434"}, {"RefNumber": "179864", "RefComponent": "MM-PUR-GF-CO", "RefTitle": "PURCHIS - commitments - problems during IR or GR", "RefUrl": "/notes/179864"}, {"RefNumber": "175341", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Multiple updating income when changing customer billing doc", "RefUrl": "/notes/175341"}, {"RefNumber": "167284", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "FAQs: Subsequent settlement (consulting, tips, Customizing)", "RefUrl": "/notes/167284"}, {"RefNumber": "159580", "RefComponent": "MM-PUR-GF-LIS", "RefTitle": "PURCHIS - Commitments - IR no update in simulation", "RefUrl": "/notes/159580"}, {"RefNumber": "153694", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Credit-side or debit-side settlement type", "RefUrl": "/notes/153694"}, {"RefNumber": "151607", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect proposal dcmnt number for cancelltn of credit memo", "RefUrl": "/notes/151607"}, {"RefNumber": "151398", "RefComponent": "MM-IS-PU", "RefTitle": "PURCHIS -double update for deliv.cst fr several POs", "RefUrl": "/notes/151398"}, {"RefNumber": "133601", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error of NAA040, NAA041 and MN272 in invoice verification", "RefUrl": "/notes/133601"}, {"RefNumber": "133493", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: Notes application area MM-PUR-VM-SET", "RefUrl": "/notes/133493"}, {"RefNumber": "128070", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: error message MN514", "RefUrl": "/notes/128070"}, {"RefNumber": "123297", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: message MN369", "RefUrl": "/notes/123297"}, {"RefNumber": "122727", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN177 with interim settlement", "RefUrl": "/notes/122727"}, {"RefNumber": "121299", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN268 cancelling credit memo", "RefUrl": "/notes/121299"}, {"RefNumber": "120610", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Data loss recompilation vendor volume data with short dump", "RefUrl": "/notes/120610"}, {"RefNumber": "120351", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Multiple update recompilation, retrospective update", "RefUrl": "/notes/120351"}, {"RefNumber": "119881", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Perf. recompile bus. vol. data/suppl. updating of bus. vol.", "RefUrl": "/notes/119881"}, {"RefNumber": "118087", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: message M8001", "RefUrl": "/notes/118087"}, {"RefNumber": "118080", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN 319 in the detailed statement", "RefUrl": "/notes/118080"}, {"RefNumber": "117988", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: messages MN514 and MN515", "RefUrl": "/notes/117988"}, {"RefNumber": "115307", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Updating business volumes for settled condition records", "RefUrl": "/notes/115307"}, {"RefNumber": "114111", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Recompilation of incomes from settlement documents RWMBON07", "RefUrl": "/notes/114111"}, {"RefNumber": "113655", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Deviating tax calculation in credt memos", "RefUrl": "/notes/113655"}, {"RefNumber": "113315", "RefComponent": "SD-MD-CM", "RefTitle": "VK894: Screen generation error for rebate", "RefUrl": "/notes/113315"}, {"RefNumber": "113240", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Alternative UM for qty-dependent materl volume rebate: VK070", "RefUrl": "/notes/113240"}, {"RefNumber": "113031", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: Taxes", "RefUrl": "/notes/113031"}, {"RefNumber": "112993", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Tax code for debit side settlement (Release 3.1I)", "RefUrl": "/notes/112993"}, {"RefNumber": "111366", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error messages MN164 and MN310", "RefUrl": "/notes/111366"}, {"RefNumber": "107699", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error MN305 during extension", "RefUrl": "/notes/107699"}, {"RefNumber": "105687", "RefComponent": "MM-PUR-RFQ", "RefTitle": "ME41: Volume rebate indicator from vendor master", "RefUrl": "/notes/105687"}, {"RefNumber": "103695", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Inconsistent deletion of period-specific conditions", "RefUrl": "/notes/103695"}, {"RefNumber": "102392", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsqunt settlemnt:program termintn, missng exceptn", "RefUrl": "/notes/102392"}, {"RefNumber": "100695", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "No update during reversal", "RefUrl": "/notes/100695"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "118080", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN 319 in the detailed statement", "RefUrl": "/notes/118080 "}, {"RefNumber": "113031", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: Taxes", "RefUrl": "/notes/113031 "}, {"RefNumber": "200703", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: Error in Standard Customizing", "RefUrl": "/notes/200703 "}, {"RefNumber": "113315", "RefComponent": "SD-MD-CM", "RefTitle": "VK894: Screen generation error for rebate", "RefUrl": "/notes/113315 "}, {"RefNumber": "76917", "RefComponent": "SD-MD-CM", "RefTitle": "Condition info: Syntax error in program RV13A...", "RefUrl": "/notes/76917 "}, {"RefNumber": "180434", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Validation of relevant documents before archiving", "RefUrl": "/notes/180434 "}, {"RefNumber": "439493", "RefComponent": "SD-BIL-IV", "RefTitle": "Expiring currencies: Incorrect document currency", "RefUrl": "/notes/439493 "}, {"RefNumber": "167284", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "FAQs: Subsequent settlement (consulting, tips, Customizing)", "RefUrl": "/notes/167284 "}, {"RefNumber": "76713", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: Foreign payments", "RefUrl": "/notes/76713 "}, {"RefNumber": "112993", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Tax code for debit side settlement (Release 3.1I)", "RefUrl": "/notes/112993 "}, {"RefNumber": "97157", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Tax code with settlement on debit-side (Release 3.0F)", "RefUrl": "/notes/97157 "}, {"RefNumber": "503040", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlement by credit memo: message MN227", "RefUrl": "/notes/503040 "}, {"RefNumber": "80233", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Debit-side settlement: Customizing, error messages", "RefUrl": "/notes/80233 "}, {"RefNumber": "452365", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Rebate arrangement currency not copied to condition records", "RefUrl": "/notes/452365 "}, {"RefNumber": "153694", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Credit-side or debit-side settlement type", "RefUrl": "/notes/153694 "}, {"RefNumber": "328145", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subs. updating of bus. vol. for deleted purchase order items", "RefUrl": "/notes/328145 "}, {"RefNumber": "336231", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Importance of messages MN634, MN635 and MN636", "RefUrl": "/notes/336231 "}, {"RefNumber": "400432", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Expiring currencies, rebate arrangmnt maint. (purch., sales)", "RefUrl": "/notes/400432 "}, {"RefNumber": "81603", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subs.settlement: Maintenance of settlement groups", "RefUrl": "/notes/81603 "}, {"RefNumber": "133493", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: Notes application area MM-PUR-VM-SET", "RefUrl": "/notes/133493 "}, {"RefNumber": "60174", "RefComponent": "BC-SRV-ASF-CAL", "RefTitle": "Factory calendar AM (incorrect special rules)", "RefUrl": "/notes/60174 "}, {"RefNumber": "437447", "RefComponent": "MM-IS-PU", "RefTitle": "RMCENEUA: Short dump MOVE_TO_LIT_NOTALLOWED", "RefUrl": "/notes/437447 "}, {"RefNumber": "96131", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseq.settlement:default accnt assgmnts not copied", "RefUrl": "/notes/96131 "}, {"RefNumber": "77258", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Required fields settlement document", "RefUrl": "/notes/77258 "}, {"RefNumber": "328142", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect conversion of EMU currencies - part 1", "RefUrl": "/notes/328142 "}, {"RefNumber": "438324", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Addition to Note 400432", "RefUrl": "/notes/438324 "}, {"RefNumber": "422649", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error in validity display (agrmt maintenance)- deletion", "RefUrl": "/notes/422649 "}, {"RefNumber": "437429", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message SG105 List settlement documents, setting up incomes", "RefUrl": "/notes/437429 "}, {"RefNumber": "73183", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Importance of error message MN042", "RefUrl": "/notes/73183 "}, {"RefNumber": "67669", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Extending field catalog for subsequent settlement", "RefUrl": "/notes/67669 "}, {"RefNumber": "201492", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Performance improvement list output recompilation", "RefUrl": "/notes/201492 "}, {"RefNumber": "190902", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Income always 0 (incorrect rounding)", "RefUrl": "/notes/190902 "}, {"RefNumber": "151607", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect proposal dcmnt number for cancelltn of credit memo", "RefUrl": "/notes/151607 "}, {"RefNumber": "122727", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN177 with interim settlement", "RefUrl": "/notes/122727 "}, {"RefNumber": "121299", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN268 cancelling credit memo", "RefUrl": "/notes/121299 "}, {"RefNumber": "113240", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Alternative UM for qty-dependent materl volume rebate: VK070", "RefUrl": "/notes/113240 "}, {"RefNumber": "111366", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error messages MN164 and MN310", "RefUrl": "/notes/111366 "}, {"RefNumber": "96224", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Business volume data recompilation, message MN380", "RefUrl": "/notes/96224 "}, {"RefNumber": "77509", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error FF718 in business volume comparison and agreement", "RefUrl": "/notes/77509 "}, {"RefNumber": "66054", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Information structures for subsequent settlement not updated", "RefUrl": "/notes/66054 "}, {"RefNumber": "375119", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Updating provisions for accrued inc. of parked credit memos", "RefUrl": "/notes/375119 "}, {"RefNumber": "200188", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message NAA234: Rebate arrangement maintenance", "RefUrl": "/notes/200188 "}, {"RefNumber": "133601", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error of NAA040, NAA041 and MN272 in invoice verification", "RefUrl": "/notes/133601 "}, {"RefNumber": "119881", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Perf. recompile bus. vol. data/suppl. updating of bus. vol.", "RefUrl": "/notes/119881 "}, {"RefNumber": "107699", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error MN305 during extension", "RefUrl": "/notes/107699 "}, {"RefNumber": "100695", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "No update during reversal", "RefUrl": "/notes/100695 "}, {"RefNumber": "85831", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect price determination", "RefUrl": "/notes/85831 "}, {"RefNumber": "394673", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Cond. record status incorrectly set when creating with ref.", "RefUrl": "/notes/394673 "}, {"RefNumber": "327921", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect condition granter accepted in arrangmnt maintnance", "RefUrl": "/notes/327921 "}, {"RefNumber": "305806", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Provisions for income in price determination", "RefUrl": "/notes/305806 "}, {"RefNumber": "216306", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Missng updatng of income customer billing doc when cancellng", "RefUrl": "/notes/216306 "}, {"RefNumber": "175341", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Multiple updating income when changing customer billing doc", "RefUrl": "/notes/175341 "}, {"RefNumber": "120610", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Data loss recompilation vendor volume data with short dump", "RefUrl": "/notes/120610 "}, {"RefNumber": "120351", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Multiple update recompilation, retrospective update", "RefUrl": "/notes/120351 "}, {"RefNumber": "115307", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Updating business volumes for settled condition records", "RefUrl": "/notes/115307 "}, {"RefNumber": "96375", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error w/ vendor business volume from subsequent debit/credit", "RefUrl": "/notes/96375 "}, {"RefNumber": "214146", "RefComponent": "MM-PUR-PO", "RefTitle": "Vendor master at plant level: No subs. settlement possible", "RefUrl": "/notes/214146 "}, {"RefNumber": "333914", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: performance collective note", "RefUrl": "/notes/333914 "}, {"RefNumber": "398739", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect conversion of EMU currencies - Part 2", "RefUrl": "/notes/398739 "}, {"RefNumber": "400898", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Recompilation bus.vol. data, incomes: sel. by arrangemt type", "RefUrl": "/notes/400898 "}, {"RefNumber": "400893", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Detailed statement, message MN514", "RefUrl": "/notes/400893 "}, {"RefNumber": "73214", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseq.settl.:Retrospec.compltn/recompltn of busin.vol.data", "RefUrl": "/notes/73214 "}, {"RefNumber": "207966", "RefComponent": "SD-BIL-GF", "RefTitle": "Cancell.billing doc.: Statistics are not updated", "RefUrl": "/notes/207966 "}, {"RefNumber": "179864", "RefComponent": "MM-PUR-GF-CO", "RefTitle": "PURCHIS - commitments - problems during IR or GR", "RefUrl": "/notes/179864 "}, {"RefNumber": "381831", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Consignment processing and subsequent settlement", "RefUrl": "/notes/381831 "}, {"RefNumber": "113655", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Deviating tax calculation in credt memos", "RefUrl": "/notes/113655 "}, {"RefNumber": "367295", "RefComponent": "MM-PUR-GF-PR", "RefTitle": "Missing tax code in subsequent settlement", "RefUrl": "/notes/367295 "}, {"RefNumber": "48497", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Item text, allocation no.", "RefUrl": "/notes/48497 "}, {"RefNumber": "98586", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Analysis reports, repair reports, Release 3.0", "RefUrl": "/notes/98586 "}, {"RefNumber": "46128", "RefComponent": "MM-PUR-VM", "RefTitle": "Itemization arrangements purchasing is incorrect", "RefUrl": "/notes/46128 "}, {"RefNumber": "39557", "RefComponent": "MM-PUR-VM", "RefTitle": "Subsequent settlement - document balance credit memo", "RefUrl": "/notes/39557 "}, {"RefNumber": "39574", "RefComponent": "MM-PUR-VM", "RefTitle": "Subsequent settlement - income update", "RefUrl": "/notes/39574 "}, {"RefNumber": "45579", "RefComponent": "MM-PUR-VM", "RefTitle": "Subsequent settlement - Update error", "RefUrl": "/notes/45579 "}, {"RefNumber": "46612", "RefComponent": "MM-PUR-VM", "RefTitle": "Itemization arrangements purchasing incorrect", "RefUrl": "/notes/46612 "}, {"RefNumber": "48078", "RefComponent": "MM-PUR-VM", "RefTitle": "Subsequent settlement and tax jurisdiction code", "RefUrl": "/notes/48078 "}, {"RefNumber": "48245", "RefComponent": "MM-PUR-VM", "RefTitle": "No provisions posting for accrued income goods rcpt", "RefUrl": "/notes/48245 "}, {"RefNumber": "48679", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error setting reference fields in FI (RW012)", "RefUrl": "/notes/48679 "}, {"RefNumber": "48968", "RefComponent": "MM-PUR-VM", "RefTitle": "Incorrect update during credit memo cancellation", "RefUrl": "/notes/48968 "}, {"RefNumber": "49067", "RefComponent": "MM-PUR-VM", "RefTitle": "Update Vol. business done with vendor Conversn prob", "RefUrl": "/notes/49067 "}, {"RefNumber": "49575", "RefComponent": "MM-PUR-VM", "RefTitle": "Subsequent settlement itemization error MN I 355", "RefUrl": "/notes/49575 "}, {"RefNumber": "62426", "RefComponent": "MM-PUR-VM", "RefTitle": "Accounting document contains incorrect income", "RefUrl": "/notes/62426 "}, {"RefNumber": "89057", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Importance of error message MN428", "RefUrl": "/notes/89057 "}, {"RefNumber": "114111", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Recompilation of incomes from settlement documents RWMBON07", "RefUrl": "/notes/114111 "}, {"RefNumber": "72199", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Problem analysis update business volume data", "RefUrl": "/notes/72199 "}, {"RefNumber": "159580", "RefComponent": "MM-PUR-GF-LIS", "RefTitle": "PURCHIS - Commitments - IR no update in simulation", "RefUrl": "/notes/159580 "}, {"RefNumber": "69800", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: Error message - FF758", "RefUrl": "/notes/69800 "}, {"RefNumber": "303047", "RefComponent": "MM-PUR-GF-LIS", "RefTitle": "RMCENEUA - Error in selection by info structures", "RefUrl": "/notes/303047 "}, {"RefNumber": "324279", "RefComponent": "SD-BIL-RB", "RefTitle": "Rebate: Displ of change docs f conditions is too slow", "RefUrl": "/notes/324279 "}, {"RefNumber": "206060", "RefComponent": "MM-PUR-PO", "RefTitle": "Ind. 'Subseq. settlement', deviating vendor data", "RefUrl": "/notes/206060 "}, {"RefNumber": "191134", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseq. settlmt accoun.:Short dump DATE_AFTER_RANGE", "RefUrl": "/notes/191134 "}, {"RefNumber": "151398", "RefComponent": "MM-IS-PU", "RefTitle": "PURCHIS -double update for deliv.cst fr several POs", "RefUrl": "/notes/151398 "}, {"RefNumber": "128070", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: error message MN514", "RefUrl": "/notes/128070 "}, {"RefNumber": "118087", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: message M8001", "RefUrl": "/notes/118087 "}, {"RefNumber": "103695", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Inconsistent deletion of period-specific conditions", "RefUrl": "/notes/103695 "}, {"RefNumber": "98843", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseq.settlement:00015(value of field is negative)", "RefUrl": "/notes/98843 "}, {"RefNumber": "90661", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: delete purchase order item", "RefUrl": "/notes/90661 "}, {"RefNumber": "117988", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: messages MN514 and MN515", "RefUrl": "/notes/117988 "}, {"RefNumber": "105687", "RefComponent": "MM-PUR-RFQ", "RefTitle": "ME41: Volume rebate indicator from vendor master", "RefUrl": "/notes/105687 "}, {"RefNumber": "102392", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsqunt settlemnt:program termintn, missng exceptn", "RefUrl": "/notes/102392 "}, {"RefNumber": "97483", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsq.settlement:too many items in settlement dcmnt", "RefUrl": "/notes/97483 "}, {"RefNumber": "94381", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect currency translation f.detailed statement", "RefUrl": "/notes/94381 "}, {"RefNumber": "92661", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Unjustified error message MN515", "RefUrl": "/notes/92661 "}, {"RefNumber": "89671", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorr.update incomes f. debt-side sttlmnt account.", "RefUrl": "/notes/89671 "}, {"RefNumber": "84256", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Extension copies deleted condition records", "RefUrl": "/notes/84256 "}, {"RefNumber": "83771", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Outline agreement:not subj. to volume-based rebate", "RefUrl": "/notes/83771 "}, {"RefNumber": "78695", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Termination when checking settlement material", "RefUrl": "/notes/78695 "}, {"RefNumber": "75655", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error message VK358: Purchasing organization ....", "RefUrl": "/notes/75655 "}, {"RefNumber": "74020", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Updating and posting provisions for accrued income", "RefUrl": "/notes/74020 "}, {"RefNumber": "62882", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "LIS - Subsequent settlement - Field catalog EKOK", "RefUrl": "/notes/62882 "}, {"RefNumber": "39327", "RefComponent": "MM-PUR", "RefTitle": "ME21 Vendor not relevant to volume rebate", "RefUrl": "/notes/39327 "}, {"RefNumber": "41072", "RefComponent": "MM-PUR-VM", "RefTitle": "Subsequent statistics setup arrangements purchasing", "RefUrl": "/notes/41072 "}, {"RefNumber": "123297", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: message MN369", "RefUrl": "/notes/123297 "}, {"RefNumber": "48063", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: Error message FF758", "RefUrl": "/notes/48063 "}, {"RefNumber": "95760", "RefComponent": "MM-SRV-SR", "RefTitle": "Subsqent settlmnt:Update entry of services performd", "RefUrl": "/notes/95760 "}, {"RefNumber": "47987", "RefComponent": "MM-PUR-VM", "RefTitle": "Subsequent statistics structure, incorrect data", "RefUrl": "/notes/47987 "}, {"RefNumber": "47199", "RefComponent": "MM-PUR-VM", "RefTitle": "Double update for subsequent statistics structure", "RefUrl": "/notes/47199 "}, {"RefNumber": "41380", "RefComponent": "MM-PUR-VM", "RefTitle": "Error when checking organizat. data for arrangement", "RefUrl": "/notes/41380 "}, {"RefNumber": "34584", "RefComponent": "MM-PUR-VM", "RefTitle": "Error when creating purch. org. w/o company code", "RefUrl": "/notes/34584 "}, {"RefNumber": "39172", "RefComponent": "MM-PUR-VM", "RefTitle": "Incorr.payment method check when maint.arrangements", "RefUrl": "/notes/39172 "}, {"RefNumber": "41086", "RefComponent": "MM-PUR-VM", "RefTitle": "Arrangements in purchasing cannot be saved", "RefUrl": "/notes/41086 "}, {"RefNumber": "41353", "RefComponent": "MM-PUR-VM", "RefTitle": "Error: No purchasing grp when creating arrangement", "RefUrl": "/notes/41353 "}, {"RefNumber": "45057", "RefComponent": "MM-PUR-VM", "RefTitle": "Error when creating with ref. to arrangem. in purch.", "RefUrl": "/notes/45057 "}, {"RefNumber": "49528", "RefComponent": "MM-PUR-VM", "RefTitle": "Subsequent settlement itemization error MN I 355", "RefUrl": "/notes/49528 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30A", "To": "31I", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}