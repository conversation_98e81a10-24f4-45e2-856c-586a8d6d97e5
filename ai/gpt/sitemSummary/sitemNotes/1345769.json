{"Request": {"Number": "1345769", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 371, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016801052017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001345769?language=E&token=FF5B61E271934197BB205B44BD2106D3"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001345769", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1345769"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Pilot Release"}, "ReleasedOn": {"_label": "Released On", "value": "29.05.2009"}, "SAPComponentKey": {"_label": "Component", "value": "MM-PUR-GF-ES"}, "SAPComponentKeyText": {"_label": "Component", "value": "Enterprise Services in Purchasing"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Purchasing", "value": "MM-PUR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "MM-PUR-GF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-GF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Enterprise Services in Purchasing", "value": "MM-PUR-GF-ES", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-GF-ES*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1345769 - OPS eSOA services for EhP4 SP03"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The following enterprise services, not released for SAP ERP 6.0 Enhancement Package 4, should be used under the terms of a \"Software-&#x00DC;berlassungsvertrag\":<br />Find Purchasing Scheduling Agreement Basic Data by Elements<br />Read Purchasing Scheduling Agreement<br />Update Purchasing Scheduling Agreement<br />Find Purchasing Scheduling Agreement Item Goods Receipt Deviation by Elements<br />Find Purchasing Scheduling Agreement with Blocked Delivery Schedules by Elements<br />Find Material Supply and Demand View Basic Data by Elements<br />Read Material Supply and Demand View<br />Find Purchase Request by Processing Status<br />Find Outbound Delivery Request by Elements<br />Find Goods Movement Exception Basic Data by Elements</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>MaterialSupplyAndDemandViewERPBasicDataByElementsQueryResponse_In<br />MaterialSupplyAndDemandViewERPByIdentifyingElementsQueryResponse_In<br />PurchasingSchedulingAgreementERPByIDQueryResponse_In<br />PurchasingSchedulingAgreementERPUpdateRequestConfirmation_In<br />PurchasingSchedulingAgreementERPBasicDataByElementsQueryResponse_In<br />PurchasingSchedulingAgreementERPItemGoodsReceiptDeviationByElementsQueryResponse_In<br />PurchasingSchedulingAgreementERPBlockedDeliveryScheduleByElementsQueryResponse_In<br />GoodsMovementExceptionERPBasicDataByElementsQueryResponse_In<br />PurchaseRequestERPByProcessingStatusQueryResponse_In<br />OutboundDeliveryRequestERPByElementsQueryResponse_In</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>SAP APPL 6.04 SP03 and ECC-DIMP 604 SP03 are a prerequisite for this note<br /><br />1. Ensure that delivered objects are not modified on your system before implementation, otherwise this may lead to errors in upcoming patching and upgrade processes.<br />2. The corrections you receive with this transport will be part of SP4 of SAP_APPL 604 and ECC-DIMP 604. Please import those SPs in one queue as soon as possible to set your system back to SAP standard. This shall be done prior to the next upgrade of components in the system.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Download the following deliverables from the service marketplace:<br />QE1K900024.SAR<br />XI7_1_SAP_APPL_6.04_bmw_obj.tpz<br /><br />Install the downloaded files according to the following description:<br />Installation of the ESR content (see SAP Note 1152640):<br />1. Step: Import ESR Content SAP APPL 604 SP3 into PI 7.1<br />2. Step: Import ESR Content ECC-DIMP 604 SP3 into PI 7.1<br />3. Step: Import ESR Content File SAP APPL 604_bmw_obj<br /><br />Installation of the cross transport file:<br />1. Step: Please store the SAR file in the transport directory (default: /usr/sap/trans) and decompress it with the following command:<br /><br />SAPCAR -xvf QE1K900024.SAR<br /><br />2. Step: Ensure that the data (R900024.QE1) and cofile (K900024.QE1) are in the corresponding subdirectories #data# and #cofiles#.<br />3. Step: Please import this package with the following command from the #bin# sub-directory of your transport directory:<br /><br />tp addtobuffer QE1K900024 &lt;SID&gt; pf=&lt;tp profile&gt;<br />tp import QE1K900024 &lt;SID&gt; client=&lt;XXX&gt; pf=&lt;tp profile&gt;<br /><br />Set the parameters in &lt; &gt; accordingly to your system.<br /><br />4. Step: After installing the cross transport file apply the following SAP notes as well (together with their prerequisite notes):<br />1343467<br />1344126<br />1346819<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "PP-MRP-PE (Planning Evaluation)"}, {"Key": "Responsible                                                                                         ", "Value": "I036160"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D033113)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001345769/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001345769/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001345769/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001345769/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001345769/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001345769/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001345769/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001345769/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001345769/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1346819", "RefComponent": "SD-SLS-ES", "RefTitle": "OPS downport services: CL_SHP_ES_MESSAGE_TYPE_PARSER methods", "RefUrl": "/notes/1346819"}, {"RefNumber": "1344126", "RefComponent": "PP-MRP-PE", "RefTitle": "Error handling in func. module MD_SELECTION_MRP_LIST_RANGE", "RefUrl": "/notes/1344126"}, {"RefNumber": "1343467", "RefComponent": "PP-MRP-PE", "RefTitle": "Error handling in function module MD_SELECTION_SR_LIST_RANGE", "RefUrl": "/notes/1343467"}, {"RefNumber": "1152640", "RefComponent": "BC-XI", "RefTitle": "SAP NetWeaver 7.1 including EHPs: Importing ESR content", "RefUrl": "/notes/1152640"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1152640", "RefComponent": "BC-XI", "RefTitle": "SAP NetWeaver 7.1 including EHPs: Importing ESR content", "RefUrl": "/notes/1152640 "}, {"RefNumber": "1344126", "RefComponent": "PP-MRP-PE", "RefTitle": "Error handling in func. module MD_SELECTION_MRP_LIST_RANGE", "RefUrl": "/notes/1344126 "}, {"RefNumber": "1346819", "RefComponent": "SD-SLS-ES", "RefTitle": "OPS downport services: CL_SHP_ES_MESSAGE_TYPE_PARSER methods", "RefUrl": "/notes/1346819 "}, {"RefNumber": "1343467", "RefComponent": "PP-MRP-PE", "RefTitle": "Error handling in function module MD_SELECTION_SR_LIST_RANGE", "RefUrl": "/notes/1343467 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "ECC-DIMP", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}