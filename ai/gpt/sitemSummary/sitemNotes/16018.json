{"Request": {"Number": "16018", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 630, "Error": {}, "SAPNote": {"_type": "00200720420000000131", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000016018?language=E&token=A8F106E994078DD3880A95862A56C27C"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000016018", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000016018/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "16018"}, "Type": {"_label": "Type", "value": "SAP-Standardhinweis"}, "Version": {"_label": "Version", "value": 35}, "Recency": {"_label": "Currentness", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Checklist"}, "Priority": {"_label": "Priority", "value": "Normal"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "22.08.2005"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-GEN"}, "SAPComponentKeyText": {"_label": "Component", "value": "Use SAP Note 1433157 to find the correct components."}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "16018 - ARCHIVE Please use KBA 1339209 - Best practices for creating incidents on the SAP Support Portal - More information about the message required"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=16018&TargetLanguage=EN&Component=XX-SER-GEN&SourceLanguage=DE&Priority=06\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/16018/D\" target=\"_blank\">/notes/16018/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Langtext\">Long Text</h3>\r\n<p><strong>Symptom</strong></p>\r\n<p>SAP Active Global Support requires detailed information.</p>\r\n\r\n<p><strong>Solution</strong></p>\r\n<p><br />Dear Customer,<br /><br />The following is a short checklist that you can use to effectively describe exceptional situations in your system.<br /><br />In order to be able to process your request in an informed and quick manner and, if necessary, to forward it to another specialist, we need all relevant detailed information about the situation described by you.<br /><br />If you do not have access to the SAPNet - R/3 front end, send us the required data by fax.<br />For the fax number, see SAP Note 560499 (international) or 16481 (Germany).<br /><br />Please describe your actions in the system in such a detailed and complete way that we can trace the entire process without further inquiries. To avoid confusion due to technical terms that are sometimes used differently in the SAP environment, we ask you to concentrate on unique (technical) terms in your description.</p>\r\n<p><strong>In any case, a complete situation description includes:</strong></p>\r\n<ul>\r\n<li>Where are you?</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Enter the system ID (for example: &#39;P30&#39;), the client, and the complete menu path.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>What did you do?</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Please fully specify the order of their actions.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Please name the values of all input fields.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>What did you get?</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Specify the error number or the values of result fields.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>What did you want to get?</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Please state your expectations for the system in this situation.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Is the problem reproducible?</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If so, write down a complete example in your message.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Has the problem occurred since a special event?</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If so, make a note of this in your message. (e.g. Upgrade, applying a patch, changing the hardware configuration, client copy, legacy data transfer ...)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Does the problem happen to all users?</li>\r\n</ul>\r\n<ul>\r\n<li>Which notes have already been taken into account on this topic?</li>\r\n</ul>\r\n<ul>\r\n<li>Is there any particular time pressure to solve the problem?<br />Define the priority of your message accordingly. Note the definition of the priorities. (F1 help for the &#39;Priority&#39; field or SAP Note 67739.)</li>\r\n</ul>\r\n<ul>\r\n<li>Subject Area<br />For SAPNet - R/3 front-end messages, specify the component as precisely as possible (F4 help), for example: &quot;BC-DB-ORA&quot;.</li>\r\n</ul>\r\n<ul>\r\n<li>Contact person for the content of the message</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Name, department, telephone and fax connection</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Contact person for opening the remote connection</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Name, department, telephone and fax connection</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Consultants (if applicable)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Name, Company, Telephone, and Fax Connection</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>System Information</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Status of the system<br />Productive, Development, Test, or Demo System</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP Release and Maintenance Level<br />visible in the menu by choosing &quot;System -> Status&quot;, for example, 3.1I incl. Support Package 6<br />If you use an IBU or add-on product, enter its name and release level.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Remote Connection<br />Is there a remote connection to the affected R/3 System?<br />(If not, see SAP Note 35010 for information about the actions required to set up a remote connection.)</li>\r\n</ul>\r\n</ul>\r\n<p><strong>Relevant minimum information for problems in the applications in detail:</strong></p>\r\n<ul>\r\n<li>Menu Path</li>\r\n</ul>\r\n<ul>\r\n<li>Error Number (5 Characters)<br />(visible by double-clicking on the status line)</li>\r\n</ul>\r\n<ul>\r\n<li>Program Information<br />(can be seen in the menu by choosing &quot;System -> Status&quot;):</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>System ID</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Client</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Transaction (4 characters)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Program (Screen)<br />Has the program been modified or are user exits used?<br />Was the program executed online or in background processing?</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Screen Number</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>In the case of program terminations:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Please provide us with the first 12 pages of the short dump (e.g. by fax).<br />(For an overview of all terminations, use transaction ST22.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Please provide us with the temporally relevant entries in the system log.<br />(You can display the system log using transaction SM21.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Are there any update terminations? If so, which one?<br />(For an overview of all terminations, use transaction SM13.)</li>\r\n</ul>\r\n<ul>\r\n<li>Are there lock entries that are not released? If so, which one?<br />(For an overview of all locks, use transaction SM12.)</li>\r\n</ul>\r\n<ul>\r\n<li>Was an attempt to repeat the transaction?</li>\r\n</ul>\r\n<p><strong>In the case of problems with batch input sessions:</strong></p>\r\n<ul>\r\n<li>How was the session created?</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>with the help of a separate report?</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>with standard reports (please specify the name of the report)?</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>created by another system?</li>\r\n</ul>\r\n</ul>\r\n<p><strong>Relevant minimum information for problems in Basis in detail:</strong></p>\r\n<ul>\r\n<li>Job log (SM37)</li>\r\n</ul>\r\n<ul>\r\n<li>Trace files (in the work directory; search for unique error messages, transaction ST11). dev_disp and dev_w* are particularly important (see Note 112).<br />If the problem requires a restart of the system or individual application servers, save the trace files and any resulting core files in the work directory beforehand.</li>\r\n</ul>\r\n<ul>\r\n<li>For database problems: Entries in the database log file/alert file</li>\r\n</ul>\r\n<ul>\r\n<li>For transport problems: error messages in the transport logs on /usr/sap/trans/log</li>\r\n</ul>\r\n<ul>\r\n<li>For upgrade problems: Error messages in the upgrade logs to /usr/sap/put/log</li>\r\n</ul>\r\n<p>This text is a standard text. Please understand that it can only apply imperfectly to the reported problem.</p></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Owner                                                                                    ", "Value": "<PERSON><PERSON><PERSON> (I028274)"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON><PERSON><PERSON> (I028274)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000016018/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "984434", "RefComponent": "XX-SER-FORME", "RefTitle": "How to speed up customer incident processing", "RefUrl": "/notes/984434"}, {"RefNumber": "97660", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Further Information Required", "RefUrl": "/notes/97660"}, {"RefNumber": "94300", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/94300"}, {"RefNumber": "902492", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/902492"}, {"RefNumber": "88931", "RefComponent": "XX-SER-NET-RCSC", "RefTitle": "OSS: Missing or insufficient error description", "RefUrl": "/notes/88931"}, {"RefNumber": "628183", "RefComponent": "BC-SEC", "RefTitle": "Top 33 General Notes", "RefUrl": "/notes/628183"}, {"RefNumber": "53597", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/53597"}, {"RefNumber": "525974", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/525974"}, {"RefNumber": "52373", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/52373"}, {"RefNumber": "45988", "RefComponent": "BC-CCM-BTC", "RefTitle": "Problem message incomplete -&gt; batch processing", "RefUrl": "/notes/45988"}, {"RefNumber": "29501", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/29501"}, {"RefNumber": "137736", "RefComponent": "XX-SER-GEN", "RefTitle": "Message Creation(India, Sreelanka, Bangaldesh Cust)", "RefUrl": "/notes/137736"}, {"RefNumber": "136139", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/136139"}, {"RefNumber": "108688", "RefComponent": "XX-CSC-US", "RefTitle": "Global Solution Center - IBD - Template for Creating Message", "RefUrl": "/notes/108688"}, {"RefNumber": "77172", "RefComponent": "XX-SER-GEN", "RefTitle": "Informação necessária da mensensm", "RefUrl": "/notes/77172"}, {"RefNumber": "77150", "RefComponent": "XX-SER-GEN", "RefTitle": "Información necesaria sobre mensaje", "RefUrl": "/notes/77150"}, {"RefNumber": "67739", "RefComponent": "XX-SER-GEN", "RefTitle": "Problem Message Priorities", "RefUrl": "/notes/67739"}, {"RefNumber": "64542", "RefComponent": "XX-SER-GEN", "RefTitle": "More information in your problem description (Italy)", "RefUrl": "/notes/64542"}, {"RefNumber": "63646", "RefComponent": "XX-SER-GEN", "RefTitle": "Information about the problem message", "RefUrl": "/notes/63646"}, {"RefNumber": "60515", "RefComponent": "XX-SER-GEN", "RefTitle": "More information required - SAP Poland", "RefUrl": "/notes/60515"}, {"RefNumber": "46962", "RefComponent": "XX-SER-GEN", "RefTitle": "More Info Required", "RefUrl": "/notes/46962"}, {"RefNumber": "44433", "RefComponent": "XX-SER-GEN", "RefTitle": "Information required on problem - SAP UK", "RefUrl": "/notes/44433"}, {"RefNumber": "38373", "RefComponent": "XX-SER-GEN", "RefTitle": "Support Center: Telephone and Fax Numbers", "RefUrl": "/notes/38373"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "Validity": {"_label": "Products", "_columnNames": {"Product": "Products"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=16018&TargetLanguage=EN&Component=XX-SER-GEN&SourceLanguage=DE&Priority=06\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/16018/D\" target=\"_blank\">/notes/16018/D</a>."}}}}