{"Request": {"Number": "457029", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 192, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000457029?language=E&token=9DDE7A3D461B06EF9219F4792CB9CFEC"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000457029", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000457029/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "457029"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "In Process"}, "ReleasedOn": {"_label": "Released On", "value": "00.00.0000"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PROJ-GSC-EVC"}, "SAPComponentKeyText": {"_label": "Component", "value": "GSC Project 'Earned Value Component'"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Project-based solutions", "value": "XX-PROJ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Global Solution Center Projects", "value": "XX-PROJ-GSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-GSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "GSC Project 'Earned Value Component'", "value": "XX-PROJ-GSC-EVC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-GSC-EVC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "457029 - EVC Release 1: Documentation"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>EVC Release 1 up to date documentation</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>EVC, Earned Value Component, Documentation, Installation, Help,<br />Training, OSS Procedures, Repairs</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>EVC Release 1 up to date documentation.&#x00A0;&#x00A0;Reference of necessary Notes<br />to apply to core SAP support to allow EVC processing.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>You can find the following EVC Release 1 Documentation in SAPSERVX,<br />where X has to be replaced by one of the following digits:<br />3 or 4 or 5 or 6, etc., for example: SAPSERV4. You can use FTP program<br />in order to download any file from any SAPSERVX by using the following<br />paths:<br /><br />*** NOTE ****<br />Currently EVC is only available on a restricted bases.&#x00A0;&#x00A0;As such the<br />normal SAP documentation and installation methodology used for other<br />Global Solution Center components is NOT being used. (The document<br />structure is shown below.)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For that reason, the EVC product is only available by contacting<br />the Global Solution Center.&#x00A0;&#x00A0;The password protected files are found at<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPSERVx:/specific/usa_ccg/evc/evc45B or<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPSERVx:/specific/usa_ccg/evc/evc46C.<br />These zipped files contain the documentation for installation.<br />**********<br /><br />1. Product overview:&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Currently not available)<br />&#x00A0;&#x00A0;~ftp/specific/usa_ccg/evc/documentation/EVC_Product_Overview.exe<br /><br />2. Help :&#x00A0;&#x00A0;&#x00A0;&#x00A0; (Currently not available)<br />&#x00A0;&#x00A0;~ftp/specific/usa_ccg/evc/documentation/help/EVC_1_Help.exe<br /><br />3. Training:&#x00A0;&#x00A0;(Currently not available)<br />&#x00A0;&#x00A0;~ftp/specific/usa_ccg/evc/documentation/training/EVC_1_Training.exe<br /><br />4. OSS Procedures:<br />&#x00A0;&#x00A0;~ftp/specific/usa_ccg/oss/OSS.exe<br /><br />5. Installation documentation:<br />&#x00A0;&#x00A0; 1) for R/3 4.5b:&#x00A0;&#x00A0; (Currently not available)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;~ftp/specific/usa_ccg/evc/documentation/R45B/EVC_1_45b_Install.exe<br /><br />&#x00A0;&#x00A0; 2) for R/3 4.6x:&#x00A0;&#x00A0;(Currently not available)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;~ftp/specific/usa_ccg/evc/documentation/R46X/EVC_1_46x_Install.exe<br /><br />All files are self-extracting ZIP files - download them in binary mode.<br />------------------------------------------------------------------------<br />To install the EVC the very first time you must have and transport<br />the EVC Main Transport. After it is done you must use the EVC<br />Installation Guide to apply all necessary repairs manually in your<br />development system and promote them through your landscape.<br />Make sure to use the correct Installation Guide for your SAP<br />R/3 release.&#x00A0;&#x00A0;Required notes to ensure are applied include:<br />&#x00A0;&#x00A0; 419067<br />&#x00A0;&#x00A0; 421871<br />&#x00A0;&#x00A0; 432677<br />&#x00A0;&#x00A0; 428875<br />&#x00A0;&#x00A0; 440751<br />&#x00A0;&#x00A0; 442930<br />&#x00A0;&#x00A0; 438431.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "FILE"}, {"Key": "Transaction codes", "Value": "SUCH"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I803769)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I803769)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000457029/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000457029/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000457029/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000457029/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000457029/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000457029/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000457029/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000457029/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000457029/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "63845", "RefComponent": "XX-SER-NET", "RefTitle": "Corrections on sapserv - searching for files", "RefUrl": "/notes/63845"}, {"RefNumber": "501534", "RefComponent": "XX-PROJ-GSC-EVC", "RefTitle": "Installation Instructions-EVC Add-on-EVC_wInsight Package", "RefUrl": "/notes/501534"}, {"RefNumber": "457030", "RefComponent": "XX-PROJ-GSC-EVC", "RefTitle": "EVC Release 1 updated weekly correction numbers", "RefUrl": "/notes/457030"}, {"RefNumber": "446168", "RefComponent": "XX-PROJ-GSC-EVC", "RefTitle": "Earned Value Component Overview", "RefUrl": "/notes/446168"}, {"RefNumber": "442930", "RefComponent": "XX-PROJ-GSC-EVC", "RefTitle": "EVC component: Composite SAP Note", "RefUrl": "/notes/442930"}, {"RefNumber": "440751", "RefComponent": "PS-IS-ACC", "RefTitle": "HFPM_READ_RPSCO_MULTI: Fields MEINH, MENTP not filled", "RefUrl": "/notes/440751"}, {"RefNumber": "438431", "RefComponent": "XX-PROJ-GSC-EVC", "RefTitle": "EVC Project: Customer Specific Enhancements for CNE5", "RefUrl": "/notes/438431"}, {"RefNumber": "432677", "RefComponent": "PS-PRG-EVA", "RefTitle": "Creating and copying simulation version", "RefUrl": "/notes/432677"}, {"RefNumber": "428875", "RefComponent": "PS-IS-ACC", "RefTitle": "HFPM_READ_RPSCO_MULTI: Client field not filled", "RefUrl": "/notes/428875"}, {"RefNumber": "421871", "RefComponent": "PS-PRG-EVA", "RefTitle": "CNE1/CNE2: Progress value calculation", "RefUrl": "/notes/421871"}, {"RefNumber": "419067", "RefComponent": "PS-SIM", "RefTitle": "Actual dates are not transferred into simulation", "RefUrl": "/notes/419067"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "446168", "RefComponent": "XX-PROJ-GSC-EVC", "RefTitle": "Earned Value Component Overview", "RefUrl": "/notes/446168 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "501534", "RefComponent": "XX-PROJ-GSC-EVC", "RefTitle": "Installation Instructions-EVC Add-on-EVC_wInsight Package", "RefUrl": "/notes/501534 "}, {"RefNumber": "438431", "RefComponent": "XX-PROJ-GSC-EVC", "RefTitle": "EVC Project: Customer Specific Enhancements for CNE5", "RefUrl": "/notes/438431 "}, {"RefNumber": "419067", "RefComponent": "PS-SIM", "RefTitle": "Actual dates are not transferred into simulation", "RefUrl": "/notes/419067 "}, {"RefNumber": "428875", "RefComponent": "PS-IS-ACC", "RefTitle": "HFPM_READ_RPSCO_MULTI: Client field not filled", "RefUrl": "/notes/428875 "}, {"RefNumber": "440751", "RefComponent": "PS-IS-ACC", "RefTitle": "HFPM_READ_RPSCO_MULTI: Fields MEINH, MENTP not filled", "RefUrl": "/notes/440751 "}, {"RefNumber": "442930", "RefComponent": "XX-PROJ-GSC-EVC", "RefTitle": "EVC component: Composite SAP Note", "RefUrl": "/notes/442930 "}, {"RefNumber": "457030", "RefComponent": "XX-PROJ-GSC-EVC", "RefTitle": "EVC Release 1 updated weekly correction numbers", "RefUrl": "/notes/457030 "}, {"RefNumber": "421871", "RefComponent": "PS-PRG-EVA", "RefTitle": "CNE1/CNE2: Progress value calculation", "RefUrl": "/notes/421871 "}, {"RefNumber": "432677", "RefComponent": "PS-PRG-EVA", "RefTitle": "Creating and copying simulation version", "RefUrl": "/notes/432677 "}, {"RefNumber": "63845", "RefComponent": "XX-SER-NET", "RefTitle": "Corrections on sapserv - searching for files", "RefUrl": "/notes/63845 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}