{"Request": {"Number": "1172899", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 384, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016524332017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001172899?language=E&token=CF0A86F93F9C1BA2312287967379F0C0"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001172899", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001172899/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1172899"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 19}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "19.04.2010"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-ADDON"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade Add-On Components"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade Add-On Components", "value": "BC-UPG-ADDON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-ADDON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1172899 - BI_CONT 7.04: Information about installation and upgrade"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note contains information about <B>Business Intelligence Content 7.04</B> in relation to<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Add-On installation<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Basis (BW) upgrade to SAP_BW 7.00 or 7.01<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Add-On Support Packages<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p><br />SAINT, installation, upgrade, BI_CONT, add-on, AOI, AOX<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You want to install Business Intelligence Content (BI_CONT 7.04).<br />You want to perform a delta upgrade from BI_CONT 7.02 or 7.03 to BI_CONT 7.04.<br />You want to perform a BW upgrade to SAP_BW 7.00 or 7.01.<br />You want to import Add-On Support Packages for BI_CONT 7.04.<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br /><B>This note is updated on a regular basis. You should therefore obtain the latest version before you start the upgrade.</B><br /><br /></p> <b>Contents</b><br /> <OL>1. Change history</OL> <OL>2. Important general information</OL> <OL><OL>a) qRFC version</OL></OL> <OL><OL>b) Updates</OL></OL> <OL>3. Installation, delta upgrade</OL> <OL><OL>a) Prerequisite</OL></OL> <OL><OL>b) Preparations</OL></OL> <OL><OL>c) Performing the installation</OL></OL> <OL><OL>d) Performing the delta upgrade</OL></OL> <p> &#x00A0;&#x00A0;&#x00A0;&#x00A0;I)&#x00A0;&#x00A0;Including BI_CONT 704 in an Enhancement Package upgrade with SAPehpi<br /> II) Delta upgrade after BI_CONT 704 with SAINT</p> <OL>4. BW upgrade</OL> <OL><OL>a) Prerequisite</OL></OL> <OL><OL>b) Preparations</OL></OL> <OL><OL>c) Additional information about PREPARE</OL></OL> <OL><OL>d) Additional information about the upgrade</OL></OL> <OL><OL>e) Password</OL></OL> <OL>5. Postprocessing the BI_CONT 704 installation/upgrade</OL> <p><br /></p> <b>Contents</b><br /> <OL>1. Change history<br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Date</TH><TH ALIGN=LEFT> Section</TH><TH ALIGN=LEFT> Short description</TH></TR> <TR><TD>19.05.2009</TD><TD> 4.e)</TD><TD> A description and solution of an error that occurs when you upgrade to ECC 6.0 (with or without an Enhancement Package) has been added.</TD></TR> <TR><TD></TD></TR> </TABLE></OL> <p></p> <OL>2. Important general information</OL> <OL><OL>a) qRFC version</OL></OL> <UL><UL><LI>Note that the qRFC version must be 45 or higher (see Note 0498484).</LI></UL></UL> <OL><OL>b) Updates</OL></OL> <UL><UL><LI>Process your V3 update entries before you carry out the upgrade. Otherwise, there is a risk that you may no longer be able to update entries if changes are introduced into the interface structures of the V3 update modules by the patch or upgrade (see Note 328181).</LI></UL></UL> <UL><LI>Before the upgrade, process your entries in the extraction queues. Otherwise, there is a risk that you may no longer be able to update these entries if changes to the interface structures of the qRFC function modules are introduced by the patch or the upgrade (see Note 328181).</LI></UL> <UL><UL><LI>Before the upgrade, delete your entries in the reconstruction tables for the logistics extraction applications. Otherwise, there is a risk that you may no longer be able to use these entries if changes to the extraction structures are introduced by the patch or the upgrade (see Note 328181).</LI></UL></UL> <OL>3. Installation, delta upgrade</OL> <OL><OL>a) Prerequisite</OL></OL> <UL><UL><LI>Import the latest SPAM/SAINT update.</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Refer to the following notes before you start the installation:</TH></TR> <TR><TD>Add-Ons: General conditions </TD><TD> 70228</TD></TR> <TR><TD>Note about the release strategy</TD><TD> 153967</TD></TR> <TR><TD>Known problems in transaction SAINT</TD><TD> 179116</TD></TR> </TABLE></UL></UL> <p></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Required component Support Packages for BI_CONT 704:</TH></TR> </TABLE> <UL><LI><B>SAP NetWeaver 7.0</B><br />SAP NetWeaver 7.0 Support Package stack 17</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>SAP_BASIS</TD><TD> 700</TD><TD> Support Package 17</TD></TR> <TR><TD>SAP_ABA</TD><TD> 700</TD><TD> Support Package 17</TD></TR> <TR><TD>SAP_BW</TD><TD> 700</TD><TD> Support Package 19</TD></TR> <TR><TD>PI_BASIS</TD><TD> 2005_1_700</TD><TD> Support Package 17</TD></TR> <TR><TD> or </TD><TD> 2006_1_700</TD><TD> Support Package 07</TD></TR> <TR><TD></TD></TR> </TABLE></UL> <p></p> <UL><LI><B>SAP EHP1 for SAP NetWeaver 7.0</B><br />SAP EHP1 for SAP NetWeaver 7.0 Support Package stack 02:</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>SAP_BASIS</TD><TD> 701</TD><TD> Support Package 02</TD></TR> <TR><TD>SAP_ABA</TD><TD> 701</TD><TD> Support Package 02</TD></TR> <TR><TD>SAP_BW</TD><TD> 701</TD><TD> Support Package 02</TD></TR> <TR><TD>PI_BASIS</TD><TD> 701</TD><TD> Support Package 02</TD></TR> </TABLE></UL> <p></p> <UL><LI><B>Component Support Packages that are also required for the delta upgrade to BI_CONT 704:</B></LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD></TD></TR> <TR><TD>BI_CONT</TD><TD> 703</TD><TD> Support Package 09</TD></TR> <TR><TD></TD></TR> <TR><TD>If you have not yet installed the required component Support Packages, you can include them in the installation or delta upgrade.</TD></TR> <TR><TD></TD></TR> <TR><TD>For the delta upgrade of BI_CONT 702 to BI_CONT 704, the BI_CONT 703 delta upgrade package SAPKIBIIIC must be made available and must be included in the delta upgrade.</TD></TR> <TR><TD></TD></TR> </TABLE></UL> <p></p> <UL><LI>BI_CONT 704 has been released with BI_CONT 704 SP01. You must include BI_CONT 704 Support Package 01 in the installation or the upgrade.<br /></LI></UL> <UL><LI>BI_CONT 704 must not be installed if BANK-ALYZE 50 has already been installed in the system. In this case, use BI_CONT 703.<br /><br />-----------------------------------------------------------</LI></UL><p></p> <OL><OL>a) Preparations</OL></OL> <UL><UL><LI>Download the installation or upgrade package from SAP Service Marketplace (quick link /INSTALLATIONS) into a temporary directory, or install the DVD&#x00A0;&#x00A0;with material number: <B>********</B>.<br />Installation:&#x00A0;&#x00A0;&lt;SAR archive&gt; = K-704IHINBICONT.SAR<br />Delta upgrade: &lt;SAR archive&gt; = K-704DHINBICONT.SAR</LI></UL></UL> <UL><UL><LI>Log on as one of the following users:<br />&lt;sid&gt;adm&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on UNIX,<br />&lt;SID&gt;OFR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on AS/400,<br />&lt;SID&gt;adm&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on Windows NT</LI></UL></UL> <UL><UL><LI>Switch to the transport directory of your SAP System. You can display the transport directory by calling transaction AL11 and selecting the DIR_TRANS parameter.</LI></UL></UL> <UL><UL><LI>Unpack the SAR archive &lt;SAR archive&gt; from the subdirectory /INST/DATA/ using the following statement:<br />SAPCAR -xvf /&lt;CD_DIR&gt;/INST/DATA/&lt;SAR archive&gt;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on UNIX,<br />SAPCAR '-xvf /QOPT/&lt;VOLID&gt;/INST/DATA/&lt;SAR archive&gt;'&#x00A0;&#x00A0;&#x00A0;&#x00A0;on AS/400,<br />SAPCAR -xvf &lt;CD_DRIVE&gt;:\\INST\\DATA\\&lt;SAR archive&gt;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on Windows NT.</LI></UL></UL> <UL><UL><LI>You must now be able to find the following file in the /EPS/in directory:<br />CSR0120031469_0033010.PAT (installation)<br />CSR0120031469_0033274.PAT (delta upgrade)</LI></UL></UL> <UL><UL><LI>Load the add-on package from there into your SAP system (transaction SPAM -&gt; \"Support Package\"-&gt; \"Load Packages\"-&gt; \"From Front End\").</LI></UL></UL> <OL><OL>b) Performing the installation</OL></OL> <UL><UL><LI>Call transaction SAINT and choose 'Start'.</LI></UL></UL> <UL><UL><LI>If you want to use the option of an import with several background processes, proceed as described in Note 705192.</LI></UL></UL> <UL><UL><LI>Select the BI_CONT 704 add-on and choose 'Continue'. If all of the necessary conditions for importing the add-on have been fulfilled, the system will now display the relevant queue. The queue consists of the add-on package and can also contain Support Packages and other add-on packages. To start the installation process, choose \"Continue\".</LI></UL></UL> <UL><UL><LI>For more information, call transaction SAINT and choose \"Info\" on the application toolbar.</LI></UL></UL> <UL><UL><LI>The system prompts you to enter a password for BI_CONT 704. The password is: <B>1415818AD7</B>.<br /></LI></UL></UL> <OL><OL>c) Performing the delta upgrade</OL></OL> <b></b><br /> <b><B> I) Including BI_CONT 704 in an Enhancement Package upgrade  with SAPehpi</B></b><br /> <UL><UL><LI>If you do not want to install an Enhancement Package and you are planning a pure delta upgrade with SAINT, ignore this section and continue reading under point II).</LI></UL></UL> <UL><UL><LI>The general procedure for Enhancement Packages is described in the Enhancement Package Installation Guide.</LI></UL></UL> <UL><UL><LI>In the phase IS_SELECT, you will be asked for a decision about dealing with the add-on BI_CONT.</LI></UL></UL> <UL><UL><LI>Here, select: \"Upgrade with SAINT package\".</LI></UL></UL> <b></b><br /> <b><B> II) Delta upgrade after BI_CONT 704 with SAINT</B></b><br /> <UL><UL><LI>Call transaction SAINT and choose 'Start'.</LI></UL></UL> <UL><UL><LI>If you want to use the option of an import with several background processes, proceed as described in Note 705192.</LI></UL></UL> <UL><UL><LI>Select the BI_CONT 704 add-on and choose 'Continue'. If all of the necessary conditions for importing the add-on have been fulfilled, the system will now display the relevant queue. The queue consists of the add-on package and can also contain Support Packages and other add-on packages. To start the installation process, choose \"Continue\".</LI></UL></UL> <UL><UL><LI>For more information, call transaction SAINT and choose \"Info\" on the application toolbar.</LI></UL></UL> <UL><UL><LI>The system prompts you to enter a password for BI_CONT 704. The password is: <B>14158187D7</B><br /></LI></UL></UL> <OL>1. Upgrades</OL> <OL><OL>a) Prerequisite</OL></OL> <UL><UL><LI>Import the latest SPAM/SAINT update.</LI></UL></UL> <UL><UL><LI>The upgrades of the following BW releases are supported:<br />BW 20B, 21C, 30B, 310 (BI_CONT 330), 350 (BI_CONT 353)</LI></UL></UL> <OL><OL>b) Preparation</OL></OL> <UL><UL><LI>Download the upgrade package from SAP Service Marketplace (quick link INSTALLATIONS) to a temporary directory, or install the CD with the material number: <B>********</B>. The add-on upgrade package is unpacked automatically from the CD. To do this, in the UPLOAD_REQUEST phase, specify the directory under which you want to mount the Add-On upgrade CD.<br />If you downloaded the add-on upgrade package directly from SAP Service Marketplace, unpack it in the &lt;DIR_EPS_ROOT&gt;/in directory.<br />Information: The CSR0120031469_0033131.PAT file should be in the &lt;DIR_EPS_ROOT&gt;/in directory (at the latest after the phase UPLOAD_REQUEST).</LI></UL></UL> <p></p> <OL><OL>c) Additional information about PREPARE</OL></OL> <UL><UL><LI>IS_SELECT phase: Answer the question for BI_CONT with 'Upgrade with SAINT package'.</LI></UL></UL> <UL><UL><LI>Phase PATCH_CHK: Include the available Add-On Support Packages in the upgrade.<br />BI_CONT 704 has been released with BI_CONT 704 SP01. You must include BI_CONT 704 Support Package 01 in the upgrade.</LI></UL></UL> <OL><OL>d) Additional information about the upgrade</OL></OL> <OL><OL>e) Known errors<br /><br />1) When you perform an upgrade to ECC 6.0 (with or without an Enhancement Package) that includes the BI_CONT 704 upgrade package SAPK-704UHINBICONT, the following objects are deleted:<br /><br />LIMU FUNC CONVERSION_EXIT_MM_TT_INPUT<br />LIMU FUNC CONVERSION_EXIT_MM_TT_OUTPUT<br />R3TR DTEL  MONTH<br />R3TR DOMA  MONTH<br /><br />This may cause a termination during the upgrade, and causes inconsistencies after the upgrade in the function group E00C and in tables that use the data element MONTH.<br />This problem does not affect new installations and delta upgrades of BI_CONT 704 or upgrades from other servers (NetWeaver, BW, SCM, Solution Manager). Therefore, you must perform the following actions only if you want to perform an upgrade to ECC 6.0 (with or without an Enhancement Package) that includes the BI_CONT 704 upgrade package SAPK-704UHINBICONT.<br />If you include SAP_HR 6.00 Support Package 24 in the upgrade, you do not have to execute actions I) and II) either. Instead, proceed as described above under point 2) and include SAPK-704C1INBICONT. To restore the two deleted function modules, you must still execute action III).<br /><br />1) To avoid a termination during the upgrade, you must repair the domain MONTH and the data element MONTH before you start the upgrade. For example, add a blank character to the short text. Save the change and activate the domain and the data element. Then release the transport request that is created.<br /><br />Before the DDIC activation, the system requests that you use transaction SPDD to compare the domain MONTH and the data element MONTH. Before you perform the upgrade, transfer the version that you created during the repair. Activate the version and continue the upgrade.<br />After the upgrade, import the transport K7FK000578 into your system. The data and the cofile are provided in the attachment K7FK000578.SAR.<br />The transport contains the four deleted objects.<br /><br />II) If you have already started an upgrade without performing the actions mentioned above under point I) and the upgrade has therefore terminated during the DDIC activation phase, proceed as follows:<br />Create the domain and<br />the data element MONTH with the attributes mentioned below in the shadow instance of your system. Start the<br />upgrade again after you do this.<br /><br /><B>Domain  MONTH</B><br />Short description Month<br />Attributes tab<br />Package  PCAL<br />Original language EN<br /><br />Definition tab<br />Data type NUMC<br />No. Characters 2<br />Decimal places 0<br />Output length  2<br /><br />Value range tab<br />Individual values<br />Fixed value Short description<br />01  January<br />02  February<br />03  March<br />04 April<br />05  May<br />06  June<br />07  July<br />08  August<br />09 September<br />10  October<br />11  November<br />12  December<br /><br /><br /><B>Data element MONTH</B><br />Short description Month<br /><br />Attributes tab<br />Package  PCAL<br />Original language EN<br /><br />Data type tab<br />X Elementary type<br />&#x00A0;&#x00A0;X Domain MONTH<br /><br />Field label Length Field Label<br />Short  10 Month<br />Medium  15 Month<br />Long 20 Month<br />Heading 2 Mo<br /><br />III) After the upgrade, import the transport K7FK000578 into your system. The data and the cofile are provided in the attachment K7FK000578.SAR.<br />The transport contains the four deleted objects.<br /><br />2) In the following cases, the system reports conflicts between SAP_HR and BI_CONT.<br /> - Upgrade to ECC 6.0 for which the BI_CONT 704 upgrade package SAPK-704UHINBICONT and SAP_HR 6.00 Support Package 24 (SAPKE60024) are included<br /><br />Solution:<br />Include the package SAPK-704C1INBICONT (BI_CONT 704: CRT for SAPKE60024) in the installation queue.<br /><br />3) In the following cases, the system reports conflicts between IS-UT and BI_CONT.<br /> - Upgrade to ECC 6.0 for which the BI_CONT 704 upgrade package SAPK-704UHINBICONT and IS-UT 603 Enhancement Package 3 (SAPK-603DHINISUT) are included<br /><br />Solution:<br />Include IS-UT 603 Support Package 06 (SAPK-60306INISUT) or IS-UT 604 Support Package 06 (SAPK-60406INISUT).</OL></OL> <OL><OL>f) The password in the phase KEY_CHK is <B>2489078</B>.</OL></OL> <OL>2. Postprocessing the BI_CONT 704 installation/upgrade</OL> <UL><LI>The current support packages for BI_CONT 704 are available on SAP Service Marketplace under the quick link /patches.</LI></UL> <UL><LI>In addition to German and English, BI_CONT 704 supports the following languages: BG, CS, DA, EL, ES, FI, FR, HE, HR, HU, IT, JA, KO, NL, NO, PL, PT, RO, RU, SK, SL, SV, TR, ZF, ZH.<br />No further postprocessing is necessary for any languages that were already installed as a default language before the add-on installation. If you wish to install one of these languages at a later stage, proceed as follows: You must install the desired default language before you install the add-on language file. Perform the add-on language import in accordance with Note 195442.</LI></UL> <UL><UL><LI>When you import language packages, errors may occur in the method execution (\"OBJECTS_NOT_CHARLIKE\") if the system has NW04s Support Package Level 07. The error symptom and the solution are described in Note <B>925752</B>.</LI></UL></UL> <UL><LI>If you use budget planning and the budget monitor (transaction UPARI_BUDGA) Business Content functions in the retail area, carry out the postprocessing steps described in Note 897072.</LI></UL> <UL><LI>If you import SAP_HR 6.00 Support Package 24 (SAPKE60024) in a system for which an upgrade was performed that included the BI_CONT 704 upgrade package SAPK-704UHINBICONT, the system reports conflicts between SAP_HR and BI_CONT.<br />Solution:<br />Include the package SAPK-704C1INBICONT (BI_CONT 704: CRT for SAPKE60024) in the installation queue.<br /></LI></UL> <UL><LI>If you install an IS-UT Enhancement Package and 603 Enhancement Package 3 (SAPK-603DHINISUT) is included in the installation queue and an upgrade that included BI_CONT 704 upgrade package SAPK-704UHINBICONT was previously performed for the system, it reports conflicts between IS-UT and BI_CONT.<br />Solution:<br />Include IS-UT 603 Support Package 06 (SAPK-60306INISUT) or IS-UT 604 Support Package 06 (SAPK-60406INISUT).</LI></UL> <p><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D038076)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D028323)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001172899/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001172899/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001172899/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001172899/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001172899/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001172899/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001172899/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001172899/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001172899/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "K7FK000578.SAR", "FileSize": "34", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000270512008&iv_version=0019&iv_guid=11159EA77ACC6A46B0B511D226C7B78C"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "916834", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/916834"}, {"RefNumber": "153967", "RefComponent": "BW-BCT-GEN", "RefTitle": "BI Content Release Strategy", "RefUrl": "/notes/153967"}, {"RefNumber": "1275142", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1275142"}, {"RefNumber": "1254946", "RefComponent": "BC-UPG-ADDON", "RefTitle": "BI_CONT 704:Information on Add-On Support Packages", "RefUrl": "/notes/1254946"}, {"RefNumber": "1000822", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Overview: SAP Notes for the add-ons BI_CONT and BI_CONT_XT", "RefUrl": "/notes/1000822"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "153967", "RefComponent": "BW-BCT-GEN", "RefTitle": "BI Content Release Strategy", "RefUrl": "/notes/153967 "}, {"RefNumber": "1000822", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Overview: SAP Notes for the add-ons BI_CONT and BI_CONT_XT", "RefUrl": "/notes/1000822 "}, {"RefNumber": "1254946", "RefComponent": "BC-UPG-ADDON", "RefTitle": "BI_CONT 704:Information on Add-On Support Packages", "RefUrl": "/notes/1254946 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BI_CONT", "From": "704", "To": "704", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}