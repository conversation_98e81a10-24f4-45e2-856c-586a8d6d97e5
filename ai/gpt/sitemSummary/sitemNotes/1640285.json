{"Request": {"Number": "1640285", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1029, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000009739002017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001640285?language=E&token=4C38949C6077C72D86D65B770B3C7A41"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001640285", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001640285/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1640285"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "07.01.2021"}, "SAPComponentKey": {"_label": "Component", "value": "BC-SEC-VIR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Anti Virus Protection"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Security - Read KBA 2985997 for subcomponents", "value": "BC-SEC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Anti Virus Protection", "value": "BC-SEC-VIR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC-VIR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1640285 - Determine MIME type with Virus Scan Interface"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>*********************************************************************<br />Do not implement this SAP Note manually unless SAP requests this explicitly or this note is a prerequisite for the implementation of another SAP Note.<br /><br />Manual implementation of this SAP Note requires SAP Note 1669429.<br />*********************************************************************<br /><br />Uploading or downloading binary files with unknown content might be problematic if the files are opended by other clients.<br /><br />In cases where you trust the file extension, wrong MIME types or generic MIME types, e.g. \"application/octet-stream\" may lead to so-called mime-sniffing when the files are downloaded by browsers.<br /><br />Additionally you may want to configure that active content in files (for example macros or scripts) should be blocked generally, not only when the scanner classifies them as harmful.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>VSI, CL_VSI, VSIService, Virus Scan Interface, NW-VSI, VSA, MIME.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Prerequisite for the configuration in ABAP is SAP Note 1669429.<br /><br />External Prerequisites (see note 1494278):</p>\r\n<ul>\r\n<li>The active content detection is available in the product you have in use.</li>\r\n</ul>\r\n<ul>\r\n<li>The external product, which you use via VSI, returns the detected MIME type.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The Virus Scan Interface (VSI) uses capabilities of the underlying virus scanners to filter and detect files based on active content and MIME types.</p>\r\n<ul>\r\n<li>Enhanced Maintenance Capabilities (ABAP only)<br /><br />In the customizing of the virus scan profiles (view cluster VSCAN_PROFILE_VC, shortcut transaction VSCANPROFILE), the following new elements are present:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>New maintenance level \"Profile Configuration Parameters\"<br /><br />In this section, you can maintain the parameters whose name starts with \"CUST_\" and which are partly explained below. These parameters apply to the profile in total.<br /><br />The former location where those parameters could be maintained was underneath the \"Steps\" and was logically incorrect for \"CUST_\"-parameters (although, in the frequent case that only a single step was defined, equivalent). This section still exists and is now renamed to \"Step Configuration Parameters\".<br /><br />The section \"Profile Configuration Parameters\" is evaluated only when you set the indicator \"Evaluate Profile Configuration Parameters\" in the profile header. This indicator is set by default for new profiles and new installations, but remains unset for existing profiles during update or patch implementation for compatibility reasons.<br /><br />When profiles are nested either by using a reference profile or profiles as steps, the profile configuration parameters and the MIME-Type list (see below) are evaluated only for the first profile in the chain that has the \"Evaluate...\" parameter set and then propagated down to the scanner engine.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>New maintenance level \"MIME-Types\"<br /><br />In this section you can maintain a list of MIME types that shall act as either include list or exclude list for scans executed for this profile.<br /><br />The evaluation of this section is only enabled when you have maintained the parameter CUST_CHECK_MIME_TYPE with value \"1\" in the profile configuration parameters.<br /><br />In this case, the data maintained in this section overwrite (without merge) the data maintained in the scan-parameters SCANMIMETYPES and BLOCKMIMETYPES if those are maintained at group or step level.<br /><br />To make the maintained list of MIME-Types a exclude list, set the profile configuration parameter CUST_MIME_TYPES_ARE_BLACKLIST with value \"1\" in addition.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>New parameters for VSI configuration</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>CUST_ACTIVE_CONTENT<br /><br />If set to \"1\", all files with active content, for example HTML with JavaScript or PDF with JavaScript or office documents with macros will be blocked.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>CUST_CHECK_MIME_TYPE<br /><br />If set to \"1\", the external VSA must return the content info (MIME type) of the scanned object. The VSA must know the parameters SCANMIMETYPES, SCANEXTENSIONS, BLOCKEXTENSIONS and BLOCKMIMETYPES.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>CUST_MIME_TYPES_ARE_BLACKLIST (only ABAP)<br /><br />If set to \"1\", the defined MIME type are not used as include list but as exclude list, which means all MIME types in the defined list should be blocked.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>BLOCKEXTENSIONS<br /><br />This parameter must be known in the VSA. The string specifies a list of file extensions, which must be blocked.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>BLOCKMIMETYPES<br /><br />This parameter must be known in the VSA. The string specifies a list of MIME types, which must be blocked.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Meaning of existing VSI parameters</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SCANMIMETYPES<br /><br />This parameter must be known in the VSA. The string specifies a list of MIME types, which are allowed to be passed, all other types must be blocked.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SCANEXTENSIONS<br /><br />This parameter must be known in the VSA. The string specifies a list of file extensions, which are allowed to pass, all other types must be blocked.</li>\r\n</ul>\r\n</ul>\r\n<p><br />The extension is integrated in VSI version 1.8.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-SEC (Security - Read KBA 2985997 for subcomponents)"}, {"Key": "DownPort/UpPort-WF", "Value": "DownPort check necessary"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D067847)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D065374)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001640285/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001640285/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001640285/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001640285/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001640285/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001640285/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001640285/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001640285/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001640285/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "851789", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/851789"}, {"RefNumber": "786179", "RefComponent": "BC-SEC-VIR", "RefTitle": "Data security products: Use in the antivirus area", "RefUrl": "/notes/786179"}, {"RefNumber": "1883929", "RefComponent": "BC-SEC-VIR", "RefTitle": "Correction of VSI call in HTTP Up- and  Download", "RefUrl": "/notes/1883929"}, {"RefNumber": "1753745", "RefComponent": "KM-KW", "RefTitle": "Virus scan profile: Enhancement for SAP KW", "RefUrl": "/notes/1753745"}, {"RefNumber": "1728283", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 721: General Information", "RefUrl": "/notes/1728283"}, {"RefNumber": "1713949", "RefComponent": "BC-JAS-SEC", "RefTitle": "Virus Scan service fails with null or empty scan data", "RefUrl": "/notes/1713949"}, {"RefNumber": "1703398", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1703398"}, {"RefNumber": "1699357", "RefComponent": "CA-GTF-IC-CHA", "RefTitle": "Connection to Virus Scan Interface in CA-GTF-IC-CHA", "RefUrl": "/notes/1699357"}, {"RefNumber": "1669429", "RefComponent": "BC-SEC", "RefTitle": "VSI customizing user-interface changes for MIME-check", "RefUrl": "/notes/1669429"}, {"RefNumber": "1656521", "RefComponent": "EP-KM-CM", "RefTitle": "Enable application specific virus scan profiles in KMC area", "RefUrl": "/notes/1656521"}, {"RefNumber": "1642209", "RefComponent": "BC-SEC-VIR", "RefTitle": "Content-Check during file exchange", "RefUrl": "/notes/1642209"}, {"RefNumber": "1494278", "RefComponent": "BC-SEC-VIR", "RefTitle": "NW-VSI: Summary of Virus Scan Adapter´s for SAP integration", "RefUrl": "/notes/1494278"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2434380", "RefComponent": "BC-SRV-KPR-CMS", "RefTitle": "How to configure KPRO Virus Scan Profile for /SCMS/KPRO_CREATE", "RefUrl": "/notes/2434380 "}, {"RefNumber": "1728283", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 721: General Information", "RefUrl": "/notes/1728283 "}, {"RefNumber": "1917129", "RefComponent": "BC-SEC-VIR", "RefTitle": "Correction of VSI call in GUI Up- and  Download", "RefUrl": "/notes/1917129 "}, {"RefNumber": "1753745", "RefComponent": "KM-KW", "RefTitle": "Virus scan profile: Enhancement for SAP KW", "RefUrl": "/notes/1753745 "}, {"RefNumber": "1883929", "RefComponent": "BC-SEC-VIR", "RefTitle": "Correction of VSI call in HTTP Up- and  Download", "RefUrl": "/notes/1883929 "}, {"RefNumber": "1494278", "RefComponent": "BC-SEC-VIR", "RefTitle": "NW-VSI: Summary of Virus Scan Adapter´s for SAP integration", "RefUrl": "/notes/1494278 "}, {"RefNumber": "786179", "RefComponent": "BC-SEC-VIR", "RefTitle": "Data security products: Use in the antivirus area", "RefUrl": "/notes/786179 "}, {"RefNumber": "1656521", "RefComponent": "EP-KM-CM", "RefTitle": "Enable application specific virus scan profiles in KMC area", "RefUrl": "/notes/1656521 "}, {"RefNumber": "1713949", "RefComponent": "BC-JAS-SEC", "RefTitle": "Virus Scan service fails with null or empty scan data", "RefUrl": "/notes/1713949 "}, {"RefNumber": "1642209", "RefComponent": "BC-SEC-VIR", "RefTitle": "Content-Check during file exchange", "RefUrl": "/notes/1642209 "}, {"RefNumber": "1669429", "RefComponent": "BC-SEC", "RefTitle": "VSI customizing user-interface changes for MIME-check", "RefUrl": "/notes/1669429 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ENGINEAPI", "From": "7.30", "To": "7.30", "Subsequent": ""}, {"SoftwareComponent": "ENGINEAPI", "From": "7.31", "To": "7.31", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "6.40", "To": "6.40EX2", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.00", "To": "7.01", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.10", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "6.40", "To": "6.40EX2", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.00", "To": "7.01", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.10", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "6.40", "To": "6.40EX2", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.00", "To": "7.01", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.10", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "6.40", "To": "6.40EX2", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.00", "To": "7.01", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.10", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "8.00", "To": "8.00", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "8.02", "To": "8.02", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "8.03", "To": "8.03", "Subsequent": ""}, {"SoftwareComponent": "SAP-JEE", "From": "6.40", "To": "6.40", "Subsequent": ""}, {"SoftwareComponent": "SAP-JEE", "From": "7.00", "To": "7.02", "Subsequent": "X"}, {"SoftwareComponent": "BC-SEC-VIR", "From": "7.00", "To": "7.00", "Subsequent": ""}, {"SoftwareComponent": "BC-SEC-VIR", "From": "7.10", "To": "7.10", "Subsequent": ""}, {"SoftwareComponent": "BC-SEC-VIR", "From": "7.01", "To": "7.01", "Subsequent": ""}, {"SoftwareComponent": "BC-SEC-VIR", "From": "7.11", "To": "7.11", "Subsequent": ""}, {"SoftwareComponent": "BC-SEC-VIR", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "620", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "72L", "To": "802", "Subsequent": ""}, {"SoftwareComponent": "SAP-JEECOR", "From": "7.02", "To": "7.02", "Subsequent": ""}, {"SoftwareComponent": "J2EE-APPS", "From": "7.10", "To": "7.11", "Subsequent": ""}, {"SoftwareComponent": "J2EE-APPS", "From": "7.20", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "J2EE-APPS", "From": "7.30", "To": "7.30", "Subsequent": ""}, {"SoftwareComponent": "J2EE-APPS", "From": "7.31", "To": "7.31", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "6.40", "To": "6.40", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.00", "To": "7.01", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.10", "To": "7.11", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.20", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "8.00", "To": "8.03", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62072", "URL": "/supportpackage/SAPKB62072"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64030", "URL": "/supportpackage/SAPKB64030"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70027", "URL": "/supportpackage/SAPKB70027"}, {"SoftwareComponentVersion": "SAP_BASIS 701", "SupportPackage": "SAPKB70112", "URL": "/supportpackage/SAPKB70112"}, {"SoftwareComponentVersion": "SAP_BASIS 702", "SupportPackage": "SAPKB70212", "URL": "/supportpackage/SAPKB70212"}, {"SoftwareComponentVersion": "SAP_BASIS 702", "SupportPackage": "SAPKB70211", "URL": "/supportpackage/SAPKB70211"}, {"SoftwareComponentVersion": "SAP_BASIS 710", "SupportPackage": "SAPKB71015", "URL": "/supportpackage/SAPKB71015"}, {"SoftwareComponentVersion": "SAP_BASIS 711", "SupportPackage": "SAPKB71110", "URL": "/supportpackage/SAPKB71110"}, {"SoftwareComponentVersion": "SAP_BASIS 720", "SupportPackage": "SAPKB72008", "URL": "/supportpackage/SAPKB72008"}, {"SoftwareComponentVersion": "SAP_BASIS 730", "SupportPackage": "SAPKB73008", "URL": "/supportpackage/SAPKB73008"}, {"SoftwareComponentVersion": "SAP_BASIS 731", "SupportPackage": "SAPKB73102", "URL": "/supportpackage/SAPKB73102"}, {"SoftwareComponentVersion": "SAP_BASIS 731", "SupportPackage": "SAPKB73103", "URL": "/supportpackage/SAPKB73103"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "J2EE ENGINE APPLICATIONS 7.31", "SupportPackage": "SP004", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200014361&support_package=SP004&patch_level=000000"}, {"SoftwareComponentVersion": "J2EE ENGINE APPLICATIONS 7.31", "SupportPackage": "SP003", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200014361&support_package=SP003&patch_level=000000"}, {"SoftwareComponentVersion": "J2EE ENGINE APPLICATIONS 7.31", "SupportPackage": "SP002", "SupportPackagePatch": "000002", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200014361&support_package=SP002&patch_level=000002"}, {"SoftwareComponentVersion": "J2EE ENGINE APPLICATIONS 7.20", "SupportPackage": "SP008", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200615320200013040&support_package=SP008&patch_level=000000"}, {"SoftwareComponentVersion": "J2EE ENGINE APPLICATIONS 7.30", "SupportPackage": "SP008", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200615320200014992&support_package=SP008&patch_level=000000"}, {"SoftwareComponentVersion": "SAP J2EE ENGINE 7.01", "SupportPackage": "SP012", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200615320200010756&support_package=SP012&patch_level=000000"}, {"SoftwareComponentVersion": "SAP J2EE ENGINE 7.02", "SupportPackage": "SP012", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200615320200012549&support_package=SP012&patch_level=000000"}, {"SoftwareComponentVersion": "J2EE ENGINE APPLICATIONS 7.31", "SupportPackage": "SP001", "SupportPackagePatch": "000002", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200014361&support_package=SP001&patch_level=000002"}, {"SoftwareComponentVersion": "SAP KERNEL 8.03 64-BIT UNICODE", "SupportPackage": "SP011", "SupportPackagePatch": "000011", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200016159&V=MAINT"}, {"SoftwareComponentVersion": "J2EE ENGINE APPLICATIONS 7.11", "SupportPackage": "SP010", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200006975&support_package=SP010&patch_level=000000"}, {"SoftwareComponentVersion": "J2EE ENGINE APPLICATIONS 7.10", "SupportPackage": "SP015", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200004740&support_package=SP015&patch_level=000000"}, {"SoftwareComponentVersion": "SAP J2EE ENGINE 7.00", "SupportPackage": "SP027", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200004229&support_package=SP027&patch_level=000000"}, {"SoftwareComponentVersion": "SAP J2EE ENGINE 6.40", "SupportPackage": "SP030", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200615320200006685&support_package=SP030&patch_level=000000"}, {"SoftwareComponentVersion": "SAP KERNEL 8.02 64-BIT UNICODE", "SupportPackage": "SP033", "SupportPackagePatch": "000033", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200015012&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT", "SupportPackage": "SP121", "SupportPackagePatch": "000121", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013053&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT UNICODE", "SupportPackage": "SP121", "SupportPackagePatch": "000121", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013054&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT", "SupportPackage": "SP121", "SupportPackagePatch": "000121", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013055&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT UNICODE", "SupportPackage": "SP121", "SupportPackagePatch": "000121", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013056&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 8.00 64-BIT UNICODE", "SupportPackage": "SP056", "SupportPackagePatch": "000056", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200017704&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT UNICODE", "SupportPackage": "SP397", "SupportPackagePatch": "000397", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004051&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT UNICODE", "SupportPackage": "SP397", "SupportPackagePatch": "000397", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004052&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT", "SupportPackage": "SP397", "SupportPackagePatch": "000397", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006931&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT", "SupportPackage": "SP397", "SupportPackagePatch": "000397", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006932&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT", "SupportPackage": "SP397", "SupportPackagePatch": "000397", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009586&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT UC", "SupportPackage": "SP397", "SupportPackagePatch": "000397", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009588&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT", "SupportPackage": "SP397", "SupportPackagePatch": "000397", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009590&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT UC", "SupportPackage": "SP397", "SupportPackagePatch": "000397", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009591&V=MAINT"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 14, "URL": "/corrins/0001640285/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_BASIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Basis compo...|<br/>| Release 620&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB62069 - SAPKB62071&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 640&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB64025 - SAPKB64029&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 700&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB70004 - SAPKB70026&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 710&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKB71014&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 711&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB71101 - SAPKB71109&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 701&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKB70111&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 702&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB70201 - SAPKB70210&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 730&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB73001 - SAPKB73007&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 720&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB72002 - SAPKB72007&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 731&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB73101 - SAPKB73102&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>For all newly created objects, choose package SVSCAN.<br/><br/>Activate each object after creation or change.<br/><br/>#######################################################################<br/>SE11, Domain, VSCAN_TEST_ACTION, Change.<br/>Value Range.<br/>New entries:<br/>&nbsp;&nbsp;MIMESCAN&nbsp;&nbsp;: Determine MIME content<br/>&nbsp;&nbsp;MIMECLEAN : Clean MIME content<br/>&nbsp;&nbsp;PROFILE&nbsp;&nbsp; : Use settings from profile<br/>#######################################################################<br/>SE11, Data type, VSCAN_BAL_CONTEXT, Create, Structure.<br/>Short Description: Structure for application log in profile maint.<br/>Components:<br/>&nbsp;&nbsp;PROFILE type VSCAN_PROFILE<br/>Enhancement Category: Cannot be enhanced.<br/>#######################################################################<br/>SE91, VSCAN, Change.<br/>Create the following new messages. Set indicator \"Self Explanatory\". Long texts will be delivered by support package only.<br/>083: Engine &amp;1 could not be started. Error: &amp;2&amp;3<br/>084: Content in object \"&amp;3&amp;4\" blocked, rule violation \"&amp;1&amp;2\"<br/>085: Active content in object \"&amp;3&amp;4\" blocked, rule violation \"&amp;1&amp;2\"<br/>086: Profile parameters exist, but evaluation not activated<br/>087: MIME types are maintained, but evaluation not activated<br/>088: Blacklist indicator only sensible with active MIME check<br/>089: Changes were not saved<br/>090: Profile is active without steps<br/>091: Profile is active and uses unsuitable reference profile<br/>092: Profile is active and has both reference profile and steps<br/>093: Step &amp;1 uses unsuitable profile<br/>094: Group &amp;1 from step &amp;2 has no operational scanner<br/>#######################################################################<br/>SE16, VSCAN_PARAM.<br/>Newly create the following entries:<br/>&nbsp;&nbsp;BLOCKEXTENSIONS, CHAR, List of blocked file extensions<br/>&nbsp;&nbsp;BLOCKMIMETYPES, CHAR, List of allowed MIME types<br/>&nbsp;&nbsp;INITDIRECTORY, IS_INIT, CHAR, Base directory for initialization<br/>&nbsp;&nbsp;INITENGINES, IS_INIT, CHAR, List of scan engines for initialization<br/>&nbsp;&nbsp;INITENGINEDIRECTORY, IS_INIT, CHAR, Directory of Engine<br/>Transport those new entries via SE16, Table Entry, Transport.<br/>#######################################################################<br/>SE24, CL_VSI, Change, Goto, Text Elements.<br/>000 (new, length 132): Content blocked by rules<br/>005 (change, length 132): At least one active content was found<br/>#######################################################################<br/>SE38, RSVSCANTEST, Text elements, Change.<br/>011 (new, length 20): Content information<br/>012 (new, length 30): File name extension<br/>013 (new, length 30): MIME Type<br/>#######################################################################<br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_BASIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Basis compo...|<br/>| Release 620&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB62068 - SAPKB62071&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 640&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB64025 - SAPKB64029&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 700&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB70004 - SAPKB70026&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 710&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKB71014&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 711&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB71101 - SAPKB71109&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 701&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKB70111&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 702&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB70201 - SAPKB70210&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 730&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB73001 - SAPKB73007&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 720&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB72002 - SAPKB72007&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 731&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB73101 - SAPKB73102&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>#######################################################################<br/>SE54, Edit View Cluster, VSCAN_PROFILE_VC, Create/Change.<br/>Events.<br/>Delete event 01.<br/>Create event 04 with FORM routine VSCAN_PROFILE_VC_SAVE.<br/>Save.<br/>Header entry.<br/>Activate.<br/>#######################################################################<br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 14, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 2, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 3, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "1598873 ", "URL": "/notes/1598873 ", "Title": "SCAN-Methods in CL_VSI: SCANRC + CUST_NOT_SCANNED_AS_WARNING", "Component": "BC-SEC-VIR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "731", "Number": "1669429 ", "URL": "/notes/1669429 ", "Title": "VSI customizing user-interface changes for MIME-check", "Component": "BC-SEC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "1598873 ", "URL": "/notes/1598873 ", "Title": "SCAN-Methods in CL_VSI: SCANRC + CUST_NOT_SCANNED_AS_WARNING", "Component": "BC-SEC-VIR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1598873 ", "URL": "/notes/1598873 ", "Title": "SCAN-Methods in CL_VSI: SCANRC + CUST_NOT_SCANNED_AS_WARNING", "Component": "BC-SEC-VIR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1598873 ", "URL": "/notes/1598873 ", "Title": "SCAN-Methods in CL_VSI: SCANRC + CUST_NOT_SCANNED_AS_WARNING", "Component": "BC-SEC-VIR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1598873 ", "URL": "/notes/1598873 ", "Title": "SCAN-Methods in CL_VSI: SCANRC + CUST_NOT_SCANNED_AS_WARNING", "Component": "BC-SEC-VIR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1640285 ", "URL": "/notes/1640285 ", "Title": "Determine MIME type with Virus Scan Interface", "Component": "BC-SEC-VIR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "710", "ValidTo": "710", "Number": "1598873 ", "URL": "/notes/1598873 ", "Title": "SCAN-Methods in CL_VSI: SCANRC + CUST_NOT_SCANNED_AS_WARNING", "Component": "BC-SEC-VIR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "711", "ValidTo": "711", "Number": "1598873 ", "URL": "/notes/1598873 ", "Title": "SCAN-Methods in CL_VSI: SCANRC + CUST_NOT_SCANNED_AS_WARNING", "Component": "BC-SEC-VIR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "720", "ValidTo": "720", "Number": "1598873 ", "URL": "/notes/1598873 ", "Title": "SCAN-Methods in CL_VSI: SCANRC + CUST_NOT_SCANNED_AS_WARNING", "Component": "BC-SEC-VIR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1598873 ", "URL": "/notes/1598873 ", "Title": "SCAN-Methods in CL_VSI: SCANRC + CUST_NOT_SCANNED_AS_WARNING", "Component": "BC-SEC-VIR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "1640285 ", "URL": "/notes/1640285 ", "Title": "Determine MIME type with Virus Scan Interface", "Component": "BC-SEC-VIR"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1796762", "RefTitle": "NW-VSI 2.0: Error when loading external adapters", "RefUrl": "/notes/0001796762"}, {"RefNumber": "1947895", "RefTitle": "Invalid data when activating VSI profile /SIHTTP/HTTP_UPLOAD", "RefUrl": "/notes/0001947895"}]}}}}}