{"Request": {"Number": "945653", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 545, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005557622017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000945653?language=E&token=B83C79F98A1238FD36213053C60744B3"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000945653", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000945653/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "945653"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 18}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "31.07.2006"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-OT-OLAP-VAR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Using Variables"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Explorer", "value": "BW-BEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "OLAP Technology", "value": "BW-BEX-OT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Analyzing Data", "value": "BW-BEX-OT-OLAP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT-OLAP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Using Variables", "value": "BW-BEX-OT-OLAP-VAR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT-OLAP-VAR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "945653 - Improvement to variable screen in 3.x BEx Analyzer II"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>One of the following problems occurs:</p> <UL><LI>If you enter invalid values in a merged variable, the system issues an error message which means that the variable screen can still only be closed by a termination.</LI></UL> <UL><LI>A variable in the default value of the query (can be changed during query navigation) with a mandatory entry overwrites the empty filter of a bookmark.</LI></UL> <UL><LI>When you refresh a workbook by reusing the variables, variables in the default value of the query (can be changed during query navigation) are displayed again.</LI></UL> <p></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>BW 3.x BEx Analyzer, RSRT, SAP GUI<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><UL><LI>SAP NetWeaver 2004s BI</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 09 for SAP NetWeaver 2004s BI (BI Patch 09 or <B> SAPKW70009</B>) into your BI system. The Support Package is available once <B> Note 914303</B> \"SAPBINews BI 7.0 Support Package 09\", which describes this Support Package in more detail, has been released for customers.<br /><br />In urgent cases, you can implement the correction instructions.<br />To provide information in advance, the notes mentioned above may already be available before the Support Packages are released. In this case, the short text of the note still contains the words \"Preliminary version\".</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BEX-OT-F4 (MasterData read services)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D027464)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D027464)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000945653/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000945653/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000945653/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000945653/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000945653/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000945653/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000945653/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000945653/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000945653/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "955990", "RefComponent": "BW", "RefTitle": "BI in SAP NetWeaver 7.0: Incompatibilities with SAP BW 3.x", "RefUrl": "/notes/955990"}, {"RefNumber": "953402", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Variables are displayed when you refresh all queries", "RefUrl": "/notes/953402"}, {"RefNumber": "930923", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Refresh query with variable values and display hierarchy", "RefUrl": "/notes/930923"}, {"RefNumber": "924316", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Variable screen in the 3.x BEx Analyzer", "RefUrl": "/notes/924316"}, {"RefNumber": "914303", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews NW BW 7.00 ABAP SP9", "RefUrl": "/notes/914303"}, {"RefNumber": "1337977", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "3.x analyzer and 7.x backend : history is not supported", "RefUrl": "/notes/1337977"}, {"RefNumber": "1008222", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "3.x Analyzer: Changeable exit variable w/o user entry", "RefUrl": "/notes/1008222"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "924316", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Variable screen in the 3.x BEx Analyzer", "RefUrl": "/notes/924316 "}, {"RefNumber": "1337977", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "3.x analyzer and 7.x backend : history is not supported", "RefUrl": "/notes/1337977 "}, {"RefNumber": "955990", "RefComponent": "BW", "RefTitle": "BI in SAP NetWeaver 7.0: Incompatibilities with SAP BW 3.x", "RefUrl": "/notes/955990 "}, {"RefNumber": "914303", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews NW BW 7.00 ABAP SP9", "RefUrl": "/notes/914303 "}, {"RefNumber": "1008222", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "3.x Analyzer: Changeable exit variable w/o user entry", "RefUrl": "/notes/1008222 "}, {"RefNumber": "953402", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Variables are displayed when you refresh all queries", "RefUrl": "/notes/953402 "}, {"RefNumber": "930923", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Refresh query with variable values and display hierarchy", "RefUrl": "/notes/930923 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "710", "To": "710", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW 700", "SupportPackage": "SAPKW70009", "URL": "/supportpackage/SAPKW70009"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "NumberOfCorrin": 1, "URL": "/corrins/0000945653/654"}, {"SoftwareComponent": "SAP_BW", "NumberOfCorrin": 11, "URL": "/corrins/0000945653/30"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 12, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 12, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "918598 ", "URL": "/notes/918598 ", "Title": "Error in variable screen if authorization S_BDS_D is missing", "Component": "BW-BEX-OT-OLAP-VAR"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "919052 ", "URL": "/notes/919052 ", "Title": "Variables in the default value and >, <, >=, <= or Excl.", "Component": "BW-BEX-OT-OLAP-VAR"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "921437 ", "URL": "/notes/921437 ", "Title": "Improvements for the variable screen in RSRT", "Component": "BW-BEX-OT-OLAP-VAR"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "923837 ", "URL": "/notes/923837 ", "Title": "Enterable text variable is not replaced correctly", "Component": "BW-BEX-OT-OLAP-VAR"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "924316 ", "URL": "/notes/924316 ", "Title": "Variable screen in the 3.x BEx Analyzer", "Component": "BW-BEX-OT-OLAP-VAR"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "924999 ", "URL": "/notes/924999 ", "Title": "Display hierarchy of a query is not used", "Component": "BW-BEX-OT-OLAP-HIER"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "930923 ", "URL": "/notes/930923 ", "Title": "Refresh query with variable values and display hierarchy", "Component": "BW-BEX-OT-OLAP-VAR"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "932807 ", "URL": "/notes/932807 ", "Title": "Variable with substitution from query and filter status", "Component": "BW-BEX-OT-OLAP-VAR"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "935216 ", "URL": "/notes/935216 ", "Title": "Error message with invalid variable hierarchy", "Component": "BW-BEX-OT-OLAP-VAR"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "942648 ", "URL": "/notes/942648 ", "Title": "No data with \"Contains Pattern\" - restriction", "Component": "BW-BEX-OT-OLAP-VAR"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "945653 ", "URL": "/notes/945653 ", "Title": "Improvement to variable screen in 3.x BEx Analyzer II", "Component": "BW-BEX-OT-OLAP-VAR"}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "ValidFrom": "700", "ValidTo": "700", "Number": "931972 ", "URL": "/notes/931972 ", "Title": "\"VARVALUE_\" text elements are not displayed", "Component": "BW-BEX-OT-OLAP"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}