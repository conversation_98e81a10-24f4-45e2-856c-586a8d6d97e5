{"Request": {"Number": "1425765", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 374, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016956302017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001425765?language=E&token=FC241B3F75471DCD358DC704A0A283AB"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001425765", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001425765/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1425765"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 19}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.11.2010"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CST-GW"}, "SAPComponentKeyText": {"_label": "Component", "value": "Gateway/CPIC"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Client/Server Technology", "value": "BC-CST", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CST*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Gateway/CPIC", "value": "BC-CST-GW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CST-GW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1425765 - Generating sec_info reg_info"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Transaction SMGW provides the option (based on the RFC destinations in the system) of creating a configuration help for the files that are defined with the parameters gw/reg_info and gw/sec_info. Call transaction SMGW and choose \"Goto -&gt; Expert Functions -&gt; External Security -&gt; Create\" reginfo or secinfo.<br /><br />In Kernel Release 46D (see the \"SP Patch Level\" tab page for the patch level), you can now also set gw/reg_info.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>reg_info, reginfo, sec_info, secinfo, display, gw/reg_no_conn_info</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><B>Reason:</B><br /><br />This facilitates the use of security features for starting and registering external programs to increase the system security. You can execute the function by calling transaction SMGW and choosing \"Goto -&gt; Expert Functions -&gt; External Security -&gt; Create\" reginfo (and/or secinfo).<br /><br />There is no relevant report for the file gw/prxy_info (only available as of Kernel Release 7.10) because there are no matches for it in the RFC destinations.<br /><br /><B>Prerequisites:</B><br /><br />All three of the following points must be met:</p> <UL><LI>Set the parameter gw/reg_no_conn_info to AT LEAST 1, but preferably to the maximum value (see Note 1444282).</LI></UL> <UL><LI>Import the required Enhancement Packages (see the \"Support Packages\" tab page).</LI></UL> <UL><LI>Import the required kernel level (see the \"SP Patch Level\" tab page).</LI></UL> <p><br />If the current levels of the Enhancement Packages AND kernel do not correspond with the required levels, you should create the secinfo and reginfo manually using the notes<br /><br /><B>1408081 Basic settings for reg_info and sec_info</B><br /><br /><br /><br />Additional information:<br /><br />614971 GW: Changes to the ACL list of the gateway (secinfo)<br />1069911 GW: Changes to the ACL list of the gateway (secinfo)<br />1444282 gw/reg_no_conn_info settings<br /><br /><br /><br />We strongly recommend that you import the required Support Package to have the necessary ABAP objects available. Although the following describes how you can import the required objects in the system using special transports, this leads to significant postprocessing during a release upgrade, among other things. This should be considered only as a last resort and you should preferably create the file manually in advance in accordance with Note 1408081.<br /><br />If you cannot avoid importing the special transport, the \"Attachments\" tab page of this note provides release-dependent ZIP files that contain the necessary objects.<br />Then proceed according to Notes 480180 and 13719 to download the data and import it in the system.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><b>A central file or a file for each application server?</b><br /> <p><br />First, you must consider whether each application server should have its OWN security settings or whether the same files and therefore rules should apply in the entire system.<br /><br />The advantage of a central file is that the system is easier to maintain.&#x00A0;&#x00A0;In this case, the parameters (UNIX syntax)<br /><br />gw/sec_info = $(DIR_GLOBAL)/secinfo<br />gw/reg_info = $(DIR_GLOBAL)/reginfo<br /><br />should be set.<br /><br />The disadvantage of a central file is that there may be large unclear files with many entries.<br /><br />The advantage of a file for each application server is better clarity of both files. Example: If a program that is to be registered registers only on one application server, the corresponding rule in a central reginfo file would also occur for all other application servers, although this does not have any effect.<br /><br /></p> <b>Creating a central file</b><br /> <p><br />In any case, the files are stored in the DIR_DATA directory (and that is the default case):<br /><br />gw/sec_info = $(DIR_DATA)/secinfo<br />gw/reg_info = $(DIR_DATA)/reginfo<br /><br />and/or<br /><br />gw/sec_info = /usr/sap/&lt;system name&gt;/&lt;instance name&gt;/secinfo<br />gw/reg_info = /usr/sap/&lt;system name&gt;/&lt;instance name&gt;/reginfo<br /><br />The rules for the default files are then defined FOR EACH APPLICATION SERVER. A proposal for a central file is always created if secinfo and reginfo are NOT in the DIR_DATA directory. Therefore, for example, as above:<br /><br />gw/sec_info = $(DIR_GLOBAL)/secinfo<br />gw/reg_info = $(DIR_GLOBAL)/reginfo<br /><br />DIR_GLOBAL is by definition a directory that can be accessed via a network. If you select another directory with the desire to generate a central file, all other application servers must naturally be able to read it also.<br /><br /></p> <b>Different security levels</b><br /> <p><br />The function is based on the entries for external programs as these were created via transaction SM59.<br /><br />\"Create secinfo\" determines proposals for starting external programs, which are defined as \"Start on Explicit Host\".<br /><br />Both other options:<br /><br />Start on Application Server and<br />Start on Front End<br /><br />are covered by the system rules on the one hand (see the following description). On the other hand, \"Start on Front End\" is always permitted from the gateway. For this, new security features exist for SAP GUI 720; these features permit the user to specifically request the start on the front end. For more information, see http://www.sdn.sap.com/irj/sdn/sap-gui and the SAP GUI Security Guide.<br /><br />First, <B>SYSTEM rules</B> are generated</p> <UL><LI>that permit the starting of an external program from an application server of your own system on an application server of your own system AND</LI></UL> <UL><LI>the registration of a server that comes from an application server of your own system and its use:</LI></UL> <p><br />P TP=* USER=* USER-HOST=internal HOST=internal&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; (secinfo) and<br />P TP=* USER=* HOST=internal CANCEL=internal ACCESS=internal (reginfo)<br /><br /><B><B>(The \"internal\" keyword is available only as of Kernel 720 patch level 54. Lower kernel releases contain a list of all IP addresses of all application servers that are currently active at</B><U><B>RUNTIME</B></U><B>. In kernel releases lower than 720, you must manually enhance application servers that were subsequently added.</B></B><br /><br /><B><B>This list of all IP addresses is abbreviated as &lt;LIST&gt; below and includes </B></B></p> <UL><LI><B><B>the application servers as well as the SAPDBHOST and the standalone enqueue server host (if it exists) in ABAP releases from Release 46D up to and including Release 711. </B></B></LI></UL><p><br /><br /><B>secinfo:</B><br /><br />The system then evaluates the SM59 entries for \"Start on Explicit Host\". For the program sapxpg to be started on the host \"Obelix\" (is triggered in an IP address in accordance with the local name resolution), for example, such a line has the following format:<br /><br />P TP=sapxpg USER=* USER-HOST=local HOST=***********<br /><br />The line implies that sapxpg on HOST *********** (Obelix) can be started only if the start request comes from the IP address of the local application server. In other words, sapxpg cannot be started from outside the system. (You must set gw/reg_no_conn_info to prevent the simulation of an incorrect IP address.)<br /><br /><B>Remark:</B> <B>Starting an external program from a host outside the system is to be assessed as particularly critical to security and you should therefore only differ from USER-HOST=local with caution.</B><br /><br /><B>reginfo:</B><br /><br />A two-step procedure applies for the default values created using \"Create reginfo\". In the first step, the system evaluates the information of transaction SM59 and creates the following entry (#VERSION=2):<br /><br />P TP=Dudel USER=* HOST=local CANCEL=local ACCESS=*<br /><br />HOST=local does not represent ANY restriction because the registration from the SYSTEM is permitted anyway using the <B>SYSTEM rules </B>(see <B>SYSTEM rules</B> above). As a result, this rule opens only the external use of the registered program \"Dudel\" for ALL IP addresses to ensure that no errors are created in a production system when proposals are activated.<br /><br />In the production operation, however, there are also many registered servers that connect from outside the system. In the second step, therefore, the system searches in the list of all active gateways in the system for the registered servers that are CURRENTLY active and also enters them with their CONNECT IP addresses in the list. (It is assumed that the active registered servers are logged on as usual to the system during the runtime of the program.) It is also important that all registered servers that are relevant for the production operation are active during the runtime of the program so that they can be entered as a rule. Such an ADDITIONAL runtime rule therefore contains the CONNECT IP address of the registered program and looks as follows:<br /><br />P TP=Dudel USER=* HOST=*********** CANCEL=local ACCESS=*<br /><br />and occurs possibly redundantly for the rule based on SM59.<br /><br /><B>Note:</B> In the ABAP releases up to and including Release 640, the parameter gw/reg_info for the definition of the file name is unknown in the standard system and is treated as an empty string in the program. If this is the case, a file \"reginfo\" is created in the same directory as defined by gw/sec_info.<br /><br /><br /><B>Procedure:</B><br /><br />We recommend the following procedure to enable as smooth a transition as possible:<br /><br />In the productive environment <B><B>without</B></B> secinfo and reginfo:<br /></p> <UL><UL><LI>from the completely \"open\" configuration (no secinfo and reginfo)</LI></UL></UL> <UL><UL><LI>to the secure configuration (activating the default values in secinfo and reginfo of both reports)</LI></UL></UL> <UL><UL><LI>and finally to the adjusted, secure configuration (manual adjustment of the USER-HOST tags in secinfo and HOST, ACCESS and CANCEL in reginfo). This requires administrator knowledge and cannot be automated.</LI></UL></UL> <p><br /><br />In the productive environment <B>with</B> existing secinfo and reginfo:<br /></p> <UL><LI>In this case, you should first revise the existing files. For the secinfo, this means that you should ensure that all lines contain ALL tags with which the additional restrictions can be met. On the one hand, this means that each should change to VERSION=2 and, for example, the <B>secinfo</B> line</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;P TP=Dudel USER=* HOST=***********<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;should be changed to<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;P TP=Dudel USER=* USER-HOST=* HOST=***********<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This does not change the semantics of the entry. <B>In this case, however, you should consider whether USER-HOST in particular could be restricted further (\"local\" or \"internal\").</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The same applies for the <B>reginfo</B> , that is, the following entry<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;P TP=Dudel USER=* HOST=Obelix<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;should be in at least<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;P TP=Dudel USER=* HOST=Obelix CANCEL=local ACCESS=*<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;where ACCESS should be restricted further.</p> <UL><LI>You should then check the default values of the two reports with the existing contents of the secinfo and reginfo to see whether they are COMPLETE, that is, are all program names entered, are there redundancies, which of them can be deleted?</LI></UL> <p></p> <b>User guidance for creating the secinfo and reginfo:</b><br /> <b></b><br /> <p>After you start transaction SMGW -&gt; \"Goto -&gt; Expert Functions -&gt; External Security -&gt; Create\" (reginfo and/or secinfo), the system first displays a list of all entries that were found in SM59 for starting external programs and/or for registered programs.<br /><br />In this list, you can or should now select the lines that should be transferred to the secinfo and/or reginfo file. Therefore, you should select all entries if you have no secinfo or reginfo file. If you created new entries only in SM59, you select only these or (only for reginfo) their counterparts in the lower part of the list where the program name is displayed again, but this time with the CONNECT IP address (see above).<br /><br />You have two options for working further with the selection. The system displays the pushbuttons \"Save Selected Entries in File\" and<br />\"Create ACL File\" (ACL = Access Control List).<br /></p> <UL><LI>\"Display ACL File\" reads the ACL from the current gateway <B>as this is currently active in the gateway process</B>, and displays it. (The ACL therefore does not have to correspond to the contents at file level. The file may have been changed since the gateway was started. See the following option \"Save Selected Entries in File\"). You can therefore use this pushbutton to check the selection for redundancy. You can use F3 to return to the selection and then choose the other pushbutton with the selection that still exists.</LI></UL> <UL><LI>\"Save Selected Entries in File\" leads to a new screen that displays in detail which lines are written into the secinfo and/or reginfo. (If there is no secinfo or reginfo, these entries are created in VERSION 2, that is, a leading \"P\" is inserted implicitly for PERMIT. If secinfo and reginfo already exist, they are set up in accordance with the existing VERSION regarding the insertion of a \"P\" or not.)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To confirm the selection, you must choose \"Save Entries in File?\". If the secinfo or reginfo already exists, the system displays a dialog box that you have the choice of <B>replacing</B> the existing file completely (that should be chosen only with caution) or of <B>inserting</B> the selected lines in the existing file (default).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The system then displays a dialog box that asks you whether the version saved in this manner by secinfo or reginfo is to be active soon in the live operation. The rules that were made in this way are valid immediately as a result.&#x00A0;&#x00A0;However, you should be aware of the fact that if it is <B>NOT</B> activated, the contents of the file at file level (display with OS means) do <B>NOT correspond</B> with the contents of the ACL file in the current gateway (transaction SMGW -&gt; \"Goto -&gt; Expert Functions -&gt; External Security -&gt; Create\" (reginfo or secinfo)) and leads more easily to incorrect interpretations as a result.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br /><br /></p> <b>Colored representation of the file contents:</b><br /> <p><br />When displaying the ACL that is currently valid (transaction SMGW -&gt; \"Goto -&gt; Expert Functions -&gt; External Security -&gt; Create\" (reginfo or secinfo)), lines are displayed as either</p> <UL><LI>Gray/white (OK),</LI></UL> <UL><LI>Yellow (warning) or</LI></UL> <UL><LI>Red (error)</LI></UL> <p><br />Incorrect lines (red) are not transferred to the live operation and lines with warnings (yellow) are transferred only in some cases. For more information with examples, see Note 1503858.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-SEC (Security - Read KBA 2985997 for subcomponents)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023620)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D023620)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001425765/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001425765/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001425765/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001425765/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001425765/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001425765/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001425765/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001425765/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001425765/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "SAP_BASIS_700_02.zip", "FileSize": "506", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000026662010&iv_version=0019&iv_guid=61225BF77A75FC4E877D56AF3551011F"}, {"FileName": "SAP_BASIS_720_02.zip", "FileSize": "562", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000026662010&iv_version=0019&iv_guid=D6E38B214153CA438A389CA828F330AD"}, {"FileName": "SAP_BASIS_710_02.zip", "FileSize": "497", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000026662010&iv_version=0019&iv_guid=21D0839CDF6BC04A815B246F52FB8E22"}, {"FileName": "SAP_BASIS_702_02.zip", "FileSize": "522", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000026662010&iv_version=0019&iv_guid=4CF0F03488292B4C83D7A4984D8B6DF3"}, {"FileName": "SAP_BASIS_620_02.zip", "FileSize": "262", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000026662010&iv_version=0019&iv_guid=B9E7E069554B7B4989DB3F7B87CA3217"}, {"FileName": "SAP_BASIS_640_02.zip", "FileSize": "379", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000026662010&iv_version=0019&iv_guid=5B9FA58D7A4FB24CAF93C650ED291136"}, {"FileName": "SAP_BASIS_711_02.zip", "FileSize": "482", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000026662010&iv_version=0019&iv_guid=E0D18A9123055C40A5D068CAC17ACEBC"}, {"FileName": "SAP_BASIS_46C_04.zip", "FileSize": "206", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000026662010&iv_version=0019&iv_guid=AAA17DD97584444589C4D2BDFB12F9CF"}, {"FileName": "SAP_BASIS_701_02.zip", "FileSize": "471", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000026662010&iv_version=0019&iv_guid=F5E6179CD1C7A244A8E2841B3B834E59"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "614971", "RefComponent": "BC-CST-GW", "RefTitle": "GW: Changes to the ACL list of the gateway (secinfo)", "RefUrl": "/notes/614971"}, {"RefNumber": "1689663", "RefComponent": "BC-CST-GW", "RefTitle": "GW: Simulation mode for reg, sec, and prxy_info", "RefUrl": "/notes/1689663"}, {"RefNumber": "1504652", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1504652"}, {"RefNumber": "1503858", "RefComponent": "BC-CST-GW", "RefTitle": "Colored lines for sec(-reg) info test in SMGW", "RefUrl": "/notes/1503858"}, {"RefNumber": "1499062", "RefComponent": "BC-CST-GW", "RefTitle": "Incomplete display of sec_info and reg_info", "RefUrl": "/notes/1499062"}, {"RefNumber": "1473017", "RefComponent": "BC-CST-GW", "RefTitle": "Uppercase/lowercase in the files reg_info and sec_info", "RefUrl": "/notes/1473017"}, {"RefNumber": "1444282", "RefComponent": "BC-CST-GW", "RefTitle": "gw/reg_no_conn_info settings", "RefUrl": "/notes/1444282"}, {"RefNumber": "1408081", "RefComponent": "BC-CST-GW", "RefTitle": "Basic settings for reg_info and sec_info", "RefUrl": "/notes/1408081"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1069911", "RefComponent": "BC-CST-GW", "RefTitle": "GW: Changes to the ACL list of the gateway (reginfo)", "RefUrl": "/notes/1069911"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2144082", "RefComponent": "BC-CTS-TLS", "RefTitle": "Transport error: User is not authorized to start tp", "RefUrl": "/notes/2144082 "}, {"RefNumber": "1851254", "RefComponent": "BC-CCM-MON", "RefTitle": "Gateway Errors for CCMS Agent", "RefUrl": "/notes/1851254 "}, {"RefNumber": "863362", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "Security checks in SAP EarlyWatch Alert, EarlyWatch and GoingLive sessions", "RefUrl": "/notes/863362 "}, {"RefNumber": "1069911", "RefComponent": "BC-CST-GW", "RefTitle": "GW: Changes to the ACL list of the gateway (reginfo)", "RefUrl": "/notes/1069911 "}, {"RefNumber": "1689663", "RefComponent": "BC-CST-GW", "RefTitle": "GW: Simulation mode for reg, sec, and prxy_info", "RefUrl": "/notes/1689663 "}, {"RefNumber": "1504652", "RefComponent": "XX-RC-BC-SEC", "RefTitle": "Consulting: Secure Configuration of Application Server ABAP", "RefUrl": "/notes/1504652 "}, {"RefNumber": "1503858", "RefComponent": "BC-CST-GW", "RefTitle": "Colored lines for sec(-reg) info test in SMGW", "RefUrl": "/notes/1503858 "}, {"RefNumber": "1473017", "RefComponent": "BC-CST-GW", "RefTitle": "Uppercase/lowercase in the files reg_info and sec_info", "RefUrl": "/notes/1473017 "}, {"RefNumber": "1499062", "RefComponent": "BC-CST-GW", "RefTitle": "Incomplete display of sec_info and reg_info", "RefUrl": "/notes/1499062 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "46C", "To": "46D", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "620", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "720", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C62", "URL": "/supportpackage/SAPKB46C62"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62070", "URL": "/supportpackage/SAPKB62070"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62069", "URL": "/supportpackage/SAPKB62069"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64027", "URL": "/supportpackage/SAPKB64027"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70023", "URL": "/supportpackage/SAPKB70023"}, {"SoftwareComponentVersion": "SAP_BASIS 701", "SupportPackage": "SAPKB70108", "URL": "/supportpackage/SAPKB70108"}, {"SoftwareComponentVersion": "SAP_BASIS 702", "SupportPackage": "SAPKB70206", "URL": "/supportpackage/SAPKB70206"}, {"SoftwareComponentVersion": "SAP_BASIS 710", "SupportPackage": "SAPKB71011", "URL": "/supportpackage/SAPKB71011"}, {"SoftwareComponentVersion": "SAP_BASIS 710", "SupportPackage": "SAPKB71012", "URL": "/supportpackage/SAPKB71012"}, {"SoftwareComponentVersion": "SAP_BASIS 711", "SupportPackage": "SAPKB71107", "URL": "/supportpackage/SAPKB71107"}, {"SoftwareComponentVersion": "SAP_BASIS 711", "SupportPackage": "SAPKB71106", "URL": "/supportpackage/SAPKB71106"}, {"SoftwareComponentVersion": "SAP_BASIS 720", "SupportPackage": "SAPKB72004", "URL": "/supportpackage/SAPKB72004"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP KERNEL 7.01 32-BIT", "SupportPackage": "SP110", "SupportPackagePatch": "000110", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011145&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 32-BIT UNICODE", "SupportPackage": "SP110", "SupportPackagePatch": "000110", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011146&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 64-BIT", "SupportPackage": "SP110", "SupportPackagePatch": "000110", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011147&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 64-BIT UNICODE", "SupportPackage": "SP110", "SupportPackagePatch": "000110", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011148&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 32-BIT", "SupportPackage": "SP114", "SupportPackagePatch": "000114", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011145&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 32-BIT UNICODE", "SupportPackage": "SP114", "SupportPackagePatch": "000114", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011146&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 64-BIT", "SupportPackage": "SP114", "SupportPackagePatch": "000114", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011147&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 64-BIT UNICODE", "SupportPackage": "SP114", "SupportPackagePatch": "000114", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011148&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 32-BIT", "SupportPackage": "SP216", "SupportPackagePatch": "000216", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004834&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 32-BIT UNICODE", "SupportPackage": "SP216", "SupportPackagePatch": "000216", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004840&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 64-BIT UNICODE", "SupportPackage": "SP216", "SupportPackagePatch": "000216", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004838&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 64-BIT", "SupportPackage": "SP216", "SupportPackagePatch": "000216", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004839&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 32-BIT", "SupportPackage": "SP220", "SupportPackagePatch": "000220", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004834&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 32-BIT UNICODE", "SupportPackage": "SP220", "SupportPackagePatch": "000220", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004840&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 64-BIT UNICODE", "SupportPackage": "SP220", "SupportPackagePatch": "000220", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004838&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 64-BIT", "SupportPackage": "SP220", "SupportPackagePatch": "000220", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004839&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 32-BIT", "SupportPackage": "SP102", "SupportPackagePatch": "000102", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011145&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 32-BIT UNICODE", "SupportPackage": "SP102", "SupportPackagePatch": "000102", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011146&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 64-BIT", "SupportPackage": "SP102", "SupportPackagePatch": "000102", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011147&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 32-BIT UNICODE", "SupportPackage": "SP102", "SupportPackagePatch": "000102", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011150&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 64-BIT UNICODE", "SupportPackage": "SP102", "SupportPackagePatch": "000102", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011148&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 32-BIT", "SupportPackage": "SP102", "SupportPackagePatch": "000102", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011149&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 64-BIT", "SupportPackage": "SP102", "SupportPackagePatch": "000102", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011151&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 64-BIT UNICODE", "SupportPackage": "SP102", "SupportPackagePatch": "000102", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011152&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT", "SupportPackage": "SP064", "SupportPackagePatch": "000064", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013053&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT UNICODE", "SupportPackage": "SP064", "SupportPackagePatch": "000064", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013054&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT", "SupportPackage": "SP064", "SupportPackagePatch": "000064", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013055&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT UNICODE", "SupportPackage": "SP064", "SupportPackagePatch": "000064", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013056&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 32-BIT UNICODE", "SupportPackage": "SP106", "SupportPackagePatch": "000106", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011150&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 32-BIT", "SupportPackage": "SP106", "SupportPackagePatch": "000106", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011149&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 64-BIT UNICODE", "SupportPackage": "SP106", "SupportPackagePatch": "000106", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011152&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 32-BIT UNICODE", "SupportPackage": "SP094", "SupportPackagePatch": "000094", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011150&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 32-BIT", "SupportPackage": "SP094", "SupportPackagePatch": "000094", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011149&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 64-BIT", "SupportPackage": "SP094", "SupportPackagePatch": "000094", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011151&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 64-BIT UNICODE", "SupportPackage": "SP094", "SupportPackagePatch": "000094", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011152&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT", "SupportPackage": "SP264", "SupportPackagePatch": "000264", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004059&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT UNICODE", "SupportPackage": "SP264", "SupportPackagePatch": "000264", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004835&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT", "SupportPackage": "SP264", "SupportPackagePatch": "000264", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004836&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT UNICODE", "SupportPackage": "SP264", "SupportPackagePatch": "000264", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004837&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT", "SupportPackage": "SP271", "SupportPackagePatch": "000271", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004059&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT UNICODE", "SupportPackage": "SP271", "SupportPackagePatch": "000271", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004835&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT", "SupportPackage": "SP271", "SupportPackagePatch": "000271", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004836&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT UNICODE", "SupportPackage": "SP271", "SupportPackagePatch": "000271", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004837&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT UNICODE", "SupportPackage": "SP350", "SupportPackagePatch": "000350", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004051&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT UNICODE", "SupportPackage": "SP350", "SupportPackagePatch": "000350", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004052&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 32-BIT", "SupportPackage": "SP208", "SupportPackagePatch": "000208", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004834&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT", "SupportPackage": "SP275", "SupportPackagePatch": "000275", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004059&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT UNICODE", "SupportPackage": "SP275", "SupportPackagePatch": "000275", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004835&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT", "SupportPackage": "SP275", "SupportPackagePatch": "000275", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004836&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 32-BIT UNICODE", "SupportPackage": "SP208", "SupportPackagePatch": "000208", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004840&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 64-BIT UNICODE", "SupportPackage": "SP208", "SupportPackagePatch": "000208", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004838&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 64-BIT", "SupportPackage": "SP208", "SupportPackagePatch": "000208", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004839&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT UNICODE", "SupportPackage": "SP275", "SupportPackagePatch": "000275", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004837&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT", "SupportPackage": "SP350", "SupportPackagePatch": "000350", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006931&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT", "SupportPackage": "SP350", "SupportPackagePatch": "000350", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006932&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT", "SupportPackage": "SP350", "SupportPackagePatch": "000350", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009590&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT", "SupportPackage": "SP350", "SupportPackagePatch": "000350", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009586&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT UC", "SupportPackage": "SP350", "SupportPackagePatch": "000350", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009588&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT UC", "SupportPackage": "SP350", "SupportPackagePatch": "000350", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009591&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT UNICODE", "SupportPackage": "SP346", "SupportPackagePatch": "000346", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004051&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT UNICODE", "SupportPackage": "SP346", "SupportPackagePatch": "000346", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004052&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT UNICODE", "SupportPackage": "SP339", "SupportPackagePatch": "000339", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004051&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT UNICODE", "SupportPackage": "SP339", "SupportPackagePatch": "000339", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004052&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT", "SupportPackage": "SP346", "SupportPackagePatch": "000346", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006931&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT", "SupportPackage": "SP346", "SupportPackagePatch": "000346", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006932&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT", "SupportPackage": "SP339", "SupportPackagePatch": "000339", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006931&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT", "SupportPackage": "SP339", "SupportPackagePatch": "000339", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006932&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT", "SupportPackage": "SP339", "SupportPackagePatch": "000339", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009586&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 4.6D_EXT 32-BIT", "SupportPackage": "SP2548", "SupportPackagePatch": "002548", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200007105&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 4.6D_EXT 64-BIT", "SupportPackage": "SP2548", "SupportPackagePatch": "002548", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200007108&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 4.6D_EX2 32-BIT", "SupportPackage": "SP2548", "SupportPackagePatch": "002548", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009585&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT", "SupportPackage": "SP346", "SupportPackagePatch": "000346", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009590&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT", "SupportPackage": "SP339", "SupportPackagePatch": "000339", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009590&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT", "SupportPackage": "SP346", "SupportPackagePatch": "000346", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009586&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT UC", "SupportPackage": "SP339", "SupportPackagePatch": "000339", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009591&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT UC", "SupportPackage": "SP346", "SupportPackagePatch": "000346", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009588&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT UC", "SupportPackage": "SP339", "SupportPackagePatch": "000339", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009588&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 4.6D_EX2 64-BIT", "SupportPackage": "SP2548", "SupportPackagePatch": "002548", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009589&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT UC", "SupportPackage": "SP346", "SupportPackagePatch": "000346", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009591&V=MAINT"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}