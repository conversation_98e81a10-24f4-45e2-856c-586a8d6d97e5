{"Request": {"Number": "99821", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 331, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014554882017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000099821?language=E&token=B06F68C68566662B6049534DFE8B0C39"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000099821", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000099821/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "99821"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 15}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.02.2002"}, "SAPComponentKey": {"_label": "Component", "value": "FI-LC-LC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Basic Functions"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Legal Consolidation", "value": "FI-LC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-LC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "FI-LC-LC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-LC-LC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "99821 - Archiving/consolidation: new archiving programs"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Long runtimes in consolidation archiving.<br />Missing consolidation archive read programs.<br />Missing link between consolidation archiving and the archive information system.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>ADK, archiving<br />Archiving objects: FLC_OBJECT, FI_LC_ITEM, FI_LC_SUM<br />Programs: RGUARCLC, RGUDELLC, RGURELLC, RGUARCTC, RGUDELTC, RGURELTC,<br />RGUARC10, RGUDEL10, RGUREL10,<br />Transactions: OCY1, OCY2, OCY3, OCY4.<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Consolidation archiving delivered in the standard system of releases prior to 4.5A uses the logical database 'GLG' as a selection routine, which reads the totals records hierarchically and selects the journal entries exclusively via the totals. As of Release 4.5A, the code and table entries for archiving use their own selection routines (which have been improved in the following releases) instead of the logical database.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>In Release 4.5A, consolidation is provided with a revised, object-oriented archiving which uses its own selection routines instead of the logical database. These code and table entries for archiving can be imported as a preliminary development from the SAPSERVx servers into Releases 3.0D to 3.1I and 4.0B.<br /><br /><br />Information on the new FI-LC code and table entries for archiving:<br />------------------------------------------------------------------</p> <OL>1. The archiving object FLC_OBJECT is replaced by two new objects:</OL> <UL><UL><LI>object FI_LC_ITEM which archives journal entries (table FILCA),</LI></UL></UL> <UL><UL><LI>object FI_LC_SUM which archives totals records (tables FILCT and GLT3).<br /></LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You should no longer use the old archiving object FLC_OBJECT. Deleting and reloading archives that were created via this object, however, continues to be permitted. <OL>2. The two new archiving objects are equipped with:</OL> <OL><OL>a) an archive write program<br />In the new archiving, the totals records and the journal entries are always archived separately.<br />Data can be written to the archive sorted by document or by account. Unsorted archiving is possible only after consulting the responsible SAP Development department.</OL></OL> <OL><OL>b) a deletion program<br />Deleting archives that were created 'without changes to the database' is possible so that no repeated 'productive' archiving is required after a successful 'unproductive' archiving run. Data can be deleted from the database by separately executing the deletion program.</OL></OL> <OL><OL>c) a reload program<br />Like the archiving program, the reload program has two reloading modes (journal entries or totals records) and can invert all archiving procedures.</OL></OL> <OL><OL>d) a read periphery<br />Archiving now also has a read periphery in form of an actual journal entry and totals read program for all archived FI-LC data.<br />You can start these read programs via Transaction SE38 or SARA, directly or in the background, for the archiving object FI_LC_ITEM, FI_LC_SUM and for the old archives (for object FLC_OBJECT).<br />The program names which depend on the archived data to be read and on your R/3 release are:<br />-&gt; Reading archived FI-LC data (for Releases 3.0D up to and&#x00A0;&#x00A0;&#x00A0;&#x00A0;including 3.1I):<br />&#x00A0;&#x00A0; - Program RGURDALC for journal entries<br />&#x00A0;&#x00A0; - Program RGURDTLC for totals records<br />-&gt; Reading archived FI-LC data (for Release 4.0B)<br />&#x00A0;&#x00A0; - Program RFILCA00 for journal entries<br />&#x00A0;&#x00A0;- Program RGUFILCT for totals records<br />The archives created via the archiving object 'FLC_OBJECT' can be read as of now. The 'Reload' function which can be used for this archiving object might thus become obsolete.<br /><br />The guide for read programs for Releases 3.0D up to and including 3.1I cannot be called using Transaction SARA. In this case, use Transaction SE61. Select 'Text in dialog' as the document class and enter 'FI_LC_READ_ITEM_ARCHIVES' (for journal entries) or 'FI_LC_READ_SUM_ARCHIVES' (for totals records) in the 'Dialog text' input field. Choose 'Display'.</OL></OL> <OL>3. Both new objects are now linked to the archive information system. This enables swift access to all archives that are sorted by document or by account. Linking the old FI-LC archiving (archiving object: FLC_OBJECT) is not possible.</OL> <OL>4. The archiving F1 helps are very detailed and should help you when selecting parameters and archiving.</OL> <p><br /><br /><br />Information on the import of the new FI-LC code and table entries for archiving:<br />---------------------------------------------------------------------</p> <OL>1. On the SAPSERVx servers, the programs for the new FI-LC archiving are currently available in English and German.<br />You will have to import the programs as described below.</OL> <OL>2. Since they are a preliminary development, the files are regularly replaced by new files to include changes to the standard system.<br />Always make sure that you use the latest version of the programs. The files listed below date from June 16, 1999 (for Release 3.0D up to and including 3.1I).</OL> <OL>3. You have to import the files again after an upgrade to a release prior to 4.5A.<br />Also, you have to import them again after an import of IS industry-specific components (for example, IS-OIL). If you plan such an import, importing the new FI-LC code and table entries for archiving should be the last step.</OL> <OL>4. To be on the safe side, make sure that during the import no programs from the EC-PCA or FI-SL code and table entries for archiving are executed.</OL> <OL>5. After the imports into the customer system described below, you can execute the new archiving.<br />You can call it using Transaction SARA but not from the consolidation menu since the old programs (for archiving object FLC_OBJECT) would be called up in this case. However, you can use Transaction SE93 to create new transaction codes for the archiving, reload and deletion program for each of the two new archiving objects and include these into your FI-LC menu.</OL> <OL>6. The import does not change the existing archives.</OL> <OL>7. If you have any questions or problems, please contact the SAP Development department in Walldorf.</OL> <p><br /><br /><br />Importing the new consolidation code and table entries for archiving:<br />---------------------------------------------------------------------<br />To import the new consolidation archiving, proceed as follows for all releases to which this note is applicable:</p> <OL>1. In Releases 3.0D up to and including 3.1I: Implement Note 89324 (new basic archive).</OL> <OL>2. Separately import the archiving tools into your system.<br />In Releases:</OL> <OL><OL>a) 3.0D up to and including 3.1I, import the files K000272.ZAT, R000272.ZAT according to Note 13719</OL></OL> <OL><OL>b) 4.0B - import files K000445.ZAT and R000445.ZAT according to Note 13719</OL></OL> <p>from directory /general/R3server/abap/note.0099821 (on the SapservX server relevant for you, where X = 3, 4, 5, 6 or 7) into the customer system.<br /><br /><br /><br />Before you use the new archiving objects:<br />----------------------------------------------------</p> <OL>1. Make sure you check the variants in the delete program before you first use the new archiving objects. You can do this as follows:</OL> <UL><LI>Call up Transaction SARA and enter FI_LC_ITEM under Object name</LI></UL> <UL><LI>Choose 'Goto -&gt; Customizing' and then 'Technical settings'</LI></UL> <UL><LI>Choose 'Variant' in the row that specifies the 'Test run variant' for the delete program. Choose 'Change values'. On the subsequent screen, the 'Test run' check box must be activated. Choose 'Continue'. If in row 'Description' no text has been entered, enter 'Test run variant'. Save the new entries and go back a screen.</LI></UL> <UL><LI>Choose 'variant' in the row that specifies the 'Prod. run variant' for the delete program. Choose 'Change values'. On the subsequent screen, the 'Test run' check box must be activated. Choose 'Continue'. If in row 'Description' no text has been entered, enter 'Prod. run variant'. Save the new entries.</LI></UL> <UL><LI>Repeat this procedure for Object name FI_LC_SUM.</LI></UL> <OL>2. Check whether the read program mentioned above can be called up in Transaction SARA: To do this proceed as follows:</OL> <UL><LI>Call up Transaction SARA, enter FI_LC_ITEM under Object name and then press 'Enter'.</LI></UL> <UL><LI>Choose 'Analyze' and then F4 help in the 'Read program'. If the list contains program RGURDALC (for Rel. 3.0D to 3.1I) or RFILCA00 (Rel. 4.0B) then all the Reports are correctly connected and the check is complete. If 'Analyze' or the programs stated above are not diplayed:</LI></UL> <UL><UL><LI>Call up Transaction SM30 and enter ARCH_REPOW into the 'Table/view' filed. Choose 'Maintain' and then 'New entries'.</LI></UL></UL> <UL><UL><LI>Enter FI_LC_ITEM into the 'Object name' field and according to the release enter either RGURDALC or RFILCA00 into the 'Program name' field. Save the entries.</LI></UL></UL> <UL><LI>Call up Transaction SARA and enter FI_LC_SUM under Object name</LI></UL> <UL><LI>Choose 'Analyze' and then F4 help in the 'Read program'. If the list contains the program RGURDTLC (for Rel. 3.0D to 3.1I) or RGUFILCT (Rel. 4.0B) then all reports are connected correctly and the check is comlpete. If 'Analyze' or the programs stated above are not displayed:</LI></UL> <UL><UL><LI>Call up Transaction Sm30 and enter ARCH_REPOW into the 'Table/view' field.&#x00A0;&#x00A0;Choose 'Maintain' and then 'New entries'.</LI></UL></UL> <UL><UL><LI>Enter FI_LC_SUM into the 'Object name' field and then according to the release enter either RGURDTLC or RGUFILCT into the 'Program name' field. Save the entries.</LI></UL></UL> <p><br /><br /><br />Importing the link of FI-LC archiving to the archive information<br />system:<br />------------------------------------------------------------------<br />To link the two new archiving objects to the archive information system, proceed as follows for all releases to which this note is applicable:</p> <OL>1. Import Note 99388 in your system according to Note 13719.</OL> <OL>2. Import the files with the archive information system Customizing settings for FI-LC in your system as specified below.<br />The file names are:</OL> <OL><OL>a) in Releases 3.0D up to and including 3.1I: R001371.ZAT and K001371.ZAT</OL></OL> <OL><OL>b) in Release 4.0B: R050597.P45 and K050597.P45.</OL></OL> <OL>3. Activate the imported information structures SAP_FI_LC_ITEM1, SAP_FI_LC_ITEM2 and SAP_FI_LC_SUM via Transaction SARI.</OL> <p><br /><br /><br />Known Problems<br />---------------</p> <OL>1. The imported archiving write programs lead to an overflow of DB locks. The system does not release the transmitted block on tables TADIR or RSSGTPDIR.<br />You can find the solution to this problem described in Note 183164.</OL> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "HIER"}, {"Key": "Transaction codes", "Value": "SE38"}, {"Key": "Transaction codes", "Value": "TRANSAKTIONEN"}, {"Key": "Transaction codes", "Value": "SM30"}, {"Key": "Transaction codes", "Value": "SARA"}, {"Key": "Transaction codes", "Value": "SARI"}, {"Key": "Transaction codes", "Value": "SE61"}, {"Key": "Transaction codes", "Value": "SE93"}, {"Key": "Transaction codes", "Value": "OCY1"}, {"Key": "Transaction codes", "Value": "OCY2"}, {"Key": "Transaction codes", "Value": "OCY3"}, {"Key": "Transaction codes", "Value": "OCY4"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D022872)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D022872)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000099821/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000099821/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000099821/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000099821/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000099821/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000099821/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000099821/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000099821/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000099821/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "98114", "RefComponent": "BC-CCM-ADK", "RefTitle": "Archiving: Reloading destroys archive files", "RefUrl": "/notes/98114"}, {"RefNumber": "89324", "RefComponent": "BC-CCM-ADK", "RefTitle": "Archiving: Revised ADK versions", "RefUrl": "/notes/89324"}, {"RefNumber": "619645", "RefComponent": "FI-LC-LC", "RefTitle": "Archiving of FI-LC line items: Input help for document type", "RefUrl": "/notes/619645"}, {"RefNumber": "368846", "RefComponent": "FI-LC-LC", "RefTitle": "Archiving consolidatn: error GX059 in write program", "RefUrl": "/notes/368846"}, {"RefNumber": "183068", "RefComponent": "FI-LC", "RefTitle": "Archiv. consolidation: write program causes DB locks overflo", "RefUrl": "/notes/183068"}, {"RefNumber": "153588", "RefComponent": "BW-BCT", "RefTitle": "Syntax error 'Type \"GUSL_T_GLU1_BIW\" is unknown'", "RefUrl": "/notes/153588"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "619645", "RefComponent": "FI-LC-LC", "RefTitle": "Archiving of FI-LC line items: Input help for document type", "RefUrl": "/notes/619645 "}, {"RefNumber": "183068", "RefComponent": "FI-LC", "RefTitle": "Archiv. consolidation: write program causes DB locks overflo", "RefUrl": "/notes/183068 "}, {"RefNumber": "368846", "RefComponent": "FI-LC-LC", "RefTitle": "Archiving consolidatn: error GX059 in write program", "RefUrl": "/notes/368846 "}, {"RefNumber": "89324", "RefComponent": "BC-CCM-ADK", "RefTitle": "Archiving: Revised ADK versions", "RefUrl": "/notes/89324 "}, {"RefNumber": "153588", "RefComponent": "BW-BCT", "RefTitle": "Syntax error 'Type \"GUSL_T_GLU1_BIW\" is unknown'", "RefUrl": "/notes/153588 "}, {"RefNumber": "98114", "RefComponent": "BC-CCM-ADK", "RefTitle": "Archiving: Reloading destroys archive files", "RefUrl": "/notes/98114 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30D", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}