{"Request": {"Number": "1319517", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 639, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016756972017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001319517?language=E&token=2A23BF1E445CA3485438576700DC2971"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001319517", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001319517/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1319517"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 42}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "2021.10.06"}, "SAPComponentKey": {"_label": "Component", "value": "BC-I18-UNI"}, "SAPComponentKeyText": {"_label": "Component", "value": "Unicode"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Internationalization (I18N)", "value": "BC-I18", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-I18*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Unicode", "value": "BC-I18-UNI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-I18-UNI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1319517 - Unicode Collection Note"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Unicode Information</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Unicode; Unicode Conversion</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This note collects important SAP notes related to Unicode. Due to the high number of notes dealing with this topic, in most cases we do not list \"simple\" bugs solved with the latest Support Package / Kernel. SAP recommends to begin with the SAP notes displayed below in bold.<br />Additional Information can be found in <a target=\"_blank\" href=\"https://support.sap.com/en/product/globalization.html\">https://support.sap.com/en/product/globalization.html</a>&#160;--&gt; Unicode&#160;<br />If you have further questions, please send a <NAME_EMAIL> .</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ol>1. Important General Information</ol>\r\n<p><strong><a target=\"_blank\" href=\"/notes/1310720\">1310720 </a>Collection Note for Unicode Systems with different endianess</strong><br /><strong><a target=\"_blank\" href=\"/notes/1322715\">1322715 </a>Unicode FAQs</strong><br /><strong><a target=\"_blank\" href=\"/notes/2033243\">2033243 </a>End of non-Unicode Support: Release Details</strong></p>\r\n<ol>2. Installation and Conversion (general)</ol>\r\n<p>9385 What do you do with QCM tables (conversion tables)<br />99507 Update terminations after upgrade/client copy<br />158774 Incorrect case mappings in some locales<br />190669 Sending lists using SAPconnect<br />197617 Roll conversion partner functions<br />455195 R3load: Use of TSK files<br />514967 Table logging following change in the code page<br />539404 FAQ: Answers to Questions about the Security Log Files<br />544623 Unicode Installation<br /><strong><a target=\"_blank\" href=\"/notes/547314\">547314 </a>FAQ: System Copy procedure</strong><br /><strong><a target=\"_blank\" href=\"/notes/548016\">548016 </a>Conversion to Unicode / Unicode System Copy</strong><br />552464 What is Big Endian/Little Endian ? What Endian do I have ? <br />552612 Differences between MS and SAP Code Pages<br />573044&#160; Unicode conversion for HR application<br />589159 Incorrect entries in matchcode fields in ADRC and ADRP<br />614550 Troubleshooting BC-I18<br />617444 Separate SCHEMA ID for database schema and tablespace name<br />627764 Unicode Migration: Table pools inconsistent after conversion<br />651880 Units of measurement problems in Latin-1 and Unicode systems<br />660873 Transporting Proposal Pools after WebAS release 6.20 SP 25<br />674882 Upgrade: Syntax error in program RKE5XXXX<br />679456 Reducing data volume before Unicode Conversion<br />688089 SYSLOG: unreadable characters after change to UNICODE<br />689951 Release upgrade from 6.20 to 6.40 for customer programs<br />693168 Minimized Downtime Service and Incremental Migration<br />695196 Error in the export for Unicode migration<br />706528 Changing Unicode setting for connection type \"L\"<br />708442 Incorrect generated programs in Unicode systems<br />709019 Inconsistency with table types after Unicode migration<br />712958 Dumps when processing personal value list<br />729348 Unicode change for units of measure<br />738858 R3load for Unicode conversion<br />740863 RADCUCNT: Objects that cannot be generated.<br />765475 Unicode Conversion: Troubleshooting<br />766703 FAQ: Credit card encryption in R/3 systems<br />771209 NW04: System copy (supplementary note)&#160;&#160; Help<br />778209 Cluster records with missing PAGENO 0 in table CDCLS<br />778662 Check and correction report for table CDCLS<br />784118 System Copy Java Tools<br />785848 Hom./Het.System Copy SAP Web AS 6.40 SR1 Java<br />790099 R/3 Parameter Settings for Unicode Conversion<br />795267 System Copy for 'SAP Web AS Java' based systems<br />795871 Conversion of the enqueue key of the TLOCK* tables<br />813445 Documentation of the report UMG_POOL_TABLE<br />821971 Co-operation between RADCUCNT and R3load<br />827289 Generation of Unicode runtime objects<br />833946 Splitting of STR files<br />837184 Report Writer and Unicode conversion<br />838402 Non-Unicode installations: drawbacks and shortcomings<br /><strong><a target=\"_blank\" href=\"/notes/837173\">837173&#160;</a>RADCUCNT in Unicode Conversion: Collective Note</strong><br />855841 Payment medium in a Unicode system<br />856557 INDX-type table with UC data but non-UC code page info<br /><strong><a target=\"_blank\" href=\"/notes/857081\">857081 </a>Unicode conversion: downtime estimate</strong><br />858242 Upgrade to Unicode: Required actions for ITS 6.20<br />867193 ABAP and kernel patches for CU&amp;UC in 46C <br /><strong><a target=\"_blank\" href=\"/notes/881048\">881048 </a>F4 Help: Unicode conversion for table DDSHPVAL50</strong><br />881879 Unicode conversion for table O2HTMLATTR<br />885343 SAP System Landscape Copy<br /><strong><a target=\"_blank\" href=\"/notes/888210\">888210 </a>NetWeaver 7.0/7.10: System copy (supplementary note) Comment: (needed for BW part (system copy) of SAP NW 7.0)</strong><br /><strong><a target=\"_blank\" href=\"/notes/901004\">901004 </a>Conversion of ABAP spool requests to Unicode.</strong><br />922783 Rescue language files for DE / EN<br /><strong><a target=\"_blank\" href=\"/notes/928729\">928729 </a>Combined Upgrade &amp; Unicode Conversion FAQ - Comment: (Important Information for CU&amp;UC)</strong><br /><strong><a target=\"_blank\" href=\"/notes/932779\">932779 </a>Unicode conversion - analysis of nametab problems&#160; - Comment: (Good description regarding nametab handling)</strong><br />935239 Adjusting TMS RFC destination after Unicode conversions<br />938738 Syslog CP6: still using ambiguous blended code page<br />952514 Pilot Projects for table splitting<br />960710 Fin statement versions: Text under UNICODE partly unusable<br />961026 EH&amp;S WWI: Errors in Unicode mode<br />977372 Dependencies corrupt after unicode conversion<br />978244 CU&amp;UC and nametab errors<br />987914 Adjusting variants, Unicode problem<br />991481 Database is using UTF-8 and not UTF-16 ?<br />1010237 Growth of DYNPSOURCE table after Unicode migration<br />1017064 Incorrect SAPscript transport<br />1017138 Correcting Customizing texts after Unicode Conversion<br />1021586 Unicode conversion of non-Latin-1 Single Codepage<br />1023337 Unicode-Umstellung \"CatManSuite\"<br />1024253 Matchcode regeneration in Unicode systems<br /><strong><a target=\"_blank\" href=\"/notes/1051576\">1051576 </a>Conversion of Single Code Page Systems to Unicode</strong><br />1055585 Texts on dynpros are displayed in the wrong language.<br />1055820 Proposal Pool Conversion<br />1056170 Easy DMS 6.0 SP07 Patch1 Release Note<br />1066404 R3load, ORDER BY, Unicode conversion of cluster<br />1077403 Cluster table check using SDBI_CLUSTER_CHECK<br />1100672 RADNTLANG as a tool for checking the text language<br /><strong><a target=\"_blank\" href=\"/notes/1139642\">1139642 </a>Hardware Requirements in Unicode Systems</strong><br />1134257 VAT Return (Russia): Corrections for Unicode Systems<br />1141801 SBWP: Euro character disappears after Unicode conversion<br />1143116 Special language keys are missing in default language vector<br />1144841 SEPA file is incorrect<br />1146480 Unicode Conversion or File I/O \"break\" Japanese U+3231<br />1149417 Unicode Conversion Experience Kit Documentation<br />1153741 Including SDBI_CLUSTER_CHECK in SPUMG<br />1178671 Conversion of Views from non-Unicode to Unicode<br />1232373 Problem with Unicode conversion for UPS object /ISDFPS/FR<br />1248636 Cleanup of inconsistencies in Data Dictionary<br />1255556 Unicode conversion preparation: Container table<br />1259939 Text table design<br />1275074 SCU3: Change Logs - Short dump or '#' in results<br />1275317 HR-RU: Incorrect characters in Russian HR tables<br />1295322 Nametab inconsistencies in table/view maintenance<br />1307982 Unicode nametab entries cannot be created<br />1309728 SDBI_CLUSTER_CHECK: Operating instructions<br />1309804 SDBI_CLUSTER_CHECK: Operation and error messages<br />1330256 LOAD_PROGRAM_NOT_FOUND Job RDDMASGL Program RDD21DAT<br />1348055 Initial cluster records during Unicode migration<br />1345121 Profile Parameters which should not be set in Unicode<br />1356472 Unicode conversion: Number of rows of table clusters<br />1373611 Unknown type in report UMG_SHOW_UCTAB during generation<br />1390229 F110 ... Information concerning a UNICODE conversion<br />1419531 R3load patch information<br />1426513 Unicode Conversion: check nametab tables<br />1428028 Unicode Conversion: check nametabs report UMG_CHECK_NAMETABS<br />1429935 Report UMG_CHECK_APPLICATION_TABS<br />1448532 PS-IS: Variants lead to terminations after Unicode migration<br />1451275 Korrekturprogramm f&#252;r BDSPHIO3 vor einer Unicodeumstellung<br />1457258 Correction instruction for the Additional Preparation Steps<br />1459066 SPUMG settings: code page 1614 as Global Fallback Code Page<br />1464541 Syntax error in update programs RMCX....<br />1499385 Lost layout after text download<br />1500340 CU&amp;UC: Long running jobs in Additional Preparation Steps<br />1518203 RTF download: missing text alignment within cells<br />1554717 SYB: Planning information for SAP on Sybase ASE<br />1563122 R3load Feature Request FEATURE_CUUC_ACTIVE<br />1571295 Non-Unicode installation option is not available in sapinst<br />1583568 RFZALI20: Unicode conversion for report variants<br />1596246 UCCHECK does not check all custom namespaces<br />1607053 Content of the Export Control Table in a CU&amp;UC<br />1612108 Reports to correct compIds in content server<br />1686722 How to check when a Unicode Conversion occurred<br />1689243 RADCUCNT report taking long to complete - Unicode Conversion<br />1700052 Secondary Connection to SAP HANA from non-Unicode<br />1859302 How to find which code page(s) contain a specific character<br />1882544&#160; Syntax error \"program is not Unicode-compatible, according to its program attributes.\"<br />1896539 Questions about compatibility and end of support to non-unicode systems<br />1912933 - How to determine the language configuration of the non-Unicode source system after a Unicode conversion<br />1968508 - Release Change &amp; Single Code Page Conversion to Unicode with DMO<br /><strong><a target=\"_blank\" href=\"/notes/1990240\">1990240&#160; </a>Support of mixed landscapes (Unicode &lt;=&gt; Non-Unicode)</strong><br />2054812 - How to delete non-unicode database when choosing \"One Server Method\" during a single code page conversion&#160;<br /><a target=\"_blank\" href=\"/notes/2311023\">2311023</a> - SWPM: Unicode conversion with parallel export/import over socket/pipe is not supported<br /><a target=\"_blank\" href=\"/notes/2316783\">2316783</a> - Unicode Conversion Tools for basis release 710 and 711</p>\r\n<p>3. MDMP or Blended Code Page related SAP Notes</p>\r\n<p>3992&#160; Purpose of the table INDX<br />427561 Transaction SPUMG<br /><strong><a target=\"_blank\" href=\"/notes/551344\">551344 </a>MDMP Conversion to Unicode</strong><br />662215 SPUMG and SUMG in Basis Release 6.20 and 6.40<br />672835 Textflags could cause problems during Unicode conversion<br />673941 Unicode conversion for address tables (ADRC, ADRP)<br />680695 Unicode conversion in tables LFA1, KNA1 and KNVK<br />682783 Default Unicode conversion codepage for HR tables<br />684332 Unicode system: Populating the TRDIR-RLOAD language key<br />737428 Converting ambiguous blended codepage system to Unicode<br />756534 Automatic Assignment of Languages with Character Statistics<br />756535 Special SAP vocabulary for maintainig the SPUMG vocabulary<br />818374 Unicode Conversion of a Blended Code Page System<br />832918 The SPUMG Exception List<br />871541 Frequently used text patterns<br />928909 Repair table data in SUMG in Unicode systems<br />938374 Additional tools for MDMP --&gt; Unicode conversion preparation<br />939691 SPUMG: INDX scan fails for client dependent INDX tables<br />945255 SUMG reports on DOKTL during Unicode conversion<br />959698 Twin Upgrade &amp; Unicode Conversion<br />975996 Probs with C&#167; during conversion to Unicode<br />992956 Duplicate keys in table UMGPMDIT after Unicode conversion<br />994909 UMG_SUMG_VERIFY_R3LOAD_XML : What to do?<br />996990 Exception List for SPUMG<br />1012025 R3load failure in Unicode Conversion with .xml files <br />1019362 Long run times during SPUMG scans<br />1034188 SPUMG: Table fields for language determination<br />1037613 Concept of an MDMP -&gt; Unicode conversion<br />1042842 Activity Logs: Unicode system converted from MDMP<br />1069209 SPUMG or SPUM4: Reprocess Log becomes too large<br />1078263 SPUM4 - Reprocess/INDX Log: Word too long<br />1096903 Tutorial: \"Table splitting in SPUMG\"<br />1114085 Tutorial: Manual Maintenance of the System Vocabulary<br />1119110 Language dependent tables in CU&amp;UC procedure<br />1128457 Additional post conversion program UMG_SAVE_HISTORY<br />1128672 Report UMG_CHECK_STXL<br />1128673 Additional post conversion step<br />1129173 Special SPUMG vocabulary used in CU&amp;UC procedure<br />1132495 Report UMG_DELETE_SSLOAD<br />1142572 Classification of notes of the Unicode conversion<br />1142573 Reuse of language assignments in SPUM4 and SPUMG<br />1177899 Huge amount of R3load Log(XML files) during UC Conversion<br />1179067 Language dependent tables in CU&amp;UC procedure<br />1244353 Authorization check in UM4_FINISH_PREPARATION<br />1247334 Performance of SUMG Repair Hints<br />1255030 SUMG: Reprocess Log is not displayed <br />1276242 UMGSTAT is missing entries, UMG_ADD_VOCABULARY<br />1291662 Cleanup of table INDX(PR)<br />1292125 Cleaning up the table INDX(IW)<br />1294414 Cleaning up table INDX (VM/RT)<br />1302042 Cleanup for table INDX(KU)<br />1313947 SUMG Manual Repair: repair of INDX Log uses wrong rowid<br />1315753 SCU3: Converted into Unicode system from MDMP<br />1318670 Bereinigung Tabelle INDX(KE)<br />1320370 SUMG Manual Repair: ALV grid sort, filter, select options<br />1338186 Language assignments are deleted when synchronizing package<br />1339144 SUMG Automatic Repair: incomplete repair of split tables<br />1345543 SUMG Manual Repair: Log list is not scrollable<br />1363394 Wrong Master Language entries for AFS objects in REPOSRC<br />1366006 Change logs after conversion from MDMP to Unicode<br />1368419 MDMP to Unicode conversion,logs not displayed correctly<br />1392027 SUMG: Display number of log entries<br />1397782 SUMG Manual Repair: Save<br />1417727 New version of report UMG_ANALYZE_REPLOG<br />1422822 I18n Table Process Information<br />1428549 SPUMG: Usage of .VOC files in the \"Vocabulary Import\"<br />1443472 SUMG: Reuse Hints<br />1497794 SPUMG: Adding words from the Reprocess Log to the Vocabulary<br />1732495 How to repair table entries manually with transaction SUMG</p>\r\n<ol>4. SAP Business Warehouse specific</ol>\r\n<p>173241 Allowed characters in the BW System<br />173255 Language problems in the BW System<br />449891 Temporary database objects in BW 3.x<br />545923 FAQ: Tech. restrictions/customer namespace/permitted chars<br />548769 BW Unicode Scenario: Dump<br />563975 BW and MDMP/BW and blended code pages not supported<br />588480 Release restrictions for BW 3.1 Content/Unicode<br />589701 Known errors in BW 3.1 Content Unicode<br />609054 Extractors: Deleting generated programs before the upgrade<br />643813 Composite SAP note - BW Unicode<br />674371 Replicating application components in Unicode BW<br />673533 Alignmnt problem during extraction into/from Unicode<br />717776 Text transformation between Unicode and non-Unicode<br />765543 SAP BW Unicode and Staging BAPI - release<br />801416 Activation of MIC content and extraction -problems<br />808019 Ldg master data from non-Unicode OLTP to Unicode BW<br />853569 Problems with allowed characters in 0UNIT (for example &#181;)<br />885441 Common Migration Errors<br />925170 Web Application Designer NW2004s und Non-Unicode Systeme<br />1314039 Logon to BEX in Vietnamese and other languages does not work<br />1478123 FAQ: Source system&#160;<br />2296693 - SAP BW Upgrade Path Finder<br /><br /></p>\r\n<ol>5. CRM and SRM specific</ol>\r\n<p>611232 System copy for BBP/CRM, CRM, EBP/CRM, EBP<br />686898 Data exchange with R/3 Backend with several code pages<br />691585 CRM Unicode / R3 MDMP Connection: User Exits<br />718324 New MDMP implementation not supported for mySAP CRM<br />765439 Collection Note for Unicode - CRM Middleware<br />793546 CRM Server Unicode Migration: Mobile Client Text<br />801949 MDMP implementation not supported for mySAP SRM<br />819426 Mobile Clients Sync Performance - Unicode servers<br />846194 OCI/OPI: Code pages and system language<br />873789 Non XML processing on Unicode CRM servers<br />902083 CRM Unicode Collection Note for CRM 4.0<br />948563 E-mail for notification in Unicode systems<br />991262 FAQ on Unicode enabled Mobile client 4.0 released<br />1039256 New 4.0 SP12 Unicode Mobile client - post installation steps<br />1032589 Unicode upgrade Note for SAP CRM Mobile client 4.0 SP12<br />1118230 SAP CRM 4.0 Non-Unicode Mobile clients to Unicode released<br />1122968 Bus. Trans. duration data corrupted after unicode conversion</p>\r\n<ol>6. SCM / APO specific</ol>\r\n<p>632357 Backing up liveCache data for SCM 4.0<br />875712 Unicode conversion and planning logs<br />882865 CCR: Incorrect results when you use the parallel processing<br />1055588 SCM 5.X upgrade and Unicode conversion<br />1063794 Heterogeneous System Copy</p>\r\n<ol>7. SAP ERP specific for MDMP customers</ol>\r\n<p>741821 Release limitations concerning SAP ERP 2004<br />747036 mySAP ERP 2004 Upgrade for R/3 MDMP Customers<br />852235 Release Limitations for mySAP ERP 2005<br />896144 mySAP ERP 2005 Upgrade for R/3 or ERP MDMP Customers</p>\r\n<ol>8. ABAP Unicode Enabling - UCCHECK</ol>\r\n<p>367676 Release Upgrade from 4.6 to 6.10 for customer programs<br />453182 Error in Unicode Program &amp; Unicode Flag<br />493387 Potential effects of table- and structure extensions<br />509898 BAPI enhancement concept and Unicode<br />549143 Searching for User-Exits which need Unicode-enabling<br />553110 User Exits: Behaviour within non-Unicode R/3 Enterprise<br />563417 Unicode indicator for BOR object type programs<br />583546 Unicode demo programs<br />594356 Function modules ws_upload and ws_download<br />665142 Reducing the check quantity in UCCHECK<br />721477 UCCHECK does not check any ZX* customer Includes<br />948691 LIST_TO_ASCI from a Unicode system<br />1056105 Using UCCHECK for 3rd party programs<br />1139325 Procedures and tools to enable ABAP programs for Unicode<br />1269873 ABAP programs continue to be generated <br />1882544 Syntax error \"program is not Unicode-compatible\"</p>\r\n<ol>9. Interfacing: Non-Unicode, MDMP and Unicode</ol>\r\n<p>190669 - Sending lists using SAPconnect<br />314676 Tips for processing data (UTF-8, Japanese)<br />413708 RFC Library that is current at this time<br />483715 Unicode support in DART<br />547444 RFC Enhancement for Unicode ./. MDMP Connections<br />600560 CPICSDK restrictions with Unicode<br />613389 ALE SAP system group with Unicode systems<br />615864 Excel output: Umlauts are not displayed correctly<br />647495 RFC for Unicode ./. MDMP Connections<br />651497 Code page problems during Unicode-&gt;nonUnicode communication<br />656350 Master Data Transfer UNICODE &lt;==&gt; MDMP Systems with ALE<br />663089 RFC bit options in SM59<br />697625 ALE: Transport problems for Customizing data<br />722193 RFC legacy non-Unicode clients and Unicode servers<br />723562 SAP Java Connector 2.x: Configuration and Requirements<br />723612 Problems in the communication with unicode enabled WebAS<br />745030 MDMP - Unicode Interfaces: Solution Overview - Comment: (Good overview - important for mixed landscapes)<br />752835 Usage of the file interfaces in Unicode systems<br />775189 XBP interface and Unicode<br />788239 Which language is used to run an RFC on the target<br />788449 Byte-order Marks in UTF-8 Files<br />790485 RFC Problem Single Code Page, non-Unicode to Unicode System<br />793847 ALE SAP system group (Unicode) with subsystem<br />794411 Supported codepages of SAP Java Connector 2.1 and 6.x<br />798824 Inf. Broadcasting installation: RFC destination and Unicode<br />809279 RFC non-Unicode to Unicode with unknown text language<br />814707 Troubleshooting for RFC connections<br />827999 Error during transfer to MDMP tables with tRFC<br />871945 Fallback Code Page for Zn Languages in Unicode/nonU RFC<br />879479 IDoc file interface in Unicode system<br />881781 Unicode/non-Unicode RFC language and code page<br />886571 RFC non-Unicode ./. Unicode with illegal text language<br />911190 PMW: Entering the code page when writing and downloading<br />920831 RFC with inactive text language<br />926816 ITS Up/Download: ascii files in UTF-8 after up/download<br />931856 Spreadsheet download in a Unicode system<br />985296 Sending multiple address versions from MDMP to other systems<br />991572 Code page settings for external RFC programs<br />991763 Idoc communication between Unicode and MDMP systems<br />1007073 Misalignment in Asian files written from a Unicode system<br />1017101 Encoding Problems with ABAP XML Processing<br />1021459 Conversion behavior of the RFC library<br /><strong><a target=\"_blank\" href=\"/notes/1025361\">1025361 </a>- Installation, Support and Availability of the SAP NetWeaver RFC Library</strong><br />1036341 Unicode executables and corrupt UTF-8 in input files<br />1040014 Importing Unicode CSV from XMII into Microsoft Excel 2003<br />1057289 Reasons for CX_SY_CONVERSION_CODEPAGE / CONVT_CODEPAGE<br />1062237 Transliteration of characters with SCP_REPLACE_STRANGE_CHARS<br />1066323 RFC add Parameter to refuse mdmp with RFC-Library<br />1066952 Working with Legacy data files in LSMW (Unicode, BOM, FTP)<br />1084953 Correct code page when receiving MDMP tables using RFC<br />1095975 SCU3 Restriction: # character in List Display of logs<br />1112206 Converting the workflow destination to Unicode<br />1114037 SAPGOF in Unicode: Problems with missing font metrics<br />1141788 Useful fonts for Unicode PDF conversion (PDFUC/ZPDFUC)<br />1141769 Reading old DART extract with the corresponding code<br />1145203 RFC connections MDMP R/3 system 4.5B or lower<br />1150217 Showcase ENCODING: Editor bug - or - Codepage issue?<br />1151258 Error when sending Excel attachments<br />1176806 Language key for external IDoc processing using<br />1236565 NW RFC SDK: Logging on with non-ISO-Latin-1 user or password<br />1269686 Reading DART extracts with different code pages<br />1274259 Payment medium before upgrade to Unicode-active system<br />1278126 Problems with PMW variants / change to Unicode<br />1294663 RTF upload in Unicode systems<br />1361970 Conversion problems during RFC communication<br />1365764 Codepage of DME file<br />1491018 IDoc: Report MASS_RSEOUT00 in Unicode systems<br />1499385 Lost layout after text download<br />1503601 Error trace for reading Unicode files and OS<br /><a target=\"_blank\" href=\"/notes/2684530 \">2684530</a>&#160;No further support for MDMP in RFC</p>\r\n<p><a target=\"_blank\" href=\"/notes/2917837 \">2917837</a> - Explicit and Force codepage in sm59 is not working</p>\r\n<ol>10. Frontend related</ol>\r\n<p><br />147519 Maintenance strategy / deadlines 'SAP GUI'<br />316903 SAPGUI: How to use I18N mode<br />382285 Some characters are converted to '#' in SAP WinGUI<br /><strong><a target=\"_blank\" href=\"/notes/508854\">508854 </a>GUI Unicode Mode: Usage and restrictions - Comment: (Main SAPGui for Windows restrictions note)</strong><br />513435 Windows code page setup for text files<br />544942 OCX controls: Connection to Unicode backend<br />578944 Expected release dates for SAP GUI for Windows &amp; ITS<br />615746 Incorrect HTML data at front end against Unicode R/3<br />673244 SAPGUI: Recent corrections for Unicode support.<br />710720 SAP GUI for Windows 6.40: Delivery and new functions<br />742662 MS Word as Editor in SAPscript and Smart Forms<br />754311 National special characters are not displayed<br />764480 SAP WinGUI I18N Trouble Shooting<br />765763 Setting the Upload/Download codepage for a SAPGUI connection<br />771333 Encoding isn't recognized in HTML control<br />817010 FB03/FBV3/FB01/JavaGUI/WebGUI: Problems with documt overview<br />837327 SAP GUI for Windows: Side-effects of \"Unicode OFF\"<br />845233 Use of Input Method Editors (IME) in Unicode System<br />857437 Search Help:Field names and dialog title seen wrongly as '?'<br />862693 OFFI:Umlauts getting displayed as \"#\" in unicode<br />880231 OFFI:Problem when unicode schema is used in TemplateDesigner<br />908948 OFFI:garbled data with CopyLinkItemToClipboard<br />910300 GridView:Truncation of text in Unicode systems<br />926840 OFFI:Insert table inserts garbled characters in document<br />929300 SAP GUI for Windows 6.20: Limited support<br />1022548 Graphics with restricted Unicode support<br />1002845 Graphic Layout Editor in Unicode systems (II)<br />1073779 setting the Frontend Up-/Download Codepage<br />1088209 Help for troubleshooting: Code page display problems<br />1159564 F4: Limitation in number of characters for the search string<br />1161278 SAPGOF preferes Japanese formats<br />1324301 Bar chart / net chart: Unicode support<br />1732998 Only code page 4103 for spreadsheet format</p>\r\n<ol>11. Printing</ol>\r\n<p>83502 Printing Support for Native Languages<br />129581 Euro sign: printing<br />215015 Unicode UTF-8 printing with a Lexmark printer<br />323736 Restrictions with \"PDF print\" through spooler<br />423003 Printers and Asian/Eastern European fonts/languages<br />564142 Composite Unicode SAP note (Smart Forms and SAPScript)<br />750002 Smart Forms: Support for Zebra label printer (ZPL2)<br />710994 Layout of lists printed with LEXUT8 Unicode printer<br />750219 Unicode UTF-8 printing with HP printers<br />776507 SAPscript/SmartForms: Which fonts for which languages?<br />785564 Device types for SAPconnect<br />791199 Default editor changed to MSWord in SAPscript and SmartForms<br />809034 Support for Arabic with SAPWIN<br />812821 Cascading font settings<br />842767 Problems with old spool requests after Unicode conversion<br />844760 UCCP f&#252;r Smart Forms<br />873239 Support for Cascading Unicode fonts when printing<br />906031 Information about cascading fonts device types<br />940429 Language independent printing in Smart Forms<br />944778 SAPGOF format from Unicode systems: ST command<br />952050 LATIN fonts in CJK SmartStyles<br />999712 PDF conversion for Unicode<br />1013433 Restrictions on usage of ANDALE_x fonts<br />1027088 Incorrect SAPscript output after Unicode conversion<br />1038413 Unicode SAPWIN device type for CJK font<br />1097990 List of Printer Vendor Wizard Notes<br />1109643 PBWW, PB60 - Problems w/ long texts from non-Unicode systems<br />1136946 Private Use Area (PUA) in Interactive Forms by Adobe<br />1138099 Cascading fonts: Support of Surrogate Pairs<br />1130255 East Asian Character Width in the Unicode PUA<br />1145959 SWINCF: Alignment of BASIC LATIN Text in CJK ABAP lists<br />1173054 SAPscript, Smart Forms: Incorrect character width in Unicode<br />1232987 Preceding and succeeding blank characters for include<br />1261426 Texts from non-Unicode are incorrect in Unicode<br />1299738 Various problems with spool requests in Unicode<br />1349413 OTF documents can no longer be used after Unicode conversion<br />1433470 PDF converter: support for Vietnamese<br />1552498 PDF Previewer<br />1657241 Unicode Printing Solutions for SAP systems<br />1704919 Cascading PDF conversion for non-cascading device<br />1738494 - Using the new Unicode Printing Capabilities<br />1738670 Adjustments of the PDF converter for UPE<br /><strong><a target=\"_blank\" href=\"/notes/1812076\">1812076 </a>Unicode Printing Enhancement (UPE)<br /></strong>2207548 - PDF: Insertion of all fonts into PDF file<br /><br /></p>\r\n<p>&#160;</p>\r\n<ol>12. Transport system</ol>\r\n<p>45548 Transporting language-specific objects<br />80727 Transporting non-LATIN-1 texts<br />136649 R3trans: Selective language import<br />330267 Transports between Basis Releases 4.6* and 6.*<br />620253 Problems when you transport Customizing addresses (6.20)<br /><strong><a target=\"_blank\" href=\"/notes/638357\">638357 </a>Transport between Unicode and Non-Unicode systems</strong><br />775114 Problems during transport into a Unicode system</p>\r\n<ol>13. DB specific</ol><ol><ol>a) Oracle</ol></ol>\r\n<p>355771 Oracle: Explanation of the new tablespace layout<br />558746 Better Oracle Data Dictionary BW Performance<br />606359 FAQ: Oracle National Language Support (NLS)<br />793113 FAQ: Oracle I/O configuration<br />806554 FAQ: I/O-intensive database operations (Oracle specific)<br />808505 Secondary connections to Oracle database<br />858869 Desupport of multibyte character sets as of Oracle 10g<br />857734 System Copy of SAP Systems based on 6.20 and 6.40<br />936441 Oracle settings for R3load based system copy<br />954268 Optimization of export: Unsorted unload<br />960280 R3LOAD EXPORT TUNING FOR SPLITTED TABLES WITH ORACLE<br />1043380 Efficient Table Splitting for Oracle Databases<br />1045847 ORACLE DIRECT PATH LOAD SUPPORT IN R3LOAD<br />1412123 check NLS_LENGTH_SEMANTICS<br />1413928 Index corruption/wrong results after rebuild index ONLINE<br />1797707 Is it possible to use Oracle tools to perform a Unicode conversion?</p>\r\n<ol><ol>b) Other Databases</ol></ol>\r\n<p>567048 INST: Unicode SAP R/3 Enterprise on IBM eServer<br />728743 zSeries: Release of DB2 V8 for SAP Components<br />744735 iSeries: migrating storage parameters to DB4<br />811431 Informix: dbcodepage and Unicode conversion<br />914966 Database enlargement due to Unicode migration:<br />926450 Short dump with error -4025 during connect to ASCII database<br />986907 SQL Server settings for R3load based system copy<br />1040674 R3ta in combination with unsorted unload<br />1054852 Recommendations for migrations to MS SQL Server<br />1062976 DB2-z/OS: fast load</p>\r\n<ol>14. RTL Languages</ol>\r\n<p>587150 Arabic Support<br />822634 RTL SAPScript/Smartforms printing with embedded LTR<br />1108855 Mixing non Latin 1 and ASCII (English) content<br />1258722 LTR Includes in RTL SAPScript/Smartforms documents<br />1280236 RTL languages not known to the SAP system<br />1291845 Display of mixture of Hebrew/Arabic, Latin, and digits<br />1391768 API for BIDI support in text processing<br />1489357 No support for code page 1810 in release 7.00 and higher<br /><br /><br /></p>\r\n<ol>15. General&#160; Descriptions and Restrictions</ol>\r\n<p>24860 Conversion: Physical MC ID -&gt; Transparent MC ID<br />42305 RSCPINST (NLS installation tool)<br />44788 How character sets are set up<br /><strong><a target=\"_blank\" href=\"/notes/73606\">73606 </a>Supported Languages and Code Pages</strong><br /><strong><a target=\"_blank\" href=\"/notes/79991\">79991 </a>Multi-Language Support / Unicode</strong><br />89384 Checking cluster tables with R3check<br />112065 Use of customer language 'Z1'<br />211521 Understanding the R3check output<br />308138 Truncated double byte characters in pop-up screens<br />379940 Unicode based mySAP availability<br />447519 Kernel patches for code pages, languages and locales<br />447596 Euro sign/Trademark sign: processing<br />449918 Reading archived data in Unicode systems<br />480671 The Text Language Flag of LANG Fields<br />497850 Condition maintenance reports and Unicode<br />503242 Supported languages<br />509898 BAPI enhancement concept and Unicode&#160;<br />510882 Alignment Corrections for structured data in containers<br />520991 Release strategy for the SAP CORE CEE add-on<br />531617 ITS 6.20 Release Notes<br />540911 Unicode-conditioned release restrictions for SAP R/3 / ERP<br />541299 Incorrect column layout on lists in Unicode systems<br />617472 Unicode system + EH&amp;S + permitted languages<br />621740 Updating the TCPUC and TCPUCATTR tables<br />624498 Downloading of lists in Unicode Systems<br />633265 SMTP PlugIn: Multi-codepage ability<br />634839 Text language for address versions<br />638844 SAP Query: EIS on Unicode systems<br />651229 MDMP Restrictions<br />669902 Oracle9i: Setting the National Character Set to UTF8<br />671730 Tables with obsolete SAP entries in R/3 Enterprise<br />679821 Check of kernel compilation mode<br />691407 No enhancements in SAPoffice<br />705447 Size of archive files<br />716200 Can characters disappear during code page conversion<br />726954 Private Use Areas in Unicode Systems<br />733416 EH&amp;S WWI and Unicode<br />734325 Table Comparison: MDMP and Unicode<br />742662 MS Word as Editor in SAPscript and Smart Forms<br />747615 Tool for converting files from one code page to<br /><strong><a target=\"_blank\" href=\"/notes/752835\">752835 </a>Usage of the file interface in UC systems</strong><br /><strong><a target=\"_blank\" href=\"/notes/752859\">752859 </a>sapiconv - a tool for converting the encoding of</strong><br />753334 Unicode Conversion: Problem in Japanese device types<br />753381 Classification of character damage<br />758870 Account statement: Character set when the file is<br />783299 Problems with new language keys in the TR SMLT<br />785091 SAPConsole: Language Support for Unicode Systems<br />799639&#160;&#160;IDES - General Information about the usage of IDES<br />823110 GTS: UNICODE and Multi Display Multi Processing<br />831959 Integrated ITS, problems with character conversion<br />837748 RFASLDPC: Incorrect processing under Unicode<br />887654 Unicode conversion KEA0<br />890302 SAP E-Recruiting for single code page systems only<br />895560 Restrictions for languages only available in Unicode system<br />899376 EH&amp;S WWI and Unicode for mySAP ERP<br />918614 ICU functions<br />920085 Additional languages for countries without SAP translation<br />923012 IPC standalone and unicode<br />952625 SORT AS TEXT: What does the collation sequence depend on?<br />956921 IDES ERP 2005 ECC 6.0<br />959874 Sending HTML e-mails in E-Recruiting<br />975768 Deprecation of Java features with non-Unicode<br />991481 Database is using utf-8 and not utf-16 ?<br />1021395 Restrictions of language activation on SAP Unicode<br />1059413 Data archived under MDMP is displayed incorrectly in Unicode<br />1071731 Unicode C-Programs and access to OS environment<br />1078295 Incomplete to upper cases in old code Pages<br />1088362 Deleted table T002T in Unicode systems<br />1125797 RW: Report output, double byte characters and Unicode<br />1134557 Language-independent search in Unicode system<br />1146910 Hong Kong Chinese in Unicode Systems<br />1149411 Support of GB-18030-2005&#160;&#160;&#160;&#160;(SAP-8402)<br />1157153 Language dependency of line break<br />1164883 RFDOPR10/RFDSLD00/RFKSLD00: Variants with special characters<br />1252407 Cyrillic code pages like KOI8-R or KOI8-U ?<br />1270827 Control Codes not filtered during input<br />1280236 RTL languages not known to the SAP system<br />1294430 How SAP handles unusual cases in UTF-8<br />1295002 Release Restrictions for SAP for Retail - 2009<br />1358929 Deprecation of SOA features with non-Unicode Backend<br />1359215 Technical prerequisites for using enterprise services<br />1375961 Use of ARCHUTF8 in Archivelink storage scenarios<br />1400236 Prerequisites for Xcelsius usage with NetWeaver BI/BW<br />1416449 Unknown language error for Unicode language after upgrade<br />1428423 Support for Unicode Normalization<br />1439009 Code page converter using wrong substitution<br />1455043 Invisible differences between characters<br />1503523 Indian Rupee Sign<br />1506363 Norwegian Collation in Unicode systems<br />1513496 Release Restrictions for SAP HANA 1.0<br />1595209 Is it possible to filter characters on a Unicode system?<br />1620200 This is an invalid SAPlogon codepage!<br />1685864 Locales used by Unicode executables<br />1688086 How to check if a system is Unicode<br /><strong><a target=\"_blank\" href=\"/notes/1790232\">1790232 </a>FAQ: Unicode - Technical FAQs</strong><br />1988840 - SQ01: Broken headings in Unicode systems<br />2020967 - \"Unicode-only\" characters in Material description in table MAKT do not display in MC44 graph&#160;<br />2067965 - WebGUI may not work in case of dialog RFC from unicode system to non unicode system<br />2298707 - Language dependent case mappings</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D028792)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D021965)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001319517/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001319517/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001319517/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001319517/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001319517/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001319517/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001319517/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001319517/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001319517/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2054812", "RefComponent": "BC-INS-MIG", "RefTitle": "How to delete non-unicode database when choosing \"One Server Method\" during a single code page conversion", "RefUrl": "/notes/2054812"}, {"RefNumber": "1912933", "RefComponent": "BC-I18-UNI", "RefTitle": "How to determine the language configuration of the non-Unicode source system after a Unicode conversion", "RefUrl": "/notes/1912933"}, {"RefNumber": "1896539", "RefComponent": "BC-I18", "RefTitle": "Compatibility and end of support to non-Unicode systems (FAQ)", "RefUrl": "/notes/1896539"}, {"RefNumber": "1882544", "RefComponent": "BC-I18-UNI", "RefTitle": "Program is not Unicode-compatible, according to its program attributes.", "RefUrl": "/notes/1882544"}, {"RefNumber": "1859302", "RefComponent": "BC-I18", "RefTitle": "How to find which code page(s) contain a specific character", "RefUrl": "/notes/1859302"}, {"RefNumber": "1797707", "RefComponent": "BC-I18", "RefTitle": "Is it possible to use Oracle tools to perform a Unicode conversion?", "RefUrl": "/notes/1797707"}, {"RefNumber": "999712", "RefComponent": "BC-CCM-PRN", "RefTitle": "PDF conversion for Unicode", "RefUrl": "/notes/999712"}, {"RefNumber": "996990", "RefComponent": "BC-I18-UNI", "RefTitle": "Exception List for transactions SPUMG and SPUM4", "RefUrl": "/notes/996990"}, {"RefNumber": "99507", "RefComponent": "LO-LIS", "RefTitle": "Update terminations after upgrade/client copy", "RefUrl": "/notes/99507"}, {"RefNumber": "994909", "RefComponent": "BC-I18-UNI", "RefTitle": "UMG_SUMG_VERIFY_R3LOAD_XML : What to do?", "RefUrl": "/notes/994909"}, {"RefNumber": "992956", "RefComponent": "BC-I18-UNI", "RefTitle": "Duplicate keys in table UMGPMDIT after Unicode conversion", "RefUrl": "/notes/992956"}, {"RefNumber": "991763", "RefComponent": "BC-MID-ALE", "RefTitle": "IDoc communication between Unicode and MDMP systems", "RefUrl": "/notes/991763"}, {"RefNumber": "991572", "RefComponent": "BC-MID-RFC", "RefTitle": "Code page settings for external RFC programs", "RefUrl": "/notes/991572"}, {"RefNumber": "991481", "RefComponent": "BC-I18-UNI", "RefTitle": "Database is using UTF-8 and not UTF-16 ?", "RefUrl": "/notes/991481"}, {"RefNumber": "991262", "RefComponent": "CRM-MSA", "RefTitle": "FAQ on Unicode enabled Mobile client 4.0", "RefUrl": "/notes/991262"}, {"RefNumber": "989338", "RefComponent": "FI-GL-GL-F", "RefTitle": "RFASLDPC: Character set conversion", "RefUrl": "/notes/989338"}, {"RefNumber": "987914", "RefComponent": "BC-ABA-TO", "RefTitle": "Adjustment of variants, Unicode problem", "RefUrl": "/notes/987914"}, {"RefNumber": "985296", "RefComponent": "BC-SRV-ADR", "RefTitle": "Sending multiple address vesions from MDMP to other systems", "RefUrl": "/notes/985296"}, {"RefNumber": "978244", "RefComponent": "BC-I18-UNI", "RefTitle": "CU&UC and NameTab errors for pools or clusters", "RefUrl": "/notes/978244"}, {"RefNumber": "977372", "RefComponent": "LO-VC-DEP", "RefTitle": "Dependencies corrupt after unicode conversion", "RefUrl": "/notes/977372"}, {"RefNumber": "975996", "RefComponent": "EHS-BD", "RefTitle": "Problems with applic. area C## during conversion to Unicode", "RefUrl": "/notes/975996"}, {"RefNumber": "975768", "RefComponent": "BC-I18", "RefTitle": "Deprecation of Java features with non-Unicode Backend", "RefUrl": "/notes/975768"}, {"RefNumber": "970892", "RefComponent": "FI-BL-PT-FO", "RefTitle": "Unicode: Specifying the code page in RFFO* programs", "RefUrl": "/notes/970892"}, {"RefNumber": "961026", "RefComponent": "EHS-BD-RDF-WWI", "RefTitle": "EH&S WWI: Errors in Unicode mode", "RefUrl": "/notes/961026"}, {"RefNumber": "960280", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/960280"}, {"RefNumber": "959874", "RefComponent": "PA-ER", "RefTitle": "Sending HTML e-mails in E-Recruiting", "RefUrl": "/notes/959874"}, {"RefNumber": "959698", "RefComponent": "BC-I18-UNI", "RefTitle": "Twin Upgrade & Unicode Conversion FAQ", "RefUrl": "/notes/959698"}, {"RefNumber": "956921", "RefComponent": "XX-IDES", "RefTitle": "IDES ERP 2005 ECC 6.0", "RefUrl": "/notes/956921"}, {"RefNumber": "954268", "RefComponent": "BC-INS-MIG", "RefTitle": "Optimization of export: Unsorted unloading", "RefUrl": "/notes/954268"}, {"RefNumber": "952625", "RefComponent": "BC-I18", "RefTitle": "SORT AS TEXT: What does the collation sequence depend on?", "RefUrl": "/notes/952625"}, {"RefNumber": "952514", "RefComponent": "BC-INS-MIG", "RefTitle": "Using the table splitting feature", "RefUrl": "/notes/952514"}, {"RefNumber": "952050", "RefComponent": "BC-SRV-SSF", "RefTitle": "LATIN fonts in CJK SmartStyles", "RefUrl": "/notes/952050"}, {"RefNumber": "948691", "RefComponent": "BC-ABA-LI", "RefTitle": "LIST_TO_ASCI from a Unicode system", "RefUrl": "/notes/948691"}, {"RefNumber": "948563", "RefComponent": "PPM-PRO-RES", "RefTitle": "E-mail for notification in Unicode systems", "RefUrl": "/notes/948563"}, {"RefNumber": "945255", "RefComponent": "BC-I18-UNI", "RefTitle": "SUMG reports on DOKTL during Unicode conversion", "RefUrl": "/notes/945255"}, {"RefNumber": "944778", "RefComponent": "BC-CCM-PRN", "RefTitle": "SAPGOF format from Unicode systems: ST command", "RefUrl": "/notes/944778"}, {"RefNumber": "940429", "RefComponent": "BC-SRV-SSF", "RefTitle": "Language independent printing in Smart Forms", "RefUrl": "/notes/940429"}, {"RefNumber": "939691", "RefComponent": "BC-I18-UNI", "RefTitle": "SPUMG: INDX scan fails for client dependent INDX tables", "RefUrl": "/notes/939691"}, {"RefNumber": "938738", "RefComponent": "BC-I18", "RefTitle": "Syslog CP6: still using ambiguous blended code page", "RefUrl": "/notes/938738"}, {"RefNumber": "9385", "RefComponent": "BC-DWB-DIC", "RefTitle": "What to do with QCM tables (conversion tables)", "RefUrl": "/notes/9385"}, {"RefNumber": "938374", "RefComponent": "BC-I18-UNI", "RefTitle": "Additional tools for MDMP --> Unicode conversion preparation", "RefUrl": "/notes/938374"}, {"RefNumber": "936441", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle settings for R3load based system copy", "RefUrl": "/notes/936441"}, {"RefNumber": "935239", "RefComponent": "BC-CTS-TMS", "RefTitle": "Adjusting TMS RFC destination after Unicode conversion", "RefUrl": "/notes/935239"}, {"RefNumber": "932779", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode conversion - analysis of nametab problems", "RefUrl": "/notes/932779"}, {"RefNumber": "931856", "RefComponent": "BC-ABA-LI", "RefTitle": "Spreadsheet download in a Unicode system", "RefUrl": "/notes/931856"}, {"RefNumber": "928909", "RefComponent": "BC-I18-UNI", "RefTitle": "Repair table data in SUMG in Unicode systems", "RefUrl": "/notes/928909"}, {"RefNumber": "928729", "RefComponent": "BC-I18-UNI", "RefTitle": "Combined Upgrade & Unicode Conversion (CU&UC)", "RefUrl": "/notes/928729"}, {"RefNumber": "926816", "RefComponent": "BC-FES-WGU", "RefTitle": "ITS Up/Download: ascii files in UTF-8 after up/download", "RefUrl": "/notes/926816"}, {"RefNumber": "926450", "RefComponent": "BC-DB-SDB", "RefTitle": "Short dump with error -4025 during connect to ASCII database", "RefUrl": "/notes/926450"}, {"RefNumber": "925170", "RefComponent": "BW-BEX-ET-WEB-AD", "RefTitle": "Web Application Designer NW04s: Saving: Non-Unicode ABAP", "RefUrl": "/notes/925170"}, {"RefNumber": "923012", "RefComponent": "CRM-IPC-DL", "RefTitle": "IPC standalone and unicode", "RefUrl": "/notes/923012"}, {"RefNumber": "922783", "RefComponent": "BC-CTS-LAN", "RefTitle": "Rescue language files for DE / EN", "RefUrl": "/notes/922783"}, {"RefNumber": "920831", "RefComponent": "BC-I18-UNI", "RefTitle": "RFC with inactive text language", "RefUrl": "/notes/920831"}, {"RefNumber": "920085", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/920085"}, {"RefNumber": "918614", "RefComponent": "BC-I18-UNI", "RefTitle": "ICU functions", "RefUrl": "/notes/918614"}, {"RefNumber": "914966", "RefComponent": "BC-DB-SDB", "RefTitle": "Database enlargement due to Unicode migration", "RefUrl": "/notes/914966"}, {"RefNumber": "911190", "RefComponent": "FI-BL-PT-FO", "RefTitle": "PMW: Code page specification when writing and downloading", "RefUrl": "/notes/911190"}, {"RefNumber": "910300", "RefComponent": "BC-FES-CTL", "RefTitle": "GridView:Truncation of text in Unicode systems.", "RefUrl": "/notes/910300"}, {"RefNumber": "908948", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/908948"}, {"RefNumber": "906031", "RefComponent": "BC-CCM-PRN", "RefTitle": "Information about cascading fonts device types", "RefUrl": "/notes/906031"}, {"RefNumber": "902083", "RefComponent": "CRM", "RefTitle": "Unicode Collection Note for CRM 4.0", "RefUrl": "/notes/902083"}, {"RefNumber": "901004", "RefComponent": "BC-I18", "RefTitle": "Conversion of ABAP spool requests to Unicode.", "RefUrl": "/notes/901004"}, {"RefNumber": "899376", "RefComponent": "EHS-BD-RDF-WWI", "RefTitle": "EH&S WWI and Unicode for mySAP ERP", "RefUrl": "/notes/899376"}, {"RefNumber": "896144", "RefComponent": "BC-I18", "RefTitle": "SAP ERP 6.0 Upgrade for R/3 or ERP MDMP Customers", "RefUrl": "/notes/896144"}, {"RefNumber": "895560", "RefComponent": "BC-I18-UNI", "RefTitle": "Support for languages only available in Unicode systems", "RefUrl": "/notes/895560"}, {"RefNumber": "89384", "RefComponent": "BC-DB-DBI", "RefTitle": "Checking cluster tables with R3check", "RefUrl": "/notes/89384"}, {"RefNumber": "890302", "RefComponent": "PA-ER", "RefTitle": "SAP E-Recruiting for single code page systems only", "RefUrl": "/notes/890302"}, {"RefNumber": "888210", "RefComponent": "BC-INS", "RefTitle": "NW 7.**: System copy (supplementary note)", "RefUrl": "/notes/888210"}, {"RefNumber": "887654", "RefComponent": "CO-PA", "RefTitle": "Unicode conversion", "RefUrl": "/notes/887654"}, {"RefNumber": "886571", "RefComponent": "BC-I18-UNI", "RefTitle": "RFC non-Unicode ./. Unicode with illegal text language", "RefUrl": "/notes/886571"}, {"RefNumber": "885441", "RefComponent": "BW-SYS-DB-MGR", "RefTitle": "Common Migration Errors", "RefUrl": "/notes/885441"}, {"RefNumber": "885343", "RefComponent": "BC", "RefTitle": "SAP System Landscape Copy", "RefUrl": "/notes/885343"}, {"RefNumber": "882865", "RefComponent": "SCM-APO-INT-CCR", "RefTitle": "CCR: Incorrect results when using parallel processing", "RefUrl": "/notes/882865"}, {"RefNumber": "881879", "RefComponent": "BC-DWB-TOO-WAB", "RefTitle": "Unicode conversion for table O2HTMLATTR", "RefUrl": "/notes/881879"}, {"RefNumber": "881781", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode/non-Unicode RFC language and code page assignment", "RefUrl": "/notes/881781"}, {"RefNumber": "881048", "RefComponent": "BC-DWB-DIC-F4", "RefTitle": "F4 Help: Unicode conversion for table DDSHPVAL50", "RefUrl": "/notes/881048"}, {"RefNumber": "880231", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/880231"}, {"RefNumber": "879479", "RefComponent": "BC-MID-ALE", "RefTitle": "IDoc file interface in Unicode system", "RefUrl": "/notes/879479"}, {"RefNumber": "875712", "RefComponent": "SCM-APO-PPS", "RefTitle": "Unicode conversion and planning logs", "RefUrl": "/notes/875712"}, {"RefNumber": "873789", "RefComponent": "CRM-MW-COM", "RefTitle": "Non XML data to mobile clients from Unicode CRM servers", "RefUrl": "/notes/873789"}, {"RefNumber": "873239", "RefComponent": "BC-CCM-PRN", "RefTitle": "Unicode: Support for cascading fonts when printing", "RefUrl": "/notes/873239"}, {"RefNumber": "871945", "RefComponent": "BC-I18-UNI", "RefTitle": "Fallback Code Page for Zn Languages in Unicode/nonU RFC", "RefUrl": "/notes/871945"}, {"RefNumber": "871541", "RefComponent": "BC-I18-UNI", "RefTitle": "Frequently used text patterns", "RefUrl": "/notes/871541"}, {"RefNumber": "867193", "RefComponent": "BC-I18-UNI", "RefTitle": "ABAP and kernel patches for CU&UC in 46C", "RefUrl": "/notes/867193"}, {"RefNumber": "858869", "RefComponent": "BC-DB-ORA", "RefTitle": "Desupport of multibyte character sets as of Oracle 10g", "RefUrl": "/notes/858869"}, {"RefNumber": "858242", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/858242"}, {"RefNumber": "857081", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode conversion: downtime estimate", "RefUrl": "/notes/857081"}, {"RefNumber": "856557", "RefComponent": "BC-I18-UNI", "RefTitle": "INDX-type table with UC data but non-UC code page info", "RefUrl": "/notes/856557"}, {"RefNumber": "855841", "RefComponent": "FI-BL-PT-FO", "RefTitle": "Payment media in Unicode systems", "RefUrl": "/notes/855841"}, {"RefNumber": "846194", "RefComponent": "CA-GTF-TS-WSI", "RefTitle": "OCI/OPI: Code pages and system language", "RefUrl": "/notes/846194"}, {"RefNumber": "845233", "RefComponent": "BC-I18-UNI", "RefTitle": "Use of Input Method Editors (IME) in Unicode System", "RefUrl": "/notes/845233"}, {"RefNumber": "844760", "RefComponent": "BC-SRV-SSF", "RefTitle": "UCCP for Smart Forms", "RefUrl": "/notes/844760"}, {"RefNumber": "842767", "RefComponent": "BC-CCM-PRN", "RefTitle": "Problems with old spool requests after Unicode conversion", "RefUrl": "/notes/842767"}, {"RefNumber": "838402", "RefComponent": "BC-I18-UNI", "RefTitle": "Problems within non-Unicode systems and  landscapes", "RefUrl": "/notes/838402"}, {"RefNumber": "837748", "RefComponent": "FI-GL-GL-F", "RefTitle": "RFASLDPC: Incorrect processing under Unicode", "RefUrl": "/notes/837748"}, {"RefNumber": "837327", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/837327"}, {"RefNumber": "837184", "RefComponent": "FI-SL-IS-A", "RefTitle": "Report Writer and Unicode conversion", "RefUrl": "/notes/837184"}, {"RefNumber": "837173", "RefComponent": "BC-I18-UNI", "RefTitle": "RADCUCNT in Unicode Conversion: Collective Note", "RefUrl": "/notes/837173"}, {"RefNumber": "83502", "RefComponent": "BC-CCM-PRN", "RefTitle": "Printing Support for Native Languages", "RefUrl": "/notes/83502"}, {"RefNumber": "834125", "RefComponent": "BC-FES-CTL", "RefTitle": "HTML Viewer: <PERSON>or<PERSON><PERSON> in Performance Assistant", "RefUrl": "/notes/834125"}, {"RefNumber": "833946", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/833946"}, {"RefNumber": "832918", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/832918"}, {"RefNumber": "831959", "RefComponent": "BC-FES-ITS", "RefTitle": "Integrated ITS, problems with character conversion", "RefUrl": "/notes/831959"}, {"RefNumber": "830502", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/830502"}, {"RefNumber": "828554", "RefComponent": "BW-SYS", "RefTitle": "Incorrect BW MDMP check when upgrading to Basis 6.40", "RefUrl": "/notes/828554"}, {"RefNumber": "827999", "RefComponent": "BC-MID-RFC", "RefTitle": "Error during transfer to MDMP tables with tRFC", "RefUrl": "/notes/827999"}, {"RefNumber": "827289", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Generation of Unicode runtime objects", "RefUrl": "/notes/827289"}, {"RefNumber": "823110", "RefComponent": "SLL-LEG", "RefTitle": "GTS: UNICODE and Multi Display Multi Processing (MDMP)", "RefUrl": "/notes/823110"}, {"RefNumber": "822634", "RefComponent": "BC-I18-BID", "RefTitle": "RTL SAPScript/Smartforms printing with embedded LTR texts", "RefUrl": "/notes/822634"}, {"RefNumber": "821971", "RefComponent": "BC-I18-UNI", "RefTitle": "Co-operation between RADCUCNT and R3load", "RefUrl": "/notes/821971"}, {"RefNumber": "819426", "RefComponent": "SRM-EBP", "RefTitle": "MDMP implementation not supported for mySAP SRM", "RefUrl": "/notes/819426"}, {"RefNumber": "818374", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Conversion of a Blended Code Page System", "RefUrl": "/notes/818374"}, {"RefNumber": "817010", "RefComponent": "FI-GL-GL-A", "RefTitle": "FB03/FBV3/FB01/JavaGUI/WebGUI: Problems with documt overview", "RefUrl": "/notes/817010"}, {"RefNumber": "814707", "RefComponent": "BC-I18-UNI", "RefTitle": "Troubleshooting for RFC connections Unicode/non-Unicode", "RefUrl": "/notes/814707"}, {"RefNumber": "813445", "RefComponent": "BC-I18-UNI", "RefTitle": "Documentation of the report UMG_POOL_TABLE", "RefUrl": "/notes/813445"}, {"RefNumber": "812821", "RefComponent": "BC-CCM-PRN", "RefTitle": "Cascading Font settings", "RefUrl": "/notes/812821"}, {"RefNumber": "811431", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/811431"}, {"RefNumber": "809279", "RefComponent": "BC-I18-UNI", "RefTitle": "RFC non-Unicode to Unicode with unknown text language", "RefUrl": "/notes/809279"}, {"RefNumber": "809034", "RefComponent": "BC-CCM-PRN", "RefTitle": "Support for Arabic with SAPWIN", "RefUrl": "/notes/809034"}, {"RefNumber": "808505", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "Secondary connections to Oracle database", "RefUrl": "/notes/808505"}, {"RefNumber": "808019", "RefComponent": "BW-BCT", "RefTitle": "Ldg master data from non-Unicode OLTP to Unicode BW", "RefUrl": "/notes/808019"}, {"RefNumber": "80727", "RefComponent": "BC-CTS-TLS", "RefTitle": "Transporting non-LATIN-1 texts", "RefUrl": "/notes/80727"}, {"RefNumber": "806554", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: I/O-intensive database operations", "RefUrl": "/notes/806554"}, {"RefNumber": "801949", "RefComponent": "CRM-MW-COM", "RefTitle": "Mobile Clients Sync Performance - Unicode servers", "RefUrl": "/notes/801949"}, {"RefNumber": "801416", "RefComponent": "BW-BCT-CGV-MIC", "RefTitle": "Activation of MIC content and extraction -problems and hints", "RefUrl": "/notes/801416"}, {"RefNumber": "79991", "RefComponent": "BC-I18", "RefTitle": "Multi-Language and Unicode support of SAP applications", "RefUrl": "/notes/79991"}, {"RefNumber": "799639", "RefComponent": "XX-IDES", "RefTitle": "IDES - General Information about the usage of IDES systems", "RefUrl": "/notes/799639"}, {"RefNumber": "795871", "RefComponent": "BC-CTS-ORG", "RefTitle": "Conversion of enqueue key of TLOCK* tables", "RefUrl": "/notes/795871"}, {"RefNumber": "794411", "RefComponent": "BC-MID-CON-JCO", "RefTitle": "Supported codepages of SAP Java Connector 2.1 and 6.x", "RefUrl": "/notes/794411"}, {"RefNumber": "793847", "RefComponent": "BC-MID-ALE", "RefTitle": "ALE system group (Unicode) with subsystem (non-Unicode)", "RefUrl": "/notes/793847"}, {"RefNumber": "793546", "RefComponent": "CRM-MSA", "RefTitle": "CRM Server Unicode Migration: Mobile Client Text Tables", "RefUrl": "/notes/793546"}, {"RefNumber": "793113", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle I/O configuration", "RefUrl": "/notes/793113"}, {"RefNumber": "791199", "RefComponent": "BC-I18-UNI", "RefTitle": "Default editor changed to MSWord in SAPscript and SmartForms", "RefUrl": "/notes/791199"}, {"RefNumber": "790485", "RefComponent": "BC-MID-RFC", "RefTitle": "RFC Problem Single Code Page, non-Unicode to Unicode System", "RefUrl": "/notes/790485"}, {"RefNumber": "790099", "RefComponent": "BC-CST-MM", "RefTitle": "R/3 Parameter Settings for Unicode conversion", "RefUrl": "/notes/790099"}, {"RefNumber": "788449", "RefComponent": "BC-ABA-LA", "RefTitle": "Byte-order Marks in UTF-8 Files", "RefUrl": "/notes/788449"}, {"RefNumber": "788239", "RefComponent": "BC-MID-RFC", "RefTitle": "Which language is used to run an RFC on the target system", "RefUrl": "/notes/788239"}, {"RefNumber": "785848", "RefComponent": "BC-INS-MIG", "RefTitle": "Hom./Het.System Copy SAP Web AS 6.40 SR1 Java", "RefUrl": "/notes/785848"}, {"RefNumber": "785564", "RefComponent": "BC-SRV-COM", "RefTitle": "Device types for SAPconnect", "RefUrl": "/notes/785564"}, {"RefNumber": "785091", "RefComponent": "BC-FES-CON", "RefTitle": "SAPConsole: language support for Unicode systems", "RefUrl": "/notes/785091"}, {"RefNumber": "784118", "RefComponent": "BC-INS-MIG-MMA", "RefTitle": "System Copy Tools for ABAP Systems", "RefUrl": "/notes/784118"}, {"RefNumber": "783299", "RefComponent": "BC-CTS-LAN", "RefTitle": "Problems with new language keys in the TR SMLT", "RefUrl": "/notes/783299"}, {"RefNumber": "778662", "RefComponent": "BC-SRV-ASF-CHD", "RefTitle": "Check and correction report for CDCLS table", "RefUrl": "/notes/778662"}, {"RefNumber": "778209", "RefComponent": "BC-SRV-ASF-CHD", "RefTitle": "Cluster records with missing PAGENO 0 in table CDCLS", "RefUrl": "/notes/778209"}, {"RefNumber": "776507", "RefComponent": "BC-CCM-PRN", "RefTitle": "SAPscript/SmartForms: Which fonts for which languages?", "RefUrl": "/notes/776507"}, {"RefNumber": "775189", "RefComponent": "BC-CCM-API-CSI-XBP", "RefTitle": "XBP interface and Unicode", "RefUrl": "/notes/775189"}, {"RefNumber": "775114", "RefComponent": "BC-I18", "RefTitle": "Problems during transport into a Unicode system", "RefUrl": "/notes/775114"}, {"RefNumber": "771333", "RefComponent": "BC-FES-CTL", "RefTitle": "Encoding isn't recognized in HTML control", "RefUrl": "/notes/771333"}, {"RefNumber": "771209", "RefComponent": "BW-SYS", "RefTitle": "NetWeaver 04: System copy (supplementary note)", "RefUrl": "/notes/771209"}, {"RefNumber": "766703", "RefComponent": "SD-BIL-IV-PC", "RefTitle": "FAQ: Credit card encryption in R/3 systems", "RefUrl": "/notes/766703"}, {"RefNumber": "765763", "RefComponent": "BC-FES-GUI", "RefTitle": "Setting the Upload/Download codepage for a SAPGUI connection", "RefUrl": "/notes/765763"}, {"RefNumber": "765543", "RefComponent": "BW-WHM-DST-BAPI", "RefTitle": "SAP BW Unicode and Staging BAPI - release information", "RefUrl": "/notes/765543"}, {"RefNumber": "765475", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Conversion: Troubleshooting", "RefUrl": "/notes/765475"}, {"RefNumber": "765439", "RefComponent": "CRM-MW", "RefTitle": "Collection Note for Unicode - CRM Middleware", "RefUrl": "/notes/765439"}, {"RefNumber": "764480", "RefComponent": "BC-I18", "RefTitle": "SAP WinGUI I18N Trouble Shooting", "RefUrl": "/notes/764480"}, {"RefNumber": "758870", "RefComponent": "FI-BL-PT-BA", "RefTitle": "Account statement: Character set when the file is uploaded", "RefUrl": "/notes/758870"}, {"RefNumber": "756535", "RefComponent": "BC-I18-UNI", "RefTitle": "SAP Vocabulary for Vocabulary Maintenance for SPUMG & SPUM4", "RefUrl": "/notes/756535"}, {"RefNumber": "756534", "RefComponent": "BC-I18-UNI", "RefTitle": "Automatic Assignment of Languages with Character Statistics", "RefUrl": "/notes/756534"}, {"RefNumber": "754311", "RefComponent": "BC-FES-OFFI", "RefTitle": "National special characters are not displayed properly.", "RefUrl": "/notes/754311"}, {"RefNumber": "753381", "RefComponent": "BC-I18", "RefTitle": "Classification of character damage", "RefUrl": "/notes/753381"}, {"RefNumber": "753334", "RefComponent": "BC-I18", "RefTitle": "Unicode Conversion: Problem in Japanese device types", "RefUrl": "/notes/753334"}, {"RefNumber": "752859", "RefComponent": "BC-I18", "RefTitle": "sapiconv - a tool for converting the encoding of files", "RefUrl": "/notes/752859"}, {"RefNumber": "752835", "RefComponent": "BC-I18-UNI", "RefTitle": "Usage of the file interfaces in Unicode systems", "RefUrl": "/notes/752835"}, {"RefNumber": "750219", "RefComponent": "BC-CCM-PRN-PVP-HP", "RefTitle": "Unicode UTF-8 printing with HP printers", "RefUrl": "/notes/750219"}, {"RefNumber": "747615", "RefComponent": "BC-I18-UNI", "RefTitle": "Tool for converting files from one code page to another", "RefUrl": "/notes/747615"}, {"RefNumber": "747036", "RefComponent": "BC-I18", "RefTitle": "mySAP ERP 2004 Upgrade for R/3 MDMP Customers", "RefUrl": "/notes/747036"}, {"RefNumber": "745030", "RefComponent": "BC-I18-UNI", "RefTitle": "MDMP - Unicode Interfaces: Solution Overview", "RefUrl": "/notes/745030"}, {"RefNumber": "742662", "RefComponent": "BC-SRV-SCR", "RefTitle": "MS Word as Editor in SAPscript and Smart Forms", "RefUrl": "/notes/742662"}, {"RefNumber": "741821", "RefComponent": "XX-SER-REL", "RefTitle": "Release limitations concerning SAP ERP 2004", "RefUrl": "/notes/741821"}, {"RefNumber": "740863", "RefComponent": "FIN-FSCM-BC-SB", "RefTitle": "RADCUCNT: Objects that cannot be generated.", "RefUrl": "/notes/740863"}, {"RefNumber": "738858", "RefComponent": "BC-I18-UNI", "RefTitle": "R3load for Unicode conversion", "RefUrl": "/notes/738858"}, {"RefNumber": "73606", "RefComponent": "BC-I18", "RefTitle": "Supported Languages and Code Pages", "RefUrl": "/notes/73606"}, {"RefNumber": "734325", "RefComponent": "BC-CUS-TOL-CST", "RefTitle": "Table comparison: MDMP and Unicode", "RefUrl": "/notes/734325"}, {"RefNumber": "733416", "RefComponent": "EHS-BD-RDF-WWI", "RefTitle": "EH&S WWI and Unicode", "RefUrl": "/notes/733416"}, {"RefNumber": "729348", "RefComponent": "BC-SRV-ASF-UOM", "RefTitle": "Unicode change for units of measure", "RefUrl": "/notes/729348"}, {"RefNumber": "728743", "RefComponent": "BC-DB-DB2", "RefTitle": "Series z: Release of DB2 V8 for SAP Components", "RefUrl": "/notes/728743"}, {"RefNumber": "726954", "RefComponent": "BC-I18-UNI", "RefTitle": "Private Use Areas in Unicode Systems", "RefUrl": "/notes/726954"}, {"RefNumber": "722193", "RefComponent": "BC-I18-UNI", "RefTitle": "RFC legacy non-Unicode clients and Unicode servers", "RefUrl": "/notes/722193"}, {"RefNumber": "721477", "RefComponent": "BC-I18-UNI", "RefTitle": "UCCHECK does not check any ZX* customer Includes", "RefUrl": "/notes/721477"}, {"RefNumber": "718324", "RefComponent": "CRM", "RefTitle": "Support restrictions for MDMP implementations of mySAP CRM", "RefUrl": "/notes/718324"}, {"RefNumber": "717776", "RefComponent": "BC-BW", "RefTitle": "Text transformation between Unicode and non-Unicode systems", "RefUrl": "/notes/717776"}, {"RefNumber": "716200", "RefComponent": "BC-I18", "RefTitle": "Can characters disappear during code page conversion", "RefUrl": "/notes/716200"}, {"RefNumber": "712958", "RefComponent": "BC-DWB-DIC-F4", "RefTitle": "Dumps when processing personal value list", "RefUrl": "/notes/712958"}, {"RefNumber": "710994", "RefComponent": "BC-I18-UNI", "RefTitle": "Layout of lists printed with LEXUT8 Unicode printer", "RefUrl": "/notes/710994"}, {"RefNumber": "710720", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/710720"}, {"RefNumber": "709019", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Inconsistency with table types after Unicode migration", "RefUrl": "/notes/709019"}, {"RefNumber": "708442", "RefComponent": "FI-SL-VSR", "RefTitle": "Incorrect generated programs in Unicode systems", "RefUrl": "/notes/708442"}, {"RefNumber": "706528", "RefComponent": "BC-MID-RFC", "RefTitle": "Changing Unicode setting for connection type \"L\"", "RefUrl": "/notes/706528"}, {"RefNumber": "705447", "RefComponent": "BC-CCM-ADK", "RefTitle": "Size of archive files", "RefUrl": "/notes/705447"}, {"RefNumber": "697625", "RefComponent": "BC-MID-ALE", "RefTitle": "ALE: Transport problems for Customizing data", "RefUrl": "/notes/697625"}, {"RefNumber": "695196", "RefComponent": "BC-DB-DBI", "RefTitle": "Error in the export for Unicode migration", "RefUrl": "/notes/695196"}, {"RefNumber": "693168", "RefComponent": "BC-UPG-NZ-DT", "RefTitle": "Minimized Downtime Service (MDS)", "RefUrl": "/notes/693168"}, {"RefNumber": "691585", "RefComponent": "CRM-MW-ADP", "RefTitle": "CRM Unicode / R3 MDMP Connection: User Exits", "RefUrl": "/notes/691585"}, {"RefNumber": "691407", "RefComponent": "BC-SRV-COM", "RefTitle": "No enhancements in SAPoffice", "RefUrl": "/notes/691407"}, {"RefNumber": "689951", "RefComponent": "BC-ABA-LA", "RefTitle": "Incompatible ABAP changes from Release 6.20 to 6.40", "RefUrl": "/notes/689951"}, {"RefNumber": "688089", "RefComponent": "BC-CCM-MON-SLG", "RefTitle": "SYSLOG: unreadable characters after change to UNICODE", "RefUrl": "/notes/688089"}, {"RefNumber": "686898", "RefComponent": "CRM-MW-ADP", "RefTitle": "Data exchange with R/3 back end that uses several code pages", "RefUrl": "/notes/686898"}, {"RefNumber": "684332", "RefComponent": "BC-DWB-TOO-ABA", "RefTitle": "Unicode system: Populating the TRDIR-RLOAD language key", "RefUrl": "/notes/684332"}, {"RefNumber": "682783", "RefComponent": "PA", "RefTitle": "Default Unicode conversion code page for HR tables", "RefUrl": "/notes/682783"}, {"RefNumber": "680695", "RefComponent": "LO-MD-BP", "RefTitle": "Unicode conversion in tables LFA1, KNA1 and KNVK", "RefUrl": "/notes/680695"}, {"RefNumber": "679821", "RefComponent": "BC-I18", "RefTitle": "Check of kernel compilation mode (Unicode/Non-Unicode)", "RefUrl": "/notes/679821"}, {"RefNumber": "679456", "RefComponent": "BC-I18-UNI", "RefTitle": "Reducing data volume before Unicode Conversion", "RefUrl": "/notes/679456"}, {"RefNumber": "675395", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/675395"}, {"RefNumber": "674882", "RefComponent": "CO-PA-MD", "RefTitle": "Upgrade: Syntax error in program RKE5XXXX", "RefUrl": "/notes/674882"}, {"RefNumber": "674371", "RefComponent": "BW-WHM-DST", "RefTitle": "Replicating application components in Unicode BW", "RefUrl": "/notes/674371"}, {"RefNumber": "673941", "RefComponent": "BC-SRV-ADR", "RefTitle": "Unicode conversion for address tables (ADRC, ADRP)", "RefUrl": "/notes/673941"}, {"RefNumber": "673533", "RefComponent": "BC-BW", "RefTitle": "Alignmnt problem during extraction into/from Unicode systems", "RefUrl": "/notes/673533"}, {"RefNumber": "672835", "RefComponent": "BC-I18", "RefTitle": "Textflags could cause problems during Unicode conversion", "RefUrl": "/notes/672835"}, {"RefNumber": "671730", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/671730"}, {"RefNumber": "669902", "RefComponent": "BC-DB-ORA", "RefTitle": "Setting the national character set to UTF8", "RefUrl": "/notes/669902"}, {"RefNumber": "665142", "RefComponent": "BC-I18-UNI", "RefTitle": "Reducing the check quantity in UCCHECK", "RefUrl": "/notes/665142"}, {"RefNumber": "663089", "RefComponent": "BC-MID-RFC", "RefTitle": "RFC bit options in transaction SM59", "RefUrl": "/notes/663089"}, {"RefNumber": "662215", "RefComponent": "BC-I18-UNI", "RefTitle": "Corrections and enhancements for transactions SPUMG and SUMG", "RefUrl": "/notes/662215"}, {"RefNumber": "660873", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/660873"}, {"RefNumber": "658095", "RefComponent": "BC-INS-MIG", "RefTitle": "R3load: Unicode conversion of cluster tables", "RefUrl": "/notes/658095"}, {"RefNumber": "656350", "RefComponent": "BC-MID-ALE", "RefTitle": "Master Data Transfer UNICODE <==> MDMP Systems with ALE", "RefUrl": "/notes/656350"}, {"RefNumber": "651497", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/651497"}, {"RefNumber": "651229", "RefComponent": "BC-I18", "RefTitle": "MDMP Restrictions", "RefUrl": "/notes/651229"}, {"RefNumber": "647495", "RefComponent": "BC-I18-UNI", "RefTitle": "RFC for Unicode ./. non-Unicode Connections", "RefUrl": "/notes/647495"}, {"RefNumber": "643813", "RefComponent": "BW-SYS", "RefTitle": "Composite SAP note - BW Unicode", "RefUrl": "/notes/643813"}, {"RefNumber": "638844", "RefComponent": "BC-SRV-QUE", "RefTitle": "SAP Query: EIS on Unicode systems", "RefUrl": "/notes/638844"}, {"RefNumber": "638357", "RefComponent": "BC-CTS", "RefTitle": "Transporting between Unicode and non-Unicode systems", "RefUrl": "/notes/638357"}, {"RefNumber": "634839", "RefComponent": "BC-SRV-ADR", "RefTitle": "Text language for address versions", "RefUrl": "/notes/634839"}, {"RefNumber": "633265", "RefComponent": "BC-SRV-COM", "RefTitle": "SMTP PlugIn: Multi-codepage ability", "RefUrl": "/notes/633265"}, {"RefNumber": "632357", "RefComponent": "BC-DB-LCA", "RefTitle": "Backup of data from liveCache for SCM 4.0 and higher SCM releases", "RefUrl": "/notes/632357"}, {"RefNumber": "627764", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Unicode migration: table pools inconsistent after conversion", "RefUrl": "/notes/627764"}, {"RefNumber": "624498", "RefComponent": "BC-ABA-LI", "RefTitle": "Downloading of lists in Unicode system", "RefUrl": "/notes/624498"}, {"RefNumber": "621740", "RefComponent": "BC-I18-UNI", "RefTitle": "Updating the TCPUC and TCPUCATTR tables", "RefUrl": "/notes/621740"}, {"RefNumber": "620253", "RefComponent": "BC-SRV-ADR", "RefTitle": "Problems when you transport Customizing addresses (6.20)", "RefUrl": "/notes/620253"}, {"RefNumber": "617472", "RefComponent": "BC-I18", "RefTitle": "Unicode system + EH&S + permitted languages", "RefUrl": "/notes/617472"}, {"RefNumber": "617444", "RefComponent": "BC-INS", "RefTitle": "Separate SCHEMA ID for database schema and tablespace name", "RefUrl": "/notes/617444"}, {"RefNumber": "615864", "RefComponent": "EHS-BD-SIS", "RefTitle": "Excel output: Umlauts are not displayed correctly", "RefUrl": "/notes/615864"}, {"RefNumber": "615746", "RefComponent": "BC-FES-GUI", "RefTitle": "Incorrect HTML data at front end against Unicode R/3", "RefUrl": "/notes/615746"}, {"RefNumber": "614550", "RefComponent": "BC-I18", "RefTitle": "Troubleshooting BC-I18", "RefUrl": "/notes/614550"}, {"RefNumber": "613389", "RefComponent": "BC-MID-ALE", "RefTitle": "ALE SAP system group with Unicode systems", "RefUrl": "/notes/613389"}, {"RefNumber": "609054", "RefComponent": "BC-BW", "RefTitle": "Extractors: Deleting generated programs before the upgrade", "RefUrl": "/notes/609054"}, {"RefNumber": "606359", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle National Language Support (NLS)", "RefUrl": "/notes/606359"}, {"RefNumber": "600560", "RefComponent": "BC-CST-GW", "RefTitle": "CPICSDK restrictions with Unicode", "RefUrl": "/notes/600560"}, {"RefNumber": "594356", "RefComponent": "BC-FES-GRA", "RefTitle": "Function modules ws_upload and ws_download are obsolete", "RefUrl": "/notes/594356"}, {"RefNumber": "589701", "RefComponent": "BW-SYS", "RefTitle": "Known errors in BW 3.1 Content Unicode", "RefUrl": "/notes/589701"}, {"RefNumber": "589159", "RefComponent": "BC-SRV-ADR", "RefTitle": "Incorrect entries in matchcode fields in ADRC and ADRP", "RefUrl": "/notes/589159"}, {"RefNumber": "588480", "RefComponent": "BW-SYS", "RefTitle": "Release restrictions for Unicode in SAP BW 3.x", "RefUrl": "/notes/588480"}, {"RefNumber": "587150", "RefComponent": "BC-I18-BID", "RefTitle": "Support of Arabic-script languages", "RefUrl": "/notes/587150"}, {"RefNumber": "583546", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode demo programs", "RefUrl": "/notes/583546"}, {"RefNumber": "578944", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/578944"}, {"RefNumber": "573044", "RefComponent": "PA", "RefTitle": "Unicode conversion HR", "RefUrl": "/notes/573044"}, {"RefNumber": "567048", "RefComponent": "BC-INS-AS4", "RefTitle": "INST: Unicode SAP R/3 Enterprise on IBM eServer iSeries", "RefUrl": "/notes/567048"}, {"RefNumber": "564142", "RefComponent": "BC-SRV-SSF", "RefTitle": "Composite Unicode SAP note (Smart Forms and SAPscript)", "RefUrl": "/notes/564142"}, {"RefNumber": "563975", "RefComponent": "BW-SYS", "RefTitle": "BW and MDMP/BW and blended code pages not supported", "RefUrl": "/notes/563975"}, {"RefNumber": "563417", "RefComponent": "BC-DWB-TOO-BOB", "RefTitle": "Unicode indicator for BOR object type programs", "RefUrl": "/notes/563417"}, {"RefNumber": "558746", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Better Oracle Data Dictionary BW Performance", "RefUrl": "/notes/558746"}, {"RefNumber": "553110", "RefComponent": "BC-I18", "RefTitle": "User Exits:  Behavior within non-Unicode R/3 Enterprise", "RefUrl": "/notes/553110"}, {"RefNumber": "552612", "RefComponent": "BC-I18", "RefTitle": "Differences between MS and SAP Code Pages", "RefUrl": "/notes/552612"}, {"RefNumber": "552464", "RefComponent": "BC", "RefTitle": "What is <PERSON> <PERSON><PERSON> / <PERSON> Endian? What Endian do I have?", "RefUrl": "/notes/552464"}, {"RefNumber": "551344", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Conversion Documentation", "RefUrl": "/notes/551344"}, {"RefNumber": "549143", "RefComponent": "BC", "RefTitle": "Searching for User-Exits which need Unicode-enabling", "RefUrl": "/notes/549143"}, {"RefNumber": "548769", "RefComponent": "BC-I18-UNI", "RefTitle": "BW Unicode Scenario: Dump CONVERSION_CODEPAGE_UNKNOWN", "RefUrl": "/notes/548769"}, {"RefNumber": "548016", "RefComponent": "BC-I18-UNI", "RefTitle": "Conversion to Unicode", "RefUrl": "/notes/548016"}, {"RefNumber": "547444", "RefComponent": "BC-I18", "RefTitle": "RFC Enhancement for Unicode ./. non-Unicode Connections", "RefUrl": "/notes/547444"}, {"RefNumber": "547314", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/547314"}, {"RefNumber": "545923", "RefComponent": "BW-SYS", "RefTitle": "FAQ: Tech. restrictions/customer namespace/permitted chars", "RefUrl": "/notes/545923"}, {"RefNumber": "544942", "RefComponent": "BC-FES-AIT-CTR", "RefTitle": "OCX Controls: Connection to Unicode backend", "RefUrl": "/notes/544942"}, {"RefNumber": "544623", "RefComponent": "BC-I18-UNI", "RefTitle": "New Installation of Unicode SAP systems", "RefUrl": "/notes/544623"}, {"RefNumber": "543715", "RefComponent": "BW-SYS-DB-MGR", "RefTitle": "BW Migrations and System Copies for BW 2.0B / 2.1 Content", "RefUrl": "/notes/543715"}, {"RefNumber": "541299", "RefComponent": "BC-I18-UNI", "RefTitle": "Incorrect column layout on lists in Unicode systems", "RefUrl": "/notes/541299"}, {"RefNumber": "540911", "RefComponent": "BC-I18", "RefTitle": "Unicode restrictions for R/3 Enterprise, ECC 5.0, ECC 6.0", "RefUrl": "/notes/540911"}, {"RefNumber": "539404", "RefComponent": "BC-SEC-SAL", "RefTitle": "FAQ | Answers to questions about the Security Audit Log (SAP versions prior to 750)", "RefUrl": "/notes/539404"}, {"RefNumber": "531617", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/531617"}, {"RefNumber": "520991", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for the SAP CORE CEE add-on", "RefUrl": "/notes/520991"}, {"RefNumber": "514967", "RefComponent": "BC-CUS-TOL-ALO", "RefTitle": "Table logging following change in the code page", "RefUrl": "/notes/514967"}, {"RefNumber": "513435", "RefComponent": "BC-I18", "RefTitle": "Windows code page setup for non-Unicode text files", "RefUrl": "/notes/513435"}, {"RefNumber": "510882", "RefComponent": "BC-I18-UNI", "RefTitle": "Alignment correction for structured data in containers", "RefUrl": "/notes/510882"}, {"RefNumber": "509898", "RefComponent": "BC-MID-API", "RefTitle": "BAPI enhancement concept and Unicode", "RefUrl": "/notes/509898"}, {"RefNumber": "503242", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/503242"}, {"RefNumber": "497850", "RefComponent": "SD-MD-CM", "RefTitle": "Condition maintenance reports and Unicode", "RefUrl": "/notes/497850"}, {"RefNumber": "493387", "RefComponent": "BC-ABA-LA", "RefTitle": "Potential effects of table- and structure - extensions", "RefUrl": "/notes/493387"}, {"RefNumber": "483715", "RefComponent": "CA-GTF-DRT", "RefTitle": "Unicode support in DART", "RefUrl": "/notes/483715"}, {"RefNumber": "480671", "RefComponent": "BC-I18", "RefTitle": "The Text Language Flag of LANG Fields", "RefUrl": "/notes/480671"}, {"RefNumber": "45548", "RefComponent": "BC-CTS-TLS", "RefTitle": "Transport of language-dependent objects", "RefUrl": "/notes/45548"}, {"RefNumber": "455195", "RefComponent": "BC-INS-MIG-TLA", "RefTitle": "R3load: Using TSK files", "RefUrl": "/notes/455195"}, {"RefNumber": "453182", "RefComponent": "BC-ABA-LA", "RefTitle": "Error in Unicode Program & Unicode Flag", "RefUrl": "/notes/453182"}, {"RefNumber": "449918", "RefComponent": "BC-CCM-ADK", "RefTitle": "Reading archived data in Unicode systems", "RefUrl": "/notes/449918"}, {"RefNumber": "449891", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Temporary database objects in BW 3.x", "RefUrl": "/notes/449891"}, {"RefNumber": "44788", "RefComponent": "BC-I18", "RefTitle": "How character sets are set up", "RefUrl": "/notes/44788"}, {"RefNumber": "447596", "RefComponent": "BC-I18", "RefTitle": "Euro sign/Trademark sign: processing", "RefUrl": "/notes/447596"}, {"RefNumber": "447519", "RefComponent": "BC-I18", "RefTitle": "Kernel patches for code pages, languages and locales", "RefUrl": "/notes/447519"}, {"RefNumber": "427561", "RefComponent": "BC-I18-UNI", "RefTitle": "Transaction SPUMG", "RefUrl": "/notes/427561"}, {"RefNumber": "42305", "RefComponent": "BC-I18", "RefTitle": "RSCPINST (I18N configuration tool)", "RefUrl": "/notes/42305"}, {"RefNumber": "423003", "RefComponent": "BC-CCM-PRN-DVM", "RefTitle": "Printers and Asian/Eastern European fonts/languages", "RefUrl": "/notes/423003"}, {"RefNumber": "413708", "RefComponent": "BC-MID-RFC", "RefTitle": "Current RFC library", "RefUrl": "/notes/413708"}, {"RefNumber": "3992", "RefComponent": "BC-ABA-LA", "RefTitle": "Purpose of the table INDX", "RefUrl": "/notes/3992"}, {"RefNumber": "382285", "RefComponent": "BC-I18", "RefTitle": "Some characters are converted to '#' in SAP WinGUI", "RefUrl": "/notes/382285"}, {"RefNumber": "379940", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode based mySAP availability", "RefUrl": "/notes/379940"}, {"RefNumber": "367676", "RefComponent": "BC-ABA-LA", "RefTitle": "Incompatible ABAP changes from Release 4.6 to 6.10", "RefUrl": "/notes/367676"}, {"RefNumber": "355771", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle: Explanation of the new tablespace layout", "RefUrl": "/notes/355771"}, {"RefNumber": "330267", "RefComponent": "BC-CTS", "RefTitle": "Transports between Basis Releases 4.6* and 6.*", "RefUrl": "/notes/330267"}, {"RefNumber": "323736", "RefComponent": "BC-CCM-PRN", "RefTitle": "Restrictions with \"PDF print\" through spooler", "RefUrl": "/notes/323736"}, {"RefNumber": "314676", "RefComponent": "BC-MID-BUS", "RefTitle": "Tips for processing data (UTF-8, Japanese)", "RefUrl": "/notes/314676"}, {"RefNumber": "308138", "RefComponent": "BC-I18", "RefTitle": "Truncated double byte characters in pop-up screens.", "RefUrl": "/notes/308138"}, {"RefNumber": "24860", "RefComponent": "BC-DB-DBI", "RefTitle": "Replacement of a matchcode ID by a search help function", "RefUrl": "/notes/24860"}, {"RefNumber": "215015", "RefComponent": "BC-CCM-PRN-PVP-LX", "RefTitle": "Unicode UTF-8 printing with Lexmark printers", "RefUrl": "/notes/215015"}, {"RefNumber": "211521", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/211521"}, {"RefNumber": "2033243", "RefComponent": "BC-I18-UNI", "RefTitle": "End of non-Unicode Support: Release Details", "RefUrl": "/notes/2033243"}, {"RefNumber": "1988840", "RefComponent": "BC-SRV-QUE", "RefTitle": "SQ01: Broken headings in Unicode systems", "RefUrl": "/notes/1988840"}, {"RefNumber": "197617", "RefComponent": "SD-BF-PD", "RefTitle": "Roll conversion partner functions", "RefUrl": "/notes/197617"}, {"RefNumber": "1968508", "RefComponent": "BC-I18-UNI", "RefTitle": "Release Change & Single Code Page Conversion to Unicode with DMO", "RefUrl": "/notes/1968508"}, {"RefNumber": "190669", "RefComponent": "BC-SRV-COM", "RefTitle": "Sending lists using SAPconnect", "RefUrl": "/notes/190669"}, {"RefNumber": "1904609", "RefComponent": "BC-I18", "RefTitle": "Restrictions and recommendations for Kazakh", "RefUrl": "/notes/1904609"}, {"RefNumber": "1784165", "RefComponent": "BC-I18", "RefTitle": "Turkish Lira Sign , U+20BA", "RefUrl": "/notes/1784165"}, {"RefNumber": "1753671", "RefComponent": "BC-I18", "RefTitle": "Restrictions and recommendations for Hindi", "RefUrl": "/notes/1753671"}, {"RefNumber": "1738494", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1738494"}, {"RefNumber": "173255", "RefComponent": "BW-WHM", "RefTitle": "Language problems in the BW System", "RefUrl": "/notes/173255"}, {"RefNumber": "173241", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Permitted characters in BW", "RefUrl": "/notes/173241"}, {"RefNumber": "1719798", "RefComponent": "FI-GL-GL-F", "RefTitle": "RFASLDPC: Systems with different byte orders", "RefUrl": "/notes/1719798"}, {"RefNumber": "1700052", "RefComponent": "HAN-DB", "RefTitle": "Secondary Connection to SAP HANA from non-Unicode System", "RefUrl": "/notes/1700052"}, {"RefNumber": "1612108", "RefComponent": "BC-SRV-KPR-CMS", "RefTitle": "Reports to correct compIds in content server", "RefUrl": "/notes/1612108"}, {"RefNumber": "158774", "RefComponent": "BC-I18", "RefTitle": "Incorrect case mappings in some locales", "RefUrl": "/notes/158774"}, {"RefNumber": "1583568", "RefComponent": "FI-AP-AP-B", "RefTitle": "RFZALI20: Unicode conversion for report variants", "RefUrl": "/notes/1583568"}, {"RefNumber": "1552498", "RefComponent": "BC-SRV-SCR", "RefTitle": "PDF Previewer", "RefUrl": "/notes/1552498"}, {"RefNumber": "1513496", "RefComponent": "XX-SER-GEN", "RefTitle": "Release Restrictions for SAP HANA 1.0", "RefUrl": "/notes/1513496"}, {"RefNumber": "1507738", "RefComponent": "EHS-BD-RDF-WWI", "RefTitle": "EH&S WWI: Reports in right to left languages", "RefUrl": "/notes/1507738"}, {"RefNumber": "1506363", "RefComponent": "BC-I18", "RefTitle": "Norwegian Collation in Unicode systems", "RefUrl": "/notes/1506363"}, {"RefNumber": "1503601", "RefComponent": "BC-I18-UNI", "RefTitle": "Error trace for reading Unicode files and OS environment", "RefUrl": "/notes/1503601"}, {"RefNumber": "1503523", "RefComponent": "BC-I18", "RefTitle": "Indian Rupee Sign , U+20B9", "RefUrl": "/notes/1503523"}, {"RefNumber": "1500340", "RefComponent": "BC-I18-UNI", "RefTitle": "CU&UC: Long running jobs in Additional Preparation Steps", "RefUrl": "/notes/1500340"}, {"RefNumber": "1499385", "RefComponent": "BC-ABA-LI", "RefTitle": "Lost layout after text download", "RefUrl": "/notes/1499385"}, {"RefNumber": "1497794", "RefComponent": "BC-I18-UNI", "RefTitle": "SPUMG: Adding words from the Reprocess Log to the Vocabulary", "RefUrl": "/notes/1497794"}, {"RefNumber": "1491018", "RefComponent": "BC-MID-ALE", "RefTitle": "IDoc: Report MASS_RSEOUT00 in Unicode systems", "RefUrl": "/notes/1491018"}, {"RefNumber": "1489515", "RefComponent": "PS-IS-LOG", "RefTitle": "RCN_SAVE_VARIANT: issue with object using a coding mask", "RefUrl": "/notes/1489515"}, {"RefNumber": "1489357", "RefComponent": "BC-I18-BID", "RefTitle": "No support for code page 1810 in release 7.00 and higher", "RefUrl": "/notes/1489357"}, {"RefNumber": "1478123", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "FAQ: SAPI Source system", "RefUrl": "/notes/1478123"}, {"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519"}, {"RefNumber": "1459066", "RefComponent": "BC-I18-UNI", "RefTitle": "SPUMG: code pages 1614 or 8341 as Global Fallback Code Page", "RefUrl": "/notes/1459066"}, {"RefNumber": "1457258", "RefComponent": "BC-I18-UNI", "RefTitle": "Correction instruction for the Additional Preparation Steps", "RefUrl": "/notes/1457258"}, {"RefNumber": "1455043", "RefComponent": "BC-I18", "RefTitle": "Invisible differences between characters", "RefUrl": "/notes/1455043"}, {"RefNumber": "1451275", "RefComponent": "BC-SRV-SCR", "RefTitle": "Correction program for BDSPHIO3 before a Unicode conversion", "RefUrl": "/notes/1451275"}, {"RefNumber": "1448532", "RefComponent": "PS-IS-LOG", "RefTitle": "PS-IS: Variants lead to terminations after Unicode migration", "RefUrl": "/notes/1448532"}, {"RefNumber": "1443472", "RefComponent": "BC-I18-UNI", "RefTitle": "SUMG: <PERSON><PERSON>", "RefUrl": "/notes/1443472"}, {"RefNumber": "1433470", "RefComponent": "BC-CCM-PRN", "RefTitle": "PDF converter: support for Vietnamese", "RefUrl": "/notes/1433470"}, {"RefNumber": "1431088", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1431088"}, {"RefNumber": "1429935", "RefComponent": "BC-I18-UNI", "RefTitle": "Report UMG_CHECK_APPLICATION_TABS", "RefUrl": "/notes/1429935"}, {"RefNumber": "1428549", "RefComponent": "BC-I18-UNI", "RefTitle": "SPUMG: Usage of .VOC files in the \"Vocabulary Import\"", "RefUrl": "/notes/1428549"}, {"RefNumber": "1428423", "RefComponent": "BC-I18-UNI", "RefTitle": "Support for Unicode Normalization", "RefUrl": "/notes/1428423"}, {"RefNumber": "1428028", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Conversion: check nametabs report UMG_CHECK_NAMETABS", "RefUrl": "/notes/1428028"}, {"RefNumber": "1426513", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Conversion: check nametab tables", "RefUrl": "/notes/1426513"}, {"RefNumber": "1422822", "RefComponent": "BC-I18-UNI", "RefTitle": "I18n Table Process Information", "RefUrl": "/notes/1422822"}, {"RefNumber": "1419531", "RefComponent": "BC-INS", "RefTitle": "R3load patch information", "RefUrl": "/notes/1419531"}, {"RefNumber": "1417727", "RefComponent": "BC-I18-UNI", "RefTitle": "New version of report UMG_ANALYZE_REPLOG", "RefUrl": "/notes/1417727"}, {"RefNumber": "1416449", "RefComponent": "BC-I18-UNI", "RefTitle": "Unknown language error for Unicode language after upgrade", "RefUrl": "/notes/1416449"}, {"RefNumber": "1413928", "RefComponent": "BC-DB-ORA", "RefTitle": "Index corruption/wrong results after rebuild index ONLINE", "RefUrl": "/notes/1413928"}, {"RefNumber": "1412123", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "Check NLS_LENGTH_SEMANTICS", "RefUrl": "/notes/1412123"}, {"RefNumber": "1400236", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "Prerequisites for Xcelsius usage with NetWeaver BI/BW", "RefUrl": "/notes/1400236"}, {"RefNumber": "1397782", "RefComponent": "BC-I18-UNI", "RefTitle": "SUMG Manual Repair: Save", "RefUrl": "/notes/1397782"}, {"RefNumber": "1392027", "RefComponent": "BC-I18-UNI", "RefTitle": "SUMG: Display number of log entries", "RefUrl": "/notes/1392027"}, {"RefNumber": "1391768", "RefComponent": "BC-I18-BID", "RefTitle": "API for BIDI support in text processing", "RefUrl": "/notes/1391768"}, {"RefNumber": "1390229", "RefComponent": "FI-AP-AP-B", "RefTitle": "F110 ... Information concerning a UNICODE conversion", "RefUrl": "/notes/1390229"}, {"RefNumber": "1388649", "RefComponent": "FI-BL-PT-FO", "RefTitle": "Report RFPAYM_UNICODE_VARIANTS cross-client", "RefUrl": "/notes/1388649"}, {"RefNumber": "1375961", "RefComponent": "BC-SRV-ARL", "RefTitle": "Use of ARCHUTF8 in Archivelink storage scenarios", "RefUrl": "/notes/1375961"}, {"RefNumber": "1375438", "RefComponent": "FI-LOC-I18", "RefTitle": "Globalization Collection Note", "RefUrl": "/notes/1375438"}, {"RefNumber": "1373611", "RefComponent": "BC-I18-UNI", "RefTitle": "Unknown type in report UMG_SHOW_UCTAB during generation", "RefUrl": "/notes/1373611"}, {"RefNumber": "1368419", "RefComponent": "BC-CUS-TOL-ALO", "RefTitle": "MDMP to Unicode conversion,logs not displayed correctly", "RefUrl": "/notes/1368419"}, {"RefNumber": "136649", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Selective language import", "RefUrl": "/notes/136649"}, {"RefNumber": "1366006", "RefComponent": "BC-CUS-TOL-ALO", "RefTitle": "Change logs after conversion from MDMP to Unicode system", "RefUrl": "/notes/1366006"}, {"RefNumber": "1365764", "RefComponent": "FI-BL-PT-FO", "RefTitle": "Codepage of DME file", "RefUrl": "/notes/1365764"}, {"RefNumber": "1363394", "RefComponent": "IS-AFS", "RefTitle": "Wrong Master Language entries for AFS objects in REPOSRC", "RefUrl": "/notes/1363394"}, {"RefNumber": "1361970", "RefComponent": "BC-MID-RFC", "RefTitle": "Conversion problems during RFC communication", "RefUrl": "/notes/1361970"}, {"RefNumber": "1359215", "RefComponent": "XX-SER-REL", "RefTitle": "Technical prerequisites for using enterprise services", "RefUrl": "/notes/1359215"}, {"RefNumber": "1358929", "RefComponent": "BC-I18", "RefTitle": "Deprecation of SOA features with non-Unicode Backend", "RefUrl": "/notes/1358929"}, {"RefNumber": "1356472", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode conversion: Number of rows of table clusters changed", "RefUrl": "/notes/1356472"}, {"RefNumber": "1349413", "RefComponent": "BC-SRV-COM", "RefTitle": "OTF documents can no longer be used after Unicode migration", "RefUrl": "/notes/1349413"}, {"RefNumber": "1348055", "RefComponent": "BC-DB-DBI", "RefTitle": "Initial cluster records for Unicode migration", "RefUrl": "/notes/1348055"}, {"RefNumber": "1345543", "RefComponent": "BC-I18-UNI", "RefTitle": "SUMG Manual Repair: Log list is not scrollable", "RefUrl": "/notes/1345543"}, {"RefNumber": "1345121", "RefComponent": "BC-I18", "RefTitle": "I18n related profile parameters which should not be set in Unicode systems", "RefUrl": "/notes/1345121"}, {"RefNumber": "1339144", "RefComponent": "BC-I18-UNI", "RefTitle": "SUMG Automatic Repair: incomplete upload of split tables", "RefUrl": "/notes/1339144"}, {"RefNumber": "1338186", "RefComponent": "BC-I18-UNI", "RefTitle": "Language assignments are deleted when synchronizing package", "RefUrl": "/notes/1338186"}, {"RefNumber": "1330256", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "LOAD_PROGRAM_NOT_FOUND Job RDDMASGL Program RDD21DAT", "RefUrl": "/notes/1330256"}, {"RefNumber": "1322715", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode FAQs", "RefUrl": "/notes/1322715"}, {"RefNumber": "1320370", "RefComponent": "BC-I18-UNI", "RefTitle": "SUMG Manual Repair: ALV grid sort, filter, select options", "RefUrl": "/notes/1320370"}, {"RefNumber": "1318670", "RefComponent": "CO-PA", "RefTitle": "Cleaning table INDX(KE)", "RefUrl": "/notes/1318670"}, {"RefNumber": "1315753", "RefComponent": "BC-CUS-TOL-ALO", "RefTitle": "SCU3: Converted into Unicode system from MDMP: Missing logs", "RefUrl": "/notes/1315753"}, {"RefNumber": "1314039", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1314039"}, {"RefNumber": "1313947", "RefComponent": "BC-I18-UNI", "RefTitle": "SUMG Manual Repair: repair of INDX Log uses wrong rowid", "RefUrl": "/notes/1313947"}, {"RefNumber": "1310720", "RefComponent": "BC-I18-UNI", "RefTitle": "Collection Note for Unicode Systems w. different endianness", "RefUrl": "/notes/1310720"}, {"RefNumber": "1309804", "RefComponent": "BC-DB-DBI", "RefTitle": "SDBI_CLUSTER_CHECK: Function and error messages", "RefUrl": "/notes/1309804"}, {"RefNumber": "1309728", "RefComponent": "BC-DB-DBI", "RefTitle": "SDBI_CLUSTER_CHECK: Usage instructions", "RefUrl": "/notes/1309728"}, {"RefNumber": "1307982", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode nametab entries cannot be created", "RefUrl": "/notes/1307982"}, {"RefNumber": "1302042", "RefComponent": "CO-PC-IS", "RefTitle": "Cleanup for table INDX(KU)", "RefUrl": "/notes/1302042"}, {"RefNumber": "1299738", "RefComponent": "BC-CCM-PRN", "RefTitle": "Various problems with spool requests in Unicode systems", "RefUrl": "/notes/1299738"}, {"RefNumber": "129581", "RefComponent": "BC-I18", "RefTitle": "Euro sign: printing", "RefUrl": "/notes/129581"}, {"RefNumber": "1295322", "RefComponent": "BC-DWB-DIC", "RefTitle": "Nametab inconsistencies in table/view maintenance indicators", "RefUrl": "/notes/1295322"}, {"RefNumber": "1295002", "RefComponent": "XX-SER-GEN", "RefTitle": "Release Restrictions for SAP for Retail - 2009", "RefUrl": "/notes/1295002"}, {"RefNumber": "1294663", "RefComponent": "BC-SRV-SCR", "RefTitle": "RTF upload in Unicode systems", "RefUrl": "/notes/1294663"}, {"RefNumber": "1294430", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1294430"}, {"RefNumber": "1294414", "RefComponent": "BC-DWB-SEM", "RefTitle": "Cleaning up table INDX (VM/RT)", "RefUrl": "/notes/1294414"}, {"RefNumber": "1292125", "RefComponent": "KM-KW", "RefTitle": "Cleaning up the table INDX(IW)", "RefUrl": "/notes/1292125"}, {"RefNumber": "1291845", "RefComponent": "BC-I18-BID", "RefTitle": "Display of mixture of Hebrew/Arabic, Latin, and digits", "RefUrl": "/notes/1291845"}, {"RefNumber": "1291662", "RefComponent": "BC-SEC-USR", "RefTitle": "Cleanup of table INDX(PR)", "RefUrl": "/notes/1291662"}, {"RefNumber": "1280236", "RefComponent": "BC-I18-BID", "RefTitle": "RTL languages not known to the SAP system", "RefUrl": "/notes/1280236"}, {"RefNumber": "1278126", "RefComponent": "FI-BL-PT-FO", "RefTitle": "Problems with variants of PMW/change to Unicode", "RefUrl": "/notes/1278126"}, {"RefNumber": "1276242", "RefComponent": "BC-I18-UNI", "RefTitle": "UMGSTAT is missing entries, UMG_ADD_VOCABULARY", "RefUrl": "/notes/1276242"}, {"RefNumber": "1275317", "RefComponent": "PA-PA-RU", "RefTitle": "HR-RU: Incorrect characters in Russian HR tables", "RefUrl": "/notes/1275317"}, {"RefNumber": "1275149", "RefComponent": "BC-DWB-DIC-F4", "RefTitle": "Input help (F4): Preparing the Unicode conversion", "RefUrl": "/notes/1275149"}, {"RefNumber": "1275074", "RefComponent": "BC-CUS-TOL-ALO", "RefTitle": "SCU3: Change Logs - Short dump or '#' in results", "RefUrl": "/notes/1275074"}, {"RefNumber": "1274259", "RefComponent": "FI-BL-PT-FO", "RefTitle": "Payment medium before upgrade to Unicode-active system", "RefUrl": "/notes/1274259"}, {"RefNumber": "1270827", "RefComponent": "BC-I18", "RefTitle": "Control Codes not filtered during input", "RefUrl": "/notes/1270827"}, {"RefNumber": "1269873", "RefComponent": "BC-ABA-LA", "RefTitle": "ABAP programs continue to be generated", "RefUrl": "/notes/1269873"}, {"RefNumber": "1269686", "RefComponent": "CA-GTF-DRT", "RefTitle": "Reading DART extracts with different code pages", "RefUrl": "/notes/1269686"}, {"RefNumber": "1265171", "RefComponent": "CO-PA-SPP", "RefTitle": "KE1x-Short dump MESSAGE_TYPE_X KG 385 in RKE_BP_VARIANT_READ", "RefUrl": "/notes/1265171"}, {"RefNumber": "1261426", "RefComponent": "BC-SRV-SCR", "RefTitle": "Texts from non-Unicode are incorrect in Unicode", "RefUrl": "/notes/1261426"}, {"RefNumber": "1258722", "RefComponent": "BC-I18-BID", "RefTitle": "LTR Includes in RTL SAPScript/Smartforms documents", "RefUrl": "/notes/1258722"}, {"RefNumber": "1255556", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode conversion preparation: Container table DBTABLOG", "RefUrl": "/notes/1255556"}, {"RefNumber": "1255030", "RefComponent": "BC-I18-UNI", "RefTitle": "SUMG: Reprocess Log is not displayed", "RefUrl": "/notes/1255030"}, {"RefNumber": "1252407", "RefComponent": "BC-I18", "RefTitle": "Cyrillic code pages like KOI8-R or KOI8-U ?", "RefUrl": "/notes/1252407"}, {"RefNumber": "1248636", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1248636"}, {"RefNumber": "1247334", "RefComponent": "BC-I18-UNI", "RefTitle": "Performance of SUMG Repair Hints", "RefUrl": "/notes/1247334"}, {"RefNumber": "1244353", "RefComponent": "BC-I18-UNI", "RefTitle": "Authorization check in UM4_/UMG_ FINISH_PREPARATION", "RefUrl": "/notes/1244353"}, {"RefNumber": "1236565", "RefComponent": "BC-MID-RFC-SDK", "RefTitle": "NW RFC SDK: Logging on with non-ISO-Latin-1 user or password", "RefUrl": "/notes/1236565"}, {"RefNumber": "1232987", "RefComponent": "BC-SRV-SCR", "RefTitle": "Preceding and succeeding blank characters for include", "RefUrl": "/notes/1232987"}, {"RefNumber": "1232373", "RefComponent": "IS-DFS-PDR", "RefTitle": "Problem with Unicode conversion for UPS object /ISDFPS/FR", "RefUrl": "/notes/1232373"}, {"RefNumber": "1179067", "RefComponent": "BC-I18-UNI", "RefTitle": "Language dependent tables in CU&UC procedure", "RefUrl": "/notes/1179067"}, {"RefNumber": "1178671", "RefComponent": "BC-I18-UNI", "RefTitle": "Conversion of Views from non-Unicode to Unicode", "RefUrl": "/notes/1178671"}, {"RefNumber": "1177899", "RefComponent": "BC-I18-UNI", "RefTitle": "Huge amount of R3load Log(XML files) during Unicode export", "RefUrl": "/notes/1177899"}, {"RefNumber": "1176806", "RefComponent": "BC-MID-RFC", "RefTitle": "Language key for external IDoc processing using startrfc", "RefUrl": "/notes/1176806"}, {"RefNumber": "1173054", "RefComponent": "BC-SRV-SCR", "RefTitle": "SAPscript, Smart Forms: Incorrect character width in Unicode", "RefUrl": "/notes/1173054"}, {"RefNumber": "1164883", "RefComponent": "FI-AP-AP-D", "RefTitle": "RFDOPR10/RFDSLD00/RFKSLD00: Variants with special characters", "RefUrl": "/notes/1164883"}, {"RefNumber": "1159564", "RefComponent": "BC-FES-CTL", "RefTitle": "F4: Limitation in number of characters for the search string", "RefUrl": "/notes/1159564"}, {"RefNumber": "1157153", "RefComponent": "BC-I18", "RefTitle": "Language dependency of line break", "RefUrl": "/notes/1157153"}, {"RefNumber": "1153741", "RefComponent": "BC-DB-DBI", "RefTitle": "Including SDBI_CLUSTER_CHECK in SPUMG", "RefUrl": "/notes/1153741"}, {"RefNumber": "1151258", "RefComponent": "BC-SRV-COM", "RefTitle": "Error when sending Excel attachments", "RefUrl": "/notes/1151258"}, {"RefNumber": "1151257", "RefComponent": "BC-SRV-COM", "RefTitle": "Converting document content", "RefUrl": "/notes/1151257"}, {"RefNumber": "1150217", "RefComponent": "BC-FES-GUI", "RefTitle": "Showcase ENCODING: Editor bug - or - Codepage issue?", "RefUrl": "/notes/1150217"}, {"RefNumber": "1149417", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Conversion Experience Kit Documentation (Alpha Vers)", "RefUrl": "/notes/1149417"}, {"RefNumber": "1149411", "RefComponent": "BC-I18", "RefTitle": "Support of GB 18030-2005    (SAP-8402)", "RefUrl": "/notes/1149411"}, {"RefNumber": "1146910", "RefComponent": "BC-I18", "RefTitle": "Hong Kong Chinese in Unicode Systems", "RefUrl": "/notes/1146910"}, {"RefNumber": "1146480", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Conversion or File I/O \"break\" Japanese U+3231", "RefUrl": "/notes/1146480"}, {"RefNumber": "1145959", "RefComponent": "BC-CCM-PRN", "RefTitle": "SWINCF: Alignment of BASIC LATIN Text in CJK ABAP lists", "RefUrl": "/notes/1145959"}, {"RefNumber": "1145203", "RefComponent": "BC-I18", "RefTitle": "RFC connections MDMP R/3 system 4.5B or lower", "RefUrl": "/notes/1145203"}, {"RefNumber": "1143659", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1143659"}, {"RefNumber": "1143116", "RefComponent": "BC-CTS-ORG", "RefTitle": "Special language keys are missing in default language vector", "RefUrl": "/notes/1143116"}, {"RefNumber": "1142573", "RefComponent": "BC-I18-UNI", "RefTitle": "Reuse of language assignments in SPUM4 and SPUMG", "RefUrl": "/notes/1142573"}, {"RefNumber": "1142572", "RefComponent": "BC-I18-UNI", "RefTitle": "Classification of notes of the Unicode conversion tools", "RefUrl": "/notes/1142572"}, {"RefNumber": "1141801", "RefComponent": "BC-SRV-COM", "RefTitle": "SBWP: Euro character is lost after Unicode conversion", "RefUrl": "/notes/1141801"}, {"RefNumber": "1141769", "RefComponent": "CA-GTF-DRT", "RefTitle": "Reading old DART extract with the corresponding code page", "RefUrl": "/notes/1141769"}, {"RefNumber": "1139642", "RefComponent": "BC-I18-UNI", "RefTitle": "Hardware Requirements in Unicode Systems", "RefUrl": "/notes/1139642"}, {"RefNumber": "1139325", "RefComponent": "BC-I18-UNI", "RefTitle": "Procedures and tools to enable ABAP programs for Unicode", "RefUrl": "/notes/1139325"}, {"RefNumber": "1138099", "RefComponent": "BC-CCM-PRN", "RefTitle": "Cascading fonts: Support of Surrogate Pairs", "RefUrl": "/notes/1138099"}, {"RefNumber": "1136946", "RefComponent": "BC-SRV-FP", "RefTitle": "Private Use Area (PUA) in Interactive Forms by Adobe", "RefUrl": "/notes/1136946"}, {"RefNumber": "1134557", "RefComponent": "PA-ER", "RefTitle": "Language-independent search in Unicode system", "RefUrl": "/notes/1134557"}, {"RefNumber": "1134257", "RefComponent": "XX-CSC-RU-FI", "RefTitle": "VAT Return (Russia): Corrections for Unicode Systems", "RefUrl": "/notes/1134257"}, {"RefNumber": "1132495", "RefComponent": "BC-I18-UNI", "RefTitle": "Report UMG_DELETE_SSLOAD", "RefUrl": "/notes/1132495"}, {"RefNumber": "1130255", "RefComponent": "BC-I18-UNI", "RefTitle": "East Asian Character Width in the Unicode PUA", "RefUrl": "/notes/1130255"}, {"RefNumber": "1129173", "RefComponent": "BC-I18-UNI", "RefTitle": "Special SPUMG vocabulary used in CU&UC procedure", "RefUrl": "/notes/1129173"}, {"RefNumber": "1128673", "RefComponent": "BC-I18-UNI", "RefTitle": "Additional post conversion step UMG_ADJUST_I18N_SETTINGS", "RefUrl": "/notes/1128673"}, {"RefNumber": "1128672", "RefComponent": "BC-I18-UNI", "RefTitle": "Report UMG_CHECK_STXL", "RefUrl": "/notes/1128672"}, {"RefNumber": "1128457", "RefComponent": "BC-I18-UNI", "RefTitle": "Additional post conversion program UMG_SAVE_HISTORY", "RefUrl": "/notes/1128457"}, {"RefNumber": "1125797", "RefComponent": "FI-SL-IS-A", "RefTitle": "RW: Report output, double byte characters and Unicode", "RefUrl": "/notes/1125797"}, {"RefNumber": "112065", "RefComponent": "BC-I18", "RefTitle": "Using customer language 'Z1'", "RefUrl": "/notes/112065"}, {"RefNumber": "1118230", "RefComponent": "CRM-MW-COM", "RefTitle": "SAP CRM 4.0 Non-Unicode Mobile clients to Unicode", "RefUrl": "/notes/1118230"}, {"RefNumber": "1114085", "RefComponent": "BC-I18-UNI", "RefTitle": "Tutorial: Manual Maintenance of the System Vocabulary", "RefUrl": "/notes/1114085"}, {"RefNumber": "1114037", "RefComponent": "BC-CCM-PRN", "RefTitle": "SAPGOF in Unicode: Problems with missing font metrics", "RefUrl": "/notes/1114037"}, {"RefNumber": "1112206", "RefComponent": "BC-BMT-WFM-RUN", "RefTitle": "Converting the workflow destination to Unicode", "RefUrl": "/notes/1112206"}, {"RefNumber": "1109643", "RefComponent": "BC-SRV-SCR", "RefTitle": "PBWW, PB60 - Problems w/ long texts from non-Unicode systems", "RefUrl": "/notes/1109643"}, {"RefNumber": "1108855", "RefComponent": "BC-SRV-SSF", "RefTitle": "Mixing non Latin 1 and ASCII (English) content", "RefUrl": "/notes/1108855"}, {"RefNumber": "1100672", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "RADNTLANG as a tool for checking the text language indicator", "RefUrl": "/notes/1100672"}, {"RefNumber": "1097990", "RefComponent": "BC-CCM-PRN-DVM", "RefTitle": "List of Printer <PERSON><PERSON>or <PERSON> Notes", "RefUrl": "/notes/1097990"}, {"RefNumber": "1096903", "RefComponent": "BC-I18-UNI", "RefTitle": "Tutorial: \"Table splitting in SPUMG or SPUM4\"", "RefUrl": "/notes/1096903"}, {"RefNumber": "1095975", "RefComponent": "BC-CUS-TOL-ALO", "RefTitle": "SCU3 Restriction: # character in List Display of logs", "RefUrl": "/notes/1095975"}, {"RefNumber": "1088362", "RefComponent": "BC-CTS-LAN", "RefTitle": "Corrupted table T002T in Unicode systems", "RefUrl": "/notes/1088362"}, {"RefNumber": "1088209", "RefComponent": "BC-FES-GUI", "RefTitle": "Help for troubleshooting: Code page display problems", "RefUrl": "/notes/1088209"}, {"RefNumber": "1084953", "RefComponent": "BC-MID-RFC", "RefTitle": "Correct code page when receiving MDMP tables using RFC", "RefUrl": "/notes/1084953"}, {"RefNumber": "1082681", "RefComponent": "SV-SMG-DIA", "RefTitle": "ST: Support for E2E Diagnostics on non-Unicode ABAP system", "RefUrl": "/notes/1082681"}, {"RefNumber": "1078295", "RefComponent": "BC-I18", "RefTitle": "Incomplete to upper cases in old code pages", "RefUrl": "/notes/1078295"}, {"RefNumber": "1078263", "RefComponent": "BC-I18-UNI", "RefTitle": "SPUM4 - Reprocess/INDX Log: Word too long", "RefUrl": "/notes/1078263"}, {"RefNumber": "1077403", "RefComponent": "BC-DB-DBI", "RefTitle": "Cluster table check with SDBI_CLUSTER_CHECK", "RefUrl": "/notes/1077403"}, {"RefNumber": "1075316", "RefComponent": "BC-SRV-SCR", "RefTitle": "Problems with transaction PBWW on Unicode", "RefUrl": "/notes/1075316"}, {"RefNumber": "1071731", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode C-programs and access to OS environment", "RefUrl": "/notes/1071731"}, {"RefNumber": "1069209", "RefComponent": "BC-I18-UNI", "RefTitle": "SPUMG or SPUM4: Reprocess Log becomes too large", "RefUrl": "/notes/1069209"}, {"RefNumber": "1066952", "RefComponent": "BC-SRV-DX-LSM", "RefTitle": "Working with legacy data files in LSMW (Unicode, BOM, FTP)", "RefUrl": "/notes/1066952"}, {"RefNumber": "1066404", "RefComponent": "BC-DB-DBI", "RefTitle": "R3load, ORDER BY, Unicode conversion of cluster tables", "RefUrl": "/notes/1066404"}, {"RefNumber": "1066323", "RefComponent": "BC-MID-RFC", "RefTitle": "RFC add parameter to refuse mdmp with RFC library", "RefUrl": "/notes/1066323"}, {"RefNumber": "1063794", "RefComponent": "BC-DB-LCA", "RefTitle": "Heterogenous system copy", "RefUrl": "/notes/1063794"}, {"RefNumber": "1062976", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: Fast load with Db2 LOAD utility and USS named pipes (FIFOs)", "RefUrl": "/notes/1062976"}, {"RefNumber": "1062237", "RefComponent": "BC-I18", "RefTitle": "Transliteration of characters with SCP_REPLACE_STRANGE_CHARS", "RefUrl": "/notes/1062237"}, {"RefNumber": "1059413", "RefComponent": "BC-CCM-ADK", "RefTitle": "Data archived under MDMP is displayed incorrectly in Unicode", "RefUrl": "/notes/1059413"}, {"RefNumber": "1057289", "RefComponent": "BC-ABA-LA", "RefTitle": "Reasons for CX_SY_CONVERSION_CODEPAGE / CONVT_CODEPAGE", "RefUrl": "/notes/1057289"}, {"RefNumber": "1056170", "RefComponent": "CA-DMS-EUI", "RefTitle": "Easy DMS 6.0 SP07 Patch1 Release Note", "RefUrl": "/notes/1056170"}, {"RefNumber": "1056105", "RefComponent": "BC-I18-UNI", "RefTitle": "Using UCCHECK for 3rd party programs", "RefUrl": "/notes/1056105"}, {"RefNumber": "1055820", "RefComponent": "BC-DOC-TTL", "RefTitle": "Proposal Pool Conversion", "RefUrl": "/notes/1055820"}, {"RefNumber": "1055588", "RefComponent": "BC-DB-LCA", "RefTitle": "Upgrade to SCM 5.X or SCM 7.0x and Unicode conversion", "RefUrl": "/notes/1055588"}, {"RefNumber": "1055585", "RefComponent": "BC-I18-UNI", "RefTitle": "Texts on dynpros are displayed in the wrong language.", "RefUrl": "/notes/1055585"}, {"RefNumber": "1054852", "RefComponent": "BC-DB-MSS", "RefTitle": "Recommendations for migrations using Microsoft SQL Server", "RefUrl": "/notes/1054852"}, {"RefNumber": "1051576", "RefComponent": "BC-I18-UNI", "RefTitle": "Conversion of Single Code Page Systems to Unicode", "RefUrl": "/notes/1051576"}, {"RefNumber": "1045847", "RefComponent": "BC-DB-ORA-INS", "RefTitle": "ORACLE DIRECT PATH LOAD SUPPORT IN R3LOAD", "RefUrl": "/notes/1045847"}, {"RefNumber": "1043380", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "Efficient Table Splitting for Oracle Databases", "RefUrl": "/notes/1043380"}, {"RefNumber": "1042842", "RefComponent": "BC-CUS-TOL-ALO", "RefTitle": "Activity Logs: Unicode system converted from MDMP system", "RefUrl": "/notes/1042842"}, {"RefNumber": "1040674", "RefComponent": "BC-DB-DB2", "RefTitle": "R3ta in combination with unsorted unload", "RefUrl": "/notes/1040674"}, {"RefNumber": "1040014", "RefComponent": "MFG-MII-CON", "RefTitle": "Importing Unicode CSV from XMII into Microsoft Excel 2003", "RefUrl": "/notes/1040014"}, {"RefNumber": "1039256", "RefComponent": "CRM-MSA", "RefTitle": "New 4.0 SP12 Unicode Mobile client - post installation steps", "RefUrl": "/notes/1039256"}, {"RefNumber": "1038413", "RefComponent": "BC-CCM-PRN-DVM", "RefTitle": "Unicode SAPWIN device type for CJK font", "RefUrl": "/notes/1038413"}, {"RefNumber": "1037613", "RefComponent": "BC-I18-UNI", "RefTitle": "Concept of an MDMP -> Unicode conversion", "RefUrl": "/notes/1037613"}, {"RefNumber": "1036341", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode executables and corrupt UTF-8 in input files", "RefUrl": "/notes/1036341"}, {"RefNumber": "1034188", "RefComponent": "BC-I18-UNI", "RefTitle": "SPUMG: Table fields for language determination", "RefUrl": "/notes/1034188"}, {"RefNumber": "1032589", "RefComponent": "CRM-MSA", "RefTitle": "Unicode upgrade Note for SAP CRM Mobile client 4.0 SP12", "RefUrl": "/notes/1032589"}, {"RefNumber": "1027088", "RefComponent": "BC-CCM-PRN", "RefTitle": "Incorrect SAPscript output after Unicode conversion", "RefUrl": "/notes/1027088"}, {"RefNumber": "1025361", "RefComponent": "BC-MID-RFC-SDK", "RefTitle": "Installation, Support and Availability of the SAP NetWeaver RFC Library 7.20", "RefUrl": "/notes/1025361"}, {"RefNumber": "1024253", "RefComponent": "BC-I18-UNI", "RefTitle": "Matchcode regeneration in Unicode systems", "RefUrl": "/notes/1024253"}, {"RefNumber": "1023337", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1023337"}, {"RefNumber": "1022548", "RefComponent": "BC-FES-GRA", "RefTitle": "Graphics with restricted Unicode support", "RefUrl": "/notes/1022548"}, {"RefNumber": "1021586", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode conversion of non-Latin-1 Single Codepage System", "RefUrl": "/notes/1021586"}, {"RefNumber": "1021459", "RefComponent": "BC-MID-RFC", "RefTitle": "Conversion behavior of the RFC library", "RefUrl": "/notes/1021459"}, {"RefNumber": "1021395", "RefComponent": "BC-I18-UNI", "RefTitle": "Restrictions of language activation on SAP Unicode systems", "RefUrl": "/notes/1021395"}, {"RefNumber": "1019362", "RefComponent": "BC-I18-UNI", "RefTitle": "Very long run times during SPUMG scans", "RefUrl": "/notes/1019362"}, {"RefNumber": "1017138", "RefComponent": "BC-CTS-LAN", "RefTitle": "Correcting Customizing texts after Unicode conversion", "RefUrl": "/notes/1017138"}, {"RefNumber": "1017101", "RefComponent": "BC-ABA-XML", "RefTitle": "Encoding Problems with ABAP XML Processing", "RefUrl": "/notes/1017101"}, {"RefNumber": "1017064", "RefComponent": "BC-SRV-SCR", "RefTitle": "Incorrect SAPscript transport Unicode->Non-Unicode/PBWW", "RefUrl": "/notes/1017064"}, {"RefNumber": "1013433", "RefComponent": "BC-SRV-SCR", "RefTitle": "Restrictions on usage of ANDALE_x fonts", "RefUrl": "/notes/1013433"}, {"RefNumber": "1012025", "RefComponent": "BC-I18-UNI", "RefTitle": "MDMP to Unicode conversion: Restart of R3load export", "RefUrl": "/notes/1012025"}, {"RefNumber": "1010237", "RefComponent": "BC-DB-DBI", "RefTitle": "Growth of DYNPSOURCE table after Unicode migration", "RefUrl": "/notes/1010237"}, {"RefNumber": "1007073", "RefComponent": "BC-I18-UNI", "RefTitle": "Misalignment in Asian files written from a Unicode system", "RefUrl": "/notes/1007073"}, {"RefNumber": "1002845", "RefComponent": "BC-DWB-TOO-SCR", "RefTitle": "Graphic Layout Editor in Unicode systems (II)", "RefUrl": "/notes/1002845"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2803613", "RefComponent": "BC-CCM-MON-SLG", "RefTitle": "\"Missing text in TSL1T( id 甚羏 #, language E)\" -> Error with junk characters in SM21", "RefUrl": "/notes/2803613 "}, {"RefNumber": "2682242", "RefComponent": "IS-H", "RefTitle": "IS-H: Problems during Unicode conversion with the tables NPAT and NGPA", "RefUrl": "/notes/2682242 "}, {"RefNumber": "2494220", "RefComponent": "LO-SLC", "RefTitle": "SAP Configure Price And Quote (CPQ) Dataloader with non-unicode backend ERP", "RefUrl": "/notes/2494220 "}, {"RefNumber": "928729", "RefComponent": "BC-I18-UNI", "RefTitle": "Combined Upgrade & Unicode Conversion (CU&UC)", "RefUrl": "/notes/928729 "}, {"RefNumber": "1753671", "RefComponent": "BC-I18", "RefTitle": "Restrictions and recommendations for Hindi", "RefUrl": "/notes/1753671 "}, {"RefNumber": "662215", "RefComponent": "BC-I18-UNI", "RefTitle": "Corrections and enhancements for transactions SPUMG and SUMG", "RefUrl": "/notes/662215 "}, {"RefNumber": "1375438", "RefComponent": "FI-LOC-I18", "RefTitle": "Globalization Collection Note", "RefUrl": "/notes/1375438 "}, {"RefNumber": "888210", "RefComponent": "BC-INS", "RefTitle": "NW 7.**: System copy (supplementary note)", "RefUrl": "/notes/888210 "}, {"RefNumber": "1349413", "RefComponent": "BC-SRV-COM", "RefTitle": "OTF documents can no longer be used after Unicode migration", "RefUrl": "/notes/1349413 "}, {"RefNumber": "587150", "RefComponent": "BC-I18-BID", "RefTitle": "Support of Arabic-script languages", "RefUrl": "/notes/587150 "}, {"RefNumber": "1310720", "RefComponent": "BC-I18-UNI", "RefTitle": "Collection Note for Unicode Systems w. different endianness", "RefUrl": "/notes/1310720 "}, {"RefNumber": "1146480", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Conversion or File I/O \"break\" Japanese U+3231", "RefUrl": "/notes/1146480 "}, {"RefNumber": "539404", "RefComponent": "BC-SEC-SAL", "RefTitle": "FAQ | Answers to questions about the Security Audit Log (SAP versions prior to 750)", "RefUrl": "/notes/539404 "}, {"RefNumber": "1422822", "RefComponent": "BC-I18-UNI", "RefTitle": "I18n Table Process Information", "RefUrl": "/notes/1422822 "}, {"RefNumber": "1348055", "RefComponent": "BC-DB-DBI", "RefTitle": "Initial cluster records for Unicode migration", "RefUrl": "/notes/1348055 "}, {"RefNumber": "1409608", "RefComponent": "BC-OP-NT", "RefTitle": "Virtualization on Windows", "RefUrl": "/notes/1409608 "}, {"RefNumber": "855772", "RefComponent": "BC-I18-UNI", "RefTitle": "Distribution Monitor", "RefUrl": "/notes/855772 "}, {"RefNumber": "837173", "RefComponent": "BC-I18-UNI", "RefTitle": "RADCUCNT in Unicode Conversion: Collective Note", "RefUrl": "/notes/837173 "}, {"RefNumber": "852235", "RefComponent": "XX-SER-REL", "RefTitle": "Release Restrictions for SAP ERP 6.0", "RefUrl": "/notes/852235 "}, {"RefNumber": "741821", "RefComponent": "XX-SER-REL", "RefTitle": "Release limitations concerning SAP ERP 2004", "RefUrl": "/notes/741821 "}, {"RefNumber": "1904609", "RefComponent": "BC-I18", "RefTitle": "Restrictions and recommendations for Kazakh", "RefUrl": "/notes/1904609 "}, {"RefNumber": "1149411", "RefComponent": "BC-I18", "RefTitle": "Support of GB 18030-2005    (SAP-8402)", "RefUrl": "/notes/1149411 "}, {"RefNumber": "1054852", "RefComponent": "BC-DB-MSS", "RefTitle": "Recommendations for migrations using Microsoft SQL Server", "RefUrl": "/notes/1054852 "}, {"RefNumber": "1062976", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: Fast load with Db2 LOAD utility and USS named pipes (FIFOs)", "RefUrl": "/notes/1062976 "}, {"RefNumber": "520991", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for the SAP CORE CEE add-on", "RefUrl": "/notes/520991 "}, {"RefNumber": "822634", "RefComponent": "BC-I18-BID", "RefTitle": "RTL SAPScript/Smartforms printing with embedded LTR texts", "RefUrl": "/notes/822634 "}, {"RefNumber": "814707", "RefComponent": "BC-I18-UNI", "RefTitle": "Troubleshooting for RFC connections Unicode/non-Unicode", "RefUrl": "/notes/814707 "}, {"RefNumber": "656350", "RefComponent": "BC-MID-ALE", "RefTitle": "Master Data Transfer UNICODE <==> MDMP Systems with ALE", "RefUrl": "/notes/656350 "}, {"RefNumber": "784118", "RefComponent": "BC-INS-MIG-MMA", "RefTitle": "System Copy Tools for ABAP Systems", "RefUrl": "/notes/784118 "}, {"RefNumber": "42305", "RefComponent": "BC-I18", "RefTitle": "RSCPINST (I18N configuration tool)", "RefUrl": "/notes/42305 "}, {"RefNumber": "44788", "RefComponent": "BC-I18", "RefTitle": "How character sets are set up", "RefUrl": "/notes/44788 "}, {"RefNumber": "73606", "RefComponent": "BC-I18", "RefTitle": "Supported Languages and Code Pages", "RefUrl": "/notes/73606 "}, {"RefNumber": "80727", "RefComponent": "BC-CTS-TLS", "RefTitle": "Transporting non-LATIN-1 texts", "RefUrl": "/notes/80727 "}, {"RefNumber": "790099", "RefComponent": "BC-CST-MM", "RefTitle": "R/3 Parameter Settings for Unicode conversion", "RefUrl": "/notes/790099 "}, {"RefNumber": "812821", "RefComponent": "BC-CCM-PRN", "RefTitle": "Cascading Font settings", "RefUrl": "/notes/812821 "}, {"RefNumber": "548016", "RefComponent": "BC-I18-UNI", "RefTitle": "Conversion to Unicode", "RefUrl": "/notes/548016 "}, {"RefNumber": "447519", "RefComponent": "BC-I18", "RefTitle": "Kernel patches for code pages, languages and locales", "RefUrl": "/notes/447519 "}, {"RefNumber": "1429935", "RefComponent": "BC-I18-UNI", "RefTitle": "Report UMG_CHECK_APPLICATION_TABS", "RefUrl": "/notes/1429935 "}, {"RefNumber": "1428028", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Conversion: check nametabs report UMG_CHECK_NAMETABS", "RefUrl": "/notes/1428028 "}, {"RefNumber": "1043380", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "Efficient Table Splitting for Oracle Databases", "RefUrl": "/notes/1043380 "}, {"RefNumber": "765475", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Conversion: Troubleshooting", "RefUrl": "/notes/765475 "}, {"RefNumber": "693168", "RefComponent": "BC-UPG-NZ-DT", "RefTitle": "Minimized Downtime Service (MDS)", "RefUrl": "/notes/693168 "}, {"RefNumber": "842767", "RefComponent": "BC-CCM-PRN", "RefTitle": "Problems with old spool requests after Unicode conversion", "RefUrl": "/notes/842767 "}, {"RefNumber": "817010", "RefComponent": "FI-GL-GL-A", "RefTitle": "FB03/FBV3/FB01/JavaGUI/WebGUI: Problems with documt overview", "RefUrl": "/notes/817010 "}, {"RefNumber": "1001383", "RefComponent": "BC-I18-UNI", "RefTitle": "Distribution Monitor - Known problems and fixes", "RefUrl": "/notes/1001383 "}, {"RefNumber": "1322715", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode FAQs", "RefUrl": "/notes/1322715 "}, {"RefNumber": "99507", "RefComponent": "LO-LIS", "RefTitle": "Update terminations after upgrade/client copy", "RefUrl": "/notes/99507 "}, {"RefNumber": "1513496", "RefComponent": "XX-SER-GEN", "RefTitle": "Release Restrictions for SAP HANA 1.0", "RefUrl": "/notes/1513496 "}, {"RefNumber": "1426513", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Conversion: check nametab tables", "RefUrl": "/notes/1426513 "}, {"RefNumber": "547444", "RefComponent": "BC-I18", "RefTitle": "RFC Enhancement for Unicode ./. non-Unicode Connections", "RefUrl": "/notes/547444 "}, {"RefNumber": "996990", "RefComponent": "BC-I18-UNI", "RefTitle": "Exception List for transactions SPUMG and SPUM4", "RefUrl": "/notes/996990 "}, {"RefNumber": "1055820", "RefComponent": "BC-DOC-TTL", "RefTitle": "Proposal Pool Conversion", "RefUrl": "/notes/1055820 "}, {"RefNumber": "497850", "RefComponent": "SD-MD-CM", "RefTitle": "Condition maintenance reports and Unicode", "RefUrl": "/notes/497850 "}, {"RefNumber": "413708", "RefComponent": "BC-MID-RFC", "RefTitle": "Current RFC library", "RefUrl": "/notes/413708 "}, {"RefNumber": "1138099", "RefComponent": "BC-CCM-PRN", "RefTitle": "Cascading fonts: Support of Surrogate Pairs", "RefUrl": "/notes/1138099 "}, {"RefNumber": "1051576", "RefComponent": "BC-I18-UNI", "RefTitle": "Conversion of Single Code Page Systems to Unicode", "RefUrl": "/notes/1051576 "}, {"RefNumber": "1431088", "RefComponent": "FI-LOC-I18", "RefTitle": "Globalization FAQs", "RefUrl": "/notes/1431088 "}, {"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519 "}, {"RefNumber": "799639", "RefComponent": "XX-IDES", "RefTitle": "IDES - General Information about the usage of IDES systems", "RefUrl": "/notes/799639 "}, {"RefNumber": "1143659", "RefComponent": "PP-BD-RTG", "RefTitle": "Report RCPTRA01 : NON-UNICODE to UNICODE does not work", "RefUrl": "/notes/1143659 "}, {"RefNumber": "793113", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle I/O configuration", "RefUrl": "/notes/793113 "}, {"RefNumber": "551344", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Conversion Documentation", "RefUrl": "/notes/551344 "}, {"RefNumber": "632357", "RefComponent": "BC-DB-LCA", "RefTitle": "Backup of data from liveCache for SCM 4.0 and higher SCM releases", "RefUrl": "/notes/632357 "}, {"RefNumber": "1096903", "RefComponent": "BC-I18-UNI", "RefTitle": "Tutorial: \"Table splitting in SPUMG or SPUM4\"", "RefUrl": "/notes/1096903 "}, {"RefNumber": "1077403", "RefComponent": "BC-DB-DBI", "RefTitle": "Cluster table check with SDBI_CLUSTER_CHECK", "RefUrl": "/notes/1077403 "}, {"RefNumber": "112065", "RefComponent": "BC-I18", "RefTitle": "Using customer language 'Z1'", "RefUrl": "/notes/112065 "}, {"RefNumber": "857081", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode conversion: downtime estimate", "RefUrl": "/notes/857081 "}, {"RefNumber": "738858", "RefComponent": "BC-I18-UNI", "RefTitle": "R3load for Unicode conversion", "RefUrl": "/notes/738858 "}, {"RefNumber": "1299738", "RefComponent": "BC-CCM-PRN", "RefTitle": "Various problems with spool requests in Unicode systems", "RefUrl": "/notes/1299738 "}, {"RefNumber": "838402", "RefComponent": "BC-I18-UNI", "RefTitle": "Problems within non-Unicode systems and  landscapes", "RefUrl": "/notes/838402 "}, {"RefNumber": "1719798", "RefComponent": "FI-GL-GL-F", "RefTitle": "RFASLDPC: Systems with different byte orders", "RefUrl": "/notes/1719798 "}, {"RefNumber": "989338", "RefComponent": "FI-GL-GL-F", "RefTitle": "RFASLDPC: Character set conversion", "RefUrl": "/notes/989338 "}, {"RefNumber": "750219", "RefComponent": "BC-CCM-PRN-PVP-HP", "RefTitle": "Unicode UTF-8 printing with HP printers", "RefUrl": "/notes/750219 "}, {"RefNumber": "734325", "RefComponent": "BC-CUS-TOL-CST", "RefTitle": "Table comparison: MDMP and Unicode", "RefUrl": "/notes/734325 "}, {"RefNumber": "752859", "RefComponent": "BC-I18", "RefTitle": "sapiconv - a tool for converting the encoding of files", "RefUrl": "/notes/752859 "}, {"RefNumber": "808505", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "Secondary connections to Oracle database", "RefUrl": "/notes/808505 "}, {"RefNumber": "672835", "RefComponent": "BC-I18", "RefTitle": "Textflags could cause problems during Unicode conversion", "RefUrl": "/notes/672835 "}, {"RefNumber": "1295002", "RefComponent": "XX-SER-GEN", "RefTitle": "Release Restrictions for SAP for Retail - 2009", "RefUrl": "/notes/1295002 "}, {"RefNumber": "954268", "RefComponent": "BC-INS-MIG", "RefTitle": "Optimization of export: Unsorted unloading", "RefUrl": "/notes/954268 "}, {"RefNumber": "906031", "RefComponent": "BC-CCM-PRN", "RefTitle": "Information about cascading fonts device types", "RefUrl": "/notes/906031 "}, {"RefNumber": "447596", "RefComponent": "BC-I18", "RefTitle": "Euro sign/Trademark sign: processing", "RefUrl": "/notes/447596 "}, {"RefNumber": "1503523", "RefComponent": "BC-I18", "RefTitle": "Indian Rupee Sign , U+20B9", "RefUrl": "/notes/1503523 "}, {"RefNumber": "1784165", "RefComponent": "BC-I18", "RefTitle": "Turkish Lira Sign , U+20BA", "RefUrl": "/notes/1784165 "}, {"RefNumber": "3992", "RefComponent": "BC-ABA-LA", "RefTitle": "Purpose of the table INDX", "RefUrl": "/notes/3992 "}, {"RefNumber": "771209", "RefComponent": "BW-SYS", "RefTitle": "NetWeaver 04: System copy (supplementary note)", "RefUrl": "/notes/771209 "}, {"RefNumber": "1097990", "RefComponent": "BC-CCM-PRN-DVM", "RefTitle": "List of Printer <PERSON><PERSON>or <PERSON> Notes", "RefUrl": "/notes/1097990 "}, {"RefNumber": "1010237", "RefComponent": "BC-DB-DBI", "RefTitle": "Growth of DYNPSOURCE table after Unicode migration", "RefUrl": "/notes/1010237 "}, {"RefNumber": "638357", "RefComponent": "BC-CTS", "RefTitle": "Transporting between Unicode and non-Unicode systems", "RefUrl": "/notes/638357 "}, {"RefNumber": "793847", "RefComponent": "BC-MID-ALE", "RefTitle": "ALE system group (Unicode) with subsystem (non-Unicode)", "RefUrl": "/notes/793847 "}, {"RefNumber": "613389", "RefComponent": "BC-MID-ALE", "RefTitle": "ALE SAP system group with Unicode systems", "RefUrl": "/notes/613389 "}, {"RefNumber": "911190", "RefComponent": "FI-BL-PT-FO", "RefTitle": "PMW: Code page specification when writing and downloading", "RefUrl": "/notes/911190 "}, {"RefNumber": "540911", "RefComponent": "BC-I18", "RefTitle": "Unicode restrictions for R/3 Enterprise, ECC 5.0, ECC 6.0", "RefUrl": "/notes/540911 "}, {"RefNumber": "1062237", "RefComponent": "BC-I18", "RefTitle": "Transliteration of characters with SCP_REPLACE_STRANGE_CHARS", "RefUrl": "/notes/1062237 "}, {"RefNumber": "190669", "RefComponent": "BC-SRV-COM", "RefTitle": "Sending lists using SAPconnect", "RefUrl": "/notes/190669 "}, {"RefNumber": "514967", "RefComponent": "BC-CUS-TOL-ALO", "RefTitle": "Table logging following change in the code page", "RefUrl": "/notes/514967 "}, {"RefNumber": "1013433", "RefComponent": "BC-SRV-SCR", "RefTitle": "Restrictions on usage of ANDALE_x fonts", "RefUrl": "/notes/1013433 "}, {"RefNumber": "1173054", "RefComponent": "BC-SRV-SCR", "RefTitle": "SAPscript, Smart Forms: Incorrect character width in Unicode", "RefUrl": "/notes/1173054 "}, {"RefNumber": "1552498", "RefComponent": "BC-SRV-SCR", "RefTitle": "PDF Previewer", "RefUrl": "/notes/1552498 "}, {"RefNumber": "1232987", "RefComponent": "BC-SRV-SCR", "RefTitle": "Preceding and succeeding blank characters for include", "RefUrl": "/notes/1232987 "}, {"RefNumber": "1108855", "RefComponent": "BC-SRV-SSF", "RefTitle": "Mixing non Latin 1 and ASCII (English) content", "RefUrl": "/notes/1108855 "}, {"RefNumber": "844760", "RefComponent": "BC-SRV-SSF", "RefTitle": "UCCP for Smart Forms", "RefUrl": "/notes/844760 "}, {"RefNumber": "952050", "RefComponent": "BC-SRV-SSF", "RefTitle": "LATIN fonts in CJK SmartStyles", "RefUrl": "/notes/952050 "}, {"RefNumber": "1141801", "RefComponent": "BC-SRV-COM", "RefTitle": "SBWP: Euro character is lost after Unicode conversion", "RefUrl": "/notes/1141801 "}, {"RefNumber": "936441", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle settings for R3load based system copy", "RefUrl": "/notes/936441 "}, {"RefNumber": "1391768", "RefComponent": "BC-I18-BID", "RefTitle": "API for BIDI support in text processing", "RefUrl": "/notes/1391768 "}, {"RefNumber": "1055588", "RefComponent": "BC-DB-LCA", "RefTitle": "Upgrade to SCM 5.X or SCM 7.0x and Unicode conversion", "RefUrl": "/notes/1055588 "}, {"RefNumber": "1112206", "RefComponent": "BC-BMT-WFM-RUN", "RefTitle": "Converting the workflow destination to Unicode", "RefUrl": "/notes/1112206 "}, {"RefNumber": "680695", "RefComponent": "LO-MD-BP", "RefTitle": "Unicode conversion in tables LFA1, KNA1 and KNVK", "RefUrl": "/notes/680695 "}, {"RefNumber": "1413928", "RefComponent": "BC-DB-ORA", "RefTitle": "Index corruption/wrong results after rebuild index ONLINE", "RefUrl": "/notes/1413928 "}, {"RefNumber": "330267", "RefComponent": "BC-CTS", "RefTitle": "Transports between Basis Releases 4.6* and 6.*", "RefUrl": "/notes/330267 "}, {"RefNumber": "785848", "RefComponent": "BC-INS-MIG", "RefTitle": "Hom./Het.System Copy SAP Web AS 6.40 SR1 Java", "RefUrl": "/notes/785848 "}, {"RefNumber": "308138", "RefComponent": "BC-I18", "RefTitle": "Truncated double byte characters in pop-up screens.", "RefUrl": "/notes/308138 "}, {"RefNumber": "752835", "RefComponent": "BC-I18-UNI", "RefTitle": "Usage of the file interfaces in Unicode systems", "RefUrl": "/notes/752835 "}, {"RefNumber": "493387", "RefComponent": "BC-ABA-LA", "RefTitle": "Potential effects of table- and structure - extensions", "RefUrl": "/notes/493387 "}, {"RefNumber": "747615", "RefComponent": "BC-I18-UNI", "RefTitle": "Tool for converting files from one code page to another", "RefUrl": "/notes/747615 "}, {"RefNumber": "1007073", "RefComponent": "BC-I18-UNI", "RefTitle": "Misalignment in Asian files written from a Unicode system", "RefUrl": "/notes/1007073 "}, {"RefNumber": "583546", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode demo programs", "RefUrl": "/notes/583546 "}, {"RefNumber": "665142", "RefComponent": "BC-I18-UNI", "RefTitle": "Reducing the check quantity in UCCHECK", "RefUrl": "/notes/665142 "}, {"RefNumber": "1056105", "RefComponent": "BC-I18-UNI", "RefTitle": "Using UCCHECK for 3rd party programs", "RefUrl": "/notes/1056105 "}, {"RefNumber": "721477", "RefComponent": "BC-I18-UNI", "RefTitle": "UCCHECK does not check any ZX* customer Includes", "RefUrl": "/notes/721477 "}, {"RefNumber": "726954", "RefComponent": "BC-I18-UNI", "RefTitle": "Private Use Areas in Unicode Systems", "RefUrl": "/notes/726954 "}, {"RefNumber": "856557", "RefComponent": "BC-I18-UNI", "RefTitle": "INDX-type table with UC data but non-UC code page info", "RefUrl": "/notes/856557 "}, {"RefNumber": "939691", "RefComponent": "BC-I18-UNI", "RefTitle": "SPUMG: INDX scan fails for client dependent INDX tables", "RefUrl": "/notes/939691 "}, {"RefNumber": "1700052", "RefComponent": "HAN-DB", "RefTitle": "Secondary Connection to SAP HANA from non-Unicode System", "RefUrl": "/notes/1700052 "}, {"RefNumber": "453182", "RefComponent": "BC-ABA-LA", "RefTitle": "Error in Unicode Program & Unicode Flag", "RefUrl": "/notes/453182 "}, {"RefNumber": "215015", "RefComponent": "BC-CCM-PRN-PVP-LX", "RefTitle": "Unicode UTF-8 printing with Lexmark printers", "RefUrl": "/notes/215015 "}, {"RefNumber": "989116", "RefComponent": "BC-I18-UNI", "RefTitle": "Distribution Monitor: Troubleshooting Guide", "RefUrl": "/notes/989116 "}, {"RefNumber": "1457258", "RefComponent": "BC-I18-UNI", "RefTitle": "Correction instruction for the Additional Preparation Steps", "RefUrl": "/notes/1457258 "}, {"RefNumber": "1612108", "RefComponent": "BC-SRV-KPR-CMS", "RefTitle": "Reports to correct compIds in content server", "RefUrl": "/notes/1612108 "}, {"RefNumber": "806554", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: I/O-intensive database operations", "RefUrl": "/notes/806554 "}, {"RefNumber": "620253", "RefComponent": "BC-SRV-ADR", "RefTitle": "Problems when you transport Customizing addresses (6.20)", "RefUrl": "/notes/620253 "}, {"RefNumber": "509898", "RefComponent": "BC-MID-API", "RefTitle": "BAPI enhancement concept and Unicode", "RefUrl": "/notes/509898 "}, {"RefNumber": "728743", "RefComponent": "BC-DB-DB2", "RefTitle": "Series z: Release of DB2 V8 for SAP Components", "RefUrl": "/notes/728743 "}, {"RefNumber": "1179067", "RefComponent": "BC-I18-UNI", "RefTitle": "Language dependent tables in CU&UC procedure", "RefUrl": "/notes/1179067 "}, {"RefNumber": "1294663", "RefComponent": "BC-SRV-SCR", "RefTitle": "RTF upload in Unicode systems", "RefUrl": "/notes/1294663 "}, {"RefNumber": "1412123", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "Check NLS_LENGTH_SEMANTICS", "RefUrl": "/notes/1412123 "}, {"RefNumber": "1057289", "RefComponent": "BC-ABA-LA", "RefTitle": "Reasons for CX_SY_CONVERSION_CODEPAGE / CONVT_CODEPAGE", "RefUrl": "/notes/1057289 "}, {"RefNumber": "1489515", "RefComponent": "PS-IS-LOG", "RefTitle": "RCN_SAVE_VARIANT: issue with object using a coding mask", "RefUrl": "/notes/1489515 "}, {"RefNumber": "1244353", "RefComponent": "BC-I18-UNI", "RefTitle": "Authorization check in UM4_/UMG_ FINISH_PREPARATION", "RefUrl": "/notes/1244353 "}, {"RefNumber": "895560", "RefComponent": "BC-I18-UNI", "RefTitle": "Support for languages only available in Unicode systems", "RefUrl": "/notes/895560 "}, {"RefNumber": "382285", "RefComponent": "BC-I18", "RefTitle": "Some characters are converted to '#' in SAP WinGUI", "RefUrl": "/notes/382285 "}, {"RefNumber": "606359", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle National Language Support (NLS)", "RefUrl": "/notes/606359 "}, {"RefNumber": "617444", "RefComponent": "BC-INS", "RefTitle": "Separate SCHEMA ID for database schema and tablespace name", "RefUrl": "/notes/617444 "}, {"RefNumber": "952514", "RefComponent": "BC-INS-MIG", "RefTitle": "Using the table splitting feature", "RefUrl": "/notes/952514 "}, {"RefNumber": "544623", "RefComponent": "BC-I18-UNI", "RefTitle": "New Installation of Unicode SAP systems", "RefUrl": "/notes/544623 "}, {"RefNumber": "614550", "RefComponent": "BC-I18", "RefTitle": "Troubleshooting BC-I18", "RefUrl": "/notes/614550 "}, {"RefNumber": "896144", "RefComponent": "BC-I18", "RefTitle": "SAP ERP 6.0 Upgrade for R/3 or ERP MDMP Customers", "RefUrl": "/notes/896144 "}, {"RefNumber": "885343", "RefComponent": "BC", "RefTitle": "SAP System Landscape Copy", "RefUrl": "/notes/885343 "}, {"RefNumber": "1139642", "RefComponent": "BC-I18-UNI", "RefTitle": "Hardware Requirements in Unicode Systems", "RefUrl": "/notes/1139642 "}, {"RefNumber": "1247334", "RefComponent": "BC-I18-UNI", "RefTitle": "Performance of SUMG Repair Hints", "RefUrl": "/notes/1247334 "}, {"RefNumber": "1149417", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Conversion Experience Kit Documentation (Alpha Vers)", "RefUrl": "/notes/1149417 "}, {"RefNumber": "669902", "RefComponent": "BC-DB-ORA", "RefTitle": "Setting the national character set to UTF8", "RefUrl": "/notes/669902 "}, {"RefNumber": "1390229", "RefComponent": "FI-AP-AP-B", "RefTitle": "F110 ... Information concerning a UNICODE conversion", "RefUrl": "/notes/1390229 "}, {"RefNumber": "1583568", "RefComponent": "FI-AP-AP-B", "RefTitle": "RFZALI20: Unicode conversion for report variants", "RefUrl": "/notes/1583568 "}, {"RefNumber": "1433470", "RefComponent": "BC-CCM-PRN", "RefTitle": "PDF converter: support for Vietnamese", "RefUrl": "/notes/1433470 "}, {"RefNumber": "1359215", "RefComponent": "XX-SER-REL", "RefTitle": "Technical prerequisites for using enterprise services", "RefUrl": "/notes/1359215 "}, {"RefNumber": "1451275", "RefComponent": "BC-SRV-SCR", "RefTitle": "Correction program for BDSPHIO3 before a Unicode conversion", "RefUrl": "/notes/1451275 "}, {"RefNumber": "1428549", "RefComponent": "BC-I18-UNI", "RefTitle": "SPUMG: Usage of .VOC files in the \"Vocabulary Import\"", "RefUrl": "/notes/1428549 "}, {"RefNumber": "1361970", "RefComponent": "BC-MID-RFC", "RefTitle": "Conversion problems during RFC communication", "RefUrl": "/notes/1361970 "}, {"RefNumber": "1489357", "RefComponent": "BC-I18-BID", "RefTitle": "No support for code page 1810 in release 7.00 and higher", "RefUrl": "/notes/1489357 "}, {"RefNumber": "1345121", "RefComponent": "BC-I18", "RefTitle": "I18n related profile parameters which should not be set in Unicode systems", "RefUrl": "/notes/1345121 "}, {"RefNumber": "1280236", "RefComponent": "BC-I18-BID", "RefTitle": "RTL languages not known to the SAP system", "RefUrl": "/notes/1280236 "}, {"RefNumber": "1252407", "RefComponent": "BC-I18", "RefTitle": "Cyrillic code pages like KOI8-R or KOI8-U ?", "RefUrl": "/notes/1252407 "}, {"RefNumber": "679456", "RefComponent": "BC-I18-UNI", "RefTitle": "Reducing data volume before Unicode Conversion", "RefUrl": "/notes/679456 "}, {"RefNumber": "944778", "RefComponent": "BC-CCM-PRN", "RefTitle": "SAPGOF format from Unicode systems: ST command", "RefUrl": "/notes/944778 "}, {"RefNumber": "753381", "RefComponent": "BC-I18", "RefTitle": "Classification of character damage", "RefUrl": "/notes/753381 "}, {"RefNumber": "1142573", "RefComponent": "BC-I18-UNI", "RefTitle": "Reuse of language assignments in SPUM4 and SPUMG", "RefUrl": "/notes/1142573 "}, {"RefNumber": "871541", "RefComponent": "BC-I18-UNI", "RefTitle": "Frequently used text patterns", "RefUrl": "/notes/871541 "}, {"RefNumber": "45548", "RefComponent": "BC-CTS-TLS", "RefTitle": "Transport of language-dependent objects", "RefUrl": "/notes/45548 "}, {"RefNumber": "935239", "RefComponent": "BC-CTS-TMS", "RefTitle": "Adjusting TMS RFC destination after Unicode conversion", "RefUrl": "/notes/935239 "}, {"RefNumber": "1506363", "RefComponent": "BC-I18", "RefTitle": "Norwegian Collation in Unicode systems", "RefUrl": "/notes/1506363 "}, {"RefNumber": "948691", "RefComponent": "BC-ABA-LI", "RefTitle": "LIST_TO_ASCI from a Unicode system", "RefUrl": "/notes/948691 "}, {"RefNumber": "9385", "RefComponent": "BC-DWB-DIC", "RefTitle": "What to do with QCM tables (conversion tables)", "RefUrl": "/notes/9385 "}, {"RefNumber": "573044", "RefComponent": "PA", "RefTitle": "Unicode conversion HR", "RefUrl": "/notes/573044 "}, {"RefNumber": "867193", "RefComponent": "BC-I18-UNI", "RefTitle": "ABAP and kernel patches for CU&UC in 46C", "RefUrl": "/notes/867193 "}, {"RefNumber": "1269686", "RefComponent": "CA-GTF-DRT", "RefTitle": "Reading DART extracts with different code pages", "RefUrl": "/notes/1269686 "}, {"RefNumber": "1507738", "RefComponent": "EHS-BD-RDF-WWI", "RefTitle": "EH&S WWI: Reports in right to left languages", "RefUrl": "/notes/1507738 "}, {"RefNumber": "1045847", "RefComponent": "BC-DB-ORA-INS", "RefTitle": "ORACLE DIRECT PATH LOAD SUPPORT IN R3LOAD", "RefUrl": "/notes/1045847 "}, {"RefNumber": "1307982", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode nametab entries cannot be created", "RefUrl": "/notes/1307982 "}, {"RefNumber": "765439", "RefComponent": "CRM-MW", "RefTitle": "Collection Note for Unicode - CRM Middleware", "RefUrl": "/notes/765439 "}, {"RefNumber": "1037613", "RefComponent": "BC-I18-UNI", "RefTitle": "Concept of an MDMP -> Unicode conversion", "RefUrl": "/notes/1037613 "}, {"RefNumber": "1428423", "RefComponent": "BC-I18-UNI", "RefTitle": "Support for Unicode Normalization", "RefUrl": "/notes/1428423 "}, {"RefNumber": "899376", "RefComponent": "EHS-BD-RDF-WWI", "RefTitle": "EH&S WWI and Unicode for mySAP ERP", "RefUrl": "/notes/899376 "}, {"RefNumber": "733416", "RefComponent": "EHS-BD-RDF-WWI", "RefTitle": "EH&S WWI and Unicode", "RefUrl": "/notes/733416 "}, {"RefNumber": "961026", "RefComponent": "EHS-BD-RDF-WWI", "RefTitle": "EH&S WWI: Errors in Unicode mode", "RefUrl": "/notes/961026 "}, {"RefNumber": "1088362", "RefComponent": "BC-CTS-LAN", "RefTitle": "Corrupted table T002T in Unicode systems", "RefUrl": "/notes/1088362 "}, {"RefNumber": "1330256", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "LOAD_PROGRAM_NOT_FOUND Job RDDMASGL Program RDD21DAT", "RefUrl": "/notes/1330256 "}, {"RefNumber": "1459066", "RefComponent": "BC-I18-UNI", "RefTitle": "SPUMG: code pages 1614 or 8341 as Global Fallback Code Page", "RefUrl": "/notes/1459066 "}, {"RefNumber": "901004", "RefComponent": "BC-I18", "RefTitle": "Conversion of ABAP spool requests to Unicode.", "RefUrl": "/notes/901004 "}, {"RefNumber": "1499385", "RefComponent": "BC-ABA-LI", "RefTitle": "Lost layout after text download", "RefUrl": "/notes/1499385 "}, {"RefNumber": "920085", "RefComponent": "BC-I18-UNI", "RefTitle": "Additional languages for countries without SAP translation", "RefUrl": "/notes/920085 "}, {"RefNumber": "1024253", "RefComponent": "BC-I18-UNI", "RefTitle": "Matchcode regeneration in Unicode systems", "RefUrl": "/notes/1024253 "}, {"RefNumber": "1141788", "RefComponent": "BC-CCM-PRN", "RefTitle": "Useful fonts for Unicode PDF conversion (PDFUC)", "RefUrl": "/notes/1141788 "}, {"RefNumber": "745030", "RefComponent": "BC-I18-UNI", "RefTitle": "MDMP - Unicode Interfaces: Solution Overview", "RefUrl": "/notes/745030 "}, {"RefNumber": "985296", "RefComponent": "BC-SRV-ADR", "RefTitle": "Sending multiple address vesions from MDMP to other systems", "RefUrl": "/notes/985296 "}, {"RefNumber": "634839", "RefComponent": "BC-SRV-ADR", "RefTitle": "Text language for address versions", "RefUrl": "/notes/634839 "}, {"RefNumber": "589159", "RefComponent": "BC-SRV-ADR", "RefTitle": "Incorrect entries in matchcode fields in ADRC and ADRP", "RefUrl": "/notes/589159 "}, {"RefNumber": "673941", "RefComponent": "BC-SRV-ADR", "RefTitle": "Unicode conversion for address tables (ADRC, ADRP)", "RefUrl": "/notes/673941 "}, {"RefNumber": "1368419", "RefComponent": "BC-CUS-TOL-ALO", "RefTitle": "MDMP to Unicode conversion,logs not displayed correctly", "RefUrl": "/notes/1368419 "}, {"RefNumber": "1478123", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "FAQ: SAPI Source system", "RefUrl": "/notes/1478123 "}, {"RefNumber": "1448532", "RefComponent": "PS-IS-LOG", "RefTitle": "PS-IS: Variants lead to terminations after Unicode migration", "RefUrl": "/notes/1448532 "}, {"RefNumber": "647495", "RefComponent": "BC-I18-UNI", "RefTitle": "RFC for Unicode ./. non-Unicode Connections", "RefUrl": "/notes/647495 "}, {"RefNumber": "764480", "RefComponent": "BC-I18", "RefTitle": "SAP WinGUI I18N Trouble Shooting", "RefUrl": "/notes/764480 "}, {"RefNumber": "1012025", "RefComponent": "BC-I18-UNI", "RefTitle": "MDMP to Unicode conversion: Restart of R3load export", "RefUrl": "/notes/1012025 "}, {"RefNumber": "1356472", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode conversion: Number of rows of table clusters changed", "RefUrl": "/notes/1356472 "}, {"RefNumber": "1455043", "RefComponent": "BC-I18", "RefTitle": "Invisible differences between characters", "RefUrl": "/notes/1455043 "}, {"RefNumber": "1503601", "RefComponent": "BC-I18-UNI", "RefTitle": "Error trace for reading Unicode files and OS environment", "RefUrl": "/notes/1503601 "}, {"RefNumber": "1040674", "RefComponent": "BC-DB-DB2", "RefTitle": "R3ta in combination with unsorted unload", "RefUrl": "/notes/1040674 "}, {"RefNumber": "513435", "RefComponent": "BC-I18", "RefTitle": "Windows code page setup for non-Unicode text files", "RefUrl": "/notes/513435 "}, {"RefNumber": "846194", "RefComponent": "CA-GTF-TS-WSI", "RefTitle": "OCI/OPI: Code pages and system language", "RefUrl": "/notes/846194 "}, {"RefNumber": "1276242", "RefComponent": "BC-I18-UNI", "RefTitle": "UMGSTAT is missing entries, UMG_ADD_VOCABULARY", "RefUrl": "/notes/1276242 "}, {"RefNumber": "1313947", "RefComponent": "BC-I18-UNI", "RefTitle": "SUMG Manual Repair: repair of INDX Log uses wrong rowid", "RefUrl": "/notes/1313947 "}, {"RefNumber": "1320370", "RefComponent": "BC-I18-UNI", "RefTitle": "SUMG Manual Repair: ALV grid sort, filter, select options", "RefUrl": "/notes/1320370 "}, {"RefNumber": "1339144", "RefComponent": "BC-I18-UNI", "RefTitle": "SUMG Automatic Repair: incomplete upload of split tables", "RefUrl": "/notes/1339144 "}, {"RefNumber": "1345543", "RefComponent": "BC-I18-UNI", "RefTitle": "SUMG Manual Repair: Log list is not scrollable", "RefUrl": "/notes/1345543 "}, {"RefNumber": "1392027", "RefComponent": "BC-I18-UNI", "RefTitle": "SUMG: Display number of log entries", "RefUrl": "/notes/1392027 "}, {"RefNumber": "1443472", "RefComponent": "BC-I18-UNI", "RefTitle": "SUMG: <PERSON><PERSON>", "RefUrl": "/notes/1443472 "}, {"RefNumber": "818374", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Conversion of a Blended Code Page System", "RefUrl": "/notes/818374 "}, {"RefNumber": "1248636", "RefComponent": "BC-ABA-LA", "RefTitle": "Cleanup of inconsistencies in Data Dictionary", "RefUrl": "/notes/1248636 "}, {"RefNumber": "999712", "RefComponent": "BC-CCM-PRN", "RefTitle": "PDF conversion for Unicode", "RefUrl": "/notes/999712 "}, {"RefNumber": "552464", "RefComponent": "BC", "RefTitle": "What is <PERSON> <PERSON><PERSON> / <PERSON> Endian? What Endian do I have?", "RefUrl": "/notes/552464 "}, {"RefNumber": "975768", "RefComponent": "BC-I18", "RefTitle": "Deprecation of Java features with non-Unicode Backend", "RefUrl": "/notes/975768 "}, {"RefNumber": "1071731", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode C-programs and access to OS environment", "RefUrl": "/notes/1071731 "}, {"RefNumber": "1358929", "RefComponent": "BC-I18", "RefTitle": "Deprecation of SOA features with non-Unicode Backend", "RefUrl": "/notes/1358929 "}, {"RefNumber": "1491018", "RefComponent": "BC-MID-ALE", "RefTitle": "IDoc: Report MASS_RSEOUT00 in Unicode systems", "RefUrl": "/notes/1491018 "}, {"RefNumber": "136649", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Selective language import", "RefUrl": "/notes/136649 "}, {"RefNumber": "1497794", "RefComponent": "BC-I18-UNI", "RefTitle": "SPUMG: Adding words from the Reprocess Log to the Vocabulary", "RefUrl": "/notes/1497794 "}, {"RefNumber": "1274259", "RefComponent": "FI-BL-PT-FO", "RefTitle": "Payment medium before upgrade to Unicode-active system", "RefUrl": "/notes/1274259 "}, {"RefNumber": "1291662", "RefComponent": "BC-SEC-USR", "RefTitle": "Cleanup of table INDX(PR)", "RefUrl": "/notes/1291662 "}, {"RefNumber": "1500340", "RefComponent": "BC-I18-UNI", "RefTitle": "CU&UC: Long running jobs in Additional Preparation Steps", "RefUrl": "/notes/1500340 "}, {"RefNumber": "1255556", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode conversion preparation: Container table DBTABLOG", "RefUrl": "/notes/1255556 "}, {"RefNumber": "1400236", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "Prerequisites for Xcelsius usage with NetWeaver BI/BW", "RefUrl": "/notes/1400236 "}, {"RefNumber": "1397782", "RefComponent": "BC-I18-UNI", "RefTitle": "SUMG Manual Repair: Save", "RefUrl": "/notes/1397782 "}, {"RefNumber": "541299", "RefComponent": "BC-I18-UNI", "RefTitle": "Incorrect column layout on lists in Unicode systems", "RefUrl": "/notes/541299 "}, {"RefNumber": "1275074", "RefComponent": "BC-CUS-TOL-ALO", "RefTitle": "SCU3: Change Logs - Short dump or '#' in results", "RefUrl": "/notes/1275074 "}, {"RefNumber": "756535", "RefComponent": "BC-I18-UNI", "RefTitle": "SAP Vocabulary for Vocabulary Maintenance for SPUMG & SPUM4", "RefUrl": "/notes/756535 "}, {"RefNumber": "932779", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode conversion - analysis of nametab problems", "RefUrl": "/notes/932779 "}, {"RefNumber": "959698", "RefComponent": "BC-I18-UNI", "RefTitle": "Twin Upgrade & Unicode Conversion FAQ", "RefUrl": "/notes/959698 "}, {"RefNumber": "940429", "RefComponent": "BC-SRV-SSF", "RefTitle": "Language independent printing in Smart Forms", "RefUrl": "/notes/940429 "}, {"RefNumber": "1069209", "RefComponent": "BC-I18-UNI", "RefTitle": "SPUMG or SPUM4: Reprocess Log becomes too large", "RefUrl": "/notes/1069209 "}, {"RefNumber": "1302042", "RefComponent": "CO-PC-IS", "RefTitle": "Cleanup for table INDX(KU)", "RefUrl": "/notes/1302042 "}, {"RefNumber": "1416449", "RefComponent": "BC-I18-UNI", "RefTitle": "Unknown language error for Unicode language after upgrade", "RefUrl": "/notes/1416449 "}, {"RefNumber": "1129173", "RefComponent": "BC-I18-UNI", "RefTitle": "Special SPUMG vocabulary used in CU&UC procedure", "RefUrl": "/notes/1129173 "}, {"RefNumber": "1417727", "RefComponent": "BC-I18-UNI", "RefTitle": "New version of report UMG_ANALYZE_REPLOG", "RefUrl": "/notes/1417727 "}, {"RefNumber": "1128672", "RefComponent": "BC-I18-UNI", "RefTitle": "Report UMG_CHECK_STXL", "RefUrl": "/notes/1128672 "}, {"RefNumber": "480671", "RefComponent": "BC-I18", "RefTitle": "The Text Language Flag of LANG Fields", "RefUrl": "/notes/480671 "}, {"RefNumber": "882865", "RefComponent": "SCM-APO-INT-CCR", "RefTitle": "CCR: Incorrect results when using parallel processing", "RefUrl": "/notes/882865 "}, {"RefNumber": "510882", "RefComponent": "BC-I18-UNI", "RefTitle": "Alignment correction for structured data in containers", "RefUrl": "/notes/510882 "}, {"RefNumber": "837748", "RefComponent": "FI-GL-GL-F", "RefTitle": "RFASLDPC: Incorrect processing under Unicode", "RefUrl": "/notes/837748 "}, {"RefNumber": "926816", "RefComponent": "BC-FES-WGU", "RefTitle": "ITS Up/Download: ascii files in UTF-8 after up/download", "RefUrl": "/notes/926816 "}, {"RefNumber": "742662", "RefComponent": "BC-SRV-SCR", "RefTitle": "MS Word as Editor in SAPscript and Smart Forms", "RefUrl": "/notes/742662 "}, {"RefNumber": "1134257", "RefComponent": "XX-CSC-RU-FI", "RefTitle": "VAT Return (Russia): Corrections for Unicode Systems", "RefUrl": "/notes/1134257 "}, {"RefNumber": "756534", "RefComponent": "BC-I18-UNI", "RefTitle": "Automatic Assignment of Languages with Character Statistics", "RefUrl": "/notes/756534 "}, {"RefNumber": "987914", "RefComponent": "BC-ABA-TO", "RefTitle": "Adjustment of variants, Unicode problem", "RefUrl": "/notes/987914 "}, {"RefNumber": "1132495", "RefComponent": "BC-I18-UNI", "RefTitle": "Report UMG_DELETE_SSLOAD", "RefUrl": "/notes/1132495 "}, {"RefNumber": "975996", "RefComponent": "EHS-BD", "RefTitle": "Problems with applic. area C## during conversion to Unicode", "RefUrl": "/notes/975996 "}, {"RefNumber": "1419531", "RefComponent": "BC-INS", "RefTitle": "R3load patch information", "RefUrl": "/notes/1419531 "}, {"RefNumber": "1128673", "RefComponent": "BC-I18-UNI", "RefTitle": "Additional post conversion step UMG_ADJUST_I18N_SETTINGS", "RefUrl": "/notes/1128673 "}, {"RefNumber": "1363394", "RefComponent": "IS-AFS", "RefTitle": "Wrong Master Language entries for AFS objects in REPOSRC", "RefUrl": "/notes/1363394 "}, {"RefNumber": "1270827", "RefComponent": "BC-I18", "RefTitle": "Control Codes not filtered during input", "RefUrl": "/notes/1270827 "}, {"RefNumber": "1309728", "RefComponent": "BC-DB-DBI", "RefTitle": "SDBI_CLUSTER_CHECK: Usage instructions", "RefUrl": "/notes/1309728 "}, {"RefNumber": "455195", "RefComponent": "BC-INS-MIG-TLA", "RefTitle": "R3load: Using TSK files", "RefUrl": "/notes/455195 "}, {"RefNumber": "1078295", "RefComponent": "BC-I18", "RefTitle": "Incomplete to upper cases in old code pages", "RefUrl": "/notes/1078295 "}, {"RefNumber": "1388649", "RefComponent": "FI-BL-PT-FO", "RefTitle": "Report RFPAYM_UNICODE_VARIANTS cross-client", "RefUrl": "/notes/1388649 "}, {"RefNumber": "1278126", "RefComponent": "FI-BL-PT-FO", "RefTitle": "Problems with variants of PMW/change to Unicode", "RefUrl": "/notes/1278126 "}, {"RefNumber": "1066952", "RefComponent": "BC-SRV-DX-LSM", "RefTitle": "Working with legacy data files in LSMW (Unicode, BOM, FTP)", "RefUrl": "/notes/1066952 "}, {"RefNumber": "1375961", "RefComponent": "BC-SRV-ARL", "RefTitle": "Use of ARCHUTF8 in Archivelink storage scenarios", "RefUrl": "/notes/1375961 "}, {"RefNumber": "1373611", "RefComponent": "BC-I18-UNI", "RefTitle": "Unknown type in report UMG_SHOW_UCTAB during generation", "RefUrl": "/notes/1373611 "}, {"RefNumber": "873789", "RefComponent": "CRM-MW-COM", "RefTitle": "Non XML data to mobile clients from Unicode CRM servers", "RefUrl": "/notes/873789 "}, {"RefNumber": "1042842", "RefComponent": "BC-CUS-TOL-ALO", "RefTitle": "Activity Logs: Unicode system converted from MDMP system", "RefUrl": "/notes/1042842 "}, {"RefNumber": "1365764", "RefComponent": "FI-BL-PT-FO", "RefTitle": "Codepage of DME file", "RefUrl": "/notes/1365764 "}, {"RefNumber": "1366006", "RefComponent": "BC-CUS-TOL-ALO", "RefTitle": "Change logs after conversion from MDMP to Unicode system", "RefUrl": "/notes/1366006 "}, {"RefNumber": "1318670", "RefComponent": "CO-PA", "RefTitle": "Cleaning table INDX(KE)", "RefUrl": "/notes/1318670 "}, {"RefNumber": "1255030", "RefComponent": "BC-I18-UNI", "RefTitle": "SUMG: Reprocess Log is not displayed", "RefUrl": "/notes/1255030 "}, {"RefNumber": "1338186", "RefComponent": "BC-I18-UNI", "RefTitle": "Language assignments are deleted when synchronizing package", "RefUrl": "/notes/1338186 "}, {"RefNumber": "938374", "RefComponent": "BC-I18-UNI", "RefTitle": "Additional tools for MDMP --> Unicode conversion preparation", "RefUrl": "/notes/938374 "}, {"RefNumber": "1292125", "RefComponent": "KM-KW", "RefTitle": "Cleaning up the table INDX(IW)", "RefUrl": "/notes/1292125 "}, {"RefNumber": "1114085", "RefComponent": "BC-I18-UNI", "RefTitle": "Tutorial: Manual Maintenance of the System Vocabulary", "RefUrl": "/notes/1114085 "}, {"RefNumber": "992956", "RefComponent": "BC-I18-UNI", "RefTitle": "Duplicate keys in table UMGPMDIT after Unicode conversion", "RefUrl": "/notes/992956 "}, {"RefNumber": "1291845", "RefComponent": "BC-I18-BID", "RefTitle": "Display of mixture of Hebrew/Arabic, Latin, and digits", "RefUrl": "/notes/1291845 "}, {"RefNumber": "795871", "RefComponent": "BC-CTS-ORG", "RefTitle": "Conversion of enqueue key of TLOCK* tables", "RefUrl": "/notes/795871 "}, {"RefNumber": "1157153", "RefComponent": "BC-I18", "RefTitle": "Language dependency of line break", "RefUrl": "/notes/1157153 "}, {"RefNumber": "1261426", "RefComponent": "BC-SRV-SCR", "RefTitle": "Texts from non-Unicode are incorrect in Unicode", "RefUrl": "/notes/1261426 "}, {"RefNumber": "1236565", "RefComponent": "BC-MID-RFC-SDK", "RefTitle": "NW RFC SDK: Logging on with non-ISO-Latin-1 user or password", "RefUrl": "/notes/1236565 "}, {"RefNumber": "1315753", "RefComponent": "BC-CUS-TOL-ALO", "RefTitle": "SCU3: Converted into Unicode system from MDMP: Missing logs", "RefUrl": "/notes/1315753 "}, {"RefNumber": "1095975", "RefComponent": "BC-CUS-TOL-ALO", "RefTitle": "SCU3 Restriction: # character in List Display of logs", "RefUrl": "/notes/1095975 "}, {"RefNumber": "785564", "RefComponent": "BC-SRV-COM", "RefTitle": "Device types for SAPconnect", "RefUrl": "/notes/785564 "}, {"RefNumber": "1258722", "RefComponent": "BC-I18-BID", "RefTitle": "LTR Includes in RTL SAPScript/Smartforms documents", "RefUrl": "/notes/1258722 "}, {"RefNumber": "879479", "RefComponent": "BC-MID-ALE", "RefTitle": "IDoc file interface in Unicode system", "RefUrl": "/notes/879479 "}, {"RefNumber": "129581", "RefComponent": "BC-I18", "RefTitle": "Euro sign: printing", "RefUrl": "/notes/129581 "}, {"RefNumber": "1151258", "RefComponent": "BC-SRV-COM", "RefTitle": "Error when sending Excel attachments", "RefUrl": "/notes/1151258 "}, {"RefNumber": "1082681", "RefComponent": "SV-SMG-DIA", "RefTitle": "ST: Support for E2E Diagnostics on non-Unicode ABAP system", "RefUrl": "/notes/1082681 "}, {"RefNumber": "1309804", "RefComponent": "BC-DB-DBI", "RefTitle": "SDBI_CLUSTER_CHECK: Function and error messages", "RefUrl": "/notes/1309804 "}, {"RefNumber": "778662", "RefComponent": "BC-SRV-ASF-CHD", "RefTitle": "Check and correction report for CDCLS table", "RefUrl": "/notes/778662 "}, {"RefNumber": "778209", "RefComponent": "BC-SRV-ASF-CHD", "RefTitle": "Cluster records with missing PAGENO 0 in table CDCLS", "RefUrl": "/notes/778209 "}, {"RefNumber": "1022548", "RefComponent": "BC-FES-GRA", "RefTitle": "Graphics with restricted Unicode support", "RefUrl": "/notes/1022548 "}, {"RefNumber": "1021395", "RefComponent": "BC-I18-UNI", "RefTitle": "Restrictions of language activation on SAP Unicode systems", "RefUrl": "/notes/1021395 "}, {"RefNumber": "79991", "RefComponent": "BC-I18", "RefTitle": "Multi-Language and Unicode support of SAP applications", "RefUrl": "/notes/79991 "}, {"RefNumber": "663089", "RefComponent": "BC-MID-RFC", "RefTitle": "RFC bit options in transaction SM59", "RefUrl": "/notes/663089 "}, {"RefNumber": "1134557", "RefComponent": "PA-ER", "RefTitle": "Language-independent search in Unicode system", "RefUrl": "/notes/1134557 "}, {"RefNumber": "83502", "RefComponent": "BC-CCM-PRN", "RefTitle": "Printing Support for Native Languages", "RefUrl": "/notes/83502 "}, {"RefNumber": "753334", "RefComponent": "BC-I18", "RefTitle": "Unicode Conversion: Problem in Japanese device types", "RefUrl": "/notes/753334 "}, {"RefNumber": "1141769", "RefComponent": "CA-GTF-DRT", "RefTitle": "Reading old DART extract with the corresponding code page", "RefUrl": "/notes/1141769 "}, {"RefNumber": "1294414", "RefComponent": "BC-DWB-SEM", "RefTitle": "Cleaning up table INDX (VM/RT)", "RefUrl": "/notes/1294414 "}, {"RefNumber": "1295322", "RefComponent": "BC-DWB-DIC", "RefTitle": "Nametab inconsistencies in table/view maintenance indicators", "RefUrl": "/notes/1295322 "}, {"RefNumber": "1017064", "RefComponent": "BC-SRV-SCR", "RefTitle": "Incorrect SAPscript transport Unicode->Non-Unicode/PBWW", "RefUrl": "/notes/1017064 "}, {"RefNumber": "367676", "RefComponent": "BC-ABA-LA", "RefTitle": "Incompatible ABAP changes from Release 4.6 to 6.10", "RefUrl": "/notes/367676 "}, {"RefNumber": "881048", "RefComponent": "BC-DWB-DIC-F4", "RefTitle": "F4 Help: Unicode conversion for table DDSHPVAL50", "RefUrl": "/notes/881048 "}, {"RefNumber": "1275149", "RefComponent": "BC-DWB-DIC-F4", "RefTitle": "Input help (F4): Preparing the Unicode conversion", "RefUrl": "/notes/1275149 "}, {"RefNumber": "887654", "RefComponent": "CO-PA", "RefTitle": "Unicode conversion", "RefUrl": "/notes/887654 "}, {"RefNumber": "1269873", "RefComponent": "BC-ABA-LA", "RefTitle": "ABAP programs continue to be generated", "RefUrl": "/notes/1269873 "}, {"RefNumber": "1145959", "RefComponent": "BC-CCM-PRN", "RefTitle": "SWINCF: Alignment of BASIC LATIN Text in CJK ABAP lists", "RefUrl": "/notes/1145959 "}, {"RefNumber": "1146910", "RefComponent": "BC-I18", "RefTitle": "Hong Kong Chinese in Unicode Systems", "RefUrl": "/notes/1146910 "}, {"RefNumber": "1275317", "RefComponent": "PA-PA-RU", "RefTitle": "HR-RU: Incorrect characters in Russian HR tables", "RefUrl": "/notes/1275317 "}, {"RefNumber": "686898", "RefComponent": "CRM-MW-ADP", "RefTitle": "Data exchange with R/3 back end that uses several code pages", "RefUrl": "/notes/686898 "}, {"RefNumber": "545923", "RefComponent": "BW-SYS", "RefTitle": "FAQ: Tech. restrictions/customer namespace/permitted chars", "RefUrl": "/notes/545923 "}, {"RefNumber": "1265171", "RefComponent": "CO-PA-SPP", "RefTitle": "KE1x-Short dump MESSAGE_TYPE_X KG 385 in RKE_BP_VARIANT_READ", "RefUrl": "/notes/1265171 "}, {"RefNumber": "1021459", "RefComponent": "BC-MID-RFC", "RefTitle": "Conversion behavior of the RFC library", "RefUrl": "/notes/1021459 "}, {"RefNumber": "813445", "RefComponent": "BC-I18-UNI", "RefTitle": "Documentation of the report UMG_POOL_TABLE", "RefUrl": "/notes/813445 "}, {"RefNumber": "1151257", "RefComponent": "BC-SRV-COM", "RefTitle": "Converting document content", "RefUrl": "/notes/1151257 "}, {"RefNumber": "1232373", "RefComponent": "IS-DFS-PDR", "RefTitle": "Problem with Unicode conversion for UPS object /ISDFPS/FR", "RefUrl": "/notes/1232373 "}, {"RefNumber": "682783", "RefComponent": "PA", "RefTitle": "Default Unicode conversion code page for HR tables", "RefUrl": "/notes/682783 "}, {"RefNumber": "1118230", "RefComponent": "CRM-MW-COM", "RefTitle": "SAP CRM 4.0 Non-Unicode Mobile clients to Unicode", "RefUrl": "/notes/1118230 "}, {"RefNumber": "1177899", "RefComponent": "BC-I18-UNI", "RefTitle": "Huge amount of R3load Log(XML files) during Unicode export", "RefUrl": "/notes/1177899 "}, {"RefNumber": "1178671", "RefComponent": "BC-I18-UNI", "RefTitle": "Conversion of Views from non-Unicode to Unicode", "RefUrl": "/notes/1178671 "}, {"RefNumber": "1176806", "RefComponent": "BC-MID-RFC", "RefTitle": "Language key for external IDoc processing using startrfc", "RefUrl": "/notes/1176806 "}, {"RefNumber": "1164883", "RefComponent": "FI-AP-AP-D", "RefTitle": "RFDOPR10/RFDSLD00/RFKSLD00: Variants with special characters", "RefUrl": "/notes/1164883 "}, {"RefNumber": "758870", "RefComponent": "FI-BL-PT-BA", "RefTitle": "Account statement: Character set when the file is uploaded", "RefUrl": "/notes/758870 "}, {"RefNumber": "1143116", "RefComponent": "BC-CTS-ORG", "RefTitle": "Special language keys are missing in default language vector", "RefUrl": "/notes/1143116 "}, {"RefNumber": "1019362", "RefComponent": "BC-I18-UNI", "RefTitle": "Very long run times during SPUMG scans", "RefUrl": "/notes/1019362 "}, {"RefNumber": "1128457", "RefComponent": "BC-I18-UNI", "RefTitle": "Additional post conversion program UMG_SAVE_HISTORY", "RefUrl": "/notes/1128457 "}, {"RefNumber": "1142572", "RefComponent": "BC-I18-UNI", "RefTitle": "Classification of notes of the Unicode conversion tools", "RefUrl": "/notes/1142572 "}, {"RefNumber": "1084953", "RefComponent": "BC-MID-RFC", "RefTitle": "Correct code page when receiving MDMP tables using RFC", "RefUrl": "/notes/1084953 "}, {"RefNumber": "1021586", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode conversion of non-Latin-1 Single Codepage System", "RefUrl": "/notes/1021586 "}, {"RefNumber": "834125", "RefComponent": "BC-FES-CTL", "RefTitle": "HTML Viewer: <PERSON>or<PERSON><PERSON> in Performance Assistant", "RefUrl": "/notes/834125 "}, {"RefNumber": "831959", "RefComponent": "BC-FES-ITS", "RefTitle": "Integrated ITS, problems with character conversion", "RefUrl": "/notes/831959 "}, {"RefNumber": "991763", "RefComponent": "BC-MID-ALE", "RefTitle": "IDoc communication between Unicode and MDMP systems", "RefUrl": "/notes/991763 "}, {"RefNumber": "771333", "RefComponent": "BC-FES-CTL", "RefTitle": "Encoding isn't recognized in HTML control", "RefUrl": "/notes/771333 "}, {"RefNumber": "1078263", "RefComponent": "BC-I18-UNI", "RefTitle": "SPUM4 - Reprocess/INDX Log: Word too long", "RefUrl": "/notes/1078263 "}, {"RefNumber": "323736", "RefComponent": "BC-CCM-PRN", "RefTitle": "Restrictions with \"PDF print\" through spooler", "RefUrl": "/notes/323736 "}, {"RefNumber": "427561", "RefComponent": "BC-I18-UNI", "RefTitle": "Transaction SPUMG", "RefUrl": "/notes/427561 "}, {"RefNumber": "1100672", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "RADNTLANG as a tool for checking the text language indicator", "RefUrl": "/notes/1100672 "}, {"RefNumber": "1159564", "RefComponent": "BC-FES-CTL", "RefTitle": "F4: Limitation in number of characters for the search string", "RefUrl": "/notes/1159564 "}, {"RefNumber": "483715", "RefComponent": "CA-GTF-DRT", "RefTitle": "Unicode support in DART", "RefUrl": "/notes/483715 "}, {"RefNumber": "952625", "RefComponent": "BC-I18", "RefTitle": "SORT AS TEXT: What does the collation sequence depend on?", "RefUrl": "/notes/952625 "}, {"RefNumber": "1153741", "RefComponent": "BC-DB-DBI", "RefTitle": "Including SDBI_CLUSTER_CHECK in SPUMG", "RefUrl": "/notes/1153741 "}, {"RefNumber": "1139325", "RefComponent": "BC-I18-UNI", "RefTitle": "Procedures and tools to enable ABAP programs for Unicode", "RefUrl": "/notes/1139325 "}, {"RefNumber": "793546", "RefComponent": "CRM-MSA", "RefTitle": "CRM Server Unicode Migration: Mobile Client Text Tables", "RefUrl": "/notes/793546 "}, {"RefNumber": "1125797", "RefComponent": "FI-SL-IS-A", "RefTitle": "RW: Report output, double byte characters and Unicode", "RefUrl": "/notes/1125797 "}, {"RefNumber": "1130255", "RefComponent": "BC-I18-UNI", "RefTitle": "East Asian Character Width in the Unicode PUA", "RefUrl": "/notes/1130255 "}, {"RefNumber": "1150217", "RefComponent": "BC-FES-GUI", "RefTitle": "Showcase ENCODING: Editor bug - or - Codepage issue?", "RefUrl": "/notes/1150217 "}, {"RefNumber": "788239", "RefComponent": "BC-MID-RFC", "RefTitle": "Which language is used to run an RFC on the target system", "RefUrl": "/notes/788239 "}, {"RefNumber": "549143", "RefComponent": "BC", "RefTitle": "Searching for User-Exits which need Unicode-enabling", "RefUrl": "/notes/549143 "}, {"RefNumber": "845233", "RefComponent": "BC-I18-UNI", "RefTitle": "Use of Input Method Editors (IME) in Unicode System", "RefUrl": "/notes/845233 "}, {"RefNumber": "1059413", "RefComponent": "BC-CCM-ADK", "RefTitle": "Data archived under MDMP is displayed incorrectly in Unicode", "RefUrl": "/notes/1059413 "}, {"RefNumber": "1145203", "RefComponent": "BC-I18", "RefTitle": "RFC connections MDMP R/3 system 4.5B or lower", "RefUrl": "/notes/1145203 "}, {"RefNumber": "991262", "RefComponent": "CRM-MSA", "RefTitle": "FAQ on Unicode enabled Mobile client 4.0", "RefUrl": "/notes/991262 "}, {"RefNumber": "1066323", "RefComponent": "BC-MID-RFC", "RefTitle": "RFC add parameter to refuse mdmp with RFC library", "RefUrl": "/notes/1066323 "}, {"RefNumber": "673533", "RefComponent": "BC-BW", "RefTitle": "Alignmnt problem during extraction into/from Unicode systems", "RefUrl": "/notes/673533 "}, {"RefNumber": "873239", "RefComponent": "BC-CCM-PRN", "RefTitle": "Unicode: Support for cascading fonts when printing", "RefUrl": "/notes/873239 "}, {"RefNumber": "1109643", "RefComponent": "BC-SRV-SCR", "RefTitle": "PBWW, PB60 - Problems w/ long texts from non-Unicode systems", "RefUrl": "/notes/1109643 "}, {"RefNumber": "1075316", "RefComponent": "BC-SRV-SCR", "RefTitle": "Problems with transaction PBWW on Unicode", "RefUrl": "/notes/1075316 "}, {"RefNumber": "1136946", "RefComponent": "BC-SRV-FP", "RefTitle": "Private Use Area (PUA) in Interactive Forms by Adobe", "RefUrl": "/notes/1136946 "}, {"RefNumber": "1114037", "RefComponent": "BC-CCM-PRN", "RefTitle": "SAPGOF in Unicode: Problems with missing font metrics", "RefUrl": "/notes/1114037 "}, {"RefNumber": "923012", "RefComponent": "CRM-IPC-DL", "RefTitle": "IPC standalone and unicode", "RefUrl": "/notes/923012 "}, {"RefNumber": "1066404", "RefComponent": "BC-DB-DBI", "RefTitle": "R3load, ORDER BY, Unicode conversion of cluster tables", "RefUrl": "/notes/1066404 "}, {"RefNumber": "1034188", "RefComponent": "BC-I18-UNI", "RefTitle": "SPUMG: Table fields for language determination", "RefUrl": "/notes/1034188 "}, {"RefNumber": "801416", "RefComponent": "BW-BCT-CGV-MIC", "RefTitle": "Activation of MIC content and extraction -problems and hints", "RefUrl": "/notes/801416 "}, {"RefNumber": "674882", "RefComponent": "CO-PA-MD", "RefTitle": "Upgrade: Syntax error in program RKE5XXXX", "RefUrl": "/notes/674882 "}, {"RefNumber": "1032589", "RefComponent": "CRM-MSA", "RefTitle": "Unicode upgrade Note for SAP CRM Mobile client 4.0 SP12", "RefUrl": "/notes/1032589 "}, {"RefNumber": "1039256", "RefComponent": "CRM-MSA", "RefTitle": "New 4.0 SP12 Unicode Mobile client - post installation steps", "RefUrl": "/notes/1039256 "}, {"RefNumber": "1063794", "RefComponent": "BC-DB-LCA", "RefTitle": "Heterogenous system copy", "RefUrl": "/notes/1063794 "}, {"RefNumber": "785091", "RefComponent": "BC-FES-CON", "RefTitle": "SAPConsole: language support for Unicode systems", "RefUrl": "/notes/785091 "}, {"RefNumber": "563417", "RefComponent": "BC-DWB-TOO-BOB", "RefTitle": "Unicode indicator for BOR object type programs", "RefUrl": "/notes/563417 "}, {"RefNumber": "449918", "RefComponent": "BC-CCM-ADK", "RefTitle": "Reading archived data in Unicode systems", "RefUrl": "/notes/449918 "}, {"RefNumber": "809034", "RefComponent": "BC-CCM-PRN", "RefTitle": "Support for Arabic with SAPWIN", "RefUrl": "/notes/809034 "}, {"RefNumber": "695196", "RefComponent": "BC-DB-DBI", "RefTitle": "Error in the export for Unicode migration", "RefUrl": "/notes/695196 "}, {"RefNumber": "1088209", "RefComponent": "BC-FES-GUI", "RefTitle": "Help for troubleshooting: Code page display problems", "RefUrl": "/notes/1088209 "}, {"RefNumber": "837184", "RefComponent": "FI-SL-IS-A", "RefTitle": "Report Writer and Unicode conversion", "RefUrl": "/notes/837184 "}, {"RefNumber": "722193", "RefComponent": "BC-I18-UNI", "RefTitle": "RFC legacy non-Unicode clients and Unicode servers", "RefUrl": "/notes/722193 "}, {"RefNumber": "211521", "RefComponent": "BC-DB-DBI", "RefTitle": "Understanding the R3check output", "RefUrl": "/notes/211521 "}, {"RefNumber": "1055585", "RefComponent": "BC-I18-UNI", "RefTitle": "Texts on dynpros are displayed in the wrong language.", "RefUrl": "/notes/1055585 "}, {"RefNumber": "173241", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Permitted characters in BW", "RefUrl": "/notes/173241 "}, {"RefNumber": "552612", "RefComponent": "BC-I18", "RefTitle": "Differences between MS and SAP Code Pages", "RefUrl": "/notes/552612 "}, {"RefNumber": "794411", "RefComponent": "BC-MID-CON-JCO", "RefTitle": "Supported codepages of SAP Java Connector 2.1 and 6.x", "RefUrl": "/notes/794411 "}, {"RefNumber": "959874", "RefComponent": "PA-ER", "RefTitle": "Sending HTML e-mails in E-Recruiting", "RefUrl": "/notes/959874 "}, {"RefNumber": "1056170", "RefComponent": "CA-DMS-EUI", "RefTitle": "Easy DMS 6.0 SP07 Patch1 Release Note", "RefUrl": "/notes/1056170 "}, {"RefNumber": "881781", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode/non-Unicode RFC language and code page assignment", "RefUrl": "/notes/881781 "}, {"RefNumber": "1038413", "RefComponent": "BC-CCM-PRN-DVM", "RefTitle": "Unicode SAPWIN device type for CJK font", "RefUrl": "/notes/1038413 "}, {"RefNumber": "543715", "RefComponent": "BW-SYS-DB-MGR", "RefTitle": "BW Migrations and System Copies for BW 2.0B / 2.1 Content", "RefUrl": "/notes/543715 "}, {"RefNumber": "1027088", "RefComponent": "BC-CCM-PRN", "RefTitle": "Incorrect SAPscript output after Unicode conversion", "RefUrl": "/notes/1027088 "}, {"RefNumber": "991481", "RefComponent": "BC-I18-UNI", "RefTitle": "Database is using UTF-8 and not UTF-16 ?", "RefUrl": "/notes/991481 "}, {"RefNumber": "858869", "RefComponent": "BC-DB-ORA", "RefTitle": "Desupport of multibyte character sets as of Oracle 10g", "RefUrl": "/notes/858869 "}, {"RefNumber": "449891", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Temporary database objects in BW 3.x", "RefUrl": "/notes/449891 "}, {"RefNumber": "1040014", "RefComponent": "MFG-MII-CON", "RefTitle": "Importing Unicode CSV from XMII into Microsoft Excel 2003", "RefUrl": "/notes/1040014 "}, {"RefNumber": "1036341", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode executables and corrupt UTF-8 in input files", "RefUrl": "/notes/1036341 "}, {"RefNumber": "24860", "RefComponent": "BC-DB-DBI", "RefTitle": "Replacement of a matchcode ID by a search help function", "RefUrl": "/notes/24860 "}, {"RefNumber": "766703", "RefComponent": "SD-BIL-IV-PC", "RefTitle": "FAQ: Credit card encryption in R/3 systems", "RefUrl": "/notes/766703 "}, {"RefNumber": "991572", "RefComponent": "BC-MID-RFC", "RefTitle": "Code page settings for external RFC programs", "RefUrl": "/notes/991572 "}, {"RefNumber": "1017138", "RefComponent": "BC-CTS-LAN", "RefTitle": "Correcting Customizing texts after Unicode conversion", "RefUrl": "/notes/1017138 "}, {"RefNumber": "902083", "RefComponent": "CRM", "RefTitle": "Unicode Collection Note for CRM 4.0", "RefUrl": "/notes/902083 "}, {"RefNumber": "1017101", "RefComponent": "BC-ABA-XML", "RefTitle": "Encoding Problems with ABAP XML Processing", "RefUrl": "/notes/1017101 "}, {"RefNumber": "922783", "RefComponent": "BC-CTS-LAN", "RefTitle": "Rescue language files for DE / EN", "RefUrl": "/notes/922783 "}, {"RefNumber": "918614", "RefComponent": "BC-I18-UNI", "RefTitle": "ICU functions", "RefUrl": "/notes/918614 "}, {"RefNumber": "970892", "RefComponent": "FI-BL-PT-FO", "RefTitle": "Unicode: Specifying the code page in RFFO* programs", "RefUrl": "/notes/970892 "}, {"RefNumber": "928909", "RefComponent": "BC-I18-UNI", "RefTitle": "Repair table data in SUMG in Unicode systems", "RefUrl": "/notes/928909 "}, {"RefNumber": "871945", "RefComponent": "BC-I18-UNI", "RefTitle": "Fallback Code Page for Zn Languages in Unicode/nonU RFC", "RefUrl": "/notes/871945 "}, {"RefNumber": "956921", "RefComponent": "XX-IDES", "RefTitle": "IDES ERP 2005 ECC 6.0", "RefUrl": "/notes/956921 "}, {"RefNumber": "1002845", "RefComponent": "BC-DWB-TOO-SCR", "RefTitle": "Graphic Layout Editor in Unicode systems (II)", "RefUrl": "/notes/1002845 "}, {"RefNumber": "994909", "RefComponent": "BC-I18-UNI", "RefTitle": "UMG_SUMG_VERIFY_R3LOAD_XML : What to do?", "RefUrl": "/notes/994909 "}, {"RefNumber": "978244", "RefComponent": "BC-I18-UNI", "RefTitle": "CU&UC and NameTab errors for pools or clusters", "RefUrl": "/notes/978244 "}, {"RefNumber": "885441", "RefComponent": "BW-SYS-DB-MGR", "RefTitle": "Common Migration Errors", "RefUrl": "/notes/885441 "}, {"RefNumber": "977372", "RefComponent": "LO-VC-DEP", "RefTitle": "Dependencies corrupt after unicode conversion", "RefUrl": "/notes/977372 "}, {"RefNumber": "718324", "RefComponent": "CRM", "RefTitle": "Support restrictions for MDMP implementations of mySAP CRM", "RefUrl": "/notes/718324 "}, {"RefNumber": "708442", "RefComponent": "FI-SL-VSR", "RefTitle": "Incorrect generated programs in Unicode systems", "RefUrl": "/notes/708442 "}, {"RefNumber": "697625", "RefComponent": "BC-MID-ALE", "RefTitle": "ALE: Transport problems for Customizing data", "RefUrl": "/notes/697625 "}, {"RefNumber": "910300", "RefComponent": "BC-FES-CTL", "RefTitle": "GridView:Truncation of text in Unicode systems.", "RefUrl": "/notes/910300 "}, {"RefNumber": "588480", "RefComponent": "BW-SYS", "RefTitle": "Release restrictions for Unicode in SAP BW 3.x", "RefUrl": "/notes/588480 "}, {"RefNumber": "621740", "RefComponent": "BC-I18-UNI", "RefTitle": "Updating the TCPUC and TCPUCATTR tables", "RefUrl": "/notes/621740 "}, {"RefNumber": "811431", "RefComponent": "BC-DB-INF", "RefTitle": "Informix: dbcodepage and Unicode conversion", "RefUrl": "/notes/811431 "}, {"RefNumber": "809279", "RefComponent": "BC-I18-UNI", "RefTitle": "RFC non-Unicode to Unicode with unknown text language", "RefUrl": "/notes/809279 "}, {"RefNumber": "920831", "RefComponent": "BC-I18-UNI", "RefTitle": "RFC with inactive text language", "RefUrl": "/notes/920831 "}, {"RefNumber": "558746", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Better Oracle Data Dictionary BW Performance", "RefUrl": "/notes/558746 "}, {"RefNumber": "355771", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle: Explanation of the new tablespace layout", "RefUrl": "/notes/355771 "}, {"RefNumber": "945255", "RefComponent": "BC-I18-UNI", "RefTitle": "SUMG reports on DOKTL during Unicode conversion", "RefUrl": "/notes/945255 "}, {"RefNumber": "691585", "RefComponent": "CRM-MW-ADP", "RefTitle": "CRM Unicode / R3 MDMP Connection: User Exits", "RefUrl": "/notes/691585 "}, {"RefNumber": "938738", "RefComponent": "BC-I18", "RefTitle": "Syslog CP6: still using ambiguous blended code page", "RefUrl": "/notes/938738 "}, {"RefNumber": "948563", "RefComponent": "PPM-PRO-RES", "RefTitle": "E-mail for notification in Unicode systems", "RefUrl": "/notes/948563 "}, {"RefNumber": "710994", "RefComponent": "BC-I18-UNI", "RefTitle": "Layout of lists printed with LEXUT8 Unicode printer", "RefUrl": "/notes/710994 "}, {"RefNumber": "679821", "RefComponent": "BC-I18", "RefTitle": "Check of kernel compilation mode (Unicode/Non-Unicode)", "RefUrl": "/notes/679821 "}, {"RefNumber": "926450", "RefComponent": "BC-DB-SDB", "RefTitle": "Short dump with error -4025 during connect to ASCII database", "RefUrl": "/notes/926450 "}, {"RefNumber": "729348", "RefComponent": "BC-SRV-ASF-UOM", "RefTitle": "Unicode change for units of measure", "RefUrl": "/notes/729348 "}, {"RefNumber": "379940", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode based mySAP availability", "RefUrl": "/notes/379940 "}, {"RefNumber": "931856", "RefComponent": "BC-ABA-LI", "RefTitle": "Spreadsheet download in a Unicode system", "RefUrl": "/notes/931856 "}, {"RefNumber": "855841", "RefComponent": "FI-BL-PT-FO", "RefTitle": "Payment media in Unicode systems", "RefUrl": "/notes/855841 "}, {"RefNumber": "886571", "RefComponent": "BC-I18-UNI", "RefTitle": "RFC non-Unicode ./. Unicode with illegal text language", "RefUrl": "/notes/886571 "}, {"RefNumber": "925170", "RefComponent": "BW-BEX-ET-WEB-AD", "RefTitle": "Web Application Designer NW04s: Saving: Non-Unicode ABAP", "RefUrl": "/notes/925170 "}, {"RefNumber": "423003", "RefComponent": "BC-CCM-PRN-DVM", "RefTitle": "Printers and Asian/Eastern European fonts/languages", "RefUrl": "/notes/423003 "}, {"RefNumber": "638844", "RefComponent": "BC-SRV-QUE", "RefTitle": "SAP Query: EIS on Unicode systems", "RefUrl": "/notes/638844 "}, {"RefNumber": "688089", "RefComponent": "BC-CCM-MON-SLG", "RefTitle": "SYSLOG: unreadable characters after change to UNICODE", "RefUrl": "/notes/688089 "}, {"RefNumber": "914966", "RefComponent": "BC-DB-SDB", "RefTitle": "Database enlargement due to Unicode migration", "RefUrl": "/notes/914966 "}, {"RefNumber": "801949", "RefComponent": "CRM-MW-COM", "RefTitle": "Mobile Clients Sync Performance - Unicode servers", "RefUrl": "/notes/801949 "}, {"RefNumber": "594356", "RefComponent": "BC-FES-GRA", "RefTitle": "Function modules ws_upload and ws_download are obsolete", "RefUrl": "/notes/594356 "}, {"RefNumber": "548769", "RefComponent": "BC-I18-UNI", "RefTitle": "BW Unicode Scenario: Dump CONVERSION_CODEPAGE_UNKNOWN", "RefUrl": "/notes/548769 "}, {"RefNumber": "875712", "RefComponent": "SCM-APO-PPS", "RefTitle": "Unicode conversion and planning logs", "RefUrl": "/notes/875712 "}, {"RefNumber": "890302", "RefComponent": "PA-ER", "RefTitle": "SAP E-Recruiting for single code page systems only", "RefUrl": "/notes/890302 "}, {"RefNumber": "765763", "RefComponent": "BC-FES-GUI", "RefTitle": "Setting the Upload/Download codepage for a SAPGUI connection", "RefUrl": "/notes/765763 "}, {"RefNumber": "828554", "RefComponent": "BW-SYS", "RefTitle": "Incorrect BW MDMP check when upgrading to Basis 6.40", "RefUrl": "/notes/828554 "}, {"RefNumber": "881879", "RefComponent": "BC-DWB-TOO-WAB", "RefTitle": "Unicode conversion for table O2HTMLATTR", "RefUrl": "/notes/881879 "}, {"RefNumber": "158774", "RefComponent": "BC-I18", "RefTitle": "Incorrect case mappings in some locales", "RefUrl": "/notes/158774 "}, {"RefNumber": "827999", "RefComponent": "BC-MID-RFC", "RefTitle": "Error during transfer to MDMP tables with tRFC", "RefUrl": "/notes/827999 "}, {"RefNumber": "747036", "RefComponent": "BC-I18", "RefTitle": "mySAP ERP 2004 Upgrade for R/3 MDMP Customers", "RefUrl": "/notes/747036 "}, {"RefNumber": "717776", "RefComponent": "BC-BW", "RefTitle": "Text transformation between Unicode and non-Unicode systems", "RefUrl": "/notes/717776 "}, {"RefNumber": "853569", "RefComponent": "BW-WHM", "RefTitle": "Problems with allowed characters in 0UNIT (for example µ)", "RefUrl": "/notes/853569 "}, {"RefNumber": "776507", "RefComponent": "BC-CCM-PRN", "RefTitle": "SAPscript/SmartForms: Which fonts for which languages?", "RefUrl": "/notes/776507 "}, {"RefNumber": "765543", "RefComponent": "BW-WHM-DST-BAPI", "RefTitle": "SAP BW Unicode and Staging BAPI - release information", "RefUrl": "/notes/765543 "}, {"RefNumber": "627764", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Unicode migration: table pools inconsistent after conversion", "RefUrl": "/notes/627764 "}, {"RefNumber": "808019", "RefComponent": "BW-BCT", "RefTitle": "Ldg master data from non-Unicode OLTP to Unicode BW", "RefUrl": "/notes/808019 "}, {"RefNumber": "553110", "RefComponent": "BC-I18", "RefTitle": "User Exits:  Behavior within non-Unicode R/3 Enterprise", "RefUrl": "/notes/553110 "}, {"RefNumber": "819426", "RefComponent": "SRM-EBP", "RefTitle": "MDMP implementation not supported for mySAP SRM", "RefUrl": "/notes/819426 "}, {"RefNumber": "684332", "RefComponent": "BC-DWB-TOO-ABA", "RefTitle": "Unicode system: Populating the TRDIR-RLOAD language key", "RefUrl": "/notes/684332 "}, {"RefNumber": "712958", "RefComponent": "BC-DWB-DIC-F4", "RefTitle": "Dumps when processing personal value list", "RefUrl": "/notes/712958 "}, {"RefNumber": "827289", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Generation of Unicode runtime objects", "RefUrl": "/notes/827289 "}, {"RefNumber": "790485", "RefComponent": "BC-MID-RFC", "RefTitle": "RFC Problem Single Code Page, non-Unicode to Unicode System", "RefUrl": "/notes/790485 "}, {"RefNumber": "823110", "RefComponent": "SLL-LEG", "RefTitle": "GTS: UNICODE and Multi Display Multi Processing (MDMP)", "RefUrl": "/notes/823110 "}, {"RefNumber": "617472", "RefComponent": "BC-I18", "RefTitle": "Unicode system + EH&S + permitted languages", "RefUrl": "/notes/617472 "}, {"RefNumber": "788449", "RefComponent": "BC-ABA-LA", "RefTitle": "Byte-order Marks in UTF-8 Files", "RefUrl": "/notes/788449 "}, {"RefNumber": "821971", "RefComponent": "BC-I18-UNI", "RefTitle": "Co-operation between RADCUCNT and R3load", "RefUrl": "/notes/821971 "}, {"RefNumber": "651229", "RefComponent": "BC-I18", "RefTitle": "MDMP Restrictions", "RefUrl": "/notes/651229 "}, {"RefNumber": "791199", "RefComponent": "BC-I18-UNI", "RefTitle": "Default editor changed to MSWord in SAPscript and SmartForms", "RefUrl": "/notes/791199 "}, {"RefNumber": "589701", "RefComponent": "BW-SYS", "RefTitle": "Known errors in BW 3.1 Content Unicode", "RefUrl": "/notes/589701 "}, {"RefNumber": "643813", "RefComponent": "BW-SYS", "RefTitle": "Composite SAP note - BW Unicode", "RefUrl": "/notes/643813 "}, {"RefNumber": "706528", "RefComponent": "BC-MID-RFC", "RefTitle": "Changing Unicode setting for connection type \"L\"", "RefUrl": "/notes/706528 "}, {"RefNumber": "705447", "RefComponent": "BC-CCM-ADK", "RefTitle": "Size of archive files", "RefUrl": "/notes/705447 "}, {"RefNumber": "716200", "RefComponent": "BC-I18", "RefTitle": "Can characters disappear during code page conversion", "RefUrl": "/notes/716200 "}, {"RefNumber": "783299", "RefComponent": "BC-CTS-LAN", "RefTitle": "Problems with new language keys in the TR SMLT", "RefUrl": "/notes/783299 "}, {"RefNumber": "89384", "RefComponent": "BC-DB-DBI", "RefTitle": "Checking cluster tables with R3check", "RefUrl": "/notes/89384 "}, {"RefNumber": "173255", "RefComponent": "BW-WHM", "RefTitle": "Language problems in the BW System", "RefUrl": "/notes/173255 "}, {"RefNumber": "775114", "RefComponent": "BC-I18", "RefTitle": "Problems during transport into a Unicode system", "RefUrl": "/notes/775114 "}, {"RefNumber": "775189", "RefComponent": "BC-CCM-API-CSI-XBP", "RefTitle": "XBP interface and Unicode", "RefUrl": "/notes/775189 "}, {"RefNumber": "615746", "RefComponent": "BC-FES-GUI", "RefTitle": "Incorrect HTML data at front end against Unicode R/3", "RefUrl": "/notes/615746 "}, {"RefNumber": "314676", "RefComponent": "BC-MID-BUS", "RefTitle": "Tips for processing data (UTF-8, Japanese)", "RefUrl": "/notes/314676 "}, {"RefNumber": "633265", "RefComponent": "BC-SRV-COM", "RefTitle": "SMTP PlugIn: Multi-codepage ability", "RefUrl": "/notes/633265 "}, {"RefNumber": "754311", "RefComponent": "BC-FES-OFFI", "RefTitle": "National special characters are not displayed properly.", "RefUrl": "/notes/754311 "}, {"RefNumber": "689951", "RefComponent": "BC-ABA-LA", "RefTitle": "Incompatible ABAP changes from Release 6.20 to 6.40", "RefUrl": "/notes/689951 "}, {"RefNumber": "740863", "RefComponent": "FIN-FSCM-BC-SB", "RefTitle": "RADCUCNT: Objects that cannot be generated.", "RefUrl": "/notes/740863 "}, {"RefNumber": "624498", "RefComponent": "BC-ABA-LI", "RefTitle": "Downloading of lists in Unicode system", "RefUrl": "/notes/624498 "}, {"RefNumber": "567048", "RefComponent": "BC-INS-AS4", "RefTitle": "INST: Unicode SAP R/3 Enterprise on IBM eServer iSeries", "RefUrl": "/notes/567048 "}, {"RefNumber": "503242", "RefComponent": "BC-ABA-NL", "RefTitle": "Supported languages", "RefUrl": "/notes/503242 "}, {"RefNumber": "709019", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Inconsistency with table types after Unicode migration", "RefUrl": "/notes/709019 "}, {"RefNumber": "564142", "RefComponent": "BC-SRV-SSF", "RefTitle": "Composite Unicode SAP note (Smart Forms and SAPscript)", "RefUrl": "/notes/564142 "}, {"RefNumber": "691407", "RefComponent": "BC-SRV-COM", "RefTitle": "No enhancements in SAPoffice", "RefUrl": "/notes/691407 "}, {"RefNumber": "674371", "RefComponent": "BW-WHM-DST", "RefTitle": "Replicating application components in Unicode BW", "RefUrl": "/notes/674371 "}, {"RefNumber": "600560", "RefComponent": "BC-CST-GW", "RefTitle": "CPICSDK restrictions with Unicode", "RefUrl": "/notes/600560 "}, {"RefNumber": "658095", "RefComponent": "BC-INS-MIG", "RefTitle": "R3load: Unicode conversion of cluster tables", "RefUrl": "/notes/658095 "}, {"RefNumber": "615864", "RefComponent": "EHS-BD-SIS", "RefTitle": "Excel output: Umlauts are not displayed correctly", "RefUrl": "/notes/615864 "}, {"RefNumber": "544942", "RefComponent": "BC-FES-AIT-CTR", "RefTitle": "OCX Controls: Connection to Unicode backend", "RefUrl": "/notes/544942 "}, {"RefNumber": "609054", "RefComponent": "BC-BW", "RefTitle": "Extractors: Deleting generated programs before the upgrade", "RefUrl": "/notes/609054 "}, {"RefNumber": "563975", "RefComponent": "BW-SYS", "RefTitle": "BW and MDMP/BW and blended code pages not supported", "RefUrl": "/notes/563975 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}