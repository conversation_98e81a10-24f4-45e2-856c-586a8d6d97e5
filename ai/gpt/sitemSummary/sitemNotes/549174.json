{"Request": {"Number": "549174", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 315, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015276942017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000549174?language=E&token=6DF0E2DC361A99B0BB803F770CC104BC"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000549174", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000549174/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "549174"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "30.10.2002"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-DB2"}, "SAPComponentKeyText": {"_label": "Component", "value": "DB2 for z/OS"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "DB2 for z/OS", "value": "BC-DB-DB2", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-DB2*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "549174 - Transaction DB03 no longer supported for DB2/390"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>After you import the latest transports that were made available for the relevant system, a dump occurs repeatedly during the day with the keywords \"CONVT_NO_NUMBER\", \"SAPLSTU3\", \"LSTU3F01\" and \"GET_INSTALLATION_PARAMETER\".<br />Transaction DB03 is no longer supported.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>DB2 MVS OS390 z/OS transport dump CONVT_NO_NUMBER SAPLSTU3 LSTU3F01 GET_INSTALLATION_PARAMETER DB03</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The support for transaction DB03 was discontinued because the same data is displayed in transactions DB2 and ST04 in a more modern format.<br />Therefore, the latest changes made there were not implemented, which causes the described dump.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>As a short-term solution, use transaction SE16 to change the entry for RSORAPAR, RSHOSTPH and RSSTATPH in the TCOLL table. You must delete every X you find in column TIMOD for the RSOARAPAR, RSHOSTPH and RSSTATPH rows. For more information, see note 12103.<br /><br />For a definitive solution, you should import the relevant Support Packages for your release as soon as they are available. You should then undo the changes to the RSHOSTPH and RSSTATPH lines in the TCOLL table to re-activate the corresponding data collectors.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5027389)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D000269)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000549174/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000549174/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000549174/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000549174/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000549174/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000549174/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000549174/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000549174/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000549174/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "863189", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/zOS: Transaction TU02 does not work", "RefUrl": "/notes/863189"}, {"RefNumber": "779989", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/zOS: Shortdump SYSTEM_LOAD_OF_PROGRAM_FAILED", "RefUrl": "/notes/779989"}, {"RefNumber": "598469", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/598469"}, {"RefNumber": "427748", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: CCMS corrections (6.10, 6.20, 6.40, 7.x)", "RefUrl": "/notes/427748"}, {"RefNumber": "417920", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/417920"}, {"RefNumber": "324739", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/324739"}, {"RefNumber": "217093", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/217093"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "12103", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Contents of table TCOLL", "RefUrl": "/notes/12103"}, {"RefNumber": "101217", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2 z/OS: Overview of transports and corrections", "RefUrl": "/notes/101217"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "427748", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: CCMS corrections (6.10, 6.20, 6.40, 7.x)", "RefUrl": "/notes/427748 "}, {"RefNumber": "12103", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Contents of table TCOLL", "RefUrl": "/notes/12103 "}, {"RefNumber": "101217", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2 z/OS: Overview of transports and corrections", "RefUrl": "/notes/101217 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "863189", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/zOS: Transaction TU02 does not work", "RefUrl": "/notes/863189 "}, {"RefNumber": "779989", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/zOS: Shortdump SYSTEM_LOAD_OF_PROGRAM_FAILED", "RefUrl": "/notes/779989 "}, {"RefNumber": "598469", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Transporte und Support Packages für 7.10", "RefUrl": "/notes/598469 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "46C", "To": "46D", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "640", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C37", "URL": "/supportpackage/SAPKB46C37"}, {"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C39", "URL": "/supportpackage/SAPKB46C39"}, {"SoftwareComponentVersion": "SAP_BASIS 46D", "SupportPackage": "SAPKB46D26", "URL": "/supportpackage/SAPKB46D26"}, {"SoftwareComponentVersion": "SAP_BASIS 46D", "SupportPackage": "SAPKB46D27", "URL": "/supportpackage/SAPKB46D27"}, {"SoftwareComponentVersion": "SAP_BASIS 610", "SupportPackage": "SAPKB61025", "URL": "/supportpackage/SAPKB61025"}, {"SoftwareComponentVersion": "SAP_BASIS 610", "SupportPackage": "SAPKB61027", "URL": "/supportpackage/SAPKB61027"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62010", "URL": "/supportpackage/SAPKB62010"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62013", "URL": "/supportpackage/SAPKB62013"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}