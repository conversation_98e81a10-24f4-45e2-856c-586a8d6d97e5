{"Request": {"Number": "917854", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 523, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016046312017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000917854?language=E&token=1A8BEE4DEC6D8FF91CEBC2D73D30828B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000917854", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000917854/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "917854"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Correction of legal function"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.04.2007"}, "SAPComponentKey": {"_label": "Component", "value": "PY-BR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Brazil"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Brazil", "value": "PY-BR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-BR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "917854 - New DDIC objects: GPS and SEFIP Compensation"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>New data dictionary objects for supporting the GPS and SEFIP Compensation functionality.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>GPS SEFIP Compensation</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The new tables, domains and data elements are need for the Compensation processing on GPS and SEFIP.<br /><br /><STRONG>IMPORTANT</STRONG><br />This note must be installed before note 920095 \"HBRGPS00 Compensation\".</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Apply the support package where this note is included in.<br /><br />An Advanced Delivery including the <B>new Data Dictionary objects</B> is available in the attached files according to the following list(\"xxxxxx\" means numbers):<br /><br />L6DKxxxxxx_500_SYST.CAR - Release ERP 2004 &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Page 3<br /><br />L6BKxxxxxx_470_SYST.CAR - Release 4.70(Enterprise)<br />L9CKxxxxxx_46C_SYST.CAR - Release 4.6C<br /><br />An Advanced Delivery including the <B>minimum Customizing entries</B> needed is available in the attached files according to the following list(\"xxxxxx\" means numbers):<br /><br />L6DKxxxxxx_500_CUST.CAR - Release ERP 2004<br />L6BKxxxxxx_470_CUST.CAR - Release 4.70(Enterprise)<br />L9CKxxxxxx_46C_CUST.CAR - Release 4.6C<br /><br />If your release is not yet included in this list please contact us.<br /><br />For more details about Advance Delivery installation procedure please<br />read the notes listed in \"Related Notes\" item.<br /><br />IMPORTANT:<br />Be aware of an Advance Delivery delivers the last version of the object, it means that if you do not have the last HR Support Package installed in you system you could get errors, either Syntax Errors or process errors. In this case the only option is to undo the changes from Advance Delivery and do the code changes manually according to the Correction Instructions available in this note.<br /><br />The new Data Dictionary objects included in this note are:<br /></p> <UL><LI><B>Tables</B></LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>T7BRCO1</TD></TR> <TR><TD>T7BRCO2</TD></TR> <TR><TD>T7BRCO3</TD></TR> <TR><TD>T7BRCO4</TD></TR> <TR><TD>T7BRCO5</TD></TR> <TR><TD>T7BRCO6</TD></TR> <TR><TD>T7BRPM0</TD></TR> </TABLE></UL> <p></p> <UL><LI><B>Table Views</B></LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>V_T7BRCO1</TD></TR> <TR><TD>V_T7BRCO2</TD></TR> <TR><TD>V_T7BRCO3</TD></TR> <TR><TD>V_T7BRCO4</TD></TR> <TR><TD>V_T7BRCO5</TD></TR> <TR><TD>V_T7BRCO6</TD></TR> <TR><TD>V_T7BRPM0</TD></TR> </TABLE></UL> <p></p> <UL><LI><B>Domains</B></LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>PBR_GENTP</TD></TR> <TR><TD>PBR_GSGRP</TD></TR> <TR><TD>PBR_GSWCD</TD></TR> <TR><TD>PBR_GSWMN</TD></TR> <TR><TD>PBR_GSWP</TD></TR> <TR><TD>PBR_FILIA2</TD></TR> <TR><TD>PBR_GSWFL</TD></TR> <TR><TD>PBR_GSWTP</TD></TR> </TABLE></UL> <p></p> <UL><LI><B>Data elements</B></LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>PBR_BALAN</TD></TR> <TR><TD>PBR_BALMN</TD></TR> <TR><TD>PBR_BALYR</TD></TR> <TR><TD>PBR_CFLAG</TD></TR> <TR><TD>PBR_COMPO</TD></TR> <TR><TD>PBR_COMPP</TD></TR> <TR><TD>PBR_GENTP</TD></TR> <TR><TD>PBR_GRPD</TD></TR> <TR><TD>PBR_GSDES</TD></TR> <TR><TD>PBR_GSGRP</TD></TR> <TR><TD>PBR_GSWCD</TD></TR> <TR><TD>PBR_MNVAL</TD></TR> <TR><TD>PBR_ORIMN</TD></TR> <TR><TD>PBR_ORIYR</TD></TR> <TR><TD>PBR_PFLAG</TD></TR></TABLE></UL> <p></p> <UL><LI><B>Search help</B></LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>PBR_GSGRP</TD></TR> <TR><TD>PBR_GSWCD</TD></TR> <TR><TD></TD></TR> </TABLE></UL> <p><B>IMPORTANT</B>: The data element PBR_CATY is created by the note 831275, but for the release 500, this object is released in this note. It will be necessary that the PBR_CATY exist to install the note 831275.<br /><br />In case of problems, create the object with the following information:<br />1 - create a domain<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;name: PBR_CATY<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;description: SEFIP: Classe de Empresas<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;data type: char<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;no. characters: 1<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Value range: 1 - &#x00DA;nica<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 2 - Matriz/Filiais<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 3 - M&#x00FA;ltiplas<br /><br />2 - create the data element<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;name: PBR_CATY<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;short text: SEFIP: Classe de Empresas<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;domain: PBR_CATY<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;field label:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Length&#x00A0;&#x00A0;Field Label<br /> Short&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 10&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ClasseEmp<br /> Medium&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;18&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Classe de Empresas<br /> Long&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;20&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Classe de Empresas<br /> Heading&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 18&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Classe de Empresas<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON> (D036108)"}, {"Key": "Processor                                                                                           ", "Value": "I812649"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000917854/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000917854/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000917854/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000917854/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000917854/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000917854/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000917854/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000917854/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000917854/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "L6BK127743_470_CUST.CAR", "FileSize": "8", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000073142006&iv_version=0009&iv_guid=0136075B0E2EA54CB44E1202CC031922"}, {"FileName": "L6DK058524_500_CUST.CAR", "FileSize": "8", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000073142006&iv_version=0009&iv_guid=E9394C1FF685FA44A6A014103D040B67"}, {"FileName": "L9CK207325_SYST_46C.CAR", "FileSize": "116", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000073142006&iv_version=0009&iv_guid=8382F9A4979F7647882A9F74757DF099"}, {"FileName": "L6BK114266_SYST_470.CAR", "FileSize": "120", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000073142006&iv_version=0009&iv_guid=B02EEB8EF08D5244863799EC3E1FBE49"}, {"FileName": "L6DK045376_SYST_500.CAR", "FileSize": "119", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000073142006&iv_version=0009&iv_guid=997C81E47DBA0045AE86CCDDDE26333F"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "920095", "RefComponent": "PY-BR", "RefTitle": "HBRGPS00 & HBRSEF00: Compensation - New functionality", "RefUrl": "/notes/920095"}, {"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1003141", "RefComponent": "PY-BR", "RefTitle": "V_T7BRCO5 and V_T7BRCO6 don't work properly", "RefUrl": "/notes/1003141"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "920095", "RefComponent": "PY-BR", "RefTitle": "HBRGPS00 & HBRSEF00: Compensation - New functionality", "RefUrl": "/notes/920095 "}, {"RefNumber": "1003141", "RefComponent": "PY-BR", "RefTitle": "V_T7BRCO5 and V_T7BRCO6 don't work properly", "RefUrl": "/notes/1003141 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46CC0", "URL": "/supportpackage/SAPKE46CC0"}, {"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46CB2", "URL": "/supportpackage/SAPKE46CB2"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47064", "URL": "/supportpackage/SAPKE47064"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47056", "URL": "/supportpackage/SAPKE47056"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50024", "URL": "/supportpackage/SAPKE50024"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50030", "URL": "/supportpackage/SAPKE50030"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50023", "URL": "/supportpackage/SAPKE50023"}, {"SoftwareComponentVersion": "SAP_HR 600", "SupportPackage": "SAPKE60006", "URL": "/supportpackage/SAPKE60006"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}