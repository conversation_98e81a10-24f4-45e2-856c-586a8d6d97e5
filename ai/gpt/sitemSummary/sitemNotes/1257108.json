{"Request": {"Number": "1257108", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 251, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016912102017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001257108?language=E&token=6E974A07222DF593FBD253EE4ECF30C7"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001257108", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001257108/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1257108"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "21.03.2013"}, "SAPComponentKey": {"_label": "Component", "value": "BC-SEC-LGN"}, "SAPComponentKeyText": {"_label": "Component", "value": "Authentication"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Security - Read KBA 2985997 for subcomponents", "value": "BC-SEC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Authentication", "value": "BC-SEC-LGN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC-LGN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1257108 - Collective Note: Analyzing issues with Single Sign On (SSO)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p><br />You are having problems with your implementation of Single Sign-On (SSO).<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p><br />logon, login, Single Sign-On, SSO, authentication, SNC, Kerberos, Logon Tickets, X.509 Client Certificates, Java Add-In, Dual Stack, Diagtool, password<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><br />This note should help you identify the cause of logon problems when a solution for SSO is implemented. There are a number of different technical solutions available for providing SSO, so this note contains several chapters, one each for the technical means used to implement SSO.<br /><br />This note concentrates on problem analysis, but also mentions some of the most common specific issues. This note will <U>not</U> list <U>all</U> known issues that are related to SSO, as there are simply too many possible details involved. Unfortunately, even though the note concentrates only on the most common cases and how to analyze them, it is rather long.<br /><br />Keep in mind that SSO solutions may appear quite complicated, especially if they span over a broad variety of system types that may exist in your system landscape.<br /><br /><B>Differentiate the possible implementations/solutions</B><br /><br />The initial step to approach problems in SSO, of course, is to identify what SSO solution actually is being used. A first indicator is the client software: do you use a Web browser for accessing your servers, or do you use SAP's \"classical\" client program SAPGUI (sometimes called WinGUI)?<br />Attention: Newer client development and the related use of words make it easy to mix up certain versions of the Web browser-based clients with SAP's standalone client SAPGUI for ABAP servers. In the context of this note, the term SAPGUI will not be used for those Web-browser based clients.<br /><br />Web browser-based clients can use either of the following mechanisms for implementing SSO:</p> <UL><UL><LI>SAP logon tickets (covered in this note) or</LI></UL></UL> <UL><UL><LI>X.509 client certificates (covered in this note) or</LI></UL></UL> <UL><UL><LI>SAML Browser/Artifact Profile (AS Java since NW 2004, AS ABAP since NetWeaver 7.10) (NOT covered in this note)</LI></UL></UL> <p><br />The SAPGUI always requires Secure Network Communications (SNC) for SSO.<br /><br /><B><B>Typical Scenarios</B></B><br /><br />The most common situation where SSO is used are system landscapes that are made accessible by an SAP Enterprise Portal.<br />Here, usually SAP logon tickets are used, because they are easily available and can be created and accepted by all SAP server products. In a typical portal landscape, the SAP Enterprise Portal issues the user a logon ticket after the initial logon, which is then used for further authentication purposes, both in the portal itself as well as in all SAP back-end systems that are being made accessible from the portal. Some third party services can also use the SAP logon ticket for authentication through the use of SAPSSOEXT (a Java-based toolkit for verifying SAP logon tickets).<br /><br />A completely different situation exists when the client software for accessing ABAP-based SAP servers is SAPGUI. With SAPGUI, the only<br />way to implement SSO is to install SNC. SNC, in turn, can be implemented in a number of ways - but it always makes use of the GSS interface provided by SAPGUI and the ABAP-based SAP servers.<br /><br />For more general information about SSO, see SAP Notes 138498 and 550742.<br /><br />In the following, we refer to the servers involved as the AS ABAP and the AS Java for ABAP-based and Java-based system, respectively.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br />In the following, you will find hints on how to analyze problems with logon and Single Sign-On, ordered by the technical means for authentication that are being used.<br /><br /><B>1. SSO based on SAP Logon Tickets</B><br /><br />SAP logon tickets are messages that confirm a successful (primary) logon. They are created on the SAP server and are passed to the user's client. From there, they are also passed to the intended target system(s) (in newer implementations also directly to the target system). SAP logon tickets provide a \"simple\", but proprietary solution for Single Sign-On.<br /><br />The actual strategy for analyzing problems with SAP logon tickets is to follow the path of the SAP logon ticket from its creation, and then along the paths it takes in the network, until it is finally received and verified in the destination server.<br /><br />The (now outdated) note 356691 is intended to help with this, but it refers to older releases, where the \"standalone Internet Transaction Server (ITS)\" was common for Web-based access. This note may still be useful though in some situations, especially when you try to access SAP systems of older releases.<br /><br />To assert the authenticity of a user, SAP logon tickets make use of digital signatures based on the DSA algorithms. (These functions fall under the SAP component for Secure Store and Forward BC-SEC-SSF.) As a consequence, Secure Store and Forward (SSF) needs to be configured correctly to be able to verify the logon tickets. This includes the configuration for trusting the SAP logon ticket issuer (in terms of SSF), which is done by storing the logon ticket issuer's public key (certificate) in the target system's certificate store. (The target system is the system that verifies the incoming logon ticket.) Additionally, the logon ticket issuer needs to be entered into an access control list (ACL). The required steps differ between the involved system's base technology (ABAP or Java), but from the SSF point of view, they serve the same purpose.<br /><br />In general, tracing SAP logon tickets will follow the same pattern, which is independent from the base technology:</p> <OL>1. Check whether the initial logon was successful.</OL> <OL>2. Check whether a SAP logon ticket was issued after the initial logon.</OL> <OL>3. Check whether the SAP logon ticket was passed to the client software (Web browser).</OL> <OL>4. Check whether the same SAP logon ticket was forwarded to the target system for authentication.</OL> <OL>5. Check whether the SAP logon ticket was processed for authentication purposes on the target system.</OL> <OL>6. Check whether the SAP logon ticket was successfully verified on the target system.</OL> <p><br />As you see, after the SAP logon ticket is created, it is forwarded through several layers within the ticket-creating system and then passed to the user's Web browser (or to the receiving system directly). The Web browser then needs to decide whether to forward the ticket (a cookie named MYSAPSSO2) to the target server(s). There the logon ticket is received, and again forwarded through some internal layers of the server to finally be verified in the programs responsible for authentication. Each of these steps can fail, and in most cases, the result will be just another popup asking for the user's logon ID and password. Unfortunately, only rarely a significant error message can be displayed.<br /><br />1.1. SAP logon ticket creation and verification on the AS ABAP<br /><br />On the AS ABAP, the profile parameters login/create_sso2_ticket and login/accept_sso2_ticket control the creating and receiving of SAP logon tickets on the server. See the online documentation for details.<br /><br />Since SAP logon tickets make use of SSF, also the correct setup of SSF on the AS ABAP is required. To check the version of the installed security toolkit for SSF (and whether a security toolkit is available at all), run the report SSF02, using the default selection \"Determine version\". For further configuration of SSF for SAP logon tickets, which includes maintaining the Personal Security Environments (PSEs) of the server and maintaining the ACL, use the transaction STRUSTSSO2. Keep in mind that the maintenance here depends on the configuration of your system. Therefore, you may be required to run STRUSTSSO2 in both the working client (the one you are logged on to) and client 000. If you configure your system as recommended by SAP, the PSE used for handling SAP logon tickets is the system PSE, and in this case, all PSE and ACL maintenance can be done in the actual logon client.<br /><br />Note: If you need to replace a certificate for use with SAP logon tickets, make sure that you not only replace the certificate in the certificate list of the related PSE in the target system, but also that you replace the ACL entry, even if the new certificate uses the same subject name as the one to be replaced.<br /><br />The transaction SSO2 originally was set up to configure SAP logon tickets on mySAP Workplace systems. Keeping this in mind, you will find that SSO2 can still provide helpful information in analyzing SSO with logon tickets, but unfortunately is no longer so helpful in modern landscapes where an AS Java creates the SAP logon tickets for users (such as the portal).<br />In current landscapes, there is a configuration wizard available with the SAP NetWeaver Administrator that replaces the old SSO2 transaction. For more information, see SAP Notes 1083421 and 1014077.<br /><br />The most important procedure for analyzing problems when creating, accepting, or verifying SAP logon tickets on the AS ABAP is described in note 495911.<br />Codes and error messages that are reported in the logon trace according to note 495911 are explained in note 320991.<br /><br />Also see note 1055856 for several common issues related to using SAP logon tickets.<br /><br />The corrections from note 1159962 extend the AS ABAP's error messages with a message that refers to the specific issue that can occur when a Java-based system issues incomplete tickets (most probably due to configuration mistakes).<br /><br />Inside the AS ABAP, the Internet Connectivity Framework (ICF) is responsible for cookie handling. The SAP logon ticket is no exception, so you also need to check the security settings for applications in transaction SICF. The settings in SICF can be maintained differently for each Web service being offered.<br /><br />1.2. SAP logon ticket creation and verification on the AS Java<br /><br />On the AS Java, the creation and receiving of SAP logon tickets is configured in the login module stack of the service being called. Usually, it is sufficient to run the SSO2 wizard in the SAP NetWeaver Administrator to enable the use of SAP logon tickets (note 1083421).<br /><br />For storing the 'SAPLogonTicketKeypair', which is used to sign its SAP logon tickets, and for storing the public-key certificates of other ticket-issuing systems, the AS Java reserves a special view in the Key Storage service named 'TicketKeystore'. If you have to re-create the logon ticket key pair manually, make sure you create it in the 'TicketKeystore' view and give it the name 'SAPLogonTicketKeypair'. Also keep in mind that the key pair must use the DSA algorithm, and (for the time being) the validity of the certificate can not exceed January 1st, 2038. SAP Note 912229 describes how to replace the key pair and certificate on your AS Java.<br /><br />Also see the online documentation for your product for specific instructions.<br /><br />The most important checks for settings are summarized in note 701205. If checking these (static) settings does not help, use the diagnostic tool described in notes 1045019 and 957707 for further analysis.<br /><br />Finally, see note 1159962, which refers to a common configuration issue in the AS Java's login module stack and related error messages on the (receiving) AS ABAP.<br /><br />1.3. SAP logon tickets on a dual-stack system<br /><br />For the analysis of logon issues with SAP logon tickets on a dual-stack system, basically the two previous chapters apply at the same time.<br /><br />In addition, see the attachment to note 701205, named 'Add-InDoku.zip', which refers to settings that are specific for Add-In and dual-stack installations.<br /><br />1.4. Tracing the SAP logon ticket on the client (Web browser)<br /><br />On the client side, SAP logon tickets are stored as non-persistent cookies named MYSAPSSO2. This means the cookies are stored in the main memory of the Web browser process only. Of course, this main memory is volatile, which makes SAP logon tickets difficult to detect. In the most simple case, if you enter 'javascript:alert(document.cookie);' as an URL, the Web browser reveals the cookies that are currently known, but unfortunately this method of detecting cookies is not always helpful.<br /><br />A lot more information can be obtained from an HTTP trace, like URLs being used; names, domains and content of COOKIEs; or Web page content.<br />We recommended using an HTTP proxy tool to trace the HTTP traffic<br />directly at the client. SAP uses HttpWatch (http://www.simtec.ltd.uk or http://www.httpwatch.com). There is a free version that you can use to provide a trace file ('*.hwl' file) that can also be<br />analyzed/read in SAP support. Please ZIP before uploading!<br /><br />1.5. Considering COOKIE properties<br /><br />Since SAP logon tickets are handed over to the client (Web browser) in the form of a cookie, all cookie properties apply. This means that they are assigned a name ('MYSAPSSO2'), and the Web browsers will most likely only forward them to URLs inside the same DNS domain as that where they were created. Also, only one cookie with a given name can exist for a single domain.<br /><br />Keep in mind that SAP has no influence on the way cookies are handled inside Web browsers, as the Web browser's behaviour is defined by the respective vendor's programming.<br /><br />For further information related to cookies and Internet standards, see note 654982.<br /><br />1.6. Special cases<br /><br />There is a very specific situation where so-called reentrance tickets (a version of SAP logon tickets) can be used to log on to an HTTP-based session from an already existing SAPGUI session on the same application server. The solution described in note 612670 works for this specific situation. The use of this ticket for authentication on other servers (other instances or other systems) is neither intended nor supported.<br /><br />For integrating non-SAP components into an SSO landscape that uses SAP logon tickets, SAP provides an interface named SAPSSOEXT, which can be  used to evaluate and verify SAP logon tickets and return the data contained. For more information, see note 304450 and its referenced notes. Note 1040335 contains information about how to trace the activities of SAPSSOEXT.<br /><br /><B>2. SSO based on X.509 client certificates</B><br /><br />Based on Internet standards, X.509 client certificates offer a nonproprietary solution to implement SSO. The use of X.509 client certificates for authentication requires the target system to offer HTTPS/SSL (see notes 510007, 739043 or 1019634, resp.).<br /><br />In all cases, authentication using client certificates has two<br />prerequisites: the client certificate needs to be mapped to a user ID (logon name) that already exists in the system, and the client certificate needs to be accepted in terms of trust.<br /><br />Note that trust can only be configured for certificates that are issued by a Certification Authority (CA). You cannot exchange individual X.509 client certificates to establish trust in this case.<br /><br />2.1. X.509 Client Certificates on the AS ABAP<br /><br />The AS ABAP uses the PSE maintenance in transaction STRUST for establishing trust. The certificate(s) of the CA that has issued the client certificates need to be present in the certificate list of the SSL server PSE that is being used to handle incoming HTTPS/SSL requests.<br /><br />The mapping of the client certificate to the internal user ID is provided in the table USREXTID (maintenance view VUSREXTID). It takes the external identity type (here 'DN'), the subject name from the client certificate, and the internal user ID (that is, USR02-BNAME) as field values. Note that some encodings of the DN are interpreted differently in the kernel than in the ABAP layer. We recommend using plain ASCII for Distinguished Names (DN).<br /><br />The setting of the profile parameter icm/HTTPS/verify_client determines whether the AS ABAP generally accepts or requires X.509 client certificates. As with accepting SAP logon tickets, the actual ICF service's security settings (transaction SICF) may influence the system's behavior.<br /><br />For problem analysis when using X.509 client certificate authentication, see the developer traces provided with the procedure described in note 495911. In addition, the ICM traces can be helpful. For displaying the certificates that are exchanged between the client and server during SSL negotiation, set the trace level to 3. For all other SSL related messages, trace level=1 should be sufficient. (High trace levels reduce readability!).<br /><br />2.1.1. SAP Passport<br /><br />SAP offers the SAP Passport as a comfortable solution to provide X.509 client certificates to users of SAP systems. For more information, see the SAP Support Portal at http://service.sap.com/TCS and follow the links \"SAP Trust Center Services in Detail ==&gt; SAP Passports in your SAP solution\" and/or \"==&gt; Single Sign-On with SAP Passports\".<br /><br />In addition to the items already mentioned, when doing problem analysis, you need to keep an eye on the validity of the system's Registration Authority (RA) certificate. (This certificate is the server's own certificate and it is in the system PSE of your AS ABAP. It is signed by the SAP DSA CA).<br /><br />2.1.2. The general case<br /><br />In the case that you are not using SAP Passports, you need to provide the required system settings mentioned manually. In particular, the user mapping in VUSREXTID can be tricky if your client certificates contain non-ASCII characters. In this case, to get the correct representation of the subject name (owner name) from the client certificate, open the certificate in STRUST (menu \"Certificate --&gt; Import\") and copy the content of the field \"Owner\".<br /><br /><br />2.2. X.509 client certificates on the AS Java<br /><br />The AS Java uses the Keystore service for maintaining encryption keys, certificates, and therefore, to establish trust. The certificate(s) of the CA that has issued the client certificates need to be present in the Keystore view named 'TrustedCAs'.<br /><br />On the AS Java, the SSL service is where you configure whether a client certificate is accepted or required for authentication. For the published services, the configured login module stacks define the authentication options. If you want to accept X.509 client certificates for authentication, the 'ClientCertLoginModule' needs to be configured accordingly.<br /><br />It is also the ClientCertLoginModule that defines the rule(s) to use to map the client certificate to the user'd ID on the AS Java. Certificates can be stored directly with the user account (and thus are explicitly mapped), or the rule extracts the actual user name from the SubjectName or SubjectAlternativeName attributes contained in the X.509 client certificate.<br /><br />In case of issues, check the \"Troubleshooting\" topics in the online documentation for the User Management Engine (UME).<br /><br /><B>3. SSO based on SNC</B><br /><br />With one exception (*), SSO can be considered a by-product when implementing message encryption with SNC on the AS ABAP. Encrypting the data traffic of DIAG (SAPGUI&lt;--&gt;server) and RFC (server&lt;---&gt;server) protocols requires that each partner possesses keys to use for encryption, and these keys are also used for mutual authentication. Therefore, when using SNC for encryption, everything is in place for providing SSO.<br /><br />(*) The one exception is when implementing SSO by using the NTLM from the Microsoft Windows System Security Provider Interface. NTLM can only provide client-side authentication (which suffices for user-side SSO), but no encryption.<br /><br />Note 66687 gives a summary about SNC in general. More detailed generic information is contained in the online documentation.<br /><br />When analyzing SNC issues, you should always look into the traces that are written by the involved executables, that is, the SAPGUI trace if the SNC issue is on the client side, and the developer traces 'dev_w##' if the server side is concerned (display using transaction ST11).<br />Error messages related to SNC use the terms 'SNC' and/or 'GSS', so it is a good idea to search the server's work directory for occurrences of these terms using tools like 'grep' (UNIX) or 'find' (Windows).<br /><br />3.1. Third-party security products<br /><br />SNC uses 'Generic Security Service' (GSS). These GSS functions that are available for use with SNC are implemented in an \"external security product\", and not in the SAP kernel itself. This means that the functions themselves must be made available through dynamically loaded libraries (DLLs, shared libraries or shared objects) from a separate product.<br /><br />SAP does provide libraries that are licensed for use with server-to-server communications, however, not for use on the client side. So, SNC for SSO will always require in a third-party product. For issues with the SNC implementation of your chosen product, refer to the support organization of your respective vendor.<br /><br />3.2. SNC for Windows<br /><br />To make the security products that are available with the Microsoft Windows operating system (SSPI = System Security Provider Interface) available for the GSS interface used by SAP programs, SAP does provide \"wrapper libraries\" for either NTLM or Kerberos on Windows 32bit or 64bit platforms.<br /><br />See note 352295 for the libraries themselves as well as a description of the known issues with this SNC implementation.<br /><br />3.3. Unsupported solutions<br /><br />While the wrapper libraries attached to note 352295 make the Kerberos implementation coming with Microsoft Windows available, these are only available for pure Microsoft Windows landscapes. Other implementations of Kerberos may be interoperable, but SAP neither tests for interoperability, nor is SAP able to provide support for those Kerberos<br />implementations.<br /><br />See note 150380 for a more detailed discussion.<br /><br /><B>4. Related issues</B><br /><br />Finally, here are some additional issues that are more or less related to SSO.<br /><br />4.1. Password-based logon<br /><br />Password-based logon can behave like SSO if the password is being stored on the client side (example: destinations in transaction SM59).<br /><br />Note 622464 discusses the relation between user types and password expiration. For user types in general, see note 327917.<br /><br />In Releases beginning with 7.00, the AS ABAP offers new password rules (distinguishing upper and lower case, and an extended password length). In system landscapes where older and newer systems exist side by side, there may be issues when components that provide the \"old\" policies need to access newer systems. Note 1023437 discusses the use of downward-compatible passwords.<br /><br />Often, SSO is mistaken for solutions that provide identical passwords throughout a large number of systems. From our experience, such attemps for password synchronization can not work completely successfully and also reduce the level of individual password security. Hence, password synchronization is not supported, as note 376856 describes.<br /><br />4.2. SPNego<br /><br />This is a special initial login on AS Java that does not require a user to enter a password to access the server. It is based on the user's login to the Microsoft Windows domain. It makes use of the Windows APIs called 'SPNego'.<br /><br />The central note for issues related to SPNego (note 968191) unfortunately may contain some obsolete references and may also miss some of the newest related notes. However, it still offers a valid entry point for problem analysis and an overview of the concepts use. In addition, see the online documentation.<br /><br />In new releases of the AS ABAP, SPNego is also supported with SAP NetWeaver Single Sign-On release 2.0 (or newer). Downports exist also for older (supported) releases, please see note 1798979.<br /><br />4.3. SAP Shortcuts<br /><br />SAP Shortcuts can store the user's password and therefore simulate SSO. Nevertheless, because the 'normal' password authentication is being used, it is not regarded as a \"true\" SSO implementation.<br /><br />4.4. Other (third-party) products<br /><br />Other third-party products for SSO (for example CA's SiteMinder) may be functional, but since SAP's support does not have any experience or information available, therefore, contact the respective vendor's support channels if you have any questions or issues.<br /><br />4.5. Additional tips/common problems<br /><br />Several common error messages and issues that occur when setting up Single Sign-On are addressed in note 1055856.<br /><br />When using some implementations of SSO, it may be cumbersome to continue to keep your passwords valid. On the AS ABAP, you can set the profile parameter login/password_change_for_SSO to set whether passwords that have become invalid need to be updated. Note 869218 contains some corrections that need to be in place to make the API available for checking this profile parameter. Keep in mind that some front-end and middleware components may still require a related correction to make the concept finally work.<br /><br />A list of all error codes that may be recorded in the developer trace during processing logon attempts is provided in note 320991.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-IAM-SL (Please use BC-IAM-SSO*)"}, {"Key": "Other Components", "Value": "BC-JAS-SEC-LGN (Logon, SSO)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I059677)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D021767)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001257108/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001257108/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001257108/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001257108/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001257108/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001257108/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001257108/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001257108/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001257108/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "968191", "RefComponent": "BC-JAS-SEC", "RefTitle": "SPNego: Central Note", "RefUrl": "/notes/968191"}, {"RefNumber": "957707", "RefComponent": "BC-JAS-SEC", "RefTitle": "Using Diagtool for Troubleshooting Single Sign-On", "RefUrl": "/notes/957707"}, {"RefNumber": "912229", "RefComponent": "BC-JAS-SEC", "RefTitle": "WEBAS Java: SSO Public Key Certificate expires every 2 years", "RefUrl": "/notes/912229"}, {"RefNumber": "869218", "RefComponent": "BC-SEC-LGN", "RefTitle": "Option: No request to change password for SSO", "RefUrl": "/notes/869218"}, {"RefNumber": "823286", "RefComponent": "BC-JAS-SEC-UME", "RefTitle": "EP 6.0: Switching UserMapping from weak to strong encryption", "RefUrl": "/notes/823286"}, {"RefNumber": "817529", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Check of SSO configuration", "RefUrl": "/notes/817529"}, {"RefNumber": "754868", "RefComponent": "EP-EP5", "RefTitle": "User Mapping in the portal with BSP applications", "RefUrl": "/notes/754868"}, {"RefNumber": "739043", "RefComponent": "BC-JAS-SEC", "RefTitle": "How to Check for Full Strength Jurisdiction Policies", "RefUrl": "/notes/739043"}, {"RefNumber": "701205", "RefComponent": "BC-JAS-SEC-UME", "RefTitle": "Single Sign-On using SAP Logon Tickets", "RefUrl": "/notes/701205"}, {"RefNumber": "654982", "RefComponent": "BC", "RefTitle": "URL requirements due to Internet standards", "RefUrl": "/notes/654982"}, {"RefNumber": "622464", "RefComponent": "BC-SEC", "RefTitle": "Change: Password change requirement for user type \"SYSTEM\"", "RefUrl": "/notes/622464"}, {"RefNumber": "612670", "RefComponent": "BC-SEC", "RefTitle": "SSO for local BSP calls from SAPGUI HTML control", "RefUrl": "/notes/612670"}, {"RefNumber": "550742", "RefComponent": "BC-SEC", "RefTitle": "FAQ: General questions about Single Sign-On", "RefUrl": "/notes/550742"}, {"RefNumber": "510007", "RefComponent": "BC-SEC-SSL", "RefTitle": "Additional considerations about setting up SSL on Application Server ABAP", "RefUrl": "/notes/510007"}, {"RefNumber": "495911", "RefComponent": "BC-SEC", "RefTitle": "Logon problem trace analysis", "RefUrl": "/notes/495911"}, {"RefNumber": "376856", "RefComponent": "BC-SEC", "RefTitle": "Password synchronization - Single Sign-On/CUA", "RefUrl": "/notes/376856"}, {"RefNumber": "358470", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/358470"}, {"RefNumber": "356691", "RefComponent": "BC-SEC-LGN", "RefTitle": "Problem analysis: SAP logon ticket with Workplace SSO", "RefUrl": "/notes/356691"}, {"RefNumber": "352295", "RefComponent": "BC-SEC-SNC", "RefTitle": "Microsoft Windows Single Sign-On options", "RefUrl": "/notes/352295"}, {"RefNumber": "327917", "RefComponent": "BC-SEC-USR-ADM", "RefTitle": "New user types as of Release 4.6C", "RefUrl": "/notes/327917"}, {"RefNumber": "320991", "RefComponent": "BC-SEC-LGN", "RefTitle": "Error codes during logon (list)", "RefUrl": "/notes/320991"}, {"RefNumber": "1798979", "RefComponent": "BC-SEC-LGN", "RefTitle": "SPNego ABAP: Downport", "RefUrl": "/notes/1798979"}, {"RefNumber": "1468217", "RefComponent": "BC-JAS-SEC", "RefTitle": "Single Sign-On problems - information required by Support", "RefUrl": "/notes/1468217"}, {"RefNumber": "1378659", "RefComponent": "BC-FES-BUS", "RefTitle": "NWBC known issues & what to check when opening a ticket", "RefUrl": "/notes/1378659"}, {"RefNumber": "1237327", "RefComponent": "BC-JAS-SEC-UME", "RefTitle": "SSO issues because of invalid/deleted SAP reference system", "RefUrl": "/notes/1237327"}, {"RefNumber": "1159962", "RefComponent": "BC-SEC-LGN", "RefTitle": "Ticket contains no/an empty ABAP user ID", "RefUrl": "/notes/1159962"}, {"RefNumber": "1107795", "RefComponent": "BC-JAS-SEC-UME", "RefTitle": "Corrupted user mappings for the SAP reference system", "RefUrl": "/notes/1107795"}, {"RefNumber": "1083421", "RefComponent": "BC-JAS-SEC-LGN", "RefTitle": "SSO2 Wizard", "RefUrl": "/notes/1083421"}, {"RefNumber": "1055856", "RefComponent": "BC-SEC-SSF", "RefTitle": "Common error messages when setting up Single Sign-On", "RefUrl": "/notes/1055856"}, {"RefNumber": "1045019", "RefComponent": "BC-JAS-SEC", "RefTitle": "Web diagtool for collecting traces", "RefUrl": "/notes/1045019"}, {"RefNumber": "1040335", "RefComponent": "BC-SEC-SSF", "RefTitle": "SAPSSOEXT Patch 4: Corrections and enhancements", "RefUrl": "/notes/1040335"}, {"RefNumber": "1026733", "RefComponent": "BC-JAS-SEC-UME", "RefTitle": "Logon and assertion tickets with \"DDIC\" as ABAP user ID", "RefUrl": "/notes/1026733"}, {"RefNumber": "1023437", "RefComponent": "BC-SEC-LGN", "RefTitle": "ABAP syst: Downwardly incompatible passwords (since NW2004s)", "RefUrl": "/notes/1023437"}, {"RefNumber": "1019634", "RefComponent": "BC-JAS-SEC", "RefTitle": "Troubleshooting SSL problems", "RefUrl": "/notes/1019634"}, {"RefNumber": "1014077", "RefComponent": "BC-SEC-LGN", "RefTitle": "Downport: API for SSO2 trust configuration (ABAP)", "RefUrl": "/notes/1014077"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2977533", "RefComponent": "BC-SEC-LGN", "RefTitle": "SSO with SAP Logon Tickets between two ABAP systems in different Domains does not work", "RefUrl": "/notes/2977533 "}, {"RefNumber": "2564192", "RefComponent": "BC-SEC-LGN-SML", "RefTitle": "Is that possible to use SAML2.0 with SAP GUI connections?", "RefUrl": "/notes/2564192 "}, {"RefNumber": "3080175", "RefComponent": "BC-CST-STS", "RefTitle": "Some sapstartsrv Web service methods return \"DpGetAssertionTicket failed\" error", "RefUrl": "/notes/3080175 "}, {"RefNumber": "352295", "RefComponent": "BC-SEC-SNC", "RefTitle": "Microsoft Windows Single Sign-On options", "RefUrl": "/notes/352295 "}, {"RefNumber": "1798979", "RefComponent": "BC-SEC-LGN", "RefTitle": "SPNego ABAP: Downport", "RefUrl": "/notes/1798979 "}, {"RefNumber": "1378659", "RefComponent": "BC-FES-BUS", "RefTitle": "NWBC known issues & what to check when opening a ticket", "RefUrl": "/notes/1378659 "}, {"RefNumber": "320991", "RefComponent": "BC-SEC-LGN", "RefTitle": "Error codes during logon (list)", "RefUrl": "/notes/320991 "}, {"RefNumber": "912229", "RefComponent": "BC-JAS-SEC", "RefTitle": "WEBAS Java: SSO Public Key Certificate expires every 2 years", "RefUrl": "/notes/912229 "}, {"RefNumber": "701205", "RefComponent": "BC-JAS-SEC-UME", "RefTitle": "Single Sign-On using SAP Logon Tickets", "RefUrl": "/notes/701205 "}, {"RefNumber": "1026733", "RefComponent": "BC-JAS-SEC-UME", "RefTitle": "Logon and assertion tickets with \"DDIC\" as ABAP user ID", "RefUrl": "/notes/1026733 "}, {"RefNumber": "823286", "RefComponent": "BC-JAS-SEC-UME", "RefTitle": "EP 6.0: Switching UserMapping from weak to strong encryption", "RefUrl": "/notes/823286 "}, {"RefNumber": "1237327", "RefComponent": "BC-JAS-SEC-UME", "RefTitle": "SSO issues because of invalid/deleted SAP reference system", "RefUrl": "/notes/1237327 "}, {"RefNumber": "1055856", "RefComponent": "BC-SEC-SSF", "RefTitle": "Common error messages when setting up Single Sign-On", "RefUrl": "/notes/1055856 "}, {"RefNumber": "968191", "RefComponent": "BC-JAS-SEC", "RefTitle": "SPNego: Central Note", "RefUrl": "/notes/968191 "}, {"RefNumber": "754868", "RefComponent": "EP-EP5", "RefTitle": "User Mapping in the portal with BSP applications", "RefUrl": "/notes/754868 "}, {"RefNumber": "1083421", "RefComponent": "BC-JAS-SEC-LGN", "RefTitle": "SSO2 Wizard", "RefUrl": "/notes/1083421 "}, {"RefNumber": "612670", "RefComponent": "BC-SEC", "RefTitle": "SSO for local BSP calls from SAPGUI HTML control", "RefUrl": "/notes/612670 "}, {"RefNumber": "654982", "RefComponent": "BC", "RefTitle": "URL requirements due to Internet standards", "RefUrl": "/notes/654982 "}, {"RefNumber": "1045019", "RefComponent": "BC-JAS-SEC", "RefTitle": "Web diagtool for collecting traces", "RefUrl": "/notes/1045019 "}, {"RefNumber": "510007", "RefComponent": "BC-SEC-SSL", "RefTitle": "Additional considerations about setting up SSL on Application Server ABAP", "RefUrl": "/notes/510007 "}, {"RefNumber": "1468217", "RefComponent": "BC-JAS-SEC", "RefTitle": "Single Sign-On problems - information required by Support", "RefUrl": "/notes/1468217 "}, {"RefNumber": "356691", "RefComponent": "BC-SEC-LGN", "RefTitle": "Problem analysis: SAP logon ticket with Workplace SSO", "RefUrl": "/notes/356691 "}, {"RefNumber": "358470", "RefComponent": "BC-SEC", "RefTitle": "Übersicht: Anmeldevarianten", "RefUrl": "/notes/358470 "}, {"RefNumber": "495911", "RefComponent": "BC-SEC", "RefTitle": "Logon problem trace analysis", "RefUrl": "/notes/495911 "}, {"RefNumber": "739043", "RefComponent": "BC-JAS-SEC", "RefTitle": "How to Check for Full Strength Jurisdiction Policies", "RefUrl": "/notes/739043 "}, {"RefNumber": "1107795", "RefComponent": "BC-JAS-SEC-UME", "RefTitle": "Corrupted user mappings for the SAP reference system", "RefUrl": "/notes/1107795 "}, {"RefNumber": "1159962", "RefComponent": "BC-SEC-LGN", "RefTitle": "Ticket contains no/an empty ABAP user ID", "RefUrl": "/notes/1159962 "}, {"RefNumber": "1040335", "RefComponent": "BC-SEC-SSF", "RefTitle": "SAPSSOEXT Patch 4: Corrections and enhancements", "RefUrl": "/notes/1040335 "}, {"RefNumber": "622464", "RefComponent": "BC-SEC", "RefTitle": "Change: Password change requirement for user type \"SYSTEM\"", "RefUrl": "/notes/622464 "}, {"RefNumber": "869218", "RefComponent": "BC-SEC-LGN", "RefTitle": "Option: No request to change password for SSO", "RefUrl": "/notes/869218 "}, {"RefNumber": "327917", "RefComponent": "BC-SEC-USR-ADM", "RefTitle": "New user types as of Release 4.6C", "RefUrl": "/notes/327917 "}, {"RefNumber": "1023437", "RefComponent": "BC-SEC-LGN", "RefTitle": "ABAP syst: Downwardly incompatible passwords (since NW2004s)", "RefUrl": "/notes/1023437 "}, {"RefNumber": "1014077", "RefComponent": "BC-SEC-LGN", "RefTitle": "Downport: API for SSO2 trust configuration (ABAP)", "RefUrl": "/notes/1014077 "}, {"RefNumber": "957707", "RefComponent": "BC-JAS-SEC", "RefTitle": "Using Diagtool for Troubleshooting Single Sign-On", "RefUrl": "/notes/957707 "}, {"RefNumber": "1019634", "RefComponent": "BC-JAS-SEC", "RefTitle": "Troubleshooting SSL problems", "RefUrl": "/notes/1019634 "}, {"RefNumber": "550742", "RefComponent": "BC-SEC", "RefTitle": "FAQ: General questions about Single Sign-On", "RefUrl": "/notes/550742 "}, {"RefNumber": "817529", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Check of SSO configuration", "RefUrl": "/notes/817529 "}, {"RefNumber": "376856", "RefComponent": "BC-SEC", "RefTitle": "Password synchronization - Single Sign-On/CUA", "RefUrl": "/notes/376856 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}