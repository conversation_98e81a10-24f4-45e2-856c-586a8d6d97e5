{"Request": {"Number": "1619185", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 269, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017289552017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=EF07ACD7EDB4D8724206C63158E8C866"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1619185"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.01.2016"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-RA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Rental Accounting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Rental Accounting", "value": "RE-FX-RA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-RA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1619185 - FAQ: Accrual/deferral (contract)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The note contains links to consulting notes and notes to<br />support troubleshooting for the accruals and deferrals for the contract in RE-FX.<br />With regard to the service charge settlement, see SAP Note 924831, which contains FAQs about settlements.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>RERAALCN, REISALIT, REISACITEM, REEXACRSHOW</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>For more information, see the related notes.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "RE-FX-CN (Contract)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D002072)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I001503)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "939972", "RefComponent": "RE-FX-RA", "RefTitle": "FAQ: Accounting", "RefUrl": "/notes/939972"}, {"RefNumber": "939971", "RefComponent": "RE-FX", "RefTitle": "FAQ and consulting notes for RE-FX", "RefUrl": "/notes/939971"}, {"RefNumber": "829676", "RefComponent": "RE-FX-RA", "RefTitle": "Consulting note: Accruals/deferrals in RE-FX", "RefUrl": "/notes/829676"}, {"RefNumber": "1567160", "RefComponent": "RE-FX-RA", "RefTitle": "RE-FX: New accruals/deferrals info system report REISACRITEM", "RefUrl": "/notes/1567160"}, {"RefNumber": "1516683", "RefComponent": "RE-FX-RA", "RefTitle": "RERAALCN: BAdI BADI_REEX_AL_ACROBJ", "RefUrl": "/notes/1516683"}, {"RefNumber": "1507525", "RefComponent": "RE-FX-RA", "RefTitle": "RERAALCN: Document list in simulation run", "RefUrl": "/notes/1507525"}, {"RefNumber": "1435886", "RefComponent": "RE-FX-RA", "RefTitle": "RERAALCN: Accrual/deferral frequency \"Annually\"", "RefUrl": "/notes/1435886"}, {"RefNumber": "1428133", "RefComponent": "RE-FX-RA", "RefTitle": "RERAALCN: Accrual/deferral per posting period", "RefUrl": "/notes/1428133"}, {"RefNumber": "1378001", "RefComponent": "RE-FX-RA", "RefTitle": "RERAALCN: Accr/def w/ posting date that is diff to key date", "RefUrl": "/notes/1378001"}, {"RefNumber": "1133406", "RefComponent": "RE-FX-BD", "RefTitle": "Master data: Functional location, automatic creation control", "RefUrl": "/notes/1133406"}, {"RefNumber": "1062550", "RefComponent": "RE-FX-RA", "RefTitle": "RERAALCN: Enhancement for accrual/deferral logic", "RefUrl": "/notes/1062550"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "939972", "RefComponent": "RE-FX-RA", "RefTitle": "FAQ: Accounting", "RefUrl": "/notes/939972 "}, {"RefNumber": "939971", "RefComponent": "RE-FX", "RefTitle": "FAQ and consulting notes for RE-FX", "RefUrl": "/notes/939971 "}, {"RefNumber": "829676", "RefComponent": "RE-FX-RA", "RefTitle": "Consulting note: Accruals/deferrals in RE-FX", "RefUrl": "/notes/829676 "}, {"RefNumber": "1133406", "RefComponent": "RE-FX-BD", "RefTitle": "Master data: Functional location, automatic creation control", "RefUrl": "/notes/1133406 "}, {"RefNumber": "1062550", "RefComponent": "RE-FX-RA", "RefTitle": "RERAALCN: Enhancement for accrual/deferral logic", "RefUrl": "/notes/1062550 "}, {"RefNumber": "1567160", "RefComponent": "RE-FX-RA", "RefTitle": "RE-FX: New accruals/deferrals info system report REISACRITEM", "RefUrl": "/notes/1567160 "}, {"RefNumber": "1378001", "RefComponent": "RE-FX-RA", "RefTitle": "RERAALCN: Accr/def w/ posting date that is diff to key date", "RefUrl": "/notes/1378001 "}, {"RefNumber": "1435886", "RefComponent": "RE-FX-RA", "RefTitle": "RERAALCN: Accrual/deferral frequency \"Annually\"", "RefUrl": "/notes/1435886 "}, {"RefNumber": "1428133", "RefComponent": "RE-FX-RA", "RefTitle": "RERAALCN: Accrual/deferral per posting period", "RefUrl": "/notes/1428133 "}, {"RefNumber": "1507525", "RefComponent": "RE-FX-RA", "RefTitle": "RERAALCN: Document list in simulation run", "RefUrl": "/notes/1507525 "}, {"RefNumber": "1516683", "RefComponent": "RE-FX-RA", "RefTitle": "RERAALCN: BAdI BADI_REEX_AL_ACROBJ", "RefUrl": "/notes/1516683 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}