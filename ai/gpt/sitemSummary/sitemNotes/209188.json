{"Request": {"Number": "209188", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 540, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000209188?language=E&token=33D0B32DBAB3A8A1A622BAB3B7AEE34A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000209188", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000209188/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "209188"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 46}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "23.01.2004"}, "SAPComponentKey": {"_label": "Component", "value": "BC-FES-CTL"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP GUI for Windows Controls"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Frontend Services (SAP Note 1322184)", "value": "BC-FES", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-FES*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP GUI for Windows Controls", "value": "BC-FES-CTL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-FES-CTL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "209188 - Collective note: TextEdit control"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Collective note; TextEdit control; CL_GUI_TEXTEDIT; C_TEXTEDIT_CONTROL</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Overview of all existing notes regarding TextEdit Control</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>This note provides you with a list of all notes grouped according to the relevant release.<br />Next to the Note number you will find a short description of the error as well as an overview of the required correction for the backend and the various GUI frontends.<br /><br />You can download patches from the following URL:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;http://service.sap.com/patches<br /><br /> Note | symptom + correction classification for backend and frontend<br />________________________________________________________________________<br />6.20<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-----------------------------<br /> 695395&#x00A0;&#x00A0;&#x00A0;&#x00A0;Unicode-complicance of text in status line<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Backend:&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Frontend: Import Patch 43 - or higher<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-----------------------------<br /> 689308&#x00A0;&#x00A0;&#x00A0;&#x00A0;Scroll behavior of vertical scroll bar<br /> 686236&#x00A0;&#x00A0;&#x00A0;&#x00A0;Memory leak removed<br /> 680072&#x00A0;&#x00A0;&#x00A0;&#x00A0;Multibyte and Unicode handling of stream tables<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Kernel:&#x00A0;&#x00A0; Patch level 1193<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Backend:&#x00A0;&#x00A0;SAPKB62034<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Frontend: Import Patch 41 - or higher<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-----------------------------<br /> 640595&#x00A0;&#x00A0;&#x00A0;&#x00A0;Script Recording: Multiple recording of a text<br /> 631865&#x00A0;&#x00A0;&#x00A0;&#x00A0;Tool tip for status text<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Backend:&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Frontend: Import Patch 36 - or higher<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;----------------------------<br /> 631865&#x00A0;&#x00A0;&#x00A0;&#x00A0;Tool tip for status text in status line<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Backend:&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Frontend: Import Patch 35 - or higher<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-----------------------------<br /> 0611619&#x00A0;&#x00A0; Memory leak when saving<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Backend:&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Frontend: Import Patch 32 - or higher<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-----------------------------<br /> 0604980&#x00A0;&#x00A0; GUI termination in the modification assistant<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Backend:&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Frontend: Import Patch 29 - or higher<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-----------------------------<br /> 0589410&#x00A0;&#x00A0; GUI termination during special editing sequences of Internet Services in the development environment<br /> 0593006&#x00A0;&#x00A0; GUI termination when finding and replacing under certain conditions<br /> 0584322&#x00A0;&#x00A0; Update of the status bar after indent/outdent<br /> 0584331&#x00A0;&#x00A0; Flicker when using the wheelmouse wheel in the first line<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Backend:&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Frontend: Import Patch 27 - or higher<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-----------------------------<br /> 0548440&#x00A0;&#x00A0; Improved search functions<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Backend:&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Frontend: Import Patch 18 - or higher<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-----------------------------<br /> 0548433&#x00A0;&#x00A0; Text alignment via shortcut<br /> 0547567&#x00A0;&#x00A0; Exception when formatting text<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Backend:&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Frontend: Import Patch 17 - or higher<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-----------------------------<br /> 0541744&#x00A0;&#x00A0; TextEdit Control: Problems with text formatting<br /> 0539262&#x00A0;&#x00A0; TextEdit Control: Performance when saving mass data<br /> 0539260&#x00A0;&#x00A0; TextEdit Control: Loading local files<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Backend:&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Frontend: Import Patch 16&#x00A0;&#x00A0;- or higher<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-----------------------------<br /> 0524917&#x00A0;&#x00A0; \"Exception 0xC0000005 with ID=0x549121BB/0x549121BB occured\"<br /> 0524967&#x00A0;&#x00A0; Text Edit Control: Redrawing window contents<br /> 0522601&#x00A0;&#x00A0; Text Edit Control: Instantiation error<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Backend:&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Frontend: Import Patch 9&#x00A0;&#x00A0;- or higher<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-----------------------------<br /> 0510866&#x00A0;&#x00A0; TextEdit control: status of buffered attributes<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Backend:&#x00A0;&#x00A0;See Note text<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Frontend:&#x00A0;&#x00A0;-<br /><br /><br />________________________________________________________________________<br /><br />6.10&#x00A0;&#x00A0;&#x00A0;&#x00A0;is no longer maintained, see Note 147519<br /><br />________________________________________________________________________<br />4.6D<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-----------------------------<br /> 0611619&#x00A0;&#x00A0; Memory leak when saving<br /> 0593006&#x00A0;&#x00A0; GUI termination when finding and replacing under certain conditions<br /> 0584331&#x00A0;&#x00A0; Flicker when using the wheelmouse wheel in the first line<br /> 0584322&#x00A0;&#x00A0; Update of the status bar after indent/outdent<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Backend:&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Frontend: Import Patch 653 - or higher<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -----------------------------<br /> 0548440&#x00A0;&#x00A0; Improve search functionality<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Backend:&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Frontend: Import Patch 620 - or higher<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-----------------------------<br /> 0548433&#x00A0;&#x00A0; Text alignment via shortcut<br /> 0547567&#x00A0;&#x00A0; Exception when formatting text<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Backend:&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Frontend: Import Patch 613 - or higher<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-----------------------------<br /> 0541744&#x00A0;&#x00A0; TextEdit Control: Problems with text formatting<br /> 0539262&#x00A0;&#x00A0; TextEdit Control: Performance when saving mass data<br /> 0539260&#x00A0;&#x00A0; TextEdit Control: Loading local files<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Backend:&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Frontend: Import Patch 606&#x00A0;&#x00A0;- or higher<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-----------------------------<br /> 0524917&#x00A0;&#x00A0; \"Exception 0xC0000005 with ID=0x549121BB/0x549121BB occured\"<br /> 0524967&#x00A0;&#x00A0; Neuzeichnen des Fensterinhaltes<br /> 0522601&#x00A0;&#x00A0; Fehler bei der Instanziierung<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Backend:&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Frontend: Import Patch 584 - or newer<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;------------------------------<br /> 0430547&#x00A0;&#x00A0;&#x00A0;&#x00A0;Opening read-only files<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Backend:&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Frontend: Import patch gui46D_459.exe - or newer<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-----------------------------<br /> 0428254&#x00A0;&#x00A0;&#x00A0;&#x00A0;Shift+Enter leads to special characters in the text<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Backend:&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Frontend: Import patch gui46D_448.exe - or newer<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-----------------------------<br /> 0409846&#x00A0;&#x00A0;&#x00A0;&#x00A0; Wheel Mouse and selection change<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Backend:&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Frontend: Import patch gui46D_394.exe - or newer<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; --------------------------<br />0385145&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Modification Status<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Backend:&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Frontend: Import patch gui46D_310.exe - or newer<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; --------------------------<br />0382035&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Windows2000 line break problem<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Backend:&#x00A0;&#x00A0; -<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Frontend: Import patch gui46D_292.exe - or newer<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-----------------------------<br />0382029&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Ins/Ovr synchronization with SAPGUI<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Backend:&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Frontend: Import gui46D_529.exe patch - or newer<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; --------------------------------<br /> 338219&#x00A0;&#x00A0;&#x00A0;&#x00A0; Codepage for Unicode conversion<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Backend: -<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Frontend: Import patch gui46D_168.exe - or newer<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-----------------------------<br /><br /> 333599&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAP Drag and Drop does not work<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Backend:&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Frontend: Patch gui46D_168.exe - or newer<br /><br /> 321865&#x00A0;&#x00A0;&#x00A0;&#x00A0; Behavior for error tolerant codepage converson<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Backend:&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Frontend: Import patch gui46D_123.exe - or newer<br />Earlier 4.6 versions (4.6A, 4.6B, and 4.6C) are no longer maintained;<br />see Note 147519.<br />Please install a current SAPGUI.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-FES-GUI (SAP GUI for Windows)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D031909)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D031829)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000209188/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000209188/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000209188/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000209188/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000209188/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000209188/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000209188/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000209188/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000209188/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "888810", "RefComponent": "BC-FES-WGU", "RefTitle": "ITS TextEdit: TextEdit Control is too long.", "RefUrl": "/notes/888810"}, {"RefNumber": "680072", "RefComponent": "BC-FES-CTL", "RefTitle": "TextEdit Control: Multibyte handling of stream tables", "RefUrl": "/notes/680072"}, {"RefNumber": "548440", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/548440"}, {"RefNumber": "539262", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/539262"}, {"RefNumber": "524967", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/524967"}, {"RefNumber": "510866", "RefComponent": "BC-FES-CTL", "RefTitle": "TextEdit control: status of buffered attributes", "RefUrl": "/notes/510866"}, {"RefNumber": "430547", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/430547"}, {"RefNumber": "428254", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/428254"}, {"RefNumber": "419818", "RefComponent": "BC-FES-CTL", "RefTitle": "TextEdit Control: Limit for number of characters per line", "RefUrl": "/notes/419818"}, {"RefNumber": "415886", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/415886"}, {"RefNumber": "415885", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/415885"}, {"RefNumber": "409846", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/409846"}, {"RefNumber": "401637", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/401637"}, {"RefNumber": "390367", "RefComponent": "BC-FES-CTL", "RefTitle": "TextEdit Control: Cursor/Selection position", "RefUrl": "/notes/390367"}, {"RefNumber": "385145", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/385145"}, {"RefNumber": "382035", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/382035"}, {"RefNumber": "382029", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/382029"}, {"RefNumber": "338219", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/338219"}, {"RefNumber": "333599", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/333599"}, {"RefNumber": "321865", "RefComponent": "BC-FES-CTL", "RefTitle": "TextEdit contr: Proced f error toler code page conversn", "RefUrl": "/notes/321865"}, {"RefNumber": "209186", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/209186"}, {"RefNumber": "188179", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/188179"}, {"RefNumber": "186567", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/186567"}, {"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519"}, {"RefNumber": "146595", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/146595"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519 "}, {"RefNumber": "680072", "RefComponent": "BC-FES-CTL", "RefTitle": "TextEdit Control: Multibyte handling of stream tables", "RefUrl": "/notes/680072 "}, {"RefNumber": "321865", "RefComponent": "BC-FES-CTL", "RefTitle": "TextEdit contr: Proced f error toler code page conversn", "RefUrl": "/notes/321865 "}, {"RefNumber": "888810", "RefComponent": "BC-FES-WGU", "RefTitle": "ITS TextEdit: TextEdit Control is too long.", "RefUrl": "/notes/888810 "}, {"RefNumber": "390367", "RefComponent": "BC-FES-CTL", "RefTitle": "TextEdit Control: Cursor/Selection position", "RefUrl": "/notes/390367 "}, {"RefNumber": "510866", "RefComponent": "BC-FES-CTL", "RefTitle": "TextEdit control: status of buffered attributes", "RefUrl": "/notes/510866 "}, {"RefNumber": "419818", "RefComponent": "BC-FES-CTL", "RefTitle": "TextEdit Control: Limit for number of characters per line", "RefUrl": "/notes/419818 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}