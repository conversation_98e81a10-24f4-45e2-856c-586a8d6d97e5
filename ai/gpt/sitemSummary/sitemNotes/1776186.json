{"Request": {"Number": "1776186", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 625, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000010494942017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001776186?language=E&token=4759F4B2832896745903B52FF4092FFB"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001776186", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001776186/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1776186"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 12}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Performance"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "08.07.2014"}, "SAPComponentKey": {"_label": "Component", "value": "HAN-DB-ENG"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP HANA DB Engines"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP HANA", "value": "HAN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'HAN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP HANA Database", "value": "HAN-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'HAN-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP HANA DB Engines", "value": "HAN-DB-ENG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'HAN-DB-ENG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1776186 - HANA - Scale out: routing to right indexserver"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>******************<br />This note is only for performance imporvement of BW on HANA which runs in the scale-out environment.<br />******************</p>\r\n<p><br />in most cases, stored procedure based queries of BW are sent to an indexserver where the actual query objects (table, view, etc) do not exist. And the indexserver which received the request must forward it to a right indexserver. This situation leads to a performance problem.</p>\r\n<p><span style=\"text-decoration: underline;\">!!! Important information for customers of ABAP Basis Release 731 &#160;!!!</span></p>\r\n<p style=\"padding-left: 30px;\">If the ABAP system runs in the following conditions:</p>\r\n<ul>\r\n<ul>\r\n<li>ABAP Basis Release 731 and</li>\r\n<li>ABAP Basis Support Package 6 and</li>\r\n<li>ABAP Kernel Version &lt; 721</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">because changes of this note is only partially available in the ABAP system, BW queries will break ABAP sessions. To avoid this severe situation, apply all the remaining ABAP changes of this note immediately which are available as source code corrections. Just to avoid this situation, applying only ABAP changes is sufficient, and it is not necessary to upgrade the ABAP kernel to 721 or higher. But the kernel Update is required later to get the performance improvement.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>trexviadbsl, trexviadbslwithparameter, TREX_EXT_AGGREGATE, TREX_DBS_AGGREGATE, scaleout</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The current routing mechanism for the stored procedure based queries is Round-Robin. This does not consider the locations of query objects.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Apply all of the following fixes:</p>\r\n<ol>1. ABAP support package or source code correction</ol><ol>2. SAP HANA SPS 5 (= Revision 45) or later</ol><ol>3. ABAP kernel version must be 721 or later. Refer to the attached note 1716826.</ol>\r\n<p><br />After applying the patch, authorization of required DB users/schemas (normally the user name is SAP&lt;SID of BW system&gt;) needs to be edited on HANA Studio. Add the following SQL object on the tab \"SQL Privileges\":<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<strong>TREXVIADBSLWITHPARAMETER (SYS)</strong><br />and select \"EXECUTE\" with \"No\" for grantable to others<br /><br /><strong><span style=\"text-decoration: underline;\">Additional information</span></strong></p>\r\n<ul>\r\n<li>Only in case that the above three corrections (ABAP + HANA + ABAP Kernel)&#160;&#160;are applied, this feature will be activated. And the order of applying the corrections does not matter.</li>\r\n</ul>\r\n<ul>\r\n<li>If some of the corrections are missing, this feature will not be used at all. The system runs just as before.</li>\r\n</ul>\r\n<ul>\r\n<li>It is harmless to apply the corrections to a single node system. This feature will just not be used in the system.</li>\r\n</ul>\r\n<ul>\r\n<li>It is recommended applying the related note 1798043 as well. This feature could run into the problem described in the note, if very many queries are executed in parallel.</li>\r\n</ul></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D027382)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D048063)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001776186/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001776186/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001776186/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001776186/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001776186/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001776186/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001776186/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001776186/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001776186/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1798043", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA BW: lock error on shared memory object", "RefUrl": "/notes/1798043"}, {"RefNumber": "1769670", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "Important Notes for SAP BW powered by HANA on SP8", "RefUrl": "/notes/1769670"}, {"RefNumber": "1716826", "RefComponent": "BC-CST", "RefTitle": "Usage of the downward compatible kernel 721 (EXT)", "RefUrl": "/notes/1716826"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1716826", "RefComponent": "BC-CST", "RefTitle": "Usage of the downward compatible kernel 721 (EXT)", "RefUrl": "/notes/1716826 "}, {"RefNumber": "1769670", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "Important Notes for SAP BW powered by HANA on SP8", "RefUrl": "/notes/1769670 "}, {"RefNumber": "1798043", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA BW: lock error on shared memory object", "RefUrl": "/notes/1798043 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "740", "To": "740", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 730", "SupportPackage": "SAPKB73010", "URL": "/supportpackage/SAPKB73010"}, {"SoftwareComponentVersion": "SAP_BASIS 730", "SupportPackage": "SAPKB73009", "URL": "/supportpackage/SAPKB73009"}, {"SoftwareComponentVersion": "SAP_BASIS 731", "SupportPackage": "SAPKB73107", "URL": "/supportpackage/SAPKB73107"}, {"SoftwareComponentVersion": "SAP_BASIS 731", "SupportPackage": "SAPKB73106", "URL": "/supportpackage/SAPKB73106"}, {"SoftwareComponentVersion": "SAP_BASIS 731", "SupportPackage": "SAPKB73109", "URL": "/supportpackage/SAPKB73109"}, {"SoftwareComponentVersion": "SAP_BASIS 740", "SupportPackage": "SAPKB74004", "URL": "/supportpackage/SAPKB74004"}, {"SoftwareComponentVersion": "SAP_BASIS 740", "SupportPackage": "SAPKB74001", "URL": "/supportpackage/SAPKB74001"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 9, "URL": "/corrins/0001776186/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Activity&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_BASIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Basis compo...|<br/>| Release 730&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB73008 - SAPKB73008&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 731&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB73105 - SAPKB73105&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>Create user parameter HDB_TREXVIADBSLPARAM:</P> <OL>1. go to transaction SE80</OL> <OL>2. select \"PACKAGE\" in the dropdown menu</OL> <OL>3. set \"STREX\" on the package name field</OL> <OL>4. right click on \"SET/GET Parameter\" and select \"CREATE\"</OL> <OL>5. Parameter ID: HDB_TREXVIADBSLPARAM</OL> <OL>6. Short Description: Switch of TrexViaDbsl</OL> <OL>7. Save</OL> <P><br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 9, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 4, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1750462 ", "URL": "/notes/1750462 ", "Title": "TREX connection error on HANA system", "Component": "HAN-DB"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1760745 ", "URL": "/notes/1760745 ", "Title": "SAP HANA for BW: search result error 2618", "Component": "HAN-DB"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1769038 ", "URL": "/notes/1769038 ", "Title": "SAP HANA BW: improve error message from TREX_EXT_AGGREGATE", "Component": "HAN-DB"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1776186 ", "URL": "/notes/1776186 ", "Title": "HANA - Scale out: routing to right indexserver", "Component": "HAN-DB-ENG"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "1750462 ", "URL": "/notes/1750462 ", "Title": "TREX connection error on HANA system", "Component": "HAN-DB"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "1760745 ", "URL": "/notes/1760745 ", "Title": "SAP HANA for BW: search result error 2618", "Component": "HAN-DB"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "1769038 ", "URL": "/notes/1769038 ", "Title": "SAP HANA BW: improve error message from TREX_EXT_AGGREGATE", "Component": "HAN-DB"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "1776186 ", "URL": "/notes/1776186 ", "Title": "HANA - Scale out: routing to right indexserver", "Component": "HAN-DB-ENG"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}