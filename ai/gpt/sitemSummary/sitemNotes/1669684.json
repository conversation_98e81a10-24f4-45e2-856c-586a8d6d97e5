{"Request": {"Number": "1669684", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 568, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017366912017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001669684?language=E&token=0A26AE72AC55DA639CBB00E6E06001DD"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001669684", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001669684/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1669684"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.10.2020"}, "SAPComponentKey": {"_label": "Component", "value": "BC-OP-SUN"}, "SAPComponentKeyText": {"_label": "Component", "value": "SUN Solaris"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Operating System Platforms", "value": "BC-OP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SUN Solaris", "value": "BC-OP-SUN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP-SUN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1669684 - <PERSON><PERSON> on Oracle Solaris 11"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>SAP supports Oracle Solaris 11 starting May 2012. Please look at the PAM (Product Availability Matrix) for further details of specific products.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Oracle Solaris 11, Solaris SPARC, Solaris X64, Solaris X86_64, Oracle Database, SAP MaxDB Database</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This note describes the prerequisites and necessary steps for already shipped SAP Products to run on Oracle Solaris 11 for SPARC and X64 platform</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This note does not replace the installation guideline for SAP software or the installation guideline for Oracle Solaris. This note deals only with points that affect the combination of operating system, hardware, database software and SAP software.<br /><br /><br />Please ensure that the following prerequisites are fulfilled</p>\r\n<ol>1. Operating System requirements</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Software prerequisites for SAP on Oracle Solaris 11 can be found in SAP note 1797712 \"Solaris software prerequisites for SAP on Oracle Solaris 11\"</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Use resource controls as described in SAP note 724713 \"Parameter settings for Oracle Solaris 10 and above\".</p>\r\n<ol>2. Java Virtual Machine</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The vendor JDK is only supported for installation and has to be replaced by SAP JVM for productive use.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;For further information please view the following SAP Notes</p>\r\n<ul>\r\n<li>Note 1555341 Downloading SAP JVM Switch Tool and SAPJVM 4</li>\r\n</ul>\r\n<ul>\r\n<li>Note 1495160 SAP JVM as replacement for Partner JDKs 1.4.2</li>\r\n</ul>\r\n<ul>\r\n<li>Note 1665953 SAP JVM Switch Tool Composite Note</li>\r\n</ul>\r\n<ol>3. SAP Components</ol><ol><ol>a) SAP Kernel</ol></ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Oracle Solaris 11 is only supported with SAP Kernel 720_EXT or higher. It is also supported with 640_EX2.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Please view the following SAP Notes on how to use the downward compatible Kernel(DCK) 720_EXT:</p>\r\n<ul>\r\n<ul>\r\n<li>Note 1636252 Installing a 7.20 kernel in SAP Web AS 7.00/7.01/7.10/7.11</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 1553301 7.20 EXT Kernel - Usage</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 1629598 SAP Kernel 720 will replace older kernel versions</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 1633731 Usage of the 7.20 Downward-Compatible Kernel</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Installation with ABAP Stack</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;For a system with ABAP stack the installation can be done with the originally provided Kernel DVD.</p>\r\n<ul>\r\n<ul>\r\n<li>Symptom: Shortdump: Transaction SICK failed</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Solution: This shortdump can be ignored during installation Replace the Kernel with an actual 720_EXT Kernel or 640_EX2 after installation.</p>\r\n<ul>\r\n<li>Java and Dual Stack installation process</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Products based on NW 7.0x x=0,1 and NW 7.1x x=0,1</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;create a local copy of Kernel CD and replace the original Kernel archives with an actual version from SAP Service Marketplace like described in<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Note 1696716 \"Creating a modified Solaris kernel medium\". Patch to 720_EXT after installation.</p>\r\n<ul>\r\n<ul>\r\n<li>Products based on NW 7.0, NW 7.20 and NW 7.30</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;An actual 720_EXT Kernel and the newest SAP JVM can be provided during the installation process.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Please do the following<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;- Download the newest version of Kernel 720_EXT and SAP JVM 6.1 from SMP.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;- Choose installation type \"Custom\" in SAPInst and provide Kernel(SAPEXE, SAPEXEDB, igs, igshelper) during installation<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;- use SAP JVM 6.1 with build 6.1.034 or higher (SPARC only)<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;- Do not provide the 720_EXT version of SAPDIAGNOSTICSAGENT Update the SMD agent after Installation as described in&#160;&#160;Note: 1671843 - Installing the SMD agent using 720_EXT kernel<br /><br /></p>\r\n<ol><ol>a) SAPInst</ol></ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The following problems can occurr during installation:</p>\r\n<ul>\r\n<li>General SAPInst Symptom: SAPInst stops with the following error:</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;An unexpected error has been detected by SAP Java Virtual Machine Internal Error (interpreter.cpp:358), pid=1513, tid=2 Error: guarantee(codelet_size &gt; 0 &lt;(&gt;&amp;&lt;)&gt;&amp; (size_t)codelet_size 2*K,\"not enough space for interpreter generation\") Manually execution of command java -version for SAP JVM 6 in installation directory writes a core file.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Solution: Patching SAPINST<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;In some cases SAPINST fails during start or terminates abnormally and needs to be patched. Please refer to and proceed according to note 929929 for information about where to download SAPINST patches and how to apply them.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;More information can be found in SAP Note 1642749&#160;&#160;SAP JVM 6.1 Patch Collection 29 (build 6.1.034)<br /><br /></p>\r\n<ul>\r\n<li>Java and Dual Stack Instance start problems:</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Startup Framework (jcontrol, jlaunch, jcmon, jspm)</p>\r\n<ul>\r\n<ul>\r\n<li>Symptom: Error during a Java or Double Stack installation in the step of the Java user check/creation. The actual provided Startup Framework does not support Solaris 11 and does not start.[Thr&#160;&#160;1] *** ERROR =&gt; OS release SunOS 5.11 11.0 sun4u is not supported with this startup framework (700) [jstartxx_mt. 4389]</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;For Rel. 6.40:<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; The description how to upgrade the J2EE Engine Startup Framework you can find in SAP note 710914.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;For Rel. &#160;&#160;700, 701, 710 and 711<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Make a local copy of the given Kernel Media and replace the provided Kernel with the most recent patch level of the Kernel<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;For Rel 702, 720, 730<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Choose \"Custom\" in window \"Parameter Settings\" for the Central instance. In window \"SAP System Archives\" provide 720_EXT versions for SAP Kernel, IGS, IGS Helper and newest SAP JVM 6.1.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; For the SMD instance provide only the newest SAP JVM 6.1<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;For Product 703, 731<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; No action required<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Problems which require a actual SAPInst from SMP:<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Startup Framework (SAPJVM, SAP JVM 6.1)<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; An unexpected error has been detected by SAP Java Virtual Machine: Internal Error (interpreter.cpp:358), pid=6569, tid=2 Error: guarantee(codelet_size &gt; 0 &lt;(&gt;&amp;&lt;)&gt;&amp; (size_t)codelet_size &gt; *K,\"not enough space for interpreter generation\")</p>\r\n<ol>4. SAPup (Upgrade) / SUM (Upgrade / EHP Installation)</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The SAP upgrade pathes on Oracle Solaris 11 will be limited to SAP target releases which were initially released with a SAP Kernel 720_REL/720_EXT or higher.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;For this upgrade or EHP Installation you have to create an Upgrade Stack and and a XML-File with SAP Solution Manager containing the Kernel 720_EXT.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;You can ignore the warning:</p>\r\n<ul>\r\n<li>WARNING: OS version 5.11 out of range (too high)</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;If this version is released for the downward compatible kernel you are using, please ignore this warning. If not, ask SAP for an individual release of your configuration. Do not continue before SAP supports your configuration.</p>\r\n<ol><ol>a) SAPup (Upgrade)</ol></ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Upgrade: note 1626284 has to be checked, if SUM 1.0 SP04 or upgrade master have to be used.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;If the upgrade Package contains a Kernel DVD 720 you have to set a link from directory &lt;upgradePath&gt;/exe to &lt;upgradePath&gt;exenew. The following steps have to be performed after upgrade module \"Extraction\":</p>\r\n<ul>\r\n<ul>\r\n<li>mv &lt;putdir&gt;/exe &lt;putdir&gt;/exe_do_not_use</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>ln -s &lt;putdir&gt;/exenew &lt;putdir&gt;/exe</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Symptom: STARTUP failed</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Solution: 1708424 Upgrade fails on startup with SAPJVM 6.1.00.6 error</p>\r\n<p>&#160;</p>\r\n<ol><ol>b) Software Update Manager SUM&#160;&#160;(Upgrade / EHP Installation)</ol></ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Only Update-tool SUM 1.0 SP4 (and higher) is supported.</p>\r\n<p>&#160;</p>\r\n<ol>5. Database</ol><ol><ol>a) Oracle Database</ol></ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Oracle Database is supported on Oracle Solaris 11 with version 11.2.0.3 and higher.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Oracle Instant Client version has to be 11.2.0.3 or higher.</p>\r\n<ul>\r\n<ul>\r\n<li>Please follow Note 1642058 on how to adapt SAPInst for Installation with Oracle Instant Client 11.2.0.3</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Raw Devices are not supported for Oracle Database 11.2.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>For 640 based installations have to be done with Oracle Instant Client 10.2 and need to be changed to Oracle Instant Client version 11.2.0.3 or higher after installation. More information on how to update the Oracle Instant Client can be found in Note 819829 and 1431794.</li>\r\n</ul>\r\n</ul>\r\n<ol><ol>b) SAP MaxDB database versions for Oracle Solaris 11</ol></ol>\r\n<ul>\r\n<li>SAP MaxDB supports Oracle Solaris 11 with Version 7.8 for Products based on</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP Basis 6.40 or lower</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP Basis 70x&#160;&#160;where x = 0, 1, 2</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP Basis 7.1x where x = 0, 1</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The minimum Version of SAP MaxDB 7.8 is 7.8.2.26</p>\r\n<ul>\r\n<li>Version 7.9.7.10 and higher for Products based on</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP Basis 7.31</li>\r\n</ul>\r\n</ul>\r\n<p><br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Almost all products based on 6.40, 7.0* or 7.1* were originally released with MaxDB 7.6 or 7.7. Therefore, you must perform the installation or system copy with the originally released MaxDB version.</p>\r\n<ul>\r\n<ul>\r\n<li>MaxDB 7.6 no minimum patchlevel required for installation</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>MaxDB 7.7.6 the minimum Version is 7.7.06.11. See Note 936973 \"Manually create a MaxDB or liveCache CD\".</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Perform a Database update to MaxDB 7.8 after installation or system copy on Oracle Solaris 11. For more information about the procedure, see one of the following SAP notes:</p>\r\n<ul>\r\n<ul>\r\n<li>1466917&#160;&#160;MaxDB 7.8: Installation/System Copy based on SAP Kernel 6.40</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>1469675&#160;&#160;MaxDB 7.8: Installation/System Copy based on SAP Kernel 7.0*</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>1478170&#160;&#160;MaxDB 7.8: Installation/System Copy based on SAP Kernel 7.1*</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<p>&#160;</p>\r\n<ul>\r\n<li>Symptom: An installation with SAP MaxDB 7.7.6 fails in Phase \"Install database server software\" with error: dbmcli: symbol _pthread_setspecific: referenced symbol not found</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Solution: Proceed as described in Note 936973</p>\r\n<p>&#160;</p>\r\n<ul>\r\n<li>Symptom: A SAP MaxDB installation with Raw Devices fails with the following error message: WARNING[E]&#160;&#160;[sysxcspecf.cpp:51] CSySpecialFileImpl::getSize() FSH-00008&#160;&#160;A system error occurred when executing function read_vtoc(). Returned index out of range.</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Solution: for NW 7.0x based Installation:</p>\r\n<ul>\r\n<ul>\r\n<li>Patch SAPINST according to Note 929929 \"Latest SAPinst Patch\"</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Solution for 6.x based Installation:</p>\r\n<ul>\r\n<ul>\r\n<li>Install on File-System or use Raw Devices with UFS filesystem</li>\r\n</ul>\r\n</ul>\r\n<ol>6. Limitations and additional requirements</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;For Solaris software prerequisites for SAP on Oracle Solaris 11 please check SAP note 1797712</p>\r\n<p>7. Limited Non-Unicode support for SAP Netweaver based products on Solaris X86_64</p>\r\n<blockquote dir=\"ltr\" style=\"margin-right: 0px;\">\r\n<p>Only Additional Application Servers are allowed on Solaris X86_64</p>\r\n</blockquote>\r\n<ul>\r\n<ul>\r\n<li>Prerequisites</li>\r\n<ul>\r\n<li>Only Non-Unicode&#160;Additional Application Servers are allowed on Solaris X86_64</li>\r\n<li>Only ABAP application servers can be aded to existing Non-Unicode systems</li>\r\n<li>Running ASCS and Database on Solaris X86_64 is&#160;not supported by SAP</li>\r\n<li>Check if your existing Non-Unicode system supported in SAP-PAM. Check SAP Knowledge Base&#160;Articles and SAP Notes for further information on Non-Unicode (eg. Note 1896539 and 1648480 and others)</li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<p>&#160;</p>\r\n<p>&#160;</p>\r\n<p>&#160;</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5074214)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON> (C5061499)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001669684/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001669684/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001669684/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001669684/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001669684/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001669684/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001669684/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001669684/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001669684/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1896539", "RefComponent": "BC-I18", "RefTitle": "Compatibility and end of support to non-Unicode systems (FAQ)", "RefUrl": "/notes/1896539"}, {"RefNumber": "822239", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB interfaces", "RefUrl": "/notes/822239"}, {"RefNumber": "819829", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Instant Client Installation and Configuration on Unix or Linux", "RefUrl": "/notes/819829"}, {"RefNumber": "724713", "RefComponent": "BC-OP-SUN", "RefTitle": "Parameter Settings for Oracle Solaris 10 and above", "RefUrl": "/notes/724713"}, {"RefNumber": "649814", "RefComponent": "BC-DB-SDB", "RefTitle": "Installation/update of SAP MaxDB/liveCache client software", "RefUrl": "/notes/649814"}, {"RefNumber": "498036", "RefComponent": "BC-DB-SDB", "RefTitle": "Overview note: Installing SAP MaxDB/liveCache versions", "RefUrl": "/notes/498036"}, {"RefNumber": "1848918", "RefComponent": "BC-OP-SUN", "RefTitle": "SAP System Copy on Oracle Solaris with Oracle Database", "RefUrl": "/notes/1848918"}, {"RefNumber": "1797712", "RefComponent": "BC-OP-SUN", "RefTitle": "Solaris software prerequisites for SAP on Oracle Solaris 11", "RefUrl": "/notes/1797712"}, {"RefNumber": "1780404", "RefComponent": "BC-SRV-FP", "RefTitle": "ADS problem on Solaris 11", "RefUrl": "/notes/1780404"}, {"RefNumber": "1707141", "RefComponent": "SV-SMG-INS-AGT", "RefTitle": "Diagnostics Agent - How to upgrade SAP Kernel", "RefUrl": "/notes/1707141"}, {"RefNumber": "1696716", "RefComponent": "BC-OP-SUN", "RefTitle": "Creating a modified Solaris kernel medium", "RefUrl": "/notes/1696716"}, {"RefNumber": "1693680", "RefComponent": "BC-OP-SUN", "RefTitle": "Running SAP Software on Oracle SuperCluster", "RefUrl": "/notes/1693680"}, {"RefNumber": "1671843", "RefComponent": "BC-INS-UNX", "RefTitle": "Installing the SMD agent using 720_EXT kernel", "RefUrl": "/notes/1671843"}, {"RefNumber": "1665953", "RefComponent": "BC-INS", "RefTitle": "SAP JVM Switch Tool Composite Note", "RefUrl": "/notes/1665953"}, {"RefNumber": "1648480", "RefComponent": "XX-SER-REL", "RefTitle": "Maintenance for SAP Business Suite 7 Software including SAP NetWeaver", "RefUrl": "/notes/1648480"}, {"RefNumber": "1643799", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1643799"}, {"RefNumber": "1642058", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "Adapt SAPInst Oracle Client 11.2.0.x", "RefUrl": "/notes/1642058"}, {"RefNumber": "1638356", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Additional information about BR*Tools Version 7.20 EXT", "RefUrl": "/notes/1638356"}, {"RefNumber": "1636252", "RefComponent": "BC-CST", "RefTitle": "Installing a 7.20 kernel in SAP Web AS 7.00/7.01/7.10/7.11", "RefUrl": "/notes/1636252"}, {"RefNumber": "1633731", "RefComponent": "BC-CST", "RefTitle": "Usage of the 7.20 Downward-Compatible Kernel", "RefUrl": "/notes/1633731"}, {"RefNumber": "1629598", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 720 will replace older kernel versions", "RefUrl": "/notes/1629598"}, {"RefNumber": "1617188", "RefComponent": "BC-DB-ORA", "RefTitle": "SAP Software and Oracle Exalogic", "RefUrl": "/notes/1617188"}, {"RefNumber": "1469675", "RefComponent": "BC-DB-SDB-INS", "RefTitle": "MaxDB 7.8: Inst./System Copy based on SAP Kernel 7.00/7.01", "RefUrl": "/notes/1469675"}, {"RefNumber": "1431800", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11.2.0: Central Technical Note", "RefUrl": "/notes/1431800"}, {"RefNumber": "1431793", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11.2.0: Upgrade Scripts", "RefUrl": "/notes/1431793"}, {"RefNumber": "1368413", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1368413"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2384855", "RefComponent": "BC-OP-SUN", "RefTitle": "Running SAP Software on Oracle MiniCluster", "RefUrl": "/notes/2384855 "}, {"RefNumber": "1704753", "RefComponent": "BC-INS-UNX", "RefTitle": "Inst.Systems Based on NetWeaver on UNIX  - Using Software Provisioning Manager 1.0", "RefUrl": "/notes/1704753 "}, {"RefNumber": "2052912", "RefComponent": "BC-OP-LNX-OLNX", "RefTitle": "SAP Software and Oracle Private Cloud Appliance (PCA)", "RefUrl": "/notes/2052912 "}, {"RefNumber": "2047465", "RefComponent": "BC-OP-SUN", "RefTitle": "SAP Software in an Oracle Solaris 11 Kernel Zone", "RefUrl": "/notes/2047465 "}, {"RefNumber": "1780404", "RefComponent": "BC-SRV-FP", "RefTitle": "ADS problem on Solaris 11", "RefUrl": "/notes/1780404 "}, {"RefNumber": "1431793", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11.2.0: Upgrade Scripts", "RefUrl": "/notes/1431793 "}, {"RefNumber": "1633731", "RefComponent": "BC-CST", "RefTitle": "Usage of the 7.20 Downward-Compatible Kernel", "RefUrl": "/notes/1633731 "}, {"RefNumber": "1636252", "RefComponent": "BC-CST", "RefTitle": "Installing a 7.20 kernel in SAP Web AS 7.00/7.01/7.10/7.11", "RefUrl": "/notes/1636252 "}, {"RefNumber": "649814", "RefComponent": "BC-DB-SDB", "RefTitle": "Installation/update of SAP MaxDB/liveCache client software", "RefUrl": "/notes/649814 "}, {"RefNumber": "1617188", "RefComponent": "BC-DB-ORA", "RefTitle": "SAP Software and Oracle Exalogic", "RefUrl": "/notes/1617188 "}, {"RefNumber": "1707141", "RefComponent": "SV-SMG-INS-AGT", "RefTitle": "Diagnostics Agent - How to upgrade SAP Kernel", "RefUrl": "/notes/1707141 "}, {"RefNumber": "822239", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB interfaces", "RefUrl": "/notes/822239 "}, {"RefNumber": "1693680", "RefComponent": "BC-OP-SUN", "RefTitle": "Running SAP Software on Oracle SuperCluster", "RefUrl": "/notes/1693680 "}, {"RefNumber": "724713", "RefComponent": "BC-OP-SUN", "RefTitle": "Parameter Settings for Oracle Solaris 10 and above", "RefUrl": "/notes/724713 "}, {"RefNumber": "1431800", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11.2.0: Central Technical Note", "RefUrl": "/notes/1431800 "}, {"RefNumber": "1848918", "RefComponent": "BC-OP-SUN", "RefTitle": "SAP System Copy on Oracle Solaris with Oracle Database", "RefUrl": "/notes/1848918 "}, {"RefNumber": "498036", "RefComponent": "BC-DB-SDB", "RefTitle": "Overview note: Installing SAP MaxDB/liveCache versions", "RefUrl": "/notes/498036 "}, {"RefNumber": "1797712", "RefComponent": "BC-OP-SUN", "RefTitle": "Solaris software prerequisites for SAP on Oracle Solaris 11", "RefUrl": "/notes/1797712 "}, {"RefNumber": "819829", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Instant Client Installation and Configuration on Unix or Linux", "RefUrl": "/notes/819829 "}, {"RefNumber": "1629598", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 720 will replace older kernel versions", "RefUrl": "/notes/1629598 "}, {"RefNumber": "1665953", "RefComponent": "BC-INS", "RefTitle": "SAP JVM Switch Tool Composite Note", "RefUrl": "/notes/1665953 "}, {"RefNumber": "1638356", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Additional information about BR*Tools Version 7.20 EXT", "RefUrl": "/notes/1638356 "}, {"RefNumber": "1642058", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "Adapt SAPInst Oracle Client 11.2.0.x", "RefUrl": "/notes/1642058 "}, {"RefNumber": "1696716", "RefComponent": "BC-OP-SUN", "RefTitle": "Creating a modified Solaris kernel medium", "RefUrl": "/notes/1696716 "}, {"RefNumber": "1671843", "RefComponent": "BC-INS-UNX", "RefTitle": "Installing the SMD agent using 720_EXT kernel", "RefUrl": "/notes/1671843 "}, {"RefNumber": "1469675", "RefComponent": "BC-DB-SDB-INS", "RefTitle": "MaxDB 7.8: Inst./System Copy based on SAP Kernel 7.00/7.01", "RefUrl": "/notes/1469675 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "620", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "740", "To": "740", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}