{"Request": {"Number": "206439", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 528, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014790852017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000206439?language=E&token=3BAEA5B3017910FE339950A589A0CD76"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000206439", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000206439/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "206439"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 19}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Performance"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "29.07.2008"}, "SAPComponentKey": {"_label": "Component", "value": "CRM-MW"}, "SAPComponentKeyText": {"_label": "Component", "value": "Middleware"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Customer Relationship Management", "value": "CRM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Middleware", "value": "CRM-MW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM-MW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "206439 - Reorganization of tables in CRM Middleware"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The tables for the business document (BDoc) message flow and the Middleware trace can increase considerably and therefore require a lot of disk space. This may also cause a decrease in performance during the processing of BDoc messages.<br />In Release 2.0B and 2.0C, this applies to the following tables:</p> <UL><LI>The log tables for the message log SMO8_LOG, SMO8_LOG2.</LI></UL> <UL><LI>The tables for message administration and the flow trace tables SMO8FTCFG, SMO8FTSTP, SMO8_TMSG, SMO8_TMDAT, SMO8_DLIST.<br /></LI></UL> <p>As of Release 3.0, this applies to the following tables:</p> <UL><LI>The tables for managing the BDoc messages SMW3_BDOC, SMW3_BDOC1, SMW3_BDOC2, SMW3_BDOC4, SMW3_BDOC5, SMW3_BDOC6, SMW3_BDOC7, SMW3_BDOCQ</LI></UL> <UL><LI>The tables for the Middleware trace SMWT_TRC.</LI></UL><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>CRM Middleware performance, Middleware log, FlowControl-Trace<br />SMO6_REORG2</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><OL>1. A log level or trace level that is too high is activated.<br />Up to Release 3.00: Flow trace is activated/log level 'T' has been chosen.<br />Release 3.0 and higher: The trace level of the Middleware trace is greater than '1'.</OL> <OL>2. The reorganization program SMO6_REORG is not scheduled to be executed regularly, or the corresponding background job MW_REORG does not run.</OL> <p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. Reduce the log level or the trace level.<br />Up to Release 3.00: Set default log level 'E' for flow trace. For more details, see Note 206366.<br />Release 3.0, 3.1, 3.5: Set default trace level '1' for all trace environments of the Middleware trace except for 'G' (generation). To do this, choose Middleware -&gt; Administration -&gt; Define Middleware Parameters (R3AC6) in the SAP Menu and in the table displayed (SMOFPARSFA), check the entries with the following structure (SMOFPARSFA):<br /><br />Column&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Value<br />----------------------------------<br />Key&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRMGENERAL<br />ParamName&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;TRACE-LEVEL<br />ParamName2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ENV=m<br />Param.value&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;LEVEL=n<br /><br />Here, m is the trace environment and n is the trace level, which may be a number between 1 and 4. If n is greater than 1 for certain entries (except for the entry in which column 'ParamName2' contains string 'ENV=G' (trace environment 'Generation'), set 1 as default value and save your changes.<br />The default value of the trace level for the trace environment generation (the entry where the 'ParamName2' column contains the string 'ENV=G') is 4.<br /><br />Release 4.0 and higher: Set default trace level '1' for all trace environments in the Middleware trace except for 'G' (generation). To do this, from the SAP user menu, choose Architecture and Technology -&gt; Middleware -&gt; Monitoring -&gt; Message Flow -&gt; Set up Middleware Trace and the choose Set default settings.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Trace Level:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;Error<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1&#x00A0;&#x00A0;Warning<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2&#x00A0;&#x00A0; Flow Services<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;3&#x00A0;&#x00A0; Detail Level 1<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4&#x00A0;&#x00A0; Detail Level 2<br /><br /></OL> <OL>2. Use transaction SM37 to check if the reorganization program SMO6_REORG or the reorganization program SMO6_REORG2 (as of CRM 4.0, Support Package 06 and in CRM 5.0) is scheduled for to be executed regularly and if the corresponding background jobs MW_REORG have been executed without errors. If no corresponding background job is scheduled, schedule the program SMO6_REORG or the program SMO6_REORG2 (see Note 713173) to be executed as a periodic job. <B>The reorganization program must run in each active client, therefore you must also schedule the background job in all active clients. </B><br /></OL> <p>Use transaction SM36 to do this, and define the following job:<br /><br />Job name:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MW_REORG<br />Job class:&#x00A0;&#x00A0;&#x00A0;&#x00A0; B<br /><br />ABAP program: SMO6_REORG (or SMO6_REORG2 (see Note 713173)) Variant:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAP&lt;(&gt;&amp;&lt;)&gt;_MW_REORG<br />Start date:&#x00A0;&#x00A0;Specify date and time<br />Select \"Execute job periodically\"<br />Period value:&#x00A0;&#x00A0;Daily<br /><br />Then save the job.<br /><br />Trace and log data that is more than one week old is deleted by the selected SAP&amp;_MW_REORG variant. If you want to keep the data for more or less time, create a new variant for the report SMO6_REORG or the report SMO6_REORG2 using transaction SE38 and use the SAP&amp;_MW_REORG variant instead when you define the job.<br /><br />If the variant SAP&amp;_MW_REORG does not exist (yet), see<br />Note 600007.<br /><br />In Release 3.00, see also related Notes 487915 and 503002.<br /></p> <b>Additional information:</b><br /> <OL>1. In Releases 2.0B and 2.0C, if the program SMO8_START_REORG_LOG is scheduled for to be executed regularly in the background job 'ReorgMiddlewareLog', you can cancel the scheduling of this job because the reorganization program SMO6_REORG adopts the tasks of the program SMO8_START_REORG_LOG.</OL> <OL>2. The program SMO6_REORG or the program SMO6_REORG2 (see Note 713173) is also responsible for reorganizing specific CRM Middleware statistical data. These are the message flow statistics, which can be displayed in transaction SMWMFLOW, and the statistics for the data exchange between the mobile clients and the CRM server, which can be displayed in the Communication Monitor (transaction SMWMCOMM as of Release 2.0C).<br />This data is subject to the same reorganization schema as all standard data from the performance database, which can be displayed using transaction ST03N. To change the retention period of this data, call transaction ST03N. Then open the 'Collector' folder and the 'Performance database' subfolder. Double-click \"Reorganization\" to display all retention periods. You can also change them. The relevant periods for the CRM Middleware statistics data are displayed in the 'For the standard data' area.</OL> <OL>3. Explanation of the trace level:<br /><br />Trace level 0 = Error:<br /> only hard errors should be reported e.g. -Table space full-<br />Trace level 1 = Warning:<br /> only circumstances which can produce an error e.g. - high watermark<br /> reached, operator intervention is required! -<br />Trace level 2 = Control-Flow:<br /> automatically written / Flow Services<br />Trace level 3 = Detail Level 1:<br /> e.g. program environment, form or module additional information,<br /> no strong warnings<br />Trace level 4 = Detail Level 2:<br /> e.g. program specific information variables, value</OL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "SE38"}, {"Key": "Transaction codes", "Value": "SM37"}, {"Key": "Transaction codes", "Value": "SM36"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I820393)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D035948)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000206439/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000206439/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000206439/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000206439/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000206439/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000206439/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000206439/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000206439/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000206439/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478"}, {"RefNumber": "675725", "RefComponent": "SRM-EBP", "RefTitle": "Unnecessary BDocs in SRM (EBP) from queue CSA_MASS_BUPA", "RefUrl": "/notes/675725"}, {"RefNumber": "638551", "RefComponent": "CRM-MW-SRV", "RefTitle": "Deactivating creation of BDoc messages", "RefUrl": "/notes/638551"}, {"RefNumber": "623195", "RefComponent": "CRM-MW-SRV", "RefTitle": "Flow-specific entries in the SMOFPARSFA table", "RefUrl": "/notes/623195"}, {"RefNumber": "600007", "RefComponent": "CRM-MW", "RefTitle": "Standard variant for report SMO6_REORG does not exist", "RefUrl": "/notes/600007"}, {"RefNumber": "536414", "RefComponent": "CRM-MKT", "RefTitle": "CRM 3.0/3.1 SAP Composite Note: Middleware reorganization", "RefUrl": "/notes/536414"}, {"RefNumber": "503002", "RefComponent": "CRM-MW-SRV", "RefTitle": "Performance problems during BDoc processing", "RefUrl": "/notes/503002"}, {"RefNumber": "494901", "RefComponent": "CRM", "RefTitle": "SAPKU30008: Support Package 8 for EBP 3.0/CRM 3.0", "RefUrl": "/notes/494901"}, {"RefNumber": "487915", "RefComponent": "CRM-MW-SRV", "RefTitle": "BDoc message flow tables are not reorganized", "RefUrl": "/notes/487915"}, {"RefNumber": "328791", "RefComponent": "CRM-MW-SRV", "RefTitle": "Quick deletion of trace files from the CRM server", "RefUrl": "/notes/328791"}, {"RefNumber": "206366", "RefComponent": "CRM-MW-SRV", "RefTitle": "Performance problems or high I/O load due to traces", "RefUrl": "/notes/206366"}, {"RefNumber": "1751383", "RefComponent": "SRM-EBP-TEC-MW", "RefTitle": "FAQ: Middleware Reorganization in SRM", "RefUrl": "/notes/1751383"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2352193", "RefComponent": "CRM-MW", "RefTitle": "-INTERNAL USE ONLY - SMO6_REORG2 program is running for a long time", "RefUrl": "/notes/2352193 "}, {"RefNumber": "2174120", "RefComponent": "CRM-MW-MON", "RefTitle": "== INTERNAL KBA ==  MW Trace is inactive", "RefUrl": "/notes/2174120 "}, {"RefNumber": "2571177", "RefComponent": "CRM-MW", "RefTitle": "====INTERNAL KBA ======= SMO6_REORG2   Performance", "RefUrl": "/notes/2571177 "}, {"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478 "}, {"RefNumber": "1751383", "RefComponent": "SRM-EBP-TEC-MW", "RefTitle": "FAQ: Middleware Reorganization in SRM", "RefUrl": "/notes/1751383 "}, {"RefNumber": "675725", "RefComponent": "SRM-EBP", "RefTitle": "Unnecessary BDocs in SRM (EBP) from queue CSA_MASS_BUPA", "RefUrl": "/notes/675725 "}, {"RefNumber": "600007", "RefComponent": "CRM-MW", "RefTitle": "Standard variant for report SMO6_REORG does not exist", "RefUrl": "/notes/600007 "}, {"RefNumber": "328791", "RefComponent": "CRM-MW-SRV", "RefTitle": "Quick deletion of trace files from the CRM server", "RefUrl": "/notes/328791 "}, {"RefNumber": "206366", "RefComponent": "CRM-MW-SRV", "RefTitle": "Performance problems or high I/O load due to traces", "RefUrl": "/notes/206366 "}, {"RefNumber": "503002", "RefComponent": "CRM-MW-SRV", "RefTitle": "Performance problems during BDoc processing", "RefUrl": "/notes/503002 "}, {"RefNumber": "487915", "RefComponent": "CRM-MW-SRV", "RefTitle": "BDoc message flow tables are not reorganized", "RefUrl": "/notes/487915 "}, {"RefNumber": "536414", "RefComponent": "CRM-MKT", "RefTitle": "CRM 3.0/3.1 SAP Composite Note: Middleware reorganization", "RefUrl": "/notes/536414 "}, {"RefNumber": "623195", "RefComponent": "CRM-MW-SRV", "RefTitle": "Flow-specific entries in the SMOFPARSFA table", "RefUrl": "/notes/623195 "}, {"RefNumber": "638551", "RefComponent": "CRM-MW-SRV", "RefTitle": "Deactivating creation of BDoc messages", "RefUrl": "/notes/638551 "}, {"RefNumber": "494901", "RefComponent": "CRM", "RefTitle": "SAPKU30008: Support Package 8 for EBP 3.0/CRM 3.0", "RefUrl": "/notes/494901 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "46C", "To": "46D", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "20B", "To": "20C", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "310", "To": "310", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "350", "To": "350", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "400", "To": "400", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "520", "To": "520", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}