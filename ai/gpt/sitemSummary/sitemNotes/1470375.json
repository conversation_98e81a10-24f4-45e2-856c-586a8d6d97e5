{"Request": {"Number": "1470375", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 271, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001470375?language=E&token=00F16F1AB6298A1487C31C01B0FC8909"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001470375", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001470375/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1470375"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "In Process"}, "ReleasedOn": {"_label": "Released On", "value": "00.00.0000"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SER-EWA"}, "SAPComponentKeyText": {"_label": "Component", "value": "EarlyWatch Alert"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Support Services", "value": "SV-SMG-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "EarlyWatch Alert", "value": "SV-SMG-SER-EWA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SER-EWA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1470375 - Service Definitions -- When to Refresh them ?"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>DO NOT USE THIS NOTE FOR CUSTOMER MESSAGES.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>DO NOT USE THIS NOTE FOR CUSTOMER MESSAGES.<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>DO NOT USE THIS NOTE FOR CUSTOMER MESSAGES.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>What are Service Definitions?<br />-----------------------------<br />As the name Suggests Service Definitions define the various services that can be executed on a SAP System using SDCCN.<br />Service definitions consist of table entries (BDL*). These table entries control which function modules are executed during data collection for a service session (for example SAP Early<br />Watch Alert, SAP Going Live Check).<br /><br />When and why are Service Definitions published?<br />------------------------------------------------<br /><br />New service definitions are published with:<br /><br />- a new version (add-on or Support Package) of add-on ST-SER,<br />- each new version (add-on or Support Package) of add-on ST-PI,<br />- or on demand due to wrong data.<br /><br />How are Service Definitions refreshed ?<br />----------------------------------------<br />There are 2 ways in which Service Definitions are refreshed.<br /><br />Automatic via SDCCN task \"Maintenance Package\":<br />-----------------------------------------------<br />There is a task scheduled in SDCCN on a periodic basis namely \"Maintenance Package\" which will refresh Service Definitions Automatically. The period for checking and refreshing the Service Definitions depends on the maintenance Settings maintained typically every 7 days.<br /><br />Manually via SDCCN:<br />------------------<br />Service definitions can be updated by customers via a \"Service Definitions Refresh\" in transaction SDCCN from OSS or by importing a transport which is downloaded from the SAPSERVx as documented in the note 727998. Following steps are broadly followed. Please refer to 727998 for detailed process and follow the steps in the note 727998 carefully:<br /><br />Step 0: Ensure that optimizer statistics are current for table D010INC<br /><br />Step 1: Delete all entries in the service definition tables.<br /><br />Step 2: Replace the entries, either by transport or from OSS, depending &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on your system configuration.<br /><br />Step 3: Regenerate the re-import includes.<br /><br />Before you begin ensure that the following connections work:<br /><br />- Connection to your SAP Solution Manager (where EWA is generated by &#x00A0;&#x00A0;&#x00A0;&#x00A0; Solution Manager)<br /><br />- SDCC_OSS<br /><br />When is a Service Definition useful and helps resolve a issue ?<br />----------------------------------------------------------------<br />Following are the cases when a Service Definition Refresh is recommended:<br /><br />- When there are errors seen in the data collection task for a EWA on &#x00A0;&#x00A0;&#x00A0;&#x00A0;the Satellite system.<br /><br />- When the satellite system does not replicate the EWA sessions &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;generated in the Solution created in Solution Manager.<br /><br />- When there is a Syntax_Error dump while processing the job SM:EXEC &#x00A0;&#x00A0;&#x00A0;&#x00A0; SERVICES.<br /><br />Outside the above 3 prime reasons, Service Definition refresh can be recommended by SAP in certain Special cases where SAP Development might request the customer refresh the definitions. But this has to be in consultation with SAP Support.<br /><br />What to check before you actually think of Refreshing Service Definitions ?<br />-----------------------------------------------------------------<br /><br />Always check the following points before investing your efforts in a Service Definition Refresh:<br /><br />- Check whether your issue falls under the reasons given above.<br />- Always check in SDCCN ( Done tab ) when was the last Service &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Definition Refresh performed in the system ( either manually or&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; automatically )<br />- Check for the completeness of the last refresh. It might be the case &#x00A0;&#x00A0; that the refresh aborted or has not completed the last time due to a &#x00A0;&#x00A0; error e.g: RFC error<br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "I047548"}, {"Key": "Processor                                                                                           ", "Value": "I047548"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001470375/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001470375/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001470375/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001470375/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001470375/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001470375/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001470375/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001470375/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001470375/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Dump_SDR_MB1.txt", "FileSize": "257", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000255022010&iv_version=0001&iv_guid=F4943E6329D0CD4BAB65F175D5D42574"}, {"FileName": "st22.txt", "FileSize": "229", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000255022010&iv_version=0001&iv_guid=A993D18D7AAB9E429347D695594FF49E"}, {"FileName": "Dump_SDR_MB2.txt", "FileSize": "274", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000255022010&iv_version=0001&iv_guid=EE5643354CEC454AA273E1EB35B0F1FF"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "763561", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCCN)  - FAQ", "RefUrl": "/notes/763561"}, {"RefNumber": "712511", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/712511"}, {"RefNumber": "216952", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCC) - FAQ", "RefUrl": "/notes/216952"}, {"RefNumber": "1601253", "RefComponent": "SV-SMG-SER-ESR", "RefTitle": "Error: GEN_INCLUDE_NOT_EXEC - /1CAGTF/IF_LOGFUNC_000500", "RefUrl": "/notes/1601253"}, {"RefNumber": "1600316", "RefComponent": "SV-SMG-SER-ESR", "RefTitle": "Error: GEN_INCLUDE_NOT_EXEC - /1CAGTF/IF_LOGFUNC_000500", "RefUrl": "/notes/1600316"}, {"RefNumber": "1586339", "RefComponent": "SV-SMG-SDD", "RefTitle": "Enhancement selfcheck Service definition refresh", "RefUrl": "/notes/1586339"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "763561", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCCN)  - FAQ", "RefUrl": "/notes/763561 "}, {"RefNumber": "1601253", "RefComponent": "SV-SMG-SER-ESR", "RefTitle": "Error: GEN_INCLUDE_NOT_EXEC - /1CAGTF/IF_LOGFUNC_000500", "RefUrl": "/notes/1601253 "}, {"RefNumber": "1586339", "RefComponent": "SV-SMG-SDD", "RefTitle": "Enhancement selfcheck Service definition refresh", "RefUrl": "/notes/1586339 "}, {"RefNumber": "1600316", "RefComponent": "SV-SMG-SER-ESR", "RefTitle": "Error: GEN_INCLUDE_NOT_EXEC - /1CAGTF/IF_LOGFUNC_000500", "RefUrl": "/notes/1600316 "}, {"RefNumber": "216952", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCC) - FAQ", "RefUrl": "/notes/216952 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}