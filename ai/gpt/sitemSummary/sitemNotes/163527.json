{"Request": {"Number": "163527", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 465, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000800622017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000163527?language=E&token=4EB8C037F495164D7ACC7922E797356A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000163527", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000163527/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "163527"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 16}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.02.2003"}, "SAPComponentKey": {"_label": "Component", "value": "FI-AP-AP-Q1"}, "SAPComponentKeyText": {"_label": "Component", "value": "Withholding Tax (Reporting)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Accounts Payable", "value": "FI-AP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "FI-AP-AP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AP-AP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Withholding Tax (Reporting)", "value": "FI-AP-AP-Q1", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AP-AP-Q1*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "163527 - Withholding Tax for USA 1099-MISC (Version 2001)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The report RFKQSU30&#x00A0;&#x00A0;does not cover the new legal requirements for 1099-MISC reporting in 2001 (legal requirement in USA)</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RFKQSU30, RFKQSU00, RFKQSU20, USA withholding tax 1099-MISC, RFW1099M,<br />extended withholding tax , simple withholding tax, 1099</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>New legal requirement for 1099-MISC reporting in 2001 which has not been covered by report RFKQSU30</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The new report RFW1099M fulfills the new legal requirements for the 1099-MISC reporting in 2001. From release 4.0B onwards this report offers extended withholding tax functionality.<br />The SAPscript form F_RFW1099M_2001 shows the data for the IRS form 1099-MISC.<br />To get the newest version of report RFW1099M, implement the latest Supportpackages containing a corrected version of report RFW1099M (see below)<br /><br />A) New report RFW1099M and SAPScript F_RFW1099M_2001 (incl.<br />&#x00A0;&#x00A0; latest corrections)&#x00A0;&#x00A0;available on SAPSERV<br /><br />Release 3.1I<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; directory: /general/R3server/abap/note.0459520<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- Transport:&#x00A0;&#x00A0;P3IK054409<br /><br />Release 4.*<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; directory: /general/R3server/abap/note.0459520<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- Transport:&#x00A0;&#x00A0;U4BK901576<br /><br />Information concerning preliminary transports is given in the attached<br />note 13719.<br /></p> <b>Release 4.5B and 4.6B:</b><br /> <p>The changes from this note are included in the following support packages:</p> <UL><UL><LI>4.5B: SAPKH45B55</LI></UL></UL> <UL><UL><LI>4.6B: SAPKH46B43</LI></UL></UL> <p><br /><br />Remark<br />Please make sure that the support packages do not nullify the latest<br />SAPserv transports.<br />When importing transport&#x00A0;&#x00A0;U4BK901576 in Release 4.5B or release 4.6B or release 4.6C some warnings can occur.<br />Examples:<br />&#x00A0;&#x00A0;different nametabs for table E070A (field ATTRIBUTE).<br />&#x00A0;&#x00A0; different nametabs for table SMODISRC (field SUB_NAME).<br /><br />These warnings can be ignored.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D028542)"}, {"Key": "Processor                                                                                           ", "Value": "D026336"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000163527/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000163527/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000163527/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000163527/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000163527/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000163527/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000163527/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000163527/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000163527/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "60928", "RefComponent": "BC-CTS", "RefTitle": "Transports between Release 3.0 or 3.1 and Release 4.0", "RefUrl": "/notes/60928"}, {"RefNumber": "510859", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Missing or wrong messages of message class FR", "RefUrl": "/notes/510859"}, {"RefNumber": "459520", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "1099-MISC report RFW1099M for USA", "RefUrl": "/notes/459520"}, {"RefNumber": "457227", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "RFW1099M:correction T record & for discount amounts", "RefUrl": "/notes/457227"}, {"RefNumber": "454732", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "RFW1099M (1099-MISC): SAPSERV import Syntax error", "RefUrl": "/notes/454732"}, {"RefNumber": "453128", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "1099-MISC: Errors in report RFW1099M", "RefUrl": "/notes/453128"}, {"RefNumber": "431201", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "RFKQSU20:withholding tax codes 13,14 and 16", "RefUrl": "/notes/431201"}, {"RefNumber": "363650", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Withholding Tax Configuration for 1099MISC", "RefUrl": "/notes/363650"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "126776", "RefComponent": "BC-CTS", "RefTitle": "Transports between Releases 4.5 and 4.6", "RefUrl": "/notes/126776"}, {"RefNumber": "120151", "RefComponent": "BC-CTS", "RefTitle": "Transports between Release 4.0 and 4.5", "RefUrl": "/notes/120151"}, {"RefNumber": "106696", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/106696"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "363650", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Withholding Tax Configuration for 1099MISC", "RefUrl": "/notes/363650 "}, {"RefNumber": "126776", "RefComponent": "BC-CTS", "RefTitle": "Transports between Releases 4.5 and 4.6", "RefUrl": "/notes/126776 "}, {"RefNumber": "60928", "RefComponent": "BC-CTS", "RefTitle": "Transports between Release 3.0 or 3.1 and Release 4.0", "RefUrl": "/notes/60928 "}, {"RefNumber": "120151", "RefComponent": "BC-CTS", "RefTitle": "Transports between Release 4.0 and 4.5", "RefUrl": "/notes/120151 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "459520", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "1099-MISC report RFW1099M for USA", "RefUrl": "/notes/459520 "}, {"RefNumber": "457227", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "RFW1099M:correction T record & for discount amounts", "RefUrl": "/notes/457227 "}, {"RefNumber": "510859", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Missing or wrong messages of message class FR", "RefUrl": "/notes/510859 "}, {"RefNumber": "431201", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "RFKQSU20:withholding tax codes 13,14 and 16", "RefUrl": "/notes/431201 "}, {"RefNumber": "453128", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "1099-MISC: Errors in report RFW1099M", "RefUrl": "/notes/453128 "}, {"RefNumber": "454732", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "RFW1099M (1099-MISC): SAPSERV import Syntax error", "RefUrl": "/notes/454732 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "31H", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 31I", "SupportPackage": "SAPKH31I93", "URL": "/supportpackage/SAPKH31I93"}, {"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B70", "URL": "/supportpackage/SAPKH40B70"}, {"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B68", "URL": "/supportpackage/SAPKH40B68"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C26", "URL": "/supportpackage/SAPKH46C26"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C34", "URL": "/supportpackage/SAPKH46C34"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 1, "URL": "/corrins/0000163527/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}