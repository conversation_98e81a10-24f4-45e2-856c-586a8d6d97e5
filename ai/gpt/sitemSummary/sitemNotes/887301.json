{"Request": {"Number": "887301", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 579, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005077242017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=68625ECC6398D5F1BBB8596D5B3AFE3C"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "887301"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.10.2005"}, "SAPComponentKey": {"_label": "Component", "value": "FS-BP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Partner"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Services", "value": "FS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Partner", "value": "FS-BP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS-BP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "887301 - BP_TR2: Transport order number is not displayed"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You are executing the business partner conversion of phase II. On the selection screen of the program RFTBUH03, the transport order number is not displayed in the order/task field.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Business partner, conversion, FTBU, RFTBUH03, RFTBUH04, RFTBUH05, RFTBUH06</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The program RFTBUH02_1 determines at least one customer-specific DDIC object (data element) in the system, which is relevant for a conversion. Then the program RFTBUH05 requires the entry of a transport order number in the order/task field. After you call the follow-up program RFTBUH03, the transport order number only appears after you reset the \"Test mode\" indicator and press the &lt;ENTER&gt; key.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Copy the correction specified below using the Note Assistant, or import the relevant Support Package.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "C5029681"}, {"Key": "Processor                                                                                           ", "Value": "D027661"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "398888", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Composite note and FAQs about bus partner conversion", "RefUrl": "/notes/398888"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "398888", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Composite note and FAQs about bus partner conversion", "RefUrl": "/notes/398888 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-FINSERV", "From": "110", "To": "110", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "200", "To": "200", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "BANK/CFM", "From": "463_20", "To": "463_20", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-FINSERV 110", "SupportPackage": "SAPKGPFA22", "URL": "/supportpackage/SAPKGPFA22"}, {"SoftwareComponentVersion": "EA-FINSERV 200", "SupportPackage": "SAPKGPFB11", "URL": "/supportpackage/SAPKGPFB11"}, {"SoftwareComponentVersion": "BANK/CFM 463_20", "SupportPackage": "SAPKIPBJ28", "URL": "/supportpackage/SAPKIPBJ28"}, {"SoftwareComponentVersion": "EA-FINSERV 500", "SupportPackage": "SAPKGPFC11", "URL": "/supportpackage/SAPKGPFC11"}, {"SoftwareComponentVersion": "EA-FINSERV 600", "SupportPackage": "SAPKGPFD02", "URL": "/supportpackage/SAPKGPFD02"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "EA-FINSERV", "NumberOfCorrin": 4, "URL": "/corrins/**********/201"}, {"SoftwareComponent": "BANK/CFM", "NumberOfCorrin": 1, "URL": "/corrins/**********/59"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 5, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 4, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "BANK/CFM", "ValidFrom": "463_20", "ValidTo": "463_20", "Number": "736512 ", "URL": "/notes/736512 ", "Title": "BP_TR2: Update of the business partner conversion 2004", "Component": "FS-BP"}, {"SoftwareComponent": "BANK/CFM", "ValidFrom": "463_20", "ValidTo": "463_20", "Number": "785156 ", "URL": "/notes/785156 ", "Title": "BP_TR2: Update of the business partner conversion 2004", "Component": "FS-BP"}, {"SoftwareComponent": "BANK/CFM", "ValidFrom": "463_20", "ValidTo": "463_20", "Number": "832236 ", "URL": "/notes/832236 ", "Title": "BP_TR2: Runtime error DYNPRO_MSG_IN_HELP", "Component": "FS-BP"}, {"SoftwareComponent": "BANK/CFM", "ValidFrom": "463_20", "ValidTo": "463_20", "Number": "834661 ", "URL": "/notes/834661 ", "Title": "BP_TR2: Order/task field not ready for input", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "110", "ValidTo": "110", "Number": "736512 ", "URL": "/notes/736512 ", "Title": "BP_TR2: Update of the business partner conversion 2004", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "110", "ValidTo": "110", "Number": "785156 ", "URL": "/notes/785156 ", "Title": "BP_TR2: Update of the business partner conversion 2004", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "110", "ValidTo": "110", "Number": "832236 ", "URL": "/notes/832236 ", "Title": "BP_TR2: Runtime error DYNPRO_MSG_IN_HELP", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "110", "ValidTo": "110", "Number": "834661 ", "URL": "/notes/834661 ", "Title": "BP_TR2: Order/task field not ready for input", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "200", "ValidTo": "200", "Number": "736512 ", "URL": "/notes/736512 ", "Title": "BP_TR2: Update of the business partner conversion 2004", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "200", "ValidTo": "200", "Number": "785156 ", "URL": "/notes/785156 ", "Title": "BP_TR2: Update of the business partner conversion 2004", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "200", "ValidTo": "200", "Number": "832236 ", "URL": "/notes/832236 ", "Title": "BP_TR2: Runtime error DYNPRO_MSG_IN_HELP", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "200", "ValidTo": "200", "Number": "834661 ", "URL": "/notes/834661 ", "Title": "BP_TR2: Order/task field not ready for input", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "500", "ValidTo": "500", "Number": "736512 ", "URL": "/notes/736512 ", "Title": "BP_TR2: Update of the business partner conversion 2004", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "500", "ValidTo": "500", "Number": "785156 ", "URL": "/notes/785156 ", "Title": "BP_TR2: Update of the business partner conversion 2004", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "500", "ValidTo": "500", "Number": "832236 ", "URL": "/notes/832236 ", "Title": "BP_TR2: Runtime error DYNPRO_MSG_IN_HELP", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "500", "ValidTo": "500", "Number": "834661 ", "URL": "/notes/834661 ", "Title": "BP_TR2: Order/task field not ready for input", "Component": "FS-BP"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}