{"Request": {"Number": "3326805", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 372, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001199992023"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003326805?language=E&token=FDA4EA0B3E688FF745E23ECC8A78BEA3"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003326805", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3326805"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.10.2023"}, "SAPComponentKey": {"_label": "Component", "value": "FIN-CS"}, "SAPComponentKeyText": {"_label": "Component", "value": "S/4HANA for Group Reporting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financials", "value": "FIN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "S/4HANA for Group Reporting", "value": "FIN-CS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-CS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "3326805 - S4TWL - Configuration in Group Reporting using Selections and FS item Role attribute"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are preparing a system upgrade to SAP S/4HANA 2023 (S4CORE 108).&#160;The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Sets, Selections, FS item Role attribute, Global System Settings, CXB3</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>In the Global System Settings (transaction code CXB3), one of the following options for the Settings for Configuration Control are not marked:</p>\r\n<ul class=\"ul\" id=\"loio64ebca416a16485484474caa4f2e4de1__ul_iwn_bkc_xtb\">\r\n<li class=\"li\">Reclassification: Selection Object in Trigger</li>\r\n<li class=\"li\">Validation in SAP S/4HANA</li>\r\n<li class=\"li\">Breakdown Category: Selection Object in Maximum Selection</li>\r\n<li class=\"li\">Currency Translation: Selection Object</li>\r\n<li class=\"li\">Use Item Role Attribute</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p id=\"\"><strong>Business Value</strong></p>\r\n<p>The configuration of some functions in Group Reporting (breakdown categories, currency translation methods, reclassification methods, automatic postings on Selected FS items...) can be flexibly defined using Selections&#160; (<a target=\"_blank\" href=\"https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/4ebf1502064b406c964b0911adfb3f01/b3d1255de2244af486cb116919c1c5f1.html?locale=en-US\">https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/4ebf1502064b406c964b0911adfb3f01/b3d1255de2244af486cb116919c1c5f1.html?locale=en-US</a>) and using FS item Role attribute (<a target=\"_blank\" href=\"https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/4ebf1502064b406c964b0911adfb3f01/b3d1255de2244af486cb116919c1c5f1.html?locale=en-US\">https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/4ebf1502064b406c964b0911adfb3f01/bb5a08412bf145e9a950bee1bf4c6c6b.html?locale=en-US</a>).</p>\r\n<p>The options to use these feature are defined in the Global System Settings (transaction code CXB3). They are by default selected.&#160;Only in some circumstances (migration from EC-CS, initial release SAP S/4HANA 1809) some of these options are possibly not selected.</p>\r\n<p><strong>Description</strong></p>\r\n<p>As of SAP S/4HANA 2023 (S4CORE 108), these options in the Global System Settings are always selected and cannot be edited.</p>\r\n<p><strong>Business Process related Information</strong></p>\r\n<p>If some of these options are not selected in your system, you must adjust your configuration before the upgrade to use the Selections and the FS item Role.</p>\r\n<p><strong>Required and Recommended Actions(s)</strong></p>\r\n<p>Check in the Global System Settings (transaction code CXB3) which of the following options are not selected:</p>\r\n<ul class=\"ul\" id=\"loio64ebca416a16485484474caa4f2e4de1__ul_iwn_bkc_xtb\">\r\n<li class=\"li\">Reclassification: Selection Object in Trigger</li>\r\n<li class=\"li\">Validation in SAP S/4HANA</li>\r\n<li class=\"li\">Breakdown Category: Selection Object in Maximum Selection</li>\r\n<li class=\"li\">Currency Translation: Selection Object</li>\r\n<li class=\"li\">Use Item Role Attribute</li>\r\n</ul>\r\n<p><span style=\"font-size: 14px;\">If you don't use the options Reclassification: Selection Object in Trigger, Breakdown Category: Selection Object in Maximum Selection, or Currency Translation: Selection Object</span></p>\r\n<p>Follow this procedure to replace sets with selections in the configuration:</p>\r\n<ul>\r\n<li>In the configuration activities Define Reclassification Methods, Define Breakdown Categories, and Define Currency Translation Methods, list all the sets you use and their content.</li>\r\n<li>In the Check Global System Settings configuration activity, select the options Reclassification: Selection Object in Trigger, Breakdown Category: Selection Object in Maximum Selection, and Currency Translation: Selection Object.</li>\r\n<li>In the Define Selections app, create a selection to replace the sets.</li>\r\n<li>In the configuration activities Define Reclassification Methods, Define Breakdown Categories, and Define Currency Translation Methods, replace the sets by the Selections.</li>\r\n<li>Check and test the configuration.</li>\r\n</ul>\r\n<p>Follow this procedure to activate the usage of validations in SAP S/4HANA:</p>\r\n<ol class=\"ol\" id=\"loio64ebca416a16485484474caa4f2e4de1__ol_djw_rqc_xtb\">\r\n<li class=\"li\">List the validation methods in use and how they are defined.</li>\r\n<li class=\"li\">In the&#160;Define Validation Methods&#160;app, define new validations.</li>\r\n<li class=\"li\">In the&#160;Assign Validation Methods&#160;app, assign the validations to consolidation units.</li>\r\n<li class=\"li\">Check and test the configuration.</li>\r\n</ol>\r\n<p>If you don't use the Use Item Role Attribute option, you can keep you configuration unchanged, or you can adjust your configuration to use the FS item Role attribute to select the FS item:</p>\r\n<ul>\r\n<li>In the Check Global System Settings configuration activity, select the Use Item Role Attribute option.</li>\r\n<li>In the Define Currency Translation Methods and Define Reclassification Methods configuration activities, check and adjust the configuration of selected FS items so that the role assigned corresponds to the correct FS item.</li>\r\n<li>Check and test the configuration</li>\r\n</ul>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "Gregoire DESOMBRE (I014845)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D044125)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0003326805/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0003326805/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003326805/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003326805/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003326805/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003326805/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003326805/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003326805/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003326805/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "106", "To": "106", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "107", "To": "107", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "108", "To": "108", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}