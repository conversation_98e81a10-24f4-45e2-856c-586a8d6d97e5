{"Request": {"Number": "811311", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 462, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000004363982017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000811311?language=E&token=46E787CCB73556C3F38ED255DF19D919"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000811311", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000811311/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "811311"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "19.07.2016"}, "SAPComponentKey": {"_label": "Component", "value": "PY-PT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Portugal"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Portugal", "value": "PY-PT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-PT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "811311 - HR-PT: Annual Income Declaration - Electronic file"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>According to the legal entity DGCI (Direc&#231;&#227;o Geral de Contribui&#231;&#245;es e Impostos), \"Portaria no. 51 / 2004, de 16 de Janeiro,\" the Annual income declaration in electronic format(attachment J) has been modified.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>RPCAIDP0; Annual Income Declaration; attachment J; Anexo J; Model 10; Modelo 10; RPUTSVP0;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>VERSIONING</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Version</strong></td>\r\n<td><strong>Date</strong></td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td>6</td>\r\n<td>July 19, 2016</td>\r\n<td>SAR files were removed.</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><span style=\"font-size: 14px;\">&#160;</span></p>\r\n<ul>\r\n<li>created new structures for Header(record J01), Detail(record J02), and Trailler(record J99);</li>\r\n</ul>\r\n<ul>\r\n<li>created new domains, data elements;</li>\r\n</ul>\r\n<ul>\r\n<li>added new option \"Modelo 10 / Anexo J\" in the selection screen for the Annual income declaration report(RPCAIDP0);</li>\r\n</ul>\r\n<ul>\r\n<li>added new option \"Decl. Anual (Modelo 10 / Anexo J)\" for TemSe Display / Download Utility (RPUTSVP0);</li>\r\n</ul>\r\n<ul>\r\n<li>Residence abroad: if the employee's residence is abroad, the system will reject this employee;</li>\r\n</ul>\r\n<ul>\r\n<li>Field income local (Local de obten&#231;&#227;o de rendimentos):&#160;&#160;If you indicate the parameter retrieve FI data in the selection screen(RPCAIDP0), the system reads the postal code (first four digits) from table QSTRE to get the \"Local de obten&#231;&#227;o de rendimentos\". See below:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>postal code (0000-8999): Local de rendimento C(Continente).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>postal code (9000-9499): Local de rendimento RM(Regi&#227;o Aut&#243;noma da Madeira).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>postal code greater than 9499: Local de rendimento MA(Regi&#227;o Aut&#243;noma dos A&#231;ores).</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;This procedure had to be done for data which comming from FI. <br /><br />The corrections described in this note are available in an LCP. Please look below for the correction number. The LCP includes:</p>\r\n<ul>\r\n<li>New domains:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_AD026</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_AD087</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_AD106</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADJCI</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADJIR</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADJRE</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADJTR</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADLRE</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADNYR</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADPRE</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADTIR</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADTRA</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADTRE</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADTRE</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADTRM</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPPT_ADTRM</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADTRT</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>New/Corrected data elements:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_AD026</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_AD087</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_AD106</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADJCI</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADJIR</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADJRE</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADJTR</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADLRE</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADNYR</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADPRE</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADTIR</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADTRA</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADTRE</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADTRM</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_ADTRT</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT_MD10J</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>New structures:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPTX1</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPTX2</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPTX3</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Modification in stuctures:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCXXXPT</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Modification in type-pool:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PPT03</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>New entries in table:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>T52B5</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>V_T52B4</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>New text-elements (RPCAIDP0): text-068, text-069, text-070, text-071, text-072, text-073, text-074, text-075</li>\r\n</ul>\r\n<ul>\r\n<li>Source code corrections to the Programs/Includes:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCAIDP0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCAIDPD</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCAIDPF</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCAIDPO</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPUTSVP0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPUTSVPD</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPUTSVPF</li>\r\n</ul>\r\n</ul>\r\n<p><br /><strong>IMPORTANT NOTE:</strong></p>\r\n<ul>\r\n<li>This note provides the files .CAR in the attachment of this note. To know which files corresponding to your release, please see below:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Release 4.5B: L4DK119386.CAR , L4DK119715.CAR and L4DK119748.CAR(*)</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;L4DK119715.CAR (has the changes as indicated in Correction Inst. 0120031469 0000376273)<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;L4DK119748.CAR (has the changes as indicated in Correction Inst. 0120031469 0000377281)</p>\r\n<ul>\r\n<ul>\r\n<li>Release 4.6B: L9BK125290.CAR , L9BK125712.CAR and L9BK125754.CAR(*)</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;L9BK125712.CAR (has the changes as indicated in Correction Inst. 0120031469 0000376273)<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;L9BK125754.CAR (has the changes as indicated in Correction Inst. 0120031469 0000377281)</p>\r\n<ul>\r\n<ul>\r\n<li>Release 4.6C: L9CK181469.CAR , L9CK182534.CAR and L9CK182689.CAR (*)</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;L9CK182534.CAR (has the changes as indicated in Correction Inst. 0120031469 0000376273)<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;L9CK182689.CAR (has the changes as indicated in Correction Inst. 0120031469 0000377281)</p>\r\n<ul>\r\n<ul>\r\n<li>Release 4.70: L6BK085675.CAR , L6BK086352.CAR , L6BK086803.CAR and L6BK086919.CAR&#160;&#160;(*)</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;L6BK086352.CAR (has the changes as indicated in Correction Inst. 0120031469 0000373439)<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;L6BK086803.CAR (has the changes as indicated in Correction Inst. 0120031469 0000376273)<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;L6BK086919.CAR (has the changes as indicated in Correction Inst. 0120031469 0000377281)<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;(*) implement the files . CAR in the sequence as indicated above.</p>\r\n<ul>\r\n<li>Be aware of an Advance Delivery delivers the last version of the object,it means that if you do not have the last HR Support Package installed in you system you could get errors, either Syntax Errors or process errors. In this case the only option is to undo the changes from Advance Delivery and do the changes manually according to the Correction Instructions available in this note.</li>\r\n</ul>\r\n<ul>\r\n<li>After applying the new version, the old variant of the report (rpcaidp0)will not work anymore. Please, create new variant for the new version.</li>\r\n</ul>\r\n<ul>\r\n<li>Printed form(SAPScript) is deactivated for option \"Modelo 10/ Anexo J\".</li>\r\n</ul>\r\n<ul>\r\n<li>New TemSe object HR_CDAJP0_.... for \"Modelo 10/ Anexo J\".</li>\r\n</ul>\r\n<ul>\r\n<li>After note has been released wage type /111 was changed from category A1 to A, please refer to the following correction instructions (according to your release):</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>4.5B --&gt; 0120061532 0000701390</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>4.6B --&gt; 0120061532 0000701389</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>4.6C --&gt; 0120061532 0000701338</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>4.70 --&gt; 0120061532 0000701336</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>5.00 --&gt; 0120061532 0000701337</li>\r\n</ul>\r\n</ul></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I823284)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I827735)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000811311/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000811311/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000811311/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000811311/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000811311/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000811311/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000811311/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000811311/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000811311/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "830457", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Header and trailer records for \"Anexo J/Modelo 10\"", "RefUrl": "/notes/830457"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1127132", "RefComponent": "PY-PT", "RefTitle": "New fields and records to Annual Income Declaration report", "RefUrl": "/notes/1127132"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1127132", "RefComponent": "PY-PT", "RefTitle": "New fields and records to Annual Income Declaration report", "RefUrl": "/notes/1127132 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "830457", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Header and trailer records for \"Anexo J/Modelo 10\"", "RefUrl": "/notes/830457 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 45B", "SupportPackage": "SAPKE45BC3", "URL": "/supportpackage/SAPKE45BC3"}, {"SoftwareComponentVersion": "SAP_HR 45B", "SupportPackage": "SAPKE45BC1", "URL": "/supportpackage/SAPKE45BC1"}, {"SoftwareComponentVersion": "SAP_HR 45B", "SupportPackage": "SAPKE45BC2", "URL": "/supportpackage/SAPKE45BC2"}, {"SoftwareComponentVersion": "SAP_HR 46B", "SupportPackage": "SAPKE46BA4", "URL": "/supportpackage/SAPKE46BA4"}, {"SoftwareComponentVersion": "SAP_HR 46B", "SupportPackage": "SAPKE46BA5", "URL": "/supportpackage/SAPKE46BA5"}, {"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46C95", "URL": "/supportpackage/SAPKE46C95"}, {"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46C96", "URL": "/supportpackage/SAPKE46C96"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47040", "URL": "/supportpackage/SAPKE47040"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47041", "URL": "/supportpackage/SAPKE47041"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50007", "URL": "/supportpackage/SAPKE50007"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50008", "URL": "/supportpackage/SAPKE50008"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_HR", "NumberOfCorrin": 9, "URL": "/corrins/0000811311/2"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 9, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 11, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "31I", "ValidTo": "31I", "Number": "169116 ", "URL": "/notes/169116 ", "Title": "HR-PT: Legal Entity handling in tax reports", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "45B", "ValidTo": "45B", "Number": "422848 ", "URL": "/notes/422848 ", "Title": "HR-PT: Income Declaration reports in EURO in 2002", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "45B", "ValidTo": "46C", "Number": "511686 ", "URL": "/notes/511686 ", "Title": "HR-PT: Legal changes in Annual Income Declaration", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "46C", "Number": "422848 ", "URL": "/notes/422848 ", "Title": "HR-PT: Income Declaration reports in EURO in 2002", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "437432 ", "URL": "/notes/437432 ", "Title": "HR-PT: General changes for report RPCAIDP0", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "721081 ", "URL": "/notes/721081 ", "Title": "HR-PT:Legal Changes to the Social Security number(11 digits)", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "470", "Number": "544008 ", "URL": "/notes/544008 ", "Title": "HR-PT: Information available from FI system", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "470", "Number": "606082 ", "URL": "/notes/606082 ", "Title": "HR-PT: Record type for Attach. J - RPCAIDP0", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "470", "Number": "612856 ", "URL": "/notes/612856 ", "Title": "HR-PT: Remuneration type in record type 2 - RPCAIDP0", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "470", "Number": "670759 ", "URL": "/notes/670759 ", "Title": "HR-PT General corrections to the report RPCAIDP0", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "500", "Number": "811311 ", "URL": "/notes/811311 ", "Title": "HR-PT: Annual Income Declaration - Electronic file", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "721081 ", "URL": "/notes/721081 ", "Title": "HR-PT:Legal Changes to the Social Security number(11 digits)", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "811311 ", "URL": "/notes/811311 ", "Title": "HR-PT: Annual Income Declaration - Electronic file", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "721081 ", "URL": "/notes/721081 ", "Title": "HR-PT:Legal Changes to the Social Security number(11 digits)", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "811311 ", "URL": "/notes/811311 ", "Title": "HR-PT: Annual Income Declaration - Electronic file", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "648574 ", "URL": "/notes/648574 ", "Title": "HR-PT:Files downloaded are not filled with spaces in the end", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "721081 ", "URL": "/notes/721081 ", "Title": "HR-PT:Legal Changes to the Social Security number(11 digits)", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "811311 ", "URL": "/notes/811311 ", "Title": "HR-PT: Annual Income Declaration - Electronic file", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "811311 ", "URL": "/notes/811311 ", "Title": "HR-PT: Annual Income Declaration - Electronic file", "Component": "PY-PT"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}