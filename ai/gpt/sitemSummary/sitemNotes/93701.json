{"Request": {"Number": "93701", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 390, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014542332017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000093701?language=E&token=1CC6C76FE3D17C83A067054F97FEE0ED"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000093701", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000093701/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "93701"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "08.04.2001"}, "SAPComponentKey": {"_label": "Component", "value": "MM-IV"}, "SAPComponentKeyText": {"_label": "Component", "value": "Invoice Verification"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Invoice Verification", "value": "MM-IV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-IV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "93701 - MR01: Tax calculation in invoice verification"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p><br />When simulating or posting an invoice, the following error messages are generated due to the tax amount checks:<br /><br />&#x00A0;&#x00A0;FF747 The tax amount must not be greater than the tax base<br />&#x00A0;&#x00A0; or<br /> FF707 Tax entered is incorrect (code &amp;, amount &amp;), correct &amp; &amp;<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>MRHR, MRHG, MR4,1 MR42, MR44, MRKO, MR08, E00055</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><br />The taxation procedure in invoice verification is not clear.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The following applies to the processing of the tax in the conventional invoice verification:<br /><br />The tax codes in the individual invoice line items are important for the tax checks and not the entries on the vendor screen.<br /><br />Only one tax code can be used for each invoice line item, that is, itis not possible to enter an invoice for a purchase order item and to enter several tax amounts/tax codes with reference to this item.<br /><br />Using the tax codes and the amounts in the individual items, the system determines the tax base for every tax code.When simulating or posting, the system checks the tax amounts and it may ask you for corrections, if necessary.<br /><br />Example:<br />&#x00A0;&#x00A0;Vendor screen:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Total amount: 337 DEM<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Taxes:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;30 DEM&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; V1 (= 15 %)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 DEM&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; V2 (=&#x00A0;&#x00A0;7 %)<br />&#x00A0;&#x00A0;1. Invoice line item:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Amount 100 DEM and Tax code V1<br />&#x00A0;&#x00A0;2. Invoice line item:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Amount 100 DEM and Tax code V2<br />&#x00A0;&#x00A0;3. Invoice line item:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Amount 100 DEM and Tax code V1<br /><br />The input on the vendor screen is primary used as an input help. On the vendor screen, you can enter up to four tax amounts and tax codes (theprecise number is determined via the version to be set in Customizing). If an invoice contains even more tax rates than this number, you canchoose Extras -&gt; Taxes to enter the tax rates during further processing.<br /><br />The fact that the tax codes are only provided as an input help on the vendor screen leads to the following symptom during document parking (Transaction MR41): if you only enter the vendor line item and a tax code without a<br />tax amount, the tax code is then not stored in the parked document. Ifyou additionally enter a tax amount, the tax code is stored (which can be displayed under Extras --&gt; Taxes).<br /><br />For the tax checks and the automatic calculation of taxes, the tax codes entered in the vendor screen are not important but those tax codes stored in the invoice line items.Referring to the above example, this means that the system determines a base amount of 200 DEM for V1 due to invoice line items 1 and 3, a tax amount of 30 DEM and it then checks, whether 30 DEM were entered.<br /><br />If you, for example, only enter the tax code V1 and a tax amount of 45 DEM in the vendor screen, the tax check then leads to the generation of error message FF707 and you see the following data on the tax screen<br /><br />&#x00A0;&#x00A0;Amount&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Tax code&#x00A0;&#x00A0; Tax base amountt<br />&#x00A0;&#x00A0;45,00&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;V1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 200,00<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;V2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;100,00<br /><br />If you wanted to post the entire invoice using V1 you would have tochange the tax code in the detail screen of the second invoice line item to the required tax code V1. Change it to be able to post the document correctly.<br /><br /><br />How can the system find the tax codes in the invoice line items?<br /><br />For order-related invoices, the system generates the tax code which isstored in the purchase order item. Otherwise, the system copies thefirst tax code from the vendor screen to the invoice line item. If the purchase order has no tax code and if you do not enter a tax code inthe vendor screen, you are asked to enter a tax code when you create the invoice line item.<br /><br />Moreover, you can store a default tax code in Customizing which is proposed during invoice creation in the vendor screen and also copied to the invoice line items if no tax code is contained in the purchase order.<br /><br />Many problems in tax calculation are based on an incorrect setting ofthe \"Tax category\" field in the G/L account (Transaction FS02). If the underlying G/L account does not contain an entry in the \"Tax category\"field, the account is regarded as not tax-relevant. A tax base amount cannot be determined for this invoice line item in this case. This causes the following situation in the tax screen with a line without tax codes:<br /><br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Amount&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Tax code&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Tax base amount<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(is missing!)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;100,00<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;15,00&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;V1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0,00<br /><br />Moreover, the field for the tax code is hidden in the detail screen for the invoice line item.<br /><br />Thus, you must define a suitable tax category for the G/L account inthis case (see also F4 help for the field). For purchase ordersassigned to an account, the G/L account which is maintained in theaccount assignment data of the purchase order item generally causes the problem.<br /><br />If the value for the tax code cannot be changed in the detail screen of the invoice line item, the tax category field is also the reason, forexample, if a certain tax code is defined as a tax category and this tax code is therefore only allowed for all postings to the G/L account.<br /><br />In addition, note that the tax code field in the items is a required field (exception: the G/L account to be posted is not tax-relevant,that is, the tax category field in Transaction FS03 is not filled).This cannot be customized. If you do not want to enter taxes, you must use a tax code with 0 % (for example, indicator V0 in the standard system).<br /><br /><br />For problems with the tax calculations with unplanned delivery costs read also Note 52370.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D021095)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000093701/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000093701/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000093701/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000093701/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000093701/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000093701/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000093701/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000093701/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000093701/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "52370", "RefComponent": "MM-IV", "RefTitle": "Problems with taxes for unplanned delivery costs", "RefUrl": "/notes/52370"}, {"RefNumber": "304275", "RefComponent": "MM-IV", "RefTitle": "Australia: Handling of GST on freight in Purchasing", "RefUrl": "/notes/304275"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "52370", "RefComponent": "MM-IV", "RefTitle": "Problems with taxes for unplanned delivery costs", "RefUrl": "/notes/52370 "}, {"RefNumber": "304275", "RefComponent": "MM-IV", "RefTitle": "Australia: Handling of GST on freight in Purchasing", "RefUrl": "/notes/304275 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "300", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}