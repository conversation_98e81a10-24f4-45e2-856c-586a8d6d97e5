{"Request": {"Number": "110910", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1453, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014581992017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000110910?language=E&token=0C25CC8CDA48DC1D3B8A5135B0C8D5C0"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000110910", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000110910/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "110910"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 28}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.12.2015"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CTS-LAN"}, "SAPComponentKeyText": {"_label": "Component", "value": "Language Transport Tools (application server ABAP)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Change and Transport System", "value": "BC-CTS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CTS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Language Transport Tools (application server ABAP)", "value": "BC-CTS-LAN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CTS-LAN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "110910 - Deletion of language load"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The language load must be deleted.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Language load, language supplementation program, language import, RSLANG20</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Language-dependent texts were imported, supplemented or deleted (for example, data element texts, screen texts, menu options and so on).<br />However, the outdated or deleted texts are still displayed when you display the relevant screens, menus and so on.<br />This occurs because only the texts were changed in the database. On the screen, however, the <strong>runtime representation (load)</strong> of the relevant objects is displayed, and this has not changed.<br />Usually, this problem does not occur because all language transport programs delete or invalidate the runtime representations after the texts were changed in the database so that the system automatically regenerates the load with the changed texts during the next access.<br />However, in rare cases, the system may incorrectly or incompletely delete the load and still display the outdated texts.</p>\r\n<p><strong>Technical information</strong></p>\r\n<p>The following load types are relevant for the language transport.</p>\r\n<ul>\r\n<li>Screen load</li>\r\n</ul>\r\n<p>Here, a language-independent runtime representation (tables D020L, D020LINF or DYNPLOAD) and a language-dependent runtime representation (tables D021L, D021LINF or DYNTXTLD) of the screen fields are available.</p>\r\n<ul>\r\n<li>Screen Painter load (table DDFTX)</li>\r\n</ul>\r\n<p>This table buffers texts from the ABAP Dictionary.</p>\r\n<ul>\r\n<li>CUA load (tables D345T, D346T and D342L)</li>\r\n</ul>\r\n<p>Texts of menu bars and application toolbars</p>\r\n<ul>\r\n<li>Easy Access Menu (table TTREELOADT) - as of Release 4.6</li>\r\n</ul>\r\n<ul>\r\n<li>Texts of ALV grid control (table LTDXT) - as of Release 4.6</li>\r\n</ul>\r\n<p><strong>Caution:</strong></p>\r\n<p>Resetting the buffer does not cause any damage within the system because missing data is automatically reselected or regenerated. However, resetting the buffer using the report mentioned below may cause problems.</p>\r\n<ul>\r\n<li>Since buffers are reset, the performance may temporarily deteriorate.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>Resetting the language-independent screen load and the screen painter load may result in lock situations. This occurs if a program generates a selection screen while it is running in a background process and users want to simultaneously use the same screen in dialog mode. If this occurs, users may be blocked until the background job is completed. For more information about this, see Notes 330165, 337897 and 395997. Therefore, be careful when you start the report in the production system, and start the report only if no background processes are scheduled that have a long runtime.</strong> To prevent the lock situations in the database, implement all the corrections that are contained in Note 395997.</li>\r\n</ul>\r\n<ul>\r\n<li>Please note the following: <strong>After you run the report RSLANG20, do NOT execute /$SYNC\"</strong> even if this is recommended in a text line output, because this can result in negative side effects (for number range management, for example). Instead, use the commands /$CUA and /$DYN.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>To explicitly delete the runtime representations, proceed as follows:</p>\r\n<ol>1. For Releases 3.1 and 4.0:</ol>\r\n<p>First, import the transport request SAPKL31H05 from the directory ~ftp/general/R3server/language/del_load from sapserv. For more information about importing, see Note 13719.</p>\r\n<p>Then execute the report <strong>RSLANG20</strong> in the background.</p>\r\n<p>The following load types are deleted: screen load (language-independent part, language-dependent part), Screen Painter load and CUA load.</p>\r\n<p>&#x00A0;</p>\r\n<ol>2. Release 4.0B as of Support Package #77:</ol>\r\n<p>Execute the report <strong>RSLANG20</strong> in the background.</p>\r\n<p>The following load types are deleted: screen load (language-independent part</p>\r\n<p>and language-dependent part), Screen Painter load and CUA load.</p>\r\n<p>&#x00A0;</p>\r\n<ol>3. Release 4.5:</ol>\r\n<p>Execute the report <strong>RSLANG20</strong> in the background.</p>\r\n<p>The following load types are deleted: screen load (language-independent part, language-dependent part), Screen Painter load and CUA load.</p>\r\n<p>&#x00A0;</p>\r\n<ol>4. Release 4.6:</ol>\r\n<p>Execute the report <strong>RSLANG20</strong> in the background.</p>\r\n<p>The following load types are deleted: screen load (language-independent part, language-dependent part), Screen Painter load and CUA load.</p>\r\n<p><strong> Change due to Basis Support Package #25(46B), #15(46C) or #6(46D):</strong> The language-independent part of the screen load is deleted only if the parameter \"FORCE\" is set; the buffered entries in the Easy Access Menu are automatically invalidated. The postprocessing steps mentioned below should no longer be required.</p>\r\n<p>&#x00A0;</p>\r\n<ol>5. Release 6.x and 7.x:</ol>\r\n<p>Execute the report <strong>RSLANG20</strong>.</p>\r\n<p>You can use the selection screen to select the load types that must be reset. For more information, see the system documentation for this report.</p>\r\n<p>In various database systems (DB2, DB6, MSSQL, INF), you may not be able to successfully delete the screen load. This is corrected with Basis Support Package #9 (SAPKB61009).</p>\r\n<p>If you use application servers with different hardware architectures, refer to Note 497561. Otherwise, you must start RSLANG20 on each application server to completely delete the CUA load. This is corrected with Basis Support Package #14 (SAPKB61014).<strong>Postprocessing</strong></p>\r\n<ul>\r\n<li>Resetting the CUA buffers and the screen buffers</li>\r\n</ul>\r\n<p>After the report is completed, you must reset the CUA buffers and the screen buffers on the individual application servers to reload the buffered menu representations and screen representations.<br /> To do this, call transaction SM51 and navigate to each listed application server by double-clicking it. Subsequently, enter the command \"/$CUA\" in the OK code field for the relevant server to reset the CUA buffer of this server. Then, enter \"/$DYN\" to empty the screen buffer. For more information, also see Note 26171.<br /> As of Support Package #40 (4.5B) or Basis Support Package #25 (4.6B), #15 (4.6C) or #6 (4.6D), the CUA buffer is automatically emptied on all application servers.</p>\r\n<ul>\r\n<li>Resetting the Easy Access Menu buffer</li>\r\n</ul>\r\n<p>Call the report <strong>BMEN_CORRMEN1</strong> (see SAP Note <strong>316313</strong>).<br /> As of Basis Support Package #25 (4.6B), #15 (4.6C), or #6 (4.6D), this is done automatically.</p>\r\n<ul>\r\n<li>Resetting the buffers of the ALV grid control</li>\r\n</ul>\r\n<p>This is required for Releases 4.6 and 6.1.<br /> The report <strong>BALVBUFDEL</strong> resets the buffered data. Please read SAP Note <strong>339258</strong> before using the report.</p>\r\n<p><strong>Additional information</strong></p>\r\n<p>In systems with <strong>Release 4.6x</strong> and kernel <strong>4.6D, Patch Level &lt;600</strong>, problems can occur during screen generation (SAP Note 363394). Due to these problems, German texts may be displayed on the screen, even if translations are available in the system. If the execution of the report RSLANG20 does not solve this problem, import the most current kernel for the system.</p>\r\n<p>If you use a Sybase database and Basis Release 7.31 or 7.40, you must observe SAP Note 2160583, too.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D042454)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D019521)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000110910/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000110910/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000110910/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000110910/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000110910/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000110910/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000110910/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000110910/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000110910/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "849814", "RefComponent": "PPM-PRO", "RefTitle": "Fields are not translated into logon language", "RefUrl": "/notes/849814"}, {"RefNumber": "821701", "RefComponent": "BC-ABA-SC", "RefTitle": "Dynamic function texts", "RefUrl": "/notes/821701"}, {"RefNumber": "764480", "RefComponent": "BC-I18", "RefTitle": "SAP WinGUI I18N Trouble Shooting", "RefUrl": "/notes/764480"}, {"RefNumber": "761911", "RefComponent": "BC-SRV-DX-LSM", "RefTitle": "LSMW: Texts are only available in German or English", "RefUrl": "/notes/761911"}, {"RefNumber": "657148", "RefComponent": "PA-PA-IT", "RefTitle": "General updates", "RefUrl": "/notes/657148"}, {"RefNumber": "624095", "RefComponent": "BC-DWB-TOO-MEN", "RefTitle": "Menu texts and title texts in incorrect language", "RefUrl": "/notes/624095"}, {"RefNumber": "497561", "RefComponent": "BC-ABA-SC", "RefTitle": "Missing German umlauts in interface texts", "RefUrl": "/notes/497561"}, {"RefNumber": "48624", "RefComponent": "BC-DWB-UTL", "RefTitle": "Screens display obsolete or multi-lingual texts", "RefUrl": "/notes/48624"}, {"RefNumber": "452109", "RefComponent": "BC-CTS-TLS", "RefTitle": "Obsolete or missing texts in menus after transport", "RefUrl": "/notes/452109"}, {"RefNumber": "436165", "RefComponent": "BC-I18", "RefTitle": "Characters are displayed incorrectly", "RefUrl": "/notes/436165"}, {"RefNumber": "426827", "RefComponent": "CA-GTF-DRT", "RefTitle": "DART translation into German", "RefUrl": "/notes/426827"}, {"RefNumber": "400280", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Known problems with Support Pkgs in Basis Release 6.10", "RefUrl": "/notes/400280"}, {"RefNumber": "395997", "RefComponent": "BC-CTS-LAN", "RefTitle": "DB block on DDFTX after language transport action", "RefUrl": "/notes/395997"}, {"RefNumber": "383270", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/383270"}, {"RefNumber": "363394", "RefComponent": "BC-ABA-SC", "RefTitle": "Problems with Quickinfo and button texts", "RefUrl": "/notes/363394"}, {"RefNumber": "339258", "RefComponent": "BC-SRV-ALV", "RefTitle": "ALV buffer: concept, recommendations and known errors", "RefUrl": "/notes/339258"}, {"RefNumber": "316313", "RefComponent": "BC-DWB-SEM", "RefTitle": "Changes to area menus not visible in Easy Access", "RefUrl": "/notes/316313"}, {"RefNumber": "303196", "RefComponent": "BC-CTS-LAN", "RefTitle": "UKSymptom", "RefUrl": "/notes/303196"}, {"RefNumber": "26171", "RefComponent": "BC-ABA", "RefTitle": "Possible entries in the command field (\"OK code\")", "RefUrl": "/notes/26171"}, {"RefNumber": "2160583", "RefComponent": "BC-DB-DBI", "RefTitle": "Exception CX_SQL_EXCEPTION when calling function DB_DROP_DYNP_LOADS in SYBASE", "RefUrl": "/notes/2160583"}, {"RefNumber": "213204", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/213204"}, {"RefNumber": "198165", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/198165"}, {"RefNumber": "195442", "RefComponent": "BC-CTS-LAN", "RefTitle": "Language import and Support Packages", "RefUrl": "/notes/195442"}, {"RefNumber": "1889861", "RefComponent": "XX-TRANSL-FR", "RefTitle": "French transation correction for CRM_WARRANTY_MGNT 000", "RefUrl": "/notes/1889861"}, {"RefNumber": "1539606", "RefComponent": "GRC-SPC", "RefTitle": "GRC PC10.0 Translation missing for non-English Language", "RefUrl": "/notes/1539606"}, {"RefNumber": "1375438", "RefComponent": "FI-LOC-I18", "RefTitle": "Globalization Collection Note", "RefUrl": "/notes/1375438"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1172697", "RefComponent": "BC-ABA-SC", "RefTitle": "Terminatn:Mismatch Dynpro Name Content Program Dynpro Number", "RefUrl": "/notes/1172697"}, {"RefNumber": "116756", "RefComponent": "BC-CTS-LAN", "RefTitle": "Removing language-dependent parts from Support Packages", "RefUrl": "/notes/116756"}, {"RefNumber": "1159021", "RefComponent": "BC-CTS-LAN", "RefTitle": "Report RSTLAN_LANGUAGE_CHECK", "RefUrl": "/notes/1159021"}, {"RefNumber": "1005083", "RefComponent": "BC-DB-DBI", "RefTitle": "Inconsistent CUA loads in table D345T", "RefUrl": "/notes/1005083"}, {"RefNumber": "1000586", "RefComponent": "BC-CTS-LAN", "RefTitle": "How to analyze missing translations / missing texts", "RefUrl": "/notes/1000586"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3082111", "RefComponent": "CA-FLP-ABA", "RefTitle": "General issues with displaying of Fiori Tile Text like Titles and Subtitles", "RefUrl": "/notes/3082111 "}, {"RefNumber": "2645566", "RefComponent": "BC-UPG-OCS-SPA", "RefTitle": "Option SPAM->Utilities->Generate System info XML is missing", "RefUrl": "/notes/2645566 "}, {"RefNumber": "2852669", "RefComponent": "CO-FIO", "RefTitle": "Fiori app F1604 in dutch gives no description", "RefUrl": "/notes/2852669 "}, {"RefNumber": "1935497", "RefComponent": "BC-CTS-LAN", "RefTitle": "How to finish a language import by SMLT", "RefUrl": "/notes/1935497 "}, {"RefNumber": "2682266", "RefComponent": "BC-UPG-NA", "RefTitle": "\"Upload TCI\" or \"Upload SAP Note\" or \"Application log\" option missing in SNOTE", "RefUrl": "/notes/2682266 "}, {"RefNumber": "2560644", "RefComponent": "CA-FLP-ABA", "RefTitle": "How to modify and customize object translation", "RefUrl": "/notes/2560644 "}, {"RefNumber": "2490933", "RefComponent": "CO-PA-IS", "RefTitle": "New value field name and description are not reflected in T-code:KE24", "RefUrl": "/notes/2490933 "}, {"RefNumber": "3346774", "RefComponent": "BC-DOC-TTL", "RefTitle": "Identifying CDS View Labels for Translation on a SAP Fiori UI", "RefUrl": "/notes/3346774 "}, {"RefNumber": "3052358", "RefComponent": "XX-PART-KVSPRINT", "RefTitle": "KVsprint 5.751H: Import einer Sprachversion / eines Sprachpaketes", "RefUrl": "/notes/3052358 "}, {"RefNumber": "3038934", "RefComponent": "XX-TRANSL-FR", "RefTitle": "Translation correction for French in TM Freight Order Management", "RefUrl": "/notes/3038934 "}, {"RefNumber": "3014700", "RefComponent": "SCM-BAS-PAK-PS", "RefTitle": "Messages, Translation for Mass Change 2nd Version, and German Translation of Field Labels for Mass Change Packaging Specification", "RefUrl": "/notes/3014700 "}, {"RefNumber": "2873545", "RefComponent": "IS-U-EIM", "RefTitle": "German Translation Missing for EDN 2.1 Data Elements", "RefUrl": "/notes/2873545 "}, {"RefNumber": "2833362", "RefComponent": "FS-FPS-RD", "RefTitle": "Result Viewer does not return any records when max. hits are set", "RefUrl": "/notes/2833362 "}, {"RefNumber": "2800987", "RefComponent": "XX-PROJ-CDP-586", "RefTitle": "German Translations of Messages /MOSB/BO 152-160 Missing After Implementing SAP Note", "RefUrl": "/notes/2800987 "}, {"RefNumber": "2686199", "RefComponent": "XX-PART-CRD-COL", "RefTitle": "Crefosprint Inkasso 4.600: Import einer Sprachversion / eines Sprachpaketes", "RefUrl": "/notes/2686199 "}, {"RefNumber": "2470179", "RefComponent": "XX-TRANSL-JA", "RefTitle": "Correction of translation of 'LEADT'", "RefUrl": "/notes/2470179 "}, {"RefNumber": "2339376", "RefComponent": "XX-TRANSL-JA", "RefTitle": "Correction of translation of ''Define Staging Areas\"", "RefUrl": "/notes/2339376 "}, {"RefNumber": "2166955", "RefComponent": "BC-DWB-DIC", "RefTitle": "Deletion and reactivation of ABAP Dictionary object generates deadlock on DDFTX", "RefUrl": "/notes/2166955 "}, {"RefNumber": "2242918", "RefComponent": "XX-PART-KVSPRINT", "RefTitle": "KVsprint 4.600: Import of a language version", "RefUrl": "/notes/2242918 "}, {"RefNumber": "2062096", "RefComponent": "XX-PART-KVSPRINT", "RefTitle": "KVsprint 5.700: Import of a language version or a language package", "RefUrl": "/notes/2062096 "}, {"RefNumber": "1375438", "RefComponent": "FI-LOC-I18", "RefTitle": "Globalization Collection Note", "RefUrl": "/notes/1375438 "}, {"RefNumber": "1889861", "RefComponent": "XX-TRANSL-FR", "RefTitle": "French transation correction for CRM_WARRANTY_MGNT 000", "RefUrl": "/notes/1889861 "}, {"RefNumber": "1861886", "RefComponent": "XX-PART-KVSPRINT", "RefTitle": "KVsprint BoniCheck: Importing the language CD ENGLISH", "RefUrl": "/notes/1861886 "}, {"RefNumber": "1513173", "RefComponent": "XX-PART-KVSPRINT", "RefTitle": "KVsprint direct conn. COFACE: Importing ENGLISH language CD", "RefUrl": "/notes/1513173 "}, {"RefNumber": "941509", "RefComponent": "XX-PART-KVSPRINT", "RefTitle": "KVsprint 4.600: Importing the ENGLISH language CD", "RefUrl": "/notes/941509 "}, {"RefNumber": "839591", "RefComponent": "XX-PART-KVSPRINT", "RefTitle": "KVsprint 4.500: Importing the ENGLISH language CD", "RefUrl": "/notes/839591 "}, {"RefNumber": "801507", "RefComponent": "XX-PART-KVSPRINT", "RefTitle": "KVsprint 4.470: Importing the ENGLISH language CD", "RefUrl": "/notes/801507 "}, {"RefNumber": "195442", "RefComponent": "BC-CTS-LAN", "RefTitle": "Language import and Support Packages", "RefUrl": "/notes/195442 "}, {"RefNumber": "1511368", "RefComponent": "XX-PART-KVSPRINT", "RefTitle": "KVsprint SmartLink: Importing the ENGLISH language CD", "RefUrl": "/notes/1511368 "}, {"RefNumber": "1648934", "RefComponent": "PPM-PRO", "RefTitle": "PPM: Hints to solve language issues", "RefUrl": "/notes/1648934 "}, {"RefNumber": "1611778", "RefComponent": "XX-TRANSL-FR", "RefTitle": "French translation of schemes for Switzerland missing", "RefUrl": "/notes/1611778 "}, {"RefNumber": "1601174", "RefComponent": "XX-PART-KVSPRINT", "RefTitle": "KVsprint direct connection COFACE: Importing POLISH lang. CD", "RefUrl": "/notes/1601174 "}, {"RefNumber": "1601152", "RefComponent": "XX-PART-KVSPRINT", "RefTitle": "KVsprint direct conn. ATRADIUS: Importing POLISH langauge CD", "RefUrl": "/notes/1601152 "}, {"RefNumber": "1601147", "RefComponent": "XX-PART-KVSPRINT", "RefTitle": "KVsprint SmartLink: Importing the POLISH language CD", "RefUrl": "/notes/1601147 "}, {"RefNumber": "1601145", "RefComponent": "XX-PART-KVSPRINT", "RefTitle": "KVsprint 4.x: Importing the POLISH language CD", "RefUrl": "/notes/1601145 "}, {"RefNumber": "1539606", "RefComponent": "GRC-SPC", "RefTitle": "GRC PC10.0 Translation missing for non-English Language", "RefUrl": "/notes/1539606 "}, {"RefNumber": "436165", "RefComponent": "BC-I18", "RefTitle": "Characters are displayed incorrectly", "RefUrl": "/notes/436165 "}, {"RefNumber": "764480", "RefComponent": "BC-I18", "RefTitle": "SAP WinGUI I18N Trouble Shooting", "RefUrl": "/notes/764480 "}, {"RefNumber": "1522030", "RefComponent": "XX-PART-KVSPRINT", "RefTitle": "KVsprint Direct connection COFACE: Importing DUTCH lang. CD", "RefUrl": "/notes/1522030 "}, {"RefNumber": "1522029", "RefComponent": "XX-PART-KVSPRINT", "RefTitle": "KVsprint direct connection COFACE: Importing FRENCH lang. CD", "RefUrl": "/notes/1522029 "}, {"RefNumber": "1511370", "RefComponent": "XX-PART-KVSPRINT", "RefTitle": "KVsprint SmartLink: Importing the FRENCH language CD", "RefUrl": "/notes/1511370 "}, {"RefNumber": "1513108", "RefComponent": "XX-PART-KVSPRINT", "RefTitle": "KVsprint direct conn. ATRADIUS: Importing FRENCH langauge CD", "RefUrl": "/notes/1513108 "}, {"RefNumber": "1499182", "RefComponent": "XX-PART-KVSPRINT", "RefTitle": "KVsprint direct con. ATRADIUS: Importing ENGLISH language CD", "RefUrl": "/notes/1499182 "}, {"RefNumber": "1513109", "RefComponent": "XX-PART-KVSPRINT", "RefTitle": "KVsprint direct conn. ATRADIUS: Importing DUTCH language CD", "RefUrl": "/notes/1513109 "}, {"RefNumber": "1417818", "RefComponent": "XX-PART-KVSPRINT", "RefTitle": "KVsprint 4.x: Importing the DUTCH language CD", "RefUrl": "/notes/1417818 "}, {"RefNumber": "1511372", "RefComponent": "XX-PART-KVSPRINT", "RefTitle": "KVsprint SmartLink: Importing the DUTCH langauge CD", "RefUrl": "/notes/1511372 "}, {"RefNumber": "1172697", "RefComponent": "BC-ABA-SC", "RefTitle": "Terminatn:Mismatch Dynpro Name Content Program Dynpro Number", "RefUrl": "/notes/1172697 "}, {"RefNumber": "116756", "RefComponent": "BC-CTS-LAN", "RefTitle": "Removing language-dependent parts from Support Packages", "RefUrl": "/notes/116756 "}, {"RefNumber": "26171", "RefComponent": "BC-ABA", "RefTitle": "Possible entries in the command field (\"OK code\")", "RefUrl": "/notes/26171 "}, {"RefNumber": "1159021", "RefComponent": "BC-CTS-LAN", "RefTitle": "Report RSTLAN_LANGUAGE_CHECK", "RefUrl": "/notes/1159021 "}, {"RefNumber": "821701", "RefComponent": "BC-ABA-SC", "RefTitle": "Dynamic function texts", "RefUrl": "/notes/821701 "}, {"RefNumber": "807494", "RefComponent": "XX-PART-KVSPRINT", "RefTitle": "KVsprint 4.46C: Importing the ENGLISH language CD", "RefUrl": "/notes/807494 "}, {"RefNumber": "1005083", "RefComponent": "BC-DB-DBI", "RefTitle": "Inconsistent CUA loads in table D345T", "RefUrl": "/notes/1005083 "}, {"RefNumber": "452109", "RefComponent": "BC-CTS-TLS", "RefTitle": "Obsolete or missing texts in menus after transport", "RefUrl": "/notes/452109 "}, {"RefNumber": "316313", "RefComponent": "BC-DWB-SEM", "RefTitle": "Changes to area menus not visible in Easy Access", "RefUrl": "/notes/316313 "}, {"RefNumber": "339258", "RefComponent": "BC-SRV-ALV", "RefTitle": "ALV buffer: concept, recommendations and known errors", "RefUrl": "/notes/339258 "}, {"RefNumber": "363394", "RefComponent": "BC-ABA-SC", "RefTitle": "Problems with Quickinfo and button texts", "RefUrl": "/notes/363394 "}, {"RefNumber": "303196", "RefComponent": "BC-CTS-LAN", "RefTitle": "UKSymptom", "RefUrl": "/notes/303196 "}, {"RefNumber": "395997", "RefComponent": "BC-CTS-LAN", "RefTitle": "DB block on DDFTX after language transport action", "RefUrl": "/notes/395997 "}, {"RefNumber": "966934", "RefComponent": "LE-DSD-VC", "RefTitle": "/DSD/VC: Adjusting translations", "RefUrl": "/notes/966934 "}, {"RefNumber": "761911", "RefComponent": "BC-SRV-DX-LSM", "RefTitle": "LSMW: Texts are only available in German or English", "RefUrl": "/notes/761911 "}, {"RefNumber": "1000586", "RefComponent": "BC-CTS-LAN", "RefTitle": "How to analyze missing translations / missing texts", "RefUrl": "/notes/1000586 "}, {"RefNumber": "400280", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Known problems with Support Pkgs in Basis Release 6.10", "RefUrl": "/notes/400280 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "849814", "RefComponent": "PPM-PRO", "RefTitle": "Fields are not translated into logon language", "RefUrl": "/notes/849814 "}, {"RefNumber": "48624", "RefComponent": "BC-DWB-UTL", "RefTitle": "Screens display obsolete or multi-lingual texts", "RefUrl": "/notes/48624 "}, {"RefNumber": "657148", "RefComponent": "PA-PA-IT", "RefTitle": "General updates", "RefUrl": "/notes/657148 "}, {"RefNumber": "624095", "RefComponent": "BC-DWB-TOO-MEN", "RefTitle": "Menu texts and title texts in incorrect language", "RefUrl": "/notes/624095 "}, {"RefNumber": "426827", "RefComponent": "CA-GTF-DRT", "RefTitle": "DART translation into German", "RefUrl": "/notes/426827 "}, {"RefNumber": "497561", "RefComponent": "BC-ABA-SC", "RefTitle": "Missing German umlauts in interface texts", "RefUrl": "/notes/497561 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "46A", "To": "46D", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "710", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B77", "URL": "/supportpackage/SAPKH40B77"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B25", "URL": "/supportpackage/SAPKH45B25"}, {"SoftwareComponentVersion": "SAP_BASIS 46A", "SupportPackage": "SAPKB46A19", "URL": "/supportpackage/SAPKB46A19"}, {"SoftwareComponentVersion": "SAP_BASIS 46B", "SupportPackage": "SAPKB46B10", "URL": "/supportpackage/SAPKB46B10"}, {"SoftwareComponentVersion": "SAP_BASIS 46B", "SupportPackage": "SAPKB46B45", "URL": "/supportpackage/SAPKB46B45"}, {"SoftwareComponentVersion": "SAP_BASIS 46B", "SupportPackage": "SAPKB46B25", "URL": "/supportpackage/SAPKB46B25"}, {"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C03", "URL": "/supportpackage/SAPKB46C03"}, {"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C15", "URL": "/supportpackage/SAPKB46C15"}, {"SoftwareComponentVersion": "SAP_BASIS 46D", "SupportPackage": "SAPKB46D06", "URL": "/supportpackage/SAPKB46D06"}, {"SoftwareComponentVersion": "SAP_BASIS 46D", "SupportPackage": "SAPKB46D01", "URL": "/supportpackage/SAPKB46D01"}, {"SoftwareComponentVersion": "SAP_BASIS 610", "SupportPackage": "SAPKB61021", "URL": "/supportpackage/SAPKB61021"}, {"SoftwareComponentVersion": "SAP_BASIS 610", "SupportPackage": "SAPKB61009", "URL": "/supportpackage/SAPKB61009"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62004", "URL": "/supportpackage/SAPKB62004"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}