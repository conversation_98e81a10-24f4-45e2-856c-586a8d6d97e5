{"Request": {"Number": "1699099", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 384, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017410582017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=E51373DBDEBB9681C35BEF17CAB17896"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1699099"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "31.08.2012"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PROJ-FI-CA"}, "SAPComponentKeyText": {"_label": "Component", "value": "obsolete: Please use Component FI-CA instead"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Project-based solutions", "value": "XX-PROJ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Finance", "value": "XX-PROJ-FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "obsolete: Please use Component FI-CA instead", "value": "XX-PROJ-FI-CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-FI-CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1699099 - PMF: SEPA in Release 4.64 (composite SAP Note)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This SAP Note concerns the use of SEPA (Single Euro Payment Area) with the payment mediums for debit memos (Direct Debit - SEPA_DD) and bank transfers (Credit Transfer - SEPA_CT) in Release 4.64 (4.6C) for Contract Accounts Receivable and Payable (FI-CA).</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>FP-PMEDIA-DE, DME, data medium exchange, SEPA, SEPA_DD, SEPA_CT, MANDAT</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><UL><LI>This SAP Note concerns the use of the SEPA functions.</LI></UL> <UL><LI>Import Support Packages SAP_ABA 64 and SAP_APPL 64. To do this, see SAP Notes 1710815, 1709875 and 1712562.</LI></UL> <UL><LI>Implement the following SAP Notes also: 1738696, 1743014, 1744200.</LI></UL> <UL><LI>Implement the manual corrections from SAP Note 1759301.</LI></UL> <UL><LI>Note that you must convert the bank data for your customers to IBAN  (International Bank Account Number) and BIC/Swift (Bank Identification Code) to use SEPA.</LI></UL> <UL><LI>In addition, you require a mandate for each of your customers as a basis for the collection of SEPA debit memos.</LI></UL> <UL><LI>To use the electronic bank statement, you must also implement SAP Note 969358.</LI></UL> <p><br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>An enhancement has been provided with Add-on Support Package 36.<br /><br />This SAP Note does not contain any source code. Due to the extensive changes, it is not possible to implement these functions using an SAP Note.<br /><br />++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++<br />Manual steps for maintaining Customizing:<br />1.) Activate SEPA mandate management in transaction SM30 using the maintenance view V_SEPA_CUST. You can use mandate management only if Contract Accounts Receivable and Payable is entered as an application. As the form, you can define the form SEPA_MANDATE for \"Smartforms\".<br />2.) Control with individual modules: Call transaction SM30 and open the maintenance view V_SEPA_CTRL to define individual function modules in Customizing for the defined event of mandate management.<br /><br />NOTE THAT WE CANNOT DELIVER ANY NEW CUSTOMIZING ENTRIES IN THE IMG FOR TECHNICAL REASONS.<br /><br />++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++<br /><br />To fill the system table for the versioning fields, create the following report in your system and execute it:<br /><br />REPORT Z_FILL_SEPA_SFIELDS.<br />* If you want to set some entries in the customizing viewl SM30<br />* V_SEPA_FIELDS there is no system value available for the few<br />* fields which are defined by SAP. With this report these values<br />* are created in the database<br /><br />DATA:<br />&#x00A0;&#x00A0;x_sepa_sfields TYPE TABLE OF sepa_sfields WITH HEADER LINE.<br /><br />x_sepa_sfields-anwnd = '1'.<br />x_sepa_sfields-fname = 'B2B'.<br />x_sepa_sfields-chtyp = '2'.<br />APPEND x_sepa_sfields.<br /><br />x_sepa_sfields-anwnd = '1'.<br />x_sepa_sfields-fname = 'MNDID'.<br />x_sepa_sfields-chtyp = '3'.<br />APPEND x_sepa_sfields.<br /><br />x_sepa_sfields-anwnd = '1'.<br />x_sepa_sfields-fname = 'REC_CRDID'.<br />x_sepa_sfields-chtyp = '2'.<br />APPEND x_sepa_sfields.<br /><br />x_sepa_sfields-anwnd = '1'.<br />x_sepa_sfields-fname = 'REC_NAME1'.<br />x_sepa_sfields-chtyp = '2'.<br />APPEND x_sepa_sfields.<br /><br />x_sepa_sfields-anwnd = '1'.<br />x_sepa_sfields-fname = 'SND_BIC'.<br />x_sepa_sfields-chtyp = '2'.<br />APPEND x_sepa_sfields.<br /><br />x_sepa_sfields-anwnd = '1'.<br />x_sepa_sfields-fname = 'SND_IBAN'.<br />x_sepa_sfields-chtyp = '2'.<br />APPEND x_sepa_sfields.<br /><br />x_sepa_sfields-anwnd = '1'.<br />x_sepa_sfields-fname = 'STATUS'.<br />x_sepa_sfields-chtyp = '2'.<br />APPEND x_sepa_sfields.<br /><br />INSERT sepa_sfields FROM TABLE x_sepa_sfields.<br />IF sy-subrc = 0.<br />&#x00A0;&#x00A0;COMMIT WORK.<br />&#x00A0;&#x00A0;write:/ 'Settings correctly inserted'.<br />ELSE.<br />&#x00A0;&#x00A0;ROLLBACK WORK.<br />&#x00A0;&#x00A0;write:/ 'Error - Entries already available.'.<br />ENDIF.<br /><br />++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++<br /><br />New transactions:<br />Create Mandate: FSEPA_M1<br />Change Mandate: FSEPA_M2<br />Display Mandate: FSEPA_M3<br />List Mandates: FSEPA_M4<br />Create mandates in a mass run: FPSEPA<br />Change mandates in a mass run: FPSEPA1<br /><br /><br /><br /><br /><br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "IS-M-CA (Contract Accounts Receivable and Payable)"}, {"Key": "Other Components", "Value": "FI-CAX (Non-industry specific contract accounts receivable, payable)"}, {"Key": "Other Components", "Value": "IS-T-CA (Contract Accounting)"}, {"Key": "Other Components", "Value": "FS-CD (Collections and Disbursements)"}, {"Key": "Other Components", "Value": "IS-PS-CA (Public Sector Contract Accounting)"}, {"Key": "Other Components", "Value": "IS-U-CA (Contract Accounts Receivable and Payable)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D035698)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I029170)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "SEPA_SFIELDS.txt", "FileSize": "1", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000071022012&iv_version=0002&iv_guid=0D2E1119EBC06C46B2B0256BA9B09EEE"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "969358", "RefComponent": "FI-BL-PT-BA", "RefTitle": "MT940: IBAN of business partner is not processed", "RefUrl": "/notes/969358"}, {"RefNumber": "1855632", "RefComponent": "XX-PROJ-FI-CA", "RefTitle": "SEPA restrictions in Releases 4.64, 4.71, and 4.72", "RefUrl": "/notes/1855632"}, {"RefNumber": "1678321", "RefComponent": "RE-FX", "RefTitle": "EhP6: SEPA mandate management for SAP Real Estate Management", "RefUrl": "/notes/1678321"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1759170", "RefComponent": "RE-FX-RA", "RefTitle": "SEPA Composite SAP Note for SAP Real Estate Management", "RefUrl": "/notes/1759170 "}, {"RefNumber": "1678321", "RefComponent": "RE-FX", "RefTitle": "EhP6: SEPA mandate management for SAP Real Estate Management", "RefUrl": "/notes/1678321 "}, {"RefNumber": "1855632", "RefComponent": "XX-PROJ-FI-CA", "RefTitle": "SEPA restrictions in Releases 4.64, 4.71, and 4.72", "RefUrl": "/notes/1855632 "}, {"RefNumber": "969358", "RefComponent": "FI-BL-PT-BA", "RefTitle": "MT940: IBAN of business partner is not processed", "RefUrl": "/notes/969358 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-U/CCS", "From": "464", "To": "464", "Subsequent": ""}, {"SoftwareComponent": "INSURANCE", "From": "464", "To": "464", "Subsequent": ""}, {"SoftwareComponent": "FI-CA", "From": "464", "To": "464", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "FI-CA 464", "SupportPackage": "SAPKIPC636", "URL": "/supportpackage/SAPKIPC636"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1738696", "RefTitle": "SEPA mandate 4.6C: Short dump when creating attachment", "RefUrl": "/notes/0001738696"}]}}}}}