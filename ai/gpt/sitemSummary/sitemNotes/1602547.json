{"Request": {"Number": "1602547", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 299, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001602547?language=E&token=E7F89337F54BC5883646946C515949F1"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001602547", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001602547/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1602547"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 43}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "05.06.2018"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-SYB"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Suite on Adaptive Server Enterprise"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Suite on Adaptive Server Enterprise", "value": "BC-DB-SYB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-SYB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1602547 - SYB: Versions of the syb_update_db script"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>After upgrading or updating your installation of the SAP ASE database server, you want to download and run the newest version of the <em>syb_update_db</em> script.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>syb_update_db, Sybase ASE scripts</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The SQL script attached to this SAP note sets important parameters for SAP ASE in an SAP Business Suite system depending on the ASE version. The script&#160;should be executed automatically during upgrade of an ASE&#160;server&#160;when you followed the mandatory upgrade instructions in these SAP Note <a target=\"_blank\" href=\"/notes/1982469\">1982469</a> - SYB: saphostctrl update SAP ASE</p>\r\n<p>&#160;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The newest version of the <em>syb_update_db.TXT </em>script is attached to this SAP Note.<br /><br />The script performs SAP-specific adjustments to the SAP&#160;ASE database. This can include the setting of configuration parameters or database options, as well as the assignment of required privileges for logins used by the SAP application.<br /><br /><strong>Note: </strong>The script must be executed with login <em>sa</em>. As the <em>sa</em> login is usually locked, you may first need to unlock the <em>sa</em> login.<br /><br />To run the <em>syb_update_db</em> script, proceed as follows:</p>\r\n<ol>\r\n<li>Log on to the database server as user <em>syb&lt;sapsid&gt;.</em></li>\r\n<li>Change into the directory that contains the <em>syb_update_db.TXT</em> script.</li>\r\n<li>Run the script as follows:<br />&#160;</li>\r\nisql -Usa -S&lt;SAPSID&gt; -X -w999 -i syb_update_db.TXT -o syb_update_db.lo</ol><ol><ol>Check for errors in</ol></ol>\r\n<p><em>syb_update_db.log</em></p>\r\n<ol><ol>. After</ol></ol>\r\n<p><em>&#65279;</em><em>&#65279; syb_update_db.sql</em></p>\r\n<ol><ol>has been executed successfully,</ol></ol>\r\n<p><em>&#65279;</em></p>\r\n<ol><ol>it is strongly recommended to lock the</ol></ol>\r\n<p><em>sa </em></p>\r\n<ol>login again.</ol>\r\n<p><strong>How to unlock or lock the <em>sa</em> login</strong></p>\r\n<p>Unlock the <em>sa</em> login:</p>\r\n<ol>\r\n<li>Log in to the database server as user <em>syb&lt;sapsid&gt;.</em></li>\r\n<li>Connect to ASE as <em>sapsso.<br />&#160;</em><br /><em>isql -Usapsso -S&lt;SAPSID&gt; -X -w999</em><br />&#160;</li>\r\n<li>Check login <em>sa:</em><br /><br /><em>sp_displaylogin sa</em><br /><em>go</em><br />&#160;</li>\r\n<li>If the <em>sa</em> login has the status<em> 'Locked: YES'</em>, unlock the <em>sa</em> login:<br /><br /><em>sp_locklogin sa, 'unlock'</em><br /><em>go</em></li>\r\n</ol>\r\n<p>Lock the <em>sa</em> login:</p>\r\n<ol>\r\n<li>Log in to the database server as user <em>syb&lt;sapsid&gt;.</em></li>\r\n<li>Connect to ASE as <em>sapsso.</em><br /><br /><em>isql -Usapsso -S&lt;SAPSID&gt; -X -w999</em><br />&#160;</li>\r\n<li>Check login <em>sa:</em><br /><br /><em>sp_displaylogin sa</em><br /><em>go</em><br />&#160;</li>\r\n<li>If the <em>sa</em> login has the status 'Locked: NO', lock the <em>sa</em> login:<br /><br /><em>sp_locklogin sa, 'lock'</em><br /><em>go</em></li>\r\n</ol>\r\n<p><strong>Change log:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Date</td>\r\n<td>Version</td>\r\n<td>Change</td>\r\n</tr>\r\n<tr>\r\n<td>2012-05-24</td>\r\n<td>2012-01</td>\r\n<td>Enable update statistics hashing as of **********<br />Enable deferred table allocation as of **********<br />Enable dump history as of **********</td>\r\n</tr>\r\n<tr>\r\n<td>2012-07-18</td>\r\n<td>2012-02</td>\r\n<td>Enable update statistics hashing as of **********<br />Enable plan sharing as of **********<br />Enable concurrent dump tran as of **********</td>\r\n</tr>\r\n<tr>\r\n<td>2012-10-30</td>\r\n<td>2012-03</td>\r\n<td>Disable update statistics hashing as of **********<br />Disable plan sharing as of **********<br />No deferred table allocation as of **********</td>\r\n</tr>\r\n<tr>\r\n<td>2013-01-30</td>\r\n<td>2013-01</td>\r\n<td>Enable deferred table allocation as of **********<br />Enable update statistics hashing as of **********<br />Enable plan sharing as of **********</td>\r\n</tr>\r\n<tr>\r\n<td>2013-04-16</td>\r\n<td>2013-02</td>\r\n<td>Enable deallocate first text page as of 15.7.0.042<br />Enable execution time monitoring as of 15.7.0.100<br />Enable incremental dumps as of 15.7.0.100</td>\r\n</tr>\r\n<tr>\r\n<td>2013-05-24</td>\r\n<td>2013-03</td>\r\n<td>Enable deallocate first text page as of 15.7.0.043 (instead of 15.7.0.042)</td>\r\n</tr>\r\n<tr>\r\n<td>2014-05-12</td>\r\n<td>2014-01</td>\r\n<td>Enable utility lvl 0 scan waits as of **********<br />disable plan sharing unconditionally</td>\r\n</tr>\r\n<tr>\r\n<td>2014-05-18</td>\r\n<td>2014-02</td>\r\n<td>Disable dump for fast load for **********</td>\r\n</tr>\r\n<tr>\r\n<td>2014-05-19</td>\r\n<td>2014-03</td>\r\n<td>Disable dump for fast load for **********</td>\r\n</tr>\r\n<tr>\r\n<td>2014-07-22</td>\r\n<td>1602547-024</td>\r\n<td>Change version to match version of this SAP Note. <br />Fix script to work properly for Java only databases.<br />Fix script to quit w/o updating server configuration when running on non SAP Business Suite ASE servers.</td>\r\n</tr>\r\n<tr>\r\n<td>2014-10-17</td>\r\n<td>1602547-025</td>\r\n<td>Disable sticky statistics&#160;as of&#160;SAP ASE 15.7 SP130.<br />Introduction of an optional optimization goal for SAP Business Suite (sap_oltp).</td>\r\n</tr>\r\n<tr>\r\n<td>2014-11-27</td>\r\n<td>1602547-026</td>\r\n<td>Adjustment of the 'sap_oltp' optimization goal</td>\r\n</tr>\r\n<tr>\r\n<td>2015-02-25</td>\r\n<td>1602547-027</td>\r\n<td>\r\n<p>Set 'wait on uncommitted inserts' as of SAP ASE **********,<br />Disable sticky statistics&#160;as of&#160;SAP ASE 16.0 SP01<br />Set 'enable select into in tran' as of SAP ASE 16.0 GA PL05</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2015-04-30</td>\r\n<td>1602547-028</td>\r\n<td>\r\n<p>&#160;Set 'allow statement rollback' for SAP ASE **********</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2015-07-16</td>\r\n<td>1602547-029</td>\r\n<td>\r\n<p>&#160;Set&#160;'restricted parameter markers' on SAP ASE 16.0 SP02</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2015-08-12</td>\r\n<td>1602547-030</td>\r\n<td>\r\n<p>&#160;Grant select permissions on [get|set|rm]_appcontext&#160;to SAPSR3</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2015-08-17</td>\r\n<td>1602547-031</td>\r\n<td>\r\n<p>&#160;Remove setting of 'restricted parameter markers' (enabled under functionality group by default)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2015-10-28</td>\r\n<td>1602547-032</td>\r\n<td>\r\n<p>&#160;Always set 'enable bulk inserts' to 0 (mandatory for SAP Netweaver)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2015-10-28</td>\r\n<td>1602547-033</td>\r\n<td>\r\n<p>&#160;Some format changes</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2015-10-28</td>\r\n<td>1602547-034</td>\r\n<td>\r\n<p>&#160;Change version number of script to match version of SAP Note</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2016-03-10</td>\r\n<td>1602547-036</td>\r\n<td>\r\n<p>&#160;Do not set DB option <em>'allow incremental dump'</em> by default on the SAP database</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2016-06-29</td>\r\n<td>1602547-037</td>\r\n<td>\r\n<p>&#160;Set DB option <em>'allow db suspect on rollback error'</em> for SAP database for ASE 15.7 SP137 and higher<br />&#160;Set DB option <em>'allow db suspect on rollback error'</em> for SAP database for ASE 16.0 SP02 PL04&#160;and higher&#160;<br />&#160;Set parameter <em>'enable lightweight rvm'</em> for SAP ASE 16.0 SP02 PL04&#160;and higher</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2016-10-10</td>\r\n<td>1602547-039</td>\r\n<td>\r\n<p>&#160;Don't quit when SAP&#160;database is not found - just print a warning. <br />&#160;DB options will not be set for any database&#160;when SAP database is not found<br />&#160;Adapt ASE config for ASE 16.0 SP02 PL05</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2017-01-11</td>\r\n<td>1602547-040</td>\r\n<td>\r\n<p>&#160;Set 'extend implicit conversion' to&#160;1 as of 16.0.02.05 <br />(be aware that&#160;this parameter&#160;exists only in SP02 PL05 HF1&#160;and higher)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2018-06-05</td>\r\n<td>1602547-043</td>\r\n<td>\r\n<p>enable 'inline table functions' in 16.0 SP03<br />re-enable logging of messages 701 and 12205</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D065050)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D058468)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001602547/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001602547/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001602547/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001602547/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001602547/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001602547/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001602547/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001602547/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001602547/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "syb_update_db.TXT", "FileSize": "10", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000300812011&iv_version=0043&iv_guid=00109B36DBDE1ED89A95586F176340C2"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1982469", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Updating SAP ASE with saphostctrl", "RefUrl": "/notes/1982469"}, {"RefNumber": "1607816", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Installing Service Packs for SAP ASE (Windows)", "RefUrl": "/notes/1607816"}, {"RefNumber": "1599814", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Installing Service Packs for SAP  ASE (UNIX + Linux)", "RefUrl": "/notes/1599814"}, {"RefNumber": "1539124", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Database Configuration for SAP applications on SAP ASE", "RefUrl": "/notes/1539124"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2103871", "RefComponent": "BW-SYS-DB-SYB", "RefTitle": "SYB: Migrating SAP BW to SAP  Adaptive Server Enterprise", "RefUrl": "/notes/2103871 "}, {"RefNumber": "2081796", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: SAP ASE optimization goals supported for SAP NetWeaver-based applications", "RefUrl": "/notes/2081796 "}, {"RefNumber": "1815695", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Automatic Database Expansion in SAP ASE", "RefUrl": "/notes/1815695 "}, {"RefNumber": "1607816", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Installing Service Packs for SAP ASE (Windows)", "RefUrl": "/notes/1607816 "}, {"RefNumber": "1539124", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Database Configuration for SAP applications on SAP ASE", "RefUrl": "/notes/1539124 "}, {"RefNumber": "1605680", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Troubleshoot the setup of the DBA Cockpit on SAP ASE", "RefUrl": "/notes/1605680 "}, {"RefNumber": "1599814", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Installing Service Packs for SAP  ASE (UNIX + Linux)", "RefUrl": "/notes/1599814 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}