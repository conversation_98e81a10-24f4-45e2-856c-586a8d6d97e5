{"Request": {"Number": "1646595", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 197, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017332972017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=89A009F493FA0F2632F8B64F31C891F3"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1646595"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 17}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.11.2021"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX"}, "SAPComponentKeyText": {"_label": "Component", "value": "Flexible Real Estate Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1646595 - RE_AUDIT: Measurement for Real Estate Management under ERP"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to understand how the measurement for the Real Estate Management component works.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Price list, system measurement, measurement, RE_AUDIT, RE_AUDIT_USER</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This is a consulting note.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Real Estate Management is the SAP module for the administration of commercial and residential rental real estate or real estate in a company&#x2019;s own stock as well as for the balance sheet evaluation of leasing contracts (IFRS 16, US GAAP 842, RE-FX only). The measurement contains both versions of SAP Real Management (RE-FX: Flexible Real Estate and RE Classic: Classic Real Estate). You can find more information about these solutions in Note 443311.<br /><br />This SAP Note describes how to calculate key figures for the purpose of using SAP Real Estate Management. Contact SAP Product Sales for information about the current price list.</p>\r\n<p>The measurement function modules that are relevant for SAP Real Estate Management provide the following information:</p>\r\n<ul>\r\n<li>when using <strong>RE-FX</strong></li>\r\n<ul>\r\n<li>Number of rental objects (relevant for SAP Real Estate Management \"residential property management\")</li>\r\n<li>Number of parcels (relevant for SAP Real Estate Management \"land management\")</li>\r\n<li>Number of RE users</li>\r\n<ul>\r\n<li>Full scope of RE-FX functions (relevant for SAP Real Estate Management \"office, retail and industrial property management\")</li>\r\n<li>SAP Contract &amp; Lease Management (\"Contract &amp; Lease Management\" license as a subfunction of SAP RE-FX)</li>\r\n<li>SAP Real Estate Portfolio Management (\"Portfolio Management\" license as the enhancement of the \"Contract &amp; Lease Management\" license)</li>\r\n</ul>\r\n</ul>\r\n<li>&#x00A0;when using <strong>Classic RE </strong>(old contracts)</li>\r\n<ul>\r\n<li>number of rental unit (apartments)</li>\r\n<li>area measurement of rental units (other usage types)</li>\r\n</ul>\r\n</ul>\r\n<p>The measurement is cross-client. No consolidation of the&#x00A0;measurement results from different clients or systems is carried out.&#x00A0;Because the switch that determines whether Classic RE or RE-FX is active is set at client level, there may be mixed results. In other words, key figures from Classic RE as well as RE-FX are listed, depending on which component is active in the respective client.</p>\r\n<p><strong>Details about the determination of key figures</strong></p>\r\n<p><strong>1. You use RE-FX.</strong></p>\r\n<p>Leading key figures:</p>\r\n<ul>\r\n<li>6004    RE-FX: Number of rental objects (mixed)</li>\r\n<li>6005    RE-FX: Number of rental objects (commercial)</li>\r\n<li>6008    RE-FX: Number of rental objects (apartments)</li>\r\n<li>6006    RE-FX: Number of parcels</li>\r\n<li>6007    RE-FX: Number of users of full scope of functions of SAP RE-FX</li>\r\n<li>6009 RE-FX: Number of users of SAP Contract and Lease Management</li>\r\n<li>6010 RE-FX: Number of users of SAP Real Estate Portfolio Management</li>\r\n</ul>\r\n<p>The key figures 6004, 6005, 6006 and 6008 are determined using the module RE_AUDIT in the clients in which RE-FX is active.</p>\r\n<p><strong>Rental Objects</strong></p>\r\n<p>The count is based on the usage type of a rental object. The (external) usage type defined in a rental object refers to an internal usage type (TIV01-SNUNRINT). The internal usage type refers to the usage category (TIV13-SNUNRINT). The latter is important for the assignment of a rental object to one of the key figures:</p>\r\n<ul>\r\n<li>Key figure 6004: Rental objects with usage category 3 Used for both</li>\r\n<li>Key figure 6005: Rental objects with usage category 2 Commercial</li>\r\n<li>Key figure 6008: Rental objects with usage category 1 Non-commercial</li>\r\n</ul>\r\n<p>The category of the rental object (rental units, rental spaces or pooled spaces) is irrelevant for the count. The count is performed consistently for each rental object.</p>\r\n<p>The following are not counted:</p>\r\n<ul>\r\n<li>rental objects whose validity has ended directly or indirectly (using higher-level objects in the hierarchy)</li>\r\n<li>rental objects with deletion flag or deletion indicator</li>\r\n</ul>\r\n<p>Rental objects in a COA are counted only once. The objects from other company codes (manager company code, object company code, condominium owner company code) linked to the COA objects are not counted.</p>\r\n<p>If you used Classic RE before and then migrated to RE-FX via REMICL, then the migrated clients contain rental units in the Classic RE tables and in the RE-FX tables in parallel. Because RE-FX is active in these clients, only the rental units of the RE-FX tables are counted.</p>\r\n<p>You can use transaction REISRO to obtain an overview of how many rental objects exist in a client. On the selection screen, choose \"Dynamic Selections\" and enter the usage types that are set up for the usage type of the relevant key figure in your system. In the list, set the layout so that the \"Number of rows\" column (REISALVROWS) is displayed. This column contains the value 1 in each case. By totaling this column, you can obtain the number of rental objects displayed in the list. Since the measurement function modules are cross-client, but reports are always executed for specific clients, you must execute the report in each client separately to obtain the comparative data. For large datasets, the report should be started in the background.</p>\r\n<p><strong>Parcels</strong></p>\r\n<p>The key figure Parcels 6006 is determined by counting all currently valid parcels. Parcels that are flagged for deletion or that refer to a deletion indicator are not counted.</p>\r\n<p>You can use transaction REISPL to obtain an overview of how many parcels exist in a client. In the list, set the layout so that the \"Number of rows\" column (REISALVROWS) is displayed. This column contains the value 1 in each case. By totaling this column, you can obtain the number of rental objects displayed in the list. Since the measurement function modules are cross-client, but reports are always executed for specific clients, you must execute the report in each client separately to obtain the comparative data. For large datasets, the report should be started in the background.</p>\r\n<p><strong>Minimum holdings</strong></p>\r\n<p>The key figures 6004, 6005, 6006 and 6008 are returned with the value zero if there are less than 100 objects of the relevant cadastral category.</p>\r\n<p><strong>RE users</strong></p>\r\n<p>The key figures 6007, 6009 and 6010 are determined using the module RE_AUDIT_USER and are based on system recordings. This is used to determine which system user (all users are taken into account regardless of the user type USTYP) called which RE transactions in the last four months. Users who are no longer valid on the measurement key date (field GLTGB) are not counted.</p>\r\n<p>This includes all users that called an RE transaction. There is no differentiation made between a transaction called to display, change, create or analyze RE data. The only important point is that the transaction is assigned to the RE-FX application. This basically means transactions that start with RE* and belong to one of the packages RE_*, /CEERE/* or GLO_REFX*.</p>\r\n<p>If you require more detailed information about which users have used which transactions, you will find a utility program in SAP Note 1786263 that lists this information.</p>\r\n<p><span style=\"text-decoration: underline;\">Key figure 6007- </span><span style=\"text-decoration: underline;\">number of users of full scope of functions of SAP RE-FX:</span></p>\r\n<p>This basically means transactions that start with RE* and belong to one of the packages RE_*, /CEERE/* or GLO_REFX*.</p>\r\n<p>The key figure 6007 is returned with the value 0 if</p>\r\n<ul>\r\n<li>no entries are found in the relevant master data tables of RE-FX or</li>\r\n<li>not more than three users were counted as having called RE transactions.</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\"></span></p>\r\n<p><span style=\"text-decoration: underline;\">Key figure 6009-</span><span style=\"text-decoration: underline;\">number of users of SAP Contract and Lease Management:</span></p>\r\n<p>This basically means transactions that start as follows and belong to one of the packages RE_*, /CEERE/* or GLO_REFX*:</p>\r\n<ul>\r\n<li>REAJ*</li>\r\n<li>RECD*</li>\r\n<li>RECE*</li>\r\n<li>RECN*</li>\r\n<li>RECPA4*</li>\r\n<li>RECPA5*</li>\r\n<li>RECPA6*</li>\r\n<li>RECPA7*</li>\r\n<li>REISCD*</li>\r\n<li>REISCN*</li>\r\n<li>REISCD*</li>\r\n<li>REISRA*</li>\r\n<li>RERA*</li>\r\n<li>RESR*</li>\r\n<li>REX*</li>\r\n<li>REEX*</li>\r\n</ul>\r\n<p>And the following unique transactions:</p>\r\n<ul>\r\n<li>RE80</li>\r\n<li>RECACUST</li>\r\n<li>RECALA</li>\r\n<li>RECARS</li>\r\n<li>RECARSCN</li>\r\n<li>RECARSGENCN</li>\r\n<li>REISMSCN</li>\r\n<li>REISREDOCCN</li>\r\n<li>REISCHGDOCCN</li>\r\n<li>REISACRITEM</li>\r\n<li>REISALIT</li>\r\n</ul>\r\n<p>The key figure 6009 is returned with the value 0 if</p>\r\n<ul>\r\n<li>no entries are found in the relevant master data tables of RE-FX or</li>\r\n<li>not more than three users were counted as having called RE transactions.</li>\r\n</ul>\r\n<p>&#x00A0;</p>\r\n<p><span style=\"text-decoration: underline;\">Key figure 6010- </span><span style=\"text-decoration: underline;\">number of users of SAP Real Estate Portfolio Management:</span></p>\r\n<p>The key figure 6010 considers all valid transaction calls as per the key figure 6007 minus the transactions already covered in the key figure 6009. (This means that all users who have called such a transaction are counted here. Please note that this user may be counted in both 6009 and 6010 if he/she has called transactions from both key figures).&#x00A0;You should also note that users who are counted in the key figure 6010 must also be licensed for the transactions of the key figure 6009 (required licensing).</p>\r\n<p>The key figure 6010 is returned with the value 0 if</p>\r\n<ul>\r\n<li>no entries are found in the relevant master data tables of RE-FX or</li>\r\n<li>not more than three users were counted as having called RE transactions.</li>\r\n</ul>\r\n<p>&#x00A0;</p>\r\n<p><strong>Mixed holdings</strong></p>\r\n<p>Real Estate measurement is more problematic for customers with mixed holdings. Within the scope of the measurement, it is not possible to specify whether an RE user operates residential rental or commercial Real Estate management. In other words, the measurement always returns the number of all RE users. The measurement can deliver only one reference point for licensing here. In these cases, you must agree with your responsible SAP Account Executive (sales personnel) on the ratio of RE users that manage commercial real estate to the total number of RE users.</p>\r\n<p>&#x00A0;</p>\r\n<p><strong>2. You use RE-Classic.</strong></p>\r\n<p>Leading key figures:</p>\r\n<ul>\r\n<li>6000 RE-Classic: Rental units</li>\r\n<li>6001 RE-Classic: Square meters (in thousand)</li>\r\n<li>6002 RE-Classic: Square feet (in thousand)</li>\r\n</ul>\r\n<p>The key figures 6000, 6001 and 6002 are determined using the module RE_AUDIT and therefore only in the clients in which RE-Classic is active.</p>\r\n<p>The number of apartment rental units (internal usage type \"apartment\") is counted, and for commercial rental units the area is counted. The reference area is totaled according to Customizing (this is defined according to the usage type). If no reference area is defined for a usage type in Customizing, then all areas that are assigned to the rental unit are totaled. If there is no area at all for a usage type within a client (for example in the case of garages), then it is counted under key figure 6000.</p>\r\n<p>&#x00A0;</p>\r\n<p>&#x00A0;</p>\r\n<p><strong>3. Outlook for measurement under S/4HANA</strong></p>\r\n<p>In SAP S/4HANA, the measurement usually takes place according to the new key figure 6014 (Number of RE-FX Objects). For details of this, see SAP Note 2444992.</p>\r\n<p>To enable customers to determine their current number of objects in ECC, the enhancement of the measurement program in accordance with SAP Note 2444992 is also available for ECC. This means that after you implement the relevant SAP Notes/support packages, the key figure 6014 is also determined in ECC.</p>\r\n<p>&#x00A0;</p>\r\n<p>&#x00A0;</p>\r\n<p>&#x00A0;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-SER-LAS (License Auditing Services)"}, {"Key": "Relevancy for System Measurement", "Value": "Engine Measurement Info"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D044055)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D032052)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "939971", "RefComponent": "RE-FX", "RefTitle": "FAQ and consulting notes for RE-FX", "RefUrl": "/notes/939971"}, {"RefNumber": "663095", "RefComponent": "RE-FX", "RefTitle": "SAP Real Estate in R/3 Enterprise and mySAP ERP - Licenses", "RefUrl": "/notes/663095"}, {"RefNumber": "443311", "RefComponent": "RE", "RefTitle": "Enterprise extension SAP Real Estate: Technical basis", "RefUrl": "/notes/443311"}, {"RefNumber": "313281", "RefComponent": "XX-SER-LAS", "RefTitle": "System measurement: Measurement of SAP Real Estate", "RefUrl": "/notes/313281"}, {"RefNumber": "3046844", "RefComponent": "RE-FX", "RefTitle": "RE-FX: New Object-Based Pricing", "RefUrl": "/notes/3046844"}, {"RefNumber": "2682691", "RefComponent": "RE-FX-IS", "RefTitle": "Update RE-FX Audit Process", "RefUrl": "/notes/2682691"}, {"RefNumber": "2444992", "RefComponent": "RE-FX", "RefTitle": "RE_AUDIT: Measurement for Real Estate Management under S/4HANA", "RefUrl": "/notes/2444992"}, {"RefNumber": "1786263", "RefComponent": "RE-FX", "RefTitle": "Evaluation information user measurement RE_AUDIT_USER (Z program)", "RefUrl": "/notes/1786263"}, {"RefNumber": "1719846", "RefComponent": "RE-FX", "RefTitle": "RE-FX user measurement: Deleted users", "RefUrl": "/notes/1719846"}, {"RefNumber": "1702352", "RefComponent": "RE-FX", "RefTitle": "RE_AUDIT: Validity of mandate CC nt taken into consideration", "RefUrl": "/notes/1702352"}, {"RefNumber": "1601903", "RefComponent": "RE-BD", "RefTitle": "No measurement results for Classic RE", "RefUrl": "/notes/1601903"}, {"RefNumber": "1559005", "RefComponent": "RE-FX", "RefTitle": "Report information for user measurement", "RefUrl": "/notes/1559005"}, {"RefNumber": "1415175", "RefComponent": "RE-FX", "RefTitle": "Measurement tool: RE_AUDIT", "RefUrl": "/notes/1415175"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3118678", "RefComponent": "RE-FX", "RefTitle": "RE_AUDIT_USER: Measurement for Real Estate Management", "RefUrl": "/notes/3118678 "}, {"RefNumber": "2928892", "RefComponent": "RE-FX", "RefTitle": "RE_AUDIT: Measurement for Real Estate Management", "RefUrl": "/notes/2928892 "}, {"RefNumber": "2682691", "RefComponent": "RE-FX-IS", "RefTitle": "Update RE-FX Audit Process", "RefUrl": "/notes/2682691 "}, {"RefNumber": "2444992", "RefComponent": "RE-FX", "RefTitle": "RE_AUDIT: Measurement for Real Estate Management under S/4HANA", "RefUrl": "/notes/2444992 "}, {"RefNumber": "2044508", "RefComponent": "RE-FX", "RefTitle": "User survey RE-FX: Conversion of data retrieval", "RefUrl": "/notes/2044508 "}, {"RefNumber": "939971", "RefComponent": "RE-FX", "RefTitle": "FAQ and consulting notes for RE-FX", "RefUrl": "/notes/939971 "}, {"RefNumber": "1786263", "RefComponent": "RE-FX", "RefTitle": "Evaluation information user measurement RE_AUDIT_USER (Z program)", "RefUrl": "/notes/1786263 "}, {"RefNumber": "1559005", "RefComponent": "RE-FX", "RefTitle": "Report information for user measurement", "RefUrl": "/notes/1559005 "}, {"RefNumber": "1719846", "RefComponent": "RE-FX", "RefTitle": "RE-FX user measurement: Deleted users", "RefUrl": "/notes/1719846 "}, {"RefNumber": "1702352", "RefComponent": "RE-FX", "RefTitle": "RE_AUDIT: Validity of mandate CC nt taken into consideration", "RefUrl": "/notes/1702352 "}, {"RefNumber": "663095", "RefComponent": "RE-FX", "RefTitle": "SAP Real Estate in R/3 Enterprise and mySAP ERP - Licenses", "RefUrl": "/notes/663095 "}, {"RefNumber": "443311", "RefComponent": "RE", "RefTitle": "Enterprise extension SAP Real Estate: Technical basis", "RefUrl": "/notes/443311 "}, {"RefNumber": "1601903", "RefComponent": "RE-BD", "RefTitle": "No measurement results for Classic RE", "RefUrl": "/notes/1601903 "}, {"RefNumber": "1415175", "RefComponent": "RE-FX", "RefTitle": "Measurement tool: RE_AUDIT", "RefUrl": "/notes/1415175 "}, {"RefNumber": "313281", "RefComponent": "XX-SER-LAS", "RefTitle": "System measurement: Measurement of SAP Real Estate", "RefUrl": "/notes/313281 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}