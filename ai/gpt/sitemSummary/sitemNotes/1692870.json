{"Request": {"Number": "1692870", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 466, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000010057842017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001692870?language=E&token=14ECB1D2AE5CB17D2E33D523125D562F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001692870", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1692870"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "19.10.2015"}, "SAPComponentKey": {"_label": "Component", "value": "SCM-APO-INT-CON-CDP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Configuration CDP"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Supply Chain Management", "value": "SCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Advanced Planning and Optimization", "value": "SCM-APO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-APO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Interfaces", "value": "SCM-APO-INT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-APO-INT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Configuration", "value": "SCM-APO-INT-CON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-APO-INT-CON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Configuration CDP", "value": "SCM-APO-INT-CON-CDP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-APO-INT-CON-CDP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1692870 - X:MESSAGE_TYPE_X processing RBA subitems with CDP values"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>When copying the results of a rules based ATP (RBA) check from the SCM system into the calling sales document,&#x00A0;&#x00A0;a runtime error with short dump containing:<br /><br />MESSAGE_TYPE_X<br /><br />Message class....... \"CUX1\"<br />Number.............. 009<br /><br />\"MESSAGE_TYPE_X\" \" \"<br /> \"SAPLCUXI\" or \"LCUXIFO3\"<br /> \"CUXIX_SET_SINGLE_CFG\"<br /><br />occurs.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>1. For the main item material a characteristic dependent planning (CDP) scenario has been set up with the SCM system.<br />and<br />2. The solution 453921A from note 453921 is active for this item and<br />the main item characteristics have been copied into more than one<br />sub item.<br />and<br />3. In the ERP system you have either the modifying solution from note 582435 active or you activated 'Copying of Characteristic Values for Sub-Items' from the ECC-DIMP layer in the IMG at:<br /><br />Sales and Distribution<br />-&gt; Basic Functions<br />&#x00A0;&#x00A0;&gt; Availability Check and Transfer of Requirements<br />&#x00A0;&#x00A0; &gt; Availability Check<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&gt; Rule-based Availability Check<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; &gt; Copying of Characteristic Values for Sub-Items<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>In the attached correction instruction you will find a follow up correction for the userexit implementation suggested in note 453921.<br /><br />The existence of this note and of this follow up correction does not imply, that the ECC-DIMP functionality 'Copying of Characteristic Values for Sub-Items' is supported by SAP (c.f. e.g. note 865969).<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SCM-APO-ATP (Global Available-to-Promise)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D021946)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I038389)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001692870/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001692870/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001692870/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001692870/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001692870/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001692870/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001692870/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001692870/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001692870/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "865969", "RefComponent": "IS-ADEC", "RefTitle": "Current release restrictions for ERP 2005 - ECC-DIMP", "RefUrl": "/notes/865969"}, {"RefNumber": "731290", "RefComponent": "IS-A", "RefTitle": "Current release restrictions for ECC DIMP 5.0", "RefUrl": "/notes/731290"}, {"RefNumber": "582435", "RefComponent": "IS-MP-SD", "RefTitle": "M: R/3: CDP char. in RBATP sub items", "RefUrl": "/notes/582435"}, {"RefNumber": "453921", "RefComponent": "SCM-APO-ATP", "RefTitle": "X: char. value, batch no., copy acc. assgnmt to RBATP item", "RefUrl": "/notes/453921"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "865969", "RefComponent": "IS-ADEC", "RefTitle": "Current release restrictions for ERP 2005 - ECC-DIMP", "RefUrl": "/notes/865969 "}, {"RefNumber": "731290", "RefComponent": "IS-A", "RefTitle": "Current release restrictions for ECC DIMP 5.0", "RefUrl": "/notes/731290 "}, {"RefNumber": "582435", "RefComponent": "IS-MP-SD", "RefTitle": "M: R/3: CDP char. in RBATP sub items", "RefUrl": "/notes/582435 "}, {"RefNumber": "453921", "RefComponent": "SCM-APO-ATP", "RefTitle": "X: char. value, batch no., copy acc. assgnmt to RBATP item", "RefUrl": "/notes/453921 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SCM", "From": "400", "To": "400", "Subsequent": ""}, {"SoftwareComponent": "SCM", "From": "410", "To": "410", "Subsequent": ""}, {"SoftwareComponent": "SCM", "From": "415", "To": "415", "Subsequent": ""}, {"SoftwareComponent": "SCM", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SCM", "From": "510", "To": "510", "Subsequent": ""}, {"SoftwareComponent": "SCM", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SCM", "From": "701", "To": "701", "Subsequent": ""}, {"SoftwareComponent": "SCM", "From": "702", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SCM", "From": "712", "To": "712", "Subsequent": ""}, {"SoftwareComponent": "SCM", "From": "713", "To": "713", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SCM", "NumberOfCorrin": 1, "URL": "/corrins/0001692870/418"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SCM", "ValidFrom": "400", "ValidTo": "700", "Number": "453921 ", "URL": "/notes/453921 ", "Title": "X: char. value, batch no., copy acc. assgnmt to RBATP item", "Component": "SCM-APO-ATP"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "453921", "RefTitle": "X: char. value, batch no., copy acc. assgnmt to RBATP item", "RefUrl": "/notes/0000453921"}]}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}