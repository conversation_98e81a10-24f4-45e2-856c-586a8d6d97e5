{"Request": {"Number": "1146357", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 516, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016474942017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001146357?language=E&token=15C1C858D617B7715C37341478E01D0D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001146357", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001146357/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1146357"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.11.2008"}, "SAPComponentKey": {"_label": "Component", "value": "BW"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Business Warehouse"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1146357 - SAPBWNews BW SP23 NW'04 Stack 23 RIN"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Support Package 23 for BW Release 3.5</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SAPBWNEWS, Support Packages for 3.5, BW 3.5, BW 3.50, BW Patches</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This note contains the SAPBWNews for BW Release 3.5 Support Package 23. It contains a list of all notes that describe the corrections and enhancements in Support Package 23 (SAPKW35023). This note will be updated when other notes are added.<br /><br />The information is divided into the following areas:</p> <UL><LI><B>Manual actions that may be necessary:</B></LI></UL> <UL><UL><LI>Factors you must take into account when you import the Support Package</LI></UL></UL> <UL><UL><LI>Errors that may occur after you import the Support Package</LI></UL></UL> <UL><LI><B>General information:</B></LI></UL> <UL><UL><LI>Errors corrected in this Support Package</LI></UL></UL> <UL><UL><LI>Enhancements delivered with this Support Package</LI></UL></UL> <p><br />Support Package 23 for BW 3.5 corresponds to the technological status of Support Package 36 for SAP BW 3.0B (SAPBWNews 986817) and Support Package 30 BW 3.1 Content (SAPBWNews 986820).<br /></p> <b>Factors you must take into account when you import the Support Package:</b><br /> <p></p> <b>Errors that may occur after you import the Support Package:</b><br /> <p><br />- To date, no errors are known.<br /></p> <b>Errors corrected in this Support Package:</b><br /> <p></p> <b>Enhancements delivered with this Support Package:</b><br /> <p>Also see the documentation enhancements on SAP Service Marketplace (http://service.sap.com/bi --&gt; Documentation --&gt; SAP BW 3.5 Documentation Enhancements) which describe the enhancements that are delivered with this Support Package.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D031867)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D031867)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001146357/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001146357/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001146357/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001146357/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001146357/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001146357/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001146357/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001146357/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001146357/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1242957", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "F table of aggregate has over 50 partitions - RSCV 847", "RefUrl": "/notes/1242957"}, {"RefNumber": "1240694", "RefComponent": "BW-EI-APD", "RefTitle": "Extra delimiter in the header line when writing into File", "RefUrl": "/notes/1240694"}, {"RefNumber": "1240660", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Error generating authorizations: Inconsistency", "RefUrl": "/notes/1240660"}, {"RefNumber": "1240076", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Ref. point missing when condensing non-cumulative cube", "RefUrl": "/notes/1240076"}, {"RefNumber": "1239994", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Dump GETWA_NOT_ASSIGNED occurs in the form SP_AGGR_CS_07", "RefUrl": "/notes/1239994"}, {"RefNumber": "1235049", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "F4 help: Unauthorized data for referencing characteristic", "RefUrl": "/notes/1235049"}, {"RefNumber": "1234411", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Master data reorganization - Performance improvement", "RefUrl": "/notes/1234411"}, {"RefNumber": "1234075", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "<PERSON>aling does not work for cumulative values", "RefUrl": "/notes/1234075"}, {"RefNumber": "1228480", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 Brain in SAPLRRI2; form BEST_WGR_02-03-", "RefUrl": "/notes/1228480"}, {"RefNumber": "1227060", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "Report-report interface (RRI) in Web: Jump takes too long", "RefUrl": "/notes/1227060"}, {"RefNumber": "1177644", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Calculation before aggregation does not work", "RefUrl": "/notes/1177644"}, {"RefNumber": "1177497", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW 02 Prevent deletion of hierarchy tables", "RefUrl": "/notes/1177497"}, {"RefNumber": "1175354", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "RFC_MODIFY_R3_DESTINATION does not execute a COMMIT", "RefUrl": "/notes/1175354"}, {"RefNumber": "1174674", "RefComponent": "BW-EI-APD", "RefTitle": "APD: Extra delimiter when data is being written into File", "RefUrl": "/notes/1174674"}, {"RefNumber": "1172176", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction of minor usability problem", "RefUrl": "/notes/1172176"}, {"RefNumber": "1172047", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN 299 in SAPLRRK0, form CACHE_CREATE_NEW_FF-01-", "RefUrl": "/notes/1172047"}, {"RefNumber": "1171296", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Destination to the BW is not checked", "RefUrl": "/notes/1171296"}, {"RefNumber": "1170818", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Additional trace when deleting jobs and logs", "RefUrl": "/notes/1170818"}, {"RefNumber": "1169659", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Process variants not entered by BDLS", "RefUrl": "/notes/1169659"}, {"RefNumber": "1169074", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Authorization check with hierarchies takes too long", "RefUrl": "/notes/1169074"}, {"RefNumber": "1168893", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Adjusting SAP BI compression to Online Table Move", "RefUrl": "/notes/1168893"}, {"RefNumber": "1168770", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Text variable replaced by time-dependent characteristics", "RefUrl": "/notes/1168770"}, {"RefNumber": "1167260", "RefComponent": "BW-WHM-DST", "RefTitle": "P19: P23: DUMP: ITAB_DUPLICATE_KEY in the include LRSM1U23", "RefUrl": "/notes/1167260"}, {"RefNumber": "1167138", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Row compression in BI migrations from other platforms", "RefUrl": "/notes/1167138"}, {"RefNumber": "1166565", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Key displayed instead of text in text element", "RefUrl": "/notes/1166565"}, {"RefNumber": "1160835", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "The key figure definition does not work. BRAIN 415", "RefUrl": "/notes/1160835"}, {"RefNumber": "1160415", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Dumps in the function RSZ_P_DB_ELT_SET_IMMEDIATELY", "RefUrl": "/notes/1160415"}, {"RefNumber": "1160377", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "RSDV: Validity table maintenance incorrect", "RefUrl": "/notes/1160377"}, {"RefNumber": "1160150", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Termination RSDRI_INFOPROV_READ: Key figure \"floating point\"", "RefUrl": "/notes/1160150"}, {"RefNumber": "1159601", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "RRMX Gateway ISSUE", "RefUrl": "/notes/1159601"}, {"RefNumber": "1159373", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "Euro character not loaded during file upload", "RefUrl": "/notes/1159373"}, {"RefNumber": "1157604", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "BRAIN 813 -only allowed to display the hierarchy to level xx", "RefUrl": "/notes/1157604"}, {"RefNumber": "1157577", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "P18:MON: Reposting several packages in the monitor", "RefUrl": "/notes/1157577"}, {"RefNumber": "1155855", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correction: MESSAGE_TYPE_X during tree configuration", "RefUrl": "/notes/1155855"}, {"RefNumber": "1155783", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "System error in program SAPLRRK0 and form SET_LEVEL-01-", "RefUrl": "/notes/1155783"}, {"RefNumber": "1155740", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error in program SAPLRRK<PERSON> and form CUMUL_S_07-", "RefUrl": "/notes/1155740"}, {"RefNumber": "1155576", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "3.X:ODS: Rolling back old requests with new ODSR SID", "RefUrl": "/notes/1155576"}, {"RefNumber": "1153678", "RefComponent": "BW-BEX-ET-WEB-ITM", "RefTitle": "Error when you execute the Web item XMLQUERYVIEWDATA", "RefUrl": "/notes/1153678"}, {"RefNumber": "1153671", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Hierarchy name not displayed as key", "RefUrl": "/notes/1153671"}, {"RefNumber": "1151306", "RefComponent": "BW-BEX-ET-RT", "RefTitle": "Scaling cannot be displayed", "RefUrl": "/notes/1151306"}, {"RefNumber": "1151176", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P18:SDL:BAPI:Message RH 230 with '-' in hierarchy name", "RefUrl": "/notes/1151176"}, {"RefNumber": "1148496", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Check of limits of generated report", "RefUrl": "/notes/1148496"}, {"RefNumber": "1147442", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "F4 ignores D mode for 0UNIT infoobject. (3.x)", "RefUrl": "/notes/1147442"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1242957", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "F table of aggregate has over 50 partitions - RSCV 847", "RefUrl": "/notes/1242957 "}, {"RefNumber": "1240076", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Ref. point missing when condensing non-cumulative cube", "RefUrl": "/notes/1240076 "}, {"RefNumber": "1168893", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Adjusting SAP BI compression to Online Table Move", "RefUrl": "/notes/1168893 "}, {"RefNumber": "1166565", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Key displayed instead of text in text element", "RefUrl": "/notes/1166565 "}, {"RefNumber": "1148496", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Check of limits of generated report", "RefUrl": "/notes/1148496 "}, {"RefNumber": "1159601", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "RRMX Gateway ISSUE", "RefUrl": "/notes/1159601 "}, {"RefNumber": "1240694", "RefComponent": "BW-EI-APD", "RefTitle": "Extra delimiter in the header line when writing into File", "RefUrl": "/notes/1240694 "}, {"RefNumber": "1167138", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Row compression in BI migrations from other platforms", "RefUrl": "/notes/1167138 "}, {"RefNumber": "1170818", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Additional trace when deleting jobs and logs", "RefUrl": "/notes/1170818 "}, {"RefNumber": "1172047", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN 299 in SAPLRRK0, form CACHE_CREATE_NEW_FF-01-", "RefUrl": "/notes/1172047 "}, {"RefNumber": "1147442", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "F4 ignores D mode for 0UNIT infoobject. (3.x)", "RefUrl": "/notes/1147442 "}, {"RefNumber": "1234075", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "<PERSON>aling does not work for cumulative values", "RefUrl": "/notes/1234075 "}, {"RefNumber": "1177497", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW 02 Prevent deletion of hierarchy tables", "RefUrl": "/notes/1177497 "}, {"RefNumber": "1160150", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Termination RSDRI_INFOPROV_READ: Key figure \"floating point\"", "RefUrl": "/notes/1160150 "}, {"RefNumber": "1155783", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "System error in program SAPLRRK0 and form SET_LEVEL-01-", "RefUrl": "/notes/1155783 "}, {"RefNumber": "1160835", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "The key figure definition does not work. BRAIN 415", "RefUrl": "/notes/1160835 "}, {"RefNumber": "1227060", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "Report-report interface (RRI) in Web: Jump takes too long", "RefUrl": "/notes/1227060 "}, {"RefNumber": "1239994", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Dump GETWA_NOT_ASSIGNED occurs in the form SP_AGGR_CS_07", "RefUrl": "/notes/1239994 "}, {"RefNumber": "1235049", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "F4 help: Unauthorized data for referencing characteristic", "RefUrl": "/notes/1235049 "}, {"RefNumber": "1240660", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Error generating authorizations: Inconsistency", "RefUrl": "/notes/1240660 "}, {"RefNumber": "1234411", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Master data reorganization - Performance improvement", "RefUrl": "/notes/1234411 "}, {"RefNumber": "1169074", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Authorization check with hierarchies takes too long", "RefUrl": "/notes/1169074 "}, {"RefNumber": "1160415", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Dumps in the function RSZ_P_DB_ELT_SET_IMMEDIATELY", "RefUrl": "/notes/1160415 "}, {"RefNumber": "1168770", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Text variable replaced by time-dependent characteristics", "RefUrl": "/notes/1168770 "}, {"RefNumber": "1177644", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Calculation before aggregation does not work", "RefUrl": "/notes/1177644 "}, {"RefNumber": "1228480", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 Brain in SAPLRRI2; form BEST_WGR_02-03-", "RefUrl": "/notes/1228480 "}, {"RefNumber": "1175354", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "RFC_MODIFY_R3_DESTINATION does not execute a COMMIT", "RefUrl": "/notes/1175354 "}, {"RefNumber": "1169659", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Process variants not entered by BDLS", "RefUrl": "/notes/1169659 "}, {"RefNumber": "1171296", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Destination to the BW is not checked", "RefUrl": "/notes/1171296 "}, {"RefNumber": "1174674", "RefComponent": "BW-EI-APD", "RefTitle": "APD: Extra delimiter when data is being written into File", "RefUrl": "/notes/1174674 "}, {"RefNumber": "1172176", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction of minor usability problem", "RefUrl": "/notes/1172176 "}, {"RefNumber": "1167260", "RefComponent": "BW-WHM-DST", "RefTitle": "P19: P23: DUMP: ITAB_DUPLICATE_KEY in the include LRSM1U23", "RefUrl": "/notes/1167260 "}, {"RefNumber": "1153671", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Hierarchy name not displayed as key", "RefUrl": "/notes/1153671 "}, {"RefNumber": "1151306", "RefComponent": "BW-BEX-ET-RT", "RefTitle": "Scaling cannot be displayed", "RefUrl": "/notes/1151306 "}, {"RefNumber": "1153678", "RefComponent": "BW-BEX-ET-WEB-ITM", "RefTitle": "Error when you execute the Web item XMLQUERYVIEWDATA", "RefUrl": "/notes/1153678 "}, {"RefNumber": "1160377", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "RSDV: Validity table maintenance incorrect", "RefUrl": "/notes/1160377 "}, {"RefNumber": "1159373", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "Euro character not loaded during file upload", "RefUrl": "/notes/1159373 "}, {"RefNumber": "1155740", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error in program SAPLRRK<PERSON> and form CUMUL_S_07-", "RefUrl": "/notes/1155740 "}, {"RefNumber": "1155576", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "3.X:ODS: Rolling back old requests with new ODSR SID", "RefUrl": "/notes/1155576 "}, {"RefNumber": "1157577", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "P18:MON: Reposting several packages in the monitor", "RefUrl": "/notes/1157577 "}, {"RefNumber": "1157604", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "BRAIN 813 -only allowed to display the hierarchy to level xx", "RefUrl": "/notes/1157604 "}, {"RefNumber": "1155855", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correction: MESSAGE_TYPE_X during tree configuration", "RefUrl": "/notes/1155855 "}, {"RefNumber": "1151176", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P18:SDL:BAPI:Message RH 230 with '-' in hierarchy name", "RefUrl": "/notes/1151176 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "350", "To": "350", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "640", "To": "640", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}