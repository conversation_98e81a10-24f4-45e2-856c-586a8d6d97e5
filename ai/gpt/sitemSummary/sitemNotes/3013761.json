{"Request": {"Number": "3013761", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 274, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000224232021"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003013761?language=E&token=8B6191CF4750A15D0B28AFF0F9A9386E"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003013761", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3013761"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.02.2021"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-REL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Suite and SAP S/4HANA Release Information"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Suite and SAP S/4HANA Release Information", "value": "XX-SER-REL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-REL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "3013761 - SAP S/4HANA 2020FPS1: Performance Restrictions"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note provides information about performance release restrictions for SAP S/4HANA 2020 FPS1</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>When SAP S/4HANA 2020 FPS1 was released, restrictions applied to the productive use of certain functions from a performance point of view (SAP wishes to inform customers of these restrictions here).</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>In General, the below mentioned Apps/SAPGUIs show higher than expected response times for one or more user interaction steps. The resolution of these issues is planned with further SAP S/4HANA 2020FP Feature Packs unless stated otherwise.</p>\r\n<p>In General, the below mentioned Apps/SAPGUIs show higher than expected response times/HANA CPU time/Application Server CPU time for one or more user interaction steps. The resolution of these issues is planned with further SAP S/4HANA On Premise 2020 OP Feature packs and CE 2105 releases unless stated otherwise</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p><strong>APP ID</strong></p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p><strong>Fiori App Name</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p><strong>Performance Violations</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F1901</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Billing Document</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>For the step: 01_SearchBillingDocument, the HANA CPU time is 1s&gt;0.15</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F3412</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Configure Score Calculation for Products</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>E2E response time in WAN for Add_Rule is 3.4s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>W0154</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Contract Balance Movements</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>E2E response time in WAN for both steps is &#126;11s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2363</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Analyze Installment Plans</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>For the step :01_Open_app,Estimated end to end response time in WAN is 6.3sec&gt;3 and&#160; HANA CPU time 12.5s &gt; 5</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F1239</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Analyze Payment Locks</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>For the step :05_Business_Partner_View,the HANA CPU time: 56s; 02_Change Chart View</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F4040</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Payments Analyzer</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>For the step : 01_Open_App,&#160; the End to end response time is 14s &gt; 3 and HANA CPU time is 88s&gt;0.35&#160; 02_Enter : the End to end response time is 12s &gt; 3 and HANA CPU time is 87s&gt;0.35</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F3183</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Exchange Agreement Sales KPI</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Click_on_analyze_button has 3 Seq Roundtrips</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F3184</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Exchange Agreement Purchase KPI</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Click_on_analyze_button has 3 Seq Roundtrips</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F4115</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Manage Force Element - Maintenance</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated response time in WAN for 03_Save_Create is 6500 ms</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F3123</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Display Treasury position values for expected losses</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>E2E response time in WAN for below steps are violated:</p>\r\n<p>Click on Go : 19s</p>\r\n<p>Add filter and Search :16s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F4569</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Configure Supply Protection</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>E2E Response Time in WAN of Navigation Step takes 6 sec(threshold 3 sec)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F4643</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Pick Products</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>For the step :02_Start_picking, E2E Response Time in WAN is 5.5s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2814_7</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Manage KPI and Reports - Save Application</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>For the step :Click on OK ,the E2E Response Time in WAN is 5.3s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F3424_B</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Billing and Invoicing Analysis</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>HANA CPU time for all 3 steps have violations: <br /> 02_filter_NoofContbyInvoicingProgress_Click_NotYetInvoiced&#160; - 8s<br /> 03_filter_NoofContsbyInvoibyStatus_Click_NotReadyforInvoing-&#160; 9.8s<br /> 04_filter_NoofContsbyBillingStatus_Click_MissingBillOrder -&#160;&#160;&#160;&#160;&#160;&#160; 11s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F4673_1</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Model Production Network</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>For Step : 02_Select_one _order,&#160; E2E Response Time in WAN is 13s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2869</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Production Action Log</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>For the step:03_ClickDetails, HANA CPU Time is 1.7s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2017</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Purchase Requisition To Order CycleTime</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>HANA CPU Time is 8s and 4s for 2 steps</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2539</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Overdue Receivables by Risk Class</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>HANA CPU Time is 18s and 8.9 for 2 steps</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2762</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Manage Budget Transfers</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>For the steps 05_Create_budget_doc, E2E Response Time in WAN is 3 sec instead of 1 sec</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F4969</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Warehouse Outbound Delivery Orders</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p><br /> E2E Response Time in WAN for Open app is&#160; 6.4s &gt;3 and HANA CPU Time is 2s&gt;0.35</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F0584A</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>My Travel and Expenses (Version 2)</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>E2E Response Time in WAN for the Steps 02_Create; 03_Save ; 06_Save_after_Edit is 5.2s; 5.4s and 8.2s &gt; 3s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F4535</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Plan Supply to Production</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>For step 1 and 3, HANA CPU Time is 6.3s;4.5s &gt; 0.35 respectively</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F1970</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Network Activity Overview</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>E2E Response Time in WAN for step: Click on Smart Link is 6s. Threshold:3</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F3302</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Reconcile GR/IR Accounts</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>HANA CPU time is 2s for Open app step</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F4733</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Display Statusboard</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p><br /> For step 1,2 and 3, High HANA CPU Time observed in 2.4s;1.9s;2.8s &gt; 0.35 respectively</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F0869A</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Create Outbound Deliveries - From Sales Orders</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>E2E Response Time in WAN for the step: 03_Create deliveries for sales order is 8.2s &gt;3</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F4909</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Initiate EBOM to MBOM Handover with Engineering Snapshot</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Performance violation of the Step 08_ClickOk on Estimated End To End Response Time in WAN [s] :5.6 &gt;3 s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F1602</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Product Master Create product</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>For the step: Click OK_Step, Estimated end to end response time in WAN is 5.9sec&gt;3</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F4662</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Manage Allowance Plans</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>HANA CPU time for Save step is 1.5&gt;0.35s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F4762</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Track Tool Usage</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>E2E response time in WAN is 1.7s&gt;1 and HANA CPU time is 2s &gt;01.15</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F4673_2</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Model Production Network</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>End to end response time in WAN is 34s and 33s for 2 steps</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F4639_1</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Handover Products</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>End to end response time in WAN is 13.5s for 1 step</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F4673_3</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Model Production Network</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p><br /> End to end response time in WAN is 18s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F4053</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Show SARA Relevant Stock Details</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>High End-End response time in WAN and&#160; more number of sequential roundtrips.<br /> <br /> Step: 01_Open App<br /> Estimated Response Time in WAN [s] : 4.1 (threshold =1 sec)<br /> Number of Sequential Roundtrips : 9 (threshold = 1)<br /> Step: 02_Click Go<br /> <br /> Estimated Response Time in WAN [s] : 8.363 (threshold =3 sec)<br /> Number of Sequential Roundtrips : 3 (threshold = 2)<br /> <br /> Step: 03_Click on export to spread button<br /> Estimated Response Time in WAN [s] : 5.375 (threshold =3 sec)<br /> Number of Sequential Roundtrips : 9 (threshold = 2)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F1744</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Future Receivables</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>High HANA CPU time violation: 01_Open_App- 12.179s &gt; 5s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2182</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Commercial Projects-Single-Project Overview</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p><br /> Estimated response time and HANA CPU time for step 001_Open_workspace 15.855 &gt;3 and 90.687&gt; 0.35 respectively<br /> Estimated response time and HANA CPU time for step 004_Alerts 5.221&gt; 3 and 38.594&gt; 0.35 respectively</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2133</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Multi Project Overview</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated End to end response time of Open_app is 10s&gt;1 and HANA CPU time takesn is 29s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F1747</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Overdue receivables</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Hana CPU Time violations: 01_Open_App : 14.662s&gt; 5s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F0860</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Analyze Overdue Items</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>E2E response time in WAN is 26 sec for \"Display Business Partners\" step</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F4509</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Manage JIT Calls for Supply to Production</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>HANA CPU time is 1.29s for Save step.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2664</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Analyze Change Impact</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated End To End Response Time in WAN for step analyze Change impact is 12s &gt;3</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F4085</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Manage Rule Mining Runs for Products</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated End To End Response Time in WAN for Save step is 5.7s&gt;3 and HANA CPU time is 1s&gt;0.35</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F4242</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Settle Route Data</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>HANA CPU Time is 1.4s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F5198B</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Maintain ACM Contracts</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated End To End Response Time in WAN for Step = 03_Click_Edit with Complex Common is 4.5s.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F5197</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Manage ACM Trading Contract Snapshots</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>HANA CPU time violation for Step 02_Click_Go = 0.963s &#160;with Simple complexity should be &lt;=150 ms , and HANA CPU time for step : 03_Click_Row1 = 2.956 with Complex Common complexity should be &lt;=350 MS navigation to display different facets</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F5130</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Network Graph for Projects</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated End To End Response Time in WAN for Step : navigating back from Network Overview is 5.4s and 5 Seq RTs</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2097</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Manage Engineering Changes</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated End To End Response Time in WAN for Step : Select Regulated Industry Change is 7.3s &gt;1 and Step Click create takes &#126;6.2s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2256A</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Manage Quality Info Records in Procurement</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated End To End Response Time in WAN [s]##01_Open_App 6.5 sec &gt;3 <br /> <br /> Database HANA CPU Time [s]##01_Open_App 167s &gt;0.35</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F1515_NL</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Run Compliance Reports / Netherlands GL Reporting / Pending Reports</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Application Estimated Response Time is 13.5s and STAD CPU time is 6.3s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F1515_NO_1</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Run Compliance Reports / Norway AP Reporting / Pending Reports</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Application Estimated Response Time is 10s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F5049_A</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Prepayment Workcenter</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response Time in WAN [s] for 03_Click on Save takes 44.8 secs and HANA CPU time taken is 264s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F5328_C</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Maintain ACM Pricing</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>05_Click on Save = Application is tested many times and From F12 trace I observed SAVE step taking more than 1.7 min because of which SUPA could not capture the measurement values</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F0867A</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Manage Outbound Deliveries</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response Time in WAN [s] for Post Step is&#160; 5.6sec &gt;3</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F5198A</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Maintain ACM Contracts</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Violation with Step 02_Click_Go , Complex Common where after the open app , user clicks on GO without specifying any filter criteria ( empty search). HANA CPU = 2.074 sec which should be &lt;=350 ms</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F5328_A</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Maintain ACM Pricing</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>05_Click on Save = Application is tested many times and From F12 trace I observed SAVE step taking more than 1.7 min because of which SUPA could not capture the measurement values</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F5328_B</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Maintain ACM Pricing</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>05_Click on Save = Application is tested many times and From F12 trace I observed SAVE step taking more than 1.7 min because of which SUPA could not capture the measurement values</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F0232A</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>RO Object Page Inbound Delivery</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Roundtrips -4 (Complex Step)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F0233A</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>R/O Object Page Outbound Delivery</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>E2E Response time in WAN is 5.8 s (Complex step)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F0234A</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>R/O Object Page Returns Delivery</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Roundtrips -4 (Complex Step)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F0251</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Manage Material Coverage</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN- 2.8s [HANA CPU Time -1.4s]</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F0673A</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Approve Bank Payments</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>E2E Response time in WAN is 9.8 s (Complex step)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F0997</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Audit Journal</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>&#160;Estimated Response Time in WAN-15.5s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F1249</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Incoming Sales Orders</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>E2E Response time in WAN-5.5</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F1483</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Schedule Actual Assessment For CO-PA</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN-4.9s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F1643</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Create Purchase Requisition</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN- 2.1 s [HANA CPU Time -4.3 s]</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F1814</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Sales Order</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN-3.2s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2019</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Overall Supplier Evaluation</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>E2E Response time in WAN is 5 s (Complex step)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2023</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Report_and_Repair_Malfunction</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN-5.9s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2129</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Revenue Recognition (Event-Based) &#8211; Projects</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN- 2.4 s [HANA CPU Time -2s]</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2173</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Find Maintenance Order and Operation</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN-11.3s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2175</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Find Maintenance Order</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN-4.5s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2358</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Monitor Purchase Order Items</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN- 2.8 s [HANA CPU Time -12 s]</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2465</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Schedule Material Availability Check</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN-6.2s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2603</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Maintenance Scheduling Board</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN-1.1 s [HANA CPU Time -3.5 s]</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2647</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Unpackaged Product - Basic Compliance Data</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN-4.7s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2651</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Manage Customer Returns - Create</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN-5.3s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2652</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Manage Customer Returns - Refund</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN- 3.2s [HANA CPU Time -1.6 s]</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2665</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Schedule BOP Run</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN-3.6s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2680</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Manage Material Valuations</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN-4.3s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2776</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Schedule Goods Receipt for Inbound Deliveries</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>&#160; Estimated Response Time in WAN-4.1 s [HANA CPU time 11s]</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2798</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Schedule Inbound Delivery Creation</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN-4.4s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2828</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>EAM Overview Page for Maintenance Planner</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>E2E Response time in WAN -20s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2925</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Cash Collection Tracker</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN-7.5s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2971</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Posting Journal Entry</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN-10s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F3008</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Manage Customer Just-In-Time Call</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN-5.9s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F3172</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Manage Tax Items for Legal Reporting</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN- 3.5s [HANA CPU Time -2.2 s]</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F3268</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Schedule Import of Purchasing Documents</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN- 4.9s [HANA CPU Time -34s]</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F3282</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Buffer Positioning</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN- 3 s [HANA CPU Time -1.3 s]</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F3290</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Manage Purchase Requisitions Centrally</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN-4.9s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F3292</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Manage Purchase Orders Centrally</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN- 2.4 s [HANA CPU Time -1.2 s]</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F3298</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Expected Goods Receipt</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN-3.9s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F3331</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Analyze Costs by Work Center/Operation</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN- 2.2 s [HANA CPU Time -10 s]</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F3555</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Manage Bank Statement Reprocessing Rules</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response Time in WAN-3.4s [HANA CPU time 3.2s]</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F3607</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Service Management &#8211; Overview</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN-6.9s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F3627</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Detect Abnormal Liquidity Items</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN- 2.1 s [HANA CPU Time -1.9 s]</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F3669</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Inspect WIP/Variance Posting</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN- 1.7 s [HANA CPU Time -2.2 s]</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F3942</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Service Contracts Analysis</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN-7.3s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F3951</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Monitor Work Center Schedules</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response Time in WAN-6.1s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F4364</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Analyze Marketability Assessments</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>E2E Response time in WAN-9.4</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F4485</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Manage Interest Runs</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>E2E Response time in WAN-3.5</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F4603</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Maintenance Order Costs</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN- 3.5s [HANA CPU Time -14 s]</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F4793</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Resolve Payment Card Issues &#8211; Reauthorizations</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN-4.6s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F4838</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Create Payment Requisition</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN-5.1s</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F4864</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Manage Chemical Data</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>Estimated Response time in WAN- 1 s [HANA CPU Time -2 s]</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"101\">\r\n<p>F2229</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"top\" width=\"427\">\r\n<p>Manage Purchase Requisitions</p>\r\n</td>\r\n<td valign=\"top\" width=\"1332\">\r\n<p>For the Step Click on Go the Estimated Response time in WAN is&#160;3s.</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 957px; height: 161px;\">\r\n<tbody>\r\n<tr>\r\n<td valign=\"top\" width=\"66\">\r\n<p>F4743</p>\r\n</td>\r\n<td valign=\"top\" width=\"161\">\r\n<p>Route Overview</p>\r\n</td>\r\n<td valign=\"top\" width=\"400\">\r\n<p>A number of more than 5,000&#160;routes / 150,000 visits (in total) can lead to high performance violations and/or Hana Out of Memory exceptions</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"66\">\r\n<p>F4742</p>\r\n</td>\r\n<td valign=\"top\" width=\"161\">\r\n<p>Route Document Flow</p>\r\n</td>\r\n<td valign=\"top\" width=\"400\">\r\n<p>A number of more than 5,000&#160;routes / 150,000 visits (in total) can lead to high performance violations and/or Hana Out of Memory exceptions</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"66\">\r\n<p>F4747</p>\r\n</td>\r\n<td valign=\"top\" width=\"161\">\r\n<p>Collected Payments</p>\r\n</td>\r\n<td valign=\"top\" width=\"400\">\r\n<p>A number of more than 5,000&#160;routes / 150,000 visits (in total) can lead to high performance violations and/or Hana Out of Memory exceptions</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I054233)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I054233)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003013761/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003013761/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003013761/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003013761/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003013761/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003013761/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003013761/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003013761/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003013761/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2916959", "RefComponent": "XX-SER-MCC", "RefTitle": "Fiori Performance Troubleshooting", "RefUrl": "/notes/2916959 "}, {"RefNumber": "2943206", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 2020: Restriction Note", "RefUrl": "/notes/2943206 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}