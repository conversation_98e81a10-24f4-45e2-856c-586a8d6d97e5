{"Request": {"Number": "114814", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 491, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014590882017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=6C9499E393F58F7671142417A3F8A918"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "114814"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 108}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.08.2022"}, "SAPComponentKey": {"_label": "Component", "value": "FI-GL-GL-M"}, "SAPComponentKeyText": {"_label": "Component", "value": "Ext. Interfaces/BAPIs/ALE"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Accounting", "value": "FI-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "FI-GL-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Ext. Interfaces/BAPIs/ALE", "value": "FI-GL-GL-M", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-GL-M*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "114814 - <PERSON><PERSON> FIDCCP01/02 and FAGLDT01/FAGLST01: Questions and problems in FI distribution"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note addresses some questions and possible solutions regarding the data distribution via ALE and in particular, the distribution of FI documents.<br /><br />This concerns the following:</p>\r\n<ul>\r\n<li>IDoc basis type</li>\r\n<ul>\r\n<li>FIDCCP01 and the successors FIDCCP02 and FAGLDT01 (as of ECC 6.0 EHP 5) and the</li>\r\n</ul>\r\n<li>Message types</li>\r\n<ul>\r\n<li>FIDCC1 -complete FI documents</li>\r\n<li>FIDCC2 -complete FI documents</li>\r\n<li>FAGLDT01 - complete FI documents (General Ledger Accounting (new) as of ECC 6.0 EHP 5)</li>\r\n</ul>\r\n</ul>\r\n<p>The FI ALE scenario with the message types mentioned above is delivered for the following standard SAP scenario: An ERP SAP system group contains one or more SAP sender systems as FI shadow systems and exactly one SAP receiver system as the central system for FI. As soon as you activate a distribution model with the message types mentioned above, the FI system responds in accordance with this scenario.</p>\r\n<ul>\r\n<li>In the sender systems, applications such as MM, SD, HR, Asset Accounting, HR, and so on that generate FI documents are implemented.</li>\r\n<li>The FI IDoc transfers the FI documents to the receiver system where the subsequent FI processes (for example, payment, dunning, valuation, closing processes, balance carryforward, and advance return for tax on sales/purchases) are carried out. An exception is the message type FIDCMT where the advance return for tax on sales/purchases is carried out in the sender system. From the subsequent processes in the receiver system, there is no retransfer of FI documents or other information (for example, OP clearing information) to the sender systems. Before you introduce FI ALE, decide together with your FI consultants and your FI team in which system the subsequent FI processes should be executed.</li>\r\n<li>In addition, message type FIDCCH (IDoc FIDCCH01) can be used to send particular document changes from MM, such as a payment block or dunning block, from the decentralized sender systems to the central receiver system in real time.</li>\r\n</ul>\r\n<p>The FI IDoc transports only FI data. It is therefore suitable only in exceptional cases for the generation of documents in other applications (for example, CO, Profit Center Accounting, or Special Purpose Ledger) in the receiver system in addition to the FI document. Each of these applications has its own ALE scenarios with own message types and IDoc types to send its data. We recommend using the ALE scenarios of the relevant application.</p>\r\n<p>We strongly advise against using FI IDocs for purposes that deviate from the FI ALE scenario, such as external data transfer, copying FI documents to other systems that are not central FI systems according to the SAP standard, or sending FI documents to the own logical system.&#x00A0;This may lead to an unexpected system or even data inconsistencies in FI. The FI ALE function modules are also programmed exactly for the standard scenario and are not suitable for customer-specific scenarios or customer programs.<br />Note the following: The solution of any subsequent problems that occur when you use FI IDocs outside of the standard FI ALE scenario is not part of SAP Support. Contact your consultant in such a case.</p>\r\n<p>Below, FIDCCP01 and FIDCCP02 are referred to as \"IDocs\". This is the same as \"IDoc basis type\" FIDCCP01 or FIDCCP02.</p>\r\n<p><strong>The function of message types FIDCC1 and FIDCC2 is kept.</strong><br /><strong>Message types FIDCC1 and FIDCC2 have practically the same function - they are equal to one another.</strong><br /><strong>FIDCC1 and FIDCC2 can be used with the new IDoc basis type FIDCCP02 as well as with IDoc basis type FIDCCP01.</strong></p>\r\n<p>The IDoc FIDCCP02 sends additional table fields, which are required especially for extended withholding tax. This added function is possible with message types FIDCC1 and FIDCC2.<br /><br /> -------------------------</p>\r\n<p><strong>Brief documentation about the distribution options with ALE in FI</strong></p>\r\n<p><br />Below you find some basic remarks regarding the distribution of FI data using the ALE functionality:<br /><br />If FI ALE is active (with an FI ALE distribution model), an IDoc is created each time an FI document is posted in an <strong>SAP sending system</strong>. Thus, ALE distribution is triggered (with the exception of the message type FIROLL). The activation of an FI ALE distribution model is the prerequisite for the correct distribution of FI documents.&#x00A0;The distribution of FI data is connected to the FI document and happens in real time when an FI document is posted in the decentralized system (sender system).&#x00A0;In the decentralized system, both the general ledger and subledger (the FI shadow system) and all other active components, such as CO, the classic profit center accounting, or the Special Purpose Ledger are updated.<br />In the central system (receiver system), the IDoc is received, processed, and transferred to the Accounting Interface for posting in the component FI. This creates an FI document with an update in the general ledger and subledger. An update of other components, such as CO, classic Profit Center Accounting, or Special Purpose Ledger is not triggered by the FI IDocs. For this purpose, there are separate IDocs in the respective component. We recommend that customers use the ALE scenarios of the relevant component to distribute documents from other components. Problems with the updating of documents from other components using the FI IDoc cannot be processed as part of standard support.<br /><br />The sent IDoc contains all information from the original document, such as tax amounts or amounts in other currencies. In the central system, no tax routines, check routines, or other routines for data derivation are processed; this means that the IDoc must contain all necessary data for the posting of the FI document in the receiver system. This is guaranteed as long as there is ALE connection between SAP ERP systems (an ERP-ERP link). This also ensures compatibility between different release levels. The standard FI ALE scenario supports SAP system groups with one or more SAP sending systems as FI shadow systems and exactly one receiver system as a central FI system.</p>\r\n<p><strong>The following scenarios are not standard SAP scenarios, which means that any problems that occur with these scenarios are not part of SAP Support.</strong></p>\r\n<p><br />a) The sender system is not an SAP system. In other words, there is an external data transfer.</p>\r\n<p style=\"padding-left: 30px;\">Each posting represents a business transaction. In an SAP sender system, all relevant functions know how they transfer the data to FI or to the IDoc, which not only ensures that the IDoc can be posted successfully in the receiver system, BUT that all subsequent processes in connection with this posting also work without errors. Therefore, in case of a customer-specific use of FI IDocs for the external data transfer, you must ensure that all IDoc fields are filled correctly. Please be advised that SAP Support cannot accept any responsibility for incorrect postings and subsequent errors.</p>\r\n<p>b) The receiver system is an external non-SAP system.<br />c) An SAP system is both the sender and receiver of FI ALE message types (FIDCC1, FIDCC2, FAGLDT01, FIDCMT, FAGLST01, FIROLL).<br />d) ALE is in a separate system (an FI ALE message is sent to its own logical system).<br />e) Customer-specific function modules are used in inbound processing.<br />f) Standard SAP function modules are used in customer programs.<br />g) The standard SAP basic types FIDCCP01, FIDCCP02, FAGLDT01, FIDCCH01, and FIDCMT01 or the standard SAP messages FIDCC1, FIDCC2, FAGLDT01, FIDCMT, FAGLST01, FIROLL, and FIDCCH are used for customer-specific applications and programs.<br />h) The available user exits, BAdIs, BTEs, and substitution and validation functions are used to change the standard behavior, which is the responsibility of the user. Inconsistent changes and their consequences are not included in the standard scope of SAP Support.</p>\r\n<p>Activation of the ALE message types is effected for all FI postings of a client (BD64). By means of a data filter, ALE activation can be restricted to certain (global) company codes. In this case, only the FI documents of this company code are sent. If no data filter is set, all FI postings within a client are sent.<br />With numerous user exits within the ALE FI, the user can influence the sending (yes/no) and the data contents of the IDoc. See transaction SMOD, SAP enhancements F050S001 through F050S007 for the basic types FIDCCP01/02.<br />For IDocs of the type FAGLDT01, BAdIs are available for this purpose. They can be found in transaction SALE at <em>Modelling and Implementing Business Processes -&gt; Configure Predefined ALE Business Processes -&gt; Accounting -&gt; AC &lt;-&gt; AC -&gt; Set Up Distribution of Financial Accounting Data (New) -&gt; Business Add-Ins (BAdIs)</em>.<br /><br /></p>\r\n<p><strong>FI ALE scenarios for classic general ledger accounting</strong></p>\r\n<ol>1. Distribution of G/L-relevant data using IDoc basis type FIDCMT01</ol>\r\n<p><br />In this scenario, the data is distributed via the message types FIDCMT and FIROLL.&#x00A0;Both message types form a unit and are used simultaneously.<br />You can use the \"Account Managed in External System\" indicator in the G/L account master record to control whether the data of the G/L account posted to is sent immediately when you post an FI document (FIDCMT) or whether the account balance from the general ledger is sent periodically (FIROLL with transaction GAL3). Thus, the \"external accounting information\" indicator is another filter and control option for the FIDCMT and FIROLL message types when you send data of a particular account.<br />If you use both message types correctly, this ensures that all G/L data is distributed completely, that is, it is sent completely to the central system. The total from the general ledgers of all decentralized sender systems corresponds to the total from the general ledger of the central receiver system.<br /><br />Important:<br />Since in this scenario, only the selected line items and not the complete FI document exist in the receiver system, there are restrictions regarding the functions that can be used in the receiver system. The IDoc FIDCMT01 is useful if it is sufficient to send G/L-relevant data without auxiliary account assignments, or to send the summarized transaction figures using FIROLL. This could be the case, for example, if only a few selected operational processes such as payment and dunning take place in the central system.<br />For further information about this scenario, see SAP Note 47410.<br /><br /></p>\r\n<ol><ol>2. Distribution of complete FI documents using IDoc basis type FIDCCP01 or FIDCCP02</ol></ol>\r\n<p><br />As an alternative to the distribution of individual document line items using the IDoc basic type FIDCMT01, there is the FI ALE scenario for the distribution of complete FI documents using the IDoc basic type FIDCCP01 or FIDCCP02. Either the ALE message type FIDCC1 or FIDCC2 can be used for that purpose. Both message types support the same function.<br />By activating message type FIDCC1 or FIDCC2, the complete FI document with all line items is sent. The IDoc structure of FIDCCP01/02 with its segments therefore also corresponds to that of the FI document. The FI document posted in the central receiver system is identical with the FI document of the sender system. The asset posting lines are an exception.<br /><br />Compared to the IDoc FIDCMT01, the distribution of complete FI documents using the IDoc FIDCCP01 or FIDCCP02 has the advantage of a larger range of functions in the receiver system.<br />We therefore recommend using the IDoc basic type FIDCCP01 or FIDCCP02 for the corresponding data constellation. However, when using the IDoc FIDCCP01 or FIDCCP02, you must take into account the increased data volume.&#x00A0;We recommend that you execute suitable performance measurements in the test system before you activate the FI ALE scenario in the production system.<br /><br />The IDocs FIDCCP01 and FIDCCP02 can be used to implement the following functions (not possible with the IDoc FIDCMT01):</p>\r\n<ul>\r\n<li>central advance return for tax on sales/purchases</li>\r\n</ul>\r\n<ul>\r\n<li>extended withholding tax</li>\r\n</ul>\r\n<ul>\r\n<li>document splitting <br />(considerable restrictions required when using the business processes to be displayed)</li>\r\n</ul>\r\n<ul>\r\n<li>vendor net procedure</li>\r\n</ul>\r\n<ul>\r\n<li>General Ledger Accounting (new) [FI-GL (new)]</li>\r\n</ul>\r\n<ul>\r\n<li>ALE remote comparison between sender system and receiver system (see SAP Note 517580)</li>\r\n</ul>\r\n<p><br />As already mentioned, you can activate either the first scenario with the message types FIDCMT and FIROLL (IDoc FIDCMT01) or the second scenario with the message types FIDCC1 or FIDCC2 (IDoc FIDCCP01/FIDCCP02). If message types from both scenarios have been activated by mistake, the message type FIDCC1 or FIDCC2 has a higher priority than the message types FIDCMT and FIROLL. This means that complete FI documents are sent in this case. The system does not issue a warning message or error message.</p>\r\n<p><strong>FI ALE scenarios for General Ledger Accounting (new) [FI-GL (new)]</strong></p>\r\n<p>Especially for General Ledger Accounting (new), as of EHP5 there is an ALE distribution scenario for transferring documents with the two message types FAGLDT01 and FAGLST01.</p>\r\n<p>For details, see the online help.</p>\r\n<p>--------------------------------</p>\r\n<p><strong>Questions concerning the distribution of complete FI documents, what is or is not possible, and what must be taken into account:</strong></p>\r\n<p>The following explanations for FIDCCP02 also apply for the message type FAGLDT01. Differences are mentioned explicitly.</p>\r\n<ol>1. How is the tax information processed?</ol><ol>2. Creation of the IDoc basic type FIDCCP02 from an external interface - external data transfer</ol><ol>3. Cross-company code documents</ol><ol>4. Sending data to all applications</ol><ol>5. Extended withholding tax&#x00A0;</ol><ol>6. Are sent documents cleared automatically?</ol><ol>7. How are assets treated?</ol><ol>8. How do you add fields to an IDoc?</ol><ol>9. Does substitution work in the receiver system?</ol><ol>10. Problem (up to Release 4.5A) for the changeable credit control area KKBER</ol><ol>11. How does the credit limit check work in decentralized systems if payments are made in the central systems?</ol><ol>12. Is the invoice reference also possible in the central system?</ol><ol>13. Use of SAP enhancements and user exits</ol><ol>14. Are special G/L transactions possible?</ol><ol>15. Is document reversal possible?</ol><ol>16. Can accrual/deferral documents be sent?</ol><ol>17. How are the reference fields AWSYS, AWTYP, and AWKEY filled?</ol><ol>18. Is data comparison between the sending and receiver systems possible?</ol><ol>19. Is payment card processing possible?</ol><ol>20. Global company code and ALE filter object</ol><ol>21. Can the accounts be different in the sending system and receiver system - account conversion?</ol><ol>22. Exceptions: Document fields that are filled by the accounting interface</ol>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>ALE IDoc FIDCCP01 FIDCCP02 FIDCC1 FIDCC2 CRESTA complete FI documents RFUMSV00 IDOC_INPUT_FIDCC1 IDOC_INPUT_FIDCC2<br />FIDCMT01 FIDCMT FIROLL GAL3 FIDCCH rollup transaction figures<br />Global company code BUKRS_GLOB T001O F1302<br />AUGBL extended withholding tax<br />CO-PA E1FISEG-PAOBJNR PAOBJNR<br />RGUCOMP4 GCAR reconciliation report ALE comparison tool comparison ledger<br />TRWPR TRWPRC subset GALE COFI F002 F001 GAL1</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Implementation of ALE FI, distributed systems </p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ol>1. How is the tax information processed?</ol>\r\n<p>---------------------------------------------------------<br /><br />When complete FI documents are sent, all tax information is also sent in the same IDoc. Primarily, the complete BSET table is included in the E1FISET IDoc-segment, which contains the most important tax information. In addition, when you use the extended withholding tax, the WITH_ITEM table in the E1FIXWT IDoc segment is sent.</p>\r\n<p>In the case of FAGLDT01, the corresponding segments are E1FAGLSET (table BSET) and E1FAGLXWT (table WITH_ITEM).<br /><br />This data is transferred and posted from the sender system to the receiver system without being changed. This means that the receiver system no longer navigates to control routines for the calculation of taxes. The IDoc must already contain the complete tax information, or the relevant IT_ACCTX and IT_ACCWT internal structures must be added to a user exit (for example EXIT_SAPLF050_008) with the necessary tax information so that they can be transferred to the RW interface for the update.<br />For external data transfer from a non-SAP system, see also the following point \"Creating the IDoc FIDCCP01 from an external interface - external data transfer\".<br /><br />By sending the complete tax information, an advance return for tax on sales/purchases is possible in the receiver system (central system).<br /><br />Caution:<br />Report RFUMSV00 for the advance return for tax on sales/purchases may display incorrect tax percentage rates on the list or generate an error message.<br />Implement the corrections in SAP Note 134620 to prevent this. The KNUMH field in the E1FISET IDoc segment will no longer be sent.<br />This correction is contained in the standard system as of Release 4.5B and is supplied as a Support Package in Release 4.0A, 4.0B and 4.5A.<br /><br /> ---------------------------------------</p>\r\n<ol>2. Creation of the IDoc basic type FIDCCP02 from an external interface - external data transfer</ol>\r\n<p>----------------------------------------------------------------------<br /><br />Posting data using ALE and FIDCCP01 is not the same as the functions of batch input using Transaction FB01, but corresponds roughly to the batch input as direct input - the data is transferred to the FI/CO interface and posted there as an FI document.<br />This means that the IDoc must contain all data for the update. The system does not add any more data, for example taxes.</p>\r\n<p><strong>Caution:</strong></p>\r\n<p>Since the system does not add any data, check other options for external data transfer to a SAP system.</p>\r\n<ul>\r\n<li>For example, there is still the option of the BI batch input using RFBIBL00.</li>\r\n</ul>\r\n<ul>\r\n<li>Another officially released interface for external data connection to R/3 is the BAPI interface, which supplies the individual R/3 applications using the accounting interface. These BAPIs were developed for individual business transactions. There are, however, also BAPIs that can be used generally.<br />See SAP Note 306504 for more information.<br />BAPI path: Transaction BAPI -&gt; Accounting - General -&gt; Accounting Interface -&gt; for example: Accounting Billing or Accounting GL Posting (IDoc ACC_BILLING0x) .</li>\r\n</ul>\r\n<ul>\r\n<li>In individual cases, you can also use the INVOIC01 or INVOIC02 IDoc basic types for the data transfer of customer postings or vendor postings from the external system, even though these are actually designed for the EDI data transfer.</li>\r\n</ul>\r\n<p>Both tools work in the same way as transaction FB01, document entry. Transaction FB01 is activated and processed internally and all missing data and line items are added automatically. A complete FI document is created. All other active applications are also updated automatically, for example SL, CO and so on.<br />This does not happen when you use the FIDCCP01 IDoc; see also section 4 \"Data should be sent to all applications\".<br /><br />The concept of 'sending complete FI documents' is designed in such a way that completely prepared and posted FI documents can be transferred with all line items using ALE from an R/3 sender to an R/3 receiver system.<br />The transfer medium for this is the FIDCCP01 IDoc and, as of 4.6B (Support Package SAPKH46B38) and as of 4.6C (Support Package SAPKH46C29), its successor IDoc basis type, FIDCCP02.<br /><br />Therefore, an FI document is sent that is ready for posting and has already run through all routines in the sender system that are involved in document creation. This means, for example, that all routines for tax calculation are also processed and (if necessary) automatic line items are generated for this purpose.<br />A posted FI document consists of different data records with contents that are related to each other, for example, BKPF contains the document header information, the BSEG records contain the individual line items, BSET and WITH_ITEM contain tax information summarized according to the tax code, the tax base amount, and so on.<br /><br />Since this conceptual basis is used, the contents of the FIDCCP01 IDoc correspond fully to those of a prepared FI document, which also contains the complete tax information with all links for the line items, all clearing line items at business area level and so on.<br />Therefore, if the FIDCCP01 IDoc is filled from an external interface, all data which corresponds to a completely created FI document must already be transferred, that is all tax line items, all clearing line items, etc., that would be automatically created by the SAP tax routines.<br />Other fields of the IDoc segments must also be filled properly because functions that are carried out in the FI/CO interface are dependent on them. For example, the E1FIKPF-MONAT field must be filled so that the BSET lines are posted in the receiver system.<br /><br />In contrast to this, the reference fields for the original document, such as AWTYP, AWREF, AWORG, and AWSYS, must not be filled during an external data transfer.<br />In external data transfer, these fields are set by the system. Reference fields (AWREF and AWORG in particular) that are partly filled lead to incorrect document number assignment with gaps in the recipient system; see SAP Note 131759 for more information.<br />If information from the sender system (non-SAP system) is to be stored in the IDoc that is then also contained in the resulting FI document created in the receiver system, the BKTXT and SGTXT text fields can be used for this purpose. Documents from the non-SAP systems can be identified in this way. However, a better option would be to use a separate document type and a separate document number interval.<br /><br />If the BKPF-AWTYP, -AWREF, -AWORG, and -AWSYS fields are empty, the system sets them as follows in the FI document:</p>\r\n<ul>\r\n<li>BKPF-AWTYP<br />= 'BKPFF'</li>\r\n</ul>\r\n<ul>\r\n<li>BKPF-AWKEY (AWREF + AWORG)<br />= FI document number(10), company code(4), fiscal year(4),</li>\r\n</ul>\r\n<ul>\r\n<li>BKPF-AWSYS<br />= ' ' blank</li>\r\n</ul>\r\n<p>In the inbound IDoc, NO further tax routines are accessed, that is, no more taxes are calculated and no automatic tax line items or other line items are generated. The FIDCCP01 IDoc from which the FI document is created and posted in the recipient system must already contain all this information.<br />If the IDoc is sent from an SAP System (R/3-R/3 coupling), no more check routines will run in the recipient system.<br />In contrast, some tax check routines will run if the IDoc is received from an external interface. The system recognizes this situation because the AWREF and AWORG fields are empty.</p>\r\n<p><strong>For exceptions in the receiver system, see the following point below:</strong></p>\r\n<p>\"Exceptions: Document fields that are filled by the accounting interface\"</p>\r\n<p><strong>Recommendation:</strong></p>\r\n<p>We recommend that you execute a manual posting in an SAP system, (such as FB01) to compile an IDoc FIDCCP01. This IDoc is created by default and can then be used as a basis for comparison for an externally created IDoc.<br />See also SAP Notes 156942 and 1350619.</p>\r\n<p><strong>If you have problems with creating and posting an IDoc with the basic type FIDCCP01/FIDCCP02 from an external interface, contact SAP Consulting. Unfortunately, problems with external data transfer cannot be handled by the SAP support team.<br /><br /></strong></p>\r\n<p><strong>Message type FAGLDT01</strong></p>\r\n<p><strong>External data transfer is not generally possible with IDocs of the basic type FAGLDT01 or FAGLST01.</strong> Even if the fields BKPF-AWTYP, -AWREF, -AWORG, and -AWSYS are empty, the system cannot correctly handle IDocs like this in IDoc inbound processing. Various problems occur.</p>\r\n<p>Since other options are available for external data transfer (in particular, the BAPI interface), it will not be enabled for FAGLDT01 and FAGLST01 in the future, either, and SAP will not create any corrections in this respect.</p>\r\n<p><strong> ----------------------------------------------------<br /></strong></p>\r\n<ol><ol>3. Related documents from a business point of view</ol></ol>\r\n<p>------------------------------------</p>\r\n<p>In some cases, the system generates multiple FI documents in a single posting run.</p>\r\n<p>Examples include cross-company-code documents, purchase account management, invoice reduction from the logistical invoice check, security retention from the logistical invoice check, cash discount, and cash-basis accounting.</p>\r\n<p>Technically, you can recognize these related documents by the fact that they have identical AW-fields (AWSYS, AWTYP, and AWKEY).</p>\r\n<p><br />These FI documents are sent individually, that is, there is an IDoc for each document. These are posted in turn as separate FI documents in the receiver system. Consequently, the connection between the documents is lost. (For example, 'leading company code' for taxes in the case of cross-company-code documents.)</p>\r\n<p><strong>Message type FAGLDT01</strong></p>\r\n<p>In the case of IDocs of the type FAGLDT01, related documents are sent together in an IDoc to preserve the context.</p>\r\n<p>Note:<br />For all related documents, the system checks whether they should be sent directly using the IDoc FAGLDT01 or summarized using the IDoc FAGLST01. <br />Thus, once one of the documents must be transmitted by means of FAGLDT01, all related documents are transmitted together by means of FAGLDT01.<br /><br /> ---------------------------------------------</p>\r\n<ol>4. Sending data to all applications</ol>\r\n<p>----------------------------------------------------<br /><br />(In Releases 3.1I, 4.0A and 4.0B<br />the FI documents sent with message type FIDCC1 were still<br />updated in all active applications in the receiver system.<br />This function is cancelled by Support Package and the data<br />is sent to FI only by default).<br /><br />In the SAP standard system, only the FI document, general ledger and subledger are updated in the receiver system, that is, the component is \"FI\". This is the same update logic as that of the (old) message types FIDCMT and FIROLL.<br />The reason for this is that there is a danger of posting errors in other applications because of missing data. In addition, overlapping with other ALE linkages can occur, for example with CO, thereby resulting in duplicate postings.<br /><br />Exception as a modification:<br />It is possible to set the I_COMP and I_COMP_CHECK parameters to blank with user exit 008, enhancement F050S005, function module EXIT_SAPLF050_008 (transaction CMOD, see documentation). <br />The inbound IDoc is processed and the data is forwarded for posting to the Accounting Interface. The I_COMP and I_COMP_CHECK parameters are used to control which applications the IDoc data is transferred to for posting.<br />The parameters are set to 'FI' by default - which is why only the update of data is started in FI.<br />If the parameters are set to blank (' '), the IDoc data is updated in all applications that are active in the target system.<br />A selective update in only some applications is not possible - either ONE or ALL active applications are updated.<br />See also SAP Note 109108.<br />(The TRWPR system table determines which applications are active. This component table must not be changed by the user, as this can lead to the disruption of the general integrative posting schedules.)<br /><br />As of Release 4.7. it it is possible to control the update of special applications using the definition of a customer-specific SUBSET in the TRWPRC table (such as subset 'GAL1').<br />See SAP Notes 899254 and 892366.</p>\r\n<p>For FAGLDT01 IDocs, the corresponding custom subset can be passed in the BAdI FAGL_ALE_DOC_CHANGE_RECEIVER using the parameter CV_SUBSET.</p>\r\n<p>For IDocs with the message type FAGLST01, a change of the subset is <strong>not</strong> possible. Since, from the standard view, it is not ensured for totals IDocs that all relevant information (for example, CO account assignments such as CO internal orders) is always present in the FI totals record, the receiver system ignores a corresponding change in the BAdI FAGL_ALE_DOC_CHANGE_RECEIVER for this message type and always only updates the FI document.</p>\r\n<p>This means that you are not allowed to activate the update in other applications by changing the subset if you use IDocs of the type FAGLDT01 and of the type FAGLST01 in a scenario. In this case, only some documents would be updated in the other applications, and this would generate inconsistencies.</p>\r\n<p><strong>Caution:<br />The change of the parameters I_COMP, I_COMP_CHECK, and I_SUBSET in the user exit 008 (FB EXIT_SAPLF050_008) and CV_SUBSET in the BAdI FAGL_ALE_DOC_CHANGE_RECEIVER or the change of the standard subset in TRWPRC is viewed as a modification. SAP does not accept any responsiblity for ensuring that the postings actually occur as they should in other applications.  <strong>If problems occur, contact SAP Consulting. Unfortunately, SAP support cannot deal with these problems.<br /><br /></strong><br /></strong><br /><strong>General information</strong></p>\r\n<p>Since the FIDCC1 and FIDCC2 message types are involved in the distribution of FI documents, only the data that is stored in the FI document can be transferred to the FI/CO interface in the receiver system. However, for the correct update of the other applications, additional data may be required that is no longer available in the receiver system. This data was stored temporarily in the FI/CO document in the sender system during the posting process and is also stored in additional tables (vectors), for example, the characteristic vector for CO-PA (PAOBJNR).<br /><br />Since none of this can be predicted in detail, especially when the scenarios the user wants to implement are not known, SAP is unable to provide detailed information on how to proceed.<br />Before you make a corresponding modification, you must check exactly whether the required result is achieved in other applications in the receiver system. Ensure that no overlaps have occurred due to sending data twice through other ALE linkages.<br /><br />There are no objections to a modification if distribution is carried out from a solely FI system to all applications. This scenario would work.</p>\r\n<p><strong>Some remarks on updating in CO:</strong></p>\r\n<p>When using the FIDCC1 or FIDCC2 FI message type, updating the profitability analysis in CO-PA does not work because a characteristic value of the profitability segment, which can be identified through the number in the PAOBJNR field, only exists in the sender system and is not distributed.<br />Thus, as of Release 4.6C, the PAOBJNR field that is filled and distributed in the E1FISEG IDoc segment is deleted again in the receiver system because it indicates the profitability segment in the sender system which is unknown in the receiver system. Otherwise incorrect postings may occur.<br />See SAP Note 197951 for an advance correction.<br /><br />A further reason why the CO-PA profitability analysis should not be updated is described below:<br />FIDCC1/FIDCC2 only submits FI-relevant data to the RWIN interface, function module AC_DOCUMENT_GENERATE. However, the special ACCIT_PA CO-PA structure is not filled.<br /><br />In inbound processing (receiver system), the RWIN AC_DOCUMENT_GENERATE function module is called for posting an FI document. When you use the FIDCC1 or FIDCC2 message type, the following data structures (parameters) are transferred:<br />T_ACCHD STRUCTURE ACCHD (BKPF)<br />T_ACCIT STRUCTURE ACCIT (BSEG)<br />T_ACCCR STRUCTURE ACCCR (BSEG)<br />T_ACCFI STRUCTURE ACCFI OPTIONAL (BSEC - CPD)<br />T_ACCTX STRUCTURE ACCBSET OPTIONAL (BSET - tax)<br />T_ACCWT STRUCTURE ACCIT_WT OPTIONAL (WITH_ITEM - extended withholding tax)<br /><br />Data structures<br />T_ACCIT_PA STRUCTURE ACCIT_PA and<br />T_ACCCR_PA STRUCTURE ACCCR_PA<br />are not transferred since this is special data for the profitability analysis in component CO-PA. In the standard system, however, only the FI component is updated.<br /><br />As such, possible customer-defined fields such as WWPCT and ZZPCT cannot be transferred to CO-PA for update because they do not exist in the ACCIT structure. Although these fields exist in the ACCIT_PA structure, this structure is not submitted to the RWIN interface.<br />Even if structure ACCIT_PA were transferred, it would not be certain that the CO-PA would be updated correctly.<br /><br />We recommend that you use the special IDocs for CO-PA instead:</p>\r\n<ul>\r\n<li>CODCMT for account-based profitability analysis.<br />This enables you to send the line items from CO-PA directly to another CO-PA (in other words, replicate them).</li>\r\n</ul>\r\n<ul>\r\n<li>CPxxxx (xxxx=profitability segment, generated IDoc) for costing-based profitability analysis.</li>\r\n</ul>\r\n<p><br />For the relevant documentation, call transaction ORKE, and choose \"Tools -&gt; Data transfers between CO-PA and other systems -&gt; Distributed profitability analysis -&gt; (individual points)\".<br />Other IMG documentation:<br />'Run initial supply for distributed profitability analysis' item: choose 'Run'. The initial screen is displayed. Choose: -&gt; Help -&gt; Application help -&gt; the IMG documentation is displayed.<br />In case of further questions, please contact CO-PA consulting.<br /><br /><br />We do not recommend the update in classic Profit Center Accounting (the component EC-PCA) either.<br />If the decentralized profit center scenario is active, an update is generally not possible in most cases.<br />When you assign a 'home system' to every profit center the system automatically sends documents or line items to the relevant home systems of the relevant profit center and there they are updated in the PCA.</p>\r\n<p><strong>If you use FI to send and post data simultaneously in PCA, there is a considerable risk of duplicate data.</strong></p>\r\n<p>Caution is also advised n the central profit center scenario. For example, it is not possible to roll decentralized secondary data (that is decentralized data originally posted in CO and PCA) at the end of the period into the central system separately.<br />You must check whether an update of PCA is possible with the FIDCC1/2 FI message types. However, these can only be individual exceptions.<br />To use the FI message types FIDCC1/2 in connection with new general ledger accounting, see SAP Note 826357.<br /><br /> -----------------------------------------------</p>\r\n<ol>5. Extended withholding tax (new as of Release 4.0)</ol>\r\n<p>----------------------------------------<br /><br />As of Releases 4.6B (Support Package SAPKH46B38) and 4.6C (Support Package SAPKH46C29), extended withholding tax is also supported in the ALE-distributed system environment. For this purpose, the FIDCCP02 successor IDoc basis type has been developed for the previous FIDCCP01 IDoc basis type.</p>\r\n<p><strong>For the use of the extended withholding tax, see SAP Notes 450076 and 459938. The new IDoc type FIDCCP02 is also available on SAPSERV3.<br /></strong></p>\r\n<p>FIDCCP02 has the same functionality as FIDCCP01 and is also delivered with the FIDCC1 and FIDCC2 message types.<br />The segment E1FIXWT for extended withholding tax, the segment E1FISE2 and some additional data fields were added to FIDCCP02.<br /><br />If you want to use the new IDoc basic type FIDCCP02, you must enter the basic type 'FIDCCP02' for the IDoc type in the partner profiles, outbound parameter of message type FIDCC1 or FIDCC2. See also SAP Note 459938.<br /><br /> ------------------------------------------------</p>\r\n<ol>6. Are sent documents cleared automatically?</ol>\r\n<p>-----------------------------------------------------------------<br /><br />Sent documents are cleared as follows in the SAP standard system:</p>\r\n<ol><ol>a) All customer and vendor line items are cleared.</ol></ol><ol><ol>b) G/L accounts managed on an open item basis are not cleared.</ol></ol>\r\n<p>Clearing information is written as follows to the concerned document line items:<br />BSEG-AUGBL = 'External ALE'<br />BSEG-AUGDT = Posting date (BKPF-BUDAT)<br />BSEG-AUGCP = Posting date (BKPF-BUDAT)</p>\r\n<p>If, unlike in the standard system, you do NOT want to clear the sent documents, you can activate user exit 10 (CMOD enhancement F050S006, function module EXIT_SAPLF050_010) and set the flag NO_AUGBL = 'X'. No sent documents are then cleared.<br />You can also use user exit 10 to deactivate the standard clearing and to clear the documents yourself.</p>\r\n<p>For detailed documentation about user exit 10, see SAP Note 336394.<strong><br /></strong></p>\r\n<p><strong>Message type FAGLDT01</strong></p>\r\n<p>The clearing of sent documents is dependent on how you have set up document transfer in transaction SALE at <em>Modelling and Implementing Business Processes -&gt; Configure Predefined ALE Business Processes -&gt; Accounting -&gt; AC &lt;-&gt; AC -&gt; Set Up Distribution of Financial Accounting Data (New) -&gt; Set Up Distribution Scenario -&gt; Make Basic Settings for Distribution Scenario</em>:<em><br /></em></p>\r\n<ol style=\"list-style-type: lower-alpha;\">\r\n<li><em><em><em>Accounts Receivable/Payable Accounting: Central<br /><br /></em></em></em><ul>\r\n<li>All customer and vendor line items are cleared.</li>\r\n<li>Open-item-managed G/L accounts are not cleared.<br />The BAdI FAGL_ALE_CLEAR_GL_ACCOUNT also provides the option of clearing open-item-managed G/L accounts.</li>\r\n</ul><em><em><br /></em></em></li>\r\n<li><em>Individual Transfer Only</em><br /><br />No documents are cleared automatically. <br />With this setting, you can therefore implement scenarios where payment takes place in the sender system.<br /><br /></li>\r\n</ol>\r\n<p>Note that only one of these settings can be selected so that no problems occur. If you select <em>Individual Transfer Only</em>, the system still transfers all customer/vendor accounting documents.</p>\r\n<p>------------------------------------------------</p>\r\n<ol>7. How are assets treated?</ol>\r\n<p>---------------------------------<br /><br />In the case of the message type FIDCMT, sending assets as line items (indicator 'Account managed in ext. system' is activated in the G/L master record of the reconciliation account) is not possible.<br />In contrast, you can send the individual asset items for the FIDCC1 and FIDCC2 message types. However, you can only store the fixed assets in the FI document, and the assets reconciliation account is updated in the general ledger. Asset Accounting is not updated at the same time. For this, you would have to send a separate IDoc for fixed assets and this does not exist at present (29.04.2004). At present, there is no intention of providing an IDoc of this kind.  The supported scenario assumes that the fixed assets are managed in decentralized systems only.<br />Problem:<br />You send assets from a decentralized system to the central system and you save them in the FI document. The fixed assets of the sender are not usually known in the receiver system and the following error message is displayed when you display the FI document in the receiver system:<br />AA001 'Asset &amp; &amp; not in company code &amp;'.<br />For this reason, a program enhancement is available (OPEN FI) in Release 4.6A, which is also described as a workaround in SAP Note 136500 and can be implemented into the customer system.<br />For this program enhancement, a clearing account needs to be added in ALE Customizing which is usually only required for the FIDCMT and FIROLL message types. In FIDCC1, this clearing account is used as an auxiliary account to enable you to post documents with enclosures in the recipient system.<br /><br /> ------------------------------------</p>\r\n<ol>8. How do you add fields to an IDoc?</ol>\r\n<p>-------------------------------------------------<br /><br />In addition to the standard fields sent in the IDoc, you can add 'user-defined' fields and/or SAP-defined fields to the IDoc.  To do so, you have to define an IDoc segment with these additional fields (see the documentation on IDocs).<br />You can only add fields to the IDoc that also exist in the ACCIT structure. The ACCIT is the transfer structure for the RW-interface, where the FI document is created.<br /><br />You can also enhance the ACCIT structure with fields.  See SAP Note 63019.<br />For more information about this topic, see SAP Note 47410, point 4.<br /><br />In inbound processing (receiver system), the RWIN AC_DOCUMENT_GENERATE function module is called for posting an FI document. The following structures are transferred if the FIDCC1 or FIDCC2 message type is used:<br />*\" T_ACCHD STRUCTURE ACCHD (BKPF)<br />*\" T_ACCIT STRUCTURE ACCIT (BSEG)<br />*\" T_ACCCR STRUCTURE ACCCR (BSEG)<br />*\" T_ACCFI STRUCTURE ACCFI OPTIONAL (BSEC - CPD)<br />*\" T_ACCTX STRUCTURE ACCBSET OPTIONAL (BSET - tax)<br />*\" T_ACCWT STRUCTURE ACCIT_WT OPTIONAL (WITH_ITEM - extended withholding tax)<br /><br />The structures<br />*\" T_ACCIT_PA STRUCTURE ACCIT_PA and<br />*\" T_ACCCR_PA STRUCTURE ACCCR_PA<br />are not transferred since this is special data for profitability analysis in the component CO-PA. In the standard system, however, only the FI component is updated.<br /><br /><br /> -----------------------------------------------</p>\r\n<ol>9. Does substitution work in the receiver system?</ol>\r\n<p>-----------------------------------------------------<br /><br />When you post an FI document, you always have the option of changing and storing fields in an FI document using 'substitution'.<br />If you use ALE FI to send data, this function can be used in the receiver system but only for callup points 1 (BKPF) and 2 (BSEG).<br />Callup point 3 (complete document) is not possible.<br /><br />SAP Note 386896 describes a modification that also allows you to use callup point 3 with ALE FI. However, this modification only carried out a substitution in the FI document and not in subsequent postings, such as FI-SL or CO. This is because substitution callup point 3 is in the 'PROJECT' time of processing. In 'PROJECT', all RW components have already received data and no further data exchange occurs. The substitution is only carried out locally in the FI document.<br /><br />This would only represent a restriction for customers who want to update all components by means of a modification in user exit 008 in the recipient system.<br />If ALE FI is used in the SAP standard, without modification of user exit 008, the activation of the substitution for callup point 3 does not represent any further restriction in the recipient system since only the FI document is updated here.<br /><br />In the case of a modification, note that if you activate substitution point 3, this activation should be performed in the recipient system only and only when posting ALE FI documents. In normal postings, the restriction mentioned above would take effect again for subsequent components.<br /><br /> -----------------------------------------------</p>\r\n<ol>10. Problem (up to Release 4.5A) for the changeable credit control area KKBER</ol>\r\n<p>----------------------------------------------------------------</p>\r\n<p><br />This problem no longer occurs.<br /><br /> -----------------------------------------------</p>\r\n<ol>11. Credit limit check</ol>\r\n<p>---------------------<br /><br />This is possible as of Release 4.0A, but not in earlier releases.<br />If open items from decentralized SD systems, that is, invoices resulting from SD orders, are distributed to the central FI accounting system, these invoices are paid in the central system.<br />As a result, the current status of the credit assignment is known in the central system but not in the decentralized system. This is because, up to now, the information reflux from central to decentralized systems has not been possible. Thus, up to Release 3.1I you could not carry out a current credit limit check in decentralized systems when posting orders.<br /><br />As of Release 4.0A, ALE can be used to distribute the current data for a credit limit check from the central system to the decentralized systems. To do so, ALE message type CRESTA must be activated and this can be used to trigger the sending of a credit vector from the central system at periodic intervals.<br /><br /> ---------------------------------------------</p>\r\n<ol>12. Is the invoice reference also possible in the receiver system ?</ol>\r\n<p>-----------------------------------------------------------<br /><br />This function is delivered with the following Releases:</p>\r\n<ul>\r\n<li>Invoice reference to FI documents as of Release 4.5A</li>\r\n</ul>\r\n<ul>\r\n<li>Invoice reference to MM/SD documents as of Release 4.6B.</li>\r\n</ul>\r\n<ul>\r\n<li>Problems with changing and with credit memos for invoice reduction documents from MM --&gt; implemented using (future) SAP Notes and Support Packages...</li>\r\n</ul>\r\n<p><br />There are several SAP Notes about this, which still have to be implemented:<br />Keywords: ALE, REBZG<br />Among others: SAP Notes 105034, 126671, 167026, 152412, 131759, and 320615.<br /><br />No invoice reference can be created in the 'loan' application in the receiver system since several follow-on documents were created. This is a special feature. This means that no unique assignment for a document is possible.<br />Documents from Loans can be identified by field BKPF-AWTYP='LOANS'.<br />In the receiver system, information for the invoice reference is deleted from the sender system. This is relevant to the REBZG, REBZJ, REBZZ and REBZT fields in the BSEG table.<br /><br /> ---------------------------------------------</p>\r\n<ol>13. Use of SAP enhancements and user exits</ol>\r\n<p>--------------------------------------------------<br /><br />To activate SAP enhancements, create a 'Project' using transaction CMOD.<br />The SAP enhancements and user exits for FI ALE can be found under enhancement F050S* using transaction SMOD. There, the individual 'function exits' (also called user exits) are listed under 'Components',  for example, EXIT_SAPLF050_008. Detailed online documentation is also stored there.<br /><br />If you use user exits, SAP views this as modification.  Questions and problems arising in this context can only be handled as consulting matters which you are charged for separately.<br />Exercise great caution when you use user exits.</p>\r\n<p><strong>If you activate projects for SAP enhancements and define customer-specific source code in user exits, the source code must not contain the ABAP statement COMMIT WORK.<br />'COMMIT WORK' or 'COMMIT WORK AND WAIT' can cause incorrect posting if, for example, the system updates one part of the data in the database but it does not update another part.<br /></strong></p>\r\n<p>The SAP program concept has been designed so that the SAP standard program does not raise a COMMIT WORK before the end of dialog processing. As a result, a logical unit of work (LUW) is completed and all database changes stored temporarily through, for example, 'CALL FUNCTION ... IN UPDATE TASK' up to that time are carried out. Furthermore, all database locks are cancelled.<br />Since a user exit is called and processed within a SAP program, a COMMIT WORK raised here might lead to an undefined and unpredictable posting state (there is a chance that this may occur) because the LUW is separated.</p>\r\n<p><strong>Important:<br />The import of Support Packages might deactivate active user exits if a program change from the Support Package in question is relevant for these user exits.</strong></p>\r\n<p>These user exits are not taken into account when the program is run, that is, the source code stored by the user is ignored. In the ALE environment, this may lead to documents being sent incorrectly and even to database inconsistencies.<br />Currently, the user exits must be checked manually and activated, if required, after importing Support Packages. See SAP Note 402972.</p>\r\n<p><strong>Message type FAGLDT01</strong></p>\r\n<p>The user exits in transaction SMOD are not relevant for FAGLDT01/FAGLST01.</p>\r\n<p>Various BAdIs are available and can be found in transaction SALE at <em>Modelling and Implementing Business Processes -&gt; Configure Predefined ALE Business Processes -&gt; Accounting -&gt; AC &lt;-&gt; AC -&gt; Set Up Distribution of Financial Accounting Data (New) -&gt; Business Add-Ins (BAdIs)</em>.<br /><br /> ---------------------------------------------</p>\r\n<ol>14. Special G/L transactions</ol>\r\n<p>-------------------------------<br /><br />Special G/L transactions are:</p>\r\n<ul>\r\n<li>Down payments</li>\r\n</ul>\r\n<ul>\r\n<li>Down payment requests</li>\r\n</ul>\r\n<ul>\r\n<li>Bills of exchange</li>\r\n</ul>\r\n<ul>\r\n<li>Others</li>\r\n</ul>\r\n<p>The SAP distribution scenario for FI documents assumes that the decentralized systems are logistics systems, for example. The central receiver system is the FI accounting system where all operational processes such as payment, dunning and so on, are carried out.<br />Under these circumstances, posting documents with special G/L transactions (except for down payment requests) is only feasible in the central system.  In other words, it is not necessary to post special G/L transactions in decentralized systems and use ALE to send them.</p>\r\n<p><strong>For this reason, sending special G/L transactions from a decentralized system is not supported in the SAP standard system.</strong><br /><strong>If the FIDCC1 or FIDCC2 message type is active, it is possible to post and send documents with special G/L transactions in a decentralized system (except for down payment requests) but it cannot be guaranteed that an FI document can be successfully posted from this IDoc in the receiver system.</strong><br /><strong>If the user sends special G/L transactions from the decentralized system anyway, it is their responsibility to ensure that the special G/L transaction document is successfully, completely and correctly posted in the receiver system.</strong></p>\r\n<p><br />Causes of possible errors are:</p>\r\n<ul>\r\n<li>The FI document and the resulting IDoc may not contain all required fields. For example, the BSED structure is missing for special G/L transaction \"Bill of exchange\".</li>\r\n</ul>\r\n<ul>\r\n<li>The FI interface for posting the IDoc documents cannot process all special G/L transactions because the corresponding fields are missing in submit structures such as ACCIT and so on.<br />'Bills of exchange', for example, are immediately rejected in RWIN, and the following error message is displayed:<br />F5 246 'Special G/L transactions of class W not supported'</li>\r\n</ul>\r\n<p> (In a technical sense: special G/L transaction documents with BSEG-UMSKS='W' and BKPF-BSTAT='S' are not posted in the accounting interface.)<br /><br />However, for a purely FI posting, it is safe to assume that after successfully posting an IDoc document in the receiver system, that is, no error message was displayed and the special G/L transaction document was posted completely and correctly.<br />It is therefore possible, that the system can, for example, process purely FI down payments successfully.  Down payments with additional account assignments (for example for purchase orders, assets, orders) are not posted completely.<br />The purchase order history is not updated in purchase orders. The purchase orders are cleared immediately and offsetting is not possible.<br />With assets, no additional clearing lines are created.<br />With orders, open down payment items are not updated.<br />If the user wants to send special G/L transactions from the decentralized system anyway, they must carry out appropriate testing. <br /><br /></p>\r\n<ul>\r\n<li>Down payment requests<br />These cannot be sent using ALE in the SAP standard system.<br />Due to the wide range of usage of ALE FI, we do not plan to develop a standard solution for this matter (since it is impossible).<br />However, it is possible (and has already been implemented) within the framework of a customer project solution to allow distribution of down payment requests in accordance with the specific user situation (-&gt; Remote Consulting).</li>\r\n</ul>\r\n<p><br /><br /> ---------------------------------------------</p>\r\n<ol>15. Is document reversal possible?</ol>\r\n<p>---------------------------------<br /><br />It is possible to send reversal documents.<br />However, all reversal documents are only posted inversely in the receiver system, even in transaction FB08.<br />Note that no cross-reference to the reversed document or to the document to be reversed is written in the document headers in the receiver system. Similarly, all open item lines are still open and not cleared. The user is responsible for clearing in the reversed documents.<br />The documents concerned can be selected using the BKPF-TCODE field (transaction code), for example = 'MR08', 'MR8M', 'FB08' and so on.<br /><br />Caution:<br />If reversal postings are sent from the decentralized system, processing runs correctly in the central system, regardless of whether the invoice had already been paid in the central system or not.<br />If the invoice had already been paid, a receivable is created which can be cleared in the next invoice.<br />If the invoice had not been paid, the payment program balances the amount to zero using the inverse posting and clears the item.<br />Thus, a problem rarely occurs when an invoice has already been paid and cleared in the central system and this invoice is subsequently reversed from the decentralized system.<br /><br />In the central system, FI documents that were posted from another system should not be reversed.<br />As of Release 4.6B, the system prevents a document from being reversed by another logical system (LOGSYS) other than its own system. Error F5A 135 'Reversal of accounting document impossible' occurs. A reversal in the central system is then impossible.<br /><br />This lock is useful, particularly with regard to reconciliations between sender and receiver systems (see new ALE comparison tool GCAR as of Release 4.7). It is a prerequisite for the use of the ALE-comparison-tool that documents coming from other systems cannot be reversed in the central system since there is no reflux into the local systems. The general ledgers in the GLT0 would become inconsistent and it would be impossible to compare them.<br /><br /><br /> ---------------------------------------------</p>\r\n<ol>16. Can accrual/deferral documents be sent?</ol>\r\n<p>-----------------------------------------------------<br /><br />Accrual/deferral postings (transaction FBS1) are special postings within the framework of financial statements. We assume that such postings are typically made in the central financial accounting system. It is not useful to make accrual/deferral postings in the decentralized systems.<br /><br />Sending documents from the decentralized systems to the central system is possible but is not intended for the standard system.<br />Incorrect postings would occur for the following reason:<br />In the case of accrual/deferral postings, the 'Reversal reason' (BKPF-STGRD) and 'Reversal date' (BKPF-STODT) fields are mandatory. These fields are not sent using ALE and therefore cannot be posted in the central FI system using the RWIN posting interface (fields are missing in the IDoc and in the ACCIT structure for the RWIN).<br />This means that a sent accrual/deferral document would become a normal FI document in the central system without allowing automatic reversal on the specified reversal date.<br /><br /><br /> ---------------------------------------------</p>\r\n<ol>17. How are the reference fields AWSYS, AWTYP, and AWKEY filled?</ol>\r\n<p>-----------------------------------------------------------------<br /><br />The AWSYS, AWTYP, and AWKEY (AWREF + AWLOG) document header fields contain information that identifies the original document. These fields can be used to uniquely identify the original document for the current FI document within the SAP system or across system boundaries. These reference fields form a logical unit, that is, no one field can uniquely identify an original document without the others.<br />This is especially important in distributed systems.</p>\r\n<ul>\r\n<li>AWSYS<br />Logical system where the document was originally posted. The name is taken from the T001-LOGSYS client table field.</li>\r\n</ul>\r\n<ul>\r\n<li>AWTYP<br />Reference transaction; identifies the accounting transaction from which the document was originally posted, for example, 'BKPF' for 'Accounting document' or 'RMRP' for 'Invoice receipt'.</li>\r\n</ul>\r\n<ul>\r\n<li>AWKEY<br />Reference key; contains the key of the original document.<br />The AWKEY consists of</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>AWREF = reference document number<br />Contains the number of the original document, for example, the FI or MM document number.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>AWORG = reference organizational units<br />Contains one or several additional keys for unique identification of the original document. For example, the reference organizational units for an FI document consist of<br />BUKRS = company code and GJAHR = fiscal year,<br />or for an MM document, they consist of<br />GJAHR = fiscal year.</li>\r\n</ul>\r\n</ul>\r\n<p><br />However, postings also exist whereby two FI documents are created from one original document (for example from MM).  In this case, the reference fields are completely identical in the FI-documents since both FI documents are related to ONE original MM document.<br />For example, documents such as these are created when you post an MM invoice reduction.<br /><br /> ---------------------------------------------</p>\r\n<ol>18. Is data comparison between the sending and receiver systems possible?</ol>\r\n<p>-----------------------------------------------------------------------<br /><br />In Release 4.7, an ALE comparison tool for ALE distributed systems is available and can be called in the central system using transaction GCAR.<br />The basis for this tool is a comparison ledger in the table GLFUNCT in the central system, for example, the cost of sales accounting (CSA) ledger 0F.<br />You can also use the F050S008 ALE BAdI to update your own comparison ledger. A comparison can only be carried out using the data that sent with the FIDCC1 or FIDCC2 message type.<br />For more information and documentation see Release Note FI_470_GL_ALE_REMOTE and SAP Note 517580.<br /><br />Caution:<br />If you use the extended withholding tax and this is calculated in the receiver system (as is the standard), then a comparison between the sender and receiver systems is not possible. In this case, the extended withholding tax is only posted to the general ledger of the receiver system. This data is missing in the general ledger of the sender systems. GCAR would correctly display differences..<br />See also SAP Notes 450076 and 1526488.<br /><br /><br /> ---------------------------------------------</p>\r\n<ol>19. Is payment card processing possible?</ol>\r\n<p>----------------------------------------------<br /><br />The processing of payment cards (such as credit card payment in SD) is impossible because the corresponding data, such as the BSEGC and the encryption data of the credit cards, is not sent using an AI-ALE IDoc (FIDCCP01/FIDCCP02 or FAGLDT01).<br />There is no intention to realize this function. <br />If required, the customer can contact SAP to enquire about a potential realization of this as part of a charged SDP project. <br /><br /><br /><br /> ---------------------------------------------</p>\r\n<ol>20. Cross-system company codes and filter conditions</ol>\r\n<p>------------------------------------------------<br /><br />The cross-system company code is the connection between the affiliated systems and is used for the interpretation and assignment of the special data of a sent local company code for a local company code in the receiver system.<br />The name the local company codes in the sender and receiver system may differ and may also occur repeatedly. To make it possible to interpret the local company codes in each system, they must be assigned to an actual cross-system company code (T001O, T001).<br />The name of the global company code for the respective local company codes must be unique beyond the system boundaries. It can exist only once in all affiliated systems. This ensures unique data assignment accross system boundaries.</p>\r\n<p><strong>If ALE FI is activated in the system/clients, the cross-system company codes must be assigned in the company code master data for \"all\" local company codes (T001).</strong></p>\r\n<p>This is also necessary for the local company codes that are not to be sent using ALE FI. This assignment is necessary for the technical procedure during the check of the ALE filter objects and does not have any undesired side effects.<br />If the assignment of a cross-system company code is missing, the following error occurs:<br />F1 302 \"No cross-system company code is defined for company code &amp;\". <br /><br />Use BD64 to set the ALE filter conditions.<br />If all company codes are to be sent, you do not have to set filter conditions. In this case, the system sends all company codes relevant for posting.<br />If only individual company codes are to be sent, you must set filter conditions. You specify the global company code of the local company code to be sent as the filter.<br /><br /> ---------------------------------------</p>\r\n<ol>21. Can the accounts be different in the sending system and receiver system - account conversion?</ol>\r\n<p>----------------------------------------------------------------------<br /><br />It is possible to have different accounts in the sending and receiver systems. However, the user must trigger an account conversion in the sending system. This can be easily implemented using the table T030V \"Account Conversion Tables for Distributed Systems\".<br />A conversion is possible in the sending system and for G/L accounts only.<br />In the sending system, the customer must maintain the table T030V. The customer enters the G/L accounts to be converted.<br />Accounts that are not to be converted are not entered. Accounts that are not contained in the table T030V are not converted and are kept as original accounts.<br /><br />The system executes the conversion automatically in outbound processing as follows:<br />Conversion for G/L accounts in chart of accounts only.<br />For the 'sending' G/L account, the system searches for an entry in the table T030V. If an entry exists, the assigned 'receiver' account is placed in the IDoc (converted). If no entry exists, no change is made in the IDoc.<br />The following G/L accounts from the IDoc segments are analyzed and, if necessary, converted:<br />E1FISEG-HKONT, E1FISEG-SAKNR, E1FISET-HKONT (message type FIDCCP01/02)</p>\r\n<p>E1FAGLSEG-HKONT, E1FAGLSEG-SAKNR, E1FAGLSEG-HKONT (message type FAGLDT01)<br /><br /> ---------------------------------------</p>\r\n<ol>22. Exceptions: Document fields that are filled by the accounting interface</ol>\r\n<p>------------------------------------------------------------------------<br /><br />There are a few exceptions where (empty) IDoc or document fields in the accounting interface in the receiver system are subsequently filled or changed. Among these are:</p>\r\n<ul>\r\n<li>Terms of payment (E1FINBU-ZTERM,...)<br />If \"*   \" is entered into the field ZTERM and the other fields for the payment terms payment such as ZFBDT are left empty, the system uses the payment terms defined in the master record to fill the document fields.</li>\r\n</ul>\r\n<ul>\r\n<li>Functional area (E1FISEG-FKBER)<br />Originally, the functional area was derived if it had not been filled. This was reversed in a later release in that the functional area is neither derived in the standard nor filled - the FKBER field remains empty in the document.<br />If the functional area is to be derived, see SAP Note 440537.</li>\r\n</ul>\r\n<ul>\r\n<li>Additional local currency (for example, HWAE2, DMBE2, ...)<br />If the Customizing is set accordingly, the data is determined by the system.</li>\r\n</ul>\r\n<ul>\r\n<li>PSWBT amount for the update in the general ledger<br />PSWSL update currency for general ledger transaction figures<br /><br />PSWBT and PSWSL are overwritten. As a rule, for ALE, Customizing in the sender and receiver systems must match. This means that it is not apparent if, for example, the fields PSWBT and PSWSL are overwritten because the contents of the fields should remain the same if Customizing is identical.<br /><br />PSWBT and PSWSL are not usually overwritten for AWTYP = 'FKKSU' (FI-CA Contract Accounts Receivable and Payable). The original contents are retained here, except for the following exceptions:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If PSWSL is empty, then PSWBT and PSWSL are derived again.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If, in the G/L account master record of the account, the field XSALH = 'X' is activated (the \"Only balances in local crcy\" indicator), then PSWBT and PSWSL are also derived again and overwritten.</li>\r\n</ul>\r\n</ul>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-GL (General Ledger Accounting)"}, {"Key": "Other Components", "Value": "FI-AR (Accounts Receivable)"}, {"Key": "Other Components", "Value": "FI-AA (Asset Accounting)"}, {"Key": "Other Components", "Value": "FI-AP (Accounts Payable)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D054057)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D028660)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "892103", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/892103"}, {"RefNumber": "826357", "RefComponent": "EC-PCA", "RefTitle": "Profit Center Accounting and General Ledger Accounting (new) in mySAP ERP", "RefUrl": "/notes/826357"}, {"RefNumber": "808521", "RefComponent": "FIN-FSCM-IHC", "RefTitle": "IHC: Update with general ledger transfer by IDoc", "RefUrl": "/notes/808521"}, {"RefNumber": "802645", "RefComponent": "PSM-FM", "RefTitle": "ALE: Problems with FM distribution", "RefUrl": "/notes/802645"}, {"RefNumber": "551035", "RefComponent": "FI", "RefTitle": "IDOC overview in FI", "RefUrl": "/notes/551035"}, {"RefNumber": "544415", "RefComponent": "FI-GL-GL-M", "RefTitle": "FAQ: FI ALE distribution of FI documents", "RefUrl": "/notes/544415"}, {"RefNumber": "47410", "RefComponent": "FI-GL-GL-M", "RefTitle": "ALE FIDCMT01: FI distribution questions & problems", "RefUrl": "/notes/47410"}, {"RefNumber": "459938", "RefComponent": "FI-GL-GL-M", "RefTitle": "FI ALE: new IDoc type of FIDCCP02 and segment extensions", "RefUrl": "/notes/459938"}, {"RefNumber": "450076", "RefComponent": "FI-AP-AP-Q", "RefTitle": "Extended withholding tax and ALE", "RefUrl": "/notes/450076"}, {"RefNumber": "2313879", "RefComponent": "FI-GL-GL-M", "RefTitle": "Distribution of documents via ALE from or to SAP S/4HANA systems", "RefUrl": "/notes/2313879"}, {"RefNumber": "197951", "RefComponent": "FI-GL-GL-M", "RefTitle": "ALE FIDCC1/2: Sender PAOBJNR invalid in receiver", "RefUrl": "/notes/197951"}, {"RefNumber": "158781", "RefComponent": "FI-GL-GL-M", "RefTitle": "ALE FI: which applications are updated?", "RefUrl": "/notes/158781"}, {"RefNumber": "156942", "RefComponent": "FI-GL-GL-M", "RefTitle": "FI ALE: status message F1310 is incomplete", "RefUrl": "/notes/156942"}, {"RefNumber": "1504566", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1504566"}, {"RefNumber": "149595", "RefComponent": "FI-GL-GL-M", "RefTitle": "FI ALE: Indirct quotatn values - new fields in IDoc", "RefUrl": "/notes/149595"}, {"RefNumber": "146358", "RefComponent": "FI-GL-GL-M", "RefTitle": "FI ALE: KKBER that can be changed is not sent", "RefUrl": "/notes/146358"}, {"RefNumber": "136500", "RefComponent": "FI-GL-GL-M", "RefTitle": "FI ALE: Error AA001 fixed asset does not exist, F1308, event", "RefUrl": "/notes/136500"}, {"RefNumber": "134620", "RefComponent": "FI-GL-GL-M", "RefTitle": "FI ALE FIDCC1: RFUMSV00 is incorrect in head office", "RefUrl": "/notes/134620"}, {"RefNumber": "1295488", "RefComponent": "FI-GL-GL-M", "RefTitle": "ALE FI FIDCC1/2: Error in asset line item KOART='A'", "RefUrl": "/notes/1295488"}, {"RefNumber": "128111", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/128111"}, {"RefNumber": "1104309", "RefComponent": "FI-GL-GL-M", "RefTitle": "ALE FIDCC1/2: F5827 tax items without KTOSL", "RefUrl": "/notes/1104309"}, {"RefNumber": "109108", "RefComponent": "FI-GL-GL-M", "RefTitle": "FI ALE FIDCC1: Sending FI doc. to all applications", "RefUrl": "/notes/109108"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2313879", "RefComponent": "FI-GL-GL-M", "RefTitle": "Distribution of documents via ALE from or to SAP S/4HANA systems", "RefUrl": "/notes/2313879 "}, {"RefNumber": "826357", "RefComponent": "EC-PCA", "RefTitle": "Profit Center Accounting and General Ledger Accounting (new) in mySAP ERP", "RefUrl": "/notes/826357 "}, {"RefNumber": "1321680", "RefComponent": "XX-CSC-MX", "RefTitle": "Digital Invoice Mexico: IDoc basic type FIDCCP02 Extension", "RefUrl": "/notes/1321680 "}, {"RefNumber": "1504566", "RefComponent": "FI-GL-GL-M", "RefTitle": "ALE FIDCCP02: Document splitting in the case of ALE", "RefUrl": "/notes/1504566 "}, {"RefNumber": "47410", "RefComponent": "FI-GL-GL-M", "RefTitle": "ALE FIDCMT01: FI distribution questions & problems", "RefUrl": "/notes/47410 "}, {"RefNumber": "1104309", "RefComponent": "FI-GL-GL-M", "RefTitle": "ALE FIDCC1/2: F5827 tax items without KTOSL", "RefUrl": "/notes/1104309 "}, {"RefNumber": "1295488", "RefComponent": "FI-GL-GL-M", "RefTitle": "ALE FI FIDCC1/2: Error in asset line item KOART='A'", "RefUrl": "/notes/1295488 "}, {"RefNumber": "588554", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: CML/FI/SL/EC interaction", "RefUrl": "/notes/588554 "}, {"RefNumber": "136500", "RefComponent": "FI-GL-GL-M", "RefTitle": "FI ALE: Error AA001 fixed asset does not exist, F1308, event", "RefUrl": "/notes/136500 "}, {"RefNumber": "802645", "RefComponent": "PSM-FM", "RefTitle": "ALE: Problems with FM distribution", "RefUrl": "/notes/802645 "}, {"RefNumber": "551035", "RefComponent": "FI", "RefTitle": "IDOC overview in FI", "RefUrl": "/notes/551035 "}, {"RefNumber": "808521", "RefComponent": "FIN-FSCM-IHC", "RefTitle": "IHC: Update with general ledger transfer by IDoc", "RefUrl": "/notes/808521 "}, {"RefNumber": "450076", "RefComponent": "FI-AP-AP-Q", "RefTitle": "Extended withholding tax and ALE", "RefUrl": "/notes/450076 "}, {"RefNumber": "158781", "RefComponent": "FI-GL-GL-M", "RefTitle": "ALE FI: which applications are updated?", "RefUrl": "/notes/158781 "}, {"RefNumber": "459938", "RefComponent": "FI-GL-GL-M", "RefTitle": "FI ALE: new IDoc type of FIDCCP02 and segment extensions", "RefUrl": "/notes/459938 "}, {"RefNumber": "544415", "RefComponent": "FI-GL-GL-M", "RefTitle": "FAQ: FI ALE distribution of FI documents", "RefUrl": "/notes/544415 "}, {"RefNumber": "156942", "RefComponent": "FI-GL-GL-M", "RefTitle": "FI ALE: status message F1310 is incomplete", "RefUrl": "/notes/156942 "}, {"RefNumber": "109108", "RefComponent": "FI-GL-GL-M", "RefTitle": "FI ALE FIDCC1: Sending FI doc. to all applications", "RefUrl": "/notes/109108 "}, {"RefNumber": "149595", "RefComponent": "FI-GL-GL-M", "RefTitle": "FI ALE: Indirct quotatn values - new fields in IDoc", "RefUrl": "/notes/149595 "}, {"RefNumber": "197951", "RefComponent": "FI-GL-GL-M", "RefTitle": "ALE FIDCC1/2: Sender PAOBJNR invalid in receiver", "RefUrl": "/notes/197951 "}, {"RefNumber": "134620", "RefComponent": "FI-GL-GL-M", "RefTitle": "FI ALE FIDCC1: RFUMSV00 is incorrect in head office", "RefUrl": "/notes/134620 "}, {"RefNumber": "128111", "RefComponent": "FI-GL-GL-M", "RefTitle": "ALE FIDCC1: Update of all components", "RefUrl": "/notes/128111 "}, {"RefNumber": "146358", "RefComponent": "FI-GL-GL-M", "RefTitle": "FI ALE: KKBER that can be changed is not sent", "RefUrl": "/notes/146358 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "618", "To": "618", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "720", "To": "720", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "730", "To": "730", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C15", "URL": "/supportpackage/SAPKH46C15"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}