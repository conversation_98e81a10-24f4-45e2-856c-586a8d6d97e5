{"Request": {"Number": "1506722", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1478, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017092722017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001506722?language=E&token=972A1ED987F0C07B1F1CFA34B657B1A9"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001506722", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001506722/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1506722"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "08.11.2010"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-ET-WJR"}, "SAPComponentKeyText": {"_label": "Component", "value": "BEx Web Java Runtime"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Explorer", "value": "BW-BEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Enduser Technology", "value": "BW-BEX-ET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-ET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BEx Web Java Runtime", "value": "BW-BEX-ET-WJR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-ET-WJR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1506722 - Generic Note for BI Java Patches and Support Packages"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><b>General Information about BI Java Patches and Support Packages</b><br /><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>BI Java, BI Information Note</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Single note containing important information about BI patches</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br /><B>Patch Availability:</B><br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Corrections of BI Java are offered with a \"Support Package &lt;N&gt; for SAP NetWeaver &lt;Release&gt; BI Java\" and - for urgent cases - also in a BI JAVA Patch.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The BI JAVA Patch can be used for the last three shipped Support Packages (SP N, SP N-1 and SP N-2).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For synchronized patch delivery process overview, reason for implementing this process and advantages please refer to Note 1163789.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;See also the \"BI Java 7. 00 Patching Guide by Start Release\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;http://www.sdn.sap. com/irj/sdn/index?rid=/webcontent/uuid/6010addf-e096-2b10-8893-e98586d005f9<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;and&#x00A0;&#x00A0;\"BI Java 7. 01 Patching Guide by Start Release \"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;http://www.sdn.sap. com/irj/sdn/index?rid=/webcontent/uuid/c098b21c-e56f-2d10-2aad-b13915b396a4<br /><br /><br /><br /><B>Important Notes</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>1033246</B> : Planned release schedule of all 700 BI Java patches for &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAP Netweaver 7.00<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>1309000</B> : Planned release schedule of all 701 BI Java patches for &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAP EHP1 for NetWeaver 7.0 (7.01)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>1512355</B> : Planned release schedule of all 730 BI Java patches for &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAP Netweaver 7.30<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>1163789</B> : Contains information about parallelized Java Patch &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;delivery<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>1013369</B> : Contains information about SP (Java) and BI SP (ABAP) &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; synchronization<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>1011241: </B> For instructions on how to find the BI Java Patches for  SAP Netweaver 7.0<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>1327345: </B> For instructions on how to find the BI Java Patches for SAP EHP1 for NetWeaver 7.0 (7.01)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>1512356: </B> For instructions on how to find the BI Java Patches for  SAP Netweaver 7.30<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>1072576</B>: For Frequently Asked Questions<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>1512988</B>: For Frequently Asked Questions for 7. 30 BI Java<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>656711: </B> Information on deploying Java Support Packages with SDM<br /><br /></p> <b>Patch Implementation</b><br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- The SCAs BI BASE SERVICES and BI WEBAPPLICATIONS have to be &#x00A0;&#x00A0;&#x00A0;&#x00A0; deployed newly.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- The server must be restarted after the deployment.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "I046675"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I815221)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001506722/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001506722/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001506722/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001506722/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001506722/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001506722/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001506722/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001506722/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001506722/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "971631", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/971631"}, {"RefNumber": "656711", "RefComponent": "BC-CTS-SDM", "RefTitle": "Deploying Java Support Packages with SDM", "RefUrl": "/notes/656711"}, {"RefNumber": "1862129", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "All the items inside the template include item are exported", "RefUrl": "/notes/1862129"}, {"RefNumber": "1847299", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Binding commands may fail for dependend variables", "RefUrl": "/notes/1847299"}, {"RefNumber": "1819129", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Comment open in single document item can't be deleted", "RefUrl": "/notes/1819129"}, {"RefNumber": "1807261", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "Performance:F4-Help in Xcelsius dashboards.", "RefUrl": "/notes/1807261"}, {"RefNumber": "1801543", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Variable screen not shown for bookmarks.", "RefUrl": "/notes/1801543"}, {"RefNumber": "1801521", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Incorrect Pagebreaks while exporting the webitems to PDF", "RefUrl": "/notes/1801521"}, {"RefNumber": "1793133", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Improper button size in selector dialog in 730 with FireFox.", "RefUrl": "/notes/1793133"}, {"RefNumber": "1789842", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Patch Level 0 for BI Java Installation", "RefUrl": "/notes/1789842"}, {"RefNumber": "1783963", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "7.3x:URL_BOOKMARK shows blank (loads the default template)", "RefUrl": "/notes/1783963"}, {"RefNumber": "1783878", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "7.30 Error while running dashboard using BICS remote app.", "RefUrl": "/notes/1783878"}, {"RefNumber": "1768970", "RefComponent": "BW-BEX-ET-WJR-ITM-FL", "RefTitle": "Error while using OPEN_DIALOG_SAVE_LOCAL_VIEW command.", "RefUrl": "/notes/1768970"}, {"RefNumber": "1751390", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: Customer Language on RFC (JCo) Communication", "RefUrl": "/notes/1751390"}, {"RefNumber": "1751318", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Performance: variables filled by pre-query in Java Web", "RefUrl": "/notes/1751318"}, {"RefNumber": "1751295", "RefComponent": "BI-RA-BICS", "RefTitle": "Synchronisieren der Variablenwerte vor synchronizeStateVars", "RefUrl": "/notes/1751295"}, {"RefNumber": "1751251", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Sorting by attribute is not working in Java Web Runtime", "RefUrl": "/notes/1751251"}, {"RefNumber": "1751030", "RefComponent": "BI-RA-BICS", "RefTitle": "Zustands XML einer Selektion lässt sich nicht wieder setzen", "RefUrl": "/notes/1751030"}, {"RefNumber": "1750888", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Design limitations of BI services with active hierarchies", "RefUrl": "/notes/1750888"}, {"RefNumber": "1750578", "RefComponent": "BI-RA-BICS", "RefTitle": "Synchronization before retrieving hierarchies of variable", "RefUrl": "/notes/1750578"}, {"RefNumber": "1749659", "RefComponent": "BI-RA-BICS", "RefTitle": "Performance: BICS_PROV_GET_MEMBERS reduction (Java)", "RefUrl": "/notes/1749659"}, {"RefNumber": "1749506", "RefComponent": "BI-RA-BICS", "RefTitle": "Performance: Directly reading back-end capabilities (Java)", "RefUrl": "/notes/1749506"}, {"RefNumber": "1749267", "RefComponent": "BI-RA-BICS", "RefTitle": "Performanz-Probleme bei Anzeige-Attributen", "RefUrl": "/notes/1749267"}, {"RefNumber": "1749168", "RefComponent": "BI-RA-BICS", "RefTitle": "Performance: Reading query text initially lesen (Java)", "RefUrl": "/notes/1749168"}, {"RefNumber": "1749109", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "<PERSON> created while performing 'Edit online' & 'Edit locally'", "RefUrl": "/notes/1749109"}, {"RefNumber": "1748993", "RefComponent": "BI-RA-BICS", "RefTitle": "Levelsichtbarkeit beim Wechsel des Resultset Typen", "RefUrl": "/notes/1748993"}, {"RefNumber": "1748970", "RefComponent": "BI-RA-BICS", "RefTitle": "Neue Formeln nicht sichtbar bei hierarchischen Strukturen", "RefUrl": "/notes/1748970"}, {"RefNumber": "1748934", "RefComponent": "BI-RA-BICS", "RefTitle": "Incorrect display of SAP HANA hierarchies", "RefUrl": "/notes/1748934"}, {"RefNumber": "1748787", "RefComponent": "BI-RA-BICS", "RefTitle": "Fehler beim Submitten eines gemergten Variablencontainers", "RefUrl": "/notes/1748787"}, {"RefNumber": "1748475", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1748475"}, {"RefNumber": "1748459", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Dynamic Input: Restore cell focus doesn't work", "RefUrl": "/notes/1748459"}, {"RefNumber": "1748453", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "MOD Planning: unexpected cells are highlighted", "RefUrl": "/notes/1748453"}, {"RefNumber": "1748254", "RefComponent": "BI-RA-BICS", "RefTitle": "Display setting doesnt change correct on free characteristic", "RefUrl": "/notes/1748254"}, {"RefNumber": "1747979", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Broadcaster offline filter navigation not working", "RefUrl": "/notes/1747979"}, {"RefNumber": "1747939", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Composite note for unified rendering corrections in BW", "RefUrl": "/notes/1747939"}, {"RefNumber": "1747664", "RefComponent": "BI-RA-BICS", "RefTitle": "Incorrect sorting type in query view XML", "RefUrl": "/notes/1747664"}, {"RefNumber": "1746940", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Analysis item document symbols not updated in 7.3x", "RefUrl": "/notes/1746940"}, {"RefNumber": "1746611", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "ODOC 730 : Document list item UI changed in 730 only", "RefUrl": "/notes/1746611"}, {"RefNumber": "1745680", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "ChartItem: Unknown error after export to excel", "RefUrl": "/notes/1745680"}, {"RefNumber": "1745594", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1745594"}, {"RefNumber": "1745250", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Cache warm up for binding: follow-up correction for 1598020", "RefUrl": "/notes/1745250"}, {"RefNumber": "1744912", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1744912"}, {"RefNumber": "1744706", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Order of structure elements and key figures not stored", "RefUrl": "/notes/1744706"}, {"RefNumber": "1744421", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification command - Results rows/totals rows", "RefUrl": "/notes/1744421"}, {"RefNumber": "1744401", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1744401"}, {"RefNumber": "1744304", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Percent (%) not exported when using cumulative calculation", "RefUrl": "/notes/1744304"}, {"RefNumber": "1743305", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1743305"}, {"RefNumber": "1742600", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Bookmark Popup is not resized as per the items length", "RefUrl": "/notes/1742600"}, {"RefNumber": "1742571", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Selector: New search functionality", "RefUrl": "/notes/1742571"}, {"RefNumber": "1740884", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Java Web: block selection in grid controls (Shift-Key)", "RefUrl": "/notes/1740884"}, {"RefNumber": "1739748", "RefComponent": "BI-RA-BICS", "RefTitle": "Error when undoing several steps at the same time", "RefUrl": "/notes/1739748"}, {"RefNumber": "1739716", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1739716"}, {"RefNumber": "1739624", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "ChartItem: Generating the chart as JPEG", "RefUrl": "/notes/1739624"}, {"RefNumber": "1739597", "RefComponent": "BI-RA-BICS", "RefTitle": "Incorrect use of decimal separators in a formula", "RefUrl": "/notes/1739597"}, {"RefNumber": "1739211", "RefComponent": "BI-RA-BICS", "RefTitle": "Variant service is reused for the same query", "RefUrl": "/notes/1739211"}, {"RefNumber": "1739187", "RefComponent": "BI-RA-BICS", "RefTitle": "Object Service: Call if readProperties are supported", "RefUrl": "/notes/1739187"}, {"RefNumber": "1739081", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BEx Web 7.3x: Error while generating HTML after sorting", "RefUrl": "/notes/1739081"}, {"RefNumber": "1737891", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "730:Changes to policy domain authentication for BICS remote.", "RefUrl": "/notes/1737891"}, {"RefNumber": "1737499", "RefComponent": "BI-RA-BICS", "RefTitle": "Incorrect result for percent calculation", "RefUrl": "/notes/1737499"}, {"RefNumber": "1737417", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "BEx Web 7.x: upload selections from file or clipboard", "RefUrl": "/notes/1737417"}, {"RefNumber": "1736964", "RefComponent": "BI-RA-BICS", "RefTitle": "Null pointer for symmetric drill and result set size limit", "RefUrl": "/notes/1736964"}, {"RefNumber": "1736195", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x SPS7/P30: Report Item: Chart Customizing Issue", "RefUrl": "/notes/1736195"}, {"RefNumber": "1736098", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Drill State is lost after adding a local formula in Java", "RefUrl": "/notes/1736098"}, {"RefNumber": "1736004", "RefComponent": "BI-RA-BICS", "RefTitle": "Detailed types for variables in BICS", "RefUrl": "/notes/1736004"}, {"RefNumber": "1735436", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: Supportdesk: Incorrect warning SPS7/30 on SPS5", "RefUrl": "/notes/1735436"}, {"RefNumber": "1735180", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1735180"}, {"RefNumber": "1734016", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "NumberFormatException in BicsRemoteBex servlet.", "RefUrl": "/notes/1734016"}, {"RefNumber": "1733496", "RefComponent": "BI-RA-BICS", "RefTitle": "Perf.: Reusing planning function and planning sequence JAVA", "RefUrl": "/notes/1733496"}, {"RefNumber": "1733320", "RefComponent": "BI-RA-BICS", "RefTitle": "Error for several excluding selections in the query filter", "RefUrl": "/notes/1733320"}, {"RefNumber": "1733250", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Scrolling: Row selection support", "RefUrl": "/notes/1733250"}, {"RefNumber": "1732996", "RefComponent": "BI-RA-BICS", "RefTitle": "Variable value ambiguous with compound characteristics", "RefUrl": "/notes/1732996"}, {"RefNumber": "1731856", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Document Module: Shows duplicate content on different cells", "RefUrl": "/notes/1731856"}, {"RefNumber": "1730163", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "730:Visible item inside hidden group item is not exported.", "RefUrl": "/notes/1730163"}, {"RefNumber": "1730148", "RefComponent": "BI-RA-BICS", "RefTitle": "Incorrect result set due to two active conditions", "RefUrl": "/notes/1730148"}, {"RefNumber": "1730132", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Lifetime of cached BI Java MIME objects", "RefUrl": "/notes/1730132"}, {"RefNumber": "1729696", "RefComponent": "BI-RA-BICS", "RefTitle": "Setting characteristic context of a condition to hier.leaves", "RefUrl": "/notes/1729696"}, {"RefNumber": "1729629", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Scrolling Module: Stops working after row is dragged out", "RefUrl": "/notes/1729629"}, {"RefNumber": "1729005", "RefComponent": "BI-RA-BICS", "RefTitle": "DSO planning on cells that do not exist (NONEXT) JAVA", "RefUrl": "/notes/1729005"}, {"RefNumber": "1728063", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1728063"}, {"RefNumber": "1728046", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "730: <PERSON><PERSON><PERSON> not delivered from reusable script item.", "RefUrl": "/notes/1728046"}, {"RefNumber": "1727905", "RefComponent": "BI-RA-BICS", "RefTitle": "Reduction of the bics_cons_get_rsadmin_param RFC calls", "RefUrl": "/notes/1727905"}, {"RefNumber": "1727134", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1727134"}, {"RefNumber": "1726912", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx WEb: Export CSV with Broadcasting: UTF-16 as default", "RefUrl": "/notes/1726912"}, {"RefNumber": "1726785", "RefComponent": "BI-RA-BICS", "RefTitle": "Table/View cannot be found in SAP hierarchies", "RefUrl": "/notes/1726785"}, {"RefNumber": "1726145", "RefComponent": "BI-RA-BICS", "RefTitle": "SAP HANA hierarchy appears in duplicate", "RefUrl": "/notes/1726145"}, {"RefNumber": "1725344", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1725344"}, {"RefNumber": "1724953", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Report Item Export with ADS PDF - Blank Pages", "RefUrl": "/notes/1724953"}, {"RefNumber": "1724848", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1724848"}, {"RefNumber": "1724597", "RefComponent": "BI-RA-BICS", "RefTitle": "Determine axis extent without collecting the result set", "RefUrl": "/notes/1724597"}, {"RefNumber": "1724447", "RefComponent": "BI-RA-BICS", "RefTitle": "Changes in the number of decimal places are not displayed", "RefUrl": "/notes/1724447"}, {"RefNumber": "1723057", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Hidden info objects are visible in 7.30", "RefUrl": "/notes/1723057"}, {"RefNumber": "1723007", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "'TARTITLE' error message is thrown while loading KM Bookmark", "RefUrl": "/notes/1723007"}, {"RefNumber": "1722984", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Keyfigure Structure sorting is not reflected in Java Web", "RefUrl": "/notes/1722984"}, {"RefNumber": "1722983", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Recommendations to resolve 'NO ESID FOUND' error", "RefUrl": "/notes/1722983"}, {"RefNumber": "1722776", "RefComponent": "BI-RA-BICS", "RefTitle": "Validation enhancement of the SET_SELECTION_STATE command", "RefUrl": "/notes/1722776"}, {"RefNumber": "1722186", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1722186"}, {"RefNumber": "1721864", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.x: KM Documents Integration: Performance Issues", "RefUrl": "/notes/1721864"}, {"RefNumber": "1721567", "RefComponent": "BI-RA-BICS", "RefTitle": "Structure does not return hierarchy", "RefUrl": "/notes/1721567"}, {"RefNumber": "1721338", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Limitations in creating and displying Documents in BEx Web", "RefUrl": "/notes/1721338"}, {"RefNumber": "1720519", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Termination when sorting on 7.x systems", "RefUrl": "/notes/1720519"}, {"RefNumber": "1720423", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1720423"}, {"RefNumber": "1719029", "RefComponent": "BI-RA-BICS", "RefTitle": "ClassCastException when using hierarchy node variant", "RefUrl": "/notes/1719029"}, {"RefNumber": "1718739", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Test Automation with BEx Web Traces Validation", "RefUrl": "/notes/1718739"}, {"RefNumber": "1718699", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Scrolling Module: Invalid Argument when all rows are removed", "RefUrl": "/notes/1718699"}, {"RefNumber": "1718527", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BEx Web 7.3x: Analysis item document symbols not updated", "RefUrl": "/notes/1718527"}, {"RefNumber": "1718206", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.30: Memory reduction in Xcelsius scenarios", "RefUrl": "/notes/1718206"}, {"RefNumber": "1718205", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Performance improvement by synchronisation", "RefUrl": "/notes/1718205"}, {"RefNumber": "1717916", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Broadcast to excel contains hierarchy images.", "RefUrl": "/notes/1717916"}, {"RefNumber": "1717836", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "BEx Web 7.3x (SPS7#P20): PDF Export scaling and headers", "RefUrl": "/notes/1717836"}, {"RefNumber": "1717730", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1717730"}, {"RefNumber": "1717722", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1717722"}, {"RefNumber": "1717511", "RefComponent": "BI-RA-BICS", "RefTitle": "Statistic even 13003 is not terminated", "RefUrl": "/notes/1717511"}, {"RefNumber": "1717368", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BEx Broadcaster: characteristic has no master data warning", "RefUrl": "/notes/1717368"}, {"RefNumber": "1717226", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1717226"}, {"RefNumber": "1716223", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Comments are getting locked automatically with KM repository", "RefUrl": "/notes/1716223"}, {"RefNumber": "1716169", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Javascript error appears after closing Document dialog", "RefUrl": "/notes/1716169"}, {"RefNumber": "1715750", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "VC 7.30: too many result rows in case of hierarchy", "RefUrl": "/notes/1715750"}, {"RefNumber": "1715642", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1715642"}, {"RefNumber": "1715006", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1715006"}, {"RefNumber": "1714871", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Performance: compound characteristics variables in Java", "RefUrl": "/notes/1714871"}, {"RefNumber": "1714736", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Scrolling Module: Alternative way to enable/disable", "RefUrl": "/notes/1714736"}, {"RefNumber": "1713835", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Scrolling Module: Wrong tab order on input fields", "RefUrl": "/notes/1713835"}, {"RefNumber": "1713495", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Context Menu: Missing translations planning and formulas", "RefUrl": "/notes/1713495"}, {"RefNumber": "1713344", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Error text missing in the Document Browser Dialog", "RefUrl": "/notes/1713344"}, {"RefNumber": "1712476", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1712476"}, {"RefNumber": "1711936", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Limit number of member selections", "RefUrl": "/notes/1711936"}, {"RefNumber": "1710626", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Safety belts and memory availability assurance", "RefUrl": "/notes/1710626"}, {"RefNumber": "1710528", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Java Objects Reuse for GC run reduction", "RefUrl": "/notes/1710528"}, {"RefNumber": "1710200", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1710200"}, {"RefNumber": "1709614", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1709614"}, {"RefNumber": "1709094", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1709094"}, {"RefNumber": "1709007", "RefComponent": "BI-RA-BICS", "RefTitle": "Error when setting a global variant that is already deleted", "RefUrl": "/notes/1709007"}, {"RefNumber": "1708919", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Template dialog functionality", "RefUrl": "/notes/1708919"}, {"RefNumber": "1708700", "RefComponent": "BI-RA-BICS", "RefTitle": "Same gobal variable can be saved multiple times", "RefUrl": "/notes/1708700"}, {"RefNumber": "1706806", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Overlapping of Texts & Items in java web in FireFox\\Safari", "RefUrl": "/notes/1706806"}, {"RefNumber": "1704854", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1704854"}, {"RefNumber": "1704316", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1704316"}, {"RefNumber": "1703966", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Limit number of user applications", "RefUrl": "/notes/1703966"}, {"RefNumber": "1703363", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1703363"}, {"RefNumber": "1703138", "RefComponent": "BI-RA-BICS", "RefTitle": "Problem with bookmark after manually sorting hierarchy nodes", "RefUrl": "/notes/1703138"}, {"RefNumber": "1702994", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Export and Template Refresh Chart Disapearing", "RefUrl": "/notes/1702994"}, {"RefNumber": "1702469", "RefComponent": "BI-RA-BICS", "RefTitle": "Member cache does not work when fetching permanent object", "RefUrl": "/notes/1702469"}, {"RefNumber": "1701369", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Error in service ITEM_TEXT_SERVICE while opening a template", "RefUrl": "/notes/1701369"}, {"RefNumber": "1700878", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Improper button size in the selector dialog with Firefox.", "RefUrl": "/notes/1700878"}, {"RefNumber": "1700732", "RefComponent": "BI-RA-BICS", "RefTitle": "Performance: Querying whether filter contains nodes & leaves", "RefUrl": "/notes/1700732"}, {"RefNumber": "1699942", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "NW 730:Exception for back to start or back navigation.", "RefUrl": "/notes/1699942"}, {"RefNumber": "1699440", "RefComponent": "BW-BEX-ET-WJR-DIA-OS", "RefTitle": "java.lang.NullPointerException occurs while closing dialog", "RefUrl": "/notes/1699440"}, {"RefNumber": "1699153", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1699153"}, {"RefNumber": "1698784", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Justifiable lock scenario:locked owner name is shown as null", "RefUrl": "/notes/1698784"}, {"RefNumber": "1698072", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Application state changes after OK_TEMPLATE_DIALOG command", "RefUrl": "/notes/1698072"}, {"RefNumber": "1697539", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.30: Memory Usage Reduction on Export Scenarios", "RefUrl": "/notes/1697539"}, {"RefNumber": "1697196", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Report Item settings are not taken while exporting in 730", "RefUrl": "/notes/1697196"}, {"RefNumber": "1696718", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "Xcelsius: <PERSON><PERSON><PERSON> message logged for the characteristic.", "RefUrl": "/notes/1696718"}, {"RefNumber": "1696706", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Assertion: selector<PERSON><PERSON><PERSON><PERSON><PERSON> is already assigned", "RefUrl": "/notes/1696706"}, {"RefNumber": "1695386", "RefComponent": "BI-RA-BICS", "RefTitle": "Creation of VariantCatalogService in different system", "RefUrl": "/notes/1695386"}, {"RefNumber": "1695304", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1695304"}, {"RefNumber": "1695218", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Module Scrolling: Javascript error if used on multiple items", "RefUrl": "/notes/1695218"}, {"RefNumber": "1694649", "RefComponent": "BI-RA-BICS", "RefTitle": "Search for members using characteristic attributes (Java)", "RefUrl": "/notes/1694649"}, {"RefNumber": "1694035", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Not possible to edit comment due to LOCK issue in 730", "RefUrl": "/notes/1694035"}, {"RefNumber": "1694016", "RefComponent": "BI-RA-BICS", "RefTitle": "Feature: Level visibility", "RefUrl": "/notes/1694016"}, {"RefNumber": "1693478", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Performance: improvement of info object member cache in Java", "RefUrl": "/notes/1693478"}, {"RefNumber": "1693333", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Invalid rendering of scroll bar after an export operation", "RefUrl": "/notes/1693333"}, {"RefNumber": "1693100", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "ChartItem: Tooltips do not contain scaling factors", "RefUrl": "/notes/1693100"}, {"RefNumber": "1692658", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1692658"}, {"RefNumber": "1691719", "RefComponent": "BI-RA-BICS", "RefTitle": "Conditions not synchronized with the provider for windowing", "RefUrl": "/notes/1691719"}, {"RefNumber": "1691681", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1691681"}, {"RefNumber": "1691626", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1691626"}, {"RefNumber": "1691004", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Unexpected hierarchy expand in selector dialog.", "RefUrl": "/notes/1691004"}, {"RefNumber": "1690459", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "JAVA BICS 730: Key Figure state lost after filtering", "RefUrl": "/notes/1690459"}, {"RefNumber": "1690127", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1690127"}, {"RefNumber": "1689984", "RefComponent": "BW-BEX-ET-WJR-DIA-VA", "RefTitle": "Sel. option variable value entered with '*' will show a '\\\\'", "RefUrl": "/notes/1689984"}, {"RefNumber": "1689880", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1689880"}, {"RefNumber": "1689808", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "ADS not working in 730 after upgrade", "RefUrl": "/notes/1689808"}, {"RefNumber": "1689248", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Documents upload does not work after the upgrade to BW 7.3", "RefUrl": "/notes/1689248"}, {"RefNumber": "1689060", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: Thread <PERSON> in class ObjectFactory", "RefUrl": "/notes/1689060"}, {"RefNumber": "1689059", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Unauthorized modification of displayed content in BEx Web", "RefUrl": "/notes/1689059"}, {"RefNumber": "1688919", "RefComponent": "BI-RA-BICS", "RefTitle": "Providing the capability information of the provider", "RefUrl": "/notes/1688919"}, {"RefNumber": "1688741", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Comments are not displayed for some set of selections.", "RefUrl": "/notes/1688741"}, {"RefNumber": "1688678", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Memory Usage Improvements for Web Applications", "RefUrl": "/notes/1688678"}, {"RefNumber": "1688613", "RefComponent": "BI-RA-BICS", "RefTitle": "Zero for restricted key figures not recognized in SAP HANA", "RefUrl": "/notes/1688613"}, {"RefNumber": "1688398", "RefComponent": "BI-RA-BICS", "RefTitle": "Deletion of formula for clone del. formula of clone parent", "RefUrl": "/notes/1688398"}, {"RefNumber": "1688338", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Snippet rendering not working after TRANSFER_STATE command.", "RefUrl": "/notes/1688338"}, {"RefNumber": "1688217", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "ConcurrentModificationException for iView with ContentLink", "RefUrl": "/notes/1688217"}, {"RefNumber": "1687803", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1687803"}, {"RefNumber": "1687756", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Incorrect background display of charts after upgrade to 730", "RefUrl": "/notes/1687756"}, {"RefNumber": "1686374", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1686374"}, {"RefNumber": "1686040", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "Error 2032 for multiple connections to same BW query.", "RefUrl": "/notes/1686040"}, {"RefNumber": "1685840", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Drag and drop only affects data not column header", "RefUrl": "/notes/1685840"}, {"RefNumber": "1685766", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1685766"}, {"RefNumber": "1685407", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Nullpointer Exception occurs while performing export", "RefUrl": "/notes/1685407"}, {"RefNumber": "1685153", "RefComponent": "BI-RA-BICS", "RefTitle": "Feature: method setHierarchy and validate the selection", "RefUrl": "/notes/1685153"}, {"RefNumber": "1684950", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1684950"}, {"RefNumber": "1683985", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Issue with size of New document and Document browser dialog", "RefUrl": "/notes/1683985"}, {"RefNumber": "1683948", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Comments are not saved and displayed correctly", "RefUrl": "/notes/1683948"}, {"RefNumber": "1683896", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Wrong display of variables from info field item in PDF.", "RefUrl": "/notes/1683896"}, {"RefNumber": "1683605", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Zero values with units looses units when exporting to Excel", "RefUrl": "/notes/1683605"}, {"RefNumber": "1683520", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification DynamicGrid for Hichert design", "RefUrl": "/notes/1683520"}, {"RefNumber": "1683024", "RefComponent": "BI-RA-BICS", "RefTitle": "Resetting start variant does not work", "RefUrl": "/notes/1683024"}, {"RefNumber": "1682975", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Modification modules show different behaviour during Export", "RefUrl": "/notes/1682975"}, {"RefNumber": "1682779", "RefComponent": "BI-RA-BICS", "RefTitle": "Error with formula double variable in quey view", "RefUrl": "/notes/1682779"}, {"RefNumber": "1682582", "RefComponent": "BI-RA-BICS", "RefTitle": "ABEND error when you query the value of a node", "RefUrl": "/notes/1682582"}, {"RefNumber": "1682487", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1682487"}, {"RefNumber": "1682037", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Java Web 7.30: time zone settings are not taken into account", "RefUrl": "/notes/1682037"}, {"RefNumber": "1681475", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1681475"}, {"RefNumber": "1680131", "RefComponent": "BI-RA-BICS", "RefTitle": "Termination when sorting on 7.3x systems", "RefUrl": "/notes/1680131"}, {"RefNumber": "1679965", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1679965"}, {"RefNumber": "1679834", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1679834"}, {"RefNumber": "1679787", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Drag & Drop operation on input ready query causes exception", "RefUrl": "/notes/1679787"}, {"RefNumber": "1679761", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Report Item: Gridlines missing or no white background", "RefUrl": "/notes/1679761"}, {"RefNumber": "1679416", "RefComponent": "BI-RA-BICS", "RefTitle": "Enhancement of the variants for Analysis Office and OLAP", "RefUrl": "/notes/1679416"}, {"RefNumber": "1679340", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "RIG: RemoveUnit module wrong output when space separator", "RefUrl": "/notes/1679340"}, {"RefNumber": "1677745", "RefComponent": "BI-RA-BICS", "RefTitle": "getInputString() Component does not use space in escape seq", "RefUrl": "/notes/1677745"}, {"RefNumber": "1676812", "RefComponent": "BI-RA-BICS", "RefTitle": "Native support of level visibility and leaf visibility", "RefUrl": "/notes/1676812"}, {"RefNumber": "1676554", "RefComponent": "BI-RA-BICS", "RefTitle": "Result set is incorrect after you expand a hierarchy", "RefUrl": "/notes/1676554"}, {"RefNumber": "1676337", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Export to Excel: Wrong value exported for 01/01/1901", "RefUrl": "/notes/1676337"}, {"RefNumber": "1674557", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "RIG: <PERSON><PERSON> does not work in Firefox", "RefUrl": "/notes/1674557"}, {"RefNumber": "1672941", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Javascript during drag & drop after export to Excel or PDF", "RefUrl": "/notes/1672941"}, {"RefNumber": "1672858", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Invalid Base64-encoded character encountered - WD ALV Print", "RefUrl": "/notes/1672858"}, {"RefNumber": "1671894", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Java Web 7.30: Document icons missing for key figure values", "RefUrl": "/notes/1671894"}, {"RefNumber": "1669924", "RefComponent": "BI-RA-BICS", "RefTitle": "Native support for the list calculation", "RefUrl": "/notes/1669924"}, {"RefNumber": "1669888", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Modification Format to hide document icon", "RefUrl": "/notes/1669888"}, {"RefNumber": "1669597", "RefComponent": "BI-RA-BICS", "RefTitle": "Hash sign when using the presentation \"Key (Not Compounded)\"", "RefUrl": "/notes/1669597"}, {"RefNumber": "1669587", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "ENTRIES_MAXCOUNT set to zero does not show all values.", "RefUrl": "/notes/1669587"}, {"RefNumber": "1667925", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Result set is invalid because you set a custom parameter", "RefUrl": "/notes/1667925"}, {"RefNumber": "1667463", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1667463"}, {"RefNumber": "1667300", "RefComponent": "BI-RA-BICS", "RefTitle": "Exception when deleting a formula that is in the filter", "RefUrl": "/notes/1667300"}, {"RefNumber": "1667230", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Query views are not deleted", "RefUrl": "/notes/1667230"}, {"RefNumber": "1667077", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification comment - Export with BLOCK_ROWS_SIZE", "RefUrl": "/notes/1667077"}, {"RefNumber": "1666960", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modif. docs - Display according to currentness", "RefUrl": "/notes/1666960"}, {"RefNumber": "1666898", "RefComponent": "BI-RA-BICS", "RefTitle": "Object Services: Enhancement for workspaces", "RefUrl": "/notes/1666898"}, {"RefNumber": "1666825", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification command - JavaScript by clicking mouse", "RefUrl": "/notes/1666825"}, {"RefNumber": "1666728", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Value \"\" of parameter \"REQUEST_ID\" could not be converted in", "RefUrl": "/notes/1666728"}, {"RefNumber": "1666354", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1666354"}, {"RefNumber": "1665944", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Module Scrolling, Planning & Input sporatic Javascript error", "RefUrl": "/notes/1665944"}, {"RefNumber": "1664331", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Unknown Error after executing a query with conditions", "RefUrl": "/notes/1664331"}, {"RefNumber": "1663366", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Percent sign not displayed or inconsistent for some cells", "RefUrl": "/notes/1663366"}, {"RefNumber": "1662669", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: Export of Conditions and Exception Items Fails", "RefUrl": "/notes/1662669"}, {"RefNumber": "1662420", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1662420"}, {"RefNumber": "1662235", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Error while exporting a Bookmark to Excel", "RefUrl": "/notes/1662235"}, {"RefNumber": "1662206", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Module Scrolling: Input cells have wrong background color", "RefUrl": "/notes/1662206"}, {"RefNumber": "1662108", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1662108"}, {"RefNumber": "1661165", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Module Document Content: row references / empty cells", "RefUrl": "/notes/1661165"}, {"RefNumber": "1660994", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Module Copy&Paste: Class Cast Exception caused by module", "RefUrl": "/notes/1660994"}, {"RefNumber": "1660949", "RefComponent": "BI-RA-BICS", "RefTitle": "Drill state is not reset if nodes are collapsed", "RefUrl": "/notes/1660949"}, {"RefNumber": "1660837", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1660837"}, {"RefNumber": "1660680", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1660680"}, {"RefNumber": "1660488", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Export internal mime images to excel", "RefUrl": "/notes/1660488"}, {"RefNumber": "1660417", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Dynamic block column and row size based on number of cells", "RefUrl": "/notes/1660417"}, {"RefNumber": "1659826", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1659826"}, {"RefNumber": "1659590", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Session timeout exception results in XML display", "RefUrl": "/notes/1659590"}, {"RefNumber": "1659251", "RefComponent": "BI-RA-BICS", "RefTitle": "Not possible to undo sorting with broken hierarchies", "RefUrl": "/notes/1659251"}, {"RefNumber": "1658298", "RefComponent": "BI-RA-BICS", "RefTitle": "UnsupportedOperationException when setting the state -XMLs", "RefUrl": "/notes/1658298"}, {"RefNumber": "1657285", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1657285"}, {"RefNumber": "1656845", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1656845"}, {"RefNumber": "1656768", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1656768"}, {"RefNumber": "1656602", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1656602"}, {"RefNumber": "1656517", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "RSBOLAP018 error while saving or executing a template in WAD", "RefUrl": "/notes/1656517"}, {"RefNumber": "1656360", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1656360"}, {"RefNumber": "1656069", "RefComponent": "BI-RA-BICS", "RefTitle": "No termination when timeout occurs for work process", "RefUrl": "/notes/1656069"}, {"RefNumber": "1655853", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1655853"}, {"RefNumber": "1655585", "RefComponent": "BI-RA-BICS", "RefTitle": "Error when collecting the RFC metadata in BICS", "RefUrl": "/notes/1655585"}, {"RefNumber": "1655227", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Modification modules not working as expected during export", "RefUrl": "/notes/1655227"}, {"RefNumber": "1655000", "RefComponent": "BI-RA-BICS", "RefTitle": "ResultSetMx returns an incorrect (old) data cell", "RefUrl": "/notes/1655000"}, {"RefNumber": "1654972", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Bookmark dialog is initially loaded blank without any items", "RefUrl": "/notes/1654972"}, {"RefNumber": "1653742", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Unexpected drill operation state loss in Java Web Runtime", "RefUrl": "/notes/1653742"}, {"RefNumber": "1653260", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1653260"}, {"RefNumber": "1653153", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1653153"}, {"RefNumber": "1652480", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1652480"}, {"RefNumber": "1652425", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1652425"}, {"RefNumber": "1651972", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1651972"}, {"RefNumber": "1651968", "RefComponent": "BW-BEX-ET-WJR-BOE", "RefTitle": "Bi Web Applications in BI 4.0: Logon with Windows AD", "RefUrl": "/notes/1651968"}, {"RefNumber": "1651050", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1651050"}, {"RefNumber": "1651044", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1651044"}, {"RefNumber": "1650992", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1650992"}, {"RefNumber": "1650853", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1650853"}, {"RefNumber": "1650395", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Navigation Pane Item: Sorting of free characteritics", "RefUrl": "/notes/1650395"}, {"RefNumber": "1649606", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Dot displayed for empty values in grid", "RefUrl": "/notes/1649606"}, {"RefNumber": "1649202", "RefComponent": "BI-RA-BICS", "RefTitle": "Querying whether a planning function can process deltas", "RefUrl": "/notes/1649202"}, {"RefNumber": "1648588", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "ODOC:Date is not exported for Document Items", "RefUrl": "/notes/1648588"}, {"RefNumber": "1648389", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Modification Format background color changes font family", "RefUrl": "/notes/1648389"}, {"RefNumber": "1647346", "RefComponent": "BI-RA-BICS", "RefTitle": "Object Services property without localization and format", "RefUrl": "/notes/1647346"}, {"RefNumber": "1646366", "RefComponent": "BI-RA-BICS", "RefTitle": "ClassCastException when you use \"Low speed connection\"", "RefUrl": "/notes/1646366"}, {"RefNumber": "1645842", "RefComponent": "BI-RA-BICS", "RefTitle": "Error when excluding hier. node in characteristic context", "RefUrl": "/notes/1645842"}, {"RefNumber": "1645590", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Java/Server SPs dependencies (and SupportDeskTool)", "RefUrl": "/notes/1645590"}, {"RefNumber": "1644772", "RefComponent": "BI-RA-BICS", "RefTitle": "Cross-classified table sorting incorr. after deserialization", "RefUrl": "/notes/1644772"}, {"RefNumber": "1644770", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Dynamic Input fields for input ready data cells", "RefUrl": "/notes/1644770"}, {"RefNumber": "1643608", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "7.30: Close button displayed in formatted dialog window", "RefUrl": "/notes/1643608"}, {"RefNumber": "1642143", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Modification Format fails on info objects and structures", "RefUrl": "/notes/1642143"}, {"RefNumber": "1642017", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Xcelcius: blank values in filter lead to empty result", "RefUrl": "/notes/1642017"}, {"RefNumber": "1641053", "RefComponent": "BW-BEX-ET-WJR-DIA-VA", "RefTitle": "Sel. option variable value entered with '*' will show a '/'", "RefUrl": "/notes/1641053"}, {"RefNumber": "1640307", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web: Issue on Set Item Parameters on Items with Commands", "RefUrl": "/notes/1640307"}, {"RefNumber": "1640137", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Sorting results are not correct", "RefUrl": "/notes/1640137"}, {"RefNumber": "1639592", "RefComponent": "BW-BEX-ET-WJR-GRAPH", "RefTitle": "Size of the chart is increased when no data available", "RefUrl": "/notes/1639592"}, {"RefNumber": "1638319", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Web Session Timeout not Working", "RefUrl": "/notes/1638319"}, {"RefNumber": "1637675", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Date appears as ####### after export to excel", "RefUrl": "/notes/1637675"}, {"RefNumber": "1637571", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification: Export comments", "RefUrl": "/notes/1637571"}, {"RefNumber": "1637197", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS:Query displays key instead of empty text for attributes", "RefUrl": "/notes/1637197"}, {"RefNumber": "1636106", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Missing include/exclude button in selector dialog in 730", "RefUrl": "/notes/1636106"}, {"RefNumber": "1634350", "RefComponent": "BW-BEX-ET-WJR-EP", "RefTitle": "RSWR_SYSTEM_ALIAS_CHECK cause exception on BI Java", "RefUrl": "/notes/1634350"}, {"RefNumber": "1634117", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Display of null attributes in HANA", "RefUrl": "/notes/1634117"}, {"RefNumber": "1632580", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "BEx Web 7.30 Report Item: Misisng Export of MIMEs from MIME", "RefUrl": "/notes/1632580"}, {"RefNumber": "1631584", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "BEx Web 7.30: Template Include Item is exported twice", "RefUrl": "/notes/1631584"}, {"RefNumber": "1629251", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx: Variant Catalogs are not correct assigned in NW 7.30", "RefUrl": "/notes/1629251"}, {"RefNumber": "1629171", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1629171"}, {"RefNumber": "1627409", "RefComponent": "BI-RA-BICS", "RefTitle": "Exception when resetting an expanded hierarchy", "RefUrl": "/notes/1627409"}, {"RefNumber": "1623428", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Export to PDF hangs for info field item.", "RefUrl": "/notes/1623428"}, {"RefNumber": "1622746", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Standard images are exported to excel in 730", "RefUrl": "/notes/1622746"}, {"RefNumber": "1622596", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Items are not correctly exported to Excel in 730", "RefUrl": "/notes/1622596"}, {"RefNumber": "1622475", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Performance: minor improvement in member access", "RefUrl": "/notes/1622475"}, {"RefNumber": "1622134", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Safety belt: Results quantity is too large (Excel/PDF)", "RefUrl": "/notes/1622134"}, {"RefNumber": "1621628", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Hidden group item is getting exported", "RefUrl": "/notes/1621628"}, {"RefNumber": "1620645", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1620645"}, {"RefNumber": "1620394", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: Corrections in Unified Rendering Libraries", "RefUrl": "/notes/1620394"}, {"RefNumber": "1619455", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Select All option missing in the selector dialog", "RefUrl": "/notes/1619455"}, {"RefNumber": "1618536", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Repeat column header does not work in Broadcast to PDF.", "RefUrl": "/notes/1618536"}, {"RefNumber": "1618051", "RefComponent": "BW-BEX-ET-WJR-DIA-OS", "RefTitle": "Empty description in \"Save As\" dialog results in overwrite", "RefUrl": "/notes/1618051"}, {"RefNumber": "1617936", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Save query view missing template id in backend table.", "RefUrl": "/notes/1617936"}, {"RefNumber": "1617840", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "SAP NW BI JAVA 7.30/BOE 4.0, context menu, row/column lock", "RefUrl": "/notes/1617840"}, {"RefNumber": "1617533", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Drilling with errors in combination with zero suppression", "RefUrl": "/notes/1617533"}, {"RefNumber": "1617318", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Invalid snippets on web template after doing export", "RefUrl": "/notes/1617318"}, {"RefNumber": "1617004", "RefComponent": "BI-RA-BICS", "RefTitle": "Cell locking administration in the backend", "RefUrl": "/notes/1617004"}, {"RefNumber": "1617002", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "NullPointerException for rpts w/ref. to HANA Analytical View", "RefUrl": "/notes/1617002"}, {"RefNumber": "1614235", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "BEx Web 7.30 Report Item: MIMEs from MIME repository missing", "RefUrl": "/notes/1614235"}, {"RefNumber": "1614013", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1614013"}, {"RefNumber": "1613879", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1613879"}, {"RefNumber": "1613110", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Text Item is not wrapped while exporting", "RefUrl": "/notes/1613110"}, {"RefNumber": "1613090", "RefComponent": "BI-RA-BICS", "RefTitle": "Incorrect values when you collapse a hierarchical structure", "RefUrl": "/notes/1613090"}, {"RefNumber": "1612599", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "Error #2032 executing dashboards after node restart", "RefUrl": "/notes/1612599"}, {"RefNumber": "1612390", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Java: Scroll<PERSON> position incorrect for G<PERSON> and selector", "RefUrl": "/notes/1612390"}, {"RefNumber": "1610963", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Modification Column Width incorrect for universal hierarchy", "RefUrl": "/notes/1610963"}, {"RefNumber": "1610656", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Enhancement for including bookmark URL while export", "RefUrl": "/notes/1610656"}, {"RefNumber": "1610325", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Termination in unrestricted overall result list calculation", "RefUrl": "/notes/1610325"}, {"RefNumber": "1610036", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Cancelation with sorting button for Attributes", "RefUrl": "/notes/1610036"}, {"RefNumber": "1608398", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Default width of items with active tray has changed with 730", "RefUrl": "/notes/1608398"}, {"RefNumber": "1607637", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: Spinning Wheel disappears too early in AJAX mode", "RefUrl": "/notes/1607637"}, {"RefNumber": "1607636", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web: Language for Web Applicatins opened in new window", "RefUrl": "/notes/1607636"}, {"RefNumber": "1607635", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web: Memory Leak on Export Scenarios (XLS, PDF, ...)", "RefUrl": "/notes/1607635"}, {"RefNumber": "1607342", "RefComponent": "BW-BEX-ET-WJR-DIA", "RefTitle": "Loading animation disappears shortly submit variable screen", "RefUrl": "/notes/1607342"}, {"RefNumber": "1606874", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Hidden container layout item is getting exported", "RefUrl": "/notes/1606874"}, {"RefNumber": "1606737", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "ODOC: Export formatting issue with respect to document items", "RefUrl": "/notes/1606737"}, {"RefNumber": "1605911", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Cells are merged by default in the broadcasted excel", "RefUrl": "/notes/1605911"}, {"RefNumber": "1603113", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "730 JAVA BI: My portfolio is missing in Save dialog", "RefUrl": "/notes/1603113"}, {"RefNumber": "1602447", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification termination when you merge columns", "RefUrl": "/notes/1602447"}, {"RefNumber": "1601504", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web >= 7.30: Broadcaster exports incorrectly containers", "RefUrl": "/notes/1601504"}, {"RefNumber": "1601289", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Expanding of Universal Hierarchy not working in Java Runtime", "RefUrl": "/notes/1601289"}, {"RefNumber": "1601080", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification: Column headers / No data", "RefUrl": "/notes/1601080"}, {"RefNumber": "1600962", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: URL Parameter LANGUAGE not always working", "RefUrl": "/notes/1600962"}, {"RefNumber": "1600287", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "LIMIT EXCEED error during BW connection from BOE 4.0", "RefUrl": "/notes/1600287"}, {"RefNumber": "1599624", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "Dashboard does not show any value if it contains only KFs", "RefUrl": "/notes/1599624"}, {"RefNumber": "1598986", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "All hidden item are exported after implementing note 1587492", "RefUrl": "/notes/1598986"}, {"RefNumber": "1598498", "RefComponent": "BI-RA-BICS", "RefTitle": "Cells are not ready for input after you collapse a structure", "RefUrl": "/notes/1598498"}, {"RefNumber": "1598073", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Null pointer exception when executing a query", "RefUrl": "/notes/1598073"}, {"RefNumber": "1598020", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Performance: selection state binding in Java Web Template", "RefUrl": "/notes/1598020"}, {"RefNumber": "1597726", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "Xcelsius dashboard does not load", "RefUrl": "/notes/1597726"}, {"RefNumber": "1597308", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Incorrect variants displayed when you execute a query", "RefUrl": "/notes/1597308"}, {"RefNumber": "1597173", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Backward navigation selectorData<PERSON>rov<PERSON> is already assigned", "RefUrl": "/notes/1597173"}, {"RefNumber": "1596751", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Saving bookmark functionality results in peculiar behaviour", "RefUrl": "/notes/1596751"}, {"RefNumber": "1593072", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx web on IE9: JavaScripts error while calling popup dialog", "RefUrl": "/notes/1593072"}, {"RefNumber": "1592991", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Filter Pane Item overlaps in firefox and Safari", "RefUrl": "/notes/1592991"}, {"RefNumber": "1592891", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Filter master data messages shown in Message List Item", "RefUrl": "/notes/1592891"}, {"RefNumber": "1592848", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1592848"}, {"RefNumber": "1592366", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Variable values are lost when bookmark is loaded", "RefUrl": "/notes/1592366"}, {"RefNumber": "1591929", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Java Rutime: Export to excel - Text format for key figures", "RefUrl": "/notes/1591929"}, {"RefNumber": "1591534", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "SET_DATA_CELL_PROPERTIES command on structure node incorrect", "RefUrl": "/notes/1591534"}, {"RefNumber": "1589860", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Performance: multiple variable values in WJR", "RefUrl": "/notes/1589860"}, {"RefNumber": "1589382", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1589382"}, {"RefNumber": "1589123", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Delete link from Show Details-> Action tab window removed", "RefUrl": "/notes/1589123"}, {"RefNumber": "1587492", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Hidden items are not exported after 7.0 to 7.01 upgrade", "RefUrl": "/notes/1587492"}, {"RefNumber": "1587376", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web: Broadcaster in Template Include Item scenario", "RefUrl": "/notes/1587376"}, {"RefNumber": "1585688", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Application hangs when exporting single document item", "RefUrl": "/notes/1585688"}, {"RefNumber": "1585680", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1585680"}, {"RefNumber": "1584387", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Problems with Firefox toolbar button width", "RefUrl": "/notes/1584387"}, {"RefNumber": "1583942", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "THJ: Validity attribute not shown for text nodes in Java Web", "RefUrl": "/notes/1583942"}, {"RefNumber": "1583928", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Javascript error if analysis item is placed inside container", "RefUrl": "/notes/1583928"}, {"RefNumber": "1581923", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: Optimization on Back with Many Data Providers", "RefUrl": "/notes/1581923"}, {"RefNumber": "1581592", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Planning with time entry is incorrect", "RefUrl": "/notes/1581592"}, {"RefNumber": "1581167", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Totals row missing after drill op.on hier.key figure struct.", "RefUrl": "/notes/1581167"}, {"RefNumber": "1579224", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "DuplicateMemberException when you undo a step", "RefUrl": "/notes/1579224"}, {"RefNumber": "1578941", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Java Runtime: issue with pattern selection in ODOC scenario", "RefUrl": "/notes/1578941"}, {"RefNumber": "1578854", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "NullPointerException during error ticket generation.", "RefUrl": "/notes/1578854"}, {"RefNumber": "1577649", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Query with 2 structures and \"Hide\" list calculation", "RefUrl": "/notes/1577649"}, {"RefNumber": "1577293", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Java dump: Hierarchical structure and structure axis", "RefUrl": "/notes/1577293"}, {"RefNumber": "1575297", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Member access issue for dependent variables (compounded characteristics)", "RefUrl": "/notes/1575297"}, {"RefNumber": "1573030", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Web application dumps when STATELESS is set to non-boolean", "RefUrl": "/notes/1573030"}, {"RefNumber": "1572776", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: USE_SNIPPETS & loading bookmarks with new items", "RefUrl": "/notes/1572776"}, {"RefNumber": "1571707", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "SAP NetWeaver BI JAVA 7.30 / BOE 4.0 items are not exported", "RefUrl": "/notes/1571707"}, {"RefNumber": "1571693", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification command - Filter on totals", "RefUrl": "/notes/1571693"}, {"RefNumber": "1571666", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "SAP BI ExportLib: Clipping, linebreaks, accessibility", "RefUrl": "/notes/1571666"}, {"RefNumber": "1571649", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1571649"}, {"RefNumber": "1569787", "RefComponent": "BW-BEX-ET-WJR-DIA-CO", "RefTitle": "Condition Dialog does not validate interval range correctly", "RefUrl": "/notes/1569787"}, {"RefNumber": "1568251", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Sorting is disabled when F4 is called on Structural elements", "RefUrl": "/notes/1568251"}, {"RefNumber": "1568175", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Performance: minor improvement for variants in Java Web", "RefUrl": "/notes/1568175"}, {"RefNumber": "1567416", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NullPointerException for system message without expiry date.", "RefUrl": "/notes/1567416"}, {"RefNumber": "1566055", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "The HTTP Session TimeOut message is not proper in Dashboard", "RefUrl": "/notes/1566055"}, {"RefNumber": "1565796", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Sorting is disabled when F4 is called on Structural elements", "RefUrl": "/notes/1565796"}, {"RefNumber": "1565469", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Error when planning DATE key figures", "RefUrl": "/notes/1565469"}, {"RefNumber": "1565207", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Drag and drop in second row inserts new line in first row", "RefUrl": "/notes/1565207"}, {"RefNumber": "1564517", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Invalid member selection on info object with conversion exit", "RefUrl": "/notes/1564517"}, {"RefNumber": "1563643", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Modification Modules RIG - Javascript error during export", "RefUrl": "/notes/1563643"}, {"RefNumber": "1562554", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Drag and Drop: incorrect behaviour on structural elements", "RefUrl": "/notes/1562554"}, {"RefNumber": "1562205", "RefComponent": "BW-BEX-ET-WJR-GRAPH", "RefTitle": "Map Item not exported to Excel", "RefUrl": "/notes/1562205"}, {"RefNumber": "1561909", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Command SET_ITEM_PARAMETERS with snippets rendering", "RefUrl": "/notes/1561909"}, {"RefNumber": "1561846", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Wildcard entry for SELECTION OPTION variable in selector", "RefUrl": "/notes/1561846"}, {"RefNumber": "1561738", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Wrong dropdown box when drag and drop between filter panes", "RefUrl": "/notes/1561738"}, {"RefNumber": "1561086", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Possible dump by loading a bookmark with an object variable", "RefUrl": "/notes/1561086"}, {"RefNumber": "1560665", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Modification RIG Document Content performance enhancement", "RefUrl": "/notes/1560665"}, {"RefNumber": "1559555", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Java Web Selector dialog has text \"Enter a value for\" twice", "RefUrl": "/notes/1559555"}, {"RefNumber": "1558368", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.30: Corrections in Support Desktool", "RefUrl": "/notes/1558368"}, {"RefNumber": "1558113", "RefComponent": "BW-BEX-ET-WJR-DIA", "RefTitle": "You cannot expand hierarchies in filter selection", "RefUrl": "/notes/1558113"}, {"RefNumber": "1557925", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Export to Excel shows a % sign in calculated key figures", "RefUrl": "/notes/1557925"}, {"RefNumber": "1556545", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Case conversion for filter values on compounded char", "RefUrl": "/notes/1556545"}, {"RefNumber": "1556012", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Visual Composer: \"Problem moving to next record\"", "RefUrl": "/notes/1556012"}, {"RefNumber": "1555612", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Wrong result set size calculation during drill operations", "RefUrl": "/notes/1555612"}, {"RefNumber": "1555154", "RefComponent": "BI-RA-BICS", "RefTitle": "BEx Web 7.30: <PERSON><PERSON> neuen Zeilen bei Planungsquery", "RefUrl": "/notes/1555154"}, {"RefNumber": "1554104", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Close button on Document browser when you click details", "RefUrl": "/notes/1554104"}, {"RefNumber": "1552647", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Scaling factor reset to default while changing formula", "RefUrl": "/notes/1552647"}, {"RefNumber": "1552272", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Modal template dialog can not be closed", "RefUrl": "/notes/1552272"}, {"RefNumber": "1550398", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: Handling of parameter SCRIPT in Web Applications", "RefUrl": "/notes/1550398"}, {"RefNumber": "1549846", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1549846"}, {"RefNumber": "1549559", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: BICS & KM integration: NullPointer in List.clear()", "RefUrl": "/notes/1549559"}, {"RefNumber": "1548840", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Condition state toggling: usability enhancement", "RefUrl": "/notes/1548840"}, {"RefNumber": "1548704", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "RRI:open mode REPLACE_WEB_APPLICATION opens a new window", "RefUrl": "/notes/1548704"}, {"RefNumber": "1548255", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Calculate Based On Results Of Former Details Calculations is", "RefUrl": "/notes/1548255"}, {"RefNumber": "1547871", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.30: Corrections in Support Desktool", "RefUrl": "/notes/1547871"}, {"RefNumber": "1547714", "RefComponent": "BW-BEX-ET-WJR-DIA-VA", "RefTitle": "Parcer error for variable values with special operators", "RefUrl": "/notes/1547714"}, {"RefNumber": "1546971", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Modification Modules RIG - Restore focus planning input cell", "RefUrl": "/notes/1546971"}, {"RefNumber": "1546963", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "BEx Web 7.x: Enhanced Scrolling Module", "RefUrl": "/notes/1546963"}, {"RefNumber": "1546051", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Planning related selection or variable has missing or invalid entries", "RefUrl": "/notes/1546051"}, {"RefNumber": "1546012", "RefComponent": "BW-BEX-ET-WJR-DIA-FM", "RefTitle": "Java ClassCast exception in query (writeStateIntoDom)", "RefUrl": "/notes/1546012"}, {"RefNumber": "1545679", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Result with hierarchy symbol", "RefUrl": "/notes/1545679"}, {"RefNumber": "1545545", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "ODOC: Broadcasting to MHTML displays all columns in doc item", "RefUrl": "/notes/1545545"}, {"RefNumber": "1544415", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Module CopyPaste:pasting more cells than the space available", "RefUrl": "/notes/1544415"}, {"RefNumber": "1543561", "RefComponent": "BW-BEX-ET-WJR-DIA-OS", "RefTitle": "Display settings are incorrectly set in Open dialog", "RefUrl": "/notes/1543561"}, {"RefNumber": "1542355", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web: Removing DEBUG=X parameter for non-admin users", "RefUrl": "/notes/1542355"}, {"RefNumber": "1540416", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1540416"}, {"RefNumber": "1539790", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1539790"}, {"RefNumber": "1538793", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Sorting on local formulas not saved after bookmark or undo", "RefUrl": "/notes/1538793"}, {"RefNumber": "1538753", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "SAP NW BI JAVA 7.x - comments option in context menu", "RefUrl": "/notes/1538753"}, {"RefNumber": "1538640", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "BiNamespace characteristics not displyed in Assignment colum", "RefUrl": "/notes/1538640"}, {"RefNumber": "1538289", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "'X' appears in result cells (Calculate Result Set as \"Hide\")", "RefUrl": "/notes/1538289"}, {"RefNumber": "1538014", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Number of decimal resets to default while changing formula", "RefUrl": "/notes/1538014"}, {"RefNumber": "1537922", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "context menu with reference to selector dataprovider fails", "RefUrl": "/notes/1537922"}, {"RefNumber": "1537039", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "RTL(Right-to-Left) display not supported for PDF,EXCEL & CSV", "RefUrl": "/notes/1537039"}, {"RefNumber": "1536756", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Filter value lost in BACK_TO_PREVIOUS_STATE for DPs >= 2", "RefUrl": "/notes/1536756"}, {"RefNumber": "1536499", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Safari Browser: Adding bookmark to browser favorites", "RefUrl": "/notes/1536499"}, {"RefNumber": "1536476", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Web template execution recording incorrect object name", "RefUrl": "/notes/1536476"}, {"RefNumber": "1536168", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Export huge text fails for single document item", "RefUrl": "/notes/1536168"}, {"RefNumber": "1535651", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Object variables are not restored correctly from a bookmark", "RefUrl": "/notes/1535651"}, {"RefNumber": "1534863", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Tooltip for hierarchies in characteristic properties pane", "RefUrl": "/notes/1534863"}, {"RefNumber": "1534743", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1534743"}, {"RefNumber": "1534100", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Unexpected filter is applied if key contains special chars", "RefUrl": "/notes/1534100"}, {"RefNumber": "1534083", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Nullpointer when time-dependent hierarchies are loaded", "RefUrl": "/notes/1534083"}, {"RefNumber": "1533296", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Module Copy-Paste: Values pasted at incorrect cell", "RefUrl": "/notes/1533296"}, {"RefNumber": "1532816", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Local formula is not deserialzed correctly (bookmark, undo)", "RefUrl": "/notes/1532816"}, {"RefNumber": "1532285", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "ArrayIndexOutOfBoundsException for dashboard execution.", "RefUrl": "/notes/1532285"}, {"RefNumber": "1532017", "RefComponent": "BW-BEX-ET-WJR-EP", "RefTitle": "Order of login modules for the ticket component in the VA", "RefUrl": "/notes/1532017"}, {"RefNumber": "1531740", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification comments", "RefUrl": "/notes/1531740"}, {"RefNumber": "1531694", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Adding Bookmark to Browser Favorites in FPN brings wrong URL", "RefUrl": "/notes/1531694"}, {"RefNumber": "1528687", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Selection screen range value is validated incorrectly", "RefUrl": "/notes/1528687"}, {"RefNumber": "1527393", "RefComponent": "BW-BEX-ET-WJR-EP", "RefTitle": "java.lang.nullpointerexception in support desk tool", "RefUrl": "/notes/1527393"}, {"RefNumber": "1527330", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Display presentation for Current user in Info field item", "RefUrl": "/notes/1527330"}, {"RefNumber": "1527186", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Export to Excel: \"0.0%\" value converted to \"0,0\"", "RefUrl": "/notes/1527186"}, {"RefNumber": "1527184", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Termination during export to PDF or Excel with mod modules", "RefUrl": "/notes/1527184"}, {"RefNumber": "1526880", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "Reverse proxy Config : Redirection to wrong url(Error #2048)", "RefUrl": "/notes/1526880"}, {"RefNumber": "1526708", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "The computation of bounds is not correct for Dashboard", "RefUrl": "/notes/1526708"}, {"RefNumber": "1526485", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "SAP NW BI JAVA 7.30 KM item dialog boxes do not work", "RefUrl": "/notes/1526485"}, {"RefNumber": "1526210", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Export of external MIMES (Intranet/Internet)", "RefUrl": "/notes/1526210"}, {"RefNumber": "1525392", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Planning function: error messages not reset (F4 selector)", "RefUrl": "/notes/1525392"}, {"RefNumber": "1525226", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1525226"}, {"RefNumber": "1524799", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Selection screen not shown with SET_VARIABLES_STATE command", "RefUrl": "/notes/1524799"}, {"RefNumber": "1524296", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "List calculation: Normalize acc next result without subtotal", "RefUrl": "/notes/1524296"}, {"RefNumber": "1524022", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Hierarchy Node with huge number in selector dialog", "RefUrl": "/notes/1524022"}, {"RefNumber": "1523974", "RefComponent": "BW-BEX-ET-WJR-ITM-FL", "RefTitle": "java.lang.ClassCastException in ListBox item", "RefUrl": "/notes/1523974"}, {"RefNumber": "1523854", "RefComponent": "BW-BEX-ET-WJR-ITM-FL", "RefTitle": "Personalization leads to exception with drop down item", "RefUrl": "/notes/1523854"}, {"RefNumber": "1523581", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "ODOC : Close button displayed in formatted dialog window.", "RefUrl": "/notes/1523581"}, {"RefNumber": "1522108", "RefComponent": "BW-BEX-ET-WJR-DIA-VA", "RefTitle": "Parcer error for hierarchy value with special characters", "RefUrl": "/notes/1522108"}, {"RefNumber": "1507068", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Negative value in Calendar tool is incorrectly processed", "RefUrl": "/notes/1507068"}, {"RefNumber": "1507065", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Adding BI Bookmarks to browser favorites in CRM system", "RefUrl": "/notes/1507065"}, {"RefNumber": "1505289", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Display line breaks or multiple spaces on BW Text Item", "RefUrl": "/notes/1505289"}, {"RefNumber": "1489306", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Accessibility: Labels for input fields are read out properly", "RefUrl": "/notes/1489306"}, {"RefNumber": "1479703", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Calendar date is incorrect while invoking a selector dialog", "RefUrl": "/notes/1479703"}, {"RefNumber": "1461256", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Export to Excel: cell format is TEXT instead of CUSTOM", "RefUrl": "/notes/1461256"}, {"RefNumber": "1451171", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Path entries are displayed twice", "RefUrl": "/notes/1451171"}, {"RefNumber": "1437358", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "NullPointerException due to incorrect template definition", "RefUrl": "/notes/1437358"}, {"RefNumber": "1425720", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification: User-defined symbols for exception II", "RefUrl": "/notes/1425720"}, {"RefNumber": "1327345", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Patches for NetWeaver 7.01  BI Java Support Package", "RefUrl": "/notes/1327345"}, {"RefNumber": "1309000", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NW 7.01 and NW 7.02: BI Java Patch Delivery", "RefUrl": "/notes/1309000"}, {"RefNumber": "1177028", "RefComponent": "BW-BEX-ET-WJR-ITM-FL", "RefTitle": "BEx Web NW7.30/BOE4.0: Filter Pane Item: Text Wrapping", "RefUrl": "/notes/1177028"}, {"RefNumber": "1174124", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Condition not used with active axis hierarchy", "RefUrl": "/notes/1174124"}, {"RefNumber": "1163789", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NetWeaver 7.0: BI Java Synchronized Patch Delivery Strategy", "RefUrl": "/notes/1163789"}, {"RefNumber": "1072576", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Frequently Asked Questions: BI Java Support Packages/patches", "RefUrl": "/notes/1072576"}, {"RefNumber": "1069253", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.x: Monitoring server node memory use in OLAP stats", "RefUrl": "/notes/1069253"}, {"RefNumber": "1055581", "RefComponent": "BW", "RefTitle": "Recommendations for Support Package Stacks for BI 7.0", "RefUrl": "/notes/1055581"}, {"RefNumber": "1033246", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NW 7.0 (NW04s): BI Java Synchronized Patch Delivery", "RefUrl": "/notes/1033246"}, {"RefNumber": "1013369", "RefComponent": "BW", "RefTitle": "SAP NetWeaver 7.0 BI - intermediate Support Packages", "RefUrl": "/notes/1013369"}, {"RefNumber": "1011241", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Patches for NetWeaver 2004s BI Java Support Package", "RefUrl": "/notes/1011241"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3312040", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Bex Web 7.x : Paste from Clipboard and Paste from File buttons are not getting visible in the Selector dialog", "RefUrl": "/notes/3312040 "}, {"RefNumber": "3305735", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BEx Query Export to Excel results with wrong values in column for display attribute of type key figure", "RefUrl": "/notes/3305735 "}, {"RefNumber": "3251131", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Java BICS : BEX query export Excel does not recognize date field", "RefUrl": "/notes/3251131 "}, {"RefNumber": "3236891", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS 2.0: Unexpected Sorting on Hierarchy expansion", "RefUrl": "/notes/3236891 "}, {"RefNumber": "3094410", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "BEx Web 7.x+: PDF Export: Empty page After Each Web Item in PDF export with poster layout scenario", "RefUrl": "/notes/3094410 "}, {"RefNumber": "3082838", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "BEx Web 7.x: Open Template, Query, Query View, Report, WB doesn't work with New Broadcaster UI", "RefUrl": "/notes/3082838 "}, {"RefNumber": "3068238", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: Rendering or Blank space issue between the web items in Microsoft Edge browser", "RefUrl": "/notes/3068238 "}, {"RefNumber": "3037323", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "BEx Web 7.3+: Microsoft Edge and Chrome compatibility fixes for MOD Input and Dialog Buttons", "RefUrl": "/notes/3037323 "}, {"RefNumber": "3007032", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BICS: VC Null value error on planning a new line", "RefUrl": "/notes/3007032 "}, {"RefNumber": "2906814", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.x: Java Broadcaster UI wizard: wrong time format and missing spaces in static texts", "RefUrl": "/notes/2906814 "}, {"RefNumber": "2906396", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hidden web items within HTML tags is not exported", "RefUrl": "/notes/2906396 "}, {"RefNumber": "2854760", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Query displays 'Not Assigned' instead of empty text for members", "RefUrl": "/notes/2854760 "}, {"RefNumber": "2842255", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web: Latest known bookmark incorrect for command sequences", "RefUrl": "/notes/2842255 "}, {"RefNumber": "2839087", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.x: Java Broadcaster UI wizard doesn't support bookmarks", "RefUrl": "/notes/2839087 "}, {"RefNumber": "2771273", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Wrong display for compound characteristics (JAVA)", "RefUrl": "/notes/2771273 "}, {"RefNumber": "2773918", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Wrong display for compound characteristics (JAVA) - 2", "RefUrl": "/notes/2773918 "}, {"RefNumber": "2813180", "RefComponent": "BI-RA-BICS-BW", "RefTitle": "BICS: Value help shown for comments and attribute planning data cells", "RefUrl": "/notes/2813180 "}, {"RefNumber": "2798084", "RefComponent": "BI-RA-BICS", "RefTitle": "BICS: Unique header currency disappears when changing value of initial data cell", "RefUrl": "/notes/2798084 "}, {"RefNumber": "2796504", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BEx Web 7.x: Comments and attribute planning raises \"data cell has no master data\"", "RefUrl": "/notes/2796504 "}, {"RefNumber": "2763143", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: Microsoft Edge browser support for BEx Web Analyzer", "RefUrl": "/notes/2763143 "}, {"RefNumber": "2779339", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Input: data cell value change gets lost using parameter RESTORE_CELL_FOCUS in Chrome and Firefox", "RefUrl": "/notes/2779339 "}, {"RefNumber": "2770870", "RefComponent": "BW-BEX-ET-WJR-DIA", "RefTitle": "BEx Web: Bookmark dialog doesn't show content and can't be closed", "RefUrl": "/notes/2770870 "}, {"RefNumber": "2745270", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "BEx Web: Latest known bookmark for export scenario", "RefUrl": "/notes/2745270 "}, {"RefNumber": "2730485", "RefComponent": "BI-RA-BICS", "RefTitle": "BICS: Optimized rounding of very big or very small numeric data cell values", "RefUrl": "/notes/2730485 "}, {"RefNumber": "2719777", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Structure element text search with special characters not working", "RefUrl": "/notes/2719777 "}, {"RefNumber": "2703965", "RefComponent": "BW-BEX-ET-WJR-EP", "RefTitle": "SAP NetWeaver BI Diagnostics & Support Desk Tool is having restricted and limited support in BW/4HANA backend system", "RefUrl": "/notes/2703965 "}, {"RefNumber": "2689730", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web Support for RRI Jump in multiple BW/BI master system", "RefUrl": "/notes/2689730 "}, {"RefNumber": "2689257", "RefComponent": "BI-RA-BICS-BW", "RefTitle": "RIC: JCo optimized locking for parallel execution", "RefUrl": "/notes/2689257 "}, {"RefNumber": "2672571", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web 7.x: External bookmarks support for Firefox and Chrome", "RefUrl": "/notes/2672571 "}, {"RefNumber": "2672275", "RefComponent": "BI-RA-BICS-BW", "RefTitle": "BICS: Changed behavior of calculated key figures in case of universal display hierarchy", "RefUrl": "/notes/2672275 "}, {"RefNumber": "2660520", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Cannot filter characteristic members containing \"\\\" character", "RefUrl": "/notes/2660520 "}, {"RefNumber": "2660925", "RefComponent": "BI-RA-BICS", "RefTitle": "BICS: JCo context not cleared", "RefUrl": "/notes/2660925 "}, {"RefNumber": "2658581", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: IE Standards Mode: Select filter value dialog: paste from file button icon is too big", "RefUrl": "/notes/2658581 "}, {"RefNumber": "2655819", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: IE Standards Mode: Dropdown box not working in variable dialog", "RefUrl": "/notes/2655819 "}, {"RefNumber": "2644512", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Content height of WAD Single Document item is too small in IE with Standards Mode", "RefUrl": "/notes/2644512 "}, {"RefNumber": "2641239", "RefComponent": "BW-BEX-ET-WJR-EP", "RefTitle": "Patch level 0 check in the SAP NetWeaver BI Diagnostics & Support Desk Tool", "RefUrl": "/notes/2641239 "}, {"RefNumber": "2633900", "RefComponent": "BW-BEX-ET-WJR-DIA-EX", "RefTitle": "Exception Dialog error for level characteristic restriction", "RefUrl": "/notes/2633900 "}, {"RefNumber": "2633244", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Filter scrollbar not working in bookmark using Chrome/Firefox/IE in Standards Mode", "RefUrl": "/notes/2633244 "}, {"RefNumber": "2603242", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: New comments and attribute planning not properly assigned", "RefUrl": "/notes/2603242 "}, {"RefNumber": "2598282", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Broadcasting error when clickjacking protection is enabled", "RefUrl": "/notes/2598282 "}, {"RefNumber": "2586530", "RefComponent": "BW-BEX-ET-WJR-EP", "RefTitle": "BI JAVA SPs & Patches dependencies", "RefUrl": "/notes/2586530 "}, {"RefNumber": "2568634", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Input: Tab key not working under Chrome and FireFox", "RefUrl": "/notes/2568634 "}, {"RefNumber": "2563591", "RefComponent": "BI-RA-BICS", "RefTitle": "BICS: Performance optimization of large results set using comments/attribute planning", "RefUrl": "/notes/2563591 "}, {"RefNumber": "2549714", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "BEx Web 7.3+: Scrolling while Drag and Drop is not working", "RefUrl": "/notes/2549714 "}, {"RefNumber": "2549276", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: Height/width of Single Document Item in IE Standards Mode", "RefUrl": "/notes/2549276 "}, {"RefNumber": "2548084", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "JPEG Image optimization for PDF export using analysis  modification modules", "RefUrl": "/notes/2548084 "}, {"RefNumber": "2547821", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: Serbo-Croatian language support", "RefUrl": "/notes/2547821 "}, {"RefNumber": "2540969", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BICS: Cumulative key figures are not applied in case of universal display hierarchy", "RefUrl": "/notes/2540969 "}, {"RefNumber": "2527776", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Attribute table cell in characteristic properties dialog grows infinitely", "RefUrl": "/notes/2527776 "}, {"RefNumber": "2527524", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: IE Standards Mode: background pane elements are clickable although spinning wheel is active", "RefUrl": "/notes/2527524 "}, {"RefNumber": "2522390", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BICS: Inconsistent restore of drill operations in case of link nodes", "RefUrl": "/notes/2522390 "}, {"RefNumber": "2515982", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: BW4HANA as backend system", "RefUrl": "/notes/2515982 "}, {"RefNumber": "2513231", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Popup Window doesn't open in a Portal FPN scenario using Chrome >=58", "RefUrl": "/notes/2513231 "}, {"RefNumber": "2511841", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Input-ready cells of planning queries compressed in IE Standards Mode, Firefox and Chrome", "RefUrl": "/notes/2511841 "}, {"RefNumber": "2511811", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: Text Overlapping issue in case of  IE Standards Mode or Firefox or Chrome", "RefUrl": "/notes/2511811 "}, {"RefNumber": "2510998", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Print version System Failure: javax.ejb.EJBException", "RefUrl": "/notes/2510998 "}, {"RefNumber": "2507942", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: IE Standards Mode: KM -> New Formatted Text dialog not working", "RefUrl": "/notes/2507942 "}, {"RefNumber": "2507426", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Exception when adding key figure selection when sorted descending", "RefUrl": "/notes/2507426 "}, {"RefNumber": "2504471", "RefComponent": "BW-BEX-ET-WJR-DIA-FM", "RefTitle": "BEx Web 7.3+: IE Standards Mode/Chrome/MS Edge Chromium: \"Characteristic value xyz does not exist\" in Formula Dialog", "RefUrl": "/notes/2504471 "}, {"RefNumber": "2492949", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Scrolling: Row alignment issue", "RefUrl": "/notes/2492949 "}, {"RefNumber": "2487927", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web 7.x: Allow RRI jump to open new window if iView is embedded in a portal page", "RefUrl": "/notes/2487927 "}, {"RefNumber": "2485317", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Input: Clear and Cut buttons not working for single cell selection", "RefUrl": "/notes/2485317 "}, {"RefNumber": "2483205", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: NullReference exception in case of result set safety belt and active delta update", "RefUrl": "/notes/2483205 "}, {"RefNumber": "2478322", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Apply State command ignores RS_BEX_FPN_IVIEW_SUFFIX parameter", "RefUrl": "/notes/2478322 "}, {"RefNumber": "2465378", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web 7.x: Move structures from free to column axis when clear selection state", "RefUrl": "/notes/2465378 "}, {"RefNumber": "2463600", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: Google Chrome browser support for BEx Web Analyzer", "RefUrl": "/notes/2463600 "}, {"RefNumber": "2460187", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Error with external value help for variables", "RefUrl": "/notes/2460187 "}, {"RefNumber": "2457781", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BW portal Server overloaded exception message text is not translated to local language", "RefUrl": "/notes/2457781 "}, {"RefNumber": "2448372", "RefComponent": "BI-RA-BICS-BW", "RefTitle": "BICS: New line planning features enhancement - client", "RefUrl": "/notes/2448372 "}, {"RefNumber": "2449835", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Integer conversion of large decimal values", "RefUrl": "/notes/2449835 "}, {"RefNumber": "2442016", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BICS and BEx: Avoid inconsistent behavior of zero suppression and universal display hierarchy (client BICS)", "RefUrl": "/notes/2442016 "}, {"RefNumber": "2441417", "RefComponent": "BI-RA-BICS-BW", "RefTitle": "BICS: Zero without Currency/Unit missing percent sign", "RefUrl": "/notes/2441417 "}, {"RefNumber": "2439347", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "BEx Web 7.x: Adding invalid F4 entries not possible with enter key", "RefUrl": "/notes/2439347 "}, {"RefNumber": "2438913", "RefComponent": "BI-RA-BICS", "RefTitle": "BICS: Planning scenario may fail with unsupported operation error", "RefUrl": "/notes/2438913 "}, {"RefNumber": "2437016", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Exception thrown during export to PDF using ADS", "RefUrl": "/notes/2437016 "}, {"RefNumber": "2436794", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Searching in value help throws error if too many values are processed", "RefUrl": "/notes/2436794 "}, {"RefNumber": "2425318", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web 7.x: Adapt hostname of generated links to window location (bookmarks)", "RefUrl": "/notes/2425318 "}, {"RefNumber": "2425103", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Input: Unable to enter value after closing variable screen", "RefUrl": "/notes/2425103 "}, {"RefNumber": "2421352", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Input: Unable to enter space when using dynamic input", "RefUrl": "/notes/2421352 "}, {"RefNumber": "2418358", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BW Report Filter not working on member with Apostrophe in name", "RefUrl": "/notes/2418358 "}, {"RefNumber": "2405012", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web 7.3+: Disable Popup blocker message", "RefUrl": "/notes/2405012 "}, {"RefNumber": "2404184", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Query displays key instead of empty text for members", "RefUrl": "/notes/2404184 "}, {"RefNumber": "2402126", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web 7.3x: Preventing JS error 'Access is denied\" after opening modal dialogs in remote content scenarios (e.g. Remote AI or FPN)", "RefUrl": "/notes/2402126 "}, {"RefNumber": "2399356", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web 7.3x: No modal dialogs in remote content scenarios (e.g. Remote AI or FPN)", "RefUrl": "/notes/2399356 "}, {"RefNumber": "2398204", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "BICS: Document selection relies on query view selection instead of cell restriction", "RefUrl": "/notes/2398204 "}, {"RefNumber": "2394712", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Unparseable Date Error when Export to Excel in Design Studio", "RefUrl": "/notes/2394712 "}, {"RefNumber": "2394420", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Delta update and refresh not correct for result set with empty axis", "RefUrl": "/notes/2394420 "}, {"RefNumber": "2389403", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BEx Web 7.3+: New data refresh functionality (Java)", "RefUrl": "/notes/2389403 "}, {"RefNumber": "2387657", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Exception raised when sorting members of structure", "RefUrl": "/notes/2387657 "}, {"RefNumber": "2387246", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: MOD Input: flickering when selecting a cell range in IE Standards Mode", "RefUrl": "/notes/2387246 "}, {"RefNumber": "2387216", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: Firefox scrolling error: Value \"NaN\" of parameter \"INDEX\" could not be converted into type int (integer)", "RefUrl": "/notes/2387216 "}, {"RefNumber": "2386892", "RefComponent": "BW-BEX-ET-WJR-DIA-FM", "RefTitle": "BEx Web 7.3+: Formula dialog is missing operators in IE Standards mode", "RefUrl": "/notes/2386892 "}, {"RefNumber": "2386479", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Exceptions not working for very low threshold values", "RefUrl": "/notes/2386479 "}, {"RefNumber": "2386081", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Save or refresh of a planning query may fail", "RefUrl": "/notes/2386081 "}, {"RefNumber": "2383685", "RefComponent": "BI-RA-BICS-BW", "RefTitle": "BICS: Drill operations may fail with enabled RFC bundling", "RefUrl": "/notes/2383685 "}, {"RefNumber": "2382668", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: Context Menu not working when safety belt is active incase of IE Standard Mode, Chrome or Firefox", "RefUrl": "/notes/2382668 "}, {"RefNumber": "2381043", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Default encoding of CSV is UTF16", "RefUrl": "/notes/2381043 "}, {"RefNumber": "2380078", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Dependent node variables not reflected in value help", "RefUrl": "/notes/2380078 "}, {"RefNumber": "2380081", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Truncated text on PDF export for report item", "RefUrl": "/notes/2380081 "}, {"RefNumber": "2348259", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "WAD Command SET_DATA_CELL_PROPERITES not applying correct Scaling Factor and Decimal Places", "RefUrl": "/notes/2348259 "}, {"RefNumber": "2373073", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: IE Standards mode large table issue during navigation", "RefUrl": "/notes/2373073 "}, {"RefNumber": "2371972", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "BICS InA Wrong member instance due to duplicate cache entry", "RefUrl": "/notes/2371972 "}, {"RefNumber": "2330469", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Incorrect \"Last Data Update\" time stamp is displayed on BI reports", "RefUrl": "/notes/2330469 "}, {"RefNumber": "2340621", "RefComponent": "BW-BEX-ET-WJR-DIA-VA", "RefTitle": "Variable name in the variable screen", "RefUrl": "/notes/2340621 "}, {"RefNumber": "2368477", "RefComponent": "BW-BEX-ET-WJR-DIA-FM", "RefTitle": "Program termination when validating unsupported boolean operators in formula expression", "RefUrl": "/notes/2368477 "}, {"RefNumber": "2342811", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Document icon disappears after variable screen submission or application rollback", "RefUrl": "/notes/2342811 "}, {"RefNumber": "2349419", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "BI Export Services : Enhanced Logs and traces", "RefUrl": "/notes/2349419 "}, {"RefNumber": "2349185", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+:  Text overflow or cell height issue when Modification module ColumnWidth is active in case of IE Standards mode, Firefox or Chrome", "RefUrl": "/notes/2349185 "}, {"RefNumber": "2349141", "RefComponent": "BW-BEX-ET-WJR-EP", "RefTitle": "Enhanced Tracing for SDT", "RefUrl": "/notes/2349141 "}, {"RefNumber": "2262211", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Creation of comments on key figures throws not supported exception", "RefUrl": "/notes/2262211 "}, {"RefNumber": "2349034", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: MOD Input: Standard Mode compatibility fixes", "RefUrl": "/notes/2349034 "}, {"RefNumber": "2348794", "RefComponent": "BW-BEX-ET-WJR-DIA", "RefTitle": "Scroll bars appear when opening dialog", "RefUrl": "/notes/2348794 "}, {"RefNumber": "2347334", "RefComponent": "BW-BEX-OT-WSP", "RefTitle": "BEx Content: Updated content of BW Workspaces", "RefUrl": "/notes/2347334 "}, {"RefNumber": "2347265", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: Debug@Customer: Portal runtime error when trying to connect to customer or internal backend system", "RefUrl": "/notes/2347265 "}, {"RefNumber": "2275696", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BICS : create dynamic selection", "RefUrl": "/notes/2275696 "}, {"RefNumber": "2343205", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Data missing in PDF when export to PDF with poster layout", "RefUrl": "/notes/2343205 "}, {"RefNumber": "2341347", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Formula value with mixed currencies/units not displaying as asterisk", "RefUrl": "/notes/2341347 "}, {"RefNumber": "2341352", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: Info Field Item rendering in IE Standards Mode", "RefUrl": "/notes/2341352 "}, {"RefNumber": "2341221", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: Text highlighting not working in IE Standards Mode, Firefox and Chrome", "RefUrl": "/notes/2341221 "}, {"RefNumber": "2340964", "RefComponent": "BI-RA-BICS", "RefTitle": "BICS Input String parsing fails for escaped space characters", "RefUrl": "/notes/2340964 "}, {"RefNumber": "2312580", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS : Exception in CL_RSBOLAP_SO_STRUCTURE while executing bookmarks", "RefUrl": "/notes/2312580 "}, {"RefNumber": "2240667", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Changing a value in an input enabled characteristic as a key figure is set to old value after a data refresh.", "RefUrl": "/notes/2240667 "}, {"RefNumber": "2339397", "RefComponent": "BI-RA-BICS", "RefTitle": "Separate variant and variant catalog to enable reuse", "RefUrl": "/notes/2339397 "}, {"RefNumber": "2339100", "RefComponent": "BI-RA-BICS-BW", "RefTitle": "BICS: Grouping of Members (Java)", "RefUrl": "/notes/2339100 "}, {"RefNumber": "2338383", "RefComponent": "BI-RA-BICS", "RefTitle": "Check child tuple size when drilling to correct node drill state", "RefUrl": "/notes/2338383 "}, {"RefNumber": "2269359", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Sorting of free characteristics in navigation pane does not consider double space character", "RefUrl": "/notes/2269359 "}, {"RefNumber": "2334212", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Statistic Update to non-master BW system in NW Visual Composer application", "RefUrl": "/notes/2334212 "}, {"RefNumber": "2267357", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BI Java Accessibility: In selector dialog, the purpose of the buttons as tooltip is missing", "RefUrl": "/notes/2267357 "}, {"RefNumber": "2329548", "RefComponent": "BI-RA-BICS", "RefTitle": "Renaming of hierarchical structure nodes not reflected in the result set", "RefUrl": "/notes/2329548 "}, {"RefNumber": "2326538", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: MOD Input: Standard Mode compatibility fixes", "RefUrl": "/notes/2326538 "}, {"RefNumber": "2326132", "RefComponent": "BI-RA-BICS-BW", "RefTitle": "No value help for text key figures after note 2304132", "RefUrl": "/notes/2326132 "}, {"RefNumber": "2324097", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Java BICS: Result rows are not visible", "RefUrl": "/notes/2324097 "}, {"RefNumber": "2323178", "RefComponent": "BI-RA-BICS", "RefTitle": "BW Datasources accept unsupported formula operators", "RefUrl": "/notes/2323178 "}, {"RefNumber": "2320317", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Exporting a document uses Arial Unicode J font instead of Arial.", "RefUrl": "/notes/2320317 "}, {"RefNumber": "2320262", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Input: Tabbing issue between fixed and scroll pane", "RefUrl": "/notes/2320262 "}, {"RefNumber": "2320142", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: IE Standards Mode: distance between cursor and tooltip using drag&drop", "RefUrl": "/notes/2320142 "}, {"RefNumber": "2319606", "RefComponent": "BI-RA-BICS", "RefTitle": "Optional ignore flag to support retrieving master system settings for non master systems", "RefUrl": "/notes/2319606 "}, {"RefNumber": "2317974", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Scrolling: COLUMN and ROW parameters not working", "RefUrl": "/notes/2317974 "}, {"RefNumber": "2292243", "RefComponent": "BW-BEX-ET-WJR-DIA-VA", "RefTitle": "Hierarchy variable value not re-initialized when cleared from variable screen", "RefUrl": "/notes/2292243 "}, {"RefNumber": "2315236", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "BEx Web 7.3+: IE Standards Mode/Firefox: cursor does not change when moving mouse over characteristics in navigation pane", "RefUrl": "/notes/2315236 "}, {"RefNumber": "2264098", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Bad performance issue after Variable 'Cancel'", "RefUrl": "/notes/2264098 "}, {"RefNumber": "2311639", "RefComponent": "BI-RA-BICS", "RefTitle": "Version Check in BICS State XML", "RefUrl": "/notes/2311639 "}, {"RefNumber": "2309750", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Java BICS: Drill level is lost on structure after any navigation", "RefUrl": "/notes/2309750 "}, {"RefNumber": "2310623", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Analysis of HANA view with SQL column type BIGINT", "RefUrl": "/notes/2310623 "}, {"RefNumber": "2308091", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.30+ - Accessibility: Labels of the Bookmark save dialog are not associated with the input fields and could not read", "RefUrl": "/notes/2308091 "}, {"RefNumber": "2306859", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.30+ - Accessibility: Cascaded radio buttons / check-boxes could not be reached", "RefUrl": "/notes/2306859 "}, {"RefNumber": "2304451", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "NullPointerException in Result Set after formula deletion", "RefUrl": "/notes/2304451 "}, {"RefNumber": "2304163", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Error when submitting variables after a local calculation was created", "RefUrl": "/notes/2304163 "}, {"RefNumber": "2304132", "RefComponent": "BI-RA-BICS-BW", "RefTitle": "BICS 7.30+ : Unwanted refresh after using F4-Help in case of attribute planning", "RefUrl": "/notes/2304132 "}, {"RefNumber": "2297455", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Input enabled date or time based data cells cause null pointer exception", "RefUrl": "/notes/2297455 "}, {"RefNumber": "2296360", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: MOD Scrolling/Input: Standard Mode compatibility fixes", "RefUrl": "/notes/2296360 "}, {"RefNumber": "2296377", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: Analysis Item rendering with planning queries in IE Standards Mode, Firefox and Chrome", "RefUrl": "/notes/2296377 "}, {"RefNumber": "2292803", "RefComponent": "BW-BEX-ET-WJR-ITM-FL", "RefTitle": "Filter Items generate very long entry", "RefUrl": "/notes/2292803 "}, {"RefNumber": "2288934", "RefComponent": "BW-BEX-ET-WJR-DIA", "RefTitle": "Not all content is displayed when using OPEN_TEMPLATE_DIALOG", "RefUrl": "/notes/2288934 "}, {"RefNumber": "2287474", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.30+ - Accessibility: Usage of misleading UI properties parts", "RefUrl": "/notes/2287474 "}, {"RefNumber": "2284698", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "BEx Web 7.3+: MOD Scrolling: synchronization of row heights not working", "RefUrl": "/notes/2284698 "}, {"RefNumber": "2283483", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+ - Changed standard theme and theme determination in Broadcaster", "RefUrl": "/notes/2283483 "}, {"RefNumber": "2281346", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: IE Standards Mode: distance between cursor and tooltip using drag&drop", "RefUrl": "/notes/2281346 "}, {"RefNumber": "2279153", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: MOD Comment: no comments displayed after Note 1909645", "RefUrl": "/notes/2279153 "}, {"RefNumber": "2279196", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "BEx Web 730+ : Incorrect Table display format while using RIG  Fixed Column Width - Incase of Chrome and Firefox", "RefUrl": "/notes/2279196 "}, {"RefNumber": "2278368", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Lifecycle dates of HANA views not in local timezone", "RefUrl": "/notes/2278368 "}, {"RefNumber": "2276402", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS / BEx Web 7.3+: Characteristic attribute selection does not affect the display of the hierarchy nodes attributes in F4", "RefUrl": "/notes/2276402 "}, {"RefNumber": "2276618", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Sorting behaves differently when condition is activated from context menu", "RefUrl": "/notes/2276618 "}, {"RefNumber": "2272906", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Scrolling: Rendering and script issues after note 2151036", "RefUrl": "/notes/2272906 "}, {"RefNumber": "2272828", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web 7.x: Bookmark loosing UDH DrillLevel", "RefUrl": "/notes/2272828 "}, {"RefNumber": "2272779", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "BICS InA: Allow cancelling of HTTP requests", "RefUrl": "/notes/2272779 "}, {"RefNumber": "2269795", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "BICS InA: Prefetch Meta Data for multiple Data Sources", "RefUrl": "/notes/2269795 "}, {"RefNumber": "2268338", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "BICS InA: Remove variable submit request", "RefUrl": "/notes/2268338 "}, {"RefNumber": "2265041", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "BICS InA Support for hierarchy node variable default values", "RefUrl": "/notes/2265041 "}, {"RefNumber": "2264336", "RefComponent": "BI-RA-BICS", "RefTitle": "Maximum number of characteristics on axis", "RefUrl": "/notes/2264336 "}, {"RefNumber": "2231088", "RefComponent": "BW-BEX-ET-WJR-ITM-FL", "RefTitle": "ListBox Selects All values during Hierarchy activation", "RefUrl": "/notes/2231088 "}, {"RefNumber": "2260131", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "\"UNKNOWN ERROR\" when a template is exported to excel", "RefUrl": "/notes/2260131 "}, {"RefNumber": "2257299", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web 7.x: Planning selection binding performance improvement", "RefUrl": "/notes/2257299 "}, {"RefNumber": "2252904", "RefComponent": "BI-RA-BICS-BW", "RefTitle": "BICS : Exception hierarchy restrictions are not considered correctly when a fixed hierarchy node filter variable is defined", "RefUrl": "/notes/2252904 "}, {"RefNumber": "2252577", "RefComponent": "BI-RA-BICS-BW", "RefTitle": "Reset Work Status Selector API", "RefUrl": "/notes/2252577 "}, {"RefNumber": "2251371", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Restriction not being removed after undo redo", "RefUrl": "/notes/2251371 "}, {"RefNumber": "2251224", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "MOD Input/Scrolling: Fixes for several issues", "RefUrl": "/notes/2251224 "}, {"RefNumber": "2247967", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "BEx Web 7.x: Enable client select of modification modules", "RefUrl": "/notes/2247967 "}, {"RefNumber": "2248069", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BICS 7.3x: Bad performance and errors when using large input strings", "RefUrl": "/notes/2248069 "}, {"RefNumber": "2246318", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "BEx Web 7.x: increase limits for selection upload from file or clipboard", "RefUrl": "/notes/2246318 "}, {"RefNumber": "2245499", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Displaying 0  instead of comment when using RIG DocumentContent", "RefUrl": "/notes/2245499 "}, {"RefNumber": "2243729", "RefComponent": "BI-RA-BICS", "RefTitle": "BICS CSV: Dimensions without texts shown as empty strings", "RefUrl": "/notes/2243729 "}, {"RefNumber": "2243184", "RefComponent": "BI-RA-BICS", "RefTitle": "BICS CSV Exporter: Incorrect File Encoding", "RefUrl": "/notes/2243184 "}, {"RefNumber": "2242756", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.x: Support for external bookmarks and Fiori Desktop (Tiles)", "RefUrl": "/notes/2242756 "}, {"RefNumber": "2239403", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Selections containing \"unassigned\" (#) members are rejected as invalid", "RefUrl": "/notes/2239403 "}, {"RefNumber": "2238392", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Chinese characters are replaced by '#' in the PDF generated by exporting the report designed in report designer", "RefUrl": "/notes/2238392 "}, {"RefNumber": "2238253", "RefComponent": "BI-RA-BICS", "RefTitle": "BICS CSV Exporter: Line Breaks in Texts cause´Exception", "RefUrl": "/notes/2238253 "}, {"RefNumber": "2236905", "RefComponent": "BI-RA-BICS", "RefTitle": "BICS CSV Exporter: Hierarchies with node alignment can not be opened", "RefUrl": "/notes/2236905 "}, {"RefNumber": "2236406", "RefComponent": "BI-RA-BICS", "RefTitle": "Wrong date values in CSV files", "RefUrl": "/notes/2236406 "}, {"RefNumber": "2235327", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BICS 7.3+: Empty result set dumps", "RefUrl": "/notes/2235327 "}, {"RefNumber": "2228348", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "BICS HANA Cancel without submit causes exception \"invalid variable values\"", "RefUrl": "/notes/2228348 "}, {"RefNumber": "2226720", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: Context menu in Firefox is displayed without CSS styles for the first time", "RefUrl": "/notes/2226720 "}, {"RefNumber": "2170789", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BICS: Duplicate sorting types (JUnit fix)", "RefUrl": "/notes/2170789 "}, {"RefNumber": "2221857", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Fortify Scans SPOT Check issue : <PERSON><PERSON>ere<PERSON> in ALVExportParser.java", "RefUrl": "/notes/2221857 "}, {"RefNumber": "2219459", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BEx Web: Depending formulas on different structures don't update correctly", "RefUrl": "/notes/2219459 "}, {"RefNumber": "2214620", "RefComponent": "BW-BEX-ET", "RefTitle": "Collective Note: SAP NetWeaver 7.5 SP00 - BI JAVA", "RefUrl": "/notes/2214620 "}, {"RefNumber": "2214270", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BEx Web: Selection of node failing for hierarchy variable in case of temporal hierarchy join", "RefUrl": "/notes/2214270 "}, {"RefNumber": "2212386", "RefComponent": "BI-RA-BICS", "RefTitle": "<PERSON><PERSON> sign in member name / key interpreted as interval selection", "RefUrl": "/notes/2212386 "}, {"RefNumber": "2183839", "RefComponent": "BI-RA-BICS", "RefTitle": "BICS: Result Set delta update enhancement (BW backend server)", "RefUrl": "/notes/2183839 "}, {"RefNumber": "2204040", "RefComponent": "BI-RA-BICS", "RefTitle": "Exception when sorting key figure with hidden totals", "RefUrl": "/notes/2204040 "}, {"RefNumber": "2199257", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Hierarchy node filter on HANA view is not correctly applied", "RefUrl": "/notes/2199257 "}, {"RefNumber": "2197997", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "BEx Web - Export 7.3+: Mssing result rows in PDF export when using as scaling factor poster", "RefUrl": "/notes/2197997 "}, {"RefNumber": "2195432", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "BICS InA Support for HANA hierarchy node variables", "RefUrl": "/notes/2195432 "}, {"RefNumber": "2195363", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "<PERSON><PERSON><PERSON> Font not set while exporting the document via BEx Broadcaster", "RefUrl": "/notes/2195363 "}, {"RefNumber": "2192959", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "HANA Variable Operator Is Null and Is Not Null not working with BICS", "RefUrl": "/notes/2192959 "}, {"RefNumber": "2191882", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Reduce HTTP Requests when dealing with multiple default values in HANA Views", "RefUrl": "/notes/2191882 "}, {"RefNumber": "2190709", "RefComponent": "BI-RA-BICS-BW", "RefTitle": "BPC Work Status Support in BICS", "RefUrl": "/notes/2190709 "}, {"RefNumber": "2190351", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Multi value string input parameter in Analysis", "RefUrl": "/notes/2190351 "}, {"RefNumber": "2189156", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "TEMPLATE ENGLISH: BW Java / BOE 4.0 / BICS", "RefUrl": "/notes/2189156 "}, {"RefNumber": "2189153", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "HANA workbook migration fails with renamed input parameters / variables", "RefUrl": "/notes/2189153 "}, {"RefNumber": "2178138", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.x: BI/BW Java network requirements, recommendations and limitations (WAN)", "RefUrl": "/notes/2178138 "}, {"RefNumber": "2175893", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Error \"not a GROUP BY expression\" when getting HANA result set", "RefUrl": "/notes/2175893 "}, {"RefNumber": "2170782", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "BEx Web 7.0x: NullPointerException when exporting no data resultset", "RefUrl": "/notes/2170782 "}, {"RefNumber": "2159842", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Variables are combined with OR instead of AND", "RefUrl": "/notes/2159842 "}, {"RefNumber": "2161435", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Scrolling: Issue with row alignment", "RefUrl": "/notes/2161435 "}, {"RefNumber": "2157758", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Wrong node order when expanding parent child hierarchies / Exception \"Invalid new element\"", "RefUrl": "/notes/2157758 "}, {"RefNumber": "2157021", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BEx Web 7.3x+: Mixed currency not detected when axis tuple contains zero values", "RefUrl": "/notes/2157021 "}, {"RefNumber": "2151601", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Dropdown box items not rendered properly when using item snippets functionality", "RefUrl": "/notes/2151601 "}, {"RefNumber": "2151036", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Scrolling: Font changes when using customized theme", "RefUrl": "/notes/2151036 "}, {"RefNumber": "2147772", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.x: Selection of planning function has invalid selections", "RefUrl": "/notes/2147772 "}, {"RefNumber": "2129830", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BICS: Enhanced title state for universal display hierarchy", "RefUrl": "/notes/2129830 "}, {"RefNumber": "2145152", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Parallel Execution with BICS InA Connection", "RefUrl": "/notes/2145152 "}, {"RefNumber": "2144849", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Multiple similar SQL statements during validation of DATE variable against HANA", "RefUrl": "/notes/2144849 "}, {"RefNumber": "2144411", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "No applicable data found for HANA views with date measures and null values", "RefUrl": "/notes/2144411 "}, {"RefNumber": "2139387", "RefComponent": "BI-RA-BICS", "RefTitle": "Allow to ignore thread checks in statistic manager", "RefUrl": "/notes/2139387 "}, {"RefNumber": "2137183", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Scrolling: Row selection does not work in Firefox", "RefUrl": "/notes/2137183 "}, {"RefNumber": "2096409", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Java BICS: Dynamic break back", "RefUrl": "/notes/2096409 "}, {"RefNumber": "2129786", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Wrong aggregation in cumulative key figure", "RefUrl": "/notes/2129786 "}, {"RefNumber": "2128654", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Active tab content of a tab strip item is exported twice in broadcast report", "RefUrl": "/notes/2128654 "}, {"RefNumber": "2128319", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD ColumnWidth: Width is applied to wrong column", "RefUrl": "/notes/2128319 "}, {"RefNumber": "2128014", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS / BEx Web 7.3+: IndexOutOfBoundsException in RsAxisTupleElement when removing result row with activated zero suppression", "RefUrl": "/notes/2128014 "}, {"RefNumber": "2124913", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Format: Hide sorting icons", "RefUrl": "/notes/2124913 "}, {"RefNumber": "2124790", "RefComponent": "BI-RA-BICS", "RefTitle": "Object Services: Enhancement for BPC Environments", "RefUrl": "/notes/2124790 "}, {"RefNumber": "2124648", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Reuse DocumentBuilder instead of creating new ones", "RefUrl": "/notes/2124648 "}, {"RefNumber": "2121454", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Scrolling: Query hangs during page navigation", "RefUrl": "/notes/2121454 "}, {"RefNumber": "2120952", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Characteristic member document cache issue", "RefUrl": "/notes/2120952 "}, {"RefNumber": "2119412", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Cache TransformerFactory", "RefUrl": "/notes/2119412 "}, {"RefNumber": "2117583", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Sort by selection incorrect in case of a hierarchy drill operation", "RefUrl": "/notes/2117583 "}, {"RefNumber": "2117546", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Hierarchy drill state lost after refresh", "RefUrl": "/notes/2117546 "}, {"RefNumber": "2115346", "RefComponent": "BI-RA-BICS", "RefTitle": "Support for local restrictions", "RefUrl": "/notes/2115346 "}, {"RefNumber": "2113621", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Format: Vertical alignment is not applied to header cells", "RefUrl": "/notes/2113621 "}, {"RefNumber": "2113076", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Exporting images with PNG format", "RefUrl": "/notes/2113076 "}, {"RefNumber": "2111815", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Scrolling: 'cells' is null exception", "RefUrl": "/notes/2111815 "}, {"RefNumber": "2077998", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Scaling factor property is not displayed correctly", "RefUrl": "/notes/2077998 "}, {"RefNumber": "2110817", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+:  Attribute Table (Scrollbar) on Attribute Tab (Characteristic Properties Dialog) is disabled", "RefUrl": "/notes/2110817 "}, {"RefNumber": "2109524", "RefComponent": "BI-RA-BICS", "RefTitle": "Switch statistic event time measurement to nano time for more detailed measurements", "RefUrl": "/notes/2109524 "}, {"RefNumber": "2108937", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Exception when using shared or merged variable container with HANA connections", "RefUrl": "/notes/2108937 "}, {"RefNumber": "2108804", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.x: Drag and drop enhancement fix", "RefUrl": "/notes/2108804 "}, {"RefNumber": "2106256", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "BEx Web 7.x: ODOC locks remain and incorrect document view state", "RefUrl": "/notes/2106256 "}, {"RefNumber": "2105574", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: Context Menu (fourth level) does not work properly in Firefox and Chrome", "RefUrl": "/notes/2105574 "}, {"RefNumber": "2103149", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Error in value help for variable", "RefUrl": "/notes/2103149 "}, {"RefNumber": "2001334", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "not normalized result set inactivates condition states", "RefUrl": "/notes/2001334 "}, {"RefNumber": "2096136", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "HANA Limitations for BI Clients using BICS HTTP Connection", "RefUrl": "/notes/2096136 "}, {"RefNumber": "2091509", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Define input validation priorities of hierarchy element types", "RefUrl": "/notes/2091509 "}, {"RefNumber": "2092088", "RefComponent": "BI-RA-BICS", "RefTitle": "Optimizations for the relational mapper and CSV provider", "RefUrl": "/notes/2092088 "}, {"RefNumber": "2087205", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: ClassCastException in ProviderInfoObject when using Document Member Caching", "RefUrl": "/notes/2087205 "}, {"RefNumber": "2084605", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Implementation of refresh for BI (SQL) datasource / relational provider", "RefUrl": "/notes/2084605 "}, {"RefNumber": "2079393", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Unique filename for export to Excel or CSV", "RefUrl": "/notes/2079393 "}, {"RefNumber": "2078795", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Scrolling: Planning does not work with module after upgrade", "RefUrl": "/notes/2078795 "}, {"RefNumber": "2078794", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD ExceptionImage: Symbol reverts to default when document icon visible", "RefUrl": "/notes/2078794 "}, {"RefNumber": "2058746", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Using level hiding and zero suppression causes an exception", "RefUrl": "/notes/2058746 "}, {"RefNumber": "2065680", "RefComponent": "BW-BEX-ET-WJR-DIA", "RefTitle": "BEx Web 7.3x: Javascript error when calling dialogs", "RefUrl": "/notes/2065680 "}, {"RefNumber": "2064268", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Support for multiple hierarchy node selection", "RefUrl": "/notes/2064268 "}, {"RefNumber": "2076062", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BICS: J<PERSON>_FIELD_ERROR_NOT_FOUND: Field I_T_MEMBER_NAMES not a member of TABLES", "RefUrl": "/notes/2076062 "}, {"RefNumber": "2067262", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "BICS InA: Hierarchies missing when using Calculation Views", "RefUrl": "/notes/2067262 "}, {"RefNumber": "2053511", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "BEx Web 7.3x: General ODOC performance issues and property caching of KM Repository Manager", "RefUrl": "/notes/2053511 "}, {"RefNumber": "2062648", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web 7.3x: Missing includes after closing dialog", "RefUrl": "/notes/2062648 "}, {"RefNumber": "2057952", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Broadcaster return error ITEM of type could not be generated", "RefUrl": "/notes/2057952 "}, {"RefNumber": "2061802", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Performance optimization for HANA(BAE) provider - Join on NULL values", "RefUrl": "/notes/2061802 "}, {"RefNumber": "2057789", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Changes in the default exporting behaviour of images", "RefUrl": "/notes/2057789 "}, {"RefNumber": "2056576", "RefComponent": "BI-RA-BICS", "RefTitle": "BICS HANA: Date/Timezone variables and filters don't take current time zone into account", "RefUrl": "/notes/2056576 "}, {"RefNumber": "2055622", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "BEx Web 7.3x: Dataprovider item empty after roundtrip", "RefUrl": "/notes/2055622 "}, {"RefNumber": "2054624", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "RuntimeException when loading a saved BICS HANA view with nested formulas", "RefUrl": "/notes/2054624 "}, {"RefNumber": "2053210", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.01+: Input of leaf returns hierarchy node presentation", "RefUrl": "/notes/2053210 "}, {"RefNumber": "2053007", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Numerous Certificate pop ups during Excel Export", "RefUrl": "/notes/2053007 "}, {"RefNumber": "2044560", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Bookmark user language", "RefUrl": "/notes/2044560 "}, {"RefNumber": "2043718", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Filter Pane Item displays the scrollbars in IE11", "RefUrl": "/notes/2043718 "}, {"RefNumber": "2038349", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Application crash:  Executing SQL statement failed", "RefUrl": "/notes/2038349 "}, {"RefNumber": "2034898", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Update of drill level behavior of universal display hierarchy", "RefUrl": "/notes/2034898 "}, {"RefNumber": "2034062", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "SET TEMPLATE command optimization", "RefUrl": "/notes/2034062 "}, {"RefNumber": "2037933", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.01+: BicsProgrammErrorException after selecting 'Above' for hierarchy property 'Position of Child-Nodes' for activated display hierarchy", "RefUrl": "/notes/2037933 "}, {"RefNumber": "2037953", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "BEx Web 7.3x: Selected entries in the selector dialog get unselected", "RefUrl": "/notes/2037953 "}, {"RefNumber": "2036522", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web 7.x: Message list item displays obsolete messages", "RefUrl": "/notes/2036522 "}, {"RefNumber": "2033984", "RefComponent": "BI-RA-BICS", "RefTitle": "Collective Note for Table Design Development Fixes Affected archive", "RefUrl": "/notes/2033984 "}, {"RefNumber": "2033608", "RefComponent": "BI-RA-BICS-BW", "RefTitle": "Input for variables based on info objects without master data are not accepted (Java)", "RefUrl": "/notes/2033608 "}, {"RefNumber": "2031641", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Data access error during drill operation not displayed (Java)", "RefUrl": "/notes/2031641 "}, {"RefNumber": "2029725", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Exporting a template containing template include item, which is assigned a template, containing only text or an empty template throws java.lang.NullPointerException", "RefUrl": "/notes/2029725 "}, {"RefNumber": "2029293", "RefComponent": "BI-RA-BICS", "RefTitle": "RFC Bundling: Result Sets are incorrectly fetched during state syncronization", "RefUrl": "/notes/2029293 "}, {"RefNumber": "2027277", "RefComponent": "BI-RA-BICS", "RefTitle": "Optimzation for accessing already retrieved members", "RefUrl": "/notes/2027277 "}, {"RefNumber": "2027005", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Performance Optimization for structure member access", "RefUrl": "/notes/2027005 "}, {"RefNumber": "2025216", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web - New short and long message text for \"New rows or columns cannot be maintained in this query view (see long text)\"", "RefUrl": "/notes/2025216 "}, {"RefNumber": "2023061", "RefComponent": "BW-BEX-ET-WJR-EP", "RefTitle": "BEx Web 7.3x: Portal Theme ID fallback", "RefUrl": "/notes/2023061 "}, {"RefNumber": "2022953", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Container Layout Item: Exporting to PDF with Fit to Page Width option does not show multiple pages", "RefUrl": "/notes/2022953 "}, {"RefNumber": "2021326", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Comment: Text is not wrapped properly when wrapping is enabled", "RefUrl": "/notes/2021326 "}, {"RefNumber": "2020589", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD ColumnWidth: Module is not working after patch", "RefUrl": "/notes/2020589 "}, {"RefNumber": "2019725", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: No data is displayed after chaning the filter value in relation with using Document Member Caching", "RefUrl": "/notes/2019725 "}, {"RefNumber": "2018823", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "export to excel exception on exporting null image source", "RefUrl": "/notes/2018823 "}, {"RefNumber": "2014870", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.x: Images are not displayed when broadcasting in MHTML", "RefUrl": "/notes/2014870 "}, {"RefNumber": "2014839", "RefComponent": "BI-RA-BICS", "RefTitle": "'java.lang.UnsupportedOperationException: No Delta Operation Support' for Universal Hierarchy in BICS Relational Provider", "RefUrl": "/notes/2014839 "}, {"RefNumber": "2010286", "RefComponent": "BI-RA-BICS", "RefTitle": "Attribute planning in BICS (Java)", "RefUrl": "/notes/2010286 "}, {"RefNumber": "2008258", "RefComponent": "BI-RA-BICS", "RefTitle": "Too much time spend in member cache for very large selections", "RefUrl": "/notes/2008258 "}, {"RefNumber": "2006630", "RefComponent": "BI-RA-BICS", "RefTitle": "BICS DesignTime API: NPE for fixed time-dependent hierarchy", "RefUrl": "/notes/2006630 "}, {"RefNumber": "2005312", "RefComponent": "BI-RA-BICS", "RefTitle": "Merged Variable Container still unsubmitted after variable cancel", "RefUrl": "/notes/2005312 "}, {"RefNumber": "2003064", "RefComponent": "BI-RA-BICS", "RefTitle": "Exception when copying selection components with literal members", "RefUrl": "/notes/2003064 "}, {"RefNumber": "1994988", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "F4 search results for hierarchy variable are not sorted", "RefUrl": "/notes/1994988 "}, {"RefNumber": "1999562", "RefComponent": "BI-RA-BICS", "RefTitle": "Wrong data cell values when suppressing zero values on the column axis", "RefUrl": "/notes/1999562 "}, {"RefNumber": "1998326", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.x: Performance improvement message long texts", "RefUrl": "/notes/1998326 "}, {"RefNumber": "1998037", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Alignment support for Right To Left languages  in generated PDF", "RefUrl": "/notes/1998037 "}, {"RefNumber": "1987154", "RefComponent": "BI-RA-BICS", "RefTitle": "Input string gets removed in new line cell", "RefUrl": "/notes/1987154 "}, {"RefNumber": "1996028", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Scrolling: Rows are not aligned in 7.01", "RefUrl": "/notes/1996028 "}, {"RefNumber": "1986457", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Wrong time stamp in report header/footer broadcasted to mail", "RefUrl": "/notes/1986457 "}, {"RefNumber": "1986387", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Broadcasting PCL/PS file to printer fails with ADS", "RefUrl": "/notes/1986387 "}, {"RefNumber": "1983524", "RefComponent": "BI-RA-BICS", "RefTitle": "Avoiding potential NullPointerExceptions in GenericConnectionContainer", "RefUrl": "/notes/1983524 "}, {"RefNumber": "1983124", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Support for multiple BI/BW master systems (Java)", "RefUrl": "/notes/1983124 "}, {"RefNumber": "1968735", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NW7.0 SPS 30 Patch 10 note for BI Java", "RefUrl": "/notes/1968735 "}, {"RefNumber": "1981559", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Web items inside the Container Layout item are truncated in exported PDF", "RefUrl": "/notes/1981559 "}, {"RefNumber": "1979455", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "BEx Web 7.x: Hidden row or column headers may cause exception", "RefUrl": "/notes/1979455 "}, {"RefNumber": "1978738", "RefComponent": "BW-BEX-ET-ODOC", "RefTitle": "BEx Web 7.x: Document exception with invalid selections", "RefUrl": "/notes/1978738 "}, {"RefNumber": "1978679", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web 7.x: Error message shown when calling context menu option \"Cumulate\"", "RefUrl": "/notes/1978679 "}, {"RefNumber": "1978674", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.x: Selection status of radio buttons are not transferred to server", "RefUrl": "/notes/1978674 "}, {"RefNumber": "1976620", "RefComponent": "BI-RA-BICS-BW", "RefTitle": "Retrieve System Alias for ABAP Messages", "RefUrl": "/notes/1976620 "}, {"RefNumber": "1976037", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "BEx Web 7.x: Analysis item changing current page number has no effect", "RefUrl": "/notes/1976037 "}, {"RefNumber": "1973948", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Performance optimization closing F4 help dialog", "RefUrl": "/notes/1973948 "}, {"RefNumber": "1973160", "RefComponent": "BI-RA-BICS", "RefTitle": "Hierarchy Variable cannot be set to empty in case the current value is invalid", "RefUrl": "/notes/1973160 "}, {"RefNumber": "1964600", "RefComponent": "BW-BEX-ET-WJR-ITM-FL", "RefTitle": "RSADMIN parameters for filter items: FILTER_WIDTH_THRESHOLD and ENABLE_FILTER_ITEMS", "RefUrl": "/notes/1964600 "}, {"RefNumber": "1969806", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Structure elements with hidden parent may cause exception", "RefUrl": "/notes/1969806 "}, {"RefNumber": "1970308", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "BEx Web: Authorization message body text adjustment", "RefUrl": "/notes/1970308 "}, {"RefNumber": "1970226", "RefComponent": "BI-RA-BICS", "RefTitle": "NullPointerException when searching structure members with a key presentation", "RefUrl": "/notes/1970226 "}, {"RefNumber": "1970186", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "Performance issue while loading the BI dashboard", "RefUrl": "/notes/1970186 "}, {"RefNumber": "1969008", "RefComponent": "BI-RA-BICS", "RefTitle": "Wrong data area used for the selector of a planning function", "RefUrl": "/notes/1969008 "}, {"RefNumber": "1940546", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: Loading Animation is not displayed in Firefox and Safari", "RefUrl": "/notes/1940546 "}, {"RefNumber": "1961654", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Change in the format of cell in exported excel file in BEx web.", "RefUrl": "/notes/1961654 "}, {"RefNumber": "1951306", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "JAVA Documents: Performance is bad due plenty of BICS_PROV_GET_MEMBERS", "RefUrl": "/notes/1951306 "}, {"RefNumber": "1953474", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "AbortMessageRuntimeException when searching with an invalid presentation", "RefUrl": "/notes/1953474 "}, {"RefNumber": "1953007", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "BEX Web 7.x: Customer Exit item not executed after roundtrip", "RefUrl": "/notes/1953007 "}, {"RefNumber": "1950026", "RefComponent": "BI-RA-BICS", "RefTitle": "New Leaf Type AZEX for Design Studio extensions", "RefUrl": "/notes/1950026 "}, {"RefNumber": "1949941", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.x: Commands not changing state are not skipped", "RefUrl": "/notes/1949941 "}, {"RefNumber": "1947322", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "RSADMIN parameter to configure truncation limit for  Info Field item.", "RefUrl": "/notes/1947322 "}, {"RefNumber": "1943401", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Dynamic Grid in Analysis Office 2.0", "RefUrl": "/notes/1943401 "}, {"RefNumber": "1941805", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: BicsResourceBwRuntimeException when using list calculations after moving hierarchical structure to free characteristics axis", "RefUrl": "/notes/1941805 "}, {"RefNumber": "1940743", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Internal encodeBase64 in AcImage", "RefUrl": "/notes/1940743 "}, {"RefNumber": "1938345", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: A drop down menu does not close any more, after a context menu has been opened in Firefox.", "RefUrl": "/notes/1938345 "}, {"RefNumber": "1937680", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "BEx Web 7.x: select all on selector search view flushes entries", "RefUrl": "/notes/1937680 "}, {"RefNumber": "1936565", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Empty data cells are formatted as text when exported to Excel", "RefUrl": "/notes/1936565 "}, {"RefNumber": "1936171", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web 7.3x: Exception after deleting a user from UME", "RefUrl": "/notes/1936171 "}, {"RefNumber": "1932399", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Dynamic Grid", "RefUrl": "/notes/1932399 "}, {"RefNumber": "1932157", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Query Technical name is displayed in BEx Web without considering the supplied Page Title from a Text variable", "RefUrl": "/notes/1932157 "}, {"RefNumber": "1931856", "RefComponent": "BI-RA-BICS", "RefTitle": "HANA Hierarchy Features for SP7", "RefUrl": "/notes/1931856 "}, {"RefNumber": "1931709", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: empty selection causes JCo exception", "RefUrl": "/notes/1931709 "}, {"RefNumber": "1901084", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Getting Unknown Error while Broadcasting Enterprise Report", "RefUrl": "/notes/1901084 "}, {"RefNumber": "904087", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "TEMPLATE ENGLISH: BW Java / BOE 4.0 / BICS", "RefUrl": "/notes/904087 "}, {"RefNumber": "904086", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "DEUTSCH: JAVA - USE 904087 in English - save translation!!!", "RefUrl": "/notes/904086 "}, {"RefNumber": "1930661", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Java Selector: Hierarchy Search enhancement in F4 dialog", "RefUrl": "/notes/1930661 "}, {"RefNumber": "1930778", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis Modification Dynamic Grid - Comment", "RefUrl": "/notes/1930778 "}, {"RefNumber": "1930079", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BEx Web 7.3+: Hierarchy node or leaf selection is transformed into a single member selection", "RefUrl": "/notes/1930079 "}, {"RefNumber": "1920745", "RefComponent": "BI-RA-BICS", "RefTitle": "Data Area Properties for creation", "RefUrl": "/notes/1920745 "}, {"RefNumber": "1926336", "RefComponent": "BI-RA-BICS", "RefTitle": "Result set state DATA_ACCESS_PROBLEMS in case of exceptions", "RefUrl": "/notes/1926336 "}, {"RefNumber": "1922735", "RefComponent": "BI-RA-BICS", "RefTitle": "Result Set Change Event when changing attributes", "RefUrl": "/notes/1922735 "}, {"RefNumber": "1924914", "RefComponent": "BI-RA-BICS", "RefTitle": "Object Services advanced search support", "RefUrl": "/notes/1924914 "}, {"RefNumber": "1918069", "RefComponent": "BI-RA-BICS", "RefTitle": "Object Services for BPC Model and Environment", "RefUrl": "/notes/1918069 "}, {"RefNumber": "1927396", "RefComponent": "BI-RA-BICS", "RefTitle": "Date base measures", "RefUrl": "/notes/1927396 "}, {"RefNumber": "1651050", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Internal: Infrastructure changes in RIC and NW 730", "RefUrl": "/notes/1651050 "}, {"RefNumber": "1923702", "RefComponent": "BI-RA-BICS", "RefTitle": "Result Set optimizations", "RefUrl": "/notes/1923702 "}, {"RefNumber": "1724953", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Report Item Export with ADS PDF - Blank Pages", "RefUrl": "/notes/1724953 "}, {"RefNumber": "1921474", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Error in Exporting ContainerLayoutItem with Columnspan > 1", "RefUrl": "/notes/1921474 "}, {"RefNumber": "1919536", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Variable screen is not shown when loading Portal Favorites", "RefUrl": "/notes/1919536 "}, {"RefNumber": "1920214", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Name of characteristic is not shown in Selector (F4 Help)", "RefUrl": "/notes/1920214 "}, {"RefNumber": "1921167", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD ColumnWidth: Inconsistent due to same index/key values", "RefUrl": "/notes/1921167 "}, {"RefNumber": "1723057", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Hidden info objects are visible in 7.30", "RefUrl": "/notes/1723057 "}, {"RefNumber": "1920792", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web 7.3x: Session timeout safety belt", "RefUrl": "/notes/1920792 "}, {"RefNumber": "1913808", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Valuehelp is not available for RIG module in WAD design time", "RefUrl": "/notes/1913808 "}, {"RefNumber": "1913659", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: Context Menu does not open in Safari", "RefUrl": "/notes/1913659 "}, {"RefNumber": "1915984", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web 7.3x: Support for multiple BI/BW master systems", "RefUrl": "/notes/1915984 "}, {"RefNumber": "1895383", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "BEx Web 7.x: Exception symbols are not exported to Excel", "RefUrl": "/notes/1895383 "}, {"RefNumber": "1821722", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "All tabs are not exported to PDF while broadcasting in 7.3X", "RefUrl": "/notes/1821722 "}, {"RefNumber": "1775737", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification comments - compounding, new lines, ..", "RefUrl": "/notes/1775737 "}, {"RefNumber": "1907603", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Comments of leaf nodes not displaying on hirearchy node.", "RefUrl": "/notes/1907603 "}, {"RefNumber": "1644770", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Dynamic Input fields for input ready data cells", "RefUrl": "/notes/1644770 "}, {"RefNumber": "1884399", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "Percentage values are not in the range 0 to 1", "RefUrl": "/notes/1884399 "}, {"RefNumber": "1645590", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Java/Server SPs dependencies (and SupportDeskTool)", "RefUrl": "/notes/1645590 "}, {"RefNumber": "1908276", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Backend messages are shown multiple times on frontends (JAVA)", "RefUrl": "/notes/1908276 "}, {"RefNumber": "1907637", "RefComponent": "BI-RA-BICS", "RefTitle": "Issues in Levelvisibility and Drilloperations", "RefUrl": "/notes/1907637 "}, {"RefNumber": "1789842", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Patch Level 0 for BI Java Installation", "RefUrl": "/notes/1789842 "}, {"RefNumber": "1802366", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Documents Invisible -Static Filter Hier. Nodes", "RefUrl": "/notes/1802366 "}, {"RefNumber": "1808950", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x:Document Items No Actualization after Selection", "RefUrl": "/notes/1808950 "}, {"RefNumber": "1898659", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD NonInputableEmptyCells: Does not work with Input module", "RefUrl": "/notes/1898659 "}, {"RefNumber": "1895898", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Input: Negative numbers displayed on multiple lines", "RefUrl": "/notes/1895898 "}, {"RefNumber": "1893527", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD RemoveUnit: Unit is still visible when exported", "RefUrl": "/notes/1893527 "}, {"RefNumber": "1889734", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Format: Custom formatting is lost after tab navigations", "RefUrl": "/notes/1889734 "}, {"RefNumber": "1882750", "RefComponent": "BI-RA-BICS", "RefTitle": "Application crash with key figures of type date", "RefUrl": "/notes/1882750 "}, {"RefNumber": "1830200", "RefComponent": "BI-RA-BICS", "RefTitle": "Not existing operands in formulas", "RefUrl": "/notes/1830200 "}, {"RefNumber": "1873448", "RefComponent": "BI-RA-BICS", "RefTitle": "Level visibility on both axes returns wrong values", "RefUrl": "/notes/1873448 "}, {"RefNumber": "1875755", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "[intern!] BI Web App in BI Platform - Package Size Reduction", "RefUrl": "/notes/1875755 "}, {"RefNumber": "1656845", "RefComponent": "BI-RA-BICS", "RefTitle": "Internal: Sammelhinweis für Refactoring", "RefUrl": "/notes/1656845 "}, {"RefNumber": "1652425", "RefComponent": "BI-RA-BICS", "RefTitle": "Sammelhinweis für BICS Tests", "RefUrl": "/notes/1652425 "}, {"RefNumber": "1703363", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Sammelhinweis für HANA Provider", "RefUrl": "/notes/1703363 "}, {"RefNumber": "1652480", "RefComponent": "BI-RA-BICS", "RefTitle": "Sammelhinweis für Hierarchies in BICS HANA Provider", "RefUrl": "/notes/1652480 "}, {"RefNumber": "1721864", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.x: KM Documents Integration: Performance Issues", "RefUrl": "/notes/1721864 "}, {"RefNumber": "1797738", "RefComponent": "BI-RA-BICS", "RefTitle": "Performance: Improvement in adding new entry enable rows", "RefUrl": "/notes/1797738 "}, {"RefNumber": "1891210", "RefComponent": "BI-RA-BICS", "RefTitle": "ClassCastException in ViewPersistenceService", "RefUrl": "/notes/1891210 "}, {"RefNumber": "1829407", "RefComponent": "BI-RA-BICS", "RefTitle": "<PERSON> von Variablen Cancel in BICS", "RefUrl": "/notes/1829407 "}, {"RefNumber": "1887126", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "mdx select on BIMC_LEVELS has no restriction for cube", "RefUrl": "/notes/1887126 "}, {"RefNumber": "1884148", "RefComponent": "BI-RA-BICS", "RefTitle": "Key returned instead of text for variable member", "RefUrl": "/notes/1884148 "}, {"RefNumber": "1873102", "RefComponent": "BI-RA-BICS", "RefTitle": "Wrong display of \"Valid To\" date of hierarchies", "RefUrl": "/notes/1873102 "}, {"RefNumber": "1779081", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Feature: Currency Support for HANA", "RefUrl": "/notes/1779081 "}, {"RefNumber": "1840832", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "BI Document is not working with compound characteristics", "RefUrl": "/notes/1840832 "}, {"RefNumber": "1853643", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Unable to retrieve presentation of hierarchy node variable", "RefUrl": "/notes/1853643 "}, {"RefNumber": "1879575", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Width of the TabItem is not set properly", "RefUrl": "/notes/1879575 "}, {"RefNumber": "1638319", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Web Session Timeout not Working", "RefUrl": "/notes/1638319 "}, {"RefNumber": "1069253", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.x: Monitoring server node memory use in OLAP stats", "RefUrl": "/notes/1069253 "}, {"RefNumber": "1869034", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Varible screen processing in multiple runtime contexts", "RefUrl": "/notes/1869034 "}, {"RefNumber": "1857184", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BICS: No wild card search in Variable Screen", "RefUrl": "/notes/1857184 "}, {"RefNumber": "1866475", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "New comment displays symbols while using other languages", "RefUrl": "/notes/1866475 "}, {"RefNumber": "1880043", "RefComponent": "BW-BEX-ET-WJR-DIA-FM", "RefTitle": "BEx Web 7.x: Formula editor not working in Firefox", "RefUrl": "/notes/1880043 "}, {"RefNumber": "1879450", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Alignment issue in export while using RIG Modules", "RefUrl": "/notes/1879450 "}, {"RefNumber": "1878705", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD ColumnWidth: Row height different in Firefox and Safari", "RefUrl": "/notes/1878705 "}, {"RefNumber": "1875189", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Scrolling: Enabling/Disabling works only on initial page", "RefUrl": "/notes/1875189 "}, {"RefNumber": "1869477", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Unknown error when doing drag and drop to Filter Pane area", "RefUrl": "/notes/1869477 "}, {"RefNumber": "1869381", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "NullPointerException during export when data entry is active", "RefUrl": "/notes/1869381 "}, {"RefNumber": "1861128", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD ColumnWidth: Data missing when wrapping enabled", "RefUrl": "/notes/1861128 "}, {"RefNumber": "1863880", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BEx Web 7.x: Data area commands causing exception", "RefUrl": "/notes/1863880 "}, {"RefNumber": "1869747", "RefComponent": "BI-RA-BICS", "RefTitle": "Eventing issues in Hana Provider", "RefUrl": "/notes/1869747 "}, {"RefNumber": "1857478", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Error: Unknown Binary Content Strategy: 0 export from ALV", "RefUrl": "/notes/1857478 "}, {"RefNumber": "1777033", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis Modification DynamicGrid", "RefUrl": "/notes/1777033 "}, {"RefNumber": "1879760", "RefComponent": "BW-BEX-ET-WJR-AD", "RefTitle": "Unable to set filter values for a structure in WAD", "RefUrl": "/notes/1879760 "}, {"RefNumber": "1866959", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "RSBOLAP 013 error calling value help with variable binding", "RefUrl": "/notes/1866959 "}, {"RefNumber": "1854618", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.x: Loading animation stops when closing a dialog", "RefUrl": "/notes/1854618 "}, {"RefNumber": "1864488", "RefComponent": "BI-RA-BICS", "RefTitle": "General Note for BICS consumer layer", "RefUrl": "/notes/1864488 "}, {"RefNumber": "1849586", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.30 - Fill dropdown boxes on demand", "RefUrl": "/notes/1849586 "}, {"RefNumber": "1862668", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "NullPointerException from com.sap.tc.logging.LogRecord.getId", "RefUrl": "/notes/1862668 "}, {"RefNumber": "1870458", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Date cell format is set to numeric in excel", "RefUrl": "/notes/1870458 "}, {"RefNumber": "1807261", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "Performance:F4-Help in Xcelsius dashboards.", "RefUrl": "/notes/1807261 "}, {"RefNumber": "1864019", "RefComponent": "BI-RA-BICS", "RefTitle": "Wrong formatted default value for input parameter Date", "RefUrl": "/notes/1864019 "}, {"RefNumber": "1863207", "RefComponent": "BI-RA-BICS", "RefTitle": "Sort hierarchy by member presentation does not work for HANA", "RefUrl": "/notes/1863207 "}, {"RefNumber": "1858626", "RefComponent": "BI-RA-BICS", "RefTitle": "supportsValueHelp returns true on EmptyVariable (HANA / BAE)", "RefUrl": "/notes/1858626 "}, {"RefNumber": "1873421", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "ChartItem: Bad Chart Performance", "RefUrl": "/notes/1873421 "}, {"RefNumber": "1835724", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Scrolling: Rows are not aligned", "RefUrl": "/notes/1835724 "}, {"RefNumber": "1875241", "RefComponent": "BW-BEX-ET-WJR-AD", "RefTitle": "Errror RSWADPREXEC(007) occurs when saving a web template", "RefUrl": "/notes/1875241 "}, {"RefNumber": "1867886", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Technical name of BEx Web Application is displayed instead", "RefUrl": "/notes/1867886 "}, {"RefNumber": "1862129", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "All the items inside the template include item are exported", "RefUrl": "/notes/1862129 "}, {"RefNumber": "1868981", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "VISIBILITY parameter is not set properly during the export", "RefUrl": "/notes/1868981 "}, {"RefNumber": "1857818", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "BEx Web 7.x: Sorting on structure elements in selector dialog", "RefUrl": "/notes/1857818 "}, {"RefNumber": "1865651", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Document lock with multiple users", "RefUrl": "/notes/1865651 "}, {"RefNumber": "1720519", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Termination when sorting on 7.x systems", "RefUrl": "/notes/1720519 "}, {"RefNumber": "1856691", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "Error 2032 during dashboard execution", "RefUrl": "/notes/1856691 "}, {"RefNumber": "1622134", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Safety belt: Results quantity is too large (Excel/PDF)", "RefUrl": "/notes/1622134 "}, {"RefNumber": "1839788", "RefComponent": "BI-RA-BICS", "RefTitle": "Performance issue when expanding level hierarchies", "RefUrl": "/notes/1839788 "}, {"RefNumber": "1766253", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Hierarchy icons in Analysis Item incorrect when RTL", "RefUrl": "/notes/1766253 "}, {"RefNumber": "1841827", "RefComponent": "BI-RA-BICS", "RefTitle": "Deleted variant not removed from variant list of other DS", "RefUrl": "/notes/1841827 "}, {"RefNumber": "1856799", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "Error Handling improvement in Disign time", "RefUrl": "/notes/1856799 "}, {"RefNumber": "1852347", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Incorrect error message:Characteristic XX has no master data", "RefUrl": "/notes/1852347 "}, {"RefNumber": "1816227", "RefComponent": "BI-RA-BICS", "RefTitle": "Issues in Levelvisibility", "RefUrl": "/notes/1816227 "}, {"RefNumber": "1841612", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "hidden items in container item are exported.", "RefUrl": "/notes/1841612 "}, {"RefNumber": "1847299", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Binding commands may fail for dependend variables", "RefUrl": "/notes/1847299 "}, {"RefNumber": "1859938", "RefComponent": "BI-RA-BICS", "RefTitle": "java.lang.ClassCastException in HANA provider with currency", "RefUrl": "/notes/1859938 "}, {"RefNumber": "1710626", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Safety belts and memory availability assurance", "RefUrl": "/notes/1710626 "}, {"RefNumber": "1840386", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD ColumnWidth: ALL_ROWS_WRAPPING parameter does not work", "RefUrl": "/notes/1840386 "}, {"RefNumber": "1853051", "RefComponent": "BI-RA-BICS", "RefTitle": "java.lang.StackOverflowError in Levelvisibility", "RefUrl": "/notes/1853051 "}, {"RefNumber": "1853798", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "BEx Web 7.x: Drag and drop issue in Firefox", "RefUrl": "/notes/1853798 "}, {"RefNumber": "1821717", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "7.0x:Metadata error messages for bookmarks or favorites.", "RefUrl": "/notes/1821717 "}, {"RefNumber": "1846403", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Webtemplate gets hanged while doing right click.", "RefUrl": "/notes/1846403 "}, {"RefNumber": "1841856", "RefComponent": "BI-RA-BICS", "RefTitle": "Error with RRI on hierarchical axis", "RefUrl": "/notes/1841856 "}, {"RefNumber": "1836137", "RefComponent": "BI-RA-BICS", "RefTitle": "Optimization for setInputString on compounded characteristic", "RefUrl": "/notes/1836137 "}, {"RefNumber": "1829588", "RefComponent": "BI-RA-BICS", "RefTitle": "NullPointerException when changing value of node variables", "RefUrl": "/notes/1829588 "}, {"RefNumber": "1781924", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "BEx Web 7.x: selector dialog enhancements and usability", "RefUrl": "/notes/1781924 "}, {"RefNumber": "1820370", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Fixed text of error message about missing master data", "RefUrl": "/notes/1820370 "}, {"RefNumber": "1850703", "RefComponent": "BW-BEX-ET-WJR-DIA-VA", "RefTitle": "Variable values description in the variable screen", "RefUrl": "/notes/1850703 "}, {"RefNumber": "1836846", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Command: Cell content is not exported", "RefUrl": "/notes/1836846 "}, {"RefNumber": "1824375", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD ColumnWidth: Cells are displaying ellipsis in IE", "RefUrl": "/notes/1824375 "}, {"RefNumber": "1851942", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Broadcaster: export to CSV, PDF, XML returns Unknown Error", "RefUrl": "/notes/1851942 "}, {"RefNumber": "1697539", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.30: Memory Usage Reduction on Export Scenarios", "RefUrl": "/notes/1697539 "}, {"RefNumber": "1847649", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Donot display Document Icons in the Result cells", "RefUrl": "/notes/1847649 "}, {"RefNumber": "1849551", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "JAVA BICS: data cell is not refreshed on Sign reversal", "RefUrl": "/notes/1849551 "}, {"RefNumber": "1845853", "RefComponent": "BI-RA-BICS", "RefTitle": "Keyfigure settings ignored after workbook conversion", "RefUrl": "/notes/1845853 "}, {"RefNumber": "1844200", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Comments not displaying with particular tab in analysis item", "RefUrl": "/notes/1844200 "}, {"RefNumber": "1826870", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web 7.x: Javascript deadlock in case of RRI to web", "RefUrl": "/notes/1826870 "}, {"RefNumber": "1820339", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Unexpected hierarchy node during input validation", "RefUrl": "/notes/1820339 "}, {"RefNumber": "1819227", "RefComponent": "BW-BEX-ET-WJR-DIA", "RefTitle": "BEx Web 7.x: Javascript error in case of selector upload", "RefUrl": "/notes/1819227 "}, {"RefNumber": "1846494", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "Xcelcius: Result set formatting displayed on both axes", "RefUrl": "/notes/1846494 "}, {"RefNumber": "1781468", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "ChartItem: Chart contains too much data", "RefUrl": "/notes/1781468 "}, {"RefNumber": "1835868", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Extra \"New\" is appended in New Document title on using $R", "RefUrl": "/notes/1835868 "}, {"RefNumber": "1819129", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Comment open in single document item can't be deleted", "RefUrl": "/notes/1819129 "}, {"RefNumber": "1838690", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Export: Unable to download in IE8", "RefUrl": "/notes/1838690 "}, {"RefNumber": "1824397", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Missing information in BI tab for master data in 730", "RefUrl": "/notes/1824397 "}, {"RefNumber": "1836983", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Not able to create comment using BW repository", "RefUrl": "/notes/1836983 "}, {"RefNumber": "1836912", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Opening a template with SDI/DLI gives error using /documents", "RefUrl": "/notes/1836912 "}, {"RefNumber": "1722984", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Keyfigure Structure sorting is not reflected in Java Web", "RefUrl": "/notes/1722984 "}, {"RefNumber": "1650853", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "ZEN corrections in RIC", "RefUrl": "/notes/1650853 "}, {"RefNumber": "1830889", "RefComponent": "BI-RA-BICS", "RefTitle": "Input string does not accept unicode input", "RefUrl": "/notes/1830889 "}, {"RefNumber": "1814691", "RefComponent": "BI-RA-BICS", "RefTitle": "Member cache not used correctly for hierarchy nodes", "RefUrl": "/notes/1814691 "}, {"RefNumber": "1817156", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Data Provider Clear Variables Flag not Working", "RefUrl": "/notes/1817156 "}, {"RefNumber": "1827848", "RefComponent": "BI-RA-BICS", "RefTitle": "Hierarchy instances not updated when prov. uses same instncs", "RefUrl": "/notes/1827848 "}, {"RefNumber": "1827404", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BW System locks when saving a Report through CRM system", "RefUrl": "/notes/1827404 "}, {"RefNumber": "1818225", "RefComponent": "BI-RA-BICS", "RefTitle": "NULLPOINTER while executing a query against NW 7.40", "RefUrl": "/notes/1818225 "}, {"RefNumber": "1824206", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD DocumentContent: Long text is displayed incorrectly", "RefUrl": "/notes/1824206 "}, {"RefNumber": "1815978", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Currency output incorrect when using division formula", "RefUrl": "/notes/1815978 "}, {"RefNumber": "1815314", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Error when executing planning function", "RefUrl": "/notes/1815314 "}, {"RefNumber": "1802714", "RefComponent": "BI-RA-BICS", "RefTitle": "BICS: Input of leaf returns hierarchy node presentation", "RefUrl": "/notes/1802714 "}, {"RefNumber": "1763740", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.31 <PERSON>Weaver, SP4 P10, ClassNotFound Exception", "RefUrl": "/notes/1763740 "}, {"RefNumber": "1827280", "RefComponent": "BW-BEX-ET-WJR-DIA", "RefTitle": "BEx Web 7.3x: Unable to launch using NWBC", "RefUrl": "/notes/1827280 "}, {"RefNumber": "1822586", "RefComponent": "BI-RA-BICS", "RefTitle": "Problems with formulas after deserialization of query view", "RefUrl": "/notes/1822586 "}, {"RefNumber": "1822009", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Support for Design Studio TLOGO object browsing in roles", "RefUrl": "/notes/1822009 "}, {"RefNumber": "1821838", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Portal runtime error on start migration", "RefUrl": "/notes/1821838 "}, {"RefNumber": "1821462", "RefComponent": "BI-RA-BICS", "RefTitle": "Many BICS_PROV_GET_PRESENTATIONS during result set fetch", "RefUrl": "/notes/1821462 "}, {"RefNumber": "1819593", "RefComponent": "BI-RA-BICS", "RefTitle": "Performance: BICS_PROV_GET_HRY_NODES calls increased", "RefUrl": "/notes/1819593 "}, {"RefNumber": "1817272", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Export to excel exports heirarchy images", "RefUrl": "/notes/1817272 "}, {"RefNumber": "1793133", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Improper button size in selector dialog in 730 with FireFox.", "RefUrl": "/notes/1793133 "}, {"RefNumber": "1783021", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Format: Wrong text color when exported", "RefUrl": "/notes/1783021 "}, {"RefNumber": "1778605", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Customer Exists in Web Dialogs Extensions", "RefUrl": "/notes/1778605 "}, {"RefNumber": "1763087", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Authorization check while saving the comment", "RefUrl": "/notes/1763087 "}, {"RefNumber": "1823742", "RefComponent": "BI-RA-BICS", "RefTitle": "Fix Selection-Hierarchy not working anymore after 1777473", "RefUrl": "/notes/1823742 "}, {"RefNumber": "1798003", "RefComponent": "BI-RA-BICS", "RefTitle": "ConcurrentModificationException when disable/enable formula", "RefUrl": "/notes/1798003 "}, {"RefNumber": "1817504", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "730:Personalization does not work for template include item.", "RefUrl": "/notes/1817504 "}, {"RefNumber": "1812885", "RefComponent": "BI-RA-BICS", "RefTitle": "Design Time: Get key figure information from data cells", "RefUrl": "/notes/1812885 "}, {"RefNumber": "1811638", "RefComponent": "BI-RA-BICS", "RefTitle": "Design Time: Wrong sorting of drill down characteristics", "RefUrl": "/notes/1811638 "}, {"RefNumber": "1807660", "RefComponent": "BI-RA-BICS", "RefTitle": "EmptySelection.EMPTY_UNMODIFIABLE_LIST is not thread safe", "RefUrl": "/notes/1807660 "}, {"RefNumber": "1778471", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "<PERSON><PERSON>pointer exception when creating comments", "RefUrl": "/notes/1778471 "}, {"RefNumber": "1764271", "RefComponent": "BI-RA-BICS", "RefTitle": "Performance: no synch before retrieval of hierarchies", "RefUrl": "/notes/1764271 "}, {"RefNumber": "1763826", "RefComponent": "BI-RA-BICS", "RefTitle": "Name eines Klons ändert sich beim Deserialisieren", "RefUrl": "/notes/1763826 "}, {"RefNumber": "1810307", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "ODOC 7.X : Display Name is blank in New Document dialog", "RefUrl": "/notes/1810307 "}, {"RefNumber": "1760831", "RefComponent": "BI-RA-BICS", "RefTitle": "Falsche Werte bei Listrechnung und Hierarchien", "RefUrl": "/notes/1760831 "}, {"RefNumber": "1808320", "RefComponent": "BI-RA-BICS", "RefTitle": "List calculation wrong values with hierarchical structur", "RefUrl": "/notes/1808320 "}, {"RefNumber": "1811001", "RefComponent": "BI-RA-BICS", "RefTitle": "Slow performance with hidden totals", "RefUrl": "/notes/1811001 "}, {"RefNumber": "1811229", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Incorrect Default Values in RSADMIN Parameters", "RefUrl": "/notes/1811229 "}, {"RefNumber": "1813105", "RefComponent": "BI-RA-BICS", "RefTitle": "Flat mode not supported exception while loading state xml", "RefUrl": "/notes/1813105 "}, {"RefNumber": "1812844", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: EP Doc and BW SecurityCheck broken note1486692", "RefUrl": "/notes/1812844 "}, {"RefNumber": "1812535", "RefComponent": "BI-RA-BICS", "RefTitle": "# character displayed instead of compound display key", "RefUrl": "/notes/1812535 "}, {"RefNumber": "1814632", "RefComponent": "BI-RA-BICS", "RefTitle": "Changing clipping influences object resultset", "RefUrl": "/notes/1814632 "}, {"RefNumber": "1803890", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "No service generation possible", "RefUrl": "/notes/1803890 "}, {"RefNumber": "1660837", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Drag and Drop: Usability enhancement selector dialog", "RefUrl": "/notes/1660837 "}, {"RefNumber": "1785491", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Error when executing planning function", "RefUrl": "/notes/1785491 "}, {"RefNumber": "1810004", "RefComponent": "BI-RA-BICS", "RefTitle": "Reduce GET_PRESENTATION calls during result set rendering", "RefUrl": "/notes/1810004 "}, {"RefNumber": "1808769", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Comments are not displayed in portal if created from RSA1", "RefUrl": "/notes/1808769 "}, {"RefNumber": "1724597", "RefComponent": "BI-RA-BICS", "RefTitle": "Determine axis extent without collecting the result set", "RefUrl": "/notes/1724597 "}, {"RefNumber": "1810027", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Export failed for the first time when docuemnt item is used", "RefUrl": "/notes/1810027 "}, {"RefNumber": "1809418", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "ODOC : 400 bad request when web dispatcher is used", "RefUrl": "/notes/1809418 "}, {"RefNumber": "1710528", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Java Objects Reuse for GC run reduction", "RefUrl": "/notes/1710528 "}, {"RefNumber": "1801521", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Incorrect Pagebreaks while exporting the webitems to PDF", "RefUrl": "/notes/1801521 "}, {"RefNumber": "1744421", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification command - Results rows/totals rows", "RefUrl": "/notes/1744421 "}, {"RefNumber": "1808977", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "High CPU utilization is observed by the jlaunch process", "RefUrl": "/notes/1808977 "}, {"RefNumber": "1798326", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD ColumnMerge: ArrayIndexOutOfBoundsException", "RefUrl": "/notes/1798326 "}, {"RefNumber": "1802978", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Scrolling: Unknown E<PERSON>r when paging", "RefUrl": "/notes/1802978 "}, {"RefNumber": "1794143", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD DocumentContent: multiple document content not displayed", "RefUrl": "/notes/1794143 "}, {"RefNumber": "1776803", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Reports- Custom Error Page not displayed for timeout.", "RefUrl": "/notes/1776803 "}, {"RefNumber": "1798932", "RefComponent": "BI-RA-BICS", "RefTitle": "# display for hierarchy node variable (mixed compound)", "RefUrl": "/notes/1798932 "}, {"RefNumber": "1801543", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Variable screen not shown for bookmarks.", "RefUrl": "/notes/1801543 "}, {"RefNumber": "1759747", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "BEx Web: Customer Mask for New Document Title in Dialog", "RefUrl": "/notes/1759747 "}, {"RefNumber": "1808710", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "730 ODOC: java.lang.NullPointerException for input ready", "RefUrl": "/notes/1808710 "}, {"RefNumber": "1798729", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "730broadcast: visible item inside hidden item not exported", "RefUrl": "/notes/1798729 "}, {"RefNumber": "1750578", "RefComponent": "BI-RA-BICS", "RefTitle": "Synchronization before retrieving hierarchies of variable", "RefUrl": "/notes/1750578 "}, {"RefNumber": "1796030", "RefComponent": "BW-BEX-ET-WJR-AD", "RefTitle": "Characteristic Valuehelp is unavailable in WAD design time", "RefUrl": "/notes/1796030 "}, {"RefNumber": "1804487", "RefComponent": "BW-BEX-ET-WJR-AD", "RefTitle": "Transformer-Exception : RSBOLAP 014 \\ RSBOLAP 013", "RefUrl": "/notes/1804487 "}, {"RefNumber": "1782290", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD DocumentContent: Wrong exception color when exported", "RefUrl": "/notes/1782290 "}, {"RefNumber": "1781567", "RefComponent": "BI-RA-BICS", "RefTitle": "Retrieve reference characteristic of hierarchy variables", "RefUrl": "/notes/1781567 "}, {"RefNumber": "1780979", "RefComponent": "BI-RA-BICS", "RefTitle": "setViewXml() for Planning Function Filter", "RefUrl": "/notes/1780979 "}, {"RefNumber": "1780003", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Scrolling: Minor updates and performance optimization", "RefUrl": "/notes/1780003 "}, {"RefNumber": "1777473", "RefComponent": "BI-RA-BICS", "RefTitle": "Falsche Hierarchie bei Zugriff auf Knoten im fixen Filter", "RefUrl": "/notes/1777473 "}, {"RefNumber": "1774138", "RefComponent": "BI-RA-BICS", "RefTitle": "Escapetes Semikolon wird nicht als Input String akzeptiert", "RefUrl": "/notes/1774138 "}, {"RefNumber": "1773501", "RefComponent": "BI-RA-BICS", "RefTitle": "Korrekte technische Namen beim browsen von <PERSON>", "RefUrl": "/notes/1773501 "}, {"RefNumber": "1769497", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Characteristic Valuehelp is unavailable in WAD design time", "RefUrl": "/notes/1769497 "}, {"RefNumber": "1779250", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "BEx Web 7.3x: Missing Documents after BI SP 8 path 20", "RefUrl": "/notes/1779250 "}, {"RefNumber": "1766869", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "BEx Web 7.3x: document list item causes exception", "RefUrl": "/notes/1766869 "}, {"RefNumber": "1792986", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD ColumnWidth: Module does not work in Firefox", "RefUrl": "/notes/1792986 "}, {"RefNumber": "1795440", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Context menu still visible after executing operations", "RefUrl": "/notes/1795440 "}, {"RefNumber": "1775209", "RefComponent": "BI-RA-BICS", "RefTitle": "Filter on hierarchy does not work", "RefUrl": "/notes/1775209 "}, {"RefNumber": "1775536", "RefComponent": "BI-RA-BICS", "RefTitle": "Performance: BICS_PROV_SET_STATE RFC Optimiziation", "RefUrl": "/notes/1775536 "}, {"RefNumber": "1775211", "RefComponent": "BI-RA-BICS", "RefTitle": "Error when applying hierarchy", "RefUrl": "/notes/1775211 "}, {"RefNumber": "1770853", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Dropdown in the variable dialog for Single values variable", "RefUrl": "/notes/1770853 "}, {"RefNumber": "1778158", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Issues in Scripting after SPS7#40 (Regression)", "RefUrl": "/notes/1778158 "}, {"RefNumber": "1791118", "RefComponent": "BW-BEX-ET-WJR-DIA-VA", "RefTitle": "Variable values description in the variable screen", "RefUrl": "/notes/1791118 "}, {"RefNumber": "1550398", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: Handling of parameter SCRIPT in Web Applications", "RefUrl": "/notes/1550398 "}, {"RefNumber": "1784773", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: Missing Notification on Downloads of Documents", "RefUrl": "/notes/1784773 "}, {"RefNumber": "1763305", "RefComponent": "BI-RA-BICS", "RefTitle": "Prototype Serenity Provider for FireFly", "RefUrl": "/notes/1763305 "}, {"RefNumber": "1769219", "RefComponent": "BI-RA-BICS", "RefTitle": "Sortieren anhand der Selektion im Back End", "RefUrl": "/notes/1769219 "}, {"RefNumber": "1786691", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.x: Javascript error if body onLoad event is used", "RefUrl": "/notes/1786691 "}, {"RefNumber": "1737417", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "BEx Web 7.x: upload selections from file or clipboard", "RefUrl": "/notes/1737417 "}, {"RefNumber": "1779110", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web variable on text direct input not possible", "RefUrl": "/notes/1779110 "}, {"RefNumber": "1771551", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Hidden item are exported from container layout item", "RefUrl": "/notes/1771551 "}, {"RefNumber": "1632580", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "BEx Web 7.30 Report Item: Misisng Export of MIMEs from MIME", "RefUrl": "/notes/1632580 "}, {"RefNumber": "1773465", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "cell format text when a cell has input ready and static sect", "RefUrl": "/notes/1773465 "}, {"RefNumber": "1758171", "RefComponent": "BI-RA-BICS", "RefTitle": "SET_VARIABLE_STATE Kommando funktioniert nicht bei Knoten", "RefUrl": "/notes/1758171 "}, {"RefNumber": "1780846", "RefComponent": "BW-BEX-ET-WJR-GRAPH", "RefTitle": "MapItem: Barheights are not correctly rendered", "RefUrl": "/notes/1780846 "}, {"RefNumber": "1780093", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "System displays very long texts as ### in Excel 2007", "RefUrl": "/notes/1780093 "}, {"RefNumber": "1768970", "RefComponent": "BW-BEX-ET-WJR-ITM-FL", "RefTitle": "Error while using OPEN_DIALOG_SAVE_LOCAL_VIEW command.", "RefUrl": "/notes/1768970 "}, {"RefNumber": "1780982", "RefComponent": "BI-RA-BICS", "RefTitle": "Open a DataArea with a given name not possible", "RefUrl": "/notes/1780982 "}, {"RefNumber": "1773577", "RefComponent": "BI-RA-BICS", "RefTitle": "Unnecessary sorting syncronization for time characteristics", "RefUrl": "/notes/1773577 "}, {"RefNumber": "1709614", "RefComponent": "BI-RA-BICS", "RefTitle": "Sammel Hinweis für Transiente Hierarchien", "RefUrl": "/notes/1709614 "}, {"RefNumber": "1771038", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx & CRM UI: Issues in Session Management on NW 7.3x", "RefUrl": "/notes/1771038 "}, {"RefNumber": "1742571", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Selector: New search functionality", "RefUrl": "/notes/1742571 "}, {"RefNumber": "1774177", "RefComponent": "BI-RA-BICS", "RefTitle": "Error when executing Web Template with node alignment BOTTOM", "RefUrl": "/notes/1774177 "}, {"RefNumber": "1766756", "RefComponent": "BI-RA-BICS", "RefTitle": "Ändern des Hierarchiestichtags führt zu neuem Result Set", "RefUrl": "/notes/1766756 "}, {"RefNumber": "1783963", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "7.3x:URL_BOOKMARK shows blank (loads the default template)", "RefUrl": "/notes/1783963 "}, {"RefNumber": "1781734", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "7.30:lowerRight must not be left or above of upperLeft.", "RefUrl": "/notes/1781734 "}, {"RefNumber": "1770492", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Depricated functions cleanup from Preexecution services", "RefUrl": "/notes/1770492 "}, {"RefNumber": "1600962", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: URL Parameter LANGUAGE not always working", "RefUrl": "/notes/1600962 "}, {"RefNumber": "1767681", "RefComponent": "BW-BEX-ET-WJR-DIA-OS", "RefTitle": "Error in rendering root for dialog 'LOAD' in the Open dialog", "RefUrl": "/notes/1767681 "}, {"RefNumber": "1769860", "RefComponent": "BI-RA-BICS", "RefTitle": "Object reference exception when using Hana Analytic View", "RefUrl": "/notes/1769860 "}, {"RefNumber": "1769368", "RefComponent": "BI-RA-BICS", "RefTitle": "NullPointerException bei Interval ohne obere Grenze", "RefUrl": "/notes/1769368 "}, {"RefNumber": "1770288", "RefComponent": "BI-RA-BICS", "RefTitle": "Änderungen v Selection Präsentationen invalidieren ResultSet", "RefUrl": "/notes/1770288 "}, {"RefNumber": "1783878", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "7.30 Error while running dashboard using BICS remote app.", "RefUrl": "/notes/1783878 "}, {"RefNumber": "1775727", "RefComponent": "BI-RA-BICS", "RefTitle": "Plandata of a DataArea not released correctly", "RefUrl": "/notes/1775727 "}, {"RefNumber": "1769650", "RefComponent": "BI-RA-BICS", "RefTitle": "Performance: <PERSON><PERSON><PERSON><PERSON><PERSON> SET_STATE bei Query mit Bedingungen", "RefUrl": "/notes/1769650 "}, {"RefNumber": "1765922", "RefComponent": "BI-RA-BICS", "RefTitle": "IndexOutOfBounds Exception bei symmetrischen Drill", "RefUrl": "/notes/1765922 "}, {"RefNumber": "1760582", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Scrolling: Enable row selection and horizontal scrolling", "RefUrl": "/notes/1760582 "}, {"RefNumber": "1679787", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Drag & Drop operation on input ready query causes exception", "RefUrl": "/notes/1679787 "}, {"RefNumber": "1751390", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: Customer Language on RFC (JCo) Communication", "RefUrl": "/notes/1751390 "}, {"RefNumber": "1764448", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web & NW Business Client: issues in NW release 7.30", "RefUrl": "/notes/1764448 "}, {"RefNumber": "1756779", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Planning: 'Selection Not Defined' when used in Firefox", "RefUrl": "/notes/1756779 "}, {"RefNumber": "1613110", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Text Item is not wrapped while exporting", "RefUrl": "/notes/1613110 "}, {"RefNumber": "1762092", "RefComponent": "BI-RA-BICS", "RefTitle": "Performance Verbesserung in Level Visibility", "RefUrl": "/notes/1762092 "}, {"RefNumber": "1762770", "RefComponent": "BI-RA-BICS", "RefTitle": "<PERSON>ull Pointer beim Setzten eines State XMLs mit Sortierung", "RefUrl": "/notes/1762770 "}, {"RefNumber": "1568175", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Performance: minor improvement for variants in Java Web", "RefUrl": "/notes/1568175 "}, {"RefNumber": "1666825", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification command - JavaScript by clicking mouse", "RefUrl": "/notes/1666825 "}, {"RefNumber": "1751251", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Sorting by attribute is not working in Java Web Runtime", "RefUrl": "/notes/1751251 "}, {"RefNumber": "1698072", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Application state changes after OK_TEMPLATE_DIALOG command", "RefUrl": "/notes/1698072 "}, {"RefNumber": "1755701", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification: Own symbols for exception - MS Excel", "RefUrl": "/notes/1755701 "}, {"RefNumber": "1758231", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Error while generating HTML after F4 help screen", "RefUrl": "/notes/1758231 "}, {"RefNumber": "1757529", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Performance: suboptimal logic in InfoFieldItem for Variables", "RefUrl": "/notes/1757529 "}, {"RefNumber": "1753511", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Incorrect display of changed values of input ready queries", "RefUrl": "/notes/1753511 "}, {"RefNumber": "1745250", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Cache warm up for binding: follow-up correction for 1598020", "RefUrl": "/notes/1745250 "}, {"RefNumber": "1744304", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Percent (%) not exported when using cumulative calculation", "RefUrl": "/notes/1744304 "}, {"RefNumber": "1761315", "RefComponent": "BI-RA-BICS", "RefTitle": "Fe<PERSON> beim Laden von eines Queryviews ohne Struktur", "RefUrl": "/notes/1761315 "}, {"RefNumber": "1760423", "RefComponent": "BI-RA-BICS", "RefTitle": "Memoryverbrauch steigt mit anhaltender Laufzeit", "RefUrl": "/notes/1760423 "}, {"RefNumber": "1761337", "RefComponent": "BI-RA-BICS", "RefTitle": "Unterstützung von Presentations im CSV Provider", "RefUrl": "/notes/1761337 "}, {"RefNumber": "1761933", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "BEx Web 7.x: Unnecessary padding around input controls", "RefUrl": "/notes/1761933 "}, {"RefNumber": "1749168", "RefComponent": "BI-RA-BICS", "RefTitle": "Performance: Reading query text initially lesen (Java)", "RefUrl": "/notes/1749168 "}, {"RefNumber": "1749267", "RefComponent": "BI-RA-BICS", "RefTitle": "Performanz-Probleme bei Anzeige-Attributen", "RefUrl": "/notes/1749267 "}, {"RefNumber": "1749659", "RefComponent": "BI-RA-BICS", "RefTitle": "Performance: BICS_PROV_GET_MEMBERS reduction (Java)", "RefUrl": "/notes/1749659 "}, {"RefNumber": "1751295", "RefComponent": "BI-RA-BICS", "RefTitle": "Synchronisieren der Variablenwerte vor synchronizeStateVars", "RefUrl": "/notes/1751295 "}, {"RefNumber": "1752281", "RefComponent": "BI-RA-BICS", "RefTitle": "Performance: Read hierarchies initially and set in back end", "RefUrl": "/notes/1752281 "}, {"RefNumber": "1752348", "RefComponent": "BI-RA-BICS", "RefTitle": "Performance: Delta processing in BICS XML parser", "RefUrl": "/notes/1752348 "}, {"RefNumber": "1752395", "RefComponent": "BI-RA-BICS", "RefTitle": "Abbruch bei Nullunterdrückung mit Hierarchien", "RefUrl": "/notes/1752395 "}, {"RefNumber": "1754345", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD ColumnWidth: Hidden columns are visible when exported", "RefUrl": "/notes/1754345 "}, {"RefNumber": "1755075", "RefComponent": "BI-RA-BICS", "RefTitle": "Variantenservice enthält nicht neu erzeugte Varianten", "RefUrl": "/notes/1755075 "}, {"RefNumber": "1755368", "RefComponent": "BI-RA-BICS", "RefTitle": "IPropertyAccess.getStringValue() liefert nicht den Default", "RefUrl": "/notes/1755368 "}, {"RefNumber": "1756173", "RefComponent": "BI-RA-BICS", "RefTitle": "Probleme bei Events während des Veränderns v Achsenmerkmalen", "RefUrl": "/notes/1756173 "}, {"RefNumber": "1757359", "RefComponent": "BI-RA-BICS", "RefTitle": "Entfernen eines externen Variablen Containers erzwingen", "RefUrl": "/notes/1757359 "}, {"RefNumber": "1759140", "RefComponent": "BI-RA-BICS", "RefTitle": "Unvollständige Anzeige von Rollen beim Speichern", "RefUrl": "/notes/1759140 "}, {"RefNumber": "1712476", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Input values disappear while performing 'Cancel' on F4 help", "RefUrl": "/notes/1712476 "}, {"RefNumber": "1753730", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification comments - word wrap in text", "RefUrl": "/notes/1753730 "}, {"RefNumber": "1742600", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Bookmark Popup is not resized as per the items length", "RefUrl": "/notes/1742600 "}, {"RefNumber": "1760858", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Java 7.01: Hierarchy corrupt after node condensation", "RefUrl": "/notes/1760858 "}, {"RefNumber": "1751318", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Performance: variables filled by pre-query in Java Web", "RefUrl": "/notes/1751318 "}, {"RefNumber": "1711936", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Limit number of member selections", "RefUrl": "/notes/1711936 "}, {"RefNumber": "1761327", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NoSuchMethodError in XSSEncoder after BW upgrade", "RefUrl": "/notes/1761327 "}, {"RefNumber": "1690459", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "JAVA BICS 730: Key Figure state lost after filtering", "RefUrl": "/notes/1690459 "}, {"RefNumber": "1674557", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "RIG: <PERSON><PERSON> does not work in Firefox", "RefUrl": "/notes/1674557 "}, {"RefNumber": "1733250", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Scrolling: Row selection support", "RefUrl": "/notes/1733250 "}, {"RefNumber": "1759539", "RefComponent": "BW-BEX-ET-WJR-EP", "RefTitle": "BEx Web is not supporting JRA (relevant for 7.30 & onwards)", "RefUrl": "/notes/1759539 "}, {"RefNumber": "1682487", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web ODOC: ABAP Side Improvements in 7.3x systems", "RefUrl": "/notes/1682487 "}, {"RefNumber": "1571649", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis Modifikation Kommentare - Fixe Spalten/Zeilen", "RefUrl": "/notes/1571649 "}, {"RefNumber": "1748993", "RefComponent": "BI-RA-BICS", "RefTitle": "Levelsichtbarkeit beim Wechsel des Resultset Typen", "RefUrl": "/notes/1748993 "}, {"RefNumber": "1748787", "RefComponent": "BI-RA-BICS", "RefTitle": "Fehler beim Submitten eines gemergten Variablencontainers", "RefUrl": "/notes/1748787 "}, {"RefNumber": "1749506", "RefComponent": "BI-RA-BICS", "RefTitle": "Performance: Directly reading back-end capabilities (Java)", "RefUrl": "/notes/1749506 "}, {"RefNumber": "1751030", "RefComponent": "BI-RA-BICS", "RefTitle": "Zustands XML einer Selektion lässt sich nicht wieder setzen", "RefUrl": "/notes/1751030 "}, {"RefNumber": "1748970", "RefComponent": "BI-RA-BICS", "RefTitle": "Neue Formeln nicht sichtbar bei hierarchischen Strukturen", "RefUrl": "/notes/1748970 "}, {"RefNumber": "1751933", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Performance: BICS_PROV_GET_MEMBERS - cumulative note", "RefUrl": "/notes/1751933 "}, {"RefNumber": "1703966", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Limit number of user applications", "RefUrl": "/notes/1703966 "}, {"RefNumber": "1745680", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "ChartItem: Unknown error after export to excel", "RefUrl": "/notes/1745680 "}, {"RefNumber": "1660680", "RefComponent": "BW-BEX-ET-WJR-DIA-FM", "RefTitle": "Description in Formula Editor with several lines", "RefUrl": "/notes/1660680 "}, {"RefNumber": "1747664", "RefComponent": "BI-RA-BICS", "RefTitle": "Incorrect sorting type in query view XML", "RefUrl": "/notes/1747664 "}, {"RefNumber": "1749109", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "<PERSON> created while performing 'Edit online' & 'Edit locally'", "RefUrl": "/notes/1749109 "}, {"RefNumber": "1748934", "RefComponent": "BI-RA-BICS", "RefTitle": "Incorrect display of SAP HANA hierarchies", "RefUrl": "/notes/1748934 "}, {"RefNumber": "1748254", "RefComponent": "BI-RA-BICS", "RefTitle": "Display setting doesnt change correct on free characteristic", "RefUrl": "/notes/1748254 "}, {"RefNumber": "1747979", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Broadcaster offline filter navigation not working", "RefUrl": "/notes/1747979 "}, {"RefNumber": "1722776", "RefComponent": "BI-RA-BICS", "RefTitle": "Validation enhancement of the SET_SELECTION_STATE command", "RefUrl": "/notes/1722776 "}, {"RefNumber": "1746940", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Analysis item document symbols not updated in 7.3x", "RefUrl": "/notes/1746940 "}, {"RefNumber": "1702994", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Export and Template Refresh Chart Disapearing", "RefUrl": "/notes/1702994 "}, {"RefNumber": "1727905", "RefComponent": "BI-RA-BICS", "RefTitle": "Reduction of the bics_cons_get_rsadmin_param RFC calls", "RefUrl": "/notes/1727905 "}, {"RefNumber": "1740884", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Java Web: block selection in grid controls (Shift-Key)", "RefUrl": "/notes/1740884 "}, {"RefNumber": "1748459", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Dynamic Input: Restore cell focus doesn't work", "RefUrl": "/notes/1748459 "}, {"RefNumber": "1750888", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Design limitations of BI services with active hierarchies", "RefUrl": "/notes/1750888 "}, {"RefNumber": "1748453", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "MOD Planning: unexpected cells are highlighted", "RefUrl": "/notes/1748453 "}, {"RefNumber": "1746611", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "ODOC 730 : Document list item UI changed in 730 only", "RefUrl": "/notes/1746611 "}, {"RefNumber": "1747939", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Composite note for unified rendering corrections in BW", "RefUrl": "/notes/1747939 "}, {"RefNumber": "1718739", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Test Automation with BEx Web Traces Validation", "RefUrl": "/notes/1718739 "}, {"RefNumber": "1732996", "RefComponent": "BI-RA-BICS", "RefTitle": "Variable value ambiguous with compound characteristics", "RefUrl": "/notes/1732996 "}, {"RefNumber": "1730148", "RefComponent": "BI-RA-BICS", "RefTitle": "Incorrect result set due to two active conditions", "RefUrl": "/notes/1730148 "}, {"RefNumber": "1729696", "RefComponent": "BI-RA-BICS", "RefTitle": "Setting characteristic context of a condition to hier.leaves", "RefUrl": "/notes/1729696 "}, {"RefNumber": "1744706", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Order of structure elements and key figures not stored", "RefUrl": "/notes/1744706 "}, {"RefNumber": "1685840", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Drag and drop only affects data not column header", "RefUrl": "/notes/1685840 "}, {"RefNumber": "1722983", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Recommendations to resolve 'NO ESID FOUND' error", "RefUrl": "/notes/1722983 "}, {"RefNumber": "1739187", "RefComponent": "BI-RA-BICS", "RefTitle": "Object Service: Call if readProperties are supported", "RefUrl": "/notes/1739187 "}, {"RefNumber": "1739748", "RefComponent": "BI-RA-BICS", "RefTitle": "Error when undoing several steps at the same time", "RefUrl": "/notes/1739748 "}, {"RefNumber": "1736195", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x SPS7/P30: Report Item: Chart Customizing Issue", "RefUrl": "/notes/1736195 "}, {"RefNumber": "1736098", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Drill State is lost after adding a local formula in Java", "RefUrl": "/notes/1736098 "}, {"RefNumber": "1743305", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Removal of the securelib in 700 and 701", "RefUrl": "/notes/1743305 "}, {"RefNumber": "1734016", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "NumberFormatException in BicsRemoteBex servlet.", "RefUrl": "/notes/1734016 "}, {"RefNumber": "1731856", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Document Module: Shows duplicate content on different cells", "RefUrl": "/notes/1731856 "}, {"RefNumber": "1729629", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Scrolling Module: Stops working after row is dragged out", "RefUrl": "/notes/1729629 "}, {"RefNumber": "1688678", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Memory Usage Improvements for Web Applications", "RefUrl": "/notes/1688678 "}, {"RefNumber": "1697196", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Report Item settings are not taken while exporting in 730", "RefUrl": "/notes/1697196 "}, {"RefNumber": "1718205", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Performance improvement by synchronisation", "RefUrl": "/notes/1718205 "}, {"RefNumber": "1726785", "RefComponent": "BI-RA-BICS", "RefTitle": "Table/View cannot be found in SAP hierarchies", "RefUrl": "/notes/1726785 "}, {"RefNumber": "1739597", "RefComponent": "BI-RA-BICS", "RefTitle": "Incorrect use of decimal separators in a formula", "RefUrl": "/notes/1739597 "}, {"RefNumber": "1739081", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BEx Web 7.3x: Error while generating HTML after sorting", "RefUrl": "/notes/1739081 "}, {"RefNumber": "1737499", "RefComponent": "BI-RA-BICS", "RefTitle": "Incorrect result for percent calculation", "RefUrl": "/notes/1737499 "}, {"RefNumber": "1736004", "RefComponent": "BI-RA-BICS", "RefTitle": "Detailed types for variables in BICS", "RefUrl": "/notes/1736004 "}, {"RefNumber": "1694649", "RefComponent": "BI-RA-BICS", "RefTitle": "Search for members using characteristic attributes (Java)", "RefUrl": "/notes/1694649 "}, {"RefNumber": "1733496", "RefComponent": "BI-RA-BICS", "RefTitle": "Perf.: Reusing planning function and planning sequence JAVA", "RefUrl": "/notes/1733496 "}, {"RefNumber": "1733320", "RefComponent": "BI-RA-BICS", "RefTitle": "Error for several excluding selections in the query filter", "RefUrl": "/notes/1733320 "}, {"RefNumber": "1729005", "RefComponent": "BI-RA-BICS", "RefTitle": "DSO planning on cells that do not exist (NONEXT) JAVA", "RefUrl": "/notes/1729005 "}, {"RefNumber": "1724447", "RefComponent": "BI-RA-BICS", "RefTitle": "Changes in the number of decimal places are not displayed", "RefUrl": "/notes/1724447 "}, {"RefNumber": "1683520", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification DynamicGrid for Hichert design", "RefUrl": "/notes/1683520 "}, {"RefNumber": "1713344", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Error text missing in the Document Browser Dialog", "RefUrl": "/notes/1713344 "}, {"RefNumber": "1737891", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "730:Changes to policy domain authentication for BICS remote.", "RefUrl": "/notes/1737891 "}, {"RefNumber": "1735436", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: Supportdesk: Incorrect warning SPS7/30 on SPS5", "RefUrl": "/notes/1735436 "}, {"RefNumber": "1739211", "RefComponent": "BI-RA-BICS", "RefTitle": "Variant service is reused for the same query", "RefUrl": "/notes/1739211 "}, {"RefNumber": "1736964", "RefComponent": "BI-RA-BICS", "RefTitle": "Null pointer for symmetric drill and result set size limit", "RefUrl": "/notes/1736964 "}, {"RefNumber": "1647346", "RefComponent": "BI-RA-BICS", "RefTitle": "Object Services property without localization and format", "RefUrl": "/notes/1647346 "}, {"RefNumber": "1726145", "RefComponent": "BI-RA-BICS", "RefTitle": "SAP HANA hierarchy appears in duplicate", "RefUrl": "/notes/1726145 "}, {"RefNumber": "1717730", "RefComponent": "BI-RA-BICS", "RefTitle": "Fetaure :BICS Consumer Sorting", "RefUrl": "/notes/1717730 "}, {"RefNumber": "1721567", "RefComponent": "BI-RA-BICS", "RefTitle": "Structure does not return hierarchy", "RefUrl": "/notes/1721567 "}, {"RefNumber": "1730132", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Lifetime of cached BI Java MIME objects", "RefUrl": "/notes/1730132 "}, {"RefNumber": "1730163", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "730:Visible item inside hidden group item is not exported.", "RefUrl": "/notes/1730163 "}, {"RefNumber": "1728046", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "730: <PERSON><PERSON><PERSON> not delivered from reusable script item.", "RefUrl": "/notes/1728046 "}, {"RefNumber": "1726912", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx WEb: Export CSV with Broadcasting: UTF-16 as default", "RefUrl": "/notes/1726912 "}, {"RefNumber": "1717836", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "BEx Web 7.3x (SPS7#P20): PDF Export scaling and headers", "RefUrl": "/notes/1717836 "}, {"RefNumber": "1722186", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "XC: Improvement in error handling feature.", "RefUrl": "/notes/1722186 "}, {"RefNumber": "1718699", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Scrolling Module: Invalid Argument when all rows are removed", "RefUrl": "/notes/1718699 "}, {"RefNumber": "1739624", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "ChartItem: Generating the chart as JPEG", "RefUrl": "/notes/1739624 "}, {"RefNumber": "1717916", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Broadcast to excel contains hierarchy images.", "RefUrl": "/notes/1717916 "}, {"RefNumber": "1718206", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.30: Memory reduction in Xcelsius scenarios", "RefUrl": "/notes/1718206 "}, {"RefNumber": "1717511", "RefComponent": "BI-RA-BICS", "RefTitle": "Statistic even 13003 is not terminated", "RefUrl": "/notes/1717511 "}, {"RefNumber": "1717368", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BEx Broadcaster: characteristic has no master data warning", "RefUrl": "/notes/1717368 "}, {"RefNumber": "1714736", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Scrolling Module: Alternative way to enable/disable", "RefUrl": "/notes/1714736 "}, {"RefNumber": "1713835", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Scrolling Module: Wrong tab order on input fields", "RefUrl": "/notes/1713835 "}, {"RefNumber": "1719029", "RefComponent": "BI-RA-BICS", "RefTitle": "ClassCastException when using hierarchy node variant", "RefUrl": "/notes/1719029 "}, {"RefNumber": "1689060", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: Thread <PERSON> in class ObjectFactory", "RefUrl": "/notes/1689060 "}, {"RefNumber": "1723007", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "'TARTITLE' error message is thrown while loading KM Bookmark", "RefUrl": "/notes/1723007 "}, {"RefNumber": "1660417", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Dynamic block column and row size based on number of cells", "RefUrl": "/notes/1660417 "}, {"RefNumber": "1714871", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Performance: compound characteristics variables in Java", "RefUrl": "/notes/1714871 "}, {"RefNumber": "1721338", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Limitations in creating and displying Documents in BEx Web", "RefUrl": "/notes/1721338 "}, {"RefNumber": "1682037", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Java Web 7.30: time zone settings are not taken into account", "RefUrl": "/notes/1682037 "}, {"RefNumber": "1718527", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BEx Web 7.3x: Analysis item document symbols not updated", "RefUrl": "/notes/1718527 "}, {"RefNumber": "1669888", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Modification Format to hide document icon", "RefUrl": "/notes/1669888 "}, {"RefNumber": "1716223", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Comments are getting locked automatically with KM repository", "RefUrl": "/notes/1716223 "}, {"RefNumber": "1716169", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Javascript error appears after closing Document dialog", "RefUrl": "/notes/1716169 "}, {"RefNumber": "1713495", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Context Menu: Missing translations planning and formulas", "RefUrl": "/notes/1713495 "}, {"RefNumber": "1709007", "RefComponent": "BI-RA-BICS", "RefTitle": "Error when setting a global variant that is already deleted", "RefUrl": "/notes/1709007 "}, {"RefNumber": "1671894", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Java Web 7.30: Document icons missing for key figure values", "RefUrl": "/notes/1671894 "}, {"RefNumber": "1664331", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Unknown Error after executing a query with conditions", "RefUrl": "/notes/1664331 "}, {"RefNumber": "1688741", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Comments are not displayed for some set of selections.", "RefUrl": "/notes/1688741 "}, {"RefNumber": "1687756", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Incorrect background display of charts after upgrade to 730", "RefUrl": "/notes/1687756 "}, {"RefNumber": "1667077", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification comment - Export with BLOCK_ROWS_SIZE", "RefUrl": "/notes/1667077 "}, {"RefNumber": "1715750", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "VC 7.30: too many result rows in case of hierarchy", "RefUrl": "/notes/1715750 "}, {"RefNumber": "1708700", "RefComponent": "BI-RA-BICS", "RefTitle": "Same gobal variable can be saved multiple times", "RefUrl": "/notes/1708700 "}, {"RefNumber": "1703138", "RefComponent": "BI-RA-BICS", "RefTitle": "Problem with bookmark after manually sorting hierarchy nodes", "RefUrl": "/notes/1703138 "}, {"RefNumber": "1717226", "RefComponent": "BI-RA-BICS", "RefTitle": "Korrekturen in Privot Table", "RefUrl": "/notes/1717226 "}, {"RefNumber": "1700732", "RefComponent": "BI-RA-BICS", "RefTitle": "Performance: Querying whether filter contains nodes & leaves", "RefUrl": "/notes/1700732 "}, {"RefNumber": "1702469", "RefComponent": "BI-RA-BICS", "RefTitle": "Member cache does not work when fetching permanent object", "RefUrl": "/notes/1702469 "}, {"RefNumber": "1587492", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Hidden items are not exported after 7.0 to 7.01 upgrade", "RefUrl": "/notes/1587492 "}, {"RefNumber": "1682779", "RefComponent": "BI-RA-BICS", "RefTitle": "Error with formula double variable in quey view", "RefUrl": "/notes/1682779 "}, {"RefNumber": "1688919", "RefComponent": "BI-RA-BICS", "RefTitle": "Providing the capability information of the provider", "RefUrl": "/notes/1688919 "}, {"RefNumber": "1701369", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Error in service ITEM_TEXT_SERVICE while opening a template", "RefUrl": "/notes/1701369 "}, {"RefNumber": "1694016", "RefComponent": "BI-RA-BICS", "RefTitle": "Feature: Level visibility", "RefUrl": "/notes/1694016 "}, {"RefNumber": "1700878", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Improper button size in the selector dialog with Firefox.", "RefUrl": "/notes/1700878 "}, {"RefNumber": "1706806", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Overlapping of Texts & Items in java web in FireFox\\Safari", "RefUrl": "/notes/1706806 "}, {"RefNumber": "1708919", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Template dialog functionality", "RefUrl": "/notes/1708919 "}, {"RefNumber": "1685766", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Problems with UR.RENDERERS/Class for Localization not Found", "RefUrl": "/notes/1685766 "}, {"RefNumber": "1682975", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Modification modules show different behaviour during Export", "RefUrl": "/notes/1682975 "}, {"RefNumber": "1698784", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Justifiable lock scenario:locked owner name is shown as null", "RefUrl": "/notes/1698784 "}, {"RefNumber": "1699942", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "NW 730:Exception for back to start or back navigation.", "RefUrl": "/notes/1699942 "}, {"RefNumber": "1688338", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Snippet rendering not working after TRANSFER_STATE command.", "RefUrl": "/notes/1688338 "}, {"RefNumber": "1681475", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis Modifikation Kommentar - Export (BLOCK_ROWS_SIZE) 2", "RefUrl": "/notes/1681475 "}, {"RefNumber": "1683605", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Zero values with units looses units when exporting to Excel", "RefUrl": "/notes/1683605 "}, {"RefNumber": "1688217", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "ConcurrentModificationException for iView with ContentLink", "RefUrl": "/notes/1688217 "}, {"RefNumber": "1679340", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "RIG: RemoveUnit module wrong output when space separator", "RefUrl": "/notes/1679340 "}, {"RefNumber": "1676337", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Export to Excel: Wrong value exported for 01/01/1901", "RefUrl": "/notes/1676337 "}, {"RefNumber": "1696706", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Assertion: selector<PERSON><PERSON><PERSON><PERSON><PERSON> is already assigned", "RefUrl": "/notes/1696706 "}, {"RefNumber": "1693478", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Performance: improvement of info object member cache in Java", "RefUrl": "/notes/1693478 "}, {"RefNumber": "1693333", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Invalid rendering of scroll bar after an export operation", "RefUrl": "/notes/1693333 "}, {"RefNumber": "1689984", "RefComponent": "BW-BEX-ET-WJR-DIA-VA", "RefTitle": "Sel. option variable value entered with '*' will show a '\\\\'", "RefUrl": "/notes/1689984 "}, {"RefNumber": "1696718", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "Xcelsius: <PERSON><PERSON><PERSON> message logged for the characteristic.", "RefUrl": "/notes/1696718 "}, {"RefNumber": "1695218", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Module Scrolling: Javascript error if used on multiple items", "RefUrl": "/notes/1695218 "}, {"RefNumber": "1693100", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "ChartItem: Tooltips do not contain scaling factors", "RefUrl": "/notes/1693100 "}, {"RefNumber": "1689808", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "ADS not working in 730 after upgrade", "RefUrl": "/notes/1689808 "}, {"RefNumber": "1689248", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Documents upload does not work after the upgrade to BW 7.3", "RefUrl": "/notes/1689248 "}, {"RefNumber": "1694035", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Not possible to edit comment due to LOCK issue in 730", "RefUrl": "/notes/1694035 "}, {"RefNumber": "1691719", "RefComponent": "BI-RA-BICS", "RefTitle": "Conditions not synchronized with the provider for windowing", "RefUrl": "/notes/1691719 "}, {"RefNumber": "1679416", "RefComponent": "BI-RA-BICS", "RefTitle": "Enhancement of the variants for Analysis Office and OLAP", "RefUrl": "/notes/1679416 "}, {"RefNumber": "1695386", "RefComponent": "BI-RA-BICS", "RefTitle": "Creation of VariantCatalogService in different system", "RefUrl": "/notes/1695386 "}, {"RefNumber": "1699440", "RefComponent": "BW-BEX-ET-WJR-DIA-OS", "RefTitle": "java.lang.NullPointerException occurs while closing dialog", "RefUrl": "/notes/1699440 "}, {"RefNumber": "1479703", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Calendar date is incorrect while invoking a selector dialog", "RefUrl": "/notes/1479703 "}, {"RefNumber": "1691004", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Unexpected hierarchy expand in selector dialog.", "RefUrl": "/notes/1691004 "}, {"RefNumber": "1641053", "RefComponent": "BW-BEX-ET-WJR-DIA-VA", "RefTitle": "Sel. option variable value entered with '*' will show a '/'", "RefUrl": "/notes/1641053 "}, {"RefNumber": "1680131", "RefComponent": "BI-RA-BICS", "RefTitle": "Termination when sorting on 7.3x systems", "RefUrl": "/notes/1680131 "}, {"RefNumber": "1682582", "RefComponent": "BI-RA-BICS", "RefTitle": "ABEND error when you query the value of a node", "RefUrl": "/notes/1682582 "}, {"RefNumber": "1666898", "RefComponent": "BI-RA-BICS", "RefTitle": "Object Services: Enhancement for workspaces", "RefUrl": "/notes/1666898 "}, {"RefNumber": "1657285", "RefComponent": "BI-RA-BICS", "RefTitle": "NumberOperator.getAllConstants results in various results", "RefUrl": "/notes/1657285 "}, {"RefNumber": "1676812", "RefComponent": "BI-RA-BICS", "RefTitle": "Native support of level visibility and leaf visibility", "RefUrl": "/notes/1676812 "}, {"RefNumber": "1669924", "RefComponent": "BI-RA-BICS", "RefTitle": "Native support for the list calculation", "RefUrl": "/notes/1669924 "}, {"RefNumber": "1658298", "RefComponent": "BI-RA-BICS", "RefTitle": "UnsupportedOperationException when setting the state -XMLs", "RefUrl": "/notes/1658298 "}, {"RefNumber": "1660949", "RefComponent": "BI-RA-BICS", "RefTitle": "Drill state is not reset if nodes are collapsed", "RefUrl": "/notes/1660949 "}, {"RefNumber": "1547714", "RefComponent": "BW-BEX-ET-WJR-DIA-VA", "RefTitle": "Parcer error for variable values with special operators", "RefUrl": "/notes/1547714 "}, {"RefNumber": "1688398", "RefComponent": "BI-RA-BICS", "RefTitle": "Deletion of formula for clone del. formula of clone parent", "RefUrl": "/notes/1688398 "}, {"RefNumber": "1688613", "RefComponent": "BI-RA-BICS", "RefTitle": "Zero for restricted key figures not recognized in SAP HANA", "RefUrl": "/notes/1688613 "}, {"RefNumber": "1597726", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "Xcelsius dashboard does not load", "RefUrl": "/notes/1597726 "}, {"RefNumber": "1685153", "RefComponent": "BI-RA-BICS", "RefTitle": "Feature: method setHierarchy and validate the selection", "RefUrl": "/notes/1685153 "}, {"RefNumber": "1679965", "RefComponent": "BI-RA-BICS", "RefTitle": "W/o result set raises supportsCurrencyTranslation() error", "RefUrl": "/notes/1679965 "}, {"RefNumber": "1683024", "RefComponent": "BI-RA-BICS", "RefTitle": "Resetting start variant does not work", "RefUrl": "/notes/1683024 "}, {"RefNumber": "1649606", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Dot displayed for empty values in grid", "RefUrl": "/notes/1649606 "}, {"RefNumber": "1683896", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Wrong display of variables from info field item in PDF.", "RefUrl": "/notes/1683896 "}, {"RefNumber": "1683948", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Comments are not saved and displayed correctly", "RefUrl": "/notes/1683948 "}, {"RefNumber": "1679761", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Report Item: Gridlines missing or no white background", "RefUrl": "/notes/1679761 "}, {"RefNumber": "1584387", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Problems with Firefox toolbar button width", "RefUrl": "/notes/1584387 "}, {"RefNumber": "1683985", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Issue with size of New document and Document browser dialog", "RefUrl": "/notes/1683985 "}, {"RefNumber": "1685407", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Nullpointer Exception occurs while performing export", "RefUrl": "/notes/1685407 "}, {"RefNumber": "1686040", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "Error 2032 for multiple connections to same BW query.", "RefUrl": "/notes/1686040 "}, {"RefNumber": "1667300", "RefComponent": "BI-RA-BICS", "RefTitle": "Exception when deleting a formula that is in the filter", "RefUrl": "/notes/1667300 "}, {"RefNumber": "1677745", "RefComponent": "BI-RA-BICS", "RefTitle": "getInputString() Component does not use space in escape seq", "RefUrl": "/notes/1677745 "}, {"RefNumber": "1648389", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Modification Format background color changes font family", "RefUrl": "/notes/1648389 "}, {"RefNumber": "1672858", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Invalid Base64-encoded character encountered - WD ALV Print", "RefUrl": "/notes/1672858 "}, {"RefNumber": "1663366", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Percent sign not displayed or inconsistent for some cells", "RefUrl": "/notes/1663366 "}, {"RefNumber": "1669587", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "ENTRIES_MAXCOUNT set to zero does not show all values.", "RefUrl": "/notes/1669587 "}, {"RefNumber": "1666960", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modif. docs - Display according to currentness", "RefUrl": "/notes/1666960 "}, {"RefNumber": "1648588", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "ODOC:Date is not exported for Document Items", "RefUrl": "/notes/1648588 "}, {"RefNumber": "1661165", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Module Document Content: row references / empty cells", "RefUrl": "/notes/1661165 "}, {"RefNumber": "1672941", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Javascript during drag & drop after export to Excel or PDF", "RefUrl": "/notes/1672941 "}, {"RefNumber": "1655227", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Modification modules not working as expected during export", "RefUrl": "/notes/1655227 "}, {"RefNumber": "1676554", "RefComponent": "BI-RA-BICS", "RefTitle": "Result set is incorrect after you expand a hierarchy", "RefUrl": "/notes/1676554 "}, {"RefNumber": "1596751", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Saving bookmark functionality results in peculiar behaviour", "RefUrl": "/notes/1596751 "}, {"RefNumber": "1656360", "RefComponent": "BI-RA-BICS", "RefTitle": "Sammelhinweis: Korrekturen im Umfeld Export ResultSet", "RefUrl": "/notes/1656360 "}, {"RefNumber": "1659826", "RefComponent": "BI-RA-BICS", "RefTitle": "Error when you hide a hierarchy level", "RefUrl": "/notes/1659826 "}, {"RefNumber": "1669597", "RefComponent": "BI-RA-BICS", "RefTitle": "Hash sign when using the presentation \"Key (Not Compounded)\"", "RefUrl": "/notes/1669597 "}, {"RefNumber": "1667925", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Result set is invalid because you set a custom parameter", "RefUrl": "/notes/1667925 "}, {"RefNumber": "1665944", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Module Scrolling, Planning & Input sporatic Javascript error", "RefUrl": "/notes/1665944 "}, {"RefNumber": "1662206", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Module Scrolling: Input cells have wrong background color", "RefUrl": "/notes/1662206 "}, {"RefNumber": "1660488", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Export internal mime images to excel", "RefUrl": "/notes/1660488 "}, {"RefNumber": "1659251", "RefComponent": "BI-RA-BICS", "RefTitle": "Not possible to undo sorting with broken hierarchies", "RefUrl": "/notes/1659251 "}, {"RefNumber": "1662235", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Error while exporting a Bookmark to Excel", "RefUrl": "/notes/1662235 "}, {"RefNumber": "1659590", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Session timeout exception results in XML display", "RefUrl": "/notes/1659590 "}, {"RefNumber": "1620645", "RefComponent": "BI-RA-BICS", "RefTitle": "BICS Hana Provider: Reduction of SQL Query Statements", "RefUrl": "/notes/1620645 "}, {"RefNumber": "1598073", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Null pointer exception when executing a query", "RefUrl": "/notes/1598073 "}, {"RefNumber": "1598020", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Performance: selection state binding in Java Web Template", "RefUrl": "/notes/1598020 "}, {"RefNumber": "1614235", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "BEx Web 7.30 Report Item: MIMEs from MIME repository missing", "RefUrl": "/notes/1614235 "}, {"RefNumber": "1617002", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "NullPointerException for rpts w/ref. to HANA Analytical View", "RefUrl": "/notes/1617002 "}, {"RefNumber": "1642143", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Modification Format fails on info objects and structures", "RefUrl": "/notes/1642143 "}, {"RefNumber": "1631584", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "BEx Web 7.30: Template Include Item is exported twice", "RefUrl": "/notes/1631584 "}, {"RefNumber": "1629251", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx: Variant Catalogs are not correct assigned in NW 7.30", "RefUrl": "/notes/1629251 "}, {"RefNumber": "1617936", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Save query view missing template id in backend table.", "RefUrl": "/notes/1617936 "}, {"RefNumber": "1568251", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Sorting is disabled when F4 is called on Structural elements", "RefUrl": "/notes/1568251 "}, {"RefNumber": "1655585", "RefComponent": "BI-RA-BICS", "RefTitle": "Error when collecting the RFC metadata in BICS", "RefUrl": "/notes/1655585 "}, {"RefNumber": "1656069", "RefComponent": "BI-RA-BICS", "RefTitle": "No termination when timeout occurs for work process", "RefUrl": "/notes/1656069 "}, {"RefNumber": "1637571", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification: Export comments", "RefUrl": "/notes/1637571 "}, {"RefNumber": "1667230", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Query views are not deleted", "RefUrl": "/notes/1667230 "}, {"RefNumber": "1666728", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Value \"\" of parameter \"REQUEST_ID\" could not be converted in", "RefUrl": "/notes/1666728 "}, {"RefNumber": "1662669", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: Export of Conditions and Exception Items Fails", "RefUrl": "/notes/1662669 "}, {"RefNumber": "1589123", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Delete link from Show Details-> Action tab window removed", "RefUrl": "/notes/1589123 "}, {"RefNumber": "1587376", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web: Broadcaster in Template Include Item scenario", "RefUrl": "/notes/1587376 "}, {"RefNumber": "1618051", "RefComponent": "BW-BEX-ET-WJR-DIA-OS", "RefTitle": "Empty description in \"Save As\" dialog results in overwrite", "RefUrl": "/notes/1618051 "}, {"RefNumber": "1571693", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification command - Filter on totals", "RefUrl": "/notes/1571693 "}, {"RefNumber": "1660994", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Module Copy&Paste: Class Cast Exception caused by module", "RefUrl": "/notes/1660994 "}, {"RefNumber": "1583928", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Javascript error if analysis item is placed inside container", "RefUrl": "/notes/1583928 "}, {"RefNumber": "1617004", "RefComponent": "BI-RA-BICS", "RefTitle": "Cell locking administration in the backend", "RefUrl": "/notes/1617004 "}, {"RefNumber": "1613879", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "EXPORT_XML - Parameter REDIRECT_ABAP_FUNCTION_MODULE", "RefUrl": "/notes/1613879 "}, {"RefNumber": "1612390", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Java: Scroll<PERSON> position incorrect for G<PERSON> and selector", "RefUrl": "/notes/1612390 "}, {"RefNumber": "1610656", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Enhancement for including bookmark URL while export", "RefUrl": "/notes/1610656 "}, {"RefNumber": "1573030", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Web application dumps when STATELESS is set to non-boolean", "RefUrl": "/notes/1573030 "}, {"RefNumber": "1649202", "RefComponent": "BI-RA-BICS", "RefTitle": "Querying whether a planning function can process deltas", "RefUrl": "/notes/1649202 "}, {"RefNumber": "1598986", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "All hidden item are exported after implementing note 1587492", "RefUrl": "/notes/1598986 "}, {"RefNumber": "1606737", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "ODOC: Export formatting issue with respect to document items", "RefUrl": "/notes/1606737 "}, {"RefNumber": "1654972", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Bookmark dialog is initially loaded blank without any items", "RefUrl": "/notes/1654972 "}, {"RefNumber": "1640307", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web: Issue on Set Item Parameters on Items with Commands", "RefUrl": "/notes/1640307 "}, {"RefNumber": "1653742", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Unexpected drill operation state loss in Java Web Runtime", "RefUrl": "/notes/1653742 "}, {"RefNumber": "1653153", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "No data in result set leads to <PERSON><PERSON><PERSON><PERSON> exception", "RefUrl": "/notes/1653153 "}, {"RefNumber": "1650395", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Navigation Pane Item: Sorting of free characteritics", "RefUrl": "/notes/1650395 "}, {"RefNumber": "1546963", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "BEx Web 7.x: Enhanced Scrolling Module", "RefUrl": "/notes/1546963 "}, {"RefNumber": "1592891", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Filter master data messages shown in Message List Item", "RefUrl": "/notes/1592891 "}, {"RefNumber": "1610036", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Cancelation with sorting button for Attributes", "RefUrl": "/notes/1610036 "}, {"RefNumber": "1569787", "RefComponent": "BW-BEX-ET-WJR-DIA-CO", "RefTitle": "Condition Dialog does not validate interval range correctly", "RefUrl": "/notes/1569787 "}, {"RefNumber": "1567416", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NullPointerException for system message without expiry date.", "RefUrl": "/notes/1567416 "}, {"RefNumber": "1606874", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Hidden container layout item is getting exported", "RefUrl": "/notes/1606874 "}, {"RefNumber": "1621628", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Hidden group item is getting exported", "RefUrl": "/notes/1621628 "}, {"RefNumber": "1643608", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "7.30: Close button displayed in formatted dialog window", "RefUrl": "/notes/1643608 "}, {"RefNumber": "1642017", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Xcelcius: blank values in filter lead to empty result", "RefUrl": "/notes/1642017 "}, {"RefNumber": "1637675", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Date appears as ####### after export to excel", "RefUrl": "/notes/1637675 "}, {"RefNumber": "1591534", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "SET_DATA_CELL_PROPERTIES command on structure node incorrect", "RefUrl": "/notes/1591534 "}, {"RefNumber": "1655000", "RefComponent": "BI-RA-BICS", "RefTitle": "ResultSetMx returns an incorrect (old) data cell", "RefUrl": "/notes/1655000 "}, {"RefNumber": "1656517", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "RSBOLAP018 error while saving or executing a template in WAD", "RefUrl": "/notes/1656517 "}, {"RefNumber": "1646366", "RefComponent": "BI-RA-BICS", "RefTitle": "ClassCastException when you use \"Low speed connection\"", "RefUrl": "/notes/1646366 "}, {"RefNumber": "1645842", "RefComponent": "BI-RA-BICS", "RefTitle": "Error when excluding hier. node in characteristic context", "RefUrl": "/notes/1645842 "}, {"RefNumber": "1651968", "RefComponent": "BW-BEX-ET-WJR-BOE", "RefTitle": "Bi Web Applications in BI 4.0: Logon with Windows AD", "RefUrl": "/notes/1651968 "}, {"RefNumber": "1607342", "RefComponent": "BW-BEX-ET-WJR-DIA", "RefTitle": "Loading animation disappears shortly submit variable screen", "RefUrl": "/notes/1607342 "}, {"RefNumber": "1607636", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web: Language for Web Applicatins opened in new window", "RefUrl": "/notes/1607636 "}, {"RefNumber": "1605911", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Cells are merged by default in the broadcasted excel", "RefUrl": "/notes/1605911 "}, {"RefNumber": "1602447", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification termination when you merge columns", "RefUrl": "/notes/1602447 "}, {"RefNumber": "1601289", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Expanding of Universal Hierarchy not working in Java Runtime", "RefUrl": "/notes/1601289 "}, {"RefNumber": "1601080", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification: Column headers / No data", "RefUrl": "/notes/1601080 "}, {"RefNumber": "1599624", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "Dashboard does not show any value if it contains only KFs", "RefUrl": "/notes/1599624 "}, {"RefNumber": "1598498", "RefComponent": "BI-RA-BICS", "RefTitle": "Cells are not ready for input after you collapse a structure", "RefUrl": "/notes/1598498 "}, {"RefNumber": "1603113", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "730 JAVA BI: My portfolio is missing in Save dialog", "RefUrl": "/notes/1603113 "}, {"RefNumber": "1593072", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx web on IE9: JavaScripts error while calling popup dialog", "RefUrl": "/notes/1593072 "}, {"RefNumber": "1589860", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Performance: multiple variable values in WJR", "RefUrl": "/notes/1589860 "}, {"RefNumber": "1562554", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Drag and Drop: incorrect behaviour on structural elements", "RefUrl": "/notes/1562554 "}, {"RefNumber": "1577649", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Query with 2 structures and \"Hide\" list calculation", "RefUrl": "/notes/1577649 "}, {"RefNumber": "1623428", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Export to PDF hangs for info field item.", "RefUrl": "/notes/1623428 "}, {"RefNumber": "1640137", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Sorting results are not correct", "RefUrl": "/notes/1640137 "}, {"RefNumber": "1639592", "RefComponent": "BW-BEX-ET-WJR-GRAPH", "RefTitle": "Size of the chart is increased when no data available", "RefUrl": "/notes/1639592 "}, {"RefNumber": "1644772", "RefComponent": "BI-RA-BICS", "RefTitle": "Cross-classified table sorting incorr. after deserialization", "RefUrl": "/notes/1644772 "}, {"RefNumber": "1634350", "RefComponent": "BW-BEX-ET-WJR-EP", "RefTitle": "RSWR_SYSTEM_ALIAS_CHECK cause exception on BI Java", "RefUrl": "/notes/1634350 "}, {"RefNumber": "1634117", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Display of null attributes in HANA", "RefUrl": "/notes/1634117 "}, {"RefNumber": "1636106", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Missing include/exclude button in selector dialog in 730", "RefUrl": "/notes/1636106 "}, {"RefNumber": "1637197", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS:Query displays key instead of empty text for attributes", "RefUrl": "/notes/1637197 "}, {"RefNumber": "1627409", "RefComponent": "BI-RA-BICS", "RefTitle": "Exception when resetting an expanded hierarchy", "RefUrl": "/notes/1627409 "}, {"RefNumber": "1546971", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Modification Modules RIG - Restore focus planning input cell", "RefUrl": "/notes/1546971 "}, {"RefNumber": "1581923", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: Optimization on Back with Many Data Providers", "RefUrl": "/notes/1581923 "}, {"RefNumber": "1622475", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Performance: minor improvement in member access", "RefUrl": "/notes/1622475 "}, {"RefNumber": "1622596", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Items are not correctly exported to Excel in 730", "RefUrl": "/notes/1622596 "}, {"RefNumber": "1617533", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Drilling with errors in combination with zero suppression", "RefUrl": "/notes/1617533 "}, {"RefNumber": "1610963", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Modification Column Width incorrect for universal hierarchy", "RefUrl": "/notes/1610963 "}, {"RefNumber": "1607637", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: Spinning Wheel disappears too early in AJAX mode", "RefUrl": "/notes/1607637 "}, {"RefNumber": "1620394", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: Corrections in Unified Rendering Libraries", "RefUrl": "/notes/1620394 "}, {"RefNumber": "1617840", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "SAP NW BI JAVA 7.30/BOE 4.0, context menu, row/column lock", "RefUrl": "/notes/1617840 "}, {"RefNumber": "1614013", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "OSGi Activation for Visual Composer (BI-KIT) removed", "RefUrl": "/notes/1614013 "}, {"RefNumber": "1610325", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Termination in unrestricted overall result list calculation", "RefUrl": "/notes/1610325 "}, {"RefNumber": "1607635", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web: Memory Leak on Export Scenarios (XLS, PDF, ...)", "RefUrl": "/notes/1607635 "}, {"RefNumber": "1617318", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Invalid snippets on web template after doing export", "RefUrl": "/notes/1617318 "}, {"RefNumber": "1613090", "RefComponent": "BI-RA-BICS", "RefTitle": "Incorrect values when you collapse a hierarchical structure", "RefUrl": "/notes/1613090 "}, {"RefNumber": "1619455", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Select All option missing in the selector dialog", "RefUrl": "/notes/1619455 "}, {"RefNumber": "1612599", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "Error #2032 executing dashboards after node restart", "RefUrl": "/notes/1612599 "}, {"RefNumber": "1618536", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Repeat column header does not work in Broadcast to PDF.", "RefUrl": "/notes/1618536 "}, {"RefNumber": "1507065", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Adding BI Bookmarks to browser favorites in CRM system", "RefUrl": "/notes/1507065 "}, {"RefNumber": "1622746", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Standard images are exported to excel in 730", "RefUrl": "/notes/1622746 "}, {"RefNumber": "1558368", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.30: Corrections in Support Desktool", "RefUrl": "/notes/1558368 "}, {"RefNumber": "1554104", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Close button on Document browser when you click details", "RefUrl": "/notes/1554104 "}, {"RefNumber": "1532017", "RefComponent": "BW-BEX-ET-WJR-EP", "RefTitle": "Order of login modules for the ticket component in the VA", "RefUrl": "/notes/1532017 "}, {"RefNumber": "1556012", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Visual Composer: \"Problem moving to next record\"", "RefUrl": "/notes/1556012 "}, {"RefNumber": "1527393", "RefComponent": "BW-BEX-ET-WJR-EP", "RefTitle": "java.lang.nullpointerexception in support desk tool", "RefUrl": "/notes/1527393 "}, {"RefNumber": "1549559", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: BICS & KM integration: NullPointer in List.clear()", "RefUrl": "/notes/1549559 "}, {"RefNumber": "1565207", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Drag and drop in second row inserts new line in first row", "RefUrl": "/notes/1565207 "}, {"RefNumber": "1608398", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Default width of items with active tray has changed with 730", "RefUrl": "/notes/1608398 "}, {"RefNumber": "1601504", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web >= 7.30: Broadcaster exports incorrectly containers", "RefUrl": "/notes/1601504 "}, {"RefNumber": "1581167", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Totals row missing after drill op.on hier.key figure struct.", "RefUrl": "/notes/1581167 "}, {"RefNumber": "1585680", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Korrekturen RIG Modification Module", "RefUrl": "/notes/1585680 "}, {"RefNumber": "1547871", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.30: Corrections in Support Desktool", "RefUrl": "/notes/1547871 "}, {"RefNumber": "1597173", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Backward navigation selectorData<PERSON>rov<PERSON> is already assigned", "RefUrl": "/notes/1597173 "}, {"RefNumber": "1592848", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "SAP NW BI JAVA 7.x / BOE 4.0 Javascript error in IE9", "RefUrl": "/notes/1592848 "}, {"RefNumber": "1597308", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Incorrect variants displayed when you execute a query", "RefUrl": "/notes/1597308 "}, {"RefNumber": "1538793", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Sorting on local formulas not saved after bookmark or undo", "RefUrl": "/notes/1538793 "}, {"RefNumber": "1600287", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "LIMIT EXCEED error during BW connection from BOE 4.0", "RefUrl": "/notes/1600287 "}, {"RefNumber": "1592991", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Filter Pane Item overlaps in firefox and Safari", "RefUrl": "/notes/1592991 "}, {"RefNumber": "1571666", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "SAP BI ExportLib: Clipping, linebreaks, accessibility", "RefUrl": "/notes/1571666 "}, {"RefNumber": "1583942", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "THJ: Validity attribute not shown for text nodes in Java Web", "RefUrl": "/notes/1583942 "}, {"RefNumber": "1575297", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Member access issue for dependent variables (compounded characteristics)", "RefUrl": "/notes/1575297 "}, {"RefNumber": "1581592", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Planning with time entry is incorrect", "RefUrl": "/notes/1581592 "}, {"RefNumber": "1585688", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Application hangs when exporting single document item", "RefUrl": "/notes/1585688 "}, {"RefNumber": "1591929", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Java Rutime: Export to excel - Text format for key figures", "RefUrl": "/notes/1591929 "}, {"RefNumber": "1592366", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Variable values are lost when bookmark is loaded", "RefUrl": "/notes/1592366 "}, {"RefNumber": "1564517", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Invalid member selection on info object with conversion exit", "RefUrl": "/notes/1564517 "}, {"RefNumber": "1461256", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Export to Excel: cell format is TEXT instead of CUSTOM", "RefUrl": "/notes/1461256 "}, {"RefNumber": "1526708", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "The computation of bounds is not correct for Dashboard", "RefUrl": "/notes/1526708 "}, {"RefNumber": "1527184", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Termination during export to PDF or Excel with mod modules", "RefUrl": "/notes/1527184 "}, {"RefNumber": "1563643", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Modification Modules RIG - Javascript error during export", "RefUrl": "/notes/1563643 "}, {"RefNumber": "1534083", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Nullpointer when time-dependent hierarchies are loaded", "RefUrl": "/notes/1534083 "}, {"RefNumber": "1579224", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "DuplicateMemberException when you undo a step", "RefUrl": "/notes/1579224 "}, {"RefNumber": "1572776", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: USE_SNIPPETS & loading bookmarks with new items", "RefUrl": "/notes/1572776 "}, {"RefNumber": "1578941", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Java Runtime: issue with pattern selection in ODOC scenario", "RefUrl": "/notes/1578941 "}, {"RefNumber": "1578854", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "NullPointerException during error ticket generation.", "RefUrl": "/notes/1578854 "}, {"RefNumber": "1577293", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Java dump: Hierarchical structure and structure axis", "RefUrl": "/notes/1577293 "}, {"RefNumber": "1526210", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Export of external MIMES (Intranet/Internet)", "RefUrl": "/notes/1526210 "}, {"RefNumber": "1558113", "RefComponent": "BW-BEX-ET-WJR-DIA", "RefTitle": "You cannot expand hierarchies in filter selection", "RefUrl": "/notes/1558113 "}, {"RefNumber": "1555154", "RefComponent": "BI-RA-BICS", "RefTitle": "BEx Web 7.30: <PERSON><PERSON> neuen Zeilen bei Planungsquery", "RefUrl": "/notes/1555154 "}, {"RefNumber": "1562205", "RefComponent": "BW-BEX-ET-WJR-GRAPH", "RefTitle": "Map Item not exported to Excel", "RefUrl": "/notes/1562205 "}, {"RefNumber": "1565469", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Error when planning DATE key figures", "RefUrl": "/notes/1565469 "}, {"RefNumber": "1565796", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Sorting is disabled when F4 is called on Structural elements", "RefUrl": "/notes/1565796 "}, {"RefNumber": "1559555", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Java Web Selector dialog has text \"Enter a value for\" twice", "RefUrl": "/notes/1559555 "}, {"RefNumber": "1566055", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "The HTTP Session TimeOut message is not proper in Dashboard", "RefUrl": "/notes/1566055 "}, {"RefNumber": "1571707", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "SAP NetWeaver BI JAVA 7.30 / BOE 4.0 items are not exported", "RefUrl": "/notes/1571707 "}, {"RefNumber": "1540416", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Javadoc adjusted in IRsAxisTuple.getAxisHierarchyInfo()", "RefUrl": "/notes/1540416 "}, {"RefNumber": "1437358", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "NullPointerException due to incorrect template definition", "RefUrl": "/notes/1437358 "}, {"RefNumber": "1533296", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Module Copy-Paste: Values pasted at incorrect cell", "RefUrl": "/notes/1533296 "}, {"RefNumber": "1543561", "RefComponent": "BW-BEX-ET-WJR-DIA-OS", "RefTitle": "Display settings are incorrectly set in Open dialog", "RefUrl": "/notes/1543561 "}, {"RefNumber": "1539790", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Exception during export of info field item.", "RefUrl": "/notes/1539790 "}, {"RefNumber": "1425720", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification: User-defined symbols for exception II", "RefUrl": "/notes/1425720 "}, {"RefNumber": "1522108", "RefComponent": "BW-BEX-ET-WJR-DIA-VA", "RefTitle": "Parcer error for hierarchy value with special characters", "RefUrl": "/notes/1522108 "}, {"RefNumber": "1538753", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "SAP NW BI JAVA 7.x - comments option in context menu", "RefUrl": "/notes/1538753 "}, {"RefNumber": "1538640", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "BiNamespace characteristics not displyed in Assignment colum", "RefUrl": "/notes/1538640 "}, {"RefNumber": "1538289", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "'X' appears in result cells (Calculate Result Set as \"Hide\")", "RefUrl": "/notes/1538289 "}, {"RefNumber": "1527330", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Display presentation for Current user in Info field item", "RefUrl": "/notes/1527330 "}, {"RefNumber": "1536756", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Filter value lost in BACK_TO_PREVIOUS_STATE for DPs >= 2", "RefUrl": "/notes/1536756 "}, {"RefNumber": "1536499", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Safari Browser: Adding bookmark to browser favorites", "RefUrl": "/notes/1536499 "}, {"RefNumber": "1536168", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Export huge text fails for single document item", "RefUrl": "/notes/1536168 "}, {"RefNumber": "1505289", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Display line breaks or multiple spaces on BW Text Item", "RefUrl": "/notes/1505289 "}, {"RefNumber": "1546051", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Planning related selection or variable has missing or invalid entries", "RefUrl": "/notes/1546051 "}, {"RefNumber": "1552647", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Scaling factor reset to default while changing formula", "RefUrl": "/notes/1552647 "}, {"RefNumber": "1561738", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Wrong dropdown box when drag and drop between filter panes", "RefUrl": "/notes/1561738 "}, {"RefNumber": "1561086", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Possible dump by loading a bookmark with an object variable", "RefUrl": "/notes/1561086 "}, {"RefNumber": "1560665", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Modification RIG Document Content performance enhancement", "RefUrl": "/notes/1560665 "}, {"RefNumber": "1557925", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Export to Excel shows a % sign in calculated key figures", "RefUrl": "/notes/1557925 "}, {"RefNumber": "1556545", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Case conversion for filter values on compounded char", "RefUrl": "/notes/1556545 "}, {"RefNumber": "1561846", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Wildcard entry for SELECTION OPTION variable in selector", "RefUrl": "/notes/1561846 "}, {"RefNumber": "1534863", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Tooltip for hierarchies in characteristic properties pane", "RefUrl": "/notes/1534863 "}, {"RefNumber": "1534100", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Unexpected filter is applied if key contains special chars", "RefUrl": "/notes/1534100 "}, {"RefNumber": "1532816", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Local formula is not deserialzed correctly (bookmark, undo)", "RefUrl": "/notes/1532816 "}, {"RefNumber": "1532285", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "ArrayIndexOutOfBoundsException for dashboard execution.", "RefUrl": "/notes/1532285 "}, {"RefNumber": "1528687", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Selection screen range value is validated incorrectly", "RefUrl": "/notes/1528687 "}, {"RefNumber": "1525392", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Planning function: error messages not reset (F4 selector)", "RefUrl": "/notes/1525392 "}, {"RefNumber": "1525226", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Personalization flag in web Java runtime", "RefUrl": "/notes/1525226 "}, {"RefNumber": "1524799", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Selection screen not shown with SET_VARIABLES_STATE command", "RefUrl": "/notes/1524799 "}, {"RefNumber": "1524296", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "List calculation: Normalize acc next result without subtotal", "RefUrl": "/notes/1524296 "}, {"RefNumber": "1524022", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Hierarchy Node with huge number in selector dialog", "RefUrl": "/notes/1524022 "}, {"RefNumber": "1523974", "RefComponent": "BW-BEX-ET-WJR-ITM-FL", "RefTitle": "java.lang.ClassCastException in ListBox item", "RefUrl": "/notes/1523974 "}, {"RefNumber": "1523854", "RefComponent": "BW-BEX-ET-WJR-ITM-FL", "RefTitle": "Personalization leads to exception with drop down item", "RefUrl": "/notes/1523854 "}, {"RefNumber": "1523581", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "ODOC : Close button displayed in formatted dialog window.", "RefUrl": "/notes/1523581 "}, {"RefNumber": "1507068", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Negative value in Calendar tool is incorrectly processed", "RefUrl": "/notes/1507068 "}, {"RefNumber": "1535651", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Object variables are not restored correctly from a bookmark", "RefUrl": "/notes/1535651 "}, {"RefNumber": "1489306", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Accessibility: Labels for input fields are read out properly", "RefUrl": "/notes/1489306 "}, {"RefNumber": "1561909", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Command SET_ITEM_PARAMETERS with snippets rendering", "RefUrl": "/notes/1561909 "}, {"RefNumber": "1555612", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Wrong result set size calculation during drill operations", "RefUrl": "/notes/1555612 "}, {"RefNumber": "1451171", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Path entries are displayed twice", "RefUrl": "/notes/1451171 "}, {"RefNumber": "1552272", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Modal template dialog can not be closed", "RefUrl": "/notes/1552272 "}, {"RefNumber": "1531740", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification comments", "RefUrl": "/notes/1531740 "}, {"RefNumber": "1548255", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Calculate Based On Results Of Former Details Calculations is", "RefUrl": "/notes/1548255 "}, {"RefNumber": "1548840", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Condition state toggling: usability enhancement", "RefUrl": "/notes/1548840 "}, {"RefNumber": "1537922", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "context menu with reference to selector dataprovider fails", "RefUrl": "/notes/1537922 "}, {"RefNumber": "1174124", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Condition not used with active axis hierarchy", "RefUrl": "/notes/1174124 "}, {"RefNumber": "1546012", "RefComponent": "BW-BEX-ET-WJR-DIA-FM", "RefTitle": "Java ClassCast exception in query (writeStateIntoDom)", "RefUrl": "/notes/1546012 "}, {"RefNumber": "1545679", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Result with hierarchy symbol", "RefUrl": "/notes/1545679 "}, {"RefNumber": "1545545", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "ODOC: Broadcasting to MHTML displays all columns in doc item", "RefUrl": "/notes/1545545 "}, {"RefNumber": "1548704", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "RRI:open mode REPLACE_WEB_APPLICATION opens a new window", "RefUrl": "/notes/1548704 "}, {"RefNumber": "1536476", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Web template execution recording incorrect object name", "RefUrl": "/notes/1536476 "}, {"RefNumber": "1538014", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Number of decimal resets to default while changing formula", "RefUrl": "/notes/1538014 "}, {"RefNumber": "1534743", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "SAP NW BI JAVA 7.30 / BOE 4.0 Excel export freeze web app", "RefUrl": "/notes/1534743 "}, {"RefNumber": "1531694", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Adding Bookmark to Browser Favorites in FPN brings wrong URL", "RefUrl": "/notes/1531694 "}, {"RefNumber": "1163789", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NetWeaver 7.0: BI Java Synchronized Patch Delivery Strategy", "RefUrl": "/notes/1163789 "}, {"RefNumber": "1537039", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "RTL(Right-to-Left) display not supported for PDF,EXCEL & CSV", "RefUrl": "/notes/1537039 "}, {"RefNumber": "1544415", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Module CopyPaste:pasting more cells than the space available", "RefUrl": "/notes/1544415 "}, {"RefNumber": "1527186", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Export to Excel: \"0.0%\" value converted to \"0,0\"", "RefUrl": "/notes/1527186 "}, {"RefNumber": "1526485", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "SAP NW BI JAVA 7.30 KM item dialog boxes do not work", "RefUrl": "/notes/1526485 "}, {"RefNumber": "1177028", "RefComponent": "BW-BEX-ET-WJR-ITM-FL", "RefTitle": "BEx Web NW7.30/BOE4.0: Filter Pane Item: Text Wrapping", "RefUrl": "/notes/1177028 "}, {"RefNumber": "1526880", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "Reverse proxy Config : Redirection to wrong url(Error #2048)", "RefUrl": "/notes/1526880 "}, {"RefNumber": "1013369", "RefComponent": "BW", "RefTitle": "SAP NetWeaver 7.0 BI - intermediate Support Packages", "RefUrl": "/notes/1013369 "}, {"RefNumber": "1055581", "RefComponent": "BW", "RefTitle": "Recommendations for Support Package Stacks for BI 7.0", "RefUrl": "/notes/1055581 "}, {"RefNumber": "1327345", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Patches for NetWeaver 7.01  BI Java Support Package", "RefUrl": "/notes/1327345 "}, {"RefNumber": "1011241", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Patches for NetWeaver 2004s BI Java Support Package", "RefUrl": "/notes/1011241 "}, {"RefNumber": "1072576", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Frequently Asked Questions: BI Java Support Packages/patches", "RefUrl": "/notes/1072576 "}, {"RefNumber": "971631", "RefComponent": "BW", "RefTitle": "Info: Vorlagehinweise und SAPBINEWS", "RefUrl": "/notes/971631 "}, {"RefNumber": "656711", "RefComponent": "BC-CTS-SDM", "RefTitle": "Deploying Java Support Packages with SDM", "RefUrl": "/notes/656711 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BI-BASE-S", "From": "7.00", "To": "7.02", "Subsequent": ""}, {"SoftwareComponent": "BI-BASE-S", "From": "7.30", "To": "7.30", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "711", "To": "711", "Subsequent": ""}, {"SoftwareComponent": "HM-BI-BASE-S", "From": "7.00", "To": "7.02", "Subsequent": ""}, {"SoftwareComponent": "HM-BIWEBAPP", "From": "7.00", "To": "7.02", "Subsequent": ""}, {"SoftwareComponent": "BIWEBAPP", "From": "7.00", "To": "7.02", "Subsequent": ""}, {"SoftwareComponent": "BIWEBAPP", "From": "7.30", "To": "7.30", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}