{"Request": {"Number": "939971", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 414, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016088962017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000939971?language=E&token=CAD6A305464778E60DDFBD62A65B04F9"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000939971", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "939971"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.05.2010"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX"}, "SAPComponentKeyText": {"_label": "Component", "value": "Flexible Real Estate Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "939971 - FAQ and consulting notes for RE-FX"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note compiles all consulting notes and FAQ notes for RE-FX.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Consulting, RE-FX<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>For more information, see related notes.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D002072)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000939971/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000939971/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000939971/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000939971/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000939971/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000939971/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000939971/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000939971/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000939971/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "977295", "RefComponent": "RE-BD", "RefTitle": "Migration LUM von RE-Classic nach RE-FX", "RefUrl": "/notes/977295"}, {"RefNumber": "952495", "RefComponent": "RE-FX", "RefTitle": "RE-FX: Sales/purchases tax change: Steps to take,", "RefUrl": "/notes/952495"}, {"RefNumber": "942335", "RefComponent": "RE-FX", "RefTitle": "Composite SAP Note about performance and memory consumption", "RefUrl": "/notes/942335"}, {"RefNumber": "939974", "RefComponent": "RE-FX-CN", "RefTitle": "FAQ: Contract, contract offer, conditions, cash flow", "RefUrl": "/notes/939974"}, {"RefNumber": "939972", "RefComponent": "RE-FX-RA", "RefTitle": "FAQ: Accounting", "RefUrl": "/notes/939972"}, {"RefNumber": "937084", "RefComponent": "RE-FX", "RefTitle": "Enhancing a search help without modification in RE-FX", "RefUrl": "/notes/937084"}, {"RefNumber": "924831", "RefComponent": "RE-FX-SC", "RefTitle": "FAQ: Accounts, SCS, service charge settlement, COA, condominium owners association, sales", "RefUrl": "/notes/924831"}, {"RefNumber": "828160", "RefComponent": "RE-FX", "RefTitle": "Migration from Classic RE to RE-FX", "RefUrl": "/notes/828160"}, {"RefNumber": "690900", "RefComponent": "RE-FX-BD", "RefTitle": "User-defined real estate master data fields as of 470x200", "RefUrl": "/notes/690900"}, {"RefNumber": "670310", "RefComponent": "RE-FX", "RefTitle": "Generating BDT subscreen containers", "RefUrl": "/notes/670310"}, {"RefNumber": "443311", "RefComponent": "RE", "RefTitle": "Enterprise extension SAP Real Estate: Technical basis", "RefUrl": "/notes/443311"}, {"RefNumber": "1820898", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1820898"}, {"RefNumber": "1666869", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1666869"}, {"RefNumber": "1663018", "RefComponent": "RE-FX", "RefTitle": "Composite SAP Note for Customer Connection Real Estate 2011/2012", "RefUrl": "/notes/1663018"}, {"RefNumber": "1654760", "RefComponent": "RE-FX-BD", "RefTitle": "Enhancing search helps: Example of validity, deletion flag", "RefUrl": "/notes/1654760"}, {"RefNumber": "1646595", "RefComponent": "RE-FX", "RefTitle": "RE_AUDIT: Measurement for Real Estate Management under ERP", "RefUrl": "/notes/1646595"}, {"RefNumber": "1645549", "RefComponent": "RE-FX-CP", "RefTitle": "FAQ: Correspondence", "RefUrl": "/notes/1645549"}, {"RefNumber": "1642677", "RefComponent": "RE-FX", "RefTitle": "Useful control parameters in RE-FX", "RefUrl": "/notes/1642677"}, {"RefNumber": "1629693", "RefComponent": "RE-FX", "RefTitle": "Influencing expiration date of RE-FX logs", "RefUrl": "/notes/1629693"}, {"RefNumber": "1619186", "RefComponent": "RE-FX-BD", "RefTitle": "FAQ: Master data", "RefUrl": "/notes/1619186"}, {"RefNumber": "1619185", "RefComponent": "RE-FX-RA", "RefTitle": "FAQ: Accrual/deferral (contract)", "RefUrl": "/notes/1619185"}, {"RefNumber": "1618971", "RefComponent": "RE-FX-BP", "RefTitle": "FAQ: Partner", "RefUrl": "/notes/1618971"}, {"RefNumber": "1618970", "RefComponent": "RE-FX-IS", "RefTitle": "FAQ: Information system", "RefUrl": "/notes/1618970"}, {"RefNumber": "1616596", "RefComponent": "RE-FX-CO", "RefTitle": "FAQ: Real Estate - Controlling", "RefUrl": "/notes/1616596"}, {"RefNumber": "1610085", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1610085"}, {"RefNumber": "1574699", "RefComponent": "RE-FX-CN", "RefTitle": "FAQ: Vacancy, notice, renewal", "RefUrl": "/notes/1574699"}, {"RefNumber": "1536179", "RefComponent": "RE-FX-RS", "RefTitle": "FAQ: Room reservation and seating arrangements", "RefUrl": "/notes/1536179"}, {"RefNumber": "1526490", "RefComponent": "RE-FX", "RefTitle": "FAQ: Using BAdIs in RE-FX", "RefUrl": "/notes/1526490"}, {"RefNumber": "1515265", "RefComponent": "RE-BD", "RefTitle": "Composite SAP note: Security of Real Estate Mgmt (RE/RE-FX)", "RefUrl": "/notes/1515265"}, {"RefNumber": "151354", "RefComponent": "BC-DWB", "RefTitle": "Editor lock in SAP Includes", "RefUrl": "/notes/151354"}, {"RefNumber": "1500322", "RefComponent": "RE-FX-BD", "RefTitle": "RE80: Layouts in navigation tree", "RefUrl": "/notes/1500322"}, {"RefNumber": "1458917", "RefComponent": "RE-FX", "RefTitle": "Composite SAP Note for archiving", "RefUrl": "/notes/1458917"}, {"RefNumber": "1448738", "RefComponent": "RE-FX-AJ", "RefTitle": "FAQ: Rent adjustment", "RefUrl": "/notes/1448738"}, {"RefNumber": "1375639", "RefComponent": "BW-BCT-RE", "RefTitle": "Delta compatibility of RE extractors", "RefUrl": "/notes/1375639"}, {"RefNumber": "1239294", "RefComponent": "RE-FX", "RefTitle": "Enhance collective search help for object numbers", "RefUrl": "/notes/1239294"}, {"RefNumber": "1151136", "RefComponent": "RE-FX", "RefTitle": "FAQ: Using BAPIs in RE-FX", "RefUrl": "/notes/1151136"}, {"RefNumber": "1149709", "RefComponent": "RE-FX-BD", "RefTitle": "Consulting note: Address inheritance", "RefUrl": "/notes/1149709"}, {"RefNumber": "1135001", "RefComponent": "RE-FX-RA", "RefTitle": "FICA, PSCD: Sammelhinweis", "RefUrl": "/notes/1135001"}, {"RefNumber": "1057734", "RefComponent": "RE-FX-MM", "RefTitle": "FAQ: COA management", "RefUrl": "/notes/1057734"}, {"RefNumber": "1013725", "RefComponent": "RE-FX-IT", "RefTitle": "FAQ: Input tax distribution, input tax corr., option rates", "RefUrl": "/notes/1013725"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "832868", "RefComponent": "RE-FX-BD", "RefTitle": "Real Estate objects and assets", "RefUrl": "/notes/832868 "}, {"RefNumber": "690900", "RefComponent": "RE-FX-BD", "RefTitle": "User-defined real estate master data fields as of 470x200", "RefUrl": "/notes/690900 "}, {"RefNumber": "1820898", "RefComponent": "RE-FX", "RefTitle": "Sammelhinweis: HANA und RE-FX", "RefUrl": "/notes/1820898 "}, {"RefNumber": "939972", "RefComponent": "RE-FX-RA", "RefTitle": "FAQ: Accounting", "RefUrl": "/notes/939972 "}, {"RefNumber": "952495", "RefComponent": "RE-FX", "RefTitle": "RE-FX: Sales/purchases tax change: Steps to take,", "RefUrl": "/notes/952495 "}, {"RefNumber": "1057734", "RefComponent": "RE-FX-MM", "RefTitle": "FAQ: COA management", "RefUrl": "/notes/1057734 "}, {"RefNumber": "1642677", "RefComponent": "RE-FX", "RefTitle": "Useful control parameters in RE-FX", "RefUrl": "/notes/1642677 "}, {"RefNumber": "942335", "RefComponent": "RE-FX", "RefTitle": "Composite SAP Note about performance and memory consumption", "RefUrl": "/notes/942335 "}, {"RefNumber": "1646595", "RefComponent": "RE-FX", "RefTitle": "RE_AUDIT: Measurement for Real Estate Management under ERP", "RefUrl": "/notes/1646595 "}, {"RefNumber": "1448738", "RefComponent": "RE-FX-AJ", "RefTitle": "FAQ: Rent adjustment", "RefUrl": "/notes/1448738 "}, {"RefNumber": "1536179", "RefComponent": "RE-FX-RS", "RefTitle": "FAQ: Room reservation and seating arrangements", "RefUrl": "/notes/1536179 "}, {"RefNumber": "1666869", "RefComponent": "RE-FX", "RefTitle": "Migrating data from legacy system to SAP system", "RefUrl": "/notes/1666869 "}, {"RefNumber": "1663018", "RefComponent": "RE-FX", "RefTitle": "Composite SAP Note for Customer Connection Real Estate 2011/2012", "RefUrl": "/notes/1663018 "}, {"RefNumber": "1458917", "RefComponent": "RE-FX", "RefTitle": "Composite SAP Note for archiving", "RefUrl": "/notes/1458917 "}, {"RefNumber": "1013725", "RefComponent": "RE-FX-IT", "RefTitle": "FAQ: Input tax distribution, input tax corr., option rates", "RefUrl": "/notes/1013725 "}, {"RefNumber": "1618970", "RefComponent": "RE-FX-IS", "RefTitle": "FAQ: Information system", "RefUrl": "/notes/1618970 "}, {"RefNumber": "1645549", "RefComponent": "RE-FX-CP", "RefTitle": "FAQ: Correspondence", "RefUrl": "/notes/1645549 "}, {"RefNumber": "1654760", "RefComponent": "RE-FX-BD", "RefTitle": "Enhancing search helps: Example of validity, deletion flag", "RefUrl": "/notes/1654760 "}, {"RefNumber": "1629693", "RefComponent": "RE-FX", "RefTitle": "Influencing expiration date of RE-FX logs", "RefUrl": "/notes/1629693 "}, {"RefNumber": "1616596", "RefComponent": "RE-FX-CO", "RefTitle": "FAQ: Real Estate - Controlling", "RefUrl": "/notes/1616596 "}, {"RefNumber": "1574699", "RefComponent": "RE-FX-CN", "RefTitle": "FAQ: Vacancy, notice, renewal", "RefUrl": "/notes/1574699 "}, {"RefNumber": "939974", "RefComponent": "RE-FX-CN", "RefTitle": "FAQ: Contract, contract offer, conditions, cash flow", "RefUrl": "/notes/939974 "}, {"RefNumber": "1618971", "RefComponent": "RE-FX-BP", "RefTitle": "FAQ: Partner", "RefUrl": "/notes/1618971 "}, {"RefNumber": "1619186", "RefComponent": "RE-FX-BD", "RefTitle": "FAQ: Master data", "RefUrl": "/notes/1619186 "}, {"RefNumber": "828160", "RefComponent": "RE-FX", "RefTitle": "Migration from Classic RE to RE-FX", "RefUrl": "/notes/828160 "}, {"RefNumber": "1526490", "RefComponent": "RE-FX", "RefTitle": "FAQ: Using BAdIs in RE-FX", "RefUrl": "/notes/1526490 "}, {"RefNumber": "1151136", "RefComponent": "RE-FX", "RefTitle": "FAQ: Using BAPIs in RE-FX", "RefUrl": "/notes/1151136 "}, {"RefNumber": "1239294", "RefComponent": "RE-FX", "RefTitle": "Enhance collective search help for object numbers", "RefUrl": "/notes/1239294 "}, {"RefNumber": "937084", "RefComponent": "RE-FX", "RefTitle": "Enhancing a search help without modification in RE-FX", "RefUrl": "/notes/937084 "}, {"RefNumber": "924831", "RefComponent": "RE-FX-SC", "RefTitle": "FAQ: Accounts, SCS, service charge settlement, COA, condominium owners association, sales", "RefUrl": "/notes/924831 "}, {"RefNumber": "1619185", "RefComponent": "RE-FX-RA", "RefTitle": "FAQ: Accrual/deferral (contract)", "RefUrl": "/notes/1619185 "}, {"RefNumber": "1500322", "RefComponent": "RE-FX-BD", "RefTitle": "RE80: Layouts in navigation tree", "RefUrl": "/notes/1500322 "}, {"RefNumber": "443311", "RefComponent": "RE", "RefTitle": "Enterprise extension SAP Real Estate: Technical basis", "RefUrl": "/notes/443311 "}, {"RefNumber": "1149709", "RefComponent": "RE-FX-BD", "RefTitle": "Consulting note: Address inheritance", "RefUrl": "/notes/1149709 "}, {"RefNumber": "670310", "RefComponent": "RE-FX", "RefTitle": "Generating BDT subscreen containers", "RefUrl": "/notes/670310 "}, {"RefNumber": "1515265", "RefComponent": "RE-BD", "RefTitle": "Composite SAP note: Security of Real Estate Mgmt (RE/RE-FX)", "RefUrl": "/notes/1515265 "}, {"RefNumber": "1135001", "RefComponent": "RE-FX-RA", "RefTitle": "FICA, PSCD: Sammelhinweis", "RefUrl": "/notes/1135001 "}, {"RefNumber": "1375639", "RefComponent": "BW-BCT-RE", "RefTitle": "Delta compatibility of RE extractors", "RefUrl": "/notes/1375639 "}, {"RefNumber": "977295", "RefComponent": "RE-BD", "RefTitle": "Migration LUM von RE-Classic nach RE-FX", "RefUrl": "/notes/977295 "}, {"RefNumber": "151354", "RefComponent": "BC-DWB", "RefTitle": "Editor lock in SAP Includes", "RefUrl": "/notes/151354 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}