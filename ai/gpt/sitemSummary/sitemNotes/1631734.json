{"Request": {"Number": "1631734", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 278, "Error": {}, "SAPNote": {"_type": "00200720410000000235", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001631734?language=E&token=C842AEC11BF4A8AA7B8334849569EB23"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001631734", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001631734/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1631734"}, "Type": {"_label": "Type", "value": "SAP Knowledge Base Article"}, "Version": {"_label": "Version", "value": 20}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "How To"}, "Priority": {"_label": "Priority", "value": "Normal"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "13.08.2019"}, "SAPComponentKey": {"_label": "Component", "value": "BI-BIP-AUT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Authentication, ActiveDirectory, LDAP, SSO, Vintela"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "1631734 - How To Setup Windows Active Directory plugin and SSO for BI 4.0 - NOTE: this solution is no longer the current standard"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<ul>\r\n<li><strong><span style=\"color: #ff0000;\">AS started below this KBA was replaced about 5 years ago and is now causing issues when it is found. Archiving to prevent case generation by using outdated article</span></strong></li>\r\n<li><strong><span style=\"color: #ff0000;\">All future SSO should be using KBA </span></strong><a target=\"_blank\" href=\"/notes/2629070\">2629070</a></li>\r\n<li></li>\r\n<li><strong><span style=\"color: #ff0000;\">***IMPORTANT UPDATE***</span></strong></li>\r\n<li>We have a<strong> BRAND NEW 4.2 SSO guide </strong>in KBA<strong> <a target=\"_blank\" href=\"/notes/2629070\">2629070</a>&#160;</strong>which should be backward compatible to all version of 4.2 and 4.1</li>\r\n<li>If you have SSO users on <strong>windows 10 using IE 11</strong> or <strong>Chrome</strong> and do not want to modify the registry to enable SSO then KBA <strong><a target=\"_blank\" href=\"/notes/2629070\">2629070</a></strong> should be used</li>\r\n<li>If your AD environment uses <strong>AES</strong> by default then use KBA <strong><a target=\"_blank\" href=\"/notes/2629070\">2629070</a></strong> configuration can use <strong>AES</strong> out of the box</li>\r\n<li>If your environment will be using <strong>SSL</strong> on the web/app KBA <strong><a target=\"_blank\" href=\"/notes/2629070\">2629070</a></strong> configuration uses <strong>SSL</strong> (HTTPS) out of the box as well</li>\r\n<li>All the latest configuration options, all the latest security settings, constrained delegation,&#160; commands to avoid duplicate SPN's, easier to implement web/app settings for non tomcat deployments, etc are in KBA <strong>2629070</strong></li>\r\n</ul>\r\n<ul>\r\n<li>If you are using server <strong>Windows AD 2003</strong> (no longer on the PAM for 4.1 and 4.2) or <strong>BI 4.0</strong> (out of support as well) then this guide is still the best option, most of the above settings can be implemented but are contained separately in other guides in the reference section below</li>\r\n<li>How to configure BI4 for manual Active Directory (AD) logon</li>\r\n<li>How to configure BI4 for AD Single Sign-on (SSO)</li>\r\n<li>Attempting to automatically logon and receiving a logon page</li>\r\n<li>SSO fails with various errors such as (but not limited to) the ones listed below...</li>\r\n<li>If bypassing SSO by using HTTP://&lt;WebApplicationServer&gt;:port/BOE/BI/logonNoSso.jsp there is no error and manual logon can be used</li>\r\n</ul>\r\n<p><strong style=\"color: #0000ff;\">NOTE</strong><span style=\"color: #0000ff;\">: when using the attached document,</span><strong style=\"color: #0000ff;\"> pay special attention</strong><span style=\"color: #0000ff;\"> to </span><strong style=\"color: #0000ff;\">section 8</strong><span style=\"color: #0000ff;\"> which has relevant </span><strong style=\"color: #0000ff;\">best practice</strong><span style=\"color: #0000ff;\"> information for </span><strong style=\"color: #0000ff;\">both</strong><span style=\"color: #0000ff;\"> Manual AD and SSO setup</span></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Environment\">Environment</h3>\r\n<ul>\r\n<li>SAP BusinessObjects Business Intelligence 4.x Doc was created for 4.0 but compatible with 4.1 4.2 all support packages and patches&#160;</li>\r\n<li>Active Directory 2008 2012 potentially newer versions</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Resolution\">Resolution</h3>\r\n<p>Please refer to the attached file <em>Configuring Active Directory Manual Authentication and SSO for BI4.pdf</em></p>\r\n<p><span style=\"text-decoration: underline;\">Table of Contents</span></p>\r\n<p><strong>Prerequisites</strong></p>\r\n<p><strong>Section 1 - Planning your Service Account Configuration</strong></p>\r\n<ul>\r\n<li>Roles of the Service Account</li>\r\n<li>Role 1 &#8211; Query Active Directory</li>\r\n<li>Role 2 &#8211; Run the SIA/CMS and allow manual AD logins.</li>\r\n<li>Role 3 &#8211; Allows Single Sign On</li>\r\n</ul>\r\n<p><strong>Section 2 - Creating and preparing the service account</strong></p>\r\n<ul>\r\n<li>Creating the Service Account</li>\r\n<li>Create Service Principal Names for the Service Account\r\n<ul>\r\n<li>Background Information</li>\r\n<li>Setspn Commands</li>\r\n<li>To View all created SPN&#8217;s</li>\r\n</ul>\r\n</li>\r\n<li>Delegation for the Service Account</li>\r\n</ul>\r\n<p><strong>Section 3 - Configure the AD Plugin Page in the CMC and map in AD groups</strong></p>\r\n<p><strong>Section 4 &#8212; Steps to start the SIA/CMS under the service account</strong></p>\r\n<ul>\r\n<li>Verify that the service account and AD logins are working</li>\r\n</ul>\r\n<p><strong>Section 5 &#8211;Configuring Manual AD authentication to Java Application Servers</strong></p>\r\n<ul>\r\n<li>Create the bscLogin.conf file</li>\r\n<li>Create the krb5.ini file</li>\r\n<li>Verify java can successfully receive a kerberos ticket</li>\r\n</ul>\r\n<p><strong>Section 6 &#8211; Configuring BI Launch Pad and CMC for manual AD logon</strong></p>\r\n<ul>\r\n<li>Enable the Authentication dropdown for BI Launch Pad</li>\r\n<li>Point your application server to the bscLogin.conf and krb5.ini files.</li>\r\n<li>Verify the bscLogin.conf has been loaded by your application server</li>\r\n<li>Common reasons why a manual login to BI Launch Pad would fail</li>\r\n</ul>\r\n<p><strong>Section 7 &#8211; Configuring Active Directory Single Sign On</strong></p>\r\n<ul>\r\n<li>Increase Tomcat&#8217;s maxHttpHeaderSize</li>\r\n<li>Create and configure a global.properties file</li>\r\n<li>Configuring the application server&#8217;s Java Options for AD Single Sign On</li>\r\n<li>Verify that the vintela filter has loaded successfully\r\n<ul>\r\n<li>If credentials are not obtained&#8230;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</li>\r\n</ul>\r\n</li>\r\n<li>Testing AD Single Sign On</li>\r\n<ul>\r\n<li>Browser Configuration</li>\r\n<li>First SSO attempt</li>\r\n</ul>\r\n<li><strong><span style=\"color: #0000ff;\">NOTE:&#160;</span></strong> Some versions of IE 11 and google Chrome browser will not always work with the default configuration (<strong>delegation to any service</strong>)</li>\r\n<li>If you have issues with IE credential guard in KBA <a target=\"_blank\" href=\"/notes/2485300\">2485300</a> or with Chrome&#160;<a target=\"_blank\" href=\"/notes/1887193\">1887193</a> then you may need to enable constrained delgation in KBA <a target=\"_blank\" href=\"/notes/2182400\">2182400</a></li>\r\n</ul>\r\n<p><strong>Section 8 &#8211; Additional information and settings</strong></p>\r\n<ul>\r\n<li>Ensure your .properties files are not overwritten after a patch or redeploy</li>\r\n<li>Encrypting your service account password with a keytab</li>\r\n<li>Setting up Constrained Delegation</li>\r\n<li>Configuring your system for end-to-end SSO to a DB</li>\r\n<li>Clean up tracing</li>\r\n</ul>\r\n<p><strong>Other information</strong></p>\r\n<p><strong>Appendix</strong></p>\r\n<p><strong>Key Terms</strong></p>\r\n<p><strong>3rd Party Troubleshooting Tools</strong></p>\r\n<h3 data-toc-skip class=\"section\" id=\"See Also\">See Also</h3>\r\n<ul>\r\n<li>If your web/app has SSL enabled some additional steps may need to be changed see <a target=\"_blank\" href=\"/notes/1379908\">KBA&#160;1379908&#160;</a></li>\r\n<li>If you are setting up SSO to the DB for SQL, hana,&#160;Analysis, Oracle or other&#160;supported DB&#160;see <a target=\"_blank\" href=\"/notes/1869952\">KBA 1869952</a></li>\r\n<li>if you are setting up web services SSO see <a target=\"_blank\" href=\"/notes/1646920\">KBA 1646920</a></li>\r\n<li><strong>After SSO is working,</strong> if you want to set up constrained delegation you can reference KBA <a target=\"_blank\" href=\"/notes/2182400\">2182400</a>&#160;this is not applicable for all configurations but shows the general single account Microsoft configuration</li>\r\n<li>To avoid using java options on 3rd party web/apps see KBA&#160;<a target=\"_blank\" href=\"/notes/2084161\">2084161</a> to setup the idm.password in the <strong>global.properties</strong></li>\r\n<li>Here is a <a target=\"_blank\" href=\"https://ping.force.com/Support/PingFederate/Integrations/How-to-configure-supported-browsers-for-Kerberos-NTLM\">good external article</a> about setting up different browsers for kerberos SSO (aka spnego or negotiate)&#160;</li>\r\n</ul>\r\n<h3 data-toc-skip>ADDITIONAL INFORMATION</h3>\r\n<ul>\r\n<li><span style=\"color: #800000;\">Please rate and comment on this KBA article, we review comments and ratings regularly and it's the best way to let us know what is working or not</span></li>\r\n<li><span style=\"color: #0000ff;\">If you have any questions related to this article try to <strong>post</strong> on <a target=\"_blank\" href=\"https://answers.sap.com/questions/ask.html?primaryTagId=629074762716334381746959911269220\"><span style=\"color: #0000ff;\">SAP Questions and Answers</span></a>&#160;or <strong>review</strong> other <a target=\"_blank\" href=\"https://answers.sap.com/tags/629074762716334381746959911269220\"><span style=\"color: #0000ff;\">Questions and Answers</span></a>&#160;</span></li>\r\n<li><span style=\"color: #0000ff;\">Search for additional solutions via our <a target=\"_blank\" href=\"https://ga.support.sap.com/dtp/viewer/#/tree/1081/actions/12120\"><span style=\"color: #0000ff;\">authentication guided answers</span></a>&#160;or search all SAP on <a target=\"_blank\" href=\"https://launchpad.support.sap.com/\"><span style=\"color: #0000ff;\">the Launchpad&#160;&#160;</span></a></span></li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Keywords\">Keywords</h3>\r\n<div style=\"font-size: 100.01%;\">\r\n<p>how to configure directions documentation documents&#160;steps to follow&#160;vintela ventila vintella ventela set up setup vintela config configuration configuring AD Active Directory single sign on sign-on slient automatic opendocument</p>\r\n<p>intermittent error fail trouble troubleshoot shoot test java tomcat websphere weblogic oracle application server&#160;netweaver JDK java SDK development kit XI4 XI&#160;4.0&#160; XI 4.1 XI41 XIR4 XI 4.x BI4.0 BI zie MNHWW</p>\r\n<p>mkba htkba biauth Account Information Not Recognized: Active Directory Authentication failed to log you on. Please contact your system administrator to make sure that you are a member of a valid mapped group and try again. If you are not a member of the default domain, enter your user name as UserName@DNS_DomainName, and then try again. (FWM 00006) HTTP 500 error or page cannot be displayed HTTP 404 error HTTP 400 bad request or bad tag jcsi.kerberos: Could not decrypt service ticket with Key type ##, KVNO ##, Principal \"HTTP/XXX.YYY.ZZZ\" using key:Principal <EMAIL></p>\r\n</div>\r\n<p><br /><a target=\"_blank\" name=\"anchor_DEFAULT_HEADER_C\"></a></p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I816814)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I816814)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001631734/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001631734/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001631734/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001631734/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001631734/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001631734/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001631734/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001631734/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001631734/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Configuring_Active_Directory_Manual_Authentication_and_SSO_for_BI4.pdf", "FileSize": "683", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002007204500000061692011&iv_version=0020&iv_guid=6EAE8B28C7191ED4BCE932AF8B477EAF"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2485300", "RefComponent": "BI-BIP-AUT", "RefTitle": "Windows AD SSO does not work on Windows 10 version client machines", "RefUrl": "/notes/2485300"}, {"RefNumber": "2182400", "RefComponent": "BI-BIP-AUT", "RefTitle": "Setting up constrained delegation in BI 4.x", "RefUrl": "/notes/2182400"}, {"RefNumber": "2084161", "RefComponent": "BI-BIP-AUT", "RefTitle": "How to setup vintela password in the global.properties file instead of using java options", "RefUrl": "/notes/2084161"}, {"RefNumber": "1887193", "RefComponent": "BI-BIP-AUT", "RefTitle": "Not able to perform SSO with BI using Google Chrome browser", "RefUrl": "/notes/1887193"}, {"RefNumber": "1869952", "RefComponent": "BI-BIP-AUT", "RefTitle": "Setting up kerberos SSO to the database requirements & troubleshooting", "RefUrl": "/notes/1869952"}, {"RefNumber": "1646920", "RefComponent": "BI-BIP-AUT", "RefTitle": "How to configure Web Services Single Sign-On (dswsbobje) with Tomcat for SAP BusinessObjects Business Intelligence platform 4.x", "RefUrl": "/notes/1646920"}, {"RefNumber": "1483762", "RefComponent": "BI-BIP-AUT", "RefTitle": "Configuring Manual Kerberos Authentication and/or SSO in Distributed Environments with XI 3.1 SP3 ***Best Practice***", "RefUrl": "/notes/1483762"}, {"RefNumber": "1478891", "RefComponent": "BI-BIP-AUT", "RefTitle": "Rules and Best Practices for group mapping in Active Directory", "RefUrl": "/notes/1478891"}, {"RefNumber": "1379908", "RefComponent": "BI-BIP-AUT", "RefTitle": "How to setup SSL and vintela SSO at the same time on a java application server", "RefUrl": "/notes/1379908"}, {"RefNumber": "", "RefComponent": "", "RefTitle": "SSO Configuration with Active Directory SAP Business Objects 4.2 (AES Encryption)", "RefUrl": "https://blogs.sap.com/2016/09/02/sso-configuration-with-active-directory-sap-business-objects-42-aes-encryption/"}, {"RefNumber": "", "RefComponent": "", "RefTitle": "How-to-configure-supported-browsers-for-Kerberos", "RefUrl": "https://ping.force.com/Support/PingFederate/Integrations/How-to-configure-supported-browsers-for-Kerberos-NTLM"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2820819", "RefComponent": "BI-BIP-AUT", "RefTitle": "BI Auth Troubleshooting Series: AD Vintela Kerberos SSO: Analyzing Web Application Logs", "RefUrl": "/notes/2820819 "}, {"RefNumber": "2783763", "RefComponent": "BI-BIP-IDT-CX", "RefTitle": "IDT Connect HIVE With Kerberos", "RefUrl": "/notes/2783763 "}, {"RefNumber": "1943522", "RefComponent": "BI-BIP-DEP", "RefTitle": "Error \"the page was not displayed because the request entity is too large\" is shown when accesing BI launchpad or when openning webi documents", "RefUrl": "/notes/1943522 "}, {"RefNumber": "2684843", "RefComponent": "BI-BIP-AUT", "RefTitle": "BI Auth Troubleshooting Series: Enable Enhanced Vintela Tracing", "RefUrl": "/notes/2684843 "}, {"RefNumber": "2640238", "RefComponent": "BI-BIP-AUT", "RefTitle": "How to Securely Integrate BI 4.x with Windows Active Directory and SSO in Distributed Environments [VIDEO]", "RefUrl": "/notes/2640238 "}, {"RefNumber": "1646920", "RefComponent": "BI-BIP-AUT", "RefTitle": "How to configure Web Services Single Sign-On (dswsbobje) with Tomcat for SAP BusinessObjects Business Intelligence platform 4.x", "RefUrl": "/notes/1646920 "}, {"RefNumber": "2629070", "RefComponent": "BI-BIP-AUT", "RefTitle": "How to Securely Integrate BI 4.2 or 4.3 with Windows Active Directory and SSO in Distributed Environments - Best Practices", "RefUrl": "/notes/2629070 "}, {"RefNumber": "1203031", "RefComponent": "BI-BIP-AUT", "RefTitle": "Error: \"Account Information Not Recognized: Active Directory Authentication failed to log you on\" when attempting to log on to BI Launchpad", "RefUrl": "/notes/1203031 "}, {"RefNumber": "1323391", "RefComponent": "BI-BIP-AUT", "RefTitle": "What are the Microsoft requirements to perform kerberos SSO in multiple AD forests environments with BI", "RefUrl": "/notes/1323391 "}, {"RefNumber": "2547475", "RefComponent": "BI-BIP-AUT", "RefTitle": "BI Auth Troubleshooting Series: testing Analysis Kerberos SSO", "RefUrl": "/notes/2547475 "}, {"RefNumber": "2543491", "RefComponent": "EIM-IS-SVR", "RefTitle": "How to setup Windows AD SSO for SAP Information Steward", "RefUrl": "/notes/2543491 "}, {"RefNumber": "2543369", "RefComponent": "EIM-DS-SVR", "RefTitle": "How to setup Windows AD SSO for Data Services", "RefUrl": "/notes/2543369 "}, {"RefNumber": "2508116", "RefComponent": "BI-BIP-AUT", "RefTitle": "SSO fails for unexplained reasons, possible license errors in java logs or after java SDK upgrade", "RefUrl": "/notes/2508116 "}, {"RefNumber": "2182400", "RefComponent": "BI-BIP-AUT", "RefTitle": "Setting up constrained delegation in BI 4.x", "RefUrl": "/notes/2182400 "}, {"RefNumber": "2485300", "RefComponent": "BI-BIP-AUT", "RefTitle": "Windows AD SSO does not work on Windows 10 version client machines", "RefUrl": "/notes/2485300 "}, {"RefNumber": "2486114", "RefComponent": "BI-BIP-AUT", "RefTitle": "How to enable SSO for Fiorified BI Launchpad in BI 4.2 SP04 and above", "RefUrl": "/notes/2486114 "}, {"RefNumber": "2483974", "RefComponent": "BI-BIP-AUT", "RefTitle": "Windows AD SSO using AES encryption not working in Business Intelligence Platform", "RefUrl": "/notes/2483974 "}, {"RefNumber": "2445067", "RefComponent": "BI-DEV-WEB", "RefTitle": "Cannot generate Logontoken when Windows Active Directory (AD) users logon to Central Management Console (CMS) through RESTful - SAP BusinessObjects Business Intelligence Platform 4.X", "RefUrl": "/notes/2445067 "}, {"RefNumber": "2430695", "RefComponent": "BI-BIP-AUT", "RefTitle": "BI SSO login using a load balancer URL fails and provides login screen", "RefUrl": "/notes/2430695 "}, {"RefNumber": "2422419", "RefComponent": "BI-BIP-AUT", "RefTitle": "SSO fails for unexplained reasons, possible license errors in java logs", "RefUrl": "/notes/2422419 "}, {"RefNumber": "2379737", "RefComponent": "BI-BIP-SL-ENG-REL", "RefTitle": "Receive error connecting to Hadoop Cloudera from non windows OS", "RefUrl": "/notes/2379737 "}, {"RefNumber": "2354696", "RefComponent": "MOB-APP-BI-IOS", "RefTitle": "Steps and prerequisites to setup BI Mobile Kerberos SSO Best Practice KBA", "RefUrl": "/notes/2354696 "}, {"RefNumber": "1483762", "RefComponent": "BI-BIP-AUT", "RefTitle": "Configuring Manual Kerberos Authentication and/or SSO in Distributed Environments with XI 3.1 SP3 ***Best Practice***", "RefUrl": "/notes/1483762 "}, {"RefNumber": "1958305", "RefComponent": "BI-BIP-AUT", "RefTitle": "How run the KINIT command in BI 4.x environment for testing windowsAD?", "RefUrl": "/notes/1958305 "}, {"RefNumber": "1996440", "RefComponent": "BI-BIP-AUT", "RefTitle": "How to configure Active Directory SSO for RESTful Web Services when using Power Query for Excel", "RefUrl": "/notes/1996440 "}, {"RefNumber": "2146460", "RefComponent": "BI-BIP-AUT", "RefTitle": "SSO on database not working on cluster environment in BusinessObjects", "RefUrl": "/notes/2146460 "}, {"RefNumber": "2143895", "RefComponent": "BI-BIP-AUT", "RefTitle": "\"Generic\" Shell KBA example for FWM login Error messages", "RefUrl": "/notes/2143895 "}, {"RefNumber": "2084161", "RefComponent": "BI-BIP-AUT", "RefTitle": "How to setup vintela password in the global.properties file instead of using java options", "RefUrl": "/notes/2084161 "}, {"RefNumber": "1184989", "RefComponent": "BI-BIP-AUT", "RefTitle": "Error: \"An error has occurred: java.lang.NullPointerException\" logging on to InfoView with Vintela Single Sign-On after setting constrained delegation", "RefUrl": "/notes/1184989 "}, {"RefNumber": "2018869", "RefComponent": "BI-BIP-AUT", "RefTitle": "AD SSO is not working in Advance Analysis for MS Office with Business Objects BI 4.0", "RefUrl": "/notes/2018869 "}, {"RefNumber": "1987304", "RefComponent": "BI-BIP-ADM", "RefTitle": "Incorrect username displayed in BI launchpad when a user is connected via manual AD and the page is refreshed", "RefUrl": "/notes/1987304 "}, {"RefNumber": "1965433", "RefComponent": "BI-BIP-AUT", "RefTitle": "Setting up AD SSO when CMS is on Unix or Linux in BI4.x ***BEST PRACTICE***", "RefUrl": "/notes/1965433 "}, {"RefNumber": "1771301", "RefComponent": "BI-BIP-AUT", "RefTitle": "How to configure Active Directory SSO with Crystal Reports for Enterprise", "RefUrl": "/notes/1771301 "}, {"RefNumber": "1914867", "RefComponent": "BI-BIP-AUT", "RefTitle": "After configuring Vintela SSO, Information Steward brings you to a login screen", "RefUrl": "/notes/1914867 "}, {"RefNumber": "1379908", "RefComponent": "BI-BIP-AUT", "RefTitle": "How to setup SSL and vintela SSO at the same time on a java application server", "RefUrl": "/notes/1379908 "}, {"RefNumber": "1688079", "RefComponent": "BI-BIP-AUT", "RefTitle": "Configuring BI4.x Analysis Edition for OLAP for End-to-End SSO to MS SQL Server Analysis Services", "RefUrl": "/notes/1688079 "}, {"RefNumber": "1869952", "RefComponent": "BI-BIP-AUT", "RefTitle": "Setting up kerberos SSO to the database requirements & troubleshooting", "RefUrl": "/notes/1869952 "}, {"RefNumber": "1821530", "RefComponent": "BI-BIP-AUT", "RefTitle": "RUNAS ERROR: Unable to run - cmd 1385: Logon failure: the user has not been granted the requested logon type at this computer", "RefUrl": "/notes/1821530 "}, {"RefNumber": "1814990", "RefComponent": "BI-BIP-ADM", "RefTitle": "\"Data source name not found and no default driver specified (IES 10901)\" when refreshing Reports", "RefUrl": "/notes/1814990 "}, {"RefNumber": "1811398", "RefComponent": "BI-BIP-AUT", "RefTitle": "How to setup BI components to login to hana via AD kerberos SSO", "RefUrl": "/notes/1811398 "}, {"RefNumber": "1781640", "RefComponent": "BI-BIP-AUT", "RefTitle": "How to configure single sign-on to the database using Web Application Container Server (WACS) in BI 4.0", "RefUrl": "/notes/1781640 "}, {"RefNumber": "1748508", "RefComponent": "BI-BIP-AUT", "RefTitle": "What is Single Sign-On (SSO)", "RefUrl": "/notes/1748508 "}, {"RefNumber": "1742096", "RefComponent": "BI-BIP-AUT", "RefTitle": "Configure Kerberos AD SSO on JBOSS BI4 fails with various errors", "RefUrl": "/notes/1742096 "}, {"RefNumber": "1476374", "RefComponent": "BI-BIP-AUT", "RefTitle": "***Best Practices***  including Basic and Advanced AD Troubleshooting Steps for Manual Logon, NTLM, Kerberos and Vintela Single Sign On", "RefUrl": "/notes/1476374 "}, {"RefNumber": "1671918", "RefComponent": "BI-BIP-ADM", "RefTitle": "Reference to Keytab file in global.properties is lost after Upgrading patch or Redeploy in BI4", "RefUrl": "/notes/1671918 "}, {"RefNumber": "2651277", "RefComponent": "BI-BIP-SL-DSL", "RefTitle": "Error message when refreshing List of Values against MSAS via kerberos SSO", "RefUrl": "/notes/2651277 "}, {"RefNumber": "2613391", "RefComponent": "BI-DEV-WEB", "RefTitle": "AD SSO for RESTful on Tomcat Server", "RefUrl": "/notes/2613391 "}, {"RefNumber": "2453258", "RefComponent": "BI-BIP-AUT", "RefTitle": "BI launchpad SSO fails on weblogic with java upgrade above 1.8.121", "RefUrl": "/notes/2453258 "}, {"RefNumber": "2377991", "RefComponent": "BI-BIP-INV", "RefTitle": "AD SSO users are prompted to change password if default authentication is set to secEnterprise", "RefUrl": "/notes/2377991 "}, {"RefNumber": "2272331", "RefComponent": "BI-RA-AD", "RefTitle": "HTTP 403 Error using Analysis for Office via non-SSL Web Services URL", "RefUrl": "/notes/2272331 "}, {"RefNumber": "2253717", "RefComponent": "BI-RA-WBI-BE-SM", "RefTitle": "Active Directory user, used as an alias to Enterprise user account is prompted to change the Enterprise password", "RefUrl": "/notes/2253717 "}, {"RefNumber": "2233536", "RefComponent": "BI-BIP-AUT", "RefTitle": "Windows Active Directory does not work for SAP BusinessObjects BI Platform 4.1  SP6", "RefUrl": "/notes/2233536 "}, {"RefNumber": "2220922", "RefComponent": "BI-BIP-AUT", "RefTitle": "Login with Windows Active Directory SSO into analysis office through Netweaver application server does not work.", "RefUrl": "/notes/2220922 "}, {"RefNumber": "1798979", "RefComponent": "BC-SEC-LGN", "RefTitle": "SPNego ABAP: Downport", "RefUrl": "/notes/1798979 "}, {"RefNumber": "2047997", "RefComponent": "BI-RA-AO-XLA", "RefTitle": "Analysis for Office: Single-Sign-On (SSO) Main Note", "RefUrl": "/notes/2047997 "}]}}, "Validity": {"_label": "Products", "_columnNames": {"Product": "Products"}, "Items": [{"Product": "SAP BusinessObjects Business Intelligence platform 4.0"}, {"Product": "SAP BusinessObjects Business Intelligence platform 4.1"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "26 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 4.21, "Quality-Votes": 28, "RatingQualityDetails": {"Stars-1": 3, "Stars-2": 0, "Stars-3": 1, "Stars-4": 8, "Stars-5": 16}, "RatingQuestions": [{"Key": "URF_QA1", "Title": "This article is inaccurate", "Description": ""}, {"Key": "URF_QA2", "Title": "This article is confusing", "Description": ""}, {"Key": "URF_QA3", "Title": "This article doesn't solve my problem", "Description": ""}, {"Key": "URF_QA4", "Title": "I do not like the feature described", "Description": ""}]}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}