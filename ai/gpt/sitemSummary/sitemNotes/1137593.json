{"Request": {"Number": "1137593", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 356, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000006777522017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001137593?language=E&token=7F5587F63E161FCF1038B68AA507D504"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001137593", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001137593/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1137593"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "08.02.2008"}, "SAPComponentKey": {"_label": "Component", "value": "PY-BR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Brazil"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Brazil", "value": "PY-BR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-BR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1137593 - HBRDIRF0: Layout adjusts for 2008"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The field \"For declarator use\" is in the wrong position in the<br />record 2. Also, the sequential number on file (registry 3) is not right.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>HBRDIRF0; \"For declarator use\"; registry 2; registry 3; 2008; Brasil<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>It was changed the type of the field \"For declarator use\" in the structures HBRDF2_2008_0, HBRDF2_2008_1 and HBRDF2_2008_2. Also the sequential number for the registry 3 was adjusted.<br /></p> <b>IMPORTANT:</b><br /> <p>Be aware of an Advance Delivery delivers the last version of the object, it means that if you do not have the last HR Support Package installed in you system you could get errors, either Syntax Errors or process errors. In this case the only option is to undo the changes from Advance Delivery and do the changes manually according to the Correction Instructions available in this note.<br /><br />An Advanced Delivery is available in the attached files according to the following list(\"xxxxxx\" means numbers):<br /><br />L7DKxxxxxx_600.CAR - Release 6.00<br />L6DKxxxxxx_500.CAR - Release 5.00<br />L6BKxxxxxx_470.CAR - Release 4.70(Enterprise)<br />L9CKxxxxxx_46C.CAR - Release 4.6C<br /><br />For more details about Advance Delivery installation procedure please<br />read the notes listed in \"Related Notes\".<br /><br /><br />The correction described in this note will be included in an HR Support<br />Package, as indicated in item \"Reference to Support Packages\".<br /><br />The support package includes:<br /><br />&#x00A0;&#x00A0;- changes in include PCDIFBR0<br />&#x00A0;&#x00A0;- new data elements:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;- PBR_FIL32<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;- PBR_USODE<br />&#x00A0;&#x00A0;- changes in table definitions:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;- HBRDF2_2008_0<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;- HBRDF2_2008_1<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;- HBRDF2_2008_2<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I811687)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I813686)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001137593/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001137593/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001137593/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001137593/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001137593/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001137593/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001137593/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001137593/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001137593/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "L9B139079_46B.car", "FileSize": "20", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000105922008&iv_version=0005&iv_guid=4DE02FB34F2E21408F75D63B54AA7679"}, {"FileName": "L6D084905_500.car", "FileSize": "19", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000105922008&iv_version=0005&iv_guid=2E3681D629D9E34ABEE3084DB1677F5A"}, {"FileName": "L6B154398_470.car", "FileSize": "20", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000105922008&iv_version=0005&iv_guid=1B8654EB20F4D943BCBDB964FA29D4A0"}, {"FileName": "L9C241027_46C.car", "FileSize": "19", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000105922008&iv_version=0005&iv_guid=BCB31BF12E2A4D42AA8F48F0EEE2B724"}, {"FileName": "L7D071847_600.car", "FileSize": "22", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000105922008&iv_version=0005&iv_guid=F0C4EC6EEC483B4A96600DBC16584C57"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1129061", "RefComponent": "PY-BR", "RefTitle": "Legal change in HBRDIRF0 for year 2008", "RefUrl": "/notes/1129061"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "1129061", "RefComponent": "PY-BR", "RefTitle": "Legal change in HBRDIRF0 for year 2008", "RefUrl": "/notes/1129061 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 46B", "SupportPackage": "SAPKE46BD4", "URL": "/supportpackage/SAPKE46BD4"}, {"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46CD3", "URL": "/supportpackage/SAPKE46CD3"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47078", "URL": "/supportpackage/SAPKE47078"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50045", "URL": "/supportpackage/SAPKE50045"}, {"SoftwareComponentVersion": "SAP_HR 600", "SupportPackage": "SAPKE60028", "URL": "/supportpackage/SAPKE60028"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_HR", "NumberOfCorrin": 5, "URL": "/corrins/0001137593/2"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 5, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 2, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "1129061 ", "URL": "/notes/1129061 ", "Title": "Legal change in HBRDIRF0 for year 2008", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "1136681 ", "URL": "/notes/1136681 ", "Title": "HBRDIRF0 - Adjusts in the registry 3 sequential number", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "1129061 ", "URL": "/notes/1129061 ", "Title": "Legal change in HBRDIRF0 for year 2008", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "1136681 ", "URL": "/notes/1136681 ", "Title": "HBRDIRF0 - Adjusts in the registry 3 sequential number", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "1129061 ", "URL": "/notes/1129061 ", "Title": "Legal change in HBRDIRF0 for year 2008", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "1136681 ", "URL": "/notes/1136681 ", "Title": "HBRDIRF0 - Adjusts in the registry 3 sequential number", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "1129061 ", "URL": "/notes/1129061 ", "Title": "Legal change in HBRDIRF0 for year 2008", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "1136681 ", "URL": "/notes/1136681 ", "Title": "HBRDIRF0 - Adjusts in the registry 3 sequential number", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1129061 ", "URL": "/notes/1129061 ", "Title": "Legal change in HBRDIRF0 for year 2008", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1136681 ", "URL": "/notes/1136681 ", "Title": "HBRDIRF0 - Adjusts in the registry 3 sequential number", "Component": "PY-BR"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}