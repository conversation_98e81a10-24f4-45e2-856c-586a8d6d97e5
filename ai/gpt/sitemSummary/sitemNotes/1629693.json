{"Request": {"Number": "1629693", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 518, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000009675702017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=EB49EB31392EF87045347E1D1662412B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1629693"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Exit added"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.09.2011"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX"}, "SAPComponentKeyText": {"_label": "Component", "value": "Flexible Real Estate Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1629693 - Influencing expiration date of RE-FX logs"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note delivers the new BAdI definition BADI_RECA_MESSAGE_LIST.<br /><br />This BAdI enables you to change the expiration date of logs for the component RE-FX.<br /><br /><U>Method description</U><br /></p> <UL><LI>CHANGE_EXPIRATION_DATE</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Changes the expiration date<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<U>Parameters</U></p> <UL><UL><LI>ID_OBJECT</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This parameter contains the log object.</p> <UL><UL><LI>ID_SUBOBJECT</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This parameter contains the log subobject.</p> <UL><UL><LI>CD_EXPIRATION_DATE</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can use this parameter to change the expiration date.</p> <UL><UL><LI>CF_CAN_DELETE_BEFORE</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you set this parameter, you can delete the log even before the expiration date.<br /><br />This note also delivers the implementation sample class CL_DEF_IM_RECA_MESSAGE_LIST that also serves as a fallback class.<br /><br />This class influences the behavior in such a way that in future, the logs for the log object REAJ (adjustment of conditions) only expire after one year and cannot be deleted any earlier.<br /><br />All other still expire (as before) 30 days after they are created and cann still be deleted before they expire (also see transaction RECALD or SLG2).<br /><br />Log objects from the RE-FX area:</p> <UL><LI>REAJ - RE: Adjustment of conditions</LI></UL> <UL><LI>RECA - RE: Cross component</LI></UL> <UL><LI>RECD - RE: Conditions and cash flow</LI></UL> <UL><LI>RECO - RE: Controlling</LI></UL> <UL><LI>RECP - RE: Correspondence</LI></UL> <UL><LI>REIT - RE: Option rate determination, input tax distribution and input tax correction</LI></UL> <UL><LI>REMI - RE: Migration</LI></UL> <UL><LI>REMM - RE: Third-party management</LI></UL> <UL><LI>REOR - RE: Rental offer and RE search request</LI></UL> <UL><LI>RERA - RE: Rental accounting</LI></UL> <UL><LI>RESC - RE: Service charges/operating costs</LI></UL> <UL><LI>RESR - RE: Sales-based rent<br /></LI></UL> <p>For an overview over the log objects from the RE-FX area, call transaction SLG0.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RECALD; SLG2<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Implement the attached program corrections.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D022894)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D050643)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "939972", "RefComponent": "RE-FX-RA", "RefTitle": "FAQ: Accounting", "RefUrl": "/notes/939972"}, {"RefNumber": "939971", "RefComponent": "RE-FX", "RefTitle": "FAQ and consulting notes for RE-FX", "RefUrl": "/notes/939971"}, {"RefNumber": "924831", "RefComponent": "RE-FX-SC", "RefTitle": "FAQ: Accounts, SCS, service charge settlement, COA, condominium owners association, sales", "RefUrl": "/notes/924831"}, {"RefNumber": "1629875", "RefComponent": "RE-FX-AJ", "RefTitle": "Expiration date when continuing adjustments", "RefUrl": "/notes/1629875"}, {"RefNumber": "1448738", "RefComponent": "RE-FX-AJ", "RefTitle": "FAQ: Rent adjustment", "RefUrl": "/notes/1448738"}, {"RefNumber": "1013725", "RefComponent": "RE-FX-IT", "RefTitle": "FAQ: Input tax distribution, input tax corr., option rates", "RefUrl": "/notes/1013725"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2935489", "RefComponent": "RE-FX-LA", "RefTitle": "Adjustment of Expiration Date of Logs for Contract Valuation RECE", "RefUrl": "/notes/2935489 "}, {"RefNumber": "1955339", "RefComponent": "RE-FX-SC", "RefTitle": "RESCIS: Error log not displayed", "RefUrl": "/notes/1955339 "}, {"RefNumber": "939972", "RefComponent": "RE-FX-RA", "RefTitle": "FAQ: Accounting", "RefUrl": "/notes/939972 "}, {"RefNumber": "939971", "RefComponent": "RE-FX", "RefTitle": "FAQ and consulting notes for RE-FX", "RefUrl": "/notes/939971 "}, {"RefNumber": "1448738", "RefComponent": "RE-FX-AJ", "RefTitle": "FAQ: Rent adjustment", "RefUrl": "/notes/1448738 "}, {"RefNumber": "1629875", "RefComponent": "RE-FX-AJ", "RefTitle": "Expiration date when continuing adjustments", "RefUrl": "/notes/1629875 "}, {"RefNumber": "1013725", "RefComponent": "RE-FX-IT", "RefTitle": "FAQ: Input tax distribution, input tax corr., option rates", "RefUrl": "/notes/1013725 "}, {"RefNumber": "924831", "RefComponent": "RE-FX-SC", "RefTitle": "FAQ: Accounts, SCS, service charge settlement, COA, condominium owners association, sales", "RefUrl": "/notes/924831 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "606", "To": "606", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-APPL 602", "SupportPackage": "SAPK-60211INEAAPPL", "URL": "/supportpackage/SAPK-60211INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 603", "SupportPackage": "SAPK-60310INEAAPPL", "URL": "/supportpackage/SAPK-60310INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 604", "SupportPackage": "SAPK-60411INEAAPPL", "URL": "/supportpackage/SAPK-60411INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 605", "SupportPackage": "SAPK-60507INEAAPPL", "URL": "/supportpackage/SAPK-60507INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 606", "SupportPackage": "SAPK-60602INEAAPPL", "URL": "/supportpackage/SAPK-60602INEAAPPL"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "EA-APPL", "NumberOfCorrin": 5, "URL": "/corrins/**********/229"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 5, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 3, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1318290 ", "URL": "/notes/1318290 ", "Title": "BAdIs: Missing sample implementations", "Component": "RE-FX"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1461985 ", "URL": "/notes/1461985 ", "Title": "Enhancements of interfaces for object assignment", "Component": "RE-FX"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1629534 ", "URL": "/notes/1629534 ", "Title": "Ability to reproduce deletion of logs", "Component": "RE-FX"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1629534 ", "URL": "/notes/1629534 ", "Title": "Ability to reproduce deletion of logs", "Component": "RE-FX"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "604", "ValidTo": "604", "Number": "1629534 ", "URL": "/notes/1629534 ", "Title": "Ability to reproduce deletion of logs", "Component": "RE-FX"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1629534 ", "URL": "/notes/1629534 ", "Title": "Ability to reproduce deletion of logs", "Component": "RE-FX"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "1629534 ", "URL": "/notes/1629534 ", "Title": "Ability to reproduce deletion of logs", "Component": "RE-FX"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}