{"Request": {"Number": "1632755", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 337, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017311702017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001632755?language=E&token=C4021334AE39841A5EEFF1D5B3B9C6E8"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001632755", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001632755/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1632755"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 19}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.06.2013"}, "SAPComponentKey": {"_label": "Component", "value": "BC-OP-AS4"}, "SAPComponentKeyText": {"_label": "Component", "value": "IBM AS/400"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Operating System Platforms", "value": "BC-OP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "IBM AS/400", "value": "BC-OP-AS4", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP-AS4*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1632755 - IBM i: Description of command APYSIDKRN"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>With the kernel 7.20, only the command APYSIDKRN is used to maintain the kernel. This requires a detailed description.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>AS/400, OS400, system i, i5OS, iSeries, APYR3KRN, APYSAP, LODR3KRN, LODSAPKRN, APYR3FIX, FIXR3OWNS</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You require information.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Case history:<br />In the past, various commands were available depending on the type of maintenance for a kernel (completely reload kernel, apply patches, synchronize ILE and PASE parts), even though their main objective was always to bring specifically packaged objects into an SAP system in such a way that they could be used by the operating system. In addition, splitting up an action into several commands was also prone to errors.<br />Therefore, we decided to simplify the maintenance of the kernel with the introduction of kernel 7.20 (see Note 1636252); to do this, the command APYSIDKRN was created. You were to be able to entirely process a maintenance action using one single call of APYSIDKRN, and all maintenance actions were to be possible using APYSIDKRN, without making the command interface too complicated. The commands for maintaining a kernel that were used previously (see above under the \"Other terms\" section) were omitted as a result.<br /><br />Background information:</p> <OL>1. The work directory of APYSIDKRN<br />In order to work correctly, APYSIDKRN requires its own infrastructure that is created during the first call of APYSIDKRN. This infrastructure consists of a separate work directory in the integrated file system (IFS). For each SAP system with the name &lt;SID&gt;, it is created once as /sapmnt/&lt;SID&gt;/patches and divided into several subdirectories.</OL> <UL><UL><LI>The subdirectory /work is a temporary work directory and servers to temporarily store data (such as unpacked SAR archives and auxiliary files) during the APYSIDKRN run. After the run of APYSIDKRN, the subdirectory should be empty again.</LI></UL></UL> <UL><UL><LI>The subdirectories /jvm, /nuc, and /uc are created by APYSIDKRN and can be used to download patches from SAP Service Marketplace.</LI></UL></UL> <UL><UL><LI>The subdirectory /log contains the logs of APYSIDKRN. They are created during each run of APYSIDKRN and are stored in this directory unless this is specified otherwise by the command interface. The name of the log begins with 'apysidkrn' and is followed by a time stamp of the APYSIDKRN run and the extension '.log'. As a result, the individual logs are created with different names and do not overwrite each other. Therefore, we recommend that you check this subdirectory occasionally and delete old logs if required. You can also use the command interface (see below: parameter LOGPATH) to specify a fixed log name in this directory (for example, '/sapmnt/&lt;SID&gt;/patches/log/APYSIDKRN.log') so that an old log is always overwritten and the number of logs in this directory remains small. In addition to the logs, this subdirectory also contains the file 'o4history.log'. Every time APYSIDKRN is called, this file stores all the SAR archives that were ever implemented. The name 'o4history.log' CANNOT be predefined as the log name externally.</LI></UL></UL> <UL><UL><LI>The subdirectory /saved contains those SAR archives in which the relevant status of this directory was archived by APYSIDKRN before or after the change of the current program directory of the system &lt;SID&gt; (see below: parameter SAVSAR). The names of these SAR archives are generated and contain a time stamp of the APYSIDKRN run. As a result, the backups do not overwrite each other. In this case, we also recommend that you check the size of this subdirectory occasionally and delete older backups. You can also use the command interface to specify a different directory for saving the current status.</LI></UL></UL> <OL>2. The program directory of the SAP system &lt;SID&gt;<br />The programs in the IFS in the directory /sapmnt/&lt;SID&gt;/exe are considered as the objects that are to be maintained primarily. This directory must be visible under the name /sapmnt/&lt;SID&gt;/exe for each logical partition (LPAR) in which parts of the SAP system &lt;SID&gt; run even if the directory is situated in an independent auxiliary storage pool (iASP) for example.</OL> <UL><UL><LI>Depending on the SAP application, the actual programs are in directories that are structured differently. For example, if you use SAP NetWeaver 7.0 or one of the Enhancement Packages that are based on it, the PASE programs are situated directly under /sapmnt/&lt;SID&gt;/exe if the system requires only Unicode programs or non-Unicode programs. Only for a dual-stack with an ABAP part in ASCII, the directory /sapmnt/&lt;SID&gt;/exe/uc exists that contains the Unicode programs of the SCS instance. In addition, if you have already changed to SAPJVM for the Java parts of the SAP system, you will find the Java programs in the directory /sapmnt/&lt;SID&gt;/exe/jvm/as400_pase_64. If you also use instances of the SAP system on Windows, the Windows Unicode programs are in /sapmnt/&lt;SID&gt;/exe/uc/ntamd64 and the Windows ASCII programs (non-Unicode programs) are in /sapmnt/&lt;SID&gt;/exe/nuc/ntamd64.</LI></UL></UL> <UL><UL><LI>However, if you use SAP NetWeaver 7.1 (PI) for example or one of the Enhancement Packages that are based on it, you will find the PASE programs only in the directories /sapmnt/&lt;SID&gt;/exe/uc/as400_pase_64 or /sapmnt/&lt;SID&gt;/exe/nuc/as400_pase_64. As a result, in SAP NetWeaver 7.1, the directory /sapmnt/&lt;SID&gt;/exe no longer contains any files but only the three (at the most) subdirectories /nuc (for non-Unicode programs, that is ASCII), /uc (for Unicode programs), and /jvm (for Java or SAP JVM). None of these subdirectories contains any files either, but they contain further subdirectories for the possible platforms: directory /as400_pase_64 for PASE/ILE, and also /ntamd64 for Windows objects and /linuxppc64 for programs that can run only on Linux on Power.</LI></UL></UL> <UL><UL><LI>The directory structure for which the PASE programs are directly in the directory /sapmnt/&lt;SID&gt;/exe is to be described as the flat type here. If the PASE programs are either in /sapmnt/&lt;SID&gt;/exe/uc/as400_pase_64 or /sapmnt/&lt;SID&gt;/exe/nuc/as400_pase_64, it is described as a hierarchical type. The directory type that is required by an SAP application depends on this application and is configured during the SAP installation; if you have to change the directory type due to an upgrade to a higher SAP release of the application, the SAP upgrade performs this automatically. Since every SAP application is aligned with its relevant program directory type, a manual change of the program directory type (for example, from flat to hierarchical) is not allowed.<br />APYSIDKRN can handle the two different directory systems as long as the structure is retained, that is, /sapmnt/&lt;SID&gt;/exe must contain at least one of the paths /nuc/as400_pase_64 or /uc/as400_pase_64 (hierarchical type) or neither of the two (flat type). For this reason also, you are NEVER allowed to change the directory structure manually.</LI></UL></UL> <UL><UL><LI>Caution: Since the directory /sapmnt/&lt;SID&gt;/exe is visible for each LPAR of the SAP system, every change in the directory is also directly visible in each LPAR.</LI></UL></UL> <UL><UL><LI>Warning: If your SAP system runs via a program directory of the flat type and if you have not changed to an instance-specific program directory with a program copy by SAPCPE (see Note 1632754), you can perform maintenance tasks in the system only when all of the instances have been shut down. The instances of the SAP system can continue to be active in parallel to the maintenance tasks only for the hierarchical type or for the flat type that is changed in SAPCPE.</LI></UL></UL> <OL>3. The program library of the SAP system &lt;SID&gt;<br />Previously, no kernel library was mentioned. The reason for this is that APYSIDKRN generally receives SAR archives as an input. APYSIDKRN stores the content of these SAR archives in its correct place in the IFS in /sapmnt/&lt;SID&gt;/exe. As a result, a kernel library is no longer required for the maintenance task. However, SAP will continue to deliver ILE objects that must exist in an ILE library (*LIB) at runtime. However, this also occurs via the IFS: All of the ILE objects delivered by SAP are first located in the IFS in the file ILE_TOOLS that is delivered in the packages SAPEXE.SAR for OS400 or also in the patch ILE. Every time the file ILE_TOOLS is changed in the IFS, APYSIDKRN unpacks the content of the file to the global runtime library SAP&lt;SID&gt;IND and prepares it for productive use by internally calling the command FIXSAPOWN that sets owners and authorizations. The name of the runtime library is predefined and you are not allowed to change it. Due to the problems that occur when maintaining objects of the type *MENU (see Note 1428151), APYSIDKRN does not copy the menu objects to SAP&lt;SID&gt;IND but to a menu library that is not blocked and that has the name SAP&lt;SID&gt;IND0 - SAP&lt;SID&gt;IND9. However, you cannot use APYSIDKRN to copy the content of a changed file ILE_TOOLS in all LPARs to each local SAP&lt;SID&gt;IND at the same time; this is performed in parallel to the running APYSIDKRN session in all other LPARs by the program SAPILED (see Note 1637588). This program is activated in every LPAR when SAPHOSTAGENT is installed (see Note 1031096). This ensures that the PASE and ILE parts can be active in their most recent version in each LPAR after a change is made. APYSIDKRN seals the library SAP&lt;SID&gt;IND that was created in this way; if you store objects in this library retroactively or perform other changes, it is automatically rebuilt by the SAPILED process in the background (refer to Note 1122239 to activate your own ILE objects). This library is always located in the system ASP. In a high availability environment, SAPILED ensures consistency in the global runtime library, and therefore it cannot be mirrored while the system is being mirrored.</OL> <UL><UL><LI>Note the following: If you use SAPCPE, and a separate runtime directory under each instance as a result, the program SAPSTART calls the program SAPCPE in the profile to automatically ensure that changed PASE programs are copied from the global directory to the local runtime directory when the instance is started. A local runtime library with the fixed name SAP&lt;SID&gt;I&lt;INSTNR&gt; in the system ASP then also belongs to this local directory. SAPSTART also updates this local runtime library when central ILE objects are changed. This update also takes place if the runtime library was changed without authorization since the last SAPCPE run. In support cases, you can shut down this 'repair' feature temporarily, in order to manually apply a debug patch or a diagnosis patch in the runtime library. To do this, create *DTAARA SUPPORT before you apply the patch in the runtime library. The diagnosis patch remains until you either delete *DTAARA SUPPORT or until SAPCPE wants to apply an official ILE.SAR patch.</LI></UL></UL> <UL><UL><LI>APYSIDKRN is not used when starting the instance. However, APYSIDKRN supports this process in such a way that the system creates the new runtime directory with the name /exe_ and the new runtime library SAP&lt;SID&gt;I&lt;INSTNR&gt;# according to the rules of SAPCPE for all local instances after you apply a patch. When you start the instance, the system renames these objects to the expected names /exe and SAP&lt;SID&gt;I&lt;INSTNR&gt;. As a result, the SAPCPE that is called afterwards no longer has to do anything. If instances are not local instances, SAPILED performs these preparatory actions.</LI></UL></UL> <OL>4. The SAR archives on SAP Service Marketplace<br />The elements that are required for importing the kernel or patch are available as SAR archives on SAP Service Marketplace under \"SAP Kernel\". There, a differentiation is made between 32-bit and 64-bit kernels and also between the character width (Unicode or non-Unicode). As a further subsection, a differentiation is made between the platform and under this, differentiation is made between database-independent archives and (for each database supported in the platform) database-dependent archives. The name of the archive is made from a template of the type &lt;name&gt;_&lt;plevel&gt;-&lt;uid&gt;.SAR. Here, &lt;name&gt; is the actual name of the archive, followed by the patch level &lt;plevel&gt; and a unique ID &lt;uid&gt; in which the bit value, the character width, the platform and the database type of the relevant archive are included again. Before you call APYSIDKRN, the archives to be imported must be downloaded into the IFS to the host on which you want to carry out the system maintenance. It can be any directory. However, for consistency reasons we recommend that you store the SAR archives in a directory that is to be created specifically for this under /sapmnt/&lt;SID&gt;/patches; for this, the subdirectories /jvm, /nuc or /uc are provided together with their relevant platform subdirectories. When you do this, you can retain the long names of the SAR archives; APYSIDKRN is able to process names of the type &lt;name&gt;_&lt;plevel&gt;-&lt;uid&gt;.SAR.<br /><B>Note: You cannot use APYSIDKRN to process the SAPHOSTAGENT patches. To do this, you must proceed in accordance with Note 1031096.</B><br />For further information about the download, see Note 19466.</OL> <OL>5. How APYSIDKRN works<br />The command APYSIDKRN must be called from within the user &lt;SID&gt;ADM. Therefore, the user &lt;SID&gt;ADM should log on for the maintenance activity. However, if you want to work under a different user, you must call 'CALL SAP&lt;SID&gt;IND/SAPINLPGM' to create the correct work environment.</OL> <UL><UL><LI>When APYSIDKRN is called, an attempt is made to obtain exclusive control over the maintenance process. Therefore, APYSIDKRN attempts to set a lock flag in the shared memory in the LPAR in which it runs. If this is not possible, the system issues a message such as \"Waiting for parallel patching of &lt;SID&gt; to end ...\", and you should check in the job log of your session whether the maintenance of the SAP system is currently locked by another user. Since this lock can only be set on the (virtual) host on which APSIDKRN is currently running, you must use organizational measures to ensure that maintenance tasks are always carried out on the same host.</LI></UL></UL> <UL><UL><LI>In principle, APYSIDKRN runs across instances for a system. Therefore, it does not require any information from the profiles of an instance. The only exception is the parameter DIR_CT_RUN that must be executed manually in all start profiles and instance profiles. In a system that has a program directory with a flat type, the parameter should be as follows:<br />DIR_CT_RUN = $(DIR_EXE_ROOT)/run (for an instance on IBM i)<br />DIR_CT_RUN = $(DIR_EXE_ROOT)\\run (for an instance on WIN)<br />For a program directory that has the hierarchical type, the entry is as follows:<br />DIR_CT_RUN = $(DIR_EXE_ROOT)/$(OS_UNICODE)/as400_pase_64 or<br />DIR_CT_RUN = $(DIR_EXE_ROOT)/$(OS_UNICODE)/linuxppc64 or<br />DIR_CT_RUN = $(DIR_EXE_ROOT)\\$(OS_UNICODE)\\ntamd64</LI></UL></UL> <UL><UL><LI>The input (see parameter ARCHIVES) first defines the list of the archives to be processed. The objects in these archives are unpacked in a separate subdirectory each in /sapmnt/&lt;SID&gt;/patches/work. After this, the attempt is made to find a manifest file in each archive (patches.mf, sapmanifest.mf ...) from which a description of the contents of each relevant package is taken. The patch level, the character width (ASCII or Unicode), and the platform (OS400 - Windows - Linux on Power) are of interest here. If no relevant manifest file can be found, the path in which the archive is stored is analyzed. If one of the subdirectories in the path is /jvm, /nuc or /uc, we assume that these are files for SAPJVM, ASCII or Unicode. If one of the subdirectories in the path also contains /as400_pase_64, /linuxppc64 or /ntmad64, the operating system OS400 or Linux on Power or Windows is used. If you then still cannot generate the information about platform or character width, APYSIDKRN terminates with an error message.</LI></UL></UL> <UL><UL><LI>The system now determines and reads the file patches.mf that has the latest creation date from all archives that are to be implemented and from the current runtime directories. This file contains a list of all of the kernel patches that have been delivered by SAP up to this point. The system searches for these patches in the program directory. If it does not find one of these patches, it marks it as log-relevant and outputs it later (see below). If SAP declared a group of patches as a bundle of patches that belong together and if at least one of these patches is in the system, the system checks whether all other parts of the bundle also exist or have to be applied. If this is not the case, APYSIDKRN terminates with a relevant error message. You must then download the patches that contain errors from SAP Service Marketplace and apply them along with the others. If one of the patches in the bundle is a SAPHOSTAGENT patch, you must apply this first using the special procedure that has been developed for it. If APYSIDKRN still terminates due to the patch ILE even though you have applied all patches that belong together, proceed as follows: Use the most recent version of the patch ILE, which you must create using SAP Note 1177123, before you call APYSIDKRN again.</LI></UL></UL> <UL><UL><LI>If one of the files to be implemented is file ILE_TOOLS, this means that the library SAP&lt;SID&gt;IND under which the current process runs will be rebuilt again. As a result, a new version of APYSIDKRN may also become active. To use this new version now already, the program behind APYSIDKRN will be extracted from ILE_TOOLS to QTEMP and called there directly to continue the APYSIDKRN process. The content of the previous exe directories are temporarily copied to /sapmnt/&lt;SID&gt;/exe_patches; in this temporary directory the new PASE programs are then unpacked. At the end of the process, the previous exe directory is renamed in exe$ and the directory exe_patches becomes the directory exe. The directory exe$ with the previous content from exe is not deleted, so that you can quickly return to the previous status by renaming the directories if there are problems with the newly created exe directory.</LI></UL></UL> <UL><UL><LI>If required, the JCE policy files are copied to /sapmnt/&lt;SID&gt;/patches/work (see below, parameter POLICYVERS). If required, the current content of the entire program directory is saved (see below, parameter SAVSAR). If a complete rebuild of the entire program directory was required (see below, parameter MODE), it is now emptied (this action does not take place for normal patch imports).</LI></UL></UL> <UL><UL><LI>In the next step, the archives that were previously unpacked in /sapmnt/&lt;SID&gt;/patches/work/... are now implemented, starting with the archive that has the lowest patch level in an ascending order up to the highest patch level. Depending on the type of the program directory, the programs are directly copied to /sapmnt/&lt;SID&gt;/exe (flat type) or stored in /sapmnt/&lt;SID&gt;/exe/nuc(uc,jvm)/as400_pase_64(ntamd64,linuxppc64) (hierarchical type). When you do this, note that the program objects outside the directory /jvm are overwritten due to new patches. For patches of SAPJVM, this is different: Here, a separate directory with the SAPJVM patch number in the name is created in the directory /sapmnt/&lt;SID&gt;/exe/jvm/&lt;platform&gt; for each SAPJVM patch. Each of these directories contains all of the SAPJVM programs. Therefore, we recommend that you check the content of the subdirectory /jvm occasionally and that you manually delete older versions of SAPJVM (DLTIFSDIR '/sapmnt/&lt;SID&gt;/exe/jvm/&lt;platform&gt;/sapjvm&lt;plevel&gt;') that are no longer required. <B>If the directory /jvm is too large, a backup of the entire program directory may no longer be possible due to the data volume.</B></LI></UL></UL> <UL><UL><LI>After this action, the JCE policy files that are to be retained are copied from the temporary backup to the subdirectory /jvm if required, and the new value of the parameter SAPJVM_VERSION is entered in the SAP profiles so that the SAP system uses the new version of SAPJVM from now on.</LI></UL></UL> <UL><UL><LI>As a result, the changes to the IFS are completed. APYSIDKRN now goes over the entire directory /sapmnt/&lt;SID&gt;/exe and sets the owners and authorizations in all objects to their correct value (this step can also be called outside of APYSIDKRN by calling the following command as QSECOFR: FIXSAPOWN &lt;SID&gt;).</LI></UL></UL> <UL><UL><LI>In the following step, the runtime library SAP&lt;SID&gt;IND is created again from the file ILE_TOOLS. However, this occurs only if this file exists in a new version in the IFS. In this case, the ILE objects are unpacked to QTEMP first and copied individually from there to SAP&lt;SID&gt;IND (except for the MENUs). The MENU objects are stored in the library SAP&lt;SID&gt;IND&lt;n&gt; that is currently not used (n = 0, ..., 9), and &lt;n&gt; is noted in SAP&lt;SID&gt;IND so that the corresponding SAP&lt;SID&gt;IND&lt;n&gt; can be transferred to the library list in addition to the library SAP&lt;SID&gt;IND during the next logon of &lt;SID&gt;ADM. After you set the owners and authorizations, the library SAP&lt;SID&gt;IND is sealed. This means that every change of SAP&lt;SID&gt;IND automatically leads to the rebuild of the owners and authorizations from now on.</LI></UL></UL> <UL><UL><LI>When you use APYSIDKRN, the system now uses the SAPCPE call from the profile to set up the new instance-specific runtime directory as /exe_ and the new ILE instance library as SAP&lt;SID&gt;I&lt;INSTNR&gt;# for all local instances. The next time you start the instance, the system renames these objects to the expected names /exe and SAP&lt;SID&gt;I&lt;INSTNR&gt;; as a result, starting an instance after you apply a patch runs as quickly as a normal start without a previous maintenance operation.</LI></UL></UL> <UL><UL><LI>As a result, the changes to the program directory and the runtime library are completed. If required when calling APYSIDKRN, the new status of the global program directory is now saved. Afterwards, the system uses the profiles to check whether the job SAPILED runs on every LPAR on which instances of the SAP system run to ensure that the required local actions can be executed here immediately. If no active SAPILED job can be found on an LPAR, the system writes a hint to the job log.</LI></UL></UL> <UL><UL><LI>SAP also delivers the content of the Info APAR in the file ILE_TOOLS (that is, the content that is current when SAP creates the file). If required, this Info APAR is copied to the specified place (see below, parameter UPDAPAR). If you keep the Info APAR up-to-date, you must always call APYSIDKRN using UPDAPAR(*NO). After this, a PTF check is performed using the specified Info APAR; possible deviations from the expected status are listed in the log and noted as \"Hint\" in the job log.</LI></UL></UL> <UL><UL><LI>If you want to update your current runtime environment to the most recent status (see below, parameter CHGENV), APYSIDKRN now recreates the environment by calling CALL SAP&lt;SID&gt;IND/SAPINLPGM.</LI></UL></UL> <UL><UL><LI>Finally, the system outputs the results of the check for patches that were possibly still missing, which it determined before: The system writes a note about missing corrections into the log and comments on a corresponding 'Hint' in the job log.</LI></UL></UL> <UL><UL><LI>APYSIDKRN ends when the APYSIDKRN work directory is cleaned up and when the lock for the process is released. We recommend that you check at least the last lines of the job log for error messages and \"Hints\"; if you are uncertain about anything, the overall log of the APYSIDKRN run can provide additional information.</LI></UL></UL> <UL><UL><LI>The programs R3trans, tp, and icmbnd are special cases. The program icmbnd is never overwritten but is always delivered under the name icmbnd.new. In the (rare) case in which you have to activate a corrected version (a relevant SAP Note must be available for this purpose), you can do so using the command WRKSAPPGMS.<br />When you use SAPCPE, corrections from the programs R3trans and tp are implemented in the global directory; however, they are required in the instance-specific program directory but you do not want to restart the instance for these programs. In this case, you can also use the command WRKSAPPGMS to transfer these programs to the instance without closing the instance.</LI></UL></UL> <OL>6. The command APYSIDKRN has the following parameters with the specified meaning:</OL> <UL><UL><LI>SID:<br />The command APYSIDKRN does not handle ILE objects or IFS parts (PASE programs) separately, but considers all objects (regardless of whether they are ILE or PASE) as a unit and processes them together instead. Therefore, the entire SAP system is considered as a unit to be maintained so that its name &lt;SID&gt; must also be included in this parameter.</LI></UL></UL> <UL><UL><LI>ARCHIVES:<br />Normally, you want to use maintenance to make an SAR archive (that was previously downloaded from SAP Service Marketplace to an IFS directory that is independent from the SAP system) available in the SAP system. The name (including the IFS path) of this SAR archive is expected to be in quotation marks in the parameter ARCHIVES. In order to be able to implement several archives at the same time, this parameter has the form of a list of up to 99 entries (SAR archives). All objects in the SAR archives of the list are implemented; in the case of objects that exist several times, the system implements the one with the highest patch level from the archive.<br />If you implement several archives from the same directory at the same time, you must specify the complete path only for the first archive of a directory; if an archive is specified in the list without specifying a path, APYSIDKRN copies the path description from the predecessor in the list. However, if the first archive in the list is also specified without the path, you must first use the command CHGCURDIR to set the current path. Otherwise, the results of the path search are undefined.<br />If all of the archives are in the same directory, it is also sufficient to specify this directory followed by '*': '/SAPpatches/download/*'. As a result, all of the objects of this directory with the extension .SAR are implicitly implemented. Archives in subdirectories of this directory are not taken into account (you can obtain the same result if you use CHGCURDIR to point towards the common directory and then specify only *CURRENT as the archive).<br />If you also want to implement all of the archives from all subdirectories, enter only the highest directory without '*': '/SAPpatches/download'. As a result, the system recursively searches for all of the objects with the extension .SAR from the specified directory including all subdirectories and implements these (you can obtain the same result if you use CHGCURDIR to point towards the highest directory and then specify only *ALL as the archive). This procedure is intended to first download the new patches into the highest directory (for example, '/SAPpatches/download') of SAP, to implement them with the extension '*' of the path, and then to move them to a subdirectory (for example, ''/SAPpatches/download/history' or '/SAPpatches/download/apply2011Feb29') in order to document the patch history. If required, you can specify the directory without the extension '*' to rebuild the program directory of the system from the complete history.<br />With the entry *NONE, you explicitly specify that no archive is to be implemented. This entry makes sense if you want to save only the current status of the program directory in the SAP system in combination with the parameter SAVSAR (see below).</LI></UL></UL> <UL><UL><LI>SAVSAR:<br />You can make an entry in this field to specify whether and how the current status of the program directory is to be saved before and/or after implementing the archives (see above, parameter ARCHIVES). If you do not require a backup, you must indicate this by using the entry *NONE.<br />A backup is normally created as an SAR archive in /sapmnt/&lt;SID&gt;/patches/saved if no other path is explicitly predefined. If you specify an ILE object of the type *FILE in an IFS notation (for example, '/QSYS.LIB/SAPPATCHES.LIB/SAVED.FILE'), you can force the system to save the backup in the specified ILE library. Note that such a library or such a file cannot be copied using CPYLIB or CPYF due to the volume of data in this *FILE object. To do this, you must always use SAVLIB or SAVOBJ and RSTLIB or RSTOBJ.<br />The possible entries in this parameter were described in detail in Note <B>1589608</B>. Therefore, the detailed description is not repeated here.</LI></UL></UL> <UL><UL><LI>LOGPATH:<br />By default, the results of the individual processing steps are noted in a log under APYSIDKRN. Normally (value *DEFAULT in this field), a log with the name 'apysidkrn&lt;time stamp&gt;.log' is stored in /sapmnt/&lt;SID&gt;/patches/log in this case, where &lt;time stamp&gt; is a ten-digit number that clearly refers to the start time of the APYSIDKRN process. This ensures that individual logs do not overwrite each other. However, the data volume continues to increase in the directory /log as a result so that we recommend that you remove older logs from this directory occasionally. You can also specify a path and a fixed log name to ensure that the system always writes only one log. In this case, however, an older log with the same name is overwritten. However, if you include a % character in the log name, the system uses the current time stamp at this point. As a result, you can predefine a separate path for the logs and a separate name (with a time stamp if required).<br />Note the following: If this field is empty or if the entry contains *NONE, no log is written. This means that, apart from the current job log, you have no other option to track the run of the command. Therefore, we recommend that you do not use this option.</LI></UL></UL> <UL><UL><LI>POLICYVERS (relevant only for SAPJVM patches):<br />Due to legal requirements, SAP delivers the \"JCE jurisdiction policy files\" in the variant \"strong but limited\" (see Note 1240081). This is sufficient for most applications. If you require the \"strong and unlimited\" version, you must download it from the manufacturer and copy it to the correct place in the subdirectory /jvm (possibly for each platform). If you now apply a new SAPJVM patch, all of the files of SAPJVM are delivered again. These also include the specified files, again in the variant \"strong but limited\". However, if you have implemented the variant \"strong and unlimited\" in an earlier SAPJVM version, you can specify the SAPJVM version under which you loaded the variant \"strong and unlimited\" to ensure that APYSIDKRN copies these files from there to ALL versions of the SAPJVM subdirectory /jvm. If you have several platforms in the directory /jvm, this specification applies to all existing platforms at the same time. Therefore, this procedure works only if you retained all versions in all platforms in the same way and if you evaluated the parameter SAPJVM_VERSION in all SAP profiles in the same way.<br />If you enter *LAST, the files are automatically retrieved from the directory /jvm with the highest version number; if you enter *CURRENT, the files are copied from the directory /jvm whose version number is in an SAP profile (therefore, all SAP profiles should have the same value for SAPJVM_VERSION). If you enter *NONE, nothing is copied. Possible adjustments must be implemented manually in this case. After this, it is usually required to set the authorizations using FIXSAPOWN SID(&lt;SID&gt;) under QSECOFR.<br />In addition, note that we recommend that you apply SAPJVM patches using the Java Support Package Manager (JSPM). To facilitate system maintenance, applying SAPJVM patches using APYSIDKRN will be supported as long as the actions that are executed by JSPM can also be reproduced by APYSIDKRN.</LI></UL></UL> <UL><UL><LI>MODE:<br />This parameter controls how the archives are to be implemented. Generally, *ADD is the predefined value. As a result, the objects are inserted from the archives under the parameter ARCHIVES (see above) into the program directory that already exists; when this occurs, objects that have the same name are overwritten. This is the standard procedure when applying a patch. You can enter *TEST to simulate applying patches such as MODE(*ADD) specified under the parameter ARCHIVES; when you do this, no changes occur in the program directory. However, you can use the lines that are noted in the log or in the job log to identify whether problems are to be expected when applying the patch or patches whose cause must be eliminated before you actually apply the patch.<br />*FULLY is also specified as an additional value. The only difference between this value and *ADD is that the program directory is completely emptied before it is filled. Similarly, the library SAP&lt;SID&gt;IND is emptied using CLRLIB. Therefore, you are allowed to enter this value only if you are certain that the archives that are specified under the parameter ARCHIVES cover an entire system. to make sure this is the case, APYSIDKRN compares the current content of the program directory with the content of the new programs specified under ARCHIVES; if program packages got lost in this process, the system would issue a warning from which the APYSIDKRN could still be terminated before any changes are made to the program directory.<br />Warning: There are even more values of this parameter; however, these are reserved for special calls of APYSIDKRN from SAP programs (for example, *TOOL). These values must never be transferred manually when the command APYSIDKRN is called.</LI></UL></UL> <UL><UL><LI>CHGENV:<br />If you have logged on as &lt;SID&gt;ADM, a runtime environment was created that corresponded to the status of the library SAP&lt;SID&gt;IND that existed before the command APYSIDKRN was called. After you apply a patch, the runtime environment that was created by SAP&lt;SID&gt;IND may have changed. As &lt;SID&gt;ADM, since you may continue to work with a new status of the runtime library SAP&lt;SID&gt;IND after you apply a patch, the runtime environment should be updated to the current status. If you enter *YES in this field, APYSIDKRN performs this rebuild of the runtime environment.<br />Note the following: You can obtain the same result manually by calling CALL PGM (SAP&lt;SID&gt;IND/SAPINLPGM).</LI></UL></UL> <UL><UL><LI>UPDAPAR:<br />When creating the archive SAPEXE.SAR or the patch ILE.SAR, SAP packs the current content of the Info APAR. Therefore, APYSIDKRN enables you to copy this Info APAR from SAP and to store it at a place to be specified by you (see below, parameter INFOAPAR). To do this, this parameter must be evaluated with *YES. After this, the parameter INFOAPAR is displayed and you must evaluate it correctly. Note that the default value of this parameter is *YES.<br />Note the following: In this case, the Info APAR is changed only if the content of the Info APAR in your system is older than the content from the new archive. This prevents the contents of your Info APAR that you stored (or were stored there every day by an automatic procedure configured by you) from being overwritten.</LI></UL></UL> <UL><UL><LI>INFOAPAR:<br />This parameter is visible only if the parameter UPDAPAR (see above) is evaluated with *YES. In this case, you must specify in this line the directory in which APYSIDKRN is to store the files of the Info APAR. Here, the Info APAR files are replaced only if the content that is implemented via the archives is more recent than the existing content.</LI></UL></UL> <UL><UL><LI>CARPATH (the display of the parameter is suppressed):<br />The PASE program SAPCAR is required to unpack the SAP archives. This program is transferred by APYSIDKRN and stored in /sapmnt/&lt;SID&gt;/patches/work from where it is used as a PASE program to unpack the archives (parameter value *BUILTIN). Therefore, you do generally not have to supply this field. You must make an entry here as required by SAP only if the program SAPCAR (that was delivered internally) works incorrectly.</LI></UL></UL> <UL><UL><LI>CRYPTODIR (the display of the parameter is suppressed):<br />When unpacking the archives, the program SAPCAR has the option to check whether a signature that may exist is from SAP and whether it is correct. To do this, the PASE-SAPCAR must use the \"SAP Cryptographic Library\" libsapcrypto.o (under PASE) with at least patch level 32. Therefore, it requires the information about where the \"SAP Cryptographic Library\" can be found. In general, the path to this PASE library is stored in the environment variable SECUDIR during the logon as &lt;SID&gt;ADM. If the parameter has the value *ENV (default value), the path specified under SECUDIR is used to search for this PASE library. Alternatively, PASE_PATH and PASE_LIBPATH are also used. However, you can also explicitly specify a path in this case. If it is not found or if the entry *NONE exists here, no check for the authenticity of the archives is carried out. The job log or the log contains the result of the authenticity check. If it was impossible to carry out the authenticity check, the archives are implemented. If the check was carried out, an archive is not implemented only if the signature is not an SAP signature or if it was withdrawn.</LI></UL></UL> <UL><UL><LI>ASP (the display of the parameter is suppressed):<br />This parameter is not evaluated (anymore) by APYSIDKRN; it only exists for compatibility reasons for older programs.</LI></UL></UL> <UL><UL><LI>ASPDEV (the display of the parameter is suppressed):<br />This parameter is not evaluated (anymore) by APYSIDKRN; it only exists for compatibility reasons for older programs.</LI></UL></UL> <UL><UL><LI>TOOLMODE (the display of the parameter is suppressed):<br />This parameter must always have the value *NONE; it must only be used internally by SAP programs with exactly defined other values.</LI></UL></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D042520)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001632755/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001632755/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001632755/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001632755/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001632755/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001632755/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001632755/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001632755/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001632755/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1770833", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Library time stamp comparison fails for APYSIDKRN", "RefUrl": "/notes/1770833"}, {"RefNumber": "1687173", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Collection of notes about the 7.20 kernel", "RefUrl": "/notes/1687173"}, {"RefNumber": "1683418", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: System maint. diffs between 7.20 and 4.6D to 7.11", "RefUrl": "/notes/1683418"}, {"RefNumber": "1656173", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Applying a diagnosis patch in an ILE library", "RefUrl": "/notes/1656173"}, {"RefNumber": "1637588", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Description of the program SAPILED", "RefUrl": "/notes/1637588"}, {"RefNumber": "1632754", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Changeover to instance-specific directory", "RefUrl": "/notes/1632754"}, {"RefNumber": "1589608", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Saving the programs after an action with APYSIDKRN", "RefUrl": "/notes/1589608"}, {"RefNumber": "1432807", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Applying a saved kernel using APYSIDKRN", "RefUrl": "/notes/1432807"}, {"RefNumber": "1428151", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Dealing w/ locked *MENU objects when applying patches", "RefUrl": "/notes/1428151"}, {"RefNumber": "1240081", "RefComponent": "BC-JVM", "RefTitle": "Java Cryptography Extension (JCE) Jurisdiction Policy Files", "RefUrl": "/notes/1240081"}, {"RefNumber": "1177123", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Reconstructing an ILE kernel library from SAR archive", "RefUrl": "/notes/1177123"}, {"RefNumber": "1122239", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: Adding another *LIB to a work process", "RefUrl": "/notes/1122239"}, {"RefNumber": "1097751", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Information and recommendations for kernel libraries", "RefUrl": "/notes/1097751"}, {"RefNumber": "1097637", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Copying an SAP kernel (kernel version 7.10/7.11/7.20)", "RefUrl": "/notes/1097637"}, {"RefNumber": "1078134", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Distribution of ILE and PASE system components", "RefUrl": "/notes/1078134"}, {"RefNumber": "1031096", "RefComponent": "BC-CCM-HAG", "RefTitle": "Installing Package SAPHOSTAGENT", "RefUrl": "/notes/1031096"}, {"RefNumber": "19466", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Downloading SAP kernel patches", "RefUrl": "/notes/19466"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2723619", "RefComponent": "BC-OP-AS4", "RefTitle": "Updating a Kernel on a High Availability System on IBM i", "RefUrl": "/notes/2723619 "}, {"RefNumber": "2494181", "RefComponent": "BC-DB-DB4", "RefTitle": "Choosing the correct kernel to upgrade with on IBM i", "RefUrl": "/notes/2494181 "}, {"RefNumber": "541508", "RefComponent": "BW-SYS-DB-DB4", "RefTitle": "IBM i: Checking the system parameters for BW", "RefUrl": "/notes/541508 "}, {"RefNumber": "800791", "RefComponent": "BC-INS-AS4", "RefTitle": "IBM i: In-place Code Page Conversion ASCII to Unicode", "RefUrl": "/notes/800791 "}, {"RefNumber": "1240081", "RefComponent": "BC-JVM", "RefTitle": "Java Cryptography Extension (JCE) Jurisdiction Policy Files", "RefUrl": "/notes/1240081 "}, {"RefNumber": "1031096", "RefComponent": "BC-CCM-HAG", "RefTitle": "Installing Package SAPHOSTAGENT", "RefUrl": "/notes/1031096 "}, {"RefNumber": "1097637", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Copying an SAP kernel (kernel version 7.10/7.11/7.20)", "RefUrl": "/notes/1097637 "}, {"RefNumber": "1589608", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Saving the programs after an action with APYSIDKRN", "RefUrl": "/notes/1589608 "}, {"RefNumber": "1177123", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Reconstructing an ILE kernel library from SAR archive", "RefUrl": "/notes/1177123 "}, {"RefNumber": "1078134", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Distribution of ILE and PASE system components", "RefUrl": "/notes/1078134 "}, {"RefNumber": "1632754", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Changeover to instance-specific directory", "RefUrl": "/notes/1632754 "}, {"RefNumber": "1770833", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Library time stamp comparison fails for APYSIDKRN", "RefUrl": "/notes/1770833 "}, {"RefNumber": "1428151", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Dealing w/ locked *MENU objects when applying patches", "RefUrl": "/notes/1428151 "}, {"RefNumber": "1432807", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Applying a saved kernel using APYSIDKRN", "RefUrl": "/notes/1432807 "}, {"RefNumber": "1683418", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: System maint. diffs between 7.20 and 4.6D to 7.11", "RefUrl": "/notes/1683418 "}, {"RefNumber": "1687173", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Collection of notes about the 7.20 kernel", "RefUrl": "/notes/1687173 "}, {"RefNumber": "1097751", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Information and recommendations for kernel libraries", "RefUrl": "/notes/1097751 "}, {"RefNumber": "1122239", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: Adding another *LIB to a work process", "RefUrl": "/notes/1122239 "}, {"RefNumber": "1637588", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Description of the program SAPILED", "RefUrl": "/notes/1637588 "}, {"RefNumber": "1656173", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Applying a diagnosis patch in an ILE library", "RefUrl": "/notes/1656173 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "KRNL32NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.42", "To": "7.42", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.22", "To": "7.22", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.22EXT", "To": "7.22EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.49", "To": "7.49", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.42", "To": "7.42", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.22", "To": "7.22", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.22EXT", "To": "7.22EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.49", "To": "7.49", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.53", "To": "7.53", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.73", "To": "7.73", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.21", "To": "7.22", "Subsequent": "X"}, {"SoftwareComponent": "KERNEL", "From": "7.42", "To": "7.42", "Subsequent": "X"}, {"SoftwareComponent": "KERNEL", "From": "7.45", "To": "7.45", "Subsequent": "X"}, {"SoftwareComponent": "KERNEL", "From": "7.49", "To": "7.49", "Subsequent": "X"}, {"SoftwareComponent": "KERNEL", "From": "7.53", "To": "7.53", "Subsequent": "X"}, {"SoftwareComponent": "KERNEL", "From": "7.73", "To": "7.73", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP KERNEL 7.41 64-BIT", "SupportPackage": "SP000", "SupportPackagePatch": "000010", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200023698&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.41 64-BIT UNICODE", "SupportPackage": "SP000", "SupportPackagePatch": "000010", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200023700&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP000", "SupportPackagePatch": "000311", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP000", "SupportPackagePatch": "000311", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP000", "SupportPackagePatch": "000311", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP000", "SupportPackagePatch": "000311", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.38 64-BIT", "SupportPackage": "SP000", "SupportPackagePatch": "000044", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200022123&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.38 64-BIT UNICODE", "SupportPackage": "SP000", "SupportPackagePatch": "000044", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67838200100200019649&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 EXT 64-BIT UC", "SupportPackage": "SP000", "SupportPackagePatch": "000616", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200013910&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 EXT 64-BIT", "SupportPackage": "SP000", "SupportPackagePatch": "000616", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200013911&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT", "SupportPackage": "SP000", "SupportPackagePatch": "000616", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013055&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT UNICODE", "SupportPackage": "SP000", "SupportPackagePatch": "000616", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013056&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.40 64-BIT", "SupportPackage": "SP000", "SupportPackagePatch": "000072", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200022526&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.40 64-BIT UNICODE", "SupportPackage": "SP000", "SupportPackagePatch": "000072", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67838200100200019652&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT UNICODE", "SupportPackage": "SP004", "SupportPackagePatch": "000004", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025031&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT", "SupportPackage": "SP004", "SupportPackagePatch": "000004", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025032&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.49 64-BIT UNICODE", "SupportPackage": "SP003", "SupportPackagePatch": "000003", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200004760&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.49 64-BIT UNICODE", "SupportPackage": "SP005", "SupportPackagePatch": "000005", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200004760&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.49 64-BIT UNICODE", "SupportPackage": "SP009", "SupportPackagePatch": "000009", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200004760&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.49 64-BIT UNICODE", "SupportPackage": "SP008", "SupportPackagePatch": "000008", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200004760&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.49 64-BIT UNICODE", "SupportPackage": "SP006", "SupportPackagePatch": "000006", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200004760&V=MAINT"}, {"SoftwareComponentVersion": "SAP HOST AGENT 7.21 EXT", "SupportPackage": "SP001", "SupportPackagePatch": "000001", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018474&support_package=SP001&patch_level=000001"}, {"SoftwareComponentVersion": "SAP HOST AGENT 7.21", "SupportPackage": "SP001", "SupportPackagePatch": "000001", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=67838200100200018473&support_package=SP001&patch_level=000001"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT UC", "SupportPackage": "", "SupportPackagePatch": "000122", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001798&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT", "SupportPackage": "", "SupportPackagePatch": "000122", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001797&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT", "SupportPackage": "", "SupportPackagePatch": "000122", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001793&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT UNICODE", "SupportPackage": "", "SupportPackagePatch": "000122", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001794&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "", "SupportPackagePatch": "000710", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "", "SupportPackagePatch": "000710", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "", "SupportPackagePatch": "000710", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "", "SupportPackagePatch": "000710", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.45 64-BIT UNICODE", "SupportPackage": "", "SupportPackagePatch": "000210", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200001710&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.45 64-BIT", "SupportPackage": "", "SupportPackagePatch": "000210", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200001751&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT UNICODE", "SupportPackage": "", "SupportPackagePatch": "000415", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025031&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT", "SupportPackage": "", "SupportPackagePatch": "000415", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025032&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.49 64-BIT UNICODE", "SupportPackage": "", "SupportPackagePatch": "000001", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200004760&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT", "SupportPackage": "SP129", "SupportPackagePatch": "000129", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001793&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT UNICODE", "SupportPackage": "SP129", "SupportPackagePatch": "000129", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001794&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT", "SupportPackage": "SP129", "SupportPackagePatch": "000129", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001797&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT UC", "SupportPackage": "SP129", "SupportPackagePatch": "000129", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001798&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.73 64-BIT UNICODE", "SupportPackage": "SP031", "SupportPackagePatch": "000031", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200007950&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.45 64-BIT UNICODE", "SupportPackage": "SP111", "SupportPackagePatch": "000111", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200001710&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.45 64-BIT", "SupportPackage": "SP111", "SupportPackagePatch": "000111", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200001751&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.45 64-BIT UNICODE", "SupportPackage": "SP211", "SupportPackagePatch": "000211", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200001710&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.45 64-BIT", "SupportPackage": "SP211", "SupportPackagePatch": "000211", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200001751&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT", "SupportPackage": "SP216", "SupportPackagePatch": "000216", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013053&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT UNICODE", "SupportPackage": "SP216", "SupportPackagePatch": "000216", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013054&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT", "SupportPackage": "SP216", "SupportPackagePatch": "000216", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013055&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT UNICODE", "SupportPackage": "SP216", "SupportPackagePatch": "000216", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013056&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.45 64-BIT UNICODE", "SupportPackage": "SP216", "SupportPackagePatch": "000216", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200001710&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.45 64-BIT", "SupportPackage": "SP216", "SupportPackagePatch": "000216", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200001751&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT UNICODE", "SupportPackage": "SP419", "SupportPackagePatch": "000419", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025031&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT", "SupportPackage": "SP419", "SupportPackagePatch": "000419", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025032&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT", "SupportPackage": "SP124", "SupportPackagePatch": "000124", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001793&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT UNICODE", "SupportPackage": "SP124", "SupportPackagePatch": "000124", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001794&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT", "SupportPackage": "SP124", "SupportPackagePatch": "000124", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001797&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT UC", "SupportPackage": "SP124", "SupportPackagePatch": "000124", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001798&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT", "SupportPackage": "SP311", "SupportPackagePatch": "000311", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT UNICODE", "SupportPackage": "SP311", "SupportPackagePatch": "000311", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP311", "SupportPackagePatch": "000311", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP311", "SupportPackagePatch": "000311", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT", "SupportPackage": "SP311", "SupportPackagePatch": "000311", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021283&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT UC", "SupportPackage": "SP311", "SupportPackagePatch": "000311", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021284&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP311", "SupportPackagePatch": "000311", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP311", "SupportPackagePatch": "000311", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.45 64-BIT UNICODE", "SupportPackage": "SP213", "SupportPackagePatch": "000213", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200001710&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.45 64-BIT", "SupportPackage": "SP213", "SupportPackagePatch": "000213", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200001751&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.41 64-BIT", "SupportPackage": "SP038", "SupportPackagePatch": "000038", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200023698&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.41 64-BIT UNICODE", "SupportPackage": "SP038", "SupportPackagePatch": "000038", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200023700&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT", "SupportPackage": "SP127", "SupportPackagePatch": "000127", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001793&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT UNICODE", "SupportPackage": "SP127", "SupportPackagePatch": "000127", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001794&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT", "SupportPackage": "SP127", "SupportPackagePatch": "000127", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001797&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT UC", "SupportPackage": "SP127", "SupportPackagePatch": "000127", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001798&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.53 64-BIT UNICODE", "SupportPackage": "SP318", "SupportPackagePatch": "000318", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200005858&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.53 64-BIT", "SupportPackage": "SP318", "SupportPackagePatch": "000318", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200006207&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT", "SupportPackage": "SP126", "SupportPackagePatch": "000126", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001793&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT UNICODE", "SupportPackage": "SP126", "SupportPackagePatch": "000126", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001794&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT", "SupportPackage": "SP126", "SupportPackagePatch": "000126", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001797&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT UC", "SupportPackage": "SP126", "SupportPackagePatch": "000126", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001798&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT UNICODE", "SupportPackage": "SP417", "SupportPackagePatch": "000417", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025031&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT", "SupportPackage": "SP417", "SupportPackagePatch": "000417", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025032&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP711", "SupportPackagePatch": "000711", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP711", "SupportPackagePatch": "000711", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP711", "SupportPackagePatch": "000711", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP711", "SupportPackagePatch": "000711", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP713", "SupportPackagePatch": "000713", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP713", "SupportPackagePatch": "000713", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP713", "SupportPackagePatch": "000713", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP713", "SupportPackagePatch": "000713", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.49 64-BIT UNICODE", "SupportPackage": "SP619", "SupportPackagePatch": "000619", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200004760&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.49 64-BIT", "SupportPackage": "SP619", "SupportPackagePatch": "000619", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200004791&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP712", "SupportPackagePatch": "000712", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP712", "SupportPackagePatch": "000712", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP712", "SupportPackagePatch": "000712", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP712", "SupportPackagePatch": "000712", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT", "SupportPackage": "SP719", "SupportPackagePatch": "000719", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001793&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT UNICODE", "SupportPackage": "SP719", "SupportPackagePatch": "000719", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001794&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT", "SupportPackage": "SP719", "SupportPackagePatch": "000719", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001797&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT UC", "SupportPackage": "SP719", "SupportPackagePatch": "000719", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001798&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT UNICODE", "SupportPackage": "SP422", "SupportPackagePatch": "000422", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025031&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT", "SupportPackage": "SP422", "SupportPackagePatch": "000422", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025032&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP714", "SupportPackagePatch": "000714", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP714", "SupportPackagePatch": "000714", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP714", "SupportPackagePatch": "000714", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP714", "SupportPackagePatch": "000714", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT UNICODE", "SupportPackage": "SP420", "SupportPackagePatch": "000420", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025031&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT", "SupportPackage": "SP420", "SupportPackagePatch": "000420", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025032&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP1200", "SupportPackagePatch": "001200", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP1200", "SupportPackagePatch": "001200", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP1200", "SupportPackagePatch": "001200", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP1200", "SupportPackagePatch": "001200", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.45 64-BIT UNICODE", "SupportPackage": "SP829", "SupportPackagePatch": "000829", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200001710&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.45 64-BIT", "SupportPackage": "SP829", "SupportPackagePatch": "000829", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200001751&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT UNICODE", "SupportPackage": "SP424", "SupportPackagePatch": "000424", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025031&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT", "SupportPackage": "SP424", "SupportPackagePatch": "000424", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025032&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.45 64-BIT", "SupportPackage": "SP201", "SupportPackagePatch": "000201", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200001751&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.45 64-BIT UNICODE", "SupportPackage": "SP201", "SupportPackagePatch": "000201", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200001710&V=MAINT"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2352906", "RefTitle": "IBM i: APYSIDKRN does not detect installed ILE Version", "RefUrl": "/notes/0002352906"}]}}}}}