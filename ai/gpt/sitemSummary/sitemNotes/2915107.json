{"Request": {"Number": "2915107", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 310, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001364642020"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=08DDD760D6BC6B80677DF3A504382278"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2915107"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 23}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.01.2024"}, "SAPComponentKey": {"_label": "Component", "value": "FI-RA-CP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Revenue Accounting Contract Processing"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Revenue Accounting", "value": "FI-RA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-RA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Revenue Accounting Contract Processing", "value": "FI-RA-CP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-RA-CP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2915107 - Revenue Accounting and Reporting with SAP S/4HANA 2020: Release Information Note"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This Release Information Note (RIN) contains information and references to notes for using Revenue Accounting and Reporting with SAP S/4HANA 2020.</p>\r\n<p>Starting with SAP S/4HANA 1809, the former Revenue Accounting and Reporting add-on, including SAP Sales Integration with SAP Revenue Accounting and Reporting 1.0, became an integral part of SAP S/4HANA.</p>\r\n<p>Support notes available up to the RAR 1.3 Support Package 12 are considered available in SAP S/4HANA 2020.</p>\r\n<p>Please note that some of the referenced notes below refer to RAR 1.3.&#160;Where these&#160;notes mention additional information, this information also applies to SAP S/4HANA 2020.</p>\r\n<p><strong>Note:</strong>&#160;This SAP Note is subject to change. Check this SAP Note for changes on a regular basis. All important changes are documented in section \"Important Changes after Release of SAP S/4HANA 2020\".</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p><span style=\"font-size: 14px;\">Revenue Accounting, SD RevRec, VF44,&#160;</span></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You want to use Revenue Accounting and Reporting with S/4HANA 2020.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This SAP Note covers the following topics:</p>\r\n<ul>\r\n<li><a target=\"_self\" href=\"#Important\">1. Important Information</a></li>\r\n<li><a target=\"_self\" href=\"#Activation\">2.&#160;Activation of Revenue Accounting and Reporting</a></li>\r\n<li><a target=\"_self\" href=\"#Landscape\">3.&#160;Technical System Landscape</a></li>\r\n<li><a target=\"_self\" href=\"#Important\">4.&#160;Important changes with S/4HANA</a></li>\r\n<li><a target=\"_self\" href=\"#Implementation_Information\">5.&#160;Implementation information after upgrade</a></li>\r\n<li><a target=\"_self\" href=\"#Important_Notes\">6.&#160;Important SAP Notes</a></li>\r\n<li><a target=\"_self\" href=\"#Optimized_RAR\">7.&#160;Comparison of Optimized and Classic Revenue Accounting</a></li>\r\n<li><a target=\"_self\" href=\"#OCM\">8.&#160;Classic Contract Management versus Optimized Contract Management</a></li>\r\n<li><a target=\"_self\" href=\"#InboundProcessing\">9.&#160;Classic Inbound Processing versus Optimized Inbound Processing</a></li>\r\n<li><a target=\"_self\" href=\"#Sizing\">10.&#160;Sizing Information for Optimized Contract Management and Inbound Processing</a></li>\r\n<li><a target=\"_self\" href=\"#FIORI\">11.&#160;FIORI applications with S/4HANA 2020</a></li>\r\n<li><a target=\"_self\" href=\"#Analytic\">12. Analytical applications with S/4HANA 2020</a></li>\r\n<li><a target=\"_self\" href=\"#application_help\">13.&#160;Application Help</a></li>\r\n<li><a target=\"_self\" href=\"#SD_REVREC\">14. SD Revenue Recognition and S/4HANA</a></li>\r\n<li><a target=\"_self\" href=\"#FPS1\">15.&#160;Additional Features with S/4HANA 2020 FPS1</a></li>\r\n</ul>\r\n<p><strong><a target=\"_blank\" name=\"Important\"></a>&#65279;<strong>1. Important Information</strong></strong></p>\r\n<p>To integrate with sender components such as SAP Sales and Distribution or other sender components, Revenue Accounting uses RFCs as part of the Inbound Processing configuration.</p>\r\n<p>For a proper integration, you have to either implement SAP Note <a target=\"_blank\" href=\"/notes/2957643\">2957643 - RFC Fast Serialization Error in compatibility mode</a>&#160;or you must <span style=\"text-decoration: underline;\"><strong>not</strong> </span>use RFC Destination NONE.</p>\r\n<p>Please install the note&#160;<a target=\"_blank\" href=\"/notes/3170627\">3170627 - Fatal message does not cancel all contracts in classic inbound processing</a><strong>&#160;</strong>to fix an important issue which may lead to data inconsistencies at RAI processing.</p>\r\n<p><strong><a target=\"_blank\" name=\"Activation\"></a>&#65279;2. Activation of Revenue Accounting and Reporting</strong></p>\r\n<p>Revenue Accounting and Reporting was subject to a release restriction&#160;(similar to the add-on early adopter care process).</p>\r\n<p>Depending on the support package you have applied, you may get&#160;information message FARR_FOUNDATION 301, which prevents the configuration of accounting principle information for Revenue Accounting.</p>\r\n<p>In the meanwhile, Revenue Accounting and Reporting can be implemented and used without restrictions. Please implement note 2675322. The correction in the note will remove the above mentioned message and unlock the configuration of accounting principle settings for Revenue Accounting.</p>\r\n<p><strong><a target=\"_blank\" name=\"Landscape\"></a>&#65279;3. &#65279;Technical System Landscape</strong></p>\r\n<p>As of SAP S/4HANA 1809, the former SAP Revenue Accounting and Reporting add-on has become an integral part of SAP S/4HANA. This relates to product version SAP REVENUE ACCOUNTING, including software component version REVREC.</p>\r\n<p>The Revenue Accounting and Reporting functionality still needs to be integrated into operational components&#160;that send order and billing information to Revenue Accounting. With SAP S/4HANA 1809, the following operational components, or products, support integration with Revenue Accounting:</p>\r\n<ul>\r\n<li>Sales and Distribution (SD)</li>\r\n<li>Billing and Revenue Innovation Management (BRIM), also known as SAP Hybris Billing</li>\r\n<li>SAP Customer Relationship Management (CRM)</li>\r\n<li>SAP S/4HANA Service</li>\r\n<li>External Sender Components</li>\r\n</ul>\r\n<p>For the integration with Sales and Distribution, the integration functionality previously deployed through the software component SAP Sales Integration with SAP Revenue Accounting and Reporting 1.0 (SAP SALES INTEGR SAP RAR 1.0) has also been added to the SAP S/4HANA 1809 stack and the S/4HANA releases that followed.</p>\r\n<p>For more information, especially on distributed system scenarios, please refer to the product assistance for S/4 HANA.</p>\r\n<p>Choose <a target=\"_blank\" href=\"https://help.sap.com/viewer/f63dd39a28bb4b90adbf9e608aff58ea/2020.000/en-US/ee17ae32442642c0af26386b7594c323.html\">Enterprise Business Applications -&gt; Finance -&gt; Accounting and Financial Close -&gt; Revenue Accounting and Reporting -&gt; Integration of Sender Components -&gt; Technical System Landscape</a></p>\r\n<p><strong><a target=\"_blank\" name=\"Important_Changes\"></a>&#65279;4. Important changes with S/4HANA</strong></p>\r\n<p>From SAP S/4HANA OP 1809 onwards, SAP changed the data types for the contract and performance obligation IDs from NUMC14 to CHAR14 and from NUMC16 to CHAR16 respectively. The change from NUMC to CHAR was done to simplify and optimize (performance) the internal handling of temporary IDs. No migration is required. Data will be migrated on the fly where neccessary. <span style=\"text-decoration: underline;\">Nevertheless, this change&#160;may have an impact on your custom code.</span> Please follow the guidelines in SAP Note&#160;<a target=\"_blank\" href=\"/notes/2672794\">2672794</a>.</p>\r\n<p><strong>Recommended Preparation for the upgrade to Revenue Accounting and Reporting in SAP S/4 HANA 2020<br /></strong></p>\r\n<p>The new field RAR_VERSION_CODE on contract level indicates whether the contract has been created in Contract Management Classic or in Contract Management. Starting with Release SAP S/4 HANA 2020, this new field is used for the determination of a contract version.</p>\r\n<p>By default, the new field&#160;is populated automatically after the upgrade to \"On Premise 2020\", making use of the Silent Data Migration infrastructure.&#160;You can&#160;already accelerate the phase of Silent Data Migration in Revenue Accounting and Reporting in SAP S/4HANA 1909 by implementing&#160;SAP Note&#160;<a target=\"_blank\" href=\"/notes/2842212\">2842212&#160;Introduction of new field RAR_VERSION_CODE in table FARR_D_CONTRACT</a>,&#160;before the upgrade to Revenue Accounting and Reporting SAP S/4HANA 2020. Then all new Revenue Accounting Contracts will already have the field correctly populated. As a result, you can already see in SAP S/4 HANA 1909 where the contract has been created.</p>\r\n<p><strong>Introduction of Optimized Inbound Processing</strong></p>\r\n<p>With SAP S/4 HANA 2020 the Optimized Inbound Processing is available (see chapter 9 below). The usage of the Optimized Inbound Processing is optional and the current Inbound Processing should work without disruption after the upgrade. However, as communication patterns were changed in order to support the Optimized Inbound Processing (e.g. from asynchronous to synchronous communication) and code was added to route the requests to either the classic or the Optimized Inbound Processing, there can be rare situations where the integration does now work as before. For example, RAIs will occur in the BGRFC Monitor instead of the RAI Monitor. To fix these (and related) issues please apply the following notes: 3100703,&#160; 3036164, 3065847, 3060298.</p>\r\n<p><strong><a target=\"_blank\" name=\"Implementation_Information\"></a>&#65279;5. Implementation Information after upgrade</strong></p>\r\n<p>A direct upgrade from Revenue Accounting and Reporting 1.2 (anyDB or S/4HANA) is not supported. If you had previously deployed the Revenue Accounting add-on, you will first have to upgrade the add-on to RAR 1.3. RAR 1.3 is generally available but is subject to a similar activation process as implemented in S/4HANA 1809,&#160;S/4HANA 1909, or S/4HANA 2020 (see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2750710\">2750710</a>).</p>\r\n<p>An upgrade from Revenue Accounting 1.3 on anyDB or an earlier S/4HANA to OP 2020 release follows the common upgrade process. There are no specific procedures in the case of Revenue Accounting. There are no changes requiring mandatory migration steps with regards to database tables are expected. The same applies to the user interface and process flows.</p>\r\n<p>Depending on the feature pack of RAR 1.3 you upgrade from, you may need to regenerate the RAI classes as new fields have been added:</p>\r\n<p>To regenerate the RAI classes, you must follow these steps:</p>\r\n<ul>\r\n<li>Start transaction FARR_RAI_CONF.</li>\r\n<li>Mark a Revenue Accounting Item Class.</li>\r\n<li>Press the Selected entries button.</li>\r\n</ul>\r\n<p>The system checks whether the configuration of the revenue accounting item class contains all fields that are available in the interface components.</p>\r\n<p>If none of the applied interface components were enhanced, it is not necessary to update the configuration and the configuration keeps the status active. In such a case, you can stop here and immediately proceed with the next revenue accounting item class.</p>\r\n<p>If an interface component was enhanced during the upgrade, the system detects this and automatically updates the configuration. In this case, the configuration of the revenue accounting item class sets the status to Modified. You must then follow these steps:</p>\r\n<ol>\r\n<li>Save the updated configuration of your revenue accounting item class.</li>\r\n<li>Activate the configuration. Afterwards you can see that the status of the configuration of the revenue accounting item class is active.</li>\r\n<li>Start transaction FARR_RAI_GEN by choosing Environment -&gt; Generation.</li>\r\n<li>Mark the updated revenue accounting item class and then press Generate.</li>\r\n<li>Choose Yes when you are asked whether you want to run the generation immediately.</li>\r\n<li>Choose No when you are asked whether you want to delete revenue accounting items.</li>\r\n<li>Repeat these steps for all revenue accounting item classes.</li>\r\n</ol>\r\n<p><strong>Note: </strong>You cannot delete revenue accounting items in production systems. You are therefore not asked whether you want to delete revenue accounting items that are available in production systems. Once this step is complete, you will receive a popup with the generated results. All status icons should be green.</p>\r\n<p><strong><a target=\"_blank\" name=\"Important_Notes\"></a>&#65279;6.&#160;Important SAP Notes</strong></p>\r\n<p>Please read the following SAP Notes before you start the implementation of SAP Revenue Accounting and Reporting as part of S/4HANA 2020.</p>\r\n<p>Make sure that you have the most up-to-date version of each SAP Note, which you can find on the SAP Support Portal at&#160;<a target=\"_blank\" href=\"https://support.sap.com/notes\">https://support.sap.com/notes</a>&#160;Information published on the SAP site.</p>\r\n<div class=\"myNNFV2-container\">\r\n<div class=\"myNNFV2-table-responsive\">\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"4\" cellspacing=\"2\" class=\"myNNFV2-table\" style=\"height: 80px; width: 929px;\"><colgroup> <col width=\"138\" /> <col span=\"2\" width=\"64\" /></colgroup>\r\n<tbody>\r\n<tr>\r\n<td height=\"19\" width=\"138\"><strong>SAP Component</strong></td>\r\n<td width=\"64\"><strong>Number</strong></td>\r\n<td width=\"64\"><strong>Title</strong></td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-CP</td>\r\n<td class=\"xl66\"><strong>2968072</strong></td>\r\n<td class=\"xl66\">Wrong attribute related to reconciliation key on non-distinct Performance Obligations at manual contract combination</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-CP</td>\r\n<td class=\"xl66\"><strong><strong>2962347</strong></strong></td>\r\n<td class=\"xl66\">Enhancement to manual contract combination by optimizing reconciliation keys algorithm</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-CP</td>\r\n<td class=\"xl66\"><strong><strong><strong>2974572</strong></strong></strong></td>\r\n<td class=\"xl66\">Validate Inbound Processing version in manual contract combination</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-CP</td>\r\n<td class=\"xl66\"><strong><strong><strong><strong>3142187</strong></strong></strong></strong></td>\r\n<td class=\"xl66\">Revenue is not recognized after revenue derecognition within the same fiscal period</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-INV</td>\r\n<td class=\"xl66\"><strong><strong><strong><strong><strong><a target=\"_blank\" href=\"/notes/3146039\">3146039</a></strong></strong></strong></strong></strong></td>\r\n<td class=\"xl66\">Invoice RAIs processed incorrectly</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-CP</td>\r\n<td class=\"xl66\"><strong><strong><strong><strong><strong>3284376</strong></strong></strong></strong></strong></td>\r\n<td class=\"xl66\">Fix endless loop in invoice processing due to invalid reconkey buffer</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-IP</td>\r\n<td class=\"xl66\"><strong><strong><strong><strong><strong><strong>3296936</strong></strong></strong></strong></strong></strong></td>\r\n<td class=\"xl66\">RA Contract Combination locking issue</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n</div>\r\n</div>\r\n<p><strong><a target=\"_blank\" name=\"Optimized_RAR\"></a>&#65279;7. Comparison of Optimized and Classic Revenue Accounting</strong></p>\r\n<div>\r\n<p>Starting&#160;from S/4HANA 1909, customers can use an&#160;optimized version of the Revenue Accounting contract&#160;management.&#160;Also in S/4HANA 2020, SAP has added an optimized&#160;version of inbound processing.&#160;The optimized contract management focuses on&#160;performance optimizations and additional features, such&#160;as day-based contract modifications.&#8203;&#160;The optimized inbound processing allows for real-time&#160;processing of operational documents.&#8203;</p>\r\n<p>For new implementations, SAP recommends to consider the optimized S/4HANA capabilities of contract management and inbound processing, as future investments will mainly focus on these new capabilities. If you have used the classic capabilities and wish to migrate to the optimized capabilities of S/4HANA, please refer to the existing migration documentation.</p>\r\n<p>By offering&#160;optimized versions, the existing contract management as known under Revenue&#160;Accounting 1.3 and S/4HANA, is now&#160;referred to as Contract Management (Classic). The&#160;inbound processing will be referred to as Inbound Processing (Classic).&#8203;</p>\r\n<p class=\"Paragraph SCXP49887199 BCX0\" lang=\"EN-GB\">In Customizing you can find the settings for the classic Contract Management and Inbound&#160;Processing under:&#160;&#8203;</p>\r\n<ul class=\"BulletListStyle1 SCXP49887199 BCX0\">\r\n<li class=\"OutlineElement Ltr SCXP49887199 BCX0\">\r\n<p class=\"Paragraph SCXP49887199 BCX0\" lang=\"EN-GB\">Revenue Accounting -&gt; Revenue Accounting Contracts (Classic)&#8203;</p>\r\n</li>\r\n<li class=\"OutlineElement Ltr SCXP49887199 BCX0\">\r\n<p class=\"Paragraph SCXP49887199 BCX0\" lang=\"EN-GB\">Revenue Accounting -&gt; Inbound Processing (Classic)</p>\r\n</li>\r\n</ul>\r\n<p class=\"Paragraph SCXP7985374 BCX0\" lang=\"EN-GB\">The Customizing for the Optimized Contract Management and Inbound Processing can be&#160;found&#160;under:&#160;&#8203;</p>\r\n<ul class=\"BulletListStyle1 SCXP7985374 BCX0\">\r\n<li class=\"OutlineElement Ltr SCXP7985374 BCX0\">\r\n<p class=\"Paragraph SCXP7985374 BCX0\" lang=\"EN-GB\">Revenue Accounting -&gt; Revenue Accounting Contracts&#8203;</p>\r\n</li>\r\n<li class=\"OutlineElement Ltr SCXP7985374 BCX0\">\r\n<p class=\"Paragraph SCXP7985374 BCX0\" lang=\"EN-GB\">Revenue Accounting -&gt; Inbound Processing</p>\r\n</li>\r\n</ul>\r\n<p>You can find more information for the optimized Revenue Accounting in the user assistance&#160;<a target=\"_blank\" class=\"Hyperlink SCXP262552847 BCX0\" href=\"https://help.sap.com/viewer/f63dd39a28bb4b90adbf9e608aff58ea/2020.000/en-US/c7ce61533be7ff4fe10000000a44176d.html\" rel=\"noreferrer\">here</a>.&#8203;</p>\r\n<p><strong><a target=\"_blank\" name=\"OCM\"></a>&#65279;8. Optimized and Classic Contract Management</strong></p>\r\n<p>To reduce the upgrade risk, optimized contract management will be introduced in parallel to the existing Contract Management (Classic).&#160;You can find additional information in the user assistance <a target=\"_blank\" href=\"https://help.sap.com/viewer/f63dd39a28bb4b90adbf9e608aff58ea/2020.000/en-US/4020cd6e8f82478b92c1edb5d6cb2f4a.html\">here</a>.</p>\r\n<p>The optimized contract management focuses on performance optimizations and additional features, such as day-based contract modifications. This requires some changes in the table structures of Revenue Accounting, but existing database tables and processes should remain stable. This is important for protecting&#160;the running processes and to reduce risk. To achieve better performance, coding had to be significantly reworked and simplified.&#160;Custom code needs to be adopted for new contracts if Contract Management (Classic) was already in use before.</p>\r\n<p>The optimized contract management can be released for new contracts based on a new contract category defined in customizing:&#160;Revenue Accounting -&gt; Revenue Accounting Contracts -&gt; Select Contract Management for Contract Categories.</p>\r\n<p>Contract Management (Classic) can continue to run for the existing contracts in parallel to the Optimized Contract Management that is used for new contracts. This&#160;allows for a step-by-step introduction of the new features.</p>\r\n<p>Alternatively, you can choose to migrate revenue contracts from the Contract Management (Classic) to the Optimized Contract Management. For more information about the migration process, please refer to the user assistance <a target=\"_blank\" href=\"https://help.sap.com/viewer/f63dd39a28bb4b90adbf9e608aff58ea/2020.000/en-US/9b41729dfecb48c99c2f8935f646d9e7.html\">here</a>.</p>\r\n<p>Optimized Contract Management can be combined with Classic and Optimized Inbound Processing.</p>\r\n<p><strong>8.1 Detailed Comparison</strong></p>\r\n<p>The following table provides a high-level overview of changes between classic and optimized contract management. Note that this table will be adjusted subsequently with new releases in S/4HANA.</p>\r\n<div class=\"myNNFV2-container\">\r\n<div class=\"myNNFV2-table-responsive\">\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"myNNFV2-table\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Functional Area</strong></td>\r\n<td>\r\n<p><strong>In scope with S/4HANA 2020 for</strong></p>\r\n<p><strong>Optimized Contract Management</strong></p>\r\n</td>\r\n<td>\r\n<p><strong>Out of scope with S/4HANA 2020&#160;</strong></p>\r\n<p><strong>(compared to existing RAR 1.3)</strong></p>\r\n</td>\r\n<td>\r\n<p><strong>Change to existing BAdIs or Customizing&#160;</strong></p>\r\n<p><strong>(compared to RAR 1.3)</strong></p>\r\n</td>\r\n<td><strong>Differences in System Behaviour</strong></td>\r\n</tr>\r\n<tr>\r\n<td>Data Modelling</td>\r\n<td>\r\n<div>\r\n<ul>\r\n<li>\r\n<div style=\"display: inline !important;\">POB attributes</div>\r\n</li>\r\n<li>Leading / Linked POBs</li>\r\n<li>POBs with hierarchies (like compound or BoM)</li>\r\n<li>POB with cost</li>\r\n</ul>\r\n</div>\r\n</td>\r\n<td>n/a</td>\r\n<td>See detailed information for Compound Groups and BoMs</td>\r\n<td>See detailed information for Compound Groups and BoMs</td>\r\n</tr>\r\n<tr>\r\n<td>Account Determination</td>\r\n<td>\r\n<p>All accounts for supported processes and related derivation rules in RAR 1.3</p>\r\n</td>\r\n<td>n/a</td>\r\n<td>n/a</td>\r\n<td>\r\n<p>Under RAR 1.3:</p>\r\n<p>Accounts are re-determined at each&#160;POB change</p>\r\n<p><br />Under Optimized Contract Management:</p>\r\n<p>Accounts are NOT re-determined at POB change but can be redetermined with the Account Redetermination functionality</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Allocation</td>\r\n<td>\r\n<ul>\r\n<li>\r\n<div style=\"display: inline !important;\">SSP determination</div>\r\n</li>\r\n<li>POB excluded from allocation</li>\r\n<li>Default implementation:</li>\r\n<ul>\r\n<li>SSP based allocation with SSP tolerances</li>\r\n<li>Residual allocation</li>\r\n</ul>\r\n</ul>\r\n</td>\r\n<td>Manual price allocation</td>\r\n<td>\r\n<p>Following RAR 1.3 BAdIs are not used:</p>\r\n<div>\r\n<ul>\r\n<li>FARR_BADI_ALLOCATION_ENGINE</li>\r\n<li>FARR_BADI_ALLOCATION_METHOD</li>\r\n</ul>\r\n</div>\r\n<div>\r\n<p>The following new BAdI is added:</p>\r\n<div>\r\n<ul>\r\n<li>FARR_BADI_PRICE_ALLOCATION</li>\r\n</ul>\r\n</div>\r\nIn the new BADI, only distinct POB, linked POB and compound group POB can be defined in the output. Billing elements and non-distinct POBs cannot be defined in the output.</div>\r\n<p>Already implemented customer logic has to be re-implemented</p>\r\n</td>\r\n<td>\r\n<p>Under RAR 1.3:</p>\r\n<p>Leading POB is first allocated with other POBs. The leading POB is then allocated with other linked POBs in a group</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Fulfillments</td>\r\n<td>\r\n<div class=\"O0\">\r\n<ul>\r\n<li>\r\n<div style=\"display: inline !important;\">Event-based POB with quantity-based fulfilment from a customer invoice, goods issue, release order, consumption or proof of delivery</div>\r\n</li>\r\n<li>Value-based fulfilments from customer invoice, release order or consumption</li>\r\n<li>Time-based POBs with start date type = 1 and 2.</li>\r\n<li>Deferral methods:</li>\r\n<ul>\r\n<li>1 (365/366 day per year)</li>\r\n<li>2 (360 day per year)</li>\r\n<li>S (12 periods based per year)</li>\r\n<li>F (First period only)</li>\r\n<li>L (Last period only)</li>\r\n</ul>\r\n<li>Manual PoC-based fulfilments</li>\r\n<li>Freeze/Unfreeze for time-based POB (details can be found here</li>\r\n</ul>\r\n</div>\r\n</td>\r\n<td>\r\n<div>\r\n<ul>\r\n<li>Time-based POB with start date type = 3</li>\r\n<li>POC based fulfilments with Result Analysis integration</li>\r\n<li>Deferral Method 3 (In Optimized Contract Management, it will have the same result as deferral method 2)</li>\r\n<li>Deferral Method 4</li>\r\n<li>Manual spreading and related API</li>\r\n</ul>\r\n</div>\r\n</td>\r\n<td>\r\n<p>Following RAR 1.3 BAdI is not used:</p>\r\n<ul>\r\n<li>FARR_BADI_DEFERRAL_METHOD</li>\r\n</ul>\r\n<p><br />Following New BAdI will be added:</p>\r\n<ul>\r\n<li>FARR_BADI_DEFERRAL_METHOD_V2</li>\r\n</ul>\r\n<p><br />Already implemented customer logic has to be re-implemented.</p>\r\n</td>\r\n<td>\r\n<p>Under RAR 1.3 fractions/nominator/denominator are used to present quantities per period.</p>\r\n<p>Optimized Contract Management works with days.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Contract change process (I)</p>\r\n</td>\r\n<td>\r\n<ul>\r\n<li>Day-based contract modification</li>\r\n<li>Determine change type</li>\r\n<li>Change reason from operational system:</li>\r\n<ul>\r\n<li>Prospective treatment: Unrecognized&#160;revenue will be re-allocated and no catch-up is calculated&#160;&#160;</li>\r\n<li>Retrospective treatment: All contractual prices will be re-allocated and revenue catch-up is calculated for already fulfilled quantities in the previous periods</li>\r\n</ul>\r\n</ul>\r\n</td>\r\n<td>&nbsp;</td>\r\n<td>\r\n<p>Following RAR 1.3 BAdIs are not used:</p>\r\n<div>\r\n<ul>\r\n<li>FARR_CHANGE_MODE_DETERMINATION</li>\r\n<li>FARR_BADI_TM_REMAINING_PERC</li>\r\n</ul>\r\n</div>\r\n<p>The following new BAdI has been added:</p>\r\n<div>\r\n<ul>\r\n<li>FARR_BADI_CHANGE_TYPE_DETN</li>\r\n</ul>\r\n</div>\r\n<p>Already implemented customer logic has to be re-implemented.<br /><br />(*) there is no need to reimplement FARR_BADI_TM_REMAINING_PERC as the effective date is introduced to calculate the remaining percentage.</p>\r\n<p>Changes are tracked in table&#160;FARR_D_POB_CTYPE (Performance Obligation Change Type).</p>\r\n<div></div>\r\n</td>\r\n<td>\r\n<p>Under RAR 1.3:</p>\r\n<div>\r\n<ul>\r\n<li>No day-based contract modification</li>\r\n<li>Change in FARR_D_CHG_TYPE is period-based</li>\r\n<li>Interface of BADI: Determine change type:</li>\r\n<ul>\r\n<li>Contract level change type</li>\r\n<li>Change type: P/R/'Empty'</li>\r\n<li>No change reason</li>\r\n</ul>\r\n<li>Default implementation: 50+ rules for retrospective and prospective.</li>\r\n<li>Standard logic may overwrite the result of the change type defined by customers</li>\r\n<li>Catch-up is always calculated together with recognized revenue</li>\r\n<li>Changes are tracked in table&#160;FARR_D_CHG_TYPE (Table of Change Type)</li>\r\n</ul>\r\n</div>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Contract change process (II)</p>\r\n</td>\r\n<td>\r\n<ul>\r\n<li>Manual Contract Combination</li>\r\n<li>Early Termination (details can be found&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/f63dd39a28bb4b90adbf9e608aff58ea/2020.000/en-US/4462c2cdb926441e84ca3944aa676058.html?q=early%20temination\">here</a>)</li>\r\n<li>POB Cancellation with Finalization Date</li>\r\n</ul>\r\n</td>\r\n<td>&nbsp;</td>\r\n<td>\r\n<p>Manual POB reassignment</p>\r\n</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Cost Recognition</p>\r\n</td>\r\n<td>\r\n<div class=\"O1\">\r\n<ul>\r\n<li>\r\n<div class=\"O1\" style=\"display: inline !important;\">Fulfillment cost (details can be found <a target=\"_blank\" href=\"https://help.sap.com/viewer/f63dd39a28bb4b90adbf9e608aff58ea/2020.000/en-US/4dcdba39994b41049c550ae997c4b9be.html\">here</a>)</div>\r\n</li>\r\n<li>Contract acquisition cost on contract level (details can be found <a target=\"_blank\" href=\"https://help.sap.com/viewer/f63dd39a28bb4b90adbf9e608aff58ea/2020.000/en-US/d12b5c6857374bdca2f1ba59a8cc5e80.html\">here</a>)</li>\r\n</ul>\r\n</div>\r\n</td>\r\n<td>\r\n<div>Contract acquisition cost on POB level</div>\r\n</td>\r\n<td>\r\n<ul>\r\n<li>The BADI FARR_BADI_COAC_DERIVE_TM_ATTR is not used.</li>\r\n</ul>\r\n</td>\r\n<td>\r\n<ul>\r\n<li>A new posting category &#8216;Accrued Cost&#8217; has been&#160;introduced and cost recognition is posted as follows:&#160; &#160; &#160;</li>\r\n</ul>\r\nDr. Recognized Cost<br />Cr. Accrued Cost<br />\r\n<ul>\r\n<li>Accrued cost and Deferred Cost related to fulfillment cost is offset with each other by program &#8216;Calculate Contract Liabilities and Contract Assets&#8217;.</li>\r\n</ul>\r\n<ul>\r\n<li>At the creation of the POB for contract acquisition cost,&#160;initial postings for accrued cost and deferred cost are created</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n</div>\r\n</div>\r\n</div>\r\n<p><strong>8.2 Compound Groups with Optimized Contract Management</strong></p>\r\n<p>Compound groups have been reworked in Optimized Contract Management. Further documentation&#160;can be found&#160;in the user assistance <a target=\"_blank\" href=\"https://help.sap.com/viewer/f63dd39a28bb4b90adbf9e608aff58ea/2020.000/en-US/576f5f3ad91f49e587ef5156fdaf9d6e.html\">here</a>.</p>\r\n<p>Optimized Contract Management is currently more restrictive than Classic Contract Management as it does not allow for the&#160; &#8220;Exclude from Allocation&#8221; flag&#160;to be set on the non-distinct POBs level&#160;in a compound group. This means, that&#160;Optimized Contract Management&#160;only allows either the whole compound group to be excluded from the allocation or none of it.</p>\r\n<p>In the Contract Management (Classic)&#160;there is the possibility to exclude some POBs of a compound group from allocation, while&#160;other POBs were not excluded&#160;(flag can be set on POB level within the compound group).</p>\r\n<p>For compound group determination in BRFplus, it is no longer allowed to assign several compound groups to the same POB.</p>\r\n<p>For time-based compound group POBs, all non-distinct POBs must be time-based as well and will be fulfilled with the same PoC as the compound group POB.</p>\r\n<p>For allocation of POBs within a compound group structure, the compound group POB is first allocated with other POBs outside the compound group and the allocated amount of the compound group POB is distributed to its non-distinct POBs accordingly there.</p>\r\n<p>The attributes of the compound group POB (I.e. the POB&#160; that represents the compound group) can be derived from attributes of its non-distinct POBs using BadI&#160;FARR_BADI_COMPND_GRP_POB_ATTR, like for example the start and end dates for time-based compound groups.</p>\r\n<p>BadI&#160;FARR_BADI_COMPOUND_FULFILLMENT, which was used to determine the fulfillment quantity of the compound group BadI, is no longer supported. In Optimized Contract Management, the compound group POB is fulfilled by the minimum PoC of its non-distinct POBs.</p>\r\n<p><strong>8.3 Bill of Material</strong></p>\r\n<p>Bill of material (BoM) has been reworked in optimized Contract Management. Further documentation can be found in the user assistance <a target=\"_blank\" href=\"https://help.sap.com/viewer/f63dd39a28bb4b90adbf9e608aff58ea/2020.000/en-US/add80a2e3a8d4a5b99ba65c37a9adaed.html\">here</a>.</p>\r\n<p>In Contract Management (Classic), the BoM is treated as a hierarchy in Revenue Accounting. In Optimized Contract Management, the BoM is treated as a sales BoM, which means that the BoM hierarchy will not be reflected in a corresponding POB hierarchy. Instead, all BoM items will be represented as top-level POBs. Nevertheless, the POBs can be grouped again using the compound group functionality.</p>\r\n<p>For example:</p>\r\n<ul class=\"ul\" id=\"loiod6f81ae82eb34a5aa8d9595a6e7d24c9__ul_pmf_sj5_3mb\">\r\n<li class=\"li\">\r\n<p class=\"p\">A company sells a&#160;batch of electronic hardware devices.</p>\r\n</li>\r\n<li class=\"li\">\r\n<p class=\"p\">Each sales order contains a BOM where the root item is a non-deliverable&#160;billing element&#160;&#8211; used to bill the whole order.</p>\r\n</li>\r\n<li class=\"li\">\r\n<p class=\"p\">The BOM child items are&#160;deliverable</p>\r\n</li>\r\n</ul>\r\n<p><span style=\"font-size: 14px;\">The Sales BoM looks as follows:</span></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" class=\"table\" frame=\"border\" id=\"loiod6f81ae82eb34a5aa8d9595a6e7d24c9__table_fyh_315_3mb\" rules=\"all\" summary=\"\">\r\n<thead class=\"thead\">\r\n<tr class=\"row\"><th class=\"entry\" id=\"d46832e68\">Sales Order Item</th><th class=\"entry\" id=\"d46832e71\">Billable</th><th class=\"entry\" id=\"d46832e74\">Deliverable</th><th class=\"entry\" id=\"d46832e80\">RAR-Relevant</th></tr>\r\n</thead>\r\n<tbody class=\"tbody\">\r\n<tr class=\"row\">\r\n<td class=\"entry\" headers=\"d46832e68 \">Root</td>\r\n<td class=\"entry\" headers=\"d46832e71 \">X</td>\r\n<td class=\"entry\" headers=\"d46832e74 \">&#160;</td>\r\n<td class=\"entry\" headers=\"d46832e80 \">X</td>\r\n</tr>\r\n<tr class=\"row\">\r\n<td class=\"entry\" headers=\"d46832e68 \">Hardware1</td>\r\n<td class=\"entry\" headers=\"d46832e71 \">&#160;</td>\r\n<td class=\"entry\" headers=\"d46832e74 \">X</td>\r\n<td class=\"entry\" headers=\"d46832e80 \">X</td>\r\n</tr>\r\n<tr class=\"row\">\r\n<td class=\"entry\" headers=\"d46832e68 \">Hardware2</td>\r\n<td class=\"entry\" headers=\"d46832e71 \"></td>\r\n<td class=\"entry\" headers=\"d46832e74 \">X</td>\r\n<td class=\"entry\" headers=\"d46832e80 \">X</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>If your business user decides that each hardware device is a distinct performance obligation, then the performance obligations in Revenue Accounting are as follows:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" class=\"table\" frame=\"border\" id=\"loiod6f81ae82eb34a5aa8d9595a6e7d24c9__table_fyh_315_3mb\" rules=\"all\" summary=\"\">\r\n<thead class=\"thead\">\r\n<tr class=\"row\"><th class=\"entry\" id=\"d46832e68\">Performance Obligation</th><th class=\"entry\" id=\"d46832e71\">POB Category</th><th class=\"entry\" id=\"d46832e74\">Distinct POB</th></tr>\r\n</thead>\r\n<tbody class=\"tbody\">\r\n<tr class=\"row\">\r\n<td class=\"entry\" headers=\"d46832e68 \">Root</td>\r\n<td class=\"entry\" headers=\"d46832e71 \">Billing Element</td>\r\n<td class=\"entry\" headers=\"d46832e74 \">Not relevant</td>\r\n</tr>\r\n<tr class=\"row\">\r\n<td class=\"entry\" headers=\"d46832e68 \">Hardware1</td>\r\n<td class=\"entry\" headers=\"d46832e71 \">Normal POB&#160;</td>\r\n<td class=\"entry\" headers=\"d46832e74 \">Distinct POB</td>\r\n</tr>\r\n<tr class=\"row\">\r\n<td class=\"entry\" headers=\"d46832e68 \">Hardware2</td>\r\n<td class=\"entry\" headers=\"d46832e71 \">Normal POB</td>\r\n<td class=\"entry\" headers=\"d46832e74 \">Distinct POB</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>If your business user decides that all hardware devices shall be grouped as a compound group structure, then the performance obligations in Revenue Accounting are as follows:</p>\r\n<p><strong>8.4 Conflict Handling</strong></p>\r\n<p>Conflict handling has been reworked in Optimized Contract Management. Further documentation&#160;can be found&#160;in the user assistance <a target=\"_blank\" href=\"https://help.sap.com/viewer/DRAFT/f63dd39a28bb4b90adbf9e608aff58ea/2020.000/en-US/2aca7d6afef0412e8aa1b84a44245a0d.html\">here</a>.</p>\r\n<p>In Optimized Contract Management, there is currently no Customizing&#160;for&#160;conflict&#160;handling (used&#160;to avoid&#160;conflicts).</p>\r\n<p>This means, there is no option to always keep manual changes or always&#160;overwrite&#160;manual changes automatically without any user interaction.&#160;The system always initially overwrites the manual changes.&#160;In the <em>Work List for Conflicted Contracts</em> application the conflicts need then be resolved by a conscious user decision.</p>\r\n<p><strong>8.5 Unsupported Functionality with Optimized Contract Management and Inbound Processing</strong></p>\r\n<p>Beside what was already mentioned before, the following functionality in Classic Contract Management&#160;is currently not supported in&#160;Optimized Contract Management:</p>\r\n<div>\r\n<div style=\"font-size: 14px;\">\r\n<ul>\r\n<li>\r\n<div style=\"font-size: 14px; display: inline !important;\">Migration from Legacy Systems (planned with feature pack)</div>\r\n</li>\r\n<li>Transition to new Accounting Principles</li>\r\n<li>Results Analysis Integration</li>\r\n<li>Simplified Invoicing</li>\r\n<li>Manual POB Reassignment between revenue contracts</li>\r\n<li>Create Contracts from SD Invoices</li>\r\n<li>Drop Shipment</li>\r\n<li>Intercompany Billing</li>\r\n<li>Condition-based Contract Acquisition Costs (Contract Acquisition Cost POBs are still supported)</li>\r\n<li>\r\n<div>Inactive condition types with zero amounts from Sales and Distribution (SD) can still be sent from integration component) to the optimized inbound processing when an active price condition is missing. For these condition types, you have to define the main condition flag in BadI&#160;FARRIC_BADI_ORDER (method ORDER_DATA_TO_ARL) since BadI FARR_BADI_RAI0 is not supported anymore in the optimized inbound processing.</div>\r\n</li>\r\n<li>Fixed exchange rate method</li>\r\n</ul>\r\n</div>\r\n</div>\r\n<p><strong>8.6 Unsupported Business Add-Ins&#160;with Optimized Contract Management and Inbound Processing</strong></p>\r\n<p>In addition to the BadIs&#160;already mentioned in the chapters above, compared to Classic Contract Management, the following Business Add-Ins are currently not supported in&#160;Optimized Contract Management:</p>\r\n<div>\r\n<ul>\r\n<li>FARR_BADI_CLEAR_DELDEFITM</li>\r\n<li>FARR_BADI_COAC_DERIVE_TM_ATTR</li>\r\n<li>FARR_BADI_COMPOUND_FULFILLMENT</li>\r\n<li>FARR_BADI_DERIVE_ACC_ASGMT_REF</li>\r\n<li>FARR_BADI_DERIVE_ACCT_ASSIGNMT</li>\r\n<li>FARR_BADI_DERIVE_VALUES</li>\r\n<li>FARR_BADI_DTMN_POB_DEF_METHOD</li>\r\n<li>FARR_BADI_EXTENDED_CHECK</li>\r\n<li>FARR_BADI_INV_CORR_ACCT_DERIV</li>\r\n<li>FARR_BADI_LOG_POB_DATA</li>\r\n<li>FARR_BADI_POB_CUST_VALIDATION</li>\r\n<li>FARR_BADI_SET_REVIEW_WORKLIST</li>\r\n</ul>\r\n</div>\r\n<p>Compared to Classic Inbound Processing, the following Business Add-Ins are currently not supported in&#160;Optimized Inbound Processing:</p>\r\n<div>\r\n<ul>\r\n<li>FARR_BADI_RAI0</li>\r\n<li>FARR_BADI_RAI2</li>\r\n<li>FARR_BADI_RAI4</li>\r\n<li>FARR_BADI_CONTRACT_COMBINATION</li>\r\n</ul>\r\n</div>\r\n<p>The BadIs are replaced with the following new BadIs:</p>\r\n<div>\r\n<ul>\r\n<li>FARR_BADI_ENRICH_ORDER_ITEM</li>\r\n<li>FARR_BADI_ENRICH_INV_ITEM</li>\r\n<li>FARR_BADI_CHECK_ORDER_ITEM</li>\r\n<li>FARR_BADI_CHECK_INV_ITEM</li>\r\n</ul>\r\n</div>\r\n<p>The contract combination BadI has been replaced by the order enrichment BadI where reference type and reference id can be used to combine order items into the same contract.</p>\r\n<p>For classic contract management, you can find the following information in the&#160;<a target=\"_blank\" href=\"/notes/2675360\">Release Information Note for Revenue Accounting and Reporting with S/4HANA 1809</a>:</p>\r\n<ul>\r\n<li>&#65279;Sizing Information</li>\r\n<li>&#65279;Inflight Checks</li>\r\n<li>&#65279;Data Validation Checks</li>\r\n<li>&#65279;Migration and Transition</li>\r\n<li>&#65279;Integrating with external sender components</li>\r\n<li>&#65279;Integrating with SAP Sales and Distribution (SD)</li>\r\n<li>&#65279;Security&#160;Information</li>\r\n<li>Standard Roles</li>\r\n<li>Standard Authorization Objects</li>\r\n<li>Additional relevant Authorization Objects</li>\r\n</ul>\r\n<p><strong><a target=\"_blank\" name=\"InboundProcessing\"></a>&#65279;9.&#160;Classic Inbound Processing versus Optimized Inbound Processing</strong></p>\r\n<p>Starting with S/4HANA 2020, customers can use an optimized version of the Revenue Accounting Inbound Processing.</p>\r\n<p>As a result, the existing Inbound Processing as known under Revenue Accounting 1.3 or up to S/4HANA 1909 will be referred to as Inbound Processing (Classic).</p>\r\n<p>In Customizing you can find the settings for the classic Inbound Processing under:&#160;Revenue Accounting -&gt;&#160;Inbound Processing (Classic)</p>\r\n<p>The Customizing for the optimized Inbound Processing you can find the settings under:&#160;Revenue Accounting -&gt;&#160;Inbound Processing.</p>\r\n<p>The optimized Inbound Processing offers a fixed&#160;database model that is based on&#160;basic plus sender&#160;component specific information. This means that structures, APIs, and runtime working&#160;structures previously&#160;generated under the classic Inbound Processing are now available as pre-define fixed structures, APIs, and runtime working structures&#160;respectively.&#8203;</p>\r\n<p>To avoid batch job processing that is present in classic Inbound Processing,&#160;the optimized Inbound&#160;Processing&#160;processes Revenue Accounting Items (RAIs) in real-time based on the Inbound Processing APIs.&#8203; This means the background jobs to transfer and process RAIs are not needed any longer. If an error occurs during processing, postponed RAIs&#160;will be created.</p>\r\n<p>Optimized Inbound Processing uses the RAI statuses&#160;Postponed&#160;and&#160;Processed;&#160;other statuses are not used.&#8203;</p>\r\n<p>Optimized Inbound Processing offers a new Fiori application&#160;Manage Postponed RAIs&#160;that&#160;provides the ability to process erroneous and partially processed RAIs (Postponed RAIs).&#8203;</p>\r\n<p>Due to&#160;real-time processing of revenue accounting items, editing of fields before processing is no longer possible in optimized Inbound&#160;Processing.</p>\r\n<p>Also it is not possible to create RAIs via the transaction FARR_RAI_SAMPLE for Optimized Inbound Processing.</p>\r\n<p>Optimized Inbound Processing can only be&#160;used with Optimized Contract Management.</p>\r\n<p>You can find additional information in the user assistance&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/f63dd39a28bb4b90adbf9e608aff58ea/2020.000/en-US/9da6da15cc8c4dae9cdbb0e115978308.html\">here</a>.</p>\r\n<p>You can also find more detailed information in the <a target=\"_blank\" href=\"/notes/3015027\">note </a>for Frequent Asked Question(FAQ) about CIP and OIP which covers the following questions:</p>\r\n<ul>\r\n<li>What are the advantages of Optimized Inbound Processing?</li>\r\n<li>Are all the features in Classic Inbound Processing available in the Optimized Inbound Processing?</li>\r\n<li>How is Optimized Inbound Processing activated?</li>\r\n<li>Can Optimized Inbound Processing and Classic Inbound Processing be used at the same time?</li>\r\n<li>Is it possible for a revenue contract created by Classic Inbound Processing to use Optimized Inbound Processing or vice versa?</li>\r\n<li>Is it possible to migrate from Classic Inbound Processing to Optimized Inbound Processing?</li>\r\n<li>If an external sender component is used such as a non-SAP system, how can it use the Optimized Inbound Processing?</li>\r\n<li>Does the FARR_RAI_SAMPLE work with Optimized Inbound Processing and Optimized Contract Management?</li>\r\n<li>How shall the BRF+ be adapted in case there is alreay BRF+ rules used for classic inbound processing?</li>\r\n</ul>\r\n<p>Please note, that for the integration with S/4HANA Service currently only the classic inbound processing is supported.</p>\r\n<p><strong>9.1 Derive Inbound Processing Version</strong></p>\r\n<p>Starting with S/4HANA 2020, Revenue Accounting and Reporting uses classic Inbound Processing and classic Contract Management as a default components.</p>\r\n<p>However, customers are provisioned to choose between classic Inbound processing and Optimized Inbound Processing for processing revenue accounting items using Business Addin \"Determination of Inbound Processing Version\" as per their needs.</p>\r\n<p>Preferably, for the net new customers, it is recommended to use optimized Inbound Processing and optimized Contract Management for the new implementations.</p>\r\n<p><strong>9.2 &#65279;Integrating with external sender components with the Optimized Inbound Processing</strong></p>\r\n<p>Starting with S/4HANA 2020, customers can make use of generic APIs function modules available with optimized Inbound Processing for integrating non-SAP operational systems to process operational documents to recognize revenue.</p>\r\n<p>Following are the four interface function modules for generic APIs, which can be directly called as they are RFC enabled:</p>\r\n<div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td valign=\"top\" width=\"312\">\r\n<p><strong>Revenue Accounting Item Type</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"312\">\r\n<p><strong>Interface Function Module</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"312\">\r\n<p>Order RAI</p>\r\n</td>\r\n<td valign=\"top\" width=\"312\">\r\n<p>FARR_INBOUND_ORDER_API&#8239;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"312\">\r\n<p>Fulfillment RAI</p>\r\n</td>\r\n<td valign=\"top\" width=\"312\">\r\n<p>FARR_INBOUND_FLFMT_API</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"312\">\r\n<p>Cost RAI</p>\r\n</td>\r\n<td valign=\"top\" width=\"312\">\r\n<p>FARR_INBOUND_COST_API</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"312\">\r\n<p>Invoice RAI</p>\r\n</td>\r\n<td valign=\"top\" width=\"312\">\r\n<p>FARR_INBOUND_INVOICE_API</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>You can find additional information in the user assistance&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/2fb06bab74ef457b9db56bfacb0b7318/2020.001/en-US/0fdcf8828f5e41a28f434a1678510fd4.html\">here</a></p>\r\n</div>\r\n<div></div>\r\n<div><strong><span style=\"font-size: 14px;\"><a target=\"_blank\" name=\"Sizing\"></a>&#65279;10. Sizing Information for Optimized Contract Management and Inbound Processing</span></strong></div>\r\n<div><span style=\"font-size: 14px;\">&#160;</span></div>\r\n<div><span style=\"font-size: 14px;\">Optimized Contract Management and Optimized Inbound Processing use a refactored version of the Revenue Accounting solution. As a result, updated sizing information is available.</span></div>\r\n<div><span style=\"font-size: 14px;\">The sizing guide is not yet available in the Quicksizer tool. In the meantime, SAP can provide you with a sizing guide in MS Excel format.&#160;</span></div>\r\n<div><span style=\"font-size: 14px;\">If you are interested in the sizing information, please create an incident on application component FI-RA-CP requesting provisioning of the sizing guide and you will be assigned to a pilot note providing the additional information.</span></div>\r\n<div></div>\r\n<div><strong style=\"font-size: 14px;\"><a target=\"_blank\" name=\"FIORI\"></a>&#65279;11. FIORI applications with S/4HANA 2020</strong></div>\r\n<p>Starting with S/4HANA 2020, the following FIORI apps are provided in combination with the optimized contract management. Please note that these FIORI applications work exclusively with optimized contract management only.</p>\r\n<div class=\"myNNFV2-container\">\r\n<div class=\"myNNFV2-table-responsive\">\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\" style=\"height: 338px; width: 633px;\">\r\n<tbody>\r\n<tr>\r\n<td valign=\"bottom\" width=\"54\">\r\n<p><strong>Fiori ID</strong></p>\r\n</td>\r\n<td valign=\"bottom\" width=\"343\">\r\n<p><strong>Fiori App Name</strong></p>\r\n</td>\r\n<td valign=\"bottom\" width=\"161\">\r\n<p><strong>Fiori Type</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"bottom\" width=\"54\">\r\n<p>F4270</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"343\">\r\n<p>Manage Postponed Revenue Accounting Items</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"161\">\r\n<p>Transactional</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"bottom\" width=\"54\">\r\n<p>F4272</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"343\">\r\n<p>Revenue Explanation</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"161\">\r\n<p>Transactional</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"bottom\" width=\"54\">\r\n<p>F4831</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"343\">\r\n<p>Revenue Catch-up</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"161\">\r\n<p>Transactional</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"bottom\" width=\"54\">\r\n<p>F3882</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"343\">\r\n<p>Revenue Schedule</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"161\">\r\n<p>Transactional</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"bottom\" width=\"54\">\r\n<p>F4273</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"343\">\r\n<p>Change History</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"161\">\r\n<p>Transactional</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"bottom\" width=\"54\">\r\n<p>F4423</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"343\">\r\n<p>Document Flow (Performance Obligation)</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"161\">\r\n<p>Transactional</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"bottom\" width=\"54\">\r\n<p>F4841</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"343\">\r\n<p>Run Business Reconciliation in Background</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"161\">\r\n<p>Transactional</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"bottom\" width=\"54\">\r\n<p>F4069</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"343\">\r\n<p>Combine Revenue Contracts</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"161\">\r\n<p>Transactional</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"bottom\" width=\"54\">\r\n<p>F4271</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"343\">\r\n<p>Work List for Conflicted Contracts</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"161\">\r\n<p>Transactional</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"bottom\" width=\"54\">\r\n<p>F4424</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"343\">\r\n<p>Allocated Amount Explanation</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"161\">\r\n<p>Transactional</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"bottom\" width=\"54\">\r\n<p>F4830</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"343\">\r\n<p>Business Reconciliation</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"161\">\r\n<p>Transactional</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"bottom\" width=\"54\">\r\n<p>F3881</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"343\">\r\n<p>Manual Fulfillment</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"161\">\r\n<p>Transactional</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"bottom\" width=\"54\">\r\n<p>F3883</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"343\">\r\n<p>Manage Revenue Contracts</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"161\">\r\n<p>Transactional</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"bottom\" width=\"54\">\r\n<p>F4067</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"343\">\r\n<p>Revenue Accounting Overview</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"161\">\r\n<p>Analytical</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"bottom\" width=\"54\">\r\n<p>F4308</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"343\">\r\n<p>Revenue Accounting Reuse Library for Utility</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"161\">\r\n<p>Reuse Component</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n</div>\r\n</div>\r\n<p>Starting with S/4HAHA 2020, the <em>F4102 Manage Performance Obligations (Transactional)</em> app is obsolete and replaced with the Manage Revenue Contracts app.</p>\r\n<p>You will need to activate and maintain your OData services in the frontend system. For this, you can use transaction /n/IWFND/MAINT_SERVICE.</p>\r\n<p>For the FIORI apps above, the following external services should be registered and linked to the system alias of your backend system.</p>\r\n<ul>\r\n<li>FARR_ALLOC_AMT_EXPLAIN_SRV</li>\r\n<li>FARR_BUSINESS_RECNCLN</li>\r\n<li>FARR_CHANGE_HISTORY</li>\r\n<li>FARR_COMBINE_CONTRACTS_SRV</li>\r\n<li>FARR_CONFLICTED_CONTRACT_WL_SRV</li>\r\n<li>FARR_CONTRACT_OVERVIEW</li>\r\n<li>FARR_DOCUMENT_FLOW_SRV</li>\r\n<li>FARR_MANAGE_POSTPONED_RAI_SRV</li>\r\n<li>FARR_MANUAL_FULFILLMENT_SRV</li>\r\n<li>FARR_OVERVIEW_PAGE_SRV</li>\r\n<li>FARR_REVENUE_EXPLANATION_SRV</li>\r\n<li>FARR_REVENUE_SCHEDULE</li>\r\n<li>FARR_SDB_REV_CATCHUP</li>\r\n</ul>\r\n<div class=\"WordSection1\">\r\n<p>For the <em>Manage Revenue Contracts</em> app, you will also need to activate and maintain the following OData service alias (Tc: /IWFND/MAINT_SERVICE) for notes, attachments, and&#160;derived profitablity segment&#160;functionalities:</p>\r\n</div>\r\n<ul>\r\n<li>SGBT_NTE_CDS_API_D_SRV</li>\r\n<li>SGBT_NTE_CDS_API_SRV</li>\r\n<li>CV_ATTACHMENT_SRV</li>\r\n<li>FCO_PROF_SEGMENT_SRV</li>\r\n</ul>\r\n<p>For the <em>Overview Page </em>app, you will also need to activate and maintain&#160;the following&#160;OData service alias (Tc: /IWFND/MAINT_SERVICE):</p>\r\n<ul>\r\n<li>C_NmbrOfRAContractsBySts_CDS</li>\r\n<li>C_RAGlobalFilterOvw_CDS</li>\r\n<li>C_RARecgdRevnByFlfmtType_CDS</li>\r\n<li>C_RARecgdRevnByQuarterOvw_CDS</li>\r\n<li>C_RARecgdRevnTrendOvw_CDS</li>\r\n</ul>\r\n<p>For the&#160;<em>Revenue Accounting Reuse Library for Utility </em>app<em>, </em>there is no Fiori user interface.&#160;This app&#160;is only used by other Fiori applications for navigation purposes. You do not need to maintain&#160;the OData service alias.&#160;However,&#160;you need to activate the service FIN_RARREUSELIB (Tc: SICF).</p>\r\n<p>In addition, you will need to generate the mapping of date and period information in the system as part of the general Financials setup.</p>\r\n<p>If you use the classic contract management, you will continue to use the existing WebDynpro applications for the revenue accoutant and SAPGUI transactions for administrative tasks such as the Revenue Accounting Item Monitor.</p>\r\n<p>To consume these UIs, you can either:</p>\r\n<ul>\r\n<li>Use the&#160; the SAP Business Client (aka NWBC) with the predefined roles outlined in the Release Information Note: 2675360 - Revenue Accounting and Reporting with SAP S/4HANA 1809: Release Information Note</li>\r\n<li>Use the FIORI Launchpad also for the SAPGUI and WebDynpro transactions (instead of NWBC). In this case, you will consume the existing transactions&#160;that have been visually harmonized (Visual Harmonization). Visual Harmonization means that each user can use the SAP Fiori Launchpad as their single entry point for all their work with SAP S/4HANA, and they have a harmonized visual user experience when calling up the various apps.</li>\r\n</ul>\r\n<p>This also helps adoption for customers migrating from the SAP Business Suite to SAP S/4HANA. It helps customers protect their investments, especially since all custom-built SAP GUI transactions and Web Dynpro ABAP apps automatically&#160;receive the new visual theme. This also allows you to take time with educating your users on new UIs.</p>\r\n<p><strong><a target=\"_blank\" name=\"Analytic\"></a>&#65279;12. Analytical applications with S/4HANA 2020</strong></p>\r\n<p>Starting with S/4HANA 2020, the following analytical applications are provided, either based on Web Dynpro Multi Grid or Design Studio. The apps can be used along with optimized contract management or classic contract management.</p>\r\n<div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\" style=\"height: 385px; width: 808px;\">\r\n<tbody>\r\n<tr>\r\n<td valign=\"bottom\" width=\"62\">\r\n<p><strong>Fiori ID</strong></p>\r\n</td>\r\n<td valign=\"bottom\" width=\"443\">\r\n<p><strong>Fiori App Name</strong></p>\r\n</td>\r\n<td valign=\"bottom\" width=\"206\">\r\n<p><strong>Fiori Type</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"bottom\" width=\"62\">\r\n<p>F4068</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"443\">\r\n<p>Disaggregation of Revenue (Design Studio)</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"206\">\r\n<p>Analytical, BW Query used</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"bottom\" width=\"62\">\r\n<p>F4269</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"443\">\r\n<p>Disaggregation of Recognized Revenue (Design Studio)</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"206\">\r\n<p>Analytical, BW Query used</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"bottom\" width=\"62\">\r\n<p>F4620</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"443\">\r\n<p>Contract Balance (Design Studio)</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"206\">\r\n<p>Analytical, BW Query used</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"bottom\" width=\"62\">\r\n<p>F4702</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"443\">\r\n<p>Remaining Performance Obligation - with Time Bands (Design Studio)</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"206\">\r\n<p>Analytical, BW Query used</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"bottom\" width=\"62\">\r\n<p>F4703</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"443\">\r\n<p>Remaining Performance Obligation (Design Studio)</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"206\">\r\n<p>Analytical, BW Query used</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"bottom\" width=\"62\">\r\n<p>F4956</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"443\">\r\n<p>Contract Balance Movements (Design Studio)</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"206\">\r\n<p>Analytical, BW Query used</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"bottom\" width=\"62\">\r\n<p>W0154</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"443\">\r\n<p>Contract Balance Movements</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"206\">\r\n<p>Web Dynpro, BW Query used</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"bottom\" width=\"62\">\r\n<p>W0155</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"443\">\r\n<p>Contract Balance</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"206\">\r\n<p>Web Dynpro, BW Query used</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"bottom\" width=\"62\">\r\n<p>W0156</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"443\">\r\n<p>Disaggregation of Recognized Revenue</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"206\">\r\n<p>Web Dynpro, BW Query used</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"bottom\" width=\"62\">\r\n<p>W0157</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"443\">\r\n<p>Disaggregation of Revenue</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"206\">\r\n<p>Web Dynpro, BW Query used</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"bottom\" width=\"62\">\r\n<p>W0158</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"443\">\r\n<p>Remaining Performance Obligation - with Time Bands</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"206\">\r\n<p>Web Dynpro, BW Query used</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"bottom\" width=\"62\">\r\n<p>W0159</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"443\">\r\n<p>Remaining Performance Obligation</p>\r\n</td>\r\n<td valign=\"bottom\" width=\"206\">\r\n<p>Web Dynpro, BW Query used</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n</div>\r\n<p>You can use the Core Data Services (CDS) views to create analytical reports&#160;that meet additional company disclosure requirements. You can find the information <a target=\"_blank\" href=\"https://help.sap.com/viewer/DRAFT/f63dd39a28bb4b90adbf9e608aff58ea/2020.000/en-US/00b11fc0cc5a44e98aa495b4137f6cab.html\">here</a>.</p>\r\n<p><strong><a target=\"_blank\" name=\"application_help\"></a>&#65279;13. Application Help</strong></p>\r\n<p>You can find the application help for Revenue Accounting in S/4HANA 2020 under&#160;<em>Enterprise Business Applications -&gt; Finance -&gt; Accounting and Financial Close -&gt; Revenue Accounting and Reporting</em>&#160;or directly&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/f63dd39a28bb4b90adbf9e608aff58ea/2020.000/en-US/c7ce61533be7ff4fe10000000a44176d.html\">here</a>.</p>\r\n<p>For the optimized contract management you can find the information&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/f63dd39a28bb4b90adbf9e608aff58ea/2020.000/en-US/4020cd6e8f82478b92c1edb5d6cb2f4a.html\">here</a>:</p>\r\n<p>Please note, that the information in the node <em>Contract Change After Contract Combination</em> is not valid for the optimized contract management and will be removed from the application help.</p>\r\n<p><strong><a target=\"_blank\" name=\"SD_REVREC\"></a>&#65279;14. SD Revenue Recognition and S/4HANA</strong></p>\r\n<div>\r\n<p>If you are currently on ECC using SD Revenue Recognition and want to upgrade to S/4HANA, during the upgrade, a SIC check will notify you that SD Revenue Recognition has been deprecated with S/4HANA. The SIC check has been implemented to make sure SAP's customers are aware of the fact that the SD Revenue Recognition solution as a whole is no longer available in the S/4HANA code line (S4CORE) and that the successor Revenue Accounting &amp; Reporting is available to continue even&#160;the legacy processes where they were left off.</p>\r\n<p>There are 2 options for you to migrate from SD Revenue Recongition to RAR:</p>\r\n<ul>\r\n<li>You can migrate to Classic Contract Management with Classic Inbound Processing.&#160;&#160;</li>\r\n<li>Or you can migrate to Optimized Contract Management with Optimized Inbound Processing.&#160;</li>\r\n</ul>\r\n<p>Please note that it is NOT POSSIBLE to migrate to Optimized Contract Management with Classic Inbound Processing.</p>\r\n<p>Some important references for the Sales Integration Component (REVRECSD Addon for ECC):</p>\r\n<p><a target=\"_blank\" href=\"https://me.sap.com/notes/2341717\">2341717</a>&#160;FAQ: Future of SD Revenue Recognition after IFRS15 is released</p>\r\n<p><a target=\"_blank\" href=\"https://me.sap.com/notes/2569950\">2569950</a>&#160;FAQ: Migration &amp; Operational Load in the SD Integration Component</p>\r\n<p><a target=\"_blank\" href=\"https://me.sap.com/notes/2703761\">2703761</a>&#160;SAP Notes for SD-BIL-RA</p>\r\n<p><a target=\"_blank\" href=\"https://me.sap.com/notes/2733866\">2733866</a>&#160;Migration Guide for SD Revenue Recognition to Revenue Accounting and Reporting. Please note that this migration guide is only valid if you want to migrate to 'Classic Contract Management'.</p>\r\n<p>As of S/4HANA 2020 FPS1 and S/4HANA 2021, with the migration capability of&#160;<strong>optimized</strong>&#160;inbound processing (OIP) and&#160;<strong>optimized</strong>&#160;contract management (OCM), you can migrate SD orders and SD contracts to Revenue Accounting, which have been used previously processed by SD Revenue Recognition.</p>\r\n<p>The migration process from SD Revenue Recognition can be performed after ECC has been updated to S/4HANA, as, throughout the migration, RAR uses the sales orders and contracts related to subsequent follow-up documents. They are still available after the upgrade to S/4HANA. Therefore, RAR OCM doesn&#8217;t rely on the SD Revenue Recognition programs and legacy data.</p>\r\n<p>The upgrade normally occurs as follows:</p>\r\n<ol start=\"1\">\r\n<li>Before you upgrade to S/4 HANA 2020 FPS1 or higher release, depending on whether you want to use&#160;<strong>optimized</strong>&#160;Inbound Processing and o<strong>ptimized</strong>&#160;Contract Management or not, the possibility of RAR configuration in ECC differs as follows:</li>\r\n<ul>\r\n<li>If you want to use&#160;<strong>classic&#160;</strong>Inbound Processing and&#160;<strong>classic&#160;</strong>Contract Management, you can already configure and setup the customizing of RAR in ECC before upgrade to S/4 HANA. And you can also use classic inbound processing and classic contract management in ECC. If past orders are needed, migration needs to be done in ECC before upgrade to S/4HANA</li>\r\n<li>If you want to use&#160;<strong>optimized</strong>&#160;Inbound Processing or o<strong>ptimized</strong>&#160;Contract Management, the configuration and setup of the customizing of RAR can only be performed after the upgrade to S/4 HANA is completed.&#160;Migration can be done after conversion, so the time lag between S/4HANA upgrade and migration needs to be taken into account in the plan</li>\r\n</ul>\r\n<li>ECC is upgraded to S/4 HANA 2020 FPS1 or higher release.</li>\r\n<li>After the upgrade is completed, the RAR setup in the new S/4 HANA environment differs as follows:</li>\r\n<ul>\r\n<li>If you want to still use&#160;<strong>classic&#160;</strong>Inbound Processing&#160;and&#160;<strong>classic&#160;</strong>Contract Management<strong>,&#160;</strong>you can continue using&#160;<strong>classic&#160;</strong>Inbound Processing&#160;and&#160;<strong>classic&#160;</strong>Contract Management<strong>&#160;</strong>after upgrade to S/4 HANA 2020 FPS or higher release.</li>\r\n<li>If you want to setup optimized Inbound Processing or optimized Contract Management, then you can now set up using&#160;<strong>optimized</strong>&#160;Inbound Processing and o<strong>ptimized</strong>&#160;Contract Management in your S/4 HANA environment.</li>\r\n</ul>\r\n<li>SD orders or contracts using SD Revenue Recognition processes that are open or partially completed, or processes where subsequent follow-on documents are expected (like credit memos, cancellations or return orders) are migrated to Revenue Accounting OCM on&#160;S/4 HANA 2020 FPS1 or higher using the standard operational load. This step is possible for the following combination:</li>\r\n</ol><ol start=\"4\">\r\n<ul>\r\n<li>classic Inbound processing&#160;and&#160;classic Contract management&#160;</li>\r\n<li>optimized&#160;Inbound Processing or optimized&#160;Contract Management</li>\r\n</ul>\r\n</ol><ol start=\"5\">\r\n<li>Please notice it is currently not possible to migrate your existing SD order or contracts to the combination of classic Inbound processing&#160;and&#160;classic Contract management</li>\r\n<li>The legacy data of SD Revenue Recognition is still available in tables relevant to SD Revenue Recognition and can be used to reconcile the results in Revenue Accounting.</li>\r\n</ol>\r\n<p>Some important references about the RAR solution (REVREC Addon for ECC):</p>\r\n<p><a target=\"_blank\" href=\"https://me.sap.com/notes/2582784\">2582784</a>&#160;Revenue Accounting and Reporting with SAP RAR 1.3 and S/4HANA 1809 - FAQ's and Guidance</p>\r\n<p><a target=\"_blank\" href=\"https://me.sap.com/notes/2656669\">2656669</a>&#160;Activation of Revenue Accounting for SAP S/4HANA 1809 and later</p>\r\n<p>You have to be aware that all SD Revenue Recognition transactions are blocklisted in S/4HANA, however you can still work on the related sales documents and follow-up documents in SAP Sales and Distribution.</p>\r\n</div>\r\n<div>\r\n<p><a target=\"_blank\" name=\"FPS1\"></a>&#65279;<strong>15.&#160;Additional Features with S/4HANA 2020 FPS1</strong></p>\r\n<p>With S/4HANA 2020 FPS1, the following notes provide access to <strong>additional features</strong> for Revenue Accounting. As a general recommendation, SAP advises to apply the support / feature package once it becomes available, rather than single notes:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"4\" cellspacing=\"2\" class=\"myNNFV2-table\">\r\n<tbody>\r\n<tr>\r\n<td width=\"64\"><strong>Number</strong></td>\r\n<td width=\"64\"><strong>Title</strong></td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2972292\">2972292&#160;</a></td>\r\n<td class=\"xl66\">Generic APIs for non-SAP Systems - Code changes</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2972335\">2972335&#160;</a></td>\r\n<td class=\"xl66\">Generic APIs for non-SAP Systems - DDIC changes</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2976260\">2976260</a></td>\r\n<td class=\"xl66\">Generic Invoice API : Include Invoice category - DDIC changes</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2976261\">2976261</a></td>\r\n<td class=\"xl66\">Generic Invoice API : Include Invoice category</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2989743\">2989743</a></td>\r\n<td class=\"xl66\">RAR IP: S4 service integration with RAR</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2990176\">2990176</a></td>\r\n<td class=\"xl66\">Assumed and Planned Invoices</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2981621\">2981621</a></td>\r\n<td class=\"xl66\">Migration from Operational Systems to Optimized Contract Management</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2989478\">2989478&#160;</a></td>\r\n<td class=\"xl66\">Adapt Posted Amount of Revenue Schedule to Support Initial Load</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2989519\">2989519</a></td>\r\n<td class=\"xl66\">Add Posted Amount for Initial Load in Disaggregation of Revenue</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2987832\">2987832</a></td>\r\n<td class=\"xl66\">Enhancement for productive cleanup report: support of Optimized Inbound Processing</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2985181\">2985181</a>&#8203;</td>\r\n<td class=\"xl66\">Adapt Fiori Application - Manage Revenue Contracts with Revenue Contracts Migrated from Classic Contract Management to Optimized Contract Management</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Please refer to consulting note <a target=\"_blank\" href=\"/notes/2972836\">2972836 </a>if you want to access&#160;Business Add-In(BAdIs) of Revenue Accounting and Reporting(RAR) in the Key User Tool.</p>\r\n<p>To understand the migration process from your legeacy systems to OCM, please refer to note&#160;<a target=\"_blank\" href=\"/notes/2981621\">2981621</a>&#160;which contains an attachment to describe that.</p>\r\n<p>In addition, the following <strong>program corrections</strong> and <strong>performance improvements&#160;</strong>have become available for optimized contract management and inbound processing:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"4\" cellspacing=\"2\" class=\"myNNFV2-table\">\r\n<tbody>\r\n<tr>\r\n<td width=\"64\"><strong>Number</strong></td>\r\n<td width=\"64\"><strong>Title</strong></td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/3027458\">3170627</a></td>\r\n<td class=\"xl66\">Fatal message does not cancel all contracts in classic inbound processing</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/3027458\">3100162&#160;</a></td>\r\n<td class=\"xl66\">Incorrect CX_FARR_FATAL_MESSAGE during price allocation</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/3027458\">3155702</a></td>\r\n<td class=\"xl66\">Invoice RAI processing: runtime error ASSERTION_FAILED</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2982238\">2982238</a></td>\r\n<td class=\"xl66\">RAR IP: Extension to support long timestamp</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2981109\">2981109</a></td>\r\n<td class=\"xl66\">Adapt Fiori Application Manage Revenue Contracts for Contracts Migrated from Classic Contract Management to Optimized Contract Management</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2981740\">2981740</a></td>\r\n<td class=\"xl66\">Revenue Schedule Support for Contract Migrated from Classic Contract Management to Optimized Contract Management</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2980585\">2980585</a></td>\r\n<td class=\"xl66\">Revenue Explanation Support for Contracts Migrated from Classic Contract Management to Optimized Contract Management</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2980585\">2960669</a></td>\r\n<td class=\"xl66\">Authorization Object Missed in Fiori Applications</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2966988\">2966988</a></td>\r\n<td class=\"xl66\">Blocked Business Partner for Fiori Applications</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2977585\">2977585&#160;</a></td>\r\n<td class=\"xl66\">Performance Enhancement for Fiori Application Manage Revenue Contracts</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2973384\">2973384</a></td>\r\n<td class=\"xl66\">RAR Inbound: Data provide Class performance optimization</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2990865\">2990865</a></td>\r\n<td class=\"xl66\">Performance Optimization for Contract Balance</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2962347\">2962347</a></td>\r\n<td class=\"xl66\">Enhancement to manual contract combination by optimizing reconciliation keys algorithm</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2968072\">2968072</a></td>\r\n<td class=\"xl66\">Wrong attribute related to reconciliation key on non-distinct Performance Obligations at manual contract combination</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2962181\">2962181</a></td>\r\n<td class=\"xl66\">Invoice Processing: Performance Improvement</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/3033006\">3033006</a></td>\r\n<td class=\"xl66\">Establish a Link between Fulfillments for Compound and Non-Distinct Performance Obligations - Backend Change</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/3028955\">3028955</a></td>\r\n<td class=\"xl66\">Establish a Link between Fulfillments for Compound and Non-Distinct Performance Obligations - UI Change for Revenue Explanation App</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/3027458\">3027458</a></td>\r\n<td class=\"xl66\">Determination of P+L account for Cost Condition Items by using new Inbound Processing</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n</div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SD-BIL-RA (SD Integration Revenue Accounting & Reporting)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON>k<PERSON> (D028561)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON> (D046671)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2990865", "RefComponent": "FI-FIO-RA", "RefTitle": "Performance Optimization for Contract Balance", "RefUrl": "/notes/2990865"}, {"RefNumber": "2990176", "RefComponent": "FI-RA-IP", "RefTitle": "Assumed and Planned Invoices", "RefUrl": "/notes/2990176"}, {"RefNumber": "2989743", "RefComponent": "FI-RA-IP", "RefTitle": "RAR IP: S4 service integration with RAR", "RefUrl": "/notes/2989743"}, {"RefNumber": "2989519", "RefComponent": "FI-FIO-RA", "RefTitle": "Add Posted Amount for Initial Load in Disaggregation of Revenue", "RefUrl": "/notes/2989519"}, {"RefNumber": "2989478", "RefComponent": "FI-FIO-RA", "RefTitle": "Adapt Posted Amount of Revenue Schedule to Support Initial Load", "RefUrl": "/notes/2989478"}, {"RefNumber": "2987832", "RefComponent": "FI-RA-CP", "RefTitle": "Enhancement for productive cleanup report: support of Optimized Inbound Processing", "RefUrl": "/notes/2987832"}, {"RefNumber": "2985181", "RefComponent": "FI-FIO-RA", "RefTitle": "Adapt Fiori Application - Manage Revenue Contracts with Revenue Contracts Migrated from Classic Contract Management to Optimized Contract Management", "RefUrl": "/notes/2985181"}, {"RefNumber": "2982238", "RefComponent": "FI-RA-IP", "RefTitle": "RAR IP: Extension to support long timestamp", "RefUrl": "/notes/2982238"}, {"RefNumber": "2981740", "RefComponent": "FI-FIO-RA", "RefTitle": "Revenue Schedule Support for Contract Migrated from Classic Contract Management to Optimized Contract Management", "RefUrl": "/notes/2981740"}, {"RefNumber": "2981621", "RefComponent": "FI-RA-CP", "RefTitle": "Migration from Operational Systems to Optimized Contract Management", "RefUrl": "/notes/2981621"}, {"RefNumber": "2981109", "RefComponent": "FI-FIO-RA", "RefTitle": "Adapt Fiori Application Manage Revenue Contracts for Contracts Migrated from Classic Contract Management to Optimized Contract Management", "RefUrl": "/notes/2981109"}, {"RefNumber": "2980585", "RefComponent": "FI-FIO-RA", "RefTitle": "Revenue Explanation Support for Contracts Migrated from Classic Contract Management to Optimized Contract Management", "RefUrl": "/notes/2980585"}, {"RefNumber": "2977585", "RefComponent": "FI-FIO-RA", "RefTitle": "Performance Enhancement for Fiori Application Manage Revenue Contracts", "RefUrl": "/notes/2977585"}, {"RefNumber": "2976261", "RefComponent": "FI-RA-IP", "RefTitle": "Generic Invoice API : Include Invoice category", "RefUrl": "/notes/2976261"}, {"RefNumber": "2976260", "RefComponent": "FI-RA-IP", "RefTitle": "Generic Invoice API : Include Invoice category - DDIC changes", "RefUrl": "/notes/2976260"}, {"RefNumber": "2973384", "RefComponent": "FI-RA-IP", "RefTitle": "RAR Inbound: Data provide Class performance optimization", "RefUrl": "/notes/2973384"}, {"RefNumber": "2972836", "RefComponent": "FI-RA-CP", "RefTitle": "RAR: Implement BAdIs using SE18 instead of Key User Tool", "RefUrl": "/notes/2972836"}, {"RefNumber": "2972335", "RefComponent": "FI-RA-IP", "RefTitle": "Generic APIs for non-SAP Systems (Enterprise Services) - DDIC changes", "RefUrl": "/notes/2972335"}, {"RefNumber": "2972292", "RefComponent": "FI-RA-IP", "RefTitle": "Generic APIs for non-SAP Systems (Enterprise Services) - Code changes", "RefUrl": "/notes/2972292"}, {"RefNumber": "2968072", "RefComponent": "FI-RA-CP", "RefTitle": "Wrong attribute related to reconciliation key on non-distinct Performance Obligations at manual contract combination", "RefUrl": "/notes/2968072"}, {"RefNumber": "2966988", "RefComponent": "FI-FIO-RA", "RefTitle": "Blocked Business Partner for Fiori Applications", "RefUrl": "/notes/2966988"}, {"RefNumber": "2962347", "RefComponent": "FI-RA-CP", "RefTitle": "Enhancement to manual contract combination by optimizing reconciliation keys algorithm", "RefUrl": "/notes/2962347"}, {"RefNumber": "2962181", "RefComponent": "FI-RA-CP", "RefTitle": "Invoice Processing: Performance Improvement", "RefUrl": "/notes/2962181"}, {"RefNumber": "2960669", "RefComponent": "FI-FIO-RA", "RefTitle": "Authorization Object Missed in Fiori Applications", "RefUrl": "/notes/2960669"}, {"RefNumber": "2675322", "RefComponent": "FI-RA-IP", "RefTitle": "Activation of Revenue Accounting for SAP S/4HANA 1809 and later", "RefUrl": "/notes/2675322"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2943206", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 2020: Restriction Note", "RefUrl": "/notes/2943206 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}