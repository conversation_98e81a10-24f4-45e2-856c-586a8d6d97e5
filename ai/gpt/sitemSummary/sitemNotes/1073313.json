{"Request": {"Number": "1073313", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 443, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016337972017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001073313?language=E&token=2B0BA4CF67DF5538FD9E9171C10CAD7B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001073313", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001073313/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1073313"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 13}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.04.2008"}, "SAPComponentKey": {"_label": "Component", "value": "XAP-EM-INT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Emissions Management Integration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Collaborative Cross Applications", "value": "XAP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XAP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Emissions Management (SAP xEM)", "value": "XAP-EM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XAP-EM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Emissions Management Integration", "value": "XAP-EM-INT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XAP-EM-INT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1073313 - xEM Integration (as of xEM 2.0 Support Package 9 and higher)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note contains information about the integration of xEM and ERP05.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Emissions Management,<br />plant safety,<br />compliance,<br />facility compliance,<br />SAP xEM,<br />SAP Environmental Compliance</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This note concerns the xEM installation (xEM 2.0 Support Package 9 or higher) and<br />IS-DFPS 610.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The SAP xEM \"Emissions Management\" was enhanced and is now the component for plant safety (compliance).<br />For the integration, you require DFPS 610.<br /><br />To be able to use the functions, you have to make settings for ERP and xEM.<br /><br /><B>ERP settings:</B><br /><B>Customizing: (transaction SPRO)</B><br /><br />In Customizing for plant maintenance, you can define new tabs. In the settings of the view profiles, the new tab should be communicated to plant safety for pieces of equipment and functional locations.<br /><br />To create the 'Compliance' tab, proceed as follows:<br />- Plant Maintenance and Customer Service<br />-- Technical Objects<br />--- General Data<br />---- Set View Profiles for Technical Objects<br />Select 'Screen group equipment data' and the corresponding profile.<br />Navigate to:<br />----- Activity and layout of views<br />Now enter screen '140', for example, with number 60; Description: Additional data; Tab active=X; Seq. no. ='140'.&#x00A0;&#x00A0;&#x00A0;&#x00A0;='140'.<br />Remark: There are four 'Seq. no.' columns. Use the first column. You enter screen number '140' here.<br /><br />Then navigate to: Icons and texts of views<br />Maintain the 'Compliance' tab title for your status number, for example, 60.<br />Maintain the view profiles for functional locations in the same way.<br /><br />Exit the view profile maintenance.<br /><br />Check the assignment of your view profile to the equipment category 'Functional Location'.<br />-- Functional Location<br />-- Equipment<br />to category, for example, to the category 'M'.<br />Then exit Customizing.<br /><br /><B><B>ABAP:</B></B><br />DFPS 610<br />Check whether the required switch is active.<br />The switch is realized using the implementation of the BAdI 'BADI_EM_INTEGRATION' of the enhancement spot 'XEM_INT'.<br />Use transaction SE18 to call the enhancement spot XEM_INT and use the BAdI 'BADI_EM_INTEGRATION' to branch to the implementation.<br />Check the method:<br />if_ex_badi_em_integration~is_active.<br />METHOD if_ex_badi_em_integration~is_active.<br />&#x00A0;&#x00A0;active = 'X'.<br />ENDMETHOD.<br /><br />If the BAdI is not yet implemented, use transaction SE19 to create an implementation for the enhancement spot 'XEM_INT'.<br />The BAdI 'BADI_EM_INTEGRATION' has an example implementation.<br />Implement this example implementation.<br /><br />In higher releases, this BAdI is also available in the Implementation Guide (IMG)<br />- Plant Maintenance and Customer Service<br />-- Integration Emissions Management<br />--- Badi: Activate integration with xApp Emissions Management<br />If the 'Active' indicator is not set, activate the implementation.<br /><br /><B>System connections</B><br /><br />Now set up the required system connections.<br />Set up the RFC connections (transaction SM59) for the xEM server.<br />Setting the HTTP connections for the external server:&#x00A0;&#x00A0;type 'G'<br />Create the following RFC connections: (name, description)<br />for use in the settings of the logical ports of the Web services.<br />RFC connection: EM_&lt;SID&gt;_&lt;Client&gt;<br />Description:&#x00A0;&#x00A0;'EM Emissions Management'<br /><br />On the 'Technical Settings' tab, maintain the target host and the port number.<br />To call the Web Dynpro to display the detailed data in xEM: RFC connection:<br />EM_DISP_DETAIL 'EM- Emissions Management - Display Detailed Data'<br /> or<br />EM_DISP_DETAIL_&lt;Client&gt; for the client-specific control<br />(Description:&#x00A0;&#x00A0;'EM- Emissions Management - Display Detailed Data')<br />On the 'Technical Settings' tab, maintain the target host and the port number. In the field 'Path Prefix', enter the following<br /><br />for xEM versions up to and including Support Package 10:<br />/webdynpro/dispatcher/technidata-ag.com/wd~startup~main/FacilityBuilder<br /><br />for xEM versions as of Support Package 11<br />/webdynpro/dispatcher/technidata.de/xem~facility~main~wd/FacilityBuilder<br /><br /><br /><br /><B>Maintaining logical ports for the Web services</B><br /><br />Transaction: lpconfig (client-specific setting)<br />Create a logical default port for all the proxy classes that are listed below. To do this, enter the proxy class or choose F4 and select the proxy class from the list. Under 'Logical Port', enter DEFAULT and also select the 'Default Port' field. Choose 'Create'.<br />On the 'Runtime' tab, select 'Web Service Infrastructure'. On the next tab 'Call Parameters', maintain the existing RFC connection EM_&lt;SID&gt;_&lt;Client&gt; under HTTP destination. Under path suffix, maintain the relevant value that is dependent on the proxy class.<br /><br />Proxy class&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Path suffix<br />CO_EM_FACILITY_GET_DET&#x00A0;&#x00A0; /EmFacilityGetDetail/Config1?style=document<br />CO_EM_FACILITY_GET_F4&#x00A0;&#x00A0;&#x00A0;&#x00A0;/EmFacilityGetF4/Config1?style=document<br />CO_EM_FACILITY_GET_LIST&#x00A0;&#x00A0;/EmFacilityGetList/Config1?style=document<br /><br />In addition, for the central system:<br />Proxy class&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Path suffix<br />CO_EM_FACILITY_SET_CHKLST /EmSDPChecklistImporter/Config1?style=document<br /><br />In addition, for a decentralized system:<br />Proxy class&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Path suffix<br />CO_EM_FACILITY_SET_DET&#x00A0;&#x00A0;&#x00A0;&#x00A0;/EmFacilitySetDetail/Config1?style=document<br /><br />The following error may occur in the ERP system:<br />SOAP:111 Unallowed RFC-XML Tag (SOAP_EINVALDOC)<br />If this is the case, check the settings for the logical ports.<br /><br /><B>xEM settings:</B><br /><br />For xEM, you must deploy the following Java Web services:<br /><br /><B>INFO </B>(As of Support Package 10, the Web services are no longer delivered separately in a note. Instead, they are delivered with Support Package 10 in the directory&#x00A0;&#x00A0;..Deployment\\NW2004\\Facility Compliance Web services. In this directory, the deployable object is: <B>technidata-ag.com~eappl~xem~emint.ear</B>. This means that the five Web services that are mentioned below are no longer deployed separately or are no longer required as of Support Package 10.)<br /><br /><B>--- only for Support Package 9:</B><br /> EmFacilityGetDetail<br /> Web service for querying detailed data for plants, used for distributing and displaying the data on PM tab or WM  report<br /><br /> EmFacilityGetF4<br />- Web service for the F4 search help in PM<br /><br /> EmFacilityGetList<br />- Web service that is required for the distribution<br /><br /> EMFacilitySetDetail<br />- Web service for posting plant data to decentralized systems (This Web service must be deployed only on decentralized systems. It is not required on the central xEM.).<br /><br /> EMSDPChecklistImporter<br />- Web service for importing checklist values on the central xEM<br />---<br /></p> <b></b><br /> <b></b><br /> <b>Activating some of the basic settings for plant safety in xEM</b><br /> <p><br />In total, you must make <B>nine </B>settings in the TDProperties:<br /><br />URL call for xEM versions up to and including Support Package 10:<br />http://&lt;host&gt;:&lt;port&gt;/webdynpro/dispatcher/technidata-ag.com/wd~startup~main/TDProperties<br /><br />URL call for xEM versions as of Support Package 11:<br />http://&lt;host&gt;:&lt;port&gt;/webdynpro/dispatcher/technidata.de/ecs~tdprop~exposed~wd/TDProperties<br /><br /><br />Create the following TDProperties or set the value as described below. (The values may exist, but have the value 'false'; the entries are case sensitive.)<br /><br />Application Name      Value <br />---------------------------------------------------------------<br /><br />XEM  facility.checkIntegration_TypeAssigned true<br /><br />XEM  facility.checkIntegrationIsValid  true<br /><br />XEM  facility.integration.WM_SDPStyle  true<br /><br />XEM  facility.pm_read_newStyle   true<br /><br />XEM  task.eam.notification.enable_advanced_bapi true<br /><br />XEM  task.eam.order.enable   true<br /><br />XEM  facility.ban_multiple_integration  true<br /><br />XEM  task.req_wizard.create_abstract  true<br /><br />XEM  permit.use_requirement_udfs   true<br /><br />As of xEM versions including Support Package 10:<br />Application: XEM<br />Name: task.checklistejob<br />Value: HOST=&lt;hostname&gt;:&lt;port&gt;;USER=&lt;username&gt;;PWD=&lt;password&gt;;FWD=&lt;forwarding email address&gt;<br /><br />You must set the specification for a Pop3 e-mail server for the placeholders.<br /><br />Once you have finished processing, choose 'Save'.<br />You can then close the browser.<br /><br /><br /><B><B>xEM RFC settings for the integration in</B></B><B>:</B><br /><br />WareHouse/LES Integration<br />Plant Maintenance Integration<br /><br />Available in the xEM note under: Administration (xEM 2.0 SP6) or Configuration/Infrastructure/RFC destinations (xEM 2.0 SP7+)<br /><br />To go to the xEM (xEM up to and including Support Package 10), use the following:<br />http://&lt;host&gt;:&lt;port&gt;/webdynpro/dispatcher/technidata-ag.com/wd~startup~main/XEM<br /><br />and for xEM versions as of Support Package 11:<br />http://&lt;host&gt;:&lt;port&gt;/webdynpro/dispatcher/technidata.de/ecs~menu~main~wd/XEM<br /><br />In the relevant dialog.<br /><br />A 'Connectable R/3 System' is defined here. You can assign several RFC types to a 'Connectable R/3 System'.<br /><br />For plant safety, the following types are required:<br /><br /> Type   Description<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;___&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;____________<br /><br /> * ALM Sync&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;--&gt; Query/assignment of      &#x00A0;&#x00A0; Equi/TP in the xEM integration.<br /><br /> * EAM Notification --&gt; Option of creating a maintenance (PM) notification<br /><br /> * WM Integration&#x00A0;&#x00A0; --&gt; Query/assignment of warehouse number, storage type,&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;storage section in the xEM Integration.<br /><br /> * EAM Order  &#x00A0;&#x00A0; --&gt; Option of creating a PM order.<br /><br /> * EAM SYNCHRONIZE&#x00A0;&#x00A0;--&gt; Query of the status of PM notifications and PM &#x00A0;&#x00A0; orders<br /><br /><B>Info</B>: As of xEM Support Package 10 PL8, the RFC type 'EAM SYNCHRONIZE' was implemented. This type is used by TaskStatusJob_EAM (background job). Note that the RFC connection, to which this RFC type is assigned, is not set up using Single Sign-On (SSO). All other RFC types can proceed using SSO, which means that you define two RFC entries for the same ERP in SAP xEM, whereby one is parametrized as SSO and all types other than 'EAM SYNCHRONIZE' are assigned there. You should set the second RFC entry as 'named user' (no SSO), so that the background job \"TaskStatusJOB_EAM\" obtains its connection to the ERP system.<br /><br /><br /><B><B>Scheduling periodic jobs in the xEM:</B></B><br /><br />To schedule periodic jobs in the xEM, the 'Cron Service' must be installed (details about the installation are available in the current xEM Installation Guide).<br /><br />To query a PM status, schedule a job in the xEM. Available in the xEM under: Configuration/Background processes<br />Active:&#x00A0;&#x00A0; (select)<br />Description:&#x00A0;&#x00A0;Query of the PM status<br />Cron Expression: 0 0/1 * * * ?<br /><br />URL for xEM versions up to and including Support Package 10: http://&lt;host&gt;:&lt;port&gt;/webdynpro/dispatcher/technidata-ag.com/wd~startup~main/TaskStatusJobEAM<br /><br />URL for xEM versions as of Support Package 11:<br />http://&lt;host&gt;:&lt;port&gt;/webdynpro/dispatcher/technidata.de/xem~taskexc~main~wd/TaskStatusJobEAM<br /><br /><B><B>Comment 1:</B></B><br />You can assign the RFC types only to one connection to the R/3. If you have already assigned an RFC type to a connection, the system no longer displays this RFC type in the dropdown list box of the elements that can be assigned.<br /><br /><B>Comment 2:</B><br />The user with which the RFC connection was parameterized must have the required authorizations in the R/3 system to create a PM order, for example.<br /><br /><B>Comment 3: </B><br />For the connection to the ERP, you can also use SSO in addition to the user and the password. To do this, you must activate the relevant indicator for SSO in the 'Connections to R/3' (as of Support Package 10).<br /><br /><B>Comment 4:</B><br /><br /> As of Support Package 9, there are no differences between the characteristic values<br />'xEM Emissions Management' and the characteristic value 'xEM Plant Safety' regarding initial data and so on.<br />However, you must ensure that the correct switch settings are used for the installation of the xEM system.<br />Important: As of Support Package 9, new UME roles are also delivered (EMRole.xml). These are roles for the xEM plant safety characteristic value. If an xEM user is in the plant safety role, this user also sees the GUI in the characteristic value plant safety. If the user is a member in both roles (emissions management and plant safety), this user must select the mode in the xEM Web Dynpro GUI before each action.&#x00A0;&#x00A0;--&gt; Note 1030645<br /><br />As of Support Package 10, you must also enter the following background processes:<br /><br />E-mail query for incoming e-mail checklists:<br /><br />Active: selected<br />Description: Checklist Task Scheduler<br />Cron Expression: 0 10 * * * ?<br /><br />URL for xEM Version Support Package 10:<br />http://&lt;host&gt;:&lt;port&gt;/webdynpro/dispatcher/technidata-ag.com/wd~startup~main/TaskStatusJobCheckliste<br /><br />URL for xEM versions as of Support Package 11:<br />http://&lt;host&gt;:&lt;port&gt;/webdynpro/dispatcher/technidata.de/xem~taskexc~main~wd/TaskStatusJobCheckliste<br /><br />Synchronization of the change history:<br /><br />Active: selected<br />Description: Change Document Transfer<br />Cron Expression: 0 0/30 * * * ?<br /><br />URL for xEM Version Support Package 10:<br />http://&lt;host&gt;:&lt;port&gt;/webdynpro/dispatcher/technidata-ag.com/wd~startup~main/ChangeDocumentTransfer<br /><br />URL for xEM versions as of Support Package 11:<br />http://&lt;host&gt;:&lt;port&gt;/webdynpro/dispatcher/technidata.de/ecs~persist~exposed~wd/ChangeDocumentTransfer<br /><br /><br /><B>Setting for branching from xEM to the PM iViews:</B><br /><br />For xEM versions up to and including Support Package 9, an authorized user can use the URL<br /><br />http://&lt;host&gt;:&lt;port&gt;/webdynpro/dispatcher/technidata-ag.com/wd~startup~main/TDProperties<br /><br />to branch to the settings. The setting for branching from the xEM PM tab to the iViews of the PM has the following description:<br />facility.pm_ext_ref_url<br />(it may be necessary to scroll in the table)<br /><br />Value to be entered:<br />http://&lt;erphost&gt;:&lt;Port&gt;/sap/bc/webdynpro/sap/mt_assetdetail_app/?sap-client=&lt;xxx&gt;&amp;sap-language=EN<br /><br />whereby &lt;xxx&gt; is to be replaced by client 004, for example:<br />--&gt;<br />http://&lt;erphost&gt;:&lt;Port&gt;/sap/bc/webdynpro/sap/mt_assetdetail_app/?sap-client=004&amp;sap-language=EN<br /><br />Comment: The language is set to EN (English) here.<br /><br />After you choose 'Save' in the TDProperties, you can restart the XEM in the browser. The settings have been transferred, and it should be possible to branch from the PM tab of the plant to the iViews of the PM if the xEM plant was assigned an equipment or functional location using the integration.<br /><br /><br /><B>TIP</B>: As of <B>Support Package 10</B>, the URL that is required to branch from the PM tab to the iViews is maintained in the xEM list of values (configuration, list of values).&#x00A0;&#x00A0;Below the node<br />'Plants', the entry 'Plant Safety' exists with the element<br />'External URL to R/3 iView'. In the details area of this object, enter the field <B>Code</B>with the URL (http://&lt;erphost&gt;:&lt;Port&gt;/sap/bc/webdynpro/sap/mt_assetdetail_app/?sap-client=&lt;xxx&gt;&amp;sap-language=EN).<br /><br /><br /><B>TIP:</B><br />If you are not sure what to enter for http://&lt;erphost&gt;:&lt;Port&gt;/sap/..., proceed as follows:<br />Log on to the ERP system.<br />- Call transaction SE80.<br />-- Package: RPLM_MT_UI<br />--- Web Dynpro - Web Dynpro Applicat. - MT_ASSETDETAIL_APP.<br /><br />Double-click MT_ASSETDETAIL_APP and go to the 'Properties' tab page. In the 'Administration Data' area (lower part of the tab page), the URL for the iViews is available. You must enter exactly this URL entry in the xEM as facility.pm_ext_ref_url. This URL is also enhanced as follows in the entry in the list of values:<br /><br />?sap-client=&lt;xxx&gt;&amp;sap-language=EN<br /><br />--&gt; whereby &lt;xxx&gt; is to be replaced by client 004, for example, and 'EN' is set as the language.<br /><br />A complete URL for the TDProperties (Support Package 9) or the entry in the list of values (Support Package 10) is as follows:<br /><br /><B>URL:</B><br />http://&lt;host&gt;:&lt;PORT&gt;/sap/bc/webdynpro/sap/mt_assetdetail_app?sap-client=&lt;SAP_CLIENT&gt;&amp;sap-language=EN<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;[Background information]<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The xEM adds the following two parameters to the above URL<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&amp; IV_OBJECT_KEY={ObjectKey}&amp;IV_OBJECT_TYPE={ObjectType}<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;whereby ObjectKey is the number of the equipment or the functional location, and ObjectType specifies whether an equipment or a functional location is involved (IFLOT / EQUI).<br /><br /><B>Setting for branching from xEM to the PM iViews:</B><br /><br />xEM Plant Management<br />-- Tab page 'PM Values', choose 'ERP iView':<br />In the ERP system, maintain the HTTP port of the ICM using a profile parameter:<br />icm/server_port_&lt;Ascending Number&gt;PROT=HTTP,PORT=&lt;Port number<br />for the HTTP port, default value is 8000&gt;,EXTBIND=1<br />for example:&#x00A0;&#x00A0;icm/server_port_0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; PROT=HTTP,PORT=8000,EXTBIND=1<br /><br />Check whether at least the following services were activated:<br />Transaction: SICF<br />/sap/public/bc<br />/sap/public/bc/ur<br />/sap/public/myssocntl<br />/sap/public/bc/webdynpro/ssr<br />/sap/bc/webdynpro/sap/mt_assetdetail_app<br /><br /><B>If you use SSO to log on to xEM- ERP, note the following:</B><br />The certificate of the Java stack is to be imported in the certificate list and the access control list (ACL) in transaction STRUSTSSO2 in the ABAP stack.<br /><br /><B>Setting for branching from xEM to the IH iViews:</B><br /><br />As of xEM Support package 12, you can integrate ERP work area information. For the branching from xEM to the IH iViews to be possible, the list of values under 'Plants' -&gt; 'Plant Safety' must contain the entry 'External URL for R/3 IH iView' with the following values.<br /><br />ID: PLS_FACILITY_IH_EXT_REF_URL<br />Code: &lt;Enter the URL for the ERP IH iView&gt;<br /><br /><br /><B>Configurations for the EAM tasks (Maintain order types and notification types in the xEM):</B><br />The integration allows you to create a PM notification and a PM order. For these objects, you must maintain the PM notification type and the PM order type in the list of values in the xEM.&#x00A0;&#x00A0;(These entries may be available in the list of values, but do not correspond to Customizing in ERP).<br /><br /><br />Location for the element in the list of values (LOV):<br /><br />/Tasks/PM order types or PM notification types&#x00A0;&#x00A0;(if these are not available, create PM order types or PM notification types, see the following lines)<br /><br /><B><B>PM order types: (settings in the list of values)</B></B><br /><br />Identifier of the element for PM order types:<br />--&gt; EAM_ORDER_TYPE<br /><br /><B>Info</B>: The element with the identifier may already be available in the system. You create the PM order types below this element.<br /><br />Creating an order type below the element 'PM order types' (in combination with control key):<br /><br />Order type and control key are separated by \"-\", for example,<br />Order type PM01&#x00A0;&#x00A0;control key FL02 becomes the<br />entry as identifier: PM01-FL02.<br /><br />Note the following: When you create the order type, you must use a minus (without blank characters) to separate it from the control key. (Do this in accordance with the specifications or existing order types and control keys of the ERP).<br /><br />You do not have to fill the remaining fields.<br />You must also set the German and the English display text to PM01-FL02. You can then select these order types with the display text for the task in a dropdown list box.<br /><br /><br />You can also maintain further order types.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;[Additional information]<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Handling in xEM source code, Support Package 10 patch level 8<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You must have '-' in the PM order type. --&gt; Otherwise, the EAM Order Type is not displayed correctly.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The entry 'PM01' returns a zero as both the order type and&#x00A0;&#x00A0;the control key<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The entry 'PM01-' returns the order type PM01 and \"\" as the control key (interpreted as not set)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;'PM01 key' returns the order type 'PM01' and the control key 'Key'<br /><br /><br /><B><B>PM notification types: (settings in the list of values)</B></B><br /><br />Below the element in the list of values path<br />/Tasks/PM notification types with the identifier EAM_NOTIF_TYPE, you can maintain notification types.<br /><br />For new notification types, you must set the value for the identifier that is available and required in ERP Customizing, for example, M1 as the identifier. You must also set the German and the English display text to M1, for example.<br />You can also maintain further notification types.<br /><br /><B>Note the following: Where are the order types and control keys that are maintained in the ERP?</B><br /><br />SPRO - IMG<br /><br />- Plant Maintenance and Customer Service<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- Maintenance and Service Processing<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- Maintenance and Service Orders<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- Functions and Settings of Order Types<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- Control Key<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- Default Values for Control Key<br />Maintaining order types<br /><br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Possible errors when you create PM oders from SAP xEM<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If, when you create a PM order, the following error message is displayed:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;' .. Item data not found ..', you should check whether a checklist description is maintained as a classifier in xEM.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;.. Header data incomplete ...<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;--&gt; Customizing in ERP does not correspond or the work center and so on may not be maintained<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;.. Planning plant xx does not support order type yy --&gt; the order type does not correspond to the plant.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Check Customizing.<br /><br /><br /><B>Checklist scenario</B><br /><br />The scenario supports the generation of a checklist from the maintenance order. You can also transfer the content of the filled-in checklist to the xEM using the function 'Import'.<br />You must create the maintenance order in the xEM with reference to an equipment or a functional location. You must create the checklist as a template.<br />&#x00A0;&#x00A0;(ADS, Adobe document services).<br />We deliver an example (/ISDFPS/EM_CHKLST_ORD_SAMPLE).<br />Technically, the functions are attached to the maintenance order using the Generic Object Services (GOS).<br />Maintain the view 'sgosattr' to use the GOS.<br />Transaction: SM30, enter 'sgosattr'.<br />Maintain (choose 'Details') the next service for<br />/ISDFPS/CP<br />Next service&#x00A0;&#x00A0;/ISDFPS/PDF<br />Choose 'Save'.<br />Maintain the new entry:<br />Name of the service:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Description:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Quick info<br />/ISDFPS/PDF&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PDF Form&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PDF Form<br />Service Type:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Service List (F4 help)<br />Icon:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ICON_PDF<br />Subservice:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/ISDFPS/PDFG<br /><br />Save your entry. Then, maintain the next entry:<br /><br />Name:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Description:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Quick info<br />/ISDFPS/PDFG Generate PDF Form&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Generate PDF Form<br />Class f.Gen.Service:&#x00A0;&#x00A0;/ISDFPS/CL_PM_GOS_SRV_PDF_GEN<br />Service Type:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Single Service (F4 help)<br />Icon&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; ICON_PDF<br />Next service&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; /ISDFPS/PDFI<br /><br />Save your entry. Then, maintain the next entry:<br /><br />Name:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Description:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Quick info<br />/ISDFPS/PDFI Import PDF Form&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Import PDF Form<br />Class f.Gen.Service:&#x00A0;&#x00A0;/ISDFPS/CL_PM_GOS_SRV_PDF_IMP<br />Service Type:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Single Service (F4 help)<br />Icon&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; ICON_PDF<br />Next service&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; /ISDFPS/PDFL<br /><br />Save your entry. Then, maintain the next entry:<br /><br />Name:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Description:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Quick info<br />/ISDFPS/PDFL Overview PDF Forms&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Overview PDF Forms<br />Class f.Gen.Service:&#x00A0;&#x00A0; /ISDFPS/CL_PM_GOS_SRV_PDF_LIST<br />Service Type:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Single Service (F4 help)<br />Icon&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; ICON_PDF<br />Save your entry.<br /><br />To check whether the GOS are callable, call<br />transaction IW33 (maintenance order). Select an order of the order type 'Maintenance order'. On the header, the new<br />functions are available under 'Services for Object'. -&gt; PDF Form&#x00A0;&#x00A0;&#x00A0;&#x00A0; --&gt; ...<br /><br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "PM-EQM-SF-EM (Emission Management Integration)"}, {"Key": "Responsible                                                                                         ", "Value": "C5069799"}, {"Key": "Processor                                                                                           ", "Value": "C5060856"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001073313/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001073313/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001073313/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001073313/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001073313/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001073313/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001073313/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001073313/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001073313/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "937800", "RefComponent": "XAP-EM-INT", "RefTitle": "xEM integration", "RefUrl": "/notes/937800"}, {"RefNumber": "1161250", "RefComponent": "XAP-EM-INT", "RefTitle": "Env. Compl. Integration (Release 2.0 SP12 PL01 and higher)", "RefUrl": "/notes/1161250"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1161250", "RefComponent": "XAP-EM-INT", "RefTitle": "Env. Compl. Integration (Release 2.0 SP12 PL01 and higher)", "RefUrl": "/notes/1161250 "}, {"RefNumber": "937800", "RefComponent": "XAP-EM-INT", "RefTitle": "xEM integration", "RefUrl": "/notes/937800 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "DFPS", "From": "610", "To": "610", "Subsequent": ""}, {"SoftwareComponent": "XEM", "From": "200", "To": "200", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}