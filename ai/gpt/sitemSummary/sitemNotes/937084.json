{"Request": {"Number": "937084", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 307, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016082732017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000937084?language=E&token=1E2AEA87B26633BC85380E0D9146382C"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000937084", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000937084/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "937084"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "31.03.2006"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX"}, "SAPComponentKeyText": {"_label": "Component", "value": "Flexible Real Estate Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "937084 - Enhancing a search help without modification in RE-FX"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Using an example, this note describes how you can enhance collective search helps in the RE-FX environment. Carry out the enhancement in customer-specific packages. The enhancement remains after you import a Support Package or after a release upgrade.<br />Application examples:</p> <UL><LI>You added customer-specific fields to the master data using the procedure described in Note 690900. You also want to display these fields in the hit list of a search help or you want to have a search help that actually searches for these fields.</LI></UL> <UL><LI>In the standard search help, it is not possible to search for a field that is important for your search.<br /></LI></UL> <p>In the first case, it is evident that the search help can not be delivered in the standard system.<br />In the second case, it would be technically possible to enhance the standard search help. However, you often require additional indexes for the high performance search for the missing criterion, therefore it is only appropriate to create these indexes for customers who also really require the criterion.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RE80, search help, F4 help, matchcode, collective search help, individual search help<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Prerequisites<br />The search help which you would like to enhance must be a collective search help.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The following is a description of how you can enhance the search help that is provided in the PS-CD environment for a FKKVKP_F4 contract account in such a way that you can search for a contract account using the address of the rental object.<br />The search help only displays a contract account if</p> <UL><LI>the contract account is used in a real estate contract AND</LI></UL> <UL><LI>a rental object is assigned to the real estate contract AND</LI></UL> <UL><LI>The assigned rental object has an address.</LI></UL> <p>The example described here only works if you use RE-FX with PS-CD and if you activated the relevant components in your system. This refers to the fields of the FKKVK and FKKVP tables as well as to the VIBPOBJREL-VKONT field.<br />However, the basic procedure is also transferable for other questions.<br /><br />Proceed as follows:</p> <OL>1. Creating a database view as selection method:<br />Firstly, you require a search method. For this purpose, you create a database view using Transaction SE11, for example YV_REXAFKKVKPRO.</OL> <OL><OL>a) In the table on the left on the Table / Join Condition tab, enter the database tables from which data must be read.<br />In the \"Join condition\" table on the right, specify which fields are used to link these tables.<br />For our example, you require the following tables:<br />VIBDRO<br />VZOBJECT<br />ADRC<br />FKKVK<br />FKKVKP<br />VIBPOBJREL<br />VICNCN<br />VIBDOBJASS<br /><br />Enter the following as a join condition:<br />ADRC&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CLIENT&#x00A0;&#x00A0;&#x00A0;&#x00A0; = VZOBJECT&#x00A0;&#x00A0;&#x00A0;&#x00A0;MANDT<br />ADRC&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ADDRNUMBER = VZOBJECT&#x00A0;&#x00A0;&#x00A0;&#x00A0;ADRNR<br />VZOBJECT&#x00A0;&#x00A0;&#x00A0;&#x00A0;MANDT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;= VIBDRO&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MANDT<br />VZOBJECT&#x00A0;&#x00A0;&#x00A0;&#x00A0;ADROBJNR&#x00A0;&#x00A0; = VIBDRO&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;INTRENO<br />VIBDRO&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MANDT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;= VIBDOBJASS&#x00A0;&#x00A0;MANDT<br />VIBDRO&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;OBJNR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;= VIBDOBJASS&#x00A0;&#x00A0;OBJNRTRG<br />VIBDOBJASS&#x00A0;&#x00A0;MANDT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;= VICNCN&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MANDT<br />VIBDOBJASS&#x00A0;&#x00A0;OBJNRSRC&#x00A0;&#x00A0; = VICNCN&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;OBJNR<br />VICNCN&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MANDT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;= VIBPOBJREL&#x00A0;&#x00A0;MANDT<br />VICNCN&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;INTRENO&#x00A0;&#x00A0;&#x00A0;&#x00A0;= VIBPOBJREL&#x00A0;&#x00A0;INTRENO<br />VIBPOBJREL&#x00A0;&#x00A0;MANDT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;= FKKVK&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MANDT<br />VIBPOBJREL&#x00A0;&#x00A0; VKONT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;= FKKVK&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;VKONT<br />VIBPOBJREL&#x00A0;&#x00A0; MANDT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;= FKKVKP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MANDT<br />VIBPOBJREL&#x00A0;&#x00A0;VKONT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;= FKKVKP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;VKONT<br />VIBPOBJREL&#x00A0;&#x00A0;PARTNER&#x00A0;&#x00A0;&#x00A0;&#x00A0;= FKKVKP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GPART<br /><br />Keep in mind that you must always include the client in the Join condition for client-specific tables, otherwise the tables are linked for all clients !<br /></OL></OL><OL><OL>b) Go to the View fields tab and enter the fields that you want to use to select and which fields should be output somewhere on the hit list of the search help. You can enter the fields directly or you can use the button to select table fields. If you require a field that occurs in two tables and that has the same name in both tables, you must change in the \"View field\" column the name so that it is unique.<br /><br />For our example, choose the fields from the \"Rental object according to address\" standard search help and the fields from the \"Contract account\" standard search help:<br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD></TD></TR> <TR><TD>View field</TD><TD> Table</TD><TD> Field</TD></TR> <TR><TD>MANDT </TD><TD> VIBDRO </TD><TD> MANDT</TD></TR> <TR><TD>VKONT </TD><TD> FKKVKP </TD><TD> VKONT</TD></TR> <TR><TD>GPART</TD><TD> FKKVKP </TD><TD> GPART</TD></TR> <TR><TD>DATE_FROM </TD><TD> ADRC </TD><TD> DATE_FROM</TD></TR> <TR><TD>NATION </TD><TD> ADRC </TD><TD> NATION</TD></TR> <TR><TD>INTRENO_RO </TD><TD> VIBDRO </TD><TD> INTRENO</TD></TR> <TR><TD>INTRENO_CN </TD><TD> VICNCN </TD><TD> INTRENO</TD></TR> <TR><TD>ADROBJTYP</TD><TD> VZOBJECT </TD><TD> ADROBJTYP</TD></TR> <TR><TD>BUKRS </TD><TD> VIBDRO </TD><TD> BUKRS</TD></TR> <TR><TD>SWENR </TD><TD> VIBDRO </TD><TD> SWENR</TD></TR> <TR><TD>SMENR </TD><TD> VIBDRO </TD><TD> SMENR</TD></TR> <TR><TD>ROTYPE </TD><TD> VIBDRO </TD><TD> ROTYPE</TD></TR> <TR><TD>XMETXT </TD><TD> VIBDRO</TD><TD> XMETXT</TD></TR> <TR><TD>RECNNR </TD><TD> VICNCN </TD><TD> RECNNR</TD></TR> <TR><TD>COUNTRY </TD><TD> ADRC </TD><TD> COUNTRY</TD></TR> <TR><TD>POST_CODE1</TD><TD> ADRC </TD><TD> POST_CODE1</TD></TR> <TR><TD>CITY1 </TD><TD> ADRC </TD><TD> CITY1</TD></TR> <TR><TD>MC_CITY1 </TD><TD> ADRC </TD><TD> MC_CITY1</TD></TR> <TR><TD>STREET </TD><TD> ADRC </TD><TD> STREET</TD></TR> <TR><TD>MC_STREET </TD><TD> ADRC </TD><TD> MC_STREET</TD></TR> <TR><TD>HOUSE_NUM1</TD><TD> ADRC </TD><TD> HOUSE_NUM1</TD></TR> <TR><TD>REGION </TD><TD> ADRC </TD><TD> REGION</TD></TR> <TR><TD>ADRZUS </TD><TD> VZOBJECT </TD><TD> ADRZUS</TD></TR> <TR><TD>IMKEY </TD><TD> VICNCN </TD><TD> IMKEY</TD></TR> <TR><TD>VKBEZ </TD><TD> FKKVK </TD><TD> VKBEZ</TD></TR> <TR><TD>VKONA </TD><TD> FKKVK</TD><TD> VKONA</TD></TR> <TR><TD>VKPBZ </TD><TD> FKKVKP </TD><TD> VKPBZ</TD></TR> <TR><TD>VKTYP </TD><TD> FKKVK </TD><TD> VKTYP</TD></TR> <TR><TD>APPLK</TD><TD> FKKVK </TD><TD> APPLK</TD></TR> <TR><TD>OPBUK </TD><TD> FKKVKP </TD><TD> OPBUK</TD></TR> <TR><TD>BEGRU </TD><TD> FKKVKP </TD><TD> BEGRU</TD></TR> <TR><TD>OBJNR </TD><TD> VICNCN </TD><TD> OBJNR</TD></TR> <TR><TD></TD></TR> <TR><TD>The field list should always include the fields that are defined as parameters in the collective search help that you want to enhance (in our case, GPART and VKONT for the FKKVKP_F4 search help)</TD></TR> </TABLE></OL></OL> <OL><OL>c) If the context already restricts the selection to certain aspects, you can enter these specifications in the \"Selection conditions\" tab. In our example, only the rental objects in the address file are relevant, the selection condition is therefore as follows:<br /><br />Table&#x00A0;&#x00A0;&#x00A0;&#x00A0;Field name Operator Comparison value AND/OR<br />VZOBJECT ADROBJTYP&#x00A0;&#x00A0;EQ&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;'VI'&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;AND<br />VZOBJECT OBTYP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;EQ&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; '54'<br /></OL></OL> <OL><OL>d) Activate the view.</OL></OL> <OL>2. Create a search help:<br />Use Transaction SE11 to create an elementary search help, for example YREXAFKKVKPRO.</OL> <OL><OL>a) Enter an appropriate short text. This later appears as a tab heading in the collective search help.</OL></OL> <OL><OL>b) Enter the following in the \"Data collection\" area in the \"Definition\" tab:<br />Selection method: Name of the view defined under 1, for example YV_REXAFKKVKPRO<br />Dialog type: Select a type. In general, for more complex search helps using application data, select \"Dialog with value restriction\" or \"Dialog depends on set of values\".<br />Select a short cut that is preferably not yet used for another individual search help of the collective search help to be enhanced.</OL></OL><OL><OL>c) You can leave the search help exit field empty. Most search helps in the RE environment have the F4UT_OPTIMIZE_COLWIDTH exit here, this exit optimizes the columns on the hit list.</OL></OL> <OL><OL>d) In the table with the search help parameters, enter the names of the fields of the database view. In the IMP or EXP column, indicate that it concerns an import or an export parameter. This only makes sense if the search help to be enhanced provides this.<br />In our example, both indicators are set for the fields VKONT and GPART.<br />If there is a Set-Get-Parameter for a field on the selection screen, you can enter the name of this parameter in the default value column (for example for BUKRS BUK, for BE business entity, ...).<br />Enter natural numbers in the LPos column for the fields that are to be displayed on the hit list. The columns are sorted in the sequence of these numbers.<br />Enter natural numbers in the SPos column for the fields that should appear on the selection screen (for \"Dialog with value restriction\"). The fields are sorted in the sequence of these numbers.<br />In the example, the following items were selected (The first figure is in the LPos column, the second in the SPos column):<br />VKONT 13 13<br />GPART 14 14<br />VKONA 15 15<br />VKPBZ 16 16<br />OPBUK 17 17<br />BUKRS 1 1<br />SWENR 2 2<br />ROTYPE 3 3<br />SMENR 4 4<br />RECNNR 5<br />COUNTRY&#x00A0;&#x00A0;0 5<br />REGION&#x00A0;&#x00A0;0 6<br />POST_CODE1 6 7<br />CITY1 7 0<br />MC_CITY1&#x00A0;&#x00A0;0 8<br />STREET 8&#x00A0;&#x00A0;0<br />MC_STREET&#x00A0;&#x00A0;0 9<br />HOUSE_NUM1 9 10<br />ADRZUS 10 11<br /><br />If you create the search help in a system in which relevant data is available in the database tables, you can choose \"Test\" to see how the selection screen and the results list appear.<br /></OL></OL> <OL><OL>e) Activate the search help.<br /></OL></OL> <OL>3. Enhance the collective search help:<br />Use Transaction SE12 to display the collective search help that you want to enhance. In our example this is the FKKVKP_F4 search help.<br />Choose Goto -&gt; Append Search Help.<br />Enter a new name for the Append Search Help to be created, for example YREXAFKKVKP.<br /><br />On the \"Included search help\" tab, enter the name of the search help that you created previously, for example YREXAFKKVKPRO. Choose \"Parameter assignment\" and assign the parameters of your search help to those of the collective search help.<br /><br />Activate the collective search help.<br /></OL> <OL>4. Check whether you require database indices:<br />If (in the selection conditions or in the join conditions of the used database view) you use fields that are not primary keys of the database table and for which there is no database index, then check whether it makes sense to create a database index for the table.<br />This also applies to fields that are frequently used as selection criteria. To determine whether a database index is necessary or useful, you should test the search help in the production system using typical selections. If the performance is unsatisfactory, you can use database indices to correct this.<br /></OL> <OL>5. Test:<br />Call a transaction that uses the collective search, and select the customer-specific search help that you just created. Test whether the correct data is displayed.</OL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D019721)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D028090)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000937084/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000937084/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000937084/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000937084/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000937084/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000937084/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000937084/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000937084/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000937084/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "939971", "RefComponent": "RE-FX", "RefTitle": "FAQ and consulting notes for RE-FX", "RefUrl": "/notes/939971"}, {"RefNumber": "1654760", "RefComponent": "RE-FX-BD", "RefTitle": "Enhancing search helps: Example of validity, deletion flag", "RefUrl": "/notes/1654760"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "939971", "RefComponent": "RE-FX", "RefTitle": "FAQ and consulting notes for RE-FX", "RefUrl": "/notes/939971 "}, {"RefNumber": "1654760", "RefComponent": "RE-FX-BD", "RefTitle": "Enhancing search helps: Example of validity, deletion flag", "RefUrl": "/notes/1654760 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}