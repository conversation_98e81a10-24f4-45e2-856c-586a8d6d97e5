{"Request": {"Number": "1171344", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 592, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016521042017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001171344?language=E&token=CD8A85F95B26436CDE2465A12E71B9D8"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001171344", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001171344/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1171344"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 25}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.05.2023"}, "SAPComponentKey": {"_label": "Component", "value": "FIN-SEM-BCS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Consolidation"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financials", "value": "FIN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Strategic Enterprise Management", "value": "FIN-SEM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-SEM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Consolidation", "value": "FIN-SEM-BCS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-SEM-BCS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1171344 - SEM-BCS in the context of SAP ERP 6.0 EHP"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Clarification of functional scope of SEM-BCS in the context of SAP ERP 6.0 (formerly mySAP ERP 2005) EHP (Enhancement Packages)</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Consolidation, Enhancement Package, SAP ERP 6.0, SEM-BCS, IFRS</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You have installed SEM-BCS on release SAP ERP 6.0.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The SAP consolidation solution SEM-BCS is an integral part of SAP ERP (Enterprise Ressource Planning), in the context of EHP (Enhancement Packages).</p>\r\n<ul>\r\n<li>The functionality which is available in EHP2, after activation of Business Function FIN_ACC_GROUP_CLOSE, consists of:</li>\r\n<ul>\r\n<li>Automatic data transfer (flexible upload, load from datastream, copy functionality) of consolidation postings (two-sided eliminations, consolidation group-dependent postings), load of technical datastreams for consolidation of investments (activity numbers, activity sequence</li>\r\n<li>Restatement characteristic in selected task types (data entry, manual posting, currency translation, validation, balance carryforward), restatement functionality in reporting,</li>\r\n<li>Posting functionality for deferred taxes only within balance sheet,</li>\r\n<li>Interprofit elimination of inventory with location of values for inventory data from totals database,</li>\r\n<li>Workflow functionality for manual posting and status changes,</li>\r\n<li>Consolidation monitor for local and corporate accountant based on webdynpro user interface,</li>\r\n<li>Consolidation monitor with multiperiod posting functionality,</li>\r\n<li>Performance improvements of status management, reporting and initial start-up of consolidation workbench and monitor.</li>\r\n</ul>\r\n<li>Within EHP3 the following functional enhancements are available, after activation of Business Function FIN_ACC_GROUPCLOSE_2:</li>\r\n<ul>\r\n<li>New tasktype for sign-off,</li>\r\n<li>Workflow functionality for flexible upload of documents with periodic data input and insert mode.</li>\r\n<ul style=\"list-style-type: none;\">\r\n<li>The functional enhancements on EHP3 depend on EHP2, i.e. , activation of Business Function FIN_ACC_GROUPCLOSE_2 requires prior activation of Business Function FIN_ACC_GROUP_CLOSE.</li>\r\n</ul>\r\n</ul>\r\n<li>In EHP4 additional functionality is provided, after activation of Business Function FIN_ACC_GC_TAXRATECHG:</li>\r\n<ul>\r\n<li>Adjustment of deferred taxes in balance sheet in case of time-dependent tax rates,</li>\r\n<li>Integrated load from datastream for compounded asset master data,</li>\r\n<li>Balance carryforward of equity data for at-equity units in case of mixed location of values for equity,</li>\r\n<li>New reporting mode \"reference\",</li>\r\n<li>Interprofit elimination of inventory with non-existing inventory datastream,</li>\r\n<li>Validation of investment data in case of location of values for investment from totals database,</li>\r\n<li>Interpretation of divestitures by group share posting report,</li>\r\n<li>Execution of task groups in batch without blocking&#160;</li>\r\n</ul>\r\n<li>The functional scope of EHP5 is organized into <strong><strong>two</strong></strong> Business Functions which can be activated independently of each other. The scope of Business Function FIN_ACC_GC_BCOMB consists of the following topics:</li>\r\n<ul>\r\n<li>Business Combinations (Goodwill allocation to goodwill-carrying units, disclosure and value adjustment of full goodwill and its non-controlling interest (minorities), gain or loss of control, partial divestiture as equity transaction, net income effect from other comprehensive income at loss of control)</li>\r\n<li>Merger activities on ceding and absorbing company,</li>\r\n<li>Preparation of change to consolidation group (PCC) for group-dependent postings,</li>\r\n<li>Balance carryforward in case of new local currency equal to group currency,</li>\r\n<li>Copy functionality for characteristic values of restatement,</li>\r\n<li>Organizational change simultaneously in both matrix dimensions,</li>\r\n<li>Usability of goodwill inheritance,</li>\r\n<li>Suppression of information messages in task logs and manual posting,</li>\r\n<li>Group-specific manual postings,</li>\r\n<li>Authority check for deletion of task logs and for execution of breakdown check,</li>\r\n<li>Legacy data transfer for interprofit elimination of assets,</li>\r\n<li>Indirect method change in C/I,</li>\r\n<li>Interactive Excel for data collection and reporting (see note 1844598).</li>\r\n</ul>\r\n<li>Also within the functional scope of EHP5, Business Function FIN_ACC_GC_ASTHFS covers the following topics:</li>\r\n<ul>\r\n<li>Assets held for sale and discontinued operations</li>\r\n<li>Treatment of source data with zero key figures in copy and load from datastream,</li>\r\n<li>Support of additional financial data for investment and equity in case of deactivated consolidation of investments (C/I),</li>\r\n<li>Consistency check on missing parent units at maintenance of accounting techniques,</li>\r\n<li>Mapping functionality for document types in balance carryforward,</li>\r\n<li>Customizable severity of check for investment vs. equity in C/I,</li>\r\n<li>Displaying vs. hiding statistical items in log of individual C/I execution,</li>\r\n<li>Goodwill in local currency for equity-consolidation,</li>\r\n<li>Equity-consolidation according to group shares.</li>\r\n</ul>\r\n<li>In EHP6 further functionality is available, after activation of Business Function FIN_ACC_GC_RESTAT:</li>\r\n<ul>\r\n<li>Restatement monitor with both manual and automatic tasks of&#160;all task types</li>\r\n<li>Colour coding of inactive organizational units in hierarchy appearance in consolidation workbench and monitor,</li>\r\n<li>Copy of organizational units and hierarchies, including year and period of first and last consolidation and assignment of consolidation of investments (C/I) methods and parent indicators, between hierarchy versions,</li>\r\n<li>Assignment of document types for preparation of change to consolidation group (PCC),</li>\r\n<li>Impact of change of parent indicator on C/I,</li>\r\n<li>Stepwise equity-consolidation in multiple investment hierarchies in C/I,</li>\r\n<li>New C/I activity for correction documents in copy of C/I documents,</li>\r\n<li>Information message on missing exchange rate at runtime of currency translation (C/T),</li>\r\n<li>Inversion of debit/credit decision in automatic postings,</li>\r\n<li>Conditional status update,</li>\r\n<li>Analysis list of task sequence and preceding task relationship,</li>\r\n<li>Reclassification customizing and analysis list for percentages in proportionate consolidation,</li>\r\n<li>Authority checks on saving and changing permanent parameters and for transport,</li>\r\n<li>Support of 'lower case' and conversion exits of BW characteristics (active only for SEM-BCS with local BW connection) in generated selection screens</li>\r\n</ul>\r\n<li>In EHP7 additional functionality is provided, after activation of Business Function FIN_ACC_GC_ALLOC:</li>\r\n<ul>\r\n<li>Allocation task for consolidation postings (pairwise eliminations (posting level 20) and group-dependent postings (posting level 30)</li>\r\n<li>Breakdown check and repair tool for customizing settings</li>\r\n<li>Executability of custom task in dependency of consolidation unit</li>\r\n<li>Method assignment to reclassification and allocation task in dependency of consolidation unit</li>\r\n<li>Assignment of rounding methods to C/T task in dependency of consolidation unit</li>\r\n<li>Breakdown information in validation task log</li>\r\n<li>Report on results of validation task execution</li>\r\n<li>Interpretation of consolidation group in reconciliation task execution</li>\r\n<li>Automated support of negative equity for <strong>all</strong> C/I activities</li>\r\n<li>Multiple selection of consolidation group in analysis list of totals database</li>\r\n<li>Authority check for changes to comments in documents</li>\r\n<li>Usability improvements of task log (last log per consolidation unit, filter function for messages, customizing setting to suppress display of certain messages in log)</li>\r\n<li>Extensions to where-used function (e.g., for period category)</li>\r\n<li>Status update in mass reversal</li>\r\n<li>Archiving of monitor status</li>\r\n</ul>\r\n<li>In EHP8 further functionality is available, after activation of Business Function FIN_ACC_GC_CHKDGT:</li>\r\n<ul>\r\n<li>Check digits for transaction data</li>\r\n<li>Manual status changes with selection screen</li>\r\n<li>Separate authority check for blocking of tasks by user intervention</li>\r\n<li>Default method with exceptions for consolidation unit-dependent method assignment to tasks</li>\r\n<li>Omission of check for blocked accounts during copy</li>\r\n<li>Search function in goodwill inheritance</li>\r\n<li>Matrix dimension for intercompany reconciliation</li>\r\n<li>User-exit in reclassification</li>\r\n<li>Aggregated posting in reclassification</li>\r\n<li>Information on method and method step in document text</li>\r\n<li>Performance checks for validation</li>\r\n<li>Existence check for value ranges on selection screens</li>\r\n<li>Suppression of display of reversal or inversion&#160;pairs in list of journal entries</li>\r\n<li>Preparation for change to consolidation group (PCC) with posting into income statement</li>\r\n<li>Distinct transaction types for posting of organizational change (OC)</li>\r\n<li>Separate activity for adjustment of first consolidation in consolidation of investments (C/I)</li>\r\n<li>Currency translation of goodwill from prior years in organizational change (OC)</li>\r\n<li>Total divestiture of consolidation unit combinations due to organizational change (OC) or merger</li>\r\n<li>Deviating investment share percentages in consolidation of investments (C/I)</li>\r\n</ul>\r\n</ul>\r\n<p>IFRS relevance of the SEM-BCS product is illustrated in the book \"IFRS-Konzernabschl&#252;sse mit SAP\" by Henning Kagermann, Karlheinz K&#252;ting and Johannes Wirth, published by Sch&#228;ffer/Poeschel in June 2008.</p>\r\n<p>Mainstream maintenance for SAP ERP 6.0 EHP6 and higher is provided until end of year 2027. (See SAP Note <strong>2881788</strong> for details.)&#160;Support for&#160;SAP NetWeaver Business Warehouse (NW BW) depends on its installation mode. (See SAP Notes <strong>1648480</strong> and <strong>2741041</strong> for details.)</p>\r\n<ul>\r\n<li>Embedded data warehouse in SAP Business Suite (SAP ERP)</li>\r\n<li>Stand-alone version of SAP NetWeaver Business Warehouse (Business Warehouse hub usage of SAP NetWeaver)</li>\r\n</ul>\r\n<p>For SEM-BCS this results in the time horizons for maintenance as follows:</p>\r\n<ul>\r\n<li>SEM-BCS EHP4 on NW BW 7.01</li>\r\n<ul>\r\n<li>On embedded BW: Mainstream maintenance until end of year 2025</li>\r\n<li>On stand-alone BW: Now in customer-specific maintenance</li>\r\n</ul>\r\n<li>SEM-BCS EHP4 on NW BW 7.30:</li>\r\n<ul>\r\n<li>On embedded BW: Mainstream maintenance until end of year 2025</li>\r\n<li>On stand-alone BW: Now in customer-specific maintenance</li>\r\n</ul>\r\n<li>SEM-BCS EHP5 on NW BW 7.02</li>\r\n<ul>\r\n<li>On embedded BW: Mainstream maintenance until end or year 2025</li>\r\n<li>On stand-alone BW: Now in customer-specific maintenance</li>\r\n</ul>\r\n<li>SEM-BCS EHP6 on NW BW 7.31</li>\r\n<ul>\r\n<li>On embedded BW: Mainstream maintenance until end of year 2027</li>\r\n<li>On stand-alone BW: Now in customer-specific maintenance</li>\r\n</ul>\r\n<li>SEM-BCS EHP7 on NW BW 7.40</li>\r\n<ul>\r\n<li>On embedded BW: Mainstream maintenance until end of year 2027</li>\r\n<li>On stand-alone BW: Now in customer-specific maintenance</li>\r\n</ul>\r\n<li>SEM-BCS EHP8 on NW BW 7.50:</li>\r\n<ul>\r\n<li>On embedded BW: Mainstream maintenance until end of year 2027</li>\r\n<li>On stand-alone BW: Mainstream maintenance until end of year 2027</li>\r\n</ul>\r\n</ul>\r\n<p>After end of mainstream maintenance, SAP provides customer-specific maintenance of SEM-BCS in accordance to the terms stated in SAP Note <strong>52505</strong>.</p>\r\n<p>Additional information about maintenance of SAP ERP is available on&#160;Service Marketplace at: support.sap.com/maintenance. For more detailed information contact your Account Team.</p>\r\n<p>If you want to run BCS on SAP&#8217;s next-generation data warehousing solution &#8211; SAP BW/4HANA, you can use \"<em>SAP BW/4HANA, Business Consolidation Add-On</em>\" (BCS/4HANA). The relevant release information on BCS/4HANA is provided in SAP Note&#160;<strong>1330000</strong>&#160;and in the rollout materials quoted therein.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D031418)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I019855)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001171344/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001171344/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001171344/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001171344/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001171344/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001171344/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001171344/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001171344/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001171344/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1844598", "RefComponent": "EC-CS", "RefTitle": "SAP Interactive Excel, version 3.0", "RefUrl": "/notes/1844598"}, {"RefNumber": "1648480", "RefComponent": "XX-SER-REL", "RefTitle": "Maintenance for SAP Business Suite 7 Software including SAP NetWeaver", "RefUrl": "/notes/1648480"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1648480", "RefComponent": "XX-SER-REL", "RefTitle": "Maintenance for SAP Business Suite 7 Software including SAP NetWeaver", "RefUrl": "/notes/1648480 "}, {"RefNumber": "2741041", "RefComponent": "BW", "RefTitle": "Maintenance for SAP NetWeaver 7.X Business Warehouse", "RefUrl": "/notes/2741041 "}, {"RefNumber": "979810", "RefComponent": "FIN-SEM-BCS", "RefTitle": "Documentation for SEM-BCS", "RefUrl": "/notes/979810 "}, {"RefNumber": "2360258", "RefComponent": "FIN-SEM-BCS", "RefTitle": "SEM-BCS in the context of SAP S/4HANA OP ( \"on premise\" )", "RefUrl": "/notes/2360258 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SEM-BW", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SEM-BW", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "SEM-BW", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "SEM-BW", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "SEM-BW", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "SEM-BW", "From": "736", "To": "736", "Subsequent": ""}, {"SoftwareComponent": "SEM-BW", "From": "634", "To": "634", "Subsequent": ""}, {"SoftwareComponent": "SEM-BW", "From": "746", "To": "746", "Subsequent": ""}, {"SoftwareComponent": "SEM-BW", "From": "747", "To": "747", "Subsequent": ""}, {"SoftwareComponent": "SEM-BW", "From": "748", "To": "748", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}