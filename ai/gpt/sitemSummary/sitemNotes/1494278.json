{"Request": {"Number": "1494278", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 230, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017075172017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001494278?language=E&token=1D269BF55AB35B3232CDF1592B657407"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001494278", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001494278/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1494278"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 28}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.01.2024"}, "SAPComponentKey": {"_label": "Component", "value": "BC-SEC-VIR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Anti Virus Protection"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Security - Read KBA 2985997 for subcomponents", "value": "BC-SEC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Anti Virus Protection", "value": "BC-SEC-VIR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC-VIR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1494278 - NW-VSI: Summary of Virus Scan Adapter´s for SAP integration"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You implement SAP Virus Scan Interface (VSI) using an external anti-malware product.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>NW-VSI, NW-VSI 2.00, NW-VSI 2.1, VSI, VSI2, VSA, Virus Scan Interface, Virus Scan Adapter, Anti-Virus, Anti-Malware, MIME Type Filter</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>An existing anti-virus (AV) solution in your company should be used to enable SAP NW-VSI in your SAP application.<br /><br />You need an external anti-virus / anti-malware product to be installed in your environment.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The Virus Scan Adapter is a shared library that integrates external anti-virus or other anti-malware solutions with NW-VSI. NW-VSI is a software layer for different programming environments available in all SAP applications.<br />The adapter library itself is only an interface between the external anti-virus solution and the SAP system. Thus scanning is performed by the anti-virus solution, based on scanning parameters provided by the SAP system.</p>\r\n<p>The latest version of NW-VSI is 2.1 (NW-VSI 2.1). The SAP Note <a target=\"_blank\" href=\"/notes/1883424\">1883424 </a>provides an overview of this version.<br />Furthermore, the interface version NW-VSI 1.00 is outdated and all certifications for this version are outdated and no longer supported.</p>\r\n<p>Until&#160;the year 2023 anti-malware solution providers were able to apply for certification for the interface by the SAP Integration and Certification Center (ICC).<br />A certification for NW-VSI is valid for 3 years but will no longer be renewed by SAP. The certification has been discontinued by SAP&#160;in&#160;2023.<br />At this point SAP strongly emphasizes, that discontinuation of the certification does not imply the discontinuation of the VSI itself.<br /> It is still supported.</p>\r\n<p>A list of products which are still certified for NW-VSI can be found <a target=\"_blank\" href=\"https://www.sap.com/dmc/exp/2013_09_adpd/enEN/#/partners?search=nw-vsi\">here</a>; search for NW-VSI. <br /><br />An adapter to NW-VSI 2.1 can be built based on any anti-virus solution, because all known vendors of anti-malware products offer their own SDK packages to integrate applications.</p>\r\n<p><strong>Explanation of categories:</strong></p>\r\n<ol>1. Status:</ol>\r\n<p>&#160; &#160; &#160; &#160; &#160; &#160; &#160; The certification will not be renewed anymore. In the past the certification status ensures that the integration of a VSA library works with all SAP applications. In addition, it ensures the support and the usage of the complete&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;scenario. You have to ask the vendor for an extra support agreement, because the license for the endpoint protection does not always cover the usage for SAP scenarios.</p>\r\n<p>&#160;</p>\r\n<ol>2. Feature:</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;SAP uses VSI not only for classical anti-virus protection, but also for content analyses and filtering. Currently we distinguish the following cases:</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Classical anti-virus: anti-virus scan based on signature files (called drivers) - mandatory in NW-VSI 1.00.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Active content block: Block active content in documents, e.g. Javascript - mandatory in NW-VSI 1.00, NW-VSI 2.00 and above.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;MIME detection/filter: Detect and filter documents based on their type - mandatory in NW-VSI 1.00, NW-VSI 2.00 and above.<br /><br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Apart from the external features the following features are available:</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;File scan: Is a MUST for a VSA, meaning that only locally stored files can be scanned. Thus the majority of the content comes from the database, a memory scan is faster - mandatory in NW-VSI 1.00 and NW-VSI 2.00 and&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; above.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Memory scan: Supports the scanning of binary objects stored in memory. No extra copy of the content is needed - optional in NW-VSI (all versions).<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;SAR scan: SAR is (SAP Archive Repository) SAPs own archive format to deliver software content - mandatory in NW-VSI 2.00 and above.<br /><br /><br />The following list of virus scan adapters provides an overview of the integrations which are or were certified for NW-VSI 2.1:</p>\r\n<p>1. Vendor: bowbridge Software GmbH</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Product: bowbridge Anti-Virus Bridge 4.0 for SAP solutions</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Contact: <a target=\"_blank\" href=\"http://www.bowbridge.net/\">http://www.bowbridge.net</a></p>\r\n<p>&#160; &#160; &#160; &#160; &#160; &#160; &#160; Mail: <EMAIL></p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Support: <a target=\"_blank\" href=\"http://www.bowbridge.net/\">http://www.bowbridge.net</a> -&gt; Support</p>\r\n<p>&#160; &#160; &#160; &#160; &#160; &#160; &#160; Mail: <EMAIL></p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Availability: Linux, Windows and in principal all SAP platforms on customer request.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<strong>Status: Officially Certified for Linux x64 and Windows x64 on AS ABAP and AS JAVA; </strong></p>\r\n<p><strong>&#160; &#160; &#160; &#160; &#160; &#160; &#160; Last Certification: October 2020</strong></p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Features: NW-VSI 2.1 (Anti-virus, active content block, MIME detection / filter, file scan, memory scan, supports SAR scan)</p>\r\n<ol>2. Vendor: Trend Micro Inc.</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Product:&#160;Trend Micro&#8482; Deep Security&#8482; 20.0 and&#160;Trend Micro Cloud One&#8482; - Workload Security 20.0</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Contact: <a target=\"_blank\" href=\"http://www.trendmicro.com/us/enterprise/cloud-solutions/deep-security\">http://www.trendmicro.com/us/enterprise/cloud-solutions/deep-security</a></p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Support:&#160;<a target=\"_blank\" href=\"https://esupport.trendmicro.com/SRF/srfnabu.aspx\">https://esupport.trendmicro.com/SRF/srfnabu.aspx</a></p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Availability: Linux, Windows&#160;and in principal all SAP platforms on customer request.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<strong>Status: Officially Certified for Linux x64 and Windows x64 on AS ABAP and AS JAVA<strong>;</strong></strong></p>\r\n<p><strong><strong>&#160; &#160; &#160; &#160; &#160; &#160; &#160; Last Certification: January 2022</strong></strong></p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Features: NW-VSI 2.1 (Anti-virus, active content block, MIME detection / filter, file scan, supports SAR scan)</p>\r\n<ol>3. Vendor: ClamAV</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Product:&#160;ClamSAP (OpenSource)</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Contact:&#160;<a target=\"_blank\" href=\"http://sourceforge.net/projects/clamsap/\">http://sourceforge.net/projects/clamsap/</a></p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Availability:&#160;All SAP platforms supported</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<strong>&#160;Status:&#160;Officially Certified for SLES for SAP</strong></p>\r\n<p>&#160; &#160; &#160; &#160; &#160; &#160; &#160; See <a target=\"_blank\" href=\"http://www.suse.com/products/sles-for-sap/features/service-pack-2.html\">http://www.suse.com/products/sles-for-sap/features/service-pack-2.html</a>. Requires ClamAV (www.clamav.net), ClamAV is part of&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; SuSE SLES (begin of SLES 10).<br />&#160; &#160; &#160; &#160; &#160; &#160; &#160; For other operating systems, please consult Cisco (<a target=\"_blank\" href=\"http://www.clamav.net/about\">http://www.clamav.net/about</a>) for official support.</p>\r\n<p>&#160; &#160; &#160; &#160; &#160; &#160; &#160;&#160;Support for Linux: <EMAIL></p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Features: NW-VSI 2.1 (Anti-virus, active content block, MIME detection / filter, file scan, supports SAR scan)</p>\r\n<p style=\"padding-left: 30px;\">&#160;4. Vendor:&#160;Beijing Jiangmin New Technology Co., Ltd</p>\r\n<p>&#160; &#160; &#160; &#160; &#160; &#160; &#160; Product: Reopard Anti-Virus 1.0 for SAP solution</p>\r\n<p>&#160; &#160; &#160; &#160; &#160; &#160; &#160; Contact:&#160;<a target=\"_blank\" href=\"http://www.jiangmin.com/\">www.jiangmin.com</a></p>\r\n<p><strong>&#160; &#160; &#160; &#160; &#160; &#160; &#160; Status: Officially Certified on SAP S/4HANA 2020<strong>;</strong></strong></p>\r\n<p><strong><strong>&#160; &#160; &#160; &#160; &#160; &#160; &#160; Last Certification: July, 2022</strong></strong></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-SEC (Security - Read KBA 2985997 for subcomponents)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D067847)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I556506)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001494278/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001494278/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001494278/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001494278/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001494278/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001494278/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001494278/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001494278/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001494278/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "817623", "RefComponent": "BC-SEC-VIR", "RefTitle": "Frequent questions about VSI in SAP applications", "RefUrl": "/notes/817623"}, {"RefNumber": "786179", "RefComponent": "BC-SEC-VIR", "RefTitle": "Data security products: Use in the antivirus area", "RefUrl": "/notes/786179"}, {"RefNumber": "782963", "RefComponent": "BC-SEC-VIR", "RefTitle": "SAP Note about usage of Virus Scan Server for NW-VSI", "RefUrl": "/notes/782963"}, {"RefNumber": "1883424", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1883424"}, {"RefNumber": "1796762", "RefComponent": "BC-SEC-VIR", "RefTitle": "NW-VSI 2.0: Error when loading external adapters", "RefUrl": "/notes/1796762"}, {"RefNumber": "1743288", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1743288"}, {"RefNumber": "1671303", "RefComponent": "BC-ESI-WS-ABA", "RefTitle": "Virus scans in Web service messages(payload and attachments)", "RefUrl": "/notes/1671303"}, {"RefNumber": "1640285", "RefComponent": "BC-SEC-VIR", "RefTitle": "Determine MIME type with Virus Scan Interface", "RefUrl": "/notes/1640285"}, {"RefNumber": "1585767", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "Enabling Virus Scanning in SAP Content Server", "RefUrl": "/notes/1585767"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2434380", "RefComponent": "BC-SRV-KPR-CMS", "RefTitle": "How to configure KPRO Virus Scan Profile for /SCMS/KPRO_CREATE", "RefUrl": "/notes/2434380 "}, {"RefNumber": "2808515", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux: Installing security software on SAP servers", "RefUrl": "/notes/2808515 "}, {"RefNumber": "2814083", "RefComponent": "BC-UPG-NA", "RefTitle": "Digitally Signed SAP Notes download fails with error  S:SCWN:823", "RefUrl": "/notes/2814083 "}, {"RefNumber": "2646093", "RefComponent": "BC-SEC-VIR", "RefTitle": "Virus scan not working as expected.", "RefUrl": "/notes/2646093 "}, {"RefNumber": "2580438", "RefComponent": "BC-SRV-APS-EXT-AQD", "RefTitle": "ADTSET_GET_ENTITYSET not implemented", "RefUrl": "/notes/2580438 "}, {"RefNumber": "2550954", "RefComponent": "MOB-SUP-RT", "RefTitle": "SMP 3.0 Support for Antivirus Solutions", "RefUrl": "/notes/2550954 "}, {"RefNumber": "2520759", "RefComponent": "OPU-FND-CS", "RefTitle": "Virus scan profile \"Recursive occurrence of virus scan profile in the sequence\" transaction /iwfnd/error_log", "RefUrl": "/notes/2520759 "}, {"RefNumber": "1792039", "RefComponent": "SRM-EBP-CA-ATT", "RefTitle": "Error \"Invalid file name\" in notes and attachments", "RefUrl": "/notes/1792039 "}, {"RefNumber": "2430668", "RefComponent": "BC-SRV-KPR", "RefTitle": "Fiori application error with Virus Scan profile /SCMS/KPRO_CREATE", "RefUrl": "/notes/2430668 "}, {"RefNumber": "3052386", "RefComponent": "BC-SEC-VIR", "RefTitle": "FAQ | Virus Scan Interface (VSI)", "RefUrl": "/notes/3052386 "}, {"RefNumber": "2510946", "RefComponent": "CEC-MKT-MEM", "RefTitle": "Unable to upload email templates or content due to Virus Scanner Adaptor CLAMSAP ClamAV", "RefUrl": "/notes/2510946 "}, {"RefNumber": "2250095", "RefComponent": "XX-S4C-OPR", "RefTitle": "VSI error in SAP Cloud Products", "RefUrl": "/notes/2250095 "}, {"RefNumber": "1730930", "RefComponent": "HAN-DB", "RefTitle": "Using antivirus software in an SAP HANA appliance", "RefUrl": "/notes/1730930 "}, {"RefNumber": "2081108", "RefComponent": "HAN-AS-XS", "RefTitle": "Virus Scan Interface (VSI) Support in SAP HANA DB and XS", "RefUrl": "/notes/2081108 "}, {"RefNumber": "1796762", "RefComponent": "BC-SEC-VIR", "RefTitle": "NW-VSI 2.0: Error when loading external adapters", "RefUrl": "/notes/1796762 "}, {"RefNumber": "786179", "RefComponent": "BC-SEC-VIR", "RefTitle": "Data security products: Use in the antivirus area", "RefUrl": "/notes/786179 "}, {"RefNumber": "817623", "RefComponent": "BC-SEC-VIR", "RefTitle": "Frequent questions about VSI in SAP applications", "RefUrl": "/notes/817623 "}, {"RefNumber": "1671303", "RefComponent": "BC-ESI-WS-ABA", "RefTitle": "Virus scans in Web service messages(payload and attachments)", "RefUrl": "/notes/1671303 "}, {"RefNumber": "1640285", "RefComponent": "BC-SEC-VIR", "RefTitle": "Determine MIME type with Virus Scan Interface", "RefUrl": "/notes/1640285 "}, {"RefNumber": "782963", "RefComponent": "BC-SEC-VIR", "RefTitle": "SAP Note about usage of Virus Scan Server for NW-VSI", "RefUrl": "/notes/782963 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}