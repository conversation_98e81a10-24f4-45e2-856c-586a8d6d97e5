{"Request": {"Number": "2668150", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 234, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001840112018"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002668150?language=E&token=5AB2640F3C857C813C95529BD496B3C7"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002668150", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002668150/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2668150"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.07.2019"}, "SAPComponentKey": {"_label": "Component", "value": "SCM-EWM-UPG"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Supply Chain Management", "value": "SCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Extended Warehouse Management", "value": "SCM-EWM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-EWM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade", "value": "SCM-EWM-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-EWM-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2668150 - SAP S/4HANA 1809: Release information and restrictions for EWM in SAP S/4HANA"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note contains release information and restrictions when using embedded Extended Warehouse Management (EWM) in SAP S/4HANA, on-premise edition 1809.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>SAP has recommendations for implementing EWM in SAP S/4HANA</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Basic Warehouse Management and Extended Warehouse Management in SAP S/4HANA</strong></p>\r\n<p>Embedded EWM in S/4HANA is available in two versions:</p>\r\n<ul>\r\n<li>Basic Warehouse Management</li>\r\n<li>Extended&#160;Warehouse Management</li>\r\n</ul>\r\n<p>Refer to the Feature Scope Description of SAP S/4HANA 1809 for an overview of the features contained in each version.</p>\r\n<p>Both versions are available with the SAP S/4HANA installation. You set up the warehouse management version at warehouse number level. You can change the version used by a warehouse at any point in time.</p>\r\n<p><strong>Release Information</strong></p>\r\n<p>A document \"Basic Settings for EWM in SAP S/4HANA, On-Premise Edition\" for starting with EWM in SAP S/4HANA is available as attachment to this SAP note.</p>\r\n<p>Business configuration content for EWM in SAP S/4HANA is available for download in the Best Practice Explorer.</p>\r\n<p>Materials and batches that were created in SAP S/4HANA releases prior to 1610 have to be enabled for usage in EWM in SAP S/4HANA. To do so run report PRD_SCM_GUID_CONVERSION for materials and report RVB_BATCH_GUID_GENERATOR for batches. For more information see the report documentation.</p>\r\n<p><strong>Major differences between embedded EWM in SAP S/4HANA 1809 and decentral SAP EWM 9.5</strong></p>\r\n<p>Embedded EWM in SAP S/4HANA and decentral SAP EWM share a common core of warehouse management functions. They support the same business scenarios and processes with only minor exceptions.</p>\r\n<p>Embedded EWM in SAP S/4HANA offers a simplified integration into other SAP S/4HANA applications:</p>\r\n<ul>\r\n<li>No Core Interface (CIF) to transfer master data to the embedded EWM application. EWM directly uses business partner, material and batch master data as well as batch classification data created in SAP S/4HANA. CIF is only needed in embedded EWM for the transfer of customer locations in the transit warehousing scenario with SAP TM.</li>\r\n<li>No replication of product valuation data using transaction /SCWM/VALUATION_SET for EWM tolerance checks or split valuation. Embedded EWM accesses directly material valuation data in SAP S/4HANA.</li>\r\n<li>No replication of accounting objects using transaction /SCWM/ACC_IMP_ERP for outbound processing to internal customers like cost centers. Embedded EWM accesses directly accounting data in SAP S/4HANA.</li>\r\n<li>No replication of WBS elements. Embedded EWM directly accesses project master data in SAP S/4HANA.</li>\r\n<li>No replication of dangerous goods, hazardous substances and phrases. Embedded EWM accesses directly PS&amp;S master data in SAP S/4HANA.</li>\r\n<li>No redundant customizing between embedded EWM and other S/4HANA applications. Embedded EWM directly accesses existing SAP S/4HANA customizing for:</li>\r\n<ul>\r\n<li>Catch Weight Tolerance Group</li>\r\n<li>Catch Weight Profile for Catch Weight Quantities</li>\r\n<li>Delivery Priority</li>\r\n<li>Handling Indicator</li>\r\n<li>Handling Unit Type</li>\r\n<li>Incoterms</li>\r\n<li>Packing Group</li>\r\n<li>Quality Inspection Group</li>\r\n<li>Serial Number Profile</li>\r\n<li>Shipping Conditions</li>\r\n<li>Transportation Group</li>\r\n<li>Warehouse Product Group</li>\r\n<li>Warehouse Storage Condition</li>\r\n</ul>\r\n<li>No replication of expected goods receipt (EGR) documents in inbound processes. Embedded EWM directly accesses purchase orders or manufacturing orders in SAP S/4HANA.</li>\r\n<li>Support of changes of the outbound delivery after handing over to EWM</li>\r\n<li>Elimination of outbound delivery requests. Embedded EWM directly creates warehouse requests for outbound deliveries.</li>\r\n<li>Elimination of inbound delivery notifications. Embedded EWM directly creates warehouse requests for inbound deliveries.</li>\r\n<li>Elimination of posting change requests. Embedded EWM directly creates warehouse requests for posting changes.</li>\r\n<li>Elimination of QIE for quality management processes with embedded EWM:</li>\r\n<ul>\r\n<li>Inspection rules are used for triggering quality inspection and detailed planning. They contain both the inspection setup and the inspection relevance checks</li>\r\n<li>Only QM inspection lots are used to run quality management processes</li>\r\n</ul>\r\n<li>Elimination of Customizing for ERP Version Control</li>\r\n<li>Support of synchronous goods movement postings from EWM to MM-IM</li>\r\n<li>Re-use of LE delivery number in EWM warehouse request number</li>\r\n<li>Transparency of storage location assignment in EWM stock type and availability group</li>\r\n</ul>\r\n<p>Compared to decentral SAP EWM, embedded EWM in SAP S/4HANA provides additional functionality:</p>\r\n<ul>\r\n<li>ABC classification based on confirmed warehouse tasks</li>\r\n<li>\r\n<div>New FIORI App: &#8220;Pack Outbound Deliveries&#8221;</div>\r\n</li>\r\n<li>\r\n<div>New FIORI App: &#8220;Count Physical Inventory&#8221;</div>\r\n</li>\r\n<li>\r\n<div>Enhancements to Fiori apps for inbound and outbound deliveries</div>\r\n</li>\r\n<li>\r\n<div>EWM-relevant data part of FIORI App: &#8220;Manage Product Master Data&#8221;</div>\r\n</li>\r\n<li>New Warehouse Management Monitor methods for monitor nodes \"Physical Stock\", \"Warehouse Task\" and \"Posting Change Items\"</li>\r\n<li>Change documents to track changes to storage bin master data</li>\r\n</ul>\r\n<p>Compared to decentral SAP EWM, some functions are not available in embedded EWM in SAP S/4HANA for simplification purposes:</p>\r\n<ul>\r\n<li>Transportation Management in EWM (also known as &#8216;FOM&#8217;)</li>\r\n<li>UIs based on MS Silverlight: the same functionality&#160;is available with UIs&#160;based on SAP UI5 Technology</li>\r\n<li>Labor Demand Planning</li>\r\n<li>SCM Route</li>\r\n<li>Transportation Cross-Docking (based on the SCM routing guide, which does not exist in SAP S/4HANA)</li>\r\n</ul>\r\n<p>Compared to decentral SAP EWM, the following restrictions apply in embedded EWM in SAP S/4HANA:</p>\r\n<ul>\r\n<li>Integration of embedded EWM in SAP S/4HANA with ERP storage locations from other clients or other systems. You can only link the EWM warehouse to&#160;storage locations managed in the same SAP S/4HANA client as the EWM warehouse.&#160;See also SAP Note 1606493 (SAP EWM Deployment Options - Best Practices).</li>\r\n<li>Delivery of customizing entries via client 000. In SAP S/4HANA, EWM customizing is not delivered from client 000. It is recommended to use the business configuration content&#160;available for download in the Best Practice Explorer. Warehouse request customizing is also available as BC sets - see SAP Note 2450387.</li>\r\n<li>Automatic creation of warehouse-specific product master (but mass creation is possible in Warehouse Management Monitor node 'Product Master Data' -&gt; 'Warehouse Attribute')</li>\r\n<li>Dock Appointment Scheduling UI accessed by carriers (but a Web Dynpro application is available)</li>\r\n<li>Integration with embedded GTS in SAP S/4HANA (but integration of EWM in SAP S/4HANA with a decentral GTS is supported)</li>\r\n<li>The following Quality Management functions and processes are not supported yet:</li>\r\n<ul>\r\n<li>Inspection object type 1 (IOT1): Preliminary inspection inbound delivery</li>\r\n<li>Inspection object type 2 (IOT2): Counting inbound delivery</li>\r\n<li>Inspection object type 6 (IOT6): Preliminary inspection handling unit</li>\r\n<li>Recording an inspection decision in RF</li>\r\n<li>Automatically separating sample stock from inspection lot stock&#160;and moving sample stock to a quality work center&#160;for inspection</li>\r\n<li>Recording and tracking quality management workload in labor management</li>\r\n<li>Exceptions triggering workflow&#160;or alert</li>\r\n</ul>\r\n<li>Returns processing&#160;(customer returns &amp; returns in distributed networks) is not supported without using Advanced Returns Management</li>\r\n<li>The following Advanced Returns Management function and process&#160;is not supported yet:</li>\r\n<ul>\r\n<li>Processing of replacement material from supplier</li>\r\n</ul>\r\n<li>Fiori application 'Process E-Commerce Returns' (as customer return inspection is not supported yet)</li>\r\n</ul>\r\n<p><strong>Restrictions of embedded EWM in S/4HANA 1809 compared to functions that are generally available in SAP S/4HANA:</strong></p>\r\n<ul>\r\n<li>Backflush from repetitive manufacturing in EWM-managed storage locations (but repetitive manufacturing with backflush from IM-managed storage location and goods receipt in EWM-managed storage location is supported)</li>\r\n<li>Batch derivation</li>\r\n<li>Batch-specific unit of measure and active ingredients</li>\r\n<li>Interchangeable Manufacturer Parts</li>\r\n<li>International address versions for locations and supply chain units</li>\r\n<li>Special stock indicators other than E (Orders on hand), K (Vendor Consignment), M (Returnable Packaging from Vendor), Q (Project Stock)</li>\r\n<li>Valuated stock in transit (SiT) processes with EWM-managed storage locations (but STO processes without valuated SiT are supported) - see also SAP note 1822497</li>\r\n<li>VMS and JIT processes as described in SAP note 2668075</li>\r\n<li>Fashion Management processes</li>\r\n<li>Inspection lot in the material inspection in Advanced Returns Management integrated with QM, see SAP Note 2259775 and 2481845.</li>\r\n<li>Inspection lots of origin 17 not considered when using \"advanced ATP\" feature.</li>\r\n<li>The serial number handling in Advanced Returns Management is not fully supported. Especially the visibility of the assigned serial numbers on the ARM material inspection is not given.</li>\r\n</ul>\r\n<p>For restrictions and recommendations regarding the usage of user interfaces in embedded EWM in SAP S/4HANA, see SAP note 2348923.</p>\r\n<p>For a list of BI data sources that can be used in the EWM context, see SAP note 2552797.</p>\r\n<p>For a list of BI data sources from the SCM Basis area that can be used in the EWM context, see SAP note 2382662.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SCM-EWM-ANA (Analytics)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D000316)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D038476)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002668150/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002668150/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002668150/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002668150/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002668150/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002668150/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002668150/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002668150/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002668150/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "BasicSettings_EWMinS4_V01.pdf", "FileSize": "274", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125900001684322018&iv_version=0009&iv_guid=00109B36D6A21ED8ADD1ED5817CAC0C9"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2668075", "RefComponent": "IS-A", "RefTitle": "SAP S/4HANA 1809, Automotive: Restriction Note", "RefUrl": "/notes/2668075"}, {"RefNumber": "2659710", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1809: Restriction Note", "RefUrl": "/notes/2659710"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2861813", "RefComponent": "SCM-EWM-QM", "RefTitle": "Quality Management (QM) in embedded EWM S/4HANA system", "RefUrl": "/notes/2861813 "}, {"RefNumber": "2819209", "RefComponent": "SCM-EWM-DLP-BF-MDD", "RefTitle": "Using O002 and I002 process codes in EWM", "RefUrl": "/notes/2819209 "}, {"RefNumber": "2753619", "RefComponent": "SCM-EWM-IF-ERP", "RefTitle": "Control Parameters for ERP Version Control in S4CORE", "RefUrl": "/notes/2753619 "}, {"RefNumber": "2545012", "RefComponent": "CA-QIE", "RefTitle": "EWM-QM integration - Inspection lots of origin 17: Unsupported processes", "RefUrl": "/notes/2545012 "}, {"RefNumber": "2483936", "RefComponent": "SCM-EWM-CNT", "RefTitle": "Additional Information on SAP Best Practices for S/4HANA  – Supply Chain Management for extended warehouse management , on-premise edition", "RefUrl": "/notes/2483936 "}, {"RefNumber": "2899739", "RefComponent": "SCM-YL", "RefTitle": "Integration Scenario TM and EWM: Release information and restrictions for SAP Yard Logistics in SAP S/4HANA", "RefUrl": "/notes/2899739 "}, {"RefNumber": "2885699", "RefComponent": "IS-ADEC-SSP", "RefTitle": "Customer stock \"B\" in EWM", "RefUrl": "/notes/2885699 "}, {"RefNumber": "2806070", "RefComponent": "SCM-EWM-UPG", "RefTitle": "SAP S/4HANA 1909: Release information and restrictions for EWM in SAP S/4HANA", "RefUrl": "/notes/2806070 "}, {"RefNumber": "2783648", "RefComponent": "SCM-EWM-QM", "RefTitle": "Returns inspection process in embedded EWM in SAP S/4HANA", "RefUrl": "/notes/2783648 "}, {"RefNumber": "2775345", "RefComponent": "SCM-EWM-UPG", "RefTitle": "Release information and restrictions of Decentralized EWM on S/4HANA 1809 FPS02", "RefUrl": "/notes/2775345 "}, {"RefNumber": "2659710", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1809: Restriction Note", "RefUrl": "/notes/2659710 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}