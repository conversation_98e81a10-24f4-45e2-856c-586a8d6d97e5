{"Request": {"Number": "33135", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 258, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014641172017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000033135?language=E&token=25E7399FFCB65E6A220D84FFC77EE197"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000033135", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000033135/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "33135"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 24}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.11.2022"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-NET-HTL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Problems with remote access from SAP to Customer system"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Network connection", "value": "XX-SER-NET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-NET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Problems with remote access from SAP to Customer system", "value": "XX-SER-NET-HTL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-NET-HTL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "33135 - Guide for OSS1"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note concerns the setting up of the remote connection via transaction OSS1.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>OSS1, RFC connections to SAPNet R/3 front end, SAPOSS, OSS_RFC, SAProuter</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The remote connection to SAP and the R/3 system are involved.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><br />On April 03, 2006, we deactivated SAPNet - R/3 frontend as a user interface. SAPNet - R/3 frontend, which was introduced in 1995 as SAP's Online Service System (OSS), was SAP's first and, for a long time, its only support system, which customers worldwide accessed using transaction OSS1.<br /><br />Today, the SAProuter connection is still used, among other things, for the following RFC connections:<br />- Problem analysis and/or service delivery in customer systems<br />- Transfer of EarlyWatch Alert data<br /> - Exchange of data using the SAP Notes Assistant<br />- Reservation of service connections<br />- Customer incidents using Solution Manager<br /><br />To install and configure this transaction, proceed exactly as follows:    <br /><br /> 1. 1. Making the technical settings for OSS1 <br />You must configure transaction OSS1 to be able to use it.  Choose \"Parameter\" from the menu bar (-&gt; Techn. Settings) and choose \"Change\".<br /><br />The technical settings for transaction OSS1 are set by default to Walldorf (sapserv3) with the IP address ***********.  If this address does not correspond with the entry in your host file, choose the sapserv3 IP address that is valid for you by choosing the menu option \"SAPRouter at SAP -&gt; Walldorf\". <br />Furthermore, enter your local SAPRouter information in the \"SAPRouter 1\" fields.  Now save the settings.<br /><br />After making these changes, the screen for the technical settings look as follows:<br /><br />Router data for the logon to SAPNet - R/3 frontend<br /><br />+-SAPRouters on the customer side------------------------------------------+<br />| +-SAPRouter 1------------------+ +-SAPRouter 2-----------------+ |<br />| | Name my_saprouter | | Name | |<br />| | IP address x.x.x.x | | IP address | |<br />| | Instance no. 99 | | Instance no. | |<br />| +------------------------------+ +-----------------------------+ |<br />+---------------------------------------------------------------------+<br /><br />+-SAPRouter and OSS Message Server at SAP----------------------------+<br />| +-SAPRouter at SAP------------+ +-OSS Message Server----------+ |<br />| | Name sapservX | | Name oss001 | |<br />| | IP address x.x.x.x | | DB name O01 | |<br />| | Instance no. 99 | | Instance no. 01 | |<br />| +------------------------------+ +-----------------------------+ |<br />+---------------------------------------------------------------------+<br /><br />Comment:<br />Replace sapservX with the following values:   <br /><br />sapserv1 (***************) connection via Internet VPN<br />sapserv2 (*************) connection via Internet SNC<br />sapserv3 (***********) for customers with connection to Germany<br />sapserv4 (************) for customers in America<br />sapserv5 (************) for customers in Japan<br />sapserv7 (*************) for customers in Asia<br />sapserv9 (***************) for customers in Asia<br /><br />Choose \"Start Logon to SAPNet - R/3 Frontend\".  If the system issues message S1 452, there are errors in the operating system configuration.  In this case, see appendix A.<br />When you install an access authorization file \"saprouttab\", you should ensure that all of your front ends and R/3 servers can establish a connection to sapserv3, service sapdp99.  Appendix E contains examples of saprouttabs.  For more information on the SAPRouter, refer to the SAPRouter documentation (Note 30289).<br />Try it again until the dialog box \"Please select a group\" appears.  If the dialog box \"Please select a group\" is displayed, the configuration for transaction OSS1 is correct.  You can then proceed with the next section.<br /><br />Comment:<br />When you try to log on to SAPNet - R/3 frontend, the system issues an error message indicating that you are no longer allowed to log on to SAPNet - R/3 frontend. <br /><br /><br /> 2. Further questions?<br />As soon as you have carried out the steps described above, the RFC destination \"SAPOSS\" (transaction SM59) is updated automatically. If you have any questions or problems,<br /><br /> o create a customer incident under the component<br /> XX-SER-NET with the short text \"SAP Service Marketplace via<br /> Extranet...\"<br /><br /> o&#x00A0;or call your local Customer Interaction Center (CIC):<br /> <a target=\"_blank\" href=\"https://support.sap.com/en/contact-us.html#section_634299596\">https://support.sap.com/en/contact-us.html#section_634299596</a><br /><br /><br />Appendix A<br /><br />If, when you try to log on to SAPNet - R/3 frontend with <br />transaction OSS1, the system displays message S1 452, there is an incorrect setting somewhere (either in the technical settings for OSS1 or at operating system level).<br />To find out why the connection to the message server was unsuccessful, choose Tools (Case, Test ( Developer trace (transaction ST11). The trace contains an entry for dev_lg. This file contains the error log. The LOCATION line, if available, contains the host on which the error occurred. The problem description is found in the ERROR line.  If you cannot find the entry dev_lg, check whether the program \"lgtst\" exists (see appendix B).<br /><br />Examples of the contents of dev_lg:<br /><br />************************************************************************<br />*<br />* ERROR partner not reached (host abc.def.gh.i, service sapdp99)<br />*<br />* TIME Thu Aug 10 09:17:57 1995<br />* RELEASE 21J<br />* COMPONENT NI (network interface)<br />* VERSION 15<br />* RC -10<br />* MODULE niuxi.c<br />* LINE 773<br />* DETAIL NiPConnect<br />* SYSTEM CALL connect<br />* ERRNO 239<br />* ERRNO TEXT Connection refused<br />* COUNTER 1<br />*<br />************************************************************************<br />Here, the system could not reach the SAPRouter. For example, no SAProuter could be found under service 99 (port 3299) on the host with the IP address abc.def.gh.i.  The SAPRouter process does not work or the IP address was not configured correctly in OSS1. <br /><br />************************************************************************<br />*<br />* ERROR service 'sapdp99' unknown<br />*<br />* TIME Thu Aug 10 9:22:00 AM 1995<br />* RELEASE 30A<br />* COMPONENT NI (network interface)<br />* VERSION 17<br />* RC -3<br />* MODULE niuxi.c<br />* LINE 404<br />* DETAIL NiPServToNo<br />* SYSTEM CALL getservbyname<br />* COUNTER 1<br />*<br />************************************************************************<br />This message indicates that the service sapdp99 was not entered in /etc/services.  Add the entry in /etc/services.  This must be available on all R/3 servers and frontends.<br /><br />************************************************************************<br />*<br />* LOCATION SapRouter on abc.def.gh.i<br />* ERROR route permission denied (XXXXXXXX to sapservX, sapdp99)<br />*<br />* TIME Thu Aug 10 9:37:44 AM 1995<br />* RELEASE 30A<br />* COMPONENT NI (network interface)<br />* VERSION 17<br />* RC -94<br />* MODULE nixxrout.c<br />* LINE 1426<br />* COUNTER 1<br />*<br />************************************************************************<br />The file saprouttab, which contains the valid connections, is incorrect.  The SAPRouter on the host abc.def.gh.i does not set up the connection to sapservX.  Check the SAPRouter file saprouttab.  This should contain every R/3 server and frontend  (see also appendix E).<br /><br />************************************************************************<br />*<br />* LOCATION SapRouter on abc.def.gh.i<br />* ERROR internal error<br />*<br />* TIME Thu Aug 10 10:50:18 AM 1995<br />* RELEASE 21J<br />* COMPONENT NI (network interface)<br />* VERSION 15<br />* RC -93<br />* MODULE niuxi.c<br />* LINE 773<br />* DETAIL NiPConnect<br />* SYSTEM CALL connect<br />* ERRNO 242<br />* ERRNO TEXT No route to host<br />* COUNTER 1<br />*<br />************************************************************************<br />This error message indicates that the host abc.def.gh.i cannot process the IP address of the next host configured in OSS1.  If the SAPRouter error message appears and the next host is sapservX, check the address for sapservX.  OSS1 is delivered with the default settings sapserv3 and IP address ***********.  Customers in the U.S.A. are normally connected to sapserv4, IP address ************.  If required, change the technical settings of OSS1 accordingly.<br /><br />************************************************************************<br />*<br />* ERROR internal error<br />*<br />* TIME Thu Nov 23 00:11:20 1995<br />* RELEASE 21J<br />* COMPONENT NI (network interface)<br />* VERSION 15<br />* RC -1<br />* COUNTER 1<br />*<br />************************************************************************<br />This message shows that the instance number entered does not agree with at least one of the technical settings for the SAPRouter defined in OSS1.  The default for the instance number of the SAPRouter is 99. Under no circumstances should you enter the instance number of your R/3 system for the SAPRouter.  You need to specify instance number 99 for sapservX. Otherwise, it is not possible to log on to SAPNet - R/3 frontend.<br /><br />************************************************************************<br />*<br />* LOCATION SapRouter on sapservX<br />* ERROR route permission denied (XXXXXX to oss002, sapmsO01)<br />*<br />* TIME Mon Nov 27 19:25:54 1995<br />* RELEASE 30A<br />* COMPONENT NI (network interface)<br />* VERSION 15<br />* RC -94<br />* MODULE nixxrout.c<br />* LINE 1390<br />* COUNTER 1<br />*<br />************************************************************************<br />An incorrect server was entered as message server 001, in this example, the server oss002.  The message server for O01 is oss001.  Change the technical settings for transaction OSS1 accordingly. <br /><br /><br />Appendix B<br /><br />The messages from transaction OSS1 (error messages and information) are given in the following list.  Each message is described briefly.<br /><br />-----------------------------------------------------------------------<br />|No.| Message Text |<br />-----------------------------------------------------------------------<br />|450| Maintain technical settings first. <br />|452| Unable to connect to SAPNet - R/3 Frontend message server.<br />|454| E: Unable to start SAPGUI. <br />|455| SAPGUI was started. <br />|456| Specify a server name. <br />|457| Specify an IP address. <br />|458| Specify an instance number. <br />|459| Specify a database name. <br />|460| No authorization to log on to Online Service System.<br />|461| No authorization to maintain technical settings. <br />|462| E: RFC destination could not be generated.<br />-----------------------------------------------------------------------<br /><br />Number 450: Maintain technical settings first.  <br />You can only log on to SAPNet - R/3 Frontend if <br />the technical settings are maintained.  The technical settings determine the network path from the customer R/3 system to the online service system. <br /><br />Number 452: Unable to connect to SAPNet - R/3 frontend message server. <br />This message appears if the connection to the SAPNet - R/3 frontend message server was not possible (system name O01, server oss001).  This may have different reasons (see appendix A).<br /><br />Number 454: E: Unable to start SAPGUI. <br />Transaction OSS1 could not start the SAPGUI (not SAPTEMU), either because the program does not exist in the specified path, or because the execute permission is not set correctly.  Check whether the SAPGUI exists; SAPTEMU alone is not sufficient.<br /><br />Number 455: SAPGUI was started. <br />This is not an error message. It merely informs you that an additional SAPGUI was started to establish a connection to SAPNet - R/3 frontend. <br /><br />Number 456: Specify a server name. <br />The server name was omitted from the technical settings. <br /><br />Number 457: Specify an IP address. <br />The IP address was omitted from the technical settings. <br /><br />Number 458: Specify an instance number. <br />The instance number was omitted from the technical settings. <br /><br />Number 459: Specify a database name. <br />The database name for the Online Service System (001) was omitted from the technical settings.<br /><br />Number 460: No authorization to log on to Online Service System<br />You do not have authorization to call transaction OSS1. Up to Release 2.2F: The authorization S_TSKH_ADM is checked for value 1.  After Release 2.2F: For transaction OSS1, there are two special authorization profiles (see appendix D).<br /><br />Number 461: No authorization to maintain technical settings. <br />You do not have the authorization to maintain the technical settings  (see appendix D).<br /><br />Number 462: E: RFC destination could not be generated.<br />In Releases 2.2*, you can ignore this message.  When saving the technical settings, an attempt is made to generate the RFC destination SAPOSS.  The length of an RFC destination is limited in 2.2*, and the maximum length was exceeded by the parameters of the technical settings.<br /><br /><br /><br /><br />Appendix C<br /><br />As of Release 2.2F, there are two different authorization profiles for transaction OSS1:  S_OSS1_START and S_OSS1_ADMIN.<br /><br />S_OSS1_START authorizes you to call transaction OSS1 and to log on to the Online Service System.  In addition, S_OSS1_ADMIN contains <br />the authorization to maintain the technical settings for the transaction.  <br /><br />The technical settings of OSS1 must be made at least once.  Therefore, add S_OSS1_ADMIN to your user profile, log off, and then log on again afterwards. <br /><br /> Appendix E<br /><br />Prerequisites:<br />(A TCP/IP connection can be established between the SAProuter on the customer system<br />and the SAProuter on sapserv3 in Walldorf. <br /><br />(The SAProuter process must be started on the server that is registered with SAP:<br /><br /><br />saprouter -r -R saprouttab &amp;<br /><br />Example of the \"saprouttab\" file with minimum configuration: <br /><br /># saprouttab - Example<br />#<br /># Allows connections from the entire customer network to sapservX<br /># and therefore to the Online Service System via SAProuter port 3299.<br />P * sapservX sapdp99 *<br /># Allows connections from sapserv3 to the entire customer network,<br /># for example, for EarlyWatch or First Level Support.<br />P sapservx * * *<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (C3303621)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (C3303606)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000033135/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000033135/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000033135/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000033135/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000033135/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000033135/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000033135/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000033135/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000033135/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "982045", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/982045"}, {"RefNumber": "89492", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/89492"}, {"RefNumber": "883401", "RefComponent": "XX-SER-NET-RCSC", "RefTitle": "RFC destination SAPOSS without description", "RefUrl": "/notes/883401"}, {"RefNumber": "766505", "RefComponent": "XX-SER-NET", "RefTitle": "OSS1: Changes to RFC connection SAPOSS", "RefUrl": "/notes/766505"}, {"RefNumber": "35010", "RefComponent": "XX-SER-NET", "RefTitle": "Service connections: Composite note (overview))", "RefUrl": "/notes/35010"}, {"RefNumber": "22244", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/22244"}, {"RefNumber": "200436", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/200436"}, {"RefNumber": "2000132", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/2000132"}, {"RefNumber": "1698817", "RefComponent": "XX-SER-NET-NEW", "RefTitle": "Network Provider for Remote Connection in China", "RefUrl": "/notes/1698817"}, {"RefNumber": "137342", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/137342"}, {"RefNumber": "122827", "RefComponent": "XX-SER-NET-RCSC", "RefTitle": "How to establish an OSS connection in Poland", "RefUrl": "/notes/122827"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "812386", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "RFC connections to SAPNet R/3 front end", "RefUrl": "/notes/812386 "}, {"RefNumber": "1698817", "RefComponent": "XX-SER-NET-NEW", "RefTitle": "Network Provider for Remote Connection in China", "RefUrl": "/notes/1698817 "}, {"RefNumber": "35010", "RefComponent": "XX-SER-NET", "RefTitle": "Service connections: Composite note (overview))", "RefUrl": "/notes/35010 "}, {"RefNumber": "766505", "RefComponent": "XX-SER-NET", "RefTitle": "OSS1: Changes to RFC connection SAPOSS", "RefUrl": "/notes/766505 "}, {"RefNumber": "883401", "RefComponent": "XX-SER-NET-RCSC", "RefTitle": "RFC destination SAPOSS without description", "RefUrl": "/notes/883401 "}, {"RefNumber": "122827", "RefComponent": "XX-SER-NET-RCSC", "RefTitle": "How to establish an OSS connection in Poland", "RefUrl": "/notes/122827 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}