{"Request": {"Number": "2347011", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 726, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000013863312017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002347011?language=E&token=53183981F56B7FC2BD0C5BA971FD970A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002347011", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2347011"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "29.07.2016"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-RA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Rental Accounting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Rental Accounting", "value": "RE-FX-RA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-RA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2347011 - RECN: Direct reversal"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The system provides the option in the real estate contract to reverse the periodic postings for the contract directly from the contract using a new menu function. Previously, only periodic posting was possible directly from the contract.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>RERAPP RERAPP_SINGLE RERAPPRV RERAPPRV_SINGLE</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This is a functional enhancement.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The system provides a new transaction RERAPPRV_SINGLE, for which an authorization other than RERAPPRV can also be assigned. However, the transaction cannot be called directly, but only using the menu in the contract (Extras -&gt; Post -&gt; Reverse Contract). Here, the company code and the contract are set by default and are not ready for input.</p>\r\n<p>Also implement SAP Note 2346751 if the valuation of leasing contracts (IFRS16, FASB842) is relevant for you.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D027300)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D026407)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002347011/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002347011/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002347011/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002347011/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002347011/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002347011/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002347011/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002347011/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002347011/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2255555", "RefComponent": "RE-FX-LA", "RefTitle": "Valuation of leasing contracts (SAP Contract and Lease Management based on SAP RE-FX)", "RefUrl": "/notes/2255555 "}, {"RefNumber": "2346751", "RefComponent": "RE-FX-LA", "RefTitle": "Leasing: Posting a valuation and canceling it from a contract", "RefUrl": "/notes/2346751 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_FIN", "From": "618", "To": "618", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "720", "To": "720", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "EA-FIN", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "EA-FIN", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "616", "To": "616", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "S4CORE 100", "SupportPackage": "SAPK-10003INS4CORE", "URL": "/supportpackage/SAPK-10003INS4CORE"}, {"SoftwareComponentVersion": "EA-APPL 606", "SupportPackage": "SAPK-60618INEAAPPL", "URL": "/supportpackage/SAPK-60618INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 616", "SupportPackage": "SAPK-61611INEAAPPL", "URL": "/supportpackage/SAPK-61611INEAAPPL"}, {"SoftwareComponentVersion": "EA-FIN 617", "SupportPackage": "SAPK-61713INEAFIN", "URL": "/supportpackage/SAPK-61713INEAFIN"}, {"SoftwareComponentVersion": "SAP_FIN 618", "SupportPackage": "SAPK-61804INSAPFIN", "URL": "/supportpackage/SAPK-61804INSAPFIN"}, {"SoftwareComponentVersion": "EA-FIN 700", "SupportPackage": "SAPK-70010INEAFIN", "URL": "/supportpackage/SAPK-70010INEAFIN"}, {"SoftwareComponentVersion": "SAP_FIN 720", "SupportPackage": "SAPK-72006INSAPFIN", "URL": "/supportpackage/SAPK-72006INSAPFIN"}, {"SoftwareComponentVersion": "SAP_FIN 730", "SupportPackage": "SAPK-73005INSAPFIN", "URL": "/supportpackage/SAPK-73005INSAPFIN"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_FIN", "NumberOfCorrin": 1, "URL": "/corrins/0002347011/15841"}, {"SoftwareComponent": "S4CORE", "NumberOfCorrin": 1, "URL": "/corrins/0002347011/19773"}, {"SoftwareComponent": "EA-FIN", "NumberOfCorrin": 1, "URL": "/corrins/0002347011/15842"}, {"SoftwareComponent": "EA-APPL", "NumberOfCorrin": 2, "URL": "/corrins/0002347011/229"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; S4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 100&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-10002INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/>See EA_APPL<br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_FIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 618&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-61803INSAPFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 720&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-72005INSAPFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 730&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-73003INSAPFIN - SAPK-73004INSAPFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>See EA_APPL<br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; EA-FIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 617&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-61701INEAFIN - SAPK-61712INEAFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 700&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-70009INEAFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>See EA_APPL<br/><br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; EA-APPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP R/3 Enterpr...|<br/>| Release 606&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-60601INEAAPPL - SAPK-60617INEAAPPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 616&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-61610INEAAPPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/><br/>For an advance correction, perform the following manual post-implementation steps:<br/><br/><br/><br/><br/>Report RFRERAPPRV_SINGLE:<br/>Call the new report RFRERAPPRV_SINGLE in transaction SE80. Choose Goto - &gt;Attributes and create the title:<br/>'Reverse Contract Postings for a Contract'.<br/><br/>Choose Goto -&gt; Text elements and create the following text symbols:<br/><br/>Symbol   Text    Max. length<br/>DOC    Doc. Selection  14<br/>OUT    Output    7<br/>PAR    Parallel Processing 25<br/>PRT    Revers.Process  14<br/>PST    Posting Data  13<br/>T11    Process    16<br/><br/>Choose Goto -&gt; Text elements and create the following selection texts:<br/><br/>Symbol   Text       Dict. indicator<br/>P_BUKRS   Company Code     X<br/>P_GRID   Use Grid Control   X<br/>P_MSGLST  Only Display Error Log<br/>P_PDATE   Posting Date     X<br/>P_PERIOD  Posting Period     <br/>P_PMODE   Mode<br/>P_PROCED  Reason for Reversal<br/>P_PTYPE   Type of Posting Run<br/>P_RECNNR  Contract       X<br/>S_FYEAR   Fiscal Year     X<br/>S_PDATE   Posting Date     X<br/>S_PERIOD  Posting Period     X<br/>S_PROID   Process ID<br/>S_REFID   Ref. Key Document    X<br/><br/>Save and activate the report.<br/><br/><br/>Create a new transaction RERAPPRV_SINGLE.<br/>To do this, copy transaction RERAPPRV to RERAPPRV_SINGLE.<br/>Transaction text: Reverse periodic posting for a contract<br/>Program: RFRERAPPRV_SINGLE<br/><br/>Save the transaction.<br/><br/><br/><br/>GUI status<br/>Call the GUI status WB_DEFAULT in the function group REGC in transaction SE80 in change mode.<br/><br/>First, expand the menu bar and delete the entries for the two function  codes RERA_POST and RERA_EVPO under \"Extras\". Confirm the warning  message stating that the changes refer to several statuses. Now add a  new empty entry in the same place. Leave the CODE empty there and enter  the text 'Post'. After pressing Enter, you can open another submenu by double-clicking the arrow. Now insert the following:<br/><br/>Code   Text<br/>RERA_POST  Post Contract<br/>RERA_REV  Reverse Contract<br/>Add a separator line after these two entries (Edit -&gt; Insert -&gt;&gt; Separator).<br/>RERA_EVPO  Post Valuation<br/>RERA_EVRV  Reverse Valuation<br/>For the two new function codes, enter the following in the dialog box:<br/><br/>Function Code: RERA_REV<br/>Functional Type: leave blank<br/>Function Text: Reverse Contract<br/>Info Text: Cancel periodic posting for contract<br/>Switch: RE_SFWS_EHP5_SC<br/>Reaction: Display<br/>Icon Name: leave blank<br/>Icon Text: leave blank<br/>Fastpath: Leave empty, assigned by the system<br/><br/>Function code: RERA_EVRV<br/>Functional Type: leave blank<br/>Function Text: Reverse Valuation<br/>Info Text: Reverse valuation posting for contract<br/>Switch: leave blank<br/>Reaction: leave blank<br/>Icon Name: leave blank<br/>Icon Text: leave blank<br/>Fastpath: Leave empty, assigned by the system<br/><br/>No changes need to be made in the application toolbar.<br/>Now expand the function key bar.<br/><br/>Function key  Code<br/>Shift+F8   RERA_POST<br/>Shift+F11   RERA_EVPO<br/>Shift+F12   RERA_REV<br/>Ctrl-F11   RERA_EVRV<br/><br/><br/>Now perform the same changes in the GUI statuses WB_DEFAULT_APPR and  WB_DEFAULT_UNLO and also activate the new function codes for these two statuses.<br/><br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 5, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 4, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 8, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EA-APPL", "ValidFrom": "605", "ValidTo": "616", "Number": "1916149 ", "URL": "/notes/1916149 ", "Title": "Direct posting from the contract: Authorization RERAPP", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "605", "ValidTo": "616", "Number": "2084421 ", "URL": "/notes/2084421 ", "Title": "Direct posting: Contracts with approvals", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "2085047 ", "URL": "/notes/2085047 ", "Title": "CC2014 (D6463) : Creating a single invoice directly from the contract", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "2110846 ", "URL": "/notes/2110846 ", "Title": "Deactivating certain functions if object has been archived", "Component": "RE-FX"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "2296200 ", "URL": "/notes/2296200 ", "Title": "Enhancement of internal function", "Component": "RE-FX"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "2318067 ", "URL": "/notes/2318067 ", "Title": "Enhancement of internal function", "Component": "RE-FX"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "2347011 ", "URL": "/notes/2347011 ", "Title": "RECN: Direct reversal", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "616", "Number": "2333713 ", "URL": "/notes/2333713 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "2085047 ", "URL": "/notes/2085047 ", "Title": "CC2014 (D6463) : Creating a single invoice directly from the contract", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "2110846 ", "URL": "/notes/2110846 ", "Title": "Deactivating certain functions if object has been archived", "Component": "RE-FX"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "2296200 ", "URL": "/notes/2296200 ", "Title": "Enhancement of internal function", "Component": "RE-FX"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "2318067 ", "URL": "/notes/2318067 ", "Title": "Enhancement of internal function", "Component": "RE-FX"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "1916149 ", "URL": "/notes/1916149 ", "Title": "Direct posting from the contract: Authorization RERAPP", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2085047 ", "URL": "/notes/2085047 ", "Title": "CC2014 (D6463) : Creating a single invoice directly from the contract", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2110846 ", "URL": "/notes/2110846 ", "Title": "Deactivating certain functions if object has been archived", "Component": "RE-FX"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2296200 ", "URL": "/notes/2296200 ", "Title": "Enhancement of internal function", "Component": "RE-FX"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2318067 ", "URL": "/notes/2318067 ", "Title": "Enhancement of internal function", "Component": "RE-FX"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "700", "Number": "2084421 ", "URL": "/notes/2084421 ", "Title": "Direct posting: Contracts with approvals", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "700", "Number": "2333713 ", "URL": "/notes/2333713 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "700", "Number": "2347011 ", "URL": "/notes/2347011 ", "Title": "RECN: Direct reversal", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2085047 ", "URL": "/notes/2085047 ", "Title": "CC2014 (D6463) : Creating a single invoice directly from the contract", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2110846 ", "URL": "/notes/2110846 ", "Title": "Deactivating certain functions if object has been archived", "Component": "RE-FX"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2296200 ", "URL": "/notes/2296200 ", "Title": "Enhancement of internal function", "Component": "RE-FX"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2318067 ", "URL": "/notes/2318067 ", "Title": "Enhancement of internal function", "Component": "RE-FX"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2296200 ", "URL": "/notes/2296200 ", "Title": "Enhancement of internal function", "Component": "RE-FX"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2318067 ", "URL": "/notes/2318067 ", "Title": "Enhancement of internal function", "Component": "RE-FX"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2333713 ", "URL": "/notes/2333713 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-RA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2347011 ", "URL": "/notes/2347011 ", "Title": "RECN: Direct reversal", "Component": "RE-FX-RA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "618", "ValidTo": "618", "Number": "2296200 ", "URL": "/notes/2296200 ", "Title": "Enhancement of internal function", "Component": "RE-FX"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "618", "ValidTo": "618", "Number": "2318067 ", "URL": "/notes/2318067 ", "Title": "Enhancement of internal function", "Component": "RE-FX"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "618", "ValidTo": "730", "Number": "2333713 ", "URL": "/notes/2333713 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-RA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "618", "ValidTo": "730", "Number": "2347011 ", "URL": "/notes/2347011 ", "Title": "RECN: Direct reversal", "Component": "RE-FX-RA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2296200 ", "URL": "/notes/2296200 ", "Title": "Enhancement of internal function", "Component": "RE-FX"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2318067 ", "URL": "/notes/2318067 ", "Title": "Enhancement of internal function", "Component": "RE-FX"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "730", "ValidTo": "730", "Number": "2296200 ", "URL": "/notes/2296200 ", "Title": "Enhancement of internal function", "Component": "RE-FX"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "730", "ValidTo": "730", "Number": "2318067 ", "URL": "/notes/2318067 ", "Title": "Enhancement of internal function", "Component": "RE-FX"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}