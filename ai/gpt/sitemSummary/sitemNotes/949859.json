{"Request": {"Number": "949859", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 487, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005587482017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000949859?language=E&token=0BEDF208650E2CF6BD8ACFDD41D78703"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000949859", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000949859/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "949859"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.08.2006"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-OT-OLAP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Analyzing Data"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Explorer", "value": "BW-BEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "OLAP Technology", "value": "BW-BEX-OT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Analyzing Data", "value": "BW-BEX-OT-OLAP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT-OLAP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "949859 - RSTT short dump \"DB_READ_PROBLEMS\" when recording traces"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The administrator activated a user to record traces. While the process to be traced is executed, it terminates with the \"DB_READ_PROBLEMS\" short dump.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RSTT, trace, termination, trace tool, query</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error.<br />During execution, the system accesses an incorrect table in which the executing user activated for tracing does not necessarily require an entry.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><UL><LI>SAP NetWeaver 2004s BI</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 09 for SAP NetWeaver 2004s BI (BI Patch 09 or SAPKW70009) into your BI system. The Support Package is available once N ote 0914303 with the short text \"SAPBINews BI 7.0 Support Package 09\", which describes this Support Package in more detail, has been released for customers.<br /><br />In urgent cases, you can use the correction instructions.<br />To provide information in advance, the notes mentioned above may already be available before the Support Package has been released. However, the short text still contains the words \"preliminary version\" in this case.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BEX-OT (OLAP Technology)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D037569)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D037569)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000949859/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000949859/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000949859/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000949859/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000949859/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000949859/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000949859/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000949859/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000949859/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "970981", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Negative runtimes when you run traces", "RefUrl": "/notes/970981"}, {"RefNumber": "950831", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RSTT new windows: Standard package/job maintenance", "RefUrl": "/notes/950831"}, {"RefNumber": "941152", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RSTT: Saving not confirmed in CATT wizard", "RefUrl": "/notes/941152"}, {"RefNumber": "940870", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RSTT: Subsequent change to test jobs does not work", "RefUrl": "/notes/940870"}, {"RefNumber": "940869", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RSTT: Termination for call transformation due to incorrect parameter", "RefUrl": "/notes/940869"}, {"RefNumber": "916815", "RefComponent": "BW-BEX-OT", "RefTitle": "RSTT: Test packages and jobs cannot be saved correctly", "RefUrl": "/notes/916815"}, {"RefNumber": "916359", "RefComponent": "BW-BEX-OT", "RefTitle": "Message handler messages when a trace is running", "RefUrl": "/notes/916359"}, {"RefNumber": "914864", "RefComponent": "BW-BEX-OT", "RefTitle": "User activated for tracing does not create any traces", "RefUrl": "/notes/914864"}, {"RefNumber": "914303", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews NW BW 7.00 ABAP SP9", "RefUrl": "/notes/914303"}, {"RefNumber": "906452", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Trace terminates because of an exception that was not caught", "RefUrl": "/notes/906452"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "914303", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews NW BW 7.00 ABAP SP9", "RefUrl": "/notes/914303 "}, {"RefNumber": "950831", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RSTT new windows: Standard package/job maintenance", "RefUrl": "/notes/950831 "}, {"RefNumber": "970981", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Negative runtimes when you run traces", "RefUrl": "/notes/970981 "}, {"RefNumber": "916359", "RefComponent": "BW-BEX-OT", "RefTitle": "Message handler messages when a trace is running", "RefUrl": "/notes/916359 "}, {"RefNumber": "940869", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RSTT: Termination for call transformation due to incorrect parameter", "RefUrl": "/notes/940869 "}, {"RefNumber": "941152", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RSTT: Saving not confirmed in CATT wizard", "RefUrl": "/notes/941152 "}, {"RefNumber": "940870", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RSTT: Subsequent change to test jobs does not work", "RefUrl": "/notes/940870 "}, {"RefNumber": "916815", "RefComponent": "BW-BEX-OT", "RefTitle": "RSTT: Test packages and jobs cannot be saved correctly", "RefUrl": "/notes/916815 "}, {"RefNumber": "914864", "RefComponent": "BW-BEX-OT", "RefTitle": "User activated for tracing does not create any traces", "RefUrl": "/notes/914864 "}, {"RefNumber": "906452", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Trace terminates because of an exception that was not caught", "RefUrl": "/notes/906452 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "710", "To": "710", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW_VIRTUAL_COMP 700", "SupportPackage": "SAPK-70009INVCBWTECH", "URL": "/supportpackage/SAPK-70009INVCBWTECH"}, {"SoftwareComponentVersion": "SAP_BW 700", "SupportPackage": "SAPKW70009", "URL": "/supportpackage/SAPKW70009"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "NumberOfCorrin": 2, "URL": "/corrins/0000949859/654"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}