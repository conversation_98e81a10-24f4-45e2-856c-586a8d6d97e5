{"Request": {"Number": "788218", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 705, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000788218?language=E&token=3545E8C2C68EBC9AE67BF2F23EEFFEAF"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000788218", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000788218/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "788218"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 40}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "05.02.2018"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-RDM"}, "SAPComponentKeyText": {"_label": "Component", "value": "README: Upgrade Supplements"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "README: Upgrade Supplements", "value": "BC-UPG-RDM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-RDM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "788218 - Add. info. on upgrading to SAP ERP Central Component 5.0 SR1"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Errors in the upgrade or update procedure or in the upgrade guides; preparations for the upgrade or update; additional information to the upgrade guide.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Update, upgrade, SUM, Software Update Manager, SAP ERP Central Component 5.00 Support Release 1</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>*</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><br /><strong>CAUTION:</strong><br />This note is updated regularly!<br />Therefore, you should read it again immediately before starting the upgrade.<br /><br /></p>\r\n<p><strong>What information can I expect from this note?</strong></p>\r\n<p>This note describes problems that may occur during the system upgrade and provides information on how to solve them. This usually takes the form of references to other notes.<br />The main purpose of this note is to prevent data loss, upgrade shutdowns, and long runtimes.<br />It deals with database-independent problems only.<br /><br /></p>\r\n<p><strong>Which additional notes do I require in preparation for the upgrade?</strong></p>\r\n<p>This depends on the functions that you are using. You will need one or several of the following notes:<br /><br />Short text................................................ Note number<br />_____________________________________________________________________<br />Additional information on upgrading to WAS 6.40 MaxDB .........669656<br />Additional Information: Upgrade to SAP Web AS 6.40.............661569<br />DB6: Additions to upgrade (based) on SAP Web AS 6.40...........662191<br />DB2-z/OS: Additions upgrade to Basis 6.40......................661252<br />Add. info. on upgrade to SCM41 SRM40 ECC50 MSSQL...............737115<br />Add. info. on upgrading to SAP Web 6.40 ORACLE.................662219<br />Additional information on the upgrade to ECC 5.0...............700675<br />_____________________________________________________________________<br />Repairs for upgrade to Basis 640 ............................. 663240<br />Corrections for for R3up Version 640 ......................... 663258<br />_____________________________________________________________________<br />OCS: Known problems with Supp.Packages in Basis Rel.6.40 ......672651<br />_____________________________________________________________________</p>\r\n<p>Use the Software Update Manager 1.0 for an update or upgrade to SAP ERP Central Component 5.0 SR1. You can find the latest SUM guide, the latest central SAP Note (including database-relevant SAP Notes), and further information such as SAP Community information on SUM at: <a target=\"_blank\" href=\"http://support.sap.com/sltoolset\">http://support.sap.com/sltoolset</a> -&gt; System Maintenance.</p>\r\n<p>&#160;</p>\r\n<p>&#160;</p>\r\n<p><strong>Contents</strong></p>\r\n<p>I/ ...... Keyword<br />II/ ..... Important General Information<br />III/ .... Corrections to the Guide<br />IV/ ..... Errors on the CD-ROM<br />V/ ...... Preventing Data Loss, Upgrade Shutdown, and Long Runtimes<br />VI/ ..... Preparing the Upgrade<br />VII/..... Problems During the PREPARE and Upgrade Phases<br />VIII/ ... Problems After the Upgrade<br />IX/ ..... Chronological Summary<br /><br /></p>\r\n<p><strong>I/ keyword</strong></p>\r\n<p>Keywords are no longer applicable.<br />(For information only: The R3up keyword was 160421.)</p>\r\n<p>&#160;</p>\r\n<p><strong>II/ Important General Information</strong></p>\r\n<p><strong>**************************************************</strong></p>\r\n<p>The former tool R3up was replaced by the Software Update Manager (SUM). See also SAP Note 1589311 and the blog:<br /><a target=\"_blank\" href=\"https://blogs.sap.com/2012/11/07/software-update-manager-sum-introducing-the-tool-for-software-maintenance/\">https://blogs.sap.com/2012/11/07/software-update-manager-sum-introducing-the-tool-for-software-maintenance/</a>.</p>\r\n<p>Use the Software Update Manager 1.0 for an update or upgrade to SAP ERP Central Component 5.0 SR1.</p>\r\n<p>You can find the latest SUM guide, the latest central SAP Note (including database-relevant SAP Notes), and further information such as SAP Community information on SUM at: <a target=\"_blank\" href=\"http://support.sap.com/sltoolset\">http://support.sap.com/sltoolset</a> -&gt; System Maintenance.</p>\r\n<p><strong>**************************************************</strong></p>\r\n<p>&#160;</p>\r\n<p><br />-----------------------&lt; D025323 MAY/24/02 &gt;--------------------<br /><strong>Corrections and Repairs for the Upgrade</strong><br />Before the upgrade, you must check whether a new version of the R3up exists for your specific upgrade.<br />For more information, see <strong>Note 663258</strong>.<br /><br />-----------------------&lt; D021371 NOV/29/05 &gt;--------------------<br /><strong>Upgrade on Linux x86_64: Correct R3up Version</strong><br />For SAP systems installed on Linux x86_64, there is no 64Bit R3up version. You need to use R3up for Linux x86 32-Bit.<br />For more information on how to use the R3up version in combination with the Kernel DVD, see Note <strong>893352</strong>.<br /><br />-----------------------&lt; D034302 DEZ/03/03 &gt;--------------------<br /><strong>Windows only: Execute program R3dllins.exe</strong><br />The 6.40 kernel is compiled with the new version of MS compiler and requires additional libraries for operation. To prevent problems during and after the upgrade, you must execute program R3dllins.exe on your central host, all application hosts, and on the remote shadow host, if you are planning to use one.<br />You can find the program on the Upgrade Master CD in the NT\\I386\\NTPATCH folder. It must be executed before you start PREPARE and directly from the NTPATCH folder (it can be shared). Copying and executing the program<br />will not work!<br /><br />-----------------------&lt; D028310 JUL/19/02 &gt;---------------------<br /><strong>Problems with the Shadow Instance.</strong><br />The following Notes contain information about problems with the shadow instance:</p>\r\n<ul>\r\n<li><strong>525677</strong>: Problems when starting the shadow instance</li>\r\n</ul>\r\n<ul>\r\n<li><strong>430318</strong>: Remote shadow instance on a different operating system</li>\r\n</ul>\r\n<p><br />------------------------&lt; D042621 FEB/02/05 &gt;-------------------------<br /><strong>LSWM now part of SAP_BASIS</strong><br />As of SAP Web AS 6.20, LSMW is part of SAP_BASIS. If you are using LSMW and your source release is based on SAP Web AS 6.10 or lower, do not implement LSMW after the upgrade.<br />For more information, see <strong>Note 673066</strong>.<br /><br />---------------------------------------------------------------------<br /><br /><br /><br /></p>\r\n<p><strong>III/ Corrections to the Guides</strong></p>\r\n<p><br />-----------------------&lt; D033903 24/SEP/08 &gt;--------------------------<br /><strong>SDK Version 1.4.x for Upgrade Assistant</strong><br />The upgrade assistant only supports Java Software Development Kit (SDK) 1.4.x. It does not support version 1.5 or higher.<br /><br />------------------------&lt; D035220 APR/23/07 &gt;--------------------------<br /><strong>Migrate data from table TVARV to TVARVC</strong><br />As of SAP Basis 6.10, the client-specific table TVARVC is used instead of cross-client table TVARV. If you want to migrate entries from table TVARV to the new table, you can use report RSTVARVCLIENTDEPENDENT.<br />For more information, see Note <strong>557314</strong>.<br /><br />------------------------&lt; D024991 OCT/19/06 &gt;-------------------------<br /><strong>Data Management Planning - Link to SAP Service Marketplace</strong><br />Site service.sap.com/dao on SAP Service Marketplace is temporarily underconstruction. Please refer to the following site and document instead:<br />service.sap.com/data-archiving -&gt; Media Library -&gt; Literature &amp; Brochure-&gt; Data Management Guide<br /><br />------------------------&lt; D022030 JUN/21/06 &gt;-------------------------<br /><strong>Section: Making Entries for the Parameter Input Module</strong><br />The maximum length of the mount directory path is <strong>50</strong> characters.<br />It may not contain any blanks or special characters.<br /><br />------------------------&lt; D022030 16/MAY/06 &gt;-------------------------<br /><strong>AIX: Isolating the Central Instance</strong><br />The values stated in the AIX part of section \"Isolating the Central Instance\" are minimum values.<br /><br />------------------------&lt; D022030 31/JAN/06 &gt;-------------------------<br /><strong>Windows: Space Requirements for MS SQL Database</strong><br />In sections \"Upgrade - Step by Step\" and \"Checking the Hardware Requirements\", you are referred to Note 689574 for space requirements with MS SQL database.<br />This reference is wrong.<br />The PREPARE program issues the correct space requirements.<br /><br />------------------------&lt; D030328 09/AUG/05 &gt;-------------------------<br /><strong>Optional Follow-Up Activity: Where-used list in the Workbench</strong><br />As of release 6.10, the where-used list for Dictionary objects has changed. If you need a proper display of the list, you need to run report SAPRSEUB after the upgrade.<br />As the runtime of the report may be quite long, we recommend that you run it in the development system only.<br />For more information, see Notes <strong>401389</strong> and <strong>28022</strong>.<br /><br /><br />------------------------&lt; D022030 APR/06/05 &gt;-------------------------<br /><strong>Names of DVDs needed - Section \"Upgrade - Step by Step\"</strong><br />The names of the DVDs needed as they are described in section \"Upgrade -Step by Step\" do not fully correspond to the titles of the actual DVDs.<br />For the correct DVD titles, see the documentation \"mySAP ERP Master Guide\" for mySAP ERP 2004 on SAP Service Marketplace under Quick Link \"instguides\" -&gt; mySAP ERP -&gt; mySAP ERP 2004.<br /><br />------------------------&lt; D022030 MAR/18/05 &gt;-------------------------<br /><strong>Documentation \"SAP Software on UNIX - OS Dependencies\"</strong><br />The information formerly contained in the documentation \"SAP Software on UNIX - OS Dependencies\" has been moved to the documentation \"Component Installation Guide &lt;your SAP component system combination&gt;, Part I.<br /><br />------------------------&lt; I002675 MAR/09/05 &gt;-------------------------<br /><strong>Windows Guide Section on Database-Specific Parameters</strong><br />In the Guide for the Upgrade on Windows, section \"Checking the Database- specific requirements for PREPARE\", you are asked to check the profile parameters for MS SQL server. As some of the parameter names have changed, SAP system tools may not recognize the parameters.<br />For more information, see <strong>Note 826528</strong>.<br /><br />---------------------------------------------------------------------<br /><br /><br /><br /></p>\r\n<p><strong>IV/ Errors on the CD-ROM</strong></p>\r\n<p><br />---------------------------------------------------------------------<br /><br /><br /><br /></p>\r\n<p><strong>V/ Preventing Data Loss, Upgrade Shutdown, and Long Runtimes</strong></p>\r\n<p><br />-----------------------&lt; D000706 28/NOV/06 &gt;--------------------------<br /><strong>Modification Adjustment Planning and Unicode Conversion</strong><br />You cannot import transport requests created in a Unicode SAP system into a non-Unicode SAP system. If you want to perform a Unicode conversion of your SAP system, create the transport request <strong>before</strong> the conversion.<br /><br />-----------------------&lt; D028597 15/SEP/06 &gt;--------------------------<br /><strong>Support Package SAPKB64018 - use corrected version</strong><br />If you include Support Package SAPKB70009 in the upgrade, make sure to use the corrected version, indicated by EPS file name CSN0120061532_0024351.PAT.<br />If you use the old version, phase XPRAS_UPG returns an error on the after import method SRM_FILL_KC_TABLES_AFTER_IMP. In this case, proceed as described in Note <strong>967821</strong>.<br />For more information about the problem in general, see Note <strong>672651 </strong>.<br /><br />------------------------&lt; D003327 15/SEP/05 &gt;-------------------------<br /><strong>Back up customer-specific entries in table EDIFCT</strong><br />If you have maintained customer-specific entries in table EDIFCT, they may get lost during the upgrade. If you do not want to lose these entries, export them before the upgrade and reimport them after the upgrade.<br />For more information, see Note <strong>865142</strong>.<br /><br />------------------------&lt; D023890 16/AUG/05 &gt;-------------------------<br /><strong>Add-On strategy for ST-PI: Do not use Delete</strong></p>\r\n<ul>\r\n<li>Phase IS_SELECT:</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Do not use the option \"DELETE\" for ST-PI in phase IS_SELECT. <br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;If the option \"INST/UPG WITH STD CD\" does not work, download the latest version of ST-PI at http://service.sap.com/installations -&gt; Installations and Upgrades -&gt; Plug-Ins -&gt; SAP Solution Tools -&gt; ST-PI -&gt; ST-PI 2005_1_640 -&gt; Supplementary Upgrade</p>\r\n<ul>\r\n<li>Phase BIND_PATCH:</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Additionally you should include (at least) 2 Support Packages at http://service.sap.com/patches -&gt; Plug-Ins -&gt; SAP Solution Tools -&gt; ST-PI -&gt; ST-PI 2005_1_640<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;You can ignore the warning: ST-PI - selected package level is 2, equivalence level is 3<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Do not ignore other warnings!<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;If you are using a different release than ST-PI 2005_1_640 include all available Support Packages for this release.<br />For more information about the release strategy of ST-PI, see SAP notes <strong>539977</strong> and <strong>606041</strong>.<br />If you have decided to delete ST-PI, this will lead to an error in Phase TR_PATCH_STATUS_UPGRADE: ...failed with RFC_ERROR_SYSTEM_FAILURE.<br /><br />------------------------&lt; D030559 14/JUL/05 &gt;-------------------------<br /><strong>Do not include ABA Support Package 12 in the Upgrade</strong><br />If you included Support Package SAPKA64012 in the upgrade, the upgrade fails in phases SHADOW_IMPORT_UPG2 or TABIM_UPG.<br />To prevent the failure, only include the Suport Package in the upgrade if you can also include at least Support Package <strong>SAPKA64013</strong> as the problem will be fixed with SP 13.<br />For more information - also on the procedure in case of an upgrade failure, see <strong>note 849925</strong>.<br /><br />------------------------&lt; D031049 14/OCT/04 &gt;-------------------------<br /><strong>Source Rel. 4.0B: Project-Related Incoming Orders</strong><br />If you are using the preliminary solution for project-related incoming orders published with Note 118780, you have to modify data elements before the upgrade.<br />For more information, see <strong>note 369542</strong>.<br /><br />------------------------&lt; D024329 14/OCT/04 &gt;-------------------------<br /><strong>Component PS-ST-WBS: Preliminary Inclusion of Field KIMSK</strong><br />If you do not want the upgrade to overwrite field KIMSK, you can include the field in the table of modifiable fields before the upgrade.<br />For more information, see <strong>note 185315</strong>.<br /><br />----------------------------------------------------------------------<br /><br /><br /><br /></p>\r\n<p><strong>VI/ Preparing the Upgrade</strong></p>\r\n<p><br />------------------------&lt; D044675 17/JAN/08 &gt;--------------------------<br /><strong>Upgrade from Source Release 4.6C: Including a minimum SP level</strong><br />If your source release system includes SAP_HR 46C Support Package level D1, data loss may occur during the upgrade. To prevent any data loss, include at least Support Package 43 of the target release into the upgrade, although the upgrade program does not request it.<br />Alternatively, update your system to SAP_HR 46C Support Package level D2 before the upgrade.<br /><br />-----------------------&lt; D001330 27/APR/06 &gt;--------------------------<br /><strong>Handling of customer translation in the upgrade</strong><br />Z languages or customer translations on system texts with transaction SE63 are not considered as modifications to the system by the upgrade and are therefore lost during the upgrade. As the SAP system may change considerably from one release to the next, it may not be worth saving the translations.<br />If you think that it is worth saving your translations or languages, seeNote <strong>485741</strong> for more information.<br /><br />--------------------------&lt; D037027 DEC/12/05 &gt;-----------------------<br /><strong>SD Texts: Check Text Customizing on Source Release</strong><br />Before the upgrade, check your text Customizing in transaction VOTX as described in Note <strong>900607</strong>.<br /><br />--------------------------&lt; D001330 10/OCT/05 &gt;-----------------------<br /><strong>Apply the Latest Upgrade Repairs for Correct Language Import</strong><br />If you do not apply the latest repairs for the upgrade as described in Note 663240, the language import from the data dictionary will be incomplete.<br />For more information, see Note <strong>885955</strong>.<br /><br />-----------------------&lt; D028310 NOV/03/04 &gt;--------------------<br /><strong>Phase CONFCHK_IMP on Distributed Systems</strong><br />Phase CONFCHK_IMP offers you a list of operating systems to select from. This list only contains one entry \"Linux\" which is valid for both Linux and Linux IA64.<br /><br />------------------------&lt; D030022 14/OCT/04 &gt;-------------------------<br /><strong>Source Rel. 3.1I: Loss of Address Data</strong><br />If you are using component \"Plant Maintenance\" (PM-WOC-MN) and convert the addresses with report&#160;&#160;RSXADR05 before the upgrade from Source Release 3.1I, make sure to read <strong>note 360929</strong>.<br />Otherwise you may lose address data during the upgrade.<br /><br />----------------------&lt; D033898 06/OCT/04 &gt;---------------------------<br /><strong>Source Release 4.6C and below: SMODILOG Entries</strong><br />In Releases 4.6C and below, when you created customer-specific parameter effectivities, the system did not make SMODILOG entries. In order not to lose these customer-specific parameter effectivities during the upgrade, proceed as described in <strong>note 741280</strong>.<br /><br />----------------------&lt; D038245 09/SEP/04 &gt;---------------------------<br /><strong>Source Release Extension Set 1.10: Exchange containers</strong><br />If your system was installed with SAP R/3 Enterprise Ext. Set 1.10 (based on SAP Web AS 6.20) and you are using a database that uses different containers for saving data (Oracle, Informix and DB2 UDB for UNIX and Windows), refer to <strong>note 674070</strong> before the upgrade.<br />Otherwise, the exchange containers (tablespaces/dbspaces) cannot be emptied during the upgrade and cannot be deleted after the upgrade.<br /><br />------------------------&lt; D038245 19/APR/04 &gt;------------------------<br /><strong>Unicode Systems: Downward Compatible Kernel 6.40</strong><br />If you are using the normal kernel for Release 6.20 with your Unicode system, PREPARE issues the error message: Could not open the ICU common library.<br />Before you start PREPARE, install the Downward Compatible Kernel for Release 6.40. Until this kernel is available, proceed as described in <strong>Note 716378</strong>.<br /><br />------------------------&lt; D032986 27/JAN/04 &gt;------------------------<br /><strong>Upgrading with reintegrated add-ons (retrofit)</strong><br />For SAP ERP Core Component 5.0 (ECC 5.0), more add-ons were reintegrated into the ECC or the extension set.<br /><strong>Note 700778</strong> contains additional information on processing these add-ons before, during and after the upgrade.<br /><br />--------------------------&lt; D032986 27/JAN/04 &gt;-----------------------<br /><strong>Upgrading with PI/PI-A</strong><br />For information on how to upgrade your system with PI/PI-A plug-ins, see <strong>Note 700779</strong>.<br /><br />--------------------------&lt; D025323 24/APR/03 &gt;-----------------------<br /><strong>Upgrade on AIX: saposcol</strong><br />Refer to Note <strong>526694</strong> before the upgrade.<br /><br />--------------------------&lt; D019926 DEC/10/02 &gt;-----------------------<br /><strong>Upgrading with AIX 5.1</strong><br />If you want to upgrade with AIX 5.1, see <strong>Note 502532</strong> before starting PREPARE.<br /><br />-----------------------&lt; D025323 FEB/20/02 &gt;--------------------------<br /><strong>Source Releases on UNIX 32-bit or AIX 64-bit</strong><br />In some cases, you may have to upgrade the operating system to 64-bit before the actual upgrade.<br />When you upgrade from AIX 4.3 64-bit, you must perform some additional actions before upgrading.<br />For more information, see <strong>Notes 496963</strong> and <strong>499708</strong>.<br /><br />-----------------------------------------------------------------------<br /><br /><br /><br /></p>\r\n<p><strong>VII/ Problems During the PREPARE and Upgrade Phases</strong></p>\r\n<p><br />This section addresses known problems that cannot be avoided using preventive measures. These problems will only occur under very specific circumstances.</p>\r\n<p><strong>Problems During the PREPARE Phases</strong></p>\r\n<p><br />------------------------&lt; D038245 05/SEP/05 &gt;------------------------<br /><strong>Problem with DVD mount paths</strong><br />When you enter mount points in PREPARE, you may get the error message: SEVERE ERROR: unable to find directory<br />In this case, check whether you mount point is longer than 94 characters or contains blanks and special characters. In this case, shorten the name and remove blanks or special characters.<br /><br />-----------------------&lt; D038245 20/APR/05 &gt;---------------------------<br /><strong>Phase RFCCHK_INI: Name or Password is Incorrect</strong><br />In phase RFCCHK_INI you may get the error message \"Name or Password is Incorrect\".<br />If you replaced R3up after phase RFCCHK_INI with the latest version from SAP Service Marketplace, this error may also come up in other phases that connect to the system using RFC.<br />For more information, see Note <strong>792850</strong>. You may have to replace disp+work and restart the system.<br /><br />-----------------------&lt; D028310 JUL/26/04 &gt;----------------------<br /><strong>Source Release 3.1I and 4.0B: Error in Phase TR_CMDIMPORT_FDTASKS </strong><br />If there are nametab entries with a non-unique UUID, you get an error message in phase TR_CMDIMPORT_FDTASKS. Proceed as described in <strong>note 705485</strong><br /><br />-----------------------&lt; D038245 APR/11/02 &gt;--------------------<br /><strong>Termination in the TOOLIMPD3 phase</strong><br />The TOOLIMPD3 phase terminates in the PREPARE module import. The following message appears in the log file: ABAP runtime error CALL_FUNCTION_NO_RECEIVER<br />Receiving data for unknown CPIC link XXXXXX.<br />Repeat the phase and continue with the upgrade.<br /><br />-----------------------&lt; D022256 SEP/04/00 &gt;--------------------<br /><strong>For Windows NT 4.0 only</strong><br />During PREPARE, a dialog box with the following error message may appear: 'The procedure entry point ... could not be located in the dynamic link library ... .',<br />In this case, import the latest DLLs using the R3DLLINS.EXE program. The program is available on the CD SAP Kernel in directory \\NT\\&lt;Processor type&gt;\\NTPATCH.<br />Reboot your machine and restart PREPARE with 'PREPARE repeat'.<br /><br />-----------------------------------------------------------------------<br /><br /><br /></p>\r\n<p><strong>Problems During the Upgrade Phases</strong></p>\r\n<p><br />------------------------&lt; D028310 20/AUG/04 &gt;--------------------------<br />Phase: DIFFEXPDDIV<br />Note: 766379<br />Description:<br />Error message in log file DIFFEXPD.ELG (directory &lt;DIR_PUT&gt;/log):<br />INACTIVE DDIC VERSIONS-Export ERRORS and RETURN CODE in SAPEDDD622.QO1<br />&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;&#126;<br />2EETW190 \"TABT\" \"TESCL&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;\" has no active version.<br />...<br /><br />-----------------------------------------------------------------------<br />Phase: PARDIST_SHD<br />Note: 73999<br />Description: PCON_640 or PARDIST_SHD upgrade phases: TG450 to TG453<br /><br />-------------------------&lt; D025988 JUN/16/00 &gt;----------------------<br />Phase: PARDIST_SHD<br />Description: For Windows NT only<br />The upgrade is terminated during the PARDIST_SHD phase. The PCONUPG.ELG log file contains an incomplete error text which was extracted from the DS&lt;date&gt;.&lt;SID&gt; log file. Repeat the phase.<br /><br />--------------------------&lt; I808985 22/FEB/06 &gt;-----------------------<br />Phase: TABIM_UPG<br />Description: You may get a duplicate key error with the following text: 2EETW000 Update and insert sequence failed due to an error in DBI<br />In this case, delete the primary index of the respective table and recreate it. Repeat the upgrade phase.<br />For more information on duplicate keys, see Note 626915.<br /><br />--------------------------&lt; D019416 19/MAR/04 &gt;-----------------------<br />Phase: TABIM_POST<br />Note: 718912<br />Description: Upgrade stops with error DI829. In this case, implement the above note and continue with the upgrade. After the upgrade, you have to revert the modification.<br /><br />--------------------------&lt; C5010299 JAN/21/05 &gt;-----------------------<br />Phase: XPRAS_UPG<br />Note: 778198<br />Description: Error message S&gt;801 in log file LONGPOST.LOG. For more<br />information, see the note above.<br /><br />--------------------------&lt; D022030 JAN/26/06 &gt;----------------------<br />Phase: XPRAS_UPG<br />Note: 534826<br />Description: LONGPOST.LOG: The XPRA RMCSXP03 reports error M2392, M2108 or M2685. The note mentioned above contains more information on problems with update programs in addition to the solution to the error.<br /><br />--------------------------&lt; D021867 AUG/10/04 &gt;-----------------------<br />Phase: JOB_RDDNTPUR<br />Description: In the longpost-log file, you may get the error message: 3PETG447 Table and runtime object \"TA22EQU_WAO\" exist without DDIC reference.<br />You can ignore this message.<br /><br />-----------------------------------------------------------------------<br />Phase JOB_RSTLANUPG<br />Note: 626272<br />Description: Termination; the log file for job RSTLAN_UPGRADE contains the following error message:<br />\"..Job terminated after system exception ERROR_MESSAGE.\"<br /><br />--------------------------&lt; D025323 MAY/23/02 &gt;-----------------<br />Phase: CHK_POSTUP<br />Note: 197886<br />Description: If you have imported Notes 178631, 116095 or 69455 into your system before the upgrade, error messages for objects without DDIC reference appear in the log LONGPOST.LOG. Proceed as described in the Note.<br /><br />----------------------------------------------------------------------<br /><br /><br /><br /><br /></p>\r\n<p><strong>VIII/ Problems After the Upgrade</strong></p>\r\n<p><br />This section addresses known problems that cannot be avoided using preventive measures. These problems will only occur under specific circumstances.<br /><br />-----------------------&lt; D030731 15/SEP/05 &gt;------------------------<br /><strong>Run Report Z_873466_REPAIR_AFTER_UPGRADE</strong><br />Table COKEY2 has been extended with two fields that cannot be initialized by the upgrade. Therefore, you need to run report Z_873466_REPAIR_AFTER_UPGRADE directly after the upgrade.<br />For more information, see Note <strong>873466</strong>.<br /><br />-----------------------&lt; D023890 11/MAR/05 &gt;------------------------<br /><strong>Ignore or delete view VFAS</strong><br />If after the upgrade the \"VFAS\" view is displayed as incorrect in transaction DB02, you can ignore this or delete the view.<br /><br />-----------------------&lt; D039516 15/FEB/05 &gt;-------------------------<br /><strong>Non-IS-BEV Users: Deactivate Beverage Solution Pushbutton</strong><br />If you are <strong>not using IS Beverage solution</strong>, you have to deactivate the Beverage Solution pushbutton after the upgrade.<br />For more information, see <strong>Note 781111</strong>.<br /><br />-----------------------&lt; D000434 28/JAN/04 &gt;-------------------------<br /><strong>Source Release 6.20: Missing DB View STWB_INFO</strong><br />If your source release is based on SAP_BASIS 6.20 SP 48 or lower, transaction DB02 might show DB view STWB_INFO missing after the upgrade. <strong>This</strong> view is not needed in the system and requires no further actions.<br /><br />-----------------------&lt; D035318 08/JUL/04 &gt;-------------------------<br /><strong>Source Release lower than Basis 6.10: Codepage Conversion</strong><br />In Release 6.10, the codepage administration has changed considerably. If you want to continue using the customer-defined codepages that start with \"9\" after the upgrade, you have to convert the codepages using report RSCP0126 after the upgrade.<br />For more information, see <strong>Notes 485455</strong> and <strong>511732</strong>.<br /><br />------------------------&lt; D020815 AUG/23/02 &gt;-------------------<br /><strong>SPAU: Names of interface methods are truncated</strong><br />Some methods (ABAP objects) that were modified and overwritten by the upgrade can be displayed in transaction SPAU with their names shortened to 30 characters.<br />As a result, the system may also incorrectly sort methods in SPAU under \"Deleted objects\".<br />Caution: Deleted objects are not displayed in the standard selection in SPAU. It is easily possible to overlook these!<br />For more information about the correction, see <strong>Note 547773</strong>.<br /><br />----------------------------------------------------------------------<br /><strong>Linux: Importing the new saposcol version</strong><br />For more information, see <strong>Note 19227.</strong><br /><br />----------------------------------------------------------------------<br /><strong>ReliantUNIX: saposcol version 32-bit or 64-bit</strong><br />For more information, see <strong>Note 148926.</strong><br /><br />----------------------------------------------------------------------<br /><strong>Solaris: saposcol version 32-bit or 64- bit</strong><br />For more information, see <strong>Note 162980.</strong><br /><br />----------------------------------------------------------------------<br /><br /><br /><br /><br /></p>\r\n<p><strong>IX/ Chronological Summary</strong></p>\r\n<p><br />Date.....Topic..Short description<br />-----------------------------------------------------------------------<br />SEP/24/08..III..SDK Version 1.4.x for Upgrade Assistant<br />JAN/17/08...VI..Source Release SAP R/3 4.6C: Including a min. SP level<br />APR/23/07..III..Migrate data from table TVARV to TVARVC<br />MAR/02/07..VII..Phase JOB_RSTLANUPG: Termination<br />NOV/28/06....V..Modification Adjustment Planning and Unicode Conversion<br />OCT/19/06..III..Data Management Planning - Link to SMP<br />SEP/15/06....V..Support Package SAPKB64018 - use corrected version<br />JUN/21/06..III..Entries for Parameter Input Module: Path Length<br />MAY/16/06..III..AIX: Isolating the Central Instance<br />27/APR/06...VI..Handling of customer translation in the upgrade<br />JAN/31/06..III..Windows: Space Requirements for MS SQL Database<br />JAN/26/06..VII..Phase XPRAS_UPG: LONGPOST.LOG -&#160;&#160;XPRA RMCSXP03<br />DEC/12/05...VI..SD Texts: Check Text Customizing on Source Release<br />NOV/29/05...II..Upgrade on Linux x86_64: Correct R3up Version<br />OCT/10/05...VI..Latest Upgrade Repairs for Correct Language Import<br />15/SEP/05....V..Back up customer-specific entries in table EDIFCT<br />15/SEP/05.VIII..Run Report Z_873466_REPAIR_AFTER_UPGRADE<br />05/SEP/05..VII..Problem with DVD mount paths<br />AUG/16/05....V..Add-On strategy for ST-PI: Do not use Delete<br />09/AUG/05..III..Opt. Follow-Up: Where-Used List<br />JUN/02/05....V..Do not include ABA Support Package 12 in the Upgrade<br />APR/20/05..VII..Phase RFCCHK_INI: Name or Password is Incorrect<br />APR/06/05..III..Names of DVDs needed - Section \"Upgrade - Step by Step\"<br />MAR/18/05..III..Documentation: SAP Software on UNIX - OS Dependencies<br />MAR/11/05.VIII..Ignore or delete view VFAS<br />MAR/09/05..III..Windows Guide Section on Database-Specific Parameters<br />FEB/15/05.VIII..Non-IS-BEV Users: Deactivate Beverage Solution<br />FEB/02/05...II..LSWM now part of SAP_BASIS<br />JAN/28/05.VIII..Source Release 6.20: Missing DB View STWB_INFO<br />JAN/21/05..VII..Phase XPRAS_UPG: Error S&gt;801<br />14/OCT/04....V..Component PS-ST-WBS: Prel. Inclusion of Field KIMSK<br />14/OCT/04....V..Source Rel. 4.0B: Project-Related Incoming Orders<br />14/OCT/04...VI..Source Rel. 3.1I: Loss of Address Data<br />06/OCT/04...VI..Source Release 4.6C and below: SMODILOG Entries<br />SEP/09/04...VI..Source Release Extension Set 1.10: Exchange containers<br />AUG/20/04..VII..Phase: DIFFEXPDDIV<br />AUG/10/04..VII..Phase JOB_RDDNTPUR: TA22EQU_WAO without reference<br />JUL/26/04..VII..Start-Rel. 3.1I / 4.0B: Phase TR_CMDIMPORT_FDTASKS<br />JUL/08/04.VIII..Source Release lower than 6.10: Codepage conversion<br />APR/19/04...VI..Unicode Systems: Downward Compatible Kernel 6.40<br />19/MAR/04..VII..Phase TABIM_POST: Error DI829<br />JAN/27/04...VI..Upgrading with reintegrated add-ons (retrofit)<br />JAN/27/04...VI..Upgrading with PI/PI-A<br />DEZ/03/03...II..Windows only: Execute program R3dllins.exe<br />SEP/17/03....I..R3up keyword<br />APR/24/03...VI..Upgrade on AIX: saposcol<br />DEC/10/02...VI..Upgrading with AIX 5.1<br />AUG/23/02.VIII..SPAU: Names of interface methods are truncated<br />JUL/23/02 ...I..Windows: Windows XP is not supported<br />JUL/19/02...II..Problems with the shadow instance<br />MAY/24/02...II..Corrections and repairs for the upgrade<br />MAY/23/02..VII..Phase CHK_POSTUP - objects without DDIC reference<br />APR/11/02..VII..Termination in the TOOLIMPD3 phase<br />APR/08/02..III..Preparation for reading the upgrade CDs<br />FEB/20/02...VI..Source releases on UNIX 32-bit or AIX 64-bit<br />OCT/19/00.VIII..Linux:&#160;&#160;Importing the new saposcol version<br />SEP/04/00..VII..Windows NT: Error message during PREPARE<br />FEB/16/00.VIII..saposcol version 32-bit or 64-bit on Reliant UNIX<br />FEB/16/00.VIII..saposcol version 32-bit or 64-bit on Solaris<br />----------------------------------------------------------------------</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D031330)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000788218/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000788218/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000788218/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000788218/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000788218/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000788218/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000788218/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000788218/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000788218/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "967821", "RefComponent": "BC-SRV-RM", "RefTitle": "Incorrect definition of SRM_FILL_KC_TABLES_AFTER_IMP", "RefUrl": "/notes/967821"}, {"RefNumber": "900607", "RefComponent": "SD-BF-TP", "RefTitle": "Checklist: SD texts when upgrading to 4.70 and higher", "RefUrl": "/notes/900607"}, {"RefNumber": "893352", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/893352"}, {"RefNumber": "885955", "RefComponent": "BC-UPG", "RefTitle": "Language content in DDIC import not imported (Upg 640/700)", "RefUrl": "/notes/885955"}, {"RefNumber": "873466", "RefComponent": "CO-OM", "RefTitle": "COKEY2: Program termination due to data inconsistency", "RefUrl": "/notes/873466"}, {"RefNumber": "865142", "RefComponent": "BC-MID-ALE", "RefTitle": "Customer-specific entries in EDIFCT are deleted", "RefUrl": "/notes/865142"}, {"RefNumber": "852964", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/852964"}, {"RefNumber": "849925", "RefComponent": "BC-UPG", "RefTitle": "Upgrade: termination in phase TABIM_UPG / SHADOW_IMPORT_UPG2", "RefUrl": "/notes/849925"}, {"RefNumber": "828554", "RefComponent": "BW-SYS", "RefTitle": "Incorrect BW MDMP check when upgrading to Basis 6.40", "RefUrl": "/notes/828554"}, {"RefNumber": "826528", "RefComponent": "BC-DB-MSS", "RefTitle": "Some profile parameters not recognized.", "RefUrl": "/notes/826528"}, {"RefNumber": "792850", "RefComponent": "BC-SEC-LGN", "RefTitle": "Preparing ABAP systems to deal with incompatible passwords", "RefUrl": "/notes/792850"}, {"RefNumber": "783308", "RefComponent": "BC-UPG", "RefTitle": "TLIBG inconsistencies before the upgrade", "RefUrl": "/notes/783308"}, {"RefNumber": "781111", "RefComponent": "FI-AR", "RefTitle": "RUERP04 - Beverage Solution pushbutton in standard system", "RefUrl": "/notes/781111"}, {"RefNumber": "778198", "RefComponent": "BC-FES-ITS", "RefTitle": "SIAC_XPRA_CONVERT_FROM_4X_64, upgrade to Netweaver 04", "RefUrl": "/notes/778198"}, {"RefNumber": "766379", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/766379"}, {"RefNumber": "747622", "RefComponent": "BC-UPG", "RefTitle": "Active components in the NetWeaver ABAP stack", "RefUrl": "/notes/747622"}, {"RefNumber": "746576", "RefComponent": "XX-SER-REL", "RefTitle": "SAP ERP Central Component 5.0: Installation/upgrade with SP1", "RefUrl": "/notes/746576"}, {"RefNumber": "741280", "RefComponent": "LO-ECH", "RefTitle": "OS60: Updating parameter effectivities", "RefUrl": "/notes/741280"}, {"RefNumber": "737115", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info on upgrade to SCM41 SRM40 ECC50 MSSQL", "RefUrl": "/notes/737115"}, {"RefNumber": "718912", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Message DI829 during mass activation of lock objects", "RefUrl": "/notes/718912"}, {"RefNumber": "716378", "RefComponent": "BC-UPG-PRP", "RefTitle": "Missing libraries during Unicode upgrade", "RefUrl": "/notes/716378"}, {"RefNumber": "705485", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/705485"}, {"RefNumber": "700779", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Upgrading SAP ECC 500/600 with PI/PI-A/SLL_PI", "RefUrl": "/notes/700779"}, {"RefNumber": "700778", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Add-ons integrated into SAP ECC 500", "RefUrl": "/notes/700778"}, {"RefNumber": "700675", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information on the upgrade to ECC 5.0 INFORMIX", "RefUrl": "/notes/700675"}, {"RefNumber": "674070", "RefComponent": "BC-UPG-PRP", "RefTitle": "Tables in the substitution container after an upgrade", "RefUrl": "/notes/674070"}, {"RefNumber": "673066", "RefComponent": "BC-SRV-DX-LSM", "RefTitle": "LSMW: Upgrade to SAP Enterprise 4.7 (or Basis 6.20)", "RefUrl": "/notes/673066"}, {"RefNumber": "672651", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Known problems with Support Packages in Basis Rel.6.40", "RefUrl": "/notes/672651"}, {"RefNumber": "669656", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info about upgrade to SAP Web AS 6.40 MaxDB", "RefUrl": "/notes/669656"}, {"RefNumber": "663258", "RefComponent": "BC-UPG-RDM", "RefTitle": "Corrections for R3up version 640", "RefUrl": "/notes/663258"}, {"RefNumber": "663240", "RefComponent": "BC-UPG-RDM", "RefTitle": "Repairs for upgrade to Basis 640", "RefUrl": "/notes/663240"}, {"RefNumber": "662219", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40 ORACLE", "RefUrl": "/notes/662219"}, {"RefNumber": "662191", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB6: Additions to upgrade (based) on SAP Web AS 6.40", "RefUrl": "/notes/662191"}, {"RefNumber": "661569", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Information: Upgrade to SAP Web AS 6.40: iSeries", "RefUrl": "/notes/661569"}, {"RefNumber": "661252", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/661252"}, {"RefNumber": "626272", "RefComponent": "BC-UPG", "RefTitle": "Termination in the JOB_RSTLANUPG phase", "RefUrl": "/notes/626272"}, {"RefNumber": "606041", "RefComponent": "SV-SMG-SDD", "RefTitle": "Upgrade with ST-PI 003C (Solution Tools Plug-In)", "RefUrl": "/notes/606041"}, {"RefNumber": "557314", "RefComponent": "BC-ABA-TO", "RefTitle": "As of Release 610: TVARV replaced with TVARVC", "RefUrl": "/notes/557314"}, {"RefNumber": "547773", "RefComponent": "BC-DWB-CEX", "RefTitle": "SPAU: interface method names are truncated", "RefUrl": "/notes/547773"}, {"RefNumber": "539977", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for add-on ST-PI", "RefUrl": "/notes/539977"}, {"RefNumber": "534826", "RefComponent": "LO-LIS-DC", "RefTitle": "LIS: Current update programs after upgrade", "RefUrl": "/notes/534826"}, {"RefNumber": "526694", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "saposcol based on AIX perfstat library", "RefUrl": "/notes/526694"}, {"RefNumber": "525677", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/525677"}, {"RefNumber": "511732", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/511732"}, {"RefNumber": "502532", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/502532"}, {"RefNumber": "499708", "RefComponent": "BC-UPG", "RefTitle": "Additional information on upgrading to Basis 620 with AIX", "RefUrl": "/notes/499708"}, {"RefNumber": "496963", "RefComponent": "BC-UPG-RDM", "RefTitle": "32-bit source release in upgrades to 620", "RefUrl": "/notes/496963"}, {"RefNumber": "485741", "RefComponent": "BC-UPG", "RefTitle": "Processing of customer translations in the upgrade", "RefUrl": "/notes/485741"}, {"RefNumber": "485455", "RefComponent": "BC-I18", "RefTitle": "Change in code page structure for Release >= 6.10", "RefUrl": "/notes/485455"}, {"RefNumber": "430318", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/430318"}, {"RefNumber": "401389", "RefComponent": "BC-DWB-UTL-INR", "RefTitle": "Where-used list in the Workbench as of Release 6.10", "RefUrl": "/notes/401389"}, {"RefNumber": "369542", "RefComponent": "PS-REV-IO", "RefTitle": "CJA1: Upgrade of Release 4.0 preliminary solution to >= 4.5", "RefUrl": "/notes/369542"}, {"RefNumber": "360929", "RefComponent": "PM-WOC-MN", "RefTitle": "Loss of address data on execution of RSXADR05", "RefUrl": "/notes/360929"}, {"RefNumber": "29972", "RefComponent": "BC-INS", "RefTitle": "Instance numbers in a distributed system", "RefUrl": "/notes/29972"}, {"RefNumber": "28022", "RefComponent": "BC-DWB-UTL", "RefTitle": "Customer system: Where-used list for SAP Objects", "RefUrl": "/notes/28022"}, {"RefNumber": "197886", "RefComponent": "BC-UPG", "RefTitle": "Upgrade: messages in CHK_POSTUP: BDL*, STSL*, BAM*, SQLR*", "RefUrl": "/notes/197886"}, {"RefNumber": "19227", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "Retrieving the latest saposcol", "RefUrl": "/notes/19227"}, {"RefNumber": "185315", "RefComponent": "PS-ST-WBS", "RefTitle": "SFAC:Advance inclusion of KIMSK into field select.", "RefUrl": "/notes/185315"}, {"RefNumber": "162980", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "saposcol for 64-bit Solaris", "RefUrl": "/notes/162980"}, {"RefNumber": "148926", "RefComponent": "BC-OP-FTS", "RefTitle": "saposcol version 32 or 64 bit on ReliantUNIX", "RefUrl": "/notes/148926"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "28022", "RefComponent": "BC-DWB-UTL", "RefTitle": "Customer system: Where-used list for SAP Objects", "RefUrl": "/notes/28022 "}, {"RefNumber": "539977", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for add-on ST-PI", "RefUrl": "/notes/539977 "}, {"RefNumber": "783308", "RefComponent": "BC-UPG", "RefTitle": "TLIBG inconsistencies before the upgrade", "RefUrl": "/notes/783308 "}, {"RefNumber": "19227", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "Retrieving the latest saposcol", "RefUrl": "/notes/19227 "}, {"RefNumber": "485455", "RefComponent": "BC-I18", "RefTitle": "Change in code page structure for Release >= 6.10", "RefUrl": "/notes/485455 "}, {"RefNumber": "674070", "RefComponent": "BC-UPG-PRP", "RefTitle": "Tables in the substitution container after an upgrade", "RefUrl": "/notes/674070 "}, {"RefNumber": "865142", "RefComponent": "BC-MID-ALE", "RefTitle": "Customer-specific entries in EDIFCT are deleted", "RefUrl": "/notes/865142 "}, {"RefNumber": "669656", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info about upgrade to SAP Web AS 6.40 MaxDB", "RefUrl": "/notes/669656 "}, {"RefNumber": "661569", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Information: Upgrade to SAP Web AS 6.40: iSeries", "RefUrl": "/notes/661569 "}, {"RefNumber": "485741", "RefComponent": "BC-UPG", "RefTitle": "Processing of customer translations in the upgrade", "RefUrl": "/notes/485741 "}, {"RefNumber": "672651", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Known problems with Support Packages in Basis Rel.6.40", "RefUrl": "/notes/672651 "}, {"RefNumber": "873466", "RefComponent": "CO-OM", "RefTitle": "COKEY2: Program termination due to data inconsistency", "RefUrl": "/notes/873466 "}, {"RefNumber": "737115", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info on upgrade to SCM41 SRM40 ECC50 MSSQL", "RefUrl": "/notes/737115 "}, {"RefNumber": "662219", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40 ORACLE", "RefUrl": "/notes/662219 "}, {"RefNumber": "673066", "RefComponent": "BC-SRV-DX-LSM", "RefTitle": "LSMW: Upgrade to SAP Enterprise 4.7 (or Basis 6.20)", "RefUrl": "/notes/673066 "}, {"RefNumber": "401389", "RefComponent": "BC-DWB-UTL-INR", "RefTitle": "Where-used list in the Workbench as of Release 6.10", "RefUrl": "/notes/401389 "}, {"RefNumber": "496963", "RefComponent": "BC-UPG-RDM", "RefTitle": "32-bit source release in upgrades to 620", "RefUrl": "/notes/496963 "}, {"RefNumber": "547773", "RefComponent": "BC-DWB-CEX", "RefTitle": "SPAU: interface method names are truncated", "RefUrl": "/notes/547773 "}, {"RefNumber": "792850", "RefComponent": "BC-SEC-LGN", "RefTitle": "Preparing ABAP systems to deal with incompatible passwords", "RefUrl": "/notes/792850 "}, {"RefNumber": "826528", "RefComponent": "BC-DB-MSS", "RefTitle": "Some profile parameters not recognized.", "RefUrl": "/notes/826528 "}, {"RefNumber": "369542", "RefComponent": "PS-REV-IO", "RefTitle": "CJA1: Upgrade of Release 4.0 preliminary solution to >= 4.5", "RefUrl": "/notes/369542 "}, {"RefNumber": "900607", "RefComponent": "SD-BF-TP", "RefTitle": "Checklist: SD texts when upgrading to 4.70 and higher", "RefUrl": "/notes/900607 "}, {"RefNumber": "700675", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information on the upgrade to ECC 5.0 INFORMIX", "RefUrl": "/notes/700675 "}, {"RefNumber": "700779", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Upgrading SAP ECC 500/600 with PI/PI-A/SLL_PI", "RefUrl": "/notes/700779 "}, {"RefNumber": "197886", "RefComponent": "BC-UPG", "RefTitle": "Upgrade: messages in CHK_POSTUP: BDL*, STSL*, BAM*, SQLR*", "RefUrl": "/notes/197886 "}, {"RefNumber": "663258", "RefComponent": "BC-UPG-RDM", "RefTitle": "Corrections for R3up version 640", "RefUrl": "/notes/663258 "}, {"RefNumber": "557314", "RefComponent": "BC-ABA-TO", "RefTitle": "As of Release 610: TVARV replaced with TVARVC", "RefUrl": "/notes/557314 "}, {"RefNumber": "967821", "RefComponent": "BC-SRV-RM", "RefTitle": "Incorrect definition of SRM_FILL_KC_TABLES_AFTER_IMP", "RefUrl": "/notes/967821 "}, {"RefNumber": "700778", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Add-ons integrated into SAP ECC 500", "RefUrl": "/notes/700778 "}, {"RefNumber": "716378", "RefComponent": "BC-UPG-PRP", "RefTitle": "Missing libraries during Unicode upgrade", "RefUrl": "/notes/716378 "}, {"RefNumber": "662191", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB6: Additions to upgrade (based) on SAP Web AS 6.40", "RefUrl": "/notes/662191 "}, {"RefNumber": "885955", "RefComponent": "BC-UPG", "RefTitle": "Language content in DDIC import not imported (Upg 640/700)", "RefUrl": "/notes/885955 "}, {"RefNumber": "534826", "RefComponent": "LO-LIS-DC", "RefTitle": "LIS: Current update programs after upgrade", "RefUrl": "/notes/534826 "}, {"RefNumber": "741280", "RefComponent": "LO-ECH", "RefTitle": "OS60: Updating parameter effectivities", "RefUrl": "/notes/741280 "}, {"RefNumber": "663240", "RefComponent": "BC-UPG-RDM", "RefTitle": "Repairs for upgrade to Basis 640", "RefUrl": "/notes/663240 "}, {"RefNumber": "828554", "RefComponent": "BW-SYS", "RefTitle": "Incorrect BW MDMP check when upgrading to Basis 6.40", "RefUrl": "/notes/828554 "}, {"RefNumber": "778198", "RefComponent": "BC-FES-ITS", "RefTitle": "SIAC_XPRA_CONVERT_FROM_4X_64, upgrade to Netweaver 04", "RefUrl": "/notes/778198 "}, {"RefNumber": "606041", "RefComponent": "SV-SMG-SDD", "RefTitle": "Upgrade with ST-PI 003C (Solution Tools Plug-In)", "RefUrl": "/notes/606041 "}, {"RefNumber": "849925", "RefComponent": "BC-UPG", "RefTitle": "Upgrade: termination in phase TABIM_UPG / SHADOW_IMPORT_UPG2", "RefUrl": "/notes/849925 "}, {"RefNumber": "747622", "RefComponent": "BC-UPG", "RefTitle": "Active components in the NetWeaver ABAP stack", "RefUrl": "/notes/747622 "}, {"RefNumber": "781111", "RefComponent": "FI-AR", "RefTitle": "RUERP04 - Beverage Solution pushbutton in standard system", "RefUrl": "/notes/781111 "}, {"RefNumber": "360929", "RefComponent": "PM-WOC-MN", "RefTitle": "Loss of address data on execution of RSXADR05", "RefUrl": "/notes/360929 "}, {"RefNumber": "185315", "RefComponent": "PS-ST-WBS", "RefTitle": "SFAC:Advance inclusion of KIMSK into field select.", "RefUrl": "/notes/185315 "}, {"RefNumber": "746576", "RefComponent": "XX-SER-REL", "RefTitle": "SAP ERP Central Component 5.0: Installation/upgrade with SP1", "RefUrl": "/notes/746576 "}, {"RefNumber": "29972", "RefComponent": "BC-INS", "RefTitle": "Instance numbers in a distributed system", "RefUrl": "/notes/29972 "}, {"RefNumber": "626272", "RefComponent": "BC-UPG", "RefTitle": "Termination in the JOB_RSTLANUPG phase", "RefUrl": "/notes/626272 "}, {"RefNumber": "718912", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Message DI829 during mass activation of lock objects", "RefUrl": "/notes/718912 "}, {"RefNumber": "499708", "RefComponent": "BC-UPG", "RefTitle": "Additional information on upgrading to Basis 620 with AIX", "RefUrl": "/notes/499708 "}, {"RefNumber": "526694", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "saposcol based on AIX perfstat library", "RefUrl": "/notes/526694 "}, {"RefNumber": "162980", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "saposcol for 64-bit Solaris", "RefUrl": "/notes/162980 "}, {"RefNumber": "148926", "RefComponent": "BC-OP-FTS", "RefTitle": "saposcol version 32 or 64 bit on ReliantUNIX", "RefUrl": "/notes/148926 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "640", "To": "640", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}