{"Request": {"Number": "425033", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 423, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001958502017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000425033?language=E&token=2AC5E8FD9B6D848D24486C6717362C46"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000425033", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000425033/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "425033"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "07.01.2002"}, "SAPComponentKey": {"_label": "Component", "value": "MM-PUR-VM-SET"}, "SAPComponentKeyText": {"_label": "Component", "value": "Subsequent Settlement"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Purchasing", "value": "MM-PUR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Vendor-Material Relationships and Conditions", "value": "MM-PUR-VM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Subsequent Settlement", "value": "MM-PUR-VM-SET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM-SET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "425033 - Problem extending arrangements if arrangement contains EURO"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><UL><LI>During the extension of rebate arrangements with currency conversion (see selection screen) you encounter difficulties if the rebate arrangement already contains the currency EUR in individual condition records, for example periods. For details, see below.</LI></UL> <UL><LI>The check to see whether rebate arrangements are extended with several currencies is implemented time-dependently. The start date of the exchange rate is provided as the key date. However, according to Note 91481, the exchange rates are to entered valid as of 01.01.1800. Consequently, due to the rate tables, it is not possible to determine from when a currency is to be converted for example to EUR. Since otherwise no table is available in the SAP System, the check is carried out without a date reference after the implementation of this note. The existence of an exchange rate with fixed exchange rates determines that the currency is to be converted.</LI></UL><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Subsequent settlement (purchasing), volume-based rebate, EURO, Transactions MEB7, MER7, reports RWMBON05, RWMBON35</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Consider the following information.</p> <UL><LI>For extensions it is important to ensure that no rebate arrangements are created in the old currency for the year 2002. You can find more details in the online documentation and in Note 400432.</LI></UL> <UL><LI>A currency is considered as belonging to the European Monetary Union when an exchange rate is defined with fixed rates.</LI></UL> <UL><LI>As of Release 4.0B, extensions provide the option of converting currencies. In the most simple case, all rebate arrangements were created in 2001 for example in DEM. The rebate arrangements can be extended for 2002 in EUR. Any values in DEM in the condition records are converted and can then be adjusted for example in the annual negotiations.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Problems occur when you work with a second currency. Therefore you can exclude rebate arrangements with a second currency from the automatic extension and carry out the extension manually, for example to keep the second currency.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Nevertheless you can, however, force an automatic extension. <br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you set the selection parameter \"Carry out conversion also if cond. scale/cond. currency euro currency differs from arr. Currency\", the rebate arrangement is extended when the second currency, for example FRF, also belongs to the European Monetary Union. Here. the amount in FRF is converted to the new rebate arrangement currency. The extension is not carried out when the second currency is for example USD.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you set the selection parameter \"Carry out conversion also if cond. scale/cond. currency differs from arr. Currency\", the rebate arrangement is basically extended irrespective of the second currency which is involved, for example USD. Here. The amount in USD is converted to the new arrangement currency.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Problems occur now when the second currency is already the new rebate arrangement currency (EUR). The program regards the currency EUR as not belonging to the European Monetary Union. If the parameter \"Carry out conversion also if cond. scale/cond. currency euro currency differs from arr. Currency\", the extension is rejected with message<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MN 638 \"Error in currency conversion for rebate arrangement nnn (no extension)\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Only setting the parameter \"Carry out conversion also if cond. scale/cond. currency differs from arr. Currency\" makes you achieve what you want. This behavior is not useful.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>See the attachment for the correction instruction or import the applicable Support Package.<br /><br />Enhance the import interface of function module MM_ARRANGEMENT_CHECK_WAERS.<br />Release 4.0/4.5:<br />Import parameter&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Ref. field Ref. type Default Optional Reference<br />I_NEW_ARRANGEMENT_WAERS KONA-WAERS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; X<br /><br />Release 4.6:<br />Import parameter&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Type spec. Ref. type Default Optional Pass value<br />I_NEW_ARRANGEMENT_WAERS LIKE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;KONA-WAERS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;X&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;X<br /><br />After implementing the correction, it is sufficient to set parameter \"Carry out conversion also if cond. scale/cond. currency euro currency differs from arr. Currency\".<br />Read Note 400432. After implementation of the note, rebate arrangements with invalid currencies cannot be saved anymore. This is also true for the extension function.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023678)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D023678)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000425033/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000425033/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000425033/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000425033/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000425033/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000425033/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000425033/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000425033/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000425033/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "400432", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Expiring currencies, rebate arrangmnt maint. (purch., sales)", "RefUrl": "/notes/400432"}, {"RefNumber": "398739", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect conversion of EMU currencies - Part 2", "RefUrl": "/notes/398739"}, {"RefNumber": "183379", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Composite SAP note subsequent settlement (Purchasing) 4.6", "RefUrl": "/notes/183379"}, {"RefNumber": "152725", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 4.5", "RefUrl": "/notes/152725"}, {"RefNumber": "104668", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 4.0", "RefUrl": "/notes/104668"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "183379", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Composite SAP note subsequent settlement (Purchasing) 4.6", "RefUrl": "/notes/183379 "}, {"RefNumber": "104668", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 4.0", "RefUrl": "/notes/104668 "}, {"RefNumber": "152725", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 4.5", "RefUrl": "/notes/152725 "}, {"RefNumber": "400432", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Expiring currencies, rebate arrangmnt maint. (purch., sales)", "RefUrl": "/notes/400432 "}, {"RefNumber": "398739", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect conversion of EMU currencies - Part 2", "RefUrl": "/notes/398739 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B67", "URL": "/supportpackage/SAPKH40B67"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B46", "URL": "/supportpackage/SAPKH45B46"}, {"SoftwareComponentVersion": "SAP_APPL 46B", "SupportPackage": "SAPKH46B33", "URL": "/supportpackage/SAPKH46B33"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C25", "URL": "/supportpackage/SAPKH46C25"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 4, "URL": "/corrins/0000425033/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "40B", "ValidTo": "40B", "Number": "400432 ", "URL": "/notes/400432 ", "Title": "Expiring currencies, rebate arrangmnt maint. (purch., sales)", "Component": "MM-PUR-VM-SET"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "45B", "ValidTo": "45B", "Number": "400432 ", "URL": "/notes/400432 ", "Title": "Expiring currencies, rebate arrangmnt maint. (purch., sales)", "Component": "MM-PUR-VM-SET"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46B", "ValidTo": "46B", "Number": "400432 ", "URL": "/notes/400432 ", "Title": "Expiring currencies, rebate arrangmnt maint. (purch., sales)", "Component": "MM-PUR-VM-SET"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "46C", "Number": "400432 ", "URL": "/notes/400432 ", "Title": "Expiring currencies, rebate arrangmnt maint. (purch., sales)", "Component": "MM-PUR-VM-SET"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}