{"Request": {"Number": "1070629", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 313, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016332932017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=6A9E197A7106F10671FEF06555DE8CA8"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1070629"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 108}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.04.2023"}, "SAPComponentKey": {"_label": "Component", "value": "FI-GL-MIG-BO"}, "SAPComponentKeyText": {"_label": "Component", "value": "general ledger migration back office"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Accounting", "value": "FI-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "general ledger migration", "value": "FI-GL-MIG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-MIG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "general ledger migration back office", "value": "FI-GL-MIG-BO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-MIG-BO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1070629 - FAQs: Migration to General Ledger Accounting (new)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><strong>Last update: December 15, 2016:</strong></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>New General Ledger Accounting, new G/L, new G/L Migration, NewGL, NewGL Migration, New G/L, New G/L Migration, FAQ, Frequently Asked Questions, GLM, NMI, NMI_CON, SAP General Ledger Migration service, migration service, migration scenario </p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This SAP Note provides answers to Frequently Asked Questions (<strong>FAQs</strong>) about the <strong>migration from classic General Ledger Accounting to General Ledger Accounting (new) and the subsequent implementation of new functions</strong>. Note that you can find important and up-to-date information about the migration and the delivered standard SAP migration scenarios at the following link: <strong>support.sap.com/glmig</strong><br /><br />Questions that <span style=\"text-decoration: underline;\">do not directly</span> relate to a migration to General Ledger Accounting (new) but might be of interest in a migration project, such as questions regarding the function and compatibility of General Ledger Accounting (new), are dealt with in an <strong>appendix</strong> (with its own sequential numbering) within this SAP Note.<br /><br /><br /><strong>Correct English name </strong>(and spelling)<strong>:</strong> The correct name for this new option in FI is \"General Ledger Accounting (new)\". The term \"new General Ledger\" is a possible abbreviation, but is not completely correct. In English, the term \"new G/L\" is used.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ol><ol>1.</ol></ol>\r\n<p><strong>Is there a certificate available for new General Ledger Accounting and for the migration to new General Ledger Accounting?</strong></p>\r\n<p><br />Yes. See SAP Note 868278.<br /><br /></p>\r\n<ol><ol>2.</ol></ol>\r\n<p><strong>Is a guide available for migration to the new general ledger? </strong></p>\r\n<p><br />A migration guide is available. Please follow the link support.sap.com/glmig -&gt; \"General Information about SAP General Ledger Migration -&gt; Migration Guide\"<br /><br /></p>\r\n<ol><ol>3.</ol></ol>\r\n<p><strong>How can I calculate the data volume in the new general ledger (before the migration), and how can I evaluate the effects on system behavior (especially on performance)? </strong></p>\r\n<p><br />For information, see Notes 820495 and 1045430.<br /><br /></p>\r\n<ol><ol>4.</ol></ol>\r\n<p><strong>What restrictions are there during a new G/L migration regarding SAP CFM (Treasury) and/or SAP CML (Loans)?</strong></p>\r\n<p><br />If you are interested in migration scenario 4 or 5 (that is, replacing the accounts approach with the ledger approach in the new general ledger) within the context mentioned, you must note that no (standard) solution currently exists for a migration of this type. The reasons behind this statement are technical restrictions in the posting logic of the SAP CFM and SAP CML components.<br />The same problem occurs if you want to use the migration scenario 7 or 8; these are not compatible either if there are postings from Treasury (<strong>SAP CFM</strong>) and Loans (<strong>SAP CML</strong>).<br />To discuss further or alternative procedures, contact SAP Consulting (preferably as part of the blueprint of your migration project).<br />For more detailed and current information on SAP migration scenarios, see the FAQ in this SAP Note and in SAP Note 1558185, and SAP Service Marketplace under: www.support.sap.com/glmig.<br /><br /></p>\r\n<ol><ol>5.</ol></ol>\r\n<p><strong>Which restrictions must I consider if I intend to activate the document splitting function in the new general ledger?</strong></p>\r\n<p><br />For information, see Notes 966000 and 985298.<br />In this context, see also the FAQ \"Is there anything to bear in mind during the migration if document splitting is to be used?\" in this note.<br /><br /></p>\r\n<ol><ol>6.</ol></ol>\r\n<p><strong>Which Special Purpose Ledgers can be transferred to the new general ledger?</strong></p>\r\n<p><br />Only Special Purpose Ledgers that are compliant with the new general ledger can and should be transferred to the new general ledger. <br />If you use additional currencies in a Special Purpose Ledger and want to replace this Special Purpose Ledger with the new general ledger, you have to check the currencies used in the Special Purpose Ledger.  The migration programs read the data in the original FI document. If the Special Purpose Ledger you want to replace uses a currency that is not contained in the original FI document, you cannot migrate this data to the new general ledger.   In this case, you must keep the relevant Special Purpose Ledger. <br /><br /></p>\r\n<ol><ol>7.</ol></ol>\r\n<p><strong>What must I consider in a system with more than one production client (multi-client system) regarding configuring, migrating and activating the new general ledger?</strong></p>\r\n<p><br />The table FAGL_ACTIVEC with the field FAGL_ACTIVE (indicator: 'New General Ledger Accounting Is Active') is client-specific. All other tables that are relevant for the new general ledger (tables with prefix FAGL_*) are also client-dependent. <br />If no data is exchanged between the productive clients, you can configure, migrate and activate the new general ledger independently in each productive client. <br /><br /></p>\r\n<ol><ol>8.</ol></ol>\r\n<p><strong>Is it possible to upgrade to mySAP ERP 2004 (=&gt; ECC5.0) or SAP ERP 6.00 (=&gt; ECC6.0) and migrate from the classic general ledger to the new general ledger in the same fiscal year?</strong></p>\r\n<p><br />As a general principle, we recommend that you do <span style=\"text-decoration: underline;\">not</span> perform the technical upgrade to SAP ERP and the migration to General Ledger Accounting (new) in the same fiscal year, regardless of the migration scenario.<br />If you are using migration scenario 3, 4, or 5, this is a requirement rather than a recommendation.<br /><span style=\"text-decoration: underline;\">For example</span>: If you intend to use document splitting in the new general ledger, activate the function for validating the document splitting in your productive system before the migration date. This means that you upgrade to ECC 6.0 in one fiscal year, activate document split validation (which does not exist before ECC 6.0) before the end of that fiscal year and migrate to the new general ledger in the next fiscal year.<br />Keep in mind that in this context, the function for validating document splitting is not available in ECC5.0.<br /><span style=\"text-decoration: underline;\">For example</span>: If you intend to replace the accounts approach with the ledger approach in the new general ledger, you must supply (certain) valuation postings with ledger group information from the beginning of the migration year onwards. You can only do this if you have already completed the upgrade to ERP in one of the preceding years.<br /><br /></p>\r\n<ol><ol>9.</ol></ol>\r\n<p><strong>What must I consider regarding migration to the new general ledger and a local currency changeover (for example, changeover to the euro)?</strong></p>\r\n<p><br />Regarding the availability of tools for a local currency changeover in the new general ledger, consider the following: <br />SAP systems ECC 5.0 and 6.0: Local currency changeover tools are available in the new general ledger. <br />Regarding the projects for local currency conversion and migration to the new general ledger, consider the following:<br />If you activate document splitting in General Ledger Accounting (new), you <span style=\"text-decoration: underline;\"><strong>cannot</strong></span> perform the local currency changeover and the migration to General Ledger Accounting (new) in the same fiscal year.<br />If local currency conversion and migration to the new general ledger must take place in the same fiscal year, there is only one possible scenario: <br />Step 1: Local currency changeover in the classic general ledger <br />Step 2: Migration to the new general ledger without document splitting<br />All other scenarios, especially active document splitting in new General Ledger Accounting, require that you perform the local currency changeover and migration to new General Ledger Accounting in different fiscal years.<br /><br />Also see SAP Note 1339877 (and 1158830), which states the following: \"... Note that if a local currency changeover is planned for the new fiscal year [=&gt; fiscal year after the migration], you <strong>must</strong> deactivate the GLT0 update [=&gt; field WRITE_GLT0] at the end of the preceding fiscal year [end of the fiscal year of the migration]. ...\"<br /><br /></p>\r\n<ol><ol>10.</ol></ol>\r\n<p><strong>Which system landscape is required for the test migrations?</strong></p>\r\n<p><br />We recommend that you use a current (and complete) copy of your production client for all test migrations. This recommendation is a requirement for the last and therefore most important test migration. In addition, the database and operating system of the test system must be comparable to the production environment.<br />In this context, note that test systems and test clients that have been generated using, for example, the Test Data Migration Server (TDMS) do not entirely reflect the production data and therefore do not ensure full data consistency for a new general ledger migration.<br /><br /></p>\r\n<ol><ol>11.</ol></ol>\r\n<p><strong>How should I check data consistency in the classic general ledger before the beginning of the first test migration? </strong></p>\r\n<p><br />To check data consistency in the classic general ledger before the beginning of the first test migration, proceed as follows: <br />1) Carry out the steps that are described in Note 1592904 in detail. If you require additional help during the analysis or correction of differences, open a message on the SAP Service Marketplace for the component XX-RC-FI.<br />2) Program SAPF190<br />Run program SAPF190 for the fiscal year prior to the migration and for the fiscal year in which the migration takes place. <br />If the program finds any differences, create a message on SAP Service Marketplace under the component FI-GL-GL-X.<br />3) Program RAABST02<br />If you use Asset Accounting, run program RAABST02.<br />If the program finds any differences, create a message on SAP Service Marketplace under the component FI-AA-AA-B.<br />4) Program RCOPCA44<br />If you use Profit Center Accounting, run the program RCOPCA44 for a ledger comparison. Important: You cannot use the transaction code SE38 to start the ledger comparison. Instead, you must use KE5T (this is the simplest method).<br />If the program finds any differences, see SAP Note 81374.<br /><br />Remove all inconsistencies before you start the first test migration.   See also Note 1467114.<br /><br /></p>\r\n<ol><ol>12.</ol></ol>\r\n<p><strong>Are there any recommendations for account control in G/L accounts before the start of the first text migration?</strong></p>\r\n<p><br />In G/L master data, the fields \"Only balances in local crcy\", \"Open item management\", \"Display line items\" and \"Reconciliation account for account type\" are relevant for the implementation of the new general ledger. <br />Analyze these fields and adjust them if necessary before starting the first test migration. <br />\"Only balances in local crcy\": If the indicator \"Only balances in local crcy\" is not active, totals records of the account are updated to all currencies.  Check if this is necessary.  Postings in different currencies inflate the number of totals records in the table FAGLFLEXT. <br />\"Open item management\" (OI management):  Check for which accounts it is useful to manage open items.  Which accounts do you actually clear? <br />If use parallel ledgers in the new general ledger, keep in mind that accounts with different valuations (for example, provision accounts) must not be managed on an open item basis. <br />If you use the foreign currency valuation program to post to accounts (program SAPF100 or transaction F.05), you should not manage these accounts on an open item basis.  For more information, see SAP Note 318399. You can configure foreign currency valuation in transaction OBA1. <br />You can use the report RFSEPA03 to switch off open item management in accounts that have been posted to.  For more information, see Note 175960.<br />\"Display line items\":  From a technical point of view, \"Display line items\" is no longer required for accounts that are not managed on an open item basis because the new general ledger manages line items for each account in the table FAGLFLEXA.  After the migration from the classic general ledger to the new general ledger, you cannot switch off line item display until the external auditor has given approval. <br />\"Reconciliation account for account type\":  If you intend to activate document splitting, make sure that the reconciliation accounts for customers and vendors are controlled in the same way in all company codes. If, for example, a G/L account is a reconciliation account for customers, this must be the case in all relevant company codes because you have to classify accounts for document splitting at chart of accounts level. <br /><br /></p>\r\n<ol><ol>13.</ol></ol>\r\n<p><strong>How can I display the IMG path and application menu for General Ledger Accounting (new) in a Customizing system or in a test system <span style=\"text-decoration: underline;\">for the migration</span>?</strong></p>\r\n<p><br />To display the menu for the new general ledger and the IMG path for implementing the new general ledger, proceed as follows. <br />1) Execute the following step in the IMG (Implementation Guide): <br />Transaction SPRO -&gt; Financial Accounting -&gt; Financial Accounting Global Settings -&gt; Activate New General Ledger Accounting. <br />Set the activation flag, and save the change in the subsequent Customizing order.<br />By setting (and saving) the activation flag, an entry is created in the FAGL_ACTIVEC table, which also contains other important information, for example regarding document splitting, GLT0 update, deriving the functional area in the entry view, and more.<br />After calling transaction SPRO, Customizing is possible in the new general ledger (in this system), and the new paths are displayed in the Implementation Guide and in the application menu.<br />2) Immediately after step 1), deactivate the new general ledger again in the same Customizing step, and save the change in the Customizing order from Step 1). However, the necessary entry remains in the table FAGL_ACTIVEC.<br />Important: If the new general ledger is not active in any other client of this system, the IMG path disappears again.<br />3) Therefore, run the report RFAGL_SWAP_IMG_NEW. The IMG path will then be displayed again even though the new G/L is not active. You can now configure the new G/L in the test system without the new G/L being active, allowing you to start your first test migration.<br />4) However, in order to create a migration plan in the live system later (for example, to activate the document splitting validation in good time), transport the Customizing order from Steps 1) and 2) to your live system.<br /><br /></p>\r\n<ol><ol>14.</ol></ol>\r\n<p><strong>I want to add additional fields to the total record table (FAGLFLEXT) or line item table (FAGLFLEXA). What must I consider?</strong></p>\r\n<p><br />For information, see Notes 961295 and 923687.<br /><br /></p>\r\n<ol><ol>15.</ol></ol>\r\n<p><strong>How should I set up my transport system in my new general ledger project?</strong></p>\r\n<p><br />The following recommendation assumes that the transport management (workbench organizer and transport system: development, test, production) is set up in the productive ERP landscape. <br />You must store pure new general ledger configurations, configuration of validation of document splitting and activation of the new general ledger in different transport orders. After completing the test, you can transport pure new general ledger settings to the productive environment.   Transport the document splitting validation at a later stage. <br />Finally (after completing the migration), transport the activation indicator to the productive system.<br /><br /></p>\r\n<ol><ol>16.</ol></ol>\r\n<p><strong>Which parts of Customizing must already exist in the production system on the migration date?</strong></p>\r\n<p><br />If you use document splitting in ECC 6.0, we recommend that you activate the validation of document splitting in the production system at the latest on the migration date. This means that the new general ledger including document splitting must be completely configured in the production system on the migration date and that all interfaces and ALE scenarios must be adjusted accordingly.<br />This does not apply to ECC 5.0 because the validation of document splitting is not available in ECC 5.0.<br />If you do not intend to use document splitting or if you use Release ECC 5.0, you can transport Customizing of the new general ledger to the productive system after the migration date. <br />The indicator \"New General Ledger Accounting Is Active\" is transported with other basic settings of the new general ledger in table FAGL_ACTIVEC. Ensure that the indictor \"New General Ledger Accounting Is Active\" is not set when you transport it to the production system. <br /><br /></p>\r\n<ol><ol>17.</ol></ol>\r\n<p><strong>When drawing up the project plan, what must I consider if I want to use document splitting?</strong></p>\r\n<p><br />We recommend that you activate document splitting validation at the latest on the migration date.<br />The new general ledger including document splitting must be completely and finally configured before you activate document splitting validation.<br /><br /></p>\r\n<ol><ol>18.</ol></ol>\r\n<p><strong>Which are the criteria for creating a migration plan?</strong></p>\r\n<p><br />There are two basic types of migration plans:<br />- With document splitting<br />- Without document splitting.<br />If you plan to activate document splitting, this has a major impact on the migration. You have to assign exactly one type to each migration plan. Therefore, if you want to activate document splitting for some company codes and not for others, you must create two migration plans.<br />Activation of validation of document splitting is also done in the migration plan in migration phase 1.<br />To create several migration plans, you also require different fiscal year variants. In this respect, only the first day of the fiscal year is relevant. The period sorted list in the fiscal year is not relevant.<br />If, for example, the fiscal year begins on January 1 in one company code and on March 1 in another company code, you must create two migration plans for the two company codes.<br />If company codes post across company codes and you want to activate document splitting in the new general ledger, note the following: You must assign all company codes that post across company codes amongst each other to the same migration plan. <br /><br /></p>\r\n<ol><ol>19.</ol></ol>\r\n<p><strong>Can any date in the fiscal year be chosen as the migration date?</strong></p>\r\n<p><br />No. The migration date must be the first day of the fiscal year. No other date is possible.<br />If you want to perform the productive migration in fiscal year 20XY, you can perform test migrations in the previous fiscal year by setting the migration date to the first day of fiscal year 20XY &#x2013; 1.  <br /><br /></p>\r\n<ol><ol>20.</ol></ol>\r\n<p><strong>Which aspects must I consider regarding the activation date of the new general ledger? </strong></p>\r\n<p><br />A prerequisite for the migration is that year-end closing has been performed for the previous fiscal year. The activation date does not have to be a particular date (for example, the first day of the month/period or the last day of the month/period). The migration requires downtime. The system can go live on any day in the month. We recommend that you choose a time slot for going live when the system standstill has minimal impact on your company. Consequently, weekends after or preceding public holidays are suitable dates, as are company holidays.<br />During the downtime, the migration programs are run. After successfully running the migration programs (but still during the downtime), figures before migration must be reconciled with figures after migration. After successfully completing the reconciliation, you can activate the new general ledger.<br />Since the reconciliation of figures before and after migration is usually done by the persons who are also in charge of the month-end closing, we recommended that you do not schedule activation of the new general ledger at the same time as the month-end closing.<br /><br /></p>\r\n<ol><ol>21.</ol></ol>\r\n<p><strong>Is it possible to activate the new general ledger at company code level? </strong></p>\r\n<p><br />No. You activate the new general ledger at client level. This means that the new general ledger is active for all company codes in the client at the same time. <br /><br /></p>\r\n<ol><ol>22.</ol></ol>\r\n<p><strong>Do I have to consider anything special for the tables ACCTHD, ACCTIT and ACCTCR in connection with the migration to the new general ledger?</strong></p>\r\n<p><br />For more information, see Note 48009, section 8.<br /><br /></p>\r\n<ol><ol>23.</ol></ol>\r\n<p><strong>You want to configure a new company code with General Ledger Accounting (new). The existing company codes use the classic general ledger. Can company codes use different general ledgers (classic and new) in the same client?</strong></p>\r\n<p><br />The implementation of a new company code and the transition from the classic general ledger to the new general ledger are two different projects which you must schedule at different times.<br />You perform the migration from the classic general ledger to the new general ledger at client level and it affects all company codes in this client.<br />Because the new general ledger is activated at client level, the new general ledger would be active for all company codes, not only for the new company code. This would mean that the new general ledger is active in the old company codes, but there was no migration from the classic general ledger to the new general ledger. This is not possible.<br />Therefore, it is not possible to start with a new company code in the new general ledger while the classic general ledger is still active in other company codes in the same client.<br />First implement the new company code. Then set up the project for the transition from the classic general ledger to the new general ledger.<br />Alternatively: First set up the project for the transition from the classic general ledger to the new general ledger and migrate the data to the new general ledger, then implement the new company code. <br /><br /></p>\r\n<ol><ol>24.</ol></ol>\r\n<p><strong>The system contains company codes that have no transaction data at all or company codes that have transaction data only in closed fiscal years and are no longer in use. These company codes are called inactive company codes. What must I consider regarding inactive company codes when migrating to General Ledger Accounting (new)?</strong></p>\r\n<p><br />At the beginning of the current fiscal year, inactive company codes do not have open items, balance carryforwards or FI documents.<br />Therefore, exclude inactive company codes from the migration to the new general ledger. <br /><br /></p>\r\n<ol><ol>25.</ol></ol>\r\n<p><strong>Must all company codes be migrated successfully before the new general ledger can be activated?</strong></p>\r\n<p><br />Basically, all documents in all company codes must be migrated completely and without errors before you can activate the new general ledger.<br />In exceptional cases, you can migrate some documents after activating the new general ledger, as long as the status of the migration plan has not been set to status \"Migration ended\". However, the project team must be aware of the consequences of missing documents for system operations. If, for example, you want to use document splitting, you cannot pay open payables from the current fiscal year until the document was migrated. <br /><br /></p>\r\n<ol><ol>26.</ol></ol>\r\n<p><strong>Is there a BAdI for transferring the balance carryforward from phase 0?</strong></p>\r\n<p><br />To derive the new account assignment fields (for example, Segment), you can use the BAdI FAGL_UPLOAD_CF. When deriving fields, you can use all fields in the structure GLU1 including any customer fields.<br /><br /></p>\r\n<ol><ol>27.</ol></ol>\r\n<p><strong>How can I use transaction FBCB post data to reconciliation accounts for assets and to accounts for input tax output tax?</strong></p>\r\n<p><br />Implement the corrections according to Note 937940.<br /><br /></p>\r\n<ol><ol>28.</ol></ol>\r\n<p><strong>What mass processing option is available for transaction FBCB?</strong></p>\r\n<p><br />Use batch input for mass processing of transaction FBCB. We recommend this, for example, if you want to split the balances of reconciliation accounts for assets to profit centers or segments. <br />For more detailed information about this and other options for the automation of the FBCB postings, see SAP Note 1679975.<br /><br /></p>\r\n<ol><ol>29.</ol></ol>\r\n<p><strong>Is the document splitting available for open items from previous fiscal years (phase 0)?</strong></p>\r\n<p><strong></strong><br /><br />No. When migrating open items from previous years (phase 0) to the new general ledger, the system does not split documents.<br />You can supplement the account assignment fields for open items from previous fiscal years in the form of a singular account assignment. This means that an open item can receive one value for each account assignment field. The BAdI FAGL_MIGR_SUBST provides this function. It is not possible to split the open item itself.  It is not possible to split the open item itself. <br /><br /></p>\r\n<ol><ol>30.</ol></ol>\r\n<p><strong>What must I do if I want the document splitting function to set partner assignments in company code clearing lines?</strong></p>\r\n<p><br />See Note 942542. <br /><br /></p>\r\n<ol><ol>31.</ol></ol>\r\n<p><strong>Is it correct that a clearing document has clearing items when the document split is active?</strong></p>\r\n<p><br />Yes. If document splitting is active, clearing documents will always have two line items in the entry view (table BSEG).  See Note 950773.<br />For information about clearing documents in phase 1, see Note 1054674.<br /><br /></p>\r\n<ol><ol>32.</ol></ol>\r\n<p><strong>Why does the batch input not issue an error message although the validation of document splitting is active and configured accordingly, and the condition for the validation is not met?</strong></p>\r\n<p><br />The view V_FAGL_SPL_PROC contains an entry that applies to all business transactions or for a specified business transaction in connection with the batch input indicator V_FAGL_SPL_PROC-BINPT. This configuration overrides the general setting for validation.<br /><br />Conversely, if an error message is issued from the validation during batch input processing, the indicator V_FAGL_SPL_PROC-BINPT should be set accordingly if batch input processing is not supposed to be terminated when a validation error occurs.</p>\r\n<ol><ol>33.</ol></ol>\r\n<p><strong>Why are account assignments missing from required entry fields for some documents from phase 1 even though validation of document splitting is active with an error message?</strong></p>\r\n<p><br />You reset cleared items in phase 1. Clearing documents do not receive the document splitting characteristics during posting because they receive the document splitting information from the cleared items during the migration. This relationship is lost due to the clearing reset. These documents must be specifically treated by enriching the data in the BAdI or using alternative Customizing for document splitting. Note 1172351 implemented a logic in such a way that documents for which a clearing was reset can be supplied with account assignments using the general logic of migration. This applies for all clearing documents that were updated after implementing Note 1172351 or after importing the relevant Support Package. For more information, see also Notes 1320378, 1320561, 1441501, and 1461614.<br />To avoid this situation, we recommend that you do not use transaction FBRA (Reset Cleared Items) during phase 1.<br />In addition, if the document to be reversed does not contain this account assignment, reversal documents will be posted without account assignments even though document splitting validation is active. <br /><br /></p>\r\n<ol><ol>34.</ol></ol>\r\n<p><strong>Does document summarization affect document splitting?</strong></p>\r\n<p><br />If you intend to use document splitting, you must consider document summarization as critical. You can never summarize fields that you want to use in document splitting (for example, profit center, segment) and the relevant partner fields.<br />You must check which other fields you require for subsequent document splitting from phase 1 during the migration. The splitting is based on the data in the table BSEG. Therefore, you cannot summarize the relevant fields.<br />After you activate the new general ledger (phase 2), the system uses the table ACCIT (and no longer table BSEG) as the basis for document splitting.<br />Even if document splitting is not active, documents from phase 1 are subsequently posted from the table BSEG. Fields that were summarized in the classic general ledger are also empty in the tables FAGLFLEXA/FAGLFLEXT after the migration.<br /><br /></p>\r\n<ol><ol>35.</ol></ol>\r\n<p><strong>If document splitting is active, which functions are available for processing bills of exchange?</strong></p>\r\n<p><br />If document splitting is active, the following relationship exists between document splitting and bill of exchange processing:<br />The document splitting provides the account assignments when creating the bill of exchange liability for discounting and collection of the bill of exchange. This function is available as of ERP 2004 Support Package 10.<br />ERP 2004 and 2005 do not provide any functions for document splitting of \"bounced bills of exchange\" (bill of exchange protest). The new invoice is posted without account assignments and not based on the input from the original invoice.<br />A workaround with default account assignment (by user exit or substitution) is possible for bounced bills of exchange. Alternatively, you can enter the original account assignment manually. <br /><br /></p>\r\n<ol><ol>36.</ol></ol>\r\n<p><strong>During document splitting, what must I consider in connection with cross-company postings?</strong></p>\r\n<p><br />If you post across company codes, the settings for document splitting must be consistent in all pairs of relevant company codes. This means that document splitting must be either active or inactive in both company codes forming a pair for cross-company postings.<br /><br /></p>\r\n<ol><ol>37.</ol></ol>\r\n<p><strong>Why does transaction FAGL_MIG_SIM_SPL (Simulation of Document Splitting) not behave in the same way as validation of document splitting and transaction FAGL_MIG_SPLIT (Subsequently Post Split Information)?</strong></p>\r\n<p><br />Transaction FAGL_MIG_SIM_SPL (Simulation of Document Splitting) takes into account only the document that is currently being processed, but no document flow. If the document being processed is part of a document flow, transaction FAGL_MIG_SIM_SPL assumes that the original document already has entries in the tables that store document splitting information.<br />The validation of document splitting and transaction FAGL_MIG_SPLIT (Subsequently Post Split Information) both take into account the whole document flow. <br /><br /></p>\r\n<ol><ol>38.</ol></ol>\r\n<p><strong>Transaction FAGL_CHECK_LINETYPE (\"Check Bus. Transaction for Documents\") shows, for example, errors in cross-company code transactions in the document of the non-leading company code. When document splitting information is built and the documents are migrated, the document splitting works correctly for both company codes.  Why does transaction FAGL_CHECK_LINETYPE (\"Check Bus. Transaction for Documents\") not behave like the actual migration?</strong></p>\r\n<p><br />Transaction FAGL_CHECK_LINETYPE is not designed to simulate a complete business process. This means that, for example, a business process with cross-company code transactions for which the business process involves more than one document will not be checked correctly. Nevertheless these documents will be migrated correctly at a later stage and, as a result, the document splitting information will be built correctly.<br />Transaction FAGL_CHECK_LINETYPE always performs only a simple check to determine if Customizing is correct. The check will run into an error if the document that needs to be checked represents part of a complex business transaction. <br /><br /></p>\r\n<ol><ol>39.</ol></ol>\r\n<p><strong>You activated document splitting. You post the bill of exchange payment in transaction F-36. In transaction F-36 you choose \"Incoming payment\" and enter the customer. In the following screens, you enter the amount to be paid and select the invoices to be paid. When posting, the system issues message GLT2 201 \"Balancing field \"&amp;1\" in line item &amp;2 not filled\". When posting, it is also possible that the document is posted without document splitting. What must I do to ensure that the document for the bill of exchange payment is split correctly in transaction F-36?</strong></p>\r\n<p><br />The reason for this error is that you use a document type for the bill of exchange payment that is not configured according to requirements.<br />For the document splitting, you must process a clearing. You cannot process clearing from transaction F-36, therefore the document type must determine this. Configure a specific document type for the bill of exchange payment and use Customizing transaction GSP_VZ3 to assign this document type to business transaction 1010 variant 0001. Use this document type in transaction F-36.<br />Then use transaction OBU1 to change the default document type for transaction F-36. <br /><br /></p>\r\n<ol><ol>40.</ol></ol>\r\n<p><strong>What is the relationship between document splitting and validation?</strong></p>\r\n<p><br />You must distinguish between validation before document splitting and validation after document splitting. \"Before document splitting\" means that the validation is processed before document splitting.  \"After document splitting\" means that the validation is processed after the document splitting.<br />The purpose of validation before document splitting is to check that the prerequisites for document splitting are fulfilled. For example, the document type has a central control function for document splitting in the new general ledger. Therefore it is vital for document splitting that you use the correct document type in each transaction. For example, you can use validation before document splitting for postings of bill of exchange payments (transaction F-36) to make sure that you use the document type which is configured for this purpose in transaction GSP_VZ3. To configure a validation before document splitting, use transaction OB28. <br />You can use a validation after document splitting to check the result of the document splitting. For this purpose, you can use the BAdI GLT0_AFTERSPLIT_VAL. For additional information, see Note 846004.<br /><br /></p>\r\n<ol><ol>41.</ol></ol>\r\n<p><strong>FAQ 41 has moved. See the appendix after FAQ 16.</strong></p>\r\n<ol><ol>42.</ol></ol>\r\n<p><strong>What must I consider when creating the zero balance clearing account for document splitting?</strong></p>\r\n<p><br />See Note 961937.<br /><br /></p>\r\n<ol><ol>43.</ol></ol>\r\n<p><strong>Until when can I post to the previous fiscal year?</strong></p>\r\n<p><br />In this context, the \"previous fiscal year\" refers to the fiscal year prior to the migration date.<br />You can post to the previous fiscal year as long as you have not started the productive migration.<br />Once you have started the productive migration or migrated any objects (for example, open items), it is no longer possible to post to the previous fiscal year.<br />Once the new general ledger has been activated, it is no longer possible to post to the fiscal year prior to the migration date either.<br />This means that the fiscal year closure for the previous fiscal year must be done in phase 1, that is, before starting the productive migration and before going live. All postings to the previous fiscal year must be stored before starting the productive migration. This includes postings according to the auditor&#x2019;s instructions.<br />In many cases, you cannot be sure that no postings to the previous fiscal year will be required until the fiscal year closure has been certified. <br /><br /></p>\r\n<ol><ol>44.</ol></ol>\r\n<p><strong>What must I consider regarding down payment requests, parked documents and held documents?</strong></p>\r\n<p><br />The migration program that migrates the documents from phase 1 processes only FI documents that update transaction figures to the classic general ledger. This program does not process down payment requests, parked documents and noted items.<br />You must check if you have to add values for the new fields you have introduced with the new general ledger (for example, functional area, profit center, segment) in parked documents and noted items. <br /><br /></p>\r\n<ol><ol>45.</ol></ol>\r\n<p><strong>How can I transfer planning data from CO-OM (Controlling &#x2013; Overhead Management) to the new general ledger?</strong></p>\r\n<p><br />Proceed as follows to transfer planning data from CO-OM (Controlling &#x2013; Overhead Management) to the new general ledger:<br />First, configure Customizing for planning in the new general ledger according to the steps in the implementation guide.<br />Then transfer existing planning data from CO-OM (planning data on cost centers and internal orders) to the new general ledger. Choose the following path in the implementation guide:<br />-&gt; General Ledger Accounting (new)<br /> -&gt; Planning<br /> -&gt; Transfer Planning Data from CO-OM<br /><br /></p>\r\n<ol><ol>46.</ol></ol>\r\n<p><strong>How can I transfer planning data from classic Profit Center Accounting (EC-PCA) or from the classic general ledger to the new general ledger?</strong></p>\r\n<p><br />To transfer planning data from classic Profit Center Accounting (EC-PCA) or from the classic general ledger to the new general ledger, proceed as follows:<br />First, configure Customizing for planning in the new general ledger according to the steps in the implementation guide.<br />No specific functions are available for transferring planning data from classic Profit Center Accounting (EC-PCA) or from the classic general ledger to the new general ledger.<br />As a workaround, use a rollup to execute the transfer.<br />Transaction GP52 that SAP used to recommend is not appropriate for the EC-PCA transfer because this transaction only transfers fields with the same name. Therefore, it does not transfer the information from the Profit centre field.<br />With a rollup, you can transfer the planning totals to the new general ledger. For a direct rollup, you must allow rollup for the new general ledger by making the required changes directly in two customizing tables. Alternatively, you can first use transaction GL21/GL25 to copy the data to a NewGL rollup (temporary) ledger and then use transaction FAGLGP52 to transfer them to the NewGL.<br /><br />To execute a rollup, you require sufficient technical knowledge (for example, displaying the field names of table GLPCT or FAGLFLEXT).<br />Since both alternatives are workarounds, you must perform extensive tests.<br />If you have not planned locally in classic Profit Center Accounting and planning data was created exclusively in classic EC-PCA as a result of planning integration with CO-OM, transfer CO-OM planning data to the new general ledger instead of transferring EC-PCA planning data.<br /><br /></p>\r\n<ol><ol>47.</ol></ol>\r\n<p><strong>What must I consider if the real-time integration from CO to FI (recommended by SAP) is active on the migration date and the standard CSA (cost of sales accounting) ledger 0F or ledger for cost of sales accounting 0F is active?</strong></p>\r\n<p><br />A special feature arises in <strong>migration phase 1</strong> if classic General Ledger Accounting, real-time integration from CO to FI, and the standard CSA ledger 0F are active or in use: Since you have activated real-time integration, you no longer perform any reconciliation ledger postings to prevent duplicate postings.<br />However, real-time integration generates only one (pure) FI follow-on document (also in migration phase 1) and no additional CSA ledger document. However, in migration phase 1, you can no longer create a CSA report based on the CSA ledger first. However, if you still want/have to do this, you must make sure that a CSA document is also created (=&gt; in ledger 0F) during real-time integration - <strong>solution:</strong> Use transaction GCL2 to assign activity COFI to ledger 0F (manually).<br /><br />In addition, note that in the CSA ledger in SL the system updates only documents that are posted without a ledger group if no reference ledger is explicitly assigned. Documents that are posted with a ledger group (even if the leading ledger is contained) are not posted to SL without further action. This would be the case only if one of the ledgers contained in the ledger group was assigned to the SL ledger as a reference ledger.</p>\r\n<ol><ol>48.</ol></ol>\r\n<p><strong>FAQ 48 was deleted in June 2009 - see FAQ 47 for information about the topic \"Real-time integration CO -&gt; FI and migration\".</strong></p>\r\n<ol><ol>49.</ol></ol>\r\n<p><strong>What are the effects of the changeover of the functional area derivation?</strong></p>\r\n<p><br />If General Ledger Accounting (new) is activated, the \"Determine Functional Area on Entry Screen\" indicator is usually set by the system. This means that the functional area is already determined during the entry of the document. For processes that have no entry screen for the individual document (periodic processing), but that still use functional areas internally, the functional area derivation may also have to be activated. For this reason, a check should be made for CO distributions and assessments to determine if a derivation of the functional area is required. If this is the case, the \"Derive Func.Ar\" indicator (for deriving the functional area from the recipient) must be set in the cycle header.<br /><br /></p>\r\n<p>For more information, see SAP Note 1154791.</p>\r\n<ol><ol>50.</ol></ol>\r\n<p><strong>How can I post cross-profit center, CO-internal allocations to FI?</strong></p>\r\n<p><br />In the variant for real time integration CO -&gt; FI, activate the indicator \"Cross-Profit-Center\". The same applies if you want to post CO-internal allocations to FI that cause a change in company code, business area, functional area, segment or grant. <br /><br /></p>\r\n<ol><ol>51.</ol></ol>\r\n<p><strong>The migration program used to transfer documents from phase 1 processes FI documents only. It does not process internal CO documents (for example, from assessments) that were created in phase 1. You require these documents in the new general ledger, for example, because you want to replace classic Profit Center Accounting with the new general ledger or because you evaluate cost elements in the new general ledger. How can I migrate internal CO documents from phase 1 to General Ledger Accounting (new)?</strong></p>\r\n<p><br />After activating the \"Real-time integration CO -&gt; FI\", you can use the program \"Transfer CO Documents into External Accounting\" (FAGL_COFI_TRANSFER_CODOCS). This program finds all CO documents with a posting date (BUDAT) that is later than the \"Key Date: Active from\" date in the variant for real time integration CO -&gt; FI and that have not been posted in real-time mode from CO into FI.<br />You may have to reopen closed periods for this activity.<br /><br /></p>\r\n<ol><ol>52.</ol></ol>\r\n<p><strong>The migration program used to transfer documents from phase 1 processes FI documents only. It does not process internal EC-PCA documents (for example, from distributions) that were created in phase 1. How can I migrate internal EC-PCA documents (for example, from distributions) from phase 1 to General Ledger Accounting (new)?</strong></p>\r\n<p><br />It is not possible to migrate internal EC-PCA documents from phase 1 to the new general ledger. If the internal EC-PCA documents result from allocations (for example, from distributions), you can create new cycles in the new general ledger and rerun the cycles in the new general ledger subsequently for the periods of phase 1.<br /><br /></p>\r\n<ol><ol>53.</ol></ol>\r\n<p><strong>Note 740519 describes the derivation of the functional area.</strong></p>\r\n<p><strong>When the new general ledger is active, can I still use the old functional area derivation for event 0005 (filled after the document entry view) instead of using event 0006?</strong><br /><br />You can continue to use the old derivation of functional area according to event 0005. There is a switch in Customizing that makes it possible to use event 0005 in the new general ledger.<br />Choose the following path in the implementation guide: Financial Accounting (New) -&gt; Financial Accounting Basic Settings (New) -&gt; Tools -&gt; Customer Enhancements -&gt; Enhance Determination of Functional Area<br />In this Customizing transaction, deactivate the indicator \"Determine FArea on Entry Screen\". For detailed information, see the field help (F1) for this field.<br /><br /></p>\r\n<ol><ol>54.</ol></ol>\r\n<p><strong>What must I consider in module CO (Controlling) regarding functional area and segment?</strong></p>\r\n<p><br />For information, see Notes 764485 and 981184.<br /><br /></p>\r\n<ol><ol>55.</ol></ol>\r\n<p><strong>How can I update the functional area and segment in CO totals records?</strong></p>\r\n<p><br />To activate the update of the functional area and segment in the CO totals tables, perform the following step in the implementation guide:<br />ECC 5.0: Controlling -&gt; General Controlling -&gt; Include Characteristics in CO Totals Records<br />ECC 6.0: Controlling -&gt; General Controlling -&gt; Include Characteristics in CO Totals Records<br />For more information, see Note 764485.<br /><br /></p>\r\n<ol><ol>56.</ol></ol>\r\n<p><strong>Which entries are required in the productive client in the table T8G10?</strong></p>\r\n<p><br />The table T8G10 belongs to delivery class C (Customizing). For this reason, during the upgrade to SAP ERP, new entries are inserted only in client 000 in table T8G10. After the upgrade, transport the following entries of the table T8G10 from client 000 to your productive client:<br />TCODE PROCESS VARIANT<br />FB1D 1010 0001<br />FB1K 1010 0001<br />FB1S 1010 0001<br />FBRA 1020 0001<br /><br /></p>\r\n<ol><ol>57.</ol></ol>\r\n<p><strong>In the first run of the program FAGL_MIG_RPITEMS_CRESPLIT (\"Build Document Splitting Information for Documents To Be Transferred\"), the system may not process all documents successfully. What can I do?</strong></p>\r\n<p><br />This issue is most likely to occur if you have cross-company code postings. One reason can be the way the program internally sorts and processes the documents.<br />To resolve the problem, simply start the program FAGL_MIG_RPITEMS_CRESPLIT (Build Document Splitting Information for Documents To Be Transferred) again. <br /><br /></p>\r\n<ol><ol>58.</ol></ol>\r\n<p><strong>This FAQ was deleted because the program addressed is not supported by SAP in the context of the migration to the new general ledger. </strong></p>\r\n<ol><ol>59.</ol></ol>\r\n<p><strong>How can I improve the performance of the program FAGL_MIG_SUBSEQ_POST (Update Documents to New General Ledger Accounting)?</strong></p>\r\n<p><br />To improve the performance of the program FAGL_MIG_SUBSEQ_POST (Update Documents to New General Ledger Accounting), perform the following steps:<br /><br />a) Update database statistics for the table FAGLFLEXA.<br />Before the migration, the table FAGLFLEXA is empty. The sequential processing of the program steps gradually fills the table FAGLFLEXA with data. When transferring documents from the current year to the new general ledger, the program FAGL_MIG_SUBSEQ_POST checks whether each document already exists in the table FAGLFLEXA. However, during the migration, the Cost-Based Optimizer (CBO) does not yet have the required information to select the right index for accessing the database tables.<br />To solve the problem, proceed as follows: As soon as you have migrated some data to the new general ledger tables (for example, after migrating open items and before migrating documents from the current year), you should run the CBO or update table statistics.<br />The programs should then use the correct index for the database access and this should considerably reduce the runtime.<br />In more detail, proceed as follows: Activate the trace in transaction ST05. Start the program FAGL_MIG_SUBSEQ_POST, for example, for one document. Deactivate the trace and display the trace.<br />Position the cursor on \"FAGLFLEXA\" and choose \"Explain\". You can see when statistics were created for the last time and which index is being used. You can also start the update of the statistics from here.<br /><br />b) \"Execute with Para.Proc.\" in the selection screen of program FAGL_MIG_SUBSEQ_POST<br />We recommend that you activate \"Execute with Para.Proc.\" in the selection screen of the program FAGL_MIG_SUBSEQ_POST, to reduce the runtime.<br />When you activate \"Execute with Para.Proc.\", you must set two R/3 parameters as follows:<br />rdisp/bufrefmode sendon,exeauto<br />Set rdisp/bufreftime to the lowest possible value.<br />For more information, see Notes 384167 and 36283 as well as the related. <br />If you set the R/3 parameters accordingly, you will avoid error message GI 754 during parallel processing of the program FAGL_MIG_SUBSEQ_POST.<br /><br /></p>\r\n<ol><ol>60.</ol></ol>\r\n<p><strong>The program FAGL_MIG_OPITEMS_CRESPLIT generates the document splitting information for open items. Is it possible to parallelize the program FAGL_MIG_OPITEMS_CRESPLIT?</strong></p>\r\n<p><br />It is not possible to run the program FAGL_MIG_OPITEMS_CRESPLIT more than once at a time. This applies even if the selection refers to different migration plans.<br />If you try to start the program FAGL_MIG_OPITEMS_CRESPLIT for a second time in parallel mode, the system issues the message MC 601 \"Object requested is currently locked by user ...\". .<br />This is due to the design of the program and the design of the migration.<br /><br /></p>\r\n<ol><ol>61.</ol></ol>\r\n<p><strong>The program FAGL_MIG_RPITEMS_CRESPLIT generates the document splitting information for documents of the current year. Is it possible to parallelize the program FAGL_MIG_RPITEMS_CRESPLIT?</strong></p>\r\n<p><br />It is not possible to run the program FAGL_MIG_RPITEMS_CRESPLIT more than once at a time. This applies even if the selection refers to different migration plans.<br />The program sets a LOCK on some of the database tables for the migration. This is to ensure the consistency of the migration.<br /><br /></p>\r\n<ol><ol>62.</ol></ol>\r\n<p><strong>What must I consider regarding the database before the productive migration?</strong></p>\r\n<p><br />There are two major aspects. First, perform a full backup before the productive migration is started. Second, deactivate database logging before starting the productive migration. <br /><br /></p>\r\n<ol><ol>63.</ol></ol>\r\n<p><strong>Is it possible to minimize the downtime of the productive migration?</strong></p>\r\n<p><br />In general, we recommend that you migrate all company codes or all data at once (=&gt; on a [long] weekend, when the system is shut down).  The sequence of the activities to be performed in the migration cockpit and the selection options of the migration programs of the migration cockpit also supports this very well technically.<br />Another unchangeable principle is that the year-end closing of the previous year must be executed and certified - it must be ensured no postings will have to be made to the previous year that in the future. In addition, new General Ledger Accounting must be finally configured. <br />As an alternative to the general SAP recommendation, you can use an \"incremental approach\" to possibly minimize the required downtime of the productive migration.  The cornerstone of the incremental approach is to migrate most of the data whilst the system is up and running.<br />If you are interested in this approach, or if this approach is the better alternative to the standard procedure for your company, contact SAP Consulting. SAP Consulting will explain the steps of an incremental migration method in detail and then support you with it. <br /><br /></p>\r\n<ol><ol>64.</ol></ol>\r\n<p><strong>How can I deactivate classic Profit Center Accounting (EC-PCA) after activating the new general ledger?</strong></p>\r\n<p><br />FAQ 63 was replaced by FAQ 106 in June 2009. FAQ 106 provides more detailed information.<br /><br /></p>\r\n<ol><ol>65.</ol></ol>\r\n<p><strong>The tables that are required for the migration (tables FAGL_MIG*) take up a lot of disk space (for example, 350 MB). Can I delete the tables FAGL_MIG* after the productive migration?</strong></p>\r\n<p><br />No. The content of the tables FAGL_MIG* must remain in the database. The data from the tables FAGL_MIG* is required for controls and checks and to verify the migration. <br /><br /></p>\r\n<ol><ol>66.</ol></ol>\r\n<p><strong>Is it possible to change Customizing of the new general ledger if the new general ledger has been used productively?</strong></p>\r\n<p><br />See Note 891144.<br /><br /></p>\r\n<ol><ol>67.</ol></ol>\r\n<p><strong>Can I continue to use the reports RFINDEX and SAPF190 (from the classic general ledger) to reconcile documents &#x2013; transaction figures &#x2013; indexes in the new general ledger?</strong></p>\r\n<p><br />In the new general ledger, use report TFC_COMPARE_VZ (transaction FAGLF03) to reconcile totals records (T tables, such as table FAGLFLEXT), line items (A tables, such as table FAGLFLEXA), secondary indexes (such as tables BSIS and BSAS), and FI documents (tables BKPF and BSEG/BSEG_ADD). Refer also to the report documentation and Notes 862523 and 946596.<br /><br /></p>\r\n<ol><ol>68.</ol></ol>\r\n<p><strong>Classic General Ledger Accounting contains the reports RAABST01 and RAABST02 to reconcile the general ledger with Asset Accounting (FI-AA). Which reports are available to reconcile General Ledger Accounting (new) with Asset Accounting (FI-AA)?</strong></p>\r\n<p><br />You can use report RAABST01 to reconcile the new general ledger and Asset Accounting. As a prerequisite you require ECC 5.0 Support Package 01 or Note 752329.<br />You can use report RAABST02 to reconcile the new general ledger and Asset Accounting. As a prerequisite, you require Support Package 14 or ECC 6.0 Support Package 7. A downgrade of this function is not possible. See Note 897388.<br />The reconciliation of the new general ledger and Asset Accounting at a level below company code and account (for example, profit center or segment) is currently not supported.<br />For general information about reconciling of the general ledger and Asset Accounting (FI-AA), see Note 543151. <br /><br /></p>\r\n<ol><ol>69.</ol></ol>\r\n<p><strong>CO-FI real-time integration and General Ledger Accounting (new) are active. How can I reconcile data in General Ledger Accounting (new) with data in Controlling (CO)?</strong></p>\r\n<p><br />Use transaction FAGLCORC for the reconciliation. For further information, see Note 908019.<br /><br /></p>\r\n<ol><ol>70.</ol></ol>\r\n<p><strong>In General Ledger Accounting (new), line items do not match the relevant totals records or the totals records are not updated correctly. Which tool is available to analyze these inconsistencies?</strong></p>\r\n<p><br />See Note 940668.<br /><br /></p>\r\n<ol><ol>71.</ol></ol>\r\n<p><strong>What must I consider for allocations for balance sheet accounts and reconciliation accounts in the new general ledger?</strong></p>\r\n<p><br />For information, see Notes 830556 and 900962.<br /><br /></p>\r\n<ol><ol>72.</ol></ol>\r\n<p><strong>Which report in General Ledger Accounting (new) has the same features as the report RCOPCA02 (\"Profit Center: Actual Line Items\") in classic Profit Center Accounting?</strong></p>\r\n<p><br />Transaction FAGLL03 in new General Ledger Accounting has the same features as the report RCOPCA02 (Profit Center: Actual Line Items) or transaction KE5Z in classic Profit Center Accounting.<br /><br /></p>\r\n<ol><ol>73.</ol></ol>\r\n<p><strong>Is it possible to select customer-specific fields in the line item display of the new general ledger (transaction FAGLL03)?</strong></p>\r\n<p><br />Note 945932 explains how to select customer-specific fields in the line item display of the new general ledger (transaction FAGLL03).<br /><br /></p>\r\n<ol><ol>74.</ol></ol>\r\n<p><strong>Is it possible to display, sort and summarize customer-specific fields in the line item display (transaction FAGLL03) in the new general ledger?</strong></p>\r\n<p><br />See Note 984305.<br /><br /></p>\r\n<ol><ol>75.</ol></ol>\r\n<p><strong>How can I display the offsetting account in the line item display?</strong></p>\r\n<p><br />SAP Note 112312 describes how you can display the offsetting account in the line item display in the classic general ledger.<br />SAP Note 1034354 describes how you can display the offsetting account in the line item display in the new general ledger.<br /><br /></p>\r\n<ol><ol>76.</ol></ol>\r\n<p><strong>Why is the \"Alternative Account Number\" field not displayed in reporting of the new general ledger?</strong></p>\r\n<p><br />To display the \"Alternative Account Number\" field in report of the new general ledger, proceed as follows: <br />1) Implement Note 895609 and 939649.<br />2) To display &#x201C;Alternative Account Number&#x201D; in the line layout variant, proceed as follows:<br />- Call transaction o7r3 and add BSEG-LOKKT as a special field.<br />- Then change the line layout variant. The system now displays the \"Alternative Account Number\" field.<br />3) In the line item display in the classic general ledger (transaction FBL3N), you could enhance the custom selections in transaction SE36 as described in Note 310886. However, in the new general ledger, the custom selection in transaction FAGLL03 has different subareas. Each of these subareas corresponds to a structure:<br />G/L account master record SKA1_FS<br />G/L account company code SKB1_FS<br />G/L account line item BSIS_FS<br />Since the \"Alternative Account Number\" is not included in the structure SKB1_FS in the standard delivery, please implement the enhancement as described in Note 945932.  To include more fields in the custom selections of transaction FAGLL03, you can enhance the structures using an APPEND.<br /><br /></p>\r\n<ol><ol>77.</ol></ol>\r\n<p><strong>Segment reorganization denotes changing the segment in a profit center if transaction data has already been stored to it. Is it possible to change the segment in a profit center if transaction data has already been stored to it?</strong></p>\r\n<p><br />As of Enhancement Package 5, SAP provides an option to reorganize profit centers. For more information, see SAP Note 1471153.<br /><br />As of Enhancement Package 6, the reorganization of segments is additionally provided. For more information, see SAP Note 1627018.<br /><br />A tool-based solution for the reorganization in lower releases is not available.<br /><br />The feasible approach for segment reorganization will be a customer specific solution.<br />In the short term, you can use the following approach, which considers only a technical solution. Deimplement notes 940440, 1037986 and 940629 from your system. After this, define the \"Segment\" field as a time-based field in transaction 0KE7. Then you can change the segment in the profit center master data even if transaction data exists.<br />Note that this solution does not guarantee data integrity. For example, you must also manually transfer the profit center balances from the old segment to the new segment.<br /><br /></p>\r\n<ol><ol>78.</ol></ol>\r\n<p><strong>General Ledger Accounting (new) is active. The \"Profit Center Update\" scenario is assigned to the ledger. Cost centers are stored in the asset account master data. You change the profit center in a cost center that is used in the asset account master data. How are the relevant balances for each profit center (for example, for acquisition and production costs) transfer posted in General Ledger Accounting (new)?</strong></p>\r\n<p><br />There is no automatic process that changes balances for each profit center in the new general ledger after the profit center has been changed in a cost center that is used in asset master records. As a workaround, you can perform a manual correction posting.<br />Proceed as follows:<br />1) Identify the values that you must repost. For this purpose, you can use the report RABEST01. Fill the field \"Cost center\" in the selection screen of the report RABEST01 with the cost center that has been assigned to a different profit center.<br />2) Set the status to \"1\" in the company codes that require adjustment postings. To do so, use the following path in the implementation guide:<br />Financial Accounting -&gt; Asset Accounting -&gt; Preparing for Production Startup -&gt; Production Startup -&gt; Activate Company Code.<br />3) Use transaction OASV to perform adjustment postings to debit or credit the profit center on reconciliation accounts in asset accounting.<br />4) Afterwards, reset the status of the company codes to \"0\". <br /><br /></p>\r\n<ol><ol>79.</ol></ol>\r\n<p><strong>What is the migration cockpit?</strong></p>\r\n<p><br />The migration cockpit is the migration tool recommended by SAP. The migration cockpit includes migration packages that are preconfigured to some extent. You can load these packages for the migration from the classic general ledger to the new general ledger.<br />Depending on the migration scenario, you can load the required migration package, which includes the migration steps in the form of a process tree.<br />SAP Note 1041066 provides instructions for installing the migration cockpit. <br /><br /></p>\r\n<ol><ol>80.</ol></ol>\r\n<p><strong>Where can I find information about the SAP Migration Service? (=&gt; SAP General Ledger Migration service) and the available migration scenarios?</strong></p>\r\n<p><br />See SAP Note 812919.<br /><br /></p>\r\n<ol><ol>81.</ol></ol>\r\n<p><strong>When the new general ledger is active, why do documents exist after a migration which have a data entry view but no general ledger view in the FI document display (transaction FB03)?</strong></p>\r\n<p><br />From the posting data for the fiscal years prior to the migration date, only open items and the balance carryforward are migrated to the table of new General Ledger Accounting. FI documents with a posting date prior to the migration date are not migrated to new General Ledger Accounting (with the identical document data).<br />As a result, no general ledger view can be called for these documents after the migration, and the document display (transaction FB03) displays only the information that existed prior to the migration, that is,  the data entry view (table BKPF, BSEG) only.<br />It is correct system behavior for FI documents with a posting date prior to the migration date to have a data entry view but no general ledger view in the FI document display (transaction FB03). <br /><br /></p>\r\n<ol><ol>82.</ol></ol>\r\n<p><strong>What must I consider for the currency settings for the new general ledger?</strong></p>\r\n<p><br />As before, the leading ledger usually manages the document currency and the first local currency. There is also the option of managing two additional, parallel currencies. Non-leading ledgers can manage only (a selection of) the currencies that are defined for the leading ledger.<br />When replacing classic Profit Center Accounting, take into account that the previous profit center accounting currency has already been managed as the local currency or parallel currency in FI since before the migration date. If this is not the case, see SAP Note 39919 for information about subsequently implementing a parallel currency in FI. You cannot perform the required data enrichment for phase 0 and phase 1 immediately before or during the migration.<br /><br /></p>\r\n<ol><ol>83.</ol></ol>\r\n<p><strong>Do you require a Unicode system for SAP General Ledger Migration?</strong></p>\r\n<p><br />No, a Unicode system is not required.<br /><br /></p>\r\n<ol><ol>84.</ol></ol>\r\n<p><strong>Is there a migration scenario to display parallel accounting using non-leading ledgers (in new General Ledger Accounting)?  </strong></p>\r\n<p><br />Using migration scenarios 4 and 5, migration can take place from the accounts approach to the non-leading ledger approach (in the new G/L) in the context of parallel accounting. <br />However, the new implementation of parallel accounting is independent of the migration and represents a separate project.  Depending on the starting situation, you can integrate it into the migration project. However, it is <strong>not </strong>part of the SAP General Ledger Migration service. All aspects for the implementation of parallel accounting, also relating to integration into the migration project, must be covered by additional consulting.<br />In addition to migration scenarios 4 and 5, migration scenario 8 is available as of July 2009. This scenario enables the changeover from the accounts approach to the ledger approach (in General Ledger Accounting (new)) if <strong>General Ledger Accounting (new) is already active</strong>.<br />See also FAQ 90 and FAQ 105 in this SAP Note.<br /><br /></p>\r\n<ol><ol>85.</ol></ol>\r\n<p><strong>In which cases is it recommended to implement or activate document splitting?</strong></p>\r\n<p><br />Generally, document splitting is useful if you want to create a financial statement (and profit and loss statement) for an additional entity (another characteristic).<br />Standard characteristics or standard fields of new General Ledger Accounting that can be used for document splitting are the profit center, segment and business area.<br />Other fields - for example, customer-specific fields (especially industries often require individual entities) - can also be processed using document splitting.</p>\r\n<ol><ol>86.</ol></ol>\r\n<p><strong>Is there anything to bear in mind during the migration if document splitting is to be used? </strong></p>\r\n<p><br />If you want to use the document splitting (immediately) after migration from the classic general ledger to the new general ledger, (only) migration scenarios 3 and 5 are available for this. Both of these migration scenarios (or \"migration packages\") are already based on active document splitting within the migration from classic to new General Ledger Accounting.<br /><strong>Caution: </strong>As of 2008, migration scenario 6 (=&gt; subsequent implementation of document splitting) also provides the option of activating document splitting subsequently (if General Ledger Accounting (new) is already active without document splitting).<br />More information about all delivered migration scenarios or migration packages is available on the SAP Service Marketplace under  <strong>www.support.sap.com/glmig</strong>, for example, in the PDF file \"Overview Presentation: SAP General Ledger Migration\".<br /><br /></p>\r\n<ol><ol>87.</ol></ol>\r\n<p><strong>This FAQ was deleted in June 2009.</strong></p>\r\n<ol><ol>88.</ol></ol>\r\n<p><strong>You are already using classic Profit Center Accounting and want to introduce segments later on. Can you use profit center mass maintenance for the initial assignment of the profit centers to segments or to change assignments for profit centers not posted to yet?</strong></p>\r\n<p><br />SAP Note 1101561 manages the change options for the segment in profit center mass maintenance (transaction KE55) using the view V_FAGL_SEGM_PRCT, in the same way as in the individual maintenance for the profit center (transaction KE52). If new General Ledger Accounting is not yet active in your system, the Maintain Segment indicator must also be set in the view V_FAGL_SEGM_CUST so that you can maintain the assignments.   <br />The difference to KE52 is that the check for existence of transaction data (which may be very time-consuming) in mass maintenance cannot take place until you save.   It is not a problem if the segment was initial beforehand, in other words, the profit center was not assigned to any segment. If a segment was assigned before, the change may be 'rejected' (with the relevant error message). In mass maintenance, it is not possible to perform prior checks and then set the segment field for a profit center to Can be changed, and set to Cannot be changed for the next one.  <br /><br /></p>\r\n<ol><ol>89.</ol></ol>\r\n<p><strong>Migration and the (EHP3) 'clearing specific to ledger groups' function. What must I take into account?</strong></p>\r\n<p><br />As of  EhP3 (SAP ERP 6.0), the clearing specific to ledger groups function will be available when you use the new general ledger.<br /><br />The following applies to the migration from the classic general ledger to the new general ledger:  It is not technically possible for you to convert the account master records to 'clearing specific to ledger groups' before or during the migration, that is, you cannot implement this together with the migration.<br />Therefore, you must convert the account master records in a separate step after the migration with an active new G/L. This separate step is not part of the SAP General Ledger Migration service.<br /><br />This function is also not taken into account in the migration cockpit for migrations within new General Ledger Accounting where EhP3 is also in use.<br /><br />If you are an EhP3 customer and you are planning a migration using the SAP General Ledger Migration service, inform the General Ledger Migration Back Office.<br /><br /></p>\r\n<ol><ol>90.</ol></ol>\r\n<p><strong>How or where do you install the migration cockpit in the system (NMI_CONT Add-On)?</strong></p>\r\n<p><br />Note the following relevant points:<br /><br />1) Install the cockpit in all of the systems in which the migration is to occur.<br /><br />2) The NMI_CONT  add-on is an official SAP add-on and is handled as such. The way in which the customer performs the installation depends mainly on customer-specific internal processes.<br /><br />3) See SAP Notes 97620 and 97621 for general information about installing add-ons (TCode SAINT) and Support Packages (TCode SPAM).<br /><br />4) You must attend Course AC212 for detailed information about handling the cockpit.<br /><br />If you encounter technical problems, contact Support. If you have other, general questions about installation and distribution within the system landscape, contact Remote Consulting.<br /><br /></p>\r\n<ol><ol>91.</ol></ol>\r\n<p><strong>What options does SAP offer for mapping parallel accounting and which options are supported by standard migration scenarios?</strong></p>\r\n<p><br />The following solutions/options are available for mapping parallel accounting in an SAP system:<br />1) Account solution<br />=&gt; possible in the classic general ledger and the new general ledger.<br /><br />2) Ledger approach in FI-SL (Special Purpose Ledgers (of the component FI-SL) are updated by assigning accounting principles)<br />=&gt; possible (only) with the classic general ledger<br /><br />3) Company code solution<br />=&gt; possible only in the classic general ledger<br /><br />4) Ledger approach in the new general ledger<br />=&gt; possible only in the new general ledger<br /><br />5) Special customer solution<br /><br />Standard migration scenarios support only (continuation of) the accounts approach or the replacement of the accounts approach by the ledger approach in the new general ledger.<br />If you use mapping options 2, 3 or 5 in the classic general ledger, and you want to switch to the account approach or ledger approach within the migration, this is always a customer specific migration. That is, this type of migration cannot be mapped completely with any of the available standard scenarios. Migrations such as this are often executed as customer-specific projects based on scenario 2 or 3. A customer-specific solution would also be possible on the basis of the migration scenario 4 or 5.  Whether or not this is possible, or whether other alternatives exist, varies from customer to customer.<br />It is not possible to continue to use or to implement options 2, 3 or 5 in a migration to the new general ledger.<br /><br /></p>\r\n<ol><ol>92.</ol></ol>\r\n<p><strong>Why can I not specify a tax code in transaction FB01L (ledger-specific posting) even though the account is tax-relevant?</strong></p>\r\n<p><br />We are not aware of any business transactions in which ledger-specific postings require a tax key. Therefore, this field does not exist and is also not checked unlike in transaction FB01. Sales tax-relevant postings are always posted in all ledgers.<br /><br />This may result in certain inconsistencies because (unlike posting with transaction FB01L) when you use periodic APC posting with transaction ASKBN, the tax code is set for ledger-specific posting. This is due to the attributes of the G/L account. Since Asset Accounting always posts net, you should always set the \"Posting without tax allowed\" indicator for the relevant G/L accounts.<br /><br /><br /></p>\r\n<ol><ol>93.</ol></ol>\r\n<p><strong>What must I consider when adding a new customer field (=&gt; the customer field or user field was not previously used) in a migration without document splitting?</strong></p>\r\n<p><br />- If you want to fill the field for the migration of documents in phase 1 and save the field in the totals table, it must (at least) be available at coding block level (=&gt; CI_COBL) as of the migration date, and a derivation logic must be implemented (by the customer).<br />To fill the field in the totals table during the migration (that is, during the actual transfer), then this must also contain the customer field and you have to assign it accordingly using Customizing transaction \"Assign Scenarios and Customer Fields to Ledgers\".<br /><br />- For the balance carryforward of G/L accounts not managed on an open item basis in phase 0, you can use the standard BAdI to subsequently add a new user field (with implementation by the customer).<br /><br />- For open items in phase 0, there is no standard option (for example, BAdI or program) as of August 2008 to subsequently add the user field.<br /><br /></p>\r\n<ol><ol>94.</ol></ol>\r\n<p><strong>Can I perform a chart of accounts conversion if General Ledger Accounting (new) is active? See the next FAQ, also.</strong></p>\r\n<p><br />Yes, this is usually possible. SAP (=&gt; the department Data Management &amp; Landscape Transformation / DM&amp;LT, SLO service) offers the service &#x201C;Chart of Account Conversion&#x201D; for this purpose. For more information about the chart of accounts conversion, see http://support.sap.com/dm&amp;lt.<br /><br /></p>\r\n<ol><ol>95.</ol></ol>\r\n<p><strong>Can I perform a SAP Landscape Transformation (SAP LT) chart of accounts conversion in the same fiscal year as the migration?</strong></p>\r\n<p><br />Basic information about the execution of SAP new General Ledger migrations and SAP LT projects is available under FAQ 111.<br />This SAP Note shows the connections between an SAP new General Ledger migration and an SAP LT project, for example, using a chart of accounts conversion.<br />.<br /><strong>Regarding the </strong><span style=\"text-decoration: underline;\"><strong>sequence</strong></span><strong> of the two projects, you must also consider the following:</strong><br /><br /><strong>=&gt; For migration scenarios 1 and 2:</strong>  You can always perform the chart of accounts conversion before (in phase 0, but N0T in phase 1) and after the productive migration - there are usually no problems in this regard.<br /><br /><strong>=&gt; For migration scenario 3 (=&gt; with document splitting):</strong> You should perform the chart of accounts conversion <strong><strong>before</strong></strong> the migration.<br /><span style=\"text-decoration: underline;\">Reason</span>: During a chart of accounts conversion, several accounts are usually merged. In other words, there are usually fewer accounts after the chart of accounts conversion than before the conversion. If, for example, you merge three accounts (which are assigned three item categories in new general ledger Customizing) into one account, then this account will also have only one item category. This may mean that some processes can no longer be displayed/posted/entered with the specified document splitting rules, and the system may generate an error during posting. As a result, you must adjust the document splitting rules accordingly (if possible).<br />The changes mentioned above and the conversion tests are easier to perform in migration phase 0 (that is, before productive migration in the classic general ledger) than at a later stage when the new general ledger is active (after the migration). If you want to perform the chart of accounts conversion and the migration to the new G/L in parallel, you must ensure that both projects are tested and subsequently executed with the same data basis. This means, for example, that you must first test the chart of accounts conversion and use the result as the data basis for the migration to the new G/L.<br />See also SAP Note 891144 for information about configuration changes after activating the new general ledger.<br /><br /><strong>=&gt; For migration scenario 4:</strong> Again, the time is not important here. However, the accounts approach concept MUST be retained after the chart of accounts conversion.<br /><span style=\"text-decoration: underline;\">This means the following</span>: Accounts that mirror different accounting principles for an accounts approach. For example, even though account 14711 and account 94711 can be converted (by SLO) (for example, to accounts 11234 and 91234), they cannot be deleted or merged into one account (for example, account 4711).<br /><span style=\"text-decoration: underline;\">Reason</span>: If you do this, the system displays only one amount (and this amount is incorrect) when the new account (=&gt; 4711) is valuated.<br /><span style=\"text-decoration: underline;\">Example (=&gt; chart of accounts conversion before migration)</span>:<br />Balance (international) account 14711: 200 Euro<br />Balance (local) account 94711: 300 Euro<br />=&gt; Assuming the accounts are now merged into account 4711:<br />There is only one amount (=&gt; 500 Euro), and it is not clear whether this is the local or the international approach.<br /><span style=\"text-decoration: underline;\">A frequent incorrect assumption (=&gt; for chart of accounts conversion after migration)</span>: During the migration, the accounts are merged. For example, as explained above, accounts 14711 and 94711 are merged into one account 4711.<br />It is correct that this occurs in the migration by transfer postings, and you can then valuate this one account for different ledgers (=&gt; different accounting principles). After this you can delete the original accounts (14711 and 94711), for example using the SLO service, because these accounts are no longer required. Even though the accounts are no longer required in the future, if account 4711 is valuated before the migration date, only one amount is displayed (and this amount is incorrect) - see above.<br /><span style=\"text-decoration: underline;\">Therefore</span>: Even after migration, the chart of accounts conversion must take into account the accounts approach logic/concept to ensure that data can be correctly evaluated before the migration date.<br /><br /><strong>=&gt; For migration scenario 5:</strong> The above remarks on migration scenarios 3 <span style=\"text-decoration: underline;\">and</span> 4 must be observed.<br /><br /><strong>=&gt; For migration scenario 6: </strong>The above remarks on migration scenario 3 must be observed.<br /><br /><strong>=&gt; For migration scenario 7: </strong>For this migration scenario, we also recommend that you execute a chart of accounts conversion <strong>before</strong> the migration (in other words in phase 0, before the migration date) or after the productive migration. Refer to the further information concerning migration scenario 4.<br /><br /><strong>=&gt; For migration scenario 8: </strong>In this migration scenario, you should also execute a chart of accounts conversion either before the migration (phase 0) or after the migration. Do not execute a chart of accounts conversion in migration phase 1. Also refer to the further information concerning migration scenario 4.<br /><br /><strong>Additional information if a conversion of the chart of accounts is planned for the new fiscal year after the migration:</strong> For all of the migration scenarios, the following statement from SAP Note 1158830 applies: \"... \"Note that when a local currency changeover or a conversion of the chart of accounts is planned for the new fiscal year [=&gt; fiscal year after the migration], or another cross-application activity, you must deactivate the GLT0 update at the end of the preceding fiscal year [end of the fiscal year of the migration]. For more information, see SAP Note 1339877.\"<br /><br /></p>\r\n<ol><ol>96.</ol></ol>\r\n<p><strong>Migration to General Ledger Accounting (new) and simultaneous introduction of a shortened fiscal year. What must I consider?</strong></p>\r\n<p><br />If you introduce a shortened fiscal year, this changes the fiscal year end and may also affect the planned <span style=\"text-decoration: underline;\">migration date</span> (for example, before the shortened fiscal year is introduced, the fiscal year end is December 12; after the shortened fiscal year is introduced, the fiscal year end changes to September 30). You must take this into consideration when planning the migration to the new general ledger. For more information about shortened fiscal years, see SAP Note 672255.<br /><br /></p>\r\n<ol><ol>97.</ol></ol>\r\n<p><strong>How do the migration date and the date of the live migration (new G/L Go-Live) relate to each other?</strong></p>\r\n<p><br />The migration date <strong>must</strong> be the first day of a fiscal year.<br /><strong>Example and assumptions:</strong> You are using the FI fiscal year variant K4 (12 periods + 4 special periods and a calendar year = a fiscal year). This means that the migration date is always January 1. It is still assumed that the current date is June 7, 2008.<br /><br />Assuming that the migration will be implemented in 2009 (for example an April or May weekend), various configurations will already be required in 2008, including in the live system. For precise details on these configurations, see SAP Standard Training AC212.<br />In the migration cockpit (SAP tool for migrating FI data from the classic G/L to the new G/L - for more information, see the FAQ \"What is a migration cockpit?\" in this SAP Note), the migration date is still set for January 1, 2009.<br />As already explained, the actual live migration / go-live for the new G/L will be carried out in Spring 2009.<br /><br />If it not possible to avoid shifting the go-live, make sure that the live migration of your data planned for January 1, 2009 is still migrated in 2009. <strong>It is <span style=\"text-decoration: underline;\">not</span> possible to shift the go-live to 2010 if the migration date has already been set for January 1, 2009.</strong> This would constitute a \"cross-fiscal year migration\".<br />However, if the given circumstances force a live migration in 2010, you will have to create a new project (migration package) with the migration date set for January 1, 2010 because cross-fiscal year migrations are not supported by SAP.<br /><br /></p>\r\n<ol><ol>98.</ol></ol>\r\n<p><strong>Are there any restrictions that I should bear in mind during a new G/L migration when using the Lease Accounting Engine (LAE)?</strong></p>\r\n<p>No, if you use the LAE with version 03/2008, there are no limitations when using it with a new G/L.<br /><span style=\"text-decoration: underline;\">Caution</span>: However, if you use the complete the SAP Leasing Process with FI-CA, and the corresponding document summarization, the restrictions in this SAP Note still apply - see the FAQ \"Is the new general ledger compatible with FI-CA (FI &#x2013; Contract Accounting)?\" See also SAP Note 893906.<br /><br /></p>\r\n<p>In addition, bear in mind the restrictions associated with the LAE in the context of General Ledger Accounting (new) with Asset Accounting (new).&#x00A0;In this context, see also SAP Note 1776828 or the release notes for EhP7 Support Package 2.</p>\r\n<ol><ol>99.</ol></ol>\r\n<p><strong>Is there a relationship between the leading ledger of General Ledger Accounting (new) and depreciation area 01 of Asset Accounting (component FI-AA) in the case of a migration with scenario 4, 5, 7, or 8?</strong></p>\r\n<p><br />If you will be using the ledger approach in General Ledger Accounting (new) and FI-AA in future, you <span style=\"text-decoration: underline;\">must</span> assign depreciation area 01 of Asset Accounting to the leading ledger. For technical reasons, there is no other option (at present).<br />As a result of this assignment requirement, it is useful to map the same accounting principle in FI-AA depreciation area 01 as in the leading ledger. In the majority of cases, this is then an internationally valid accounting principle or a group accounting principle, because for a ledger approach in the new general ledger, the leading ledger should generally reflect the group approach. <br />If you continue to map a local accounting principle in FI-AA depreciation area 01 (typically, from the history), then as a consequence you have to carry out a depreciation area change. We support such a depreciation area change by means of a conversion service. If you are interested in this conversion service, send an e-<NAME_EMAIL>.<br /><span style=\"text-decoration: underline;\">Important</span>: You must carry out the depreciation area change <span style=\"text-decoration: underline;\">before</span> the General Ledger Accounting (new) migration project.<br /><br /></p>\r\n<ol><ol>100.</ol></ol>\r\n<p><strong>Why are there no concrete statements about the runtimes of the migration programs?</strong></p>\r\n<p><br />Because runtime depends on various factors such as (note that the following is not a complete list):<br />- The operating system<br />- The configuration of your hardware and your server<br />- The configuration of your database<br />- Usage of (customer-specific) BAdI implementations<br />- The volume of data to be migrated<br />- The number of background or dialog processes (during the migration)<br />- The new general ledger configuration, for example, the characteristics that are updated<br />- The migration scenario<br />- The data quality of the FI documents in the classic general ledger<br />- The applications in use<br />- ...<br /><br /></p>\r\n<ol><ol>101.</ol></ol>\r\n<p><strong>How can I evaluate FI data from migration phase 0 (that is, from fiscal years before the migration year or migration date) after a new general ledger migration?</strong><br /><strong>Key phrase: \"Cross-fiscal year reporting\" after migration</strong></p>\r\n<p><br />As part of the migration, (only) the balance carryforwards (in the migration year) for accounts not managed with open item management are transferred (for example, from the classic FI totals table GLT0) to the new general ledger (that is, in period 0 of the migration year of the new general ledger totals table).<br />For G/L accounts and customer and vendor reconciliation accounts managed on an open item basis, the items of previous years (with the original posting date) that are (still) open are transferred. The system then uses them to create the balance carryforward for the OI accounts in the migration year only.<br />You then discover that you can only find rudimentary data (only the values of the accounts managed on an open item basis in the fiscal years of the original posting date) for fiscal years <span style=\"text-decoration: underline;\">before</span> the migration date in the tables of General Ledger Accounting (new) <span style=\"text-decoration: underline;\">after</span> the migration has been completed. However, these tables are evaluated after a migration by the standard reports (for example, the balance sheet report RFBILA00) when General Ledger Accounting (new) is active. <br />It is no longer useful or possible to attempt to use the standard reports to carry out an evaluation for fiscal years before the migration when the new general ledger is active.<br /><br />For (comparative) evaluations before the migration date, we recommend that you work with a rollup ledger or use the capabilities of the BI (that is, the SAP Business Warehouse). For more information, see SAP Note 893206.<br /><br />The report RFBILA10 offers an alternative. You can use it to carry out reports for two ledgers (classic FI ledger 00 and General Ledger Accounting (new)). See SAP Note 1620177.<br /><br />If you want report on the classic data at account level, see SAP Note 1155999.</p>\r\n<ol><ol>102.</ol></ol>\r\n<p><strong>Do new company codes that have already been copied by an existing company code that has been assigned to a migration plan have to be assigned again (for a second time) to a migration plan?</strong></p>\r\n<p><br />Yes. When a company code is copied, the assignment of the source company code for a migration plan (that is, an entry in table FAGL_MIG_002) is not retained.  Note that the assignment (and the accompanying validation of document splitting*) is carried out in the new company code before the first posting. <br />* The validation of document splitting is only available as of SAP ERP 6.0.<br /><br /></p>\r\n<ol><ol>103.</ol></ol>\r\n<p><strong>What is an access key, why is it necessary and how do you get one?</strong></p>\r\n<p><br />When you create a migration pack (transaction code CNV_MBT_NGLM), you are requested to enter an access key in a separate dialog box. SAP assigns the access key <strong>for each system installation number</strong> and <strong>for each client</strong> (=&gt; not for each migration pack). It is not possible to create or load a migration pack without an access key.<br />For migration scenarios 1-5, the access key sets an upper limit for the migration pack to be created according to the filled questionnaire: This means that when you use an access key, for example, for migration scenario 3, the system also creates or loads the packages for migration scenarios 1 and 2 but not the packages for migration scenarios 4 and 5. For migration package 6 (and the future packages 7 and 8), explicit access keys are delivered for these exact migration scenarios.<br />Receipt of an access key: You will receive the access key for your <strong>production system</strong> shortly after signing the service contract, usually with the information mail from the SAP General Ledger Migration back office, which (also) confirms the release for the download of the Migration Cockpit.<br />To obtain an access key for your <strong>test or development systems or clients</strong> too, send an e-mail to the SAP General Ledger Migration back office (=&gt; e-mail: <EMAIL>). The back office will check your request and then issues you with another access key.<br />You can find more information about access keys in SAP Notes 1140365 and 1162474.<br /><br /></p>\r\n<ol><ol>104.</ol></ol>\r\n<p><strong>Transfer open items (=&gt; OIs) of migration phase 0 from an FI-SL ledger?</strong></p>\r\n<p><br />A customer working with a classical FI-SL ledger (before the implementation of the new G/L or before the migration to the new G/L) who has activated FI-SL document splitting for this FI-SL ledger can transfer the (split) open items when migrating (with scenarios 3 and 5) the OIs of phase 0 from the FI-SL ledger to the new G/L. This is an alternative to the previously only option of transferring the OIs (with a BAdI) based on the table BSEG.<br />Special features and further information - <strong>see SAP Note 1163997:</strong><br />=&gt; This option is available as of SAP ERP 6.0/ECC 6.0. However, you also need Support Package 04 of Add-On NMI-CONT.<br />=&gt; This option is really only available for customers with an FI-SL ledger (with FI-SL document splitting). In other words, it is not for customers who work with the classical profit center ledger 8A.<br /><br /></p>\r\n<ol><ol>105.</ol></ol>\r\n<p><strong>Why is the line item display of an account not possible in migration phase 1 in very specific cases?</strong></p>\r\n<p><br />For a G/L account that is LI-managed (=&gt; indicator XKRES is set in the G/L account master) and to which postings are made in migration phase 1 <strong>in connection with just one non-leading ledger</strong> (=&gt; for example, foreign currency valuation or periodic APC value posting report RAPERB2000 for migration scenario 4 or 5), no line item display can be called for the reporting periods of migration phase 1.<br /><strong>Explanation:</strong> Since such documents already (must) have a (non-leading) ledger group in the document header, they are already recognized in migration phase 1 as documents that are migrated only to a non-leading ledger in the actual migration. Therefore, already in migration phase 1, these documents are saved only in the table BSEG_ADD, and not in the tables BSIS and BSEG. However, the line item display of a G/L account requires the entry in the table BSIS. <br />After the actual migration, that is, in migration phase 2, the line item display (of the general ledger view) is possible again, because here the (migrated) documents or values are read from the table FAGLFLEXA. <br /><br /></p>\r\n<ol><ol>106.</ol></ol>\r\n<p><strong>What types of standard SAP migration scenarios are available?</strong></p>\r\n<p><br />The following standard SAP migration scenarios are available:<br /><br />=<strong>&gt; Migration scenarios that enable a changeover from classic General Ledger Accounting to new General Ledger Accounting:</strong> These migration scenarios are required if you still use classic General Ledger Accounting (=&gt; totals table GLT0).<br />This includes <strong>migration scenarios 1 to 5</strong>. For more information about the scenarios, go to www.support.sap.com/glmig.<br /><br /><strong>=&gt; Migration scenarios that enable subsequent implementation of further functions of new General Ledger Accounting:</strong> You can use these migration scenarios if you (already) use General Ledger Accounting (new) (=&gt; totals table FAGLFLEXT) and want to use further functions of General Ledger Accounting (new). This includes the following scenarios:<br /><strong>- Migration scenario 6:</strong> Subsequent implementation of document splitting<br /><strong>- Migration scenario 7:</strong> Subsequent implementation of an additional (non-leading) ledger<br /><strong>- Migration scenario 8:</strong> Subsequent changeover from the accounts approach to the ledger approach (in General Ledger Accounting (new)).<br />For more information about these scenarios, go to www.support.sap.com/glmig.<br /><br /></p>\r\n<p>In <em>SAP ERP</em>, in General Ledger Accounting (new) with migration scenarios 7 and 8, you can subsequently implement a new ledger and subsequently switch from the accounts approach to the ledger approach. These migration scenarios are supported in classic Asset Accounting, but <strong>not</strong> in Asset Accounting (new).</p>\r\n<ol><ol>107.</ol></ol>\r\n<p><strong>When, how and where are classic applications deactivated after the migration to new General Ledger Accounting?</strong></p>\r\n<p><br /><strong>Classic General Ledger Accounting </strong>(=&gt; totals table GLT0)<strong>:</strong> We recommend that you keep the period of the parallel update of the totals record tables of classic and new General Ledger Accounting as short as possible, and deactivate the update of the table GLT0 at the end of the fiscal year of the migration at the latest. To deactivate the GLT0 update in Customizing, go to Financial Accounting (New) -&gt; Financial Accounting Global Settings (New) -&gt; Tools -&gt; Deactivate Update of Classic General Ledger (GLT0).<br />See also <strong>SAP Note 1158830</strong>.<br /><strong>Caution: </strong>Note the following: If a local currency changeover or another cross-application activity is planned for the fiscal year after the migration, you must deactivate the update of the totals table GLT0 at the end of the migration year. For more information, see <strong>SAP Note 1339877</strong>.<br /><br /><strong>Ledger for cost of sales accounting </strong>(=&gt; totals table GLFUNCT)<strong>:</strong> The Special Purpose Ledger 0F (which is delivered in the standard SAP system and that is often used to display functional areas if the classic general ledger is used) is no longer required after the migration to new General Ledger Accounting because the functional area characteristic is also stored in the totals table FAGLFLEXT. For this reason, you can and should deactivate the ledger for cost of sales accounting (ledger 0F) after a short transition time: Choose \"Customizing -&gt; Financial Accounting -&gt; Special Purpose Ledger -&gt; Basic Settings -&gt; Master Data -&gt; Ledger -&gt; Define Ledger -&gt; Change Ledger\". Select the relevant ledger and delete the assignment to your company codes.<br /><br /><strong>Classic Profit Center Accounting </strong>(=&gt; totals table GLPCT)<strong>:</strong> Since parallel usage is not useful for various reasons, you should deactivate classic Profit Center Accounting after a short transition time.  See also <strong>FAQ 2 </strong>of the <strong>appendix </strong> of this SAP Note.<br />You can deactivate direct posting (for actual data) by choosing  Customizing -&gt; Controlling -&gt; Profit Center Accounting -&gt; Basic Settings -&gt; Controlling Area Settings<br />-&gt; Activate Direct Postings -&gt; Set Control Parameters for Actual Data.<br />For more information, also see <strong>SAP Note 702854</strong>.<br /><br /><strong>Special Purpose Ledgers </strong>(=&gt; customer-specific Z totals table)<strong>:</strong> After a short transition time, you can and should deactivate the update of Special Purpose Ledgers that offer <strong>similar functions to General Ledger Accounting (new)</strong> and whose functions have been integrated into General Ledger Accounting (new) and are therefore no longer required in the future: Customizing -&gt; Financial Accounting -&gt;<br />Special Purpose Ledger -&gt; Basic Settings -&gt; Master Data -&gt; Ledger -&gt; Define Ledger -&gt; Change Ledger. Select the relevant ledger and delete the assignment to your company codes.<br /><br /><strong>Reconciliation ledger:</strong> If General Ledger Accounting (new) is active, the reconciliation ledger is (automatically) no longer available for use (immediately in the standard system). The real-time integration of CO with FI replaces this classic function. An additional activity is not required. However, you can also choose to deactivate the reconciliation ledger in the Customizing for CO: SAP Customizing Implementation Guide -&gt; Controlling -&gt; Cost Element Accounting -&gt; Reconciliation Ledger -&gt; Activate/Deactivate Reconciliation Ledger.<br /><br /></p>\r\n<ol><ol>108.</ol></ol>\r\n<p><strong>Do existing SAP customers (=&gt; with Release R/3 4.7 or lower) have to use new General Ledger Accounting after an upgrade to the solutions mySAP ERP 2004 or SAP ERP 6.00?</strong></p>\r\n<p><br />No After a technical upgrade to the solutions mentioned above, classic General Ledger Accounting (=&gt; totals table GLT0) remains activated, and new General Ledger Accounting (=&gt; totals table FAGLFLEXT) remains deactivated.<br />If you want to make use of the benefits of new General Ledger Accounting, you must execute a migration to the new G/L (=&gt; this FAQ note exists for this reason).<br />If you simply want to continue using the classic General Ledger, you do not have to perform any \"changeover actions\".<br />In the case of <strong>new customers</strong> (=&gt; not a technical upgrade of an existing SAP ERP system, but a new installation instead), see SAP Note 999614 for information about this.<br /><br /></p>\r\n<ol><ol>109.</ol></ol>\r\n<p><strong>Can quantity specifications be migrated (in FI documents)?</strong></p>\r\n<p><br />If you want to migrate the values of the fields MENGE and MEINS (of the table BSEG) during the migration to the new general ledger from the classic general ledger, specific prerequisites must be fulfilled in the system. For information, see <strong>SAP Notes 1477286 and 1477363</strong>.<br /><strong>Caution:</strong> All related SAP Notes must already have been implemented in phase 0 in your system to ensure a correct migration in relation to quantities.<br /><br /></p>\r\n<ol><ol>110.</ol></ol>\r\n<p><strong>What must I do to create ledger-specific postings (transaction FB01L) with the program RFBIBL00?</strong></p>\r\n<p><br />Transaction FB01L is not supported by the batch input program RFBIBL00. Use transaction FB01, which also creates ledger-specific postings if the field BBKPF-LDGRP is filled.<br /><br /></p>\r\n<ol><ol>111.</ol></ol>\r\n<p><strong>What must I take into account for a SAP Landscape Transformation (SAP LT) project and a migration to General Ledger Accounting (new)?</strong></p>\r\n<p><br />We strongly recommend that you execute SAP LT projects and a migration to General Ledger Accounting (new) separately.<br />You can execute an SAP LT project and a migration in the same fiscal year. However, you must make clear differentiations and several (general) points must be considered:<br />This concerns two independent projects. However, these projects may have dependencies and should therefore be reconciled with each other. Ensure that you keep this in mind when planning both projects. You must inform the relevant SAP contact person about the planned projects.<br /><br /><span style=\"text-decoration: underline;\">Sequence if document splitting to be implemented:</span><br />If you execute an SAP new General Ledger Migration Service in which document splitting is active, we recommend that you execute the SAP LT project first because the SAP LT project may be more complex when document splitting is active.<br />An SAP LT project should be executed either before the migration to General Ledger Accounting (new), in other words, in phase 0 (before the migration date), or after the productive migration to General Ledger Accounting (new).<br />FAQ 95 shows the connections between an SAP new General Ledger Migration and an SAP LT project, for example, using a SAP LT chart of accounts conversion.<br /><br /></p>\r\n<ol>112. Can the SAP standard program RGURECGLFLEX be used as a replacement for the migration?</ol>\r\n<p><br />In the standard SAP system, programs that are not released for customer use are delivered. <br />Among other things, this includes the follow-up posting program RGURECGLFLEX.<br />This program must never be used for migration purposes because incorrect<br />use of this report may result in inconsistencies. The incorrect use of RGURECGLFLEX<br />by customers or consultants may lead to a system standstill. For follow-on problems of this type,<br />Support cannot provide maintenance. For more information, see SAP Note 1355813.<br />____________________________________________________________________<br /><br /><br /><strong>Appendix:</strong> Questions that <span style=\"text-decoration: underline;\">do not directly</span> apply to General Ledger Accounting (new) but which may be of interest in a migration project include:</p>\r\n<ol><ol>1.</ol></ol>\r\n<p><strong>In ECC 5.0, which functions are not supported and not released for the new general ledger? </strong></p>\r\n<p><br />For information, see SAP Note 741821, section FI-GL-GL, Release with limitations, New General Ledger: <br />\"The following functions are not supported for the new general ledger and are therefore not released:...\"<br /><br /></p>\r\n<ol><ol>2.</ol></ol>\r\n<p><strong>When using new General Ledger Accounting, should I continue to map Profit Center Accounting in the EC-PCA component, or map it in the new general ledger, or can I use \"parallel processing\"?</strong></p>\r\n<p><br />As a basic principle, we strongly recommend that you do not use \"parallel processing\". For more information (including reasons and examples) about this subject area, see SAP Note 826357.<br /><br /></p>\r\n<ol><ol>3.</ol></ol>\r\n<p><strong>Is the new general ledger compatible with FI-CA (FI &#x2013; Contract Accounting)? </strong></p>\r\n<p><br />This question is relevant for each Industry Solution that uses component FI-CA (Contract Accounting) as accounts receivable accounting (such as IS-U, IS-T, IS-M, IS-PS-CA, FS-CD) and the non-industry-specific contract accounting FI-CAX.<br />As far as possible, FI-CA uses the new general ledger in the same way it uses the classic general ledger. In addition, (since SAP ERP 6.0) FI-CA supports the account assignment \"Segment\", which was introduced (with mySAP ERP 2004) as a new standard account assignment in the new general ledger.  (=&gt; see also part 1 of the additional information at the end of this FAQ)<br />With SAP ERP 6.04 (SAP ERP 6.0 with Enhancement Package 4), the Profit Center is provided in FI-CA as an account assignment object for postings to Contract Accounts Receivable and Payable.<br /><br />The following general statement applies in relation to the compatibility of FI-CA and the new general ledger with active document splitting: If you are using FI-CA and you are using document splitting in new General Ledger Accounting, you must ensure that the corresponding required characteristics are enriched in FI-CA. Specifically, this means that FI documents that were transferred from FI-CA to the new general ledger can contain characteristic information only if they already contained it beforehand (in other words, in the FI-CA document). This applies for all characteristics, such as the characteristics for the business area, the Profit Center, the segment and customer fields. (=&gt; see also part 2 of the additional information at the end of this FAQ)<br /><span style=\"text-decoration: underline;\">Therefore</span>: Document splitting activities or functions (such as the creation of additional document lines or document items for the FI general ledger update, inheritance of characteristics or characteristic assignments using a constant) do not exist for transferred FI-CA documents.<strong>*</strong> (=&gt; see also part 3 of the additional information at the end of this FAQ)<br />This results in the following situation for the FI document transferred from FI-CA: The document is posted in the new general ledger, despite the fact that characteristics are missing according to the Customizing for the new general ledger. Therefore, the system does not issue an error message if a characteristic is defined as a required entry field in the new general ledger or in Customizing for document splitting.  (=&gt; see also part 4 of the additional information at the end of this FAQ) <strong>Note the following:</strong> The system response described also applies to subsequent processes in FI. This means that it also applies to subsequent documents for FI documents that were generated from FI-CA originally. Example: A clearing to bank subaccounts cannot be assigned completely for this reason.<br />You must check the compatibility in each case depending on the industry solution you are using.<br /><br /><strong>*</strong> There is one exception in this case: Document splitting logic can be used to create clearing lines. (=&gt; see also part 5 of the additional information at the end of this FAQ)<br /><br />With regard to a <strong>migration</strong> to General Ledger Accounting (new), the following applies in the context described: For FI documents that are transferred from FI-CA to the general ledger, the validation of document splitting (in the same way as described above) does <strong>not</strong> stop documents that are not assigned completely.<br /><br />With Enhancement Package 2 (=&gt; SAP ERP 6.02), FI-CA supports the ledger group at the level of the  FI-CA general ledger items and requires a zero balance for each ledger group within a document. This means that the FI-CA document first provides this option in all programs that create documents with valuation variances (such as the foreign currency valuation). However, these programs then have no ledger group in their initial data that they use for document creation. Contact SAP Consulting in individual cases.<br /><br /><strong>Additional information:</strong><br />1. The system uses FI-CA-specific rules to carry out segment derivation. These rules differ from the splitting logic in the new general ledger.<br />2. For reconciliation reasons, FI documents from FI-CA cannot be enriched with FI characteristics in FI since the reconciliation occurs at account assignment level (for example, business area, segment, profit center) in FI-CA .<br />3. Certain functions of document splitting (active split, inheritance, constants) are not available since the FI document from FI-CA is posted in summarized form and the process relationship cannot be recognized from the FI document.<br />4. The FI-CA documents are not enriched with account assignments of the General Ledger as standard. Therefore, the validation of the characteristics (validation in document splitting in accordance with the characteristic definition in Customizing) is deactivated in FI.<br />5. Document splitting ensures that the balance of the documents is zero in accordance with the characteristic definition (creation of clearings).<br /><br /></p>\r\n<ol><ol>4.</ol></ol>\r\n<p><strong>Is new General Ledger Accounting compatible with IS-A (Industry Solution Automotive)?</strong></p>\r\n<p><br />See SAP Note 927241.<br /><br /></p>\r\n<ol><ol>5.</ol></ol>\r\n<p><strong>Is the new general ledger compatible with Financial Services SAP Leasing?</strong></p>\r\n<p><br />See SAP Note 893906.<br /><br /></p>\r\n<ol><ol>6.</ol></ol>\r\n<p><strong>Is the new general ledger compatible with Real Estate (RE)?</strong></p>\r\n<p><br />See SAP Note 784567.<br /><br /></p>\r\n<ol><ol>7.</ol></ol>\r\n<p><strong>What must I consider in a distributed system landscape regarding the new general ledger (ALE)? </strong></p>\r\n<p><br />For basic information about the new general ledger and ALE, see SAP Note 114814. <br />For further information, see SAP Notes 892103, 892366, 899254 and 897083.<br /><br /></p>\r\n<ol><ol>8.</ol></ol>\r\n<p><strong>Are there any restrictions when using transfer prices in the new G/L?</strong></p>\r\n<p><br />See the \"Transfer prices\" section in SAP Note 826357.<br /><br /></p>\r\n<ol><ol>9.</ol></ol>\r\n<p><strong>What is the relationship between the new general ledger and the material ledger?</strong></p>\r\n<p><br />You can use the material ledger in combination with the classic general ledger and in combination with the new general ledger. <br />It is not possible to replace the material ledger with the new general ledger.<br /><br /></p>\r\n<ol><ol>10.</ol></ol>\r\n<p><strong>Combination of new General Ledger Accounting and consolidation (with a distinction between EC-CS, the R/3-based consolidation, and SEM-BCS, the BI-based consolidation).</strong></p>\r\n<p><br /><span style=\"text-decoration: underline;\">Information about EC-CS</span>: You can also carry out an <strong>online transfer</strong> from FI (=&gt; General Ledger Accounting (new)) to EC-CS as before in the classic FI environment if you do not use document splitting.<br />If document splitting is active, all of the lines from the FI document that are in the BSEG table are transferred to EC-CS. This includes the lines in the BSEG table that are split automatically in the new general ledger in the same way as in the classic environment. This means that the information added by document splitting (new line items and field contents that are not in the BSEG table) is not transferred to EC-CS. *<br />Therefore, the realtime update is useful only if you do not require any field contents in EC-CS that are filled by document splitting in the new general ledger.<br />Additional information: Transaction codes for reconciling the totals tables:<br />=&gt; classic general ledger (reconciliation of the ECMCT and GLTO totals tables): transaction CXNR<br />=&gt; new general ledger (reconciliation of the ECMCT and FAGLFLEXT totals tables): transaction CXNZ<br />* In technical terms: In the AC interface (function module AC_DOCUMENT_CREATE), the EC-CS component processes the field contents of the T_ACCHD, T_ACCIT and T_ACCCR structures, but it does not process the T_ACCIT_SPL and T_ACCCR_SPL structures.<br /><br />With regard to the <strong>periodic extract and rollup</strong> (of EC-CS): Both are also possible in the new general ledger:<br />=&gt; Direct rollup from the new general ledger totals table FAGLFLEXT<br />=&gt; Periodic extract (still) from totals table GLT3 (trading partner characteristics and consolidation transaction type) and including totals table FAGLFLEXT (the account balances in particular). Totals table GLT0 is no longer used.<br />However, this means: Although you may (also) save the trading partner characteristics and consolidation transaction type in the FAGLFLEXT totals table, you <span style=\"text-decoration: underline;\">cannot</span> deactivate the GLT3 totals table for the preparations for consolidation if you are using the periodic extract.<br />Important: As before, the (new) FI segment characteristic is not persistent in the GLT3 totals table. However, if you want to carry out consolidation using the segment characteristic despite this, you can only use the rollup in conjunction with user exits.<br /><br /><span style=\"text-decoration: underline;\">Information about SEM-BCS</span> and profit center consolidation in particular: Profit Center Consolidation is also available when the new general ledger is active.  For more information, see SAP Notes 852971 and 826357.<br /><br />See also <strong>SAP Note 1256119</strong>.<br /><br /></p>\r\n<ol><ol>11.</ol></ol>\r\n<p><strong>Which extractors are available to extract data from the new general ledger to BI (Business Warehouse)? </strong></p>\r\n<p><br />In ECC 5.0 (=&gt; mySAP ERP 2004) and ECC 6.0 (=&gt; SAP ERP 6.0), there is an extractor for extracting totals records from the new general ledger to BI: 0FI_GL_10.<br /><strong>Note the following:</strong> As of Enhancement Pack 3 (=&gt; EHP3 in General Ledger Accounting (new)), you can also use the line item extractor 0FI_GL_14 (=&gt; DataSource: BWBCT_DS_0FI_GL_14). This also allows you to extract single documents of the leading ledger for BI reporting.<br />You can also generate extractors for non-leading ledgers (=&gt; transaction code FAGLBW03). The generated extractors are called 3FI_GL_XX_SI, where XX is the name of the ledger.<br />For further information, see (for example) the SAP Library under \"General Ledger (New): Line Items of Leading Ledger\" or the documentation for the FIN_GL_CI_1 Business Function (=&gt; transaction code SFW5). <br /><br /></p>\r\n<ol><ol>12.</ol></ol>\r\n<p><strong>What do I have to consider if I use the new general ledger and HR?</strong></p>\r\n<p><br />Check whether SAP Notes 911172 and 1006691 are relevant for you.<br /><br /></p>\r\n<ol><ol>13.</ol></ol>\r\n<p><strong>How do I configure Asset Accounting (FI-AA) so that the same account determination is used in non-leading ledgers of the new general ledger as in the leading ledger when I use the ledger approach in new General Ledger Accounting?</strong></p>\r\n<p><br />All depreciation areas should use the same accounts as the leading depreciation area.  To ensure this, fill the field \"Different Depreciation Area\" in transaction OADB with the correct values.  This field defines the depreciation area for account determination.  If you do not want to specify a different depreciation area, you must maintain at least the reconciliation accounts in exactly the same way to prevent reconciliation differences.</p>\r\n<ol><ol>14.</ol></ol>\r\n<p><strong>Is it possible to assign the scenario FIN_SEGM (Segmentation) to a ledger if this ledger does not have the scenario FIN_PCA (Profit Center Update) assigned? </strong></p>\r\n<p><br />The use of segments has been officially released by SAP in combination with the usage of profit centers only. For more information, see also SAP Note 1035140.<br /><br /></p>\r\n<ol><ol>15.</ol></ol>\r\n<p><strong>Why does an FI-AA posting </strong></p>\r\n<ol><ol>(still)</ol></ol>\r\n<p><strong>trigger error message GU 444?</strong></p>\r\n<p><br />See SAP Note 1094630 first.<br />In certain circumstances, the error message GU 444 can still occur in asset accounting. This happens when a ledger group assigned to a depreciation area contains exactly one ledger, and this ledger is not used for the company code currently being used. However, if there are ledger groups with more than one ledger, and one of these ledgers is not used in a company code, you can still make a posting.<br /><br /><span style=\"text-decoration: underline;\">Reason</span>: If asset accounting permitted this configuration, values and line items would still occur in the relevant depreciation areas in FI-AA, but values would not be updated in the corresponding account / ledger. This means that the RAABST02 report would always show differences, even though there are none.<br /><br /><span style=\"text-decoration: underline;\">Solution</span>:<br />- New customers / new implementation:  Distribute the company codes across different valuation plans<br />- Existing customers: All company codes of a valuation plan use the same ledger<br /><br /><strong>Example:</strong><br /><strong>1.</strong><br /><span style=\"text-decoration: underline;\">Ledger Company Code</span><br />H1 1000<br />H1 2000<br />H2 1000<br /><br /><span style=\"text-decoration: underline;\">Ledger Ledger Group FI-AA Depreciation Area</span><br />H1 H1 01<br />H2 H2 60<br />=&gt; Error message GU 444 is issued for company code 2000<br /><br /><strong>2.</strong><br /><span style=\"text-decoration: underline;\">Ledger Company Code</span><br />I1 1000<br />I1 2000<br />H1 1000<br />H1 2000<br />HT*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1000&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *HT = daily ledger<br /><br /><span style=\"text-decoration: underline;\">Ledger Ledger Group FI-AA Depreciation Area</span><br />I1 I1 01<br />H1 H1 60<br />HT H1 60<br />=&gt; Error message GU 444 is not issued for company code 2000 because postings are possible in ledger H1.<br /><br /></p>\r\n<ol><ol>16.</ol></ol>\r\n<p><strong>In which case does the document reversal trigger the reversal process in document splitting, and in which case are the document splitting rules processed?</strong></p>\r\n<p><br />The document splitting processes the reversal process for FI reversals (transaction FB08 and FBU8). During that, no rules are processed. Instead, a reversed document is generated from the account assignment information of the document to be reversed (in the case of reassignments from the profit center to an account assignment object, the profit center is derived again for cost elements/revenue elements). The system determines the business transaction variant of the document to be reversed because some document splitting settings depend on the business transaction variant.<br />You must distinguish between process-based (passive) splitting and rule-based (active) splitting.<br />The system processes the process-based splitting in certain clearing processes and similar processes. This is triggered either by the process itself, for example in transaction FBRA (Reset Cleared Items, which is relevant only in special purpose ledgers with document splitting) or by FI reversal using business transaction RFBU or by the attributes of single document line items (clearing line items, line items belonging to an invoice). <br />For the FI reversal, the process-based splitting is relevant for the entire document. Each document line item \"inherits\" the account assignments of the relevant line item of the source document.<br />In the classic general ledger, the system creates pure clearing documents for zero clearing. The relevant documents do not contain line items.<br />In the new general ledger with active document splitting, the system does not create any zero clearings. The system always creates documents with clearing lines.<br />However, a clearing does not necessarily always have to be a zero clearing. You can also create a residual item or post differences. In this case, the process-based splitting is relevant only for some lines of the document.<br />Conclusion: If there is a rule-based or a process-determined split, it does not always apply to the entire document (that is only in exceptional cases). Usually, <br />only individual line items of a document are affected by the process-determined split - irrespective of which business transaction variant is processed.<br />A rule-based split always applies to business transactions that have no reference to a document that is already posted. In this case, one example is posting an invoice using an FI transaction. In addition, the rule-based split is processed by business transactions, which can be clearing processes or processes similar to clearing this is not necessary in every case. In this case, one example if the reversal by MM using MR8M. Reversals that are not executed using business transaction RFBU process the rule-based split. That is, depending on the document type of the document to be reversed, a reversal document type is assigned in OBA7.<br />In transaction GSP_VZ3, the system finds the business transaction variant for the reversal document type of the document to be reversed. Using the business transaction variant of this reversal document type, the Customizing of this reversal document type (defined in GSP_RD) is processed during the reversal document split. Keep in mind: Depending on the attributes of each line item, process-based (passive) splitting may be processed for single line items in this reversal as well.</p>\r\n<ol><ol>17.</ol></ol>\r\n<p><strong>Additional PCA lines</strong></p>\r\n<p><br />If you map Profit Center Accounting (PCA) in new General Ledger Accounting in mySAP ERP, you can update the additional PCA lines that are know from classic Profit Center Accounting in new General Ledger Accounting as well using consulting note 937872.<br /><br />If you use the transfer price function, you do not require SAP Note 937872. In this case, the additional PCA rows are automatically updated in new General Ledger Accounting if the profit center valuation approach is managed in new General Ledger Accounting.</p>\r\n<ol><ol>18.</ol></ol>\r\n<p><strong>New developments within new General Ledger Accounting, delivered with SAP Enhancement Packages - listed as bullet points:</strong></p>\r\n<p><br /><strong>EhP3 - Business Function FIN_GL_CI_1 (=&gt; New General Ledger Accounting):</strong></p>\r\n<ul>\r\n<li>Posting and clearing specific to ledger groups - transactions FB1SL, FB05L, F13L.</li>\r\n</ul>\r\n<ul>\r\n<li>External transfer of planning data to new General Ledger Accounting - BAPI BAPI_FAGL_PLANNING_POST.</li>\r\n</ul>\r\n<ul>\r\n<li>CO integrated planning for secondary cost elements - important: See also SAP Note 1009299.</li>\r\n</ul>\r\n<ul>\r\n<li>Cumulative entry of planning data for balance sheet accounts</li>\r\n</ul>\r\n<ul>\r\n<li>Drilldown reports for profit center and segments - the following drilldown reporting: Actual/plan/variance profit center group, plan/plan/actual profit center group, key figures profit center group, return on investment profit center comparison, plan/actual/variance segment, plan/plan/actual segment, key figures segment, return on investment segment comparison.</li>\r\n</ul>\r\n<ul>\r\n<li>Tool for transferring Report Writer and Report Painter reports from Profit Center Accounting - transaction FAGL_RMIGR.</li>\r\n</ul>\r\n<ul>\r\n<li>Use of the \"Elimination profit center\" and \"Origin object type\" fields in reports - fields ZZEPRCTR and ZZHOART.</li>\r\n</ul>\r\n<ul>\r\n<li>Line item extractor - see FAQ 11 in this appendix.</li>\r\n</ul>\r\n<ul>\r\n<li>Authorization check for profit center - authorization object K_PCA.</li>\r\n</ul>\r\n<ul>\r\n<li>Conversion of G/L accounts to open item management - program FAGL_SWITCH_TO_OPEN_ITEM - see also SAP Note 175960.</li>\r\n</ul>\r\n<p><br /><strong>EhP4 - Business Function FIN_GL_CI_2 (=&gt; New General Ledger Accounting 2):</strong></p>\r\n<ul>\r\n<li>Assignment overview for profit center - transaction 1KE4.</li>\r\n</ul>\r\n<ul>\r\n<li>Two wizards for Customizing of document splitting - one wizard to configure the document splitting and one wizard to create splitting rules.</li>\r\n</ul>\r\n<ul>\r\n<li>Enhanced standard configuration of document splitting - \"clean\" handling of invoice reductions and security retention amounts in transaction MIRO, in connection with the business function LOG_MMFI_P2P (=&gt; MM, integration of materials management and financial accounting).</li>\r\n</ul>\r\n<ul>\r\n<li>Switch from FI to CO reports - program FAGL_RRI_RECON_CO.</li>\r\n</ul>\r\n<ul>\r\n<li>Validation of account assignment combinations - transaction FAGL_VALIDATE.</li>\r\n</ul>\r\n<ul>\r\n<li>Document display specific to ledger groups</li>\r\n</ul>\r\n<ul>\r\n<li>Separate check of posting period for postings from CO to FI - transaction FAGL_EHP4_T001B_COFI.</li>\r\n</ul>\r\n<ul>\r\n<li>Checking posting period for non-representative ledger - indicator for posting periods in table T001.</li>\r\n</ul>\r\n<blockquote><ol start=\"19\">\r\n<li></li>\r\n</ol></blockquote>\r\n<p><strong>What must I take into account for the General Ledger Accounting (new) migration in connection with PSM-FM Funds Management (FM) and the online payment update?&#xFEFF;<br /></strong></p>\r\n<p>Funds Management (FM) is active in a system in which the General Ledger Accounting (new) migration is carried out. You want to activate the online payment update.</p>\r\n<p>If the online payment update must or should be activated beforehand despite this, the technical split fields (KN fields and VO fields) must not be enriched using the migration BAdI FAGL_MIG_ADJ_ACCIT.</p>\r\n<p>If the account assignments (for example, PRCTR/SEGMENT) are filled via the migration BAdI FAGL_MIG_ADJ_ACCIT, the correct enrichment of the KN fields is not guaranteed. The KN fields can and must not be filled via the migration BAdI FAGL_MIG_ADJ_ACCIT.<br /><br />If the online payment update is activated after the successful General Ledger Accounting (new) migration, the online payment update may not convert all FM documents. For more information, refer to the FM documentation. For more information about the FM Customizing, see SAP Note 1695556.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-GL-MIG (general ledger migration)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D026506)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D034134)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "NewGL_FAQ.pdf", "FileSize": "248", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000241992007&iv_version=0108&iv_guid=2D81E38793775C43A00D470C67E269AB"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "824995", "RefComponent": "CA-EUR-CNV", "RefTitle": "LCC: General questions/answers about local currency changeover in ECC", "RefUrl": "/notes/824995"}, {"RefNumber": "812919", "RefComponent": "FI-GL-MIG", "RefTitle": "SAP ERP new general ledger: Migration", "RefUrl": "/notes/812919"}, {"RefNumber": "672255", "RefComponent": "CA-LT-SRV", "RefTitle": "Shortened fiscal year/changing fiscal periods", "RefUrl": "/notes/672255"}, {"RefNumber": "2405554", "RefComponent": "FI-GL-MIG-TL", "RefTitle": "Incompatibility between Standard Migration Scenarios (Scenario : 7 & 8) and active New Asset Accounting", "RefUrl": "/notes/2405554"}, {"RefNumber": "2403248", "RefComponent": "FI-AA", "RefTitle": "FI-AA (new): Availability of RAFABNEW", "RefUrl": "/notes/2403248"}, {"RefNumber": "1679975", "RefComponent": "FI-GL-MIG", "RefTitle": "FBCB requires a great deal of time during migration", "RefUrl": "/notes/1679975"}, {"RefNumber": "1619168", "RefComponent": "FI-GL-MIG", "RefTitle": "Overview of the different migration scenarios", "RefUrl": "/notes/1619168"}, {"RefNumber": "1600481", "RefComponent": "FI-GL-MIG", "RefTitle": "Migration on basis of Special Purpose Ledger", "RefUrl": "/notes/1600481"}, {"RefNumber": "1592904", "RefComponent": "FI-GL-GL-X", "RefTitle": "FI consistency check in advance of migration to NewGL", "RefUrl": "/notes/1592904"}, {"RefNumber": "1558185", "RefComponent": "FI-GL-MIG", "RefTitle": "Special features during migration to new general ledger", "RefUrl": "/notes/1558185"}, {"RefNumber": "1555535", "RefComponent": "FI-SL-IS-A", "RefTitle": "FAQs: Migration of PCA Report Painter reports to NewGL", "RefUrl": "/notes/1555535"}, {"RefNumber": "1467114", "RefComponent": "FI-GL-MIG-GL", "RefTitle": "Open item total not checked against carryforward", "RefUrl": "/notes/1467114"}, {"RefNumber": "1337521", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1337521"}, {"RefNumber": "1171402", "RefComponent": "IS-M-SD-PS-BL", "RefTitle": "Compatibility IS-M/SD with General Ledger Accounting (new)", "RefUrl": "/notes/1171402"}, {"RefNumber": "1154791", "RefComponent": "FI-GL-MIG", "RefTitle": "Update of segment or functional area in CO", "RefUrl": "/notes/1154791"}, {"RefNumber": "1072121", "RefComponent": "FI-GL-MIG", "RefTitle": "New general ledger migration: What is covered by Support?", "RefUrl": "/notes/1072121"}, {"RefNumber": "1039752", "RefComponent": "FI-GL-MIG", "RefTitle": "New G/L migration: Restrictions, important information", "RefUrl": "/notes/1039752"}, {"RefNumber": "1014369", "RefComponent": "FI-GL-MIG", "RefTitle": "NewGL migration: Availability of Development Support", "RefUrl": "/notes/1014369"}, {"RefNumber": "1014364", "RefComponent": "FI-GL-MIG", "RefTitle": "New G/L migration: Information, prerequisites, performance", "RefUrl": "/notes/1014364"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2911106", "RefComponent": "EC-PCA", "RefTitle": "After system upgrade to S/4HANA, PCA data can not be displayed in classic Profit Center report", "RefUrl": "/notes/2911106 "}, {"RefNumber": "2595776", "RefComponent": "EC-CS-INT", "RefTitle": "EC-CS 'Real-time update': Clarification on usage of reference ledger", "RefUrl": "/notes/2595776 "}, {"RefNumber": "2541064", "RefComponent": "FI-GL-IS", "RefTitle": "FBL3H, FBL3N cannot be found in SAP Easy Access Menu", "RefUrl": "/notes/2541064 "}, {"RefNumber": "2479182", "RefComponent": "FI-GL-GL-A", "RefTitle": "Default document type and posting key value in initial screen", "RefUrl": "/notes/2479182 "}, {"RefNumber": "2405554", "RefComponent": "FI-GL-MIG-TL", "RefTitle": "Incompatibility between Standard Migration Scenarios (Scenario : 7 & 8) and active New Asset Accounting", "RefUrl": "/notes/2405554 "}, {"RefNumber": "672255", "RefComponent": "CA-LT-SRV", "RefTitle": "Shortened fiscal year/changing fiscal periods", "RefUrl": "/notes/672255 "}, {"RefNumber": "1679975", "RefComponent": "FI-GL-MIG", "RefTitle": "FBCB requires a great deal of time during migration", "RefUrl": "/notes/1679975 "}, {"RefNumber": "1337521", "RefComponent": "FI-GL-MIG-GL", "RefTitle": "safety delta in incremental work list update for phase 1", "RefUrl": "/notes/1337521 "}, {"RefNumber": "1014364", "RefComponent": "FI-GL-MIG", "RefTitle": "New G/L migration: Information, prerequisites, performance", "RefUrl": "/notes/1014364 "}, {"RefNumber": "1592904", "RefComponent": "FI-GL-GL-X", "RefTitle": "FI consistency check in advance of migration to NewGL", "RefUrl": "/notes/1592904 "}, {"RefNumber": "1558185", "RefComponent": "FI-GL-MIG", "RefTitle": "Special features during migration to new general ledger", "RefUrl": "/notes/1558185 "}, {"RefNumber": "1619168", "RefComponent": "FI-GL-MIG", "RefTitle": "Overview of the different migration scenarios", "RefUrl": "/notes/1619168 "}, {"RefNumber": "1467114", "RefComponent": "FI-GL-MIG-GL", "RefTitle": "Open item total not checked against carryforward", "RefUrl": "/notes/1467114 "}, {"RefNumber": "1555535", "RefComponent": "FI-SL-IS-A", "RefTitle": "FAQs: Migration of PCA Report Painter reports to NewGL", "RefUrl": "/notes/1555535 "}, {"RefNumber": "1600481", "RefComponent": "FI-GL-MIG", "RefTitle": "Migration on basis of Special Purpose Ledger", "RefUrl": "/notes/1600481 "}, {"RefNumber": "812919", "RefComponent": "FI-GL-MIG", "RefTitle": "SAP ERP new general ledger: Migration", "RefUrl": "/notes/812919 "}, {"RefNumber": "1072121", "RefComponent": "FI-GL-MIG", "RefTitle": "New general ledger migration: What is covered by Support?", "RefUrl": "/notes/1072121 "}, {"RefNumber": "824995", "RefComponent": "CA-EUR-CNV", "RefTitle": "LCC: General questions/answers about local currency changeover in ECC", "RefUrl": "/notes/824995 "}, {"RefNumber": "1039752", "RefComponent": "FI-GL-MIG", "RefTitle": "New G/L migration: Restrictions, important information", "RefUrl": "/notes/1039752 "}, {"RefNumber": "1171402", "RefComponent": "IS-M-SD-PS-BL", "RefTitle": "Compatibility IS-M/SD with General Ledger Accounting (new)", "RefUrl": "/notes/1171402 "}, {"RefNumber": "1014369", "RefComponent": "FI-GL-MIG", "RefTitle": "NewGL migration: Availability of Development Support", "RefUrl": "/notes/1014369 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}