{"Request": {"Number": "1237763", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 286, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000007233872017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001237763?language=E&token=EA4AD0997A6309246164A67ED7C193B7"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001237763", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001237763/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1237763"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 13}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "25.08.2008"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PART-ISHMED-ORD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Service Management i.s.h.med"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Partner solutions", "value": "XX-PART", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Clinical System i.s.h.med", "value": "XX-PART-ISHMED", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART-ISHMED*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Management i.s.h.med", "value": "XX-PART-ISHMED-ORD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART-ISHMED-ORD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1237763 - Clinical Order: Incorrect Connection Radiological Documents"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>During case revision and when using the collective order the system may create an incorrect connection between the order item of the clinical order and the corresponding radiological documents.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Clinical Order, Radiology</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. Import the corresponding attachment for the release into your system.</OL> <OL>2. If you execute the source code corrections on a system with basis release SAP Web AS 6.20 Support Package 54 or lower or on a system with basis release SAP NetWeaver 2004 (SAP Web AS 6.40) Support Package 12 or lower, you must first create the method GET_ANFID_FOR_OBJECT in the class CL_ISHMED_SHIFT_OBJECTS:</OL> <UL><LI>Call transaction SE24.</LI></UL> <UL><LI>In the #Object Type# input field enter the name of class CL_ISHMED_SHIFT_OBJECTS and choose #Change#.</LI></UL> <UL><LI>Select the \"Methods\" tab page and add a new row (\"Add Method\" function).</LI></UL> <UL><LI>In the new row enter the following values:</LI></UL> <UL><UL><LI>Method: GET_ANFID_FOR_OBJECT</LI></UL></UL> <UL><UL><LI>Visibility: Public</LI></UL></UL> <UL><UL><LI>Type: Static Method</LI></UL></UL> <UL><UL><LI>Description: Determines the ANFID for the NDOC</LI></UL></UL> <UL><LI>Position the cursor on the method and choose \"Parameters\".</LI></UL> <UL><LI>Create the parameter IT_SHIFT_OBJECT:</LI></UL> <UL><UL><LI>Parameter: IT_SHIFT_OBJECT</LI></UL></UL> <UL><UL><LI>Type: Importing</LI></UL></UL> <UL><UL><LI>Typing Method: Type</LI></UL></UL> <UL><UL><LI>Associated Type: ISH_SHIFT_OBJECT</LI></UL></UL> <UL><LI>Create the parameter E_ANFID:</LI></UL> <UL><UL><LI>Parameter: E_ANFID</LI></UL></UL> <UL><UL><LI>Type: Exporting</LI></UL></UL> <UL><UL><LI>Typing Method: Type</LI></UL></UL> <UL><UL><LI>Associated Type: ANFID</LI></UL></UL> <UL><LI>Save and activate the changes.</LI></UL> <OL>3. Implement the source code corrections.</OL> <p><br />To correct any incorrect connections which may result execute the report RN1_CORRECT_N1VKG_REQCOMPID.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-PART-ISHMED (Clinical System i.s.h.med)"}, {"Key": "Responsible                                                                                         ", "Value": "C5001669"}, {"Key": "Processor                                                                                           ", "Value": "C5001995"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001237763/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001237763/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001237763/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001237763/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001237763/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001237763/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001237763/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001237763/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001237763/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "HW1237763_600.zip", "FileSize": "5", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000350382008&iv_version=0013&iv_guid=A6A2AC275C64C049B890CA4223F863DB"}, {"FileName": "HW1237763_603.zip", "FileSize": "5", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000350382008&iv_version=0013&iv_guid=85CC74BB84512F4DA805CA887745A9D0"}, {"FileName": "HW1237763_472.zip", "FileSize": "3", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000350382008&iv_version=0013&iv_guid=DEE3B14979C3324BA076E7716DD4BA2A"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-H", "From": "472", "To": "472", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "603", "To": "603", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "IS-H 472", "SupportPackage": "SAPKIPHF29", "URL": "/supportpackage/SAPKIPHF29"}, {"SoftwareComponentVersion": "IS-H 472", "SupportPackage": "SAPKIPHF30", "URL": "/supportpackage/SAPKIPHF30"}, {"SoftwareComponentVersion": "IS-H 600", "SupportPackage": "SAPK-60015INISH", "URL": "/supportpackage/SAPK-60015INISH"}, {"SoftwareComponentVersion": "IS-H 602", "SupportPackage": "SAPK-60204INISH", "URL": "/supportpackage/SAPK-60204INISH"}, {"SoftwareComponentVersion": "IS-H 603", "SupportPackage": "SAPK-60303INISH", "URL": "/supportpackage/SAPK-60303INISH"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 9, "URL": "/corrins/0001237763/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 9, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 3, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "935845 ", "URL": "/notes/935845 ", "Title": "Clin. Patient Management: Reassigning an Order Item", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1037463 ", "URL": "/notes/1037463 ", "Title": "Clinical Patient Mgmt: Case Revision: Reassign Replaced Serv", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1093147 ", "URL": "/notes/1093147 ", "Title": "Clinical Patient Management: Change Movement - Medical Recor", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "935845 ", "URL": "/notes/935845 ", "Title": "Clin. Patient Management: Reassigning an Order Item", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1093147 ", "URL": "/notes/1093147 ", "Title": "Clinical Patient Management: Change Movement - Medical Recor", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1093147 ", "URL": "/notes/1093147 ", "Title": "Clinical Patient Management: Change Movement - Medical Recor", "Component": "XX-PART-ISHMED"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}