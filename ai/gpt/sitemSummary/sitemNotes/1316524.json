{"Request": {"Number": "1316524", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 594, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016751202017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001316524?language=E&token=5962F89D76A9007B189DA168DA6807A0"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001316524", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001316524/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1316524"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 26}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.08.2018"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-TWB-BCA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Process Change Analyzer"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Test Management", "value": "SV-SMG-TWB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-TWB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Process Change Analyzer", "value": "SV-SMG-TWB-BCA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-TWB-BCA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1316524 - FAQ: BPCA - Business Process Change Analyzer"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>FAQ, SAP Solution Manager 7.1 and 7.2, Information on SAP Solution Manager Tools and Functionalities, here: BPCA</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>BPCA, Business Process Change Analyzer, Change Impact Analysis, TBOM, Identification of Business Processes affected by change, Regression Test, Risk-based Test Recommendation and Test Plan Generation</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><br />[1] Question: What is the Business Process Change Analyzer?<br />[2] Question: What is a TBOM and how do I create it?<br />[3] Question: Are there any requirements to the managed system, where a TBOM for a transaction should be recorded?<br />[4] Question: There are&#160;3 TBOM Creation Modes: Dynamic, Semi-dynamic&#160;and Static. Which Mode does SAP recommend?<br />[5] Which types of executables are supported by Business Process Change Analyzer?<br />[6] Does Business Process Change Analyzer also support non-ABAP applications?<br />[7] Question: What is the result of a Change Impact Analysis and how do I get a recommendation for regression tests using BPCA?<br />[8] Question: Can I ignore certain objects contained in a TBOM?<br />[9] Question: Is it possible to analyze&#160;the Impact of a Support Package&#160;without implementing it?<br />[10] Question: Where do I find further detailed information on BPCA?<br /><br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;[1] Question: What is the Business Process Change Analyzer?</p>\r\n<p>Answer:<br />The Business Process Change Analyzer identifies business processes or business process steps that are affected by planned change events. It is available in the Work Center \"Test Management\" of SAP Solution Manager.<br /><br />Relevant change events supported by BPCA:</p>\r\n<ul>\r\n<li>Customizing and ABAP Coding managed via Transport Management System or Change Request Management</li>\r\n</ul>\r\n<ul>\r\n<li>Support Packages and Support Package Stacks</li>\r\n</ul>\r\n<ul>\r\n<li>SAP Enhancement Packages / Add-ons</li>\r\n</ul>\r\n<ul>\r\n<li>SAP Business Functions</li>\r\n</ul>\r\n<ul>\r\n<li>Planned change of specific objects (Object List)</li>\r\n</ul>\r\n<p>Based on the ABAP objects that are part of the planned change event the BPCA can analyze each documented business process / business process step that has an assigned TBOM (Technical Bill of Materials). The TBOM is a list of ABAP objects that is used during execution of a certain transaction in a certain scenario, business process or process step.<br />Whenever a change contains one or more objects that are part of a TBOM the related Business Process Node is marked as affected.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;[2] Question: What is a TBOM and how do I create it?</p>\r\n<p>Answer:<br />TBOM is the acronym for Technical Bill of Materials. A TBOM is a list of ABAP objects assigned to a transaction/executable as part of your business process documentation.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;[3] Question: Are there any requirements to the managed system, where a TBOM for a transaction should be recorded?</p>\r\n<p>Answer:<br />Yes, in the managed systems release 2008_1_xx or a higher release of software component ST-PI must be installed. Generally SAP recommends to use the newest Support Package for software component ST-PI 2008_1_xx.<br />Starting with ST-PI 2008_1_xx SP 05 it is possible to explicitly start and stop the TBOM recording.&#160;This enables a user to record one TBOM for a complete process (including multiple transactions).<br />All remote/managed systems also require for software component SAP_BASIS a version 46C or higher&#160;&#160;for dynamic TBOM recording. For static TBOM recording, there are no further requirements.<br /><br />Use cases that require further prerequisites:<br />1. Business Function Analysis:<br />On the SAP Solution Manager system you need for software component ST 700 at least SP 23. On the managed system you need in addition to the above mentioned ST-PI prerequisites at least one of the following releases:<br />a. NetWeaver 700 EhP 1 SPS06<br />b. NetWeaver 700 EhP2 SPS02<br />c. ERP 6.0 EhP4 SPS06<br />d. CRM 7.0 EhP1<br /><br />2. TBOM recording via SAP TAO:<br />On the SAP Solution Manager system you need for software component ST 700 at least SP 25. On the remote system you need for software component ST-PI release 2008_1_xx at least SP 04.&#160;&#160;In addition you still require at least one of the following releases for software component SAP_BASIS and SAP KERNEL:<br />a. SAP_BASIS 700, SAP KERNEL 700 Patch Level 264<br />b. SAP_BASIS 701, SAP KERNEL 701 Patch Level 116<br />c. SAP_BASIS 702, SAP KERNEL 720 Patch Level 0<br /><br />Please consult SAP Note 1595424 for BPCA requirements in SAP Solution Manager 7.1.<br />Please consult SAP Note 1684704 for BPCA requirements for TBOM recording of batch jobs that are scheduled during the TBOM recording of an application (this function is available as of SAP Solution Manager 7.1 SP05).<br /><br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;[4] Question: There are&#160;3 Modes for TBOM creation: Dynamic, Semi-dynamic and Static. Which Mode does SAP recommend?</p>\r\n<p>Answer:<br />To get the most precise results, we recommend to use dynamic TBOMs, nevertheless using&#160;semi-dynamic TBOMs is an efficient way to start with BPCA. Static TBOMs are not recommended for real use.<br /><br />Below you find the comparison of the 3 modes to outline this recommendation:<br /><br />1. Dynamic TBOM<br />A dynamic TBOM is created with a trace for the specified user (explicit or from RFC destination) in background when executing a specific business transaction in the managed system and includes exactly those objects actually used during that execution. Thus it is not sufficient to just call the transaction code and exit, but the transaction has to be executed in the same way it is described in the business process context (e.g. in the related test case description).<br /><br />With SAP Solution Manager 7.1 SAP provides additional functionality to reduce the effort for creating dynamic TBOMs:</p>\r\n<ul>\r\n<li>TBOM work items provide an easy way for a business user to record the TBOMs during normal work in background.</li>\r\n</ul>\r\n<ul>\r\n<li>When performing manual tests using the Test Workbench in SAP Solution Manager TBOMs can be recorded in background.</li>\r\n</ul>\r\n<ul>\r\n<li>Automated recording of TBOMs while executing automated tests either with Test Automation Framework in SAP Solution Manager or with SAP TAO.</li>\r\n</ul>\r\n<p>To include additional objects to an existing TBOM you can start the recording again while creating so-called TBOM-enhancements.<br /><br />Note: When you record a dynamic TBOM, all actions are performed by that user, are recorded in the managed system. Ensure that this user does not perform any actions during the TBOM recording, which do not belong to the recorded executable entity.</p>\r\n<p>2.&#160;Semi-Dynamic TBOM<br />A&#160;semi-dynamic TBOM is created while analyzing the ABAP call hierarchy including called objects as well as using Usage&#160;Data (UPL or SCMON) to filter the objects that have been used in the past.&#160;The advantage of a semi-dynamic TBOM is that it can be created and updated&#160;&#160;automatically in background without involvement of any business process expert.&#160;It does not include dynamic calls to objects&#160;that are only known during run time.&#160;Up to 999 levels in the call hierarchy&#160;are supported here. In consequence the BPCA results can be treated as an indicator on area to focus the test activities. For prerequisites regarding UPL (Usage Procedure Logging or SCMON) please check SAP Notes 1955847 and 2442065.<br /><br />3. Static TBOM<br />A static TBOM is created while analyzing the ABAP call hierarchy including called objects. This is limited to 4 levels due to performance reason. The advantage of a static TBOM is that it can be created and updated&#160;&#160;automatically in background without involvement of any business process expert. But the disadvantage is that it contains many objects that are not used when executing a transaction in the context of a specific business process and it does not include dynamic calls to objects (enhancements, user exits) that are only known during run time. It also does not contain objects beyond Level 4 that might be called in the context of a specific business process. In consequence the BPCA results will not be precise.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;[5] Question: Which types of executables are supported by Business Process Change Analyzer?</p>\r\n<p>Answer: Business Process Change Analyzer supports the following executable types:</p>\r\n<ul>\r\n<li>Solution Manager 7.0</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Transactions</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Solution Manager 7.1</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Transactions</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Programs</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>BSP Applications</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>ABAP Web Dynpro Applications</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP CRM People-Centric UI Application</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>CRM Web Client Applications</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Applications that can be started via a URL where the back-end server is an ABAP system (via type URL SAP application; only dynamic TBOMs).<br /><br /></li>\r\n</ul>\r\n<li>Solution Manager 7.2</li>\r\n<ul>\r\n<li>Transactions</li>\r\n<li>Programs</li>\r\n<li>BSP Applications</li>\r\n<li>ABAP Web Dynpro Applications&#160;</li>\r\n<li>ABAP Web Dynpro Application Configurations&#160;</li>\r\n<li>SAP CRM People-Centric UI Application</li>\r\n<li>CRM Web Client Applications</li>\r\n<li>Applications that can be started via a URL where the back-end server is an ABAP system (via type URL; only dynamic TBOMs).</li>\r\n<li>Fiori Application (From SP05; only dynamic TBOMs supported)</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;[6] Question: Does Business Process Change Analyzer also support non-ABAP applications?</p>\r\n<p>Answer: No currently Business Process Change Analyzer supports only ABAP applications.</p>\r\n<p><br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;[7] Question: What is the result of a Change Impact Analysis and how do I get a recommendation for regression tests using BPCA?</p>\r\n<p>Answer:<br />The result of the analysis is a list of transactions in the context of the related business process structure nodes.<br />Test Cases assigned to affected business process nodes can be automatically selected to be part of a Test Plan.<br />This Test Plan then reflects the scope for Regression Test of the planned change event. Since SAP Solution Manger 7.1 a new functionality 'Test Scope Optimization' provides the functionality to optimize the Test Scope in a way that each changed object just needs to be tested once. As there might be the chance that different processes using the same object but different lines of code of this object are actually used, SAP recommends to include all business critical processes that are impacted by a change in any case.<br />Example: You have classified your business processes in four categories (Criticality: Very High / High / Medium / Low) via attributes. During Test Scope Optimization, you can add processes with criticality 'Very High' and 'High' into the 'Base Scope' to make sure all affected processes identified by BPCA and classified as critical will be completely tested. For non-critical processes the test scope is optimized.<br /><br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;[8] Question: Can I ignore certain objects contained in a TBOM?</p>\r\n<p>Answer:<br />You can use the TBOM filter to set whether the Business Process Change Analyzer is supposed to ignore certain objects contained in the TBOM. These objects are not taken into account in the analysis then. Even if these objects are affected by a change, they are not listed as a result.<br /><br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;[9] Is it possible to analyze&#160;the Impact of a Support Package&#160;without implementing it?</p>\r\n<p>Answer:<br />Yes, it is possible to perform an Impact Analysis for a Support Package without implementing it by performing a test import of the SP or using Scope and Effort Analyzer<br />For Test Import proceed in the following way to achieve this:<br />1. Download the Support Packages as usual.<br />2. Open the Support Package Manager (transaction SPAM).<br />3. Add the SPs to the import queue.<br />4. In the menu select 'Extras -&gt; Settings'<br />5. Open the 'Import Queue' tab.<br />6. In the area 'Scenario' mark 'Test'.<br />7. Start the import process.<br /><br />This procedure will make the object list of the SPs available in the system where SPAM was executed. BPCA can use this object list for an Impact Analysis.<br />With 'Scope and Effort Analyzer' that was shipped with SAP Solution Manager 7.1 SP 11 you can&#160;analyze SAP Support Packages or Enhancement Packages&#160;even without test import.</p>\r\n<p>For some updates like SAP ERP Enhancement Packages it is not possible to execute a test import in transaction SPAM. These updates have to be executed via Software Update Manager (SUM). However it is still to use BPCA also for these updates without executing the actual update. Proceed as follows:</p>\r\n<ul>\r\n<li>Start the Update with SUM and only proceed to the Prepare phase. After this phase the piece lists of all to be imported update packages are available in the system.</li>\r\n<li>A reset of the Prepare is possible if the Execution of the update has not started yet.</li>\r\n<li>Please see the documentation of Software Update Manager (SUM) for further details.</li>\r\n</ul>\r\n<p><br /><br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;[10] Question: Where do I find further detailed information on BPCA?</p>\r\n<p><br />Answer:<br />Test Management Wiki in SCN:<br /><a target=\"_blank\" href=\"http://wiki.scn.sap.com/wiki/display/SM/SAP+Solution+Manager+WIKI+-+Test+Management\">http://wiki.scn.sap.com/wiki/display/SM/SAP+Solution+Manager+WIKI+-+Test+Management</a>&#160; &gt; Business Process Change Analyzer (BPCA)</p>\r\n<p>SAP Solution Manager 7.1:<br />- RKT Learning Map \"SAP Solution Manager 7.1 SP12: Test Management\":<br /><a target=\"_blank\" href=\"https://websmp105.sap-ag.de/&#126;sapidb/011000358700000326762014E/R_SOLMAN71SP12TESTM.htm\">http://service.sap.com/&#126;sapidb/011000358700000326762014E/R_SOLMAN71SP12TESTM.htm</a><br /><br />- Application Help &gt; SAP Solution manager 7.1 SP12 &gt; Test Management:<br /><a target=\"_blank\" href=\"http://help.sap.com/saphelp_sm71_sp12/helpdata/EN/2e/e7769b243b4d0993429c71996f985e/frameset.htm\">http://help.sap.com/saphelp_sm71_sp12/helpdata/EN/2e/e7769b243b4d0993429c71996f985e/frameset.htm</a><br /><br />- SAP Support Portal&#160;&gt; ALM Process \"Test Management\" 7.1:<br /><a target=\"_blank\" href=\"https://support.sap.com/support-programs-services/solution-manager/processes.html\">https://support.sap.com/support-programs-services/solution-manager/processes.html</a>&#160;&gt; Test Management<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-SMG-TWB-PLN (Test Plan Management)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D019894)"}, {"Key": "Processor                                                                                           ", "Value": "Abdelhak NEZZARI (I023373)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001316524/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001316524/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001316524/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001316524/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001316524/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001316524/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001316524/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001316524/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001316524/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "BPCA71_Overview.pdf", "FileSize": "1744", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000016852009&iv_version=0026&iv_guid=E2FE04CFB6F4A9478DD9DE294F58D000"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "401389", "RefComponent": "BC-DWB-UTL-INR", "RefTitle": "Where-used list in the Workbench as of Release 6.10", "RefUrl": "/notes/401389"}, {"RefNumber": "1875064", "RefComponent": "SV-SMG-TWB-BCA", "RefTitle": "BPCA: Known supported and unsupported Object Types", "RefUrl": "/notes/1875064"}, {"RefNumber": "1684704", "RefComponent": "SV-SMG-TWB-BCA", "RefTitle": "BPCA: Prerequisites for TBOMs of background jobs", "RefUrl": "/notes/1684704"}, {"RefNumber": "1595424", "RefComponent": "SV-SMG-TWB-BCA", "RefTitle": "SOLMAN 7.1 BPCA: Prerequisites for TBOM recording", "RefUrl": "/notes/1595424"}, {"RefNumber": "1291102", "RefComponent": "SV-SMG-TWB-BCA", "RefTitle": "TBOM: ST-PI \"E2E Testing Agent\" does not exist", "RefUrl": "/notes/1291102"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1875064", "RefComponent": "SV-SMG-TWB-BCA", "RefTitle": "BPCA: Known supported and unsupported Object Types", "RefUrl": "/notes/1875064 "}, {"RefNumber": "1595424", "RefComponent": "SV-SMG-TWB-BCA", "RefTitle": "SOLMAN 7.1 BPCA: Prerequisites for TBOM recording", "RefUrl": "/notes/1595424 "}, {"RefNumber": "1684704", "RefComponent": "SV-SMG-TWB-BCA", "RefTitle": "BPCA: Prerequisites for TBOMs of background jobs", "RefUrl": "/notes/1684704 "}, {"RefNumber": "1291102", "RefComponent": "SV-SMG-TWB-BCA", "RefTitle": "TBOM: ST-PI \"E2E Testing Agent\" does not exist", "RefUrl": "/notes/1291102 "}, {"RefNumber": "401389", "RefComponent": "BC-DWB-UTL-INR", "RefTitle": "Where-used list in the Workbench as of Release 6.10", "RefUrl": "/notes/401389 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}