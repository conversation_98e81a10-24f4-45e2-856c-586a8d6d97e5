{"Request": {"Number": "113711", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 374, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014588582017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000113711?language=E&token=AD8B586150C663FBD2838CAA232F749D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000113711", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000113711/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "113711"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Workaround of missing functionality"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.06.2003"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-MX"}, "SAPComponentKeyText": {"_label": "Component", "value": "Mexico"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Mexico", "value": "XX-CSC-MX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-MX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "113711 - FA ISR Depreciation Adjust. & IMPAC Balance Average"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The ISR Fiscal Depreciation Calculation (adjustment) and the IMPAC<br />Balance Average in FIXED ASSETS, are required in Mexico for the annual<br />declaration.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>ISR, IMPAC Mexico</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Functionallity required for Mexico</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Take care this note contains a local solution for Mexico.<br />This program for the ISR and IMPAC calculation is only a base program.<br /><br />Because the law is not exact in this case, each customer, can interpret<br />the calculation in different ways. Big differences can be found<br />from one way of calculation to another:<br />- Different quantity of months of use<br />- Different treatment for assets acquired after June<br />- Different determination of the Revaluation Factor<br /><br />This program should be then considered as a base program but if any<br />change is requiered because of the \"interpretacion de la ley\" in the<br />customer, the changes have to be done by the customer itself in the<br />ZXANLU04 include code (please refer to OSS note 616225)<br /><br />The program actually calculates the ISR Depreciation and IMPAC as:<br /><br />1. Useful period (from Start Date to End Date) for ISR and IMPAC:<br />a Start Date:<br />&#x00A0;&#x00A0;- Depreciation start date for new assets<br />&#x00A0;&#x00A0;- January of the calculation year for old assets<br />b. End Date:<br />&#x00A0;&#x00A0; - Reval.Measure Date (= Calculation Date) for assets with Deprn.<br />&#x00A0;&#x00A0;- End of useful life month for asset that ended their useful life<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;before the date of the calculation.<br /><br />If the End Date is January, the index month selected is January.<br /><br />2.IMPAC:<br />a. Calculation Index if calculation date &gt; June: ALWAYS index of JUNE<br /><br />b. For assets capitalized in the current year after June, only the<br />&#x00A0;&#x00A0; calculation of the \"Saldo promedio\" without revaluation is done.<br /><br />If calculation of IMPAC done after June, the Index selected is always<br />June.<br /><br />The program does not support retired assets.<br />The program only supports STR.LINE Deprn. Method<br /><br /><br />*************************************************************<br />*************&#x00A0;&#x00A0;NOTE IMPLEMENTATION&#x00A0;&#x00A0; ***********************<br />*************************************************************<br /><br />After release upgrade the import has to be repeated for the new<br />release.<br />Please take the transport from SAPSERVx, path:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; ~ftp/specific/latinamerica/MX/note.0113711<br />Directory note.0113711 contains the following transports files:<br /><br />- For Rel. 3.0F and 3.1 please transport:<br />&#x00A0;&#x00A0;R900246.LD3 (data)<br />&#x00A0;&#x00A0;D900246.LD3 (cofile)<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;which will create the next objects:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;DEVC ZAA&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Development class<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;DOMA ZINDE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Domain for INPC value<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;DTEL ZINDE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Data element for INPC value<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;FUGR ZINP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Function group for table maintenance<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;PROG ZAAUFW01&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MAIN PROGRAM<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;PROG ZXANLU04&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Include with calculation logic<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;TABL ZINPC&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Table for INPC values<br /><br />If you want to transport the INPCs values into the ZINPC table, please<br />AFTER having transported the previous files, transport files:<br />&#x00A0;&#x00A0;R900248.LD3<br />&#x00A0;&#x00A0;K900248.LD3<br />which contains the INPCs values since 1950.<br /><br />- For Rel. 4.0B and after, please transport:<br />&#x00A0;&#x00A0;R900040.LD1<br />&#x00A0;&#x00A0;K900040.LD1<br /><br />In rel. 4.0B and after, the program uses the INMA indexes tables. There<br />is no need to create a table ZINPC anymore to maintain the INPCs<br />values.<br />The Index that the program uses is the general one defined in the<br />Inflation Method for the company code for FI. It uses the definive<br />values of it.<br /><br />******<br />Additional to the transport, the following tasks have to be done:<br /><br /><br />1.Copy the EXIT_RAAUFW01_001 function module into the EXIT_ZAAUFW01_002<br /><br />&#x00A0;&#x00A0;Edit the EXIT_ZAAUFW01_002 function.<br />&#x00A0;&#x00A0;In the Import/Export Parameter, add:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;I_AUFDAT as an import parameter with T089-AUFDAT as reference field<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;E_AUFNL as an export parameter with ANLC_AUFNL as reference field<br />&#x00A0;&#x00A0;In the Table Parameters/Exceptions, add:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;NO_INPC as an exception<br />&#x00A0;&#x00A0;In the source code is where the ZXANLU04 include appears. This is<br />&#x00A0;&#x00A0;where the calculation is done. The ZXANLU04 can be created for the<br />&#x00A0;&#x00A0;system with another name, if so, please create it with double click<br />&#x00A0;&#x00A0;and insert logic from ZXANLU04.<br /><br />2.Go to the SMOD transaction (System Modification).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Select the ARVL0001 enhancement, and add in the components in the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;function exits, the EXIT_ZAAUFW01_002 function module. Use here the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;development class AA.<br /><br />3.Go to the CMOD transaction (Customer Modification).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Create the ZAAREVAL project, enter a desciption and add in the SAP<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;enhancements the ARVL0001 extention.<br /><br />4. Create two depreciation areas:<br />&#x00A0;&#x00A0;- One area will keep the depreciation revaluation I.S.R. (i.e 16).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;The depreciation key has to use the acquisition value as base<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;value.<br />&#x00A0;&#x00A0;- The other area will keep the average value IMPAC (i.e 18).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;The depreciation key should be 0000.<br /><br />5.Create two revaluation measures (transaction OAA2):<br />&#x00A0;&#x00A0;- Revaluation Measure 90 (it HAS TO BE 90!) for Depreciation Adjust.:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*Revalaution area should be the defined for the Deprn Adjust. ISR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*Base area should be the Historical Fiscal Data area<br />&#x00A0;&#x00A0;- Revaluation Measure 91 (it HAS TO BE 91!) for Balance Average:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*Revalution area should be the defined for Balance Average IMPAC<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*Base area should be the Historical Fiscal Data area<br />&#x00A0;&#x00A0;Be aware that in both revaluation measures, the date has to be the<br />&#x00A0;&#x00A0;SAME. The date of the revaluation measure is the base date for the<br />&#x00A0;&#x00A0;calculation, NOT the dates in the ZAAUFW01 program!!<br /><br /><br />6.The transaction types that have to be defined are (transaction AO84):<br />&#x00A0;&#x00A0;- 816 (it HAS TO BE 816) copy from tty 800 and limited to the Deprn<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Adjustment area for Display Selection &amp; Always Post (Tran OAXJ).<br />&#x00A0;&#x00A0;- 818 (it HAS TO BE 818) copy from tty 800 and limited to the Balance<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Average Area for Display Selection &amp; Always Post (Tran OAXJ).<br /><br /><br />EXECUTION OF THE CALCULATIONS:<br /><br />The Main Program is the ZAAUFW01. This is the one which has to be<br />executed to do the calculation of the adjustments. It has to be run<br />first for the ISR depreciation Adjustment Calculation (RV Meas. 90) and<br />the second run will determine the IMPAC Balance Average (RV Meas.91)<br />because the IMPAC calculation needs a value of the ISR Deprn. Adj.<br />In both calculations, a Batch Input is created; the results once the<br />batchs are runned, are inserted as a planned valueS in each adjustment<br />area defined.<br />For the I.S.R, the values are inserted in the Revaluation and in the<br />Reval. ord. depr. fields. The revaluation value is the net adjustment<br />amount. For the Reval. ord. depr., the amount displayed is the adjusted<br />depreciation (not only the net adjustment).<br />For the IMPAC, the value of the average is inserted in the Revaluation<br />field.<br /><br />The program works for LINEAL depreciations.<br /><br />NOTE: To get the last version of the include, please refer to note<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 616225<br /><br />For more information about transports please refer to the related note<br />(13719).<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I004624)"}, {"Key": "Processor                                                                                           ", "Value": "I010826"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000113711/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000113711/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000113711/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000113711/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000113711/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000113711/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000113711/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000113711/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000113711/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "388371", "RefComponent": "XX-CSC-MX", "RefTitle": "8Z876: No time base defined, in ISR/IMPAC batch", "RefUrl": "/notes/388371"}, {"RefNumber": "193600", "RefComponent": "XX-CSC-MX", "RefTitle": "Calculate IMPAC always with JUNE Index of curr.year", "RefUrl": "/notes/193600"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "193600", "RefComponent": "XX-CSC-MX", "RefTitle": "Calculate IMPAC always with JUNE Index of curr.year", "RefUrl": "/notes/193600 "}, {"RefNumber": "388371", "RefComponent": "XX-CSC-MX", "RefTitle": "8Z876: No time base defined, in ISR/IMPAC batch", "RefUrl": "/notes/388371 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30C", "To": "31H", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46D", "To": "46D", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}