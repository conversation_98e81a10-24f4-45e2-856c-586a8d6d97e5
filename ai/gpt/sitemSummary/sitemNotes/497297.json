{"Request": {"Number": "497297", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 268, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000002325252017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000497297?language=E&token=F39745D31F056B90633B67C0D917E182"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000497297", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000497297/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "497297"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 11}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.02.2004"}, "SAPComponentKey": {"_label": "Component", "value": "FI-AA-AA-C"}, "SAPComponentKeyText": {"_label": "Component", "value": "Transactions"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Asset Accounting", "value": "FI-AA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "FI-AA-AA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AA-AA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Transactions", "value": "FI-AA-AA-C", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AA-AA-C*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "497297 - MIRO: Error for posting with fixed asset integration"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>When you post an invoice receipt using Transaction MIRO, problems occur in Asset Accounting:</p> <OL>1. AA629 \"Balance for transaction type group 10 negative for the area &amp;\"</OL> <OL>2. For credit memo postings, the system uses the wrong transaction type (transaction type 100 instead of 160, or 100 instead of 105).</OL> <OL>3. For an incoming invoice offset against affiliated companies, the system uses the wrong transaction type (100 instead of 150).</OL> <p></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>MIRO; default asset value date; AA628, AA629, AAPO161; TABWD, TABWD_VID; TABWD_VIT.<br />Overview of the table TABWD (standard Customizing):<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RMRPRMRP000000000000 159 <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RMRPRMRP000000100000 105 <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RMRPRMRP000000110000 160 <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RMRPRMRP100000000000 153 <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RMRPRMRP100000100000 106 <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RMRPRMRP100000110000 161 <p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This has not been programmed.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><b>Caution: This note should not be implemented manually. SAP recommends that you import the appropriate Support Package! The implementation of this note requires knowledge of the ABAP development environment!</b><br /> <OL>1. Make the following changes to screen SAPLMR1M 6410:</OL> <OL><OL>a) Transaction SE80:Display function group MR1M and select screen 6410. Start the layout editor in the change mode.</OL></OL> <OL><OL>b) Move the 'Reference date' field (DRSEG_CO-DABRZ) as follows:</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Position the cursor on column field DRSEG_CO-DABRZ. Press the left mouse button and hold it down. Now drag the column to the right to its new location, beside the contract number column. <OL><OL>c) Generate a new column with the field 'Reference date for asset' (DRSEG_CO-BZDAT) as follows:</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Again scroll to the left to the fields 'Asset' and 'Subnumber'. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Generate an input/output field by choosing 'Input/output field' in the left Screen Painter area and positioning the cursor on the 'Subnumber' input field (DRSEG_CO-ANLN2).This causes the system to generate a new column to the right of this field. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Double-click on the generated column into an input/output field and enter the following data: <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Name&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;: DRSEG_CO-BZDAT <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Def.Length: 10 <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Vis.Length: 10 <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Groups&#x00A0;&#x00A0;&#x00A0;&#x00A0;: 016 (in the 3rd input field) <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Format&#x00A0;&#x00A0;&#x00A0;&#x00A0;: DATS <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Close the 'Screen Painter Attribute window'. <OL><OL>d) Generate a text field by choosing 'Text field' in the left Screen Painter area and positioning the cursor on the header line of the generated column (DRSEG_CO-BZDAT).</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Double-click on the generated header and enter the following data: <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Name : DRSEG_CO-BZDAT <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Text: Reference date for asset <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Close the 'Screen Painter Attribute window'. <OL><OL>e) Check and activate screen 6410.</OL></OL> <p></p> <OL>2. Implement the source code from this note.</OL> <OL>3. Start report ZATABWD in all affected systems (Customizing, test and production systems).This causes the required entries in system and Customizing tables to be generated.At any time later, you can then change your Customizing as usual in the Customizing system and then transport it into all subsequent systems (Asset Accounting IMG: Transactions -&gt; Specify Default Transaction Types).If postings with Transaction MIRO later trigger error message AAPO161 \"Accounting transaction category &amp;1 is not defined\", then the required table entries are missing in the system.Then restart the report in this system.</OL> <OL>4. Create the new transaction type 159 by copying transaction type 156. Assign the current-year acquisitions transaction type 100 to this new transaction type 159. If this transaction type 156 does not exist in your system, refer to Note 327088 for more details on this transaction type.</OL> <OL>5. Assign transaction type 159 to the \"Acquisition from invoice receipt w/o affil. comp.\" transaction (Asset Accounting IMG:Transactions -&gt; Specify Default Transaction Types).</OL> <OL>6. Assign transaction type 153 to the \"Acquisition from invoice receipt w/ affil. comp.\" transaction (Asset Accounting IMG:Transactions -&gt; Specify Default Transaction Types).</OL> <p><br />Once you have correctly implemented the corrections, the following business transactions can be posted (correctly).The descriptions refer to the transactions with the default transaction type:</p> <OL>1. A non-valuated goods receipt is made for the purchase order. The posting of the incoming invoice is made with transaction type 100. You can manually enter the reference date of the transaction in Transaction MIRO. If you do not enter a reference date, the system applies the calculation formulas for the reference date determination.</OL> <OL>2. An invoice in the current fiscal year is reversed (MR8M).This is no reversal in the sense of Asset Accounting.This is why the posting is made with the credit memo transaction type 105.</OL> <OL>3. A credit memo is posted for an incoming invoice.In this case, too, asset transaction type 105 is used for the posting.</OL> <OL>4. A valuated goods receipt is made for the purchase order.The posting of the incoming invoice with a different amount than the goods receipt is made with the appropriate +/- sign with transaction type 100 (large invoiced amount) or 105 (small invoiced amount).The asset value date of the transaction cannot be entered manually in Transaction MIRO here.It has been filled with the reference date of the goods receipt.</OL> <OL>5. An invoice is posted in the current fiscal year for a valuated goods receipt from the previous year.The reference date is determined according to the derivation rules for the reference date determination.The posting of the incoming invoice with a different amount than the goods receipt is made with the appropriate +/- sign with transaction type 159 (large invoiced amount) or 160 (small invoiced amount).</OL> <OL>6. Transaction types 160 or 159 are used for the reversal of the above transactions.</OL> <OL>7. If the incoming invoice is offset against affiliated companies, the corresponding transaction types for offsettings against affiliated companies are used (153 instead of 159, 106 instead of 105 and 161 instead of 160).</OL> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "MM-IV-INT-AA (Asset Accounting)"}, {"Key": "Transaction codes", "Value": "CLEAR"}, {"Key": "Transaction codes", "Value": "MOVE"}, {"Key": "Transaction codes", "Value": "HIER"}, {"Key": "Transaction codes", "Value": "SE80"}, {"Key": "Transaction codes", "Value": "0000"}, {"Key": "Transaction codes", "Value": "MR1M"}, {"Key": "Transaction codes", "Value": "MR01"}, {"Key": "Transaction codes", "Value": "MR8M"}, {"Key": "Transaction codes", "Value": "AB01"}, {"Key": "Transaction codes", "Value": "ABNA"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D029438)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D033895)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000497297/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000497297/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000497297/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000497297/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000497297/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000497297/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000497297/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000497297/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000497297/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "699158", "RefComponent": "FI-AA-AA-C", "RefTitle": "Error AA999 with posting to asset via Materials Management", "RefUrl": "/notes/699158"}, {"RefNumber": "615865", "RefComponent": "FI-AA-AA-C", "RefTitle": "Vendor not copied to fixed asset in MIRO", "RefUrl": "/notes/615865"}, {"RefNumber": "575206", "RefComponent": "MM-IV-LIV", "RefTitle": "MIRO/MR11: Goods receipt date not filled", "RefUrl": "/notes/575206"}, {"RefNumber": "547233", "RefComponent": "FI-AA-AA-C", "RefTitle": "AA629 w/ reversal of valuated goods receipt in subsequent yr", "RefUrl": "/notes/547233"}, {"RefNumber": "546228", "RefComponent": "FI-AA-AA-C", "RefTitle": "FAQ - MM posting with fixed asset integration", "RefUrl": "/notes/546228"}, {"RefNumber": "541200", "RefComponent": "FI-AA-AA-C", "RefTitle": "Error AA629 although values in fixed asset are ok", "RefUrl": "/notes/541200"}, {"RefNumber": "525536", "RefComponent": "FI-AA-AA-C", "RefTitle": "AA629 with Transaction MIRO", "RefUrl": "/notes/525536"}, {"RefNumber": "525451", "RefComponent": "FI-AA-AA-C", "RefTitle": "MR01: no line items according to HP31", "RefUrl": "/notes/525451"}, {"RefNumber": "524877", "RefComponent": "MM-IV-LIV-MR1M", "RefTitle": "MR1M: Incorrect movement type with asset posting", "RefUrl": "/notes/524877"}, {"RefNumber": "524446", "RefComponent": "FI-AA-AA-C", "RefTitle": "MR8M: update termination during update of table ANEK", "RefUrl": "/notes/524446"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "575206", "RefComponent": "MM-IV-LIV", "RefTitle": "MIRO/MR11: Goods receipt date not filled", "RefUrl": "/notes/575206 "}, {"RefNumber": "524877", "RefComponent": "MM-IV-LIV-MR1M", "RefTitle": "MR1M: Incorrect movement type with asset posting", "RefUrl": "/notes/524877 "}, {"RefNumber": "547233", "RefComponent": "FI-AA-AA-C", "RefTitle": "AA629 w/ reversal of valuated goods receipt in subsequent yr", "RefUrl": "/notes/547233 "}, {"RefNumber": "699158", "RefComponent": "FI-AA-AA-C", "RefTitle": "Error AA999 with posting to asset via Materials Management", "RefUrl": "/notes/699158 "}, {"RefNumber": "541200", "RefComponent": "FI-AA-AA-C", "RefTitle": "Error AA629 although values in fixed asset are ok", "RefUrl": "/notes/541200 "}, {"RefNumber": "615865", "RefComponent": "FI-AA-AA-C", "RefTitle": "Vendor not copied to fixed asset in MIRO", "RefUrl": "/notes/615865 "}, {"RefNumber": "546228", "RefComponent": "FI-AA-AA-C", "RefTitle": "FAQ - MM posting with fixed asset integration", "RefUrl": "/notes/546228 "}, {"RefNumber": "525536", "RefComponent": "FI-AA-AA-C", "RefTitle": "AA629 with Transaction MIRO", "RefUrl": "/notes/525536 "}, {"RefNumber": "525451", "RefComponent": "FI-AA-AA-C", "RefTitle": "MR01: no line items according to HP31", "RefUrl": "/notes/525451 "}, {"RefNumber": "524446", "RefComponent": "FI-AA-AA-C", "RefTitle": "MR8M: update termination during update of table ANEK", "RefUrl": "/notes/524446 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C31", "URL": "/supportpackage/SAPKH46C31"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 3, "URL": "/corrins/0000497297/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 14, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "40A", "ValidTo": "46C", "Number": "394345 ", "URL": "/notes/394345 ", "Title": "Error AK501 with document parking", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "45A", "ValidTo": "46C", "Number": "217637 ", "URL": "/notes/217637 ", "Title": "Incor.depreciatn calc./intrcompany transfer(ABUMN)", "Component": "FI-AA"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "45A", "ValidTo": "46C", "Number": "302314 ", "URL": "/notes/302314 ", "Title": "Posting trans: Validation ANTS-GKONT not filled", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "45A", "ValidTo": "46C", "Number": "302773 ", "URL": "/notes/302773 ", "Title": "Error AA480 with negative quantities", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "45A", "ValidTo": "46C", "Number": "400201 ", "URL": "/notes/400201 ", "Title": "Short dump ACCOUNT_NOT_VALID due to missing account", "Component": "FI-AA"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46A", "ValidTo": "46C", "Number": "408278 ", "URL": "/notes/408278 ", "Title": "Incorrect doc. type for intracompany/intercompany transfer", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46A", "ValidTo": "46C", "Number": "424049 ", "URL": "/notes/424049 ", "Title": "Error AA478 w/asset acquisition with group assets", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46A", "ValidTo": "46C", "Number": "426552 ", "URL": "/notes/426552 ", "Title": "Error AA321 with Transaction GJT1", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46A", "ValidTo": "46C", "Number": "452106 ", "URL": "/notes/452106 ", "Title": "AA480 if reversal of complete rtrmt of negative asset & qty", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "46C", "Number": "206176 ", "URL": "/notes/206176 ", "Title": "FM AAPO106/AAPO155 w/atmpt to pst intrcpy ast trsfr", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "46C", "Number": "318171 ", "URL": "/notes/318171 ", "Title": "AU133 w/invoice receipt", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "46C", "Number": "337294 ", "URL": "/notes/337294 ", "Title": "Error KI097 or M8326 for invoice receipt", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "46C", "Number": "360684 ", "URL": "/notes/360684 ", "Title": "MIRO/MR01: vendor not transferd to asst mastr recrd", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "46C", "Number": "380748 ", "URL": "/notes/380748 ", "Title": "MIRO: no statistical order account assignment", "Component": "FI-AA-AA-C"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "575206", "RefTitle": "MIRO/MR11: Goods receipt date not filled", "RefUrl": "/notes/**********"}]}}}}}