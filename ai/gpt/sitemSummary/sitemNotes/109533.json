{"Request": {"Number": "109533", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 361, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014578782017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000109533?language=E&token=18A1A4395937F01B48518AB100BE2873"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000109533", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000109533/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "109533"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 11}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.05.2008"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DWB-TOO-FUB"}, "SAPComponentKeyText": {"_label": "Component", "value": "Function Builder"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "ABAP Workbench, Java IDE and Infrastructure", "value": "BC-DWB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Workbench Tools: Editors, Painter, Modeler", "value": "BC-DWB-TOO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB-TOO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Function Builder", "value": "BC-DWB-TOO-FUB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB-TOO-FUB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "109533 - Use of SAP function modules"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p><br/>You want to call function modules from the SAP standard system in your own programming. What must you bear in mind?<br/></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p><br/>Function module, release, function group, customer development, SE37<br/></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><br/>SAP function modules (FMs) can be delivered with various release statuses. If a function module has been given the status &quot;Released for customers&quot;, this means that SAP ensures the following:</p> <UL><LI>The downward compatibility of the interfaces of this module</LI></UL> <UL><LI>The error-free function of the FMs in accordance with the documentation</LI></UL> <UL><LI>The fulfillment of general software maintenance tasks</LI></UL> <p><br/>SAP AG <B>does not offer any support</B> for other release statuses.<br/><br/>SAP does not make any claims regarding the release independence of the interfaces and the continuing existence/functioning of the modules.<br/><br/>However, if an error occurs in modules of this kind in a standard SAP program, support is provided.<br/><br/>The documented interfaces of the function modules of user exits delivered by SAP (enhancement concept) are not affected by the aforementioned exclusion from support. Thus, the use of user exits and their interfaces is supported in the standard system.<br/></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br/>SAP support will not handle problems that occur in the use of non-released function modules in customer systems.<br/><br/>Naturally, you can contact SAP consulting for help with issues of this kind.<br/></p> <b>Exceptions</b><br/> <p>The following function modules are supported even if they do not have the status &quot;Released for customers&quot;:</p> <OL>1. Modules with the name stem &quot;BDC_*&quot;</OL> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-SER-GEN (Please use note 1433157 for finding the right component)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D026744)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D002530)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000109533/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000109533/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000109533/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000109533/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000109533/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000109533/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000109533/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000109533/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000109533/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1818114", "RefComponent": "PP-SFC-IS", "RefTitle": "COIS_SELECT_ORDER_DATA_READ - Order type", "RefUrl": "/notes/1818114"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3089374", "RefComponent": "EIM-DS-ODP", "RefTitle": "/SAPDS/RFC_READ_TABLE2 not working as expected when used by third party", "RefUrl": "/notes/3089374 "}, {"RefNumber": "2527926", "RefComponent": "BC-CTS-ORG", "RefTitle": "Support for non released function module", "RefUrl": "/notes/2527926 "}, {"RefNumber": "1795864", "RefComponent": "SD-SLS-GF-IF", "RefTitle": "Serial number in sales order processing using BAPIs", "RefUrl": "/notes/1795864 "}, {"RefNumber": "3011121", "RefComponent": "BC-MID-RFC", "RefTitle": "How to remove RFCDES table entry with RFCTYPE is blank", "RefUrl": "/notes/3011121 "}, {"RefNumber": "3253920", "RefComponent": "SD-SLS-GF-IF", "RefTitle": "Using BAPI to create/change Scheduling Agreements", "RefUrl": "/notes/3253920 "}, {"RefNumber": "2318454", "RefComponent": "MM-PUR-SQ-QTA", "RefTitle": "Is there a specific program or BAPI to maintain Quota?", "RefUrl": "/notes/2318454 "}, {"RefNumber": "3074931", "RefComponent": "BC-FES-ITS", "RefTitle": "Function Modules with destination SAPGUI", "RefUrl": "/notes/3074931 "}, {"RefNumber": "3059590", "RefComponent": "LE-WM", "RefTitle": "How to find WM BAPIs, Function Modules and User-Exits (Warehouse Management)", "RefUrl": "/notes/3059590 "}, {"RefNumber": "2905085", "RefComponent": "FI-AA-AA-A", "RefTitle": "Clipboard error when excel file upload using FM ALSM_EXCEL_TO_INTERNAL_TABLE", "RefUrl": "/notes/2905085 "}, {"RefNumber": "3001894", "RefComponent": "BC-SEC-USR", "RefTitle": "Problems with FM SMUM_XML_PARSE", "RefUrl": "/notes/3001894 "}, {"RefNumber": "2375345", "RefComponent": "MM-PUR-REQ", "RefTitle": "Dump POSTING_ILLEGAL_STATEMENT in SAPLEBNE / LEBNEU01", "RefUrl": "/notes/2375345 "}, {"RefNumber": "2928910", "RefComponent": "PM-WOC-M<PERSON>", "RefTitle": "RAISE_EXCEPTION DUMP when using FM PM_ORDER_DATA_READ", "RefUrl": "/notes/2928910 "}, {"RefNumber": "2033925", "RefComponent": "SV-SMG-SVD-SWB", "RefTitle": "DSVAS_DOC_FILENAME_SPLIT not found in SAP ABA 7.4", "RefUrl": "/notes/2033925 "}, {"RefNumber": "2571879", "RefComponent": "PA-IS", "RefTitle": "Exporting to excel - corrupt output", "RefUrl": "/notes/2571879 "}, {"RefNumber": "2864090", "RefComponent": "SRM-EBP-CA-ATT", "RefTitle": "Accessing SRM document attachments from SAP GUI", "RefUrl": "/notes/2864090 "}, {"RefNumber": "1913529", "RefComponent": "PA-PA-XX", "RefTitle": "Function module HR_MAINTAIN_MASTERDATA does not work as expected when hiring an employee", "RefUrl": "/notes/1913529 "}, {"RefNumber": "2827770", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Unable to access BW Query with /QTQVC/SAPLBEX_QUERY", "RefUrl": "/notes/2827770 "}, {"RefNumber": "2812724", "RefComponent": "BC-BMT-WFM", "RefTitle": "Error in Function Module SWN_CREATE_SHORTCUT", "RefUrl": "/notes/2812724 "}, {"RefNumber": "1855914", "RefComponent": "MM-IM-GF-MISC", "RefTitle": "Possible reasons for gaps in material document number range - SAP ERP & SAP S/4 HANA", "RefUrl": "/notes/1855914 "}, {"RefNumber": "2780278", "RefComponent": "LE-SHP-DL", "RefTitle": "Use of Function Module BBP_INB_DELIVERY_CREATE to create an Inbound Delivery", "RefUrl": "/notes/2780278 "}, {"RefNumber": "2763685", "RefComponent": "LE-SHP-DL", "RefTitle": "BAPIs and Function Modules for Delivery creation", "RefUrl": "/notes/2763685 "}, {"RefNumber": "2697070", "RefComponent": "LE-WM-TFM", "RefTitle": "Error message handling for Function Module L_TO_CREATE_MULTIPLE", "RefUrl": "/notes/2697070 "}, {"RefNumber": "2656986", "RefComponent": "SRM-EBP-INT", "RefTitle": "FM BBP_MATERIAL_READ doesn't provide the expected result", "RefUrl": "/notes/2656986 "}, {"RefNumber": "2659445", "RefComponent": "BC-MID-ALE", "RefTitle": "Usage of BAPI_CURRENCY_CONV_TO_INTERNAL and BAPI_CURRENCY_CONV_TO_EXTERNAL", "RefUrl": "/notes/2659445 "}, {"RefNumber": "2655595", "RefComponent": "MM-PUR-GF-CU", "RefTitle": "Usage of SAP_CONVERT_TO_XLS_FORMAT", "RefUrl": "/notes/2655595 "}, {"RefNumber": "2650140", "RefComponent": "LO-MD-BP", "RefTitle": "RFC_CVI_EI_INBOUND_MAIN to become obsolete.", "RefUrl": "/notes/2650140 "}, {"RefNumber": "2646420", "RefComponent": "BC-SEC-USR", "RefTitle": "Function module S_WAP_USER_PASSWORD_RESET does not exist", "RefUrl": "/notes/2646420 "}, {"RefNumber": "2016862", "RefComponent": "FI-AA-AA-C", "RefTitle": "FI-AA: BAPI / Function Module does not work: unexpected or incorrect posting", "RefUrl": "/notes/2016862 "}, {"RefNumber": "2635274", "RefComponent": "BW-BEX-OT", "RefTitle": "BW Query called by customer program does not work after upgrading to new BW release", "RefUrl": "/notes/2635274 "}, {"RefNumber": "1686357", "RefComponent": "BC-UPG", "RefTitle": "Function Module SUBST_GET_FILE_LIST changed after Upgrade", "RefUrl": "/notes/1686357 "}, {"RefNumber": "2624391", "RefComponent": "PPM-PRO", "RefTitle": "Function module to determine frontend URL for project", "RefUrl": "/notes/2624391 "}, {"RefNumber": "2617062", "RefComponent": "BC-SRV-COM", "RefTitle": "DATA_LENGTH_TOO_LARGE dump occurs with CONVERT_OTF", "RefUrl": "/notes/2617062 "}, {"RefNumber": "2400979", "RefComponent": "CO-PC-ACT", "RefTitle": "Use of function module CKMD_DOCUMENT_REPORT under transaction CKM3", "RefUrl": "/notes/2400979 "}, {"RefNumber": "2599940", "RefComponent": "QM-IM", "RefTitle": "NO_ENTRY when executing function module QPAP_PLAN_SELECT with SEL_ID", "RefUrl": "/notes/2599940 "}, {"RefNumber": "2589124", "RefComponent": "BC-FES-ITS", "RefTitle": "FM: F4_FILENAME not work on SAP GUI for HTML", "RefUrl": "/notes/2589124 "}, {"RefNumber": "2546010", "RefComponent": "LO-MD-BP", "RefTitle": "RFC_CVI_EI_INBOUND_MAIN does not create/update the Business Partner master record as expected.", "RefUrl": "/notes/2546010 "}, {"RefNumber": "2519319", "RefComponent": "BC-SRV-ALV", "RefTitle": "Support troubleshooting guide: ALV - Internal Use Only", "RefUrl": "/notes/2519319 "}, {"RefNumber": "2521348", "RefComponent": "MM-PUR-PO", "RefTitle": "Use of CALCULATE_TAX_ITEM and CALCULATE_TAX_FROM_NET_AMOUNT in custom programs", "RefUrl": "/notes/2521348 "}, {"RefNumber": "2492048", "RefComponent": "LO-MD-MM", "RefTitle": "Alternative for un-released FM T685T_SINGLE_READ", "RefUrl": "/notes/2492048 "}, {"RefNumber": "2482270", "RefComponent": "BC-CTS-ORG", "RefTitle": "ABAP runtime error ASSIGN_LENGTH_0 in the function module TR_SPLIT_TEXT", "RefUrl": "/notes/2482270 "}, {"RefNumber": "1814974", "RefComponent": "BC-FES-OFFI", "RefTitle": "Error UX893 when using function module TEXT_CONVERT_XLS_TO_SAP", "RefUrl": "/notes/1814974 "}, {"RefNumber": "2481261", "RefComponent": "BC-MOB-DOE", "RefTitle": "Function Module SDOE_GET_LAST_DAY_OF_MONTH does not exist", "RefUrl": "/notes/2481261 "}, {"RefNumber": "2478131", "RefComponent": "CA-CL", "RefTitle": "Dump occurs with exception INVALID_CLASS_TYPE in Program SAPLCLAF", "RefUrl": "/notes/2478131 "}, {"RefNumber": "1775915", "RefComponent": "MM-PUR-PO-BAPI", "RefTitle": "It is not possible to create confirmations using BAPI", "RefUrl": "/notes/1775915 "}, {"RefNumber": "2468367", "RefComponent": "BC-DWB-TOO-FUB", "RefTitle": "EU512 error message when implementing an SAP Note [ SAP Internal only ]", "RefUrl": "/notes/2468367 "}, {"RefNumber": "2462651", "RefComponent": "SD-MD-CM", "RefTitle": "Unreleased Function Modules RV_CONDITION_COPY, RV_CONDITION_SAVE, RV_CONDITION_RESET", "RefUrl": "/notes/2462651 "}, {"RefNumber": "2152421", "RefComponent": "BC-BMT-WFM", "RefTitle": "Workflow function modules are not working as expected after system/Support Package upgrade", "RefUrl": "/notes/2152421 "}, {"RefNumber": "1909989", "RefComponent": "BC-DWB-TOO-FUB", "RefTitle": "FAQ - Function Modules usage and status", "RefUrl": "/notes/1909989 "}, {"RefNumber": "2481235", "RefComponent": "CA-GTF-MIG", "RefTitle": "Restrictions and extensibility of pre-delivered migration objects - SAP S/4HANA Migration Cockpit (on premise)", "RefUrl": "/notes/2481235 "}, {"RefNumber": "2725288", "RefComponent": "CRM-ISA", "RefTitle": "Use of RFC-enabled function modules of SAP CRM Web Channel, SAP E-Commerce, and SAP Web Channel Experience Management", "RefUrl": "/notes/2725288 "}, {"RefNumber": "1013758", "RefComponent": "CRM-BF", "RefTitle": "Explanations regarding enhancements in CRM", "RefUrl": "/notes/1013758 "}, {"RefNumber": "1818114", "RefComponent": "PP-SFC-IS", "RefTitle": "COIS_SELECT_ORDER_DATA_READ - Order type", "RefUrl": "/notes/1818114 "}, {"RefNumber": "1715800", "RefComponent": "PP-SFC-IS", "RefTitle": "COIS_SELECT_ORDER_DATA_READ - Incomplete data", "RefUrl": "/notes/1715800 "}, {"RefNumber": "382318", "RefComponent": "BC-SEC-AIS-TOO", "RefTitle": "FAQ | Function module RFC_READ_TABLE", "RefUrl": "/notes/382318 "}, {"RefNumber": "1426210", "RefComponent": "PSM-FM-PO-EF", "RefTitle": "FMRE: Field selection check for FMFR_CREATE_FROM_DATA", "RefUrl": "/notes/1426210 "}, {"RefNumber": "1514710", "RefComponent": "BC-DWB-TOO-FUB", "RefTitle": "RFC_GET_TABLE_ENTRIES", "RefUrl": "/notes/1514710 "}, {"RefNumber": "1135984", "RefComponent": "SD-MD-CM", "RefTitle": "Conditions cannot be created with BAPI_PRICES_CONDITIONS", "RefUrl": "/notes/1135984 "}, {"RefNumber": "499627", "RefComponent": "MM-PUR-REQ-BAPI", "RefTitle": "FAQ: BAPIs for purchase requisitions", "RefUrl": "/notes/499627 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}