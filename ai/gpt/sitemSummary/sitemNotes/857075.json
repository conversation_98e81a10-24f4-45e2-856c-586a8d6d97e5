{"Request": {"Number": "857075", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 363, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000004839642017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000857075?language=E&token=91A10322B094AC7C9A96EF5A599AD9BC"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000857075", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000857075/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "857075"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 13}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.09.2013"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CCM-PRN-TMS"}, "SAPComponentKeyText": {"_label": "Component", "value": "TemSe (Repository for temporary sequential objects)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Computer Center Management System (CCMS)", "value": "BC-CCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Print and Output Management", "value": "BC-CCM-PRN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-PRN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "TemSe (Repository for temporary sequential objects)", "value": "BC-CCM-PRN-TMS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-PRN-TMS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "857075 - TemSe:  Core DUMP in rstsf_tel_040_22707_131"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p><br />The trace file of a work process contains the following:<br /> C-stack for xxxxx<BR/> M ---------------------C-STACK-------------------------------------<BR/> ...................................................................<BR/> ?raise() at 0x..............<BR/> ?abort() at 0x..............<BR/> ?rstssbpw_sync_buff_prep_write() at 0x........<BR/> ..............................................<br />or<br /><br />?rstsf_tel_040_22707_131() at 0x............<br />.............................................<br /><br />One of the following entries occurs in the system log:&#x00A0;&#x00A0;E06 Assurance too many DB rows<br /><br />All these messages occur during the output to the database.<br /><br /><br />You cannot terminate the background job. The system constantly restarts the process.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SYSTEM_CORE_DUMPED, SPOOL_TEMSE_ERROR</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The maximum size of 2GB for part of a TemSe object was reached.<br />This is usually displayed if an existing object is extended. This is often the case for spool requests. We recommend that you set large spool requests to &#39;completed&#39; so that these spool requests can no longer be extended.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>For all releases &gt; = 7.00: Read the information in SAP Note 1890546:<br /><br />For all releases &lt; 7.00: Unfortunately, it is not possible to exceed this limit. Generally, you should ensure that application programs select as little data as possible to prevent this problem from occurring.<br /><br />To avoid the problem, you have the following options:<br /></p> <OL>1. Create a new TemSe object, or a further part for the existing object, in your application.</OL> <OL>2. If the TemSe object is a Sapscript or Smart form spool request, ensure that the form is as small as possible. Check if the form uses embedded true-type fonts or graphics. Graphics should contain no whitespace, and have low color detail. When you upload the graphics it can be helpful to set the indicator &#39;Resident in printer memory&#39;. True-type fonts are often unnecessary if the printer you use has the same font installed (for example, Arial, Helvetica and Univers are nearly identical fonts).<br /></OL> <p>After you implement the attached correction instructions, the report RSTS0014 is available. You can use this report to select spool requests that have a critical size, but that can still be extended. In the attributes, set the indicator &#39;completed, no longer possible to add&#39;. We recommend that you run this report once a month in the dialog.<br /><br />The background job is now completed correctly and the system writes a message to the system log.<br /> The problem is resolved with a kernel patch.<br />4.6D - patch 2388<br />6.40 - patch 227<br />7.00 - patch 155<br />7.10 - patch 99<br /><br />Add the following message using transaction SE92:<br /><br />F3O   2 GB limit for TemSe object reached<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-CCM-PRN (Print and Output Management)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D025322)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D025322)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000857075/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000857075/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000857075/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000857075/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000857075/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000857075/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000857075/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000857075/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000857075/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1890546", "RefComponent": "BC-CCM-PRN", "RefTitle": "Spool requests larger than 2 GB", "RefUrl": "/notes/1890546"}, {"RefNumber": "1688396", "RefComponent": "BC-CCM-PRN", "RefTitle": "Dump in SP01 when displaying TemSe attributes", "RefUrl": "/notes/1688396"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2605285", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "2 GB limit for TemSe object reached", "RefUrl": "/notes/2605285 "}, {"RefNumber": "1890546", "RefComponent": "BC-CCM-PRN", "RefTitle": "Spool requests larger than 2 GB", "RefUrl": "/notes/1890546 "}, {"RefNumber": "1688396", "RefComponent": "BC-CCM-PRN", "RefTitle": "Dump in SP01 when displaying TemSe attributes", "RefUrl": "/notes/1688396 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "31H", "To": "31I", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "46A", "To": "46D", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "640", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "701", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "711", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C56", "URL": "/supportpackage/SAPKB46C56"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62064", "URL": "/supportpackage/SAPKB62064"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62065", "URL": "/supportpackage/SAPKB62065"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64023", "URL": "/supportpackage/SAPKB64023"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64022", "URL": "/supportpackage/SAPKB64022"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70015", "URL": "/supportpackage/SAPKB70015"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70016", "URL": "/supportpackage/SAPKB70016"}, {"SoftwareComponentVersion": "SAP_BASIS 710", "SupportPackage": "SAPKB71007", "URL": "/supportpackage/SAPKB71007"}, {"SoftwareComponentVersion": "SAP_BASIS 710", "SupportPackage": "SAPKB71006", "URL": "/supportpackage/SAPKB71006"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP KERNEL 7.01 32-BIT", "SupportPackage": "SP003", "SupportPackagePatch": "000003", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011145&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 32-BIT UNICODE", "SupportPackage": "SP003", "SupportPackagePatch": "000003", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011146&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 64-BIT", "SupportPackage": "SP003", "SupportPackagePatch": "000003", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011147&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 64-BIT UNICODE", "SupportPackage": "SP003", "SupportPackagePatch": "000003", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011148&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 32-BIT", "SupportPackage": "SP108", "SupportPackagePatch": "000000", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004834&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 64-BIT UNICODE", "SupportPackage": "SP108", "SupportPackagePatch": "000000", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004838&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 64-BIT", "SupportPackage": "SP108", "SupportPackagePatch": "000000", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004839&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 32-BIT UNICODE", "SupportPackage": "SP108", "SupportPackagePatch": "000000", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004840&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT UNICODE", "SupportPackage": "SP237", "SupportPackagePatch": "000237", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004051&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT UNICODE", "SupportPackage": "SP237", "SupportPackagePatch": "000237", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004052&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT", "SupportPackage": "SP237", "SupportPackagePatch": "000237", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006931&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT", "SupportPackage": "SP237", "SupportPackagePatch": "000237", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006932&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT", "SupportPackage": "SP164", "SupportPackagePatch": "000164", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004059&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT UNICODE", "SupportPackage": "SP164", "SupportPackagePatch": "000164", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004835&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT", "SupportPackage": "SP164", "SupportPackagePatch": "000164", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004836&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT UNICODE", "SupportPackage": "SP164", "SupportPackagePatch": "000164", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004837&V=MAINT"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 4, "URL": "/corrins/0000857075/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}