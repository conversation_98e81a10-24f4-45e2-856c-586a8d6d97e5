{"Request": {"Number": "787665", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 255, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000004616192017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000787665?language=E&token=FB6B88E68B1F16EF1131CB89F105EF2F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000787665", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "787665"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.09.2005"}, "SAPComponentKey": {"_label": "Component", "value": "IS-H"}, "SAPComponentKeyText": {"_label": "Component", "value": "Hospital"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hospital", "value": "IS-H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "787665 - IS-H*MED transfer function master data cl. order"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note provides the option of transporting preregistration types or clinical orders as a function in IS-H and IS-H-MED. In order to be able to use the transport functionality from the master data transaction to maintain preregistration or order types (N1COT), you must first execute the RN1_SYNC_CORDERTYPES program. Note the documentation for the above mentioned program.<br />This note represents an advance-delivery of a new functionality. This note requires a large number of manual adjustments. It is possible to avoid these manual adjustments by importing 4.72 Add-On Support Package 11, which includes this function. If you require this function before 4.72 Add-On Support Package 11, we advise you to read the instructions for importing this note.<br />In addition, we would like to advise you that this note is NOT an optional note. This note must be imported (either with 4.72 Add-On Support Package 11 or by using these correction instructions) since future notes may require this note as a prerequisite.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>N1COT,<br />Transport of preregistration types or clinical order types</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is caused by a missing functionality.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Before implementing the changes, you must carry out the following actions:</p> <b>Import the attachment</b><br /> <UL><LI>Unpack the attached file HW787665_472.zip for IS-H/IS-H-MED version 4.72.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note that you cannot download the attached file via OSS, but must do so from the SAP Service Marketplace (see also Note 480180 and Note 13719 on how to import attachments).</p> <UL><LI>Import the unpacked orders into your system.</LI></UL> <b>Executing manual changes</b><br /> <p>Create new methods for the CL_ISHMED_FATTR class:</p> <UL><LI>Call Transaction SE24.</LI></UL> <UL><LI>Specify CL_ISHMED_FATTR as an object type and select \"Change\".</LI></UL> <UL><LI>Select the tab page \"Method\".</LI></UL> <UL><LI>Scroll to the last method and enter the following values in the last free row:</LI></UL> <UL><UL><LI>Method: GET_DATA</LI></UL></UL> <UL><UL><LI>Type: Instance Method</LI></UL></UL> <UL><UL><LI>Visibility: Public</LI></UL></UL> <UL><UL><LI>Description: Returns the data (N1FATTR) of the object.</LI></UL></UL> <UL><LI>Place the cursor on the new method, and select the function \"parameter\" (= pushbutton).</LI></UL> <UL><LI>Enter the following values in the first row:</LI></UL> <UL><UL><LI>Parameter: RS_N1FATTR</LI></UL></UL> <UL><UL><LI>Type: Returning</LI></UL></UL> <UL><UL><LI>Pass by value: Select checkbox</LI></UL></UL> <UL><UL><LI>Optional:</LI></UL></UL> <UL><UL><LI>Typing: Type</LI></UL></UL> <UL><UL><LI>Mix type: N1FATTR</LI></UL></UL> <UL><UL><LI>Default value:</LI></UL></UL> <UL><UL><LI>Description: IS-H*MED: Clinical order: Field attributes</LI></UL></UL> <UL><LI>Save the class.</LI></UL> <UL><LI>Activate the class.</LI></UL> <p>Create a new method for class CL_ISHMED_CMETHSTA:</p> <UL><LI>Call Transaction SE24.</LI></UL> <UL><LI>Specify CL_ISHMED_CMETHSTA as an object type and select the function \"Change\".</LI></UL> <UL><LI>Select the tab page \"method\".</LI></UL> <UL><LI>Scroll to the last method and enter the following values in the first free row:</LI></UL> <UL><UL><LI>Method: GET_DATA</LI></UL></UL> <UL><UL><LI>Type: Instance Method</LI></UL></UL> <UL><UL><LI>Visibility: Public</LI></UL></UL> <UL><UL><LI>Description: Returns data (N1CMETHSTA) of the object.</LI></UL></UL> <UL><LI>Position the cursor on the new method and select the function \"parameter\" (= pushbutton)</LI></UL> <UL><LI>Enter the following values in the first row:</LI></UL> <UL><UL><LI>Parameter: RS_N1CMETHSTA</LI></UL></UL> <UL><UL><LI>Type: Returning</LI></UL></UL> <UL><UL><LI>Pass value: Select checkbox</LI></UL></UL> <UL><UL><LI>Optional:</LI></UL></UL> <UL><UL><LI>Typing: Type</LI></UL></UL> <UL><UL><LI>Mix type: N1CMETHSTA</LI></UL></UL> <UL><UL><LI>Default value:</LI></UL></UL> <UL><UL><LI>Description: IS-H*MED: Assigning methods of a module to status change</LI></UL></UL> <UL><LI>Save the class.</LI></UL> <UL><LI>Activate the class.</LI></UL> <p>Create a new method for class CL_ISH_CORDTSTA:</p> <UL><LI>Call Transaction SE24.</LI></UL> <UL><LI>Specify CL_ISH_CORDTSTA as an object type and select the function \"Change\".</LI></UL> <UL><LI>Select the tab page \"method\".</LI></UL> <UL><LI>Scroll to the last method and enter the following values in the first free row:</LI></UL> <UL><UL><LI>Method: GET_DATA</LI></UL></UL> <UL><UL><LI>Type: Instance Method</LI></UL></UL> <UL><UL><LI>Visibility: Public</LI></UL></UL> <UL><UL><LI>Description: Returns data of the status assignment.</LI></UL></UL> <UL><LI>Position the cursor on the new method and select the function \"parameter\" (= pushbutton).</LI></UL> <UL><LI>Enter following values in the first row:</LI></UL> <UL><UL><LI>Parameter: RS_N1CORDTSTA</LI></UL></UL> <UL><UL><LI>Type: Returning</LI></UL></UL> <UL><UL><LI>Pass by value: Select checkbox</LI></UL></UL> <UL><UL><LI>Optional:</LI></UL></UL> <UL><UL><LI>Typing: Type</LI></UL></UL> <UL><UL><LI>Mix type: N1CORDTSTA</LI></UL></UL> <UL><UL><LI>Default value:</LI></UL></UL> <UL><UL><LI>Description: IS-H: Order category assignment - status profile</LI></UL></UL> <UL><LI>Save the class.</LI></UL> <UL><LI>Activate the class.</LI></UL> <p>Enhancements for class CL_ISH_CORDTYP:</p> <UL><LI>Call the transaction SE24.</LI></UL> <UL><LI>Specify as an object type CL_ISH_CORDTYP and select the function \"Change\".</LI></UL> <UL><LI>Select the tab page \"feature\".</LI></UL> <UL><LI>Enter following type group in the first empty row under \"forward declarations\": TRWBO.</LI></UL> <UL><LI>Save the class.</LI></UL> <UL><LI>Switch to the tab page \"attribute\".</LI></UL> <UL><LI>Scroll to the last attribute and enter following values in the first free row:</LI></UL> <UL><UL><LI>Attribute: GT_TRANSPORT_OBJECTS_ORIG</LI></UL></UL> <UL><UL><LI>Type: Instance attributes</LI></UL></UL> <UL><UL><LI>Visibility: Protected</LI></UL></UL> <UL><UL><LI>Modelled only:</LI></UL></UL> <UL><UL><LI>Read-only:</LI></UL></UL> <UL><UL><LI>Typing: Type</LI></UL></UL> <UL><UL><LI>Mix type: TR_OBJECTS</LI></UL></UL> <UL><UL><LI>Description: Objects</LI></UL></UL> <UL><UL><LI>Initial value:</LI></UL></UL> <UL><LI>Enter the following values in the next empty row:</LI></UL> <UL><UL><LI>Attribute: GT_TRANSPORT_OBJECTS</LI></UL></UL> <UL><UL><LI>Type: Instance attributes</LI></UL></UL> <UL><UL><LI>Visibility: Protected</LI></UL></UL> <UL><UL><LI>Modelled only:</LI></UL></UL> <UL><UL><LI>Read-only ones:</LI></UL></UL> <UL><UL><LI>Typing: Type</LI></UL></UL> <UL><UL><LI>Mix type: TR_OBJECTS</LI></UL></UL> <UL><UL><LI>Description: Objects</LI></UL></UL> <UL><UL><LI>Initial value:</LI></UL></UL> <UL><LI>Enter the following values in the next empty row:</LI></UL> <UL><UL><LI>Attribute: GT_TRANSPORT_KEYS_ORIG</LI></UL></UL> <UL><UL><LI>Type: Instance attributes</LI></UL></UL> <UL><UL><LI>Visibility: Protected</LI></UL></UL> <UL><UL><LI>Modelled only:</LI></UL></UL> <UL><UL><LI>Read-only ones:</LI></UL></UL> <UL><UL><LI>Typing: Type</LI></UL></UL> <UL><UL><LI>Mix type: TR_KEYS</LI></UL></UL> <UL><UL><LI>Description: Table key</LI></UL></UL> <UL><UL><LI>Initial value:</LI></UL></UL> <UL><LI>Enter the following values in the next empty row:</LI></UL> <UL><UL><LI>Attribute: GT_TRANSPORT_KEYS</LI></UL></UL> <UL><UL><LI>Type: Instance attributes</LI></UL></UL> <UL><UL><LI>Visibility: Protected</LI></UL></UL> <UL><UL><LI>Modelled only:</LI></UL></UL> <UL><UL><LI>Read-only ones:</LI></UL></UL> <UL><UL><LI>Typing: Type</LI></UL></UL> <UL><UL><LI>Mix type: TR_KEYS</LI></UL></UL> <UL><UL><LI>Description: Table key</LI></UL></UL> <UL><UL><LI>Initial value:</LI></UL></UL> <UL><LI>Save the class.</LI></UL> <UL><LI>Switch to the tab page \"method\".</LI></UL> <UL><LI>Scroll up to the last method and enter the following values in the first free row:</LI></UL> <UL><UL><LI>Method: GET_T_TRANSPORT_DATA</LI></UL></UL> <UL><UL><LI>Type: Instance Method</LI></UL></UL> <UL><UL><LI>Visibility: Protected</LI></UL></UL> <UL><UL><LI>Description: Return the data of the order category to be transported.</LI></UL></UL> <UL><LI>Position the cursor on the new method and select the function \"parameter\" (= pushbutton).</LI></UL> <UL><LI>Enter the following values in the first row:</LI></UL> <UL><UL><LI>Parameter: ET_OBJECTS</LI></UL></UL> <UL><UL><LI>Type: Exporting</LI></UL></UL> <UL><UL><LI>Pass by value: Select checkbox</LI></UL></UL> <UL><UL><LI>Optional:</LI></UL></UL> <UL><UL><LI>Typing: Type</LI></UL></UL> <UL><UL><LI>Mix type: TR_OBJECTS</LI></UL></UL> <UL><UL><LI>Default value:</LI></UL></UL> <UL><UL><LI>Description: Objects</LI></UL></UL> <UL><LI>Enter the following values in the next row:</LI></UL> <UL><UL><LI>Parameter: ET_KEYS</LI></UL></UL> <UL><UL><LI>Type: Exporting</LI></UL></UL> <UL><UL><LI>Pass by value: Select checkbox</LI></UL></UL> <UL><UL><LI>Optional:</LI></UL></UL> <UL><UL><LI>Typing: Type</LI></UL></UL> <UL><UL><LI>Mix type: TR_KEYS</LI></UL></UL> <UL><UL><LI>Default value:</LI></UL></UL> <UL><UL><LI>Description: Table key</LI></UL></UL> <UL><LI>Enter the following values in the next row:</LI></UL> <UL><UL><LI>Parameter: E_RC</LI></UL></UL> <UL><UL><LI>Type: Exporting</LI></UL></UL> <UL><UL><LI>Pass by value: Select checkbox</LI></UL></UL> <UL><UL><LI>Optional:</LI></UL></UL> <UL><UL><LI>Typing: Type</LI></UL></UL> <UL><UL><LI>Mix type: ISH_METHOD_RC</LI></UL></UL> <UL><UL><LI>Default value:</LI></UL></UL> <UL><UL><LI>Description: IS-H: Return code with method calls</LI></UL></UL> <UL><LI>Enter the following values in the next row:</LI></UL> <UL><UL><LI>Parameter: CR_ERRORHANDLER</LI></UL></UL> <UL><UL><LI>Type: Changing</LI></UL></UL> <UL><UL><LI>Pass by value:</LI></UL></UL> <UL><UL><LI>Optional: Select checkbox</LI></UL></UL> <UL><UL><LI>Typing: Type Ref To</LI></UL></UL> <UL><UL><LI>Mix type: CL_ISHMED_ERRORHANDLING</LI></UL></UL> <UL><UL><LI>Default value:</LI></UL></UL> <UL><UL><LI>Description: IS-H*MED: Class for the error processing</LI></UL></UL> <UL><LI>Save the class.</LI></UL> <UL><LI>Select the function \"method\" (= pushbutton) to return to the method display.</LI></UL> <UL><LI>Enter the following data in the next free row:</LI></UL> <UL><UL><LI>Method: GET_T_TRANSPORT_DATA_DELETED</LI></UL></UL> <UL><UL><LI>Type: Instance Method</LI></UL></UL> <UL><UL><LI>Visibility: Proteced</LI></UL></UL> <UL><UL><LI>Description: Return the deleted data to be transported.</LI></UL></UL> <UL><LI>Position the cursor on the new method and select the function \"parameter\" (= pushbutton).</LI></UL> <UL><LI>Enter the same parameters as before describedly for the method GET_T_TRANSPORT_DATA.</LI></UL> <UL><LI>Save the class.</LI></UL> <UL><LI>Select the function \"method\" (= pushbutton) to return to the method display.</LI></UL> <UL><LI>Enter the following data in the next free row:</LI></UL> <UL><UL><LI>Method: INITIALIZE_TRANSPORT_DATA</LI></UL></UL> <UL><UL><LI>Type: Instance Method</LI></UL></UL> <UL><UL><LI>Visibility: Public</LI></UL></UL> <UL><UL><LI>Description: Initialize the original data for the transport comparison.</LI></UL></UL> <UL><LI>Save the class.</LI></UL> <UL><LI>Enter the following data in the next free row:</LI></UL> <UL><UL><LI>Method: TRANSPORT</LI></UL></UL> <UL><UL><LI>Type: Instance Method</LI></UL></UL> <UL><UL><LI>Visibility: Public</LI></UL></UL> <UL><UL><LI>Description: Transport of the order category.</LI></UL></UL> <UL><LI>Position the cursor on the new method and select the function \"parameter\" (= pushbutton).</LI></UL> <UL><LI>Enter the following values in the first row:</LI></UL> <UL><UL><LI>Parameter: I_SUPRESS_DIALOG</LI></UL></UL> <UL><UL><LI>Type: Importing</LI></UL></UL> <UL><UL><LI>Pass by value: Select checkbox</LI></UL></UL> <UL><UL><LI>Optional: Select checkbox</LI></UL></UL> <UL><UL><LI>Typing: Type</LI></UL></UL> <UL><UL><LI>Mix type: TRPARFLAG</LI></UL></UL> <UL><UL><LI>Default value: SPACE</LI></UL></UL> <UL><UL><LI>Description: Flag (\"X\" or \" \")</LI></UL></UL> <UL><LI>Enter the following values in the next row:</LI></UL> <UL><UL><LI>Parameter: E_RC</LI></UL></UL> <UL><UL><LI>Type: Exporting</LI></UL></UL> <UL><UL><LI>Pass by value: Select checkbox</LI></UL></UL> <UL><UL><LI>Optional:</LI></UL></UL> <UL><UL><LI>Typing: Type</LI></UL></UL> <UL><UL><LI>Mix type: ISH_METHOD_RC</LI></UL></UL> <UL><UL><LI>Default value:</LI></UL></UL> <UL><UL><LI>Description: IS-H: Return code with method calls</LI></UL></UL> <UL><LI>Enter the following values in the next row:</LI></UL> <UL><UL><LI>Parameter: C_REQUEST</LI></UL></UL> <UL><UL><LI>Type: Changing</LI></UL></UL> <UL><UL><LI>Pass by value:</LI></UL></UL> <UL><UL><LI>Optional: Select checkbox</LI></UL></UL> <UL><UL><LI>Typing: Type</LI></UL></UL> <UL><UL><LI>Mix type: TRKORR</LI></UL></UL> <UL><UL><LI>Default value: SPACE</LI></UL></UL> <UL><UL><LI>Description: Order/task</LI></UL></UL> <UL><LI>Enter the following values in the next row:</LI></UL> <UL><UL><LI>Parameter: CR_ERRORHANDLER</LI></UL></UL> <UL><UL><LI>Type: Changing</LI></UL></UL> <UL><UL><LI>Pass by value:</LI></UL></UL> <UL><UL><LI>Optional: Select checkbox</LI></UL></UL> <UL><UL><LI>Typing: Type Ref To</LI></UL></UL> <UL><UL><LI>Mix type: CL_ISHMED_ERRORHANDLING</LI></UL></UL> <UL><UL><LI>Default value:</LI></UL></UL> <UL><UL><LI>Description: IS-H*MED: Class for the error processing</LI></UL></UL> <UL><LI>Select the function \"method\" (= pushbutton) to return to the method display.</LI></UL> <UL><LI>Save the class.</LI></UL> <UL><LI>Activate the class.</LI></UL> <p>Redefine the method in class CL_ISHMED_CORDTYP:</p> <UL><LI>Call the transaction SE24.</LI></UL> <UL><LI>Specify as an object type CL_ISHMED_CORDTYP and select the function \"Change\".</LI></UL> <UL><LI>Switch to the tab page \"method\".</LI></UL> <UL><LI>Position the cursor on the method GET_T_TRANSPORT_DATA and select the function \"redefine\" (= pushbutton/icon).</LI></UL> <UL><LI>Delete the (generated) rows between METHOD and ENDMETHOD.</LI></UL> <UL><LI>Save the method.</LI></UL> <UL><LI>Activate the method.</LI></UL> <p>Enhancements for the class CL_ISH_ALVGRID_CORDERTYPES:</p> <UL><LI>Call the transaction SE24.</LI></UL> <UL><LI>Specify as an object type CL_ISH_ALVGRID_CORDERTYPES and select the function \"Change\".</LI></UL> <UL><LI>Switch to the tab page \"event\".</LI></UL> <UL><LI>Scroll to the last event and enter the following values in the first free row:</LI></UL> <UL><UL><LI>Event: EV_TRANSPORT</LI></UL></UL> <UL><UL><LI>Type: Event instance</LI></UL></UL> <UL><UL><LI>Visibility: Public</LI></UL></UL> <UL><UL><LI>Description: User selected function order category Transport.</LI></UL></UL> <UL><LI>Position the cursor on the new event and select the function \"parameter\" (= pushbutton).</LI></UL> <UL><LI>Enter the following values in the first row:</LI></UL> <UL><UL><LI>Parameter: ES_CORDTYP_OUTTAB</LI></UL></UL> <UL><UL><LI>Optional:</LI></UL></UL> <UL><UL><LI>Typing: Type</LI></UL></UL> <UL><UL><LI>Mix type: RN1_CORDTYP_OUTTAB</LI></UL></UL> <UL><UL><LI>Default value:</LI></UL></UL> <UL><UL><LI>Description: IS-H: Structure for the display in COrdType Overview (ALV-grid)</LI></UL></UL> <UL><LI>Save the class.</LI></UL> <UL><LI>Switch to the tab page \"method\".</LI></UL> <UL><LI>Scroll to the last method and enter the following values in the first free row:</LI></UL> <UL><UL><LI>Methods: PROCESS_TRANSPORT</LI></UL></UL> <UL><UL><LI>Type: Instance Method</LI></UL></UL> <UL><UL><LI>Visibility: Private</LI></UL></UL> <UL><UL><LI>Description: Handling TRAN button.</LI></UL></UL> <UL><LI>Save the class.</LI></UL> <UL><LI>Select the menu option \"Goto/Text elements\".</LI></UL> <UL><LI>Scroll to the last text element and enter the following values in the first free row:</LI></UL> <UL><UL><LI>Sym: 018</LI></UL></UL> <UL><UL><LI>Text: Transport order type</LI></UL></UL> <UL><UL><LI>mLen: 40</LI></UL></UL> <UL><LI>Save the text elements.</LI></UL> <UL><LI>Activate the text elements.</LI></UL> <UL><LI>Activate the class.</LI></UL> <p>Entering new messages</p> <UL><LI>Call the transaction SE91.</LI></UL> <UL><LI>Enter as message class N1CORDMG and as a number 124.</LI></UL> <UL><LI>Select the function \"Change\".</LI></UL> <UL><LI>Enter the following message short text:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Order type &amp;1 was connected to transport request &amp;2. </p> <UL><LI>Save the change in active version.</LI></UL> <UL><LI>Repeat the activity for the following messages:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Number: 125, text: Action canceled, transport connection not carried out.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Number: 126, text: The system could not carry out the transport connection.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Number: 127, text: Order category &amp; 1 was saved and connected to transport request &amp;2.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Number: 128, text: Order category &amp; 1 was saved, transport connection not carried out.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Number: 134, text: an order type may be selected for this function only.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Number: 138, text: No transport request was created.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Number: 139, text: Current system is non-synchronous with target system of the transport request.</p> <UL><LI>For message 139, deselect the checkbox \"self-explanatory\".</LI></UL> <UL><LI>Position the cursor on message 139 and select the function \"long text\".</LI></UL> <UL><LI>Enter text the following under \"diagnosis\":</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The master data for the order types of the current system is non-synchronous with the data of the target system of the selected transport request. A data transfer between both systems would result in a data inconsistency.</p> <UL><LI>Enter text following under \"procedure\":</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In order to be able to transport the desired order types to the desired target system, you must execute the RN1_SYNC_CORDERTYPES program beforehand to synchronize the data of the two systems.</p> <UL><LI>Save the change in active version.</LI></UL> <p>Text element entry</p> <UL><LI>Call the transaction SE80.</LI></UL> <UL><LI>In the input help of the upper input field, select \"function group\". Enter the function group N1DEFCORDT under that and press the return key.</LI></UL> <UL><LI>Position the cursor on the function group N1DEFCORDT and press the right mouse button.</LI></UL> <UL><LI>You select the display function -&gt; text elements.</LI></UL> <UL><LI>Change into the change mode.</LI></UL> <UL><LI>Enter the following data in the next free row:</LI></UL> <UL><UL><LI>Sym: 011</LI></UL></UL> <UL><UL><LI>Text: The order category must be protected from the transport. Do you want to continue?</LI></UL></UL> <UL><UL><LI>mLen: 120</LI></UL></UL> <UL><LI>You Save and activate the entered data.</LI></UL> <p>Creating function code</p> <UL><LI>Call the transaction SE80.</LI></UL> <UL><LI>In the input help of the upper input field, select \"function group\". Enter the function group N1DEFCORDT and press the return key.</LI></UL> <UL><LI>Expand the node GUI_Status and open the status 0100 with double-click.</LI></UL> <UL><LI>Change into the change mode.</LI></UL> <UL><LI>Expand the node \"menu bar\" in the right display window.</LI></UL> <UL><LI>Open the entry \"order category\" by double-clicking it.</LI></UL> <UL><LI>Place the cursor on the hyphen between the displayed functions and select Edit -&gt; Insert -&gt; entry in the menu.</LI></UL> <UL><LI>Enter the TRAN function code in the following dialog box and confirm the entry.</LI></UL> <UL><LI>Confirm the dialog box with the function code and text type (static text).</LI></UL> <UL><LI>Enter the following values:</LI></UL> <UL><UL><LI>Function text: Transport</LI></UL></UL> <UL><UL><LI>Icon name: ICON_TRANSPORT</LI></UL></UL> <UL><LI>Confirm your entries.</LI></UL> <UL><LI>Expand the node \"Function key\" in the right display window.</LI></UL> <UL><LI>Enter the TRAN function under \"freely assignable function keys\" (for example, Shift-F1 ) and enter the text \"Transport\" next to it.</LI></UL> <UL><LI>Save and activate the entered data.</LI></UL> <p><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-PART-ISHMED-ORD (Service Management i.s.h.med)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5046924)"}, {"Key": "Processor                                                                                           ", "Value": "C5054647"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000787665/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000787665/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000787665/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000787665/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000787665/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000787665/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000787665/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000787665/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000787665/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "HW787665_472.zip", "FileSize": "15", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200001546972004&iv_version=0001&iv_guid=B416D569B2E5E14A9996907B55947842"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "889167", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clinical Order: Transport of Order Types", "RefUrl": "/notes/889167"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "889167", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clinical Order: Transport of Order Types", "RefUrl": "/notes/889167 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-H", "From": "472", "To": "472", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "IS-H 472", "SupportPackage": "SAPKIPHF11", "URL": "/supportpackage/SAPKIPHF11"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 1, "URL": "/corrins/0000787665/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}