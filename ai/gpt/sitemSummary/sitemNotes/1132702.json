{"Request": {"Number": "1132702", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 2184, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016448132017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001132702?language=E&token=B91E8222C8C16BA9883617AF7F43D00F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001132702", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001132702/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1132702"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.01.2008"}, "SAPComponentKey": {"_label": "Component", "value": "IS-A-LMN"}, "SAPComponentKeyText": {"_label": "Component", "value": "Long Material Number"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Automotive", "value": "IS-A", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-A*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Long Material Number", "value": "IS-A-LMN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-A-LMN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1132702 - Upgrade ECC-DIMP600 is not completed"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note is only useful for the Release ERP2005/ECC-DIMP 600 with activated Business Function Set 'DIMP'.<br /><br />There are following cases:<br />1. You have upgraded or installed ECC-DIMP 600 and the business function DIMP_SDUD has been switched on.<br />But the After-Switch method MGV_DDIC_CHANGE_REPORT_DIMP is not executed. You can check it when you look at the domain KKB_MATNR in the transaction SE11. If its output length is still 18 characters, the method is not executed yet.<br /><br />2. You will upgrade ECC-DIMP 600 before the support package 12 and you will switch on the business function DIMP_SDUD.<br /><br />3. You will install ECC-DIMP 600 before the support package 12 and you will switch on the business function DIMP_SDUD.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>DIMP&#x00A0;&#x00A0;DIMP_SDUD MGV_LAMA&#x00A0;&#x00A0;IS_AD_MPN<br />CL_IM_LAMACHANGES_FOR_DIMP<br />LAMA_CHANGES&#x00A0;&#x00A0;LAMACHANGES_FOR_DIMP&#x00A0;&#x00A0;DIMP_GENERAL<br />MGV_DDIC_CHANGE_REPORT_DIMP<br />KKB_MATNR&#x00A0;&#x00A0;SCHABLONE&#x00A0;&#x00A0;MATSCH<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The reason for case (1) is there is a definition error in BADI Enhancement Implementation LAMA_CHANGES.<br />After switch-on the business function DIMP_SDUD during the upgrade (case 1) the report MGV_DDIC_CHANGE_REPORT_DIMP should be executed to change some DDIC objects, but the report could not be found because the filter name of the enhancement LAMACHANGES_FOR_DIMP is wrong.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>A) To remove the error using the transaction SE19:<br />- Enter BADI Enhancement Implementation: LAMA_CHANGES and press the button 'CHANGE'.<br />- Select the filter of the BADI implementation LAMACHANGES_FOR_DIMP by double clicking the filter.<br />- Change the filter name (Value 1) from DIMP_GEN to DIMP_GENERAL.<br />- Save and activate this change.<br /><br />This correction will be delivered via ECC-DIMP 600 support package 12.<br /><br />B) To make the DDIC-Changes which should be done by the report MGV_DDIC_CHANGE_REPORT_DIMP during the upgrade:<br /><br />There is a new report ZMGV_DDIC_CHANGE_REPORT_DIMP append in the attachments of this note.<br />This report refers to the report(I) mentioned in the note 849981 and makes the same DDIC changes like it. Please pay attention to the description 'Risk of data loss' in the note 849981.<br /><br />You can download the file from the attachments and create the local report ZMGV_DDIC_CHANGE_REPORT_DIMP in your system.<br /><br />For case (1):<br />- Download and create the local report.<br />- Execute the report in SE38 to make the DDIC changes afterwards.<br /><br />For case (2):<br />- Download and create the local report.<br />- Upgrade ECC-DIMP600.<br />- Run the local report to make the DDIC changes afterwards.<br /><br />For case (3):<br />- Before switching on the Business Function DIMP_SDUD in SFW5, you have to execute the correction in (A) and after switching you don't need running the local report.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D021411"}, {"Key": "Processor                                                                                           ", "Value": "D002209"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001132702/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001132702/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001132702/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001132702/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001132702/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001132702/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001132702/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001132702/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001132702/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "ZMGV_DDIC_CHANGE_REPORT_DIMP.txt", "FileSize": "29", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000045182008&iv_version=0002&iv_guid=B3EAEB7FAD6C414289B20801C1DC92ED"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "874471", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Additions for installing or activating ECC-DIMP on ECC 6.0", "RefUrl": "/notes/874471"}, {"RefNumber": "872197", "RefComponent": "IS-A", "RefTitle": "Additional info about upgrade to SAP ECC 6.0 w/ ECC-DIMP 6.0", "RefUrl": "/notes/872197"}, {"RefNumber": "849981", "RefComponent": "IS-A-LMN", "RefTitle": "DIMP: Upgrade to ERP 2005", "RefUrl": "/notes/849981"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "874471", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Additions for installing or activating ECC-DIMP on ECC 6.0", "RefUrl": "/notes/874471 "}, {"RefNumber": "872197", "RefComponent": "IS-A", "RefTitle": "Additional info about upgrade to SAP ECC 6.0 w/ ECC-DIMP 6.0", "RefUrl": "/notes/872197 "}, {"RefNumber": "849981", "RefComponent": "IS-A-LMN", "RefTitle": "DIMP: Upgrade to ERP 2005", "RefUrl": "/notes/849981 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ECC-DIMP", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "ECC-DIMP", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "ECC-DIMP", "From": "603", "To": "603", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "ECC-DIMP 600", "SupportPackage": "SAPK-60012INECCDIMP", "URL": "/supportpackage/SAPK-60012INECCDIMP"}, {"SoftwareComponentVersion": "ECC-DIMP 602", "SupportPackage": "SAPK-60202INECCDIMP", "URL": "/supportpackage/SAPK-60202INECCDIMP"}, {"SoftwareComponentVersion": "ECC-DIMP 603", "SupportPackage": "SAPK-60301INECCDIMP", "URL": "/supportpackage/SAPK-60301INECCDIMP"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}