{"Request": {"Number": "1227207", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 397, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016551032017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001227207?language=E&token=E8A5FA5A03A61EFA508D4FEBA4B997D6"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001227207", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001227207/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1227207"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "30.05.2022"}, "SAPComponentKey": {"_label": "Component", "value": "PSM-FM-UP-FI"}, "SAPComponentKeyText": {"_label": "Component", "value": "FI Integration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Public Sector Management", "value": "PSM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PSM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Funds Management", "value": "PSM-FM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PSM-FM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Actual Update and Commitment Update", "value": "PSM-FM-UP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PSM-FM-UP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "FI Integration", "value": "PSM-FM-UP-FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PSM-FM-UP-FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1227207 - Tax update or profit and loss adjustment in FM"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note describes the prerequisites that are required to correctly determine the Funds Management (FM) account assignments for tax lines or for profit and loss adjustment if document splitting is active in financial accounting.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Value-added tax, follow-up costs, minor differences, distribution according to expenses</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You use Funds Management and document splitting in the general ledger.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Profit and loss adjustment</strong></p>\r\n<p>If you use document splitting in the general ledger, financial accounting no longer supports the report SAPF181 (\"Profit and Loss Adjustment\") because FI already distributes the cash discount paid, the cash discount received, the lost cash discount, exchange rate differences, and the backdated tax calculation during document posting.<br /><br />If you want to distribute these postings to the relevant FM account assignments, you must adjust this in Customizing for document splitting. To do this, you must define the FM account assignments that you used as document splitting characteristics. Please note: All used FM account assignments are defined as a document splitting characteristic of General Ledger Accounting. Note that this information is also passed on in the tax items. This means that in the view V_FAGL_SPLIT_FL2, the \"Assign to Tax\" flag is on (default setting).</p>\r\n<p>If you use the functional area, cost of sales accounting in particular therefore cannot be used, because this requires the functional area to be used as a document splitting characteristic of CO. In addition, we recommend that you set at least the characteristic KNBELNR because this is the prerequisite for the processes mentioned above to run without errors after a reassignment. You can do this in Customizing for financial accounting by choosing:</p>\r\n<ul>\r\n<li>Financial Accounting (New)  General Ledger Accounting (New) - Business Transactions -&gt; Document Splitting -&gt; Define Document Splitting Characteristics for General Ledger Accounting</li>\r\n</ul>\r\n<p><br />As of ERP Release 6.0 Enhancement Package 4, you can access the characteristic KNBELNR by choosing:</p>\r\n<ul>\r\n<li>Financial Accounting (New)  General Ledger Accounting (New) - Business Transactions -&gt; Document Splitting -&gt; Define Technical Document Splitting Characteristics</li>\r\n</ul>\r\n<p><br />Remarks:</p>\r\n<ul>\r\n<ul>\r\n<li>Note that the commitment item (FIPOS) document splitting characteristic always performs the splitting for the funds center (FISTL). Therefore, you do not have to set the funds center separately.</li>\r\n<li>The commitment item (FIPOS) document splitting characteristic automatically activates overwrite mode in the document breakdown. In this case, all of the account assignments to be split are projected from the G/L account lines to the open line items. This means that non-FM account assignments such as the profit center are also overwritten if appropriate.</li>\r\n<li>You can activate the functional area as a document splitting characteristic in the general ledger only as of ERP 6.0 Enhancement Package 4. Before this, it is available only as a document splitting characteristic in controlling. If you use the functional area, along with defining it as a document splitting characteristic in controlling, you must also set the indicator \"Assign to Tax\" in the view V_FAGL_SPLIT_FL2 (transaction SM30) for this functional area and for all document splitting characteristics in the general ledger.</li>\r\n<li>You do not have to perform an additional document split for profit and loss adjustment or for the tax update in FM after VOBELNR. This is required only for the online payment update for FM. For more information about this, see the documentation for the online payment update (Knowledge Warehouse, IMG documentation, RKT documents).</li>\r\n<li>To ensure that FM is updated correctly, in the general ledger, you do not have to also update the fields that are set as document splitting characteristics. The document volume is increased by the line item tables and the totals record tables from the general ledger only if you also perform the affected account assignments in the general ledger. Only the document volume of the table FAGL_SPLINFO, which contains the splitting information for the documents, increases as the number of document splitting characteristics increases.</li>\r\n</ul>\r\n</ul>\r\n<p><strong>Tax update in FM</strong></p>\r\n<p>The document splitting characteristics are also a prerequisite for ensuring the tax update in FM in accordance with Customizing for FM (transaction OFUP). This applies to all methods in the FM tax update (net, gross, and separate). Make sure that you do not use the account for tax transfer as a cost element, since in this case the tax item is not split by document splitting. As a result, the tax cannot be calculated in FM in accordance with Customizing (transaction OFUP) if document splitting is active. In this case, you must derive the funds center of the tax item in FMDERIVE. The account assignment of the tax items is applied in FM as entered or derived.</p>\r\n<p><br />Alternative:<br />If you do <span style=\"text-decoration: underline;\">not</span> require distribution according to expenses for the follow-up costs described in the section above (profit and loss adjustment) or backdated tax calculation and you do not want to set document splitting as described above, you still have the option to ensure that the tax update in FM is correct. Implement the method FMRI_BADI_10 for the BAdI FMRI (see Note 1226707) to ensure that the system performs the tax distribution within FM and not in document splitting.<br /><br />Remarks:</p>\r\n<ul>\r\n<ul>\r\n<li>The system cannot process backdated tax calculations. This is possible only using FI document splitting.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The system cannot perform tax splitting with a jurisdiction code. This is possible only using FI document splitting.</li>\r\n</ul>\r\n</ul>\r\n<p><br />Note that you can use the BAdI implementation mentioned above only to influence the distribution of tax lines and not to influence the profit and loss adjustment.<br /><br /></p>\r\n<p><strong>Implementing FM retroactively</strong></p>\r\n<p>You have the various options for implementing FM retroactively:</p>\r\n<ul>\r\n<li>If you want to implement FM, you must set all of the required document splitting characteristics in Customizing for document splitting from the start.</li>\r\n</ul>\r\n<ul>\r\n<li>In ERP, you can alternatively use migration scenario 6 together with the FM implementation for the subsequent implementation of document splitting. The subsequent implementation of document splitting is currently not released in S/4HANA for customers who use Public Sector Management. In this case, contact SAP.</li>\r\n</ul>\r\n<p><br />If document splitting is used live already without the relevant document splitting characteristics, you must set up a suitable project to convert the data.</p>\r\n<p><strong>Simplified update of tax update or of profit and loss adjustment in FM</strong></p>\r\n<p>If you use document splitting in FI but do not want to adjust the profit and loss or distribute the tax according to cause in FM, you can update these items directly in fixed FM account assignments without distribution. The system then uses the derivation strategy in FM (FMDERIVE) to determine the account assignments for these. For the tax update, you must implement the method FMRI_BADI_10 for the BAdI FMRI as described in section 2.</p>\r\n<p><strong>Invoices posted net cash discount</strong></p>\r\n<p>Note that it is impossible to use the <span style=\"font-size: 16px;\">'Adjust Tax Update in GL to FM' function (transaction OFUP)</span> together with invoices posted net cash discount. The described indicator must not be set here.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D023145)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D023145)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001227207/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001227207/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001227207/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001227207/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001227207/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001227207/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001227207/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001227207/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001227207/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1595174", "RefComponent": "PSM-FM-UP-FI", "RefTitle": "FAGL_CONF100 when document splitting is activated for new c.", "RefUrl": "/notes/1595174"}, {"RefNumber": "1226707", "RefComponent": "PSM-FM-UP-FI", "RefTitle": "Resource-related distribution of tax items with new FI", "RefUrl": "/notes/1226707"}, {"RefNumber": "1030497", "RefComponent": "PSM-FM", "RefTitle": "SAP ERP 6.0: Public sector scenarios in new general ledger", "RefUrl": "/notes/1030497"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2499524", "RefComponent": "PSM-FM-UP-FI", "RefTitle": "Error FI606 during FI posting", "RefUrl": "/notes/2499524 "}, {"RefNumber": "2988545", "RefComponent": "FI-GL", "RefTitle": "Subsequent implementation of document splitting: Deactivation of check of whether SAP Public Sector Management is used", "RefUrl": "/notes/2988545 "}, {"RefNumber": "1595174", "RefComponent": "PSM-FM-UP-FI", "RefTitle": "FAGL_CONF100 when document splitting is activated for new c.", "RefUrl": "/notes/1595174 "}, {"RefNumber": "1226707", "RefComponent": "PSM-FM-UP-FI", "RefTitle": "Resource-related distribution of tax items with new FI", "RefUrl": "/notes/1226707 "}, {"RefNumber": "1030497", "RefComponent": "PSM-FM", "RefTitle": "SAP ERP 6.0: Public sector scenarios in new general ledger", "RefUrl": "/notes/1030497 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-PS", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "EA-PS", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "EA-PS", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "EA-PS", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "EA-PS", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "EA-PS", "From": "616", "To": "616", "Subsequent": ""}, {"SoftwareComponent": "EA-PS", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "EA-PS", "From": "618", "To": "618", "Subsequent": ""}, {"SoftwareComponent": "EA-PS", "From": "801", "To": "801", "Subsequent": ""}, {"SoftwareComponent": "EA-PS", "From": "802", "To": "802", "Subsequent": ""}, {"SoftwareComponent": "EA-PS", "From": "803", "To": "803", "Subsequent": ""}, {"SoftwareComponent": "EA-PS", "From": "804", "To": "804", "Subsequent": ""}, {"SoftwareComponent": "EA-PS", "From": "805", "To": "805", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}