{"Request": {"Number": "902000", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1670, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015992272017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000902000?language=E&token=9AE72ED84EE84608458CC8762301C7E7"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000902000", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000902000/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "902000"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.10.2020"}, "SAPComponentKey": {"_label": "Component", "value": "PA-BC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Authorization and HCM Basis"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Personnel Management", "value": "PA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Authorization and HCM Basis", "value": "PA-BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PA-BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "902000 - Analyzing HR authorizations"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You want to check your settings in the HR authorizations area.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RH<PERSON>SU00, R<PERSON>BASU01, RHBAUS02,<br />RHUSERRELATIONS,<br />HRAUTH,<br />PA Time Logic,<br />structural authorizations.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The following Basis Support Package provides a new analysis workbench, which you can call using transaction HRAUTH.<br /><br />The actual functions are contained in the SAP_BASIS software component. The changes in SAP_HR add a comment only to the old report RHUSERRELATIONS.<br /><br />More precise documentation is available in the transaction documentation.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-BMT-OM (Organizational Management)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D002108)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D035111)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000902000/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000902000/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000902000/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000902000/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000902000/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000902000/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000902000/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000902000/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000902000/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "PA_Time_Logic_EN.pdf", "FileSize": "67", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000675012005&iv_version=0003&iv_guid=20ED47A99AADD74AA89004E94ED4D662"}, {"FileName": "Authorizations_in_mySAP_HR_46C.pdf", "FileSize": "819", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000675012005&iv_version=0003&iv_guid=40BB820FB074E64E9BA1D5C88E8E7584"}, {"FileName": "Context_Sensitive_Authorization.pdf", "FileSize": "76", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000675012005&iv_version=0003&iv_guid=EA163E9EA8FA0045B34752B64D323F65"}, {"FileName": "Berechtigungen in mySAP HR 46c.pdf", "FileSize": "823", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000675012005&iv_version=0003&iv_guid=00109B36D6A21EDB85F15CB1760400E4"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2153540", "RefComponent": "PA-BC", "RefTitle": "BAdI HRPAD00AUTH_CHECK: Use of the delegation principle", "RefUrl": "/notes/2153540"}, {"RefNumber": "2054808", "RefComponent": "PA-BC", "RefTitle": "HR authorizations: Collective SAP Note with questions and answers", "RefUrl": "/notes/2054808"}, {"RefNumber": "1709879", "RefComponent": "PA-PA-XX", "RefTitle": "HR authoriztn check: Symmetric double verification principle", "RefUrl": "/notes/1709879"}, {"RefNumber": "1690968", "RefComponent": "PA-BC", "RefTitle": "Structural authorization: Scheduling of reports RHBAUSnn", "RefUrl": "/notes/1690968"}, {"RefNumber": "1261193", "RefComponent": "BC-SEC-AUT", "RefTitle": "Composite SAP Note : HCM Authorizations Documentation", "RefUrl": "/notes/1261193"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1938122", "RefComponent": "BC-BMT-WFM", "RefTitle": "Error message \"Task does not exist - 5W304\" in transaction PFTC", "RefUrl": "/notes/1938122 "}, {"RefNumber": "2153356", "RefComponent": "BC-BMT-WFM", "RefTitle": "Workflow task is missing from the system (How to transport workflow task)", "RefUrl": "/notes/2153356 "}, {"RefNumber": "2598362", "RefComponent": "PA-BC", "RefTitle": "HCM supplement to SAP Note 2590321", "RefUrl": "/notes/2598362 "}, {"RefNumber": "1261193", "RefComponent": "BC-SEC-AUT", "RefTitle": "Composite SAP Note : HCM Authorizations Documentation", "RefUrl": "/notes/1261193 "}, {"RefNumber": "1709879", "RefComponent": "PA-PA-XX", "RefTitle": "HR authoriztn check: Symmetric double verification principle", "RefUrl": "/notes/1709879 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "620", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "710", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47073", "URL": "/supportpackage/SAPKE47073"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50039", "URL": "/supportpackage/SAPKE50039"}, {"SoftwareComponentVersion": "SAP_HR 600", "SupportPackage": "SAPKE60022", "URL": "/supportpackage/SAPKE60022"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62063", "URL": "/supportpackage/SAPKB62063"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64022", "URL": "/supportpackage/SAPKB64022"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70014", "URL": "/supportpackage/SAPKB70014"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}