{"Request": {"Number": "1104030", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1105, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016393742017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001104030?language=E&token=C99AE969F8E0050644A0A895B4BBFFAB"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001104030", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001104030/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1104030"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.10.2015"}, "SAPComponentKey": {"_label": "Component", "value": "BW-WHM-DOC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Documentation"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Warehouse Management", "value": "BW-WHM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Documentation", "value": "BW-WHM-DOC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DOC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1104030 - SAPBWNews BW 7.10 ABAP SP 05"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note concerns Support Package 05 for BI Release 7.10, which is part of NetWeaver 7.10.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAPBWNEWS, Support Packages for 7.10, BW 7.10, BI 7.10, BW Patches, BI, BI 7.10, SAPBINEWS</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This note contains the SAPBINews for Support Package 05 for SAP NetWeaver 7.10. This note lists all notes that describe the corrections or enhancements contained in Support Package 05.<br />This note will be updated when other notes are added. <br /><br />The information is divided into the following areas:</p>\r\n<ul>\r\n<li><strong>Manual actions that may be necessary:</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Factors you must take into account when you import the Support Package</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Errors that may occur after you import the Support Package</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>General information:</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Errors corrected in this Support Package </li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Enhancements delivered with this Support Package</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>See the release and information notes.</li>\r\n</ul>\r\n</ul>\r\n<p><strong>Factors you must take into account when you import the Support Package:</strong><br /><strong>Errors that may occur after you import the Support Package:</strong></p>\r\n<p><br />- To date, no errors are known.</p>\r\n<p><strong>Errors corrected in this Support Package: </strong><br /><strong>Enhancements delivered with this Support Package:</strong></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-SYS (Basis System and Installation)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D031867)"}, {"Key": "Processor                                                                                           ", "Value": "I822646"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001104030/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001104030/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001104030/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001104030/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001104030/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001104030/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001104030/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001104030/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001104030/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "968273", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Cache with flat files: <PERSON><PERSON> is not caught", "RefUrl": "/notes/968273"}, {"RefNumber": "1252162", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "ByD 710: Korrektur Übersetzungen von Broadcast-Einstellung", "RefUrl": "/notes/1252162"}, {"RefNumber": "1137084", "RefComponent": "BW-BEX-OT", "RefTitle": "RSTT: Processing not adjusted correctly to new TAREA concept", "RefUrl": "/notes/1137084"}, {"RefNumber": "1137081", "RefComponent": "BW-BEX-OT", "RefTitle": "RSTT 'NULL' object reference when exiting CATT wizard", "RefUrl": "/notes/1137081"}, {"RefNumber": "1125986", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17) Performance when you are editing transformations", "RefUrl": "/notes/1125986"}, {"RefNumber": "1125971", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "No BIA monitor authorization check for execution of actions", "RefUrl": "/notes/1125971"}, {"RefNumber": "1125965", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Amount field or quantity field of type D34D", "RefUrl": "/notes/1125965"}, {"RefNumber": "1125890", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "F4 mode A supported too often", "RefUrl": "/notes/1125890"}, {"RefNumber": "1125657", "RefComponent": "BW-BEX-OT-DBIF-CON", "RefTitle": "Statement too large during compression: IN list too long", "RefUrl": "/notes/1125657"}, {"RefNumber": "1125647", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN A 154 with UNIID characteristics", "RefUrl": "/notes/1125647"}, {"RefNumber": "1125587", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "Corr.: No structure names in dropdown during PSA selection", "RefUrl": "/notes/1125587"}, {"RefNumber": "1125475", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P17:SDL:Dump in method MAP_LOADING_TO_STATMANTYPE", "RefUrl": "/notes/1125475"}, {"RefNumber": "1125351", "RefComponent": "BW-WHM-MTD-INST", "RefTitle": "Inactive SAP objects are not reactivated", "RefUrl": "/notes/1125351"}, {"RefNumber": "1125235", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: Fields sorted incorrectly when you load files", "RefUrl": "/notes/1125235"}, {"RefNumber": "1125231", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17) Dump when you activate transformations", "RefUrl": "/notes/1125231"}, {"RefNumber": "1125037", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "OLAP cache: Selections are processed incorrectly", "RefUrl": "/notes/1125037"}, {"RefNumber": "1125025", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P17:PSA:DSO:ODSR missing in PSA process for write-opt. DSO", "RefUrl": "/notes/1125025"}, {"RefNumber": "1124972", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI7.0(SP17) Logging activation of open hub dest. improved", "RefUrl": "/notes/1124972"}, {"RefNumber": "1124868", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1124868"}, {"RefNumber": "1124746", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.1SPS05 Fehler bei Aktivierung Transformation", "RefUrl": "/notes/1124746"}, {"RefNumber": "1124681", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Hierarchy not directly expanded in F4 help", "RefUrl": "/notes/1124681"}, {"RefNumber": "1124620", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "Currency/unit fields are not transferred automatically", "RefUrl": "/notes/1124620"}, {"RefNumber": "1124225", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Different problems with single rule test", "RefUrl": "/notes/1124225"}, {"RefNumber": "1124217", "RefComponent": "BW-BEX-OT-OLAP-CUR", "RefTitle": "Incorrect display of unit of measure during qty conversion", "RefUrl": "/notes/1124217"}, {"RefNumber": "1124059", "RefComponent": "BW-WHM-DST", "RefTitle": "P17:BI_DELETE_OLD_MSG_PARM_DTPTEMP scheduled several times", "RefUrl": "/notes/1124059"}, {"RefNumber": "1124055", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Corrections for master data provider (new)", "RefUrl": "/notes/1124055"}, {"RefNumber": "1123700", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17) Filling time-reference characteristic w/ routine", "RefUrl": "/notes/1123700"}, {"RefNumber": "1123633", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination: Program SAPLRRI2 and form FEHLER_INIT-01-", "RefUrl": "/notes/1123633"}, {"RefNumber": "1123516", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Variables hidden when you refresh", "RefUrl": "/notes/1123516"}, {"RefNumber": "1123381", "RefComponent": "BW-BEX-OT", "RefTitle": "Transac LISTCUBE: Query on aggregate does not work correctly", "RefUrl": "/notes/1123381"}, {"RefNumber": "1123379", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Workbooks are not displayed in query WHERE-USED list", "RefUrl": "/notes/1123379"}, {"RefNumber": "1123345", "RefComponent": "BW-WHM-DST", "RefTitle": "P17:DTP: Additional logs during DTP status transition", "RefUrl": "/notes/1123345"}, {"RefNumber": "1123324", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: Incorrect display after object version switched", "RefUrl": "/notes/1123324"}, {"RefNumber": "1122958", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "RSAR 479 when you activate formulas from BI Content", "RefUrl": "/notes/1122958"}, {"RefNumber": "1122855", "RefComponent": "BW-WHM-DST", "RefTitle": "P17:WO-DSO: Archiving of write-optimized DSOs", "RefUrl": "/notes/1122855"}, {"RefNumber": "1122853", "RefComponent": "BW-WHM-DST", "RefTitle": "P17: BDLS does not rearrange staus manager tables", "RefUrl": "/notes/1122853"}, {"RefNumber": "1122789", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: PC_ACTIVE does not write a log", "RefUrl": "/notes/1122789"}, {"RefNumber": "1122741", "RefComponent": "BW", "RefTitle": "Termination during request check of transactional InfoCubes", "RefUrl": "/notes/1122741"}, {"RefNumber": "1122654", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Change run monitor: Reset status", "RefUrl": "/notes/1122654"}, {"RefNumber": "1122596", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Term'tion in CL_RSR_RRK0_HIERARCHY & form _RESOLVE_NODES_05", "RefUrl": "/notes/1122596"}, {"RefNumber": "1122504", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Dump in Package-Based Master Data Deletion", "RefUrl": "/notes/1122504"}, {"RefNumber": "1122482", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17) Problems occur when you maintain units", "RefUrl": "/notes/1122482"}, {"RefNumber": "1122481", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0 (SP17) An error occurs when you are generating Content", "RefUrl": "/notes/1122481"}, {"RefNumber": "1122444", "RefComponent": "BW-WHM-DST", "RefTitle": "P17: Incorrect error message when DataSource is inactive", "RefUrl": "/notes/1122444"}, {"RefNumber": "1122300", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0 (SP17) Transporting the deletion for a transformation", "RefUrl": "/notes/1122300"}, {"RefNumber": "1122289", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P17:PC: Dependencies of PC variants determined incorrectly", "RefUrl": "/notes/1122289"}, {"RefNumber": "1122284", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P17: Additional check during delta/repeat request", "RefUrl": "/notes/1122284"}, {"RefNumber": "1122281", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P17:SDL:Content:Find active IPackages if shadows are deleted", "RefUrl": "/notes/1122281"}, {"RefNumber": "1122259", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Correction: Destination *_DIALOG created with password error", "RefUrl": "/notes/1122259"}, {"RefNumber": "1122236", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RS_SYSTEM_SHUTDOWN: Warning in the case of open RDA requests", "RefUrl": "/notes/1122236"}, {"RefNumber": "1122214", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN A125 when generating query; <G_S_DATA_...> declared", "RefUrl": "/notes/1122214"}, {"RefNumber": "1122186", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P17:Deleting cont. completely continues even if error occurs", "RefUrl": "/notes/1122186"}, {"RefNumber": "1122185", "RefComponent": "BW-WHM-AWB", "RefTitle": "P17:DWWB: Searching for InfoPackages is slow", "RefUrl": "/notes/1122185"}, {"RefNumber": "1122155", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Problems with virtual time hierarchies and date 29.2", "RefUrl": "/notes/1122155"}, {"RefNumber": "1122124", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Termination in SAPLRRI2 and form FAC_VAR_IN_VREP_INSERT-1-", "RefUrl": "/notes/1122124"}, {"RefNumber": "1122080", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0 (SP17) Dump when you save a transformation (Content)", "RefUrl": "/notes/1122080"}, {"RefNumber": "1122063", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination: Inconsistent input param: I_KHANDLE, value 0000", "RefUrl": "/notes/1122063"}, {"RefNumber": "1121993", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Analysis auth's: Performance optimization for special situ's", "RefUrl": "/notes/1121993"}, {"RefNumber": "1121766", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination in SAPLRRI2 and form REP_ASSIGN_INITIAL_OPT-01-", "RefUrl": "/notes/1121766"}, {"RefNumber": "1121557", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "No text for variants in 3.X Analyzer variable screen F4", "RefUrl": "/notes/1121557"}, {"RefNumber": "1121382", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 BRAIN in SAPLRRI2; CELL_FUELLEN_FEMZ-02-", "RefUrl": "/notes/1121382"}, {"RefNumber": "1121357", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI 7.0 (SP17) Data not in external format file destination", "RefUrl": "/notes/1121357"}, {"RefNumber": "1121319", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error during transformation of new images or delete images", "RefUrl": "/notes/1121319"}, {"RefNumber": "1121223", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P17:SDL:RSSM_SHIPDVERS_CLEANUP finds incorrect shadow IPak", "RefUrl": "/notes/1121223"}, {"RefNumber": "1121222", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "P17:MON: Dump when setting QM status for an IDoc request", "RefUrl": "/notes/1121222"}, {"RefNumber": "1121202", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning functions: Distribution with keys", "RefUrl": "/notes/1121202"}, {"RefNumber": "1121165", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error in InfoSet join", "RefUrl": "/notes/1121165"}, {"RefNumber": "1121140", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P17:PC:CR:BIA indicators in change run do not work", "RefUrl": "/notes/1121140"}, {"RefNumber": "1121138", "RefComponent": "BW-WHM-DST", "RefTitle": "P17:DEL: Deleting requests w/o P dim. entries from InfoCubes", "RefUrl": "/notes/1121138"}, {"RefNumber": "1120981", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "3.x DataSource: Attribute dependency during emulation", "RefUrl": "/notes/1120981"}, {"RefNumber": "1120942", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1120942"}, {"RefNumber": "1120747", "RefComponent": "BW-WHM-DST", "RefTitle": "P17: OutlookSoft: Only one open APO request permitted", "RefUrl": "/notes/1120747"}, {"RefNumber": "1120719", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0 (SP17) Problems occur during the transport", "RefUrl": "/notes/1120719"}, {"RefNumber": "1120639", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P17:PSA:CL:PC:Deleting only one type of requests from PSA/CL", "RefUrl": "/notes/1120639"}, {"RefNumber": "1120620", "RefComponent": "BW-BEX-OT", "RefTitle": "Error in delta pair with aggregate data in AGGR part", "RefUrl": "/notes/1120620"}, {"RefNumber": "1120618", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Terminated time change run does not release lock", "RefUrl": "/notes/1120618"}, {"RefNumber": "1120461", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Transporting a 3.x DataSource fails", "RefUrl": "/notes/1120461"}, {"RefNumber": "1120454", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P17:SDL: Content release and content time stamp changed", "RefUrl": "/notes/1120454"}, {"RefNumber": "1120453", "RefComponent": "BW-WHM-AWB", "RefTitle": "P17: Attribute change run in the DWWB (object list)", "RefUrl": "/notes/1120453"}, {"RefNumber": "1120449", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17) Performance prob. when you open transformations", "RefUrl": "/notes/1120449"}, {"RefNumber": "1120360", "RefComponent": "BW-WHM-AWB", "RefTitle": "P17:DWWB: Reading the InfoArea/appl. comp. hier. w/o enqueue", "RefUrl": "/notes/1120360"}, {"RefNumber": "1120327", "RefComponent": "BW-PLA-IP-PQ", "RefTitle": "X299 in SAPLRRK0; form SELDR_TO_CHECK-01-", "RefUrl": "/notes/1120327"}, {"RefNumber": "1120110", "RefComponent": "BW-WHM", "RefTitle": "P17: Text changes/incorrectly translated texts/messages", "RefUrl": "/notes/1120110"}, {"RefNumber": "1120078", "RefComponent": "BW-BEX-OT", "RefTitle": "Message performance slightly improved", "RefUrl": "/notes/1120078"}, {"RefNumber": "1120063", "RefComponent": "BW-BEX-OT", "RefTitle": "\"Do Not Suppress Messages/Warnings\" does not work", "RefUrl": "/notes/1120063"}, {"RefNumber": "1119944", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Archiving request:Production mode instead of simulation mode", "RefUrl": "/notes/1119944"}, {"RefNumber": "1119697", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "A125 BRAIN when checking or generating a query", "RefUrl": "/notes/1119697"}, {"RefNumber": "1119619", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination in program SAPLRRS<PERSON> and form EXIT_INVERS_FOR_F", "RefUrl": "/notes/1119619"}, {"RefNumber": "1119507", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17) \"RequestID\" and \"PackageID\" in structure", "RefUrl": "/notes/1119507"}, {"RefNumber": "1119337", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Transport dialog box terminates after display -> change", "RefUrl": "/notes/1119337"}, {"RefNumber": "1119155", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP 17) Unrequired warnings are issued", "RefUrl": "/notes/1119155"}, {"RefNumber": "1119131", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "CX_SY_DYNAMIC_OSQL_SYNTAX in the new OLAP cache", "RefUrl": "/notes/1119131"}, {"RefNumber": "1119095", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Runtime object not regenerated", "RefUrl": "/notes/1119095"}, {"RefNumber": "1119076", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "RRI: Adjusting name after transporting transformations", "RefUrl": "/notes/1119076"}, {"RefNumber": "1119000", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "COMMIT occurs when you execute planning functions", "RefUrl": "/notes/1119000"}, {"RefNumber": "1118983", "RefComponent": "BW", "RefTitle": "VORLAGE: BI 7.0 (SP17) / BI 7.1 SPS05", "RefUrl": "/notes/1118983"}, {"RefNumber": "1118725", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "F4 help cannot be displyed for referencing characteristics", "RefUrl": "/notes/1118725"}, {"RefNumber": "1118662", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17) InfoSource deletion; transformation not deleted", "RefUrl": "/notes/1118662"}, {"RefNumber": "1118585", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "RSRV, Hierarchie: Falsche Fehlerpriorität", "RefUrl": "/notes/1118585"}, {"RefNumber": "1118426", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Problem: Replacing variable w/ variable (time-dep. attrib.)", "RefUrl": "/notes/1118426"}, {"RefNumber": "1118423", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Replacing text variables when source value is initial (#)", "RefUrl": "/notes/1118423"}, {"RefNumber": "1118163", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Process chains in the BI HC framework", "RefUrl": "/notes/1118163"}, {"RefNumber": "1118037", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Restoring 3.x DataSource terminates with RSDS 135", "RefUrl": "/notes/1118037"}, {"RefNumber": "1117949", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "THJ: Incorrect elimination of internal business volume", "RefUrl": "/notes/1117949"}, {"RefNumber": "1117809", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: After-import termination with LOAD_PROGRAM_LOST", "RefUrl": "/notes/1117809"}, {"RefNumber": "1117761", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Termination in program SAPLRRI2 and form MOVE_VRNID-04-", "RefUrl": "/notes/1117761"}, {"RefNumber": "1117663", "RefComponent": "BW-BEX-OT-VC", "RefTitle": "VPROV time-dependent navigation attributes read incorrectly", "RefUrl": "/notes/1117663"}, {"RefNumber": "1117548", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Authorizations of componding parents not considered in F4", "RefUrl": "/notes/1117548"}, {"RefNumber": "1117536", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Message: unable to move ...  (<PERSON> 618)", "RefUrl": "/notes/1117536"}, {"RefNumber": "1117380", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Implement statistics event for exit variable/variable screen", "RefUrl": "/notes/1117380"}, {"RefNumber": "1117348", "RefComponent": "BW-PLA-IP", "RefTitle": "Subsequent correction to Note 1101187 (OLAP tunnel)", "RefUrl": "/notes/1117348"}, {"RefNumber": "1117313", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Job log:archiving run for data archiving process - empty msg", "RefUrl": "/notes/1117313"}, {"RefNumber": "1117281", "RefComponent": "BW-BEX-ET-RT", "RefTitle": "Restriction in Analyzer context menu", "RefUrl": "/notes/1117281"}, {"RefNumber": "1116933", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Characteristics with a Universal Unique Identifier", "RefUrl": "/notes/1116933"}, {"RefNumber": "1116853", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorr data for query w/ exception aggr and two structures", "RefUrl": "/notes/1116853"}, {"RefNumber": "1116846", "RefComponent": "BW-WHM-DST-UPD", "RefTitle": "BI7.0(SP17) Problems when you transport update rules", "RefUrl": "/notes/1116846"}, {"RefNumber": "1116813", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Optimizing time distribution for floating point key figures", "RefUrl": "/notes/1116813"}, {"RefNumber": "1116719", "RefComponent": "BW-WHM-DST-BAPI", "RefTitle": "BI7.0(SP17) Fehlermeldung BAPI BAPI_ISOURCE_TD_T_CREATE", "RefUrl": "/notes/1116719"}, {"RefNumber": "1116689", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Function R_TRIM does not work", "RefUrl": "/notes/1116689"}, {"RefNumber": "1116632", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Queries on InfoCubes that read all requests are slow", "RefUrl": "/notes/1116632"}, {"RefNumber": "1116485", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Unit not displayed for display attribute", "RefUrl": "/notes/1116485"}, {"RefNumber": "1116410", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17) Dump when editing transformations", "RefUrl": "/notes/1116410"}, {"RefNumber": "1116379", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Problems with the error handling when master data is read", "RefUrl": "/notes/1116379"}, {"RefNumber": "1116377", "RefComponent": "BW-WHM-DST-BAPI", "RefTitle": "Creating InfoSource using a BAPI: Dialog box triggers error", "RefUrl": "/notes/1116377"}, {"RefNumber": "1116269", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "New variants are not taken into account for buckets", "RefUrl": "/notes/1116269"}, {"RefNumber": "1116268", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Changing default value for MaxRows in 3.X variable screen", "RefUrl": "/notes/1116268"}, {"RefNumber": "1116261", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Termination in program SAPLR<PERSON><PERSON><PERSON> and form RRK_LIST_NOTIFY-01-", "RefUrl": "/notes/1116261"}, {"RefNumber": "1116250", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Hierarchy &1 is invalid for key date ********", "RefUrl": "/notes/1116250"}, {"RefNumber": "1116153", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17) Deleting orphaned transformations", "RefUrl": "/notes/1116153"}, {"RefNumber": "1116138", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "PSA maintenance - File download and total func. do not work", "RefUrl": "/notes/1116138"}, {"RefNumber": "1116105", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "RSRV:Wrong parameter/missing text for Multibyte-Analysis", "RefUrl": "/notes/1116105"}, {"RefNumber": "1116004", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17) Error in the HTML display of the transformation", "RefUrl": "/notes/1116004"}, {"RefNumber": "1115927", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Master record not displayed with value but as #", "RefUrl": "/notes/1115927"}, {"RefNumber": "1115923", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Check or transfer of empty formulas causes runtime error", "RefUrl": "/notes/1115923"}, {"RefNumber": "1115910", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning functions: Performance improvements", "RefUrl": "/notes/1115910"}, {"RefNumber": "1115902", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Correction UDC: Connection defective after restoring", "RefUrl": "/notes/1115902"}, {"RefNumber": "1115790", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "RSDMD001 during the master data activation", "RefUrl": "/notes/1115790"}, {"RefNumber": "1115751", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI7.0(SP17) Table not deleted when you delete destination", "RefUrl": "/notes/1115751"}, {"RefNumber": "1115665", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Description of key figures truncated when replacing", "RefUrl": "/notes/1115665"}, {"RefNumber": "1115574", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI 7.0 (SP 17) Dump with incorrect template", "RefUrl": "/notes/1115574"}, {"RefNumber": "1115515", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Key figure of type TIMS (time) displayed as date", "RefUrl": "/notes/1115515"}, {"RefNumber": "1115356", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA indexing terminates with an SQL error", "RefUrl": "/notes/1115356"}, {"RefNumber": "1115355", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No texts for 0UNIT or 0CURRENCY in the query", "RefUrl": "/notes/1115355"}, {"RefNumber": "1115241", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: Error when Unicode file uploaded from app server", "RefUrl": "/notes/1115241"}, {"RefNumber": "1115194", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Termination CELL_FUELLEN_FEMZ-02 during query generation", "RefUrl": "/notes/1115194"}, {"RefNumber": "1115168", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI 7.0 (SP17) Destination is incorrectly activated", "RefUrl": "/notes/1115168"}, {"RefNumber": "1115140", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Hierarchy authorization does not work", "RefUrl": "/notes/1115140"}, {"RefNumber": "1115031", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination CL_RSDRC_MULTIPROV (form _SELDR_MULTI_TO_PART)", "RefUrl": "/notes/1115031"}, {"RefNumber": "1115010", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17): Migration of update rule incorrect", "RefUrl": "/notes/1115010"}, {"RefNumber": "1115004", "RefComponent": "BW-WHM-DBA", "RefTitle": "BI7.0(SP17) DataStore/InfoCube cannot be activated", "RefUrl": "/notes/1115004"}, {"RefNumber": "1114773", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "900 index on F fact tab. is BITMAP but cube is transactional", "RefUrl": "/notes/1114773"}, {"RefNumber": "1114659", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "P17:MON:No DataSource preselection when you select data req.", "RefUrl": "/notes/1114659"}, {"RefNumber": "1114658", "RefComponent": "BW-WHM-DST", "RefTitle": "P17:BATCH: Restarting child processes in child process", "RefUrl": "/notes/1114658"}, {"RefNumber": "1114657", "RefComponent": "BW-WHM-DST", "RefTitle": "P17: Select for /BI0/SREQUID fills single record buffer", "RefUrl": "/notes/1114657"}, {"RefNumber": "1114483", "RefComponent": "BW-WHM-DST", "RefTitle": "P17: Additional sequence check of a delta/init request", "RefUrl": "/notes/1114483"}, {"RefNumber": "1114475", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "No F4 help for special characters for CSV converter", "RefUrl": "/notes/1114475"}, {"RefNumber": "1114426", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Termination of migration due to missing auth.", "RefUrl": "/notes/1114426"}, {"RefNumber": "1114409", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "ASSERTION_FAILED in CL_RSTRAN_RSFO=>COPY_ON_DATABASE", "RefUrl": "/notes/1114409"}, {"RefNumber": "1114299", "RefComponent": "BW-WHM-DST", "RefTitle": "P17:DSO:STATMANL: Activating several request; performance", "RefUrl": "/notes/1114299"}, {"RefNumber": "1114164", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "Termination in the cache due to insufficient shared memory", "RefUrl": "/notes/1114164"}, {"RefNumber": "1114163", "RefComponent": "BW-BEX-OT", "RefTitle": "RSTT: Authorization & message processing in the trace tool", "RefUrl": "/notes/1114163"}, {"RefNumber": "1114104", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA_RESET terminates if there is a red request", "RefUrl": "/notes/1114104"}, {"RefNumber": "1114097", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "P17:RDA: Check if real-time request is the last request", "RefUrl": "/notes/1114097"}, {"RefNumber": "1114084", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA index with compunded InfoObject without text", "RefUrl": "/notes/1114084"}, {"RefNumber": "1114047", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Compounding and text variable, dynamic filter", "RefUrl": "/notes/1114047"}, {"RefNumber": "1113931", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17) Transformations are not transferred correctly", "RefUrl": "/notes/1113931"}, {"RefNumber": "1113907", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Performance hierarchy node input help", "RefUrl": "/notes/1113907"}, {"RefNumber": "1113798", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 BRAIN in SAPLRRK0; form LRECH_AGGR_ELSE-02-", "RefUrl": "/notes/1113798"}, {"RefNumber": "1113718", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17): Transport: No authorization for object", "RefUrl": "/notes/1113718"}, {"RefNumber": "1113716", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17) Fehlermeldung: Es ist eine Ausnahme aufgetreten", "RefUrl": "/notes/1113716"}, {"RefNumber": "1113690", "RefComponent": "BW-WHM", "RefTitle": "P17: Syntax error in report RSSM_PREPARE_SDLINIT_TABLES_40", "RefUrl": "/notes/1113690"}, {"RefNumber": "1113610", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "P17:SDL:PC:RDA:Daemon runs: Do not start delta/init IPackage", "RefUrl": "/notes/1113610"}, {"RefNumber": "1113609", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P17:SDL: Deliver/activate DB connect source system", "RefUrl": "/notes/1113609"}, {"RefNumber": "1113608", "RefComponent": "BW-WHM-DST", "RefTitle": "P17:REQARCH:Archiving prog. takes too long to check requests", "RefUrl": "/notes/1113608"}, {"RefNumber": "1113607", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1113607"}, {"RefNumber": "1113606", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "P17:MON:Dump \"connection closed (no data)\" in the monitor", "RefUrl": "/notes/1113606"}, {"RefNumber": "1113605", "RefComponent": "BW-WHM-DST", "RefTitle": "P17:PSA:PSA process locks RSICCONT for too long; Sorting", "RefUrl": "/notes/1113605"}, {"RefNumber": "1113604", "RefComponent": "BW-WHM-DST", "RefTitle": "P17: Possible to delete updated requests", "RefUrl": "/notes/1113604"}, {"RefNumber": "1113603", "RefComponent": "BW-WHM-DST", "RefTitle": "P17:DSO:Postprocessing ODS - activating and updating", "RefUrl": "/notes/1113603"}, {"RefNumber": "1113602", "RefComponent": "BW-WHM-DST", "RefTitle": "P17:PSA:Slow performance during select on RSREQDONE", "RefUrl": "/notes/1113602"}, {"RefNumber": "1113581", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination in the form KFB_FUELLEN_BU of program SAPLRRI2", "RefUrl": "/notes/1113581"}, {"RefNumber": "1113439", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Preview of raw data (in the InfoPackage)", "RefUrl": "/notes/1113439"}, {"RefNumber": "1113121", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Duplicated data when delta extracted frm write-opt DataStore", "RefUrl": "/notes/1113121"}, {"RefNumber": "1112918", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "There are too many nodes selected when entered directly", "RefUrl": "/notes/1112918"}, {"RefNumber": "1112792", "RefComponent": "BW-BEX-OT", "RefTitle": "RSTT: Invalid processing mode in the package maintenance", "RefUrl": "/notes/1112792"}, {"RefNumber": "1112742", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error when you are maintaining transformations", "RefUrl": "/notes/1112742"}, {"RefNumber": "1112519", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "Inaccuracies in OLAP cache", "RefUrl": "/notes/1112519"}, {"RefNumber": "1112338", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Temporal hierarchy join in structure elements", "RefUrl": "/notes/1112338"}, {"RefNumber": "1112273", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Incorr. authorization for time-dependent hierarchy structure", "RefUrl": "/notes/1112273"}, {"RefNumber": "1112165", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1112165"}, {"RefNumber": "1112109", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "Transfer structure is not selected during Content transfer", "RefUrl": "/notes/1112109"}, {"RefNumber": "1111733", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "x_message AUTH_TO_SETOBJECT:CX_RSMDS_INPUT_INVALID", "RefUrl": "/notes/1111733"}, {"RefNumber": "1111539", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Log is missing after replication", "RefUrl": "/notes/1111539"}, {"RefNumber": "1111386", "RefComponent": "BW-WHM-MTD-INST", "RefTitle": "BI Content installation even though system cannot be changed", "RefUrl": "/notes/1111386"}, {"RefNumber": "1111256", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "BRAIN 627 after incorrect value changed to correct value", "RefUrl": "/notes/1111256"}, {"RefNumber": "1111229", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA daemon terminates after six hours", "RefUrl": "/notes/1111229"}, {"RefNumber": "1110997", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Correction for Note 1090490", "RefUrl": "/notes/1110997"}, {"RefNumber": "1110375", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "IOBJ_VALUE_NOT_VALID when you link to the Broadcaster", "RefUrl": "/notes/1110375"}, {"RefNumber": "1110324", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Irrelevant message after attempt to delete Master Data", "RefUrl": "/notes/1110324"}, {"RefNumber": "1109654", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "GETWA_NOT_ASSIGNED in class CL_RSMD_READ_MASTER_DATA_SPEC", "RefUrl": "/notes/1109654"}, {"RefNumber": "1109636", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1109636"}, {"RefNumber": "1109510", "RefComponent": "BW-WHM-MTD-CTS", "RefTitle": "Active objects are not displayed in the package", "RefUrl": "/notes/1109510"}, {"RefNumber": "1109475", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "BI 7.1 SPS05: UUID Transformation bricht ab", "RefUrl": "/notes/1109475"}, {"RefNumber": "1109352", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1109352"}, {"RefNumber": "1109219", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect replacement of a variable from a query result", "RefUrl": "/notes/1109219"}, {"RefNumber": "1108906", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Replication deletes content time stamp", "RefUrl": "/notes/1108906"}, {"RefNumber": "1108877", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1108877"}, {"RefNumber": "1108873", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Incorrect Oracle hint for InfoCube compression", "RefUrl": "/notes/1108873"}, {"RefNumber": "1108801", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Delete filter and process variable screen again", "RefUrl": "/notes/1108801"}, {"RefNumber": "1108758", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Missing charac. value in filter when request status not 0", "RefUrl": "/notes/1108758"}, {"RefNumber": "1108347", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "OLAP cache GETWA_NOT_ASSIGNED in CL_RSR_CACHE_QUERY_CUBE_MEM", "RefUrl": "/notes/1108347"}, {"RefNumber": "1108116", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning functions: Various errors", "RefUrl": "/notes/1108116"}, {"RefNumber": "1107988", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA daemon sets the status of the running DTP request", "RefUrl": "/notes/1107988"}, {"RefNumber": "1107434", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "OLAP cache consultation: Long wait times due to locks", "RefUrl": "/notes/1107434"}, {"RefNumber": "1107369", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No data or incorr data with elimination of internal bus vol", "RefUrl": "/notes/1107369"}, {"RefNumber": "1107276", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "No API for reading the error messages", "RefUrl": "/notes/1107276"}, {"RefNumber": "1107072", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Improving the input help for 0INFOPROV", "RefUrl": "/notes/1107072"}, {"RefNumber": "1106801", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN 299 in CL_RSR_RRK0_HIERARCHY; SEL_TO_SELDR_EQSID-03-", "RefUrl": "/notes/1106801"}, {"RefNumber": "1106705", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Runtime error ASSERTION_FAILED for formulas", "RefUrl": "/notes/1106705"}, {"RefNumber": "1106692", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA daemon does not close open requests", "RefUrl": "/notes/1106692"}, {"RefNumber": "1106485", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Deleting master data; and aggregates", "RefUrl": "/notes/1106485"}, {"RefNumber": "1106363", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Repeat is not reported", "RefUrl": "/notes/1106363"}, {"RefNumber": "1106058", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Transformation end routine - ALL_FIELDS does not work", "RefUrl": "/notes/1106058"}, {"RefNumber": "1105595", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "No text for MemberSet value or bucket variable", "RefUrl": "/notes/1105595"}, {"RefNumber": "1105583", "RefComponent": "BW-BEX-OT", "RefTitle": "RSTT: Correct parameter display for reduced call stack", "RefUrl": "/notes/1105583"}, {"RefNumber": "1105498", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Termination: 'GET_SID-1-' in program SAPLRRS2", "RefUrl": "/notes/1105498"}, {"RefNumber": "1105495", "RefComponent": "BW-BEX-OT", "RefTitle": "Verbesserung Technische Information in RSRT", "RefUrl": "/notes/1105495"}, {"RefNumber": "1105334", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help: \"Max. statement length exceeded\"", "RefUrl": "/notes/1105334"}, {"RefNumber": "1105139", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "Filling the OLAP cache with \"contains pattern\" in BI 7.0", "RefUrl": "/notes/1105139"}, {"RefNumber": "1105096", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Transformation cannot be deleted due to transport system", "RefUrl": "/notes/1105096"}, {"RefNumber": "1105094", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Structure name error in RSDS_RANGE_TO_WHERE", "RefUrl": "/notes/1105094"}, {"RefNumber": "1105004", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Assigning navigation attributes to dimensions", "RefUrl": "/notes/1105004"}, {"RefNumber": "1104367", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "No success message when you successfully activate a DAP", "RefUrl": "/notes/1104367"}, {"RefNumber": "1087400", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RSRV check for technical info of queries", "RefUrl": "/notes/1087400"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1117281", "RefComponent": "BW-BEX-ET-RT", "RefTitle": "Restriction in Analyzer context menu", "RefUrl": "/notes/1117281 "}, {"RefNumber": "1113602", "RefComponent": "BW-WHM-DST", "RefTitle": "P17:PSA:Slow performance during select on RSREQDONE", "RefUrl": "/notes/1113602 "}, {"RefNumber": "1116138", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "PSA maintenance - File download and total func. do not work", "RefUrl": "/notes/1116138 "}, {"RefNumber": "1122289", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P17:PC: Dependencies of PC variants determined incorrectly", "RefUrl": "/notes/1122289 "}, {"RefNumber": "1115790", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "RSDMD001 during the master data activation", "RefUrl": "/notes/1115790 "}, {"RefNumber": "1125037", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "OLAP cache: Selections are processed incorrectly", "RefUrl": "/notes/1125037 "}, {"RefNumber": "1107072", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Improving the input help for 0INFOPROV", "RefUrl": "/notes/1107072 "}, {"RefNumber": "1106801", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN 299 in CL_RSR_RRK0_HIERARCHY; SEL_TO_SELDR_EQSID-03-", "RefUrl": "/notes/1106801 "}, {"RefNumber": "1122741", "RefComponent": "BW", "RefTitle": "Termination during request check of transactional InfoCubes", "RefUrl": "/notes/1122741 "}, {"RefNumber": "1124972", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI7.0(SP17) Logging activation of open hub dest. improved", "RefUrl": "/notes/1124972 "}, {"RefNumber": "1125890", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "F4 mode A supported too often", "RefUrl": "/notes/1125890 "}, {"RefNumber": "1120063", "RefComponent": "BW-BEX-OT", "RefTitle": "\"Do Not Suppress Messages/Warnings\" does not work", "RefUrl": "/notes/1120063 "}, {"RefNumber": "1252162", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "ByD 710: Korrektur Übersetzungen von Broadcast-Einstellung", "RefUrl": "/notes/1252162 "}, {"RefNumber": "1110324", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Irrelevant message after attempt to delete Master Data", "RefUrl": "/notes/1110324 "}, {"RefNumber": "1120327", "RefComponent": "BW-PLA-IP-PQ", "RefTitle": "X299 in SAPLRRK0; form SELDR_TO_CHECK-01-", "RefUrl": "/notes/1120327 "}, {"RefNumber": "1120078", "RefComponent": "BW-BEX-OT", "RefTitle": "Message performance slightly improved", "RefUrl": "/notes/1120078 "}, {"RefNumber": "1112519", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "Inaccuracies in OLAP cache", "RefUrl": "/notes/1112519 "}, {"RefNumber": "1113121", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Duplicated data when delta extracted frm write-opt DataStore", "RefUrl": "/notes/1113121 "}, {"RefNumber": "1105498", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Termination: 'GET_SID-1-' in program SAPLRRS2", "RefUrl": "/notes/1105498 "}, {"RefNumber": "1108116", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning functions: Various errors", "RefUrl": "/notes/1108116 "}, {"RefNumber": "1109510", "RefComponent": "BW-WHM-MTD-CTS", "RefTitle": "Active objects are not displayed in the package", "RefUrl": "/notes/1109510 "}, {"RefNumber": "1109654", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "GETWA_NOT_ASSIGNED in class CL_RSMD_READ_MASTER_DATA_SPEC", "RefUrl": "/notes/1109654 "}, {"RefNumber": "1122063", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination: Inconsistent input param: I_KHANDLE, value 0000", "RefUrl": "/notes/1122063 "}, {"RefNumber": "1115010", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17): Migration of update rule incorrect", "RefUrl": "/notes/1115010 "}, {"RefNumber": "1113718", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17): Transport: No authorization for object", "RefUrl": "/notes/1113718 "}, {"RefNumber": "1116153", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17) Deleting orphaned transformations", "RefUrl": "/notes/1116153 "}, {"RefNumber": "1115031", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination CL_RSDRC_MULTIPROV (form _SELDR_MULTI_TO_PART)", "RefUrl": "/notes/1115031 "}, {"RefNumber": "1105334", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help: \"Max. statement length exceeded\"", "RefUrl": "/notes/1105334 "}, {"RefNumber": "1122281", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P17:SDL:Content:Find active IPackages if shadows are deleted", "RefUrl": "/notes/1122281 "}, {"RefNumber": "1122186", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P17:Deleting cont. completely continues even if error occurs", "RefUrl": "/notes/1122186 "}, {"RefNumber": "1106363", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Repeat is not reported", "RefUrl": "/notes/1106363 "}, {"RefNumber": "1113606", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "P17:MON:Dump \"connection closed (no data)\" in the monitor", "RefUrl": "/notes/1113606 "}, {"RefNumber": "1114163", "RefComponent": "BW-BEX-OT", "RefTitle": "RSTT: Authorization & message processing in the trace tool", "RefUrl": "/notes/1114163 "}, {"RefNumber": "1137084", "RefComponent": "BW-BEX-OT", "RefTitle": "RSTT: Processing not adjusted correctly to new TAREA concept", "RefUrl": "/notes/1137084 "}, {"RefNumber": "1137081", "RefComponent": "BW-BEX-OT", "RefTitle": "RSTT 'NULL' object reference when exiting CATT wizard", "RefUrl": "/notes/1137081 "}, {"RefNumber": "1121202", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning functions: Distribution with keys", "RefUrl": "/notes/1121202 "}, {"RefNumber": "1122855", "RefComponent": "BW-WHM-DST", "RefTitle": "P17:WO-DSO: Archiving of write-optimized DSOs", "RefUrl": "/notes/1122855 "}, {"RefNumber": "1125231", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17) Dump when you activate transformations", "RefUrl": "/notes/1125231 "}, {"RefNumber": "1122504", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Dump in Package-Based Master Data Deletion", "RefUrl": "/notes/1122504 "}, {"RefNumber": "1125965", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Amount field or quantity field of type D34D", "RefUrl": "/notes/1125965 "}, {"RefNumber": "1087400", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RSRV check for technical info of queries", "RefUrl": "/notes/1087400 "}, {"RefNumber": "1117380", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Implement statistics event for exit variable/variable screen", "RefUrl": "/notes/1117380 "}, {"RefNumber": "1115923", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Check or transfer of empty formulas causes runtime error", "RefUrl": "/notes/1115923 "}, {"RefNumber": "1116105", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "RSRV:Wrong parameter/missing text for Multibyte-Analysis", "RefUrl": "/notes/1116105 "}, {"RefNumber": "1105139", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "Filling the OLAP cache with \"contains pattern\" in BI 7.0", "RefUrl": "/notes/1105139 "}, {"RefNumber": "1115004", "RefComponent": "BW-WHM-DBA", "RefTitle": "BI7.0(SP17) DataStore/InfoCube cannot be activated", "RefUrl": "/notes/1115004 "}, {"RefNumber": "1115910", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning functions: Performance improvements", "RefUrl": "/notes/1115910 "}, {"RefNumber": "1122853", "RefComponent": "BW-WHM-DST", "RefTitle": "P17: BDLS does not rearrange staus manager tables", "RefUrl": "/notes/1122853 "}, {"RefNumber": "1117809", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: After-import termination with LOAD_PROGRAM_LOST", "RefUrl": "/notes/1117809 "}, {"RefNumber": "1110997", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Correction for Note 1090490", "RefUrl": "/notes/1110997 "}, {"RefNumber": "1111256", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "BRAIN 627 after incorrect value changed to correct value", "RefUrl": "/notes/1111256 "}, {"RefNumber": "1104367", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "No success message when you successfully activate a DAP", "RefUrl": "/notes/1104367 "}, {"RefNumber": "1112792", "RefComponent": "BW-BEX-OT", "RefTitle": "RSTT: Invalid processing mode in the package maintenance", "RefUrl": "/notes/1112792 "}, {"RefNumber": "1114047", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Compounding and text variable, dynamic filter", "RefUrl": "/notes/1114047 "}, {"RefNumber": "1123700", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17) Filling time-reference characteristic w/ routine", "RefUrl": "/notes/1123700 "}, {"RefNumber": "1116379", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Problems with the error handling when master data is read", "RefUrl": "/notes/1116379 "}, {"RefNumber": "1122214", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN A125 when generating query; <G_S_DATA_...> declared", "RefUrl": "/notes/1122214 "}, {"RefNumber": "1124225", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Different problems with single rule test", "RefUrl": "/notes/1124225 "}, {"RefNumber": "1124055", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Corrections for master data provider (new)", "RefUrl": "/notes/1124055 "}, {"RefNumber": "1107369", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No data or incorr data with elimination of internal bus vol", "RefUrl": "/notes/1107369 "}, {"RefNumber": "1118163", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Process chains in the BI HC framework", "RefUrl": "/notes/1118163 "}, {"RefNumber": "1121993", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Analysis auth's: Performance optimization for special situ's", "RefUrl": "/notes/1121993 "}, {"RefNumber": "1117348", "RefComponent": "BW-PLA-IP", "RefTitle": "Subsequent correction to Note 1101187 (OLAP tunnel)", "RefUrl": "/notes/1117348 "}, {"RefNumber": "1121557", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "No text for variants in 3.X Analyzer variable screen F4", "RefUrl": "/notes/1121557 "}, {"RefNumber": "1116269", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "New variants are not taken into account for buckets", "RefUrl": "/notes/1116269 "}, {"RefNumber": "1117548", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Authorizations of componding parents not considered in F4", "RefUrl": "/notes/1117548 "}, {"RefNumber": "1115140", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Hierarchy authorization does not work", "RefUrl": "/notes/1115140 "}, {"RefNumber": "1106058", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Transformation end routine - ALL_FIELDS does not work", "RefUrl": "/notes/1106058 "}, {"RefNumber": "1120461", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Transporting a 3.x DataSource fails", "RefUrl": "/notes/1120461 "}, {"RefNumber": "1120620", "RefComponent": "BW-BEX-OT", "RefTitle": "Error in delta pair with aggregate data in AGGR part", "RefUrl": "/notes/1120620 "}, {"RefNumber": "1108758", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Missing charac. value in filter when request status not 0", "RefUrl": "/notes/1108758 "}, {"RefNumber": "968273", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Cache with flat files: <PERSON><PERSON> is not caught", "RefUrl": "/notes/968273 "}, {"RefNumber": "1115355", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No texts for 0UNIT or 0CURRENCY in the query", "RefUrl": "/notes/1115355 "}, {"RefNumber": "1113605", "RefComponent": "BW-WHM-DST", "RefTitle": "P17:PSA:PSA process locks RSICCONT for too long; Sorting", "RefUrl": "/notes/1113605 "}, {"RefNumber": "1124217", "RefComponent": "BW-BEX-OT-OLAP-CUR", "RefTitle": "Incorrect display of unit of measure during qty conversion", "RefUrl": "/notes/1124217 "}, {"RefNumber": "1113798", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 BRAIN in SAPLRRK0; form LRECH_AGGR_ELSE-02-", "RefUrl": "/notes/1113798 "}, {"RefNumber": "1114409", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "ASSERTION_FAILED in CL_RSTRAN_RSFO=>COPY_ON_DATABASE", "RefUrl": "/notes/1114409 "}, {"RefNumber": "1122789", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: PC_ACTIVE does not write a log", "RefUrl": "/notes/1122789 "}, {"RefNumber": "1125986", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17) Performance when you are editing transformations", "RefUrl": "/notes/1125986 "}, {"RefNumber": "1124681", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Hierarchy not directly expanded in F4 help", "RefUrl": "/notes/1124681 "}, {"RefNumber": "1105495", "RefComponent": "BW-BEX-OT", "RefTitle": "Verbesserung Technische Information in RSRT", "RefUrl": "/notes/1105495 "}, {"RefNumber": "1117663", "RefComponent": "BW-BEX-OT-VC", "RefTitle": "VPROV time-dependent navigation attributes read incorrectly", "RefUrl": "/notes/1117663 "}, {"RefNumber": "1113609", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P17:SDL: Deliver/activate DB connect source system", "RefUrl": "/notes/1113609 "}, {"RefNumber": "1112273", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Incorr. authorization for time-dependent hierarchy structure", "RefUrl": "/notes/1112273 "}, {"RefNumber": "1115194", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Termination CELL_FUELLEN_FEMZ-02 during query generation", "RefUrl": "/notes/1115194 "}, {"RefNumber": "1123379", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Workbooks are not displayed in query WHERE-USED list", "RefUrl": "/notes/1123379 "}, {"RefNumber": "1107276", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "No API for reading the error messages", "RefUrl": "/notes/1107276 "}, {"RefNumber": "1125657", "RefComponent": "BW-BEX-OT-DBIF-CON", "RefTitle": "Statement too large during compression: IN list too long", "RefUrl": "/notes/1125657 "}, {"RefNumber": "1122259", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Correction: Destination *_DIALOG created with password error", "RefUrl": "/notes/1122259 "}, {"RefNumber": "1113604", "RefComponent": "BW-WHM-DST", "RefTitle": "P17: Possible to delete updated requests", "RefUrl": "/notes/1113604 "}, {"RefNumber": "1116853", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorr data for query w/ exception aggr and two structures", "RefUrl": "/notes/1116853 "}, {"RefNumber": "1122482", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17) Problems occur when you maintain units", "RefUrl": "/notes/1122482 "}, {"RefNumber": "1117761", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Termination in program SAPLRRI2 and form MOVE_VRNID-04-", "RefUrl": "/notes/1117761 "}, {"RefNumber": "1115241", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: Error when Unicode file uploaded from app server", "RefUrl": "/notes/1115241 "}, {"RefNumber": "1119076", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "RRI: Adjusting name after transporting transformations", "RefUrl": "/notes/1119076 "}, {"RefNumber": "1123516", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Variables hidden when you refresh", "RefUrl": "/notes/1123516 "}, {"RefNumber": "1125235", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: Fields sorted incorrectly when you load files", "RefUrl": "/notes/1125235 "}, {"RefNumber": "1120981", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "3.x DataSource: Attribute dependency during emulation", "RefUrl": "/notes/1120981 "}, {"RefNumber": "1124620", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "Currency/unit fields are not transferred automatically", "RefUrl": "/notes/1124620 "}, {"RefNumber": "1122284", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P17: Additional check during delta/repeat request", "RefUrl": "/notes/1122284 "}, {"RefNumber": "1125025", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P17:PSA:DSO:ODSR missing in PSA process for write-opt. DSO", "RefUrl": "/notes/1125025 "}, {"RefNumber": "1125475", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P17:SDL:Dump in method MAP_LOADING_TO_STATMANTYPE", "RefUrl": "/notes/1125475 "}, {"RefNumber": "1125971", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "No BIA monitor authorization check for execution of actions", "RefUrl": "/notes/1125971 "}, {"RefNumber": "1119131", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "CX_SY_DYNAMIC_OSQL_SYNTAX in the new OLAP cache", "RefUrl": "/notes/1119131 "}, {"RefNumber": "1125647", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN A 154 with UNIID characteristics", "RefUrl": "/notes/1125647 "}, {"RefNumber": "1121382", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 BRAIN in SAPLRRI2; CELL_FUELLEN_FEMZ-02-", "RefUrl": "/notes/1121382 "}, {"RefNumber": "1125587", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "Corr.: No structure names in dropdown during PSA selection", "RefUrl": "/notes/1125587 "}, {"RefNumber": "1123381", "RefComponent": "BW-BEX-OT", "RefTitle": "Transac LISTCUBE: Query on aggregate does not work correctly", "RefUrl": "/notes/1123381 "}, {"RefNumber": "1122654", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Change run monitor: Reset status", "RefUrl": "/notes/1122654 "}, {"RefNumber": "1125351", "RefComponent": "BW-WHM-MTD-INST", "RefTitle": "Inactive SAP objects are not reactivated", "RefUrl": "/notes/1125351 "}, {"RefNumber": "1122596", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Term'tion in CL_RSR_RRK0_HIERARCHY & form _RESOLVE_NODES_05", "RefUrl": "/notes/1122596 "}, {"RefNumber": "1124746", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.1SPS05 Fehler bei Aktivierung Transformation", "RefUrl": "/notes/1124746 "}, {"RefNumber": "1124059", "RefComponent": "BW-WHM-DST", "RefTitle": "P17:BI_DELETE_OLD_MSG_PARM_DTPTEMP scheduled several times", "RefUrl": "/notes/1124059 "}, {"RefNumber": "1121138", "RefComponent": "BW-WHM-DST", "RefTitle": "P17:DEL: Deleting requests w/o P dim. entries from InfoCubes", "RefUrl": "/notes/1121138 "}, {"RefNumber": "1123345", "RefComponent": "BW-WHM-DST", "RefTitle": "P17:DTP: Additional logs during DTP status transition", "RefUrl": "/notes/1123345 "}, {"RefNumber": "1121140", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P17:PC:CR:BIA indicators in change run do not work", "RefUrl": "/notes/1121140 "}, {"RefNumber": "1121223", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P17:SDL:RSSM_SHIPDVERS_CLEANUP finds incorrect shadow IPak", "RefUrl": "/notes/1121223 "}, {"RefNumber": "1122185", "RefComponent": "BW-WHM-AWB", "RefTitle": "P17:DWWB: Searching for InfoPackages is slow", "RefUrl": "/notes/1122185 "}, {"RefNumber": "1122444", "RefComponent": "BW-WHM-DST", "RefTitle": "P17: Incorrect error message when DataSource is inactive", "RefUrl": "/notes/1122444 "}, {"RefNumber": "1120747", "RefComponent": "BW-WHM-DST", "RefTitle": "P17: OutlookSoft: Only one open APO request permitted", "RefUrl": "/notes/1120747 "}, {"RefNumber": "1121222", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "P17:MON: Dump when setting QM status for an IDoc request", "RefUrl": "/notes/1121222 "}, {"RefNumber": "1121766", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination in SAPLRRI2 and form REP_ASSIGN_INITIAL_OPT-01-", "RefUrl": "/notes/1121766 "}, {"RefNumber": "1122481", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0 (SP17) An error occurs when you are generating Content", "RefUrl": "/notes/1122481 "}, {"RefNumber": "1117313", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Job log:archiving run for data archiving process - empty msg", "RefUrl": "/notes/1117313 "}, {"RefNumber": "1122958", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "RSAR 479 when you activate formulas from BI Content", "RefUrl": "/notes/1122958 "}, {"RefNumber": "1123633", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination: Program SAPLRRI2 and form FEHLER_INIT-01-", "RefUrl": "/notes/1123633 "}, {"RefNumber": "1122236", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RS_SYSTEM_SHUTDOWN: Warning in the case of open RDA requests", "RefUrl": "/notes/1122236 "}, {"RefNumber": "1123324", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: Incorrect display after object version switched", "RefUrl": "/notes/1123324 "}, {"RefNumber": "1122124", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Termination in SAPLRRI2 and form FAC_VAR_IN_VREP_INSERT-1-", "RefUrl": "/notes/1122124 "}, {"RefNumber": "1121319", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error during transformation of new images or delete images", "RefUrl": "/notes/1121319 "}, {"RefNumber": "1122300", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0 (SP17) Transporting the deletion for a transformation", "RefUrl": "/notes/1122300 "}, {"RefNumber": "1121165", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error in InfoSet join", "RefUrl": "/notes/1121165 "}, {"RefNumber": "1122080", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0 (SP17) Dump when you save a transformation (Content)", "RefUrl": "/notes/1122080 "}, {"RefNumber": "1122155", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Problems with virtual time hierarchies and date 29.2", "RefUrl": "/notes/1122155 "}, {"RefNumber": "1114483", "RefComponent": "BW-WHM-DST", "RefTitle": "P17: Additional sequence check of a delta/init request", "RefUrl": "/notes/1114483 "}, {"RefNumber": "1120719", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0 (SP17) Problems occur during the transport", "RefUrl": "/notes/1120719 "}, {"RefNumber": "1121357", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI 7.0 (SP17) Data not in external format file destination", "RefUrl": "/notes/1121357 "}, {"RefNumber": "1114104", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA_RESET terminates if there is a red request", "RefUrl": "/notes/1114104 "}, {"RefNumber": "1113907", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Performance hierarchy node input help", "RefUrl": "/notes/1113907 "}, {"RefNumber": "1112918", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "There are too many nodes selected when entered directly", "RefUrl": "/notes/1112918 "}, {"RefNumber": "1119619", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination in program SAPLRRS<PERSON> and form EXIT_INVERS_FOR_F", "RefUrl": "/notes/1119619 "}, {"RefNumber": "1120449", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17) Performance prob. when you open transformations", "RefUrl": "/notes/1120449 "}, {"RefNumber": "1107434", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "OLAP cache consultation: Long wait times due to locks", "RefUrl": "/notes/1107434 "}, {"RefNumber": "1120360", "RefComponent": "BW-WHM-AWB", "RefTitle": "P17:DWWB: Reading the InfoArea/appl. comp. hier. w/o enqueue", "RefUrl": "/notes/1120360 "}, {"RefNumber": "1120453", "RefComponent": "BW-WHM-AWB", "RefTitle": "P17: Attribute change run in the DWWB (object list)", "RefUrl": "/notes/1120453 "}, {"RefNumber": "1120454", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P17:SDL: Content release and content time stamp changed", "RefUrl": "/notes/1120454 "}, {"RefNumber": "1120639", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P17:PSA:CL:PC:Deleting only one type of requests from PSA/CL", "RefUrl": "/notes/1120639 "}, {"RefNumber": "1114164", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "Termination in the cache due to insufficient shared memory", "RefUrl": "/notes/1114164 "}, {"RefNumber": "1120618", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Terminated time change run does not release lock", "RefUrl": "/notes/1120618 "}, {"RefNumber": "1106485", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Deleting master data; and aggregates", "RefUrl": "/notes/1106485 "}, {"RefNumber": "1119697", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "A125 BRAIN when checking or generating a query", "RefUrl": "/notes/1119697 "}, {"RefNumber": "1117949", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "THJ: Incorrect elimination of internal business volume", "RefUrl": "/notes/1117949 "}, {"RefNumber": "1120110", "RefComponent": "BW-WHM", "RefTitle": "P17: Text changes/incorrectly translated texts/messages", "RefUrl": "/notes/1120110 "}, {"RefNumber": "1116377", "RefComponent": "BW-WHM-DST-BAPI", "RefTitle": "Creating InfoSource using a BAPI: Dialog box triggers error", "RefUrl": "/notes/1116377 "}, {"RefNumber": "1118725", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "F4 help cannot be displyed for referencing characteristics", "RefUrl": "/notes/1118725 "}, {"RefNumber": "1116485", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Unit not displayed for display attribute", "RefUrl": "/notes/1116485 "}, {"RefNumber": "1116268", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Changing default value for MaxRows in 3.X variable screen", "RefUrl": "/notes/1116268 "}, {"RefNumber": "1105595", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "No text for MemberSet value or bucket variable", "RefUrl": "/notes/1105595 "}, {"RefNumber": "1119944", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Archiving request:Production mode instead of simulation mode", "RefUrl": "/notes/1119944 "}, {"RefNumber": "1113690", "RefComponent": "BW-WHM", "RefTitle": "P17: Syntax error in report RSSM_PREPARE_SDLINIT_TABLES_40", "RefUrl": "/notes/1113690 "}, {"RefNumber": "1114657", "RefComponent": "BW-WHM-DST", "RefTitle": "P17: Select for /BI0/SREQUID fills single record buffer", "RefUrl": "/notes/1114657 "}, {"RefNumber": "1114658", "RefComponent": "BW-WHM-DST", "RefTitle": "P17:BATCH: Restarting child processes in child process", "RefUrl": "/notes/1114658 "}, {"RefNumber": "1114659", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "P17:MON:No DataSource preselection when you select data req.", "RefUrl": "/notes/1114659 "}, {"RefNumber": "1113603", "RefComponent": "BW-WHM-DST", "RefTitle": "P17:DSO:Postprocessing ODS - activating and updating", "RefUrl": "/notes/1113603 "}, {"RefNumber": "1113608", "RefComponent": "BW-WHM-DST", "RefTitle": "P17:REQARCH:Archiving prog. takes too long to check requests", "RefUrl": "/notes/1113608 "}, {"RefNumber": "1113610", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "P17:SDL:PC:RDA:Daemon runs: Do not start delta/init IPackage", "RefUrl": "/notes/1113610 "}, {"RefNumber": "1114097", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "P17:RDA: Check if real-time request is the last request", "RefUrl": "/notes/1114097 "}, {"RefNumber": "1114299", "RefComponent": "BW-WHM-DST", "RefTitle": "P17:DSO:STATMANL: Activating several request; performance", "RefUrl": "/notes/1114299 "}, {"RefNumber": "1119507", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17) \"RequestID\" and \"PackageID\" in structure", "RefUrl": "/notes/1119507 "}, {"RefNumber": "1119000", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "COMMIT occurs when you execute planning functions", "RefUrl": "/notes/1119000 "}, {"RefNumber": "1119337", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Transport dialog box terminates after display -> change", "RefUrl": "/notes/1119337 "}, {"RefNumber": "1105004", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Assigning navigation attributes to dimensions", "RefUrl": "/notes/1105004 "}, {"RefNumber": "1119155", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP 17) Unrequired warnings are issued", "RefUrl": "/notes/1119155 "}, {"RefNumber": "1115751", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI7.0(SP17) Table not deleted when you delete destination", "RefUrl": "/notes/1115751 "}, {"RefNumber": "1118983", "RefComponent": "BW", "RefTitle": "VORLAGE: BI 7.0 (SP17) / BI 7.1 SPS05", "RefUrl": "/notes/1118983 "}, {"RefNumber": "1118037", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Restoring 3.x DataSource terminates with RSDS 135", "RefUrl": "/notes/1118037 "}, {"RefNumber": "1119095", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Runtime object not regenerated", "RefUrl": "/notes/1119095 "}, {"RefNumber": "1112742", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error when you are maintaining transformations", "RefUrl": "/notes/1112742 "}, {"RefNumber": "1108873", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Incorrect Oracle hint for InfoCube compression", "RefUrl": "/notes/1108873 "}, {"RefNumber": "1118662", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17) InfoSource deletion; transformation not deleted", "RefUrl": "/notes/1118662 "}, {"RefNumber": "1118585", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "RSRV, Hierarchie: Falsche Fehlerpriorität", "RefUrl": "/notes/1118585 "}, {"RefNumber": "1118426", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Problem: Replacing variable w/ variable (time-dep. attrib.)", "RefUrl": "/notes/1118426 "}, {"RefNumber": "1111386", "RefComponent": "BW-WHM-MTD-INST", "RefTitle": "BI Content installation even though system cannot be changed", "RefUrl": "/notes/1111386 "}, {"RefNumber": "1118423", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Replacing text variables when source value is initial (#)", "RefUrl": "/notes/1118423 "}, {"RefNumber": "1115356", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA indexing terminates with an SQL error", "RefUrl": "/notes/1115356 "}, {"RefNumber": "1116689", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Function R_TRIM does not work", "RefUrl": "/notes/1116689 "}, {"RefNumber": "1111733", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "x_message AUTH_TO_SETOBJECT:CX_RSMDS_INPUT_INVALID", "RefUrl": "/notes/1111733 "}, {"RefNumber": "1108347", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "OLAP cache GETWA_NOT_ASSIGNED in CL_RSR_CACHE_QUERY_CUBE_MEM", "RefUrl": "/notes/1108347 "}, {"RefNumber": "1116933", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Characteristics with a Universal Unique Identifier", "RefUrl": "/notes/1116933 "}, {"RefNumber": "1116813", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Optimizing time distribution for floating point key figures", "RefUrl": "/notes/1116813 "}, {"RefNumber": "1112338", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Temporal hierarchy join in structure elements", "RefUrl": "/notes/1112338 "}, {"RefNumber": "1117536", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Message: unable to move ...  (<PERSON> 618)", "RefUrl": "/notes/1117536 "}, {"RefNumber": "1109219", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect replacement of a variable from a query result", "RefUrl": "/notes/1109219 "}, {"RefNumber": "1113581", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination in the form KFB_FUELLEN_BU of program SAPLRRI2", "RefUrl": "/notes/1113581 "}, {"RefNumber": "1116846", "RefComponent": "BW-WHM-DST-UPD", "RefTitle": "BI7.0(SP17) Problems when you transport update rules", "RefUrl": "/notes/1116846 "}, {"RefNumber": "1116410", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17) Dump when editing transformations", "RefUrl": "/notes/1116410 "}, {"RefNumber": "1113931", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17) Transformations are not transferred correctly", "RefUrl": "/notes/1113931 "}, {"RefNumber": "1107988", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA daemon sets the status of the running DTP request", "RefUrl": "/notes/1107988 "}, {"RefNumber": "1111229", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA daemon terminates after six hours", "RefUrl": "/notes/1111229 "}, {"RefNumber": "1116719", "RefComponent": "BW-WHM-DST-BAPI", "RefTitle": "BI7.0(SP17) Fehlermeldung BAPI BAPI_ISOURCE_TD_T_CREATE", "RefUrl": "/notes/1116719 "}, {"RefNumber": "1116250", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Hierarchy &1 is invalid for key date ********", "RefUrl": "/notes/1116250 "}, {"RefNumber": "1116632", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Queries on InfoCubes that read all requests are slow", "RefUrl": "/notes/1116632 "}, {"RefNumber": "1114773", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "900 index on F fact tab. is BITMAP but cube is transactional", "RefUrl": "/notes/1114773 "}, {"RefNumber": "1115168", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI 7.0 (SP17) Destination is incorrectly activated", "RefUrl": "/notes/1115168 "}, {"RefNumber": "1116261", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Termination in program SAPLR<PERSON><PERSON><PERSON> and form RRK_LIST_NOTIFY-01-", "RefUrl": "/notes/1116261 "}, {"RefNumber": "1115927", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Master record not displayed with value but as #", "RefUrl": "/notes/1115927 "}, {"RefNumber": "1115665", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Description of key figures truncated when replacing", "RefUrl": "/notes/1115665 "}, {"RefNumber": "1115515", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Key figure of type TIMS (time) displayed as date", "RefUrl": "/notes/1115515 "}, {"RefNumber": "1116004", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17) Error in the HTML display of the transformation", "RefUrl": "/notes/1116004 "}, {"RefNumber": "1108906", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Replication deletes content time stamp", "RefUrl": "/notes/1108906 "}, {"RefNumber": "1111539", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Log is missing after replication", "RefUrl": "/notes/1111539 "}, {"RefNumber": "1114426", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Termination of migration due to missing auth.", "RefUrl": "/notes/1114426 "}, {"RefNumber": "1115902", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Correction UDC: Connection defective after restoring", "RefUrl": "/notes/1115902 "}, {"RefNumber": "1110375", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "IOBJ_VALUE_NOT_VALID when you link to the Broadcaster", "RefUrl": "/notes/1110375 "}, {"RefNumber": "1108801", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Delete filter and process variable screen again", "RefUrl": "/notes/1108801 "}, {"RefNumber": "1115574", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI 7.0 (SP 17) Dump with incorrect template", "RefUrl": "/notes/1115574 "}, {"RefNumber": "1112109", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "Transfer structure is not selected during Content transfer", "RefUrl": "/notes/1112109 "}, {"RefNumber": "1113716", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17) Fehlermeldung: Es ist eine Ausnahme aufgetreten", "RefUrl": "/notes/1113716 "}, {"RefNumber": "1114475", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "No F4 help for special characters for CSV converter", "RefUrl": "/notes/1114475 "}, {"RefNumber": "1113439", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Preview of raw data (in the InfoPackage)", "RefUrl": "/notes/1113439 "}, {"RefNumber": "1114084", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA index with compunded InfoObject without text", "RefUrl": "/notes/1114084 "}, {"RefNumber": "1105583", "RefComponent": "BW-BEX-OT", "RefTitle": "RSTT: Correct parameter display for reduced call stack", "RefUrl": "/notes/1105583 "}, {"RefNumber": "1106705", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Runtime error ASSERTION_FAILED for formulas", "RefUrl": "/notes/1106705 "}, {"RefNumber": "1109475", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "BI 7.1 SPS05: UUID Transformation bricht ab", "RefUrl": "/notes/1109475 "}, {"RefNumber": "1106692", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA daemon does not close open requests", "RefUrl": "/notes/1106692 "}, {"RefNumber": "1105096", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Transformation cannot be deleted due to transport system", "RefUrl": "/notes/1105096 "}, {"RefNumber": "1105094", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Structure name error in RSDS_RANGE_TO_WHERE", "RefUrl": "/notes/1105094 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "710", "To": "710", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}