{"Request": {"Number": "872800", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 285, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015940092017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000872800?language=E&token=D373F6B002C87526BF4F570BC4865CA6"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000872800", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000872800/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "872800"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 16}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "07.05.2009"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SER"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Support Services"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Support Services", "value": "SV-SMG-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "872800 - Roles for SAP Solution Manager: SAP service provider"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The system user that an SAP Support employee works with in a customer SAP Solution Manager system does not have the required authorizations or does not yet exist.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>TQM, service provider, SAP Service and Support, remote, onsite, authorizations, roles, PFCG, SAP_SOLMAN_ONSITE_COMP, SAP_SOLMAN_ONSITE_ALL_COMP, Internet Communication Framework (ICF) authorization<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You have not assigned the necessary roles to the system user, or the system user does not exist yet.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>To ensure a service provider or another employee of SAP Support can fulfill their tasks, their system user must have specific roles in the live client of SAP Solution Manager. These roles depend on how many authorizations you, as a customer, want to give the SAP Support employee.<br />This note describes how a specific user is created and defined, if necessary, based on the analysis user SAPSUPPORT.<br />SAP delivers two composite roles, because a distinction is made between two scenarios:<br /><br />a. Customer maintains system information and defines solutions<br /><br />The customer carries out the main tasks in SAP Solution Manager, that is, maintains the system landscapes (transaction: \\nSMSY) determines which solutions should exist and which ones should be registered by SAP, that is, transfers the solution as initial.<br />In this case, it is sufficient to assign the restricted role SAP_SOLMAN_ONSITE_COMP to the SAP Support employee (valid as of Support Package 10).<br /><br />Role SAP_SOLMAN_ONSITE_COMP <B>permits</B>:</p> <UL><LI>The creation of scenarios, business processes and business process steps</LI></UL> <UL><LI>The creation of issues and Top Issues (sending Top Issues and using Expertise on Demand (EoD))</LI></UL> <UL><LI>The creation of projects, reports about these, and the use of Roadmaps</LI></UL> <UL><LI>The opening of business process maintenance, maintenance of contacts and services</LI></UL> <UL><LI>Authorizations for transactions in the \"SolMan Diagnostics\" area</LI></UL> <UL><LI>The addition of a test case to a business process step</LI></UL> <UL><LI>The creation of a test catalog (Tx STWB_1)</LI></UL> <UL><LI>The creation of a test plan (Tx STWB_2)</LI></UL> <UL><LI>The creation of test packages and the assignment of testers</LI></UL> <UL><LI>The opening of the test execution (Tx STWB_WORK)</LI></UL> <p><br /><br />Role SAP_SOLMAN_ONSITE_COMP <B>does not permit</B>:</p> <UL><LI>The creation of solutions</LI></UL> <UL><LI>The creation or change of logical components</LI></UL> <UL><LI>The creation or change of systems</LI></UL> <UL><LI>The creation or change of servers</LI></UL> <UL><LI>The creation or change of databases</LI></UL> <UL><LI>The change of the configuration of the data transfer</LI></UL> <p><br /><br />b. Customer trusts SAP Support and allows enhanced authorizations<br /><br />The SAP Support employee is permitted to make changes to the system landscape (for example, the addition of systems, the creation of logical components, and so on). They are also permitted to create and transfer solutions.<br />The SAP Support employee is assigned the role SAP_SOLMAN_ONSITE_ALL_COMP (applies as of Support Package 14).<br />For Support Packages lower than Support Package 14, the SAP Support employee must be assigned the superuser role SAP_SV_SOLUTION_MANAGER (for more information see the section: Up to Release ST 400, Support Package 10 applies).<br /><br />Role SAP_SOLMAN_ONSITE_ALL_COMP <B>permits</B>:</p> <UL><LI>The creation of scenarios, business processes and business process steps</LI></UL> <UL><LI>The creation of issues and Top Issues (sending Top Issues and using Expertise on Demand (EoD))</LI></UL> <UL><LI>The creation of projects, reports about these, and the use of Roadmaps</LI></UL> <UL><LI>The opening of business process maintenance, maintenance of contacts and services</LI></UL> <UL><LI>Authorizations for transactions in the \"SolMan Diagnostics\" area</LI></UL> <UL><LI>The addition of a test case to a business process step</LI></UL> <UL><LI>The creation of a test catalog (Tx STWB_1)</LI></UL> <UL><LI>The creation of a test plan (Tx STWB_2)</LI></UL> <UL><LI>The creation of test packages and the assignment of testers</LI></UL> <UL><LI>The opening of the test execution (Tx STWB_WORK)</LI></UL> <UL><LI>The creation of solutions</LI></UL> <UL><LI>The creation or change of logical components</LI></UL> <UL><LI>The creation or change of systems</LI></UL> <UL><LI>The creation or change of servers</LI></UL> <UL><LI>The creation or change of databases</LI></UL> <UL><LI>The change of the configuration of the data transfer</LI></UL> <UL><LI>The execution of transactions SE16, SM59 and SU01D</LI></UL> <p><br /><br />Recommended procedure<br /><br />1. If the system user SAPSUPPORT has already been created using the automatic basic configuration (transaction SOLMAN_SETUP), copy this user with all roles to the Z namespace. Then proceed with step 5.<br />If a user does not yet exist, you must create it manually. Use the SAP Implementation Guide for this purpose (transaction SPRO -&gt; SAP Reference IMG -&gt; SAP Solution Manager -&gt; Basic Settings -&gt; Authorizations/Users -&gt; Create user SAPSUPPORT).<br /><br />2. Copy the composite role SAP_SOLMAN_ONSITE_COMP or SAP_SOLMAN_ONSITE_ALL_COMP to your specific namespace, for example, Z_SAP_SOLMAN_ONSITE_COMP (transaction PFCG). Confirm the query if all single roles contained are to be copied. Afterwards, a table appears in which all single roles are listed. At this point, a new name (for example, Z_*) must be specified for every single role.<br /><br />3. Assign the relevant role to the system user previously created (transaction SU01).<br />Note the following: These are composite roles. For this reason use the \"Composite role\" tab page and not the \"Single role\" tab page in the search for the relevant role.<br />Important: After you save your changes, all assigned roles are in a list. You can distinguish the composite role according to the type of the single roles. In addition, the composite role is defined in black font, while the single roles are defined in blue.<br /><br />4. In addition, assign the single role SAP_RCA_DISP to the user.<br /><br />5. The individual single roles of the composite role must be defined and generated (see Note 840516). For this reason, you must process every single role, that is, you must define all authorization objects that still have a yellow triangle. You can do this in the user maintenance. Select \"Roles\" -&gt; \"Single role\" -&gt; \"Authorizations\" (Note: Make sure you are in change mode) -&gt; Display authorization data\". We recommend you give full authorizations to all these authorization objects. For this reason, you must select the yellow triangle and confirm the subsequent query with the green checkmark. In a case, where you do not want to assign full authorizations to individual objects, assign these objects their own specific values by selecting the glasses icon (see also the example in the attachment). Choose Generate (red or white button) to generate the assignments.<br />You can find additional information about the authorization objects in the SAP Solution Manager security guide: http://service.sap.com/instguides -&gt; SAP Components -&gt; SAP Solution Manager &lt;current release&gt;.<br /><br />6. Execute the user comparison. Enter the relevant composite role in the role maintenance (/nPFCG) and go to change mode. On the \"User\" tab page choose \"User comparison\". (All single roles must be defined and generated, see also point 3.)<br /><br />7. If BI does not run in the live client of SAP Solution Manager, you must create a user of the same name in the BI client (of the BI system) and assign the role SAP_BI_E2E to this.<br /><br />As of Support Package 15:<br />You have to add the following roles to both the composite roles SAP_SOLMAN_ONSITE_COMP or SAP_SOLMAN_ONSITE_ALL_COMP:</p> <UL><LI>SAP_SOLMANDIAG_SAPSUPPORT</LI></UL> <UL><LI>SAP_SOLMANDIAG_E2E</LI></UL> <UL><LI>SAP_BI_E2E (only if BI_CONT has been delivered)</LI></UL> <UL><LI>SAP_SM_SOLUTION_*</LI></UL> <UL><LI>SAP_ISSUE_MANAGEMENT_EXE</LI></UL> <p><br />As of Support Package 18 (Enhancement Package 1):</p> <UL><LI>SAP_DBA_DISP</LI></UL> <UL><LI>SAP_RCA_EXE</LI></UL> <UL><LI>SAP_RCA_DISP</LI></UL> <UL><LI>SAP_SMSY_ALL</LI></UL> <UL><LI>Various work center roles</LI></UL> <UL><LI>The roles SAP_SOLMANDIAG_SAPSUPPORT and SAP_SOLMANDIAG_E2E no longer exist as such. They have been partially included in the new roles.</LI></UL> <p><br />Only for Support Package 19 and Support Package 20:</p> <UL><LI>If you are a service provider and you have carried out the IMG activities for work centers for customers, you must assign the role SAP_SM_SPC to the SAPSUPPORT/SAP_ONSITE user and generate it. In the role SAP_SMWORK_BASIC, check whether the authorization object S_ICF is active. If it is not, activate the object and define it as described in the IMG activity for restricting accesses to services SOLMAN_SPC_ALIAS.</LI></UL> <p><br />As of Support Package 21:</p> <UL><LI> SAP_SM_ADMIN_COMPONENT_ALL</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The role SAP_SM_ADMIN_COMPONENT_ALL allows the user to display a list of all MDM servers and repositories for an MDM system and their status in the MDM Administration Cockpit.<br />To obtain the status of an MDM server, you may have to create a logical port (if it does not already exist). This is required so that the ABAP proxy can carry out a Web service call.<br />In Support Package 19, the authorizations for creating and generating, and for changing and displaying the authorization object S_RFC_ADM have been added to the role.<br /><br />The role SAP_SM_ADMIN_COMPONENT_ALL allows you to carry out the following actions from the cockpit:</p> <UL><UL><LI>Display status of MDM server</LI></UL></UL> <UL><UL><LI>Start MDM server</LI></UL></UL> <UL><UL><LI>Stop MDM server</LI></UL></UL> <UL><UL><LI>Display status of MDM repositories</LI></UL></UL> <UL><UL><LI>Load/unload MDM repository</LI></UL></UL> <UL><UL><LI>Archive repository</LI></UL></UL> <UL><UL><LI>Verify repository</LI></UL></UL> <UL><UL><LI>Repair repository</LI></UL></UL> <UL><UL><LI>Data Model Statistics (new with Support Package 21)</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This role does not replace the MDM repository security policy, rather it extends it to the ABAP environment.<br />The MDM repository role assigned to a user should allow the same activities that are allowed by the role SAP_SM_ADMIN_COMPONENT_ALL. Otherwise a user cannot carry out activities in the MDM Administration Cockpit.<br />You can find additional information in the MDM administration configuration in the section for a trusted connection setting in the MDM server and the MDM repository. <br /><br />Up to Release ST 400, Support Package 10:</p> <UL><LI>To create the relevant role (authorizations) for the SAP service supplier, proceed as follows:<br />In transaction PFCG, copy the role SAP_SV_SOLUTION_MANAGER into your namespace (Z_*). Assign an appropriate role name.<br />As a superuser role for SAP Solution Manager Operations, the role SAP_SV_SOLUTION_MANAGER contains full authorization for all transactions in the Operations area.<br />Up to Support Package 13, add the following function group to the authorization object S_RFC in the role SAP_SV_SOLUTION_MANAGER: SUP_STSUP.</LI></UL> <p><br /><br />As of ST400, Support Package 10 or Support Package 14:</p> <UL><LI>As of Support Package 10, the following composite role is available: SAP_SOLMAN_ONSITE_COMP</LI></UL> <UL><LI>As of Support Package 14, the enhanced composite role is available: SAP_SOLMAN_ONSITE_ALL_COMP</LI></UL> <p><br /><br />For the instance of both roles, read Note 834534.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D039155"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON> (D026420)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000872800/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000872800/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000872800/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000872800/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000872800/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000872800/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000872800/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000872800/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000872800/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Example.PPT", "FileSize": "467", "MimeType": "application/vnd.ms-powerpoint", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000306702005&iv_version=0016&iv_guid=A57564046E4B0044AE4C181A5D93C406"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "840516", "RefComponent": "SV-SMG", "RefTitle": "Roles and profile definition", "RefUrl": "/notes/840516"}, {"RefNumber": "834534", "RefComponent": "SV-SMG", "RefTitle": "SAP Solution Manager roles in Release 7.0", "RefUrl": "/notes/834534"}, {"RefNumber": "625773", "RefComponent": "SV-SMG", "RefTitle": "Roles in SAP Solution Manager Release 3.20", "RefUrl": "/notes/625773"}, {"RefNumber": "1634757", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Service 'Performance Optimization'", "RefUrl": "/notes/1634757"}, {"RefNumber": "1601951", "RefComponent": "SV-SMG-SER", "RefTitle": "Self Service 'SQL Statement Tuning' - Prerequisites and FAQ", "RefUrl": "/notes/1601951"}, {"RefNumber": "1335441", "RefComponent": "SV-SMG-SVD", "RefTitle": "Note for service 'CCoE_SA' (Customer CoE Self Assessment)", "RefUrl": "/notes/1335441"}, {"RefNumber": "1172939", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1172939"}, {"RefNumber": "1149742", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP (CQC) Going Live Support", "RefUrl": "/notes/1149742"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1321295", "RefComponent": "SV-SMG-SER", "RefTitle": "Enterprise Support Report", "RefUrl": "/notes/1321295 "}, {"RefNumber": "1609155", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Services", "RefUrl": "/notes/1609155 "}, {"RefNumber": "1074808", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Change and Transport Analysis Session: Requirements", "RefUrl": "/notes/1074808 "}, {"RefNumber": "1149742", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP (CQC) Going Live Support", "RefUrl": "/notes/1149742 "}, {"RefNumber": "1323405", "RefComponent": "SV-SMG-SER", "RefTitle": "Technical Preparation of a CQC BPPO service", "RefUrl": "/notes/1323405 "}, {"RefNumber": "834534", "RefComponent": "SV-SMG", "RefTitle": "SAP Solution Manager roles in Release 7.0", "RefUrl": "/notes/834534 "}, {"RefNumber": "1442799", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Enterprise Support Report", "RefUrl": "/notes/1442799 "}, {"RefNumber": "1601951", "RefComponent": "SV-SMG-SER", "RefTitle": "Self Service 'SQL Statement Tuning' - Prerequisites and FAQ", "RefUrl": "/notes/1601951 "}, {"RefNumber": "1634757", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Service 'Performance Optimization'", "RefUrl": "/notes/1634757 "}, {"RefNumber": "1335441", "RefComponent": "SV-SMG-SVD", "RefTitle": "Note for service 'CCoE_SA' (Customer CoE Self Assessment)", "RefUrl": "/notes/1335441 "}, {"RefNumber": "1392411", "RefComponent": "XX-SER-GEN", "RefTitle": "Remote Access to SRM 5.0 - 7.0 solutions", "RefUrl": "/notes/1392411 "}, {"RefNumber": "625773", "RefComponent": "SV-SMG", "RefTitle": "Roles in SAP Solution Manager Release 3.20", "RefUrl": "/notes/625773 "}, {"RefNumber": "840516", "RefComponent": "SV-SMG", "RefTitle": "Roles and profile definition", "RefUrl": "/notes/840516 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST", "From": "400", "To": "400", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}