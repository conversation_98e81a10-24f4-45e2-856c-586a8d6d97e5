{"Request": {"Number": "79084", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 350, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014513172017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000079084?language=E&token=60DDF8B09BA8377F9521794635C5FD09"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000079084", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000079084/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "79084"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 30}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "01.10.2013"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CST-EQ"}, "SAPComponentKeyText": {"_label": "Component", "value": "Enqueue"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Client/Server Technology", "value": "BC-CST", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CST*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Enqueue", "value": "BC-CST-EQ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CST-EQ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "79084 - Syslog: Error when writing lock handler file"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>1. There are error messages in the system log of the central lock handler:<br/> GEM K Error writing to a lock handler file.<br/> GEL K Error reading a lock handler file.<br/> GZZ K &gt; ...\\SYS\\global\\ENQLIS...<br/> GZZ K &gt; Invalid argument<br/> GEA K Internal error in lock management.<br/> GZZ K &gt; LstActualFile(); rtc=8<br/>There are error messages in the system log of the local application server:<br/> GI0 K Error calling the central lock handler<br/> <br/> GI7 K &gt; Request to update the report file<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; failed.<br/><br/> GZZ K &gt; EnqueOpMem(); Illegal OpCode A<br/> GEA K Internal lock administration error<br/> GEH K Enqueue: Transmission error when reading lock entries<br/><br/>2. From kernel Release 46D, the system can also issue the following error messages:<br/>....<br/>E&#x00A0;&#x00A0;*** ERROR =&gt; EnqServer(). Inconsistent Request Length. [enctrl.c<br/>E&#x00A0;&#x00A0;*** ERROR =&gt; Input Buffer LenTh = 592, LenRec = 29616 [enctrl.c<br/>E&#x00A0;&#x00A0;*** ERROR =&gt; EnqueQueryRemote: rtc = 8 [enxxhead.c&#x00A0;&#x00A0; 3673]<br/>or<br/>E&#x00A0;&#x00A0;*** ERROR =&gt; EnqueSendQueryRequest: MaxResponseSize(...) !=<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ComBufferSize(...) [enxxhead.c&#x00A0;&#x00A0;...]<br/></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>enqueue enque lock lock handler reporting file report file batch input SAPMSBDC ENQUE_REPORT ENQUEUE_REPORT ENQUE_READ ENQUEUE_READ enxxhead RSBDCBTC EnqueOpMem Illegal OpCode A<br/>EnqueQueryRemote EnqueQueryServer<br/></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><OL>1. There are invalid calls of the function modules ENQUE_REPORT and ENQUE_READ outside the lock server programs.</OL> <span>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;</span>The ENQUE_REPORT and ENQUE_READ functions are only intended for execution on the enqueue server. Application programs must use the ENQUEUE_READ or ENQUEUE_REPORT functions instead. <span>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;</span>If you use the function module ENQUE_REPORT_TEST, which has not been released, the system can also issue the above system log entries. <OL>2. The profile parameter enque/process_location is set in the enqueue instance on REMOTE_TASKHANDLER. You should not set enque/process_location explicitly; if you do, the default value has no effect.</OL> <span>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;</span>Exception: You should only set this parameter in a high-availability configuration. In this special case, be sure to note the specific configuration guidelines of the high-availability configuration. <p></p> <OL>3. The instances of the system have different kernel versions.</OL> <p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>1. Invalid call of ENQUE_REPORT or ENQUE_READ<br/><br/>In the programs RSBDCBTC and SAPMSBDC, replace the calls of ENQUE_REPORT with calls of ENQUEUE_REPORT. The parameters remain the same. The errors in these programs are corrected in Release 4.6x and later.<br/><br/>Other programs may also contain ENQUE_READ or ENQUE_REPORT calls, which will cause the same error.<br/>We have frequently observed invalid calls in customer modifications, as well - usually in programs whose names start with X, Y, or Z.<br/><br/>We recommend using the where-used list to search for incorrect calls in other programs.<br/>SE37, ENQUE_READ -&gt; Utilities -&gt; Where-Used List<br/>SE37, ENQUE_REPORT -&gt; Utilities -&gt; Where-Used List<br/>SE37, ENQUE_REPORT_TEST -&gt; Utilities -&gt; Where-Used List<br/><br/>Note that the where-used list is not updated automatically for SAP objects. Therefore, first proceed as described in SAP Note 28022.<br/><br/>The only legal calls of the ENQUE_READ and ENQUE_REPORT functions are contained in enqueue programs such as RSENQRR2, RSENQTSx, and LSENTUxx. Calls of ENQUE_REPORT_TEST are NOT allowed.<br/><br/>Do not let the possible presence of comment lines such as<br/>23.07.97 vh Funktionsbaustein ENQUE_REPORT durch ENQUEUE_REPORT<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; vh ersetzt, L&#x00F6;schen von Sperren<br/><br/>prevent you from replacing all occurrences of function calls for ENQUE_READ and ENQUE_REPORT with function calls for ENQUEUE_READ and ENQUEUE_REPORT, respectively. The programs RSENQRR2, RSENQTS*, and LSENTU* are the only exceptions.<br/>IMPORTANT: Do NOT replace the calls in the programs RSENQRR2, RSENQTS*, and LSENTU*.<br/><br/>The following occurrences of the &quot;incorrect&quot; calls are currently known:<br/><br/>&#x00A0;&#x00A0;RPIEWT04<br/>&#x00A0;&#x00A0;RPWI2000<br/>&#x00A0;&#x00A0;RSBDCBTC<br/>&#x00A0;&#x00A0;SAPMSBDC<br/>&#x00A0;&#x00A0;RSBDCTL3<br/>&#x00A0;&#x00A0;RSBDCTL4<br/>&#x00A0;&#x00A0;RSBDCTL6<br/>&#x00A0;&#x00A0;RSBDCTL7<br/>&#x00A0;&#x00A0;LZWABUU01<br/>&#x00A0;&#x00A0;LZWABUU02<br/>&#x00A0;&#x00A0;LZWABUU03<br/>&#x00A0;&#x00A0;LZWABUU04<br/>&#x00A0;&#x00A0;Z3C0M007<br/>&#x00A0;&#x00A0;Z3C0M022<br/>&#x00A0;&#x00A0;/SSA/ABS (corrected with SAP Note 1920219)<br/><br/>Occurrences in comment lines do not result in the error, but you can replace them anyway.<br/><br/>2. Invalid profile parameter enque/process_location: Remove the profile parameter enque/process_location from the default profile, DEFAULT.PFL, and from instance profiles if your system does not have a high-availability configuration.<br/><br/>3. Call transaction SM51 and check whether all the system instances<br/>have the same patch level. All instances of a system must use the same kernel patch level.<br/> <br/></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "SE37"}, {"Key": "Transaction codes", "Value": "SM51"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D050002)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000079084/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000079084/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000079084/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000079084/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000079084/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000079084/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000079084/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000079084/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000079084/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "606159", "RefComponent": "BC-ABA-SC", "RefTitle": "Batch input: Enqueue error messages in the syslog", "RefUrl": "/notes/606159"}, {"RefNumber": "590506", "RefComponent": "IS-A-SBO", "RefTitle": "MRER: Inconsistencies in the lock management", "RefUrl": "/notes/590506"}, {"RefNumber": "28022", "RefComponent": "BC-DWB-UTL", "RefTitle": "Customer system: Where-used list for SAP Objects", "RefUrl": "/notes/28022"}, {"RefNumber": "198330", "RefComponent": "IS-AFS-MM-PUR", "RefTitle": "Exchange of function module 'ENQUE_READ'", "RefUrl": "/notes/198330"}, {"RefNumber": "1920219", "RefComponent": "SV-SMG-SER", "RefTitle": "/SSA/ABS: Vermeidung des Aufrufs der Funktion ENQUE_READ", "RefUrl": "/notes/1920219"}, {"RefNumber": "143947", "RefComponent": "PT-RC-IW", "RefTitle": "RPWI2000: Error when writing the lock handler file", "RefUrl": "/notes/143947"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1997582", "RefComponent": "BC-CST-EQ", "RefTitle": "ENQU: \"|GE|H Enqueue: Transfer error while reading lock entries\" message in the System Log", "RefUrl": "/notes/1997582 "}, {"RefNumber": "1565578", "RefComponent": "BC-CST-EQ", "RefTitle": "Lock Table Overflow", "RefUrl": "/notes/1565578 "}, {"RefNumber": "2273909", "RefComponent": "BC-BMT-WFM-RUN", "RefTitle": "Inadmissible use of 'ENQUE_READ'", "RefUrl": "/notes/2273909 "}, {"RefNumber": "28022", "RefComponent": "BC-DWB-UTL", "RefTitle": "Customer system: Where-used list for SAP Objects", "RefUrl": "/notes/28022 "}, {"RefNumber": "1920219", "RefComponent": "SV-SMG-SER", "RefTitle": "/SSA/ABS: Vermeidung des Aufrufs der Funktion ENQUE_READ", "RefUrl": "/notes/1920219 "}, {"RefNumber": "1804834", "RefComponent": "IS-B-BCA-PT", "RefTitle": "Unauthorized usage of ENQUE_READ", "RefUrl": "/notes/1804834 "}, {"RefNumber": "606159", "RefComponent": "BC-ABA-SC", "RefTitle": "Batch input: Enqueue error messages in the syslog", "RefUrl": "/notes/606159 "}, {"RefNumber": "198330", "RefComponent": "IS-AFS-MM-PUR", "RefTitle": "Exchange of function module 'ENQUE_READ'", "RefUrl": "/notes/198330 "}, {"RefNumber": "590506", "RefComponent": "IS-A-SBO", "RefTitle": "MRER: Inconsistencies in the lock management", "RefUrl": "/notes/590506 "}, {"RefNumber": "143947", "RefComponent": "PT-RC-IW", "RefTitle": "RPWI2000: Error when writing the lock handler file", "RefUrl": "/notes/143947 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}