{"Request": {"Number": "1660394", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 458, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017353172017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001660394?language=E&token=AC36DD75F965B0AF540E98169D98E2A7"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001660394", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001660394/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1660394"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 27}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.10.2019"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-ADDON"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade Add-On Components"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade Add-On Components", "value": "BC-UPG-ADDON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-ADDON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1660394 - CEEISUT 606: Component Support Packages"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Information about component support packages for CEEISUT 606</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Support Packages, Add-on, AOP, CSP, CRT, SPAM, CEEISUT 606</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You have a system with the CEEISUT Add-on installed.<br />Component support packages (CSPs) contain corrections and fixes for the CEEISUT Add-on or previous component support packages.<br />This note contains information about component support packages for the CEEISUT Add-on.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Content</strong></p>\r\n<p><br />I.&#160;&#160; General information&#160;<br />&#160;&#160; 1. Downloading Add-on support packages<br />&#160;&#160; 2. SPAM update<br />&#160;&#160; 3. Import of support packages<br />II. Schedule<br />III. Problems while importing Add-on support packages<br />IV.&#160;&#160;Postprocessing the import of Add-on support packages</p>\r\n<p><strong>I. General information</strong></p>\r\n<p><br />The content of this note is specific for CEEISUT 606. If you have not installed this addon in your SAP system, this note is not relevant for you.<br />More information on addon support packages is available in note 160168.<br /><br /><strong>CAUTION: This note is updated constantly.</strong><br />Before you import Add-on support packages, read the current version of this note.</p>\r\n<ol>\r\n<li>Downloading Add-on support packages<br />Add-on support packages are available in the SAP Service Marketplace:<br /><a target=\"_blank\" href=\"http://service.sap.com/patches\">http://service.sap.com/patches</a>&#160;-&gt; Browse our Download Catalog -&gt; Country-specific Add-ons&#160;-&gt;&#160;&#160;SAP IS-UT CEE&#160;-&gt;&#160;&#160;SAP IS-UT CEE 606</li>\r\n<li>SPAM update<br />Import the latest SPAM/SAINT update. Make sure that you have imported the latest SPAM/SAINT update into your system. If a newer version is available on the SAP Service Marketplace, import the new SPAM/SAINT update.</li>\r\n<li>Import of support packages<br />Import support packages using Transaction SPAM. See the corresponding online documentation for more information.</li>\r\n</ol>\r\n<p><strong>II. Schedule</strong></p>\r\n<p>You can find a schedule when to expect support packages in this table:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"1\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><strong>Support Package</strong></p>\r\n</td>\r\n<td>\r\n<p><strong>Date</strong></p>\r\n</td>\r\n<td>\r\n<p><strong>Comment</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SAPK-60617INCEEISUT</p>\r\n</td>\r\n<td>\r\n<p>03.11.2018</p>\r\n</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SAPK-60616INCEEISUT</p>\r\n</td>\r\n<td>\r\n<p>23.04.2018</p>\r\n</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SAPK-60615INCEEISUT</p>\r\n</td>\r\n<td>\r\n<p>23.10.2017</p>\r\n</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SAPK-60614INCEEISUT</p>\r\n</td>\r\n<td>\r\n<p>24.04.2017</p>\r\n</td>\r\n<td>requires IS-UT 606 SP17 or 617 SP12 or 618 SP02</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SAPK-60613INCEEISUT</p>\r\n</td>\r\n<td>\r\n<p>24.04.2017</p>\r\n</td>\r\n<td><strong>Its mandatory that SP 13 / 14 must be installed together.</strong></td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SAPK-60612INCEEISUT</p>\r\n</td>\r\n<td>\r\n<p>25-apr-2016</p>\r\n</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SAPK-60611INCEEISUT</p>\r\n</td>\r\n<td>\r\n<p>11-Sep-2015</p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SAPK-60610INCEEISUT</p>\r\n</td>\r\n<td>\r\n<p>21-Apr-2015</p>\r\n</td>\r\n<td>\r\n<p>&#160;requires SAPK-60614INISUT or SAPK-61707INISUT or IS-UT 618</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SAPK-60609INCEEISUT</p>\r\n</td>\r\n<td>\r\n<p>10-Oct-2014</p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SAPK-60608INCEEISUT</p>\r\n</td>\r\n<td>\r\n<p>21-May-2014</p>\r\n</td>\r\n<td>\r\n<p>requires SAPK-60609INISUT or SAPK-61702INISUT or IS-UT 618</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SAPK-60607INCEEISUT</p>\r\n</td>\r\n<td>\r\n<p>27-Jan-2014</p>\r\n</td>\r\n<td>\r\n<p>Note 1954087 and SAPKB73105 required</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SAPK-60606INCEEISUT</p>\r\n</td>\r\n<td>\r\n<p>24-Sep-2013</p>\r\n</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SAPK-60605INCEEISUT</p>\r\n</td>\r\n<td>\r\n<p>14-Jun-2013</p>\r\n</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SAPK-60604INCEEISUT</p>\r\n</td>\r\n<td>\r\n<p>23-Jan-2013</p>\r\n</td>\r\n<td>requires SAPK-60604INISUT or IS-UT 617 or IS-UT 618</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SAPK-60603INCEEISUT</p>\r\n</td>\r\n<td>\r\n<p>25-Sep-2012</p>\r\n</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SAPK-60602INCEEISUT</p>\r\n</td>\r\n<td>\r\n<p>23-May-2012</p>\r\n</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SAPK-60601INCEEISUT</p>\r\n</td>\r\n<td>\r\n<p>26-Jan-2012</p>\r\n</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>III. Problems during import of Add-on support packages</strong><br /><br /></p>\r\n<ol>\r\n<li>SAPK-60607INCEEISUT<br />Note 1954087 is required before implementation of SAPK-60607INCEEISUT. Transaction SPAM will require a password to ensure this. This password is 34103A0960<br />.</li>\r\n<li>SAPK-60608INCEEISUT<br />These generation errors might occur. To solve that, see IV.1. below.<br />\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Program /SAPCE/SAPLFKSK_EVENTS, Include /SAPCE/LFKSK_EVENTSU04: Syntax error in line 000027<br />Type 'FKKSK_STR_SDATA' is unknown</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<br />.</li>\r\n<li>SAPK-60610INCEEISUT<br />These generation errors might occur. To solve that, see IV.2. below<br />\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Program /SAPCE/FK_SK_EVYBER: Syntax error in line 000021<br />Field 'T7SK52' is unknown. It is neither in one of the specified tables nor defined by a 'DATA' stat</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n</li>\r\n<li>SAPK-60612INCEEISUT<br />These generation errors might occur. To solve see the note <strong>2153215, 2228198</strong><br />\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p>Program /SAPCE/CL_EDOCUMENT_ISU_SVN===CP: &#160;Include /SAPCE/CL_EDOCUMENT_ISU_SVN===CU&#160;Syntax error in line 000007<br />Class 'CL_EDOC_PROCESS' IS unknown</p>\r\n<p>Program /SAPCE/IUHU_CL_AUDIT_FUNC===CP, Include /SAPCE/IUHU_CL_AUDIT_FUNC===CU: Syntax error in line 000009<br />Type 'IF_EX_HU_AUDIT_REPORT' is unknown</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n</li>\r\n<li><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-bidi-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">SAPK-60615INCEEISUT </span><br />These generation errors might occur. To solve see the note <strong><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; color: #385723; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-bidi-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">2446369</span>, <span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; color: #385723; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-bidi-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">2427168</span></strong><br />\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Program /SAPCE/CL_EDOCUMENT_ISU_SVN===CP, Include /SAPCE/CL_EDOCUMENT_ISU_SVN===CM00J: Syntax error in line 000024</em></p>\r\n<p><em>The data object 'MS_EDOCUMENT' does not have a component called 'EDOCUMENT_CLASS'.</em></p>\r\n<p><em>Program /SAPCE/CL_EDOC_FACTORY_ISU_SVNCP, Include /SAPCE/CL_EDOC_FACTORY_ISU_SVNCU: Syntax error in line 000003</em></p>\r\n<p><em>Type 'CL_EDOC_FACTORY' is unknown</em></p>\r\n<p><em>Program /SAPCE/EDOC_UPDATE_TABLE, Include /SAPCE/EDOC_UPDATE_TABLE_C01: Syntax error in line 000049</em></p>\r\n<p><em>The data object 'GS_EDOCUMENT' does not have a component called 'EDOCUMENT_CLASS'.</em></p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n</li>\r\n<li>The following Notes are required before implementation of SAPK-60617INCEEISUT.</li>\r\n</ol>\r\n<p style=\"padding-left: 30px;\">&#160; SAPNOTE No 2662557,0002662198,0002659385,0002650662,2650058,2645177,2638811,2637524,2636499,2635824,2632796,2623088,</p>\r\n<p style=\"padding-left: 120px;\">2601568,2593403,2592185,2592164,2589248,2587421,2583335,2582557,2561564,2550216.</p>\r\n<p style=\"padding-left: 30px;\">&#160;These generation errors might occur. By implementation of above notes resolve the errors</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\" style=\"height: 121px; width: 535px; padding-left: 30px;\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p>/SAPCE/CL_EDOCUMENT_ISU_HU&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Class /SAPCE/CL_EDOCUMENT_ISU_HU, Public Section&#160; Type \"CL_EDOCUMENT_HU_INV\" is unknown</p>\r\n<p>/SAPCE/CL_EDOC_FACTORY_ISU_HU&#160;&#160;&#160;&#160;&#160;&#160;&#160;Class /SAPCE/CL_EDOCUMENT_ISU_HU, Public Section&#160; Type \"CL_EDOCUMENT_HU_INV\" is unknown</p>\r\n<p>/SAPCE/CL_EDOC_MAP_HU_ISU&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Class /SAPCE/CL_EDOC_MAP_HU_ISU, Public Section&#160;&#160;&#160;&#160; Type \"CL_EDOC_MAP_HU_INV\" is unknown</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>&#160;7.&#160;</strong><span style=\"font-size: medium;\">SAPK-60619INCEEISUT:</span></p>\r\n<p><span style=\"font-size: medium;\">&#160;Below mentioned generation errors might occur while impoing the package. In case genration error occurs please implement following notes, in order to &#160; relove the generation error.</span></p>\r\n<p>2764737,2763851,2764791,2747446.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellspacing=\"0\" style=\"width: 983px; height: 59px;\">\r\n<tbody>\r\n<tr>\r\n<td>Program /SPACE/CL_EDOC_MAP_HU_ISU=====CP,&#160; Include /SPACE/CL_EDOC_MAP_HU_ISU=====CO: syntax error in line 000005<br />Type 'EDO_HU_I11_OPERATION_TYPE' is unknown</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>IV. Postprocessing the import of Add-on support packages</strong></p>\r\n<ol>\r\n<li>SAPK-60608INCEEISUT<br />The support package requires the following FI-CA notes if you are using the Slovakian localization:<br /><br />\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\" style=\"width: 724px;\">\r\n<tbody>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"80\">\r\n<p>1942466</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"644\">\r\n<p>SK FI-CA Electronic VAT Ledger</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"80\">\r\n<p>1954895</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"644\">\r\n<p>SK FI-CA Electronic VAT Ledger</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"80\">\r\n<p>1958171</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"644\">\r\n<p>Slovakia: Authorization Object F_KKID_SK cannot be created in customer environment</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"80\">\r\n<p>1955535</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"644\">\r\n<p>Correction of SK FI-CA VAT Electronic Ledger</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"80\">\r\n<p>1957599</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"644\">\r\n<p>Correction #2 SK FI-CA Electronic VAT Ledger</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"80\">\r\n<p>1960475</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"644\">\r\n<p>Correction #3 SK FI-CA Electronic VAT Ledger</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"80\">\r\n<p>1961275</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"644\">\r\n<p>Correction #4 SK FI-CA Electronic VAT Ledger</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"80\">\r\n<p>1960997</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"644\">\r\n<p>Correction #5 SK FI-CA Electronic VAT Ledger</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<br />.</li>\r\n<li>SAPK-60610INCEEISUT<br />The support package requires the following FI-CA notes if you are using the Slovakian localization: 2005304, 1995867, 2021860, 2042944</li>\r\n</ol>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "Sankar Prasad MVV (I345080)"}, {"Key": "Processor                                                                                           ", "Value": "Poornendu M G (I054365)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001660394/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001660394/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001660394/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001660394/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001660394/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001660394/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001660394/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001660394/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001660394/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1666931", "RefComponent": "XX-CSC-RU-IS-U", "RefTitle": "Russian specific functionality for IS-UT 606", "RefUrl": "/notes/1666931"}, {"RefNumber": "1645477", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Upgrade to SAP ERP 6.0 EHP6 with CEEISUT 606", "RefUrl": "/notes/1645477"}, {"RefNumber": "1645476", "RefComponent": "BC-UPG-ADDON", "RefTitle": "CEEISUT 606:Installation on ERP 6.0", "RefUrl": "/notes/1645476"}, {"RefNumber": "1015193", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Overview note for SAP IS-UT CEE add-on", "RefUrl": "/notes/1015193"}, {"RefNumber": "1014997", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for the SAP IS-UT CEE add-on", "RefUrl": "/notes/1014997"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1014997", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for the SAP IS-UT CEE add-on", "RefUrl": "/notes/1014997 "}, {"RefNumber": "1666931", "RefComponent": "XX-CSC-RU-IS-U", "RefTitle": "Russian specific functionality for IS-UT 606", "RefUrl": "/notes/1666931 "}, {"RefNumber": "1645476", "RefComponent": "BC-UPG-ADDON", "RefTitle": "CEEISUT 606:Installation on ERP 6.0", "RefUrl": "/notes/1645476 "}, {"RefNumber": "1645477", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Upgrade to SAP ERP 6.0 EHP6 with CEEISUT 606", "RefUrl": "/notes/1645477 "}, {"RefNumber": "1015193", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Overview note for SAP IS-UT CEE add-on", "RefUrl": "/notes/1015193 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "CEEISUT", "From": "606", "To": "606", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}