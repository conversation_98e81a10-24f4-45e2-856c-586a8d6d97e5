{"Request": {"Number": "1663018", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 223, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017356942017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001663018?language=E&token=B29342389F62A802C6DCD1ECFF494473"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001663018", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001663018/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1663018"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.09.2014"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX"}, "SAPComponentKeyText": {"_label": "Component", "value": "Flexible Real Estate Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1663018 - Composite SAP Note for Customer Connection Real Estate 2011/2012"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Within the Customer Connection initiative, new functions have been provided for the focus topic &quot;Real Estate&quot;.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Customer Connection, CustConn, focus topic, improvement, FAQ</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>For further information see the &quot;Reference to related Notes&quot; section.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D002072)"}, {"Key": "Processor                                                                                           ", "Value": "D030839"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001663018/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001663018/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001663018/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001663018/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001663018/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001663018/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001663018/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001663018/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001663018/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "939971", "RefComponent": "RE-FX", "RefTitle": "FAQ and consulting notes for RE-FX", "RefUrl": "/notes/939971"}, {"RefNumber": "1820728", "RefComponent": "RE-FX-SC", "RefTitle": "Flagging service charge keys as 'obsolete'", "RefUrl": "/notes/1820728"}, {"RefNumber": "1814243", "RefComponent": "RE-FX-RA", "RefTitle": "RE-FX: Vendor contracts and FI-CA", "RefUrl": "/notes/1814243"}, {"RefNumber": "1761084", "RefComponent": "RE-FX-RA", "RefTitle": "Invoice creation and FI-CA accounting system", "RefUrl": "/notes/1761084"}, {"RefNumber": "1735549", "RefComponent": "RE-FX-BP", "RefTitle": "Partner: Processing in business partner role grouping", "RefUrl": "/notes/1735549"}, {"RefNumber": "1721652", "RefComponent": "RE-FX-RA", "RefTitle": "RERACL: Execute clearing for real estate contracts", "RefUrl": "/notes/1721652"}, {"RefNumber": "1716812", "RefComponent": "RE-FX-SC", "RefTitle": "New itemization for settlement units", "RefUrl": "/notes/1716812"}, {"RefNumber": "1711608", "RefComponent": "RE-FX-CF", "RefTitle": "Retaining condition lines during mode change", "RefUrl": "/notes/1711608"}, {"RefNumber": "1709132", "RefComponent": "RE-FX-CF", "RefTitle": "Marking condition type as obsolete", "RefUrl": "/notes/1709132"}, {"RefNumber": "1705718", "RefComponent": "RE-FX-BP", "RefTitle": "Real Estate: SEPA mandate for business partner", "RefUrl": "/notes/1705718"}, {"RefNumber": "1697218", "RefComponent": "RE-FX-CN", "RefTitle": "Flag contract type as obsolete", "RefUrl": "/notes/1697218"}, {"RefNumber": "1693706", "RefComponent": "RE-FX-CO", "RefTitle": "Cross-line item reports for all real estate objects", "RefUrl": "/notes/1693706"}, {"RefNumber": "1689558", "RefComponent": "RE-FX-BD", "RefTitle": "Mass change of organizational assignment", "RefUrl": "/notes/1689558"}, {"RefNumber": "1678321", "RefComponent": "RE-FX", "RefTitle": "EhP6: SEPA mandate management for SAP Real Estate Management", "RefUrl": "/notes/1678321"}, {"RefNumber": "1663164", "RefComponent": "RE-FX-BD", "RefTitle": "Flag fixtures and fittings characteristic as \"obsolete\"", "RefUrl": "/notes/1663164"}, {"RefNumber": "1663110", "RefComponent": "RE-FX-SC", "RefTitle": "Information system: Status of settled advance payments", "RefUrl": "/notes/1663110"}, {"RefNumber": "1642677", "RefComponent": "RE-FX", "RefTitle": "Useful control parameters in RE-FX", "RefUrl": "/notes/1642677"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2227993", "RefComponent": "RE-FX", "RefTitle": "CC2014: Obsolete Customizing entries - measurement type", "RefUrl": "/notes/2227993 "}, {"RefNumber": "2063451", "RefComponent": "RE-FX", "RefTitle": "CC2014: Composite SAP Note for Customer Connection Real Estate 2014/2015", "RefUrl": "/notes/2063451 "}, {"RefNumber": "1938395", "RefComponent": "RE-FX-BD", "RefTitle": "BAdI for fixtures and fittings characteristics", "RefUrl": "/notes/1938395 "}, {"RefNumber": "1636166", "RefComponent": "RE-FX-CF", "RefTitle": "Cash flow comparison", "RefUrl": "/notes/1636166 "}, {"RefNumber": "1955450", "RefComponent": "RE-FX-CP", "RefTitle": "Invoice for reservations with one-time posting", "RefUrl": "/notes/1955450 "}, {"RefNumber": "1952842", "RefComponent": "RE-FX-RA", "RefTitle": "Condition split and extended withholding tax", "RefUrl": "/notes/1952842 "}, {"RefNumber": "1920102", "RefComponent": "RE-FX-CO", "RefTitle": "CO period reports: Value type selection", "RefUrl": "/notes/1920102 "}, {"RefNumber": "1814243", "RefComponent": "RE-FX-RA", "RefTitle": "RE-FX: Vendor contracts and FI-CA", "RefUrl": "/notes/1814243 "}, {"RefNumber": "1705718", "RefComponent": "RE-FX-BP", "RefTitle": "Real Estate: SEPA mandate for business partner", "RefUrl": "/notes/1705718 "}, {"RefNumber": "1735549", "RefComponent": "RE-FX-BP", "RefTitle": "Partner: Processing in business partner role grouping", "RefUrl": "/notes/1735549 "}, {"RefNumber": "939971", "RefComponent": "RE-FX", "RefTitle": "FAQ and consulting notes for RE-FX", "RefUrl": "/notes/939971 "}, {"RefNumber": "1642677", "RefComponent": "RE-FX", "RefTitle": "Useful control parameters in RE-FX", "RefUrl": "/notes/1642677 "}, {"RefNumber": "1820728", "RefComponent": "RE-FX-SC", "RefTitle": "Flagging service charge keys as 'obsolete'", "RefUrl": "/notes/1820728 "}, {"RefNumber": "1721652", "RefComponent": "RE-FX-RA", "RefTitle": "RERACL: Execute clearing for real estate contracts", "RefUrl": "/notes/1721652 "}, {"RefNumber": "1761084", "RefComponent": "RE-FX-RA", "RefTitle": "Invoice creation and FI-CA accounting system", "RefUrl": "/notes/1761084 "}, {"RefNumber": "1716812", "RefComponent": "RE-FX-SC", "RefTitle": "New itemization for settlement units", "RefUrl": "/notes/1716812 "}, {"RefNumber": "1663164", "RefComponent": "RE-FX-BD", "RefTitle": "Flag fixtures and fittings characteristic as \"obsolete\"", "RefUrl": "/notes/1663164 "}, {"RefNumber": "1711608", "RefComponent": "RE-FX-CF", "RefTitle": "Retaining condition lines during mode change", "RefUrl": "/notes/1711608 "}, {"RefNumber": "1709132", "RefComponent": "RE-FX-CF", "RefTitle": "Marking condition type as obsolete", "RefUrl": "/notes/1709132 "}, {"RefNumber": "1697218", "RefComponent": "RE-FX-CN", "RefTitle": "Flag contract type as obsolete", "RefUrl": "/notes/1697218 "}, {"RefNumber": "1693706", "RefComponent": "RE-FX-CO", "RefTitle": "Cross-line item reports for all real estate objects", "RefUrl": "/notes/1693706 "}, {"RefNumber": "1663110", "RefComponent": "RE-FX-SC", "RefTitle": "Information system: Status of settled advance payments", "RefUrl": "/notes/1663110 "}, {"RefNumber": "1689558", "RefComponent": "RE-FX-BD", "RefTitle": "Mass change of organizational assignment", "RefUrl": "/notes/1689558 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}