{"Request": {"Number": "1707579", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 599, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000010146292017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001707579?language=E&token=54B40E37D84C951B8BBFF22DF3C4C793"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001707579", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001707579/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1707579"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.11.2012"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DWB-DIC-AC"}, "SAPComponentKeyText": {"_label": "Component", "value": "ABAP Dictionary Activation and Conversion"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "ABAP Workbench, Java IDE and Infrastructure", "value": "BC-DWB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "ABAP Dictionary and ABAP CDS", "value": "BC-DWB-DIC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB-DIC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "ABAP Dictionary Activation and Conversion", "value": "BC-DWB-DIC-AC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB-DIC-AC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1707579 - SWT2DB: No check of bit in runtime object header"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You use the add-on SWT2DB (SAP Business Application Accelerator powered by HANA). The ABAP Dictionary check of a runtime object in transaction SE11 or SE14 issues errors for the runtime object header flag 2, bit 6 for tables or views that are used in SAP Business Application Accelerator scenarios. Activating the affected object does not correct the inconsistency.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The affected bit in the header of the runtime object is not checked by the ABAP Dictionary, and in particular, it is not changed during an activation. Moreover, the ABAP Dictionary does generally not know if the bit is currently set. As a consequence, the flag must not be checked in the ABAP Dictionary.<br />ABAP Dictionary mass checks (RUTMSJOB) do not provide default settings for bit 6 of flag 2 of the runtime object header. After you implement the corrections contained in this SAP Note, the system also ignores the bit during the runtime checks in transaction SE11 and SE14.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Implement the correction instructions or import the referenced Support Package.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (D022370)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D023980)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001707579/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001707579/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001707579/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001707579/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001707579/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001707579/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001707579/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001707579/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001707579/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1694697", "RefComponent": "BC-DB-DBI", "RefTitle": "SAP Business Application Accelerator powered by HANA", "RefUrl": "/notes/1694697"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1694697", "RefComponent": "BC-DB-DBI", "RefTitle": "SAP Business Application Accelerator powered by HANA", "RefUrl": "/notes/1694697 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70028", "URL": "/supportpackage/SAPKB70028"}, {"SoftwareComponentVersion": "SAP_BASIS 701", "SupportPackage": "SAPKB70113", "URL": "/supportpackage/SAPKB70113"}, {"SoftwareComponentVersion": "SAP_BASIS 710", "SupportPackage": "SAPKB71016", "URL": "/supportpackage/SAPKB71016"}, {"SoftwareComponentVersion": "SAP_BASIS 711", "SupportPackage": "SAPKB71111", "URL": "/supportpackage/SAPKB71111"}, {"SoftwareComponentVersion": "SAP_BASIS 720", "SupportPackage": "SAPKB72008", "URL": "/supportpackage/SAPKB72008"}, {"SoftwareComponentVersion": "SAP_BASIS 730", "SupportPackage": "SAPKB73008", "URL": "/supportpackage/SAPKB73008"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 8, "URL": "/corrins/0001707579/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 8, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 4, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "987217 ", "URL": "/notes/987217 ", "Title": "Runtime object is inconsistent in field DBLENGTH2", "Component": "BC-DWB-DIC-AC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1022798 ", "URL": "/notes/1022798 ", "Title": "Nametab inconsistent for field with structure components", "Component": "BC-DWB-DIC-AC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1511493 ", "URL": "/notes/1511493 ", "Title": "Disable activation type check for pooled/cluster tables", "Component": "BC-DWB-DIC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1511493 ", "URL": "/notes/1511493 ", "Title": "Disable activation type check for pooled/cluster tables", "Component": "BC-DWB-DIC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1480999 ", "URL": "/notes/1480999 ", "Title": "Error when checking table types with secondary indexes", "Component": "BC-DWB-DIC-AC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1511493 ", "URL": "/notes/1511493 ", "Title": "Disable activation type check for pooled/cluster tables", "Component": "BC-DWB-DIC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "710", "ValidTo": "710", "Number": "1480999 ", "URL": "/notes/1480999 ", "Title": "Error when checking table types with secondary indexes", "Component": "BC-DWB-DIC-AC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "710", "ValidTo": "710", "Number": "1511493 ", "URL": "/notes/1511493 ", "Title": "Disable activation type check for pooled/cluster tables", "Component": "BC-DWB-DIC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "711", "ValidTo": "711", "Number": "1480999 ", "URL": "/notes/1480999 ", "Title": "Error when checking table types with secondary indexes", "Component": "BC-DWB-DIC-AC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "711", "ValidTo": "711", "Number": "1511493 ", "URL": "/notes/1511493 ", "Title": "Disable activation type check for pooled/cluster tables", "Component": "BC-DWB-DIC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "720", "ValidTo": "720", "Number": "1480999 ", "URL": "/notes/1480999 ", "Title": "Error when checking table types with secondary indexes", "Component": "BC-DWB-DIC-AC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "720", "ValidTo": "720", "Number": "1511493 ", "URL": "/notes/1511493 ", "Title": "Disable activation type check for pooled/cluster tables", "Component": "BC-DWB-DIC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1511493 ", "URL": "/notes/1511493 ", "Title": "Disable activation type check for pooled/cluster tables", "Component": "BC-DWB-DIC"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}