{"Request": {"Number": "1453646", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 575, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000008576112017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001453646?language=E&token=BB593CCA1A93B68B4C735D237A2DE9B5"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001453646", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001453646/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1453646"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.04.2010"}, "SAPComponentKey": {"_label": "Component", "value": "IS-A-LMN"}, "SAPComponentKeyText": {"_label": "Component", "value": "Long Material Number"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Automotive", "value": "IS-A", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-A*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Long Material Number", "value": "IS-A-LMN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-A-LMN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1453646 - LSMW: MATNR_NUMBER_CREATE int matnr with leading exclamation"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>If you create a lot of similar long material numbers with LSMW, new internal material numbers with leading exclamtion mark will be created.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>LSMW<br />MATNR_NUMBER_CREATE&#x00A0;&#x00A0;exclamation</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Program error.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Apply the correction instruction.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "IS-ADEC-MPN (Manufacturer Part Number)"}, {"Key": "Responsible                                                                                         ", "Value": "D021411"}, {"Key": "Processor                                                                                           ", "Value": "D021411"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001453646/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001453646/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001453646/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001453646/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001453646/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001453646/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001453646/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001453646/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001453646/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "735529", "RefComponent": "IS-A-LMN", "RefTitle": "FAQ: DIMP/DI long material number and MPN", "RefUrl": "/notes/735529"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "735529", "RefComponent": "IS-A-LMN", "RefTitle": "FAQ: DIMP/DI long material number and MPN", "RefUrl": "/notes/735529 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "DIMP", "From": "471", "To": "471", "Subsequent": ""}, {"SoftwareComponent": "ISPSADIN", "From": "10A", "To": "10A", "Subsequent": ""}, {"SoftwareComponent": "ECC-DIMP", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "ECC-DIMP", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "ECC-DIMP", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "ECC-DIMP", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "ECC-DIMP", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "ECC-DIMP", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "DI", "From": "46C2", "To": "46C2", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "ECC-DIMP 500", "SupportPackage": "SAPKIPMH13", "URL": "/supportpackage/SAPKIPMH13"}, {"SoftwareComponentVersion": "ECC-DIMP 600", "SupportPackage": "SAPK-60018INECCDIMP", "URL": "/supportpackage/SAPK-60018INECCDIMP"}, {"SoftwareComponentVersion": "ECC-DIMP 602", "SupportPackage": "SAPK-60208INECCDIMP", "URL": "/supportpackage/SAPK-60208INECCDIMP"}, {"SoftwareComponentVersion": "ECC-DIMP 603", "SupportPackage": "SAPK-60307INECCDIMP", "URL": "/supportpackage/SAPK-60307INECCDIMP"}, {"SoftwareComponentVersion": "ECC-DIMP 604", "SupportPackage": "SAPK-60407INECCDIMP", "URL": "/supportpackage/SAPK-60407INECCDIMP"}, {"SoftwareComponentVersion": "ECC-DIMP 605", "SupportPackage": "SAPK-60502INECCDIMP", "URL": "/supportpackage/SAPK-60502INECCDIMP"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "ECC-DIMP", "NumberOfCorrin": 2, "URL": "/corrins/0001453646/591"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 5, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "ECC-DIMP", "ValidFrom": "500", "ValidTo": "500", "Number": "803231 ", "URL": "/notes/803231 ", "Title": "MM01: internal material number with exclamation mark (!)", "Component": "IS-A-LMN"}, {"SoftwareComponent": "ECC-DIMP", "ValidFrom": "500", "ValidTo": "500", "Number": "1034439 ", "URL": "/notes/1034439 ", "Title": "Internal material number with exclamation if MPN activated", "Component": "IS-A-LMN"}, {"SoftwareComponent": "ECC-DIMP", "ValidFrom": "500", "ValidTo": "500", "Number": "1238910 ", "URL": "/notes/1238910 ", "Title": "Performance while loading materials with similar numbers", "Component": "IS-A-LMN"}, {"SoftwareComponent": "ECC-DIMP", "ValidFrom": "600", "ValidTo": "600", "Number": "1034439 ", "URL": "/notes/1034439 ", "Title": "Internal material number with exclamation if MPN activated", "Component": "IS-A-LMN"}, {"SoftwareComponent": "ECC-DIMP", "ValidFrom": "600", "ValidTo": "600", "Number": "1084779 ", "URL": "/notes/1084779 ", "Title": "MATNR_NUMBER_CREATE: internal numbers with leading (!)", "Component": "IS-A-LMN"}, {"SoftwareComponent": "ECC-DIMP", "ValidFrom": "600", "ValidTo": "600", "Number": "1238910 ", "URL": "/notes/1238910 ", "Title": "Performance while loading materials with similar numbers", "Component": "IS-A-LMN"}, {"SoftwareComponent": "ECC-DIMP", "ValidFrom": "600", "ValidTo": "605", "Number": "1440038 ", "URL": "/notes/1440038 ", "Title": "MM01: Error MPN01 041", "Component": "IS-ADEC-MPN"}, {"SoftwareComponent": "ECC-DIMP", "ValidFrom": "602", "ValidTo": "602", "Number": "1034439 ", "URL": "/notes/1034439 ", "Title": "Internal material number with exclamation if MPN activated", "Component": "IS-A-LMN"}, {"SoftwareComponent": "ECC-DIMP", "ValidFrom": "602", "ValidTo": "602", "Number": "1084779 ", "URL": "/notes/1084779 ", "Title": "MATNR_NUMBER_CREATE: internal numbers with leading (!)", "Component": "IS-A-LMN"}, {"SoftwareComponent": "ECC-DIMP", "ValidFrom": "602", "ValidTo": "602", "Number": "1238910 ", "URL": "/notes/1238910 ", "Title": "Performance while loading materials with similar numbers", "Component": "IS-A-LMN"}, {"SoftwareComponent": "ECC-DIMP", "ValidFrom": "603", "ValidTo": "603", "Number": "1238910 ", "URL": "/notes/1238910 ", "Title": "Performance while loading materials with similar numbers", "Component": "IS-A-LMN"}, {"SoftwareComponent": "ECC-DIMP", "ValidFrom": "604", "ValidTo": "604", "Number": "1238910 ", "URL": "/notes/1238910 ", "Title": "Performance while loading materials with similar numbers", "Component": "IS-A-LMN"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}