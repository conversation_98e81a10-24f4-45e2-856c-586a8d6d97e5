{"Request": {"Number": "840602", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 371, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015887232017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000840602?language=E&token=7808FB329491C441976A02F1A7F0F2D2"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000840602", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "840602"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.04.2005"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-PLA-RAP"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW only - Retail Assortment Planning"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Planning", "value": "BW-BCT-PLA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-PLA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Retail Assortment Planning", "value": "BW-BCT-PLA-RAP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-PLA-RAP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "840602 - Determining the season/generic article for mode in MAP"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>When you update the 0RMP_DS01 and 0RMP_DS02ODS objects, the season is not updated correctly. In addition, the variant, rather than the generic article, is updated in the mode case.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>MAP, assortment planning, 0RMP_MC21.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>A prerequisite is to use the SAP ERP, where you have maintained the color and size for the generic article, as well as the seasonal pattern (season, season year and the collection). You use the standard 0MATERIAL_ATTR extraction for the article.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>In the SAP Retail MAP, the season is determined from the 0RF_SKU or 0CM_SKU InfoObjects. If the season is not maintained there, up to now neither the season nor the generic article are updated - only the variant is updated. However, from the SAP ERP the season, the season year and the collection can only be transferred using 0MATERIAL_ATTR on 0MATERIAL. Therefore, these are distribution-independent or category-independent seasonal settings on the article. If this case also needs to be taken into account, the following program lines must be included in the update of the article (Style/Product) in 0RMP_DS01 and 0RMP_DS02 (all updates of the logistics extraction 2LIS_02_SCL, 2LIS_03_BF, 2LIS_40_REVAL and 2LIS_03_UM as well as 0RT_PA_TRAN_CONTROL are affected here).<br />Replace the following program lines (after the sy-subrc of the RS_BCT_CM_READ_CDT function module is processed)<br /><br /><br />* result value of the routine<br />&#x00A0;&#x00A0;if ( glob_matstruc-rt_color&#x00A0;&#x00A0; is initial or<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; glob_matstruc-rt_size&#x00A0;&#x00A0;&#x00A0;&#x00A0;is initial or<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; glob_cdtstruc-rt_season&#x00A0;&#x00A0;is initial or<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; glob_cdtstruc-rt_seasyr&#x00A0;&#x00A0;is initial or<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; glob_cdtstruc-rt_skuroll is initial ).<br />* Basic product<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;RESULT = COMM_STRUCTURE-material.<br />&#x00A0;&#x00A0;else.<br />* Fashion! Upload the generic<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;if glob_matstruc-rt_confmat is initial.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RESULT = COMM_STRUCTURE-material.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;else.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RESULT = glob_matstruc-rt_confmat.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;endif.<br />&#x00A0;&#x00A0;endif.<br /><br />durch den folgenden Programmcode<br /><br />&#x00A0;&#x00A0;If glob_cdtstruc-rt_season is initial.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; glob_cdtstruc-rt_season&#x00A0;&#x00A0;= glob_matstruc-rt_season.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; glob_cdtstruc-rt_seasyr&#x00A0;&#x00A0;= glob_matstruc-rt_seasyr.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; glob_cdtstruc-rt_skuroll = glob_matstruc-rt_searoll.<br />&#x00A0;&#x00A0;Endif.<br />* result value of the routine<br />&#x00A0;&#x00A0;if ( glob_matstruc-rt_color&#x00A0;&#x00A0; is initial or<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; glob_matstruc-rt_size&#x00A0;&#x00A0;&#x00A0;&#x00A0;is initial or<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; glob_cdtstruc-rt_season&#x00A0;&#x00A0;is initial or<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; glob_cdtstruc-rt_seasyr&#x00A0;&#x00A0;is initial ).<br />* Basic product<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;RESULT = COMM_STRUCTURE-material.<br />&#x00A0;&#x00A0;else.<br />* Fashion! Upload the generic<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;if glob_matstruc-rt_confmat is initial.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RESULT = COMM_STRUCTURE-material.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;else.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RESULT = glob_matstruc-rt_confmat.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;endif.<br />&#x00A0;&#x00A0;endif.<br /><br />This results in the season, season year and collection being updated from 0MATERIAL if the season is not stored on 0RF_SKU/0CM_SKU. The collection is optional in this case, that is, the generic article is also updated if only the season and season year is maintained.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "I802868"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D026559)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000840602/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000840602/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000840602/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000840602/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000840602/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000840602/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000840602/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000840602/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000840602/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "586163", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Composite SAP Note for SAP ERP Inventory Management in SAP BW", "RefUrl": "/notes/586163 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BI_CONT", "From": "352", "To": "352", "Subsequent": ""}, {"SoftwareComponent": "BI_CONT", "From": "353", "To": "353", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}