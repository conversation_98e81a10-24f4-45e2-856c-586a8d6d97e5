{"Request": {"Number": "1071989", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 2076, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016335222017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001071989?language=E&token=C4522F1C1CBE06A4A3EB8E7C9EB5B69A"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001071989", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001071989/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1071989"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 42}, "Recency": {"_label": "Currentness", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Correction of legal function"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.07.2007"}, "SAPComponentKey": {"_label": "Component", "value": "PY-AT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Austria"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Austria", "value": "PY-AT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-AT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1071989 - UI contribution for men as of 56 (via clearing groups)"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1071989&TargetLanguage=EN&Component=PY-AT&SourceLanguage=DE&Priority=04\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/1071989/D\" target=\"_blank\">/notes/1071989/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>************************************************************************<br />ATTENTION: The SAP Note has changed on July 24, 2007. The changes<br />are marked with ******** at the end of the note.<br />************************************************************************<br />Our previous recommendation about the procedure for the recalculation of UI contributions (SAP Note 1039538) with variant 2 - recalculation (contribution group old vs. new) is now not recognized by all funds.</p> <UL><LI>Variant 2 was first published by the OÖ-GKK at the end of April and was fully applicable to all affected employees in its initial version.</LI></UL> <UL><LI>We also have written commitments from the Vienna GKK and the NÖ-GKK to accept variant 2 without restriction.</LI></UL> <UL><LI>However, we were informed that the Kärnter-GKK and the Steiermarktkische-GKK were not informed about this and that the above mentioned procedure completely refused to do so.</LI></UL><UL><LI>On June 12, 2007, the OÖ-GKK also spontaneously added variant 2 to the body text in its publication and restricted it to only active employees (homepage www.ooegkk.at). This implicitly means that variant 1 is to be used for employees who have left the company as of now.</LI></UL> <UL><LI>Variant 2 can only be used for active employees.</LI></UL> <UL><LI>Variant 1 can be used for all affected employees.</LI></UL> <UL><LI>If you have not yet reported the affected employees using ELDA, we therefore recommend that you use variant 1 for all employees.</LI></UL><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>UI Contribution for Men from 56<br />Clearing groups N15g N25g N15m N25m N15n N25n<br />VwGH 2005/08/0057-7 from 20th December 2006</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>Amendment of the implementing rules.<br />The change affects only the display of the UI contributions for which retroactive accounting has been performed in the monthly contribution statement (RPCSVBA2), both in the list form and in the ELDA data file. The clearing groups according to variant 1 (difference recalculation) are now displayed here.<br />A new payroll run is therefore required  n i c h t  .</p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><p>With the delivery according to this SAP Note, it is now possible to use the monthly contribution statement (RPCSVBA2) to perform the retroactive clearing of UI contributions with variant 1 (difference recalculation).<br />Import the HR Support Package relevant for your system or note the adjustments contained in the attachment (advance delivery as an SAR file) for your relevant release.</p> <b>The advance delivery contains all previous deliveries<br />You can find the corresponding numbers under Attachments</b><br /> <UL><LI>For SAP ERP 2005:        L7DKnnnnnn.SAR</LI></UL> <UL><LI>For SAP ERP 2004:        L6DKnnnnnn.SAR</LI></UL> <UL><LI>For SAP R/3 Enterprise:  L6BKnnnnnn.SAR</LI></UL> <UL><LI>For SAP 4.6C:            L9CKnnnnnn.SAR<br /></LI></UL> <p>Then reconcile all objects with your productive client.<br />In particular, check the call of subschema AJSY in subschema ASV0.</p> <b>Procedure:</b><br /> <p>Recalculation takes place in two steps:</p> <OL>1. Execute the monthly contribution statement RPCSVBA2. First, activate the new radio button &quot;Variant 1 (SAP Note#1071989)&quot; and select the checkbox &quot;Separate Run for Difference Clearing&quot; with &#39;x&#39;.</OL> <p>              In this run, the subsequent adjustments are determined and assigned to the clearing groups N15g, N25g, N15m, N25m, N15n, or N25n. This is based on the contribution basis that was valid together with the old contribution group. All difference amounts determined in this way for each evaluation period are assigned cumulatively to the in-period of the evaluation (this is a specification for social insurance). Any amounts that cannot be recalculated that have already been reimbursed by AMS due to semiretirement are offset (wage type /9ZU, /9ZV). Any additional arrears payments from past calendar years are not taken into account in this run. <OL>2. Introduce the monthly contribution statement RPCSVBA2 for the same period as the second times. First, activate the new radio button &quot;Variant 1 (SAP Note#1071989)&quot; and deselect the checkbox &quot;Separate Run for Difference Clearing&quot;.</OL> <p>              In this run, the contribution statement with all contribution groups is created as usual. Arrears payments from past calendar years are already taken into account in this run on the basis of the new bonus contribution groups.<br /> <p>If you want to reimburse or correct UI contributions for other affected employees in subsequent periods, perform the procedure described above again (points 1 and 2).<br />If you no longer want to reimburse or correct UI contributions in subsequent periods, set the new parameters as described in point 2. These new parameters are automatically hidden and inactive as of the year 2008 and for periods as of 01/2008.<br /></p> <b>Further Remarks</b><br /> <UL><LI>The UI contributions for which retroactive accounting has been run are absolutely identical for variant 1 and variant 2. Only display format and assignment to periods and contribution groups or Clearing groups are different using the list display and the ELDA data file. If you have already sent your ELDA dataset for all of your affected employees using variant 2 via ELDA, you do not need to take any additional action.</LI></UL> <p>******** Start<br />The correction is relevant for you only if payroll was run for semiretirement for the affected employees:<br />The UI reimbursement may not be determined correctly for semiretirement:<br />If you have imported the SAR file according to the SAP Note before July 24, 2007, import the new updated attachment or alternatively replace the following line in the source code of RPCSVBA2: * h_allbg = h_eval_tab-allbg + p_eval_tab-bgatz.        &quot;&lt;&lt;&lt;&lt; DELETE<br />  h_allbg = h_eval_tab-allbg +                          &quot;&lt;&lt;&lt;&lt; INSERT<BR/>  ( p_eval_tab-bgatz * p_eval_tab-procs / h_Prozs ).    &quot;&lt;&lt;&lt;&lt; INSERT<br />Then repeat the evaluation for the affected employees.<br />******** End</p></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Owner                                                                                    ", "Value": "D001888"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON> (D022643)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001071989/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "L7DK053695.SAR", "FileSize": "118", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000257782007&iv_version=0042&iv_guid=C61A9F00967E004AA0E75215FE30091F"}, {"FileName": "L6DK073792.SAR", "FileSize": "86", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000257782007&iv_version=0042&iv_guid=F6E662387CE92141A4D9021CCBA7C248"}, {"FileName": "L6BK143269.SAR", "FileSize": "85", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000257782007&iv_version=0042&iv_guid=F1FC48CB7813AA47AACD7EF49044FB98"}, {"FileName": "L9CK231929.SAR", "FileSize": "80", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000257782007&iv_version=0042&iv_guid=67EEB2F3CA8A1342867ED5BFE4234DF7"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1110486", "RefComponent": "PY-AT", "RefTitle": "Retroactive accounting for subsequent period and UI contribution for men as of 56", "RefUrl": "/notes/1110486"}, {"RefNumber": "1039538", "RefComponent": "PY-AT", "RefTitle": "Unemployment insurance contribution for men from 56", "RefUrl": "/notes/1039538"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "1039538", "RefComponent": "PY-AT", "RefTitle": "Unemployment insurance contribution for men from 56", "RefUrl": "/notes/1039538 "}, {"RefNumber": "1110486", "RefComponent": "PY-AT", "RefTitle": "Retroactive accounting for subsequent period and UI contribution for men as of 56", "RefUrl": "/notes/1110486 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46CC6", "URL": "/supportpackage/SAPKE46CC6"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47071", "URL": "/supportpackage/SAPKE47071"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50037", "URL": "/supportpackage/SAPKE50037"}, {"SoftwareComponentVersion": "SAP_HR 600", "SupportPackage": "SAPKE60020", "URL": "/supportpackage/SAPKE60020"}]}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1071989&TargetLanguage=EN&Component=PY-AT&SourceLanguage=DE&Priority=04\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/1071989/D\" target=\"_blank\">/notes/1071989/D</a>."}}}}