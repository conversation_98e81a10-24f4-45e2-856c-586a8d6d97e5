{"Request": {"Number": "1799113", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 401, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017571732017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001799113?language=E&token=796482B2A9A908EDCCEEF5EADE1134AF"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001799113", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001799113/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1799113"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "03.10.2013"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-PL-IS-U"}, "SAPComponentKeyText": {"_label": "Component", "value": "use FI-LOC-UT-PL"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Poland", "value": "XX-CSC-PL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-PL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-Spec. Component", "value": "XX-CSC-PL-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-PL-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "use FI-LOC-UT-PL", "value": "XX-CSC-PL-IS-U", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-PL-IS-U*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1799113 - CEEISUT:Localization for Poland:Budget Billing and Invoicing"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>SAP CEEISUT add-on Poland localization for country specific functionality with&#x00A0;&#x00A0;installation and documentation details</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>CEEISUT, Localization for Poland, Budget Billing and Invoicing, Weighting key, Budget Billing Plan, Industrial Customers, Correction Invoice, Reactive Power Consumption,&#x00A0;&#x00A0;EASIBI, EA11, EA21, WK, BBIC, //SAPCE/PL_BBPIC, /SAPCE/IU_WKBB, /SAPCE/IU_WKBBD, /SAPCE/IU_WKBBT, /SAPCE/IUWK, KZABSVER, /SAPCE/IUPL_BBIISU Localization for Poland: Budget billing and Invoicing, /SAPCE/IUPL_BBI_SFWS_01, ISU Localization for Poland: BBI - switch, /SAPCE/ISU_INV_PL_SFWS_01, Business Function, /SAPCE/ISU_INV_PL,&#x00A0;&#x00A0;package , /SAPCE/IUPL_EVENT_R960, /SAPCE/IUPL_EVENT_R994, /SAPCE/IUPL_CORR_INV,<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You have to install add-on CEEISUT Rel.6.06, SP level 5 It contains IS-U/CCS localization for CEE countries.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Note: <br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;o&#x00A0;&#x00A0;These local enhancements are part of the Poland country version &#x00A0;&#x00A0; of IS-U/CCS delivered in add-on CEEISUT 6.06<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;o&#x00A0;&#x00A0;Business Function /SAPCE/ISU_INV_PL<br />....o&#x00A0;&#x00A0;Switch Framework /SAPCE/ISU_INV_PL_SFWS_01<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;o&#x00A0;&#x00A0;These enhancements are not released for any other country.<br /><br />***********************************************************************<br /><br />.&#x00A0;&#x00A0;Overview of enhancements<br /></p> <b>&#x00A0;&#x00A0;&#x00A0;&#x00A0;Budget Billing Plan for Industrial/Large customers (BBIC)</b><br /> <p><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;For the industrial customers there is a request for a specific<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;procedure for determination of the budget billing plan (BBP) with<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;following basic characteristics:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;o Post real partial bills and settlement invoice, meaning &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;posting in general ledger and not a statistical posting for&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Industrial customers<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;o Multiple (up to five) budget billing request for one billing<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;period(max. of 1 month)<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;o The \"fixed\" due dates (parameters) of budget bills on<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;any date of the month, (Polish calendar of the work days &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;is taken into account) or individually negotiable dates. It&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; can be defined in the contract.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;o Generation of BBP for the next billing period during &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;creation of invoice for current period.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;o Fixed amount and fixed quantity can be defined for BB &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Request in the contract.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Page 2<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This functionality is delivered in following function   modules which&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;needs to be attached with events &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;R994, R960 and R993 in FQEVENTS transaction:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;&#x00A0;&#x00A0; /SAPCE/IUPL_EVENT_R960 (Generation of BB requests for &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Poland, Define Multiple Due Dates for Plan)),<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;&#x00A0;&#x00A0; /SAPCE/IUPL_EVENT_R994 (Generation of BB requests for &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Poland, Change BB Amount for Single Items),<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;&#x00A0;&#x00A0; /SAPCE/IUPL_EVENT_R993(Change Tax Code on saving BBP)<br /><br /> Manual Activities<br /> Required Manual Activities are available in the attachment<br /> \"Poland_ISU_Manual_Activities.zip.pdf\"<br /><br /> Solution-specific customizing:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Maintain portions having installations belonging to &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;industrial customer for Budget Billing, with currency,&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;multiple due dates and amounts to generate multiple budget&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;billing requests (for BB partial bill procedure) during a&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; period of one month for segregation between BBP data&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;maintained for small/mass and large/Industrial customers<br /><br /> To access customizing you can use one of the following transactions:<br /> o&#x00A0;&#x00A0;SPRO - SAP Utilities -&gt; Invoicing -&gt; Budget Billing Localization for Poland -&gt;Define Default Parameters<br />  </p> <b>&#x00A0;&#x00A0;&#x00A0;&#x00A0; Budget Billing Plan for Small/Mass customers</b><br /> <b></b><br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;For small customers there is a request for a specific<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;procedure for determination of the budget billing plan (BBP) with<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;following basic characteristics:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;o Post real partial bills and settlement invoice, meaning &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;posting in general ledger and not a statistical posting for&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; small customers<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;o The budget bills (BB) generated are real (Partial Bill &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Procedure for Budget Billing Amount)<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;o Multiple budget billing request for one billing<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;period(Period can vary from 3 - 12 months)<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;o Generation of BBP for the next billing period during &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;creation of invoice for current period.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;o System extrapolated amount and quantity get calculated for &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BB Request in the contract<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;o Maintain weighing key to modify and build the budget billing&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; plan amount on real consumption. It should only be defined&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;through 12 values for each month in a year. Sum of the&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;percentage values of 12 month have to be 100%.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;o The program reads the weights of weighting key, that is set  on the third tab of contract, from table /SAPCE/IU_WKBBD. Then program calculates the amount for each month and replaces the proposed constant amount in budget billing   plan.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;o Maintain rates and budget billing debit sub transactions, &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;which are used for calculating flat rate amount, so as to &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;modify the BB amount calculation when weighing key is &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;specified in the contract.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;o Partial bills generated for small customers should contain: &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1.Estimated consumption quantity with units and amounts for &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;budget billing requests of current billing period.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2.BB requests with estimated consumption and extrapolated &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;amounts divided on the basis of weighing key.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;3.Sales date, amount of excise tax for current BB period &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;and VAT registration number of seller and buyer<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;o Settlement Invoice generated for small customers should &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; contain:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1.Budget billing requests for the current period with due &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;dates and consumption quantity with units, as well as the&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;estimated consumption quantities with units and estimated&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;amounts with units for all future budget billing requests<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2.BB requests with estimated consumption and extrapolated &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;amounts divided on the basis of weighing key.<br /><br />  This functionality is delivered in following function   modules which needs to be attached with events R994 and R993  in FQEVENTS transaction:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;/SAPCE/IUPL_EVENT_R994 (Generation of BB requests for &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Poland, Change BB Amount for Single Items),<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/SAPCE/IUPL_EVENT_R993(Change Tax Code on saving BBP)<br /><br /> Manual Activities \"<br /> Required Manual Activities are available in the attachment<br /> \"Poland_ISU_Manual_Activities.zip.pdf\"<br /><br /> Solution-specific customizing:<br /><br /> You can define Weighting keys, to access customizing you can use one of the following transactions:<br /><br /> o&#x00A0;&#x00A0;SPRO - SAP Utilities -&gt; Invoicing -&gt; Budget Billing Localization for Poland -&gt;Define Weighting Keys for BBP<br /><br /> You can also define components not relevant for Weighing Key Calculation using following IMG configuration:<br /><br /> o&#x00A0;&#x00A0;SPRO - SAP Utilities -&gt; Invoicing -&gt; Budget Billing &#x00A0;&#x00A0;Localization for Poland -&gt;Define Components Not Relevant for  &#x00A0;&#x00A0;Weighing Key Calculation<br /></p> <b>&#x00A0;&#x00A0;&#x00A0;&#x00A0;Charge for Excessive Reactive Power Consumption</b><br /> <p><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;A new variant program was created to calculate the charge for excessive reactive power consumption using a country specific formula for Poland.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;o The variant program can be added in the rate steps / billing&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; schema, that would be executed at the time of billing&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; process.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;o Not all customers are entitled to the calculation of the &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;charge for excessive reactive power. The customer can be &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;included to this charge by adding the variant in the &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;billing schema or rate categories.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;o The variant program indicator is not set to generate billing&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;line items relevant to posting.Info lines are generated&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;which are not relevant for posting.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;o The amount of reactive power is measured in KWH in the &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;system. No conversion is done for the calculation of &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;coefficient for reactive energy.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;o Activate BC SET /CEEISUT/ISU_PL_03_BBPINV, if not active.in  transaction SCPR20.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;This funcionality is delivered in following function module:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;ISU__CE_RPC (Poland: Determine Charge for Reactive Power &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Consumption).<br /></p> <b>&#x00A0;&#x00A0;&#x00A0;&#x00A0;Corrective Invoice</b><br /> <p><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Customer specific application forms have to be created to display the correction invoice for partial documents and settlement documents.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;o Correction Invoice will display the details of the &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;corrected settlement document and the corrected partial&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; document.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;o Correction invoice will display the details of the Corrected&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; document<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;o Information like Sales date, Excise Tax, Vat Registration &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Number etc<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;o Correction Invoices will display the details of the &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;correction like \"WAS\" , \"SHOULD BE\"&#x00A0;&#x00A0;and \"DIFFERENCES\" with&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; their relevant details under them.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;o Quantity of each line item in the partial bill is displayed &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; for the correction invoice partial document.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;o Steps for the Configuration of the below mentioned includes &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;and structures are used in the form class and form which is &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;available in the attachment \"Manual Steps for Poland.pdf\"<br /><br /> The following includes are used under their corresponding nodes in the application form to display the above mentioned details of correction invoice:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;/SAPCE/ISU_PL_CHK_DOC_CORR<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;/SAPCE/BBP_PART_INV_UNITS_PL<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;/SAPCE/IUPL_LINE_ITM_HEADING<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;/SAPCE/BBP_MRTYPE_PL<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;/SAPCE/IUPL_GET_DELTA<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;/SAPCE/IUPL_GET_BBP_REVDOC<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;/SAPCE/BBP_CONSM_UNITS_PL<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;/SAPCE/BBP_LCP_VATREG_PL<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;/SAPCE/IUPL_PART_INV<br /><br /> &#x00A0;&#x00A0;&#x00A0;&#x00A0;Following structures are used in the form class during the creation of the relevant nodes for displaying of correction invoice details:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;/SAPCE/S_UTL_VAT<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;/SAPCE/ISU_PL_DOC_CORRECTED<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;/SAPCE/ISU_BI_BILL_S_MRTYPE<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;/SAPCE/ISU_INV_AMT_QUAN_DLTA<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;/SAPCE/ISU_INV_CORR_PART_INV<br /><br /> Required Manual Activities are available in the attachment \"Manual Steps for Poland.pdf\"<br /></p> <b>&#x00A0;&#x00A0;&#x00A0;&#x00A0; IS-U/CRM Contract Integration- Addition of New Fields</b><br /> <p><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The following steps have to be carried out in order to add the &#x00A0;&#x00A0;new fields (BBP related fields specific to Poland) to the contract &#x00A0;&#x00A0;screen:<br /> &#x00A0;&#x00A0;&#x00A0;&#x00A0;1. Simplified Configuration with Container Storage(BUS2000149)<br /> 2. Enhancement of Utility Contract using Easy Enhancement   &#x00A0;&#x00A0;Workbench<br /><br /> &#x00A0;&#x00A0;In addition required&#x00A0;&#x00A0;activities are available in the attachment<br /> &#x00A0;&#x00A0;\"Enhancement_of_Contract_in_CRM.zip\" more details on IS-U/CRM &#x00A0;&#x00A0;replication can be found in attachement  \"ISU-CRM_Standard_Guides.zip\"<br /><br /></p> <b></b><br /> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I060454)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I046704)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001799113/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001799113/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001799113/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001799113/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001799113/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001799113/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001799113/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001799113/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001799113/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Enhancement_of_Contract_in_CRM.zip", "FileSize": "671", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000899122012&iv_version=0002&iv_guid=D2D80290C80717468CEF701883A4B006"}, {"FileName": "Poland_ISU_KW.zip", "FileSize": "749", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000899122012&iv_version=0002&iv_guid=F805F5D82B71B5428CF8434E41299A05"}, {"FileName": "Poland_ISU_Manual_Activities.zip", "FileSize": "1925", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000899122012&iv_version=0002&iv_guid=FF667F1C0D117241958910B8FB4286FE"}, {"FileName": "ISU-CRM_Standard_Guides.zip", "FileSize": "1152", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000899122012&iv_version=0002&iv_guid=390C6A0E86366C488AFCB61AF9DDECF5"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1866443", "RefComponent": "XX-CSC-PL-IS-U", "RefTitle": "CEEISUT Add-on : IS-U Localization for Poland", "RefUrl": "/notes/1866443"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2400430", "RefComponent": "XX-CSC-PL-IS-U", "RefTitle": "CEEISUT: IS-U Localization for Poland- Creation of BAdI Implementation for existing BAdI Definition", "RefUrl": "/notes/2400430 "}, {"RefNumber": "1866443", "RefComponent": "XX-CSC-PL-IS-U", "RefTitle": "CEEISUT Add-on : IS-U Localization for Poland", "RefUrl": "/notes/1866443 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "CEEISUT", "From": "606", "To": "606", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "CEEISUT 606", "SupportPackage": "SAPK-60607INCEEISUT", "URL": "/supportpackage/SAPK-60607INCEEISUT"}, {"SoftwareComponentVersion": "CEEISUT 606", "SupportPackage": "SAPK-60605INCEEISUT", "URL": "/supportpackage/SAPK-60605INCEEISUT"}, {"SoftwareComponentVersion": "CEEISUT 606", "SupportPackage": "SAPK-60604INCEEISUT", "URL": "/supportpackage/SAPK-60604INCEEISUT"}, {"SoftwareComponentVersion": "CEEISUT 606", "SupportPackage": "SAPK-60606INCEEISUT", "URL": "/supportpackage/SAPK-60606INCEEISUT"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}