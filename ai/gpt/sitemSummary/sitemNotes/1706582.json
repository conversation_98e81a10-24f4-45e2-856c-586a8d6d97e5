{"Request": {"Number": "1706582", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 647, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000010140472017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001706582?language=E&token=8315A416960079983B732FAC882AF939"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001706582", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001706582/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1706582"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.01.2016"}, "SAPComponentKey": {"_label": "Component", "value": "CA-EPT-ANL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Analytics Infrastructure"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Processes and Tools for Enterprise Applications", "value": "CA-EPT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-EPT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Analytics Infrastructure", "value": "CA-EPT-ANL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-EPT-ANL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1706582 - CRM Data Sources released for ODP data replication API"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You would like to use the ODP data replication API to replicate data from your CRM System using SAP BusinessObjects Data Services. This SAP Note describes which Data Sources have been released by SAP to be used in this scenario.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>ETL Interface, Operational Delta Queue</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>To use the ODP data replication API the Support Packages as described in SAP Note 1521883 have to be installed.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Attention: This note has been merged with others into the new Note 2232584, which allows to release all tested Extractors at once. </strong><strong>It is adviced to use the new note instead.</strong></p>\r\n<p><strong>___________________________________________</strong></p>\r\n<p>The Data Sources listed below have been released for usage with ODP Data Replication API.<br /><br />To use the ODP data replicataion API for any generic Data Source(extraction methods view extraction or domain extraction) you need to implemenent SAP Note 1585204.<br /><br /><br />You can release the Data Sources (listed below) by running the attached report Z_ANLY_DS_RELEASE_CRM (Type: Executable Program) in each of your systems.<br /><br /><br />0BP_DEF_ADDRESS_ATTR<br />0BP_RELATIONS_ATTR<br />0BPARTNER_ATTR<br />0BPARTNER_TEXT<br />0CRM_BP_FUNCTIONS_ATTR<br />0CRM_HR_PA_OS_1<br />0CRM_MKTATTR_TEXT<br />0CRM_ORGUNIT_SALES_ATTR<br />0CRM_ORGUNIT_SERVICE_ATTR<br />0CRM_POSITION_ATTR<br />0CRM_SALES_ORDER_I<br />0CRM_SRV_CONFIRM_H<br />0CRM_SRV_CONFIRM_I<br />0CRM_SRV_PROCESS_H<br />0CRM_SRV_REQ_PROB_H<br />0CSM_USER_TEXT<br />0PR_STATUS_ATTR<br />0PROD_CATEG_ATTR<br />0PROD_CATEG_TEXT<br />0PRODUCT_ATTR<br /><br />0BP_CCARD_ATTR<br />0CRM_CRM_FS_VARIANT_ATTR<br />0CRM_MKTATTR_ATTR<br />0CRM_PR_FIN01_ATTR<br />0CRM_PR_FIN02_ATTR<br />0CRM_PR_FIN03_ATTR<br />0CRM_SALES_ACT_1<br />0CRM_SRV_PROCESS_I<br />0PRODUCT_TEXT<br /><br />0BP_ID_TYPE_TEXT<br />0BP_ROLE_TEXT<br />0BP_ROLES_ATTR<br />0BPTYPE_TEXT<br />0COUNTRY_TEXT<br />0CRM_ITM_TYPE_TEXT<br />0CRM_TRAS_ATTR<br />0CRMT_FS_FLOWCAT_E_TEXT<br />0IND_SECTOR_TEXT<br />0LEGALFORM_TEXT<br />0PR_CATEGORIES_ATTR<br /><br />0CRM_COMPLAINTS_I<br />0CRM_SALES_CONTR_I<br />0CRM_SRV_REQ_INCI_H<br />0CRM_USERSTATUS_TEXT<br />0CRM_CATEGORY_ATTR<br />0CRM_PRIO_TXT<br />0CRM_USERSTATUS_TEXT<br />0CRM_PROC_TYPE_TEXT<br /><br />0BANKKEY_TEXT<br />0BP_BANK_ATTR<br />0BP_TAXCAT_TEXT<br />0BP_TAXNUMB_ATTR<br />0CCARDTYPE_TEXT<br />0CRM_ORGUNIT_TEXT<br />0CRM_TAXCLASS_TEXT<br />0CRM_TRLV_TEXT<br />0CSM_CATE_TEXT<br />0CSM_ESCA_TEXT<br />0CSM_PRCS_TEXT<br />0CSM_PRIO_TEXT<br />0CSM_RCOD_TEXT<br />0CSM_SECU_TEXT<br />0CSM_SPRO_TEXT<br />0CSM_SSTA_TEXT<br />0CSM_STAT_TEXT<br />0CSM_TYPE_ATTR<br />0CSM_TYPE_TEXT<br />0FSBP_RATING_ATTR<br />0PR_TYPE_TEXT<br /><br />0CRM_UT_SRV_CONT_I<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D021215)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D002665)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001706582/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001706582/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001706582/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001706582/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001706582/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001706582/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001706582/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001706582/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001706582/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1585204", "RefComponent": "BC-EIM-ODP", "RefTitle": "ETL interface: Release of DataSources", "RefUrl": "/notes/1585204"}, {"RefNumber": "1521883", "RefComponent": "BC-BW-ODP", "RefTitle": "ODP Data Replication API 1.0", "RefUrl": "/notes/1521883"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2731192", "RefComponent": "EIM-DH-MD", "RefTitle": "SAP Data Hub - ABAP connection type for SAP Data Hub", "RefUrl": "/notes/2731192 "}, {"RefNumber": "1521883", "RefComponent": "BC-BW-ODP", "RefTitle": "ODP Data Replication API 1.0", "RefUrl": "/notes/1521883 "}, {"RefNumber": "1585204", "RefComponent": "BC-EIM-ODP", "RefTitle": "ETL interface: Release of DataSources", "RefUrl": "/notes/1585204 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BBPCRM", "From": "701", "To": "701", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "702", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "712", "To": "712", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "713", "To": "713", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "BBPCRM", "NumberOfCorrin": 1, "URL": "/corrins/0001706582/63"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}