{"Request": {"Number": "1093412", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 364, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000006494232017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001093412?language=E&token=219615D24EB6415A0233285C7EE626EE"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001093412", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001093412/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1093412"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.09.2007"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PART-ISHMED"}, "SAPComponentKeyText": {"_label": "Component", "value": "Clinical System i.s.h.med"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Partner solutions", "value": "XX-PART", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Clinical System i.s.h.med", "value": "XX-PART-ISHMED", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART-ISHMED*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1093412 - Clinical Order: Order Templates"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note concerns the enhancement for using \"Order Templates\" for the clinical order.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Advance Development<br />The following notes are a prerequisite for this note:</p> <UL><LI></LI></UL><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>You can use the order template functionality to create clinical orders while automatically presetting selected attributes, e.g. the service.<br /><br />In the order template configuration function (transaction N1COT) you can create or change order templates for a specific order type. When you create a clinical order you will find the templates in the selection list of order types below the relevant order type.<br />In the selection list you can either select a template or an order type.<br /><br />We recommend that you only import this note if you actually need it (subsequent note). Otherwise these technical adaptations will be automatically provided after you import SAP ECC Industry Extension Healthcare 6.0 Support Package 12 .<br /><br />Unzip the attached file HW1093412_600.zip for i.s.h.med Version 6.00.<br /><br />You should note that you cannot download the attached file using OSS, but only using the Service Marketplace (see also note number 480180 and note number 13719 on importing attachments). Import the unzipped orders into your system. Execute the source code correction included.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-PART-ISHMED-ORD (Service Management i.s.h.med)"}, {"Key": "Responsible                                                                                         ", "Value": "C5021077"}, {"Key": "Processor                                                                                           ", "Value": "C5001995"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001093412/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001093412/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001093412/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001093412/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001093412/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001093412/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001093412/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001093412/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001093412/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "HW1093412_600.zip", "FileSize": "326", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000595972007&iv_version=0004&iv_guid=CDBAF6F629839F4F986F68D93B181EA2"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-H", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "602", "To": "602", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "IS-H 600", "SupportPackage": "SAPK-60012INISH", "URL": "/supportpackage/SAPK-60012INISH"}, {"SoftwareComponentVersion": "IS-H 602", "SupportPackage": "SAPK-60201INISH", "URL": "/supportpackage/SAPK-60201INISH"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 1, "URL": "/corrins/0001093412/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 30, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "600", "Number": "885352 ", "URL": "/notes/885352 ", "Title": "Clin.Order: Performance of Order Type Selection List", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "600", "Number": "913590 ", "URL": "/notes/913590 ", "Title": "Clin. Order: Order Type Selection List - Create Order", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "600", "Number": "965398 ", "URL": "/notes/965398 ", "Title": "Clinical order: the initiating OU is a department", "Component": "IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "600", "Number": "966611 ", "URL": "/notes/966611 ", "Title": "Clinical order: initiating OU is department", "Component": "IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "890572 ", "URL": "/notes/890572 ", "Title": "Clinical Order: Transport Order Types - Various Adjustments", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "901702 ", "URL": "/notes/901702 ", "Title": "Clinical Order: Input Help for Surgery Team", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "902204 ", "URL": "/notes/902204 ", "Title": "Clin Order: Order Type Selection List: Preregistered Item", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "923806 ", "URL": "/notes/923806 ", "Title": "Clinical Order: Sorting Services", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "946779 ", "URL": "/notes/946779 ", "Title": "Clinical Order: Synchronizing Order Types", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "956909 ", "URL": "/notes/956909 ", "Title": "Clinical Order: Report RN1_SYNC_CORDERTYPES Abort", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "957214 ", "URL": "/notes/957214 ", "Title": "Clin. Order: Selection List - Order Type Not Transferred", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "961205 ", "URL": "/notes/961205 ", "Title": "Clin. Order: Selection List - Order Type Not Transferred", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "963141 ", "URL": "/notes/963141 ", "Title": "Clinical order: Creating runtime error for request", "Component": "IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "964328 ", "URL": "/notes/964328 ", "Title": "Surgery System: Surgery Monitor - Create Clinical Order", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "977273 ", "URL": "/notes/977273 ", "Title": "Clinical Order: Runtime Error in the Selection List", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "979325 ", "URL": "/notes/979325 ", "Title": "Clinical Order: Order Initiator", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "992866 ", "URL": "/notes/992866 ", "Title": "Surgery System: Create Request in Surgery Monitor: Runtime E", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "992921 ", "URL": "/notes/992921 ", "Title": "Request: Selection List Order Types - Request Profile", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "993614 ", "URL": "/notes/993614 ", "Title": "Clinical Work Station: Request Cannot Be Created", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1010732 ", "URL": "/notes/1010732 ", "Title": "Request: Request Profile - Selectable Individual Service", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1011621 ", "URL": "/notes/1011621 ", "Title": "Clinical Order: Selection List", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1011970 ", "URL": "/notes/1011970 ", "Title": "Clinical Order: Selection List Without Content", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1015066 ", "URL": "/notes/1015066 ", "Title": "Clinical Order: Report RN1_SYNC_CORDERTYPES", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1019123 ", "URL": "/notes/1019123 ", "Title": "Clinical Order: Business Add-In N1_HLST_NEW_FIELDS", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1031066 ", "URL": "/notes/1031066 ", "Title": "Clinical order: Base item", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1048434 ", "URL": "/notes/1048434 ", "Title": "Clinical Order: Lock Concept", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1050034 ", "URL": "/notes/1050034 ", "Title": "Clinical order: Initiator is not checked", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1064617 ", "URL": "/notes/1064617 ", "Title": "Request: Request Type - Service with Highest PRIO", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1085152 ", "URL": "/notes/1085152 ", "Title": "Technical Adaptation", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1093987 ", "URL": "/notes/1093987 ", "Title": "Klin.Auftrag: Prüfung Zuordnung Veranlasser zu Auftragstyp", "Component": "XX-PART-ISHMED"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}