{"Request": {"Number": "1302490", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 599, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000007652812017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001302490?language=E&token=CA55101DFB3174C566ECD275D9C5E1E3"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001302490", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1302490"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Currentness", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.02.2009"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-CH-IS-H"}, "SAPComponentKeyText": {"_label": "Component", "value": "Hospital"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Switzerland", "value": "XX-CSC-CH", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-specific component", "value": "XX-CSC-CH-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hospital", "value": "XX-CSC-CH-IS-H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH-IS-H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1302490 - IS-H CH: Corrections for Tarmed 1.6 II"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1302490&TargetLanguage=EN&Component=XX-CSC-CH-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/1302490/D\" target=\"_blank\">/notes/1302490/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This SAP Note is relevant only for the country version Switzerland (CH).<br />Service assignments (or Assignments of service blocks and chapters) to services are partially incorrect or have an incorrect validity date.<br />This SAP Note also delivers the new deletion report RNWCHUDLS00 for the targeted removal of the Tarmed catalog (you can use this if you want to remove the Tarmed catalog and completely reimport it).</p><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>Tarmed 1.6, RNWCHNTPK00, cumulation, RNWCHUDLS00, deletion report<br />Service Master</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>Program error</p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><OL>1. Before you implement the source code corrections, implement the attached attachment as follows:</OL> <OL><OL>a) Unpack the attached file.</OL></OL> <p>                       HW1302490_472.zip for IS-H Version 4.72 <p>                       HW1302490_60.zip for IS-H Version 6.0 <p>                       HW1302490_603.zip for IS-H Version 6.03 <p>                       HW1302490_604.zip for IS-H Version 6.04 <p>                       Note that you cannot download the attached files using OSS, but only from SAP Service Marketplace (see also SAP Notes 480180 and 13719 for information about importing attachments). <p>                       Import the unpacked requests into your system. <OL>2. Now implement the source code corrections from this SAP Note.</OL></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Owner                                                                                    ", "Value": "<PERSON><PERSON><PERSON> (C5043714)"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON><PERSON><PERSON> (C5043714)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001302490/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "HW1302490_472.zip", "FileSize": "17", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000127042009&iv_version=0001&iv_guid=6D36F6940051554996D3C53D6CF97614"}, {"FileName": "HW1302490_603.zip", "FileSize": "21", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000127042009&iv_version=0001&iv_guid=BC2D60224B839447A4852D187B61ECFC"}, {"FileName": "HW1302490_604.zip", "FileSize": "28", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000127042009&iv_version=0001&iv_guid=7D9F13E6892D3D40BB16481FF24F35C4"}, {"FileName": "HW1302490_60.zip", "FileSize": "21", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000127042009&iv_version=0001&iv_guid=DFFC77460008C4459BFAF6F4A12AE897"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1307393", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Corrections for Tarmed 1.06 IV", "RefUrl": "/notes/1307393"}, {"RefNumber": "1304419", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Corrections for Tarmed 1.6 III", "RefUrl": "/notes/1304419"}, {"RefNumber": "1299774", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Corrections for Tarmed 1.06", "RefUrl": "/notes/1299774"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "1307393", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Corrections for Tarmed 1.06 IV", "RefUrl": "/notes/1307393 "}, {"RefNumber": "1304419", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Corrections for Tarmed 1.6 III", "RefUrl": "/notes/1304419 "}, {"RefNumber": "1299774", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Corrections for Tarmed 1.06", "RefUrl": "/notes/1299774 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "IS-H", "From": "472", "To": "472", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": [{"SoftwareComponentVersion": "IS-H 472", "SupportPackage": "SAPKIPHF32", "URL": "/supportpackage/SAPKIPHF32"}, {"SoftwareComponentVersion": "IS-H 600", "SupportPackage": "SAPK-60018INISH", "URL": "/supportpackage/SAPK-60018INISH"}, {"SoftwareComponentVersion": "IS-H 602", "SupportPackage": "SAPK-60207INISH", "URL": "/supportpackage/SAPK-60207INISH"}, {"SoftwareComponentVersion": "IS-H 603", "SupportPackage": "SAPK-60306INISH", "URL": "/supportpackage/SAPK-60306INISH"}, {"SoftwareComponentVersion": "IS-H 604", "SupportPackage": "SAPK-60404INISH", "URL": "/supportpackage/SAPK-60404INISH"}]}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 4, "URL": "/corrins/0001302490/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 25, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "733710 ", "URL": "/notes/733710 ", "Title": "IS-H CH: Service Rule - C19, Maximum Tare Medium (Periods)", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "736454 ", "URL": "/notes/736454 ", "Title": "IS-H CH: Service Rule C19 - Maximum - New Periods", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "738336 ", "URL": "/notes/738336 ", "Title": "IS-H CH: RNWCHNTPK00 - Corr. Cumulation Information (3)", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "750822 ", "URL": "/notes/750822 ", "Title": "IS-H CH: RNWCHNTPK00 - Time-Based Acc./Def. Services + NTPZ Assgmt", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "757253 ", "URL": "/notes/757253 ", "Title": "IS-H CH: RNWCHNTPK00 - Consider Validity for NTPKCH_xxx", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "771105 ", "URL": "/notes/771105 ", "Title": "IS-H CH: RNWCHNTPK00 - Update Records with Warnings Correctly", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "772125 ", "URL": "/notes/772125 ", "Title": "IS-H CH: Service Rules: Quantity Limitation, Anesthesia Rule", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "795367 ", "URL": "/notes/795367 ", "Title": "IS-H CH: RNWCHNTPK00 - Swiss Additional tables NTPKCH", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "801485 ", "URL": "/notes/801485 ", "Title": "IS-H CH: RNWCHNTPK00 - Modifications for TARMED DB Vers. 1.2", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "821571 ", "URL": "/notes/821571 ", "Title": "IS-H CH: RNWCHNTPK00 - Correction of Import Report", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "833546 ", "URL": "/notes/833546 ", "Title": "IS-H CH: Service Rule C19, C29 - TARMED Maximum", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "833550 ", "URL": "/notes/833550 ", "Title": "IS-H CH: Service Rule TARMED Maximum - Unit Change", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "932056 ", "URL": "/notes/932056 ", "Title": "IS-H CH: NTPKCH - Adjustments Due to Key Change", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1020045 ", "URL": "/notes/1020045 ", "Title": "IS-H CH: Adjustments Based on TARMED Version 1.04", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1042045 ", "URL": "/notes/1042045 ", "Title": "IS-H CH: RNWCHNTPK00 - Validity of Service Asgmt (NTPZ)", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1045690 ", "URL": "/notes/1045690 ", "Title": "IS-H CH: Service Rule C29 - Maximum Rule", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1092712 ", "URL": "/notes/1092712 ", "Title": "IS-H CH: Service Rules - Case Category - Change", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1099059 ", "URL": "/notes/1099059 ", "Title": "IS-H CH: Tarmed Version 1.05 - Adjustments", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1114525 ", "URL": "/notes/1114525 ", "Title": "IS-H CH: Service Rules - Global Conversion of ABRKZ", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1127699 ", "URL": "/notes/1127699 ", "Title": "IS-H CH: Tarmed - Maximum Rule - Unit 09", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1159755 ", "URL": "/notes/1159755 ", "Title": "IS-H CH: RNWCHNTPK00- Enhancement Cumulation Explosion", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1165857 ", "URL": "/notes/1165857 ", "Title": "IS-H CH: RNWCHNTPK00- Correction of Update Errors", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1225801 ", "URL": "/notes/1225801 ", "Title": "IS-H CH: TARMED Quantity Rule C19", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1295246 ", "URL": "/notes/1295246 ", "Title": "IS-H CH: TARMED Database Version 01.06.00", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1299774 ", "URL": "/notes/1299774 ", "Title": "IS-H CH: Corrections for Tarmed 1.06", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "932056 ", "URL": "/notes/932056 ", "Title": "IS-H CH: NTPKCH - Adjustments Due to Key Change", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1020045 ", "URL": "/notes/1020045 ", "Title": "IS-H CH: Adjustments Based on TARMED Version 1.04", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1042045 ", "URL": "/notes/1042045 ", "Title": "IS-H CH: RNWCHNTPK00 - Validity of Service Asgmt (NTPZ)", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1045690 ", "URL": "/notes/1045690 ", "Title": "IS-H CH: Service Rule C29 - Maximum Rule", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1092712 ", "URL": "/notes/1092712 ", "Title": "IS-H CH: Service Rules - Case Category - Change", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1099059 ", "URL": "/notes/1099059 ", "Title": "IS-H CH: Tarmed Version 1.05 - Adjustments", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1114525 ", "URL": "/notes/1114525 ", "Title": "IS-H CH: Service Rules - Global Conversion of ABRKZ", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1127699 ", "URL": "/notes/1127699 ", "Title": "IS-H CH: Tarmed - Maximum Rule - Unit 09", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1159755 ", "URL": "/notes/1159755 ", "Title": "IS-H CH: RNWCHNTPK00- Enhancement Cumulation Explosion", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1165857 ", "URL": "/notes/1165857 ", "Title": "IS-H CH: RNWCHNTPK00- Correction of Update Errors", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1225801 ", "URL": "/notes/1225801 ", "Title": "IS-H CH: TARMED Quantity Rule C19", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1295246 ", "URL": "/notes/1295246 ", "Title": "IS-H CH: TARMED Database Version 01.06.00", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1299774 ", "URL": "/notes/1299774 ", "Title": "IS-H CH: Corrections for Tarmed 1.06", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1159755 ", "URL": "/notes/1159755 ", "Title": "IS-H CH: RNWCHNTPK00- Enhancement Cumulation Explosion", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1165857 ", "URL": "/notes/1165857 ", "Title": "IS-H CH: RNWCHNTPK00- Correction of Update Errors", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1225801 ", "URL": "/notes/1225801 ", "Title": "IS-H CH: TARMED Quantity Rule C19", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1295246 ", "URL": "/notes/1295246 ", "Title": "IS-H CH: TARMED Database Version 01.06.00", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1299774 ", "URL": "/notes/1299774 ", "Title": "IS-H CH: Corrections for Tarmed 1.06", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1295246 ", "URL": "/notes/1295246 ", "Title": "IS-H CH: TARMED Database Version 01.06.00", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1299774 ", "URL": "/notes/1299774 ", "Title": "IS-H CH: Corrections for Tarmed 1.06", "Component": "XX-CSC-CH-IS-H"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1302490&TargetLanguage=EN&Component=XX-CSC-CH-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/1302490/D\" target=\"_blank\">/notes/1302490/D</a>."}}}}