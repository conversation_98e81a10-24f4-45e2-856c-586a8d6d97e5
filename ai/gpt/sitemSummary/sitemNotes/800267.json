{"Request": {"Number": "800267", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 279, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015812992017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000800267?language=E&token=EAE9C91777864D0488C171D019FBD744"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000800267", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000800267/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "800267"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 11}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "2023-07-11"}, "SAPComponentKey": {"_label": "Component", "value": "BC-XI"}, "SAPComponentKeyText": {"_label": "Component", "value": "NetWeaver Process Integration (PI)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "NetWeaver Process Integration (PI)", "value": "BC-XI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-XI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "800267 - \"Integration Repository/directory\" service connections"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You require an Integration Repository or Integration Directory connection for your systems for a support terminal session.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>XI Exchange Infrastructure, repository, directory, STFK</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<ul>\r\n<li>A SAProuter connection for SAP (SAPserv X) is already set up.</li>\r\n</ul>\r\n<ul>\r\n<li>The corresponding application Integration Repository or Integration Directory is available on your server or PC.</li>\r\n</ul>\r\n<p>Setting up the SAProuter connection and the application Integration Repository or Integration Directory is not dealt with here.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The description of how to set up the&#160;<strong>INTEGRATION REPOSITORY </strong>or<strong> INTEGRATION DIRECTORY</strong>connection is divided into three sections:</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;- Determining the ports used (RMI and http(s)) as well as the server names for the Integration Repository/directory.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;- Setup process on your SAProuter</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;- Setting up the service, maintaining the system data, and opening the service connection in the SAP Support Portal (SAP for Me customer portal)</p>\r\n<p>Note that SAP employees can log on only to servers that are maintained in the system data.</p>\r\n<p><br /><strong><span style=\"text-decoration: underline;\">Determining the used ports and the server name</span>:</strong><br />The procedure for determining the http(s)or RMI ports as well as the server name is identical for the two services 'Integration Repository' and 'Integration Directory'. The http or https port should be configured in accordance with the server configuration.<br /><br />Value of http-port/https-port and rmi-port and server name can be found out depending on usage_type -<br /><br />There are three ways to check property values -<br /><br />1) Checking property through Admin Pages (supported via CE usage type as well as XPI usage type)<br />2) Checking property through Exchange Profile (supported via XPI usage_type )<br />3) Checking property through Configuration Manager (supported via CE usage_type)<br /><br /><br /><strong>1) Checking of Property through Admin Pages</strong><br /><br />&#160;&#160; Carry out the following steps :<br />The process is same for all releases and for all environments (PI/CE)<br /><br />Launch the main start page of 'Integration Repository' or 'Integration Directory.<br />For Repository -<br />http(s)://&lt;host:port/rep/start/index.jsp&gt;<br />For Directory -<br />http(s)://&lt;host:port/dir/start/index.jsp&gt;<br /><br />On the top right corner click on link 'Administration'.<br />You need to have admin permissions to view these pages.<br /><br />In the page that opens up, click on the desired application. e.g. for Repository application, select the 'Repository' tab and for Directory application, select the 'Directory' tab. Click on the link 'AiiProperties'.<br />&#160;&#160;&#160;&#160;&#160;&#160;o&#160;&#160;Enter property com.sap.aii.connect.repository.httpport. It shows the value for the http-port.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Enter com.sap.aii.connect.repository.httpsport to get the value for the https port.<br />&#160;&#160;&#160;&#160;&#160;&#160;o&#160;&#160;Entering com.sap.aii.connect.repository.rmiport returns the value for the RMI port.<br />&#160;&#160;&#160;&#160;&#160;&#160;o&#160;&#160;Entering com.sap.aii.connect.repository.name returns the value for the server name.<br /><br /><br /><strong>2) Checking of Property through Exchange Profile</strong><br /><br />Steps to be followed are -<br /><br />&#160;&#160;&#160;&#160;&#160;&#160;o Select the entry 'Administration' on the first page of the SAP Exchange Infrastructure<br />&#160;&#160;&#160;&#160;&#160;&#160; &#160; (http(s)://&lt;servername&gt;:&lt;j2ee_http(s)_port&gt;/rep/start/index.jsp)<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; (calling the page as well as the 'Exchange profile' requires SAP Exchange Infrastructure administrator rights')<br /><br />&#160;&#160;&#160;&#160;&#160;&#160;o Select the entry 'Exchange profile' under the 'Administration of Properties ' on the 'Repository' tab.<br />&#160;&#160;&#160;&#160;&#160;&#160;o In the 'Exchange Profile' interface, open the 'Connections' tree node.<br /><br />&#160;&#160;&#160;&#160; &#160;o&#160;&#160;Enter property com.sap.aii.connect.repository.httpport. It shows the value for the http port.Enter com.sap.aii.connect.repository.httpsport to get the value for the https port.<br />&#160;&#160;&#160;&#160;&#160;&#160;o&#160;&#160;Entering com.sap.aii.connect.repository.rmiport returns the value for the RMI port.<br />&#160;&#160;&#160;&#160;&#160;&#160;o&#160;&#160;Entering com.sap.aii.connect.repository.name returns the value for the server name<br /><br /><br /><strong>3) Change of Property through Configuration Manager</strong><br /><br />In this case, the steps to be followed are -<br /><br />Launch the main page of NWA<br /><br />http(s)://&lt;host:port/nwa<br /><br />Login to the engine as Administrator.<br />Click on Configuration Management<br />Click on Infrastructure<br /><br />Click on Java System Properties. Select the desired template.<br />There depending on release, we can find the desired property.<br /><br />Releases 7.10 SP3 and successive SPs:-<br />Go to Applications tab and search for \"com.sap.xi.repository\". Select the applicaiton and then view the value of the properties.<br /><br />Releases 7.11(EhP1) and onwards:-<br />Go to Services tab and search for \"com.sap.aii.utilxi.cfg.svc\". Select the service and then view the value of the properties.<br /><br />Enter property com.sap.aii.connect.repository.httpport. It shows the value for the http port.<br />Enter com.sap.aii.connect.repository.httpsport to get the value for the https port.<br />&#160;&#160;&#160;&#160;&#160;&#160;o&#160;&#160;Entering com.sap.aii.connect.repository.rmiport returns the value for the RMI port.<br />&#160;&#160;&#160;&#160;&#160;&#160;o&#160;&#160;Entering com.sap.aii.connect.repository.name returns the value for the server name<br /><br /><strong><br /></strong></p>\r\n<p><strong><span style=\"text-decoration: underline;\">Setup process on your SAProuter:</span></strong></p>\r\n<p>- You must determine which route permission table \"saprouttab\" is used by the SAProuter that you are using.</p>\r\n<p>- In the relevant table, create an entry of the following type:</p>\r\n<p><strong><span style=\"text-decoration: underline;\">Note</span></strong>: <strong>INTEGRATION REPOSITORY</strong> or <strong>INTEGRATION DIRECTORY</strong> remote connection uses the multiple TCP/IP ports. These ports must be released explicitly. The connections that are permitted when using the respective ports are determined in a configuration file called 'saprouttab'.</p>\r\n<p><em>Port 1 - RMI port (default ending should be xxx04)</em><br /><em> Port 2 - Transport protocol, please enter the HTTP or HTTPS port</em></p>\r\n<p style=\"padding-left: 30px;\">P &lt;IP address SAP-SR&gt; &lt;IP address server|host name|FQDN/FQHN&gt; &lt;RMI&gt;04<br /> P &lt;IP address SAP-SR&gt; &lt;IP address server|host name|FQDN/FQHN&gt; &lt;HTTP/S&gt;</p>\r\n<p>or, in the case of SNC:</p>\r\n<p style=\"padding-left: 30px;\">KP \"p:CN=sapserv&lt;X&gt;, OU=SAProuter, O=SAP, C=DE\" &lt;IP address server|host name|FQDN/FQHN&gt; &lt;RMI&gt;04<br /> KP \"p:CN=sapserv&lt;X&gt;, OU=SAProuter, O=SAP, C=DE\" &lt;IP address server|host name|FQDN/FQHN&gt; &lt;HTTP/S&gt;</p>\r\n<p>Examples:</p>\r\n<p style=\"padding-left: 30px;\"><strong>#for sapserv1</strong><strong><br /> </strong>P *************** *********** 59104<br /> P *************** *********** 443</p>\r\n<p style=\"padding-left: 30px;\"><strong>#for sapserv2</strong><strong><br /> </strong>KP \"p:CN=sapserv2, OU=SAProuter, O=SAP, C=DE\" *********** 59104<br /> KP \"p:CN=sapserv2, OU=SAProuter, O=SAP, C=DE\" *********** 443</p>\r\n<p style=\"padding-left: 30px;\"><strong>#for sapserv3</strong><strong><br /> </strong>P *********** *********** 59104<br /> P *********** *********** 443</p>\r\n<p style=\"padding-left: 30px;\"><strong>#for sapserv4</strong><strong><br /> </strong>P ************ *********** 59104<br /> P ************ *********** 443</p>\r\n<p style=\"padding-left: 30px;\"><strong>#for sapserv5</strong><strong><br /> </strong>P ************ *********** 59104<br /> P ************ *********** 443</p>\r\n<p style=\"padding-left: 30px;\"><strong>#for sapserv7</strong><strong><br /> </strong>P 194.39.134.35 *********** 59104<br /> P 194.39.134.35 *********** 443</p>\r\n<p style=\"padding-left: 30px;\"><strong>#for sapserv9</strong><strong><br /> </strong>KP \"p:CN=sapserv9, OU=SAProuter, O=SAP, C=DE\" *********** 59104<br /> KP \"p:CN=sapserv9, OU=SAProuter, O=SAP, C=DE\" *********** 443</p>\r\n<p>-&#160;&#160;&#160;&#160;&#160;<strong>A generic entry such as P * * * is insufficient, because the wild card \"*\" that specifies the TCP port (the 3rd *), activates only ports 3200 to 3299 and does not activate any ports outside this port-range.</strong></p>\r\n<p>-&#160;&#160;&#160;&#160; Update the changed route permission table \"saprouttab\" in the SAProuter with the command \"saprouter -n\" or restart the SAProuter.</p>\r\n<p>-&#160;&#160;&#160;&#160; Check that the SAProuter can reach the target host (IP address or host name) on the relevant application port. If this is not the case, set up the network and DNS accordingly.&#160; Further details see: SAP note 48243.</p>\r\n<p>-&#160;&#160;&#160;&#160;&#160;<strong>In case your SAProuter is password-protected in the saprouttab, define the SAProuter password in the Customer Remote Logon Depot (formerly known as secure area) in accordance with SAP Note 1773689. Note: max. password length = 8 characters!</strong></p>\r\n<p>Note that only the current SAProuter software supports all services, therefore, we recommend to always use the current version.</p>\r\n<p>&#160;</p>\r\n<p><strong><span style=\"text-decoration: underline;\">Setting up the service, maintaining the system data and opening the connection in SAP Support Portal (SAP for Me customer portal)</span></strong><strong>:</strong></p>\r\n<p>- Log on to SAP for Me, URL:&#160;<a target=\"_blank\" href=\"https://me.sap.com/systemsprovisioning/connectivity\">https://me.sap.com/systemsprovisioning/connectivity</a><br /> - If necessary, switch to the tab \"All\" in the systems area.<br /> - Click the relevant system ID to select the required system.</p>\r\n<p><strong><span style=\"text-decoration: underline;\">Creating the service !!is only required!! if it does not yet exist in the connection type list</span></strong><strong>:</strong></p>\r\n<p>- Maintain the required connection type by clicking the \"+ symbol\" (add connection type).<br /> - Select the service:&#160;<strong>INTEGRATION REPOSITORY </strong>or<strong> INTEGRATION DIRECTORY<br /> </strong>- Port assignment:</p>\r\n<p style=\"padding-left: 30px;\">Port 1 - RMI port (default ending should be xxx04)<br /> Port 2 - Transport protocol, please enter the HTTP or HTTPS port<br /> <strong>!!Mark the HTTPS flag only, if a secure HTTPS connection should be used!!</strong></p>\r\n<p>- Enter at least one contact person.<br /> - Save the selection.<br /> - Navigate back to the service connections by clicking the \"&lt; symbol\" (back).</p>\r\n<p><strong><span style=\"text-decoration: underline;\">System data maintenance !!is only required!! if neither database nor application server are maintained</span></strong><strong>:</strong></p>\r\n<p>- Navigate to the system data by choosing \"Maintain System Data\".<br /> - Choose the menu option \"Server and SAProuter\".<br /> - Choose DB/Application/Other servers.<br /> - Select either database server or the \"+ symbol\" (add application / other server).<br /> - If necessary, click Edit and enter at least the required data (required entry fields are marked with a red asterisk).<br /> - Note that the entries for the additional SAProuter !!are only required!! if you have multiple SAProuters in sequence (cascaded - one after another) on your side.<br /> - Save your changes.<br /> - Navigate back to the service connections by clicking the \"&lt; symbol\" (back).</p>\r\n<p><strong><span style=\"text-decoration: underline;\">Opening the Connection</span></strong><strong>:</strong></p>\r\n<p>- Choose the service:&#160;<strong>INTEGRATION REPOSITORY </strong>or<strong> INTEGRATION DIRECTORY</strong>in the list of connection types.<br /> - Choose \"Open connection\".<br /> - Enter at least one contact person.<br /> - Set the date and time when the connection is closed automatically.<br /> - Click on \"Open Connection\".</p>\r\n<p>In case there are any problems with service connections, create a case (<a target=\"_blank\" href=\"https://me.sap.com/servicessupport/productsupport\">https://me.sap.com/servicessupport/productsupport</a>) under the component XX-SER-NET.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-XI-IBD (Integration Builder - Design)"}, {"Key": "Other Components", "Value": "BC-XI-IBC (Integration Builder - Configuration)"}, {"Key": "Responsible                                                                                         ", "Value": "I045290"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (C3303606)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000800267/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000800267/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000800267/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000800267/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000800267/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000800267/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000800267/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000800267/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000800267/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "31515", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "Service connections", "RefUrl": "/notes/31515"}, {"RefNumber": "1229022", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1229022"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1832180", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "How to assist the customer with network connectivity problems between SAP and the customer system", "RefUrl": "/notes/1832180 "}, {"RefNumber": "2919242", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "Connection type Integration Directory/Integration Repository does not work", "RefUrl": "/notes/2919242 "}, {"RefNumber": "984434", "RefComponent": "XX-SER-FORME", "RefTitle": "How to speed up customer incident processing", "RefUrl": "/notes/984434 "}, {"RefNumber": "617604", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Services for SAP PI/XI", "RefUrl": "/notes/617604 "}, {"RefNumber": "48243", "RefComponent": "XX-SER-NET-NEW", "RefTitle": "Integrating the SAProuter software into a firewall environment", "RefUrl": "/notes/48243 "}, {"RefNumber": "31515", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "Service connections", "RefUrl": "/notes/31515 "}, {"RefNumber": "1229022", "RefComponent": "BC-XI-IBD", "RefTitle": "Logon to Directory / Repository release 7.10 via STFK fails", "RefUrl": "/notes/1229022 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_XITOOL", "From": "3.0", "To": "3.0", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}