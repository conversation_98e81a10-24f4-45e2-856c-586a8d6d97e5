{"Request": {"Number": "91615", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 319, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000330052017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000091615?language=E&token=73494B10494AA057A48C917222F836C0"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000091615", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000091615/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "91615"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 81}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "08.09.2004"}, "SAPComponentKey": {"_label": "Component", "value": "EC-PCA-TL-ARC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Archiving"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Enterprise Controlling", "value": "EC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Profit Center Accounting", "value": "EC-PCA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EC-PCA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Extras", "value": "EC-PCA-TL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EC-PCA-TL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Archiving", "value": "EC-PCA-TL-ARC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EC-PCA-TL-ARC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "91615 - Archiving/profit center: new archiving programs"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Long runtimes in profit center archiving (archiving object PCA_OBJECT).<br />Missing profit center archive read programs.<br />Missing connection between the profit center archiving and the Archive Information System SAP AS.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>ADK, archiving<br />Archiving objects: PCA_OBJECT<br />Programs: RGUARCPC, RGUDELPC, RGURELPC<br />Transactions: KE71, KE72, KE73, KE74</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The problem is due to the program design.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The PCA_OBJECT archiving object has been revised. You can import the new programs for Releases 3.0F, 3.1I, 4.0B and 4.5B as a preliminary development via the SAPSERVx servers.<br /><br />Range of functions of the new EC-PCA archiving periphery:<br />---------------------------------------------------------</p> <OL>1. Archiving write program, delete program</OL> <p>The write program's selection screen was revised. The following fields are no longer available: account, PCA document number, posting date, reference document number and reference fiscal year. These fields were used by customers to improve the performance of the write program when the GLPCA table was very large. Since the write program was revised, it can perform well even without these entries.<br />Archiving ensures that all data relating to business operation is archived together. The selection fields contained in the latest version: controlling area, company code, profit center, fiscal year, record type and version are sufficient for this.<br /><br />It is also unnecessary to create specific indexes for archiving. Archiving uses standard index No. 1 (profit center oriented index). This is possible, since the program first determines the accounts and profit centers to be archived from the totals table so that it can transfer all fields of the index into the WHERE clause of the SELECT command. (You can determine the index used via Transaction ST04 or ST05. Do not use Transaction SM50 for this purpose.)</p> <OL>2. Read periphery. See Note 422836.</OL> <OL>3. Hints<br />You can call up hints for the points mentioned above via \"Go to -&gt; Hints\" after you choose an action (archive, delete, and so on), using Transaction SARA.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You cannot use Transaction SARA to call up hints for the read programs for Release 3.0D up to and including 3.1I. Instead, use Transaction SE61. Select 'Text in dialog' for the 'Document class' and enter 'EC_PCA_READ' in the 'Dialog text' input field. Then choose 'Display'. <OL>4. Link to the archiving information system (SAP AS).<br />This allows fast access to all archives that have been sorted by document or by account.</OL> <p><br /><br />Information on importing the new EC-PCA archiving periphery:<br />-----------------------------------------------------------------</p> <OL>1. The programs for the new EC-PCA archiving are stored on the SAPSERVx servers in English and German. Make sure that you always use the latest version of the programs.</OL> <OL>2. After upgrading to a Release prior to Release 4.6A, you have to import the files again (only those which have been defined for the particular release).<br />You also have to reimport these files after importing IS solutions (for example, IS-OIL). If you are planning such an import, install the new PCA archiving periphery last.</OL> <OL>3. As a precaution, make sure that you do not execute any programs from the FI-LC or FI-SL archiving periphery during the import.</OL> <OL>4. The import does not change the existing archives. They can be processed without restrictions by the new programs.</OL> <OL>5. Before you archive using the imported programs, create new variants for the write program. You can no longer use any of the variants created up to now, because the selection screen has changed.</OL> <OL>6. If you have questions or problems, contact the development department of SAP Walldorf directly.</OL> <p><br /><br />Importing the new Profit Center archiving periphery:<br />----------------------------------------------------<br />For Release 3.0D up to and including 3.1I:<br />----------------------------------------</p> <OL>1. Implement Note 89324 (new archiving base).</OL> <OL>2. Import the K000233.ZAT and R000233.ZAT files from the Directory /general/R3server/abap/note.0091615 as described in Note 13719.</OL> <OL>3. Afterwards, import the K001295.ZAT and R001295.ZAT files from the Directory /general/R3server/abap/note.0091615.</OL> <p><br />For Release 4.0B<br />----------------</p> <OL>1. Import the K063879.P45 and R063879.P45 files as described in Note 13719.</OL> <OL>2. Afterwards, import the K001295.ZAT and R001295.ZAT files from the Directory /general/R3server/abap/note.0091615.</OL> <p><br />For Release 4.5B:<br />-----------------<br />Import the K001295.ZAT and R001295.ZAT files from the Directory /general/R3server/abap/note.0091615.<br /><br /><br /><br />For Releases 3.0D up to and including 4.5B:<br />-------------------------------<br />The above files are only available in German and English. To obtain Japanese texts, import the K910620.J45 and R910620.J45 files from the Directory/general/R3server/abap/note.0091615/japanese_texts. You can ignore any import errors that occur, provided that the Japanese texts are actually available.<br /><br /><br /><br />Implementing the connection of EC-PCA archiving to the Archive Information System (SAP AS):<br />-------------------------------------------------------------------</p> <OL>1. Only for Release 3.0D up to and including Release 4.0B: implement Note 99388 into your system as described in Note 13719.</OL> <OL>2. Only for Release 3.0D up to and including Release 4.0B: import the files with the Archive Information System customizing settings for EC-PCA into your system as specified below.<br />The file names for the releases are:</OL><OL><OL>a) Release 3.0D up to and including Release 3.1I: K901129.ZAT and R901129.ZAT.</OL></OL> <OL><OL>b) Release 4.0B: K053913.P45 and R053913.P45.</OL></OL> <OL>3. For all releases (Release 3.0D up to and including Release 4.5B): activate the SAP_EC_PCA_001 info structure by using Transaction SARI.<br />You also have the SAP_EC_PCA_002 info structure in your system. Only activate it if you have used the PCA_OBJECT archiving object with the 'Archiving by document number' archiving type in the past. Only create this info structure for those kind of archives. Do not fill the SAP_EC_PCA_001 info structure for these archives.</OL> <p><br /></p> <b>Known problems</b><br /> <OL>1. Long write program runtimes when archiving individual periods.</OL> <p>The write program uses an index in Table GLPCA which does not contain the period field (POPER).<br />If you want to archive the actual line items of several periods, archive them in one archiving run. If you want to archive the data by periods, create an index inTable GLPCA which contains all fields of index no. 1 \"Profit-center-oriented index\" in the same order. Add field POPER for the period at the end.</p> <OL>2. After reloading, the status of archiving runs is not changed to \"reloaded\".</OL> <p>Apply the the attached corrections to solve the problem.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-SL-SL-E (Archiving)"}, {"Key": "Transaction codes", "Value": "HIER"}, {"Key": "Transaction codes", "Value": "TRANSAKTIONEN"}, {"Key": "Transaction codes", "Value": "SARA"}, {"Key": "Transaction codes", "Value": "ST04"}, {"Key": "Transaction codes", "Value": "SM50"}, {"Key": "Transaction codes", "Value": "SARI"}, {"Key": "Transaction codes", "Value": "ST05"}, {"Key": "Transaction codes", "Value": "SE61"}, {"Key": "Transaction codes", "Value": "KE72"}, {"Key": "Transaction codes", "Value": "KE73"}, {"Key": "Transaction codes", "Value": "KE71"}, {"Key": "Transaction codes", "Value": "KE74"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D022872)"}, {"Key": "Processor                                                                                           ", "Value": "D020848"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000091615/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000091615/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000091615/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000091615/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000091615/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000091615/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000091615/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000091615/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000091615/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "98114", "RefComponent": "BC-CCM-ADK", "RefTitle": "Archiving: Reloading destroys archive files", "RefUrl": "/notes/98114"}, {"RefNumber": "89324", "RefComponent": "BC-CCM-ADK", "RefTitle": "Archiving: Revised ADK versions", "RefUrl": "/notes/89324"}, {"RefNumber": "422836", "RefComponent": "EC-PCA-TL-ARC", "RefTitle": "Archive. Profit Center archiving: How to read archived data", "RefUrl": "/notes/422836"}, {"RefNumber": "203545", "RefComponent": "EC-PCA-TL-ARC", "RefTitle": "Archiv. profit center: Analysis of table GLPCA", "RefUrl": "/notes/203545"}, {"RefNumber": "178919", "RefComponent": "EC-PCA-ACT", "RefTitle": "Table GLPCA: How to reduce the data volume?", "RefUrl": "/notes/178919"}, {"RefNumber": "153588", "RefComponent": "BW-BCT", "RefTitle": "Syntax error 'Type \"GUSL_T_GLU1_BIW\" is unknown'", "RefUrl": "/notes/153588"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "203545", "RefComponent": "EC-PCA-TL-ARC", "RefTitle": "Archiv. profit center: Analysis of table GLPCA", "RefUrl": "/notes/203545 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "422836", "RefComponent": "EC-PCA-TL-ARC", "RefTitle": "Archive. Profit Center archiving: How to read archived data", "RefUrl": "/notes/422836 "}, {"RefNumber": "178919", "RefComponent": "EC-PCA-ACT", "RefTitle": "Table GLPCA: How to reduce the data volume?", "RefUrl": "/notes/178919 "}, {"RefNumber": "89324", "RefComponent": "BC-CCM-ADK", "RefTitle": "Archiving: Revised ADK versions", "RefUrl": "/notes/89324 "}, {"RefNumber": "153588", "RefComponent": "BW-BCT", "RefTitle": "Syntax error 'Type \"GUSL_T_GLU1_BIW\" is unknown'", "RefUrl": "/notes/153588 "}, {"RefNumber": "98114", "RefComponent": "BC-CCM-ADK", "RefTitle": "Archiving: Reloading destroys archive files", "RefUrl": "/notes/98114 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30D", "To": "30D", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "30F", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 2, "URL": "/corrins/0000091615/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}