{"Request": {"Number": "812919", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 226, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015838332017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000812919?language=E&token=68FDDD4E9F03DD75C406C36F9F14D705"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000812919", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000812919/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "812919"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 14}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.05.2021"}, "SAPComponentKey": {"_label": "Component", "value": "FI-GL-MIG"}, "SAPComponentKeyText": {"_label": "Component", "value": "general ledger migration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Accounting", "value": "FI-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "general ledger migration", "value": "FI-GL-MIG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-MIG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "812919 - SAP ERP new general ledger: Migration"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to use the new general ledger in SAP ERP and you are already using the classic general ledger productively in an R/3 release.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Migration, NewGL, New GL, new general ledger</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You can only migrate from the classic general ledger to the new general ledger after you upgrade your R/3 system to SAP ERP.<br />The enhancements, the complexity of the migration and the affected data volume can be very extensive. An extensive analysis of your initial situation and a detailed planning of your migration are the most important success factors. Bear in mind that the migration can only be successful if there is a separate migration project within the New G/L overall project.<br />You must prepare and execute this migration project with the utmost care to ensure accounting is still in order after the migration.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>To ensure maximum security, SAP supports each migration project with the SAP General Ledger Migration migration service. This mandatory technical service is based on standard migration scenarios and includes a scenario-specific General Ledger Migration Cockpit and service sessions to ensure the quality of the data and of the migration project. The service sessions are provided by a NewGL migration back office, which was specifically set up for this purpose.<br />After you commission the service, you are provided with the General Ledger Migration Cockpit and the service sessions are delivered after consultation.<br /><br />The migration service contains the following functions:<br /><br />a) General Ledger Migration Cockpit for executing the migration<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;- Process tree with scenario-based management by the individual activities of the migration<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;- Monitor of the migration steps with status management<br /><br />b) Remote service session to validate the scenario and analyze the system<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;- Possible consistency checks against target Customizing of the new general ledger accounting<br /><br />c) Remote service session to validate tests<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;- Technical validation checks in a test system after a data migration<br /><br />d) Development support by the NewGL migration back office<br /><br /><br />For more information about this migration service with a description of the migration scenario, a migration guide and other useful information, see SAP&#160;Support Portal: https://support.sap.com/glmig.&#160;<br /><br />If you have any further questions about using this service, contact <EMAIL>.<br /><br />Note that this service refers exclusively to support for the technical migration of the data from the classic to the new general ledger.<br /><br />You can also get consulting support for the business concepts and the implementation of the new general ledger from your relevant subsidiary or from qualified partners.<br /><br />The customer is responsible for drawing up the blueprint and the concept of the new general ledger including its integration with other components, industry-specific and country-specific versions. The customer, together with the consultants, must ensure that the implementation functions correctly, and is supported by SAP. This check is not part of the SAP General Ledger Migration migration service.<br /><br />To access the site mentioned above, you require authorization for SAP Service Marketplace and an S user ID. Contact your system administrator regarding this.<br /><br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I059246)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I059246)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000812919/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000812919/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000812919/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000812919/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000812919/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000812919/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000812919/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000812919/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000812919/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "NewGL_Mig_Service_new.pdf", "FileSize": "274", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000078492005&iv_version=0014&iv_guid=63F324F9D5A13F4A8232D69628BF824B"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "966000", "RefComponent": "CA-JVA", "RefTitle": "mySAP new general ledger and Joint Venture Accounting", "RefUrl": "/notes/966000"}, {"RefNumber": "890237", "RefComponent": "FI-GL-FL", "RefTitle": "New GL with document splitting: Legacy data transfer", "RefUrl": "/notes/890237"}, {"RefNumber": "1619168", "RefComponent": "FI-GL-MIG", "RefTitle": "Overview of the different migration scenarios", "RefUrl": "/notes/1619168"}, {"RefNumber": "1330451", "RefComponent": "FI-GL", "RefTitle": "SAP ERP: Changing leading reporting", "RefUrl": "/notes/1330451"}, {"RefNumber": "1070629", "RefComponent": "FI-GL-MIG-BO", "RefTitle": "FAQs: Migration to General Ledger Accounting (new)", "RefUrl": "/notes/1070629"}, {"RefNumber": "1039752", "RefComponent": "FI-GL-MIG", "RefTitle": "New G/L migration: Restrictions, important information", "RefUrl": "/notes/1039752"}, {"RefNumber": "1030497", "RefComponent": "PSM-FM", "RefTitle": "SAP ERP 6.0: Public sector scenarios in new general ledger", "RefUrl": "/notes/1030497"}, {"RefNumber": "1014369", "RefComponent": "FI-GL-MIG", "RefTitle": "NewGL migration: Availability of Development Support", "RefUrl": "/notes/1014369"}, {"RefNumber": "1014364", "RefComponent": "FI-GL-MIG", "RefTitle": "New G/L migration: Information, prerequisites, performance", "RefUrl": "/notes/1014364"}, {"RefNumber": "1006320", "RefComponent": "PSM-EC", "RefTitle": "Expenditure Certification: Certifying Payment Amounts", "RefUrl": "/notes/1006320"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1070629", "RefComponent": "FI-GL-MIG-BO", "RefTitle": "FAQs: Migration to General Ledger Accounting (new)", "RefUrl": "/notes/1070629 "}, {"RefNumber": "999614", "RefComponent": "FI-GL-GL", "RefTitle": "Activate classic GL Accounting/deactivate new GL Accounting", "RefUrl": "/notes/999614 "}, {"RefNumber": "1014364", "RefComponent": "FI-GL-MIG", "RefTitle": "New G/L migration: Information, prerequisites, performance", "RefUrl": "/notes/1014364 "}, {"RefNumber": "1619168", "RefComponent": "FI-GL-MIG", "RefTitle": "Overview of the different migration scenarios", "RefUrl": "/notes/1619168 "}, {"RefNumber": "1030497", "RefComponent": "PSM-FM", "RefTitle": "SAP ERP 6.0: Public sector scenarios in new general ledger", "RefUrl": "/notes/1030497 "}, {"RefNumber": "966000", "RefComponent": "CA-JVA", "RefTitle": "mySAP new general ledger and Joint Venture Accounting", "RefUrl": "/notes/966000 "}, {"RefNumber": "1330451", "RefComponent": "FI-GL", "RefTitle": "SAP ERP: Changing leading reporting", "RefUrl": "/notes/1330451 "}, {"RefNumber": "1039752", "RefComponent": "FI-GL-MIG", "RefTitle": "New G/L migration: Restrictions, important information", "RefUrl": "/notes/1039752 "}, {"RefNumber": "1014369", "RefComponent": "FI-GL-MIG", "RefTitle": "NewGL migration: Availability of Development Support", "RefUrl": "/notes/1014369 "}, {"RefNumber": "890237", "RefComponent": "FI-GL-FL", "RefTitle": "New GL with document splitting: Legacy data transfer", "RefUrl": "/notes/890237 "}, {"RefNumber": "1006320", "RefComponent": "PSM-EC", "RefTitle": "Expenditure Certification: Certifying Payment Amounts", "RefUrl": "/notes/1006320 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}