{"Request": {"Number": "89671", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 399, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000318052017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=C667D294A87E0B73E75CDDFBDBBB104E"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "89671"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.04.1998"}, "SAPComponentKey": {"_label": "Component", "value": "MM-PUR-VM-SET"}, "SAPComponentKeyText": {"_label": "Component", "value": "Subsequent Settlement"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Purchasing", "value": "MM-PUR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Vendor-Material Relationships and Conditions", "value": "MM-PUR-VM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Subsequent Settlement", "value": "MM-PUR-VM-SET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM-SET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "89671 - Incorr.update incomes f. debt-side sttlmnt account."}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>During settlement of a subsequent settlement agreement by SD billing document, under certain circumstances when you release the document for accounts, the system may not update the incomes for individual items in the Logistics Information System statistics.<br />This affects agreements requiring periodic settlement that are settled on the debit side (settlement types 2 and 3). Agreements to be settled on the credit side (whose accounting document is an MM credit memo, settlement type 1) are not affected.<br />As a result, incorrect settlements occur during final settlement. In general, the volume rebate incomes (claims to vendors) are generally too high since the system does not correctly determine or clear the incomes from the partial or interim settlements from the Logistics Information System.<br />The error occasionally occurs for accounting documents which include a large number of billing items. This is generally the case for arrangements with several conditions (main condition records).<br />In addition, the system may fail to reverse the update of the incomes if you cancel a billing document.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Subsequent settlement, volume-based rebate, settlement, Transactions MEB4, MEB2, MEU2 and VF11, report RWMBON01</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The cause of the problem is the implementation of various notes concerning billing and the sales information system.<br />Credit-side settlement type 1 is not affected.<br />The error concerning the occasionally missing update of incomes occurs after you implement Note 70091, that is:</p> <UL><LI>for Releases 3.0F and 3.1G, after you implement Note 70091,</LI></UL> <UL><LI>for Releases 3.0F and 3.1G, after you apply Hot Package SAPKH30F04,</LI></UL> <p><br />but you have not yet implemented Note 74490 or applied Hot Package SAPKH30F15.<br /><br />The problem concerning the missing update reversal after you cancel billing documents occurs after you implement correction Note 74990 for Note 70091, that is:</p> <UL><LI>for Releases 3.0F and 3.1G, after you implement Note 70091 and then Note 74990 to correct it</LI></UL> <UL><LI>for Releases 3.0F and 3.1G, after you apply Hot Package SAPKH30F15,</LI></UL> <UL><LI>for Release 3.1H, in general.</LI></UL> <p><br />The R/3 Retail industry solution is also affected since it contains Hot Package SAPKH30F04 but not correction Note 74990.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The problems are caused by various program errors. These errors are finally corrected in Release 3.1I.<br />If you not have done so already, implement Notes 70091, 74990, and 95122. Note 100695 could also be relevant.<br />In addition, implement the attached program corrections.<br />If you have settled agreements (in Release 3.0F or 3.1G)</p> <UL><LI>after you implement Note 70091 but before you implement Note 74990 or</LI></UL> <UL><LI>after you apply Hot Package SAPKH30F04 but before you apply Hot Package SAPKH30F15,</LI></UL> <p><br />use Transaction MEB9 (Statement: statistical data) or MEB6 (Listing of Vendor Business Volume Data) to check whether the system has updated incomes for the settled period condition records. You can restrict the selection of data to the period(s) in question. For settlement on the debit side on plant level, make sure that incomes exist for every plant (separate billing item).</p> <UL><LI>In Release 3.0F or 3.1G, after you implement Note 74990 the following the implementation of Note 70091</LI></UL> <UL><LI>after you apply Hot Package SAPKH30F15 or</LI></UL> <UL><LI>in Release 3.1H in general,</LI></UL> <p><br />check the incomes as well as the settlements for those agreements with accounting documents (billing documents) cancelled after implementation of Note 74990.<br />You can use Report ZLISTFAK to determine the affected agreements (see attached corrections, these are not contained in the standard R/3 System). You can restrict the selection by entering a 'Billing document created after' date (created after implementation of the note or after you upgrade your R/3 release).<br />If you find errors or require further support, please contact SAP. If necessary, update the incomes from the accounting documents.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023678)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "95122", "RefComponent": "SD-BF-CM", "RefTitle": "Open bill. doc. value/incorr. sales vol. after cancellation", "RefUrl": "/notes/95122"}, {"RefNumber": "74990", "RefComponent": "SD-BF-CM", "RefTitle": "Wrong open billing document value after reversal", "RefUrl": "/notes/74990"}, {"RefNumber": "70091", "RefComponent": "SD-IS-DC", "RefTitle": "Performance SIS sales order, delivery, billing doc.", "RefUrl": "/notes/70091"}, {"RefNumber": "40147", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 3.0", "RefUrl": "/notes/40147"}, {"RefNumber": "100695", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "No update during reversal", "RefUrl": "/notes/100695"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "40147", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 3.0", "RefUrl": "/notes/40147 "}, {"RefNumber": "100695", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "No update during reversal", "RefUrl": "/notes/100695 "}, {"RefNumber": "70091", "RefComponent": "SD-IS-DC", "RefTitle": "Performance SIS sales order, delivery, billing doc.", "RefUrl": "/notes/70091 "}, {"RefNumber": "95122", "RefComponent": "SD-BF-CM", "RefTitle": "Open bill. doc. value/incorr. sales vol. after cancellation", "RefUrl": "/notes/95122 "}, {"RefNumber": "74990", "RefComponent": "SD-BF-CM", "RefTitle": "Wrong open billing document value after reversal", "RefUrl": "/notes/74990 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30F", "To": "31H", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 30F", "SupportPackage": "SAPKH30F25", "URL": "/supportpackage/SAPKH30F25"}, {"SoftwareComponentVersion": "SAP_APPL 31H", "SupportPackage": "SAPKH31H09", "URL": "/supportpackage/SAPKH31H09"}, {"SoftwareComponentVersion": "SAP_HR 31H", "SupportPackage": "SAPKE31H09", "URL": "/supportpackage/SAPKE31H09"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 2, "URL": "/corrins/**********/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}