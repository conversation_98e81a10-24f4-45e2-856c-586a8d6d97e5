{"Request": {"Number": "413807", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1129, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015027662017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000413807?language=E&token=722E2CD71F889F4884827C2FCE93E86A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000413807", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000413807/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "413807"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.04.2002"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW only - Business Content and Extractors"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "413807 - BW/LIS: DBIF_RSQL_INVALID_CURSOR in data extraction"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><OL>1. The data extraction from LIS information structures terminates with a short dump:</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DBIF_RSQL_INVALID_CURSOR <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Invalid interruption of a database selection. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The error occurs in the form routine 'G_SELECT' of a generated program 'GP...'. onto. The program is called by the function module 'MCS_BIW_LIS_API'. <OL>2. The extraction of data from logistics extract structures terminates with a short dump:</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DBIF_RSQL_INVALID_CURSOR <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Invalid interruption of a database selection. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The error occurs in the function module MCEX_BW_LO_API. <p></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>PlugIn, OLTP, PI 2000.1, PI-A 2000.1, PI 2000.2, PI-A 2000.2, PI, 2001.1, PI-A 2001.1, DBIF_RSQL_INVALID_CURSOR, USER-EXIT, RSAP0001, EXIT_SAPLRSAP_001, MCS_BIW_LIS_API, G_SELECT, MCEX_BW_LO_API, LXRSAU01, ZXRSAU01, LRSAPF06, RSA3, extraction, extractor<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Cause: The database cursor during the data extraction from</p> <OL>1. LIS information structures or</OL> <OL>2. Logistics extract structures</OL> <p><br />are completed in the user exit for transaction data by the commands 'COMMIT WORK' or 'ROLLBACK WORK' after the extraction of the first data package. After the next data package (FETCH NEXT CURSOR) has been read, the short dump occurs.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>In the function module EXIT_SAPLRSAP_001 (movement data) of the SAP enhancement RSAP0001, you must not use any 'COMMIT WORK' or 'ROLLBACK WORK' commands in the source code for the extraction from information structures (Extractor MCS_BIW_LIS_API - DataSource 2LIS_nn_Smmm; nn = application number and Smmm = information structure) or logistics extract structures (extractor MCEX_BW_LO_API - DataSource 2LIS_nn_evhie; nn = application number, ev = event(s), hie = hierarchy).<br />However, the function module 'DB_COMMIT' can be used.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BCT-PP (Production Planning and Control)"}, {"Key": "Other Components", "Value": "BW-BCT-LE-TRA (BW only - Transport)"}, {"Key": "Other Components", "Value": "BW-BCT-PP-LIS (BW only - Logistics Information System)"}, {"Key": "Other Components", "Value": "BW-BCT-MM-IM (BW only - Inventory Management)"}, {"Key": "Other Components", "Value": "BW-BCT-SD-LIS (BW only - LIS)"}, {"Key": "Other Components", "Value": "BW-BCT-MM-LIS (BW only - Logistics Information System)"}, {"Key": "Other Components", "Value": "BW-BCT-MM-PUR (BW only - Purchase)"}, {"Key": "Other Components", "Value": "BW-BCT-LO (BW only - Logistics - General)"}, {"Key": "Other Components", "Value": "BW-BCT-LO-LIS (BW only - Logistics Information System)"}, {"Key": "Other Components", "Value": "BW-BCT-LE (Logistics Execution)"}, {"Key": "Other Components", "Value": "BW-BCT-OIL (BW only - BW Content IS-OIL)"}, {"Key": "Other Components", "Value": "BW-BCT-MM (BW only - Materials Management)"}, {"Key": "Other Components", "Value": "BW-BCT-PP-PP (BW only - Production Planning)"}, {"Key": "Other Components", "Value": "BW-BCT-SD-BIL (BW only - Billing)"}, {"Key": "Other Components", "Value": "BW-BCT-LE-SHP (BW only - Shipping)"}, {"Key": "Other Components", "Value": "BW-BCT-ISR (BW only - Retail and Consumer Products)"}, {"Key": "Other Components", "Value": "BW-BCT-SD (BW only - Sales and Distribution)"}, {"Key": "Other Components", "Value": "BW-BCT-SD-SLS (BW only - Sales)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D028886)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D028886)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000413807/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000413807/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000413807/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000413807/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000413807/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000413807/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000413807/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000413807/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000413807/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "586163", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Composite SAP Note for SAP ERP Inventory Management in SAP BW", "RefUrl": "/notes/586163 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "31H", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "PI", "From": "2000_1_31H", "To": "2000_1_46C", "Subsequent": ""}, {"SoftwareComponent": "PI", "From": "2000_2_31I", "To": "2000_2_46C", "Subsequent": ""}, {"SoftwareComponent": "PI", "From": "2001_1_31I", "To": "2001_1_46C", "Subsequent": ""}, {"SoftwareComponent": "PI", "From": "2001_2_31I", "To": "2001_2_46C", "Subsequent": ""}, {"SoftwareComponent": "PI-A", "From": "2000_1_31H", "To": "2000_1_45B", "Subsequent": ""}, {"SoftwareComponent": "PI-A", "From": "2000_2_31I", "To": "2000_2_45B", "Subsequent": ""}, {"SoftwareComponent": "PI-A", "From": "2001_1_31I", "To": "2001_1_45B", "Subsequent": ""}, {"SoftwareComponent": "PI-A", "From": "2001_2_31I", "To": "2001_2_45B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}