{"Request": {"Number": "1080977", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 255, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016349122017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001080977?language=E&token=8220DD5F1312A486D95F5FF54FE59CDD"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001080977", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001080977/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1080977"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.02.2018"}, "SAPComponentKey": {"_label": "Component", "value": "BC-TWB-TST-ECA"}, "SAPComponentKeyText": {"_label": "Component", "value": "eCATT Extended Computer Aided Test Tool"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Test Workbench", "value": "BC-TWB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-TWB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Testing Tools", "value": "BC-TWB-TST", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-TWB-TST*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "eCATT Extended Computer Aided Test Tool", "value": "BC-TWB-TST-ECA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-TWB-TST-ECA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1080977 - Controls and eCATT"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note provides information about problems that occur when you use eCATT (Computer Aided Test Tool) to record transactions that contain the controls that are available as of Release 46A.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SECATT, eCATT, TCD, controls, SCAT, CATT, SCEM, SCPM</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This problem is caused by transactions that contain the controls that are available as of Release 46A.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>General information</strong></p>\r\n<p>If you use Enjoy controls (for example, tree control, ALV Grid) in transactions, when you use the eCATT command TCD to record the test case, the system records only the control-relevant data stream between the application server and the front end. When you run the test case, the Enjoy controls are only simulated. This means that the controls themselves are not active or \"do not live in the front end\", instead you import only the recorded data stream again. When you import this data stream, the transaction behaves exactly as if a user operates the control during the processing.<br />An advantage of this method is that, when you run the TCD command, you do not require a front end. This means that there is no platform dependency, and it enables you to execute the TCD command in the background.<br />Technically, this is achieved by using the eCATT command TCD to record the data stream from and to the control framework. <strong>This ensures that you can use the TCD command in eCATT to record each control.</strong><br />Usually, it is not clear to the user which type of information is delivered from or to the control. Therefore, the user only notices that there is an increased sensitivity towards program changes, interface changes or Customizing changes compared to transactions that consist only of screens (invalidation of data stream, see below).<br />In certain contexts (for example, if the recorded data is no longer suitable within a ClassConstructor call), CLASSCONSTRUCTOR FAILED dumps may also occur.</p>\r\n<p><strong>Remarks</strong></p>\r\n<ol>1. You cannot use eCATT to test whether the control itself is working, because the control is not active during the run. </ol><ol>2. When you run the TCD command in the foreground, the control is not visible, because it is not instantiated.</ol><ol>3. You cannot edit or set parameters for input values or actions in a Control.</ol><ol>4. The ALV (that is frequently used) is a control even if the ALV Classic may easily be mixed-up with a normal TableControl.</ol><ol>5. For transactions containing controls that were recorded using RFC, you may not be able to use RFC to run these.</ol>\r\n<p><strong>Remarks for application developers</strong></p>\r\n<ol>1. If possible, create pushbuttons on the normal menu bar and use the usual OK code method to process these.</ol><ol>2. If you require the pushbuttons in the control itself, if possible, use the application events to process these instead of using the sys events.</ol>\r\n<p><strong>Invalidation of the data stream</strong></p>\r\n<ol>1. When you run the eCATT TCD command, program changes that may also be invisible to the user (for example, additional get properties, method calls, changes to the sequence of calls or the data definitions) may lead to errors.</ol><ol>2. If the control contains user-specific data (for example, for the personalization), you cannot run the TCD under a different user.</ol><ol>3. If the data stream contains time or date information, you may no longer be able to run the command.</ol><ol>4. For control contents that were created at run time (for example, additional entries in the tree control), processing that is dependent on the cursor position may fail.</ol><ol>5. If the control itself is active (for example, since it processes arithmetic operations or since it uses the front end to read data), it is most likely, that the data stream in the run no longer corresponds to the current status.</ol>\r\n<p><strong>Tips &amp; Tricks</strong></p>\r\n<ol>1. Always use the latest SAPGUI (internal).</ol><ol>2. Wherever possible, you should avoid the use of the control and should use the corresponding menu options/pushbutton functions, instead. In particular, this applies to the double-click function.</ol><ol>3. When you select an entry from ALV or Grid Control, avoid scrolling and, if possible, work using a positioning function.</ol><ol>4. Where possible, during recording, use additional \"returns\" to force a dialog for the R/3.</ol><ol>5. As soon as you must use the control to execute the text case, we recommend that you use the new eCATT commands SAPGUI, GETGUI (as of eCATT 6.40) and CHEGUI (as of eCATT 6.40) instead of using the command TCD.</ol>\r\n<ul>\r\n<ul>\r\n<li>Advantage: SAPGUI, GETGUI and CHEGUI use the scripting interface of the GUI to process the application that is to be tested. To be able to use this scripting interface, a front end must be actively available for the run.  Since this front end is available for the SAPGUI, GETGUI or CHEGUI commands anyway, these commands can use the scripting interface to operate the Enjoy controls. This means that you can use these commands to test even transactions that have a high level of use of the control.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Disadvantage 1: Since a front end must always be active for the GUI commands and this front end is always accessed using the scripting interface, the performance of the GUI command is worse than that of the TCD command. The performance of the run allows you to follow the individual actions on the screen.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Disadvantage 2: Since a front end must always be active for the GUI commands, batch input is no longer possible. This means that you must always run the GUI commands in the foreground and you can never run them in the background.</li>\r\n</ul>\r\n</ul></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D031829)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON> (D022885)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001080977/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001080977/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001080977/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001080977/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001080977/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001080977/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001080977/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001080977/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001080977/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "965439", "RefComponent": "BC-TWB-TST-ECA", "RefTitle": "eCATT deleting TCD control data causes ATT377", "RefUrl": "/notes/965439"}, {"RefNumber": "520119", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/520119"}, {"RefNumber": "373156", "RefComponent": "IS-U-CS-BT-IO", "RefTitle": "Log output not possible during CATT procedure in EC50E", "RefUrl": "/notes/373156"}, {"RefNumber": "370929", "RefComponent": "BC-TWB-TST-CAT", "RefTitle": "CATT Enjoy and long text", "RefUrl": "/notes/370929"}, {"RefNumber": "314738", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/314738"}, {"RefNumber": "311440", "RefComponent": "BC-ABA-SC", "RefTitle": "Batch input and controls", "RefUrl": "/notes/311440"}, {"RefNumber": "217437", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "ME21N/ME51N: Batch input and CATT not possible", "RefUrl": "/notes/217437"}, {"RefNumber": "198865", "RefComponent": "BC-TWB-TST-CAT", "RefTitle": "Controls and CATT", "RefUrl": "/notes/198865"}, {"RefNumber": "190453", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/190453"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1864062", "RefComponent": "BC-SEC-USR-ADM", "RefTitle": "Problems using Batch Input for User Maintenance", "RefUrl": "/notes/1864062 "}, {"RefNumber": "217437", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "ME21N/ME51N: Batch input and CATT not possible", "RefUrl": "/notes/217437 "}, {"RefNumber": "965439", "RefComponent": "BC-TWB-TST-ECA", "RefTitle": "eCATT deleting TCD control data causes ATT377", "RefUrl": "/notes/965439 "}, {"RefNumber": "370929", "RefComponent": "BC-TWB-TST-CAT", "RefTitle": "CATT Enjoy and long text", "RefUrl": "/notes/370929 "}, {"RefNumber": "373156", "RefComponent": "IS-U-CS-BT-IO", "RefTitle": "Log output not possible during CATT procedure in EC50E", "RefUrl": "/notes/373156 "}, {"RefNumber": "198865", "RefComponent": "BC-TWB-TST-CAT", "RefTitle": "Controls and CATT", "RefUrl": "/notes/198865 "}, {"RefNumber": "520119", "RefComponent": "XX-RC-CA-CAT", "RefTitle": "Remote Consulting - Test management services", "RefUrl": "/notes/520119 "}, {"RefNumber": "314738", "RefComponent": "BC-TWB", "RefTitle": "CATT: Tips & tricks", "RefUrl": "/notes/314738 "}, {"RefNumber": "190453", "RefComponent": "BC-TWB", "RefTitle": "Advance clarification of a CATT message", "RefUrl": "/notes/190453 "}, {"RefNumber": "311440", "RefComponent": "BC-ABA-SC", "RefTitle": "Batch input and controls", "RefUrl": "/notes/311440 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}