{"Request": {"Number": "77258", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 855, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014509732017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000077258?language=E&token=E3E9646C120F44C29E30A699B5545069"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000077258", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000077258/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "77258"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "07.12.2001"}, "SAPComponentKey": {"_label": "Component", "value": "MM-PUR-VM-SET"}, "SAPComponentKeyText": {"_label": "Component", "value": "Subsequent Settlement"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Purchasing", "value": "MM-PUR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Vendor-Material Relationships and Conditions", "value": "MM-PUR-VM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Subsequent Settlement", "value": "MM-PUR-VM-SET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM-SET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "77258 - Required fields settlement document"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>One of the two following error messages is generated in the list log during the credit-side settlement accounting of an arrangement of the subsequent settlement (settlement document is credit memo document, settlement type is '1', see Customizing arrangement type)</p> <UL><LI>Error message 00055 \"Required entry not made\",</LI></UL> <p><br />or</p> <UL><LI>Error message 00344 \"No batch input data for screen SAPMM08R &amp;\" (&amp; is the screen number which can be different)</LI></UL> <p><br />in connection with error message NN240 \"Not possible to create any settlement documents\".</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Subsequent settlement, volume-based rebate, settlement, Report RWMBON01, Transactions MEB4, MEB2 and MEU2.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>A credit memo document (invoice verification) is created as settlement document for each condition record to be settled within the credit-side settlement type. This is done via a batch input session.You can define certain fields as required-entry fields via different Customizing settings in Financial Accounting.<br />The document is created automatically without any possibility for a manual entry.It is therefore necessary to make the Customizing settings in Financial Accounting so that they are compatible with the data made available by the subsequent settlement.In contrast, the subsequent settlement might have to supply fields with valid values.<br />Missing values cause the above errors if it is set in Financial Accounting that the fields are required-entry fields and no useful default value can be predefined.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>This note should allow you to carry out an independent problem analysis. As this applies to Customizing settings in Financial Accounting you may want to consult the person responsible in your company.<br />You have two possible solutions to narrow down the problem:</p> <UL><LI>Enter a credit memo (first possible solution) manually for testing purposes,</LI></UL> <UL><LI>Check the settings directly in Financial Accounting Customizing.</LI></UL> <p><br />However, the procedure of the second possible solution can demand a high effort since you have to check a lot of settings (fields).We therefore recommend the procedure according to the first possible solution.<br /></p> <OL>1. Possible solution</OL> <UL><LI>Determine the G/L accounts separately according to debit and credit indicators used for the subsequent settlement (account key BO1 for provisions for accrued income and BO2 for income) using Transaction OMR0 (Account assignment pushbutton). You need your chart of accounts.You can ignore account key BO1 if you do not use provisions for accrued income.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Accounts 192700 (provisions for accrued income) and 281200 (income) are delivered with the R/3 Standard System.</p> <UL><LI>Call Transaction MR01, Invoice Verification.Select 'Credit memo', enter the document date (current date), document type RA (document type for subsequent credit memo settlement) and your company code.Enter a valid currency (for example, the country currency) and enter the condition granter in the 'Vendor' field.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;field (BKPF-XBLNR) or the 'Header text' field (BKPF-BKTXT) are required-entry fields, read Solution, Document type (see below).This can be the cause of error message 00055. If required enter any data in the fields ands complete the check. Press 'Enter'. Press Enter.</p> <UL><LI>You come to the screen 'Enter credit memo: Vendor Items'. Enter any value in the 'Amount' field.The 'Allocation' field (BSEG-ZUONR) and 'Text' field (BSEG-SGTXT) in the 'Allocation/text' section may be required-entry fields.This can be the cause of error message 00055. Refer to the solution section, posting key (see below).If required enter any data in the fields and carry on with the checks.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Press Enter. The system may display an additional screen that contains required entry fields.These may be the cause of error message 00344. The following fields are affected for foreign payments:State central bank indicator (BSEG-LZBKZ), Supplying country (BSEG-LANDL), Service indicator (foreign payment) (BSEG-DIEKZ) and Customs Date (BSEG-ZOLLD) and Customs Tariff Number (BSEG-ZOLLT) for foreign payments are affected.Consider sections Solution, posting keys and accounts (see below).</p> <UL><LI>You come to the screen 'Enter credit memo: Document overview in ...'Via 'Edit -&gt; New item -&gt; G/L account' for each of the accounts to be determined enter a posting item together with the respective credit and debit indicators. The screen 'Enter Invoice: G/L Account Item' is displayed.Again, enter any amount and a valid tax code. Other required entry fields on this screen can be the cause of error message 00055. This can be the cause of error message 00055. If required enter any data in the fields and complete the check.Other required entry fields on this screen can be the cause of error message 00055. This, in turn, can be the fields 'Allocation' (BSEG-ZUONR) and 'Text' (BSEG-SGTXT).This can be the cause of error message 00055. Read Solution, Accounts (see below). Enter the values into the fields, if necessary, and press 'ENTER'. An additional screen containing required-entry fields may be displayed. These may be the cause of error message 00344. You consider the solution section, account (see below).Enter any values in the required-entry fields and press 'Enter'.You return to the 'Enter Invoice: Document Overview in ...' screen.Check other G/L accounts in the same way.</LI></UL> <p><br />All problematic fields are now determined.You, of course, do not need to post the credit memo document.</p> <OL>2. Possible solution</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In Financial Accounting or in the invoice verification, you can define certain fields as optional-entry fields or required-entry fields, or also hide fields.You can do this at document type and posting key level as well as at account level (vendor reconciliation account or G/L accounts).Required-entry fields that are not supplied by the settlement program of the subsequent settlement and for which no default value (invoice verification) is defined can cause problems (see above under 'Cause and Preconditions').</p> <OL><OL>a) Check the document type.</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Call Transaction OMB0.Choose Document type RA (document type for subsequent credit memo settlement). Check the 'Details necessary for document entry' section.If the fields 'Reference number' or 'Document header' text are defined as required-entry fields, read Solution, Document type.This can be the cause of error message 055(00).</p> <OL><OL>b) Check the posting keys.</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The invoice verification uses posting keys 21 (debit posting) and 31 (credit posting) for the vendor posting as well as posting keys 40 (debit posting) and 50 (credit posting) for the G/L account.Call Transaction OB41, select the posting key and then choose 'Edit -&gt; Field status -&gt; Field status'.Required-entry fields, in particular the fields 'Allocation number' (BSEG-ZUONR) , 'Text' (BSEG-SGTXT) and fields of the area additional account assignments (cost center (BSEG-KOSTL), profit center (BSEG-PRCTR)) can cause problems (error message 00055), see Solution, Posting key.</p> <OL><OL>c) Check the accounts (vendor reconciliation account and G/L accounts).</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Determine the G/L accounts of the subsequent settlement (see above) if you have not already done so.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Determine the reconciliation account of the condition granter (see the vendor master).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Determine the field status group of the accounts (reconciliation account and G/L accounts) using Transaction FS03.After you have entered the account number and the company code, the screen 'Display G/L Account: Control Chart of Accounts' is displayed.Press Enter to open the 'Display G/L Account:Control Company Code' screen.Here you find the 'Field status group' field.The field status group controls the selection of the required-entry fields, among other things, when you enter an item while creating the credit memo.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Determine the field status variant of your company code using Transaction OBC5.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can display or change the field status group (depending on the field status variant) using Transaction OBC4.Select the group and choose 'Edit field status'.Check the required-entry fields for all listed groups.These might be the cause of the error messages.<br /></p> <OL>3. Solution possibilities</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You certainly define the respective fields as optional-entry fields instead of required-entry fields, but then they are missing for settlement documents of the subsequent settlement.This is certainly not always useful and it can only be decided on an individual basis.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In detail:</p> <UL><LI>Level Document type</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can define fields in the screen 'Credit Memo:Initial' at document type level (R/3 Standard:Document type is RA) as required-entry fields.This applies to the fields 'Reference number' (XBKPF-XBLNR) and 'Document header text' (BKPF-BKTXT), see Note 48497. Also bear in mind the reverse document type, if necessary.This should also be document type RA in the R/3 Standard.</p> <UL><LI>Level Posting keys</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Level Accounts (reconciliation account or G/L accounts)You can define a number of fields as required-entry fields for the posting key.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;See Note 48497 for the fields 'Allocation' (BSEG-ZUONR) and 'Text' (BSEG-SGTXT).For the fields of the area additional account assignments (for example, Cost center (BSEG-KOSTL), profit center (BSEG-PRCTR)) refer to Note 96131.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Read Note 76713 for the fields 'State central bank indicator' (BSEG-LZBKZ), 'Supplying country' (BSEG-LANDL) and 'Service indicator' (BSEG-DIEKZ) as well as 'Customs date' (BSEG-TOLLD) and 'Customs tariff number' (BSEG-ZOLLT).</p> <UL><LI>Read Note 76713 for the fields 'State central bank indicator' (BSEG-LZBKZ), 'Supplying country' (BSEG-LANDL) and 'Service indicator' (BSEG-DIEKZ) as well as 'Customs date' (BSEG-TOLLD) and 'Customs tariff number' (BSEG-ZOLLT).</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Level Accounts (reconciliation account or G/L accounts)You can define a number of fields as required-entry fields for the posting key.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;See Note 48497 for the fields 'Allocation' (BSEG-ZUONR) and 'Text' (BSEG-SGTXT).For the fields of the area additional account assignments (for example, Cost center (BSEG-KOSTL), profit center (BSEG-PRCTR)) refer to Note 96131.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Read Note 76713 for the fields 'State central bank indicator' (BSEG-LZBKZ), 'Supplying country' (BSEG-LANDL) and 'Service indicator' (BSEG-DIEKZ) as well as 'Customs date' (BSEG-TOLLD) and 'Customs tariff number' (BSEG-ZOLLT). The fields mentioned last are only important for the reconciliation accounts (vendor), see note.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Other possibly missing fields can either not be supplied usefully with values or are currently not supplied by the settlement program.In addition, the invoice verification transaction also does not propose a value.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You should therefore not define these fields as required-entry fields, if possible.You should check, if necessary, whether you can change the account settings (reconciliation account or G/L accounts) or whether you can create suitable new G/L accounts.<br /><br />Contact SAP if you are still having problems with regard to the above.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "HIER"}, {"Key": "Transaction codes", "Value": "MR01"}, {"Key": "Transaction codes", "Value": "MEB4"}, {"Key": "Transaction codes", "Value": "MEB2"}, {"Key": "Transaction codes", "Value": "MEU2"}, {"Key": "Transaction codes", "Value": "OB41"}, {"Key": "Transaction codes", "Value": "FS03"}, {"Key": "Transaction codes", "Value": "OBC4"}, {"Key": "Transaction codes", "Value": "OMB0"}, {"Key": "Transaction codes", "Value": "OBC5"}, {"Key": "Transaction codes", "Value": "OMR0"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023678)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000077258/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000077258/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000077258/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000077258/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000077258/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000077258/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000077258/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000077258/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000077258/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "96131", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseq.settlement:default accnt assgmnts not copied", "RefUrl": "/notes/96131"}, {"RefNumber": "76713", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: Foreign payments", "RefUrl": "/notes/76713"}, {"RefNumber": "48497", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Item text, allocation no.", "RefUrl": "/notes/48497"}, {"RefNumber": "40147", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 3.0", "RefUrl": "/notes/40147"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "76713", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: Foreign payments", "RefUrl": "/notes/76713 "}, {"RefNumber": "40147", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 3.0", "RefUrl": "/notes/40147 "}, {"RefNumber": "96131", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseq.settlement:default accnt assgmnts not copied", "RefUrl": "/notes/96131 "}, {"RefNumber": "48497", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Item text, allocation no.", "RefUrl": "/notes/48497 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "300", "To": "31I", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}