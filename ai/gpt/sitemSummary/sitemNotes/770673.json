{"Request": {"Number": "770673", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 319, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015754452017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000770673?language=E&token=71810BDA20F95A7E3EEC7D4995ECAC93"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000770673", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000770673/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "770673"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 25}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.07.2007"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-LCA"}, "SAPComponentKeyText": {"_label": "Component", "value": "liveCache Applications"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "liveCache Applications", "value": "BC-DB-LCA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-LCA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "770673 - /SAPAPO/OM_LC_UPGRADE_41: Problems during the upgrade"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Various problems may occur when you upgrade from APO 3.0, APO 3.1 or SCM 4.0 to SCM 4.1. The notes that are currently available are listed here.<br />Note that you must implement or refer to certain notes before you carry out the upgrade.<br />If you are using CDP, refer to Note 724946.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Before Release 4.1 (Prepare, before Section A):<br />Note&#x00A0;&#x00A0;&#x00A0;&#x00A0; SP&#x00A0;&#x00A0;&#x00A0;&#x00A0; Priority Short Text<br />1035428 -&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 /SAPAPO/DELETE_LC_ANCHORS may not run during upgrade<br />977717&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4&#x00A0;&#x00A0;Upgrading a system with DP data only<br />953030&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Upgrade to 4.1: fixed pegging on negative stocks<br />918238&#x00A0;&#x00A0;25/30&#x00A0;&#x00A0;4 Check on invalid secondary resources<br />846953&#x00A0;&#x00A0;24/30&#x00A0;&#x00A0;4 RPM: if RPM is not active, it must not be error message<br />796317&#x00A0;&#x00A0;23/29&#x00A0;&#x00A0;4 Extended check in /sapapo/om_check_activities<br />757196&#x00A0;&#x00A0;21/30&#x00A0;&#x00A0;2 Correction: Extended check in /sapapo/om_check_activities<br />750727&#x00A0;&#x00A0;21/28&#x00A0;&#x00A0;3 Enhanced check in /sapapo/om_check_activities<br />747060&#x00A0;&#x00A0;21/28&#x00A0;&#x00A0;3 Enhanced check in /sapapo/om_check_activities<br />744043&#x00A0;&#x00A0;21/28&#x00A0;&#x00A0;3 Enhanced check in /sapapo/om_check_activities<br />728288&#x00A0;&#x00A0;20/28&#x00A0;&#x00A0;2 Integrated consistency check suborders<br /><br />In Release 4.1 (before Section C):<br />Note&#x00A0;&#x00A0;&#x00A0;&#x00A0; SP&#x00A0;&#x00A0;&#x00A0;&#x00A0; Priority Short Text<br />1074340 -&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 Upgrade to SCM 4.1 only with DP data<br />1035428 -&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 /SAPAPO/DELETE_LC_ANCHORS may not run during upgrade<br />977717&#x00A0;&#x00A0; 12&#x00A0;&#x00A0;&#x00A0;&#x00A0; 4&#x00A0;&#x00A0;Upgrading a system with DP data only<br />961872&#x00A0;&#x00A0; 12&#x00A0;&#x00A0;&#x00A0;&#x00A0; 4 Upgrade analysis program: Recreation for tracing<br />948083&#x00A0;&#x00A0; 11&#x00A0;&#x00A0;&#x00A0;&#x00A0; 2 Upgrade SCM: External constraints not complete<br />931779&#x00A0;&#x00A0; 11&#x00A0;&#x00A0;&#x00A0;&#x00A0; 3 Upgrade: Error in /SAPAPO/OM_FIX_PEGGING_CHANGE<br />919315&#x00A0;&#x00A0; 10&#x00A0;&#x00A0;&#x00A0;&#x00A0; 2 Upgrade: Duplicate constraints<br />916054&#x00A0;&#x00A0;10&#x00A0;&#x00A0;&#x00A0;&#x00A0; 3 OM17: GETWA_NOT_ASSIGNED, /SAPAPO/SAPLMC01_R05<br />915365&#x00A0;&#x00A0;10&#x00A0;&#x00A0;&#x00A0;&#x00A0; 2 liveCache problems after converting to SCM 4.1<br />909411&#x00A0;&#x00A0;10&#x00A0;&#x00A0;&#x00A0;&#x00A0; 4 Traffic light for Customizing clients with active time series<br />871254&#x00A0;&#x00A0; 10&#x00A0;&#x00A0;&#x00A0;&#x00A0; 4 Analysis of delayed requests<br />883807&#x00A0;&#x00A0; 9&#x00A0;&#x00A0;&#x00A0;&#x00A0; 4&#x00A0;&#x00A0;Link to \"Analysis of delayed requests\"<br />883497&#x00A0;&#x00A0; 9&#x00A0;&#x00A0;&#x00A0;&#x00A0; 2 The creation of planning objects in the liveCache fails<br />878167&#x00A0;&#x00A0; 9&#x00A0;&#x00A0;&#x00A0;&#x00A0; 2 Many requests missing when upgrading from Release 3.x<br />854162&#x00A0;&#x00A0; 8&#x00A0;&#x00A0;&#x00A0;&#x00A0; 3 Comparison after upgrade from 30 to 41 is too strict<br />848569&#x00A0;&#x00A0; 7&#x00A0;&#x00A0;&#x00A0;&#x00A0; 2 Upgrade: Return code 315 when uploading liveCache data<br />808500&#x00A0;&#x00A0; 5&#x00A0;&#x00A0;&#x00A0;&#x00A0; 2 only Support Package 4: /sapapo/upgrade_lc_anchors does not work<br />785223&#x00A0;&#x00A0; 4&#x00A0;&#x00A0;&#x00A0;&#x00A0; 3 Loading the planning versions: Errors are ignored<br />779349&#x00A0;&#x00A0; 4&#x00A0;&#x00A0;&#x00A0;&#x00A0; 2 Short dump during upgrade of liveCache data<br />770142&#x00A0;&#x00A0; 4&#x00A0;&#x00A0;&#x00A0;&#x00A0; 4 Upgrade from 3.x to 4.x: Comparison<br />767501&#x00A0;&#x00A0; 4&#x00A0;&#x00A0;&#x00A0;&#x00A0; 3 Upgrade 3.x --&gt; 4.1: Improved log output when an error occurs<br />765108&#x00A0;&#x00A0; 4&#x00A0;&#x00A0;&#x00A0;&#x00A0; 4 Upgrade from 3.x to 4.x: Compare with sched. agr. delivery schedule lines<br />748039&#x00A0;&#x00A0; 3&#x00A0;&#x00A0;&#x00A0;&#x00A0; 4 /sapapo/delete_lc_anchors in the upgrade<br />743834&#x00A0;&#x00A0;Info&#x00A0;&#x00A0;&#x00A0;&#x00A0; Upgrade - /sapapo/om17<br />731099&#x00A0;&#x00A0; 2&#x00A0;&#x00A0;&#x00A0;&#x00A0; 4 Upgrade from 3.x to 4.x: Comparison</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D020876)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D020876)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000770673/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000770673/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000770673/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000770673/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000770673/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000770673/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000770673/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000770673/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000770673/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "977717", "RefComponent": "BC-DB-LCA", "RefTitle": "Upgrading a system with DP data only", "RefUrl": "/notes/977717"}, {"RefNumber": "974572", "RefComponent": "BC-DB-LCA", "RefTitle": "Missing ORDKEY entries during upgrade", "RefUrl": "/notes/974572"}, {"RefNumber": "961872", "RefComponent": "BC-DB-LCA", "RefTitle": "Upgrade analysis program: Recreation for tracing", "RefUrl": "/notes/961872"}, {"RefNumber": "953030", "RefComponent": "BC-DB-LCA", "RefTitle": "Upgrade to 4.1: fixed pegging on negative stocks", "RefUrl": "/notes/953030"}, {"RefNumber": "948083", "RefComponent": "BC-DB-LCA", "RefTitle": "Upgrade SCM: External constraints not complete", "RefUrl": "/notes/948083"}, {"RefNumber": "931779", "RefComponent": "BC-DB-LCA", "RefTitle": "Upgrade: Error in /SAPAPO/OM_FIX_PEGGING_CHANGE", "RefUrl": "/notes/931779"}, {"RefNumber": "919315", "RefComponent": "BC-DB-LCA", "RefTitle": "Upgrade: Duplicate constraints", "RefUrl": "/notes/919315"}, {"RefNumber": "918238", "RefComponent": "BC-DB-LCA", "RefTitle": "Check on invalid secondary resources", "RefUrl": "/notes/918238"}, {"RefNumber": "918146", "RefComponent": "SCM-APO-PPS", "RefTitle": "Upgrading to 4.1 - /SAPAPO/PEGKEY incorrectly initialized", "RefUrl": "/notes/918146"}, {"RefNumber": "917248", "RefComponent": "SCM-APO-PPS-PVW", "RefTitle": "Product View : Orders not displayed in the Elements tab", "RefUrl": "/notes/917248"}, {"RefNumber": "916054", "RefComponent": "SCM-APO-MD-RE", "RefTitle": "OM17: GETWA_NOT_ASSIGNED, /SAPAPO/SAPLMC01_R05", "RefUrl": "/notes/916054"}, {"RefNumber": "915365", "RefComponent": "SCM-APO-PPS", "RefTitle": "liveCache problems after converting to SCM 4.1", "RefUrl": "/notes/915365"}, {"RefNumber": "909411", "RefComponent": "BC-DB-LCA", "RefTitle": "Traffic light for Customizng clients with active time series", "RefUrl": "/notes/909411"}, {"RefNumber": "883807", "RefComponent": "BC-DB-LCA", "RefTitle": "Link to \"Analysis of delayed requests\"", "RefUrl": "/notes/883807"}, {"RefNumber": "883497", "RefComponent": "SCM-APO-PPS", "RefTitle": "The creation of planning objects in the liveCache fails", "RefUrl": "/notes/883497"}, {"RefNumber": "878167", "RefComponent": "BC-DB-LCA", "RefTitle": "Many requests missing when upgrading from Release 3.x", "RefUrl": "/notes/878167"}, {"RefNumber": "871254", "RefComponent": "BC-DB-LCA", "RefTitle": "Analysis of delayed requests", "RefUrl": "/notes/871254"}, {"RefNumber": "854162", "RefComponent": "BC-DB-LCA", "RefTitle": "Comparison after upgrade from 30 to 41 is too strict", "RefUrl": "/notes/854162"}, {"RefNumber": "848569", "RefComponent": "BC-DB-LCA", "RefTitle": "Upgrade: Return code 315 when uploading liveCache data", "RefUrl": "/notes/848569"}, {"RefNumber": "847492", "RefComponent": "SCM-APO-MD-RE", "RefTitle": "COMPUTE_BCD_OVERFLOW for /SAPAPO/OM_LC_UPGRADE_41", "RefUrl": "/notes/847492"}, {"RefNumber": "846953", "RefComponent": "SCM-APO-PPS-RPM", "RefTitle": "RPM: if RPM is not active, it must not be error message", "RefUrl": "/notes/846953"}, {"RefNumber": "809504", "RefComponent": "BC-DB-LCA", "RefTitle": "Implementing notes for section C", "RefUrl": "/notes/809504"}, {"RefNumber": "808500", "RefComponent": "BC-DB-LCA", "RefTitle": "/sapapo/upgrade_lc_anchors does not work", "RefUrl": "/notes/808500"}, {"RefNumber": "796317", "RefComponent": "BC-DB-LCA", "RefTitle": "Extended check in /sapapo/om_check_activities", "RefUrl": "/notes/796317"}, {"RefNumber": "779349", "RefComponent": "BC-DB-LCA", "RefTitle": "Short dump during upgrade of liveCache data", "RefUrl": "/notes/779349"}, {"RefNumber": "770142", "RefComponent": "BC-DB-LCA", "RefTitle": "Upgrade from 3.x to 4.x: Comparison", "RefUrl": "/notes/770142"}, {"RefNumber": "767501", "RefComponent": "BC-DB-LCA", "RefTitle": "Upgrade 3.x-->4.1: Improved log output when an error occurs", "RefUrl": "/notes/767501"}, {"RefNumber": "765108", "RefComponent": "BC-DB-LCA", "RefTitle": "Upgrade 3.x-->4.x: Compare with sched. agr.schedule lines", "RefUrl": "/notes/765108"}, {"RefNumber": "757196", "RefComponent": "BC-DB-LCA", "RefTitle": "Correction: Extended check in /sapapo/om_check_activities", "RefUrl": "/notes/757196"}, {"RefNumber": "750727", "RefComponent": "BC-DB-LCA", "RefTitle": "Extended check in /sapapo/om_check_activities", "RefUrl": "/notes/750727"}, {"RefNumber": "748039", "RefComponent": "BC-DB-LCA", "RefTitle": "/sapapo/delete_lc_anchors in the upgrade", "RefUrl": "/notes/748039"}, {"RefNumber": "747060", "RefComponent": "BC-DB-LCA", "RefTitle": "Enhanced check in /sapapo/om_check_activities", "RefUrl": "/notes/747060"}, {"RefNumber": "744043", "RefComponent": "BC-DB-LCA", "RefTitle": "Enhanced check in /sapapo/om_check_activities", "RefUrl": "/notes/744043"}, {"RefNumber": "743834", "RefComponent": "BC-DB-LCA", "RefTitle": "Upgrade - /sapapo/om 17", "RefUrl": "/notes/743834"}, {"RefNumber": "731099", "RefComponent": "BC-DB-LCA", "RefTitle": "Upgrade from 3.x to 4.x: Comparison", "RefUrl": "/notes/731099"}, {"RefNumber": "728288", "RefComponent": "BC-DB-LCA", "RefTitle": "Integrated consistency check for suborders", "RefUrl": "/notes/728288"}, {"RefNumber": "684406", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/684406"}, {"RefNumber": "1074340", "RefComponent": "BC-DB-LCA", "RefTitle": "Upgrade to SCM 4.1 only with DP data", "RefUrl": "/notes/1074340"}, {"RefNumber": "1064891", "RefComponent": "BC-DB-LCA", "RefTitle": "/SAPAPO/DELETE_LC_ANCHORS during upgrade or download/upload", "RefUrl": "/notes/1064891"}, {"RefNumber": "1035428", "RefComponent": "BC-DB-LCA", "RefTitle": "/SAPAPO/DELETE_LC_ANCHORS may not run during upgrade", "RefUrl": "/notes/1035428"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "809504", "RefComponent": "BC-DB-LCA", "RefTitle": "Implementing notes for section C", "RefUrl": "/notes/809504 "}, {"RefNumber": "1074340", "RefComponent": "BC-DB-LCA", "RefTitle": "Upgrade to SCM 4.1 only with DP data", "RefUrl": "/notes/1074340 "}, {"RefNumber": "1064891", "RefComponent": "BC-DB-LCA", "RefTitle": "/SAPAPO/DELETE_LC_ANCHORS during upgrade or download/upload", "RefUrl": "/notes/1064891 "}, {"RefNumber": "1035428", "RefComponent": "BC-DB-LCA", "RefTitle": "/SAPAPO/DELETE_LC_ANCHORS may not run during upgrade", "RefUrl": "/notes/1035428 "}, {"RefNumber": "847492", "RefComponent": "SCM-APO-MD-RE", "RefTitle": "COMPUTE_BCD_OVERFLOW for /SAPAPO/OM_LC_UPGRADE_41", "RefUrl": "/notes/847492 "}, {"RefNumber": "918238", "RefComponent": "BC-DB-LCA", "RefTitle": "Check on invalid secondary resources", "RefUrl": "/notes/918238 "}, {"RefNumber": "977717", "RefComponent": "BC-DB-LCA", "RefTitle": "Upgrading a system with DP data only", "RefUrl": "/notes/977717 "}, {"RefNumber": "728288", "RefComponent": "BC-DB-LCA", "RefTitle": "Integrated consistency check for suborders", "RefUrl": "/notes/728288 "}, {"RefNumber": "974572", "RefComponent": "BC-DB-LCA", "RefTitle": "Missing ORDKEY entries during upgrade", "RefUrl": "/notes/974572 "}, {"RefNumber": "961872", "RefComponent": "BC-DB-LCA", "RefTitle": "Upgrade analysis program: Recreation for tracing", "RefUrl": "/notes/961872 "}, {"RefNumber": "953030", "RefComponent": "BC-DB-LCA", "RefTitle": "Upgrade to 4.1: fixed pegging on negative stocks", "RefUrl": "/notes/953030 "}, {"RefNumber": "948083", "RefComponent": "BC-DB-LCA", "RefTitle": "Upgrade SCM: External constraints not complete", "RefUrl": "/notes/948083 "}, {"RefNumber": "916054", "RefComponent": "SCM-APO-MD-RE", "RefTitle": "OM17: GETWA_NOT_ASSIGNED, /SAPAPO/SAPLMC01_R05", "RefUrl": "/notes/916054 "}, {"RefNumber": "919315", "RefComponent": "BC-DB-LCA", "RefTitle": "Upgrade: Duplicate constraints", "RefUrl": "/notes/919315 "}, {"RefNumber": "931779", "RefComponent": "BC-DB-LCA", "RefTitle": "Upgrade: Error in /SAPAPO/OM_FIX_PEGGING_CHANGE", "RefUrl": "/notes/931779 "}, {"RefNumber": "871254", "RefComponent": "BC-DB-LCA", "RefTitle": "Analysis of delayed requests", "RefUrl": "/notes/871254 "}, {"RefNumber": "918146", "RefComponent": "SCM-APO-PPS", "RefTitle": "Upgrading to 4.1 - /SAPAPO/PEGKEY incorrectly initialized", "RefUrl": "/notes/918146 "}, {"RefNumber": "917248", "RefComponent": "SCM-APO-PPS-PVW", "RefTitle": "Product View : Orders not displayed in the Elements tab", "RefUrl": "/notes/917248 "}, {"RefNumber": "915365", "RefComponent": "SCM-APO-PPS", "RefTitle": "liveCache problems after converting to SCM 4.1", "RefUrl": "/notes/915365 "}, {"RefNumber": "909411", "RefComponent": "BC-DB-LCA", "RefTitle": "Traffic light for Customizng clients with active time series", "RefUrl": "/notes/909411 "}, {"RefNumber": "765108", "RefComponent": "BC-DB-LCA", "RefTitle": "Upgrade 3.x-->4.x: Compare with sched. agr.schedule lines", "RefUrl": "/notes/765108 "}, {"RefNumber": "883807", "RefComponent": "BC-DB-LCA", "RefTitle": "Link to \"Analysis of delayed requests\"", "RefUrl": "/notes/883807 "}, {"RefNumber": "883497", "RefComponent": "SCM-APO-PPS", "RefTitle": "The creation of planning objects in the liveCache fails", "RefUrl": "/notes/883497 "}, {"RefNumber": "878167", "RefComponent": "BC-DB-LCA", "RefTitle": "Many requests missing when upgrading from Release 3.x", "RefUrl": "/notes/878167 "}, {"RefNumber": "854162", "RefComponent": "BC-DB-LCA", "RefTitle": "Comparison after upgrade from 30 to 41 is too strict", "RefUrl": "/notes/854162 "}, {"RefNumber": "848569", "RefComponent": "BC-DB-LCA", "RefTitle": "Upgrade: Return code 315 when uploading liveCache data", "RefUrl": "/notes/848569 "}, {"RefNumber": "846953", "RefComponent": "SCM-APO-PPS-RPM", "RefTitle": "RPM: if RPM is not active, it must not be error message", "RefUrl": "/notes/846953 "}, {"RefNumber": "808500", "RefComponent": "BC-DB-LCA", "RefTitle": "/sapapo/upgrade_lc_anchors does not work", "RefUrl": "/notes/808500 "}, {"RefNumber": "796317", "RefComponent": "BC-DB-LCA", "RefTitle": "Extended check in /sapapo/om_check_activities", "RefUrl": "/notes/796317 "}, {"RefNumber": "750727", "RefComponent": "BC-DB-LCA", "RefTitle": "Extended check in /sapapo/om_check_activities", "RefUrl": "/notes/750727 "}, {"RefNumber": "779349", "RefComponent": "BC-DB-LCA", "RefTitle": "Short dump during upgrade of liveCache data", "RefUrl": "/notes/779349 "}, {"RefNumber": "748039", "RefComponent": "BC-DB-LCA", "RefTitle": "/sapapo/delete_lc_anchors in the upgrade", "RefUrl": "/notes/748039 "}, {"RefNumber": "770142", "RefComponent": "BC-DB-LCA", "RefTitle": "Upgrade from 3.x to 4.x: Comparison", "RefUrl": "/notes/770142 "}, {"RefNumber": "767501", "RefComponent": "BC-DB-LCA", "RefTitle": "Upgrade 3.x-->4.1: Improved log output when an error occurs", "RefUrl": "/notes/767501 "}, {"RefNumber": "757196", "RefComponent": "BC-DB-LCA", "RefTitle": "Correction: Extended check in /sapapo/om_check_activities", "RefUrl": "/notes/757196 "}, {"RefNumber": "747060", "RefComponent": "BC-DB-LCA", "RefTitle": "Enhanced check in /sapapo/om_check_activities", "RefUrl": "/notes/747060 "}, {"RefNumber": "744043", "RefComponent": "BC-DB-LCA", "RefTitle": "Enhanced check in /sapapo/om_check_activities", "RefUrl": "/notes/744043 "}, {"RefNumber": "743834", "RefComponent": "BC-DB-LCA", "RefTitle": "Upgrade - /sapapo/om 17", "RefUrl": "/notes/743834 "}, {"RefNumber": "731099", "RefComponent": "BC-DB-LCA", "RefTitle": "Upgrade from 3.x to 4.x: Comparison", "RefUrl": "/notes/731099 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SCM", "From": "410", "To": "410", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}