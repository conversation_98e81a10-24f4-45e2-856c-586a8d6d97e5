{"Request": {"Number": "1327345", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 933, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016769672017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001327345?language=E&token=663B92FD5FEB319B331F16EC1960E80A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001327345", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001327345/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1327345"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.09.2016"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-ET-WJR"}, "SAPComponentKeyText": {"_label": "Component", "value": "BEx Web Java Runtime"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Explorer", "value": "BW-BEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Enduser Technology", "value": "BW-BEX-ET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-ET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BEx Web Java Runtime", "value": "BW-BEX-ET-WJR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-ET-WJR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1327345 - Patch<PERSON> for NetWeaver 7.01  BI Java Support Package"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are facing a problem in SAP EHP1 for NetWeaver 7.0 (7.01) BI Java Runtime. SAP has provided a solution for this problem that is available in a Patch for Service Pack.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Patch<PERSON>, EHP1for NetWeaver 7.0 (7.01), BI Java Runtime, SCA, BIBASES, BIWEBAPP.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Code correction.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>SAP NetWeaver BI Java Runtime consist of two SCA files:<br />BIBASES.SCA (BI Web Runtime: BI Base Services)<br />BIWEBAPP.SCA (BI Web Runtime: BI Web Application)<br /><br />For the full functionality <strong>both SCA files</strong> must be deployed on the system.<br /><br /><strong>BI Web Runtime SCA files must be on the same SP level and on the same Patch level.</strong><br /><br />In case of error correction a SAP Note will be created with two correction instructions:</p>\r\n<ul>\r\n<li><strong>First,</strong> the information of the Service Pack (SP) with the official correction will be provided. As soon the SP is released, it can be deployed on the system.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>Second,</strong>the Patches for already delivered Service Packs will be listed like following example:</li>\r\n</ul>\r\n<p><br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<strong>SP [YY]: Patch #[XX] or higher</strong><br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;http://service.sap.com/patches<br />Support Packages and Patches<br />BI BASE SERVICES 7.01<br />OSINDEP<br /><strong>BIBASES[YY]P patch #[XX]</strong><br />BI WEB APPLICATIONS 7.01<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;OSINDEP<br /><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;BIWEBAPP[YY]P patch #[XX]</strong><br /><br /><strong>[YY]</strong> - is the Service Pack level for which the correction is applicable.<br /><strong>[XX]</strong> - is the minimum Patch level that must be applied for the correction.<br /><br />In most cases only BIBASES &amp; BIWEBAPP SCA files are involved in the correction and must be re-deployed.</p>\r\n<p><strong>How to find a Patch for specific Service Pack.</strong></p>\r\n<ol>1. Go to the Service Market Place: http://service.sap.com/patches.</ol><ol>2. In the left pane click on 'Entry by Application Group'.</ol><ol>3. Click on 'SAP NetWeaver' in the list on the right side.</ol><ol>4. Click again on 'SAP NETWEAVER' in the list.</ol><ol>5. Click on 'SAP EHP1 FOR SAP NETWEAVER 7.0' in the list.</ol><ol>6. Click on 'Entry by Component' in the list.</ol><ol>7. Click on 'BI Java' in the expanded node.</ol><ol>8. You will find a list of the relevant SCAs (Software Component Archives). Search for:</ol>\r\n<ul>\r\n<ul>\r\n<li>BI BASE SERVICES 7.01 (BIBASES.SCA)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>BI WEB APPLICATIONS 7.01 (BIWEBAPP.SCA)</li>\r\n</ul>\r\n</ul>\r\n<ol>9. You have to download the SCA files from each leaf as follows:</ol><ol><ol>a) Click on the leaf.</ol></ol><ol><ol>b) Click on '#OS independent'</ol></ol><ol><ol>c) Scroll down and look at the list. There are two types of files in the list:</ol></ol>\r\n<ul>\r\n<ul>\r\n<li>SP[YY] for BI ... 7.01 (the full SPs)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Patch for BI ... 7.01 SP[YY] (the patches)</li>\r\n</ul>\r\n</ul>\r\n<ol><ol>d) Find a Patch for your SP level and download the file.</ol></ol>\r\n<ul>\r\n<li>Important: If recommended Patch level is not available for download it indicates that the Patch has been not yet released.</li>\r\n</ul>\r\n<ol>10. Apply (deploy) the SCA files to your system and restart the system.</ol>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><strong>SAP NW 7.01 Support Package Stack Schedule</strong></p>\r\n</td>\r\n<td>\r\n<p><strong>BI BASE EXPORT SERVICES </strong></p>\r\n</td>\r\n<td>\r\n<p><strong>BI WEB APPLICATIONS</strong></p>\r\n</td>\r\n<td>\r\n<p><strong>Notes </strong></p>\r\n</td>\r\n<td>\r\n<p><strong>Release</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>SPS17</strong></p>\r\n</td>\r\n<td><strong><strong>Patch 30&#160;</strong></strong>BIBASES17_30-10005888.SCA<strong>&#160; </strong></td>\r\n<td><strong><strong>Patch 30&#160;</strong></strong>BIWEBAPP17_30-20002783.SCA</td>\r\n<td>\r\n<p><strong>2156444</strong></p>\r\n</td>\r\n<td>CW44 2016</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>SPS17</strong></p>\r\n</td>\r\n<td><strong>Patch 20</strong> BIBASES17_20-10005888.SCA</td>\r\n<td><strong>Patch 20&#160; </strong><span style=\"font-size: 12pt; font-family: 'Arial',sans-serif; color: black; line-height: 115%; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">BIWEBAPP17_20-20002783.SCA</span></td>\r\n<td>\r\n<p><strong>2116457</strong></p>\r\n</td>\r\n<td>CW23 2015</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>SPS17</strong></p>\r\n</td>\r\n<td><strong>Patch 10</strong>&#160;BIBASES17_10-10005888.SCA</td>\r\n<td><strong><strong>Patch 10&#160;</strong></strong>BIWEBAPP17_10-20002783.SCA</td>\r\n<td>\r\n<p><strong>2061891</strong></p>\r\n</td>\r\n<td>CW16 2015</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>SPS17</strong></p>\r\n</td>\r\n<td><strong>SP17</strong>&#160;&#160;BIBASES17_0-10005888.SCA</td>\r\n<td><strong><strong>SP17&#160;&#160;</strong></strong>BIWEBAPP17_0-20002783.SCA</td>\r\n<td>\r\n<p><strong>2110848</strong></p>\r\n</td>\r\n<td>CW12 2015</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>SPS18</strong></p>\r\n</td>\r\n<td>\r\n<p><strong>Patch 10&#160;</strong><span style=\"font-size: 12pt; font-family: 'Arial',sans-serif; color: black; line-height: 115%; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">BIBASES18_10-10005888.SCA</span></p>\r\n</td>\r\n<td>\r\n<p><strong>Patch 10</strong> <span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; color: black; line-height: 107%; mso-ascii-theme-font: minor-latin; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-hansi-theme-font: minor-latin; mso-bidi-font-family: Arial; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">BIWEBAPP33_10-10003476.SCA</span></p>\r\n</td>\r\n<td>\r\n<p><strong>2156444</strong></p>\r\n</td>\r\n<td>\r\n<p>CW15 2016</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>SPS18</strong></p>\r\n</td>\r\n<td>\r\n<p><strong>SP18</strong>&#160;BIBASES18_0-10005888.SCA</p>\r\n</td>\r\n<td>\r\n<p><strong>SP18 </strong>BIWEBAPP18_0-20002783.SCA</p>\r\n</td>\r\n<td>\r\n<p><strong>2110809</strong></p>\r\n</td>\r\n<td>\r\n<p>CW13 2016</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "Christof LITZNERSKI (D034845)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I310996)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001327345/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001327345/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001327345/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001327345/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001327345/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001327345/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001327345/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001327345/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001327345/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "HOW_TO_DOWNLOAD_BI_PATCH.zip", "FileSize": "820", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000137722009&iv_version=0003&iv_guid=5F4E4C062D9F5D44BD4EDC8B4E164DA0"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1645590", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Java/Server SPs dependencies (and SupportDeskTool)", "RefUrl": "/notes/1645590"}, {"RefNumber": "1506722", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Generic Note for BI Java Patches and Support Packages", "RefUrl": "/notes/1506722"}, {"RefNumber": "1479100", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 3 for BI JAVA 7.01 SP06 Patch 30 (PL33)", "RefUrl": "/notes/1479100"}, {"RefNumber": "1477643", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 2 for BI JAVA 7.01 SP06 Patch 30 (PL32)", "RefUrl": "/notes/1477643"}, {"RefNumber": "1476076", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 1 for BI JAVA 7.01 SP06 Patch 30 (PL31)", "RefUrl": "/notes/1476076"}, {"RefNumber": "1464785", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 1 for BI JAVA 7.01 SP06 Patch 20 (PL21)", "RefUrl": "/notes/1464785"}, {"RefNumber": "1445900", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 1 for BI JAVA 7.01 SP06 Patch 10 (PL11)", "RefUrl": "/notes/1445900"}, {"RefNumber": "1421102", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 2 for BI JAVA 7.01 SP05 Patch 20 (PL22)", "RefUrl": "/notes/1421102"}, {"RefNumber": "1418496", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 1 for BI JAVA 7.01 SP05 Patch 20 (PL21)", "RefUrl": "/notes/1418496"}, {"RefNumber": "1409971", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 6 for BI JAVA 7.01 SP05 Patch 10 (PL16)", "RefUrl": "/notes/1409971"}, {"RefNumber": "1391286", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BI JAVA corrections for SAP EHP1 for NetWeaver 7.0 (7.01)", "RefUrl": "/notes/1391286"}, {"RefNumber": "1390539", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 2 for BI JAVA 7.01 SP05 Patch 10 (PL12)", "RefUrl": "/notes/1390539"}, {"RefNumber": "1390538", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "TEMPL Hotfix [Z] for BI JAVA 7.01 SP[XX] Patch [YY] (PL[YZ])", "RefUrl": "/notes/1390538"}, {"RefNumber": "1342675", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Released- NW 7.01 SPS 03 Patch 6 note for BI Java", "RefUrl": "/notes/1342675"}, {"RefNumber": "1335378", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NW 7.01 SPS 03 Patch 4 note for BI Java", "RefUrl": "/notes/1335378"}, {"RefNumber": "1309000", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NW 7.01 and NW 7.02: BI Java Patch Delivery", "RefUrl": "/notes/1309000"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2156444", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NW 7.01 SP17 Patch 30 and SPS18 Patch 10 and NW 7.02 SP17 Patch 30 and SPS18 Patch10 for BI Java", "RefUrl": "/notes/2156444 "}, {"RefNumber": "2116457", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NW 7.01 SP17 Patch Level (PL) 20 and NW 7.02 SP17 Patch Level (PL) 20 for BI Java", "RefUrl": "/notes/2116457 "}, {"RefNumber": "1309000", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NW 7.01 and NW 7.02: BI Java Patch Delivery", "RefUrl": "/notes/1309000 "}, {"RefNumber": "1645590", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Java/Server SPs dependencies (and SupportDeskTool)", "RefUrl": "/notes/1645590 "}, {"RefNumber": "1506722", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Generic Note for BI Java Patches and Support Packages", "RefUrl": "/notes/1506722 "}, {"RefNumber": "1390538", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "TEMPL Hotfix [Z] for BI JAVA 7.01 SP[XX] Patch [YY] (PL[YZ])", "RefUrl": "/notes/1390538 "}, {"RefNumber": "1287013", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "MIME sapbi_ac_tray_comp.js does not exist.", "RefUrl": "/notes/1287013 "}, {"RefNumber": "1479100", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 3 for BI JAVA 7.01 SP06 Patch 30 (PL33)", "RefUrl": "/notes/1479100 "}, {"RefNumber": "1477643", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 2 for BI JAVA 7.01 SP06 Patch 30 (PL32)", "RefUrl": "/notes/1477643 "}, {"RefNumber": "1476076", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 1 for BI JAVA 7.01 SP06 Patch 30 (PL31)", "RefUrl": "/notes/1476076 "}, {"RefNumber": "1464785", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 1 for BI JAVA 7.01 SP06 Patch 20 (PL21)", "RefUrl": "/notes/1464785 "}, {"RefNumber": "1445900", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 1 for BI JAVA 7.01 SP06 Patch 10 (PL11)", "RefUrl": "/notes/1445900 "}, {"RefNumber": "1421102", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 2 for BI JAVA 7.01 SP05 Patch 20 (PL22)", "RefUrl": "/notes/1421102 "}, {"RefNumber": "1418496", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 1 for BI JAVA 7.01 SP05 Patch 20 (PL21)", "RefUrl": "/notes/1418496 "}, {"RefNumber": "1409971", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 6 for BI JAVA 7.01 SP05 Patch 10 (PL16)", "RefUrl": "/notes/1409971 "}, {"RefNumber": "1390539", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 2 for BI JAVA 7.01 SP05 Patch 10 (PL12)", "RefUrl": "/notes/1390539 "}, {"RefNumber": "1391286", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BI JAVA corrections for SAP EHP1 for NetWeaver 7.0 (7.01)", "RefUrl": "/notes/1391286 "}, {"RefNumber": "1361005", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NEW TEMPLATE: SAP NW BI JAVA 7.01 / 7.11", "RefUrl": "/notes/1361005 "}, {"RefNumber": "1355804", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NW 7.01 SPS 04 Patch 3 note for BI Java", "RefUrl": "/notes/1355804 "}, {"RefNumber": "1352411", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NW 7.01 SPS 04 Patch 2 note for BI Java", "RefUrl": "/notes/1352411 "}, {"RefNumber": "1330334", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NW 7.01 SPS 04 Patch 1 note for BI Java", "RefUrl": "/notes/1330334 "}, {"RefNumber": "1342675", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Released- NW 7.01 SPS 03 Patch 6 note for BI Java", "RefUrl": "/notes/1342675 "}, {"RefNumber": "1335378", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NW 7.01 SPS 03 Patch 4 note for BI Java", "RefUrl": "/notes/1335378 "}, {"RefNumber": "1330333", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Released - NW 7.01 SPS 03 Patch 3 note for BI Java", "RefUrl": "/notes/1330333 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "701", "To": "701", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}