{"Request": {"Number": "553110", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 298, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015285372017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000553110?language=E&token=D4008C89D2F9C94F27BBA8B48CA41E17"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000553110", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000553110/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "553110"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.11.2003"}, "SAPComponentKey": {"_label": "Component", "value": "BC-I18"}, "SAPComponentKeyText": {"_label": "Component", "value": "Internationalization (I18N)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Internationalization (I18N)", "value": "BC-I18", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-I18*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "553110 - User Exits:  Behavior within non-Unicode R/3 Enterprise"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>During conversion to non-Unicode R/3 Enterprise, customers may experience Unicode-errors in their User Exit coding.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Unicode R/3 Enterprise User Exit</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>During conversion to non-Unicode R/3 Enterprise, customers may experience Unicode-errors in their User Exit coding because there are still many SMOD/CMOD user exits within SAP R/3 Enterprise. SMOD/CMOD - type user exits are permitting that ABAP files written by the customer are included directly into function groups and other SAP coding. Since nearly the complete SAP coding of SAP R/3 Enterprise is Unicode-enabled (except the known exceptions, see SAP Note 540911), the attribute &#x0093;Extended (Unicode) syntax checks active&#x0094; also applies to customer coding and therefore forces extended syntax- and semantics-checks.<br />Although Unicode-enabling of the complete customer user exit coding would solve this problem, the solution proposed below makes sure that Unicode-related effort is minimized on the customer side.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><UL><LI>All existing SAP user exits (6000) have been classified into critical and non-critical user exits and less than 10 % are flagged as \"critical\".</LI></UL> <UL><LI>A tool (report \"Z_RSUNUXS2\", see attachment) scans for critical User Exit coding, taking into account which of the involved functions and function groups contain no or only very simple SAP coding.</LI></UL><UL><LI>This report is also taking APO / SCM into account, now !</LI></UL> <UL><LI>If the customer's code is Unicode compatible, nothing has to be done.</LI></UL> <UL><LI>If there are functions groups with customer coding that is not Unicode compatible and at the same time containing no or only uncritical SAP coding, the Unicode flag of these function groups is switched off automatically.</LI></UL> <UL><LI>Only if the customer coding is not Unicode compatible and the SAP coding in the same function group is substantial, too, the customer needs to Unicode-enable his coding with support from SAP's UCCHECK tool. The \"Unicode Enabling Cookbook\" (see attachment) explains all necessary steps.<br /></LI></UL> <b>How do I have to proceed with Z_RSUNUXS2:<br /></b><br /> <UL><LI>Please upload report Z_RSUNUXS2 and the related include file Z_RSUNUXI1 to your development system and execute it with the following options:<br /></LI></UL> <UL><LI>The most important parameter is the first one (SWITCHIM, \"Change Unicode-flag immediately\"). For the first run, you should leave it off, so that the database will be scanned and a report is going to be displayed.</LI></UL> <UL><LI>You may alternatively use interactive reporting to either switch the Unicode-flag manually or to call the transaction UCCHECK for the listed objects.</LI></UL> <UL><LI>You may also restrict the scanner to specific functions, if you know their names.</LI></UL> <UL><LI>The report has an additional parameter to select the level of detail. Value of ' 0 ' creates the shortest report possible. Larger values will add additional information to the report.</LI></UL> <UL><LI>The output of Z_RSUNUXS2 generally consists of a list of function groups considered of being critical. All function groups are classified into up to six different kinds with a short description for each category. Finally, there are push-buttons to modify the Unicode flag or directly start UCCHECK.</LI></UL> <UL><LI>Each part of the list will only show up if needed, in other words: Ideally, the list contains only the sentence \"There are no problems\".</LI></UL> <UL><LI>Please be aware that the changes made by report Z_RSUNUXS2 will not be part of a CTS transport request. As a consequence, make sure to run the report in each of the systems affected, i.e. quality assurance, production, etc.</LI></UL> <UL><LI>Please contact SAP finally to discuss the results and to obtain additional support.<br /></LI></UL> <UL><LI>Questions? <NAME_EMAIL></LI></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D027999)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D028792)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000553110/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000553110/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000553110/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000553110/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000553110/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000553110/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000553110/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000553110/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000553110/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Z_RSUNUXI1.txt", "FileSize": "321", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000177082002&iv_version=0008&iv_guid=8B7B1EEA91A55547B656AD2DA7674384"}, {"FileName": "Z_RSUNUXS2.txt", "FileSize": "34", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000177082002&iv_version=0008&iv_guid=A3E7F5A567AF3D49BC1AA358F85D0F16"}, {"FileName": "UNICODE_ENABLING_COOKBOOK.PDF", "FileSize": "228", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000177082002&iv_version=0008&iv_guid=9FA5DDBFFA6B024E9C314EA4C4AF393E"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "975861", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 6.0 ABAP", "RefUrl": "/notes/975861"}, {"RefNumber": "961512", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/961512"}, {"RefNumber": "961511", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/961511"}, {"RefNumber": "947991", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/947991"}, {"RefNumber": "925240", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/925240"}, {"RefNumber": "913849", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/913849"}, {"RefNumber": "913848", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/913848"}, {"RefNumber": "826488", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/826488"}, {"RefNumber": "826487", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/826487"}, {"RefNumber": "826093", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/826093"}, {"RefNumber": "737800", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to upgrade on SAP CRM 4.0 SR1", "RefUrl": "/notes/737800"}, {"RefNumber": "737696", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/737696"}, {"RefNumber": "637683", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/637683"}, {"RefNumber": "628205", "RefComponent": "BC-I18", "RefTitle": "User Includes:Behavior within non-Unicode SAP R/3 Enterprise", "RefUrl": "/notes/628205"}, {"RefNumber": "620301", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/620301"}, {"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852"}, {"RefNumber": "587896", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/587896"}, {"RefNumber": "540911", "RefComponent": "BC-I18", "RefTitle": "Unicode restrictions for R/3 Enterprise, ECC 5.0, ECC 6.0", "RefUrl": "/notes/540911"}, {"RefNumber": "513536", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/513536"}, {"RefNumber": "493387", "RefComponent": "BC-ABA-LA", "RefTitle": "Potential effects of table- and structure - extensions", "RefUrl": "/notes/493387"}, {"RefNumber": "1319517", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Collection Note", "RefUrl": "/notes/1319517"}, {"RefNumber": "1108861", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1108861"}, {"RefNumber": "1108700", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1108700"}, {"RefNumber": "1071404", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1071404"}, {"RefNumber": "1039395", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1039395"}, {"RefNumber": "1010762", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1010762"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1319517", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Collection Note", "RefUrl": "/notes/1319517 "}, {"RefNumber": "540911", "RefComponent": "BC-I18", "RefTitle": "Unicode restrictions for R/3 Enterprise, ECC 5.0, ECC 6.0", "RefUrl": "/notes/540911 "}, {"RefNumber": "493387", "RefComponent": "BC-ABA-LA", "RefTitle": "Potential effects of table- and structure - extensions", "RefUrl": "/notes/493387 "}, {"RefNumber": "975861", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 6.0 ABAP", "RefUrl": "/notes/975861 "}, {"RefNumber": "737800", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to upgrade on SAP CRM 4.0 SR1", "RefUrl": "/notes/737800 "}, {"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852 "}, {"RefNumber": "628205", "RefComponent": "BC-I18", "RefTitle": "User Includes:Behavior within non-Unicode SAP R/3 Enterprise", "RefUrl": "/notes/628205 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}