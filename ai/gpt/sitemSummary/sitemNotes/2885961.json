{"Request": {"Number": "2885961", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 297, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001635392020"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002885961?language=E&token=1AF04727A4B86EEB4EA0B09124CB7E17"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002885961", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002885961/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2885961"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 27}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.09.2023"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-REL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Suite and SAP S/4HANA Release Information"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Suite and SAP S/4HANA Release Information", "value": "XX-SER-REL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-REL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2885961 - Restriction Note for advanced Available-to-Promise (aATP) in SAP S/4HANA 2020"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are using advanced Availabile-to-Promise (aATP) in SAP S/4HANA&#9702;2020. This note details restrictions in aATP with SAP S/4HANA 2020.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>ABC, BOP, PAL, RFDY, ATP, advanced ATP, aATP, availability check, Transportation Management, TM</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The advanced Available-to-Promise functions have been activated in the corresponding checking group for the relevant material-plant combination.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Abbreviations used in this note:</p>\r\n<p>ABC = Alternative-Based Confirmation (automatic determination of confirmation alternatives from a different shipping plant)<br />BOP = Backorder Processing (mass availability check with sophisticated prioritization capabilities)<br />PAC = Product Availability Check (standard availability check algorithm for a material-plant combination based on stock, concurrent supply and demand elements defined by the scope of check)<br />PAL = Product Allocation (availability check algorithm based on virtual supply which is available for a given&#9702;characteristic value combination and time period)<br />SUP = Supply Protection (optimized availability of products for specific requirements by a new available-to-promise check method)</p>\r\n<p>With SAP S/4HANA 2020, the following restrictions apply:</p>\r\n<ul>\r\n<li><strong>General restrictions for Available-to-Promise in SAP S/4HANA:</strong></li>\r\n<ul>\r\n<li>Check against forecast&#9702;</li>\r\n</ul>\r\n</ul>\r\n<p><span style=\"font-size: 14px;\">&#9702;</span></p>\r\n<ul>\r\n<li><strong>General restrictions in the advanced ATP check:</strong></li>\r\n</ul>\r\n<ol><ol>\r\n<li><span style=\"font-size: 14px;\">Alternative-Based Confirmations<br /></span>If used, ABC would consider all plants in the sales organization for the requested material. Currently, there is no means to restrict the set of plants to be used. Please consider system performance when there are several plants in the sales organization.</li>\r\n<li>Product selection</li>\r\n<li>Sales scheduling agreements with call-offs</li>\r\n<li>Delivery blocks with shifting confirmation to the future</li>\r\n<li>Weight-dependent transportation and shipment scheduling</li>\r\n<li>PSP grouping (table GRPGA)&#160;and cross-project material (field MARC-KZPSP on MRP3-View)&#160;</li>\r\n<li>SAP Transportation Management based scheduling</li>\r\n<li>Transactions V_V2, V_RA&#9702;and CO06 are not supported anymore</li>\r\n<li>Sales Order processing out of Service Order or Service Notification (Transactions IW31, IW32&#9702;/ IW51, IW52 ) or where ever Sales Order processing is called by 'Call Dialog'.</li>\r\n<li>Enhancement ATP00001</li>\r\n<li>Supply assignment with Quantity distribution is not supported</li>\r\n<li>Supersessions are not supported in aATP</li>\r\n</ol></ol>\r\n<p><span style=\"font-size: 14px;\">&#9702;</span></p>\r\n<ul>\r\n<li><strong>Backorder Processing (BOP)</strong></li>\r\n</ul>\r\n<ol><ol>\r\n<li><em>Restriction</em><span style=\"font-size: 14px;\"><em>:</em>&#9702;</span><strong style=\"font-size: 14px;\">Requested schedule line displayed incorrectly in the Monitor BOP Run app<br /></strong><em>Prerequisite:</em>&#9702;The respective sales order line item is&#9702;categorized for confirmation strategy \"Fill\" and&#9702;contains a&#9702;delivery proposal with multiple confirmed schedule lines.<br /><em>Mitigation:</em> Display the item details to see all confirmed schedule lines and ignore the incorrect aggregation line.</li>\r\n<li><em>Restriction:</em>&#9702;<strong>Short dump during&#9702;BOP run: TSV_TNEW_PAGE_ALLOC_FAILED or TSV_TNEW_OCCURS_NO_ROLL_MEMORY<br /></strong>Prerequisite:&#9702;The&#9702;selected number of line items is very large (&gt; 250k line items). <br /><em>Mitigation:</em>&#9702;Use global filters to reduce the number of selected line items or increase the memory for your batch work processes (TA ST02 or RZ10, parameter abap/heap_area_nondia). When changing the system parameters, please be aware that the number of work processes and the memory per work process need to be balanced out and suit the available hardware.<br /><em>Explanation:</em> The volume of data processed in a BOP run is proportional to the number of requirements selected for processing. It must be ensured that the ABAP workprocesses have sufficient memory available to process these requirements. As a rule of thumb, the memory consumption of a BOP run can be calculated based on the following figures: a straightforward BOP with Product Availability Check only, needs approximately 10-15kB per requested schedule line. If Supply Protection and/or Product Allocation is used, the memory consumption rises to 15-25kB or more. If Alternative-Based Confirmation is activated, each evaluated alternative needs the same memory as a requested schedule line. As ATP is highly configurable and there are many influencing factors, these numbers might vary depending on the overall data volume and the complexity of the business process.</li>\r\n<li><em>Restriction:</em>&#9702;<strong>Sub-optimal availability check results for line items where PAC and PAL&#9702;are active</strong>.<br /><em>Prerequisite:</em> The&#9702;characteristic combination for the PAL check is not using&#9702;material&#9702;and plant as key attributes. In addition, BOP is checking several line items for different material-plant combinations which check against the same PAL time series. Furthermore, one of the material-plant combinations is locked, resulting in the&#9702;PAC check being unable to create a confirmation.<br /><em>Mitigation:</em> Do not use PAL&#9702;with key combinations which do not comprise MATNR&#9702;and WERKS. Furthermore, avoid parallel availability checks for material-plant combinations which are being processed in a BOP run.<br /><em>Explanation:</em> PAC and PAL use differing lock strategies.</li>\r\n<li><em>Restriction:</em>&#9702;<strong>Unexpected scheduling&#9702;results<br /></strong><em>Prerequisite:</em> The affected material has weight groups which are relevant for transportation scheduling.<br /><em>Mitigation:</em> Do not use weight groups.<br /><em>Explanation:</em> Weight-dependent scheduling is not supported in BOP.</li>\r\n<li><em>Restriction:</em>&#9702;<strong>Stock transfer&#9702;requisitions&#9702;cannot be&#9702;handled&#9702;<br /></strong><em>Prerequisite:</em> Stock transfer&#9702;requisitions compete for the same stocks (or planned receipts) as sales orders.<br /><em>Mitigation:</em>&#9702;Use stock transport orders as BOP-relevant requirements instead.<br /><em>Explanation:</em> Stock transfer requisitions are not supported for selection in BOP segments.</li>\r\n<li><em>Restriction:</em>&#9702;<strong>Scheduling agreements with call-offs and non-delivery relevant document types&#9702;such as quotations cannot be&#9702;handled&#9702;<br /></strong><em>Prerequisite:</em> Scheduling agreements with call-offs and non-delivery relevant document types&#9702;such as quotations compete for the same stocks (or planned receipts) as the sales orders.<br /><em>Mitigation:</em> For mass availability checks for non-delivery relevant document types&#9702;such as quotations, use the rescheduling function available in transaction V_V2; therefore do not activate aATP for material-plant combinations which are used for those document types.<br /><em>Explanation:</em> Not implemented (to be addressed in a future SAP S/4HANA release).</li>\r\n<li><em>Restriction:</em>&#9702;<strong>No pre-delivered selection attribute available to differentiate between document types when defining BOP segments<br /></strong><em>Prerequisite:</em> You are using Select Option Tool (SOT) to define selection criteria.<br /><em>Mitigation:</em> Define your own characteristic value groups to represent the document types.<br /><em>Explanation:</em> Field ATPRELEVANTDOCUMENTCATEGY is no longer delivered by SAP</li>\r\n<li><em>Restriction:</em>&#9702;<strong>No simulation capability for BOP segments containing HRF-based filter conditions.<br /></strong><em>Prerequisite:</em> You are using SAP HANA Rules Framework (HRF) to define selection criteria.<br /><em>Mitigation:</em> Migrate from HRF to Select Option Tool (SOT), see <a target=\"_blank\" href=\"/notes/2800374\">2800374-Migration of HRF-based Filter Conditions in aATP</a>.<br /><em>Explanation:</em> Limitation for HRF-based BOP segments exists, see <a target=\"_blank\" href=\"/notes/2885945\">2885945- Limitation: Functional limitation in Configure BOP Segment when using HRF</a>.</li>\r\n<li><em>Restriction:</em> <strong>The BOP fallback run cannot be triggered if more than 2000 material-plant combinations failed in the first run.</strong><br /><em>Prerequisite:</em> You are using the Select Option Tool (SOT) to define selection criteria and you are working with the strategies Win or Gain.<br /><em>Mitigation:</em> Adapt the BOP Variant so that fewer requirements are classified in the confirmation strategies Win or Gain.<br /><em>Explanation:</em> The definition of the database procedure cannot exceed 256k characters.</li>\r\n<li><em>Restriction: </em><strong>Delivery proposal behavior is not supported for Stock transport order (STO) items in BOP run if \"Rules for Adoption of ATP Results in Purchasing\"(T161V-REVFE) is set to \"SPACE = One-Time Delivery (Rescheduling: Delivery Proposal)\"<br /></strong><em><em>Prerequisite:&#160;</em></em>\"Rules for Adoption of ATP Results in Purchasing\" is set to \"SPACE = One-Time Delivery (Rescheduling: Delivery Proposal)\" for the relevant \"Purchasing Document Type\" and \"Plant\" combination under customizing \"Configure Delivery Type &amp; Availability Check Procedure by Plant\"<em><em>.<br /></em></em><em>Mitigation: </em>To enable \"Delivery proposal\" behavior for STO items in BOP run use value \"C =&#160;Delivery Proposal\" or \"E = Dialog Box in Case of Undercoverage (Batch: Deliv. Proposal)\" in \"Rules for Adoption of ATP Results in Purchasing\"&#160;for the relevant \"Purchasing Document Type\" and \"Plant\" combination in customizing \"Configure Delivery Type &amp; Availability Check Procedure by Plant\".&#160;&#160;For more details, see&#160;<a target=\"_blank\" href=\"https://me.sap.com/notes/885177\">885177 - Stock transfer in Purchasing: New schedule line processing</a><br /><em>Explanation: </em>Old schedule line processing for STO items is not supported in BOP.</li>\r\n</ol></ol>\r\n<ul>\r\n<li><strong>Release for Delivery</strong></li>\r\n</ul>\r\n<ol><ol>\r\n<li>Restriction:&#9702;<strong>Incorrect processing items identified for Supply Assignment (ARun)</strong><br />Prerequisite: Items to be processed with Supply Assignment (ARun) are not detected by the Release for Delivery app.<br />Mitigation: Avoid items that are to be processed with Supply Assignment (ARun).<br />Explanation: Not implemented (to be addressed in a future SAP S/4HANA release).</li>\r\n</ol></ol>\r\n<p><span style=\"font-size: 14px;\"><strong>&#9702;</strong></span></p>\r\n<ul>\r\n<li><strong>Product Allocation (PAL)</strong></li>\r\n</ul>\r\n<ol><ol>\r\n<li>Restriction:<strong>&#9702;<strong>A re-check of product allocation for order items with Supply Assignment (ARun) quantities can lead to confirmations being before the PAL consumption for the assigned quantities.<br /></strong></strong>Prerequisite:&#9702;Supply assignments exist for affected orders. Availability is re-checked for the orders, also against PAL and the allocation check is executed with goods issue date or with requested delivery date. SD scheduling uses calendars with non-working days and allocation consumption period lies directly after a non-working day. Backward scheduling of consumption date leads to a material availability date which lies before the non-working days.<br />Mitigation:&#9702;Avoid checking PAL with supply assignments with the above mentioned prerequisites.&#9702;<br />Explanation: Re-checking order items with a re-scheduling of supply assignment dates can lead to dates not respecting PAL planned quantities.&#9702;<br />Recommendation: Use PAL with check date time type material availability date in such scenarios.</li>\r\n<li>Restriction:&#9702;<strong>Sub-optimal quantity consumption of capacity product allocation check for sales BOM items<br /></strong>Prerequisite:&#9702;A requirement-relevant subitem of a sales BOM (LUMF)&#9702;is checked within the capacity product allocation check.<br />Explanation:&#9702;If sales BOM items are checked for product allocation, the check reserves (consumes) allocation quantity for each item. After all checks have been performed, the delivery group correlation for all sales BOM items is performed to determine a common delivery date for all items. Afterwards, for the allocation reservations only the confirmation dates are adapted, but not the consumption periods, in which allocation quantity is reserved. This can lead to the fact that quantities are consumed in earlier time periods although the confirmation and hence the planned delivery of the items is later. This is a sub-optimal result in case of capacity-based product allocations, as the allocation quantities represent production or transport capacities, which should optimally be consumed at the confirmation time point and not earlier.<br />Mitigation: Avoid capacity-based product allocation checks for sales BOM items.</li>\r\n</ol></ol>\r\n<p><span style=\"font-size: 14px;\"><strong>&#9702;</strong></span></p>\r\n<ul>\r\n<li><strong>Supply Protection (SUP)</strong></li>\r\n</ul>\r\n<ol><ol>\r\n<li><span style=\"font-size: 14px;\">Restriction: <strong>Creation of protection groups for dedicated Business Partners is not possible</strong></span><strong style=\"font-size: 14px;\"><br /></strong>Prerequisite:&#9702;The catalog for SUP must contain Business Partner.<br />Mitigation: Implement OP2020 FPS1.</li>\r\n<li><span style=\"font-size: 14px;\">Restriction:&#9702;</span><strong style=\"font-size: 14px;\">Creation of supply protection objects with generic articles is not possible<br /></strong>Prerequisite: Create supply protection object with generic article.<br />Mitigation: Create supply protection objects only for article variants.</li>\r\n<li><span style=\"font-size: 14px;\">Restriction:&#9702;</span><strong style=\"font-size: 14px;\">Not all Object Types are considered from the Supply Protection logic<br /></strong>Prerequisite: Several document types (for example, delivery documents) are not restricted by Supply Protection.&#9702;<br />Mitigation: Check if stock can be reduced without being restricted by Supply Protection.</li>\r\n<li><span style=\"font-size: 14px;\">Restriction:&#9702;</span><strong style=\"font-size: 14px;\">FIORI UI for Supply Protection does not fulfill the usability standards<br /></strong>Prerequisite: Create and maintain protection groups.<br />Mitigation:&#9702;Implement OP2020 FPS1.</li>\r\n<li><span style=\"font-size: 14px;\">Restriction:&#9702;</span><strong style=\"font-size: 14px;\">No alerting to prevent over-protection<br /></strong>Prerequisite: Create supply protection objects with high protected quantities.<br />Mitigation: Check the current stock in the system and compare this with the protected stock in Supply Protection.</li>\r\n<li><span style=\"font-size: 14px;\">Restriction:&#9702;</span><strong style=\"font-size: 14px;\">No analytics showing protection and consumed protection<br /></strong>Prerequisite: Create supply protection objects.<br />Mitigation: Check the current stock in the system and compare this with the protected stock in Supply Protection.</li>\r\n<li><span style=\"font-size: 14px;\">Restriction:&#9702;</span><strong style=\"font-size: 14px;\">Supply Protection not included in Explanation Component<br /></strong>Prerequisite: Create, for example, a sales order and check the item availability or availability overview.<br />Mitigation: Check the current stock in the system and compare this with the protected stock in Supply Protection.</li>\r\n<li><span style=\"font-size: 14px;\">Restriction:&#9702;</span><strong style=\"font-size: 14px;\">Supply Protection Objects cannot be imported via Excel<br /></strong>Prerequisite: Open Supply Protection Objects overview.<br />Mitigation: Use the APIs to import or update supply protection objects.</li>\r\n<li><span style=\"font-size: 14px;\">Restriction:&#9702;</span><strong style=\"font-size: 14px;\">Activities triggering a new ATP check (for example, order change or manual check) could lead to incorrect consumption when there is a partial delivery<br /></strong>Prerequisite: Using Supply Protection with partially delivered orders.<br />Mitigation: None</li>\r\n<li><span style=\"font-size: 14px;\">Restriction:&#9702;</span><strong style=\"font-size: 14px;\">Changes in orders are not always considered in Supply Protection and can lead to incorrect matching<br /></strong>Prerequisite: Changing an order which does not trigger ATP.<br />Mitigation: Backorder Processing will call the ATP check and the order will be considered correctly regarding Supply Protection.&#9702;</li>\r\n<li>Restriction&#9702;(valid with OP2020 FPS1 and following):&#9702;<strong>The number of time buckets is limited to 55.<br /></strong>Prerequisite: Maintenance of a supply protection object with many (&gt; 55) time buckets.<br />Mitigation: Create an additional successor supply protection object seamlessly fitting to the predecessor.&#9702;</li>\r\n<li>Restriction:&#9702;<strong>No direct impact of changes in delivery</strong> on consumption of protected quantities<br />Prerequisite: Changes in delivery without related change in the order document.<br />Mitigation: None; only changes on the consuming documents (Sales Orders/STOs) will be considered.</li>\r\n<li>Restriction: <strong>In some cases changed quantities of a Sales Order or STO are not considered as relevant changes for aATP</strong> (especially, if there is already a related delivery). In this case changes of the Sales Order or STO will not lead to an update of the consumption in the related time buckets of Supply Protection.<br />Prerequisite: Change an order which does not trigger ATP.<br />Mitigation: none</li>\r\n<li><em>Restriction</em>: Supersessions are not supported in aATP and lead to wrong results in the consumption of Supply Protection<br /><em>Prerequisite</em>: Use Supersession for Sales Documents or STOs in combination with Supply Protection.<br />Mitigation: For Sales Documents ABC should be used instead of Supersessions. For STOs there is no mitigation.</li>\r\n</ol></ol>\r\n<ul>\r\n<li><strong>Alternative-Based Confirmation (ABC)</strong><br />SAP S/4HANA 2020 supports the productive use of ABC functionality for initial plant determination during sales order creation (inquiry, quotation, contracts, etc. are not supported). In addition, ABC can be used to process previously posted line items, in VA02 or with BOP.</li>\r\n</ul>\r\n<ol><ol>\r\n<li>Restriction:&#9702;<strong>Scheduling Agreements with Delivery Schedules&#9702;</strong><br />Prerequisite: ABC is configured to find a valid substitution strategy according to the attributes in the alternative control.&#9702;<br />Mitigation: Do not use ABC for scheduling agreements with delivery schedules</li>\r\n<li>Restriction:&#9702;<strong>'Other Plants' button is gone&#9702;</strong><br />Prerequisite:&#9702;A line item has been checked with ABC and, therefore, the original material-plant combination has changed.&#9702;<br />Mitigation: Configure ABC to ensure that the best plant is determined automatically.<br />Explanation: This behavior is correct. ABC will, in the long-term, offer interactive plant manipulation capabilities.</li>\r\n<li>Restriction:&#9702;<strong>No ABC evaluation&#9702;for sales BOM items&#9702;</strong>(see also restriction 5)<strong>&#9702;<br /></strong>Prerequisite:&#9702;A requirement relevant subitem of a sales BOM (LUMF)&#9702;is checked and a valid subsitution strategy is determined.<br />Explanation: A sales BOM is plant-specific. Substituting the plant for single subitems might, therefore, lead to inconsistent results.</li>\r\n<li>Restriction:&#9702;<strong>When checking availability for a substitute, PAC and PAL checks are executed using the customizing settings for the original requirement</strong><br />Explanation: This could have multiple unexpected side effects: PAL quantities are consumed for requirements which are not PAL-relevant. These quantities will remain and will not automatically be removed during further processing. Similarly, PAC checks are executed even if customized.<br />Mitigation: Ensure that all potential alternative plants are configured similarly to the original plant, at the&#9702;very least with regards to the requirement relevancy setting and ATP relevancy setting in their corresponding requirement classes.</li>\r\n<li>Restriction:&#9702;<strong>No inline substitution within a subitem</strong><br />Explanation: The plant change in an item which is a subitem of another item is not yet implemented.</li>\r\n<li>Restriction:&#9702;<strong>ABC in Make-to-Stock (MTS) scenario only<br /></strong>Prerequisite: The sales order is created in the context of special stock scenarios (for example, Make-to-Order (MTO)).<br />Explanation: From a PAC perspective, ABC would normally create subitems in the MTS segment only. In other scenarios (for example, MTO or project), there is no stock to confirm against during requirement creation; it is possible to have non-MTS subitems. ABC shall, therefore, not modify or delete these subitems.</li>\r\n<li>Restriction:&#9702;<strong>Product substitution not supported</strong><br />Explanation: ABC does not consume the master data solution provided by Product Substitution.&#9702;ABC will support product substitution master data consumption in a future SAP S/4HANA release.</li>\r\n<li>Restriction: <strong>Supply Assignment/ARun: Retention of assigned quantities for subitems in an ATP recheck not supported</strong><strong><br /></strong>Prerequisite: The material is relevant for ARun and supply assignments exists for subitems<br />Explanation: If an item has quantities assigned to it, a recheck will ensure that the assigned quantities are retained. When a main item is rechecked, the subitems are always deleted and regenerated. In such scenarios, the subitems are always unassigned.</li>\r\n<li>Restriction: <strong>Item category usage in ABC<br /></strong>Details: Item category usage is configured to use a different item category than TAN together with ABC2.<br />Explanation: ABC only supports item category TAN (to be addressed in a future SAP S/4HANA release).</li>\r\n<li><em>Restriction</em>:&#160;<strong>Sales order integration in SAP Transportation Management</strong><br /><em>Details</em>:&#160;Logistics Integration is capable of integrating Sales Orders in SAP Transportation Management.&#160;The integration is neither released nor supported for ABC.<br /><em>Explanation</em>: ABC deletes and recreates sales order subitems whenever a change is made and the integration cannot recognize a potentially existing link to already existing freight units. Freight units are therefore also deleted and recreated &#8211; this removes an existing transportation planning result.</li>\r\n</ol></ol>\r\n<p><strong style=\"font-size: 14px;\"><strong>&#160;</strong></strong></p>\r\n<ul>\r\n<li><strong>Sales Order Processing &#8211; \"Other Plants\" function on the delivery proposal screen</strong></li>\r\n</ul>\r\n<ol><ol>\r\n<li><span style=\"font-size: 14px;\">Restriction:&#9702;</span><strong style=\"font-size: 14px;\">Issues with Product Allocation Check<br /></strong>Details: Checking availability&#9702;in other plants in simulation mode is based on the PAC method only. Furthermore, the requested material availability date&#9702;for the other plants is taken directly from the original sales order item&#9702;and is not scheduled using the original requested delivery date.<br />Mitigation: New scheduling will take place when a&#9702;specific&#9702;plant is selected and&#9702;will use the check methods as customized.</li>\r\n<li><span style=\"font-size: 14px;\">Restriction:&#9702;</span><strong style=\"font-size: 14px;\">\"Other Plants\" does not work for ABC items</strong><span style=\"font-size: 14px;\">.<br /></span>Prerequisite:&#9702;A line item has been checked with ABC. The original material-plant has, therefore, changed.&#9702;<br />Mitigation: Configure ABC to determine the best plant automatically.<br />Explanation: This behavior is correct. ABC will offer interactive plant manipulation capabilities in the long-term.</li>\r\n</ol></ol>\r\n<p><span style=\"font-size: 14px;\"><strong>&#9702;</strong></span></p>\r\n<ul style=\"font-size: 10px; font-family: Verdana, Arial, Helvetica, sans-serif; white-space: normal; word-spacing: 0px; text-transform: none; font-weight: 400; color: #000000; font-style: normal; orphans: 2; widows: 2; letter-spacing: normal; text-indent: 0px; font-variant-ligatures: normal; font-variant-caps: normal; -webkit-text-stroke-width: 0px; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;\">\r\n<li style=\"font-size: 14px; color: black;\"><strong>Sales Order Processing &#8211; Manual change of shipping dates of the schedule line data</strong></li>\r\n</ul>\r\n<ol style=\"font-size: 14px; font-family: Verdana, Arial, Helvetica, sans-serif; white-space: normal; word-spacing: 0px; text-transform: none; font-weight: 400; color: #000000; font-style: normal; orphans: 2; widows: 2; letter-spacing: normal; text-indent: 0px; font-variant-ligatures: normal; font-variant-caps: normal; -webkit-text-stroke-width: 0px; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;\"><ol style=\"font-size: 14px; color: black;\">\r\n<li style=\"font-size: 14px; color: black;\"><em>Problem</em>:&#160;<strong>The availability check overrides manually changed shipping dates with new scheduling results</strong><br /><em>Details</em>: As soon as the availability check needs to schedule the dates again, manually changed shipping dates will be overwritten with the new scheduling results. This happens, for example, if the confirmation is not on-time, or if the product allocation check is involved which can check based on different date time types (like delivery date, goods issue date or material availability date).<br /><em>Mitigation</em>: Do not manually change the shipping dates</li>\r\n</ol></ol>\r\n<p style=\"font-size: 14px; color: black;\"><strong>&#65279;</strong></p>\r\n<ul>\r\n<li><strong>Review Availability Check Result (RACR)</strong></li>\r\n</ul>\r\n<ol><ol>\r\n<li><span style=\"font-size: 14px;\">Restriction: <strong>RACR does not display a confirmation, instead delivery proposal screen displays.</strong><br />Details: Not all scenarios (for example, third-party processing) are covered for displaying confirmations.<br />Explanation: Not implemented (to be addressed in a future SAP S/4HANA release).<br /></span></li>\r\n<li><span style=\"font-size: 14px;\">Restriction: <strong>No navigation from RACR to delivery proposal screen when ATP confirmation has subitems.</strong><br />Details: For items displayed in RACR, you can navigate to the delivery proposal to see additional information or adjust delivery proposal. Navigation to the delivery proposal is not possible when Alternative-Based Confirmation is used and the confirmation include subitems.<br />Explanation:&#9702;This behavior is correct. RACR will offer such delivery proposal functionalities&#9702;in a future SAP S/4HANA release.&#9702;</span></li>\r\n<li>Restriction:&#9702;<strong>\"Other Plants\" does not work together with RACR</strong>.<br />Details: RACR is used to display confirmations and 'Other Plants' in delivery proposal is used, additional ATP checks will be carried out that lead to an additional RACR screen.&#9702;<br />Explanation: This behavior is correct. ABC will offer interactive plant manipulation capabilities in the long-term.</li>\r\n</ol></ol>\r\n<p>If you have any questions relating to the above, please contact your SAP consultant.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CA-ATP (Available to Promise (ATP))"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D058465)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D038632)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002885961/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002885961/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002885961/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002885961/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002885961/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002885961/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002885961/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002885961/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002885961/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2885945", "RefComponent": "CA-ATP-BOP", "RefTitle": "Limitation: Functional Limitation in Configure BOP Segment for HRF-based Segments", "RefUrl": "/notes/2885945"}, {"RefNumber": "2802208", "RefComponent": "XX-SER-REL", "RefTitle": "Restriction Note for advanced Available-to-Promise (aATP) in SAP S/4HANA 1909", "RefUrl": "/notes/2802208"}, {"RefNumber": "2642047", "RefComponent": "XX-SER-REL", "RefTitle": "Restriction Note for advanced Available-to-Promise (aATP) in SAP S/4HANA 1809", "RefUrl": "/notes/2642047"}, {"RefNumber": "2518072", "RefComponent": "XX-SER-REL", "RefTitle": "Restriction Note for advanced Available-to-Promise (aATP) SAP S/4HANA 1709", "RefUrl": "/notes/2518072"}, {"RefNumber": "2343524", "RefComponent": "XX-SER-REL", "RefTitle": "Restriction Note for advanced Available-to-Promise (aATP) in SAP S/4HANA 1610", "RefUrl": "/notes/2343524"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3275747", "RefComponent": "XX-SER-REL", "RefTitle": "Restriction Note for advanced Available-to-Promise (aATP) in SAP S/4HANA 2023", "RefUrl": "/notes/3275747 "}, {"RefNumber": "3205013", "RefComponent": "XX-SER-REL", "RefTitle": "Restriction Note for advanced Available-to-Promise (aATP) in SAP S/4HANA 2022 and 2022 FPS01 and FPS02", "RefUrl": "/notes/3205013 "}, {"RefNumber": "2982461", "RefComponent": "XX-SER-REL", "RefTitle": "Restriction Note for advanced Available-to-Promise (aATP) in SAP S/4HANA 2021", "RefUrl": "/notes/2982461 "}, {"RefNumber": "2952651", "RefComponent": "TM-CF", "RefTitle": "SAP S/4HANA 2020 Supply Chain for Transportation Management - Release information", "RefUrl": "/notes/2952651 "}, {"RefNumber": "2943206", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 2020: Restriction Note", "RefUrl": "/notes/2943206 "}, {"RefNumber": "2802208", "RefComponent": "XX-SER-REL", "RefTitle": "Restriction Note for advanced Available-to-Promise (aATP) in SAP S/4HANA 1909", "RefUrl": "/notes/2802208 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}