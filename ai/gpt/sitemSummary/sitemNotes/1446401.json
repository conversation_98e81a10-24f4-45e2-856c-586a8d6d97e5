{"Request": {"Number": "1446401", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 312, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016990652017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001446401?language=E&token=B496AE252FE11998D174658985771794"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001446401", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001446401/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1446401"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.06.2011"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-ISR-RSL"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW only - Retail Stock Ledger"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Retail and Consumer Products", "value": "BW-BCT-ISR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-ISR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Retail Stock Ledger", "value": "BW-BCT-ISR-RSL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-ISR-RSL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1446401 - How to implement additional buckets in RMA stock ledger"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You want to enhance the standard RMA stock ledger by certain additional buckets.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Retail-Method-of-Accounting, Stock-Ledger<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>ECC 6.03 and BI_CONT 7.04 or higher. Business function ISR_RETAIL_RMA is switched on in ECC.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The solution depends on various influencing factors. One thing which makes a difference is whether the additional bucket directly affects a separate G/L account, which is not among those which are configured for back-posting to FI during RMA period close, or not. This note gives an example for each of the two cases:</p> <OL>1. Additional goods receipts bucket, depending on purchase order (PO) type:</OL> <OL><OL>a) Enhance dictionary structure WRMA_MCMSEG in ECC by field BSART</OL></OL> <OL><OL>b) Call transaction LBWE in ECC, and maintain extract structure MC03BF0 of application '03' (Inventory controlling); take over field BSART</OL></OL> <OL><OL>c) Generate data source 2LIS_03_BF in LBWE</OL></OL> <OL><OL>d) Create an implementation of BAdI RSU5_SAPI_BADI in ECC, where field BSART of data source 2LIS_03_BF has to be filled by means of calling function module ME_EKKO_SINGLE_READ</OL></OL> <OL><OL>e) Replicate the updated data source 2LIS_03_BF to the BW system</OL></OL> <OL><OL>f) Enhance info source 2LIS_03_BF_TR in BW by info object 0DOCTYPE, and map field BSART of the data source to the new attribute of the info source</OL></OL> <OL><OL>g) Activate transformation RSDS 2LIS_03_BF -&gt; TRCS 2LIS_03_BF_TR</OL></OL> <OL><OL>h) Assign a new transaction key for the new bucket to application '03' (Inventory) via IMG in BW Business Intelligence -&gt; Settings for BI Content -&gt; Trade Industries -&gt; Trade Foundation -&gt; Define Applications</OL></OL> <OL><OL>i) Enhance the start routine of transformation TRCS 2LIS_03_BF_TR -&gt; TRCS 0RT_SL_01 by a piece of code, which splits records with transaction key 1 (Goods Receipt / Vendor) into an old one, and a new one with the new transaction key.</OL></OL> <OL><OL>j) Create a new access condition type category for the new bucket via IMG in BW Business Intelligence -&gt; Settings for BI Content -&gt; Trade Industries -&gt; Retailing -&gt; Stock Ledger -&gt; RMA Stock Ledger -&gt; Condition Technology for RMA -&gt; Define Access Condition Type Category.</OL></OL> <OL><OL>k) Create a new access condition type via IMG in BW Business Intelligence -&gt; Settings for BI Content -&gt; Trade Industries -&gt; Retailing -&gt; Stock Ledger -&gt; RMA Stock Ledger -&gt; Condition Technology for RMA -&gt; Define Access Condition Types. Assign the new access condition type to the access condition type category created in the previous step.</OL></OL> <OL><OL>l) Map the new transaction key to the new access condition via IMG in BW Business Intelligence -&gt; Settings for BI Content -&gt; Trade Industries -&gt; Retailing -&gt; Stock Ledger -&gt; RMA Stock Ledger -&gt; Set Updating Control in RMA Raw Data DSO. Note that access condition type TOGR_C for the inventory changes according to Cost Method of Accounting (CMA) has to be provided in the raw data mapping also.</OL></OL> <OL><OL>m) Introduce the new bucket as an additional above-line bucket into the RMA calculation schema in BW:</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Create an additional condition type, and assign application specific data, under IMG node \"Condition technology for RMA\". Depending on whether you use a period-specific or year-to-date-specific RMA calculation schema, you can create the new condition type either as a copy of P_PURC or Y_PURC. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Copy the lines for either P_PURC or Y_PURC in the calculation schema to new lines with the new condition type. <OL>2. Additional cost allocation bucket (e.g. \"manual cost adjustments\"), which is intended for the capitalization of certain inventory-related cost on a separate G/L account.</OL> <OL><OL>a) Create a new condition group for the additional cost allocation bucket in ECC via IMG Logistics - General -&gt; Traditional Retail Method (RMA) -&gt; Cost Allocation -&gt; Maintain buckets for cost allocation; assign the new condition group to use case type '04' (Markdown Provisions). Choose the new condition group from the appropriate customer namespace (between Z5* and Z9*). This namespace has been added with note 1495775.</OL></OL> <OL><OL>b) Depending on whether the single settlement request for cost allocation has to create postings to G/L or not, create a separate billing type and payment type for that document, and assign a separate calculation schema of agency business to it by means of a separate document schema, which is maintained at the billing type level. Such a calculation schema would have at least one line with an account key assigned for which the separate G/L account is configured. Note that, in general, offsetting of that account, and posting to the RMA inventory adjustment account and/or the RMA COGS adjustment account instead, could possibly also be handled solely by the calculation schema of the RMA inbound, which is processed only at the time of end-of-period closing.</OL></OL> <OL><OL>c) Assign the new transaction key for the new bucket to application 'AB' (Agency business) via IMG in BW Business Intelligence -&gt; Settings for BI Content -&gt; Trade Industries -&gt; Trade Foundation -&gt; Define Applications. The new transaction key has to consist of the last three digits of the respective condition group, which was defined earlier in ECC.</OL></OL> <OL><OL>d) Given that the RMA raw data access condition for the new bucket shall have one of the existing access condition type category (e.g. '43' for markdown reserves), just create a new access condition type via IMG in BW Business Intelligence -&gt; Settings for BI Content -&gt; Trade Industries -&gt; Retailing -&gt; Stock Ledger -&gt; RMA Stock Ledger -&gt; Condition Technology for RMA -&gt; Define Access Condition Types. Otherwise, create a new access conditiont type category first via BW Business Intelligence -&gt; Settings for BI Content -&gt; Trade Industries -&gt; Retailing -&gt; Stock Ledger -&gt; RMA Stock Ledger -&gt; Condition Technology for RMA -&gt; Define Access Condition Type Category, and then create the new access condition type as described above, and assigned to the new access condition type category.</OL></OL> <OL><OL>e) Map the new transaction key to the new access condition via IMG in BW Business Intelligence -&gt; Settings for BI Content -&gt; Trade Industries -&gt; Retailing -&gt; Stock Ledger -&gt; RMA Stock Ledger -&gt; Set Updating Control in RMA Raw Data DSO. Source field for the key figure needs to be 0AB_NETVAL. It is assumed here that the value entered in ECC represents a cost value, so that no further transformation of that value needs to be carried out in the RMA calculation schema.</OL></OL> <OL><OL>f) Introduce the new bucket as an additional above-line bucket into the RMA calculation schema in BW:</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Create an additional condition type, and assign application specific data, under IMG node \"Condition technology for RMA\". Depending on whether you use a period-specific or year-to-date-specific RMA calculation schema, you can create the new condition type either as a copy of P_PRDF or Y_PRDF. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Copy the lines for either P_PRDF or Y_PRDF in the calculation schema to new lines with the new condition type. <OL><OL>g) Add the new access condition type to the following table of conditions which have to be transferred back to ECC during RMA period close: IMG in BW Business Intelligence -&gt; Settings for BI Content -&gt; Trade Industries -&gt; Retailing -&gt; Stock Ledger -&gt; RMA Stock Ledger -&gt; Define Data Transfer RMA -&gt; ERP</OL></OL> <OL><OL>h) Maintain the new BW access condition type also in ECC via IMG Logistics - General -&gt; Traditional Retail Method (RMA) -&gt; Inbound Processing and FI Posting -&gt; Maintain BI Access Condition Types</OL></OL> <OL><OL>i) Create a fixed value append of domain WRMA_COND_INTENT in ECC with fixed value 'Z' (Manual cost adjustments), and activate the domain.</OL></OL> <OL><OL>j) Define a new FI condition type for the new meaning of condition type (fixed value 'Z'). For doing so, carry out the following steps:</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Create the condition type via IMG Logistics - General -&gt; Agency Business -&gt; Basic Settings -&gt; Define Price Determination Process -&gt; Define Condition Types with settings condition class 'A' (Discount or surcharge), calculation type 'A' (Percentage), and 'Item condition' flagged. For comparison, you could look at condition type RMF0 from delivery customizing. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Enter the new condition type as FI update condition type via IMG Logistics - General -&gt; Traditional Retail Method (RMA) -&gt; Inbound Processing and FI Posting -&gt; Define FI Update Condition Type <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Assign the new FI update condition type to the new condition type meaning: IMG Logistics - General -&gt; Traditional Retail Method (RMA) -&gt; Inbound Processing and FI Posting -&gt; Assign Meaning to Condition Types <OL><OL>k) Define a pair of statistic condition types for the new FI condition type. For doing so, carry out the following steps:</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Create two new ondition types via IMG Logistics - General -&gt; Agency Business -&gt; Basic Settings -&gt; Define Price Determination Process -&gt; Define Condition Types with settings condition class 'A' (Discount or surcharge), calculation type 'B' (Fixed amount), and 'Item condition' flagged. For comparison, you could look at condition types RMB9 and RMF1 from delivery customizing. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Assign the two new statistic condition types to its appropriate meanings: IMG Logistics - General -&gt; Traditional Retail Method (RMA) -&gt; Inbound Processing and FI Posting -&gt; Assign Meaning to Condition Types. The one comparable to RMF1, must have the new meaning (created as a fixed value append of domain WRMA_COND_INTENT previously). The other one - comparable to RMB9 - must have meaning '2' (Loss from Stock Transfer), since it acts as an RMA above line adjustment. <OL><OL>l) Map the new statistic ERP condition types to the new BI condition type: IMG Logistics - General -&gt; Traditional Retail . Method (RMA) -&gt; Inbound Processing and FI Posting -&gt; Link BI and ERP Condition Types. For doing so, the entries for BI condition type PRDIFF from standard customizing can be copied accordingly. (For one of the condition types, the condition value must be multiplied by -1; this is the one which will go to G/L account to be offset in the calculation schema of agency business.)</OL></OL> <OL><OL>m) Create a new account key for the affected G/L account, if no such account key exists so far, by maintaining view V_T687, application 'M' (Purchasing).</OL></OL> <OL><OL>n) Assign the affected G/L account to the new account key by means of transaction OBYC.</OL></OL> <OL><OL>o) Enhance the calculation schema of the RMA inbound agent: IMG Logistics - General -&gt; Agency Business -&gt; Basic Settings -&gt; Define Price Determination Process -&gt; Define Calculation Schema. For doing so, the entries from standard calculation schema RMAAD2 for condition types RMB9, RMF0 and RMF1 can be copied appropriately. The new account key needs to be assigned to the line with the new FI condition type.</OL></OL> <OL>3. In addition to the above mentioned changes for cases 1 and 2. the RMA audit trail has to be enhanced with the new buckets. For enhancement of the audit trail some existing SAP coding has to be modified. Please execute the following steps.</OL> <OL><OL>a) Please configure additional lines for the new buckets in the Raw Data Customizing. For the audit trail it is important that the document DSO is maintained at the second level of the customizing \"BW logistics Process Key\".</OL></OL> <p></p> <OL><OL>b) Please adjust the coding in form routine DISPLAY_DOCUMENT of include WRMA_AUDIT_TRAIL_DOC_F01 to allow the display of the Total RMA values in the Audit Trail for the new bucket. In the form routine a main \"CASE\" structure can be found. Here you have to add an additional \"When\" statement for each new bucket. The sample provides an idea how the additional coding should look like:</OL></OL> <p><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; WHEN 'Access Condition Type Category of the new bucket'.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; g_docu_title = 'Description of the new bucket'.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; CALL SCREEN 0001.<br /></p> <OL><OL>c) With this changes the system will display all existing documents for the new bucket as a above line bucket, means the key figures Cost, Retail and Retail with Tax would be displayed even if they are empty and not filled. You can distinguish between above and below line buckets but adding additional lines in include WRMA_AUDIT_TRAIL_DOC_LCL_IMP inside method DISPLAY_DOCUMENTS. If the new bucket is used as a below line bucket please add the access condition type category of the new bucket into the ELSEIF condition. If the new bucket is used as a above line bucket it is not necessary to change coding here. The following sample gives an idea how the additional coding could look like:</OL></OL> <p><br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Check if Reclassification documents are requested<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;IF g_acc_categ EQ gc_reclass_in OR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;g_acc_categ EQ gc_reclass_out.<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Check if Below line bucket documents are requested<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ELSEIF g_acc_categ EQ gc_count_adjust&#x00A0;&#x00A0;OR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; g_acc_categ EQ gc_actual_shrink OR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; g_acc_categ EQ 'Access Cond. Type Categ. of the new bucket'.</p> <OL><OL>d) To display the details for a single document for example in a transaction in SAP ERP you have to adjust the from-routine CALL_ERP_TRANSACTION inside the include WRMA_AUDIT_TRAIL_DOC_F01. In the case statement for the Access Condition Type Category you have to add the new bucket and specify what ERP transaction should be executed for the bucket by calling a remote function call. In the standard delivery transactions like MIR4, MIGO, FB03, VF03, MB03 and WZR3 are covered. The following sample gives an idea how the additional coding could look for a additional cost allocation bucket:</OL></OL> <p><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; CASE g_acc_categ.<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;new Bucket<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;WHEN 'Access Condition Type Category of the new bucket'<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PERFORM display_erp_agency_business<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;USING i_doc_num<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;i_source_system.</p> <OL>4. Further remark: Sometimes, it also makes sense to map an additional custom key figure from a Trade Foundation data source to a custom RMA bucket. If, for instance, the name of the custom key figure is ZBUCKET, note that, in addition to maintaining the raw data update customizing with field name ZBUCKET (for the source key figure), it is required to create a customer append of structure WRMA_S_SOURCE_FIELDS_RMA with field BUCKET, for the new key figure to get transferred to the RMA raw data DSO 0RMA_DS01.</OL> <OL>5. In case the custom bucket is filled using a key figure outside the SAP namespace (for example ZVALUE) the key figure needs to be added in the following way as source field in the RMA raw data customizing. The field name has to start with a single character like Z to indicate that the key figure is a custom one. After that the prefix /BIC/ has to follow. And finally the InfoObject name of the customer key figure must be specified. Example: a customer uses a key figure ZVALUE. In RMA raw data customizing this field needs to be configured as Z/BIC/ZVALUE.</OL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D019835)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D024566)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001446401/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001446401/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001446401/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001446401/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001446401/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001446401/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001446401/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001446401/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001446401/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1602879", "RefComponent": "BW-BCT-ISR-RSL", "RefTitle": "Audit Trail is not working for Customer Buckets", "RefUrl": "/notes/1602879"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "586163", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Composite SAP Note for SAP ERP Inventory Management in SAP BW", "RefUrl": "/notes/586163 "}, {"RefNumber": "1602879", "RefComponent": "BW-BCT-ISR-RSL", "RefTitle": "Audit Trail is not working for Customer Buckets", "RefUrl": "/notes/1602879 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}