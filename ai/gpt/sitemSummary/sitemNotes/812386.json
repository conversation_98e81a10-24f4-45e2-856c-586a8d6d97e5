{"Request": {"Number": "812386", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 394, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015837132017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000812386?language=E&token=8E885857DA5DF902FA525ED99323766A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000812386", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000812386/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "812386"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.05.2014"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-NET-HTL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Problems with remote access from SAP to Customer system"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Network connection", "value": "XX-SER-NET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-NET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Problems with remote access from SAP to Customer system", "value": "XX-SER-NET-HTL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-NET-HTL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "812386 - RFC connections to SAPNet R/3 front end"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note provides a description of the RFC connections to the SAPNet R/3 front end.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Logon groups SAPNet R/3 front end (OSS), SMLG, EWA, SNOTE, Solution Manager, SDCC, SDCCN, RTCCTOOL, RSECNOTE, MOPZ, maintenance optimizer, Solution Manager basic functions, RNOTIFUPDATE, RNOTIFUPDATE01, RNOTIFUPDATE7, OSS1, Service Provider, VAR, Service Desk, partner, incident management, RFC connections to SAP, GET_PPMS_DATA_FROM_OSS, RAGS_DSWP_SERV_CONTENT_UPDATE, AI_SC_LISTENER, AI_SC_SEND_SYSTEM_RELATIONSHIP, AI_SC_REFRESH_READ_ONLY_DATA, SOLMAN_SETUP, Service Delivery, ST13, ST14_RTCC, OSS_RFC, EarlyWatch Alert, Service Data Control Center, SAPSNOTE, CUPOSS, SOLMAN_SAP_BACKEND008, REFRESH_ADMIN_DATA_FROM_SUPPORT, SAPOSS, SAP-OSS, SAP-OSS-LIST, CUPOSS, SDCC_OSS, SAPNET_RFC, SAPNET_RTCC, OSS, AI_CRM_IM_UPDATE_FROM_SAP, AI_CRM_IM_UPDATE_FROM_SAP_ISV, SAProuter</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>No connection to the SAPNet R/3 front end, poor performance</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>======================================================================</strong><br /><strong>For releases lower than 4.0*, ignore this SAP Note. In this case, you do not need to make any changes.</strong><br /><strong>======================================================================</strong><br /><br />You must check your connections to the SAPNet R/3 front end (OSS) (transactions: OSS1, SDCC). The RFC connection in your customer system (transaction /sm59) is still set to \"No Load Balancing\" and therefore accesses the main instance of the SAPNet R/3 front end directly. For technical reasons, you should use the load distribution feature of the SAPNet R/3 front end. This has nothing to do with the general deactivation of the SAPGUI access for OSS. An RFC access will still be possible under the specified prerequisites.<br /><br />Refer also to SAP Note 766505, which automatically recreates the RFC connection SAPOSS for load balancing with the relevant logon group. The RFC connection SAPOSS serves as a template for all further connections to the SAPNet R/3 front end. To ensure that these RFC connections are also set correctly, you must first delete these connections (if they exist) using transaction SM59 and create them again using the relevant transactions. Other logon groups must no longer be used for the data transfer to SAP.<br /><br />Please use the following procedure:<br />1.) SAP Note 766505: import the change using transaction SNOTE.</p>\r\n<p>2.) Call transaction SM59 in your SAP system.<br />This shows all existing RFC connections.<br />Delete only the following RFC connections:<br />SAPOSS Transaction OSS1, SAP Note 766505<br />SAPNET_RFC Transaction SDCC<br />SAPNET_RTCC Transaction SDCC<br />SDCC_OSS Transaction SDCCN</p>\r\n<p>3.) Then call transaction OSS1 and choose \"Parameters -&gt; Techn. Settings -&gt; Change Mode -&gt; SAVE\".<br />The RFC destination SAPOSS is not updated automatically until you save.</p>\r\n<p>4.) Recreation of RFC connections:</p>\r\n<ul>\r\n<li>You can create SAPNET_RTCC as follows: Call transaction SE38 -&gt; program name RTCCTOOL -&gt; menu \"List -&gt; Refresh from SAPNet\" <br />=&gt; see SAP Note 763561</li>\r\n<li>SAPNET_RFC =&gt; see SAP Note 763561</li>\r\n<li>SDCC_OSS =&gt; see SAP Note 763561</li>\r\n<li>SDCCN =&gt; see SAP Note 763561</li>\r\n</ul>\r\n<p>In an SAP system, you can usually find the following RFC connections (see also SAP Note 2000132):</p>\r\n<p>RFC connections<br />----------------------------------------------------------------------<br />SAPOSS Transaction OSS1, SAP Note 766505<br />SAPNET_RFC Transaction SDCC<br />SAPNET_RTCC Transaction SDCC<br />SDCC_OSS Transaction SDCCN<br />CUPOSS<br />OSSNOTE<br />SAP-OSS<br />SAP-OSS-LIST<br />SM_SP_&lt;customer number&gt;</p>\r\n<p>Use of logon groups in dialog mode (SAPGUI connection to the SAPNET R/3 front end, started with transaction OSS1):<br /><br />Logon Group    Purpose<br />----------------------------------------------------------------------<br />1_PUBLIC Note Assistant, customer messages, tracking of<br /> message status.<br />2_JAPANESE As with 1_PUBLIC, for Japanese customers.</p>\r\n<p>EWA Use of EarlyWatch Alert</p>\r\n<p><strong>Performance problem with SNOTE</strong></p>\r\n<p>If you experience a performance problem with SNOTE, download the SAP Note from SAP Service Marketplace using the SAP Download Manager. You can find further information in SAP Note 797001.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-SMG-SER-EWA (EarlyWatch Alert)"}, {"Key": "Other Components", "Value": "XX-SER-SAPSMP-SUP (Support Applications)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D016377)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D016377)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000812386/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000812386/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000812386/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000812386/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000812386/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000812386/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000812386/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000812386/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000812386/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "982045", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/982045"}, {"RefNumber": "906992", "RefComponent": "XX-SER-NET-RCSC", "RefTitle": "AS/400: Load Balancing", "RefUrl": "/notes/906992"}, {"RefNumber": "802659", "RefComponent": "SV-SMG-SUP-INT", "RefTitle": "Update job RNOTIFUPDATE01 no longer works", "RefUrl": "/notes/802659"}, {"RefNumber": "801846", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/801846"}, {"RefNumber": "766505", "RefComponent": "XX-SER-NET", "RefTitle": "OSS1: Changes to RFC connection SAPOSS", "RefUrl": "/notes/766505"}, {"RefNumber": "763561", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCCN)  - FAQ", "RefUrl": "/notes/763561"}, {"RefNumber": "455709", "RefComponent": "BC-MID-RFC", "RefTitle": "Message server in SM59 is too short (shorter than 100)", "RefUrl": "/notes/455709"}, {"RefNumber": "33135", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "Guide for OSS1", "RefUrl": "/notes/33135"}, {"RefNumber": "216952", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCC) - FAQ", "RefUrl": "/notes/216952"}, {"RefNumber": "182308", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/182308"}, {"RefNumber": "169924", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/169924"}, {"RefNumber": "1234500", "RefComponent": "SV-SMG-UMP", "RefTitle": "Composite SAP Note: Information about automatic update", "RefUrl": "/notes/1234500"}, {"RefNumber": "1172939", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1172939"}, {"RefNumber": "1140938", "RefComponent": "SV-SMG-OP", "RefTitle": "There is no new service plan to be collected ...", "RefUrl": "/notes/1140938"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "766505", "RefComponent": "XX-SER-NET", "RefTitle": "OSS1: Changes to RFC connection SAPOSS", "RefUrl": "/notes/766505 "}, {"RefNumber": "455709", "RefComponent": "BC-MID-RFC", "RefTitle": "Message server in SM59 is too short (shorter than 100)", "RefUrl": "/notes/455709 "}, {"RefNumber": "906992", "RefComponent": "XX-SER-NET-RCSC", "RefTitle": "AS/400: Load Balancing", "RefUrl": "/notes/906992 "}, {"RefNumber": "802659", "RefComponent": "SV-SMG-SUP-INT", "RefTitle": "Update job RNOTIFUPDATE01 no longer works", "RefUrl": "/notes/802659 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "46A", "To": "46D", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "640", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "730", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "740", "To": "740", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}