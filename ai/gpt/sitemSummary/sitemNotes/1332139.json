{"Request": {"Number": "1332139", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 449, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016777992017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001332139?language=E&token=5C77088D946206AD2F8E16F392116A0F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001332139", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001332139/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1332139"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.09.2009"}, "SAPComponentKey": {"_label": "Component", "value": "BC-XI-CON"}, "SAPComponentKeyText": {"_label": "Component", "value": "Connectivity"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "NetWeaver Process Integration (PI)", "value": "BC-XI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-XI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Connectivity", "value": "BC-XI-CON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-XI-CON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1332139 - NW04s Support Package Stack 19"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You want to import SAP Exchange Infrastructure (XI) NW04s Support Package Stack 19 using the Support Package Stack Guide.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SAP Exchange Infrastructure, XI 70, SAPXITOOL, SAPXIAF, SAPXIAFC<br />SAPXICONS, SAPXIPCK, SPS19, Support Package Stack 19, NW04s</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Support Package 19 contains all the attached XI corrections. Some functions were corrected and the corrections that are mentioned in the SAP notes were implemented. Import Support Package Stack 19 for SAP Process Infrastructure using theSupport Package Stack Guide. The Support Package Stack Guide is available on SAP Service Marketplace as described in Note 952402.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D037120)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON> (D049116)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001332139/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001332139/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001332139/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001332139/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001332139/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001332139/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001332139/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001332139/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001332139/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "824821", "RefComponent": "BC-BMT-WFM-MON", "RefTitle": "Deadline reporting ignores all blocks", "RefUrl": "/notes/824821"}, {"RefNumber": "1376276", "RefComponent": "BC-XI", "RefTitle": "NW04s XI Support Package Stack 20", "RefUrl": "/notes/1376276"}, {"RefNumber": "1315276", "RefComponent": "BC-XI-IS", "RefTitle": "PI7.x: Display of messages during switch run", "RefUrl": "/notes/1315276"}, {"RefNumber": "1313447", "RefComponent": "BC-XI-CON-ABA-IDO", "RefTitle": "IDoc adapter: Dump in outbound adapter for packaging", "RefUrl": "/notes/1313447"}, {"RefNumber": "1313378", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: Incorrect content type with XI adapter", "RefUrl": "/notes/1313378"}, {"RefNumber": "1311880", "RefComponent": "BC-XI-CON-AFW", "RefTitle": "SOAP (Axis) adapter app may not be redeployed nor restarted", "RefUrl": "/notes/1311880"}, {"RefNumber": "1311247", "RefComponent": "BC-XI-IS", "RefTitle": "XI Runtime: \"Boundary not set\" in XI HTTP adapter", "RefUrl": "/notes/1311247"}, {"RefNumber": "1311219", "RefComponent": "BC-XI-IS-SLD", "RefTitle": "Incorrect error text for error in SLD access", "RefUrl": "/notes/1311219"}, {"RefNumber": "1310238", "RefComponent": "BC-BMT-WFM-BND", "RefTitle": "Runtime error COMPARE_NOT_SUPPORTED in CL_SWF_CNT_SERVICE", "RefUrl": "/notes/1310238"}, {"RefNumber": "1309542", "RefComponent": "BC-XI-CON-JDB", "RefTitle": "Several open connections noticed in DB2", "RefUrl": "/notes/1309542"}, {"RefNumber": "1308428", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: Optimizing the channel database access", "RefUrl": "/notes/1308428"}, {"RefNumber": "1308233", "RefComponent": "BC-XI-IS", "RefTitle": "Transfer of non-XI header to synchronous response", "RefUrl": "/notes/1308233"}, {"RefNumber": "1306410", "RefComponent": "BC-XI-IS", "RefTitle": "Queue status is not always set correctly", "RefUrl": "/notes/1306410"}, {"RefNumber": "1306388", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: Error when canceling messages", "RefUrl": "/notes/1306388"}, {"RefNumber": "1305273", "RefComponent": "BC-XI-CON-ABA-IDO", "RefTitle": "IDoc adapter: Incorrect outbound binding", "RefUrl": "/notes/1305273"}, {"RefNumber": "1303599", "RefComponent": "BC-XI-IBC-MAP", "RefTitle": "XI mapping: Endless loop in XI packaging", "RefUrl": "/notes/1303599"}, {"RefNumber": "1301864", "RefComponent": "BC-XI-IS-SLD", "RefTitle": "XI-Runtime: Error when registering AAE in SLD", "RefUrl": "/notes/1301864"}, {"RefNumber": "1301363", "RefComponent": "BC-XI-IBC", "RefTitle": "XI Routing: Receiver party determined incorrectly", "RefUrl": "/notes/1301363"}, {"RefNumber": "1299599", "RefComponent": "BC-XI-IS-BPE", "RefTitle": "BPE-RUN: Checking parameters for send steps", "RefUrl": "/notes/1299599"}, {"RefNumber": "1298448", "RefComponent": "BC-XI-IS", "RefTitle": "XI Runtime: no automatic retry for EO message processing", "RefUrl": "/notes/1298448"}, {"RefNumber": "1297830", "RefComponent": "BC-XI-IS", "RefTitle": "XI Runtime: Problems in Simple SOAP Adapter inbound procg", "RefUrl": "/notes/1297830"}, {"RefNumber": "1297495", "RefComponent": "BC-XI-CON-FIL", "RefTitle": "File adapter: NegativeArrayException occurs while Archiving", "RefUrl": "/notes/1297495"}, {"RefNumber": "1297282", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "MTE class 'SXMBSysErrorAlerts' - Incorrect alert severity", "RefUrl": "/notes/1297282"}, {"RefNumber": "1296819", "RefComponent": "BC-XI-CON-JDB", "RefTitle": "Configuring Maximum Message Size Limits for OOM Error", "RefUrl": "/notes/1296819"}, {"RefNumber": "1295746", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: Error in event-driven message processing", "RefUrl": "/notes/1295746"}, {"RefNumber": "1294947", "RefComponent": "BC-XI-IS-IEN", "RefTitle": "Event-driven message processing: No queue entry", "RefUrl": "/notes/1294947"}, {"RefNumber": "1294766", "RefComponent": "BC-XI-CON-JMS", "RefTitle": "Connect on Demand Feature for JMS Adapter", "RefUrl": "/notes/1294766"}, {"RefNumber": "1294312", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "XI alerting: SXMS_TO_ADAPTER_ERRTXT is truncated", "RefUrl": "/notes/1294312"}, {"RefNumber": "1294211", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: Synchronous messages with response mapping", "RefUrl": "/notes/1294211"}, {"RefNumber": "1294122", "RefComponent": "BC-XI-IS", "RefTitle": "XI3.0/PI7.x: Archiving the tables IDXSNDPOR and IDXRCVPOR", "RefUrl": "/notes/1294122"}, {"RefNumber": "1293823", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: SA bridge and duplicate synchronous calls", "RefUrl": "/notes/1293823"}, {"RefNumber": "1293344", "RefComponent": "BC-XI-CON-ABA-IDO", "RefTitle": "IDoc adapter: Incorr control record fields with XI packaging", "RefUrl": "/notes/1293344"}, {"RefNumber": "1292248", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: Activity monitor and work prozess 0", "RefUrl": "/notes/1292248"}, {"RefNumber": "1292067", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "JCO connection for retrieving the end-to-end-monitoring Url", "RefUrl": "/notes/1292067"}, {"RefNumber": "1290742", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "Audit Log can't be displayed due to IllegalArgumentException", "RefUrl": "/notes/1290742"}, {"RefNumber": "1286336", "RefComponent": "BC-BMT-WFM-RUN", "RefTitle": "SWIA: New mass-capable ‘Logically Delete’ function", "RefUrl": "/notes/1286336"}, {"RefNumber": "1286041", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: Writing the receiver for acknowledgement msgs", "RefUrl": "/notes/1286041"}, {"RefNumber": "1282862", "RefComponent": "BC-XI-CON-AFW", "RefTitle": "SLD dependency for getting the XI connection params in AF", "RefUrl": "/notes/1282862"}, {"RefNumber": "1281584", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: HTTP protocol checks", "RefUrl": "/notes/1281584"}, {"RefNumber": "1281328", "RefComponent": "BC-XI-IS", "RefTitle": "Split mapping: Dump SYSTEM_NO_ROLL", "RefUrl": "/notes/1281328"}, {"RefNumber": "1277359", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: \"Zero Object\" error in sender queue", "RefUrl": "/notes/1277359"}, {"RefNumber": "1273096", "RefComponent": "BC-XI-CON", "RefTitle": "NW04s Support Package Stack 18", "RefUrl": "/notes/1273096"}, {"RefNumber": "1272854", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: Synchronous message with the same ID", "RefUrl": "/notes/1272854"}, {"RefNumber": "1272512", "RefComponent": "BC-XI-IS", "RefTitle": "XI mapping: Endless loop in split mapping", "RefUrl": "/notes/1272512"}, {"RefNumber": "1271812", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: Total message size in runtime object", "RefUrl": "/notes/1271812"}, {"RefNumber": "1269336", "RefComponent": "BC-XI-IS", "RefTitle": "Packaging: No receiver can be determined", "RefUrl": "/notes/1269336"}, {"RefNumber": "1268238", "RefComponent": "BC-XI-CON-ABA-IDO", "RefTitle": "IDoc adapter: Duplicate segment fields in XML", "RefUrl": "/notes/1268238"}, {"RefNumber": "1266924", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "TREX Indexing: database table XI_AF_TREX_MSG_LOG grows big", "RefUrl": "/notes/1266924"}, {"RefNumber": "1265459", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "XI Alerting: Changing alert rules possible w/o authorization", "RefUrl": "/notes/1265459"}, {"RefNumber": "1261933", "RefComponent": "BC-XI-IBD-MAP", "RefTitle": "Context Caching Function behaviour change since 1158485", "RefUrl": "/notes/1261933"}, {"RefNumber": "1260178", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "Status shows a red \"LED\" after activation of indexing", "RefUrl": "/notes/1260178"}, {"RefNumber": "1258920", "RefComponent": "BC-XI-IS", "RefTitle": "XI3.0/PI7.x: Incorrect time stamp name in performance header", "RefUrl": "/notes/1258920"}, {"RefNumber": "1255110", "RefComponent": "BC-XI-IS-BPE", "RefTitle": "BPE-RUN: Adapter status of processed message is not final", "RefUrl": "/notes/1255110"}, {"RefNumber": "1247753", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "Message Moni: WebDispatcher not used for payload display", "RefUrl": "/notes/1247753"}, {"RefNumber": "1240862", "RefComponent": "BC-XI-CON-AFW", "RefTitle": "Incorrect password returned when ispassword=false in AMD", "RefUrl": "/notes/1240862"}, {"RefNumber": "1113757", "RefComponent": "BC-XI-IS", "RefTitle": "XI3.0/7.0x/7.1x: Clean-up of the history table", "RefUrl": "/notes/1113757"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1113757", "RefComponent": "BC-XI-IS", "RefTitle": "XI3.0/7.0x/7.1x: Clean-up of the history table", "RefUrl": "/notes/1113757 "}, {"RefNumber": "1271812", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: Total message size in runtime object", "RefUrl": "/notes/1271812 "}, {"RefNumber": "1294766", "RefComponent": "BC-XI-CON-JMS", "RefTitle": "Connect on Demand Feature for JMS Adapter", "RefUrl": "/notes/1294766 "}, {"RefNumber": "1261933", "RefComponent": "BC-XI-IBD-MAP", "RefTitle": "Context Caching Function behaviour change since 1158485", "RefUrl": "/notes/1261933 "}, {"RefNumber": "1282862", "RefComponent": "BC-XI-CON-AFW", "RefTitle": "SLD dependency for getting the XI connection params in AF", "RefUrl": "/notes/1282862 "}, {"RefNumber": "1299599", "RefComponent": "BC-XI-IS-BPE", "RefTitle": "BPE-RUN: Checking parameters for send steps", "RefUrl": "/notes/1299599 "}, {"RefNumber": "1297495", "RefComponent": "BC-XI-CON-FIL", "RefTitle": "File adapter: NegativeArrayException occurs while Archiving", "RefUrl": "/notes/1297495 "}, {"RefNumber": "1295746", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: Error in event-driven message processing", "RefUrl": "/notes/1295746 "}, {"RefNumber": "1240862", "RefComponent": "BC-XI-CON-AFW", "RefTitle": "Incorrect password returned when ispassword=false in AMD", "RefUrl": "/notes/1240862 "}, {"RefNumber": "1298448", "RefComponent": "BC-XI-IS", "RefTitle": "XI Runtime: no automatic retry for EO message processing", "RefUrl": "/notes/1298448 "}, {"RefNumber": "1255110", "RefComponent": "BC-XI-IS-BPE", "RefTitle": "BPE-RUN: Adapter status of processed message is not final", "RefUrl": "/notes/1255110 "}, {"RefNumber": "1310238", "RefComponent": "BC-BMT-WFM-BND", "RefTitle": "Runtime error COMPARE_NOT_SUPPORTED in CL_SWF_CNT_SERVICE", "RefUrl": "/notes/1310238 "}, {"RefNumber": "1286336", "RefComponent": "BC-BMT-WFM-RUN", "RefTitle": "SWIA: New mass-capable ‘Logically Delete’ function", "RefUrl": "/notes/1286336 "}, {"RefNumber": "1247753", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "Message Moni: WebDispatcher not used for payload display", "RefUrl": "/notes/1247753 "}, {"RefNumber": "1309542", "RefComponent": "BC-XI-CON-JDB", "RefTitle": "Several open connections noticed in DB2", "RefUrl": "/notes/1309542 "}, {"RefNumber": "1296819", "RefComponent": "BC-XI-CON-JDB", "RefTitle": "Configuring Maximum Message Size Limits for OOM Error", "RefUrl": "/notes/1296819 "}, {"RefNumber": "1297830", "RefComponent": "BC-XI-IS", "RefTitle": "XI Runtime: Problems in Simple SOAP Adapter inbound procg", "RefUrl": "/notes/1297830 "}, {"RefNumber": "1293344", "RefComponent": "BC-XI-CON-ABA-IDO", "RefTitle": "IDoc adapter: Incorr control record fields with XI packaging", "RefUrl": "/notes/1293344 "}, {"RefNumber": "1269336", "RefComponent": "BC-XI-IS", "RefTitle": "Packaging: No receiver can be determined", "RefUrl": "/notes/1269336 "}, {"RefNumber": "1293823", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: SA bridge and duplicate synchronous calls", "RefUrl": "/notes/1293823 "}, {"RefNumber": "1313447", "RefComponent": "BC-XI-CON-ABA-IDO", "RefTitle": "IDoc adapter: Dump in outbound adapter for packaging", "RefUrl": "/notes/1313447 "}, {"RefNumber": "1376276", "RefComponent": "BC-XI", "RefTitle": "NW04s XI Support Package Stack 20", "RefUrl": "/notes/1376276 "}, {"RefNumber": "1290742", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "Audit Log can't be displayed due to IllegalArgumentException", "RefUrl": "/notes/1290742 "}, {"RefNumber": "1311880", "RefComponent": "BC-XI-CON-AFW", "RefTitle": "SOAP (Axis) adapter app may not be redeployed nor restarted", "RefUrl": "/notes/1311880 "}, {"RefNumber": "1292067", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "JCO connection for retrieving the end-to-end-monitoring Url", "RefUrl": "/notes/1292067 "}, {"RefNumber": "1308428", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: Optimizing the channel database access", "RefUrl": "/notes/1308428 "}, {"RefNumber": "1273096", "RefComponent": "BC-XI-CON", "RefTitle": "NW04s Support Package Stack 18", "RefUrl": "/notes/1273096 "}, {"RefNumber": "1266924", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "TREX Indexing: database table XI_AF_TREX_MSG_LOG grows big", "RefUrl": "/notes/1266924 "}, {"RefNumber": "824821", "RefComponent": "BC-BMT-WFM-MON", "RefTitle": "Deadline reporting ignores all blocks", "RefUrl": "/notes/824821 "}, {"RefNumber": "1315276", "RefComponent": "BC-XI-IS", "RefTitle": "PI7.x: Display of messages during switch run", "RefUrl": "/notes/1315276 "}, {"RefNumber": "1311247", "RefComponent": "BC-XI-IS", "RefTitle": "XI Runtime: \"Boundary not set\" in XI HTTP adapter", "RefUrl": "/notes/1311247 "}, {"RefNumber": "1308233", "RefComponent": "BC-XI-IS", "RefTitle": "Transfer of non-XI header to synchronous response", "RefUrl": "/notes/1308233 "}, {"RefNumber": "1311219", "RefComponent": "BC-XI-IS-SLD", "RefTitle": "Incorrect error text for error in SLD access", "RefUrl": "/notes/1311219 "}, {"RefNumber": "1313378", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: Incorrect content type with XI adapter", "RefUrl": "/notes/1313378 "}, {"RefNumber": "1303599", "RefComponent": "BC-XI-IBC-MAP", "RefTitle": "XI mapping: Endless loop in XI packaging", "RefUrl": "/notes/1303599 "}, {"RefNumber": "1306410", "RefComponent": "BC-XI-IS", "RefTitle": "Queue status is not always set correctly", "RefUrl": "/notes/1306410 "}, {"RefNumber": "1306388", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: Error when canceling messages", "RefUrl": "/notes/1306388 "}, {"RefNumber": "1305273", "RefComponent": "BC-XI-CON-ABA-IDO", "RefTitle": "IDoc adapter: Incorrect outbound binding", "RefUrl": "/notes/1305273 "}, {"RefNumber": "1258920", "RefComponent": "BC-XI-IS", "RefTitle": "XI3.0/PI7.x: Incorrect time stamp name in performance header", "RefUrl": "/notes/1258920 "}, {"RefNumber": "1301864", "RefComponent": "BC-XI-IS-SLD", "RefTitle": "XI-Runtime: Error when registering AAE in SLD", "RefUrl": "/notes/1301864 "}, {"RefNumber": "1301363", "RefComponent": "BC-XI-IBC", "RefTitle": "XI Routing: Receiver party determined incorrectly", "RefUrl": "/notes/1301363 "}, {"RefNumber": "1297282", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "MTE class 'SXMBSysErrorAlerts' - Incorrect alert severity", "RefUrl": "/notes/1297282 "}, {"RefNumber": "1294122", "RefComponent": "BC-XI-IS", "RefTitle": "XI3.0/PI7.x: Archiving the tables IDXSNDPOR and IDXRCVPOR", "RefUrl": "/notes/1294122 "}, {"RefNumber": "1294947", "RefComponent": "BC-XI-IS-IEN", "RefTitle": "Event-driven message processing: No queue entry", "RefUrl": "/notes/1294947 "}, {"RefNumber": "1294312", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "XI alerting: SXMS_TO_ADAPTER_ERRTXT is truncated", "RefUrl": "/notes/1294312 "}, {"RefNumber": "1294211", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: Synchronous messages with response mapping", "RefUrl": "/notes/1294211 "}, {"RefNumber": "1292248", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: Activity monitor and work prozess 0", "RefUrl": "/notes/1292248 "}, {"RefNumber": "1281584", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: HTTP protocol checks", "RefUrl": "/notes/1281584 "}, {"RefNumber": "1286041", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: Writing the receiver for acknowledgement msgs", "RefUrl": "/notes/1286041 "}, {"RefNumber": "1281328", "RefComponent": "BC-XI-IS", "RefTitle": "Split mapping: Dump SYSTEM_NO_ROLL", "RefUrl": "/notes/1281328 "}, {"RefNumber": "1272512", "RefComponent": "BC-XI-IS", "RefTitle": "XI mapping: Endless loop in split mapping", "RefUrl": "/notes/1272512 "}, {"RefNumber": "1277359", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: \"Zero Object\" error in sender queue", "RefUrl": "/notes/1277359 "}, {"RefNumber": "1260178", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "Status shows a red \"LED\" after activation of indexing", "RefUrl": "/notes/1260178 "}, {"RefNumber": "1272854", "RefComponent": "BC-XI-IS", "RefTitle": "XI runtime: Synchronous message with the same ID", "RefUrl": "/notes/1272854 "}, {"RefNumber": "1265459", "RefComponent": "BC-XI-IS-WKB", "RefTitle": "XI Alerting: Changing alert rules possible w/o authorization", "RefUrl": "/notes/1265459 "}, {"RefNumber": "1268238", "RefComponent": "BC-XI-CON-ABA-IDO", "RefTitle": "IDoc adapter: Duplicate segment fields in XML", "RefUrl": "/notes/1268238 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_XITOOL", "From": "7.00", "To": "7.00", "Subsequent": ""}, {"SoftwareComponent": "SAP_XIAF", "From": "7.00", "To": "7.00", "Subsequent": ""}, {"SoftwareComponent": "SAP_XIPCK", "From": "7.00", "To": "7.00", "Subsequent": ""}, {"SoftwareComponent": "SAP-XIAFC", "From": "7.00", "To": "7.00", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}