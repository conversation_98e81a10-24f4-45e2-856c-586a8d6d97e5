{"Request": {"Number": "36326", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 457, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014394852017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000036326?language=E&token=31FD91B2F92ADEF4746229D9FF7B9203"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000036326", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000036326/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "36326"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "25.08.2005"}, "SAPComponentKey": {"_label": "Component", "value": "CO"}, "SAPComponentKeyText": {"_label": "Component", "value": "Controlling"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Controlling", "value": "CO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "36326 - Enhancement of KKRAMERK (3.0/4.0 orders)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You want to add further master data fields to the KKRAMERK structure in order to use them to define reference characteristics that you want to add to the class SAP_KKR_CLASS.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Order summarization, OKQ3</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This note applies only to the area of order classification in Releases 3.0/3.1/4.0. For higher releases read Note 339863. In those releases you should do without the order classification as far as possible. However, in exceptional cases you can continue to use this modification.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Caution:<br />--------<br />If this modification has been implemented and a reference characteristic has already been created with reference to the new field, you can only return to the standard SAP System</p> <UL><LI>after a release containing the new field in the standard SAP System has been imported,</LI></UL> <UL><LI>or after the reference characteristic has been deleted from classification</LI></UL> <UL><LI>or after the modification specified below has been made again.</LI></UL> <p>--------------------------------------------------------------<br />Procedure for the modification<br />------------------------------<br /><br />The KKRAMERK structure contains a substructure KKRMKOR, which is contained in every field for each order, and for which reference characteristics are permitted.<br />Add the new field to this structure, taking into account that you may only copy fields from one of the following master records:<br /></p> <UL><UL><LI>AUFK</LI></UL></UL> <UL><UL><LI>AFKO</LI></UL></UL> <UL><UL><LI>AFPO</LI></UL></UL> <UL><UL><LI>MARA</LI></UL></UL> <p><br />In addition, no fields of the data type CURR or CUKY may be used.<br /><br />You must also adapt an interface program that transfers the data from CO into the classification system.<br /><br /></p> <OL>1. DDIC change to the KKRAMERK structure</OL> <p><br />Choose Transaction SE11:</p> <UL><LI>Object name KKRMKOR</LI></UL> <UL><LI>Activate the \"Structures\" radio button</LI></UL> <UL><LI>Press the \"Change\" button.</LI></UL> <p><br />When a list of all fields of the structure KKRMKOR appears, carry out the following menu steps:<br />-&gt; Extras -&gt; Copy fields...<br /><br />When a popup appears you must enter one of the above-mentioned master record files (for example: \"AFKO\"). Then press the \"Field selection\" button.<br /><br />A list of all fields of the specific master record file appears. Position the cursor on the field to be copied (for example, PLNNR). Then press the \"Choose\" button. The specific field appears in color. Then press the \"Copy\" pushbutton.<br /><br />You have returned to the overview of all fields of the KKRMKOR structure. Position the list at the end and press the \"New fields\" button. Position the cursor on the first empty field at the end of the list and then carry out the following menu steps:<br />-&gt; Edit -&gt; Paste<br /><br />Now the selected field (AFKO-PLNNR in the example) is added at the end of the KKRMKOR structure.<br /><br />Important:<br />----------<br /><br />The new field in the KKRMKOR structure must start with the prefix \"OR\" followed by the original field name of the original table. Thus, the new field in our example must be called \"ORPLNNR\".<br />Change the name of the copied field by adding the prefix \"OR\" to the beginning of it.<br /><br />Finally, save and activate the KKRMKOR structure.<br /><br />If, due to the prefix, the name is now longer than allowed, leave out the last one or two characters. This, however, has the effect that recalculation from within Transaction OKQ3 is no longer possible. In this case you can execute subsequent classification of orders using report RKOCLASS.<br /></p> <OL>2. Adapting the interface program</OL> <p><br />In Member LKKRFF01, the following routine must be adapted according to which master record file the new field was copied from.<br /><br /><br />Interface routines in the member LKKRFF01: <br />&#x00A0;&#x00A0;Routine&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;Master record file<BR/> ---------------------|----------------------<BR/> &#x00A0;&#x00A0;FILL_KKRAMERK_AUFK |&#x00A0;&#x00A0;&#x00A0;&#x00A0;AUFK<BR/> &#x00A0;&#x00A0;FILL_KKRAMERK_AFKO |&#x00A0;&#x00A0;&#x00A0;&#x00A0;AFKO<BR/> &#x00A0;&#x00A0;FILL_KKRAMERK_AFPO |&#x00A0;&#x00A0;&#x00A0;&#x00A0;AFPO<BR/> &#x00A0;&#x00A0;FILL_KKRAMERK_MARA |&#x00A0;&#x00A0;&#x00A0;&#x00A0;MARA<BR/> <br /><br />Example:<br />---------<br />In the following case, the new field AFKO-XYZ is included in the KKRMKOR structure. Here, the new field has the name \"ORXYZ\" and the FORM routine FILL_KKRAMERK_FROM_AFKO must be modified as follows:<br /> FORM FILL_KKRAMERK_FROM_AFKO USING<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; IAFKO&#x00A0;&#x00A0;&#x00A0;&#x00A0; STRUCTURE AFKO<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; IKKRAMERK STRUCTURE KKRAMERK.<BR/> ...<BR/> &#x00A0;&#x00A0;IKKRAMERK-ORXYZ&#x00A0;&#x00A0;&#x00A0;&#x00A0;= IAFKO-XYZ.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; &lt;== NEW<BR/> ENDFORM.<br /></p> <OL>3. Generating the corresponding reference characteristic</OL> <p><br />After you complete steps 1. and 2., the new field appears in the list of the reference characteristics to be generated using Transaction OKQ3. By means of this transaction, you can generate the new reference characteristic and start the corresponding recalculation run.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D002147)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D023370)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000036326/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000036326/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000036326/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000036326/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000036326/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000036326/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000036326/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000036326/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000036326/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "52631", "RefComponent": "PS-ST", "RefTitle": "Enhancing KKRAMERK (Ref. Characteristics Projects)", "RefUrl": "/notes/52631"}, {"RefNumber": "44354", "RefComponent": "PS-ST-TMP", "RefTitle": "Including proj. definition in summarization struct.", "RefUrl": "/notes/44354"}, {"RefNumber": "339863", "RefComponent": "CO-PC-IS", "RefTitle": "General object summarization as of Release 4.5A", "RefUrl": "/notes/339863"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "849413", "RefComponent": "CO-PC-IS", "RefTitle": "Classification: Revaluation of WBS element characteristic", "RefUrl": "/notes/849413 "}, {"RefNumber": "339863", "RefComponent": "CO-PC-IS", "RefTitle": "General object summarization as of Release 4.5A", "RefUrl": "/notes/339863 "}, {"RefNumber": "52631", "RefComponent": "PS-ST", "RefTitle": "Enhancing KKRAMERK (Ref. Characteristics Projects)", "RefUrl": "/notes/52631 "}, {"RefNumber": "44354", "RefComponent": "PS-ST-TMP", "RefTitle": "Including proj. definition in summarization struct.", "RefUrl": "/notes/44354 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30A", "To": "31I", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}