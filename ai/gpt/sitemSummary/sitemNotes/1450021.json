{"Request": {"Number": "1450021", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 517, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016997662017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001450021?language=E&token=E7482CD1EA93F1100BBD228A2267DC8E"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001450021", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001450021/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1450021"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.06.2010"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PART-ISHMED"}, "SAPComponentKeyText": {"_label": "Component", "value": "Clinical System i.s.h.med"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Partner solutions", "value": "XX-PART", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Clinical System i.s.h.med", "value": "XX-PART-ISHMED", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART-ISHMED*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1450021 - Medication: Various Corrections and Technical Adaptations"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note contains various error corrections or technical adaptations in Medication</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>This is due to a program error.<br /><br />Before you import this note, you have to have already imported Notes 1280874, 1302988, 1306514, 1334411, 1345069, 1365394 1377745, 1394387, 1401222, 1406523, 1423313, 1428914, 1429456, 1434314, 1441365 and 1451918.<br />Note 1428914 contains supplements on the transport of variants.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>If you are using the Medication module, you must unpack the following files for Version 6.04 of i.s.h.med/IS-H and import them into your system in the following order:</p> <UL><LI>HW1450021_604_1.zip</LI></UL> <UL><LI>HW1450021_604_2.zip</LI></UL> <UL><LI>HW1450021_604_3.zip</LI></UL> <UL><LI>HW1450021_604_4.zip</LI></UL> <p><br />Note that you cannot download the attached file from the Customer Service System. You can only download it from SAP Service Marketplace (see also Note 480180 and Note 13719 for information on importing attachments).<br /><br />These attachments only contain coding and must not be imported on multiple clients.<br />You have to individually import these attachments in the order specified. If errors occur in an attachment you must first fix the errors so that the attachment reaches the target system free of errors. You can only then import the next attachment.<br /><br />If you do not import this note, the corrections or technical adaptations of the above files are automatically provided in SAP ERP 6.0, enhancement package 4, support package 11.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-PART-ISHMED-ORD (Service Management i.s.h.med)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5056012)"}, {"Key": "Processor                                                                                           ", "Value": "C5091415"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001450021/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001450021/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001450021/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001450021/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001450021/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001450021/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001450021/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001450021/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001450021/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "HW1450021_604_1.zip", "FileSize": "669", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000046132010&iv_version=0004&iv_guid=19C9323E58268242B8AB3D402334ACDA"}, {"FileName": "HW1450021_604_3.zip", "FileSize": "801", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000046132010&iv_version=0004&iv_guid=5BE65B22A6F36A4594046FDC9571330E"}, {"FileName": "HW1450021_604_4.zip", "FileSize": "394", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000046132010&iv_version=0004&iv_guid=AAA5EF4F5404394E9CF96BC5FA7F0243"}, {"FileName": "HW1450021_604_2.zip", "FileSize": "748", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000046132010&iv_version=0004&iv_guid=9DE2BFF6EA155A46A2FECB7487E24D13"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1598018", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1598018"}, {"RefNumber": "1585174", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Emergency Event - Various Corrections", "RefUrl": "/notes/1585174"}, {"RefNumber": "1583139", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: PRN Medication - Various Corrections", "RefUrl": "/notes/1583139"}, {"RefNumber": "1575994", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Medication Order - Determine Case Number", "RefUrl": "/notes/1575994"}, {"RefNumber": "1574650", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Order - Presetting OU Parameter N1ME_DEFDU", "RefUrl": "/notes/1574650"}, {"RefNumber": "1573058", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1573058"}, {"RefNumber": "1572295", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Drug Master - Incorrect Currency for Price", "RefUrl": "/notes/1572295"}, {"RefNumber": "1572187", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Order - Error with Input in Frequency Field", "RefUrl": "/notes/1572187"}, {"RefNumber": "1569196", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: <PERSON> - <PERSON><PERSON><PERSON>", "RefUrl": "/notes/1569196"}, {"RefNumber": "1566092", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Drug Search and Suspend Order", "RefUrl": "/notes/1566092"}, {"RefNumber": "1565897", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Order - Display Infusion Details in DWS", "RefUrl": "/notes/1565897"}, {"RefNumber": "1564976", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Administration - <PERSON> <PERSON><PERSON> not Displayed", "RefUrl": "/notes/1564976"}, {"RefNumber": "1564472", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Drug Master - Saving Incorrect Entries", "RefUrl": "/notes/1564472"}, {"RefNumber": "1563762", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Medication Events View - Performance", "RefUrl": "/notes/1563762"}, {"RefNumber": "1562029", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Order Activities - Report RN1ME_OAPROC", "RefUrl": "/notes/1562029"}, {"RefNumber": "1561128", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Administration of Compounds - Various Errors", "RefUrl": "/notes/1561128"}, {"RefNumber": "1560885", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Immediate Dose - Various Corrections", "RefUrl": "/notes/1560885"}, {"RefNumber": "1558739", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Master Data - Import of Combination Preparations", "RefUrl": "/notes/1558739"}, {"RefNumber": "1557751", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Med. Order - Incorrect Entries Can Be Saved", "RefUrl": "/notes/1557751"}, {"RefNumber": "1557640", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Drug Search - \"Description\" Column Missing", "RefUrl": "/notes/1557640"}, {"RefNumber": "1555460", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: PRN Medication Order - Immediate Event", "RefUrl": "/notes/1555460"}, {"RefNumber": "1550480", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Order - \"Issue Quantity Proposal\" Function", "RefUrl": "/notes/1550480"}, {"RefNumber": "1550095", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: User-Defined Presettings", "RefUrl": "/notes/1550095"}, {"RefNumber": "1549475", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Order Templates - Flow Rates Dose Calculator", "RefUrl": "/notes/1549475"}, {"RefNumber": "1549420", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Event Generation - OU Outpatient Movement", "RefUrl": "/notes/1549420"}, {"RefNumber": "1549223", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: User Favorites - Add Drug", "RefUrl": "/notes/1549223"}, {"RefNumber": "1549154", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Drug Search - Double-Click in MO Templates", "RefUrl": "/notes/1549154"}, {"RefNumber": "1549035", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Description Missing in Ingredients", "RefUrl": "/notes/1549035"}, {"RefNumber": "1549020", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Incorrect Presetting of Administration Row", "RefUrl": "/notes/1549020"}, {"RefNumber": "1549016", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Process Order Activities - RN1ME_OAPROC", "RefUrl": "/notes/1549016"}, {"RefNumber": "1547786", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Master Data and Administration - Runtime Error", "RefUrl": "/notes/1547786"}, {"RefNumber": "1547694", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Administration - Reasons for Differences Missing", "RefUrl": "/notes/1547694"}, {"RefNumber": "1542754", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Drug Search - Criteria for Template Search", "RefUrl": "/notes/1542754"}, {"RefNumber": "1542392", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Administration and Chart - Various Errors", "RefUrl": "/notes/1542392"}, {"RefNumber": "1541817", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: User-Spec. Presettings - Authorizations", "RefUrl": "/notes/1541817"}, {"RefNumber": "1540864", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Order - Frequent Regeneration of Events", "RefUrl": "/notes/1540864"}, {"RefNumber": "1540522", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Medication Order - Error Message in Chart", "RefUrl": "/notes/1540522"}, {"RefNumber": "1540426", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Order Details - Hide Chart", "RefUrl": "/notes/1540426"}, {"RefNumber": "1539931", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Rounding Errors Due to Too Few Decimal Places", "RefUrl": "/notes/1539931"}, {"RefNumber": "1539923", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Event Generation After Transfer - Status", "RefUrl": "/notes/1539923"}, {"RefNumber": "1538309", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Drug Search - Favorites in Search Results", "RefUrl": "/notes/1538309"}, {"RefNumber": "1538152", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Admin. - Integration of Cust.-Spec. Screens", "RefUrl": "/notes/1538152"}, {"RefNumber": "1538072", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Pick/Fill List - Calculation Error", "RefUrl": "/notes/1538072"}, {"RefNumber": "1537628", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Change Transfer: Error During Regeneration of Events", "RefUrl": "/notes/1537628"}, {"RefNumber": "1537626", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: End Event - Error when Ending Infusions", "RefUrl": "/notes/1537626"}, {"RefNumber": "1536320", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Generation of Events - Runtime Error", "RefUrl": "/notes/1536320"}, {"RefNumber": "1535793", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Medication Order - Runtime Error in Chart", "RefUrl": "/notes/1535793"}, {"RefNumber": "1535695", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Order - Calculate Duration of Flow Rate", "RefUrl": "/notes/1535695"}, {"RefNumber": "1534205", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Create and Administer Infusion", "RefUrl": "/notes/1534205"}, {"RefNumber": "1533693", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Error in Scrap Calculation During Administration", "RefUrl": "/notes/1533693"}, {"RefNumber": "1533025", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Administration - Error when Calculating Scrap", "RefUrl": "/notes/1533025"}, {"RefNumber": "1532998", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Chart Display - Min/Max Rate Without Duration", "RefUrl": "/notes/1532998"}, {"RefNumber": "1532799", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Check Condition for Mixed Drugs", "RefUrl": "/notes/1532799"}, {"RefNumber": "1530704", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Dose Calculators - Various Error Corrections", "RefUrl": "/notes/1530704"}, {"RefNumber": "1529466", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Copy Orders - Connections Missing", "RefUrl": "/notes/1529466"}, {"RefNumber": "1529400", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Infusions - Reason for Dose Difference", "RefUrl": "/notes/1529400"}, {"RefNumber": "1529147", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Flow Rate Calculation - Rounding Up Error", "RefUrl": "/notes/1529147"}, {"RefNumber": "1527950", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Mixed Drug - Time Difference", "RefUrl": "/notes/1527950"}, {"RefNumber": "1526940", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: PCA Buttons Without Function when Administrating", "RefUrl": "/notes/1526940"}, {"RefNumber": "1526839", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: User-Defined Settings - Error", "RefUrl": "/notes/1526839"}, {"RefNumber": "1526566", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Generation for Time 00:00 Incorrect", "RefUrl": "/notes/1526566"}, {"RefNumber": "1526461", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Pick and Fill List - Issue Quantity", "RefUrl": "/notes/1526461"}, {"RefNumber": "1525643", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Error During Import of Drugs", "RefUrl": "/notes/1525643"}, {"RefNumber": "1524078", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Drug Search; Template Texts Shortened", "RefUrl": "/notes/1524078"}, {"RefNumber": "1523765", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Template Group Search - Error in Input Help", "RefUrl": "/notes/1523765"}, {"RefNumber": "1518961", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Error when Creating Search Index for Drugs", "RefUrl": "/notes/1518961"}, {"RefNumber": "1518342", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Events View: Nursing Service Display Error", "RefUrl": "/notes/1518342"}, {"RefNumber": "1517612", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Witness <PERSON><PERSON><PERSON><PERSON> not Correctly Taken into Acc.", "RefUrl": "/notes/1517612"}, {"RefNumber": "1517126", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Indiscretely Dosed Drug, Duration Incorrect", "RefUrl": "/notes/1517126"}, {"RefNumber": "1516273", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: <PERSON><PERSON>. Work Station; Selection not Correct", "RefUrl": "/notes/1516273"}, {"RefNumber": "1515995", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: <PERSON><PERSON><PERSON> After Resetting Defaults", "RefUrl": "/notes/1515995"}, {"RefNumber": "1515757", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Error with Different Data Format in Events", "RefUrl": "/notes/1515757"}, {"RefNumber": "1514900", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: C<PERSON> <PERSON><PERSON><PERSON><PERSON>; Search Takes Long Time", "RefUrl": "/notes/1514900"}, {"RefNumber": "1514745", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: <PERSON><PERSON> Cal<PERSON>tor - Dosage Using Overall Dose", "RefUrl": "/notes/1514745"}, {"RefNumber": "1514270", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Witness Deleted in Emergency Events", "RefUrl": "/notes/1514270"}, {"RefNumber": "1511654", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Error in Drug Selection of Master Data", "RefUrl": "/notes/1511654"}, {"RefNumber": "1510628", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Resume Order - Incorrect Duration", "RefUrl": "/notes/1510628"}, {"RefNumber": "1509348", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: <PERSON><PERSON><PERSON> Dosage, Cycle Saved Incorrectly", "RefUrl": "/notes/1509348"}, {"RefNumber": "1509142", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Error in Event Generation Dispensing Order", "RefUrl": "/notes/1509142"}, {"RefNumber": "1509117", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Authorization Check Administration Documentation", "RefUrl": "/notes/1509117"}, {"RefNumber": "1508868", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Order, Error in Sliding Dosage", "RefUrl": "/notes/1508868"}, {"RefNumber": "1508813", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Incorrect Overall Dose with Bolus Admin. IV", "RefUrl": "/notes/1508813"}, {"RefNumber": "1490187", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1490187"}, {"RefNumber": "1489348", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Order - Dose Information not Always Correct", "RefUrl": "/notes/1489348"}, {"RefNumber": "1482043", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: <PERSON><PERSON> in Pick List", "RefUrl": "/notes/1482043"}, {"RefNumber": "1481975", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Determining OU for MO for Emergency Admin.", "RefUrl": "/notes/1481975"}, {"RefNumber": "1479608", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Drug Master - Error in Long Text Editor", "RefUrl": "/notes/1479608"}, {"RefNumber": "1479280", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Determine Purpose by OU", "RefUrl": "/notes/1479280"}, {"RefNumber": "1479060", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Do<PERSON> Definition with <PERSON><PERSON><PERSON> <PERSON><PERSON>", "RefUrl": "/notes/1479060"}, {"RefNumber": "1478934", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Duplicate Preparations in Emergency Orders", "RefUrl": "/notes/1478934"}, {"RefNumber": "1471865", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Drug Search - Masking Character", "RefUrl": "/notes/1471865"}, {"RefNumber": "1471109", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1471109"}, {"RefNumber": "1470540", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Event Generation - Runtime Error", "RefUrl": "/notes/1470540"}, {"RefNumber": "1469826", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: <PERSON>or<PERSON><PERSON> for \"Execute\" Function", "RefUrl": "/notes/1469826"}, {"RefNumber": "1467956", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Alternative Dose Unit - Incorrect Dosage", "RefUrl": "/notes/1467956"}, {"RefNumber": "1463841", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Drug Search - Selection Criteria", "RefUrl": "/notes/1463841"}, {"RefNumber": "1462926", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Search Result Grouping Classification Code", "RefUrl": "/notes/1462926"}, {"RefNumber": "1460368", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1460368"}, {"RefNumber": "1454921", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1454921"}, {"RefNumber": "1451918", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clinical Work Station: Forward Patient Context", "RefUrl": "/notes/1451918"}, {"RefNumber": "1441365", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1441365"}, {"RefNumber": "1434314", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1434314"}, {"RefNumber": "1429456", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1429456"}, {"RefNumber": "1428914", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Documentation Work Station: Transport Variants f. Base Items", "RefUrl": "/notes/1428914"}, {"RefNumber": "1423313", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1423313"}, {"RefNumber": "1406523", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1406523"}, {"RefNumber": "1401222", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1401222"}, {"RefNumber": "1394387", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1394387"}, {"RefNumber": "1377745", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1377745"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1365394", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1365394"}, {"RefNumber": "1345069", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1345069"}, {"RefNumber": "1306514", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1306514"}, {"RefNumber": "1304411", "RefComponent": "PY-DE-NT-NI", "RefTitle": "Fehlende Prüfkennzeichen für Berechtigungsobjekte", "RefUrl": "/notes/1304411"}, {"RefNumber": "1302988", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1302988"}, {"RefNumber": "1280874", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1280874"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1515995", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: <PERSON><PERSON><PERSON> After Resetting Defaults", "RefUrl": "/notes/1515995 "}, {"RefNumber": "1598018", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1598018 "}, {"RefNumber": "1573058", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1573058 "}, {"RefNumber": "1585174", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Emergency Event - Various Corrections", "RefUrl": "/notes/1585174 "}, {"RefNumber": "1583139", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: PRN Medication - Various Corrections", "RefUrl": "/notes/1583139 "}, {"RefNumber": "1575994", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Medication Order - Determine Case Number", "RefUrl": "/notes/1575994 "}, {"RefNumber": "1549020", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Incorrect Presetting of Administration Row", "RefUrl": "/notes/1549020 "}, {"RefNumber": "1549475", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Order Templates - Flow Rates Dose Calculator", "RefUrl": "/notes/1549475 "}, {"RefNumber": "1562029", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Order Activities - Report RN1ME_OAPROC", "RefUrl": "/notes/1562029 "}, {"RefNumber": "1566092", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Drug Search and Suspend Order", "RefUrl": "/notes/1566092 "}, {"RefNumber": "1572295", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Drug Master - Incorrect Currency for Price", "RefUrl": "/notes/1572295 "}, {"RefNumber": "1574650", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Order - Presetting OU Parameter N1ME_DEFDU", "RefUrl": "/notes/1574650 "}, {"RefNumber": "1572187", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Order - Error with Input in Frequency Field", "RefUrl": "/notes/1572187 "}, {"RefNumber": "1569196", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: <PERSON> - <PERSON><PERSON><PERSON>", "RefUrl": "/notes/1569196 "}, {"RefNumber": "1558739", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Master Data - Import of Combination Preparations", "RefUrl": "/notes/1558739 "}, {"RefNumber": "1550480", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Order - \"Issue Quantity Proposal\" Function", "RefUrl": "/notes/1550480 "}, {"RefNumber": "1542754", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Drug Search - Criteria for Template Search", "RefUrl": "/notes/1542754 "}, {"RefNumber": "1565897", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Order - Display Infusion Details in DWS", "RefUrl": "/notes/1565897 "}, {"RefNumber": "1561128", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Administration of Compounds - Various Errors", "RefUrl": "/notes/1561128 "}, {"RefNumber": "1564472", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Drug Master - Saving Incorrect Entries", "RefUrl": "/notes/1564472 "}, {"RefNumber": "1560885", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Immediate Dose - Various Corrections", "RefUrl": "/notes/1560885 "}, {"RefNumber": "1564976", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Administration - <PERSON> <PERSON><PERSON> not Displayed", "RefUrl": "/notes/1564976 "}, {"RefNumber": "1557751", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Med. Order - Incorrect Entries Can Be Saved", "RefUrl": "/notes/1557751 "}, {"RefNumber": "1563762", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Medication Events View - Performance", "RefUrl": "/notes/1563762 "}, {"RefNumber": "1533025", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Administration - Error when Calculating Scrap", "RefUrl": "/notes/1533025 "}, {"RefNumber": "1555460", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: PRN Medication Order - Immediate Event", "RefUrl": "/notes/1555460 "}, {"RefNumber": "1533693", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Error in Scrap Calculation During Administration", "RefUrl": "/notes/1533693 "}, {"RefNumber": "1557640", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Drug Search - \"Description\" Column Missing", "RefUrl": "/notes/1557640 "}, {"RefNumber": "1523765", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Template Group Search - Error in Input Help", "RefUrl": "/notes/1523765 "}, {"RefNumber": "1549154", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Drug Search - Double-Click in MO Templates", "RefUrl": "/notes/1549154 "}, {"RefNumber": "1550095", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: User-Defined Presettings", "RefUrl": "/notes/1550095 "}, {"RefNumber": "1549016", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Process Order Activities - RN1ME_OAPROC", "RefUrl": "/notes/1549016 "}, {"RefNumber": "1547694", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Administration - Reasons for Differences Missing", "RefUrl": "/notes/1547694 "}, {"RefNumber": "1549035", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Description Missing in Ingredients", "RefUrl": "/notes/1549035 "}, {"RefNumber": "1540426", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Order Details - Hide Chart", "RefUrl": "/notes/1540426 "}, {"RefNumber": "1549420", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Event Generation - OU Outpatient Movement", "RefUrl": "/notes/1549420 "}, {"RefNumber": "1549223", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: User Favorites - Add Drug", "RefUrl": "/notes/1549223 "}, {"RefNumber": "1547786", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Master Data and Administration - Runtime Error", "RefUrl": "/notes/1547786 "}, {"RefNumber": "1542392", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Administration and Chart - Various Errors", "RefUrl": "/notes/1542392 "}, {"RefNumber": "1541817", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: User-Spec. Presettings - Authorizations", "RefUrl": "/notes/1541817 "}, {"RefNumber": "1540522", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Medication Order - Error Message in Chart", "RefUrl": "/notes/1540522 "}, {"RefNumber": "1540864", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Order - Frequent Regeneration of Events", "RefUrl": "/notes/1540864 "}, {"RefNumber": "1539923", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Event Generation After Transfer - Status", "RefUrl": "/notes/1539923 "}, {"RefNumber": "1538152", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Admin. - Integration of Cust.-Spec. Screens", "RefUrl": "/notes/1538152 "}, {"RefNumber": "1538072", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Pick/Fill List - Calculation Error", "RefUrl": "/notes/1538072 "}, {"RefNumber": "1535695", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Order - Calculate Duration of Flow Rate", "RefUrl": "/notes/1535695 "}, {"RefNumber": "1537626", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: End Event - Error when Ending Infusions", "RefUrl": "/notes/1537626 "}, {"RefNumber": "1539931", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Rounding Errors Due to Too Few Decimal Places", "RefUrl": "/notes/1539931 "}, {"RefNumber": "1535793", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Medication Order - Runtime Error in Chart", "RefUrl": "/notes/1535793 "}, {"RefNumber": "1536320", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Generation of Events - Runtime Error", "RefUrl": "/notes/1536320 "}, {"RefNumber": "1537628", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Change Transfer: Error During Regeneration of Events", "RefUrl": "/notes/1537628 "}, {"RefNumber": "1538309", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Drug Search - Favorites in Search Results", "RefUrl": "/notes/1538309 "}, {"RefNumber": "1530704", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Dose Calculators - Various Error Corrections", "RefUrl": "/notes/1530704 "}, {"RefNumber": "1532799", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Check Condition for Mixed Drugs", "RefUrl": "/notes/1532799 "}, {"RefNumber": "1532998", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Chart Display - Min/Max Rate Without Duration", "RefUrl": "/notes/1532998 "}, {"RefNumber": "1534205", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Create and Administer Infusion", "RefUrl": "/notes/1534205 "}, {"RefNumber": "1529466", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Copy Orders - Connections Missing", "RefUrl": "/notes/1529466 "}, {"RefNumber": "1529400", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Infusions - Reason for Dose Difference", "RefUrl": "/notes/1529400 "}, {"RefNumber": "1529147", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Flow Rate Calculation - Rounding Up Error", "RefUrl": "/notes/1529147 "}, {"RefNumber": "1526839", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: User-Defined Settings - Error", "RefUrl": "/notes/1526839 "}, {"RefNumber": "1527950", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Mixed Drug - Time Difference", "RefUrl": "/notes/1527950 "}, {"RefNumber": "1524078", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Drug Search; Template Texts Shortened", "RefUrl": "/notes/1524078 "}, {"RefNumber": "1526566", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Generation for Time 00:00 Incorrect", "RefUrl": "/notes/1526566 "}, {"RefNumber": "1526461", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Pick and Fill List - Issue Quantity", "RefUrl": "/notes/1526461 "}, {"RefNumber": "1526940", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: PCA Buttons Without Function when Administrating", "RefUrl": "/notes/1526940 "}, {"RefNumber": "1518961", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Error when Creating Search Index for Drugs", "RefUrl": "/notes/1518961 "}, {"RefNumber": "1525643", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Error During Import of Drugs", "RefUrl": "/notes/1525643 "}, {"RefNumber": "1518342", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Events View: Nursing Service Display Error", "RefUrl": "/notes/1518342 "}, {"RefNumber": "1515757", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Error with Different Data Format in Events", "RefUrl": "/notes/1515757 "}, {"RefNumber": "1517612", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Witness <PERSON><PERSON><PERSON><PERSON> not Correctly Taken into Acc.", "RefUrl": "/notes/1517612 "}, {"RefNumber": "1514745", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: <PERSON><PERSON> Cal<PERSON>tor - Dosage Using Overall Dose", "RefUrl": "/notes/1514745 "}, {"RefNumber": "1517126", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Indiscretely Dosed Drug, Duration Incorrect", "RefUrl": "/notes/1517126 "}, {"RefNumber": "1516273", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: <PERSON><PERSON>. Work Station; Selection not Correct", "RefUrl": "/notes/1516273 "}, {"RefNumber": "1509348", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: <PERSON><PERSON><PERSON> Dosage, Cycle Saved Incorrectly", "RefUrl": "/notes/1509348 "}, {"RefNumber": "1509142", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Error in Event Generation Dispensing Order", "RefUrl": "/notes/1509142 "}, {"RefNumber": "1508868", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Order, Error in Sliding Dosage", "RefUrl": "/notes/1508868 "}, {"RefNumber": "1508813", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Incorrect Overall Dose with Bolus Admin. IV", "RefUrl": "/notes/1508813 "}, {"RefNumber": "1510628", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Resume Order - Incorrect Duration", "RefUrl": "/notes/1510628 "}, {"RefNumber": "1511654", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Error in Drug Selection of Master Data", "RefUrl": "/notes/1511654 "}, {"RefNumber": "1514900", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: C<PERSON> <PERSON><PERSON><PERSON><PERSON>; Search Takes Long Time", "RefUrl": "/notes/1514900 "}, {"RefNumber": "1514270", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Witness Deleted in Emergency Events", "RefUrl": "/notes/1514270 "}, {"RefNumber": "1509117", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Authorization Check Administration Documentation", "RefUrl": "/notes/1509117 "}, {"RefNumber": "1490187", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1490187 "}, {"RefNumber": "1471109", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1471109 "}, {"RefNumber": "1489348", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Order - Dose Information not Always Correct", "RefUrl": "/notes/1489348 "}, {"RefNumber": "1471865", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Drug Search - Masking Character", "RefUrl": "/notes/1471865 "}, {"RefNumber": "1470540", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Event Generation - Runtime Error", "RefUrl": "/notes/1470540 "}, {"RefNumber": "1469826", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: <PERSON>or<PERSON><PERSON> for \"Execute\" Function", "RefUrl": "/notes/1469826 "}, {"RefNumber": "1467956", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Alternative Dose Unit - Incorrect Dosage", "RefUrl": "/notes/1467956 "}, {"RefNumber": "1460368", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1460368 "}, {"RefNumber": "1462926", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Search Result Grouping Classification Code", "RefUrl": "/notes/1462926 "}, {"RefNumber": "1463841", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Drug Search - Selection Criteria", "RefUrl": "/notes/1463841 "}, {"RefNumber": "1454921", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1454921 "}, {"RefNumber": "1481975", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Determining OU for MO for Emergency Admin.", "RefUrl": "/notes/1481975 "}, {"RefNumber": "1482043", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: <PERSON><PERSON> in Pick List", "RefUrl": "/notes/1482043 "}, {"RefNumber": "1441365", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1441365 "}, {"RefNumber": "1478934", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Duplicate Preparations in Emergency Orders", "RefUrl": "/notes/1478934 "}, {"RefNumber": "1479060", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Do<PERSON> Definition with <PERSON><PERSON><PERSON> <PERSON><PERSON>", "RefUrl": "/notes/1479060 "}, {"RefNumber": "1479280", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Determine Purpose by OU", "RefUrl": "/notes/1479280 "}, {"RefNumber": "1479608", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Medication: Drug Master - Error in Long Text Editor", "RefUrl": "/notes/1479608 "}, {"RefNumber": "1451918", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clinical Work Station: Forward Patient Context", "RefUrl": "/notes/1451918 "}, {"RefNumber": "1434314", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1434314 "}, {"RefNumber": "1429456", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1429456 "}, {"RefNumber": "1428914", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Documentation Work Station: Transport Variants f. Base Items", "RefUrl": "/notes/1428914 "}, {"RefNumber": "1423313", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1423313 "}, {"RefNumber": "1406523", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1406523 "}, {"RefNumber": "1401222", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1401222 "}, {"RefNumber": "1377745", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1377745 "}, {"RefNumber": "1394387", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1394387 "}, {"RefNumber": "1365394", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1365394 "}, {"RefNumber": "1345069", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1345069 "}, {"RefNumber": "1306514", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1306514 "}, {"RefNumber": "1302988", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1302988 "}, {"RefNumber": "1304411", "RefComponent": "PY-DE-NT-NI", "RefTitle": "Fehlende Prüfkennzeichen für Berechtigungsobjekte", "RefUrl": "/notes/1304411 "}, {"RefNumber": "1280874", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1280874 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-H", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "IS-H 604", "SupportPackage": "SAPK-60411INISH", "URL": "/supportpackage/SAPK-60411INISH"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}