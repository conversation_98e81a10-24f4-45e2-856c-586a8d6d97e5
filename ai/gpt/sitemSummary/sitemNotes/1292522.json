{"Request": {"Number": "1292522", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 783, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000007593582017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001292522?language=E&token=BF816E673A39DD559372D08C68137042"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001292522", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001292522/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1292522"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 15}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.11.2009"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SER"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Support Services"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Support Services", "value": "SV-SMG-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1292522 - KPIs for Custom Repository Analysis missing in ST14 download"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>During a SAP service delivery of SAP Upgrade Assessment, SAP Solution Transition Assessment or SAP Data Volume Management service certain KPIs are missing in the ST14 download, especially in the area of Usage Statistics, Code Analysis and Customer Namespace Analysis.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>UA, STA, Upgrade Assessment, Solution Transition Assessment, DVM</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Program Error</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Before you implement the coding via SNOTE please make sure the ST-A/PI 01L* release is implemented in all systems involved in the transport  landscape. Please execute the report /SSF/SAO_UTILS in all those systems via transaction SE38 and flag the first option 'Uncomment/Recomment analyis coding for additional components'. This has to be done BEFORE the coding correction is being implemented. After the execution of the report please start the implementation of the attached coding via transaction SNOTE.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-BO (Backoffice Service Delivery)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D034574)"}, {"Key": "Processor                                                                                           ", "Value": "I048976"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001292522/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001292522/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001292522/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001292522/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001292522/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001292522/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001292522/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001292522/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001292522/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1353047", "RefComponent": "SV-SMG-SER", "RefTitle": "ST14: Solution Transition Assessment missing", "RefUrl": "/notes/1353047"}, {"RefNumber": "1274306", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Solution Transition Assessment - Preparation Note", "RefUrl": "/notes/1274306"}, {"RefNumber": "1159758", "RefComponent": "SV-SMG-DVM", "RefTitle": "Data Volume Management: Central Preparation Note", "RefUrl": "/notes/1159758"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1159758", "RefComponent": "SV-SMG-DVM", "RefTitle": "Data Volume Management: Central Preparation Note", "RefUrl": "/notes/1159758 "}, {"RefNumber": "1077981", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Upgrade Assessment Preparation Note", "RefUrl": "/notes/1077981 "}, {"RefNumber": "1610241", "RefComponent": "SV-SMG-DVM", "RefTitle": "DVM Service Preparaion Note", "RefUrl": "/notes/1610241 "}, {"RefNumber": "1274306", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Solution Transition Assessment - Preparation Note", "RefUrl": "/notes/1274306 "}, {"RefNumber": "1353047", "RefComponent": "SV-SMG-SER", "RefTitle": "ST14: Solution Transition Assessment missing", "RefUrl": "/notes/1353047 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST-A/PI", "From": "01L_BCO46C", "To": "01L_BCO46C", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01L_BCO46D", "To": "01L_BCO46D", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01L_BCO610", "To": "01L_BCO610", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01L_BCO620", "To": "01L_BCO620", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01L_BCO640", "To": "01L_BCO640", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01L_R3_46C", "To": "01L_R3_46C", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01L_R3_470", "To": "01L_R3_470", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01L_SCM400", "To": "01L_SCM400", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01L_CRM300", "To": "01L_CRM300", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01L_CRM400", "To": "01L_CRM400", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01L_SCM410", "To": "01L_SCM410", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01L_ECC500", "To": "01L_ECC500", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01L_BCO700", "To": "01L_BCO700", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01L_ECC600", "To": "01L_ECC600", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01L_SCM570", "To": "01L_SCM570", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01L_BCO710", "To": "01L_BCO710", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01L_CRM570", "To": "01L_CRM570", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "ST-A/PI", "NumberOfCorrin": 4, "URL": "/corrins/0001292522/389"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}