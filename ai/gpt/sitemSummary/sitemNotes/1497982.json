{"Request": {"Number": "1497982", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 512, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000008866922017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001497982?language=E&token=6A7706F4218A20D497E02114F249B649"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001497982", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001497982/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1497982"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 14}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.09.2012"}, "SAPComponentKey": {"_label": "Component", "value": "PY-BR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Brazil"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Brazil", "value": "PY-BR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-BR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1497982 - Ordinance 1620: Homolognet"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Ordinance 1620 was published in July 14, 2010 to regulate the legislation concerning the Work Contract Termination Term and Terms of Approval.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Homolognet; legal change; termination approval; Brazil; ordinance 1620<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You have installed the support package listed below for your release:</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Software component&#x00A0;&#x00A0; Release&#x00A0;&#x00A0;Package</TH></TR> <TR><TD>SAP_HR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;604&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKE60428</TD></TR> <TR><TD>SAP_HR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;600&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKE60062</TD></TR> <TR><TD>SAP_HR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;500&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKE50079</TD></TR> <TR><TD>SAP_HR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;470&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKE470B3</TD></TR> </TABLE> <p><br />Alternatively, you have installed CLC-BR 2010-11. For more information, see SAP note 1515927.<br /><br />You have installed the following SAP notes:</p> <UL><LI>1528076 - HBRUTMS5: Viewing XML TemSe Files</LI></UL> <UL><LI>1512248 - Ordinance 1621: General Functionalities</LI></UL> <UL><LI>1537967 - HR Attributes: Only first attribute value returned</LI></UL> <UL><LI>1553891 - Homolognet-Termination Term: Customizing tables</LI></UL> <UL><LI>1564011 - PY-BR: New class to read the table T7BR596</LI></UL> <p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The solution created to comply with Ordinance 1620 consists in a new report, called <B>HomologNet</B> (HBRCHMN0). The report was created to generate the XML file described by Ordinance 1620 - HomologNet.<br /></p> <b><B>Installing the solution</B></b><br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To install the solution, proceed with the following steps: <OL><OL>a) Install the Advanced Delivery file that is attached to the note according to your release:</OL></OL> <p></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>Release_604.ZIP - Release 604 (ERP 6.0 EhP4)</TD></TR> <TR><TD>Release_600.ZIP - Release 600 (ERP 2005)</TD></TR> <TR><TD>Release_500.ZIP - Release 500 (ERP 2004)</TD></TR> <TR><TD>Release_470.ZIP - Release 470 (R/3 Enterprise)</TD></TR> <TR><TD></TD></TR> <TR><TD>For more information, see <B>Risk and Restrictions inherent in Transport Files</B> below.</TD></TR> </TABLE> <OL><OL>b) Install the correction instructions of the note.</OL></OL> <OL><OL>c) Execute the <B>Manual Steps</B> below.</OL></OL> <OL><OL>d) Customize your system according to the attached document \"Customizing_Guide.pdf\".</OL></OL> <OL><OL>e) If necessary, implement the HR_BR_TERM_HOMOLOGNET BAdI. For more information, see the attached document \"Customizing_Guide.pdf\" and SAP note 1512248.</OL></OL> <p></p> <b><B>Manual Steps</B></b><br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Update the text elements of the HBRCHMN0 report as following: <UL><LI>Text symbols:</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT><B>Sym</TH><TH ALIGN=LEFT> Text</B></TH></TR> <TR><TD>001</TD><TD> Entidade sindical patronal</TD></TR> <TR><TD>002</TD><TD> Processamento</TD></TR> </TABLE></UL> <p></p> <UL><LI>Selection texts:</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT><B>Name</TH><TH ALIGN=LEFT> Text</B></TH></TR> <TR><TD>CNPJSIND</TD><TD> CNPJ ent. sind./CNPJ conta EES</TD></TR> <TR><TD>CODESIND</TD><TD> C&#x00F3;digo sindical/n&#x00BA; conta EES</TD></TR> <TR><TD>P_LOG</TD><TD> Visualizar log</TD></TR> <TR><TD></TD></TR> </TABLE></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Add the following fields at the table T7BRT0: <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Field</TH><TH ALIGN=LEFT> Data element</TH></TR> <TR><TD>RSNHN</TD><TD> PBR_HMN_FIRE_REASON</TD></TR> <TR><TD>HMNTS</TD><TD> PBR_HMN_MOVTRANSF</TD></TR> <TR><TD></TD></TR> </TABLE> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Add the following fields at the table view V_T7BRT0: <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>View Field</TH><TH ALIGN=LEFT> Table</TH><TH ALIGN=LEFT> Field</TH></TR> <TR><TD>RSNHN</TD><TD> T7BRT0</TD><TD> RSNHN</TD></TR> <TR><TD>HMNTS</TD><TD> T7BRT0</TD><TD> HMNTS</TD></TR> <TR><TD></TD></TR> <TR><TD>After that, update the maintenance dialog of the table view in the SE54 transaction.</TD></TR> <TR><TD></TD></TR> </TABLE> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Add the following fields at the table T7BRAB: <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Field</TH><TH ALIGN=LEFT> Data element</TH><TH ALIGN=LEFT> Check table</TH></TR> <TR><TD>HMNTS</TD><TD> PBR_HMN_MOVEXIT</TD><TD> T7BRFM</TD></TR> <TR><TD>HMNTR</TD><TD> PBR_HMN_MOVRETURN</TD><TD> T7BRFM</TD></TR> <TR><TD></TD></TR> </TABLE> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Add the following fields at the table view V_T7BRAB: <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>View Field</TH><TH ALIGN=LEFT> Table</TH><TH ALIGN=LEFT> Field</TH></TR> <TR><TD>HMNTS</TD><TD> T7BRAB</TD><TD> HMNTS</TD></TR> <TR><TD>HMNTR</TD><TD> T7BRAB</TD><TD> HMNTR</TD></TR> <TR><TD></TD></TR> <TR><TD>After that, update the maintenance dialog of the table view in the SE54 transaction.</TD></TR> <TR><TD></TD></TR> </TABLE> <b></b><br /> <b><B>Risk and Restrictions inherent in Transport Files</B></b><br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you use a Transport (SAR) file instead of installing the appropriate Support Package or CLC Package, please note the following: <UL><LI>Read carefully SAP Note 1318389, where conditions and risks of using Transport files are explained in detail.</LI></UL> <UL><LI>There are no updates to Transport files when any objects in them are modified. Objects contained in Transport files may become obsolete without warning.</LI></UL> <UL><LI>Transport files are not valid once their content is available via Support Packages or CLC Packages. The changes may then be installed only via the Packages.</LI></UL> <UL><LI>Text objects are provided in the language in which they were created. Translation is available only via the Packages.</LI></UL> <UL><LI>Changes to the SAP Easy Access menu and Implementation Guide (IMG) are provided only via the Packages.</LI></UL> <p><br /><br />The objects mentioned below will be included in an HR Support Package, as indicated in item \"Reference to Support Packages\".<br /></p> <b><B>Objects created</B></b><br /> <UL><LI>Report</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;HBRCHMN0</p> <UL><LI>Transaction</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PC00_M37_HBRCHMN0</p> <UL><LI>Area Menu entry</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Pessoal -&gt; C&#x00E1;lculo das folhas de pagamento -&gt; Am&#x00E9;rica -&gt; Brasil -&gt; Atividades subsequentes -&gt; Independente do Per&#x00ED;odo -&gt; An&#x00E1;lise -&gt; Rescis&#x00E3;o -&gt; HomologNet</p> <UL><LI>Includes</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PCHMNDBR0, PCHMNFBR0, PCHMNSBR0</p> <UL><LI>Class</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CL_HR_BR_HOMOLOGNET</p> <UL><LI>Transformation</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PBR_HOMOLOGNET</p> <UL><LI>Messages at class HRPAYBR99</LI></UL> <UL><UL><LI>127: CBO do empregado n&#x00E3;o localizado na tabela T7BRCB</LI></UL></UL> <UL><UL><LI>204: Op&#x00E7;&#x00E3;o de sele&#x00E7;&#x00E3;o inv&#x00E1;lida</LI></UL></UL> <UL><UL><LI>267: N&#x00E3;o foi poss&#x00ED;vel determinar per&#x00ED;odo de sele&#x00E7;&#x00E3;o de dados (Registro &amp;1 )</LI></UL></UL> <UL><UL><LI>272: A verba rescis&#x00F3;ria &amp;1 &#x00E9; composta por rubricas com diferentes incid&#x00EA;ncias</LI></UL></UL> <UL><UL><LI>273: Os pr&#x00E9;-requisitos de customizing para o registro 0 n&#x00E3;o foram executados</LI></UL></UL> <UL><UL><LI>700: Informe o nome da m&#x00E3;e no infotipo Fam&#x00ED;lia/pessoa de refer&#x00EA;ncia (0021)</LI></UL></UL> <UL><UL><LI>701: Informe o CPF no infotipo Documentos (0465), subtipo 0001</LI></UL></UL> <UL><UL><LI>702: Informe o endere&#x00E7;o do empregado no infotipo Endere&#x00E7;os (0006)</LI></UL></UL> <UL><UL><LI>703: Informe a data de nascimento do empregado no IT Dados pessoais (0002)</LI></UL></UL> <UL><UL><LI>704: Informe o nome do empregado no IT Dados pessoais (0002)</LI></UL></UL> <UL><UL><LI>705: Valor '&amp;1' &#x00E9; inv&#x00E1;lido para o campo &amp;2&amp;3&amp;4</LI></UL></UL> <UL><UL><LI>706: O campo opcional &amp; n&#x00E3;o ser&#x00E1; preenchido</LI></UL></UL> <UL><UL><LI>707: Erro ao preencher o campo Categoria do Trabalhador, verificar BAdI</LI></UL></UL> <UL><UL><LI>708: Valor '&amp;1' &#x00E9; inv&#x00E1;lido para o campo &amp;2&amp;3&amp;4</LI></UL></UL> <UL><UL><LI>709: Valor '&amp;1' &#x00E9; inv&#x00E1;lido para o campo &amp;2&amp;3 em &amp;4</LI></UL></UL> <UL><UL><LI>714: Erro ao processar o campo NRSequencialProduto &amp;1&amp;2 (Dados Financeiros) &amp;3</LI></UL></UL> <UL><UL><LI>715: Descri&#x00E7;&#x00E3;o da rubrica &amp;1 &#x00E9; de preenchimento obrigat&#x00F3;rio</LI></UL></UL> <UL><UL><LI>716: Valor m&#x00E1;ximo atingido para a rubrica &amp;1; verificar customiza&#x00E7;&#x00F5;es</LI></UL></UL> <UL><UL><LI>717: Valor '&amp;1' &#x00E9; inv&#x00E1;lido para o campo TPCalculoMediasVariaveisDecimoTerceiro</LI></UL></UL> <UL><UL><LI>718: Valor '&amp;1' &#x00E9; inv&#x00E1;lido para o campo NRDecimoTerceiroUltimosMesesQuantidade</LI></UL></UL> <UL><UL><LI>719: Valor '&amp;1' &#x00E9; inv&#x00E1;lido para o campo NRDecimoTerceiroUltimosMesesMaiores</LI></UL></UL> <UL><UL><LI>720: Valor '&amp;1' &#x00E9; inv&#x00E1;lido para o campo TPCalculoMediasVariaveisAvisoPrevio</LI></UL></UL> <UL><UL><LI>721: Valor '&amp;1' &#x00E9; inv&#x00E1;lido para o campo NRAvisoPrevioUltimosMesesQuantidade</LI></UL></UL> <UL><UL><LI>722: Valor '&amp;1' &#x00E9; inv&#x00E1;lido para o campo NRAvisoPrevioUltimosMesesMaiores</LI></UL></UL> <UL><UL><LI>723: Valor '&amp;1' &#x00E9; inv&#x00E1;lido para o campo TPEmpregadoDispensadoAvisoPrevio</LI></UL></UL> <UL><UL><LI>724: Percentual '&amp;1' inv&#x00E1;lido para rubrica &amp;2 em &amp;3/&amp;4</LI></UL></UL> <UL><UL><LI>725: Quantidade '&amp;1' inv&#x00E1;lida para rubrica &amp;2 em &amp;3/&amp;4</LI></UL></UL> <UL><LI>Features</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BRTPS, BRAVG</p> <UL><LI>Table types</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PBR_T_HMN_ABSENCE, PBR_T_HMN_EARNINGSDATA, PBR_T_HMN_EMPLOYEE, PBR_T_HMN_EMPLOYER, PBR_T_HMN_MOVIMENT, PBR_T_HMN_OTHER_DEDUCTIONS, PBR_T_HMN_PAYMENTDATA, PBR_T_HMN_VACATIONDATA, PBR_T_HMN_WAGETYPE</p> <UL><LI>Structures</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PBR_S_HMN_CONTRACT, PBR_S_HMN_EMPLOYEE, PBR_S_HMN_INITIALDATA, PBR_S_HMN_ADDRESS, PBR_S_HMN_EMPLOYER, PBR_S_HMN_13SAL, PBR_S_HMN_ABSENCE, PBR_S_HMN_EMPLOYEE, PBR_S_HMN_VACATIONDATA, PBR_S_HMN_CONTRACT, PBR_S_HMN_DEDUCTIONS, PBR_S_HMN_OTHER_DEDUCTIONS, PBR_S_HMN_PAYMENTDATA, PBR_S_HMN_MOVIMENT, PBR_S_HMN_EARNINGSDATA, PBR_S_HMN_FINANCIAL, PBR_S_HMN_WAGETYPE</p> <UL><LI>Data elements</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PBR_HMN_NRHOURS, PBR_HMN_MOVTRANSF, PBR_HMN_MOVRETURN, PBR_HMN_MOVEXIT, PBR_HMN_INCIDENCE, PBR_HMN_FIRE_REASON, PBR_HMN_CODWT1, PBR_HMNWORKDAY, PBR_HMNTPT, PBR_HMNPCDT, PBR_HMNNAME, PBR_HMNDIS, PBR_DEC12_02, PBR_HMN_NRPRODUCT, PBR_HMNCTPSSERIAL, PBR_HMNCPNME, PBR_HMNCNPJT, PBR_HMNCNPJCEI, PBR_HMNCNAE, PBR_HMNCIT, PBR_HMNCHOICE, PBR_HMN_NR_NOTICE, PBR_HMN_PERC, PBR_HMN_QTDPRODUCT, PBR_HMNDATE, PBR_HMNADDR, PBR_HMNADNUM, PBR_HMNAFFIX</p> <UL><LI>Domains</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PBR_HMN_FIRE_REASON, PBR_CNPJCEI, PBR_HMNDISTRICT, PBR_DEC12_02, PBR_DEC2_7, PBR_DEC3_2</p> <UL><LI>Customizing activities in the Implementation Guide</LI></UL> <UL><UL><LI>C&#x00E1;lculo das folhas de pagamento -&gt; C&#x00E1;lc.folha pagamento Brasil -&gt; Rescis&#x00E3;o do contrato de trabalho -&gt; HomologNet -&gt; Definir valores para registro Dados iniciais</LI></UL></UL> <UL><UL><LI>C&#x00E1;lculo das folhas de pagamento -&gt; C&#x00E1;lc.folha pagamento Brasil -&gt; Rescis&#x00E3;o do contrato de trabalho -&gt; HomologNet -&gt; Definir identifica&#x00E7;&#x00E3;o do sindicato do empregado</LI></UL></UL> <UL><UL><LI>C&#x00E1;lculo das folhas de pagamento -&gt; C&#x00E1;lc.folha pagamento Brasil -&gt; Rescis&#x00E3;o do contrato de trabalho -&gt; HomologNet -&gt; Definir causa do afastamento</LI></UL></UL> <UL><UL><LI>C&#x00E1;lculo das folhas de pagamento -&gt; C&#x00E1;lc.folha pagamento Brasil -&gt; Rescis&#x00E3;o do contrato de trabalho -&gt; HomologNet -&gt; Manter c&#x00F3;digo de movimenta&#x00E7;&#x00E3;o</LI></UL></UL> <UL><UL><LI>C&#x00E1;lculo das folhas de pagamento -&gt; C&#x00E1;lc.folha pagamento Brasil -&gt; Rescis&#x00E3;o do contrato de trabalho -&gt; HomologNet -&gt; Definir rubricas salariais internas</LI></UL></UL> <UL><UL><LI>C&#x00E1;lculo das folhas de pagamento -&gt; C&#x00E1;lc.folha pagamento Brasil -&gt; Rescis&#x00E3;o do contrato de trabalho -&gt; HomologNet -&gt; Definir rubricas salariais externas</LI></UL></UL> <UL><UL><LI>C&#x00E1;lculo das folhas de pagamento -&gt; C&#x00E1;lc.folha pagamento Brasil -&gt; Rescis&#x00E3;o do contrato de trabalho -&gt; HomologNet -&gt; Definir tipo de forma&#x00E7;&#x00E3;o de sal&#x00E1;rio</LI></UL></UL> <OL>1. <B>Objects changed</B></OL> <UL><LI>Tables</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;T7BRT0, T7BRAB, T7BRFM</p> <UL><LI>Reports</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;HBRUTGUI, HBRUTMS5</p> <UL><LI>Includes</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PUTMDBR0, PUTMMBR2</p> <UL><LI>Views</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;V_T7BRT0, V_T7BRAB, V_T7BRFM</p> <UL><LI>Type Pools</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PBR42</p> <UL><LI>Data Elements</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PBR_FGTSM</p> <OL>2. <B>Entries in SAP Tables</B></OL> <UL><LI>Table view: V_T596A</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Molga|Sub-application|Text of subapplication&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|Type|Legal</TH></TR> <TR><TH ALIGN=LEFT>-------------------------------------------------------------</TH></TR> <TR><TD>37&#x00A0;&#x00A0; |HMNT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|Homolognet&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|Dia |X</TD></TR> </TABLE></UL> <p></p> <UL><LI>Table view: V_T596G</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Molga: 37 <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Appl: HMNT <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Group|Text of Group</TH></TR> <TR><TH ALIGN=LEFT>----------------------------------------------------------------</TH></TR> <TR><TD>001 |Sal&#x00E1;rio fixo</TD></TR> <TR><TD>002 |Garantia</TD></TR> <TR><TD>003 |Produ&#x00E7;&#x00E3;o</TD></TR> <TR><TD>004 |Horas extras</TD></TR> <TR><TD>005 |Horas trabalhadas no m&#x00EA;s</TD></TR> <TR><TD>006 |Percentagem</TD></TR> <TR><TD>007 |Comiss&#x00E3;o</TD></TR> <TR><TD>008 |Pr&#x00EA;mios</TD></TR> <TR><TD>009 |Multa Art. 477, &#x00A7; 8&#x00BA; CLT - atraso pagto. rescis&#x00E3;o</TD></TR> <TR><TD>010 |Viagens</TD></TR> <TR><TD>011 |Gorjetas</TD></TR> <TR><TD>012 |Horas adicional noturno</TD></TR> <TR><TD>013 |Insalubridade</TD></TR> <TR><TD>013B|Base de C&#x00E1;lculo para 013 - Insalubridade</TD></TR> <TR><TD>014 |Periculosidade</TD></TR> <TR><TD>014B|Base de C&#x00E1;lculo para 014 - Periculosidade</TD></TR> <TR><TD>015 |Sobreaviso</TD></TR> <TR><TD>016 |Prontid&#x00E3;o</TD></TR> <TR><TD>017 |Gratifica&#x00E7;&#x00E3;o</TD></TR> <TR><TD>018 |Adicional tempo servi&#x00E7;o</TD></TR> <TR><TD>018B|Base de C&#x00E1;lculo para 018 - Adicional Tempo Servi&#x00E7;o</TD></TR> <TR><TD>019 |Adicional por transfer&#x00EA;ncia de localidade de trabalho</TD></TR> <TR><TD>019B|Base de C&#x00E1;lculo para 019 - Adicional por Transfer&#x00EA;ncia de Localidade de Trabalho</TD></TR> <TR><TD>020 |Sal&#x00E1;rio fam&#x00ED;lia no que exceder o valor legal obrigat&#x00F3;rio</TD></TR> <TR><TD>021 |Abono ou gratifica&#x00E7;&#x00E3;o de f&#x00E9;rias</TD></TR> <TR><TD>022 |Di&#x00E1;rias para viagem</TD></TR> <TR><TD>023 |Ajuda de custo</TD></TR> <TR><TD>024 |Etapas, no caso dos mar&#x00ED;timos</TD></TR> <TR><TD>025 |Licen&#x00E7;a-pr&#x00EA;mio indenizada</TD></TR> <TR><TD>026 |Quebra de caixa</TD></TR> <TR><TD>027 |Participa&#x00E7;&#x00E3;o do empregado nos lucros e resultados da empresa</TD></TR> <TR><TD>028 |Indeniza&#x00E7;&#x00E3;o recebida a titulo de incentivo a demiss&#x00E3;o</TD></TR> <TR><TD>029 |Bolsa aprendizagem</TD></TR> <TR><TD>030 |Abonos desvinculados do sal&#x00E1;rio</TD></TR> <TR><TD>031 |Ganhos eventuais desvinculados do sal&#x00E1;rio</TD></TR> <TR><TD>032 |Reembolso creche pago em conformidade &#x00E0; legisla&#x00E7;&#x00E3;o trabalhista</TD></TR> <TR><TD>033 |Reembolso bab&#x00E1; pago em conformidade &#x00E0; legisla&#x00E7;&#x00E3;o trabalhista e previdenci&#x00E1;ria</TD></TR> <TR><TD>034 |Gratifica&#x00E7;&#x00E3;o semestral</TD></TR> <TR><TD>035 |N&#x00FA;mero de dias trabalhados no m&#x00EA;s</TD></TR> <TR><TD>036 |Multa do art. 476-A, &#x00A7; 5&#x00BA;, da CLT</TD></TR> <TR><TD>1313|Valor do d&#x00E9;cimo terceiro sal&#x00E1;rio</TD></TR> <TR><TD>131P|Valor de adiantamento 13&#x00BA; sal&#x00E1;rio a ser descontado do empregado na rescis&#x00E3;o</TD></TR> <TR><TD>13DI|Valor do d&#x00E9;cimo terceiro sal&#x00E1;rio no off-cycle 13DI</TD></TR> <TR><TD>ADIA|Valor de adiantamento salarial a ser descontado do empregado no m&#x00EA;s da rescis&#x00E3;o</TD></TR> <TR><TD>DSR7|DSR do no m&#x00EA;s do afast., com exce&#x00E7;&#x00E3;o &#x00FA;ltima semana</TD></TR> <TR><TD>DSR8|DSR do m&#x00EA;s da rescis&#x00E3;o</TD></TR> <TR><TD>EXT7|R&#x00FA;bricas Externas - Dados Financeiros para C&#x00E1;lculo da Rescis&#x00E3;o</TD></TR> <TR><TD>FAPI|Contribui&#x00E7;&#x00F5;es para o FAPI</TD></TR> <TR><TD>I13S|Verbas rescis&#x00F3;rias que integram em suas apura&#x00E7;&#x00F5;es c&#x00E1;lculos de 13&#x00BA;</TD></TR> <TR><TD>I480|Valor da indeniza&#x00E7;&#x00E3;o devido &#x00E0; rescis&#x00E3;o antecipada do contrato de trabalho</TD></TR> <TR><TD>IAVP|Verbas rescis&#x00F3;rias que integram em suas apura&#x00E7;&#x00F5;es c&#x00E1;lculos de aviso pr&#x00E9;vio</TD></TR> <TR><TD>IFER|Verbas rescis&#x00F3;rias que integram em suas apura&#x00E7;&#x00F5;es c&#x00E1;lculos de f&#x00E9;rias</TD></TR> <TR><TD>IRRF|Ourtas dedu&#x00E7;&#x00F5;es para base de c&#x00E1;lculo de IRRF</TD></TR> <TR><TD>LOAN|Valor atual do saldo devedor do cr&#x00E9;dito consignado em folha de pagamento</TD></TR> <TR><TD>OUT8|Outros Descontos - Dados de Descontos</TD></TR> <TR><TD>PENS|Perc. da pens&#x00E3;o aliment&#x00ED;cia a ser retido no valor da rescis&#x00E3;o do contrato</TD></TR> <TR><TD>PRIV|Contribui&#x00E7;&#x00F5;es para as entidades de previd&#x00EA;ncia privada domiciliadas no Brasil</TD></TR> <TR><TD>SIND|Informar o valor a ser descontado referente &#x00E0; contribui&#x00E7;&#x00E3;o sindical laboral</TD></TR> <TR><TD>SLMT|Valor do sal&#x00E1;rio l&#x00ED;quido do m&#x00EA;s anterior &#x00E0; rescis&#x00E3;o</TD></TR> <TR><TD>VADE|Valor do desconto de vale-alimenta&#x00E7;&#x00E3;o, a ser descontado do empregado na rescis&#x00E3;o</TD></TR> <TR><TD>VARE|Valor do reembolso de vale-alimenta&#x00E7;&#x00E3;o a ser descontado do empregado na rescis&#x00E3;o</TD></TR> <TR><TD>VFER|Montante relativo aos dias de f&#x00E9;rias no m&#x00EA;s do afastamento, pago no m&#x00EA;s anterior</TD></TR> <TR><TD>VTRE|Valor do reembolso de vale-transporte, a ser descontado do empregado na rescis&#x00E3;o</TD></TR> <TR><TD>VTVT|Valor gasto pelo empregador a t&#x00ED;tulo de vale-transporte, no m&#x00EA;s da rescis&#x00E3;o</TD></TR> </TABLE> <UL><LI>Table view: V_T596I</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Molga: 37 <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Appl: HMNT <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Cumul. WT | Wage Type | Start Date | End Date</TH></TR> <TR><TH ALIGN=LEFT>----------------------------------------------------------------</TH></TR> <TR><TD>&#x00A0;&#x00A0; 1313&#x00A0;&#x00A0; |&#x00A0;&#x00A0; /333&#x00A0;&#x00A0;&#x00A0;&#x00A0;| 01.01.1800 | 31.12.9999</TD></TR> <TR><TD>&#x00A0;&#x00A0; 13DI&#x00A0;&#x00A0; |&#x00A0;&#x00A0; /357&#x00A0;&#x00A0;&#x00A0;&#x00A0;| 01.01.1800 | 31.12.9999</TD></TR> </TABLE></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-CSC-BR (Brazil)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I824606)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I810942)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001497982/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001497982/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001497982/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001497982/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001497982/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001497982/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001497982/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001497982/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001497982/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Release_604.ZIP", "FileSize": "68", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000440342010&iv_version=0014&iv_guid=109724745540C24D9DE558E948000576"}, {"FileName": "Release_600.ZIP", "FileSize": "54", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000440342010&iv_version=0014&iv_guid=33760108B3CD1D42B3AB8298F5592490"}, {"FileName": "Customizing_Guide.pdf", "FileSize": "339", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000440342010&iv_version=0014&iv_guid=C5479F7851800545893CD45B970830A0"}, {"FileName": "Release_470.ZIP", "FileSize": "48", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000440342010&iv_version=0014&iv_guid=EC7002B00175304894A6D23FFD4E2C93"}, {"FileName": "Release_500.ZIP", "FileSize": "50", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000440342010&iv_version=0014&iv_guid=94E7E5A6D77FFF458F0312B4CA2551BD"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "1553891", "RefComponent": "PY-BR", "RefTitle": "Homolognet-Termination Term: Customizing tables", "RefUrl": "/notes/1553891"}, {"RefNumber": "1552082", "RefComponent": "PY-BR", "RefTitle": "LC Announcement: Homolognet", "RefUrl": "/notes/1552082"}, {"RefNumber": "1528076", "RefComponent": "PY-BR", "RefTitle": "HBRUTMS5: Viewing XML TemSe Files", "RefUrl": "/notes/1528076"}, {"RefNumber": "1512248", "RefComponent": "PY-BR", "RefTitle": "General Functionalities for Ordinance 1620 and 1621", "RefUrl": "/notes/1512248"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1318389", "RefComponent": "BC-CTS", "RefTitle": "How to Use .SAR/.CAR files and Risks Involved", "RefUrl": "/notes/1318389"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1512248", "RefComponent": "PY-BR", "RefTitle": "General Functionalities for Ordinance 1620 and 1621", "RefUrl": "/notes/1512248 "}, {"RefNumber": "1552082", "RefComponent": "PY-BR", "RefTitle": "LC Announcement: Homolognet", "RefUrl": "/notes/1552082 "}, {"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "1553891", "RefComponent": "PY-BR", "RefTitle": "Homolognet-Termination Term: Customizing tables", "RefUrl": "/notes/1553891 "}, {"RefNumber": "1528076", "RefComponent": "PY-BR", "RefTitle": "HBRUTMS5: Viewing XML TemSe Files", "RefUrl": "/notes/1528076 "}, {"RefNumber": "1318389", "RefComponent": "BC-CTS", "RefTitle": "How to Use .SAR/.CAR files and Risks Involved", "RefUrl": "/notes/1318389 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HRCBR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HRCBR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HRCBR", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_HRCBR", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HRCBR 470", "SupportPackage": "SAPK-470C5INSAPHRCBR", "URL": "/supportpackage/SAPK-470C5INSAPHRCBR"}, {"SoftwareComponentVersion": "SAP_HRCBR 500", "SupportPackage": "SAPK-50091INSAPHRCBR", "URL": "/supportpackage/SAPK-50091INSAPHRCBR"}, {"SoftwareComponentVersion": "SAP_HRCBR 600", "SupportPackage": "SAPK-60074INSAPHRCBR", "URL": "/supportpackage/SAPK-60074INSAPHRCBR"}, {"SoftwareComponentVersion": "SAP_HRCBR 604", "SupportPackage": "SAPK-60440INSAPHRCBR", "URL": "/supportpackage/SAPK-60440INSAPHRCBR"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_HRCBR", "NumberOfCorrin": 4, "URL": "/corrins/0001497982/5362"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 4, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_HRCBR", "ValidFrom": "470", "ValidTo": "470", "Number": "1464284 ", "URL": "/notes/1464284 ", "Title": "Portaria No. 1510 - Electronic time clock record", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HRCBR", "ValidFrom": "470", "ValidTo": "470", "Number": "1527811 ", "URL": "/notes/1527811 ", "Title": "DIRF 2010 - Instrução Normativa Nº 1.033", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HRCBR", "ValidFrom": "470", "ValidTo": "470", "Number": "1528076 ", "URL": "/notes/1528076 ", "Title": "HBRUTMS5: Viewing XML TemSe Files", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HRCBR", "ValidFrom": "500", "ValidTo": "500", "Number": "1464284 ", "URL": "/notes/1464284 ", "Title": "Portaria No. 1510 - Electronic time clock record", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HRCBR", "ValidFrom": "500", "ValidTo": "500", "Number": "1527811 ", "URL": "/notes/1527811 ", "Title": "DIRF 2010 - Instrução Normativa Nº 1.033", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HRCBR", "ValidFrom": "500", "ValidTo": "500", "Number": "1528076 ", "URL": "/notes/1528076 ", "Title": "HBRUTMS5: Viewing XML TemSe Files", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HRCBR", "ValidFrom": "600", "ValidTo": "600", "Number": "1512248 ", "URL": "/notes/1512248 ", "Title": "General Functionalities for Ordinance 1620 and 1621", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HRCBR", "ValidFrom": "600", "ValidTo": "600", "Number": "1527811 ", "URL": "/notes/1527811 ", "Title": "DIRF 2010 - Instrução Normativa Nº 1.033", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HRCBR", "ValidFrom": "600", "ValidTo": "600", "Number": "1528076 ", "URL": "/notes/1528076 ", "Title": "HBRUTMS5: Viewing XML TemSe Files", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HRCBR", "ValidFrom": "600", "ValidTo": "604", "Number": "1464284 ", "URL": "/notes/1464284 ", "Title": "Portaria No. 1510 - Electronic time clock record", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HRCBR", "ValidFrom": "604", "ValidTo": "604", "Number": "1512248 ", "URL": "/notes/1512248 ", "Title": "General Functionalities for Ordinance 1620 and 1621", "Component": "PY-BR"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}