{"Request": {"Number": "897623", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 217, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016013552017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000897623?language=E&token=B63B1DC50E2769FFAB2A5F2A41D6972B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000897623", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000897623/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "897623"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.05.2014"}, "SAPComponentKey": {"_label": "Component", "value": "PT-RC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Time Data Recording and Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Personnel Time Management", "value": "PT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Time Data Recording and Management", "value": "PT-RC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PT-RC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "897623 - User-Exits and BAdIs in PT"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Which expansion options (user exits and BAdIs) exist in Personnel Time Management (PT)?</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Collective note, Personnel Time Management, user exits, BAdIs, PT</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Not relevant</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The following is a list of all user exits/BAdIs that are available in Personnel Time Management (PT).<br /><br />The following BAdIs can be called up via transaction SE18, while the function modules are called up via transaction SE37 (unless otherwise stated). If BAdIs are available in each area, they are listed first. Unless otherwise stated, these expansions are available from at least Release 4.6 C onwards.<br /><br />The section entitled \"Related SAP Notes\" contains a list of additional consulting notes for Personnel Time Management.<br /><br />Absence Valuation in Payroll (Function XNAB)<br /><br />- HRTIM_COV_HISTORY<br /> Enhancements to Payroll Function XNAB<br />- HRTIM_UCCONV_COVER<br /> Unicode Conversion Table COVER Cluster PC<br /><br />- EXIT_SAPLHRTIM00COV_HIST_001<br /> Change Rule Group for Refining Absences<br />- EXIT_SAPLHRTIM00COV_HIST_002<br /> Determination of Seniority<br />- EXIT_SAPLHRTIM00COV_HIST_003<br /> Round Selection of Rules for Absence Refinement<br />- EXIT_SAPLHRTIM00COV_HIST_004<br /> Determine Marker for Absence Day<br />- EXIT_SAPLHRTIM00COV_HIST_005<br /> Refine Table AB<br />- EXIT_SAPLHRTIM00COV_HIST_006<br /> Determine Relevant Period for Change in Marker<br />- EXIT_SAPLHRTIM00COV_HIST_007<br /> Choose Days with Change in Marker<br />- EXIT_SAPLHRTIM00COV_HIST_008<br /> Determine Age<br /><br />BW Time Management and Infotype 439 (BW-BCT-PT)<br /><br />- PTDW_CREATE_0439<br /> Enhancement for Creating Records of Infotype 0439<br /><br />- EXIT_SAPLHRMS_BIW_PTDW_001<br /> Customer Exit for Time Data Extractor: Hours According to Personal WS<br />- EXIT_SAPLHRMS_BIW_PTDW_002<br /> Customer Exit for Time Data Extractor: Time and Labor<br />- EXIT_SAPLHRMS_BIW_PTDW_003<br /> Customer Exit for Time Data Extractor: Quota Transaction Data<br /><br />Concurrent Employment (PT-CE)<br /><br />- PTCCE_DIST_DATA<br /> Distribute Time Evaluation Data over Personnel Assignments<br /><br />Absence Reporting (PT-IS)<br /><br />- EXIT_SAPLHRTA_001<br /> Customer Exit: Att./Absence Reporting<br /><br />Transferring External Wage Types<br /><br />- EXIT_RPIEWT00_001<br /> Supply Currency Key<br /><br />Leave Request (ESS)<br /><br />- PT_ABS_REQ_CUST (Rel. 4.7 onward)<br /> Read Customizing Settings for Leave Request<br />- PT_GEN_REQ (Rel. 5.00 onward)<br /> BAdI: Control Processing Processes for Time Management Web<br />- PT_ABS_REQ (Rel. 5.00 onwards)<br /> Enhancements for Leave Request<br />- PT_IAC_LEAVREQ_0001 (as of Rel. 4.7)<br /> Leave Request Web Application: Attendance/Absence Types<br />- PT_IAC_REPORT (as of Rel. 5.00)<br /> Specify Application Report Variant by User Name<br />- PT_COR_REQ (as of Rel. 5.00)<br /> Enhancements for Clock-In/Out Corrections<br /><br />- EXIT_SAPMWS01000109H_001<br /> Leave Requests - Overview: Determine Approver<br />- EXIT_SAPMWS04200009H_001<br /> Leave Request: Determine Approver<br />- EXIT_SAPMWS12400005H_001<br /> Leave Requests - Overview: Determine Approver<br />- EXIT_SAPMWS20000081H_001<br /> Leave Request: Determine Approver<br /><br />Time Manager's Workplace (TMW)<br /><br />- PT_BLP_USER<br /> Enhance Business Logic for Time Data<br />- PT_GUI_TMW_TDE_NM (as of Rel. 4.7)<br /> TMW team view: Fill Customer Fields<br />- PT_GUI_TMW_CALENDAR (as of Rel. 4.7)<br /> Fill Default Values in TMW Calendar<br /><br />- EXIT_SAPLHRTIM00DVEXIT_001<br /> TMW: Title of Customer Fields in Table Control<br />- EXIT_SAPLHRTIM00DVEXIT_002<br /> TMW: Fill Contents of Customer Fields in Table Control<br /><br />Shift Planning (PT-SP)<br /><br />- HRSPPBS_ADD_CHECKS<br /> Ad-hoc Update of Information Columns<br /><br />- RHEI_CUSTOMER_EXIT (using SE38)<br />- FH5AH_CUSTOMER_EXIT (using SE38)<br /><br />Time Data Recording (PA61)<br /><br />- PT_QUOTA_DEDUCTION<br /> BAdI for Quota Deduction<br />- PT_QUOTA_DEF_VAL<br /> Decision About Default Values for Quotas<br />- PT_ABS_ATT_COUNTRY<br /> Free Determination of Payroll Hours and Payroll Days<br />- TIM00ABSCOUNTRY_DAY (use PT_ABS_ATT_COUNTRY)<br /> Country Enhancement of Daily Counting of IT2001/2002<br />- TIM00ATTABSCOUNTING (use PT_ABS_ATT_COUNTRY)<br /> Multiplication of Payroll Hours and Payroll Days<br /><br />- EXIT_MP000500_001<br /> User exit for infotype 0005<br />- EXIT_RPILVA00_001<br /> User exit for leave accrual: program RPILVA00<br />- EXIT_MP200000_001<br /> Customer-specific default values for activity allocation<br />- EXIT_MP200000_002<br /> Customer-specific default values for cost assignment<br />- EXIT_MP200000_003<br /> Customer-specific default values for external services<br />- EXIT_MP200000_004<br /> Customer-specific validation for activity allocation<br />- EXIT_MP200000_005<br /> Customer-Specific Default Value for Att./Abs. Type for<br /> weekly recording<br />- EXIT_MP200000_006<br /> Customer-Specific Validation of All Data Records in Weekly Calendar<br /><br />Work Schedules (PT-WS)<br /><br />- EXIT_SAPLPTWS_001<br /> User Exit for Generating Monthly Work Schedule<br /><br />Time Leveling/Incentive Wages Check MiniApp (???)<br /><br />- EXIT_SAPLPTWAO_TIMELEV_001<br /> Change Period and Report Variant for Time Management<br /> MiniApp<br /><br />MiniApp Balance Display: Customer Exit<br /><br />- EXIT_SAPLPTWAO_BALANCES_001<br /> Customer-specific infofield<br /><br />Time statement (RPDEDT00)<br /><br />- EXIT_RPTEDT00_001<br /> Modification of Cluster B2 Data After Import In RPTEDT00<br /><br />Quota Generation (PA61 and function QUOTA<br /><br />- PT_QUOTA<br /> Automatic Generation of Absence Quotas<br /><br />- EXIT_SAPLHRLV_001<br /> Customer Enhancement for Quota Generation - Applicability<br />- EXIT_SAPLHRLV_002<br /> Customer Enhancement for Quota Generation - Accrual Entitlement<br />- EXIT_SAPLHRLV_003<br /> Customer Enhancement for Quota Generation - Reduction Rule<br />- EXIT_SAPLHRLV_004<br /> Customer Enhancement for Quota Generation - Base Entitlement<br />- EXIT_SAPLHRLV_005<br /> Customer Enhancement for Quota Generation - Transfer Specification<br />- EXIT_SAPLHRLV_006<br /> Customer Enhancement for Quota Generation - Entry/Leaving Date<br />- EXIT_SAPLHRLV_007<br /> Customer Enhancement for Quota Generation - Defaults for Validity Interval<br />- EXIT_SAPLHRLV_008<br /> Customer Enhancement for Quota Generation - Selection Rule Group<br />- EXIT_SAPLHRLV_009<br /> Customer Enhancement for Quota Generation - Result of Generation for<br /> Defaults<br />- EXIT_SAPLHRLV_010<br /> Customer Enhancement for Quota Generation - IT2013 Validity/<br /> Deduction Interval<br />- EXIT_SAPLHRLV_011<br /> Customer Enhancement for Quota Generation - Validity and<br /> Deduction Interval<br />- EXIT_SAPLHRLV_012<br /> Customer Enhancement for Quota Generation - Accrual Period<br /><br />Incentive Wages (PW01, PW02, PW03)<br />(Called up using SE38)<br />- MP53LF99, FORM USER_SET_LSTYP<br /> Determine Time Ticket Type<br />- MP53LF99, FORM USER_SOW01<br /> Determine Target Labor Time<br />- MP53LF99, FORM USER_SOW02<br /> Determine Target Setup Time<br />- MP53LF99, FORM USER_SOW05<br /> Determine Target Teardown Time<br />- MP53LF99, FORM USER_SOW03<br /> Determine Target Machine Time<br />- MP53LF99, FORM USER_SOW04<br /> Determine Target Time for Variable Activity<br />- MP53LF99, FORM USER_PRFOR_LS<br /> Time Ticket Premium Formula<br />- MP53LF99, FORM USER_LGRAD<br /> Calculate Labor Utilization Rate<br />- MP53LF99, FORM USER_COLLECT_LS<br /> Cumulate Time Ticket Data<br />- MP53LF99, FORM USER_CHECK_LOARR<br /> Validate Wage Type<br />- MP53LF99, FORM USER_CHECK_LOGRR<br /> Validate Pay Scale Group<br />- MP53LF99, FORM USER_CHECK_KOSTR<br /> Validate Cost Center<br />- MP53LF99, FORM USER_GET_AUFTRAGSDATEN<br /> Read Own Order Data<br /><br />KK1 / HR-PDC Interface<br /><br />- EXIT_SAPLRPPD_001<br /> User Exit for Processing Statuses from Time Events and Pair Formation<br />- EXIT_SAPLRPTC_001<br /> User Exit for Downloading HR Mini Master<br />- EXIT_SAPLRPTC_002<br /> User Exit for Downloading Personnel Time Balances<br />- EXIT_SAPLRPTC_003<br /> User Exit for Uploading Time Events<br />- EXIT_SAPLRPTC_004<br /> User Exit for Uploading Employee Expenditures<br />- EXIT_SAPLRPTC_005<br /> User Exit for Downloading Attendance/Absence Reasons<br />- EXIT_SAPLRPTC_006<br /> User Exit for Downloading Cost Centers<br />- EXIT_SAPLRPTC_007<br /> Userexit for Downloading Permitted Employee Expenditures<br />- EXIT_SAPLRPTC_008<br /> User Exit for Downloading Internal Orders<br />- EXIT_SAPLRPTC_009<br /> User Exit for Downloading Objects (for example, positions)<br />- EXIT_SAPLRPTC_010<br /> User Exit for Downloading Time Event Type Groups<br />- EXIT_SAPLRPTC_011<br /> User Exit for Downloading Projects</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D035702)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D019399)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000897623/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000897623/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000897623/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000897623/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000897623/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000897623/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000897623/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000897623/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000897623/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "818886", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/818886"}, {"RefNumber": "746999", "RefComponent": "PT-RC", "RefTitle": "Use of Transaction PT69 (report RPTENT00)", "RefUrl": "/notes/746999"}, {"RefNumber": "659143", "RefComponent": "PT-RC-TE", "RefTitle": "Time events of the last workday are missing", "RefUrl": "/notes/659143"}, {"RefNumber": "613033", "RefComponent": "PT-RC", "RefTitle": "Constants for attendance/absence valuation", "RefUrl": "/notes/613033"}, {"RefNumber": "581301", "RefComponent": "PT-EV-QT", "RefTitle": "HRPTIM03: Conversion of customer exits", "RefUrl": "/notes/581301"}, {"RefNumber": "555588", "RefComponent": "PT-EV", "RefTitle": "Change of the Time Evaluation periodicity", "RefUrl": "/notes/555588"}, {"RefNumber": "537373", "RefComponent": "PT-EV", "RefTitle": "Runtime measurement Time Evaluation (RPTIME00)", "RefUrl": "/notes/537373"}, {"RefNumber": "534275", "RefComponent": "PT-RC", "RefTitle": "RPTBPC10: change of deduction sequence", "RefUrl": "/notes/534275"}, {"RefNumber": "487099", "RefComponent": "PT-RC-QT", "RefTitle": "Quota generation with leaving / example for user exit 012", "RefUrl": "/notes/487099"}, {"RefNumber": "484834", "RefComponent": "PT-EV-QT", "RefTitle": "Quota generation after organizational reassignment", "RefUrl": "/notes/484834"}, {"RefNumber": "447097", "RefComponent": "PT-RC-UI-TMW", "RefTitle": "Questions and answers concerning the TMW implementation", "RefUrl": "/notes/447097"}, {"RefNumber": "443694", "RefComponent": "PT-EV-QT", "RefTitle": "Automatic rounding of quota corrections not required", "RefUrl": "/notes/443694"}, {"RefNumber": "412261", "RefComponent": "PT-RC", "RefTitle": "Enhancement of Time Management infotypes", "RefUrl": "/notes/412261"}, {"RefNumber": "411854", "RefComponent": "PT-EV", "RefTitle": "Processing of remaining quotas", "RefUrl": "/notes/411854"}, {"RefNumber": "400514", "RefComponent": "PT-EV-QT", "RefTitle": "Processing of remaining leave with absence quotas", "RefUrl": "/notes/400514"}, {"RefNumber": "389732", "RefComponent": "PT-EV", "RefTitle": "Processing of infotype 0208 in time evaluation", "RefUrl": "/notes/389732"}, {"RefNumber": "385799", "RefComponent": "PT-EV", "RefTitle": "RPTIME00: Rounding of TIP pairs", "RefUrl": "/notes/385799"}, {"RefNumber": "374935", "RefComponent": "PT-RC-QT", "RefTitle": "Processing of quotas when an action is deleted", "RefUrl": "/notes/374935"}, {"RefNumber": "369536", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/369536"}, {"RefNumber": "364256", "RefComponent": "PT-EV", "RefTitle": "Generation of absence quotas with date type", "RefUrl": "/notes/364256"}, {"RefNumber": "358014", "RefComponent": "PT-RC-QT", "RefTitle": "Problems with RPUQTA50", "RefUrl": "/notes/358014"}, {"RefNumber": "351867", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/351867"}, {"RefNumber": "326120", "RefComponent": "PT-RC", "RefTitle": "Performance problem of external interface", "RefUrl": "/notes/326120"}, {"RefNumber": "211918", "RefComponent": "PT-RC-TE", "RefTitle": "KK1 is replaced by HR-PDC", "RefUrl": "/notes/211918"}, {"RefNumber": "179077", "RefComponent": "PT-RC-TE", "RefTitle": "HR-PDC: Time events in exact seconds", "RefUrl": "/notes/179077"}, {"RefNumber": "171491", "RefComponent": "PT-RC", "RefTitle": "Attendances: Proposal and limitation", "RefUrl": "/notes/171491"}, {"RefNumber": "128501", "RefComponent": "PT-RC", "RefTitle": "Concept: Previous day assignment in pair formation", "RefUrl": "/notes/128501"}, {"RefNumber": "1245560", "RefComponent": "CA-GTF-TS-XSS", "RefTitle": "Composite SAP Note : XSS Documentation", "RefUrl": "/notes/1245560"}, {"RefNumber": "117153", "RefComponent": "PT-EV", "RefTitle": "RPTIME00: Absences with zero target hours", "RefUrl": "/notes/117153"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2547614", "RefComponent": "CA-TS", "RefTitle": "How to make Att./Abs. (AWART) more meaningful", "RefUrl": "/notes/2547614 "}, {"RefNumber": "2514312", "RefComponent": "PT-RC-EX", "RefTitle": "Time recording systems with different identifiers - ZAUSW", "RefUrl": "/notes/2514312 "}, {"RefNumber": "2511595", "RefComponent": "CA-TS", "RefTitle": "How to avoid user-exit influence in CATS area", "RefUrl": "/notes/2511595 "}, {"RefNumber": "1245560", "RefComponent": "CA-GTF-TS-XSS", "RefTitle": "Composite SAP Note : XSS Documentation", "RefUrl": "/notes/1245560 "}, {"RefNumber": "581301", "RefComponent": "PT-EV-QT", "RefTitle": "HRPTIM03: Conversion of customer exits", "RefUrl": "/notes/581301 "}, {"RefNumber": "358014", "RefComponent": "PT-RC-QT", "RefTitle": "Problems with RPUQTA50", "RefUrl": "/notes/358014 "}, {"RefNumber": "400514", "RefComponent": "PT-EV-QT", "RefTitle": "Processing of remaining leave with absence quotas", "RefUrl": "/notes/400514 "}, {"RefNumber": "128501", "RefComponent": "PT-RC", "RefTitle": "Concept: Previous day assignment in pair formation", "RefUrl": "/notes/128501 "}, {"RefNumber": "447097", "RefComponent": "PT-RC-UI-TMW", "RefTitle": "Questions and answers concerning the TMW implementation", "RefUrl": "/notes/447097 "}, {"RefNumber": "326120", "RefComponent": "PT-RC", "RefTitle": "Performance problem of external interface", "RefUrl": "/notes/326120 "}, {"RefNumber": "746999", "RefComponent": "PT-RC", "RefTitle": "Use of Transaction PT69 (report RPTENT00)", "RefUrl": "/notes/746999 "}, {"RefNumber": "389732", "RefComponent": "PT-EV", "RefTitle": "Processing of infotype 0208 in time evaluation", "RefUrl": "/notes/389732 "}, {"RefNumber": "211918", "RefComponent": "PT-RC-TE", "RefTitle": "KK1 is replaced by HR-PDC", "RefUrl": "/notes/211918 "}, {"RefNumber": "659143", "RefComponent": "PT-RC-TE", "RefTitle": "Time events of the last workday are missing", "RefUrl": "/notes/659143 "}, {"RefNumber": "364256", "RefComponent": "PT-EV", "RefTitle": "Generation of absence quotas with date type", "RefUrl": "/notes/364256 "}, {"RefNumber": "484834", "RefComponent": "PT-EV-QT", "RefTitle": "Quota generation after organizational reassignment", "RefUrl": "/notes/484834 "}, {"RefNumber": "443694", "RefComponent": "PT-EV-QT", "RefTitle": "Automatic rounding of quota corrections not required", "RefUrl": "/notes/443694 "}, {"RefNumber": "613033", "RefComponent": "PT-RC", "RefTitle": "Constants for attendance/absence valuation", "RefUrl": "/notes/613033 "}, {"RefNumber": "171491", "RefComponent": "PT-RC", "RefTitle": "Attendances: Proposal and limitation", "RefUrl": "/notes/171491 "}, {"RefNumber": "555588", "RefComponent": "PT-EV", "RefTitle": "Change of the Time Evaluation periodicity", "RefUrl": "/notes/555588 "}, {"RefNumber": "537373", "RefComponent": "PT-EV", "RefTitle": "Runtime measurement Time Evaluation (RPTIME00)", "RefUrl": "/notes/537373 "}, {"RefNumber": "534275", "RefComponent": "PT-RC", "RefTitle": "RPTBPC10: change of deduction sequence", "RefUrl": "/notes/534275 "}, {"RefNumber": "487099", "RefComponent": "PT-RC-QT", "RefTitle": "Quota generation with leaving / example for user exit 012", "RefUrl": "/notes/487099 "}, {"RefNumber": "385799", "RefComponent": "PT-EV", "RefTitle": "RPTIME00: Rounding of TIP pairs", "RefUrl": "/notes/385799 "}, {"RefNumber": "411854", "RefComponent": "PT-EV", "RefTitle": "Processing of remaining quotas", "RefUrl": "/notes/411854 "}, {"RefNumber": "117153", "RefComponent": "PT-EV", "RefTitle": "RPTIME00: Absences with zero target hours", "RefUrl": "/notes/117153 "}, {"RefNumber": "412261", "RefComponent": "PT-RC", "RefTitle": "Enhancement of Time Management infotypes", "RefUrl": "/notes/412261 "}, {"RefNumber": "374935", "RefComponent": "PT-RC-QT", "RefTitle": "Processing of quotas when an action is deleted", "RefUrl": "/notes/374935 "}, {"RefNumber": "179077", "RefComponent": "PT-RC-TE", "RefTitle": "HR-PDC: Time events in exact seconds", "RefUrl": "/notes/179077 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}