{"Request": {"Number": "48400", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 384, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014447712017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000048400?language=E&token=ACEC70A569A5362316435ECAC55473A2"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000048400", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000048400/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "48400"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 32}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.06.2023"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CCM-PRN-TMS"}, "SAPComponentKeyText": {"_label": "Component", "value": "TemSe (Repository for temporary sequential objects)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Computer Center Management System (CCMS)", "value": "BC-CCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Print and Output Management", "value": "BC-CCM-PRN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-PRN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "TemSe (Repository for temporary sequential objects)", "value": "BC-CCM-PRN-TMS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-PRN-TMS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "48400 - Reorganization of TemSe and spool"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>In your SAP system, you have found an inconsistency in the spool database or in the database for temporary sequential objects (TemSe).<br /><br /><br /></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Consistency, XRTAB, RT_UPDATE, RT_INSERT, SPOOL_INTERNAL_ERROR, reorg,<br />TemSe, rows, DIR_GLOBAL</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>There can be many different causes. For example, inconsistencies can occur if you delete table entries manually from the spool and TemSe tables, delete spool and TemSe objects from the file system or do not carry out the consistency checks correctly. Terminations of the report and transactions may also cause these inconsistencies. An incorrectly executed client copy also leads to inconsistencies in TemSe and the spool.<br />Inconsistencies do not 'just appear'. They always have concrete causes. However, in many cases it is impossible to determine these causes after they have occurred.<br />A precise analysis is generally possible only if you have a reproducible example. The following sections will therefore only describe how to detect and eliminate inconsistencies.<br /><br />Definition of the term TemSe:<br />Some objects, which are normally not retained in the system for a long time, are stored in the temporary sequential objects (TemSe) data storage.<br />These are:</p>\r\n<ul>\r\n<ul>\r\n<li>1. Spool requests (TemSe name: SPOOL.......)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>2. Job logs (TemSe name: JOBLG........) - up to and including SAP_BASIS 7.51</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>3. Objects from other applications, for example Human Resources Management (TemSe name: HR.......), Financial Accounting - data medium exchange (TemSe name: DTA...)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>4. An object beginning with KONS. This object is used constantly by the report RSPO1043 and should never be deleted under normal circumstances (SAP Note <a target=\"_blank\" href=\"/notes/98065\">98065</a>).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>5. If the new log procedure is used for batch input (SAP Note <a target=\"_blank\" href=\"/notes/175596\">175596</a>), the batch input logs (TemSe name: BDC....).</li>\r\n</ul>\r\n</ul>\r\n<p><br />Each TemSe object consists of a header entry in table TST01 and the actual object. It can be stored in the file system (always the case for job logs) or in table TST03 (always for HR data). In the case of spool entries, you can use a parameter to decide whether the object is stored in the file system or in the table TST03 (default setting, see SAP Note <a target=\"_blank\" href=\"/notes/20176\">20176</a>).</p>\r\n<p>With data medium exchange objects from Financial Accounting, you can determine whether the object is stored in TST03 or in the file system for the relevant runs (in the latter case, there is no header entry in TST01. Thus, the object is no longer a TemSe object, and administration takes place via transaction FDTA.) For more information about DTA objects and how to manage them, refer to the section on data medium exchange in the Accounts Receivable and Accounts Payable Accounting sections of the online documentation (see also SAP Note <a target=\"_blank\" href=\"/notes/15960\">15960</a>).</p>\r\n<p>Since each TemSe object belongs to an application, there are normally additional tables in the application that contain the application object to which the TemSe object belongs. Thus, spool requests also have entries in table TSP01 (header entries for the spool request) and possibly in the table TSP02 (header entries for possible output requests) as well as in several other spool tables. Therefore, you normally should never delete only a TemSe object without the relevant application object; otherwise, you create inconsistencies in the application.</p>\r\n<p>CAUTION:<br />Until SAP Note <a target=\"_blank\" href=\"/notes/1890546\">1890546</a>,&#x00A0;TemSe could not manage any objects larger than 2 GB, regardless of whether the object was stored in the database or at file system level. If you cannot implement SAP Note 1890546, you must set up the application so that the data is distributed to several objects if the object would otherwise be larger than 2 GB.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ul>\r\n<li>1. Check memory distribution in the TemSe data storage.<br /><br />Transaction SP12 -&gt; TemSe database -&gt; memory allocation<br /><br />Here, you can check the distribution of your TemSe objects by age, client and creator. For more information about this, see SAP Note <a target=\"_blank\" href=\"/notes/11070\">11070</a>. Check whether the number of your TemSe objects is very large.<br /><br />CAUTION:<br />The expiry time of the TemSe objects is usually unlimited. The reason for this is that a TemSe object should never be deleted without the relevant application object. Accordingly, the expiry date of the application object determines the lifetime of the Temse object. The retention period of the spool orders, for example, is stored in the table TSP01 and can be read in the header data for the spool request (transaction SP01).<br /><br />NOTE THAT TEMSE IS NOT AN ARCHIVING SYSTEM.<br />Furthermore, the maximum permitted number of spool requests is limited (see SAP note <a target=\"_blank\" href=\"/notes/48284\">48284</a>). Use archiving for spool requests.<br />Always ensure that the number of objects and the memory allocation do not continually increase in your system.</li>\r\n</ul>\r\n<p>&#x00A0;</p>\r\n<ul>\r\n<li>2. Delete any application objects that are no longer required.</li>\r\n<ul>\r\n<li>A. Spool objects that are no longer required can be deleted using the report  <strong>RSPO0041</strong> or <strong>RSPO1041</strong> (see SAP Note <a target=\"_blank\" href=\"/notes/41547\"> 41547</a>).</li>\r\n</ul>\r\n<ul>\r\n<li>B. Job logs that are no longer required can be deleted using the reports <strong>RSBTCDEL2</strong> and <strong>RSTS0024</strong>.</li>\r\n</ul>\r\n<ul>\r\n<li>C. Refer to SAP Notes <a target=\"_blank\" href=\"/notes/98995\">98995</a>&#x00A0;and <a target=\"_blank\" href=\"/notes/385283\">385283</a> for information on deleting HR objects.</li>\r\n</ul>\r\n<ul>\r\n<li>D. Use transaction <strong>FDTA</strong> to delete DTA objects from Financials (a description of how to do this is available in the online documentation). Display the list and use the Delete pushbutton.&#x00A0;Here, you can also check for possible inconsistencies (the table REGUT contains the reference to the TemSe object).</li>\r\n</ul>\r\n<ul>\r\n<li>E. To delete batch input logs that are no longer needed, use the report <strong>RSBDCREO</strong> (SAP Note <a target=\"_blank\" href=\"/notes/25219\">25219</a>).</li>\r\n</ul>\r\n</ul>\r\n<p><br />CAUTION:<br />If retention times for jobs and spool requests differ, then spool request numbers are sometimes reused. This may lead to invalid allocations. For more information, see SAP Notes <a target=\"_blank\" href=\"/notes/422136\">422136</a> and <a target=\"_blank\" href=\"/notes/1174127\">1174127</a>.<br /><br /></p>\r\n<ul>\r\n<li>3. Perform the consistency check.</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\"><br />A. Consistency check on TemSe objects and associated files</p>\r\n<p style=\"padding-left: 60px;\">The components batch, background, batch input, and spool (optional) write their TemSe data into the global file system. To find files for which there are no longer any TemSe objects, use the program <strong>RSTS0043</strong> (see SAP Note <a target=\"_blank\" href=\"/notes/936049\">936049</a>).</p>\r\n<p style=\"padding-left: 60px;\">To identify TemSe objects whose files cannot be accessed, use the report <strong>RSTS_SHOW_OBJ_WITHOUT_FILES</strong> (see SAP Note <a target=\"_blank\" href=\"/notes/2045227\">2045227</a>). If the report lists files, the cause for this must not necessarily be that they no longer physically exist. The reason for this may also be temporary network access problems or the fact that not all servers access the same global file directory.</p>\r\n<p style=\"padding-left: 60px;\">&#x00A0;<br />B. Spool consistency check</p>\r\n<p style=\"padding-left: 60px;\">The report <strong>RSPO1043</strong> (SAP Note <a target=\"_blank\" href=\"/notes/98065\">98065</a>) checks the tables TSP01, TSP02, TST01, and TST03 as well as other TSP* tables. It deletes inconsistencies only if it has found them in repeated runs. Therefore, you run no risk of deleting temporary inconsistencies.</p>\r\n<p style=\"padding-left: 60px;\">The report <strong>RSPO1042</strong> (SAP Note <a target=\"_blank\" href=\"/notes/1493058\">1493058</a>) finds and deletes ADS files for which there is no longer a spool request.<br /><br />C. Consistency check for batch input: Use the reports <strong>RSBDCCKA</strong> and <strong>RSBDCCKT</strong>. For more information, see SAP Note <a target=\"_blank\" href=\"/notes/175596\">175596</a>.</p>\r\n<p style=\"padding-left: 30px;\"><br /><br />Which reports should you schedule regularly?<br />You should run the following reports daily:<br /><strong>RSPO0041</strong> (or <strong>RSPO1041</strong>), <strong>RSBTCDEL2</strong>, <strong>RSBDCREO</strong>: Deleting outdated objects</p>\r\n<p style=\"padding-left: 30px;\"><strong>RSPO1043</strong> (daily), <strong>RSTS0024</strong> (weekly), <strong>RSTS0043</strong> (weekly)&#x00A0;for the consistency check.</p>\r\n<p style=\"padding-left: 30px;\">You can find further information about reorganization check programs and consistency check programs in SAP Note <a target=\"_blank\" href=\"/notes/16083\">16083</a>.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-ABA-SC (Dynpro and CUA engine)"}, {"Key": "Other Components", "Value": "BC-CCM-PRN-SPO (Spool System)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D025322)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D025322)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000048400/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000048400/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000048400/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000048400/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000048400/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000048400/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000048400/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000048400/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000048400/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "98995", "RefComponent": "PY-XX-BS", "RefTitle": "Deleting TemSe objects in the HR module", "RefUrl": "/notes/98995"}, {"RefNumber": "98065", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "Spool consistency check with RSPO1043 as of 4.0A", "RefUrl": "/notes/98065"}, {"RefNumber": "936049", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "TemSe: Consistency check, file system against TST01", "RefUrl": "/notes/936049"}, {"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478"}, {"RefNumber": "67055", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "Explanation of syslog message xrtab(?)->? for ????", "RefUrl": "/notes/67055"}, {"RefNumber": "504952", "RefComponent": "BC-CCM-PRN", "RefTitle": "Composite SAP Note for spool and print", "RefUrl": "/notes/504952"}, {"RefNumber": "48284", "RefComponent": "BC-CCM-PRN", "RefTitle": "System cannot generate any more spool requests", "RefUrl": "/notes/48284"}, {"RefNumber": "422136", "RefComponent": "BC-CCM-BTC", "RefTitle": "Incorrect spool assignment for the background step", "RefUrl": "/notes/422136"}, {"RefNumber": "41547", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "How does report RSPO0041 work?", "RefUrl": "/notes/41547"}, {"RefNumber": "406363", "RefComponent": "BC-CCM-PRN", "RefTitle": "Spool error: START_SPOOL SUBRC 20 spool job not found", "RefUrl": "/notes/406363"}, {"RefNumber": "385283", "RefComponent": "PY-MX", "RefTitle": "Deleting HR TemSe files", "RefUrl": "/notes/385283"}, {"RefNumber": "25219", "RefComponent": "BC-ABA-SC", "RefTitle": "RSBDCREO and parametrization", "RefUrl": "/notes/25219"}, {"RefNumber": "2045227", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "TemSe objects without files", "RefUrl": "/notes/2045227"}, {"RefNumber": "20176", "RefComponent": "BC-CCM-PRN", "RefTitle": "Where is the spool request saved?", "RefUrl": "/notes/20176"}, {"RefNumber": "1890546", "RefComponent": "BC-CCM-PRN", "RefTitle": "Spool requests larger than 2 GB", "RefUrl": "/notes/1890546"}, {"RefNumber": "175596", "RefComponent": "BC-ABA-SC", "RefTitle": "Conversion to new batch input log", "RefUrl": "/notes/175596"}, {"RefNumber": "16534", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "Job terminates due to SPOOL_INTERNAL_ERROR", "RefUrl": "/notes/16534"}, {"RefNumber": "16083", "RefComponent": "BC-CCM-BTC", "RefTitle": "Standard jobs, reorganization jobs", "RefUrl": "/notes/16083"}, {"RefNumber": "15960", "RefComponent": "FI-AP", "RefTitle": "Data medium exchange: where is the DME file?", "RefUrl": "/notes/15960"}, {"RefNumber": "1493058", "RefComponent": "BC-CCM-PRN", "RefTitle": "Orphaned ADS files", "RefUrl": "/notes/1493058"}, {"RefNumber": "147638", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "Deletion date of Temse object", "RefUrl": "/notes/147638"}, {"RefNumber": "140547", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "TST03 - adjustment of DB row length", "RefUrl": "/notes/140547"}, {"RefNumber": "1336", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "Spooler reports database I/0 errors", "RefUrl": "/notes/1336"}, {"RefNumber": "1174127", "RefComponent": "BC-CCM-BTC", "RefTitle": "Job refers to incorrect spool request", "RefUrl": "/notes/1174127"}, {"RefNumber": "11070", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "Space requirements of TemSe and spooler", "RefUrl": "/notes/11070"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1909093", "RefComponent": "BC-OP-NT", "RefTitle": "How to check file access problems on Windows - Netweaver", "RefUrl": "/notes/1909093 "}, {"RefNumber": "2649378", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "Table TST03 grow fast", "RefUrl": "/notes/2649378 "}, {"RefNumber": "2186590", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "Deadlocks on table TSP01", "RefUrl": "/notes/2186590 "}, {"RefNumber": "2457819", "RefComponent": "BC-CCM-PRN", "RefTitle": "Print and Output Management: Summary of Resource Links and Information", "RefUrl": "/notes/2457819 "}, {"RefNumber": "2675824", "RefComponent": "BC-CCM-PRN", "RefTitle": "Old Spools Not Deleted with RSPO0041 / RSPO1041 Variant Settings", "RefUrl": "/notes/2675824 "}, {"RefNumber": "2635724", "RefComponent": "BC-CCM-PRN", "RefTitle": "Monitor or Display Spool Range Limit and Status", "RefUrl": "/notes/2635724 "}, {"RefNumber": "2593715", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "(Internal KBA)  Spool: RT_INSERT Reports Error 128 for Table TSP02", "RefUrl": "/notes/2593715 "}, {"RefNumber": "2317433", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: Slow INSERT performance on table TST03", "RefUrl": "/notes/2317433 "}, {"RefNumber": "67055", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "Explanation of syslog message xrtab(?)->? for ????", "RefUrl": "/notes/67055 "}, {"RefNumber": "140547", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "TST03 - adjustment of DB row length", "RefUrl": "/notes/140547 "}, {"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478 "}, {"RefNumber": "67205", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: Copying large and productive clients", "RefUrl": "/notes/67205 "}, {"RefNumber": "22514", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: Error analysis for client copy", "RefUrl": "/notes/22514 "}, {"RefNumber": "489690", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: Copying large production clients", "RefUrl": "/notes/489690 "}, {"RefNumber": "16534", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "Job terminates due to SPOOL_INTERNAL_ERROR", "RefUrl": "/notes/16534 "}, {"RefNumber": "48284", "RefComponent": "BC-CCM-PRN", "RefTitle": "System cannot generate any more spool requests", "RefUrl": "/notes/48284 "}, {"RefNumber": "147638", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "Deletion date of Temse object", "RefUrl": "/notes/147638 "}, {"RefNumber": "16083", "RefComponent": "BC-CCM-BTC", "RefTitle": "Standard jobs, reorganization jobs", "RefUrl": "/notes/16083 "}, {"RefNumber": "25528", "RefComponent": "BC-CST-DP", "RefTitle": "Configuration of maximum work process runtime - parameter rdisp/max_wprun_time", "RefUrl": "/notes/25528 "}, {"RefNumber": "130978", "RefComponent": "BC-CCM-PRN", "RefTitle": "RSPO1041 - alternative to RSPO0041", "RefUrl": "/notes/130978 "}, {"RefNumber": "422136", "RefComponent": "BC-CCM-BTC", "RefTitle": "Incorrect spool assignment for the background step", "RefUrl": "/notes/422136 "}, {"RefNumber": "98065", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "Spool consistency check with RSPO1043 as of 4.0A", "RefUrl": "/notes/98065 "}, {"RefNumber": "11070", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "Space requirements of TemSe and spooler", "RefUrl": "/notes/11070 "}, {"RefNumber": "504952", "RefComponent": "BC-CCM-PRN", "RefTitle": "Composite SAP Note for spool and print", "RefUrl": "/notes/504952 "}, {"RefNumber": "41547", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "How does report RSPO0041 work?", "RefUrl": "/notes/41547 "}, {"RefNumber": "98995", "RefComponent": "PY-XX-BS", "RefTitle": "Deleting TemSe objects in the HR module", "RefUrl": "/notes/98995 "}, {"RefNumber": "503499", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "TemSe consistency check: Inconsistencies cannot be deleted", "RefUrl": "/notes/503499 "}, {"RefNumber": "406363", "RefComponent": "BC-CCM-PRN", "RefTitle": "Spool error: START_SPOOL SUBRC 20 spool job not found", "RefUrl": "/notes/406363 "}, {"RefNumber": "1336", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "Spooler reports database I/0 errors", "RefUrl": "/notes/1336 "}, {"RefNumber": "385283", "RefComponent": "PY-MX", "RefTitle": "Deleting HR TemSe files", "RefUrl": "/notes/385283 "}, {"RefNumber": "15960", "RefComponent": "FI-AP", "RefTitle": "Data medium exchange: where is the DME file?", "RefUrl": "/notes/15960 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "46B", "To": "46D", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "620", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 46B", "SupportPackage": "SAPKB46B50", "URL": "/supportpackage/SAPKB46B50"}, {"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C41", "URL": "/supportpackage/SAPKB46C41"}, {"SoftwareComponentVersion": "SAP_BASIS 46D", "SupportPackage": "SAPKB46D30", "URL": "/supportpackage/SAPKB46D30"}, {"SoftwareComponentVersion": "SAP_BASIS 610", "SupportPackage": "SAPKB61029", "URL": "/supportpackage/SAPKB61029"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62017", "URL": "/supportpackage/SAPKB62017"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}