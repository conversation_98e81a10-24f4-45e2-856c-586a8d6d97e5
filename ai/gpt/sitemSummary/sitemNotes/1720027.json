{"Request": {"Number": "1720027", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 635, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017441702017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001720027?language=E&token=08C53E6700E94E16DB11AF0F3D2BFB01"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001720027", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1720027"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.06.2013"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-HU-PS"}, "SAPComponentKeyText": {"_label": "Component", "value": "use FI-LOC-PS-HU"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hungary", "value": "XX-CSC-HU", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-HU*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "use FI-LOC-PS-HU", "value": "XX-CSC-HU-PS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-HU-PS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1720027 - New Public Sector Reports Hungary - Reporting Framework"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The basis of the reports for Hungary Public Section is implemented Reporting Framework (see related Notes) and the Data Connector. The Data Connector is responsible for data gathering. The Reporting Framework is responsible for data displaying.<br /><br />Implementation consists of the following steps:</p> <UL><LI>Configuration of the Reporting Framework</LI></UL> <UL><LI>Definition of report structures in the Data Dictionary</LI></UL> <UL><LI>The Reporting Framework events implementation</LI></UL> <UL><LI>Configuration of the Data Connector</LI></UL> <p></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Reporting Framework; Public Sector Hungaria; Transaction REP_EAPS_DATCON; Maintenance View REP_EAPS_DATCOVC; Switch GLO_REP_EAPS_SFWS_01; Business Function PSM_REP_LOC_01</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>On above of Reporting Framework in Software Component SAP_FIN (starting Enhacement Package 7) the solution is part of Software Component EA-PS.<br />Make sure you have activated Business Function PSM_REP_LOC_01 with all its Switch BC-Sets.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Please install corresponding Support Pack.<br /><br />The following transactions will be used to run the reports for Hungary public sector and for their customizing.<br /></p> <UL><LI>Run reporting:</LI></UL> <UL><UL><LI>IDREPFW_REP - Reporting Framework</LI></UL></UL> <p></p> <UL><LI>Customizing transactions:</LI></UL> <UL><UL><LI>IDREPFW_EVN_MGMT - Events Management</LI></UL></UL> <UL><UL><LI>REP_EAPS_DATCON - Data Connector Management</LI></UL></UL> <UL><UL><LI>SIMGH - Settings for Reporting Framework</LI></UL></UL> <p><br />Implemented reports:</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>01</TD><TD> Accounting Balance Sheet</TD></TR> <TR><TD>21</TD><TD> Expenditure per Activity</TD></TR> <TR><TD>22</TD><TD> Revenues per Activity</TD></TR> <TR><TD>23</TD><TD> Reconciliation Budget Appropriations</TD></TR> <TR><TD>24</TD><TD> Cash Flow Reconciliation</TD></TR> <TR><TD>42</TD><TD> Residual Budget Trends</TD></TR> <TR><TD>98</TD><TD> Central Budgetary Report</TD></TR> </TABLE> <p><br />These reports have different data sources:</p> <UL><LI>Source: RFBILA report</LI></UL> <UL><UL><LI>01&#x00A0;&#x00A0;&#x00A0;&#x00A0;Accounting Balance Sheet</LI></UL></UL> <UL><UL><LI>24&#x00A0;&#x00A0;&#x00A0;&#x00A0;Cash Flow Reconciliation</LI></UL></UL> <UL><UL><LI>42&#x00A0;&#x00A0;&#x00A0;&#x00A0;Residual Budget Trends</LI></UL></UL> <UL><LI>Source: Controlling</LI></UL> <UL><UL><LI>21&#x00A0;&#x00A0;&#x00A0;&#x00A0;Expenditure per Activity</LI></UL></UL> <UL><UL><LI>22&#x00A0;&#x00A0;&#x00A0;&#x00A0;Revenues per Activity</LI></UL></UL> <UL><LI>Source: FI</LI></UL> <UL><UL><LI>23&#x00A0;&#x00A0;&#x00A0;&#x00A0;Reconciliation Budget Appropriations</LI></UL></UL> <UL><UL><LI>98&#x00A0;&#x00A0;&#x00A0;&#x00A0;Central Budgetary Report</LI></UL></UL> <p><br />Each of these reports has its header with a couple of values which should be printed at top of a page. Sources of these header values are:</p> <UL><UL><LI>constants</LI></UL></UL> <UL><UL><LI>selection screen</LI></UL></UL> <UL><UL><LI>additional specifications for Company Code</LI></UL></UL> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I069660)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I041621)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001720027/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001720027/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001720027/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001720027/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001720027/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001720027/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001720027/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001720027/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001720027/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1916461", "RefComponent": "XX-CSC-HU-PS", "RefTitle": "PS-HU: Move of BF Objects", "RefUrl": "/notes/1916461"}, {"RefNumber": "1892068", "RefComponent": "XX-CSC-HU-PS", "RefTitle": "PS-HU: Change in Business Function Assignment", "RefUrl": "/notes/1892068"}, {"RefNumber": "1703201", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Reporting Framework - New Common Tool", "RefUrl": "/notes/1703201"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3347307", "RefComponent": "FI-LOC-PS-HU", "RefTitle": "S4TWL - Public Sector Localization for Hungary Deprecation", "RefUrl": "/notes/3347307 "}, {"RefNumber": "2044963", "RefComponent": "XX-CSC-HU-PS", "RefTitle": "PS-HU: Customer Usage Measure", "RefUrl": "/notes/2044963 "}, {"RefNumber": "1987452", "RefComponent": "XX-CSC-HU-PS", "RefTitle": "PS-HU: Incorrect Calculation Types in Data Connector", "RefUrl": "/notes/1987452 "}, {"RefNumber": "1926226", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Data Connector for Reporting Framework Enhancement", "RefUrl": "/notes/1926226 "}, {"RefNumber": "1871746", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "PS CZ: New Reports Czech Rep. - Reporting Framework", "RefUrl": "/notes/1871746 "}, {"RefNumber": "1963308", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "PS CZ: New Reports Czech Rep. - Manual Activities", "RefUrl": "/notes/1963308 "}, {"RefNumber": "1946331", "RefComponent": "XX-CSC-HU-PS", "RefTitle": "PS-HU: Enable Report Group PSHU", "RefUrl": "/notes/1946331 "}, {"RefNumber": "1892068", "RefComponent": "XX-CSC-HU-PS", "RefTitle": "PS-HU: Change in Business Function Assignment", "RefUrl": "/notes/1892068 "}, {"RefNumber": "1916461", "RefComponent": "XX-CSC-HU-PS", "RefTitle": "PS-HU: Move of BF Objects", "RefUrl": "/notes/1916461 "}, {"RefNumber": "1703201", "RefComponent": "XX-CSC-CZ-PS", "RefTitle": "Rep.Frwk. - Reporting Framework - New Common Tool", "RefUrl": "/notes/1703201 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-PS", "From": "617", "To": "617", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-PS 617", "SupportPackage": "SAPK-61701INEAPS", "URL": "/supportpackage/SAPK-61701INEAPS"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}