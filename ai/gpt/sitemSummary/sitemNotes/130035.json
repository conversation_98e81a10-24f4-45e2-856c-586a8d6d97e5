{"Request": {"Number": "130035", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 289, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014624742017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000130035?language=E&token=4268A6060286DDAF99FFA09184C31606"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000130035", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000130035/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "130035"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.02.2001"}, "SAPComponentKey": {"_label": "Component", "value": "PA-PA-XX"}, "SAPComponentKeyText": {"_label": "Component", "value": "General Parts"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Personnel Management", "value": "PA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Personnel Administration", "value": "PA-PA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PA-PA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Parts", "value": "PA-PA-XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PA-PA-XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "130035 - Authorization check with P_PERNR incorrect."}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Authorization check with P_PERNR does not return the required authorizations, that is the user can perform more or fewer activities in the system than expected.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>P_PERNR, personnel number check.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is most frequently caused by incorrect settings in the authorization check. Since the relevant information is scattered in the documentation, this note was created.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Please note the following instructions:<br /><br />1) First of all, if the problems occur user-independently, check whether the authorization check with P_PERNR is activated in the INCLUDE MPPAUTSW. The switch is active if it is set to '1'. Also note the following line. &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; P_PERNR(1) VALUE '1',&#x00A0;&#x00A0;\"check by using T513A<br /><br />2) If the problems occur user-independently in reporting, it could be that the authorization check with P_PERNR is unavailable in your release. In that case, implement note 124949 in your system.<br /><br />3) If, in addition to the authorization check with P_PERNR, you have activated authorization checks with P_ORGIN and/or P_ORGXX in your system, your users need the following minimum authorization for P_ORGIN and/or P_ORGXX in reporting in addition to the authorizations for P_PERNR: AUTHC = R, INFTY = \"as for P_PERNR\". Leave all other fields ready for entry.<br /><br />4) Check whether the assignment of users to personnel numbers in table T513A was actually maintained. You can check this with transaction SM30. To do that, use maintenance view V_T513A, or, from release 4.6, infotype 0105 subtype 0001.<br /><br />5) Check whether the users authorizations are all present and active in his or her user master record. Note that the user must log on again before the system takes any changed authorizations into account.<br /><br />6) It is important to understand that authorizations for the object P_PERNR concern exclusively the personnel number assigned to a user (in accordance with table T513A). No other personnel numbers are affected by the authorization specifications for P_PERNR. Should you find, for example, that a user can perform activities for personnel numbers other than his or her own, this has nothing to do with the authorizations for P_PERNR. For this reason, SAP suggests that you first set up the authorizations that a user is supposed to have without taking P_PERNR into account, and then use P_PERNR to adjust the authorizations for the assigned personnel number as required.<br />A very large number of problem notifications regarding the object P_PERNR are based on the assumption that authorizations for P_PERNR have an influence on other personnel numbers. This is definitely not the case.<br /><br />7) The \"Interpretation of an assigned personnel number\" field (PSIGN) has the following useful specifications: \"E\" and \"I\". All other specifications are ignored by the system or cause an undefined system response. Since the general explanation of this fact often causes confusion, note the following examples.<br /><br />7a) You do not want a user to be dealt with separately by P_PERNR. In that case, do not give the user any authorizations at all for P_PERNR. If you give him or her an authorization with AUTHC = *, PSIGN = *, INFTY = *, SUBTY = *, the result of the authorization check is undefined for every infotype/subtype and every authorization level. Usually, the system denies the user any authorization for access to his or her own data. If you wish to achieve this effect deliberately, see example 7b).<br /><br />7b) You want to make sure that a user cannot access his or her own data, although he or she is allowed do everything else. To this end you assign an authorization with AUTHC = *, PSIGN = E, INFTY = *, SUBTY = *.<br /><br />7 c) You want to make sure that a user has read access only to his or her own data, although he or she is allowed to do everything else. To this end you assign an authorization with AUTHC = W,S,E,D, PSIGN = E, INFTY = *, SUBTY = *. With that you remove all conceivable authorizations for write access to the users own personnel number.<br /><br />7d) You want to make sure that a user has read access for his or her own personnel number, although he or she has no other authorizations. To this end you assign an authorization with AUTHC = R,M, PSIGN = I, INFTY = *, SUBTY = *.<br /><br />7e) A user has 2 authorizations for the object P_PERNR. First AUTHC = *, PSIGN = I, INFTY = 0008,0014, SUBTY = *, second AUTHC = *, PSIGN = E, INFTY = 0014,0015, SUBTY = *. This means that, independently of the authorizations for other authorization objects, the user can maintain infotype 0008 for his or her own personnel number and neither maintain nor display infotype 0015. The system response for infotype 0014 is undefined, there for PSIGN both \"E\" and \"\"I\"\" are possible. Usually the system prefers the interpretation \"E\".<br /><br />8) Use transaction SU53 to analyze authorization problems if you have comprehensive experience with authorization checks, since this transaction displays only the last aborted authorization check. A better option is to use transaction ST01 to get an authorization trace. Always start the authorization trace before you reproduce your problem. In order to start with empty internal buffers, make sure you exit the corresponding transaction completely. For example, if you have started transaction PA30, exit it completely before you start the authorization trace with ST01.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "PA-PA (Personnel Administration)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D024826)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000130035/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000130035/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000130035/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000130035/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000130035/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000130035/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000130035/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000130035/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000130035/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "155650", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/155650"}, {"RefNumber": "1261193", "RefComponent": "BC-SEC-AUT", "RefTitle": "Composite SAP Note : HCM Authorizations Documentation", "RefUrl": "/notes/1261193"}, {"RefNumber": "124949", "RefComponent": "PA-PA", "RefTitle": "Logical database SAPDBPNP does not check P_PERNR", "RefUrl": "/notes/124949"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1261193", "RefComponent": "BC-SEC-AUT", "RefTitle": "Composite SAP Note : HCM Authorizations Documentation", "RefUrl": "/notes/1261193 "}, {"RefNumber": "124949", "RefComponent": "PA-PA", "RefTitle": "Logical database SAPDBPNP does not check P_PERNR", "RefUrl": "/notes/124949 "}, {"RefNumber": "155650", "RefComponent": "PA-PA-XX", "RefTitle": "Authorization problems", "RefUrl": "/notes/155650 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "310", "To": "31I", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}