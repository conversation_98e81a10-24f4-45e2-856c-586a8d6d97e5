{"Request": {"Number": "458676", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 399, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015142782017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000458676?language=E&token=F9DA3785FFE270C49A9E22EAF8FC3FB2"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000458676", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000458676/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "458676"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 18}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.03.2003"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-AFS"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW only - Apparel and Footwear Solution"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Apparel and Footwear Solution", "value": "BW-BCT-AFS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-AFS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "458676 - Enhancing extractors with AFS-specific fields"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You want to extract data from an AFS system higher than or equal to AFS 3.0B into a BW system in order to supply the AFS-specific InfoCubes delivered as of BW 3.0B, for example.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>AFS data extraction, /AFS/RMCSBWXP, elementary fields<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The standard extractors do not contain any AFS-specific fields.Note the prerequisites contained in note 502045 with regard to using AFS and SAP BW.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>You have connected an AFS 3.0B system or higher with a BW 3.0B system or higher and want to supply the AFS-specific InfoProvider contained in the Business Content.<br />Enhancing the extractors, which are delivered without AFS fields as standard, using the required AFS fields involves the following steps (it is absolutely essential that you adhere to this sequence).<br /><br />Overview:</p> <OL>1. Transfer and generate the standard (non AFS-specfic) DataSources.</OL> <OL>2. Transfer WITHOUT generating the AFS-specific fields.</OL> <OL>3. If necessary, enhance the DataSources with your own fields.</OL> <OL>4. Generate the DataSources.</OL> <OL>5. Activate the update.</OL> <OL>6. Replicate the DataSources into BW</OL> <OL>7. Maintain the transfer rules in BW.</OL> <p><br /><br />Details:<br />On 1.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Copy and activate the DataSources delivered by SAP with the Business Content that you want to use at a later stage. You can do this using transaction SBIW: Business Information Warehouse --&gt; Business Content DataSources --&gt; Transfer Business Content DataSources or directly in transaction RSA5.<br />IMPORTANT: Do NOT perform this action again at a later stage. Otherwise, you may delete fields that were added by AFS or by yourself.<br /><br />On 2.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Use transaction SE38 to start the /AFS/RMCSBWXP report. On the selection screen, you can select the applications (for example, purchasing, inventory controlling,...)that you want to use to enhance the extractors with AFS fields.The following extractors (DataSources) are enhanced:</p> <OL><OL>a) Application 02 (Purchasing):</OL></OL> <UL><UL><LI>2LIS_02_SCL (Purchasing extraction - schedule line)</LI></UL></UL> <OL><OL>b) Application 03 (Stock controlling):</OL></OL> <UL><UL><LI>2LIS_03_BF (MM extraction - material movements)</LI></UL></UL> <UL><UL><LI>2LIS_03_UM (MM extraction - revaluations)</LI></UL></UL> <OL><OL>c) Application 11 (SD sales):</OL></OL> <UL><UL><LI>2LIS_11_VASCL (Extraction of SD sales document delivery schedule)</LI></UL></UL> <UL><UL><LI>2LIS_11_V_SCL (Extraction of SD sales schedule line allocation)</LI></UL></UL> <OL><OL>d) Application 12 (SU shipping):</OL></OL> <UL><UL><LI>2LIS_12_VCITM (Extraction of LE shipping line item)</LI></UL></UL> <OL><OL>e) Application 13 (SD billing):</OL></OL> <UL><UL><LI>2LIS_13_VDITM (Extraction of SD billing line item)</LI></UL></UL> <OL><OL>f) AF application (AFS production):</OL></OL> <UL><UL><LI>2LIS_AF_PABDSI (Extraction of AFS/PP production order component view)</LI></UL></UL> <UL><UL><LI>2LIS_AF_PABSSI (Extraction of AFS/PP production order material view)</LI></UL></UL> <p><br />Refer here to the documentation for this report (information button) as well as the log messages displayed after you execute the report.<br /><br />On 3.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Executing the aforementioned report ensures an initial download of the AFS contents delivered with BW 3.0B. In addition, as in the standard case, you can select additional fields from the MC structures (LIS communication structure) for the extraction using the Logistic Extraction Customizing Cockpit (transaction LBWE). You will find more information about the Logistic Extraction Customizing Cockpit by clicking on the documentation for this cockpit from the menu option 'Business Information Warehouse --&gt; Settings for Application-Specific DataSources --&gt; Logistics' in transaction SBIW.<br /><br />On 4.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Generate the required DataSources in the Logistics Extraction Customizing Cockpit (transaction LBWE) by clicking on the relevant DataSource.IMPORTANT:you have to manually set the inversion flag (+/-) for the following fields:</p> <OL><OL>g) 2LIS_02_SCL DataSource:</OL></OL> <UL><UL><LI>J_3AABRMNG Quantity</LI></UL></UL> <UL><UL><LI>J_3ABAMNG Quantity</LI></UL></UL> <UL><UL><LI>J_3ABSMNG Quantity ordered</LI></UL></UL> <UL><UL><LI>J_3AEKABMG Quantity</LI></UL></UL> <UL><UL><LI>J_3AKOMNG Quantity</LI></UL></UL> <UL><UL><LI>J_3ANETW Net value</LI></UL></UL> <OL><OL>h) 2LIS_11_VASCL DataSource</OL></OL> <UL><UL><LI>/AFS/BRGEW Gross weight</LI></UL></UL> <UL><UL><LI>/AFS/NTGEW Net weight</LI></UL></UL> <UL><UL><LI>/AFS/VOLUM Volume</LI></UL></UL> <UL><UL><LI>J_3ANETW Net value</LI></UL></UL> <OL><OL>i) 2LIS_13_VDITM DataSource:</OL></OL> <UL><UL><LI>J_3AFKIMG Quantity actually invoiced (in base units of measure)</LI></UL></UL> <UL><UL><LI>J_3AFKLMG Quantity invoiced in delivery units</LI></UL></UL> <UL><UL><LI>J_3ANETW Net value of the billing item in the document currency</LI></UL></UL> <OL><OL>j) 2LIS_AF_PABDSI DataSource</OL></OL> <UL><UL><LI>DMBTR Amount in local currency</LI></UL></UL> <UL><UL><LI>J_3AGLMG AFS Quantity issued</LI></UL></UL> <OL><OL>k) 2LIS_AF_PABSSI DataSource</OL></OL> <UL><UL><LI>/AFS/SI_LIEF Delivery date target/actual variance</LI></UL></UL> <UL><UL><LI>AMENG Gross quantity in BME</LI></UL></UL> <UL><UL><LI>J_3AGLMG AFS Quantity delivered</LI></UL></UL> <UL><UL><LI>J_3AXMNGA AFS Total scrap quantity confirmed</LI></UL></UL> <UL><UL><LI>PMENG Gross weight in BME</LI></UL></UL> <p><br />On 5<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Activate the update by clicking on the corresponding entry for the DataSource in the update column.<br /><br />On 6.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Replicate the extended DataSources into BW and assign the corresponding AFS-specific InfoSources to them (in the BW system).<br /><br />On 7.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You must only carry out this step for BW with a release lower than BW 3.2:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You must then manually create the transfer rules between the standard DataSource (2LIS...)and the corresponding AFS-specific InfoSources (2AF...).These mapping rules are included in the BW documentation (Business Content --&gt; Industry Solutions --&gt; Apparel and Footwear --&gt; Content --&gt; InfoSources in AFS).Note 520006 contains additional information on the transfer rules.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "SE38"}, {"Key": "Transaction codes", "Value": "SP03"}, {"Key": "Transaction codes", "Value": "SBIW"}, {"Key": "Responsible                                                                                         ", "Value": "I025887"}, {"Key": "Processor                                                                                           ", "Value": "I025887"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000458676/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000458676/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000458676/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000458676/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000458676/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000458676/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000458676/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000458676/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000458676/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "717229", "RefComponent": "BW-BCT-AFS", "RefTitle": "Using AFS 5.0 and SAP BW", "RefUrl": "/notes/717229"}, {"RefNumber": "580694", "RefComponent": "BW-BCT-AFS", "RefTitle": "Include changes to /AFS/RMCSBWXP in transport request", "RefUrl": "/notes/580694"}, {"RefNumber": "520006", "RefComponent": "BW-BCT-AFS", "RefTitle": "Transfer rules AFS-BW", "RefUrl": "/notes/520006"}, {"RefNumber": "502045", "RefComponent": "BW-BCT-AFS", "RefTitle": "Using AFS and SAP BW", "RefUrl": "/notes/502045"}, {"RefNumber": "459406", "RefComponent": "BW-BCT-AFS", "RefTitle": "Status update for the Apparel & Footwear Content BW 3.0A", "RefUrl": "/notes/459406"}, {"RefNumber": "458694", "RefComponent": "BW-BCT-AFS", "RefTitle": "AFS-PP contents", "RefUrl": "/notes/458694"}, {"RefNumber": "458610", "RefComponent": "BW-BCT-AFS", "RefTitle": "AFS BW: Stock initialization", "RefUrl": "/notes/458610"}, {"RefNumber": "425995", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.0A Support Package 03", "RefUrl": "/notes/425995"}, {"RefNumber": "425993", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.0A Support Package 02", "RefUrl": "/notes/425993"}, {"RefNumber": "422644", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Additions to installation/delta-upgr of AFS V3.0B", "RefUrl": "/notes/422644"}, {"RefNumber": "1483164", "RefComponent": "IS-AFS", "RefTitle": "Installation / Delta upgrade of SAP AFS 6.5 - P3A V605", "RefUrl": "/notes/1483164"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "586163", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Composite SAP Note for SAP ERP Inventory Management in SAP BW", "RefUrl": "/notes/586163 "}, {"RefNumber": "1483164", "RefComponent": "IS-AFS", "RefTitle": "Installation / Delta upgrade of SAP AFS 6.5 - P3A V605", "RefUrl": "/notes/1483164 "}, {"RefNumber": "717229", "RefComponent": "BW-BCT-AFS", "RefTitle": "Using AFS 5.0 and SAP BW", "RefUrl": "/notes/717229 "}, {"RefNumber": "458610", "RefComponent": "BW-BCT-AFS", "RefTitle": "AFS BW: Stock initialization", "RefUrl": "/notes/458610 "}, {"RefNumber": "425995", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.0A Support Package 03", "RefUrl": "/notes/425995 "}, {"RefNumber": "425993", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.0A Support Package 02", "RefUrl": "/notes/425993 "}, {"RefNumber": "422644", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Additions to installation/delta-upgr of AFS V3.0B", "RefUrl": "/notes/422644 "}, {"RefNumber": "502045", "RefComponent": "BW-BCT-AFS", "RefTitle": "Using AFS and SAP BW", "RefUrl": "/notes/502045 "}, {"RefNumber": "580694", "RefComponent": "BW-BCT-AFS", "RefTitle": "Include changes to /AFS/RMCSBWXP in transport request", "RefUrl": "/notes/580694 "}, {"RefNumber": "520006", "RefComponent": "BW-BCT-AFS", "RefTitle": "Transfer rules AFS-BW", "RefUrl": "/notes/520006 "}, {"RefNumber": "458694", "RefComponent": "BW-BCT-AFS", "RefTitle": "AFS-PP contents", "RefUrl": "/notes/458694 "}, {"RefNumber": "459406", "RefComponent": "BW-BCT-AFS", "RefTitle": "Status update for the Apparel & Footwear Content BW 3.0A", "RefUrl": "/notes/459406 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "P3A", "From": "V3.0B", "To": "V3.0B", "Subsequent": ""}, {"SoftwareComponent": "P3A", "From": "V500", "To": "V500", "Subsequent": ""}, {"SoftwareComponent": "P3A", "From": "V600", "To": "V600", "Subsequent": ""}, {"SoftwareComponent": "P3A", "From": "V603", "To": "V603", "Subsequent": ""}, {"SoftwareComponent": "P3A", "From": "V604", "To": "V604", "Subsequent": ""}, {"SoftwareComponent": "P3A", "From": "V605", "To": "V605", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}