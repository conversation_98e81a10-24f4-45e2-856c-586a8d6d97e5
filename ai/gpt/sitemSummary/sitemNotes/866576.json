{"Request": {"Number": "866576", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 514, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000004904522017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000866576?language=E&token=2960F8D0DCA998793FB2D69EB40B3378"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000866576", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000866576/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "866576"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 21}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "19.05.2009"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SDD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Service Data Download"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Data Download", "value": "SV-SMG-SDD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SDD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "866576 - Composite note: ST-PI modules for MS SQL Server"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note describes errors in and changes to the ST-PI download modules for functions relevant to the SQL Server.<br /><br />1. The data collector (SDCC) calls the logical function module<br />MSQ_DBCC_DOWNLOAD. This calls function module<br /><br />1.1. The function module /SDF/MSS_DBCC_DOWNLOAD returns ABAP runtime error DBIF_DSQL2_SQL_ERROR:<br />An SQL error occurred when executing Native SQL<br />and writes the following entry into the SysLog:<br />BY2 Database error 50001 at FET<br />BY0 &gt; xpsql.cpp: Error 997 from GetProxyAccount on line 604<br />R68 Perform rollback<br />AB0 Run-time error \"DBIF_DSQL2_SQL_ERROR\" occurred<br />1.2. The function module returns an empty results table CHECKDBRESULT.<br /><br />2. The data collector (SDCC or SDCCN) calls the logical function module MSS_PERFHIST. This calls function module /SDF/MSS_PERFHIST. The function module /SDF/MSS_PERFHIST returns ABAP runtime error CALL_FUNCTION_CONFLICT_LENG:<br />Type conflict when calling a function module (field length)<br /><br />3. The data collector calls logical function module MSQ_VTLFILESTATS. This calls /SDF/MSS_VIRTFILESTATS. If it is called several times, multiple entries are created in results table VTLFILE.<br /><br />4. The data collector (SDCC or SDCCN) calls the logical function module SQL_MSSQL_ANALYSIS_FAST. Depending on your SAP release, this calls SQL_MSSQL_ANALYSIS_FAST or /SDF/SQL_MSSQL_ANALYSIS_FAST. The results table MSSTATS_LIST does not contain the statement type (for example, SELECT, INSERT ...).<br /><br />5. The data collector (SDCC or SDCCN) calls the logical function module MSQ_SPINFO_DOWNLOAD. This calls /SDF/MSS_SPECIAL_INFO. Function module /SDF/MSS_SPECIAL_INFO writes errors such as the following in the system log:<br />1. Database error 50000 occurred in FET<br />&gt; The object 'dbo.DBSTATHMSS' does not exist in database<br />Database error 50000 occurred<br />or<br />2. Database error 50000 occurred in FET<br />&gt; The object '&lt;sid&gt;.syscomments' does not exist in database &lt;SID&gt;<br />Database error 50000 occurred<br /><br />6. The data collector (SDCC or SDCCN) calls the logical function module MSS_DBCC_FILEHEADER. This calls /SDF/MSS_DBCC_FILEHEADER. The function module writes the error message: User '&lt;sid&gt;' does not have permission to run DBCC fileheader in results table DBCCSTATUS.<br /><br />7. The data collector (SDCC or SDCCN) calls the logical function module MSQ_SHOW_EXTENT_DOWNLOAD. This calls /SDF/MSS_EXTENT_DISTRIBUTION. The function module writes the error message: User '&lt;sid&gt;' does not have permission to run DBCC extentinfo in results tables DISTRIBUTION and DISTSIMPLE.<br /><br />8. The data collector (SDCC or SDCCN) calls logical function module MSQ_PERFHIST. This calls function module<br />/SDF/MSS_PERFHIST, which in turn calls function module MSS_SELECT_MSQSNAP. The call returns ABAP runtime error CALL_FUNCTION_UC_STRUCT.<br /><br />9. The data collector (SDCC or SDCCN) calls the logical function module MSQ_SPINFO_DOWNLOAD. This calls the function module /SDF/MSS_SPECIAL_INFO, which in turn calls the function module MSS_GET_TABLE_SIZE_INFO. The call returns ABAP runtime error CALL_FUNCTION_CONFLICT_LENG.<br /><br />10. Function module /SDF/MSS_STATEMENT_DETAILS delivers cryptic table names (for example, @P0001) in results table TABLELIST.<br /><br />11. The data collector (SDCC or SDCCN) calls logical function module MSQ_DBCC_DOWNLOAD. Depending upon your SAP release, this calls function module /SDF/MSS_DBCC_CHECKDB or MSQ_DBCC_DOWNLOAD. The function modules call MSS_READ_TEXT_FILE for reading a file at operating system level. This access is disallowed and the SDCC log displays error 'MSS_TOOLS_AUTHORITY: Illegal program name SAPLSFMSS_ANALYZE on &lt;instancename&gt;'.<br /><br />12. The data collector (SDCC or SDCCN) calls logical function module MSS_TEXT_IMAGE_FIELDS. This calls function module /SDF/MSS_TEXT_IMAGE_FIELDS. SQL Server 2005 does not return a result. The 'Text and Image Fields' indicator is always empty in EarlyWatch Alert.<br /><br />13. The data collector (SDCC or SDCCN) calls logical function module MSQ_PERFHIST. Depending on your SAP release, this calls function module MSQ_PERFHIST_DOWNLOAD or /SDF/MSS_PERFHIST. Depending on the data that is read, function module /SDF/MSS_PERFHIST returns ABAP runtime error BCD_OVERFLOW.<br /><br />14. The data collector (SDCC or SDCCN) calls the logical function module SQL_MSSQL_ANALYSIS_FAST. Depending on your SAP release, this calls SQL_MSSQL_ANALYSIS_FAST or /SDF/SQL_MSSQL_ANALYSIS_FAST. Depending on the data that is read, the function module returns the ABAP runtime error COMPUTE_BCD_OVERFLOW. In the SDCC or SDCCN log, the system displays the following error:&#x00A0;&#x00A0;Overflow with arithmetic operation (type P) in program /SDF/SAPLRI_SQLSERVER.<br /><br />15. The data collector (SDCC) calls the logical function module MSQ_DBCC_DOWNLOAD,&#x00A0;&#x00A0;which calls the function module /SDF/MSS_DBCC_CHECKDB. The following errors occur:<br /><br />15.1. The function module /SDF/MSS_DBCC_CHECKDB returns the following entry in the SAP syslog:<br /><br />Database error 4861 at OPC<br />&gt; Cannot bulk load because the file \"C:\\Program Files\\Microsof<br />&gt; SQL Server\\MSSQL.1\\MSSQL\\CCMS_CHECK_DB_HIST_2007.txt\" could<br />&gt; not be opened. Operating system error code 32(The process<br />&gt; cannot access the file because it is being used by another<br />&gt; process.).<br />Database error 4861<br /><br />15.2. The function module /SDF/MSS_DBCC_CHECKDB returns the following item in the SAP syslog:<br /><br />Database error 4861 at OPC<br />&gt; Cannot bulk load because the file \"C:\\Program Files\\Microsof<br />&gt; SQL Server\\MSSQL.1\\MSSQL\\CCMS_CHECK_DB_HIST_2007.txt\" could<br />&gt; not be opened. Operating system error code 5(Access is<br />&gt; denied.).<br />Database error 4861<br /><br />16. The function module /SDF/MSS_DBCC_CHECKDB does not return a result, even though the file CCMS_CHECK_DB_HIST_&lt;yyyy&gt;.txt is located in the SQL Server installation directory and contains entries<br /><br />or<br /><br />The module delivers the following error message in the SAP syslog:<br /><br />Database error 4860 at OPC<br />&gt; Cannot bulk load. The file \"C:\\Program Files\\Microsoft SQL<br />&gt; Server\\MSSQL.1\\MSSQL\\CCMS_CHECK_DB_HIST_2009.txt\" does not<br />&gt; exist.<br />Database error 4860</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SDCC SDCCN xp_cmdshell Error 50001 /SDF/SAPLRI_SQLSERVER syscomment DBSTATHMSS MSS_GET_TABLE_SIZE_INFO sap_get_table_size_info 'syscomments', MSS_TOOLS_AUTHORITY</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>1.1. The error occurs in SAP Release 6.20 and higher. Function module /SDF/MSS_DBCC_DOWNLOAD calls the statement: master..xp_cmdshell. This command can be executed only by 'dbo' users. In a schema-based system (meaning that the database user is not 'dbo'), the call causes an ABAP runtime error.<br />1.2. The error occurs in SAP Release 6.20. The function module looks for the \"CCMS_CHECK_DB_HIST.txt\" file in the SQL Server installation directory. However, this file has been renamed as a file of the format \"CCMS_CHECK_DB_HIST_yyyy.txt\", where yyyy is the current year.<br /><br />2. The error occurs only in SAP releases higher than 6.20. Function module /SDF/MSS_PERFHIST calls function module MSS_SELECT_MSQSNAP. Passed parameter /SDF/MSSPFCACH does not correspond to type MSS_PFCACH.<br /><br />3. The error occurs as of SAP Release 6.20. Results table VTLFILE is not correctly reset.<br /><br />4. The error occurs as of SAP Release 6.20.<br /><br />5. Function module /SDF/MSS_SPECIAL_INFO calls function module GET_TABSTATS_MSQ for tables \"DBSTATHMSS\" and \"syscomments\". The reasons for the errors listed above are:<br />1. The DBSTATHMSS table does not exist in the ABAP Data Dictionary.<br />2. The syscomments table does not exist in the specified database schema. In the error described above, the database schema is specified with &lt;sid&gt;.<br /><br />6. The error occurs as of SAP Release 6.20. Function module /SDF/MSS_DBCC_FILEHEADER calls the command \"dbcc fileheader('&lt;SID&gt;')\" on the database. This command can be executed only by database user dbo. In schema-based systems in which the database user is not dbo (but &lt;sid&gt;), the error described here occurs.<br /><br />7. The error occurs as of SAP Release 6.20. Function module /SDF/MSS_EXTENT_DISTRIBUTION calls the command \"dbcc extentinfo(&lt;database number&gt;, &lt;object number&gt;)\" on the database. This command can be executed only by database user dbo. In schema-based systems in which the database user is not dbo (but &lt;sid&gt;), the error described here occurs.<br /><br />8. In Basis systems, the structure of MSQSNAP is declared differently to how it is declared in ST-PI 2005_1 patch 0003. The error occurs in ST-PI 2005_1 patch 0003 in SAP Basis Release 6.20 and higher.<br /><br />9. In Basis systems, the structure of TABLE_SIZEINFO is declared differently to how it is declared in ST-PI 2005_1 patch 0003. The error occurs in ST-PI 2005_1 patch 0003 in SAP Basis Release 6.20 and higher.<br /><br />10. Form statements<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SELECT &lt;select_list&gt; FROM &lt;tablename&gt; or<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SELECT k0 = @P000,k1 = @P001 UNION ALL<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SELECT @P002, @P003 UNION ALL<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SELECT @P004, @P005 UNION ALL<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SELECT @P006, @P007 UNION ALL SELECT ...<br />are not parsed correctly in module /SDF/MSS_STATEMENT_DETAILS, which means that the table name &lt;tablename&gt; is not returned correctly. This error occurs in ST-PI 2005_1 Patch 0003 and lower releases.<br /><br />11. The error occurs in SAP Basis Release 6.40 only.<br /><br />12. The error occurs in SQL Server 2005 systems only. The database call is not compatible in SQL Server 2005.<br /><br />13. An assignment returns an arithmetic overflow. The /SDF/MSSPFCACH64 and /SDF/MSSPFCACH structures are declared differently.<br /><br />14. An assignment returns an arithmetic overflow. The structures /SDF/MSSTATSSUMMARY-RF_TOT_MS and MAXRFTOTMS are different.<br /><br />15. The function module calls SXPG_CALL_SYSTEM to read the contents of the SQL Server installation directory. This function module returns an error.<br /><br />16. The cause of this error is described in Note 1227499.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>1.1. Program correction:<br />The program corrections for SAP Basis releases higher than Release 6.20 are contained in ST-PI 2005_1 patch 0003.<br />For SAP Basis Release 6.20, see Note 594508.<br />1.2. Program correction:<br />The program correction for SAP Basis Release 6.20 is contained in ST-PI 2005_1 patch 0003.<br />2. Program correction:<br />The change is contained in the current ST-PI 2005_1_&lt;release&gt; (release Q1 2008) but not in ST-PI 2008_1_&lt;release&gt; patch level 0000. For ST-PI 2008_1_&lt;release&gt;, you must implement Note 1266975.<br />3. Program correction:<br />The program corrections are contained in ST-PI 2005_1 patch 0003.<br />4. The error will be corrected in a future Support Package. This error is not relevant to the service delivery.<br />5. Program correction:<br />The program corrections are contained in ST-PI 2005_1 patch 0003.<br />6. Program correction:<br />The program corrections are contained in ST-PI 2005_1 patch 0003.<br />7. Program correction:<br />The program corrections are contained in ST-PI 2005_1 patch 0003.<br />8. In the /SDF/MSSPMEM640 structure, change the type of the CACHEHIT field from DEC 23 to DEC 13. You must implement this change manually and it is not contained in the correction instructions.<br />The change is contained in ST-PI 2005_1 patch 0004.<br />9. Program correction:<br />The program correction is available in the note as correction instructions. The change is contained in ST-PI 2005_1 patch 0004.<br />10. Program correction:<br />The change is contained in ST-PI 2005_1 patch 0004.<br />11. See Note 939870.<br />12. Program correction:<br />The change is contained in the next ST-PI Support Package and will soon be attached to the correction instructions in the note.<br />13. Program correction:<br />The change is contained in the next ST-PI Support Package. See Note 1008622.<br />14. Program correction:<br />The correction is contained in the next ST-PI Support Package, or as correction instructions in Note 1062622.<br />15. See Notes 1229938 and 1227499<br />16. See Note 1227499.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON> (I815574)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D039122)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000866576/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000866576/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000866576/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000866576/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000866576/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000866576/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000866576/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000866576/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000866576/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "939870", "RefComponent": "BC-DB-MSS", "RefTitle": "Minor changes in MSSQL DB monitor authorization check", "RefUrl": "/notes/939870"}, {"RefNumber": "934045", "RefComponent": "SV-SMG-SDD", "RefTitle": "Composite note: SDCCN modules for MS SQL Server 2005", "RefUrl": "/notes/934045"}, {"RefNumber": "781680", "RefComponent": "SV-SMG-SDD", "RefTitle": "SDCC/ SDCCN - Problems with function modules", "RefUrl": "/notes/781680"}, {"RefNumber": "594508", "RefComponent": "BC-DB-MSS", "RefTitle": "Setting up the proxy account for xp_cmdshell", "RefUrl": "/notes/594508"}, {"RefNumber": "1266975", "RefComponent": "SV-SMG-SER", "RefTitle": "/SDF/MSS_PERFHIST returns CALL_FUNCTION_CONFLICT_LENG", "RefUrl": "/notes/1266975"}, {"RefNumber": "1227499", "RefComponent": "SV-SMG-SER", "RefTitle": "/SDF/MSS_DBCC_CHECKDB returns no results", "RefUrl": "/notes/1227499"}, {"RefNumber": "1062622", "RefComponent": "SV-SMG-SDD", "RefTitle": "FM SQL_MSSQL_ANALYSIS_FAST returns error COMPUTE_BCD_OVERFLO", "RefUrl": "/notes/1062622"}, {"RefNumber": "1008622", "RefComponent": "SV-SMG-SER", "RefTitle": "FM /SDF/MSS_PERFHIST returns BCD_OVERFLOW in MSSQL", "RefUrl": "/notes/1008622"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "781680", "RefComponent": "SV-SMG-SDD", "RefTitle": "SDCC/ SDCCN - Problems with function modules", "RefUrl": "/notes/781680 "}, {"RefNumber": "594508", "RefComponent": "BC-DB-MSS", "RefTitle": "Setting up the proxy account for xp_cmdshell", "RefUrl": "/notes/594508 "}, {"RefNumber": "1227499", "RefComponent": "SV-SMG-SER", "RefTitle": "/SDF/MSS_DBCC_CHECKDB returns no results", "RefUrl": "/notes/1227499 "}, {"RefNumber": "1266975", "RefComponent": "SV-SMG-SER", "RefTitle": "/SDF/MSS_PERFHIST returns CALL_FUNCTION_CONFLICT_LENG", "RefUrl": "/notes/1266975 "}, {"RefNumber": "1062622", "RefComponent": "SV-SMG-SDD", "RefTitle": "FM SQL_MSSQL_ANALYSIS_FAST returns error COMPUTE_BCD_OVERFLO", "RefUrl": "/notes/1062622 "}, {"RefNumber": "1008622", "RefComponent": "SV-SMG-SER", "RefTitle": "FM /SDF/MSS_PERFHIST returns BCD_OVERFLOW in MSSQL", "RefUrl": "/notes/1008622 "}, {"RefNumber": "934045", "RefComponent": "SV-SMG-SDD", "RefTitle": "Composite note: SDCCN modules for MS SQL Server 2005", "RefUrl": "/notes/934045 "}, {"RefNumber": "939870", "RefComponent": "BC-DB-MSS", "RefTitle": "Minor changes in MSSQL DB monitor authorization check", "RefUrl": "/notes/939870 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST-PI", "From": "2005_1_40B", "To": "2005_1_40B", "Subsequent": ""}, {"SoftwareComponent": "ST-PI", "From": "2005_1_45B", "To": "2005_1_45B", "Subsequent": ""}, {"SoftwareComponent": "ST-PI", "From": "2005_1_46B", "To": "2005_1_46B", "Subsequent": ""}, {"SoftwareComponent": "ST-PI", "From": "2005_1_46C", "To": "2005_1_46C", "Subsequent": ""}, {"SoftwareComponent": "ST-PI", "From": "2005_1_46D", "To": "2005_1_46D", "Subsequent": ""}, {"SoftwareComponent": "ST-PI", "From": "2005_1_610", "To": "2005_1_610", "Subsequent": ""}, {"SoftwareComponent": "ST-PI", "From": "2005_1_620", "To": "2005_1_620", "Subsequent": ""}, {"SoftwareComponent": "ST-PI", "From": "2005_1_640", "To": "2005_1_640", "Subsequent": ""}, {"SoftwareComponent": "ST-PI", "From": "2005_1_700", "To": "2005_1_700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "ST-PI 2005_1_40B", "SupportPackage": "SAPKITLQA4", "URL": "/supportpackage/SAPKITLQA4"}, {"SoftwareComponentVersion": "ST-PI 2005_1_46C", "SupportPackage": "SAPKITLQD8", "URL": "/supportpackage/SAPKITLQD8"}, {"SoftwareComponentVersion": "ST-PI 2005_1_46C", "SupportPackage": "SAPKITLQD6", "URL": "/supportpackage/SAPKITLQD6"}, {"SoftwareComponentVersion": "ST-PI 2005_1_620", "SupportPackage": "SAPKITLQG3", "URL": "/supportpackage/SAPKITLQG3"}, {"SoftwareComponentVersion": "ST-PI 2005_1_640", "SupportPackage": "SAPKITLQH3", "URL": "/supportpackage/SAPKITLQH3"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "ST-PI", "NumberOfCorrin": 1, "URL": "/corrins/0000866576/212"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}