{"Request": {"Number": "1126457", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 643, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000006701992017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001126457?language=E&token=2BE2AD5036066B2D2E521C189CD212E7"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001126457", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001126457/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1126457"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.10.2010"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-TR-IS-U"}, "SAPComponentKeyText": {"_label": "Component", "value": "use FI-LOC-UT-TR"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Turkey", "value": "XX-CSC-TR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-TR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-Spec. Component", "value": "XX-CSC-TR-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-TR-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "use FI-LOC-UT-TR", "value": "XX-CSC-TR-IS-U", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-TR-IS-U*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1126457 - IS-U TR Address management corrections"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Correction of maintenance of county, township and municipality/village.<br />*----------------------------------------------------------------------<br />When we assign a street code to another district and run quarterly<br />adjustment programs (namely RSADRQU1, RSADRQU2 and RSADRQU3) our<br />expectation was that address hierarchy on the connection object will be<br />updated to the top. But quarterly adjustment programs just change the<br />district but do not change higher nodes. Means, an hierarchical change<br />in address structure is not taken into account by quarterly adjustment<br />programs as a whole.<br /><br />When the address master data for the Turkish address localization<br />fields are updated either as a mapping change (for example a village is<br />assigned to another township)&#x00A0;&#x00A0;or as a name change (for example name of<br />a county is changed), the updates are not reflected to IS-U Objects<br />like BP and Connection Object when quarterly adjustment programs<br />(namely RSADRQU1, RSADRQU2 and RSADRQU3) are run.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>IS-U Turkey address management;<br />Hierarchical changes above district are not updated;</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>1. Same district ID no can be recorded for same combination of county-<br /> township-M/V, and not checked if it is unique.<br /><br />2. In street maintenance the code and the combination check fails.<br /><br />3. The texts address maintenance have mapped wrong.<br /> The correct mapping is:<br /> Street3: ADRC-STR_SUPPL2<br /> Street4: ADRC-STR_SUPPL3<br /> Street5: ADRC-LOCATION<br /><br />4. Where the address data are maintained, if values in fields township and M/V are deleted then text fields are not cleared.<br /><br />5. After entry of a value for county, township, M/V; description of<br /> these fields are not visible nearby field.<br /><br />6. In transaction SR10 or SR11, districts can be defined without any conuty code assigned. However any district should be assigned to at least county.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>For point 1-4: install the note.<br /><br />For point 5:<br /> At function group SZRT screen 200.<br /> Place the county, township and M/V fileds under each other. And place next to them, on the right side of them the following fields in this order:<br /> ADRSTREETD-/SAPCE/IUTR_CONA<br /> ADRSTREETD-/SAPCE/IUTR_TWSN<br /> ADRSTREETD-/SAPCE/IUTR_MVNA<br /><br />For point 6:<br /> Set manually the county field to mandatory.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I053163)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I053163)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001126457/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001126457/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001126457/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001126457/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001126457/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001126457/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001126457/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001126457/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001126457/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1456827", "RefComponent": "XX-CSC-TR-IS-U", "RefTitle": "IS-U Loc. for TR - Address Update: FM-s + bug corrections", "RefUrl": "/notes/1456827"}, {"RefNumber": "1122857", "RefComponent": "XX-CSC-TR-IS-U", "RefTitle": "Turkish functionality for IS-UT 600: Address modification", "RefUrl": "/notes/1122857"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1122857", "RefComponent": "XX-CSC-TR-IS-U", "RefTitle": "Turkish functionality for IS-UT 600: Address modification", "RefUrl": "/notes/1122857 "}, {"RefNumber": "1456827", "RefComponent": "XX-CSC-TR-IS-U", "RefTitle": "IS-U Loc. for TR - Address Update: FM-s + bug corrections", "RefUrl": "/notes/1456827 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "CEEISUT", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "CEEISUT", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "CEEISUT 600", "SupportPackage": "SAPK-60017INCEEISUT", "URL": "/supportpackage/SAPK-60017INCEEISUT"}, {"SoftwareComponentVersion": "CEEISUT 600", "SupportPackage": "SAPK-60006INCEEISUT", "URL": "/supportpackage/SAPK-60006INCEEISUT"}, {"SoftwareComponentVersion": "CEEISUT 600", "SupportPackage": "SAPK-60018INCEEISUT", "URL": "/supportpackage/SAPK-60018INCEEISUT"}, {"SoftwareComponentVersion": "CEEISUT 600", "SupportPackage": "SAPK-60019INCEEISUT", "URL": "/supportpackage/SAPK-60019INCEEISUT"}, {"SoftwareComponentVersion": "CEEISUT 604", "SupportPackage": "SAPK-60408INCEEISUT", "URL": "/supportpackage/SAPK-60408INCEEISUT"}, {"SoftwareComponentVersion": "CEEISUT 604", "SupportPackage": "SAPK-60407INCEEISUT", "URL": "/supportpackage/SAPK-60407INCEEISUT"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "CEEISUT", "NumberOfCorrin": 5, "URL": "/corrins/0001126457/532"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 5, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}