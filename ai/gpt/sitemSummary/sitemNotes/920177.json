{"Request": {"Number": "920177", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 619, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005322562017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000920177?language=E&token=E8BE920C0CA841BBAFDDB4C60A0AE615"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000920177", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000920177/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "920177"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 38}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.02.2010"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DWB"}, "SAPComponentKeyText": {"_label": "Component", "value": "ABAP Workbench, Java IDE and Infrastructure"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "ABAP Workbench, Java IDE and Infrastructure", "value": "BC-DWB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "920177 - Where-used list incorrect for includes"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The where-used list for includes does not find all usage locations.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>WBCROSSI, SE38, where-used list</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Implement the advance correction or import the specified Support Package.<br />To generate the where-used list index, you must rerun the SAPRSEUB report after you implement the advance correction. Schedule the report in the background. See Note 28022.<br /><br /><STRONG>For Release 6.20, note the following procedure for implementing the advance correction:</STRONG><br />For Support Package level 55 and higher, class attributes and method interfaces are implemented automatically by the Note Assistant. For Support Packages lower than 55, class attributes and method interfaces must be created or changed in the class builder before transferring the sources. To do this, proceed as follows:</p> <OL>1. Implement the advance correction, but do not activate it.</OL> <OL>2. Proceed as follows: Call transaction SE24 (Class Builder) and change the following objects:</OL> <UL><LI>Go to the 'Attributes' tab page.</LI></UL> <UL><UL><LI>Create the following class attributes:</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;TABLE_WBCROSSI_RAHMEN Static Attribute Private Type WBCROSST<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;TABLE_WBCROSSI_INCLUDES Static Attribute Private Type WBCROSST<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;INSERT_INCLUDE Static Attribute Private Type CHAR1<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ALL_PROGRAMS Static Attribute Private Type PROGRAMTAB<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Change the reference type of the attribute TABLE_WBCROSSI to TY_WBCROSSI_TAB.<br /></p> <UL><LI>Navigate to the 'Methods' tab page</LI></UL> <UL><UL><LI>Create the following methods with the relevant interfaces. The method type is 'Static Method' and visibility is 'Private'.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Method CHECK_DELETE_WBCROSSI<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import parameters P_DELETE_WA type WBCROSSI and Changingparameter P_DELETE_TABLE type WBCROSST .<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Method SELECT_WBCROSSI_INCLUDES (no parameters)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Method SELECT_WBCROSSI_MASTER<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import parameter P_PROGRAM_TAB Type PROGRAMTAB</p> <UL><UL><LI>Change the typing of the P_WBCROSSI parameter of the GET_INDEX to TY_WBCROSSI_TAB method.</LI></UL></UL> <OL>3. Save and activate the CL_WB_CROSSREFERENCE class.</OL> <p><br /><STRONG>For Release 6.40, proceed as follows to implement the advance correction:</STRONG></p> <OL>1. First of all, create the WBACTINDEX table in the system. To do this, call transaction SE11 and enter the 'WBACTINDEX' name in the 'Database table' field. Choose the 'Create' function.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Enter the following values:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Package: SEUIX<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Delivery class of the table:&#x00A0;&#x00A0;'S' = 'System table, maint only by SAP, change = modification'.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Data Browser/Table View Maint:&#x00A0;&#x00A0;Data Browser/Table View Maint:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;'Display/Maintenance Allowed with Restrictions'.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Data class: SSRC<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Size category: 0<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Buffering not allowed<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Enhancement Category: Can be enhanced (character-type)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The short description of the table is 'Update Programs for Navigation Index'. Create the PROGNAME field with the corresponding PROGNAME data element. Select the 'Key' and 'Initial Values' checkboxes for this field.</p> <OL>2. Activate the table.</OL> <OL>3. Now implement the advance corrections for Release 6.40.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Important: Do not activate all objects yet. First of all, activate the function module: REPS_OBJECT_ACTIVATE separately. To do this, proceed as follows: If the dialog box for activating all objects is displayed when you implement the note, enter a parallel mode and activate the REPS_OBJECT_ACTIVATE function module. Confirm the activation when you implement the note.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br /><STRONG>For Release 7.00, note the following procedure for implementing the advance correction:</STRONG><br />Copy the advance correction for Release 7.00.<br />Important: Do not activate all objects yet. First of all, activate the function module: REPS_OBJECT_ACTIVATE separately. After you have successfully activated the function module, you can activate all the other objects.<br /><br /><br /><br /><br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DWB-UTL (Workbench Utilities)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D030328)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000920177/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000920177/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000920177/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000920177/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000920177/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000920177/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000920177/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000920177/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000920177/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "964749", "RefComponent": "BC-DWB-UTL-INR", "RefTitle": "Performance problems occur in connection with table WBCROSSI", "RefUrl": "/notes/964749"}, {"RefNumber": "28022", "RefComponent": "BC-DWB-UTL", "RefTitle": "Customer system: Where-used list for SAP Objects", "RefUrl": "/notes/28022"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "28022", "RefComponent": "BC-DWB-UTL", "RefTitle": "Customer system: Where-used list for SAP Objects", "RefUrl": "/notes/28022 "}, {"RefNumber": "964749", "RefComponent": "BC-DWB-UTL-INR", "RefTitle": "Performance problems occur in connection with table WBCROSSI", "RefUrl": "/notes/964749 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "620", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62058", "URL": "/supportpackage/SAPKB62058"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62059", "URL": "/supportpackage/SAPKB62059"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64017", "URL": "/supportpackage/SAPKB64017"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70007", "URL": "/supportpackage/SAPKB70007"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70008", "URL": "/supportpackage/SAPKB70008"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 11, "URL": "/corrins/0000920177/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 11, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 9, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46B", "ValidTo": "620", "Number": "811025 ", "URL": "/notes/811025 ", "Title": "EU_INIT terminates with error SYSTEM_CORE_DUMPED", "Component": "BC-DWB-TOO"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "495014 ", "URL": "/notes/495014 ", "Title": "Long runtime with the EU_REORG job", "Component": "BC-DWB"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "735560 ", "URL": "/notes/735560 ", "Title": "Errors in where-used list for type groups and includes", "Component": "BC-DWB-UTL-INR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "883071 ", "URL": "/notes/883071 ", "Title": "Corrections to the RS_EU_CROSSREF_LIST function module", "Component": "BC-DWB-UTL"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "899175 ", "URL": "/notes/899175 ", "Title": "Incorrect restart mechanism of job EU_INIT SAPRSEUB", "Component": "BC-DWB-UTL"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "920177 ", "URL": "/notes/920177 ", "Title": "Where-used list incorrect for includes", "Component": "BC-DWB"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "1017949 ", "URL": "/notes/1017949 ", "Title": "Gaps in field determination and where-used list", "Component": "BC-DWB-TOO"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "704035 ", "URL": "/notes/704035 ", "Title": "Where-used list does not find usage location", "Component": "BC-DWB-UTL"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "735560 ", "URL": "/notes/735560 ", "Title": "Errors in where-used list for type groups and includes", "Component": "BC-DWB-UTL-INR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "742791 ", "URL": "/notes/742791 ", "Title": "Using messages in message classes", "Component": "BC-DWB-TOO"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "883071 ", "URL": "/notes/883071 ", "Title": "Corrections to the RS_EU_CROSSREF_LIST function module", "Component": "BC-DWB-UTL"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "920177 ", "URL": "/notes/920177 ", "Title": "Where-used list incorrect for includes", "Component": "BC-DWB"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "700", "Number": "899175 ", "URL": "/notes/899175 ", "Title": "Incorrect restart mechanism of job EU_INIT SAPRSEUB", "Component": "BC-DWB-UTL"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "883071 ", "URL": "/notes/883071 ", "Title": "Corrections to the RS_EU_CROSSREF_LIST function module", "Component": "BC-DWB-UTL"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "920177 ", "URL": "/notes/920177 ", "Title": "Where-used list incorrect for includes", "Component": "BC-DWB"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}