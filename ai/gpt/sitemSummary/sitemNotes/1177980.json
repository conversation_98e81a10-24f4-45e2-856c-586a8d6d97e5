{"Request": {"Number": "1177980", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 421, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016535172017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001177980?language=E&token=C01ACBEF7F5054D87213D203119ED09F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001177980", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001177980/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1177980"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.07.2016"}, "SAPComponentKey": {"_label": "Component", "value": "PA-PA-PT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Portugal"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Personnel Management", "value": "PA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Personnel Administration", "value": "PA-PA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PA-PA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Portugal", "value": "PA-PA-PT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PA-PA-PT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1177980 - Citizen's Card - new functionality of infotype 0185"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>A new law supporting the Citizen's Card has been published. According to the law n&#186; 7/2007 from February 5th, the Citizens Card will be the new identity card for Portuguese citizens. It will be multifunctional, practical and secure and will combine and replace the existing Taxpayer Card, the National Health Service User's Card, the Social Security card and the Voter's Card.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Citizen's Card, Cart&#227;o de Cidad&#227;o, Law n&#186; 7/2007, Lei n&#186; 7/2007, new portuguese identity card, infotype 0185</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This new functionality is required to keep the Standard SAP system in accordance to the new law n&#186; 7/2007 from February 5th.<br /><br />The following strategic modernisation objectives are associated with the new Citizen's Card:</p>\r\n<ul>\r\n<li>Make identifying people more secure;</li>\r\n</ul>\r\n<ul>\r\n<li>Bring the civil system for identifying Portuguese citizens into line with European Union requirements;</li>\r\n</ul>\r\n<ul>\r\n<li>Make people's lives easier by physically combining various cards;</li>\r\n</ul>\r\n<ul>\r\n<li>Foster the use of electronic services, using digital authentication and signature mechanisms;</li>\r\n</ul>\r\n<ul>\r\n<li>Improve the way in which public services are provided, by aligning organisational and technological modernisation processes;</li>\r\n</ul>\r\n<ul>\r\n<li>Rationalise resources, means and costs for the State, citizens and businesses;</li>\r\n</ul>\r\n<ul>\r\n<li>Foster Portugal's competitivity, by reengineering and simplifying processes and procedures.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>VERSIONING</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Version</strong></td>\r\n<td><strong>Date</strong></td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td>2</td>\r\n<td>July 20, 2016</td>\r\n<td>SAR files were removed.</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>A new subtype in infotype 0185 was created to store the Citizen's Card basic information.<br /><br />The correction described in this note will be included in an HR Support<br />Package, as indicated in item \"Reference to Support Packages\".<br /><br />An Advanced Delivery including Customizing changes is available in the<br />attached files according to the following list(\"xxxxxx\" means numbers):<br /><br />L7DKxxxxxx_600_CUST.CAR - Release 6.00<br />L6DKxxxxxx_500_CUST.CAR - Release ERP 2004<br />L6BKxxxxxx_470_CUST.CAR - Release 4.70(Enterprise)<br />L9CKxxxxxx_46C_CUST.CAR - Release 4.6C</p>\r\n<p><strong>IMPORTANT:</strong></p>\r\n<p>Be aware of an Advance Delivery delivers the last version of the object, it means that if you do not have the last HR Support Package installed in you system you could get errors, either Syntax Errors or process errors. In this case the only option is to undo the changes from Advance Delivery and do the changes manually according to the&#160;&#160;instructions in this note.<br /><br />The attached Advanced Delivery delivers the following customization:<br /><br />- View V_T5R05:<br /><br />&#160;&#160; IC|Description&#160;&#160;&#160;&#160;&#160;&#160;|ID Color|Add|WP|Module Name|NomProgr<br />&#160;&#160; --+-----------------+--------+---+--+-----------+--------<br />&#160;&#160; 06|Cart&#227;o de Cidad&#227;o|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;|&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />&#160;&#160; --+-----------------+--------+---+--+-----------+--------<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I813651)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I810944)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001177980/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001177980/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001177980/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001177980/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001177980/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001177980/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001177980/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001177980/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001177980/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_HRCPT", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HRCPT", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HRCPT", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_HRCPT", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46CD9", "URL": "/supportpackage/SAPKE46CD9"}, {"SoftwareComponentVersion": "SAP_HRCPT 470", "SupportPackage": "SAPK-47084INSAPHRCPT", "URL": "/supportpackage/SAPK-47084INSAPHRCPT"}, {"SoftwareComponentVersion": "SAP_HRCPT 500", "SupportPackage": "SAPK-50050INSAPHRCPT", "URL": "/supportpackage/SAPK-50050INSAPHRCPT"}, {"SoftwareComponentVersion": "SAP_HRCPT 600", "SupportPackage": "SAPK-60033INSAPHRCPT", "URL": "/supportpackage/SAPK-60033INSAPHRCPT"}, {"SoftwareComponentVersion": "SAP_HRCPT 604", "SupportPackage": "SAPK-60401INSAPHRCPT", "URL": "/supportpackage/SAPK-60401INSAPHRCPT"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}