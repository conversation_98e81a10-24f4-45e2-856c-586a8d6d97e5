{"Request": {"Number": "68048", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 201, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014492802017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000068048?language=E&token=1196EECE0ED9A9BE23AEE54A9014E28D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000068048", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000068048/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "68048"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.06.2007"}, "SAPComponentKey": {"_label": "Component", "value": "BC-SEC-LGN"}, "SAPComponentKeyText": {"_label": "Component", "value": "Authentication"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Security - Read KBA 2985997 for subcomponents", "value": "BC-SEC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Authentication", "value": "BC-SEC-LGN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC-LGN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "68048 - Deactivating the automatic SAP* user"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You can use profile parameter login/no_automatic_user_sap* to deactivate the automatic SAP* user. However, if you enter the parameter in the default profile, it has no effect.<br />When you use transaction RZ10, the system displays a message asking you whether you want to use the parameter as it is unknown.<br /><br /><br />If you try to create the profile parameter with transaction RZ11, the system displays a message telling you that wildcards are not permitted.<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Logon<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Profile parameter login/no_automatic_user_sap* does not work in the default profile because the SAP kernel does not allow this.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><U>Release 3.0 to Release 3.1G:</U><br />Use transaction RZ10 to enter the login/no_automatic_user_sap* parameter in the instance profile with the value 1. You can ignore the message when transferring it. Save and activate the instance profile then stop and restart the instance.<br />If you want the value to take effect in the entire system, it must be entered in all instance profiles.<br /><br /><U>As of Release 3.1H:</U><br />Use new default profile parameter login/no_automatic_user_sapstar to deactivate the SAP* user. The default value is 0. Use the value 1 to deactivate it. The parameter is known in transactions RZ10 and RZ11. If you enter login/no_automatic_user_sapstar in the default profile, it takes effect in all instances of the SAP system.<br />For the sake of compatibility, the old login/no_automatic_user_sap* profile parameter is supported up to Release 3.1G.<br />However, you cannot use both profile parameters in parallel. For deactivation, use only parameter login/no_automatic_user_sapstar. Remove the login/no_automatic_user_sap* parameter from your profiles.<br /><br /><U>As of Release 4.0:</U><br />Only the login/no_automatic_user_sapstar parameter is supported for deactivating the SAP* user. If you enter the parameter in the default profile, it takes effect in all instances of the SAP System.<br />The old login/no_automatic_user_sap* profile parameter has no effect.<br /><br /><U>As of Release 7.0 (NetWeaver 7.0/2004s):</U><br />The default value of the login/no_automatic_user_sapstar profile parameter has been changed (previously 0, now 1). To activate the emergency user, you must set the login/no_automatic_user_sapstar profile parameter to the value 0.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D021767)"}, {"Key": "Processor                                                                                           ", "Value": "C5028099"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000068048/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000068048/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000068048/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000068048/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000068048/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000068048/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000068048/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000068048/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000068048/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "963360", "RefComponent": "BC-SEC-USR-IS", "RefTitle": "RSUSR200 - Handling users with inactive password", "RefUrl": "/notes/963360"}, {"RefNumber": "8852", "RefComponent": "BC-SEC", "RefTitle": "Logon with SAP* password 06071992 not possible", "RefUrl": "/notes/8852"}, {"RefNumber": "862989", "RefComponent": "BC-SEC-LGN", "RefTitle": "New password rules as of SAP NetWeaver 2004s (NW ABAP 7.0)", "RefUrl": "/notes/862989"}, {"RefNumber": "806819", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/806819"}, {"RefNumber": "7312", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/7312"}, {"RefNumber": "550894", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-ADMIN: Setting up a new client", "RefUrl": "/notes/550894"}, {"RefNumber": "24853", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: Client Copy, Functionality", "RefUrl": "/notes/24853"}, {"RefNumber": "2383", "RefComponent": "BC-SEC", "RefTitle": "Documentation: description of \"super user\" SAP*", "RefUrl": "/notes/2383"}, {"RefNumber": "1459897", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1459897"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1610103", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "EarlyWatch Alert: Troubleshooting details for chapter \"Default Password of Standard Users\"", "RefUrl": "/notes/1610103 "}, {"RefNumber": "2341383", "RefComponent": "BC-SEC-LGN", "RefTitle": "Reset password for DDIC and SAP* accounts", "RefUrl": "/notes/2341383 "}, {"RefNumber": "3303172", "RefComponent": "BC-SEC-LGN", "RefTitle": "Activating a Super-User SAP*", "RefUrl": "/notes/3303172 "}, {"RefNumber": "863362", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "Security checks in SAP EarlyWatch Alert, EarlyWatch and GoingLive sessions", "RefUrl": "/notes/863362 "}, {"RefNumber": "1891583", "RefComponent": "BC-SEC-USR-ADM", "RefTitle": "Restricting logon to the application server", "RefUrl": "/notes/1891583 "}, {"RefNumber": "550894", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-ADMIN: Setting up a new client", "RefUrl": "/notes/550894 "}, {"RefNumber": "22514", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: Error analysis for client copy", "RefUrl": "/notes/22514 "}, {"RefNumber": "24853", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: Client Copy, Functionality", "RefUrl": "/notes/24853 "}, {"RefNumber": "963360", "RefComponent": "BC-SEC-USR-IS", "RefTitle": "RSUSR200 - Handling users with inactive password", "RefUrl": "/notes/963360 "}, {"RefNumber": "862989", "RefComponent": "BC-SEC-LGN", "RefTitle": "New password rules as of SAP NetWeaver 2004s (NW ABAP 7.0)", "RefUrl": "/notes/862989 "}, {"RefNumber": "4326", "RefComponent": "BC-SEC", "RefTitle": "No user with super user authorizations", "RefUrl": "/notes/4326 "}, {"RefNumber": "2383", "RefComponent": "BC-SEC", "RefTitle": "Documentation: description of \"super user\" SAP*", "RefUrl": "/notes/2383 "}, {"RefNumber": "8852", "RefComponent": "BC-SEC", "RefTitle": "Logon with SAP* password 06071992 not possible", "RefUrl": "/notes/8852 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}