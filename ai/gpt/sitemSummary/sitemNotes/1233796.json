{"Request": {"Number": "1233796", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 440, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016564082017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=2A32CEE7B48BC1ACFFBE615E296896C0"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1233796"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.11.2008"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-TR-IS-U"}, "SAPComponentKeyText": {"_label": "Component", "value": "use FI-LOC-UT-TR"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Turkey", "value": "XX-CSC-TR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-TR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-Spec. Component", "value": "XX-CSC-TR-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-TR-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "use FI-LOC-UT-TR", "value": "XX-CSC-TR-IS-U", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-TR-IS-U*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1233796 - XI Configuration for IS-U Bank Interfaces in Turkey"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The Enhancement Package 4 of ERP 6.00 provides with a bank interface to facilitate the exchange of data between the Utility Company and the Bank (details follow) via Exchange Infrastructure (XI).<br /><br />A cookbook is needed for the configurations in XI.<br /><br /><B><B>Business Background: </B></B><br />Turkish Utility companies provide the customer with an option of paying the bills at Turkish Banks.<br />The Turkish Banks acting as the external cash desks for the company need to have the list of all the open items.<br />This list of the open items is sent from the Utility Company to the Bank via an electronic bank interface.<br />The details of the payments made are sent back from the Bank to the Utility Company via the same electronic bank interface.<br /><br /><B><B>Outgoing File:</B></B><br />Flat file sent from the utility company to the selected bank, containing the list of all selected open items based on various selection criteria (transaction FPEXC_TR, events 6201 and 6204).<br />The list of open items is sent to the Bank on a daily basis or even more frequently. The items that are not yet cleared will be sent repeatedly until the payment is made.<br />Only one file would be created for the single bank mentioned in the selection screen.<br /><br />The outgoing file is generated in XI using mapping between enterprise service interface CashPointOpenItemSummaryNotification_out and interface BankInterfaceTurkey_in. The file in the XML format is converted to flat file format and stored on the XI server.<br /><br /><B>Incoming File: </B><br />The details of the payments received are sent back to the Utility Company at the end of the day via the incoming file.<br />The incoming file lists all the items for which the payment was received on the day along with the details of the payments. There is no 1 to 1 correspondence between the items in the incoming and outgoing file.<br />The Utility Company then clears the open items based on the data from the incoming file.<br /><br />The incoming file in the flat file format is stored in the XI server. The XI picks up the incoming file and converts to XML format. Enterprise service CashPointPaymentNotification takes care of the clearing of the open items.<br />Please check in the transaction FPEXC_MONI for the status of the open items cleared.<br /><br /><B>Others: </B><br />This bank interface is released with the EhP4 business function ISU_LOC_CI_1 only for Utility Customers and has to be activated with the switch FICA_EHP4_TR.<br />Further industries working with Contract Accounting (FI-CA) and needing the same business process have to request the enhancement of the development for the corresponding industry.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>IS-U FI-CA RFKKTROUTEXTDSK00, Bank Interface, Transfer of open items, FPEXC_TR, FPEXC_MONI, BankInterfaceTurkey, XI, Configuration, CashPointPaymentCreateNotification, CashPointOpenItemSummaryNotification, FPEXC, EVENT 6204, EVENT 6201</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><B>Prerequisites:</B><br />- Software Component version (SWCV) FI-CA 604 exists in the system,<br />- The SWCV CNT-LOC 1.00 is installed in the system, which is based on SWCV FI-CA 604.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Please refer to the attached document XI_configuration_ISU_Turkey.pdf for setting up of XI configurations for the IS-U Bank Interface in Turkey.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-CSC-TR-FICA (use FI-LOC-CA-TR)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I046704)"}, {"Key": "Processor                                                                                           ", "Value": "I041329"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "XI_Configurations_ISU_Turkey.zip", "FileSize": "702", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000423492008&iv_version=0004&iv_guid=6B138E543BA16E47AF261DB6B02D873E"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1122857", "RefComponent": "XX-CSC-TR-IS-U", "RefTitle": "Turkish functionality for IS-UT 600: Address modification", "RefUrl": "/notes/1122857"}, {"RefNumber": "1122854", "RefComponent": "XX-CSC-TR-IS-U", "RefTitle": "IS-U localization for Turkey: Address modification", "RefUrl": "/notes/1122854"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1122857", "RefComponent": "XX-CSC-TR-IS-U", "RefTitle": "Turkish functionality for IS-UT 600: Address modification", "RefUrl": "/notes/1122857 "}, {"RefNumber": "1122854", "RefComponent": "XX-CSC-TR-IS-U", "RefTitle": "IS-U localization for Turkey: Address modification", "RefUrl": "/notes/1122854 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "FI-CA", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "IS-UT", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "IS-UT 604", "SupportPackage": "SAPK-60401INISUT", "URL": "/supportpackage/SAPK-60401INISUT"}, {"SoftwareComponentVersion": "FI-CA 604", "SupportPackage": "SAPK-60401INFICA", "URL": "/supportpackage/SAPK-60401INFICA"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}