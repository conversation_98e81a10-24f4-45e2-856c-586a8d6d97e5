{"Request": {"Number": "2215424", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1053, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018162642017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002215424?language=E&token=99E15E3E82374287E61BDED55F64691B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002215424", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002215424/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2215424"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 28}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.06.2017"}, "SAPComponentKey": {"_label": "Component", "value": "CA-FLE-MAT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Field Lenght Extension for Material"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Cross Application Field Lenght Extension", "value": "CA-FLE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-FLE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Field Lenght Extension for Material", "value": "CA-FLE-MAT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-FLE-MAT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2215424 - Material Number Field Length Extension - General Information"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The material&#160;number and related data types&#160;are extended.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Material Number Field Length Extension, MATNR, ATWRT, Classification, Material Number, BAPI, IDoc, RFC, 18 digits, 40 digits</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>In SAP S/4HANA, the material number&#160;field length&#160;has been&#160;extended from 18 to 40 characters. This change was first implemented in SAP S/4HANA, on-premise edition 1511 and affects this release and higher releases.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Preface</strong></p>\r\n<p>SAP S/4HANA supports material numbers with up to 40 characters. Therefore, the maximum length of the&#160;fields that handle the material number has been extended from 18 to 40 characters.</p>\r\n<p>This note gives an overview about how this extension&#160;was implemented&#160;and what the consequences of the extension are for customer code. For&#160;details on different aspects, see the referenced notes.</p>\r\n<p>Extending the material number means that the code in the system must guarantee that all relevant bits of code can handle 40 characters.&#160;It's especially important to&#160;ensure that the material number&#160;is not&#160;truncated to its former length of 18 characters,&#160;because this would result in data loss. The&#160;corresponding SAP development entities (domains, data elements, structures, table types, and transparent tables, external and internal interfaces, user interfaces, and so on) have been adapted accordingly.</p>\r\n<p>Furthermore, all database fields that may be used to store a material number also have to be extended.&#160;Where required, automated logic is in place and executed automatically in case a customer converts his current SAP Business Suite system to SAP S/4HANA.</p>\r\n<p>This note and the referenced notes describe which changes were needed to accommodate for the technical length extension of the material number. As customer and partner code uses the same techniques all described adaptions need also to be done in affected customer coding, irrespective of whether the extended material number is actually activated (see below) or whether you stay with&#160;material numbers of 18 characters.</p>\r\n<p><strong>System-Internal Coding</strong></p>\r\n<p>In an SAP system,&#160;consistent behavior of a data field is usually guaranteed by the fact that all data elements used&#160;are derived from the same domain, the domain defining the technical length of the derived fields. Whenever code uses these fields,&#160;it is automatically adapted when the domain changes. For the material number, several domains&#160;that are used to define material number fields have been extended to 40 characters.</p>\r\n<p>Besides these direct usages,&#160;the material number is also valid content in other fields such as, for example, characteristics values.&#160;For those fields for which a material &#8211; besides other possible values &#8211; is valid content, it has been checked that these fields are long enough to hold the extended material number. If necessary, these fields have also been extended.</p>\r\n<p>These&#160;kinds of &#8220;target&#8221; fields,&#160;which are not material numbers in themselves, were only&#160;extended if the material number needs to be handled in the field.&#160;In several places, the material number is used as reference, template, or for other reasons, but another (shorter) value &#8211; for example a GUID &#8211; would&#160;also be&#160;useable. In these and other similar cases, it may be a&#160;better&#160;option to switch to another value. Accordingly, this other value&#160;was&#160;used everywhere where it was appropriate.</p>\r\n<p>Overall, the entire data flow in the system&#160;was analysed to identify all the places&#160;in which a material number was moved to a field that was not long enough for a 40 character material number. These conflicts have been handled in one of the ways described above. This type of analysis is also required for customer or partner coding (using appropriate tools).</p>\r\n<p>The described changes have also been applied to parameters of all interfaces that are usually called only within one system, such as local function modules, class methods, BAdIs, and so on. In the types and structures that are used as parameters in these local calls, the material number has simply been extended to 40 characters. The same is also true for other extended fields. This was usually also done for unreleased remote-enabled function modules,&#160;because the main use case for such function modules is an internal decoupling within one system (e.g. parallel processing).</p>\r\n<p>For interfaces that are usually called remotely, a different way has been chosen. For more information, see the specific chapter below.</p>\r\n<p><strong>Database Representation of the Material Number</strong></p>\r\n<p>Extending the material number&#160;in the database means that the field length of the MATNR field in the database has been extended from 18 to 40 characters. This has been done in all tables (and all fields within the tables) in which a material number can be stored.</p>\r\n<p>The way&#160;in which&#160;the material number content is stored in these database field has not been changed compared to SAP Business Suite.</p>\r\n<p>This holds especially true for purely numeric material numbers. With standard Customizing (lexicographic flag not set, i.e. leading zeroes are not significant), purely numeric material numbers are still restricted to 18 characters and will be filled up&#160;in the database&#160; up to only 18 characters by leading zeroes. This is the same behavior as before.</p>\r\n<p>Overall, the chosen way of storing material number content avoids data conversions in the database. Note that such data conversion&#160;is necessary&#160;when a material number is stored in a field that holds concatenated content, and the material number is part of that concatenated content: Concatenation in the code uses the technical length of the field &#8211; which is now 40 characters &#8211; and that is also reflected in the database content. If you store such concatenated values in own tables, data conversion for these tables may also be needed.</p>\r\n<p>If you have activated the DIMP (Discrete Industries &amp; Mill Products) long material number or manufacturer parts number, the content that&#160;is stored in the database for the material number will be need to be changed. For further information see note <a target=\"_blank\" href=\"/notes/2381633\">2381633</a>.</p>\r\n<p><strong>Database Representation for Other Extended Fields</strong></p>\r\n<p>If possible, the database representation has also&#160;been kept for the other extended fields,</p>\r\n<p>Some of the other extended fields may contain concatenated data that is constructed from the content of other extended fields. In such cases, a conversion is usually necessary.&#160;See the referenced SAP Note <a target=\"_blank\" href=\"/notes/2215871\">2215871</a> on data migration.</p>\r\n<p><strong>Released External Interfaces</strong></p>\r\n<p>External interfaces used for integration in a multi-system landscape (with SAP and/or non-SAP systems)&#160;must be&#160;compatible with the old version of the interfaces, because it cannot be assumed that all connected system are able to deal with 40 character material numbers immediately.</p>\r\n<p>This&#160;is especially relevant for&#160;the commonly used integration techniques BAPI, RFC, and IDoc,&#160;because these techniques rely on a fixed length and&#160;order of&#160;fields in the transmitted data.&#160;Simply extending the material number field (or other extended fields) in these interfaces would therefore technically break the version compatibility. For the material number and other extended fields as described in this note, version compatibility is achieved in the same way that is commonly used for BAPI interfaces: The already existing field keeps its original length and a new field has been added at the end of the structure that allows transmitting 40 characters material numbers.</p>\r\n<p>To enforce that only values can be maintained in the system that&#160;can be transmitted in the original short fields,&#160;the extended material number functionality must be activated explicitly. Only after this decision is it&#160;possible to allow more than 18 characters for the material number.</p>\r\n<p>The changes described have&#160;been&#160;made&#160;for BAPIs, IDocs, and released remote-enabled function modules.</p>\r\n<p>For released WebServices,&#160;adding a new field or extending the material number field was usually not necessary&#160;because these services already allow material numbers with up to 60 characters in their interfaces.</p>\r\n<p>If you have created own external interfaces you have to check if similar compatible changes are needed. A detailed description of the applied changes can be found in note <a target=\"_blank\" href=\"/notes/2215871\">2215852</a>.</p>\r\n<p><strong>System Behavior That Depends on the Extended Material Number Activation</strong></p>\r\n<p>If the extended material number functionality is <strong>NOT</strong>&#160;<strong>activated</strong>, the system behaves as follows:</p>\r\n<ul>\r\n<li>After an SAP Business Suite System&#160;has been&#160;migrated to SAP S/4HANA, the B2B and A2A communication via BAPIs, IDOCs, Web-Services, and released RFCs (inbound) still works without further changes.</li>\r\n<li>The system prevents data&#160;from being&#160;created that cannot be sent via the old interface, that is, the usage of the extended fields is restricted to the old length.</li>\r\n<li>The shorter versions of the extended fields</li>\r\n<ul>\r\n<li>are still part of the interfaces</li>\r\n<li>are still filled when sending data</li>\r\n<li>are still understood when receiving data</li>\r\n</ul>\r\n<li>This is also true if the extended field is used as part of a communicated concatenated field. This concatenated field is still&#160;sent&#160;in the original field in the old short format, and is interpreted in the inbound case in the original field in the old short format.</li>\r\n<li>Communication partners can still rely on the old known behavior of the interfaces.</li>\r\n<li>The new 40 character field and other new extended fields in the interfaces are filled and interpreted, too. So the 18 character material number is also communicated via the 40 character field. This is also true if the extended field is used as part of a communicated concatenated field. The new extended field will contain and expect the new long format. This means that the communication partners can already adapt their interfaces for using the extended field&#160;even though&#160;only short material numbers are allowed.</li>\r\n</ul>\r\n<p>When the extended material number functionality is <strong>activated</strong>, it is no longer guaranteed that the data can be trasmitted&#160;in the old fields. The system behaves as follows:</p>\r\n<ul>\r\n<li>Material numbers and other extended fields can be used with the full length.</li>\r\n<li>It is no longer&#160;guaranteed that the old short fields in external interfaces can be filled or accepted:</li>\r\n<ul>\r\n<li>If the material number or other extended fields are used with more than the original length, the shorter version of an extended field cannot longer be filled in the interface and is therefore left empty.</li>\r\n<li>This is also true for concatenated keys containing extended fields. If the value that is part of the concatenate is longer than the original field length, the concatenate can only be sent and evaluated in the new format.</li>\r\n</ul>\r\n<ul>\r\n<li>If the current value of the material number or the current value of another extended field still fits into the old short field in the interface, the short field&#160;is filled in outbound and accepted in inbound as well.</li>\r\n<li>This is also true for concatenated values: if the old format can still be used&#160;because the current value of the extended field contained in the concatenate is short enough, the old format is still sent in outbound and accepted in inbound in the old short field.</li>\r\n</ul>\r\n<li>Communication partners have to adjust to the new fields and data formats. <br />Be aware that all SAP Business Suite systems are communication partners!</li>\r\n</ul>\r\n<p>Note: Before SAP S/4HANA, on-premise edition 1511 FPS2, the old short fields have not been filled or accpeted when the extended material number functionality is activated. With&#160;SAP S/4HANA, on-premise edition 1511 FPS2, this has been changed to the behavior mentioned above (see also note <a target=\"_blank\" href=\"/notes/2287625\">2287625</a>).</p>\r\n<p>It is important to understand that the activation of the extended material number only affects the length of the value that can be entered. Independent from the activation of the functionality the technical length of the material number field (and other affected fields) is already extended to the new maximum length. That means that&#160;all coding needs to be able to handle a technical field of length 40 for the material number even if the extended material number functionality is not enabled and you stick to values with 18 characters.</p>\r\n<p><strong>Internal Calls of Released External Interfaces</strong></p>\r\n<p>As described in the previous chapters, different strategies have been chosen for internal and for released external APIs.</p>\r\n<p>If a released external API is called internally, that is, locally within one system,&#160;compatibility is not required. The interfaces already understand and fill the new extended fields. Therefore,&#160;all internal calls of external interfaces must only use the newly added extended fields.</p>\r\n<p>This is also true if structures that are also used in released external interfaces, and which have therefore been changed in the way described, are used internally. Additionally, only the new extended field should be used&#160;for all internal coding in this case.</p>\r\n<p><span style=\"color: #444444; font-family: BentonSans, Helvetica, Arial, sans-serif; font-size: 14px;\">Inquiry is just a simple document showing the interest shown by your prospective customer on products and services.&#160;</span></p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "LO-MD-M<PERSON> (Material Master)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D048317)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D022841)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002215424/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002215424/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002215424/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002215424/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002215424/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002215424/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002215424/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002215424/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002215424/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2382620", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Field Length Extension: converting concatenated values read from an archive", "RefUrl": "/notes/2382620"}, {"RefNumber": "2381633", "RefComponent": "CA-FLE-MAT-CNV", "RefTitle": "Material Field Length Extension: additional information for  DIMP systems with active long material number or MPN", "RefUrl": "/notes/2381633"}, {"RefNumber": "2381584", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension: ALE interface generation and calling of generated ALE outbound modules for changed BOR objects", "RefUrl": "/notes/2381584"}, {"RefNumber": "2310255", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension - Structures used in Workflow Containers", "RefUrl": "/notes/2310255"}, {"RefNumber": "2287625", "RefComponent": "LO-MD-MM", "RefTitle": "Changed behavior of material mapping at interfaces for extended material number functionality", "RefUrl": "/notes/2287625"}, {"RefNumber": "2218350", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension - IDoc Interface/ALE", "RefUrl": "/notes/2218350"}, {"RefNumber": "2216958", "RefComponent": "CA-FLE-MAT", "RefTitle": "Precheck Program for Migration to SAP S/4HANA with regards to Material Number Field Length Extension: Find BOR Usage and Material Number Usage", "RefUrl": "/notes/2216958"}, {"RefNumber": "2216654", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension - BOR Objects", "RefUrl": "/notes/2216654"}, {"RefNumber": "2215871", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension - Data Migration", "RefUrl": "/notes/2215871"}, {"RefNumber": "2215852", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension: Code Adaptions", "RefUrl": "/notes/2215852"}, {"RefNumber": "2214790", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension - UIs", "RefUrl": "/notes/2214790"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2782444", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics value & description extension in HANA system", "RefUrl": "/notes/2782444 "}, {"RefNumber": "2334547", "RefComponent": "LO-MD-MM", "RefTitle": "Increase Material Number to over 18 Characters", "RefUrl": "/notes/2334547 "}, {"RefNumber": "2514937", "RefComponent": "CA-CL-CHR", "RefTitle": "Error C4010 'Entry & longer than maximum of 30 characters'", "RefUrl": "/notes/2514937 "}, {"RefNumber": "2453997", "RefComponent": "LO-MD-MM", "RefTitle": "S4/HANA: BMG142 The numeric material number is longer than 18 characters", "RefUrl": "/notes/2453997 "}, {"RefNumber": "2680524", "RefComponent": "LO-FSH-PP", "RefTitle": "FAQ - Internal PP Master Data for Fashion", "RefUrl": "/notes/2680524 "}, {"RefNumber": "2422224", "RefComponent": "BW-BCT-MM-BW", "RefTitle": "SAP S/4HANA Long Material number integration with SAP BW / SAP BW/4HANA", "RefUrl": "/notes/2422224 "}, {"RefNumber": "2443908", "RefComponent": "CA-FLE-MAT", "RefTitle": "SAP S/4HANA: Material Number Field Length Extension in BAPI / BOR method parameters: Restriction Note", "RefUrl": "/notes/2443908 "}, {"RefNumber": "2438006", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension: Code Adaptions for compatibly enhanced local function modules", "RefUrl": "/notes/2438006 "}, {"RefNumber": "2438110", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension: Code Adaptions for usages of RFC enabled function modules", "RefUrl": "/notes/2438110 "}, {"RefNumber": "2438131", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension: Code Adaptions for usages of released RFCs and BAPIs", "RefUrl": "/notes/2438131 "}, {"RefNumber": "2381633", "RefComponent": "CA-FLE-MAT-CNV", "RefTitle": "Material Field Length Extension: additional information for  DIMP systems with active long material number or MPN", "RefUrl": "/notes/2381633 "}, {"RefNumber": "2381584", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension: ALE interface generation and calling of generated ALE outbound modules for changed BOR objects", "RefUrl": "/notes/2381584 "}, {"RefNumber": "2353005", "RefComponent": "SCM-BAS-INT", "RefTitle": "CIF adjustments for material field length extensions (MFLE)", "RefUrl": "/notes/2353005 "}, {"RefNumber": "2330311", "RefComponent": "SCM-BAS-INT", "RefTitle": "CIF Adjustments for MFLE", "RefUrl": "/notes/2330311 "}, {"RefNumber": "2337941", "RefComponent": "CA-FLE-MAT-CNV", "RefTitle": "Material Field Length Extension - Unfinished Workflows from DIMP LAMA system", "RefUrl": "/notes/2337941 "}, {"RefNumber": "2310255", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension - Structures used in Workflow Containers", "RefUrl": "/notes/2310255 "}, {"RefNumber": "2274307", "RefComponent": "BC-MID-ALE", "RefTitle": "BDBG dumps as the transaction is blacklisted in SAP S/4HANA, on-premise edition 1511", "RefUrl": "/notes/2274307 "}, {"RefNumber": "2267140", "RefComponent": "CA-FLE-MAT", "RefTitle": "S4TWL - Material Number Field Length Extension", "RefUrl": "/notes/2267140 "}, {"RefNumber": "2218350", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension - IDoc Interface/ALE", "RefUrl": "/notes/2218350 "}, {"RefNumber": "2216654", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension - BOR Objects", "RefUrl": "/notes/2216654 "}, {"RefNumber": "2215871", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension - Data Migration", "RefUrl": "/notes/2215871 "}, {"RefNumber": "2214790", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension - UIs", "RefUrl": "/notes/2214790 "}, {"RefNumber": "2215852", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension: Code Adaptions", "RefUrl": "/notes/2215852 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "QRT_STAG", "From": "100", "To": "100", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": "X"}, {"SoftwareComponent": "SAP_ABA", "From": "75A", "To": "75A", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}