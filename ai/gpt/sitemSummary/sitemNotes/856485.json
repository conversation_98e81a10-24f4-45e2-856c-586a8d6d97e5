{"Request": {"Number": "856485", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 443, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016014502017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000856485?language=E&token=D8F48AACDDE2F2D0778E99E4C5FDF646"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000856485", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000856485/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "856485"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "07.07.2005"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-OT-OLAP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Analyzing Data"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Explorer", "value": "BW-BEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "OLAP Technology", "value": "BW-BEX-OT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Analyzing Data", "value": "BW-BEX-OT-OLAP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT-OLAP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "856485 - Calculating with attributes with display hierarchy"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>In a query, you are using a formula variable that is to be replaced from an attribute of a characteristic. In cells that are restricted to a hierarchy node on the characteristic, the formula variable is not replaced.<br />(For more information about this problem, also see Note 379832).<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Node, hierarchy, numeric variable<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Since the hierarchy node does not have any attributes, this behavior is correct. However, if the hierarchy node characteristic also has this attribute, you would expect the variable to be replaced despite that.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>As of the Support Packages specified below, the expectation described above can be implemented. If you enter the 'USE_HIERATTR' string in the OBJECT field in the RSADMIN table and you enter the character 'X' in the VALUE field, the numeric variables are also replaced in all queries if the node characteristic has the attribute. You can create the required entry using the SAP_RSADMIN_MAINTAIN program (Transaction SE38).<br /><B>Caution:</B><br /><B>As of</B><B> Netweaver 2004s,</B><B> this function is not controlled by the parameter in the </B><B>RSADMIN </B><B> table. If you want to use the function after this release, you must change the definition of the formula variable.</B><br /></p> <UL><LI>BW 3.0B</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 28 for 3.0B (BW3.0B Patch28 or <B> SAPKW30B28</B>) into your BW system. The Support Package is available when N<B>ote 0783170</B> \"SAPBWNews BW3.0B Support Package 28\", which describes this Support Package in more detail, is released for customers.</p> <UL><LI>BW 3.10 Content</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 22 for 3.10 (BW3.10 Patch22 or <B> SAPKW31022</B>) into your BW system. The Support Package is available when N<B>ote 0783252</B> \"SAPBWNews BW3.1 Content SP 22\", which describes this Support Package in more detail, is released for customers.</p> <UL><LI>BW 3.50</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 14 for 3.5 (BW3.50 Patch14 or <B> SAPKW35014</B>) into your BW system. The Support Package is available when N<B>ote 0836439</B> \"SAPBWNews BW SP14 NetWeaver'04 Stack 14\", which describes this Support Package in more detail, is released for customers.<br /><br />In urgent cases, you can use the correction instructions.<br />To provide advance information, the notes mentioned above may be available before the Support Package is released. In this case, the short text still contains the words \"Preliminary version\".<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BEX (Business Explorer)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023396)"}, {"Key": "Processor                                                                                           ", "Value": "D025759"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000856485/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000856485/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000856485/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000856485/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000856485/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000856485/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000856485/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000856485/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000856485/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "955990", "RefComponent": "BW", "RefTitle": "BI in SAP NetWeaver 7.0: Incompatibilities with SAP BW 3.x", "RefUrl": "/notes/955990"}, {"RefNumber": "836439", "RefComponent": "BW", "RefTitle": "SAPBWNews BW Support Package 14 NW'04 Stack 14 RIN", "RefUrl": "/notes/836439"}, {"RefNumber": "783252", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.1 Content Support Package 22", "RefUrl": "/notes/783252"}, {"RefNumber": "783170", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.0B Support Package 28", "RefUrl": "/notes/783170"}, {"RefNumber": "379832", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Calculating w/ variables w/ subst. from attrib. w/o char.", "RefUrl": "/notes/379832"}, {"RefNumber": "1385580", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "How does a formula variable with a replacement path work?", "RefUrl": "/notes/1385580"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1385580", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "How does a formula variable with a replacement path work?", "RefUrl": "/notes/1385580 "}, {"RefNumber": "955990", "RefComponent": "BW", "RefTitle": "BI in SAP NetWeaver 7.0: Incompatibilities with SAP BW 3.x", "RefUrl": "/notes/955990 "}, {"RefNumber": "379832", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Calculating w/ variables w/ subst. from attrib. w/o char.", "RefUrl": "/notes/379832 "}, {"RefNumber": "836439", "RefComponent": "BW", "RefTitle": "SAPBWNews BW Support Package 14 NW'04 Stack 14 RIN", "RefUrl": "/notes/836439 "}, {"RefNumber": "783170", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.0B Support Package 28", "RefUrl": "/notes/783170 "}, {"RefNumber": "783252", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.1 Content Support Package 22", "RefUrl": "/notes/783252 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "30B", "To": "30B", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "310", "To": "310", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "350", "To": "350", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "30B", "To": "30B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW_VIRTUAL_COMP 30B", "SupportPackage": "SAPK-30B35INVCBWTECH", "URL": "/supportpackage/SAPK-30B35INVCBWTECH"}, {"SoftwareComponentVersion": "SAP_BW 30B", "SupportPackage": "SAPKW30B28", "URL": "/supportpackage/SAPKW30B28"}, {"SoftwareComponentVersion": "SAP_BW 310", "SupportPackage": "SAPKW31022", "URL": "/supportpackage/SAPKW31022"}, {"SoftwareComponentVersion": "SAP_BW 310", "SupportPackage": "SAPKW31022", "URL": "/supportpackage/SAPKW31022"}, {"SoftwareComponentVersion": "SAP_BW 350", "SupportPackage": "SAPKW35014", "URL": "/supportpackage/SAPKW35014"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}