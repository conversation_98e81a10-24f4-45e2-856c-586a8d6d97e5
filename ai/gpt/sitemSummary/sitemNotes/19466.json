{"Request": {"Number": "19466", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 631, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014340192017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000019466?language=E&token=249C68386D1AA00875FA36EA05721E65"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000019466", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000019466/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "19466"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 155}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.02.2024"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-SAPSMP-SWC"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Support Portal - Software Distribution Center"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Support Portal", "value": "XX-SER-SAPSMP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-SAPSMP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Support Portal - Software Distribution Center", "value": "XX-SER-SAPSMP-SWC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-SAPSMP-SWC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "19466 - Downloading SAP kernel patches"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Downloading and applying kernel patches via SAP Support Portal, Software Distribution Center</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Download, software center, kernel, binary patches, hotfix, correction, SAP ONE Support Launchpad</p>\r\n<p>SAPEXE, SAPEXEDB, disp+work, tp, R3trans, lib_dbsl</p>\r\n<p>SP Stack Kernel, RKS, AKK, DCK</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This SAP Note applies to all SAP kernel versions that are currently in maintenance. Release-specific information is provided below.</p>\r\n<p>Consider the general SAP recommendation on how to apply SAP kernel patches:</p>\r\n<ol>\r\n<li>Follow the <a target=\"_blank\" href=\"https://support.sap.com/en/my-support/software-downloads/support-package-stacks/support-package-stack-strategy.html\">SAP Support Package Stack Strategy</a> for SAP software products. The most recent SP Stack Kernel will be applied to your system automatically.<span style=\"font-size: 14px;\"><br /><br /></span></li>\r\n<li>Perform additional SAP kernel updates only if you need to solve a problem in the system. In this case:</li>\r\n</ol>\r\n<ul>\r\n<li>Apply the <strong>latest SP Stack Kernel</strong>&#160;provided that it contains the required correction.</li>\r\n<li>Apply a <strong>hotfix&#160;only</strong> if you are experiencing a serious error that is fixed by this hotfix but not by the latest SP Stack Kernel.</li>\r\n</ul>\r\n<p>The paper&#160;<a target=\"_blank\" href=\"https://support.sap.com/deployment-strategies-kernel-abap.pdf\" rel=\"noopener noreferrer\" tabindex=\"-1\" title=\"https://support.sap.com/deployment-strategies-kernel-abap.pdf\">Update Strategy for the Kernel of the Application Server ABAP in On Premise Landscapes</a>&#160;provides details on SAP recommendations.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>1. Downloading from SAP Support Portal</strong></p>\r\n<p>Patches for the SAP kernel are available for download in the&#160;<a target=\"_blank\" href=\"https://support.sap.com/en/my-support/software-downloads.html\">SAP Software Download Center</a>&#160;on the SAP Support Portal.</p>\r\n<p>To download SAP kernel patches, proceed as follows:</p>\r\n<p>Open the&#160;<a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/softwarecenter\">software downloads</a>&#160;page.</p>\r\n<p>Switch to the subarea <span style=\"text-decoration: underline;\">\"SUPPORT PACKAGES &amp; PATCHES\"</span>.</p>\r\n<p style=\"padding-left: 30px;\">The following introductory options are available:</p>\r\n<ul>\r\n<li>\"By Alphabetical Index (A-Z)\" -&gt; \"K\"&#160;</li>\r\n<li>\"By Category\" -&gt; \"Additional Components\" -&gt; \"SAP kernel\"</li>\r\n</ul>\r\n<p>To select the required kernel version or variant, you must first take account of the following properties:</p>\r\n<ul>\r\n<li>64-/32-bit</li>\r\n<li>Unicode /Non-Unicode</li>\r\n<li>Standard/EXT/EX2</li>\r\n</ul>\r\n<p>Then select the required kernel version, for example,&#160;SAP kernel 7.53 64-BIT UNICODE</p>\r\n<p>Then select your operating system, for example, AIX64bit.</p>\r\n<ul>\r\n<li>Under \"#DATABASE INDEPENDENT\", you will find all database-independent patches for the SAP kernel.</li>\r\n<li>Select your database for the database-specific kernel patches (for example: SAP HANA DATABASE).</li>\r\n</ul>\r\n<p>Note that you must download the patches <span style=\"text-decoration: underline;\">from both directories</span> to update your kernel completely.</p>\r\n<p>The following information about a patch is displayed in each case:</p>\r\n<ul>\r\n<li>File name (may differ from the patch names described below)</li>\r\n<li>Short text</li>\r\n<li>Patch level</li>\r\n<li>Related info. This contains the list of delivered corrections.</li>\r\n</ul>\r\n<p>For each SAP kernel, the following patches are available for download:</p>\r\n<ul>\r\n<li>All available SP Stack Kernel versions (see point 3.1)</li>\r\n<li>The last published hotfixes (see point 3.2)</li>\r\n</ul>\r\n<p>Choose the patch name to download a patch.</p>\r\n<p>Patches for&#160;<strong>out of maintenance </strong>releases&#160;can be found&#160;in&#160;<span style=\"text-decoration: underline;\"><a target=\"_blank\" class=\"external-link\" href=\"https://launchpad.support.sap.com/#/softwarecenter/template/_APP=00200682500000001943&amp;_EVENT=DISPHIER&amp;HEADER=N&amp;FUNCTIONBAR=Y&amp;EVENT=TREE&amp;TMPL=INTRO_SWDC_AR_K&amp;V=MAINT&amp;TA=ARCHIVE&amp;REFERER=ARCHIVE_A-Z/Archive%20by%20Alphabetical%20Index%20(A-Z)%20-%20K\" rel=\"nofollow\">the archive area of SAP Support Portal</a></span>:&#160;&#160;SUPPORT PACKAGES &amp; PATCHES &#8594;&#160;<strong>Archive</strong>&#160;by Alphabetical Index (A-Z)&#160;&#8594; K &#8594; SAP KERNEL &lt;variant&gt;, example: SAP KERNEL 64-BIT&#160;UNICODE &#8594;&#160;&lt;kernel version&gt;</p>\r\n<p>For <strong>kernel DVD sets</strong> (available only for kernel 722 and 749), open the&#160;<a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/softwarecenter\">software downloads</a>&#160;page &#8594; switch to subarea&#160;INSTALLATIONS AND UPGRADES &#8594; by Alphabetical Index (A-Z) &#8594; K &#8594; SAP KERNEL &lt;variant&gt;, example: SAP KERNEL 64-BIT&#160;UNICODE &#8594;&#160;&lt;kernel version&gt;</p>\r\n<p>For more information about navigating within the Software Downloads section, see&#160;<a target=\"_blank\" href=\"https://support.sap.com/content/dam/library/ssp/support-programs-services/lpdocu/software-downloads-lp.pdf\">Online help: Software downloads</a>.</p>\r\n<p>Alternatively, you can add the required patch for a later download to your <span style=\"text-decoration: underline;\">Download Basket</span>, or use the <a target=\"_blank\" href=\"https://support.sap.com/dlmanager\">SAP Download Manager</a>. Refer to the Software Center help for more information.</p>\r\n<p><strong>2. Patch format</strong></p>\r\n<p style=\"padding-left: 30px;\"><strong>2.1 SAPCAR archive tool: </strong>Kernel patches are provided in the SAR archive in the format \"&lt;name&gt;.SAR\" and can be unpacked with the SAPCAR archive tool.</p>\r\n<p style=\"padding-left: 30px;\">Use the latest version of SAPCAR available on&#160;SAP Support Portal. Download the archive SAPCAR_&lt;PLevel&gt;-&lt;UID&gt;.EXE &#160;for your platform: SAPCAR (UNIX, IBM i) or sapcar.exe (Windows).</p>\r\n<p style=\"padding-left: 60px;\"><a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/softwarecenter\">https://launchpad.support.sap.com/#/softwarecenter</a></p>\r\n<p style=\"padding-left: 60px;\">Change to the subarea&#160;\"SUPPORT PACKAGES &amp; PATCHES\"&#160;-&gt;\"By Category\" -&gt; Additional Components -&gt; SAPCAR -&gt; SAPCAR 7.2&lt;n&gt; -&gt; &lt;your platform&gt;.</p>\r\n<p style=\"padding-left: 30px;\">To unpack SAR archive enter the command \"SAPCAR -xvf &lt;name&gt;.SAR\".</p>\r\n<p style=\"padding-left: 30px;\"><strong>2.2 Naming convention</strong></p>\r\n<p style=\"padding-left: 30px;\">&lt;Patchname&gt;_&lt;Patchlevel&gt;-&lt;SAP-internal-GUID-No&gt;.&lt;archext&gt;</p>\r\n<p style=\"padding-left: 60px;\">Example: DW_559-10001684.SAR</p>\r\n<p>&#160;</p>\r\n<p><strong>3. Kernel patch types</strong></p>\r\n<p style=\"padding-left: 30px;\"><strong>3.1&#160;<strong>SP Stack Kernel</strong> archives SAPEXE.SAR/SAPEXEDB.SAR&#160;</strong></p>\r\n<p style=\"padding-left: 30px;\">The SP Stack Kernel comprises the complete set of executables and shared libraries of the kernel:</p>\r\n<ul>\r\n<li>SAPEXE.SAR contains all files for a specific operating system that are independent of the type of the used central database</li>\r\n<li>SAPEXEDB.SAR contains all files required for a specific type of the central database</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">To update the SP Stack Kernel, you need to download and install <strong>both archives</strong> by combining the SAPEXE.SAR with the SAPEXEDB.SAR for their platform.</p>\r\n<p style=\"padding-left: 30px;\">Frequency of distribution:&#160;One to three times a year depending on the kernel release.</p>\r\n<p style=\"padding-left: 30px;\">The SP Stack Kernels can be identified by the following patch levels:</p>\r\n<p style=\"padding-left: 30px;\">100 [101, 102,..., 109]</p>\r\n<p style=\"padding-left: 30px;\">200 [201, 202,..., 209]</p>\r\n<p style=\"padding-left: 30px;\">300 [...]</p>\r\n<p style=\"padding-left: 30px;\">...</p>\r\n<p style=\"padding-left: 30px;\">The patch numbers 101 to 109, 201 to 209, 301 to 309, and so on, are reserved for emergency corrections of the SP Stack Kernel.</p>\r\n<p style=\"padding-left: 30px;\">Delivered SP Stack Kernel versions stay available to download unless a version has been revoked because of a critical issue.</p>\r\n<p style=\"padding-left: 30px;\">Subscribe to the page&#160;<strong>SAP Kernel: Important News&#160;</strong><a target=\"_blank\" href=\"https://help.sap.com/docs/SUPPORT_CONTENT/si/3362958728.html\">https://help.sap.com/docs/SUPPORT_CONTENT/si/3362958728.html</a>,<br />where SAP announces:</p>\r\n<ul>\r\n<li>new SP Stack Kernel releases</li>\r\n<li>severe issues in kernel.</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\"><strong>3.2 Hotfixes</strong></p>\r\n<p style=\"padding-left: 30px;\">Regular kernel patches (hotfixes) are labelled by ascending&#160;patch levels: [110, 111, ..., 199], [210, 211, ..., 299], [310, 311, ...], and so on. Example: dw_219-80000760.sar.</p>\r\n<p style=\"padding-left: 30px;\">Not all of these numbers are actually used.</p>\r\n<p style=\"padding-left: 30px;\">The packages are <strong>cumulative</strong>. This means that the available version contains&#160;all&#160;corrections from the previous patch levels plus the latest fixes. As result,&#160;<strong>only&#160;the latest kernel patch</strong> is available for download.</p>\r\n<p style=\"padding-left: 30px;\">A separate SAP Note is created for each kernel correction.&#160;As soon as the correction is delivered, the&#160;exact patch level of the initial delivery can be found in the &#8220;Support Package Patches&#8221;&#160;section of the SAP Note.</p>\r\n<p style=\"padding-left: 60px;\"><strong>3.2.1 dw.sar kernel patch</strong></p>\r\n<p style=\"padding-left: 60px;\">The dw.sar package contains the smallest set of files that need to be replaced to update the program for the kernel runtime - the disp+work executable.&#160;When applying a new dw.sar package to the system, the package content must be replaced in full.</p>\r\n<p style=\"padding-left: 60px;\">Frequency of distribution: Weekly to monthly depending on the kernel release.</p>\r\n<p style=\"padding-left: 60px;\"><strong>3.2.2 Patches for individual kernel archives (for example, R3trans, kernel utilities, sapftp, saphttp)</strong></p>\r\n<p style=\"padding-left: 60px;\">Selected files from the SAPEXE.SAR and SAPEXEDB.SAR archives (SP Stack Kernel) are available separately for an update. These could be either single files or packages containing a few files that need to be replaced simultaneously. Refer to note&#160;<a target=\"_blank\" href=\"/notes/2966761\" title=\"2966761  - Overview of SAP Kernel Correction Archives\">2966761 - Overview of SAP kernel Correction Archives</a>&#160;for more details on individual kernel correction archives.</p>\r\n<p style=\"padding-left: 60px;\">Frequency of distribution: On demand.</p>\r\n<p><strong>4. Applying a kernel patch</strong></p>\r\n<p>If you wish to use the rolling kernel switch (RKS) procedure to minimize system downtime when replacing the kernel, refer to SAP Note <a target=\"_blank\" href=\"/notes/953653\">953653</a>.</p>\r\n<p style=\"padding-left: 30px;\"><strong>4.1 IBM i</strong></p>\r\n<p style=\"padding-left: 30px;\">The installation of kernel patches is automated for IBM i. For kernel versions 710 and higher, use the command APYSIDKRN (SAP Note <a target=\"_blank\" href=\"/notes/1097751\">1097751</a>). For lower kernel versions, the command APYR3FIX is used.</p>\r\n<p style=\"padding-left: 30px;\"><strong>4.2&#160;Unix/Linux&#160;</strong></p>\r\n<ul>\r\n<li>Copy the patch into a temporary directory on your system.</li>\r\n<li>Unpack the patch as described above.</li>\r\n<li>Stop the SAP system and the instance services (sapstartsrv).</li>\r\n<li>Save the current SAP kernel staging directory by backup or by copying into a separate backup directory. (This way, you will always have the option to return to the old kernel version if problems occur with the new patch level)</li>\r\n<li>Copy or move the unpacked binaries into the current SAP kernel staging directory:</li>\r\n<ul>\r\n<li>UNIX/Linux: /usr/sap/&lt;SAPSID&gt;/SYS/exe/run&#160; which is a symbolic link pointing to&#160; /sapmnt/&lt;SAPSID&gt;/exe/&lt;uc|nuc&gt;/&lt;platform&gt;</li>\r\n</ul>\r\n<li>Start the SAP system. \"sapcpe&#8221; will copy the required binaries from staging into the instance executable directory.</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\"><strong>4.3 Windows</strong></p>\r\n<ol>\r\n<li>Logon to Windows host with a domain or local user, which is a member of the local administrators group.<br /> It&#8217;s not recommended to use &lt;SID&gt;adm user to exchange/update a SAP kernel.</li>\r\n<li>Copy the sar-archive to a tempory directory on your Windows host, for example: c:\\temp</li>\r\n<li>Unpack the archive with sapcar.exe as described above.</li>\r\n<li>Stop the related SAP instance or in case you want to patch the ASCS instance, stop the SAP system.</li>\r\n<li>Make a backup of the central staging directory which is: &lt;SAPGLOBALHOST&gt;\\sapmnt\\&lt;SID&gt;\\sys\\exe\\uc\\ntamd64<br /> Warning!<br /> It is strongly recommended to create a copy/backup of the current staging directory! In case you run into problems, it&#8217;s easy to revert the changes and return to the old kernel/patch level.</li>\r\n<li>Copy all files from the temporary directory to &lt;SAPGLOBALHOST&gt;\\sapmnt\\&lt;SID&gt;\\sys\\exe\\uc\\ntamd64.<br /> This means that you overwrite existing files with newer versions.</li>\r\n<li>Start the SAP instance again. SAPCPE.EXE will copy the executables and DLLs from central staging directory to the local executable directory.</li>\r\n</ol>\r\n<p style=\"padding-left: 30px;\"><strong>4.4</strong>&#160;<strong>Copying and applying a patch for R3trans and tp</strong></p>\r\n<p style=\"padding-left: 30px;\">You do not have to stop the SAP system for the R3trans and tp programs. The following simplified procedure applies here:</p>\r\n<ul>\r\n<li>Copy the patch into a temporary directory on your system.</li>\r\n<li>Unpack the patch as described above.</li>\r\n<li>Save the affected program file (UNIX: R3trans or tp, Windows: R3trans.exe bzw. tp.exe) by copying to a separate backup directory.</li>\r\n<li>Copy the unpacked program to the SAP kernel directory.</li>\r\n<ul>\r\n<li>UNIX: /usr/sap/&lt;SAPSID&gt;/sys/exe/run</li>\r\n<li>Windows: &lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\exe\\&lt;uc|nuc&gt;\\&lt;platform&gt;</li>\r\n</ul>\r\n<li>Also copy or move the unpacked program into the kernel directories of the instances:</li>\r\n<ul>\r\n<li>UNIX: /usr/sap/&lt;SAPSID&gt;/&lt;instance name&gt;/exe</li>\r\n<li>Windows: &lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\&lt;instance name&gt;\\exe</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><strong>5. Recommendations for applying kernel patches</strong></p>\r\n<div class=\"longtext\">\r\n<p>The paper&#160;<a target=\"_blank\" href=\"https://support.sap.com/deployment-strategies-kernel-abap.pdf\" rel=\"noopener noreferrer\" tabindex=\"-1\" title=\"https://support.sap.com/deployment-strategies-kernel-abap.pdf\">Update Strategy for the Kernel of the Application Server ABAP in On Premise Landscapes</a>&#160;provides details on SAP recommendations on how to patch the SAP kernel.&#160;Please make yourself familiar with the SAP recommendations.</p>\r\n</div>\r\n<p style=\"padding-left: 30px;\"><strong>5.1 77*, 78*, 79* kernel versions</strong></p>\r\n<p style=\"padding-left: 30px;\">Generally, for 77*, 78* and 79* kernel versions, we recommend using the latest SP Stack Kernel (SAPEXE&#160;&amp; SAPEXEDB); see SAP Note <a target=\"_blank\" href=\"/notes/2083594\">2083594</a>. You should apply other patches (hotfixes) only if you require a specific correction or a new function in the kernel at short notice. For roadmap refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/2907361\" rel=\"nofollow\" title=\"2907361 - Release Roadmap for Kernel 77x and 78x\">2907361</a>.</p>\r\n<p style=\"padding-left: 30px;\">For the kernel versions in maintenance&#160;all regressions are listed as soon as they are found. An SAP Note search for KRNL<em>&lt;Kernel version&gt;</em>PL<em>&lt;Patch level&gt;</em>, for example, for kernel 777 patch level 210, this would be KRNL777PL210, determines the SAP Note that contains this list. It is advisable to view this SAP Note regularly. For more information about regressions in the kernel, refer to SAP Note <a target=\"_blank\" href=\"/notes/1802333\">1802333</a>.</p>\r\n<p style=\"padding-left: 30px;\"><strong>5.2 74* and 75* kernel versions</strong></p>\r\n<p style=\"padding-left: 30px;\">For 74* and 75* kernel versions, using the latest SP Stack Kernel (SAPEXE&#160;&amp; SAPEXEDB) is generally recommended; see SAP Note <a target=\"_blank\" href=\"/notes/2083594\">2083594</a>. You should apply other patches (hotfixes) only if you require a specific correction or a new function in the kernel on short notice.&#160;For roadmap refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/1969546\">1969546</a>.</p>\r\n<p style=\"padding-left: 30px;\">For the kernel versions in maintenance&#160;the system lists all regressions as soon as these are found. An SAP Note search for KRNL<em>&lt;Kernel version&gt;</em>PL<em>&lt;Patch level&gt;</em>, for example, for kernel 753 patch level 625, this would be KRNL753PL625, determines the SAP Note that contains this list. It is advisable to view this SAP Note regularly. For more information about regressions in the kernel, refer to SAP Note <a target=\"_blank\" href=\"/notes/1802333\">1802333</a>.</p>\r\n<p style=\"padding-left: 30px;\"><strong>5.3 72* kernel versions</strong></p>\r\n<p style=\"padding-left: 30px;\">For kernel versions 72*, using the latest SP Stack Kernel (SAPEXE &amp; SAPEXEDB) is generally recommended; see SAP Note <a target=\"_blank\" href=\"/notes/2083594\">2083594</a>. You should only apply other patches (hotfixes) if you temporarily need a specific fix or new feature in the kernel.&#160;For roadmap refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/1744209\" title=\"1744209  - SAP Kernel 720, 721 and 722: Versions and Kernel Patch Levels\">1744209</a>.</p>\r\n<p style=\"padding-left: 30px;\">For the kernel versions in maintenance, all regressions are listed as soon as they are found. An SAP Note search for KRNL<em>&lt;Kernel version&gt;</em>PL<em>&lt;Patch level&gt;</em>, for example, for kernel 722 patch level 1000, this would be KRNL722PL1000, determines the SAP Note that contains this list. It is advisable to view this SAP Note regularly. For more information about regressions in the kernel, refer to SAP Note <a target=\"_blank\" href=\"/notes/1802333\">1802333</a>.</p>\r\n<p style=\"padding-left: 30px;\"><strong>5.4 Older kernel versions</strong></p>\r\n<p style=\"padding-left: 60px;\"><strong>5.4.1 Stack kernel/Support Package Stacks (as of Kernel 4.6D)</strong></p>\r\n<p style=\"padding-left: 60px;\">For regular maintenance, we recommend importing Support Package Stacks (more information available at <a target=\"_blank\" href=\"https://support.sap.com/sp-stacks\">https://support.sap.com/sp-stacks</a>). A Support Package Stack consists of advanced kernel patches (\"kernel stacks\"), which are offered as a complete package. A stack kernel consists of the database-independent archive \"SAPEXE\" and the database-dependent archive \"SAPEXEDB\". On the SAP Support Portal, they are labeled as \"Kernel part I\" / \"Kernel part II\" with the specification of the stack version (for example, \"Stack Q3/2004\"). Only apply other patches if you have an actual problem.</p>\r\n<p style=\"padding-left: 60px;\">The existing recommendation continues to apply for Kernel Releases 3.1 (_EXT) to 4.5B (_EXT) for which no kernel stacks are available: Import the latest packages dw.sar, R3TRANS.SAR, TP.SAR and LIB_DBSL.SAR; import all other patches only for specific problems.</p>\r\n<p style=\"padding-left: 60px;\"><strong>5.4.2 Kernel 6.40 downward-compatible with 6.20/6.10</strong></p>\r\n<p style=\"padding-left: 60px;\">Kernel 6.40 is downward-compatible with 6.x so that the kernel of the current release can be used to eliminate kernel errors without the SAP system itself having to be upgraded to the new release. Use the current 6.40 patch kernel for troubleshooting because patches for lower 6.20 kernels will only remain available for a short time.</p>\r\n<p style=\"padding-left: 60px;\">However, it is important that each patch always matches a particular kernel release. For example, you cannot use individual 6.40 executables together with older kernel versions. Therefore, if you want to apply a 6.40 patch and you still work with an older kernel, you must exchange the kernel beforehand.</p>\r\n<p style=\"padding-left: 60px;\">Refer to SAP Note <a target=\"_blank\" href=\"/notes/664679\">664679</a> \"Installing 6.40 kernel in SAP WEB AS 6.10/6.20\".</p>\r\n<p style=\"padding-left: 60px;\"><strong>5.4.3 Kernel 4.6D/4.6D_EXT downward-compatible with 4.6A/B/C</strong></p>\r\n<p style=\"padding-left: 60px;\">To use the downward-compatible 4.6D Kernel in 46A/B/C systems, the same steps apply as those described above.</p>\r\n<p style=\"padding-left: 60px;\">Refer to SAP Note <a target=\"_blank\" href=\"/notes/318846\">318846</a> \"Installing the 4.6D kernel in 4.6A/B/C SAP systems\" in this case.</p>\r\n<p style=\"padding-left: 60px;\"><strong>5.4.4 Kernel 4.5B/4.5B_EXT downward-compatible with 4.5A</strong></p>\r\n<p style=\"padding-left: 60px;\">To use the downward-compatible 4.5B kernel with 4.5A systems, the same steps apply as those described above.</p>\r\n<p style=\"padding-left: 60px;\">Refer to SAP Note <a target=\"_blank\" href=\"/notes/149682\">149682</a> \"Installation of 4.5B/40B_COM kernel with 4.5A/40B system\" in this case.</p>\r\n<p style=\"padding-left: 60px;\"><strong>5.4.5 Kernel 4.0B_COM/4.0B_EXT downward-compatible with 4.0B, 4.0A</strong></p>\r\n<p style=\"padding-left: 60px;\">To use the downward-compatible 4.0B_COM kernel with 4.0A systems, the same steps apply as those described above.</p>\r\n<p style=\"padding-left: 60px;\">Refer to SAP Note <a target=\"_blank\" href=\"/notes/102461\">102461</a> \"Installation of 4.0B/40B_COM kernel with 4.0A/40B system\" in this case.</p>\r\n<p style=\"padding-left: 60px;\"><strong>5.4.6 Kernel 3.1I_COM/3.1I_EXT downward-compatible as of 3.0C</strong></p>\r\n<p style=\"padding-left: 60px;\">To use the downward-compatible 3.1I kernel with 3.x systems, the same steps apply as those described above.</p>\r\n<p style=\"padding-left: 60px;\">Refer to SAP Note <a target=\"_blank\" href=\"/notes/102445\">102445 </a> \"Installation 3.1I/31I_COM kernel w/. 3.0C/D/E/F,3.1G/H-DB\" in this case.</p>\r\n<p style=\"padding-left: 30px;\"><strong>5.5 Windows Failover Cluster</strong></p>\r\n<p style=\"padding-left: 30px;\">Make sure that you use a current version of the saprc.dll file before you upgrade to a new SAP kernel.</p>\r\n<p style=\"padding-left: 30px;\">Refer to SAP Note <a target=\"_blank\" href=\"/notes/1596496\">1596496</a> &#8211; \"How to update SAP Resource Type DLLs for Cluster Resource Monitor\".</p>\r\n<p style=\"padding-left: 30px;\">If you use an SAP kernel as of 7.45 with a very old version of the saprc.dll file (older than 2014), your SAP system will no longer start.</p>\r\n<p><span style=\"font-size: 14px;\">SUPPORT PACKAGES &amp; PATCHES &#8594;&#160;</span><strong style=\"font-size: 14px;\">Archive</strong><span style=\"font-size: 14px;\">&#160;by Alphabetical Index (A-Z)&#160;&#8594;K &#8594;SAP KERNEL &lt;variant&gt;, e.g. SAP KERNEL 64-BIT&#160;UNICODE &#8594;&#160;&lt;kernel version&gt;</span></p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "SAR"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I823601)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I533306)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000019466/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000019466/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000019466/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000019466/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000019466/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000019466/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000019466/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000019466/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000019466/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "953653", "RefComponent": "BC-CST", "RefTitle": "Rolling Kernel Switch", "RefUrl": "/notes/953653"}, {"RefNumber": "664679", "RefComponent": "BC-CST", "RefTitle": "Installing 6.40 kernel in SAP WEB AS 6.10/6.20", "RefUrl": "/notes/664679"}, {"RefNumber": "318846", "RefComponent": "BC-CST", "RefTitle": "Installing the 4.6D kernel in 4.6A/B/C SAP systems", "RefUrl": "/notes/318846"}, {"RefNumber": "2907361", "RefComponent": "BC-UPG-PRP", "RefTitle": "Release Roadmap for Kernel 77x and 78x and 79x", "RefUrl": "/notes/2907361"}, {"RefNumber": "2083594", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel Versions and SAP Kernel Patch Levels", "RefUrl": "/notes/2083594"}, {"RefNumber": "1969546", "RefComponent": "BC-UPG-PRP", "RefTitle": "Release Roadmap for Kernel 74x and 75x", "RefUrl": "/notes/1969546"}, {"RefNumber": "1802333", "RefComponent": "BC-CST", "RefTitle": "Finding information about regressions in the SAP kernel", "RefUrl": "/notes/1802333"}, {"RefNumber": "1744209", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 720, 721 and 722: Versions and Kernel Patch Levels", "RefUrl": "/notes/1744209"}, {"RefNumber": "149682", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/149682"}, {"RefNumber": "1097751", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Information and recommendations for kernel libraries", "RefUrl": "/notes/1097751"}, {"RefNumber": "102461", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/102461"}, {"RefNumber": "102445", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/102445"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2517546", "RefComponent": "BC-CST-IC", "RefTitle": "ESI - Service Ping ERROR: invalid host header found", "RefUrl": "/notes/2517546 "}, {"RefNumber": "3220461", "RefComponent": "BC-FES-WGU", "RefTitle": "WebGUI_Cannot select texts in TableControl since kernel upgrade", "RefUrl": "/notes/3220461 "}, {"RefNumber": "2657076", "RefComponent": "BC-CST", "RefTitle": "Sessions remain in SM04", "RefUrl": "/notes/2657076 "}, {"RefNumber": "3255687", "RefComponent": "BC-CST-STS", "RefTitle": "Unable to start the system after updating or downgrading the SAP kernel", "RefUrl": "/notes/3255687 "}, {"RefNumber": "2308858", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Checks after phase PREP_GENCHECKS/NTACT_CHK were negative!", "RefUrl": "/notes/2308858 "}, {"RefNumber": "1898829", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Error during upgrade phase 'TR_CMDIMPORT_FDTASKS'", "RefUrl": "/notes/1898829 "}, {"RefNumber": "1948943", "RefComponent": "BC-UPG-PRP", "RefTitle": "Error in upgrade phase \"PREP_IMPORT/SQLDB_CLEARPATCHES\"", "RefUrl": "/notes/1948943 "}, {"RefNumber": "2490106", "RefComponent": "BC-CTS-TMS-CTR", "RefTitle": "cCTS server must be updated on system", "RefUrl": "/notes/2490106 "}, {"RefNumber": "2487473", "RefComponent": "BC-CTS-TLS", "RefTitle": "Internal error during writing of dynpro fields to the database", "RefUrl": "/notes/2487473 "}, {"RefNumber": "2393434", "RefComponent": "BC-OP-AS4", "RefTitle": "SAP system doesn't start ... Cannot execute igswd_mt", "RefUrl": "/notes/2393434 "}, {"RefNumber": "3198412", "RefComponent": "BC-INS-TC-CNT", "RefTitle": "Long running PCA tasklist", "RefUrl": "/notes/3198412 "}, {"RefNumber": "3194367", "RefComponent": "BC-CST-LG", "RefTitle": "German \"umlaut\" characters in SNC names on the Logon Group layer (Message Server)", "RefUrl": "/notes/3194367 "}, {"RefNumber": "2382789", "RefComponent": "BC-CST-STS", "RefTitle": "Application server fail to start after Kernel update / Upgrade / Installation", "RefUrl": "/notes/2382789 "}, {"RefNumber": "3148968", "RefComponent": "BC-CST-IC", "RefTitle": "FAQ for SAP Security Note 3123396 [CVE-2022-22536] Request smuggling and request concatenation", "RefUrl": "/notes/3148968 "}, {"RefNumber": "3121355", "RefComponent": "BC-CST", "RefTitle": "Shortdumps CALL_FUNCTION_ACCEPT_FAILED", "RefUrl": "/notes/3121355 "}, {"RefNumber": "3118807", "RefComponent": "QM-FIO", "RefTitle": "QM Fiori apps: in long text editor the end of the text line is overlapped", "RefUrl": "/notes/3118807 "}, {"RefNumber": "2395494", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: The “Show Source” button is disabled when checking \"TOP SQL Statements\" in Transaction DBACOCKPIT", "RefUrl": "/notes/2395494 "}, {"RefNumber": "1792296", "RefComponent": "BC-CST-IC", "RefTitle": "CST – Error: Operation failed (rc=1) Message no. ICM006", "RefUrl": "/notes/1792296 "}, {"RefNumber": "2173629", "RefComponent": "BC-CST-MM", "RefTitle": "CST - Memory dumps due to global PROC memory limit", "RefUrl": "/notes/2173629 "}, {"RefNumber": "2992775", "RefComponent": "BC-CST-DP", "RefTitle": "CST - SAP System becomes standstill with DpILogonCntTabChange in C-STACK of work process trace", "RefUrl": "/notes/2992775 "}, {"RefNumber": "2631116", "RefComponent": "BC-CST-EQ", "RefTitle": "CST - Enqueue Server status: Running but not responding", "RefUrl": "/notes/2631116 "}, {"RefNumber": "2355918", "RefComponent": "BC-CST-DP", "RefTitle": "CST - Flooding of ABAP system with \"Load info\" messages", "RefUrl": "/notes/2355918 "}, {"RefNumber": "2542767", "RefComponent": "BC-CST-IC", "RefTitle": "ICM: ERROR => IcmConnProxyHandshake: proxy read(5000) failed(rc=-<nr>)", "RefUrl": "/notes/2542767 "}, {"RefNumber": "2249530", "RefComponent": "BC-CST-DP", "RefTitle": "CST - Resources Check in Kernels greater than 7.4X", "RefUrl": "/notes/2249530 "}, {"RefNumber": "3100003", "RefComponent": "BC-CST", "RefTitle": "CST - Template KBA - Version Independent", "RefUrl": "/notes/3100003 "}, {"RefNumber": "3096195", "RefComponent": "BC-UPG-NA", "RefTitle": "'Error in serialization' during note download in Non-Unicode system", "RefUrl": "/notes/3096195 "}, {"RefNumber": "3064761", "RefComponent": "BC-CST", "RefTitle": "CST - Template KBA - Version Dependent", "RefUrl": "/notes/3064761 "}, {"RefNumber": "2172522", "RefComponent": "BC-CTS-TLS", "RefTitle": "ALOG contains non-analyzable data", "RefUrl": "/notes/2172522 "}, {"RefNumber": "3055348", "RefComponent": "BC-SYB-REP", "RefTitle": "Fault Manager not working in ASE 16.0 SP04 GA - SRS", "RefUrl": "/notes/3055348 "}, {"RefNumber": "2479624", "RefComponent": "BC-UPG-NA", "RefTitle": "UNCAUGHT_EXECPTION dump while downloading SAP Note", "RefUrl": "/notes/2479624 "}, {"RefNumber": "3003693", "RefComponent": "BC-CST-STS", "RefTitle": "System not starting with error 'mismatch of table VBHDR'", "RefUrl": "/notes/3003693 "}, {"RefNumber": "2650414", "RefComponent": "BC-DB-SYB", "RefTitle": "startsap does not work on Linux Machine After Kernel Upgrade - SAP ASE for BS", "RefUrl": "/notes/2650414 "}, {"RefNumber": "2052988", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "The executable R3trans in .SUM/abap/exe is too old. It needs a release date of <date> or later.", "RefUrl": "/notes/2052988 "}, {"RefNumber": "2875858", "RefComponent": "BC-UPG-NA", "RefTitle": "Syntax error in SAPLSPAM '<LS_INTERVALL_QUEUE>-OBJ_VERSNOS' is not compatible with field symbol", "RefUrl": "/notes/2875858 "}, {"RefNumber": "1541076", "RefComponent": "BC-CTS-TMS", "RefTitle": "Preliminary checks and troubleshooting for transport issues", "RefUrl": "/notes/1541076 "}, {"RefNumber": "2036084", "RefComponent": "BC-CTS-TMS", "RefTitle": "WARNING:  Background job RDDIMPDP could not be started or terminated abnormally.", "RefUrl": "/notes/2036084 "}, {"RefNumber": "2791479", "RefComponent": "BC-EIM-ESH", "RefTitle": "CALL_FUNCTION_SEND_ERROR dump in SAPLESH_FED_PARALLEL", "RefUrl": "/notes/2791479 "}, {"RefNumber": "2850410", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "DBSL is missing during remote database connection to Oracle", "RefUrl": "/notes/2850410 "}, {"RefNumber": "2515633", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "SUM: ABAP  \"Missing opening bracket \" error in EU_CLONE_MIG_UT_PRP", "RefUrl": "/notes/2515633 "}, {"RefNumber": "2825193", "RefComponent": "BC-FES-IGS", "RefTitle": "IGS Watchdog is failing, (has GRAY stastus) in System start phase during installation with Software Provisioning Manager (SWPM Tool)", "RefUrl": "/notes/2825193 "}, {"RefNumber": "2106205", "RefComponent": "BC-INS-SWPM", "RefTitle": "dipgntab failed with: \"ERROR in initialization (can't get R/3-version)\" or \"ERROR in initialization (can't get SVERS-version)\"", "RefUrl": "/notes/2106205 "}, {"RefNumber": "2769084", "RefComponent": "PM-WOC-M<PERSON>", "RefTitle": "Unable to access /n switch from same transaction.", "RefUrl": "/notes/2769084 "}, {"RefNumber": "2806098", "RefComponent": "BC-DB-ORA", "RefTitle": "DBSQL_INTERNAL_ERROR and ORA-28267", "RefUrl": "/notes/2806098 "}, {"RefNumber": "2788793", "RefComponent": "BC-UPG-NA", "RefTitle": "SNOTE: Installed SAPCAR version does not support verification of signed archives", "RefUrl": "/notes/2788793 "}, {"RefNumber": "2328478", "RefComponent": "BC-INS-MIG", "RefTitle": "Error in Import: DECFLOAT_DEC to BCD conversion", "RefUrl": "/notes/2328478 "}, {"RefNumber": "2753164", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "Program ZBW_HANA_CHECKLIST fails with Dump in transaction st22 with header MOVE_TO_LIT_NOTALLOWED_NODATA", "RefUrl": "/notes/2753164 "}, {"RefNumber": "2201677", "RefComponent": "BC-CTS-TLS", "RefTitle": "It is always recommended to use the latest tp and R3trans", "RefUrl": "/notes/2201677 "}, {"RefNumber": "2699815", "RefComponent": "BC-CTS-ORG", "RefTitle": "unknown function 'TOMD' - Warning in transport logs", "RefUrl": "/notes/2699815 "}, {"RefNumber": "2732486", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "SUM stuck in phase \"RUN_CREATE_ABAP_DEL_TRANSP\" Batchjob RSUPG_CREATE_ABAP_DEL_TRANSP failed with return code: \"0006\"", "RefUrl": "/notes/2732486 "}, {"RefNumber": "2713822", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "SUM stops in upgrade phase MAIN_SHDRUN/SUBMOD_DIFFEXP/JOB_* with dump DBSQL_MISSING_TENANT_KEY_ENTRY", "RefUrl": "/notes/2713822 "}, {"RefNumber": "2697083", "RefComponent": "BC-DB-SYB", "RefTitle": "stopdb terminates with Error Code 13 - SAP ASE for Business Suite", "RefUrl": "/notes/2697083 "}, {"RefNumber": "1943605", "RefComponent": "BC-DB-SYB", "RefTitle": "\"R3trans -d\" fails following ASE upgrade - SAP ASE for Business Suite", "RefUrl": "/notes/1943605 "}, {"RefNumber": "2618069", "RefComponent": "BC-FES-WGU", "RefTitle": "Webgui shows empty \"SAP Popup Window\"", "RefUrl": "/notes/2618069 "}, {"RefNumber": "2615828", "RefComponent": "BC-INS-SWPM", "RefTitle": "Archive file '(...).SAR' did not match", "RefUrl": "/notes/2615828 "}, {"RefNumber": "2596868", "RefComponent": "BC-INS-TLS", "RefTitle": "SAPCAR error: SAPCAR: can not change date for /usr/sap/trans/SIGNATURE.SMF (error 14). Operation not permitted", "RefUrl": "/notes/2596868 "}, {"RefNumber": "2596441", "RefComponent": "BC-UPG-NA", "RefTitle": "Error during uploading 0002424539_00.SAR file after Note 2408073 implementation", "RefUrl": "/notes/2596441 "}, {"RefNumber": "2428673", "RefComponent": "BC-SRV-GBT-GOS", "RefTitle": "Syntax error during implementing/activating SAP Note 2293171", "RefUrl": "/notes/2428673 "}, {"RefNumber": "2459456", "RefComponent": "BC-CST", "RefTitle": "How to find User Assistance for Client-Server Technology (BC-CST*)", "RefUrl": "/notes/2459456 "}, {"RefNumber": "2048215", "RefComponent": "BC-MID-RFC", "RefTitle": "Troubleshooting CALL_FUNCTION_SIGNON_INCOMPL runtime errors", "RefUrl": "/notes/2048215 "}, {"RefNumber": "1747927", "RefComponent": "BC-SRV-ARL", "RefTitle": "Dump DATA_OFFSET_LENGTH_TOO_LARGE raised when scrolling in a print list", "RefUrl": "/notes/1747927 "}, {"RefNumber": "2559793", "RefComponent": "BC-UPG-OCS", "RefTitle": "Could not read transport info for <package> in SPAM/SAINT", "RefUrl": "/notes/2559793 "}, {"RefNumber": "2040298", "RefComponent": "BC-MID-RFC", "RefTitle": "CALL_FUNCTION_SEND_ERROR CM_DEALLOCATED_NORMAL cmRc=18", "RefUrl": "/notes/2040298 "}, {"RefNumber": "2508588", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Error in phase EU_CLONE_CRE_SHDVIEWS - unknown template variable", "RefUrl": "/notes/2508588 "}, {"RefNumber": "1793593", "RefComponent": "BC-UPG-PRP", "RefTitle": "Severe error in the phase PREP_INTEGRATION/GENBUFFER_PREPUT", "RefUrl": "/notes/1793593 "}, {"RefNumber": "2506197", "RefComponent": "BC-DB-MSS", "RefTitle": "SQL error 137 during Upgrade in phase XPRA_EXECUTION", "RefUrl": "/notes/2506197 "}, {"RefNumber": "2501816", "RefComponent": "BC-INS-MIG", "RefTitle": "How to fix when system copy hangs for a long time", "RefUrl": "/notes/2501816 "}, {"RefNumber": "2495018", "RefComponent": "BC-DB-ORA-CCM", "RefTitle": "Offline backup does not work from DBACOCKPIT/DB13", "RefUrl": "/notes/2495018 "}, {"RefNumber": "2487825", "RefComponent": "BC-UPG-PRP", "RefTitle": "SUM Error: Expected matching releases of kernel archives in the phase PREP_INPUT_CHECK/SCANDIR_COLLECT", "RefUrl": "/notes/2487825 "}, {"RefNumber": "1586315", "RefComponent": "BC-OP-NT", "RefTitle": "Dialog Queue Info Unavailable in SAP MMC After Kernel Update", "RefUrl": "/notes/1586315 "}, {"RefNumber": "2482610", "RefComponent": "BC-DB-DB6", "RefTitle": "Attempt to convert virtual Table SVERS failed with dbcode -104", "RefUrl": "/notes/2482610 "}, {"RefNumber": "2452731", "RefComponent": "BC-FES-ITS", "RefTitle": "Webgui: Dropdown List element selection problem in Firefox 52 or higher", "RefUrl": "/notes/2452731 "}, {"RefNumber": "2433966", "RefComponent": "BC-ABA-LA", "RefTitle": "Programs contained Host Variables are terminated with SYNTAX_ERROR", "RefUrl": "/notes/2433966 "}, {"RefNumber": "2430338", "RefComponent": "OPU-GW-COR", "RefTitle": "BRFplus dump: Inconsistency in the dictionary for structure \"TY_S_CONDITIONS\"", "RefUrl": "/notes/2430338 "}, {"RefNumber": "1817692", "RefComponent": "BC-INS-MIG", "RefTitle": "Installation phase Post Load Activities -  An error occurred while processing option.", "RefUrl": "/notes/1817692 "}, {"RefNumber": "3429944", "RefComponent": "BC-CST", "RefTitle": "SAP Support Package Stack Kernel 7.54 Patch Level 300", "RefUrl": "/notes/3429944 "}, {"RefNumber": "3434625", "RefComponent": "BC-CTS-TLS", "RefTitle": "gCTS: Performance when pulling CDAT TABU TDAT VDAT objects", "RefUrl": "/notes/3434625 "}, {"RefNumber": "3428728", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans aborts with signal 11 during 'import preview' which leads to return code 13", "RefUrl": "/notes/3428728 "}, {"RefNumber": "3420050", "RefComponent": "BC-CST", "RefTitle": "SAP Support Package Stack Kernel 7.53 Patch Level 1300", "RefUrl": "/notes/3420050 "}, {"RefNumber": "3420412", "RefComponent": "BC-CST", "RefTitle": "SAP Support Package Stack Kernel 7.54 Patch Level 201", "RefUrl": "/notes/3420412 "}, {"RefNumber": "3420156", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp: superfluous warning message in SLOG", "RefUrl": "/notes/3420156 "}, {"RefNumber": "3418005", "RefComponent": "BC-CTS-TLS", "RefTitle": "new transport profile parameter DELIVERY_TR_IS_TENANT_ONLY", "RefUrl": "/notes/3418005 "}, {"RefNumber": "3412664", "RefComponent": "BC-CTS-TLS", "RefTitle": "Export of a 'logical transport object' fails in case it contains parts of type 'LIMU DOCU'", "RefUrl": "/notes/3412664 "}, {"RefNumber": "3406288", "RefComponent": "BC-CTS-TLS", "RefTitle": "Export of objects of type R3TR DOCV IMxyz fails", "RefUrl": "/notes/3406288 "}, {"RefNumber": "3405447", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp returncode 250 during Import", "RefUrl": "/notes/3405447 "}, {"RefNumber": "3401430", "RefComponent": "BC-CTS-TLS", "RefTitle": "Export of a 'logical transport object' fails in case it contains parts of type 'LIMU DOCU'", "RefUrl": "/notes/3401430 "}, {"RefNumber": "3387313", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans stops with siganl 6 in upgrade PREPUT phase", "RefUrl": "/notes/3387313 "}, {"RefNumber": "3385608", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp does not stop immediately in dictionary- or main-import in case of an error.", "RefUrl": "/notes/3385608 "}, {"RefNumber": "3379306", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: limit implicit transport of SPROX tables to 7.02 SP1", "RefUrl": "/notes/3379306 "}, {"RefNumber": "3378263", "RefComponent": "BC-CTS-TLS", "RefTitle": "stop implicit transport of SPR0X tables during upgrade", "RefUrl": "/notes/3378263 "}, {"RefNumber": "3375722", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans shows error message 'Unexpected record length' when reding a *.reduced datafile", "RefUrl": "/notes/3375722 "}, {"RefNumber": "3367199", "RefComponent": "BC-CTS-TLS", "RefTitle": "gCTS: revert fingerprinting algorithm to the 794 R3trans version", "RefUrl": "/notes/3367199 "}, {"RefNumber": "3359089", "RefComponent": "BC-CST", "RefTitle": "SAP Support Package Stack Kernel 7.85 Patch Level 300", "RefUrl": "/notes/3359089 "}, {"RefNumber": "3361969", "RefComponent": "BC-CTS-GIT", "RefTitle": "gCTS: preserve existing role when a namespace is imported.", "RefUrl": "/notes/3361969 "}, {"RefNumber": "3353627", "RefComponent": "BC-CST", "RefTitle": "SAP Support Package Stack Kernel 7.77 Patch Level 600", "RefUrl": "/notes/3353627 "}, {"RefNumber": "3348732", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Incorrect log message during field conversion", "RefUrl": "/notes/3348732 "}, {"RefNumber": "3346272", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans gets signal 6 during export of a table with key field length longer than 172", "RefUrl": "/notes/3346272 "}, {"RefNumber": "3330151", "RefComponent": "BC-CST", "RefTitle": "SAP Support Package Stack Kernel 7.22 EXT, EX2 Patch Level 1300", "RefUrl": "/notes/3330151 "}, {"RefNumber": "3339948", "RefComponent": "BC-CST", "RefTitle": "SAP Support Package Stack Kernel 7.54 Patch Level 200", "RefUrl": "/notes/3339948 "}, {"RefNumber": "3334442", "RefComponent": "BC-CTS-TLS", "RefTitle": "Upgrade stops in phase TABIM_UPG due to R3trans error", "RefUrl": "/notes/3334442 "}, {"RefNumber": "3327172", "RefComponent": "BC-CST", "RefTitle": "SAP Support Package Stack Kernel 7.53 Patch Level 1200", "RefUrl": "/notes/3327172 "}, {"RefNumber": "3320776", "RefComponent": "BC-CTS-TLS", "RefTitle": "'getimportstate' breaks with signal11", "RefUrl": "/notes/3320776 "}, {"RefNumber": "3320576", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp put with umode 1", "RefUrl": "/notes/3320576 "}, {"RefNumber": "3320394", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp: avoid crash in case of an error in process handling on windows", "RefUrl": "/notes/3320394 "}, {"RefNumber": "3307536", "RefComponent": "BC-CST", "RefTitle": "SAP Support Package Stack Kernel 7.81 Patch Level 400", "RefUrl": "/notes/3307536 "}, {"RefNumber": "3300373", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "inactive export of objects of type DRAS DRTY DSFD DSFI", "RefUrl": "/notes/3300373 "}, {"RefNumber": "3287208", "RefComponent": "BC-CST", "RefTitle": "SAP Support Package Stack Kernel 7.54 Patch Level 100 (RTC)", "RefUrl": "/notes/3287208 "}, {"RefNumber": "3295255", "RefComponent": "BC-CTS-TLS", "RefTitle": "Wrong R3trans error message if transport request owner name contains / and number", "RefUrl": "/notes/3295255 "}, {"RefNumber": "3291747", "RefComponent": "BC-CTS-TLS", "RefTitle": "autorepeat in case of severe errors", "RefUrl": "/notes/3291747 "}, {"RefNumber": "3281854", "RefComponent": "BC-MID-RFC", "RefTitle": "FAQ for Security Note 3089413", "RefUrl": "/notes/3281854 "}, {"RefNumber": "3289422", "RefComponent": "BC-CTS-TLS", "RefTitle": "transport of VDAT objects: view based updates do not arrive in target system", "RefUrl": "/notes/3289422 "}, {"RefNumber": "3283014", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans during upgrade: returncode 16 accessing table TFDIR", "RefUrl": "/notes/3283014 "}, {"RefNumber": "3280484", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans error Internal error: Constant \"MAXFIELD\" ... is too short.", "RefUrl": "/notes/3280484 "}, {"RefNumber": "3270316", "RefComponent": "BC-CST", "RefTitle": "SAP Support Package Stack Kernel 7.53 Patch Level 1100", "RefUrl": "/notes/3270316 "}, {"RefNumber": "3278141", "RefComponent": "BC-CTS-TLS", "RefTitle": "gCTS: handling of object types SMDD and SMDL (modification info)", "RefUrl": "/notes/3278141 "}, {"RefNumber": "3269829", "RefComponent": "BC-CTS-TLS", "RefTitle": "error message during export with PCA tool: cannot open file ...", "RefUrl": "/notes/3269829 "}, {"RefNumber": "3269338", "RefComponent": "BC-CTS-TLS", "RefTitle": "Import fails with the error message: Update on table ... failed with RTEDUPLICATEKEY error for unknown reason", "RefUrl": "/notes/3269338 "}, {"RefNumber": "3269297", "RefComponent": "BC-CTS-TLS", "RefTitle": "failing DELETEs in case of underscore in generic selection", "RefUrl": "/notes/3269297 "}, {"RefNumber": "3266378", "RefComponent": "BC-CTS-TLS", "RefTitle": "SUM: error in phase TABIM_UPG due to table delivery class L", "RefUrl": "/notes/3266378 "}, {"RefNumber": "3258282", "RefComponent": "BC-CTS-TLS", "RefTitle": "treat SQL errors always with return code 0012", "RefUrl": "/notes/3258282 "}, {"RefNumber": "3257372", "RefComponent": "BC-CTS-TLS", "RefTitle": "gCTS: repository export fails when objects of type HOTA are to be pulled", "RefUrl": "/notes/3257372 "}, {"RefNumber": "3256932", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans Multi-Tenancy-Import:  LIMU REPS belonging to R3TR XSLT wrongly categorized as 'shared'", "RefUrl": "/notes/3256932 "}, {"RefNumber": "3254868", "RefComponent": "BC-CTS-TLS", "RefTitle": "improvements that concern the transport of  LANG DOCU objects", "RefUrl": "/notes/3254868 "}, {"RefNumber": "3254866", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans for SUM: improved \"reduce\" functionality", "RefUrl": "/notes/3254866 "}, {"RefNumber": "3249022", "RefComponent": "BC-CTS-TLS", "RefTitle": "upgrade stops with return code 0008 due to duplicate key error on table DD17S / missing extension index after upgrade", "RefUrl": "/notes/3249022 "}, {"RefNumber": "3248121", "RefComponent": "BC-CST-STS", "RefTitle": "Which SAP Kernel Patch Level is reported to the SLD", "RefUrl": "/notes/3248121 "}, {"RefNumber": "3245558", "RefComponent": "BC-CTS-TLS", "RefTitle": "data dictionary activation error in upgrade phase ACT_UPG", "RefUrl": "/notes/3245558 "}, {"RefNumber": "3242697", "RefComponent": "BC-CTS-TLS", "RefTitle": "upgrade stops with error message 'missing exchange table for TRESC'", "RefUrl": "/notes/3242697 "}, {"RefNumber": "3239657", "RefComponent": "BC-CTS-TLS", "RefTitle": "language import fails with return code 16 due to invalid string handle", "RefUrl": "/notes/3239657 "}, {"RefNumber": "3239066", "RefComponent": "BC-MID-UCO", "RefTitle": "Wrong created frontend statistic sub records", "RefUrl": "/notes/3239066 "}, {"RefNumber": "3238961", "RefComponent": "BC-CTS-TLS", "RefTitle": "Import aborts with sql error on table DDDTDC_SOURCE due to missing field DDLANGUAGE", "RefUrl": "/notes/3238961 "}, {"RefNumber": "3228098", "RefComponent": "BC-CTS-TLS", "RefTitle": "reimport into another client of source system fails", "RefUrl": "/notes/3228098 "}, {"RefNumber": "3227110", "RefComponent": "BC-CTS-TLS", "RefTitle": "Improvements in parallel import", "RefUrl": "/notes/3227110 "}, {"RefNumber": "3226636", "RefComponent": "BC-CTS-TLS", "RefTitle": "Performance Improvement for tp during 'shared Import'", "RefUrl": "/notes/3226636 "}, {"RefNumber": "3212758", "RefComponent": "BC-CTS-TLS", "RefTitle": "Bugfix post language import", "RefUrl": "/notes/3212758 "}, {"RefNumber": "3222442", "RefComponent": "BC-CTS-TLS", "RefTitle": "Function groups with syntax errors after upgrade", "RefUrl": "/notes/3222442 "}, {"RefNumber": "3210035", "RefComponent": "BC-CTS-TLS", "RefTitle": "gCTS: import fails with \"unknown field in table\"", "RefUrl": "/notes/3210035 "}, {"RefNumber": "3217122", "RefComponent": "BC-CTS-TLS", "RefTitle": "Wrong error handling of string handles in R3trans", "RefUrl": "/notes/3217122 "}, {"RefNumber": "3216972", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans core on platform AIX", "RefUrl": "/notes/3216972 "}, {"RefNumber": "3215795", "RefComponent": "BC-CTS-TLS", "RefTitle": "proper reset of static tp variables", "RefUrl": "/notes/3215795 "}, {"RefNumber": "3153907", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Support for new OS release IBM i 7.5", "RefUrl": "/notes/3153907 "}, {"RefNumber": "3207438", "RefComponent": "BC-CTS-TLS", "RefTitle": "gCTS: pulling initial commit does not remove customizing data from system", "RefUrl": "/notes/3207438 "}, {"RefNumber": "3201487", "RefComponent": "BC-CTS-TLS", "RefTitle": "gCTS: after import methods run in client 000 instead of selected target client", "RefUrl": "/notes/3201487 "}, {"RefNumber": "3192233", "RefComponent": "BC-CTS-TLS", "RefTitle": "gCTS: treat systems of type VCS always with ctc=FALSE", "RefUrl": "/notes/3192233 "}, {"RefNumber": "3191189", "RefComponent": "BC-CTS-TLS", "RefTitle": "gCTS: import to git repository is incomplete due to rc=0008", "RefUrl": "/notes/3191189 "}, {"RefNumber": "3168318", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans core or crash in function db_basic_str_prep", "RefUrl": "/notes/3168318 "}, {"RefNumber": "3155924", "RefComponent": "BC-CST", "RefTitle": "SAP Support Package Stack Kernel 7.53 Patch Level 1000", "RefUrl": "/notes/3155924 "}, {"RefNumber": "3165078", "RefComponent": "BC-CTS-TLS", "RefTitle": "no deletion of obsolete function modules", "RefUrl": "/notes/3165078 "}, {"RefNumber": "3164129", "RefComponent": "BC-CTS-TLS", "RefTitle": "gCTS: table entries with keys longer than 120 caharcters", "RefUrl": "/notes/3164129 "}, {"RefNumber": "3063817", "RefComponent": "BC-CST-IC", "RefTitle": "Note Template for Web Dispatcher and ICM corrections", "RefUrl": "/notes/3063817 "}, {"RefNumber": "3063808", "RefComponent": "BC-CST-WDP", "RefTitle": "Note Template for Web Dispatcher only corrections", "RefUrl": "/notes/3063808 "}, {"RefNumber": "3156866", "RefComponent": "BC-CST", "RefTitle": "Using Kernel 7.54 instead of Kernel 7.40, 7.41, 7.42, 7.45, 7.49 or 7.53", "RefUrl": "/notes/3156866 "}, {"RefNumber": "3154061", "RefComponent": "BC-CTS-TLS", "RefTitle": "gCTS: Cloning a Git repository or pulling content fails with error message 'data file is destroyed'", "RefUrl": "/notes/3154061 "}, {"RefNumber": "3146013", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans stops with error message \"abtypes.h: unknown ddic type \"0\".\"", "RefUrl": "/notes/3146013 "}, {"RefNumber": "3142779", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: performance fix for tables with xstring fields", "RefUrl": "/notes/3142779 "}, {"RefNumber": "3142139", "RefComponent": "BC-CTS-TLS", "RefTitle": "Hana Object Transport: 'tp getprots sid action=5' fails", "RefUrl": "/notes/3142139 "}, {"RefNumber": "3133273", "RefComponent": "BC-CTS-GIT", "RefTitle": "gcTS: performance of customizing transports from Git to ABAP", "RefUrl": "/notes/3133273 "}, {"RefNumber": "3125260", "RefComponent": "BC-CTS-TLS", "RefTitle": "'ERROR_CHECKING_IMPORTSTATE' during GETIMPORTSTATE due to wrong length of SAP_TPLOGPTR_LN", "RefUrl": "/notes/3125260 "}, {"RefNumber": "3125012", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: the deletion of a R3TR ENHO transport object is not recognized correctly", "RefUrl": "/notes/3125012 "}, {"RefNumber": "3124274", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans import breaks with RC=0016 after", "RefUrl": "/notes/3124274 "}, {"RefNumber": "3122424", "RefComponent": "BC-CTS-TLS", "RefTitle": "SUM upgrade stops in phase DIFFEXPABAP* 'invalid string handle'", "RefUrl": "/notes/3122424 "}, {"RefNumber": "3118949", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans export log shows \"ACGR has 2 primary tables\" and return code 4", "RefUrl": "/notes/3118949 "}, {"RefNumber": "3112157", "RefComponent": "BC-CTS-TLS", "RefTitle": "deletion of table entries with double asterisk in the selection key", "RefUrl": "/notes/3112157 "}, {"RefNumber": "3111708", "RefComponent": "BC-CTS-TLS", "RefTitle": "Improved main memory usage in case of parallel import with many processes", "RefUrl": "/notes/3111708 "}, {"RefNumber": "3111713", "RefComponent": "BC-CTS-GIT", "RefTitle": "gCTS: support for SNOTE scenario (partial objects in a git repository)", "RefUrl": "/notes/3111713 "}, {"RefNumber": "3107459", "RefComponent": "BC-CST", "RefTitle": "SAP Support Package Stack Kernel 7.53 Patch Level 900", "RefUrl": "/notes/3107459 "}, {"RefNumber": "3106360", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans fails when parsing large control files", "RefUrl": "/notes/3106360 "}, {"RefNumber": "3103559", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans export writes E071-OBJFUNC 'D' for transport object R3TR TABL if table is not found.", "RefUrl": "/notes/3103559 "}, {"RefNumber": "3095543", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp fails with error message concerning BATCHHOST on steampunk systems", "RefUrl": "/notes/3095543 "}, {"RefNumber": "3076355", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp clearold does not remove original files during archiving", "RefUrl": "/notes/3076355 "}, {"RefNumber": "3081872", "RefComponent": "BC-CTS-TLS", "RefTitle": "gCTS: no export of modification info from git repository", "RefUrl": "/notes/3081872 "}, {"RefNumber": "3081534", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp error message „MAX_IMPORT_STEPS too small\" during \"IMPORT SOME\"", "RefUrl": "/notes/3081534 "}, {"RefNumber": "3080523", "RefComponent": "BC-CTS-TLS", "RefTitle": "client transport issues with table BSEG", "RefUrl": "/notes/3080523 "}, {"RefNumber": "3079367", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: enable memory limit for string cache also for copy-to-shadow", "RefUrl": "/notes/3079367 "}, {"RefNumber": "3078595", "RefComponent": "BC-CTS-TLS", "RefTitle": "SPAM: missing buffer synchronization after 'downtime optimized import'", "RefUrl": "/notes/3078595 "}, {"RefNumber": "3078356", "RefComponent": "BC-CTS-TLS", "RefTitle": "Program tp does not start the execution of step METHOD EXECUTION POST-PROCESSING", "RefUrl": "/notes/3078356 "}, {"RefNumber": "3078349", "RefComponent": "BC-CTS-TLS", "RefTitle": "gCTS: initialization of last modification date and time", "RefUrl": "/notes/3078349 "}, {"RefNumber": "3075295", "RefComponent": "BC-CTS-TLS", "RefTitle": "gCTS: logical transport object metada is not updated", "RefUrl": "/notes/3075295 "}, {"RefNumber": "3072886", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: inconsistent data copied during copytoshadow of tables with field types DecFloat16 and DecFloat34", "RefUrl": "/notes/3072886 "}, {"RefNumber": "3072930", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans error during upgrade phase copytoshadow for table FPLAYOUTT", "RefUrl": "/notes/3072930 "}, {"RefNumber": "3066880", "RefComponent": "BC-CTS-TLS", "RefTitle": "gCTS: improvement for conflict resolution on objects with namespace", "RefUrl": "/notes/3066880 "}, {"RefNumber": "3064372", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp call without arguments ends with return code 12", "RefUrl": "/notes/3064372 "}, {"RefNumber": "3064357", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans ignores temporary SQL error", "RefUrl": "/notes/3064357 "}, {"RefNumber": "2888887", "RefComponent": "BC-CTS-GIT", "RefTitle": "gCTS: restrictions in supported object types", "RefUrl": "/notes/2888887 "}, {"RefNumber": "3049640", "RefComponent": "BC-CTS-TLS", "RefTitle": "gCTS: improvements for customizing transport", "RefUrl": "/notes/3049640 "}, {"RefNumber": "3049552", "RefComponent": "BC-CTS-TLS", "RefTitle": "MultiTenancy: tenant import of shared T100 entries", "RefUrl": "/notes/3049552 "}, {"RefNumber": "3049456", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans import error: Input buffer ... bytes too short. Probably the data file was destroyed during file transfer!?", "RefUrl": "/notes/3049456 "}, {"RefNumber": "3046811", "RefComponent": "BC-DB-SYB", "RefTitle": "SIQ: connection to IQ with IQ 16.1 SP04 PL09 ODBC driver fails", "RefUrl": "/notes/3046811 "}, {"RefNumber": "3042207", "RefComponent": "BC-CST-MS", "RefTitle": "MS: deactivate default message server port detection", "RefUrl": "/notes/3042207 "}, {"RefNumber": "3040941", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans memory leak during export", "RefUrl": "/notes/3040941 "}, {"RefNumber": "3036418", "RefComponent": "BC-CTS-GIT", "RefTitle": "gCTS: tp VCSCOMMIT fails because no datafile is available", "RefUrl": "/notes/3036418 "}, {"RefNumber": "3035842", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Upgrade stops in phase DIFFEXPABAP_CIGPR with error: 'invalid string handle'", "RefUrl": "/notes/3035842 "}, {"RefNumber": "3034680", "RefComponent": "BC-CTS-TLS", "RefTitle": "Enable lowercase table names in control file", "RefUrl": "/notes/3034680 "}, {"RefNumber": "3034611", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans SQL error during SHADOW IMPORT when trying to access to table REPOSRC~", "RefUrl": "/notes/3034611 "}, {"RefNumber": "3017467", "RefComponent": "BC-CST", "RefTitle": "SAP Support Package Stack Kernel 7.53 Patch Level 800", "RefUrl": "/notes/3017467 "}, {"RefNumber": "3013954", "RefComponent": "BC-CTS-GIT", "RefTitle": "gCTS: Fix for issue with CDAT support", "RefUrl": "/notes/3013954 "}, {"RefNumber": "3001620", "RefComponent": "BC-CTS-GIT", "RefTitle": "gCTS: abap2vcs.jar version for SAP S/4HANA 2020 FPS 01", "RefUrl": "/notes/3001620 "}, {"RefNumber": "3005764", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans error \"strange 'tw_exchange_table_read' call\" in upgrade copy-to-shadow phase", "RefUrl": "/notes/3005764 "}, {"RefNumber": "2992062", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans import fails with error message 'Input buffer ... bytes too short.'", "RefUrl": "/notes/2992062 "}, {"RefNumber": "2821718", "RefComponent": "BC-CTS-GIT", "RefTitle": "Central Note for Git-enabled Change and Transport System (gCTS)", "RefUrl": "/notes/2821718 "}, {"RefNumber": "2985769", "RefComponent": "BC-CTS-TLS", "RefTitle": "Extension of transport object R3TR WDYN by documenation objects with id WC", "RefUrl": "/notes/2985769 "}, {"RefNumber": "2984882", "RefComponent": "BC-CTS-GIT", "RefTitle": "gCTS: Support for Git repositories with subdirectories", "RefUrl": "/notes/2984882 "}, {"RefNumber": "2982494", "RefComponent": "BC-CTS-TLS", "RefTitle": "gCTS: Export from Git repository with parameter abapreference=yes", "RefUrl": "/notes/2982494 "}, {"RefNumber": "2964187", "RefComponent": "BC-CTS-TLS", "RefTitle": "customer transports included in upgrade: returncode handling", "RefUrl": "/notes/2964187 "}, {"RefNumber": "2957173", "RefComponent": "BC-CTS-TLS", "RefTitle": "STMS - tp import status for \"Import All\" not yet started although already finished", "RefUrl": "/notes/2957173 "}, {"RefNumber": "2956596", "RefComponent": "BC-CTS-TLS", "RefTitle": "Export of docu object fails when name contains the character '", "RefUrl": "/notes/2956596 "}, {"RefNumber": "2954020", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: introduce automatic memory limit for string cache", "RefUrl": "/notes/2954020 "}, {"RefNumber": "2942185", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp trace does not activate the RFC trace", "RefUrl": "/notes/2942185 "}, {"RefNumber": "2083594", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel Versions and SAP Kernel Patch Levels", "RefUrl": "/notes/2083594 "}, {"RefNumber": "2946197", "RefComponent": "BC-CTS-TLS", "RefTitle": "Export of TEXT object fails with RC=0008", "RefUrl": "/notes/2946197 "}, {"RefNumber": "1969700", "RefComponent": "HAN-DB", "RefTitle": "SQL Statement Collection for SAP HANA", "RefUrl": "/notes/1969700 "}, {"RefNumber": "2932993", "RefComponent": "BC-CTS-TLS", "RefTitle": "nZDM upgrade stops in phase PREP_SPACECALC / TR_GET_SPCREQ_ADD (SAPK-201AHINDMIS)", "RefUrl": "/notes/2932993 "}, {"RefNumber": "2926440", "RefComponent": "BC-UPG-OCS-SPA", "RefTitle": "ABAP syntax errors after applying SPs with SPAM/SAINT option 'downtime minimized import'", "RefUrl": "/notes/2926440 "}, {"RefNumber": "2924279", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "SAP upgrade stops in phase  SHADOW_IMPORT_INC_CUST with transport tools RC=0016", "RefUrl": "/notes/2924279 "}, {"RefNumber": "2922236", "RefComponent": "BC-CTS-GIT", "RefTitle": "Error message 'Could not 'unescape' the parameter 'XY': Bad character in unicode escape' for call of Java application 'abap2vcs'", "RefUrl": "/notes/2922236 "}, {"RefNumber": "2920899", "RefComponent": "BC-CTS-TLS", "RefTitle": "Error during release of transport request with R3TR FILE object", "RefUrl": "/notes/2920899 "}, {"RefNumber": "2907555", "RefComponent": "BC-CTS-TLS", "RefTitle": "VDAT and CDAT transports updates fields that are not part of a view", "RefUrl": "/notes/2907555 "}, {"RefNumber": "2877926", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp import performance for extremely large number of requests (greater than 500)", "RefUrl": "/notes/2877926 "}, {"RefNumber": "2894760", "RefComponent": "BC-CTS-TLS", "RefTitle": "ADT / AIE: objects are invisible after transport", "RefUrl": "/notes/2894760 "}, {"RefNumber": "2893596", "RefComponent": "BC-CTS-TLS", "RefTitle": "Missing methods in DCEX object after dictionary import", "RefUrl": "/notes/2893596 "}, {"RefNumber": "2886926", "RefComponent": "BC-CTS-TLS", "RefTitle": "SE09: Successfully exported transports are displayed as 'export still running'", "RefUrl": "/notes/2886926 "}, {"RefNumber": "2886050", "RefComponent": "BC-CTS-GIT", "RefTitle": "gCTS: Error message 'SAP system not installed properly'", "RefUrl": "/notes/2886050 "}, {"RefNumber": "2877341", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans 'duplicate key error' in shadow import or copy to shadow if twrtab limit is reached", "RefUrl": "/notes/2877341 "}, {"RefNumber": "2869690", "RefComponent": "BC-CTS-TLS", "RefTitle": "deinstallation of a component fails due to object of type SQLT", "RefUrl": "/notes/2869690 "}, {"RefNumber": "2859491", "RefComponent": "BC-CTS-TLS", "RefTitle": "Generation of ABAP programs during transports", "RefUrl": "/notes/2859491 "}, {"RefNumber": "2856419", "RefComponent": "BC-CTS-TLS", "RefTitle": "dbcrtab error in phase DIFFEXPABAP_CIGPR", "RefUrl": "/notes/2856419 "}, {"RefNumber": "2856416", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans error accessing table TRBAT3 during upgrade", "RefUrl": "/notes/2856416 "}, {"RefNumber": "2850946", "RefComponent": "BC-CTS-TLS", "RefTitle": "Error in R3trans export option LSM=ALL", "RefUrl": "/notes/2850946 "}, {"RefNumber": "2843838", "RefComponent": "BC-CTS-TLS", "RefTitle": "Post Copy Automation: \"Duplicate Key\" error during import", "RefUrl": "/notes/2843838 "}, {"RefNumber": "2818897", "RefComponent": "BC-CTS-TLS", "RefTitle": "Signal in R3trans during upgrade phase copy-to-shadow for table RLFW_RO_MODE_LOG", "RefUrl": "/notes/2818897 "}, {"RefNumber": "2818730", "RefComponent": "BC-CTS-TLS", "RefTitle": "Problem with parallel processing of R3trans on UNIX", "RefUrl": "/notes/2818730 "}, {"RefNumber": "2816843", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans parallel processing not working correctly on Windows", "RefUrl": "/notes/2816843 "}, {"RefNumber": "2793192", "RefComponent": "BC-CTS-TLS", "RefTitle": "Avoid deadlocks on table REPOSRC during parallel import", "RefUrl": "/notes/2793192 "}, {"RefNumber": "2791124", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS Translation: localization of SAP GUI for HTML own text", "RefUrl": "/notes/2791124 "}, {"RefNumber": "2788458", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS TextEdit: context menu does not work on popup", "RefUrl": "/notes/2788458 "}, {"RefNumber": "2786696", "RefComponent": "BC-CTS-TLS", "RefTitle": "Performance enhancement for TABIM_UPG phase for non ABAP-Multi-Tenancy systems for 7.73 kernel", "RefUrl": "/notes/2786696 "}, {"RefNumber": "2773545", "RefComponent": "BC-CTS-TLS", "RefTitle": "Performance enhancement for TABIM_UPG phase for multi tenancy systems", "RefUrl": "/notes/2773545 "}, {"RefNumber": "2738010", "RefComponent": "BC-FES-ITS", "RefTitle": "Export to clipboard not working in Firefox 63", "RefUrl": "/notes/2738010 "}, {"RefNumber": "2772908", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Customer transports involved in an upgrade are not visible in the import history", "RefUrl": "/notes/2772908 "}, {"RefNumber": "2769502", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS File Save Dialog on Safari: save to native file system feature not working.", "RefUrl": "/notes/2769502 "}, {"RefNumber": "2762591", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp: parameter 'handling_of_inactive_transports = SKIP' does NOT skip inactive entries in shadow buffer", "RefUrl": "/notes/2762591 "}, {"RefNumber": "2751869", "RefComponent": "BC-FES-ITS", "RefTitle": "Framework texts are not displayed in chinese but in english", "RefUrl": "/notes/2751869 "}, {"RefNumber": "2747474", "RefComponent": "BC-CTS-TLS", "RefTitle": "Multi Tenancy import into shared: 1AETW000 ===> HALT: missing shared table for REPOTEXT Please contact the SAP support", "RefUrl": "/notes/2747474 "}, {"RefNumber": "2747074", "RefComponent": "BC-FES-ITS", "RefTitle": "FSM: excel files couldn't be opened with the WebGUI File Browser", "RefUrl": "/notes/2747074 "}, {"RefNumber": "2747063", "RefComponent": "BC-FES-ITS", "RefTitle": "FSM: unnecessary error popup when accessing native file path", "RefUrl": "/notes/2747063 "}, {"RefNumber": "2743483", "RefComponent": "BC-SRV-ASF-AT", "RefTitle": "Update of long text logs is interrupted by application of certain kernel patches", "RefUrl": "/notes/2743483 "}, {"RefNumber": "2742625", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp error during MTSHCONTAINER update", "RefUrl": "/notes/2742625 "}, {"RefNumber": "2737391", "RefComponent": "BC-FES-ITS", "RefTitle": "FSM: rename of files in WebGUI File Browser", "RefUrl": "/notes/2737391 "}, {"RefNumber": "2732720", "RefComponent": "BC-FES-ITS", "RefTitle": "FSM: save to native file system for multipart download", "RefUrl": "/notes/2732720 "}, {"RefNumber": "2731661", "RefComponent": "BC-FES-ITS", "RefTitle": "FSM: virtual filesystem case sensitiv", "RefUrl": "/notes/2731661 "}, {"RefNumber": "2731652", "RefComponent": "BC-CTS-TLS", "RefTitle": "missing 'clientcascade' during shadow import of upgrade on multi tenancy system", "RefUrl": "/notes/2731652 "}, {"RefNumber": "2728063", "RefComponent": "BC-CTS-TLS", "RefTitle": "Error when executing tp command \"extrdocu\"", "RefUrl": "/notes/2728063 "}, {"RefNumber": "2103871", "RefComponent": "BW-SYS-DB-SYB", "RefTitle": "SYB: Migrating SAP BW to SAP  Adaptive Server Enterprise", "RefUrl": "/notes/2103871 "}, {"RefNumber": "2726321", "RefComponent": "BC-CTS-TLS", "RefTitle": "missing function include after import with R3trans (parallel mode) on DB6 database", "RefUrl": "/notes/2726321 "}, {"RefNumber": "2724221", "RefComponent": "BC-FES-ITS", "RefTitle": "SAP HTML Control: sapevents with parameters in Personas 2", "RefUrl": "/notes/2724221 "}, {"RefNumber": "2722178", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS HTML Control: display of files with content type application/xml", "RefUrl": "/notes/2722178 "}, {"RefNumber": "2716679", "RefComponent": "BC-CTS-TLS", "RefTitle": "after import method /CFG/AFTER_IMPORT_MANAGER is not executed in postprocessing step", "RefUrl": "/notes/2716679 "}, {"RefNumber": "2713386", "RefComponent": "BC-CTS-TLS", "RefTitle": "Export of deletion transport ends with returncode 0006", "RefUrl": "/notes/2713386 "}, {"RefNumber": "2709399", "RefComponent": "BC-FES-ITS", "RefTitle": "FSM: files with extension p7m couldn' be displayed", "RefUrl": "/notes/2709399 "}, {"RefNumber": "2708434", "RefComponent": "BC-FES-ITS", "RefTitle": "FSM: error popup not shown in some situations", "RefUrl": "/notes/2708434 "}, {"RefNumber": "2682816", "RefComponent": "BC-CTS-TLS", "RefTitle": "Skipped Correction \"tp error \"ERROR:<transdir>/buffer/<SID>OLD : cant close\"", "RefUrl": "/notes/2682816 "}, {"RefNumber": "2700289", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS Toolbar: group button does not render state when checked", "RefUrl": "/notes/2700289 "}, {"RefNumber": "2700239", "RefComponent": "BC-FES-ITS", "RefTitle": "DATA Aging: disabled indexeddb api", "RefUrl": "/notes/2700239 "}, {"RefNumber": "2699571", "RefComponent": "BC-FES-ITS", "RefTitle": "FSM: direct file upload", "RefUrl": "/notes/2699571 "}, {"RefNumber": "2693603", "RefComponent": "BC-CTS-TLS", "RefTitle": "SAP language delivery transport deletes OTR texts of customer", "RefUrl": "/notes/2693603 "}, {"RefNumber": "2690502", "RefComponent": "BC-FES-ITS", "RefTitle": "FESR: dialog step time period too long", "RefUrl": "/notes/2690502 "}, {"RefNumber": "2686650", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: GUI status load deletion missing during import", "RefUrl": "/notes/2686650 "}, {"RefNumber": "2677526", "RefComponent": "BC-FES-ITS", "RefTitle": "FESR: SAP GUI for HTML and FLP", "RefUrl": "/notes/2677526 "}, {"RefNumber": "2673002", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS HTML Viewer: / in sapevent action part", "RefUrl": "/notes/2673002 "}, {"RefNumber": "2672894", "RefComponent": "BC-CTS-TLS", "RefTitle": "DOKCLU access from R3trans in Kernel 7.73", "RefUrl": "/notes/2672894 "}, {"RefNumber": "2671372", "RefComponent": "BC-FES-ITS", "RefTitle": "FSM: issue with client popups opened at the same time", "RefUrl": "/notes/2671372 "}, {"RefNumber": "2663188", "RefComponent": "BC-DB-HDB-SYS", "RefTitle": "Enhancement of DBSL trace for SAP MaxDB and SAP HANA for troubleshooting", "RefUrl": "/notes/2663188 "}, {"RefNumber": "2660665", "RefComponent": "BC-FES-ITS", "RefTitle": "Data aging", "RefUrl": "/notes/2660665 "}, {"RefNumber": "2659395", "RefComponent": "BC-CTS-TLS", "RefTitle": "bad performance during language import in systems with many clients", "RefUrl": "/notes/2659395 "}, {"RefNumber": "2658081", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS TextEdit: js Error Uncaught TypeError when uploading files", "RefUrl": "/notes/2658081 "}, {"RefNumber": "2656608", "RefComponent": "BC-CTS-TLS", "RefTitle": "Error: R3<PERSON>ns time runs backward", "RefUrl": "/notes/2656608 "}, {"RefNumber": "2643542", "RefComponent": "BC-FES-ITS", "RefTitle": "HTML Viewer: method ENABLE_REMOTE_SECURITY_SESSION", "RefUrl": "/notes/2643542 "}, {"RefNumber": "2642463", "RefComponent": "BC-DB-MSS", "RefTitle": "Open SQL Statements with single space literals are translated using an N prefix on non-unicode systems", "RefUrl": "/notes/2642463 "}, {"RefNumber": "2641891", "RefComponent": "BC-FES-ITS", "RefTitle": "webgui_min.js doesn't contain frontend services javascript files", "RefUrl": "/notes/2641891 "}, {"RefNumber": "2638149", "RefComponent": "BC-CTS-TLS", "RefTitle": "Import breaks with SQL error \"string data right truncation\"", "RefUrl": "/notes/2638149 "}, {"RefNumber": "2636552", "RefComponent": "BC-FES-ITS", "RefTitle": "FESR: client display of FESR data and ITS kernel times", "RefUrl": "/notes/2636552 "}, {"RefNumber": "2634367", "RefComponent": "BC-FES-ITS", "RefTitle": "WebGUI File Browser: multiple export of files in zip format", "RefUrl": "/notes/2634367 "}, {"RefNumber": "2632699", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans fails during access to table CTS_HOT_OBJECT", "RefUrl": "/notes/2632699 "}, {"RefNumber": "2629721", "RefComponent": "BC-CTS-TLS", "RefTitle": "ZDM upgrade stops in phase MAIN_SHDIMP/SUBMOD_SHD2_RUN/GENKEYTRACE", "RefUrl": "/notes/2629721 "}, {"RefNumber": "2201060", "RefComponent": "BC-DB-MSS", "RefTitle": "Setting up Microsoft SQL Server 2016", "RefUrl": "/notes/2201060 "}, {"RefNumber": "2619019", "RefComponent": "BC-CTS-TLS", "RefTitle": "Message class is not visible in ABAP Workbench after transport", "RefUrl": "/notes/2619019 "}, {"RefNumber": "2618645", "RefComponent": "BC-CTS-TLS", "RefTitle": "Too many entries from SEO* tables can be deleted during parallel import of class by R3trans", "RefUrl": "/notes/2618645 "}, {"RefNumber": "2618753", "RefComponent": "BC-FES-ITS", "RefTitle": "File download contains additional script tag", "RefUrl": "/notes/2618753 "}, {"RefNumber": "2615119", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans import with statistical information", "RefUrl": "/notes/2615119 "}, {"RefNumber": "2610285", "RefComponent": "BC-FES-ITS", "RefTitle": "FESR not activated by default and some data are incorrect", "RefUrl": "/notes/2610285 "}, {"RefNumber": "2608300", "RefComponent": "BC-FES-ITS", "RefTitle": "UR Performance trace activation", "RefUrl": "/notes/2608300 "}, {"RefNumber": "2608009", "RefComponent": "BC-FES-ITS", "RefTitle": "Performance: load UR javascript files with classes_its.js and on demand", "RefUrl": "/notes/2608009 "}, {"RefNumber": "2608160", "RefComponent": "BC-CTS-TLS", "RefTitle": "Statistics Information for the Import", "RefUrl": "/notes/2608160 "}, {"RefNumber": "2607320", "RefComponent": "BC-CTS-TLS", "RefTitle": "no access to 'exttransdirpath' during export", "RefUrl": "/notes/2607320 "}, {"RefNumber": "2582812", "RefComponent": "BC-FES-ITS", "RefTitle": "FESR: client type field value is XAP_ITS or empty.", "RefUrl": "/notes/2582812 "}, {"RefNumber": "2533233", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux on Alibaba Cloud (IaaS): Adaption of Your SAP License", "RefUrl": "/notes/2533233 "}, {"RefNumber": "2579002", "RefComponent": "BC-FES-ITS", "RefTitle": "FSM: clipboard upload not working", "RefUrl": "/notes/2579002 "}, {"RefNumber": "2558739", "RefComponent": "BC-CTS-TLS", "RefTitle": "Import aborts due to memory shortage when import table entries with large strings", "RefUrl": "/notes/2558739 "}, {"RefNumber": "2556153", "RefComponent": "BC-CST", "RefTitle": "Using Kernel 7.53 instead of Kernel 7.40, 7.41, 7.42, 7.45, or 7.49", "RefUrl": "/notes/2556153 "}, {"RefNumber": "2554077", "RefComponent": "BC-CTS-TLS", "RefTitle": "SUM fails due to R3trans error in \"getspacerequirements\" (upgrade phase PREP_SPACECALC/TR_GET_SPCREQ_DISC)", "RefUrl": "/notes/2554077 "}, {"RefNumber": "2552448", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS HTML Viewer: &,=,? characters in input fields not correctly parsed", "RefUrl": "/notes/2552448 "}, {"RefNumber": "2552460", "RefComponent": "BC-CTS-TLS", "RefTitle": "SUM fails due to R3trans error when accessing table /CFG/RT_FW_CNF_C", "RefUrl": "/notes/2552460 "}, {"RefNumber": "2547468", "RefComponent": "BC-FES-ITS", "RefTitle": "WebGUI File Browser: option in file save dialog to save to native file system.", "RefUrl": "/notes/2547468 "}, {"RefNumber": "2533756", "RefComponent": "BC-OP-NT", "RefTitle": "Windows on Alibaba Cloud (IaaS): Adaption of your SAP License", "RefUrl": "/notes/2533756 "}, {"RefNumber": "2533751", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans SIMULATED system: R3TR CLAS is not moved correctly from TENANT to SHARED", "RefUrl": "/notes/2533751 "}, {"RefNumber": "2532907", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS HTML Control: name attribut added to iframe element", "RefUrl": "/notes/2532907 "}, {"RefNumber": "2528051", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp support for XCLA objects in DD import", "RefUrl": "/notes/2528051 "}, {"RefNumber": "2501439", "RefComponent": "BC-FES-ITS", "RefTitle": "FSM: files not selectable in file open dialog", "RefUrl": "/notes/2501439 "}, {"RefNumber": "2499488", "RefComponent": "BC-FES-ITS", "RefTitle": "FSM: Safari: export to filename \"unknown\"", "RefUrl": "/notes/2499488 "}, {"RefNumber": "2457896", "RefComponent": "BC-DB-MSS", "RefTitle": "Open SQL Statements with literals are translated using an N prefix on non-unicode systems", "RefUrl": "/notes/2457896 "}, {"RefNumber": "2476418", "RefComponent": "BC-FES-ITS", "RefTitle": "WebGUI File Browser: CTRL-A, close button, messagebar", "RefUrl": "/notes/2476418 "}, {"RefNumber": "990045", "RefComponent": "BC-DB-SDB-INS", "RefTitle": "Installation of SAP MaxDB 7.6 for Release 46D SAP products", "RefUrl": "/notes/990045 "}, {"RefNumber": "2449341", "RefComponent": "BC-CTS-TLS", "RefTitle": "Missing synchronization after deletion of function groups by deletion transport/calendar buffer", "RefUrl": "/notes/2449341 "}, {"RefNumber": "2442103", "RefComponent": "BC-FES-ITS", "RefTitle": "WebGUI File Browser: file listview issues, key default action on popups", "RefUrl": "/notes/2442103 "}, {"RefNumber": "2436369", "RefComponent": "BC-CTS-TLS", "RefTitle": "Upgrade stops in phase TABIM_UPG due to strange SQL error", "RefUrl": "/notes/2436369 "}, {"RefNumber": "2362340", "RefComponent": "BC-DB-SDB", "RefTitle": "DESCRIBE does not return any metadata", "RefUrl": "/notes/2362340 "}, {"RefNumber": "2419988", "RefComponent": "BC-FES-ITS", "RefTitle": "WebGUI File Browser: Safari version identification", "RefUrl": "/notes/2419988 "}, {"RefNumber": "2413399", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "R3trans GETKEYTRACE", "RefUrl": "/notes/2413399 "}, {"RefNumber": "2409342", "RefComponent": "BC-FES-ITS", "RefTitle": "HTML Viewer: backend error control not found", "RefUrl": "/notes/2409342 "}, {"RefNumber": "2407126", "RefComponent": "BC-FES-ITS", "RefTitle": "WebGUI File Browser: sorting in file listview", "RefUrl": "/notes/2407126 "}, {"RefNumber": "2382696", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "SUM shadow import: Correct handling of objects that only partially have shadow tables", "RefUrl": "/notes/2382696 "}, {"RefNumber": "2381734", "RefComponent": "BC-DB-HDB-SYS", "RefTitle": "Re-prepare for SQL error -10404", "RefUrl": "/notes/2381734 "}, {"RefNumber": "2379949", "RefComponent": "BC-CTS-TLS", "RefTitle": "sapnames logging", "RefUrl": "/notes/2379949 "}, {"RefNumber": "2377867", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans does not export all TADIR entries of the objects contained in a transport request", "RefUrl": "/notes/2377867 "}, {"RefNumber": "2377088", "RefComponent": "BC-FES-ITS", "RefTitle": "WebGUI File Browser: js error on double click in listview header", "RefUrl": "/notes/2377088 "}, {"RefNumber": "2376306", "RefComponent": "BC-FES-ITS", "RefTitle": "WebGUI File Browser: file paths with language specific characters incorrect", "RefUrl": "/notes/2376306 "}, {"RefNumber": "2373301", "RefComponent": "BC-FES-ITS", "RefTitle": "WebGUI File Browser: Chrome: popup freeze", "RefUrl": "/notes/2373301 "}, {"RefNumber": "2368382", "RefComponent": "BC-CTS-TLS", "RefTitle": "Automatic cleanup of suspended transport requests from the import queue", "RefUrl": "/notes/2368382 "}, {"RefNumber": "2364819", "RefComponent": "BC-DB-HDB-SYS", "RefTitle": "SAP HANA DB connections should be shut down immediately", "RefUrl": "/notes/2364819 "}, {"RefNumber": "2364045", "RefComponent": "BC-FES-ITS", "RefTitle": "WebGUI File Browser: Chrome: calculation of free space of browser's file system", "RefUrl": "/notes/2364045 "}, {"RefNumber": "2363551", "RefComponent": "BC-FES-ITS", "RefTitle": "first webgui dynpro response is without no-cache", "RefUrl": "/notes/2363551 "}, {"RefNumber": "2355704", "RefComponent": "BC-FES-ITS", "RefTitle": "WebGUI File Browser: help text updated.", "RefUrl": "/notes/2355704 "}, {"RefNumber": "2355446", "RefComponent": "BC-FES-ITS", "RefTitle": "WebGUI File Browser: HTML Local Storage disabled", "RefUrl": "/notes/2355446 "}, {"RefNumber": "2355211", "RefComponent": "BC-FES-ITS", "RefTitle": "Frontend Services: locking of frontend services corrected", "RefUrl": "/notes/2355211 "}, {"RefNumber": "2353418", "RefComponent": "BC-FES-ITS", "RefTitle": "Frontend Services: Java applet-free implementation is now default for kernel 722", "RefUrl": "/notes/2353418 "}, {"RefNumber": "2350940", "RefComponent": "BC-FES-ITS", "RefTitle": "WebGUI File Browser: locking of long running import task", "RefUrl": "/notes/2350940 "}, {"RefNumber": "2349175", "RefComponent": "BC-FES-ITS", "RefTitle": "WebGUI File Browser:  change of some display texts", "RefUrl": "/notes/2349175 "}, {"RefNumber": "2349169", "RefComponent": "BC-FES-ITS", "RefTitle": "WebGUI File Browser:  filter selection in file dialogs raises js error", "RefUrl": "/notes/2349169 "}, {"RefNumber": "2346338", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp: Duplicate parameter VCSIMPORT", "RefUrl": "/notes/2346338 "}, {"RefNumber": "2343441", "RefComponent": "BC-CTS-TLS", "RefTitle": "Parallel import of deletion transport request terminates on SAP HANA database", "RefUrl": "/notes/2343441 "}, {"RefNumber": "2342870", "RefComponent": "BC-FES-ITS", "RefTitle": "WebGUI File Browser: js error during file download with append flag.", "RefUrl": "/notes/2342870 "}, {"RefNumber": "2338162", "RefComponent": "BC-FES-ITS", "RefTitle": "WebGUI File Browser: clipboard import error in firefox", "RefUrl": "/notes/2338162 "}, {"RefNumber": "2337071", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Specified table entry for R3TR TABU not included in export", "RefUrl": "/notes/2337071 "}, {"RefNumber": "2336289", "RefComponent": "BC-FES-ITS", "RefTitle": "WebGUI File Browser: incorrect filename/foldername in file/folder dialogs", "RefUrl": "/notes/2336289 "}, {"RefNumber": "2335138", "RefComponent": "BC-FES-ITS", "RefTitle": "WebGUI File Browser: directory not found in file open dialog", "RefUrl": "/notes/2335138 "}, {"RefNumber": "2334023", "RefComponent": "BC-FES-ITS", "RefTitle": "WebGUI File Browser: no error popup for privacy mode", "RefUrl": "/notes/2334023 "}, {"RefNumber": "2332442", "RefComponent": "BC-FES-ITS", "RefTitle": "WebGUI File Browser: memory usage: file blobs in listview", "RefUrl": "/notes/2332442 "}, {"RefNumber": "2332272", "RefComponent": "BC-FES-ITS", "RefTitle": "WebGUI File Browser: tracing improved", "RefUrl": "/notes/2332272 "}, {"RefNumber": "2331866", "RefComponent": "BC-FES-ITS", "RefTitle": "WebGUI File Browser: wrong close button behavior in popups.", "RefUrl": "/notes/2331866 "}, {"RefNumber": "2330866", "RefComponent": "BC-ABA-LA", "RefTitle": "Termination during initialization of a work process", "RefUrl": "/notes/2330866 "}, {"RefNumber": "2327159", "RefComponent": "BC-SEC-LIK", "RefTitle": "SAP NetWeaver License Behavior in Virtual and Cloud Environments", "RefUrl": "/notes/2327159 "}, {"RefNumber": "2323253", "RefComponent": "BC-DB-HDB-SYS", "RefTitle": "UPDATE-INSERT Emulation statt UPSERT verwenden", "RefUrl": "/notes/2323253 "}, {"RefNumber": "2321767", "RefComponent": "BC-FES-ITS", "RefTitle": "WebGUI File Browser: deleting several files at once", "RefUrl": "/notes/2321767 "}, {"RefNumber": "2321115", "RefComponent": "BC-FES-ITS", "RefTitle": "WebGUI File Browser: improvement of document display", "RefUrl": "/notes/2321115 "}, {"RefNumber": "2312253", "RefComponent": "BC-FES-ITS", "RefTitle": "WebGUI File Browser: Applet free implementation of the frontend services for 745 and higher and minor changes", "RefUrl": "/notes/2312253 "}, {"RefNumber": "2307859", "RefComponent": "BC-DB-HDB-SYS", "RefTitle": "SAP HANA DB connections are not stopped for an SAP GUI for HTML application", "RefUrl": "/notes/2307859 "}, {"RefNumber": "2305615", "RefComponent": "BC-FES-ITS", "RefTitle": "WebGUI File Browser: extensions (MIME types) supported for opening files", "RefUrl": "/notes/2305615 "}, {"RefNumber": "2304861", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp IMPORTPREVIEW does not come to an end", "RefUrl": "/notes/2304861 "}, {"RefNumber": "2301100", "RefComponent": "BC-FES-ITS", "RefTitle": "Html Viewer: quick clicking results in backend error", "RefUrl": "/notes/2301100 "}, {"RefNumber": "2172935", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "Installation - SAP Systems based on SAP NetWeaver : Oracle Database", "RefUrl": "/notes/2172935 "}, {"RefNumber": "2294558", "RefComponent": "BC-FES-ITS", "RefTitle": "WebGUI File Browser: different symptoms", "RefUrl": "/notes/2294558 "}, {"RefNumber": "2288251", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans transports the TRDIRT entries for function groups incorrectly", "RefUrl": "/notes/2288251 "}, {"RefNumber": "2286332", "RefComponent": "BC-ABA-LA", "RefTitle": "Session termination for change of internal table via FILTER", "RefUrl": "/notes/2286332 "}, {"RefNumber": "2283259", "RefComponent": "BC-DB-HDB-SYS", "RefTitle": "Suspending SAP HANA DB connections with ABAP secure store (SSFS)", "RefUrl": "/notes/2283259 "}, {"RefNumber": "2273843", "RefComponent": "BC-CTS-TLS", "RefTitle": "After an import with parallel import processes, ABAP programs are inconsistent", "RefUrl": "/notes/2273843 "}, {"RefNumber": "2266887", "RefComponent": "BC-FES-ITS", "RefTitle": "SAP GUI for HTML: System buttons are not displayed with ~webgui_simple_toolbar = 8", "RefUrl": "/notes/2266887 "}, {"RefNumber": "2265699", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS UpDown: dynamic menu and frontend services", "RefUrl": "/notes/2265699 "}, {"RefNumber": "1984485", "RefComponent": "MFG-ME-IM", "RefTitle": "Bad request error running SAPMEINT CTC Wizard", "RefUrl": "/notes/1984485 "}, {"RefNumber": "2251972", "RefComponent": "BC-CST", "RefTitle": "Using kernel 7.45 instead of kernel 7.40, 7.41, or 7.42", "RefUrl": "/notes/2251972 "}, {"RefNumber": "2251608", "RefComponent": "BC-CTS-TLS", "RefTitle": "Wrong NUMC-CHAR conversion by R3trans", "RefUrl": "/notes/2251608 "}, {"RefNumber": "2250602", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS UpDown: applet certificate validity end date 01/11/2016", "RefUrl": "/notes/2250602 "}, {"RefNumber": "2243725", "RefComponent": "BC-CTS-TLS", "RefTitle": "\"Value of key specification field truncated\"", "RefUrl": "/notes/2243725 "}, {"RefNumber": "2243054", "RefComponent": "BC-CTS-TLS", "RefTitle": "Transport of CDS annotation translations", "RefUrl": "/notes/2243054 "}, {"RefNumber": "2237545", "RefComponent": "BC-CTS-TLS", "RefTitle": "Missing <PERSON><PERSON><PERSON><PERSON> after copytoshadow phase", "RefUrl": "/notes/2237545 "}, {"RefNumber": "2236960", "RefComponent": "BC-CTS-TLS", "RefTitle": "Avoidance of interferences between classic views and DDLS views", "RefUrl": "/notes/2236960 "}, {"RefNumber": "2236884", "RefComponent": "BC-FES-ITS", "RefTitle": "SAP GUI for HTML: No complete processing of Batch Input", "RefUrl": "/notes/2236884 "}, {"RefNumber": "2236762", "RefComponent": "BC-ABA-LA", "RefTitle": "CDS table functions: Permitting reserved names for AMDP methods", "RefUrl": "/notes/2236762 "}, {"RefNumber": "2236537", "RefComponent": "BC-CTS-TLS", "RefTitle": "Errors when activating the CDS view", "RefUrl": "/notes/2236537 "}, {"RefNumber": "2226892", "RefComponent": "BC-CTS-TLS", "RefTitle": "During import of R3TR VDAT R3trans crashes with signal 11", "RefUrl": "/notes/2226892 "}, {"RefNumber": "2230375", "RefComponent": "BC-CTS-TLS", "RefTitle": "After-import methods of a preliminary import are not executed", "RefUrl": "/notes/2230375 "}, {"RefNumber": "2226783", "RefComponent": "BC-CTS-TLS", "RefTitle": "Memory leak in R3trans", "RefUrl": "/notes/2226783 "}, {"RefNumber": "2221016", "RefComponent": "BC-CTS-TLS", "RefTitle": "performace issue for 'tp import' when 'client transport control' (CTC) is used", "RefUrl": "/notes/2221016 "}, {"RefNumber": "2214277", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans may import wrong initial values to newly added fields of tables.", "RefUrl": "/notes/2214277 "}, {"RefNumber": "2201635", "RefComponent": "BC-CTS-TLS", "RefTitle": "Error in export: invalid generic selection", "RefUrl": "/notes/2201635 "}, {"RefNumber": "2198472", "RefComponent": "SV-SMG-CM", "RefTitle": "ChaRM/QGM: import options supportability of collection import in cCTS project", "RefUrl": "/notes/2198472 "}, {"RefNumber": "2190094", "RefComponent": "BC-DB-SDB", "RefTitle": "SSL connection for SAP MaxDB/liveCache DBM connection", "RefUrl": "/notes/2190094 "}, {"RefNumber": "2186266", "RefComponent": "BC-CTS-TLS", "RefTitle": "DCBOs werden beim Export mit der Option 'inactive=yes' fälschlicherweise als Löschungen behandelt", "RefUrl": "/notes/2186266 "}, {"RefNumber": "2185220", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Incorrect E071-LOCKFLAG update for parallel import for SQL-ERROR (rollback)", "RefUrl": "/notes/2185220 "}, {"RefNumber": "2179441", "RefComponent": "XX-PROJ-AAK", "RefTitle": "Installation/upgrade of SAP Add-On Assembly Kit 5.00", "RefUrl": "/notes/2179441 "}, {"RefNumber": "2147622", "RefComponent": "BC-DB-HDB-SYS", "RefTitle": "R3load problem with deferred LOB writing", "RefUrl": "/notes/2147622 "}, {"RefNumber": "2135690", "RefComponent": "BC-DB-HDB-SYS", "RefTitle": "7.22 HANA DBSL Patches", "RefUrl": "/notes/2135690 "}, {"RefNumber": "2135137", "RefComponent": "BC-CTS-TLS", "RefTitle": "Conversion BYTE --> SHORT/INT results in negative values", "RefUrl": "/notes/2135137 "}, {"RefNumber": "2135136", "RefComponent": "BC-CTS-TLS", "RefTitle": "Blank characters in fields of type TIMS and DATS are changed from <blank> to \"000000\"", "RefUrl": "/notes/2135136 "}, {"RefNumber": "2128995", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp terminates import with RC 156", "RefUrl": "/notes/2128995 "}, {"RefNumber": "2128122", "RefComponent": "BC-CST", "RefTitle": "Use of 7.42 kernel instead of 7.40 or 7.41 kernel", "RefUrl": "/notes/2128122 "}, {"RefNumber": "2127050", "RefComponent": "BC-CTS-TLS", "RefTitle": "Parallel transport R3TR VDAT or CDAT: Not all columns updated", "RefUrl": "/notes/2127050 "}, {"RefNumber": "2124000", "RefComponent": "BC-CTS-TLS", "RefTitle": "Entries of client dependent customizing tables are not imported to client 000", "RefUrl": "/notes/2124000 "}, {"RefNumber": "2123059", "RefComponent": "BC-CTS-TLS", "RefTitle": "Imports of ABAP system includes are not immediately effective", "RefUrl": "/notes/2123059 "}, {"RefNumber": "2116545", "RefComponent": "BC-CTS-TMS-CTR", "RefTitle": "central CTS: Recommended versions of transport tools", "RefUrl": "/notes/2116545 "}, {"RefNumber": "2114937", "RefComponent": "BC-CTS-TLS", "RefTitle": "TP terminates during processing of step 9", "RefUrl": "/notes/2114937 "}, {"RefNumber": "2111687", "RefComponent": "BC-CTS-HTA", "RefTitle": "SAP HANA object transport in ABAP: Minimum version of transport tools", "RefUrl": "/notes/2111687 "}, {"RefNumber": "2110509", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp does not start when called via RFC", "RefUrl": "/notes/2110509 "}, {"RefNumber": "2109609", "RefComponent": "BC-CTS-TLS", "RefTitle": "import hangs due to unknown entries in table TRBAT (FUNCTION = C)", "RefUrl": "/notes/2109609 "}, {"RefNumber": "2035875", "RefComponent": "BC-SEC-LIK", "RefTitle": "Windows on Microsoft Azure: Adaption of your SAP License", "RefUrl": "/notes/2035875 "}, {"RefNumber": "2099205", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS Up/Down: Execute on Linux shows popup \"Unable to execute the program\"", "RefUrl": "/notes/2099205 "}, {"RefNumber": "2093997", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Upgrade with start release < 7.0 stops due to missing table RSTLOGOPROP", "RefUrl": "/notes/2093997 "}, {"RefNumber": "2086952", "RefComponent": "BC-CTS-TLS", "RefTitle": "Import preview terminates with RC=0016 (\"unknown step\")", "RefUrl": "/notes/2086952 "}, {"RefNumber": "2060337", "RefComponent": "BC-ABA-LA", "RefTitle": "AMDP: Too short SQL message in exception CX_AMDP_...", "RefUrl": "/notes/2060337 "}, {"RefNumber": "2048110", "RefComponent": "BC-CTS-TLS", "RefTitle": "Imports incomplete/termination with \"Internal asset failed...\"", "RefUrl": "/notes/2048110 "}, {"RefNumber": "2036556", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS Up/down: exception CNTL_ERROR for GET_IP_ADDRESS", "RefUrl": "/notes/2036556 "}, {"RefNumber": "2034921", "RefComponent": "BC-CTS-TLS", "RefTitle": "CTS+: No deployment step if double stack system is configured with \"ctc=1\"", "RefUrl": "/notes/2034921 "}, {"RefNumber": "1496410", "RefComponent": "BC-OP-LNX-RH", "RefTitle": "Red Hat Enterprise Linux 6.x: Installation and Upgrade", "RefUrl": "/notes/1496410 "}, {"RefNumber": "2028886", "RefComponent": "BC-CTS-TLS", "RefTitle": "HALT: \"Internal error: commit during array insert (SAPKB73112)\"", "RefUrl": "/notes/2028886 "}, {"RefNumber": "1994690", "RefComponent": "BC-CST", "RefTitle": "Using the 7.41 kernel instead of 7.40", "RefUrl": "/notes/1994690 "}, {"RefNumber": "1997611", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS UpDown: IllegalStateException: Applet's parent container not set up", "RefUrl": "/notes/1997611 "}, {"RefNumber": "1984787", "RefComponent": "BC-OP-LNX", "RefTitle": "SUSE LINUX Enterprise Server 12: Installation notes", "RefUrl": "/notes/1984787 "}, {"RefNumber": "1979970", "RefComponent": "BC-CTS-TLS", "RefTitle": "Sporadic error message \"transport in buffer too long or unknown format detected\"", "RefUrl": "/notes/1979970 "}, {"RefNumber": "1973389", "RefComponent": "BC-CTS-TLS", "RefTitle": "Transport in mixed AS400/Windows scenarios", "RefUrl": "/notes/1973389 "}, {"RefNumber": "1969656", "RefComponent": "BC-ABA-LA", "RefTitle": "itab statements with component access using named include", "RefUrl": "/notes/1969656 "}, {"RefNumber": "1968601", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS Html Control: no display of mail messages with html tags", "RefUrl": "/notes/1968601 "}, {"RefNumber": "1965871", "RefComponent": "BC-CTS-TLS", "RefTitle": "Automatic handling of BW objects during transport", "RefUrl": "/notes/1965871 "}, {"RefNumber": "1964773", "RefComponent": "BC-ABA-LA", "RefTitle": "Incorrect line comparison after deactivating itab sharing", "RefUrl": "/notes/1964773 "}, {"RefNumber": "1963317", "RefComponent": "BC-CTS-TMS", "RefTitle": "Terminated import cannot be restarted using STMS", "RefUrl": "/notes/1963317 "}, {"RefNumber": "1952701", "RefComponent": "BC-DB-HDB-SYS", "RefTitle": "DBSL supports new SAP HANA SP9 version number", "RefUrl": "/notes/1952701 "}, {"RefNumber": "1952609", "RefComponent": "BC-DB-HDB-SYS", "RefTitle": "R3szchk and tp getdbobjsize optimized for SAP HANA", "RefUrl": "/notes/1952609 "}, {"RefNumber": "1920875", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS Up/Down: different problems in java plugin because of new security restrictions", "RefUrl": "/notes/1920875 "}, {"RefNumber": "1605140", "RefComponent": "CA-LT-SLT", "RefTitle": "SAP Landscape Transformation Replication Server (SLT)", "RefUrl": "/notes/1605140 "}, {"RefNumber": "1731383", "RefComponent": "BC-CTS-TMS", "RefTitle": "Installing/Updating SAP CTS Plug-In 2.0 (for validation)", "RefUrl": "/notes/1731383 "}, {"RefNumber": "1919032", "RefComponent": "BC-DB-HDB-SYS", "RefTitle": "Exception in hdl_dbds_err", "RefUrl": "/notes/1919032 "}, {"RefNumber": "1917691", "RefComponent": "BC-CTS-TLS", "RefTitle": "Deletion of variant during transport contains errors", "RefUrl": "/notes/1917691 "}, {"RefNumber": "1453112", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS agent and kernel patches", "RefUrl": "/notes/1453112 "}, {"RefNumber": "86859", "RefComponent": "BC-INS", "RefTitle": "INST: 4.0A/B R/3 Inst. - Homogeneous System Copy", "RefUrl": "/notes/86859 "}, {"RefNumber": "201149", "RefComponent": "BC-INS", "RefTitle": "INST: 4.6C/4.6C SR1 R/3 Inst. - Homogeneous System Copy", "RefUrl": "/notes/201149 "}, {"RefNumber": "1920550", "RefComponent": "BC-DB-HDB-SYS", "RefTitle": "SQL addition TOP 1 for SELECT SINGLE", "RefUrl": "/notes/1920550 "}, {"RefNumber": "1665940", "RefComponent": "BC-CTS-TMS-CTR", "RefTitle": "Installing/Updating SAP CTS Plug-In 2.0", "RefUrl": "/notes/1665940 "}, {"RefNumber": "12741", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Current versions of BR*Tools", "RefUrl": "/notes/12741 "}, {"RefNumber": "1600066", "RefComponent": "BC-DB-HDB-SYS", "RefTitle": "Available DBSL patches for NewDB", "RefUrl": "/notes/1600066 "}, {"RefNumber": "1484221", "RefComponent": "XX-CSC-PT", "RefTitle": "Software Certification: Portugal", "RefUrl": "/notes/1484221 "}, {"RefNumber": "1310037", "RefComponent": "BC-OP-LNX-SUSE", "RefTitle": "SUSE LINUX Enterprise Server 11: Installation notes", "RefUrl": "/notes/1310037 "}, {"RefNumber": "1886006", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp RC 16 when importing with option \"catchupxpra\"", "RefUrl": "/notes/1886006 "}, {"RefNumber": "1824896", "RefComponent": "BC-SEC-SSF", "RefTitle": "Sporadic error in methods of claass CL_ABAP_X509_CERTIFICATE", "RefUrl": "/notes/1824896 "}, {"RefNumber": "1896796", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS Html Viewer: empty display (sapr3 url with \"-\")", "RefUrl": "/notes/1896796 "}, {"RefNumber": "1894796", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS HTML Control: displaying a network path", "RefUrl": "/notes/1894796 "}, {"RefNumber": "1884522", "RefComponent": "BC-DB-SDB", "RefTitle": "R3szchk : Index name with \"^\" instead of \"~\"", "RefUrl": "/notes/1884522 "}, {"RefNumber": "1896733", "RefComponent": "BC-ABA-LA", "RefTitle": "Mode termination when using debugger symbol KEY_STATUS", "RefUrl": "/notes/1896733 "}, {"RefNumber": "1899663", "RefComponent": "BC-CTS-TLS", "RefTitle": "ABAP loads with errors following import during routine operations", "RefUrl": "/notes/1899663 "}, {"RefNumber": "1048303", "RefComponent": "BC-OP-LNX-RH", "RefTitle": "Red Hat Enterprise Linux 5.x: Installation and upgrade", "RefUrl": "/notes/1048303 "}, {"RefNumber": "1694830", "RefComponent": "BC-CTS-TMS-PLS", "RefTitle": "tp clearold extension for cts+ directories and tmp directory", "RefUrl": "/notes/1694830 "}, {"RefNumber": "1742433", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS Up/down: errors and missing functionality", "RefUrl": "/notes/1742433 "}, {"RefNumber": "1878195", "RefComponent": "BC-CTS-TLS", "RefTitle": "Inclusion of knowledge warehouse files within the datafile", "RefUrl": "/notes/1878195 "}, {"RefNumber": "1460214", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS UpDown: printing in frontend services", "RefUrl": "/notes/1460214 "}, {"RefNumber": "1333251", "RefComponent": "BC-CTS-TLS", "RefTitle": "\"import all\" with umode 1 causes overtaker problem", "RefUrl": "/notes/1333251 "}, {"RefNumber": "49365", "RefComponent": "XX-INT-FA-MAKE", "RefTitle": "iSeries: Applying a patch", "RefUrl": "/notes/49365 "}, {"RefNumber": "80727", "RefComponent": "BC-CTS-TLS", "RefTitle": "Transporting non-LATIN-1 texts", "RefUrl": "/notes/80727 "}, {"RefNumber": "101573", "RefComponent": "BC-SRV-COM-INT", "RefTitle": "SAP Internet Mail Gateway: Versions", "RefUrl": "/notes/101573 "}, {"RefNumber": "1687173", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Collection of notes about the 7.20 kernel", "RefUrl": "/notes/1687173 "}, {"RefNumber": "700784", "RefComponent": "BC-DOC-DTL", "RefTitle": "Repair errors in documentation objects", "RefUrl": "/notes/700784 "}, {"RefNumber": "1818615", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS Up/Down: properties of other controls not saved", "RefUrl": "/notes/1818615 "}, {"RefNumber": "1824319", "RefComponent": "BC-ABA-LA", "RefTitle": "READ TABLE - Unallowed termination with DYN_KEY_DUPLICATE", "RefUrl": "/notes/1824319 "}, {"RefNumber": "525751", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "Installation of the SNC SAPRouter as NT Service", "RefUrl": "/notes/525751 "}, {"RefNumber": "1844292", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS UpDown: filedialog error for libraries folder", "RefUrl": "/notes/1844292 "}, {"RefNumber": "1606260", "RefComponent": "BC-DB-SDB", "RefTitle": "R3szchk does not use the file directory counter", "RefUrl": "/notes/1606260 "}, {"RefNumber": "958253", "RefComponent": "BC-OP-LNX-SUSE", "RefTitle": "SUSE LINUX Enterprise Server 10: Installation notes", "RefUrl": "/notes/958253 "}, {"RefNumber": "1838593", "RefComponent": "BC-ABA-LA", "RefTitle": "Itab Patch Collection 01/2013", "RefUrl": "/notes/1838593 "}, {"RefNumber": "40815", "RefComponent": "BC-I18", "RefTitle": "Single or Double byte installed? (nls_check)", "RefUrl": "/notes/40815 "}, {"RefNumber": "1814627", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp getimportstate: Incorrect result for overtaker transports", "RefUrl": "/notes/1814627 "}, {"RefNumber": "1566004", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans termination in 'db-repo' with reference to 'SYMTAB'", "RefUrl": "/notes/1566004 "}, {"RefNumber": "1566007", "RefComponent": "BC-CTS-TLS", "RefTitle": "RStrans termination in DIFFEXP*: sap_dext 400", "RefUrl": "/notes/1566007 "}, {"RefNumber": "140547", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "TST03 - adjustment of DB row length", "RefUrl": "/notes/140547 "}, {"RefNumber": "1833947", "RefComponent": "BC-CST", "RefTitle": "SAP Stack Kernel 721 (EXT) PL 100", "RefUrl": "/notes/1833947 "}, {"RefNumber": "1622681", "RefComponent": "BC-DB-HDB-SYS", "RefTitle": "DBSL hints for SAP HANA", "RefUrl": "/notes/1622681 "}, {"RefNumber": "1746172", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS Up/Down: ITS's own applet pse", "RefUrl": "/notes/1746172 "}, {"RefNumber": "1810114", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS HtmlViewer: Display of MHT documents is not correct", "RefUrl": "/notes/1810114 "}, {"RefNumber": "313721", "RefComponent": "BC-SRV-KPR-CMS", "RefTitle": "UKSymptom", "RefUrl": "/notes/313721 "}, {"RefNumber": "197746", "RefComponent": "BC-FES-ITS", "RefTitle": "Maint. strategy: Internet Transaction Server (ITS)", "RefUrl": "/notes/197746 "}, {"RefNumber": "1739704", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS HTMLViewer: window open with url rewriting", "RefUrl": "/notes/1739704 "}, {"RefNumber": "1069417", "RefComponent": "BC-CTS-TLS", "RefTitle": "Generation and syntax check of programs after transport", "RefUrl": "/notes/1069417 "}, {"RefNumber": "1135525", "RefComponent": "BC-INS-NT", "RefTitle": "OBSOLETE. NW 7.0 SR3 & SAP Business Suite 2005 SR3 on Win: SAP MaxDB", "RefUrl": "/notes/1135525 "}, {"RefNumber": "1135524", "RefComponent": "BC-INS-UNX", "RefTitle": "OBSOLETE. NW 7.0 SR3 & SAP Business Suite 2005 SR3 on UNIX: SAP MaxDB", "RefUrl": "/notes/1135524 "}, {"RefNumber": "98287", "RefComponent": "BC-CUS-TOL-IMG", "RefTitle": "New IMG is missing after upgrade to Release 4.0A", "RefUrl": "/notes/98287 "}, {"RefNumber": "96296", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-ERROR: Database problem in client copy", "RefUrl": "/notes/96296 "}, {"RefNumber": "208919", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: Performance as of Release 4.6", "RefUrl": "/notes/208919 "}, {"RefNumber": "1695819", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS TextEdit: text disappears after import of text", "RefUrl": "/notes/1695819 "}, {"RefNumber": "1717075", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS Up-/Download: improvements for older java versions", "RefUrl": "/notes/1717075 "}, {"RefNumber": "1677088", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS UpDown: open url in new security applet", "RefUrl": "/notes/1677088 "}, {"RefNumber": "1664157", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS UpDown: Texts in dialogs not translated", "RefUrl": "/notes/1664157 "}, {"RefNumber": "1714234", "RefComponent": "BC-CTS-TLS", "RefTitle": "Missing deletion for import", "RefUrl": "/notes/1714234 "}, {"RefNumber": "854170", "RefComponent": "BC-VMC", "RefTitle": "Switching on the component \"VM Container\"", "RefUrl": "/notes/854170 "}, {"RefNumber": "1728435", "RefComponent": "BC-DB-HDB-SYS", "RefTitle": "SQL error 454 wrong hint syntax", "RefUrl": "/notes/1728435 "}, {"RefNumber": "389530", "RefComponent": "BC-INS", "RefTitle": "INST: R/3 4.6C SR2 - Homogeneous System Copy", "RefUrl": "/notes/389530 "}, {"RefNumber": "1586898", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS TextEdit: Unspecified Script Error", "RefUrl": "/notes/1586898 "}, {"RefNumber": "1746289", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp: Old semaphore causes DB lock for TPALOGHDR", "RefUrl": "/notes/1746289 "}, {"RefNumber": "1730009", "RefComponent": "BC-CTS-TLS", "RefTitle": "Import queue does not work", "RefUrl": "/notes/1730009 "}, {"RefNumber": "173993", "RefComponent": "CA-EUR-CNV", "RefTitle": "Cluster: System settings during Euro LC changeover", "RefUrl": "/notes/173993 "}, {"RefNumber": "601846", "RefComponent": "FS-CD", "RefTitle": "Additional info about installation of SAP Insurance 4.71", "RefUrl": "/notes/601846 "}, {"RefNumber": "793099", "RefComponent": "FS-PM", "RefTitle": "Enhancement to installation FSPM 2.00 WEB/AS R/3 6.20", "RefUrl": "/notes/793099 "}, {"RefNumber": "879817", "RefComponent": "FS-PM", "RefTitle": "Additional info about installing FSPM 3.00 WEB/ACE R/3 6.40", "RefUrl": "/notes/879817 "}, {"RefNumber": "930901", "RefComponent": "FS-PM", "RefTitle": "Enhancements for FSPM 3.10 NW04s installations", "RefUrl": "/notes/930901 "}, {"RefNumber": "785921", "RefComponent": "BC-INS-UNX", "RefTitle": "SAP Web AS 6.40 SR1 Installation on UNIX: Oracle", "RefUrl": "/notes/785921 "}, {"RefNumber": "1597627", "RefComponent": "BC-DB-HDB-SYS", "RefTitle": "SAP HANA connection", "RefUrl": "/notes/1597627 "}, {"RefNumber": "1661740", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS UpDown: moved applet into kernel for urgui", "RefUrl": "/notes/1661740 "}, {"RefNumber": "1447096", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS Up/Down: hardcopy functionality missing", "RefUrl": "/notes/1447096 "}, {"RefNumber": "722273", "RefComponent": "BC-OP-LNX-RH", "RefTitle": "Red Hat Enterprise Linux 3.x, 4.x: Installation and Upgrade", "RefUrl": "/notes/722273 "}, {"RefNumber": "564395", "RefComponent": "BC-DB-DB4", "RefTitle": "DB4, Windows: Applying Application Server Kernel Patches", "RefUrl": "/notes/564395 "}, {"RefNumber": "1715868", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS UpDown: erros in urgui with old java versions", "RefUrl": "/notes/1715868 "}, {"RefNumber": "300828", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: DBA Cockpit and Remote Monitoring for Releases < 6.10", "RefUrl": "/notes/300828 "}, {"RefNumber": "1690510", "RefComponent": "BC-SRV-OTR", "RefTitle": "OTR: Customer-specific texts deleted during upgrade", "RefUrl": "/notes/1690510 "}, {"RefNumber": "1332203", "RefComponent": "BC-DB-SDB", "RefTitle": "MaxDB MVCC support for ABAP", "RefUrl": "/notes/1332203 "}, {"RefNumber": "1692560", "RefComponent": "BC-DB-SDB", "RefTitle": "TH callback is not called when canceling", "RefUrl": "/notes/1692560 "}, {"RefNumber": "357693", "RefComponent": "BC-DWB-SEM", "RefTitle": "Redundancy avoidance in Easy Access", "RefUrl": "/notes/357693 "}, {"RefNumber": "330267", "RefComponent": "BC-CTS", "RefTitle": "Transports between Basis Releases 4.6* and 6.*", "RefUrl": "/notes/330267 "}, {"RefNumber": "378103", "RefComponent": "BC-INS", "RefTitle": "INST: 4.0B SR1 + 40B_COM Kernel - Hom. System Copy", "RefUrl": "/notes/378103 "}, {"RefNumber": "333097", "RefComponent": "BC-INS-MIG", "RefTitle": "INST: 4.6C / 4.6C SR R/3 on UNIX/Oracle - Het. System Copy", "RefUrl": "/notes/333097 "}, {"RefNumber": "316353", "RefComponent": "BC-INS-MIG", "RefTitle": "INST: 4.6D SAP Basis - Heterogeneous System Copy", "RefUrl": "/notes/316353 "}, {"RefNumber": "316355", "RefComponent": "BC-INS", "RefTitle": "INST: 4.6D SAP Basis - Homogeneous System Copy", "RefUrl": "/notes/316355 "}, {"RefNumber": "407123", "RefComponent": "BC-INS", "RefTitle": "INST: SAP Web AS 6.10 - Hom. + Het. System Copy", "RefUrl": "/notes/407123 "}, {"RefNumber": "484699", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DBIF_DSQL2_SQL_ERROR runtime error in DB6_PM_SQLCACHE", "RefUrl": "/notes/484699 "}, {"RefNumber": "1428618", "RefComponent": "BC-FES-ITS", "RefTitle": "Tree control: Node colorings are not displayed", "RefUrl": "/notes/1428618 "}, {"RefNumber": "1558267", "RefComponent": "BC-CTS-TLS", "RefTitle": "Meaning of \"langdeletions\" export option", "RefUrl": "/notes/1558267 "}, {"RefNumber": "1286275", "RefComponent": "BC-ABA-LA", "RefTitle": "Syntax check: Termination when evaluating func. expression", "RefUrl": "/notes/1286275 "}, {"RefNumber": "1678130", "RefComponent": "BC-DB-SDB", "RefTitle": "Crash in DBA Cockpit for SELECT EDITOR", "RefUrl": "/notes/1678130 "}, {"RefNumber": "1454536", "RefComponent": "BC-CTS-TLS", "RefTitle": "Deadlock on table REPOSRC when importing into running system", "RefUrl": "/notes/1454536 "}, {"RefNumber": "1688905", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp error after parallel import", "RefUrl": "/notes/1688905 "}, {"RefNumber": "1584921", "RefComponent": "BC-DB-SDB", "RefTitle": "Update statistic with SAPSYSTEMNAME longer than 3 characters", "RefUrl": "/notes/1584921 "}, {"RefNumber": "1462695", "RefComponent": "BC-CTS-TLS-PLS", "RefTitle": "CTS+ in NetWeaver 7.0 EhP 1 SP9 / EhP 2 SP7", "RefUrl": "/notes/1462695 "}, {"RefNumber": "578324", "RefComponent": "BC-DB-SDB", "RefTitle": "Make and release information for MaxDB/HDB DBSL", "RefUrl": "/notes/578324 "}, {"RefNumber": "1486451", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS HtmlViewer: session token added", "RefUrl": "/notes/1486451 "}, {"RefNumber": "1604046", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS HTML Viewer: bibs css error for firefox", "RefUrl": "/notes/1604046 "}, {"RefNumber": "1621799", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS Toolbar: Missing right border of the rightmost button", "RefUrl": "/notes/1621799 "}, {"RefNumber": "939406", "RefComponent": "BC-OP-LNX", "RefTitle": "System Copy 4.0B - 4.6C to Linux x86_64", "RefUrl": "/notes/939406 "}, {"RefNumber": "1502104", "RefComponent": "SRM-EBP-CA-ACC", "RefTitle": "FAQ: GL Account Determination, FI Validation", "RefUrl": "/notes/1502104 "}, {"RefNumber": "577652", "RefComponent": "BC-CCM-MON", "RefTitle": "Syslog message R19: Initialization of alerts failed", "RefUrl": "/notes/577652 "}, {"RefNumber": "308061", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS monitor architecture: Monitor 3x systems", "RefUrl": "/notes/308061 "}, {"RefNumber": "1469610", "RefComponent": "BC-CTS-TLS", "RefTitle": "Missing ABAP programs after \"Downtime minimized import\"", "RefUrl": "/notes/1469610 "}, {"RefNumber": "732453", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Support Package Stacks for SAP Kernel 4.6D", "RefUrl": "/notes/732453 "}, {"RefNumber": "1595058", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS TextEdit: resize icon inside textarea in FF4", "RefUrl": "/notes/1595058 "}, {"RefNumber": "1626591", "RefComponent": "BC-DB-SDB", "RefTitle": "Closing locators at the end of transaction", "RefUrl": "/notes/1626591 "}, {"RefNumber": "1619504", "RefComponent": "BC-DB-SDB", "RefTitle": "R3szchk: Unknown table name __TABLE_SIZES__", "RefUrl": "/notes/1619504 "}, {"RefNumber": "1553844", "RefComponent": "BC-CTS-TLS", "RefTitle": "No effect of deletion of an enhancement implementation", "RefUrl": "/notes/1553844 "}, {"RefNumber": "1596921", "RefComponent": "BC-CTS-TLS", "RefTitle": "Parallel import: <PERSON><PERSON><PERSON> in 'autorepeat'", "RefUrl": "/notes/1596921 "}, {"RefNumber": "1552367", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS Calendar: datepicker and ime editor", "RefUrl": "/notes/1552367 "}, {"RefNumber": "1558443", "RefComponent": "BW-WHM-AWB", "RefTitle": "Request list horizontal scroll bar is missing", "RefUrl": "/notes/1558443 "}, {"RefNumber": "1457293", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS Up/Down: opening a html page on windows client", "RefUrl": "/notes/1457293 "}, {"RefNumber": "90323", "RefComponent": "BC-MID-RFC", "RefTitle": "Display of RFC logon screen in SM51", "RefUrl": "/notes/90323 "}, {"RefNumber": "822271", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB client software", "RefUrl": "/notes/822271 "}, {"RefNumber": "1592431", "RefComponent": "BC-TRX", "RefTitle": "Speicherschutzverletzung in FbCreateDbslReader", "RefUrl": "/notes/1592431 "}, {"RefNumber": "1580502", "RefComponent": "BC-DB-SDB", "RefTitle": "Log file of update statistic not found", "RefUrl": "/notes/1580502 "}, {"RefNumber": "45548", "RefComponent": "BC-CTS-TLS", "RefTitle": "Transport of language-dependent objects", "RefUrl": "/notes/45548 "}, {"RefNumber": "1589811", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS TextEdit: javascript error in IE in theme 99", "RefUrl": "/notes/1589811 "}, {"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "1567948", "RefComponent": "BC-ABA-LA", "RefTitle": "Core while generating programs with nested DDIC itabs", "RefUrl": "/notes/1567948 "}, {"RefNumber": "1566056", "RefComponent": "BC-ABA-LA", "RefTitle": "READ TABLE .. INTO, with STRUCTU<PERSON> typed TABLES parameter", "RefUrl": "/notes/1566056 "}, {"RefNumber": "1556166", "RefComponent": "BC-ABA-LA", "RefTitle": "Core occurs with unsharing of itab with SORTED secondary key", "RefUrl": "/notes/1556166 "}, {"RefNumber": "1556022", "RefComponent": "BC-ABA-LA", "RefTitle": "Incorrect line sequence after INSERT LINES OF .. USING KEY", "RefUrl": "/notes/1556022 "}, {"RefNumber": "1556015", "RefComponent": "BC-ABA-LA", "RefTitle": "DELETE TABLE .. WITH TABLE KEY .. deletes incorrect row", "RefUrl": "/notes/1556015 "}, {"RefNumber": "683842", "RefComponent": "CRM", "RefTitle": "CRM 3.1 SP Stack 12/2003 (SAPKU31007): Release & Info. Note", "RefUrl": "/notes/683842 "}, {"RefNumber": "1441091", "RefComponent": "BC-FES-ITS", "RefTitle": "JavaScript error during drag and drop in tree control", "RefUrl": "/notes/1441091 "}, {"RefNumber": "329021", "RefComponent": "BC-CST-MM", "RefTitle": "SPOOL_INTERNAL_ERROR, ERROR => EmIAllocMmResourceEg", "RefUrl": "/notes/329021 "}, {"RefNumber": "849483", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections for BR*Tools Version 7.00", "RefUrl": "/notes/849483 "}, {"RefNumber": "1340617", "RefComponent": "BC-DB-SDB", "RefTitle": "Enhancement for MaxDB bridge", "RefUrl": "/notes/1340617 "}, {"RefNumber": "6591", "RefComponent": "FI-AR-AR-C", "RefTitle": "Resetting dunning data after printing", "RefUrl": "/notes/6591 "}, {"RefNumber": "1552785", "RefComponent": "BC-CTS-TLS", "RefTitle": "Termination when importing deletions in wide tables", "RefUrl": "/notes/1552785 "}, {"RefNumber": "1541334", "RefComponent": "BC-CTS-TLS", "RefTitle": "Database connect takes two minutes", "RefUrl": "/notes/1541334 "}, {"RefNumber": "1434959", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans terminates with: Input Buffer ... bytes too short", "RefUrl": "/notes/1434959 "}, {"RefNumber": "1463054", "RefComponent": "BC-CTS-TLS", "RefTitle": "MSSQL: R3trans import fails with SQL error 0", "RefUrl": "/notes/1463054 "}, {"RefNumber": "634869", "RefComponent": "CRM", "RefTitle": "SAPKU40001: Support Package 01 for CRM 4.0", "RefUrl": "/notes/634869 "}, {"RefNumber": "651069", "RefComponent": "CRM", "RefTitle": "SAPKU40002: Support Package 02 for CRM 4.0", "RefUrl": "/notes/651069 "}, {"RefNumber": "677456", "RefComponent": "CRM", "RefTitle": "SAPKU40003: Support Package 03 for CRM 4.0", "RefUrl": "/notes/677456 "}, {"RefNumber": "696262", "RefComponent": "CRM", "RefTitle": "CRM 4.0 SP Stack 1/2004 (SAPKU40004): Release & Info. Note", "RefUrl": "/notes/696262 "}, {"RefNumber": "743291", "RefComponent": "CRM-BF", "RefTitle": "CRM 4.0 SP Stack 6/2004 (SAPKU40006): Release & Info. Note", "RefUrl": "/notes/743291 "}, {"RefNumber": "709921", "RefComponent": "CRM-BF", "RefTitle": "CRM 4.0 SP Stack 4/2004 (SAPKU40005): Release & Info. Note", "RefUrl": "/notes/709921 "}, {"RefNumber": "754723", "RefComponent": "CRM-BF", "RefTitle": "CRM 4.0 SP Stack 9/2004 Support Release 1: Release Note", "RefUrl": "/notes/754723 "}, {"RefNumber": "844193", "RefComponent": "CRM-BF", "RefTitle": "CRM 4.0 SP Stack 09 (07/2005) Release & Info. Note", "RefUrl": "/notes/844193 "}, {"RefNumber": "610994", "RefComponent": "CRM", "RefTitle": "SAPKU31004: Support Package 04 for CRM 3.1", "RefUrl": "/notes/610994 "}, {"RefNumber": "653691", "RefComponent": "CRM", "RefTitle": "SAPKU31006: Support Package 06 for CRM 3.1", "RefUrl": "/notes/653691 "}, {"RefNumber": "663180", "RefComponent": "CRM", "RefTitle": "Support Release 01 for CRM 3.1", "RefUrl": "/notes/663180 "}, {"RefNumber": "1502444", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS Toolbar: scrollable toolbar on MacOs", "RefUrl": "/notes/1502444 "}, {"RefNumber": "1455908", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS: <PERSON><PERSON><PERSON> error with hidden tree controls on tabstrips", "RefUrl": "/notes/1455908 "}, {"RefNumber": "1491290", "RefComponent": "BC-CTS-TLS", "RefTitle": "Missing synchronization after importing include deletions", "RefUrl": "/notes/1491290 "}, {"RefNumber": "207404", "RefComponent": "XX-CSC-ABPL", "RefTitle": "CSC-ABPL: Asian Best Practice Library 5.0.0", "RefUrl": "/notes/207404 "}, {"RefNumber": "1354591", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS: RFC table processing is improved for offset gaps", "RefUrl": "/notes/1354591 "}, {"RefNumber": "1486695", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS: Status line is wrapped in Firefox", "RefUrl": "/notes/1486695 "}, {"RefNumber": "1449610", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp terminates in function 'tp_search_buffer_entry'", "RefUrl": "/notes/1449610 "}, {"RefNumber": "136649", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Selective language import", "RefUrl": "/notes/136649 "}, {"RefNumber": "1486831", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS: Key navigation improvements in tree control", "RefUrl": "/notes/1486831 "}, {"RefNumber": "1499650", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS Up/down: no confirmation popup during download", "RefUrl": "/notes/1499650 "}, {"RefNumber": "1140770", "RefComponent": "SRM-EBP-CA-ACC", "RefTitle": "FAQ: F4-Help Cost Assignment", "RefUrl": "/notes/1140770 "}, {"RefNumber": "1485418", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS: Tree icons are framed in blue", "RefUrl": "/notes/1485418 "}, {"RefNumber": "1500391", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS: Incorrect button design in tree control", "RefUrl": "/notes/1500391 "}, {"RefNumber": "1308455", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS: Certain items are missing in tree control", "RefUrl": "/notes/1308455 "}, {"RefNumber": "1074030", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: \"Duplicate key\" composite SAP Note (as of Release 6.10)", "RefUrl": "/notes/1074030 "}, {"RefNumber": "1417583", "RefComponent": "BC-FES-ITS", "RefTitle": "Tree control does not use available container height", "RefUrl": "/notes/1417583 "}, {"RefNumber": "1376935", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS: Column selection does not work reliably", "RefUrl": "/notes/1376935 "}, {"RefNumber": "1457637", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS: Hidden nodes are displayed in tree control", "RefUrl": "/notes/1457637 "}, {"RefNumber": "1410451", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS: Tree control event onselectionchange is ignored", "RefUrl": "/notes/1410451 "}, {"RefNumber": "138704", "RefComponent": "XX-SER-REL", "RefTitle": "Provision of patches for kernel releases", "RefUrl": "/notes/138704 "}, {"RefNumber": "1468464", "RefComponent": "BC-CTS-TLS", "RefTitle": "Access to non-existing tables DD43L and DD43T", "RefUrl": "/notes/1468464 "}, {"RefNumber": "1428617", "RefComponent": "BC-ABA-LA", "RefTitle": "Wrong sort order after \"INSERT LINES OF\"", "RefUrl": "/notes/1428617 "}, {"RefNumber": "1473896", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS: Clicking tree control header without result", "RefUrl": "/notes/1473896 "}, {"RefNumber": "318846", "RefComponent": "BC-CST", "RefTitle": "Installing the 4.6D kernel in 4.6A/B/C SAP systems", "RefUrl": "/notes/318846 "}, {"RefNumber": "1417505", "RefComponent": "BC-CTS-TLS", "RefTitle": "Incomplete import of (ABAP Dictionary) objects", "RefUrl": "/notes/1417505 "}, {"RefNumber": "1432276", "RefComponent": "BC-FES-WGU", "RefTitle": "ITS Up/down: filedialog default extension error", "RefUrl": "/notes/1432276 "}, {"RefNumber": "1459400", "RefComponent": "BC-DB-SDB", "RefTitle": "Handling SQL error -8040", "RefUrl": "/notes/1459400 "}, {"RefNumber": "1466271", "RefComponent": "BC-DB-SDB", "RefTitle": "Number of open SAPDB connections (32) exceeded", "RefUrl": "/notes/1466271 "}, {"RefNumber": "1462882", "RefComponent": "BC-CTS-TLS", "RefTitle": "Error with addtobuffer: Illegal umode '3' specified", "RefUrl": "/notes/1462882 "}, {"RefNumber": "1446219", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS: Tree control displays too many columns", "RefUrl": "/notes/1446219 "}, {"RefNumber": "49143", "RefComponent": "XX-INT-FA-MAKE", "RefTitle": "Corrections in the delivery status", "RefUrl": "/notes/49143 "}, {"RefNumber": "1441001", "RefComponent": "BC-CTS-TLS", "RefTitle": "Performance when importing report variants", "RefUrl": "/notes/1441001 "}, {"RefNumber": "1162379", "RefComponent": "BC-UPG-OCS", "RefTitle": "After Support Package imp: Syntax errors due to ENQUEUE FMs", "RefUrl": "/notes/1162379 "}, {"RefNumber": "1379949", "RefComponent": "BC-ABA-LA", "RefTitle": "Syntax warnings for internal table keys", "RefUrl": "/notes/1379949 "}, {"RefNumber": "1357074", "RefComponent": "BC-ABA-LA", "RefTitle": "Access to Hash Tables with Data References in the Table Key", "RefUrl": "/notes/1357074 "}, {"RefNumber": "1435408", "RefComponent": "BC-CTS-TLS", "RefTitle": "Specifically transporting TDDAT, TVDIR, and TVIMF entries", "RefUrl": "/notes/1435408 "}, {"RefNumber": "1447663", "RefComponent": "BC-DB-SDB", "RefTitle": "SQL error -4005 for dbdd_get_size (MaxDB 7.8)", "RefUrl": "/notes/1447663 "}, {"RefNumber": "1437804", "RefComponent": "BC-DB-SDB", "RefTitle": "SQL error -4 005 during explain (MaxDB 7.7)", "RefUrl": "/notes/1437804 "}, {"RefNumber": "1427975", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans terminates with signal 6", "RefUrl": "/notes/1427975 "}, {"RefNumber": "680046", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools Version 6.40", "RefUrl": "/notes/680046 "}, {"RefNumber": "1364945", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS: Internal ID not set correctly in tree control", "RefUrl": "/notes/1364945 "}, {"RefNumber": "1372301", "RefComponent": "BC-CTS-TLS", "RefTitle": "Handling unconfirmed transport requests", "RefUrl": "/notes/1372301 "}, {"RefNumber": "1420733", "RefComponent": "BC-DB-SDB", "RefTitle": "DBM commands via the DBSL interface", "RefUrl": "/notes/1420733 "}, {"RefNumber": "1377688", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS: Incorrect context menu after right-clicking tree item", "RefUrl": "/notes/1377688 "}, {"RefNumber": "1374373", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS: Internal name of tree control is not unique", "RefUrl": "/notes/1374373 "}, {"RefNumber": "1418794", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS: System core dump for list trees with header line", "RefUrl": "/notes/1418794 "}, {"RefNumber": "1357431", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS: Search help hit list contains truncated entries", "RefUrl": "/notes/1357431 "}, {"RefNumber": "1318878", "RefComponent": "BC-CTS-TLS", "RefTitle": "Incomplete transport of deletions", "RefUrl": "/notes/1318878 "}, {"RefNumber": "1358085", "RefComponent": "BC-DB-SDB", "RefTitle": "SQL error -10807 Client Server Technology Veris", "RefUrl": "/notes/1358085 "}, {"RefNumber": "1382175", "RefComponent": "BC-DB-SDB", "RefTitle": "DESCRIBE for MaxDB system tables", "RefUrl": "/notes/1382175 "}, {"RefNumber": "561459", "RefComponent": "BC-DB-DBI", "RefTitle": "Accessing cluster tables with kernel 46D PL 1009-1303", "RefUrl": "/notes/561459 "}, {"RefNumber": "490482", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info on upgrading to SAP Web AS 6.20 MS SQL Server", "RefUrl": "/notes/490482 "}, {"RefNumber": "493396", "RefComponent": "BC-FES-GUI", "RefTitle": "CALL_FUNCTION_OPEN_ERROR", "RefUrl": "/notes/493396 "}, {"RefNumber": "1173582", "RefComponent": "BC-CTS-TLS", "RefTitle": "Error occurs during export: invalid value '...' in E071.LANG", "RefUrl": "/notes/1173582 "}, {"RefNumber": "575962", "RefComponent": "BC-DB-SDB-INS", "RefTitle": "Installing 3.1I SR1,4.0B SR1,4.5B and 4.6B with SAP DB 7.3", "RefUrl": "/notes/575962 "}, {"RefNumber": "1331604", "RefComponent": "BC-CTS-TLS", "RefTitle": "Content of DATE/TIME fields changed from blank to 000000", "RefUrl": "/notes/1331604 "}, {"RefNumber": "1333198", "RefComponent": "BC-DB-SDB", "RefTitle": "Signal 11 when exporting via R3load", "RefUrl": "/notes/1333198 "}, {"RefNumber": "80475", "RefComponent": "BC", "RefTitle": "Terms: Release, Support Package, kernel patch", "RefUrl": "/notes/80475 "}, {"RefNumber": "814162", "RefComponent": "BC-CST", "RefTitle": "Sessions of the plug-in HTTP type are not closed", "RefUrl": "/notes/814162 "}, {"RefNumber": "1338630", "RefComponent": "BC-DB-SDB", "RefTitle": "DBSL: Procedure to determine a data source for MaxDB", "RefUrl": "/notes/1338630 "}, {"RefNumber": "1309166", "RefComponent": "BC-ABA-LA", "RefTitle": "Performance: Incremental secondary key changes", "RefUrl": "/notes/1309166 "}, {"RefNumber": "1108909", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS tree control: Improved inclusion of MIME files", "RefUrl": "/notes/1108909 "}, {"RefNumber": "1059989", "RefComponent": "BC-ABA-LA", "RefTitle": "COLLECT/SUM: Overflow handling for types I and INT2", "RefUrl": "/notes/1059989 "}, {"RefNumber": "1288313", "RefComponent": "BC-DB-SDB", "RefTitle": "VER790D conversion error", "RefUrl": "/notes/1288313 "}, {"RefNumber": "1298409", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans import deletes enhancement objects", "RefUrl": "/notes/1298409 "}, {"RefNumber": "1315666", "RefComponent": "BC-DB-SDB", "RefTitle": "Error when accessing the MaxDB LOB locators", "RefUrl": "/notes/1315666 "}, {"RefNumber": "854044", "RefComponent": "BC-CCM-PRN-PC", "RefTitle": "Front-end printing with control technology does not work", "RefUrl": "/notes/854044 "}, {"RefNumber": "1303222", "RefComponent": "BC-UPG-TLS", "RefTitle": "R3trans terminates in the upgrade phase DDIC_UPG", "RefUrl": "/notes/1303222 "}, {"RefNumber": "1315090", "RefComponent": "BC-UPG-TLS", "RefTitle": "Termination due to deadlock during upgrade", "RefUrl": "/notes/1315090 "}, {"RefNumber": "1310023", "RefComponent": "BC-CTS-TLS", "RefTitle": "CTS+: tp hangs during file system deployment", "RefUrl": "/notes/1310023 "}, {"RefNumber": "1309061", "RefComponent": "BC-ABA-LA", "RefTitle": "Unintended sharing of two SORTED tables", "RefUrl": "/notes/1309061 "}, {"RefNumber": "1008058", "RefComponent": "BC-CTS-TLS", "RefTitle": "Performance of individual imports", "RefUrl": "/notes/1008058 "}, {"RefNumber": "1297852", "RefComponent": "BC-DB-SDB", "RefTitle": "LOB does not correspond to the database", "RefUrl": "/notes/1297852 "}, {"RefNumber": "816997", "RefComponent": "BC-CTS-TLS", "RefTitle": "No import after QA approval", "RefUrl": "/notes/816997 "}, {"RefNumber": "132536", "RefComponent": "BC-CTS-TLS", "RefTitle": "Improved semaphore mechanism in tp", "RefUrl": "/notes/132536 "}, {"RefNumber": "655018", "RefComponent": "BC-DB-SDB", "RefTitle": "Input parameters for SQL statements increased", "RefUrl": "/notes/655018 "}, {"RefNumber": "1267841", "RefComponent": "BC-DB-SDB", "RefTitle": "SQL error -3014 during CREATE INDEX SERIAL", "RefUrl": "/notes/1267841 "}, {"RefNumber": "1280771", "RefComponent": "BC-DB-SDB", "RefTitle": "Memory management is optimized for LOB locators", "RefUrl": "/notes/1280771 "}, {"RefNumber": "1288056", "RefComponent": "BC-DB-SDB", "RefTitle": "LOB_GET_LENGTH calls are not displayed in ST05", "RefUrl": "/notes/1288056 "}, {"RefNumber": "1236593", "RefComponent": "BC-FES-ITS", "RefTitle": "Long wait times when processing large trees", "RefUrl": "/notes/1236593 "}, {"RefNumber": "1267634", "RefComponent": "BC-ABA-LA", "RefTitle": "Unexpected ITAB_DUPLICATE_KEY for a non-unique secondary key", "RefUrl": "/notes/1267634 "}, {"RefNumber": "216901", "RefComponent": "BC-ABA-TV", "RefTitle": "SHD0: dialog box for inputting values is not displayed", "RefUrl": "/notes/216901 "}, {"RefNumber": "1163555", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS Tree control: Javascript file size optimized", "RefUrl": "/notes/1163555 "}, {"RefNumber": "78795", "RefComponent": "BC-TWB-TST-CAT", "RefTitle": "Advance installation CATT Recording from 3.1H", "RefUrl": "/notes/78795 "}, {"RefNumber": "128908", "RefComponent": "BC-CTS-TLS", "RefTitle": "Transporting report variants", "RefUrl": "/notes/128908 "}, {"RefNumber": "704121", "RefComponent": "CRM-BF", "RefTitle": "EBP/CRM 3.0 SP Stack 02/2004 (SAPKU30018): Release&Info.Note", "RefUrl": "/notes/704121 "}, {"RefNumber": "792317", "RefComponent": "CRM-BF", "RefTitle": "EBP/CRM 3.0 SP Stack 11/2004 (SAPKU30020): Release&Info.Note", "RefUrl": "/notes/792317 "}, {"RefNumber": "844191", "RefComponent": "CRM-BF", "RefTitle": "EBP/CRM 3.0 SP Stack 21 (05/2005) Release & Information Note", "RefUrl": "/notes/844191 "}, {"RefNumber": "747299", "RefComponent": "CRM-BF", "RefTitle": "EBP/CRM 3.0 SP Stack 06/2004 (SAPKU30019): Release&Info.Note", "RefUrl": "/notes/747299 "}, {"RefNumber": "853479", "RefComponent": "BC-CTS-TLS", "RefTitle": "Duplication of tp import with umode 1 where ctc=true", "RefUrl": "/notes/853479 "}, {"RefNumber": "334777", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: Missing user-defined functions", "RefUrl": "/notes/334777 "}, {"RefNumber": "216704", "RefComponent": "BC-DB-MSS-SYS", "RefTitle": "UPDATE dbtab FROM TABLE itab - wrong Returncode", "RefUrl": "/notes/216704 "}, {"RefNumber": "981165", "RefComponent": "BC-ABA-XML", "RefTitle": "Downport dataset reader/writer", "RefUrl": "/notes/981165 "}, {"RefNumber": "1239758", "RefComponent": "BC-FES-ITS", "RefTitle": "Javascript error with its_treeManager in IACs/EWTs", "RefUrl": "/notes/1239758 "}, {"RefNumber": "158709", "RefComponent": "BC-OP-FTS", "RefTitle": "Installation SAP R/3 64 bit kernel ReliantUNIX", "RefUrl": "/notes/158709 "}, {"RefNumber": "1236294", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp import error with CTC=1 and umode 1", "RefUrl": "/notes/1236294 "}, {"RefNumber": "1179504", "RefComponent": "BC-CTS-TMS", "RefTitle": "Alert handling in TMS and alert display in the CCMS monitor", "RefUrl": "/notes/1179504 "}, {"RefNumber": "1129638", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS Tree Control: Javascript error in Firefox 3", "RefUrl": "/notes/1129638 "}, {"RefNumber": "1131073", "RefComponent": "BC-FES-ITS", "RefTitle": "Incorrect display of list trees (SU53,SU56)", "RefUrl": "/notes/1131073 "}, {"RefNumber": "1135374", "RefComponent": "BC-FES-ITS", "RefTitle": "Tree control: AutoWidth calculation for individual columns", "RefUrl": "/notes/1135374 "}, {"RefNumber": "1108907", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS Tree Control: Javascript error after subtractions", "RefUrl": "/notes/1108907 "}, {"RefNumber": "1112697", "RefComponent": "BC-FES-ITS", "RefTitle": "Tree control: JavaScript error for treeMetaColumnLeftCell", "RefUrl": "/notes/1112697 "}, {"RefNumber": "709038", "RefComponent": "BC-FES-ITS", "RefTitle": "SAP Integrated ITS", "RefUrl": "/notes/709038 "}, {"RefNumber": "1117945", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS tree control: Incorrect sequence of node items", "RefUrl": "/notes/1117945 "}, {"RefNumber": "1119628", "RefComponent": "BC-FES-ITS", "RefTitle": "Optional display of status bar message in dialog box", "RefUrl": "/notes/1119628 "}, {"RefNumber": "355730", "RefComponent": "XX-CSC-ABPL", "RefTitle": "CSC-ABPL: Asian Best Practice Library 600", "RefUrl": "/notes/355730 "}, {"RefNumber": "1173828", "RefComponent": "BC-ABA-LA", "RefTitle": "Unjustified type conflict for DDIC int. tables with ALIAS", "RefUrl": "/notes/1173828 "}, {"RefNumber": "1173174", "RefComponent": "BC-DB-DB4", "RefTitle": "DB2/400: Import terminates w/o warning after main import", "RefUrl": "/notes/1173174 "}, {"RefNumber": "793879", "RefComponent": "BC-CTS-TLS", "RefTitle": "Incorrect transport sequence after preliminary import", "RefUrl": "/notes/793879 "}, {"RefNumber": "1120973", "RefComponent": "BC-MID-RFC", "RefTitle": "SAPGUI terminates w/o error message during connection test", "RefUrl": "/notes/1120973 "}, {"RefNumber": "1168961", "RefComponent": "BC-DB-LVC", "RefTitle": "640 EXT-2 DBSL for SCM", "RefUrl": "/notes/1168961 "}, {"RefNumber": "1164122", "RefComponent": "BC-DB-SDB", "RefTitle": "DBSL truncation warning with trace level 2", "RefUrl": "/notes/1164122 "}, {"RefNumber": "1167655", "RefComponent": "BC-ABA-LA", "RefTitle": "Unjustified type conflict for internal tables with ALIAS", "RefUrl": "/notes/1167655 "}, {"RefNumber": "1164120", "RefComponent": "BC-DB-LVC", "RefTitle": "Communication problem with liveCache 7.8", "RefUrl": "/notes/1164120 "}, {"RefNumber": "583452", "RefComponent": "BC-ABA-LA", "RefTitle": "Memory consumption display for internal tables (2)", "RefUrl": "/notes/583452 "}, {"RefNumber": "582724", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: dmdb6rdi fails with SQL0443N \"Cannot close the pipe\"", "RefUrl": "/notes/582724 "}, {"RefNumber": "580670", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: db6clp \"user is not allowed to use Security Services\"", "RefUrl": "/notes/580670 "}, {"RefNumber": "580348", "RefComponent": "BC-DB-LVC", "RefTitle": "Restricting liveCache connections", "RefUrl": "/notes/580348 "}, {"RefNumber": "578387", "RefComponent": "BC-ABA-LA", "RefTitle": "Large tokens in dynamic WHERE clause", "RefUrl": "/notes/578387 "}, {"RefNumber": "575784", "RefComponent": "BC-ABA-LA", "RefTitle": "Type check for generic table parameters", "RefUrl": "/notes/575784 "}, {"RefNumber": "573196", "RefComponent": "BC-ABA-LA", "RefTitle": "Overwrite protection for table parameters", "RefUrl": "/notes/573196 "}, {"RefNumber": "572538", "RefComponent": "BC-DB-LVC", "RefTitle": "ABAP program -> liveCache Connect", "RefUrl": "/notes/572538 "}, {"RefNumber": "571465", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans terminates when you export LIMU MESS", "RefUrl": "/notes/571465 "}, {"RefNumber": "568000", "RefComponent": "BC-DB-SDB", "RefTitle": "IN predicate with more than 254 values in the values list", "RefUrl": "/notes/568000 "}, {"RefNumber": "567997", "RefComponent": "BC-DB-SDB", "RefTitle": "SAPDB DBSL with SAPDB 7.4.3", "RefUrl": "/notes/567997 "}, {"RefNumber": "567422", "RefComponent": "BC-CTS-ORG", "RefTitle": "\"Object locked by upgrade\" error message", "RefUrl": "/notes/567422 "}, {"RefNumber": "565190", "RefComponent": "BC-ABA-LA", "RefTitle": "Memory consumption display for internal tables", "RefUrl": "/notes/565190 "}, {"RefNumber": "564992", "RefComponent": "BC-UPG-TLS", "RefTitle": "Deleted table entries after a language import", "RefUrl": "/notes/564992 "}, {"RefNumber": "564161", "RefComponent": "BC-CTS-TLS", "RefTitle": "Transport profile parameter 'dropstatistics'", "RefUrl": "/notes/564161 "}, {"RefNumber": "563784", "RefComponent": "BC-DB-SDB", "RefTitle": "Crash during initialization of the DBADASLIB", "RefUrl": "/notes/563784 "}, {"RefNumber": "563721", "RefComponent": "BC-UPG-TLS", "RefTitle": "Defective function group after upgrade with add-ons", "RefUrl": "/notes/563721 "}, {"RefNumber": "559614", "RefComponent": "BC-DB-SDB", "RefTitle": "Use of SELECT INTO for single-line resulting set", "RefUrl": "/notes/559614 "}, {"RefNumber": "556886", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: DB12 does not show any updated user exit logs data", "RefUrl": "/notes/556886 "}, {"RefNumber": "556837", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: dmdb6srp,dmdb6rts CLI0109E String data right truncation", "RefUrl": "/notes/556837 "}, {"RefNumber": "555861", "RefComponent": "BC-DB-SDB", "RefTitle": "Work process terminates after SET ISOLATION LEVEL", "RefUrl": "/notes/555861 "}, {"RefNumber": "554845", "RefComponent": "BC-CTS-TLS", "RefTitle": "ADO import fails on DB400A (AS400/ASCII)", "RefUrl": "/notes/554845 "}, {"RefNumber": "552708", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "DB error does not stop update", "RefUrl": "/notes/552708 "}, {"RefNumber": "549598", "RefComponent": "BC-DB-MSS", "RefTitle": "Data inconsistencies on MSSQL 2000 (4.6* and 6.20)", "RefUrl": "/notes/549598 "}, {"RefNumber": "549423", "RefComponent": "BC-DB-DBI", "RefTitle": "Core dump when logical cluster table with INT field accessed", "RefUrl": "/notes/549423 "}, {"RefNumber": "549053", "RefComponent": "BC-CTS-TLS", "RefTitle": "Restarting after deadlock: Objects not imported", "RefUrl": "/notes/549053 "}, {"RefNumber": "546310", "RefComponent": "BC-ABA-LA", "RefTitle": "Long runtimes during DELETE", "RefUrl": "/notes/546310 "}, {"RefNumber": "545939", "RefComponent": "BC-ABA-LA", "RefTitle": "Data references and calling the garbage collector", "RefUrl": "/notes/545939 "}, {"RefNumber": "545134", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: No version data for kernel programs in RZ20", "RefUrl": "/notes/545134 "}, {"RefNumber": "792850", "RefComponent": "BC-SEC-LGN", "RefTitle": "Preparing ABAP systems to deal with incompatible passwords", "RefUrl": "/notes/792850 "}, {"RefNumber": "539334", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: DB12 Does not display all brarchive log files", "RefUrl": "/notes/539334 "}, {"RefNumber": "538727", "RefComponent": "BC-DB-SDB", "RefTitle": "Crash (core) and signal 11 when you exit R/3", "RefUrl": "/notes/538727 "}, {"RefNumber": "538119", "RefComponent": "BC-ABA-LA", "RefTitle": "Missing alignment checks for casting operations", "RefUrl": "/notes/538119 "}, {"RefNumber": "538177", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans terminates when importing variants", "RefUrl": "/notes/538177 "}, {"RefNumber": "538014", "RefComponent": "BC-ABA-LA", "RefTitle": "Type mismatch for text symbols", "RefUrl": "/notes/538014 "}, {"RefNumber": "787347", "RefComponent": "BC-CTS-TLS", "RefTitle": "Import terminates: Internal storage of source not possible", "RefUrl": "/notes/787347 "}, {"RefNumber": "786540", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: dmdb6rdi 4.6D patch 18/6.NN patch 12", "RefUrl": "/notes/786540 "}, {"RefNumber": "536068", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp clearold: Termination with long transport request names", "RefUrl": "/notes/536068 "}, {"RefNumber": "535115", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: tp - error in signal handling -> CEEDUMP", "RefUrl": "/notes/535115 "}, {"RefNumber": "777132", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: dmdb6srp,dmdb6rts Changed Table Size Calculation", "RefUrl": "/notes/777132 "}, {"RefNumber": "534491", "RefComponent": "BC-DB-LVC", "RefTitle": "SQL error -810 (Connection already in use)", "RefUrl": "/notes/534491 "}, {"RefNumber": "534476", "RefComponent": "BC-DB-SDB", "RefTitle": "Exception during disconnect from SAPDB", "RefUrl": "/notes/534476 "}, {"RefNumber": "677061", "RefComponent": "BC-ABA-LA", "RefTitle": "LOOP at a referenced internal table", "RefUrl": "/notes/677061 "}, {"RefNumber": "674065", "RefComponent": "BC-DB-SDB", "RefTitle": "Using non supported SAPDB precompiler runtime", "RefUrl": "/notes/674065 "}, {"RefNumber": "668858", "RefComponent": "BC-CTS-TLS", "RefTitle": "Transporting transactions does not delete any authorizations", "RefUrl": "/notes/668858 "}, {"RefNumber": "1100615", "RefComponent": "BC-DB-SDB", "RefTitle": "Support for MaxDB 7.6", "RefUrl": "/notes/1100615 "}, {"RefNumber": "1095575", "RefComponent": "BC-DB-SDB", "RefTitle": "Update statistics logging", "RefUrl": "/notes/1095575 "}, {"RefNumber": "640082", "RefComponent": "BC-DB-SDB", "RefTitle": "Statement length for Update statistics command", "RefUrl": "/notes/640082 "}, {"RefNumber": "640049", "RefComponent": "BC-DB-SDB", "RefTitle": "All statements are active in the statement cache", "RefUrl": "/notes/640049 "}, {"RefNumber": "532186", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Logging 'S' type Customizing objects", "RefUrl": "/notes/532186 "}, {"RefNumber": "633062", "RefComponent": "BC-ABA-LA", "RefTitle": "Core dump in LOOP on a hashed table", "RefUrl": "/notes/633062 "}, {"RefNumber": "632975", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: ST22 - short dump ASSIGN_OFFSET+LENGTH_TOOLARGE", "RefUrl": "/notes/632975 "}, {"RefNumber": "632838", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: 'dmdb6bkp' setting the block size of the tape", "RefUrl": "/notes/632838 "}, {"RefNumber": "632323", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: db2db6  Setting the block size of the tape", "RefUrl": "/notes/632323 "}, {"RefNumber": "978742", "RefComponent": "BC-DB-SDB", "RefTitle": "MaxDB FIRST_ROWS hint for UP TO n ROWS", "RefUrl": "/notes/978742 "}, {"RefNumber": "1084953", "RefComponent": "BC-MID-RFC", "RefTitle": "Correct code page when receiving MDMP tables using RFC", "RefUrl": "/notes/1084953 "}, {"RefNumber": "1084757", "RefComponent": "BC-MID-RFC", "RefTitle": "RFC Patch Collection 33 2007", "RefUrl": "/notes/1084757 "}, {"RefNumber": "626889", "RefComponent": "BC-DB-SDB", "RefTitle": "DBSL insert without flag DBSL_FLAG_FBULK", "RefUrl": "/notes/626889 "}, {"RefNumber": "626179", "RefComponent": "BC-CTS-TLS", "RefTitle": "Screen destroyed after transport", "RefUrl": "/notes/626179 "}, {"RefNumber": "624930", "RefComponent": "BC-DB-SDB", "RefTitle": "dbs/ada/connect_delay profile parameter (internal)", "RefUrl": "/notes/624930 "}, {"RefNumber": "910935", "RefComponent": "BC-ABA-LA", "RefTitle": "SELECT ... APPENDNING to a hash table", "RefUrl": "/notes/910935 "}, {"RefNumber": "531094", "RefComponent": "BC-ABA-LA", "RefTitle": "AT END OF with a HASHED TABLE", "RefUrl": "/notes/531094 "}, {"RefNumber": "621334", "RefComponent": "BC-DB-DBI", "RefTitle": "\"invalid interface parameter 0\" with cluster access", "RefUrl": "/notes/621334 "}, {"RefNumber": "619428", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: dmdb6bkp error when importing parameters", "RefUrl": "/notes/619428 "}, {"RefNumber": "750042", "RefComponent": "BC-ABA-LA", "RefTitle": "TABLE_LINE_NOT_EXISTING with <PERSON>OOP in HASHED TABLE", "RefUrl": "/notes/750042 "}, {"RefNumber": "878803", "RefComponent": "BC-DB-DBI", "RefTitle": "R3check does not report failed name tab access", "RefUrl": "/notes/878803 "}, {"RefNumber": "616545", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: History \"Table and Indexes\" is empty", "RefUrl": "/notes/616545 "}, {"RefNumber": "746476", "RefComponent": "BC-CTS-TLS", "RefTitle": "Export with 'client=all': Data does not arrive", "RefUrl": "/notes/746476 "}, {"RefNumber": "745464", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: dmdb6bkp terminates with error SQL2036N", "RefUrl": "/notes/745464 "}, {"RefNumber": "612140", "RefComponent": "BC-ABA-LA", "RefTitle": "ITAB_ILLEGAL_ORDER runtime error", "RefUrl": "/notes/612140 "}, {"RefNumber": "742683", "RefComponent": "BC-ABA-LA", "RefTitle": "Semantics of the SUM statement", "RefUrl": "/notes/742683 "}, {"RefNumber": "529735", "RefComponent": "BC-ABA-LA", "RefTitle": "DATA_BAD_REFERENCE with READ TABLE", "RefUrl": "/notes/529735 "}, {"RefNumber": "870327", "RefComponent": "BC-DB-SDB", "RefTitle": "Optimizing update statistics", "RefUrl": "/notes/870327 "}, {"RefNumber": "527777", "RefComponent": "BC-DB-LVC", "RefTitle": "Statement cache for EXEC SQL calls", "RefUrl": "/notes/527777 "}, {"RefNumber": "607354", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: db6clp new parameter -db2clprc and SQL0104N", "RefUrl": "/notes/607354 "}, {"RefNumber": "526600", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp terminates with RC=234 (\"uncaught internal error\")", "RefUrl": "/notes/526600 "}, {"RefNumber": "525406", "RefComponent": "BC-ABA-SC", "RefTitle": "Screen Compress: Box in screen overwrites fields", "RefUrl": "/notes/525406 "}, {"RefNumber": "851455", "RefComponent": "BC-DB-DBI", "RefTitle": "R3check ends prematurely", "RefUrl": "/notes/851455 "}, {"RefNumber": "850826", "RefComponent": "BC-DB-SDB", "RefTitle": "32-bit SAPDB DBSL for NTAMD64", "RefUrl": "/notes/850826 "}, {"RefNumber": "524202", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: dmdb6srp,dmdb6rts \"Support for dbstatc-optio Vxxxx\".", "RefUrl": "/notes/524202 "}, {"RefNumber": "931129", "RefComponent": "BC-MID-RFC", "RefTitle": "Deactivating the command field during RFC debugging", "RefUrl": "/notes/931129 "}, {"RefNumber": "523435", "RefComponent": "BC-ABA-SC", "RefTitle": "Batch input: Short dump processing with CALL DIALOG USING", "RefUrl": "/notes/523435 "}, {"RefNumber": "847616", "RefComponent": "BC-DB-SDB", "RefTitle": "Update statistics: Table SDBUPDEXCL", "RefUrl": "/notes/847616 "}, {"RefNumber": "732506", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: dmdb6rdi 4.6D <PERSON> 16/6.NN Patch 10", "RefUrl": "/notes/732506 "}, {"RefNumber": "520562", "RefComponent": "BC-DB-SDB", "RefTitle": "SHARE lock after using DB_SET_ISOLATION_LEVEL", "RefUrl": "/notes/520562 "}, {"RefNumber": "731676", "RefComponent": "BC-DB-INF-INS", "RefTitle": "Inst. SAP Systems with EXT Kernel on Win/Informix 930, 9.40", "RefUrl": "/notes/731676 "}, {"RefNumber": "731295", "RefComponent": "BC-INS-UNX", "RefTitle": "Inst. SAP Systems with EXT Kernel on UNIX/Informix 9.30 9.40", "RefUrl": "/notes/731295 "}, {"RefNumber": "599461", "RefComponent": "BC-ABA-LA", "RefTitle": "Table exchange after READ/LOOP ... ASSIGNING", "RefUrl": "/notes/599461 "}, {"RefNumber": "517746", "RefComponent": "BC-DB-SDB", "RefTitle": "Problems after termination of SQL command", "RefUrl": "/notes/517746 "}, {"RefNumber": "517611", "RefComponent": "BC-DB-LVC", "RefTitle": "SQL error (-709, -821) with disconnect of the liveCache", "RefUrl": "/notes/517611 "}, {"RefNumber": "517100", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: ST04 displays incorrect value for Package Cache Size", "RefUrl": "/notes/517100 "}, {"RefNumber": "516750", "RefComponent": "BC-CTS-TLS", "RefTitle": "Export terminates due to invalid objects", "RefUrl": "/notes/516750 "}, {"RefNumber": "516675", "RefComponent": "BC-DB-DBI", "RefTitle": "UPDATE/DELETE operations terminate with subquery", "RefUrl": "/notes/516675 "}, {"RefNumber": "516663", "RefComponent": "BC-ABA-SC", "RefTitle": "SE30 with CALL TRANSACTION xxx USING and COMMIT WORK", "RefUrl": "/notes/516663 "}, {"RefNumber": "516513", "RefComponent": "BC-CTS-TLS", "RefTitle": "Incomplete transport of CDAT objects", "RefUrl": "/notes/516513 "}, {"RefNumber": "726657", "RefComponent": "BC-ABA-LA", "RefTitle": "Redundant MOVEs in table operations", "RefUrl": "/notes/726657 "}, {"RefNumber": "594650", "RefComponent": "BC-ABA-LA", "RefTitle": "VARY/VARYING and OO attributes", "RefUrl": "/notes/594650 "}, {"RefNumber": "513486", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: dmdb6rdi fails with SQL0444 reason code \"4\"", "RefUrl": "/notes/513486 "}, {"RefNumber": "594353", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: dmdb6bkp - Avoiding SQL1035N", "RefUrl": "/notes/594353 "}, {"RefNumber": "593881", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: dmdb6srp,dmdb6rts - improved table and index sizes", "RefUrl": "/notes/593881 "}, {"RefNumber": "722778", "RefComponent": "BC-ABA-LA", "RefTitle": "MOVE_TO_LIT_NOTALLOWED with READ ... TRANSPORTING", "RefUrl": "/notes/722778 "}, {"RefNumber": "508795", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: DBIF_DSQL2_SQL_ERROR runtime error in DB6_PM_SQLCACHE", "RefUrl": "/notes/508795 "}, {"RefNumber": "858033", "RefComponent": "BC-DB-MSS", "RefTitle": "For All Entries fails on large IN clauses", "RefUrl": "/notes/858033 "}, {"RefNumber": "685929", "RefComponent": "BC-ABA-LA", "RefTitle": "READ ... TRANSPORTING overwrites key fields", "RefUrl": "/notes/685929 "}, {"RefNumber": "952534", "RefComponent": "BC-DB-DBI", "RefTitle": "Incorrect termination with decompression error EOSDE", "RefUrl": "/notes/952534 "}, {"RefNumber": "680586", "RefComponent": "BC-DB-SDB", "RefTitle": "SQL error -4005 during explain (MaxDB 7.5)", "RefUrl": "/notes/680586 "}, {"RefNumber": "553755", "RefComponent": "BC-MID-RFC", "RefTitle": "Beispiel RFC Patchcollection <Woche> <Jahr>", "RefUrl": "/notes/553755 "}, {"RefNumber": "553233", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgrade 3.1->4.6 stops in phase SHADOW_IMPORT_REP", "RefUrl": "/notes/553233 "}, {"RefNumber": "661420", "RefComponent": "BC-INS-NT", "RefTitle": "Install. SAP Systems with EXT Kernel on Windows / SAP DB", "RefUrl": "/notes/661420 "}, {"RefNumber": "661419", "RefComponent": "BC-INS-UNX", "RefTitle": "Install. SAP Systems with EXT Kernel on UNIX / SAP DB", "RefUrl": "/notes/661419 "}, {"RefNumber": "661417", "RefComponent": "BC-INS-UNX", "RefTitle": "Install. SAP Systems with EXT Kernel on UNIX / Informix 7.3x", "RefUrl": "/notes/661417 "}, {"RefNumber": "655379", "RefComponent": "BC-ABA-LA", "RefTitle": "Performance: FROM MODIFY itab <wa><", "RefUrl": "/notes/655379 "}, {"RefNumber": "703397", "RefComponent": "BC-ABA-LA", "RefTitle": "Memory leak with LOOP ... WHERE", "RefUrl": "/notes/703397 "}, {"RefNumber": "652899", "RefComponent": "BC-DB-SDB", "RefTitle": "Update statistics with tables without key and index", "RefUrl": "/notes/652899 "}, {"RefNumber": "700146", "RefComponent": "BC-ABA-LA", "RefTitle": "Optional table parameters not supplied", "RefUrl": "/notes/700146 "}, {"RefNumber": "650745", "RefComponent": "BC-DB-SDB", "RefTitle": "Set UPDATE and DELETE do not process all records", "RefUrl": "/notes/650745 "}, {"RefNumber": "936522", "RefComponent": "BC-CTS-TLS", "RefTitle": "Exporting add-ons and Support Packages with TRESC conflicts", "RefUrl": "/notes/936522 "}, {"RefNumber": "648692", "RefComponent": "BC-DB-DBI", "RefTitle": "Core with repeated access to corrupt cluster record", "RefUrl": "/notes/648692 "}, {"RefNumber": "696689", "RefComponent": "BC-DB-SDB", "RefTitle": "Update statistics of a table without log file", "RefUrl": "/notes/696689 "}, {"RefNumber": "481685", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: dmdb6srp and dmdb6rts ignore dbstatc-activ='I'", "RefUrl": "/notes/481685 "}, {"RefNumber": "481162", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: dmdb6rdi extension for 6.10 DBA Cockpit", "RefUrl": "/notes/481162 "}, {"RefNumber": "480610", "RefComponent": "BC-DB-DBI", "RefTitle": "Coinciding cluster time stamp", "RefUrl": "/notes/480610 "}, {"RefNumber": "453189", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: dmdb6bkp always returns return code zero (0)", "RefUrl": "/notes/453189 "}, {"RefNumber": "453242", "RefComponent": "BC-UPG-OCS", "RefTitle": "Forms/styles deleted by Support Package", "RefUrl": "/notes/453242 "}, {"RefNumber": "455684", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: db6clp does not work with option \" -tvf <sqlfile>\"", "RefUrl": "/notes/455684 "}, {"RefNumber": "451953", "RefComponent": "BC-DB-LVC", "RefTitle": "SQL error 807 during the Disconnect of the liveCache", "RefUrl": "/notes/451953 "}, {"RefNumber": "452109", "RefComponent": "BC-CTS-TLS", "RefTitle": "Obsolete or missing texts in menus after transport", "RefUrl": "/notes/452109 "}, {"RefNumber": "339898", "RefComponent": "BC-DB-DB6-DBA", "RefTitle": "DB6: db6clp cannot execute several commands", "RefUrl": "/notes/339898 "}, {"RefNumber": "456925", "RefComponent": "BC-DB-SDB", "RefTitle": "SAPDB : Rollback before each disconnect", "RefUrl": "/notes/456925 "}, {"RefNumber": "457011", "RefComponent": "BC-ABA-SC", "RefTitle": "Trace and plausibility check on valid spaptr in dyrlalim()", "RefUrl": "/notes/457011 "}, {"RefNumber": "450605", "RefComponent": "BC-CTS-TLS", "RefTitle": "MSSQL/ADABAS: incomplete export of ABAP classes", "RefUrl": "/notes/450605 "}, {"RefNumber": "450049", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: dmdb6tbs ends with \"GET TBS STAT.SQLCODE: 290\"", "RefUrl": "/notes/450049 "}, {"RefNumber": "449376", "RefComponent": "BC-CTS-TLS", "RefTitle": "Activation error during import because of missing objects", "RefUrl": "/notes/449376 "}, {"RefNumber": "447862", "RefComponent": "BC-CTS-TMS", "RefTitle": "TMS: Source client cannot be displayed in the import queue", "RefUrl": "/notes/447862 "}, {"RefNumber": "445462", "RefComponent": "BC-DB-SDB", "RefTitle": "Incorrect patch level for dbadaslib", "RefUrl": "/notes/445462 "}, {"RefNumber": "444578", "RefComponent": "BC-MID-RFC", "RefTitle": "Core in dyrlalim as a result of IMC", "RefUrl": "/notes/444578 "}, {"RefNumber": "444045", "RefComponent": "BC-CTS-TLS", "RefTitle": "Views after import/upgrade without join conditions", "RefUrl": "/notes/444045 "}, {"RefNumber": "321115", "RefComponent": "BC-CTS-TLS", "RefTitle": "TP: many rfc*.trc files in the work directory", "RefUrl": "/notes/321115 "}, {"RefNumber": "323039", "RefComponent": "BC-ABA-LA", "RefTitle": "OBJECTS_NOT_COMPATIBLE in READ TABLE", "RefUrl": "/notes/323039 "}, {"RefNumber": "323061", "RefComponent": "BC-ABA-SC", "RefTitle": "Order lost in batch input after F4 help", "RefUrl": "/notes/323061 "}, {"RefNumber": "323228", "RefComponent": "BC-CTS-TLS", "RefTitle": "Tp requires dbtype in transport file", "RefUrl": "/notes/323228 "}, {"RefNumber": "324490", "RefComponent": "BC-ABA-SC", "RefTitle": "Double messages in the batch input log", "RefUrl": "/notes/324490 "}, {"RefNumber": "325713", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: Calling transaction ST04 terminates with SQL0302N.", "RefUrl": "/notes/325713 "}, {"RefNumber": "325771", "RefComponent": "BC-DB-LVC", "RefTitle": "Exception in storage management", "RefUrl": "/notes/325771 "}, {"RefNumber": "326487", "RefComponent": "BC-DB-SDB", "RefTitle": "Connect error : non-supported SAPDB version", "RefUrl": "/notes/326487 "}, {"RefNumber": "326774", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: Incorrect DB2_STRTTM column contents in table db6pmsm", "RefUrl": "/notes/326774 "}, {"RefNumber": "442127", "RefComponent": "BC-UPG-TLS", "RefTitle": "Error in table DDTYPET_4G, field AS4LOCAL does not exist", "RefUrl": "/notes/442127 "}, {"RefNumber": "330378", "RefComponent": "BC-CTS-TMS", "RefTitle": "All errors are not displayed in the import history TMS", "RefUrl": "/notes/330378 "}, {"RefNumber": "310777", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp/TMS asyn. import in client 000 instead of export client", "RefUrl": "/notes/310777 "}, {"RefNumber": "311147", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans refuses import of a physical cluster", "RefUrl": "/notes/311147 "}, {"RefNumber": "313003", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans term. in dbdocu.c (CI_WRITE_LINE)", "RefUrl": "/notes/313003 "}, {"RefNumber": "314159", "RefComponent": "BC-CTS-TLS", "RefTitle": "Language transport overwrites EUDB entries", "RefUrl": "/notes/314159 "}, {"RefNumber": "319440", "RefComponent": "BC-ABA-SC", "RefTitle": "E- and W-messages from subscreens run incorrectly", "RefUrl": "/notes/319440 "}, {"RefNumber": "319972", "RefComponent": "BC-ABA-LA", "RefTitle": "INSERT LINES OF ... INTO SrtTab INDEX ...", "RefUrl": "/notes/319972 "}, {"RefNumber": "309583", "RefComponent": "BC-CTS-TLS", "RefTitle": "Tp: Termination in SPAM/SAINT phase report AFTER PUT", "RefUrl": "/notes/309583 "}, {"RefNumber": "306885", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: Incorrect column contents table db6pmcm2-spm_lgpath", "RefUrl": "/notes/306885 "}, {"RefNumber": "302617", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Table logging of views", "RefUrl": "/notes/302617 "}, {"RefNumber": "303289", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp: Transport request too long although nbufform=1", "RefUrl": "/notes/303289 "}, {"RefNumber": "301932", "RefComponent": "BC-ABA-LA", "RefTitle": "READ/WRITE TEXTPOOL ... INTO/FROM itab", "RefUrl": "/notes/301932 "}, {"RefNumber": "440547", "RefComponent": "BC-CTS-TLS", "RefTitle": "Inactive import logs are not displayed", "RefUrl": "/notes/440547 "}, {"RefNumber": "438511", "RefComponent": "BC-ABA-LA", "RefTitle": "Memory overwritten for \"LOOP ... INTO <fs>\"", "RefUrl": "/notes/438511 "}, {"RefNumber": "333534", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Performance during import of table with INT fields", "RefUrl": "/notes/333534 "}, {"RefNumber": "333525", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Transport with % in key deletes too many entries", "RefUrl": "/notes/333525 "}, {"RefNumber": "334530", "RefComponent": "BC-DB-LVC", "RefTitle": "Exception in COM routine at 2 identical ABAP tables", "RefUrl": "/notes/334530 "}, {"RefNumber": "336241", "RefComponent": "BC-CTS-TLS", "RefTitle": "TP/TMS: import subset with ctc=0 ignores client", "RefUrl": "/notes/336241 "}, {"RefNumber": "337224", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: No deletion in language-specific table", "RefUrl": "/notes/337224 "}, {"RefNumber": "337107", "RefComponent": "BC-CTS-TLS", "RefTitle": "Core when importing transport profile under LINUX", "RefUrl": "/notes/337107 "}, {"RefNumber": "337516", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp/tms: import for request .. already running", "RefUrl": "/notes/337516 "}, {"RefNumber": "339299", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: Prog.dmdb6rdi terminates with SQL1403N for CLP commands", "RefUrl": "/notes/339299 "}, {"RefNumber": "339092", "RefComponent": "BC-DB-DBI", "RefTitle": "DB MultiConnect with Oracle as secondary database", "RefUrl": "/notes/339092 "}, {"RefNumber": "339270", "RefComponent": "BC-ABA-LA", "RefTitle": "Table statements for table view controls", "RefUrl": "/notes/339270 "}, {"RefNumber": "339756", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp leaves entry HEADER/D in table TRBAT", "RefUrl": "/notes/339756 "}, {"RefNumber": "319961", "RefComponent": "BC-ABA-LA", "RefTitle": "Core dump at INSERT LINES OF it1 INTO it2", "RefUrl": "/notes/319961 "}, {"RefNumber": "373137", "RefComponent": "SRM-EBP", "RefTitle": "SAPKU20C01: Support Package 1 for EBP 2.0/CRM 2.0C", "RefUrl": "/notes/373137 "}, {"RefNumber": "373138", "RefComponent": "SRM-EBP", "RefTitle": "SAPKU20C02: Support Package 2 for EBP 2.0/CRM 2.0C", "RefUrl": "/notes/373138 "}, {"RefNumber": "321018", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR programs Version 4.6D", "RefUrl": "/notes/321018 "}, {"RefNumber": "434861", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: Usage text of program sddb6his is incomprehensible", "RefUrl": "/notes/434861 "}, {"RefNumber": "351685", "RefComponent": "BC-ABA-SC", "RefTitle": "Screen objects resized incorrectly when window is enlarged", "RefUrl": "/notes/351685 "}, {"RefNumber": "352651", "RefComponent": "BC-DB-DBI", "RefTitle": "DBIF_RSQL_INVALID_REQUEST f.very old cluster records", "RefUrl": "/notes/352651 "}, {"RefNumber": "353681", "RefComponent": "BC-ABA-LA", "RefTitle": "Unexpected portability warnings from enhanced program check", "RefUrl": "/notes/353681 "}, {"RefNumber": "328596", "RefComponent": "BC-DB-DB6-DBA", "RefTitle": "Problems when calling the Control Center Extension", "RefUrl": "/notes/328596 "}, {"RefNumber": "355415", "RefComponent": "BC-UPG-TLS", "RefTitle": "R3trans: Add-On installation deletes DD texts", "RefUrl": "/notes/355415 "}, {"RefNumber": "355714", "RefComponent": "BC-DB-DBI", "RefTitle": "Sy-SubRc 8 if SELECT SINGLE on log. cluster table", "RefUrl": "/notes/355714 "}, {"RefNumber": "356804", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp does not import the first request of a list", "RefUrl": "/notes/356804 "}, {"RefNumber": "357341", "RefComponent": "BC-BSP", "RefTitle": "Installation of notes", "RefUrl": "/notes/357341 "}, {"RefNumber": "359764", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp imports to client 000 by mistake", "RefUrl": "/notes/359764 "}, {"RefNumber": "360822", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: core dump in program dmdb6bkp", "RefUrl": "/notes/360822 "}, {"RefNumber": "361735", "RefComponent": "BC-CTS-TLS", "RefTitle": "Inactive import of reports", "RefUrl": "/notes/361735 "}, {"RefNumber": "408265", "RefComponent": "BC-CTS-TLS", "RefTitle": "Comma as a language ID for the R3TR TEXT object", "RefUrl": "/notes/408265 "}, {"RefNumber": "409175", "RefComponent": "BC-ABA-LA", "RefTitle": "READ TABLE for tables of the kind SORTED TABLE (II)", "RefUrl": "/notes/409175 "}, {"RefNumber": "410175", "RefComponent": "BC-DB-DBI", "RefTitle": "SELECT SINGLE with SY-SUBRC=4: Changed Workarea", "RefUrl": "/notes/410175 "}, {"RefNumber": "410179", "RefComponent": "BC-UPG-NA", "RefTitle": "Ausbau von Hinweiskorrekturen", "RefUrl": "/notes/410179 "}, {"RefNumber": "410209", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgrade 46D DIFFEXPCUST: R3trans termin. sap_dext...", "RefUrl": "/notes/410209 "}, {"RefNumber": "365017", "RefComponent": "BC-CTS-TLS", "RefTitle": "Error 204 when importing the SPAM update", "RefUrl": "/notes/365017 "}, {"RefNumber": "365539", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans selects too many languages for LIMU CUAD", "RefUrl": "/notes/365539 "}, {"RefNumber": "411961", "RefComponent": "BC-CTS-TLS", "RefTitle": "Incorrect QA approval of client-depend. transport request", "RefUrl": "/notes/411961 "}, {"RefNumber": "412357", "RefComponent": "BC-MID-RFC", "RefTitle": "Status \"RFC error when sending logging data\" in sm58", "RefUrl": "/notes/412357 "}, {"RefNumber": "366436", "RefComponent": "BC-MID-RFC", "RefTitle": "ABAP runtime error PERFORM_NOT_FOUND for RFC", "RefUrl": "/notes/366436 "}, {"RefNumber": "412891", "RefComponent": "BC-CTS-TMS", "RefTitle": "TMS QA and several transport groups", "RefUrl": "/notes/412891 "}, {"RefNumber": "412872", "RefComponent": "BC-CTS-TMS", "RefTitle": "TMS No status change for imported requests", "RefUrl": "/notes/412872 "}, {"RefNumber": "413899", "RefComponent": "BC-DB-DBI", "RefTitle": "C core when accessing logical cluster table", "RefUrl": "/notes/413899 "}, {"RefNumber": "415811", "RefComponent": "BC-CTS-TLS", "RefTitle": "Old selection screens are active after transport", "RefUrl": "/notes/415811 "}, {"RefNumber": "370485", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgrade 4.6D: Termination in phase TABIM_UPG", "RefUrl": "/notes/370485 "}, {"RefNumber": "420223", "RefComponent": "BC-DB-SDB", "RefTitle": "EXEC SQL CANCEL via the DBSL", "RefUrl": "/notes/420223 "}, {"RefNumber": "374449", "RefComponent": "BC-CTS-TLS", "RefTitle": "Write transport logs into subdirectories", "RefUrl": "/notes/374449 "}, {"RefNumber": "374799", "RefComponent": "BC-CTS-TLS", "RefTitle": "Correction for the parameter STOPIMMEDIATELY", "RefUrl": "/notes/374799 "}, {"RefNumber": "375307", "RefComponent": "BC-UPG-OCS", "RefTitle": "tp: New feature \"dropbuffer\" for SPAM", "RefUrl": "/notes/375307 "}, {"RefNumber": "88656", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Duplicate key composite SAP note (Release 4.0-4.6)", "RefUrl": "/notes/88656 "}, {"RefNumber": "311308", "RefComponent": "BC-INS-UNX", "RefTitle": "INST: 4.6D SAP Basis Inst. on UNIX - Oracle", "RefUrl": "/notes/311308 "}, {"RefNumber": "311305", "RefComponent": "BC-INS-UNX", "RefTitle": "INST: 4.6D SAP Basis Installation on UNIX", "RefUrl": "/notes/311305 "}, {"RefNumber": "375562", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans MSSQL: missing documentation for R3TR SOBJ", "RefUrl": "/notes/375562 "}, {"RefNumber": "376214", "RefComponent": "BC-CTS-TLS", "RefTitle": "getspacerequirements for split R3trans files", "RefUrl": "/notes/376214 "}, {"RefNumber": "431913", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: sddb6mir hangs", "RefUrl": "/notes/431913 "}, {"RefNumber": "376707", "RefComponent": "BC-MID-RFC", "RefTitle": "Cannot find Function \"XAB_RE...\" in SMQ1/SMQ2/SM58", "RefUrl": "/notes/376707 "}, {"RefNumber": "431450", "RefComponent": "BC-ABA-LA", "RefTitle": "Core dump for COLLECT with components of STRING type", "RefUrl": "/notes/431450 "}, {"RefNumber": "424330", "RefComponent": "BC-DB-SDB", "RefTitle": "SQL error -4005 with statistics update (UPD_COND)", "RefUrl": "/notes/424330 "}, {"RefNumber": "425502", "RefComponent": "BC-DB-DBI", "RefTitle": "ASCII data in cluster tables after EBCDIC conversion", "RefUrl": "/notes/425502 "}, {"RefNumber": "379231", "RefComponent": "BC-DB-INF", "RefTitle": "Informix client versions", "RefUrl": "/notes/379231 "}, {"RefNumber": "424986", "RefComponent": "CRM-MW-COM", "RefTitle": "Middleware HOTFIX for Mobile Clients: CRM 2.0C", "RefUrl": "/notes/424986 "}, {"RefNumber": "430632", "RefComponent": "BC-DB-LVC", "RefTitle": "SQL error -752 when you connect to the liveCache", "RefUrl": "/notes/430632 "}, {"RefNumber": "381968", "RefComponent": "BC-DB-DBI", "RefTitle": "DBIF_RSQL_INVALID_REQUEST for log. cluster tables", "RefUrl": "/notes/381968 "}, {"RefNumber": "383380", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans leaves behind obsolete E071K entries", "RefUrl": "/notes/383380 "}, {"RefNumber": "384570", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgrade 4.6D: Terminate in Phase DIFFEXPDDIV", "RefUrl": "/notes/384570 "}, {"RefNumber": "430411", "RefComponent": "BC-ABA-LA", "RefTitle": "Memory consumption display for hash tables in debugger", "RefUrl": "/notes/430411 "}, {"RefNumber": "391944", "RefComponent": "BC-DB-LVC", "RefTitle": "Statement cache for COM routines", "RefUrl": "/notes/391944 "}, {"RefNumber": "393032", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: dmdb6srp: get uid, get password failed rc= 19", "RefUrl": "/notes/393032 "}, {"RefNumber": "393055", "RefComponent": "BC-INS-MIG", "RefTitle": "DBEXPORT: Problems with R3SZCHK_IND_INF (R3szchk)", "RefUrl": "/notes/393055 "}, {"RefNumber": "393378", "RefComponent": "BC-ABA-LA", "RefTitle": "Garbage collection beyond 600MB", "RefUrl": "/notes/393378 "}, {"RefNumber": "204493", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: Program termination of dmdb6rdi", "RefUrl": "/notes/204493 "}, {"RefNumber": "395205", "RefComponent": "BC-ABA-LA", "RefTitle": "READ TABLE for SORTED TABLES", "RefUrl": "/notes/395205 "}, {"RefNumber": "212569", "RefComponent": "BC-ABA-LI", "RefTitle": "Error with list entry fields in batch input", "RefUrl": "/notes/212569 "}, {"RefNumber": "210790", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: Perfrmnc montr does not display current values", "RefUrl": "/notes/210790 "}, {"RefNumber": "425192", "RefComponent": "BC-DWB-TOO-FUB", "RefTitle": "SMDL transport after SPAU compar. of func. modules", "RefUrl": "/notes/425192 "}, {"RefNumber": "422289", "RefComponent": "BC-ABA-LA", "RefTitle": "Incorrect order after 'INSERT LINES OF' ...'", "RefUrl": "/notes/422289 "}, {"RefNumber": "311309", "RefComponent": "BC-INS-UNX", "RefTitle": "SAP Software on UNIX: OS Dependencies 4.6D", "RefUrl": "/notes/311309 "}, {"RefNumber": "173814", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Known problems with Support Packages for Release 4.6", "RefUrl": "/notes/173814 "}, {"RefNumber": "429186", "RefComponent": "BC-CTS-TLS", "RefTitle": "Deleting recently imported DD entries", "RefUrl": "/notes/429186 "}, {"RefNumber": "427719", "RefComponent": "BC-DB-SDB", "RefTitle": "SAPDB database version with build level", "RefUrl": "/notes/427719 "}, {"RefNumber": "310513", "RefComponent": "BC-SEC-AUT", "RefTitle": "SU53 only delivers 'Check on S_TCODE'", "RefUrl": "/notes/310513 "}, {"RefNumber": "332320", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Data not visible in buffered views", "RefUrl": "/notes/332320 "}, {"RefNumber": "410485", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Import stops although no errors occurred", "RefUrl": "/notes/410485 "}, {"RefNumber": "419918", "RefComponent": "BC-INS-UNX", "RefTitle": "INST: SAP Basis 4.6D SR1 Installation on UNIX", "RefUrl": "/notes/419918 "}, {"RefNumber": "378313", "RefComponent": "BC-CTS-LAN", "RefTitle": "R3trans: termination in \"postlanguageimport\"", "RefUrl": "/notes/378313 "}, {"RefNumber": "353110", "RefComponent": "BC-OP-SUN", "RefTitle": "Installation 64-bit SAP 4.6D Kernel for Solaris", "RefUrl": "/notes/353110 "}, {"RefNumber": "416210", "RefComponent": "BC-DB-LVC", "RefTitle": "omsTerminate with long error text", "RefUrl": "/notes/416210 "}, {"RefNumber": "403142", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans destroys documentation objects with more than 65535", "RefUrl": "/notes/403142 "}, {"RefNumber": "402375", "RefComponent": "BC-CTS-TLS", "RefTitle": "Buffer synchronization of VARIS during transport", "RefUrl": "/notes/402375 "}, {"RefNumber": "402255", "RefComponent": "BC-ABA-LA", "RefTitle": "Table code definition with offset/length", "RefUrl": "/notes/402255 "}, {"RefNumber": "401227", "RefComponent": "BC-DB-LVC", "RefTitle": "ABAP structures as input parameter COM routines incorrect", "RefUrl": "/notes/401227 "}, {"RefNumber": "400216", "RefComponent": "BC-DB-DBI", "RefTitle": "Forcing minimum patch level of the database library", "RefUrl": "/notes/400216 "}, {"RefNumber": "400516", "RefComponent": "BC-ABA-LA", "RefTitle": "Core dump with READ with attribute access", "RefUrl": "/notes/400516 "}, {"RefNumber": "400818", "RefComponent": "BC-DB-DBI", "RefTitle": "Information about the R/3 database library", "RefUrl": "/notes/400818 "}, {"RefNumber": "505755", "RefComponent": "BC-DB-SDB", "RefTitle": "'DBMS client library' does not contain any data", "RefUrl": "/notes/505755 "}, {"RefNumber": "503229", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp terminates with signal 4 (Compaq True64 5.1A)", "RefUrl": "/notes/503229 "}, {"RefNumber": "500435", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans hangs in a high availability environment", "RefUrl": "/notes/500435 "}, {"RefNumber": "500363", "RefComponent": "BC-CTS-TLS", "RefTitle": "Import not possible as type X cannot be converted to type B", "RefUrl": "/notes/500363 "}, {"RefNumber": "500306", "RefComponent": "BC-ABA-SC", "RefTitle": "Batch input: Authorization for deleting transactions", "RefUrl": "/notes/500306 "}, {"RefNumber": "499672", "RefComponent": "BC-DB-SDB", "RefTitle": "Internal SAPDB_GET_DBSL_INFO database procedure", "RefUrl": "/notes/499672 "}, {"RefNumber": "497065", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: dmdb6rdi SQL5066W database config parameter truncated", "RefUrl": "/notes/497065 "}, {"RefNumber": "495625", "RefComponent": "BC-I18", "RefTitle": "Transliteration of non-Latin1 texts while importing", "RefUrl": "/notes/495625 "}, {"RefNumber": "494875", "RefComponent": "BC-CTS-TLS", "RefTitle": "FATAL ERROR: Transport profile not correctly maintained", "RefUrl": "/notes/494875 "}, {"RefNumber": "490553", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: dmdb6bkp Incorrect \"location\" in DB2 History File", "RefUrl": "/notes/490553 "}, {"RefNumber": "489868", "RefComponent": "BC-CTS-TLS", "RefTitle": "Import damages function groups and deletes screens", "RefUrl": "/notes/489868 "}, {"RefNumber": "488377", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: dmdb6rdi CLI0111E Numeric value out of range", "RefUrl": "/notes/488377 "}, {"RefNumber": "487295", "RefComponent": "BC-DB-SDB", "RefTitle": "New update statistics feature with DBSL_UPD_STAT", "RefUrl": "/notes/487295 "}, {"RefNumber": "486631", "RefComponent": "BC-CTS-TMS", "RefTitle": "imports in transport groups", "RefUrl": "/notes/486631 "}, {"RefNumber": "486561", "RefComponent": "BC-CTS-TLS", "RefTitle": "Faulty transport of an unsorted transport request", "RefUrl": "/notes/486561 "}, {"RefNumber": "486284", "RefComponent": "BC-DB-LVC", "RefTitle": "Operation type displayed incorrectly in ST05", "RefUrl": "/notes/486284 "}, {"RefNumber": "180743", "RefComponent": "BC-CTS-TLS", "RefTitle": "Missing DDTYPES entries after transport", "RefUrl": "/notes/180743 "}, {"RefNumber": "173330", "RefComponent": "BC-UPG-TLS", "RefTitle": "Conversion of tables EDIDOC & CDCLS Release 4.6", "RefUrl": "/notes/173330 "}, {"RefNumber": "163149", "RefComponent": "BC-ABA-SC", "RefTitle": "translated titles are not accessible", "RefUrl": "/notes/163149 "}, {"RefNumber": "163639", "RefComponent": "BC-ABA-SC", "RefTitle": "Transaction processed in spite of error", "RefUrl": "/notes/163639 "}, {"RefNumber": "155261", "RefComponent": "BC-ABA-LI", "RefTitle": "Signal 11 when printing Hebrew or Arabic", "RefUrl": "/notes/155261 "}, {"RefNumber": "123694", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: dmdb6srp ends with memory protection violation", "RefUrl": "/notes/123694 "}, {"RefNumber": "126776", "RefComponent": "BC-CTS", "RefTitle": "Transports between Releases 4.5 and 4.6", "RefUrl": "/notes/126776 "}, {"RefNumber": "1115001", "RefComponent": "BC-UPG-TLS", "RefTitle": "Termination in upgrade phase Shadow_Import_UPG1", "RefUrl": "/notes/1115001 "}, {"RefNumber": "1109102", "RefComponent": "BC-ABA-LA", "RefTitle": "Platform-dependent calculation of source hashes", "RefUrl": "/notes/1109102 "}, {"RefNumber": "304238", "RefComponent": "BC-INS-UNX", "RefTitle": "SAP BW 2.0B Installation on UNIX", "RefUrl": "/notes/304238 "}, {"RefNumber": "1143249", "RefComponent": "BC-ABA-LA", "RefTitle": "STRING_BAD_REF: Actual parameter is illegal PXA string ref.", "RefUrl": "/notes/1143249 "}, {"RefNumber": "1110690", "RefComponent": "BC-UPG-OCS", "RefTitle": "Deadlock when importing Support Package", "RefUrl": "/notes/1110690 "}, {"RefNumber": "411829", "RefComponent": "CRM-MW-COM", "RefTitle": "Middleware HOTFIX for Mobile Clients: CRM 2.0B", "RefUrl": "/notes/411829 "}, {"RefNumber": "1130854", "RefComponent": "BC-DB-SDB", "RefTitle": "Commit after update statistics using DBSL", "RefUrl": "/notes/1130854 "}, {"RefNumber": "1131799", "RefComponent": "BC-ABA-LA", "RefTitle": "Dynamic MOVE-CORRESPONDING cache: TIME_OUT", "RefUrl": "/notes/1131799 "}, {"RefNumber": "1130991", "RefComponent": "BC-ABA-LA", "RefTitle": "Dynamic MOVE-CORRESPONDING cache: ABAP_ASSERT", "RefUrl": "/notes/1130991 "}, {"RefNumber": "1113226", "RefComponent": "BC-ABA-LA", "RefTitle": "EXPORT/IMPORT: UC converter for R3TRANS", "RefUrl": "/notes/1113226 "}, {"RefNumber": "1130853", "RefComponent": "BC-DB-SDB", "RefTitle": "SQL error -10416 when reading DECFLOAT_DEC", "RefUrl": "/notes/1130853 "}, {"RefNumber": "1130891", "RefComponent": "BC-ABA-LA", "RefTitle": "ITAB_DUPLICATE_KEY after SORT of itab with UNIQUE keys", "RefUrl": "/notes/1130891 "}, {"RefNumber": "524570", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgrade terminates in phase TP_ACTION_CP2STAB", "RefUrl": "/notes/524570 "}, {"RefNumber": "854073", "RefComponent": "BC-ABA-LA", "RefTitle": "Core dump in ab_BtreeBlockDelete() on Linux IA64", "RefUrl": "/notes/854073 "}, {"RefNumber": "1132805", "RefComponent": "BC-ABA-LA", "RefTitle": "ABAP_ASSERT for AT NEW / END OF a->b", "RefUrl": "/notes/1132805 "}, {"RefNumber": "1129593", "RefComponent": "BC-ABA-LA", "RefTitle": "Coredump after TIME_OUT during selection screen generation", "RefUrl": "/notes/1129593 "}, {"RefNumber": "511323", "RefComponent": "BC-DB-DB6-DBA", "RefTitle": "DB6: brdb6brt -Installing the latest redirected restore tool", "RefUrl": "/notes/511323 "}, {"RefNumber": "455506", "RefComponent": "BC-DB-DB6-DBA", "RefTitle": "DB6: Installing the Latest 6NN DB2 Admin Tools", "RefUrl": "/notes/455506 "}, {"RefNumber": "1129477", "RefComponent": "BC-ABA-LA", "RefTitle": "STRING_BAD_REF: Illegal PXA strings in dyn. WHERE condition", "RefUrl": "/notes/1129477 "}, {"RefNumber": "1121076", "RefComponent": "BC-DB-SDB", "RefTitle": "Incorrect return code for native SQL", "RefUrl": "/notes/1121076 "}, {"RefNumber": "101365", "RefComponent": "BC-CST-GW", "RefTitle": "Connection error NIECONN_PENDING", "RefUrl": "/notes/101365 "}, {"RefNumber": "1121860", "RefComponent": "BC-ABA-LA", "RefTitle": "Static MOVE-CORRESPONDING: Data loss", "RefUrl": "/notes/1121860 "}, {"RefNumber": "973246", "RefComponent": "BC-SEC-VIR", "RefTitle": "Setting the temporary scan directory", "RefUrl": "/notes/973246 "}, {"RefNumber": "917891", "RefComponent": "BC-ABA-LA", "RefTitle": "MVCO: Incorrect syntax message for CLIKE and SIMPLE operands", "RefUrl": "/notes/917891 "}, {"RefNumber": "1111752", "RefComponent": "BC-ABA-LA", "RefTitle": "Missing statement coverage for tests in ABAP Unit Favorites", "RefUrl": "/notes/1111752 "}, {"RefNumber": "1088922", "RefComponent": "BC-MID-RFC", "RefTitle": "Improvement: Starting RFC servers", "RefUrl": "/notes/1088922 "}, {"RefNumber": "967081", "RefComponent": "BC-UPG-TLS", "RefTitle": "Improved performance during BW upgrade", "RefUrl": "/notes/967081 "}, {"RefNumber": "1092566", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans SPRX: Export error for table SPROXHDR", "RefUrl": "/notes/1092566 "}, {"RefNumber": "60928", "RefComponent": "BC-CTS", "RefTitle": "Transports between Release 3.0 or 3.1 and Release 4.0", "RefUrl": "/notes/60928 "}, {"RefNumber": "120151", "RefComponent": "BC-CTS", "RefTitle": "Transports between Release 4.0 and 4.5", "RefUrl": "/notes/120151 "}, {"RefNumber": "93096", "RefComponent": "BC-DB-INF-UPG", "RefTitle": "Upgrade 4.0A: Termin. in phase NTACT_PREMV/NTACT_MV", "RefUrl": "/notes/93096 "}, {"RefNumber": "1096387", "RefComponent": "BC-DB-SDB", "RefTitle": "Update statistics (PREPARE) runtime optimization", "RefUrl": "/notes/1096387 "}, {"RefNumber": "1097930", "RefComponent": "BC-DB-SDB", "RefTitle": "No readable password for SQLOPT in developer trace", "RefUrl": "/notes/1097930 "}, {"RefNumber": "1081277", "RefComponent": "BC-ABA-LA", "RefTitle": "Single record DELETE with secondary key from deep table", "RefUrl": "/notes/1081277 "}, {"RefNumber": "695033", "RefComponent": "BC-ABA-LA", "RefTitle": "Eliminating AKK violation of CALL TRANSFORMATION", "RefUrl": "/notes/695033 "}, {"RefNumber": "1086430", "RefComponent": "BC-DB-SDB", "RefTitle": "Update statistics (PREPARE) considers number of lines", "RefUrl": "/notes/1086430 "}, {"RefNumber": "6588", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Memory allocation failed", "RefUrl": "/notes/6588 "}, {"RefNumber": "1060088", "RefComponent": "BC-CTS-TMS", "RefTitle": "Long runtime for workflow-controlled imports", "RefUrl": "/notes/1060088 "}, {"RefNumber": "1040924", "RefComponent": "BC-ABA-LA", "RefTitle": "Problems with the alignment of decfloat34 etc.", "RefUrl": "/notes/1040924 "}, {"RefNumber": "1040759", "RefComponent": "BC-ABA-LA", "RefTitle": "Itab Patch Collection 01/2007", "RefUrl": "/notes/1040759 "}, {"RefNumber": "1040925", "RefComponent": "BC-ABA-LA", "RefTitle": "Syntax error not issued for the CREATE DATA itab", "RefUrl": "/notes/1040925 "}, {"RefNumber": "739825", "RefComponent": "BC-ABA-XML", "RefTitle": "Core dump at tt:copy during deserialization", "RefUrl": "/notes/739825 "}, {"RefNumber": "747155", "RefComponent": "BC-ABA-XML", "RefTitle": "Static interface attributes in asXML", "RefUrl": "/notes/747155 "}, {"RefNumber": "1041547", "RefComponent": "BC-DB-SDB", "RefTitle": "Return value 100 in update statistics run", "RefUrl": "/notes/1041547 "}, {"RefNumber": "494348", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Corrections for the MDX parser", "RefUrl": "/notes/494348 "}, {"RefNumber": "1020550", "RefComponent": "BC-ABA-LA", "RefTitle": "Session termination during SORT", "RefUrl": "/notes/1020550 "}, {"RefNumber": "363632", "RefComponent": "BC-DB-INF", "RefTitle": "Problems with IDS Version 7.31[UTF]C7", "RefUrl": "/notes/363632 "}, {"RefNumber": "1014411", "RefComponent": "BC-DB-SDB", "RefTitle": "Commection name corrected for EXPLAIN", "RefUrl": "/notes/1014411 "}, {"RefNumber": "325402", "RefComponent": "BC-DB-LVC", "RefTitle": "dbadaslib/dbsdbslib: How do I apply a patch?", "RefUrl": "/notes/325402 "}, {"RefNumber": "982723", "RefComponent": "BC-CTS-TLS", "RefTitle": "Transport of tables with String key fields", "RefUrl": "/notes/982723 "}, {"RefNumber": "871945", "RefComponent": "BC-I18-UNI", "RefTitle": "Fallback Code Page for Zn Languages in Unicode/nonU RFC", "RefUrl": "/notes/871945 "}, {"RefNumber": "937535", "RefComponent": "BC-ABA-LA", "RefTitle": "ABAP Debugger: Jump over LOOP ... WHERE", "RefUrl": "/notes/937535 "}, {"RefNumber": "121998", "RefComponent": "BC-MID-RFC", "RefTitle": "ABAP runtime error: CALL_FUNCTION_WAIT_ERROR", "RefUrl": "/notes/121998 "}, {"RefNumber": "970473", "RefComponent": "BC-MID-RFC", "RefTitle": "SAPGUI uses 32-bit RFC library on 64-bit WIN XP", "RefUrl": "/notes/970473 "}, {"RefNumber": "929512", "RefComponent": "BC-SEC-SSF", "RefTitle": "Sporadic errors with SSO (MYSAPSSO2) from J2EE to ABAP", "RefUrl": "/notes/929512 "}, {"RefNumber": "970967", "RefComponent": "BC-CTS-TLS", "RefTitle": "Termination in the SHADOW_IMPORT_INC phase with rc=12", "RefUrl": "/notes/970967 "}, {"RefNumber": "955028", "RefComponent": "BC-ABA-LA", "RefTitle": "Poor performance after changes to COLLECT table", "RefUrl": "/notes/955028 "}, {"RefNumber": "955030", "RefComponent": "BC-ABA-LA", "RefTitle": "Missing registrations in LOOP", "RefUrl": "/notes/955030 "}, {"RefNumber": "962708", "RefComponent": "BC-DB-SDB", "RefTitle": "MaxDB DBSL patch collection 1 2006", "RefUrl": "/notes/962708 "}, {"RefNumber": "954973", "RefComponent": "SRM-EBP", "RefTitle": "SRM 5.0 SP-Stack 05 (07/2006) (SAPKIBKT05): Release/Info Not", "RefUrl": "/notes/954973 "}, {"RefNumber": "809279", "RefComponent": "BC-I18-UNI", "RefTitle": "RFC non-Unicode to Unicode with unknown text language", "RefUrl": "/notes/809279 "}, {"RefNumber": "939104", "RefComponent": "BC-DB-DBI", "RefTitle": "Logical cluster tables in the namespace", "RefUrl": "/notes/939104 "}, {"RefNumber": "959198", "RefComponent": "BC-VMC", "RefTitle": "NetWeaver 04S kernel patch level for CRM 5.0 SP 5", "RefUrl": "/notes/959198 "}, {"RefNumber": "170297", "RefComponent": "BC-DB-SDB", "RefTitle": "SAP DB release upgrade to 6.2", "RefUrl": "/notes/170297 "}, {"RefNumber": "210033", "RefComponent": "BC-DB-SDB", "RefTitle": "-2010 after table copy", "RefUrl": "/notes/210033 "}, {"RefNumber": "811918", "RefComponent": "BC-DB-SDB-INS", "RefTitle": "Installing MaxDB 7.5 for SAP products in Basis 46D", "RefUrl": "/notes/811918 "}, {"RefNumber": "155580", "RefComponent": "BC-CTS-TLS", "RefTitle": "Creating report loads after transport", "RefUrl": "/notes/155580 "}, {"RefNumber": "544881", "RefComponent": "BC-CCM-BTC-JOB", "RefTitle": "Composite SAP note: Time-driven jobs do not run", "RefUrl": "/notes/544881 "}, {"RefNumber": "835291", "RefComponent": "BC-ABA-LA", "RefTitle": "ABAP debugger: Go to statement during LOOP", "RefUrl": "/notes/835291 "}, {"RefNumber": "932516", "RefComponent": "BC-DB-SDB", "RefTitle": "Signal 11 with explain", "RefUrl": "/notes/932516 "}, {"RefNumber": "934196", "RefComponent": "BC-DB-SDB", "RefTitle": "DB50 - PERM block defect", "RefUrl": "/notes/934196 "}, {"RefNumber": "930736", "RefComponent": "BC-DB-DBI", "RefTitle": "RTAB: invalid cursor after initial rollback", "RefUrl": "/notes/930736 "}, {"RefNumber": "664679", "RefComponent": "BC-CST", "RefTitle": "Installing 6.40 kernel in SAP WEB AS 6.10/6.20", "RefUrl": "/notes/664679 "}, {"RefNumber": "925337", "RefComponent": "BC-DB-SDB", "RefTitle": "ADBC : If cursor is used with HOLD, incorrect data is read", "RefUrl": "/notes/925337 "}, {"RefNumber": "923561", "RefComponent": "BC-ABA-LA", "RefTitle": "Trigger garbage collector at READ ... REFERENCE INTO", "RefUrl": "/notes/923561 "}, {"RefNumber": "923564", "RefComponent": "BC-ABA-LA", "RefTitle": "READ with key with deep structure", "RefUrl": "/notes/923564 "}, {"RefNumber": "920743", "RefComponent": "BC-DB-SDB", "RefTitle": "Signal 11 when you create a BIA index", "RefUrl": "/notes/920743 "}, {"RefNumber": "432272", "RefComponent": "BC-CTS-TLS", "RefTitle": "Transporting language data using tp/R3trans", "RefUrl": "/notes/432272 "}, {"RefNumber": "919603", "RefComponent": "BC-ABA-LA", "RefTitle": "VMIT: Collective patch 2006 #1", "RefUrl": "/notes/919603 "}, {"RefNumber": "407107", "RefComponent": "BC-INS-UNX", "RefTitle": "INST: SAP Web AS 6.10 Installation on UNIX", "RefUrl": "/notes/407107 "}, {"RefNumber": "908602", "RefComponent": "BC-DB-SDB", "RefTitle": "MaxDB DBSL patch collection 2 2005", "RefUrl": "/notes/908602 "}, {"RefNumber": "907288", "RefComponent": "BC-CTS-TLS", "RefTitle": "Error message \"key field ENHOBJ-ENHOBJTYPE missing\"", "RefUrl": "/notes/907288 "}, {"RefNumber": "708625", "RefComponent": "BC-DB-SDB", "RefTitle": "SQL error -3005 or -4005 with UPDSTAT (SAP DB 7.4 or higher)", "RefUrl": "/notes/708625 "}, {"RefNumber": "778958", "RefComponent": "BC-CTS-TLS", "RefTitle": "Repeat import without umode 1", "RefUrl": "/notes/778958 "}, {"RefNumber": "97630", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Familiar problems with patches 3.1H-4.0B", "RefUrl": "/notes/97630 "}, {"RefNumber": "383473", "RefComponent": "CRM-MW-COM", "RefTitle": "CRM installations with firewalls - configuration guidelines", "RefUrl": "/notes/383473 "}, {"RefNumber": "857506", "RefComponent": "BC-DB-SDB", "RefTitle": "Correction of STRING fields after EXPORT/IMPORT with R3load", "RefUrl": "/notes/857506 "}, {"RefNumber": "390062", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to 4.6C SR2", "RefUrl": "/notes/390062 "}, {"RefNumber": "871851", "RefComponent": "BC-ABA-LA", "RefTitle": "GETWA_NOT_ASSIGNED as of patch level 84", "RefUrl": "/notes/871851 "}, {"RefNumber": "313107", "RefComponent": "BC-INS-UNX", "RefTitle": "INST: 4.6C SR1 R/3 Installation on UNIX", "RefUrl": "/notes/313107 "}, {"RefNumber": "375182", "RefComponent": "BC-INS-UNX", "RefTitle": "INST: 4.0B SR1 + 40B_COM Kernel R/3 on UNIX", "RefUrl": "/notes/375182 "}, {"RefNumber": "387074", "RefComponent": "BC-INS-UNX", "RefTitle": "INST: R/3 4.6C SR2 Installation on UNIX", "RefUrl": "/notes/387074 "}, {"RefNumber": "852036", "RefComponent": "BC-ABA-LA", "RefTitle": "INSERT to a shared HASHED TABLE in a LOOP", "RefUrl": "/notes/852036 "}, {"RefNumber": "96885", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Downloading front end patches", "RefUrl": "/notes/96885 "}, {"RefNumber": "97629", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Known problems with patches Rel. 4.5", "RefUrl": "/notes/97629 "}, {"RefNumber": "841997", "RefComponent": "BC-ABA-LA", "RefTitle": "Mode termination after SORT within a LOOP statement", "RefUrl": "/notes/841997 "}, {"RefNumber": "644640", "RefComponent": "BC", "RefTitle": "Basis Support Package and SAP Kernel interdependencies", "RefUrl": "/notes/644640 "}, {"RefNumber": "835809", "RefComponent": "BC-DB-SDB", "RefTitle": "Optimizing the stream compression", "RefUrl": "/notes/835809 "}, {"RefNumber": "850827", "RefComponent": "BC-DB-SDB", "RefTitle": "R3load: Length of STRING fields is incorrect (Unicode)", "RefUrl": "/notes/850827 "}, {"RefNumber": "850830", "RefComponent": "BC-DB-SDB", "RefTitle": "MaxDB DBSL patch collection 1 2005", "RefUrl": "/notes/850830 "}, {"RefNumber": "845378", "RefComponent": "BC-ABA-LA", "RefTitle": "TABLE_LINE_NOT_EXISTING for MODIFY ... WHERE", "RefUrl": "/notes/845378 "}, {"RefNumber": "843787", "RefComponent": "BC-DB-SDB", "RefTitle": "Filling up NUMC data", "RefUrl": "/notes/843787 "}, {"RefNumber": "846524", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp addtobuffer/import terminates with signal 11", "RefUrl": "/notes/846524 "}, {"RefNumber": "183118", "RefComponent": "BC-ABA-LA", "RefTitle": "SELECT FOR ALL ENTRIES and ORDER BY PRIMARY KEY", "RefUrl": "/notes/183118 "}, {"RefNumber": "90039", "RefComponent": "BC-I18", "RefTitle": "work process termination on Windows NT", "RefUrl": "/notes/90039 "}, {"RefNumber": "838823", "RefComponent": "BC-DB-SDB", "RefTitle": "Exception or signal 11 in tp, R3trans or R3load", "RefUrl": "/notes/838823 "}, {"RefNumber": "808634", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgrade to Basis Release 6.20/6.40 deletes screens", "RefUrl": "/notes/808634 "}, {"RefNumber": "839691", "RefComponent": "BC-ABA-LA", "RefTitle": "Performance: Comparison of shared internal tables", "RefUrl": "/notes/839691 "}, {"RefNumber": "834807", "RefComponent": "BC-ABA-LA", "RefTitle": "Memory consumption of a SORTED TABLE after IMPORT", "RefUrl": "/notes/834807 "}, {"RefNumber": "825670", "RefComponent": "BC-ABA-LA", "RefTitle": "READ optimization for over-specific key specifications", "RefUrl": "/notes/825670 "}, {"RefNumber": "620544", "RefComponent": "SRM-EBP", "RefTitle": "Support Package 02 for SRM 3.0", "RefUrl": "/notes/620544 "}, {"RefNumber": "807216", "RefComponent": "BC-DB-SDB", "RefTitle": "Incorrect number of records with Native SQL in ST05", "RefUrl": "/notes/807216 "}, {"RefNumber": "807218", "RefComponent": "BC-DB-SDB", "RefTitle": "No minimum release check for the second connection", "RefUrl": "/notes/807218 "}, {"RefNumber": "795666", "RefComponent": "BC-ABA-LA", "RefTitle": "SHO: ABAP_ASSERT with ASSIGING during update lock", "RefUrl": "/notes/795666 "}, {"RefNumber": "313111", "RefComponent": "BC-INS-UNX", "RefTitle": "SAP Software on UNIX: OS Dependencies 4.6C SR1", "RefUrl": "/notes/313111 "}, {"RefNumber": "808719", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: APYR3FIX terminates because of missing program", "RefUrl": "/notes/808719 "}, {"RefNumber": "514205", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools version 6.20", "RefUrl": "/notes/514205 "}, {"RefNumber": "808860", "RefComponent": "BC-ABA-LA", "RefTitle": "SHO: Changing priv. inst. attr. under shared lock, core dump", "RefUrl": "/notes/808860 "}, {"RefNumber": "421121", "RefComponent": "BC-OP-AIX", "RefTitle": "Transaction SICK reports AIX 5.1, 5.2, 5.3 as unsupported", "RefUrl": "/notes/421121 "}, {"RefNumber": "813367", "RefComponent": "BC-DB-DBI", "RefTitle": "R3load: cluster nametabs appear to have shrunk", "RefUrl": "/notes/813367 "}, {"RefNumber": "819443", "RefComponent": "BC-DB-MSS", "RefTitle": "Memory problem in MSS DBSL", "RefUrl": "/notes/819443 "}, {"RefNumber": "686580", "RefComponent": "BC-UPG-OCS", "RefTitle": "tp: <PERSON><PERSON> after error in request-independent step", "RefUrl": "/notes/686580 "}, {"RefNumber": "807933", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgrade stops in phase DIFFEXP*", "RefUrl": "/notes/807933 "}, {"RefNumber": "338500", "RefComponent": "XX-PROJ-CBU", "RefTitle": "CBU: additnal informatn on the Export/Upgrade, 46C SR2", "RefUrl": "/notes/338500 "}, {"RefNumber": "673666", "RefComponent": "BC-DB-DBI", "RefTitle": "Code page migration of cluster tables (add fields)", "RefUrl": "/notes/673666 "}, {"RefNumber": "783184", "RefComponent": "BC-DB-SDB", "RefTitle": "Core Dump with R3trans -d", "RefUrl": "/notes/783184 "}, {"RefNumber": "720083", "RefComponent": "BC", "RefTitle": "6.20 Basis Support Package 38 requires SAP kernel patch", "RefUrl": "/notes/720083 "}, {"RefNumber": "788658", "RefComponent": "BC-DB-SDB", "RefTitle": "Current DBSL patch level not displayed", "RefUrl": "/notes/788658 "}, {"RefNumber": "169536", "RefComponent": "BC-CTS-TLS", "RefTitle": "TP changed ORA_NLS33 to a wrong value", "RefUrl": "/notes/169536 "}, {"RefNumber": "783870", "RefComponent": "BC-ABA-LA", "RefTitle": "Memory bottleneck despite deletion of large internal tables", "RefUrl": "/notes/783870 "}, {"RefNumber": "481228", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: dmdb6rdi support for incremental and delta backups", "RefUrl": "/notes/481228 "}, {"RefNumber": "539378", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: dmdb6rdi support for node configuration", "RefUrl": "/notes/539378 "}, {"RefNumber": "596298", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: ST04 error message \"C-CALL GET_DBPARMS failed\" (II)", "RefUrl": "/notes/596298 "}, {"RefNumber": "748297", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: ST04 \"No systems are enabled or configured\"", "RefUrl": "/notes/748297 "}, {"RefNumber": "301749", "RefComponent": "BC-UPG-TLS", "RefTitle": "Problems with LDBAS when including packages", "RefUrl": "/notes/301749 "}, {"RefNumber": "781331", "RefComponent": "BC-DB-MSS", "RefTitle": "MARS Support for SQL Server 2005", "RefUrl": "/notes/781331 "}, {"RefNumber": "780318", "RefComponent": "BC-CTS-TLS", "RefTitle": "Incomplete import although the return code is 8 or lower", "RefUrl": "/notes/780318 "}, {"RefNumber": "780238", "RefComponent": "BC-DB-SDB", "RefTitle": "No SESSION ABORT after SQL error -810", "RefUrl": "/notes/780238 "}, {"RefNumber": "682808", "RefComponent": "MDM", "RefTitle": "MDM 2.00 SP05: Release and info note (SAPK-20005INMDM)", "RefUrl": "/notes/682808 "}, {"RefNumber": "753384", "RefComponent": "MDM", "RefTitle": "MDM 2.00 SP06:Release and information note (SAPK-20006INMDM)", "RefUrl": "/notes/753384 "}, {"RefNumber": "588568", "RefComponent": "BC-XI", "RefTitle": "SAP Exchange Infrastructure 2.0: Patch procedure", "RefUrl": "/notes/588568 "}, {"RefNumber": "749082", "RefComponent": "BC-ABA-LA", "RefTitle": "Shared objects - deleting a local internal table", "RefUrl": "/notes/749082 "}, {"RefNumber": "732922", "RefComponent": "BC-ABA-LA", "RefTitle": "CREATE for SORTED or HASHED TABLE with DEFAULT KEY", "RefUrl": "/notes/732922 "}, {"RefNumber": "761167", "RefComponent": "BC-CTS-TLS", "RefTitle": "Nonsense error message referring truncated NUMC field", "RefUrl": "/notes/761167 "}, {"RefNumber": "761170", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp terminates due to error in function Utf8nToUcnOverlap_2", "RefUrl": "/notes/761170 "}, {"RefNumber": "749075", "RefComponent": "BC-ABA-LA", "RefTitle": "Inserting a LOOP reference in an HASHED TABLE", "RefUrl": "/notes/749075 "}, {"RefNumber": "750052", "RefComponent": "BC-DB-INF", "RefTitle": "NT: Unexpected SQL-410/-349/-255xx terminations with AS", "RefUrl": "/notes/750052 "}, {"RefNumber": "754819", "RefComponent": "BC-DB-MSS", "RefTitle": "GetNextMsst1 error when running 6.40 executables on 6.20", "RefUrl": "/notes/754819 "}, {"RefNumber": "395326", "RefComponent": "MP", "RefTitle": "Collective Note: MarketSet 2.0 Patch Installation", "RefUrl": "/notes/395326 "}, {"RefNumber": "313110", "RefComponent": "BC-INS-UNX", "RefTitle": "INST: 4.6C SR1 R/3 Inst. on UNIX - Oracle", "RefUrl": "/notes/313110 "}, {"RefNumber": "201147", "RefComponent": "BC-INS-UNX", "RefTitle": "INST: 4.6C R/3 Inst. on UNIX - Oracle", "RefUrl": "/notes/201147 "}, {"RefNumber": "176426", "RefComponent": "BC-INS-UNX", "RefTitle": "Using CAR for extracting archives on AIX", "RefUrl": "/notes/176426 "}, {"RefNumber": "142996", "RefComponent": "BC-INS-UNX", "RefTitle": "INST: 4.5B R/3 Inst. on UNIX - Oracle Database", "RefUrl": "/notes/142996 "}, {"RefNumber": "370601", "RefComponent": "SCM-APO", "RefTitle": "Composite SAP note: APO 3.0 and 3.1 performance", "RefUrl": "/notes/370601 "}, {"RefNumber": "403706", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools Version 6.10", "RefUrl": "/notes/403706 "}, {"RefNumber": "686861", "RefComponent": "SRM-EBP", "RefTitle": "Support Package 04 for SRM 3.0", "RefUrl": "/notes/686861 "}, {"RefNumber": "749078", "RefComponent": "BC-ABA-LA", "RefTitle": "RUNT_ILLEGAL_SWITCH with REFERENCE INTO", "RefUrl": "/notes/749078 "}, {"RefNumber": "730157", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Incorrect behavior in the Web after kernel patch", "RefUrl": "/notes/730157 "}, {"RefNumber": "611264", "RefComponent": "BC-ABA-LA", "RefTitle": "New INTO addition for CLEANUP", "RefUrl": "/notes/611264 "}, {"RefNumber": "179139", "RefComponent": "BC-INS-UNX", "RefTitle": "INST: 4.6B R/3 Inst. on UNIX - Oracle", "RefUrl": "/notes/179139 "}, {"RefNumber": "720343", "RefComponent": "BC-DB-SDB", "RefTitle": "DBSL error 21 in DbSlLobGetPiece", "RefUrl": "/notes/720343 "}, {"RefNumber": "725720", "RefComponent": "BC-ABA-LA", "RefTitle": "READ ... BINARY for SORTED TABLE with attribute key", "RefUrl": "/notes/725720 "}, {"RefNumber": "733079", "RefComponent": "BC-ABA-LA", "RefTitle": "Core at INSERT after a SORT statement", "RefUrl": "/notes/733079 "}, {"RefNumber": "721190", "RefComponent": "BC-ABA-LA", "RefTitle": "ABAP_ASSERT for DETACH_COMMIT in the Garbage Collector", "RefUrl": "/notes/721190 "}, {"RefNumber": "363086", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp termination in showbuffer only during RFC call", "RefUrl": "/notes/363086 "}, {"RefNumber": "712739", "RefComponent": "BC-ABA-LA", "RefTitle": "Shared objects: Update for initial table", "RefUrl": "/notes/712739 "}, {"RefNumber": "713242", "RefComponent": "BC-DB-SDB", "RefTitle": "Dialog is not cancelled", "RefUrl": "/notes/713242 "}, {"RefNumber": "699411", "RefComponent": "BC-ABA-LA", "RefTitle": "INSERT LINES insertion not stable", "RefUrl": "/notes/699411 "}, {"RefNumber": "702442", "RefComponent": "BC-ABA-LA", "RefTitle": "RUNT_ILLEGAL_SWITCH during garbage collection", "RefUrl": "/notes/702442 "}, {"RefNumber": "706908", "RefComponent": "BC-DB-SDB", "RefTitle": "Support of MySQL MaxDB 7.5", "RefUrl": "/notes/706908 "}, {"RefNumber": "708201", "RefComponent": "BC-DB-LVC", "RefTitle": "CHECKPOINT NOWAIT is no longer required", "RefUrl": "/notes/708201 "}, {"RefNumber": "527047", "RefComponent": "BC-INS", "RefTitle": "SAP Web AS 6.20 server installation/upgrade Asia", "RefUrl": "/notes/527047 "}, {"RefNumber": "532039", "RefComponent": "BC-INS", "RefTitle": "SAP BW 3.0B Server installation/upgrade Asia", "RefUrl": "/notes/532039 "}, {"RefNumber": "534417", "RefComponent": "BC-INS", "RefTitle": "Enterprise Buyer 3.5 Server installation/upgrade Asia", "RefUrl": "/notes/534417 "}, {"RefNumber": "702544", "RefComponent": "BC-ABA-LA", "RefTitle": "LOOP on a referenced internal table (2)", "RefUrl": "/notes/702544 "}, {"RefNumber": "118956", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Syslog: No active link, CMSEND(A)", "RefUrl": "/notes/118956 "}, {"RefNumber": "508216", "RefComponent": "BC-CTS-TLS", "RefTitle": "Import terminates due to int. tw_get_type_conversion error", "RefUrl": "/notes/508216 "}, {"RefNumber": "618400", "RefComponent": "SRM-EBP", "RefTitle": "Support Package 01 for SRM 3.0", "RefUrl": "/notes/618400 "}, {"RefNumber": "655737", "RefComponent": "SRM-EBP", "RefTitle": "Support Package 03 for SRM 3.0", "RefUrl": "/notes/655737 "}, {"RefNumber": "143047", "RefComponent": "BC-INS-UNX", "RefTitle": "INST: 4.5B R/3 Inst. on UNIX - Adabas Database", "RefUrl": "/notes/143047 "}, {"RefNumber": "103562", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: 'twrtab memory limit reached' then term.", "RefUrl": "/notes/103562 "}, {"RefNumber": "680591", "RefComponent": "BC-DB-SDB", "RefTitle": "SQL error -3008/-3017 during the explain (UNICODE system)", "RefUrl": "/notes/680591 "}, {"RefNumber": "672471", "RefComponent": "CRM", "RefTitle": "EBP/CRM 3.0 SP Stack 11/2003 (SAPKU30017): Release&Info.Note", "RefUrl": "/notes/672471 "}, {"RefNumber": "619598", "RefComponent": "SRM-EBP", "RefTitle": "Support Package 07 for SRM 2.0", "RefUrl": "/notes/619598 "}, {"RefNumber": "545613", "RefComponent": "CRM", "RefTitle": "SAPKU30011: Support Package 11 for EBP 3.0/CRM 3.0", "RefUrl": "/notes/545613 "}, {"RefNumber": "535882", "RefComponent": "CRM", "RefTitle": "SAPKU30010: Support Package 10 for EBP 3.0/CRM 3.0", "RefUrl": "/notes/535882 "}, {"RefNumber": "668370", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans termination with SQL error 99999 table E071", "RefUrl": "/notes/668370 "}, {"RefNumber": "656248", "RefComponent": "BC-ABA-LA", "RefTitle": "Core at CALL METHOD parameter transfer", "RefUrl": "/notes/656248 "}, {"RefNumber": "651612", "RefComponent": "BC-ABA-LA", "RefTitle": "ASSIGNING with unsuccessful operation", "RefUrl": "/notes/651612 "}, {"RefNumber": "656881", "RefComponent": "BC-ABA-LA", "RefTitle": "READ: core for structured attribute key", "RefUrl": "/notes/656881 "}, {"RefNumber": "594640", "RefComponent": "SRM-EBP", "RefTitle": "Support Package 06 for SRM 2.0", "RefUrl": "/notes/594640 "}, {"RefNumber": "650413", "RefComponent": "BC-ABA-LA", "RefTitle": "Core with CREATE DATA for internal table", "RefUrl": "/notes/650413 "}, {"RefNumber": "600094", "RefComponent": "BC-CTS-TLS", "RefTitle": "BSP fragment not current after transport", "RefUrl": "/notes/600094 "}, {"RefNumber": "646661", "RefComponent": "CRM", "RefTitle": "SAPKU30016: Support Package 16 for EBP 3.0/CRM 3.0", "RefUrl": "/notes/646661 "}, {"RefNumber": "102034", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Control of the COMMIT intervals", "RefUrl": "/notes/102034 "}, {"RefNumber": "628397", "RefComponent": "CRM", "RefTitle": "SAPKU30015: Support Package 15 for EBP 3.0/CRM 3.0", "RefUrl": "/notes/628397 "}, {"RefNumber": "645922", "RefComponent": "BC-ABA-LA", "RefTitle": "CREATE DATA patch collection I", "RefUrl": "/notes/645922 "}, {"RefNumber": "643960", "RefComponent": "BC-ABA-LA", "RefTitle": "ABAP_ASSERT with READ with empty dynamic key specification", "RefUrl": "/notes/643960 "}, {"RefNumber": "651018", "RefComponent": "BC-ABA-LA", "RefTitle": "Incorrect syntax check warning for method named OBJECT", "RefUrl": "/notes/651018 "}, {"RefNumber": "547705", "RefComponent": "CRM", "RefTitle": "SAPKU31001: Support Package 01 for CRM 3.1", "RefUrl": "/notes/547705 "}, {"RefNumber": "532976", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgrade phase TOOLIMP* or XCNV_DUMP with source Rel.<= 4.5", "RefUrl": "/notes/532976 "}, {"RefNumber": "545852", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgrade to Basis Release 6.20 SHADOW_IMPORT_*", "RefUrl": "/notes/545852 "}, {"RefNumber": "638251", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp: Termination during the import into Unicode systems", "RefUrl": "/notes/638251 "}, {"RefNumber": "639322", "RefComponent": "BC-DB-SDB", "RefTitle": "Output in the developer trace is too long", "RefUrl": "/notes/639322 "}, {"RefNumber": "570951", "RefComponent": "BC-ABA-LA", "RefTitle": "READ...BINARY SEARCH in Unicode systems", "RefUrl": "/notes/570951 "}, {"RefNumber": "608811", "RefComponent": "BC-DB-SDB", "RefTitle": "ABAP 'UP TO N ROWS' option", "RefUrl": "/notes/608811 "}, {"RefNumber": "609353", "RefComponent": "CRM", "RefTitle": "SAPKU30014: Support Package 14 for EBP 3.0/CRM 3.0", "RefUrl": "/notes/609353 "}, {"RefNumber": "622675", "RefComponent": "BC-DB-DBI", "RefTitle": "R3trans termination with signal 11", "RefUrl": "/notes/622675 "}, {"RefNumber": "622164", "RefComponent": "BC-ABA-LA", "RefTitle": "Incorrect syntax error with CREATE DATA", "RefUrl": "/notes/622164 "}, {"RefNumber": "618431", "RefComponent": "BC-UPG-ADDON", "RefTitle": "AddOn-Upgrade:XPRAs are not executed", "RefUrl": "/notes/618431 "}, {"RefNumber": "617505", "RefComponent": "BC-ABA-LA", "RefTitle": "Runtime error DSTEP_WRONG_PROGRAM when exiting a transaction", "RefUrl": "/notes/617505 "}, {"RefNumber": "611229", "RefComponent": "BC-ABA-LA", "RefTitle": "Short dump EXCP_INTERNAL_ERROR with watchpoints", "RefUrl": "/notes/611229 "}, {"RefNumber": "611212", "RefComponent": "BC-ABA-LA", "RefTitle": "Syntax error with MESSAGE with objects except for exceptions", "RefUrl": "/notes/611212 "}, {"RefNumber": "611147", "RefComponent": "BC-ABA-LA", "RefTitle": "SUBMIT and LEAVE not allowed in CLEANUP", "RefUrl": "/notes/611147 "}, {"RefNumber": "616516", "RefComponent": "BC-ABA-LA", "RefTitle": "Syntax error with MESSAGE string_or_ref", "RefUrl": "/notes/616516 "}, {"RefNumber": "615583", "RefComponent": "BC-ABA-LA", "RefTitle": "SYSTEM_CORE_DUMPED in Workbench, navigation, syntax check", "RefUrl": "/notes/615583 "}, {"RefNumber": "604447", "RefComponent": "BC-DB-SDB", "RefTitle": "SAPDB: Performance problem with the DB connect", "RefUrl": "/notes/604447 "}, {"RefNumber": "547725", "RefComponent": "SRM", "RefTitle": "Support Package 03 for SRM 2.0", "RefUrl": "/notes/547725 "}, {"RefNumber": "559048", "RefComponent": "SRM", "RefTitle": "Support Package 04/Service Release 01 for SRM 2.0", "RefUrl": "/notes/559048 "}, {"RefNumber": "569649", "RefComponent": "SRM", "RefTitle": "Support Package 05 for SRM 2.0", "RefUrl": "/notes/569649 "}, {"RefNumber": "607740", "RefComponent": "BC-DB-SDB", "RefTitle": "SQL error 200 during the statistics update", "RefUrl": "/notes/607740 "}, {"RefNumber": "565640", "RefComponent": "CRM", "RefTitle": "SAPKU31002: Support Package 02 for CRM 3.1", "RefUrl": "/notes/565640 "}, {"RefNumber": "609071", "RefComponent": "BC-ABA-LA", "RefTitle": "Mode termination with MODIFY with index specification", "RefUrl": "/notes/609071 "}, {"RefNumber": "330793", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Downloading patches", "RefUrl": "/notes/330793 "}, {"RefNumber": "582586", "RefComponent": "CRM", "RefTitle": "SAPKU30013: Support Package 13 for EBP 3.0/CRM 3.0", "RefUrl": "/notes/582586 "}, {"RefNumber": "596802", "RefComponent": "BC-MID-ICF", "RefTitle": "ABAP runtime error HTTP_NO_HANDLE", "RefUrl": "/notes/596802 "}, {"RefNumber": "599414", "RefComponent": "BC-ABA-LA", "RefTitle": "Core dump with CREATE DATA for internal tables", "RefUrl": "/notes/599414 "}, {"RefNumber": "556734", "RefComponent": "BC-CTS", "RefTitle": "FAQ Transport: Setup and further information", "RefUrl": "/notes/556734 "}, {"RefNumber": "593677", "RefComponent": "BC-ABA-LA", "RefTitle": "Debugger function \"Go to statement using LOOPs", "RefUrl": "/notes/593677 "}, {"RefNumber": "596290", "RefComponent": "BC-DB-SDB", "RefTitle": "Incorrect return code when reading LOB data", "RefUrl": "/notes/596290 "}, {"RefNumber": "577109", "RefComponent": "BC-DB-SDB", "RefTitle": "Known errors: Installing SAP Systems with SAP DB 7.3", "RefUrl": "/notes/577109 "}, {"RefNumber": "338968", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "ICNV in the upgrade - cluster tables", "RefUrl": "/notes/338968 "}, {"RefNumber": "588924", "RefComponent": "BC-ESI-WS-ABA", "RefTitle": "SOAP inbox ignores IMPORTING parameter", "RefUrl": "/notes/588924 "}, {"RefNumber": "588809", "RefComponent": "BC-ABA-LA", "RefTitle": "ABAP_ASSERT in LOOP processing", "RefUrl": "/notes/588809 "}, {"RefNumber": "561987", "RefComponent": "CRM", "RefTitle": "SAPKU30012: Support Package 12 for EBP 3.0/CRM 3.0", "RefUrl": "/notes/561987 "}, {"RefNumber": "583424", "RefComponent": "BC-ABA-LA", "RefTitle": "Potential core dumps in ABTSV shift", "RefUrl": "/notes/583424 "}, {"RefNumber": "583402", "RefComponent": "BC-ABA-LA", "RefTitle": "Missing syntax warning message for CREATE", "RefUrl": "/notes/583402 "}, {"RefNumber": "507237", "RefComponent": "CRM", "RefTitle": "SAPKU30009: Support Package 9 for EBP 3.0/CRM 3.0", "RefUrl": "/notes/507237 "}, {"RefNumber": "578059", "RefComponent": "BC-DB-SDB", "RefTitle": "Cursor handling via 2 connections", "RefUrl": "/notes/578059 "}, {"RefNumber": "578336", "RefComponent": "BC-DB-SDB", "RefTitle": "Signal 11 with client copy", "RefUrl": "/notes/578336 "}, {"RefNumber": "578949", "RefComponent": "BC-DB-SDB", "RefTitle": "Signal 11 when closing an unopened cursor", "RefUrl": "/notes/578949 "}, {"RefNumber": "581255", "RefComponent": "BC-ABA-LA", "RefTitle": "Dictionary (SE11) initial line count for internal tables", "RefUrl": "/notes/581255 "}, {"RefNumber": "542605", "RefComponent": "BC-ABA-SC", "RefTitle": "MODIFY SCREEN: REQUIRED attribute depends on INPUT", "RefUrl": "/notes/542605 "}, {"RefNumber": "571735", "RefComponent": "BC-ABA-LA", "RefTitle": "RTM test for writing to write-protected tables", "RefUrl": "/notes/571735 "}, {"RefNumber": "313879", "RefComponent": "BC-UPG-OCS", "RefTitle": "Error message concerning table class during SP import", "RefUrl": "/notes/313879 "}, {"RefNumber": "501866", "RefComponent": "BC-ABA-LA", "RefTitle": "Garbage collector deletes live event handler objects", "RefUrl": "/notes/501866 "}, {"RefNumber": "569417", "RefComponent": "BC-ABA-LA", "RefTitle": "Syntax error with DELETE dbtab FROM wa", "RefUrl": "/notes/569417 "}, {"RefNumber": "566474", "RefComponent": "BC-UPG-TLS", "RefTitle": "Defective ABAP class after upgrade", "RefUrl": "/notes/566474 "}, {"RefNumber": "562358", "RefComponent": "BC-DB-SDB", "RefTitle": "UCS-2 Cursor and statement ID", "RefUrl": "/notes/562358 "}, {"RefNumber": "505771", "RefComponent": "BC-CTS-TLS", "RefTitle": "Incorrect import status in the STMS transaction", "RefUrl": "/notes/505771 "}, {"RefNumber": "494901", "RefComponent": "CRM", "RefTitle": "SAPKU30008: Support Package 8 for EBP 3.0/CRM 3.0", "RefUrl": "/notes/494901 "}, {"RefNumber": "558377", "RefComponent": "BC-ABA-LA", "RefTitle": "READ with a structured key", "RefUrl": "/notes/558377 "}, {"RefNumber": "554772", "RefComponent": "BC-DB-SDB", "RefTitle": "String fields are truncated for INSERT quantity", "RefUrl": "/notes/554772 "}, {"RefNumber": "555313", "RefComponent": "BC-DB-SDB", "RefTitle": "SQL error -802 with INSERT of packed numbers", "RefUrl": "/notes/555313 "}, {"RefNumber": "168175", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp check/clearold problems", "RefUrl": "/notes/168175 "}, {"RefNumber": "439285", "RefComponent": "BC-CTS-TLS", "RefTitle": "Arbeiten mit signierten Dateien (SSF)", "RefUrl": "/notes/439285 "}, {"RefNumber": "334412", "RefComponent": "BC-CTS-TLS", "RefTitle": "Faster import of R3TR FORM on DB2/390", "RefUrl": "/notes/334412 "}, {"RefNumber": "550941", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgrade 3.1 -> Basis 6.20 termination in DIFFEXP*", "RefUrl": "/notes/550941 "}, {"RefNumber": "352269", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "Spool kernel patches (collective note)", "RefUrl": "/notes/352269 "}, {"RefNumber": "549764", "RefComponent": "BC-DB-DBI", "RefTitle": "Core in wa_check_longfld", "RefUrl": "/notes/549764 "}, {"RefNumber": "547844", "RefComponent": "BC-ABA-LA", "RefTitle": "Performance: Unicode STRUCTURE parameter transfer", "RefUrl": "/notes/547844 "}, {"RefNumber": "536328", "RefComponent": "BC-ABA-LA", "RefTitle": "REF_TYPE_CONFLICT for REFERENCE INTO", "RefUrl": "/notes/536328 "}, {"RefNumber": "146248", "RefComponent": "BC-OP-HPX", "RefTitle": "Installation SAP R/3 4.0B-64bit kernel", "RefUrl": "/notes/146248 "}, {"RefNumber": "450366", "RefComponent": "CRM", "RefTitle": "SAPKU30007: Support Package 7 for EBP 3.0/CRM 3.0", "RefUrl": "/notes/450366 "}, {"RefNumber": "437284", "RefComponent": "SRM-EBP", "RefTitle": "SAPKU30005: Support Package 5 for EBP 3.0/CRM 3.0", "RefUrl": "/notes/437284 "}, {"RefNumber": "433032", "RefComponent": "SRM-EBP", "RefTitle": "SAPKU30004: Support Package 4 for EBP 3.0/CRM 3.0", "RefUrl": "/notes/433032 "}, {"RefNumber": "424198", "RefComponent": "SRM-EBP", "RefTitle": "SAPKU30002: Support Package 2 for EBP 3.0/CRM 3.0", "RefUrl": "/notes/424198 "}, {"RefNumber": "542697", "RefComponent": "BC-ABA-LA", "RefTitle": "Memory leak in syntax check", "RefUrl": "/notes/542697 "}, {"RefNumber": "546291", "RefComponent": "BC-DB-SDB", "RefTitle": "DBSL memory usage in RSDBBUFF", "RefUrl": "/notes/546291 "}, {"RefNumber": "452342", "RefComponent": "BC-DB-SDB", "RefTitle": "Application terminates with message -602 or -8006", "RefUrl": "/notes/452342 "}, {"RefNumber": "544548", "RefComponent": "BC-DB-SDB-UPG", "RefTitle": "Incorrect upgrade user check", "RefUrl": "/notes/544548 "}, {"RefNumber": "156314", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgrade to 4.5B: Termination in phase DIFFEXP*", "RefUrl": "/notes/156314 "}, {"RefNumber": "485479", "RefComponent": "BC-OP-AS4", "RefTitle": "R3trans: 2EETW152 Cannot open file \"/usr/sap/trans/tmp/...\"", "RefUrl": "/notes/485479 "}, {"RefNumber": "515578", "RefComponent": "BC-ABA-LA", "RefTitle": "READ ... BINARY SEARCH on a SORTED TABLE", "RefUrl": "/notes/515578 "}, {"RefNumber": "144892", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR programs version 4.5B", "RefUrl": "/notes/144892 "}, {"RefNumber": "100189", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Applying kernel patches", "RefUrl": "/notes/100189 "}, {"RefNumber": "532030", "RefComponent": "BC-ABA-LA", "RefTitle": "Write protection with REFERENCE INTO", "RefUrl": "/notes/532030 "}, {"RefNumber": "163694", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Logging table changes", "RefUrl": "/notes/163694 "}, {"RefNumber": "525502", "RefComponent": "BC-ABA-LA", "RefTitle": "GETWA_NOT_ASSIGNED in functional method call (module)", "RefUrl": "/notes/525502 "}, {"RefNumber": "525065", "RefComponent": "BC-ABA-LA", "RefTitle": "ABAP generation: core dump in initial record compression", "RefUrl": "/notes/525065 "}, {"RefNumber": "81950", "RefComponent": "BC-SRV-TIM-TZ", "RefTitle": "Time zones: local date in dialog and in RFC", "RefUrl": "/notes/81950 "}, {"RefNumber": "514582", "RefComponent": "BC-ABA-LA", "RefTitle": "SYSTEM_ON_COMMIT_INTERRUPTED", "RefUrl": "/notes/514582 "}, {"RefNumber": "110934", "RefComponent": "BW", "RefTitle": "SAP BW Support Packages", "RefUrl": "/notes/110934 "}, {"RefNumber": "514054", "RefComponent": "BC-DB-SDB", "RefTitle": "LIKE does not find any records on a NUMC field", "RefUrl": "/notes/514054 "}, {"RefNumber": "489522", "RefComponent": "BC-DB-SDB", "RefTitle": "Error during LOB update", "RefUrl": "/notes/489522 "}, {"RefNumber": "500280", "RefComponent": "BC-DB-SDB", "RefTitle": "Select returns random results despite null value in DB", "RefUrl": "/notes/500280 "}, {"RefNumber": "426523", "RefComponent": "BC-SRV-COM-FTP", "RefTitle": "SAPFTP: Commands cannot be executed", "RefUrl": "/notes/426523 "}, {"RefNumber": "498091", "RefComponent": "BC-ABA-LA", "RefTitle": "CREATE DATA for text elements", "RefUrl": "/notes/498091 "}, {"RefNumber": "442193", "RefComponent": "BC-UPG-TLS", "RefTitle": "sap_dext called with msgnr \"\"\"32\"\"\" in SHADOW_IMPORT_ALL", "RefUrl": "/notes/442193 "}, {"RefNumber": "429474", "RefComponent": "BC-CTS-TLS", "RefTitle": "Transport CHAR->NUMC incorrect", "RefUrl": "/notes/429474 "}, {"RefNumber": "493870", "RefComponent": "BC-DB-SDB", "RefTitle": "Exception in R3load when creating a table", "RefUrl": "/notes/493870 "}, {"RefNumber": "18236", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Export to tape / Splitting the export", "RefUrl": "/notes/18236 "}, {"RefNumber": "144487", "RefComponent": "BC-DB-DBI", "RefTitle": "Missing lines in logical cluster tables", "RefUrl": "/notes/144487 "}, {"RefNumber": "108576", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions upgrade from 2.2x to 4.0B ADABAS", "RefUrl": "/notes/108576 "}, {"RefNumber": "149686", "RefComponent": "BC-UPG-OCS", "RefTitle": "Known problems when applying BW patches (rel. 1.2x)", "RefUrl": "/notes/149686 "}, {"RefNumber": "458338", "RefComponent": "PY-NO", "RefTitle": "HR-NO: Legal changes Norwegian tax programs", "RefUrl": "/notes/458338 "}, {"RefNumber": "449999", "RefComponent": "BC-ABA-LA", "RefTitle": "Changing a table in the ABAP debugger", "RefUrl": "/notes/449999 "}, {"RefNumber": "306096", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Message class with underscore in the name", "RefUrl": "/notes/306096 "}, {"RefNumber": "336496", "RefComponent": "BC-DB-DBI", "RefTitle": "Incremental conversion of cluster tables", "RefUrl": "/notes/336496 "}, {"RefNumber": "162696", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans generates INF-360 when import. W. CSI=1", "RefUrl": "/notes/162696 "}, {"RefNumber": "156084", "RefComponent": "BC-DB-DBI", "RefTitle": "ORA-1023 after Reconnect", "RefUrl": "/notes/156084 "}, {"RefNumber": "452710", "RefComponent": "BC-CTS-TLS", "RefTitle": "duplicate record with RSMPTEXTS/EUDB table", "RefUrl": "/notes/452710 "}, {"RefNumber": "155560", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp: No delivery of target groups", "RefUrl": "/notes/155560 "}, {"RefNumber": "174108", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp: couldn't locate TA-info in .../cofiles", "RefUrl": "/notes/174108 "}, {"RefNumber": "197742", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Message documentation with _ in name", "RefUrl": "/notes/197742 "}, {"RefNumber": "193745", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Transport of 120 characters table key", "RefUrl": "/notes/193745 "}, {"RefNumber": "185908", "RefComponent": "BC-CTS-TLS", "RefTitle": "Incomplete transport of function modules", "RefUrl": "/notes/185908 "}, {"RefNumber": "217262", "RefComponent": "BC-ABA-LA", "RefTitle": "Runtime error text for TSV_TNEW_PAGE_ALLOC_FAILED", "RefUrl": "/notes/217262 "}, {"RefNumber": "141465", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Conversion of tables EDIDOC & CDCLS, Release 4.5", "RefUrl": "/notes/141465 "}, {"RefNumber": "315352", "RefComponent": "BC-DB-DB6-DBA", "RefTitle": "Install. Instlltn of 4.6D DB2 admin tools fr 4.0B/4.5Bsystms", "RefUrl": "/notes/315352 "}, {"RefNumber": "420122", "RefComponent": "BC-DB-DBI", "RefTitle": "Too many results during import with MINOR-ID add-on", "RefUrl": "/notes/420122 "}, {"RefNumber": "422593", "RefComponent": "BC-CTS-TLS", "RefTitle": "'Import all' terminates with RC 234.", "RefUrl": "/notes/422593 "}, {"RefNumber": "427031", "RefComponent": "BC-ABA-LA", "RefTitle": "Coredump when generating in cg_iloop()", "RefUrl": "/notes/427031 "}, {"RefNumber": "333878", "RefComponent": "BC-CST-UP", "RefTitle": "Transactional RFC is not executed", "RefUrl": "/notes/333878 "}, {"RefNumber": "426060", "RefComponent": "BC-UPG-TLS", "RefTitle": "Error in the upgrade on Basis 6.10 (Phase SHADOW_IMPORT_ALL)", "RefUrl": "/notes/426060 "}, {"RefNumber": "174309", "RefComponent": "BC-DB-ORA-CCM", "RefTitle": "DB12: Incorrect Alert: Redo logs not saved", "RefUrl": "/notes/174309 "}, {"RefNumber": "422615", "RefComponent": "BC-ABA-LA", "RefTitle": "SLIN: Warning with REFRESH in the OO context", "RefUrl": "/notes/422615 "}, {"RefNumber": "421554", "RefComponent": "BC-DB-DBI", "RefTitle": "R3load: Problems when converting cluster tables", "RefUrl": "/notes/421554 "}, {"RefNumber": "425547", "RefComponent": "BC-DB-SDB", "RefTitle": "SQL error 709 when building a service link", "RefUrl": "/notes/425547 "}, {"RefNumber": "85985", "RefComponent": "BC-DB-INF", "RefTitle": "dbst_enter_exec_stmt: illegal operation type 3", "RefUrl": "/notes/85985 "}, {"RefNumber": "44460", "RefComponent": "BC-DB-INF", "RefTitle": "DBIF_REPO_NO_MEMORY in transaction KEA0", "RefUrl": "/notes/44460 "}, {"RefNumber": "29053", "RefComponent": "BC-DB-INF", "RefTitle": "saplicense problems after 2.1l/2.2F/3.0A upgrade", "RefUrl": "/notes/29053 "}, {"RefNumber": "50773", "RefComponent": "BC-DB-INF", "RefTitle": "0x5c (ASCII '\\') is Default Escape Character", "RefUrl": "/notes/50773 "}, {"RefNumber": "62427", "RefComponent": "BC-DB-INF", "RefTitle": "Bad performance of SELECT .. IN <itab>", "RefUrl": "/notes/62427 "}, {"RefNumber": "84967", "RefComponent": "BC-DB-INF", "RefTitle": "Incorrect values in transactions ST03,STAT,SM50", "RefUrl": "/notes/84967 "}, {"RefNumber": "131093", "RefComponent": "BC-DB-INF-SYS", "RefTitle": "Possible problems with Informix EXEC SQL interface", "RefUrl": "/notes/131093 "}, {"RefNumber": "319783", "RefComponent": "BC-UPG-OCS", "RefTitle": "4.5B SP 13/16: DSYS_LOHEAD_E_CI/T156F entries are missing", "RefUrl": "/notes/319783 "}, {"RefNumber": "121245", "RefComponent": "CA-EUR-CNV", "RefTitle": "System settings for local currency changeover to euro", "RefUrl": "/notes/121245 "}, {"RefNumber": "419783", "RefComponent": "BC-ABA-LA", "RefTitle": "MOVE_DREF_NOT_COMPATIBLE with CREATE for internal table", "RefUrl": "/notes/419783 "}, {"RefNumber": "203881", "RefComponent": "SRM-EBP", "RefTitle": "SAPKU20A05: Support Package 5 for BBP 2.0A/CRM1.2", "RefUrl": "/notes/203881 "}, {"RefNumber": "413750", "RefComponent": "BC-DB-DBI", "RefTitle": "Decompression error CsIni not recognized by R3check", "RefUrl": "/notes/413750 "}, {"RefNumber": "101735", "RefComponent": "BC-INS-UNX", "RefTitle": "Solaris2.6: Inst. Japanese 3.0D and later version", "RefUrl": "/notes/101735 "}, {"RefNumber": "198659", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Release 4.6A/B: Recovering corrupted CDCLS data", "RefUrl": "/notes/198659 "}, {"RefNumber": "101464", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA: Statistic flag(s) ... not reset", "RefUrl": "/notes/101464 "}, {"RefNumber": "409087", "RefComponent": "BC-DB-SDB", "RefTitle": "Importing the 64-bit 45B SAP kernel on HP/UX-SAP DB", "RefUrl": "/notes/409087 "}, {"RefNumber": "373126", "RefComponent": "SRM-EBP", "RefTitle": "SAPKU20B09: Support Package 9 for BBP / CRM 2.0B", "RefUrl": "/notes/373126 "}, {"RefNumber": "307965", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3 trans: Additional data element docu. not transported", "RefUrl": "/notes/307965 "}, {"RefNumber": "184857", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp clearold:ERROR: tpdelsapnentry: unexpected error", "RefUrl": "/notes/184857 "}, {"RefNumber": "357054", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "ST04: Tremination w/ error 270 'C-Call GET_DBPARMS failed'", "RefUrl": "/notes/357054 "}, {"RefNumber": "117197", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: RC 13 during export (R3TR, CDAT...)", "RefUrl": "/notes/117197 "}, {"RefNumber": "116577", "RefComponent": "BC-CTS-TLS", "RefTitle": "Problems after transporting area menus", "RefUrl": "/notes/116577 "}, {"RefNumber": "71251", "RefComponent": "BC-CST-GW", "RefTitle": "Gateway : Versions of dplib are different", "RefUrl": "/notes/71251 "}, {"RefNumber": "189841", "RefComponent": "BC-CTS-TLS", "RefTitle": "'TP clearold' deletes nothing", "RefUrl": "/notes/189841 "}, {"RefNumber": "189101", "RefComponent": "BC-DB-DBI", "RefTitle": "R3check \"ignores\" incorrect cluster record", "RefUrl": "/notes/189101 "}, {"RefNumber": "114564", "RefComponent": "BC-CTS-TLS", "RefTitle": "No table logging for R3trans", "RefUrl": "/notes/114564 "}, {"RefNumber": "372581", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: recclient does not work on Windows", "RefUrl": "/notes/372581 "}, {"RefNumber": "365506", "RefComponent": "BC-CTS-ORG", "RefTitle": "Version administration displays incorrect transport request", "RefUrl": "/notes/365506 "}, {"RefNumber": "399966", "RefComponent": "SV-ASA-ADD-RBE", "RefTitle": "Latest EXE for RBE 2.0", "RefUrl": "/notes/399966 "}, {"RefNumber": "373078", "RefComponent": "BC-CTS-LAN", "RefTitle": "Importing r3trans language transport in Release <= 45B", "RefUrl": "/notes/373078 "}, {"RefNumber": "104215", "RefComponent": "BC-DB-ORA-CCM", "RefTitle": "DB13-DB24: CPIC communication error", "RefUrl": "/notes/104215 "}, {"RefNumber": "322849", "RefComponent": "SRM-EBP", "RefTitle": "SAPKU20B04: Support Package 4 for BBP / CRM 2.0B", "RefUrl": "/notes/322849 "}, {"RefNumber": "371716", "RefComponent": "SRM-EBP", "RefTitle": "SAPKU20B08: Support Package 8 for BBP / CRM 2.0B", "RefUrl": "/notes/371716 "}, {"RefNumber": "335455", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: duplicate key error for key with _", "RefUrl": "/notes/335455 "}, {"RefNumber": "383498", "RefComponent": "BC-DB-SDB", "RefTitle": "CCMS Support for SAPDB V7.2", "RefUrl": "/notes/383498 "}, {"RefNumber": "361244", "RefComponent": "BC-DB-DB4", "RefTitle": "R3load terminates during cluster access on AS/400", "RefUrl": "/notes/361244 "}, {"RefNumber": "375576", "RefComponent": "SCM-APO-PPS", "RefTitle": "Performance when exiting interactive planning", "RefUrl": "/notes/375576 "}, {"RefNumber": "216321", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR programs with version 4.6C", "RefUrl": "/notes/216321 "}, {"RefNumber": "326543", "RefComponent": "BC-DB-INF", "RefTitle": "R3INST/R3SETUP/DB-Migration : Anlegen > 100 Chunks", "RefUrl": "/notes/326543 "}, {"RefNumber": "205074", "RefComponent": "BC-OP-AIX", "RefTitle": "Installation SAP R/3 64-bit kernel for AIX", "RefUrl": "/notes/205074 "}, {"RefNumber": "106412", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Conversion of tables EDIDOC & CDCLS, Release 4.0B", "RefUrl": "/notes/106412 "}, {"RefNumber": "92665", "RefComponent": "BC-UPG", "RefTitle": "Upgrade to 4.0A/B: screen texts are deleted", "RefUrl": "/notes/92665 "}, {"RefNumber": "152502", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp createcofile: R3trans termin. wth return code 16", "RefUrl": "/notes/152502 "}, {"RefNumber": "128589", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgrade to 4.0B/4.5A destroys customer variants", "RefUrl": "/notes/128589 "}, {"RefNumber": "304547", "RefComponent": "BC-OP-TRU64", "RefTitle": "SICK error on Tru64 5.x", "RefUrl": "/notes/304547 "}, {"RefNumber": "188592", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgrade Release 4.6B: Termination in the case of inactive RE", "RefUrl": "/notes/188592 "}, {"RefNumber": "205467", "RefComponent": "BC-DB-LVC", "RefTitle": "-1002 during access to liveCache, Tru64 -> WIN-NT", "RefUrl": "/notes/205467 "}, {"RefNumber": "188733", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp: SPAM starts the main import in scenario 'T'", "RefUrl": "/notes/188733 "}, {"RefNumber": "143288", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA: Super collective note", "RefUrl": "/notes/143288 "}, {"RefNumber": "197714", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgr. 4.6B: Termination phase SHADOWIMP or DIFFEXP*", "RefUrl": "/notes/197714 "}, {"RefNumber": "207474", "RefComponent": "BC-CTS-TLS", "RefTitle": "TP termination if target system group entries > 50", "RefUrl": "/notes/207474 "}, {"RefNumber": "198604", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "4.6A/B: Deletion of corrupt CDCLS data", "RefUrl": "/notes/198604 "}, {"RefNumber": "187645", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR programs version 4.6B", "RefUrl": "/notes/187645 "}, {"RefNumber": "327290", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgrade 46B/46C:R3trans termintn phase PATCH_CMDIMP", "RefUrl": "/notes/327290 "}, {"RefNumber": "214097", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgrade 4.6C FCS: Termination in phase SHADOW_IMPORT*", "RefUrl": "/notes/214097 "}, {"RefNumber": "357365", "RefComponent": "BC-DB-SDB-SYS", "RefTitle": "SQL statement with more than 290 host variables", "RefUrl": "/notes/357365 "}, {"RefNumber": "93452", "RefComponent": "BC-CTS-TMS", "RefTitle": "Impossible to delete transport request in TMS", "RefUrl": "/notes/93452 "}, {"RefNumber": "169399", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR programs 4.6A version", "RefUrl": "/notes/169399 "}, {"RefNumber": "133168", "RefComponent": "BC-CTS-TLS", "RefTitle": "Menus not active after transport", "RefUrl": "/notes/133168 "}, {"RefNumber": "169326", "RefComponent": "BC-DB-DBI", "RefTitle": "Incorrect bind list when accessing cluster tables", "RefUrl": "/notes/169326 "}, {"RefNumber": "318825", "RefComponent": "BC-CTS-TLS", "RefTitle": "Delivery of translations w/o deletions", "RefUrl": "/notes/318825 "}, {"RefNumber": "124607", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR programs version 4.0B", "RefUrl": "/notes/124607 "}, {"RefNumber": "318533", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Transport of classes/interfaces w/o docu", "RefUrl": "/notes/318533 "}, {"RefNumber": "120300", "RefComponent": "BC-SEC-AUT", "RefTitle": "Missing authorizations for profile parameter >2730", "RefUrl": "/notes/120300 "}, {"RefNumber": "311409", "RefComponent": "BW", "RefTitle": "Short dump CNTL_ERROR after RFC into source system", "RefUrl": "/notes/311409 "}, {"RefNumber": "313866", "RefComponent": "BC-UPG-OCS", "RefTitle": "Deleted texts table DSYAT after LCP import", "RefUrl": "/notes/313866 "}, {"RefNumber": "309587", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp: test import does not work for target groups", "RefUrl": "/notes/309587 "}, {"RefNumber": "212597", "RefComponent": "BC-DWB-CEX", "RefTitle": "SPAU/SE95: Incorrect names for indexes, messages", "RefUrl": "/notes/212597 "}, {"RefNumber": "304444", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Incomplete transport R3TR TOBJ", "RefUrl": "/notes/304444 "}, {"RefNumber": "97633", "RefComponent": "BC-UPG-OCS", "RefTitle": "@1B@OCS Help: SPAM Step DISASSEMBLE", "RefUrl": "/notes/97633 "}, {"RefNumber": "192386", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA: Missing CBO Statistics in check after Year 2000", "RefUrl": "/notes/192386 "}, {"RefNumber": "153229", "RefComponent": "BC-DB-LVC", "RefTitle": "APO liveCache patch for Support Package 1", "RefUrl": "/notes/153229 "}, {"RefNumber": "302542", "RefComponent": "BC-ABA-SC", "RefTitle": "No subarea assigned", "RefUrl": "/notes/302542 "}, {"RefNumber": "119207", "RefComponent": "BC-UPG-TLS", "RefTitle": "Phase DBCHK in the 45A BW upgrade fails", "RefUrl": "/notes/119207 "}, {"RefNumber": "115372", "RefComponent": "BC-UPG-OCS", "RefTitle": "Patches forwarded by mistake to target system", "RefUrl": "/notes/115372 "}, {"RefNumber": "76181", "RefComponent": "BC-UPG-OCS", "RefTitle": "R3trans signal 11 in DISASSEMBLE_PATCH", "RefUrl": "/notes/76181 "}, {"RefNumber": "177735", "RefComponent": "BC-CTS-TLS", "RefTitle": "TP mvstopmark creates unremovable STOPMARK", "RefUrl": "/notes/177735 "}, {"RefNumber": "174110", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp check> sapname file .. contains invalid data", "RefUrl": "/notes/174110 "}, {"RefNumber": "165332", "RefComponent": "BC-CTS-TLS", "RefTitle": "46A FCS tp appendbuffer deletes SYNCMARKs", "RefUrl": "/notes/165332 "}, {"RefNumber": "155350", "RefComponent": "BC-CTS-TLS", "RefTitle": "A syntax description of tp version numbers", "RefUrl": "/notes/155350 "}, {"RefNumber": "155328", "RefComponent": "BC-CTS-TLS", "RefTitle": "ERROR: SETSYNCMARK is not supported with CTC=TRUE", "RefUrl": "/notes/155328 "}, {"RefNumber": "146773", "RefComponent": "BC-CTS-TLS", "RefTitle": "PANIC: creation of shadow class XX failed with rc 0", "RefUrl": "/notes/146773 "}, {"RefNumber": "144062", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans generates INF-201 during import with CSI=1", "RefUrl": "/notes/144062 "}, {"RefNumber": "136708", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp ends with return code 222", "RefUrl": "/notes/136708 "}, {"RefNumber": "132383", "RefComponent": "BC-CTS-TLS", "RefTitle": "Tp terminates if it is started by SPAM", "RefUrl": "/notes/132383 "}, {"RefNumber": "128834", "RefComponent": "BC-CTS-TLS", "RefTitle": "Order release reports RFCIO_ERROR_NOHANDLE", "RefUrl": "/notes/128834 "}, {"RefNumber": "123307", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans RC=13 for CREATECOFILE", "RefUrl": "/notes/123307 "}, {"RefNumber": "119204", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp: STOPMARKs are deleted with import all U0", "RefUrl": "/notes/119204 "}, {"RefNumber": "114075", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp cannot be started via RFC", "RefUrl": "/notes/114075 "}, {"RefNumber": "111341", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp: buffer file corrupt", "RefUrl": "/notes/111341 "}, {"RefNumber": "108995", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp does not supply all supply systems", "RefUrl": "/notes/108995 "}, {"RefNumber": "103305", "RefComponent": "BC-CTS-TLS", "RefTitle": "Upgrade 4.0A/4.0B error in a DIFFEXP* phase", "RefUrl": "/notes/103305 "}, {"RefNumber": "102236", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp: Defective import buffer after Hot Package", "RefUrl": "/notes/102236 "}, {"RefNumber": "101437", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp imports transports into incorrect clients", "RefUrl": "/notes/101437 "}, {"RefNumber": "91383", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp exits with return code 224", "RefUrl": "/notes/91383 "}, {"RefNumber": "91382", "RefComponent": "BC-CTS-TLS", "RefTitle": "trfunction 'W': object type LIMUVARX not allowed", "RefUrl": "/notes/91382 "}, {"RefNumber": "82708", "RefComponent": "BC-CTS", "RefTitle": "R3trans converts report texts incompletely", "RefUrl": "/notes/82708 "}, {"RefNumber": "82546", "RefComponent": "BC-CTS", "RefTitle": "R3trans does not take all INCLUDEs from FUGR", "RefUrl": "/notes/82546 "}, {"RefNumber": "82343", "RefComponent": "BC-CTS", "RefTitle": "RStrans:EM during exprt 'TW135 illegal selection..'", "RefUrl": "/notes/82343 "}, {"RefNumber": "75270", "RefComponent": "BC-CTS", "RefTitle": "R3trans: unexpected return code 16 on INS call", "RefUrl": "/notes/75270 "}, {"RefNumber": "66456", "RefComponent": "BC-CTS", "RefTitle": "Test import in source instead of target system", "RefUrl": "/notes/66456 "}, {"RefNumber": "188730", "RefComponent": "BC-CTS-TLS", "RefTitle": "TP: TMS indicates many requests as already imported", "RefUrl": "/notes/188730 "}, {"RefNumber": "123089", "RefComponent": "BC-ABA-SC", "RefTitle": "Status MEN of the user interface MENU is missing", "RefUrl": "/notes/123089 "}, {"RefNumber": "216176", "RefComponent": "BC-ABA-LA", "RefTitle": "RUNT_ILLEGAL_SWITCH with DESCRIBE ... INTO ...", "RefUrl": "/notes/216176 "}, {"RefNumber": "130880", "RefComponent": "BC-SEC", "RefTitle": "Initial menu not active after changing password", "RefUrl": "/notes/130880 "}, {"RefNumber": "124606", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR programs version 3.1I", "RefUrl": "/notes/124606 "}, {"RefNumber": "214434", "RefComponent": "BC-CTS-TLS", "RefTitle": "TP impsync: no stop although transport is required", "RefUrl": "/notes/214434 "}, {"RefNumber": "124608", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR programs version 4.5A", "RefUrl": "/notes/124608 "}, {"RefNumber": "76604", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 3.1H", "RefUrl": "/notes/76604 "}, {"RefNumber": "209652", "RefComponent": "BC-CTS-TLS", "RefTitle": "Buffer synchronization - tp locksys or tp unlocksys", "RefUrl": "/notes/209652 "}, {"RefNumber": "201816", "RefComponent": "BC-CTS-TMS", "RefTitle": "<PERSON><PERSON><PERSON>uffer does not terminate for tasks", "RefUrl": "/notes/201816 "}, {"RefNumber": "138806", "RefComponent": "FI", "RefTitle": "Asterisk(*) not working for balancing journal entry", "RefUrl": "/notes/138806 "}, {"RefNumber": "206168", "RefComponent": "BC-UPG-TLS", "RefTitle": "R3trans termination in upgrade phase DIFFEXPDOCU", "RefUrl": "/notes/206168 "}, {"RefNumber": "102308", "RefComponent": "BC-SEC-AUT", "RefTitle": "Exclusive lock waits on table USRBF", "RefUrl": "/notes/102308 "}, {"RefNumber": "198890", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp: error in export if nbufform=0", "RefUrl": "/notes/198890 "}, {"RefNumber": "200504", "RefComponent": "BC-DB-DBI", "RefTitle": "R3load hangs during import", "RefUrl": "/notes/200504 "}, {"RefNumber": "200647", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Different processing of requests", "RefUrl": "/notes/200647 "}, {"RefNumber": "143589", "RefComponent": "BC-INS-UNX", "RefTitle": "Solaris 7 : Inst. Japanese 3.0D and later version", "RefUrl": "/notes/143589 "}, {"RefNumber": "198216", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: R3TR FUGX deleted although screens exist", "RefUrl": "/notes/198216 "}, {"RefNumber": "61994", "RefComponent": "BC-INS-NT", "RefTitle": "INST: 3.1G  R/3 Inst. on Windows NT ORACLE DB", "RefUrl": "/notes/61994 "}, {"RefNumber": "202639", "RefComponent": "BC-DB-DBI", "RefTitle": "Work process terminates during access to cluster table", "RefUrl": "/notes/202639 "}, {"RefNumber": "194483", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans(CSI=1): does not delete table entries", "RefUrl": "/notes/194483 "}, {"RefNumber": "142427", "RefComponent": "BC-DB-DBI", "RefTitle": "DB_SETGET delivers incorrect return code", "RefUrl": "/notes/142427 "}, {"RefNumber": "191137", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp: Error when importing with CTC=1 and unmode 0", "RefUrl": "/notes/191137 "}, {"RefNumber": "163538", "RefComponent": "BC-ABA-LA", "RefTitle": "Type conflicts f.internal tables creatd dynamically", "RefUrl": "/notes/163538 "}, {"RefNumber": "191300", "RefComponent": "BC-ABA-SC", "RefTitle": "BDC log: date / time always 01/Jan/1970 1:00", "RefUrl": "/notes/191300 "}, {"RefNumber": "185998", "RefComponent": "BC-DB-DBI", "RefTitle": "SELECT SINGLE on a logical cluster table", "RefUrl": "/notes/185998 "}, {"RefNumber": "182667", "RefComponent": "BC-ABA-LA", "RefTitle": "READ/LOOP on SORTED TABLE type tables", "RefUrl": "/notes/182667 "}, {"RefNumber": "185709", "RefComponent": "BC-CTS-TLS", "RefTitle": "ORA-903 during import in table w. namespce prefix", "RefUrl": "/notes/185709 "}, {"RefNumber": "91934", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA: -analyze DBSTATCO delivers Returncode>0", "RefUrl": "/notes/91934 "}, {"RefNumber": "185317", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp:ERR:Not exactly 1 SYNCMARK set;buffer unchanged", "RefUrl": "/notes/185317 "}, {"RefNumber": "136482", "RefComponent": "BC-ABA-SC", "RefTitle": "Missing documents when updating in batch input", "RefUrl": "/notes/136482 "}, {"RefNumber": "154912", "RefComponent": "BC", "RefTitle": "Year 2000 information for 3.1I and 4.0B kernels", "RefUrl": "/notes/154912 "}, {"RefNumber": "183367", "RefComponent": "XX-SER-LAS", "RefTitle": "Measurement of concurrent users: no RFCs", "RefUrl": "/notes/183367 "}, {"RefNumber": "183504", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp forgets unconditional mode for \"import some\"", "RefUrl": "/notes/183504 "}, {"RefNumber": "178233", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp stops on signal 11 during Main Import with CTC=1", "RefUrl": "/notes/178233 "}, {"RefNumber": "176669", "RefComponent": "BC-ABA-LA", "RefTitle": "Mode termination for \"DESCRIBE FIELD f INTO td\"", "RefUrl": "/notes/176669 "}, {"RefNumber": "143708", "RefComponent": "BC-ABA-SC", "RefTitle": "ABAP runtime error during batch input", "RefUrl": "/notes/143708 "}, {"RefNumber": "119350", "RefComponent": "BC-ABA-SC", "RefTitle": "Problems during the local update", "RefUrl": "/notes/119350 "}, {"RefNumber": "47968", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA: Export/import error after restart on NT", "RefUrl": "/notes/47968 "}, {"RefNumber": "44395", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA: Missing indexes after reorg run terminates", "RefUrl": "/notes/44395 "}, {"RefNumber": "172032", "RefComponent": "BW-SYS", "RefTitle": "New processing of transport objects ISFS and ISMP", "RefUrl": "/notes/172032 "}, {"RefNumber": "153440", "RefComponent": "BC-ABA-SC", "RefTitle": "PF4/OCX: Deadlock after PF4/PF1 in subscreen", "RefUrl": "/notes/153440 "}, {"RefNumber": "127300", "RefComponent": "BC-ABA-SC", "RefTitle": "Screen standard size during batch input", "RefUrl": "/notes/127300 "}, {"RefNumber": "164958", "RefComponent": "BC-DB-LVC", "RefTitle": "Incorrect Connection when you call a DB procedure", "RefUrl": "/notes/164958 "}, {"RefNumber": "162395", "RefComponent": "BC-UPG-RDM", "RefTitle": "Upgrade 4.0B, TABIMP: duplicate key error T51T3", "RefUrl": "/notes/162395 "}, {"RefNumber": "110968", "RefComponent": "BC-ABA-SC", "RefTitle": "SAPgui character problems after 3.1I upgrade", "RefUrl": "/notes/110968 "}, {"RefNumber": "164628", "RefComponent": "BC-ABA-LA", "RefTitle": "Syntax error in the LOOP during a table exchange", "RefUrl": "/notes/164628 "}, {"RefNumber": "123370", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA: sapdba -check reports REDOLOG_NOT_MIRRORED", "RefUrl": "/notes/123370 "}, {"RefNumber": "158306", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: New CCMS functions for 4.0B spt rel 1", "RefUrl": "/notes/158306 "}, {"RefNumber": "105474", "RefComponent": "BC-UPG-OCS", "RefTitle": "@3B@Documentation Hot Package Collections to 3.1I", "RefUrl": "/notes/105474 "}, {"RefNumber": "104127", "RefComponent": "BC-UPG-OCS", "RefTitle": "@3B@Documentation Hot Package Collections to 4.0B", "RefUrl": "/notes/104127 "}, {"RefNumber": "154988", "RefComponent": "BC-DB-DBI", "RefTitle": "INF391 during table import using tp", "RefUrl": "/notes/154988 "}, {"RefNumber": "96227", "RefComponent": "BC-ABA-SC", "RefTitle": "Problems with dialog boxes for exiting or deleting", "RefUrl": "/notes/96227 "}, {"RefNumber": "156929", "RefComponent": "BC-ABA-LA", "RefTitle": "40B / kernel patch level 404 - generation error", "RefUrl": "/notes/156929 "}, {"RefNumber": "154382", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Missing documentation for R3TR SOBJ", "RefUrl": "/notes/154382 "}, {"RefNumber": "152059", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp: Extnded trnsprt ctrl -> Import in source system", "RefUrl": "/notes/152059 "}, {"RefNumber": "112357", "RefComponent": "CA-CAD", "RefTitle": "CAD/RFC CadRfcGetDmsData no return values", "RefUrl": "/notes/112357 "}, {"RefNumber": "121105", "RefComponent": "BC-DB-DBI", "RefTitle": "Duplicate records in table logging", "RefUrl": "/notes/121105 "}, {"RefNumber": "104128", "RefComponent": "BC-UPG-OCS", "RefTitle": "@3B@Documentation LCP Collections to 4.0B", "RefUrl": "/notes/104128 "}, {"RefNumber": "105475", "RefComponent": "BC-UPG-OCS", "RefTitle": "@3B@Documentation LCP Collections to 3.1I", "RefUrl": "/notes/105475 "}, {"RefNumber": "145039", "RefComponent": "BC-CTS-TLS", "RefTitle": "Table delivery class L contents do not arrive", "RefUrl": "/notes/145039 "}, {"RefNumber": "61042", "RefComponent": "BC-ABA-LA", "RefTitle": "Elimination of a minimal incompatibility", "RefUrl": "/notes/61042 "}, {"RefNumber": "107434", "RefComponent": "BC-ABA-SC", "RefTitle": "Field <name> is not available in the <screen>", "RefUrl": "/notes/107434 "}, {"RefNumber": "143665", "RefComponent": "XX-PART-BEV", "RefTitle": "Multiple returns order from an extension request", "RefUrl": "/notes/143665 "}, {"RefNumber": "124430", "RefComponent": "CA-CAD-LIB", "RefTitle": "CAD - Shared libraries", "RefUrl": "/notes/124430 "}, {"RefNumber": "133970", "RefComponent": "BW-SYS", "RefTitle": "R3trans & BW: Treatment of objects ISMT ISTS ISTT RSIC", "RefUrl": "/notes/133970 "}, {"RefNumber": "142268", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: E070C-TARCLIENT is not filled", "RefUrl": "/notes/142268 "}, {"RefNumber": "94911", "RefComponent": "BC-ABA-SC", "RefTitle": "Batch input: Double documents after update RC 255", "RefUrl": "/notes/94911 "}, {"RefNumber": "72959", "RefComponent": "BC-INS-MIG", "RefTitle": "3.1G R3INST: Homogeneous migration of an Informix db", "RefUrl": "/notes/72959 "}, {"RefNumber": "72958", "RefComponent": "BC-INS-MIG", "RefTitle": "3.0F R3INST: Homogeneous migrtn of an Informix db", "RefUrl": "/notes/72958 "}, {"RefNumber": "63297", "RefComponent": "BC-DB-INF", "RefTitle": "sapservx: Installation/DBCOPY/Migration tools", "RefUrl": "/notes/63297 "}, {"RefNumber": "73782", "RefComponent": "BC-DB-INF", "RefTitle": "DBCOPY: 3.1G DBCOPY on UNIX - Informix Database", "RefUrl": "/notes/73782 "}, {"RefNumber": "70626", "RefComponent": "BC-DB-INF", "RefTitle": "DBCOPY: 3.0F DBCOPY on UNIX - Informix Database", "RefUrl": "/notes/70626 "}, {"RefNumber": "67706", "RefComponent": "BC-DB-INF", "RefTitle": "DBCOPY: 3.0E DBCOPY on UNIX - Informix Database", "RefUrl": "/notes/67706 "}, {"RefNumber": "138625", "RefComponent": "BC-DB-DBI", "RefTitle": "Cluster buffer \"shrunk\"", "RefUrl": "/notes/138625 "}, {"RefNumber": "138201", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans:segmentation fault core dumped HP-UX 11.0", "RefUrl": "/notes/138201 "}, {"RefNumber": "102231", "RefComponent": "BC-ABA-LA", "RefTitle": "PXA_DESTROYED under DEC-UNIX", "RefUrl": "/notes/102231 "}, {"RefNumber": "78448", "RefComponent": "BC-ABA-SC", "RefTitle": "Advance installation of batch input recording", "RefUrl": "/notes/78448 "}, {"RefNumber": "127582", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "CBO: UPGRADE 4.0/4.5 (reduction of runtime)", "RefUrl": "/notes/127582 "}, {"RefNumber": "109059", "RefComponent": "BC-UPG-TLS", "RefTitle": "Customer-specific matchcodes destroyed after 4.0B upgrade", "RefUrl": "/notes/109059 "}, {"RefNumber": "134478", "RefComponent": "CA-CAD", "RefTitle": "CAD - DVA via SapRouter", "RefUrl": "/notes/134478 "}, {"RefNumber": "32926", "RefComponent": "BC-UPG", "RefTitle": "3.0 Upgrade: Customer rpt. wth new d. of change", "RefUrl": "/notes/32926 "}, {"RefNumber": "121120", "RefComponent": "BC-ABA-SC", "RefTitle": "Termination of SAPgui when displ. table controls", "RefUrl": "/notes/121120 "}, {"RefNumber": "130883", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Constant MAX_STATUS_CNT is too short", "RefUrl": "/notes/130883 "}, {"RefNumber": "126765", "RefComponent": "BC-CTS-TLS", "RefTitle": "Import logs after parallel import in the background", "RefUrl": "/notes/126765 "}, {"RefNumber": "123996", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp/R3trans: Database trace is written to stdout", "RefUrl": "/notes/123996 "}, {"RefNumber": "90961", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA: ORA-01403 for SAPDBA analysis", "RefUrl": "/notes/90961 "}, {"RefNumber": "50036", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA: Splitting large export dumps with R3chop", "RefUrl": "/notes/50036 "}, {"RefNumber": "110989", "RefComponent": "BC-ABA-SC", "RefTitle": "Problems in matchcode selection in background-input", "RefUrl": "/notes/110989 "}, {"RefNumber": "113739", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "DB14: No LOG-INFO available for various DBA ops.", "RefUrl": "/notes/113739 "}, {"RefNumber": "105959", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA: Message \"Opt. Statistics for Views\"", "RefUrl": "/notes/105959 "}, {"RefNumber": "100974", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA: Prg. ex. after func. 'Create missing stats'", "RefUrl": "/notes/100974 "}, {"RefNumber": "96272", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA: Error with -analyze during reorganization", "RefUrl": "/notes/96272 "}, {"RefNumber": "74809", "RefComponent": "CA-CAD", "RefTitle": "Check in/out with dialog user (dialog interface)", "RefUrl": "/notes/74809 "}, {"RefNumber": "112498", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA: -checkopt PSAP% does not store new tables in DBSTATC", "RefUrl": "/notes/112498 "}, {"RefNumber": "118582", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Transport of R3TRPROG deletes variants", "RefUrl": "/notes/118582 "}, {"RefNumber": "121551", "RefComponent": "BC-ABA-SC", "RefTitle": "Field <name> (is not an input field)", "RefUrl": "/notes/121551 "}, {"RefNumber": "51724", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA: Recovery of a off-line file", "RefUrl": "/notes/51724 "}, {"RefNumber": "45928", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA: Reorg. run on NT (missing table contents)", "RefUrl": "/notes/45928 "}, {"RefNumber": "122548", "RefComponent": "BC-UPG", "RefTitle": "\"@2V@CD Rel. 3.1I/4.0B, SAP KERNEL PATCH\" Oct '98", "RefUrl": "/notes/122548 "}, {"RefNumber": "119617", "RefComponent": "BC-ABA-SC", "RefTitle": "Termination with queue error in batch input", "RefUrl": "/notes/119617 "}, {"RefNumber": "112196", "RefComponent": "BC-UPG", "RefTitle": "\"@2V@CD Rel. 3.1I/4.0B, SAP KERNEL PATCH\" Aug '98", "RefUrl": "/notes/112196 "}, {"RefNumber": "120010", "RefComponent": "BC-SEC", "RefTitle": "Password in the logon screen", "RefUrl": "/notes/120010 "}, {"RefNumber": "120008", "RefComponent": "BC-SEC", "RefTitle": "Password in ABAP short dumps", "RefUrl": "/notes/120008 "}, {"RefNumber": "120006", "RefComponent": "BC-SEC", "RefTitle": "RFC logon: USR41 entries are not deleted", "RefUrl": "/notes/120006 "}, {"RefNumber": "118736", "RefComponent": "BC-SEC-AUT", "RefTitle": "Autho. check in transaction cannot be switched off", "RefUrl": "/notes/118736 "}, {"RefNumber": "115841", "RefComponent": "BC-DWB-SEM", "RefTitle": "Transaction is unknown in Session Manager", "RefUrl": "/notes/115841 "}, {"RefNumber": "117859", "RefComponent": "BC-ABA-LA", "RefTitle": "Non-represent.charact. after INSERT in intern.table", "RefUrl": "/notes/117859 "}, {"RefNumber": "116768", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Te<PERSON><PERSON>. \"use devclass\", devclass > 4", "RefUrl": "/notes/116768 "}, {"RefNumber": "103592", "RefComponent": "BC-DB-DBI", "RefTitle": "31I tp does not perform buffer synchronization", "RefUrl": "/notes/103592 "}, {"RefNumber": "114077", "RefComponent": "CA-CAD", "RefTitle": "CAD interface under NT Alpha", "RefUrl": "/notes/114077 "}, {"RefNumber": "109705", "RefComponent": "BC-CTS-TLS", "RefTitle": "tp: Import is not repeated in spite of umode 1", "RefUrl": "/notes/109705 "}, {"RefNumber": "102233", "RefComponent": "BC-DB-DBI", "RefTitle": "3.1X/4.0A: Short dump when calling ST04", "RefUrl": "/notes/102233 "}, {"RefNumber": "82818", "RefComponent": "BC-ABA-SC", "RefTitle": "No possible entries pushbutton", "RefUrl": "/notes/82818 "}, {"RefNumber": "91704", "RefComponent": "BC-INS-NT", "RefTitle": "Inst: 4.0A R/3 Installation on Windows NT: ORACLE", "RefUrl": "/notes/91704 "}, {"RefNumber": "28913", "RefComponent": "BC-ABA-SC", "RefTitle": "LOOP in batch-input", "RefUrl": "/notes/28913 "}, {"RefNumber": "102333", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Incomplete export of function group", "RefUrl": "/notes/102333 "}, {"RefNumber": "88401", "RefComponent": "BC-UPG", "RefTitle": "@2V@CD \"Release 3.1H, SAP KERNEL PATCH\" (12/97)", "RefUrl": "/notes/88401 "}, {"RefNumber": "98787", "RefComponent": "BC-UPG", "RefTitle": "@2V@Release 3.1H, SAP KERNEL PATCH, March '98", "RefUrl": "/notes/98787 "}, {"RefNumber": "101854", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: SQL-302 during import of DD30T entries", "RefUrl": "/notes/101854 "}, {"RefNumber": "102645", "RefComponent": "BC-UPG", "RefTitle": "Upgrade 4.0A->4.0B: termination in DIFFEXP* phases", "RefUrl": "/notes/102645 "}, {"RefNumber": "96019", "RefComponent": "BC-SEC-AUT", "RefTitle": "Authorization check cannot be deactivated", "RefUrl": "/notes/96019 "}, {"RefNumber": "76465", "RefComponent": "BC-SEC-AUT", "RefTitle": "Jobs: No more memory in the SHORT class", "RefUrl": "/notes/76465 "}, {"RefNumber": "80212", "RefComponent": "BC-INS-NT", "RefTitle": "Inst: 3.1H  R/3 Inst. on Windows NT: DB2/CS", "RefUrl": "/notes/80212 "}, {"RefNumber": "45782", "RefComponent": "BC-ABA-LA", "RefTitle": "Runtime error GETWA_NOT_ALLOC for LOOP (extract)", "RefUrl": "/notes/45782 "}, {"RefNumber": "90356", "RefComponent": "BC-ABA-SC", "RefTitle": "Message in dialog box with flow of a batch input", "RefUrl": "/notes/90356 "}, {"RefNumber": "90369", "RefComponent": "BC-ABA-LA", "RefTitle": "RUNT_ILLEGAL_SWITCH in ab_tgetupd()", "RefUrl": "/notes/90369 "}, {"RefNumber": "88452", "RefComponent": "BC-UPG-OCS", "RefTitle": "CANNOT_DISASSEMBLE_R_DATA_FILE", "RefUrl": "/notes/88452 "}, {"RefNumber": "76948", "RefComponent": "BC-CTS", "RefTitle": "R3trans: <PERSON><PERSON><PERSON> failed (twglobal.c, 545)", "RefUrl": "/notes/76948 "}, {"RefNumber": "84639", "RefComponent": "BC-CTS", "RefTitle": "R3trans warning for import (table E071KF)", "RefUrl": "/notes/84639 "}, {"RefNumber": "67452", "RefComponent": "PP", "RefTitle": "Duplicated postings and terminations !!!!!!!!!!!", "RefUrl": "/notes/67452 "}, {"RefNumber": "86537", "RefComponent": "BC-ABA", "RefTitle": "Right to left (Hebrew) printing for AS/400 and NT", "RefUrl": "/notes/86537 "}, {"RefNumber": "81451", "RefComponent": "BC-ABA-SC", "RefTitle": "Processing batch input: endless loop.", "RefUrl": "/notes/81451 "}, {"RefNumber": "20635", "RefComponent": "BC-OP-TRU64", "RefTitle": "PXA_DESTROYED under OSF/1 (DEC-UNIX)", "RefUrl": "/notes/20635 "}, {"RefNumber": "74411", "RefComponent": "BC-DB-DBI", "RefTitle": "APQI entries only visible after COMMIT", "RefUrl": "/notes/74411 "}, {"RefNumber": "77622", "RefComponent": "BC-ABA", "RefTitle": "Right to left (Hebrew) printing does not work 3.0F", "RefUrl": "/notes/77622 "}, {"RefNumber": "63505", "RefComponent": "LO-MD-MM", "RefTitle": "Program termination in material master / materials created t", "RefUrl": "/notes/63505 "}, {"RefNumber": "61166", "RefComponent": "BC-DB-DBI", "RefTitle": "CONVT_NO_NUMBER in SQL trace display", "RefUrl": "/notes/61166 "}, {"RefNumber": "61865", "RefComponent": "BC-DB-DBI", "RefTitle": "DBIF_RSQL_INTERNAL_ERROR - RC 6 in DbSl", "RefUrl": "/notes/61865 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}