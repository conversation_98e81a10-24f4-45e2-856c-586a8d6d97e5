{"Request": {"Number": "91488", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 315, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014537602017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000091488?language=E&token=A870B1DA1E1C20218D5B398615283682"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000091488", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000091488/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "91488"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 190}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Documentation error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.12.2023"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SER"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Support Services"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Support Services", "value": "SV-SMG-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "91488 - SAP Support Services - Central preparatory note"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You need to prepare a system for which an SAP Support Service has been scheduled.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP Support Services, Safeguarding, Empowering, Standard Support<br />SDCC, RTCCTOOL, ST14, SQLR, ST-PI, ST-A/PI, SQL Trace, ABAP Trace,<br />SAP Solution Manager, Service Preparation<br />SAP GoingLive Check, SAP GoingLive Functional Upgrade Check, SAP OS/DB Migration Service, SAP EarlyWatch Alert, SAP EarlyWatch Service,&#160;<br />SAP Business Process Performance Optimization</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This note provides you with a central overview of the preparation steps required SAP Support Service sessions for ABAP stacks.</p>\r\n<p>For systems with Java stacks see&#160; <strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/1498779\">1498779</a></strong>&#160;<strong>Preparing Java-based systems for Support Services</strong><br />Appendix A provides an overview of SAP Notes with recommendations specific to products such as BW, SCM etc.<br />Appendix B provides an overview of SAP Notes with further recommendations, specific to service types.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Services</strong><br />An overview of support offerings and related service types is at <a target=\"_blank\" href=\"https://support.sap.com/en/offerings-programs.html\">https://support.sap.com/en/offerings-programs.html</a></p>\r\n<ul>\r\n<li>for services delivered<strong> without SAP Solution Manager:</strong></li>\r\n</ul>\r\n<p><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/2899458\">2899458</a>&#160;</strong>provides an overview of services which can be delivered without SAP Solution Manager and the technical requirements of such services.</p>\r\n<p>Be aware that recent EWA data for the relevant system must be available to SAP for such sessions.&#160;Refer to the note for more details.</p>\r\n<ul>\r\n<li>for service delivered using <strong>SAP Solution Manager </strong>systems:</li>\r\n</ul>\r\n<p><strong>SAP Note&#160;<a target=\"_blank\" href=\"https://me.sap.com/notes/2253047\">2253047</a>&#160;&#160;</strong>describes the technical prerequisites required to ensure your SAP Solution Manager 7.2 system is ready to deliver services.</p>\r\n<p><br /><strong>Managed systems</strong><br />To ensure that SAP can deliver a service for each SAP System that you wish to analyze the prerequisites listed below must be met.</p>\r\n<p><strong>Timing</strong><br />To ensure that sufficient data for meaningful analysis is available during the session, the preparation steps listed below should be checked ca 4-6 weeks before the agreed session date. This ensures that any problems can be discovered and resolved in such a way that sufficient data will be available on the day of the session.</p>\r\n<p>Lead time is generally&#160;three weeks before requested date, five weeks if Solution Manager is not ready for delivery. For Golive / Upgrade / Migration, it is three weeks before the requested Analysis session date.</p>\r\n<p>Please&#160;be&#160;sure&#160;to book services as early as possible to leave enough time for Technical Preparation and Delivery.</p>\r\n<p><br /><br /><strong>Prepare Step, Questionnaires, Quick Sizer</strong></p>\r\n<p>Some services (usually Analysis sessions) require a <strong>Prepare step</strong> to be completed by the customer.&#160; This is to provide/confirm various information related to the session. (Hardware/Sizing etc.)</p>\r\n<p>For sessions performed <strong>without SAP Solution Manager:</strong></p>\r\n<p>the customer contact entered during the booking of the session should automatically get an email with a link to the prepare step. An SAP contact such as TQM or ESA (if present) gets a copy of the mail.</p>\r\n<p>For sessions performed with <strong>SAP Solution Manager:</strong></p>\r\n<p>find the session in your SAP Solution Manager, open the prepare step and complete it.&#160; SAP Note<span style=\"font-family: Arial; background-color: #eff4fa;\">&#160; <a target=\"_blank\" href=\"https://me.sap.com/notes/2478967\">2478967&#160;</a> explains how to find the session.&#160;</span></p>\r\n<p>For some service sessions <strong>questionnaires</strong> need to be filled in. Part of the analysis for such sessions is based on the information in the questionnaires, so it is important that these are filled in accurately. They should be returned ideally 1-2 weeks prior to the agreed session date so that any open questions can be adressed prior to the start of the actual service session.<br /><br />The <strong>Quick Sizer</strong> is a web based tool designed to assist with the sizing of SAP Applications. It has been developed by SAP in close cooperation with all platform partners and is free of cost.<br />For some sessions a Quick Sizer project is needed to contribute to the planned analysis. As with the questionnaires the project should be created and filled ideally at least 1-2 weeks prior to the session so that any open questions can be addressed in good time before the session starts.<br />For more information see&#160;<a target=\"_blank\" href=\"https://www.sap.com/about/benchmark/sizing.quick-sizer.html#quick-sizer\">https://www.sap.com/about/benchmark/sizing.quick-sizer.html#quick-sizer</a><br />Where questionnaires or Quick Sizer projects are needed for a session you will be advised of this when you arrange the session date.</p>\r\n<p><br /><br /><strong>EarlyWatch Alert</strong><br /><strong>EarlyWatch Alert</strong> is required for delivery of all run phase remote services, such as EarlyWatch Check, GoingLive Verification etc.<br />It is a free service for the preventative monitoring of the performance of SAP Systems which automatically and regularly collects data on your SAP system. As it provides you with continuous service reports it considerably reduces the risk of system bottlenecks or failures.<br /><br />You should activate EarlyWatch Alert for all productive SAP systems.<br /><br />For further information please review the information provided at&#160;<a target=\"_blank\" href=\"https://support.sap.com/support-programs-services/services/earlywatch-alert.html\">support.sap.com/support-programs-services/services/earlywatch-alert.html </a>as well as SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/1257308\">1257308</a>.</p>\r\n<p><br /><br /><strong>Common steps in ABAP stacks</strong><br /><br /><strong>1. Connections</strong></p>\r\n<p style=\"padding-left: 30px;\">&#160;<strong>Access to your ABAP system</strong></p>\r\n<p style=\"padding-left: 60px;\">Access is often needed during the preparation phase of the session. Most commonly,&#160; a connection of the type <strong>R/3 support </strong>is used. ideally it should be set up and available ca 4-6 weeks before the session.</p>\r\n<p style=\"padding-left: 60px;\">SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/812732\">812732</a> provides more information about this connection type.</p>\r\n<p style=\"padding-left: 60px;\"><br /><strong>Logon information </strong>can be stored in Customer&#160;Remote Logon Depot by accessing&#160;<a target=\"_blank\" href=\"https://me.sap.com/remoteconnectivity\">https://me.sap.com/remoteconnectivity</a>&#160;or&#160;<a target=\"_blank\" href=\"https://me.sap.com/systemsprovisioning/systems\">https://me.sap.com/systemsprovisioning/systems</a>, selecting the relevant system and then selecting&#160;<em>Maintain Access Data.</em>&#160;More details in SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/2436508\">2436508.</a>&#160; No case is required.</p>\r\n<p style=\"padding-left: 60px;\">Access to systems is greatly simplified where the <strong>Line Opener Program ( LOP ) </strong>is used, as this allows access without extra interaction with a contact person at customer side.</p>\r\n<p style=\"padding-left: 60px;\">More details:</p>\r\n<p style=\"padding-left: 60px;\"><a target=\"_blank\" href=\"https://support.sap.com/en/tools/connectivity-tools/remote-support.html\">https://support.sap.com/en/tools/connectivity-tools/remote-support.html</a></p>\r\n<p style=\"padding-left: 60px;\"><a target=\"_blank\" href=\"https://support.sap.com/en/tools/connectivity-tools/line-opener.html\">https://support.sap.com/en/tools/connectivity-tools/line-opener.html</a></p>\r\n<p style=\"padding-left: 60px;\">Where more assistance is needed with set up or testing connections, an case can be opened (XX-SER-NET-HTL).</p>\r\n<p style=\"padding-left: 30px;\"><br /><strong>Access from your ABAP system to SAP</strong></p>\r\n<p style=\"padding-left: 60px;\">To use RTCCTOOL, and to some extent also SDCCN, a connection to SAP is necessary.</p>\r\n<p style=\"padding-left: 60px;\">Please ensure that task list&#160;SAP_BASIS_CONFIG_OSS_COMM has been checked and completed, ideally 4-6 weeks before the session date.</p>\r\n<p style=\"padding-left: 60px;\"><br />Completing this task creates the following HTTP destinations:</p>\r\n<ul>\r\n<ul>\r\n<ul>\r\n<li>\r\n<p>SAP Support Portal (SAP-SUPPORT_PORTAL - Type H)</p>\r\n</li>\r\n<li>\r\n<p>SAP Parcel Download (SAP-SUPPORT_PARCELBOX - Type G)</p>\r\n</li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\"><strong>SAP Note&#160;<a target=\"_blank\" href=\"https://me.sap.com/notes/2827658\">2827658</a></strong> provides steps and information on completing the task list.</p>\r\n<p style=\"padding-left: 60px;\">If the SAP_BASIS_CONFIG_OSS_COMM&#160;task list is not available to your SAP_BASIS release, <strong>SAP Notes&#160;<a target=\"_blank\" href=\"https://me.sap.com/notes/2289984\">2289984</a> and&#160;<a target=\"_blank\" href=\"https://me.sap.com/notes/2716729\">2716729</a></strong>&#160;provide information on configuring the destinations manually.</p>\r\n<p style=\"padding-left: 60px;\">If more assistance is needed a case can be opened. (SV-SMG-INS-CFG)</p>\r\n<p style=\"padding-left: 30px;\"><br /><strong>From your ABAP system to your SAP Solution Manager</strong></p>\r\n<p style=\"padding-left: 30px;\">To ensure that SDCCN in the managed system can communicate with your SAP Solution Manager, the *BACK destination must be maintained in SDCCN, and must test ok in sdccn. The connection is typically set up via Solution Manager-&gt;solman_setup-&gt;Managed Systems Confirguration-&gt;SID-&gt;Configure Systems-&gt;Full Configuration-&gt;3 Maintain RFC</p>\r\n<p style=\"padding-left: 30px;\">If more assistance is needed a case can be opened (SV-SMG-SYS).</p>\r\n<p><strong>2. Installation number</strong></p>\r\n<p>Installation number, system number&#160;and system ID are the key for all service administration, both at SAP and on the customer side.&#160; It is therefore vital that this information is up to date (and consistent)&#160;in the managed system itself, in the SAP Solution Manager and at&#160; <a target=\"_blank\" href=\"https://me.sap.com/systemsprovisioning/systems\">https://me.sap.com/systemsprovisioning/systems</a>.&#160; Any change must be reflected in LMDB in the SAP Solution Manager, as well as the corresponding logical components.</p>\r\n<p>Changes should be updated as soon as possible in all areas.</p>\r\n<p>If service sessions have been agreed already, contacts at SAP should be advised of a new installation number as soon as possible, as the necessary adjustments have to be made manually.</p>\r\n<p>This will ensure that sessions can be booked correctly and that they are technically consistent.</p>\r\n<p>If you need help implementing the installation number / license key inside a system, you can open a case. (XX-SER-LIKEY)</p>\r\n<p>If you need help updating information LMDB in your SAP Solution Manager you can open a case. (SV-SMG-LDB)</p>\r\n<p><strong>3. Tools</strong></p>\r\n<p style=\"padding-left: 60px;\"><strong>ST-PI and support packages</strong></p>\r\n<p style=\"padding-left: 60px;\">Addon ST-PI and the corresponding support packages contain the <strong>Service Data Control Center (SDCCN), </strong>which controls the collection and transfer of data analysed during a service session. They also contain various function modules and reports which are executed by function modules during data collection.</p>\r\n<p style=\"padding-left: 60px;\">Details regarding the content can be found in <strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/539977\">539977</a>.</strong></p>\r\n<p style=\"padding-left: 60px;\">Please note that only SDCCN should be used for data collection, rather than SDCC (older version). All developments since 2005 have been made for SDCCN only. Only SDCCN can make full use of the changes contained in ST-PI and the support packages.</p>\r\n<p style=\"padding-left: 60px;\"><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/763561\">763561</a> </strong>contains answers to frequently asked questions about SDCCN as well as a guide on the functionalities.</p>\r\n<p style=\"padding-left: 60px;\">If you need help regarding SDCCN you can open an case. (SV-SMG-SDD)</p>\r\n<ol></ol><ol></ol>\r\n<p style=\"padding-left: 60px;\"><strong>ST-A/PI</strong></p>\r\n<p style=\"padding-left: 60px;\">Addon ST-A/PI contains data collectors which contribute to data collections with SDCCN. It also contains additional functionalities for separately analysing and gathering application based data, as well as various trace tools. Another important feature is report <strong>RTCCTOOL</strong>.</p>\r\n<p style=\"padding-left: 60px;\">This can be run either directly or from within SDCCN, and gives you an overview of many of the steps and tools needed to prepare a system for a session.&#160; It provides a rating depending on whether a step still needs to be addressed or executed, and provides details on what needs to be done.&#160; Using RTCCTOOL can therefore save time when working through the preparation steps.</p>\r\n<p style=\"padding-left: 60px;\"><strong>Online Input for data collection</strong>: As of ST-A/PI 01N* the RTCCTOOL update mechanism delivers <strong>Online Input </strong>for some of the ST-A/PI data collectors. This input is then used in the next data collection executed in SDCCN.</p>\r\n<p style=\"padding-left: 60px;\">More details about ST-A/PI can be found in <strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/69455\">69455</a></strong></p>\r\n<ol><ol>.</ol></ol>\r\n<p><strong>4.&#160;Collectors and Monitors</strong></p>\r\n<p style=\"padding-left: 90px;\"><strong>Performance Collector</strong></p>\r\n<p style=\"padding-left: 90px;\">In order to provide data for peformance analysis in service sessions, the performance collector must run, and must store data correctly. The data for each instance must be stored in folder 'week' in ST03.<br />The following SAP Notes assist in starting the setup of the performance collector:</p>\r\n<p style=\"padding-left: 90px;\"><br /><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/12103\">12103</a> </strong>Contents of the TCOLL table [ releases &lt; 640]<br /><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/970449\">970449</a> </strong>Contents of TCOLL table in SAP_BASIS 640</p>\r\n<p style=\"padding-left: 90px;\"><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/966309\">966309</a> </strong>Contents of table TCOLL in SAP_BASIS 700</p>\r\n<p style=\"padding-left: 90px;\"><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/966631\">966631</a> </strong>Contents of TCOLL table in SAP_BASIS 710</p>\r\n<p style=\"padding-left: 90px;\"><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/1394391\">1394391</a> </strong>Contents of the table TCOLL in SAP_BASIS 720</p>\r\n<p style=\"padding-left: 90px;\"><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/1394392\">1394392</a> </strong>Contents of the table TCOLL in SAP_BASIS 730</p>\r\n<p style=\"padding-left: 90px;\">If more assistance is needed a case can be opened. (BC-CCM-MON-TUN)</p>\r\n<p style=\"padding-left: 90px;\">Further information on service relevant settings can also be found in <strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/144864\">144864</a></strong></p>\r\n<p style=\"padding-left: 90px;\"><strong>Hardware Utilization</strong></p>\r\n<p style=\"padding-left: 90px;\">A number of SAP services require information about the hardware utilization of each system in the landscape. To enable meaningful analysis it is important that the monitoring and recording of this data is set up correctly. Most services need ca two weeks worth of data.</p>\r\n<p style=\"padding-left: 90px;\">Ensure that data for all servers is being collected and can be viewed in ST06N, or ST06 -&gt; Detailed analysis -&gt; Compare recent days. This is required also for servers without an SAP instance (e.g. standalone servers, Java).&#160; Ideally this should be carefully reviewed 4-6 weeks prior to the agreed session date. Where problems exist this ensures that they can be recognised and fixed, in time to then still record the required amount of data correctly.</p>\r\n<p style=\"padding-left: 90px;\">If assistance is needed a case can be opened. (BC-CCM-MON-OS)</p>\r\n<p style=\"padding-left: 90px;\">Where the OS Collector is used please bear in mind the following :</p>\r\n<p style=\"padding-left: 90px;\">a) The OS collector version should be kept up to date by updating the SAP Host Agent. You can find the newest version following</p>\r\n<p style=\"padding-left: 90px;\">SAP Note&#160;<a target=\"_blank\" href=\"https://me.sap.com/notes/2598404\">2598404</a><a target=\"_blank\" href=\"/notes/19227\"><br /></a></p>\r\n<p style=\"padding-left: 90px;\">for troubleshooting, this note can be helpful:</p>\r\n<p style=\"padding-left: 90px;\"><a target=\"_blank\" href=\"https://me.sap.com/notes/2846165\">2846165</a> - SAPOSCOL is not starting due Shared Memory Not Available</p>\r\n<p style=\"padding-left: 90px;\">b) Where agents and/or connections are used to monitor and record data please ensure that they are set up correctly, and that the set up is reflected in transactions OS07 and AL15.</p>\r\n<p style=\"padding-left: 90px;\">c) If you are using IBM Logical Partitions with SPLPARS you need to take extra steps to configure monitoring correctly. Please refer to SAP Notes <a target=\"_blank\" href=\"https://me.sap.com/notes/895816\">895816 </a>and <a target=\"_blank\" href=\"https://me.sap.com/notes/994025\">994025</a> for more information.</p>\r\n<p style=\"padding-left: 90px;\">Many services have a preparation phase during which SAP colleagues perform a brief review of the preparation steps. Where complex set ups are being used it can be useful to prepare a high level description which explains the relationship between logical and physical servers.</p>\r\n<p style=\"padding-left: 90px;\">If hardware is shared between several SAP systems or a form of virtualization is used please inform the person delivering the session as this can affect the analysis.</p>\r\n<p style=\"padding-left: 90px;\">Recommendations given in the service will depend on the description of the exact hardware setup.</p>\r\n<p>&#160;</p>\r\n<p style=\"padding-left: 90px;\"><strong>Central Performance History (CPH)</strong></p>\r\n<p style=\"padding-left: 90px;\">SAP Services evaluate data from various sources. One source is the central performance history of the CCMS. To activate the collection of this data for SAP Services, refer to&#160;<strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/1147334\">1147334</a></strong></p>\r\n<p>&#160;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Database Monitor</strong></p>\r\n<p style=\"padding-left: 90px;\">The Database Monitor provides a snapshot of data buffer, cache quality, SQL cache catalog, I/O activity and other values used to analyze database performance.&#160; As of SAP NetWeaver 7.0 SPS 13, a new platform-independent tool called DBA Cockpit (transaction DBACOCKPIT) is available for all supported databases. This tool replaces many of the old database monitors and transactions.</p>\r\n<ol></ol><ol><ol><ol>Should there be any issues with these tools (e.g. no data, negative values), a case&#160; should be created. (BC-DB-&lt;DB type&gt;-CCM)</ol></ol></ol><ol><ol>To ensure the counters have enough information for analysis (one full working day) a restart of the database just before the agreed session date should be avoided.&#160; If the required information is not available, we will not be able to perform a detailed database analysis.</ol></ol>\r\n<p style=\"padding-left: 90px;\"><br />For further details please refer to the following SAP Notes:</p>\r\n<p style=\"padding-left: 90px;\">If you use central monitoring system (CEN) for databases:&#160;<strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/1068204\">1068204</a> </strong>New monitor template for database monitoring.</p>\r\n<p><strong>5. Traces</strong></p>\r\n<p style=\"padding-left: 90px;\">To enable the safe use of <strong>SQL Traces </strong>please ensure that the requirements described in <strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/1076804\">1076804</a> </strong>and <strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/1132350\">1132350</a> </strong>are met. (Older kernels only.)</p>\r\n<p style=\"padding-left: 90px;\">To enable the safe use of <strong>ABAP Traces&#160; </strong>please ensure that the requirements described in&#160; <strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/981919\">981919</a> </strong>are met. (640 and 700 only)</p>\r\n<p>&#160;</p>\r\n<p><strong>6. &#160;Profile parameters</strong><br />To activate the recording of the required performance data, set the ABAP instance profile parameters as follows:</p>\r\n<ol><ol>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;stat/level&#160;&#160; = 1</ol></ol><ol><ol>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;rsdb/staton&#160;&#160;= 1</ol><ol></ol><ol></ol><ol></ol></ol>\r\n<p>For Oracle databases, set the following parameter in configuration file \"init&lt;SID&gt;.ora\":</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;timed_statistics&#160;&#160;= true</p>\r\n<p>For Informix databases, set the following parameter in configuration file \"/informix/&lt;SID&gt;/etc/onconfig.&lt;dbhost&gt;.&lt;sid&gt;\":</p>\r\n<ol>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;WSTATS = 1</ol>\r\n<p><strong>7. Data Protection</strong></p>\r\n<p>When an SAP Support Service session is executed, no data is changed; data is only displayed according to the user's authorizations, so that service related system analyses can be carried out. Detailed information on data protection in connection with remote connections is available in&#160;<strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/46902\">46902</a></strong>. Where SAP employees come into contact with customers' personal/secret data these employees are bound to secrecy by &#167;5 according to the German Data Protection Act. More information can be found in <strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/35493\">35493</a></strong>.</p>\r\n<p>&#160;</p>\r\n<p><strong>8. Authorizations</strong></p>\r\n<p>To execute a service session, SAP requires users who are equipped with suitable authorization profiles.</p>\r\n<p><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/1405975\">1405975</a>&#160;</strong>describes which authorizations for various service types.</p>\r\n<p>&#160;</p>\r\n<p><strong>9. Questions </strong></p>\r\n<p>If you have questions or experience any difficulties during service preparation, create a case.</p>\r\n<p>Components:<br /><br /><strong>XX-SER-NET-HTL&#160;&#160; </strong>Setting up the connection from SAP to the&#160;customer system, or problems when using this connection<br /><strong>XX-SER-LIKEY</strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160;Problems regarding installation numbers<br /><strong>BC-UPG-ADDON</strong>&#160;&#160;&#160;&#160; Problems implementing addons (ST-PI or ST-A/PI)<br /><strong>BC-UPG-OCS</strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Problems implementing support packages<br /><strong>SV-SMG-LDB&#160;&#160;</strong>&#160; &#160; &#160; &#160; Problems related to transaction LMDB<br /><strong>SV-SMG-SDD</strong>&#160; &#160; &#160; &#160; &#160; Problems with RTCCTOOL or SDCCN<br /><strong>SV-SMG-SER-EWA</strong>&#160;&#160;EarlyWatch Alert<br /><strong>SV-BO-REQ</strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &#160;Request for Remote Services<br /><strong>SV-SMG-SER</strong>&#160; &#160; &#160; &#160; &#160; General issues with Remote services<br /><strong>BC-CCM-MON-OS</strong>&#160;&#160;&#160;Problems with the OS collector (ST06)<br /><strong>BC-CCM-MON-TUN</strong>&#160;&#160;Problems with the performance collector (ST03)<br /><strong>SV-SMG-MAI-APR</strong>&#160;&#160; The colleagues in this component can release the required download basket in SAP for Me , if a SAP Solution Manager system (and therefore Maintenance Optimizer) are temporarily unavailable, and software is urgently needed.</p>\r\n<p>&#160;</p>\r\n<p><strong>10. Service Reports in Service Messages</strong></p>\r\n<p>When a session is completed the service consultant uploads the report so it is available in a service message in SAP for Me.</p>\r\n<p>https://me.sap.com/app/servicemessage</p>\r\n<p>A SAP Support&#160;ID (\"SUser\") can be nominated to be the main recipient for a service message. (Assuming the SUser is based in the customer number which owns the analysed system.) A feedback form is provided in the service message containing the session results. To provide feedback, an SUser must have the authorisation ACCESS_SERVICE_MESSAGES for the customer number of the analysed system. If you experience problems accessing a report for a session please inform the service consultant.</p>\r\n<ol><ol></ol><ol>.</ol></ol>\r\n<p><strong>Appendix A</strong><br /><strong>Overview of SAP Notes with recommendations specific to Products:</strong><br /><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/320903\">320903</a></strong>&#160;&#160;Supply Chain Management ( SCM)<br /><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/160777\">160777</a></strong><a target=\"_blank\" href=\"https://me.sap.com/notes/160777\">&#160;</a>&#160;Business Information Warehouse (BW)<br /><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/1258585\">1258585</a></strong>&#160;&#160;SAP Customer Relationship Management<br /><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/1295142\">1295142</a></strong>&#160;&#160;SAP Remote Services for SAP SRM<br /><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/617604\">617604</a></strong>&#160;&#160;SAP Remote Services for SAP PI/XI<br /><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/1498779\">1498779</a></strong>&#160;Preparing Java-based systems for Support Services<br /><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/1251022\">1251022</a></strong>&#160;&#160;SAP GA Service for MDM/SRM-MDM: Technical Preparation</p>\r\n<p><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/1736373\">1736373</a></strong><a target=\"_blank\" href=\"https://me.sap.com/notes/1736373\">&#160;</a> SAP GV/EW Service for MDM/SRM-MDM: Technical Preparation</p>\r\n<p><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/738676\">738676</a></strong>&#160;&#160; Non ABAP components<br /><br /><strong>Appendix B</strong><br /><strong>Overview of SAP Notes with recommendations specific to Services:</strong><br /><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/696478\">696478</a></strong>&#160;&#160;SAP Security Optimization: Preparation &amp; additional info<br /><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/1323405\">1323405</a></strong>&#160;Technical Preparation of a CQC BPPO service<br /><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/1159758\">1159758</a>&#160;</strong>Data Volume Management: Central Preparation Note<br /><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/1077981\">1077981</a>&#160;</strong>SAP Upgrade Assessment Preparation Note&#160; &#160;<br /><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/1861798/E\">1861798</a>&#160;</strong>Advanced Remote Delivery - Custom Code Maintainability Check</p>\r\n<p><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/350011\">350011</a></strong><a target=\"_blank\" href=\"/notes/350011\">&#160;</a>&#160;GoingLive Service - Preparations for BPS Benchmark Check<br /><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/1149742\">1149742</a></strong><a target=\"_blank\" href=\"https://me.sap.com/notes/1149742\">&#160;</a>&#160;SAP CQC Going Live Support</p>\r\n<p><br /><strong>Appendix C</strong><br /><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/1892593\">1892593</a></strong> Preparing Support Services for SAP HANA Scenarios</p>\r\n<p><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/1765551\">1765551 </a></strong>Pre-requisites for SAP Service Delivery&#160; ( Sybase ASE Database Platform ) <br /><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/352081\">352081</a></strong><a target=\"_blank\" href=\"/notes/352081\">&#160;</a>&#160; Additional service-relevant functions for MaxDB<br /><strong>SAP Note <a target=\"_blank\" href=\"https://me.sap.com/notes/145316\">145316</a></strong>&#160;&#160; DB2-z/OS: Preparations for SAP Support Services</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D038895)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000091488/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000091488/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000091488/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000091488/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000091488/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000091488/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000091488/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000091488/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000091488/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2716729", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP backbone connectivity - SAP Parcel Box configuration", "RefUrl": "/notes/2716729"}, {"RefNumber": "2289984", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Configure the synchronous communication channel", "RefUrl": "/notes/2289984"}, {"RefNumber": "984434", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/984434"}, {"RefNumber": "948066", "RefComponent": "SV-PERF", "RefTitle": "Performance Analysis: Transactions to use", "RefUrl": "/notes/948066"}, {"RefNumber": "930747", "RefComponent": "SV-SMG", "RefTitle": "Service Delivery on SAP Solution Manager (Recommended Notes)", "RefUrl": "/notes/930747"}, {"RefNumber": "792941", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/792941"}, {"RefNumber": "781680", "RefComponent": "SV-SMG-SDD", "RefTitle": "SDCC/ SDCCN - Problems with function modules", "RefUrl": "/notes/781680"}, {"RefNumber": "763561", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCCN)  - FAQ", "RefUrl": "/notes/763561"}, {"RefNumber": "738676", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Download for Non-ABAP Components", "RefUrl": "/notes/738676"}, {"RefNumber": "713674", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/713674"}, {"RefNumber": "712757", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/712757"}, {"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455"}, {"RefNumber": "617604", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Services for SAP PI/XI", "RefUrl": "/notes/617604"}, {"RefNumber": "560630", "RefComponent": "SV-SMG-SDD", "RefTitle": "ST-PI: Solution Tools plug-in - prerequisite not met!", "RefUrl": "/notes/560630"}, {"RefNumber": "539977", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for add-on ST-PI", "RefUrl": "/notes/539977"}, {"RefNumber": "407293", "RefComponent": "SV-BO-REQ", "RefTitle": "Preparing APO System with GATP for GoingLive", "RefUrl": "/notes/407293"}, {"RefNumber": "320903", "RefComponent": "SV-PERF-SCM", "RefTitle": "Preparing the SCM (APO) system for EarlyWatch and GoingLive", "RefUrl": "/notes/320903"}, {"RefNumber": "2827658", "RefComponent": "BC-INS-TC-CNT", "RefTitle": "Automated Configuration of new Support Backbone Communication - Update 02", "RefUrl": "/notes/2827658"}, {"RefNumber": "216952", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCC) - FAQ", "RefUrl": "/notes/216952"}, {"RefNumber": "207223", "RefComponent": "SV-SMG-SDD", "RefTitle": "SAP EarlyWatch Alert Processed at SAP", "RefUrl": "/notes/207223"}, {"RefNumber": "20624", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/20624"}, {"RefNumber": "202934", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/202934"}, {"RefNumber": "19227", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "Retrieving the latest saposcol", "RefUrl": "/notes/19227"}, {"RefNumber": "1892593", "RefComponent": "SV-SMG-SER", "RefTitle": "Preparing Support Services for SAP HANA Scenarios", "RefUrl": "/notes/1892593"}, {"RefNumber": "1873719", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1873719"}, {"RefNumber": "1784032", "RefComponent": "SV-SMG-SDD", "RefTitle": "Extension of EWA data by EWM data", "RefUrl": "/notes/1784032"}, {"RefNumber": "1657403", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1657403"}, {"RefNumber": "1518015", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1518015"}, {"RefNumber": "1498779", "RefComponent": "SV-BO-REQ", "RefTitle": "Preparing Support Services for Systems with a Java Stack", "RefUrl": "/notes/1498779"}, {"RefNumber": "145316", "RefComponent": "SV-SMG-SER", "RefTitle": "DB2-z/OS: Preparations for SAP Support Services", "RefUrl": "/notes/145316"}, {"RefNumber": "144864", "RefComponent": "SV-SMG-SER", "RefTitle": "Setting Up the ABAP Workload Monitor", "RefUrl": "/notes/144864"}, {"RefNumber": "1405975", "RefComponent": "SV-SMG-SER", "RefTitle": "Minimum Authorization Profile for Remote Service Delivery", "RefUrl": "/notes/1405975"}, {"RefNumber": "1330674", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Custom Code Maintainability Check", "RefUrl": "/notes/1330674"}, {"RefNumber": "1324027", "RefComponent": "SV-SMG", "RefTitle": "SAP Software Solution", "RefUrl": "/notes/1324027"}, {"RefNumber": "1323405", "RefComponent": "SV-SMG-SER", "RefTitle": "Technical Preparation of a CQC BPPO service", "RefUrl": "/notes/1323405"}, {"RefNumber": "1295142", "RefComponent": "SV-PERF", "RefTitle": "SAP Remote Services for SAP SRM", "RefUrl": "/notes/1295142"}, {"RefNumber": "1258585", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Remote Services for SAP CRM", "RefUrl": "/notes/1258585"}, {"RefNumber": "1257308", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "FAQ: Using EarlyWatch Alert", "RefUrl": "/notes/1257308"}, {"RefNumber": "1172939", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1172939"}, {"RefNumber": "1170668", "RefComponent": "SV-SMG-SVD", "RefTitle": "The role of SAP Solution Manager in Remote Service Delivery", "RefUrl": "/notes/1170668"}, {"RefNumber": "1149742", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP (CQC) Going Live Support", "RefUrl": "/notes/1149742"}, {"RefNumber": "1077981", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Upgrade Assessment Preparation Note", "RefUrl": "/notes/1077981"}, {"RefNumber": "1012503", "RefComponent": "SV-SMG-SER", "RefTitle": "Storage for RFC Destination to Solution Manager is too short", "RefUrl": "/notes/1012503"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3138242", "RefComponent": "SV-SMG-SVD-SSP", "RefTitle": "How to reset a CQC service preparation step?", "RefUrl": "/notes/3138242 "}, {"RefNumber": "2034274", "RefComponent": "SV-SMG-SVD", "RefTitle": "Advanced Remote Service Delivery  SAP Standard Support - Customer Info", "RefUrl": "/notes/2034274 "}, {"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455 "}, {"RefNumber": "781680", "RefComponent": "SV-SMG-SDD", "RefTitle": "SDCC/ SDCCN - Problems with function modules", "RefUrl": "/notes/781680 "}, {"RefNumber": "539977", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for add-on ST-PI", "RefUrl": "/notes/539977 "}, {"RefNumber": "1257308", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "FAQ: Using EarlyWatch Alert", "RefUrl": "/notes/1257308 "}, {"RefNumber": "1784032", "RefComponent": "SV-SMG-SDD", "RefTitle": "Extension of EWA data by EWM data", "RefUrl": "/notes/1784032 "}, {"RefNumber": "1609155", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Services", "RefUrl": "/notes/1609155 "}, {"RefNumber": "1330674", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Custom Code Maintainability Check", "RefUrl": "/notes/1330674 "}, {"RefNumber": "1892593", "RefComponent": "SV-SMG-SER", "RefTitle": "Preparing Support Services for SAP HANA Scenarios", "RefUrl": "/notes/1892593 "}, {"RefNumber": "1149742", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP (CQC) Going Live Support", "RefUrl": "/notes/1149742 "}, {"RefNumber": "1323405", "RefComponent": "SV-SMG-SER", "RefTitle": "Technical Preparation of a CQC BPPO service", "RefUrl": "/notes/1323405 "}, {"RefNumber": "19227", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "Retrieving the latest saposcol", "RefUrl": "/notes/19227 "}, {"RefNumber": "617604", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Services for SAP PI/XI", "RefUrl": "/notes/617604 "}, {"RefNumber": "1498779", "RefComponent": "SV-BO-REQ", "RefTitle": "Preparing Support Services for Systems with a Java Stack", "RefUrl": "/notes/1498779 "}, {"RefNumber": "207223", "RefComponent": "SV-SMG-SDD", "RefTitle": "SAP EarlyWatch Alert Processed at SAP", "RefUrl": "/notes/207223 "}, {"RefNumber": "1442799", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Enterprise Support Report", "RefUrl": "/notes/1442799 "}, {"RefNumber": "1405975", "RefComponent": "SV-SMG-SER", "RefTitle": "Minimum Authorization Profile for Remote Service Delivery", "RefUrl": "/notes/1405975 "}, {"RefNumber": "948066", "RefComponent": "SV-PERF", "RefTitle": "Performance Analysis: Transactions to use", "RefUrl": "/notes/948066 "}, {"RefNumber": "1657403", "RefComponent": "SV-SMG-SVD", "RefTitle": "SAP Support Remote Services for SAP BusinessObjects IDD/EIM", "RefUrl": "/notes/1657403 "}, {"RefNumber": "1295142", "RefComponent": "SV-PERF", "RefTitle": "SAP Remote Services for SAP SRM", "RefUrl": "/notes/1295142 "}, {"RefNumber": "1077981", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Upgrade Assessment Preparation Note", "RefUrl": "/notes/1077981 "}, {"RefNumber": "160777", "RefComponent": "SV-PERF", "RefTitle": "SAP Remote Services for SAP BI/BW", "RefUrl": "/notes/160777 "}, {"RefNumber": "700518", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Service Sessions: How To Do Error Analysis", "RefUrl": "/notes/700518 "}, {"RefNumber": "320903", "RefComponent": "SV-PERF-SCM", "RefTitle": "Preparing the SCM (APO) system for EarlyWatch and GoingLive", "RefUrl": "/notes/320903 "}, {"RefNumber": "984434", "RefComponent": "XX-SER-FORME", "RefTitle": "How to speed up customer incident processing", "RefUrl": "/notes/984434 "}, {"RefNumber": "1518015", "RefComponent": "SV-ES-SAC", "RefTitle": "Enterprise Support Prerequisites", "RefUrl": "/notes/1518015 "}, {"RefNumber": "145316", "RefComponent": "SV-SMG-SER", "RefTitle": "DB2-z/OS: Preparations for SAP Support Services", "RefUrl": "/notes/145316 "}, {"RefNumber": "1258585", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Remote Services for SAP CRM", "RefUrl": "/notes/1258585 "}, {"RefNumber": "763561", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCCN)  - FAQ", "RefUrl": "/notes/763561 "}, {"RefNumber": "216952", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCC) - FAQ", "RefUrl": "/notes/216952 "}, {"RefNumber": "1324027", "RefComponent": "SV-SMG", "RefTitle": "SAP Software Solution", "RefUrl": "/notes/1324027 "}, {"RefNumber": "738676", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Download for Non-ABAP Components", "RefUrl": "/notes/738676 "}, {"RefNumber": "930747", "RefComponent": "SV-SMG", "RefTitle": "Service Delivery on SAP Solution Manager (Recommended Notes)", "RefUrl": "/notes/930747 "}, {"RefNumber": "144864", "RefComponent": "SV-SMG-SER", "RefTitle": "Setting Up the ABAP Workload Monitor", "RefUrl": "/notes/144864 "}, {"RefNumber": "1170668", "RefComponent": "SV-SMG-SVD", "RefTitle": "The role of SAP Solution Manager in Remote Service Delivery", "RefUrl": "/notes/1170668 "}, {"RefNumber": "1012503", "RefComponent": "SV-SMG-SER", "RefTitle": "Storage for RFC Destination to Solution Manager is too short", "RefUrl": "/notes/1012503 "}, {"RefNumber": "560630", "RefComponent": "SV-SMG-SDD", "RefTitle": "ST-PI: Solution Tools plug-in - prerequisite not met!", "RefUrl": "/notes/560630 "}, {"RefNumber": "407293", "RefComponent": "SV-BO-REQ", "RefTitle": "Preparing APO System with GATP for GoingLive", "RefUrl": "/notes/407293 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}