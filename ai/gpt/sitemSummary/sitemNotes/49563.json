{"Request": {"Number": "49563", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 235, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014453312017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=2E6DF0363AC56B977ACAB791869C5AE5"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "49563"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 13}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.08.2017"}, "SAPComponentKey": {"_label": "Component", "value": "FI-AA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Asset Accounting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Asset Accounting", "value": "FI-AA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "49563 - Subsequent transfer of data to FI-AA"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to perform subsequent transfers of data to asset accounting, although the company code is already productive.<br />This is the case if not all data could originally be transferred directly for the following reasons, for example:</p>\r\n<ol>1. The data was kept in a different system.</ol><ol>2. A fusion of company codes is to take place.</ol>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Legacy data transfer, incremental legacy data</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This Note only describes cases in which the data transfer into to the SAP R/3 system is performed in portions for a company code.<br />Example:<br />First data transfer for company code 0001 on 31.12.1994<br />Second data transfer for the same company code in a subsequent fiscal year.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>SAP supports two solutions. Only the first solution enables clean representation of the balance continuity within the R/3 system.</p>\r\n<ol><ol>1.</ol></ol>\r\n<p><strong>Maintaining balance continuity for the company code</strong></p>\r\n<ol><ol>A subsequent transfer of data cannot be performed using the legacy data transfer function.</ol></ol><ol>This transfer should be represented as acquisitions in the current year.</ol><ol>2. If you do not necessarily want to maintain the balance continuity for closed fiscal years in the R/3 system, you can also perform the second transfer using the 'legacy data transfer' function. Take the following into account here:</ol><ol><ol>a) The previous fiscal years before the second transfer must be closed and must not be opened again.</ol></ol><ol><ol>b) Old assets from the first data transfer must never be touched using the legacy data transactions. In particular, ensure that no new postings are made at the time of the second transfer to the company code.</ol></ol><ol><ol>c) After the transfer, the company code must always be set to 'Productive' again.</ol></ol><ol><ol>d) A reconciliation from the end of the previous year to start of the current year within asset accounting, or between asset accounting and general ledger accounting, is no longer possible using the asset history sheet for the year of the second transfer.</ol></ol><ol><ol>e) Transfers of assets that are assigned to a group asset are therefore not possible. Use a separate report to adjust the values in the group asset on the database.</ol><ol></ol><ol></ol><ol></ol><ol></ol><ol>f) Even if you define the periods in Customizing up to which you want to transfer the posted depreciation for the new assets from the operational system, the depreciation posting system takes the assets into account immediately.</ol></ol><ol><ol><ol><ol>Example: You have posted depreciations in your SAP system up to period 3. You set the period for the posted depreciation to 5. For each period, 100 EUR of depreciations were accrued in your legacy system. You therefore transfer 500 Euro as posted depreciations. If you now execute the depreciation posting run for period 4, the system calculates 400 Euro of planned depreciations to period 4. Because 500 Euro of planned depreciation exists, the depreciation posting run therefore posts +100 Euro in period 4.</ol></ol></ol></ol><ol><ol><ol><ol>If you want to transfer the depreciation to period 5, you cannot perform the transfer until the depreciation posting is completed for period 5.</ol><ol></ol><ol></ol></ol></ol></ol>\r\n<p>&#x00A0;</p>\r\n<p><br /><strong>Major restrictions:</strong> If you use the Business Warehouse (BW), the incremental data transfer in accordance with solution 2 would mean that the data transfer values could not be correctly extracted to BW. Period reporting for this fiscal year would only be possible with restrictions in BW.<br /><br />For further questions, please contact SAP consulting.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BCT-FI-AA (BW only - Asset Accounting)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D019672)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D019672)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "762911", "RefComponent": "BW-BCT-FI-AA", "RefTitle": "0FI_AA_12: Extraction for subannual data transfer", "RefUrl": "/notes/762911"}, {"RefNumber": "757228", "RefComponent": "BW-BCT-FI-AA", "RefTitle": "0FI_AA_12, cost center, internal order, CO Controlling area", "RefUrl": "/notes/757228"}, {"RefNumber": "738741", "RefComponent": "BW-BCT-FI-AA", "RefTitle": "0FI_AA_12: Extraction during mid-year data transfer", "RefUrl": "/notes/738741"}, {"RefNumber": "728479", "RefComponent": "BW-BCT-FI-AA", "RefTitle": "0FI_AA_12: Extraction during mid-year convers. is incorrect", "RefUrl": "/notes/728479"}, {"RefNumber": "69225", "RefComponent": "FI-AA", "RefTitle": "Reconciliation difference financial/asset accounting", "RefUrl": "/notes/69225"}, {"RefNumber": "550176", "RefComponent": "FI-AA-AA-A", "RefTitle": "FAQ note legacy data transfer asset master records", "RefUrl": "/notes/550176"}, {"RefNumber": "1500830", "RefComponent": "BW-BCT-FI-AA", "RefTitle": "Extracting 0FI_AA_12 after mid-year legacy data transfer", "RefUrl": "/notes/1500830"}, {"RefNumber": "110373", "RefComponent": "CA-EUR-CNV", "RefTitle": "Production startup with local currency Euro", "RefUrl": "/notes/110373"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3318060", "RefComponent": "FI-AA-AA-C", "RefTitle": "Central KBA Legacy Data Transfer in Asset Accounting S/4HANA  / How-to scenarios, standard behavior and guides.", "RefUrl": "/notes/3318060 "}, {"RefNumber": "1500830", "RefComponent": "BW-BCT-FI-AA", "RefTitle": "Extracting 0FI_AA_12 after mid-year legacy data transfer", "RefUrl": "/notes/1500830 "}, {"RefNumber": "762911", "RefComponent": "BW-BCT-FI-AA", "RefTitle": "0FI_AA_12: Extraction for subannual data transfer", "RefUrl": "/notes/762911 "}, {"RefNumber": "728479", "RefComponent": "BW-BCT-FI-AA", "RefTitle": "0FI_AA_12: Extraction during mid-year convers. is incorrect", "RefUrl": "/notes/728479 "}, {"RefNumber": "757228", "RefComponent": "BW-BCT-FI-AA", "RefTitle": "0FI_AA_12, cost center, internal order, CO Controlling area", "RefUrl": "/notes/757228 "}, {"RefNumber": "738741", "RefComponent": "BW-BCT-FI-AA", "RefTitle": "0FI_AA_12: Extraction during mid-year data transfer", "RefUrl": "/notes/738741 "}, {"RefNumber": "550176", "RefComponent": "FI-AA-AA-A", "RefTitle": "FAQ note legacy data transfer asset master records", "RefUrl": "/notes/550176 "}, {"RefNumber": "69225", "RefComponent": "FI-AA", "RefTitle": "Reconciliation difference financial/asset accounting", "RefUrl": "/notes/69225 "}, {"RefNumber": "110373", "RefComponent": "CA-EUR-CNV", "RefTitle": "Production startup with local currency Euro", "RefUrl": "/notes/110373 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}