{"Request": {"Number": "1689663", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 548, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017397472017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001689663?language=E&token=6D92E4EAA0FC5B454F3804A5D8B85E77"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001689663", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001689663/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1689663"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "21.12.2021"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CST-GW"}, "SAPComponentKeyText": {"_label": "Component", "value": "Gateway/CPIC"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Client/Server Technology", "value": "BC-CST", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CST*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Gateway/CPIC", "value": "BC-CST-GW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CST-GW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1689663 - GW: Simulation mode for reg, sec, and prxy_info"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Up to now, you could not use SAP Note 1425765 to create a reg_info or sec_info file based on the data contained in transaction SM59 (more specifically, the table RFCDES). Since the Access Control Lists (ACL) that are created in this way evaluate only the connections that are stored in the system, the restart behavior for dynamic RFC destinations or a connection between two external programs using the gateway might not be mapped correctly in the ACL files. If activation is immediate, malfunctions can occur in productive operation.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>reg_info reginfo sec_info secinfo proxy_info prxy_info&#x00A0;gw/logging gw/sim_mode gateway</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><br /><strong>1.) NEW: Simulation mode</strong><br /><br />A simulation mode should make implementing new ACL files even easier to minimize possible malfunctions in productive operation. To do this, a \"Permit All\" rule is set (normal: \"Deny All\").<br /><br />Caution: Simulation mode is intended only to improve the impact analysis of the ACL file activation. The mode itself does not improve security.<br /><br />Simulation mode is controlled by the dynamic profile parameter<br /><br />gw/sim_mode = [0,1].<br /><br />Set the value to 1 to activate it or 0 (default) to deactivate it.<br /><br />As soon as the parameter has the value 1, the gateway processes the rules in reginfo <strong>AND</strong> secinfo as follows:</p>\r\n<ul>\r\n<li>If the system finds a suitable PERMIT rule, it permits the request as before.</li>\r\n</ul>\r\n<ul>\r\n<li>If the system finds a suitable DENY rule, it rejects the request as before. (The system still considers explicit rules.)</li>\r\n</ul>\r\n<ul>\r\n<li>If the system does <strong>NOT</strong> find a suitable rule, it permits the request and writes a special entry in the gateway log file that you defined using gw/logging (see SAP Note 2818285).</li>\r\n</ul>\r\n<p><br /><br />As a result, the system allows all of the following as long as they were not entered with explicit rules:</p>\r\n<ul>\r\n<li>Restart attempts (secinfo)</li>\r\n<li>Registrations with registered programs (reginfo, ONLY registration) or</li>\r\n<li>Forwarding attempts (prxyinfo)</li>\r\n</ul>\r\n<p><br /><br /><br />However, you notice that <strong>communication</strong> with registered programs of servers that are not listed in the ACCESS option is rejected. Reason: The leading PERMIT or DENY can be defined only for the registration as such. In contrast, the host list in the ACCESS option is a positive list of the permitted servers. To ensure that the administrator always has the option of defining a rejection, simulation mode is not permitted to force any changes here.<br /><br /><br /><strong>2.) NEW: LOGGING feature for simulation mode</strong><br /><br />You can use the new simulation mode to activate an incomplete reginfo file or secinfo file. If no rule exists for a specific external program, the system still permits it despite this and it can be logged. Logging is controlled via a new gateway action for LOGGING (see the parameter gw/logging or call transaction SMGW and choose \"Goto -&gt; Expert Functions -&gt; Logging\"). Activate this by setting the indicator Z.<br /><br />You can either set the indicator using the parameter gw/logging (see SAP Note <strong>2527689</strong>) or dynamically until the next time the instance restarts by calling transaction SMGW and choosing \"Goto -&gt; Expert Functions -&gt; Logging\" (select the \"System Rejections (reg-secinfo)\" checkbox).<br />All restart attempts (secinfo), registrations and communication with registered programs (reginfo), and forwarding attempts of an RFC connection set up due to gateway options whereby these attempts were not entered via explicit rules are logged with the indicator Z in the gateway log file.<br />The ACL files must then be adjusted through the evaluation of the entries with the indicator Z. Finally, you must deactivate simulation mode again to activate security using the ACL file.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The enhancement to the functions contains a kernel change AND ABAP adjustments. Simulation mode and the LOGGING feature also work without ABAP changes if the required kernel patch is active.<br /><br />Apply the patch level for the kernel release as described in more detail in the \"Support Package Patches\" section and set the following parameter in the instance profile to activate simulation mode:<br /><br />gw/sim_mode = 1 (active, 0 inactive)<br /><br />The instance profile parameter gw/sim_mode is dynamic; this means that you do not need to restart the instance to change its values.<br /><br />If you have imported the enhancement packages required for the relevant ABAP adjustments (see \"Support Packages\"), several options for activating the parameter dynamically exist:</p>\r\n<ul>\r\n<li>Set the parameter in the instance profile or the DEFAULT profile and carry out a restart (this corresponds to the dynamic variant but you must set the parameter in the profile to ensure that it is activated permanently).</li>\r\n</ul>\r\n<ul>\r\n<li>You can call transaction RZ11 to change the parameter value (the support packages listed in this SAP Note are required).</li>\r\n</ul>\r\n<ul>\r\n<li>You can call transaction SMGW and choose \"Goto -&gt; Parameters -&gt; Change\" to change the parameter value (the support packages listed in this SAP Note are required).</li>\r\n</ul>\r\n<ul>\r\n<li>You can call transaction SMGW and choose \"Goto -&gt; Expert Functions -&gt; Logging\" to change the parameter value. In the bottom frame, you can use the ON and OFF radio buttons to set the parameter (the support packages listed in this SAP Note are required).</li>\r\n</ul>\r\n<p><br />If you have NOT yet imported the required enhancement packages for the ABAP adjustments, you must call the function module TH_CHANGE_PARAMETER in transaction SE37.<br /><br />Select the uppercase/lowercase checkbox. <br />For the parameter name, enter: gw/sim_mode<br />For the parameter value, enter: 1<br />For CHECK_PARAMETER, enter:  0<br /><br />and press F8.  Note that for the dynamic change, the required kernel patch level must also run in your system.<br /><br /><br /><strong><span style=\"text-decoration: underline;\">Note the following:</span></strong></p>\r\n<ul>\r\n<ul>\r\n<li>As part of the ABAP enhancements, the following errors have also been corrected in all SAP_BASIS releases: When you call transaction SMGW and choose \"Goto -&gt; Expert Functions -&gt; Logging\", you can also select the \"Specify Old File Again\" option in the \"Toggle Criteria\" section. This did not work and has been corrected.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>In addition, a further option (\"Only Rejected Accesses\") has been implemented below the \"Security\" checkbox on the same screen for Basis Releases 620 to 711. This records ALL rejected accesses. This includes those that take place due to an explicit DENY rule and those that are rejected because the system could not find a suitable rule. (This feature already exists in higher Basis releases.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>In addition, the dynamic profile parameters gw/sim_mode, gw/reg_no_conn_info, and rdisp/thsend_mode have been created and documented in transaction RZ11.</li>\r\n</ul>\r\n</ul></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023620)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D023620)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001689663/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001689663/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001689663/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001689663/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001689663/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001689663/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001689663/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001689663/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001689663/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2818285", "RefComponent": "BC-CST-GW", "RefTitle": "GW: Setting up Gateway logging", "RefUrl": "/notes/2818285"}, {"RefNumber": "2527689", "RefComponent": "BC-CST-GW", "RefTitle": "GW: Setting up gateway logging (new)", "RefUrl": "/notes/2527689"}, {"RefNumber": "1888894", "RefComponent": "BC-CST-GW", "RefTitle": "GW: Improvements for logging with action Z", "RefUrl": "/notes/1888894"}, {"RefNumber": "1425765", "RefComponent": "BC-CST-GW", "RefTitle": "Generating sec_info reg_info", "RefUrl": "/notes/1425765"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2004229", "RefComponent": "BC-CCM-MON-SLG", "RefTitle": "System log \"Missng:TSL1TE,S1I\"", "RefUrl": "/notes/2004229 "}, {"RefNumber": "2605523", "RefComponent": "BC-CST-GW", "RefTitle": "[WEBINAR] RFC Gateway Security Features", "RefUrl": "/notes/2605523 "}, {"RefNumber": "1888894", "RefComponent": "BC-CST-GW", "RefTitle": "GW: Improvements for logging with action Z", "RefUrl": "/notes/1888894 "}, {"RefNumber": "1425765", "RefComponent": "BC-CST-GW", "RefTitle": "Generating sec_info reg_info", "RefUrl": "/notes/1425765 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "KRNL32NUC", "From": "4.6DEXT", "To": "4.6DEX2", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "6.40", "To": "6.40EX2", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.00", "To": "7.01", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.10", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "6.40", "To": "6.40EX2", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.00", "To": "7.01", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.10", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "4.6DEXT", "To": "4.6DEX2", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "6.40", "To": "6.40EX2", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.00", "To": "7.01", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.10", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.38", "To": "7.38", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.40", "To": "7.40", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "6.40", "To": "6.40EX2", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.00", "To": "7.01", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.10", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.38", "To": "7.38", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.40", "To": "7.40", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": "X"}, {"SoftwareComponent": "KERNEL", "From": "4.6D", "To": "4.6D", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "6.40", "To": "6.40", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.00", "To": "7.01", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.10", "To": "7.11", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.20", "To": "7.21", "Subsequent": "X"}, {"SoftwareComponent": "KERNEL", "From": "7.38", "To": "7.38", "Subsequent": "X"}, {"SoftwareComponent": "KERNEL", "From": "7.40", "To": "7.40", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62072", "URL": "/supportpackage/SAPKB62072"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64030", "URL": "/supportpackage/SAPKB64030"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70027", "URL": "/supportpackage/SAPKB70027"}, {"SoftwareComponentVersion": "SAP_BASIS 701", "SupportPackage": "SAPKB70112", "URL": "/supportpackage/SAPKB70112"}, {"SoftwareComponentVersion": "SAP_BASIS 702", "SupportPackage": "SAPKB70212", "URL": "/supportpackage/SAPKB70212"}, {"SoftwareComponentVersion": "SAP_BASIS 710", "SupportPackage": "SAPKB71015", "URL": "/supportpackage/SAPKB71015"}, {"SoftwareComponentVersion": "SAP_BASIS 711", "SupportPackage": "SAPKB71110", "URL": "/supportpackage/SAPKB71110"}, {"SoftwareComponentVersion": "SAP_BASIS 720", "SupportPackage": "SAPKB72008", "URL": "/supportpackage/SAPKB72008"}, {"SoftwareComponentVersion": "SAP_BASIS 730", "SupportPackage": "SAPKB73008", "URL": "/supportpackage/SAPKB73008"}, {"SoftwareComponentVersion": "SAP_BASIS 731", "SupportPackage": "SAPKB73104", "URL": "/supportpackage/SAPKB73104"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT", "SupportPackage": "SP010", "SupportPackagePatch": "000010", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT UNICODE", "SupportPackage": "SP010", "SupportPackagePatch": "000010", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP010", "SupportPackagePatch": "000010", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP010", "SupportPackagePatch": "000010", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT", "SupportPackage": "SP010", "SupportPackagePatch": "000010", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021283&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT UC", "SupportPackage": "SP010", "SupportPackagePatch": "000010", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021284&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP010", "SupportPackagePatch": "000010", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP010", "SupportPackagePatch": "000010", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}