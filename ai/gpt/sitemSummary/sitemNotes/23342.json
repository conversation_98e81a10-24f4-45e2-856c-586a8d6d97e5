{"Request": {"Number": "23342", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 368, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000089572017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000023342?language=E&token=B8AD33240CA2E83596AB6D7582EEAC45"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000023342", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000023342/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "23342"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 21}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Documentation error"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.11.2003"}, "SAPComponentKey": {"_label": "Component", "value": "BC-SEC-USR-ADM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Users and Authorization administration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Security - Read KBA 2985997 for subcomponents", "value": "BC-SEC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "User Administration", "value": "BC-SEC-USR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC-USR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Users and Authorization administration", "value": "BC-SEC-USR-ADM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC-USR-ADM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "23342 - No authorization ... --> analysis (also for external users)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>A user tries to activate a function and a message such as \"No authorization for ...\" or \"You are not authorized to ...\" appears.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Error messages SF 261, SAP Easy Access, /SAPAPO/<br />SU53 reference user user buffer role profile</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><OL>1. The most frequent cause is that the ABAP/4 program includes an authorization check (statement AUTHORITY-CHECK) against an authorization object with specific values, and the user does not have the appropriate authorization in his/her user master record.<br />Analysis:</OL> <OL><OL>a) Call transaction SU53 (in a second session if possible). It shows which authorization object is checked and with which values. It also shows which authorizations the user has for the relevant object in his/her master record.<br />As of Basis Support Package 4.6B SP 25, 4.6C SP 15, 4.6D SP 06, the data is copied to the database by calling transaction SU53. A user administrator also can call <PERSON><PERSON>53 for this user and analyze the missing authorizations herself/himself.<br />With another Support Package as of 4.6B or correction instruction 291726, display in SU53 is enhanced. In addition to the authorizations of the user, the profile or role via which the user has achieved the authorization is displayed as well.<br />With another Support Package as of 4.6C or correction instruction 291727, display in SU53 is enhanced. If the user has been given additional authorizations via the assignment of a reference user, this is displayed as well.</OL></OL> <OL><OL>b) Have your administrator run an authorization trace. This trace is part of the ABAP trace in Rel. 2.x and part of the SAP System trace in Rel. 3.0A. For more information, please see note no. 18529.</OL></OL><p></p> <OL>2. The user master record was supplemented, but the user needs to log off and back on first.</OL> <p></p> <OL>3. The user has been granted the required authorization, but has so many authorizations in his/her user master record that the user buffer overflows.<br />Analysis: Transaction SU53 or the authorization trace will show which authorization object is checked.&#x00A0;&#x00A0;&#x00A0;&#x00A0;The user info system can be used to display all the authorizations that a user has in his/her user master. With transaction SU56, the logged on test user can check which authorizations appear in the user buffer and compare them with the list from the user info system. If transaction SU56 displays fewer authorizations than the Info system, a buffer overflow has occurred.</OL> <p></p> <OL>4. A check was made outside the SAP authorization concept. In this case, the user does not meet the necessary requirements.<br />Analysis: Observe the long text of the error message or the documentation of the corresponding application.</OL> <p></p> <OL>5. As of Release 3.1G, a mirroring of the user buffer has been introduced in the database. This results in considerably increased performance for applications which make frequent RFC calls to other systems.<br />This can, unfortunately, lead to inconsistencies when users or profiles are transported. The result of this is that when a user attempts to log on whose data has been modified (using a transport), the system still draws on the old authorizations.</OL> <p></p> <OL>6. As of Rel. 4.6A, transactions are usually called from the SAP Easy Access menu. This new transaction call has been implemented internally with CALL TRANSACTION. Therefore, in some cases an incorrect error message may still occur.<br />Example: If a transaction has been locked by using transaction SM01, error message SF261 \"You have no authorization for transaction &amp;\" is displayed instead of the correct error message 00034 \"Transaction &amp; is locked\".</OL><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. The user administrator needs to check whether the user needs to be granted authorization for the function. If necessary, the administrator should add to the user authorizations. This can be done by changing the user master record, a profile contained therein, or by changing an authorization. The exact procedure depends on the structure of the authorization environment.</OL> <p></p> <OL>2. Log off, log on again, and try the function again.</OL> <p></p> <OL>3. Try to reduce the number of authorizations in the affected master record by removing redundant authorizations and authorization profiles from the master record and/or grouping several individual authorizations together as one authorization object with a single authorization.<br />Or increase the system profile parameter auth/auth_number_in_userbuffer (also see note no. 10187).</OL> <p></p> <OL>4. To solve the problem, refer to the long text of the error message or the manual of the relevant application.</OL> <p></p> <OL>5. You need to reset the user buffers manually.<br />In Release 3.1G this is done within the \"Maintain users\" transaction (SU01) by choosing: Utilities -&gt; Mass changes -&gt; Reset all user buffers. It is also possible, from 3.1H, to reset these buffers without an authorization check taking place by choosing the following System -&gt; User profile -&gt; User defaults -&gt; = Enter RSHU in the ok field -&gt; ENTER.<br />Reason for this procedure: In certain situations it is unfortunately possible that no user has the authorization to reset the user buffers. It was therefore necessary to incorporate a way of resetting the buffers which was not subject to authorization checks. This is not a problem, since, as a consequence, the system reconstructs the buffer from new whenever a user logs on. It will just correspond to its performance before 3.1G at the first authorization check following the logon. Since unauthorized users will only have to carry out this procedure very rarely, we thought it best to \"hide\" the procedure as described.<br /><br />As of version 4.0B, this function was stored in transaction SU01 for security reasons. Here you can now use the OK code \"RSET\" in every screen (including from the start screen) to reset the user buffer. This therefore restricts this function to administrative users.</OL> <p></p> <OL>6. Check whether it is possible to call the transaction displayed in the error message of the SAP Easy Access menu by entering it in the OK field with /O prior to the transaction.<br />Example:<br />When you call the transaction via the SAP Easy Access menu, the following error message is displayed:<br />You have no authorization for transaction /SAPAPO/SCEVERSCOMP Enter the following in the OK field:<br />Enter the following in the OK field:<br />/O/SAPAPO/SCEVERSCOMP<br />and press 'Enter'.<br />Using the correction described in Note 307641, the incorrect error message for locked transactions described above is eliminated.</OL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "HIER"}, {"Key": "Transaction codes", "Value": "COPY"}, {"Key": "Transaction codes", "Value": "SU01"}, {"Key": "Transaction codes", "Value": "SU53"}, {"Key": "Transaction codes", "Value": "SM01"}, {"Key": "Transaction codes", "Value": "SU56"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D019687)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D021767)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000023342/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000023342/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000023342/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000023342/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000023342/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000023342/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000023342/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000023342/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000023342/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "675771", "RefComponent": "BC-SEC-AUT-PFC", "RefTitle": "Deletion of the profile parameter auth/check_value_write_on", "RefUrl": "/notes/675771"}, {"RefNumber": "307641", "RefComponent": "BC-DWB-SEM", "RefTitle": "SU53 does not display TCODE checks which fail", "RefUrl": "/notes/307641"}, {"RefNumber": "28175", "RefComponent": "BC-SEC-USR-ADM", "RefTitle": "Questions regarding the authorization concept", "RefUrl": "/notes/28175"}, {"RefNumber": "198079", "RefComponent": "PM-EQM", "RefTitle": "No check of authorization S_TCODE for CALL TRANSACTION", "RefUrl": "/notes/198079"}, {"RefNumber": "18529", "RefComponent": "BC-SEC-AUT", "RefTitle": "Which authorization objects are checked ?", "RefUrl": "/notes/18529"}, {"RefNumber": "1412800", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX and BW authorizations", "RefUrl": "/notes/1412800"}, {"RefNumber": "109154", "RefComponent": "BC-SEC-AUT", "RefTitle": "SU01: Resetting user buffers", "RefUrl": "/notes/109154"}, {"RefNumber": "102308", "RefComponent": "BC-SEC-AUT", "RefTitle": "Exclusive lock waits on table USRBF", "RefUrl": "/notes/102308"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1412800", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX and BW authorizations", "RefUrl": "/notes/1412800 "}, {"RefNumber": "675771", "RefComponent": "BC-SEC-AUT-PFC", "RefTitle": "Deletion of the profile parameter auth/check_value_write_on", "RefUrl": "/notes/675771 "}, {"RefNumber": "307641", "RefComponent": "BC-DWB-SEM", "RefTitle": "SU53 does not display TCODE checks which fail", "RefUrl": "/notes/307641 "}, {"RefNumber": "18529", "RefComponent": "BC-SEC-AUT", "RefTitle": "Which authorization objects are checked ?", "RefUrl": "/notes/18529 "}, {"RefNumber": "28175", "RefComponent": "BC-SEC-USR-ADM", "RefTitle": "Questions regarding the authorization concept", "RefUrl": "/notes/28175 "}, {"RefNumber": "109154", "RefComponent": "BC-SEC-AUT", "RefTitle": "SU01: Resetting user buffers", "RefUrl": "/notes/109154 "}, {"RefNumber": "198079", "RefComponent": "PM-EQM", "RefTitle": "No check of authorization S_TCODE for CALL TRANSACTION", "RefUrl": "/notes/198079 "}, {"RefNumber": "102308", "RefComponent": "BC-SEC-AUT", "RefTitle": "Exclusive lock waits on table USRBF", "RefUrl": "/notes/102308 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "31I", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46B", "To": "46D", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "640", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 46B", "SupportPackage": "SAPKB46B35", "URL": "/supportpackage/SAPKB46B35"}, {"SoftwareComponentVersion": "SAP_BASIS 46B", "SupportPackage": "SAPKB46B25", "URL": "/supportpackage/SAPKB46B25"}, {"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C15", "URL": "/supportpackage/SAPKB46C15"}, {"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C26", "URL": "/supportpackage/SAPKB46C26"}, {"SoftwareComponentVersion": "SAP_BASIS 46D", "SupportPackage": "SAPKB46D15", "URL": "/supportpackage/SAPKB46D15"}, {"SoftwareComponentVersion": "SAP_BASIS 46D", "SupportPackage": "SAPKB46D06", "URL": "/supportpackage/SAPKB46D06"}, {"SoftwareComponentVersion": "SAP_BASIS 610", "SupportPackage": "SAPKB61007", "URL": "/supportpackage/SAPKB61007"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 2, "URL": "/corrins/0000023342/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46B", "ValidTo": "610", "Number": "23342 ", "URL": "/notes/23342 ", "Title": "No authorization ... --> analysis (also for external users)", "Component": "BC-SEC-USR-ADM"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}