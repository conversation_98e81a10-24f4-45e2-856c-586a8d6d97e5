{"Request": {"Number": "721081", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 417, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000003878962017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000721081?language=E&token=F92CF15ED34F45CC739789945389D124"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000721081", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000721081/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "721081"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 16}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "19.07.2016"}, "SAPComponentKey": {"_label": "Component", "value": "PY-PT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Portugal"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Portugal", "value": "PY-PT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-PT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "721081 - HR-PT:Legal Changes to the Social Security number(11 digits)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>According to new portuguese law defined by \"Portaria n&#176;1360/2003\" of December 13th,2003 the social security numbers will be changed from<br />9 digits to 11 digits.<br /><br />In the solution delivered for each release by the first Support Packages<br />of this note some issues were found and are solved as described below.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SS ;11 digits ;Portaria n 1360/2003 ;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>VERSIONING</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Version</strong></td>\r\n<td><strong>Date</strong></td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td>16</td>\r\n<td>July 19, 2016</td>\r\n<td>SAR files were removed.</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>This note creates new tables in the dictionary so in case of release<br />migration it is mandatory to have installed the HR Support Packages<br />refered below in this note.<br /><br /><br />In order to allow the system to process the Social Security numbers with<br />the new format of 11 digits the following changes in tables were<br />necessary:</p>\r\n<ul>\r\n<li>table T5P2I was substituted by T5P2V which includes now validity date;</li>\r\n</ul>\r\n<ul>\r\n<li>table T5P2E was substituted by T5P2U with a key of 11 digits instead of 9 digits;</li>\r\n</ul>\r\n<ul>\r\n<li>table T5P0P will contain a new field \"C&#243;digo de sub&#225;rea SS\"(SSNSA).</li>\r\n</ul>\r\n<p><br />It means that references to the fields of table T5P2I and T5P2E will<br />be changed to use the fields of table T5P2V and t5P2U, respectively.<br /><br /><br />The table T5P2V(old T5P2I) remains as a SAP table, it means the entries<br />will be delivered together with the dictionary changes with validity<br />date according to the law. Also a new entry is being included in this<br />table with code 100 for \"Seguran&#231;a Social Nacional\". The table T5P2V was<br />converted from table T5P2I, but <strong>customers should have in attention</strong><br /><strong>if they have done any changes in their table T5P2I because of course</strong><br /><strong>it will not be included in the new table T5P2V.</strong><br /><br />Table T5P2U(old T5P2E) remains as a Customer table, it means the entries<br />will have to be migrated from table T5P2E into new table T5P2U.<br /><br />Table T5P0P has now a new field \"C&#243;digo de sub&#225;rea SS\"(SSNSA) which has<br />to be filled by the customer and is relevant to the Remuneration Sheet<br />report (RPCSSRP0 - Folha de remunera&#231;&#245;es).<br /><br /><br />When converting the Social Security numbers the customer needs to create a report and can use function module HR_P_SS_DIGIT_NUMBER in order to get the check digit number calculated, the input for this function module should have length of at least 10 digits, where first digit is '1' for employees and '2' for companies and the other 9 digits will be<br />the old 9 digits Social Security number.<br /><br /><br />The changes done in the Data Dictionary will be available by Advanced<br />Delivery according to the attached files(\"xxxxxx\" means numbers):<br /><br />L4DKxxxxxx_DDIC_45B.CAR - Release 4.5B<br />L6BKxxxxxx_DDIC_470.CAR - Release 4.70(Enterprise)<br />L9BKxxxxxx_DDIC_46B.CAR - Release 4.6B<br />L9CKxxxxxx_DDIC_46C.CAR - Release 4.6C<br /><br /><br />New Function Modules will be available by Advanced Delivery according to the attached files(\"xxxxxx\" means numbers):<br /><br />L4DKxxxxxx_FM_45B.CAR - Release 4.5B<br />L6BKxxxxxx_FM_470.CAR - Release 4.70(Enterprise)<br />L9BKxxxxxx_FM_46B.CAR - Release 4.6B<br />L9CKxxxxxx_FM_46C.CAR - Release 4.6C<br /><br /><br />Changes done in code will be available by Advanced Delivery according to the attached files(\"xxxxxx\" means numbers):<br /><br />L4DKxxxxxx_CODE_45B.CAR - Release 4.5B<br />L6BKxxxxxx_CODE_470.CAR - Release 4.70(Enterprise)<br />L9BKxxxxxx_CODE_46B.CAR - Release 4.6B<br />L9CKxxxxxx_CODE_46C.CAR - Release 4.6C<br /><br /><br />Report RPCSSRP0 has now a flag which still allow the customers to<br />generate the file for Social Security entity in the old format(RC3008).<br /><br /><br /><strong>STEPS SUMMARY:</strong><br /><br />1. Install Advance Delivery with Data Dictionary;<br />2. do the necessary table convertions and adjusts as described before;<br />3. change the customizing of feature PSSCN to have the new 11 digits of Social Security number for the new institution code \"100\";<br />4. install Advance Delivery with Function Modules;<br />5. install Advance Delivery or Correction Instructions with code change;<br />6. infotype 0332 which use Institution code and Social Security number have to be verified by the customer in order to have a new entry with 11 digits and National Social Security institution valid from the month the customer consider to start with the new process, the same process could be necessary to infotype 0021.<br /><br /><br /><strong>IMPORTANT:</strong><br />Be aware of an Advance Delivery delivers the last version of the object,<br />it means that if you do not have the last HR Support Package installed<br />in you system you could get errors, either Syntax Errors or process<br />errors. In this case the only option is to undo the changes from Advance<br />Delivery and do the changes manually according to the Correction<br />Instructions available in this note.<br /><br /><br />The correction described in this note is included in an HR Support<br />Package.<br /><br />The support package includes:</p>\r\n<ul>\r\n<li>data dictionary changes</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>new domains PPT_SSINS2, PPT_SSBEN2, PPT_SSCON2, PPT_SSNSA;</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>new data element PPT_SSINS2;</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>new tables PPTD0, PPTD1, PPTD2, PPTD3, T5P2U, T5P2V and change in tables PS0332, PS0335 and PMEP0;</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>new views V_T5P2U and V_T5P2V, view V_T5P0P was changed;</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>search help H_T5P2V;</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>function module changes</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>new function module HR_PT_SS_GET_CONTRIBUTOR_2004 and HR_PT_SS_GET_INSTITUTION_2004 (they are a copy of the old function modules HR_PT_SS_GET_CONTRIBUTOR and HR_PT_SS_GET_INSTITUTION);</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>function module HR_P_CHECK_SS_NUMBER and HR_P_SS_DIGIT_NUMBER;</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>new message code 105(3I) with text \"D&#237;gito verificador errado para n&#176; de seguran&#231;a social\";</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>code changes</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>report RPUTSVP0, RPUTSVPD, RPUTSVPF;</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>report RPCSSRP0, RPCSSRP1, RPCSSRPD, RPCSSRPS, RPCSSRPT;</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>report MP033210, MP033220, MP033230, MP033240;</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>report MP033510, MP033520, MP033530, MP033540;</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>screen changes</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>screen 3000 of report MP033200, the field P0332-SSNUM had the Visible Length changed from 10 to 11;</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>screen 2000 of report MP033200, field P0332-SSINS is not a Listbox anymore, it has now a new field next to it only for output that shows field T5P2V-SITXT which will contain the description corresponding to P0332-SSINS.</li>\r\n</ul>\r\n</ul>\r\n<p><br /><br />In the solution delivered for each release by the first Support Packages<br />of this note some issues were found and are solved as described below:</p>\r\n<ul>\r\n<li>an improvement was done, the phone number(field TELNO) of view V_T5P2U is now filled with zeros on the left;</li>\r\n</ul>\r\n<ul>\r\n<li>when an employee changes the subarea, if there were differences that refer to the old subarea they were being reported to the new subarea, please apply correction instruction 0120031469 0000312653;</li>\r\n</ul>\r\n<ul>\r\n<li>a new line of code was included to allow the generation of different files for different institutions even if the contribution number used for the private institutions is the same of Nacional Social Security, correction instruction 0120031469 0000310474;</li>\r\n</ul>\r\n<ul>\r\n<li>field SSINS of table T5P0P was not been checked against the new table T5P2V, it will be delivered in the next Support Package;</li>\r\n</ul>\r\n<ul>\r\n<li>for release 4.5B and 4.6B the Social Security number was always saved in the cluster table SS, but not shown with 11 digits, a correction in the code is provided in the correction instructions 0120031469 0000310387 for 4.6B and 0120031469 0000310382 for 4.5B;</li>\r\n</ul>\r\n<ul>\r\n<li>for all releases the old format file did not show the tax number(NIF), solution for this is the correction instruction 0120061532 0000654760;</li>\r\n</ul>\r\n<ul>\r\n<li>for release 4.5B the new view V_T5P2U had field NPO wrongly as a Radio Botton instead of as Check Box, it is solved and provided in Advanced Delivery via the attached file L4DK113424.CAR;</li>\r\n</ul>\r\n<ul>\r\n<li>for release 4.70(Enterprise) the old format file had the records one after the other without starting a new line, solution for this is the correction instruction 0120061532 0000654106;</li>\r\n</ul>\r\n<ul>\r\n<li>for release 4.70(Enterprise) the process of new field SSNSA of view V_T5P0P was missing in the automatically generated code, it will be delivered included in note 731753, but the correction can be done in include L0PPAF00 now manually as described in the following code.</li>\r\n</ul>\r\n<p><br />FORM GET_DATA_V_T5P0P.<br />...<br />V_T5P0P-SANPO =<br />T5P0P-SANPO .<br />V_T5P0P-SSNSA =&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&lt;--- insert this line<br />T5P0P-SSNSA .&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&lt;--- insert this line<br />&#160;&#160;&#160;&#160;&#160;&#160;SELECT SINGLE * FROM T5P2I WHERE<br />...<br />ENDFORM.<br /><br />FORM DB_UPD_V_T5P0P .<br />...<br />T5P0P-SANPO =<br />V_T5P0P-SANPO .<br />T5P0P-SSNSA =&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&lt;--- insert this line<br />V_T5P0P-SSNSA .&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&lt;--- insert this line<br />&#160;&#160;&#160;&#160;&#160;&#160;IF SY-SUBRC = 0.<br />&#160;&#160;&#160;&#160;&#160;&#160;UPDATE T5P0P .<br />...<br />ENDFORM.<br /><br />FORM READ_SINGLE_ENTRY_V_T5P0P.<br />...<br />V_T5P0P-SANPO =<br />T5P0P-SANPO .<br />V_T5P0P-SSNSA =&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&lt;--- insert this line<br />T5P0P-SSNSA .&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&lt;--- insert this line<br />&#160;&#160;&#160;&#160;&#160;&#160;SELECT SINGLE * FROM T5P2I WHERE<br />...<br />&#160;&#160;&#160;&#160;&#160;&#160;CLEAR V_T5P0P-SAACT .<br />&#160;&#160;&#160;&#160;&#160;&#160;CLEAR V_T5P0P-SANPO .<br />&#160;&#160;&#160;&#160;&#160;&#160;CLEAR V_T5P0P-SSNSA .&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &lt;--- insert this line<br />&#160;&#160;&#160;&#160;&#160;&#160;CLEAR V_T5P0P-SITXT .<br />&#160;&#160;&#160;&#160;&#160;&#160;CLEAR V_T5P0P-SVTXT .<br />...<br />ENDFORM.<br /><br />FORM COMPL_V_T5P0P USING WORKAREA.<br />...<br />T5P0P-SANPO =<br />V_T5P0P-SANPO .<br />T5P0P-SSNSA =&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&lt;--- insert this line<br />V_T5P0P-SSNSA .&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&lt;--- insert this line<br />&#160;&#160;&#160;&#160;&#160;&#160;SELECT SINGLE * FROM T5P2I WHERE<br />...<br />ENDFORM.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I823284)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I827735)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000721081/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000721081/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000721081/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000721081/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000721081/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000721081/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000721081/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000721081/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000721081/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 45B", "SupportPackage": "SAPKE45BB2", "URL": "/supportpackage/SAPKE45BB2"}, {"SoftwareComponentVersion": "SAP_HR 45B", "SupportPackage": "SAPKE45BB3", "URL": "/supportpackage/SAPKE45BB3"}, {"SoftwareComponentVersion": "SAP_HR 45B", "SupportPackage": "SAPKE45BB4", "URL": "/supportpackage/SAPKE45BB4"}, {"SoftwareComponentVersion": "SAP_HR 46B", "SupportPackage": "SAPKE46B96", "URL": "/supportpackage/SAPKE46B96"}, {"SoftwareComponentVersion": "SAP_HR 46B", "SupportPackage": "SAPKE46B95", "URL": "/supportpackage/SAPKE46B95"}, {"SoftwareComponentVersion": "SAP_HR 46B", "SupportPackage": "SAPKE46B94", "URL": "/supportpackage/SAPKE46B94"}, {"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46C87", "URL": "/supportpackage/SAPKE46C87"}, {"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46C86", "URL": "/supportpackage/SAPKE46C86"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47032", "URL": "/supportpackage/SAPKE47032"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47031", "URL": "/supportpackage/SAPKE47031"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47041", "URL": "/supportpackage/SAPKE47041"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50002", "URL": "/supportpackage/SAPKE50002"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50003", "URL": "/supportpackage/SAPKE50003"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_HR", "NumberOfCorrin": 18, "URL": "/corrins/0000721081/2"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 18, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 9, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "721081 ", "URL": "/notes/721081 ", "Title": "HR-PT:Legal Changes to the Social Security number(11 digits)", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "457989 ", "URL": "/notes/457989 ", "Title": "HR-PT: Remuneration Sheet relating to December/2001 in EURO", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "470", "Number": "532143 ", "URL": "/notes/532143 ", "Title": "HR-PT: Some corrections to Remuneration Sheet", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "470", "Number": "606082 ", "URL": "/notes/606082 ", "Title": "HR-PT: Record type for Attach. J - RPCAIDP0", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "470", "Number": "696406 ", "URL": "/notes/696406 ", "Title": "HR-PT: Legal Changes to the Social Security number", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "721081 ", "URL": "/notes/721081 ", "Title": "HR-PT:Legal Changes to the Social Security number(11 digits)", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "318150 ", "URL": "/notes/318150 ", "Title": "New buffer option of the ALV", "Component": "BC"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "436529 ", "URL": "/notes/436529 ", "Title": "HR-PT: Corrections to Redesign of Remuneration Sheet", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "721081 ", "URL": "/notes/721081 ", "Title": "HR-PT:Legal Changes to the Social Security number(11 digits)", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "563598 ", "URL": "/notes/563598 ", "Title": "HR-PT: Incidents with the Download functionality", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "648574 ", "URL": "/notes/648574 ", "Title": "HR-PT:Files downloaded are not filled with spaces in the end", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "721081 ", "URL": "/notes/721081 ", "Title": "HR-PT:Legal Changes to the Social Security number(11 digits)", "Component": "PY-PT"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}