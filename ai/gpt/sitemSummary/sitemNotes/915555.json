{"Request": {"Number": "915555", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 498, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005289062017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000915555?language=E&token=5920F94CD27A277BEE4081F47289A3C5"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000915555", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000915555/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "915555"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "29.03.2006"}, "SAPComponentKey": {"_label": "Component", "value": "FI-GL-GL-F"}, "SAPComponentKeyText": {"_label": "Component", "value": "Value Added Tax (VAT)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Accounting", "value": "FI-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "FI-GL-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Value Added Tax (VAT)", "value": "FI-GL-GL-F", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-GL-F*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "915555 - Elec tax return special advance pymt and permanent extension"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note describes the delivery of the function for the electronic dispatch of the special advance payment and the permanent extension for Germany.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>ELSTER, SVZ, UVA</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>*</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>To dispatch the special advance payment and the permanent extension, the program FOT_B2A_SAP is delivered, which you can also call using Transaction FOTP.<br /><br />A prerequisite is that you assign the tax on sales/purchases group to the company codes, with which you can define the special advance payment.&#x00A0;&#x00A0;(See for example Customizing Transaction OBCJ, Tax on sales/purchases group maintenance)<br />The data created is sent using Transaction FOTV in the same way as the advance return for tax on sales/purchases and logged as follows:<br />Special advance payment message type '0011'<br />Permanent extension message type '0012'.<br /><br />You must create dispatch Customizing for the new message types '0011' and '0012' using Transaction FOTED1, where the executive programs use the control parameters to dispatch from the UVA.<br />You can maintain the dispatch parameters using Transaction FOTED2, in which the UVA parameters are identical, that is, have already been maintained.<br />For the special advance payment, you can maintain the following parameters independently of the UVA:<br />'Clearing of the refund' and<br />'Cancellation of the collection authorization'.<br /><br />INFORMATION:<br />At the moment, only the solution for the Business Connector is supported for Release ERP 2005.<br />The note will be enhanced for Releases 4.0, 4.5 and 4.6B by February 3, 2005.<br /><br /><br /><br />Installation:<br /><br /><STRONG>CAUTION:</STRONG><br /><STRONG>A transport file that contains all the required corrections from this note and requires only manual changes in the dictionary is available in Note 920578 as a pilot note. (February 2, 2006). You can log in as a pilot customer when you open a problem message.<br />Note 922097 is available for Release 4.6B and 4.5B with the relevant transport files.<br /></STRONG><br /><br />Prerequisite:<br /><br />You must have implemented Note 906384.<br />Before you implement Note 906384, you must have implemented the corrections from Note 814603 or the relevant Support Package. For technical reasons, you must also import the enhancement for the Netherlands (Note 899851).<br />You must also have implemented Note 901107 in advance.<br /><br />Use Transaction SE38 to create the program FOT_B2A_SAP.<br />Title: Generation of the electronic special advance payment/permanent ex tension<br />Category: Executable program<br />Status: Live SAP standard program<br />Application: Financial accounting<br />Package: FOT_EDECLARATION<br />Deactivate the switch 'Fixed point arithmetic'.<br /><br />Save the program.<br /><br />Implement the attached correction instructions.<br /><br /><br />In addition, you must carry out the following manual tasks:<br /></p> <OL>1. Use Transaction SE91 to create and change the messages for the message class FOT_B2A: (To maintain long texts: Add the placeholders &amp;V1&amp; and &amp;V2&amp; to the long text by choosing \"Edit -&gt; Command -&gt; Insert command -&gt; Symbols\" in the long text of the message, or replace the editor)</OL> <OL><OL>a) Create the message FOT_B2A503 Short text: Company code &amp;1 is not set up for electronic tax returnDeactivate the indicator \"Self-explanatory\"<br /><br />Maintain the following long text: <br /><br />Diagnosis The leading company code &amp;V1&amp; is not created for the reporting category with the reporting country.<br />System responses<br />The electronic message is not created.<br /><br />Procedure Create the relevant Customizing data for the reporting category &amp;V3&amp; and reporting country&#x00A0;&#x00A0;for the leading company code .</OL></OL> <OL><OL>b) Create the message FOT_B2A701 Short text: Message type &amp;1 transfers the parameter / control of the message type 0001Deactivate the indicator \"Self-explanatory\"<br /><br />Maintain the following long text: <br /><br />Diagnosis Create the message type &amp;V1&amp;.No control data attributes can be maintained for the message type.<br /><br /><br />System responses<br />The system hides the parameters, since the programs using them automatically access the control data of the message type '0001'. You must maintain the parameter data for the message type &amp;V1&amp;, but we recommend you use the same parameter data as in the message type '0001'.<br />Procedure Create the control data for the message type &amp;V1&amp;, for which you do not have to define any attributes (Mode of transfer, time span).<br />Maintain the parameter data for the message type &amp;V1&amp;. Maintain the same entries here.Maintain the entries:<br />Cancellation of the collection authorization<br />and clearing of the refund<br />individually for the special advance payment.</OL></OL> <OL>2. Use Transaction SE11 to maintain the following data in the dictionary:</OL> <OL><OL>a) Domain:&#x00A0;&#x00A0;FOT_DCLTYP<br />Add the following values in the valuation area:<br />0011 elec. logon to the special advance payment<br />0012 elec. logon to permanent extension<br /><br />Activate the domain.</OL></OL> <OL><OL>b) Structure / data type FOT_S_DECL_HELP</OL></OL> <OL><OL>c) Add the following field at the end of the structure:<br /><br />Field&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Data type / Data element<br />ADVANCED_PAYM&#x00A0;&#x00A0; FOT_DECAMT<br /><br />Save and activate the structure.</OL></OL> <OL><OL>d) Structure / data type FOT_S_SVZL<br />Short text: List of the messages in FOT_B2A_SVZ<br />Package: FOT_EDECLARATION<br /><br />Field&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Data type/ Data element&#x00A0;&#x00A0;Reference tab Reference field<br /> TBUKRS FOT_TBUKRS</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DECL_TYPE &#x00A0;&#x00A0;&#x00A0;&#x00A0;FOT_DCLTYP<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DECL_CTRY&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;FOT_DCCTRY<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DECL_YEAR &#x00A0;&#x00A0;&#x00A0;&#x00A0;FOT_DEYEAR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DECL_PER &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;FOT_DCLPER<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DECL_RUNID &#x00A0;&#x00A0;&#x00A0;&#x00A0;FOT_RUNID<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ORGTXT &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;FOT_ORGTXT<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ADVANCED_PAYM FOT_DECAMT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; FOT_S_SVZL&#x00A0;&#x00A0;DECL_CURR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DECL_CURR FOT_CURR<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Save and activate the structure.<br /></p> <OL>3. Create the transaction code FOTP:</OL> <OL><OL>a) Procedure: Call Transaction SE93<br /><br />Enter the transaction code FOTP in the next screen and choose 'Create'.<br /><br />In the next screen, enter:<br />Short text: Create electronic special advance payment<br />Initial object: Choose report transaction and confirm your entries.<br /><br />On the next screen, enter:<br />Program: FOT_B2A_SAP<br /><br />and set the GUI capability for all selections.<br /><br />Save your data.</OL></OL> <OL>4. Use Transaction SE38 to create the following text elements and selection texts for the program FOT_B2A_SAP:<br /></OL> <OL><OL>a) Text elements:<br /><B>301</B> - 'Special advance payment not maintained for tax on sales/purchases group - no reporting data created'<br /><B>302</B> - 'Tax on sales/purchases group not maintained- no reporting data created':<br /><B>F01</B> - 'Selection data'<br /><B>F02</B> -&#x00A0;&#x00A0;'Electronic advance return'<B></B><br /><B></B><B>F03</B> - 'Test function'<br /><B>F04</B> - 'Output control'<br /></OL></OL> <OL><OL>b) Selection texts:<br /><B>PAR_TEST</B> - 'Execute test run'<br /><B>PAR_XDFV</B> - 'Permanent extension request'<br /><B>PAR_XSVZ</B> - 'Special advance payment'<br /><B>PA_CORR</B> - 'Adjusted Advance Tax Return'<br /><B>PA_LSTM</B> - 'Country for tax return'<br /><B>PA_RJAHR -</B><B>'Reporting year'</B><B></B><br /><B></B><B>PA_VARI</B> - 'Layout'<br /><B>SO_ORG - </B><B>'Taxes on sales/purchases group'</B><B></B></OL></OL> <OL><OL>c) As a program short text, enter the attributes:<br />'Generation of the electronic special advance payment / permanent extension'<br /><br />Activate the program.</OL></OL> <OL>5. Create the following cross-client parameters for the table FOTPARAMC:<br /><br />Procedure: Call Transaction SE16, table FOTPARAMC<br />and choose 'Create'.<br />1. Parameter<br />Enter and save the following data:<br />DECL_PARA : TAXDECL_REVOKE_COLLECTION_AUT<br />PARTEXT:&#x00A0;&#x00A0;&#x00A0;&#x00A0;'Indicator revoke collection authorization adv.paym'<br /><br />2. Parameter:<br />DECL_PARA:&#x00A0;&#x00A0;TAXDECL_CLEARING_REFUND_IND1<br />PARTEXT:&#x00A0;&#x00A0;&#x00A0;&#x00A0;'Indicator clearing refund advanced payment'</OL> <OL>6. To maintain the parameters of the table FOTPARREFC, attach the existing program Z_FOTPARREFC_0011 as an executable test program in the development class $TMP and execute it.<br /><br />Then use Transaction SE16 to select the message types '0011' and '0012' and transfer them to your transport request.<br />Select all entries --&gt; Table entries menu --&gt; 'Transport entries'.<br /><br /><br /><B>Note on the authorization concept</B></OL> <p>The authorization object to create logon data for the special advance payment or permanent extension is 'FOT_B2A_V'. This must be assigned to you. The authorization object checks the objects 'company code' and 'message type'. Maintain the following message types:</p> <UL><LI>0011 Special advance payment</LI></UL> <UL><LI>0012 Permanent extension</LI></UL> <p>To create a message, you must be assigned the activity 01 (Create). <B> You are not referred to missing authorizations</B>. If you are missing authorizations for all selected company codes, Transaction FOTP terminates with an empty output list with the message \"List contains no data\".</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D000339"}, {"Key": "Processor                                                                                           ", "Value": "D000339"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000915555/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000915555/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000915555/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000915555/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000915555/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000915555/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000915555/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000915555/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000915555/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Z_fotparrefc_0011.txt", "FileSize": "4", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000047252006&iv_version=0009&iv_guid=39FD45780C226749857BE253D30514F6"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "922097", "RefComponent": "FI-GL-GL-F", "RefTitle": "Special advance payment delivery as a transport request", "RefUrl": "/notes/922097"}, {"RefNumber": "921251", "RefComponent": "FI-GL-GL-F", "RefTitle": "ELSTER special advance payment with value zero", "RefUrl": "/notes/921251"}, {"RefNumber": "920578", "RefComponent": "FI-GL-GL-F", "RefTitle": "Delivery special advance payment as a transport request", "RefUrl": "/notes/920578"}, {"RefNumber": "906384", "RefComponent": "FI-GL-GL-F", "RefTitle": "FOTV: Administration report revision", "RefUrl": "/notes/906384"}, {"RefNumber": "904153", "RefComponent": "FI-GL-GL-F", "RefTitle": "Electr tax return - Enhancement electr adv VAT report 2006", "RefUrl": "/notes/904153"}, {"RefNumber": "901107", "RefComponent": "FI-GL-GL-F", "RefTitle": "Adj of electronic advance ret for tax on sls/purchs for 2006", "RefUrl": "/notes/901107"}, {"RefNumber": "899851", "RefComponent": "XX-CSC-NL-FI", "RefTitle": "NL: Electronic VAT & EC Sales list", "RefUrl": "/notes/899851"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1305039", "RefComponent": "FI-GL-GL-F", "RefTitle": "Return for special advance payment of TSP in ERP 6.0", "RefUrl": "/notes/1305039"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1978293", "RefComponent": "FI-GL-GL-F", "RefTitle": "FOTP - special advance payment with time-dependent taxes on sales/purchases groups", "RefUrl": "/notes/1978293 "}, {"RefNumber": "920578", "RefComponent": "FI-GL-GL-F", "RefTitle": "Delivery special advance payment as a transport request", "RefUrl": "/notes/920578 "}, {"RefNumber": "1804765", "RefComponent": "FI-GL-GL-F", "RefTitle": "Authentication of special advance payment 2013", "RefUrl": "/notes/1804765 "}, {"RefNumber": "1305039", "RefComponent": "FI-GL-GL-F", "RefTitle": "Return for special advance payment of TSP in ERP 6.0", "RefUrl": "/notes/1305039 "}, {"RefNumber": "906384", "RefComponent": "FI-GL-GL-F", "RefTitle": "FOTV: Administration report revision", "RefUrl": "/notes/906384 "}, {"RefNumber": "899851", "RefComponent": "XX-CSC-NL-FI", "RefTitle": "NL: Electronic VAT & EC Sales list", "RefUrl": "/notes/899851 "}, {"RefNumber": "901107", "RefComponent": "FI-GL-GL-F", "RefTitle": "Adj of electronic advance ret for tax on sls/purchs for 2006", "RefUrl": "/notes/901107 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "904153", "RefComponent": "FI-GL-GL-F", "RefTitle": "Electr tax return - Enhancement electr adv VAT report 2006", "RefUrl": "/notes/904153 "}, {"RefNumber": "921251", "RefComponent": "FI-GL-GL-F", "RefTitle": "ELSTER special advance payment with value zero", "RefUrl": "/notes/921251 "}, {"RefNumber": "922097", "RefComponent": "FI-GL-GL-F", "RefTitle": "Special advance payment delivery as a transport request", "RefUrl": "/notes/922097 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B87", "URL": "/supportpackage/SAPKH40B87"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B65", "URL": "/supportpackage/SAPKH45B65"}, {"SoftwareComponentVersion": "SAP_APPL 46B", "SupportPackage": "SAPKH46B60", "URL": "/supportpackage/SAPKH46B60"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C52", "URL": "/supportpackage/SAPKH46C52"}, {"SoftwareComponentVersion": "SAP_APPL 470", "SupportPackage": "SAPKH47026", "URL": "/supportpackage/SAPKH47026"}, {"SoftwareComponentVersion": "SAP_APPL 500", "SupportPackage": "SAPKH50012", "URL": "/supportpackage/SAPKH50012"}, {"SoftwareComponentVersion": "SAP_APPL 600", "SupportPackage": "SAPKH60004", "URL": "/supportpackage/SAPKH60004"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 4, "URL": "/corrins/0000915555/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 6, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "46B", "ValidTo": "46C", "Number": "813876 ", "URL": "/notes/813876 ", "Title": "Electr tax return: Special char in fields for contact person", "Component": "FI-GL-GL-F"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46B", "ValidTo": "46C", "Number": "901107 ", "URL": "/notes/901107 ", "Title": "Adj of electronic advance ret for tax on sls/purchs for 2006", "Component": "FI-GL-GL-F"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "46C", "Number": "795961 ", "URL": "/notes/795961 ", "Title": "Incorrect decoding of the data part", "Component": "FI-GL-GL-F"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "46C", "Number": "797910 ", "URL": "/notes/797910 ", "Title": "Electronic advance return for tax on sales/purch. for 2005", "Component": "FI-GL-GL-F"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "46C", "Number": "829645 ", "URL": "/notes/829645 ", "Title": "ELSTER with a country key that is not 'DE' for Germany", "Component": "FI-GL-GL-F"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "470", "ValidTo": "470", "Number": "795961 ", "URL": "/notes/795961 ", "Title": "Incorrect decoding of the data part", "Component": "FI-GL-GL-F"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "470", "ValidTo": "470", "Number": "797910 ", "URL": "/notes/797910 ", "Title": "Electronic advance return for tax on sales/purch. for 2005", "Component": "FI-GL-GL-F"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "470", "ValidTo": "500", "Number": "813876 ", "URL": "/notes/813876 ", "Title": "Electr tax return: Special char in fields for contact person", "Component": "FI-GL-GL-F"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "470", "ValidTo": "500", "Number": "829645 ", "URL": "/notes/829645 ", "Title": "ELSTER with a country key that is not 'DE' for Germany", "Component": "FI-GL-GL-F"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "470", "ValidTo": "600", "Number": "871151 ", "URL": "/notes/871151 ", "Title": "FOTV: Name of the contractor missing in formatted display", "Component": "FI-GL-GL-F"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "470", "ValidTo": "600", "Number": "901107 ", "URL": "/notes/901107 ", "Title": "Adj of electronic advance ret for tax on sls/purchs for 2006", "Component": "FI-GL-GL-F"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "500", "ValidTo": "500", "Number": "795961 ", "URL": "/notes/795961 ", "Title": "Incorrect decoding of the data part", "Component": "FI-GL-GL-F"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "500", "ValidTo": "500", "Number": "797910 ", "URL": "/notes/797910 ", "Title": "Electronic advance return for tax on sales/purch. for 2005", "Component": "FI-GL-GL-F"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "921251", "RefTitle": "ELSTER special advance payment with value zero", "RefUrl": "/notes/0000921251"}]}}}}}