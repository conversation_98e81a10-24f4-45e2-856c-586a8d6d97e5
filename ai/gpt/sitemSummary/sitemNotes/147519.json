{"Request": {"Number": "147519", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 323, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014667192017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000147519?language=E&token=3F3BD4B7218FC80C11566B48A4385AC9"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000147519", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000147519/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "147519"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 179}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.03.2024"}, "SAPComponentKey": {"_label": "Component", "value": "BC-FES-GUI"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP GUI for Windows"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Frontend Services (SAP Note 1322184)", "value": "BC-FES", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-FES*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP GUI for Windows", "value": "BC-FES-GUI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-FES-GUI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "147519 - Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><strong>Preliminary remarks</strong></p>\r\n<ul>\r\n<li>This SAP Note explains the maintenance strategy for SAP GUI for Windows and SAP GUI for Java</li>\r\n<li>Pay attention to the hardware requirements (see <a target=\"_blank\" href=\"/notes/26417\">SAP Note 26417</a>)</li>\r\n<li>The list of supported operating systems can be found in <a target=\"_blank\" href=\"/notes/66971\">SAP Notes 66971</a> (SAP GUI for Windows), <a target=\"_blank\" href=\"/notes/146505\">SAP Note 146505</a>&#160;(SAP GUI for Java)</li>\r\n<li>You can also find a support platform matrix on SAP Community at&#160;<a target=\"_blank\" href=\"https://community.sap.com/topics/gui\" rel=\"noreferrer noopener\" tabindex=\"-1\" title=\"https://community.sap.com/topics/gui\">https://community.sap.com/topics/gui</a>&#160;or in the Product Availability Matrix <a target=\"_blank\" href=\"http://service.sap.com/pam\">http://service.sap.com/pam</a>.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Frontend, GUI, maintenance, patch, upgrade, installation, Windows, JAVA,&#160;support, deadlines, release, 7.40, 740, 7.50, 750, 7.60, 760, 770, 7.70, 8.00, 800</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><strong>Important remarks:</strong></p>\r\n<ul>\r\n<li>A successor to SAP GUI for Windows 8.00 is currently being developed. The delivery of this next release is not expected before Q3/2025 (this may be subject to change). Updates will be published here.</li>\r\n<li>End of support for SAP GUI for Windows 7.70 approaching: On 09.04.2024 support for SAP GUI for Windows 7.70 will end (no extended support available). Our recommendation for all customers presently using 7.70 or an older release is to urgently plan the upgrade to SAP GUI for Windows 8.00.</li>\r\n<li>SAP GUI for Windows 8.00 was shipped&#160;on 27th of January 2023</li>\r\n<li>SAP GUI for Windows 7.70 was shipped on 29th of January 2021&#160;</li>\r\n<li>SAP GUI for Windows 7.60 went out of support on 12th of April 2022, please upgrade</li>\r\n<li>SAP GUI for Windows 7.50 went out of support on 31st of March 2020, please upgrade</li>\r\n<li>SAP GUI for Java 7.80 was shipped on 19th September 2022</li>\r\n<li>SAP GUI for Java 7.70 was shipped on 16th of March 2021 and went out of support on&#160;31st of March 2023,&#160;please upgrade</li>\r\n<li>SAP GUI for Java 7.50 was shipped on 14th of&#160;August 2017 and&#160;went out of support on 30th of September 2021, please upgrade</li>\r\n<li>You can subscribe to <a target=\"_blank\" href=\"/notes/1053737\">SAP Note 1053737</a> to get actively informed in case of updates regarding the SAP GUI for Windows patch delivery. For SAP GUI for Java, have a look at the SCN WIKI:&#160;<a target=\"_blank\" href=\"https://wiki.scn.sap.com/wiki/display/ATopics/SAP+GUI+for+Windows%3A+Important+News\">https://wiki.scn.sap.com/wiki/display/ATopics/Notification+on+SAP+GUI+for+Java+News</a></li>\r\n<li>All releases not listed in this SAP Note are not or no longer supported</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>SAP GUI for Windows Support Deadlines</strong></p>\r\n<ul>\r\n<li>7.40: No longer supported since 30th of June 2018</li>\r\n<li>7.50: No longer supported since 31st of March 2020</li>\r\n<li>7.60: No longer supported since 12th of April 2022</li>\r\n<li>7.70: Full support up to 9th of April 2024</li>\r\n<li>8.00: Full support up to 12th of January 2027 (delivery on 27th of January 2023)</li>\r\n</ul>\r\n<p><strong>SAP GUI for Java Support Deadlines</strong></p>\r\n<ul>\r\n<li>7.70:&#160;Out of support since 31th of March 2023</li>\r\n<li>7.80: Full support up to 31th of March 2025 <br />(Support typically ends about 6 months after shipment of the succeeding major release)</li>\r\n<li>A successor to SAP GUI for Java 7.80 is currently being developed. The delivery of this next release is not expected before Q4/2024 (subject to change).</li>\r\n</ul>\r\n<p>SAP generally&#160;publishes&#160;compatible successors to SAP GUI releases which are about to reach end of support, well before the end of support is reached.</p>\r\n<p><strong>Frequently asked questions</strong></p>\r\n<p><strong>Support for specific operating systems</strong></p>\r\n<p>Have a look at SAP Notes <a target=\"_blank\" href=\"/notes/66971\">66971</a> (SAP GUI for Windows), <a target=\"_blank\" href=\"/notes/146505\">146505</a>&#160;(SAP GUI for Java) or SAP Community at&#160;<a target=\"_blank\" href=\"https://community.sap.com/topics/gui\" rel=\"noreferrer noopener\" tabindex=\"-1\" title=\"https://community.sap.com/topics/gui\">https://community.sap.com/topics/gui</a>&#160;for more information.</p>\r\n<p><strong>Upgrading SAP GUI</strong></p>\r\n<p>If the SAP GUI version you are using is not or no longer supported, you can proceed as follows:</p>\r\n<ul>\r\n<li>Upgrade the SAP GUI on your PCs to a supported version. We recommend to change to the highest available patch of the latest release in an upgrade to save additional upgrades or updates later on.</li>\r\n<li>Use Terminal Servers and install a new SAP GUI version on the server.</li>\r\n</ul>\r\n<p><br /><strong>How are the SAP GUI support durations determined?</strong></p>\r\n<p>As of release 6.40 SAP&#160;ties the support duration for SAP GUI for Windows releases to the support duration for the development environment used. The reason for this is that we may require corrections from Microsoft for solving an issue in SAP GUI, but Microsoft does not provide us with corrections once support for a development environment is stopped.</p>\r\n<ul>\r\n<li>SAP GUI for Windows 7.60 was developed with Visual Studio 2017 which reached end of mainstream support by Microsoft on 12th of April 2022.&#160;Consequently SAP GUI for Windows 7.60 was supported until then.</li>\r\n<li>SAP GUI for Windows 7.70 was developed with Visual Studio 2019 which will reach end of mainstream support by Microsoft on 2nd of April 2024.&#160;Consequently SAP GUI for Windows 7.70 is supported until then.</li>\r\n<li>SAP GUI for Windows 8.00 was developed with Visual Studio 2022 which will reach end of mainstream support by Microsoft on 12th of January 2027.&#160;Consequently SAP GUI for Windows 8.00 is supported until then.</li>\r\n</ul>\r\n<p>You can be certain that as long as SAP supports any application based on Dynpro technology there will be a supported SAP GUI for Windows release.</p>\r\n<p><strong>Which version of SAP GUI should I use?</strong></p>\r\n<ul>\r\n<li>SAP recommends to always use the latest available version of SAP GUI when installing new clients. The reason for this recommendation is that only the latest releases / patches contain the most recent new features / corrections you might need. By using this approach you will effectively have to do fewer SAP GUI upgrades. If you do not want to install a new release of SAP GUI, you can keep using your current release (as long as this is supported), but you will not benefit from the new features available in newer releases (see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2796898\">2796898</a> for information on new features in release 7.70 and SAP Note <a target=\"_blank\" href=\"/notes/3075781\">3075781</a> for information on new features in release 8.00).</li>\r\n</ul>\r\n<ul>\r\n<li>Like almost any other software, SAP GUI may also contain security vulnerabilities. At SAP such vulnerabilities are addressed through SAP Security Notes. In case an SAP Security Notes was published for SAP GUI, we strongly recommend only using patches which contain the respective correction.<br /><br /></li>\r\n<li>You should also take into account that new platforms may only be supported with new SAP GUI releases. See SAP Notes <a target=\"_blank\" href=\"/notes/66971\">66971</a> and&#160;<a target=\"_blank\" href=\"/notes/146505\">146505</a>.<br /><br /></li>\r\n<li>SAP support may ask you to update to the latest available SAP GUI patch when you report a product issue affecting SAP GUI. There are circumstances under which this is required, because for example SAP support cannot reproduce the issue you reported or there is already an SAP Note that fits the symptom of your incident. In such cases it is typically sufficient to apply the latest available patch on a single affected workstation to check whether the correction solves the issue. This workstation can afterwards be downgraded to the formerly used patch again (uninstallation of SAP GUI required for downgrade).</li>\r\n</ul>\r\n<p><strong>Compatibility</strong></p>\r\n<p>Just like older SAP GUI releases SAP GUI for Windows 7.70 and the 32bit version of SAP GUI for Windows 8.00 are compatible with all releases of SAP products which are supported by SAP (this includes releases in extended maintenance and releases newer or equal to SAP R/3 3.1i in customer specific maintenance). However, the 64bit version of SAP GUI for Windows 8.00 and all subsequent releases are not compatible with old SAP systems using SAP_BASIS versions lower than 7.00. If a user connects to such a system with a 64bit version of SAP GUI for Windows, the logon attempt will be rejected. See SAP Note&#160;<a target=\"_blank\" href=\"/notes/3218166\">3218166</a> for more information on functional differences of the 32bit and 64bit versions.<br /><br /><strong>What does \"End of Support\" mean for SAP GUI?</strong></p>\r\n<ul>\r\n<li>Once an SAP GUI release has reached the end of support SAP no longer offers functional and security corrections for this release. Existing patches remain on SAP Support Portal for a while, but will eventually be removed.</li>\r\n<li>Messages reporting errors which only occur in a release that is out of support will be closed asking for an upgrade to a supported SAP GUI release. Issues which also occur in an SAP GUI release that is still supported will be analyzed, but only fixed in supported releases.</li>\r\n<li>Since SAP GUI is always compatible there is no extended or customer specific support for SAP GUI - you should therefore make sure that you have completed the upgrade to a supported SAP GUI release before end of support for your current SAP GUI release is reached.</li>\r\n</ul>\r\n<p><strong>What does \"Restricted Support\" mean for SAP GUI?</strong></p>\r\n<ul>\r\n<li>Support incidents are getting processed normally.</li>\r\n<li>Patches are supplied normally.</li>\r\n<li>Corrections for issues which can be corrected within SAP code are supplied normally.</li>\r\n<li>If an issue occurs that needs a correction from another vendor for a product that is no longer in maintenance (like&#160;Visual Studio from Microsoft) SAP will try to provide a workaround. If this also files, SAP will provide a correction within a patch for the latest available SAP GUI release. Therefore in such extraordinary situations the only solution may be to upgrade SAP GUI.</li>\r\n<li>Please note: Restricted support is only available under unusual circumstances (for example if the delivery of a new SAP GUI release needs to be delayed).</li>\r\n</ul>\r\n<p><strong>How do I get corrections for SAP GUI for Windows / SAP GUI for Java?</strong></p>\r\n<p>Corrections for both SAP GUI versions are shipped on SAP Support Portal (see SAP Note <a target=\"_blank\" href=\"/notes/563161\">563161</a> for more information on the download of these patches).<br />A collection of SAP GUI for Windows corrections is called a \"Patch\", while a collection of SAP GUI for Java corrections is called a \"Revision\". Both patches and revisions are cumulative, this means they contain all corrections from previous patches / revisions. Therefore to get all the latest corrections you only need to apply the latest available patch / revision.<br />For information on the availability of patches / revisions see SAP Note <a target=\"_blank\" href=\"/notes/1053737\">1053737</a> (SAP GUI for Windows) and <a target=\"_blank\" href=\"/notes/1229666\">1229666</a> (SAP GUI for Java).</p>\r\n<p><strong>SAP Community</strong></p>\r\n<p>For further information on the topic SAP GUI please refer to&#160;<a target=\"_blank\" href=\"https://community.sap.com/topics/gui\">https://community.sap.com/topics/gui</a>.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-FES-JAV (SAP GUI for JAVA)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D031909)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D019204)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000147519/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000147519/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000147519/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000147519/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000147519/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000147519/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000147519/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000147519/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000147519/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "97056", "RefComponent": "BC-SRV-ASF-CAL", "RefTitle": "Patch for SAPKALE for the Year 2000", "RefUrl": "/notes/97056"}, {"RefNumber": "968222", "RefComponent": "FIN-SEM-CPM", "RefTitle": "SEM 3.5 Front end patch 8 (July 2006)", "RefUrl": "/notes/968222"}, {"RefNumber": "950052", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/950052"}, {"RefNumber": "899881", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/899881"}, {"RefNumber": "889148", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/889148"}, {"RefNumber": "853535", "RefComponent": "BC-ABA-SC", "RefTitle": "Icons are displayed incorrectly", "RefUrl": "/notes/853535"}, {"RefNumber": "811239", "RefComponent": "CA-DMS", "RefTitle": "DMS: Displaying originals of a Document Info Record.", "RefUrl": "/notes/811239"}, {"RefNumber": "811071", "RefComponent": "FIN-SEM-CPM", "RefTitle": "SEM 3.5 Frontend Patch 6 (January 2005)", "RefUrl": "/notes/811071"}, {"RefNumber": "760646", "RefComponent": "FIN-SEM-CPM", "RefTitle": "SEM 3.5 front-end Support Package 5 (Aug.2004)", "RefUrl": "/notes/760646"}, {"RefNumber": "734171", "RefComponent": "FIN-SEM-CPM", "RefTitle": "SEM 3.5 front-end Support Package 3 (May 2004)", "RefUrl": "/notes/734171"}, {"RefNumber": "722513", "RefComponent": "BC-FES-OFFI", "RefTitle": "Desktop Office Integration: Maintenance information", "RefUrl": "/notes/722513"}, {"RefNumber": "710720", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/710720"}, {"RefNumber": "678193", "RefComponent": "FIN-SEM-CPM", "RefTitle": "SEM 3.5 frontend patch 2 (Nov.2003)", "RefUrl": "/notes/678193"}, {"RefNumber": "675156", "RefComponent": "SCM-TEC", "RefTitle": "SAP APO 3.0A SP26: Release and information note", "RefUrl": "/notes/675156"}, {"RefNumber": "66971", "RefComponent": "BC-FES-GUI", "RefTitle": "Supported SAP GUI platforms", "RefUrl": "/notes/66971"}, {"RefNumber": "645197", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/645197"}, {"RefNumber": "645114", "RefComponent": "FIN-SEM-CPM", "RefTitle": "SEM 3.1B front end patch 5 (July.2003)", "RefUrl": "/notes/645114"}, {"RefNumber": "641748", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 25 for APO Release 3.0A", "RefUrl": "/notes/641748"}, {"RefNumber": "618337", "RefComponent": "SCM-TEC", "RefTitle": "APO of Support Package 24 for APO Release 3.0A", "RefUrl": "/notes/618337"}, {"RefNumber": "612454", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/612454"}, {"RefNumber": "590056", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 23 for APO Release 3.0A", "RefUrl": "/notes/590056"}, {"RefNumber": "574373", "RefComponent": "FIN-SEM-CPM", "RefTitle": "SEM 3.1B front end package 4 (Nov.2002)", "RefUrl": "/notes/574373"}, {"RefNumber": "563161", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP GUI, SAP ITS, and SAP IGS patches and installation media in SAP ONE Support Launchpad", "RefUrl": "/notes/563161"}, {"RefNumber": "560595", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 22 for APO Release 3.0A", "RefUrl": "/notes/560595"}, {"RefNumber": "532619", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 21 for APO Release 3.0A", "RefUrl": "/notes/532619"}, {"RefNumber": "523987", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/523987"}, {"RefNumber": "517492", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 20 for APO Release 3.0A", "RefUrl": "/notes/517492"}, {"RefNumber": "513344", "RefComponent": "FIN-SEM-CPM", "RefTitle": "SEM 3.1B front end patch 1 (Apr.2002)", "RefUrl": "/notes/513344"}, {"RefNumber": "491882", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 19 for APO Release 3.0A", "RefUrl": "/notes/491882"}, {"RefNumber": "459985", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 18 for APO Release 3.0A", "RefUrl": "/notes/459985"}, {"RefNumber": "448518", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 17 for APO Release 3.0A", "RefUrl": "/notes/448518"}, {"RefNumber": "438337", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 16 for APO Release 3.0A", "RefUrl": "/notes/438337"}, {"RefNumber": "437237", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/437237"}, {"RefNumber": "426156", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/426156"}, {"RefNumber": "425173", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/425173"}, {"RefNumber": "424758", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 15 for APO Release 3.0A", "RefUrl": "/notes/424758"}, {"RefNumber": "423594", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 14 for APO Release 3.0A", "RefUrl": "/notes/423594"}, {"RefNumber": "422446", "RefComponent": "SCM-APO-OCX", "RefTitle": "APO front end patch", "RefUrl": "/notes/422446"}, {"RefNumber": "421757", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/421757"}, {"RefNumber": "415727", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 13 for APO Release 3.0A", "RefUrl": "/notes/415727"}, {"RefNumber": "415099", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/415099"}, {"RefNumber": "408248", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 12 for APO Release 3.0A", "RefUrl": "/notes/408248"}, {"RefNumber": "407355", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/407355"}, {"RefNumber": "402961", "RefComponent": "BC-FES-GRA", "RefTitle": "SEM 3.0A IGS front-end patch (18 May 2001)", "RefUrl": "/notes/402961"}, {"RefNumber": "399227", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 11 for APO Release 3.0A", "RefUrl": "/notes/399227"}, {"RefNumber": "398235", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/398235"}, {"RefNumber": "396644", "RefComponent": "FIN-SEM-CPM", "RefTitle": "SEM 3.0A Frontend patch 1 (May.2001)", "RefUrl": "/notes/396644"}, {"RefNumber": "391335", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 10 in APO Release 3.0A", "RefUrl": "/notes/391335"}, {"RefNumber": "390212", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/390212"}, {"RefNumber": "385483", "RefComponent": "SCM-APO", "RefTitle": "APO Support Package 13 for APO Release 2.0A", "RefUrl": "/notes/385483"}, {"RefNumber": "383890", "RefComponent": "SCM-APO", "RefTitle": "APO Support Package 9 for APO Release 3.0A", "RefUrl": "/notes/383890"}, {"RefNumber": "381953", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/381953"}, {"RefNumber": "379106", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/379106"}, {"RefNumber": "376378", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/376378"}, {"RefNumber": "369475", "RefComponent": "BC-SRV-ALV", "RefTitle": "List viewer: Front end hangs when you build a list", "RefUrl": "/notes/369475"}, {"RefNumber": "368306", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/368306"}, {"RefNumber": "368155", "RefComponent": "BC-SRV-ALV", "RefTitle": "List Viewer: Clipboard", "RefUrl": "/notes/368155"}, {"RefNumber": "367964", "RefComponent": "SCM-APO", "RefTitle": "APO Support Package 8 for APO Release 3.0A", "RefUrl": "/notes/367964"}, {"RefNumber": "366352", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/366352"}, {"RefNumber": "364437", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/364437"}, {"RefNumber": "362128", "RefComponent": "SCM-APO", "RefTitle": "APO Support Package 12 for APO Release 2.0A", "RefUrl": "/notes/362128"}, {"RefNumber": "355490", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/355490"}, {"RefNumber": "355487", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/355487"}, {"RefNumber": "352838", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/352838"}, {"RefNumber": "352614", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/352614"}, {"RefNumber": "337544", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/337544"}, {"RefNumber": "330728", "RefComponent": "BC-SRV-ALV", "RefTitle": "List Viewers: Expanding totals lines", "RefUrl": "/notes/330728"}, {"RefNumber": "325616", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/325616"}, {"RefNumber": "323560", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/323560"}, {"RefNumber": "3218166", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP GUI for Windows: Functional differences of the 64bit version compared to the 32bit version", "RefUrl": "/notes/3218166"}, {"RefNumber": "320347", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/320347"}, {"RefNumber": "3075781", "RefComponent": "BC-FES-GUI", "RefTitle": "New and changed features in SAP GUI for Windows 8.00", "RefUrl": "/notes/3075781"}, {"RefNumber": "30460", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP GUI: Compatibility with different SAP System releases", "RefUrl": "/notes/30460"}, {"RefNumber": "303751", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/303751"}, {"RefNumber": "2796898", "RefComponent": "BC-FES-GUI", "RefTitle": "New and changed features in SAP GUI for Windows 7.70", "RefUrl": "/notes/2796898"}, {"RefNumber": "26417", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP GUI Resources: Hardware and software", "RefUrl": "/notes/26417"}, {"RefNumber": "2600384", "RefComponent": "BC-FES-GUI", "RefTitle": "New and changed features in SAP GUI for Windows 7.60", "RefUrl": "/notes/2600384"}, {"RefNumber": "23414", "RefComponent": "BC-FES-GUI", "RefTitle": "SAPGUI on Windows 95", "RefUrl": "/notes/23414"}, {"RefNumber": "209188", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/209188"}, {"RefNumber": "208591", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/208591"}, {"RefNumber": "2059424", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/2059424"}, {"RefNumber": "197746", "RefComponent": "BC-FES-ITS", "RefTitle": "Maint. strategy: Internet Transaction Server (ITS)", "RefUrl": "/notes/197746"}, {"RefNumber": "1793938", "RefComponent": "BC-WD-ABA", "RefTitle": "Main Browser Note for NetWeaver 7.4", "RefUrl": "/notes/1793938"}, {"RefNumber": "1758540", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1758540"}, {"RefNumber": "1740908", "RefComponent": "SCM-APO-FCS-INF", "RefTitle": "Integration of SCM Add-On frontend files into SAP GUI", "RefUrl": "/notes/1740908"}, {"RefNumber": "1670678", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1670678"}, {"RefNumber": "166130", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP front end: Delivery and compatibility", "RefUrl": "/notes/166130"}, {"RefNumber": "1656902", "RefComponent": "SCM-APO-FCS", "RefTitle": "SAP GUI 7.20, Compatibility with SCM APO releases", "RefUrl": "/notes/1656902"}, {"RefNumber": "1652771", "RefComponent": "BW-SYS-GUI", "RefTitle": "BI 7.x in 730 DVD - Removal of the BW 3.5 Frontend Tools", "RefUrl": "/notes/1652771"}, {"RefNumber": "1570297", "RefComponent": "BW-SYS-GUI", "RefTitle": "BExAnalyer- No response to toolbar - Registry key issue", "RefUrl": "/notes/1570297"}, {"RefNumber": "154156", "RefComponent": "BC-FES-GUI", "RefTitle": "Parallel use of SAP GUIs of different releases", "RefUrl": "/notes/154156"}, {"RefNumber": "1540331", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1540331"}, {"RefNumber": "153825", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/153825"}, {"RefNumber": "1535594", "RefComponent": "BW-SYS-GUI", "RefTitle": "BW Frontend(7.X)-Upgrade scenarios in different GUI(710\\720)", "RefUrl": "/notes/1535594"}, {"RefNumber": "146505", "RefComponent": "BC-FES-JAV", "RefTitle": "SAP GUI for the Java environment (Platform Independent GUI)", "RefUrl": "/notes/146505"}, {"RefNumber": "1430516", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1430516"}, {"RefNumber": "142614", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/142614"}, {"RefNumber": "1393114", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1393114"}, {"RefNumber": "138869", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP GUI on Windows Terminal Server (WTS)", "RefUrl": "/notes/138869"}, {"RefNumber": "1366010", "RefComponent": "BW-SYS-GUI", "RefTitle": "BI 7.x Frontend SP 901 & 902 - Release date mismatch", "RefUrl": "/notes/1366010"}, {"RefNumber": "1363911", "RefComponent": "BW-SYS-GUI", "RefTitle": "Incorrect/Incomplete installation of BI 7.x Frontend SP 902", "RefUrl": "/notes/1363911"}, {"RefNumber": "1347295", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1347295"}, {"RefNumber": "1319517", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Collection Note", "RefUrl": "/notes/1319517"}, {"RefNumber": "1298789", "RefComponent": "BW-SYS-GUI", "RefTitle": "BW/BI 7.X Precalculation - New installation methods", "RefUrl": "/notes/1298789"}, {"RefNumber": "1298788", "RefComponent": "BW-SYS-GUI", "RefTitle": "BW/BI 7.X Precalculation - Pre-requisites", "RefUrl": "/notes/1298788"}, {"RefNumber": "1296465", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1296465"}, {"RefNumber": "1296464", "RefComponent": "BC-FES", "RefTitle": "Main Browser Note for NW 7.1", "RefUrl": "/notes/1296464"}, {"RefNumber": "1296463", "RefComponent": "BC-FES", "RefTitle": "Main Browser Note for NW 7.0", "RefUrl": "/notes/1296463"}, {"RefNumber": "1296419", "RefComponent": "BC-FES", "RefTitle": "Main Browser Note for NW 2004", "RefUrl": "/notes/1296419"}, {"RefNumber": "1236773", "RefComponent": "BW-SYS-GUI", "RefTitle": "BI7.X(7.10) Precalculation - General Information&Limitations", "RefUrl": "/notes/1236773"}, {"RefNumber": "1235105", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1235105"}, {"RefNumber": "1231292", "RefComponent": "BW-BEX", "RefTitle": "BI front end add-in strategy 6.40/7.10 GUI version", "RefUrl": "/notes/1231292"}, {"RefNumber": "1229666", "RefComponent": "BC-FES-JAV", "RefTitle": "Expected release dates for SAP GUI for Java revisions", "RefUrl": "/notes/1229666"}, {"RefNumber": "1085218", "RefComponent": "BW-BEX-ET", "RefTitle": "NetWeaver 7.x BW Frontend Patch Delivery Schedule", "RefUrl": "/notes/1085218"}, {"RefNumber": "1067757", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1067757"}, {"RefNumber": "1053737", "RefComponent": "BC-FES-GUI", "RefTitle": "Expected release dates for SAP GUI for Windows", "RefUrl": "/notes/1053737"}, {"RefNumber": "1025122", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "BEx Analyzer and compilation CD 1 of SAP GUI 7.10", "RefUrl": "/notes/1025122"}, {"RefNumber": "1013957", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Planning: Planning Grid - SAPGUI for Windows Hardware/Software", "RefUrl": "/notes/1013957"}, {"RefNumber": "1013140", "RefComponent": "BW-SYS-GUI", "RefTitle": "BW/BI Frontend (GUI 710): General Information & Limitations", "RefUrl": "/notes/1013140"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3098477", "RefComponent": "BC-FES-CTL", "RefTitle": "F4 help search functionality gives an error:  Internal Error: Table Format", "RefUrl": "/notes/3098477 "}, {"RefNumber": "3381850", "RefComponent": "BC-SRV-ALV", "RefTitle": "performance issue happen when use  'Excel inplace' to export ALV data to excel", "RefUrl": "/notes/3381850 "}, {"RefNumber": "3321580", "RefComponent": "BC-FES-GUI", "RefTitle": "How to obtain older GUI compilation", "RefUrl": "/notes/3321580 "}, {"RefNumber": "3157641", "RefComponent": "BC-SRV-SCR", "RefTitle": "Error message TD245 - Graphical from painter could not be called (FORMPAINTER_CREATE_WINDOW)", "RefUrl": "/notes/3157641 "}, {"RefNumber": "2964308", "RefComponent": "MM-PUR-GF-CAT", "RefTitle": "How to change the default browser used to open catalog from ME21N/ME51N?", "RefUrl": "/notes/2964308 "}, {"RefNumber": "2904135", "RefComponent": "BW-BEX-OT", "RefTitle": "Invalid state: OLAP caller violated protocol, details in application log", "RefUrl": "/notes/2904135 "}, {"RefNumber": "2366776", "RefComponent": "CA-WUI-UI-RT", "RefTitle": "SAP CRM WebUI: Transaction WUI_SSO (Single Sign-On) or SM_CRM runs only in Internet Explorer", "RefUrl": "/notes/2366776 "}, {"RefNumber": "2936950", "RefComponent": "XX-CSC-IN-FI", "RefTitle": "Downloaded excel file from FIWTIN_QRETURNS gives an error: file format is not valid", "RefUrl": "/notes/2936950 "}, {"RefNumber": "2950094", "RefComponent": "CA-GTF-TS-WSI", "RefTitle": "Browser usage of OCI catalogs from MM", "RefUrl": "/notes/2950094 "}, {"RefNumber": "2880032", "RefComponent": "BC-FES-CTL", "RefTitle": "OLE_FLUSH_CALL and GUI time in NetWeaver AS ABAP", "RefUrl": "/notes/2880032 "}, {"RefNumber": "2640427", "RefComponent": "BC-SRV-ALV", "RefTitle": "Export to Excel Spreadsheet gets a pop up box \"Save XXL List object in SAPoffice\"", "RefUrl": "/notes/2640427 "}, {"RefNumber": "2757832", "RefComponent": "BC-FES-GUI", "RefTitle": "Where to download archived SAP GUI patch.", "RefUrl": "/notes/2757832 "}, {"RefNumber": "2721349", "RefComponent": "BC-ABA-SC", "RefTitle": "“Hold Data” or \"Set Data\" is not working as expected.", "RefUrl": "/notes/2721349 "}, {"RefNumber": "2001352", "RefComponent": "BC-CCM-BTC-JOB", "RefTitle": "The \"job documentation\" column in SM37 contains \"@  @\" symbol when using SAPGUI 7.20", "RefUrl": "/notes/2001352 "}, {"RefNumber": "2019332", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Common issues occuring with Web Templates", "RefUrl": "/notes/2019332 "}, {"RefNumber": "2474104", "RefComponent": "BC-FES-CTL", "RefTitle": "Component Specific Questions (BC-FES-CTL)", "RefUrl": "/notes/2474104 "}, {"RefNumber": "2207497", "RefComponent": "BC-FES-GUI", "RefTitle": "Component Specific Questions (BC-FES-GUI)", "RefUrl": "/notes/2207497 "}, {"RefNumber": "3409410", "RefComponent": "BC-FES-CTL", "RefTitle": "SAP GUI for Windows: Added new method SET_NAME for class CL_GUI_INPUTFIELD for unique field history", "RefUrl": "/notes/3409410 "}, {"RefNumber": "1617725", "RefComponent": "BC-FES-JAV", "RefTitle": "SAP GUI for Java FAQ and problem solving", "RefUrl": "/notes/1617725 "}, {"RefNumber": "3263097", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP UI Landscape: Backup of local configuration files", "RefUrl": "/notes/3263097 "}, {"RefNumber": "2901278", "RefComponent": "BC-FES-CTL", "RefTitle": "SAP GUI HTML Control based on Chromium Edge: Legacy HTML does not work (correctly) / present limitations", "RefUrl": "/notes/2901278 "}, {"RefNumber": "2892419", "RefComponent": "BC-FES-GUI", "RefTitle": "Loading SAP UI landscape files via HTTPS: Certificate warnings / errors", "RefUrl": "/notes/2892419 "}, {"RefNumber": "1053737", "RefComponent": "BC-FES-GUI", "RefTitle": "Expected release dates for SAP GUI for Windows", "RefUrl": "/notes/1053737 "}, {"RefNumber": "2913405", "RefComponent": "BC-FES-CTL", "RefTitle": "SAP GUI for Windows: Dependencies to browsers / browser controls", "RefUrl": "/notes/2913405 "}, {"RefNumber": "2704603", "RefComponent": "FIN-SEM-BCS", "RefTitle": "Assignment Control not available anymore for SAP GUI 7.70 (UI adjustment)", "RefUrl": "/notes/2704603 "}, {"RefNumber": "2704440", "RefComponent": "BC-FES-CTL", "RefTitle": "Assignment Control not available anymore as of SAP GUI for Windows 7.70", "RefUrl": "/notes/2704440 "}, {"RefNumber": "1322923", "RefComponent": "BW-BEX-ET", "RefTitle": "Maintenance strategy / deadlines BW Business Explorer (BEx)", "RefUrl": "/notes/1322923 "}, {"RefNumber": "2646715", "RefComponent": "BC-FES-WTS", "RefTitle": "SAP GUI Terminal Virtualization with Amazon AppStream 2.0", "RefUrl": "/notes/2646715 "}, {"RefNumber": "146505", "RefComponent": "BC-FES-JAV", "RefTitle": "SAP GUI for the Java environment (Platform Independent GUI)", "RefUrl": "/notes/146505 "}, {"RefNumber": "2302074", "RefComponent": "BC-FES-BUS-DSK", "RefTitle": "Maintenance strategy and deadlines for SAP Business Client / NWBC", "RefUrl": "/notes/2302074 "}, {"RefNumber": "2436142", "RefComponent": "SV-SMG-TWB-CBT", "RefTitle": "Additional information : Component-Based Test Automation Supportability", "RefUrl": "/notes/2436142 "}, {"RefNumber": "2417687", "RefComponent": "BC-FES-GUI", "RefTitle": "New features in SAP GUI for Windows 7.50", "RefUrl": "/notes/2417687 "}, {"RefNumber": "2256415", "RefComponent": "BC-FES-AIT-CTR", "RefTitle": "Adaptation of RFC controls (Logon, Function, Table and BAPI) to use SAP NetWeaver RFC Library", "RefUrl": "/notes/2256415 "}, {"RefNumber": "2365556", "RefComponent": "BC-FES-WGU", "RefTitle": "Availability of the Fiori 2.0 visual theme for classical applications (theme \"Belize\") in SAP GUI for HTML, SAP GUI for Java and SAP GUI for Windows", "RefUrl": "/notes/2365556 "}, {"RefNumber": "2217489", "RefComponent": "CA-UI5-DLV", "RefTitle": "Maintenance and Update Strategy for SAP Fiori Front-End Server", "RefUrl": "/notes/2217489 "}, {"RefNumber": "26417", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP GUI Resources: Hardware and software", "RefUrl": "/notes/26417 "}, {"RefNumber": "1319517", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Collection Note", "RefUrl": "/notes/1319517 "}, {"RefNumber": "66971", "RefComponent": "BC-FES-GUI", "RefTitle": "Supported SAP GUI platforms", "RefUrl": "/notes/66971 "}, {"RefNumber": "1793938", "RefComponent": "BC-WD-ABA", "RefTitle": "Main Browser Note for NetWeaver 7.4", "RefUrl": "/notes/1793938 "}, {"RefNumber": "1229666", "RefComponent": "BC-FES-JAV", "RefTitle": "Expected release dates for SAP GUI for Java revisions", "RefUrl": "/notes/1229666 "}, {"RefNumber": "1085218", "RefComponent": "BW-BEX-ET", "RefTitle": "NetWeaver 7.x BW Frontend Patch Delivery Schedule", "RefUrl": "/notes/1085218 "}, {"RefNumber": "722513", "RefComponent": "BC-FES-OFFI", "RefTitle": "Desktop Office Integration: Maintenance information", "RefUrl": "/notes/722513 "}, {"RefNumber": "30460", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP GUI: Compatibility with different SAP System releases", "RefUrl": "/notes/30460 "}, {"RefNumber": "1298789", "RefComponent": "BW-SYS-GUI", "RefTitle": "BW/BI 7.X Precalculation - New installation methods", "RefUrl": "/notes/1298789 "}, {"RefNumber": "1298788", "RefComponent": "BW-SYS-GUI", "RefTitle": "BW/BI 7.X Precalculation - Pre-requisites", "RefUrl": "/notes/1298788 "}, {"RefNumber": "166130", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP front end: Delivery and compatibility", "RefUrl": "/notes/166130 "}, {"RefNumber": "1431414", "RefComponent": "EHS-BD-RDF-WWI", "RefTitle": "EH&S WWI: Release for MS Office Word 2010", "RefUrl": "/notes/1431414 "}, {"RefNumber": "1410878", "RefComponent": "BW-BEX-ET", "RefTitle": "Maintenance for BW 3.5 front-end add-ons", "RefUrl": "/notes/1410878 "}, {"RefNumber": "197746", "RefComponent": "BC-FES-ITS", "RefTitle": "Maint. strategy: Internet Transaction Server (ITS)", "RefUrl": "/notes/197746 "}, {"RefNumber": "1296463", "RefComponent": "BC-FES", "RefTitle": "Main Browser Note for NW 7.0", "RefUrl": "/notes/1296463 "}, {"RefNumber": "1509421", "RefComponent": "BC-FES-GUI", "RefTitle": "Main Browser Note for NW 7.3", "RefUrl": "/notes/1509421 "}, {"RefNumber": "1740908", "RefComponent": "SCM-APO-FCS-INF", "RefTitle": "Integration of SCM Add-On frontend files into SAP GUI", "RefUrl": "/notes/1740908 "}, {"RefNumber": "154156", "RefComponent": "BC-FES-GUI", "RefTitle": "Parallel use of SAP GUIs of different releases", "RefUrl": "/notes/154156 "}, {"RefNumber": "138869", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP GUI on Windows Terminal Server (WTS)", "RefUrl": "/notes/138869 "}, {"RefNumber": "1656902", "RefComponent": "SCM-APO-FCS", "RefTitle": "SAP GUI 7.20, Compatibility with SCM APO releases", "RefUrl": "/notes/1656902 "}, {"RefNumber": "1570297", "RefComponent": "BW-SYS-GUI", "RefTitle": "BExAnalyer- No response to toolbar - Registry key issue", "RefUrl": "/notes/1570297 "}, {"RefNumber": "1236773", "RefComponent": "BW-SYS-GUI", "RefTitle": "BI7.X(7.10) Precalculation - General Information&Limitations", "RefUrl": "/notes/1236773 "}, {"RefNumber": "563161", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP GUI, SAP ITS, and SAP IGS patches and installation media in SAP ONE Support Launchpad", "RefUrl": "/notes/563161 "}, {"RefNumber": "1535594", "RefComponent": "BW-SYS-GUI", "RefTitle": "BW Frontend(7.X)-Upgrade scenarios in different GUI(710\\720)", "RefUrl": "/notes/1535594 "}, {"RefNumber": "1540331", "RefComponent": "BW-SYS-GUI", "RefTitle": "Installation & Uninstallation scenarios of BEx frontend", "RefUrl": "/notes/1540331 "}, {"RefNumber": "1013957", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Planning: Planning Grid - SAPGUI for Windows Hardware/Software", "RefUrl": "/notes/1013957 "}, {"RefNumber": "1013140", "RefComponent": "BW-SYS-GUI", "RefTitle": "BW/BI Frontend (GUI 710): General Information & Limitations", "RefUrl": "/notes/1013140 "}, {"RefNumber": "1296465", "RefComponent": "BC-FES", "RefTitle": "Main Browser Note for NW 7.20", "RefUrl": "/notes/1296465 "}, {"RefNumber": "1296464", "RefComponent": "BC-FES", "RefTitle": "Main Browser Note for NW 7.1", "RefUrl": "/notes/1296464 "}, {"RefNumber": "1296419", "RefComponent": "BC-FES", "RefTitle": "Main Browser Note for NW 2004", "RefUrl": "/notes/1296419 "}, {"RefNumber": "1430516", "RefComponent": "BW-SYS-GUI", "RefTitle": "BI Frontend - Versioning methods of the library files", "RefUrl": "/notes/1430516 "}, {"RefNumber": "1363911", "RefComponent": "BW-SYS-GUI", "RefTitle": "Incorrect/Incomplete installation of BI 7.x Frontend SP 902", "RefUrl": "/notes/1363911 "}, {"RefNumber": "1366010", "RefComponent": "BW-SYS-GUI", "RefTitle": "BI 7.x Frontend SP 901 & 902 - Release date mismatch", "RefUrl": "/notes/1366010 "}, {"RefNumber": "1231292", "RefComponent": "BW-BEX", "RefTitle": "BI front end add-in strategy 6.40/7.10 GUI version", "RefUrl": "/notes/1231292 "}, {"RefNumber": "899881", "RefComponent": "XX-TRANSL-CNX", "RefTitle": "Download of a new version SAPGUI and SP from the Marketplace", "RefUrl": "/notes/899881 "}, {"RefNumber": "1235105", "RefComponent": "BW-SYS-GUI", "RefTitle": "BI Frontend 7.00 to 7.X Migration - General Information", "RefUrl": "/notes/1235105 "}, {"RefNumber": "574373", "RefComponent": "FIN-SEM-CPM", "RefTitle": "SEM 3.1B front end package 4 (Nov.2002)", "RefUrl": "/notes/574373 "}, {"RefNumber": "560595", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 22 for APO Release 3.0A", "RefUrl": "/notes/560595 "}, {"RefNumber": "678193", "RefComponent": "FIN-SEM-CPM", "RefTitle": "SEM 3.5 frontend patch 2 (Nov.2003)", "RefUrl": "/notes/678193 "}, {"RefNumber": "675156", "RefComponent": "SCM-TEC", "RefTitle": "SAP APO 3.0A SP26: Release and information note", "RefUrl": "/notes/675156 "}, {"RefNumber": "645114", "RefComponent": "FIN-SEM-CPM", "RefTitle": "SEM 3.1B front end patch 5 (July.2003)", "RefUrl": "/notes/645114 "}, {"RefNumber": "641748", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 25 for APO Release 3.0A", "RefUrl": "/notes/641748 "}, {"RefNumber": "532619", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 21 for APO Release 3.0A", "RefUrl": "/notes/532619 "}, {"RefNumber": "618337", "RefComponent": "SCM-TEC", "RefTitle": "APO of Support Package 24 for APO Release 3.0A", "RefUrl": "/notes/618337 "}, {"RefNumber": "968222", "RefComponent": "FIN-SEM-CPM", "RefTitle": "SEM 3.5 Front end patch 8 (July 2006)", "RefUrl": "/notes/968222 "}, {"RefNumber": "734171", "RefComponent": "FIN-SEM-CPM", "RefTitle": "SEM 3.5 front-end Support Package 3 (May 2004)", "RefUrl": "/notes/734171 "}, {"RefNumber": "517492", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 20 for APO Release 3.0A", "RefUrl": "/notes/517492 "}, {"RefNumber": "513344", "RefComponent": "FIN-SEM-CPM", "RefTitle": "SEM 3.1B front end patch 1 (Apr.2002)", "RefUrl": "/notes/513344 "}, {"RefNumber": "590056", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 23 for APO Release 3.0A", "RefUrl": "/notes/590056 "}, {"RefNumber": "760646", "RefComponent": "FIN-SEM-CPM", "RefTitle": "SEM 3.5 front-end Support Package 5 (Aug.2004)", "RefUrl": "/notes/760646 "}, {"RefNumber": "656975", "RefComponent": "FIN-SEM-CPM", "RefTitle": "SEM 3.5 frontend patch 1 (Oct.2003)", "RefUrl": "/notes/656975 "}, {"RefNumber": "811071", "RefComponent": "FIN-SEM-CPM", "RefTitle": "SEM 3.5 Frontend Patch 6 (January 2005)", "RefUrl": "/notes/811071 "}, {"RefNumber": "422446", "RefComponent": "SCM-APO-OCX", "RefTitle": "APO front end patch", "RefUrl": "/notes/422446 "}, {"RefNumber": "396644", "RefComponent": "FIN-SEM-CPM", "RefTitle": "SEM 3.0A Frontend patch 1 (May.2001)", "RefUrl": "/notes/396644 "}, {"RefNumber": "402961", "RefComponent": "BC-FES-GRA", "RefTitle": "SEM 3.0A IGS front-end patch (18 May 2001)", "RefUrl": "/notes/402961 "}, {"RefNumber": "491882", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 19 for APO Release 3.0A", "RefUrl": "/notes/491882 "}, {"RefNumber": "1025122", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "BEx Analyzer and compilation CD 1 of SAP GUI 7.10", "RefUrl": "/notes/1025122 "}, {"RefNumber": "853535", "RefComponent": "BC-ABA-SC", "RefTitle": "Icons are displayed incorrectly", "RefUrl": "/notes/853535 "}, {"RefNumber": "811239", "RefComponent": "CA-DMS", "RefTitle": "DMS: Displaying originals of a Document Info Record.", "RefUrl": "/notes/811239 "}, {"RefNumber": "330728", "RefComponent": "BC-SRV-ALV", "RefTitle": "List Viewers: Expanding totals lines", "RefUrl": "/notes/330728 "}, {"RefNumber": "368155", "RefComponent": "BC-SRV-ALV", "RefTitle": "List Viewer: Clipboard", "RefUrl": "/notes/368155 "}, {"RefNumber": "369475", "RefComponent": "BC-SRV-ALV", "RefTitle": "List viewer: Front end hangs when you build a list", "RefUrl": "/notes/369475 "}, {"RefNumber": "23414", "RefComponent": "BC-FES-GUI", "RefTitle": "SAPGUI on Windows 95", "RefUrl": "/notes/23414 "}, {"RefNumber": "391335", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 10 in APO Release 3.0A", "RefUrl": "/notes/391335 "}, {"RefNumber": "399227", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 11 for APO Release 3.0A", "RefUrl": "/notes/399227 "}, {"RefNumber": "408248", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 12 for APO Release 3.0A", "RefUrl": "/notes/408248 "}, {"RefNumber": "415727", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 13 for APO Release 3.0A", "RefUrl": "/notes/415727 "}, {"RefNumber": "423594", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 14 for APO Release 3.0A", "RefUrl": "/notes/423594 "}, {"RefNumber": "424758", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 15 for APO Release 3.0A", "RefUrl": "/notes/424758 "}, {"RefNumber": "438337", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 16 for APO Release 3.0A", "RefUrl": "/notes/438337 "}, {"RefNumber": "448518", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 17 for APO Release 3.0A", "RefUrl": "/notes/448518 "}, {"RefNumber": "459985", "RefComponent": "SCM-TEC", "RefTitle": "APO Support Package 18 for APO Release 3.0A", "RefUrl": "/notes/459985 "}, {"RefNumber": "383890", "RefComponent": "SCM-APO", "RefTitle": "APO Support Package 9 for APO Release 3.0A", "RefUrl": "/notes/383890 "}, {"RefNumber": "362128", "RefComponent": "SCM-APO", "RefTitle": "APO Support Package 12 for APO Release 2.0A", "RefUrl": "/notes/362128 "}, {"RefNumber": "385483", "RefComponent": "SCM-APO", "RefTitle": "APO Support Package 13 for APO Release 2.0A", "RefUrl": "/notes/385483 "}, {"RefNumber": "367964", "RefComponent": "SCM-APO", "RefTitle": "APO Support Package 8 for APO Release 3.0A", "RefUrl": "/notes/367964 "}, {"RefNumber": "97056", "RefComponent": "BC-SRV-ASF-CAL", "RefTitle": "Patch for SAPKALE for the Year 2000", "RefUrl": "/notes/97056 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}