{"Request": {"Number": "1637199", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 351, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017318472017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001637199?language=E&token=4DECAE0E02AE899EB3B7D01991B1834F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001637199", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001637199/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1637199"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 69}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "13.09.2022"}, "SAPComponentKey": {"_label": "Component", "value": "BW-PLA-IP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Integrated Planning"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Planning", "value": "BW-PLA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-PLA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Integrated Planning", "value": "BW-PLA-IP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-PLA-IP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1637199 - Using the planning applications KIT (PAK)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Starting with SAP BW 7.30 SP5 we have introduced the Planning Applications KIT <strong>(PAK)</strong> which allows execution of BW-IP standard functions in HANA. The following needs to be considered when using the ABAP Planning Applications KIT:</p>\r\n<ul>\r\n<li>The use of the Planning Applications KIT requires the following license: 'SAP BusinessObjects Planning and Consolidation, version for SAP NetWeaver'. If you do not have acquired this license, please contact your account executive for further information.</li>\r\n</ul>\r\n<ul>\r\n<li>Some standard BW-IP functions are not accelerated with the ABAP Planning Applications KIT or lead to errors when executed in HANA.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Hana optimized Planning, planning applications KIT, disable, deactivate, unable, PAK, HANA</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You have acquired the license mentioned above and want to leverage BW-IP standard functions in HANA. Certain features available today in BW-IP (and PAK) are only supported in ABAP and might lead to additional data exchanges between the ABAP layer and the HANA database which can have a bad impact on the system performance. Also some ABAP specific features are not supported in the HANA optimized execution (see below for a list of all these features). Some remodeling might be required to leverage full HANA optimization when starting from an existing scenario.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Following switches exist to de-/activate ABAP Planning Applications KIT:</p>\r\n<ul>\r\n<li>Main switch to generally activate the Planning Applications KIT (license required): table view RSPLS_HDB_ACT in SM30.&#160;If not yet existing, create a new entry for&#160;\"Deep HANA Integration Active\"&#160;(HANA_ACT) and mark the checkbox 'Function Active'&#160;in order to activate the ABAP Planning Applications KIT.</li>\r\n</ul>\r\n<ul>\r\n<li>In rare cases some scenarios might cause problems when executed in HANA. Thus the table view RSPLS_HDB_ACT_IP in SM30 can be used to deactivate the ABAP Planning Applications KIT for individual InfoProviders. Choose the real-time InfoCube, direct update DSO or aDSO you&#160;do not want to use for HANA planning. This feature is available in order to guarantee a smooth transition from existing BW-IP scenarios and to avoid additional roundtrips due to the restrictions mentioned below.&#160;<br /><br />Every real-time InfoCube must be HANA optimized to run in HANA.&#160;In case of a MultiProvider or CompositeProvider all planning enabled part providers must&#160;be HANA enabled (not de -activated in the table mentioned above) and also HANA optimized. One disabled or not HANA optimized InfoProvider disables the entire Multi- or CompositeProvider for HANA processing. By default all InfoProviders not mentioned in the table view RSPLS_HDB_ACT_IP are HANA enabled. It is only necessary to add an InfoProvider when it should be HANA disabled. This can be done by adding the InfoProvider to the table and de-selecting the &#8216;Functn. Active&#8217; checkbox.</li>\r\n</ul>\r\n<ul>\r\n<li>There is a global &#8220;emergency exit switch&#8221; that can be set using report SAP_RSADMIN_MAINTAIN with parameter object RSPLS_HDB_PROCESSING_OFF. This switch should only be used in rare special circumstances.<br />Possible values are:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>'F': Force Off&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;No HANA optimized processing, cannot be overruled any user</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>'X': Off&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;No HANA optimized processing, but can be overruled for single users for special purposes</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Not set: switch inactive (default and usual value) <br />HANA processing will run on the intended planning function etc., but can be switched off for single users (traces, comparison etc.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>For project studies/PoC or special tasks like special process chains one can set a user specific PAK activation switch: Set/Get-Parameter RSPLS_HDB_SUPPORT (set in transaction SU01)<br />Possible Values are:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>'HDB_ON': HANA optimized processing when possible also if global switch set&#160;&#160; 'X' (OFF).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>'HDB_OFF': HANA optimized processing disabled independently from the global setting.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Not set: inactive, general system settings are used (including the global switch)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Trace information like whether a planning function is executed HANA optimized or the information about changed and created records are only displayed and written to the log when the user parameter RS_DEBUGLEVEL is set to 2 (or higher).</li>\r\n<li>If you use transaction RSPLAN and run a step in a planning sequence by pressing 'Execute Step with Trace' (as opposed to 'Execute Step') the function currently still runs in the IP (ABAP) runtime since we need to retrieve information for a detailed display. See knowledge base article 1911914 and 2)</li>\r\n</ul>\r\n<p><br />&#160;<br />Below we provide a list of features which are supported to run fully in HANA and also a list of features which are still executed in ABAP application server. Those will cause an additional data round trip and might harm the performance.</p>\r\n<p>Also a list of functions is added which are not supported on HANA. We have been working on enabling further functions in HANA (and continue to do so) so this list is subject to change. Some of the topics refer to a comment at the end of the note indicating the potential to eliminate the restrictions over time. In order to check whether a scenario in principle can run HANA optimized we provide the report RSPLS_PLANNING_ON_HDB_ANALYSIS (note 1824516 or 7.30 SP 10/ 7.31 SP8 and note 2336691 for better&#160;FOX support). In 7.4 SP5 and follow up releases they are supported as well if not mention explicit.</p>\r\n<p>1. List of features which run fully in HANA</p>\r\n<ul>\r\n<li>Following Planning Functions are executed in HANA:\r\n<ul>\r\n<li>Planning Function Type: 0RSPL_COPY&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; Copy<br />&#160;&#160;</li>\r\n<li>Planning Function Type: 0RSPL_COPY_NO_ZEROS&#160;&#160;&#160;&#160;&#160; Copy ( without records where all keyfigures are zero )&#160;<br /><br /></li>\r\n<li>Planning Function Type: 0RSPL_CREATE_CR&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;Generate Combinations<br />&#160;&#160;</li>\r\n<li>Planning Function Type: 0RSPL_CURR_CONV&#160; &#160; &#160; &#160; &#160; &#160; &#160; Currency Translation<br /> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; requires BW 7.4 SP8 or note&#160;2076956&#160;for BW 7.3/7.31&#160;and Hana Revision &gt;= *********</li>\r\n<li>Planning Function Type: 0RSPL_DELETE&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;Delete<br /><br /></li>\r\n<li>Planning Function Type: 0RSPL_DELETE_DSO&#160; &#160; &#160; &#160; &#160; &#160; &#160;Delete DSO data physically<br /> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;requires note&#160;&#160;1855154 or 7.30 SP10/7.31 SP9/7.40 SP5 <br />&#160;&#160;</li>\r\n<li>Planning Function Type: 0RSPL_DELETE_CR&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; Deletion of Invalid Combinations<br /> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;requires note&#160;&#160;1778939 or 7.30 SP9/7.31 SP7<br />&#160;&#160;</li>\r\n<li>Planning Function Type: 0RSPL_DELETE_CR_DSO&#160; &#160; &#160; &#160; Physical Deletion of Invalid Combinations in DSOs<br /> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;requires note&#160;&#160;1855154 or 7.30 SP10/7.31 SP9/7.40 SP5<br /><br /></li>\r\n<li>Planning Function Type: 0RSPL_DISTR_KEY&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;Distribution with Keys<br /> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; requires BW 7.4 SP8 and HANA revision &gt;= 1.00.73<br /> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; or only note 1821899 or 7.30 SP10/7.31 SP9/7.4 SP5 for distribution type&#160;'Distribute Not-Assigned (#)'.&#160;<br /><br /></li>\r\n<li>Planning Function Type: 0RSPL_DISTR_REFDATA&#160; &#160; &#160; &#160; &#160; Distribution by Reference Data<br />&#160;&#160;</li>\r\n<li>Planning Function Type: 0RSPL_FORMULA&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; Formula<br /> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; (see restrictions below)<br /><br /></li>\r\n<li>Planning Function Type: 0RSPL_REPOST&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;Repost<br />&#160; &#160;</li>\r\n<li>Planning Function Type: 0RSPL_REPOST_DSO&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;Repost Data and Delete DSO Data physically<br /> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;requires note&#160;&#160;1912307 or 7.30 SP11/7.31 SP10/7.40 SP5 <br />&#160;&#160;</li>\r\n<li>Planning Function Type: 0RSPL_REPOST_CR&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; Repost on Basis of Characteristic Relationship<br /> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; requires note 1855154 or 7.30 SP10/7.31 SP9<br />&#160;&#160;</li>\r\n<li>Planning Function Type: 0RSPL_REPOST_CR_DSO&#160; &#160; &#160; &#160; &#160; Repost DSO Data on Basis of Characteristic Relationships<br /> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;requires note&#160;&#160;1855154 or 7.30 SP10/7.31 SP9/7.40 SP5&#160;</li>\r\n<li>Planning Function Type: 0RSPL_REVALUATION&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;Revaluation<br /><br /></li>\r\n<li>Planning Function Type: 0RSPL_SET_VALUES&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;Set Key Figure Values<br /><br /></li>\r\n<li>Planning Function Type: 0RSPL_UNIT_CONV&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; Unit Conversion<br /> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; requires BW 7.4 SP8, or note&#160;2076956&#160;for BW 7.3/7.31&#160;and Hana Revision &gt;= *********</li>\r\n</ul>\r\n</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<ul>\r\n<li>delta mode flag switched on in planning functions is supported. However, the determination of the filter is done in ABAP and can cause more time then executing all records in HANA. It should therefore be only used for functional reasons and not due to better performance as in the classic IP case</li>\r\n<li>Disaggregation in the Query is executed in HANA if</li>\r\n<ul>\r\n<li>the query is modeled directly on the aggregation level</li>\r\n<li>for all restricting characteristics all contributing key figures are only restricted to one single value <br />&#160;</li>\r\n</ul>\r\n<li>Following characteristic relationships are supported to be fully checked in HANA</li>\r\n<ul>\r\n<li>Characteristic relationship of type attribute</li>\r\n<li>Characteristic relationship of type DSO</li>\r\n<li>Characteristic relationships of type hierarchy is supported with&#160;7.30 SP12 or 7.31 SP12 or&#160;note 1984344&#160;</li>\r\n<li>Characteristic relationship of type transient. This is only available through the ABAP BICS interface<br />&#160;</li>\r\n</ul>\r\n<li>Following data slices are supported to be fully checked in HANA</li>\r\n<ul>\r\n<li>Data slices of type selection with 7.30 SP9 or 7.31 SP7 or note 1803016<br />&#160;</li>\r\n</ul>\r\n<li>Logging BADI requires 7.30 SP9 or 7.31 SP7 or note 1802658<br />&#160;</li>\r\n<li>Key-figures in the InfoCube or aDSO with aggregate &#8216;SUM&#8217;. For direct update DSO we also allow &#8216;NO2&#8217;. The usage of MIN/MAX in read only key figure like in classic BW-IP requires BW 7.4 SP8.</li>\r\n<li>The new DSO of type planning (see note 1735590) is released for HANA optimized planning with 7.30 SP9 or 7.31 SP7 or note 1799168. Character like key figures which were introduced with BW 7.4 SP8 are available in PAK with note 2196138<br />&#160;<br />In this case where the model allows the algorithm to fully execute in HANA also the data of the planning buffer only resists in HANA (see above). Then also the save of plan buffers is fully executed within HANA only.</li>\r\n</ul>\r\n<p>2. List of features still executed in ABAP application server with data round trip</p>\r\n<ul>\r\n<li>Planning:</li>\r\n<ul>\r\n<li>Own planning function type: any planning function exit as own type as long as no interface IF_RSPLFA_SRVTYPE_TREX_EXEC or IF_RSPLFA_SRVTYPE_TREX_EXEC_R is implemented leveraging SQL Script for HANA processing. See Standard BW Documentation or note 1870369.</li>\r\n<li>Planning function type: 0RSPL_FORECASTING&#160;&#160; Forecast<br />As workaround one can use a SQL Script based implementation (see footnote 1) which includes some PAL functions<br />(See <a target=\"_blank\" href=\"http://help.sap.com/hana/SAP_HANA_Predictive_Analysis_Library_PAL_en.pdf\">http://help.sap.com/HANA/SAP_HANA_Predictive_Analysis_Library_PAL_en.pdf</a> and <a target=\"_blank\" href=\"http://www.saphana.com/community/hana-academy/#predictive-analytics-library\">http://www.sapHANA.com/community/HANA-academy/#predictive-analytics-library</a>)<br />&#160;&#160;</li>\r\n</ul>\r\n<li>Planning function type 'FOX' does not support the following commands and is in those cases executed in the application server</li>\r\n<ul>\r\n<li>CALL FUNCTION (1)</li>\r\n<li>ABAP BREAK-POINT command is ignored</li>\r\n<li>FOX key words are not supported for other usage like variable names. E.g. DATA I TYPE I is not allowed but DATA J TYPE I is allowed.</li>\r\n<li>attribute lookup (ATRV/ATRVT) or validity check (MDEXIST) on standard time&#160;characteristics like 0CALYEAR or characteristic&#160;referencing those</li>\r\n<li>attribute lookup (ATRV/ATRVT) or validity check (MDEXIST) on characteristics which don't have a master data table, which is a BW object. But&#160;HANA View based info objects are supported ATRV in FOX on HANA view solved with note 2644984 and Revision 2.00.030.01</li>\r\n<li>Using a hierarchy node variable in VARC or similar statements</li>\r\n<li>Using compounding in variables before revision 122.05. It&#160;work&#160;from revision&#160;122.05 onwards&#160;when applying&#160;note&#160;2386434.</li>\r\n<li>Using InfoObjects&#160;outside the aggregation level (e.g. in DATA statements)</li>\r\n<li>The underlying aggregation level contains a float key figure you need at least note&#160;2644984 and for HANA 1.0 revision 1.00.122.17 or for HANA 2.0 2.00.030.01. See also notes 2449817 and 460652 and the&#160;general advice to avoid float in BW.&#160;</li>\r\n<li>New FOX features introduced with BW 7.4 SP8 like reading from external aggregation levels which is supported for PAK with revision 102.4 incl. note 2234156 and 2241643. &#160;Internal tables are also supported in PAK with HANA revision 97.02 and note 2145611</li>\r\n<li>The new FOX features &#8216;modularization&#8217; or &#8216;forms&#8217;&#160;introduced with BW 7.5 SP0 are currently not yet supported in PAK and lead to the processing in ABAP application server. &#160;</li>\r\n<li>The new FOX feature &#8216;directly reading from a non-input enabled DSO &#8216; (introduced in BW 7.5 SP0) is&#160; supported in PAK with SAP BW 7.52. In case the DSO contains non-key fields the the execution however is still on the ABAP stack.</li>\r\n<li>The new feature of transient attributes&#160;coming with BW 7.4 SP4 will&#160;not be supported to run in HANA for time dependent attributes in FOX (e.g. ATRV). Then the FOX is processed on ABAP side.</li>\r\n<li>Key fields in internal tables of type KEYFIGURE_NAME.</li>\r\n<li>Access to external InfoProviders with variables of type KEYFIGURE_NAME.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;Note 2336691 includes&#160;the HANA check of&#160;FOX in Fox Editor</p>\r\n<ul>\r\n<li>If conditions exist on a planning function and</li>\r\n<ul>\r\n<li>More on has more than&#160;1 condition on this planning function or</li>\r\n<li>The global filter contains selection on attributes, but the condition contains selection on the basic characteristics or</li>\r\n<li>The filter contains selections on hierarchy nodes and the condition has selections on the characteristic the hierarchy is defined on or</li>\r\n<li>The selection contains selections which are not single values restriction and those selections are not one by the same in the global filter.<br />&#160;</li>\r\n</ul>\r\n<li>Dis-aggregations in the Query are executed not in the database if</li>\r\n<ul>\r\n<li>Planning model uses MultiProvider on top of an aggregation level</li>\r\n<li>A formula is used as reference key figure for disaggregation</li>\r\n<li>The key figure is restricted to multiple values for a given characteristic except several single values. E.g. intervals, or hierarchy nodes in the restriction lead to execution in the application server.</li>\r\n<li>You use disaggregation with reference to a further structure element that contains a constant selection for a characteristic.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Here the additional impact of a round trip is not as considerable as for planning functions. Only the generated data has to be pushed to the database.</p>\r\n<p>&#160;</p>\r\n<p>3. List of features which cannot be executed with the ABAP applications planning KIT in HANA and leads to switch to BW-IP (for the entire model, not a single step)</p>\r\n<ul>\r\n<li>TIMS &amp; DATS key-figures in the InfoCube. We recommend to use data type DEC and instead of TIMS or DATS</li>\r\n</ul>\r\n<ul>\r\n<li>Master data access of type Own Implementation or Remote (Direct Access). With note 1929130 we allow (as exemption) characteristics referencing to standard BW time characteristics, source system (0SOURSYSTEM) or InfoProvider (0INFOPROV) when using their standard implementations.</li>\r\n</ul>\r\n<ul>\r\n<li>Virtual master data, including hierarchies and technical master data which are not persisted in tables. This also affects compounded characteristics leveraging those master data. Examples are InfoObjects that exist in BW but without stored data in the InfoObject tables. Exceptions are time and system characteristics which also work in the HANA optimized case.</li>\r\n</ul>\r\n<ul>\r\n<li>Virtual and referencing key-figures</li>\r\n</ul>\r\n<ul>\r\n<li>Transient characteristics</li>\r\n</ul>\r\n<ul>\r\n<li>Certain constraints by characteristic relationship cannot be checked in HANA yet. This includes</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>characteristic relationships of type EXIT as long as the interface IF_RSPLS_CR_EXIT_HDB is not implemented. One can either leveraging SQL Script for HANA processing (see notes 1877182, 1874412 and 1929130(1)) or use ABAP as fall back (see note 1956085 - BW-IP (PAK): ABAP as fallback for exit characteristic relations and data slices on SAP HANA)</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<ul>\r\n<li>Certain constraints by data slices cannot be checked in HANA yet. This includes</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>data slices of type EXIT as long as the interface IF_RSPLS_DS_EXIT_HDB is not implemented leveraging SQL Script for HANA processing. See notes 1877182, 1874412 and 1929130. See footnote (1)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>CP-Problem as explained in the long text of message BRAIN 313 (see note 2095418) is not working before BW 7.4 SP11 or note 2111189.</li>\r\n<li>HANA views with placeholder in virtual providers or as part providers in a CompositeProvider</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p>Potential to overcome current limitation<br />(this list reflects development plans and is no commitment for dates and or functionality)</p>\r\n<ul>\r\n<ul>\r\n<li>(1) By conception, we will not be able to support the full flexibility of an ABAP EXIT within HANA, in particular, when other ABAP sources are leveraged in the implementation. However, there are EXIT implementations using SQL script that can be executed in HANA.&#160;&#160;Prerequisites are HANA 1.0 SP6 Revision 67 and NW 7.30 SP10/7.31 SP9. For BW one can alternatively apply notes 1861395, 1870342, 1877182, 1870369, 1929130 and 1956085.</li>\r\n</ul>\r\n</ul></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D031184)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I059251)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001637199/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001637199/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001637199/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001637199/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001637199/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001637199/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001637199/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001637199/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001637199/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1911914", "RefComponent": "BW-PLA-IP", "RefTitle": "BW In-memory Planning will not be Executed in Trace Mode.", "RefUrl": "/notes/1911914"}, {"RefNumber": "2644984", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "FOX functions - execution on SAP HDB - release of features", "RefUrl": "/notes/2644984"}, {"RefNumber": "2375039", "RefComponent": "BW-PLA-IP", "RefTitle": "BW-PAK: Check result handler (help for troubleshooting)", "RefUrl": "/notes/2375039"}, {"RefNumber": "2336691", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Execution of FOX formulas in PAK", "RefUrl": "/notes/2336691"}, {"RefNumber": "2196138", "RefComponent": "BW-PLA-IP", "RefTitle": "BW-IP: In-memory planning - attribute planning", "RefUrl": "/notes/2196138"}, {"RefNumber": "2189861", "RefComponent": "BW-PLA-IP", "RefTitle": "Details and Condition for Planning on native HANA views", "RefUrl": "/notes/2189861"}, {"RefNumber": "1956085", "RefComponent": "BW-PLA-IP", "RefTitle": "BW-IP (PAK): ABAP as fallback for exit characteristic relations and data slices on SAP HANA", "RefUrl": "/notes/1956085"}, {"RefNumber": "1930335", "RefComponent": "BW-PLA-IP", "RefTitle": "BW-IP (PAK): Default data processing option for InfoProvider", "RefUrl": "/notes/1930335"}, {"RefNumber": "1928715", "RefComponent": "BW-PLA-IP", "RefTitle": "SQL-Script based planning exits in PAK", "RefUrl": "/notes/1928715"}, {"RefNumber": "1927309", "RefComponent": "BW-PLA-IP", "RefTitle": "Tracing for reason why PAK switch does not seem to work", "RefUrl": "/notes/1927309"}, {"RefNumber": "1919631", "RefComponent": "BW-PLA-BPC", "RefTitle": "Activating the BPC embedded", "RefUrl": "/notes/1919631"}, {"RefNumber": "1824516", "RefComponent": "BW-BEX-OT", "RefTitle": "Missing analysis report for planning func.: SAP HANA - Mem", "RefUrl": "/notes/1824516"}, {"RefNumber": "1802658", "RefComponent": "BW-PLA-IP", "RefTitle": "BW-IP: PAK and logging BAdI", "RefUrl": "/notes/1802658"}, {"RefNumber": "1792782", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "FOX: Reserved words", "RefUrl": "/notes/1792782"}, {"RefNumber": "1778939", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "SAP HANA planning functions: Further developments", "RefUrl": "/notes/1778939"}, {"RefNumber": "1735590", "RefComponent": "BW-PLA-IP", "RefTitle": "Q&A on DSO Planning", "RefUrl": "/notes/1735590"}, {"RefNumber": "1694205", "RefComponent": "BW-PLA-IP", "RefTitle": "BW-IP: Preliminary clarification of Planning Application Kit (PAK) message", "RefUrl": "/notes/1694205"}, {"RefNumber": "1637148", "RefComponent": "BW-PLA-IP", "RefTitle": "BW on HANA: Activation of Planning Application Kit", "RefUrl": "/notes/1637148"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2000002", "RefComponent": "HAN-DB-PERF", "RefTitle": "FAQ: SAP HANA SQL Optimization", "RefUrl": "/notes/2000002 "}, {"RefNumber": "2868496", "RefComponent": "BW-PLA-IP", "RefTitle": "System error  LCL_CHECK_COLLECTOR LCL_CHECK_COLLECTOR~EXECUTE_CHECK-01-", "RefUrl": "/notes/2868496 "}, {"RefNumber": "2564991", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "BW-IP (PAK): Error \"PE error 38.001: Condition 'getCalcScenario()->saveScenario()' fail\" - Message no. RSR_PE005", "RefUrl": "/notes/2564991 "}, {"RefNumber": "2382829", "RefComponent": "HAN-DB-ENG-PLE", "RefTitle": "Indexserver Crash at PlanningEngine::PleFormulaPop::executePlePop While Executing Planning Function", "RefUrl": "/notes/2382829 "}, {"RefNumber": "2343286", "RefComponent": "EPM-BPC-BW4-PLA", "RefTitle": "Planning on BW/4HANA", "RefUrl": "/notes/2343286 "}, {"RefNumber": "1972819", "RefComponent": "CO-OM", "RefTitle": "Setup SAP BPC optimized for S/4 HANA Finance and Embedded BW Reporting (aka Integrated Business Planning for Finance)", "RefUrl": "/notes/1972819 "}, {"RefNumber": "1666756", "RefComponent": "BW-PLA-BPS", "RefTitle": "BW-BPS/SEM-BPS on SAP BW 7.3x/7.4x/7.50, powered by HANA DB", "RefUrl": "/notes/1666756 "}, {"RefNumber": "2110152", "RefComponent": "BW-PLA-IP", "RefTitle": "Support 0CLIENT in PAK", "RefUrl": "/notes/2110152 "}, {"RefNumber": "2105252", "RefComponent": "BW-PLA-IP", "RefTitle": "BPC or PAK License Usage", "RefUrl": "/notes/2105252 "}, {"RefNumber": "2054183", "RefComponent": "BW-PLA-IP", "RefTitle": "BW-IP: Maintenance of Fiscal Year Variants (SAP HANA)", "RefUrl": "/notes/2054183 "}, {"RefNumber": "2052373", "RefComponent": "BW-BEX-OT-WSP", "RefTitle": "Pilot Note to disable PAK auditing for planable local Provider", "RefUrl": "/notes/2052373 "}, {"RefNumber": "1929531", "RefComponent": "BW-BCT-CO-PC", "RefTitle": "Product Cost Forecast and Simulation", "RefUrl": "/notes/1929531 "}, {"RefNumber": "1930335", "RefComponent": "BW-PLA-IP", "RefTitle": "BW-IP (PAK): Default data processing option for InfoProvider", "RefUrl": "/notes/1930335 "}, {"RefNumber": "1927309", "RefComponent": "BW-PLA-IP", "RefTitle": "Tracing for reason why PAK switch does not seem to work", "RefUrl": "/notes/1927309 "}, {"RefNumber": "1824516", "RefComponent": "BW-BEX-OT", "RefTitle": "Missing analysis report for planning func.: SAP HANA - Mem", "RefUrl": "/notes/1824516 "}, {"RefNumber": "1929130", "RefComponent": "BW-PLA-IP", "RefTitle": "BW-IP (PAK): Char. relations, data slices and ref. chars.", "RefUrl": "/notes/1929130 "}, {"RefNumber": "1928715", "RefComponent": "BW-PLA-IP", "RefTitle": "SQL-Script based planning exits in PAK", "RefUrl": "/notes/1928715 "}, {"RefNumber": "1637148", "RefComponent": "BW-PLA-IP", "RefTitle": "BW on HANA: Activation of Planning Application Kit", "RefUrl": "/notes/1637148 "}, {"RefNumber": "1919631", "RefComponent": "BW-PLA-BPC", "RefTitle": "Activating the BPC embedded", "RefUrl": "/notes/1919631 "}, {"RefNumber": "1735590", "RefComponent": "BW-PLA-IP", "RefTitle": "Q&A on DSO Planning", "RefUrl": "/notes/1735590 "}, {"RefNumber": "1694205", "RefComponent": "BW-PLA-IP", "RefTitle": "BW-IP: Preliminary clarification of Planning Application Kit (PAK) message", "RefUrl": "/notes/1694205 "}, {"RefNumber": "1778939", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "SAP HANA planning functions: Further developments", "RefUrl": "/notes/1778939 "}, {"RefNumber": "1770553", "RefComponent": "BW-PLA-IP", "RefTitle": "PAK: Hana optimized planning notes in BW 7.30 SP9", "RefUrl": "/notes/1770553 "}, {"RefNumber": "1802658", "RefComponent": "BW-PLA-IP", "RefTitle": "BW-IP: PAK and logging BAdI", "RefUrl": "/notes/1802658 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "731", "To": "731", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "740", "To": "740", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "750", "To": "751", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}