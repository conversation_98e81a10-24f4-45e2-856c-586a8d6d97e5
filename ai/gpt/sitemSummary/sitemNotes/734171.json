{"Request": {"Number": "734171", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 496, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015660002017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000734171?language=E&token=942DF5CAC69F4C21704DB032428A6EDE"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000734171", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000734171/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "734171"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.05.2004"}, "SAPComponentKey": {"_label": "Component", "value": "FIN-SEM-CPM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Corporate Performance Monitor"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financials", "value": "FIN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Strategic Enterprise Management", "value": "FIN-SEM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-SEM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Corporate Performance Monitor", "value": "FIN-SEM-CPM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-SEM-CPM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "734171 - SEM 3.5 front-end Support Package 3 (May 2004)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>SEM 3.1A/B,SEM 3.2, SEM 3.5: An incorrect display occurs with the chart control for the portfolio diagram and with diagrams with values selected as invalid.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Support Package, SAPGUI, front end, front, graphic, installation, SAPClient, SEM, front-end Support Package, SEMOCX, Balanced Scorecard,BSC</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Prerequisite is at least 4.6D GUI Compilation 3. Downward compatible to SEM 2.0B, SEM 3.0A, SEM 3.1A, SEM 3.1B, SEM 3.2, SEM 3.5</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Starting with Release 4.6D Compilation 3 the SAPGUI installation provides a new tool for the application of Support Packages. Instead of unpacking Support Packages manually on the installation server as before, the import of the Support Package occurs automatically. In this case, the system also updates the installation database on the installation server in addition to the copying of the Support Package files. Clients, on which SAPSetup is started, can recognize in this way that the server has received a new Support Package and can update themselves correspondingly. As of the following Support Package levels, you can only install Support Packages with the help this tool:<br /></p> <UL><LI>sem31a:&#x00A0;&#x00A0;&#x00A0;&#x00A0; 4</LI></UL> <UL><LI>setup46D:&#x00A0;&#x00A0; 5</LI></UL> <p>The manual unpacking is NOT possible anymore with these Support Packages and their successores (see Note 361222)</p> <OL>1. Prepare an installation server by starting setup.exe and selecting \"Administrative set-up\".</OL> <OL>2. By downloading the archive of SapservX and copying the files into the installatino directory of the server. Procedure:</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;a) By downloading the archive sem31a.exe - SEM 3.5 Support Package 3 patch ftp://sapservX/general/frontend/patches/rel610/Windows/Win32/ <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;b) Downloading locally onto the GUI server <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;c) On the installation server, start SapSetup -&gt; Configuration -&gt; Program SapAdmin from the \\netinst directory. Consider that the call has to occur via an UNC path: \\\\&lt;Servername&gt;\\&lt;ShareName&gt;\\netinst\\sapadmin.exe <OL>3. Select the menu option Extras --&gt; Import Support Package</OL> <OL>4. Specify the Support Package file which you want to install.</OL> <OL>5. Select \"Install\" On the clients:</OL> <OL>6. Start the installation very usually with Netsetup, select SEM Add-On and install it. Do not register OCX files manually. A correct deinstallation of Netinstall is then not ensured anymore!</OL> <p>Stand-alone computer. If no installation server is available, nevertheless, you have the option to install the new Support Package. For this purpose you need the Setup update: localpat46D_Y.exe (Y designates the current Support Package level) which is available on: sapservx/general/frontend/patches/rel46D/Windows/Win32 (see Note 361222). You require an SAP chart&#x00A0;&#x00A0;OCX both for Balanced Scorecard and for Management Cockpit as well for the display of graphic components. You can also find the most current version of this component on sapservX. CSN Note 318196 describes how you can receive and install this Support Package. We recommand to always import the most current Support Package here. The SEM components require the basic chart components. Install them with the most current front-end Support Package. Installation of the most current front-end Support Package of, for example, sapserv3: for 6.10: ftp://sapserv3/general/frontend/patches/rel610/Windows/Win32/<br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D030766)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I023479)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000734171/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000734171/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000734171/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000734171/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000734171/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000734171/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000734171/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000734171/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000734171/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "96885", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Downloading front end patches", "RefUrl": "/notes/96885"}, {"RefNumber": "656975", "RefComponent": "FIN-SEM-CPM", "RefTitle": "SEM 3.5 frontend patch 1 (Oct.2003)", "RefUrl": "/notes/656975"}, {"RefNumber": "598364", "RefComponent": "FIN-SEM-CPM", "RefTitle": "Error when calling graphic OCX in SEM-CPM", "RefUrl": "/notes/598364"}, {"RefNumber": "571477", "RefComponent": "FIN-SEM", "RefTitle": "Installation of SAP SEM Release 3.2", "RefUrl": "/notes/571477"}, {"RefNumber": "566094", "RefComponent": "FIN-SEM", "RefTitle": "Upgrade to SAP SEM Release 3.2", "RefUrl": "/notes/566094"}, {"RefNumber": "454415", "RefComponent": "FIN-SEM", "RefTitle": "Short dump CNTL_ERROR for transactions in SEM", "RefUrl": "/notes/454415"}, {"RefNumber": "396640", "RefComponent": "FIN-SEM-CPM", "RefTitle": "Implementing SEM 3.0A Frontend patch", "RefUrl": "/notes/396640"}, {"RefNumber": "361222", "RefComponent": "BC-FES-INS", "RefTitle": "SapPatch: Importing GUI Patches", "RefUrl": "/notes/361222"}, {"RefNumber": "314973", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/314973"}, {"RefNumber": "311212", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/311212"}, {"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519 "}, {"RefNumber": "454415", "RefComponent": "FIN-SEM", "RefTitle": "Short dump CNTL_ERROR for transactions in SEM", "RefUrl": "/notes/454415 "}, {"RefNumber": "656975", "RefComponent": "FIN-SEM-CPM", "RefTitle": "SEM 3.5 frontend patch 1 (Oct.2003)", "RefUrl": "/notes/656975 "}, {"RefNumber": "396640", "RefComponent": "FIN-SEM-CPM", "RefTitle": "Implementing SEM 3.0A Frontend patch", "RefUrl": "/notes/396640 "}, {"RefNumber": "361222", "RefComponent": "BC-FES-INS", "RefTitle": "SapPatch: Importing GUI Patches", "RefUrl": "/notes/361222 "}, {"RefNumber": "96885", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Downloading front end patches", "RefUrl": "/notes/96885 "}, {"RefNumber": "571477", "RefComponent": "FIN-SEM", "RefTitle": "Installation of SAP SEM Release 3.2", "RefUrl": "/notes/571477 "}, {"RefNumber": "598364", "RefComponent": "FIN-SEM-CPM", "RefTitle": "Error when calling graphic OCX in SEM-CPM", "RefUrl": "/notes/598364 "}, {"RefNumber": "566094", "RefComponent": "FIN-SEM", "RefTitle": "Upgrade to SAP SEM Release 3.2", "RefUrl": "/notes/566094 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46C", "To": "46D", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}