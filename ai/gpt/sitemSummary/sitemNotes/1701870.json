{"Request": {"Number": "1701870", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 363, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017414242017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001701870?language=E&token=FF074F4359B8B56380E42A5CA681FFFD"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001701870", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001701870/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1701870"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.01.2024"}, "SAPComponentKey": {"_label": "Component", "value": "BC-MID-RFC"}, "SAPComponentKeyText": {"_label": "Component", "value": "RFC"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Middleware", "value": "BC-MID", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-MID*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "RFC", "value": "BC-MID-RFC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-MID-RFC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1701870 - RFC client communication supporting SNC without SSO"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You've configured your RFC client program to use SNC. In addition, you configured a correct user/password combination as credentials in order to logon to the backend system. However, the logon still fails with an error message signalling that the specified user does not fit to the provided SNC identity.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SSO, SNC, SNC mapping</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Up to the extension described in this note, the&#160;implementation in the ABAP backend always leads to a Single-Signon behavior. This means, the user associated with the SNC identity in the corresponding mapping table VUSREXTID will be logged on automatically to the ABAP backend system. When specifying a user in addition, the logon module checks whether the user really fits to the SNC identity. If this is not the case, the logon fails.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Make sure to use the latest version of your RFC client/connector SDK. A new configuration option has been added to all of the following libraries, which allows to turn off the SSO behavior from RFC client side. The required minimum patch level is mentioned below:</p>\r\n<ul>\r\n<li>NCo 3.0.7 (destination configuration option SNC_SSO)</li>\r\n</ul>\r\n<ul>\r\n<li>JCo 3.0.9 (destination configuration property jco.client.snc_sso)</li>\r\n</ul>\r\n<ul>\r\n<li>NW RFC SDK 7.20 PL 9 (configuration option SNC_SSO)</li>\r\n</ul>\r\n<ul>\r\n<li>SAP Business Connector 4.8 Service Release 8 (configuration option \"SNC Login with User/Password\")</li>\r\n</ul>\r\n<p><br />In all client SDKs, the default is to use the Single-Signon mechanism of SNC for also doing the RFC logon to the system, reflecting a value of '1'. By setting the connector configuration value to '0', SNC is only used for establishing a secure network communication (e.g. with encrypting the line if SNC privacy protection has been chosen), but the SNC identity is not used&#160;for logging on to the ABAP backend system anymore. In this case, the additionally provided user credentials&#160;will be used for doing the RFC logon. Nevertheless, please note that an SNC identity is always required for using any SNC services and for doing the SNC handshake before the logon takes place.<br /><br />In addition to the new configuration option on client side, an AS ABAP side enhancement is required as well, which is included starting with the kernel patch levels as mentioned in the \"SP Patch Level\" section of this note.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-MID-RFC-SDK (NetWeaver RFC SDK)"}, {"Key": "Other Components", "Value": "BC-MID-CON-NCO (SAP .Net Connector)"}, {"Key": "Other Components", "Value": "BC-MID-BUS (Business Connector)"}, {"Key": "Other Components", "Value": "BC-MID-CON-JCO (Java-Connector)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D027654)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D062660)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001701870/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001701870/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001701870/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001701870/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001701870/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001701870/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001701870/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001701870/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001701870/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3415167", "RefComponent": "BC-MID-CON-JCO", "RefTitle": "SNC connection with user and password fails with Logon data incomplete error", "RefUrl": "/notes/3415167"}, {"RefNumber": "1795909", "RefComponent": "BC-MID-BUS", "RefTitle": "Business Connector 4.8 Service Release 8 (SR8)", "RefUrl": "/notes/1795909"}, {"RefNumber": "1690662", "RefComponent": "BC-SEC-SNC", "RefTitle": "Option: Blocking unencrypted SAPGUI/RFC connections", "RefUrl": "/notes/1690662"}, {"RefNumber": "1686380", "RefComponent": "BC-MID-CON-NCO", "RefTitle": "SAP .NET Connector Version 3.0.7", "RefUrl": "/notes/1686380"}, {"RefNumber": "1671480", "RefComponent": "BC-MID-CON-JCO", "RefTitle": "SAP Java Connector Release 3.0.9", "RefUrl": "/notes/1671480"}, {"RefNumber": "1643878", "RefComponent": "BC-IAM-SSO-SL", "RefTitle": "Release Notes for SNC Client Encryption", "RefUrl": "/notes/1643878"}, {"RefNumber": "1616598", "RefComponent": "BC-SEC-LGN", "RefTitle": "Enabling RFC password logon despite SNC", "RefUrl": "/notes/1616598"}, {"RefNumber": "1561161", "RefComponent": "BC-SEC-LGN", "RefTitle": "Enabling SAP GUI password logon despite using SNC", "RefUrl": "/notes/1561161"}, {"RefNumber": "1028503", "RefComponent": "BC-SEC-LGN", "RefTitle": "SNC-secured RFC connection: Logon ticket is ignored", "RefUrl": "/notes/1028503"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1795909", "RefComponent": "BC-MID-BUS", "RefTitle": "Business Connector 4.8 Service Release 8 (SR8)", "RefUrl": "/notes/1795909 "}, {"RefNumber": "1671480", "RefComponent": "BC-MID-CON-JCO", "RefTitle": "SAP Java Connector Release 3.0.9", "RefUrl": "/notes/1671480 "}, {"RefNumber": "1028503", "RefComponent": "BC-SEC-LGN", "RefTitle": "SNC-secured RFC connection: Logon ticket is ignored", "RefUrl": "/notes/1028503 "}, {"RefNumber": "1690662", "RefComponent": "BC-SEC-SNC", "RefTitle": "Option: Blocking unencrypted SAPGUI/RFC connections", "RefUrl": "/notes/1690662 "}, {"RefNumber": "1643878", "RefComponent": "BC-IAM-SSO-SL", "RefTitle": "Release Notes for SNC Client Encryption", "RefUrl": "/notes/1643878 "}, {"RefNumber": "1686380", "RefComponent": "BC-MID-CON-NCO", "RefTitle": "SAP .NET Connector Version 3.0.7", "RefUrl": "/notes/1686380 "}, {"RefNumber": "1616598", "RefComponent": "BC-SEC-LGN", "RefTitle": "Enabling RFC password logon despite SNC", "RefUrl": "/notes/1616598 "}, {"RefNumber": "1561161", "RefComponent": "BC-SEC-LGN", "RefTitle": "Enabling SAP GUI password logon despite using SNC", "RefUrl": "/notes/1561161 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "KRNL32NUC", "From": "7.20", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.20", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.20", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.20", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "SAP JCO", "From": "3.0", "To": "3.0", "Subsequent": "X"}, {"SoftwareComponent": "SAP .NET CONNECTOR", "From": "3.0", "To": "3.0", "Subsequent": "X"}, {"SoftwareComponent": "NWRFCSDK", "From": "7.20", "To": "7.20", "Subsequent": "X"}, {"SoftwareComponent": "KERNEL", "From": "7.20", "To": "7.21", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP023", "SupportPackagePatch": "000023", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT", "SupportPackage": "SP023", "SupportPackagePatch": "000023", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT UNICODE", "SupportPackage": "SP023", "SupportPackagePatch": "000023", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP023", "SupportPackagePatch": "000023", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT", "SupportPackage": "SP023", "SupportPackagePatch": "000023", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021283&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP023", "SupportPackagePatch": "000023", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT UC", "SupportPackage": "SP023", "SupportPackagePatch": "000023", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021284&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP023", "SupportPackagePatch": "000023", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT", "SupportPackage": "SP313", "SupportPackagePatch": "000313", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013053&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 EXT 64-BIT UC", "SupportPackage": "SP313", "SupportPackagePatch": "000313", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200013910&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 EXT 64-BIT", "SupportPackage": "SP313", "SupportPackagePatch": "000313", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200013911&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 EXT 32-BIT UC", "SupportPackage": "SP313", "SupportPackagePatch": "000313", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200013912&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 EXT 32-BIT", "SupportPackage": "SP313", "SupportPackagePatch": "000313", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200013913&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT UNICODE", "SupportPackage": "SP313", "SupportPackagePatch": "000313", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013054&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT", "SupportPackage": "SP313", "SupportPackagePatch": "000313", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013055&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT UNICODE", "SupportPackage": "SP313", "SupportPackagePatch": "000313", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013056&V=MAINT"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}