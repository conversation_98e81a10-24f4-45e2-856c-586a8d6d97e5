{"Request": {"Number": "2569950", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 403, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000487352018"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002569950?language=E&token=88086CE38234BB878A83B1641F2290BE"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002569950", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002569950/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2569950"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 14}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "21.10.2022"}, "SAPComponentKey": {"_label": "Component", "value": "SD-BIL-RA"}, "SAPComponentKeyText": {"_label": "Component", "value": "SD Integration Revenue Accounting & Reporting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Sales and Distribution", "value": "SD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Billing", "value": "SD-BIL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD-BIL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SD Integration Revenue Accounting & Reporting", "value": "SD-BIL-RA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD-BIL-RA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2569950 - FAQ: Migration & Operational Load in the SD Integration Component"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You have recently started or planning to start the implementation of the SAP Revenue Accounting and Reporting (SAP RAR) functionality and you are about to migrate your existing Sales &amp; Distribution processes into this new solution. In this SAP Note you can find the frequently asked questions and answers about this process.</p>\r\n<p><a target=\"_blank\" name=\"TOC\"></a>&#65279;Table of contents:</p>\r\n<ol>\r\n<li><a target=\"_self\" href=\"#FAQ1\">Is there any general information&#160;available for Customers&#160;about migration and operational load?</a></li>\r\n<ol style=\"list-style-type: lower-alpha;\">\r\n<li><a target=\"_self\" href=\"#FAQ1a\">Can you explain us on a high level how the operational load program works and what is happening during migration?</a></li>\r\n</ol>\r\n<li><a target=\"_self\" href=\"#FAQ2\">What tools are provided by SAP to support the operational load/migration of SD documents?</a></li>\r\n<li><a target=\"_self\" href=\"#FAQ3\">Do I have to migrate all documents related to my sales process manually, like orders, invoices, deliveries and financial documents?</a></li>\r\n<li><a target=\"_self\" href=\"#FAQ4\">Can I migrate selected items of a sales document?</a></li>\r\n<li><a target=\"_self\" href=\"#FAQ5\">What does it mean that a sales document item is \"excluded\" or \"skipped\" from migration?</a></li>\r\n<li><a target=\"_self\" href=\"#FAQ6\">What happens if an existing item category is marked with revenue accounting relevance but some of the sales documents where this is used have already been migrated?</a></li>\r\n<li><a target=\"_self\" href=\"#FAQ7\">Can I migrate all standard SD processes to SAP RAR?</a></li>\r\n<ol style=\"list-style-type: lower-alpha;\">\r\n<li><a target=\"_self\" href=\"#FAQ7a\">Do I have to migrate completed processes?</a></li>\r\n<li><a target=\"_self\" href=\"#FAQ7b\">What can I do if some of my documents were archived in the to-be-migrated process?</a></li>\r\n<li><a target=\"_self\" href=\"#FAQ7c\">Can I migrate proof-of-delivery relevant processes?</a></li>\r\n<li><a target=\"_self\" href=\"#FAQ7d\">Can I migrate processes which have different billing currency than order currency?</a></li>\r\n<li><a target=\"_self\" href=\"#FAQ7e\">Can I migrate proforma invoices?</a></li>\r\n<li><a target=\"_self\" href=\"#FAQ7f\">Can I migrate cross-company sales processes?</a></li>\r\n</ol>\r\n<li><a target=\"_self\" href=\"#FAQ8\">Can I migrate SD Revenue Recognition processes to SAP RAR?</a></li>\r\n<ol style=\"list-style-type: lower-alpha;\">\r\n<li><a target=\"_self\" href=\"#FAQ8a\">Which SD Revenue Recognition processes do I have to migrate?</a></li>\r\n<li><a target=\"_self\" href=\"#FAQ8b\">What happens to open SD&#160;Revenue Recognition&#160;processes during migration?</a></li>\r\n<li><a target=\"_self\" href=\"#FAQ8c\">Can I migrate completed SD Revenue Recognition processes?</a></li>\r\n<li><a target=\"_self\" href=\"#FAQ8d\">Can I migrate cancelled revenue lines?</a></li>\r\n<li><a target=\"_self\" href=\"#FAQ8e\">Can I migrate manually completed documents?</a></li>\r\n<li><a target=\"_self\" href=\"#FAQ8f\">Can I migrate all event-based processes?</a></li>\r\n<li><a target=\"_self\" href=\"#FAQ8g\">Can I migrate the revenue schedule which was forecasted by SD Revenue Recognition?</a></li>\r\n<li><a target=\"_self\" href=\"#FAQ8h\">Can I migrate FASB 52 relevant documents?</a></li>\r\n<li><a target=\"_self\" href=\"#FAQ8i\">What happens if a billing document which was relevant for SD Revenue Recognition gets cancelled after its migration?</a></li>\r\n</ol>\r\n<li><a target=\"_self\" href=\"#FAQ9\">What is the revenue correction report?</a></li>\r\n<ol style=\"list-style-type: lower-alpha;\">\r\n<li><a target=\"_self\" href=\"#FAQ9a\">How do I know if I should use the revenue correction report?</a></li>\r\n</ol>\r\n<li><a target=\"_self\" href=\"#FAQ10\">I have received an error during operational load, can I undo and restart it?</a></li>\r\n<li><a target=\"_self\" href=\"#FAQ11\">Are cancelled billing documents migrated?</a></li>\r\n<li><a target=\"_self\" href=\"#FAQ12\">What happens to incomplete sales documents during migration?</a></li>\r\n<li><a target=\"_self\" href=\"#FAQ13\">What is a migration package and how can I utilize it?</a></li>\r\n<li><a target=\"_self\" href=\"#FAQ14\">Why does the operational load generate RAIs without initial load indicator?</a></li>\r\n<li><a target=\"_self\" href=\"#FAQ15\">Is it possible to migrate costs?</a></li>\r\n</ol>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SD Revenue Accounting, SD IC, Integration component, SD Revenue Recognition, migration, FAQ, frequently asked questions, answers</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong><a target=\"_blank\" name=\"FAQ1\"></a>&#65279;1. Is there any general information&#160;available for Customers&#160;about migration and operational load?</strong></p>\r\n<p style=\"padding-left: 30px;\">Yes there are documentations available on the SAP help ... :</p>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"https://help.sap.com/viewer/product/SAP_REVENUE_ACCOUNTING_AND_REPORTING/1.3.3/en-US\">https://help.sap.com/viewer/product/SAP_REVENUE_ACCOUNTING_AND_REPORTING/1.3.3/en-US</a></li>\r\n<li><a target=\"_blank\" href=\"https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3.3/en-US/565e63540a7b8f4ce10000000a4450e5.html\">https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3.3/en-US/565e63540a7b8f4ce10000000a4450e5.html</a></li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">... and FAQ Notes in the Revenue Accounting Engine:</p>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"/notes/2580961\">2580961</a> - RAR Data Migration and Transition - Additional Information</li>\r\n<li><a target=\"_blank\" href=\"/notes/2616387\">2616387</a> - Important Performance Considerations when implementing SAP Revenue Accounting and Reporting</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">... and the Migration Guide for SD Revenue Recognition Customers attached to the following Note:</p>\r\n<ul>\r\n<li>\r\n<p><a target=\"_blank\" href=\"/notes/2733866\">2733866</a>&#160;-&#160; Migration Guide for SD Revenue Recognition to Revenue Accounting and Reporting</p>\r\n</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\"><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></p>\r\n<p style=\"padding-left: 30px;\"><strong><a target=\"_blank\" name=\"FAQ1a\"></a>&#65279;1.a Can you explain us on a high level how the operational load program works and what is happening during migration?</strong></p>\r\n<p style=\"padding-left: 30px;\">The migration is a quite complex sequence of actions during which sales documents, deliveries and billing documents are marked with the revenue account relevance and their legacy information is extracted from the&#160;corresponding posting entries from financials. During this transformation SD01, SD02 and SD03 class revenue account items (RAIs) are sent to SAP RAR along with the special migration specific legacy RAIs used for the cumulative catchup.</p>\r\n<p style=\"padding-left: 30px;\">In the first step the operational load program collects all sales documents from the system based on the input parameters of the FARRIC_OL report. This initial worklist is then extended using the document flow of the sales documents found in the first step. The documents are sorted into three sub-categories: <strong>orders</strong> <em>(sales order, sales contract, credit memo request, debit memo request, free order, scheduling agreement, return order)</em>, <strong>deliveries</strong> <em>(outbound delivery, return delivery)</em> and <strong>billing documents</strong> <em>(invoice, invoice cancellation, credit memo, debit memo, credit memo cancellation, intercompany billing document, intercompany credit memo)</em>.</p>\r\n<p style=\"padding-left: 30px;\">Each order item is validated with a number of consistency checks and the items which are not excluded (see point 5) are migrated to revenue accounting. The migration is performed using a sales document change BAPI call (VA02) where each item gets marked with the revenue accounting relevance from the customizing (VBKD-FARR_RELTYPE). If the process was relevant for SD Revenue Recognition that obsolete relevance flag is initialized (VBKD-RRREL). This change in the revenue accounting relevance indicator also impacts some core Sales areas, for example pricing must derive a G/L account for each pricing condition for revenue accounting relevant items and each item must have a profitability segment number assigned to it. These changes are all automatically performed &amp; saved in the corresponding sales document. This means that the migration process may change your existing sales documents to be able to provide all the required data to revenue accounting. Once these changes are saved and committed to the database the SDOI (&amp; SDPI) RAIs are sent to the RAI monitor. Depending on which functionalities you have activated the order processing may result in additional acceptance date fulfillment RAIs (SDFI AD), incoming vendor invoice fulfillment RAIs (SDFI PI) or release order fulfillment RAIs (SDFI RO).</p>\r\n<p style=\"padding-left: 30px;\">After the orders are completed each delivery is processed one by one. The delivery items are marked with the same revenue accounting relevance which was saved in their referenced sales process in the first processing block (LIPS-FARR_RELTYPE). Other than this there is no change in existing deliveries. Once these changes are saved and committed to the database the SDFI RAIs are sent to the RAI monitor. Depeding on which functionalities you have activated the delivery processing may also result in additional proof of delivery fulfillment RAIs (SDFI PD)</p>\r\n<p style=\"padding-left: 30px;\">After the deliveries each billing document is processed one by one. The billing document items are also marked with the same revenue accounting relevance which was saved in their referenced sales process in the first processing block (VBRP-FARR_RELTYPE). If the process was relevant for SD Revenue Recognition that relevance flag is initializd (VBRP-RRREL). Other than this there is no change in the existing billing documents. Once these changes are saved and committed to the database the SDII RAIs are sent to the RAI monitor. Depending on which functionalities you have activated the invoice processing may result in intercompany billing fulfillment RAIs (SDFI IB) instead of SDII RAIs.</p>\r\n<p style=\"padding-left: 30px;\">During order, delivery and billing document processing the operational load program collects the total realized revenue amount for each process. In regular sales processes this is the total billed amount up to the transfer date, in SD Revenue Recognition processes this is calculated from the realized revenue lines up to the transfer date (VBREVE). These realized legacy amounts are consolidated into legacy condition items (LEGACYC). The general process information is consolidated into legacy main items (LEGACY), whereas the revenue schedules from time-based SD Revenue Recognition processes can be converted to legacy revenue schedule (LEGACYSF).</p>\r\n<p style=\"padding-left: 30px;\">Once all orders, deliveries and billing documents are processed these three types of legacy data are passed to the revenue accounting initial load API. They are then included into the corresponding performance obligations and contracts and the migration is completed.</p>\r\n<p style=\"padding-left: 30px;\">In specific SD Revenue Recognition processes a post-migration activity may be required - the execution of the revenue correction report. (See 9 and 9a)</p>\r\n<p style=\"padding-left: 30px;\"><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></p>\r\n<p><strong><a target=\"_blank\" name=\"FAQ2\"></a>&#65279;2. </strong><strong>&#65279;</strong><strong>What tools are provided by SAP to support the operational load/migration of SD documents?</strong></p>\r\n<p style=\"padding-left: 30px;\">The Operational Load report<br /> Transaction FARRIC_OL<br /> <br /> The Operational Load report&#180;s expert mode<br /> Transaction FARRIC_OL_EXPERT<br /> <br /> The Operational Load report&#180;s cleanup (SAP RAR tool)<br /> Transaction FARRIC_IL_CLEANUP</p>\r\n<p style=\"padding-left: 30px;\">The Correction report<br />Transaction&#160;FARRIC_RR_CORR</p>\r\n<p style=\"padding-left: 30px;\">The Correction report&#180;s&#160;expert mode<br />Transaction FARRIC_RR_CORR_EXP</p>\r\n<p style=\"padding-left: 30px;\"><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></p>\r\n<p><strong><a target=\"_blank\" name=\"FAQ3\"></a>&#65279;3. Do I have to migrate all documents related to my sales process manually, like orders, invoices, deliveries and financial documents?</strong></p>\r\n<p style=\"padding-left: 30px;\">No, the migration can only be triggered on sales document level and the operational load program takes care of the collecting and processing all relevant follow-on documents.</p>\r\n<p style=\"padding-left: 30px;\"><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></p>\r\n<p><strong><a target=\"_blank\" name=\"FAQ4\"></a>&#65279;4. Can I migrate selected items of a sales document?</strong></p>\r\n<p style=\"padding-left: 30px;\">Generally the migration is performed on sales document level so you cannot enter a specific sales document item for migration. The standard operational load program first performs validations on each sales document (item) and the processes which are not supported are excluded from the migration. It is possible to implement customer-specific logic to exclude further documents or items using BADI&#160;FARRIC_OL_VALIDATION (enhancement spot&#160;FARRIC_SD_OL).<strong><br /></strong></p>\r\n<p style=\"padding-left: 30px;\">The sales document items that were not excluded by the validations are cross-checked with the customizing settings from SPRO activity \"Maintain Revenue Accounting Item Settings\". The sales document items with revenue accounting relevant item categories are processed by the operational load.</p>\r\n<p style=\"padding-left: 30px;\"><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></p>\r\n<p><strong><a target=\"_blank\" name=\"FAQ5\"></a>&#65279;5. What does it mean that a sales document item is \"excluded\" or \"skipped\" from migration?</strong></p>\r\n<p style=\"padding-left: 30px;\">There are several validation checks implemented in the operational load tool to ensure consistent migration. If any of these checks fail the impacted sales document or sales document item is excluded from the current operational load run.</p>\r\n<p style=\"padding-left: 30px;\">This means that sales document (item) is not updated with the revenue accounting relevance flag, there are no revenue accounting items generated and the follow-on documents related to the given sales document (item) are also skipped from the migration. The processing of such sales processes remains fully in the SD application. In other words, the migration tool ignores the sales document (item) entirely.</p>\r\n<p style=\"padding-left: 30px;\">There are special cases where documents or items are excluded due to resolvable issues e.g.: the document must be updated first, correction postings must be done first, the document must be unlocked. In these cases, once the corresponding root cause is resolved the operational load can be repeated (see point 6.). If the issues are not resolvable though (see points 7. and 8.) then the item in question cannot be consistently transferred to revenue accounting with the Standard tools offered by SAP.</p>\r\n<p style=\"padding-left: 30px;\"><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></p>\r\n<p><strong><a target=\"_blank\" name=\"FAQ6\"></a>&#65279;6. What happens if an existing item category is marked with revenue accounting relevance but some of the sales documents where this is used have already been migrated?</strong></p>\r\n<p style=\"padding-left: 30px;\">Sales documents can be reprocessed with the operational load tool without the need for resetting the previous operational load runs. Already migrated document items are not changed, however the remaining items and their follow-on documents which have become relevant for revenue accounting since the latest run are migrated. A re-run can be executed with a new migration package ID, so there is no need to worry if the original migration package has already been changed to productive status.</p>\r\n<p style=\"padding-left: 30px;\"><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></p>\r\n<p><strong><a target=\"_blank\" name=\"FAQ7\"></a>&#65279;7. Can I migrate all standard SD processes to SAP RAR?</strong></p>\r\n<p style=\"padding-left: 30px;\">No, some SD processes are currently not supported by the Revenue Accounting Engine. See further sub-questions and SAP Note <a target=\"_blank\" href=\"/notes/2591055\">2591055</a>.</p>\r\n<p style=\"padding-left: 30px;\"><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></p>\r\n<p><strong>&#160; &#160; &#160; <a target=\"_blank\" name=\"FAQ7a\"></a>&#65279;7.a Do I have to migrate completed processes?</strong></p>\r\n<p style=\"padding-left: 60px;\">No, in the expert version of the operational load report the screen parameter \"Skip completed Sales Documents\" controls whether completed sales documents are processed or not.</p>\r\n<p style=\"padding-left: 60px;\"><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></p>\r\n<p style=\"padding-left: 30px;\"><strong><a target=\"_blank\" name=\"FAQ7b\"></a>&#65279;7.b What can I do if some of my documents were archived in the to-be-migrated process?</strong></p>\r\n<p style=\"padding-left: 60px;\">As of SAP Note&#160;<a target=\"_blank\" href=\"/notes/2364641\">2364641</a>,&#160;in the expert version of the operational load report the screen parameter \"Read from Archive\" can enable the reading &amp; evaluation of archived documents during operational load.</p>\r\n<p style=\"padding-left: 60px;\"><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></p>\r\n<p style=\"padding-left: 30px;\"><strong><a target=\"_blank\" name=\"FAQ7c\"></a>&#65279;7.c Can I migrate proof-of-delivery relevant processes?</strong></p>\r\n<p style=\"padding-left: 60px;\">Yes, sales processes that are relevant for proof-of-delivery can be migrated as part of new functionality delivered in SP18/SP19. (Notes 2691932, 2698524, 2702194, 2715963, 2726341, 2694114)<br /><br /> <em>The technical field for this check is: VBKD-PDSTA.</em></p>\r\n<p style=\"padding-left: 60px;\"><em><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></em></p>\r\n<p style=\"padding-left: 30px;\"><strong><a target=\"_blank\" name=\"FAQ7d\"></a>&#65279;7.d Can I migrate processes which have different billing currency than order currency?</strong></p>\r\n<p style=\"padding-left: 60px;\">No, these types of documents cannot be handled correctly by the Revenue Accounting Engine, therefore they are excluded from migration.</p>\r\n<p style=\"padding-left: 60px;\"><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></p>\r\n<p style=\"padding-left: 30px;\"><strong><a target=\"_blank\" name=\"FAQ7e\"></a>&#65279;7.e Can I migrate proforma invoices?</strong></p>\r\n<p style=\"padding-left: 60px;\">No, processes with only proforma invoices do not receive real invoices, therefore they cannot generate revenue. Individual proforma invoices created in actually billing-relevant processes are also excluded as they never generate a financials posting. Therefore, it makes no sense to transfer such documents to Revenue Accounting.</p>\r\n<p style=\"padding-left: 60px;\"><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></p>\r\n<p style=\"padding-left: 30px;\"><strong><a target=\"_blank\" name=\"FAQ7f\"></a>&#65279;7.f Can I migrate cross-company sales processes?</strong></p>\r\n<p style=\"padding-left: 60px;\">Yes, as of SAP Note&#160;<a target=\"_blank\" href=\"/notes/2338218\">2338218</a>, the migration of Cross-Company scenarios is also supported. <br />Costs of intercompany sales processes can also be processed in the new intercompany billing functionality delivered in SP18/SP19. (Note&#160;2651782,&#160;2649839,&#160;2654138)</p>\r\n<p style=\"padding-left: 60px;\"><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></p>\r\n<p><strong><a target=\"_blank\" name=\"FAQ8\"></a>&#65279;8. Can I migrate SD Revenue Recognition processes to SAP RAR?</strong></p>\r\n<p style=\"padding-left: 30px;\">Yes, the standard Operational Load report also supports the migration of SD Revenue Recognition processes.</p>\r\n<p style=\"padding-left: 30px;\"><strong>Type \"A\" - Time-based revenue recognition processes</strong>&#160;<br /> These document items are migrated into SDOI RAIs with fulfillment type = T (time-based) and deferral method = S (linear distribution)</p>\r\n<p style=\"padding-left: 30px;\"><strong>Type \"B\" - Service-based revenue recognition processes with delivery</strong>&#160;<br /> These document items are&#160;migrated into SDOI RAIs with fulfillment type = E (event) and event type = GI (goods issue)&#160;<br /> <br /> <strong>Type \"B\" - Service-based revenue recognition processes without delivery&#160;</strong><br /> These document items are migrated into SDOI RAIs with fulfillment type = T (time-based)&#160;and deferral method = S (linear distribution)<br /> <br /> <strong>Type \"D\" - Time-based and invoice-based revenue recognition processes</strong>&#160;<br />These documents items are migrated into SDIG RAIs with fulfillment type = T (time-based) and deferral method = S (linear distribution)<br />Migration details are explained in <strong>SAP Note&#160;<a target=\"_blank\" href=\"/notes/2719185\">2719185</a></strong><br /> <br /> <strong>Type \"F\" - Credit/debit memo processing with reference to preceding document&#160;<br /> </strong>These document items must <strong>NOT</strong>&#160;be migrated directly into new POBs, however using revenue accounting type \"M\" instead of \"X\" their reference processes should get updated with their value adjustments.</p>\r\n<p style=\"padding-left: 30px;\">The POB attribues like fulfillment type, event type or deferral method are hard-coded in the operational load to ensure the compatibility of the legacy SD Revenue Recognition processes with the new RAR processes. The BRF+ customizing does not redetermine these attributes. However, enhancement spot&#160;FARRIC_SD_OL offers a BADI called&#160;FARRIC_OL_LEGACY which can be used to modify these attributes in case of Customer-specific requirements. <span style=\"text-decoration: underline;\">Any custom attributes set here are considered a process modification and SAP reserves the right to reject support requests if non-Standard attributes are found in migrated POBs.</span></p>\r\n<p style=\"padding-left: 30px;\">There are numerous limitations and restrictions for the migration of SD Revenue Recognition processes.</p>\r\n<p style=\"padding-left: 30px;\"><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></p>\r\n<p style=\"padding-left: 30px;\"><strong><a target=\"_blank\" name=\"FAQ8a\"></a>&#65279;8.a Which SD Revenue Recognition processes do I have to migrate?</strong></p>\r\n<p style=\"padding-left: 30px;\">The SD Revenue Recognition functionality is no longer available in S/4HANA and since the migration relies on this functionality the operational load can no longer be executed after the upgrade to S/4HANA. Therefore, if you are about to upgrade to S/4HANA you must first migrate all SD Revenue Recognition processes in your system which are:</p>\r\n<ul style=\"padding-left: 30px;\">\r\n<ul>\r\n<li style=\"padding-left: 30px;\">Not fully delivered and invoiced</li>\r\n<li style=\"padding-left: 30px;\">Have deferred revenue still to be realized</li>\r\n<li style=\"padding-left: 30px;\">For which you expect follow-up activities such as increase quantity, create credit memo, or cancel invoice (only migrated processes can get follow-on documents in S/4HANA)</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">If you are <strong>not</strong> about to migrate to S/4HANA you can perform partial migration of selected sales processes using the revenue accounting item category relevance to control which documents are to be migrated. You can even keep the SD Revenue Recognition processes in your system and finish them with the legacy application and only create the new documents with revenue accounting relevance. (SD Revenue Recognition does not support price allocations or other IFRS15 requirements in the Standard, therefore if you need these features the migration must be performed nevertheless but this may vary in your individual processes)</p>\r\n<p style=\"padding-left: 30px;\"><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></p>\r\n<p style=\"padding-left: 30px;\"><strong><a target=\"_blank\" name=\"FAQ8b\"></a>&#65279;8.b What happens to open SD <strong>Revenue Recognition&#160;</strong>processes during migration?</strong></p>\r\n<p style=\"padding-left: 60px;\">During migration, the sales document items and billing document items lose their SD Revenue Recognition relevance flag, whereas the existing revenue lines get marked as \"migrated\". Follow-on documents like invoices or deliveries created after the migration&#160;for these document items will not be processed by the standard SD Revenue Recognition logic anymore. Migrated revenue lines are <strong>not</strong> proposed for processing by the standard Revenue Recognition transactions like VF44/VF46. Therefore, further processing in the classical SD Revenue Recognition application is no longer possible.</p>\r\n<p style=\"padding-left: 60px;\">Revenue lines which were realized in VF44 with a posting date after the transfer date, are marked for correction posting by the migration report. With the execution of the corresponding correction report, these entries are reverted similarly like the postings made via transaction VF46.</p>\r\n<p style=\"padding-left: 60px;\">Revenue lines which were realized in VF44 with a posted date before/on the transfer date are accumulated into legacy data and considered as cumulative catch-up.</p>\r\n<p style=\"padding-left: 60px;\">Revenue lines which were cancelled or are cancellation lines themselves are ignored completely.</p>\r\n<p style=\"padding-left: 60px;\">The revenue recognition type used in the migrated document is saved into database table FARRIC_MIGRATED.<br /> <br /> <em>The technical fields for these are: VBKD-RRREL = space, VBRP-RRREL = space, VBREVE-REVFIX = M</em></p>\r\n<p style=\"padding-left: 60px;\"><em><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></em></p>\r\n<p style=\"padding-left: 30px;\"><strong><a target=\"_blank\" name=\"FAQ8c\"></a>&#65279;8.c Can I migrate completed SD Revenue Recognition processes?</strong></p>\r\n<p style=\"padding-left: 60px;\">Yes, in the expert version of the operational load report the screen parameter \"Incl Rev status cmpl items\" controls whether completed SD Revenue Recognition processes are migrated or not.<br /> <br /> <em>The technical field for this check is: VBUP-RRSTA</em></p>\r\n<p style=\"padding-left: 60px;\"><em><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></em></p>\r\n<p style=\"padding-left: 30px;\"><strong><a target=\"_blank\" name=\"FAQ8d\"></a>&#65279;8.d Can I migrate cancelled revenue lines?</strong></p>\r\n<p style=\"padding-left: 60px;\">No, revenue lines that are cancelled or are cancellations themselves are not migrated. These lines are also not part of the legacy data regardless of their posting date.<br /> <br /> <em>The technical field for this check is: VBREVE-REVFIX = \"A\" or \"B\".</em></p>\r\n<p style=\"padding-left: 60px;\"><em><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></em></p>\r\n<p style=\"padding-left: 30px;\"><strong><a target=\"_blank\" name=\"FAQ8e\"></a>&#65279;8.e Can I migrate manually completed documents?</strong></p>\r\n<p style=\"padding-left: 60px;\">No, sales processes that have been manually completed are not migrated.<br /> <br /> <em>The technical field for this check is: VBUK-MANEK.</em></p>\r\n<p style=\"padding-left: 60px;\"><em><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></em></p>\r\n<p style=\"padding-left: 30px;\"><strong><a target=\"_blank\" name=\"FAQ8f\"></a>&#65279;8.f Can I migrate event-based processes?</strong></p>\r\n<p style=\"padding-left: 60px;\"><strong>Yes:</strong></p>\r\n<ul>\r\n<ul>\r\n<li>Event type \"_ - Standard POD\" can be migrated into a proof of delivery event-based process (Note&#160;2691932)</li>\r\n<li>Event type \"N - Not POD-relevant\" can be migrated into a goods issue event-based process (Note&#160;2691932)</li>\r\n<li>Event type \"A - Incoming invoice\" can be migrated into an incoming vendor invoice event-based process (Note&#160;2696483)</li>\r\n<li>Event type \"B - Acceptance Date\" can be migrated into an acceptance date event-based process (Note&#160;&#160;2768928,&#160;2737585)</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\"><strong><span style=\"font-size: 14px;\">No:</span></strong></p>\r\n<ul>\r\n<ul>\r\n<li>Event type \"X\", \"Y\", \"Z\" - Customer-Specific events <strong>cannot be migrated in the Standard</strong></li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\"><em>The technical field for this check is: VBKD-REVEVTYP &amp; VBKD-PODKZ</em></p>\r\n<p style=\"padding-left: 60px;\"><em><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></em></p>\r\n<p style=\"padding-left: 30px;\"><strong><a target=\"_blank\" name=\"FAQ8g\"></a>&#65279;8.g Can I migrate the revenue schedule which was forecasted by SD Revenue Recognition?</strong></p>\r\n<p style=\"padding-left: 60px;\">Yes, in the expert version of the operational load report the screen parameter \"Transfer Revenue Schedule\" enables the transfer of existing forecast VBREVE lines to Revenue Accounting. This is only supported for time-based processes.</p>\r\n<p style=\"padding-left: 60px;\"><em>Technical field for this check is: VBKD-RRREL = A or D</em></p>\r\n<p style=\"padding-left: 60px;\"><em><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></em></p>\r\n<p style=\"padding-left: 30px;\"><strong><a target=\"_blank\" name=\"FAQ8h\"></a>&#65279;8.h Can I migrate FASB 52 relevant documents?</strong></p>\r\n<p style=\"padding-left: 60px;\">Yes, FASB 52 relevant documents can be migrated once the redesigned migration logic is installed in your system. (Note&#160;2725702)</p>\r\n<p style=\"padding-left: 60px;\"><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></p>\r\n<p style=\"padding-left: 30px;\"><strong><a target=\"_blank\" name=\"FAQ8i\"></a>&#65279;8.i What happens if a billing document which was relevant for SD Revenue Recognition gets cancelled after its migration?</strong></p>\r\n<p style=\"padding-left: 60px;\">Cancellations created after the migration post between <span style=\"text-decoration: underline;\">different G/L accounts</span>, than the original billing documents.<br /><span style=\"text-decoration-line: underline;\"><br /></span>Details:&#160;If we take an invoice item as an example where no revenue has been realized in transaction VF44 yet:</p>\r\n<ul>\r\n<ul>\r\n<li>The invoice posts a debit entry (-) to the <em>customer receivables</em> account and a credit entry (+) to the&#160;<em>deferred revenue </em>account<em>&#160;</em>(in the source system)</li>\r\n<li><span style=\"text-decoration-line: underline;\">Before</span> the migration, an invoice cancellation posts a debit entry (-) to the <span style=\"text-decoration: underline;\"><em>deferred revenue</em></span>&#160;account&#160;and a credit entry (+) to the&#160;<em>customer receivables</em> account&#160;(in the source system)</li>\r\n<li><span style=\"text-decoration: underline;\">Af<span style=\"text-decoration: underline;\">ter</span></span> the migration, an invoice cancellation posts a debit entry (-) to the&#160;<span style=\"text-decoration: underline;\"><em>revenue</em></span>&#160;account&#160;and a credit entry (+) to the&#160;<em>customer receivables </em>account&#160;(in the source system)</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">This is all intended and works as designed, because the invoice cancellation item created after the migration is transferred to the Revenue Accounting engine as a productive SDII RAI. As such it gets assigned to a productive reconciliation key and triggers an invoice correction posting which gets transferred to G/L upon processing. (Unlike the initial load SDIIs which are assigned to the migration reconciliation key.) The invoice correction posts a credit entry (+) to the&#160;<em><span style=\"text-decoration: underline;\">revenue</span> </em>account&#160;and a debit entry (-) to the<em>&#160;receivables adjustment </em>account<em>&#160;(</em>equivalent of the<em> deferred</em> <em>revenue&#160;</em>account in SAP RAR).<em><span style=\"text-decoration: underline;\"><br /></span></em></p>\r\n<p style=\"padding-left: 60px;\">If the invoice cancellation posted&#160;<span style=\"text-decoration: underline;\">after</span> the migration triggered a true reversal of the original invoice item, the Revenue Accounting engine's invoice correction posting would result in a duplicate accrual posting which cannot be automatically cleared in SAP RAR by the end of the process. This is also the reason why the \"new\" cancellation procedure cannot be used in revenue accounting relevant billing documents.</p>\r\n<p style=\"padding-left: 60px;\"><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></p>\r\n<p><strong><a target=\"_blank\" name=\"FAQ9\"></a>&#65279;9. What is the revenue correction report?</strong></p>\r\n<p style=\"padding-left: 30px;\">The revenue correction report (transaction&#160;FARRIC_RR_CORR) is a correction tool meant for migrated SD Revenue Recognition processes which contain billing documents or revenue lines posted after the company code&#180;s or migration package&#180;s transfer date. Documents posted after the transfer date will be processed in the revenue accounting engine once the given company code or migration package is set to productive status. These will then result in postings to the RAR sub-ledger and ultimately to the general ledger, where the Standard RAR posting logic applies.</p>\r\n<p style=\"padding-left: 30px;\">If a billing document is posted after the transfer date the Standard RAR posting logic expects its credit side of the posting on the revenue account. However, if the document was still processed with the legacy SD Revenue Recognition logic its credit side was posted to an accruals account instead. The correction report releases the accrued invoiced amount to the revenue account to avoid duplicate accruals postings in the Engine.</p>\r\n<p style=\"padding-left: 30px;\">If a revenue line is posted after the transfer date it is not part of the legacy data generated during the migration and hence it is not considered when the cumulative catch-up is calculated in the Standard RAR posting logic. Such revenue lines must therefore be cancelled in the source system to avoid duplicate revenue postings in the Engine.</p>\r\n<p style=\"padding-left: 30px;\"><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></p>\r\n<p style=\"padding-left: 30px;\"><strong><a target=\"_blank\" name=\"FAQ9a\"></a>&#65279;9.a How do I know if I should use the revenue correction report?</strong></p>\r\n<p style=\"padding-left: 30px;\">If your legacy processes were not relevant for SD Revenue Recognition you will never have to run this report.</p>\r\n<p style=\"padding-left: 30px;\">If your legacy processes were relevant for SD Revenue Recognition and there were billing documents or revenue lines posted after the transfer date, there is a message in the application log of the migration run stating this and the item impacted must be processed with the correction report before the migration is completed. Transaction&#160;FARRIC_RR_CORR /&#160;FARRIC_RR_CORR_EXP has to be executed to perform the correction posting(s).</p>\r\n<p style=\"padding-left: 30px;\">If you have missed the application log you can also check database table FARRIC_MIGRATED for entries where the INV_CORR flag is not completed (\"C\") or irrelevant (\"SPACE\").</p>\r\n<p style=\"padding-left: 30px;\"><span style=\"text-decoration: underline;\">If there is any correction posting required make sure you have installed the redesigned correction tool in your system:</span></p>\r\n<ul>\r\n<li>SAP Notes 2715378 &amp;&#160;2702035</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\"><em>Technical fields for this check: VBREVR database entries where BUDAT &gt; transfer date, VBREVE database entries where BUDAT &gt; transfer date and REVFIX is initial</em></p>\r\n<p style=\"padding-left: 30px;\"><em><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></em></p>\r\n<p><strong><a target=\"_blank\" name=\"FAQ10\"></a>&#65279;10. I have received an error during operational load, can I undo and restart it?</strong></p>\r\n<p style=\"padding-left: 30px;\">Yes, as long as the corresponding company code or migration package is still in \"migration\" status, the operational load can be repeated even for individual documents. This makes sense if incorrect or inconsistent RAIs/legacy data were/was generated&#160;during the&#160;previous operational load.<strong><br /></strong></p>\r\n<ol>\r\n<li>Clean up the generated inconsistent / incorrect RAIs using transaction FARRIC_IL_CLEANUP<br />Deletes the relevant RAIs and their additional data from the RAR inbound processing (IP / ARL).<br /><br /></li>\r\n<li>Execute transaction&#160;FARRIC_OL or&#160;FARRIC_OL_EXPERT with processing mode \"RESET\"<br />Rolls back the changes performed during the original load run.<br /><br /></li>\r\n<li>Execute transaction FARRIC_OL or FARRIC_OL_EXPERT with processing mode \"LOAD\"<br />Performs the operational load of the selected documents again.</li>\r\n</ol>\r\n<p style=\"padding-left: 30px;\"><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></p>\r\n<p><strong><a target=\"_blank\" name=\"FAQ11\"></a>&#65279;11. Are cancelled billing documents migrated?</strong></p>\r\n<p style=\"padding-left: 30px;\">By default, the cancelled SD billing documents are <strong>not</strong> migrated if both the original document and its cancellation were created before the transfer date. This is because the two documents offset each other completely in Financials, so in sum they do not influence the legacy data.</p>\r\n<p style=\"padding-left: 30px;\">In productive use, there is no information available at the time of billing about a possible future cancellation, so the SDII RAIs are always generated automatically upon the release of billing document to Financials. As the document&#180;s SDII RAIs are always transferred to the Revenue Accounting Engine in productive mode, cancellation RAIs must also be processed and sent otherwise the two systems were not consistent anymore.</p>\r\n<p style=\"padding-left: 30px;\">If the cancellation document&#180;s billing date falls after the transfer date of the migration package, then both the cancelled document&#180;s and the cancellation document&#180;s RAIs are transferred to the Revenue Accounting Engine during migration.</p>\r\n<p style=\"padding-left: 30px;\">Another exception are the billing-related &amp; time-based SD Revenue Recognition processes (RRREL = D)&#160;. These billing documents are only excluded from the operational load if both the cancelled document and its cancellation are fully realized in transaction VF44. If either of these processes still has open revenue lines then the two documents do not cancel out each other completely and their exact legacy amounts must be accumulated and transferred so that the open revenue is scheduled accordingly in SAP RAR.</p>\r\n<p style=\"padding-left: 30px;\"><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></p>\r\n<p><strong><a target=\"_blank\" name=\"FAQ12\"></a>&#65279;12. What happens to incomplete sales documents during migration?</strong></p>\r\n<p style=\"padding-left: 30px;\">If the migrated sales document has an incompleteness log entry for delivery data, invoice data or pricing then the entire sales document &amp; its document flow is skipped during migration. The document&#180;s missing data must be completed in order to migrate the process.</p>\r\n<p style=\"padding-left: 30px;\">Incomplete sales documents with missing delivery/invoice/pricing data never create SDOI RAIs, neither during migration nor during productive use. As normally sales documents are the leading objects for POB creation in case their RAIs are not generated, the RAIs of follow-on documents would not have their parent performance obligation. Due to this, migrating the entire document chain makes no sense as long as the leading document cannot be migrated and the POB is not available.</p>\r\n<p style=\"padding-left: 30px;\"><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></p>\r\n<p><strong><a target=\"_blank\" name=\"FAQ13\"></a>&#65279;13. What is a migration package and how can I utilize it?</strong></p>\r\n<p style=\"padding-left: 30px;\">Migration packages are the units for performing granular migration in a given company code in several steps. Individual migration packages may be configured differently than their company code level settings - migration packages have their own status and transfer date. A typical use case is to assign new migration package in \"Migration\" status to an already \"Productive\" company code. This lets you create and process new documents in the company code immediately in SAP RAR, whereas migrating legacy documents using the migration package is still possible.</p>\r\n<p style=\"padding-left: 30px;\">More information: <a target=\"_blank\" href=\"https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3.3/en-US/28394e5754fd0f4be10000000a4450e5.html\">https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3.3/en-US/28394e5754fd0f4be10000000a4450e5.html</a></p>\r\n<p style=\"padding-left: 30px;\"><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></p>\r\n<p><strong><a target=\"_blank\" name=\"FAQ14\"></a>&#65279;14. Why does the operational load generate RAIs without initial load indicator?</strong></p>\r\n<p style=\"padding-left: 30px;\">The operational load migrates an entire sales process to revenue accounting including the documents created both before and after the transfer date defined for the company code or migration package.</p>\r\n<ul>\r\n<li>Sales documents with a \"Document date (VBAK-AUDAT)\" before or on the transfer date are considered \"legacy\" orders and their SDOI RAIs are created with the initial load flag</li>\r\n<li>Billing documents with a \"Billing date (VBRK-FKDAT)\" before or on the tranfer date are considered \"legacy\" invoices and their SDII RAIs are creatd with the initial load flag</li>\r\n<li>In the fulfillment items the SDFI RAI&#180;s event date field decides whether the initial load flag is set or not; the event date is determined differently for each fulfillment event type:</li>\r\n<ul>\r\n<li>Event type \"GI\" - goods issue date (MKPF-BUDAT, LIKP-WADAT_IST)</li>\r\n<li>Event type \"AD\" - acceptance date (VEDA-VABNDAT)</li>\r\n<li>Event type \"RO\" - release order document date (VBAK-AUDAT)</li>\r\n<li>Event type \"PI\" - incoming vendor invoice posting date (BKPF-BUDAT)</li>\r\n<li>Event type \"IB\" - billing date (VBRK-FKDAT)</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">If a RAI is marked with the initial load flag it can only be processed while the given company code is still in migration status, whereas RAIs without the initial load flag can only be processed when the company code has already been switched to productive status.</p>\r\n<p style=\"padding-left: 30px;\"><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></p>\r\n<p><strong><a target=\"_blank\" name=\"FAQ15\"></a>&#65279;15. Is it possible to migrate costs?</strong></p>\r\n<p style=\"padding-left: 30px;\">Yes, as of SAP Note 2754952 (REVRECSD SP20) it is also possible to send legacy conditions for costs realized before the migration.</p>\r\n<p style=\"padding-left: 30px;\"><em><a target=\"_self\" href=\"#TOC\">[Back to the table of contents]</a></em></p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I300540)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I300540)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002569950/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002569950/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002569950/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002569950/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002569950/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002569950/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002569950/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002569950/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002569950/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3271578", "RefComponent": "FI-RA-MIG", "RefTitle": "Missing Legacy Data in RAR When Checked in FARR_RAI_MON transaction", "RefUrl": "/notes/3271578 "}, {"RefNumber": "2582784", "RefComponent": "FI-RA", "RefTitle": "Revenue Accounting and Reporting with SAP RAR 1.3 and S/4HANA 1809 & 1909 OP - FAQ's and Guidance", "RefUrl": "/notes/2582784 "}, {"RefNumber": "2341717", "RefComponent": "SD-BIL-RR", "RefTitle": "FAQ: Future of SD Revenue Recognition after IFRS15 is released", "RefUrl": "/notes/2341717 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "REVRECSD", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}