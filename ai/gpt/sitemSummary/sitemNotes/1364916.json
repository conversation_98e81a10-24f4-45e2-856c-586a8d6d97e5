{"Request": {"Number": "1364916", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 628, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000008045272017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001364916?language=E&token=FC616B032D1EC23A1B601D3D4EEE96E6"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001364916", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001364916/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1364916"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.03.2010"}, "SAPComponentKey": {"_label": "Component", "value": "BW-WHM-DST-TRF"}, "SAPComponentKeyText": {"_label": "Component", "value": "Transformation"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Warehouse Management", "value": "BW-WHM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Staging", "value": "BW-WHM-DST", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DST*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Transformation", "value": "BW-WHM-DST-TRF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DST-TRF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1364916 - Migration generates formula with incorrect 'FORMTP'"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>When you transport a transformation with a formula, the system issues an:</p> <UL><LI>ABAP runtime error 'ASSERTION_FAILED'</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;or</p> <UL><LI>error message R7 105</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;in the transport log of 'After_Import' processing in the following area:<br />after-import method RS_TRFN_AFTER_IMPORT for object type(s) TRFN (activation mode)</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Short dump, dump, R7105, R7 105, 105(R7)</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error.<br />Prerequisites: The transformation was generated via migration from transfer or update rules.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Note 1369395 provides a repair tool that can be used to automatically correct this metadata inconsistency.<br /></p> <UL><LI>SAP NetWeaver BI 7.00</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 22 for SAP NetWeaver BI 7. 00 (SAPKW70022) into your BI system. The Support Package is available when <B>Note 1325072 </B>\"SAPBINews NW 7.00 BI Support Package 22\", which describes this Support Package in more detail, is released for customers.</p> <UL><LI>SAP NetWeaver BI 7.01 (SAP NW BI 7.0 Enhancement Package 1)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 05 for SAP NetWeaver BI 7. 01 (SAPKW70105) into your BI system.&#x00A0;&#x00A0;The Support Package is available when <B>Note 1324445</B>\"SAPBINews NW 7.01 BI ABAP Support Package 05\", which describes this Support Package in more detail, has been released for customers.</p> <UL><LI>SAP NetWeaver BI 7.02 (SAP NW BI 7.0 Enhancement Package 2)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 01 for SAP NetWeaver BI 7. 02 (SAPKW70201) into your system. The Support Package is available when <B>Note</B><B><B>1332017</B></B> \"SAPBINews NW 7.02 BI ABAP Support Package 01\", which describes this Support Package in more detail, has been released for customers.</p> <UL><LI>SAP NetWeaver BI 7.10</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 08 for SAP NetWeaver BI 7. 10 (SAPKW71008) into your BI system. The Support Package is available when <B>Note 1260071 </B>\"SAPBINews NW 7.10 BI Support Package 8\", which describes this Support Package in more detail, has been released for customers.<br /></p> <UL><LI>SAP NetWeaver BI 7.11</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 03 for SAP NetWeaver BI 7. 11 (SAPKW71103) into your BI system. The Support Package is available when <B>Note 1263691 </B>\"SAPBINews NW 7.11 BI Support Package 03\", which describes this Support Package in more detail, has been released for customers.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br />In urgent cases, you can implement the correction instructions as an advance correction.<br /><br /><B>You must first read Note 875986, which provides information about transaction SNOTE.</B><br /><br />To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note contains the words \"Preliminary version\".<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D002702"}, {"Key": "Processor                                                                                           ", "Value": "D002702"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001364916/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001364916/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001364916/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001364916/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001364916/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001364916/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001364916/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001364916/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001364916/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1369395", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Transformations generated from migration: Repair tools", "RefUrl": "/notes/1369395"}, {"RefNumber": "1052648", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Migration of transfer rules and update rules for BW7.x", "RefUrl": "/notes/1052648"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1369395", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Transformations generated from migration: Repair tools", "RefUrl": "/notes/1369395 "}, {"RefNumber": "1052648", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Migration of transfer rules and update rules for BW7.x", "RefUrl": "/notes/1052648 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "710", "To": "720", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "701", "To": "701", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "711", "To": "711", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW 700", "SupportPackage": "SAPKW70022", "URL": "/supportpackage/SAPKW70022"}, {"SoftwareComponentVersion": "SAP_BW_VIRTUAL_COMP 701", "SupportPackage": "SAPK-70109INVCBWTECH", "URL": "/supportpackage/SAPK-70109INVCBWTECH"}, {"SoftwareComponentVersion": "SAP_BW 701", "SupportPackage": "SAPKW70105", "URL": "/supportpackage/SAPKW70105"}, {"SoftwareComponentVersion": "SAP_BW 702", "SupportPackage": "SAPKW70202", "URL": "/supportpackage/SAPKW70202"}, {"SoftwareComponentVersion": "SAP_BW 710", "SupportPackage": "SAPKW71009", "URL": "/supportpackage/SAPKW71009"}, {"SoftwareComponentVersion": "SAP_BW 711", "SupportPackage": "SAPKW71104", "URL": "/supportpackage/SAPKW71104"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BW", "NumberOfCorrin": 2, "URL": "/corrins/0001364916/30"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 4, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "1124225 ", "URL": "/notes/1124225 ", "Title": "Different problems with single rule test", "Component": "BW-WHM-DST-TRF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "1140799 ", "URL": "/notes/1140799 ", "Title": "RSAR 051 when transporting transformations", "Component": "BW-WHM-DST-TRF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "701", "Number": "1244935 ", "URL": "/notes/1244935 ", "Title": "Endless loop in CL_FOBU_FORMULA->ALL_OPERANDS_GET_INTERNAL", "Component": "BW-WHM-DST-UPD"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "701", "Number": "1271513 ", "URL": "/notes/1271513 ", "Title": "Syntax error after migration of transfer rules with formulas", "Component": "BW-WHM-DST-TRF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "710", "ValidTo": "710", "Number": "1124225 ", "URL": "/notes/1124225 ", "Title": "Different problems with single rule test", "Component": "BW-WHM-DST-TRF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "710", "ValidTo": "710", "Number": "1271513 ", "URL": "/notes/1271513 ", "Title": "Syntax error after migration of transfer rules with formulas", "Component": "BW-WHM-DST-TRF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "711", "ValidTo": "711", "Number": "1271513 ", "URL": "/notes/1271513 ", "Title": "Syntax error after migration of transfer rules with formulas", "Component": "BW-WHM-DST-TRF"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}