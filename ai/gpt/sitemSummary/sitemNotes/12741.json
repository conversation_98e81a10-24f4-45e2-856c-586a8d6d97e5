{"Request": {"Number": "12741", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 224, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014331872017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000012741?language=E&token=7895CC99CE9E37C0C4929981D10A3D4E"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000012741", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000012741/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "12741"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 163}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.03.2023"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA-DBA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Database Administration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Administration", "value": "BC-DB-ORA-DBA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA-DBA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "12741 - Current versions of BR*Tools"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note concerns current and corrected versions of BR*Tools.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>DBA tools for Oracle</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Error correction or use of a higher release is required for functional reasons.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>-----------------------------------------------------------------------<br />Contents<br />-----------------------------------------------------------------------<br /><br /> [1] Which programs are contained in the BR*Tools program package?<br /> [2] Where can I find the BR*Tools package?<br /> [3] Copying the BR*Tools program package<br /> [4] Unpacking the BR*Tools program package<br /> [5] After copying and unpacking (for UNIX only):<br /> [6] Where can I find information about the programs of the BR*Tools package?<br /> [7] Remarks<br /><br />The BR*Tools correction procedure corresponds to the SAP standard.<br /><br />-----------------------------------------------------------------------<br />[1] Which programs are contained in the BR*Tools program package?<br />-----------------------------------------------------------------------<br /><br />The BR*Tools program package contains the following programs:<br /><br /> BRARCHIVE<br /> BRBACKUP<br /> BRCONNECT<br /> BRRECOVER<br /> BRRESTORE<br /> BRSPACE<br /> BRTOOLS<br /><br />-----------------------------------------------------------------------<br />[2] Where can I find the BR*Tools package?<br />-----------------------------------------------------------------------<br /><br />The BR*Tools package is available on SAP Service Marketplace (http://service.sap.com).<br />As of January 2010, BR*Tools 7.20 is available for Oracle 10g and 11g and is released for productive use.<br />As of June 2013, BR*Tools 7.40 is available for Oracle 11g and released for productive use. As of June 2015, Oracle 12c is supported by BR*Tools 7.40 (see SAP Note 2087004). BR*Tools 7.40 now supports higher Oracle versions, too.<br /><br />Caution:<br />--------<br />There is no particular BR*Tools package for Unicode. The non-Unicode and Unicode versions of BR*Tools are identical.<br /><br />--------------------------------------------------------<br />Main path for downloading from SAP Marketplace<br />--------------------------------------------------------<br />https://support.sap.com/software.html<br /> -&gt; Support Packages and Patches<br /> --&gt; A - Z Index / Alphabetical List of Products<br /> --&gt; K (Kernel)<br /><br />-----------------------------------------------------------------------<br />[2.1] Version 7.20 and 7.40<br />-----------------------------------------------------------------------<br /><br />The BR*Tools 7.20 and 7.40 packages are available on SAP Service Marketplace as an SAPCAR archive.<br />Identical versions of BR*Tools 7.20 are delivered for SAP Kernel Releases 7.20, 7.21, 7.22, and 7.38.<br />Note: BR*Tools 7.20 is no longer available/is no longer maintained for SAP Kernel Releases 7.20 und 7.38.&#x00A0;Please use the BR*Tools packages found under SAP Kernel Release&#x00A0;7.21 or 7.22.<br />Identical versions of BR*Tools 7.40 are delivered for SAP Kernel Releases 7.40, 7.41,&#x00A0;7.42, 7.45, 7.49, and 7.53. BR*Tools 7.40 is also supported and delivered for the new SAP Kernel Release 7.54.&#x00A0;<br />Note: BR*Tools 7.40 is no longer available/is no longer maintained for SAP Kernel Releases 7.40, 7.41, 7.42, 7.45, and 7.49.&#x00A0;Please use the BR*Tools packages found under SAP Kernel Release 7.53 or 7.54.</p>\r\n<p>The BR*Tools 7.20 package can be used with all SAP releases that are based on Oracle database 10g or 11g.<br />The BR*Tools 7.40 package can be used with all SAP releases based on an Oracle 11g or 12c database or higher.<br />BR*Tools 7.20 and 7.40 can also be used for SAP systems with non-ABAP stack without functional restrictions (for example, J2EE-only systems, portals, MDM, requisite, and so on). In non-ABAP stack systems, some control tables may not exist. Therefore, in addition, refer to SAP Notes 320457 and 892294.<br />The BR*Tools documentation is available as described under point [6]. In particular, the documentation contains recommendations on the type and frequency of regular upcoming database administration activities such as backups, updating statistics, and so on.<br /><br />Depending on the system configuration, the following table describes the path to the BR*Tools program package:<br /><br /> Operating system Oracle version SAP Kernel Access path<br /> --------------------------------------------------------------------<br /> LINUX 32-bit 10g 32-bit 32-bit 1<br /> WINDOWS 32-bit 10g 32-bit 32-bit 1<br /> UNIX 64-bit 10g 64-bit 64-bit 2<br /> WINDOWS 64-bit 10g 64-bit 64-bit 2<br /> --------------------------------------------------------------------<br />Caution: The 32-bit versions of BR*Tools are available only with SAP Kernel 7.20.<br /><br /><br />Access Path 1:<br /> Main path as starting point (see point [2]):<br /> --&gt; SAP KERNEL 32-BIT [UNICODE]<br /> --&gt; SAP KERNEL 7.21/7.22 32-BIT [UNICODE]<br /> --&gt; Linux on IA32 32bit | Windows Server on IA32 32bit<br /> --&gt; ORACLE<br /> --&gt; DBATL720O10_nn<br /><br />Access Path 2:<br /> Main path as starting point (see point [2]):<br /> --&gt; SAP KERNEL 64-BIT [UNICODE]<br /> --&gt; SAP KERNEL 7.21/7.21_EXT/7.22/7.22_EXT/7.49 64-BIT/7.53 64-BIT [UNICODE]/7.54 64-BIT [UNICODE]<br /> --&gt; AIX 64bit | HP-UX on IA64 64bit | Linux on x86_64 64bit ...<br /> --&gt; ORACLE<br /> --&gt; DBATL720O10_nn / DBATL740O11_nn<br />-----------------------------------------------------------------------<br />[3] Copying the BR*Tools program package<br />-----------------------------------------------------------------------<br /><br />You can copy the programs from SAP Service Marketplace using the marketplace download function. For more information, see SAP Note 19466.<br /><br />-----------------------------------------------------------------------<br />[4] Unpacking the BR*Tools program package<br />-----------------------------------------------------------------------<br /><br />The *.SAR archives are unpacked using SAPCAR, for example<br /> SAPCAR -xvf DBATL720O10.SAR<br />-----------------------------------------------------------------------<br />[5] After copying and unpacking (only for UNIX):<br />-----------------------------------------------------------------------<br /><br />Please check the owner, group, and access authorizations for the unpacked programs of the BR*Tools package in accordance with SAP Note 113747 or for Oracle installations using the \"oracle\" user (for example, RAC/ASM and Oracle 12c installations) as described in SAP Note 1598594.<br /><br />File libsbt.* is a dynamic backup library (DLL). By default, it is not used. For more information about this library, see Note 142635. The settings for this are:<br />-rwxr-xr-x &lt;sid&gt;adm sapsys ... libsbt.*<br /><br />-----------------------------------------------------------------------<br />[6] Where can I find information about the programs of the BR*Tools package?<br />-----------------------------------------------------------------------<br />For current information and documentation about BR*Tools, refer to the SAP Community Network (SCN) at http://scn.sap.com/community/oracle<br />(see \"Key Topics\" and \"Related Resources\" in the left area of the page).<br /><br />There (or directly on http://help.sap.com ) you can find the online documentation for the BR*Tools program package as of Release 6.40.<br />Online documentation for all programs of the BR*Tools package<br /><br />-----------------------------------------------------------------------<br />[7] Remarks<br />-----------------------------------------------------------------------<br />1.<br />To import the new versions of BR*Tools, an SAP upgrade is not necessary. The programs are independent of the rest of the SAP environment and can be downloaded from SAPNet without an upgrade. Only the database dependencies specified under 2.1 must be taken into account.<br /><br />2.<br />Basically, BR*Tools are downward-compatible and can be used for all SAP releases. However, OS versions and DB release dependencies must be taken into account here.<br /><br />3.<br />The 7.XX executables are linked to operating system versions which are intended for SAP 7.XX. As a result, they may not run on lower operating system versions (which are allowed for SAP 6.XX). See also SAP Note 407314 or <a target=\"_blank\" href=\"http://www.service.sap.com/pam\">www.service.sap.com/pam</a> for more information about this.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Database System", "Value": "ORACLE"}, {"Key": "Responsible                                                                                         ", "Value": "D000674"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D049481)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000012741/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000012741/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000012741/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000012741/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000012741/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000012741/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000012741/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000012741/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000012741/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "898787", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRBACKUP error message BR0382E", "RefUrl": "/notes/898787"}, {"RefNumber": "849483", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections for BR*Tools Version 7.00", "RefUrl": "/notes/849483"}, {"RefNumber": "778700", "RefComponent": "FS-BA", "RefTitle": "Bank Analyzer: Database parameter Tool-BW", "RefUrl": "/notes/778700"}, {"RefNumber": "680046", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools Version 6.40", "RefUrl": "/notes/680046"}, {"RefNumber": "663522", "RefComponent": "BC-DWB-UTL", "RefTitle": "Where-used list: Space required, DBIF_RSQL_SQL_ERROR", "RefUrl": "/notes/663522"}, {"RefNumber": "657995", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR975W Alert file <SID>ALRT.log not found", "RefUrl": "/notes/657995"}, {"RefNumber": "656365", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Error due to missing BRCONNECT functions", "RefUrl": "/notes/656365"}, {"RefNumber": "652888", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Collective note: Bad format in detail BRBACKUP log", "RefUrl": "/notes/652888"}, {"RefNumber": "651812", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "FAQ: BR*TOOLS and SAPDBA", "RefUrl": "/notes/651812"}, {"RefNumber": "650631", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP note: ORA-01003", "RefUrl": "/notes/650631"}, {"RefNumber": "633914", "RefComponent": "BC-DB-ORA", "RefTitle": "Error when executing DBMS_STATS", "RefUrl": "/notes/633914"}, {"RefNumber": "631117", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Upgrade to SAP R/3 Enterprise SR1 with Oracle 8.1.7", "RefUrl": "/notes/631117"}, {"RefNumber": "593582", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "sapd<PERSON>, BR tools 6.x: libclntsh is not found", "RefUrl": "/notes/593582"}, {"RefNumber": "592657", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-12701/ORA-12705/ORA-12709", "RefUrl": "/notes/592657"}, {"RefNumber": "580257", "RefComponent": "BC-DB-ORA", "RefTitle": "Sapdba 6.10 Patchlevel 54", "RefUrl": "/notes/580257"}, {"RefNumber": "579504", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA/BR*Tools: Support for earlier versions", "RefUrl": "/notes/579504"}, {"RefNumber": "562750", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA: Error selecting next data file, ORA-01002", "RefUrl": "/notes/562750"}, {"RefNumber": "553854", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Oracle: Problems with file size limit", "RefUrl": "/notes/553854"}, {"RefNumber": "546006", "RefComponent": "BC-DB-ORA", "RefTitle": "Problems with Oracle due to operating system errors", "RefUrl": "/notes/546006"}, {"RefNumber": "540021", "RefComponent": "BC-DB-ORA", "RefTitle": "Add. information on migrating/updating to Oracle 9.2.0: UNIX", "RefUrl": "/notes/540021"}, {"RefNumber": "514205", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools version 6.20", "RefUrl": "/notes/514205"}, {"RefNumber": "458932", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP note: ORA-00235", "RefUrl": "/notes/458932"}, {"RefNumber": "458872", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "ORA-00001 with sapdba or BR tools", "RefUrl": "/notes/458872"}, {"RefNumber": "437362", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite note ORA-12500", "RefUrl": "/notes/437362"}, {"RefNumber": "43484", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Collective note: General DBA", "RefUrl": "/notes/43484"}, {"RefNumber": "403706", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools Version 6.10", "RefUrl": "/notes/403706"}, {"RefNumber": "401721", "RefComponent": "BC-UPG-RDM", "RefTitle": "on upgrading to SAPWeb AS 6.10 ORACLE", "RefUrl": "/notes/401721"}, {"RefNumber": "364601", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Core dump in 46D SAPDBA patch 14", "RefUrl": "/notes/364601"}, {"RefNumber": "362068", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/362068"}, {"RefNumber": "339581", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "CHECK: BW tables without optimizer statistics", "RefUrl": "/notes/339581"}, {"RefNumber": "321018", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR programs Version 4.6D", "RefUrl": "/notes/321018"}, {"RefNumber": "30923", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/30923"}, {"RefNumber": "29864", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA - BRBACKUP release dependencies", "RefUrl": "/notes/29864"}, {"RefNumber": "23345", "RefComponent": "BC-DB-ORA", "RefTitle": "Consistency check of ORACLE database", "RefUrl": "/notes/23345"}, {"RefNumber": "216888", "RefComponent": "BC-DB-ORA", "RefTitle": "Additions: Oracle migr./upgrade to 8.1.6: UNIX", "RefUrl": "/notes/216888"}, {"RefNumber": "216321", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR programs with version 4.6C", "RefUrl": "/notes/216321"}, {"RefNumber": "216303", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/216303"}, {"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "203651", "RefComponent": "BC-DB-ORA-CCM", "RefTitle": "Backups via DB13 with EXTSHM=ON do not work", "RefUrl": "/notes/203651"}, {"RefNumber": "195161", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/195161"}, {"RefNumber": "19466", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Downloading SAP kernel patches", "RefUrl": "/notes/19466"}, {"RefNumber": "192386", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA: Missing CBO Statistics in check after Year 2000", "RefUrl": "/notes/192386"}, {"RefNumber": "188495", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "CBO:Alert - missg statistics for POOL/CLUSTER indxs", "RefUrl": "/notes/188495"}, {"RefNumber": "187645", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR programs version 4.6B", "RefUrl": "/notes/187645"}, {"RefNumber": "184133", "RefComponent": "BC-DB-ORA", "RefTitle": "Additions Oracle migr./upgrade to 8.0.6: UNIX", "RefUrl": "/notes/184133"}, {"RefNumber": "174309", "RefComponent": "BC-DB-ORA-CCM", "RefTitle": "DB12: Incorrect Alert: Redo logs not saved", "RefUrl": "/notes/174309"}, {"RefNumber": "169399", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR programs 4.6A version", "RefUrl": "/notes/169399"}, {"RefNumber": "1689026", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "Obsolete: Install NW 7.0x Systems with Oracle Client 11.2.0x", "RefUrl": "/notes/1689026"}, {"RefNumber": "145777", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/145777"}, {"RefNumber": "144892", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR programs version 4.5B", "RefUrl": "/notes/144892"}, {"RefNumber": "144326", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/144326"}, {"RefNumber": "144325", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/144325"}, {"RefNumber": "144323", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/144323"}, {"RefNumber": "143288", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA: Super collective note", "RefUrl": "/notes/143288"}, {"RefNumber": "134592", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Import of SAPDBA role (sapdba_role.sql)", "RefUrl": "/notes/134592"}, {"RefNumber": "125589", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/125589"}, {"RefNumber": "125588", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/125588"}, {"RefNumber": "125587", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/125587"}, {"RefNumber": "124608", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR programs version 4.5A", "RefUrl": "/notes/124608"}, {"RefNumber": "124607", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR programs version 4.0B", "RefUrl": "/notes/124607"}, {"RefNumber": "124606", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR programs version 3.1I", "RefUrl": "/notes/124606"}, {"RefNumber": "1133765", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1133765"}, {"RefNumber": "1133646", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1133646"}, {"RefNumber": "1011731", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Long runtimes for RMAN backups with Oracle 10g", "RefUrl": "/notes/1011731"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3034513", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools error: ORA-00932 and ORA-00904", "RefUrl": "/notes/3034513 "}, {"RefNumber": "2925891", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*tools error: libnnz10.so: open failed: No such file or directory - Netweaver", "RefUrl": "/notes/2925891 "}, {"RefNumber": "2921786", "RefComponent": "BC-DB-ORA-CCM", "RefTitle": "Checklist: Remote Oracle Monitoring in SOLMAN", "RefUrl": "/notes/2921786 "}, {"RefNumber": "2590826", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR0602E BR0602W No valid SAP license found - SAP eSourcing/SAP CLM", "RefUrl": "/notes/2590826 "}, {"RefNumber": "1578001", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR0970W IN_WRONG_TABLESPACE, BR0989W Unknown tablespace ,", "RefUrl": "/notes/1578001 "}, {"RefNumber": "2529984", "RefComponent": "BC-UPG-PRP", "RefTitle": "Non-solvable error in PREP_INIT/TOOLCHECKXML_UPG - Tool: brconnect", "RefUrl": "/notes/2529984 "}, {"RefNumber": "2847437", "RefComponent": "BC-DB-ORA", "RefTitle": "Older Versions:  SAP Software and Oracle Exadata", "RefUrl": "/notes/2847437 "}, {"RefNumber": "2290084", "RefComponent": "BC-DB-ORA", "RefTitle": "SAP Software and Oracle Database Appliance Version 12.1", "RefUrl": "/notes/2290084 "}, {"RefNumber": "1590515", "RefComponent": "BC-DB-ORA", "RefTitle": "SAP Software and Oracle Exadata", "RefUrl": "/notes/1590515 "}, {"RefNumber": "23345", "RefComponent": "BC-DB-ORA", "RefTitle": "Consistency check of ORACLE database", "RefUrl": "/notes/23345 "}, {"RefNumber": "1689026", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "Obsolete: Install NW 7.0x Systems with Oracle Client 11.2.0x", "RefUrl": "/notes/1689026 "}, {"RefNumber": "651812", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "FAQ: BR*TOOLS and SAPDBA", "RefUrl": "/notes/651812 "}, {"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "849483", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections for BR*Tools Version 7.00", "RefUrl": "/notes/849483 "}, {"RefNumber": "134592", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Import of SAPDBA role (sapdba_role.sql)", "RefUrl": "/notes/134592 "}, {"RefNumber": "546006", "RefComponent": "BC-DB-ORA", "RefTitle": "Problems with Oracle due to operating system errors", "RefUrl": "/notes/546006 "}, {"RefNumber": "680046", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools Version 6.40", "RefUrl": "/notes/680046 "}, {"RefNumber": "898787", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRBACKUP error message BR0382E", "RefUrl": "/notes/898787 "}, {"RefNumber": "321018", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR programs Version 4.6D", "RefUrl": "/notes/321018 "}, {"RefNumber": "633914", "RefComponent": "BC-DB-ORA", "RefTitle": "Error when executing DBMS_STATS", "RefUrl": "/notes/633914 "}, {"RefNumber": "553854", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Oracle: Problems with file size limit", "RefUrl": "/notes/553854 "}, {"RefNumber": "592657", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-12701/ORA-12705/ORA-12709", "RefUrl": "/notes/592657 "}, {"RefNumber": "1011731", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Long runtimes for RMAN backups with Oracle 10g", "RefUrl": "/notes/1011731 "}, {"RefNumber": "540021", "RefComponent": "BC-DB-ORA", "RefTitle": "Add. information on migrating/updating to Oracle 9.2.0: UNIX", "RefUrl": "/notes/540021 "}, {"RefNumber": "631117", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Upgrade to SAP R/3 Enterprise SR1 with Oracle 8.1.7", "RefUrl": "/notes/631117 "}, {"RefNumber": "778700", "RefComponent": "FS-BA", "RefTitle": "Bank Analyzer: Database parameter Tool-BW", "RefUrl": "/notes/778700 "}, {"RefNumber": "458932", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP note: ORA-00235", "RefUrl": "/notes/458932 "}, {"RefNumber": "401721", "RefComponent": "BC-UPG-RDM", "RefTitle": "on upgrading to SAPWeb AS 6.10 ORACLE", "RefUrl": "/notes/401721 "}, {"RefNumber": "514205", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools version 6.20", "RefUrl": "/notes/514205 "}, {"RefNumber": "403706", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools Version 6.10", "RefUrl": "/notes/403706 "}, {"RefNumber": "662644", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP Note: ORA-00942", "RefUrl": "/notes/662644 "}, {"RefNumber": "593582", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "sapd<PERSON>, BR tools 6.x: libclntsh is not found", "RefUrl": "/notes/593582 "}, {"RefNumber": "663522", "RefComponent": "BC-DWB-UTL", "RefTitle": "Where-used list: Space required, DBIF_RSQL_SQL_ERROR", "RefUrl": "/notes/663522 "}, {"RefNumber": "657995", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR975W Alert file <SID>ALRT.log not found", "RefUrl": "/notes/657995 "}, {"RefNumber": "656365", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Error due to missing BRCONNECT functions", "RefUrl": "/notes/656365 "}, {"RefNumber": "652888", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Collective note: Bad format in detail BRBACKUP log", "RefUrl": "/notes/652888 "}, {"RefNumber": "650631", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP note: ORA-01003", "RefUrl": "/notes/650631 "}, {"RefNumber": "437362", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite note ORA-12500", "RefUrl": "/notes/437362 "}, {"RefNumber": "580257", "RefComponent": "BC-DB-ORA", "RefTitle": "Sapdba 6.10 Patchlevel 54", "RefUrl": "/notes/580257 "}, {"RefNumber": "458872", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "ORA-00001 with sapdba or BR tools", "RefUrl": "/notes/458872 "}, {"RefNumber": "579504", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA/BR*Tools: Support for earlier versions", "RefUrl": "/notes/579504 "}, {"RefNumber": "562750", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA: Error selecting next data file, ORA-01002", "RefUrl": "/notes/562750 "}, {"RefNumber": "144892", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR programs version 4.5B", "RefUrl": "/notes/144892 "}, {"RefNumber": "203651", "RefComponent": "BC-DB-ORA-CCM", "RefTitle": "Backups via DB13 with EXTSHM=ON do not work", "RefUrl": "/notes/203651 "}, {"RefNumber": "339581", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "CHECK: BW tables without optimizer statistics", "RefUrl": "/notes/339581 "}, {"RefNumber": "174309", "RefComponent": "BC-DB-ORA-CCM", "RefTitle": "DB12: Incorrect Alert: Redo logs not saved", "RefUrl": "/notes/174309 "}, {"RefNumber": "216888", "RefComponent": "BC-DB-ORA", "RefTitle": "Additions: Oracle migr./upgrade to 8.1.6: UNIX", "RefUrl": "/notes/216888 "}, {"RefNumber": "184133", "RefComponent": "BC-DB-ORA", "RefTitle": "Additions Oracle migr./upgrade to 8.0.6: UNIX", "RefUrl": "/notes/184133 "}, {"RefNumber": "216321", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR programs with version 4.6C", "RefUrl": "/notes/216321 "}, {"RefNumber": "364601", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Core dump in 46D SAPDBA patch 14", "RefUrl": "/notes/364601 "}, {"RefNumber": "143288", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA: Super collective note", "RefUrl": "/notes/143288 "}, {"RefNumber": "187645", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR programs version 4.6B", "RefUrl": "/notes/187645 "}, {"RefNumber": "169399", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR programs 4.6A version", "RefUrl": "/notes/169399 "}, {"RefNumber": "124607", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR programs version 4.0B", "RefUrl": "/notes/124607 "}, {"RefNumber": "192386", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA: Missing CBO Statistics in check after Year 2000", "RefUrl": "/notes/192386 "}, {"RefNumber": "124606", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR programs version 3.1I", "RefUrl": "/notes/124606 "}, {"RefNumber": "124608", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR programs version 4.5A", "RefUrl": "/notes/124608 "}, {"RefNumber": "188495", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "CBO:Alert - missg statistics for POOL/CLUSTER indxs", "RefUrl": "/notes/188495 "}, {"RefNumber": "29864", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA - BRBACKUP release dependencies", "RefUrl": "/notes/29864 "}, {"RefNumber": "43484", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Collective note: General DBA", "RefUrl": "/notes/43484 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}