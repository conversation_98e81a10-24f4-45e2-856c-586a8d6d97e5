{"Request": {"Number": "1359226", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 221, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016823542017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001359226?language=E&token=7C2773DA015660ADC994BA1134669A39"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001359226", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001359226/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1359226"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "30.06.2009"}, "SAPComponentKey": {"_label": "Component", "value": "BC-SEC-AUT-PFC"}, "SAPComponentKeyText": {"_label": "Component", "value": "ABAP Authorization and Role Administration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Security - Read KBA 2985997 for subcomponents", "value": "BC-SEC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Authorization", "value": "BC-SEC-AUT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC-AUT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "ABAP Authorization and Role Administration", "value": "BC-SEC-AUT-PFC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC-AUT-PFC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1359226 - Tracing of authority checks"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You cannot execute code because of failed authorization checks, that is, the user does not have the necessary authorizations. You are looking for a method to use to find out which authorizations are missing and which works generally in all scenarios.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>PFCG, authority-check, ST01, developer trace</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>missing authorizations</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Sometimes you run into the problem that you have to find out which authorizations are checked within an application. You need a method that works in all cases and that reliably yields comprehensive results in an efficient way.<br />The reasons why you might need this include:</p> <OL>1. You have developed your own application and you want to create your own authorization roles or profiles for this application.</OL> <OL>2. You are using SAP roles for a specific application but the SAP roles are not sufficient or incomplete.</OL> <OL>3. You are trying to create your own roles using the profile generator (transaction PFCG), but the outcome does not work because the authorization default data (SU24 data) for the applications you need are incomplete.</OL> <p>Apart from other things you could or should do (that is, opening a problem ticket in the latter two cases), you need results as soon as possible. The tool that works in all cases is the developer trace (transaction ST01). This note explains how to use the developer trace to find out what authorizations you need.<br /><br />Prerequisites:<br />Create a user that will execute the applications you need to trace. Using a dedicated user for the trace enables you to filter the trace results for that user only, so you can be sure that you do not trace anything else accidentally. Make sure that this user is used solely for authorization tracing and by noone else or otherwise your tracing results might be inaccurate.<br /><br />Also assign the authorization profile SAP_ALL to the user you created in the previous step. With SAP_ALL assigned, the user has ALL authorizations that might be checked in the application. Of course, this is potentially dangerous, but when performing the authorization trace you are able to execute any code branch you need. Since the user should not be used in a productive environment, the risk is limited.<br /><br />You can then log on to the system with the user from the first step. Depending on the kind of GUI technology you are using, tracing works differently.<br /><br />Determining the Application Server to Use for the Trace:<br />Authorization tracing using the developer trace only works if the trace and your application run on the same application server. To ensure this, two cases have to be distinguished:<br /><br />Case 1: Your application runs in an SAP GUI (the classical GUI). In this case, open a second window for the developer trace by choosing System-&gt;\"Create Session\" from the menu options.<br /><br />Case 2: Your application runs in a Web browser window, for example, when using a Web Dynpro ABAP application. In this case, start your application as always. Obtain the host name of the application server by analyzing the URL in the Web browser. For example, the fully-qualified host name for the application server used for the following URL is \"ldcibce.wdf.sap.corp\".<br /><br />\"http://ldcibce.wdf.sap.corp:50000/sap/bc/webdynpro/sap/demo_roadmap?sap-language=EN\"<br /><br />Log on to this server in an SAP GUI using SAP Logon with server selection. You will need this SAP GUI window for the trace.<br /><br />Preparing the Trace:<br />From here on, things work basically the same in all cases. You first have to prepare the trace. Start transaction ST01 in the SAP GUI window (as described for either of the above cases). In the \"Trace Components\" subscreen, select the option \"Authorization Check\" and deselect all others. Then choose \"General filters\" and enter the user ID in the field \"Trace for user only\". Switch the trace on.<br /><br />Executing the Trace:<br />Now execute the application for which you want to perform the authorization trace, either in the second SAP GUI window or the Web browser window. Perform everything that you need in your application. When you have finished, switch the trace off.<br /><br />Analyzing the Trace:<br />To analyze the trace, choose \"Trace Analysis\" in transaction ST01, and in the subsequent screen, deselect all options in the \"Trace Records\"-subscreen except for \"Authorization check\". Also, fill in the appropriate fields in the \"General Restrictions\" subscreen, in particular, the \"User Name\" field. Then execute the trace.<br /><br />You will get a list of all the authorization checks that were performed during the time the trace was activated, including all field values that have been checked.<br /><br />For example, lines such as the following could appear:<br /><br />14:45:25,329|AUTH |&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;---|S_TCODE&#x00A0;&#x00A0;&#x00A0;&#x00A0;RC=0 |TCD=SU01;<br /><br />This tells you that the authorization object S_TCODE has been checked, the check was successful (RC=0), and the field TCD has been checked with the value 'SU01'.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-SEC-AUT (Authorization)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D032971)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D024271)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001359226/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001359226/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001359226/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001359226/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001359226/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001359226/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001359226/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001359226/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001359226/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1412800", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX and BW authorizations", "RefUrl": "/notes/1412800"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1969032", "RefComponent": "BW-MT", "RefTitle": "Missing authority for resources related to BW Modeling Tools", "RefUrl": "/notes/1969032 "}, {"RefNumber": "1412800", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX and BW authorizations", "RefUrl": "/notes/1412800 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}