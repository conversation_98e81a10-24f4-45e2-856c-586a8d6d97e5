{"Request": {"Number": "785156", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 584, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000004600312017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=FA71CD824208B3CF9A25B7BB905E0C26"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "785156"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.05.2014"}, "SAPComponentKey": {"_label": "Component", "value": "FS-BP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Partner"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Services", "value": "FS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Partner", "value": "FS-BP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS-BP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "785156 - BP_TR2: Update of the business partner conversion 2004"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Update of the business partner conversion 2004.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>FTBU, FBPAR<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Error corrections, update of the source code and the documentation.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Implement the corrections in the following sequence, or import the specified Support Package.<br /><br /><B>Preparations</B></p> <UL><LI>Create the program RFTBUH0_I_02</LI></UL> <UL><UL><LI>Start Transaction SE38.</LI></UL></UL> <UL><UL><LI>Enter the program name and select 'Create'.</LI></UL></UL> <UL><UL><LI>Enter the following report features and save the object.<br />Title:&#x00A0;&#x00A0;Data declaration DDIC conversion report<br />Type:&#x00A0;&#x00A0;&#x00A0;&#x00A0;Include program<br />Status: Live SAP standard program</LI></UL></UL> <UL><UL><LI>Enter the FTBU development class in the next dialog box and save the object.</LI></UL></UL> <UL><LI>Create the program RFTBUH0_I_03</LI></UL> <UL><UL><LI>Start Transaction SE38.</LI></UL></UL> <UL><UL><LI>Enter the program name and select 'Create'.</LI></UL></UL> <UL><UL><LI>Enter the following report features and save the object<br />Title:&#x00A0;&#x00A0;Parameter and selection screen processing DDIC conversion reports<br />Type:&#x00A0;&#x00A0;&#x00A0;&#x00A0;Include program<br />Status: Live SAP standard program</LI></UL></UL> <UL><UL><LI>Enter the FTBU development class in the next dialog box and save the object</LI></UL></UL> <UL><LI>Create the program RFTBUPXP12 (only EA-FINSERV, not BANK/CFM)</LI></UL> <UL><UL><LI>Start Transaction SE38.</LI></UL></UL> <UL><UL><LI>Enter the program name and select 'Create'.</LI></UL></UL> <UL><UL><LI>Enter the following report features and save the object.<br />Title:&#x00A0;&#x00A0;Conversion report for industries (BUPXPRA12)<br />Type:&#x00A0;&#x00A0;&#x00A0;&#x00A0;Executable program<br />Status: Live SAP standard program</LI></UL></UL> <UL><UL><LI>Enter the FTBU development class in the next dialog box and save the object</LI></UL></UL> <UL><LI>Create the program RFTBUPXP13 (only EA-FINSERV, not BANK/CFM)</LI></UL> <UL><UL><LI>Start Transaction SE38.</LI></UL></UL> <UL><UL><LI>Enter the program name and select 'Create'.</LI></UL></UL> <UL><UL><LI>Enter the following report features and save the object<br />Title:&#x00A0;&#x00A0;Conversion report for identification numbers (BUPXPRA13)<br />Type:&#x00A0;&#x00A0;&#x00A0;&#x00A0;Executable program<br />Status: Live SAP standard program</LI></UL></UL> <UL><UL><LI>Enter the FTBU development class in the next dialog box and save the object.</LI></UL></UL> <p></p> <UL><LI>Maintain the following selection texts</LI></UL> <UL><UL><LI>Start Transaction SE38.</LI></UL></UL> <UL><UL><LI>Enter the program name RFTBUPO7, select the text elements subobject and select the processing type \"Change\".</LI></UL></UL> <UL><UL><LI>Select the \"Selection texts\" title element and enter the following values.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ANZ&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Display<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Delete&#x00A0;&#x00A0;&#x00A0;&#x00A0; Delete<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DTB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Until<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DTV&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Date of creation<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PAR_PROJ&#x00A0;&#x00A0; Project<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PAR_TABN&#x00A0;&#x00A0; Subproject<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;USR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Created by<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ZTB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Until<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ZTV&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Time of creation</p> <UL><UL><LI>Activate the program. </LI></UL></UL> <UL><LI>Maintain the following text symbols</LI></UL> <UL><UL><LI>Start Transaction SE38.</LI></UL></UL> <UL><UL><LI>Enter the program name RFTBUH02_1, select the text elements subobject and then the processing type \"Change\".</LI></UL></UL> <UL><UL><LI>Select the \"Text symbols\" title element and enter the following values.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;004&#x00A0;&#x00A0;&#x00A0;&#x00A0;Original system&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Max length = 15<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;005&#x00A0;&#x00A0;&#x00A0;&#x00A0;Package&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Max length = 30<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;006&#x00A0;&#x00A0;&#x00A0;&#x00A0; Application components&#x00A0;&#x00A0;&#x00A0;&#x00A0;Max length = 24<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;007&#x00A0;&#x00A0;&#x00A0;&#x00A0;Not relevant&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Max length = 14</p> <UL><UL><LI>Activate the program. </LI></UL></UL> <UL><LI>Maintain the following text symbols</LI></UL> <UL><UL><LI>Start Transaction SE38.</LI></UL></UL> <UL><UL><LI>Enter the program name RFTBUP10_BP000, select the text elements subobject and then the processing type \"Change\".</LI></UL></UL> <UL><UL><LI>Select the \"Text symbols\" title element and enter the following values.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;F03&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Check&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Max length = 10</p> <UL><UL><LI>Activate the program. </LI></UL></UL> <UL><LI>Maintain table entries.</LI></UL> <UL><UL><LI>Start Transaction UCU4.</LI></UL></UL> <UL><UL><LI>Enter the project 0001 and press the \"Copy\" function key.</LI></UL></UL> <UL><UL><LI>Create the following entries</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RFTBUPXP12<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RFTBUPXP13</p> <UL><UL><LI>Save the table </LI></UL></UL> <UL><LI>Maintain table entries.</LI></UL> <UL><UL><LI>Start Transaction UCU5</LI></UL></UL> <UL><UL><LI>Enter the project 0001 and press the \"Copy\" function key.</LI></UL></UL> <UL><UL><LI>Create the following entries</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RFTBUPXP12&#x00A0;&#x00A0; 10&#x00A0;&#x00A0; 4&#x00A0;&#x00A0; RFTBUPXP12<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RFTBUPXP13&#x00A0;&#x00A0; 11&#x00A0;&#x00A0; 4&#x00A0;&#x00A0; RFTBUPXP13</p> <UL><UL><LI>Save the table </LI></UL></UL> <UL><LI>Copy the source code from the correction instructions, or implement the correction specified below using the Note Assistant.</LI></UL> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "C5029681"}, {"Key": "Processor                                                                                           ", "Value": "D047682"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "736512", "RefComponent": "FS-BP", "RefTitle": "BP_TR2: Update of the business partner conversion 2004", "RefUrl": "/notes/736512"}, {"RefNumber": "398888", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Composite note and FAQs about bus partner conversion", "RefUrl": "/notes/398888"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "398888", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Composite note and FAQs about bus partner conversion", "RefUrl": "/notes/398888 "}, {"RefNumber": "736512", "RefComponent": "FS-BP", "RefTitle": "BP_TR2: Update of the business partner conversion 2004", "RefUrl": "/notes/736512 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "110", "To": "110", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "200", "To": "200", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "BANK/CFM", "From": "463_20", "To": "463_20", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-FINSERV 110", "SupportPackage": "SAPKGPFA20", "URL": "/supportpackage/SAPKGPFA20"}, {"SoftwareComponentVersion": "EA-FINSERV 200", "SupportPackage": "SAPKGPFB09", "URL": "/supportpackage/SAPKGPFB09"}, {"SoftwareComponentVersion": "BANK/CFM 463_20", "SupportPackage": "SAPKIPBJ27", "URL": "/supportpackage/SAPKIPBJ27"}, {"SoftwareComponentVersion": "EA-FINSERV 500", "SupportPackage": "SAPKGPFC06", "URL": "/supportpackage/SAPKGPFC06"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "EA-FINSERV", "NumberOfCorrin": 3, "URL": "/corrins/**********/201"}, {"SoftwareComponent": "BANK/CFM", "NumberOfCorrin": 1, "URL": "/corrins/**********/59"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 4, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "BANK/CFM", "ValidFrom": "463_20", "ValidTo": "463_20", "Number": "516621 ", "URL": "/notes/516621 ", "Title": "SAP BP conversion phase II: Report RFTBUP10_BP000", "Component": "FS-BP"}, {"SoftwareComponent": "BANK/CFM", "ValidFrom": "463_20", "ValidTo": "463_20", "Number": "591007 ", "URL": "/notes/591007 ", "Title": "SAP BP Conversion:Composite SAP note various error correctns", "Component": "FS-BP"}, {"SoftwareComponent": "BANK/CFM", "ValidFrom": "463_20", "ValidTo": "463_20", "Number": "736512 ", "URL": "/notes/736512 ", "Title": "BP_TR2: Update of the business partner conversion 2004", "Component": "FS-BP"}, {"SoftwareComponent": "BANK/CFM", "ValidFrom": "463_20", "ValidTo": "463_20", "Number": "762061 ", "URL": "/notes/762061 ", "Title": "BP_TR2: Deactivate consistency check for table BP000", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "110", "ValidTo": "110", "Number": "516621 ", "URL": "/notes/516621 ", "Title": "SAP BP conversion phase II: Report RFTBUP10_BP000", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "110", "ValidTo": "110", "Number": "591007 ", "URL": "/notes/591007 ", "Title": "SAP BP Conversion:Composite SAP note various error correctns", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "110", "ValidTo": "110", "Number": "736512 ", "URL": "/notes/736512 ", "Title": "BP_TR2: Update of the business partner conversion 2004", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "110", "ValidTo": "110", "Number": "762061 ", "URL": "/notes/762061 ", "Title": "BP_TR2: Deactivate consistency check for table BP000", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "200", "ValidTo": "200", "Number": "736512 ", "URL": "/notes/736512 ", "Title": "BP_TR2: Update of the business partner conversion 2004", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "200", "ValidTo": "200", "Number": "762061 ", "URL": "/notes/762061 ", "Title": "BP_TR2: Deactivate consistency check for table BP000", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "500", "ValidTo": "500", "Number": "736512 ", "URL": "/notes/736512 ", "Title": "BP_TR2: Update of the business partner conversion 2004", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "500", "ValidTo": "500", "Number": "762061 ", "URL": "/notes/762061 ", "Title": "BP_TR2: Deactivate consistency check for table BP000", "Component": "FS-BP"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}