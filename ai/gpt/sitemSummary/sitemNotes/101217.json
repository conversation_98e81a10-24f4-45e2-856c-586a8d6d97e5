{"Request": {"Number": "101217", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 272, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014558262017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000101217?language=E&token=64265A0DEEA82D5D4FD6AEB12523233C"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000101217", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000101217/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "101217"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 28}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "30.07.2009"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-DB2"}, "SAPComponentKeyText": {"_label": "Component", "value": "DB2 for z/OS"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "DB2 for z/OS", "value": "BC-DB-DB2", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-DB2*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "101217 - DB2 z/OS: Overview of transports and corrections"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note contains an overview of all transports and corrections necessary for a certain R/3 release. It is updated on a regular basis.<br />The following areas are affected:</p> <UL><LI>Errors in the R/3 system (Data Dictionary, tp, R3trans, R3up, saposcol, CCMS, and so on)</LI></UL> <UL><LI>Missing functions (database monitor, and so on)</LI></UL> <UL><LI>Checks for Going-Live service<br /></LI></UL><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>DB2/390, DB2 390, DDIC, tp, R3trans, R3up, Upgrade, Conversion, Activation, SE14, Data Dictionary, ABAP, NAMETAB, Import, Transport, Migration, Export, Installation, UNIX, Windows NT, Primary Key Index, Table, Storage Group, SQL Error, Database Object Check, Problem, CCMS, Saposcol, Correction, Fix<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Correction transports that have not been implemented are responsible for the majority of problems occurring during R/3 installations on DB2 for OS/390.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>When you start to deal with each problem, you should check whether all corrections made available by SAP up to now have been transported into the R/3 system.<br />This check is also an important part of the Going Live check.<br /><br />Procedure:</p> <OL>1. You will find a list of all corrections and function enhancements - depending on your release - in the following notes:</OL> <UL><LI>Release 3.1I: Note 101303</LI></UL> <UL><LI>Release 4.0B: Note 103707</LI></UL> <UL><LI>Release 4.5A: Note 118901</LI></UL> <UL><LI>Release 4.5B: Note 151394</LI></UL> <UL><LI>Basis release 4.6A: Note 166083</LI></UL> <UL><LI>Basis release 4.6B : Note 191215</LI></UL> <UL><LI>Basis release 4.6C: Note 217093</LI></UL> <UL><LI>Basis release 4.6D: Note 324739</LI></UL> <UL><LI>Basis release 6.10: Note 417920</LI></UL> <UL><LI>Basis release 6.20: Note 493577</LI></UL> <UL><LI>Basis release 6.40: Note 661260</LI></UL> <UL><LI>Basis release 7.0 and higher: Note 823245</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Classification of the corrections is as follows: <UL><LI>Without all transports that are marked as \"mandatory\", the R/3 system is not fully functional.</LI></UL> <UL><LI>For \"optional\" transports, you may decide yourself whether you need the functions or not.</LI></UL> <OL>2. Verify the successful import of the transports using</OL> <UL><LI>transaction SE01 (menu option: Request -&gt; Find -&gt; Transport Request)</LI></UL> <UL><LI>of report \"RTCCTOOL\" (start it from the ABAP/4 editor, transaction SA38). Beforehand, install the newest version of this report as described in Note 187939. This report automatically compiles a list of missing transports, and controls their implementation. Important: At the moment, RTCCTOOL only works for the transports from areas \"Installation\", \"DDIC corrections\" and \"CCMS functionality\".</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;There is also information on imported transports for a heterogeneous or homogeneous system copy. <OL>3. Note 13719 describes how to download corrections and function enhancements and transport them into the R/3 system.</OL> <OL>4. Make sure that you only import transports whose release matches that of your system.<br /></OL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Operating system", "Value": "NT/INTEL"}, {"Key": "Operating system", "Value": "AIX"}, {"Key": "Operating system", "Value": "NT/INTEL4.0"}, {"Key": "Operating system", "Value": "OS/390"}, {"Key": "Operating system", "Value": "OS/390  1.3"}, {"Key": "Operating system", "Value": "AIX  4.1.4"}, {"Key": "Operating system", "Value": "AIX  4.1.5"}, {"Key": "Operating system", "Value": "AIX  4.2.X"}, {"Key": "Database System", "Value": "DB2/390"}, {"Key": "Database System", "Value": "DB2/390 5.1"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D022631)"}, {"Key": "Processor                                                                                           ", "Value": "C5020400"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000101217/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000101217/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000101217/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000101217/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000101217/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000101217/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000101217/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000101217/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000101217/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "97461", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/97461"}, {"RefNumber": "88271", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/88271"}, {"RefNumber": "823245", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: Transports & Support Packages (7.0 and higher)", "RefUrl": "/notes/823245"}, {"RefNumber": "765983", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/zOS: Reducing number of DSNACCOR REORG recommendations", "RefUrl": "/notes/765983"}, {"RefNumber": "68082", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Termination in report RSALSUP5", "RefUrl": "/notes/68082"}, {"RefNumber": "661260", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: Transports and Support Packages for 6.40", "RefUrl": "/notes/661260"}, {"RefNumber": "593469", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Usage of global TEMP tables and related problems", "RefUrl": "/notes/593469"}, {"RefNumber": "581269", "RefComponent": "BC-DB-DB2", "RefTitle": "Scheduling via DB13C in target DB2/390 system fails", "RefUrl": "/notes/581269"}, {"RefNumber": "551104", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: SAPLS_CCDSNU_DB2 terminates due to a timeout", "RefUrl": "/notes/551104"}, {"RefNumber": "549174", "RefComponent": "BC-DB-DB2", "RefTitle": "Transaction DB03 no longer supported for DB2/390", "RefUrl": "/notes/549174"}, {"RefNumber": "507824", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390 V7: Real Time Statistics and DSNACCOR", "RefUrl": "/notes/507824"}, {"RefNumber": "493577", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Transports and Support Packages for 6.20", "RefUrl": "/notes/493577"}, {"RefNumber": "448848", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Adjusting job skeleton in DBA planning calendar", "RefUrl": "/notes/448848"}, {"RefNumber": "440954", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: MCOD aspects in CCMS", "RefUrl": "/notes/440954"}, {"RefNumber": "423566", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/423566"}, {"RefNumber": "418037", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: LISTCAT job fails in Transaction DB02", "RefUrl": "/notes/418037"}, {"RefNumber": "417920", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/417920"}, {"RefNumber": "413885", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/413885"}, {"RefNumber": "411798", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/411798"}, {"RefNumber": "407240", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/407240"}, {"RefNumber": "406744", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/406744"}, {"RefNumber": "406585", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/406585"}, {"RefNumber": "401658", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/401658"}, {"RefNumber": "397191", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/397191"}, {"RefNumber": "396716", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/396716"}, {"RefNumber": "396044", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/396044"}, {"RefNumber": "390080", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/390080"}, {"RefNumber": "382412", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/382412"}, {"RefNumber": "378100", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/378100"}, {"RefNumber": "361332", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/361332"}, {"RefNumber": "357720", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/357720"}, {"RefNumber": "351390", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/351390"}, {"RefNumber": "337776", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/337776"}, {"RefNumber": "335921", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/335921"}, {"RefNumber": "334114", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/334114"}, {"RefNumber": "331608", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/331608"}, {"RefNumber": "331366", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/331366"}, {"RefNumber": "330900", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/330900"}, {"RefNumber": "324739", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/324739"}, {"RefNumber": "321751", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/321751"}, {"RefNumber": "317876", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/317876"}, {"RefNumber": "313419", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/313419"}, {"RefNumber": "313400", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/313400"}, {"RefNumber": "304923", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/304923"}, {"RefNumber": "303216", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/303216"}, {"RefNumber": "217538", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/217538"}, {"RefNumber": "217468", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/217468"}, {"RefNumber": "217093", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/217093"}, {"RefNumber": "213255", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/213255"}, {"RefNumber": "207985", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/207985"}, {"RefNumber": "200789", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/200789"}, {"RefNumber": "199260", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/199260"}, {"RefNumber": "198097", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/198097"}, {"RefNumber": "195308", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/195308"}, {"RefNumber": "194340", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/194340"}, {"RefNumber": "191323", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/191323"}, {"RefNumber": "191215", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/191215"}, {"RefNumber": "187939", "RefComponent": "SV-SMG-SDD", "RefTitle": "SAP Servicetools Update (RTCCTOOL)", "RefUrl": "/notes/187939"}, {"RefNumber": "187191", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/187191"}, {"RefNumber": "181286", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/181286"}, {"RefNumber": "177890", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/177890"}, {"RefNumber": "177312", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/177312"}, {"RefNumber": "176998", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/176998"}, {"RefNumber": "175833", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/175833"}, {"RefNumber": "173973", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/173973"}, {"RefNumber": "172175", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/172175"}, {"RefNumber": "171948", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/171948"}, {"RefNumber": "171661", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/171661"}, {"RefNumber": "170319", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/170319"}, {"RefNumber": "170271", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/170271"}, {"RefNumber": "166083", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/166083"}, {"RefNumber": "165075", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/165075"}, {"RefNumber": "164494", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/164494"}, {"RefNumber": "163279", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/163279"}, {"RefNumber": "161540", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/161540"}, {"RefNumber": "160404", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Newest version of the CCMS 4.5B", "RefUrl": "/notes/160404"}, {"RefNumber": "159102", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/159102"}, {"RefNumber": "159030", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/159030"}, {"RefNumber": "154394", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/154394"}, {"RefNumber": "152102", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/152102"}, {"RefNumber": "151919", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/151919"}, {"RefNumber": "145418", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/145418"}, {"RefNumber": "145316", "RefComponent": "SV-SMG-SER", "RefTitle": "DB2-z/OS: Preparations for SAP Support Services", "RefUrl": "/notes/145316"}, {"RefNumber": "145147", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/145147"}, {"RefNumber": "140868", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/140868"}, {"RefNumber": "140769", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/140769"}, {"RefNumber": "138547", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/138547"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "136297", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/136297"}, {"RefNumber": "132419", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/132419"}, {"RefNumber": "132031", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/132031"}, {"RefNumber": "131155", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/131155"}, {"RefNumber": "126684", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/126684"}, {"RefNumber": "125020", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Duprec for Insert in table SDBAD", "RefUrl": "/notes/125020"}, {"RefNumber": "118901", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/118901"}, {"RefNumber": "117867", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/117867"}, {"RefNumber": "112483", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/112483"}, {"RefNumber": "112478", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/112478"}, {"RefNumber": "111764", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/111764"}, {"RefNumber": "110069", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/110069"}, {"RefNumber": "104859", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/104859"}, {"RefNumber": "104348", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/104348"}, {"RefNumber": "104075", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/104075"}, {"RefNumber": "103707", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/103707"}, {"RefNumber": "102661", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/102661"}, {"RefNumber": "102643", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/102643"}, {"RefNumber": "101752", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Platform-specific errors in 4.0B", "RefUrl": "/notes/101752"}, {"RefNumber": "101303", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/101303"}, {"RefNumber": "101256", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/101256"}, {"RefNumber": "101239", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/101239"}, {"RefNumber": "100777", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/100777"}, {"RefNumber": "100682", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/100682"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "145316", "RefComponent": "SV-SMG-SER", "RefTitle": "DB2-z/OS: Preparations for SAP Support Services", "RefUrl": "/notes/145316 "}, {"RefNumber": "823245", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: Transports & Support Packages (7.0 and higher)", "RefUrl": "/notes/823245 "}, {"RefNumber": "581269", "RefComponent": "BC-DB-DB2", "RefTitle": "Scheduling via DB13C in target DB2/390 system fails", "RefUrl": "/notes/581269 "}, {"RefNumber": "551104", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: SAPLS_CCDSNU_DB2 terminates due to a timeout", "RefUrl": "/notes/551104 "}, {"RefNumber": "549174", "RefComponent": "BC-DB-DB2", "RefTitle": "Transaction DB03 no longer supported for DB2/390", "RefUrl": "/notes/549174 "}, {"RefNumber": "593469", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Usage of global TEMP tables and related problems", "RefUrl": "/notes/593469 "}, {"RefNumber": "507824", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390 V7: Real Time Statistics and DSNACCOR", "RefUrl": "/notes/507824 "}, {"RefNumber": "765983", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/zOS: Reducing number of DSNACCOR REORG recommendations", "RefUrl": "/notes/765983 "}, {"RefNumber": "448848", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Adjusting job skeleton in DBA planning calendar", "RefUrl": "/notes/448848 "}, {"RefNumber": "440954", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: MCOD aspects in CCMS", "RefUrl": "/notes/440954 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "661260", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: Transports and Support Packages for 6.40", "RefUrl": "/notes/661260 "}, {"RefNumber": "493577", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Transports and Support Packages for 6.20", "RefUrl": "/notes/493577 "}, {"RefNumber": "598469", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Transporte und Support Packages für 7.10", "RefUrl": "/notes/598469 "}, {"RefNumber": "187939", "RefComponent": "SV-SMG-SDD", "RefTitle": "SAP Servicetools Update (RTCCTOOL)", "RefUrl": "/notes/187939 "}, {"RefNumber": "68082", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Termination in report RSALSUP5", "RefUrl": "/notes/68082 "}, {"RefNumber": "418037", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: LISTCAT job fails in Transaction DB02", "RefUrl": "/notes/418037 "}, {"RefNumber": "160404", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Newest version of the CCMS 4.5B", "RefUrl": "/notes/160404 "}, {"RefNumber": "125020", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Duprec for Insert in table SDBAD", "RefUrl": "/notes/125020 "}, {"RefNumber": "101752", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Platform-specific errors in 4.0B", "RefUrl": "/notes/101752 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}