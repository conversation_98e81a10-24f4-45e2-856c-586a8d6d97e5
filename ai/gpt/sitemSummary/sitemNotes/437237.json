{"Request": {"Number": "437237", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 370, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000437237?language=E&token=562FC7EE01BDC514D83C613DDB50A234"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000437237", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000437237/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "437237"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "00.00.0000"}, "SAPComponentKey": {"_label": "Component", "value": "FIN-SEM-CPM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Corporate Performance Monitor"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financials", "value": "FIN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Strategic Enterprise Management", "value": "FIN-SEM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-SEM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Corporate Performance Monitor", "value": "FIN-SEM-CPM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-SEM-CPM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "437237 - SEM 3.1A frontend patch 1 (Oct.2001)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>SEM 3.0A: The system dumps if you display graphics</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Patch, Gui, Sapgui, frontend, front, graphic, installation, SAPClient, SEM, frontend patch, SEMOCX, Cockpit, Balanced Scorecard, Management Cockpit, BSC, MC</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>At least 46D GUI Compilation 3. Downward compatible to SEM 2.0B, SEM 3.0A, SEM 3.1A</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Starting with Release 4.6D Compilation 3 the SAP GUI installation provides a new tool for the application of patches.Instead of unpacking patches manually on the installation server as before, the import of the patch now occurs automatically.In the process the system does not only copy the patch files but also updates the installation database on the installation server.Clients on which you start SapSetup can thus see that the server was equipped with a new patch and update themselves correspondingly.As of the following patch levels you can install patches only by using this tool:</p> <UL><LI>sem31a:&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1</LI></UL> <UL><LI>setup46D:&#x00A0;&#x00A0; 5</LI></UL> <p>With these patches and their successors you CANNOT unpack patches manually any more. (Refer to Note 361222)</p> <OL>1. Prepare an installation server by starting setup.exe and selecting \"Administrative setup\".</OL> <OL>2. Download the archive from the SapservX and copy the files into the installation directory of the server, the procedure is:</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;a) download archive&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; sem31a_1.exe&#x00A0;&#x00A0;&#x00A0;&#x00A0;- SEM 3.1a SP1 patch ftp://sapservX/general/frontend/patches/rel610/Windows/Win32/ <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;b) Download locally onto the GUI-server <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;c) On the installation server, start the SapSetup -&gt; Configuration -&gt; program SapAdmin from the \\netinst directory.Note that the call must occur via a UNC-path: \\\\&lt;servername&gt; <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ShareName&gt;\\netinst\\sapadmin.exe <OL>3. Select menu option Extras --&gt; Apply patch</OL> <OL>4. Specify the patch file which you want to install.<br />On the clients:</OL> <OL>5. Start the installation as usual with Netsetup, select SEM Addon and install. Under no circumstances register OCX files manually.Otherwise a correct deinstallation of Netinstall cannot be ensured any more!</OL> <p>If no installation server is available you are nevertheless able to install the new patch. For this purpose you need the setup update:localpat46D_Y.exe (Y is the current patch level) which is available on:<br />sapservx/general/frontend/patches/rel46D/Windows/Win32.(Refer to Note 361222) To display graphic components you require an SAP Chart - OCX for both Balanced Scorecard and Management Cockpit.You can find the newest version of this component also on the sapservX.CSN Note 318196 describes how to obtain and install this patch.SAP recommends that you always import the newest patch.The SEM components require the Basis-Chart components.Install these with the most current frontend patch.Installation of the most current frontend patch from (for example) sapserv3: for 610: ftp://sapserv3/general/frontend/patches/rel610/Windows/Win32/<br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-FES-GRA (Graphics)"}, {"Key": "Transaction codes", "Value": "HIER"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D030766)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D031909)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000437237/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000437237/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000437237/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000437237/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000437237/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000437237/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000437237/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000437237/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000437237/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "96885", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Downloading front end patches", "RefUrl": "/notes/96885"}, {"RefNumber": "454415", "RefComponent": "FIN-SEM", "RefTitle": "Short dump CNTL_ERROR for transactions in SEM", "RefUrl": "/notes/454415"}, {"RefNumber": "396640", "RefComponent": "FIN-SEM-CPM", "RefTitle": "Implementing SEM 3.0A Frontend patch", "RefUrl": "/notes/396640"}, {"RefNumber": "361222", "RefComponent": "BC-FES-INS", "RefTitle": "SapPatch: Importing GUI Patches", "RefUrl": "/notes/361222"}, {"RefNumber": "314973", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/314973"}, {"RefNumber": "311212", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/311212"}, {"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519 "}, {"RefNumber": "454415", "RefComponent": "FIN-SEM", "RefTitle": "Short dump CNTL_ERROR for transactions in SEM", "RefUrl": "/notes/454415 "}, {"RefNumber": "396640", "RefComponent": "FIN-SEM-CPM", "RefTitle": "Implementing SEM 3.0A Frontend patch", "RefUrl": "/notes/396640 "}, {"RefNumber": "361222", "RefComponent": "BC-FES-INS", "RefTitle": "SapPatch: Importing GUI Patches", "RefUrl": "/notes/361222 "}, {"RefNumber": "96885", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Downloading front end patches", "RefUrl": "/notes/96885 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46D", "To": "46D", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}