{"Request": {"Number": "1083464", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 319, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000006427902017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001083464?language=E&token=563BE0FA10F488606A10EF8DBD349226"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001083464", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001083464/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1083464"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 22}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.09.2007"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PART-ISHMED-ORD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Service Management i.s.h.med"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Partner solutions", "value": "XX-PART", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Clinical System i.s.h.med", "value": "XX-PART-ISHMED", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART-ISHMED*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Management i.s.h.med", "value": "XX-PART-ISHMED-ORD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART-ISHMED-ORD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1083464 - <PERSON><PERSON>. Patient Management: BAdI for Case Validity Check"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note concerns the assignment of caseless objects such as clinical orders, appointments, requests, etc.<br />When these caseless objects are assigned to a case, this case, ideally, should not be completed or final-billed.<br />In i.s.h.med you can use two system parameters which control which message type (error, warning, no message) should be displayed, of a caseless object should be assigned to a final-billed or completed case.<br />The check of these two system parameters is not sufficient for all scenarios of customer use.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>N1CHKOES, N1ANFNST, ISHMED_CASE_VALID, ES_ISHMED_CHECK_CASE_VALID</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is an advance development.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>As the check of these two parameters is not sufficient in all scenarios of customer use, this logic is outsourced in a sample implementation of a business add-in (BAdI), which can be overridden using this customer implementation. This BAdI with default implementation if part of this note.<br />You must refer to the documentation of the BAdI.<br /><br />When importing this note using the NOTE ASSISTANT, you should note that you must also enhance the interface of a function module by adding parameters. As the Note Assistant cannot execute this itself, you may need to manually insert the parameters, otherwise a syntax error will occur on activation.<br /><br />See attachment<br /></p> <b>Carry out the following manual correction:</b><br /> <UL><LI>Call transaction SE37.</LI></UL> <UL><LI>Enter the value ISHMED_IS_FALL_CLOSED in the input field and choose \"Change\".</LI></UL> <UL><LI>Select the 'Import' tab.</LI></UL> <UL><LI>Enter the new parameter I_CALLER with the following values:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Typing Method: TYPE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Associated Type: N1CALLER<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Optional: No<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Pass by Value: Yes<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Short text: Calling program</p> <UL><LI>Choose the \"Export\" tab page.</LI></UL> <UL><LI>Enter the new parameter E_ERR_LEVEL with the following values:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Typing Method: TYPE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Associated Type: RNT40-MARK<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Pass by Value: Yes<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Short text: Error Level (' ', 'E', 'W')</p> <UL><LI>Activate your changes.</LI></UL> <p></p> <b>The following manual correction is only relevant for 4.72:</b><br /> <UL><LI>Call transaction SE18.</LI></UL> <UL><LI>Enter ISHMED_CASE_VALID as the definition name and select \"Change\".</LI></UL> <UL><LI>Choose 'Goto - Default Coding - Create'.</LI></UL> <UL><LI>Follow the user instructions and activate the changes.</LI></UL> <p><br />Implement the source code corrections.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-PART-ISHMED (Clinical System i.s.h.med)"}, {"Key": "Responsible                                                                                         ", "Value": "C5001669"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (C5048269)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001083464/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001083464/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001083464/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001083464/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001083464/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001083464/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001083464/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001083464/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001083464/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "HW1083464_472.zip", "FileSize": "14", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000482332007&iv_version=0022&iv_guid=41E3E49289DD9C4CB9BC28E36C683038"}, {"FileName": "HW1083464_600.zip", "FileSize": "16", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000482332007&iv_version=0022&iv_guid=5DBF09D65C5EE74FBE33F4AD8773E735"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-H", "From": "472", "To": "472", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "602", "To": "602", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "IS-H 472", "SupportPackage": "SAPKIPHF23", "URL": "/supportpackage/SAPKIPHF23"}, {"SoftwareComponentVersion": "IS-H 600", "SupportPackage": "SAPK-60011INISH", "URL": "/supportpackage/SAPK-60011INISH"}, {"SoftwareComponentVersion": "IS-H 602", "SupportPackage": "SAPK-60201INISH", "URL": "/supportpackage/SAPK-60201INISH"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 2, "URL": "/corrins/0001083464/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 97, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "737630 ", "URL": "/notes/737630 ", "Title": "IS-H*MED: Surgery Monitor - Case Lock", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "740915 ", "URL": "/notes/740915 ", "Title": "IS-H*MED: Surgery Monitor: Replace Service", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "742867 ", "URL": "/notes/742867 ", "Title": "IS-H/IS-H*MED: Clinical Order: Admission/Visit Without Appt.", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "743633 ", "URL": "/notes/743633 ", "Title": "IS-H*MED: Manual Case Assignment in Clinical Order", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "743771 ", "URL": "/notes/743771 ", "Title": "IS-H*MED: Start Surgery - Diagnosis Caseless Request", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "743950 ", "URL": "/notes/743950 ", "Title": "IS-H*MED: Surgery Monitor: Clinical Order Diagnoses", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "745677 ", "URL": "/notes/745677 ", "Title": "IS-H/IS-H*MED: Clin. Order: Admission - Create Case Ref.", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "745968 ", "URL": "/notes/745968 ", "Title": "IS-H*MED: Team Entry - Entries not Current", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "748295 ", "URL": "/notes/748295 ", "Title": "IS-H/IS-H*MED:Clin. Order/Admission NV2000/Surgeries View Ty", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "749732 ", "URL": "/notes/749732 ", "Title": "IS-H*MED: Surg. Monitor: Start Surg. for Emerg. Surgeries", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "756033 ", "URL": "/notes/756033 ", "Title": "IS-H*MED: Create Case Reference - Dialog Box", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "760391 ", "URL": "/notes/760391 ", "Title": "IS-H*MED: Surgery Monitor - Sequence of Team Members", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "761270 ", "URL": "/notes/761270 ", "Title": "IS-H*MED: Surgery Request: Tasks in Team Entry Table", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "763268 ", "URL": "/notes/763268 ", "Title": "IS-H*MED:Start Surgery - No More Synchronization", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "766344 ", "URL": "/notes/766344 ", "Title": "IS-H/IS-H*MED: Outpatient/service facility - assign case", "Component": "IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "767195 ", "URL": "/notes/767195 ", "Title": "IS-H*MED:Surgery Monitor: Start Surgery - Departments (Dept.", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "768421 ", "URL": "/notes/768421 ", "Title": "IS-H*MED: Surgery Monitor - Appt Data, Start Surgery", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "773023 ", "URL": "/notes/773023 ", "Title": "IS-H*MED: clin. order: Employee - Authorization", "Component": "IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "773261 ", "URL": "/notes/773261 ", "Title": "IS-H*MED: Quick entry service localization ID 15398", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "775536 ", "URL": "/notes/775536 ", "Title": "IS-H*MED: Request: Department as Requesting Nursing OU", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "781265 ", "URL": "/notes/781265 ", "Title": "IS-H*MED: Service Management: Quick Service Entry", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "783109 ", "URL": "/notes/783109 ", "Title": "IS-H*MED:Start Surgery:Employee Responsible Check", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "783738 ", "URL": "/notes/783738 ", "Title": "IS-H*MED: Surgery Request: Surgery Rooms w/o Planning Object", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "785954 ", "URL": "/notes/785954 ", "Title": "IS-H*MED: Surgery Monitor: Cancel Secondary Surgery", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "787882 ", "URL": "/notes/787882 ", "Title": "IS-H*MED: Start Surgery - Employee Responsible Check", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "790001 ", "URL": "/notes/790001 ", "Title": "IS-H*MED:Create Case Reference: Cancellation Indicator Reset", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "790391 ", "URL": "/notes/790391 ", "Title": "IS-H*MED: Patient Organizer: Create Surgery Request", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "793826 ", "URL": "/notes/793826 ", "Title": "IS-H/IS-H*MED: Clinical Order - Data Migration Error", "Component": "IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "795946 ", "URL": "/notes/795946 ", "Title": "IS-H*MED: Surgery Monitor - Date Format - Documents", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "797648 ", "URL": "/notes/797648 ", "Title": "IS-H*MED: Quick Entry - Various Errors", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "799876 ", "URL": "/notes/799876 ", "Title": "IS-H*MED/IS-H: Clin.Work Stat: Create Case Ref. Short Dump", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "799889 ", "URL": "/notes/799889 ", "Title": "Clinical Order: Deadline, Opening Times", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "800815 ", "URL": "/notes/800815 ", "Title": "IS-H*MED: Request: Lock Entry on Patient (NPAT)", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "802639 ", "URL": "/notes/802639 ", "Title": "IS-H*MED: Service Management: Localization of a Service", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "803074 ", "URL": "/notes/803074 ", "Title": "IS-H*MED: Clinical Order/Request: Start Surgery (Caseless)", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "803955 ", "URL": "/notes/803955 ", "Title": "IS-H*MED: Clinical Work Station: Borrow Medical Record", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "804036 ", "URL": "/notes/804036 ", "Title": "IS-H*MED: Start Surgery - Treatment Category", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "804422 ", "URL": "/notes/804422 ", "Title": "IS-H*MED: Caseless Request - Technical Document", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "810363 ", "URL": "/notes/810363 ", "Title": "Case Revision: Authorization Checks", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "816758 ", "URL": "/notes/816758 ", "Title": "Appointment Mgmt/Planning: Department Presetting", "Component": "IS-H-PM"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "821329 ", "URL": "/notes/821329 ", "Title": "Surgery Monitor: Performance Optimized (OR Plan/OR Schedule)", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "823641 ", "URL": "/notes/823641 ", "Title": "Preregistration, Clinical Order: Create Surgery Preregistrat", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "828108 ", "URL": "/notes/828108 ", "Title": "Request: Runtime Error Using System Parameter N1ANFRSP", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "829989 ", "URL": "/notes/829989 ", "Title": "Service management: Check of requesting, nursing OU", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "831651 ", "URL": "/notes/831651 ", "Title": "Surgeries: Surgery monitor - check if surgery is cancelled", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "832753 ", "URL": "/notes/832753 ", "Title": "Clinical Order: Enter Service for Billed Case", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "835345 ", "URL": "/notes/835345 ", "Title": "Surgeries: Start surgery - treatment category", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "837125 ", "URL": "/notes/837125 ", "Title": "Surgeries: Emergency surgeries - Department of surgery movement", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "839757 ", "URL": "/notes/839757 ", "Title": "Clin.patient management: Create visit Austria-specific fields", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "845391 ", "URL": "/notes/845391 ", "Title": "Surgeries: Start surgery after discharge", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "847194 ", "URL": "/notes/847194 ", "Title": "Surgeries: Start surgery - treatment category for all case types", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "847872 ", "URL": "/notes/847872 ", "Title": "Surgeries: Surgery monitor: Surg.Doc.- Diagnoses clin. orders", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "849836 ", "URL": "/notes/849836 ", "Title": "OR, Request: Functions - Material without MM Integration", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "850839 ", "URL": "/notes/850839 ", "Title": "Service Management: Create Request - Caseless", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "852198 ", "URL": "/notes/852198 ", "Title": "Surgeries: Start surgery: Treatment category all case types", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "852813 ", "URL": "/notes/852813 ", "Title": "Case Revision - Check for Range of Services", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "854371 ", "URL": "/notes/854371 ", "Title": "Service Management: Create Request - Caseless", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "856482 ", "URL": "/notes/856482 ", "Title": "Clinical Order: Case Optional", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "858371 ", "URL": "/notes/858371 ", "Title": "i.s.h.med:16128 BAdI Quick Service Entry", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "858535 ", "URL": "/notes/858535 ", "Title": "Request: Case Reference - Diagnosis Caseless Request", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "859409 ", "URL": "/notes/859409 ", "Title": "Patient Organizer: Create Caseless Request", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "861148 ", "URL": "/notes/861148 ", "Title": "Radiology: Request with Desired Room", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "872693 ", "URL": "/notes/872693 ", "Title": "Surg.system: Surgery monitor - Diagnosis display", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "872887 ", "URL": "/notes/872887 ", "Title": "Clin. Work Station: Requests View (Clinical Orders)", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "878781 ", "URL": "/notes/878781 ", "Title": "Surgeries: Surgery Monitor: Release Service - Admission Faci", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "879631 ", "URL": "/notes/879631 ", "Title": "Clinical order: Case completed/final billed", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "885082 ", "URL": "/notes/885082 ", "Title": "Clin.Patient Management: Patient No. XXX Replaced by YYY", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "896792 ", "URL": "/notes/896792 ", "Title": "Patient Organizer/Surg. Monitor: Documents for Surgery", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "898239 ", "URL": "/notes/898239 ", "Title": "Clinical Order: \"Case Obligatory\" Indicator", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "901189 ", "URL": "/notes/901189 ", "Title": "Diagnosis/Service Quick Entry: Scroll", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "902898 ", "URL": "/notes/902898 ", "Title": "Surgeries: Begin Surgery - Treatment Category", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "910320 ", "URL": "/notes/910320 ", "Title": "Request: <PERSON>", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "910923 ", "URL": "/notes/910923 ", "Title": "Clin. order: Case check when detaching order from case", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "921308 ", "URL": "/notes/921308 ", "Title": "Surgery System: Start Surgery - Inpatient Admission in Futur", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "922171 ", "URL": "/notes/922171 ", "Title": "Clinical Order: Employee Responsible", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "922252 ", "URL": "/notes/922252 ", "Title": "Surgery System: Begin Surgery - Change of Case Type After Ti", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "926177 ", "URL": "/notes/926177 ", "Title": "Clin. Patient Management:  As<PERSON> Case - Diagnoses Authoriza", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "945674 ", "URL": "/notes/945674 ", "Title": "Surgery System:Begin Surgery - Canceled Services Considered", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "946917 ", "URL": "/notes/946917 ", "Title": "Diagnosis/Service Quick Entry: Long Text for Diagnosis", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "948889 ", "URL": "/notes/948889 ", "Title": "Surgery System: Function Terminate Materials Consumption Doc", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "949591 ", "URL": "/notes/949591 ", "Title": "Composite Request - Services Without Case Reference", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "962065 ", "URL": "/notes/962065 ", "Title": "Clinical Order: Autom. Status Change With Begin Surgery", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "964328 ", "URL": "/notes/964328 ", "Title": "Surgery System: Surgery Monitor - Create Clinical Order", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "974469 ", "URL": "/notes/974469 ", "Title": "Diagnosis/Service Quick Entry: Performance Problem", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "977422 ", "URL": "/notes/977422 ", "Title": "Quick Diagnosis/Service Entry: Filters Deleted", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "978147 ", "URL": "/notes/978147 ", "Title": "Clinical Pat. Mgmt: Case Revision - Reassign ISH Services", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "996012 ", "URL": "/notes/996012 ", "Title": "Quick Diagnosis/Service Entry: Week of Pregnancy", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "997854 ", "URL": "/notes/997854 ", "Title": "Request: Transfer Indicators from Collective Request", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "999294 ", "URL": "/notes/999294 ", "Title": "Request: Presetting Request Data", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1008668 ", "URL": "/notes/1008668 ", "Title": "Surgery System: Surgery Monitor - Entered Services Not Displ", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1013694 ", "URL": "/notes/1013694 ", "Title": "Clin. Work Station: Surgery View Type - Priority Not Filled", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1018495 ", "URL": "/notes/1018495 ", "Title": "Diagnosis/Service Quick Entry: Scrolling", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1020559 ", "URL": "/notes/1020559 ", "Title": "Clin.Patient Management:  Case Revision - Completed Cases", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1048434 ", "URL": "/notes/1048434 ", "Title": "Clinical Order: Lock Concept", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1066419 ", "URL": "/notes/1066419 ", "Title": "Clin.Patient Mgmt:Create Case Reference - ISHMED_CASE_ASSIGN", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1071083 ", "URL": "/notes/1071083 ", "Title": "Request: <PERSON>", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1081744 ", "URL": "/notes/1081744 ", "Title": "Surgery System: Time Entry - Runtime Error", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "878781 ", "URL": "/notes/878781 ", "Title": "Surgeries: Surgery Monitor: Release Service - Admission Faci", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "879631 ", "URL": "/notes/879631 ", "Title": "Clinical order: Case completed/final billed", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "885082 ", "URL": "/notes/885082 ", "Title": "Clin.Patient Management: Patient No. XXX Replaced by YYY", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "898239 ", "URL": "/notes/898239 ", "Title": "Clinical Order: \"Case Obligatory\" Indicator", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "901189 ", "URL": "/notes/901189 ", "Title": "Diagnosis/Service Quick Entry: Scroll", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "902898 ", "URL": "/notes/902898 ", "Title": "Surgeries: Begin Surgery - Treatment Category", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "910320 ", "URL": "/notes/910320 ", "Title": "Request: <PERSON>", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "910923 ", "URL": "/notes/910923 ", "Title": "Clin. order: Case check when detaching order from case", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "921308 ", "URL": "/notes/921308 ", "Title": "Surgery System: Start Surgery - Inpatient Admission in Futur", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "922252 ", "URL": "/notes/922252 ", "Title": "Surgery System: Begin Surgery - Change of Case Type After Ti", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "945674 ", "URL": "/notes/945674 ", "Title": "Surgery System:Begin Surgery - Canceled Services Considered", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "949591 ", "URL": "/notes/949591 ", "Title": "Composite Request - Services Without Case Reference", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "962065 ", "URL": "/notes/962065 ", "Title": "Clinical Order: Autom. Status Change With Begin Surgery", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "978147 ", "URL": "/notes/978147 ", "Title": "Clinical Pat. Mgmt: Case Revision - Reassign ISH Services", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "997854 ", "URL": "/notes/997854 ", "Title": "Request: Transfer Indicators from Collective Request", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1013694 ", "URL": "/notes/1013694 ", "Title": "Clin. Work Station: Surgery View Type - Priority Not Filled", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1020559 ", "URL": "/notes/1020559 ", "Title": "Clin.Patient Management:  Case Revision - Completed Cases", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1048434 ", "URL": "/notes/1048434 ", "Title": "Clinical Order: Lock Concept", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1066419 ", "URL": "/notes/1066419 ", "Title": "Clin.Patient Mgmt:Create Case Reference - ISHMED_CASE_ASSIGN", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1081744 ", "URL": "/notes/1081744 ", "Title": "Surgery System: Time Entry - Runtime Error", "Component": "XX-PART-ISHMED"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}