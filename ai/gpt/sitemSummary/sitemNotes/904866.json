{"Request": {"Number": "904866", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 453, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005197182017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000904866?language=E&token=8857809D00E6E1C5303E098FBE83C659"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000904866", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000904866/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "904866"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "21.02.2006"}, "SAPComponentKey": {"_label": "Component", "value": "CRM-PCF"}, "SAPComponentKeyText": {"_label": "Component", "value": "obsolete: People Centric UI Framework"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Customer Relationship Management", "value": "CRM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "obsolete: People Centric UI Framework", "value": "CRM-PCF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM-PCF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "904866 - Correction CRM BSP Framework, SP06 (09)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Correction CRM BSP Framework, SP06 (09)<br /><br />Symptom 1 :The blank entry created when Add Entry is clicked in Detail area is lost if toggled to List or Form without entering any data. The user had to click Add Entry again to Create a new blank entry.<br /><br />Symptom 2: The extended personalization (rendering behaviour and filter line) only was available for \"pure\" lists. For trees both in \"list\" and in \"hierarchical\" mode (some trees can be toggled between list view and hierarchical view) the two checkboxes for the extended personalization were disabled.<br />This has been modified: Now also the extended personalization is also available for trees in \"list mode\".</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Correction CRM BSP Framework, SP06 (09)</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Manual Corretions for solving Symptom 1.<br />1.&#x00A0;&#x00A0;Goto SE24, open class CL_CRM_BSP_FRAME_DETAIL in Change mode<br /><br />2.&#x00A0;&#x00A0;Goto Attributes tab<br /><br />3.&#x00A0;&#x00A0;Add following three attributes<br />   <br /> 3.1) Attribute:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GV_ADD_ENTRY_CLICKED<br /><br />  Level:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Instance Attribute<br /><br />  Typing:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Type<br /><br />  Reference type: CRMT_BOOLEAN<br /><br /> 3.2) Attribute:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GV_SCREEN_STRUC_OLD<br /><br />  Level:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Instance Attribute<br /><br />  Typing:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Type<br /><br />  Reference type: STRING<br /><br /> 3.3)Attribute:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GV_SCREEN_STRUC_NEW<br /><br />  Level:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Instance Attribute<br /><br />  Typing:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Type<br /><br />  Reference type: STRING<br /><br />Manual Corretions for solving Symptom 2:</p> <OL>1. Goto SE24, open class CL_CRM_BSP_LIST_PERS</OL> <OL>2. Goto Attributes tab</OL> <OL>3. Add new attribute</OL> <UL><UL><LI>Attribute:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; GT_LINK_CACHE_EXP</LI></UL></UL> <UL><UL><LI>Level:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Static Attribute</LI></UL></UL> <UL><UL><LI>Visibility:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Public</LI></UL></UL> <UL><UL><LI>Typing:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Type</LI></UL></UL> <UL><UL><LI>Associated Type: CRMT_BSP_LINK_CACHE_EXP</LI></UL></UL> <UL><UL><LI>Description:&#x00A0;&#x00A0;&#x00A0;&#x00A0; Cache for Personalization Link</LI></UL></UL> <OL>4. Add new attribute</OL> <UL><UL><LI>Attribute:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; GC_TREETYPE</LI></UL></UL> <UL><UL><LI>Level:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Constant</LI></UL></UL> <UL><UL><LI>Visibility:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Private</LI></UL></UL> <UL><UL><LI>Typing:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Type</LI></UL></UL> <UL><UL><LI>Associated Type: STRING</LI></UL></UL> <UL><UL><LI>Description:&#x00A0;&#x00A0;&#x00A0;&#x00A0; Type of the tree</LI></UL></UL> <UL><UL><LI>Initial value:&#x00A0;&#x00A0; 'TREETYPE'</LI></UL></UL> <OL>5. Add new attribute</OL> <UL><UL><LI>Attribute:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; GC_TREETYPE_LIST</LI></UL></UL> <UL><UL><LI>Level:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Constant</LI></UL></UL> <UL><UL><LI>Visibility:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Private</LI></UL></UL> <UL><UL><LI>Typing:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Type</LI></UL></UL> <UL><UL><LI>Associated Type: STRING</LI></UL></UL> <UL><UL><LI>Description:&#x00A0;&#x00A0;&#x00A0;&#x00A0; LIST-type of the tree</LI></UL></UL> <UL><UL><LI>Initial value:&#x00A0;&#x00A0; 'LIST'</LI></UL></UL> <OL>6. Add new attribute</OL> <UL><UL><LI>Attribute:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; GV_TREETYPE</LI></UL></UL> <UL><UL><LI>Level:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Instance Attribute</LI></UL></UL> <UL><UL><LI>Visibility:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Private</LI></UL></UL> <UL><UL><LI>Typing:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Type</LI></UL></UL> <UL><UL><LI>Associated Type: CRMT_BSP_SCRTYPE</LI></UL></UL> <UL><UL><LI>Description:&#x00A0;&#x00A0;&#x00A0;&#x00A0; Current type of the tree (LIST or TREE)</LI></UL></UL> <OL>7. Delete the attribute GT_LINK_CACHE</OL> <OL>8. Goto Methods tab</OL> <OL>9. Select method GET_LINK_TO_PERS</OL> <OL>10. Press the button Parameters</OL> <OL>11. Add new parameter:</OL> <UL><UL><LI>Parameter:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; IV_TREETYPE</LI></UL></UL> <UL><UL><LI>Type:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Importing </LI></UL></UL> <UL><UL><LI>Optional:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;selected</LI></UL></UL> <UL><UL><LI>Associated Type: CRMT_BSP_SCRTYPE </LI></UL></UL> <UL><UL><LI>Description:&#x00A0;&#x00A0;&#x00A0;&#x00A0; Type of the tree (LIST or TREE)</LI></UL></UL> <OL>12. Save and Activate</OL> <OL>13. Goto SE11</OL> <OL>14. Create new data type</OL> <UL><UL><LI>Select 'Data type'</LI></UL></UL> <UL><UL><LI>Enter 'CRM_BSP_LINK_CACHE_EXP' in the belonging input field</LI></UL></UL> <UL><UL><LI>Press the button 'Create'</LI></UL></UL> <UL><UL><LI>Choose 'Structure'</LI></UL></UL> <UL><UL><LI>Enter as description 'Expanded Object Key for Personalization'</LI></UL></UL> <UL><UL><LI>Click into the first free field in the column 'Component' and choose 'Edit' -&gt; 'Include' -&gt; 'Insert': in the field 'Structure' maintain the value 'CRM_BSP_LINK_CACHE' and press the button 'Continue'</LI></UL></UL> <UL><UL><LI>Maintain another component called 'TREETYPE' of 'Component Type' 'CRM_BSP_STRING'</LI></UL></UL> <UL><UL><LI>Press the button 'Save'</LI></UL></UL> <UL><UL><LI>Choose 'Can be Enhanced (Deep)'</LI></UL></UL> <UL><UL><LI>Choose 'CRM_BSP_FRAME_SC_TOOLS' as 'Package' and press the button 'Save'</LI></UL></UL> <OL>15. Activate the data type</OL> <OL>16. Goto SE11</OL> <OL>17. Create new data type</OL> <UL><UL><LI>Select 'Data type'</LI></UL></UL> <UL><UL><LI>Enter 'CRM_BSP_PERS_OBJ_EXP' in the belonging input field</LI></UL></UL> <UL><UL><LI>Press the button 'Create'</LI></UL></UL> <UL><UL><LI>Choose 'Structure'</LI></UL></UL> <UL><UL><LI>Enter as description 'Expanded Object Key for Personalization'</LI></UL></UL> <UL><UL><LI>Click into the first free field in the column 'Component' and choose 'Edit' -&gt; 'Include' -&gt; 'Insert': in the field 'Structure' maintain the value 'CRM_BSP_PERS_OBJ' and press the button 'Continue'</LI></UL></UL> <UL><UL><LI>Maintain another component called 'TREETYPE' of 'Component Type' 'CRM_BSP_STRING'</LI></UL></UL> <UL><UL><LI>Press the button 'Save'</LI></UL></UL> <UL><UL><LI>Choose 'Can be Enhanced (Deep)'</LI></UL></UL> <UL><UL><LI>Choose 'CRM_BSP_FRAME_SC_TOOLS' as 'Package' and press the button 'Save'</LI></UL></UL> <OL>18. Activate the data type</OL> <OL>19. Goto SE11</OL> <OL>20. Create new data type</OL> <UL><UL><LI>Select 'Data type'</LI></UL></UL> <UL><UL><LI>Enter 'CRMT_BSP_LINK_CACHE_EXP' in the belonging input field</LI></UL></UL> <UL><UL><LI>Press the button 'Create'</LI></UL></UL> <UL><UL><LI>Choose 'Table type'</LI></UL></UL> <UL><UL><LI>In the input field for 'Short text' maintain the description 'Expanded Cache for Personalization Link'</LI></UL></UL> <UL><UL><LI>Chose the tab 'Line Type' and select 'Line Type' and maintain in the belonging input field the value 'CRM_BSP_LINK_CACHE_EXP' and press the button 'Save'</LI></UL></UL> <UL><UL><LI>Choose 'CRM_BSP_FRAME_SC_TOOLS' as 'Package' and press the button 'Save'</LI></UL></UL> <OL>21. Activate the data type</OL> <p>-</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CA-GTF-PCF (People Centric UI Framework)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023949)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I029917)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000904866/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000904866/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000904866/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000904866/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000904866/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000904866/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000904866/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000904866/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000904866/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "949447", "RefComponent": "CRM-BF", "RefTitle": "mySAP CRM 2005 SP Stack 05", "RefUrl": "/notes/949447"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "949447", "RefComponent": "CRM-BF", "RefTitle": "mySAP CRM 2005 SP Stack 05", "RefUrl": "/notes/949447 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_ABA", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_ABA 700", "SupportPackage": "SAPKA70007", "URL": "/supportpackage/SAPKA70007"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_ABA", "NumberOfCorrin": 1, "URL": "/corrins/0000904866/44"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 21, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "868404 ", "URL": "/notes/868404 ", "Title": "Correction CRM BSP Framework, SP05", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "871213 ", "URL": "/notes/871213 ", "Title": "Correction CRM BSP Framework, SP05 (2)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "871793 ", "URL": "/notes/871793 ", "Title": "Correction CRM BSP Framework, SP05 (3)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "871918 ", "URL": "/notes/871918 ", "Title": "Correction CRM BSP Framework, SP05 (4)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "872178 ", "URL": "/notes/872178 ", "Title": "Correction CRM BSP Framework, SP05 (5)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "872453 ", "URL": "/notes/872453 ", "Title": "Correction CRM BSP Framework, SP05 (6)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "873034 ", "URL": "/notes/873034 ", "Title": "Correction CRM BSP Framework, SP05 (7)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "873662 ", "URL": "/notes/873662 ", "Title": "Correction CRM BSP Framework, SP05 (8)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "875518 ", "URL": "/notes/875518 ", "Title": "Correction CRM BSP Framework, SP05 (9)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "880178 ", "URL": "/notes/880178 ", "Title": "Basis-relevant PCUI changes", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "881858 ", "URL": "/notes/881858 ", "Title": "Correction CRM BSP Framework, SP05 (13)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "884465 ", "URL": "/notes/884465 ", "Title": "BSP-relevant changes in the CRM PCUI (3)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "885050 ", "URL": "/notes/885050 ", "Title": "Correction CRM BSP Framework, SP06 (01)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "887916 ", "URL": "/notes/887916 ", "Title": "Correction CRM BSP Framework, SP06 (2)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "889142 ", "URL": "/notes/889142 ", "Title": "Correction CRM BSP Framework, SP06 (03)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "890821 ", "URL": "/notes/890821 ", "Title": "Correction CRM BSP Framework, SP06 (04)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "890843 ", "URL": "/notes/890843 ", "Title": "Correction CRM BSP Framework, SP06 (05)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "895343 ", "URL": "/notes/895343 ", "Title": "Correction CRM BSP Framework, SP06 (06)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "900260 ", "URL": "/notes/900260 ", "Title": "CRM5.0 Tree OIC messages: 849614 and 914460 (SP06)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "901864 ", "URL": "/notes/901864 ", "Title": "Correction CRM BSP Framework, SP06 (07)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "902573 ", "URL": "/notes/902573 ", "Title": "Correction CRM BSP Framework, SP06 (08)", "Component": "CRM-PCF"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}