{"Request": {"Number": "517102", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 489, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015209612017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000517102?language=E&token=BDB735AA2C22EC0D85E25EB6BF0736D2"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000517102", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000517102/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "517102"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 22}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "25.08.2004"}, "SAPComponentKey": {"_label": "Component", "value": "FS-BP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Partner"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Services", "value": "FS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Partner", "value": "FS-BP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS-BP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "517102 - BP_ADU: Composite SAP note time-dependent address usages"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You use the SAP business partner for Financial Services. In the course of the upgrade of different Releases to Banking Release 4.63/CFM 2.0 or R/3 Enterprise 1.10 or 2.0 various problems occur regarding time-dependent address usages. These problems both affect the business partner maintenance online and the external data transfer as well as the conversion and parallel maintenance between the SAP BP and the TR BP. The read modules (address determination) are also affected.<br />In other software components, these notes also extend the functions of time-dependent address usages (both the online maintenance and external data transfer and the read modules) and correct existing errors.<br />Bear in mind that the note long texts also contain important information (partly consulting information, partly also Customizing changes, creation of error messages or function modules). Thus, carefully read all notes relevant for your Support Package level and carry out the steps they describe.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SAP BP, FS BP, business partners, time-dependent address usages, FSBP_04, conversion, parallel supply, external data transfer, BUT021_FS, Customizing, BUT021, BUT020</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The solution part of this note contains an overview of the notes regarding the time-dependent address usages created up to April 15, 2004.<br />This note will be updated frequently in the future. Thus, if problems concerning the time-dependent address usages occur, first check this composite SAP note to see whether new notes have been added.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The following notes are listed by release. The Support Package relevant to the corresponding release is entered in parentheses after the short text of the note, thus you can see at a glance if the note is still relevant for your current Support Package version or not.<br /><br />If you use Release R/3 Enterprise 4.70 Financial Services 1.10 or Financial Services 2.0 (EA-FINSERV 470x110 or EA-FINSERV 470x200), the notes that were created for Release ABA 6.20 and R/3 Core 4.70 (R/3 Enterprise 4.70) are relevant to you as well.<br /><br />If you use Release R/3 Enterprise 4.70 Financial Services 1.10 or Financial Services 2.0 ((EA-FINSERV 470x110 or EA-FINSERV 470x200), it is absolutely necessary that you consider Note 588916. That note describes the conflict that exists in this release in connection with the different versions of the address usages and refers to other notes that provide a solution for the problem.<br /></p> <b>I. Consulting notes (without program corrections) - release-independent</b><br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note&#x00A0;&#x00A0;&#x00A0;&#x00A0; Short text<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;724492 - BP_DIA: Correspondence language for SAP business partner<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;717833 - BP_ADU: R1856 \"Error when updating table BUT021\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;616044 - BP_ADU: Restrictions on the use of BAPIs for the SAP FS BP<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;614663 - BP_ADU: Addresses in the overview indistinguishable (online)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;594871 - BDT-Cust: Address usage and button \"company code data\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;591721 - SAP BP: Deletion report for time-dependent address usages<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;588916 - BP_ADU: Address usage conflicts for business partner (important!)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;536249 - SAP BP: PAR: House number not transferred to TR BP<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;532670 - Activating the R/3 Enterprise Extension EA-FS<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;504734 - SAP BP: Time-dependent address usages of SAP BP for FS</p> <UL><UL><LI>This note contains a complete documentation of the time-dependent address usages.<br /></LI></UL></UL> <b>II. Notes regarding Release BANK/CFM 463_20 or BANK-TRBK 2.0</b><br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note&#x00A0;&#x00A0;&#x00A0;&#x00A0; Short text<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;767427 - BP_ADU: Error R1614 in report RFSBPBUT021 (26)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;766567 - BP_ADU: Termination when displaying change documents (26)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;755612 - BP_ADU: Error in reading address usages (26)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;750491 - BP_ADU: Error while reading address data (26)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;749316 - BP_ADU: Report RFSBPBUT021, error in log (26)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;747754 - BP_ADU: Performance of report RFSBPBUT021<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;738298 - BP_ADU: Performance improvement for report RFSBPBUT021 (26)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;729592 - BP_ADU: Time-dependent address usages Financial Services<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;727528 - BP_ADU: Error in the address uses (BANK/CFM 463_20)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;725889 - BP_ADU: Update termination for report RFSBPBUT021 (25)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;725887 - BP_ADU: Error in the address determination (25)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;724144 - BP_TR1: Standard indicator not set in table BUT020</p> <UL><UL><LI>You can find the relevant correction in Note 721609 (26)</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;724023 - BP_ADU: FM FSBP_READ_ADDRESS_DATA not rfc-enabled (25)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;721721 - BP_ADU: Performance problem with report RFSBPBUT021 (25)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;717945 - BP_ADU: Address usages not transferred to loans (25)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;717831 - BP_ADU: Various corrections for report RFSBPBUT021 (25)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;708117 - BP_ZZZ: City name is only output in upper case (25)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;705841 - BP_ADU: Communication data is selected incompletely (25)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;703393 - BP_ADU: Same usage more then once in the menu tree (25)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;700524 - BP_ADU: External data transfer of address usages (25)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;695812 - BP_POT: Display of address types for loans FNVS (25)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;693547 - BP_TR1: Correspondence language for persons is missing<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;691847 - BP_ADU: Short dump when creating/changing a loan (24)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;691327 - BP_ADU: Various corrections to FS read modules (24)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;689957 - BP_TR2: Correction report adjustment for table ADRC (24)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;686087 - BP_ADU: Error when editing a special arrangement (24)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;679126 - SAP BP: Various corrections for FS address read modules (24)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;677526 - Report RFSBPBUT020 deletes external partner number (24)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;677462 - SAP BP address usages: Start date after end date possible (24)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;677237 - SI: Error when creating an alternative address (24)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;670034 - BP_ZZZ: Incorrect formatting of addresses (23)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;666801 - SAP BP: Error AM053 after RFSBP_CUST_ADDRESS_UPDATE (23)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;663280 - SAP BP address usages: Multiple assignment of same address (24)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;656796 - BP_CVI: Bckgrnd maintnnce time-depndnt address usages in CVI (22)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;652049 - SAP BP:Problems while scrollg in time-dependent addr. usages (22)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;651195 - BP_ADU: Addr. usages lost in loan during partner maintenance (24)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;648464 - BP_ADU: Incorrect read logic in FSBP_DBREAD_BUT021_FS (21)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;647973 - BP_ADU: Address info in addr. usage overview not updated (21)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;645872 - BP_TR1: Conversion of form of addr:Corr report for tab. ADRC (21)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;643033 - SAP BP ADVW: Description 'SDSD' for address usage XXDEFAULT (21)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;642884 - BP_ADV: Incorrect fields in EDT filled by addr. usage (21)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;636199 - EDT SAP BP FS address usages: Overlaps possible (21)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;634554 - SAP BP: Read module FSBP_DBREAD_BUT021_FS incorrect (21)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;633843 - BP_ADU: Error in address formatting (form of addr, language) (21/22)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;632530 - SAP BP ADVW: Various corrections \"Address usage conflicts\" (24)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;623450 - SAP BP: Problems using PS and FS applications in one client (20)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;621959 - PAR: Parallel update of address usages: incorrect transfer (20)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;619636 - SAP BP:Various corrections for read modules of SAP BP FS (20)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;618291 - C_ADR: Redundant update BUT021 from BUT021_FS (20)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;617414 - SAP BP: Adjustments in report RFTBP030 (20)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;617220 - BP_POT: Address transfer to loan contract (20)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;617052 - SAP BP: Various adjustments for report RFSBP21FS (20)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;616625 - SAP BP: Report RFSBPBUT020 not complete (20)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;615194 - SAP BP: Transfer of current standard address to customer (20)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;615131 - FS BP: c/o address not transferred (20)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;607007 - SAP BP: Report RFSBPBUT021 does not change table BUT020 (19)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;605368 - BP_ADU: FM FS04_BUPA_MEMORY_GET returns incorrect data (19)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;603007 - BP_ADU: E-mail address not found (error FTRO263) (18)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;601008 - BP_ADU: Read logic in FS04_BUPA_MEMORY_GET not correct (18)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;598142 - BP_ADU: Incorrect data transfer from BUT021_FS to BUT021 (18)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;590392 - BP_TR1: Incorrect report RFTBP030 (convert BP addresses) (17/18)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;588851 - SAP BP ADVW:Buttons active in display mode (18/19)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;581758 - SAP BP ADVW:Buttons active in display mode (16)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;573969 - BP_CDC:Incorrect generation change docs for address usage (15)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;572257 - BP_CDC:Short dump change docmts of time-depend.addr. usages (15/16)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;569561 - SAP BP ADVW: Incorrect error because of overlap, short dump (16)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;566432 - SAP BP ADVW: Sorting in tree, overlaps, short dump (15)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;564976 - SAPBP: update termin (CML) &amp; delete address (15)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;564339 - BP_CDC: Validity date is missing in the change documents (15)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;563077 - SAP BP: Short dump with report RFSBP21FS (memory overflow) (15)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;560753 - SAP BP: Address read modules find no/incorrect address type (14)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;559617 - SAP BP address uses:Various corrections (14)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;555558 - Address uses: Different corrections (14)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;555138 - SAP BP: Address uses (dialog and conversion) (14)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;550854 - SAP BP: SAP + TR BP standard address type in parallel maint. (13)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;550547 - FS BP:EDT:Termination if internal address number not filled (13)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;550153 - SAP BP: Address modules always generate default address (SP13)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;547299 - SAP BP: Address usages (conversion, parallel maint. , dialog) (13)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;544759 - SAP BP: Conversion/maintenance of address uses (XXDEFAULT) (13)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;538748 - PAR: Dialog box (missing postal code) when saving SAP BP (SP12)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;536636 - BP_XDT: Error FSBP519 with Modify/Delete of address usage (SP12)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;535351 - BP_XDT:MODIFY always uses number of standard address (SP12)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;532705 - BP_CDC: Validity date is missing in the change documents (SP13)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;532246 - BP_XDT: Missing address number for the EDT address usages (SP11)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;532191 - BP_XDT: Error in DI of the time-dependent address uses (SP11)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;531424 - BP_XDT:Incorrect Modify of the address usages for DI (SP11)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;513762 - PAR:Standard address transferred incorrectly into BP030 (SP10)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;505977 - SAP BP conversion Customizing: View V_TP14 and standard flag (SP08)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;</p> <b>III. Notes regarding SAP ABA 6.20</b><br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note&#x00A0;&#x00A0;&#x00A0;&#x00A0; Short text (SP)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;767427 - BP_ADU: Termination when displaying change documents (44)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;766567 - BP_ADU: Termination when displaying change documents (44)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;755612 - BP_ADU: Error in reading address usages (43)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;749316 - BP_ADU: Report RFSBPBUT021, error in log (43)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;747754 - BP_ADU: Performance of report RFSBPBUT021 (42)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;738298 - BP_ADU: Performance improvement for report RFSBPBUT021 (41)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;729592 - BP_ADU: Time-dependent address usages Financial Services (41)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;725889 - BP_ADU: Update termination for report RFSBPBUT021 (40)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;725887 - BP_ADU: Error in the address determination (40)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;724023 - BP_ADU: FM FSBP_READ_ADDRESS_DATA not rfc-enabled (39)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;721721 - BP_ADU: Performance problem with report RFSBPBUT021 (39)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;717945 - BP_ADU: Address usages not transferred to loans (39)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;717831 - BP_ADU: Various corrections for report RFSBPBUT021 (39)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;705841 - BP_ADU: Communication data is selected incompletely (38)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;704768 - GP_ADU: Incorrect traffic lights with WUL for addresses (38)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;704767 - BP_ADU: Address usages not active in some roles (38)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;703393 - BP_ADU: Same usage more then once in the menu tree (38)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;700524 - BP_ADU: External data transfer of address usages (38)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;695812 - BP_POT: Display of address types for loans FNVS (37)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;695595 - BP_BAP: Source code for installation of Note 629569<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;691847 - BP_ADU: Short dump when creating/changing a loan (36)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;691327 - BP_ADU: Various corrections to FS read modules (37)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;686087 - BP_ADU: Error when editing a special arrangement (35)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;679126 - SAP BP: Various corrections for FS address read modules (34)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;677526 - Report RFSBPBUT020 deletes external partner number (33)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;677462 - SAP BP address usages: Start date after end date possible (34)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;677237 - SI: Error when creating an alternative address (34)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;670406 - Incorrect scroll bar in BP address overview (32)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;663280 - SAP BP address usages: Multiple assignment of same address (35)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;652599 - SAP BP address usages: Changes in display mode possible (29)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;652049 - SAP BP:Problems while scrollg in time-dependent addr. usages (29)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;651195 - BP_ADU: Addr. usages lost in loan during partner maintenance (35)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;648464 - BP_ADU: Incorrect read logic in FSBP_DBREAD_BUT021_FS (29)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;647973 - BP_ADU: Address info in addr. usage overview not updated (29)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;643033 - SAP BP ADVW: Description 'SDSD' for address usage XXDEFAULT (29)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;642884 - BP_ADV: Incorrect fields in EDT filled by addr. usage (28)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;642420 - SAP BP: Where-used list for addresses incorrect (29)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;636199 - EDT SAP BP FS address usages: Overlaps possible (27/29)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;634554 - SAP BP: Read module FSBP_DBREAD_BUT021_FS incorrect (26)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;632530 - SAP BP ADVW: Various corrections \"Address usage conflicts\" (32/35)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;629569 - BP_BAP:2 New BAPIs for addresses with time-depend. usages (32)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;623450 - SAP BP: Problems using PS and FS applications in one client (25/26)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;621959 - PAR: Parallel update of address usages: incorrect transfer (25)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;619636 - SAP BP: Various corrections for read modules of SAP BP FS (26)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;617172 - BP_BAP: New BAPIs for addresses with time-depent. usages (25)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;617052 - SAP BP: Various adjustments for report RFSBP21FS (20)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;616625 - SAP BP: Report RFSBPBUT020 not complete (24)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;607007 - SAP BP: Report RFSBPBUT021 does not change table BUT020 (22)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;605368 - BP_ADU: FM FS04_BUPA_MEMORY_GET returns incorrect data (22)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;601008 - BP_ADU: Read logic in FS04_BUPA_MEMORY_GET not correct (20)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;598142 - BP_ADU: Incorrect data transfer from BUT021_FS to BUT021 (20)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;588851 - SAP BP address usages: Various corrections (20/21/22)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;569561 - SAP BP ADVW: Incorrect error because of overlap, short dump (21)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;557372 - BP_ADU: Switch for time-dependent address usage (11/15/16)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br /><br /><br /><br /><br /></p> <b>IV. Notes regarding SAP_APPL 4.70</b><br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note Short text (SP)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;708117 - BP_ZZZ: City name is only output in upper case (20)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;684664 - BP_PAR: Error in parallel maintenance and address conversion (18)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;670034 - BP_ZZZ: Incorrect formatting of addresses (16)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;633843 - BP_ADR: Error in address formatting (form of addr, language) (13/15/16)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;632530 -&#x00A0;&#x00A0;SAP BP ADVW: Various corrections \"Address usage conflicts\" (18)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;588851 - SAP BP address usages: Various corrections (09)<br /> <br /><br /><br /><br /><br /></p> <b>V. Notes for EA_FINSERV 1.10</b><br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note&#x00A0;&#x00A0;&#x00A0;&#x00A0; Short text (SP)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;750491 - BP_ADU: Error while reading address data (19)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;724144 - BP_TR1: Standard indicator not set in table BUT020</p> <UL><UL><LI>You CAN find the relevant corrections in Note 721609 (18)</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;693547 - BP_TR1: Correspondence language for persons is missing<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;691327 - BP_ADU: Various corrections to FS read modules (17)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;689957 - BP_TR2: Correction report adjustment for table ADRC (17)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;684664 - BP_PAR: Error in parallel maintenance and address conversion (17)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;666801 - SAP BP: Error AM053 after RFSBP_CUST_ADDRESS_UPDATE (16)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;656796 - BP_CVI: Bckgrnd maintnnce time-depndnt address usages in CVI (15)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;645872 - BP_TR1: Conversion of form of addr:Corr report for tab. ADRC (14)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;635397 - SAP BP: No change documents for address usage date (27)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;634554 - SAP BP: Read module FSBP_DBREAD_BUT021_FS incorrect (13)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;632530 - SAP BP ADVW: Various corrections \"Address usage conflicts\" (16/17)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;623450 - SAP BP: Problems using PS and FS applications in one client (11)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;619636 - SAP BP: Various corrections for read modules of SAP BP FS (12)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;617414 - SAP BP: Adjustments in report RFTBP030 (11)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;615194 - SAP BP: Transfer of current standard address to customer (10)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;603007 - BP_ADU: E-mail address not found (error FTRO263) (10)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;590392 - BP_TR1: Incorrect report RFTBP030 (convert BP addresses) (08/10)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;588851 - SAP BP address usages: Various corrections (09/10)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;557372 - BP_ADU: Switch for time-dependent address usage (09)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br /><br /><br /><br /><br /></p> <b>VI. Notes for EA_FINSERV 2.0</b><br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note&#x00A0;&#x00A0;&#x00A0;&#x00A0; Short text (SP)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;750491 - BP_ADU: Error while reading address data (08)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;724144 - BP_TR1: Standard indicator not set in table BUT020</p> <UL><UL><LI>You can find the relevant corrections in Note 721609 (07)</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;693547 - BP_TR1: Correspondence language for persons is missing<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;691327 - BP_ADU: Various corrections to FS read modules (04)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;684664 - BP_PAR: Error in parallel maintenance and address conversion (04)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;666801 - SAP BP: Error AM053 after RFSBP_CUST_ADDRESS_UPDATE (02)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;656796 - BP_CVI: Bckgrnd maintnnce time-depndnt address usages in CVI (01)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;645872 - BP_TR1: Conversion of form of addr:Corr report for tab. ADRC (14)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;632530 - SAP BP ADVW: Various corrections \"Address usage conflicts\" (03/04)</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "AP-MD-BP (Business Partner)"}, {"Key": "Other Components", "Value": "TR-TM-TM (Basic Data)"}, {"Key": "Transaction codes", "Value": "BP"}, {"Key": "Transaction codes", "Value": "FS04"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D033094)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D033094)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000517102/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000517102/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000517102/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000517102/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000517102/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000517102/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000517102/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000517102/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000517102/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "767427", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Error R1614 in report RFSBPBUT021", "RefUrl": "/notes/767427"}, {"RefNumber": "766567", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Termination when displaying change documents", "RefUrl": "/notes/766567"}, {"RefNumber": "764550", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: R1612 \"There is no address for business partner &\"", "RefUrl": "/notes/764550"}, {"RefNumber": "755612", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Error in reading address usages", "RefUrl": "/notes/755612"}, {"RefNumber": "750491", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Error while reading address data", "RefUrl": "/notes/750491"}, {"RefNumber": "749316", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Report RFSBPBUT021, error in log", "RefUrl": "/notes/749316"}, {"RefNumber": "747754", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Performance of report RFSBPBUT021", "RefUrl": "/notes/747754"}, {"RefNumber": "738298", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Performance improvement for report RFSBPBUT021", "RefUrl": "/notes/738298"}, {"RefNumber": "729592", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Time-dependent address usages Financial Services", "RefUrl": "/notes/729592"}, {"RefNumber": "727528", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Error in the address uses (BANK/CFM 463_20)", "RefUrl": "/notes/727528"}, {"RefNumber": "725889", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Update termination for report RFSBPBUT021", "RefUrl": "/notes/725889"}, {"RefNumber": "725887", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Error in the address determination", "RefUrl": "/notes/725887"}, {"RefNumber": "724492", "RefComponent": "FS-BP", "RefTitle": "BP_DIA: Correspondence language for SAP business partner", "RefUrl": "/notes/724492"}, {"RefNumber": "724144", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Standard indicator not set in table BUT020", "RefUrl": "/notes/724144"}, {"RefNumber": "724023", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: FSBP_READ_ADDRESS_DATA FM not rfc-enabled", "RefUrl": "/notes/724023"}, {"RefNumber": "721721", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Performance problem with report RFSBPBUT021", "RefUrl": "/notes/721721"}, {"RefNumber": "717945", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Address usages not transferred to loans", "RefUrl": "/notes/717945"}, {"RefNumber": "717833", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: R1 856: \"Error when updating table BUT021\"", "RefUrl": "/notes/717833"}, {"RefNumber": "717831", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Various corrections for report RFSBPBUT021", "RefUrl": "/notes/717831"}, {"RefNumber": "708117", "RefComponent": "FS-BP", "RefTitle": "BP_ZZZ: City name is only output in upper case", "RefUrl": "/notes/708117"}, {"RefNumber": "705841", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Communication data is selected incompletely", "RefUrl": "/notes/705841"}, {"RefNumber": "704768", "RefComponent": "FS-BP", "RefTitle": "GP_ADU: Incorrect traffic lights with WUL for addresses", "RefUrl": "/notes/704768"}, {"RefNumber": "704767", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Address usages not active in some roles", "RefUrl": "/notes/704767"}, {"RefNumber": "703393", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Same usage more then once in the menu tree", "RefUrl": "/notes/703393"}, {"RefNumber": "700524", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: External data transfer of address usages", "RefUrl": "/notes/700524"}, {"RefNumber": "695812", "RefComponent": "FS-BP", "RefTitle": "BP_POT: Display of address types for loans FNVS", "RefUrl": "/notes/695812"}, {"RefNumber": "693547", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Correspondence language for persons is missing", "RefUrl": "/notes/693547"}, {"RefNumber": "691847", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Short dump when creating/changing a loan", "RefUrl": "/notes/691847"}, {"RefNumber": "691327", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Various corrections to FS read modules", "RefUrl": "/notes/691327"}, {"RefNumber": "689957", "RefComponent": "FS-BP", "RefTitle": "BP_TR2: Correction report adjustment for table ADRC", "RefUrl": "/notes/689957"}, {"RefNumber": "686087", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Error when editing a special arrangement", "RefUrl": "/notes/686087"}, {"RefNumber": "684664", "RefComponent": "FS-BP", "RefTitle": "BP_PAR: Error in parallel maintenance and address conversion", "RefUrl": "/notes/684664"}, {"RefNumber": "679126", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Various corrections for FS address read modules", "RefUrl": "/notes/679126"}, {"RefNumber": "677526", "RefComponent": "FS-BP", "RefTitle": "Report RFSBPBUT020 deletes external partner number", "RefUrl": "/notes/677526"}, {"RefNumber": "677462", "RefComponent": "FS-BP", "RefTitle": "SAP BP address usages: Start date after end date possible", "RefUrl": "/notes/677462"}, {"RefNumber": "677237", "RefComponent": "FS-BP", "RefTitle": "SI: Error when creating an alternative address", "RefUrl": "/notes/677237"}, {"RefNumber": "670406", "RefComponent": "FS-BP", "RefTitle": "Incorrect scroll bar in BP address overview", "RefUrl": "/notes/670406"}, {"RefNumber": "670034", "RefComponent": "FS-BP", "RefTitle": "BP_ZZZ: Incorrect formatting of addresses", "RefUrl": "/notes/670034"}, {"RefNumber": "666801", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Error AM053 after RFSBP_CUST_ADDRESS_UPDATE", "RefUrl": "/notes/666801"}, {"RefNumber": "663280", "RefComponent": "FS-BP", "RefTitle": "SAP BP address usages: Multiple assignment of same address", "RefUrl": "/notes/663280"}, {"RefNumber": "656796", "RefComponent": "FS-BP", "RefTitle": "BP_CVI: Bckgrnd maintnnce time-depndnt address usages in CVI", "RefUrl": "/notes/656796"}, {"RefNumber": "652599", "RefComponent": "FS-BP", "RefTitle": "SAP BP address usages: Changes in display mode possible", "RefUrl": "/notes/652599"}, {"RefNumber": "652049", "RefComponent": "FS-BP", "RefTitle": "SAP BP:Problems while scrollg in time-dependent addr.usages", "RefUrl": "/notes/652049"}, {"RefNumber": "651195", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Addr.usages lost in loan during partner maintenance", "RefUrl": "/notes/651195"}, {"RefNumber": "648464", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Incorrect read logic in FSBP_DBREAD_BUT021_FS", "RefUrl": "/notes/648464"}, {"RefNumber": "647973", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Address info in addr.usage overview not updated", "RefUrl": "/notes/647973"}, {"RefNumber": "645872", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Conversion of form of addr:Corr report for tab. ADRC", "RefUrl": "/notes/645872"}, {"RefNumber": "643033", "RefComponent": "FS-BP", "RefTitle": "SAP BP ADVW: Description 'SDSD' for address usage XXDEFAULT", "RefUrl": "/notes/643033"}, {"RefNumber": "642884", "RefComponent": "FS-BP", "RefTitle": "BP_ADV: Incorrect fields in EDT filled by addr.usage", "RefUrl": "/notes/642884"}, {"RefNumber": "642420", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Where-used list for addresses incorrect", "RefUrl": "/notes/642420"}, {"RefNumber": "636199", "RefComponent": "FS-BP", "RefTitle": "EDT SAP BP FS address usages: Overlaps possible", "RefUrl": "/notes/636199"}, {"RefNumber": "635397", "RefComponent": "FS-BP", "RefTitle": "SAP BP: No change documents for address usage date", "RefUrl": "/notes/635397"}, {"RefNumber": "634554", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Read module FSBP_DBREAD_BUT021_FS incorrect", "RefUrl": "/notes/634554"}, {"RefNumber": "633843", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Error in address formatting (form of addr, language)", "RefUrl": "/notes/633843"}, {"RefNumber": "632530", "RefComponent": "FS-BP", "RefTitle": "SAP BP ADVW: Various corrections \"Address usage conflicts\"", "RefUrl": "/notes/632530"}, {"RefNumber": "629569", "RefComponent": "FS-BP", "RefTitle": "BP_BAP:2 New BAPIs for addresses with time-depend. usages", "RefUrl": "/notes/629569"}, {"RefNumber": "623450", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Problems using PS and FS applications in one client", "RefUrl": "/notes/623450"}, {"RefNumber": "621959", "RefComponent": "FS-BP", "RefTitle": "PAR: Parallel update of address usages: incorrect transfer", "RefUrl": "/notes/621959"}, {"RefNumber": "619636", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Various corrections for read modules of SAP BP FS", "RefUrl": "/notes/619636"}, {"RefNumber": "618291", "RefComponent": "FS-BP", "RefTitle": "C_ADR: Redundant update BUT021 from BUT021_FS", "RefUrl": "/notes/618291"}, {"RefNumber": "617414", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Adjustments in report RFTBP030", "RefUrl": "/notes/617414"}, {"RefNumber": "617220", "RefComponent": "FS-BP", "RefTitle": "BP_POT: Address transfer to loan contract", "RefUrl": "/notes/617220"}, {"RefNumber": "617172", "RefComponent": "FS-BP", "RefTitle": "BP_BAP: New BAPIs for addresses with time-depent. usages", "RefUrl": "/notes/617172"}, {"RefNumber": "617052", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Various adjustments for report RFSBP21FS", "RefUrl": "/notes/617052"}, {"RefNumber": "616625", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Report RFSBPBUT020 not complete", "RefUrl": "/notes/616625"}, {"RefNumber": "616044", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Limited use of the BAPIs for SAP FS BPs", "RefUrl": "/notes/616044"}, {"RefNumber": "615194", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Transfer of current standard address to customer", "RefUrl": "/notes/615194"}, {"RefNumber": "615131", "RefComponent": "FS-BP", "RefTitle": "FS BP: c/o address not transferred", "RefUrl": "/notes/615131"}, {"RefNumber": "614663", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Addresses in overview indistinguishable", "RefUrl": "/notes/614663"}, {"RefNumber": "607007", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Report RFSBPBUT021 does not change table BUT020", "RefUrl": "/notes/607007"}, {"RefNumber": "605368", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: FM FS04_BUPA_MEMORY_GET returns incorrect data", "RefUrl": "/notes/605368"}, {"RefNumber": "603007", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: E-mail address not found (error FTRO263)", "RefUrl": "/notes/603007"}, {"RefNumber": "601008", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Read logic in FS04_BUPA_MEMORY_GET not correct", "RefUrl": "/notes/601008"}, {"RefNumber": "598142", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Incorrect data transfer from BUT021_FS to BUT021", "RefUrl": "/notes/598142"}, {"RefNumber": "594871", "RefComponent": "FS-BP", "RefTitle": "BP_BDT: Address usage and \"Company code data\" button", "RefUrl": "/notes/594871"}, {"RefNumber": "591721", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Deletion report for time-dependent address usages", "RefUrl": "/notes/591721"}, {"RefNumber": "590392", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Incorrect report RFTBP030 (convert BP addresses)", "RefUrl": "/notes/590392"}, {"RefNumber": "588916", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Address usage conflicts for business partner", "RefUrl": "/notes/588916"}, {"RefNumber": "588851", "RefComponent": "FS-BP", "RefTitle": "SAP BP address usages: Various corrections", "RefUrl": "/notes/588851"}, {"RefNumber": "581758", "RefComponent": "FS-BP", "RefTitle": "SAP BP ADVW:Buttons active in display mode", "RefUrl": "/notes/581758"}, {"RefNumber": "573969", "RefComponent": "FS-BP", "RefTitle": "BP_CDC: Incorrect generation change docs for address usage", "RefUrl": "/notes/573969"}, {"RefNumber": "572257", "RefComponent": "FS-BP", "RefTitle": "BP_CDC: Short dump change docmts of time-depend.addr.usages", "RefUrl": "/notes/572257"}, {"RefNumber": "569561", "RefComponent": "FS-BP", "RefTitle": "SAP BP ADVW: Incorrect error because of overlap, short dump", "RefUrl": "/notes/569561"}, {"RefNumber": "566432", "RefComponent": "FS-BP", "RefTitle": "SAP BP ADVW: Sorting in tree, overlaps, short dump", "RefUrl": "/notes/566432"}, {"RefNumber": "564976", "RefComponent": "FS-BP", "RefTitle": "SAPBP: Addrs Mangmt: update termin (CML) & delete address", "RefUrl": "/notes/564976"}, {"RefNumber": "564339", "RefComponent": "FS-BP", "RefTitle": "BP_CDC: Validity date is missing in the change documents", "RefUrl": "/notes/564339"}, {"RefNumber": "563077", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Short dump with report RFSBP21FS (memory overflow)", "RefUrl": "/notes/563077"}, {"RefNumber": "560753", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Address read modules find no/incorrect address type", "RefUrl": "/notes/560753"}, {"RefNumber": "559617", "RefComponent": "FS-BP", "RefTitle": "SAP BP address uses:Various corrections", "RefUrl": "/notes/559617"}, {"RefNumber": "557372", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Switch for time-dependent address usage", "RefUrl": "/notes/557372"}, {"RefNumber": "555558", "RefComponent": "FS-BP", "RefTitle": "Address uses: Different corrections", "RefUrl": "/notes/555558"}, {"RefNumber": "555138", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Address uses (dialog and conversion)", "RefUrl": "/notes/555138"}, {"RefNumber": "550854", "RefComponent": "FS-BP", "RefTitle": "SAP BP: SAP + TR BP standard address type in parallel maint.", "RefUrl": "/notes/550854"}, {"RefNumber": "550547", "RefComponent": "FS-BP", "RefTitle": "FS BP:EDT:Termination if internal address number not filled", "RefUrl": "/notes/550547"}, {"RefNumber": "550153", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Address modules always generate default address", "RefUrl": "/notes/550153"}, {"RefNumber": "547299", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Address usages (conversion, parallel maint., dialog)", "RefUrl": "/notes/547299"}, {"RefNumber": "544759", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Conversion/maintenance of address uses (XXDEFAULT)", "RefUrl": "/notes/544759"}, {"RefNumber": "538748", "RefComponent": "FS-BP", "RefTitle": "PAR: Dialog box (missing postal code) when saving SAP BP", "RefUrl": "/notes/538748"}, {"RefNumber": "536636", "RefComponent": "FS-BP", "RefTitle": "BP_XDT: Error FSBP519 with Modify/Delete of address usage", "RefUrl": "/notes/536636"}, {"RefNumber": "536249", "RefComponent": "FS-BP", "RefTitle": "SAP BP: PAR: House number not transferred to TR BP", "RefUrl": "/notes/536249"}, {"RefNumber": "535351", "RefComponent": "FS-BP", "RefTitle": "BP_XDT: MODIFY always uses number of standard address", "RefUrl": "/notes/535351"}, {"RefNumber": "532705", "RefComponent": "FS-BP", "RefTitle": "BP_CDC: Validity date is missing in the change documents", "RefUrl": "/notes/532705"}, {"RefNumber": "532670", "RefComponent": "FS-BP", "RefTitle": "Activating the R/3 Enterprise Extension EA-FS", "RefUrl": "/notes/532670"}, {"RefNumber": "532246", "RefComponent": "FS-BP", "RefTitle": "BP_XDT: Missing address number for the EDT address usages", "RefUrl": "/notes/532246"}, {"RefNumber": "532191", "RefComponent": "FS-BP", "RefTitle": "BP_XDT: Error in DI of the time-dependent address uses", "RefUrl": "/notes/532191"}, {"RefNumber": "531424", "RefComponent": "FS-BP", "RefTitle": "BP_XDT:Incorrect Modify of the address usages for DI", "RefUrl": "/notes/531424"}, {"RefNumber": "513762", "RefComponent": "FS-BP", "RefTitle": "PAR:Standard address transferred incorrectly into BP030", "RefUrl": "/notes/513762"}, {"RefNumber": "505977", "RefComponent": "FS-BP", "RefTitle": "SAP BP conversion Customizing: View V_TP14 and standard flag", "RefUrl": "/notes/505977"}, {"RefNumber": "504734", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Time-dependent address usages of SAP BP for FS", "RefUrl": "/notes/504734"}, {"RefNumber": "398888", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Composite note and FAQs about bus partner conversion", "RefUrl": "/notes/398888"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "513762", "RefComponent": "FS-BP", "RefTitle": "PAR:Standard address transferred incorrectly into BP030", "RefUrl": "/notes/513762 "}, {"RefNumber": "505977", "RefComponent": "FS-BP", "RefTitle": "SAP BP conversion Customizing: View V_TP14 and standard flag", "RefUrl": "/notes/505977 "}, {"RefNumber": "398888", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Composite note and FAQs about bus partner conversion", "RefUrl": "/notes/398888 "}, {"RefNumber": "693547", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Correspondence language for persons is missing", "RefUrl": "/notes/693547 "}, {"RefNumber": "594871", "RefComponent": "FS-BP", "RefTitle": "BP_BDT: Address usage and \"Company code data\" button", "RefUrl": "/notes/594871 "}, {"RefNumber": "614663", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Addresses in overview indistinguishable", "RefUrl": "/notes/614663 "}, {"RefNumber": "724023", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: FSBP_READ_ADDRESS_DATA FM not rfc-enabled", "RefUrl": "/notes/724023 "}, {"RefNumber": "616044", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Limited use of the BAPIs for SAP FS BPs", "RefUrl": "/notes/616044 "}, {"RefNumber": "677237", "RefComponent": "FS-BP", "RefTitle": "SI: Error when creating an alternative address", "RefUrl": "/notes/677237 "}, {"RefNumber": "764550", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: R1612 \"There is no address for business partner &\"", "RefUrl": "/notes/764550 "}, {"RefNumber": "550854", "RefComponent": "FS-BP", "RefTitle": "SAP BP: SAP + TR BP standard address type in parallel maint.", "RefUrl": "/notes/550854 "}, {"RefNumber": "642420", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Where-used list for addresses incorrect", "RefUrl": "/notes/642420 "}, {"RefNumber": "717833", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: R1 856: \"Error when updating table BUT021\"", "RefUrl": "/notes/717833 "}, {"RefNumber": "704767", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Address usages not active in some roles", "RefUrl": "/notes/704767 "}, {"RefNumber": "619636", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Various corrections for read modules of SAP BP FS", "RefUrl": "/notes/619636 "}, {"RefNumber": "755612", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Error in reading address usages", "RefUrl": "/notes/755612 "}, {"RefNumber": "767427", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Error R1614 in report RFSBPBUT021", "RefUrl": "/notes/767427 "}, {"RefNumber": "623450", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Problems using PS and FS applications in one client", "RefUrl": "/notes/623450 "}, {"RefNumber": "766567", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Termination when displaying change documents", "RefUrl": "/notes/766567 "}, {"RefNumber": "729592", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Time-dependent address usages Financial Services", "RefUrl": "/notes/729592 "}, {"RefNumber": "749316", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Report RFSBPBUT021, error in log", "RefUrl": "/notes/749316 "}, {"RefNumber": "750491", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Error while reading address data", "RefUrl": "/notes/750491 "}, {"RefNumber": "747754", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Performance of report RFSBPBUT021", "RefUrl": "/notes/747754 "}, {"RefNumber": "738298", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Performance improvement for report RFSBPBUT021", "RefUrl": "/notes/738298 "}, {"RefNumber": "724492", "RefComponent": "FS-BP", "RefTitle": "BP_DIA: Correspondence language for SAP business partner", "RefUrl": "/notes/724492 "}, {"RefNumber": "588916", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Address usage conflicts for business partner", "RefUrl": "/notes/588916 "}, {"RefNumber": "717831", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Various corrections for report RFSBPBUT021", "RefUrl": "/notes/717831 "}, {"RefNumber": "504734", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Time-dependent address usages of SAP BP for FS", "RefUrl": "/notes/504734 "}, {"RefNumber": "727528", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Error in the address uses (BANK/CFM 463_20)", "RefUrl": "/notes/727528 "}, {"RefNumber": "691327", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Various corrections to FS read modules", "RefUrl": "/notes/691327 "}, {"RefNumber": "686087", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Error when editing a special arrangement", "RefUrl": "/notes/686087 "}, {"RefNumber": "536249", "RefComponent": "FS-BP", "RefTitle": "SAP BP: PAR: House number not transferred to TR BP", "RefUrl": "/notes/536249 "}, {"RefNumber": "725889", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Update termination for report RFSBPBUT021", "RefUrl": "/notes/725889 "}, {"RefNumber": "569561", "RefComponent": "FS-BP", "RefTitle": "SAP BP ADVW: Incorrect error because of overlap, short dump", "RefUrl": "/notes/569561 "}, {"RefNumber": "708117", "RefComponent": "FS-BP", "RefTitle": "BP_ZZZ: City name is only output in upper case", "RefUrl": "/notes/708117 "}, {"RefNumber": "684664", "RefComponent": "FS-BP", "RefTitle": "BP_PAR: Error in parallel maintenance and address conversion", "RefUrl": "/notes/684664 "}, {"RefNumber": "679126", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Various corrections for FS address read modules", "RefUrl": "/notes/679126 "}, {"RefNumber": "677526", "RefComponent": "FS-BP", "RefTitle": "Report RFSBPBUT020 deletes external partner number", "RefUrl": "/notes/677526 "}, {"RefNumber": "677462", "RefComponent": "FS-BP", "RefTitle": "SAP BP address usages: Start date after end date possible", "RefUrl": "/notes/677462 "}, {"RefNumber": "670406", "RefComponent": "FS-BP", "RefTitle": "Incorrect scroll bar in BP address overview", "RefUrl": "/notes/670406 "}, {"RefNumber": "670034", "RefComponent": "FS-BP", "RefTitle": "BP_ZZZ: Incorrect formatting of addresses", "RefUrl": "/notes/670034 "}, {"RefNumber": "666801", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Error AM053 after RFSBP_CUST_ADDRESS_UPDATE", "RefUrl": "/notes/666801 "}, {"RefNumber": "663280", "RefComponent": "FS-BP", "RefTitle": "SAP BP address usages: Multiple assignment of same address", "RefUrl": "/notes/663280 "}, {"RefNumber": "652599", "RefComponent": "FS-BP", "RefTitle": "SAP BP address usages: Changes in display mode possible", "RefUrl": "/notes/652599 "}, {"RefNumber": "652049", "RefComponent": "FS-BP", "RefTitle": "SAP BP:Problems while scrollg in time-dependent addr.usages", "RefUrl": "/notes/652049 "}, {"RefNumber": "651195", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Addr.usages lost in loan during partner maintenance", "RefUrl": "/notes/651195 "}, {"RefNumber": "648464", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Incorrect read logic in FSBP_DBREAD_BUT021_FS", "RefUrl": "/notes/648464 "}, {"RefNumber": "647973", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Address info in addr.usage overview not updated", "RefUrl": "/notes/647973 "}, {"RefNumber": "645872", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Conversion of form of addr:Corr report for tab. ADRC", "RefUrl": "/notes/645872 "}, {"RefNumber": "643033", "RefComponent": "FS-BP", "RefTitle": "SAP BP ADVW: Description 'SDSD' for address usage XXDEFAULT", "RefUrl": "/notes/643033 "}, {"RefNumber": "642884", "RefComponent": "FS-BP", "RefTitle": "BP_ADV: Incorrect fields in EDT filled by addr.usage", "RefUrl": "/notes/642884 "}, {"RefNumber": "633843", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Error in address formatting (form of addr, language)", "RefUrl": "/notes/633843 "}, {"RefNumber": "636199", "RefComponent": "FS-BP", "RefTitle": "EDT SAP BP FS address usages: Overlaps possible", "RefUrl": "/notes/636199 "}, {"RefNumber": "635397", "RefComponent": "FS-BP", "RefTitle": "SAP BP: No change documents for address usage date", "RefUrl": "/notes/635397 "}, {"RefNumber": "634554", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Read module FSBP_DBREAD_BUT021_FS incorrect", "RefUrl": "/notes/634554 "}, {"RefNumber": "632530", "RefComponent": "FS-BP", "RefTitle": "SAP BP ADVW: Various corrections \"Address usage conflicts\"", "RefUrl": "/notes/632530 "}, {"RefNumber": "621959", "RefComponent": "FS-BP", "RefTitle": "PAR: Parallel update of address usages: incorrect transfer", "RefUrl": "/notes/621959 "}, {"RefNumber": "618291", "RefComponent": "FS-BP", "RefTitle": "C_ADR: Redundant update BUT021 from BUT021_FS", "RefUrl": "/notes/618291 "}, {"RefNumber": "617414", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Adjustments in report RFTBP030", "RefUrl": "/notes/617414 "}, {"RefNumber": "700524", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: External data transfer of address usages", "RefUrl": "/notes/700524 "}, {"RefNumber": "617052", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Various adjustments for report RFSBP21FS", "RefUrl": "/notes/617052 "}, {"RefNumber": "557372", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Switch for time-dependent address usage", "RefUrl": "/notes/557372 "}, {"RefNumber": "588851", "RefComponent": "FS-BP", "RefTitle": "SAP BP address usages: Various corrections", "RefUrl": "/notes/588851 "}, {"RefNumber": "616625", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Report RFSBPBUT020 not complete", "RefUrl": "/notes/616625 "}, {"RefNumber": "615194", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Transfer of current standard address to customer", "RefUrl": "/notes/615194 "}, {"RefNumber": "615131", "RefComponent": "FS-BP", "RefTitle": "FS BP: c/o address not transferred", "RefUrl": "/notes/615131 "}, {"RefNumber": "607007", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Report RFSBPBUT021 does not change table BUT020", "RefUrl": "/notes/607007 "}, {"RefNumber": "605368", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: FM FS04_BUPA_MEMORY_GET returns incorrect data", "RefUrl": "/notes/605368 "}, {"RefNumber": "603007", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: E-mail address not found (error FTRO263)", "RefUrl": "/notes/603007 "}, {"RefNumber": "601008", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Read logic in FS04_BUPA_MEMORY_GET not correct", "RefUrl": "/notes/601008 "}, {"RefNumber": "598142", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Incorrect data transfer from BUT021_FS to BUT021", "RefUrl": "/notes/598142 "}, {"RefNumber": "591721", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Deletion report for time-dependent address usages", "RefUrl": "/notes/591721 "}, {"RefNumber": "590392", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Incorrect report RFTBP030 (convert BP addresses)", "RefUrl": "/notes/590392 "}, {"RefNumber": "532670", "RefComponent": "FS-BP", "RefTitle": "Activating the R/3 Enterprise Extension EA-FS", "RefUrl": "/notes/532670 "}, {"RefNumber": "725887", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Error in the address determination", "RefUrl": "/notes/725887 "}, {"RefNumber": "724144", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Standard indicator not set in table BUT020", "RefUrl": "/notes/724144 "}, {"RefNumber": "689957", "RefComponent": "FS-BP", "RefTitle": "BP_TR2: Correction report adjustment for table ADRC", "RefUrl": "/notes/689957 "}, {"RefNumber": "721721", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Performance problem with report RFSBPBUT021", "RefUrl": "/notes/721721 "}, {"RefNumber": "717945", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Address usages not transferred to loans", "RefUrl": "/notes/717945 "}, {"RefNumber": "532246", "RefComponent": "FS-BP", "RefTitle": "BP_XDT: Missing address number for the EDT address usages", "RefUrl": "/notes/532246 "}, {"RefNumber": "531424", "RefComponent": "FS-BP", "RefTitle": "BP_XDT:Incorrect Modify of the address usages for DI", "RefUrl": "/notes/531424 "}, {"RefNumber": "535351", "RefComponent": "FS-BP", "RefTitle": "BP_XDT: MODIFY always uses number of standard address", "RefUrl": "/notes/535351 "}, {"RefNumber": "572257", "RefComponent": "FS-BP", "RefTitle": "BP_CDC: Short dump change docmts of time-depend.addr.usages", "RefUrl": "/notes/572257 "}, {"RefNumber": "573969", "RefComponent": "FS-BP", "RefTitle": "BP_CDC: Incorrect generation change docs for address usage", "RefUrl": "/notes/573969 "}, {"RefNumber": "532705", "RefComponent": "FS-BP", "RefTitle": "BP_CDC: Validity date is missing in the change documents", "RefUrl": "/notes/532705 "}, {"RefNumber": "564339", "RefComponent": "FS-BP", "RefTitle": "BP_CDC: Validity date is missing in the change documents", "RefUrl": "/notes/564339 "}, {"RefNumber": "536636", "RefComponent": "FS-BP", "RefTitle": "BP_XDT: Error FSBP519 with Modify/Delete of address usage", "RefUrl": "/notes/536636 "}, {"RefNumber": "532191", "RefComponent": "FS-BP", "RefTitle": "BP_XDT: Error in DI of the time-dependent address uses", "RefUrl": "/notes/532191 "}, {"RefNumber": "550547", "RefComponent": "FS-BP", "RefTitle": "FS BP:EDT:Termination if internal address number not filled", "RefUrl": "/notes/550547 "}, {"RefNumber": "704768", "RefComponent": "FS-BP", "RefTitle": "GP_ADU: Incorrect traffic lights with WUL for addresses", "RefUrl": "/notes/704768 "}, {"RefNumber": "705841", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Communication data is selected incompletely", "RefUrl": "/notes/705841 "}, {"RefNumber": "703393", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Same usage more then once in the menu tree", "RefUrl": "/notes/703393 "}, {"RefNumber": "629569", "RefComponent": "FS-BP", "RefTitle": "BP_BAP:2 New BAPIs for addresses with time-depend. usages", "RefUrl": "/notes/629569 "}, {"RefNumber": "695812", "RefComponent": "FS-BP", "RefTitle": "BP_POT: Display of address types for loans FNVS", "RefUrl": "/notes/695812 "}, {"RefNumber": "691847", "RefComponent": "FS-BP", "RefTitle": "BP_ADU: Short dump when creating/changing a loan", "RefUrl": "/notes/691847 "}, {"RefNumber": "617220", "RefComponent": "FS-BP", "RefTitle": "BP_POT: Address transfer to loan contract", "RefUrl": "/notes/617220 "}, {"RefNumber": "617172", "RefComponent": "FS-BP", "RefTitle": "BP_BAP: New BAPIs for addresses with time-depent. usages", "RefUrl": "/notes/617172 "}, {"RefNumber": "656796", "RefComponent": "FS-BP", "RefTitle": "BP_CVI: Bckgrnd maintnnce time-depndnt address usages in CVI", "RefUrl": "/notes/656796 "}, {"RefNumber": "547299", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Address usages (conversion, parallel maint., dialog)", "RefUrl": "/notes/547299 "}, {"RefNumber": "581758", "RefComponent": "FS-BP", "RefTitle": "SAP BP ADVW:Buttons active in display mode", "RefUrl": "/notes/581758 "}, {"RefNumber": "563077", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Short dump with report RFSBP21FS (memory overflow)", "RefUrl": "/notes/563077 "}, {"RefNumber": "559617", "RefComponent": "FS-BP", "RefTitle": "SAP BP address uses:Various corrections", "RefUrl": "/notes/559617 "}, {"RefNumber": "538748", "RefComponent": "FS-BP", "RefTitle": "PAR: Dialog box (missing postal code) when saving SAP BP", "RefUrl": "/notes/538748 "}, {"RefNumber": "566432", "RefComponent": "FS-BP", "RefTitle": "SAP BP ADVW: Sorting in tree, overlaps, short dump", "RefUrl": "/notes/566432 "}, {"RefNumber": "564976", "RefComponent": "FS-BP", "RefTitle": "SAPBP: Addrs Mangmt: update termin (CML) & delete address", "RefUrl": "/notes/564976 "}, {"RefNumber": "544759", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Conversion/maintenance of address uses (XXDEFAULT)", "RefUrl": "/notes/544759 "}, {"RefNumber": "550153", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Address modules always generate default address", "RefUrl": "/notes/550153 "}, {"RefNumber": "555558", "RefComponent": "FS-BP", "RefTitle": "Address uses: Different corrections", "RefUrl": "/notes/555558 "}, {"RefNumber": "560753", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Address read modules find no/incorrect address type", "RefUrl": "/notes/560753 "}, {"RefNumber": "555138", "RefComponent": "FS-BP", "RefTitle": "SAP BP: Address uses (dialog and conversion)", "RefUrl": "/notes/555138 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_ABA", "From": "50A", "To": "50A", "Subsequent": ""}, {"SoftwareComponent": "SAP_ABA", "From": "620", "To": "620", "Subsequent": ""}, {"SoftwareComponent": "BANK/CFM", "From": "463_20", "To": "463_20", "Subsequent": ""}, {"SoftwareComponent": "BANK-TRBK", "From": "20", "To": "20", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_ABA 50A", "SupportPackage": "SAPKA50A18", "URL": "/supportpackage/SAPKA50A18"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}