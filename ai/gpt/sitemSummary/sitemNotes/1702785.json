{"Request": {"Number": "1702785", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 242, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017415482017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001702785?language=E&token=57D325B147D6007344A44F4CE5143675"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001702785", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001702785/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1702785"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.07.2021"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CST-IC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Internet Communication Manager"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Client/Server Technology", "value": "BC-CST", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CST*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Internet Communication Manager", "value": "BC-CST-IC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CST-IC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1702785 - Error diagnosis for SMTP using TLS and SMTP authentication"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note describes errors that occur when using SMTP via TLS and SMTP authentication and offers solution options.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>When you receive mails, the system issues one of the following error messages in ICM Trace (dev_icm) or in ICM Security Trace (dev_icm_sec):</p>\r\n<ul>\r\n<li>Error message: SMTP PLAIN authentication failed for user ... from ..., rc ...: ...</li>\r\n</ul>\r\n<p>The logon for the specified user failed. SAP Note 320991 contains a list of the possible errors. Ensure that the user exists in the client 000 and has the user type SYSTEM. Ensure that the SMTP client uses the correct password. Up to a certain version level, it was possible to use user names only in capital letters (see SAP Note 3060334).</p>\r\n<ul>\r\n<li>Error message: SMTP PLAIN authentication failed for user ... from ...: user not in SMTP auth user list (AUTHUSERS)<br />or<br />SMTP EXTERNAL authentication failed for user ... from ...: user not in SMTP auth user list (AUTHUSERS)</li>\r\n</ul>\r\n<p>For the authentication with SMTP Auth, a user was specified that is not part of the user list AUTHUSERS. AUTHUSERS is an attribute of the ICM SMTP port (Parameter icm/server_port_&lt; n &gt;). If the user is to be allowed for the SMTP authentication, you must add it to the user list in the attribute AUTHUSERS of the icm/server_port parameter.</p>\r\n<ul>\r\n<li>Error message: SMTP EXTERNAL authentication failed: no client cert available from ...</li>\r\n</ul>\r\n<p>The SMTP EXTERNAL authentication is to be used. However, the ICM (as server) has not received a valid client certificate of the SMTP client. Ensure that the client has a valid client certificate. Ensure that the server PSE accepts the client certificate as trusted. Ensure that the ICM queries the client certificate (attribute VCLIENT=1 or VCLIENT=2 am icm/server_port_&lt; n &gt; parameter).</p>\r\n<ul>\r\n<li>Error message: SMTP EXTERNAL authentication failed for cert \"...\" from ..., rc ...:</li>\r\n</ul>\r\n<p>The logon for the specified certificate failed. SAP Note 320991 contains a list of the possible errors. Ensure that the certificate is assigned to a user in the client 000 and has the user type SYSTEM.</p>\r\n<ul>\r\n<li>Error message: SMTP server TLS usage is mandatory, but no STARTTLS received, command ... from ...</li>\r\n</ul>\r\n<p>The use of TLS is required because the attribute TLS=2 am icm/server_port_&lt; n &gt; parameter is set. An SMTP client uses SMTP without having switched to using TLS. Ensure that the SMTP client supports the use of TLS. Ensure that the SMTP client accepts the server certificate of the ICM as trusted (or does not carry out a check). Alternatively, you can configure the ICM in such a way that the use of TLS is deactivated or optional (attribute TLS=0 or TLS=1 of the parameter icm/server_port_&lt; n &gt;).</p>\r\n<ul>\r\n<li>Error message: SMTP server authentication is mandatory, but not done, command ... from ...</li>\r\n</ul>\r\n<p>The ICM is configured for the use of SMTP (attribute AUTHMECHANISMS of the parameter icm/server_port_&lt; n &gt;). The SMTP client has tried to use SMTP without previous authentication. Ensure that the SMTP client has the required credentials (user and password or client certificate) for the SMTP authentication. Alternatively, you can configure the ICM in such a way that the SMTP authentication is not required (omission of the attribute AUTHMECHANISMS of the parameter icm/server_port_&lt; n &gt;).</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-SRV-COM (Communication Services: Mail, Fax, SMS, Telephony)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D021676)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D021676)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001702785/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001702785/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001702785/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001702785/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001702785/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001702785/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001702785/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001702785/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001702785/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "320991", "RefComponent": "BC-SEC-LGN", "RefTitle": "Error codes during logon (list)", "RefUrl": "/notes/320991"}, {"RefNumber": "3060334", "RefComponent": "BC-SEC-LGN", "RefTitle": "SMTP inbound authentication fails for AUTHUSERS which are not exclusively in upper case", "RefUrl": "/notes/3060334"}, {"RefNumber": "1747180", "RefComponent": "BC-CST-IC", "RefTitle": "SMTP via TLS and SMTP authentication", "RefUrl": "/notes/1747180"}, {"RefNumber": "1724704", "RefComponent": "BC-SRV-COM", "RefTitle": "SCOT: Settings for TLS and SMTP AUTH", "RefUrl": "/notes/1724704"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2718594", "RefComponent": "BC-SRV-COM", "RefTitle": "\"MTA Open Mail Relaying Allowed\" message for SMTP port in SAP system", "RefUrl": "/notes/2718594 "}, {"RefNumber": "2700110", "RefComponent": "BC-SRV-COM", "RefTitle": "Error code XS751 with STARTTLS occurs in transaction SOST during mail sending", "RefUrl": "/notes/2700110 "}, {"RefNumber": "2690360", "RefComponent": "BC-SRV-COM", "RefTitle": "XS812 - 550 5.7.60 SMTP; Client does not have permissions to send as this sender", "RefUrl": "/notes/2690360 "}, {"RefNumber": "2439601", "RefComponent": "BC-SRV-COM", "RefTitle": "Password length limited to 20 characters - SCOT", "RefUrl": "/notes/2439601 "}, {"RefNumber": "3060334", "RefComponent": "BC-SEC-LGN", "RefTitle": "SMTP inbound authentication fails for AUTHUSERS which are not exclusively in upper case", "RefUrl": "/notes/3060334 "}, {"RefNumber": "320991", "RefComponent": "BC-SEC-LGN", "RefTitle": "Error codes during logon (list)", "RefUrl": "/notes/320991 "}, {"RefNumber": "1747180", "RefComponent": "BC-CST-IC", "RefTitle": "SMTP via TLS and SMTP authentication", "RefUrl": "/notes/1747180 "}, {"RefNumber": "1724704", "RefComponent": "BC-SRV-COM", "RefTitle": "SCOT: Settings for TLS and SMTP AUTH", "RefUrl": "/notes/1724704 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}