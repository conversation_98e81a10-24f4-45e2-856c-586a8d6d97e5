{"Request": {"Number": "1300732", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 389, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000007641772017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001300732?language=E&token=56CFA5CB5801137611194E7A9C35458A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001300732", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001300732/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1300732"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "29.06.2010"}, "SAPComponentKey": {"_label": "Component", "value": "FS-CML"}, "SAPComponentKeyText": {"_label": "Component", "value": "Consumer and Mortgage Loans"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Services", "value": "FS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Consumer and Mortgage Loans", "value": "FS-CML", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS-CML*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1300732 - CML: UI Enhancement with respect to Table Controls"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Since some of the users have shifted to larger user screens there is a need felt among the end users to enhance some of the user interfaces for take advantage of new screen size.<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>FS-CML,UI Enhancement.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>User Interface of CML was originally developed keeping the traditional user screens of the end user which are comparatively smaller than the screen for development. Since some of the users have shifted to larger user screens there is a need felt among the end users to enhance some of the user interfaces for take advantage of new screen size.<br />As an outcome of the analysis of the most frequented transactions among the end users, this specification addresses the areas wherein these enhancements shall be addressed which will impact the user experience positively.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The size of the specified table controls have been enhanced vertically and horizontally to take advantage of the bigger screens size.<br /><br />The transport files can be found as attachement to the note.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Release's validity for upgrade customers checked?", "Value": "checked"}, {"Key": "Modification", "Value": "the note contains a modification"}, {"Key": "Responsible                                                                                         ", "Value": "D027488"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D031221)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001300732/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001300732/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001300732/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001300732/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001300732/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001300732/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001300732/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001300732/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001300732/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "1300732_incl_1397036.zip", "FileSize": "1595", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000107452009&iv_version=0004&iv_guid=D942BED52C987942987F3FA4D89E917C"}, {"FileName": "transport_files.zip", "FileSize": "715", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000107452009&iv_version=0004&iv_guid=DC6F09837FE4664C9B752F44B6AA05FA"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1890930", "RefComponent": "FS-CML-NB-CD", "RefTitle": "STM: Increased table control for conditions and partners", "RefUrl": "/notes/1890930"}, {"RefNumber": "1883893", "RefComponent": "FS-CML-NB-CD", "RefTitle": "STM: Screen for object data has been broadened (in standard system)", "RefUrl": "/notes/1883893"}, {"RefNumber": "1617826", "RefComponent": "FS-CML-PO-WBO", "RefTitle": "Workplace: Various screen adjustments", "RefUrl": "/notes/1617826"}, {"RefNumber": "1411731", "RefComponent": "FS-CML", "RefTitle": "CML: Adjustments for UI Enhancement", "RefUrl": "/notes/1411731"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2716306", "RefComponent": "FS-CML", "RefTitle": "WBO: Screen size for display of open items or waivers and write-offs", "RefUrl": "/notes/2716306 "}, {"RefNumber": "1994601", "RefComponent": "FS-CML", "RefTitle": "STM: Display of object data - additional lines", "RefUrl": "/notes/1994601 "}, {"RefNumber": "1890930", "RefComponent": "FS-CML-NB-CD", "RefTitle": "STM: Increased table control for conditions and partners", "RefUrl": "/notes/1890930 "}, {"RefNumber": "1883893", "RefComponent": "FS-CML-NB-CD", "RefTitle": "STM: Screen for object data has been broadened (in standard system)", "RefUrl": "/notes/1883893 "}, {"RefNumber": "1617826", "RefComponent": "FS-CML-PO-WBO", "RefTitle": "Workplace: Various screen adjustments", "RefUrl": "/notes/1617826 "}, {"RefNumber": "1411731", "RefComponent": "FS-CML", "RefTitle": "CML: Adjustments for UI Enhancement", "RefUrl": "/notes/1411731 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-FINSERV", "From": "600", "To": "600", "Subsequent": "X"}, {"SoftwareComponent": "EA-FINSERV", "From": "603", "To": "603", "Subsequent": "X"}, {"SoftwareComponent": "EA-FINSERV", "From": "604", "To": "604", "Subsequent": "X"}, {"SoftwareComponent": "EA-FINSERV", "From": "605", "To": "605", "Subsequent": "X"}, {"SoftwareComponent": "EA-FINSERV", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "616", "To": "616", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "EA-FINSERV", "NumberOfCorrin": 1, "URL": "/corrins/0001300732/201"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1411731", "RefTitle": "CML: Adjustments for UI Enhancement", "RefUrl": "/notes/0001411731"}]}}}}}