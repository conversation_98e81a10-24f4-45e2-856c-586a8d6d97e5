{"Request": {"Number": "883111", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 223, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015956182017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000883111?language=E&token=66D7D75ECF78D6104848BBDB5A38FA9E"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000883111", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000883111/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "883111"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.11.2005"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SDD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Service Data Download"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Data Download", "value": "SV-SMG-SDD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SDD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "883111 - Deactivating old EarlyWatchAlert (Transaction SCUI)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The first versions of the EarlyWatchAlert were delivered to SAP customers between 1993 and 2000. The administration transaction for this \"old\" EarlyWatchAlert is called SCUI. Over time, Transaction SCUI was replaced with Transaction SDCC, or since 2005 with Transaction SDCCN. However, sometimes customers still have the old EarlyWatchAlert (Transaction SCUI) activated in many SAP systems. This particularly concerns SAP Releases 3.1*, 4.0*, 4.5*, 4.6*.<br /><br />In concrete terms, this means that in the worst case scenario, the old EarlyWatchAlert makes an attempt every minute to establish a link from the customer system to SAP (in OSS - SAPNet R/3 frontend) to transfer the statistical performance data. Obviously, this does not work, since the mechanism for the old EarlyWatchAlert was deactivated several years ago at SAP. The old EarlyWatchAlert mechanism (Transaction SCUI) was replaced with the new EarlyWatchAlert mechanism (Transaction SDCCN).<br /><br />See below for a description of how to deactivate this old EarlyWatchAlert (Transaction SCUI) in your system.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SAPOSS, EarlyWatchAlert, SCUI, SDCC, SDCCN, EWA, TCC, SAPNET_RTCC, SAPNET_RFC, SCUP_TRANS_CUS_DATA_100, OSS_RFC, SCUICOLL, SCUISEND,</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>By oversight, the old EarlyWatchAlert mechanism was not deactivated in Releases 3.1*, 4.0*, 4.5*, 4.6*. There is no documentation about this.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>To solve the problem, proceed as follows:<br /><br />Log onto each SAP system as an SAP customer (the SAP releases mentioned in this note =&gt; 3,1*, 4.0*, 4.5*, 4.6*).<br />If you do not do this, your system will probably regularly try to establish a connection to SAP (which can also mean incurring connection costs or telephone costs).<br /><br />Start Transaction SCUI.<br />If the \"Transactional RFC\" pushbutton is on the screen of Transaction SCUI, click on the pushbutton. If this pushbutton does not exist, call SM58 and proceed as described further on in the note.<br /><br />Note: Whether the \"Transactional RFC\" pushbutton is in your SAP system within Transaction SCUI depends on the Support Package imported.<br /><br />If the \"Transactional RFC\" pushbutton exists, choose it. In the list that now appears, find the user under which the old EWA is running. The user is in the first column of the list, therefore on the left, in the first column.<br /><br />Now log off the system again, and log back onto the system with the user that you saw previously in the list (under which the old EarlyWatchAlert is running).<br />Start Transaction SCUI again, and choose \"Transactional RFC\".<br />In the list that now appears, select each individual line (for the old EarlyWatchAlert) and click the \"Delete\" pushbutton until all the entries of the list (for the old EarlyWatchAlert) are deleted.<br /><br />Then, in Transaction SCUI, go to the menu and select \"GOTO\" and \"QOUT scheduler\".<br /><br />In the list that now appears, delete the entry for \"SAPOSS\", \"SAPNET_RFC\", \"SAPNET_RTCC\", or \"SCUP_TRANS_CUS_DATA_100\" if they exist.<br />This deletion procedure does not delete the RFC connection to SAP but simply the entry from the list.<br /><br /><br />If the pushbutton \"Transactional RFC\" does not exist in Transaction SCUI  or under the menu point \"GoTo\" and \"QOUT Scheduler\", start Transaction SM58 instead. For the display period, enter the value \"01.01.1900\" for the start date and the value \"31.12.9999\" for the end date, enter an asterisk \"*\" for the user name and leave all other default values, then choose execute (F8). In the list that now appears, delete all entries that belong to the old EarlyWatchAlert.<br />These can be recognized, amongst other things, by the fact that an error such as \"SCUP_TRANS_CUS_DATA_100\" appears in the status text column.<br /><br />Whichever way you proceed, you should also look at SM37.<br />Start Transaction SM37 and see whether background jobs with the *SCUI* naming convention are scheduled and are running. If YES, first set this to SCHEDULED and then also delete the scheduled *SCUI* jobs.<br /><br />You should now have successfully deallocated and deactivated the old EarlyWatchAlert.<br /><br />The procedure described here for deactivating the old EarlyWatchAlert has no effect whatsoever on the new EarlyWatchAlert which runs on Transaction (SDCC or) SDCCN.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-BO (Backoffice Service Delivery)"}, {"Key": "Other Components", "Value": "SV-SMG (SAP Solution Manager)"}, {"Key": "Other Components", "Value": "SV-SMG-SER-EWA (EarlyWatch Alert)"}, {"Key": "Other Components", "Value": "XX-SER-TCC-EW (Please use component node SV-BO*)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D001690)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D025781)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000883111/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000883111/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000883111/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000883111/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000883111/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000883111/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000883111/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000883111/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000883111/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "792941", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/792941"}, {"RefNumber": "786918", "RefComponent": "SV-SMG-OP", "RefTitle": "SDCCN administration tool uses wrong RFC connection", "RefUrl": "/notes/786918"}, {"RefNumber": "766505", "RefComponent": "XX-SER-NET", "RefTitle": "OSS1: Changes to RFC connection SAPOSS", "RefUrl": "/notes/766505"}, {"RefNumber": "207223", "RefComponent": "SV-SMG-SDD", "RefTitle": "SAP EarlyWatch Alert Processed at SAP", "RefUrl": "/notes/207223"}, {"RefNumber": "1784032", "RefComponent": "SV-SMG-SDD", "RefTitle": "Extension of EWA data by EWM data", "RefUrl": "/notes/1784032"}, {"RefNumber": "727998", "RefComponent": "SV-SMG-SDD", "RefTitle": "Complete Replacement of Service Definitions of the Service Data Control Center", "RefUrl": "/notes/727998"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1784032", "RefComponent": "SV-SMG-SDD", "RefTitle": "Extension of EWA data by EWM data", "RefUrl": "/notes/1784032 "}, {"RefNumber": "207223", "RefComponent": "SV-SMG-SDD", "RefTitle": "SAP EarlyWatch Alert Processed at SAP", "RefUrl": "/notes/207223 "}, {"RefNumber": "727998", "RefComponent": "SV-SMG-SDD", "RefTitle": "Complete Replacement of Service Definitions of the Service Data Control Center", "RefUrl": "/notes/727998 "}, {"RefNumber": "766505", "RefComponent": "XX-SER-NET", "RefTitle": "OSS1: Changes to RFC connection SAPOSS", "RefUrl": "/notes/766505 "}, {"RefNumber": "786918", "RefComponent": "SV-SMG-OP", "RefTitle": "SDCCN administration tool uses wrong RFC connection", "RefUrl": "/notes/786918 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}