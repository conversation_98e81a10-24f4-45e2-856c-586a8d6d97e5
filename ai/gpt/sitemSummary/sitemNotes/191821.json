{"Request": {"Number": "191821", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 373, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014758602017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000191821?language=E&token=147C7D5D4FD35C9CF6B6BB77A3096685"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000191821", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000191821/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "191821"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.03.2000"}, "SAPComponentKey": {"_label": "Component", "value": "TR-CB"}, "SAPComponentKeyText": {"_label": "Component", "value": "Cash Budget Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Treasury", "value": "TR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'TR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Cash Budget Management", "value": "TR-CB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'TR-CB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "191821 - Cash Budget Management in Release 4.6"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You use Cash Budget Management.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>FMR1, FMR2, FMR3, FMR4, FMR5A, RFFMRP18, RFFMZBUM, RFFMZBVT, FMWAZ, RFFMPLAN, FM48, FM49, KG828, K9151, K9068, K9154 .</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>In Release 4.6, major changes were made in different areas of Cash Budget Management: planning, reporting, updating transaction data from MM and FI.<br />These changes are compatible with the status prior to Release 4.6, that is if you use an early release and use Cash Budget Management, Cash Budget Management also runs without problems after the release upgrade; in particular, it is not necessary to reconstruct funds transaction data from the original documents. However, you must note a few points and these are briefly displayed in the following:</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The Customizing Transaction 'OF01' now contains control options in the standard system as opposed to previous releases in which these options had to be implemented in the standard system via a modification:</p> <UL><LI>You can do without the updating of purchase orders in Cash Budget Management: In many companies, the delivery date in a purchase order is not adjusted to delays that actually occurred, that is why the purchase orders do not provide accurate information on the time of the outgoing payments to be expected.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This flag does not affect the inheritance of the commitment items from the purchase order lines into the follow-on documents.</p> <UL><LI>You can do without the updating of the parked FI documents.</LI></UL> <UL><LI>You can do without the updating of the actual and commitment data in (alternative) company code currency: if the company code currency differs from the FM area currency, all original documents usually generate 'double' line items and totals records in Cash Budget Management: in FM area currency and in company code currency in each case. Considering the space requirements of the funds transaction data, this is a considerable waste of resources if you are not at all interested in an evaluation in company code currency.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The updating of the values in transaction currency and the valuation options do not change because of this flag.</p> <UL><LI>You can save space for the line item table by doing without the statistical additional information from FI, CO and MM in the funds line items and by summarizing commitment items (with transaction 30: Expense/revenue). This flag is particularly useful if you often have documents with a relatively high number of expense or revenue lines in which there is a relatively small number of transaction no. 30 commitment items.</LI></UL> <UL><LI>You can change the updating date of bank lines (financial transaction 90, 80) from value date to posting date. This way you can always reconcile the balances displayed in Cash Budget Management with the bank account balances in Accounting.</LI></UL> <UL><LI>Finally you can set the clearing information in such a way that the clearing information from Accounting is not immediately updated in Cash Budget Management. As a result, the update processes of FB05 postings run a lot faster. Clearing information is buffered and must then be implemented by means of the batch program (RFFMRP18).<br /></LI></UL> <p>Major parts of the program for updating FI documents has been rewritten.Most changes are invisible to you; however, a visible difference is the changed display of on account payments bank - vendor (or bank - customer): While such documents were previously only visible in Actual (value type: payment), they now also cause a reduction in the commitments (value type: invoice). Thus Cash Budget Management responds in this point in the same way as Cash Management and Forecast.<br /><br />The updating of the funds balances was changed to a physical totals updating, that is cumulation now actually no longer occurs for every update on the database but only in reporting. The balance table FMBE is technically no longer used; the balances are now in totals table FMSU with their own value type '04'. This gets rid of the cumbersome setting of an annual interval for the funds balances (old Transaction 'OFGE') which is also prone to errors.<br />If you came from an earlier release to Release 4.6, you must start program RFFMZBUM in the update run once to transfer the balances into the new version. It is not required that this is carried out directly following a release upgrade.<br />Due to the new update logic, you must explicitly carry out the balance carryforward into a new fiscal year by means of a program (RFFMZBVT in the application menu) - just like you do in Accounting. You may repeat this as often as you wish.<br /><br />In reporting, the old standard reports (Transactions FMR1 to FMR4) were replaced by new standard reports (FMR1 to FMR4 and FMR5A). In Release 4.6A, supplement the report tree which was still delivered without these reports. The new reports use the ALV function and therefore offer you the option of hiding and displaying fields as per your requirements. Unfortunately, the display of commitment item hierarchies is not without problems; only one level is possible at present.<br />Even though the drilldown reports were included in the delivery, SAP recommends you use the standard reports.<br /><br />The funds transfers as a means to correct actual figures in Cash Budget Management still only work for commitment items of same type (revenue or expense). To avoid this restriction, SAP created a new transaction (FMWAZ) which in the 'mixed' case creates two old payment transfers. Thebalance is posted to two clearing line items that must be defined in Customizing.<br /><br />As of Relese 4.6B, financial budgeting is in the CO planning processor and is therefore in Handling and in cost planning. As a functional enhancement, you now have the option of planning in transaction currency. In addition to that, there are a number of reports for planning support (RFFMPLAN*) which you can execute using Transaction SA38.<br />However, the delivery of Release 4.6B does not contain the planned standard planning layouts for Cash Budget Management, the system therefore generates error messages KG828 or K9151 when you execute the planning transactions. You can obtain the missing planning layouts:<br /></p> <OL>1. '1SAPTRCB': Planning in FM area currency</OL> <OL>2. '2SAPTRCB': Planning in a fixed transaction currency</OL> <OL>3. '3SAPTRCB': Planning in several transaction currencies<br /></OL> <p>as follows: Import from SAPSERVx:<br /><br />Transport name: KK4K043394<br />Directory:&#x00A0;&#x00A0;/general/R3server/abap/note.0191821<br /><br />You can find information on how to import SAPSERVx transports in Note 13719. As usual, you must then attach these or planning layouts that you created yourself to your planner profile (Transaction KP34PS; planning area TR - financial budgeting).<br />If your system language is not 'DE' (German, this is unfortunately not enough: When trying to call the planning transaction FM48, you will be stopped by error message K9068 or K9154. The reason is that the translated texts for the layout header and the columns are missing. There are two ways of getting rid of the problem:<br />Either translate the layouts (from FM48_2) or copy '1SAPTRCB' etc into new layouts (FM48_1): When creating new layouts this way, the column headers will first be empty. By double-clicking the cells and confirming, you will however get default texts in your logon language.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "TR-CB-IN (Integration)"}, {"Key": "Other Components", "Value": "XX-RC-TR-CB (-obsolete-  RC-TR-Cash Budget Management)"}, {"Key": "Other Components", "Value": "TR-CB-PO (Posting)"}, {"Key": "Other Components", "Value": "TR-CB-IS (Information System)"}, {"Key": "Other Components", "Value": "TR-CB-PL (Planning)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D021772)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000191821/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000191821/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000191821/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000191821/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000191821/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000191821/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000191821/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000191821/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000191821/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "92129", "RefComponent": "TR-CB-IN", "RefTitle": "Preventing an online update of purchase orders", "RefUrl": "/notes/92129"}, {"RefNumber": "77472", "RefComponent": "TR-CB-IN", "RefTitle": "Update date for bank documents", "RefUrl": "/notes/77472"}, {"RefNumber": "580622", "RefComponent": "TR-CB", "RefTitle": "Cash budget management without forecast", "RefUrl": "/notes/580622"}, {"RefNumber": "519839", "RefComponent": "TR-CB-IN", "RefTitle": "Chain creation in Cash Budget Management", "RefUrl": "/notes/519839"}, {"RefNumber": "486201", "RefComponent": "TR-CB-IN", "RefTitle": "Update termination w/ clearing because of memory bottleneck", "RefUrl": "/notes/486201"}, {"RefNumber": "445799", "RefComponent": "TR-CB-PL", "RefTitle": "FM area text not in logon language w/ planning", "RefUrl": "/notes/445799"}, {"RefNumber": "412606", "RefComponent": "FIN-FSCM-LP", "RefTitle": "Cash Budget Management or Liquidity Planner?", "RefUrl": "/notes/412606"}, {"RefNumber": "396622", "RefComponent": "TR-CB-PL", "RefTitle": "Financial budgeting: Values always saved as positive", "RefUrl": "/notes/396622"}, {"RefNumber": "391380", "RefComponent": "TR-CB-PL", "RefTitle": "Financial budgeting per company code or business area", "RefUrl": "/notes/391380"}, {"RefNumber": "367722", "RefComponent": "TR-CB-PL", "RefTitle": "Runtime error RAISE_EXCEPTION for FM48", "RefUrl": "/notes/367722"}, {"RefNumber": "350582", "RefComponent": "TR-CB-IN", "RefTitle": "Incorrect cash balances after release upgrade", "RefUrl": "/notes/350582"}, {"RefNumber": "323829", "RefComponent": "TR-CB-IN", "RefTitle": "Parked documents in Release 4.6", "RefUrl": "/notes/323829"}, {"RefNumber": "322580", "RefComponent": "TR-CB-IS", "RefTitle": "Hierarchy in standard reports", "RefUrl": "/notes/322580"}, {"RefNumber": "309214", "RefComponent": "PS-CAF-PLN-PLN", "RefTitle": "Creating authorization groups for planner profiles", "RefUrl": "/notes/309214"}, {"RefNumber": "214336", "RefComponent": "TR-CB-IS", "RefTitle": "No balances in drilldown reports", "RefUrl": "/notes/214336"}, {"RefNumber": "214198", "RefComponent": "TR-CB-IS", "RefTitle": "Reporting tree in Easy Acess Menu", "RefUrl": "/notes/214198"}, {"RefNumber": "195817", "RefComponent": "TR-CB-IS", "RefTitle": "Standard reports", "RefUrl": "/notes/195817"}, {"RefNumber": "137568", "RefComponent": "TR-CB", "RefTitle": "Document volume in Cash Budget Management", "RefUrl": "/notes/137568"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "123569", "RefComponent": "TR-CB-IN", "RefTitle": "Cash Budget Management: Online/background", "RefUrl": "/notes/123569"}, {"RefNumber": "122659", "RefComponent": "TR-CB-PO", "RefTitle": "Enhance payment transfers", "RefUrl": "/notes/122659"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "486201", "RefComponent": "TR-CB-IN", "RefTitle": "Update termination w/ clearing because of memory bottleneck", "RefUrl": "/notes/486201 "}, {"RefNumber": "445799", "RefComponent": "TR-CB-PL", "RefTitle": "FM area text not in logon language w/ planning", "RefUrl": "/notes/445799 "}, {"RefNumber": "367722", "RefComponent": "TR-CB-PL", "RefTitle": "Runtime error RAISE_EXCEPTION for FM48", "RefUrl": "/notes/367722 "}, {"RefNumber": "396622", "RefComponent": "TR-CB-PL", "RefTitle": "Financial budgeting: Values always saved as positive", "RefUrl": "/notes/396622 "}, {"RefNumber": "137568", "RefComponent": "TR-CB", "RefTitle": "Document volume in Cash Budget Management", "RefUrl": "/notes/137568 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "77472", "RefComponent": "TR-CB-IN", "RefTitle": "Update date for bank documents", "RefUrl": "/notes/77472 "}, {"RefNumber": "580622", "RefComponent": "TR-CB", "RefTitle": "Cash budget management without forecast", "RefUrl": "/notes/580622 "}, {"RefNumber": "391380", "RefComponent": "TR-CB-PL", "RefTitle": "Financial budgeting per company code or business area", "RefUrl": "/notes/391380 "}, {"RefNumber": "122659", "RefComponent": "TR-CB-PO", "RefTitle": "Enhance payment transfers", "RefUrl": "/notes/122659 "}, {"RefNumber": "412606", "RefComponent": "FIN-FSCM-LP", "RefTitle": "Cash Budget Management or Liquidity Planner?", "RefUrl": "/notes/412606 "}, {"RefNumber": "214198", "RefComponent": "TR-CB-IS", "RefTitle": "Reporting tree in Easy Acess Menu", "RefUrl": "/notes/214198 "}, {"RefNumber": "323829", "RefComponent": "TR-CB-IN", "RefTitle": "Parked documents in Release 4.6", "RefUrl": "/notes/323829 "}, {"RefNumber": "519839", "RefComponent": "TR-CB-IN", "RefTitle": "Chain creation in Cash Budget Management", "RefUrl": "/notes/519839 "}, {"RefNumber": "322580", "RefComponent": "TR-CB-IS", "RefTitle": "Hierarchy in standard reports", "RefUrl": "/notes/322580 "}, {"RefNumber": "123569", "RefComponent": "TR-CB-IN", "RefTitle": "Cash Budget Management: Online/background", "RefUrl": "/notes/123569 "}, {"RefNumber": "309214", "RefComponent": "PS-CAF-PLN-PLN", "RefTitle": "Creating authorization groups for planner profiles", "RefUrl": "/notes/309214 "}, {"RefNumber": "92129", "RefComponent": "TR-CB-IN", "RefTitle": "Preventing an online update of purchase orders", "RefUrl": "/notes/92129 "}, {"RefNumber": "350582", "RefComponent": "TR-CB-IN", "RefTitle": "Incorrect cash balances after release upgrade", "RefUrl": "/notes/350582 "}, {"RefNumber": "214336", "RefComponent": "TR-CB-IS", "RefTitle": "No balances in drilldown reports", "RefUrl": "/notes/214336 "}, {"RefNumber": "195817", "RefComponent": "TR-CB-IS", "RefTitle": "Standard reports", "RefUrl": "/notes/195817 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 46B", "SupportPackage": "SAPKH46B01", "URL": "/supportpackage/SAPKH46B01"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}