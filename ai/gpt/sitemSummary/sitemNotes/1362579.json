{"Request": {"Number": "1362579", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 331, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000008033162017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001362579?language=E&token=ACCB8FEBC30F9697F6F3D1B15C5F9B24"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001362579", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001362579/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1362579"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Modification"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Pilot Release"}, "ReleasedOn": {"_label": "Released On", "value": "10.08.2009"}, "SAPComponentKey": {"_label": "Component", "value": "SRM-EBP-ADM-ORG"}, "SAPComponentKeyText": {"_label": "Component", "value": "Organizational Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Supplier Relationship Management", "value": "SRM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SRM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SRM", "value": "SRM-EBP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SRM-EBP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Organization/Business Partner Administration", "value": "SRM-EBP-ADM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SRM-EBP-ADM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Organizational Management", "value": "SRM-EBP-ADM-ORG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SRM-EBP-ADM-ORG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1362579 - Concurrent Employment Enablement of Shopping Cart"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p><B>************************ DRAFT VERSION ********************************</B><br /><B>***************** OPEN FOR FEEDBACK UNTIL CW 35 ***********************</B><br />The feature Concurrent Employment (CE) is not part of the standard product definition of SRM. This means that in the standard design of SRM the Shopping Cart requester (Employee) can only use a single organizational context (user attributes like e.g. catalogues, manager assignment) of a Concurrent Employment (CE) and has no option to choose a different organizational assignment.<br /><br />This modification note demonstrates how one can realize an organizational context selection for employees in Concurrent Employments. The modifications and BAdI implementations attached to this note contain functions that allows enabling employees to choose their organizational assignments in a separate Web Dynpro application. The employee's selection will then be taken into account when they start the Shopping Cart Self Service.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SRM 5.0, Concurrent Employment, Attributes, PPOMA_BBP, Organizational Model, Organizational Structure, Organizational Plan, Employee, Central Person</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><UL><LI>Concurrent Employment is used in the integrated HCM component.</LI></UL> <UL><LI>Concurrent Employment has a single user account only (in HCM and in all integrated components, in particular in SRM).</LI></UL> <p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><B>************************ DRAFT VERSION ********************************</B><br /><B>***************** OPEN FOR FEEDBACK UNTIL CW 35 ***********************</B><br /><B><B>Note for pilot customers: SAP will help to apply this pilot solution until we agree that it works correctly. At that time the status \"draft\" of this note will be removed and any issues that arise in relation to this modification will be handled as separate consulting issues. This applies to issues relating to the new functionality enabled by the modification, but also to issues that may arise in other areas.</B></B><br /><br />After applying this note employees in Concurrent Employments can choose their organizational context (e.g. the catalogue set, the reporting-line manager, etc.) in a Web Dynpro application. This selection will then be recognized when they start the Shopping Cart.<br /></p> <b>Main activation steps:</b><br /> <OL>1. Please import the attached transport file (for detailed description see manual correction instruction).</OL> <OL>2. Add the following customer field to the header structures INCL_EEW_PD_HEADER_CSF_SC and INCL_EEW_PD_HEADER_CSF:</OL> <UL><UL><LI>Component: 'ZPOSITION'</LI></UL></UL> <UL><UL><LI>Component Type: 'HROBJID'</LI></UL></UL> <OL>3. Transaction SE19:</OL> <OL><OL>a) Activate the implementation BBP_OM_CE_SC_ORGCTXT of the BAdI BBP_DOC_CHANGE_BADI (with filter value 'BUS2121'). This implementation stores the current employee position in the customer field 'ZPOSITION' of the SC header.</OL></OL> <OL><OL>b) Activate the implementation BBP_OM_CE_WF_ORGCTXT of the BAdI BBP_WFL_APPROV_BADI.</OL></OL> <OL><OL>c) Activate the implementation BBP_OM_CE_LS_ORGCTXT of the BAdI BBP_WF_LIST (with filter value 'BUS2121'). This implementation ensures that only Shopping Carts with the correct organizational assignment are displayed in application \"Check Status\" (BBPSC04) and as Favorites &amp; Templates in application \"Shop\".</OL></OL> <OL>4. Transaction SM30, view T77OMATTR (modification): Exchange the scenario class CL_BBP_ATTR_SCENARIO by CL_BBP_OM_CE_ATTR_SCENARIO.</OL> <OL>5. Transaction PFCG, tab card \"Menu\": The roles for employees in Concurrent Employments should contain the navigation link to select the organizational assignment. Therefore please add the Web Dynpro application 'BBP_WDA_ORGCONTEXT_SEL' to the menu of these roles (button \"Add Others\" on tab card \"Menu\").</OL> <OL>6. Transaction PERSREG: Please verify that the personalization object BBP_OM_CE_ORGCONTEXT (\"Organizational Context Selection (Concurrent Employment)\") is registered with dialog function module BBP_OM_CE_PERS_ORGCONTEXT and DDIC type name HROBJECT. The object should be visible in user but not in role maintenance.</OL> <OL>7. Transaction SWB_COND: Maintain the starting conditions correctly that the workflow WS14000133 is always started for employees in Concurrent Employments.</OL> <p></p> <b>Main functional and UI enhancements:</b><br /> <UL><LI>Administrative Service: The organizational context (i.e. active position assignment) of an employee can be changed by an administrator through personalization of the SU01 user account of that employee (personalization object \"Organizational Context Selection (Concurrent Employment)\" (BBP_OM_CE_PERS_ORGCONTEXT) - see tab card \"Personalization\").</LI></UL> <UL><LI>Employee Self Service: The organizational context (i.e. active position assignment) of an employee can also be changed by the employee itself through the Web Dynpro Self Service \"Select Organizational Context\" (BBP_OM_CE_WDA_ORGCONTEXT).</LI></UL> <UL><LI>The current organizational context selection is stored in the customer field 'ZPOSITION' of the Shopping Cart header and is taken into account by the following functions:</LI></UL> <UL><UL><LI>Approver determination in the workflow WS14000133 (n-step approval).</LI></UL></UL> <UL><UL><LI>Only Shopping Carts with a ZPOSITION value that is equal to the active position assignment of the employee are listed in application \"Check Status\" (transaction BBPSC04).</LI></UL></UL> <UL><UL><LI>Only Shopping Carts with a ZPOSITION value that is equal to the active position assignment of the employee are listed as \"Favorites and Templates\" in application \"Shop\" (transaction BBPSC01).</LI></UL></UL> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SRM-EBP-ADM-USR (User Administration)"}, {"Key": "Other Components", "Value": "SRM-EBP-SHP (Shopping Cart)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D035333)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D041157)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001362579/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001362579/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001362579/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001362579/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001362579/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001362579/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001362579/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001362579/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001362579/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "PMEK025049.zip", "FileSize": "105", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000400382009&iv_version=0004&iv_guid=41E7E5B6290A5740A078525FA2CE9B29"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SRM_SERVER", "From": "550", "To": "550", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Activity&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SRM_SERVER&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 550&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/>Please import the transport files attached to this note as ZIP<br/>file \"PMEK025049.zip\" by following these steps:</P> <OL>1. Open the note in CSN.</OL> <OL>2. Select \"Notes Administration\"</OL> <OL>3. Navigate to the tab \"Attachments\"</OL> <OL>4. Download the ZIP file</OL> <OL>5. Unpack the file</OL> <OL>6. Follow the implementation instruction as explained in note 13719 (see related notes).</OL> <P></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}