{"Request": {"Number": "925164", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 532, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005414392017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000925164?language=E&token=BF92C852F2A185DDA85622E0B166B50F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000925164", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000925164/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "925164"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 33}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.07.2010"}, "SAPComponentKey": {"_label": "Component", "value": "PY-BR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Brazil"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Brazil", "value": "PY-BR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-BR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "925164 - HBRRAIS0 Legal Changes 2006"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>RAIS report changes for legal compliance of 2005 results.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Relatorio Anual de Informacoes Sociais, mudanca legal, Brasil, Brazil<br />RAIS; 2006;</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Some extra information was added to different RAIS records.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Please, attention that the fields of CNPJ and \"Valor contrib.\" included in the selection screen and also in table T7BR06(multi company) have to be filled as they should be reported in the file, without special characters, commas, etc.<br /><br /><B>IMPORTANT:</B><br />In order to install this note you should have the latest released SP<br />installed in your system, if you do not have it you have to be very<br />carefull with the required notes.<br /><br /><STRONG>This note includes changes in table T7BR06 which was created by note 882093.</STRONG><br /><br />There are four new values for evaluation class 12 which is relevant for RAIS. Each of these new values is related to a new field. It is necessary that customers customize the wage types they want to have reported in each of this four new fields.<br /><br /><br />The correction described in this note will be included in an HR Support Package, as indicated in item \"Reference to Support Packages\".<br /><br />The support package includes:<br /></p> <UL><LI>new values to Evaluation Class 12 as listed below:</LI></UL> <UL><UL><LI>05 - \"Valor de f&#x00E9;rias na rescis&#x00E3;o\"</LI></UL></UL> <UL><UL><LI>06 - \"Horas trabalhadas no m&#x00EA;s\"</LI></UL></UL> <UL><UL><LI>07 - \"Diss&#x00ED;dio coletivo pago somente na rescis&#x00E3;o de contrato\"</LI></UL></UL> <UL><UL><LI>08 - \"Multa rescis&#x00F3;ria\"</LI></UL></UL> <p></p> <UL><LI>wage type MR10 and MR20 had Evaluation Class 12 customized to value 05;</LI></UL> <p></p> <UL><LI>wage type /H00 had Evaluation Class 12 customized to value 06 (Please have in attention that wage type /H00 represents the work hours of contract and not the effective worked hours, but it is being used as a reference in this report.);</LI></UL> <p></p> <UL><LI>changes in type pools PBRRS, PBR94 and PBR99;</LI></UL> <p></p> <UL><LI>changes in table T7BR06;</LI></UL> <p></p> <UL><LI>changes in view V_T7BR06(it is not yet available for release 4.70);</LI></UL> <p></p> <UL><LI>new structures HBRRS0_05, HBRRS1_05, HBRRS2_05 and HBRRS9_05;</LI></UL> <p></p> <UL><LI>new tables HBRRS0_05, HBRRS1_05, HBRRS2_05 and HBRRS9_05;</LI></UL> <p></p> <UL><LI>in interface IF_EX_HRPAYBR_RAIS_RECORD1 method UPDATE_RECORD1 had \"reference type\" of paramenter RECORD1 changed to HBRRS1_05;</LI></UL> <p></p> <UL><LI>new text symbol and selection texts in report HBRRAIS0;</LI></UL> <p></p> <UL><LI>changes in includes HBRRAIS0, PCRAIBR0, PCRAIBR2, PCRAIBRD, PUTMDBR0 and PUTMMBR2;</LI></UL> <p></p> <UL><LI>new entries in table T52B4 and T52B5 for temse RAIS06;</LI></UL> <p></p> <UL><LI>new data elements PBR_ASSIST, PBR_ASSOC, PBR_CONFED, PBR_SINDIC, PBR_TOASSI, PBR_TOASSO, PBR_TOCONF, PBR_TOSIND, PBR_BONUS, PBR_CNPJPATASC, PBR_CNPJPATAST, PBR_CNPJPATCONF, PBR_CNPJPATSIND, PBR_COMPMOV, PBR_CONTPATASC, PBR_CONTPATAST, PBR_CONTPATCONF, PBR_CONTPATSIND, PBR_DISSIDIO, PBR_EXTRAHOURS, PBR_FGTSFINE, PBR_NCOMPBONUS, PBR_NCOMPDISS, PBR_NCOMPHOURS, PBR_VACTERM, PBR_WRKHOURSM were created.</LI></UL> <p><br /><br />An Advanced Delivery including changes done in the Data Dictionary and<br />ABAP code is available in the attached files according to the following list (\"xxxxxx\" means numbers):<br /><br />L6DKxxxxxx_500_SYST.CAR - Release ERP 2004<br />L6BKxxxxxx_470_SYST.CAR - Release 4.70(Enterprise)<br />L9CKxxxxxx_46C_SYST.CAR - Release 4.6C<br />L9BKxxxxxx_46B_SYST.CAR - Release 4.6B<br />L4DKxxxxxx_45B_SYST.CAR - Release 4.5B<br /><br />An Advanced Delivery including Customizing changes is available in the attached files according to the following list(\"xxxxxx\" means numbers):<br /><br />L6DKxxxxxx_500_CUST.CAR - Release ERP 2004<br />L6BKxxxxxx_470_CUST.CAR - Release 4.70(Enterprise)<br />L9CKxxxxxx_46C_CUST.CAR - Release 4.6C<br />L9BKxxxxxx_46B_CUST.CAR - Release 4.6B<br />L4DKxxxxxx_45B_CUST.CAR - Release 4.5B<br /><br />If your release is not yet included in this list please contact us.<br /><br />For more details about Advance Delivery installation procedure please read the notes listed in \"Related Notes\" item.<br /><br /><B>IMPORTANT:</B><br />Be aware of an Advance Delivery delivers the last version of the object, it means that if you do not have the last HR Support Package installed in you system you could get errors, either Syntax Errors or process errors. In this case the only option is to undo the changes from Advance Delivery and do the code changes manually according to the Correction Instructions available in this note.<br /><br /><br />CORRECTIONS - ATTENTION:<br />The customizing objects were not changed after the original version, so the Advance Delivery files with name&#x00A0;&#x00A0;*_CUST.CAR were not changed.<br />The system objects have been changed due to some corrections, the attached Advance Delivery files with name *_SYST.CAR have been updated and have always the last complete version.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D034789"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I811676)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000925164/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000925164/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000925164/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000925164/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000925164/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000925164/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000925164/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000925164/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000925164/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "L4DK125783_45B_SYST.CAR", "FileSize": "58", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000156142006&iv_version=0033&iv_guid=A827717757701945B1130F2319EE0C1F"}, {"FileName": "L7DK012394_600_CUST.CAR", "FileSize": "8", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000156142006&iv_version=0033&iv_guid=3321BA22D6A46B4CB128C1AE83583E58"}, {"FileName": "L6BK111408_470_SYST.CAR", "FileSize": "73", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000156142006&iv_version=0033&iv_guid=135AEB62C0EDB9479E4187E0815CBAA5"}, {"FileName": "L9BK134166_46B_SYST.CAR", "FileSize": "70", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000156142006&iv_version=0033&iv_guid=3152FDCA7357B84D9CBA1FDBD48F1141"}, {"FileName": "L4DK125715_45B_CUST.CAR", "FileSize": "5", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000156142006&iv_version=0033&iv_guid=C6CD5D1BF629CE41A5545DA913099DCF"}, {"FileName": "L9CK204408_46C_CUST.CAR", "FileSize": "6", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000156142006&iv_version=0033&iv_guid=6117E1BEF292A844AC66A8E98DCD44DE"}, {"FileName": "L9CK204765_46C_SYST.CAR", "FileSize": "111", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000156142006&iv_version=0033&iv_guid=E298AA50B6202248A66B00386DB2C4D1"}, {"FileName": "L7DK012394_600_SYST.CAR", "FileSize": "81", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000156142006&iv_version=0033&iv_guid=EB6E5BCD9A877841861C799438B72D58"}, {"FileName": "L6DK042630_500_SYST.CAR", "FileSize": "112", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000156142006&iv_version=0033&iv_guid=ECA9FF0992301B46AE7EA008118CB99D"}, {"FileName": "L9BK134067_46B_CUST.CAR", "FileSize": "7", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000156142006&iv_version=0033&iv_guid=E42DCB4C8E7EDC4D97E02D4005F33F51"}, {"FileName": "L6DK042337_500_CUST.CAR", "FileSize": "7", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000156142006&iv_version=0033&iv_guid=D970ED2111FD044EA6D474CA5A83D4EF"}, {"FileName": "L6BK110988_470_CUST.CAR", "FileSize": "8", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000156142006&iv_version=0033&iv_guid=F6FC4F8BDA32074E8B6E9F98D82096DF"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 45B", "SupportPackage": "SAPKE45BD7", "URL": "/supportpackage/SAPKE45BD7"}, {"SoftwareComponentVersion": "SAP_HR 46B", "SupportPackage": "SAPKE46BB9", "URL": "/supportpackage/SAPKE46BB9"}, {"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46CB0", "URL": "/supportpackage/SAPKE46CB0"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47055", "URL": "/supportpackage/SAPKE47055"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50021", "URL": "/supportpackage/SAPKE50021"}, {"SoftwareComponentVersion": "SAP_HR 600", "SupportPackage": "SAPKE60005", "URL": "/supportpackage/SAPKE60005"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_HR", "NumberOfCorrin": 6, "URL": "/corrins/0000925164/2"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 6, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 62, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "390938 ", "URL": "/notes/390938 ", "Title": "Neg. WT are displayed as if there were pos. ones", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "412841 ", "URL": "/notes/412841 ", "Title": "Eletronic GPS", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "438382 ", "URL": "/notes/438382 ", "Title": "HR: Adjustment of 13th Salary in December", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "483363 ", "URL": "/notes/483363 ", "Title": "Housekeeping: search information from branches/cei", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "490606 ", "URL": "/notes/490606 ", "Title": "HBRRAIS: Legal Changes 2002 additional changes", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "494599 ", "URL": "/notes/494599 ", "Title": "HBRRAIS: Complete reviewed RAIS report", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "496862 ", "URL": "/notes/496862 ", "Title": "HBRRAIS: Legal Changes 2002 Aviso Previo not printed", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "499227 ", "URL": "/notes/499227 ", "Title": "HBRRAIS: Company size indicator missing, TPADM filled incor.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "499493 ", "URL": "/notes/499493 ", "Title": "HBRRAIS: TPINS REG0 not filled, TPADM not filled correctly", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "500161 ", "URL": "/notes/500161 ", "Title": "hbrrais: SALARY under some circumstances zero, CBO not found", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "519162 ", "URL": "/notes/519162 ", "Title": "HBRRAIS: <PERSON><PERSON><PERSON> not disp., only two month processed", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "588258 ", "URL": "/notes/588258 ", "Title": "RAIS - Legal Change 2003", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "592805 ", "URL": "/notes/592805 ", "Title": "RAIS - firing date generated incorrectly", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "593233 ", "URL": "/notes/593233 ", "Title": "RAIS - termination and hiring codes", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "595751 ", "URL": "/notes/595751 ", "Title": "RAIS - Hiring code is not generated", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "597039 ", "URL": "/notes/597039 ", "Title": "RAIS - company change", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "600559 ", "URL": "/notes/600559 ", "Title": "RAIS - employee transfered and fired during the year", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "600781 ", "URL": "/notes/600781 ", "Title": "HBRRAIS0 - 13th salary & termination", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "698095 ", "URL": "/notes/698095 ", "Title": "RAIS: Legal change 2004", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "708151 ", "URL": "/notes/708151 ", "Title": "HBRRAIS0: salary is missing 2 digits", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "777905 ", "URL": "/notes/777905 ", "Title": "HBRRAIS0 - Field Number or Complement in file is mandatory", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "778824 ", "URL": "/notes/778824 ", "Title": "HBRRAIS0 Visa document warning", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "818359 ", "URL": "/notes/818359 ", "Title": "Legal change in HBRDIRF0 for year 2005", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "830026 ", "URL": "/notes/830026 ", "Title": "HBRRAIS0 - Filling up additional fields in record type 1", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "866686 ", "URL": "/notes/866686 ", "Title": "DIRF file display", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "887240 ", "URL": "/notes/887240 ", "Title": "HBRRAIS0 and prenotice in AJUS offcycle", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "917189 ", "URL": "/notes/917189 ", "Title": "HBRRAIS0 does not fill fields \"tiposal\" and \"horassem\"", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46B", "Number": "381758 ", "URL": "/notes/381758 ", "Title": "HBRUTMS no display for 2001 DIRF reports", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46B", "Number": "816121 ", "URL": "/notes/816121 ", "Title": "RAIS: Absence of one branch considered in another branch", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "197389 ", "URL": "/notes/197389 ", "Title": "HBRDIRF0 - acertos gerais", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "203897 ", "URL": "/notes/203897 ", "Title": "HBRRAIS - Alterações diversas para emissão RAIS/99", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "211535 ", "URL": "/notes/211535 ", "Title": "SEFIP - Alter. para nova versao", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "302941 ", "URL": "/notes/302941 ", "Title": "HBRCAGED - Atualização para a versão 1.16", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "327840 ", "URL": "/notes/327840 ", "Title": "HBRRAIS0 - Acertos gerais", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "376649 ", "URL": "/notes/376649 ", "Title": "Legal change HBRRAIS0 2001", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "381485 ", "URL": "/notes/381485 ", "Title": "HBRDIRF IDTAX preset wrong, third struct unnecessary", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "384995 ", "URL": "/notes/384995 ", "Title": "HBRRAIS0 - Correções para tipo de admissão", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "470", "Number": "578445 ", "URL": "/notes/578445 ", "Title": "HBRIN860 IRRF indicator is empty in saved file", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "500", "Number": "815438 ", "URL": "/notes/815438 ", "Title": "RAIS absence code", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "500", "Number": "816281 ", "URL": "/notes/816281 ", "Title": "RAIS: incorrect remuneration assignment", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "500", "Number": "833953 ", "URL": "/notes/833953 ", "Title": "HBRRAIS0 - \"Receipt flag\" field not correctly filled", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "390938 ", "URL": "/notes/390938 ", "Title": "Neg. WT are displayed as if there were pos. ones", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "412841 ", "URL": "/notes/412841 ", "Title": "Eletronic GPS", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "438382 ", "URL": "/notes/438382 ", "Title": "HR: Adjustment of 13th Salary in December", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "483363 ", "URL": "/notes/483363 ", "Title": "Housekeeping: search information from branches/cei", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "490606 ", "URL": "/notes/490606 ", "Title": "HBRRAIS: Legal Changes 2002 additional changes", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "494599 ", "URL": "/notes/494599 ", "Title": "HBRRAIS: Complete reviewed RAIS report", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "496862 ", "URL": "/notes/496862 ", "Title": "HBRRAIS: Legal Changes 2002 Aviso Previo not printed", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "499227 ", "URL": "/notes/499227 ", "Title": "HBRRAIS: Company size indicator missing, TPADM filled incor.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "499493 ", "URL": "/notes/499493 ", "Title": "HBRRAIS: TPINS REG0 not filled, TPADM not filled correctly", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "500161 ", "URL": "/notes/500161 ", "Title": "hbrrais: SALARY under some circumstances zero, CBO not found", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "519162 ", "URL": "/notes/519162 ", "Title": "HBRRAIS: <PERSON><PERSON><PERSON> not disp., only two month processed", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "588258 ", "URL": "/notes/588258 ", "Title": "RAIS - Legal Change 2003", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "592805 ", "URL": "/notes/592805 ", "Title": "RAIS - firing date generated incorrectly", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "593233 ", "URL": "/notes/593233 ", "Title": "RAIS - termination and hiring codes", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "595751 ", "URL": "/notes/595751 ", "Title": "RAIS - Hiring code is not generated", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "597039 ", "URL": "/notes/597039 ", "Title": "RAIS - company change", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "600559 ", "URL": "/notes/600559 ", "Title": "RAIS - employee transfered and fired during the year", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "600781 ", "URL": "/notes/600781 ", "Title": "HBRRAIS0 - 13th salary & termination", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "698095 ", "URL": "/notes/698095 ", "Title": "RAIS: Legal change 2004", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "777905 ", "URL": "/notes/777905 ", "Title": "HBRRAIS0 - Field Number or Complement in file is mandatory", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "778824 ", "URL": "/notes/778824 ", "Title": "HBRRAIS0 Visa document warning", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "818359 ", "URL": "/notes/818359 ", "Title": "Legal change in HBRDIRF0 for year 2005", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "830026 ", "URL": "/notes/830026 ", "Title": "HBRRAIS0 - Filling up additional fields in record type 1", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "866686 ", "URL": "/notes/866686 ", "Title": "DIRF file display", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "887240 ", "URL": "/notes/887240 ", "Title": "HBRRAIS0 and prenotice in AJUS offcycle", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "917189 ", "URL": "/notes/917189 ", "Title": "HBRRAIS0 does not fill fields \"tiposal\" and \"horassem\"", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "380722 ", "URL": "/notes/380722 ", "Title": "HBRUTMS0 version handling is missing", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "390938 ", "URL": "/notes/390938 ", "Title": "Neg. WT are displayed as if there were pos. ones", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "412841 ", "URL": "/notes/412841 ", "Title": "Eletronic GPS", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "438382 ", "URL": "/notes/438382 ", "Title": "HR: Adjustment of 13th Salary in December", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "483363 ", "URL": "/notes/483363 ", "Title": "Housekeeping: search information from branches/cei", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "490606 ", "URL": "/notes/490606 ", "Title": "HBRRAIS: Legal Changes 2002 additional changes", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "494599 ", "URL": "/notes/494599 ", "Title": "HBRRAIS: Complete reviewed RAIS report", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "496862 ", "URL": "/notes/496862 ", "Title": "HBRRAIS: Legal Changes 2002 Aviso Previo not printed", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "499227 ", "URL": "/notes/499227 ", "Title": "HBRRAIS: Company size indicator missing, TPADM filled incor.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "499493 ", "URL": "/notes/499493 ", "Title": "HBRRAIS: TPINS REG0 not filled, TPADM not filled correctly", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "500161 ", "URL": "/notes/500161 ", "Title": "hbrrais: SALARY under some circumstances zero, CBO not found", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "519162 ", "URL": "/notes/519162 ", "Title": "HBRRAIS: <PERSON><PERSON><PERSON> not disp., only two month processed", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "588258 ", "URL": "/notes/588258 ", "Title": "RAIS - Legal Change 2003", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "592805 ", "URL": "/notes/592805 ", "Title": "RAIS - firing date generated incorrectly", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "593233 ", "URL": "/notes/593233 ", "Title": "RAIS - termination and hiring codes", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "595751 ", "URL": "/notes/595751 ", "Title": "RAIS - Hiring code is not generated", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "597039 ", "URL": "/notes/597039 ", "Title": "RAIS - company change", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "600559 ", "URL": "/notes/600559 ", "Title": "RAIS - employee transfered and fired during the year", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "600781 ", "URL": "/notes/600781 ", "Title": "HBRRAIS0 - 13th salary & termination", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "639349 ", "URL": "/notes/639349 ", "Title": "HBRGPS00 collects 13thSal twice", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "698095 ", "URL": "/notes/698095 ", "Title": "RAIS: Legal change 2004", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "706350 ", "URL": "/notes/706350 ", "Title": "HBRRAIS0: Rais 2004 does not print email address", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "777905 ", "URL": "/notes/777905 ", "Title": "HBRRAIS0 - Field Number or Complement in file is mandatory", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "778824 ", "URL": "/notes/778824 ", "Title": "HBRRAIS0 Visa document warning", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "816121 ", "URL": "/notes/816121 ", "Title": "RAIS: Absence of one branch considered in another branch", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "818359 ", "URL": "/notes/818359 ", "Title": "Legal change in HBRDIRF0 for year 2005", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "830026 ", "URL": "/notes/830026 ", "Title": "HBRRAIS0 - Filling up additional fields in record type 1", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "866686 ", "URL": "/notes/866686 ", "Title": "DIRF file display", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "879822 ", "URL": "/notes/879822 ", "Title": "HBRRAIS0 and inactive branches", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "887240 ", "URL": "/notes/887240 ", "Title": "HBRRAIS0 and prenotice in AJUS offcycle", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "470", "Number": "917189 ", "URL": "/notes/917189 ", "Title": "HBRRAIS0 does not fill fields \"tiposal\" and \"horassem\"", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "500", "Number": "921993 ", "URL": "/notes/921993 ", "Title": "SEFIP: CBO change in record type 13", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "494599 ", "URL": "/notes/494599 ", "Title": "HBRRAIS: Complete reviewed RAIS report", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "519162 ", "URL": "/notes/519162 ", "Title": "HBRRAIS: <PERSON><PERSON><PERSON> not disp., only two month processed", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "585539 ", "URL": "/notes/585539 ", "Title": "HBRRAIS0 rejects employees not having VISA document", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "588258 ", "URL": "/notes/588258 ", "Title": "RAIS - Legal Change 2003", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "592805 ", "URL": "/notes/592805 ", "Title": "RAIS - firing date generated incorrectly", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "592889 ", "URL": "/notes/592889 ", "Title": "RAIS - pre-notice filled incorrectly", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "593233 ", "URL": "/notes/593233 ", "Title": "RAIS - termination and hiring codes", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "595751 ", "URL": "/notes/595751 ", "Title": "RAIS - Hiring code is not generated", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "597039 ", "URL": "/notes/597039 ", "Title": "RAIS - company change", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "600135 ", "URL": "/notes/600135 ", "Title": "RAIS - solution improvement for company changes", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "600559 ", "URL": "/notes/600559 ", "Title": "RAIS - employee transfered and fired during the year", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "600781 ", "URL": "/notes/600781 ", "Title": "HBRRAIS0 - 13th salary & termination", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "639349 ", "URL": "/notes/639349 ", "Title": "HBRGPS00 collects 13thSal twice", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "656604 ", "URL": "/notes/656604 ", "Title": "Maternity leave: Legal change for September/2003", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "695279 ", "URL": "/notes/695279 ", "Title": "HBRCAGED doesn´t consider employees with categ. 04", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "698095 ", "URL": "/notes/698095 ", "Title": "RAIS: Legal change 2004", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "706350 ", "URL": "/notes/706350 ", "Title": "HBRRAIS0: Rais 2004 does not print email address", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "708009 ", "URL": "/notes/708009 ", "Title": "HBRRAIS0: takes wrong arrival date from foreigner IT0465", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "708917 ", "URL": "/notes/708917 ", "Title": "HBRRAIS0: neg values substracted, admission code missing", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "709362 ", "URL": "/notes/709362 ", "Title": "HBRRAIS0: Pensioneers treated like fired employees", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "744876 ", "URL": "/notes/744876 ", "Title": "BRTS - 13th salary eligibility in February", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "777905 ", "URL": "/notes/777905 ", "Title": "HBRRAIS0 - Field Number or Complement in file is mandatory", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "778824 ", "URL": "/notes/778824 ", "Title": "HBRRAIS0 Visa document warning", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "799103 ", "URL": "/notes/799103 ", "Title": "HBRRAIS0 does not generate record type 2", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "816121 ", "URL": "/notes/816121 ", "Title": "RAIS: Absence of one branch considered in another branch", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "818359 ", "URL": "/notes/818359 ", "Title": "Legal change in HBRDIRF0 for year 2005", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "830026 ", "URL": "/notes/830026 ", "Title": "HBRRAIS0 - Filling up additional fields in record type 1", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "866686 ", "URL": "/notes/866686 ", "Title": "DIRF file display", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "879822 ", "URL": "/notes/879822 ", "Title": "HBRRAIS0 and inactive branches", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "887240 ", "URL": "/notes/887240 ", "Title": "HBRRAIS0 and prenotice in AJUS offcycle", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "923818 ", "URL": "/notes/923818 ", "Title": "RAIS: fields of record 0 are not correcly filled", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "500", "Number": "821726 ", "URL": "/notes/821726 ", "Title": "HBRRAIS0 does not read 13DI offcycle in December", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "500", "Number": "909805 ", "URL": "/notes/909805 ", "Title": "SEFIP 8.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "744876 ", "URL": "/notes/744876 ", "Title": "BRTS - 13th salary eligibility in February", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "777905 ", "URL": "/notes/777905 ", "Title": "HBRRAIS0 - Field Number or Complement in file is mandatory", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "778824 ", "URL": "/notes/778824 ", "Title": "HBRRAIS0 Visa document warning", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "799103 ", "URL": "/notes/799103 ", "Title": "HBRRAIS0 does not generate record type 2", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "816121 ", "URL": "/notes/816121 ", "Title": "RAIS: Absence of one branch considered in another branch", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "818359 ", "URL": "/notes/818359 ", "Title": "Legal change in HBRDIRF0 for year 2005", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "830026 ", "URL": "/notes/830026 ", "Title": "HBRRAIS0 - Filling up additional fields in record type 1", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "866686 ", "URL": "/notes/866686 ", "Title": "DIRF file display", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "879822 ", "URL": "/notes/879822 ", "Title": "HBRRAIS0 and inactive branches", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "887240 ", "URL": "/notes/887240 ", "Title": "HBRRAIS0 and prenotice in AJUS offcycle", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "917189 ", "URL": "/notes/917189 ", "Title": "HBRRAIS0 does not fill fields \"tiposal\" and \"horassem\"", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "923818 ", "URL": "/notes/923818 ", "Title": "RAIS: fields of record 0 are not correcly filled", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "879822 ", "URL": "/notes/879822 ", "Title": "HBRRAIS0 and inactive branches", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "887240 ", "URL": "/notes/887240 ", "Title": "HBRRAIS0 and prenotice in AJUS offcycle", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "909805 ", "URL": "/notes/909805 ", "Title": "SEFIP 8.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "917189 ", "URL": "/notes/917189 ", "Title": "HBRRAIS0 does not fill fields \"tiposal\" and \"horassem\"", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "920095 ", "URL": "/notes/920095 ", "Title": "HBRGPS00 & HBRSEF00: Compensation - New functionality", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "942778 ", "URL": "/notes/942778 ", "Title": "HBRRAIS0 - at first record 1 the responsible company/branch", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "945787 ", "URL": "/notes/945787 ", "Title": "HBRCAGED: Record type C and X - Layout changes", "Component": "PY-BR"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}