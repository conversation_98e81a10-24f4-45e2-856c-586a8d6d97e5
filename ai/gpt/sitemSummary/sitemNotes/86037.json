{"Request": {"Number": "86037", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 366, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000295262017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000086037?language=E&token=ACE7D8204DE2DAF1CE915EC4B0325BB8"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000086037", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000086037/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "86037"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 38}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.02.2000"}, "SAPComponentKey": {"_label": "Component", "value": "MM-IS-IC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Inventory Controlling"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Information System", "value": "MM-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Inventory Controlling", "value": "MM-IS-IC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-IS-IC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "86037 - Collective note on doc.evaluations, Stock/Reqs"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Various errors occur during document evaluations in Inventory Controlling that apply to the following symptoms:<br /></p> <UL><UL><LI>New ABC indicator is not set during a cross-company ABC analysis.</LI></UL></UL> <UL><UL><LI>Incorrect cumulation during ABC analysis</LI></UL></UL> <UL><UL><LI>Program termination with SQL error during access to table MCON</LI></UL></UL> <UL><UL><LI>Program termination if more than 1000 material movements were posted for each material.</LI></UL></UL> <UL><UL><LI>Runtime problems</LI></UL></UL> <UL><UL><LI>Incorrect totals line</LI></UL></UL> <UL><UL><LI>Incorrect results during slow-moving item analysis</LI></UL></UL> <UL><UL><LI>Error in consumption calculation (Transaction MC45)</LI></UL></UL> <UL><UL><LI>Incorrect details</LI></UL></UL> <UL><UL><LI>Materials with deletion flag are selected even if the respective parameter is not set in the selection screen.</LI></UL></UL> <UL><UL><LI>Incorrect calculation of dead stock</LI></UL></UL> <UL><UL><LI>A stock value is displayed for material type UNBW</LI></UL></UL> <UL><UL><LI>Not all materials are displayed during the selection across several or all plants.</LI></UL></UL> <UL><UL><LI>Program termination: BCD_FIELD_OVERFLOW</LI></UL></UL> <UL><UL><LI>An incorrect unit is displayed for the graphic functions</LI></UL></UL> <UL><UL><LI>The analysis date is missing in the list header</LI></UL></UL> <UL><UL><LI>The detail display does not contain all relevant material documents.</LI></UL></UL> <UL><UL><LI>Sortings are not stable/do not work.</LI></UL></UL> <UL><UL><LI>Transaction MC48: Currencies with decimal places are not interpreted<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;correctly with the result that the stock value is incorrectly<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;determined.</LI></UL></UL> <p>************************************************************************<br />Standard analyses in Inventory Controlling on the current stock situation / requirements situation and long-term planning are incorrect. Report RMCBDISP that creates and updates stock data and requirements data for information structure S094 is incorrect. Update terminations occur and the update is carried out in the wrong period. In the evaluation for long-term planning, the stock transfer planned orders are not taken into account.<br />Document evaluations on several plants are incorrect. Stocks are multiplied by the number of the plants.<br />The materials are not valuated correctly.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Document evaluations, Inventory Controlling, LIS, ABC analysis, range of coverage, inventory turnover, slow-moving item, stock value, dead stock, consumption values, requirement values, detail display, sorting<br />MC40, MC41, MC42, MC43, MC44, MC45, MC46, MC47, MC48, MC49, MC50, MC51, MCB&amp;, MCBZ, MCB)<br />BCD_FIELD_OVERFLOW, CONVT_NO_NUMBER, TABLE_INVALID_INDEX,<br />SAPLMCB3, RMCBBS30, RMCBDISP</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The problem is caused by a program error.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. Implement Note 84738 first (up to Release 3.1H).</OL> <OL>2. Import a transport request from Sapserv3/Sapserv4.<br />The transport request is only applicable to Release 3*!!!<br />The path is: /general/R3server/abap/note.0086037. The names of the files are K004888.T3I, R004888.T3I. Refer to Note 13719 for this.</OL> <OL>3. You must create a secondary index for database table S094. You must specify the key fields in the following order: MATNR-WERKS-VRSIO-SPMON-SPTAG-SPWOC-SPBUP. The type of the index is NON-UNIQUE. Note: You can create an index by calling the dictionary via ABAP Workbench. Enter S094 for the database table and choose the \"Indices\" icon. For this, make sure you select the customer name range for the name of the index. This means that the index should start with 'Y' or 'Z'.</OL> <p>************************************************************************<br /><br />As a result, the following programs will be current and need no longer be corrected by individual notes:<br />SAPLMCB3, LMCB3TOP, LMCB3UXX, LMCB3O01, LMCB3F01, LMCB3F02, LMCB3F03, LMCB3F04, LMCB3F05, LMCB3L01, LMCB3L02, LMCB3I01, LMCB3F00, LMCB3F06, LMCB3F07, LMCB3F08, LMCB3F09, LMCB3F10, LMCB3F11, LMCB3F12, LMCB3F13, LMCB3F14, LMCB3F15, LMCB3O02, LMCB3U01, LMCB3U02, LMCB3U03, LMCB3U04, LMCB3U05, LMCB3U06, LMCB3U07, LMCB3U08, LMCB3U09, LMCB3U10, LMCB3U11, LMCB3U12, LMCB3U13, LMCB3U16, LMCB3U17, LMCB3U18, LMCB3U14, RMCBDISP, RMCBMRP2.<br />This applies to the following notes:<br />36323, 43014, 46073, 48228, 51057, 51384, 53604, 62613, 76194, 76736, 77035, 78793, 80067, 80350, 80443, 82080, 83328, 51905, 68265<br /><br />***********************************************************************<br />Documentation of the V E R S I O N S:<br />*************************************<br />No.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DATE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;REASON&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;FILES<br />***********************************************************************<br />0038&#x00A0;&#x00A0;&#x00A0;&#x00A0;02.09.2000&#x00A0;&#x00A0;&#x00A0;&#x00A0;Update Coding&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; R004888.T3I/K004888.T3I<br />0037&#x00A0;&#x00A0;&#x00A0;&#x00A0;02.03.2000&#x00A0;&#x00A0;&#x00A0;&#x00A0;Update Coding&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; R004863.T3I/K004863.T3I<br />0036&#x00A0;&#x00A0;&#x00A0;&#x00A0;12.14.1999&#x00A0;&#x00A0;&#x00A0;&#x00A0;Update Coding&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; R004436.T3I/K004436.T3I</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D026033)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000086037/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000086037/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000086037/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000086037/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000086037/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000086037/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000086037/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000086037/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000086037/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "92659", "RefComponent": "MM-IS-IC", "RefTitle": "\"SAPLMCB3\" \"CALL_FUNCTION_PARM_MISSING\"", "RefUrl": "/notes/92659"}, {"RefNumber": "91966", "RefComponent": "MM-IS-IC", "RefTitle": "Synt.error after importing Hot Pack. no. 7 Rel.3.1H", "RefUrl": "/notes/91966"}, {"RefNumber": "84738", "RefComponent": "MM-IS-IC-RPT", "RefTitle": "Texts:consumption val., rqmnt value, val.dead stock", "RefUrl": "/notes/84738"}, {"RefNumber": "76003", "RefComponent": "MM-IS-IC-RPT", "RefTitle": "Wrong document analysis for consumption to the day", "RefUrl": "/notes/76003"}, {"RefNumber": "75502", "RefComponent": "MM-IS-IC", "RefTitle": "No change documents during ABC analysis", "RefUrl": "/notes/75502"}, {"RefNumber": "68265", "RefComponent": "MM-IS-IC", "RefTitle": "Reading the requirements is incorrect", "RefUrl": "/notes/68265"}, {"RefNumber": "62613", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/62613"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "84738", "RefComponent": "MM-IS-IC-RPT", "RefTitle": "Texts:consumption val., rqmnt value, val.dead stock", "RefUrl": "/notes/84738 "}, {"RefNumber": "76003", "RefComponent": "MM-IS-IC-RPT", "RefTitle": "Wrong document analysis for consumption to the day", "RefUrl": "/notes/76003 "}, {"RefNumber": "75502", "RefComponent": "MM-IS-IC", "RefTitle": "No change documents during ABC analysis", "RefUrl": "/notes/75502 "}, {"RefNumber": "68265", "RefComponent": "MM-IS-IC", "RefTitle": "Reading the requirements is incorrect", "RefUrl": "/notes/68265 "}, {"RefNumber": "92659", "RefComponent": "MM-IS-IC", "RefTitle": "\"SAPLMCB3\" \"CALL_FUNCTION_PARM_MISSING\"", "RefUrl": "/notes/92659 "}, {"RefNumber": "91966", "RefComponent": "MM-IS-IC", "RefTitle": "Synt.error after importing Hot Pack. no. 7 Rel.3.1H", "RefUrl": "/notes/91966 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30C", "To": "31H", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 31H", "SupportPackage": "SAPKH31H07", "URL": "/supportpackage/SAPKH31H07"}, {"SoftwareComponentVersion": "SAP_APPL 31H", "SupportPackage": "SAPKH31H10", "URL": "/supportpackage/SAPKH31H10"}, {"SoftwareComponentVersion": "SAP_HR 31H", "SupportPackage": "SAPKE31H07", "URL": "/supportpackage/SAPKE31H07"}, {"SoftwareComponentVersion": "SAP_HR 31H", "SupportPackage": "SAPKE31H10", "URL": "/supportpackage/SAPKE31H10"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 1, "URL": "/corrins/0000086037/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}