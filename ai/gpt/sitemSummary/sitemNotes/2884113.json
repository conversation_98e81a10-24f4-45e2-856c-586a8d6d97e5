{"Request": {"Number": "2884113", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 666, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000119902020"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002884113?language=E&token=5A23997E619E579C5615F53E96CF763E"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002884113", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002884113/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2884113"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.08.2020"}, "SAPComponentKey": {"_label": "Component", "value": "FI-LOC-CER-AO-BIL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Digital Signature"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Localizations", "value": "FI-LOC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-LOC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Certification", "value": "FI-LOC-CER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-LOC-CER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Angola", "value": "FI-LOC-CER-AO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-LOC-CER-AO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Digital Signature", "value": "FI-LOC-CER-AO-BIL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-LOC-CER-AO-BIL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2884113 - Digital Signature Cross-Country Correction: Cannot implement note 2827101 due to error - LS_BUS_TAX has no component called TAX_NUMBER_XL"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>While implementing SAP note&#160;2827101 you are facing error: LS_BUS_TAX has no component called TAX_NUMBER_XL</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Digital&#160;Signature, RDSV,&#160;RDSV_BUPA_PAI_BUP100,&#160;RDSV_BUPA_PAI_BUTX01,&#160;TAX_NUMBER_XL,&#160;XO_MEMORY_FACTORY=============CP,&#160;XO_MEMORY_FACTORY=============CM009,&#160;GET_INSTANCE,&#160;ASSERTION_FAILED,&#160;CA-FS-XO</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>In some customer systems the field TAX_NUMBER_XL of DDIC structure BUS_TAX is not available.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>As a general rule SAP recommends that you implement the Support Package specified for your release. However, if you need to install a solution earlier, use the SAP Note Assistant to implement the correction instruction. You can find more information about the SAP Note Assistant in SAP Support Portal, under SAP Note Assistant.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>VERSION</strong></td>\r\n<td><strong>DATE</strong></td>\r\n<td><strong>REASON</strong></td>\r\n</tr>\r\n<tr>\r\n<td>1</td>\r\n<td>27.01.2020</td>\r\n<td>Pilot Release for SAP_APPL 600 and SAP_APPL 618&#160;</td>\r\n</tr>\r\n<tr>\r\n<td>4</td>\r\n<td>05.02.2002</td>\r\n<td>Pilot Release SAP_APPL 617 added</td>\r\n</tr>\r\n<tr>\r\n<td>5</td>\r\n<td>27.02.2020</td>\r\n<td>Release for all customers</td>\r\n</tr>\r\n<tr>\r\n<td>6 - 7</td>\r\n<td>04.03.2020</td>\r\n<td>S4CORE 100 added</td>\r\n</tr>\r\n<tr>\r\n<td>8</td>\r\n<td>26.08.2020</td>\r\n<td>Search Terms updated</td>\r\n</tr>\r\n</tbody>\r\n</table></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "C5265470"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D071014)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002884113/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002884113/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002884113/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002884113/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002884113/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002884113/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002884113/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002884113/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002884113/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2964641", "RefComponent": "IS-U-MD-BP", "RefTitle": "Dump XO_MEMORY_FACTORY ============= CP when changing a business partner", "RefUrl": "/notes/2964641 "}, {"RefNumber": "2792765", "RefComponent": "IS-U-MD-BP", "RefTitle": "FAQ: IS-UT upgrade S4", "RefUrl": "/notes/2792765 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "616", "To": "616", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "618", "To": "618", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "619", "To": "619", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "S4CORE 100", "SupportPackage": "SAPK-10011INS4CORE", "URL": "/supportpackage/SAPK-10011INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 101", "SupportPackage": "SAPK-10109INS4CORE", "URL": "/supportpackage/SAPK-10109INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 102", "SupportPackage": "SAPK-10206INS4CORE", "URL": "/supportpackage/SAPK-10206INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 103", "SupportPackage": "SAPK-10304INS4CORE", "URL": "/supportpackage/SAPK-10304INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 104", "SupportPackage": "SAPK-10402INS4CORE", "URL": "/supportpackage/SAPK-10402INS4CORE"}, {"SoftwareComponentVersion": "SAP_APPL 600", "SupportPackage": "SAPKH60033", "URL": "/supportpackage/SAPKH60033"}, {"SoftwareComponentVersion": "SAP_APPL 602", "SupportPackage": "SAPKH60223", "URL": "/supportpackage/SAPKH60223"}, {"SoftwareComponentVersion": "SAP_APPL 603", "SupportPackage": "SAPKH60322", "URL": "/supportpackage/SAPKH60322"}, {"SoftwareComponentVersion": "SAP_APPL 604", "SupportPackage": "SAPKH60423", "URL": "/supportpackage/SAPKH60423"}, {"SoftwareComponentVersion": "SAP_APPL 605", "SupportPackage": "SAPKH60520", "URL": "/supportpackage/SAPKH60520"}, {"SoftwareComponentVersion": "SAP_APPL 606", "SupportPackage": "SAPKH60626", "URL": "/supportpackage/SAPKH60626"}, {"SoftwareComponentVersion": "SAP_APPL 617", "SupportPackage": "SAPKH61721", "URL": "/supportpackage/SAPKH61721"}, {"SoftwareComponentVersion": "SAP_APPL 618", "SupportPackage": "SAPK-61815INSAPAPPL", "URL": "/supportpackage/SAPK-61815INSAPAPPL"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 9, "URL": "/corrins/0002884113/1"}, {"SoftwareComponent": "S4CORE", "NumberOfCorrin": 5, "URL": "/corrins/0002884113/19773"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 14, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2827101 ", "URL": "/notes/2827101 ", "Title": "Digital Signature Cross-Country: FI, SD and Cross-Application (NOT Applicable for Release S4CORE 104)", "Component": "FI-LOC-CER-AO-BIL"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2827101 ", "URL": "/notes/2827101 ", "Title": "Digital Signature Cross-Country: FI, SD and Cross-Application (NOT Applicable for Release S4CORE 104)", "Component": "FI-LOC-CER-AO-BIL"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "102", "ValidTo": "102", "Number": "2827101 ", "URL": "/notes/2827101 ", "Title": "Digital Signature Cross-Country: FI, SD and Cross-Application (NOT Applicable for Release S4CORE 104)", "Component": "FI-LOC-CER-AO-BIL"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "103", "ValidTo": "103", "Number": "2827101 ", "URL": "/notes/2827101 ", "Title": "Digital Signature Cross-Country: FI, SD and Cross-Application (NOT Applicable for Release S4CORE 104)", "Component": "FI-LOC-CER-AO-BIL"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "2827101 ", "URL": "/notes/2827101 ", "Title": "Digital Signature Cross-Country: FI, SD and Cross-Application (NOT Applicable for Release S4CORE 104)", "Component": "FI-LOC-CER-AO-BIL"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "2827101 ", "URL": "/notes/2827101 ", "Title": "Digital Signature Cross-Country: FI, SD and Cross-Application (NOT Applicable for Release S4CORE 104)", "Component": "FI-LOC-CER-AO-BIL"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "2827101 ", "URL": "/notes/2827101 ", "Title": "Digital Signature Cross-Country: FI, SD and Cross-Application (NOT Applicable for Release S4CORE 104)", "Component": "FI-LOC-CER-AO-BIL"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "604", "ValidTo": "604", "Number": "2827101 ", "URL": "/notes/2827101 ", "Title": "Digital Signature Cross-Country: FI, SD and Cross-Application (NOT Applicable for Release S4CORE 104)", "Component": "FI-LOC-CER-AO-BIL"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "2827101 ", "URL": "/notes/2827101 ", "Title": "Digital Signature Cross-Country: FI, SD and Cross-Application (NOT Applicable for Release S4CORE 104)", "Component": "FI-LOC-CER-AO-BIL"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "2827101 ", "URL": "/notes/2827101 ", "Title": "Digital Signature Cross-Country: FI, SD and Cross-Application (NOT Applicable for Release S4CORE 104)", "Component": "FI-LOC-CER-AO-BIL"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "2827101 ", "URL": "/notes/2827101 ", "Title": "Digital Signature Cross-Country: FI, SD and Cross-Application (NOT Applicable for Release S4CORE 104)", "Component": "FI-LOC-CER-AO-BIL"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "617", "ValidTo": "617", "Number": "2827101 ", "URL": "/notes/2827101 ", "Title": "Digital Signature Cross-Country: FI, SD and Cross-Application (NOT Applicable for Release S4CORE 104)", "Component": "FI-LOC-CER-AO-BIL"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "618", "ValidTo": "618", "Number": "2827101 ", "URL": "/notes/2827101 ", "Title": "Digital Signature Cross-Country: FI, SD and Cross-Application (NOT Applicable for Release S4CORE 104)", "Component": "FI-LOC-CER-AO-BIL"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}