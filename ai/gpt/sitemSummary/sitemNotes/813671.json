{"Request": {"Number": "813671", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 266, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015839972017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000813671?language=E&token=A77F470A3BD5D0C51840CD4258A05089"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000813671", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000813671/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "813671"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Pilot Release"}, "ReleasedOn": {"_label": "Released On", "value": "28.01.2005"}, "SAPComponentKey": {"_label": "Component", "value": "IS-M-AMC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Advertising Management Classified"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Media", "value": "IS-M", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-M*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Advertising Management Classified", "value": "IS-M-AMC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-M-AMC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "813671 - Special Customizing The Washington Post from SAP AM2-465"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>How-to allocate special customizing of The Washington Post (TWP) from SAP AM2-465</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>To allocate all content of TWP-own customizing request AM2K900008<br />to build up its own customizing in The Washington Post own systems.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This note is activated customer-specific for The Washington Post only!</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>How-to manual to deliver SAP-customizing-transports to The Washington Post and installing it in TWP own systems:<br /></p> <b>1. Background:</b><br /> <UL><LI>Currently we do have one TWP-own customizing request with several<br />tasks in our SAP AG system AM2, customer-own-client 465:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Customizing Request: AM2K900008<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Client: 465<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Special Customizing Washington Post<br />After finishing all customizing activities by TWP and saving to this request/tasks it was released, included into a transport of copies (request AM2K900019) and exported. This means we got a transport-datafile R900019.AM2 and a transport-cofile K900019.AM2, which will be delivered via link to Attachment Service (SAPmats).<br /></LI></UL> <b>2. Download data- and cofile to your local system:</b><br /> <UL><LI>Described customizing above will be delivered as packed zip-file.<br />Please download this file 'Customizing_Washington_Post.zip' from SAPmats server via link - please copy &amp; paste following link to your browser:<br /><B>http://sapmats-us.sap-ag.de/download/download.cgi?id=5M5UAYZK6D6CYZBR2Q6R1DDR543H2I9GGFNBDKSZHOV2LBV90V</B><br />Be aware - this file will be deleted after 20 days! The length of the files is ~ 16 MByte.<br />Download to your local system and un-zip it. You will get<br />transport-datafile R900019.AM2 and a transport-cofile K900019.AM2.<br /></LI></UL><b>3. Install files on your system:</b><br /> <UL><LI>Please copy this files to the transport directory<br />of your target system as described in the following way:</LI></UL> <UL><UL><LI>You can display the transport directory using Transaction AL11 under DIR_TRANS.</LI></UL></UL> <UL><UL><LI>For copying data-file use binary-mode and for cofile use ASCII-mode!</LI></UL></UL> <UL><UL><LI>Copy data-file R900019.AM2 using&#x00A0;&#x00A0;binary-mode to &lt;DIR_TRANS&gt;/data</LI></UL></UL> <UL><UL><LI>Copy cofile K900019.AM2 using ASCII-mode to &lt;DIR_TRANS&gt;/cofile<br /><br /></LI></UL></UL> <UL><LI>Add to transport buffer and import to target system + client</LI></UL> <UL><UL><LI>Call transaction STMS</LI></UL></UL> <UL><UL><LI>Switch to 'Overview' -&gt; 'Imports'</LI></UL></UL> <UL><UL><LI>Choose your target system</LI></UL></UL> <UL><UL><LI>Switch to 'Extras' -&gt; 'Other Requests' -&gt; 'Add'</LI></UL></UL> <UL><UL><LI>Fill in Transp. Request no. AM2K900019</LI></UL></UL> <UL><UL><LI>Enter</LI></UL></UL> <UL><UL><LI>Mark request AM2K900019</LI></UL></UL> <UL><UL><LI>Switch to 'Request' -&gt; 'Import'</LI></UL></UL> <UL><UL><LI>Choose Target Client (do not choose client 000 ! )</LI></UL></UL> <UL><UL><LI>Switch to Tabrider 'Options' and select 'Ignore Non-Permitted Transport type'</LI></UL></UL> <UL><UL><LI>Select other options if you want/need</LI></UL></UL> <UL><UL><LI>Import via pressing Continue/Enter</LI></UL></UL> <UL><UL><LI>after import is finished successful: check import logs via 'Request' -&gt; 'Display' -&gt; 'Logs'<br /><br /></LI></UL></UL><UL><LI>To copy/import content of single customizing requests to other clients</LI></UL> <UL><UL><LI>Logon to target client</LI></UL></UL> <UL><UL><LI>Call transaction SCC1</LI></UL></UL> <UL><UL><LI>Fill in Source client and desired customizing request and follow the screen description.</LI></UL></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D033006)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D033006)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000813671/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000813671/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000813671/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000813671/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000813671/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000813671/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000813671/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000813671/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000813671/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-M/AMC", "From": "1.0", "To": "1.0", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}