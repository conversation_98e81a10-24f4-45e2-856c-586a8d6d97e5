{"Request": {"Number": "840901", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 360, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015887912017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000840901?language=E&token=E885CA5CE8ABD9CAE253889871901FB8"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000840901", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000840901/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "840901"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.08.2016"}, "SAPComponentKey": {"_label": "Component", "value": "BC-SRV-NUM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Number Range Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basis Services/Communication Interfaces", "value": "BC-SRV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SRV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Number Range Management", "value": "BC-SRV-NUM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SRV-NUM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "840901 - Parallel buffering and pseudo-ascending number assignment"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are running or would like to run 'parallel buffering' for number ranges in accordance with Note 599157.<br />For legal reasons, it is sometimes necessary for numbers to be assigned in continuous chronological ascending order.<br />However, it is not logically possible to assign numbers that are simultaneously continuous, chronological and buffered.<br />For this reason, this note offers you a solution that can still fulfill the legal requirements.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>NRIVSHADOW, parallel buffering, Italy</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>For number range intervals that are not buffered, a lock is set on the interval as soon as a number has been assigned. This lock is only removed again once the application that has taken the number ends the LUW (Logical Unit of Work) with a COMMIT or a ROLLBACK.<br />This ensures that if there is a rollback, the number that was just taken is available to the next application, and therefore continuity is guaranteed. In addition, the next highest number in the interval can only be assigned once the first number is definitively assigned. This ensures that the system strictly adheres to the ascending order.<br />A huge disadvantage of this lock is that the application sometimes requires a very long time between assigning the number and the final COMMIT, and this has a considerable effect on system performance (see Note 678501).</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Thus, it is clear that it is logically impossible to assign numbers in a continuous chronological ascending order with a buffer. However, the new buffering type fulfills this requirement as far as possible.<br />An absolute ascending sequence is not always possible; we therefore talk about a <strong>pseudo-ascending</strong> sequence.<br />For example: Process A takes a number (buffered) and processes further whilst waiting for a COMMIT; Process B later also takes a number (buffered, higher) but sets its COMMIT earlier than A.<br /><br />Depending on when the application sets a time stamp, the time stamps might contradict the sequence. (Note here that if these time stamps are set in the update task, the same situation can occur with parallel updates with or without number range object buffering.) However, in most cases, it is sufficient if a lower number is not assigned the next day.<br />To ensure that there are no gaps, a number that was already assigned in the buffer is made available again in both the enhanced local buffering and in the parallel buffering, in case there is a rollback in the application due to a problem IN THE FOREGROUND. (However, if the rollback occurs in the update task, the numbers are lost forever, regardless of whether they were buffered or not). Thus, the aim is to stop these numbers from being lost; in order to prevent the lower numbers from interrupting the ascending sequence, however, they should also no longer be used.<br />Such numbers are recognized by the system and - rather than being used for documents - are written to a database table (NRIV_RESTE or NRIV_DOCU9) so that they remain documented (report RSSNR0S1).<br /><br />This special parallel buffering case cannot be activated using customizing. Instead, it requires a modification via an exit in the application. See FI note 834257, for example.<br />USEREXIT_NUMBER_RANGE can be activated for SD (RV_BELEG), as in Note 363901, but using the parameter NO_BUFFER = 'S' rather than the parameter NO_BUFFER = 'X'.<br />The prerequisite for this is that you implement the relevant Support Package (Note 599157).<br /><br />You must use transaction SNUM to set the buffering of the number range object to parallel buffering.<br /><br />In addition, the number assignment (function module NUMBER_GET_NEXT) is called with the parameter IGNORE_BUFFER = 'S'. (This call must always be the same for the same interval.) As a result, the buffer size is automatically set to 1 and the rolled back numbers are not used again. If they are still in the buffer, they are written into NRIV_RESTE. When a number is requested, a new number is assigned.<br /><br />You can use Notes 449030, 599157 and 834257 to activate this solution for the number range object RF_BELEG.</p>\r\n<p>Note that the change of the buffering type of the number range objects delivered in the standard system constitutes a modification and no support can thus be provided for resulting problems unless the modification is covered by SAP Notes for localization purposes. The modification is overwritten as soon as the affected number range object is redelivered - in other words, you must check the change manually each time you import a release.<br />Before changing the buffering of a number range object, please check carefully whether this is required - in legal terms.</p>\r\n<p>If a number range object uses main memory buffering in the standard system, the buffering type should be changed only in the case of statutory requirements.</p>\r\n<p>&#x00A0;</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D019862)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D020499)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000840901/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000840901/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000840901/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000840901/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000840901/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000840901/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000840901/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000840901/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000840901/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Buffering_FOR_ITALY.htm", "FileSize": "27", "MimeType": "text/html", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000121312005&iv_version=0007&iv_guid=3575344663C67144B4CB7D8C458675FE"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "834257", "RefComponent": "FI-GL-GL-A", "RefTitle": "FI document number assignment: Buffering using BTE 1170", "RefUrl": "/notes/834257"}, {"RefNumber": "363901", "RefComponent": "SD-BIL-IV", "RefTitle": "Country-specific buffering RV_BELEG/number assignment in SD", "RefUrl": "/notes/363901"}, {"RefNumber": "2022364", "RefComponent": "BC-SRV-NUM", "RefTitle": "NR: NUMBER_MOVE_RESTE - program termination", "RefUrl": "/notes/2022364"}, {"RefNumber": "1421287", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1421287"}, {"RefNumber": "1398444", "RefComponent": "FI-GL-GL-A", "RefTitle": "Buffering the document number assignment for RF_BELEG", "RefUrl": "/notes/1398444"}, {"RefNumber": "1091025", "RefComponent": "BC-SRV-NUM", "RefTitle": "DATA_OFFSET_TOO_LARGE in SAPLSNR3", "RefUrl": "/notes/1091025"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2689405", "RefComponent": "XX-SER-MCC", "RefTitle": "FAQ: SAP S/4HANA Performance Best Practices - Collective Note", "RefUrl": "/notes/2689405 "}, {"RefNumber": "3200907", "RefComponent": "BC-SRV-NUM", "RefTitle": "Buffering Number Ranges: performance and buffering methods", "RefUrl": "/notes/3200907 "}, {"RefNumber": "1843002", "RefComponent": "BC-SRV-NUM", "RefTitle": "Gaps and jumps in number range assignment", "RefUrl": "/notes/1843002 "}, {"RefNumber": "2556454", "RefComponent": "BC-SRV-NUM", "RefTitle": "Error \"Mixed requests for numbers (Italian and parallel)\" in system log", "RefUrl": "/notes/2556454 "}, {"RefNumber": "2184567", "RefComponent": "FI-CF", "RefTitle": "Central Finance: Frequently Asked Questions (FAQ)", "RefUrl": "/notes/2184567 "}, {"RefNumber": "2474833", "RefComponent": "XX-PROJ-FI-CA", "RefTitle": "FI-CA: Enabling number of jobs in mass activities > 999", "RefUrl": "/notes/2474833 "}, {"RefNumber": "504875", "RefComponent": "BC-SRV-NUM", "RefTitle": "Buffering of number ranges", "RefUrl": "/notes/504875 "}, {"RefNumber": "2042606", "RefComponent": "XX-PART-IPS", "RefTitle": "IP and DMP numbers not assigned continuously", "RefUrl": "/notes/2042606 "}, {"RefNumber": "1911051", "RefComponent": "SD-BIL-IV", "RefTitle": "Country-specific buffering RV_BELEG / number assignment in SD (2)", "RefUrl": "/notes/1911051 "}, {"RefNumber": "1249480", "RefComponent": "SD-BIL-IV", "RefTitle": "Country-specific buffering RV_BELEG/number assignment in SD (mod/example)", "RefUrl": "/notes/1249480 "}, {"RefNumber": "862597", "RefComponent": "SD-BIL-IV", "RefTitle": "Display of document number gaps for SD billing documents", "RefUrl": "/notes/862597 "}, {"RefNumber": "1679514", "RefComponent": "XX-PART-DTM", "RefTitle": "Material, customer, vendor numbers not assigned continuously", "RefUrl": "/notes/1679514 "}, {"RefNumber": "363901", "RefComponent": "SD-BIL-IV", "RefTitle": "Country-specific buffering RV_BELEG/number assignment in SD", "RefUrl": "/notes/363901 "}, {"RefNumber": "1398444", "RefComponent": "FI-GL-GL-A", "RefTitle": "Buffering the document number assignment for RF_BELEG", "RefUrl": "/notes/1398444 "}, {"RefNumber": "1421287", "RefComponent": "ICM-PR-TA", "RefTitle": "ICM document number assignment : Buffering using user exit", "RefUrl": "/notes/1421287 "}, {"RefNumber": "1091025", "RefComponent": "BC-SRV-NUM", "RefTitle": "DATA_OFFSET_TOO_LARGE in SAPLSNR3", "RefUrl": "/notes/1091025 "}, {"RefNumber": "834257", "RefComponent": "FI-GL-GL-A", "RefTitle": "FI document number assignment: Buffering using BTE 1170", "RefUrl": "/notes/834257 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "620", "To": "640", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "711", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "730", "To": "730", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "740", "To": "740", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "750", "To": "752", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "771", "To": "772", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}