{"Request": {"Number": "792941", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 209, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000792941?language=E&token=2F09A3837A33935AC89BB3947D9EFD58"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000792941", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000792941/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "792941"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "15.12.2004"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SDD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Service Data Download"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Data Download", "value": "SV-SMG-SDD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SDD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "792941 - Maintenance of transaction SDCC or SDCCN"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You need to find out how SDCC or SDCCN are maintained for your basis release</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SDCC, SDCCN, EWA, Earlywatch Alert, SAP Solution Manager, Remote Service<br />Service Data Control Center</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You want to collect data for a service session.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>From Spring 2005 a new version of the Service Data Control Center<br />will be available for basis releases 4.x and 6.x.&#x00A0;&#x00A0;With this version<br />the old transaction SDCC will be replaced by transaction SDCCN.<br />Until SDCCN is generally released, please make sure that the latest<br />version of SDCC has been implemented.<br />This note explains how the Service Data Control Center can be maintained<br />for your basis release.<br /><br /><br /><B>I. Service Data Control Center - Transaction SDCC</B></p> <OL>1. <B>Basis releases 3.x</B><br /><br /><B>Basis Releases 3.0D/3.0F</B><br /><br />Step 1:&#x00A0;&#x00A0;Implement basis transport from Note 597323<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; ( contains low SDCC version + function modules + RTCCTOOL)<br /><br />Step 2:&#x00A0;&#x00A0;Update SDCC to version 2.3 with transport from note 178631<br /><br /><B>Basis Releases 3.1 H + 3.1 I</B><br /><br />Implement basis transport from Note 597323<br />( contains SDCC 2.3 + function modules + RTCCTOOL)<br /></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;No ST-PI was developed for basis release 3. x. All these systems must not refresh service definitions from SAPNet R3/Frontend ( OSS) anymore, as this will now cause shortdumps. They must therefore also implement note 712757.<br /> <OL>2. <B>Basis releases 4.x</B></OL> <OL><OL>a) Newest version of SDCC 2.4<br />Implement add on ST-PI 003C*<br />(contains newest version of SDCC 2.4 + function modules)<br /><br />Strategy note for ST-PI: 539977<br />Support Packages for ST-PI: see note 539977<br /></OL></OL> <OL><OL>b) Systems without ST-PI must have at least SDCC version 2.3. The old basis transport (contains SDCC 2.3, function modules + RTCCTOOL) must be requested through a message as described in note 560630<br />They also must switch off Service Definition refreshes, as per note 713674.<br />Note that this version of SDCC is unable to collect many of the newer function modules.<br /></OL></OL> <OL>3. <B>Basis releases 6.x</B><br />In basis release 610 SDCC is maintained via basis support package, up until SP41. Ditto for basis release 620, SP45.<br />In addition&#x00A0;&#x00A0;an add on ST-PI was provided,&#x00A0;&#x00A0;which contains mostly function modules. The last ST-PI of this type for basis release 6.x was ST-PI 003C_6*. The last support package for ST-PI 003C_6* will be SP3.<br /><br /><br /><B>II. Service Data Control Centre - Transaction SDCCN</B><br /></OL> <OL>4. <B>Basis releases 3.x</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;- not available<br /></OL> <OL>5. <B>Basis releases 4.x</B><br />For basis releases 4.x&#x00A0;&#x00A0;SDCCN is available from add on ST-PI 2005_1.<br />This contains SDCCN and some function modules. Corrections are contained in support packages for ST-PI.<br /></OL> <OL>6. <B>Basis releases 6.x</B><br />For basis releases 6.x&#x00A0;&#x00A0;SDCCN is available from add on ST-PI 2005_1.<br />This contains SDCCN and some function modules. Corrections are contained in support packages for ST-PI.<br /></OL> <p>Note 539977 will remain the strategy note for ST-PI, it will advise you when new versions of ST-PI or new support packages for ST-PI become available. As per note 91488, we strongly recommend the use of RTCCTOOL (note 69455), as this tool will automatically advise you of the newest ST-PI versions. Never implement versions of ST-PI which have not been released for general use.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D038895)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D038895)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000792941/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000792941/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000792941/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000792941/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000792941/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000792941/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000792941/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000792941/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000792941/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488"}, {"RefNumber": "883111", "RefComponent": "SV-SMG-SDD", "RefTitle": "Deactivating old EarlyWatchAlert (Transaction SCUI)", "RefUrl": "/notes/883111"}, {"RefNumber": "763561", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCCN)  - FAQ", "RefUrl": "/notes/763561"}, {"RefNumber": "539977", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for add-on ST-PI", "RefUrl": "/notes/539977"}, {"RefNumber": "216952", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCC) - FAQ", "RefUrl": "/notes/216952"}, {"RefNumber": "116095", "RefComponent": "SV-SMG-SDD", "RefTitle": "Solution Tools Plug-In (TCC Basis Tools and Trace Tools)", "RefUrl": "/notes/116095"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "539977", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for add-on ST-PI", "RefUrl": "/notes/539977 "}, {"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488 "}, {"RefNumber": "763561", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCCN)  - FAQ", "RefUrl": "/notes/763561 "}, {"RefNumber": "216952", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCC) - FAQ", "RefUrl": "/notes/216952 "}, {"RefNumber": "883111", "RefComponent": "SV-SMG-SDD", "RefTitle": "Deactivating old EarlyWatchAlert (Transaction SCUI)", "RefUrl": "/notes/883111 "}, {"RefNumber": "116095", "RefComponent": "SV-SMG-SDD", "RefTitle": "Solution Tools Plug-In (TCC Basis Tools and Trace Tools)", "RefUrl": "/notes/116095 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}