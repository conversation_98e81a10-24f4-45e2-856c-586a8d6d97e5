{"Request": {"Number": "874471", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 249, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016008182017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000874471?language=E&token=9E5CE43E4839216227F5AC74C1C88B43"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000874471", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000874471/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "874471"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.05.2010"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-ADDON"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade Add-On Components"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade Add-On Components", "value": "BC-UPG-ADDON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-ADDON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "874471 - Additions for installing or activating ECC-DIMP on ECC 6.0"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Activating Industry Extension ECC-DIMP.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>You want to install an add-on for the business function set DIMP.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This note tells you how to activate the Industry Extension ECC-DIMP 6.0 in the Switch Framework.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br /><B>Implement the current version of this note before you start the installation process.</B><br />Contents<br />&#x00A0;&#x00A0;1. Change history<br />&#x00A0;&#x00A0;2. Prerequisites for activating ECC-DIMP 6.0<br />&#x00A0;&#x00A0;3. Activating ECC-DIMP 6.0<br />&#x00A0;&#x00A0;4. After you activate ECC-DIMP 6.0<br />&#x00A0;&#x00A0;5. Language support<br /></p> <b>1. Change history</b><br /> <p></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>Date</TD><TD> Topic</TD><TD> Short description</TD></TR> <TR><TD>20.12.2005</TD><TD> 2.</TD><TD> Required Support Packages</TD></TR> <TR><TD>20.12.2005</TD><TD> 4.</TD><TD> Importing SAP_APPL Support Packages</TD></TR> <TR><TD></TD></TR> </TABLE> <b>2. Prerequisites for activating ECC-DIMP 6.0</b><br /> <UL><LI>Deactivating ECC-DIMP<br />Before you activate ECC-DIMP, note it is not possible to deactivate the Industry Extension ECC-DIMP. Release Strategy Note 874473 provides more information about the consequences activating ECC-DIMP has for your ERP system.</LI></UL> <UL><LI>Required Release<br />SAP Enterprise Central Component 6.0 is required as a prerequisite.</LI></UL> <UL><LI>Required Support Packages<br />The Industry Extension ECC-DIMP 6.0 is dependent on the SAP_APPL component. Before the activation, you must adjust the Support Package level of the ECC-DIMP component to the SAP_APPL Support Package level in the system.</LI></UL> <UL><LI>You should obtain and read the following notes thoroughly before you activate the component:</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD></TD></TR> <TR><TD>Release Strategy Note</TD><TD> 874473</TD></TR> <TR><TD>Release restrictions for ECC-DIMP in ERP 2005 </TD><TD> 865969</TD></TR> <TR><TD>Note for avoiding loss of data </TD><TD> 849981</TD></TR> <TR><TD>Corrected report for activating DIMP using LAMA/MPN </TD><TD> 890513</TD></TR> <TR><TD></TD></TR> </TABLE></UL> <b>3. Activating ECC-DIMP 6.0</b><br /> <UL><LI>User to be used:<br />Log onto your ERP system as a user with SAP_ALL authorization. Do NOT use the SAP* or DDIC users.</LI></UL> <UL><LI>Starting the activation process:<br />Call transaction SFW5. Choose the business function set \"Discrete Industries / Mill Products\". Select the business function you want. Now choose the activation button. This starts a background job SFW_ACTIVATE_SF0X that activates the industry extension.</LI></UL> <UL><LI>In transaction SM37, you can track the status of the background job.</LI></UL> <UL><LI>You can use the following paths to display the logs: Transaction SFW5 -&gt; Goto -&gt; DDIC Logs<br />SFW5 -&gt; Goto -&gt; Switch Framework Logs<br /></LI></UL> <b>4. After you activate ECC-DIMP 6.0</b><br /> <UL><LI>Importing Support Packages after the installation:</LI></UL> <UL><UL><LI>The Industry Extension ECC-DIMP 6.0 is dependent on the SAP_APPL, EA-APPL and EA-IPPE components.</LI></UL></UL> <UL><UL><LI>If you import a Support Package for SAP_APPL, you must also import a corresponding Support Package for the ECC-DIMP component. This is available at the same time as the SAP_APPL Support Package.</LI></UL></UL> <UL><UL><LI>If you want to import an ECC-DIMP Support Package, the system will prompt you to import the corresponding Support Packages for the SAP_APPL, EA-APPL and EA-IPPE.</LI></UL></UL> <UL><UL><LI>You can find the Support Packages on SAP Service Marketplace at service.sap.com/patches.</LI></UL></UL> <UL><UL><LI>You can import the Support Packages for components on which ECC-DIMP is not dependent without importing ECC-DIMP Support Packages.</LI></UL></UL> <UL><LI>Delivery Customizing:</LI></UL> <UL><UL><LI>Unpack the Delivery Customizing in client 000, and copy it into other clients if necessary or adjust it in existing clients. For more information, see Note 337623.<br />If the business functions that you have activated use special Customizing, refer to Note 868771.<br /></LI></UL></UL> <b>5. Language support</b><br /> <UL><LI>In addition to German and English, IECC-DIMP 6.0 supports Czech, Danish, Greek, Spanish, French, Italian, Japanese, Korean, Dutch, Polish, Portuguese, Slovenian, Turkish and Chinese.<br />Hebrew is delivered by a partner. If you want to install Hebrew, contact your local support.<br /><br />If you have activated ECC-DIMP 600 and want to install an additional language that is released for it, proceed as described in Note 876718.</LI></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D033006)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D016179)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000874471/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000874471/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000874471/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000874471/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000874471/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000874471/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000874471/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000874471/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000874471/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "983876", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Installing SAP DBM 6.0 on SAP ECC 600/ERP 2005", "RefUrl": "/notes/983876"}, {"RefNumber": "917999", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "PARCONV_UPG terminates with TSV_TNEW_PAGE_ALLOG_FAILED", "RefUrl": "/notes/917999"}, {"RefNumber": "890513", "RefComponent": "IS-A-LMN", "RefTitle": "Corrected report for activating DIMP with LAMA or MPN", "RefUrl": "/notes/890513"}, {"RefNumber": "889740", "RefComponent": "IS-A-LMN", "RefTitle": "Correction program for the LAMA/MPN screen after upgrade", "RefUrl": "/notes/889740"}, {"RefNumber": "874473", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for ECC-DIMP as of ECC 6.0", "RefUrl": "/notes/874473"}, {"RefNumber": "865969", "RefComponent": "IS-ADEC", "RefTitle": "Current release restrictions for ERP 2005 - ECC-DIMP", "RefUrl": "/notes/865969"}, {"RefNumber": "856284", "RefComponent": "XX-SER-REL", "RefTitle": "SAP ERP Central Component 6.0: Inst./upgrad and architecture", "RefUrl": "/notes/856284"}, {"RefNumber": "1879011", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Activation of HANA specific DIMP Content", "RefUrl": "/notes/1879011"}, {"RefNumber": "1132702", "RefComponent": "IS-A-LMN", "RefTitle": "Upgrade ECC-DIMP600 is not completed", "RefUrl": "/notes/1132702"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1879011", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Activation of HANA specific DIMP Content", "RefUrl": "/notes/1879011 "}, {"RefNumber": "1824376", "RefComponent": "BC-DWB-TOO-SFW", "RefTitle": "SFW5 does not check components for currently selected BF Set", "RefUrl": "/notes/1824376 "}, {"RefNumber": "865969", "RefComponent": "IS-ADEC", "RefTitle": "Current release restrictions for ERP 2005 - ECC-DIMP", "RefUrl": "/notes/865969 "}, {"RefNumber": "983876", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Installing SAP DBM 6.0 on SAP ECC 600/ERP 2005", "RefUrl": "/notes/983876 "}, {"RefNumber": "1132702", "RefComponent": "IS-A-LMN", "RefTitle": "Upgrade ECC-DIMP600 is not completed", "RefUrl": "/notes/1132702 "}, {"RefNumber": "856284", "RefComponent": "XX-SER-REL", "RefTitle": "SAP ERP Central Component 6.0: Inst./upgrad and architecture", "RefUrl": "/notes/856284 "}, {"RefNumber": "874473", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for ECC-DIMP as of ECC 6.0", "RefUrl": "/notes/874473 "}, {"RefNumber": "890513", "RefComponent": "IS-A-LMN", "RefTitle": "Corrected report for activating DIMP with LAMA or MPN", "RefUrl": "/notes/890513 "}, {"RefNumber": "849981", "RefComponent": "IS-A-LMN", "RefTitle": "DIMP: Upgrade to ERP 2005", "RefUrl": "/notes/849981 "}, {"RefNumber": "917999", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "PARCONV_UPG terminates with TSV_TNEW_PAGE_ALLOG_FAILED", "RefUrl": "/notes/917999 "}, {"RefNumber": "889740", "RefComponent": "IS-A-LMN", "RefTitle": "Correction program for the LAMA/MPN screen after upgrade", "RefUrl": "/notes/889740 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": "X"}, {"SoftwareComponent": "ECC-DIMP", "From": "600", "To": "600", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}