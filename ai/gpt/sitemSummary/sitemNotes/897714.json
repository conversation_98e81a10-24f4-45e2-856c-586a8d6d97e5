{"Request": {"Number": "897714", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 409, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016013722017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000897714?language=E&token=B292E97858F082A5DA1701A850B181BA"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000897714", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000897714/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "897714"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 13}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "03.02.2020"}, "SAPComponentKey": {"_label": "Component", "value": "IS-R-STR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Retail Short Text Replacement"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-Specific Component Retail", "value": "IS-R", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-R*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Retail Short Text Replacement", "value": "IS-R-STR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-R-STR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "897714 - Activating Retail Short Text Replacement"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Your SAP Retail ERP system user interface shows standard texts instead of retail-specific texts.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Material article, plant, site</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You need to rerun the Retail Short Text Replacement.<br />This note is only valid for languages and releases SAP delivers retail replacement texts for. You can get the current file for your release and language by opening an incident for component IS-R-STR. Please note that not every language is supported.</p>\r\n<p>The files for S4HANA can be found in note <span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: DE; mso-bidi-language: AR-SA;\">2712569.</span></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ol>1. Log on to your SAP system, client 000. The user should have the necessary authorizations&#160;to use transactions SE38 and STMS_IMPORT.</ol><ol>2. You need the newest&#160;short text replacement archive file for your release and language. If you don't have this file available, please open an incident for component IS-R-STR. If you already have the current file please proceed with step 7.</ol><ol>3. To reset the system to standard texts, start report RSBRAN03 and fill the selection fields as follows:</ol>\r\n<ul>\r\n<ul>\r\n<li>BRANCHE: ISR</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>LANG: &lt;your language&gt;</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RESET: checked</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;All other fields remain default.</p>\r\n<ol>4. Run the report in background (F9) and wait till it is finished.</ol><ol>5. Download the archive file and extract it to a local directory.</ol><ol><ol>6. If the archive contains R* and K* files, import the contained transport into your system using tp.</ol></ol><ol>If the archive contains a *.PAT file, import the texts using transaction SMLT.</ol><ol>7. Start report RSBRAN03 and fill the selection fields as follows:</ol>\r\n<ul>\r\n<ul>\r\n<li>BRANCHE: ISR</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>LANG: &lt;your language&gt;</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;All other fields remain default.</p>\r\n<ol>8. Run the report in background (F9).</ol><ol>9. After the job finished, reset the text buffers by entering /$SYNC in the command line.</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Please note: Resetting buffers can significantly change the performance of the entire system. Nevertheless the reset of the buffers is necessary.</p>\r\n<ol>10. Please note that the report must be executed in every system separately.</ol>\r\n<p><br />Do not check CL_FORCE and UP_FORCE because they are reserved for SAP internal services.<br /><br />Please note that only short texts will be replaced. All long texts like F1 help, generated HTML help and forms will continue to use standard terminology. Also customer-specific texts will not be touched.<br /><br />To return to standard terms, you can run report RSBRAN03 again with field RESET checked additionally.<br /><br />During language imports, texts in standard terminology can overwrite already replaced retail texts. In this case, please repeat the activation of the retail short texts after the import. We recommend scheduling RSBRAN03 periodically.<br /><br />If you should still find some texts in standard terminology, please be sure you installed the latest text files from SAP Service Marketplace as described above. If the problem persists you can replace the terminology manually. Please refer to SAP Note 904437.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D019793)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D024566)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000897714/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000897714/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000897714/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000897714/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000897714/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000897714/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000897714/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000897714/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000897714/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "922250", "RefComponent": "BC-DOC-TTL", "RefTitle": "The most important program corrections for RSBRAN03", "RefUrl": "/notes/922250"}, {"RefNumber": "904437", "RefComponent": "IS-R", "RefTitle": "Some short texts contain std terms instead of retail terms", "RefUrl": "/notes/904437"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2776375", "RefComponent": "IS-R-STR", "RefTitle": "Note 897714 contains outdated URL for Retail Short Text Replacement files", "RefUrl": "/notes/2776375 "}, {"RefNumber": "2480985", "RefComponent": "BC-UPG-OCS-SPA", "RefTitle": "Texts changed after ABAP Support Package import", "RefUrl": "/notes/2480985 "}, {"RefNumber": "2712569", "RefComponent": "LO-RFM", "RefTitle": "Retail Term Replacement in S/4 for 1709 ff", "RefUrl": "/notes/2712569 "}, {"RefNumber": "2185631", "RefComponent": "XX-TRANSL-ZF", "RefTitle": "Correction of Replacement Term \"Article\" after Retail Short Tex Replacement EHP 7", "RefUrl": "/notes/2185631 "}, {"RefNumber": "922250", "RefComponent": "BC-DOC-TTL", "RefTitle": "The most important program corrections for RSBRAN03", "RefUrl": "/notes/922250 "}, {"RefNumber": "904437", "RefComponent": "IS-R", "RefTitle": "Some short texts contain std terms instead of retail terms", "RefUrl": "/notes/904437 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}