{"Request": {"Number": "822271", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 365, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015855332017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000822271?language=E&token=8E09BB2A738C981956FE4909C04D0B62"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000822271", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000822271/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "822271"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "07.05.2018"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-SDB"}, "SAPComponentKeyText": {"_label": "Component", "value": "MaxDB"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "MaxDB", "value": "BC-DB-SDB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-SDB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "822271 - FAQ: SAP MaxDB client software"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note is a collection of the questions that are often asked by colleagues and customers regarding SAP MaxDB client software. It provides answers and refers you to other information sources. The note is in no way complete.</p>\r\n<ol>\r\n<li>What is the purpose of the SAP MaxDB client software?</li>\r\n<li>What is the purpose of the library dbadaslib or dbsdbslib?</li>\r\n<li>As of which SAP kernel version will the dbadaslib be replaced with dbsdbslib?</li>\r\n<li>Is dbadaslib or dbsdbslib part of the SAP MaxDB client software?</li>\r\n<li>Which software components are included in the SAP MaxDB client software?</li>\r\n<li>Where can I find a description of how to install SAP MaxDB client software?</li>\r\n<li>How do I uninstall SAP MaxDB client software again?</li>\r\n<li>Where can I find a description of how to install the library dbadaslib or dbsdbslib?</li>\r\n<li>How is the version of the SAP MaxDB client software dependent on the version of the SAP MaxDB?</li>\r\n<li>How is the release of the library dbadaslib or dbsdbslib dependent on the version of the SAP MaxDB Precompiler Runtime?</li>\r\n<li>Which directories contain the components of the SAP MaxDB client software?</li>\r\n<li>Where can I find information about which components are contained in the SAP MaxDB client software?</li>\r\n<li>Which environment variables are important when you access the SAP MaxDB client software?</li>\r\n<li>The SAP recommendations for &lt;LIBRARYPATH&gt; are confusing and sometimes appear contradictory. How can I set these environment variables correctly?</li>\r\n<li>Is it unnecessary to install the SAP MaxDB client software on a host on which the database and SAP system are installed?</li>\r\n<li>Where can I find the current patches for the SAP MaxDB client software?</li>\r\n<li>If the SAP MaxDB client software is installed incorrectly, which errors may occur?</li>\r\n<li>Where do I find more information about SAP MaxDB client software?</li>\r\n</ol>\r\n<p>&#x00A0;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>FAQ, client software, dbadaslib, dbsdbslib, Precompiler Runtime, SQLDBC, DBMCLI</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You are using an SAP MaxDB database or SAP liveCache technology in your SAP system.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ol>\r\n<li>What is the purpose of the SAP MaxDB client software?<br />You require SAP MaxDB client software to communicate between application servers and the SAP MaxDB database. Pure application servers do not have any access to the SAP MaxDB server software. You must therefore also install special parts of the software (for example, DBMCLI, DBM Server, and the precompiler runtime/SQLDBC) on the application servers.<br />In a central system, the client software installed with the server installation is used by the application server in database versions &lt; 7.8.<br />As of the database version 7.8, you must also perform an explicit client installation in a central system since all software parts are handled separately in the isolated installation.<br /><br /></li>\r\n<li>What is the purpose of the library dbadaslib or dbsdbslib?<br />The libraries <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;dbadaslib&#xFEFF;</em></span> and <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;dbsdbslib&#xFEFF;</em></span> are the SAP-MaxDB-specific interfaces for the database-independent SAP database interface (DBI). These DBSL libraries ensure that the SQL statements that are sent to the database are transferred in the correct syntax of the relevant database system.<br />Each of the database systems that are supported by SAP has its own database-specific DBSL library.<br />The DBSL library release (<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;dbadaslib.dll&#xFEFF;</em></span> or <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;dbsdbslib.dll&#xFEFF;</em></span>) must correspond to the SAP kernel release.<br /><br /></li>\r\n<li>As of which SAP kernel version will the dbadaslib be replaced with dbsdbslib?<br />As of SAP Kernel 7.0, <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;dbadaslib&#xFEFF;</em></span> is no longer used, and is replaced with <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;dbsdbslib&#xFEFF;</em></span>.<br />The 6.40 SAP EXT kernels continue to use <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;dbadaslib&#xFEFF;</em></span>.<br /><br /></li>\r\n<li>Is dbadaslib or dbsdbslib part of the SAP MaxDB client software?<br />No. The library <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;dbadaslib&#xFEFF;</em></span> or <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;dbsdbslib&#xFEFF;</em></span> is part of the database-independent WebAS kernel package.<br /><br /></li>\r\n<li>Which software components are included in the SAP MaxDB client software?<br />The SAP MaxDB client software consists of the following components, for example:<br />Precomplier Runtime versions<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">SQLDBC</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">JDBC</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">ODBC</span><br />The software that is required so that the database assistant and the liveCache assistant can operate (transactions DB50 and LC10).<br /><br />For more information about this, refer to SAP Note <a target=\"_blank\" href=\"/notes/649814\">649814</a>.<br /><br /></li>\r\n<li>Where can I find a description of how to install the SAP MaxDB client software?<br />SAP Note <a target=\"_blank\" href=\"/notes/649814\">649814</a> contains detailed instructions about the installation of the client software.<br /><br /></li>\r\n<li>How do I uninstall the SAP MaxDB client software again?<br />You can use the SAP MaxDB tool <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">SDBUNINST</span> to uninstall the client software. For more information about this, refer to SAP Note <a target=\"_blank\" href=\"/notes/599129\">599129</a>.<br /><br /></li>\r\n<li>Where can I find a description of how to install the library dbadaslib or dbsdbslib?<br />See the information in SAP Note <a target=\"_blank\" href=\"/notes/19466\">19466</a>. <br />For detailed instructions about installing the library <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;dbadaslib&#xFEFF;</em></span> or <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;dbsdbsli&#xFEFF;</em></span>, see SAP Note <a target=\"_blank\" href=\"/notes/325402\">325402</a>.<br /><br /></li>\r\n<li>How is the version of the SAP MaxDB client software dependent on the version of the SAP MaxDB kernel?<br />In SAP systems, the SAP kernel determines which version of the precompiler software/SQLDBC is used. Each SAP MaxDB client package therefore contains all of the Precompiler Runtimes or SQLDBC versions that are available.<br />To operate the data assistant or liveCache assistant (transactions DB50/LC10) and the DBA planning calendar (transaction DB13), you require a minimum version of the client (DBMCLI) that corresponds to the relevant SAP MaxDB kernel. After you change the database version from 7.6 to, for example, 7.7, you must therefore also update the client software on the application servers. When you apply a database patch, you do not usually have to update the client software.<br /><br /></li>\r\n<li>How is the release of the library dbadaslib or dbsdbslib dependent on the version of the SAP MaxDB Precompiler Runtime?<br />SAP Note <a target=\"_blank\" href=\"/notes/578324\">578324</a> contains information for the UNIX platforms.<br />The library <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;dbadaslib&#xFEFF;</em></span> or <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;dbsdbslib&#xFEFF;</em></span> is dynamically linked.<br /><br /></li>\r\n<li>Which directories contain the components of the SAP MaxDB client software?<br />SAP MaxDB Version &lt; 7.8: <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">INDEPPROGRAM</span> directory<br />SAP MaxDB Version&#x00A0;&gt;= 7.8: <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;ClientProgPath&#xFEFF;</em></span> directory <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;/sapdb/clients/&lt;SID&gt;&#xFEFF;</em></span>&#x00A0;(determination of path with <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;dbm_getpath&#xFEFF;</em></span>)<br />The software of the library <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;dbadaslib&#xFEFF;</em></span> or <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;dbsdbslib&#xFEFF;</em></span> is located in the SAP kernel directory.<br /><br /></li>\r\n<li>Where can I find information about which components are contained in the SAP MaxDB client software?<br />Information about the various SAP MaxDB software components is available here: <a target=\"_blank\" href=\"https://wiki.scn.sap.com/wiki/x/6Bu7Gg\">SAP MaxDB Components</a><br /><br /></li>\r\n<li>Which environment variables are important when you access the SAP MaxDB client software?<br />SAP MaxDB Version &lt; 7.8: <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">INDEPPROGRAM/bin</span> must be contained in the path variables. For Microsoft Windows, <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">INDEPPROGRAM/pgm</span> must be contained in the path variables.<br />SAP MaxDB Version&#x00A0;&gt;= 7.8: <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;ClientProgPath/bin&#xFEFF;</em></span> must be contained in the path variables.<br /><br /></li>\r\n<li>The SAP recommendations for &lt;LIBRARYPATH&gt; are confusing and sometimes appear contradictory. How can I set these environment variables correctly?<br />SAP MaxDB Version &lt; 7.8: In the environment variable <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">LD_LIBRARY_PATH</span>, <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">INDEPPROGRAM/lib</span>&#x00A0;must be contained on UNIX.<br />SAP MaxDB Version&#x00A0;&gt;= 7.8: In the environment variable <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">LD_LIBRARY_PATH</span>, <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;ClientProgPath/lib&#xFEFF;</em></span>&#x00A0;must be contained on UNIX.<br /><br /></li>\r\n<li>Is it unnecessary to install the SAP MaxDB client software on a host on which the database and SAP system are installed?<br />SAP MaxDB Version &lt; 7.8: Yes, because the server package that also contains the SAP MaxDB client software is installed during the installation of the database server.<br />SAP MaxDB Version&#x00A0;&gt;= 7.8: No. As of SAP MaxDB Version 7.8, the isolated installation was introduced. This means that each database has its own software. No software is now shared between databases. Although the client software is still installed on the database server during a server installation, it is installed in a directory that the SAP kernel cannot use.<br />Using an explicit client installation, the software is provided separately for each database that is installed on the host. As of 7.8, it is located in the directory <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;ClientProgPath/lib&#xFEFF;</em></span>, for example, <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;/sapdb/clients/&lt;sid&gt;/lib&#xFEFF;</em></span><br /><br /></li>\r\n<li>Where can I find the current patches for the SAP MaxDB client software?<br />You can use SWDC to download the SAP MaxDB client software. For more information, see SAP Note <a target=\"_blank\" href=\"/notes/649814\">649814</a>.<br /><br /></li>\r\n<li>If the SAP MaxDB client software is installed incorrectly, which errors can occur?<br />The precompiler runtime or SQLDBC is not installed:&#x00A0;You receive a corresponding error message in the developer trace of the SAP system (<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;dev_w*&#xFEFF;</em></span> files). You cannot usually set up any connection to the database.<br />The DBM server package is not installed. The DBA Cockpit (transaction DBACOCKPIT) or the database assistant/liveCache assistant (transaction DB50/LC10) does not work.<br /><br /></li>\r\n<li>Where do I find more information about SAP MaxDB client software?<br />Further information about the software components of SAP MaxDB is contained in SAP Note <a target=\"_blank\" href=\"/notes/822239\">822239</a>: \"FAQ: MaxDB interfaces\" and in the <a target=\"_blank\" href=\"https://wiki.scn.sap.com/wiki/x/6Bu7Gg\">SAP MaxDB Components</a> documentation.</li>\r\n</ol></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-LVC (liveCache)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D024848)"}, {"Key": "Processor                                                                                           ", "Value": "D024844"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000822271/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000822271/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000822271/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000822271/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000822271/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000822271/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000822271/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000822271/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000822271/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "955670", "RefComponent": "BC-DB-DBI", "RefTitle": "DB multiconnect with SAP MaxDB as secondary database", "RefUrl": "/notes/955670"}, {"RefNumber": "822239", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB interfaces", "RefUrl": "/notes/822239"}, {"RefNumber": "649814", "RefComponent": "BC-DB-SDB", "RefTitle": "Installation/update of SAP MaxDB/liveCache client software", "RefUrl": "/notes/649814"}, {"RefNumber": "599129", "RefComponent": "BC-DB-SDB", "RefTitle": "Uninstalling SAP MaxDB/SAP DB software using SDBUNINST", "RefUrl": "/notes/599129"}, {"RefNumber": "578324", "RefComponent": "BC-DB-SDB", "RefTitle": "Make and release information for MaxDB/HDB DBSL", "RefUrl": "/notes/578324"}, {"RefNumber": "520647", "RefComponent": "BW-SYS-DB-SDB", "RefTitle": "External database connect to an SAP MaxDB database", "RefUrl": "/notes/520647"}, {"RefNumber": "325402", "RefComponent": "BC-DB-LVC", "RefTitle": "dbadaslib/dbsdbslib: How do I apply a patch?", "RefUrl": "/notes/325402"}, {"RefNumber": "19466", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Downloading SAP kernel patches", "RefUrl": "/notes/19466"}, {"RefNumber": "1668638", "RefComponent": "BC-DB-SDB", "RefTitle": "Uninstallation using SDBUNINST as of SAP MaxDB 7.8", "RefUrl": "/notes/1668638"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2325807", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "How to configure SAP Content Server 6.50 with multiple databases?", "RefUrl": "/notes/2325807 "}, {"RefNumber": "1992150", "RefComponent": "BC-DB-LCA", "RefTitle": "CRM 7.03 - liveCache Installation", "RefUrl": "/notes/1992150 "}, {"RefNumber": "649814", "RefComponent": "BC-DB-SDB", "RefTitle": "Installation/update of SAP MaxDB/liveCache client software", "RefUrl": "/notes/649814 "}, {"RefNumber": "822239", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB interfaces", "RefUrl": "/notes/822239 "}, {"RefNumber": "599129", "RefComponent": "BC-DB-SDB", "RefTitle": "Uninstalling SAP MaxDB/SAP DB software using SDBUNINST", "RefUrl": "/notes/599129 "}, {"RefNumber": "1668638", "RefComponent": "BC-DB-SDB", "RefTitle": "Uninstallation using SDBUNINST as of SAP MaxDB 7.8", "RefUrl": "/notes/1668638 "}, {"RefNumber": "578324", "RefComponent": "BC-DB-SDB", "RefTitle": "Make and release information for MaxDB/HDB DBSL", "RefUrl": "/notes/578324 "}, {"RefNumber": "520647", "RefComponent": "BW-SYS-DB-SDB", "RefTitle": "External database connect to an SAP MaxDB database", "RefUrl": "/notes/520647 "}, {"RefNumber": "325402", "RefComponent": "BC-DB-LVC", "RefTitle": "dbadaslib/dbsdbslib: How do I apply a patch?", "RefUrl": "/notes/325402 "}, {"RefNumber": "955670", "RefComponent": "BC-DB-DBI", "RefTitle": "DB multiconnect with SAP MaxDB as secondary database", "RefUrl": "/notes/955670 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}