{"Request": {"Number": "1097637", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 890, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016379282017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001097637?language=E&token=BFAAB9621E5EFF810D1AC80EAED6802A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001097637", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001097637/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1097637"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 11}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.10.2011"}, "SAPComponentKey": {"_label": "Component", "value": "BC-OP-AS4"}, "SAPComponentKeyText": {"_label": "Component", "value": "IBM AS/400"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Operating System Platforms", "value": "BC-OP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "IBM AS/400", "value": "BC-OP-AS4", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP-AS4*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1097637 - IBM i: Copying an SAP kernel (kernel version 7.10/7.11/7.20)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You want to copy the kernel of an SAP system, and you want to run another SAP system with this copy.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Kernel library, SAP kernel, iSeries</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Example:<br />You applied several kernel patches in the test system and tested them. To keep the downtime of the production system short, it can be more favorable (depending on the size and number of the patch files) not to apply the patches directly into the existing SAP kernel of the production system, but rather to create a copy of the kernel library of the test system and to run the production system with this copy.<br />An SAP kernel in this sense is all PASE programs (executable AIX binary files) and ILE objects (*LIB, *PGM, *CMD, *SRVPGM, ...) that belong to an SAP system.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. Log on as &lt;SID&gt;ADM to the iSeries from which you want to copy the SAP kernel. You have the following two options to save the source kernel:</OL> <OL>2. Option 1:</OL> <UL><UL><LI>You position the cursor on the global program directory of your test system &lt;SID&gt;:<br /><br />&#x00A0;&#x00A0; CHGCURDIR '/sapmnt/&lt;SID&gt;/exe'<br /></LI></UL></UL><UL><UL><LI>Save the complete SAP kernel of the test system in a save archive (SAR file):<br /><br /><br />&#x00A0;&#x00A0; SAPCAR PARMLIST('-cvf /home/<USER>/&lt;ARCHIVE&gt; .')<br /><br />whereby &lt;ARCHIVE&gt; is any name of an archive, although it must end in '.SAR' (for example, SAPKERNEL.SAR). In this example, the archive is created in the home directory of &lt;SID&gt;ADM. Note also the point in the PARMLIST parenthesis.</LI></UL></UL> <OL>3. Option 2:<br />You use the command APYSIDKRN to create a backup:<br /><br />&#x00A0;&#x00A0; APYSIDKRN SID(&lt;SID&gt;)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CARPATH('/sapmnt/&lt;SID&gt;/exe/(n)uc/as400_pase_64')<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;UPDAPAR(*NO)<br />This creates files in the directory /sapmnt/&lt;SID&gt;/patches/saved with the name default_&lt;time stamp&gt;_&lt;type&gt;_as400_pase_64, where &lt;time stamp&gt; is a string of ten digits (or a string of 2 digits, 1 letter and 7 digits in Releases 7.10 and 7.11) and &lt;type&gt; is one of the abbreviations jvm, nuc, or uc. (Note: If you also have objects for Linux or Windows, the last part of the name is either linuxppc64 or ntamd64 instead of as400_pase_64). These files together represent the object mentioned in the following archive.</OL> <OL>4. Log on as &lt;PRD&gt;ADM to the iSeries to which you want to copy the SAP kernel (in the SAR archive). &lt;PRD&gt; is the name of the SAP system in which you want to include the kernel.</OL> <OL>5. If the outbound host and target host are different, transfer the archive in binary mode to the target host, preferably to the home directory of &lt;PRD&gt;ADM:&#x00A0;&#x00A0;/home/<USER>/OL> <OL>6. Use APYSIDKRN to restore the kernel library under the central program directory of your production system &lt;PRD&gt;, whereby you specify the previously created or transferred archive in the parameter ARCHIVES:<br /><br />&#x00A0;&#x00A0; APYSIDKRN SID(&lt;PRD&gt;)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ARCHIVES('/home/<USER>/&lt;ARCHIVE&gt;')<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CARPATH('/home/<USER>')<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MODE(*FULLY)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;LOGPATH('/home/<USER>/APYSIDKRN.log')<br /><br />It is assumed here that a PASE program SAPCAR is to be found in the home directory of &lt;PRD&gt;ADM. You can download such a program from SAP Service Marketplace.<br />The MODE(*FULLY) may be applied only to an empty directory /sapmnt/&lt;PRD&gt;/exe.</OL> <OL>7. This completes the copying of the SAP kernel. During the copying process, you must not shut down the production system. If you immediately want to use the programs you imported, you must shut down the production system completely after copying, and then restart it.</OL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D042520)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001097637/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001097637/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001097637/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001097637/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001097637/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001097637/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001097637/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001097637/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001097637/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "49701", "RefComponent": "BC-INS-AS4", "RefTitle": "iSeries: Info and recommendations for kernel libraries (46D)", "RefUrl": "/notes/49701"}, {"RefNumber": "1687173", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Collection of notes about the 7.20 kernel", "RefUrl": "/notes/1687173"}, {"RefNumber": "1632755", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Description of command APYSIDKRN", "RefUrl": "/notes/1632755"}, {"RefNumber": "1589608", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Saving the programs after an action with APYSIDKRN", "RefUrl": "/notes/1589608"}, {"RefNumber": "148208", "RefComponent": "BC-OP-AS4", "RefTitle": "AS/400: Copying the R/3 kernel (Release 46D)", "RefUrl": "/notes/148208"}, {"RefNumber": "1152131", "RefComponent": "BC-OP-AS4", "RefTitle": "i5/OS: JSPM 710 error during deployment of kernel archives", "RefUrl": "/notes/1152131"}, {"RefNumber": "1097751", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Information and recommendations for kernel libraries", "RefUrl": "/notes/1097751"}, {"RefNumber": "1078134", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Distribution of ILE and PASE system components", "RefUrl": "/notes/1078134"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1632755", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Description of command APYSIDKRN", "RefUrl": "/notes/1632755 "}, {"RefNumber": "148208", "RefComponent": "BC-OP-AS4", "RefTitle": "AS/400: Copying the R/3 kernel (Release 46D)", "RefUrl": "/notes/148208 "}, {"RefNumber": "1589608", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Saving the programs after an action with APYSIDKRN", "RefUrl": "/notes/1589608 "}, {"RefNumber": "1078134", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Distribution of ILE and PASE system components", "RefUrl": "/notes/1078134 "}, {"RefNumber": "1687173", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Collection of notes about the 7.20 kernel", "RefUrl": "/notes/1687173 "}, {"RefNumber": "1097751", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Information and recommendations for kernel libraries", "RefUrl": "/notes/1097751 "}, {"RefNumber": "1152131", "RefComponent": "BC-OP-AS4", "RefTitle": "i5/OS: JSPM 710 error during deployment of kernel archives", "RefUrl": "/notes/1152131 "}, {"RefNumber": "49701", "RefComponent": "BC-INS-AS4", "RefTitle": "iSeries: Info and recommendations for kernel libraries (46D)", "RefUrl": "/notes/49701 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "KRNL32NUC", "From": "7.10", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.10", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.10", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.10", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.10", "To": "7.11", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.20", "To": "7.20", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}