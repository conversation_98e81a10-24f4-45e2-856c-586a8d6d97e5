{"Request": {"Number": "1753638", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 801, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017487412017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001753638?language=E&token=186E3AB5AC73C2954E38DA7780AF0D64"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001753638", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001753638/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1753638"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 41}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "30.01.2024"}, "SAPComponentKey": {"_label": "Component", "value": "BC-OP-S390"}, "SAPComponentKeyText": {"_label": "Component", "value": "IBM z/OS"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Operating System Platforms", "value": "BC-OP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "IBM z/OS", "value": "BC-OP-S390", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP-S390*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1753638 - z/OS: Enqueue Replication into System z Coupling Facility"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Enqueue Replication into IBM Z Cross Coupling Facility (XCF or CF)</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>repstzOSCf.so cleanrepstzOSCf HA reason code 0x17030035</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The standard mechanism for ensuring high availability for the enqueue server is by running an enqueue replication server on a remote host. The Enqueue Server will replicate its lock data by sending them via TCP/IP to an enqueue replication server. The replication server writes the data received from the enqueue server into a replication table.<br /><br />In case of a failover the enqueue server moves to the host where the&#160;replication server was running. There it reads the data from the&#160;replication table to reestablish its enqueue table.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This SAP note describes an alternate way to replicate enqueue data for the z/OS platform.<br />This solution is based on the fact that several z/OS hosts (LPARs) can be organized in a z/OS SYSPLEX. In SYSPLEX LPARs&#160;share data via the IBM Z Cross Coupling Facility (CF).<br /><br />Instead of sending replication requests to an enqueue repication server on a remote host the enqueue server writes the replication data directly into the CF.<br />A replication server is no longer needed.<br /><br />In case of a failover the enqueue server can be restarted manually or by the automation software on any LPAR within the SYSPLEX&#160;and reads the replication data from the CF.<br /><br /></p>\r\n<p><strong>SAP Releases supporting Enqueue Replication into the CF</strong><br /><br />The Enqueue Replication into the CF is supported beginning with SAP kernel release 7.21 and patch number 38.&#160;<br />All SAP kernels releases 7.4x&#160;and higher have this support.<br /><br />The SAR package delivering the complete SAP kernel (SAPEXE.SAR Kernel Part I database independent)&#160;contains the additional parts:<br /><br />repstzOSCf.so<br /><br />This is a shared object library accessing the CF&#160;to be loaded by the enqueue server (see SAP profile&#160;parameter enque/server/replication_dll below)<br /><br />cleanrepstzOSCf<br /><br />A tool to remove an enqueue replication table from&#160;the Coupling Facility. This tool is intended for&#160;use in the same situations where cleanipc is used&#160;to remove shared memory<br /><br /><strong>Note:</strong><br />The replication solution is suppported&#160;for SAP production systems based on SAP NetWeaver 7.0 up to 7.3&#160;with SAP kernel levels 7.21 or higher 7.2x levels. For SAP NetWeaver 7.4 and 7.5&#160;the solution is supported with SAP&#160;kernel releases 7.4x and 7.5x<br /><br /><strong>Setup of the Coupling Facility for Enqueue Replication</strong><br /><br />The required setup of the Coupling Facility for Enqueue Replication and further details of this solution are described in the PDF document SAP_ENQ_REP_Inst.pdf that is attached to this SAP note.</p>\r\n<p>This document has been updated with some corrections and two&#160;paragraphs were added (version of 27th of May 2021):<br /> &#183; &#160; &#160; &#160; &#160;<em>How to resume replication after a CF outage</em><br /> &#183; &#160; &#160; &#160; &#160;<em>Secondary note pad structure</em><br /> <br /><strong>High Availability Setup </strong><br /><br />The steps how to include the Coupling Facility for Enqueue Replication solution into a High Availability setup are described in the book \"Business Continuity for SAP on IBM Z\" available on <a target=\"_blank\" href=\"https://www.ibm.com/docs/en/bcfsoz\">https://www.ibm.com/docs/en/bcfsoz</a>.&#160;You may also download the associated PDF from there.</p>\r\n<p>The book discusses how to include CF replication into a new or already existing setup running under IBM Z System Automation.</p>\r\n<p>With SA z/OS APAR OA54684 a new REXX utility <span style=\"font-size: small; font-family: BookMaster;\"><span style=\"font-size: small; font-family: BookMaster;\">SAPMVCF </span></span>is available which can detect and resolve a <em><span style=\"font-size: small; font-family: Palatino-Italic;\"><em><span style=\"font-size: small; font-family: Palatino-Italic;\">co-location </span></em></span></em>situation of an SAP enqueue server and its coupling facility replication data.<br /><br /><strong>SAP Profile Parameters</strong><br /><br />Enqueue Replication is switched on as usual by specifying the SAP profile parameter:<br /><br />&#160; &#160; enque/server/replication = true<br /><br />To switch on the enqueue replication into the CF you have to specify the following SAP profile parameters:<br /><br />&#160; &#160; enque/server/replication_local = true<br /><br />As a consequence the replication is now done locally by the&#160;enqueue server by means of a specialized platform-specific&#160;shared object library (DLL).<br />This library must be specified by the SAP profile parameter<br />&#160; &#160; enque/server/replication_dll.<br />The default for enque/server/replication_local is false.<br /><br />For the z/OS platform and replication into CF the following&#160;shared library must be used:<br /><br />&#160; &#160; enque/server/replication_dll = repstzOSCf.so<br /><br />The default for this parameter is an empty string (no DLL specified).<br /><br />If the profile parameters enque/server/replication and enque/server/replication_local are both set to true the parameter<br />enque/server/replication_dll must be set to a non-empty value. Otherwise the enqueue server terminates with a corresponding error message.<br /><br />If you have a very high frequency of enqueue requests the throughput of the standard replication mechanism might not be sufficient.<br />In this case you can parallelize the replication work by setting the SAP profile parameter<br /><br />&#160; &#160; enque/server/replication_thread_count<br /><br />to a value greater than 1 (the default) and up to 10.</p>\r\n<p><strong>Important Note:</strong><br /> Before activating the replication_thread_count SAP profile parameter, make sure that your installation fulfills the prerequisites. If you have fragmented enqueue requests, do not change the default value. With a value greater than 1 you can get &#8220;out of order&#8221; fragments, replication will stop and you loose high availability of the enqueue server. In the file dev_enqrepl_1 you will see warning messages such as this: EnReplicateEnqToRep::process: stamp of fragment (1/556742519/804000) does not match [enclrep.cpp 2686].</p>\r\n<p dir=\"ltr\"><strong>SAP kernel 7.21 - performance problem when using replication with kernel patch level 313&#160;up to 511</strong></p>\r\n<p dir=\"ltr\">SAP Enqueue server&#160;7.21 patch level 313 up to 511&#160;and setting profile parameter enque/server/replication_thread_count to a value larger than 1 results in extreme enqueue performance degradation when running with enqueue replication.<br /><strong>The recommendation is <span style=\"text-decoration: underline;\">not</span> to set enque/server/replication_thread_count or to set the value to 1 for kernel patch level between 313 and 511 both included. SAP kernel 7.22 is affected up to patch level 004.</strong></p>\r\n<p dir=\"ltr\">The problem was solved for SAP kernel 7.21 with patch level 512 and for SAP kernel 7.22 with patch level 005. See SAP note 2167244.<br /><br />Before using this parameter read more details in the attached PDF document.<br /><br /><strong>Cleanup of Enqueue Replication Tables in the Coupling Facility</strong><br /><br />If you need to delete an enqueue replication table you do this usually by the cleanipc tool since the table is stored in shared memory.<br />If you have set up enqueue replication into the IBM Z Coupling Facility then cleanipc is not able to clear the replication table.<br />You need to use the cleanup tool cleanrepstzOSCf to remove the enqueue replication data from the z/OS CF.<br /><br /><strong>Warning:</strong> deleting replication data while your SAP system is running will temporarily disrupt your failover capability and should be used with caution.<br /><br />To delete an enqueue table issue:<br /><br />cleanrepstzOSCf &lt;sapsid&gt; &lt;instnum&gt;<br /><br />where &lt;sapsid&gt; is the SAP system id and &lt;instnum&gt; the instance number under<br />which the enqueue server was running. E.g.:<br /><br />cleanrepstzOSCf C11 12<br /><br />As SAP system administrator you normally have set the environment variable SAPSYSTEMNAME. If set its value is taken as SAP system id by the cleanup tool so you can omit the &lt;sapsid&gt; parameter and running cleanrepstzOSCf just specifying the instance number:<br /><br />cleanrepstzOSCf &lt;instnum&gt;&#160;&#160;&#160;&#160;&#160;&#160; e.g.&#160;&#160;&#160;&#160;cleanrepstzOSCf 12</p>\r\n<p><strong>Fix for problem due to missing access rights to RACF FACILITY profile IXCNOTE.SAPC11.ENQUEUE</strong></p>\r\n<p>A problem in&#160;the shared object library repstzOSCf.so causes the enqueue server to fail during startup if the SAP system administrator user id &lt;sid&gt;adm has no read access to RACF class FACILITY profile IXCNOTE.SAPC11.ENQUEUE. In this case the enqueue server fails during startup with error message</p>\r\n<p>[Thr 215DAE00:00000001] &gt;&gt;&gt; REPSTORE CF (repstzOSCF.so): ERROR (function &lt;isIXCNOTEAvailable()&gt; line 2098): The check for the XCF note pad services needed to store the replication data into the IBM Z Coupling Facility failed with return code 0x0008 - reason code 0x17030035 from IXCNOTE.</p>\r\n<p>As reason code you may also see a value of 0x1701084C in this message depending of the maintenance level of your z/OS system.</p>\r\n<p>In the z/OS system log you will get RACF message</p>\r\n<p>ICH408I USER(&lt;sid&gt;adm) GROUP(SAPSYS ) NAME(*****)<br />&#160; IXCNOTE.SAPC11.ENQUEUE CL(FACILITY)&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; <br />&#160; INSUFFICIENT ACCESS AUTHORITY&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; <br />&#160; ACCESS INTENT(READ&#160;&#160; )&#160; ACCESS ALLOWED(NONE&#160;&#160; )</p>\r\n<p>For SAP kernel 7.21 this problem is fixed with ZSCS patch level 326.<br />For SAP kernel 7.42 it is fixed with patch level 28.</p>\r\n<p><strong>Fix for enqueue server shutdown when the enqueue replication table in the IBM Z Cross Coupling Facility is not reachable</strong></p>\r\n<p>If the CF where the enqueue replication table is stored goes down the replication gets suspendend after 2*3 tries to access the replication table. When it gets resumed after the timeout defined by enque/enrep/stop_timeout_s the enqueue server deletes the note pad holding the old replication data and creates a new one in the CF. If this fails the enqueue server shuts down. The enqueue server also terminates at initialization if it cannot reach the CF.</p>\r\n<p>The fix is available for</p>\r\n<p>- SAP kernel 7.22 with patch level 1022</p>\r\n<p>- SAP kernel 7.49 with patch level 948</p>\r\n<p>- SAP kernel 7.53 with patch level 810</p>\r\n<p><strong>Fix to ensure all replicas of deleted enqueue locks are removed from the replication&#160;<strong>table in the IBM Z Cross Coupling Facility</strong></strong></p>\r\n<p>When using enqueue lock replication into the IBM Z Cross Coupling Facility (CF) some replicas of deleted enqueue locks might be not be deleted from the replication table in the CF. A restart of the enqueue server will create invalid locks from these erroneously not deleted replicas. This patch corrects the problem.</p>\r\n<p>Important note:<br />Even after activating the enqueue server containing this fix, it might still re-create invalid locks from the replication table created by an enqueue server version without this fix. In this case delete this entries using SAP transaction SM12. Alternatively, if there should be no locks in the replication table,&#160; you can delete that table from the CF by using the executable&#160;cleanrepstzOSCf before you start the enqueue server including this fix.</p>\r\n<p>This fix is available for</p>\r\n<p>- SAP kernel 7.22 with patch level 1111</p>\r\n<p>- SAP kernel 7.49 with patch level 1022</p>\r\n<p>- SAP kernel 7.53 with patch level 828</p>\r\n<p><strong>Fix cut off descriptions of note pads for replication&#160;<strong>tables in the IBM Z Cross Coupling Facility</strong></strong></p>\r\n<p>When displaying an XCF note pad for an SAP enqueue replication table on the MVS console its description is cut-off by the last character as shown in the following example</p>\r\n<p>The MVS DISPLAY command for an XCF note pad with a SAP enqueue replication table</p>\r\n<p>D XCF,NP,NPNM=SAPC11.ENQUEUE.11,SCOPE=DET</p>\r\n<p>shows</p>\r\n<p>SDSF OPERLOG COH1 11/09/2021 4W <br /> COMMAND INPUT ===&gt; <br /> RESPONSE=COH1 <br /> IXC443I 09.53.07 DISPLAY XCF 246 <br /> INFO FOR NOTE PAD SAPHA1.ENQUEUE.11<br /> DESCRIPTION: <strong>SAPENQRepl SAPSID C11 Instance 1</strong> <br /> HOST STRUCTURE: IXCNP_SAPC1100 <br /> STATUS: CREATED <br /> SYSTEMS CONNECTED: COH1 <br /> CREATED: 11/03/2021 21:32:13.879060 <br /> LIST NUMBER: 16</p>\r\n<p>The last characater (the last digit of the SAP instance number) in the DESCRIPTION is missing . The description should be&#160;<strong>SAPENQRepl SAPSID C11 Instance</strong> <strong>1</strong><em><strong>1</strong><br /></em></p>\r\n<p>The reason for this is that the value for the description cannot be longer than 32 characters. The actual description would be 33 characters long where the last character is cut off in order to prevent a buffer overflow. So this bug does not cause any functional problem.</p>\r\n<p>This patch corrects the problem by providing a shorter message displaying the complete instance number. The message with this patch now looks like</p>\r\n<p><strong>SAPENQRepl SAPSID C11 Inst 11</strong></p>\r\n<p>and applies to all note pads created after this fix is applied. The fixed part is the shared object library repstzOSCf.so.</p>\r\n<p>This fix is available for</p>\r\n<p>- SAP kernel 7.22 with patch level 1115</p>\r\n<p>- SAP kernel 7.49 with patch level 1032</p>\r\n<p>- SAP kernel 7.53 with patch level 911</p>\r\n<p><strong>Fix to handle return code when&#160;<strong>remove of records from the replication&#160;<strong>table in the IBM Z Cross Coupling Facility</strong></strong>&#160;fails</strong></p>\r\n<p>If the CF containing the enqueue replication records is not available this was not indicated when deleting records since the correponding return code was ignored. As consequence the enqueue replication may remain active although the CF is not available or set activ in those cases.</p>\r\n<p>This patch fixes this problem.&#160;The fixed part is the shared object library repstzOSCf.so.</p>\r\n<p>The fix is available for</p>\r\n<p>- SAP kernel 7.22 with patch level 1117</p>\r\n<p>- SAP kernel 7.49 with patch level 1039</p>\r\n<p>- SAP kernel 7.53 with patch level 920</p>\r\n<p><strong>Fix cleanup of thread specific resources in case initialization of the replication table in the IBM Z Cross Coupling Facility fails</strong></p>\r\n<p>If no CF structure for the note pad to contain the enqueue replication data is available at initialization the thread specific resources are not cleaned up properly when handling that failure. Since e.g every 300 seconds there are retries to create the replication table thread specific resources like the thread keys run short and you get error messages in the dev_enqrepl developer trace like</p>\r\n<p>[Thr 21EA3800:00000009] &gt;&gt;&gt; REPSTORE CF (repstzOSCF.so): ERROR (function &lt;repstInit(iEnqueueTrace *, int *)&gt; line 379): pthread_key_create to create key for thread specific data returned with -1 and errno 112.</p>\r\n<p>This patch fixes this problem by proper cleanup of the thread specific resources. The fixed part is the shared object library repstzOSCf.so.</p>\r\n<p>The fix is available for</p>\r\n<p>- SAP kernel 7.22 with patch level 1310.</p>\r\n<p>- SAP kernel 7.53 with patch level 1211</p>\r\n<p>- SAP kernel 7.54 with patch level 119</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-CST-EQ (Enqueue)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5016550)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (C1079908)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001753638/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001753638/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001753638/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001753638/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001753638/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001753638/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001753638/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001753638/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001753638/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "SAP_ENQ_REP_Inst.pdf", "FileSize": "456", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000722982012&iv_version=0041&iv_guid=00109B36BC1E1EDBAFE0773BCFB360E8"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "81737", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: APAR List", "RefUrl": "/notes/81737"}, {"RefNumber": "2167244", "RefComponent": "BC-CST-EQ", "RefTitle": "ENQU (ENSA): Performance problems on z/OS", "RefUrl": "/notes/2167244"}, {"RefNumber": "1826634", "RefComponent": "BC-CST-EQ", "RefTitle": "[Pilot]Enqueue Replication via IBM Coupling Facility (XCF)", "RefUrl": "/notes/1826634"}, {"RefNumber": "1823660", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: High Availability with SA z/OS updates and fixes", "RefUrl": "/notes/1823660"}, {"RefNumber": "1728283", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 721: General Information", "RefUrl": "/notes/1728283"}, {"RefNumber": "1716826", "RefComponent": "BC-CST", "RefTitle": "Usage of the downward compatible kernel 721 (EXT)", "RefUrl": "/notes/1716826"}, {"RefNumber": "1713986", "RefComponent": "BC-CST", "RefTitle": "Installation of kernel 721 (EXT)", "RefUrl": "/notes/1713986"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2496410", "RefComponent": "BC-OP-S390", "RefTitle": "z/OS: INSUFF<PERSON>IENT SPACE IN STRUCTURE during the installation of the SAP ENQ Replication into System z Coupling Facility", "RefUrl": "/notes/2496410 "}, {"RefNumber": "2131873", "RefComponent": "BC-DB-DB2", "RefTitle": "z/OS: Automated Rolling Kernel Switch in HA environment", "RefUrl": "/notes/2131873 "}, {"RefNumber": "1728283", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 721: General Information", "RefUrl": "/notes/1728283 "}, {"RefNumber": "1713986", "RefComponent": "BC-CST", "RefTitle": "Installation of kernel 721 (EXT)", "RefUrl": "/notes/1713986 "}, {"RefNumber": "1823660", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: High Availability with SA z/OS updates and fixes", "RefUrl": "/notes/1823660 "}, {"RefNumber": "1716826", "RefComponent": "BC-CST", "RefTitle": "Usage of the downward compatible kernel 721 (EXT)", "RefUrl": "/notes/1716826 "}, {"RefNumber": "1826634", "RefComponent": "BC-CST-EQ", "RefTitle": "[Pilot]Enqueue Replication via IBM Coupling Facility (XCF)", "RefUrl": "/notes/1826634 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "KRNL64NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.40", "To": "7.40", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.41", "To": "7.41", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.42", "To": "7.42", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.22", "To": "7.22", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.22EXT", "To": "7.22EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.49", "To": "7.49", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.40", "To": "7.40", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.41", "To": "7.41", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.42", "To": "7.42", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.22", "To": "7.22", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.22EXT", "To": "7.22EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.49", "To": "7.49", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.53", "To": "7.53", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.22", "To": "7.22", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.49", "To": "7.49", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.53", "To": "7.53", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.54", "To": "7.54", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP KERNEL 7.40 64-BIT", "SupportPackage": "SP000", "SupportPackagePatch": "000000", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200022526&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.41 64-BIT", "SupportPackage": "SP000", "SupportPackagePatch": "000000", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200023698&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.41 64-BIT UNICODE", "SupportPackage": "SP000", "SupportPackagePatch": "000000", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200023700&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT UNICODE", "SupportPackage": "SP000", "SupportPackagePatch": "000000", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025031&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT", "SupportPackage": "SP000", "SupportPackagePatch": "000000", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025032&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.40 64-BIT UNICODE", "SupportPackage": "SP000", "SupportPackagePatch": "000000", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67838200100200019652&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP000", "SupportPackagePatch": "000326", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP000", "SupportPackagePatch": "000326", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP000", "SupportPackagePatch": "000326", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP000", "SupportPackagePatch": "000326", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.53 64-BIT UNICODE", "SupportPackage": "SP810", "SupportPackagePatch": "000810", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200005858&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.53 64-BIT", "SupportPackage": "SP810", "SupportPackagePatch": "000810", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200006207&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.53 64-BIT UNICODE", "SupportPackage": "SP920", "SupportPackagePatch": "000920", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200005858&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.53 64-BIT", "SupportPackage": "SP920", "SupportPackagePatch": "000920", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200006207&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22_EX2 64-BIT", "SupportPackage": "SP1115", "SupportPackagePatch": "001115", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200010235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22_EX2 64-BIT UC", "SupportPackage": "SP1115", "SupportPackagePatch": "001115", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200010236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT", "SupportPackage": "SP1115", "SupportPackagePatch": "001115", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001793&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT UNICODE", "SupportPackage": "SP1115", "SupportPackagePatch": "001115", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001794&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT", "SupportPackage": "SP1115", "SupportPackagePatch": "001115", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001797&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT UC", "SupportPackage": "SP1115", "SupportPackagePatch": "001115", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001798&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22_EX2 64-BIT", "SupportPackage": "SP1022", "SupportPackagePatch": "001022", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200010235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22_EX2 64-BIT UC", "SupportPackage": "SP1022", "SupportPackagePatch": "001022", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200010236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT", "SupportPackage": "SP1022", "SupportPackagePatch": "001022", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001793&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT UNICODE", "SupportPackage": "SP1022", "SupportPackagePatch": "001022", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001794&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT", "SupportPackage": "SP1022", "SupportPackagePatch": "001022", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001797&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT UC", "SupportPackage": "SP1022", "SupportPackagePatch": "001022", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001798&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22_EX2 64-BIT", "SupportPackage": "SP1111", "SupportPackagePatch": "001111", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200010235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22_EX2 64-BIT UC", "SupportPackage": "SP1111", "SupportPackagePatch": "001111", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200010236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT", "SupportPackage": "SP1111", "SupportPackagePatch": "001111", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001793&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT UNICODE", "SupportPackage": "SP1111", "SupportPackagePatch": "001111", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001794&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT", "SupportPackage": "SP1111", "SupportPackagePatch": "001111", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001797&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT UC", "SupportPackage": "SP1111", "SupportPackagePatch": "001111", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001798&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22_EX2 64-BIT", "SupportPackage": "SP1117", "SupportPackagePatch": "001117", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200010235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22_EX2 64-BIT UC", "SupportPackage": "SP1117", "SupportPackagePatch": "001117", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200010236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT", "SupportPackage": "SP1117", "SupportPackagePatch": "001117", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001793&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT UNICODE", "SupportPackage": "SP1117", "SupportPackagePatch": "001117", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001794&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT", "SupportPackage": "SP1117", "SupportPackagePatch": "001117", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001797&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT UC", "SupportPackage": "SP1117", "SupportPackagePatch": "001117", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001798&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.53 64-BIT UNICODE", "SupportPackage": "SP911", "SupportPackagePatch": "000911", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200005858&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.53 64-BIT UNICODE", "SupportPackage": "SP828", "SupportPackagePatch": "000828", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200005858&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.53 64-BIT", "SupportPackage": "SP828", "SupportPackagePatch": "000828", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200006207&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.53 64-BIT", "SupportPackage": "SP911", "SupportPackagePatch": "000911", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200006207&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.49 64-BIT UNICODE", "SupportPackage": "SP1020", "SupportPackagePatch": "001020", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200004760&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.49 64-BIT", "SupportPackage": "SP1020", "SupportPackagePatch": "001020", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200004791&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.49 64-BIT UNICODE", "SupportPackage": "SP948", "SupportPackagePatch": "000948", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200004760&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.49 64-BIT", "SupportPackage": "SP948", "SupportPackagePatch": "000948", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200004791&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.49 64-BIT UNICODE", "SupportPackage": "SP1032", "SupportPackagePatch": "001032", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200004760&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.49 64-BIT", "SupportPackage": "SP1032", "SupportPackagePatch": "001032", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200004791&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.49 64-BIT UNICODE", "SupportPackage": "SP1039", "SupportPackagePatch": "001039", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200004760&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.49 64-BIT", "SupportPackage": "SP1039", "SupportPackagePatch": "001039", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200004791&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.54 64-BIT", "SupportPackage": "SP119", "SupportPackagePatch": "000119", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200019167&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.54 64-BIT UNICODE", "SupportPackage": "SP119", "SupportPackagePatch": "000119", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200019168&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.53 64-BIT UNICODE", "SupportPackage": "SP1211", "SupportPackagePatch": "001211", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200005858&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.53 64-BIT", "SupportPackage": "SP1211", "SupportPackagePatch": "001211", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200006207&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22_EX2 64-BIT", "SupportPackage": "SP1310", "SupportPackagePatch": "001310", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200010235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22_EX2 64-BIT UC", "SupportPackage": "SP1310", "SupportPackagePatch": "001310", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73554900100200010236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT", "SupportPackage": "SP1310", "SupportPackagePatch": "001310", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001797&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT UC", "SupportPackage": "SP1310", "SupportPackagePatch": "001310", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001798&V=MAINT"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}