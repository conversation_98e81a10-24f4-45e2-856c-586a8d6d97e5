{"Request": {"Number": "2628714", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 418, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000763262018"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=66C697E4BA4C3639384930E8823C5BC6"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2628714"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "21.09.2018"}, "SAPComponentKey": {"_label": "Component", "value": "CA-FLE-AMT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Amount Field Extension"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Cross Application Field Lenght Extension", "value": "CA-FLE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-FLE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Amount Field Extension", "value": "CA-FLE-AMT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-FLE-AMT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2628714 - Amount Field Length Extension: User Interface Adaptations"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Selected currency amount&#160;field lengths and related data types&#160;are extended. See SAP Note <a target=\"_blank\" href=\"/notes/2628654\">2628654&#160;</a>for motivation and scope.</p>\r\n<p>This is relevant, if you are converting from SAP ERP ECC60 or upgrading to SAP S/4HANA On-Premise 1809 or higher.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Amount Field Length Extension, AFLE, 23 digits, Currency, &#160;User Interface, GUI, Dynpro, Web Dynpro, List Printing, Print Forms, OData, FIORI, JavaScript.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>In SAP S/4HANA,&#160;currency amount fields with a field length between 9-22 including 2 decimals have been extended to 23 digits including 2 decimals. In addition to currency amount fields, selected data elements of DDIC type DEC, CHAR, and NUMC with varying lengths and decimal places&#160;that may hold amounts have been affected. This feature is available in SAP S/4HANA, on-premise edition 1809 and higher releases.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Introduction</strong></p>\r\n<p>Amount field&#160;extensions were mainly realized via domain exchanges of existing data-elements, which guarantee that most of the affected development artifacts like ABAP datatypes, structures, and database tables within the system are also adjusted consistently. With this approach, the ABAP code that uses the affected types is still syntactically correct in almost all cases.&#160;This also applies to your code that references&#160;the extended data elements. For more background information about the amount field length extensions in general, see SAP Note <a target=\"_blank\" href=\"/notes/2628040\">2628040 </a>and SAP Note <a target=\"_blank\" href=\"/notes/2628654\">2628654</a>. Nevertheless, there are some artifacts that may require manual adjustments.</p>\r\n<p>All user interfaces in which at least one of the extended amount fields is part of the underlying data model are affected. In these UIs, the properties of the UI elements or programs might need to be adjusted in such a way that the complete length of the amount field is displayed in case it is intended to support extended amount field length in the respective screen. In other cases where extended amount field length shall not be supported, it might be necessary to make adjustments to ensure that no large amounts are entered.</p>\r\n<p>To support the adoption, the following sections provide instructions for different UI technologies.</p>\r\n<p><strong><a target=\"_self\" href=\"#ABAPGUI\">ABAP GUI Dynpro</a></strong></p>\r\n<p><strong><a target=\"_self\" href=\"#WebDynpro\">Web Dynpro</a></strong></p>\r\n<p><strong><a target=\"_self\" href=\"#FIORIANDJAVASCRIPT\">Fiori Applications and JavaScript Code</a><br /></strong></p>\r\n<p><strong><strong><a target=\"_self\" href=\"#ADOBEFORMS\">Adobe Forms</a></strong></strong></p>\r\n<p>We recommend comprehensive testing for all affected user interfaces to ensure that they work correctly after upgrade to S/4HANA 1809 or higher.</p>\r\n<p><strong><a target=\"_blank\" name=\"ABAPGUI\"></a>ABAP GUI Dynpro</strong></p>\r\n<p>If extended amount field length shall not be supported, amounts and the corresponding screen fields are affected because of the amount field length extension. In this case, consider replacing the data element with an equivalent data element that was not extended, for example, one of the data elements to support the original length. If this change is not possible and the provided amount field length extension will not be activated in customizing, the conversion exits attached to domains will only allow entries of the previous amount field length.</p>\r\n<p>If the extended amount field is part of GUI screen and you decided to support amount field length extension, adjustments in the screenpainter are required. You need to extend the visible field length by adjusting the &#8220;Vis. Length&#8221; property of the field to be equal to &#8220;Def. length&#8221; whenever desired, to support displaying&#160;long amounts. Determine&#160;this on a case by case basis based on screen layout. Please refer to figure 1 in attachment.</p>\r\n<p>If the screen field visible length cannot be adjusted, we provide a&#160;fallback strategy that enables displaying of&#160;an extended amount field in a smaller visible length. Ellipsis will be displayed in this scenario and the entire amount is displayed on mouse hover. Please refer to figure 2 in attachment. Take care that the DDIC Modify flag in the screen attributes is not set to 'X' (modified compared to the Dictionary) for the provided amount field length extension to work correctly.</p>\r\n<p>For the screens where a field is intended to support amount field length extension and it is not activated in customizing, conversion exits attached to the domains automatically restrict the entry of larger amounts. If the screen field is not referring to the dictionary, the respective conversion routine must be specified explicitly in the screen painter. Conversion routine naming convention uses Data Type, Sign Information, No. of characters &amp; Decimals as shown in the examples below.</p>\r\n<p style=\"padding-left: 30px;\">AC132: Used for signed currency domains with 13 characters and 2 decimals</p>\r\n<p style=\"padding-left: 30px;\">AU132: Used for unsigned currency domains with 13 characters and 2 decimals</p>\r\n<p style=\"padding-left: 30px;\">AD132: Used for signed decimal domains with 13 characters and 2 decimals</p>\r\n<p style=\"padding-left: 30px;\">AE132: Used for unsigned decimal domains with 13 characters and 2 decimals</p>\r\n<p>For more information and&#160;for a list of domains and the corresponding conversion exits, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2628040\">2628040</a>.</p>\r\n<p>For cases where a maintenance view exists on the tables using amount field length extension, the maintenance view should be regenerated to support this.</p>\r\n<p><strong><a target=\"_blank\" name=\"WebDynpro\"></a>&#65279;Web Dynpro</strong></p>\r\n<p>Please make sure that the \"length\" and \"width\" properties of affected fields in static models are set correctly. If the Web Dynpro models are created dynamically, check the corresponding implementation.</p>\r\n<p><strong><a target=\"_blank\" name=\"FIORIANDJAVASCRIPT\"></a>&#65279;Fiori Applications and JavaScript Code</strong></p>\r\n<p>Adjustments required for Fiori applications might consist of adjustments to the backing OData service(s) as well as JavaScript code or view definitions in the Fiori application itself.</p>\r\n<p><strong>OData</strong></p>\r\n<p>OData services are usually used in Fiori application as backend dataprovider. The need for adjustments of the OData services depends on the implementation style of the service and on the choice, if the provided amount field length extension shall be supported or not. Note that the decision must be made based on the implementation of the affected entity type instead of on the ODATA service definition as there might be different implementation styles mixed within one OData service.</p>\r\n<p>If you decide not to support the provided amount field length&#160;extension for your system, you need to&#160;make the following adjustments:&#160;For your OData services containing CDS views, that use the &#8220;@OData.publish:true&#8221; annotation, adjust the dictionary structure used in the&#160;CDS view definition to use equivalent data elements that are not extended. For example, adjust the dictionary structure for the corresponding _CS&#160;data element by casting the field in the CDS view to the respective data element and by activating the view. If you decide to support&#160;the provided amount field length&#160;extension no changes are necessary.</p>\r\n<p>For your OData services created via transaction SEGW using Data Source Reference to CDS views, make the necessary changes in the CDS views. If you decide not to support the provided amount field length&#160;extension, the respective field in the CDS view definition must be adjusted to use equivalent data elements that are not extended, for example, the corresponding _CS&#160;data element by casting the field in the CDS view to the respective data element and by activating the view. If you decide to support&#160;the provided amount field length&#160;extension and you would like to support both short and long amounts, to improve the usability and perform input validations at client side, implement/adjust the model provider extension class MPC_EXT as shown <a target=\"_self\" href=\"#MPC_EXT_CHANGES\">below</a>.</p>\r\n<p>For your OData services modeled in SEGW, if you decide not to support the provided amount field length&#160;extension, the dictionary structure referenced in the respective entity type or complex type must be adjusted to use equivalent data elements that are not extended, for example, the corresponding _CS&#160;data element. If you decide to support extended amounts, the steps below must be performed:</p>\r\n<ol>\r\n<li>In the property attributes of the affected type&#8217;s property &#8220;Precision&#8221; and &#8220;Scale&#8221; shall be adjusted to &#8220;24&#8221; and &#8220;3&#8221; respectively for the amount property of Gateway Builder. Refer to figure 3 in the attachment.</li>\r\n<li>Implement/adjust model provider extension class MPC_EXT changes as described <a target=\"_self\" href=\"#MPC_EXT_CHANGES\">below </a>for better usability.</li>\r\n</ol>\r\n<p>Note: In the front-end development system, you might need to clean the services cache to reflect the latest changes when testing the service.</p>\r\n<p>For your OData services where metadata was defined by implementing the model provider class, if you decide not to support the provided amount field length&#160;extension, metadata definition code usually does not need to be adjusted, however it might be advisable to adjust the affected DDIC structures used for implementing the service, for example by using equivalent data elements that are not extended, for example&#160;the corresponding _CS data element. If you decide to support the provided amount field length&#160;extension, execute the following steps described below:</p>\r\n<ol>\r\n<li>Adjust the implementation in the model provider class code by setting the &#8220;Maxlength&#8221; and &#8220;precision&#8221; of the respective properties to &#8220;24&#8221; and &#8220;3&#8221; respectively.</li>\r\n<li>Implement/adjust model provider extension class MPC_EXT changes as described <a target=\"_self\" href=\"#MPC_EXT_CHANGES\">below </a>for better usability.</li>\r\n</ol>\r\n<p>Note: In the front-end development system, you might need to clean the services cache to reflect the latest changes when testing the service.</p>\r\n<p><strong><a target=\"_blank\" name=\"MPC_EXT_CHANGES\"></a>&#65279;Dynamic adjustments of service metadata based on activation of extended amount length in the backend SAP system</strong></p>\r\n<p>In case DDIC changes described above are not possible or&#160;the provided amount field length&#160;extension shall be supported with the possibility to dynamically react to activated / deactivated amount field length extension, a possibility to dynamically adjust the OData metadata is provided by class CL_AFLE_MPC_EXT_UTILITY_CLASS. The class needs to be called in the model provider extension class (_MPC_EXT)</p>\r\n<p>Enhance the DEFINE method of the model provider extension class MPC_EXT to call ADJUST_MODEL of utility class CL_AFLE_MPC_EXT_UTILITY_CLASS at the very end of the method.</p>\r\n<p style=\"padding-left: 30px;\">Example:</p>\r\n<p style=\"padding-left: 60px;\">METHOD define.</p>\r\n<p style=\"padding-left: 90px;\">super-&gt;define( ).</p>\r\n<p style=\"padding-left: 90px;\">cl_afle_mpc_ext_utility_class=&gt;adjust_model( io_model = model ).</p>\r\n<p style=\"padding-left: 60px;\">ENDMETHOD.</p>\r\n<p>The GET_LAST_MODIFIED method has to be adjusted to call ADJUST_LAST_MODIFED at the end of the method to ensure metadata caches are properly invalidated.</p>\r\n<p style=\"padding-left: 60px;\">Example:</p>\r\n<p style=\"padding-left: 60px;\">METHOD GET_LAST_MODIFIED.</p>\r\n<p style=\"padding-left: 90px;\">rv_last_modified = super-&gt;get_last_modified( ).</p>\r\n<p style=\"padding-left: 90px;\">cl_afle_mpc_ext_utility_class=&gt;adjust_last_modified( CHANGING cv_last_modified = rv_last_modified ).</p>\r\n<p style=\"padding-left: 60px;\">ENDMETHOD.</p>\r\n<p><strong>Fiori and JavaScript coding</strong>:</p>\r\n<p>The following adjustments are usually only relevant in case&#160;the provided amount field length&#160;extension shall be supported by the respective Fiori application.</p>\r\n<p>It is a known limitation that, JavaScript has no native datatype for decimal numbers. There is only the number/float type which internally uses double precision binary floating point format (IEEE 754, binary64). This data type has 53 bits for the mantissa, which means between 15 and 16 digits in decimal representation. Therefore, amounts exceeding 15 digits returned from OData model will result in rounding as shown below. The OData model itself represents decimal amounts with datatype &#8220;Edm.Decimal&#8221; for which the representation JavaScript side is a string.</p>\r\n<p style=\"padding-left: 30px;\">The OData amount &#8220;123456789012345678901.23&#8221;, is represented like123456789012345680000.00</p>\r\n<p>If you decided to support&#160;the provided amount field length&#160;extension for Fiori screens, to avoid the rounding issue different aspects need to be considered</p>\r\n<p>Generally, the SAP UI5 controls are supporting larger amounts without the rounding issues described above. There is once exception in control sap.ui.unified.Currency that needs to be considered. The same applies in case the applications implemented their own formatting for amounts that internally convert to datatype number. Fiori control &#8220;sap.ui.unified.Currency&#8221; is used to handle extended amount values in Fiori XML views. The control&#8217;s property value is typed as float, that is, rounding issues will occur. Instead in SAP UI5 1.54 an alternative property stringValue was introduced, which can be used instead and supports larger amounts.</p>\r\n<p style=\"padding-left: 60px;\">Example coding with type -\"sap.ui.model.type.String\" as of SAP UI5 1.52</p>\r\n<p style=\"padding-left: 90px;\">&lt;unified:Currency</p>\r\n<p style=\"padding-left: 90px;\">value=\"{path: 'AmountInDisplayCurrency',</p>\r\n<p style=\"padding-left: 90px;\">type: 'sap.ui.model.type.String'}\"</p>\r\n<p style=\"padding-left: 90px;\">currency=\"{path: 'DisplayCurrency'}\"/&gt;</p>\r\n<p style=\"padding-left: 90px;\">With the new property &#8220;stringValue&#8221; introduced to this control in SAP UI5 1.54, adapt the prior example coding as below.</p>\r\n<p style=\"padding-left: 90px;\">&lt;unified:Currency</p>\r\n<p style=\"padding-left: 90px;\">stringValue=\"{path: 'AmountInDisplayCurrency'}</p>\r\n<p style=\"padding-left: 90px;\">currency=\"{path: 'DisplayCurrency'}\"/&gt;</p>\r\n<p>Adjust custom formatter functions that internally use datatype number to ensure that no rounding issues occur. There is no guideline on how to identify the relevant formatters. Check functions like parseFloat() and Number() but be sure to check others as well. Check whether you can use SAP UI5 standard functions like sap.ui.core.format.NumberFormat.getCurrencyInstance and sap.ui.model.type.Currency for the adjustments. There is no guideline on how to identify the relevant formatters. Check functions like parseFloat() and Number() but be sure to check others as well. Check whether you can use SAP UI5 standard functionalities like sap.ui.core.format.NumberFormat.getCurrencyInstance and sap.ui.model.type.Currency for the adjustments.</p>\r\n<p>In case of a calculation on the client side (inside the Web browser) with amounts exceeding 15 digits, the calculation result may not be accurate. A third-party JavaScript library to handle larger amount might be considered as a solution. Alternatively, the calculation logic could be done in the backend and then only the calculation result is transferred and used on the client side.</p>\r\n<p><strong><a target=\"_blank\" name=\"ADOBEFORMS\"></a>&#65279;Adobe Forms</strong></p>\r\n<p>Adobe form output is unaffected in case&#160;the provided amount field length&#160;extension shall not be activated in customizing, at least in case the amounts are passed as decimal numbers in the form interface. In case character datatypes are used, adjustments of the code (see SAP Note <a target=\"_blank\" href=\"/notes/2610650\">2610650</a>) might be required.</p>\r\n<p>Regarding supporting extended amount length in Adobe form, we currently cannot give detailed guidance.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-GL (General Ledger Accounting)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D048317)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D048317)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Note UI attachment.pdf", "FileSize": "141", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125900000631042018&iv_version=0010&iv_guid=6EAE8B28C7391ED88FB6D5072CB1A0CB"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2628654", "RefComponent": "CA-FLE-AMT", "RefTitle": "S4TWL: Amount Field Length Extension", "RefUrl": "/notes/2628654"}, {"RefNumber": "2628040", "RefComponent": "CA-FLE-AMT", "RefTitle": "Amount Field Length Extension: General Information", "RefUrl": "/notes/2628040"}, {"RefNumber": "2610650", "RefComponent": "CA-FLE-AMT", "RefTitle": "Amount Field Length Extension: Code Adaptations", "RefUrl": "/notes/2610650"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2628641", "RefComponent": "CA-FLE-AMT", "RefTitle": "Amount Field Length Extension: IDoc Interface/ALE Adaptations", "RefUrl": "/notes/2628641 "}, {"RefNumber": "2628654", "RefComponent": "CA-FLE-AMT", "RefTitle": "S4TWL: Amount Field Length Extension", "RefUrl": "/notes/2628654 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}