{"Request": {"Number": "1690662", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 836, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017399092017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001690662?language=E&token=4DB97C1E6264ABF2DE077EBD0040CCEB"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001690662", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001690662/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1690662"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.11.2012"}, "SAPComponentKey": {"_label": "Component", "value": "BC-SEC-SNC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Secure Network Communications"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Security - Read KBA 2985997 for subcomponents", "value": "BC-SEC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Secure Network Communications", "value": "BC-SEC-SNC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC-SNC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1690662 - Option: Blocking unencrypted SAPGUI/RFC connections"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You want to ensure that inbound RFC or GUI connections have to be encrypted.<br />Previously, you could use parameters such as snc/accept_insecure_gui or snc/accept_insecure_rfc to configure only whether you had to use SNC credentials for the logon in the case of an inbound connection secured with SNC, or whether other logon procedures were also accepted.<br />You have no option to force a connection secured and encrypted with SNC for all other logon procedures (password-based, for example).<br />To encrypt SAPGUI or RFC connections, you must use an SNC product with the QoP setting \"Encryption\". If you choose a lower QoP level (\"Integrity\", for example), the required condition will not be met.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>snc/only_encrypted_gui, snc/only_encrypted_rfc, QoP, quality of protection<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by missing functions.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Import a 721 kernel with <B>at least</B> patch level 33 (see SAP Note 1716826).<br /><br />snc/only_encrypted_gui has the following permitted values:</p> <UL><LI>0 (Default) - Inbound GUI connections are treated as before.</LI></UL> <UL><LI>1 - Inbound GUI connections must be secured <B>AND</B> encrypted with SNC.</LI></UL> <p><br />snc/only_encrypted_rfc has the following permitted values:</p> <UL><LI>0 (Default) - Inbound RFC connections are treated as before.</LI></UL> <UL><LI>1 - Inbound RFC connections of non-ABAP clients must be secured <B>AND</B> encrypted with SNC.</LI></UL> <UL><LI>2 - In addition to 1, inbound external RFC connections of ABAP clients must also be secured <B>AND</B> encrypted with SNC.</LI></UL> <UL><LI>3 - In addition to 2, inbound internal RFC connections of other application servers of the same system must also be secured <B>AND</B> encrypted with SNC. IMPORTANT: Take into account the parameters snc/r3int_rfc_secure and snc/r3int_rfc_qop.</LI></UL> <p><br />Encryption corresponds with a negotiated quality of protection (QoP) during the SNC handshake of level 3. Which QoP is negotiated depends on the SNC setting of the client and of the server. The parameters snc/data_protection/max and snc/data_protection/min are relevant for the server.<br />The SNC product NTLM does not offer a quality of protection of level 3.<br /><br />The assigned ABAP correction (delivery possible only via Support Package) introduces the two new profile parameters snc/only_encrypted_gui and snc/only_encrypted_rfc in RZ10/RZ11.<br />However, you can also use the parameters with an earlier ABAP Support Package if you have applied the required kernel patch.<br />In this case, create system message SNC(100) in transaction SE91 manually:<br /></p> <UL><LI>Call transaction SE91.</LI></UL> <UL><LI>Message class: SNC<br />Message: 100<br />Choose \"Change\".</LI></UL> <UL><LI>Enter the following message text:<br />Unencrypted communication is rejected by this system</LI></UL> <UL><LI>Select the \"Self-Explanatory\" checkbox.</LI></UL> <UL><LI>Save this change.</LI></UL> <p><br />If you set the parameter in RZ10 without having imported the ABAP Support Package, you receive warnings about the parameters not being recognized.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-SEC-LGN (Authentication)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D046282)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D021767)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001690662/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001690662/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001690662/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001690662/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001690662/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001690662/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001690662/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001690662/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001690662/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2122578", "RefComponent": "BC-SEC-SNC", "RefTitle": "New: Security Audit Log event for unencrypted GUI / RFC connections", "RefUrl": "/notes/2122578"}, {"RefNumber": "1728283", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 721: General Information", "RefUrl": "/notes/1728283"}, {"RefNumber": "1716826", "RefComponent": "BC-CST", "RefTitle": "Usage of the downward compatible kernel 721 (EXT)", "RefUrl": "/notes/1716826"}, {"RefNumber": "1701870", "RefComponent": "BC-MID-RFC", "RefTitle": "RFC client communication supporting SNC without SSO", "RefUrl": "/notes/1701870"}, {"RefNumber": "1670678", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1670678"}, {"RefNumber": "1643878", "RefComponent": "BC-IAM-SSO-SL", "RefTitle": "Release Notes for SNC Client Encryption", "RefUrl": "/notes/1643878"}, {"RefNumber": "1617641", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1617641"}, {"RefNumber": "1616598", "RefComponent": "BC-SEC-LGN", "RefTitle": "Enabling RFC password logon despite SNC", "RefUrl": "/notes/1616598"}, {"RefNumber": "1561161", "RefComponent": "BC-SEC-LGN", "RefTitle": "Enabling SAP GUI password logon despite using SNC", "RefUrl": "/notes/1561161"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3089624", "RefComponent": "BC-SEC-SNC", "RefTitle": "How to massively update the flag \"Allow password logon for SAP GUI (user-specific)\" for users", "RefUrl": "/notes/3089624 "}, {"RefNumber": "2987807", "RefComponent": "HAN-STD-ADM", "RefTitle": "How to block unencrypted communication when creating BW Project in HANA Studio", "RefUrl": "/notes/2987807 "}, {"RefNumber": "2510046", "RefComponent": "BC-IAM-SSO-SL", "RefTitle": "\"SNC required for this connection\" error using SNC Client Encryption 2.0", "RefUrl": "/notes/2510046 "}, {"RefNumber": "3426823", "RefComponent": "BC-SEC-LGN", "RefTitle": "Accept RFC connections from ABAP clients for virtual users without SNC also if snc/accept_insecure_rfc is not set to 1", "RefUrl": "/notes/3426823 "}, {"RefNumber": "2122578", "RefComponent": "BC-SEC-SNC", "RefTitle": "New: Security Audit Log event for unencrypted GUI / RFC connections", "RefUrl": "/notes/2122578 "}, {"RefNumber": "1701870", "RefComponent": "BC-MID-RFC", "RefTitle": "RFC client communication supporting SNC without SSO", "RefUrl": "/notes/1701870 "}, {"RefNumber": "1728283", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 721: General Information", "RefUrl": "/notes/1728283 "}, {"RefNumber": "1643878", "RefComponent": "BC-IAM-SSO-SL", "RefTitle": "Release Notes for SNC Client Encryption", "RefUrl": "/notes/1643878 "}, {"RefNumber": "1616598", "RefComponent": "BC-SEC-LGN", "RefTitle": "Enabling RFC password logon despite SNC", "RefUrl": "/notes/1616598 "}, {"RefNumber": "1561161", "RefComponent": "BC-SEC-LGN", "RefTitle": "Enabling SAP GUI password logon despite using SNC", "RefUrl": "/notes/1561161 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "KRNL32NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.38", "To": "7.38", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.38", "To": "7.38", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "740", "To": "740", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.38", "To": "7.38", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70029", "URL": "/supportpackage/SAPKB70029"}, {"SoftwareComponentVersion": "SAP_BASIS 701", "SupportPackage": "SAPKB70113", "URL": "/supportpackage/SAPKB70113"}, {"SoftwareComponentVersion": "SAP_BASIS 702", "SupportPackage": "SAPKB70213", "URL": "/supportpackage/SAPKB70213"}, {"SoftwareComponentVersion": "SAP_BASIS 710", "SupportPackage": "SAPKB71016", "URL": "/supportpackage/SAPKB71016"}, {"SoftwareComponentVersion": "SAP_BASIS 711", "SupportPackage": "SAPKB71111", "URL": "/supportpackage/SAPKB71111"}, {"SoftwareComponentVersion": "SAP_BASIS 730", "SupportPackage": "SAPKB73009", "URL": "/supportpackage/SAPKB73009"}, {"SoftwareComponentVersion": "SAP_BASIS 731", "SupportPackage": "SAPKB73106", "URL": "/supportpackage/SAPKB73106"}, {"SoftwareComponentVersion": "SAP_BASIS 740", "SupportPackage": "SAPKB74001", "URL": "/supportpackage/SAPKB74001"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP KERNEL 7.38 64-BIT", "SupportPackage": "SP002", "SupportPackagePatch": "000002", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200022123&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.38 64-BIT UNICODE", "SupportPackage": "SP002", "SupportPackagePatch": "000002", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67838200100200019649&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT", "SupportPackage": "SP013", "SupportPackagePatch": "000013", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT UNICODE", "SupportPackage": "SP013", "SupportPackagePatch": "000013", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP013", "SupportPackagePatch": "000013", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP013", "SupportPackagePatch": "000013", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT", "SupportPackage": "SP013", "SupportPackagePatch": "000013", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021283&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT UC", "SupportPackage": "SP013", "SupportPackagePatch": "000013", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021284&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP013", "SupportPackagePatch": "000013", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP013", "SupportPackagePatch": "000013", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT", "SupportPackage": "SP033", "SupportPackagePatch": "000033", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT UNICODE", "SupportPackage": "SP033", "SupportPackagePatch": "000033", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP033", "SupportPackagePatch": "000033", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP033", "SupportPackagePatch": "000033", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT", "SupportPackage": "SP033", "SupportPackagePatch": "000033", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021283&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT UC", "SupportPackage": "SP033", "SupportPackagePatch": "000033", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021284&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP033", "SupportPackagePatch": "000033", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP033", "SupportPackagePatch": "000033", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}