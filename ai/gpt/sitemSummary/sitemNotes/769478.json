{"Request": {"Number": "769478", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 213, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015752022017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000769478?language=E&token=2BBAF468B4D4A294EF99E30281473BD6"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000769478", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000769478/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "769478"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.08.2005"}, "SAPComponentKey": {"_label": "Component", "value": "BC-XI"}, "SAPComponentKeyText": {"_label": "Component", "value": "NetWeaver Process Integration (PI)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "NetWeaver Process Integration (PI)", "value": "BC-XI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-XI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "769478 - Remote connection with XI systems"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>SAP Support requires a remote connection to an XI customer system.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>XI tools WTS pcanywhere NetMeeting Citrix HTTP connect Telnet</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>SAP Support requires additional help for troubleshooting.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>A connection to the ABAP is required for the XI runtime:</p> <UL><UL><LI>R/3 Support</LI></UL></UL> <p><br />For a complete error analysis on an XI system, SAP Support often require remote access to a Windows PC to be able to start the XI tools (repository and directory).<br />You have the following options to open this connection:</p> <UL><UL><LI>Windows terminal server connection - see note 605795.</LI></UL></UL> <UL><UL><LI>T.120 NetMeeting connection - see note 356635.</LI></UL></UL> <UL><UL><LI>pcANYWHERE connection - see note 100740.</LI></UL></UL> <UL><UL><LI>Citrix metaframe - see note 701588.</LI></UL></UL> <p><br /><br />As of XI 3.0, direct connections to the tools are possible</p> <UL><UL><LI>Service connections \"Integration Repository/Directory\"   - Note 800267</LI></UL></UL> <p><br />If the XI tools are not required, an HTTP connection is often sufficient:</p> <UL><UL><LI>Set up the HTTP Connect service - see note 592085.</LI></UL></UL> <p><br />For a UNIX server, a Telnet connection may also be useful:</p> <UL><UL><LI>Setting up a Telnet connection to a customer system - see note 37001.</LI></UL></UL> <p><br />Logon data can be stored in the secure area of the message:</p> <UL><UL><LI>Logon data of the customer - see note 508140.</LI></UL></UL> <p><br />You should also read the composite SAP note for service connections:</p> <UL><UL><LI>Service connections: Composite SAP note (Overview) - note 35010</LI></UL></UL> <p><br />and the information on the following web page, http://service.sap.com/access-support.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D021285)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D022245)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000769478/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000769478/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000769478/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000769478/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000769478/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000769478/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000769478/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000769478/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000769478/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "854536", "RefComponent": "BC-XI-CON-AFW", "RefTitle": "Adapter Framework: Information Required by SAP Support", "RefUrl": "/notes/854536"}, {"RefNumber": "814334", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/814334"}, {"RefNumber": "813656", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/813656"}, {"RefNumber": "701588", "RefComponent": "XX-SER-NET", "RefTitle": "Setting up Service Citrix MetaFrame connection", "RefUrl": "/notes/701588"}, {"RefNumber": "617604", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Services for SAP PI/XI", "RefUrl": "/notes/617604"}, {"RefNumber": "605795", "RefComponent": "XX-SER-NET", "RefTitle": "Windows Terminal Server connection in remote support", "RefUrl": "/notes/605795"}, {"RefNumber": "592085", "RefComponent": "XX-SER-NET", "RefTitle": "Set up the HTTP Connect service", "RefUrl": "/notes/592085"}, {"RefNumber": "1912318", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "WTS Connection with NLA in remote support", "RefUrl": "/notes/1912318"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1912318", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "WTS Connection with NLA in remote support", "RefUrl": "/notes/1912318 "}, {"RefNumber": "605795", "RefComponent": "XX-SER-NET", "RefTitle": "Windows Terminal Server connection in remote support", "RefUrl": "/notes/605795 "}, {"RefNumber": "617604", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Services for SAP PI/XI", "RefUrl": "/notes/617604 "}, {"RefNumber": "730870", "RefComponent": "BC-XI-CON-RFC", "RefTitle": "FAQ PI/PO RFC Adapter", "RefUrl": "/notes/730870 "}, {"RefNumber": "854536", "RefComponent": "BC-XI-CON-AFW", "RefTitle": "Adapter Framework: Information Required by SAP Support", "RefUrl": "/notes/854536 "}, {"RefNumber": "701588", "RefComponent": "XX-SER-NET", "RefTitle": "Setting up Service Citrix MetaFrame connection", "RefUrl": "/notes/701588 "}, {"RefNumber": "592085", "RefComponent": "XX-SER-NET", "RefTitle": "Set up the HTTP Connect service", "RefUrl": "/notes/592085 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}