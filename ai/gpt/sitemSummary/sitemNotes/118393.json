{"Request": {"Number": "118393", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 385, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014598342017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=236BBE072A70ACB2699C1659CBE5EF0A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "118393"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 39}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.12.2001"}, "SAPComponentKey": {"_label": "Component", "value": "EC-CS-CSF-H"}, "SAPComponentKeyText": {"_label": "Component", "value": "Elim. of IU profit/loss in inventory"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Enterprise Controlling", "value": "EC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Legal Consolidation", "value": "EC-CS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EC-CS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Consolidation Functions", "value": "EC-CS-CSF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EC-CS-CSF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Elim. of IU profit/loss in inventory", "value": "EC-CS-CSF-H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EC-CS-CSF-H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "118393 - EC-CS: P/L in inventory available for 4.0A/B"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You want to use the elimination of IU profit/loss in inventory in the EC-CS Consolidation component in Release 4.0A/B.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Elimination of IU profit and loss, inventory</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You are using R/3 Release 4.0A/4.0B. However, the function for the elimination of interunit profit and loss in the inventory is only delivered as of Release 4.5.<br />If you are not using Hot Package status 18 or later, implement the advance corrections from Note 151272.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Note that no menu options and IMG activities are available for this advance delivery of the Release 4.5A function. Importing transport files requires reprocessing measures.<br /><br />Diownload the transport files R900507.I40 and K900507.I40 from directory /general/R3server/abap/note.0118393 on SAPSERVX and import them into your system.<br />You can find the online documentation in HTML format in the files IPI_DE.htm (German) and IPI_EN.htm (English).<br /><br />You may have to activate view cluster VC_TF580 before you can use the new function. To do this, use Transaction SE54. Choose 'Edit viewcluster'. You now see a screen where you can enter the view cluster VC_TF580. Choose 'Create/change'. Several dialog boxes are now displayed, which you confirm by pressing Enter. Before you reach the change mode, you have to enter an access key for the view cluster. You can obtain this via OSS. In the middle of the change screen, you see the 'Activate' pushbutton. Now activate the view cluster. You are asked: \"Should the subobject maintenance screens be modified?\". Choose \"No\". The view cluster VC_TF580 has now been activated.<br />When you import you may find the message 'Maintenance view variant not activated' is generated due to a base error in Release 4.0. If this happens, activate the view variants VV_TF591_591, VV_TF591_592, VV_TF591_593 and VV_TF591_597. To do this start Transaction SE54 and choose 'Edit view variant' and then proceed in the same way as for the view cluster.<br />Additionally, you may have to maintain the events for view V_TF583. For this choose the menu path Environment -&gt; Events in Transaction SE54. On the following screen, click on \"New entries\" and enter the event \"AA\" for \"T\" (without quotes) and FORM V_TF583_READ for FORM routine and save subsequently.<br /><br />Assign a new special version to the consolidation version for the elimination of IU profit/loss in inventory.<br /><br />Then you may call the function using the following transactions:<br /><br />Proceed:<br />CX5U0 Elimination of IU Profit/Loss in transferred inventory<br />When status management is active, the status of the task for elimination of IU profit/loss in transferred inventory is checked and updated in the consolidation monitor when carrying out Transaction CX5U0. Execution of the task from within the consolidation monitor is not possible with the preliminary delivery for 4.0B, however.<br /><br />Customizing:<br />CX5U1 Product groups + Inventory items<br />CX5U4 Elim. IU P/L in inv.: Posted items: Offset entry<br />CX5U5 Elim. IU P/L in inv.: Posted items: Translation Diffs.<br />CX5U6 Elim. IU P/L in inv.: Posted items: Reposting Distr. Costs<br />CX5UA Elim. IU P/L in inv.: Posted items: Distribution Costs<br />CX5T7 Elim. IU P/L in inv.: Tasks<br /><br />In addition, ABAP reports FICIPI85 and FICIPI86 are available which you can use to list the additional financial data.<br /><br />Please note that the language-specific texts are only available in German and English.<br />If you need alternative languages, translate the following objects into the required language:</p> <OL>1. Messages:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Class G00, message numbers 500 - 526</OL> <OL>2. Program texts: FICIPI00, FICIPI10, FICIPI85, FICIPI86, SAPMF230</OL> <OL>3. Screen texts:&#x00A0;&#x00A0;SAPMF230, screen number 1000<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPL02FC, screen number 2001<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPLA5FC, screen number 5850<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPLA5FC, screen number 5851<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPLA5FC, screen number 5860<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPLA5FC, screen number 5861</OL> <OL>4. Data elements: FC_PRGRP, FC_IPIVS, FC_BKVAL, FC_VLCNL, FC_VLCLF,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;FC_QUANT, FC_ADDCO, FC_ACPCT, FC_PERCR, FC_APIND,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;FC_BPIND, FC_CSPCT, FC_COGMT, FC_CMPCT, FC_ITKEYIP,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;FC_DOCPP, FC_EXRINDIP, FC_POIND, FC_NPIND,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;FC_IP86CR, FC_IPICR, FC_IPICR2</OL> <OL>5. Domains:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; FC_ITKEYIP, FC_IP86CR, FC_IPICR, FC_IPICR2</OL> <OL>6. Dialog text:&#x00A0;&#x00A0; ECCS_IPI00</OL> <p><br />Refer also to the corrections in the attached notes.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D021715)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "505046", "RefComponent": "CA-EUR-CNV-EC", "RefTitle": "EC-CS: Additional financial data not converted to IPI", "RefUrl": "/notes/505046"}, {"RefNumber": "445228", "RefComponent": "EC-CS-CSF-H", "RefTitle": "SAP Cons.: Rounding During IU Profit/Loss Calculation", "RefUrl": "/notes/445228"}, {"RefNumber": "376535", "RefComponent": "EC-CS-CSF-H", "RefTitle": "SAP CS: Equity CUs in elimination of IU profit+loss", "RefUrl": "/notes/376535"}, {"RefNumber": "355549", "RefComponent": "EC-CS-CSF-H", "RefTitle": "SAP Cons.: Arithmetical overflow w/ supplier data", "RefUrl": "/notes/355549"}, {"RefNumber": "308036", "RefComponent": "EC-CS-CSF-H", "RefTitle": "SAP Cons.: mssgs fr elmntn of IU prft/lss nt dsplyd", "RefUrl": "/notes/308036"}, {"RefNumber": "300401", "RefComponent": "EC-CS-CSF-H", "RefTitle": "SAP cons.: incorrect setting used for elim. IU P+L", "RefUrl": "/notes/300401"}, {"RefNumber": "216088", "RefComponent": "EC-CS-CSF-H", "RefTitle": "SAP cons.:Determin.prior period f.elim. IU prf/loss", "RefUrl": "/notes/216088"}, {"RefNumber": "205471", "RefComponent": "EC-CS-CSF-H", "RefTitle": "Cons:elim. IU P/L in inv: add.fin.data in grp curr.", "RefUrl": "/notes/205471"}, {"RefNumber": "199397", "RefComponent": "EC-CS-CSF-H", "RefTitle": "SAP cons: log elim. of IU P+L: deb/cred sign credit", "RefUrl": "/notes/199397"}, {"RefNumber": "181184", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/181184"}, {"RefNumber": "180497", "RefComponent": "EC-CS", "RefTitle": "EC-CS: Customizing settings not found", "RefUrl": "/notes/180497"}, {"RefNumber": "177619", "RefComponent": "EC-CS-CSF-H", "RefTitle": "EC-CS: Elim.IU P/L inv.: check pre-period doc.type", "RefUrl": "/notes/177619"}, {"RefNumber": "170669", "RefComponent": "EC-CS-CSF-H", "RefTitle": "EC-CS: positioning in supplier data", "RefUrl": "/notes/170669"}, {"RefNumber": "169489", "RefComponent": "EC-CS-CSF-H", "RefTitle": "EC-CS: elim.IU P/L: setting for task missing", "RefUrl": "/notes/169489"}, {"RefNumber": "164266", "RefComponent": "EC-CS-CSF-H", "RefTitle": "EC-CS: preperd postng unappr.retaind earngs bal.sht", "RefUrl": "/notes/164266"}, {"RefNumber": "160677", "RefComponent": "EC-CS-CSF-H", "RefTitle": "EC-CS: elim. IU P/L inv.: old documents not deleted", "RefUrl": "/notes/160677"}, {"RefNumber": "155981", "RefComponent": "EC-CS-CSF-H", "RefTitle": "EC-CS: Conversion of additional financial data", "RefUrl": "/notes/155981"}, {"RefNumber": "151710", "RefComponent": "EC-CS-CSF-H", "RefTitle": "EC-CS: document for proportional consolidation unit", "RefUrl": "/notes/151710"}, {"RefNumber": "151406", "RefComponent": "EC-CS-CSF-H", "RefTitle": "EC-CS: elim. IU P/L:missing messages in update run", "RefUrl": "/notes/151406"}, {"RefNumber": "151272", "RefComponent": "EC-CS-CSF-H", "RefTitle": "EC-CS: Precond. downgrade elim.IU P/L in inv. 4.0B", "RefUrl": "/notes/151272"}, {"RefNumber": "147093", "RefComponent": "EC-CS-CSF-H", "RefTitle": "EC-CS: Pre-period posting for automatic reversal", "RefUrl": "/notes/147093"}, {"RefNumber": "138821", "RefComponent": "EC-CS-CSF-H", "RefTitle": "EC-CS: unnecessary currency translation differences", "RefUrl": "/notes/138821"}, {"RefNumber": "138337", "RefComponent": "EC-CS-CSF-H", "RefTitle": "EC-CS: Addit.fin.data / elim of IU P/L in inv./4.0B", "RefUrl": "/notes/138337"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "445228", "RefComponent": "EC-CS-CSF-H", "RefTitle": "SAP Cons.: Rounding During IU Profit/Loss Calculation", "RefUrl": "/notes/445228 "}, {"RefNumber": "216088", "RefComponent": "EC-CS-CSF-H", "RefTitle": "SAP cons.:Determin.prior period f.elim. IU prf/loss", "RefUrl": "/notes/216088 "}, {"RefNumber": "205471", "RefComponent": "EC-CS-CSF-H", "RefTitle": "Cons:elim. IU P/L in inv: add.fin.data in grp curr.", "RefUrl": "/notes/205471 "}, {"RefNumber": "355549", "RefComponent": "EC-CS-CSF-H", "RefTitle": "SAP Cons.: Arithmetical overflow w/ supplier data", "RefUrl": "/notes/355549 "}, {"RefNumber": "180497", "RefComponent": "EC-CS", "RefTitle": "EC-CS: Customizing settings not found", "RefUrl": "/notes/180497 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "138337", "RefComponent": "EC-CS-CSF-H", "RefTitle": "EC-CS: Addit.fin.data / elim of IU P/L in inv./4.0B", "RefUrl": "/notes/138337 "}, {"RefNumber": "505046", "RefComponent": "CA-EUR-CNV-EC", "RefTitle": "EC-CS: Additional financial data not converted to IPI", "RefUrl": "/notes/505046 "}, {"RefNumber": "151272", "RefComponent": "EC-CS-CSF-H", "RefTitle": "EC-CS: Precond. downgrade elim.IU P/L in inv. 4.0B", "RefUrl": "/notes/151272 "}, {"RefNumber": "376535", "RefComponent": "EC-CS-CSF-H", "RefTitle": "SAP CS: Equity CUs in elimination of IU profit+loss", "RefUrl": "/notes/376535 "}, {"RefNumber": "308036", "RefComponent": "EC-CS-CSF-H", "RefTitle": "SAP Cons.: mssgs fr elmntn of IU prft/lss nt dsplyd", "RefUrl": "/notes/308036 "}, {"RefNumber": "300401", "RefComponent": "EC-CS-CSF-H", "RefTitle": "SAP cons.: incorrect setting used for elim. IU P+L", "RefUrl": "/notes/300401 "}, {"RefNumber": "199397", "RefComponent": "EC-CS-CSF-H", "RefTitle": "SAP cons: log elim. of IU P+L: deb/cred sign credit", "RefUrl": "/notes/199397 "}, {"RefNumber": "177619", "RefComponent": "EC-CS-CSF-H", "RefTitle": "EC-CS: Elim.IU P/L inv.: check pre-period doc.type", "RefUrl": "/notes/177619 "}, {"RefNumber": "170669", "RefComponent": "EC-CS-CSF-H", "RefTitle": "EC-CS: positioning in supplier data", "RefUrl": "/notes/170669 "}, {"RefNumber": "169489", "RefComponent": "EC-CS-CSF-H", "RefTitle": "EC-CS: elim.IU P/L: setting for task missing", "RefUrl": "/notes/169489 "}, {"RefNumber": "164266", "RefComponent": "EC-CS-CSF-H", "RefTitle": "EC-CS: preperd postng unappr.retaind earngs bal.sht", "RefUrl": "/notes/164266 "}, {"RefNumber": "160677", "RefComponent": "EC-CS-CSF-H", "RefTitle": "EC-CS: elim. IU P/L inv.: old documents not deleted", "RefUrl": "/notes/160677 "}, {"RefNumber": "147093", "RefComponent": "EC-CS-CSF-H", "RefTitle": "EC-CS: Pre-period posting for automatic reversal", "RefUrl": "/notes/147093 "}, {"RefNumber": "155981", "RefComponent": "EC-CS-CSF-H", "RefTitle": "EC-CS: Conversion of additional financial data", "RefUrl": "/notes/155981 "}, {"RefNumber": "151710", "RefComponent": "EC-CS-CSF-H", "RefTitle": "EC-CS: document for proportional consolidation unit", "RefUrl": "/notes/151710 "}, {"RefNumber": "151406", "RefComponent": "EC-CS-CSF-H", "RefTitle": "EC-CS: elim. IU P/L:missing messages in update run", "RefUrl": "/notes/151406 "}, {"RefNumber": "138821", "RefComponent": "EC-CS-CSF-H", "RefTitle": "EC-CS: unnecessary currency translation differences", "RefUrl": "/notes/138821 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}