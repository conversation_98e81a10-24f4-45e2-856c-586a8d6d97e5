{"Request": {"Number": "1769670", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 443, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017527172017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001769670?language=E&token=2E30BE832FCF1B463733A9883C8F4901"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001769670", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001769670/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1769670"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "19.04.2013"}, "SAPComponentKey": {"_label": "Component", "value": "HAN-DB-ENG-BW"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP HANA BW Engine"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP HANA", "value": "HAN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'HAN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP HANA Database", "value": "HAN-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'HAN-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP HANA DB Engines", "value": "HAN-DB-ENG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'HAN-DB-ENG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP HANA BW Engine", "value": "HAN-DB-ENG-BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'HAN-DB-ENG-BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1769670 - Important Notes for SAP BW powered by HANA on SP8"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You would like to use your SAP BW system on SAP In-Memory Database after successful migration/installation.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Support Package</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>SAP highly recommends the following notes on top of SAP Netweaver SP08 when your system is running on HANA DB.<br />If you are on 731, this is applicable for SP5 and SP6. (SP6: not all notes still necessary)<br /><br />Please note that upon availability Support Package 9 for SAP NetWeaver BW 730 (SAPKW73009) will provide the opportunity to apply all important fixes to customers who do run the NetWeaver BW 730 powered by SAP HANA (or 731 SP7).<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Note 1767502 InfoCube Konvertierung: MPRO Laufzeitobjekt validieren<br />Note 1767014 RSDU_TABLE_CONSISTENCY meldet zu viele Inkonsistenzen<br />Note 1766059 RSDDSTAT: Werte in der Tabelle RSDDSTATBIAUSE zu hoch<br />Note 1765358 Performanceverbesserung: Lesen v. InfoObjects (Nav.-attrib.)<br />Note 1765286 Deadlock auf Tabelle RSDDSTATBIAUSE im Mehrbenutzerbetrieb<br />Note 1760688 Update of the LASTUSED timestamp for query componentes<br />Note 1751219 DB02: fehlende Sekund&#x00E4;rindexes<br />Note 1272916 Displaying Texts for hierarchy node variable in BEx<br /> Check that InfoObjects are configured as described in the note.<br />Note 984021 Changes to InfoObject 0LANGU<br /> Check that InfoObjects are configured as described in the note.<br />1777509 Poor OLAP cache performance on mode 5 'BLOB/Cluster Enh.'<br />1759172 BW metadata API: Error when activating SAP HANA views<br />1769374 BW Metadaten API: Unterst&#x00FC;tzung f&#x00FC;r Query Snapshot Modelle<br />1794792 BW Metadaten API - Assert bei DSOs mit read-only Merkmalen<br />1756099 RSHDB: Tables Consistency Check (7.30 SP9)<br />1754729 SHDB: Konsistenzpr&#x00FC;fungen auf verteilten HANA<br />1764247 RSBOLAP: Deletion of QuerySettings buffer after var. change<br />1767755 Lange Laufzeit beim Zugriff auf SPs<br />1773025 Ausf&#x00FC;hren und Erkl&#x00E4;ren bricht ab<br />1788145 BICS: calling variable screen twice removes the currency<br />1783421 Incorrect query results for MultiProvider with LPOA parts<br />1762935 HANADB: Using the compressed change log (delta)<br />1783817 Meldung \"View Attribute \"0REQUID_SID\" not found\" angezeigt<br />1780575 Konvertierung scheitert mit Fehler 59<br />1760745 SAP HANA for BW: search result error 2618<br />1770457 730SP9:DBIF_RSQL_INVALID_RSQL in CL_RSDMD_LOOKUP_MASTER_DATA<br />1720595 SP30:Performance optimization - Master data prefetch<br />1756630 Nachkorrektur zu Hinweis 1750766<br />1630248 SP30:Field descriptions of Errorstack gets lost<br />1691977 DBACockpit: CX_SY_STRUCT_COMP_NAME from INI FILE application<br />1714492 Technical BW problems for queries with many hierarchies<br />1720370 BICS: Hierarchy Level is incorrect in selector of VAR screen<br />1730806 Potential data loss with RSAR_PSA_CLEANUP_DIRECTORY/_MS<br />1733477 Increasing wait time for enqueueing PSA request<br />1734555 SNOTE: Performance fix in Interface enhancement<br />1738185 F4: report displayes all available master data<br />1745472 Missing hierarchy nodes in MDX queries (2)<br />1746755 RSPCM navigates to the wrong chain run at times.<br />1748342 Unusual drilldowns w/artificial characteristic currency/unit<br />1748473 Wrong data with selections on time characteristic<br />1748929 P30:Performance:RSBM_ERRORLOG_DELETE:Vollst. Daten L&#x00F6;schen<br />1749848 DSO: SAP-HANA-optimierte DSOs f&#x00FC;r Mengenumrechnung<br />1749978 SP30:Auth.check switched off from extraction of MD export DS<br />1750462 TREX connection error on HANA system<br />1750721 SPO: Query on SAP HANA-optimized InfoCube SPO terminates<br />1750766 Der generierte Report enth&#x00E4;lt gleichen Code mehrfach<br />1750896 DUMP in CL_RSLPO_VERS_CMPR_VIEW<br />1750925 HANA DB: Improved log for SAP HANA-optimized DataStores<br />1751321 Fehlende Hierarchieknoten in MDX Queries (3)<br />1751739 Temporary indexes are not deleted in the input help<br />1751768 BIA: Memory overflow when query is executed<br />1751878 Falsches Aggregationsverhalten bei 'ODSO like' InfoSource<br />1752626 NO2 aggregation for InfoCubes fails/activate<br />1753692 SP09 BW on SAP HANA: Adjustment RSCDS_CHECK_EPART_DUPLICATES<br />1753959 Incorrect data or missing hierarchy nodes in query<br />1754680 Various errors for F4 on char. and char. as InfoProvider<br />1757340 DBIF_RSQL_INVALID_RSQL for MOST_RECENT master data access<br />1758163 Query cannot be generated without errors<br />1760763 730SP9: Error activating TRF having DSO and MD lookups<br />1761214 P09:DTP:Cockpit zeigt negative Laufzeiten an; rsddstatdtp<br />1764928 Zu viele Daten in einer speziellen Situation<br />1765506 SP30:'RS_PSA_TABTYPE_EXISTENCE_CHECK' to check PSA tab types<br />1765533 Korrekturen f&#x00FC;r BW auf HANA DB, 7.30 SP9, 7.31 SP6<br />1765584 DSO: Inconsistency w/regard to primary index for DB object<br />1765760 Tempor&#x00E4;re DB Tabellen im Konsistenzcheck<br />1765830 Hot/Cold - give PARTNO-information to database<br />1766901 P30:ODS:DSO: Activation terminates for last package<br />1766993 Disaggregation does not distribute vals for unposted vals<br />1767119 730SP9:Changelog of IMO DSO handled incorrect by PSA tools<br />1767399 Einheits- and W&#x00E4;hrungsumrechnung in BW Calcscenario Defs.<br />1767880 Non-active data concept for BW on SAP HANA DB<br />1768020 CHECK_BIA_AVAILABLE Prog: CL_RSDRC_SEL_SPLITTER<br />1769038 SAP HANA BW: improve error message from TREX_EXT_AGGREGATE<br />1772501 DSO: Partitioning of write-optimized DSOs in SAP HANA<br />1772882 _PSA_CHANGES_CHECK: RFC-Fehler auswerten<br />1774561 730SP9: 'Assertion failed' during deleting a link of rule<br />1774577 P09:REQARCH:Kein RSREQARCHMON-Eintrag f&#x00FC;r archiv. Requests<br />1776030 Generation error for exception aggregation first/last value<br />1776111 RSZDELETE: Selection by LASTUSED timestamp does not work<br />1776749 DSO: SAP HANA conversion for write-optimized DSOs<br />1778066 SP30:ASSERTION_FAILED during Save/activation or TRF<br />1778553 Leafs or nodes display as # in F4<br />1782540 Migration nach SAP-HANA-DB: Import bricht mit SQL-Fehler ab<br />1783680 Texte werden mit \"nicht zugeordnet\" angezeigt<br />1784613 730SP9:DSO lookup doesnt work correctly<br />1784970 SPO: Activation in after-import mode<br />1785116 SPO: SAP-HANA-Konvertierung f&#x00FC;r schreiboptimierte DSO-SPOs<br />1786109 mehrere Ausnahmeaggregation und erster/letzter Wert<br />1786311 SAP HANA for BW: search with '*' returns too many results<br />1786559 Hierarchieknoten-F4 benutzt keinen BWA/HANA Zugriff<br />1786576 InfoProvidereinschr&#x00E4;nkung wird im getMembers nicht verwendet<br />1786682 Peformance: Queries mit vielen Strukturmembern<br />1787929 Performanceoptimierung F4 Hilfe im Modus D auf MultiProvider<br />1788431 Slow parallel execution of the bookmarks<br />1789234 RSHDB: TabLocation - Fact tables are never on master<br />1793318 BW Query Entry Support f&#x00FC;r Calculation Scenarios<br />1793436 Verbesserung der Anlegeperformance in BW Calcscenarien<br />1797600 Selektion/Berechtigungspr&#x00FC;fung auf Hierarchieknoten<br />1803064 SAP HANA: further analysis of ABAP errors via TREX_EXT_*<br />1803613 Wertehilfe im F4-Modus D f&#x00FC;r zeitabh&#x00E4;ngige NavAttribute<br />1790333 Erweiterungen der Remote API f&#x00FC;r BW Metadaten Information<br />1752384 Performance improvements of BW metadata interface<br />1809127 GETWA_NOT_ASSIGNED in der Klasse CL_RSL_ME_METADATA<br />1801194 Bei Systemnamen-Umsetzung DB-Merge f&#x00FC;r PSAs ansto&#x00DF;en<br />1776186 SAP HAHA BW - Scale out: routing to right indexserver<br />1798043 SAP HANA BW: lock error on shared memory object<br />1781314 leere PSA Tabelle hat mehrere Partitionen<br />1767496 Exception aggregation AV0 in HANA/BWA is incorrect<br />1783330 Problems with IMO DSO Activation<br />1793826 connected filters combined with *toN joins not<br />1808846 Verbesserung Log f&#x00FC;r PSA-Versionierung in Post-Migration<br />1809892 Enable extraction from old versions of a DataSource<br />1790134 Fehlermeldung bei RSMIGRHANADB wg. falscher Partitionierung<br />1790134 Fehlermeldung bei RSMIGRHANADB wg. falscher Partitionierung<br />1783238 SMIGR_CREATE_DDL: Estimated Row Count f&#x00FC;r Faktentabellen<br />1771922 Schlechte Performance bei nur 'excluding' Bedingung auf SAP HANA<br />1781787 Best&#x00E4;nde: historische Deltas bei Neuaufbau valid-tab<br />1791902 Multiprov: Fehler beim Partition-Pruning f&#x00FC;r SPO<br />1785210 BW auf HANA: Logischer Index nach Konvertierung von InfoCube<br />1786388 BW auf HANA: Konvertierung des InfoCubes schl&#x00E4;gt fehl<br />1786390 BW auf HANA: Daten laden in einen InfoCube bricht ab<br />1773398 SAP HANA: No compression for scale-out &amp; non-optimized cube<br />1770163 BW HANA: InfoCube compression after conversion terminates<br />1769321 BW HANA: Request status after compressing empty requests<br />1766577 Converting non-cumulative InfoCubes: All requests compressed<br />1800483 Nach selektiven L&#x00F6;schen ist InMemory DSO ChangeLog leer<br />1768641 BDLS dumpt mit DBIF_RSQL_SQL_ERROR auf HANA BW System<br />1796960 BW Stammdatenprovider-Query endet mit Dump CHECK_KHANDLE<br />1798043 SAP HANA BW: lock error on shared memory object<br />1776186 SAP HAHA BW - Scale out: routing to right indexserver<br />1789992 HANADB: Fehlerhafter RFC-Kontext bei Konvertierung von DSOs<br />1742707 HANA-DB: Column view reactivation if InfoObjects are changed<br />1796184 Condensor: Termination CONVT_NO_NUMBER in CL_RSCDS_COMPR_INV<br />1777403 Commit is not performed after modifying table rszwmditemdocu<br />1807404 Nach Upgrade: Falsche Interpretation einiger Eigenschaften<br />1710832 HANA BW: I_RESULT_INDEX_NAME with TREX_EXT_AGGREGATE<br />1841927 Error-Counter ist 1 bei serieller Verarbeitung<br /><br />1842238 Extraktion aus Errorstack langsam auf HANA-DB<br />1844042 Fehler beim Hinzuf&#x00FC;gen eines neuen Nav.attr. zu einem IOBJ<br />1818430 Value help: Error during plan execution;Failed to find source column<br />1769377 Konstante Mappings auf NULL-Wert in BW CalcScenarios<br />1819123 SAP HANA Landscape Reorg<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D027968)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D024795)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001769670/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001769670/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001769670/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001769670/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001769670/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001769670/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001769670/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001769670/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001769670/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "984021", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "Changes to InfoObject 0LANGU", "RefUrl": "/notes/984021"}, {"RefNumber": "1845120", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "SAP HANA DB: Column view generation incorrect for InfoObject", "RefUrl": "/notes/1845120"}, {"RefNumber": "1844042", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "Error when adding a new navigation attribute to an IOBJ", "RefUrl": "/notes/1844042"}, {"RefNumber": "1842238", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Extraction from error stack slow on SAP HANA DB", "RefUrl": "/notes/1842238"}, {"RefNumber": "1841927", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Error counter is 1 during serial processing", "RefUrl": "/notes/1841927"}, {"RefNumber": "1819123", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1819123"}, {"RefNumber": "1818430", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help: Error during plan execution;Failed to find sourc", "RefUrl": "/notes/1818430"}, {"RefNumber": "1809892", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Enable extraction from old versions of a DataSource", "RefUrl": "/notes/1809892"}, {"RefNumber": "1809127", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "GETWA_NOT_ASSIGNED in the class CL_RSL_ME_METADATA", "RefUrl": "/notes/1809127"}, {"RefNumber": "1808846", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "Improvement log for PSA versioning in post-migration", "RefUrl": "/notes/1808846"}, {"RefNumber": "1807404", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Incorrect interpretation of some properties after upgrade", "RefUrl": "/notes/1807404"}, {"RefNumber": "1803613", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Value help in F4 mode D f.time-dependent navigation attrib.", "RefUrl": "/notes/1803613"}, {"RefNumber": "1803064", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA: further analysis of ABAP errors via TREX_EXT_*", "RefUrl": "/notes/1803064"}, {"RefNumber": "1801194", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Triggering DB merge for PSAs when converting system names", "RefUrl": "/notes/1801194"}, {"RefNumber": "1800483", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "In-memory DSO change log is empty after selective deletion", "RefUrl": "/notes/1800483"}, {"RefNumber": "1798043", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA BW: lock error on shared memory object", "RefUrl": "/notes/1798043"}, {"RefNumber": "1797600", "RefComponent": "BW-BEX-OT", "RefTitle": "Selection/authorization check for hierarchy nodes", "RefUrl": "/notes/1797600"}, {"RefNumber": "1796960", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW master data provider query ends with dump CHECK_KHANDLE", "RefUrl": "/notes/1796960"}, {"RefNumber": "1796184", "RefComponent": "BW-BEX-OT-DBIF-CON", "RefTitle": "Condensor: Termination CONVT_NO_NUMBER in CL_RSCDS_COMPR_INV", "RefUrl": "/notes/1796184"}, {"RefNumber": "1794792", "RefComponent": "BW-WHM-MTD", "RefTitle": "BW metadata API - Assert for DSOs w/ read-only char.", "RefUrl": "/notes/1794792"}, {"RefNumber": "1793826", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "connected filters combined with *toN joins not supported", "RefUrl": "/notes/1793826"}, {"RefNumber": "1793436", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Improving creation performance in BW CalculationScenarios", "RefUrl": "/notes/1793436"}, {"RefNumber": "1793318", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BW query entry support for calculation scenarios", "RefUrl": "/notes/1793318"}, {"RefNumber": "1791902", "RefComponent": "BW-BEX-OT", "RefTitle": "MultiProv: <PERSON><PERSON>r during partition pruning for SPO", "RefUrl": "/notes/1791902"}, {"RefNumber": "1790333", "RefComponent": "BW-WHM-MTD", "RefTitle": "Enhancements of remote API for BW metadata information", "RefUrl": "/notes/1790333"}, {"RefNumber": "1790134", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "Error message for RSMIGRHANADB due to incorrect partitioning", "RefUrl": "/notes/1790134"}, {"RefNumber": "1789992", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "HANADB: Incorrect RFC context when converting from DSOs", "RefUrl": "/notes/1789992"}, {"RefNumber": "1788431", "RefComponent": "BW-BEX-ET", "RefTitle": "Slow parallel execution of the bookmarks", "RefUrl": "/notes/1788431"}, {"RefNumber": "1788145", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: calling variable screen twice removes the currency", "RefUrl": "/notes/1788145"}, {"RefNumber": "1787929", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Performance optimization: F4 help in mode D on MultiProvider", "RefUrl": "/notes/1787929"}, {"RefNumber": "1786682", "RefComponent": "BI-RA-BICS", "RefTitle": "Peformance: Queries mit vielen Strukturmembern", "RefUrl": "/notes/1786682"}, {"RefNumber": "1786576", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "InfoProvider restriction is not used in getMembers", "RefUrl": "/notes/1786576"}, {"RefNumber": "1786559", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Hierarchy node input help does not use BWA/SAP HANA access", "RefUrl": "/notes/1786559"}, {"RefNumber": "1786390", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "BW on SAP HANA: Loading data to an InfoCube terminates", "RefUrl": "/notes/1786390"}, {"RefNumber": "1786388", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW on HANA: Conversion of InfoCube fails", "RefUrl": "/notes/1786388"}, {"RefNumber": "1786311", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA for BW: search with '*' returns too many results", "RefUrl": "/notes/1786311"}, {"RefNumber": "1786109", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Multiple exception aggregation and first/last value", "RefUrl": "/notes/1786109"}, {"RefNumber": "1785210", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "BW on HANA: Logical index after converting InfoCube", "RefUrl": "/notes/1785210"}, {"RefNumber": "1785116", "RefComponent": "BW-WHM-DBA-SPO", "RefTitle": "SPO: SAP HANA conversion for write-optimized DSO SPOs", "RefUrl": "/notes/1785116"}, {"RefNumber": "1784970", "RefComponent": "BW-WHM-DBA-SPO", "RefTitle": "SPO: Activation in after-import mode", "RefUrl": "/notes/1784970"}, {"RefNumber": "1784613", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "730SP9:DSO lookup doesnt work correctly", "RefUrl": "/notes/1784613"}, {"RefNumber": "1783817", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Message \"View Attribute \"0REQUID_SID\" not found\" displayed", "RefUrl": "/notes/1783817"}, {"RefNumber": "1783680", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Texts displayed with \"Not Assigned\"", "RefUrl": "/notes/1783680"}, {"RefNumber": "1783421", "RefComponent": "BW-BEX-OT", "RefTitle": "Incorrect query results for MultiProvider with LPOA parts", "RefUrl": "/notes/1783421"}, {"RefNumber": "1783330", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "Problems with IMO DSO Activation", "RefUrl": "/notes/1783330"}, {"RefNumber": "1783238", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "SMIGR_CREATE_DDL: Estimated row count for fact tables", "RefUrl": "/notes/1783238"}, {"RefNumber": "1782540", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "Migration to SAP HANA DB: Import terminates with SQL error", "RefUrl": "/notes/1782540"}, {"RefNumber": "1781787", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Non-cumulatives: Historical deltas with rebuild valid-tab", "RefUrl": "/notes/1781787"}, {"RefNumber": "1781314", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "Empty PSA table has multiple partitions", "RefUrl": "/notes/1781314"}, {"RefNumber": "1780575", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "Conversion fails with error \"RSDRI_HDB 059\"", "RefUrl": "/notes/1780575"}, {"RefNumber": "1778553", "RefComponent": "BI-RA-BICS", "RefTitle": "Leafs or nodes display as # in F4", "RefUrl": "/notes/1778553"}, {"RefNumber": "1778066", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "SP30:ASSERTION_FAILED during Save/activation or TRF", "RefUrl": "/notes/1778066"}, {"RefNumber": "1777509", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "Poor OLAP cache performance on mode 5 'BLOB/Cluster Enh.'", "RefUrl": "/notes/1777509"}, {"RefNumber": "1777403", "RefComponent": "BW-BEX-ET-WJR-AD", "RefTitle": "Commit is not performed after modifying table rszwmditemdocu", "RefUrl": "/notes/1777403"}, {"RefNumber": "1776749", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "DSO: SAP HANA conversion for write-optimized DSOs", "RefUrl": "/notes/1776749"}, {"RefNumber": "1776186", "RefComponent": "HAN-DB-ENG", "RefTitle": "HANA - Scale out: routing to right indexserver", "RefUrl": "/notes/1776186"}, {"RefNumber": "1776111", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "RSZDELETE: Selection by LASTUSED timestamp does not work", "RefUrl": "/notes/1776111"}, {"RefNumber": "1776030", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Generation error for exception aggregation first/last value", "RefUrl": "/notes/1776030"}, {"RefNumber": "1774577", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "P09; REQARCH: No RSREQARCHMON entry for archived requests", "RefUrl": "/notes/1774577"}, {"RefNumber": "1774561", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "730SP9: '<PERSON><PERSON><PERSON> failed' during deleting a link of rule", "RefUrl": "/notes/1774561"}, {"RefNumber": "1773398", "RefComponent": "BW-BEX-OT-DBIF-CON", "RefTitle": "SAP HANA: No compression for scale-out & non-optimized cube", "RefUrl": "/notes/1773398"}, {"RefNumber": "1773025", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Execute and Explain terminates", "RefUrl": "/notes/1773025"}, {"RefNumber": "1772882", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "_PSA_CHANGES_CHECK: Analysing RFC error", "RefUrl": "/notes/1772882"}, {"RefNumber": "1772501", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "DSO: Partitioning of write-optimized DSOs in SAP HANA", "RefUrl": "/notes/1772501"}, {"RefNumber": "1771922", "RefComponent": "BW-BEX-OT", "RefTitle": "Poor performance for only 'Excluding' condition on SAP HANA", "RefUrl": "/notes/1771922"}, {"RefNumber": "1770457", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "730SP9:DBIF_RSQL_INVALID_RSQL in CL_RSDMD_LOOKUP_MASTER_DATA", "RefUrl": "/notes/1770457"}, {"RefNumber": "1770163", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "BW HANA: InfoCube compression after conversion terminates", "RefUrl": "/notes/1770163"}, {"RefNumber": "1769377", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Constant mappings to zero value in BW calculation scenarios", "RefUrl": "/notes/1769377"}, {"RefNumber": "1769374", "RefComponent": "BW-WHM-MTD", "RefTitle": "BW metadata API: Support for query snapshot models", "RefUrl": "/notes/1769374"}, {"RefNumber": "1769321", "RefComponent": "BW-BEX-OT-DBIF-CON", "RefTitle": "BW HANA: Request status after compressing empty requests", "RefUrl": "/notes/1769321"}, {"RefNumber": "1769038", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA BW: improve error message from TREX_EXT_AGGREGATE", "RefUrl": "/notes/1769038"}, {"RefNumber": "1768641", "RefComponent": "BC-MID-ALE", "RefTitle": "BDLS dump occurs w/ DBIF_RSQL_SQL_ERROR on HANA BW system", "RefUrl": "/notes/1768641"}, {"RefNumber": "1768020", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "CHECK_BIA_AVAILABLE Prog: CL_RSDRC_SEL_SPLITTER", "RefUrl": "/notes/1768020"}, {"RefNumber": "1767880", "RefComponent": "BW-WHM", "RefTitle": "Non-active data concept for BW on SAP HANA DB", "RefUrl": "/notes/1767880"}, {"RefNumber": "1767755", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Long runtime when accessing SPs", "RefUrl": "/notes/1767755"}, {"RefNumber": "1767502", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "InfoCube conversion: invalidate MPRO runtime object", "RefUrl": "/notes/1767502"}, {"RefNumber": "1767496", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Exception aggregation AV0 in HANA/BWA is incorrect", "RefUrl": "/notes/1767496"}, {"RefNumber": "1767399", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Unit and currency conversion in BW CalcScenario definitions", "RefUrl": "/notes/1767399"}, {"RefNumber": "1767119", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "730SP9:Changelog of IMO DSO handled incorrect by PSA tools", "RefUrl": "/notes/1767119"}, {"RefNumber": "1767014", "RefComponent": "BC-DB-HDB-POR", "RefTitle": "RSDU_TABLE_CONSISTENCY reports too many inconsistencies", "RefUrl": "/notes/1767014"}, {"RefNumber": "1766993", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Disaggregation does not distribute vals for unposted vals", "RefUrl": "/notes/1766993"}, {"RefNumber": "1766901", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "P30:ODS:DSO: Activation terminates for last package", "RefUrl": "/notes/1766901"}, {"RefNumber": "1766577", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "Converting non-cumulative InfoCubes: All requests compressed", "RefUrl": "/notes/1766577"}, {"RefNumber": "1766059", "RefComponent": "BW-BEX-OT", "RefTitle": "RSDDSTAT: Values in the table RSDDSTATBIAUSE too high", "RefUrl": "/notes/1766059"}, {"RefNumber": "1765830", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "Hot/Cold - give PARTNO-information to database", "RefUrl": "/notes/1765830"}, {"RefNumber": "1765760", "RefComponent": "HAN-DB", "RefTitle": "Temporary DB tables in consistency check", "RefUrl": "/notes/1765760"}, {"RefNumber": "1765584", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "DSO: Inconsistency w/regard to primary index for DB object", "RefUrl": "/notes/1765584"}, {"RefNumber": "1765533", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "Corrections for BW on SAP HANA DB, 7.30 SP9, 7.31 SP6", "RefUrl": "/notes/1765533"}, {"RefNumber": "1765506", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "SP30:'RS_PSA_TABTYPE_EXISTENCE_CHECK' to check PSA tab types", "RefUrl": "/notes/1765506"}, {"RefNumber": "1765358", "RefComponent": "BW-BEX-OT", "RefTitle": "Performance improvement: Reading InfoObjects (nav. attrib.)", "RefUrl": "/notes/1765358"}, {"RefNumber": "1765286", "RefComponent": "BW-BEX-OT", "RefTitle": "Deadlock in table RSDDSTATBIAUSE in MultiUser operation", "RefUrl": "/notes/1765286"}, {"RefNumber": "1764928", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Too much data in specific situation", "RefUrl": "/notes/1764928"}, {"RefNumber": "1764247", "RefComponent": "BW-BEX-OT-BICS-PROV", "RefTitle": "RSBOLAP: Deletion of QuerySettings buffer after var. change", "RefUrl": "/notes/1764247"}, {"RefNumber": "1762935", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "HANADB: Using the compressed change log (delta)", "RefUrl": "/notes/1762935"}, {"RefNumber": "1761214", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P09:DTP:Cockpit displays negative runtimes; rsddstatdtp", "RefUrl": "/notes/1761214"}, {"RefNumber": "1760763", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "730SP9: Error activating TRF having DSO and MD lookups", "RefUrl": "/notes/1760763"}, {"RefNumber": "1760745", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA for BW: search result error 2618", "RefUrl": "/notes/1760745"}, {"RefNumber": "1760688", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Update of the LASTUSED timestamp for query components", "RefUrl": "/notes/1760688"}, {"RefNumber": "1759172", "RefComponent": "BW-WHM-MTD", "RefTitle": "BW metadata API: Error when activating SAP HANA views", "RefUrl": "/notes/1759172"}, {"RefNumber": "1758163", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query cannot be generated without errors", "RefUrl": "/notes/1758163"}, {"RefNumber": "1757340", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "DBIF_RSQL_INVALID_RSQL for MOST_RECENT master data access", "RefUrl": "/notes/1757340"}, {"RefNumber": "1756630", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Subsequent correction to SAP Note 1750766", "RefUrl": "/notes/1756630"}, {"RefNumber": "1756099", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "RSHDB: Consistency check for tables (7.30 SP9)", "RefUrl": "/notes/1756099"}, {"RefNumber": "1754729", "RefComponent": "BC-DB-HDB-POR", "RefTitle": "SHBD: Consistency checking for distributed HANA systems", "RefUrl": "/notes/1754729"}, {"RefNumber": "1754680", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Various errors for F4 on char. and char. as <PERSON>foProvider", "RefUrl": "/notes/1754680"}, {"RefNumber": "1753959", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Incorrect data or missing hierarchy nodes in query", "RefUrl": "/notes/1753959"}, {"RefNumber": "1753692", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "SP09 BW on SAP HANA: Adjustment RSCDS_CHECK_EPART_DUPLICATES", "RefUrl": "/notes/1753692"}, {"RefNumber": "1752626", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "NO2 aggregation for InfoCubes fails/activate", "RefUrl": "/notes/1752626"}, {"RefNumber": "1752384", "RefComponent": "BW-WHM-MTD", "RefTitle": "Performance improvements of BW metadata interface", "RefUrl": "/notes/1752384"}, {"RefNumber": "1751878", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Incorrect aggregation behavior for 'ODSO-Like' InfoSource", "RefUrl": "/notes/1751878"}, {"RefNumber": "1751768", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA: Memory overflow when query is executed", "RefUrl": "/notes/1751768"}, {"RefNumber": "1751739", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Temporary indexes are not deleted in the input help", "RefUrl": "/notes/1751739"}, {"RefNumber": "1751321", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Missing hierarchy nodes in BW queries (3)", "RefUrl": "/notes/1751321"}, {"RefNumber": "1751219", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "DB02: Missing secondary indexes", "RefUrl": "/notes/1751219"}, {"RefNumber": "1750925", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "HANA DB: Improved log for SAP HANA-optimized DataStores", "RefUrl": "/notes/1750925"}, {"RefNumber": "1750896", "RefComponent": "BW-WHM-DBA-SPO", "RefTitle": "DUMP in CL_RSLPO_VERS_CMPR_VIEW", "RefUrl": "/notes/1750896"}, {"RefNumber": "1750766", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Generated report contains same source code several times", "RefUrl": "/notes/1750766"}, {"RefNumber": "1750721", "RefComponent": "BW-WHM-DBA-SPO", "RefTitle": "SPO: Query on SAP HANA-optimized InfoCube SPO terminates", "RefUrl": "/notes/1750721"}, {"RefNumber": "1750462", "RefComponent": "HAN-DB", "RefTitle": "TREX connection error on HANA system", "RefUrl": "/notes/1750462"}, {"RefNumber": "1749978", "RefComponent": "BW-WHM-DBA-DMA", "RefTitle": "SP30:Auth.check switched off from extraction of MD export DS", "RefUrl": "/notes/1749978"}, {"RefNumber": "1749848", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "DSO: SAP HANA-optimized DSOs for quantity conversion", "RefUrl": "/notes/1749848"}, {"RefNumber": "1748929", "RefComponent": "BW-WHM-DST", "RefTitle": "P30:Performance:RSBM_ERRORLOG_DELETE: Delete data completely", "RefUrl": "/notes/1748929"}, {"RefNumber": "1748473", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Wrong data with selections on time characteristic", "RefUrl": "/notes/1748473"}, {"RefNumber": "1748342", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Unusual drilldowns w/artificial characteristic currency/unit", "RefUrl": "/notes/1748342"}, {"RefNumber": "1746755", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "RSPCM navigates to the wrong chain run at times.", "RefUrl": "/notes/1746755"}, {"RefNumber": "1745472", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Missing hierarchy nodes in BW queries (2)", "RefUrl": "/notes/1745472"}, {"RefNumber": "1742707", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "HANA-DB: Column view reactivation if InfoObjects are changed", "RefUrl": "/notes/1742707"}, {"RefNumber": "1738185", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "F4: report displayes all available master data", "RefUrl": "/notes/1738185"}, {"RefNumber": "1734555", "RefComponent": "BC-UPG-NA", "RefTitle": "SNOTE: Performance fix in Interface enhancement", "RefUrl": "/notes/1734555"}, {"RefNumber": "1733477", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "Increasing wait time for enqueueing PSA request", "RefUrl": "/notes/1733477"}, {"RefNumber": "1730806", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "Potential data loss for write-optimized DSO", "RefUrl": "/notes/1730806"}, {"RefNumber": "1720595", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "SP31:Performance optimization - Master data prefetch", "RefUrl": "/notes/1720595"}, {"RefNumber": "1720370", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Hierarchy Level is incorrect in selector of VAR screen", "RefUrl": "/notes/1720370"}, {"RefNumber": "1714492", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Technical BW problems for queries with many hierarchies", "RefUrl": "/notes/1714492"}, {"RefNumber": "1710832", "RefComponent": "HAN-DB-ENG", "RefTitle": "HANA BW: I_RESULT_INDEX_NAME with TREX_EXT_AGGREGATE", "RefUrl": "/notes/1710832"}, {"RefNumber": "1691977", "RefComponent": "HAN-STD-ADM-DBA", "RefTitle": "DBACockpit: CX_SY_STRUCT_COMP_NAME from INI FILE application", "RefUrl": "/notes/1691977"}, {"RefNumber": "1272916", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Displaying Texts for hierarchy node variable in BEx", "RefUrl": "/notes/1272916"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1783680", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Texts displayed with \"Not Assigned\"", "RefUrl": "/notes/1783680 "}, {"RefNumber": "1767880", "RefComponent": "BW-WHM", "RefTitle": "Non-active data concept for BW on SAP HANA DB", "RefUrl": "/notes/1767880 "}, {"RefNumber": "1720370", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Hierarchy Level is incorrect in selector of VAR screen", "RefUrl": "/notes/1720370 "}, {"RefNumber": "1754729", "RefComponent": "BC-DB-HDB-POR", "RefTitle": "SHBD: Consistency checking for distributed HANA systems", "RefUrl": "/notes/1754729 "}, {"RefNumber": "1710832", "RefComponent": "HAN-DB-ENG", "RefTitle": "HANA BW: I_RESULT_INDEX_NAME with TREX_EXT_AGGREGATE", "RefUrl": "/notes/1710832 "}, {"RefNumber": "1808846", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "Improvement log for PSA versioning in post-migration", "RefUrl": "/notes/1808846 "}, {"RefNumber": "1783238", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "SMIGR_CREATE_DDL: Estimated row count for fact tables", "RefUrl": "/notes/1783238 "}, {"RefNumber": "1765533", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "Corrections for BW on SAP HANA DB, 7.30 SP9, 7.31 SP6", "RefUrl": "/notes/1765533 "}, {"RefNumber": "1767014", "RefComponent": "BC-DB-HDB-POR", "RefTitle": "RSDU_TABLE_CONSISTENCY reports too many inconsistencies", "RefUrl": "/notes/1767014 "}, {"RefNumber": "1777403", "RefComponent": "BW-BEX-ET-WJR-AD", "RefTitle": "Commit is not performed after modifying table rszwmditemdocu", "RefUrl": "/notes/1777403 "}, {"RefNumber": "1691977", "RefComponent": "HAN-STD-ADM-DBA", "RefTitle": "DBACockpit: CX_SY_STRUCT_COMP_NAME from INI FILE application", "RefUrl": "/notes/1691977 "}, {"RefNumber": "1807404", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Incorrect interpretation of some properties after upgrade", "RefUrl": "/notes/1807404 "}, {"RefNumber": "1768020", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "CHECK_BIA_AVAILABLE Prog: CL_RSDRC_SEL_SPLITTER", "RefUrl": "/notes/1768020 "}, {"RefNumber": "1818430", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help: Error during plan execution;Failed to find sourc", "RefUrl": "/notes/1818430 "}, {"RefNumber": "1774561", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "730SP9: '<PERSON><PERSON><PERSON> failed' during deleting a link of rule", "RefUrl": "/notes/1774561 "}, {"RefNumber": "1768641", "RefComponent": "BC-MID-ALE", "RefTitle": "BDLS dump occurs w/ DBIF_RSQL_SQL_ERROR on HANA BW system", "RefUrl": "/notes/1768641 "}, {"RefNumber": "1809892", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Enable extraction from old versions of a DataSource", "RefUrl": "/notes/1809892 "}, {"RefNumber": "1776186", "RefComponent": "HAN-DB-ENG", "RefTitle": "HANA - Scale out: routing to right indexserver", "RefUrl": "/notes/1776186 "}, {"RefNumber": "1714492", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Technical BW problems for queries with many hierarchies", "RefUrl": "/notes/1714492 "}, {"RefNumber": "1778553", "RefComponent": "BI-RA-BICS", "RefTitle": "Leafs or nodes display as # in F4", "RefUrl": "/notes/1778553 "}, {"RefNumber": "1759172", "RefComponent": "BW-WHM-MTD", "RefTitle": "BW metadata API: Error when activating SAP HANA views", "RefUrl": "/notes/1759172 "}, {"RefNumber": "1734555", "RefComponent": "BC-UPG-NA", "RefTitle": "SNOTE: Performance fix in Interface enhancement", "RefUrl": "/notes/1734555 "}, {"RefNumber": "1783817", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Message \"View Attribute \"0REQUID_SID\" not found\" displayed", "RefUrl": "/notes/1783817 "}, {"RefNumber": "1845120", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "SAP HANA DB: Column view generation incorrect for InfoObject", "RefUrl": "/notes/1845120 "}, {"RefNumber": "1844042", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "Error when adding a new navigation attribute to an IOBJ", "RefUrl": "/notes/1844042 "}, {"RefNumber": "1730806", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "Potential data loss for write-optimized DSO", "RefUrl": "/notes/1730806 "}, {"RefNumber": "1842238", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Extraction from error stack slow on SAP HANA DB", "RefUrl": "/notes/1842238 "}, {"RefNumber": "1841927", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Error counter is 1 during serial processing", "RefUrl": "/notes/1841927 "}, {"RefNumber": "1793436", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Improving creation performance in BW CalculationScenarios", "RefUrl": "/notes/1793436 "}, {"RefNumber": "1793318", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BW query entry support for calculation scenarios", "RefUrl": "/notes/1793318 "}, {"RefNumber": "1758163", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query cannot be generated without errors", "RefUrl": "/notes/1758163 "}, {"RefNumber": "1756099", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "RSHDB: Consistency check for tables (7.30 SP9)", "RefUrl": "/notes/1756099 "}, {"RefNumber": "1720595", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "SP31:Performance optimization - Master data prefetch", "RefUrl": "/notes/1720595 "}, {"RefNumber": "1790134", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "Error message for RSMIGRHANADB due to incorrect partitioning", "RefUrl": "/notes/1790134 "}, {"RefNumber": "1796960", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW master data provider query ends with dump CHECK_KHANDLE", "RefUrl": "/notes/1796960 "}, {"RefNumber": "1780575", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "Conversion fails with error \"RSDRI_HDB 059\"", "RefUrl": "/notes/1780575 "}, {"RefNumber": "1760745", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA for BW: search result error 2618", "RefUrl": "/notes/1760745 "}, {"RefNumber": "1796184", "RefComponent": "BW-BEX-OT-DBIF-CON", "RefTitle": "Condensor: Termination CONVT_NO_NUMBER in CL_RSCDS_COMPR_INV", "RefUrl": "/notes/1796184 "}, {"RefNumber": "1742707", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "HANA-DB: Column view reactivation if InfoObjects are changed", "RefUrl": "/notes/1742707 "}, {"RefNumber": "1786390", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "BW on SAP HANA: Loading data to an InfoCube terminates", "RefUrl": "/notes/1786390 "}, {"RefNumber": "1765506", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "SP30:'RS_PSA_TABTYPE_EXISTENCE_CHECK' to check PSA tab types", "RefUrl": "/notes/1765506 "}, {"RefNumber": "1789992", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "HANADB: Incorrect RFC context when converting from DSOs", "RefUrl": "/notes/1789992 "}, {"RefNumber": "1803613", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Value help in F4 mode D f.time-dependent navigation attrib.", "RefUrl": "/notes/1803613 "}, {"RefNumber": "1801194", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Triggering DB merge for PSAs when converting system names", "RefUrl": "/notes/1801194 "}, {"RefNumber": "1809127", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "GETWA_NOT_ASSIGNED in the class CL_RSL_ME_METADATA", "RefUrl": "/notes/1809127 "}, {"RefNumber": "1803064", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA: further analysis of ABAP errors via TREX_EXT_*", "RefUrl": "/notes/1803064 "}, {"RefNumber": "1769038", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA BW: improve error message from TREX_EXT_AGGREGATE", "RefUrl": "/notes/1769038 "}, {"RefNumber": "1786388", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW on HANA: Conversion of InfoCube fails", "RefUrl": "/notes/1786388 "}, {"RefNumber": "1791902", "RefComponent": "BW-BEX-OT", "RefTitle": "MultiProv: <PERSON><PERSON>r during partition pruning for SPO", "RefUrl": "/notes/1791902 "}, {"RefNumber": "1788431", "RefComponent": "BW-BEX-ET", "RefTitle": "Slow parallel execution of the bookmarks", "RefUrl": "/notes/1788431 "}, {"RefNumber": "1783330", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "Problems with IMO DSO Activation", "RefUrl": "/notes/1783330 "}, {"RefNumber": "1790333", "RefComponent": "BW-WHM-MTD", "RefTitle": "Enhancements of remote API for BW metadata information", "RefUrl": "/notes/1790333 "}, {"RefNumber": "1797600", "RefComponent": "BW-BEX-OT", "RefTitle": "Selection/authorization check for hierarchy nodes", "RefUrl": "/notes/1797600 "}, {"RefNumber": "1800483", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "In-memory DSO change log is empty after selective deletion", "RefUrl": "/notes/1800483 "}, {"RefNumber": "1786576", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "InfoProvider restriction is not used in getMembers", "RefUrl": "/notes/1786576 "}, {"RefNumber": "1784613", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "730SP9:DSO lookup doesnt work correctly", "RefUrl": "/notes/1784613 "}, {"RefNumber": "1798043", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA BW: lock error on shared memory object", "RefUrl": "/notes/1798043 "}, {"RefNumber": "1752626", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "NO2 aggregation for InfoCubes fails/activate", "RefUrl": "/notes/1752626 "}, {"RefNumber": "1769374", "RefComponent": "BW-WHM-MTD", "RefTitle": "BW metadata API: Support for query snapshot models", "RefUrl": "/notes/1769374 "}, {"RefNumber": "1794792", "RefComponent": "BW-WHM-MTD", "RefTitle": "BW metadata API - Assert for DSOs w/ read-only char.", "RefUrl": "/notes/1794792 "}, {"RefNumber": "1793826", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "connected filters combined with *toN joins not supported", "RefUrl": "/notes/1793826 "}, {"RefNumber": "1753959", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Incorrect data or missing hierarchy nodes in query", "RefUrl": "/notes/1753959 "}, {"RefNumber": "1785210", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "BW on HANA: Logical index after converting InfoCube", "RefUrl": "/notes/1785210 "}, {"RefNumber": "1786682", "RefComponent": "BI-RA-BICS", "RefTitle": "Peformance: Queries mit vielen Strukturmembern", "RefUrl": "/notes/1786682 "}, {"RefNumber": "1781314", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "Empty PSA table has multiple partitions", "RefUrl": "/notes/1781314 "}, {"RefNumber": "1788145", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: calling variable screen twice removes the currency", "RefUrl": "/notes/1788145 "}, {"RefNumber": "1751321", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Missing hierarchy nodes in BW queries (3)", "RefUrl": "/notes/1751321 "}, {"RefNumber": "1787929", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Performance optimization: F4 help in mode D on MultiProvider", "RefUrl": "/notes/1787929 "}, {"RefNumber": "1745472", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Missing hierarchy nodes in BW queries (2)", "RefUrl": "/notes/1745472 "}, {"RefNumber": "1786559", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Hierarchy node input help does not use BWA/SAP HANA access", "RefUrl": "/notes/1786559 "}, {"RefNumber": "1784970", "RefComponent": "BW-WHM-DBA-SPO", "RefTitle": "SPO: Activation in after-import mode", "RefUrl": "/notes/1784970 "}, {"RefNumber": "1785116", "RefComponent": "BW-WHM-DBA-SPO", "RefTitle": "SPO: SAP HANA conversion for write-optimized DSO SPOs", "RefUrl": "/notes/1785116 "}, {"RefNumber": "1750462", "RefComponent": "HAN-DB", "RefTitle": "TREX connection error on HANA system", "RefUrl": "/notes/1750462 "}, {"RefNumber": "1776111", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "RSZDELETE: Selection by LASTUSED timestamp does not work", "RefUrl": "/notes/1776111 "}, {"RefNumber": "1767119", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "730SP9:Changelog of IMO DSO handled incorrect by PSA tools", "RefUrl": "/notes/1767119 "}, {"RefNumber": "1786311", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA for BW: search with '*' returns too many results", "RefUrl": "/notes/1786311 "}, {"RefNumber": "1781787", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Non-cumulatives: Historical deltas with rebuild valid-tab", "RefUrl": "/notes/1781787 "}, {"RefNumber": "1786109", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Multiple exception aggregation and first/last value", "RefUrl": "/notes/1786109 "}, {"RefNumber": "1767399", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Unit and currency conversion in BW CalcScenario definitions", "RefUrl": "/notes/1767399 "}, {"RefNumber": "1776749", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "DSO: SAP HANA conversion for write-optimized DSOs", "RefUrl": "/notes/1776749 "}, {"RefNumber": "1783421", "RefComponent": "BW-BEX-OT", "RefTitle": "Incorrect query results for MultiProvider with LPOA parts", "RefUrl": "/notes/1783421 "}, {"RefNumber": "1782540", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "Migration to SAP HANA DB: Import terminates with SQL error", "RefUrl": "/notes/1782540 "}, {"RefNumber": "1778066", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "SP30:ASSERTION_FAILED during Save/activation or TRF", "RefUrl": "/notes/1778066 "}, {"RefNumber": "1772501", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "DSO: Partitioning of write-optimized DSOs in SAP HANA", "RefUrl": "/notes/1772501 "}, {"RefNumber": "1776030", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Generation error for exception aggregation first/last value", "RefUrl": "/notes/1776030 "}, {"RefNumber": "1757340", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "DBIF_RSQL_INVALID_RSQL for MOST_RECENT master data access", "RefUrl": "/notes/1757340 "}, {"RefNumber": "1772882", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "_PSA_CHANGES_CHECK: Analysing RFC error", "RefUrl": "/notes/1772882 "}, {"RefNumber": "1777509", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "Poor OLAP cache performance on mode 5 'BLOB/Cluster Enh.'", "RefUrl": "/notes/1777509 "}, {"RefNumber": "1771922", "RefComponent": "BW-BEX-OT", "RefTitle": "Poor performance for only 'Excluding' condition on SAP HANA", "RefUrl": "/notes/1771922 "}, {"RefNumber": "1738185", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "F4: report displayes all available master data", "RefUrl": "/notes/1738185 "}, {"RefNumber": "1774577", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "P09; REQARCH: No RSREQARCHMON entry for archived requests", "RefUrl": "/notes/1774577 "}, {"RefNumber": "1766901", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "P30:ODS:DSO: Activation terminates for last package", "RefUrl": "/notes/1766901 "}, {"RefNumber": "1751739", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Temporary indexes are not deleted in the input help", "RefUrl": "/notes/1751739 "}, {"RefNumber": "1773025", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Execute and Explain terminates", "RefUrl": "/notes/1773025 "}, {"RefNumber": "1749978", "RefComponent": "BW-WHM-DBA-DMA", "RefTitle": "SP30:Auth.check switched off from extraction of MD export DS", "RefUrl": "/notes/1749978 "}, {"RefNumber": "1773398", "RefComponent": "BW-BEX-OT-DBIF-CON", "RefTitle": "SAP HANA: No compression for scale-out & non-optimized cube", "RefUrl": "/notes/1773398 "}, {"RefNumber": "1762935", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "HANADB: Using the compressed change log (delta)", "RefUrl": "/notes/1762935 "}, {"RefNumber": "1752384", "RefComponent": "BW-WHM-MTD", "RefTitle": "Performance improvements of BW metadata interface", "RefUrl": "/notes/1752384 "}, {"RefNumber": "1770457", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "730SP9:DBIF_RSQL_INVALID_RSQL in CL_RSDMD_LOOKUP_MASTER_DATA", "RefUrl": "/notes/1770457 "}, {"RefNumber": "1767755", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Long runtime when accessing SPs", "RefUrl": "/notes/1767755 "}, {"RefNumber": "1770163", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "BW HANA: InfoCube compression after conversion terminates", "RefUrl": "/notes/1770163 "}, {"RefNumber": "1765830", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "Hot/Cold - give PARTNO-information to database", "RefUrl": "/notes/1765830 "}, {"RefNumber": "1769377", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Constant mappings to zero value in BW calculation scenarios", "RefUrl": "/notes/1769377 "}, {"RefNumber": "1764247", "RefComponent": "BW-BEX-OT-BICS-PROV", "RefTitle": "RSBOLAP: Deletion of QuerySettings buffer after var. change", "RefUrl": "/notes/1764247 "}, {"RefNumber": "1769321", "RefComponent": "BW-BEX-OT-DBIF-CON", "RefTitle": "BW HANA: Request status after compressing empty requests", "RefUrl": "/notes/1769321 "}, {"RefNumber": "1767502", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "InfoCube conversion: invalidate MPRO runtime object", "RefUrl": "/notes/1767502 "}, {"RefNumber": "1766577", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "Converting non-cumulative InfoCubes: All requests compressed", "RefUrl": "/notes/1766577 "}, {"RefNumber": "1764928", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Too much data in specific situation", "RefUrl": "/notes/1764928 "}, {"RefNumber": "1767496", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Exception aggregation AV0 in HANA/BWA is incorrect", "RefUrl": "/notes/1767496 "}, {"RefNumber": "1765584", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "DSO: Inconsistency w/regard to primary index for DB object", "RefUrl": "/notes/1765584 "}, {"RefNumber": "1766993", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Disaggregation does not distribute vals for unposted vals", "RefUrl": "/notes/1766993 "}, {"RefNumber": "1766059", "RefComponent": "BW-BEX-OT", "RefTitle": "RSDDSTAT: Values in the table RSDDSTATBIAUSE too high", "RefUrl": "/notes/1766059 "}, {"RefNumber": "1765760", "RefComponent": "HAN-DB", "RefTitle": "Temporary DB tables in consistency check", "RefUrl": "/notes/1765760 "}, {"RefNumber": "1765286", "RefComponent": "BW-BEX-OT", "RefTitle": "Deadlock in table RSDDSTATBIAUSE in MultiUser operation", "RefUrl": "/notes/1765286 "}, {"RefNumber": "1765358", "RefComponent": "BW-BEX-OT", "RefTitle": "Performance improvement: Reading InfoObjects (nav. attrib.)", "RefUrl": "/notes/1765358 "}, {"RefNumber": "1750925", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "HANA DB: Improved log for SAP HANA-optimized DataStores", "RefUrl": "/notes/1750925 "}, {"RefNumber": "1760688", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Update of the LASTUSED timestamp for query components", "RefUrl": "/notes/1760688 "}, {"RefNumber": "1760763", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "730SP9: Error activating TRF having DSO and MD lookups", "RefUrl": "/notes/1760763 "}, {"RefNumber": "1761214", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P09:DTP:Cockpit displays negative runtimes; rsddstatdtp", "RefUrl": "/notes/1761214 "}, {"RefNumber": "1272916", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Displaying Texts for hierarchy node variable in BEx", "RefUrl": "/notes/1272916 "}, {"RefNumber": "1746755", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "RSPCM navigates to the wrong chain run at times.", "RefUrl": "/notes/1746755 "}, {"RefNumber": "1733477", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "Increasing wait time for enqueueing PSA request", "RefUrl": "/notes/1733477 "}, {"RefNumber": "1756630", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Subsequent correction to SAP Note 1750766", "RefUrl": "/notes/1756630 "}, {"RefNumber": "1751878", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Incorrect aggregation behavior for 'ODSO-Like' InfoSource", "RefUrl": "/notes/1751878 "}, {"RefNumber": "1754680", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Various errors for F4 on char. and char. as <PERSON>foProvider", "RefUrl": "/notes/1754680 "}, {"RefNumber": "1753692", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "SP09 BW on SAP HANA: Adjustment RSCDS_CHECK_EPART_DUPLICATES", "RefUrl": "/notes/1753692 "}, {"RefNumber": "1751219", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "DB02: Missing secondary indexes", "RefUrl": "/notes/1751219 "}, {"RefNumber": "1750766", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Generated report contains same source code several times", "RefUrl": "/notes/1750766 "}, {"RefNumber": "1751768", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA: Memory overflow when query is executed", "RefUrl": "/notes/1751768 "}, {"RefNumber": "1748342", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Unusual drilldowns w/artificial characteristic currency/unit", "RefUrl": "/notes/1748342 "}, {"RefNumber": "1750721", "RefComponent": "BW-WHM-DBA-SPO", "RefTitle": "SPO: Query on SAP HANA-optimized InfoCube SPO terminates", "RefUrl": "/notes/1750721 "}, {"RefNumber": "1750896", "RefComponent": "BW-WHM-DBA-SPO", "RefTitle": "DUMP in CL_RSLPO_VERS_CMPR_VIEW", "RefUrl": "/notes/1750896 "}, {"RefNumber": "1749848", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "DSO: SAP HANA-optimized DSOs for quantity conversion", "RefUrl": "/notes/1749848 "}, {"RefNumber": "1748929", "RefComponent": "BW-WHM-DST", "RefTitle": "P30:Performance:RSBM_ERRORLOG_DELETE: Delete data completely", "RefUrl": "/notes/1748929 "}, {"RefNumber": "1748473", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Wrong data with selections on time characteristic", "RefUrl": "/notes/1748473 "}, {"RefNumber": "984021", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "Changes to InfoObject 0LANGU", "RefUrl": "/notes/984021 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "731", "To": "731", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}