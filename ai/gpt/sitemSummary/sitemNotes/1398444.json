{"Request": {"Number": "1398444", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 320, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016890952017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=94A067C3C6B31C130D96470DEC86F293"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1398444"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "2019.03.21"}, "SAPComponentKey": {"_label": "Component", "value": "FI-GL-GL-A"}, "SAPComponentKeyText": {"_label": "Component", "value": "Posting/Clearing"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Accounting", "value": "FI-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "FI-GL-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Posting/Clearing", "value": "FI-GL-GL-A", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-GL-A*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1398444 - Buffering the document number assignment for RF_BELEG"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><br />This note contains frequently asked questions about buffering the document number assignment in financial accounting (number range object RF_BELEG).</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SNRO, SNUM, NRIV, NRIV_LOKAL, NRIVSHADOW, NUMBER_GET_NEXT, RF_GET_DOCUMENT_NUMBER, FI document number assignment, lock waits, deadlock, CJ8G, ********, RKO7CO88, DBIF_RSQL_SQL_ERROR, CX_SY_OPEN_SQL_DB, READ_NRIV, ORA-00060, SAPLSNR3, LSNR3F01, FAQ</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Questions:</p>\r\n<ol>1. When and why should you consider buffering?</ol><ol>2. Which buffering types are there and what are the advantages and disadvantages?</ol><ol>3. Should the object RF_BELEG be buffered?</ol><ol>4. Which buffering type should be used for RF_BELEG?</ol><ol>5. How do I activate the buffering?</ol><ol>6. Can the buffering be activated for certain company codes only?</ol><ol>7. How can I activate the pseudo ascending document number assignment?</ol><ol>8. Where can I find more information about Business Transaction Event ********?</ol><ol>9. When is the entry date and time set in the FI document?</ol>\r\n<p><br /><br /><strong>1. When and why should you consider buffering?</strong><br />The document number is assigned in chronologically ascending order based on the table NRIV. For this, the table NRIV is locked until the application (LUW) is terminated by either a COMMIT WORK or ROLLBACK (see also Note 639754). Another application cannot take a document number during this time.<br />The update is called by the COMMIT WORK and the document number is assigned permanently. In the case of a rollback, the document number that was just used is not assigned and is available for the next posting in NRIV again.<br />This lock guarantees a choronologically ascending assignment of the document number without gaps.  However, the lock causes a serialization in the table NRIV, which can seriously impair the system performance (see also Note 678501). There are performance problems in particular in the case of parallel batch processes, as the lock is held for a very long time here.<br />There are two solutions for this:</p>\r\n<ul>\r\n<li>No parallel processing</li>\r\n</ul>\r\n<ul>\r\n<li>Buffering of the number range object RF_BELEG</li>\r\n</ul>\r\n<p>In <strong>SAP S/4HANA</strong>, as of Support Package 1&#x00A0;of SAP S/4HANA 1610, <strong>parallel buffering with pseudo-ascending document number assignment</strong> (Italian solution) is used <strong>by default</strong>.<strong> </strong>For Releases 1511 and 1605, you can implement SAP Note <a target=\"_blank\" href=\"/notes/2376817\" title=\"2376817 - Switch-on buffering for number range object RF_BELEG (parallel buffering with buffer size 1) in S/4HANA\">2376817</a> as an advance correction. For more information about buffering in SAP S/4HANA, see <strong>SAP Note <a target=\"_blank\" href=\"/notes/2376829\">2376829</a> - FAQ: Buffering for number range object RF_BELEG in S/4HANA </strong></p>\r\n<p><strong>2. Which buffering types are there and what are the advantages and disadvantages?</strong><br />There are the following buffering types whose advantages and disadvantages are described in Note 504875:</p>\r\n<ul>\r\n<li>Main memory</li>\r\n</ul>\r\n<ul>\r\n<li>Local buffering</li>\r\n</ul>\r\n<ul>\r\n<li>Local buffering with work process ID (details in Note 179224)</li>\r\n</ul>\r\n<ul>\r\n<li>Parallel buffering (Note 599157)</li>\r\n</ul>\r\n<ul>\r\n<li>Parallel buffering with pseudo ascending document number assignment (Note 840901)</li>\r\n</ul>\r\n<p><br /><strong>3. Should the object RF_BELEG be buffered?</strong><br />Legal regulations basically require a consecutive and complete entry of all business transactions. On the one hand, there are no concrete legal requirements in many countries or accounting principles regarding the sequence of the FI document number assignment. However, there are also rules depending on the business process.<br />A general statement is not possible because of the numerous legal regulations or local accounting principles. <strong>Therefore the activation of the buffering must be reconciled with the financial accounting department.</strong><br /><br /><strong>4. Which buffering type should be used for RF_BELEG?</strong><br />The buffering in the <strong>main memory</strong> should <strong>not</strong> be used, because gap-free document number assignment cannot be guaranteed.<br /><br />Because the <strong>local buffering</strong> does not sufficiently solve the performance problems and the document number assignment is not performed chronologically, the local buffering is <strong>not recommended</strong> either.<br /><br />In the case of the <strong>local buffering with work process ID</strong> (Note 179224), the document number assignment is performed per application server and work process using the table NRIV_LOKAL. The document numbers are basically assigned without gaps, but <strong>not chronologically</strong> to the document entry or posting. In addition, gaps may occur in the document number assignment at the end of a fiscal year if not all numbers in NRIV_LOKAL were used up.<br />However, these can be documented using the report RSSNR0A1.<br />For an efficient document number assignment in parallel batch processes, the quantity of numbers selected in the buffer must not be too small. If too large a quantity of numbers is selected in the buffer, a smaller number range can be quickly exhausted if there are several application processes.<br />Because of this disadvantage, this type of buffering is to be used <strong>only</strong> <strong>if the parallel buffering is not available</strong>.<br /><br />The <strong>parallel buffering</strong> (Note 599157) solves the problems of the local buffering with work process ID for parallel batch processes.<br />The quantity of numbers in the buffer can therefore be reduced to 1. The document number assignment comes as close as possible to the behavior of the <strong>unbuffered assignment from NRIV</strong>, because the number is assigned without gaps and basically in chronologically ascending order. In the case of a rollback, the document number is available again in the buffer table NRIVSHADOW and is used for the next document on this application server and work process. In this exception, the document number assignment is no longer performed in chronologically ascending order. Gaps in the document number assignment should only be explained by update terminations, as in the case of the unbuffered document number assignment. You can use the report RFVBER00 for this.<br /><br />Logically, the document number assignment performs better if a larger quantity of numbers is used in the buffer - 10, for example. However, the document numbers are then not assigned in a chronologically ascending order. The numbers that are still in the buffer are documented as in the enhanced local buffering, but here it is performed using the report RSSNR0S1. Gaps cannot be explained exclusively by means of RFVBER00.<br /><br /><strong>Parallel buffering with pseudo-ascending document number assignment</strong> (SAP Note 840901) is a special case of parallel buffering - the so-called Italian solution. Buffer size 1 is automatically used, regardless of the quantity of numbers used in the buffer in transaction SNUM. In the case of a rollback, the assigned document number is no longer available. These gaps can also be documented with the report RSSNR0S1 using the table NRIV_RESTE or NRIV_DOCU (as of SAP Note 2022364). The document numbers are assigned in a chronologically ascending order to costs of a detectable document number gap. For more information, see the information on <a target=\"_blank\" href=\"https://help.sap.com/saphelp_nw73ehp1/helpdata/de/33/59f4db6697405dbd316ca2c689023f/content.htm?frameset=/en/4d/fc28a2fb884cba8550cf58eed1d5af/frameset.htm&amp;current_toc=/de/01/bec26bfd5d4784b7b391674b29b0ea/plain.htm&amp;node_id=12\">parallel buffering</a> in the SAP Help Portal.<br /><br />Due to the advantages of <strong>parallel buffering</strong>, it should be <strong>used</strong> for the number range object RF_BELEG.  Since the document number assignment performs well even with a <strong>buffer size of 1</strong> and comes pretty close to the unbuffered assignment, it <strong>is</strong> <strong>recommended</strong> from an accounting perspective.<br />In some countries, the documents of one day must have a higher number than the documents of the previous day. This kind of legal regulations can be adhered to only with the pseudo ascending document number assignment.<br /><br /><strong>5. How do I activate the buffering?</strong><br />The buffering is activated globally in transaction SNRO or SNUM - this applies for all clients, company codes and business transactions.<br /><br /><strong>6. Can the buffering be activated for certain company codes only?</strong><br />As dealt with in question 3, there are different rules for document number assignment. This ranges from generally valid, undefined requirements to detailed regulations at business process level.<br />The use of the buffering per company code, number range or fiscal year is only indirectly possible. First, the buffering must be set in transaction SNRO. It is a prerequisite that the number range object RF_BELEG is buffered. The number of numbers in the buffer must be <strong>greater than 0</strong> because otherwise the document number assignment is not buffered.</p>\r\n<p>You can use the process Business Transaction Event (BTE) ******** to avoid the buffering of RF_BELEG set in SNRO. For this, the export parameter E_NO_BUFFER must be set to 'X'. This can be done independently of the company code (I_COMPANY), number range interval (I_RANGE) and fiscal year (I_YEAR). Because the number range interval is assigned to the document type (transaction OBA7), it is possible to use the buffering for certain business processes and company codes only, in which the serialization in NRIV (see question 1) leads to problems.<br /><br />To prevent the number assignment being inadvertently buffered, we recommend the following procedure:<br />1. Implement the customer-specific function module for BTE ******** and activate the BTE (transaction FIBF).<br />2. Globally activate the buffering in SNRO.</p>\r\n<p>Also make sure to leave the fields for the country and the application indicator empty when you assign your function module to the BTE ********. If this is not the case, the system does not call your function module, and the buffering set in transaction SNRO is used for all number ranges, company codes, and fiscal years.<br /><br /><strong>7. How can I activate the pseudo ascending document number assignment?</strong><br />Parallel buffering must be set in transaction SNRO.<br />In BTE ********, the export parameter E_NO_BUFFER must be set to 'S' instead of 'X'. This can be done regardless of number range, company code and fiscal year. It is then possible to use the pseudo ascending document number assignment only if legally required.<br /><br /><strong>8. Where can I find more information about Business Transaction Event ********?</strong><br />You can find documentation for Business Transaction Events in the Implementation Guide (transaction SPRO) under<br />Financial Accounting -&gt; Financial Accounting Global Settings -&gt; Business Transaction Events<br />or<br />Financial Accounting (New) -&gt; Financial Accounting Global Settings (New) -&gt; Tools -&gt; Customer Enhancements -&gt; Business Transaction Events<br /><br />For more information about BTE ********, please use transaction BERP.<br /><br /><strong>9. When is the entry date and time set in the FI document?</strong><br />In FI, the time (BKPF-CPUTM = SY-UZEIT) is set during posting (more precisely at the event PROJECT) before the document number assignment and before the update is called. The entry time of the FI document must be available for other components, as these are partially used there.<br />In the case of the pseudo ascending document number assignment, a document with an earlier entry time may receive a higher document number. This is the case if other accounting components require a longer runtime after the time is set than for a document with a later entry time. However, this can also happen with an unbuffered number range.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-AR-AR-A (Posting/Clearing/Special General Ledger)"}, {"Key": "Other Components", "Value": "FI-AP-AP-J (Integration/Accounting Interface)"}, {"Key": "Other Components", "Value": "FI-AR-AR-J (Integration/Accounting Interface)"}, {"Key": "Other Components", "Value": "FI-GL-GL-J (Integration/Accounting Interface)"}, {"Key": "Other Components", "Value": "FI-AP-AP-A (Posting/Clearing/Special General Ledger)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D024958)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D024958)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "840901", "RefComponent": "BC-SRV-NUM", "RefTitle": "Parallel buffering and pseudo-ascending number assignment", "RefUrl": "/notes/840901"}, {"RefNumber": "678501", "RefComponent": "BC-SRV-NUM", "RefTitle": "System standstill, locks on NRIV", "RefUrl": "/notes/678501"}, {"RefNumber": "639754", "RefComponent": "BC-SRV-NUM", "RefTitle": "When is a number finally assigned?", "RefUrl": "/notes/639754"}, {"RefNumber": "599157", "RefComponent": "BC-SRV-NUM", "RefTitle": "Number ranges: New buffering method", "RefUrl": "/notes/599157"}, {"RefNumber": "504875", "RefComponent": "BC-SRV-NUM", "RefTitle": "Buffering of number ranges", "RefUrl": "/notes/504875"}, {"RefNumber": "2376829", "RefComponent": "FI", "RefTitle": "FAQ: Buffering for number range object RF_BELEG in S/4HANA", "RefUrl": "/notes/2376829"}, {"RefNumber": "179224", "RefComponent": "BC-SRV-NUM", "RefTitle": "Doc.no.assignment for unbuffered number ranges", "RefUrl": "/notes/179224"}, {"RefNumber": "1522367", "RefComponent": "FI-GL-GL-A", "RefTitle": "Document number gap reason and analysing method", "RefUrl": "/notes/1522367"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2658401", "RefComponent": "BC-SRV-NUM", "RefTitle": "Owner of Number Range Object", "RefUrl": "/notes/2658401 "}, {"RefNumber": "2489011", "RefComponent": "BC-DB-SYB", "RefTitle": "Locks, deadlocks, SQL error 12205 , 12207 on table NRIV  - SAP ASE for Business Suite", "RefUrl": "/notes/2489011 "}, {"RefNumber": "2469364", "RefComponent": "BC-SRV-NUM", "RefTitle": "Locks in table NRIV", "RefUrl": "/notes/2469364 "}, {"RefNumber": "2556454", "RefComponent": "BC-SRV-NUM", "RefTitle": "Error \"Mixed requests for numbers (Italian and parallel)\" in system log", "RefUrl": "/notes/2556454 "}, {"RefNumber": "2521182", "RefComponent": "CO-PC-ACT", "RefTitle": "CKMLMV037 SQL error 1205 when accessing table NRIV in CKMLCP", "RefUrl": "/notes/2521182 "}, {"RefNumber": "2208321", "RefComponent": "FI-AA-AA", "RefTitle": "FAQ for legacy data transfer in SAP_FIN 720 and subsequent releases", "RefUrl": "/notes/2208321 "}, {"RefNumber": "2184567", "RefComponent": "FI-CF", "RefTitle": "Central Finance: Frequently Asked Questions (FAQ)", "RefUrl": "/notes/2184567 "}, {"RefNumber": "2474833", "RefComponent": "XX-PROJ-FI-CA", "RefTitle": "FI-CA: Enabling number of jobs in mass activities > 999", "RefUrl": "/notes/2474833 "}, {"RefNumber": "2376829", "RefComponent": "FI", "RefTitle": "FAQ: Buffering for number range object RF_BELEG in S/4HANA", "RefUrl": "/notes/2376829 "}, {"RefNumber": "2376817", "RefComponent": "FI-GL", "RefTitle": "Switch-on buffering for number range object RF_BELEG (parallel buffering with buffer size 1) in S/4HANA", "RefUrl": "/notes/2376817 "}, {"RefNumber": "504875", "RefComponent": "BC-SRV-NUM", "RefTitle": "Buffering of number ranges", "RefUrl": "/notes/504875 "}, {"RefNumber": "639754", "RefComponent": "BC-SRV-NUM", "RefTitle": "When is a number finally assigned?", "RefUrl": "/notes/639754 "}, {"RefNumber": "1522367", "RefComponent": "FI-GL-GL-A", "RefTitle": "Document number gap reason and analysing method", "RefUrl": "/notes/1522367 "}, {"RefNumber": "678501", "RefComponent": "BC-SRV-NUM", "RefTitle": "System standstill, locks on NRIV", "RefUrl": "/notes/678501 "}, {"RefNumber": "599157", "RefComponent": "BC-SRV-NUM", "RefTitle": "Number ranges: New buffering method", "RefUrl": "/notes/599157 "}, {"RefNumber": "840901", "RefComponent": "BC-SRV-NUM", "RefTitle": "Parallel buffering and pseudo-ascending number assignment", "RefUrl": "/notes/840901 "}, {"RefNumber": "179224", "RefComponent": "BC-SRV-NUM", "RefTitle": "Doc.no.assignment for unbuffered number ranges", "RefUrl": "/notes/179224 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}