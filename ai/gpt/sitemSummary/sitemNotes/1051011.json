{"Request": {"Number": "1051011", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 638, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000006800042017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001051011?language=E&token=CC507E4EBC86E1375665DE09AF7A7D4A"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001051011", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001051011/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1051011"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Currentness", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "31.05.2007"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-AT-IS-H"}, "SAPComponentKeyText": {"_label": "Component", "value": "Hospital"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Austria", "value": "XX-CSC-AT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-AT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-specific component", "value": "XX-CSC-AT-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-AT-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hospital", "value": "XX-CSC-AT-IS-H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-AT-IS-H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1051011 - IS-H AT: EDI Message Dispatch with Comment"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1051011&TargetLanguage=EN&Component=XX-CSC-AT-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/1051011/D\" target=\"_blank\">/notes/1051011/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This SAP Note is relevant only for the country version Austria (AT).<br />Delivery of a new feature to send messages manually with a comment option.</p><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>Manual message dispatch, EDI, K12.</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>Prerequisite (only for Austria): Note 1050830.</p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><p>Before you implement the correction instructions from this SAP Note, you must perform the following manual tasks:</p> <OL>1. Unpack the attached files:</OL> <OL><OL>a) For IS-H Version 6.00 AOP 01 - 09, the files HW1051011_600_WB.ZIP (workbench request) and HW1051011_600_CUST.ZIP (Customizing request)</OL></OL> <OL><OL>b) For IS-H Version 4.72 AOP 01 - 21, the file HW1051011_472.ZIP</OL></OL> <OL><OL>c) For both IS-H versions, the files HW1051011_ANLEITUNG.ZIP and K12_DOK_ISHA-7817_SAP.zip</OL></OL> <p>              Note that you cannot download the attached files using OSS, but only from SAP Service Marketplace (see also SAP Notes 480180 and 13719 for information about importing attachments). <OL>2. Import the unpacked requests into your system.</OL> <p>              Note that when you activate the workbench request, errors may occur with regard to the objects ISH_321_KOMV10_FILL and ISH_321_MSG_LAYOUT. Activate anyway because the problem is solved when you implement the correction instructions. <OL>3. Now make the adjustments in accordance with the attached instructions.</OL> <OL>4. Implement the correction instructions of this SAP Note.</OL> <OL>5. Deactivate and activate existing implementations for the Business Add-Ins (BAdIs) ISH_AT_ELDA_P321 and ISH_AT_ELDA_ELDAL to announce the new methods for comment messages there.</OL> <p><br />The attached document K12_DOK_ISHA-7817_SAP.pdf describes the new function for manually sending messages with a comment option.</p></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "IS-H (Hospital)"}, {"Key": "Owner                                                                                    ", "Value": "<PERSON><PERSON> (C5008711)"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON> (C5025082)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001051011/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "HW1051011_600_Cust.zip", "FileSize": "435", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000176302007&iv_version=0003&iv_guid=2D443CE44EAC6B43A0678AD5A897583C"}, {"FileName": "HW1051011_Anleitung.zip", "FileSize": "418", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000176302007&iv_version=0003&iv_guid=14B123343A7E884BB46F0FB47CFE9799"}, {"FileName": "HW1051011_600_WB.zip", "FileSize": "203", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000176302007&iv_version=0003&iv_guid=683DE8B7CA838848B3BAE5AB4E99464F"}, {"FileName": "K12_DOK_ISHA-7817_SAP.zip", "FileSize": "102", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000176302007&iv_version=0003&iv_guid=A89DC3700EBE32418A2CEF5B26DB14ED"}, {"FileName": "HW1051011_472.zip", "FileSize": "204", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000176302007&iv_version=0003&iv_guid=B7A173CABA119947BADFEEE818C34EFA"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1060504", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: EDI Comment Dispatch on Worklist", "RefUrl": "/notes/1060504"}, {"RefNumber": "1050830", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: EDI Message Dispatch with Comment (Delta)", "RefUrl": "/notes/1050830"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "1060504", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: EDI Comment Dispatch on Worklist", "RefUrl": "/notes/1060504 "}, {"RefNumber": "1050830", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: EDI Message Dispatch with Comment (Delta)", "RefUrl": "/notes/1050830 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "IS-H", "From": "472", "To": "472", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "602", "To": "602", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": [{"SoftwareComponentVersion": "IS-H 472", "SupportPackage": "SAPKIPHF22", "URL": "/supportpackage/SAPKIPHF22"}, {"SoftwareComponentVersion": "IS-H 600", "SupportPackage": "SAPK-60010INISH", "URL": "/supportpackage/SAPK-60010INISH"}]}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 2, "URL": "/corrins/0001051011/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 73, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "753527 ", "URL": "/notes/753527 ", "Title": "IS-H AT: ELDA - IV Requests for Several IRs", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "768823 ", "URL": "/notes/768823 ", "Title": "IS-H AT: ELDA - Extension Display", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "776215 ", "URL": "/notes/776215 ", "Title": "IS-H AT: ELDA - Institution Indicator (Field KANKZ)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "786907 ", "URL": "/notes/786907 ", "Title": "IS-H AT: ELDA: Import Insurance Verification - Differences", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "789039 ", "URL": "/notes/789039 ", "Title": "IS-H AT: ELDA - Update Termination Patient Merge", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "789300 ", "URL": "/notes/789300 ", "Title": "IS-H AT: ELDA - Discharge Notification Field ENTS via TNCMAPP", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "807225 ", "URL": "/notes/807225 ", "Title": "IS-H AT: ELDA - End of EHIC Data Record", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "811846 ", "URL": "/notes/811846 ", "Title": "IS-H AT: ELDA/SCORE Changes to Settings", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "829574 ", "URL": "/notes/829574 ", "Title": "IS-H AT: ELDA - Checks for EEA Patients UK/GB", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "839456 ", "URL": "/notes/839456 ", "Title": "IS-H AT: ELDA - Outpatient Service Report", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "842007 ", "URL": "/notes/842007 ", "Title": "IS-H AT: ELDA Outpatient Admission notification or Construction Progress Report", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "843037 ", "URL": "/notes/843037 ", "Title": "IS-H AT: Outpatient Service Data (New Subscreens)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "857550 ", "URL": "/notes/857550 ", "Title": "IS-H AT: ELDA - Use of BAdI Methods, BETREU/URS", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "858293 ", "URL": "/notes/858293 ", "Title": "IS-H AT: Data Exchange - Care Certificate, Cause of Treatment", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "859390 ", "URL": "/notes/859390 ", "Title": "IS-H AT: Data Exchange - Convert Title with TNCMAPP", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "859391 ", "URL": "/notes/859391 ", "Title": "IS-H AT: ELDA - DB Lock RNC30100 (NRIV)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "859789 ", "URL": "/notes/859789 ", "Title": "IS-H AT: Data Exchange - Control User-Defined Diagnoses", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "860189 ", "URL": "/notes/860189 ", "Title": "IS-H AT: Data Exchange - Ignore Care Certificate 0", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "861548 ", "URL": "/notes/861548 ", "Title": "IS-H AT: Data Exchange - Error Corrections (VVP,LDA)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "861881 ", "URL": "/notes/861881 ", "Title": "IS-H AT: Data Exchange - Describe First Names with Umlauts", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "862001 ", "URL": "/notes/862001 ", "Title": "IS-H AT: Data Exchange - Numbering Preprocessing Record (DTNR)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "862327 ", "URL": "/notes/862327 ", "Title": "IS-H AT: Data Exchange - Event Report Visit Date To", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "863402 ", "URL": "/notes/863402 ", "Title": "IS-H AT: ELDA - Remaining, Incorrect STO_AUFN", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "864116 ", "URL": "/notes/864116 ", "Title": "IS-H AT: ELDA - Missing Cancellations for Case Type Change", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "865892 ", "URL": "/notes/865892 ", "Title": "IS-H AT: Data Exchange - Event Report DB Lock NRIV", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "866111 ", "URL": "/notes/866111 ", "Title": "IS-H AT: ELDA - Outpatient IV Groups", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "867866 ", "URL": "/notes/867866 ", "Title": "IS-H AT: Data Exchange - S/T/U Diagnoses for ENTL", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "871814 ", "URL": "/notes/871814 ", "Title": "IS-H AT: Data Exchange - ELDAL Line per Segment", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "872143 ", "URL": "/notes/872143 ", "Title": "IS-H AT: ELDA - Cancel Outpatient Admission Notification After Confirmation", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "872907 ", "URL": "/notes/872907 ", "Title": "IS-H AT: ELDAL - R<PERSON><PERSON><PERSON>LDALU0 Submitted RNWATELDAU1", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "876276 ", "URL": "/notes/876276 ", "Title": "IS-H AT: Data Exchange - ELDAL File Visit Date", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "879950 ", "URL": "/notes/879950 ", "Title": "IS-H AT: Data Exchange - STATE Only Conditional Required Field", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "880952 ", "URL": "/notes/880952 ", "Title": "IS-H AT: Data Exchange - Outpatient Manual Events", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "884505 ", "URL": "/notes/884505 ", "Title": "IS-H AT: ELDAL - Cancel Outpatient Performance Data", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "887107 ", "URL": "/notes/887107 ", "Title": "IS-H AT: Data Exchange - Outpatient Visit in the future", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "888950 ", "URL": "/notes/888950 ", "Title": "IS-H AT: Data Exchange - Loss of HI Addr. at Import VZE", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "889299 ", "URL": "/notes/889299 ", "Title": "IS-H AT: Data Exchange - ELDAL Receipt of Error Messages", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "903941 ", "URL": "/notes/903941 ", "Title": "IS-H AT: Data Exchange - P321 EHIC Also for Discharge", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "904414 ", "URL": "/notes/904414 ", "Title": "IS-H AT: Data Exchange - P321 AUF/ENT-STO Without Diagnoses", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "909690 ", "URL": "/notes/909690 ", "Title": "IS-H AT: Determine Care Certificate Using TNWAT_BETR", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "909988 ", "URL": "/notes/909988 ", "Title": "IS-H AT: ELDA - Insurance Verification Overlapping IV Requests", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "914170 ", "URL": "/notes/914170 ", "Title": "IS-H AT: ELDA - Required Field Checks", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "917463 ", "URL": "/notes/917463 ", "Title": "IS-H AT: ELDA - Lock Number Range (NRIV) by RNC301I0", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "918052 ", "URL": "/notes/918052 ", "Title": "IS-H AT: Data Exchange P321 - Extension Display", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "918382 ", "URL": "/notes/918382 ", "Title": "IS-H AT: Data Exchange P321 - Disp. Adm. Despite Final Billing", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "918521 ", "URL": "/notes/918521 ", "Title": "IS-H AT: ELDA - Lock Number Range (NRIV) - NV2001 Locks", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "921499 ", "URL": "/notes/921499 ", "Title": "IS-H AT: ELDAL - Insurance Provider Responsible for Service (D.5)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "921944 ", "URL": "/notes/921944 ", "Title": "IS-H AT: ELDA - Memory space problems during file import", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "926716 ", "URL": "/notes/926716 ", "Title": "IS-H AT: ELDA - NRIV Lock, ELDAL - First Visit Date", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "927795 ", "URL": "/notes/927795 ", "Title": "IS-H AT: Data Exchange - Event Processing via Reports", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "928673 ", "URL": "/notes/928673 ", "Title": "IS-H AT: Data Exchange - Change of Health/Illness Newborn", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "936603 ", "URL": "/notes/936603 ", "Title": "IS-H AT: Data Exchange - Various Corrections", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "940487 ", "URL": "/notes/940487 ", "Title": "IS-H AT: ELDA - Memory Problems Importing Large Files", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "943845 ", "URL": "/notes/943845 ", "Title": "IS-H AT: ELDA - Current Adjustments", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "945407 ", "URL": "/notes/945407 ", "Title": "IS-H AT: ELDAL - Manual Event Triggering Only", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "954447 ", "URL": "/notes/954447 ", "Title": "IS-H AT: ELDA - Current Adjustments (P321, ELDAL)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "961620 ", "URL": "/notes/961620 ", "Title": "IS-H AT: ELDA - Outpatient Dunning event \\&quot;E\\&quot; instead of \\&quot;U\\&quot;", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "969592 ", "URL": "/notes/969592 ", "Title": "IS-H AT: P321 Dead Locks and Sent Admission Ads", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "985366 ", "URL": "/notes/985366 ", "Title": "IS-H AT: Suppress RNWATKOSMA Error \\&quot;Missing Windows\\&quot;", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "993056 ", "URL": "/notes/993056 ", "Title": "ISH-AT: RNWATKOSMA IV Dunning in Batch", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "997652 ", "URL": "/notes/997652 ", "Title": "IS-H AT: Standard Implementation EDIVKA 5.0", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "998220 ", "URL": "/notes/998220 ", "Title": "IS-H AT: ELDA - RNWATELDAU1 Sel. Ind./Visit Period Combination", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1012209 ", "URL": "/notes/1012209 ", "Title": "IS-H AT: ELDA - Corrections December 2006", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1018776 ", "URL": "/notes/1018776 ", "Title": "IS-H AT: ELDA New BAdI Methods and Notification to KFA", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1021479 ", "URL": "/notes/1021479 ", "Title": "IS-H AT: ELDA - Corrections January 2007", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1026248 ", "URL": "/notes/1026248 ", "Title": "IS-H AT: Performance RNWATKUELIST RNWAT_EDI_LIST RNWATKOSMA", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1042873 ", "URL": "/notes/1042873 ", "Title": "IS-H AT: ELDA STO Messages for Incorrect Rejection", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1045763 ", "URL": "/notes/1045763 ", "Title": "IS-H AT: Insurance Verification - Extension", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1046198 ", "URL": "/notes/1046198 ", "Title": "IS-H AT: ELDA Outpatient Admission Notification to Companion Admission", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1046505 ", "URL": "/notes/1046505 ", "Title": "IS-H AT: ELDA EHIC Data Record for Extension/Reminder", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "876276 ", "URL": "/notes/876276 ", "Title": "IS-H AT: Data Exchange - ELDAL File Visit Date", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "879950 ", "URL": "/notes/879950 ", "Title": "IS-H AT: Data Exchange - STATE Only Conditional Required Field", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "880952 ", "URL": "/notes/880952 ", "Title": "IS-H AT: Data Exchange - Outpatient Manual Events", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "884505 ", "URL": "/notes/884505 ", "Title": "IS-H AT: ELDAL - Cancel Outpatient Performance Data", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "885516 ", "URL": "/notes/885516 ", "Title": "IS-H AT: Data Exchange - VZE Without Start Date", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "887107 ", "URL": "/notes/887107 ", "Title": "IS-H AT: Data Exchange - Outpatient Visit in the future", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "888950 ", "URL": "/notes/888950 ", "Title": "IS-H AT: Data Exchange - Loss of HI Addr. at Import VZE", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "889299 ", "URL": "/notes/889299 ", "Title": "IS-H AT: Data Exchange - ELDAL Receipt of Error Messages", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "903941 ", "URL": "/notes/903941 ", "Title": "IS-H AT: Data Exchange - P321 EHIC Also for Discharge", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "904414 ", "URL": "/notes/904414 ", "Title": "IS-H AT: Data Exchange - P321 AUF/ENT-STO Without Diagnoses", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "909988 ", "URL": "/notes/909988 ", "Title": "IS-H AT: ELDA - Insurance Verification Overlapping IV Requests", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "914170 ", "URL": "/notes/914170 ", "Title": "IS-H AT: ELDA - Required Field Checks", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "917463 ", "URL": "/notes/917463 ", "Title": "IS-H AT: ELDA - Lock Number Range (NRIV) by RNC301I0", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "918052 ", "URL": "/notes/918052 ", "Title": "IS-H AT: Data Exchange P321 - Extension Display", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "918382 ", "URL": "/notes/918382 ", "Title": "IS-H AT: Data Exchange P321 - Disp. Adm. Despite Final Billing", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "918521 ", "URL": "/notes/918521 ", "Title": "IS-H AT: ELDA - Lock Number Range (NRIV) - NV2001 Locks", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "921499 ", "URL": "/notes/921499 ", "Title": "IS-H AT: ELDAL - Insurance Provider Responsible for Service (D.5)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "921944 ", "URL": "/notes/921944 ", "Title": "IS-H AT: ELDA - Memory space problems during file import", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "926716 ", "URL": "/notes/926716 ", "Title": "IS-H AT: ELDA - NRIV Lock, ELDAL - First Visit Date", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "927795 ", "URL": "/notes/927795 ", "Title": "IS-H AT: Data Exchange - Event Processing via Reports", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "928673 ", "URL": "/notes/928673 ", "Title": "IS-H AT: Data Exchange - Change of Health/Illness Newborn", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "936603 ", "URL": "/notes/936603 ", "Title": "IS-H AT: Data Exchange - Various Corrections", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "938453 ", "URL": "/notes/938453 ", "Title": "IS-H AT: Correction Collection for Version 6.00 to Patch 02", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "940487 ", "URL": "/notes/940487 ", "Title": "IS-H AT: ELDA - Memory Problems Importing Large Files", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "943845 ", "URL": "/notes/943845 ", "Title": "IS-H AT: ELDA - Current Adjustments", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "945407 ", "URL": "/notes/945407 ", "Title": "IS-H AT: ELDAL - Manual Event Triggering Only", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "954447 ", "URL": "/notes/954447 ", "Title": "IS-H AT: ELDA - Current Adjustments (P321, ELDAL)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "961620 ", "URL": "/notes/961620 ", "Title": "IS-H AT: ELDA - Outpatient Dunning event \\&quot;E\\&quot; instead of \\&quot;U\\&quot;", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "969592 ", "URL": "/notes/969592 ", "Title": "IS-H AT: P321 Dead Locks and Sent Admission Ads", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "976151 ", "URL": "/notes/976151 ", "Title": "IS-H AT: ELDA - Current Adjustments", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "985366 ", "URL": "/notes/985366 ", "Title": "IS-H AT: Suppress RNWATKOSMA Error \\&quot;Missing Windows\\&quot;", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "993056 ", "URL": "/notes/993056 ", "Title": "ISH-AT: RNWATKOSMA IV Dunning in Batch", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "998220 ", "URL": "/notes/998220 ", "Title": "IS-H AT: ELDA - RNWATELDAU1 Sel. Ind./Visit Period Combination", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1012209 ", "URL": "/notes/1012209 ", "Title": "IS-H AT: ELDA - Corrections December 2006", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1018776 ", "URL": "/notes/1018776 ", "Title": "IS-H AT: ELDA New BAdI Methods and Notification to KFA", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1021479 ", "URL": "/notes/1021479 ", "Title": "IS-H AT: ELDA - Corrections January 2007", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1026248 ", "URL": "/notes/1026248 ", "Title": "IS-H AT: Performance RNWATKUELIST RNWAT_EDI_LIST RNWATKOSMA", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1042873 ", "URL": "/notes/1042873 ", "Title": "IS-H AT: ELDA STO Messages for Incorrect Rejection", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1045763 ", "URL": "/notes/1045763 ", "Title": "IS-H AT: Insurance Verification - Extension", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1046198 ", "URL": "/notes/1046198 ", "Title": "IS-H AT: ELDA Outpatient Admission Notification to Companion Admission", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1046505 ", "URL": "/notes/1046505 ", "Title": "IS-H AT: ELDA EHIC Data Record for Extension/Reminder", "Component": "XX-CSC-AT-IS-H"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1051011&TargetLanguage=EN&Component=XX-CSC-AT-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/1051011/D\" target=\"_blank\">/notes/1051011/D</a>."}}}}