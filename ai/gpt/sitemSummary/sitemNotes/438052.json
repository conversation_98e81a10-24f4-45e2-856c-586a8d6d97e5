{"Request": {"Number": "438052", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1425, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000002053232017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=73672C4AC36629DB5274278C13A157D0"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "438052"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "30.09.2001"}, "SAPComponentKey": {"_label": "Component", "value": "FS-BP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Partner"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Services", "value": "FS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Partner", "value": "FS-BP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS-BP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "438052 - SAP-BP: Release history incomplete after BP conversion"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You have imported release Banking 4.62/CFM 1.0 or Banking 4.63/CFM 2.0 and carry out business partner conversion to the SAP business partner. If you then use the business partner release, it is not possible for you after the conversion to display any historic release data for the release object BPAR of the Treasury business partnerfrom Treasury Business Partner maintenance.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Business partner, release, SAP-BP, TR-BP, conversion, BPC2, BPC3, BPAR_F_Partner_Release_Popup, Popup_confirmation_data, history, release object, central business partner, ZGP, BPAR</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>For technical reasons, it is not possible for you to display the release data for both release objects (ZGP and BPAR). Since SAP-BP is the leading BP (after the conversion), the programming was changed so that the system only displays the release data for release object ZGP. As a result, it is no longer possible for you to diplay the historic release data for TR-BP.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>First implement the attached program corrections. As a result, the system automatically displays the release history for release object BPAR, if no information is found for the object ZGP. If no data for the business partner currently processed exists for any of the two release objects, the system then generates a new information message. To implement it, access Transaction SE91 (Message Maintenance:Initial Screen) and create a new message (number 050) in message class FTBP. As a message short text, enter the following:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;No release data is available for the release objects 'ZGP' and 'BPAR'<br /><br />Enter the following long text for this message:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;After checking the release objects ZGP and BPAR (for the SAP and Treasury business partners), the system was unable to determine the release history.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If release data is displayed for the object ZGP, additional release data may also exist for the object BPAR. You can view this data using Transaction BPRI.<br /><br />Report BPAR_RELEASE_INFORMATION displays the release data for the object BPAR for a partner. To implement this report, proceed as follows:</p> <OL>1. Call Transaction SE38 (ABAP Editor:Intial Screen) and enter BPAR_RELEASE_INFORMATION in the \"Program\" field.</OL> <OL>2. Choose 'Create' and assign following attributes:</OL> <OL><OL>a) Title: Display release information for TR-BP</OL></OL> <OL><OL>b) Type: Executable program</OL></OL> <OL><OL>c) Status: SAP standard production program</OL></OL> <OL><OL>d) Development class: FBPAR<br /></OL></OL> <OL>3. Implement the source code from the attached correction instructions.</OL> <OL>4. Choose the menu \"Goto &gt; Text elements &gt; Selection texts\" to access the maintenance of the ABAP text elements and set the \"Dictionary\" flagfor field P_partnr.</OL> <OL>5. Save and activate the report.</OL> <OL>6. Call Transaction SE80 (Object navigator) and call the report you just created.</OL> <OL>7. Via \"Workbench &gt; Edit object\", branch to the object selection.</OL> <OL>8. On the \"More\" tab page, select the \"Transaction\" button and enter Transaction BPRI in the field on the right.</OL> <OL>9. Choose \"Create\".</OL> <OL>10. On the following dialog box, enter the short text \"Release history display of TR-BP\" and select the \"Program and selection screen (report transaction)\" radio button.</OL> <OL>11. On the following screen, enter program name BPAR_RELEASE_INFORMATION and save the entries (development class FBPAR).</OL> <p><br />By calling Transaction BPRI, you can now display the release history for any TR business partner.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "TR-TM (Treasury Management)"}, {"Key": "Other Components", "Value": "AP-MD-BP (Business Partner)"}, {"Key": "Other Components", "Value": "TR-TM-TM (Basic Data)"}, {"Key": "Other Components", "Value": "FIN-FSCM-TRM-BF-BP (Please use component FS-BP!)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D033094)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "398888", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Composite note and FAQs about bus partner conversion", "RefUrl": "/notes/398888"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "398888", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Composite note and FAQs about bus partner conversion", "RefUrl": "/notes/398888 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "BANK/CFM", "From": "462_10", "To": "463_20", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "BANK/CFM 462_10", "SupportPackage": "SAPKIPBI08", "URL": "/supportpackage/SAPKIPBI08"}, {"SoftwareComponentVersion": "BANK/CFM 463_20", "SupportPackage": "SAPKIPBJ03", "URL": "/supportpackage/SAPKIPBJ03"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "BANK/CFM", "NumberOfCorrin": 4, "URL": "/corrins/**********/59"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 2, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "BANK/CFM", "ValidFrom": "462_10", "ValidTo": "462_10", "Number": "322429 ", "URL": "/notes/322429 ", "Title": "PAR: Release history missing for TR business partner", "Component": "TR-TM-TM"}, {"SoftwareComponent": "BANK/CFM", "ValidFrom": "462_10", "ValidTo": "462_10", "Number": "376534 ", "URL": "/notes/376534 ", "Title": "PAR: TR business partner in release", "Component": "TR-TM-TM"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}