{"Request": {"Number": "1731416", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 340, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017457742017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001731416?language=E&token=A6F5FE3F8F978F07EACD9577F9B73689"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001731416", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001731416/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1731416"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.07.2012"}, "SAPComponentKey": {"_label": "Component", "value": "IS-U-CS-BT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Transactions"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Utilities", "value": "IS-U", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-U*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Customer Service", "value": "IS-U-CS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-U-CS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Transactions", "value": "IS-U-CS-BT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-U-CS-BT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1731416 - IS-U: Sending disconn. status for manual disconn. or reconn."}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><b>Improvement Request</b><br /> <p>After you import the relevant Support Package, you can use enterprise services to send a notification about the status change of the devices to the external system (for example, meter data unification and synchronization (MDUS)) when you manually disconnect or reconnect advanced metering infrastructure (AMI) devices.<br /></p> <b>Request Reason</b><br /> <p>Up to now, the disconnection status was transferred from the external system to IS-U only for AMI devices that were disconnected and reconnected remotely. As of now, the external system can also receive information about a manual disconnection or reconnection, or about a reversal of this activity. The enterprise services that are used for this are the following:<br /><br />Disconnection/reconnection: SmartMeterUtilitiesConnectionStatusChangeRequestERPBulkNotification_OUT<br />Reversing the disconnection/reconnection: SmartMeterUtilitiesConnectionStatusChangeRequestERPCancellationBulkNotification_OUT<br /><br />The enterprise services for the disconnection and the reconnection differ only by their relevant category type code (\"1\" or \"2\").<br /><br />The services specified above are sent for the following activities:<br /><br />Disconnection transactions (EC85/EC86)<br />BOR methods for disconnections in the background<br />Transactions with disconnected devices (EG30, EG31, EG34, EG35)<br /><br />Comment:&#x00A0;&#x00A0;When you perform a billing-related removal (transaction EG35), the system communicates a reconnection (category code = 2). When you perform an installation (transaction EG31 or EG34), the system always sets the disconnection time to 00:00:00. If the implementation date or the transaction date is later than the disconnection date, the system now uses and transfers the disconnection information instead of the implementation date or the transaction date.<br /><br />In addition, you can use the report REAMI_MANUAL_DISC_SEND to transfer the disconnection status to the external system.<br /><br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Disconnection, reconnection, meter data unification and synchronization, MDUS</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is an improvement for customers.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><b>Improvement</b><br /> <p>Services are sent to external systems when you change the disconnection status.</p> <b>Benefit</b><br /> <p>Information about the disconnection status is now also sent for AMI devices that cannot be disconnected remotely.</p> <b>Delivery</b><br /> <p>You can obtain this improvement via the relevant Support Package.<br />Activate the business function ISU_AMI_4A.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Improvement Note", "Value": "Yes"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D022649)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D019477)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001731416/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001731416/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001731416/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001731416/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001731416/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001731416/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001731416/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001731416/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001731416/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1754249", "RefComponent": "IS-U", "RefTitle": "Enterprise Services for AMI Integration with MDUS systems", "RefUrl": "/notes/1754249"}, {"RefNumber": "1751206", "RefComponent": "IS-OIL", "RefTitle": "Incl. BF to Combined BFS Oil & Gas and Mining with Utilities", "RefUrl": "/notes/1751206"}, {"RefNumber": "1746718", "RefComponent": "IS-U-DM", "RefTitle": "IS-U: Basis for various new functions", "RefUrl": "/notes/1746718"}, {"RefNumber": "1730659", "RefComponent": "IS-U-CS-BT", "RefTitle": "Interface note: Basis for various new functions", "RefUrl": "/notes/1730659"}, {"RefNumber": "1730084", "RefComponent": "IS-U-CS-BT", "RefTitle": "Interface note: Disconnection or reconnection in AMI monitor", "RefUrl": "/notes/1730084"}, {"RefNumber": "1730074", "RefComponent": "IS-U-CS-BT", "RefTitle": "Interface note: Basis for various new functions", "RefUrl": "/notes/1730074"}, {"RefNumber": "1729883", "RefComponent": "IS-U-CS-BT", "RefTitle": "IS-U interface note: Disconn. or reconn. in EG30, EG31, etc.", "RefUrl": "/notes/1729883"}, {"RefNumber": "1729861", "RefComponent": "IS-U-CS-BT", "RefTitle": "IS-U interface note: Man. disconn. or reconn. of BOR methods", "RefUrl": "/notes/1729861"}, {"RefNumber": "1729860", "RefComponent": "IS-U-CS-BT", "RefTitle": "IS-U: Interface note for sending manual disconn. or reconn.", "RefUrl": "/notes/1729860"}, {"RefNumber": "1729780", "RefComponent": "IS-U-CS-BT", "RefTitle": "IS-U: Interface note for sending manual disconnection class", "RefUrl": "/notes/1729780"}, {"RefNumber": "1728429", "RefComponent": "IS-U", "RefTitle": "Interface note: Basis for various new functions", "RefUrl": "/notes/1728429"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1754249", "RefComponent": "IS-U", "RefTitle": "Enterprise Services for AMI Integration with MDUS systems", "RefUrl": "/notes/1754249 "}, {"RefNumber": "1751206", "RefComponent": "IS-OIL", "RefTitle": "Incl. BF to Combined BFS Oil & Gas and Mining with Utilities", "RefUrl": "/notes/1751206 "}, {"RefNumber": "1728429", "RefComponent": "IS-U", "RefTitle": "Interface note: Basis for various new functions", "RefUrl": "/notes/1728429 "}, {"RefNumber": "1746718", "RefComponent": "IS-U-DM", "RefTitle": "IS-U: Basis for various new functions", "RefUrl": "/notes/1746718 "}, {"RefNumber": "1730659", "RefComponent": "IS-U-CS-BT", "RefTitle": "Interface note: Basis for various new functions", "RefUrl": "/notes/1730659 "}, {"RefNumber": "1730074", "RefComponent": "IS-U-CS-BT", "RefTitle": "Interface note: Basis for various new functions", "RefUrl": "/notes/1730074 "}, {"RefNumber": "1730084", "RefComponent": "IS-U-CS-BT", "RefTitle": "Interface note: Disconnection or reconnection in AMI monitor", "RefUrl": "/notes/1730084 "}, {"RefNumber": "1729883", "RefComponent": "IS-U-CS-BT", "RefTitle": "IS-U interface note: Disconn. or reconn. in EG30, EG31, etc.", "RefUrl": "/notes/1729883 "}, {"RefNumber": "1729861", "RefComponent": "IS-U-CS-BT", "RefTitle": "IS-U interface note: Man. disconn. or reconn. of BOR methods", "RefUrl": "/notes/1729861 "}, {"RefNumber": "1729860", "RefComponent": "IS-U-CS-BT", "RefTitle": "IS-U: Interface note for sending manual disconn. or reconn.", "RefUrl": "/notes/1729860 "}, {"RefNumber": "1729780", "RefComponent": "IS-U-CS-BT", "RefTitle": "IS-U: Interface note for sending manual disconnection class", "RefUrl": "/notes/1729780 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-UT", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "IS-UT", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "IS-UT", "From": "616", "To": "616", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "IS-UT 605", "SupportPackage": "SAPK-60509INISUT", "URL": "/supportpackage/SAPK-60509INISUT"}, {"SoftwareComponentVersion": "IS-UT 606", "SupportPackage": "SAPK-60605INISUT", "URL": "/supportpackage/SAPK-60605INISUT"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}