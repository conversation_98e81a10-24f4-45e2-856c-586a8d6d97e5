{"Request": {"Number": "1411834", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 440, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016933202017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001411834?language=E&token=BC97AFB10DAD5CCA064D0EE16EE4185D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001411834", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001411834/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1411834"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.05.2010"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CCM-MON-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Database Monitors for Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Computer Center Management System (CCMS)", "value": "BC-CCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "CCMS Monitoring & Alerting", "value": "BC-CCM-MON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-MON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Monitors for Oracle", "value": "BC-CCM-MON-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-MON-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1411834 - DBA Cockpit: GVD_* tables, RSORAHCL, history"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note is valid for the DBA Cockpit on Oracle.<br /><br />- You notice that there is a large workload in your system that is caused by the reports RSORAHCL and RSORAHST.<br />The table GVD_OBJECT_DEPEN, GVD_SEGSTAT, or other tables that have the name GVD_* are large and require a lot of disk space. The following tables are affected:<br /><br />&#x00A0;&#x00A0;GVD_BGPROCESS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_BUFF_POOL_ST&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_CURR_BLKSRV<br />&#x00A0;&#x00A0;GVD_DATABASE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_DATAFILE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; GVD_DATAGUARD_ST<br />&#x00A0;&#x00A0;GVD_DB_CACHE_ADV&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_ENQUEUE_STAT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; GVD_FILESTAT<br />&#x00A0;&#x00A0;GVD_INSTANCE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_LATCH&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_LATCH_MISSES<br />&#x00A0;&#x00A0;GVD_LATCH_PARENT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_LATCHCHILDS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_LATCHHOLDER<br />&#x00A0;&#x00A0;GVD_LATCHNAME&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_LIBRARYCACHE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_LOCK<br />&#x00A0;&#x00A0;GVD_LOCK_ACTIVTY&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_LOCKED_OBJEC&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; GVD_LOGFILE<br />&#x00A0;&#x00A0;GVD_MANGD_STANBY&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_OBJECT_DEPEN&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; GVD_PARAMETER<br />&#x00A0;&#x00A0;GVD_PARAMETER2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_PGA_TARGET_A&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; GVD_PGA_TARGET_H<br />&#x00A0;&#x00A0;GVD_PGASTAT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_PROCESS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_PX_SESSION<br />&#x00A0;&#x00A0;GVD_ROWCACHE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_SEGMENT_STAT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; GVD_SEGSTAT<br />&#x00A0;&#x00A0;GVD_SERVERLIST&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_SESS_IO&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_SESSION<br />&#x00A0;&#x00A0;GVD_SESSION_EVT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_SESSION_WAIT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_SESSTAT<br />&#x00A0;&#x00A0;GVD_SGA&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_SGACURRRESIZ&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_SGADYNCOMP<br />&#x00A0;&#x00A0;GVD_SGADYNFREE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_SGARESIZEOPS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; GVD_SGASTAT<br />&#x00A0;&#x00A0;GVD_SHAR_P_ADV&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_SPPARAMETER&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_SQL<br />&#x00A0;&#x00A0;GVD_SQL_WA_ACTIV&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_SQL_WA_HISTO&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; GVD_SQL_WORKAREA<br />&#x00A0;&#x00A0;GVD_SQLAREA&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_SQLTEXT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_SYSSTAT<br />&#x00A0;&#x00A0;GVD_SYSTEM_EVENT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_TEMPFILE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; GVD_UNDOSTAT<br />&#x00A0;&#x00A0;GVD_WAITSTAT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GVD_WPTOTALINFO&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ORA_SNAPSHOT<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RSORAHCL RSORAHST GVD_OBJECT_DEPEN GVD_SEGSTAT GVD_LATCH_CHILDS ORA_TABLESPACES</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Recommendation</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>As of Oracle Release 10, the Oracle database can save histories that provide additional analysis options. The Oracle database writes this history data to the active workload repository (AWR). To access this information, you can use Structured Query Language (SQL) and read the DBA_HIST_* views.<br /><br />For releases lower than Oracle Release 10, there was no option to read this information within the database. Therefore, we have developed an infrastructure to read histories for Oracle Release 9 in some cases. For this, various GV$ views are copied to tables that have the name GVD_ * at regular intervals (once each hour). To do this, the two reports RSORAHCL and RSORAHST are used (only RSORAHCL is scheduled via the table TCOLL).<br /><br />This method has a few disadvantages compared to the native method of Oracle:<br />- The scope of the saved information in AWR is larger and therefore more suitable for analyzing problems than the method using the GVD_* tables.<br />- The workload that is generated by the reports RSORAHCL and RSORAHST is quite large.<br />- Depending on the system, the GVD_* tables may become very large and require a lot of disk space.<br /><br />If you do not require the information from the GVD_* tables, we recommend that you deactivate this history update type:<br />1. Deschedule the report RSORAHCL (see the table TCOLL).<br />2. Delete the contents of the GVD_* tables.<br /><br />When you deactivate the history update, SQL accesses of the following type are no longer applicable:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; DELETE FROM \"ORA_TABLESPACES\" WHERE \"SNAPSHOT_ID\"=:A0<br />These accesses have caused performance problems on various occasions.<br /><br /><br />Note:<br />As of Basis 7.00 Support Package 21, the data collection using the reports RSORAHCL and RSORAHST is no longer used (also see Notes 1080813 and 1369716).<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D021680)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D044039)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001411834/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001411834/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001411834/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001411834/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001411834/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001411834/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001411834/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001411834/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001411834/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478"}, {"RefNumber": "1879327", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "DBA Cockpit: System event history is deactivated", "RefUrl": "/notes/1879327"}, {"RefNumber": "1790169", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "DBA Cockpit: Deletion of GVD_* tables", "RefUrl": "/notes/1790169"}, {"RefNumber": "1369716", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "DBA Cockpit: History update is deactivated", "RefUrl": "/notes/1369716"}, {"RefNumber": "1080813", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "DBA Cockpit: Changing history update (Oracle database)", "RefUrl": "/notes/1080813"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1879327", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "DBA Cockpit: System event history is deactivated", "RefUrl": "/notes/1879327 "}, {"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478 "}, {"RefNumber": "1790169", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "DBA Cockpit: Deletion of GVD_* tables", "RefUrl": "/notes/1790169 "}, {"RefNumber": "1369716", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "DBA Cockpit: History update is deactivated", "RefUrl": "/notes/1369716 "}, {"RefNumber": "1080813", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "DBA Cockpit: Changing history update (Oracle database)", "RefUrl": "/notes/1080813 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "620", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "730", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}