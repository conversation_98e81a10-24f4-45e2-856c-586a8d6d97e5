{"Request": {"Number": "1747180", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 615, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017479782017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001747180?language=E&token=E1F877682D3B7C40E9DACC71249C45FA"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001747180", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1747180"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.11.2012"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CST-IC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Internet Communication Manager"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Client/Server Technology", "value": "BC-CST", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CST*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Internet Communication Manager", "value": "BC-CST-IC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CST-IC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1747180 - SMTP via TLS and SMTP authentication"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You want to use SMTP via TLS or SMTP authentication.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The implementation of SMTP via TLS and SMTP authentication is contained as of 7.31 Support Package 6 and 7.21 kernel.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Use at least 7.31 Support Package 6 and a 7.21 kernel.<br /><br />SAP Note 1702785 contains help for troubleshooting.<br /><br />Since the configuration of SMTP via TLS and SMTP authentication on the server is not yet described in the online documentation for 7.31 Support Package 6, the description is contained here:<br /></p> <b>Configuring SMTP authentication and SMTP via TLS/SSL for incoming e-mails (system type AS-ABAP)</b><br /> <UL><LI>This affects the parameter: icm/server_port_&lt;xx&gt; (AS ABAP)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You have already set the log to be used (PROT) to SMTP and specified the port to be used (PORT) in the parameter icm/server_port_&lt;xx&gt; and you now want to configure the SMTP authentication or the SMTP settings via TLS/SSL for incoming e-mails.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In this article, the following SMTP-specific configuration options are described:</p> <UL><UL><LI>TLS: Configure SMTP via TLS/SSL</LI></UL></UL> <UL><UL><LI>AUTHMECHANISM: Authentication for incoming e-mails</LI></UL></UL> <UL><UL><LI>AUTHUSERS: Define authorized AS ABAP users</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The following contains two examples for the possible configuration:</p> <UL><UL><LI>Example A: icm/server_port_1 = PROT=SMTP, PORT=25000, TLS=2</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This opens the port 25000 for SMTP requests and prompts the client for the TLS encryption. If this is not possible, the connection is terminated.</p> <UL><UL><LI>Example B: icm/server_port_1 = PROT=SMTP, PORT=25000, TLS=2, AUTHMECHANISMS=PLAIN; EXTERNAL, AUTHUSERS=ABAPUser1; ABAPUser2</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This opens the port 25000 for SMTP requests and prompts the client for the TLS encryption. If this is not possible, the connection is terminated. The client must identify himself with a user/password or client certificate. ABAPUser1 and ABAPUser2 are entered as authorized users.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The character string adheres to the following syntax:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PROT=&lt;SMTP&gt;, PORT=&lt;port or service name&gt;[, TIMEOUT=&lt;timeout&gt;, PROCTIMEOUT=&lt;proctimeout&gt;, EXTBIND=1, HOST=&lt;host name&gt;, SSLCONFIG=ssl_config_&lt;xx&gt;, VCLIENT=&lt;SSL client verification&gt;, ACLFILE=&lt;ACL file&gt;, TLS=&lt;SMTP TLS usage&gt;, AUTHMECHANISMS=&lt;SMTP authentication&gt;, AUTHUSERS=&lt;SMTP user authentication&gt;]</p> <UL><LI>Configuration</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;TLS: Configure SMTP via TLS/SSL</p> <UL><UL><LI>0: A TLS prompt does not take place.</LI></UL></UL> <UL><UL><LI>1: The server prompts the client to encrypt using TLS. If this is not possible, the connection via SMTP is accepted without TLS.</LI></UL></UL> <UL><UL><LI>2: The client has to use TLS for encryption; otherwise, the connection is terminated.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;AUTHMECHANISMS: Authentication for incoming e-mails</p> <UL><UL><LI>PLAIN: The authentication takes place with a user/password query</LI></UL></UL> <UL><UL><LI>EXTERNAL: The authentication takes place with a client certificate</LI></UL></UL> <UL><UL><LI>NONE: No prompt for authentication takes place</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(The internet standard RFC 4422 describes the SMTP authentication in detail.)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;AUTHUSERS: Define authorized users<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;With the AUTHUSERS option, the users allowed for the SMTP authentication are specified. For the SMTP authentication, standard AS ABAP users are used in the client 000, user type SYSTEM.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In the AUTHUSERS, you have to specify at least one user and a maximum of 10 users. The users are separated by a semicolon in the character string; # refer to example B.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note the following:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you want to use the authentication via user/password (PLAIN), you first have to generate a relevant ABAP user.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you want to use the authentication with a client certificate (EXTERNAL), a valid certificate must first be assigned to the relevant ABAP user.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-SRV-COM (Communication Services: Mail, Fax, SMS, Telephony)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D021676)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I823279)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001747180/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001747180/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001747180/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001747180/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001747180/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001747180/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001747180/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001747180/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001747180/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1728283", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 721: General Information", "RefUrl": "/notes/1728283"}, {"RefNumber": "1724704", "RefComponent": "BC-SRV-COM", "RefTitle": "SCOT: Settings for TLS and SMTP AUTH", "RefUrl": "/notes/1724704"}, {"RefNumber": "1702785", "RefComponent": "BC-CST-IC", "RefTitle": "Error diagnosis for SMTP using TLS and SMTP authentication", "RefUrl": "/notes/1702785"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3064215", "RefComponent": "BC-SRV-COM", "RefTitle": "Error message XS816 'SMTP communication error' for SMTP messages in SOST", "RefUrl": "/notes/3064215 "}, {"RefNumber": "2756932", "RefComponent": "BC-SRV-COM", "RefTitle": "XS612 - SSL peer certificate untrusted using TLS authentication", "RefUrl": "/notes/2756932 "}, {"RefNumber": "2718594", "RefComponent": "BC-SRV-COM", "RefTitle": "\"MTA Open Mail Relaying Allowed\" message for SMTP port in SAP system", "RefUrl": "/notes/2718594 "}, {"RefNumber": "2700110", "RefComponent": "BC-SRV-COM", "RefTitle": "Error code XS751 with STARTTLS occurs in transaction SOST during mail sending", "RefUrl": "/notes/2700110 "}, {"RefNumber": "2690360", "RefComponent": "BC-SRV-COM", "RefTitle": "XS812 - 550 5.7.60 SMTP; Client does not have permissions to send as this sender", "RefUrl": "/notes/2690360 "}, {"RefNumber": "2439601", "RefComponent": "BC-SRV-COM", "RefTitle": "Password length limited to 20 characters - SCOT", "RefUrl": "/notes/2439601 "}, {"RefNumber": "2000465", "RefComponent": "BC-CST-IC", "RefTitle": "Support of AUTH LOGIN as SMTP client", "RefUrl": "/notes/2000465 "}, {"RefNumber": "1728283", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 721: General Information", "RefUrl": "/notes/1728283 "}, {"RefNumber": "1702785", "RefComponent": "BC-CST-IC", "RefTitle": "Error diagnosis for SMTP using TLS and SMTP authentication", "RefUrl": "/notes/1702785 "}, {"RefNumber": "1724704", "RefComponent": "BC-SRV-COM", "RefTitle": "SCOT: Settings for TLS and SMTP AUTH", "RefUrl": "/notes/1724704 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "KRNL32NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "8.04", "To": "8.04", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "740", "To": "740", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "8.04", "To": "8.04", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 731", "SupportPackage": "SAPKB73106", "URL": "/supportpackage/SAPKB73106"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT", "SupportPackage": "SP033", "SupportPackagePatch": "000033", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT UNICODE", "SupportPackage": "SP033", "SupportPackagePatch": "000033", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP033", "SupportPackagePatch": "000033", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP033", "SupportPackagePatch": "000033", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP033", "SupportPackagePatch": "000033", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT", "SupportPackage": "SP033", "SupportPackagePatch": "000033", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021283&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP033", "SupportPackagePatch": "000033", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT UC", "SupportPackage": "SP033", "SupportPackagePatch": "000033", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021284&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 8.04 64-BIT UNICODE", "SupportPackage": "SP044", "SupportPackagePatch": "000044", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200018078&V=MAINT"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2086511", "RefTitle": "Secure P4 in client role fails upon SSL initialization", "RefUrl": "/notes/0002086511"}]}}}}}