{"Request": {"Number": "1687173", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 294, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017393842017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001687173?language=E&token=B39FD167D747183D78B47AE1DC65FAD9"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001687173", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001687173/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1687173"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.11.2012"}, "SAPComponentKey": {"_label": "Component", "value": "BC-OP-AS4"}, "SAPComponentKeyText": {"_label": "Component", "value": "IBM AS/400"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Operating System Platforms", "value": "BC-OP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "IBM AS/400", "value": "BC-OP-AS4", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP-AS4*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1687173 - IBM i: Collection of notes about the 7.20 kernel"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You want to obtain general information about the 7.20 kernel</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>AS/400, OS400, system i, i5/OS, iSeries, DB4, DB2/400, Kernel, Downward Compatible Kernel, AKK, DCK, rolling kernel switch, SAPHOSTAGENT, SAP HOST AGENT, EXT, EXT Kernel, Kernel Maintenance, saphostexec, sapstartsrv, sapcpe, installation, SAPinst, SAPILED, SAPILEDX, SIDADM login, Download, Software Center, binary patch SAPEXE, SAPEXEDB, Support Package Stacks sapinit, R3SYS, QSTRUPPGM, ADD<PERSON><PERSON>LE, STARTSAP, R3<PERSON><PERSON>GM, configure sapstartsrv as boot service, CONVUSRCPT, APYR3KRN, APYSAP, LODR3KRN, LODSAPKRN, APYR3FIX, APYSIDKRN, FIXR3OWNS, FIXSAPOWN, DSPSAPVERS, LIBPATH, PASE_LIBPATH, kernel library, SAP kernel, SAP_TOOLS, startrfc, rfcexec, ALE, Application Link Enabling, IDoc</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Information</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>This note lists other notes on the 7.20 kernel, which may be of general interest as seen from the IBM i porting team.<br /><br /><B>General Considerations</B></p> <UL><LI>1629598: End of maintenance for pre-720 kernels</LI></UL> <UL><LI>1553300: 720 or 720_EXT kernel ?</LI></UL> <UL><LI>1553301: Profile based optimization of 720_EXT kernel programs</LI></UL> <p><br /><B>Installation</B></p> <UL><LI>1031096: SAPHOSTAGENT as prerequisite for the 7.20 kernel</LI></UL> <UL><LI>1499408: Installation prerequisites</LI></UL> <UL><LI>&#x00A0;&#x00A0;19466: Download from SAP Service Marketplace</LI></UL> <UL><LI>1634894: Digitally signed SAPCAR archives</LI></UL> <p><br /><B>General Services</B></p> <UL><LI>1637588: Description of program SAPILED</LI></UL> <UL><LI> 893215: SAPstart service on IBM i</LI></UL> <p><br /><B>System Setup</B></p> <UL><LI>1581170: Security concept</LI></UL> <UL><LI>1423667: Dealing with system locales</LI></UL> <UL><LI>1632754: Changing to an instance specific program directory</LI></UL> <UL><LI>1149318: User concept in a 7.20 system</LI></UL> <UL><LI>1097751: Recommendation for kernel libraries</LI></UL> <UL><LI>1078134: Distribution of PASE and ILE parts in a kernel</LI></UL> <UL><LI>1636252: Manually applying a 7.20 kernel to a pre-720 system</LI></UL> <UL><LI> 953653: Successive kernel updates for system instances</LI></UL> <p><br /><B>System Maintenance</B></p> <UL><LI>1683418: Differences in kernel maintenance to pre-720 kernels</LI></UL> <UL><LI>1632755: Description of general maintenance command APYSIDKRN</LI></UL> <UL><LI>1589608: Saving the program directory with APYSIDKRN</LI></UL> <UL><LI>1428151: Dealing with locks on *MENU objects when patching</LI></UL> <UL><LI>1315075: Determining the patch level of a program</LI></UL> <UL><LI>1432807: Applying a saved program directory with APYSIDKRN</LI></UL> <UL><LI>1097637: Copying a kernel</LI></UL> <UL><LI>1177123: Rebuilding a destroyed ILE library for APYSIDKRN</LI></UL> <UL><LI>1656173: Applying a diagnosis patch</LI></UL> <p><br /><B>System Modifications</B></p> <UL><LI>1581595: rfcexec missing in 7.20 kernel</LI></UL> <UL><LI>1122239: Adding additional ILE libraries to the system environment</LI></UL> <UL><LI>1149088: Adding additional PASE programs to the system environment</LI></UL> <UL><LI>1660479: Files from QNTC or NFS must be opened in LEGACY MODE / CODEPAGE</LI></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D042520)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001687173/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001687173/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001687173/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001687173/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001687173/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001687173/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001687173/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001687173/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001687173/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "953653", "RefComponent": "BC-CST", "RefTitle": "Rolling Kernel Switch", "RefUrl": "/notes/953653"}, {"RefNumber": "893215", "RefComponent": "BC-OP-AS4", "RefTitle": "SAP Start Service on iSeries", "RefUrl": "/notes/893215"}, {"RefNumber": "19466", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Downloading SAP kernel patches", "RefUrl": "/notes/19466"}, {"RefNumber": "1771252", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Problems with locales in 7.21", "RefUrl": "/notes/1771252"}, {"RefNumber": "1683418", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: System maint. diffs between 7.20 and 4.6D to 7.11", "RefUrl": "/notes/1683418"}, {"RefNumber": "1660479", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: open dataset in legacy mode for NFS and QNTC", "RefUrl": "/notes/1660479"}, {"RefNumber": "1656173", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Applying a diagnosis patch in an ILE library", "RefUrl": "/notes/1656173"}, {"RefNumber": "1637588", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Description of the program SAPILED", "RefUrl": "/notes/1637588"}, {"RefNumber": "1636252", "RefComponent": "BC-CST", "RefTitle": "Installing a 7.20 kernel in SAP Web AS 7.00/7.01/7.10/7.11", "RefUrl": "/notes/1636252"}, {"RefNumber": "1634894", "RefComponent": "BC-INS-TLS", "RefTitle": "SAPCAR: Signed Archive", "RefUrl": "/notes/1634894"}, {"RefNumber": "1632755", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Description of command APYSIDKRN", "RefUrl": "/notes/1632755"}, {"RefNumber": "1632754", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Changeover to instance-specific directory", "RefUrl": "/notes/1632754"}, {"RefNumber": "1629598", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 720 will replace older kernel versions", "RefUrl": "/notes/1629598"}, {"RefNumber": "1589608", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Saving the programs after an action with APYSIDKRN", "RefUrl": "/notes/1589608"}, {"RefNumber": "1581595", "RefComponent": "BC-MID-RFC-SDK", "RefTitle": "rfcexec or startrfc are missing after upgrade", "RefUrl": "/notes/1581595"}, {"RefNumber": "1581170", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Security concept on IBM i", "RefUrl": "/notes/1581170"}, {"RefNumber": "1553301", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1553301"}, {"RefNumber": "1553300", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1553300"}, {"RefNumber": "1499408", "RefComponent": "BC-INS-AS4", "RefTitle": "IBM i: Installation Fails Due to Incomplete Kernel Update", "RefUrl": "/notes/1499408"}, {"RefNumber": "1432807", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Applying a saved kernel using APYSIDKRN", "RefUrl": "/notes/1432807"}, {"RefNumber": "1428151", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Dealing w/ locked *MENU objects when applying patches", "RefUrl": "/notes/1428151"}, {"RefNumber": "1423667", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Problems with locales in 7.20", "RefUrl": "/notes/1423667"}, {"RefNumber": "1315075", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Determining the patch level of an ILE program", "RefUrl": "/notes/1315075"}, {"RefNumber": "1177123", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Reconstructing an ILE kernel library from SAR archive", "RefUrl": "/notes/1177123"}, {"RefNumber": "1149318", "RefComponent": "BC-OP-AS4", "RefTitle": "User concept conversion using the tool CONVUSRCPT", "RefUrl": "/notes/1149318"}, {"RefNumber": "1149088", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: Adding a directory to a work process", "RefUrl": "/notes/1149088"}, {"RefNumber": "1122239", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: Adding another *LIB to a work process", "RefUrl": "/notes/1122239"}, {"RefNumber": "1097751", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Information and recommendations for kernel libraries", "RefUrl": "/notes/1097751"}, {"RefNumber": "1097637", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Copying an SAP kernel (kernel version 7.10/7.11/7.20)", "RefUrl": "/notes/1097637"}, {"RefNumber": "1078134", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Distribution of ILE and PASE system components", "RefUrl": "/notes/1078134"}, {"RefNumber": "1031096", "RefComponent": "BC-CCM-HAG", "RefTitle": "Installing Package SAPHOSTAGENT", "RefUrl": "/notes/1031096"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1636252", "RefComponent": "BC-CST", "RefTitle": "Installing a 7.20 kernel in SAP Web AS 7.00/7.01/7.10/7.11", "RefUrl": "/notes/1636252 "}, {"RefNumber": "1031096", "RefComponent": "BC-CCM-HAG", "RefTitle": "Installing Package SAPHOSTAGENT", "RefUrl": "/notes/1031096 "}, {"RefNumber": "953653", "RefComponent": "BC-CST", "RefTitle": "Rolling Kernel Switch", "RefUrl": "/notes/953653 "}, {"RefNumber": "1632755", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Description of command APYSIDKRN", "RefUrl": "/notes/1632755 "}, {"RefNumber": "1771252", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Problems with locales in 7.21", "RefUrl": "/notes/1771252 "}, {"RefNumber": "1423667", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Problems with locales in 7.20", "RefUrl": "/notes/1423667 "}, {"RefNumber": "1097637", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Copying an SAP kernel (kernel version 7.10/7.11/7.20)", "RefUrl": "/notes/1097637 "}, {"RefNumber": "1315075", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Determining the patch level of an ILE program", "RefUrl": "/notes/1315075 "}, {"RefNumber": "1149088", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: Adding a directory to a work process", "RefUrl": "/notes/1149088 "}, {"RefNumber": "1589608", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Saving the programs after an action with APYSIDKRN", "RefUrl": "/notes/1589608 "}, {"RefNumber": "1177123", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Reconstructing an ILE kernel library from SAR archive", "RefUrl": "/notes/1177123 "}, {"RefNumber": "1078134", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Distribution of ILE and PASE system components", "RefUrl": "/notes/1078134 "}, {"RefNumber": "1632754", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Changeover to instance-specific directory", "RefUrl": "/notes/1632754 "}, {"RefNumber": "1428151", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Dealing w/ locked *MENU objects when applying patches", "RefUrl": "/notes/1428151 "}, {"RefNumber": "1432807", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Applying a saved kernel using APYSIDKRN", "RefUrl": "/notes/1432807 "}, {"RefNumber": "1683418", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: System maint. diffs between 7.20 and 4.6D to 7.11", "RefUrl": "/notes/1683418 "}, {"RefNumber": "1097751", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Information and recommendations for kernel libraries", "RefUrl": "/notes/1097751 "}, {"RefNumber": "1122239", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: Adding another *LIB to a work process", "RefUrl": "/notes/1122239 "}, {"RefNumber": "1637588", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Description of the program SAPILED", "RefUrl": "/notes/1637588 "}, {"RefNumber": "1149318", "RefComponent": "BC-OP-AS4", "RefTitle": "User concept conversion using the tool CONVUSRCPT", "RefUrl": "/notes/1149318 "}, {"RefNumber": "1660479", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: open dataset in legacy mode for NFS and QNTC", "RefUrl": "/notes/1660479 "}, {"RefNumber": "1629598", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 720 will replace older kernel versions", "RefUrl": "/notes/1629598 "}, {"RefNumber": "1581595", "RefComponent": "BC-MID-RFC-SDK", "RefTitle": "rfcexec or startrfc are missing after upgrade", "RefUrl": "/notes/1581595 "}, {"RefNumber": "1634894", "RefComponent": "BC-INS-TLS", "RefTitle": "SAPCAR: Signed Archive", "RefUrl": "/notes/1634894 "}, {"RefNumber": "1656173", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Applying a diagnosis patch in an ILE library", "RefUrl": "/notes/1656173 "}, {"RefNumber": "1499408", "RefComponent": "BC-INS-AS4", "RefTitle": "IBM i: Installation Fails Due to Incomplete Kernel Update", "RefUrl": "/notes/1499408 "}, {"RefNumber": "1581170", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Security concept on IBM i", "RefUrl": "/notes/1581170 "}, {"RefNumber": "893215", "RefComponent": "BC-OP-AS4", "RefTitle": "SAP Start Service on iSeries", "RefUrl": "/notes/893215 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}