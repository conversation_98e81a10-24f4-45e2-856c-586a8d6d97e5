{"Request": {"Number": "1851809", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 424, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000010946982017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001851809?language=E&token=E6AD584DC08E8CE239DF44A8C936BE0D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001851809", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001851809/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1851809"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.03.2019"}, "SAPComponentKey": {"_label": "Component", "value": "BW-WHM-DST"}, "SAPComponentKeyText": {"_label": "Component", "value": "Data Staging"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Warehouse Management", "value": "BW-WHM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Staging", "value": "BW-WHM-DST", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DST*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1851809 - P31:Manage: DTP: Performance: Clearing TESTDATRNRPART tables"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Clearing the tables TESTDATRNRPART0 to TESTDATRNRPARTZ may take a long time because the system executes single deletes.<br />They are now executed in groups of 5 requests so that the traffic through the network has a smaller impact.<br />In addition, as of 7.30, the system executes function module RSDU_TABLE_TRUNCATE at the end of the clearing process even though the table is already empty.<br />This removes the segments that were formerly filled from the database so that the delete statement works faster. The delete statement is required to detect possible errors in the enqueue server and to ensure that the table is used exclusively.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>P31:Manage:DTP:Performance: Clearing TESTDATRNRPART tables</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p></p> <UL><LI>SAP NetWeaver BW 7.00<br /><br />Import Support Package 31 for SAP NetWeaver BW 7.00 (SAPKW70031) into your BW system. The Support Package is available when <B>SAP Note 1782745</B> \"SAPBWNews NW 7.00 BW ABAP SP31\", which describes this Support Package in more detail, is released for customers.</LI></UL> <UL><LI>SAP NetWeaver BW 7.01 (SAP NW BW 7.0 Enhancement Package 1)<br /><br />Import Support Package 14 for SAP NetWeaver BW 7.01 (SAPKW70114) into your BW system. The Support Package is available when <B>SAP Note 1794836</B> \"SAPBWNews NW 7.01 BW ABAP SP14\", which describes this Support Package in more detail, is released for customers.</LI></UL> <UL><LI>SAP NetWeaver BW 7.02 (SAP NW BW 7.0 Enhancement Package 2)<br /><br />Import Support Package 14 for SAP NetWeaver BW 7.02 (SAPKW70214) into your BW system. The Support Package is available when <B>SAP Note 1800952</B> \"SAPBWNews NW 7.02 BW ABAP SP14\", which describes this Support Package in more detail, is released for customers.</LI></UL> <UL><LI>SAP NetWeaver BW 7.11<br /><br />Import Support Package 12 for SAP NetWeaver BW 7.11 (SAPKW71112) into your BW system. The Support Package is available when <B>SAP Note 1797080</B> \"SAPBWNews NW 7.11 BW ABAP SP12\", which describes this Support Package in more detail, is released for customers.</LI></UL> <UL><LI>SAP NetWeaver BW 7.30<br /><br />Import Support Package 10 for SAP NetWeaver BW 7.30 (SAPKW73010) into your BW system. The Support Package is available when <B>SAP Note 1810084</B> \"SAPBWNews NW 7.30 BW ABAP SP10\", which describes this Support Package in more detail, is released for customers.</LI></UL> <UL><LI>SAP NetWeaver BW 7.31 (SAP NW BW 7.3 Enhancement Package 1)<br /><br />Import Support Package 09 for SAP NetWeaver BW 7.31 (SAPKW73109) into your BW system. The Support Package is available when <B>SAP Note </B><B><B>1847231</B></B><B> </B>with the short text \"SAPBWNews NW BW 7.31/7.03 ABAP SP9\", which describes this Support Package in more detail, is released for customers.</LI></UL> <UL><LI>SAP NetWeaver BW 7.40<br /><br />Import Support Package 03 for SAP NetWeaver BW 7.40 (SAPKW74003) into your BW system. The Support Package is available when <B>SAP Note</B><B><B> 1818593</B></B><B> </B>\"SAPBWNews NW BW 7.4 ABAP SP03\", which describes this Support Package in more detail, is released for customers.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br />In urgent cases, you can implement the correction instructions as an advance correction.<br /><br /><B>You must first read SAP Note 1668882, which provides information about transaction SNOTE.</B><br /><br />To provide information in advance, the SAP Notes mentioned above may already be available before the Support Package is released. In this case, the short text of the SAP Note still contains the words \"Preliminary version\".<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023663)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D023663)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001851809/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001851809/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001851809/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001851809/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001851809/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001851809/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001851809/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001851809/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001851809/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1847231", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.31 ABAP SP 09", "RefUrl": "/notes/1847231"}, {"RefNumber": "1818593", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.40 ABAP SP 03", "RefUrl": "/notes/1818593"}, {"RefNumber": "1810084", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.30 ABAP SP 10", "RefUrl": "/notes/1810084"}, {"RefNumber": "1800952", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.02 ABAP SP 14", "RefUrl": "/notes/1800952"}, {"RefNumber": "1797080", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews NW BW 7.11 ABAP SP12", "RefUrl": "/notes/1797080"}, {"RefNumber": "1794836", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.01 ABAP SP 14", "RefUrl": "/notes/1794836"}, {"RefNumber": "1782745", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.00 ABAP SP 31", "RefUrl": "/notes/1782745"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1949164", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: slowdown due to SNAP_GET_UTIL on DPF", "RefUrl": "/notes/1949164 "}, {"RefNumber": "1800952", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.02 ABAP SP 14", "RefUrl": "/notes/1800952 "}, {"RefNumber": "1847231", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.31 ABAP SP 09", "RefUrl": "/notes/1847231 "}, {"RefNumber": "1810084", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.30 ABAP SP 10", "RefUrl": "/notes/1810084 "}, {"RefNumber": "1794836", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.01 ABAP SP 14", "RefUrl": "/notes/1794836 "}, {"RefNumber": "1782745", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.00 ABAP SP 31", "RefUrl": "/notes/1782745 "}, {"RefNumber": "1818593", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.40 ABAP SP 03", "RefUrl": "/notes/1818593 "}, {"RefNumber": "1797080", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews NW BW 7.11 ABAP SP12", "RefUrl": "/notes/1797080 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "711", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "740", "To": "740", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "701", "To": "701", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "711", "To": "711", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW 700", "SupportPackage": "SAPKW70031", "URL": "/supportpackage/SAPKW70031"}, {"SoftwareComponentVersion": "SAP_BW 701", "SupportPackage": "SAPKW70114", "URL": "/supportpackage/SAPKW70114"}, {"SoftwareComponentVersion": "SAP_BW 702", "SupportPackage": "SAPKW70214", "URL": "/supportpackage/SAPKW70214"}, {"SoftwareComponentVersion": "SAP_BW 711", "SupportPackage": "SAPKW71112", "URL": "/supportpackage/SAPKW71112"}, {"SoftwareComponentVersion": "SAP_BW 730", "SupportPackage": "SAPKW73010", "URL": "/supportpackage/SAPKW73010"}, {"SoftwareComponentVersion": "SAP_BW 731", "SupportPackage": "SAPKW73109", "URL": "/supportpackage/SAPKW73109"}, {"SoftwareComponentVersion": "SAP_BW 740", "SupportPackage": "SAPKW74003", "URL": "/supportpackage/SAPKW74003"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BW", "NumberOfCorrin": 7, "URL": "/corrins/0001851809/30"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 7, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 3, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "1335591 ", "URL": "/notes/1335591 ", "Title": "P22: Slow performance deleting TESTDATRNRPART_ tables", "Component": "BW-WHM-DST"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "1550122 ", "URL": "/notes/1550122 ", "Title": "P31: Manage: Dump in include LRSSMU56", "Component": "BW-WHM-DST"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "701", "ValidTo": "701", "Number": "1335591 ", "URL": "/notes/1335591 ", "Title": "P22: Slow performance deleting TESTDATRNRPART_ tables", "Component": "BW-WHM-DST"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "701", "ValidTo": "701", "Number": "1550122 ", "URL": "/notes/1550122 ", "Title": "P31: Manage: Dump in include LRSSMU56", "Component": "BW-WHM-DST"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "702", "ValidTo": "702", "Number": "1550122 ", "URL": "/notes/1550122 ", "Title": "P31: Manage: Dump in include LRSSMU56", "Component": "BW-WHM-DST"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "711", "ValidTo": "711", "Number": "1335591 ", "URL": "/notes/1335591 ", "Title": "P22: Slow performance deleting TESTDATRNRPART_ tables", "Component": "BW-WHM-DST"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "711", "ValidTo": "711", "Number": "1550122 ", "URL": "/notes/1550122 ", "Title": "P31: Manage: Dump in include LRSSMU56", "Component": "BW-WHM-DST"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1550122 ", "URL": "/notes/1550122 ", "Title": "P31: Manage: Dump in include LRSSMU56", "Component": "BW-WHM-DST"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1717897 ", "URL": "/notes/1717897 ", "Title": "P08:CheckMan:Various quality problems solved without effect", "Component": "BW-WHM-DST"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1550122 ", "URL": "/notes/1550122 ", "Title": "P31: Manage: Dump in include LRSSMU56", "Component": "BW-WHM-DST"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1717897 ", "URL": "/notes/1717897 ", "Title": "P08:CheckMan:Various quality problems solved without effect", "Component": "BW-WHM-DST"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "1550122 ", "URL": "/notes/1550122 ", "Title": "P31: Manage: Dump in include LRSSMU56", "Component": "BW-WHM-DST"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}