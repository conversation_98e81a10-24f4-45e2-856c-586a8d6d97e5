{"Request": {"Number": "104558", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 554, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000409172017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000104558?language=E&token=70FE671DA3DB79173FEEE2D2356ACF9B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000104558", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000104558/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "104558"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "29.05.2000"}, "SAPComponentKey": {"_label": "Component", "value": "PP-IS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Information System"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Production Planning and Control", "value": "PP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Information System", "value": "PP-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PP-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "104558 - Collective note PPIS, Release 3.1H"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Different errors occur in the Shop Floor Information System.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is caused by program errors. No modifications were made in the programs of the Shop Floor Information System or no modification notes were implemented.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>This note solves all known problems of the Shop Floor Information System except for the problems listed below. You only have to import the transports from directory /general/R3server/abap/note.0104558 of a Sapserv computer and make a few manual corrections. If possible, carry it out in a test system. Do not import the transport into the production system while it is in operation.<br />Cross-application errors of the Logistics Information System are not corrected.<br />Execute program RMCFNE22 once in the background before you execute the operation analysis.<br />The following notes are not contained in the collective note and you must implement them manually, if required:<br />Area repetitive manufacturing:<br />71597 Updating of information structure S028 (reporting point statistics) is not executed correctly for alternative units of measure.<br />78360 Material consumption statistics of repetitive manufacturing are not updated correctly.<br />78824 In the evaluation structure of information structure S025 only two key figures are displayed.<br />80247 In self-defined information structures for repetitive manufacturing that contain the quantity of goods received or actual scrap quantity key figures (for example, a copy of information structure S025), the units are not updated.<br />92453 No or incorrect LIS data for orders<br />101381 For the very rare case, that for a user-defined information structure in the area repetitive manufacturing goods receipt and goods issue dates are combined, it can happen that during goods issue backflush (Transaction MF4U) existing units are deleted.<br />Miscellaneous:<br />78940 Various error can occur when you copy standard information structures S021, S022, S023 and S024.<br />79258 In the work center analysis, the \"Responsible planner group\" exists as a characteristic, however, you would expect the \"Person responsible for work center\" or the \"Planner group of the capacity\". This note is only partially contained, RMCSTEXT and RMCSS024 must be adjusted for it.<br />79980 The posting date from the goods issue is not contained in any communications structure and thus cannot be used for the update. For this note only step five, the field catalog, is missing.<br />80595 If you post several goods receipts for production orders in one transaction, only the first goods receipt is updated in the Shop Floor Information System.<br />82111 Cancelling a goods issue for a production order or a process order is not updated in the Shop Floor Information System. This affects the standard information structures S026 and S027.<br />83433 The delivered lot size is not always filled.<br />87780 Posting date for partial deliveries. For this note, only the field catalog must be created.<br />94196 The German term \"Ausschu&#x00DF;\" (scrap) is misspelled five times as \"Auschu&#x00DF;\" in the Logistics Information Library.<br /><br />Attention:<br />If you implemented Note 119466 \"EMS: incorrect lines in planned/actual comparison\", you must again implement the the part of programs RMCF01TP, RMCF02TP, RMCF03TP, RMCF04TP, RMCF05TP, RMCF06TP, RMCF07TP, RMCF08TP, RMCF25TP, RMCF26TP, RMCF27TP, RMCFPKTP.<br /><br />The following objects are included in the transport:<br />Data elements:<br />MC_BUDTCMP<br />Function module:<br />MCF_STATISTICS_LIS, MCF_STATISTICS_LIS_SMRES, MCF_VERSION_UPD_V2<br />Programs: FMCF1F01, FMCF2F01, FMCF3F01, FMCF4F01, LMCF2F01, LMCF2TOP, LMCF2U00, RMCF01F0, RMCF02F0, RMCF02TP, RMCF04F0, RMCF04TP, RMCF05F0, RMCF08F0, RMCFNEUA, RMCFNEUC, RMCFNEUE, RMCSS021, RMCSS022, RMCSS023, RMCSS024, SAPFMCFF<br />Program texts:<br />RMCFNEUA<br />Structures and tables:<br />MCAFPO, MCAFVG, MCCOMP, MCFKENNZ, MCKALK, MCKALKW, S022, MCTBUDTCMP<br />Functional groups:<br />MCF4<br />Table contents:<br />T804A, TMC2, TMC23, TMC2D, TMC2F, TMC2K, TMC73, TMCEX</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BCT-PP (Production Planning and Control)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D027230)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000104558/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000104558/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104558/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104558/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104558/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104558/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104558/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104558/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104558/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "97017", "RefComponent": "PP-IS-DC", "RefTitle": "Cancel confirmation: Incorrect costs in S027", "RefUrl": "/notes/97017"}, {"RefNumber": "96849", "RefComponent": "PP-IS-DC", "RefTitle": "Completing technically --> empty entries in S026", "RefUrl": "/notes/96849"}, {"RefNumber": "93075", "RefComponent": "PP-IS-DC", "RefTitle": "Deadlocks during update in PPIS", "RefUrl": "/notes/93075"}, {"RefNumber": "91161", "RefComponent": "PP-IS-DC", "RefTitle": "Operations without work center", "RefUrl": "/notes/91161"}, {"RefNumber": "90080", "RefComponent": "PP-IS-REP", "RefTitle": "Termination in operation/work center analysis", "RefUrl": "/notes/90080"}, {"RefNumber": "90035", "RefComponent": "PP-IS-DC", "RefTitle": "Plant in operation/work center analysis", "RefUrl": "/notes/90035"}, {"RefNumber": "89659", "RefComponent": "PP-IS-DC", "RefTitle": "Setup of statistical data for costs", "RefUrl": "/notes/89659"}, {"RefNumber": "89593", "RefComponent": "PP-IS-DC", "RefTitle": "Deletion indicator and 'Partially released' status", "RefUrl": "/notes/89593"}, {"RefNumber": "89460", "RefComponent": "PP-IS", "RefTitle": "Used calendar in standard analyses", "RefUrl": "/notes/89460"}, {"RefNumber": "87780", "RefComponent": "PP-IS-DC", "RefTitle": "Posting date for partial deliveries", "RefUrl": "/notes/87780"}, {"RefNumber": "87744", "RefComponent": "PP-IS-DC", "RefTitle": "Scrap cause and personnel number in the PPIS", "RefUrl": "/notes/87744"}, {"RefNumber": "87465", "RefComponent": "PP-IS-DC", "RefTitle": "Posting date is not always filled", "RefUrl": "/notes/87465"}, {"RefNumber": "87040", "RefComponent": "PP-IS-DC", "RefTitle": "Combination of goods receipt and goods issue", "RefUrl": "/notes/87040"}, {"RefNumber": "87036", "RefComponent": "PP-IS-DC", "RefTitle": "Reversal of automatic goods movements", "RefUrl": "/notes/87036"}, {"RefNumber": "84202", "RefComponent": "PP-IS-DC", "RefTitle": "Incorrect actual scrap quantity order item", "RefUrl": "/notes/84202"}, {"RefNumber": "83858", "RefComponent": "PP-IS-DC", "RefTitle": "By-products in the shop floor information system", "RefUrl": "/notes/83858"}, {"RefNumber": "81030", "RefComponent": "PP-IS-REP", "RefTitle": "BCD_FIELD_OVERFLOW in work center/operatn analysis", "RefUrl": "/notes/81030"}, {"RefNumber": "79980", "RefComponent": "PP-IS-DC", "RefTitle": "Posting date is not updated", "RefUrl": "/notes/79980"}, {"RefNumber": "79974", "RefComponent": "PP-IS-DC", "RefTitle": "Phantom assemblies are updated", "RefUrl": "/notes/79974"}, {"RefNumber": "74317", "RefComponent": "PP-IS-DC", "RefTitle": "Execution times are updated incorrectly", "RefUrl": "/notes/74317"}, {"RefNumber": "67902", "RefComponent": "PP-IS", "RefTitle": "Negative actual lead times and actual queue times", "RefUrl": "/notes/67902"}, {"RefNumber": "62627", "RefComponent": "PP-IS", "RefTitle": "Backflush in the Shop Floor Information System", "RefUrl": "/notes/62627"}, {"RefNumber": "45341", "RefComponent": "PP-IS", "RefTitle": "Syntax error in program FMCF2xyz", "RefUrl": "/notes/45341"}, {"RefNumber": "362140", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/362140"}, {"RefNumber": "200653", "RefComponent": "PP-IS-REP", "RefTitle": "Repeated display of document/master data", "RefUrl": "/notes/200653"}, {"RefNumber": "200239", "RefComponent": "PP-IS-REP", "RefTitle": "In operation analysis not enough data is displayed", "RefUrl": "/notes/200239"}, {"RefNumber": "200154", "RefComponent": "PP-IS-DC", "RefTitle": "Incorrect costs with no planned costs intended", "RefUrl": "/notes/200154"}, {"RefNumber": "194347", "RefComponent": "PP-IS-DC", "RefTitle": "Deadlocks in table MCAFPOV", "RefUrl": "/notes/194347"}, {"RefNumber": "188966", "RefComponent": "PP-IS-DC", "RefTitle": "Statistical data setup for event PF", "RefUrl": "/notes/188966"}, {"RefNumber": "188202", "RefComponent": "PP-IS-DC", "RefTitle": "Date initial in S023", "RefUrl": "/notes/188202"}, {"RefNumber": "185039", "RefComponent": "PP-IS-DC", "RefTitle": "Actual scrap quantity order item is incorrect", "RefUrl": "/notes/185039"}, {"RefNumber": "182585", "RefComponent": "PP-IS-DC", "RefTitle": "Capacity split causes update errors", "RefUrl": "/notes/182585"}, {"RefNumber": "181537", "RefComponent": "PP-IS-DC", "RefTitle": "PERFORM ON COMMIT: NESTED_PERFORM_ON_COMMIT", "RefUrl": "/notes/181537"}, {"RefNumber": "180461", "RefComponent": "PP-IS-DC", "RefTitle": "Posting date of last confirmation not filled", "RefUrl": "/notes/180461"}, {"RefNumber": "172674", "RefComponent": "PP-IS-DC", "RefTitle": "Secondary resources not complete", "RefUrl": "/notes/172674"}, {"RefNumber": "170576", "RefComponent": "PP-IS-REP", "RefTitle": "Selection log in standard analysis repetitive mfg", "RefUrl": "/notes/170576"}, {"RefNumber": "170083", "RefComponent": "PP-IS-DC", "RefTitle": "By-products/co-products S026/S027", "RefUrl": "/notes/170083"}, {"RefNumber": "166200", "RefComponent": "PP-IS", "RefTitle": "Planned quantities in release versions incorrect", "RefUrl": "/notes/166200"}, {"RefNumber": "165034", "RefComponent": "PP-IS-DC", "RefTitle": "Statistical setup: MESSAGE_TYPE_X", "RefUrl": "/notes/165034"}, {"RefNumber": "162202", "RefComponent": "PP-IS-DC", "RefTitle": "Actual execution times are too long", "RefUrl": "/notes/162202"}, {"RefNumber": "160872", "RefComponent": "PP-IS-DC", "RefTitle": "Posting date in manufacture of co-products", "RefUrl": "/notes/160872"}, {"RefNumber": "160322", "RefComponent": "PP-IS-DC", "RefTitle": "Queue time in hours", "RefUrl": "/notes/160322"}, {"RefNumber": "159124", "RefComponent": "PP-IS-DC", "RefTitle": "SAPSQL_ARRAY_INSERT_DUPREC in MCF_VERSION_UPD_V1", "RefUrl": "/notes/159124"}, {"RefNumber": "150965", "RefComponent": "PP-IS-DC", "RefTitle": "Deletion flag and S026/MCCOMP", "RefUrl": "/notes/150965"}, {"RefNumber": "145294", "RefComponent": "PP-IS-DC", "RefTitle": "Text items in the material consumption", "RefUrl": "/notes/145294"}, {"RefNumber": "145120", "RefComponent": "PP-IS-REP", "RefTitle": "Text display in flexible analysis", "RefUrl": "/notes/145120"}, {"RefNumber": "138860", "RefComponent": "PP-IS-DC", "RefTitle": "MCAFPO: Event PB not for Partial confirmation", "RefUrl": "/notes/138860"}, {"RefNumber": "138319", "RefComponent": "PP-IS-DC", "RefTitle": "Setup repetitive manufacturing by-products", "RefUrl": "/notes/138319"}, {"RefNumber": "137340", "RefComponent": "PP-IS-DC", "RefTitle": "Setup of statistical data f. unplanned goods issues", "RefUrl": "/notes/137340"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "136057", "RefComponent": "PP-IS-REP", "RefTitle": "MCP9 - no time series for production line possible", "RefUrl": "/notes/136057"}, {"RefNumber": "135045", "RefComponent": "PP-IS-REP", "RefTitle": "Field overflow in production order/operation analysis", "RefUrl": "/notes/135045"}, {"RefNumber": "132614", "RefComponent": "PP-IS-REP", "RefTitle": "Resource and process order in standard analysis", "RefUrl": "/notes/132614"}, {"RefNumber": "128906", "RefComponent": "PP-IS-REP", "RefTitle": "Planned/actual comparison in order/operation analys", "RefUrl": "/notes/128906"}, {"RefNumber": "128705", "RefComponent": "PP-IS-REP", "RefTitle": "MCP1: Key figures can no longer be selected", "RefUrl": "/notes/128705"}, {"RefNumber": "128535", "RefComponent": "PP-IS-DC", "RefTitle": "CM25: DBIF_RTAB_NO_MEMORY", "RefUrl": "/notes/128535"}, {"RefNumber": "128290", "RefComponent": "PP-IS-DC", "RefTitle": "Capacity requirements is empty", "RefUrl": "/notes/128290"}, {"RefNumber": "128020", "RefComponent": "PP-IS-DC", "RefTitle": "Setup is too slow", "RefUrl": "/notes/128020"}, {"RefNumber": "126710", "RefComponent": "PP-IS-DC", "RefTitle": "Update problems in collective orders", "RefUrl": "/notes/126710"}, {"RefNumber": "125874", "RefComponent": "PP-IS", "RefTitle": "RMCFNEUA - 'SQL error 1562'", "RefUrl": "/notes/125874"}, {"RefNumber": "121674", "RefComponent": "PP-IS-EWS", "RefTitle": "EWS work center analysis: Capacity key figures", "RefUrl": "/notes/121674"}, {"RefNumber": "121052", "RefComponent": "PP-IS-DC", "RefTitle": "Incomplete update if S026/S027 is inactive", "RefUrl": "/notes/121052"}, {"RefNumber": "119927", "RefComponent": "PP-IS-DC", "RefTitle": "Update problems with different currencies (1)", "RefUrl": "/notes/119927"}, {"RefNumber": "117190", "RefComponent": "PP-IS-DC", "RefTitle": "Too large key fig.f.costs during setup of stat.data", "RefUrl": "/notes/117190"}, {"RefNumber": "113952", "RefComponent": "PP-IS-DC", "RefTitle": "Incomplete update", "RefUrl": "/notes/113952"}, {"RefNumber": "111967", "RefComponent": "PP-IS-DC", "RefTitle": "Update date information structure S023 and S024", "RefUrl": "/notes/111967"}, {"RefNumber": "111644", "RefComponent": "PP-IS-DC", "RefTitle": "Problems: Setup rep. manufacturing, document log", "RefUrl": "/notes/111644"}, {"RefNumber": "111299", "RefComponent": "PP-IS", "RefTitle": "Flexible analysis via Operations: too large values", "RefUrl": "/notes/111299"}, {"RefNumber": "110661", "RefComponent": "PP-IS-DC", "RefTitle": "Too large cap.key fgs in user-defined info structrs", "RefUrl": "/notes/110661"}, {"RefNumber": "110275", "RefComponent": "PP-IS-DC", "RefTitle": "Units in S021 and S023 / update log", "RefUrl": "/notes/110275"}, {"RefNumber": "109906", "RefComponent": "PP-IS-DC", "RefTitle": "Order confirmation: BUDAT, GRUND, PERNR", "RefUrl": "/notes/109906"}, {"RefNumber": "107973", "RefComponent": "PP-IS-DC", "RefTitle": "Deletion of partially delivered orders", "RefUrl": "/notes/107973"}, {"RefNumber": "107909", "RefComponent": "PP-IS-DC", "RefTitle": "Automatic goods movements by-/co-products", "RefUrl": "/notes/107909"}, {"RefNumber": "107122", "RefComponent": "PP-IS-DC", "RefTitle": "Problems with automatic goods movements", "RefUrl": "/notes/107122"}, {"RefNumber": "104523", "RefComponent": "PP-IS-DC", "RefTitle": "No costs during change of work center", "RefUrl": "/notes/104523"}, {"RefNumber": "104131", "RefComponent": "PP-IS-DC", "RefTitle": "No update rules for information structure S029", "RefUrl": "/notes/104131"}, {"RefNumber": "101694", "RefComponent": "PP-IS-DC", "RefTitle": "Double components are updated incorrectly", "RefUrl": "/notes/101694"}, {"RefNumber": "100515", "RefComponent": "PP-IS-DC", "RefTitle": "Setup orders with co-products and by-products", "RefUrl": "/notes/100515"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "137340", "RefComponent": "PP-IS-DC", "RefTitle": "Setup of statistical data f. unplanned goods issues", "RefUrl": "/notes/137340 "}, {"RefNumber": "119927", "RefComponent": "PP-IS-DC", "RefTitle": "Update problems with different currencies (1)", "RefUrl": "/notes/119927 "}, {"RefNumber": "138860", "RefComponent": "PP-IS-DC", "RefTitle": "MCAFPO: Event PB not for Partial confirmation", "RefUrl": "/notes/138860 "}, {"RefNumber": "104131", "RefComponent": "PP-IS-DC", "RefTitle": "No update rules for information structure S029", "RefUrl": "/notes/104131 "}, {"RefNumber": "194347", "RefComponent": "PP-IS-DC", "RefTitle": "Deadlocks in table MCAFPOV", "RefUrl": "/notes/194347 "}, {"RefNumber": "89593", "RefComponent": "PP-IS-DC", "RefTitle": "Deletion indicator and 'Partially released' status", "RefUrl": "/notes/89593 "}, {"RefNumber": "188966", "RefComponent": "PP-IS-DC", "RefTitle": "Statistical data setup for event PF", "RefUrl": "/notes/188966 "}, {"RefNumber": "188202", "RefComponent": "PP-IS-DC", "RefTitle": "Date initial in S023", "RefUrl": "/notes/188202 "}, {"RefNumber": "135045", "RefComponent": "PP-IS-REP", "RefTitle": "Field overflow in production order/operation analysis", "RefUrl": "/notes/135045 "}, {"RefNumber": "128290", "RefComponent": "PP-IS-DC", "RefTitle": "Capacity requirements is empty", "RefUrl": "/notes/128290 "}, {"RefNumber": "200653", "RefComponent": "PP-IS-REP", "RefTitle": "Repeated display of document/master data", "RefUrl": "/notes/200653 "}, {"RefNumber": "162202", "RefComponent": "PP-IS-DC", "RefTitle": "Actual execution times are too long", "RefUrl": "/notes/162202 "}, {"RefNumber": "111967", "RefComponent": "PP-IS-DC", "RefTitle": "Update date information structure S023 and S024", "RefUrl": "/notes/111967 "}, {"RefNumber": "170083", "RefComponent": "PP-IS-DC", "RefTitle": "By-products/co-products S026/S027", "RefUrl": "/notes/170083 "}, {"RefNumber": "96849", "RefComponent": "PP-IS-DC", "RefTitle": "Completing technically --> empty entries in S026", "RefUrl": "/notes/96849 "}, {"RefNumber": "166200", "RefComponent": "PP-IS", "RefTitle": "Planned quantities in release versions incorrect", "RefUrl": "/notes/166200 "}, {"RefNumber": "200154", "RefComponent": "PP-IS-DC", "RefTitle": "Incorrect costs with no planned costs intended", "RefUrl": "/notes/200154 "}, {"RefNumber": "128705", "RefComponent": "PP-IS-REP", "RefTitle": "MCP1: Key figures can no longer be selected", "RefUrl": "/notes/128705 "}, {"RefNumber": "128020", "RefComponent": "PP-IS-DC", "RefTitle": "Setup is too slow", "RefUrl": "/notes/128020 "}, {"RefNumber": "121052", "RefComponent": "PP-IS-DC", "RefTitle": "Incomplete update if S026/S027 is inactive", "RefUrl": "/notes/121052 "}, {"RefNumber": "113952", "RefComponent": "PP-IS-DC", "RefTitle": "Incomplete update", "RefUrl": "/notes/113952 "}, {"RefNumber": "100515", "RefComponent": "PP-IS-DC", "RefTitle": "Setup orders with co-products and by-products", "RefUrl": "/notes/100515 "}, {"RefNumber": "136057", "RefComponent": "PP-IS-REP", "RefTitle": "MCP9 - no time series for production line possible", "RefUrl": "/notes/136057 "}, {"RefNumber": "172674", "RefComponent": "PP-IS-DC", "RefTitle": "Secondary resources not complete", "RefUrl": "/notes/172674 "}, {"RefNumber": "182585", "RefComponent": "PP-IS-DC", "RefTitle": "Capacity split causes update errors", "RefUrl": "/notes/182585 "}, {"RefNumber": "79980", "RefComponent": "PP-IS-DC", "RefTitle": "Posting date is not updated", "RefUrl": "/notes/79980 "}, {"RefNumber": "160322", "RefComponent": "PP-IS-DC", "RefTitle": "Queue time in hours", "RefUrl": "/notes/160322 "}, {"RefNumber": "159124", "RefComponent": "PP-IS-DC", "RefTitle": "SAPSQL_ARRAY_INSERT_DUPREC in MCF_VERSION_UPD_V1", "RefUrl": "/notes/159124 "}, {"RefNumber": "128535", "RefComponent": "PP-IS-DC", "RefTitle": "CM25: DBIF_RTAB_NO_MEMORY", "RefUrl": "/notes/128535 "}, {"RefNumber": "107122", "RefComponent": "PP-IS-DC", "RefTitle": "Problems with automatic goods movements", "RefUrl": "/notes/107122 "}, {"RefNumber": "111644", "RefComponent": "PP-IS-DC", "RefTitle": "Problems: Setup rep. manufacturing, document log", "RefUrl": "/notes/111644 "}, {"RefNumber": "101694", "RefComponent": "PP-IS-DC", "RefTitle": "Double components are updated incorrectly", "RefUrl": "/notes/101694 "}, {"RefNumber": "107973", "RefComponent": "PP-IS-DC", "RefTitle": "Deletion of partially delivered orders", "RefUrl": "/notes/107973 "}, {"RefNumber": "109906", "RefComponent": "PP-IS-DC", "RefTitle": "Order confirmation: BUDAT, GRUND, PERNR", "RefUrl": "/notes/109906 "}, {"RefNumber": "121674", "RefComponent": "PP-IS-EWS", "RefTitle": "EWS work center analysis: Capacity key figures", "RefUrl": "/notes/121674 "}, {"RefNumber": "125874", "RefComponent": "PP-IS", "RefTitle": "RMCFNEUA - 'SQL error 1562'", "RefUrl": "/notes/125874 "}, {"RefNumber": "132614", "RefComponent": "PP-IS-REP", "RefTitle": "Resource and process order in standard analysis", "RefUrl": "/notes/132614 "}, {"RefNumber": "150965", "RefComponent": "PP-IS-DC", "RefTitle": "Deletion flag and S026/MCCOMP", "RefUrl": "/notes/150965 "}, {"RefNumber": "185039", "RefComponent": "PP-IS-DC", "RefTitle": "Actual scrap quantity order item is incorrect", "RefUrl": "/notes/185039 "}, {"RefNumber": "117190", "RefComponent": "PP-IS-DC", "RefTitle": "Too large key fig.f.costs during setup of stat.data", "RefUrl": "/notes/117190 "}, {"RefNumber": "145294", "RefComponent": "PP-IS-DC", "RefTitle": "Text items in the material consumption", "RefUrl": "/notes/145294 "}, {"RefNumber": "165034", "RefComponent": "PP-IS-DC", "RefTitle": "Statistical setup: MESSAGE_TYPE_X", "RefUrl": "/notes/165034 "}, {"RefNumber": "170576", "RefComponent": "PP-IS-REP", "RefTitle": "Selection log in standard analysis repetitive mfg", "RefUrl": "/notes/170576 "}, {"RefNumber": "200239", "RefComponent": "PP-IS-REP", "RefTitle": "In operation analysis not enough data is displayed", "RefUrl": "/notes/200239 "}, {"RefNumber": "128906", "RefComponent": "PP-IS-REP", "RefTitle": "Planned/actual comparison in order/operation analys", "RefUrl": "/notes/128906 "}, {"RefNumber": "160872", "RefComponent": "PP-IS-DC", "RefTitle": "Posting date in manufacture of co-products", "RefUrl": "/notes/160872 "}, {"RefNumber": "110661", "RefComponent": "PP-IS-DC", "RefTitle": "Too large cap.key fgs in user-defined info structrs", "RefUrl": "/notes/110661 "}, {"RefNumber": "111299", "RefComponent": "PP-IS", "RefTitle": "Flexible analysis via Operations: too large values", "RefUrl": "/notes/111299 "}, {"RefNumber": "104523", "RefComponent": "PP-IS-DC", "RefTitle": "No costs during change of work center", "RefUrl": "/notes/104523 "}, {"RefNumber": "181537", "RefComponent": "PP-IS-DC", "RefTitle": "PERFORM ON COMMIT: NESTED_PERFORM_ON_COMMIT", "RefUrl": "/notes/181537 "}, {"RefNumber": "145120", "RefComponent": "PP-IS-REP", "RefTitle": "Text display in flexible analysis", "RefUrl": "/notes/145120 "}, {"RefNumber": "138319", "RefComponent": "PP-IS-DC", "RefTitle": "Setup repetitive manufacturing by-products", "RefUrl": "/notes/138319 "}, {"RefNumber": "126710", "RefComponent": "PP-IS-DC", "RefTitle": "Update problems in collective orders", "RefUrl": "/notes/126710 "}, {"RefNumber": "110275", "RefComponent": "PP-IS-DC", "RefTitle": "Units in S021 and S023 / update log", "RefUrl": "/notes/110275 "}, {"RefNumber": "90080", "RefComponent": "PP-IS-REP", "RefTitle": "Termination in operation/work center analysis", "RefUrl": "/notes/90080 "}, {"RefNumber": "107909", "RefComponent": "PP-IS-DC", "RefTitle": "Automatic goods movements by-/co-products", "RefUrl": "/notes/107909 "}, {"RefNumber": "97017", "RefComponent": "PP-IS-DC", "RefTitle": "Cancel confirmation: Incorrect costs in S027", "RefUrl": "/notes/97017 "}, {"RefNumber": "93075", "RefComponent": "PP-IS-DC", "RefTitle": "Deadlocks during update in PPIS", "RefUrl": "/notes/93075 "}, {"RefNumber": "91161", "RefComponent": "PP-IS-DC", "RefTitle": "Operations without work center", "RefUrl": "/notes/91161 "}, {"RefNumber": "90035", "RefComponent": "PP-IS-DC", "RefTitle": "Plant in operation/work center analysis", "RefUrl": "/notes/90035 "}, {"RefNumber": "87465", "RefComponent": "PP-IS-DC", "RefTitle": "Posting date is not always filled", "RefUrl": "/notes/87465 "}, {"RefNumber": "84202", "RefComponent": "PP-IS-DC", "RefTitle": "Incorrect actual scrap quantity order item", "RefUrl": "/notes/84202 "}, {"RefNumber": "89460", "RefComponent": "PP-IS", "RefTitle": "Used calendar in standard analyses", "RefUrl": "/notes/89460 "}, {"RefNumber": "67902", "RefComponent": "PP-IS", "RefTitle": "Negative actual lead times and actual queue times", "RefUrl": "/notes/67902 "}, {"RefNumber": "62627", "RefComponent": "PP-IS", "RefTitle": "Backflush in the Shop Floor Information System", "RefUrl": "/notes/62627 "}, {"RefNumber": "79974", "RefComponent": "PP-IS-DC", "RefTitle": "Phantom assemblies are updated", "RefUrl": "/notes/79974 "}, {"RefNumber": "74317", "RefComponent": "PP-IS-DC", "RefTitle": "Execution times are updated incorrectly", "RefUrl": "/notes/74317 "}, {"RefNumber": "81030", "RefComponent": "PP-IS-REP", "RefTitle": "BCD_FIELD_OVERFLOW in work center/operatn analysis", "RefUrl": "/notes/81030 "}, {"RefNumber": "89659", "RefComponent": "PP-IS-DC", "RefTitle": "Setup of statistical data for costs", "RefUrl": "/notes/89659 "}, {"RefNumber": "87780", "RefComponent": "PP-IS-DC", "RefTitle": "Posting date for partial deliveries", "RefUrl": "/notes/87780 "}, {"RefNumber": "87744", "RefComponent": "PP-IS-DC", "RefTitle": "Scrap cause and personnel number in the PPIS", "RefUrl": "/notes/87744 "}, {"RefNumber": "83858", "RefComponent": "PP-IS-DC", "RefTitle": "By-products in the shop floor information system", "RefUrl": "/notes/83858 "}, {"RefNumber": "87036", "RefComponent": "PP-IS-DC", "RefTitle": "Reversal of automatic goods movements", "RefUrl": "/notes/87036 "}, {"RefNumber": "87040", "RefComponent": "PP-IS-DC", "RefTitle": "Combination of goods receipt and goods issue", "RefUrl": "/notes/87040 "}, {"RefNumber": "45341", "RefComponent": "PP-IS", "RefTitle": "Syntax error in program FMCF2xyz", "RefUrl": "/notes/45341 "}, {"RefNumber": "180461", "RefComponent": "PP-IS-DC", "RefTitle": "Posting date of last confirmation not filled", "RefUrl": "/notes/180461 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "31H", "To": "31H", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 1, "URL": "/corrins/0000104558/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}