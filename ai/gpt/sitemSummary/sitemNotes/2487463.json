{"Request": {"Number": "2487463", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 393, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000019380432017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002487463?language=E&token=AFE3F96E2A042A8F5A5C09E8475EB715"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002487463", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002487463/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2487463"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "13.11.2017"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-REL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Suite and SAP S/4HANA Release Information"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Suite and SAP S/4HANA Release Information", "value": "XX-SER-REL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-REL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2487463 - SAP S/4HANA Cloud 1708: Release Restriction Note"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are using&#160;<em>SAP S/4HANA Cloud 1708.&#160;</em>This note&#160;informs you about any restrictions&#160;in this release. This SAP Note is subject to change, please check it for regular updates.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Relevant for <em>SAP S/4HANA Cloud 1708</em>:</strong></p>\r\n<ul>\r\n<li><strong><strong>General:</strong></strong></li>\r\n<ul>\r\n<li>In case you require additional roles&#160;to set authorization, please get in touch with&#160;the SAP Service Center.</li>\r\n<li>Tax calculation in the US:</li>\r\n<ul>\r\n<li>The internal US tax calculation in <em>SAP S/4HANA Cloud 1708</em>&#160;and any reporting based on this calculation may not comprise of all reporting requirements in your jurisdiction due to the specifics of the tax law system in the United States. You must check with your accounting or tax experts&#160;to make sure that the results generated by this report are fully compliant with your relevant jurisdictions&#8217; specific sales and use tax reporting requirements. For further information on these restrictions, please refer to the SAP Note <strong><a target=\"_blank\" href=\"/notes/**********\">2440096</a></strong>.</li>\r\n</ul>\r\n<li>Configure backorder processing (BOP) segment: The maintenance of the selection criteria comes with the following restrictions:</li>\r\n<ul>\r\n<li>Maintenance can be done in English language only. Logging on to the system in a different language does not change that.</li>\r\n<li>There is no input help for the values of the selection attributes, but an auto-completion function for the possible operations only. Operators have to be maintained in English.</li>\r\n</ul>\r\n<li>Restrictions&#160;valid for SAP Fiori visual theme for classic applications are described in the SAP Note <strong><a target=\"_blank\" href=\"/notes/2428021\">2428021</a>.</strong></li>\r\n<li>Product number with leading zeros is not listed in the enterprise search when searching with a product number only. Please see SAP Note <strong><a target=\"_blank\" href=\"/notes/2543546\">2543546</a></strong> for further details.&#160;</li>\r\n</ul>\r\n<li><strong><strong>Configuration:</strong></strong></li>\r\n<ul>\r\n<li>Please contact <em>SAP Service Ce</em>nter, if you need further content configurations. Be aware that not all changes are possible.</li>\r\n<li>Maintenance of scope (scope items, languages) needs to be done before content activation and other configurations happen.</li>\r\n<li>Maintenance of&#160;chart of accounts configurations and personalization (key changes) of pre-configured organizational data in the Manage Your Solution app needs to be done before content activation. After the content activation, an extension of organizational structure is still possible.</li>\r\n<li>Deletion of customizing entries is currently not allowed.</li>\r\n<li>Currently, only one change project at a time is allowed.</li>\r\n<li>Currently, only one country solution can be changed at a time across the entire customer project. Simultaneous changes to different countries are not allowed even if conducted by different users.</li>\r\n<li>Self Service Configuration UIs (SSCUIs):</li>\r\n<ul>\r\n<li>Save Button might remain disabled even after a user fills all the input fields. In this case, please choose the value from the F4 help wherever applicable. The Save Button should then be enabled.</li>\r\n<li>List of released SSCUIs can be found in the Release Information Note (SAP Note <strong><a target=\"_blank\" href=\"/notes/2500088\">2500088</a></strong>).</li>\r\n</ul>\r\n</ul>\r\n<li><strong>Extensibility:</strong></li>\r\n<ul>\r\n<li>Custom Business Objects: CBOs are not enabled for read access logging and change logging.</li>\r\n<li>In the <em>SAP S/4HANA Cloud Starter Edition</em> extensibility for key users is deactivated by default, but can be activated upon request.&#160;Please see SAP Note <strong><a target=\"_blank\" href=\"/notes/2283716/E\">2283716</a></strong>&#160;for further details.</li>\r\n<li>Custom fields and custom business object cannot be marked as relevant for Data Protection &amp; Privacy (DP&amp;P) and&#160;cannot be blocked or deleted. Please don&#180;t store&#160;DP&amp;P relevant content in custom fields or custom business objects.</li>\r\n</ul>\r\n<li><strong>SAP S/4HANA embedded analytics:</strong></li>\r\n<ul>\r\n<li>It is not possible to extract data from the <em>SAP S/4HANA Cloud 1708</em> instance into the SAP BW hub.</li>\r\n</ul>\r\n<li><strong>Information LifeCycle Management:</strong></li>\r\n<ul>\r\n<li>Value of the field \"Time Reference\" cannot be maintained from the Object Groups maintenance app. Users can edit values of this field during rule maintenance.</li>\r\n</ul>\r\n<li><strong>Localization:</strong></li>\r\n<ul>\r\n<li>For localization-specific restrictions, please see the Country Versions: Release Information &amp; Restriction Note (SAP Note&#160;<strong><a target=\"_blank\" href=\"/notes/2489378\">2489378</a>)</strong>.</li>\r\n</ul>\r\n<li><strong>Browser/device restrictions:</strong></li>\r\n<ul>\r\n<li>Restrictions of the SAP Fiori visual theme for classic applications as well as general browser and device restrictions can be found in the Browser/ Device Information Note (SAP Note <a target=\"_blank\" href=\"/notes/2487425\"><strong>2487425</strong></a>).</li>\r\n</ul>\r\n<li><strong>Master Data Management:</strong></li>\r\n<ul>\r\n<li>Consolidation Process: for the financial&#160;service specific business partner relationship categories&#160;FSB001 and FSB002 it is not possible to activate the detailed data stored within the table BUT053 using Master Data Governance (MDG), Consolidation and Mass Processing.</li>\r\n<li>When you try to edit a product from the Product Master app, which has valuation areas attached and the BWVA1 (Valuation Variant for Future Standard Cost Estimate) field filled out, you will receive the following error: &#8220;The value 0 is not allowed for the filled future valuation strategy&#8221;. It will not be possible to save that product in the Product Master app. Please change material using the Change Material app instead.</li>\r\n</ul>\r\n<li><strong>Financial specific:</strong></li>\r\n<ul>\r\n<li>For&#160;printing of lists please&#160;use&#160;SAP Fiori app Display G/L Account Line Items and perform the \"Download to Excel\" and&#160;the \"Print from Excel\".</li>\r\n<li>If you want to create an E-Balance sheet for Germany:</li>\r\n<ol>\r\n<li>Upload the taxonomy into the system</li>\r\n<li>Enter the mapping of the accounts to the taxonomy positions</li>\r\n<li>You can now either:</li>\r\n</ol>\r\n<ul style=\"list-style-type: none;\">\r\n<li>i. download and install the ERP standalone client for&#160;E-Balance&#160;and enter the data into this excel sheet manually, validate and sent off to the authority or</li>\r\n<li>ii. transfer the mapped data to your tax consultant</li>\r\n</ul>\r\n<li>Central bank reporting (Z5A) for Germany:</li>\r\n<ol>\r\n<li>Set indicator in invoices via app Mange Journal Entries</li>\r\n<li>Use SAP&#8217;s service offering to fill BAdI according to your specifications</li>\r\n</ol>\r\n<li>Taxes:</li>\r\n<ul>\r\n<li>Taxation is only possible for Domestic Transactions in Project Based Services&#160;and Sell from Stock scenarios.</li>\r\n<li>It is not possible to&#160;configure the external tax connection credentials from partner Vertex. These credentials can only be entered in a backend view by&#160;the SAP Service Center.</li>\r\n<li>If additional example Data for export taxes and domestic service taxes&#160;are required, please get in touch with&#160;the SAP Service Center.</li>\r\n</ul>\r\n<li>Fiori app Manage Bank Statement: For non-digit currencies please enter the mandatory header field \"House bank account\" of bank statement first, otherwise the amounts are displayed with two digits by default and the balance will not be converted respecting the decimals: e.g. an amount of 100,00 will be shown as 10000 Yen and not as 100 Yen. Alternatively, you can change the amount fields of non-digit currencies manually.</li>\r\n<li>It is only possible to do validation &amp; substitution on journal entries for the fields, which are delivered under Validation &amp; Substitution Cloud BADIs.</li>\r\n<li>The direct debit payment method is available only for SEPA countries and the five non-SEPA countries: Australia, Canada, United States, Philippines and New Zealand.</li>\r\n<li>Cash Flow Analyzer app: Under \"Cumulative Display\" mode fields: \"Planning Level\", \"Planning Group\", \"Liquidity Item\" can't be put in the setting \"Group\".</li>\r\n<li>Account Determination app:</li>\r\n<ul>\r\n<li>It is not allowed to change the posting keys or rules for a transaction key.</li>\r\n<li>It is not allowed to delete any existing records.</li>\r\n</ul>\r\n<li>Data Aging: with the following apps, you cannot select journal entries that have been moved to the historical part of the database:</li>\r\n<ul>\r\n<li>Display G/L Account Balances</li>\r\n<li>Display G/L Account Line Items &#8211; Reporting View</li>\r\n<li>Display G/L Account Line Items &#8211; Posting View</li>\r\n</ul>\r\n<li>Consolidation: if one of the scope items DE_1SG or US_1SG (\"Financial Consolidation for S/4 HANA Cloud\") has been installed, but not finetuned with&#160;the <em>SAP S/HANA 1705 Cloud</em>&#160;release, please raise an incident to request XX_1SG Scope Extension Procedure to the SAP Service Center.</li>\r\n</ul>\r\n<li><strong>View Browser Application:</strong></li>\r\n<ul>\r\n<li>If the table variant is modified in View Browser, there could be a possibility to have the outdated app state.&#160;It is therefore not recommended to bookmark the page but rather to re-launch the application.</li>\r\n</ul>\r\n<li><strong>Cloud Foundation:</strong></li>\r\n<ul>\r\n<li>Extensibility Cockpit supports English language only irrespective of logon language.</li>\r\n</ul>\r\n<li><strong>Sales &amp; Distribution:</strong></li>\r\n<ul>\r\n<li>Leading zeros&#160;should be entered if you search for documents (such as Sales Orders, Billing Documents and Deliveries) in the filter bar, when you search with search condition \"starts with\".</li>\r\n</ul>\r\n<li><strong>Procurement:</strong></li>\r\n<ul>\r\n<li>Setting of approval values for the Purchase Order Approval via Self-Service Configuration UI has been replaced by Self-Service Configuration UI Activate Flexible Workflow for Purchase Orders and related apps for Flexible Workflows. If you want to continue to configure the older Purchase Order Approval process (for existing customers), please create a ticket on component XX-S4C-SRV-CON with additional configuration changes.&#160;Existing customers should consider an initiative to migrate to the Flexible Workflow for Purchase Orders. You can find additional information under this <strong><a target=\"_blank\" href=\"https://help.sap.com/viewer/9d794cbd48c648bc8a176e422772de7e/1708%20500/en-US/46c9e5b6ba7a4feeb920ca1cbad68c73.html\">link</a>.</strong></li>\r\n</ul>\r\n<li><strong>SAP S/4HANA Cloud for invoice processing by OpenText:</strong></li>\r\n<ul>\r\n<li>SSCUI app Supplier Settings: Please ensure that the sum of the values entered in the auto coding split is exactly 100%. This app provides currently no consistency check.</li>\r\n<li>This solution supports&#160;a subset of all countries supported by <em>S/4HANA Cloud 1708</em>. Please check the Customer Guide for scope item \"Invoice Processing by OpenText\" (1LE) for further details.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><strong>Relevant for <em>SAP S/4HANA Cloud 1708</em> excluding <em>SAP S/4HANA Cloud 1708 for Finance</em>:</strong></p>\r\n<ul>\r\n<li><strong>Project System:</strong></li>\r\n<ul>\r\n<li>Note Text related to Billing Items (Fixed Price, Time and Expense Items) of your Project:</li>\r\n<ul>\r\n<li>Fixed Price Items: If the note text is changed via the Plan Customer Project app, this change will presently not be reflected in the corresponding billing proposal item entry in the Release Billing Proposals app.</li>\r\n<li>Time and Expense Items: If the note text for an actual cost posting in the Release Billing Proposals app is modified by a project manager, a modification in My Timesheet app by an employee will overwrite the note text that the project manager had initiated. Upon finding a different note text in the Release Billing Proposals app, the project manager can manually edit/update the note text before releasing the billing proposal item(s).</li>\r\n<li>Restrictions of SAP CoPilot while using Release Billing Proposals:<br />It is not possible to navigate to a screenshot of Billing Proposal Item Details through collection using SAP CoPilot&#160;in a new session of Fiori Launchpad. You will be navigated to the screen containing the list of billing proposals instead. \"Go to Linked Screen\" is&#160;always navigating to the last saved URL of the current browser user session. Project manager can access the Release Billing Proposals app, select the relevant billing proposal(s) and manually navigate to the billing proposal items screen if needed.</li>\r\n</ul>\r\n</ul>\r\n<li><strong>Commercial Project Management:</strong></li>\r\n<ul>\r\n<li>The project management apps do not support multiple employments for an employee record.</li>\r\n</ul>\r\n<li><strong>Sales &amp; Distribution:</strong></li>\r\n<ul>\r\n<li>Apps Order-to-Cash Performance Monitor and Order-to-Cash Performance Monitor - Time Series: Process Flow and Aggregated Process Flow for Sales Process Performance Monitoring do not display backwards connection. Therefore&#160;some information about the process execution may not be available in the diagrams.</li>\r\n</ul>\r\n<li><strong>Procurement:</strong></li>\r\n<ul>\r\n<li>Procurement Overview Page: List Apps are not sorted and filtered in the same way as the cards. When navigating from the card headers, the corresponding app might be filtered and sorted in a different way than the card is filtered and sorted in the Overview Page. This restriction only applies to the transactional cards Monitor Purchase Order Items and Monitor RFQs.</li>\r\n</ul>\r\n<li><strong>Resource Management:</strong></li>\r\n<ul>\r\n<li>It is not possible to switch off Resource Scheduling functionality, once it has been switched on (system switch).</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><strong>Relevant for <em>SAP S/4HANA Cloud 1708</em>&#160;excluding <em>SAP S/4HANA Cloud 1708&#160;for Professional Services</em>:</strong></p>\r\n<ul>\r\n<li><strong>Financial specific:</strong></li>\r\n<ul>\r\n<li>App Journal Entries List can't display universal journals integrated from FI (ACDOCA). It only displays Journal Entries (ECMCA) put into Cloud Consolidation. Please use another FI accounting app related to Journal entries, such as Journal Entry Analyzer,<em> </em>to display these universal journals.</li>\r\n<li>Revenue Recognition (Event-Based) &#8211; Sales Order: Only standard sales order based sell from stock processes and delivery based billings will trigger revenue recognition postings (Sales Document Item Category TAN). Variants of sell from stock processes such as the following will not trigger any revenue recognition postings:</li>\r\n<ol style=\"list-style-type: lower-roman;\">\r\n<li>returns</li>\r\n<li>intercompany sales</li>\r\n<li>third-party sales</li>\r\n</ol>\r\n<li>Intercompany Stock Transfer Order: costs of sales are not assigned to a profitability segment and are also not&#160;displayed in&#160;the account-based profitability analysis.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><strong>Relevant for <em>SAP S/4HANA Cloud 1708&#160;</em>excluding <em>SAP S/4HANA&#160;Cloud 1708 for Finance</em> &amp; <em>SAP S/4HANA&#160;Cloud 1708 for Professional Services</em>:</strong></p>\r\n<ul>\r\n<li><strong>General:</strong></li>\r\n<ul>\r\n<li>If factory/holiday calendars need to be updated, please reach out to the <em>SAP Service Center</em>. This can be the case especially for the&#160;following applications:</li>\r\n<ul>\r\n<li>Material Requirements Planning (MRP)</li>\r\n<li>Available to Promise (ATP)</li>\r\n<li>Delivery Scheduling</li>\r\n<li>Production Order Scheduling</li>\r\n</ul>\r\n</ul>\r\n<li><strong>Sales &amp; Distribution:</strong></li>\r\n<ul>\r\n<li>Controls used in the&#160;apps Business Process Activities and Aggregated Business Process Activities for Order-to-Cash Process Performance Monitoring&#160;cannot&#160;currently display backwards connections. So, some information referring to previous process steps will not be reflected on the diagrams.</li>\r\n<li>The log for Resolve Payment Card Issues app&#160;&#8211; Schedule Job should list documents that were processed and errors that occurred during the report run. This log is currently not available. You can check for updates while changing/displaying the Sales Order.</li>\r\n</ul>\r\n<li><strong>Procurement:</strong></li>\r\n<ul>\r\n<li>Procurement Overview Page: while navigating&#160;to List Apps from the card header, the list may be greyed out for some cards. In this case, please&#160;press \"Go\" to make the list active.</li>\r\n</ul>\r\n<li><strong>Produce:</strong></li>\r\n<ul>\r\n<li>Predictive Model Material &#8211; Overdue Stock in Transit&#160;cannot be used in the app Material &#8211; Overdue Stock in Transit in a productive environment/system.</li>\r\n</ul>\r\n<li><strong>Product Lifecycle Management (PLM):</strong></li>\r\n<ul>\r\n<li>Restrictions on PLM &#8211; Advanced Variant Configuration can be found under the SAP Note&#160;<strong><a target=\"_blank\" href=\"/notes/2504074\">2504074</a>&#160;</strong></li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><strong>Relevant for <em>SAP S/4HANA Cloud 1708&#160;for Professional Services</em> only:</strong></p>\r\n<ul>\r\n<li>Material determination configuration within the DIP&#160;(Dynamic Item Processor)&#160;is not possible at the moment, therefore additional material/activity&#160;types T* should&#160;not be created.&#160;Please take the existing 20 T* materials and rename them.</li>\r\n<li>Credit Agency Integration and Credit Management&#160;are not available for <em>S/4HANA Cloud 1708 for Professional Services</em>.</li>\r\n<li>Scope items&#160;\"Asset Under Construction - Parallel Ledger\" (1GF)&#160;and&#160;\"Asset Under Construction\" (BFH)&#160;are parts of the packaging for <em>S/4HANA Cloud 1708 for Professional Services</em>, but cannot be used right now as a process functionality.&#160;</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><strong>&#160;</strong></p>\r\n<p>&#65279;&#65279;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D068020"}, {"Key": "Processor                                                                                           ", "Value": "D068020"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002487463/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002487463/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002487463/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002487463/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002487463/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002487463/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002487463/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002487463/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002487463/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2504074", "RefComponent": "LO-VCH", "RefTitle": "Restrictions for Advanced Variant Configuration SAP S/4HANA 1708 / C1709", "RefUrl": "/notes/2504074"}, {"RefNumber": "2500088", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA Cloud 1708: Release Information Note", "RefUrl": "/notes/2500088"}, {"RefNumber": "2489378", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA Cloud 1708, Country Versions: Release Information & Restriction Note", "RefUrl": "/notes/2489378"}, {"RefNumber": "2487425", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA Cloud 1708: Browser/ Device Information Note", "RefUrl": "/notes/2487425"}, {"RefNumber": "2440096", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA Cloud 1705 (and all later releases) Restrictions Internal US Tax Calculation", "RefUrl": "/notes/2440096"}, {"RefNumber": "2428021", "RefComponent": "XX-SER-REL", "RefTitle": "S/4HANA Cloud 1711 (and later releases): Restrictions on WebGUI/HTML GUI", "RefUrl": "/notes/2428021"}, {"RefNumber": "2283716", "RefComponent": "BC-SRV-APS-EXT-SL", "RefTitle": "Key User Application is not configured", "RefUrl": "/notes/2283716"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2538700", "RefComponent": "CA-GTF-MIG", "RefTitle": "Collective SAP Note and FAQ for SAP S/4HANA Migration Cockpit - File/Staging (Cloud / SAPSCORE)", "RefUrl": "/notes/2538700 "}, {"RefNumber": "2487425", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA Cloud 1708: Browser/ Device Information Note", "RefUrl": "/notes/2487425 "}, {"RefNumber": "2500088", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA Cloud 1708: Release Information Note", "RefUrl": "/notes/2500088 "}, {"RefNumber": "2514864", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA Cloud 1708: Performance Restrictions", "RefUrl": "/notes/2514864 "}, {"RefNumber": "2489378", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA Cloud 1708, Country Versions: Release Information & Restriction Note", "RefUrl": "/notes/2489378 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}