{"Request": {"Number": "36353", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 360, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000096462017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=3CE7CAF6F35C5B0D8567B3F058DA706C"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "36353"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 80}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Customizing"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.08.2023"}, "SAPComponentKey": {"_label": "Component", "value": "FI-GL-GL-J"}, "SAPComponentKeyText": {"_label": "Component", "value": "Integration/Accounting Interface"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Accounting", "value": "FI-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "FI-GL-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Integration/Accounting Interface", "value": "FI-GL-GL-J", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-GL-J*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "36353 - Accounting Interface: Summarization of FI documents"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>When you post documents using the AC interface (from SD, MM or other applications), items appear in the FI document which are identical in all or in almost all fields.<br /><br />This may also cause error message F5 727 ('Maximum number of items in FI reached'). The system issues this error message if the maximum number of 999 items is exceeded in an FI document.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAPLFACI, VF01, VF02, VF04, MB01, MB11, MR1M, CO15, BSEG, totaling, document summarization, CKMI_RUN, F5 727, F1 807</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The system will only total items in the FI document if you made the proper settings for this action in Customizing (= sending application, for example: VBRK for SD, MKPF for MM Position Management, RMRP for MM Logistics Invoice Verification).<br />There is no summarization for documents that are entered in FI (object type BKPF) application (for example EDI invoice receipt, FB01) or in invoice verification (MR01) (with object type BKPF).<br /><br />The system can only total items in the FI document if they have the same account assignments and only differ in the value fields.<br />Only G/L account line items can be summarized or totaled, but not customer line items or vendor line items. In addition, it is not possible to total across different G/L accounts.<br />The summarization can be achieved by deleting certain fields in all items (can be configured in Customizing).<br />As a consequence, these fields will not contain data in the FI document. They are therefore no longer available for selection, clearing or reconciliation with other applications.</p>\r\n<p><strong>This field deletion only affects the FI document, not documents from other AC applications.<br />The field contents deleted in the FI document are still available for the other AC applications and can be updated there. The AC applications are not updated on the basis of the FI document. In addition, there are other summarization procedures in other applications, for example in CO (Note 147766).</strong></p>\r\n<p>The reports contained in the correction instructions of this SAP Note cannot be implemented automatically using transaction SNOTE.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Procedure for configuring summarization:<br /><br />Carry out the following actions for each object type (sending application, AWTYP field), for which you want to summarize FI documents.<br />You can find the AWTYP object type on the detail screen 'Document header' under the 'Name' field when you display the document using transaction FB03.</p>\r\n<p>1.&#x00A0;If you think an FI document has too many items, find out in which fields the items differentiate.<br /> To do so, display the FI document (FB03) or table BSEG and ONE FI document (SE16).</p>\r\n<p>2. If there are items which are the same in all same account assignments:</p>\r\n<p>Make the following entry in the TTYPV table (transaction OBCY):</p>\r\n<p>Table   Field name</p>\r\n<p>* *</p>\r\n<p>As a consequence, items which are the same in all fields except value fields are totaled, that is, they are combined into a single item.</p>\r\n<p>(The '* *' entry can only be made in Release 3.0D or higher.) If you use a release lower than 3.0D, enter any field which, when deleted, will not disrupt transactions from the affected application. As of Release 4.0, the entry  '* *' can no longer be made using the view maintenance (SM31) of the table TTYPV. Check whether the entry '* *' can be replaced by an entry of a field name. If required, you can use the report ZTTYPV contained in the correction instructions of this SAP Note to make the entry '* *' for the required object type. Alternatively, you can use the report ZTTYPS to place generic entries in the table TTYPS so that you can later also place generic entries in the table TTYPV using transaction OBCY.</p>\r\n<p>3. If there are ITEMS which differ with regards to specific fields:</p>\r\n<p>Enter the fields in the TTYPV table.</p>\r\n<p>The contents of the fields entered in the TTYPV table are deleted.</p>\r\n<p>This has a higher summarization effect than entry '* *'!</p>\r\n<p>Typical fields for each application are:</p>\r\n<ol><ol>Application        AWTYP  Table   Field name</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>SD VBRK BSEG MATNR</ol></ol><ol><ol>Billing                           MEINS</ol></ol><ol><ol>MENGE</ol></ol><ol><ol>PAOBJNR</ol></ol><ol><ol>POSN2</ol></ol><ol><ol>VBEL2</ol></ol><ol><ol>WERKS</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>MM                 MKPF   BSEG    BPMNG</ol></ol><ol><ol>Position Management              BPRME</ol></ol><ol><ol>ERFME</ol></ol><ol><ol>ERFMG</ol></ol><ol><ol>MATNR</ol></ol><ol><ol>MEINS</ol></ol><ol><ol>MENGE  (restriction PSM-FM see below)</ol></ol><ol><ol>PAOBJNR</ol></ol><ol><ol>POSN2</ol></ol><ol><ol>WERKS</ol></ol><ol><ol>BWTAR</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>MM                 RMRP   BSEG    BPMNG</ol></ol><ol><ol>Logistics                        BPRME</ol></ol><ol><ol>Invoice Verification              ERFME</ol></ol><ol><ol>ERFMG</ol></ol><ol><ol>MATNR</ol></ol><ol><ol>MEINS</ol></ol><ol><ol>MENGE  (restriction PSM-FM see below)</ol></ol><ol><ol>PAOBJNR</ol></ol><ol><ol>WERKS</ol></ol><ol><ol>As of 4.0A</ol></ol><ol><ol>HR                 HRPAY         see SAP Note 116523</ol></ol><ol><ol>Payroll accounting</ol></ol><ol><ol>The list of the allowed fields in Release 3.1I includes the following entries in the TTYPS table:</ol></ol><ol><ol>BSEG BPMNG</ol></ol><ol><ol>BSEG BPRME</ol></ol><ol><ol>BSEG BWTAR</ol></ol><ol><ol>BSEG ERFME</ol></ol><ol><ol>BSEG ERFMG</ol></ol><ol><ol>BSEG MATNR</ol></ol><ol><ol>BSEG MEINS</ol></ol><ol><ol>BSEG MENGE</ol></ol><ol><ol>BSEG PAOBJNR</ol></ol><ol><ol>BSEG POSN2</ol></ol><ol><ol>BSEG PPRCT</ol></ol><ol><ol>BSEG VBEL2</ol></ol><ol><ol>BSEG WERKS</ol></ol><ol><ol>BSEG PRCTR (for restrictions, see below)</ol></ol><ol><ol>BSEG FKBER (SAP Note 413418)</ol></ol>\r\n<p>General remarks:<br />Items with identical account assignments but different debit/credit codes (BSEG-SHKZG) will NOT be put together as a single item (for example, revenues and sales deductions in billing documents).<br /><br />In releases lower than 3.0F, the system did not allow all of these fields as entries for the TTYPV table. To expand the number of fields allowed for the TTYPV table, enter the fields in the TTYPS table. (Call transaction SM31 (view maintenance) to add an entry to the V_TTYPS view.) (You can add the BSEG BWTAR field only using transaction SE16 (table maintenance) for the TTYPS table). The standard system contains the BSEG-PRCTR in the TTYPS table only as of Release 4.6B.<br />This is an SAP table modification. Only enter the fields listed above. Do not enter any other fields without first contacting SAP, since these fields will then be deleted in every FI document from the application in question.<br /><br />Notes:</p>\r\n<ul>\r\n<li>When you carry out summarization using the material number (MATNR) during position management or invoice verification transactions, the 'material number' information is lost in the BSEG document table of Financial Accounting. Therefore no entries are written to the BSIM table. This can have the following consequences:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Transactions such as MR51 (accounting documents for material) or MB5B (balance at posting date) produce incorrect results.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The evaluation of balances at material number level (characteristic 0MATERIAL) in the <strong>SAP Business Information Warehouse</strong> displays faulty or incomplete results.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>You can no longer use the Value Flow Monitor (transaction CKMVFM) to compare differences at material level between price difference accounts and the relevant FI postings.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>If you delete the material number (MATNR), the amount (MENGE), the unit of measure (MEINS) or the plant (WERKS) in position management, this information is no longer available in financial accounting. The RMVKON00 report (transactions MRKO and MRPI) however, which indicates and settles the consignation stock, is based on the documents of financial accounting. This has two consequences:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If the FI documents are not summarized, each individual withdrawal is indicated in the report and can also be individually settled. If the FI documents are summarized, several withdrawals can be displayed in one single report line and can no longer be separately settled. Furthermore, certain data, such as material number, amount and unit of measure, may no longer be displayed.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If in the FI documents the material number or the plant is deleted and you settle the withdrawal, the tax codes that you defined using the tax indicator (in the material master) or in a purchasing info record can no longer be found. Instead, only the tax code that you defined as default value in the Customizing settings for the invoice verification (transaction OMR2) is determined. If no valid tax code has been defined there, the settlement terminates with message FF 704.</li>\r\n</ul>\r\n</ul>\r\n<p>Read Note 38982 to learn how to determine the tax code.<br /> For Release 4.0a, these restrictions no longer apply to the RMVKON00 report.</p>\r\n<ul>\r\n<li>Clearing of the GR/IR clearing account for each purchase order item: The account assignments to the purchase order (EBELN) and to the purchase order item (EBELP) are NOT deleted by default. In both the goods receipt with reference to the purchase order (MB01) and the invoice verification, they are assigned to an account in the FI document and prevent the summarization of the FI document.<br />This makes it possible to clear the GR/IR clearing account at purchase order item level.</li>\r\n</ul>\r\n<p><strong>Using Profit center for summarization</strong></p>\r\n<p>If you summarize using the Profit center field in FI, only FI and PCA balances can be reconciled at account level.<br />A summarization using the Profit center field is only worthwhile if other CO account assignments are also summarized. These account assignments are therefore also not available for a derivation of the Profit center field when the FI document are subsequently posted to Profit Center Accounting using transaction 1KE8. Since summarized documents are always postings whose original is <strong>not</strong> posted in FI, you should always subsequently post such documents from the sending application component, anyway. However, you can directly transfer unoriginal FI documents using Note 102634 or, as of Release 4.5B, using the standard system. In this case, you must make sure that this function is not used for the summarized documents.<br />For the reason stated above, a summarization using the Profit center field is only worthwhile for transaction HRPAY. Note that HR only supports a subsequent posting from the sending application component as of Release 4.5A. In the releases 4.0A up to and including 4.0B, no programs are available in the standard system to generate subsequently the profit center document. You should implement Note 118533 in these releases beforehand to make sure that the online document flow from HR to PCA works.<br />A summarization using the Profit center field should definitely not be carried out in Releases 4.0A and 4.0B if the online update to Profit Center Accounting has not been activated in the fiscal year or if it should be activated subsequently (mid-year).<br /><br /><strong>Restrictions when using General Ledger Accounting (new) (FI-GL-FL):</strong><br />When you use General Ledger Accounting (new), the system no longer supports the summarization of the fields that are totals record characteristics or document splitting characteristics in the General Ledger or Special Purpose Ledger. This is necessary because the General Ledger must be able to reproduce from the FI document.</p>\r\n<p><strong>Restrictions when using Funds Management (PSM-FM):</strong></p>\r\n<p>Funds Management generates exactly one Funds Management document for each Financial Accounting document. If you carry out the summarization in Financial Accounting using fields that are updated in Funds Management, a posting termination can occur in Funds Management if the Funds Management document is generated with more than 999 lines as a result. In particular, the correct update in Funds Management may also be affected.<br />If you have set 'GR and IR' for the GR update or IR update in the 'Funds Management Updating Control' customizing activity (transaction OFUP), for example, a summarization using the fields BSEG-XREF3 (-&gt;Reference Key for Line Item), BSEG-MENGE (-&gt; Quantity), BSEG-MEINS (-&gt; Base Unit of Measure), BSEG-EBELN, BSEG-EBELP, and BSEG-EBELN, BSEG-EBELP, BSEG-ZEKKN as well as the fields BSEG-FMXDOCNR, BSEG-FMXYEAR, BSEG-FMXDOCLN, and BSEG-FMXZEKKN must NOT be maintained or set for the reference transactions MKPF and RMRP. This field content is indispensable for the correct calculation of GR reduction values in Funds Management. Otherwise, there are incorrect FM actual postings, incorrect budget consumptions or update terminations.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-AP-AP-J (Integration/Accounting Interface)"}, {"Key": "Other Components", "Value": "FI-AR-AR-J (Integration/Accounting Interface)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D035054)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D035054)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "94868", "RefComponent": "FI-AR-AR-J", "RefTitle": "SD/FI: Document summarization for valuated materials", "RefUrl": "/notes/94868"}, {"RefNumber": "9150", "RefComponent": "SD-BIL-IL", "RefTitle": "Reversal of invoice list", "RefUrl": "/notes/9150"}, {"RefNumber": "861824", "RefComponent": "FI-GL-GL-F", "RefTitle": "Unwanted compression of tax table BSET", "RefUrl": "/notes/861824"}, {"RefNumber": "85982", "RefComponent": "FI-AR-AR", "RefTitle": "Summarizing the tax line items from SD", "RefUrl": "/notes/85982"}, {"RefNumber": "819188", "RefComponent": "IS-A-JIT", "RefTitle": "JIT: Accounting doc. cannot be created for ESP delivery.", "RefUrl": "/notes/819188"}, {"RefNumber": "79265", "RefComponent": "LE-SHP-GI", "RefTitle": "GI: Collective note for FI problems with GI", "RefUrl": "/notes/79265"}, {"RefNumber": "77161", "RefComponent": "FI-GL-GL-J", "RefTitle": "Summarization of postings in FI with purchase order reference", "RefUrl": "/notes/77161"}, {"RefNumber": "73907", "RefComponent": "FI-GL-GL-J", "RefTitle": "Special development: Summarization in FI dependent on document type", "RefUrl": "/notes/73907"}, {"RefNumber": "728740", "RefComponent": "IS-M-SD-PS-ST", "RefTitle": "IS-M/SD: FI/CO interface - FI document summarization", "RefUrl": "/notes/728740"}, {"RefNumber": "687873", "RefComponent": "MM-IV-LIV-CON", "RefTitle": "MRKO: Short dump during IDOC settlement", "RefUrl": "/notes/687873"}, {"RefNumber": "67640", "RefComponent": "FI-AP", "RefTitle": "Summarizing FI documents from MM does not work", "RefUrl": "/notes/67640"}, {"RefNumber": "645356", "RefComponent": "FI-AP-AP-J", "RefTitle": "No summarization of FI documents from MM due to BSEG-XREF3", "RefUrl": "/notes/645356"}, {"RefNumber": "643273", "RefComponent": "FI-GL-GL-F", "RefTitle": "BSET summarization for 'Determine taxes line-by-line'", "RefUrl": "/notes/643273"}, {"RefNumber": "571928", "RefComponent": "FI-GL-GL-F", "RefTitle": "Summarization of tax items with external tax system", "RefUrl": "/notes/571928"}, {"RefNumber": "558378", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/558378"}, {"RefNumber": "545137", "RefComponent": "FI-AR-AR-J", "RefTitle": "FAQ Interface - summarization", "RefUrl": "/notes/545137"}, {"RefNumber": "517768", "RefComponent": "PS-CAF-ACT", "RefTitle": "(FI) FI057 due to BUZEI in connectn w/ doc. split in FI (1)", "RefUrl": "/notes/517768"}, {"RefNumber": "497992", "RefComponent": "MM-IM-GF-REP", "RefTitle": "XXX MB5B : Composite SAP Note for Release >= 4.0B 05/2002", "RefUrl": "/notes/497992"}, {"RefNumber": "485236", "RefComponent": "PS-CAF-ACT", "RefTitle": "(SD) FI057 with 'Buzei in LFMPRF04'", "RefUrl": "/notes/485236"}, {"RefNumber": "46230", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/46230"}, {"RefNumber": "426100", "RefComponent": "PS-CAF-ACT", "RefTitle": "(CC) split processor and reference to clearing document line", "RefUrl": "/notes/426100"}, {"RefNumber": "42347", "RefComponent": "FI-GL-CP", "RefTitle": "GI182: System error in SAPLGIN3 during update", "RefUrl": "/notes/42347"}, {"RefNumber": "413418", "RefComponent": "FI-GL-GL-J", "RefTitle": "AC-INT: Summarization of FI documents using functional area", "RefUrl": "/notes/413418"}, {"RefNumber": "408681", "RefComponent": "PS-CAF-ACT", "RefTitle": "Follow-up posting: Long text FI057 no info on source documnt", "RefUrl": "/notes/408681"}, {"RefNumber": "406420", "RefComponent": "IS-M-SD-PS-BL", "RefTitle": "IS-M/SD: FI documents not summarized when transferring", "RefUrl": "/notes/406420"}, {"RefNumber": "402381", "RefComponent": "IS-M-SD-PS-BL", "RefTitle": "IS-M/SD: FI interface - FI document summarization", "RefUrl": "/notes/402381"}, {"RefNumber": "310837", "RefComponent": "FI-GL-GL-J", "RefTitle": "Simulation for document summarization in FI", "RefUrl": "/notes/310837"}, {"RefNumber": "309294", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/309294"}, {"RefNumber": "301077", "RefComponent": "SD-BIL-CA", "RefTitle": "User exits for the Accounting Interface", "RefUrl": "/notes/301077"}, {"RefNumber": "28292", "RefComponent": "MM-IV", "RefTitle": "MR01: Zero lines on GR/IR clearing account", "RefUrl": "/notes/28292"}, {"RefNumber": "213546", "RefComponent": "IS-R-IFC-IN", "RefTitle": "HPR collective note: POS inbound", "RefUrl": "/notes/213546"}, {"RefNumber": "207340", "RefComponent": "FI-GL-GL-J", "RefTitle": "Termination of program SAPLFACI", "RefUrl": "/notes/207340"}, {"RefNumber": "205438", "RefComponent": "CA-JVA", "RefTitle": "Note 36353 and JVA", "RefUrl": "/notes/205438"}, {"RefNumber": "201039", "RefComponent": "MM-IM-GF-REP", "RefTitle": "Missing accounting documents in MB5B and MR51", "RefUrl": "/notes/201039"}, {"RefNumber": "199467", "RefComponent": "CO-PA", "RefTitle": "Info: New act assignment table as of Release 4.5", "RefUrl": "/notes/199467"}, {"RefNumber": "178487", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/178487"}, {"RefNumber": "1779136", "RefComponent": "FI-GL-GL-J", "RefTitle": "Extended summarization of FI documents", "RefUrl": "/notes/1779136"}, {"RefNumber": "1672634", "RefComponent": "XX-CSC-IN-SD", "RefTitle": "Runtime error for Excise with more than 999 line items:J1IIN", "RefUrl": "/notes/1672634"}, {"RefNumber": "166880", "RefComponent": "FI-GL-GL-J", "RefTitle": "Incorrect tax company code in FI document", "RefUrl": "/notes/166880"}, {"RefNumber": "166487", "RefComponent": "FI", "RefTitle": "TABLE_INVALID_INDEX in FI/CO interface", "RefUrl": "/notes/166487"}, {"RefNumber": "1482786", "RefComponent": "FI-GL-GL-F", "RefTitle": "Using the enhanced BAdI BADI_TAX1_XTXIT_SET", "RefUrl": "/notes/1482786"}, {"RefNumber": "147766", "RefComponent": "CO-OM-CEL-E", "RefTitle": "Document summarization in CO available as of Release 4.0A", "RefUrl": "/notes/147766"}, {"RefNumber": "1477287", "RefComponent": "FI-GL-GL-F", "RefTitle": "BADI_TAX1_XTXIT_SET: Summarizing tax items", "RefUrl": "/notes/1477287"}, {"RefNumber": "144638", "RefComponent": "IS-M-AM-BL", "RefTitle": "M/AM - AC interface: FI document summarization", "RefUrl": "/notes/144638"}, {"RefNumber": "1443088", "RefComponent": "SD-BIL-CA", "RefTitle": "Transfer prices: Tax code prevents summarization", "RefUrl": "/notes/1443088"}, {"RefNumber": "1442868", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1442868"}, {"RefNumber": "1431727", "RefComponent": "FI-GL-GL-J", "RefTitle": "Accounting Interface: Separate summarization of tax data", "RefUrl": "/notes/1431727"}, {"RefNumber": "1423063", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1423063"}, {"RefNumber": "1421394", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1421394"}, {"RefNumber": "141899", "RefComponent": "SD-BIL-CA", "RefTitle": "Transfer price: lines with zero posting in FI document", "RefUrl": "/notes/141899"}, {"RefNumber": "1411253", "RefComponent": "SCM-EWM-IF-ERP-DLP", "RefTitle": "F5 727 occurs for goods movements from EWM", "RefUrl": "/notes/1411253"}, {"RefNumber": "1405674", "RefComponent": "FI-GL-GL-F", "RefTitle": "FF 793 in Indian company code", "RefUrl": "/notes/1405674"}, {"RefNumber": "1338729", "RefComponent": "FI-GL-GL-F", "RefTitle": "MIRO in Indian company code: Error messages FF", "RefUrl": "/notes/1338729"}, {"RefNumber": "1318938", "RefComponent": "AC-INT", "RefTitle": "BAPI: tax data in ACCTX for Accounting Interface", "RefUrl": "/notes/1318938"}, {"RefNumber": "1316014", "RefComponent": "FI-GL-GL-F", "RefTitle": "MIRO: BSEG-TXGRP incorrect for Indian company code", "RefUrl": "/notes/1316014"}, {"RefNumber": "130578", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/130578"}, {"RefNumber": "1283019", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1283019"}, {"RefNumber": "1268872", "RefComponent": "CA-EUR-CNV-FI", "RefTitle": "RGFLC1F: Incorrect balance for FI item summarization", "RefUrl": "/notes/1268872"}, {"RefNumber": "123223", "RefComponent": "LE-SHP-GI", "RefTitle": "Obsolete: GI: 'Maximum number of items in FI reached'", "RefUrl": "/notes/123223"}, {"RefNumber": "117708", "RefComponent": "MM-IV", "RefTitle": "A maximum of 999 items can be posted in the FI document", "RefUrl": "/notes/117708"}, {"RefNumber": "1176566", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1176566"}, {"RefNumber": "116523", "RefComponent": "PY-XX-DT", "RefTitle": "Q&A: How to customize Payroll Accounting postings in Rel.4.x", "RefUrl": "/notes/116523"}, {"RefNumber": "1158279", "RefComponent": "FI-GL-GL-F", "RefTitle": "Line-by-line tax: No tax items in clearing document", "RefUrl": "/notes/1158279"}, {"RefNumber": "108583", "RefComponent": "FI-AP-AP-J", "RefTitle": "RWIN: Document summarization for doc.s with MR1M", "RefUrl": "/notes/108583"}, {"RefNumber": "1024923", "RefComponent": "FI-GL-GL-F", "RefTitle": "India MIRO F5807 too many BSET entries despite summarization", "RefUrl": "/notes/1024923"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2416929", "RefComponent": "FI-GL-GL-J", "RefTitle": "GH067 in OBCY", "RefUrl": "/notes/2416929 "}, {"RefNumber": "2918650", "RefComponent": "FI-GL-GL-J", "RefTitle": "Over 999 line items and GR/IR clearing in S/4 HANA - Summarization of FI documents", "RefUrl": "/notes/2918650 "}, {"RefNumber": "1671055", "RefComponent": "MM-IV-LIV-CRE", "RefTitle": "MIRO: Error message FF755 - SAP ERP & SAP S/4HANA", "RefUrl": "/notes/1671055 "}, {"RefNumber": "1801529", "RefComponent": "FI-AP-AP-J", "RefTitle": "Error message F5727 \"Maximum number of items in FI reached\" when release MM document to accounting", "RefUrl": "/notes/1801529 "}, {"RefNumber": "2699107", "RefComponent": "FI-GL-GL-J", "RefTitle": "Summarization in New G/L", "RefUrl": "/notes/2699107 "}, {"RefNumber": "1906562", "RefComponent": "MM-IV-LIV-CRE", "RefTitle": "MIRO shows error message M7008 when posting an invoice with more than 9999 lines", "RefUrl": "/notes/1906562 "}, {"RefNumber": "1928465", "RefComponent": "MM-IM-GF-REP", "RefTitle": "Warning message M7390 in MB5B - (SAP ERP & SAP S/4 HANA)", "RefUrl": "/notes/1928465 "}, {"RefNumber": "2568457", "RefComponent": "FI-AP-AP-J", "RefTitle": "Error FC250 - Field name EBELN (EBELP) of table BSEG is not allowed", "RefUrl": "/notes/2568457 "}, {"RefNumber": "2200095", "RefComponent": "FI-GL-GL-A", "RefTitle": "Assignment ends with zero strings in F.13", "RefUrl": "/notes/2200095 "}, {"RefNumber": "3103680", "RefComponent": "FI-GL-GL-J", "RefTitle": "How to activate summarization of customer fields in BSEG", "RefUrl": "/notes/3103680 "}, {"RefNumber": "2665965", "RefComponent": "FI-AP-AP-J", "RefTitle": "Analysis of the document summarization in FI at posting runtime", "RefUrl": "/notes/2665965 "}, {"RefNumber": "2628963", "RefComponent": "FI-GL-GL-J", "RefTitle": "Large journal entries in the universal journal of S/4H", "RefUrl": "/notes/2628963 "}, {"RefNumber": "2337334", "RefComponent": "FI-GL-GL-J", "RefTitle": "Activating simple document summarization without report ZTTYPV or ZTTYPVX", "RefUrl": "/notes/2337334 "}, {"RefNumber": "2179270", "RefComponent": "FI-GL", "RefTitle": "S/4 HANA Finance or S/4 HANA OP: Document summarization in FI, CO, CO-PA, and profitability segment summarization", "RefUrl": "/notes/2179270 "}, {"RefNumber": "643273", "RefComponent": "FI-GL-GL-F", "RefTitle": "BSET summarization for 'Determine taxes line-by-line'", "RefUrl": "/notes/643273 "}, {"RefNumber": "77161", "RefComponent": "FI-GL-GL-J", "RefTitle": "Summarization of postings in FI with purchase order reference", "RefUrl": "/notes/77161 "}, {"RefNumber": "558378", "RefComponent": "SD-BIL-CA", "RefTitle": "Several FI documents per billing document", "RefUrl": "/notes/558378 "}, {"RefNumber": "1779136", "RefComponent": "FI-GL-GL-J", "RefTitle": "Extended summarization of FI documents", "RefUrl": "/notes/1779136 "}, {"RefNumber": "1443088", "RefComponent": "SD-BIL-CA", "RefTitle": "Transfer prices: Tax code prevents summarization", "RefUrl": "/notes/1443088 "}, {"RefNumber": "130578", "RefComponent": "SD-BIL-CA", "RefTitle": "Transfer invoiced quantity into accounting", "RefUrl": "/notes/130578 "}, {"RefNumber": "73907", "RefComponent": "FI-GL-GL-J", "RefTitle": "Special development: Summarization in FI dependent on document type", "RefUrl": "/notes/73907 "}, {"RefNumber": "1431727", "RefComponent": "FI-GL-GL-J", "RefTitle": "Accounting Interface: Separate summarization of tax data", "RefUrl": "/notes/1431727 "}, {"RefNumber": "116523", "RefComponent": "PY-XX-DT", "RefTitle": "Q&A: How to customize Payroll Accounting postings in Rel.4.x", "RefUrl": "/notes/116523 "}, {"RefNumber": "1482786", "RefComponent": "FI-GL-GL-F", "RefTitle": "Using the enhanced BAdI BADI_TAX1_XTXIT_SET", "RefUrl": "/notes/1482786 "}, {"RefNumber": "1421394", "RefComponent": "FI-GL-GL-F", "RefTitle": "BSET summarization without BSEG summarization required", "RefUrl": "/notes/1421394 "}, {"RefNumber": "1672634", "RefComponent": "XX-CSC-IN-SD", "RefTitle": "Runtime error for Excise with more than 999 line items:J1IIN", "RefUrl": "/notes/1672634 "}, {"RefNumber": "117708", "RefComponent": "MM-IV", "RefTitle": "A maximum of 999 items can be posted in the FI document", "RefUrl": "/notes/117708 "}, {"RefNumber": "310837", "RefComponent": "FI-GL-GL-J", "RefTitle": "Simulation for document summarization in FI", "RefUrl": "/notes/310837 "}, {"RefNumber": "1423063", "RefComponent": "FI-GL-GL-F", "RefTitle": "Tax Reporting country India: not always tax line-by-line", "RefUrl": "/notes/1423063 "}, {"RefNumber": "1158279", "RefComponent": "FI-GL-GL-F", "RefTitle": "Line-by-line tax: No tax items in clearing document", "RefUrl": "/notes/1158279 "}, {"RefNumber": "1477287", "RefComponent": "FI-GL-GL-F", "RefTitle": "BADI_TAX1_XTXIT_SET: Summarizing tax items", "RefUrl": "/notes/1477287 "}, {"RefNumber": "1176566", "RefComponent": "CO-PA", "RefTitle": "Too many FI documents because of different PAOBJNR", "RefUrl": "/notes/1176566 "}, {"RefNumber": "1471432", "RefComponent": "PSM-FM-UP-FI-GR", "RefTitle": "Restore Quantity in Accounting Interface for FM documents", "RefUrl": "/notes/1471432 "}, {"RefNumber": "1338729", "RefComponent": "FI-GL-GL-F", "RefTitle": "MIRO in Indian company code: Error messages FF", "RefUrl": "/notes/1338729 "}, {"RefNumber": "1316014", "RefComponent": "FI-GL-GL-F", "RefTitle": "MIRO: BSEG-TXGRP incorrect for Indian company code", "RefUrl": "/notes/1316014 "}, {"RefNumber": "497992", "RefComponent": "MM-IM-GF-REP", "RefTitle": "XXX MB5B : Composite SAP Note for Release >= 4.0B 05/2002", "RefUrl": "/notes/497992 "}, {"RefNumber": "748028", "RefComponent": "LE-TRA-FC-SET", "RefTitle": "Information VY 110: General error during transfer", "RefUrl": "/notes/748028 "}, {"RefNumber": "1442868", "RefComponent": "XX-PROJ-CDP-TEST-059", "RefTitle": "SETI: Compression does not work for FI documents", "RefUrl": "/notes/1442868 "}, {"RefNumber": "1405674", "RefComponent": "FI-GL-GL-F", "RefTitle": "FF 793 in Indian company code", "RefUrl": "/notes/1405674 "}, {"RefNumber": "1411253", "RefComponent": "SCM-EWM-IF-ERP-DLP", "RefTitle": "F5 727 occurs for goods movements from EWM", "RefUrl": "/notes/1411253 "}, {"RefNumber": "1318938", "RefComponent": "AC-INT", "RefTitle": "BAPI: tax data in ACCTX for Accounting Interface", "RefUrl": "/notes/1318938 "}, {"RefNumber": "1283019", "RefComponent": "FI-GL", "RefTitle": "FAQ Maximum and Overflow Line Items", "RefUrl": "/notes/1283019 "}, {"RefNumber": "1268872", "RefComponent": "CA-EUR-CNV-FI", "RefTitle": "RGFLC1F: Incorrect balance for FI item summarization", "RefUrl": "/notes/1268872 "}, {"RefNumber": "123223", "RefComponent": "LE-SHP-GI", "RefTitle": "Obsolete: GI: 'Maximum number of items in FI reached'", "RefUrl": "/notes/123223 "}, {"RefNumber": "1024923", "RefComponent": "FI-GL-GL-F", "RefTitle": "India MIRO F5807 too many BSET entries despite summarization", "RefUrl": "/notes/1024923 "}, {"RefNumber": "213546", "RefComponent": "IS-R-IFC-IN", "RefTitle": "HPR collective note: POS inbound", "RefUrl": "/notes/213546 "}, {"RefNumber": "301077", "RefComponent": "SD-BIL-CA", "RefTitle": "User exits for the Accounting Interface", "RefUrl": "/notes/301077 "}, {"RefNumber": "586163", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Composite SAP Note for SAP ERP Inventory Management in SAP BW", "RefUrl": "/notes/586163 "}, {"RefNumber": "861824", "RefComponent": "FI-GL-GL-F", "RefTitle": "Unwanted compression of tax table BSET", "RefUrl": "/notes/861824 "}, {"RefNumber": "199467", "RefComponent": "CO-PA", "RefTitle": "Info: New act assignment table as of Release 4.5", "RefUrl": "/notes/199467 "}, {"RefNumber": "819188", "RefComponent": "IS-A-JIT", "RefTitle": "JIT: Accounting doc. cannot be created for ESP delivery.", "RefUrl": "/notes/819188 "}, {"RefNumber": "571928", "RefComponent": "FI-GL-GL-F", "RefTitle": "Summarization of tax items with external tax system", "RefUrl": "/notes/571928 "}, {"RefNumber": "728740", "RefComponent": "IS-M-SD-PS-ST", "RefTitle": "IS-M/SD: FI/CO interface - FI document summarization", "RefUrl": "/notes/728740 "}, {"RefNumber": "687873", "RefComponent": "MM-IV-LIV-CON", "RefTitle": "MRKO: Short dump during IDOC settlement", "RefUrl": "/notes/687873 "}, {"RefNumber": "645356", "RefComponent": "FI-AP-AP-J", "RefTitle": "No summarization of FI documents from MM due to BSEG-XREF3", "RefUrl": "/notes/645356 "}, {"RefNumber": "517768", "RefComponent": "PS-CAF-ACT", "RefTitle": "(FI) FI057 due to BUZEI in connectn w/ doc. split in FI (1)", "RefUrl": "/notes/517768 "}, {"RefNumber": "141899", "RefComponent": "SD-BIL-CA", "RefTitle": "Transfer price: lines with zero posting in FI document", "RefUrl": "/notes/141899 "}, {"RefNumber": "545137", "RefComponent": "FI-AR-AR-J", "RefTitle": "FAQ Interface - summarization", "RefUrl": "/notes/545137 "}, {"RefNumber": "201039", "RefComponent": "MM-IM-GF-REP", "RefTitle": "Missing accounting documents in MB5B and MR51", "RefUrl": "/notes/201039 "}, {"RefNumber": "426100", "RefComponent": "PS-CAF-ACT", "RefTitle": "(CC) split processor and reference to clearing document line", "RefUrl": "/notes/426100 "}, {"RefNumber": "408681", "RefComponent": "PS-CAF-ACT", "RefTitle": "Follow-up posting: Long text FI057 no info on source documnt", "RefUrl": "/notes/408681 "}, {"RefNumber": "144638", "RefComponent": "IS-M-AM-BL", "RefTitle": "M/AM - AC interface: FI document summarization", "RefUrl": "/notes/144638 "}, {"RefNumber": "485236", "RefComponent": "PS-CAF-ACT", "RefTitle": "(SD) FI057 with 'Buzei in LFMPRF04'", "RefUrl": "/notes/485236 "}, {"RefNumber": "85982", "RefComponent": "FI-AR-AR", "RefTitle": "Summarizing the tax line items from SD", "RefUrl": "/notes/85982 "}, {"RefNumber": "9150", "RefComponent": "SD-BIL-IL", "RefTitle": "Reversal of invoice list", "RefUrl": "/notes/9150 "}, {"RefNumber": "147766", "RefComponent": "CO-OM-CEL-E", "RefTitle": "Document summarization in CO available as of Release 4.0A", "RefUrl": "/notes/147766 "}, {"RefNumber": "28292", "RefComponent": "MM-IV", "RefTitle": "MR01: Zero lines on GR/IR clearing account", "RefUrl": "/notes/28292 "}, {"RefNumber": "413418", "RefComponent": "FI-GL-GL-J", "RefTitle": "AC-INT: Summarization of FI documents using functional area", "RefUrl": "/notes/413418 "}, {"RefNumber": "166880", "RefComponent": "FI-GL-GL-J", "RefTitle": "Incorrect tax company code in FI document", "RefUrl": "/notes/166880 "}, {"RefNumber": "402381", "RefComponent": "IS-M-SD-PS-BL", "RefTitle": "IS-M/SD: FI interface - FI document summarization", "RefUrl": "/notes/402381 "}, {"RefNumber": "406420", "RefComponent": "IS-M-SD-PS-BL", "RefTitle": "IS-M/SD: FI documents not summarized when transferring", "RefUrl": "/notes/406420 "}, {"RefNumber": "205438", "RefComponent": "CA-JVA", "RefTitle": "Note 36353 and JVA", "RefUrl": "/notes/205438 "}, {"RefNumber": "309294", "RefComponent": "FI-GL-GL-J", "RefTitle": "Information on summerization of FI documents", "RefUrl": "/notes/309294 "}, {"RefNumber": "207340", "RefComponent": "FI-GL-GL-J", "RefTitle": "Termination of program SAPLFACI", "RefUrl": "/notes/207340 "}, {"RefNumber": "166487", "RefComponent": "FI", "RefTitle": "TABLE_INVALID_INDEX in FI/CO interface", "RefUrl": "/notes/166487 "}, {"RefNumber": "108583", "RefComponent": "FI-AP-AP-J", "RefTitle": "RWIN: Document summarization for doc.s with MR1M", "RefUrl": "/notes/108583 "}, {"RefNumber": "79265", "RefComponent": "LE-SHP-GI", "RefTitle": "GI: Collective note for FI problems with GI", "RefUrl": "/notes/79265 "}, {"RefNumber": "67640", "RefComponent": "FI-AP", "RefTitle": "Summarizing FI documents from MM does not work", "RefUrl": "/notes/67640 "}, {"RefNumber": "42347", "RefComponent": "FI-GL-CP", "RefTitle": "GI182: System error in SAPLGIN3 during update", "RefUrl": "/notes/42347 "}, {"RefNumber": "94868", "RefComponent": "FI-AR-AR-J", "RefTitle": "SD/FI: Document summarization for valuated materials", "RefUrl": "/notes/94868 "}, {"RefNumber": "46230", "RefComponent": "FI", "RefTitle": "Q&A - Document Summarization in rel 3.0", "RefUrl": "/notes/46230 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "300", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "616", "To": "616", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "618", "To": "618", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "720", "To": "720", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "106", "To": "106", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "107", "To": "107", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "108", "To": "108", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "40B", "To": "40B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B28", "URL": "/supportpackage/SAPKH40B28"}, {"SoftwareComponentVersion": "SAP_HR 40B", "SupportPackage": "SAPKE40B28", "URL": "/supportpackage/SAPKE40B28"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B09", "URL": "/supportpackage/SAPKH45B09"}, {"SoftwareComponentVersion": "SAP_APPL 46B", "SupportPackage": "SAPKH46B01", "URL": "/supportpackage/SAPKH46B01"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_FIN", "NumberOfCorrin": 1, "URL": "/corrins/**********/15841"}, {"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 1, "URL": "/corrins/**********/1"}, {"SoftwareComponent": "S4CORE", "NumberOfCorrin": 1, "URL": "/corrins/**********/19773"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}