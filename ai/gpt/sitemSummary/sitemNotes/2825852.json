{"Request": {"Number": "2825852", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 547, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000002311242019"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002825852?language=E&token=CF889B39A6940EC7A363708744C5E2A3"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002825852", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2825852"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.12.2019"}, "SAPComponentKey": {"_label": "Component", "value": "IS-ADEC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Ind.-Spec.Comp. Aerospace&Defense / Engineering&Construction"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Ind.-Spec.Comp. Aerospace&Defense / Engineering&Construction", "value": "IS-ADEC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-ADEC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2825852 - SAP S/4HANA 1909, Aerospace and Defense : Restriction Note"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are using SAP S/4HANA 1909. This SAP Note&#160;informs you about all restrictions in this release with regard to Aerospace and Defense&#160;solution.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Release restrictions, SAP S/4HANA 1909, Aerospace and Defense</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This SAP Note provides information about the restrictions that exist for SAP S/4HANA 1909 in the Aerospace and Defense area.</p>\r\n<p>Note: This SAP Note is subject to change.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ul>\r\n<li>(PDN) Product Designer Work Bench is no longer available and is replaced by Product Structure Management(PSM).</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">To use the A&amp;D functions of iPPE, please use transaction code PPE.&#160; PSM does not contain these features.</p>\r\n<ul>\r\n<li>Transaction MB11 is no longer available in S/4 HANA. Transaction&#160;MIGO replaces MB11.</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">MIGO does not support&#160;using Movement type MT711B and MT712B&#160;for Customer Stock load in SAP S/4HANA.</p>\r\n<p style=\"padding-left: 30px;\">Please use movement types MT501B and MT502B when&#160;using transaction&#160;MIGO.</p>\r\n<p style=\"padding-left: 30px;\">When performing inventory adjustments&#160;with Inventory Documents, MT711B and MT712B should still work within the background of the transaction code LI21.</p>\r\n<p style=\"padding-left: 30px;\"><span style=\"text-decoration: underline;\">Restrictions specific to GPD:</span></p>\r\n<ul>\r\n<li>Valuated GPD is restricted from being used in SAP S/4HANA.</li>\r\n<li>GPD does not support delivery costs (cost of transporting stock from one plant to another).</li>\r\n<li>Only requirements from network activities, production orders, sales orders and planned independent requirements are supported by GPD.&#160;</li>\r\n<li>By-products are not supported.</li>\r\n<li>Negative stock quantities are not supported.</li>\r\n<li>Model-Unit type of Parameter Effectivity (PE) is not supported by GPD.&#160; Recommend using an operative WBS Element in the Project for each model unit number to plan such Model-Unit requirements (tip: Model-Unit requirement for embedded PEO application in S/4 HANA) while the supporting non-Model-Unit parts in the product can still be planned using GPD with type 2 grouping. The very nature of Model-Unit PE affected part numbers are at the top of the Bill-of-Material tree. Other forms of Parameter Effectivity that involve master data (routings and bills of material) changes involves parts being deactivated/activated and are not dependent on other assembly levels (like Model-Unit PE) for MRP planning and PP processing which should be okay for GPD, as GPD processes are at the \"Source Order\" (purchase and make orders) levels.&#160; In general, GPD is blind to PE numbers but support MRP and PP processes where it can. Another tip; MRP does not support Model-Unit PE either, which is another reason for using an operative WBS Element for each Model-Unit number that will segregate planning so that each operative WBS Element is an MRP planning silo for each Model-Unit Number.</li>\r\n<li>Serial numbers in stock and goods movements are ignored.</li>\r\n<li>GPD is blind to MRP Areas.&#160; GPD plans the entire plant as if it is one Plant MRP Area segmented by WBS Element planning.</li>\r\n<li>Transfer Borrow Loan Payback (TBLP) process is not supported by GPD.&#160; It is meant for standard WBS material and cost processing.</li>\r\n<li>Production Order Split functionality does not support GPD.<br />GPD would need&#160;components and the productionorder item detail&#160;costs to move from the Parent Order to the Child Order.</li>\r\n<li>PP/DS does not support GPD.&#160; PP/DS has been moved to SAP S/4 HANA.</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\"><span style=\"text-decoration: underline;\">Restrictions specific to MRO Subcontracting:</span></p>\r\n<ul>\r\n<li>The Aerospace &amp; Defense MRO Subcontracting does not support EWM users&#160;with SAP S/4HANA.</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">The A&amp;D subcontracting orders do not create inbound delivery for the material number on the purchase order item with SAP S/4HANA.</p>\r\n<p style=\"padding-left: 30px;\">EWM depends on the Goods&#160;receipt of the Inbound Delivery as the Interface between SAP S/4HANA and EWM.</p>\r\n<p style=\"padding-left: 30px;\">We recommend using core Subcontracting process&#160;only with transaction code ME2O (Subcontracting Stock Monitoring for Supplier) when using EWM.&#160;Do not use transaction code ADSUBCON with EWM for creating subcontracting orders.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON> (I041991)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I303690)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002825852/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002825852/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002825852/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002825852/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002825852/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002825852/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002825852/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002825852/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002825852/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2799003", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1909: Restriction Note", "RefUrl": "/notes/2799003"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2933435", "RefComponent": "IS-ADEC-GPD", "RefTitle": "S4TWL - Grouping Pegging Distribution (GPD) in SAP S/4HANA", "RefUrl": "/notes/2933435 "}, {"RefNumber": "2590604", "RefComponent": "IS-ADEC-SUB", "RefTitle": "FAQ: Subcontracting for MRO processes / A&D subcontracting", "RefUrl": "/notes/2590604 "}, {"RefNumber": "2799003", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1909: Restriction Note", "RefUrl": "/notes/2799003 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}