{"Request": {"Number": "1160520", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 420, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016498552017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001160520?language=E&token=B6DC1835C42ABD1BD0F2DC9ACCD4F90E"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001160520", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001160520/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1160520"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.04.2008"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-OT-BIA"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW Accelerator"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Explorer", "value": "BW-BEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "OLAP Technology", "value": "BW-BEX-OT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW Accelerator", "value": "BW-BEX-OT-BIA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT-BIA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1160520 - BIA and non-cumulative queries"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p><br />Behaviour of BI with BIA for non-cumulative queries<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p><br />non-cumulatives BIA BI stock values query performance TREX NCUM<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br />For a general description of non-cumulative calculation see the documentation:<br />http://help.sap.com/saphelp_nw04s/helpdata/en/80/1a62dee07211d2acb80000e829fbfe/frameset.htm<br />Also the common performance tips for non-cumulative queries are also valied for the BIA:<br />http://help.sap.com/saphelp_nw04s/helpdata/en/20/ea58fd956c9c4f825f0537e5481c35/frameset.htm<br /><br />If the infocube data is stored within a BIA, the retrieval of stock values and retrieval of stock changes is speed up using the BIA. The computation of the absolute stock values however is not speed up, as this takes place in the BI analytics engine.<br /><br />In the aggregation manager query statistics the following can be seen:<br />The BIA needs one access marked with \"Type of Data Read Access\" = 'N' (non-cumulative) to determine the latest stock values, and one access to retrieve the stock changes.<br /><br />With the classical implementation in general 2 database accesses are needed for the latest stock values with Type of Data Read Access = 'N', one of them for the F and one for the E fact table as reflected in the column table type; similarly 2 accesses are needed for the stock changes with table type 'F' and 'E'.<br /><br /><U>BIA and validity table</U><br />Non-cumulative queries always take into account the validity table. As with the non-cumulative processing on the database for each combination of values of the validity-determining characteristics stock values and stock changes are determined separately. Therefore it is important to use as few validity-determining characteristics as possible.<br /><br /><U>BIA and cube compression</U><br />Regular compression of data is very important with non-cumulative infocubes to keep the amount of data low, that needs to be read into the analytic engine. Compression of data packages that are already rolled up into the BIA does not affect the BIA data and therefore not change the BIA query performance.<br />In order to make use of the compressed data, non-cumulative infocubes in BIA should be regularly rebuilt completely after data packages have been compressed.<br />If you are unsure, how often this is neccessary, you can perform the following performance measurement to see how this impacts query performance with your specific data.</p> <OL>1. For one of your infocubes check, how many data packages have been loaded and compressed since the last time the BIA index has been rebuilt.</OL> <OL>2. Run some non-cumulative queries for that infocube on the BIA and measure their performance. Important is the read time in the aggregation layer statistics.</OL> <OL>3. Rebuild the BIA index for that infocube</OL> <OL>4. Again measure the performance of the non-cumulative queries.</OL> <OL>5. Now you can see the relationship between the amount of uncompressed data in the BIA and the performance degradation this causes for non-cumulative queries. The bigger the improvement is with compressed data in the BIA index, the more often you should rebuild the BIA index for non-cumulative infocubes.</OL> <p><br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BEX-OT-NC (Non cumulative value)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D038032)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D038032)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001160520/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001160520/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001160520/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001160520/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001160520/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001160520/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001160520/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001160520/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001160520/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "957171", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Incorrect data for non-cumulatives and BIA", "RefUrl": "/notes/957171"}, {"RefNumber": "924757", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Stocks and BIA - repairs", "RefUrl": "/notes/924757"}, {"RefNumber": "1548125", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Interesting Facts about Non-Cumulatives", "RefUrl": "/notes/1548125"}, {"RefNumber": "1159305", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Wrong Data with BIA packagewise read and non-cumulatives", "RefUrl": "/notes/1159305"}, {"RefNumber": "1082468", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Error with RSRV BIA sums check with non-cumulative key figs", "RefUrl": "/notes/1082468"}, {"RefNumber": "1038181", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "NOHPA flag for RSRREPDIR table ignored for non-cumulatives", "RefUrl": "/notes/1038181"}, {"RefNumber": "1012974", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "wrong data in non-cumulative query with RKFs and BIA", "RefUrl": "/notes/1012974"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1548125", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Interesting Facts about Non-Cumulatives", "RefUrl": "/notes/1548125 "}, {"RefNumber": "1038181", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "NOHPA flag for RSRREPDIR table ignored for non-cumulatives", "RefUrl": "/notes/1038181 "}, {"RefNumber": "1159305", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Wrong Data with BIA packagewise read and non-cumulatives", "RefUrl": "/notes/1159305 "}, {"RefNumber": "1082468", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Error with RSRV BIA sums check with non-cumulative key figs", "RefUrl": "/notes/1082468 "}, {"RefNumber": "1012974", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "wrong data in non-cumulative query with RKFs and BIA", "RefUrl": "/notes/1012974 "}, {"RefNumber": "924757", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Stocks and BIA - repairs", "RefUrl": "/notes/924757 "}, {"RefNumber": "957171", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Incorrect data for non-cumulatives and BIA", "RefUrl": "/notes/957171 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "700", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}