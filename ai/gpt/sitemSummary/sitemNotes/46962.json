{"Request": {"Number": "46962", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 145, "Error": {}, "SAPNote": {"_type": "00200720420000000131", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000046962?language=E&token=39D00C155DB00335FE4E0E11E0724D6D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000046962", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000046962/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "46962"}, "Type": {"_label": "Type", "value": "SAP Standard Note"}, "Version": {"_label": "Version", "value": 18}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Checklist"}, "Priority": {"_label": "Priority", "value": "Normal"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.04.2023"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-GEN"}, "SAPComponentKeyText": {"_label": "Component", "value": "Please use note 1433157 for finding the right component"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "46962 - More information required"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Long Text\">Long Text</h3>\r\n<p><strong>Symptom</strong></p>\r\n<p>In order to solve your problem, the SAP Support Organisation requires<br />more information.</p>\r\n<p><strong>Other Terms</strong></p>\r\n<p>priority, basic info, support package, short dump, remote connection,<br />problem scenario, fax, sapserv, add-on, kernel patch</p>\r\n\r\n<p><strong>Solution</strong></p>\r\n<p>Dear customer,<br /><br />We kindly request that you carefully read the following guideline,<br />and provide us with the extra information we require to solve your<br />problem.</p>\r\n<ol>1. PRIORITY</ol>\r\n<p><br />Please use the correct priority for your case.&#160;&#160; All priorities are<br />described in note 67739.&#160;&#160;If you have a good reason for setting<br />a higher priority, make sure to clarify this in the case.</p>\r\n<ol>2. APPLIED NOTES</ol>\r\n<p><br />We assume that you have already searched for notes yourself.<br />Inform us of the notes you have already found and tried out, to avoid<br />that we suggest the same notes to you.<br /><br />How to perform a more precise note search? See notes 29501 and 94569.</p>\r\n<ol>3. BASIC INFO</ol>\r\n<p><br />Please provide us with :<br />- the correct component (application area)<br />- the transaction(s) for which the problem occurs<br />- the program name and screen number<br />- the error&#160;case number and text<br />- a description of the problem<br />- if you have a short dump, please also see &#167; 5.<br /><br />Ensure that you select the right component and, preferably,<br />subcomponent. As every support engineer at SAP works on his/her<br />specific domain, a correct (sub-)component will speed up the solving<br />process.<br /><br />Most of the above requested info can be found via the menu path<br />\"System -&gt; Status...\"&#160;&#160;From this screen you can click [further kernel<br />info] for more technical info (rel. &gt;3.0Z).<br /><br />The error message appears on the status bar (or pop-up).&#160;&#160;A double-<br />click on the message (or [help]) will show you the&#160;case number and<br />the message long text.</p>\r\n<ol>4. SUPPORT PACKAGE</ol>\r\n<p><br />Please inform us of your Support Package level.<br /><br />You can find your current level via the menu path \"System -&gt; Status...\"<br />- rel. &lt;3.1A, the window \"patch\" displays the hotpackage level<br />- 3.1A&lt;= rel. &lt;4.6A, \"hotpackage\" displays the type and name (=level)<br />- 4.6A&lt;= rel. &lt;6.10, click [component information], click tab<br />&#160;&#160; ]patches[ or ]SupPack[ to view the patch type(s) and its level(s).<br />- rel. &gt;=6.10, click [component information] to view the type(s) and<br />&#160;&#160; level(s).<br /><br />Note 97621 explains the differences between the Support Package types<br />over the releases.</p>\r\n<ol>5. SHORT DUMP</ol>\r\n<p><br />When reporting a problem based on a short dump, please ALWAYS provide<br />us with the short dump.<br /><br />Procedure :<br />- use transaction ST22 to find back the short dump<br />- displaying the dump pages, use the menu path \"System -&gt; List -&gt;<br />&#160;&#160; Save -&gt; Local file\"<br />- save \"unconverted\", as a .TXT-file to your front-end PC<br />- edit the .TXT-file with a TXT-editor (e.g. MS Notepad), delete<br />&#160;&#160; everything below the paragraph \"Directory of data areas\" and then<br />&#160;&#160; save the file again. If the mentioned paragraph cannot be found then<br />&#160;&#160; search for the paragraph \"ABAP control blocks\" or, if this one also<br />&#160;&#160; cannot be found, then after the 250th line.<br /><br />Now, you can :<br />- in the SN-R/3-Frontend (OSS), use the [upload]-button to import the<br />&#160;&#160; local file in the long text of the case.<br />- in the Service Marketplace, use the \"attachment\"-link on the long<br />&#160;&#160; text screen.<br />- use ftp to upload the file to sapservX - see note 40024, but please<br />&#160;&#160; inform us of the filename(s) in the long text of the case.</p>\r\n<ol>6. REMOTE CONNECTION</ol>\r\n<p><br />Please provide us with connection data to your system.&#160;&#160;We want to<br />reproduce the problem on a non-productive system (or client).&#160;&#160;Provide<br />us with connection data to a productive system only if the problem<br />cannot be reproduced on non-productive systems, and mention clearly in<br />your&#160;case that it is a productive system.<br /><br />Note 508140 explains how to add the logon information in a SECURE way<br />to the case.&#160;&#160;For security reasons logon info should not be placed<br />in the open text of the case.<br /><br />Open the connection only on request.&#160;&#160;Provide us with the name and<br />phone number of the person we should call for a connection.</p>\r\n<ol>7. SCENARIO</ol>\r\n<p><br />We need to be able to reproduce the problem exactly the way you have<br />it - on your system, or on one of our testsystems.<br /><br />Therefore, we need a complete scenario.&#160;&#160;Inform us of :<br />- the transaction or program you started<br />- the data you entered in the different fields<br />- the buttons you clicked<br />- the menu path you followed<br />- the keys you hit</p>\r\n<ol>8. ADDING PROBLEM DOCUMENTATION</ol>\r\n<p><br />Provide us with all useful information on your problem.<br /><br />You can ftp all printouts, screen copies, large log files, etc. to<br />sapservX.&#160;&#160;See note 40024 for details, but do not forget to inform us<br />of the filename(s).<br /><br />In the Service Marketplace, files can be attached to the&#160;case by<br />making use of the \"attachment\"-link on the long text screen.<br /><br />Alternatively, you can send us printouts or screen copies via fax.<br />Always mention the&#160;case number as subject of the fax.&#160;&#160;You can find<br />our fax number in note 38373.<br /><br />Useful transactions for logs:<br />&#160;&#160;SM12 - Display and delete locks (locking problems)<br />&#160;&#160;SM13 - Display update records (failed updates)<br />&#160;&#160;SM37 - Background job overview (job logs)<br />&#160;&#160;ST22 - ABAP/4 runtime error analysis (short dumps)<br />&#160;&#160;SM21 - System log<br />&#160;&#160;AL11 - Display SAP directories (all trace files)<br />&#160;&#160;ST11 - Display developer traces (dev_w* trace)</p>\r\n<ol>9. ADD-ONS</ol>\r\n<p><br />Inform us of your Add-On level (if applicable).&#160;&#160;Check the contents of<br />table AVERS with transaction SE16 for the complete list of installed<br />add-on packages.</p>\r\n<ol>10. KERNEL PATCHES</ol>\r\n<p><br />Inform us of your kernel release and kernel patch level.<br /><br />You can find your kernel release and level via the menu path<br />\"System -&gt; Status...\"&#160;&#160;From this screen you must click [further kernel<br />info] (rel. &gt;3.0Z).<br /><br /><br />HELP YOURSELF :<br />If something in this note is not clear, send us a separate message on<br />component XX-SER-GEN explaining your issue.&#160;&#160;The Support Organisation<br />can contact the responsible of this note for improving it.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D026744)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000046962/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000046962/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000046962/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000046962/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000046962/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000046962/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000046962/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000046962/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000046962/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "97621", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS Info: Online Correction Support (OCS)", "RefUrl": "/notes/97621"}, {"RefNumber": "919850", "RefComponent": "BW-PLA-IP", "RefTitle": "Problems with the planning modeler and the planning wizard", "RefUrl": "/notes/919850"}, {"RefNumber": "40024", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/40024"}, {"RefNumber": "29501", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/29501"}, {"RefNumber": "67739", "RefComponent": "XX-SER-GEN", "RefTitle": "Priority of problem cases", "RefUrl": "/notes/67739"}, {"RefNumber": "508140", "RefComponent": "XX-SER-SAPSMP-SUP", "RefTitle": "Customer incident - Customer logon data", "RefUrl": "/notes/508140"}, {"RefNumber": "38373", "RefComponent": "XX-SER-GEN", "RefTitle": "Support Center: Phone/fax numbers", "RefUrl": "/notes/38373"}, {"RefNumber": "16018", "RefComponent": "XX-SER-GEN", "RefTitle": "More information required on reported incident", "RefUrl": "/notes/16018"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}, "Validity": {"_label": "Products", "_columnNames": {"Product": "Products"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}