{"Request": {"Number": "67014", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 367, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014490612017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000067014?language=E&token=8CA881EE75CEC62A1DF2914E3CE56A9A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000067014", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000067014/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "67014"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 16}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.11.2008"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CST-UP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Update"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Client/Server Technology", "value": "BC-CST", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CST*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Update", "value": "BC-CST-UP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CST-UP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "67014 - Reorganizing update requests"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>There are records in the update tables VBMOD and VBDATA even though transaction SM13 does not display any update requests.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SM13, update, update request, VB table, VBHDR, VBERROR<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Some SAP transactions use constructs that result in entries in the update tables VBMOD and VBDATA before the final screen change (call dialog). If the transaction is then cancelled, these records \"hang\" as an incomplete update request. They are not displayed in transaction SM13 \"Update Requests: Initial Screen\" and cannot be executed either and thus only consume memory space unnecessarily. When you start up the update server instances, they are deleted by a reorganization run. However, the records may accumulate because the update servers are started very rarely (depending on the customer situation).<br />It has been shown that reorganizing update tables in the current production operation occasionally caused data inconsistencies. As a result, the function was changed using the specified Support Packages in such a way that reorganization only occurs after the user confirms a warning dialog box. Reorganization is stopped completely in the background and a syslog message is displayed if you try to start it again:<br />\"Reorganizing update data in the background is prohibited\".<br />Unfortunately, the reorganization is started automatically by the system each time a server is restarted, which causes this type of syslog, connected with an update termination and a short dump, during every restart. Because this reorganization is started from the kernel, it can only be prevented by a kernel patch.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The corrections are contained in the following kernel patches:<br />4.6D: 1263<br />6.20:&#x00A0;&#x00A0;254<br />Until then, either ignore the syslog messages and the update termination including the short dump or set the profile parameter rdisp/vbreorg to the value 0. Then the reorganization is not triggered during server start-up.<br /><br />You can delete incomplete update requests using the program RSM13002. For this purpose, set the parameter REORG to 'X'. Note that incomplete update requests are nearly always temporarily created during productive operation. These are also detected and deleted here. Data loss may occur as a result. Therefore only execute the program if you are certain that no productive actions are running in the system!<br /><br />Entries cannot exist in the update tables when you upgrade to a higher release. If the aforementioned measures fail, you may need to clean the tables VBHDR and VBERROR using database tools.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "SM13"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D000830)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D000830)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000067014/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000067014/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000067014/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000067014/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000067014/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000067014/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000067014/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000067014/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000067014/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "762951", "RefComponent": "BW-BCT-SD-SLS", "RefTitle": "Organization of structure changes in the LBWE", "RefUrl": "/notes/762951"}, {"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478"}, {"RefNumber": "652310", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "LBWE: Message MCEX 140 with extract structure change", "RefUrl": "/notes/652310"}, {"RefNumber": "571592", "RefComponent": "BW-BCT-SD-SLS", "RefTitle": "XPRA RMCSBWXP_SD_2 terminates during upgrade to PI 2002.1", "RefUrl": "/notes/571592"}, {"RefNumber": "542521", "RefComponent": "BC-CST", "RefTitle": "CST Patch Collection 31 2002", "RefUrl": "/notes/542521"}, {"RefNumber": "396647", "RefComponent": "BW-BCT", "RefTitle": "FAQ: V3 update: Questions and answers", "RefUrl": "/notes/396647"}, {"RefNumber": "1597364", "RefComponent": "BW-BCT-GEN", "RefTitle": "FAQ: BW-BCT: Extraction performance in source system", "RefUrl": "/notes/1597364"}, {"RefNumber": "1429935", "RefComponent": "BC-I18-UNI", "RefTitle": "Report UMG_CHECK_APPLICATION_TABS", "RefUrl": "/notes/1429935"}, {"RefNumber": "1146273", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1146273"}, {"RefNumber": "1083709", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "Error when importing support packages", "RefUrl": "/notes/1083709"}, {"RefNumber": "1081287", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "Data extraction orders are blocking the upgrade process", "RefUrl": "/notes/1081287"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2492899", "RefComponent": "BC-CST-UP", "RefTitle": "RAISE_EXCEPTION or BAD_CONFIGURATION in RSM13000 or RSM13002", "RefUrl": "/notes/2492899 "}, {"RefNumber": "2584092", "RefComponent": "BC-UPG-OCS-SPA", "RefTitle": "\"Open v3 update requests\" is reported in CHECK_REQUIREMENTS in SPAM/SAINT", "RefUrl": "/notes/2584092 "}, {"RefNumber": "2783516", "RefComponent": "BC-CST-UP", "RefTitle": "UPD: changes to RSM13002 as of SAP_BASIS 740", "RefUrl": "/notes/2783516 "}, {"RefNumber": "2773024", "RefComponent": "BC-ABA-SC", "RefTitle": "Batch input: Release not possible; an update request is still present for session &", "RefUrl": "/notes/2773024 "}, {"RefNumber": "1429935", "RefComponent": "BC-I18-UNI", "RefTitle": "Report UMG_CHECK_APPLICATION_TABS", "RefUrl": "/notes/1429935 "}, {"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478 "}, {"RefNumber": "1597364", "RefComponent": "BW-BCT-GEN", "RefTitle": "FAQ: BW-BCT: Extraction performance in source system", "RefUrl": "/notes/1597364 "}, {"RefNumber": "1083709", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "Error when importing support packages", "RefUrl": "/notes/1083709 "}, {"RefNumber": "1081287", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "Data extraction orders are blocking the upgrade process", "RefUrl": "/notes/1081287 "}, {"RefNumber": "542521", "RefComponent": "BC-CST", "RefTitle": "CST Patch Collection 31 2002", "RefUrl": "/notes/542521 "}, {"RefNumber": "652310", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "LBWE: Message MCEX 140 with extract structure change", "RefUrl": "/notes/652310 "}, {"RefNumber": "762951", "RefComponent": "BW-BCT-SD-SLS", "RefTitle": "Organization of structure changes in the LBWE", "RefUrl": "/notes/762951 "}, {"RefNumber": "396647", "RefComponent": "BW-BCT", "RefTitle": "FAQ: V3 update: Questions and answers", "RefUrl": "/notes/396647 "}, {"RefNumber": "571592", "RefComponent": "BW-BCT-SD-SLS", "RefTitle": "XPRA RMCSBWXP_SD_2 terminates during upgrade to PI 2002.1", "RefUrl": "/notes/571592 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "300", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46A", "To": "46D", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "620", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "72L", "To": "72L", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 31I", "SupportPackage": "SAPKH31IA6", "URL": "/supportpackage/SAPKH31IA6"}, {"SoftwareComponentVersion": "SAP_APPL 31I", "SupportPackage": "SAPKH31IA5", "URL": "/supportpackage/SAPKH31IA5"}, {"SoftwareComponentVersion": "SAP_HR 31I", "SupportPackage": "SAPKE31IA5", "URL": "/supportpackage/SAPKE31IA5"}, {"SoftwareComponentVersion": "SAP_HR 31I", "SupportPackage": "SAPKE31IA6", "URL": "/supportpackage/SAPKE31IA6"}, {"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B77", "URL": "/supportpackage/SAPKH40B77"}, {"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B76", "URL": "/supportpackage/SAPKH40B76"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B55", "URL": "/supportpackage/SAPKH45B55"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B54", "URL": "/supportpackage/SAPKH45B54"}, {"SoftwareComponentVersion": "SAP_BASIS 46B", "SupportPackage": "SAPKB46B42", "URL": "/supportpackage/SAPKB46B42"}, {"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C33", "URL": "/supportpackage/SAPKB46C33"}, {"SoftwareComponentVersion": "SAP_BASIS 46D", "SupportPackage": "SAPKB46D22", "URL": "/supportpackage/SAPKB46D22"}, {"SoftwareComponentVersion": "SAP_BASIS 610", "SupportPackage": "SAPKB61020", "URL": "/supportpackage/SAPKB61020"}, {"SoftwareComponentVersion": "SAP_BASIS 610", "SupportPackage": "SAPKB61021", "URL": "/supportpackage/SAPKB61021"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62004", "URL": "/supportpackage/SAPKB62004"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}