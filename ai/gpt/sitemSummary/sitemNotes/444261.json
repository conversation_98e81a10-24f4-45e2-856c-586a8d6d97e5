{"Request": {"Number": "444261", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 315, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015386352017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000444261?language=E&token=07FE1FE0E2A4AD728B3040D9A1A7F5A1"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000444261", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000444261/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "444261"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "08.10.2004"}, "SAPComponentKey": {"_label": "Component", "value": "BC-BW"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW Service API"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW Service API", "value": "BC-BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "444261 - <PERSON><PERSON> DeltaQueue: No data is extracted"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The DeltaQueue monitor (Transaction RSA7) indicates that data exists for a DataSource in the queue. Despite this, a delta extraction to the BW does not supply any records and the detailed view in the DeltaQueue monitor neither displays records for a delta update nor for duplicating a delta record.<br />As of SAPI 3.0A patch 6 (that corresponds to PlugIn 2001.2 Support Package 1), the error message RSQU 031 with the short text 'Error when reading from the qRFC queue: QSTATE = WAITUPDA' is issued during extraction from the qRFC queue.<br />As another diagnostic option, you can use the data browser (Transaction SE16) for the qRFC queue in question (QNAME field) and the affected BW system (DEST field) to check whether there are records with the value 'U' for the NOSEND field.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>OLTP, extractor, data extraction, DataSource, Service API, SAPI, S-API, 'No new Data since the last delta update' (RSM2 046), RSQU 31, <PERSON><PERSON><PERSON>31, <PERSON>A7, <PERSON><PERSON></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Cause:<br />Data was written directly to the DeltaQueue in an interactive or background process and update tasks are simultaneously started that are canceled or not yet updated completely. The affected qRFC-LUW locks the delta queue.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>If it is a DataSource that uses the extraction queues of the Logistic cockpit, check whether the BW DeltaQueue or the MCEXnn extraction queue (nn as the number of the application) are affected. With the extraction queue, that is, with a hanging MCEXnn queue, proceed according to Note 728687.<br />Otherwise, the following applies:<br />To prevent the problem from occurring in future, the corresponding application must respond and shift the writing to the DeltaQueue of the dialog task in the update task (see related notes).<br />You have four ways of continuing the delta process after the problem has occurred:</p> <OL>1. Delete the entire qRFC queue using Transaction SMQ1 (see question 12 in Note 380078 for the derivation of the queue name from the name of the DataSource) and initialize the delta extraction again from BW.</OL><OL>2. Try to update the update requests in question in the update monitor (Transaction SM13). If this is successful, you can then request a delta request. If it does not work for all LUWs, you must decide on either alternative 3 or 4.</OL> <OL>3. Delete the LUWs concerned in the qRFC-monitor if you do not want to load the LUWs to the BW and then request a new delta request.</OL> <OL>4. In the TRFCQOUT table, set the status of the LUWs from NOSEND = 'U' to NOSEND = 'X' (for example, with an ABAP program), if you still want to load the LUW to BW despite the update termination, and then request a new delta request. First check whether the canceled update requests do not appear again in the queue later, as otherwise double records will appear in BW.</OL> <p>To find the LUWs concerned in the qRFC queue, call the data browser (Transaction SE16) for the TRFCQOUT table and use the queue name (QNAME field) to select the RFC destination of the BW (DEST field) and the value U for the NOSEND field.<br /><br />We recommend alternative 1 as the simplest and most effective solution.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-WHM-SAPI (Please use Component BC-BW)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D003286)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D030494)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000444261/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000444261/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000444261/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000444261/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000444261/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000444261/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000444261/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000444261/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000444261/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "728687", "RefComponent": "BW-BCT", "RefTitle": "Delta queued: No data in RSA7", "RefUrl": "/notes/728687"}, {"RefNumber": "480145", "RefComponent": "BW-BCT-PP-PP", "RefTitle": "BW extraction call for application 04 in the background", "RefUrl": "/notes/480145"}, {"RefNumber": "448489", "RefComponent": "SRM-BW-EBP", "RefTitle": "EBP 20b-30: Calling RSC1_TRFC_QUEUE_WRITE in update task", "RefUrl": "/notes/448489"}, {"RefNumber": "440822", "RefComponent": "BC-BW", "RefTitle": "Composite SAP note BW-SAPI 30A Patch 6", "RefUrl": "/notes/440822"}, {"RefNumber": "417189", "RefComponent": "MM-PUR", "RefTitle": "BW/SAPLEINS - Online update of delta queue", "RefUrl": "/notes/417189"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "448489", "RefComponent": "SRM-BW-EBP", "RefTitle": "EBP 20b-30: Calling RSC1_TRFC_QUEUE_WRITE in update task", "RefUrl": "/notes/448489 "}, {"RefNumber": "586163", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Composite SAP Note for SAP ERP Inventory Management in SAP BW", "RefUrl": "/notes/586163 "}, {"RefNumber": "728687", "RefComponent": "BW-BCT", "RefTitle": "Delta queued: No data in RSA7", "RefUrl": "/notes/728687 "}, {"RefNumber": "710066", "RefComponent": "BW-BCT-FS-TB", "RefTitle": "Missing update modules for the delta queue", "RefUrl": "/notes/710066 "}, {"RefNumber": "599429", "RefComponent": "BW-BCT-LE-TRA", "RefTitle": "Application 08: Writing DeltaQueue in the background", "RefUrl": "/notes/599429 "}, {"RefNumber": "480145", "RefComponent": "BW-BCT-PP-PP", "RefTitle": "BW extraction call for application 04 in the background", "RefUrl": "/notes/480145 "}, {"RefNumber": "440822", "RefComponent": "BC-BW", "RefTitle": "Composite SAP note BW-SAPI 30A Patch 6", "RefUrl": "/notes/440822 "}, {"RefNumber": "417189", "RefComponent": "MM-PUR", "RefTitle": "BW/SAPLEINS - Online update of delta queue", "RefUrl": "/notes/417189 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BW-SAPI", "From": "30A_40B", "To": "30A_40B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "BW-SAPI 30A_40B", "SupportPackage": "JC4KS30A06", "URL": "/supportpackage/JC4KS30A06"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}