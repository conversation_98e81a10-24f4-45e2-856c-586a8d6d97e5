{"Request": {"Number": "774539", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 373, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015761142017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000774539?language=E&token=88059444D257DB91BBF019CCDF554FC4"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000774539", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000774539/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "774539"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Customizing"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.05.2006"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-CZ-IS-U"}, "SAPComponentKeyText": {"_label": "Component", "value": "use FI-LOC-UT-CZ"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Czech Republic", "value": "XX-CSC-CZ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CZ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-Spec. Component", "value": "XX-CSC-CZ-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CZ-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "use FI-LOC-UT-CZ", "value": "XX-CSC-CZ-IS-U", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CZ-IS-U*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "774539 - Budget Billing for IC and Weighting Key"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>IS-U/CCS local enhancements - country specific functionality for Czech Republic, Slovakia, Russia and Ukraine, installation and documentation.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Weighting key, Budget Billing Plan, Industrial Customers, WK, BBIC, /SAPCE/IU_WKBB, /SAPCE/IU_WKBBD, /SAPCE/IU_WKBBT, /SAPCE/IUWK, KZABSVER</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You have to install add-on CEEISUT rel.4.72. It contains IS-U/CCS and IS-T localization for CEE countries.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Note:</p> <UL><LI>These local enhancements are part of the Czech, Slovak, Russian and Ukrainian country version of IS-U/CCS delivered in add-on CEEISUT</LI></UL> <UL><LI>Local supports in particular countries are responsible for maintenance and hotline support of those enhancements</LI></UL> <UL><LI>These enhancements are not released for any other country.<br /></LI></UL> <p>************************************************************************</p> <OL>1. Overview of enhancements</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Budget billing plan for industrial customers (BBIC)<br /><br />For the industrial customers there is a request for specific procedure for determination of the budget billing plan (BBP) with following basic characteristics: <UL><UL><LI>From one to three (none to five) budget bills for one billing period,</LI></UL></UL> <UL><UL><LI>The \"fixed\" due dates (parameters) of budget bills (on the 5th, 15th, 25th day of the month, calendar of the work days is taken into account) or individually negotiable dates,</LI></UL></UL> <UL><UL><LI>Generation of BBP for the after next billing period,</LI></UL></UL> <UL><UL><LI>Static principle or dynamic principle of determination of BBP,</LI></UL></UL> <UL><UL><LI>The budget bills (BB) generated are statistical (statistical procedure of BB) without tax,</LI></UL></UL> <UL><UL><LI>Billing period is 1 month.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This funcionality is delivered in following function modules: <UL><UL><LI>/SAPCE/IU_EVENT_R401 (Selection of CA items for account maintainance),</LI></UL></UL> <UL><UL><LI>/SAPCE/IU_EVENT_R402 (Generation of BB requests for CZ/SK),<br />/SAPCE/IU_R402_BBIC_NEXTPER (Generation of BB requests for RU) or<br />SAPCE/IUUA_R402_BBIC (Generation of BB requests for UA)</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In order to meet these requirements new subscreen on Payments/Taxes tab of contract account maintenance is added, where user can assign to contract account additional incoming payment methods. An appropriate payment method is afterwards used when FI-CA document is posted.<br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Weighting key for budget billing plan (WK)<br /><br />For several installations, where consumption varies during the year, there is a request to modify the amount of budget bills according to the consumption pattern by defining a weighting key procedure for budget billing plan.<br />This funcionality is contained in func.module /SAPCE/IU_EVENT_R994.<br />The program reads the weights of weighting key, that is set on the third tab of contract, from table /SAPCE/IU_WKBBD. Then program calucalates the amount for each month and replaces the proposed constant amount in budget billing plan.<br /> <p>************************************************************************</p> <OL>2. Solution-specific customizing</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The solution-specific customizing could be set by activating: <UL><LI>BC set /CEEISUT/ISU_CZSK_1_11 for IS-U CZ and SK</LI></UL> <UL><LI>BC set /CEEISUT/ISU_RU_07 for IS-U RU</LI></UL> <UL><LI>BC set /CEEISUT/ISU_UA_02 for IS-U UA</LI></UL> <UL><LI>or one of the following hierarchical BC sets:</LI></UL> <UL><UL><LI>/CEEISUT/ISU_CZ for IS-U CZ</LI></UL></UL> <UL><UL><LI>/CEEISUT/ISU_SK for IS-U SK</LI></UL></UL> <UL><UL><LI>/CEEISUT/ISU_RU for IS-U RU</LI></UL></UL> <UL><UL><LI>/CEEISUT/ISU_UA for IS-U UA</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;in transaction SCPR20.<br /><br /> Note: Before activating any BC set compare its values with already existing customizing by using function 'Compare with system tables' in initial screen of transaction SCPR20 to avoid overwriting of already existing customizing.<br /> <p>************************************************************************</p> <OL>3. Installation-specific customizing</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can define BBIC-defaults and Weighting keys and set account maintenance customizing as described in attached documentation.<br />To access customizing you can use one of the following transactions: <UL><LI>/SAPCE/IU_CZ_IMG - IMG for IS-U localization for CZ</LI></UL> <UL><LI>/SAPCE/IU_SK_IMG - IMG for IS-U localization for SK</LI></UL> <UL><LI>SPRO - SAP Utilities -&gt; Invoicing -&gt; Budget Billing Plan -&gt; &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Budget Billing for Industrial Customers&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; for IS-U RU/UA</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note: To call for example transaction /SAPCE/IU_CZ_IMG, you have to write /n/SAPCE/IU_CZ_IMG in command field or add transaction to your favorites.<br /> <p>************************************************************************</p> <OL>4. Documentation</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The documentation is attached to this note. <br />(If you have accessed this note through SAP Service Marketplace see Tab-page Attachements.)</div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-CSC-SK-IS-U (use FI-LOC-UT-SK)"}, {"Key": "Other Components", "Value": "XX-CSC-UA-IS-U (use FI-LOC-UT-UA)"}, {"Key": "Other Components", "Value": "XX-CSC-RU-IS-U (Utilities)"}, {"Key": "Responsible                                                                                         ", "Value": "I024302"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I003984)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000774539/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000774539/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000774539/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000774539/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000774539/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000774539/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000774539/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000774539/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000774539/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "BBIC_WK_docu_472_CZSKRUUA_en.pdf", "FileSize": "204", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000440102004&iv_version=0008&iv_guid=4CDEFF39C9D97C42A75ED17C19FA3EE4"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "921035", "RefComponent": "XX-CSC-RU-IS-U", "RefTitle": "Russian specific functionality IS-U 4.72 (collective)", "RefUrl": "/notes/921035"}, {"RefNumber": "876337", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "BBIC: Distribute BB amount to percentage <= 100%", "RefUrl": "/notes/876337"}, {"RefNumber": "794753", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "BBIC: invoicing - deregulation fields in OP not filled", "RefUrl": "/notes/794753"}, {"RefNumber": "794506", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "CZSK: Move-in and move-out - Error message", "RefUrl": "/notes/794506"}, {"RefNumber": "793022", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "Weighting key: error during BBP gener. in invoicing", "RefUrl": "/notes/793022"}, {"RefNumber": "774624", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "Czech & Slovak specific functionality IS-U 4.72 (collective)", "RefUrl": "/notes/774624"}, {"RefNumber": "1125443", "RefComponent": "IS-U-BI", "RefTitle": "INV: error AJ 064 in extrapolation for budget billing plan", "RefUrl": "/notes/1125443"}, {"RefNumber": "1093637", "RefComponent": "XX-CSC-RU-IS-U", "RefTitle": "BBIC: Missing Parameter in Func Call - Deregulation", "RefUrl": "/notes/1093637"}, {"RefNumber": "1058944", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "BBIC-R402: wrong period and doubled BB items", "RefUrl": "/notes/1058944"}, {"RefNumber": "1057075", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "BBIC: extrapolation - error in /SAPCE/IU_EVENT_R402", "RefUrl": "/notes/1057075"}, {"RefNumber": "1014963", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "Czech & Slovak specific functionality IS-UT 600 (collective)", "RefUrl": "/notes/1014963"}, {"RefNumber": "1014954", "RefComponent": "XX-CSC-RU-IS-U", "RefTitle": "Russian specific functionality for IS-UT 600 (collective)", "RefUrl": "/notes/1014954"}, {"RefNumber": "1014953", "RefComponent": "XX-CSC-UA-IS-U", "RefTitle": "Ukrainian specific functionality for IS-UT 600 (collective)", "RefUrl": "/notes/1014953"}, {"RefNumber": "1012272", "RefComponent": "XX-CSC-UA-IS-U", "RefTitle": "Ukrainian specific functionality IS-U 4.72 (collective)", "RefUrl": "/notes/1012272"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2745362", "RefComponent": "XX-CSC-UA-IS-U", "RefTitle": "IS-U Ukraine Budget Billing: Incorrect calculation of tax amounts for invoices", "RefUrl": "/notes/2745362 "}, {"RefNumber": "1014954", "RefComponent": "XX-CSC-RU-IS-U", "RefTitle": "Russian specific functionality for IS-UT 600 (collective)", "RefUrl": "/notes/1014954 "}, {"RefNumber": "921035", "RefComponent": "XX-CSC-RU-IS-U", "RefTitle": "Russian specific functionality IS-U 4.72 (collective)", "RefUrl": "/notes/921035 "}, {"RefNumber": "1125443", "RefComponent": "IS-U-BI", "RefTitle": "INV: error AJ 064 in extrapolation for budget billing plan", "RefUrl": "/notes/1125443 "}, {"RefNumber": "1093637", "RefComponent": "XX-CSC-RU-IS-U", "RefTitle": "BBIC: Missing Parameter in Func Call - Deregulation", "RefUrl": "/notes/1093637 "}, {"RefNumber": "1058944", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "BBIC-R402: wrong period and doubled BB items", "RefUrl": "/notes/1058944 "}, {"RefNumber": "1057075", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "BBIC: extrapolation - error in /SAPCE/IU_EVENT_R402", "RefUrl": "/notes/1057075 "}, {"RefNumber": "1014953", "RefComponent": "XX-CSC-UA-IS-U", "RefTitle": "Ukrainian specific functionality for IS-UT 600 (collective)", "RefUrl": "/notes/1014953 "}, {"RefNumber": "1014963", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "Czech & Slovak specific functionality IS-UT 600 (collective)", "RefUrl": "/notes/1014963 "}, {"RefNumber": "1012272", "RefComponent": "XX-CSC-UA-IS-U", "RefTitle": "Ukrainian specific functionality IS-U 4.72 (collective)", "RefUrl": "/notes/1012272 "}, {"RefNumber": "876337", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "BBIC: Distribute BB amount to percentage <= 100%", "RefUrl": "/notes/876337 "}, {"RefNumber": "794753", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "BBIC: invoicing - deregulation fields in OP not filled", "RefUrl": "/notes/794753 "}, {"RefNumber": "794506", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "CZSK: Move-in and move-out - Error message", "RefUrl": "/notes/794506 "}, {"RefNumber": "793022", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "Weighting key: error during BBP gener. in invoicing", "RefUrl": "/notes/793022 "}, {"RefNumber": "774624", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "Czech & Slovak specific functionality IS-U 4.72 (collective)", "RefUrl": "/notes/774624 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-U/CCS", "From": "472", "To": "472", "Subsequent": ""}, {"SoftwareComponent": "CEEISUT", "From": "472", "To": "472", "Subsequent": ""}, {"SoftwareComponent": "CEEISUT", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}