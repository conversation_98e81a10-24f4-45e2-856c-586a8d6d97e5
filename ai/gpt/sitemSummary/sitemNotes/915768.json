{"Request": {"Number": "915768", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 321, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016303392017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000915768?language=E&token=9EEA4B1133FDDFDA73B093564819C68B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000915768", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000915768/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "915768"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Customizing"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.03.2006"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-SR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Sales-Based Rent"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Sales-Based Rent", "value": "RE-FX-SR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-SR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "915768 - Sales-based rent agreements for lease-in contracts"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Lease-in contract contains a sales-based rent agreement</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Lease-in, Expense lease</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>When tenants manage in lease-in contracts (expense leases) with landlords sales-based rent agreements and execute related settlements, specific settings in customizing are required.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. Customizing of condition types<br /><br />In order to manage sales-based rent agreements in lease-in contracts (real estate contracts at vendor side), it is necessary to define special condition types. (This procedure is comparable to what has to be executed for lease-out contracts on the customer side.)</OL> <OL><OL>a) Sales-based rent<br /><br />Please create this condition type with the attribute \"sales-based rent\" and assign it to a corresponding flow type.<br /><br />Purpose of this condition is to be used as an \"anchor\" in the sales-based rent agreement without any amount. Although the \"sales-based rent\" condition type is not taken into account during periodic postings, the information about linked posting terms and objects is relevant for the sales-based rent settlement process.<br /><br />The definition of this condition type is <B>mandatory</B> for the management of sales-based rent agreements in SAP Real Estate Management.</OL></OL> <OL><OL>b) Advance payments on sales-based rent<br /><br />If advance payments are paid for sales-based rents, please create a condition \"advance-payments for sales-based rent\" with the attribute \"AP sales-based rent\" and select \"revenue\", and assign it to a corresponding flow type.<br /><br />Please note: although this condition is an expense condition, the field \"revenue\" has to be selected, because it indicates that this condition is relevant for profit and lost accounting.<br /><br />It is <B>not</B> recommended to use special general ledger indicators in the definition of the flow types that you assign to this condition type. Nevertheless, you could use special general ledger indicators. However, we assume that in business practice it does not make sense to apply special general ledgers in this area.<br /><br />The definition of this condition type is optional for the management of sales-based rents.</OL></OL> <OL><OL>c) Minimum sales-based rent<br /><br />If the customer has to pay a minimum rent, please create a condition with the attribute \"minimum sales-based rent\", and assign it to a corresponding flow type.<br /><br />Sometimes, the periodical \"basic rent\" represents the \"minimum sales-based rent\". In this case, please customize the \"basic rent\" condition as described above for \"minimum sales-based rent\" condition types.<br /><br />The definition of this condition type is optional for the management of sales-based rents.</OL></OL> <OL><OL>d) Maximum sales-based rent<br /><br />If the sales-based rent is limited to a maximum amount, please create a condition type with the attribute \"maximum sales-based rent\", and assign it to a corresponding flow type.<br /><br />The \"maximum rent\" is normally defined as a statistical condition. The reason is that the \"maximum rent\" will not be posted but represents only a limitation for the purpose of sales-based settlement (e.g. maximum is equal to two time of the basic rent). This condition is taken into account when the sales-based rent is calculated during the sales-based rent settlement.<br /><br />The definition of this condition type is optional for the management of sales-based rents.</OL></OL> <OL>2. Customizing of condition purpose<br /><br />Please maintain the condition purpose for the defined condition types. The recommended customizing is as follows:<br /><br />- Sales-based rent: Actual rent<br /><br />- Advance payments on sales-based rent: Actual rent<br /><br />- Minimum sales-based rent: Actual rent<br /><br />- Maximum sales-based rent: Statistical</OL> <OL>3. Customizing of flow types<br /><br />Please define appropriate flow and reference flow types and assign them to the condition types.</OL> <OL>4. Customizing of condition groups<br /><br />Please assign the sales-based rent conditions to the relevant condition group (e.g. commercial real estate contract) and make sure that the condition group is assigned to the right contract type.</OL> <OL>5. Customizing of sales rule<br /><br />Please define the name of the sales rules. Please observe that this feature is not limited to turnover of companies, but you could also define other types of rules such as phone units, profit-based rents, etc.</OL> <OL>6. Customizing of reporting rules<br /><br />Please define unique key and name for each sales type. When the sales type is for sales-based rent settlement based on quantitative sales, then you have to enter the unit of measurement for the quantity (such as gallons). For sales-based rent settlement based on monetary sales, you do not have to enter a unit of measure in this activity. The system automatically defaults the company code currency in the sales rule. Examples are Complete Assortment, Clothing, Tobacco products, Foodstuffs, and Beverages.</OL> <OL>7. Create sales-based rent agreements<br /><br />When you create a new lease-in contract, please select<br /><br />- select \"relevant for sales\" on the \"General Data\" tab<br /><br />- and insert the relevant conditions on the \"Conditions\" tab<br /><br />- enter sales and reporting rules on the \"Sales-Based Rent Agreement\"<br /><br />- assign the sales rules to the relevant \"conditions\"</OL> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D032292)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D043228)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000915768/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000915768/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000915768/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000915768/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000915768/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000915768/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000915768/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000915768/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000915768/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "517673", "RefComponent": "RE-FX", "RefTitle": "Flexible Real Estate: Functions and restrictions", "RefUrl": "/notes/517673"}, {"RefNumber": "443311", "RefComponent": "RE", "RefTitle": "Enterprise extension SAP Real Estate: Technical basis", "RefUrl": "/notes/443311"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "517673", "RefComponent": "RE-FX", "RefTitle": "Flexible Real Estate: Functions and restrictions", "RefUrl": "/notes/517673 "}, {"RefNumber": "443311", "RefComponent": "RE", "RefTitle": "Enterprise extension SAP Real Estate: Technical basis", "RefUrl": "/notes/443311 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "200", "To": "200", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}