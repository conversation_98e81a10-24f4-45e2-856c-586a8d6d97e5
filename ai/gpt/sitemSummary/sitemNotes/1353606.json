{"Request": {"Number": "1353606", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 732, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016815362017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001353606?language=E&token=511F7032709DF462CFBBB4FA3095B27C"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001353606", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1353606"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.09.2009"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-LC-HU"}, "SAPComponentKeyText": {"_label": "Component", "value": "Hungary"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Real Estate Localization", "value": "RE-FX-LC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hungary", "value": "RE-FX-LC-HU", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC-HU*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1353606 - RE-FX Country Version for Hungary"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Localization of Flexible Real Estate Management for Hungary.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Real Estate, Flexible Real Estate, RE-FX, Localization, Correction invoice, Reversal Invoice, Fulfillment Date, Copy Numbering</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Flexible Real Estate Management is used by a company in Hungary.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p></p> <OL>1. Release strategy</OL> <p><br />SAP Real Estate Management is currently available in two versions:<br />Classic Real Estate (old version) and Flexible Real Estate (new &#x00A0;&#x00A0;&#x00A0;&#x00A0;version). Please read more details about the solution strategy in<br />SAP Note 443311.<br /><br />Please note that Classic Real Estate will not be developed and localized anymore and mainly covers only the requirements of residential real estate. Therefore, Classic Real Estate is not released for new implementation projects since the general availability for the release SAP ERP 6.0.</p> <OL>2. Localization</OL> <p><br />It is planned that as of EhP5 (Enhancement Package 5) of SAP ERP 6.0 Hungarian residential and commercial real estate companies can use the localized Flexible Real Estate Management solution for Hungary with a standard localization functionality.<br /><br />Please read more about localization of SAP Real Estate Management in general and the consequences of a missing localization in SAP Note 771098.</p> <OL>3. Localization scope (Legal requirements and major business practices in Hungary)</OL> <p><br />Planned to be available as of Enhancement Package 5 (EhP5) of SAP ERP 6.0.<br /></p> <b><U><B>Correction Invoices / Reversal Invoices</B></U></b><br /> <p><br />When you need to make a correction on an invoice, or you need to reverse it, in some countries like Hungary you are required to create a correction invoice or a reversal invoice, respectively.<br /><br />A <U>Correction Invoice</U> is an invoice which contains the withdrawn original amount, the corrected amount and a reference to the original invoice item which was corrected.<br /><br />A Reversal Invoice is an invoice which contains the reversed amount and a reference to the original invoice.<br /><br />A new transaction enables you to determine for which financial documents created via periodic postings, Correction Invoices or Reversal Invoices should be created.<br /><br />The print form of these invoices will respect the Hungarian prescriptions for Correction / Reversal Invoices.<br /><br />You can also create a Correction / Reversal Invoices for a Correction Invoices.<br /><br />The correction/reversal invoicing functionality is extensible for further countries.<br /></p> <b>Fulfillment Date</b><br /> <p><br />Till note 1023317 (ERP 6.00 SP11, EhP2) there was no dedicated field for the Fulfillment Date. Customers used the Document Date or the Posting Date for this purpose. Since note 1023317 the financial documents have a 'Tax Reporting Date' on the header level. See also note 1232484 for how to use this date.<br /><br />For Hungarian companies the Fulfillment Date of the financial documents created with real estate periodic postings is determined depending on the individual real estate contract conditions if they are categorized as continuous or discontinuous service.<br /><br />In case of continuous services, the Fulfillment Date is the same as the payment due date, in case of non-continuous services, it could be different (the business practice in Hungary is to use the document date entered at the time of the periodic posting).<br /><br />The system determines the Fulfillment Date at the time of the periodic posting and stores it in the Tax Reporting Date (because of historical reasons also in the Document Date) of the document header. It also splits the financial documents in case multiple fulfillment dates were determined in as many pieces as different fulfillment dates.<br /><br />The Fulfillment Date on the header data of a real estate invoice is read from the Tax Reporting Date of the financial documents.<br /><br />The Fulfillment Date is determined also for the correction invoices and for reversal invoices.<br /></p> <b>Copy Numbering</b><br /> <p><br />When you print your original, correction, and reversal invoices for Hungarian companies, the system identifies each copy with a unique consecutive number as defined by law. You can apply the copy numbering for non-preprinted invoices for printing with separate copies or for printing with carbon copies.<br /></p> <OL>1. Others</OL> <b>Missing country-specific functions</b><br /> <b></b><br /> <UL><LI>Correction Invoices and Reversal Invoices created from financial documents originating from 'One-time Posting' transactions -&gt; use the one-time conditions to have the Correction/Reversal Invoices originating from periodical postings.</LI></UL> <UL><LI>Correction Invoices and Reversal Invoices originating from sales-based rent postings or yearly service charge settlement postings.</LI></UL> <UL><LI>Registration of base amounts and tax amounts in the cash flow of credit-side contracts according to the amounts in the incoming invoices.</LI></UL> <OL>1. Localization recommendations</OL> <p><br />Please note that by default localization enhancements which affect databases or user interfaces made in higher releases cannot be downgraded on earlier versions.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "RE-FX (Flexible Real Estate Management)"}, {"Key": "Responsible                                                                                         ", "Value": "D021316"}, {"Key": "Processor                                                                                           ", "Value": "D042440"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001353606/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001353606/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001353606/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001353606/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001353606/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001353606/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001353606/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001353606/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001353606/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "771098", "RefComponent": "RE-FX-LC", "RefTitle": "Countries: Release restriction RE-FX", "RefUrl": "/notes/771098"}, {"RefNumber": "517673", "RefComponent": "RE-FX", "RefTitle": "Flexible Real Estate: Functions and restrictions", "RefUrl": "/notes/517673"}, {"RefNumber": "1682436", "RefComponent": "RE-FX-LC-HU", "RefTitle": "RE-FX HU: REXCHUOC1 does not support distr.form. 'D'", "RefUrl": "/notes/1682436"}, {"RefNumber": "1563201", "RefComponent": "RE-FX-LC-HU", "RefTitle": "RE-FX Country Version for Hungary", "RefUrl": "/notes/1563201"}, {"RefNumber": "1232484", "RefComponent": "FI-GL-GL-F", "RefTitle": "How to use the VAT due date, tax reporting date VATDATE", "RefUrl": "/notes/1232484"}, {"RefNumber": "1023317", "RefComponent": "FI-GL-GL-F", "RefTitle": "Legal Change: Vat Due date", "RefUrl": "/notes/1023317"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "771098", "RefComponent": "RE-FX-LC", "RefTitle": "Countries: Release restriction RE-FX", "RefUrl": "/notes/771098 "}, {"RefNumber": "517673", "RefComponent": "RE-FX", "RefTitle": "Flexible Real Estate: Functions and restrictions", "RefUrl": "/notes/517673 "}, {"RefNumber": "1682436", "RefComponent": "RE-FX-LC-HU", "RefTitle": "RE-FX HU: REXCHUOC1 does not support distr.form. 'D'", "RefUrl": "/notes/1682436 "}, {"RefNumber": "1023317", "RefComponent": "FI-GL-GL-F", "RefTitle": "Legal Change: Vat Due date", "RefUrl": "/notes/1023317 "}, {"RefNumber": "1563201", "RefComponent": "RE-FX-LC-HU", "RefTitle": "RE-FX Country Version for Hungary", "RefUrl": "/notes/1563201 "}, {"RefNumber": "1232484", "RefComponent": "FI-GL-GL-F", "RefTitle": "How to use the VAT due date, tax reporting date VATDATE", "RefUrl": "/notes/1232484 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "605", "To": "605", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1563201", "RefTitle": "RE-FX Country Version for Hungary", "RefUrl": "/notes/0001563201"}]}}}}}