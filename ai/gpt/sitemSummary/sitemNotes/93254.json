{"Request": {"Number": "93254", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 256, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014541422017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000093254?language=E&token=1B76D44F7E17C9E4D4C1B0AFC1930636"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000093254", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000093254/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "93254"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 26}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.10.2012"}, "SAPComponentKey": {"_label": "Component", "value": "BC-MID-RFC"}, "SAPComponentKeyText": {"_label": "Component", "value": "RFC"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Middleware", "value": "BC-MID", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-MID*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "RFC", "value": "BC-MID-RFC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-MID-RFC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "93254 - RFC short dump RFC_NO_AUTHORITY"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The system generates short dumps with the ABAP runtime error RFC_NO_AUTHORITY in transaction ST22.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RFC, Remote Function Call, S_RFC, RFC_NO_AUTHORITY, authorization, authority</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>As of Release 31G, you can perform Remote Function Call authorization checks based on the (called) function groups.<br /><br />If the instance profile parameter auth/rfc_authority_check is set (equal to 1), the system automatically performs an RFC authorization check. The authorization check refers to the function group of the function module to be called. If you do not have authorization for the authorization object S_RFC, the ABAP runtime error RFC_NO_AUTHORITY occurs. You can use the function module AUTHORITY_CHECK_RFC to check the authorization in advance.<br /><br />The RFC authorization check in the ABAP system is carried out during each RFC access to the function module that is called (the check is performed against the relevant function module group and as of SAP Release 702 for all required function modules). The authorization check is performed using the authorization object S_RFC. This authorization object contains the following three fields:<br /><br />RFC_TYPE: Type of RFC object to be protected<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; This field can have the value FUGR (function group).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; As of NetWeaver Release 702, this field can<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; have the value FUGR (function group) as well as the value FUNC<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(function module).<br /><br />RFC_NAME: Name of RFC to be protected<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; This field contains the names of the function groups.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; As of NetWeaver Release 702, this field can<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; contain the names of the function groups as well as the names of the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; function modules.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; This field may be a maximum of 18 characters in length in Release 31.I.<br /><br /><br />and ACTVT:&#x00A0;&#x00A0;Activity<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This field can currently have the value 16 (Execute).<br /><br />As of Release 4.0, the authorization check is active by default (that is, auth/rfc_authority_check = 1).<br /><br />Comment: This system behavior is documented and the ABAP online documentation contains information about this.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>You assign the required authorizations for the function groups that are called to the user or for all required function modules as of Release 702. When you do this, the applications that use this RFC user must determine which authorization profile the user must receive.<br /><br />Important note: For security reasons, we recommend that you do not deactivate the profile parameter (that is, do NOT set the value of the profile parameter \"auth/rfc_authority_check to 0).<br /><br />If the ABAP runtime error RFC_NO_AUTHORITY occurs with the short text:<br />&#x00A0;&#x00A0;No RFC authorization for function group \"-1\" for user \"Wrong authority\",<br />you can correct this problem with the following patch level:</p> <UL><LI>For Release 46B, with patch level 260 and the patch text:<br />\"RfcOpenEx works incorrect with saprfc.ini\" (Note 93254)</LI></UL> <UL><LI>For Release 46D, with patch level 21 and the patch text:<br /> rfc authority problem (Note 93254).</LI></UL> <p><br />You can correct an error in the RFC authorization check using the patch \"RFC authority check failed\". In the case of this error, an RFC user was granted authorization, even though this was not defined in the user master record.<br />This problem is corrected with the following patch level:</p> <UL><LI>For kernel 31I: Patch level 545</LI></UL> <UL><LI>For kernel 40B: Patch level 743</LI></UL> <UL><LI>For kernel 45B: Patch level 531</LI></UL> <UL><LI>For kernel 46D: Patch level 195</LI></UL> <p><br />In addition, the ABAP runtime error RFC_NO_AUTHORITY that occurs with the short text:<br />&#x00A0;&#x00A0;No RFC authorization for function group \"1\"<br />is corrected for kernel 31I with patch level 554 and the patch text \"RFC authority check failed after second call\".<br /><br />For information about downloading patches, see Note 19466.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D020135)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D026759)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000093254/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000093254/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000093254/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000093254/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000093254/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000093254/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000093254/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000093254/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000093254/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "944615", "RefComponent": "BC-MID-RFC", "RefTitle": "RFC_NO_AUTHORITY ABAP runtime error for SAPSYS user", "RefUrl": "/notes/944615"}, {"RefNumber": "931252", "RefComponent": "BC-MID-RFC", "RefTitle": "Security Note: Authority Check for Function Group SRFC", "RefUrl": "/notes/931252"}, {"RefNumber": "81846", "RefComponent": "BC-FES-INS", "RefTitle": "Desktop integration: Information & documentation", "RefUrl": "/notes/81846"}, {"RefNumber": "767543", "RefComponent": "BC-TWB-TST-ECA", "RefTitle": "Short Dumps in eCATT Due To Missing RFC Authorizations", "RefUrl": "/notes/767543"}, {"RefNumber": "726874", "RefComponent": "BC-FES-AIT-CTR", "RefTitle": "OCX Controls: Authorization for function modules.", "RefUrl": "/notes/726874"}, {"RefNumber": "69382", "RefComponent": "MM-PUR-GF-DTF", "RefTitle": "Batch input of POs: EM 00344 with screen SAPLSPO1 300", "RefUrl": "/notes/69382"}, {"RefNumber": "626073", "RefComponent": "BC-MID-ICF", "RefTitle": "Unreleased Internet Communication Framework services", "RefUrl": "/notes/626073"}, {"RefNumber": "550878", "RefComponent": "CA-DMS", "RefTitle": "Easy DMS interface: Prerequisites", "RefUrl": "/notes/550878"}, {"RefNumber": "1844452", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1844452"}, {"RefNumber": "165061", "RefComponent": "BC-BMT-WFM", "RefTitle": "RFC error for workflow administration reports", "RefUrl": "/notes/165061"}, {"RefNumber": "165018", "RefComponent": "BC-BMT-WFM", "RefTitle": "RFC error in the workflow process", "RefUrl": "/notes/165018"}, {"RefNumber": "164079", "RefComponent": "BC-BMT-WFM", "RefTitle": "RFC error in generating events", "RefUrl": "/notes/164079"}, {"RefNumber": "1487606", "RefComponent": "BC-MID-ALE", "RefTitle": "IDoc inbound processing via HTTP/SOAP", "RefUrl": "/notes/1487606"}, {"RefNumber": "1394100", "RefComponent": "BC-MID-ICF", "RefTitle": "Security note: Access to RFC-enabled modules via SOAP", "RefUrl": "/notes/1394100"}, {"RefNumber": "129795", "RefComponent": "BC-DWB-SEM", "RefTitle": "RFC authorizations for the R/3 logon with SM", "RefUrl": "/notes/129795"}, {"RefNumber": "128105", "RefComponent": "BC-CCM-PRN-PC", "RefTitle": "Front-end printing (composite SAP Note)", "RefUrl": "/notes/128105"}, {"RefNumber": "1261193", "RefComponent": "BC-SEC-AUT", "RefTitle": "Composite SAP Note : HCM Authorizations Documentation", "RefUrl": "/notes/1261193"}, {"RefNumber": "123418", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/123418"}, {"RefNumber": "1168772", "RefComponent": "BC-MID-CON-JCO", "RefTitle": "Minimum authorization profile for JRA", "RefUrl": "/notes/1168772"}, {"RefNumber": "101971", "RefComponent": "BC-DWB-TOO-SCR", "RefTitle": "37527 Graphical full screen is not available (RFC)", "RefUrl": "/notes/101971"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2629803", "RefComponent": "BC-MID-RFC", "RefTitle": "Parameter  auth/trfc_no_authority_check is unknown in RZ11", "RefUrl": "/notes/2629803 "}, {"RefNumber": "2569749", "RefComponent": "BC-MID-RFC", "RefTitle": "Troubleshooting RFC_NO_AUTHORITY dump - Guided Answers", "RefUrl": "/notes/2569749 "}, {"RefNumber": "2498923", "RefComponent": "BC-MID-RFC", "RefTitle": "auth/rfc_authority_check parameter", "RefUrl": "/notes/2498923 "}, {"RefNumber": "1947361", "RefComponent": "EHS-SRC-SCC", "RefTitle": "Errors with the Remote Function Call authority check", "RefUrl": "/notes/1947361 "}, {"RefNumber": "128105", "RefComponent": "BC-CCM-PRN-PC", "RefTitle": "Front-end printing (composite SAP Note)", "RefUrl": "/notes/128105 "}, {"RefNumber": "1261193", "RefComponent": "BC-SEC-AUT", "RefTitle": "Composite SAP Note : HCM Authorizations Documentation", "RefUrl": "/notes/1261193 "}, {"RefNumber": "101971", "RefComponent": "BC-DWB-TOO-SCR", "RefTitle": "37527 Graphical full screen is not available (RFC)", "RefUrl": "/notes/101971 "}, {"RefNumber": "1168772", "RefComponent": "BC-MID-CON-JCO", "RefTitle": "Minimum authorization profile for JRA", "RefUrl": "/notes/1168772 "}, {"RefNumber": "69382", "RefComponent": "MM-PUR-GF-DTF", "RefTitle": "Batch input of POs: EM 00344 with screen SAPLSPO1 300", "RefUrl": "/notes/69382 "}, {"RefNumber": "944615", "RefComponent": "BC-MID-RFC", "RefTitle": "RFC_NO_AUTHORITY ABAP runtime error for SAPSYS user", "RefUrl": "/notes/944615 "}, {"RefNumber": "767543", "RefComponent": "BC-TWB-TST-ECA", "RefTitle": "Short Dumps in eCATT Due To Missing RFC Authorizations", "RefUrl": "/notes/767543 "}, {"RefNumber": "726874", "RefComponent": "BC-FES-AIT-CTR", "RefTitle": "OCX Controls: Authorization for function modules.", "RefUrl": "/notes/726874 "}, {"RefNumber": "550878", "RefComponent": "CA-DMS", "RefTitle": "Easy DMS interface: Prerequisites", "RefUrl": "/notes/550878 "}, {"RefNumber": "164079", "RefComponent": "BC-BMT-WFM", "RefTitle": "RFC error in generating events", "RefUrl": "/notes/164079 "}, {"RefNumber": "165061", "RefComponent": "BC-BMT-WFM", "RefTitle": "RFC error for workflow administration reports", "RefUrl": "/notes/165061 "}, {"RefNumber": "165018", "RefComponent": "BC-BMT-WFM", "RefTitle": "RFC error in the workflow process", "RefUrl": "/notes/165018 "}, {"RefNumber": "129795", "RefComponent": "BC-DWB-SEM", "RefTitle": "RFC authorizations for the R/3 logon with SM", "RefUrl": "/notes/129795 "}, {"RefNumber": "81846", "RefComponent": "BC-FES-INS", "RefTitle": "Desktop integration: Information & documentation", "RefUrl": "/notes/81846 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}