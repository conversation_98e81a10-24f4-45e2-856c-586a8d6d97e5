{"Request": {"Number": "1229334", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 510, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016555452017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001229334?language=E&token=931E2314CFF3735DA2A3A0071600E855"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001229334", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001229334/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1229334"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Correction of legal function"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.07.2016"}, "SAPComponentKey": {"_label": "Component", "value": "PY-PT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Portugal"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Portugal", "value": "PY-PT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-PT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1229334 - RPCAIDP0 and RPCIIDP0 improvements"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Changes in the Income declaration reports for Portugal (Annual Income Declaration - RPCAIDP0 and Individual Income Declaration - RPCIIDP0) were necessary to improve the consistence of both reports processes.<br /><br />The following symptoms exist for both reports:<br /><br />- The differences of legal contribution (Social Security) and Union deduction from previous years are not being attributed to the correspondent income category.<br /><br />- The field number of previous years is miscalculated when the income category H2 is.<br /><br />- The reports are not using the wage types customized for employee contribution of Social Security Christmas bonus and vacation to fill the field Legal contribution (\"Descontos legais).<br /><br />The following symptoms exist for the Annual Income Declaration report (RPCAIDP0) only:<br /><br />- When the fields Incomes from the year (\"Rendimentos do Ano\"), Incomes from previous years (\"Rendimentos do Anos Anteriores\"), or Deductions (\"Import&#226;ncias Retidas\")are not filled, the records J02 of the TemSe file are rejected by the software validator (Modelo 10 - 2008 Version 3.0).<br /><br />- The report is not converting to Euro the amount retrieved from Social Security contributions (\"Descontos Legais\") from previous years.<br /><br />Both legal reports had the same business processes. However they were implemented in difference source codes, which made it more complex to maintain the code and implement legal changes.<br /><br /></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>RPCAIDP0, RPCIIDP0, CL_HR_PT_EMPLOYEE_TAX_DATA, Employee Tax Data class, Income declaration, Annual Income Declaration, Individual Income Declaration</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>VERSIONING</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Version</strong></td>\r\n<td><strong>Date</strong></td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td>5</td>\r\n<td>July 20, 2016</td>\r\n<td>SAR files were removed.</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>A new solution was developed to improve the processing performance, the maintenance, and the usability of both reports.<br /><br />The core processes were unified in a new solution based on a process flow in the Employee Tax Data class (CL_HR_PT_EMPLOYEE_TAX_DATA). The functionality of both reports remains the same, but their implementation is now centralized.<br /><br />The Employee Tax Data class generates information for both individual and annual income declaration reports. The class retrieves data from the cluster and prepares a list of entries to be reported by both legal reports. For more details about the Employee Tax Data class, see the class documentation.<br /><br />The technical log, an additional functionality, was created for both reports. The technical log is an ALV output containing a list of detailed payments done per employee. The payments are grouped by employee number, legal person, income category, and payment type. Each record has its type and information extracted from cluster, such as wage type, for period date of the payment, amount, payroll sequence number, etc.<br /><br />An Advanced Delivery is available in the attached files according to the following list (\"xxxxxx\" means numbers):<br /><br />L7DKxxxxxx_600.CAR - Release 600 (ERP 2005)<br />L6DKxxxxxx_500.CAR - Release 500 (ERP 2004)<br />L6BKxxxxxx_470.CAR - Release 4.70 (Enterprise)<br /><br />For more details about Advance Delivery installation procedure please read the notes listed in \"Related Notes\".<br /><br /><strong>Important</strong>: Be aware of an Advance Delivery delivers the last version of the object. It means that if you do not have the last HR Support Package installed in your system you could get errors, either Syntax Errors or process errors. In this case the only option is to undo the changes from Advance Delivery and do the code changes manually according to the Correction Instructions available in this note.<br /><br />The correction described in this note will be included in an HR Support Package. The support package includes the following:<br /><br />The following objects were created:<br /><br />- Class (ABAP Objects)<br /> CL_HR_PT_EMPLOYEE_TAX_DATA<br /><br />- Data Element<br /> PPT_PAYTDESC<br /> PPT_PAYTID<br /><br />- Single Message<br /> 3I 118<br /> 3I 119<br /> <br />- Table<br /> PPTS_ETD_PAYMENT_RECORD<br /> <br />The following objects were changed:<br /> <br />- GUI Definition <br /> RPCIIDP0<br /> RPCAIDP0<br /> <br />- Report Source Code<br /> RPCAIDPF<br /> RPCAIDPO<br /> RPCAIDPS<br /> RPCAIDPD<br /> RPCIIDP0<br /> RPCIIDPD<br /> RPCIIDPF<br /> RPCTDFPF<br /><br />- Type Group<br /> PPTTD</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I812659)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I827735)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001229334/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001229334/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001229334/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001229334/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001229334/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001229334/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001229334/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001229334/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001229334/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1322534", "RefComponent": "PY-PT", "RefTitle": "RPCIIDP0: Incomes from /111 wage type reported in category A", "RefUrl": "/notes/1322534"}, {"RefNumber": "1306249", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1306249"}, {"RefNumber": "1302323", "RefComponent": "PY-PT", "RefTitle": "RPCAIDP0, RPCIIDP0: Personnel areas without legal person", "RefUrl": "/notes/1302323"}, {"RefNumber": "1301223", "RefComponent": "PY-PT", "RefTitle": "RPCAIDP0, RPCIIDP0: Reporting of incomes from previous year", "RefUrl": "/notes/1301223"}, {"RefNumber": "1299447", "RefComponent": "PY-PT", "RefTitle": "RPCIIDP0: Missing Compulsory Discounts and Union Deduction", "RefUrl": "/notes/1299447"}, {"RefNumber": "1297578", "RefComponent": "PY-PT", "RefTitle": "RPCIIDP0:  Duplicate Income lines in the output", "RefUrl": "/notes/1297578"}, {"RefNumber": "1297078", "RefComponent": "PY-PT", "RefTitle": "RPCAIDP0: TemSe file with blank lines", "RefUrl": "/notes/1297078"}, {"RefNumber": "1296498", "RefComponent": "PY-PT", "RefTitle": "RPCAIDP0: Compulsory Discounts and Union Deduction in H2", "RefUrl": "/notes/1296498"}, {"RefNumber": "1295951", "RefComponent": "PY-PT", "RefTitle": "RPCIIDP0: Adjustments to Customer functions calls", "RefUrl": "/notes/1295951"}, {"RefNumber": "1276830", "RefComponent": "PY-PT", "RefTitle": "RPCIIDP0 - Adjustments to ESS printing", "RefUrl": "/notes/1276830"}, {"RefNumber": "1265045", "RefComponent": "PY-PT", "RefTitle": "HR-PT: improvements in PDF output for RPCIIDP0 report", "RefUrl": "/notes/1265045"}, {"RefNumber": "1127132", "RefComponent": "PY-PT", "RefTitle": "New fields and records to Annual Income Declaration report", "RefUrl": "/notes/1127132"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "1301223", "RefComponent": "PY-PT", "RefTitle": "RPCAIDP0, RPCIIDP0: Reporting of incomes from previous year", "RefUrl": "/notes/1301223 "}, {"RefNumber": "1322534", "RefComponent": "PY-PT", "RefTitle": "RPCIIDP0: Incomes from /111 wage type reported in category A", "RefUrl": "/notes/1322534 "}, {"RefNumber": "1302323", "RefComponent": "PY-PT", "RefTitle": "RPCAIDP0, RPCIIDP0: Personnel areas without legal person", "RefUrl": "/notes/1302323 "}, {"RefNumber": "1297078", "RefComponent": "PY-PT", "RefTitle": "RPCAIDP0: TemSe file with blank lines", "RefUrl": "/notes/1297078 "}, {"RefNumber": "1297578", "RefComponent": "PY-PT", "RefTitle": "RPCIIDP0:  Duplicate Income lines in the output", "RefUrl": "/notes/1297578 "}, {"RefNumber": "1296498", "RefComponent": "PY-PT", "RefTitle": "RPCAIDP0: Compulsory Discounts and Union Deduction in H2", "RefUrl": "/notes/1296498 "}, {"RefNumber": "1295951", "RefComponent": "PY-PT", "RefTitle": "RPCIIDP0: Adjustments to Customer functions calls", "RefUrl": "/notes/1295951 "}, {"RefNumber": "1299447", "RefComponent": "PY-PT", "RefTitle": "RPCIIDP0: Missing Compulsory Discounts and Union Deduction", "RefUrl": "/notes/1299447 "}, {"RefNumber": "1265045", "RefComponent": "PY-PT", "RefTitle": "HR-PT: improvements in PDF output for RPCIIDP0 report", "RefUrl": "/notes/1265045 "}, {"RefNumber": "1276830", "RefComponent": "PY-PT", "RefTitle": "RPCIIDP0 - Adjustments to ESS printing", "RefUrl": "/notes/1276830 "}, {"RefNumber": "1127132", "RefComponent": "PY-PT", "RefTitle": "New fields and records to Annual Income Declaration report", "RefUrl": "/notes/1127132 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_HRCPT", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HRCPT", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HRCPT", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_HRCPT", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HRCPT 470", "SupportPackage": "SAPK-47087INSAPHRCPT", "URL": "/supportpackage/SAPK-47087INSAPHRCPT"}, {"SoftwareComponentVersion": "SAP_HRCPT 500", "SupportPackage": "SAPK-50053INSAPHRCPT", "URL": "/supportpackage/SAPK-50053INSAPHRCPT"}, {"SoftwareComponentVersion": "SAP_HRCPT 600", "SupportPackage": "SAPK-60036INSAPHRCPT", "URL": "/supportpackage/SAPK-60036INSAPHRCPT"}, {"SoftwareComponentVersion": "SAP_HRCPT 604", "SupportPackage": "SAPK-60403INSAPHRCPT", "URL": "/supportpackage/SAPK-60403INSAPHRCPT"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}