{"Request": {"Number": "1060629", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 318, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016296552017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001060629?language=E&token=79ED0FA6FF19AD01E5E7522EB680A534"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001060629", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001060629/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1060629"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.08.2011"}, "SAPComponentKey": {"_label": "Component", "value": "SD-BIL-RB"}, "SAPComponentKeyText": {"_label": "Component", "value": "Rebate Processing"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Sales and Distribution", "value": "SD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Billing", "value": "SD-BIL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD-BIL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Rebate Processing", "value": "SD-BIL-RB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD-BIL-RB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1060629 - SDS060RB/ENH_REBATE_S469RB:Reorg. of data relevant to rebate"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You can use report SDS060RB to reorganize information structure S060. You can call this report in Customizing for rebate processing under \"Simulate and Execute Reorganization of Statistical Data\" using transaction VFSN.<br /><br />An incorrect execution of the report triggers incorrect sales volume, accrual and payment values. This note explains the functions and correct use of the report.<br /><br />Carry out the same procedure for the report ENH_REBATE_S469RB to reorganize the structure S469.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>S060, RUWRT, AUWRT, KAWRT_K VIS, rebate reorganization, SDS060RB, SDS060RC, VFSN<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Some documentation is missing.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>You should generally only use report SDS060RB if there are errors in the statistics update. You should <B>not</B> schedule or execute the report regularly. You can use report SDS060RC instead to simulate the results of the update of an individual billing document.<br /><br /><br /><U>Functions of the report:</U><br /><br />Report SDS060RB selects the billing documents determined and cumulates the values in information structure S060.<br />If the values of the billing document have already been updated this triggers <B><B>duplicate</B></B>values or <B>repeatedly cumulated</B>values.<br /><br />Therefore, an essential element of the report is the 'INITS060' function.<br />If you start the report with this option, the system deletes all data (regardless of the selection criteria) from S060.<br /><br />To reorganize the information structure correctly, the values must be initialized and all billing documents must be recalculated.<br /><br /><br /><U>Correct execution:</U><br /><br />You can only use the report for a complete reorganization of statistical information structure S060.<br /><br />To reorganize the information structure, you must execute the report for all documents exactly once. You can split report SDS060RB in intervals, as in partial runs. You must set the 'INITS060' indicator for the first partial run, which completely initializes the information structure. For subsequent partial runs you <B>cannot</B> set the indicator, as otherwise the data of the previous partial runs is deleted.<br /><br />The safest thing to do is to execute an individual run of report SDS060RB without restrictions, with the 'INITS060' indicator set.<br /><br /><br /><U>Risks of an incorrect execution:</U></p> <UL><LI>In partial runs where 'INITS060' is set for the first partial run, the system does not select all billing documents and data is missing for the billing documents that are not processed.</LI></UL> <UL><LI>In partial runs where 'INITS060' is set for the first partial run, the system repeatedly selects billing documents and this means there are multiple sales volume, accrual and payment values.</LI></UL> <UL><LI>In partial runs where 'INITS060' is set for all partial runs, data is missing for the partial runs prior to the last run.</LI></UL> <UL><LI>If you run the report without setting the 'INITS060' indicator, values are added and there are multiple sales volume, accrual and payment values.</LI></UL> <p><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SD-BIL-RB-ENH (Enhanced Rebates)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D025004)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D025004)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001060629/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001060629/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001060629/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001060629/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001060629/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001060629/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001060629/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001060629/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001060629/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "546322", "RefComponent": "SD-BIL-RB", "RefTitle": "Periodic partial settlement uses entire sales volume", "RefUrl": "/notes/546322"}, {"RefNumber": "456187", "RefComponent": "SD-BIL-RB", "RefTitle": "No S060 update for rebate documents or cancellation", "RefUrl": "/notes/456187"}, {"RefNumber": "196145", "RefComponent": "SD-BIL-RB", "RefTitle": "Volume-based rebate: Upgrade from 3.0/3.1 to 4.X", "RefUrl": "/notes/196145"}, {"RefNumber": "195618", "RefComponent": "SD-BIL-RB", "RefTitle": "Volume-based rebate: Upgrade from 3.0/3.1 to 4.X", "RefUrl": "/notes/195618"}, {"RefNumber": "188620", "RefComponent": "SD-BIL-RB", "RefTitle": "Volume-based rebate: Upgrade from Release 3.0/3.1 to 4.X", "RefUrl": "/notes/188620"}, {"RefNumber": "179482", "RefComponent": "SD-BIL-RB", "RefTitle": "Volume-based rebate: Upgrade from 3.0/3.1 to 4.X", "RefUrl": "/notes/179482"}, {"RefNumber": "161788", "RefComponent": "SD-BIL-RB", "RefTitle": "Rebate: Condition basis after rebate correct. incor", "RefUrl": "/notes/161788"}, {"RefNumber": "160160", "RefComponent": "SD-BIL-RB", "RefTitle": "Vol.-based rebate:Reorg. S060 uses incorrect report", "RefUrl": "/notes/160160"}, {"RefNumber": "159436", "RefComponent": "SD-BIL-RB", "RefTitle": "Log for S060 reorganization", "RefUrl": "/notes/159436"}, {"RefNumber": "140741", "RefComponent": "SD-BIL-RB", "RefTitle": "Volume-based rebate:Data enhmnt.S060 Rel.4.* slow", "RefUrl": "/notes/140741"}, {"RefNumber": "140718", "RefComponent": "SD-BIL-RB", "RefTitle": "Log during statistical setup of S060", "RefUrl": "/notes/140718"}, {"RefNumber": "122863", "RefComponent": "SD-BIL-RB", "RefTitle": "Rebate:Data enhancement S060 in Rel. 4.* is slow", "RefUrl": "/notes/122863"}, {"RefNumber": "116638", "RefComponent": "SD-BIL-RB", "RefTitle": "Upgrade of info. structure S060 in Release 4.*", "RefUrl": "/notes/116638"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2200691", "RefComponent": "SD-BIL-RB", "RefTitle": "SD rebate: Simplifications in SAP S/4HANA", "RefUrl": "/notes/2200691 "}, {"RefNumber": "456187", "RefComponent": "SD-BIL-RB", "RefTitle": "No S060 update for rebate documents or cancellation", "RefUrl": "/notes/456187 "}, {"RefNumber": "546322", "RefComponent": "SD-BIL-RB", "RefTitle": "Periodic partial settlement uses entire sales volume", "RefUrl": "/notes/546322 "}, {"RefNumber": "188620", "RefComponent": "SD-BIL-RB", "RefTitle": "Volume-based rebate: Upgrade from Release 3.0/3.1 to 4.X", "RefUrl": "/notes/188620 "}, {"RefNumber": "195618", "RefComponent": "SD-BIL-RB", "RefTitle": "Volume-based rebate: Upgrade from 3.0/3.1 to 4.X", "RefUrl": "/notes/195618 "}, {"RefNumber": "196145", "RefComponent": "SD-BIL-RB", "RefTitle": "Volume-based rebate: Upgrade from 3.0/3.1 to 4.X", "RefUrl": "/notes/196145 "}, {"RefNumber": "179482", "RefComponent": "SD-BIL-RB", "RefTitle": "Volume-based rebate: Upgrade from 3.0/3.1 to 4.X", "RefUrl": "/notes/179482 "}, {"RefNumber": "159436", "RefComponent": "SD-BIL-RB", "RefTitle": "Log for S060 reorganization", "RefUrl": "/notes/159436 "}, {"RefNumber": "161788", "RefComponent": "SD-BIL-RB", "RefTitle": "Rebate: Condition basis after rebate correct. incor", "RefUrl": "/notes/161788 "}, {"RefNumber": "140718", "RefComponent": "SD-BIL-RB", "RefTitle": "Log during statistical setup of S060", "RefUrl": "/notes/140718 "}, {"RefNumber": "116638", "RefComponent": "SD-BIL-RB", "RefTitle": "Upgrade of info. structure S060 in Release 4.*", "RefUrl": "/notes/116638 "}, {"RefNumber": "140741", "RefComponent": "SD-BIL-RB", "RefTitle": "Volume-based rebate:Data enhmnt.S060 Rel.4.* slow", "RefUrl": "/notes/140741 "}, {"RefNumber": "160160", "RefComponent": "SD-BIL-RB", "RefTitle": "Vol.-based rebate:Reorg. S060 uses incorrect report", "RefUrl": "/notes/160160 "}, {"RefNumber": "122863", "RefComponent": "SD-BIL-RB", "RefTitle": "Rebate:Data enhancement S060 in Rel. 4.* is slow", "RefUrl": "/notes/122863 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}