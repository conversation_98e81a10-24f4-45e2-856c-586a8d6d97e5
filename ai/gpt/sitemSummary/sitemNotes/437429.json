{"Request": {"Number": "437429", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 416, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000002048692017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000437429?language=E&token=B8C672AA2978E2A78829BB754B51E260"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000437429", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000437429/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "437429"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "25.11.2001"}, "SAPComponentKey": {"_label": "Component", "value": "MM-PUR-VM-SET"}, "SAPComponentKeyText": {"_label": "Component", "value": "Subsequent Settlement"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Purchasing", "value": "MM-PUR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Vendor-Material Relationships and Conditions", "value": "MM-PUR-VM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Subsequent Settlement", "value": "MM-PUR-VM-SET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM-SET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "437429 - Message SG105 List settlement documents, setting up incomes"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The system displays message SG105 \"Enter rate &amp; / &amp; rate type &amp; for &amp; in the system settings\" during the list output of the settlement documents of a rebate arrangement, the subsequent settlement or the setup of the incomes.<br />The first parameter (Currency) is missing, that is, it is empty.<br />At the same time, the system does not update the incomes from the settlement documents, that is, the 'Statement of statistical data' function displays incorrect incomes.<br />As a result, incorrect incomes might be settled during the final settlement.<br />Only Releases 4.5 and 4.6 are affected when you carry out settlements via customer billing documents or vendor billing documents, and not via credit memos (invoice verification).<br />The error only occurs if the settlement documents contain tax condition records and if the settlement documents have a currency which is different from the rebate arrangement currency (during the creation of the rebate arrangement).</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Subsequent settlement (Purchasing), volume-based rebate, incomes, customer billing document, debit-side settlement accounting, reports RW<PERSON>ON01, <PERSON><PERSON><PERSON><PERSON>31, <PERSON>W<PERSON><PERSON>13, RW<PERSON><PERSON>43, RWMBON07, RW<PERSON><PERSON>37, Transactions MEB4, MER4, MEBJ, MERJ, MEBS, MERS, Euro.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The error can be generated by two causes:</p> <UL><LI>Only with customer billing documents as settlement documents:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Error in accordance with Note 439493.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The settlement documents are generally supposed to be created in rebate arrangement currency (for example, DEM).You might have to change the rebate arrangement currency with the provided functions of the subsequent settlement.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;However, in accordance with Note 358893, it is set that all customer billing documents, that is system-wide, are supposed to be created in a different currency (EUR). The customer billing document receives the currency EUR. It is therefore created in a currency deviating from the rebate arrangement currency (during the creation of the rebate arrangement).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;However, due to an error in the general billing interface the document conditions are not converted from DEM to EUR.In the Financial Accounting the DEM amounts are posted in EUR (Error 1).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the document contains tax conditions, the incomes are not transferred to the subsequent settlement (Error 2). If the document does not contain any tax conditions, the incomes are transferred into the subsequent settlement, however, with the incorrect amount, that is, only error 1 occurs.</p> <UL><LI>Vendor and customer billing documents as settlement documents:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The rebate arrangement currency was converted (for example, from DEM to EUR). The following also applies: If the document contains tax conditions, the incomes are not transferred to the subsequent settlement (Error 2).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the document does not contain any tax conditions, the incomes are correctly transferred to the subsequent settlement (no error).</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Implement the attached program correction as well as the solution from Note 439493.<br /><br />In Release 4.5/4.6B/C you must also create message:<br />NAA169 \"Cust. billing doc. &amp;1, document currency &amp;2 instead of &amp;3, Note 437429\".<br />In Release 4.0B you must also create message MN699 \"Cust. billing document &amp;, document currency &amp; instead of &amp;, note 437429\".<br /><br />In Release 3.1I you must also create messages MN604 \"The document conditions are missing (internal program error)\"<br />MN620 \"The condition records are inconsistent (different old rebate arrangement currency)\"<br />MN626 \"The condition records are inconsistent (different old rebate arrangement currency)\"<br />MN657 \"Accounting document &amp;, &amp;, &amp; could not be found\"<br />MN658 \"Billing document &amp; for rebate arrangement &amp; could not be found\"<br />MN659 \"The income data for rebate arrangement &amp; could not be set up\"<br />MN660 \"The document index contains invalid data records (internal error)\"<br />MN699 \"Cust. billing document &amp;, document currency &amp; instead of &amp;, Note 437429\".<br /><br />In Releases 3.1I and 4.0B, you must create text element 011 for report RWMBON07 with the following text (length 80):<br /><br />Afterwards, you must setup the incomes for the affected rebate arrangements (if necessary all rebate arrangements).<br />For the income setup:<br />To do this, you must start report RWMBON07 (Vendor rebate arrangements) or RWMBON37 (Customer arrangements).Delete the 'Check run' indicator and enter '000' for the version.<br />You can find further information on reports RWMBON07 and RWMBON37 in the online documentation.<br />In Release 3.1I and 4.0B you must refer to Note 114111.<br />In addition, you must cancel incorrect customer billing documents (see message NAA169 in the list of settlement documents (as of Release 4.5) or income setup).</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "HIER"}, {"Key": "Transaction codes", "Value": "MEB4"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023678)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000437429/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000437429/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000437429/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000437429/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000437429/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000437429/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000437429/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000437429/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000437429/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "439493", "RefComponent": "SD-BIL-IV", "RefTitle": "Expiring currencies: Incorrect document currency", "RefUrl": "/notes/439493"}, {"RefNumber": "437199", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlment per customer billing document w/o accntng document", "RefUrl": "/notes/437199"}, {"RefNumber": "40147", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 3.0", "RefUrl": "/notes/40147"}, {"RefNumber": "358893", "RefComponent": "SD-BIL-GF", "RefTitle": "Create billing document with different document currency", "RefUrl": "/notes/358893"}, {"RefNumber": "183379", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Composite SAP note subsequent settlement (Purchasing) 4.6", "RefUrl": "/notes/183379"}, {"RefNumber": "152725", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 4.5", "RefUrl": "/notes/152725"}, {"RefNumber": "104668", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 4.0", "RefUrl": "/notes/104668"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "358893", "RefComponent": "SD-BIL-GF", "RefTitle": "Create billing document with different document currency", "RefUrl": "/notes/358893 "}, {"RefNumber": "439493", "RefComponent": "SD-BIL-IV", "RefTitle": "Expiring currencies: Incorrect document currency", "RefUrl": "/notes/439493 "}, {"RefNumber": "183379", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Composite SAP note subsequent settlement (Purchasing) 4.6", "RefUrl": "/notes/183379 "}, {"RefNumber": "104668", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 4.0", "RefUrl": "/notes/104668 "}, {"RefNumber": "152725", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 4.5", "RefUrl": "/notes/152725 "}, {"RefNumber": "437199", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlment per customer billing document w/o accntng document", "RefUrl": "/notes/437199 "}, {"RefNumber": "40147", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 3.0", "RefUrl": "/notes/40147 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 31I", "SupportPackage": "SAPKH31I90", "URL": "/supportpackage/SAPKH31I90"}, {"SoftwareComponentVersion": "SAP_HR 31I", "SupportPackage": "SAPKE31I90", "URL": "/supportpackage/SAPKE31I90"}, {"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B68", "URL": "/supportpackage/SAPKH40B68"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B47", "URL": "/supportpackage/SAPKH45B47"}, {"SoftwareComponentVersion": "SAP_APPL 46B", "SupportPackage": "SAPKH46B34", "URL": "/supportpackage/SAPKH46B34"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C26", "URL": "/supportpackage/SAPKH46C26"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 5, "URL": "/corrins/0000437429/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 5, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 4, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "30F", "ValidTo": "31I", "Number": "114111 ", "URL": "/notes/114111 ", "Title": "Recompilation of incomes from settlement documents RWMBON07", "Component": "MM-PUR-VM-SET"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "40A", "ValidTo": "40B", "Number": "114111 ", "URL": "/notes/114111 ", "Title": "Recompilation of incomes from settlement documents RWMBON07", "Component": "MM-PUR-VM-SET"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "40B", "ValidTo": "40B", "Number": "176433 ", "URL": "/notes/176433 ", "Title": "Income recompilation, income deleted completely", "Component": "MM-PUR-VM-SET"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "45B", "ValidTo": "45B", "Number": "415396 ", "URL": "/notes/415396 ", "Title": "Statistical distrib. of income incorr., vendor billing doc.", "Component": "MM-PUR-VM-SET"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "45B", "ValidTo": "46C", "Number": "364754 ", "URL": "/notes/364754 ", "Title": "Incorrect income update frm vendor billing docs. f. invoices", "Component": "MM-PUR-VM-SET"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}