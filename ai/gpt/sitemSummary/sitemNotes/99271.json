{"Request": {"Number": "99271", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 433, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014553252017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000099271?language=E&token=060F2EE693BD570046340BB71B70B151"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000099271", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000099271/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "99271"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 26}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.04.2000"}, "SAPComponentKey": {"_label": "Component", "value": "CA-EUR-CUR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Euro Currency Customizing/Handling"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "European Monetary Union - Euro", "value": "CA-EUR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-EUR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Euro Currency Customizing/Handling", "value": "CA-EUR-CUR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-EUR-CUR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "99271 - Curr.maintenance euro, Curr. Customizing Assistant"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>If you are using several currencies in the R/3 System, the changes to the Currency Customizing which are required for the introduction of the Euro can be very extensive. Manual maintenance as described in Notes 120420 and 91481 with the existing Customizing views is then prone to errors.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>EEMU, EMU, European Monetary Union, tables TCURF, TCURR, TCURV, TCURW, TCURC, TCURT, Transaction EWCM, dual currency phase<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Advance delivery<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>In Releases 3.0D, 3.0F, 3.1H, 3.1I, 4.0A, 4.0B and 4.5A, a transaction will be available (assistant) which can be used to check the existing settings and to add any missing settings.<br />In Release 4.5B, the assistant is included in the standard delivery, but it can no longer be used once the indirect quoted exchange rate display has been activated.<br />In Release 4.6A, the assistant is not available, as of Release 4.6B, it is contained the standard delivery.<br />In Releases 3.0D, 3.0F or 3.1H, the following Hot Packages are a prerequisite for using the assistant (Note 77984):<br /> Release&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Hot Package<BR/> -------&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -----------<BR/> 3.0D&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;36<BR/> 3.0F&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;23<BR/> 3.1H&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;07<br />This assistant is available for all customers on SAPSERVx in a special transport. There are three versions of the assistant which can be found in the following SAPSERVx directories:</p> <UL><LI>for Release 3.0D general/R3server/abap/note.0099271/release3.0d</LI></UL> <UL><LI>for Release 3.0F, 3.1H and 3.1I: general/R3server/abap/note.0099271/release3.x</LI></UL> <UL><LI>for Release 4.0A, 4.0B, 4.5A: general/R3server/abap/note.0099271/release4.x</LI></UL> <p>Note 13719 describes how to import a transport.<br />Consider the following special features when importing:</p> <UL><UL><LI>Specify client 000 explicitly as a target client of the transport.</LI></UL></UL> <UL><UL><LI>Do not change the name of the transport.</LI></UL></UL> <p>After the import of the transport, the assistant can be called with Transaction EWCM.<br /><br />After the call, the assistant supplies some basic information on Euro Currency Customizing. Then it analyzes the existing Currency Customizing and requests missing specifications (fixed exchange rates, currency keys, ratios and exchange rates of the euro to external currencies) in a sequence of input screens. The entry is simplified by default values and is supported by additional information, which can be called by choosing corresponding buttons.<br />Once you have made all entries , you can display the missing entries and then finally write them to the database.</p> <h5 data-toc-skip>If several exchange rate types are used productively, the assistant must be executed several times to set up these exchange rate types for the Euro.</H5> <p>The assistant supports the following Customizing activities:</p> <UL><LI>Setting up an exchange rate type with which conversion is supported according to the EMU regulations</LI></UL> <UL><LI>Creating historical exchange rates<br />These are exchange rates between the Euro and all other currencies which have a validity date prior to 01/01/1999. These exchange rates are needed for technical reasons together with the local currency conversion to Euro. In addition, these exchange rates are required if you want to perform retroactive balance sheet comparisons.</LI></UL> <UL><LI>Setting up exchange rate types with which participating currencies and external currencies or two external currencies can be converted to each other via the reference currency Euro. With these exchange rate types, the regular maintenance of the exchange rates later on can be reduced considerably.<br /></LI></UL> <b>If you have already made settings in your system, refer to Note 131454!<br /></b><br /> <p><B>Recommendation:</B> Only run the assistant in your production system after the fixed exchange rates have been announced. The assistant is not able to correct dummy exchange rates entered in advance.<br /></p> <b>Recommended procedure</b><br /> <UL><LI>Do not make any Euro settings in your production client before 12/31/98.</LI></UL> <UL><LI>Only run the assistant in your production client when the fixed exchange rates of the participating currencies are known.</LI></UL> <UL><LI>First of all, test the setup of the currency tables as completely as possible.</LI></UL> <UL><UL><LI>To do this, set up the currency tables in a test system/client precisely as in your production system/client.</LI></UL></UL> <UL><UL><LI>Then perform all actions which are required in your production system/client.</LI></UL></UL> <UL><UL><LI>If you are using several exchange rate types productively, already set up all exchange rate types in the test system in the same way as you plan to do it for your production system.</LI></UL></UL> <UL><UL><LI>If you are using several production clients with different currency Customizing, perform the tests for all clients.</LI></UL></UL> <p><br />An alternative procedure is described in Note 131418.<br /></p> <b>Note on testing</b><br /> <p>Simple tests (translation of currency amounts) can be performed with Transaction EWCT. This transaction can be called in the first and last screen of the assistant under the menu Extras -&gt; Test calculations.<br /></p> <b>Changes<br /></b><br /> <p>11/17/98 corrected sapserv3 directory:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; .../note.0099271 instead of .../note.0099721<br />11/25/98 \"Important recommendation\" added<br />11/27/98 Directory specifications under point \"for Release 3.0D\"<br />11/27/98 \"Recommended procedure\" and \"Note on testing\" added<br />11/29/98 Transport for Release 4.x made available in German<br />12/08/98 Corrected transports for all releases in German and English<br />12/09/98 Additional note on the import of the transport<br />12/18/98 References to Notes 131418 and <B>131454(!)</B> added.<br />12/30/98 latest transports on sapservX:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Release 3.0D:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CN0K900598<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Release 3.0F, 3.1H und 3.1I: CN0K900599<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Release 4.0, 4.0B and 4.5QA: A5BK000187<br />01/05/99 latest transports on sapservX:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Release 3.0D:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CN0K900600<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Release 3.0F, 3.1H and 3.1I: CN0K900601<br />01/08/99 Release 4.0A, 4.0B and 4.5A: A5BK000188<br />02/01/99 latest transports on sapservX:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Release 3.0D:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CN0K900647<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Release 3.0F, 3.1H and 3.1I: CN0K900648</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CA-EUR (European Monetary Union - Euro)"}, {"Key": "Responsible                                                                                         ", "Value": "D023901"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000099271/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000099271/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000099271/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000099271/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000099271/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000099271/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000099271/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000099271/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000099271/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "99272", "RefComponent": "CA-EUR-CUR", "RefTitle": "Currency maintenance euro, dual currency phase", "RefUrl": "/notes/99272"}, {"RefNumber": "91481", "RefComponent": "CA-EUR-CUR", "RefTitle": "Customizing of currency tables for the euro", "RefUrl": "/notes/91481"}, {"RefNumber": "77984", "RefComponent": "CA-EUR", "RefTitle": "Euro - fulfilling the legal requirements", "RefUrl": "/notes/77984"}, {"RefNumber": "440870", "RefComponent": "CA-EUR-CUR", "RefTitle": "Currency settings after dual currency phase", "RefUrl": "/notes/440870"}, {"RefNumber": "377051", "RefComponent": "BW-BEX", "RefTitle": "BW: <PERSON><PERSON><PERSON><PERSON> translation", "RefUrl": "/notes/377051"}, {"RefNumber": "352354", "RefComponent": "BC-UPG", "RefTitle": "Modification adjustmnt w/ dev.classes EWC1 and EWC2", "RefUrl": "/notes/352354"}, {"RefNumber": "185366", "RefComponent": "CA-EUR-CUR", "RefTitle": "Analysis of Crncy Customizing w/ transaction EWCM", "RefUrl": "/notes/185366"}, {"RefNumber": "159374", "RefComponent": "CA-EUR-CUR", "RefTitle": "Error message VH 777", "RefUrl": "/notes/159374"}, {"RefNumber": "139553", "RefComponent": "CA-EUR-CUR", "RefTitle": "Euro: Company codes w/ non-participating currency", "RefUrl": "/notes/139553"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "135661", "RefComponent": "CA-EUR-CUR", "RefTitle": "Currency Customizing Euro - participatng currencies", "RefUrl": "/notes/135661"}, {"RefNumber": "131454", "RefComponent": "CA-EUR-CUR", "RefTitle": "EWCM for Customizing carried out manually", "RefUrl": "/notes/131454"}, {"RefNumber": "131418", "RefComponent": "CA-EUR-CUR", "RefTitle": "EWCM Transport of new tab.entries into prod.system", "RefUrl": "/notes/131418"}, {"RefNumber": "130272", "RefComponent": "CA-EUR-CUR", "RefTitle": "EWCM: SAPSQL_ARRAY_INSERT_DUPREC when setting up", "RefUrl": "/notes/130272"}, {"RefNumber": "129397", "RefComponent": "CA-EUR-CUR", "RefTitle": "Using currency assistant euro in test systems", "RefUrl": "/notes/129397"}, {"RefNumber": "128809", "RefComponent": "CA-EUR-CUR", "RefTitle": "Curr. Customizing Euro, ERT for external currencies", "RefUrl": "/notes/128809"}, {"RefNumber": "126737", "RefComponent": "CA-EUR-CUR", "RefTitle": "Assistant curr. Customizing euro generation error", "RefUrl": "/notes/126737"}, {"RefNumber": "123298", "RefComponent": "SD-BF-PR", "RefTitle": "Euro: Conversion of document currency in sales&distribution", "RefUrl": "/notes/123298"}, {"RefNumber": "120420", "RefComponent": "CA-EUR-CUR", "RefTitle": "Collective Note on Customizing of currency tables", "RefUrl": "/notes/120420"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "159374", "RefComponent": "CA-EUR-CUR", "RefTitle": "Error message VH 777", "RefUrl": "/notes/159374 "}, {"RefNumber": "91481", "RefComponent": "CA-EUR-CUR", "RefTitle": "Customizing of currency tables for the euro", "RefUrl": "/notes/91481 "}, {"RefNumber": "123298", "RefComponent": "SD-BF-PR", "RefTitle": "Euro: Conversion of document currency in sales&distribution", "RefUrl": "/notes/123298 "}, {"RefNumber": "128809", "RefComponent": "CA-EUR-CUR", "RefTitle": "Curr. Customizing Euro, ERT for external currencies", "RefUrl": "/notes/128809 "}, {"RefNumber": "129397", "RefComponent": "CA-EUR-CUR", "RefTitle": "Using currency assistant euro in test systems", "RefUrl": "/notes/129397 "}, {"RefNumber": "99272", "RefComponent": "CA-EUR-CUR", "RefTitle": "Currency maintenance euro, dual currency phase", "RefUrl": "/notes/99272 "}, {"RefNumber": "77984", "RefComponent": "CA-EUR", "RefTitle": "Euro - fulfilling the legal requirements", "RefUrl": "/notes/77984 "}, {"RefNumber": "440870", "RefComponent": "CA-EUR-CUR", "RefTitle": "Currency settings after dual currency phase", "RefUrl": "/notes/440870 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "377051", "RefComponent": "BW-BEX", "RefTitle": "BW: <PERSON><PERSON><PERSON><PERSON> translation", "RefUrl": "/notes/377051 "}, {"RefNumber": "135661", "RefComponent": "CA-EUR-CUR", "RefTitle": "Currency Customizing Euro - participatng currencies", "RefUrl": "/notes/135661 "}, {"RefNumber": "120420", "RefComponent": "CA-EUR-CUR", "RefTitle": "Collective Note on Customizing of currency tables", "RefUrl": "/notes/120420 "}, {"RefNumber": "139553", "RefComponent": "CA-EUR-CUR", "RefTitle": "Euro: Company codes w/ non-participating currency", "RefUrl": "/notes/139553 "}, {"RefNumber": "131454", "RefComponent": "CA-EUR-CUR", "RefTitle": "EWCM for Customizing carried out manually", "RefUrl": "/notes/131454 "}, {"RefNumber": "131418", "RefComponent": "CA-EUR-CUR", "RefTitle": "EWCM Transport of new tab.entries into prod.system", "RefUrl": "/notes/131418 "}, {"RefNumber": "352354", "RefComponent": "BC-UPG", "RefTitle": "Modification adjustmnt w/ dev.classes EWC1 and EWC2", "RefUrl": "/notes/352354 "}, {"RefNumber": "185366", "RefComponent": "CA-EUR-CUR", "RefTitle": "Analysis of Crncy Customizing w/ transaction EWCM", "RefUrl": "/notes/185366 "}, {"RefNumber": "130272", "RefComponent": "CA-EUR-CUR", "RefTitle": "EWCM: SAPSQL_ARRAY_INSERT_DUPREC when setting up", "RefUrl": "/notes/130272 "}, {"RefNumber": "126737", "RefComponent": "CA-EUR-CUR", "RefTitle": "Assistant curr. Customizing euro generation error", "RefUrl": "/notes/126737 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30F", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}