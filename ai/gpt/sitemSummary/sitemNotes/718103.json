{"Request": {"Number": "718103", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 939, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000003860542017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000718103?language=E&token=1134D526A0DE02FA4CF46F2DA4FDC17C"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000718103", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000718103/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "718103"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Currentness", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.03.2004"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-CH-IS-H"}, "SAPComponentKeyText": {"_label": "Component", "value": "Hospital"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Switzerland", "value": "XX-CSC-CH", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-specific component", "value": "XX-CSC-CH-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hospital", "value": "XX-CSC-CH-IS-H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH-IS-H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "718103 - CH: Enhancements for assistant physicians and percentages"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=718103&TargetLanguage=EN&Component=XX-CSC-CH-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/718103/D\" target=\"_blank\">/notes/718103/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This SAP Note is relevant for the country version Switzerland (CH).<br />The following changes are required to transfer TARMED services to fees:</p> <UL><LI>If surgeons and assistants are entered for a TARMED service, for example: Surgeons receive only the medical portion and assistants receive only the assistant portion. If several wizards are entered, the proportion should be equal or lt. Information (percentages, equivalence numbers) can be distributed among the assistant physicians.</LI></UL> <UL><LI>For some services, more than 100% of the fee-relevant portion must be transferred to the fee.</LI></UL><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>Surgeon, assistant, assistant physicians, fee determination, fee settlement, TARMED, conditions, condition types</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>Note that problems may occur when you implement the correction instructions prior to IS-H Version 4.63B AOP 08. Therefore, contact us before you implement this SAP Note.</p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><UL><LI>New table for assigning condition types to roles. This makes it possible to assign certain roles to condition types (for example: surgeon receives only the condition &quot;Medical Contribution&quot;, the assistant receives only the condition &quot;Assistant Contribution&quot;).</LI></UL> <UL><LI>New field in the breakdown code master data for which you can define percentages when creating a new breakdown code. This percentage is taken into account in fee determination.</LI></UL><UL><LI>Changes to take the new table or field into account accordingly (changes in fee determination, maintenance dialog for breakdown code master data, and so on).</LI></UL> <p><br />Before you implement the correction instructions of this SAP Note, implement the attached attachment as follows:</p> <OL>1. Unpack the attached file Hinweis718103_463B.ZIP for IS-H Version 4.63B AOP 08 - 18 or SAP Note 718103_471.ZIP for IS-H Version 4.71 AOP 01 - 09.</OL> <p>              Note that you cannot download the attached files using OSS, but only from SAP Service Marketplace (see also SAP Notes 480180 and 13719 for information about importing attachments). <OL>2. Import the unpacked requests into your system.</OL> <OL>3. Implement the correction instructions contained in this SAP Note in your system.</OL></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Owner                                                                                    ", "Value": "C5013524"}, {"Key": "Processor                                                                                          ", "Value": "C5013524"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000718103/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "Hinweis718103_471.zip", "FileSize": "291", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000036312004&iv_version=0001&iv_guid=D6377E44ADEE9240A9C41DDD49D48514"}, {"FileName": "Hinweis718103_463B.zip", "FileSize": "308", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000036312004&iv_version=0001&iv_guid=16ACEAA9D932D44CAD5ED7C1F7D1C1BD"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "531595", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Collective Note for Release 4.63B", "RefUrl": "/notes/531595"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "531595", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Collective Note for Release 4.63B", "RefUrl": "/notes/531595 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "IS-H", "From": "463B", "To": "463B", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "471", "To": "471", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": [{"SoftwareComponentVersion": "IS-H 463B", "SupportPackage": "SAPKIPHD19", "URL": "/supportpackage/SAPKIPHD19"}, {"SoftwareComponentVersion": "IS-H 471", "SupportPackage": "SAPKIPHE10", "URL": "/supportpackage/SAPKIPHE10"}]}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 2, "URL": "/corrins/0000718103/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 10, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "545410 ", "URL": "/notes/545410 ", "Title": "IS-H CH, AT: Div. Corrections for Fee", "Component": "IS-H-PA"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "559565 ", "URL": "/notes/559565 ", "Title": "IS-H CH: Fee Determination: Error Messages for Pricing", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "572790 ", "URL": "/notes/572790 ", "Title": "IS-H CH: Fee Physicians for Non-Billable Fee-Based Services", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "611132 ", "URL": "/notes/611132 ", "Title": "CH, AT: Rounding Difference in Fee Determination", "Component": "IS-H-PA"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "636949 ", "URL": "/notes/636949 ", "Title": "IS-H AT, CH: Problems with Conditions in Fee Determination", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "650553 ", "URL": "/notes/650553 ", "Title": "IS-H: CH, AT: Conditions for Fee Determination", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "675638 ", "URL": "/notes/675638 ", "Title": "CH, AT: Problems with tax in fee determination", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "693001 ", "URL": "/notes/693001 ", "Title": "CH: Problems with pricing in fee determination", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "694556 ", "URL": "/notes/694556 ", "Title": "IS-H CH, AT: Fee: Same Physician with Different Role", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "694637 ", "URL": "/notes/694637 ", "Title": "IS-H/CH: Fee for Non-Billable Services", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "471", "ValidTo": "471", "Number": "636949 ", "URL": "/notes/636949 ", "Title": "IS-H AT, CH: Problems with Conditions in Fee Determination", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "471", "ValidTo": "471", "Number": "650553 ", "URL": "/notes/650553 ", "Title": "IS-H: CH, AT: Conditions for Fee Determination", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "471", "ValidTo": "471", "Number": "675638 ", "URL": "/notes/675638 ", "Title": "CH, AT: Problems with tax in fee determination", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "471", "ValidTo": "471", "Number": "693001 ", "URL": "/notes/693001 ", "Title": "CH: Problems with pricing in fee determination", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "471", "ValidTo": "471", "Number": "694556 ", "URL": "/notes/694556 ", "Title": "IS-H CH, AT: Fee: Same Physician with Different Role", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "471", "ValidTo": "471", "Number": "694637 ", "URL": "/notes/694637 ", "Title": "IS-H/CH: Fee for Non-Billable Services", "Component": "XX-CSC-CH-IS-H"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=718103&TargetLanguage=EN&Component=XX-CSC-CH-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/718103/D\" target=\"_blank\">/notes/718103/D</a>."}}}}