{"Request": {"Number": "208540", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 336, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001096732017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000208540?language=E&token=740E26E3267992A8671BA66D0E765E58"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000208540", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000208540/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "208540"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.08.2000"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DWB-UTL-BRR"}, "SAPComponentKeyText": {"_label": "Component", "value": "<PERSON>os<PERSON><PERSON>, Object Navigator"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "ABAP Workbench, Java IDE and Infrastructure", "value": "BC-DWB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Workbench Utilities", "value": "BC-DWB-UTL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB-UTL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "<PERSON>os<PERSON><PERSON>, Object Navigator", "value": "BC-DWB-UTL-BRR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB-UTL-BRR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "208540 - Multiple scheduling of the jobs EU_PUT and EU_REORG"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The job EU_PUT or the job EU_REORG or both jobs are scheduled several times (twice, three times, ...) and also run several times every night.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>EU_REORG, SAPRSLOG, EU_PUT, SAPRSEUT, SE80, Object Navigator, Repository Browser, Object Browser, Background, ABAP Workbench, ABAP Development</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>For the current reorganization of its tables, the ABAP Workbench needs two background jobs which should run every night. These are the jobs EU_REORG and EU_PUT.<br />These jobs are scheduled automatically. This is done by an automatic scheduling mechanism in Transaction SE80 (Object Navigator / Repository browser).<br />This scheduling mechanism first determines all jobs scheduled up to now. If the jobs are not found, the mechanism schedules the jobs again.<br />A multiple scheduling can occur due to three different error causes:</p> <OL>1. Due to a missing lock, multiple scheduling can occur if several developers call Transaction SE80 at the same time at the beginning of a working day.</OL> <OL>2. If the user who initializes the mechanism only has restricted background processing authorizations, only the background jobs of the current client are determined. If, however, the background jobs are already scheduled in another client, the described multiple scheduling occurs.</OL> <OL>3. At the beginning of a Release upgrade, all jobs with the status \"released\" are set to the status \"suspended\". At the end of the Release upgrade, these suspended jobs are then reset to the status \"released\". If during this period, Transaction SE80 is called, the scheduling mechanism might not find the jobs EU_REORG and EU_PUT and therefore schedules them again.</OL> <p>In all three cases one or both jobs EU_REORG and EU_PUT run twice or even more often than that every night.<br />This multiple running of the jobs does not cause any data inconsistencies. However, resources are of course used unnecessary.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Error cause 1: is corrected in Release 4.6B. It is corrected with Support Package 30 in Release 4.5B, with Support Package 52 in Release 4.0B. Also refer to Note 174645.<br />Error cause 2: is corrected in Release 4.6C. It is corrected with Support Package 16 in Release 4.6B, with Support Package 30 in Release 4.5B and with Support Package 52 in Release 4.0B. Also refer to Note 188790.<br />Error cause 3: is corrected in Release 4.6D. It is corrected with Support Package 1 in Release 4.6C, with Support Package 16 in Release 4.6B, with Support Package 30 in Release 4.5B and with Support Package 52 in Release 4.0B.<br />This note will replace Notes 174645 and 188790 in the future.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D000552"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000208540/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000208540/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000208540/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000208540/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000208540/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000208540/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000208540/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000208540/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000208540/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "188790", "RefComponent": "BC-DWB-UTL-BRR", "RefTitle": "Multiple scheduling of jobs EU_PUT and EU_REORG", "RefUrl": "/notes/188790"}, {"RefNumber": "18023", "RefComponent": "BC-DWB", "RefTitle": "Jobs EU_INIT, EU_REORG, EU_PUT", "RefUrl": "/notes/18023"}, {"RefNumber": "174645", "RefComponent": "BC-DWB-UTL-BRR", "RefTitle": "Multiple scheduling of the jobs EU_PUT and EU_REORG", "RefUrl": "/notes/174645"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "18023", "RefComponent": "BC-DWB", "RefTitle": "Jobs EU_INIT, EU_REORG, EU_PUT", "RefUrl": "/notes/18023 "}, {"RefNumber": "174645", "RefComponent": "BC-DWB-UTL-BRR", "RefTitle": "Multiple scheduling of the jobs EU_PUT and EU_REORG", "RefUrl": "/notes/174645 "}, {"RefNumber": "188790", "RefComponent": "BC-DWB-UTL-BRR", "RefTitle": "Multiple scheduling of jobs EU_PUT and EU_REORG", "RefUrl": "/notes/188790 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B52", "URL": "/supportpackage/SAPKH40B52"}, {"SoftwareComponentVersion": "SAP_HR 40B", "SupportPackage": "SAPKE40B52", "URL": "/supportpackage/SAPKE40B52"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B30", "URL": "/supportpackage/SAPKH45B30"}, {"SoftwareComponentVersion": "SAP_BASIS 46B", "SupportPackage": "SAPKB46B16", "URL": "/supportpackage/SAPKB46B16"}, {"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C01", "URL": "/supportpackage/SAPKB46C01"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 1, "URL": "/corrins/0000208540/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}