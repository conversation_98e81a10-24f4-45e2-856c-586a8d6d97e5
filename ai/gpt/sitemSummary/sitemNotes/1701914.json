{"Request": {"Number": "1701914", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 542, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000010113522017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001701914?language=E&token=ED7A0F3E552481C2D3AD7312C64F6CEC"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001701914", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001701914/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1701914"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.04.2012"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-LC-IT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Italy"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Real Estate Localization", "value": "RE-FX-LC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Italy", "value": "RE-FX-LC-IT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC-IT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1701914 - IRE: Tenant Transfer"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>In accordance with legal regulations, , when the tenant of a contract is changed, you can transfer the contract to the new tenant. In this case, an additional IRE record for the transfer shall be added.<br /><br />So far the IRE solution has not supported this transfer, and the contract had to be closed with a notice and a new contract had to be created for the new tenant.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RE-FX Italy, Localization for Italy, IRE Registration tax (Imposta di Registro), Transfer</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><U>Reason</U><br />The IRE (Imposta di Registro) is a tax which must be paid at the beginning of the rental contract and(, usually, annually). The tax must be paid when the end of the contract is changed due to a renewal or a notice.<br /><br />If the tenant changes during the contract validity, the contract remains the same from the fiscal authority point of view, but an additional IRE amount must be paid for the contract transfer.<br /><br /><U>Prerequisites</U><br />- apply the Italian RE-FX solution as it is described in SAP Note 872301<br />- use real estate contracts in Italy with IRE data<br />- maintain the IRE amount in the Customizing activity Maintain Time-Dependent Parameters<br />- assign the proper rental object code to the new tax code determination parameter 'C' (Transfer) in customizing Tax Codes for Rental Object Codes</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>When during the validity of a contract a new tenant leases in the real estate object, you (as the landlord) do not have to terminate the contract and create a new one. Instead, you can transfer the contract to the new tenant (business partner) on the Partners tab page.<br /><br />In this case, you need to assign the new business partner to the contract. The system then creates a new IRE record with status 'C' (Transfer), and uses the validity of the new business partner as the start date of the record.<br /><br /><U>Implementation</U><br />As a general rule, we recommend that you install a solution by<br />implementing a Support Package. However, if you need to install a solution<br />earlier, use the Note Assistant to implement the correction instruction.<br />You can find more information about the Note Assistant in SAP Service<br />Marketplace, under service.sap.com/note-assistant.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I012287)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I033770)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001701914/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001701914/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001701914/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001701914/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001701914/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001701914/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001701914/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001701914/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001701914/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Registration_Tax_Italy.pdf", "FileSize": "64", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000098182012&iv_version=0003&iv_guid=D1415EAB9AE80040A90E18987C4389F0"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "872301", "RefComponent": "RE-FX-LC-IT", "RefTitle": "RE-FX Country Version for Italy", "RefUrl": "/notes/872301"}, {"RefNumber": "1771743", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: Transfer of Ownership", "RefUrl": "/notes/1771743"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1771743", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: Transfer of Ownership", "RefUrl": "/notes/1771743 "}, {"RefNumber": "872301", "RefComponent": "RE-FX-LC-IT", "RefTitle": "RE-FX Country Version for Italy", "RefUrl": "/notes/872301 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "606", "To": "606", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-APPL 600", "SupportPackage": "SAPKGPAD22", "URL": "/supportpackage/SAPKGPAD22"}, {"SoftwareComponentVersion": "EA-APPL 602", "SupportPackage": "SAPK-60212INEAAPPL", "URL": "/supportpackage/SAPK-60212INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 603", "SupportPackage": "SAPK-60311INEAAPPL", "URL": "/supportpackage/SAPK-60311INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 604", "SupportPackage": "SAPK-60412INEAAPPL", "URL": "/supportpackage/SAPK-60412INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 605", "SupportPackage": "SAPK-60509INEAAPPL", "URL": "/supportpackage/SAPK-60509INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 606", "SupportPackage": "SAPK-60604INEAAPPL", "URL": "/supportpackage/SAPK-60604INEAAPPL"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "EA-APPL", "NumberOfCorrin": 1, "URL": "/corrins/0001701914/229"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Activity&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; EA-APPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP R/3 Enterpr...|<br/>| Release 600&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKGPAD01 - SAPKGPAD21&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 602&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-60211INEAAPPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 603&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-60310INEAAPPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 604&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-60401INEAAPPL - SAPK-60411INEAAPPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 605&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-60503INEAAPPL - SAPK-60508INEAAPPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 606&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-60601INEAAPPL - SAPK-60603INEAAPPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/></P> <h2 data-toc-skip>1. Add a new value to domain REXCITIREPYMNTTYPE</H2> <OL>1. Change domain REXCITIREPYMNTTYPE with transaction SE11</OL> <OL>2. Select tab \"Value range\"</OL> <OL>3. In section \"Single Val.\" add the following line:</OL> <OL>4. \"C\" -\"Transfer (C)\"</OL> <OL>5. Save and activate the changes</OL> <P></P> <h2 data-toc-skip>2. Add a new value to domain REXCITYEARTYPE</H2> <OL>1. Change domain REXCITYEARTYPE with transaction SE11</OL> <OL>2. Select tab \"Value range\"</OL> <OL>3. In section \"Single Val.\" add the following line:</OL> <OL>4. \"C\" -\"Transfer\"</OL> <OL>5. Save and activate the changes</OL> <h2 data-toc-skip></H2> <P></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 20, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "979757 ", "URL": "/notes/979757 ", "Title": "RE-FX Italy: IRE enhancements", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1035952 ", "URL": "/notes/1035952 ", "Title": "RE-FX Italy: Contracts with VAT and Annual Payment of IRE", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1118059 ", "URL": "/notes/1118059 ", "Title": "IRE rounding parameter does not work", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1122467 ", "URL": "/notes/1122467 ", "Title": "IRE Overview Data records interrupted by contract renewal", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1122680 ", "URL": "/notes/1122680 ", "Title": "IRE - Incorrect IRE status by renewal before Save + payback", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1129215 ", "URL": "/notes/1129215 ", "Title": "IRE Status Enhancements (notice, transfer of ownership)", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1137431 ", "URL": "/notes/1137431 ", "Title": "IRE Status Enh.(syncronization w. notice/renewal changes)", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1454091 ", "URL": "/notes/1454091 ", "Title": "'N' type IRE record for posted payback amounts", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1468460 ", "URL": "/notes/1468460 ", "Title": "IRE: the system does not create the new N (notice) record", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1473525 ", "URL": "/notes/1473525 ", "Title": "IRE:N record (notice) is not created-->corr for note 1468460", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1695075 ", "URL": "/notes/1695075 ", "Title": "IRE: Recalculated data is not stored in the database", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "602", "Number": "1677523 ", "URL": "/notes/1677523 ", "Title": "RE-FX IT: Notice activation times-out", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "604", "Number": "1399920 ", "URL": "/notes/1399920 ", "Title": "IRE Payback amount calculation incorrectly before rounding", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "605", "Number": "1491557 ", "URL": "/notes/1491557 ", "Title": "IRE:amount for advance resolution is incorrectly calculated", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "605", "Number": "1494103 ", "URL": "/notes/1494103 ", "Title": "IRE: N record is not calculated", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "605", "Number": "1504394 ", "URL": "/notes/1504394 ", "Title": "IRE: two N record for a contract", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "605", "Number": "1516045 ", "URL": "/notes/1516045 ", "Title": "IRE: Incorrect length of term for one-time pymnt and renewal", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "605", "Number": "1579671 ", "URL": "/notes/1579671 ", "Title": "IRE Payback Rate", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "606", "Number": "1652757 ", "URL": "/notes/1652757 ", "Title": "RE-FX IT: IRE payback amount is not editable in RECN", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "606", "Number": "1656374 ", "URL": "/notes/1656374 ", "Title": "RE-FX IT: Wrong IRE record(s) after contrat renewal", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1035952 ", "URL": "/notes/1035952 ", "Title": "RE-FX Italy: Contracts with VAT and Annual Payment of IRE", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1118059 ", "URL": "/notes/1118059 ", "Title": "IRE rounding parameter does not work", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1122680 ", "URL": "/notes/1122680 ", "Title": "IRE - Incorrect IRE status by renewal before Save + payback", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1129215 ", "URL": "/notes/1129215 ", "Title": "IRE Status Enhancements (notice, transfer of ownership)", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1137431 ", "URL": "/notes/1137431 ", "Title": "IRE Status Enh.(syncronization w. notice/renewal changes)", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1454091 ", "URL": "/notes/1454091 ", "Title": "'N' type IRE record for posted payback amounts", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1468460 ", "URL": "/notes/1468460 ", "Title": "IRE: the system does not create the new N (notice) record", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1473525 ", "URL": "/notes/1473525 ", "Title": "IRE:N record (notice) is not created-->corr for note 1468460", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1695075 ", "URL": "/notes/1695075 ", "Title": "IRE: Recalculated data is not stored in the database", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1035952 ", "URL": "/notes/1035952 ", "Title": "RE-FX Italy: Contracts with VAT and Annual Payment of IRE", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1118059 ", "URL": "/notes/1118059 ", "Title": "IRE rounding parameter does not work", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1122680 ", "URL": "/notes/1122680 ", "Title": "IRE - Incorrect IRE status by renewal before Save + payback", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1129215 ", "URL": "/notes/1129215 ", "Title": "IRE Status Enhancements (notice, transfer of ownership)", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1137431 ", "URL": "/notes/1137431 ", "Title": "IRE Status Enh.(syncronization w. notice/renewal changes)", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1454091 ", "URL": "/notes/1454091 ", "Title": "'N' type IRE record for posted payback amounts", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1468460 ", "URL": "/notes/1468460 ", "Title": "IRE: the system does not create the new N (notice) record", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1473525 ", "URL": "/notes/1473525 ", "Title": "IRE:N record (notice) is not created-->corr for note 1468460", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "604", "ValidTo": "604", "Number": "1122680 ", "URL": "/notes/1122680 ", "Title": "IRE - Incorrect IRE status by renewal before Save + payback", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "604", "ValidTo": "604", "Number": "1454091 ", "URL": "/notes/1454091 ", "Title": "'N' type IRE record for posted payback amounts", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "604", "ValidTo": "604", "Number": "1468460 ", "URL": "/notes/1468460 ", "Title": "IRE: the system does not create the new N (notice) record", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "604", "ValidTo": "604", "Number": "1473525 ", "URL": "/notes/1473525 ", "Title": "IRE:N record (notice) is not created-->corr for note 1468460", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "604", "ValidTo": "606", "Number": "1677523 ", "URL": "/notes/1677523 ", "Title": "RE-FX IT: Notice activation times-out", "Component": "RE-FX-LC-IT"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}