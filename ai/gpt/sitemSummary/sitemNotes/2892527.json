{"Request": {"Number": "2892527", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 536, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000273302020"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002892527?language=E&token=6F8372D032685B5B2537F53F8A39D78B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002892527", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2892527"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.03.2020"}, "SAPComponentKey": {"_label": "Component", "value": "EHS-MGM-PRC-IMD"}, "SAPComponentKeyText": {"_label": "Component", "value": "IMDS"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Environment, Health, and Safety / Product Compliance", "value": "EHS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP EHS Management", "value": "EHS-MGM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS-MGM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Product Compliance Management", "value": "EHS-MGM-PRC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS-MGM-PRC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "IMDS", "value": "EHS-MGM-PRC-IMD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS-MGM-PRC-IMD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2892527 - Supplier MDS Refers To Purchased Component"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n\r\n<p>You are using the&#160;Product Compliance&#160;functionality.</p>\r\n<p>You download the latest data changes from IMDS (International Material Data System). The system assigns matching compliance data objects and specifications to the newly created supplier MDS (Material Data Sheet) records. You find the compliance data object of the purchased component linked with the MDS records rather than the compliance data objects of the correct supplier component.</p>\r\n<p>&#160;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n\r\n<p>EHPRC_CPI04, EHPRC_CPI03, EHPRC_CPO20, Import from Application Server, CMS Daily, EHPRC_CPM_BOMBOS 048,&#160;More than one specification exists for manufacturer part ... from ...</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n\r\n<p>The symptom is caused by a program error.</p>\r\n<p>Prerequisites can be found in the relevant correction instructions.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n\r\n<p>Implement this SAP Note or import either the corresponding Support Packages for the component extension of SAP EHS Management or the Feature Packages Stacks for SAP S/4HANA.</p>\r\n\r\n<p><span style=\"text-decoration: underline;\">Note:</span> This correction changes the default exit implementation EHPRC_CP_IM52S_OBJECT_MATCH. Review the changes and adjust your custom version of this implementation as necessary.</p>\r\n<p>You have following options to correct the assignment of the supplier MDS to the compliance data object and specification.</p>\r\n<ul>\r\n<li>If you assigned the specification manually in IMDS Supplier Center you would have to re-assign the correct specification manually.</li>\r\n<li>If you already performed the import on the MDS in IMDS Supplier Center you would have to re-assign the correct specification manually.</li>\r\n<li>In the other cases the import will take care of the re-assignment to the correct specification the next time.</li>\r\n</ul>\r\n<p>&#160;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D054665)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D054619)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002892527/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002892527/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002892527/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002892527/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002892527/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002892527/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002892527/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002892527/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002892527/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3383695", "RefComponent": "EHS-MGM-PRC-IMD", "RefTitle": "IMDS Import has an insufficient Performance", "RefUrl": "/notes/3383695 "}, {"RefNumber": "2147718", "RefComponent": "XX-SER-REL", "RefTitle": "Component Extension 6.0 for SAP EHS Management: RIN", "RefUrl": "/notes/2147718 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}, {"SoftwareComponent": "EHSM", "From": "400", "To": "400", "Subsequent": ""}, {"SoftwareComponent": "EHSM", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "EHSM", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "S4CORE 100", "SupportPackage": "SAPK-10011INS4CORE", "URL": "/supportpackage/SAPK-10011INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 101", "SupportPackage": "SAPK-10109INS4CORE", "URL": "/supportpackage/SAPK-10109INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 102", "SupportPackage": "SAPK-10207INS4CORE", "URL": "/supportpackage/SAPK-10207INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 103", "SupportPackage": "SAPK-10305INS4CORE", "URL": "/supportpackage/SAPK-10305INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 104", "SupportPackage": "SAPK-10403INS4CORE", "URL": "/supportpackage/SAPK-10403INS4CORE"}, {"SoftwareComponentVersion": "EHSM 400", "SupportPackage": "SAPK-40007INEHSM", "URL": "/supportpackage/SAPK-40007INEHSM"}, {"SoftwareComponentVersion": "EHSM 500", "SupportPackage": "SAPK-50006INEHSM", "URL": "/supportpackage/SAPK-50006INEHSM"}, {"SoftwareComponentVersion": "EHSM 600", "SupportPackage": "SAPK-60007INEHSM", "URL": "/supportpackage/SAPK-60007INEHSM"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "S4CORE", "NumberOfCorrin": 5, "URL": "/corrins/0002892527/19773"}, {"SoftwareComponent": "EHSM", "NumberOfCorrin": 3, "URL": "/corrins/0002892527/9587"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 8, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 7, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "1926963 ", "URL": "/notes/1926963 ", "Title": "Issues in IMDS Centers and import of MDS files", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2058981 ", "URL": "/notes/2058981 ", "Title": "Errors in IMDS Import", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2325787 ", "URL": "/notes/2325787 ", "Title": "Import: Avoid dumps due to messages without message type", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2476343 ", "URL": "/notes/2476343 ", "Title": "IMDS import: Correction for file encoding", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2541384 ", "URL": "/notes/2541384 ", "Title": "IMDS: Corrections in import of own and external Material Data Sheets", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2580412 ", "URL": "/notes/2580412 ", "Title": "IMDS - Inconsistent Recipient Entries After Deletion", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "600", "Number": "2497662 ", "URL": "/notes/2497662 ", "Title": "IMDS - recipient entries in status &#x201E;Edit&#x201C; cannot be deleted", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "500", "ValidTo": "500", "Number": "2325787 ", "URL": "/notes/2325787 ", "Title": "Import: Avoid dumps due to messages without message type", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "500", "ValidTo": "500", "Number": "2476343 ", "URL": "/notes/2476343 ", "Title": "IMDS import: Correction for file encoding", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "500", "ValidTo": "500", "Number": "2541384 ", "URL": "/notes/2541384 ", "Title": "IMDS: Corrections in import of own and external Material Data Sheets", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "500", "ValidTo": "500", "Number": "2580412 ", "URL": "/notes/2580412 ", "Title": "IMDS - Inconsistent Recipient Entries After Deletion", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "600", "ValidTo": "600", "Number": "2325787 ", "URL": "/notes/2325787 ", "Title": "Import: Avoid dumps due to messages without message type", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "600", "ValidTo": "600", "Number": "2476343 ", "URL": "/notes/2476343 ", "Title": "IMDS import: Correction for file encoding", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "600", "ValidTo": "600", "Number": "2541384 ", "URL": "/notes/2541384 ", "Title": "IMDS: Corrections in import of own and external Material Data Sheets", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "600", "ValidTo": "600", "Number": "2580412 ", "URL": "/notes/2580412 ", "Title": "IMDS - Inconsistent Recipient Entries After Deletion", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2325787 ", "URL": "/notes/2325787 ", "Title": "Import: Avoid dumps due to messages without message type", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2476343 ", "URL": "/notes/2476343 ", "Title": "IMDS import: Correction for file encoding", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2497662 ", "URL": "/notes/2497662 ", "Title": "IMDS - recipient entries in status &#x201E;Edit&#x201C; cannot be deleted", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2541384 ", "URL": "/notes/2541384 ", "Title": "IMDS: Corrections in import of own and external Material Data Sheets", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2580412 ", "URL": "/notes/2580412 ", "Title": "IMDS - Inconsistent Recipient Entries After Deletion", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2476343 ", "URL": "/notes/2476343 ", "Title": "IMDS import: Correction for file encoding", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2497662 ", "URL": "/notes/2497662 ", "Title": "IMDS - recipient entries in status &#x201E;Edit&#x201C; cannot be deleted", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2541384 ", "URL": "/notes/2541384 ", "Title": "IMDS: Corrections in import of own and external Material Data Sheets", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2580412 ", "URL": "/notes/2580412 ", "Title": "IMDS - Inconsistent Recipient Entries After Deletion", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "102", "ValidTo": "102", "Number": "2497662 ", "URL": "/notes/2497662 ", "Title": "IMDS - recipient entries in status &#x201E;Edit&#x201C; cannot be deleted", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "102", "ValidTo": "102", "Number": "2541384 ", "URL": "/notes/2541384 ", "Title": "IMDS: Corrections in import of own and external Material Data Sheets", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "102", "ValidTo": "102", "Number": "2580412 ", "URL": "/notes/2580412 ", "Title": "IMDS - Inconsistent Recipient Entries After Deletion", "Component": "EHS-MGM-PRC-IMD"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}