{"Request": {"Number": "821148", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 296, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016000842017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000821148?language=E&token=0037DDC2D14E84DD0AF2781178D29414"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000821148", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000821148/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "821148"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.05.2006"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-ADDON"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade Add-On Components"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade Add-On Components", "value": "BC-UPG-ADDON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-ADDON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "821148 - Installing/upgrading Basis Plug-in (PI_BASIS) 2005.1"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p><br />This note contains information about <B>Basis Plug-In 2005.1</B> regarding<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Add-on installation<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Add-on delta upgrade<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Add-On Support Packages<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Language support<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p><br />SAINT, add-on, installation/delta upgrade, PI_BASIS, AOI, AOU, AOP<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><br /><br /></p> <UL><LI>You want to install Basis Plug-In (PI_BASIS) 2005.1.</LI></UL> <UL><LI>PI_BASIS is already installed, and you want to carry out an add-on delta upgrade.</LI></UL> <UL><LI>You want to import Support Packages for PI_BASIS 2005.1.</LI></UL> <UL><LI>If you intend to include PI_BASIS 2005.1 in an upgrade (add-on supplement CD), see Note <B>570810</B>.</LI></UL> <p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br />0. Change history</p> <OL>1. General information</OL> <OL>2. Installation/delta upgrade</OL> <OL><OL>a) Installing PI_BASIS on systems with Basis 620, 640 or Basis 700</OL></OL> <OL><OL>b) Prerequisites</OL></OL> <OL><OL>c) Preparation</OL></OL> <OL><OL>d) Execution</OL></OL> <OL><OL>e) Postprocessing</OL></OL> <OL>3. Support Packages</OL> <p><br /><B>Caution: This note is updated regularly.</B> You must therefore read it once again before beginning the installation.<br /><br />0. Change history<br /><br />Date&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Section&#x00A0;&#x00A0;&#x00A0;&#x00A0; Short description<br />------------------------------------------------------------------------<br />DEC/21/2005&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Material numbers updated.<br />Jan/19/2006&#x00A0;&#x00A0;div.&#x00A0;&#x00A0;&#x00A0;&#x00A0;Adjustment to current SAP terminology<br />Apr/19/2006&#x00A0;&#x00A0;div.&#x00A0;&#x00A0;&#x00A0;&#x00A0;Revision of PI 2004 Support Package 10 and PI_BASIS<br /> <br /><br /></p> <OL>1. Release PI_BASIS 2005.1 for R/3 Enterprise, ECC 500<br />You are <B>not</B> allowed to use PI_BASIS 2005.1 with PI 2003.1 or lower is. <B>Using PI_BASIS 2005.1 with PI 2004.1 requires patch level PI 10.</B> After the PI_BASIS 2005.1 upgrade, upgrade your PI 2004.1 installation to at least patch level 10. </OL> <OL>2. General information<br />The release information for PI_BASIS 2005.1 is available in the INFO directory on the CDs.</OL> <OL>3. Installing PI_BASIS</OL> <OL><OL>a) Installing PI_BASIS on systems with Basis 620, 640 or Basis 700.<br />This note descibes the installation process of PI_BASIS 2005.1 on a system with Basis 620 or 640. PI_BASIS 2005.1 is already an integral part of NW04S (and all application on Basis 700) and is therefore not covered by this note.</OL></OL> <OL><OL>b) R3trans version<br />Make sure that you use the current R3trans version for the installation.</OL></OL> <OL><OL>c) Prerequisites for the installation and upgrade<br />You can only install PI_BASIS 2005.1 on a system with Basis 620. You can carry out the delta upgrade PI_BASIS 2005.1 on systems with Basis 620 and Basis 640. PI_BASIS 2005.1 is an integral part of NW04S and all applications based on NW04S.Therefore you do not have to install it seperately.</OL></OL> <UL><UL><LI>Import the latest SPAM/SAINT update.<br />Make sure that you installed the latest SPAM/SAINT update into your system.</LI></UL></UL> <UL><UL><LI>Refer to the following notes before the installation:<br /></LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>Add-ons Conditions</TD><TD> 70228</TD><TD> </TD></TR> <TR><TD>Overview note</TD><TD> -</TD><TD> </TD></TR> <TR><TD>Release strategy note</TD><TD> -</TD><TD> </TD></TR> <TR><TD>Known problems with transaction SAINT</TD><TD> 484219</TD><TD> </TD></TR> </TABLE></UL></UL> <p></p> <UL><UL><LI>Check whether the following prerequisites are met in your system:<br /><br /><B>Basis 620:</B><br /><br />Required components (see table CVERS):<br />SAP_BASIS 620<br />SAP_ABA 620<br /><br />Required Support Packages:<br />SAP_BASIS 620: 0014<br />SAP_ABA&#x00A0;&#x00A0;620: 0014<br /><br />Required components and Support Packages:<br />for R/3 Enterprise Systems: PI 2002_1_470, Support Package 0003 or at least PI Release 2002_2_470<br />for CRM 310 systems: BBPCRM 310, Support Package 0002<br />for CRM 350 systems: BBPCRM 350, Support Package 0003<br /><br /><B>Basis 640:</B><br /><br />Required components (see table CVERS):<br />SAP_BASIS 640<br />SAP_ABA 640<br /><br />Required Support Packages:<br />SAP_BASIS 640: 0001<br />SAP_ABA&#x00A0;&#x00A0;640: 0001<br /><br />Other components or Support Packages:<br />No further requirements (This also applies to the required PI_BASIS source release for the delta upgrade. in other words, every source release is supported).<br /><br />Memory required in the transport directory: 50MB<br /></LI></UL></UL> <UL><UL><LI>The Basis Plug-In PI_BASIS can be used together with all other add-ons.</LI></UL></UL> <UL><UL><LI>The technical name for PI_BASIS 2005.1 is 2005_1_&lt;REL&gt;, for example, for 620: PI_BASIS 2005_1_620</LI></UL></UL> <UL><UL><LI>R/3 Plug-In and Basis Plug-In (R/3 Enterprise, ECC 500 only)<br />In SAP R/3 Enterprise, the SAP Basis Plug-In is a prerequisite for the SAP R/3 Plug-In. You are <B>not</B> allowed to use PI_BASIS 2005.1 with PI 2003.1 or lower is. Using PI_BASIS 2005.1 with PI 2004.1 requires patch level PI 10.<br />However, when you upgrade PI and PI_BASIS in the SAP R/3 Enterprise system, you do not have to upgrade PI_BASIS in all other SAP systems of your system landscape.</LI></UL></UL> <OL><OL>d) Preparations for the installation/delta upgrade of PI_BASIS 2005.1</OL></OL> <UL><UL><LI>Download the CD (material number <B>51030955</B> for PI_BASIS 2005_1_620, <B>51030956</B> for PI_BASIS 2005_1_640) from SAP Service Marketplace (quick link installations) into a temporary directory.</LI></UL></UL> <UL><UL><LI>Log on with the following users:<br />&lt;sid&gt;adm&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on UNIX,<br />&lt;SID&gt;OFR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on AS/400,<br />&lt;SID&gt;adm&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on Windows NT</LI></UL></UL> <UL><UL><LI>Change to the transport directory of your SAP System. You can call the transport directory under the DIR_TRANS parameter in transaction AL11.</LI></UL></UL> <UL><UL><LI>Unpack the SAPCAR archive KINBB7A.SAR (for PI_BASIS 2005_1_620) or KINBB8A.SAR (for PI_BASIS 2005_1_640) with the following statement:<br />SAPCAR -xvf /&lt;DIR_TEMP&gt;/&lt;ARCHIV&gt;.SAR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on UNIX,<br />SAPCAR '-xvf /QOPT/&lt;VOLID&gt;/DATA/&lt;ARCHIV&gt;.SAR'&#x00A0;&#x00A0;&#x00A0;&#x00A0;on AS/400,<br />SAPCAR -xvf &lt;DIR_TEMP&gt;\\&lt;ARCHIV&gt;.SAR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on Windows NT.</LI></UL></UL> <UL><UL><LI>The SAPCAR archive for installation and delta upgrade is in the DELT_&lt;REL&gt; directory.</LI></UL></UL> <UL><UL><LI>Depending on the Basis Release of the system, you can now find the corresponding file in the /EPS/in directory in the following file:<br />Installation/delta upgrade 2005_1_620:&#x00A0;&#x00A0;CSN0120061532_0020779.PAT<br />Delta upgrade 2005_1_640:&#x00A0;&#x00A0;CSR0120031469_0020648.PAT</LI></UL></UL> <OL><OL>e) Executing the installation/delta upgrade of PI_BASIS 2005.1</OL></OL> <UL><UL><LI>Log on to client 000 as a user with SAP_ALL authorization. Do NOT use SAP* or DDIC.</LI></UL></UL> <UL><UL><LI>Display the installation/upgrade packages for the add-on installation:<br />Call transaction SAINT and choose 'Load'. After the list of loaded packages is displayed, choose F3 or Back to return to the initial screen of transaction SAINT.</LI></UL></UL> <UL><UL><LI>Start the installation or the upgrade:<br />Call transaction SAINT, and select the PI_BASIS 2005_1_&lt;REL&gt; add-on. Choose the 'Continue'. If your system meets all the prerequisites for importing the add-on, the relevant queue is displayed. The queue contains the installation package, and may contain Support Packages and Add-On Support Package. For more information, see the section \"General Information\".<br />Choose \"Continue\" to start the installation. To obtain more information, call transaction SAINT and choose the relevant button in the application toolbar.</LI></UL></UL> <OL><OL>f) Passwords</OL></OL> <UL><UL><LI>Installation and delta upgrade PI_BASIS 2005_1_620 (SAPKINBB7A): <B>6AD13FA7C9</B></LI></UL></UL> <UL><UL><LI>Delta upgrade PI_BASIS 2005_1_640 (SAPKINBB8A): <B>6AD13FA8C9</B></LI></UL></UL> <OL><OL>g) Postprocessing the installation of PI_BASIS 2005.1</OL></OL> <UL><UL><LI>Import all available Support Packages for PI_BASIS 2005.1 after the installation (Stage 2).</LI></UL></UL> <OL>4. Support Packages for PI_BASIS 2005.1<br />The Support Packages are located on SAP Service Marketplace (quick link PATCHES). For information about PI_BASIS Support Packages, see Note 553527.</OL> <OL>5. Language support<br />PI_BASIS 2005.1 supports all standard languages, which include German, English and the following languages:<br />AR Arabic<br />BG Bulgarian<br />ZH Chinese<br />ZF Chinese trad.<br />HR Croatian<br />DA Danish<br />FI Finnish<br />FR French<br />EL Greek<br />HE Hebrew<br />IT Italian<br />JA Japanese<br />KO Korean<br />NL Dutch<br />NO Norwegian<br />PL Polish<br />PT Portuguese<br />RO Romanian<br />RU Russian<br />SV Swedish<br />SH Serbo-Croatian<br />SK Slovakian<br />SL Slowenian<br />ES Spanish<br />TH Thai<br />CS Czech<br />TR Turkish<br />HU Hungarian<br /><br />If you wish to install Hebrew, contact your local Support team.<br /><br />The PI_BASIS languages for the PI_BASIS release provided with an ABAP server are contained in the language transports of the ABAP server. The languages for this PI_BASIS release are then automatically available on the ABAP server when additional languages are subsequently installed.<br />However, if a more current PI_BASIS release is installed on the ABAP server and if you want to install another language of the ABAP server, the PI_BASIS section of this language corresponds to the status of the PI_BASIS release delivered with the ABAP server after the language transport. Proceed as described in Note 846476.</OL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D032986)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D028323)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000821148/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000821148/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000821148/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000821148/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000821148/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000821148/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000821148/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000821148/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000821148/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Release_Notes_PI_Basis_2005_1.zip", "FileSize": "522", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000169882005&iv_version=0007&iv_guid=586B37033020CF44814630373B7A7E7F"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "903242", "RefComponent": "BC-BW", "RefTitle": "CONVERT_NO_NUMBER while processing table ROVERCUBE1", "RefUrl": "/notes/903242"}, {"RefNumber": "897807", "RefComponent": "CRM-BF", "RefTitle": "CRM 3.1 SP Stack 12 (12/2005): Release & Inf. Note", "RefUrl": "/notes/897807"}, {"RefNumber": "846476", "RefComponent": "BC-UPG-ADDON", "RefTitle": "PI_BASIS 2005.1: Subsequent installation of languages", "RefUrl": "/notes/846476"}, {"RefNumber": "555060", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Overview: Notes about the Add-On PI_BASIS", "RefUrl": "/notes/555060"}, {"RefNumber": "553527", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Support Packages for the PI_BASIS (Basis plug-in)", "RefUrl": "/notes/553527"}, {"RefNumber": "153967", "RefComponent": "BW-BCT-GEN", "RefTitle": "BI Content Release Strategy", "RefUrl": "/notes/153967"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "153967", "RefComponent": "BW-BCT-GEN", "RefTitle": "BI Content Release Strategy", "RefUrl": "/notes/153967 "}, {"RefNumber": "897807", "RefComponent": "CRM-BF", "RefTitle": "CRM 3.1 SP Stack 12 (12/2005): Release & Inf. Note", "RefUrl": "/notes/897807 "}, {"RefNumber": "553527", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Support Packages for the PI_BASIS (Basis plug-in)", "RefUrl": "/notes/553527 "}, {"RefNumber": "555060", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Overview: Notes about the Add-On PI_BASIS", "RefUrl": "/notes/555060 "}, {"RefNumber": "903242", "RefComponent": "BC-BW", "RefTitle": "CONVERT_NO_NUMBER while processing table ROVERCUBE1", "RefUrl": "/notes/903242 "}, {"RefNumber": "846476", "RefComponent": "BC-UPG-ADDON", "RefTitle": "PI_BASIS 2005.1: Subsequent installation of languages", "RefUrl": "/notes/846476 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "PI_BASIS", "From": "2005_1_620", "To": "2005_1_640", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}