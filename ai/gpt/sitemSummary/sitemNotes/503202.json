{"Request": {"Number": "503202", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 273, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000002366822017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000503202?language=E&token=C4A6FEB464CDFBB25541A31F939C8E6F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000503202", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000503202/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "503202"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.06.2002"}, "SAPComponentKey": {"_label": "Component", "value": "CRM-MSA-BP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Use CRM-MSA(Business Partner)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Customer Relationship Management", "value": "CRM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Mobile Sales", "value": "CRM-MSA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM-MSA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Use CRM-MSA(Business Partner)", "value": "CRM-MSA-BP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM-MSA-BP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "503202 - R/3 customer hierarchy not visible at the CRM mobile client"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>R/3 customer hierachy downloaded to CRM and CDB can not be<br />viewed at the mobile client correctly. In the tilesets Group Hierarchy<br />Specific to Business Partners and General Group Hierarchy the R/3<br />customer hierarchy is not displayed completely. Other applications<br />(i.e. listings, pricing) based on the group hierarchy can not use<br />this information.<br />Inconsistent data in the table SMONODEREL valid_from &gt; valid_to .</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Group hierarchy , SMOBUHI1, SMORELATION, SMONODEREL</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Installation of CRM Mobile Sales 3.0 SP6, SP7 or SP8<br />Downloading the customer hierarchy from R/3 to CRM Online and CDB</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Please get the following files from<br />ftp://sapserv3/specific/crm/CRM30/MSA/note503202/ :<br /><br />ARSUpdate.sar<br />K066165.PIC<br />K068724.PIC<br />R066165.PIC<br />R068724.PIC<br /><br />1. Client Changes :<br />The&#x00A0;&#x00A0;ARSUpdate.sar includes all the client relevant changes. Please<br />extract the file using SAPCAR. The handling is the same as for a normal<br />arsupdate. This tool imports the necessary client changes on your<br />repository depending on your installed support package.If you upgrade later on to a higher support package before SP9, you must run the tool<br />again. The tool will then make the necessary changes for this SP.<br />If you have upgraded to support package SP09 or above, then running of<br />arsupdate.sar isn't necessary anymore.<br /><br />The following 2 notes need to be applied after running of arsupdate.sar<br />1. Note 521058 - General Group Hierarchy tileset does not display trees<br />2. Note 526289 - Group Hierarchy; wrong path to root is returned<br /><br />2. BDOC Changes:<br />The other files (K066165.PIC, R066165.PIC, K068724.PIC,R068724.PIC)<br />include the corresponding BDOC changes. Please import them as mentioned<br />in the refered note 13719.<br />The following BDOC's are included:<br /><br />CAPBUHI1KNA1_QUERY<br />CAPBUHI1_QUERY<br />CAPBUHISALESAREA_QUERY<br />CAPNODEVALIDTO_QUERY<br />CAPNODEVALID_QUERY<br />CAPSALEGRPTREE_QUERY<br />CAPSALENODE_QUERY<br /><br />For this BDOC's you have to generate the stored procedures using the<br />client console and distribute it to the other laptops together with the<br />new TPS file.<br /><br />3. Adapter changes :<br />The attached correction instruction fixes the problem of wrong<br />entries in the tablefields SMONODEREL VALID_FROM, VALID_TO.<br /><br />4. TL Changes :<br />After applying all the above 3 steps, the following note for TL<br />corrections has to be applied, for the functionality to work correctly:<br />Note 497748 : Transaction Layer Hotfix for 3.0 Mobile Clients<br /><br />5. Changes in other components to support R/3 hierarchy:<br />Listings at the client can use (customizable) the R/3 hierarchy from<br />SP9 onwards, pricing from SP10 onwards.<br /><br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CRM-MSA (Mobile Sales)"}, {"Key": "Transaction codes", "Value": "TIME"}, {"Key": "Transaction codes", "Value": "FILE"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D029329)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D029329)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000503202/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000503202/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000503202/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000503202/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000503202/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000503202/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000503202/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000503202/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000503202/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "544029", "RefComponent": "CRM-MSA-BP", "RefTitle": "Cannot expand child nodes for R/3 Group Hierarchy", "RefUrl": "/notes/544029"}, {"RefNumber": "526289", "RefComponent": "CRM-MSA-BP", "RefTitle": "Group Hierarchy; wrong path to root is returned", "RefUrl": "/notes/526289"}, {"RefNumber": "521058", "RefComponent": "CRM-MSA-BP", "RefTitle": "General Group Hierarchy tileset does not display trees", "RefUrl": "/notes/521058"}, {"RefNumber": "521051", "RefComponent": "CRM-MSA-IPC-PRI", "RefTitle": "R/3 Business Partner Hierarchy for Pricing", "RefUrl": "/notes/521051"}, {"RefNumber": "508712", "RefComponent": "CRM-MD-BP-IF", "RefTitle": "Download of R/3 customer hierarchy not equal to category 'A'", "RefUrl": "/notes/508712"}, {"RefNumber": "506648", "RefComponent": "CRM-MD-BP-IF", "RefTitle": "Runtime error in Transaction BPH_DNL", "RefUrl": "/notes/506648"}, {"RefNumber": "505942", "RefComponent": "CRM-MSA-ADP", "RefTitle": "Initial download of BUHI_MAIN(BP Group Hierarchy)", "RefUrl": "/notes/505942"}, {"RefNumber": "500087", "RefComponent": "CRM-MD-BP", "RefTitle": "Initial Download Group hierarchy CRM -> CDB", "RefUrl": "/notes/500087"}, {"RefNumber": "497748", "RefComponent": "CRM-MW-COM", "RefTitle": "Middleware HOTFIX for Mobile Clients: CRM 3.0 SP00 - SP18", "RefUrl": "/notes/497748"}, {"RefNumber": "494873", "RefComponent": "CRM-MD-BP-IF", "RefTitle": "R/3 customer hierarchy and CRM group hierarchy", "RefUrl": "/notes/494873"}, {"RefNumber": "486203", "RefComponent": "CRM-MD-BP", "RefTitle": "Non-defined sales areas in CRM System cause errors", "RefUrl": "/notes/486203"}, {"RefNumber": "454138", "RefComponent": "CRM-MD-BP", "RefTitle": "Change of customer hierarchy does not arrive in CRM", "RefUrl": "/notes/454138"}, {"RefNumber": "421281", "RefComponent": "CRM-MSA", "RefTitle": "mySAP CRM 3.0 / 3.1 Mobile Client Component", "RefUrl": "/notes/421281"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "494873", "RefComponent": "CRM-MD-BP-IF", "RefTitle": "R/3 customer hierarchy and CRM group hierarchy", "RefUrl": "/notes/494873 "}, {"RefNumber": "506648", "RefComponent": "CRM-MD-BP-IF", "RefTitle": "Runtime error in Transaction BPH_DNL", "RefUrl": "/notes/506648 "}, {"RefNumber": "454138", "RefComponent": "CRM-MD-BP", "RefTitle": "Change of customer hierarchy does not arrive in CRM", "RefUrl": "/notes/454138 "}, {"RefNumber": "500087", "RefComponent": "CRM-MD-BP", "RefTitle": "Initial Download Group hierarchy CRM -> CDB", "RefUrl": "/notes/500087 "}, {"RefNumber": "521058", "RefComponent": "CRM-MSA-BP", "RefTitle": "General Group Hierarchy tileset does not display trees", "RefUrl": "/notes/521058 "}, {"RefNumber": "526289", "RefComponent": "CRM-MSA-BP", "RefTitle": "Group Hierarchy; wrong path to root is returned", "RefUrl": "/notes/526289 "}, {"RefNumber": "497748", "RefComponent": "CRM-MW-COM", "RefTitle": "Middleware HOTFIX for Mobile Clients: CRM 3.0 SP00 - SP18", "RefUrl": "/notes/497748 "}, {"RefNumber": "421281", "RefComponent": "CRM-MSA", "RefTitle": "mySAP CRM 3.0 / 3.1 Mobile Client Component", "RefUrl": "/notes/421281 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "508712", "RefComponent": "CRM-MD-BP-IF", "RefTitle": "Download of R/3 customer hierarchy not equal to category 'A'", "RefUrl": "/notes/508712 "}, {"RefNumber": "486203", "RefComponent": "CRM-MD-BP", "RefTitle": "Non-defined sales areas in CRM System cause errors", "RefUrl": "/notes/486203 "}, {"RefNumber": "544029", "RefComponent": "CRM-MSA-BP", "RefTitle": "Cannot expand child nodes for R/3 Group Hierarchy", "RefUrl": "/notes/544029 "}, {"RefNumber": "521051", "RefComponent": "CRM-MSA-IPC-PRI", "RefTitle": "R/3 Business Partner Hierarchy for Pricing", "RefUrl": "/notes/521051 "}, {"RefNumber": "505942", "RefComponent": "CRM-MSA-ADP", "RefTitle": "Initial download of BUHI_MAIN(BP Group Hierarchy)", "RefUrl": "/notes/505942 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BBPCRM", "From": "300", "To": "300", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "BBPCRM 300", "SupportPackage": "SAPKU30009", "URL": "/supportpackage/SAPKU30009"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "BBPCRM", "NumberOfCorrin": 1, "URL": "/corrins/0000503202/63"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}