{"Request": {"Number": "926919", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 440, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016064502017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000926919?language=E&token=9DB7A2C8B2766799DBC691A80D9DA735"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000926919", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000926919/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "926919"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.01.2013"}, "SAPComponentKey": {"_label": "Component", "value": "BW-SYS-DB-DB6"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW DB2 Universal Database"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basis System and Installation", "value": "BW-SYS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-SYS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW Database Platforms", "value": "BW-SYS-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-SYS-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW DB2 Universal Database", "value": "BW-SYS-DB-DB6", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-SYS-DB-DB6*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "926919 - DB6: DB2 9 Data row compression for SAP NetWeaver 2004s BI"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You want to activate DB2 9 Deep Compression for PSA tables, DataStore object tables and fact tables in the BW environment to save disk space.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>InfoCube, DataStore, PSA, DB2 9 Deep Compression, ODS, DB6, DB2, UDB<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You are using a DB2 9 database for Linux, UNIX, and Windows.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Deep Compression in DB2 9 for Linux, UNIX, and Windows uses static dictionaries (compression dictionaries) to compress the data rows of tables. The dictionaries are assigned to the tables. For each database partition that contains the table, one compression dictionary is created and stored there as part of the table. The compression dictionary can be generated from a subset of the data; 5% to 10% is usually sufficient. The table must contain at least 1 MB of data on each database partition. Only data objects are compressed, not indexes, LONG, LOB or XML data. The data is compressed and stored on the disk and in the buffer pool.  The log records of compressed data are also compressed. This not only reduces disk space and input/output (I/O), but also increases the buffer pool hit ratio.<br /><br />Internal tests and tests with customer data have shown that compression rates between 40% and 80% are achieved. Up to 10 % additional CPU resources are required to compress and decompress the data records. Depending on the compression rate, the I/O may decrease considerably.<br /><br />The Support Packages listed below provide the following support for Deep Compression in SAP NetWeaver 2004s BI:</p> <UL><LI>There is a new RSADMIN parameter, DB6_ROW_COMPRESSION, which is set to NO by default. If you set this parameter to YES using report SAP_RSADMIN_MAINTAIN, all newly generated PSA tables, ODS tables, and fact tables with the option COMPRESS YES are created on the database.</LI></UL> <UL><LI>Data has to be loaded into the tables before you can create the compression dictionary. You must therefore load one or more InfoPackages into the PSA tables, ODS objects or InfoCubes. There are two options:</LI></UL> <UL><UL><LI>You carry out an offline table reorganization. During this process, the compression dictionary is created and all data is compressed.</LI></UL></UL> <UL><UL><LI>You execute command DB2 INSPECT with the option ROWCOMPESTIMATE. During this process, the system only creates the compression dictionary that is used to compress all data records that are subsequently added or changed. Existing data is not compressed.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can also use transaction RSRV to create the compression dictionaries.<br /><br />Transaction RSRV contains two new tests in the \"Database\" folder. You can use these tests to check whether Deep Compression is activated for InfoProvider and PSA tables, how big the tables are and whether there are compression dictionaries. The names of the test are:</p> <UL><LI>\"Data row compression of InfoProviders in DB2 f. Linux, UNIX and Windows\" and</LI></UL> <UL><LI>\"Data row compression of PSA database tables in DB2 f. Linux, UNIX and Windows\".</LI></UL> <p><br />A repair function is also provided. You can use it to trigger the offline reorganization or the execution of INSPECT.<br /><br />When you use the InfoProvider test for an InfoCube, the system checks the fact tables and the fact tables of the aggregates. If Deep Compression is activated, the table contains data and there is no compression dictionary, the test fails. When you carry out the test for ODS objects, the system checks all tables that belong to the ODS object. When you carry out the test for PSA tables, enter the PSA table to be checked.<br /><br />If the test fails, you can use the repair switch to schedule an offline reorganization or the execution of INSPECT as a background job for the affected tables.&#x00A0;&#x00A0;You can use the 'Reorg_Table' job from transaction DB13 for the offline reorganization. The job is visible in transaction SM37 as a job with the prefix DBA:REORG_TABLE#. The INSPECT is done using the DB13 job 'Reorgck_All', seen in transaction SM37 with the prefix DBA:REORGCK_ALL#. You can call the job logs from either DB13 or from transaction SM37.<br /><br />The system may not be able to compress certain tables because they are too narrow and during compression the line length was below the specified minimum. In this case, the job log contains the relevant error message. We recommend that in this case, you deactivate Deep Compression for the affected tables at database level. You can do this using the command \"ALTER TABLE &lt;tabname&gt; COMPRESS NO\".<br /><br />You need the following Support Package:<br /></p> <UL><LI>SAP NetWeaver 2004s BI</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 08 for SAP NetWeaver 2004s BI (BI Patch 08 or <B>SAPKW70008</B>) into your BI system. The Support Package is available once <B>Note 872280 </B>\"SAPBINews BI 7.0 Support Package08\", which describes this Support Package in more detail, has been released for customers.<br /><br />To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note still contains the words \"Preliminary version\".</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-SYS-DB (BW Database Platforms)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5028876)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON> (D021078)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000926919/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000926919/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000926919/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000926919/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000926919/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000926919/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000926919/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000926919/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000926919/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "937377", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Activating row compression in the BW environment", "RefUrl": "/notes/937377"}, {"RefNumber": "930487", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: Using DB2 V9.1 with SAP Software", "RefUrl": "/notes/930487"}, {"RefNumber": "914303", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews NW BW 7.00 ABAP SP9", "RefUrl": "/notes/914303"}, {"RefNumber": "1055182", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Reclustering and Compression", "RefUrl": "/notes/1055182"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "930487", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: Using DB2 V9.1 with SAP Software", "RefUrl": "/notes/930487 "}, {"RefNumber": "1055182", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Reclustering and Compression", "RefUrl": "/notes/1055182 "}, {"RefNumber": "914303", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews NW BW 7.00 ABAP SP9", "RefUrl": "/notes/914303 "}, {"RefNumber": "937377", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Activating row compression in the BW environment", "RefUrl": "/notes/937377 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "710", "To": "710", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW_VIRTUAL_COMP 700", "SupportPackage": "SAPK-70008INVCBWTECH", "URL": "/supportpackage/SAPK-70008INVCBWTECH"}, {"SoftwareComponentVersion": "SAP_BW_VIRTUAL_COMP 700", "SupportPackage": "SAPK-70009INVCBWTECH", "URL": "/supportpackage/SAPK-70009INVCBWTECH"}, {"SoftwareComponentVersion": "SAP_BW 700", "SupportPackage": "SAPKW70009", "URL": "/supportpackage/SAPKW70009"}, {"SoftwareComponentVersion": "SAP_BW 700", "SupportPackage": "SAPKW70008", "URL": "/supportpackage/SAPKW70008"}, {"SoftwareComponentVersion": "SAP_BW 700", "SupportPackage": "SAPKW70008", "URL": "/supportpackage/SAPKW70008"}, {"SoftwareComponentVersion": "SAP_BW 700", "SupportPackage": "SAPKW70009", "URL": "/supportpackage/SAPKW70009"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}