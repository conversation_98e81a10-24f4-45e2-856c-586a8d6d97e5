{"Request": {"Number": "493318", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 277, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015166602017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000493318?language=E&token=1F914077F964CEE00A60C4B9666ABE1C"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000493318", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000493318/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "493318"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 18}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.12.2013"}, "SAPComponentKey": {"_label": "Component", "value": "MM-PUR-REQ-GUI"}, "SAPComponentKeyText": {"_label": "Component", "value": "Userinterface - Purchase Requisitions"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Purchasing", "value": "MM-PUR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Purchase Requisitions", "value": "MM-PUR-REQ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-REQ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Userinterface - Purchase Requisitions", "value": "MM-PUR-REQ-GUI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-REQ-GUI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "493318 - FAQ: Purchase requisition (ME51N, ME52N, ME53N)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note contains a list of frequently asked questions relating to purchase requisition (ME51N, ME52N, ME53N). For general questions on purchase requisition (PReq), refer to FAQ Note 493315.</p>\r\n<p><strong>Catalog of questions</strong></p>\r\n<p>1. Where can I find <strong>detailed information about transactions ME21N, ME22N, and ME23N</strong> in purchasing?</p>\r\n<p>2. Where can I define whether the <strong>purchase requisition price</strong> is to be transferred to the purchase order as net or gross price?</p>\r\n<p>3. Why do the <strong>field exits</strong> no longer work in the purchase requisition?</p>\r\n<p>4. Why is only a limited use of <strong>screen variants</strong> and <strong>transaction variants</strong> possible with the new transactions?</p>\r\n<p>5. How can I change and save a <strong>standard display variant</strong> for the document overview?</p>\r\n<p>6. Why does the <strong>field selection key</strong> ACTV not have the same effect as in the old purchase requisition (transaction ME51)?</p>\r\n<p>7. Why does the field EBAN-BATOL (<strong>Resubmission Interval</strong> of Purchase Requisition) no longer exist in the new purchase requisition?</p>\r\n<p>8. Why are <strong>batch input</strong> and <strong>CATT</strong> not possible in the purchase requisition (ME51N, ME52N, ME53N)?</p>\r\n<p>9. Why is transaction ME53 called instead of transaction ME53N <strong>(or vice versa)</strong> in some applications?</p>\r\n<p>10. Why is it not possible to use transaction ME52N to change the <strong>price-influencing fields</strong> (for example, the material group) for some purchase requisitions?</p>\r\n<p>11. Why is the <strong>ALV Grid Control</strong> not available in the <strong>WebGUI</strong>?</p>\r\n<p>12. Why are the source determination fields (for example, the purchasing organization) only available on the &quot;Source of Supply&quot; tab page in the <strong>WebGUI</strong> and not in the item overview?</p>\r\n<p>13. Why are some functions of the item overview not available in the <strong>WebGUI</strong>?</p>\r\n<p>14. Why do <strong>special characters</strong> disappear from the item overview of the purchase requisition if you use the ALV grid?</p>\r\n<p>15. Why does the <strong>history</strong> not work in the purchase requisition (ME51N, ME52N, ME53N)?</p>\r\n<p>16. Which <strong>fields</strong> are displayed in the grid?</p>\r\n<p>17. Can the system copy the <strong>total value</strong> from the info record?</p>\r\n<p>18. You sort the <strong>delivery date column</strong> in ME51N, ME52N, and ME53N. Afterwards, the sort result is incorrect.</p>\r\n<p>19. What happens if you enter an incorrect <strong>storage location</strong> in the purchase requisition?</p>\r\n<p>20. In the WebGUI, you display the <strong>units of measure</strong> using the input help in transaction ME51N. The system displays only the units of measure used in purchasing.</p>\r\n<p>21. Why does the material number column sometimes disappear in transactions ME51N and ME52N?</p>\r\n<p><br/>--------------------------</p>\r\n<p><strong>1. Question:</strong></p>\r\n<p>Where can I find <strong>detailed information about transactions ME21N, ME22N, and ME23N</strong> in purchasing?</p>\r\n<p><strong>Answer:</strong></p>\r\n<p>See Note 210502.<br/><br/>--------------------------</p>\r\n<p><strong>2. Question:</strong></p>\r\n<p>Where can I define whether the <strong>purchase requisition price</strong> is to be transferred to the purchase order as net or gross price?</p>\r\n<p><strong>Answer:</strong></p>\r\n<p>See Note 422624.<br/><br/>--------------------------</p>\r\n<p><strong>3. Question:</strong></p>\r\n<p>Why do the <strong>field exits</strong> no longer work in the purchase requisition?</p>\r\n<p><strong>Answer:</strong></p>\r\n<p>See Note 307760.<br/><br/>--------------------------</p>\r\n<p><strong>4. Question:</strong></p>\r\n<p>Why is only a limited use of <strong>screen variants</strong> and <strong>transaction variants</strong> possible with the new transactions?</p>\r\n<p><strong>Answer:</strong></p>\r\n<p>See Note 322090.<br/><br/>--------------------------</p>\r\n<p><strong>5. Question:</strong></p>\r\n<p>How can I change and save a <strong>standard display variant</strong> for the document overview?</p>\r\n<p><strong>Answer:</strong></p>\r\n<p>See Note 310608.<br/><br/>--------------------------</p>\r\n<p><strong>6. Question:</strong></p>\r\n<p>Why does the <strong>field selection key</strong> ACTV not have the same effect as in the old purchase requisition (transaction ME51)?</p>\r\n<p><strong>Answer:</strong></p>\r\n<p>See Note 326125.<br/><br/>--------------------------</p>\r\n<p><strong>7. Question:</strong></p>\r\n<p>Why does the field EBAN-BATOL (<strong>Resubmission Interval</strong> of Purchase Requisition) no longer exist in the new purchase requisition?</p>\r\n<p><strong>Answer:</strong></p>\r\n<p>This is not provided for technical reasons.<br/><br/>--------------------------</p>\r\n<p><strong>8. Question:</strong></p>\r\n<p>Why are <strong>batch input</strong> and <strong>CATT</strong> not possible in the purchase requisition (ME51N, ME52N, ME53N)?</p>\r\n<p><strong>Answer:</strong></p>\r\n<p>For technical reasons, there is only a restricted availability of these functions. Also see Note 217437.<br/><br/>--------------------------</p>\r\n<p><strong>9. Question:</strong></p>\r\n<p>Why is transaction ME53 called instead of transaction ME53N <strong>(or vice versa)</strong> in some applications?</p>\r\n<p><strong>Answer:</strong></p>\r\n<p>The call of the purchase requisition is controlled by the user parameter ENJ_CALL_ME53N. To call transaction ME53N, ENJ_CALL_ME53N must be set to value &#39;X&#39;.<br/><br/>--------------------------</p>\r\n<p><strong>10. Question:</strong></p>\r\n<p>Why is it not possible to use transaction ME52N to change the <strong>price-influencing fields</strong> (for example, the material group) for some purchase requisitions?</p>\r\n<p><strong>Answer:</strong></p>\r\n<p>See Note 603630.<br/><br/>--------------------------</p>\r\n<p><strong>11. Question:</strong></p>\r\n<p>Why is the <strong>ALV Grid Control</strong> not available in the <strong>WebGUI</strong>?</p>\r\n<p><strong>Answer:</strong></p>\r\n<p>For the editable ALV Grid Control there are functional restrictions due to the Basis when you use the ALV Grid Control with the WebGUI. As a result, only the table control is available in the item overview. You cannot overwrite this with your personal settings. <br/>For Release ERP 2005 (ECC 6.00), Note 925300 removes this restriction.<br/><br/>--------------------------</p>\r\n<p><strong>12. Question:</strong></p>\r\n<p>Why are the source determination fields (for example, the purchasing organization) only available on the &quot;Source of Supply&quot; tab page in the <strong>WebGUI</strong> and not in the item overview?</p>\r\n<p><strong>Answer:</strong></p>\r\n<p>You can only display these fields using the ALV Grid Control. The ALV Grid Control is not available in the WebGUI for the item overview. Also refer to the previous question. The affected fields are listed in Note 445188.<br/><br/>--------------------------</p>\r\n<p><strong>13. Question:</strong></p>\r\n<p>Why are some functions of the item overview not available in the <strong>WebGUI</strong>?</p>\r\n<p><strong>Answer:</strong></p>\r\n<p>All functions that are based on the ALV Grid Control are not available in the WebGUI. Refer to the two previous questions.<br/>The following functions are affected: &quot;Total&quot;, &quot;Subtotal&quot;, &quot;Print&quot;, &quot;Views&quot;, &quot;Export&quot;, &quot;Select layout&quot; and &quot;User documentation&quot;.<br/><br/>--------------------------</p>\r\n<p><strong>14. Question:</strong></p>\r\n<p>Why do <strong>special characters</strong> disappear from the item overview of the purchase requisition if you use the ALV grid?</p>\r\n<p><strong>Answer:</strong></p>\r\n<p>According to Note 508854, the editable ALV grid is not Unicode-enabled. If you want to use special characters, use the table control. To do this, set the &quot;Item overview as grid control&quot; flag to blank in the &quot;Personal Setting&quot; on the &quot;Basic settings&quot; tab page.<br/><br/>--------------------------</p>\r\n<p><strong>15. Question:</strong></p>\r\n<p>Why does the <strong>history</strong> not work in the purchase requisition (ME51N, ME52N, ME53N)?</p>\r\n<p><strong>Answer:</strong></p>\r\n<p>The ALV grid does not support the history function. If you want to use the history function in transaction ME51N, you must use the standard display of the item overview (as in transaction ME21N). You can activate it again by choosing &quot;Personal Setting&quot; and deactivating the &quot;Item overview as grid control&quot; setting.<br/><br/>--------------------------</p>\r\n<p><strong>16. Question:</strong></p>\r\n<p>Which <strong>fields</strong> are displayed in the grid?</p>\r\n<p><strong>Answer:</strong></p>\r\n<p>The system displays all fields except for the fields which refer to the account assignment, such as <br/>funds, funds center, commitment item, and so on (for example). <br/><br/>--------------------------</p>\r\n<p><strong>17. Question:</strong></p>\r\n<p>Can the system copy the <strong>total value</strong> from the info record?</p>\r\n<p><strong>Answer:</strong></p>\r\n<p>No. In the standard R/3 system, you cannot copy the total value or the valuation price from the info record.<br/><br/>--------------------------</p>\r\n<p><strong>18. Question:</strong></p>\r\n<p>You sort the <strong>delivery date column</strong> in ME51N, ME52N, and ME53N. Afterwards, the sort result is incorrect.</p>\r\n<p><strong>Answer:</strong></p>\r\n<p>See Note 636343.<br/><br/>--------------------------</p>\r\n<p><strong>19. Question:</strong></p>\r\n<p>What happens if you enter an incorrect <strong>storage location</strong> in the purchase requisition?</p>\r\n<p><strong>Answer:</strong></p>\r\n<p>The system automatically deletes the storage location. No error message is output.<br/><br/>--------------------------</p>\r\n<p><strong>20. Question:</strong></p>\r\n<p>In the WebGUI, you display the <strong>units of measure</strong> using the input help in transaction ME51N. The system displays only the units of measure used in purchasing.</p>\r\n<p><strong>Answer:</strong></p>\r\n<p>This is the standard system behavior. In the WebGUI, only the table control is available in the item overview.<br/>  In the table control, <br/>the system only displays the units of measure that are relevant for purchasing.  <br/>The indicator T006-KZKEH must also be set for these units of measure.<br/><br/>--------------------------</p>\r\n<p><strong>21. Question:</strong></p>\r\n<p>Why does the material number column sometimes disappear in transactions ME51N and ME52N?</p>\r\n<p><strong>Answer:</strong></p>\r\n<p>See Note 492757.<br/><br/>--------------------------<br/><br/></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>FAQ, purchase requisition price, field exit, ME51, ME52, ME53, ME51N, ME52N, ME53N, resubmission interval, BATOL, EBAN, EBAN-BATOL, document overview, standard display variant, display variant, field selection key, field selection, batch input, CATT, screen variant, transaction variant, SHD0, WebGUI, contract, item number of contract, fixed vendor, info record, desired vendor, purchasing organization, supplying plant, ALV Grid Control, table control, source determination, total, subtotal, print, views, export, select layout, special character, Unicode, user documentation</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>-</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>-</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D031268)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D041269)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000493318/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000493318/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000493318/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000493318/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000493318/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000493318/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000493318/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000493318/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000493318/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "925300", "RefComponent": "MM-PUR-REQ-GUI", "RefTitle": "ME51N: ALV grid does not exist in the Web GUI", "RefUrl": "/notes/925300"}, {"RefNumber": "636343", "RefComponent": "MM-PUR-REQ-GUI", "RefTitle": "ME51N/52N/53N: incorrect sort criteria for delivery date", "RefUrl": "/notes/636343"}, {"RefNumber": "635511", "RefComponent": "MM-SRV", "RefTitle": "FAQ: Purchase requisitions in the service", "RefUrl": "/notes/635511"}, {"RefNumber": "603630", "RefComponent": "MM-PUR-REQ-GUI", "RefTitle": "ME52N: Price influencing fields cannot be changed", "RefUrl": "/notes/603630"}, {"RefNumber": "508854", "RefComponent": "BC-FES-GUI", "RefTitle": "SAPGUI: How to use Unicode", "RefUrl": "/notes/508854"}, {"RefNumber": "493315", "RefComponent": "MM-PUR-REQ", "RefTitle": "FAQ: Purchase requisition (general)", "RefUrl": "/notes/493315"}, {"RefNumber": "492757", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "ME21N/ME52N: You cannot set field selection for material", "RefUrl": "/notes/492757"}, {"RefNumber": "445188", "RefComponent": "MM-PUR-REQ-GUI", "RefTitle": "ME51N: Fields for source of supply are not in item overview", "RefUrl": "/notes/445188"}, {"RefNumber": "422624", "RefComponent": "MM-PUR-REQ-GUI", "RefTitle": "ME51N Order price adoption: Additional info", "RefUrl": "/notes/422624"}, {"RefNumber": "329437", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "ME21N: Functions not available", "RefUrl": "/notes/329437"}, {"RefNumber": "326125", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "ME21N ME51N: Field selection AKTV for item change", "RefUrl": "/notes/326125"}, {"RefNumber": "322090", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "ME21N ME51N Hide \"Transaction Variants\" tab", "RefUrl": "/notes/322090"}, {"RefNumber": "310608", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "ME21N ME51N Save standard layouts", "RefUrl": "/notes/310608"}, {"RefNumber": "307760", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "ME21N field exits do not work as in ME21", "RefUrl": "/notes/307760"}, {"RefNumber": "217437", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "ME21N/ME51N: Batch input and CATT not possible", "RefUrl": "/notes/217437"}, {"RefNumber": "210502", "RefComponent": "MM-PUR-GF-DOCU", "RefTitle": "Incomplete documentation about single-screen applications in purchasing", "RefUrl": "/notes/210502"}, {"RefNumber": "1803189", "RefComponent": "MM-PUR-PO", "RefTitle": "FAQ: End of Support of ME21, ME51, BAPI_PO_CREATE and Archiving Reports etc.", "RefUrl": "/notes/1803189"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1528127", "RefComponent": "MM-PUR-REQ", "RefTitle": "Error 06553: \"Not possible to assign a source of supply\" in transaction ME51N", "RefUrl": "/notes/1528127 "}, {"RefNumber": "2814244", "RefComponent": "MM-PUR-GF-OC", "RefTitle": "No Print Preview / Smartform option in ME53N, PRs", "RefUrl": "/notes/2814244 "}, {"RefNumber": "1803189", "RefComponent": "MM-PUR-PO", "RefTitle": "FAQ: End of Support of ME21, ME51, BAPI_PO_CREATE and Archiving Reports etc.", "RefUrl": "/notes/1803189 "}, {"RefNumber": "493315", "RefComponent": "MM-PUR-REQ", "RefTitle": "FAQ: Purchase requisition (general)", "RefUrl": "/notes/493315 "}, {"RefNumber": "217437", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "ME21N/ME51N: Batch input and CATT not possible", "RefUrl": "/notes/217437 "}, {"RefNumber": "925300", "RefComponent": "MM-PUR-REQ-GUI", "RefTitle": "ME51N: ALV grid does not exist in the Web GUI", "RefUrl": "/notes/925300 "}, {"RefNumber": "326125", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "ME21N ME51N: Field selection AKTV for item change", "RefUrl": "/notes/326125 "}, {"RefNumber": "210502", "RefComponent": "MM-PUR-GF-DOCU", "RefTitle": "Incomplete documentation about single-screen applications in purchasing", "RefUrl": "/notes/210502 "}, {"RefNumber": "310608", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "ME21N ME51N Save standard layouts", "RefUrl": "/notes/310608 "}, {"RefNumber": "445188", "RefComponent": "MM-PUR-REQ-GUI", "RefTitle": "ME51N: Fields for source of supply are not in item overview", "RefUrl": "/notes/445188 "}, {"RefNumber": "322090", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "ME21N ME51N Hide \"Transaction Variants\" tab", "RefUrl": "/notes/322090 "}, {"RefNumber": "307760", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "ME21N field exits do not work as in ME21", "RefUrl": "/notes/307760 "}, {"RefNumber": "492757", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "ME21N/ME52N: You cannot set field selection for material", "RefUrl": "/notes/492757 "}, {"RefNumber": "635511", "RefComponent": "MM-SRV", "RefTitle": "FAQ: Purchase requisitions in the service", "RefUrl": "/notes/635511 "}, {"RefNumber": "329437", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "ME21N: Functions not available", "RefUrl": "/notes/329437 "}, {"RefNumber": "636343", "RefComponent": "MM-PUR-REQ-GUI", "RefTitle": "ME51N/52N/53N: incorrect sort criteria for delivery date", "RefUrl": "/notes/636343 "}, {"RefNumber": "422624", "RefComponent": "MM-PUR-REQ-GUI", "RefTitle": "ME51N Order price adoption: Additional info", "RefUrl": "/notes/422624 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}