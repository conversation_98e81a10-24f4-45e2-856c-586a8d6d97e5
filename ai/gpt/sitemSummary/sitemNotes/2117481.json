{"Request": {"Number": "2117481", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 332, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018012382017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002117481?language=E&token=C9E509E847D653FC9EBC515FDD3976B2"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002117481", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002117481/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2117481"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 125}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.09.2023"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-REL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Suite and SAP S/4HANA Release Information"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Suite and SAP S/4HANA Release Information", "value": "XX-SER-REL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-REL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2117481 - SAP Simple Finance, on-premise edition 1503 - Release Information Note"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This Release Information Note (RIN) contains information and references to SAP Notes for applying support package (SP) stacks of the SAP Simple Finance, on-premise edition 1503.</p>\r\n<p><strong>Note:</strong> As of the support package stack released in May 2015, the product name was changed:</p>\r\n<ul>\r\n<li>New name: SAP Simple Finance, on-premise edition 1503 support package stack 01</li>\r\n<li>Old name:&#160;SAP Simple Finance add-on 2.0 for SAP Business Suite powered by SAP HANA, support package stack 01</li>\r\n</ul>\r\n<p>For more information on this product name change refer to SAP Note <a target=\"_blank\" href=\"/notes/2171868\">2171868</a>.</p>\r\n<p>SAP Simple Finance, on-premise edition 1503 is the successor&#160;release of SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA.</p>\r\n<p><strong>For a proper data migration to the&#160;SAP Simple Finance, on-premise edition 1503 it is key to apply the corrections and enhancements to the migration programs as listed below.</strong></p>\r\n<p><strong>Note: </strong>This SAP Note is subject to change. Listed below are points to keep in mind:</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>GENERAL INFORMATION</strong></span></p>\r\n<p>Read this SAP Note completely BEFORE installing or upgrading the SAP Simple Finance as well as BEFORE applying support package (SP) stacks of&#160;the&#160;SAP Simple Finance, on-premise edition&#160;1503. Follow the instructions given below.</p>\r\n<ul>\r\n<li>Check this SAP Note for changes on a regular basis. All changes made after release of a Support Package (SP) Stack are documented in section \"Changes made after Release of SP stack &lt;xx&gt;\". New SAP Notes added to the lists below will be marked with an \"added on\" date.</li>\r\n<li><strong>NEW:</strong> SAP Solution Manager&#8217;s cloud-based <strong>Maintenance Planner</strong> is the successor of Maintenance Optimizer, Landscape Planner and Product System Editor. <strong><em>Maintenance Optimizer is no longer supported</em>.</strong> Maintenance planner helps you plan and maintain systems in your landscape. Please use the Maintenance planner to calculate a stack XLM file for a system maintenance and add-on installation. To access the tool go to <a target=\"_blank\" href=\"https://apps.support.sap.com/sap/support/mp\">https://apps.support.sap.com/sap/support/mp</a> . A <a target=\"_blank\" href=\"http://wiki.scn.sap.com/wiki/download/attachments/187337812/Maintenance_Planner_User_Guide.pdf\">Maintenance Planner User Guide</a> can be accessed from the home screen of Maintenance Planner. For the<strong> big picture of the Maintenance Planner</strong> and related tools, see <a target=\"_blank\" href=\"http://scn.sap.com/docs/DOC-55363\">Landscape Management</a> @ the SCN.</li>\r\n</ul>\r\n<p><strong>Installation and Upgrade Information</strong></p>\r\n<ul>\r\n<li>You can find the relevant information on installation, configuration and dependencies in the <a target=\"_blank\" href=\"http://help.sap.com/sfin200\">Admin Guide</a> for the SAP Simple Finance, on-premise edition&#160;1503.</li>\r\n<li>Refer to SAP Note <a target=\"_blank\" href=\"/notes/2157996\">2157996</a>&#160;for a detailed checklist,&#160;which can support you in preparing the SAP Simple &#160;Finance installation&#160;and upgrade.</li>\r\n<li>For a system copy of the SAP Simple Finance there are currently problems with HANA Content activation. For more information, please see SAP Note <a target=\"_blank\" href=\"/notes/2090914\">2090914</a>.</li>\r\n<li>You can find information about browser support in SAP Note <a target=\"_blank\" href=\"/notes/1971111\">1971111</a>.</li>\r\n<li>You can find information on the license for SAP Cash Management powered by SAP HANA in SAP Note <a target=\"_blank\" href=\"/notes/2044295\">2044295</a>.</li>\r\n</ul>\r\n<ul>\r\n<li>\r\n<p>For sizing of new system installations use the HANA version of the <a target=\"_blank\" href=\"http://service.sap.com/quicksizer\">Quick Sizer</a>. For a migration to SAP Simple Finance&#160;use the standard ABAP sizing report from SAP Note&#160;<a target=\"_blank\" href=\"/notes/1872170\">1872170</a>.</p>\r\n</li>\r\n<li>\r\n<p>In general SAP recommends to use the latest Support Package stack of SAP Simple Finance, on-premise edition 1503.</p>\r\n</li>\r\n</ul>\r\n<p><strong>Important Considerations</strong></p>\r\n<ul>\r\n<li>Release scope information for the&#160;SAP Simple Finance, on-premise edition 1503, including information on the compatibility of enterprise extensions, industry solutions, and add-ons can be found in SAP Note <a target=\"_blank\" href=\"/notes/2119188\">2119188</a>.</li>\r\n<li>If you want to use add-ons with SAP Simple Finance, then refer to SAP Note <a target=\"_blank\" href=\"/notes/2103558\">2103558</a>.</li>\r\n<li>If you want to copy clients from/to a SAP Simple Finance system, you have to implement the correction from SAP Note <a target=\"_blank\" href=\"/notes/2168826\">2168826</a>&#160;first.</li>\r\n<li>Please be informed that SAP Simple Finance is only released on In-Memory databases (SAP HANA database 1.0 and 2.0), detailed Revision and Maintenance Strategy refer to&#160;<a target=\"_blank\" href=\"/notes/2021789\">2021789</a> and&#160;<a target=\"_blank\" href=\"/notes/2378962\">2378962</a>.</li>\r\n<li>Please note that SAP HANA 1.0 SPS12 is out of standard maintenance as of July 1<sup>st</sup>&#160;2021.&#160;</li>\r\n<li>Release restrictions can be found in SAP note <a target=\"_blank\" href=\"/notes/2127080\">2127080</a>.</li>\r\n<li>Please note that the software component EA-FI has been retrofitted into SAP_FIN 720 within the&#160;SAP Simple Finance, on-premise edition 1503. You can find the equivalence levels between EA-FI 200 and SAP_FIN 720 in the corresponding SP stack sections.</li>\r\n<li>Please note that the content of the software components EA-FIN 720&#160;and FSCM_CCD 720&#160;has been&#160;moved into the software component SAP_FIN 720&#160;within the&#160;SAP Simple Finance, on-premise edition 1503. The software components themselves still have to be retained for technical reasons. Consequently, no support packages are delivered for these software component versions.</li>\r\n<li>SAP Simple Finance uses latest SAP NetWeaver technologies such as DDL views (aka CDS views) to retrieve data from the SAP HANA database. Creating and editing DDL views requires ABAP for Ecplise. SAP recommends to prepare yourself for using ABAP for Eclipse. For more information refer to <a target=\"_blank\" href=\"https://tools.hana.ondemand.com/#abap\">https://tools.hana.ondemand.com/#abap</a>.</li>\r\n<li>You have installed the SAP Simple Finance add-on 1.0 and want to upgrade and migrate to SAP Simple Finance, on-premise edition 1503. Please note parts of the Analytics content were replaced with similar Fiori applications. For more information refer to SAP Note <a target=\"_blank\" href=\"/notes/2131643\">2131643</a>.</li>\r\n<li><strong>Upgrades</strong>&#160;and&#160;<strong>migrations</strong>&#160;to SAP Simple Finance on-premise edition 1503 shall target Support Package stack SP03 (11/2015) at minimum to use the latest versions of the migration programs and significantly reduce the effort for applying related SAP Notes. Details refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2253237\">2253237</a>.</li>\r\n<li><strong>Prerequisite</strong>&#160;of <strong>Migrations</strong>&#160;to SAP Simple Finance on-premise edition 1503 shall be checked. Deatils refer to SAP Note <a target=\"_blank\" href=\"/notes/2294486\">2294486</a>.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>financials, on-premise edition for SAP Business Suite powered by SAP HANA 2.0, SAP SFINANCIALS 2.0, SAP ERP 6.0, Upgrade, SFIN</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You want to run&#160;the&#160;SAP Simple Finance, on-premise edition 1503 and look for overall information.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>SUPPORT PACKAGE STACK 20 (09/2023)</strong></p>\r\n<p><strong>A) Installation Requirements:</strong></p>\r\n<p>&#160;SAP Simple Finance, on-premise edition 1503 support package stack 20 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP Stack 27. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 27, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/1737650\">1737650</a>. Refer to the corresponding Upgrade Master Guide for detailed information on enhancement package 7 for SAP ERP 6.0. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 30. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a>. You can either update the underlying SAP NetWeaver 7.40 stack upfront or during the installation of the&#160;SAP Simple Finance, on-premise edition 1503.</li>\r\n<li>Use&#160;the&#160;<strong>kernel</strong>&#160;7.49 SP 1100 or 7.53 SP 1200 or 7.54 SP 100 or higher</li>\r\n<li>Before update to SP Stack 27 of enhancement package 7 for SAP ERP 6.0 and SAP Simple Finance, check if the SAP&#160;<strong>HANA database</strong>&#160;1.0 was already updated to the required minimal&#160;revision 122 of SP stack&#160;12 of SAP HANA&#160;database. For more information of SP stack 12, refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2165826\">2165826</a>,&#160;and follow the SAP Note&#160;<a target=\"_blank\" href=\"/notes/2021789\">2021789&#160;</a>to use higher revision.&#160;</li>\r\n<li>SAP&#160;<strong>HANA database 2.0</strong>&#160;SP stack 01 Revision 11 or higher is released for SAP Simple Finance&#160;1503 and enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2494263</a>, and follow the SAP Note&#160;<a target=\"_blank\" href=\"/notes/2378962\">2378962&#160;</a>to use higher revision.</li>\r\n<li>In general the&#160;<strong>installation</strong>&#160;of SAP Simple Finance 1503&#160;into an existing ERP installation is only possible via the SUM tool. SAP recommends you update to the latest available patch level of SUM 1.0.</li>\r\n<li>If your system is already on SAP Simple Finance, on-premise edition 1503 and you would like to&#160;<strong>update</strong>&#160;to Support Package stack 20, you can also use SPAM.</li>\r\n</ul>\r\n<p><strong>B) Notes to be applied as part of this stack:</strong></p>\r\n<p><strong>Before the installation / upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 20</strong></p>\r\n<p>The following table lists SAP Notes, which must be applied to avoid issues during the installation/upgrade of the SAP Simple Finance or which are required to prepare the application data migration on the initial system constellation&#160;before the SAP Simple Finance installation/upgrade. For this purpose, check the validity of each of these SAP notes. Apply all notes relevant to your initial system constellation.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td width=\"825\">\r\n<p><em>Description</em></p>\r\n</td>\r\n<td width=\"150\">\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td width=\"104\">\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1925679\">1925679</a></p>\r\n</td>\r\n<td width=\"825\">DB views FMKK_BKPF_BSIK and FMKK_BKPF_BSAK were deleted</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2015-05-18</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1941711\">1941711</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Dump: INSERT_PROGRAM_NAME_BLANK during After-Import processing of SXCI / SXSD</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845848\">1845848</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Removing generated fields (\"_COUNTER\", \"_FISCPER\",...)</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845847\">1845847</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP HANA view generatn w/o flds _COUNTER, _FISCPER, _BUZEI3</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2172805\">2172805</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP Simple Finance upgrade: &#8220;COPC_V_PAYRQ_H&#8221; not activated, SAPK-70005INSAPFIN included in the target stack-xml</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2202445\">2202445</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Virtuelle Hierarchien werden nicht ins Zielsystem transportiert</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2207750\">2207750</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SQL-Fehler \"cannot use duplicate index name\" in MAIN_SHDCRE/SUBMOD_SHDDBCLONE/DBCLONE</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">\r\n<p>2015-08-21</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>During the installation or upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 19:</strong></p>\r\n<p>The following table lists SAP Notes, that must be applied during the installation/upgrade of SAP Simple Finance using SUM.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2146844\">2146844</a></p>\r\n</td>\r\n<td>\r\n<p>Conversion program: Pool/cluster to transparent terminates with return code 8 because views could not be created</p>\r\n</td>\r\n<td>\r\n<p>&#160;X</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The following table lists additional SAP Notes, that provide troubleshooting hints for known problems during the installation/upgrade.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2096457\">2096457</a></p>\r\n</td>\r\n<td>\r\n<p>Errors in ACT_UPG during SPDD: customer version of table can not be accessed</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2139357\">2139357</a></p>\r\n</td>\r\n<td>\r\n<p>SAP Simple Finance OP: Activation errors during installation of Support Packages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-06-15</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2086899\">2086899</a></p>\r\n</td>\r\n<td>\r\n<p>Upgrade phase MAIN_SHDRUN/ACT_UPG returns error messages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Migration related information - relevant if upgrading to SAP Simple Finance, on-premise edition 1503 for the first time:</strong></p>\r\n<p>Additional guidance for migration (consulting):</p>\r\n<p>The steps for application data migration have to be executed in a predefined order (see Migration Guide at&#160;<a target=\"_blank\" href=\"https://help.sap.com/sfin200\">help.sap.com/sfin200</a>&#160;for details). Some of these steps cannot be repeated. However in case of errors or after the implementation of corrections to the migration programs, this may be required. Follow the instructions in SAP Note&#160;<a target=\"_blank\" href=\"/notes/2138644\">2138644</a>&#160;and/or SAP Note&#160;<a target=\"_blank\" href=\"/notes/2155561\">2155561 i</a>n such cases.</p>\r\n<p><strong>Latest before application configuration and testing:</strong></p>\r\n<p>The following table lists important SAP Notes, which should be applied latest after the application data migration. They are required to avoid known problems during application configuration and testing.</p>\r\n<p>Cash Management:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2138445\">2138445</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: SAP Cash Management powered by SAP HANA as part of the SAP Simple Finance, on-premise edition 1503</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>If you do not use SAP Cash Management powered by SAP HANA, you must use the new Bank Account Management (BAM Lite) to migrate your house bank accounts and manage the new bank account master data. In this case, the following SAP Notes should be applied:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2154658\" title=\"2154658  - Release Information Note: Bank Account Management Lite\">2154658</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: Bank Account Management Lite</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Other important SAP Notes need to be checked or applied:</p>\r\n<p><strong>C) Related SAP Notes for SAP NetWeaver 7.40</strong></p>\r\n<p>Refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2125084\">2125084</a>&#160;for a list of important corrections for SAP NetWeaver 7.40.</p>\r\n<p><strong>D) EA-FI Equivalence Levels:</strong></p>\r\n<p>EA-FI 200 SP22 corresponds to SAP Simple Finance, on-premise edition 1503 support package stack 20.</p>\r\n<p><strong>_________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK 19 (03/2023)</strong></p>\r\n<p><strong>A) Installation Requirements:</strong></p>\r\n<p>&#160;SAP Simple Finance, on-premise edition 1503 support package stack 19 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP Stack 26. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 26, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/1737650\">1737650</a>. Refer to the corresponding Upgrade Master Guide for detailed information on enhancement package 7 for SAP ERP 6.0. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 29. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a>. You can either update the underlying SAP NetWeaver 7.40 stack upfront or during the installation of the&#160;SAP Simple Finance, on-premise edition 1503.</li>\r\n<li>Use&#160;the&#160;<strong>kernel</strong>&#160;7.49 SP 1100 or 7.53 SP 1100 or 7.54 SP 023 or higher</li>\r\n<li>Before update to SP Stack 26 of enhancement package 7 for SAP ERP 6.0 and SAP Simple Finance, check if the SAP&#160;<strong>HANA database</strong>&#160;1.0 was already updated to the required minimal&#160;revision 122 of SP stack&#160;12 of SAP HANA&#160;database. For more information of SP stack 12, refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2165826\">2165826</a>,&#160;and follow the SAP Note&#160;<a target=\"_blank\" href=\"/notes/2021789\">2021789&#160;</a>to use higher revision.&#160;</li>\r\n<li>SAP&#160;<strong>HANA database 2.0</strong>&#160;SP stack 01 Revision 11 or higher is released for SAP Simple Finance&#160;1503 and enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2494263</a>, and follow the SAP Note&#160;<a target=\"_blank\" href=\"/notes/2378962\">2378962&#160;</a>to use higher revision.</li>\r\n<li>In general the&#160;<strong>installation</strong>&#160;of SAP Simple Finance 1503&#160;into an existing ERP installation is only possible via the SUM tool. SAP recommends you update to the latest available patch level of SUM 1.0.</li>\r\n<li>If your system is already on SAP Simple Finance, on-premise edition 1503 and you would like to&#160;<strong>update</strong>&#160;to Support Package stack 19, you can also use SPAM.</li>\r\n</ul>\r\n<p><strong>B) Notes to be applied as part of this stack:</strong></p>\r\n<p><strong>Before the installation / upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 19</strong></p>\r\n<p>The following table lists SAP Notes, which must be applied to avoid issues during the installation/upgrade of the SAP Simple Finance or which are required to prepare the application data migration on the initial system constellation&#160;before the SAP Simple Finance installation/upgrade. For this purpose, check the validity of each of these SAP notes. Apply all notes relevant to your initial system constellation.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td width=\"825\">\r\n<p><em>Description</em></p>\r\n</td>\r\n<td width=\"150\">\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td width=\"104\">\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1925679\">1925679</a></p>\r\n</td>\r\n<td width=\"825\">DB views FMKK_BKPF_BSIK and FMKK_BKPF_BSAK were deleted</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2015-05-18</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1941711\">1941711</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Dump: INSERT_PROGRAM_NAME_BLANK during After-Import processing of SXCI / SXSD</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845848\">1845848</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Removing generated fields (\"_COUNTER\", \"_FISCPER\",...)</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845847\">1845847</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP HANA view generatn w/o flds _COUNTER, _FISCPER, _BUZEI3</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2172805\">2172805</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP Simple Finance upgrade: &#8220;COPC_V_PAYRQ_H&#8221; not activated, SAPK-70005INSAPFIN included in the target stack-xml</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2202445\">2202445</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Virtuelle Hierarchien werden nicht ins Zielsystem transportiert</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2207750\">2207750</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SQL-Fehler \"cannot use duplicate index name\" in MAIN_SHDCRE/SUBMOD_SHDDBCLONE/DBCLONE</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">\r\n<p>2015-08-21</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>During the installation or upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 19:</strong></p>\r\n<p>The following table lists SAP Notes, that must be applied during the installation/upgrade of SAP Simple Finance using SUM.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2146844\">2146844</a></p>\r\n</td>\r\n<td>\r\n<p>Conversion program: Pool/cluster to transparent terminates with return code 8 because views could not be created</p>\r\n</td>\r\n<td>\r\n<p>&#160;X</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The following table lists additional SAP Notes, that provide troubleshooting hints for known problems during the installation/upgrade.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2096457\">2096457</a></p>\r\n</td>\r\n<td>\r\n<p>Errors in ACT_UPG during SPDD: customer version of table can not be accessed</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2139357\">2139357</a></p>\r\n</td>\r\n<td>\r\n<p>SAP Simple Finance OP: Activation errors during installation of Support Packages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-06-15</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2086899\">2086899</a></p>\r\n</td>\r\n<td>\r\n<p>Upgrade phase MAIN_SHDRUN/ACT_UPG returns error messages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Migration related information - relevant if upgrading to SAP Simple Finance, on-premise edition 1503 for the first time:</strong></p>\r\n<p>Additional guidance for migration (consulting):</p>\r\n<p>The steps for application data migration have to be executed in a predefined order (see Migration Guide at&#160;<a target=\"_blank\" href=\"https://help.sap.com/sfin200\">help.sap.com/sfin200</a>&#160;for details). Some of these steps cannot be repeated. However in case of errors or after the implementation of corrections to the migration programs, this may be required. Follow the instructions in SAP Note&#160;<a target=\"_blank\" href=\"/notes/2138644\">2138644</a>&#160;and/or SAP Note&#160;<a target=\"_blank\" href=\"/notes/2155561\">2155561 i</a>n such cases.</p>\r\n<p><strong>Latest before application configuration and testing:</strong></p>\r\n<p>The following table lists important SAP Notes, which should be applied latest after the application data migration. They are required to avoid known problems during application configuration and testing.</p>\r\n<p>Cash Management:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2138445\">2138445</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: SAP Cash Management powered by SAP HANA as part of the SAP Simple Finance, on-premise edition 1503</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>If you do not use SAP Cash Management powered by SAP HANA, you must use the new Bank Account Management (BAM Lite) to migrate your house bank accounts and manage the new bank account master data. In this case, the following SAP Notes should be applied:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2154658\" title=\"2154658  - Release Information Note: Bank Account Management Lite\">2154658</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: Bank Account Management Lite</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Other important SAP Notes need to be checked or applied:</p>\r\n<p><strong>C) Related SAP Notes for SAP NetWeaver 7.40</strong></p>\r\n<p>Refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2125084\">2125084</a>&#160;for a list of important corrections for SAP NetWeaver 7.40.</p>\r\n<p><strong>D) EA-FI Equivalence Levels:</strong></p>\r\n<p>EA-FI 200 SP22 corresponds to SAP Simple Finance, on-premise edition 1503 support package stack 19.</p>\r\n<p><strong>_________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK 18 (09/2022)</strong></p>\r\n<p><strong>A) Installation Requirements:</strong></p>\r\n<p>&#160;SAP Simple Finance, on-premise edition 1503 support package stack 18 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP Stack 25. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 25, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/1737650\">1737650</a>. Refer to the corresponding Upgrade Master Guide for detailed information on enhancement package 7 for SAP ERP 6.0. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 28. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a>. You can either update the underlying SAP NetWeaver 7.40 stack upfront or during the installation of the&#160;SAP Simple Finance, on-premise edition 1503.</li>\r\n<li>Use&#160;the&#160;<strong>kernel</strong>&#160;7.49 SP 1000 or 7.53 SP900 or higher</li>\r\n<li>Before update to SP Stack 25 of enhancement package 7 for SAP ERP 6.0 and SAP Simple Finance, check if the SAP&#160;<strong>HANA database</strong>&#160;1.0 was already updated to the required minimal&#160;revision 122 of SP stack&#160;12 of SAP HANA&#160;database. For more information of SP stack 12, refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2165826\">2165826</a>,&#160;and follow the SAP Note&#160;<a target=\"_blank\" href=\"/notes/2021789\">2021789&#160;</a>to use higher revision.&#160;</li>\r\n<li>SAP&#160;<strong>HANA database 2.0</strong>&#160;SP stack 01 Revision 11 or higher is released for SAP Simple Finance&#160;1503 and enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2494263</a>, and follow the SAP Note&#160;<a target=\"_blank\" href=\"/notes/2378962\">2378962&#160;</a>to use higher revision.</li>\r\n<li>In general the&#160;<strong>installation</strong>&#160;of SAP Simple Finance 1503&#160;into an existing ERP installation is only possible via the SUM tool. SAP recommends you update to the latest available patch level of SUM 1.0.</li>\r\n<li>If your system is already on SAP Simple Finance, on-premise edition 1503 and you would like to&#160;<strong>update</strong>&#160;to Support Package stack 18, you can also use SPAM.</li>\r\n</ul>\r\n<p><strong>B) Notes to be applied as part of this stack:</strong></p>\r\n<p><strong>Before the installation / upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 18</strong></p>\r\n<p>The following table lists SAP Notes, which must be applied to avoid issues during the installation/upgrade of the SAP Simple Finance or which are required to prepare the application data migration on the initial system constellation&#160;before the SAP Simple Finance installation/upgrade. For this purpose, check the validity of each of these SAP notes. Apply all notes relevant to your initial system constellation.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td width=\"825\">\r\n<p><em>Description</em></p>\r\n</td>\r\n<td width=\"150\">\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td width=\"104\">\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1925679\">1925679</a></p>\r\n</td>\r\n<td width=\"825\">DB views FMKK_BKPF_BSIK and FMKK_BKPF_BSAK were deleted</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2015-05-18</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1941711\">1941711</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Dump: INSERT_PROGRAM_NAME_BLANK during After-Import processing of SXCI / SXSD</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845848\">1845848</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Removing generated fields (\"_COUNTER\", \"_FISCPER\",...)</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845847\">1845847</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP HANA view generatn w/o flds _COUNTER, _FISCPER, _BUZEI3</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2172805\">2172805</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP Simple Finance upgrade: &#8220;COPC_V_PAYRQ_H&#8221; not activated, SAPK-70005INSAPFIN included in the target stack-xml</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2202445\">2202445</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Virtuelle Hierarchien werden nicht ins Zielsystem transportiert</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2207750\">2207750</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SQL-Fehler \"cannot use duplicate index name\" in MAIN_SHDCRE/SUBMOD_SHDDBCLONE/DBCLONE</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">\r\n<p>2015-08-21</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>During the installation or upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 18:</strong></p>\r\n<p>The following table lists SAP Notes, that must be applied during the installation/upgrade of SAP Simple Finance using SUM.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2146844\">2146844</a></p>\r\n</td>\r\n<td>\r\n<p>Conversion program: Pool/cluster to transparent terminates with return code 8 because views could not be created</p>\r\n</td>\r\n<td>\r\n<p>&#160;X</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The following table lists additional SAP Notes, that provide troubleshooting hints for known problems during the installation/upgrade.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2096457\">2096457</a></p>\r\n</td>\r\n<td>\r\n<p>Errors in ACT_UPG during SPDD: customer version of table can not be accessed</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2139357\">2139357</a></p>\r\n</td>\r\n<td>\r\n<p>SAP Simple Finance OP: Activation errors during installation of Support Packages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-06-15</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2086899\">2086899</a></p>\r\n</td>\r\n<td>\r\n<p>Upgrade phase MAIN_SHDRUN/ACT_UPG returns error messages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Migration related information - relevant if upgrading to SAP Simple Finance, on-premise edition 1503 for the first time:</strong></p>\r\n<p>Additional guidance for migration (consulting):</p>\r\n<p>The steps for application data migration have to be executed in a predefined order (see Migration Guide at&#160;<a target=\"_blank\" href=\"https://help.sap.com/sfin200\">help.sap.com/sfin200</a>&#160;for details). Some of these steps cannot be repeated. However in case of errors or after the implementation of corrections to the migration programs, this may be required. Follow the instructions in SAP Note&#160;<a target=\"_blank\" href=\"/notes/2138644\">2138644</a>&#160;and/or SAP Note&#160;<a target=\"_blank\" href=\"/notes/2155561\">2155561 i</a>n such cases.</p>\r\n<p><strong>Latest before application configuration and testing:</strong></p>\r\n<p>The following table lists important SAP Notes, which should be applied latest after the application data migration. They are required to avoid known problems during application configuration and testing.</p>\r\n<p>Cash Management:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2138445\">2138445</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: SAP Cash Management powered by SAP HANA as part of the SAP Simple Finance, on-premise edition 1503</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>If you do not use SAP Cash Management powered by SAP HANA, you must use the new Bank Account Management (BAM Lite) to migrate your house bank accounts and manage the new bank account master data. In this case, the following SAP Notes should be applied:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2154658\" title=\"2154658  - Release Information Note: Bank Account Management Lite\">2154658</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: Bank Account Management Lite</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Other important SAP Notes need to be checked or applied:</p>\r\n<p><strong>C) Related SAP Notes for SAP NetWeaver 7.40</strong></p>\r\n<p>Refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2125084\">2125084</a>&#160;for a list of important corrections for SAP NetWeaver 7.40.</p>\r\n<p><strong>D) EA-FI Equivalence Levels:</strong></p>\r\n<p>EA-FI 200 SP22 corresponds to SAP Simple Finance, on-premise edition 1503 support package stack 18.</p>\r\n<p><strong>_________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK 17 (03/2022)</strong></p>\r\n<p><strong>A) Installation Requirements:</strong></p>\r\n<p>&#160;SAP Simple Finance, on-premise edition 1503 support package stack 17 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP Stack 24. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 24, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/1737650\">1737650</a>. Refer to the corresponding Upgrade Master Guide for detailed information on enhancement package 7 for SAP ERP 6.0. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 27. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a>. You can either update the underlying SAP NetWeaver 7.40 stack upfront or during the installation of the&#160;SAP Simple Finance, on-premise edition 1503.</li>\r\n<li>Use&#160;the&#160;<strong>kernel</strong>&#160;7.49 SP 1000 or 7.53 SP900 or higher</li>\r\n<li>Before update to SP Stack 24 of enhancement package 7 for SAP ERP 6.0 and SAP Simple Finance, check if the SAP&#160;<strong>HANA database</strong>&#160;1.0 was already updated to the required minimal&#160;revision 122 of SP stack&#160;12 of SAP HANA&#160;database. For more information of SP stack 12, refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2165826\">2165826</a>,&#160;and follow the SAP Note&#160;<a target=\"_blank\" href=\"/notes/2021789\">2021789&#160;</a>to use higher revision.&#160;</li>\r\n<li>SAP&#160;<strong>HANA database 2.0</strong>&#160;SP stack 01 Revision 11 or higher is released for SAP Simple Finance&#160;1503 and enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2494263</a>, and follow the SAP Note&#160;<a target=\"_blank\" href=\"/notes/2378962\">2378962&#160;</a>to use higher revision.</li>\r\n<li>In general the&#160;<strong>installation</strong>&#160;of SAP Simple Finance 1503&#160;into an existing ERP installation is only possible via the SUM tool. SAP recommends you update to the latest available patch level of SUM 1.0.</li>\r\n<li>If your system is already on SAP Simple Finance, on-premise edition 1503 and you would like to&#160;<strong>update</strong>&#160;to Support Package stack 17, you can also use SPAM.</li>\r\n</ul>\r\n<p><strong>B) Notes to be applied as part of this stack:</strong></p>\r\n<p><strong>Before the installation / upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 17</strong></p>\r\n<p>The following table lists SAP Notes, which must be applied to avoid issues during the installation/upgrade of the SAP Simple Finance or which are required to prepare the application data migration on the initial system constellation&#160;before the SAP Simple Finance installation/upgrade. For this purpose, check the validity of each of these SAP notes. Apply all notes relevant to your initial system constellation.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td width=\"825\">\r\n<p><em>Description</em></p>\r\n</td>\r\n<td width=\"150\">\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td width=\"104\">\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1925679\">1925679</a></p>\r\n</td>\r\n<td width=\"825\">DB views FMKK_BKPF_BSIK and FMKK_BKPF_BSAK were deleted</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2015-05-18</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1941711\">1941711</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Dump: INSERT_PROGRAM_NAME_BLANK during After-Import processing of SXCI / SXSD</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845848\">1845848</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Removing generated fields (\"_COUNTER\", \"_FISCPER\",...)</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845847\">1845847</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP HANA view generatn w/o flds _COUNTER, _FISCPER, _BUZEI3</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2172805\">2172805</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP Simple Finance upgrade: &#8220;COPC_V_PAYRQ_H&#8221; not activated, SAPK-70005INSAPFIN included in the target stack-xml</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2202445\">2202445</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Virtuelle Hierarchien werden nicht ins Zielsystem transportiert</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2207750\">2207750</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SQL-Fehler \"cannot use duplicate index name\" in MAIN_SHDCRE/SUBMOD_SHDDBCLONE/DBCLONE</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">\r\n<p>2015-08-21</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>During the installation or upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 17:</strong></p>\r\n<p>The following table lists SAP Notes, that must be applied during the installation/upgrade of SAP Simple Finance using SUM.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2146844\">2146844</a></p>\r\n</td>\r\n<td>\r\n<p>Conversion program: Pool/cluster to transparent terminates with return code 8 because views could not be created</p>\r\n</td>\r\n<td>\r\n<p>&#160;X</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The following table lists additional SAP Notes, that provide troubleshooting hints for known problems during the installation/upgrade.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2096457\">2096457</a></p>\r\n</td>\r\n<td>\r\n<p>Errors in ACT_UPG during SPDD: customer version of table can not be accessed</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2139357\">2139357</a></p>\r\n</td>\r\n<td>\r\n<p>SAP Simple Finance OP: Activation errors during installation of Support Packages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-06-15</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2086899\">2086899</a></p>\r\n</td>\r\n<td>\r\n<p>Upgrade phase MAIN_SHDRUN/ACT_UPG returns error messages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Migration related information - relevant if upgrading to SAP Simple Finance, on-premise edition 1503 for the first time:</strong></p>\r\n<p>Additional guidance for migration (consulting):</p>\r\n<p>The steps for application data migration have to be executed in a predefined order (see Migration Guide at&#160;<a target=\"_blank\" href=\"https://help.sap.com/sfin200\">help.sap.com/sfin200</a>&#160;for details). Some of these steps cannot be repeated. However in case of errors or after the implementation of corrections to the migration programs, this may be required. Follow the instructions in SAP Note&#160;<a target=\"_blank\" href=\"/notes/2138644\">2138644</a>&#160;and/or SAP Note&#160;<a target=\"_blank\" href=\"/notes/2155561\">2155561 i</a>n such cases.</p>\r\n<p><strong>Latest before application configuration and testing:</strong></p>\r\n<p>The following table lists important SAP Notes, which should be applied latest after the application data migration. They are required to avoid known problems during application configuration and testing.</p>\r\n<p>Cash Management:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2138445\">2138445</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: SAP Cash Management powered by SAP HANA as part of the SAP Simple Finance, on-premise edition 1503</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>If you do not use SAP Cash Management powered by SAP HANA, you must use the new Bank Account Management (BAM Lite) to migrate your house bank accounts and manage the new bank account master data. In this case, the following SAP Notes should be applied:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2154658\" title=\"2154658  - Release Information Note: Bank Account Management Lite\">2154658</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: Bank Account Management Lite</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Other important SAP Notes need to be checked or applied:</p>\r\n<p><strong>C) Related SAP Notes for SAP NetWeaver 7.40</strong></p>\r\n<p>Refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2125084\">2125084</a>&#160;for a list of important corrections for SAP NetWeaver 7.40.</p>\r\n<p><strong>D) EA-FI Equivalence Levels:</strong></p>\r\n<p>EA-FI 200 SP21 corresponds to SAP Simple Finance, on-premise edition 1503 support package stack 17.</p>\r\n<p><strong>_________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK 16 (09/2021)</strong></p>\r\n<p><strong>A) Installation Requirements:</strong></p>\r\n<p>&#160;SAP Simple Finance, on-premise edition 1503 support package stack 16 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP Stack 23. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 23, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/1737650\">1737650</a>. Refer to the corresponding Upgrade Master Guide for detailed information on enhancement package 7 for SAP ERP 6.0. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 26. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a>. You can either update the underlying SAP NetWeaver 7.40 stack upfront or during the installation of the&#160;SAP Simple Finance, on-premise edition 1503.</li>\r\n<li>Use&#160;the&#160;<strong>kernel</strong>&#160;7.49 SP 1000 or 7.53 SP800 or higher</li>\r\n<li>Before update to SP Stack 23 of enhancement package 7 for SAP ERP 6.0 and SAP Simple Finance, check if the SAP&#160;<strong>HANA database</strong>&#160;1.0 was already updated to the required minimal&#160;revision 122 of SP stack&#160;12 of SAP HANA&#160;database. For more information of SP stack 12, refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2165826\">2165826</a>,&#160;and follow the SAP Note&#160;<a target=\"_blank\" href=\"/notes/2021789\">2021789&#160;</a>to use higher revision.&#160;</li>\r\n<li>SAP&#160;<strong>HANA database 2.0</strong>&#160;SP stack 01 Revision 11 or higher is released for SAP Simple Finance&#160;1503 and enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2494263</a>, and follow the SAP Note&#160;<a target=\"_blank\" href=\"/notes/2378962\">2378962&#160;</a>to use higher revision.</li>\r\n<li>In general the&#160;<strong>installation</strong>&#160;of SAP Simple Finance 1503&#160;into an existing ERP installation is only possible via the SUM tool. SAP recommends you update to the latest available patch level of SUM 1.0.</li>\r\n<li>If your system is already on SAP Simple Finance, on-premise edition 1503 and you would like to&#160;<strong>update</strong>&#160;to Support Package stack 16, you can also use SPAM.</li>\r\n</ul>\r\n<p><strong>B) Notes to be applied as part of this stack:</strong></p>\r\n<p><strong>Before the installation / upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 16</strong></p>\r\n<p>The following table lists SAP Notes, which must be applied to avoid issues during the installation/upgrade of the SAP Simple Finance or which are required to prepare the application data migration on the initial system constellation&#160;before the SAP Simple Finance installation/upgrade. For this purpose, check the validity of each of these SAP notes. Apply all notes relevant to your initial system constellation.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td width=\"825\">\r\n<p><em>Description</em></p>\r\n</td>\r\n<td width=\"150\">\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td width=\"104\">\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1925679\">1925679</a></p>\r\n</td>\r\n<td width=\"825\">DB views FMKK_BKPF_BSIK and FMKK_BKPF_BSAK were deleted</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2015-05-18</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1941711\">1941711</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Dump: INSERT_PROGRAM_NAME_BLANK during After-Import processing of SXCI / SXSD</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845848\">1845848</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Removing generated fields (\"_COUNTER\", \"_FISCPER\",...)</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845847\">1845847</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP HANA view generatn w/o flds _COUNTER, _FISCPER, _BUZEI3</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2172805\">2172805</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP Simple Finance upgrade: &#8220;COPC_V_PAYRQ_H&#8221; not activated, SAPK-70005INSAPFIN included in the target stack-xml</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2202445\">2202445</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Virtuelle Hierarchien werden nicht ins Zielsystem transportiert</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2207750\">2207750</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SQL-Fehler \"cannot use duplicate index name\" in MAIN_SHDCRE/SUBMOD_SHDDBCLONE/DBCLONE</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">\r\n<p>2015-08-21</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>During the installation or upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 16:</strong></p>\r\n<p>The following table lists SAP Notes, that must be applied during the installation/upgrade of SAP Simple Finance using SUM.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2146844\">2146844</a></p>\r\n</td>\r\n<td>\r\n<p>Conversion program: Pool/cluster to transparent terminates with return code 8 because views could not be created</p>\r\n</td>\r\n<td>\r\n<p>&#160;X</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The following table lists additional SAP Notes, that provide troubleshooting hints for known problems during the installation/upgrade.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2096457\">2096457</a></p>\r\n</td>\r\n<td>\r\n<p>Errors in ACT_UPG during SPDD: customer version of table can not be accessed</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2139357\">2139357</a></p>\r\n</td>\r\n<td>\r\n<p>SAP Simple Finance OP: Activation errors during installation of Support Packages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-06-15</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2086899\">2086899</a></p>\r\n</td>\r\n<td>\r\n<p>Upgrade phase MAIN_SHDRUN/ACT_UPG returns error messages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Migration related information - relevant if upgrading to SAP Simple Finance, on-premise edition 1503 for the first time:</strong></p>\r\n<p>Additional guidance for migration (consulting):</p>\r\n<p>The steps for application data migration have to be executed in a predefined order (see Migration Guide at&#160;<a target=\"_blank\" href=\"https://help.sap.com/sfin200\">help.sap.com/sfin200</a>&#160;for details). Some of these steps cannot be repeated. However in case of errors or after the implementation of corrections to the migration programs, this may be required. Follow the instructions in SAP Note&#160;<a target=\"_blank\" href=\"/notes/2138644\">2138644</a>&#160;and/or SAP Note&#160;<a target=\"_blank\" href=\"/notes/2155561\">2155561 i</a>n such cases.</p>\r\n<p><strong>Latest before application configuration and testing:</strong></p>\r\n<p>The following table lists important SAP Notes, which should be applied latest after the application data migration. They are required to avoid known problems during application configuration and testing.</p>\r\n<p>Cash Management:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2138445\">2138445</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: SAP Cash Management powered by SAP HANA as part of the SAP Simple Finance, on-premise edition 1503</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>If you do not use SAP Cash Management powered by SAP HANA, you must use the new Bank Account Management (BAM Lite) to migrate your house bank accounts and manage the new bank account master data. In this case, the following SAP Notes should be applied:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2154658\" title=\"2154658  - Release Information Note: Bank Account Management Lite\">2154658</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: Bank Account Management Lite</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Other important SAP Notes need to be checked or applied:</p>\r\n<p><strong>C) Related SAP Notes for SAP NetWeaver 7.40</strong></p>\r\n<p>Refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2125084\">2125084</a>&#160;for a list of important corrections for SAP NetWeaver 7.40.</p>\r\n<p><strong>D) EA-FI Equivalence Levels:</strong></p>\r\n<p>EA-FI 200 SP20 corresponds to SAP Simple Finance, on-premise edition 1503 support package stack 16.</p>\r\n<p><strong>_________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK 15 (03/2021)</strong></p>\r\n<p><strong>A) Installation Requirements:</strong></p>\r\n<p>&#160;SAP Simple Finance, on-premise edition 1503 support package stack 15 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP Stack 22. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 22, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/1737650\">1737650</a>. Refer to the corresponding Upgrade Master Guide for detailed information on enhancement package 7 for SAP ERP 6.0. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 25. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a>. You can either update the underlying SAP NetWeaver 7.40 stack upfront or during the installation of the&#160;SAP Simple Finance, on-premise edition 1503.</li>\r\n<li>Use&#160;the&#160;<strong>kernel</strong>&#160;7.49 SP 900 or 7.53 SP700 or higher</li>\r\n<li>Before update to SP Stack 22 of enhancement package 7 for SAP ERP 6.0 and SAP Simple Finance, check if the SAP&#160;<strong>HANA database</strong>&#160;1.0 was already updated to the required minimal&#160;revision 122 of SP stack&#160;12 of SAP HANA&#160;database. For more information of SP stack 12, refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2165826\">2165826</a>,&#160;and follow the SAP Note <a target=\"_blank\" href=\"/notes/2021789\">2021789&#160;</a>to use higher revision.</li>\r\n<li>SAP&#160;<strong>HANA database 2.0</strong>&#160;SP stack 01 Revision 11 or higher is released for SAP Simple Finance&#160;1503 and enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2494263</a>, and follow the SAP Note <a target=\"_blank\" href=\"/notes/2378962\">2378962 </a>to use higher revision.</li>\r\n<li>In general the&#160;<strong>installation</strong>&#160;of SAP Simple Finance 1503&#160;into an existing ERP installation is only possible via the SUM tool. SAP recommends you update to the latest available patch level of SUM 1.0.</li>\r\n<li>If your system is already on SAP Simple Finance, on-premise edition 1503 and you would like to&#160;<strong>update</strong>&#160;to Support Package stack 15, you can also use SPAM.</li>\r\n</ul>\r\n<p><strong>B) Notes to be applied as part of this stack:</strong></p>\r\n<p><strong>Before the installation / upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 15</strong></p>\r\n<p>The following table lists SAP Notes, which must be applied to avoid issues during the installation/upgrade of the SAP Simple Finance or which are required to prepare the application data migration on the initial system constellation&#160;before the SAP Simple Finance installation/upgrade. For this purpose, check the validity of each of these SAP notes. Apply all notes relevant to your initial system constellation.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td width=\"825\">\r\n<p><em>Description</em></p>\r\n</td>\r\n<td width=\"150\">\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td width=\"104\">\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1925679\">1925679</a></p>\r\n</td>\r\n<td width=\"825\">DB views FMKK_BKPF_BSIK and FMKK_BKPF_BSAK were deleted</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2015-05-18</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1941711\">1941711</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Dump: INSERT_PROGRAM_NAME_BLANK during After-Import processing of SXCI / SXSD</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845848\">1845848</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Removing generated fields (\"_COUNTER\", \"_FISCPER\",...)</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845847\">1845847</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP HANA view generatn w/o flds _COUNTER, _FISCPER, _BUZEI3</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2172805\">2172805</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP Simple Finance upgrade: &#8220;COPC_V_PAYRQ_H&#8221; not activated, SAPK-70005INSAPFIN included in the target stack-xml</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2202445\">2202445</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Virtuelle Hierarchien werden nicht ins Zielsystem transportiert</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2207750\">2207750</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SQL-Fehler \"cannot use duplicate index name\" in MAIN_SHDCRE/SUBMOD_SHDDBCLONE/DBCLONE</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">\r\n<p>2015-08-21</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>During the installation or upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 14:</strong></p>\r\n<p>The following table lists SAP Notes, that must be applied during the installation/upgrade of SAP Simple Finance using SUM.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2146844\">2146844</a></p>\r\n</td>\r\n<td>\r\n<p>Conversion program: Pool/cluster to transparent terminates with return code 8 because views could not be created</p>\r\n</td>\r\n<td>\r\n<p>&#160;X</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The following table lists additional SAP Notes, that provide troubleshooting hints for known problems during the installation/upgrade.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2096457\">2096457</a></p>\r\n</td>\r\n<td>\r\n<p>Errors in ACT_UPG during SPDD: customer version of table can not be accessed</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2139357\">2139357</a></p>\r\n</td>\r\n<td>\r\n<p>SAP Simple Finance OP: Activation errors during installation of Support Packages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-06-15</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2086899\">2086899</a></p>\r\n</td>\r\n<td>\r\n<p>Upgrade phase MAIN_SHDRUN/ACT_UPG returns error messages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Migration related information - relevant if upgrading to SAP Simple Finance, on-premise edition 1503 for the first time:</strong></p>\r\n<p>Additional guidance for migration (consulting):</p>\r\n<p>The steps for application data migration have to be executed in a predefined order (see Migration Guide at&#160;<a target=\"_blank\" href=\"https://help.sap.com/sfin200\">help.sap.com/sfin200</a>&#160;for details). Some of these steps cannot be repeated. However in case of errors or after the implementation of corrections to the migration programs, this may be required. Follow the instructions in SAP Note&#160;<a target=\"_blank\" href=\"/notes/2138644\">2138644</a>&#160;and/or SAP Note&#160;<a target=\"_blank\" href=\"/notes/2155561\">2155561 i</a>n such cases.</p>\r\n<p><strong>Latest before application configuration and testing:</strong></p>\r\n<p>The following table lists important SAP Notes, which should be applied latest after the application data migration. They are required to avoid known problems during application configuration and testing.</p>\r\n<p>Cash Management:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2138445\">2138445</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: SAP Cash Management powered by SAP HANA as part of the SAP Simple Finance, on-premise edition 1503</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>If you do not use SAP Cash Management powered by SAP HANA, you must use the new Bank Account Management (BAM Lite) to migrate your house bank accounts and manage the new bank account master data. In this case, the following SAP Notes should be applied:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2154658\" title=\"2154658  - Release Information Note: Bank Account Management Lite\">2154658</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: Bank Account Management Lite</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Other important SAP Notes need to be checked or applied:</p>\r\n<p><strong>C) Related SAP Notes for SAP NetWeaver 7.40</strong></p>\r\n<p>Refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2125084\">2125084</a>&#160;for a list of important corrections for SAP NetWeaver 7.40.</p>\r\n<p><strong>D) EA-FI Equivalence Levels:</strong></p>\r\n<p>EA-FI 200 SP19 corresponds to SAP Simple Finance, on-premise edition 1503 support package stack 15.</p>\r\n<p><strong>_________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK 14 (09/2020)</strong></p>\r\n<p><strong>A) Installation Requirements:</strong></p>\r\n<p>&#160;SAP Simple Finance, on-premise edition 1503 support package stack 14 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP Stack 21. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 21, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/1737650\">1737650</a>. Refer to the corresponding Upgrade Master Guide for detailed information on enhancement package 7 for SAP ERP 6.0. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 24. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a>. You can either update the underlying SAP NetWeaver 7.40 stack upfront or during the installation of the&#160;SAP Simple Finance, on-premise edition 1503.</li>\r\n<li>Use&#160;the&#160;<strong>kernel</strong>&#160;7.49 SP 800 or 7.53 SP500 or higher</li>\r\n<li>Before update to SP Stack 21 of enhancement package 7 for SAP ERP 6.0 and SAP Simple Finance, check if the SAP&#160;<strong>HANA database</strong>&#160;1.0 was already updated to the required minimal&#160;revision 122 of SP stack&#160;12 of SAP HANA&#160;database. For more information of SP stack 12, refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2165826\">2165826</a>,&#160;and follow the&#160;<a target=\"_blank\" href=\"https://service.sap.com/&#126;sapidb/011000358700001182742013\">SAP HANA Revision and Maintenance Strategy</a>&#160;to use higher revision.</li>\r\n<li>SAP&#160;<strong>HANA database 2.0</strong>&#160;SP stack 01 Revision 11 or higher is released for SAP Simple Finance&#160;1503 and enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;</li>\r\n<li>In general the&#160;<strong>installation</strong>&#160;of SAP Simple Finance 1503&#160;into an existing ERP installation is only possible via the SUM tool. SAP recommends you update to the latest available patch level of SUM 1.0.</li>\r\n<li>If your system is already on SAP Simple Finance, on-premise edition 1503 and you would like to&#160;<strong>update</strong>&#160;to Support Package stack 14, you can also use SPAM.</li>\r\n</ul>\r\n<p><strong>B) Notes to be applied as part of this stack:</strong></p>\r\n<p><strong>Before the installation / upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 14</strong></p>\r\n<p>The following table lists SAP Notes, which must be applied to avoid issues during the installation/upgrade of the SAP Simple Finance or which are required to prepare the application data migration on the initial system constellation&#160;before the SAP Simple Finance installation/upgrade. For this purpose, check the validity of each of these SAP notes. Apply all notes relevant to your initial system constellation.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td width=\"825\">\r\n<p><em>Description</em></p>\r\n</td>\r\n<td width=\"150\">\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td width=\"104\">\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1925679\">1925679</a></p>\r\n</td>\r\n<td width=\"825\">DB views FMKK_BKPF_BSIK and FMKK_BKPF_BSAK were deleted</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2015-05-18</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1941711\">1941711</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Dump: INSERT_PROGRAM_NAME_BLANK during After-Import processing of SXCI / SXSD</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845848\">1845848</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Removing generated fields (\"_COUNTER\", \"_FISCPER\",...)</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845847\">1845847</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP HANA view generatn w/o flds _COUNTER, _FISCPER, _BUZEI3</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2172805\">2172805</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP Simple Finance upgrade: &#8220;COPC_V_PAYRQ_H&#8221; not activated, SAPK-70005INSAPFIN included in the target stack-xml</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2202445\">2202445</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Virtuelle Hierarchien werden nicht ins Zielsystem transportiert</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2207750\">2207750</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SQL-Fehler \"cannot use duplicate index name\" in MAIN_SHDCRE/SUBMOD_SHDDBCLONE/DBCLONE</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">\r\n<p>2015-08-21</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>During the installation or upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 14:</strong></p>\r\n<p>The following table lists SAP Notes, that must be applied during the installation/upgrade of SAP Simple Finance using SUM.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2146844\">2146844</a></p>\r\n</td>\r\n<td>\r\n<p>Conversion program: Pool/cluster to transparent terminates with return code 8 because views could not be created</p>\r\n</td>\r\n<td>\r\n<p>&#160;X</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The following table lists additional SAP Notes, that provide troubleshooting hints for known problems during the installation/upgrade.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2096457\">2096457</a></p>\r\n</td>\r\n<td>\r\n<p>Errors in ACT_UPG during SPDD: customer version of table can not be accessed</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2139357\">2139357</a></p>\r\n</td>\r\n<td>\r\n<p>SAP Simple Finance OP: Activation errors during installation of Support Packages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-06-15</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2086899\">2086899</a></p>\r\n</td>\r\n<td>\r\n<p>Upgrade phase MAIN_SHDRUN/ACT_UPG returns error messages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Migration related information - relevant if upgrading to SAP Simple Finance, on-premise edition 1503 for the first time:</strong></p>\r\n<p>Additional guidance for migration (consulting):</p>\r\n<p>The steps for application data migration have to be executed in a predefined order (see Migration Guide at&#160;<a target=\"_blank\" href=\"https://help.sap.com/sfin200\">help.sap.com/sfin200</a>&#160;for details). Some of these steps cannot be repeated. However in case of errors or after the implementation of corrections to the migration programs, this may be required. Follow the instructions in SAP Note&#160;<a target=\"_blank\" href=\"/notes/2138644\">2138644</a>&#160;and/or SAP Note&#160;<a target=\"_blank\" href=\"/notes/2155561\">2155561 i</a>n such cases.</p>\r\n<p><strong>Latest before application configuration and testing:</strong></p>\r\n<p>The following table lists important SAP Notes, which should be applied latest after the application data migration. They are required to avoid known problems during application configuration and testing.</p>\r\n<p>Cash Management:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2138445\">2138445</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: SAP Cash Management powered by SAP HANA as part of the SAP Simple Finance, on-premise edition 1503</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>If you do not use SAP Cash Management powered by SAP HANA, you must use the new Bank Account Management (BAM Lite) to migrate your house bank accounts and manage the new bank account master data. In this case, the following SAP Notes should be applied:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2154658\" title=\"2154658  - Release Information Note: Bank Account Management Lite\">2154658</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: Bank Account Management Lite</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Other important SAP Notes need to be checked or applied:</p>\r\n<p><strong>C) Related SAP Notes for SAP NetWeaver 7.40</strong></p>\r\n<p>Refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2125084\">2125084</a>&#160;for a list of important corrections for SAP NetWeaver 7.40.</p>\r\n<p><strong>D) EA-FI Equivalence Levels:</strong></p>\r\n<p>EA-FI 200 SP18 corresponds to SAP Simple Finance, on-premise edition 1503 support package stack 14.</p>\r\n<p><strong>_________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK 13 (03/2020)</strong></p>\r\n<p><strong>A) Installation Requirements:</strong></p>\r\n<p>&#160;SAP Simple Finance, on-premise edition 1503 support package stack 13 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP Stack 20. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 20, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/1737650\">1737650</a>. Refer to the corresponding Upgrade Master Guide for detailed information on enhancement package 7 for SAP ERP 6.0. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 23. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a>. You can either update the underlying SAP NetWeaver 7.40 stack upfront or during the installation of the&#160;SAP Simple Finance, on-premise edition 1503.</li>\r\n<li>Use&#160;the&#160;<strong>kernel</strong>&#160;7.49 SP 800 or 7.53 SP500 or higher</li>\r\n<li>Before update to SP Stack 20 of enhancement package 7 for SAP ERP 6.0 and SAP Simple Finance, check if the SAP&#160;<strong>HANA database</strong>&#160;1.0 was already updated to the required minimal&#160;revision 122 of SP stack&#160;12 of SAP HANA&#160;database. For more information of SP stack 12, refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2165826\">2165826</a>,&#160;and follow the&#160;<a target=\"_blank\" href=\"https://service.sap.com/&#126;sapidb/011000358700001182742013\">SAP HANA Revision and Maintenance Strategy</a>&#160;to use higher revision.</li>\r\n<li>SAP&#160;<strong>HANA database 2.0</strong>&#160;SP stack 01 Revision 11 or higher is released for SAP Simple Finance&#160;1503 and enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;</li>\r\n<li>In general the&#160;<strong>installation</strong>&#160;of SAP Simple Finance 1503&#160;into an existing ERP installation is only possible via the SUM tool. SAP recommends you update to the latest available patch level of SUM 1.0.</li>\r\n<li>If your system is already on SAP Simple Finance, on-premise edition 1503 and you would like to&#160;<strong>update</strong>&#160;to Support Package stack 13, you can also use SPAM.</li>\r\n</ul>\r\n<p><strong>B) Notes to be applied as part of this stack:</strong></p>\r\n<p><strong>Before the installation / upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 13</strong></p>\r\n<p>The following table lists SAP Notes, which must be applied to avoid issues during the installation/upgrade of the SAP Simple Finance or which are required to prepare the application data migration on the initial system constellation&#160;before the SAP Simple Finance installation/upgrade. For this purpose, check the validity of each of these SAP notes. Apply all notes relevant to your initial system constellation.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td width=\"825\">\r\n<p><em>Description</em></p>\r\n</td>\r\n<td width=\"150\">\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td width=\"104\">\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1925679\">1925679</a></p>\r\n</td>\r\n<td width=\"825\">DB views FMKK_BKPF_BSIK and FMKK_BKPF_BSAK were deleted</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2015-05-18</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1941711\">1941711</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Dump: INSERT_PROGRAM_NAME_BLANK during After-Import processing of SXCI / SXSD</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845848\">1845848</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Removing generated fields (\"_COUNTER\", \"_FISCPER\",...)</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845847\">1845847</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP HANA view generatn w/o flds _COUNTER, _FISCPER, _BUZEI3</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2172805\">2172805</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP Simple Finance upgrade: &#8220;COPC_V_PAYRQ_H&#8221; not activated, SAPK-70005INSAPFIN included in the target stack-xml</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2202445\">2202445</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Virtuelle Hierarchien werden nicht ins Zielsystem transportiert</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2207750\">2207750</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SQL-Fehler \"cannot use duplicate index name\" in MAIN_SHDCRE/SUBMOD_SHDDBCLONE/DBCLONE</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">\r\n<p>2015-08-21</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>During the installation or upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 13:</strong></p>\r\n<p>The following table lists SAP Notes, that must be applied during the installation/upgrade of SAP Simple Finance using SUM.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2146844\">2146844</a></p>\r\n</td>\r\n<td>\r\n<p>Conversion program: Pool/cluster to transparent terminates with return code 8 because views could not be created</p>\r\n</td>\r\n<td>\r\n<p>&#160;X</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The following table lists additional SAP Notes, that provide troubleshooting hints for known problems during the installation/upgrade.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2096457\">2096457</a></p>\r\n</td>\r\n<td>\r\n<p>Errors in ACT_UPG during SPDD: customer version of table can not be accessed</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2139357\">2139357</a></p>\r\n</td>\r\n<td>\r\n<p>SAP Simple Finance OP: Activation errors during installation of Support Packages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-06-15</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2086899\">2086899</a></p>\r\n</td>\r\n<td>\r\n<p>Upgrade phase MAIN_SHDRUN/ACT_UPG returns error messages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Migration related information - relevant if upgrading to SAP Simple Finance, on-premise edition 1503 for the first time:</strong></p>\r\n<p>Additional guidance for migration (consulting):</p>\r\n<p>The steps for application data migration have to be executed in a predefined order (see Migration Guide at&#160;<a target=\"_blank\" href=\"https://help.sap.com/sfin200\">help.sap.com/sfin200</a>&#160;for details). Some of these steps cannot be repeated. However in case of errors or after the implementation of corrections to the migration programs, this may be required. Follow the instructions in SAP Note&#160;<a target=\"_blank\" href=\"/notes/2138644\">2138644</a>&#160;and/or SAP Note&#160;<a target=\"_blank\" href=\"/notes/2155561\">2155561 i</a>n such cases.</p>\r\n<p><strong>Latest before application configuration and testing:</strong></p>\r\n<p>The following table lists important SAP Notes, which should be applied latest after the application data migration. They are required to avoid known problems during application configuration and testing.</p>\r\n<p>Cash Management:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2138445\">2138445</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: SAP Cash Management powered by SAP HANA as part of the SAP Simple Finance, on-premise edition 1503</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>If you do not use SAP Cash Management powered by SAP HANA, you must use the new Bank Account Management (BAM Lite) to migrate your house bank accounts and manage the new bank account master data. In this case, the following SAP Notes should be applied:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2154658\" title=\"2154658  - Release Information Note: Bank Account Management Lite\">2154658</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: Bank Account Management Lite</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Other important SAP Notes need to be checked or applied:</p>\r\n<p><strong>C) Related SAP Notes for SAP NetWeaver 7.40</strong></p>\r\n<p>Refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2125084\">2125084</a>&#160;for a list of important corrections for SAP NetWeaver 7.40.</p>\r\n<p><strong>D) EA-FI Equivalence Levels:</strong></p>\r\n<p>EA-FI 200 SP17 corresponds to SAP Simple Finance, on-premise edition 1503 support package stack 13.</p>\r\n<p><strong>_________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK 12 (10/2019)</strong></p>\r\n<p><strong>A) Installation Requirements:</strong></p>\r\n<p>&#160;SAP Simple Finance, on-premise edition 1503 support package stack 12 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP Stack&#160;19. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 19, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/1737650\">1737650</a>. Refer to the corresponding Upgrade Master Guide for detailed information on enhancement package 7 for SAP ERP 6.0. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 22. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a>. You can either update the underlying SAP NetWeaver 7.40 stack upfront or during the installation of the&#160;SAP Simple Finance, on-premise edition 1503.</li>\r\n<li>Use&#160;the&#160;<strong>kernel</strong>&#160;7.49 SP 700 or higher</li>\r\n<li>Before update to SP Stack&#160;19 of enhancement package 7 for SAP ERP 6.0 and SAP Simple Finance, check if the SAP&#160;<strong>HANA database</strong>&#160;1.0 was already updated to the required minimal&#160;revision 122 of SP stack&#160;12 of SAP HANA&#160;database. For more information of SP stack 12, refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2165826\">2165826</a>,&#160;and follow the&#160;<a target=\"_blank\" href=\"https://service.sap.com/&#126;sapidb/011000358700001182742013\">SAP HANA Revision and Maintenance Strategy</a>&#160;to use higher revision.</li>\r\n<li>SAP&#160;<strong>HANA database 2.0</strong>&#160;SP stack 01 Revision 11 or higher is released for SAP Simple Finance&#160;1503 and enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;</li>\r\n<li>In general the&#160;<strong>installation</strong>&#160;of SAP Simple Finance 1503&#160;into an existing ERP installation is only possible via the SUM tool. SAP recommends you update to the latest available patch level of SUM 1.0.</li>\r\n<li>If your system is already on SAP Simple Finance, on-premise edition 1503 and you would like to&#160;<strong>update</strong>&#160;to Support Package stack 12, you can also use SPAM.</li>\r\n</ul>\r\n<p><strong>B) Notes to be applied as part of this stack:</strong></p>\r\n<p><strong>Before the installation / upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 12</strong></p>\r\n<p>The following table lists SAP Notes, which must be applied to avoid issues during the installation/upgrade of the SAP Simple Finance or which are required to prepare the application data migration on the initial system constellation&#160;before the SAP Simple Finance installation/upgrade. For this purpose, check the validity of each of these SAP notes. Apply all notes relevant to your initial system constellation.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td width=\"825\">\r\n<p><em>Description</em></p>\r\n</td>\r\n<td width=\"150\">\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td width=\"104\">\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1925679\">1925679</a></p>\r\n</td>\r\n<td width=\"825\">DB views FMKK_BKPF_BSIK and FMKK_BKPF_BSAK were deleted</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2015-05-18</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1941711\">1941711</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Dump: INSERT_PROGRAM_NAME_BLANK during After-Import processing of SXCI / SXSD</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845848\">1845848</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Removing generated fields (\"_COUNTER\", \"_FISCPER\",...)</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845847\">1845847</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP HANA view generatn w/o flds _COUNTER, _FISCPER, _BUZEI3</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2172805\">2172805</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP Simple Finance upgrade: &#8220;COPC_V_PAYRQ_H&#8221; not activated, SAPK-70005INSAPFIN included in the target stack-xml</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2202445\">2202445</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Virtuelle Hierarchien werden nicht ins Zielsystem transportiert</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2207750\">2207750</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SQL-Fehler \"cannot use duplicate index name\" in MAIN_SHDCRE/SUBMOD_SHDDBCLONE/DBCLONE</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">\r\n<p>2015-08-21</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>During the installation or upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 12:</strong></p>\r\n<p>The following table lists SAP Notes, that must be applied during the installation/upgrade of SAP Simple Finance using SUM.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2146844\">2146844</a></p>\r\n</td>\r\n<td>\r\n<p>Conversion program: Pool/cluster to transparent terminates with return code 8 because views could not be created</p>\r\n</td>\r\n<td>\r\n<p>&#160;X</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The following table lists additional SAP Notes, that provide troubleshooting hints for known problems during the installation/upgrade.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2096457\">2096457</a></p>\r\n</td>\r\n<td>\r\n<p>Errors in ACT_UPG during SPDD: customer version of table can not be accessed</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2139357\">2139357</a></p>\r\n</td>\r\n<td>\r\n<p>SAP Simple Finance OP: Activation errors during installation of Support Packages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-06-15</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2086899\">2086899</a></p>\r\n</td>\r\n<td>\r\n<p>Upgrade phase MAIN_SHDRUN/ACT_UPG returns error messages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Migration related information - relevant if upgrading to SAP Simple Finance, on-premise edition 1503 for the first time:</strong></p>\r\n<p>Additional guidance for migration (consulting):</p>\r\n<p>The steps for application data migration have to be executed in a predefined order (see Migration Guide at&#160;<a target=\"_blank\" href=\"https://help.sap.com/sfin200\">help.sap.com/sfin200</a>&#160;for details). Some of these steps cannot be repeated. However in case of errors or after the implementation of corrections to the migration programs, this may be required. Follow the instructions in SAP Note&#160;<a target=\"_blank\" href=\"/notes/2138644\">2138644</a>&#160;and/or SAP Note&#160;<a target=\"_blank\" href=\"/notes/2155561\">2155561 i</a>n such cases.</p>\r\n<p><strong>Latest before application configuration and testing:</strong></p>\r\n<p>The following table lists important SAP Notes, which should be applied latest after the application data migration. They are required to avoid known problems during application configuration and testing.</p>\r\n<p>Cash Management:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2138445\">2138445</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: SAP Cash Management powered by SAP HANA as part of the SAP Simple Finance, on-premise edition 1503</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>If you do not use SAP Cash Management powered by SAP HANA, you must use the new Bank Account Management (BAM Lite) to migrate your house bank accounts and manage the new bank account master data. In this case, the following SAP Notes should be applied:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2154658\" title=\"2154658  - Release Information Note: Bank Account Management Lite\">2154658</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: Bank Account Management Lite</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Other important SAP Notes need to be checked or applied:</p>\r\n<p><strong>C) Related SAP Notes for SAP NetWeaver 7.40</strong></p>\r\n<p>Refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2125084\">2125084</a>&#160;for a list of important corrections for SAP NetWeaver 7.40.</p>\r\n<p><strong>D) EA-FI Equivalence Levels:</strong></p>\r\n<p>EA-FI 200 SP16 corresponds to SAP Simple Finance, on-premise edition 1503 support package stack 12.</p>\r\n<p><strong>_________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK 11 (03/2019)</strong></p>\r\n<p><strong>A) Installation Requirements:</strong></p>\r\n<p>&#160;SAP Simple Finance, on-premise edition 1503 support package stack 11 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP Stack&#160;18. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 18, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/1737650\">1737650</a>. Refer to the corresponding Upgrade Master Guide for detailed information on enhancement package 7 for SAP ERP 6.0. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 21. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a>. You can either update the underlying SAP NetWeaver 7.40 stack upfront or during the installation of the&#160;SAP Simple Finance, on-premise edition 1503.</li>\r\n<li>Use&#160;the&#160;<strong>kernel</strong>&#160;7.45 SP 800 or higher or kernel 7.49 SP 600 or higher</li>\r\n<li>Before update to SP Stack&#160;18 of enhancement package 7 for SAP ERP 6.0 and SAP Simple Finance, check if the SAP&#160;<strong>HANA database</strong>&#160;1.0 was already updated to the required minimal&#160;revision 122 of SP stack&#160;12 of SAP HANA&#160;database. For more information of SP stack 12, refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2165826\">2165826</a>,&#160;and follow the&#160;<a target=\"_blank\" href=\"https://service.sap.com/&#126;sapidb/011000358700001182742013\">SAP HANA Revision and Maintenance Strategy</a>&#160;to use higher revision.</li>\r\n<li>SAP&#160;<strong>HANA database 2.0</strong>&#160;SP stack 01 Revision 11 or higher is released for SAP Simple Finance&#160;1503 and enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;</li>\r\n<li>In general the&#160;<strong>installation</strong>&#160;of SAP Simple Finance 1503&#160;into an existing ERP installation is only possible via the SUM tool. SAP recommends you update to the latest available patch level of SUM 1.0.</li>\r\n<li>If your system is already on SAP Simple Finance, on-premise edition 1503 and you would like to&#160;<strong>update</strong>&#160;to Support Package stack 11, you can also use SPAM.</li>\r\n</ul>\r\n<p><strong>B) Notes to be applied as part of this stack:</strong></p>\r\n<p><strong>Before the installation / upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 11</strong></p>\r\n<p>The following table lists SAP Notes, which must be applied to avoid issues during the installation/upgrade of the SAP Simple Finance or which are required to prepare the application data migration on the initial system constellation&#160;before the SAP Simple Finance installation/upgrade. For this purpose, check the validity of each of these SAP notes. Apply all notes relevant to your initial system constellation.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td width=\"825\">\r\n<p><em>Description</em></p>\r\n</td>\r\n<td width=\"150\">\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td width=\"104\">\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1925679\">1925679</a></p>\r\n</td>\r\n<td width=\"825\">DB views FMKK_BKPF_BSIK and FMKK_BKPF_BSAK were deleted</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2015-05-18</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1941711\">1941711</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Dump: INSERT_PROGRAM_NAME_BLANK during After-Import processing of SXCI / SXSD</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845848\">1845848</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Removing generated fields (\"_COUNTER\", \"_FISCPER\",...)</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845847\">1845847</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP HANA view generatn w/o flds _COUNTER, _FISCPER, _BUZEI3</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2172805\">2172805</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP Simple Finance upgrade: &#8220;COPC_V_PAYRQ_H&#8221; not activated, SAPK-70005INSAPFIN included in the target stack-xml</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2202445\">2202445</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Virtuelle Hierarchien werden nicht ins Zielsystem transportiert</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2207750\">2207750</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SQL-Fehler \"cannot use duplicate index name\" in MAIN_SHDCRE/SUBMOD_SHDDBCLONE/DBCLONE</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">\r\n<p>2015-08-21</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>During the installation or upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 10:</strong></p>\r\n<p>The following table lists SAP Notes, that must be applied during the installation/upgrade of SAP Simple Finance using SUM.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2146844\">2146844</a></p>\r\n</td>\r\n<td>\r\n<p>Conversion program: Pool/cluster to transparent terminates with return code 8 because views could not be created</p>\r\n</td>\r\n<td>\r\n<p>&#160;X</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The following table lists additional SAP Notes, that provide troubleshooting hints for known problems during the installation/upgrade.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2096457\">2096457</a></p>\r\n</td>\r\n<td>\r\n<p>Errors in ACT_UPG during SPDD: customer version of table can not be accessed</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2139357\">2139357</a></p>\r\n</td>\r\n<td>\r\n<p>SAP Simple Finance OP: Activation errors during installation of Support Packages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-06-15</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2086899\">2086899</a></p>\r\n</td>\r\n<td>\r\n<p>Upgrade phase MAIN_SHDRUN/ACT_UPG returns error messages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Migration related information - relevant if upgrading to SAP Simple Finance, on-premise edition 1503 for the first time:</strong></p>\r\n<p>Additional guidance for migration (consulting):</p>\r\n<p>The steps for application data migration have to be executed in a predefined order (see Migration Guide at&#160;<a target=\"_blank\" href=\"https://help.sap.com/sfin200\">help.sap.com/sfin200</a>&#160;for details). Some of these steps cannot be repeated. However in case of errors or after the implementation of corrections to the migration programs, this may be required. Follow the instructions in SAP Note&#160;<a target=\"_blank\" href=\"/notes/2138644\">2138644</a>&#160;and/or SAP Note&#160;<a target=\"_blank\" href=\"/notes/2155561\">2155561 i</a>n such cases.</p>\r\n<p><strong>Latest before application configuration and testing:</strong></p>\r\n<p>The following table lists important SAP Notes, which should be applied latest after the application data migration. They are required to avoid known problems during application configuration and testing.</p>\r\n<p>Cash Management:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2138445\">2138445</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: SAP Cash Management powered by SAP HANA as part of the SAP Simple Finance, on-premise edition 1503</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>If you do not use SAP Cash Management powered by SAP HANA, you must use the new Bank Account Management (BAM Lite) to migrate your house bank accounts and manage the new bank account master data. In this case, the following SAP Notes should be applied:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2154658\" title=\"2154658  - Release Information Note: Bank Account Management Lite\">2154658</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: Bank Account Management Lite</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Other important SAP Notes need to be checked or applied:</p>\r\n<p><strong>C) Related SAP Notes for SAP NetWeaver 7.40</strong></p>\r\n<p>Refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2125084\">2125084</a>&#160;for a list of important corrections for SAP NetWeaver 7.40.</p>\r\n<p><strong>D) EA-FI Equivalence Levels:</strong></p>\r\n<p>EA-FI 200 SP15 corresponds to SAP Simple Finance, on-premise edition 1503 support package stack 11.</p>\r\n<p><strong>_________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK 10 (10/2018)</strong></p>\r\n<p><strong>A) Installation Requirements:</strong></p>\r\n<p>&#160;SAP Simple Finance, on-premise edition 1503 support package stack 10 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP Stack&#160;17. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 17, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/1737650\">1737650</a>. Refer to the corresponding Upgrade Master Guide for detailed information on enhancement package 7 for SAP ERP 6.0. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack 20. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a>. You can either update the underlying SAP NetWeaver 7.40 stack upfront or during the installation of the&#160;SAP Simple Finance, on-premise edition 1503.</li>\r\n<li>Use&#160;the&#160;<strong>kernel</strong>&#160;7.45 SP&#160;700 or higher or kernel 7.49 SP 500 or higher</li>\r\n<li>Before update to SP Stack&#160;17 of enhancement package 7 for SAP ERP 6.0 and SAP Simple Finance, check if the SAP&#160;<strong>HANA database</strong>&#160;1.0 was already updated to the required minimal&#160;revision 122 of SP stack&#160;12 of SAP HANA&#160;database, for more information of SP stack 12, refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2165826\">2165826</a>,&#160;and follow the&#160;<a target=\"_blank\" href=\"https://service.sap.com/&#126;sapidb/011000358700001182742013\">SAP HANA Revision and Maintenance Strategy</a>&#160;to use higher revision.</li>\r\n<li>SAP&#160;<strong>HANA database 2.0</strong>&#160;SP stack 01 Revision 11 or higher is released for SAP Simple Finance&#160;1503 and enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;</li>\r\n<li>In general the&#160;<strong>installation</strong>&#160;of SAP Simple Finance 1503&#160;into an existing ERP installation is only possible via the SUM tool. SAP recommends you update to the latest available patch level of SUM 1.0.</li>\r\n<li>If your system is already on SAP Simple Finance, on-premise edition 1503 and you would like to&#160;<strong>update</strong>&#160;to Support Package stack 10, you can also use SPAM.</li>\r\n</ul>\r\n<p><strong>B) Notes to be applied as part of this stack:</strong></p>\r\n<p><strong>Before the installation / upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 10</strong></p>\r\n<p>The following table lists SAP Notes, which must be applied to avoid issues during the installation/upgrade of the SAP Simple Finance or which are required to prepare the application data migration on the initial system constellation&#160;before the SAP Simple Finance installation/upgrade. For this purpose, check the validity of each of these SAP notes. Apply all notes relevant to your initial system constellation.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td width=\"825\">\r\n<p><em>Description</em></p>\r\n</td>\r\n<td width=\"150\">\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td width=\"104\">\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1925679\">1925679</a></p>\r\n</td>\r\n<td width=\"825\">DB views FMKK_BKPF_BSIK and FMKK_BKPF_BSAK were deleted</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2015-05-18</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1941711\">1941711</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Dump: INSERT_PROGRAM_NAME_BLANK during After-Import processing of SXCI / SXSD</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845848\">1845848</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Removing generated fields (\"_COUNTER\", \"_FISCPER\",...)</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845847\">1845847</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP HANA view generatn w/o flds _COUNTER, _FISCPER, _BUZEI3</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2172805\">2172805</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP Simple Finance upgrade: &#8220;COPC_V_PAYRQ_H&#8221; not activated, SAPK-70005INSAPFIN included in the target stack-xml</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2202445\">2202445</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Virtuelle Hierarchien werden nicht ins Zielsystem transportiert</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2207750\">2207750</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SQL-Fehler \"cannot use duplicate index name\" in MAIN_SHDCRE/SUBMOD_SHDDBCLONE/DBCLONE</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">\r\n<p>2015-08-21</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>During the installation or upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 10:</strong></p>\r\n<p>The following table lists SAP Notes, that must be applied during the installation/upgrade of SAP Simple Finance using SUM.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2146844\">2146844</a></p>\r\n</td>\r\n<td>\r\n<p>Conversion program: Pool/cluster to transparent terminates with return code 8 because views could not be created</p>\r\n</td>\r\n<td>\r\n<p>&#160;X</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The following table lists additional SAP Notes, that provide troubleshooting hints for known problems during the installation/upgrade.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2096457\">2096457</a></p>\r\n</td>\r\n<td>\r\n<p>Errors in ACT_UPG during SPDD: customer version of table can not be accessed</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2139357\">2139357</a></p>\r\n</td>\r\n<td>\r\n<p>SAP Simple Finance OP: Activation errors during installation of Support Packages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-06-15</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2086899\">2086899</a></p>\r\n</td>\r\n<td>\r\n<p>Upgrade phase MAIN_SHDRUN/ACT_UPG returns error messages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Migration related information - relevant if upgrading to SAP Simple Finance, on-premise edition 1503 for the first time:</strong></p>\r\n<p>Additional guidance for migration (consulting):</p>\r\n<p>The steps for application data migration have to be executed in a predefined order (see Migration Guide at&#160;<a target=\"_blank\" href=\"https://help.sap.com/sfin200\">help.sap.com/sfin200</a>&#160;for details). Some of these steps cannot be repeated. However in case of errors or after the implementation of corrections to the migration programs, this may be required. Follow the instructions in SAP Note&#160;<a target=\"_blank\" href=\"/notes/2138644\">2138644</a>&#160;and/or SAP Note&#160;<a target=\"_blank\" href=\"/notes/2155561\">2155561 i</a>n such cases.</p>\r\n<p><strong>Latest before application configuration and testing:</strong></p>\r\n<p>The following table lists important SAP Notes, which should be applied latest after the application data migration. They are required to avoid known problems during application configuration and testing.</p>\r\n<p>Cash Management:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2138445\">2138445</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: SAP Cash Management powered by SAP HANA as part of the SAP Simple Finance, on-premise edition 1503</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>If you do not use SAP Cash Management powered by SAP HANA, you must use the new Bank Account Management (BAM Lite) to migrate your house bank accounts and manage the new bank account master data. In this case, the following SAP Notes should be applied:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2154658\" title=\"2154658  - Release Information Note: Bank Account Management Lite\">2154658</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: Bank Account Management Lite</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Other important SAP Notes need to be checked or applied:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2738698\">2738698</a></p>\r\n</td>\r\n<td>\r\n<p>Bank chains: determination of intermediary banks with unexpected results</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2019-01-11</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>C) Related SAP Notes for SAP NetWeaver 7.40</strong></p>\r\n<p>Refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2125084\">2125084</a>&#160;for a list of important corrections for SAP NetWeaver 7.40.</p>\r\n<p><strong>D) EA-FI Equivalence Levels:</strong></p>\r\n<p>EA-FI 200 SP14 corresponds to SAP Simple Finance, on-premise edition 1503 support package stack 10.</p>\r\n<p><strong>Changes after release of support package stack 10</strong></p>\r\n<p>[11.Jan.2019] Add the SAP Note 2738698</p>\r\n<p><strong>_________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK 09 (03/2018)</strong></p>\r\n<p><strong>A) Installation Requirements:</strong></p>\r\n<p>&#160;SAP Simple Finance, on-premise edition 1503 support package stack 09 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP Stack&#160;16 .For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 16, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/1737650\">1737650</a>. Refer to the corresponding Upgrade Master Guide for detailed information on enhancement package 7 for SAP ERP 6.0. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;19. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a>. You can either update the underlying SAP NetWeaver 7.40 stack upfront or during the installation of the&#160;SAP Simple Finance, on-premise edition 1503.</li>\r\n<li>Use&#160;the&#160;<strong>kernel</strong>&#160;7.45 SP&#160;500 or higher or kernel 7.49 SP 300 or higher</li>\r\n<li>Before update to SP Stack&#160;16 of enhancement package 7 for SAP ERP 6.0 and SAP Simple Finance, check if the SAP&#160;<strong>HANA database</strong>&#160;1.0 was already updated to the required minimal&#160;revision 122 of SP stack&#160;12 of SAP HANA&#160;database, for more information of SP stack 12, refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2165826\">2165826</a>,&#160;and follow the&#160;<a target=\"_blank\" href=\"https://service.sap.com/&#126;sapidb/011000358700001182742013\">SAP HANA Revision and Maintenance Strategy</a>&#160;to use higher revision.</li>\r\n<li>SAP&#160;<strong>HANA database 2.0</strong>&#160;SP stack 01 Revision 11 or higher is released for SAP Simple Finance&#160;1503 and enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note&#160;<a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;</li>\r\n<li>In general the&#160;<strong>installation</strong>&#160;of SAP Simple Finance 1503&#160;into an existing ERP installation is only possible via the SUM tool. SAP recommends you update to the latest available patch level of SUM 1.0.</li>\r\n<li>If your system is already on SAP Simple Finance, on-premise edition 1503 and you would like to&#160;<strong>update</strong>&#160;to Support Package stack 09, you can also use SPAM.</li>\r\n</ul>\r\n<p><strong>B) Notes to be applied as part of this stack:</strong></p>\r\n<p><strong>Before the installation / upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 09</strong></p>\r\n<p>The following table lists SAP Notes, which must be applied to avoid issues during the installation/upgrade of the SAP Simple Finance or which are required to prepare the application data migration on the initial system constellation&#160;before the SAP Simple Finance installation/upgrade. For this purpose, check the validity of each of these SAP notes. Apply all notes relevant to your initial system constellation.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td width=\"825\">\r\n<p><em>Description</em></p>\r\n</td>\r\n<td width=\"150\">\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td width=\"104\">\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1925679\">1925679</a></p>\r\n</td>\r\n<td width=\"825\">DB views FMKK_BKPF_BSIK and FMKK_BKPF_BSAK were deleted</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2015-05-18</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1941711\">1941711</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Dump: INSERT_PROGRAM_NAME_BLANK during After-Import processing of SXCI / SXSD</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845848\">1845848</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Removing generated fields (\"_COUNTER\", \"_FISCPER\",...)</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845847\">1845847</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP HANA view generatn w/o flds _COUNTER, _FISCPER, _BUZEI3</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2172805\">2172805</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP Simple Finance upgrade: &#8220;COPC_V_PAYRQ_H&#8221; not activated, SAPK-70005INSAPFIN included in the target stack-xml</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2202445\">2202445</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Virtuelle Hierarchien werden nicht ins Zielsystem transportiert</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2207750\">2207750</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SQL-Fehler \"cannot use duplicate index name\" in MAIN_SHDCRE/SUBMOD_SHDDBCLONE/DBCLONE</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">\r\n<p>2015-08-21</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>During the installation or upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 09:</strong></p>\r\n<p>The following table lists SAP Notes, that must be applied during the installation/upgrade of SAP Simple Finance using SUM.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2146844\">2146844</a></p>\r\n</td>\r\n<td>\r\n<p>Conversion program: Pool/cluster to transparent terminates with return code 8 because views could not be created</p>\r\n</td>\r\n<td>\r\n<p>&#160;X</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The following table lists additional SAP Notes, that provide troubleshooting hints for known problems during the installation/upgrade.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2096457\">2096457</a></p>\r\n</td>\r\n<td>\r\n<p>Errors in ACT_UPG during SPDD: customer version of table can not be accessed</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2139357\">2139357</a></p>\r\n</td>\r\n<td>\r\n<p>SAP Simple Finance OP: Activation errors during installation of Support Packages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-06-15</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2086899\">2086899</a></p>\r\n</td>\r\n<td>\r\n<p>Upgrade phase MAIN_SHDRUN/ACT_UPG returns error messages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Migration related information - relevant if upgrading to SAP Simple Finance, on-premise edition 1503 for the first time:</strong></p>\r\n<p>Additional guidance for migration (consulting):</p>\r\n<p>The steps for application data migration have to be executed in a predefined order (see Migration Guide at&#160;<a target=\"_blank\" href=\"https://help.sap.com/sfin200\">help.sap.com/sfin200</a>&#160;for details). Some of these steps cannot be repeated. However in case of errors or after the implementation of corrections to the migration programs, this may be required. Follow the instructions in SAP Note&#160;<a target=\"_blank\" href=\"/notes/2138644\">2138644</a>&#160;and/or SAP Note&#160;<a target=\"_blank\" href=\"/notes/2155561\">2155561 i</a>n such cases.</p>\r\n<p><strong>Latest before application configuration and testing:</strong></p>\r\n<p>The following table lists important SAP Notes, which should be applied latest after the application data migration. They are required to avoid known problems during application configuration and testing.</p>\r\n<p>Cash Management:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2138445\">2138445</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: SAP Cash Management powered by SAP HANA as part of the SAP Simple Finance, on-premise edition 1503</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>If you do not use SAP Cash Management powered by SAP HANA, you must use the new Bank Account Management (BAM Lite) to migrate your house bank accounts and manage the new bank account master data. In this case, the following SAP Notes should be applied:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2154658\" title=\"2154658  - Release Information Note: Bank Account Management Lite\">2154658</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: Bank Account Management Lite</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Other important SAP Notes need to be checked or applied:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2738698\">2738698</a></p>\r\n</td>\r\n<td>\r\n<p>Bank chains: determination of intermediary banks with unexpected results</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2019-01-11</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>C) Related SAP Notes for SAP NetWeaver 7.40</strong></p>\r\n<p>Refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2125084\">2125084</a>&#160;for a list of important corrections for SAP NetWeaver 7.40.</p>\r\n<p><strong>D) EA-FI Equivalence Levels:</strong></p>\r\n<p>EA-FI 200 SP13 corresponds to SAP Simple Finance, on-premise edition 1503 support package stack 09.</p>\r\n<p><strong>Changes after release of support package stack 09</strong></p>\r\n<p>[11.Jan.2019]&#160;Add the SAP Note 2738698</p>\r\n<p><strong>_________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK 08 (01/2018)</strong></p>\r\n<p><strong>A) Installation Requirements:</strong></p>\r\n<p>&#160;SAP Simple Finance, on-premise edition 1503 support package stack 08 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP Stack&#160;15. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 15, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/1737650\">1737650</a>. Refer to the corresponding Upgrade Master Guide for detailed information on enhancement package 7 for SAP ERP 6.0. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;18. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a>. You can either update the underlying SAP NetWeaver 7.40 stack upfront or during the installation of the&#160;SAP Simple Finance, on-premise edition 1503.</li>\r\n<li>Use&#160;the&#160;<strong>kernel</strong> 7.45 SP&#160;500 or higher or kernel 7.49 SP 300 or higher</li>\r\n<li>Before update to SP Stack&#160;15 of enhancement package 7 for SAP ERP 6.0 and SAP Simple Finance, check if the SAP <strong>HANA database</strong> 1.0 was already updated to the required minimal&#160;revision 122 of SP stack&#160;12 of SAP HANA&#160;database, for more information of SP stack 12, refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2165826\">2165826</a>,&#160;and follow the&#160;<a target=\"_blank\" href=\"https://service.sap.com/&#126;sapidb/011000358700001182742013\">SAP HANA Revision and Maintenance Strategy</a>&#160;to use higher revision.</li>\r\n<li>SAP <strong>HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for SAP Simple Finance&#160;1503 and enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note <a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;</li>\r\n<li>In general the <strong>installation</strong> of SAP Simple Finance 1503&#160;into an existing ERP installation is only possible via the SUM tool. SAP recommends you update to the latest available patch level of SUM 1.0.</li>\r\n<li>If your system is already on SAP Simple Finance, on-premise edition 1503 and you would like to <strong>update</strong> to Support Package stack 08, you can also use SPAM.</li>\r\n</ul>\r\n<p><strong>B) Notes to be applied as part of this stack:</strong></p>\r\n<p><strong>Before the installation / upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 08:</strong></p>\r\n<p>The following table lists SAP Notes, which must be applied to avoid issues during the installation/upgrade of the SAP Simple Finance or which are required to prepare the application data migration on the initial system constellation&#160;before the SAP Simple Finance installation/upgrade. For this purpose, check the validity of each of these SAP notes. Apply all notes relevant to your initial system constellation.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td width=\"825\">\r\n<p><em>Description</em></p>\r\n</td>\r\n<td width=\"150\">\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td width=\"104\">\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1925679\">1925679</a></p>\r\n</td>\r\n<td width=\"825\">DB views FMKK_BKPF_BSIK and FMKK_BKPF_BSAK were deleted</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2015-05-18</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1941711\">1941711</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Dump: INSERT_PROGRAM_NAME_BLANK during After-Import processing of SXCI / SXSD</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1936455\">1936455</a></td>\r\n<td width=\"825\">Error in RS_TYPE_EXISTENCE_CHECK / Program RUTPRERN2VIEW hangs endlessly</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845848\">1845848</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Removing generated fields (\"_COUNTER\", \"_FISCPER\",...)</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845847\">1845847</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP HANA view generatn w/o flds _COUNTER, _FISCPER, _BUZEI3</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2172805\">2172805</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP Simple Finance upgrade: &#8220;COPC_V_PAYRQ_H&#8221; not activated, SAPK-70005INSAPFIN included in the target stack-xml</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2202445\">2202445</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Virtuelle Hierarchien werden nicht ins Zielsystem transportiert</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2207750\">2207750</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SQL-Fehler \"cannot use duplicate index name\" in MAIN_SHDCRE/SUBMOD_SHDDBCLONE/DBCLONE</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">\r\n<p>2015-08-21</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>During the installation or upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 08:</strong></p>\r\n<p>The following table lists SAP Notes, that must be applied during the installation/upgrade of SAP Simple Finance using SUM.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2146844\">2146844</a></p>\r\n</td>\r\n<td>\r\n<p>Conversion program: Pool/cluster to transparent terminates with return code 8 because views could not be created</p>\r\n</td>\r\n<td>\r\n<p>&#160;X</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The following table lists additional SAP Notes, that provide troubleshooting hints for known problems during the installation/upgrade.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2096457\">2096457</a></p>\r\n</td>\r\n<td>\r\n<p>Errors in ACT_UPG during SPDD: customer version of table can not be accessed</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2139357\">2139357</a></p>\r\n</td>\r\n<td>\r\n<p>SAP Simple Finance OP: Activation errors during installation of Support Packages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-06-15</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2086899\">2086899</a></p>\r\n</td>\r\n<td>\r\n<p>Upgrade phase MAIN_SHDRUN/ACT_UPG returns error messages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Migration related information - relevant if upgrading to SAP Simple Finance, on-premise edition 1503 for the first time:</strong></p>\r\n<p>Additional guidance for migration (consulting):</p>\r\n<p>The steps for application data migration have to be executed in a predefined order (see Migration Guide at&#160;<a target=\"_blank\" href=\"https://help.sap.com/sfin200\">help.sap.com/sfin200</a>&#160;for details). Some of these steps cannot be repeated. However in case of errors or after the implementation of corrections to the migration programs, this may be required. Follow the instructions in SAP Note&#160;<a target=\"_blank\" href=\"/notes/2138644\">2138644</a>&#160;and/or SAP Note&#160;<a target=\"_blank\" href=\"/notes/2155561\">2155561 i</a>n such cases.</p>\r\n<p><strong>Latest before application configuration and testing:</strong></p>\r\n<p>The following table lists important SAP Notes, which should be applied latest after the application data migration. They are required to avoid known problems during application configuration and testing.</p>\r\n<p>Cash Management:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2138445\">2138445</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: SAP Cash Management powered by SAP HANA as part of the SAP Simple Finance, on-premise edition 1503</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>If you do not use SAP Cash Management powered by SAP HANA, you must use the new Bank Account Management (BAM Lite) to migrate your house bank accounts and manage the new bank account master data. In this case, the following SAP Notes should be applied:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2154658\" title=\"2154658  - Release Information Note: Bank Account Management Lite\">2154658</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: Bank Account Management Lite</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Other important SAP Notes need to be checked or applied:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2738698\">2738698</a></p>\r\n</td>\r\n<td>\r\n<p>Bank chains: determination of intermediary banks with unexpected results</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2019-01-11</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>C) Related SAP Notes for SAP NetWeaver 7.40</strong></p>\r\n<p>Refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2125084\">2125084</a>&#160;for a list of important corrections for SAP NetWeaver 7.40.</p>\r\n<p><strong>D) EA-FI Equivalence Levels:</strong></p>\r\n<p>EA-FI 200 SP12 corresponds to SAP Simple Finance, on-premise edition 1503 support package stack 08.</p>\r\n<p><strong>Changes after release of support package stack 08</strong></p>\r\n<p>[11.Jan.2019]&#160;Add the SAP Note 2738698</p>\r\n<p><strong>_________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK 07 (07/2017)</strong></p>\r\n<p><strong>A) Installation Requirements:</strong></p>\r\n<p>&#160;SAP Simple Finance, on-premise edition 1503 support package stack 07 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP Stack&#160;14. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 14, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/1737650\">1737650</a>. Refer to the corresponding Upgrade Master Guide for detailed information on enhancement package 7 for SAP ERP 6.0. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;17. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a>. You can either update the underlying SAP NetWeaver 7.40 stack upfront or during the installation of the&#160;SAP Simple Finance, on-premise edition 1503.</li>\r\n<li>Use&#160;the&#160;<strong>kernel</strong> 7.45 SP&#160;500 or higher or kernel 7.49 SP 300 or higher</li>\r\n<li>Before update to SP Stack&#160;14 of enhancement package 7 for SAP ERP 6.0 and SAP Simple Finance, check if the SAP HANA database was already updated to the required minimal&#160;revision 122 of SP stack&#160;12 of SAP HANA&#160;database, for more information of SP stack 12, refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2165826\">2165826</a>,&#160;and follow the&#160;<a target=\"_blank\" href=\"https://service.sap.com/&#126;sapidb/011000358700001182742013\">SAP HANA Revision and Maintenance Strategy</a>&#160;to use higher revision.</li>\r\n</ul>\r\n<ul>\r\n<li>SAP <strong>HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for SAP Simple Finance&#160;1503 and enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note <a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;</li>\r\n</ul>\r\n<ul>\r\n<li>In general the installation of SAP Simple Finance 1503&#160;into an existing ERP installation is only possible via the SUM tool. SAP recommends you update to the latest available patch level of SUM 1.0.</li>\r\n<li>If your system is already on SAP Simple Finance, on-premise edition 1503 and you would like to update to Support Package stack 07, you can also use SPAM.</li>\r\n</ul>\r\n<p><strong>B) Notes to be applied as part of this stack:</strong></p>\r\n<p><strong>Before the installation / upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 07:</strong></p>\r\n<p>The following table lists SAP Notes, which must be applied to avoid issues during the installation/upgrade of the SAP Simple Finance or which are required to prepare the application data migration on the initial system constellation&#160;before the SAP Simple Finance installation/upgrade. For this purpose, check the validity of each of these SAP notes. Apply all notes relevant to your initial system constellation.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td width=\"825\">\r\n<p><em>Description</em></p>\r\n</td>\r\n<td width=\"150\">\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td width=\"104\">\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1925679\">1925679</a></p>\r\n</td>\r\n<td width=\"825\">DB views FMKK_BKPF_BSIK and FMKK_BKPF_BSAK were deleted</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2015-05-18</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1941711\">1941711</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Dump: INSERT_PROGRAM_NAME_BLANK during After-Import processing of SXCI / SXSD</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1936455\">1936455</a></td>\r\n<td width=\"825\">Error in RS_TYPE_EXISTENCE_CHECK / Program RUTPRERN2VIEW hangs endlessly</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845848\">1845848</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Removing generated fields (\"_COUNTER\", \"_FISCPER\",...)</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845847\">1845847</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP HANA view generatn w/o flds _COUNTER, _FISCPER, _BUZEI3</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2172805\">2172805</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP Simple Finance upgrade: &#8220;COPC_V_PAYRQ_H&#8221; not activated, SAPK-70005INSAPFIN included in the target stack-xml</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2202445\">2202445</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Virtuelle Hierarchien werden nicht ins Zielsystem transportiert</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2207750\">2207750</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SQL-Fehler \"cannot use duplicate index name\" in MAIN_SHDCRE/SUBMOD_SHDDBCLONE/DBCLONE</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">\r\n<p>2015-08-21</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>During the installation or upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 07:</strong></p>\r\n<p>The following table lists SAP Notes, that must be applied during the installation/upgrade of SAP Simple Finance using SUM.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2146844\">2146844</a></p>\r\n</td>\r\n<td>\r\n<p>Conversion program: Pool/cluster to transparent terminates with return code 8 because views could not be created</p>\r\n</td>\r\n<td>\r\n<p>&#160;X</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The following table lists additional SAP Notes, that provide troubleshooting hints for known problems during the installation/upgrade.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2096457\">2096457</a></p>\r\n</td>\r\n<td>\r\n<p>Errors in ACT_UPG during SPDD: customer version of table can not be accessed</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2139357\">2139357</a></p>\r\n</td>\r\n<td>\r\n<p>SAP Simple Finance OP: Activation errors during installation of Support Packages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-06-15</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2086899\">2086899</a></p>\r\n</td>\r\n<td>\r\n<p>Upgrade phase MAIN_SHDRUN/ACT_UPG returns error messages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Migration related information - relevant if upgrading to SAP Simple Finance, on-premise edition 1503 for the first time:</strong></p>\r\n<p>Additional guidance for migration (consulting):</p>\r\n<p>The steps for application data migration have to be executed in a predefined order (see Migration Guide at&#160;<a target=\"_blank\" href=\"https://help.sap.com/sfin200\">help.sap.com/sfin200</a>&#160;for details). Some of these steps cannot be repeated. However in case of errors or after the implementation of corrections to the migration programs, this may be required. Follow the instructions in SAP Note&#160;<a target=\"_blank\" href=\"/notes/2138644\">2138644</a>&#160;and/or SAP Note&#160;<a target=\"_blank\" href=\"/notes/2155561\">2155561 i</a>n such cases.</p>\r\n<p>&#160;</p>\r\n<p><strong>Latest before application configuration and testing:</strong></p>\r\n<p>The following table lists important SAP Notes, which should be applied latest after the application data migration. They are required to avoid known problems during application configuration and testing.</p>\r\n<p>Cash Management:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2138445\">2138445</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: SAP Cash Management powered by SAP HANA as part of the SAP Simple Finance, on-premise edition 1503</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>If you do not use SAP Cash Management powered by SAP HANA, you must use the new Bank Account Management (BAM Lite) to migrate your house bank accounts and manage the new bank account master data. In this case, the following SAP Notes should be applied:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2154658\" title=\"2154658  - Release Information Note: Bank Account Management Lite\">2154658</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: Bank Account Management Lite</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>C) Related SAP Notes for SAP NetWeaver 7.40</strong></p>\r\n<p>Refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2125084\">2125084</a>&#160;for a list of important corrections for SAP NetWeaver 7.40.</p>\r\n<p><strong>D) EA-FI Equivalence Levels:</strong></p>\r\n<p>EA-FI 200 SP11 corresponds to SAP Simple Finance, on-premise edition 1503 support package stack 07.</p>\r\n<p>&#160;</p>\r\n<p><strong>Changes made after release of SP stack 07:</strong></p>\r\n<p>[10.01.18] SAP HANA database 2.0 SP stack 01 Revision 11 or higher is released for SAP Simple Finance&#160;1503</p>\r\n<p>__________________________________________________________________________________________</p>\r\n<p><strong>SUPPORT PACKAGE STACK 06 (01/2017)</strong></p>\r\n<p><strong>A) Installation Requirements:</strong></p>\r\n<p>&#160;SAP Simple Finance, on-premise edition 1503 support package stack 06 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP Stack&#160;13. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 13, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/1737650\">1737650</a>. Refer to the corresponding Upgrade Master Guide for detailed information on enhancement package 7 for SAP ERP 6.0. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;16. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a>. You can either update the underlying SAP NetWeaver 7.40 stack upfront or during the installation of the&#160;SAP Simple Finance, on-premise edition 1503.</li>\r\n<li>Use&#160;the&#160;kernel 7.42 SP&#160;402 or higher.</li>\r\n</ul>\r\n<ul>\r\n<li>Before update to SP Stack&#160;13 of enhancement package 7 for SAP ERP 6.0 and SAP Simple Finance, check if the SAP HANA database was already updated to the required minimal&#160;revision 102 of SP stack 10 of SAP HANA&#160;database, for more information of SP stack 10, refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2165826\">2165826</a>,&#160;and follow the&#160;<a target=\"_blank\" href=\"https://service.sap.com/&#126;sapidb/011000358700001182742013\">SAP HANA Revision and Maintenance Strategy</a>&#160;to use higher revision.</li>\r\n</ul>\r\n<ul>\r\n<li>SAP <strong>HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for SAP Simple Finance&#160;1503 and enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note <a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;</li>\r\n</ul>\r\n<ul>\r\n<li>In general the installation of SAP Simple Finance 1503&#160;into an existing ERP installation is only possible via the SUM tool. SAP recommends you update to the latest available patch level of SUM 1.0.</li>\r\n<li>If your system is already on SAP Simple Finance, on-premise edition 1503 and you would like to update to Support Package stack 06, you can also use SPAM..</li>\r\n</ul>\r\n<p><strong>B) Notes to be applied as part of this stack:</strong></p>\r\n<p><strong>Before the installation / upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 06:</strong></p>\r\n<p>The following table lists SAP Notes, which must be applied to avoid issues during the installation/upgrade of the SAP Simple Finance or which are required to prepare the application data migration on the initial system constellation&#160;before the SAP Simple Finance installation/upgrade. For this purpose, check the validity of each of these SAP notes. Apply all notes relevant to your initial system constellation.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td width=\"825\">\r\n<p><em>Description</em></p>\r\n</td>\r\n<td width=\"150\">\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td width=\"104\">\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1925679\">1925679</a></p>\r\n</td>\r\n<td width=\"825\">DB views FMKK_BKPF_BSIK and FMKK_BKPF_BSAK were deleted</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2015-05-18</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1941711\">1941711</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Dump: INSERT_PROGRAM_NAME_BLANK during After-Import processing of SXCI / SXSD</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1936455\">1936455</a></td>\r\n<td width=\"825\">Error in RS_TYPE_EXISTENCE_CHECK / Program RUTPRERN2VIEW hangs endlessly</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845848\">1845848</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Removing generated fields (\"_COUNTER\", \"_FISCPER\",...)</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845847\">1845847</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP HANA view generatn w/o flds _COUNTER, _FISCPER, _BUZEI3</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2172805\">2172805</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP Simple Finance upgrade: &#8220;COPC_V_PAYRQ_H&#8221; not activated, SAPK-70005INSAPFIN included in the target stack-xml</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2202445\">2202445</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Virtuelle Hierarchien werden nicht ins Zielsystem transportiert</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2207750\">2207750</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SQL-Fehler \"cannot use duplicate index name\" in MAIN_SHDCRE/SUBMOD_SHDDBCLONE/DBCLONE</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">\r\n<p>2015-08-21</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>During the installation or upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 06:</strong></p>\r\n<p>The following table lists SAP Notes, that must be applied during the installation/upgrade of SAP Simple Finance using SUM.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2146844\">2146844</a></p>\r\n</td>\r\n<td>\r\n<p>Conversion program: Pool/cluster to transparent terminates with return code 8 because views could not be created</p>\r\n</td>\r\n<td>\r\n<p>&#160;X</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The following table lists additional SAP Notes, that provide troubleshooting hints for known problems during the installation/upgrade.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2096457\">2096457</a></p>\r\n</td>\r\n<td>\r\n<p>Errors in ACT_UPG during SPDD: customer version of table can not be accessed</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2139357\">2139357</a></p>\r\n</td>\r\n<td>\r\n<p>SAP Simple Finance OP: Activation errors during installation of Support Packages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-06-15</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2086899\">2086899</a></p>\r\n</td>\r\n<td>\r\n<p>Upgrade phase MAIN_SHDRUN/ACT_UPG returns error messages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Migration related information - relevant if upgrading to SAP Simple Finance, on-premise edition 1503 for the first time:</strong></p>\r\n<p>Additional guidance for migration (consulting):</p>\r\n<p>The steps for application data migration have to be executed in a predefined order (see Migration Guide at&#160;<a target=\"_blank\" href=\"https://help.sap.com/sfin200\">help.sap.com/sfin200</a>&#160;for details). Some of these steps cannot be repeated. However in case of errors or after the implementation of corrections to the migration programs, this may be required. Follow the instructions in SAP Note&#160;<a target=\"_blank\" href=\"/notes/2138644\">2138644</a>&#160;and/or SAP Note&#160;<a target=\"_blank\" href=\"/notes/2155561\">2155561 i</a>n such cases.</p>\r\n<p><strong>Latest before application configuration and testing:</strong></p>\r\n<p>The following table lists important SAP Notes, which should be applied latest after the application data migration. They are required to avoid known problems during application configuration and testing.</p>\r\n<p>Accounting:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;na</p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n<td>&nbsp;</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Asset Accounting:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;na</p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Controlling:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><em>&#160;na</em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;</em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;</em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;</em></p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Cash Management:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2138445\">2138445</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: SAP Cash Management powered by SAP HANA as part of the SAP Simple Finance, on-premise edition 1503</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>If you do not use SAP Cash Management powered by SAP HANA, you must use the new Bank Account Management (BAM Lite) to migrate your house bank accounts and manage the new bank account master data. In this case, the following SAP Notes should be applied:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2154658\" title=\"2154658  - Release Information Note: Bank Account Management Lite\">2154658</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: Bank Account Management Lite</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>C) Related SAP Notes for SAP NetWeaver 7.40</strong></p>\r\n<p>Refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2125084\">2125084</a>&#160;for a list of important corrections for SAP NetWeaver 7.40.</p>\r\n<p><strong>D) EA-FI Equivalence Levels:</strong></p>\r\n<p>EA-FI 200 SP10 corresponds to SAP Simple Finance, on-premise edition 1503 support package stack 06.</p>\r\n<p>&#160;</p>\r\n<p><strong>Changes made after release of SP stack 06:</strong></p>\r\n<p>(30.03.17) Removed of combined installation package of SAP EHP7 for SAP ERP 6.0 SP10&#160;and SAP Simple Finance, on-premise edition&#160;1503 SP03</p>\r\n<p>(30.03.17) NEW<strong>:</strong> SAP Solution Manager&#8217;s cloud-based <strong>Maintenance Planner</strong> is the successor of Maintenance Optimizer...</p>\r\n<p>[10.01.18] SAP <strong>HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for SAP Simple Finance&#160;1503</p>\r\n<p>-------------------------------------------------------------------------------------------------------------------------------------------------------</p>\r\n<p><strong>SUPPORT PACKAGE STACK 05 (08/2016)</strong></p>\r\n<p><strong>A) Installation Requirements:</strong></p>\r\n<p>&#160;SAP Simple Finance, on-premise edition 1503 support package stack 05 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP Stack&#160;12. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 12, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/1737650\">1737650</a>. Refer to the corresponding Upgrade Master Guide for detailed information on enhancement package 7 for SAP ERP 6.0. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 SP Stack&#160;15. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a>. You can either update the underlying SAP NetWeaver 7.40 stack upfront or during the installation of the&#160;SAP Simple Finance, on-premise edition 1503.</li>\r\n<li>Use&#160;the&#160;kernel 7.42 SP&#160;400 or higher.</li>\r\n</ul>\r\n<ul>\r\n<li>Before update to SP Stack&#160;12 of enhancement package 7 for SAP ERP 6.0 and SAP Simple Finance, check if the SAP HANA database was already updated to the required minimal&#160;revision 102 of SP stack 10 of SAP HANA&#160;database, for more information of SP stack 10, refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2165826\">2165826</a>,&#160;and follow the&#160;<a target=\"_blank\" href=\"https://service.sap.com/&#126;sapidb/011000358700001182742013\">SAP HANA Revision and Maintenance Strategy</a>&#160;to use higher revision.</li>\r\n</ul>\r\n<ul>\r\n<li>SAP <strong>HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for SAP Simple Finance&#160;1503 and enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note <a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;</li>\r\n</ul>\r\n<ul>\r\n<li>In general the installation of SAP Simple Finance 1503&#160;into an existing ERP installation is only possible via the SUM tool. SAP recommends you update to the latest available patch level of SUM 1.0.</li>\r\n<li>If your system is already on SAP Simple Finance, on-premise edition 1503 and you would like to update to Support Package stack 05, you can also use SPAM.</li>\r\n</ul>\r\n<p><strong>B) Notes to be applied as part of this stack:</strong></p>\r\n<p><strong>Before the installation / upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 05:</strong></p>\r\n<p>The following table lists SAP Notes, which must be applied to avoid issues during the installation/upgrade of the SAP Simple Finance or which are required to prepare the application data migration on the initial system constellation&#160;before the SAP Simple Finance installation/upgrade. For this purpose, check the validity of each of these SAP notes. Apply all notes relevant to your initial system constellation.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td width=\"825\">\r\n<p><em>Description</em></p>\r\n</td>\r\n<td width=\"150\">\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td width=\"104\">\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1925679\">1925679</a></p>\r\n</td>\r\n<td width=\"825\">DB views FMKK_BKPF_BSIK and FMKK_BKPF_BSAK were deleted</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2015-05-18</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1941711\">1941711</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Dump: INSERT_PROGRAM_NAME_BLANK during After-Import processing of SXCI / SXSD</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1936455\">1936455</a></td>\r\n<td width=\"825\">Error in RS_TYPE_EXISTENCE_CHECK / Program RUTPRERN2VIEW hangs endlessly</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845848\">1845848</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Removing generated fields (\"_COUNTER\", \"_FISCPER\",...)</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845847\">1845847</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP HANA view generatn w/o flds _COUNTER, _FISCPER, _BUZEI3</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2172805\">2172805</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP Simple Finance upgrade: &#8220;COPC_V_PAYRQ_H&#8221; not activated, SAPK-70005INSAPFIN included in the target stack-xml</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2202445\">2202445</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Virtuelle Hierarchien werden nicht ins Zielsystem transportiert</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2207750\">2207750</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SQL-Fehler \"cannot use duplicate index name\" in MAIN_SHDCRE/SUBMOD_SHDDBCLONE/DBCLONE</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">\r\n<p>2015-08-21</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>During the installation or upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 05:</strong></p>\r\n<p>The following table lists SAP Notes, that must be applied during the installation/upgrade of SAP Simple Finance using SUM.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2146844\">2146844</a></p>\r\n</td>\r\n<td>\r\n<p>Conversion program: Pool/cluster to transparent terminates with return code 8 because views could not be created</p>\r\n</td>\r\n<td>\r\n<p>&#160;X</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The following table lists additional SAP Notes, that provide troubleshooting hints for known problems during the installation/upgrade.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2096457\">2096457</a></p>\r\n</td>\r\n<td>\r\n<p>Errors in ACT_UPG during SPDD: customer version of table can not be accessed</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2139357\">2139357</a></p>\r\n</td>\r\n<td>\r\n<p>SAP Simple Finance OP: Activation errors during installation of Support Packages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-06-15</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2086899\">2086899</a></p>\r\n</td>\r\n<td>\r\n<p>Upgrade phase MAIN_SHDRUN/ACT_UPG returns error messages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Migration related information - relevant if upgrading to SAP Simple Finance, on-premise edition 1503 for the first time:</strong></p>\r\n<p>Additional guidance for migration (consulting):</p>\r\n<p>The steps for application data migration have to be executed in a predefined order (see Migration Guide at&#160;<a target=\"_blank\" href=\"https://help.sap.com/sfin200\">help.sap.com/sfin200</a>&#160;for details). Some of these steps cannot be repeated. However in case of errors or after the implementation of corrections to the migration programs, this may be required. Follow the instructions in SAP Note&#160;<a target=\"_blank\" href=\"/notes/2138644\">2138644</a>&#160;and/or SAP Note&#160;<a target=\"_blank\" href=\"/notes/2155561\">2155561 i</a>n such cases.</p>\r\n<p>The following table lists SAP Notes, which must be applied directly after the upgrade&#160;to SAP Simple Finance on-premise edition 1503 support package stack 04, i.e.&#160;before the application data migration.&#160;<br />These SAP Notes are crucial for a correct migration of application data.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td width=\"825\">\r\n<p><em>Description</em></p>\r\n</td>\r\n<td width=\"150\">\r\n<p><em>Manual activity&#160;</em><em><br />&#160;&#160;<em>required</em></em></p>\r\n</td>\r\n<td width=\"104\">\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2309339\">2309339</a></td>\r\n<td width=\"825\">sFIN migration: Performance improvement for initial calculation of depreciation</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2016-08-01</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2334465\">2334465</a></td>\r\n<td width=\"825\">Migration of FI-AA items: Correction items are posted to asset</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2016-08-01</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2377006\">2377006</a></td>\r\n<td width=\"825\">Error FINS_FI_MIG002 during migration of GL balances</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2017-01-09</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong>Latest before application configuration and testing:</strong></p>\r\n<p>The following table lists important SAP Notes, which should be applied latest after the application data migration. They are required to avoid known problems during application configuration and testing.</p>\r\n<p>Accounting:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n<td>&nbsp;</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Asset Accounting:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Controlling:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><em>&#160;-</em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;</em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;</em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;</em></p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Cash Management:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2138445\">2138445</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: SAP Cash Management powered by SAP HANA as part of the SAP Simple Finance, on-premise edition 1503</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>If you do not use SAP Cash Management powered by SAP HANA, you must use the new Bank Account Management (BAM Lite) to migrate your house bank accounts and manage the new bank account master data. In this case, the following SAP Notes should be applied:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2154658\" title=\"2154658  - Release Information Note: Bank Account Management Lite\">2154658</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: Bank Account Management Lite</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>C) Related SAP Notes for SAP NetWeaver 7.40</strong></p>\r\n<p>Refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2125084\">2125084</a>&#160;for a list of important corrections for SAP NetWeaver 7.40.</p>\r\n<p><strong>D) EA-FI Equivalence Levels:</strong></p>\r\n<p>EA-FI 200 SP09 corresponds to SAP Simple Finance, on-premise edition 1503 support package stack 05.</p>\r\n<p><strong>Changes made after release of SP stack 05:</strong></p>\r\n<p>(30.03.17) Removed of combined installation package of SAP EHP7 for SAP ERP 6.0 SP10&#160;and SAP Simple Finance, on-premise edition&#160;1503 SP03</p>\r\n<p>[10.01.18] SAP <strong>HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for SAP Simple Finance&#160;1503</p>\r\n<p>-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 04 (02/2016)</strong></span></p>\r\n<p><strong>A) Installation Requirements:</strong></p>\r\n<p>&#160;SAP Simple Finance, on-premise edition 1503 support package stack 04&#160;is based on:</p>\r\n<ul>\r\n<li><span style=\"text-decoration: underline;\">SAP enhancement package 7 for SAP ERP 6.0 SP Stack&#160;11</span> For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 11, see SAP Note <a target=\"_blank\" href=\"/notes/1737650\">1737650</a>. Refer to the corresponding Upgrade Master Guide for detailed information on enhancement package 7 for SAP ERP 6.0. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li><span style=\"text-decoration: underline;\">SAP NetWeaver 7.40 SP Stack&#160;13</span>. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a>. You can either update the underlying SAP NetWeaver 7.40 stack upfront or during the installation of the&#160;SAP Simple Finance, on-premise edition 03.</li>\r\n<li>Use&#160;the <span style=\"text-decoration: underline;\">kernel 7.42 SP&#160;200 or higher</span>.</li>\r\n</ul>\r\n<ul>\r\n<li>Before update to SP Stack&#160;11 of enhancement package 7 for SAP ERP 6.0 and SAP Simple Finance,&#160;check if the SAP HANA database was already updated to the required minimal&#160;revision 102 of SP stack 10 of SAP HANA&#160;database, for more information of SP stack 10, refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2165826\">2165826</a>,&#160;and follow the&#160;<a target=\"_blank\" href=\"https://service.sap.com/&#126;sapidb/011000358700001182742013\">SAP HANA Revision and Maintenance Strategy</a>&#160;to use higher revision.</li>\r\n<li>SAP <strong>HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for SAP Simple Finance&#160;1503 and enhancement package&#160;7 for SAP ERP 6.0,&#160;for further particulars we refer to SAP note <a target=\"_blank\" href=\"/notes/2494263\">2494263</a>.&#160;</li>\r\n<li>In general the installation of SAP Simple Finance 1503&#160;into an existing ERP installation is only possible via the SUM tool. SAP recommends you update to the latest available patch level of SUM 1.0.</li>\r\n<li>If your system is already on SAP Simple Finance, on-premise edition 1503 and you would like to update to Support Package stack 04, you can also use SPAM.</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><strong>B) Notes to be applied as part of this stack:</strong></p>\r\n<p><strong>Before the installation / upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 04:</strong></p>\r\n<p>The following table lists SAP Notes, which must be applied to avoid issues during the installation/upgrade of the SAP Simple Finance or which are required to prepare the application data migration on the initial system constellation <span style=\"text-decoration: underline;\">before the SAP Simple Finance installation/upgrade</span>. For this purpose, check the validity of each of these SAP notes. Apply all notes relevant to your initial system constellation.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\" style=\"width: 1143px;\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td width=\"825\">\r\n<p><em>Description</em></p>\r\n</td>\r\n<td width=\"150\">\r\n<p><em>Manual activity </em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td width=\"104\">\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1925679\">1925679</a></p>\r\n</td>\r\n<td width=\"825\">DB views FMKK_BKPF_BSIK and FMKK_BKPF_BSAK were deleted</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2015-05-18</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1941711\">1941711</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Dump: INSERT_PROGRAM_NAME_BLANK during After-Import processing of SXCI / SXSD</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/1936455\">1936455</a></td>\r\n<td width=\"825\">Error in RS_TYPE_EXISTENCE_CHECK / Program RUTPRERN2VIEW hangs endlessly</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845848\">1845848</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Removing generated fields (\"_COUNTER\", \"_FISCPER\",...)</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/1845847\">1845847</a></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP HANA view generatn w/o flds _COUNTER, _FISCPER, _BUZEI3</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"/notes/2172805\">2172805</a></span></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SAP Simple Finance upgrade: &#8220;COPC_V_PAYRQ_H&#8221; not activated, SAPK-70005INSAPFIN included in the target stack-xml</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>X</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"/notes/2202445\">2202445</a></span></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>Virtuelle Hierarchien werden nicht ins Zielsystem transportiert</p>\r\n</td>\r\n<td width=\"150\">\r\n<p>&#160;</p>\r\n</td>\r\n<td width=\"104\">\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"/notes/2207750\">2207750</a></span></p>\r\n</td>\r\n<td width=\"825\">\r\n<p>SQL-Fehler \"cannot use duplicate index name\" in MAIN_SHDCRE/SUBMOD_SHDDBCLONE/DBCLONE</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">\r\n<p>2015-08-21</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>During the installation or upgrade of SAP Simple Finance, on-premise edition 1503 support package stack 04:</strong></p>\r\n<p>The following table lists SAP Notes, that must be applied during the installation/upgrade of SAP Simple Finance using SUM.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity </em><em><br /><em>required</em></em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2146844\">2146844</a></p>\r\n</td>\r\n<td>\r\n<p>Conversion program: Pool/cluster to transparent terminates with return code 8 because views could not be created</p>\r\n</td>\r\n<td>\r\n<p>&#160;X</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The following table lists additional SAP Notes, that provide troubleshooting hints for known problems during the installation/upgrade.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity </em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2096457\">2096457</a></p>\r\n</td>\r\n<td>\r\n<p>Errors in ACT_UPG during SPDD: customer version of table can not be accessed</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-05-18</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2139357\">2139357 </a></p>\r\n</td>\r\n<td>\r\n<p>SAP Simple Finance OP: Activation errors during installation of Support Packages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-06-15</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"/notes/2086899\">2086899</a></span></p>\r\n</td>\r\n<td>\r\n<p>Upgrade phase MAIN_SHDRUN/ACT_UPG returns error messages</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2015-08-18</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><strong>Migration related information - relevant if upgrading to SAP Simple Finance, on-premise edition 1503 for the first time:</strong></strong></p>\r\n<p><span style=\"text-decoration: underline;\">Additional guidance for migration (consulting):</span></p>\r\n<p>The steps for application data migration have to be executed in a predefined order (see Migration Guide at <a target=\"_blank\" href=\"https://help.sap.com/sfin200\">help.sap.com/sfin200</a> for details). Some of these steps cannot be repeated. However in case of errors or after the implementation of corrections to the migration programs, this may be required. Follow the instructions in SAP Note <a target=\"_blank\" href=\"/notes/2138644\">2138644</a> and/or SAP Note <a target=\"_blank\" href=\"/notes/2155561\">2155561 i</a>n such cases.</p>\r\n<p><span style=\"text-decoration: underline;\">Migration notes:</span></p>\r\n<p>Before starting with notes implementation, implement below notes, in particular read and follow the given instructions to ensure that the additional migration notes can be implemented successfully.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><em>Note ID</em></td>\r\n<td><em>Description</em></td>\r\n<td>\r\n<p><em>Manual activity <br /></em><em>required</em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The following table lists SAP Notes, which must be applied directly after the upgrade&#160;to SAP Simple Finance on-premise edition 1503 support package stack 04, i.e. <span style=\"text-decoration: underline;\">before the application data migration</span>. <br />These SAP Notes are crucial for a correct migration of application data.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 1143px;\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td width=\"825\">\r\n<p><em>Description</em></p>\r\n</td>\r\n<td width=\"150\">\r\n<p><em>Manual activity </em><em><br />&#160; <em>required</em></em></p>\r\n</td>\r\n<td width=\"104\">\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2259159\">2259159</a></td>\r\n<td width=\"825\">sFIN Migration: Wrong values in field ACDOCA-PREC_AWITEM</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2016-01-29</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2265477\">2265477</a></td>\r\n<td width=\"825\">sFIN Migration: Duplicate ACDOCA records for classic G/L</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2016-01-29</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2266209\">2266209</a></td>\r\n<td width=\"825\">sFIN Migration: ACDOCA-RKCUR not filled for all items in non-leading ledgers</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2016-01-29</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2269775\">2269775</a></td>\r\n<td width=\"825\">sFIN Migration: Wrong BLART &amp; AWITGRP for ACDOCA items created from COEP items of business transaction COIE or INV*</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2016-01-29</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2250671\">2250671</a></td>\r\n<td width=\"825\">Migration: Step 'Enrichment' fails</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2016-01-29</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2265261\">2265261</a></td>\r\n<td width=\"825\">Syntax Error in CL_FINS_CO_MIG_UTIL: Invalid statement for \"COBK\"</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2016-01-29</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2262531\">2262531</a></td>\r\n<td width=\"825\">CL_FINS_CO_MIG_COB: syntax error 'Invalid statement for COSP_BAK'</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2016-01-29</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2276510\">2276510&#160;</a></td>\r\n<td width=\"825\">sFIN Migration: Empty ACDOCA-KSL for COEP and classic G/L</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2016-04-25</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2276979\">2276979&#160;</a></td>\r\n<td width=\"825\">sFIN migration: Missing CO amounts not displayed in reconciliation of line items</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2016-04-25</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2242401\">2242401</a></td>\r\n<td width=\"825\">\r\n<p>FINS FI-AA migration: Checking and displaying the pre-analysis results in step</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2016-06-22</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2253004\">2253004</a></td>\r\n<td width=\"825\">\r\n<p>Currency type of real time posting area is not initial</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2016-06-22</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2256721\">2256721</a></td>\r\n<td width=\"825\">\r\n<p>IDOC_INPUT_FIXEDASSET_CREATEIV has status 53 but legacy assets are missing</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2016-06-22</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2260723\">2260723</a></td>\r\n<td width=\"825\">\r\n<p>Cost centers not displayed in ANLP after migration</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2016-06-22</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2266158\">2266158</a></td>\r\n<td width=\"825\">\r\n<p>Migration: During reset of balance migration table FAAT_YDDA is not reset compl</p>\r\n</td>\r\n<td width=\"150\">X</td>\r\n<td width=\"104\">2016-06-22</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2266306\">2266306</a></td>\r\n<td width=\"825\">\r\n<p>sFIN Migration: check the max open fiscal year of asset accounting</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2016-06-22</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2270220\">2270220</a></td>\r\n<td width=\"825\">\r\n<p>sFIN Migration: Wrong debit/credit indicator and posting key in ACDOCA for CO items with zero amounts in at least one currency</p>\r\n</td>\r\n<td width=\"150\">X</td>\r\n<td width=\"104\">2016-06-22</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2270905\">2270905</a></td>\r\n<td width=\"825\">\r\n<p>FI-AA migration RC4: Error message FINS_RECON781 for group assets</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2016-06-22</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2276807\">2276807</a></td>\r\n<td width=\"825\">\r\n<p>Migration - XANTEI 5 f&#252;r BUHBKT und MOVCAT zur&#252;cksetzen</p>\r\n</td>\r\n<td width=\"150\">X</td>\r\n<td width=\"104\">2016-06-22</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2278622\">2278622</a></td>\r\n<td width=\"825\">\r\n<p>Migration: Enrich additional fields in table COEP</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2016-06-22</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2279934\">2279934</a></td>\r\n<td width=\"825\">\r\n<p>Migration: Line items missing in Unified Journal</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2016-06-22</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2301034\">2301034</a></td>\r\n<td width=\"825\">\r\n<p>sFIN migration: Runtime error OBJECTS_OBJREF_NOT_ASSIGNED in CL_FAA_DC_SEGMENTS</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2016-06-22</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2324085\">2324085</a></td>\r\n<td width=\"825\">\r\n<p>sFIN Migration: Wrong sign for quantities migrated from BSEG credit i&#8230;</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2016-06-22</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2325862\">2325862</a></td>\r\n<td width=\"825\">\r\n<p>sFIN Migration: Wrong values in ACDOCA-AWITEM</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2016-06-22</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2325045\">2325045</a></td>\r\n<td width=\"825\">\r\n<p>Unnecessary records created in the migration of CO totals</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2016-06-22</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2327243\">2327243</a></td>\r\n<td width=\"825\">\r\n<p>sFIN Migration: Wrong Debit/Credit Indicator for BSEG-XNEGP items</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2016-06-22</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2298836\">2298836</a></td>\r\n<td width=\"825\">\r\n<p>Migration: Entry in BSEG without corresponding entry in ACDOCA</p>\r\n</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2016-07-20</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2323496\">2323496</a></td>\r\n<td width=\"825\">Enrichment fails for CO documents</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2016-07-20</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2309339\">2309339</a></td>\r\n<td width=\"825\">sFIN migration: Performance improvement for initial calculation of depreciation</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2016-08-01</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2334465\">2334465</a></td>\r\n<td width=\"825\">Migration of FI-AA items: Correction items are posted to asset</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2016-08-01</td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"/notes/2377006\">2377006</a></td>\r\n<td width=\"825\">Error FINS_FI_MIG002 during migration of GL balances</td>\r\n<td width=\"150\"></td>\r\n<td width=\"104\">2017-01-09</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong>Latest before application configuration and testing: </strong></p>\r\n<p>The following table lists important SAP Notes, which should be applied latest after the application data migration. They are required to avoid known problems during application configuration and testing.</p>\r\n<p><span style=\"text-decoration: underline;\">Accounting:</span></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity </em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n<td>&nbsp;</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><span style=\"text-decoration: underline;\">Asset Accounting:</span></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity </em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><span style=\"text-decoration: underline;\">Controlling:</span></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity </em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><em>&#160;-</em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;</em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;</em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;</em></p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><span style=\"text-decoration: underline;\">Cash Management:</span></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"/notes/2138445\">2138445</a></span></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: SAP Cash Management powered by SAP HANA as part of the SAP Simple Finance, on-premise edition 1503</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>If you do not use SAP Cash Management powered by SAP HANA, you must use the new Bank Account Management (BAM Lite) to migrate your house bank accounts and manage the new bank account master data. In this case, the following SAP Notes should be applied:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2154658\" title=\"2154658  - Release Information Note: Bank Account Management Lite\">2154658</a></p>\r\n</td>\r\n<td>\r\n<p>Release Information Note: Bank Account Management Lite</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>C) Related SAP Notes for SAP NetWeaver 7.40</strong></p>\r\n<p>Refer to SAP Note <a target=\"_blank\" href=\"/notes/2125084\">2125084</a>&#160;for a list of important corrections for SAP NetWeaver 7.40.</p>\r\n<p><strong>D) EA-FI Equivalence Levels:</strong></p>\r\n<p>EA-FI 200 SP08&#160;corresponds to SAP Simple Finance, on-premise edition 1503 support package stack 04.</p>\r\n<p>&#160;</p>\r\n<p><strong>Changes made after release of SP stack 04:</strong></p>\r\n<p>(30.03.17) Removed of combined installation package of SAP EHP7 for SAP ERP 6.0 SP10&#160;and SAP Simple Finance, on-premise edition&#160;1503 SP03</p>\r\n<p>[10.01.18] SAP <strong>HANA database 2.0</strong> SP stack 01 Revision 11 or higher is released for SAP Simple Finance&#160;1503</p>\r\n<p>-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------</p>\r\n<p>For similar information on the previous support package stacks, refer to SAP Note: <a target=\"_blank\" href=\"/notes/2247816\">2247816</a></p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I027565)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I027565)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002117481/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002117481/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002117481/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002117481/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002117481/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002117481/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002117481/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002117481/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002117481/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2253237", "RefComponent": "FIN-MIG", "RefTitle": "SAP Simple Finance, on-premise edition 1503: Support Package Stack Recommendations", "RefUrl": "/notes/2253237"}, {"RefNumber": "2171868", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Simple Finance / S/4HANA Finance: Information on Product Name and Versioning Changes", "RefUrl": "/notes/2171868"}, {"RefNumber": "2157996", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA Finance: Checklist for Technical Installation / Upgrade", "RefUrl": "/notes/2157996"}, {"RefNumber": "2148769", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA 1 SPS09 Revision 095.00", "RefUrl": "/notes/2148769"}, {"RefNumber": "2138515", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA 1 SPS09 Revision 094.00", "RefUrl": "/notes/2138515"}, {"RefNumber": "2131643", "RefComponent": "FI-GL-IS", "RefTitle": "Replacement of SAP Simple Finance add-on 1.0 reporting content with SAP Simple Finance, on-premise edition 1503 or 1605", "RefUrl": "/notes/2131643"}, {"RefNumber": "2127080", "RefComponent": "XX-SER-REL", "RefTitle": "Release restrictions for SAP Simple Finance on-premise edition 1503", "RefUrl": "/notes/2127080"}, {"RefNumber": "2125084", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Simple Finance, on-premise edition 1503: Important SAP NETWEAVER 7.40 corrections", "RefUrl": "/notes/2125084"}, {"RefNumber": "2119188", "RefComponent": "XX-SER-REL", "RefTitle": "Release Scope Information: SAP Simple Finance, on-premise edition 1503", "RefUrl": "/notes/2119188"}, {"RefNumber": "2117719", "RefComponent": "XX-SER-REL", "RefTitle": "Release Information Note: SAP Fiori for the SAP Simple Finance, on-premise edition 1503", "RefUrl": "/notes/2117719"}, {"RefNumber": "2110214", "RefComponent": "FI-GL", "RefTitle": "SAP S/4HANA Finance Documentation Corrections", "RefUrl": "/notes/2110214"}, {"RefNumber": "2103558", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Simple Finance, on-premise edition 1503: Compatible add-ons", "RefUrl": "/notes/2103558"}, {"RefNumber": "2090914", "RefComponent": "BC-CTS-HTC", "RefTitle": "SAP Simple Finance add-on 1.0 installation using SWPM stops during HANA content activation", "RefUrl": "/notes/2090914"}, {"RefNumber": "2044295", "RefComponent": "FIN-FSCM-CM", "RefTitle": "SAP Cash Management: License and Usage Information", "RefUrl": "/notes/2044295"}, {"RefNumber": "1971111", "RefComponent": "FI-GL-IS", "RefTitle": "SAP S/4HANA Finance: Browser Requirements", "RefUrl": "/notes/1971111"}, {"RefNumber": "1737650", "RefComponent": "XX-SER-REL", "RefTitle": "EHP7 for SAP ERP 6.0 SP Stacks - Release & Information Note", "RefUrl": "/notes/1737650"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2049241", "RefComponent": "XX-SER-REL", "RefTitle": "Intellectual Property Management and Financials add-on for SAP Business Suite powered by SAP HANA", "RefUrl": "/notes/2049241 "}, {"RefNumber": "2261242", "RefComponent": "FIN-MIG", "RefTitle": "SAP S/4HANA Finance: Information on Upgrade Paths", "RefUrl": "/notes/2261242 "}, {"RefNumber": "2247816", "RefComponent": "XX-SER-REL", "RefTitle": "Archive: 2117481 - Release Information Note: SAP Simple Finance, on-premise edition 1503", "RefUrl": "/notes/2247816 "}, {"RefNumber": "1939991", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy: financials add-on for SAP Business Suite powered by SAP HANA", "RefUrl": "/notes/1939991 "}, {"RefNumber": "2185245", "RefComponent": "FIN-MIG", "RefTitle": "New SAP S/4HANA installations (Release 1503 and higher): Migration of customizing settings in Financials required?", "RefUrl": "/notes/2185245 "}, {"RefNumber": "2172805", "RefComponent": "FI-GL", "RefTitle": "SAP Simple Finance upgrade: \"COPC_V_PAYRQ_H\" not activated, SAPK-70005INSAPFIN included in target stack-xml", "RefUrl": "/notes/2172805 "}, {"RefNumber": "2157996", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA Finance: Checklist for Technical Installation / Upgrade", "RefUrl": "/notes/2157996 "}, {"RefNumber": "2112354", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Simple Finance, on-premise edition: Important installation information", "RefUrl": "/notes/2112354 "}, {"RefNumber": "2119188", "RefComponent": "XX-SER-REL", "RefTitle": "Release Scope Information: SAP Simple Finance, on-premise edition 1503", "RefUrl": "/notes/2119188 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}