{"Request": {"Number": "1268987", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 668, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000007417282017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001268987?language=E&token=2B146B2B76DB3B43B9CDA1F91C57D752"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001268987", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001268987/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1268987"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Currentness", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.11.2008"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-AT-IS-H"}, "SAPComponentKeyText": {"_label": "Component", "value": "Hospital"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Austria", "value": "XX-CSC-AT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-AT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-specific component", "value": "XX-CSC-AT-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-AT-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hospital", "value": "XX-CSC-AT-IS-H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-AT-IS-H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1268987 - IS-H AT: EDIVKA - Changes"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1268987&TargetLanguage=EN&Component=XX-CSC-AT-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/1268987/D\" target=\"_blank\">/notes/1268987/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This SAP Note is relevant only for the country version AT (Austria). This SAP Note delivers the following new functions for data exchange with private insurance (EDIVKA).</p> <OL>1. An evaluation report that provides an overview of EDIVKA messages and insurance verification (RNWAT_EDI_LIST_EDIVKA). For a detailed description, see the report documentation.</OL> <OL>2. In the invoice overview (transaction NA30N), the following information is output for invoices that are sent using EDLEI.</OL> <OL><OL>a) Dispatch date of the last EDLEI message for the invoice (EDI Date field)</OL></OL> <OL><OL>b) Status of the last EDLEI message sent for the invoice (Send Status field)</OL></OL> <OL><OL>c) Status of the last imported payment advice note for the invoice (field Rec.Status)</OL></OL> <OL><OL>d) Import date of the last payment advice note for the bill (Rec.date)</OL></OL> <OL>3. The dunning report for insurance verification (RNWATKOSMA) now also selects cases with insurance providers that participate in the electronic data exchange procedure EDKOS (in the same way as for P321). If you do not want this, you must exclude the EDIVKA insurance providers on the selection screen or adjust any existing variants.<br />Since there is no separate message type for the reminder, the last message sent (AUFN, VLG_AUFN, AEND) is sent with a comment in the Admission Reason Free Text field.</OL> <OL>4. Change to admission notification dispatch logic: The system does not send an admission notification if only the companion has supplementary insurance. In addition, an admission notification is only sent to the private insurance of the patient if the patient and the companion have different private insurances active in the case.</OL><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>RNWAT_EDI_LIST, EDIVKA, EDKOS, EDI, data interchange</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>Advance development</p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><OL>1. Before you implement the source code corrections, implement the attached attachment as follows:</OL> <OL><OL>a) Unpack the attached file:</OL></OL> <p>                       HW1268987_604.zip for IS-H Version 6.04 <p>                       HW1268987_603.zip for IS-H Version 6.03 <p>                       HW1268987_600.zip for IS-H Version 6.00 <p>                       HW1268987_472.zip for IS-H Version 4.72 <p>                       Note that you cannot download the attached files using OSS, but only from SAP Service Marketplace (see also SAP Notes 480180 and 13719 for information about importing attachments).<br /><br />When you import the attachment transports, generation errors may occur that are corrected after you implement the source code corrections from this SAP Note. <OL>2. You must then perform the following manual tasks:</OL> <OL><OL>a) Enhance the structure ISH_VBRK_DETAIL:</OL></OL> <UL><UL><LI>Go to transaction SE11.</LI></UL></UL> <UL><UL><LI>Select &quot;Data type&quot; and enter the value ISH_VBRK_DETAIL in the input field.</LI></UL></UL> <UL><UL><LI>Choose &quot;Change&quot;.</LI></UL></UL> <UL><UL><LI>Now position the cursor on the first free cell at the end of the structure.</LI></UL></UL> <UL><UL><LI>In the menu bar, choose &quot;Edit -> Include -> Insert&quot;.</LI></UL></UL> <UL><UL><LI>In the Structure field, enter RNWAT_VBRK_DETAIL. Choose &quot;Continue&quot;.</LI></UL></UL> <UL><UL><LI>Save and activate the structure.</LI></UL></UL> <OL><OL>b) Create text symbol 101 in the function group NWAT_EDIVKA:</OL></OL> <UL><UL><LI>Call transaction SE80.</LI></UL></UL> <UL><UL><LI>In the input help of the upper of the two input fields, choose &quot;Function Group&quot;. Enter the function group name NWAT_EDIVKA in the lower input field. Confirm your entry.</LI></UL></UL> <UL><UL><LI>Double-click the node &quot;NWAT_EDIVKA&quot; in the object list.</LI></UL></UL> <UL><UL><LI>In the dialog box that appears, choose &quot;Main Program&quot;.</LI></UL></UL> <UL><UL><LI>In the menu bar, choose &quot;Goto -> Text Elements -> Text Symbols&quot;.</LI></UL></UL> <UL><UL><LI>Click on &quot;Change&quot; button in the application toolbar.</LI></UL></UL> <UL><UL><LI>Position the cursor on the first empty line and enter the following values:<br />Sym:  101<br />Text: This message is sent because there was no reply to our last message from &amp;1.<br />mLen 132</LI></UL></UL> <UL><UL><LI>Save the changes actively.</LI></UL></UL> <p>Now implement the source code corrections.</p></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Owner                                                                                    ", "Value": "C5021078"}, {"Key": "Processor                                                                                          ", "Value": "C5013524"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001268987/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "HW1268987_472.zip", "FileSize": "57", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000724332008&iv_version=0003&iv_guid=01B87E9453DA7A43BDC5ED60ACA21523"}, {"FileName": "HW1268987_604.zip", "FileSize": "76", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000724332008&iv_version=0003&iv_guid=C7F3282B529DA04E91C1F1733BD940B5"}, {"FileName": "HW1268987_603.zip", "FileSize": "55", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000724332008&iv_version=0003&iv_guid=6A22C3C376B31D4985EE7C2EE370DD0E"}, {"FileName": "HW1268987_600.zip", "FileSize": "56", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000724332008&iv_version=0003&iv_guid=C413691FC79E7F4695BCB259AAA2F819"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "IS-H", "From": "472", "To": "472", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": [{"SoftwareComponentVersion": "IS-H 472", "SupportPackage": "SAPKIPHF31", "URL": "/supportpackage/SAPKIPHF31"}, {"SoftwareComponentVersion": "IS-H 600", "SupportPackage": "SAPK-60016INISH", "URL": "/supportpackage/SAPK-60016INISH"}, {"SoftwareComponentVersion": "IS-H 602", "SupportPackage": "SAPK-60205INISH", "URL": "/supportpackage/SAPK-60205INISH"}, {"SoftwareComponentVersion": "IS-H 603", "SupportPackage": "SAPK-60304INISH", "URL": "/supportpackage/SAPK-60304INISH"}, {"SoftwareComponentVersion": "IS-H 604", "SupportPackage": "SAPK-60402INISH", "URL": "/supportpackage/SAPK-60402INISH"}]}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 4, "URL": "/corrins/0001268987/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 34, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "472", "Number": "1013098 ", "URL": "/notes/1013098 ", "Title": "IS-H: NA30 - termination LOAD_PROGRAM_NOT_FOUND during ABC analysis", "Component": "IS-H-PA-BIL"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "954447 ", "URL": "/notes/954447 ", "Title": "IS-H AT: ELDA - Current Adjustments (P321, ELDAL)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "976665 ", "URL": "/notes/976665 ", "Title": "IS-H CH: Reversal/Rebilling of Pure SP Invoices", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "985366 ", "URL": "/notes/985366 ", "Title": "IS-H AT: Suppress RNWATKOSMA Error \\&quot;Missing Windows\\&quot;", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "993056 ", "URL": "/notes/993056 ", "Title": "ISH-AT: RNWATKOSMA IV Dunning in Batch", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1001093 ", "URL": "/notes/1001093 ", "Title": "IS-H AT: EDIVKA 5.0 - Corrections", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1008978 ", "URL": "/notes/1008978 ", "Title": "IS-H AT: EDIVKA 5.0 - Corrections December 2006", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1013707 ", "URL": "/notes/1013707 ", "Title": "IS-H AT: EDIVKA 5.0 - Corrections January 2007", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1015189 ", "URL": "/notes/1015189 ", "Title": "IS-H AT: EDIVKA 5.0 - Corrections January 2007 - Part 2", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1016889 ", "URL": "/notes/1016889 ", "Title": "IS-H AT: EDIVKA 5.0 - Corrections January 2007 - Part 3", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1026248 ", "URL": "/notes/1026248 ", "Title": "IS-H AT: Performance RNWATKUELIST RNWAT_EDI_LIST RNWATKOSMA", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1047116 ", "URL": "/notes/1047116 ", "Title": "IS-H AT: EDIVKA 5.0 - Corrections April 2007", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1051011 ", "URL": "/notes/1051011 ", "Title": "IS-H AT: EDI Message Dispatch with Comment", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1058098 ", "URL": "/notes/1058098 ", "Title": "IS-H AT: EDIVKA 5.0 - Enhancements 2007 Part 1", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1064060 ", "URL": "/notes/1064060 ", "Title": "IS-H AT: EDIVKA 5.0 - Enhancements 2007 Part 1a", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1073187 ", "URL": "/notes/1073187 ", "Title": "IS-H AT: EDIVKA 5.0 - Corrections July 2007", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1088126 ", "URL": "/notes/1088126 ", "Title": "IS-H AT: ELDA - Current Corrections at End of August 2007", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1091629 ", "URL": "/notes/1091629 ", "Title": "IS-H AT: EDIVKA Missing AUFN Notif., Self-Payer Cancellation...", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1094185 ", "URL": "/notes/1094185 ", "Title": "IS-H AT: EDIVKA - Insurance Verification from Companions", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1103522 ", "URL": "/notes/1103522 ", "Title": "IS-H AT: EDIVKA - Data Collection Point for Renewals", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1105504 ", "URL": "/notes/1105504 ", "Title": "IS-H AT: EDIVKA - Enhancement Package 2", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1119981 ", "URL": "/notes/1119981 ", "Title": "IS-H AT: EDIVKA - Procedure for Physician Contributions", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1120905 ", "URL": "/notes/1120905 ", "Title": "IS-H: NA30 - Unclear Display of Insurance Provider/Payer", "Component": "IS-H-PA-BIL"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1121410 ", "URL": "/notes/1121410 ", "Title": "IS-H AT: EDIVKA - Corrections December 2007", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1132159 ", "URL": "/notes/1132159 ", "Title": "IS-H AT: EDIVKA - Corrections January 2008", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1137711 ", "URL": "/notes/1137711 ", "Title": "IS-H AT: EDIVKA - Corrections February 2008", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1150701 ", "URL": "/notes/1150701 ", "Title": "IS-H AT: EDIVKA - Corr. Policy Number Check Counterparty", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1157433 ", "URL": "/notes/1157433 ", "Title": "IS-H AT: EDIVKA - Corrections April 2008", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1159210 ", "URL": "/notes/1159210 ", "Title": "IS-H AT: EDIVKA - Corrections June 2008", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1229251 ", "URL": "/notes/1229251 ", "Title": "IS-H AT: EDIVKA - Payment Advice Note Communication Private Insurance", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "954447 ", "URL": "/notes/954447 ", "Title": "IS-H AT: ELDA - Current Adjustments (P321, ELDAL)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "976665 ", "URL": "/notes/976665 ", "Title": "IS-H CH: Reversal/Rebilling of Pure SP Invoices", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "985366 ", "URL": "/notes/985366 ", "Title": "IS-H AT: Suppress RNWATKOSMA Error \\&quot;Missing Windows\\&quot;", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "993056 ", "URL": "/notes/993056 ", "Title": "ISH-AT: RNWATKOSMA IV Dunning in Batch", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1004893 ", "URL": "/notes/1004893 ", "Title": "IS-H CH: Partial Cancellation of SP Invoice", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1013098 ", "URL": "/notes/1013098 ", "Title": "IS-H: NA30 - termination LOAD_PROGRAM_NOT_FOUND during ABC analysis", "Component": "IS-H-PA-BIL"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1026248 ", "URL": "/notes/1026248 ", "Title": "IS-H AT: Performance RNWATKUELIST RNWAT_EDI_LIST RNWATKOSMA", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1047116 ", "URL": "/notes/1047116 ", "Title": "IS-H AT: EDIVKA 5.0 - Corrections April 2007", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1051011 ", "URL": "/notes/1051011 ", "Title": "IS-H AT: EDI Message Dispatch with Comment", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1058098 ", "URL": "/notes/1058098 ", "Title": "IS-H AT: EDIVKA 5.0 - Enhancements 2007 Part 1", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1064060 ", "URL": "/notes/1064060 ", "Title": "IS-H AT: EDIVKA 5.0 - Enhancements 2007 Part 1a", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1073187 ", "URL": "/notes/1073187 ", "Title": "IS-H AT: EDIVKA 5.0 - Corrections July 2007", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1088126 ", "URL": "/notes/1088126 ", "Title": "IS-H AT: ELDA - Current Corrections at End of August 2007", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1091629 ", "URL": "/notes/1091629 ", "Title": "IS-H AT: EDIVKA Missing AUFN Notif., Self-Payer Cancellation...", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1094185 ", "URL": "/notes/1094185 ", "Title": "IS-H AT: EDIVKA - Insurance Verification from Companions", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1103522 ", "URL": "/notes/1103522 ", "Title": "IS-H AT: EDIVKA - Data Collection Point for Renewals", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1105504 ", "URL": "/notes/1105504 ", "Title": "IS-H AT: EDIVKA - Enhancement Package 2", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1119981 ", "URL": "/notes/1119981 ", "Title": "IS-H AT: EDIVKA - Procedure for Physician Contributions", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1120905 ", "URL": "/notes/1120905 ", "Title": "IS-H: NA30 - Unclear Display of Insurance Provider/Payer", "Component": "IS-H-PA-BIL"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1121410 ", "URL": "/notes/1121410 ", "Title": "IS-H AT: EDIVKA - Corrections December 2007", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1122016 ", "URL": "/notes/1122016 ", "Title": "IS-H AT: EDIVKA - Connection MediDocu for i.s.h.med", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1132159 ", "URL": "/notes/1132159 ", "Title": "IS-H AT: EDIVKA - Corrections January 2008", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1137711 ", "URL": "/notes/1137711 ", "Title": "IS-H AT: EDIVKA - Corrections February 2008", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1149728 ", "URL": "/notes/1149728 ", "Title": "IS-H AT: Deadlocks in NRIV (EDIVKA)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1150701 ", "URL": "/notes/1150701 ", "Title": "IS-H AT: EDIVKA - Corr. Policy Number Check Counterparty", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1157433 ", "URL": "/notes/1157433 ", "Title": "IS-H AT: EDIVKA - Corrections April 2008", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1159210 ", "URL": "/notes/1159210 ", "Title": "IS-H AT: EDIVKA - Corrections June 2008", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1229251 ", "URL": "/notes/1229251 ", "Title": "IS-H AT: EDIVKA - Payment Advice Note Communication Private Insurance", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1120905 ", "URL": "/notes/1120905 ", "Title": "IS-H: NA30 - Unclear Display of Insurance Provider/Payer", "Component": "IS-H-PA-BIL"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1137711 ", "URL": "/notes/1137711 ", "Title": "IS-H AT: EDIVKA - Corrections February 2008", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1149728 ", "URL": "/notes/1149728 ", "Title": "IS-H AT: Deadlocks in NRIV (EDIVKA)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1150701 ", "URL": "/notes/1150701 ", "Title": "IS-H AT: EDIVKA - Corr. Policy Number Check Counterparty", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1157433 ", "URL": "/notes/1157433 ", "Title": "IS-H AT: EDIVKA - Corrections April 2008", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1159210 ", "URL": "/notes/1159210 ", "Title": "IS-H AT: EDIVKA - Corrections June 2008", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1229251 ", "URL": "/notes/1229251 ", "Title": "IS-H AT: EDIVKA - Payment Advice Note Communication Private Insurance", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1247164 ", "URL": "/notes/1247164 ", "Title": "IS-H AT: GINA - New Features", "Component": "XX-CSC-AT-IS-H"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1268987&TargetLanguage=EN&Component=XX-CSC-AT-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/1268987/D\" target=\"_blank\">/notes/1268987/D</a>."}}}}