{"Request": {"Number": "98586", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 442, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000373752017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000098586?language=E&token=0E173A69ECD78C02E6D8873B70DFA71D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000098586", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000098586/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "98586"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 38}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "07.05.2000"}, "SAPComponentKey": {"_label": "Component", "value": "MM-PUR-VM-SET"}, "SAPComponentKeyText": {"_label": "Component", "value": "Subsequent Settlement"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Purchasing", "value": "MM-PUR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Vendor-Material Relationships and Conditions", "value": "MM-PUR-VM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Subsequent Settlement", "value": "MM-PUR-VM-SET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM-SET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "98586 - Analysis reports, repair reports, Release 3.0"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note contains additional information on problem analysis and problem elimination (subsequent settlement purchasing).</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Subsequent settlement (volume-based rebate) in purchasing</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Owing to program errors, an analysis of the arrangements to check for possible errors in updating business volume or a checking of the settled incomes may be required.<br />This note contains reports which at present are not contained in the delivery but which are useful for problem analysis:</p> <UL><LI>ZANASTAT: Comparison of the business volume data from the detailed statement with business volume data in the statistics (only for Releases 3.0 to 3.1I)</LI></UL> <UL><LI>ZINCOME: Comparison of the scale and condition basis with the incomes for condition records that have already been settled (only for Releases 3.0 to 3.1I)</LI></UL> <UL><LI>ZDELSTAT: Deletion of the business volume data for an arrangement (only in Releases 3.0 to 3.1I)</LI></UL> <UL><LI>ZCHKEKBO: Comparison of the purchase order history (invoices) with the updated invoices (consistency check) (only in Releases 3.0F, 3.1H, and 3.1I). Note that the version for Release 3.0F is different from the version for Release 3.1H and 3.1I.</LI></UL> <p><br />Note the date details in the individual reports (see the source code header). The reports are enhanced and updated as the need arises.<br />Not all reports are available for all releases. The notes with the reports for the other releases - if they are available - can be found under 'Related notes'.<br />Due to changes in the functions, a separate version of the reports is generally necessary for every R/3 version.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>With report ZANASTAT (latest version dated October 26th, 1998), you can analyze your arrangements to check for variances in the business volumes update.<br />The report performs a detailed statement, that is, it determines all business volume data from the documents and compares them with the statistical data. Arrangements in which variances occur are logged. Note that the report has a very long runtime since all documents for the arrangement are processed.<br />You can select arrangements on the basis of the usual criteria, in particular by status of the arrangement, for example to exclude arrangements that have already been settled. With a parameter you can define whether deleted condition records should be checked or not (default). For arrangements requiring periodic settlement, you can also exclude periods already settled from the check (Performance, check all period condition records as default). With parameter S_DETAIL, you can display a detail log (\"expert mode\").<br />With functions Detailed statement and Statement of statistical data you can selectively analyze the variances for these arrangements and, if necessary, recompile the business volume data and incomes.<br />Refer to Note 73214 which includes a description of the procedure for setting up statistical data.<br />For this, deletion of all data for the arrangement in the Logistics Information System is necessary. As of Release 4.0B, this can be performed automatically with the reports for setup. In Release 4.0B, Note 128279 (contained in Hot Package 10) must be implemented. In Release 3.0 use attached report ZDELSTAT. You need the authorization for executing a setup of statistical data in Purchasing. The report deletes data in the information structures S074 and S015 not, however, in additional user-defined structures.<br /><br />With report ZINCOME, you can analyze agreements for variances between scale/condition basis and income, that is if the business volume data and incomes correspond.<br />You can select the arrangements by agreement number as well as the arrangement period.<br />The report edits only settled condition records which have not been deleted. If the report determines a variance and indicator S_CHECK (check run) is deleted, the status of the condition record is reset to active so that further settlement of the condition record is possible. As a result, the settlement already executed is converted into an interim settlement. When the settlement is performed again, the income is redetermined on the basis of the current business volume data. Already settled incomes from previous settlements are automatically offset.<br />An interim settlement is a settlement in which the status of the settled condition records remains active so that a further settlement is possible (advance settlement during the lifetime of a condition record).<br />Special features:</p> <UL><LI>So that a condition record can be checked, all settlement documents must be released for accounting.</LI></UL> <UL><LI>The report does not check the income data in the statistics against the incomes in the settlement documents (correct income update). Any variance reported may be caused by a variance at this point. You would have to check this separately.</LI></UL> <UL><LI>In the case of fully settled periodic arrangements, the system only checks the main condition against the total business volume. This is sufficient.</LI></UL> <p><br />The list output consists of three parts. In part 1, any messages that may have been issued are displayed:</p> <UL><LI>All condition records with variance (only if parameter S_DETAIL is set, larger list).</LI></UL> <UL><LI>All arrangements including settlement documents for which release is missing (only if parameter S_DETAIL is set, larger list).</LI></UL> <UL><LI>Other errors (none should generally occur).</LI></UL> <p><br />In part 2, all arrangements which could not be checked are listed (settlement documents that are not released).<br />In part 3, all arrangements are listed, for which a variance has been determined in at least one condition record. The settlement date on which the arrangement is been settled again is displayed (latest valid-to date). However, the missing settlement is performed automatically with the next settlement run.<br /><br />With report ZCHKEKBO, you can perform a comparison between the purchase order history (invoices) and the executed updates. The report determines all invoices for which the business volume data has not been updated. All purchase order items listed in the detailed statement are processed. Purchase order items with missing subsequent updates can therefore not be examined. Their registration for subsequent settlement is only performed when an update is executed (refer also to Note 73214).<br />Such a check is useful, for example, if all documents are not entered when an arrangement is settled, that is if follow-on documents ( invoices) are entered after the arrangement is settled. The update can then no longer be performed.<br />This check is also performed as part of the detailed statement, however, here it cannot be performed separately without determination of the update data. In addition, you have the option of displaying the business volume data for the missing invoices (parameter S_FODATA = 'X'). Implement Note 117988 - if you have not done so already.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023678)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000098586/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000098586/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000098586/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000098586/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000098586/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000098586/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000098586/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000098586/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000098586/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "40147", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 3.0", "RefUrl": "/notes/40147"}, {"RefNumber": "301595", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Analysis/repair reports, Release 4.6", "RefUrl": "/notes/301595"}, {"RefNumber": "301592", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Analysis/Repair reports, Rel. 4.5B", "RefUrl": "/notes/301592"}, {"RefNumber": "168242", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Analysis reports, repair reports, Release 4.0B", "RefUrl": "/notes/168242"}, {"RefNumber": "115307", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Updating business volumes for settled condition records", "RefUrl": "/notes/115307"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "40147", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 3.0", "RefUrl": "/notes/40147 "}, {"RefNumber": "301595", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Analysis/repair reports, Release 4.6", "RefUrl": "/notes/301595 "}, {"RefNumber": "301592", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Analysis/Repair reports, Rel. 4.5B", "RefUrl": "/notes/301592 "}, {"RefNumber": "115307", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Updating business volumes for settled condition records", "RefUrl": "/notes/115307 "}, {"RefNumber": "168242", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Analysis reports, repair reports, Release 4.0B", "RefUrl": "/notes/168242 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "300", "To": "31I", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 4, "URL": "/corrins/0000098586/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}