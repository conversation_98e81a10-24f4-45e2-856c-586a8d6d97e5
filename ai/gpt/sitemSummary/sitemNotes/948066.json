{"Request": {"Number": "948066", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 330, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016100672017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000948066?language=E&token=4A13B56850E3DD41DD75234882E1DFF5"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000948066", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000948066/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "948066"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Performance"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "19.05.2022"}, "SAPComponentKey": {"_label": "Component", "value": "SV-PERF"}, "SAPComponentKeyText": {"_label": "Component", "value": "Performance Problems"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Performance Problems", "value": "SV-PERF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-PERF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "948066 - Performance Analysis: Transactions to use"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>An analysis of a system is required (BI, SRM, ERP, CRM etc)</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>DBACOCKPIT, ST22, SM21, ST02, ST04, ST06N, ST03, Database, Performance, Transactions</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You require to know what transactions are available to analyze general performance problems in the system. You need a breakdown of possible transactions and how they may help in gathering sufficient data for futher analysis. The transactions may look different depending on SAP release, Operating System and Database type.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The following is a list of the most important performance analysis transactions and brief explanation of their purpose. ST02 SAP Memory Configuration monitor checks the SAP Buffers and SAP Memory areas for problems such as swapping.</p>\r\n<p>It is a snapshot about the utilization of SAP shared buffers. High water marks of utilization for example extended, roll, paging and heap memory can be obtained from the SAP memory configuration monitor ST03N: General performance statistics such as response time. This workload monitor can show response times daily/weekly/monthly and is the primary tool for checking dialog/background/update/spool/rfc response times.</p>\r\n<p><br />ST04 or DBACOCKPIT: A snapshot of databuffer/cache quality, SQL cache catalog/pin ratio. For further detailed analysis click button 'Detail Analysis Menu'</p>\r\n<p>ST06/ST06N/OS07: The Operating System Monitor is used as a snapshot of CPU utilization, RAM, Swap space. For further detailed analysis click button 'Detail Analysis Menu'.</p>\r\n<p><br />SM66: The main tool used for monitoring current system activity via the Global Work Process Overview</p>\r\n<p>ST22: Store for ABAP Dumps such as - For example the following would be critical dumps beginning with TSV_TNEW -DBIF_RSQL_SQL_ERROR - DBIF_RSQL_INVALID_SQL_ERROR - SYSTEM_NO_ROLL - SYSTEM_ROLL_IN_ERROR - TABLE_HASH_NO_MEMORY - STORAGE_HASH_NO_MEMORY - STORAGE_PARAMETERS_WRONG_SET - SAPSQL_ARRAY_INSERT_DUPREC</p>\r\n<p>SM21: Typical messages like : - Printer problems - Signal 11 - Short dumps - Time-outs - Matchcode problems - Aborted postings - ORA errors</p>\r\n<p>SM04: Active Users and number of modes of same. Also check memory, page, roll, priv usage via goto-&gt;memory</p>\r\n<p><br />AL08: The number of active users can be obtained from the Global User Monitor SARFC: An overview of server resources and under column description whether there is a problem</p>\r\n<p>AL11: List of SAP directories</p>\r\n<p>DB01: Is a snapshot about exclusive wait situations using the Database Lock Monitor<br />DB02: Check database growth, freespace in tablespaces, critical objects, extents of tables and indexes<br />DB12: Check backup/restore situation<br />DB16: On Oracle check for search for messages with the \"SEVERITY\" type \"E\" (error) that occurred in the last 4 weeks</p>\r\n<p>DB24: Check administration tasks such as backup/recovery, archive frequency, administration tasks Report<br />/SDF/MON: The tool allows for the collection of data on CPU Utilization ,Memory Management, Database Performance, WorkProcess Utilization, Workload, STAD, RFC etc. The information linked with each o f the above areas is stored in the database for further usage. The data can be collected for predefined periods of time and for a set frequency of seconds STAD: Used to check response time of programs/transactions and provides various levels of detail which can be tailored.<br />ST14: The ST14 application monitor is mainly used during SAP GoingLive session. Analysis batch jobs collect performance-relevant key figures such as document statistics and customizing settings, the analysis results can be viewed as a tree and downloaded to a service session. See SAP Note 69455. ST05: ST05 traces every action of a user on a server. SQL trace needs to be switched off and the<br />ST05 writes trace files into the local filesystem and overwrites them circularily.</p>\r\n<p><br />ST12 combines ABAP and performance (SQL) trace into one transaction, with major functional enhancements especially for the ABAP trace part. In a joint switch on/off with the performance trace, ST12 allows to activate the ABAP trace for another user. See Note 755977. ST12 can also be used for tracing workprocess, program/transaction or a user. At a click of a button you can view the SQL and ABAP trace information.</p>\r\n<p>Other useful transactions that can be used depending on circumstances: SM58 SM59,SMQ1 SMQ2 SMGW SDCCN SM37 SM51 SM50 RZ04 RZ10 RZ20 SE16 SE12 TU02 DB03 SMQS DB21 SMQR SM13:</p>\r\n<p>List of useful notes &amp; KBA's that may help</p>\r\n<p>146289&#160;&#160;&#160; Parameter Recommendations for 64-Bit SAP Kernel&#160;<br />825653&#160;&#160;&#160;&#160;Oracle: Common Errors<br />806554&#160;&#160;&#160;&#160;FAQ: I/O-intensive database operations<br />805934&#160;&#160;&#160;&#160;FAQ: Database time<br />797629&#160;&#160;&#160;&#160;FAQ: Oracle histograms<br />793113&#160;&#160;&#160;&#160;FAQ: Oracle I/O configuration<br />386605&#160;&#160;&#160;&#160;SAP Memory Management for Linux<br />724713&#160;&#160;&#160;&#160;Parameter settings for Solaris 10<br />195471&#160;&#160;&#160;&#160;SAP Banking performance<br />192658&#160;&#160;&#160;&#160;Setting basis parameters for BW systems<br />146528&#160;&#160;&#160;&#160;Configuration of R/3 on large RAM<br />127715&#160;&#160;&#160;&#160;CBO: Optimum parameters for performance<br />123366&#160;&#160;&#160;&#160;Maximum addressable memory for the Oracle SGA</p>\r\n<p>2000000&#160; FAQ: SAP HANA Performance Optimization</p>\r\n<p>2586666 SAP HANA Database: Slow System-wide Performance</p>\r\n<p><br />172747 &#160;&#160;&#160; HP-UX Operating System kernel parameter recommendations<br />1077887&#160;&#160; SAP on HP-UX:mount &amp; filesystem options for best performance<br />1075118&#160;&#160; SAP on HP-UX: FAQ</p>\r\n<p>941735&#160;&#160;&#160;&#160; LINUX parameters</p>\r\n<p>1048686&#160;&#160; Recommended AIX settings for SAP<br />143646&#160;&#160;&#160;&#160; AIX patches for new memory management<br />856848&#160;&#160;&#160;&#160; AIX extended memory disclaiming and note<br />973227&#160;&#160;&#160;&#160; AIX Virtural memory management parameter.<br />323816&#160;&#160;&#160;&#160; AIX user limits<br />78498&#160;&#160;&#160;&#160;&#160;&#160; AIX paging rates<br />789477&#160;&#160;&#160;&#160; Large extended memory on AIX (64-bit) as of Kernel<br />912425&#160;&#160;&#160;&#160; AIX: Unexplained high memory consumption<br />1121904&#160;&#160; SAP on AIX: Recommendations for paging space</p>\r\n<p>1085937 Wait Event Analysis For SQL Server<br />111291&#160;&#160; FAQ: SQL server analysis and avoiding deadlocks<br />1152848 FAQ: SQL Server Wait Events<br />1237682 Configuration Parameters for SQL Server 2008<br />555223&#160;&#160; FAQ: Microsoft SQL Server<br />62988&#160;&#160;&#160;&#160; Service packs for Microsoft SQL Server<br />806342&#160;&#160; FAQ: Analyzing exclusive database locks on SQL Server</p>\r\n<p>846890&#160;FAQ: MaxDB Administration<br />912905&#160;FAQ: storage systems used with MaxDB<br />822239&#160;FAQ: MaxDB Interfaces<br />832544&#160;FAQ: MaxDB Hints<br />819324&#160;FAQ: MaxDB SQL optimization<br />952783&#160;FAQ: MaxDB high availability<br />928037&#160;FAQ: MaxDB Indexes</p>\r\n<p>1692571&#160;14&#160;48&#160;DB6: DB2 10.1 Standard Parameter Settings</p>\r\n<p>899322&#160;&#160; DB6: DB2 V9.1 Standard Parameter Settings<br />1086130&#160;DB6: DB2 V9.5 Standard Parameter Settings<br />1329179 DB6: DB2 V9.7 Standard Parameter Settings<br />101809&#160;&#160; DB6: Supported Fix Packs IBM DB2 for Linux, UNIX and Windows</p>\r\n<p>In the event that you cannot get a satisfactory performance for a transaction/program try searching for notes using the keywords performance and the name of the transaction causing problems to see if anything exists to help.</p>\r\n<p>You can also open a message on SV-BO component. Ensure connection to system is open and that user has authorization for transactions [see point 5 in note 160777]. If issue is reproducible include a full example with screenshots if necessary.</p>\r\n<p>Discuss with your developers/programmers if performance improvements are possible. For performance problems caused by customer programs SAP offers SAP Customer Program Optimization (http://service.sap.com/cpo) by eliminating costly performance bottlenecks by tuning critical customer programs. Ensure you have setup Earlywatch Alert as per SAP Note 207223.</p>\r\n<p>A good starting point for performance documentation is: http://service.sap.com/performance http://service.sap.com/earlywatch</p>\r\n<p>http://service.sap.com/goinglivecheck</p>\r\n<p>http://service.sap.com/safeguarding<br />HANA:<br />http://service.sap.com/hana</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I008787)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I331270)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000948066/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000948066/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000948066/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000948066/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000948066/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000948066/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000948066/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000948066/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000948066/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2000000", "RefComponent": "HAN-DB-PERF", "RefTitle": "FAQ: SAP HANA Performance Optimization", "RefUrl": "/notes/2000000"}, {"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488"}, {"RefNumber": "618868", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle performance", "RefUrl": "/notes/618868"}, {"RefNumber": "1266771", "RefComponent": "SRM-EBP-TEC-PFM", "RefTitle": "SRM 7.0 Performance Guide", "RefUrl": "/notes/1266771"}, {"RefNumber": "1169088", "RefComponent": "SRM-EBP-TEC-PFM", "RefTitle": "SRM 6.0 Performance Guide", "RefUrl": "/notes/1169088"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2676688", "RefComponent": "SV-PERF", "RefTitle": "Frequently-Used Performance Analysis transactions and Tools", "RefUrl": "/notes/2676688 "}, {"RefNumber": "2383809", "RefComponent": "SV-PERF", "RefTitle": "How to configure /SDF/MON for performance monitoring and analysis", "RefUrl": "/notes/2383809 "}, {"RefNumber": "618868", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle performance", "RefUrl": "/notes/618868 "}, {"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488 "}, {"RefNumber": "1169088", "RefComponent": "SRM-EBP-TEC-PFM", "RefTitle": "SRM 6.0 Performance Guide", "RefUrl": "/notes/1169088 "}, {"RefNumber": "1266771", "RefComponent": "SRM-EBP-TEC-PFM", "RefTitle": "SRM 7.0 Performance Guide", "RefUrl": "/notes/1266771 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}