{"Request": {"Number": "2489544", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 319, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000019011582017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002489544?language=E&token=C2E4470C9DCDC6812CB9EDA44E2AA3BC"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002489544", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002489544/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2489544"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "21.06.2017"}, "SAPComponentKey": {"_label": "Component", "value": "IS-OIL-DS-OGSD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oil&Gas Secondary Distribution"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oil", "value": "IS-OIL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-OIL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Downstream", "value": "IS-OIL-DS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-OIL-DS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oil&Gas Secondary Distribution", "value": "IS-OIL-DS-OGSD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-OIL-DS-OGSD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2489544 - S4TWL - OGSD - Classic Interfaces"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are converting a system with installed Add-on OGSD to SAP S/4HANA.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Simplification Items, System Conversion to SAP S/4HANA, Transactions /ICO/MO_IF, /ICO/MO_IA, /ICO/MO_IB, /ICO/MO_I9, ICO/MO_IH, ICO/MO_IM, ICO/MO_FD, ICO/MO_FU</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You are using the OGSD application \"Classic Interfaces\"&#160;to process (mostly inbound) IDocs using VOFM-style customer enhancements via form routines.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Description:</strong></p>\r\n<p>The old or \"Classic Interfaces\"&#160;incorporates the processes for the import and processing of (mostly) inbound IDocs in order to trigger further activities resulting from the purchase and procurement of petroleum products and the delivery of these products to customers by making use only of the Classic Data Collation (see SAP-note&#160;2477777). This means, it processes IDocs coming via ALE, transforms IDoc data or creates IDocs from flat files. The interfaces are used for checking purposes and also for adding data to incoming data.</p>\r\n<p><strong>Business related information:</strong></p>\r\n<p>The Classic Interfaces allow for importing data in the form of flat files or IDocs received by ALE and pass on these data to the Classic Data Colation (see SAP-Note 2477777) which then integrates and automates frequently used processes mainly in Materials Management (MM) and Sales and Distribution (SD).<br />It therefore represents a needed first step to allow for the direct connection of MM and SD processes, such as procuring materials, creating and changing sales contracts, sales orders, and deliveries.</p>\r\n<p>For further information about the old or \"Classic Data Collation\" &#160;see:</p>\r\n<p><a target=\"_blank\" href=\"https://help.sap.com/viewer/ac2edcbb31de46e48bb3986017667d9a/7.0.6/en-US/16c7aa533dae0077e10000000a4450e5.html\">https://help.sap.com/viewer/ac2edcbb31de46e48bb3986017667d9a/7.0.6/en-US/16c7aa533dae0077e10000000a4450e5.html</a></p>\r\n<p>For further information about the NEW Interfaces see:</p>\r\n<p><a target=\"_blank\" href=\"https://help.sap.com/viewer/ac2edcbb31de46e48bb3986017667d9a/7.0.6/en-US/75320f54366b2e18e10000000a174cb4.html\">https://help.sap.com/viewer/ac2edcbb31de46e48bb3986017667d9a/7.0.6/en-US/75320f54366b2e18e10000000a174cb4.html</a></p>\r\n<p>There is no information about the Classic Interfaces&#160;online anymore, but the listed steps are quite similar to the New Interfaces, except that wher \"methods\" are stated, the Classic Interfaces are using form routines at special events during the process.</p>\r\n<p><strong>Required and recommended action(s):</strong></p>\r\n<ol>\r\n<li>If you want to continue using the Interfaces, you need to switch to the New Interfaces, which is using a method framework instead of form routines and has many more new features . In this case you have to check, if you have created some own functionality inside or for use by the now called \"Classic Interfaces\". If such own functionalities exists, you need to manually migrate your functionalities into the&#160;New Interfaces or adapt them in order to make use of them in the future too.</li>\r\n<li>You need to consider this as a project, as there are no migration tools which may assist you in transfering code of form-routines into class-methods. All adaptations need to be executed manually.</li>\r\n<li>You do not need to migrate OGSD-own funtionalities of the Classic Interfaces, these have been migrated into the succcessor application \"New Interfaces\".</li>\r\n<li>You need to organize knowledge transfer to all users working with Classic Interfaces as their pattern of work will change when working with the&#160;\"New Interfaces\". Users will have to use new applications for creating, changing, displaying &#160;and processing interface data.</li>\r\n<li>An introduction to the New Interfaces can be found in a course available on openSAP:&#160;<br />https://open.sap.com/courses/ogsd1<br />Week 3 Units 4 - 5 deal with the New Interfaces.</li>\r\n</ol>\r\n<p>&#160;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5036248)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (C5057779)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002489544/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002489544/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002489544/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002489544/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002489544/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002489544/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002489544/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002489544/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002489544/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}