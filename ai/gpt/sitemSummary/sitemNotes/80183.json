{"Request": {"Number": "80183", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 451, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000260022017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000080183?language=E&token=3FEC08A3793C7784D1DDB762FB421D9D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000080183", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000080183/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "80183"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 43}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.02.2023"}, "SAPComponentKey": {"_label": "Component", "value": "SD-BF-PR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Pricing"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Sales and Distribution", "value": "SD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "SD-BF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD-BF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Pricing", "value": "SD-BF-PR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD-BF-PR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "80183 - Rounding"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>During the determination of the net price of a document item, the following symptoms can occur:</p>\r\n<p>Symptom 1: The value of 'net price * quantity' can differ from the net value displayed if the net value results from a basic price plus/minus surcharges or discounts.</p>\r\n<p>Symptom 2: The net price is sometimes rounded differently for different quantities.<br /><br /></p>\r\n<p><strong>Example</strong></p>\r\n<p>A discount of 9% is granted for an article with a price of EUR 135.50 per piece. The discount for a single piece is therefore EUR 12.19(5) if calculated precisely, and the precise net price is EUR 123.30(5).</p>\r\n<p>The standard pricing returns the following results:</p>\r\n<p>Case A: Standard pricing result for 3 pieces:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"1\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>PR00</td>\r\n<td>Price</td>\r\n<td>135.50</td>\r\n<td>EUR per pc</td>\r\n<td>406.50</td>\r\n<td>EUR</td>\r\n</tr>\r\n<tr>\r\n<td>RA00</td>\r\n<td>Discount</td>\r\n<td>-9.00</td>\r\n<td>%</td>\r\n<td>-36.59</td>\r\n<td>EUR</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>Net value</td>\r\n<td>123.30</td>\r\n<td>EUR per pc</td>\r\n<td>369.91</td>\r\n<td>EUR &#x2190; Symptom 1: 3 * EUR 123.30 = EUR 369.90 &#x2260; EUR 369.91</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Case B: Default pricing result for 10 pieces:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"1\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>PR00</td>\r\n<td>Price</td>\r\n<td>135.50</td>\r\n<td>EUR per pc</td>\r\n<td>1,355.00</td>\r\n<td>EUR</td>\r\n</tr>\r\n<tr>\r\n<td>RA00</td>\r\n<td>Discount</td>\r\n<td>-9.00</td>\r\n<td>%</td>\r\n<td>-121.95</td>\r\n<td>EUR</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>Net value</td>\r\n<td>123.31</td>\r\n<td>EUR per pc</td>\r\n<td>1,233.05</td>\r\n<td>\r\n<p>EUR &#x2190; Symptom 1: 10 * EUR 123.31 = EUR 1,233.10 &#x2260; EUR 1,233.05</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>In both cases A and B, symptom 1 occurs, whereas symptom 2 is visible only if you compare case A (net price of EUR 123.30 per piece) with case B (net price of EUR 123.31 per piece).</p>\r\n<p>&#x00A0;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Rounding error</p>\r\n<p>&#x00A0;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Standard logic for pricing<br />Surcharges and discounts are based on the net value. The resulting net price is determined by dividing the net value by the quantity (see SAP Note 201830).</p>\r\n<p>&#x00A0;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Please note that the solution described here is a workaround that is not included in the standard system. Inquiries and problem messages concerning this subject are not covered by Support but are processed by Consulting, which is subject to a separate fee. See SAP Note 381348 for more information.</strong></p>\r\n<p>This SAP Note replaces SAP Notes 19454 (corresponds to variants 1 and 2 described below) and 38389 (corresponds to variant 3 described below) in Release 3.0A and subsequent releases.<br /><br /></p>\r\n<p><strong>Restrictions<br /><br /></strong>1. Due to the characteristics of absolute amounts (calculation rule 'B'), it is generally necessary to derive the net price from the net value. Therefore, the net value is a derived value and is always quantity-dependent by design. This means that the net price cannot be developed independently of the net value. There is no parallel calculation at amount and value level on the basis of which rounding differences could be displayed.<br /><br />Rounding differences can occur in this case only if you force the relation \"price * quantity = value\" at the net value level. Only variant 1 is able to handle absolute amount conditions in this sense. Due to their approach to absolute amount conditions, variants 2 and 3 principally cannot offer a solution, and the corresponding rounding formulas therefore lead to a pricing error.<br /><br />2. For the lines in the pricing procedure after the price condition and before the rounding condition, problems may occur if conditions exist that could receive a surcharge from the rounding differences clearing as part of the group condition processing. These scenarios have not been tested, and this consulting note cannot guarantee that they will work. Any modifications or enhancements that may be required in this context fall into the area of consulting. We therefore recommend that you suppress the rounding difference adjustment via a group key routine (see SAP Note 39034), if necessary.<br /><br />General reason: The rounding difference clearing is carried out with the pricing type \"F\". If a condition in this pricing mode is then updated, the corresponding condition base and condition value formulas are no longer executed in change mode; so all changes in the work area XKOMV or the variable XKWERT are rejected. This affects the variables XKOMV-KKURS, XKOMV-KINAK, and XKWERT in the value formulas 19, 20, 919, and 920.<br /><br />However, the following statements can be made for the individual variants:<br /><br />a) Variant 1 is not critical with regard to rounding difference adjustment for the discount conditions because the discount conditions do not contain any formulas. However, the discount amount in the item containing the rounding difference determined in the rounding difference adjustment is then no longer correct.<br /><br />b) Variant 2 will probably still work despite the formulas if a rounding difference clearing is carried out for the discount conditions. XKOMV-KKURS and XKOMV-KINAK should already be set correctly in item pricing. As a result, it should no longer be necessary to set them again within group condition processing. A surcharge for the discount condition due to a rounding difference adjustment should be caught again as a result of the rounding condition NETP, and the relationship 'amount * quantity = value' should still apply.<br /><br />c) In particular for variant 3, the relationship 'amount * quantity = value' no longer applies in the case of a surcharge for a discount condition due to a rounding difference adjustment. The surcharge immediately affects the condition value of the net price condition PNTP.<br /><br />3. In all the above solutions, the rebate amount is always rounded commercially and not the net price resulting from the calculation. Take the calculations specified under variant 3 as an example.<br /><br /></p>\r\n<p><strong>Procedure described in detail</strong></p>\r\n<p>1. Use transaction V/06 to create the condition type NETP (rounding difference) (NETP is not required for variant 3):</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"1\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Condition class</td>\r\n<td>'A'</td>\r\n<td>Discount or surcharge</td>\r\n</tr>\r\n<tr>\r\n<td>Calculation type</td>\r\n<td>'C'</td>\r\n<td>Quantity</td>\r\n</tr>\r\n<tr>\r\n<td>Condition type</td>\r\n<td>'L'</td>\r\n<td>Generally new when copying</td>\r\n</tr>\r\n<tr>\r\n<td>Manual entries</td>\r\n<td>'D'</td>\r\n<td>Not possible to process manually</td>\r\n</tr>\r\n<tr>\r\n<td>Item condition</td>\r\n<td>'X'</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>No access sequence is defined for the condition type 'NETP'.<br /><br /></p>\r\n<p>2. Use transaction V/06 to create the condition type 'PNTP' (net price):</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"1\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Condition class</td>\r\n<td>'B'</td>\r\n<td>Prices</td>\r\n</tr>\r\n<tr>\r\n<td>Calculation type</td>\r\n<td>'C'</td>\r\n<td>Quantity</td>\r\n</tr>\r\n<tr>\r\n<td>Condition type</td>\r\n<td>'L'</td>\r\n<td>Generally new when copying</td>\r\n</tr>\r\n<tr>\r\n<td>Manual entries</td>\r\n<td>'D'</td>\r\n<td>Not possible to process manually</td>\r\n</tr>\r\n<tr>\r\n<td>Item condition</td>\r\n<td>'X'</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>No access sequence is defined for the condition type 'PNTP'.</p>\r\n<p><br />3. Use transaction VOFM to create the formulas from the attached correction instructions in the customer namespace (base formula 917 and value formulas 906, 919, and 920). <br />You only need base formula 917 and value formula 919 or 920 for variant 3. <br />If you want to use only value formula 920 but not 919, you should still create value formula 919 in full because it contains a data definition that is required for formula 920.<br /><br />4. Change your pricing procedure in accordance with one of the following three variants:<br /><br /></p>\r\n<p><strong><em>Pricing procedure variant 1:</em></strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"1\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>CType</td>\r\n<td>Description</td>\r\n<td>Req.</td>\r\n<td>AltCTy</td>\r\n<td>AltCBV</td>\r\n<td>Stat</td>\r\n<td>Print</td>\r\n</tr>\r\n<tr>\r\n<td>PR00</td>\r\n<td>Price</td>\r\n<td>&#x00A0;2</td>\r\n<td>&nbsp;</td>\r\n<td>&#x00A0;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>RA00</td>\r\n<td>Discount</td>\r\n<td>&#x00A0;2</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>NETP</td>\r\n<td>Rounding diff.</td>\r\n<td>&#x00A0;2</td>\r\n<td>6</td>\r\n<td>&#x00A0;3</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>PNTP</td>\r\n<td>Net price</td>\r\n<td>&#x00A0;2</td>\r\n<td>&#x00A0;906</td>\r\n<td>&#x00A0;3</td>\r\n<td>\r\n<p>&#x00A0;X</p>\r\n</td>\r\n<td>&#x00A0;X</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>Net value</td>\r\n<td>&#x00A0;2</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>...</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Variant 1 has the following properties:</p>\r\n<ul>\r\n<li>The relationship 'net value = net price * quantity' applies. This corrects symptom 1.</li>\r\n</ul>\r\n<ul>\r\n<li>The net price is not necessarily constant. This means that different net prices can result for different quantities.</li>\r\n</ul>\r\n<ul>\r\n<li>Discounts are calculated correctly.</li>\r\n</ul>\r\n<p>This variant therefore ensures that the relationship 'Net price * quantity = net value' is always fulfilled. However, the net price can still be different due to rounding effects.</p>\r\n<p>Any number of discount conditions of the same kind can be before the NETP condition.</p>\r\n<p><em><br />Mode of operation of variant 1:</em><br /><br />Case A: Pricing result for 3 pcs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"1\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>PR00</td>\r\n<td>Price</td>\r\n<td>135.50</td>\r\n<td>EUR per pc</td>\r\n<td>406.50</td>\r\n<td>EUR</td>\r\n</tr>\r\n<tr>\r\n<td>RA00</td>\r\n<td>Discount</td>\r\n<td>-9.00</td>\r\n<td>%</td>\r\n<td>-36.59</td>\r\n<td>EUR</td>\r\n</tr>\r\n<tr>\r\n<td>NETP</td>\r\n<td>Rounding diff.</td>\r\n<td>123.30</td>\r\n<td>EUR per pc</td>\r\n<td>-0.01</td>\r\n<td>EUR</td>\r\n</tr>\r\n<tr>\r\n<td>PNTP</td>\r\n<td>Net price</td>\r\n<td>123.30</td>\r\n<td>EUR per pc</td>\r\n<td>369.90</td>\r\n<td>EUR</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>Net value</td>\r\n<td>123.30</td>\r\n<td>EUR per pc</td>\r\n<td>369.90</td>\r\n<td>EUR</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>A 9% discount is applied to the condition value of EUR 406.50. When rounded, the discount comes to EUR 36.59. The result is a current net value of KOMP-NETWR = EUR 369.91. For the condition type NETP, base formula 3 uses the resulting current net price of KOMP-NETPR&#x00A0;= EUR 123.30. This means that the condition value XKOMV-KWERT becomes EUR 369.90 for this condition type. Condition value formula 6 now calculates a rounding difference of EUR 0.01 from this, which is again subtracted from the current net value. The rounding difference (= condition type NETP) works like a normal surcharge or discount. The resulting net value is thus corrected by it so that the relationship 'net price * quantity = net value' is fulfilled.<br /><br />If you were now to add a subtotal line item immediately after the condition type NETP to display the net value, a different net price might arise for the following reason (again relating to rounding). Why? <br /><br />The net price has 2 decimal places, and the quantity has 3 decimal places; the net value, therefore, theoretically has six decimal places. Because the net value uses only two decimal places, however, it has to be rounded. In contrast with normal condition type lines, subtotal line items calculate the net price backwards from the net value (see Section 2 of SAP Note 791944). <br />With a hypothetical quantity of 1,234 pieces in the above example, this would give rise to the following:<br /><br /> EUR 1,221.60 / 1,234 pcs = EUR 989,95(1377...)  </p>\r\n<p>On the otherhand, if you print a net price/ net value line item, the following applies:<br /><br /> EUR 989.95 * 1.234 St = EUR 1,221.60&#x00A0;(to be precise: EUR 1,221.5983)<br /><br />To definitively rule out any possible deviations here, the dummy condition type PNTP is being introduced to bypass the calculation logic of the subtotal line items. It is primarily used to provide a consistent net price/net value line item for printing the document. With base formula 3, a condition amount is again calculated from the condition value here. Due to the standard pricing logic, this gives the net value if multiplied by the quantity. Value formula 906 finally determines the net price and the net value resulting from it as price information.<br />In the last subtotal line item \"net value\", a deviating net price may theoretically result as described, but it is of no importance.</p>\r\n<p><br />Case B: Pricing result for 10 pcs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"1\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>PR00</td>\r\n<td>Price</td>\r\n<td>135.50</td>\r\n<td>EUR per pc</td>\r\n<td>1,355.00</td>\r\n<td>EUR</td>\r\n</tr>\r\n<tr>\r\n<td>RA00</td>\r\n<td>Discount</td>\r\n<td>-9.00</td>\r\n<td>%</td>\r\n<td>-121.95</td>\r\n<td>EUR</td>\r\n</tr>\r\n<tr>\r\n<td>NETP</td>\r\n<td>Rounding diff.</td>\r\n<td>123.31</td>\r\n<td>EUR per pc</td>\r\n<td>0.05</td>\r\n<td>EUR</td>\r\n</tr>\r\n<tr>\r\n<td>PNTP</td>\r\n<td>Net price</td>\r\n<td>123.31</td>\r\n<td>EUR per pc</td>\r\n<td>1,233.10</td>\r\n<td>EUR</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>Net value</td>\r\n<td>123.31</td>\r\n<td>EUR per pc</td>\r\n<td>1,233.10</td>\r\n<td>EUR</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>In both cases A and B, you can see that the relationship 'net value = net price * quantity' applies. However, the net prices differ by EUR 0.01 (EUR 123.30 per piece or EUR 123.31 per piece).<br /><br /></p>\r\n<p><em><strong>Pricing procedure variant 2:</strong></em></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"1\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>CType</td>\r\n<td>Description</td>\r\n<td>Req.</td>\r\n<td>AltCTy</td>\r\n<td>AltCBV</td>\r\n<td>Stat</td>\r\n<td>Print</td>\r\n</tr>\r\n<tr>\r\n<td>PR00</td>\r\n<td>Gross price</td>\r\n<td>&#x00A0;2</td>\r\n<td>&nbsp;</td>\r\n<td>&#x00A0;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>RA00</td>\r\n<td>Discount</td>\r\n<td>&#x00A0;2</td>\r\n<td>19</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>NETP</td>\r\n<td>Rounding diff.</td>\r\n<td>&#x00A0;2</td>\r\n<td>6</td>\r\n<td>&#x00A0;17</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>PNTP</td>\r\n<td>Net price</td>\r\n<td>&#x00A0;2</td>\r\n<td>&#x00A0;906</td>\r\n<td>&#x00A0;17</td>\r\n<td>\r\n<p>&#x00A0;X</p>\r\n</td>\r\n<td>&#x00A0;X</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>Net value</td>\r\n<td>&#x00A0;2</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>...</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Variant 2 has the following properties:</p>\r\n<ul>\r\n<li>The relationship 'net value = net price * quantity' applies. This corrects symptom 1.</li>\r\n</ul>\r\n<ul>\r\n<li>The net price remains constant. This means that the same net price always arises regardless of the quantity.&#x00A0;This corrects symptom 2.</li>\r\n</ul>\r\n<ul>\r\n<li>Discounts are calculated correctly.</li>\r\n</ul>\r\n<p>In contrast with variant 1, this variant also ensures that the net price always remains constant.</p>\r\n<p>Instead of the condition type RA00 or in addition to the condition type RA00, you can use the following condition types with the following value formulas if they come before the condition NETP:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"1\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>CType</td>\r\n<td>Description</td>\r\n<td>Value formula</td>\r\n<td>Calculation type</td>\r\n</tr>\r\n<tr>\r\n<td>RA00</td>\r\n<td>Percentage of gross</td>\r\n<td>&#x00A0;19</td>\r\n<td>&#x00A0;A</td>\r\n</tr>\r\n<tr>\r\n<td>RA01</td>\r\n<td>Percentage of decr. value</td>\r\n<td>&#x00A0;20</td>\r\n<td>&#x00A0;A</td>\r\n</tr>\r\n<tr>\r\n<td>RC00</td>\r\n<td>Quantity discount</td>\r\n<td>&#x00A0;19</td>\r\n<td>&#x00A0;C</td>\r\n</tr>\r\n<tr>\r\n<td>RD00</td>\r\n<td>Gross weight discount</td>\r\n<td>&#x00A0;19</td>\r\n<td>&#x00A0;D</td>\r\n</tr>\r\n<tr>\r\n<td>RE00</td>\r\n<td>Net weight discount</td>\r\n<td>&#x00A0;19</td>\r\n<td>&#x00A0;E</td>\r\n</tr>\r\n<tr>\r\n<td>RF00</td>\r\n<td>Volume discount</td>\r\n<td>&#x00A0;19</td>\r\n<td>&#x00A0;F</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br /><em>Mode of operation of variant 2:</em><br /><br />Case A: Pricing result for 3 pcs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"1\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>PR00</td>\r\n<td>Price</td>\r\n<td>135.50</td>\r\n<td>EUR per pc</td>\r\n<td>406.50</td>\r\n<td>EUR</td>\r\n</tr>\r\n<tr>\r\n<td>RA00</td>\r\n<td>Discount</td>\r\n<td>-9.00</td>\r\n<td>%</td>\r\n<td>-36.59</td>\r\n<td>EUR</td>\r\n</tr>\r\n<tr>\r\n<td>NETP</td>\r\n<td>Rounding diff.</td>\r\n<td>123.31</td>\r\n<td>EUR per pc</td>\r\n<td>0.02</td>\r\n<td>EUR</td>\r\n</tr>\r\n<tr>\r\n<td>PNTP</td>\r\n<td>Net price</td>\r\n<td>123.31</td>\r\n<td>EUR per pc</td>\r\n<td>369.93</td>\r\n<td>EUR</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>Net value</td>\r\n<td>123.31</td>\r\n<td>EUR per pc</td>\r\n<td>369.93</td>\r\n<td>EUR</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>In contrast with variant 1, the discount is also calculated directly on the net price using value formula 19 here. The net price is therefore no longer calculated by dividing the net value by the quantity. As a result, the net price is always the same due to the construction. This net price calculated in this way is then set as a condition amount in base formula 17. However, the standard procedure in which the discount is calculated from the net value runs parallel. In value formula 6, the difference between the product of 'net price * quantity' and the independently calculated net value is now determined again. The net value is then corrected by this amount. Using value formula 906, the condition type PNTP sets the net price and net value determined in this way anew.</p>\r\n<p><br />Case B: Pricing result for 10 pcs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"1\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>PR00</td>\r\n<td>Price</td>\r\n<td>135.50</td>\r\n<td>EUR per pc</td>\r\n<td>1,355.00</td>\r\n<td>EUR</td>\r\n</tr>\r\n<tr>\r\n<td>RA00</td>\r\n<td>Discount</td>\r\n<td>-9.00</td>\r\n<td>%</td>\r\n<td>-121.95</td>\r\n<td>EUR</td>\r\n</tr>\r\n<tr>\r\n<td>NETP</td>\r\n<td>Rounding diff.</td>\r\n<td>123.31</td>\r\n<td>EUR per pc</td>\r\n<td>0.05</td>\r\n<td>EUR</td>\r\n</tr>\r\n<tr>\r\n<td>PNTP</td>\r\n<td>Net price</td>\r\n<td>123.31</td>\r\n<td>EUR per pc</td>\r\n<td>1,233.10</td>\r\n<td>EUR</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>Net value</td>\r\n<td>123.31</td>\r\n<td>EUR per pc</td>\r\n<td>1,233.10</td>\r\n<td>EUR</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>You can see that in variant 3, the relationship 'net price * quantity = net value' is fulfilled and, in addition, the net price is the same.<br /><br /></p>\r\n<p><em><strong>Pricing procedure variant 3 (P variant from R/2):</strong></em></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"1\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>CType</td>\r\n<td>Description</td>\r\n<td>Req.</td>\r\n<td>AltCTy</td>\r\n<td>AltCBV</td>\r\n<td>Stat</td>\r\n<td>Print</td>\r\n</tr>\r\n<tr>\r\n<td>PR00</td>\r\n<td>Price</td>\r\n<td>&#x00A0;2</td>\r\n<td>&nbsp;</td>\r\n<td>&#x00A0;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>RA00</td>\r\n<td>Discount</td>\r\n<td>&#x00A0;2</td>\r\n<td>&#x00A0;919</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>PNTP</td>\r\n<td>Net price</td>\r\n<td>&#x00A0;2</td>\r\n<td>&#x00A0;906</td>\r\n<td>&#x00A0;917</td>\r\n<td>\r\n<p>&#x00A0;X</p>\r\n</td>\r\n<td>&#x00A0;X</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>Net value</td>\r\n<td>&#x00A0;2</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&#x00A0;X</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>...</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Variant 3 has the following properties:</p>\r\n<ul>\r\n<li>The relationship 'net value = net price * quantity' applies. This corrects symptom 1.</li>\r\n</ul>\r\n<ul>\r\n<li>The net price remains constant. This means that the same net price always arises regardless of the quantity. This corrects symptom 2.</li>\r\n</ul>\r\n<ul>\r\n<li>Discounts are not calculated correctly.</li>\r\n</ul>\r\n<p>As with variant 2, this variant ensures that the relationship 'net price * quantity = net value' is always fulfilled and the net price always remains the same.&#x00A0;Rounding differences, however, are no longer displayed separately. Instead, they are contained in the discount.</p>\r\n<p>Instead of the condition type RA00 or in addition to the condition type RA00, you can use the following condition types with the following value formulas if they come before the condition PNTP:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"1\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>CType</td>\r\n<td>Description</td>\r\n<td>Value formula</td>\r\n<td>Calculation type</td>\r\n</tr>\r\n<tr>\r\n<td>RA00</td>\r\n<td>Percentage of gross</td>\r\n<td>&#x00A0;919</td>\r\n<td>&#x00A0;A</td>\r\n</tr>\r\n<tr>\r\n<td>RA01</td>\r\n<td>Percentage of decr. value</td>\r\n<td>&#x00A0;920</td>\r\n<td>&#x00A0;A</td>\r\n</tr>\r\n<tr>\r\n<td>RC00</td>\r\n<td>Quantity discount</td>\r\n<td>&#x00A0;919</td>\r\n<td>&#x00A0;C</td>\r\n</tr>\r\n<tr>\r\n<td>RD00</td>\r\n<td>Gross weight discount</td>\r\n<td>&#x00A0;919</td>\r\n<td>&#x00A0;D</td>\r\n</tr>\r\n<tr>\r\n<td>RE00</td>\r\n<td>Net weight discount</td>\r\n<td>&#x00A0;919</td>\r\n<td>&#x00A0;E</td>\r\n</tr>\r\n<tr>\r\n<td>RF00</td>\r\n<td>Volume discount</td>\r\n<td>&#x00A0;919</td>\r\n<td>&#x00A0;F</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>By using formula 920, you continuously grant the discounts for the accumulated new value/net price. For formula 919, every discount for the net value/net price is calculated before the first condition to which formula 919 or 920 was assigned.</p>\r\n<p><br /><em>Mode of operation of variant 3:</em><br /><br />Case A: Pricing result for 3 pcs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"1\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>PR00</td>\r\n<td>Price</td>\r\n<td>135.50</td>\r\n<td>EUR per pc</td>\r\n<td>406.50</td>\r\n<td>EUR</td>\r\n</tr>\r\n<tr>\r\n<td>RA00</td>\r\n<td>Discount</td>\r\n<td>-9.00</td>\r\n<td>%</td>\r\n<td>-36.60</td>\r\n<td>EUR</td>\r\n</tr>\r\n<tr>\r\n<td>PNTP</td>\r\n<td>Net price</td>\r\n<td>123.30</td>\r\n<td>EUR per pc</td>\r\n<td>369.90</td>\r\n<td>EUR</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>Net value</td>\r\n<td>123.30</td>\r\n<td>EUR per pc</td>\r\n<td>369.90</td>\r\n<td>EUR</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Here, the net price is calculated directly first, and then the net value is calculated from it. You calculate the discount by determining the difference between the new net value and the initial amount. If the discount calculated in this way is then subtracted from the initial amount, you receive the first calculated net value again. This variant does not display any rounding differences since these differences are contained in the discount.</p>\r\n<p><br />Case B: Pricing result for 10 pcs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"1\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>PR00</td>\r\n<td>Price</td>\r\n<td>135.50</td>\r\n<td>EUR per pc</td>\r\n<td>1,355.00</td>\r\n<td>EUR</td>\r\n</tr>\r\n<tr>\r\n<td>RA00</td>\r\n<td>Discount</td>\r\n<td>-9.00</td>\r\n<td>%</td>\r\n<td>-122.00</td>\r\n<td>EUR</td>\r\n</tr>\r\n<tr>\r\n<td>PNTP</td>\r\n<td>Net price</td>\r\n<td>123.30</td>\r\n<td>EUR per pc</td>\r\n<td>1,233.00</td>\r\n<td>EUR</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>Net value</td>\r\n<td>123.30</td>\r\n<td>EUR per pc</td>\r\n<td>1,233.00</td>\r\n<td>EUR</td>\r\n</tr>\r\n</tbody>\r\n</table></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I538846)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I538846)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000080183/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000080183/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000080183/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000080183/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000080183/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000080183/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000080183/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000080183/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000080183/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "923023", "RefComponent": "AP-PRC-PR", "RefTitle": "positive net value with return items using note 80183", "RefUrl": "/notes/923023"}, {"RefNumber": "825410", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Brazilian Tax Calculation and Unit Price for Nota Fiscal", "RefUrl": "/notes/825410"}, {"RefNumber": "791944", "RefComponent": "SD-BF-PR", "RefTitle": "How is KBETR determined in a subtotal?", "RefUrl": "/notes/791944"}, {"RefNumber": "785568", "RefComponent": "AP-PRC-PR", "RefTitle": "Value 0 during NetPrice rounding with Form.10119/note 80183", "RefUrl": "/notes/785568"}, {"RefNumber": "531953", "RefComponent": "AP-PRC-PR", "RefTitle": "Rounding inaccuracies in the net price", "RefUrl": "/notes/531953"}, {"RefNumber": "526308", "RefComponent": "SD-BF-PR", "RefTitle": "Condition basis is too large by factor 100", "RefUrl": "/notes/526308"}, {"RefNumber": "521910", "RefComponent": "AP-PRC-PR", "RefTitle": "Object processing in IPC of user exits and formulas", "RefUrl": "/notes/521910"}, {"RefNumber": "502854", "RefComponent": "AP-PRC-PR", "RefTitle": "Several errors during the rounding of the net price", "RefUrl": "/notes/502854"}, {"RefNumber": "391139", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/391139"}, {"RefNumber": "39034", "RefComponent": "SD-BF-PR", "RefTitle": "Group condition routine", "RefUrl": "/notes/39034"}, {"RefNumber": "38389", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/38389"}, {"RefNumber": "381348", "RefComponent": "SD", "RefTitle": "Using user exit, customer exit, VOFM in SD", "RefUrl": "/notes/381348"}, {"RefNumber": "19454", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/19454"}, {"RefNumber": "130742", "RefComponent": "SD-BF-PR", "RefTitle": "Roundings in subtotal lines", "RefUrl": "/notes/130742"}, {"RefNumber": "1276910", "RefComponent": "MM-PUR-VM-REC", "RefTitle": "Wrong Net Price Calculation", "RefUrl": "/notes/1276910"}, {"RefNumber": "122595", "RefComponent": "SD-BF-PR", "RefTitle": "Inactive conditions with value formula 19 or 20", "RefUrl": "/notes/122595"}, {"RefNumber": "117347", "RefComponent": "SD-BF-PR", "RefTitle": "Pricing type K modified for condition category L", "RefUrl": "/notes/117347"}, {"RefNumber": "116632", "RefComponent": "SD-BF-PR", "RefTitle": "Net price is inaccurate", "RefUrl": "/notes/116632"}, {"RefNumber": "102374", "RefComponent": "SD-BF-PR", "RefTitle": "Constant net price during milestone billing", "RefUrl": "/notes/102374"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2126601", "RefComponent": "FI-LOC-SD-BR", "RefTitle": "Guidelines for Avoiding Rounding Issues in Brazil Sales Nota Fiscal", "RefUrl": "/notes/2126601 "}, {"RefNumber": "2776897", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit:  Collective KBA Migration object \"Purchasing info record\"/ \"Purchasing info record- extend existing record\"", "RefUrl": "/notes/2776897 "}, {"RefNumber": "2679294", "RefComponent": "CA-GTF-MIG", "RefTitle": "Migration Cockpit: Enter Net Price Error or Enter order Price Un.", "RefUrl": "/notes/2679294 "}, {"RefNumber": "2437878", "RefComponent": "MM-PUR-PO", "RefTitle": "Change the field quantity (MENGE) to more than 3 decimal places", "RefUrl": "/notes/2437878 "}, {"RefNumber": "3052816", "RefComponent": "AP-PRC-PR", "RefTitle": "Return item shows incorrect prices due to value formula 906", "RefUrl": "/notes/3052816 "}, {"RefNumber": "2270050", "RefComponent": "AP-PRC-PR", "RefTitle": "Rounding", "RefUrl": "/notes/2270050 "}, {"RefNumber": "1276910", "RefComponent": "MM-PUR-VM-REC", "RefTitle": "Wrong Net Price Calculation", "RefUrl": "/notes/1276910 "}, {"RefNumber": "1456045", "RefComponent": "AP-PRC-PR", "RefTitle": "rounding error in pre-delivered formula based on note 80183", "RefUrl": "/notes/1456045 "}, {"RefNumber": "521910", "RefComponent": "AP-PRC-PR", "RefTitle": "Object processing in IPC of user exits and formulas", "RefUrl": "/notes/521910 "}, {"RefNumber": "39034", "RefComponent": "SD-BF-PR", "RefTitle": "Group condition routine", "RefUrl": "/notes/39034 "}, {"RefNumber": "102374", "RefComponent": "SD-BF-PR", "RefTitle": "Constant net price during milestone billing", "RefUrl": "/notes/102374 "}, {"RefNumber": "791944", "RefComponent": "SD-BF-PR", "RefTitle": "How is KBETR determined in a subtotal?", "RefUrl": "/notes/791944 "}, {"RefNumber": "391139", "RefComponent": "AP-PRC-PR", "RefTitle": "Rounding", "RefUrl": "/notes/391139 "}, {"RefNumber": "923023", "RefComponent": "AP-PRC-PR", "RefTitle": "positive net value with return items using note 80183", "RefUrl": "/notes/923023 "}, {"RefNumber": "858793", "RefComponent": "AP-PRC-PR", "RefTitle": "Incorrect value with formula 10119 & 10120 (note 80183)", "RefUrl": "/notes/858793 "}, {"RefNumber": "785568", "RefComponent": "AP-PRC-PR", "RefTitle": "Value 0 during NetPrice rounding with Form.10119/note 80183", "RefUrl": "/notes/785568 "}, {"RefNumber": "1000681", "RefComponent": "SD-BF-PR", "RefTitle": "Syntax error: Missing data declaration for PROZENTUAL", "RefUrl": "/notes/1000681 "}, {"RefNumber": "825410", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Brazilian Tax Calculation and Unit Price for Nota Fiscal", "RefUrl": "/notes/825410 "}, {"RefNumber": "381348", "RefComponent": "SD", "RefTitle": "Using user exit, customer exit, VOFM in SD", "RefUrl": "/notes/381348 "}, {"RefNumber": "526308", "RefComponent": "SD-BF-PR", "RefTitle": "Condition basis is too large by factor 100", "RefUrl": "/notes/526308 "}, {"RefNumber": "531953", "RefComponent": "AP-PRC-PR", "RefTitle": "Rounding inaccuracies in the net price", "RefUrl": "/notes/531953 "}, {"RefNumber": "502854", "RefComponent": "AP-PRC-PR", "RefTitle": "Several errors during the rounding of the net price", "RefUrl": "/notes/502854 "}, {"RefNumber": "130742", "RefComponent": "SD-BF-PR", "RefTitle": "Roundings in subtotal lines", "RefUrl": "/notes/130742 "}, {"RefNumber": "117347", "RefComponent": "SD-BF-PR", "RefTitle": "Pricing type K modified for condition category L", "RefUrl": "/notes/117347 "}, {"RefNumber": "116632", "RefComponent": "SD-BF-PR", "RefTitle": "Net price is inaccurate", "RefUrl": "/notes/116632 "}, {"RefNumber": "122595", "RefComponent": "SD-BF-PR", "RefTitle": "Inactive conditions with value formula 19 or 20", "RefUrl": "/notes/122595 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "MERCURY", "To": "MERCURY", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "616", "To": "616", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "618", "To": "618", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "106", "To": "106", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "107", "To": "107", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "108", "To": "108", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 2, "URL": "/corrins/0000080183/1"}, {"SoftwareComponent": "S4CORE", "NumberOfCorrin": 1, "URL": "/corrins/0000080183/19773"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}