{"Request": {"Number": "1028690", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 420, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016316592017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001028690?language=E&token=1B14B2EFFA87EBB356E0A1A2B5EDE7FA"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001028690", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001028690/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1028690"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Customizing"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.02.2010"}, "SAPComponentKey": {"_label": "Component", "value": "BC-FES-IGS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Internet Graphics Server"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Frontend Services (SAP Note 1322184)", "value": "BC-FES", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-FES*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Internet Graphics Server", "value": "BC-FES-IGS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-FES-IGS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1028690 - True Type Fonts delivered with IGS"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You want to install and configure the fonts that are shipped with the IGS.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Fonts True Type installation igshelper<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You have installed IGS with at least one of the interpreters BWGIS, GFWCHART and XMLCHART. You want to use a font different than the IGS standard font with limited options.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><B><B>Installation:</B></B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>IGS 6.40:</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Install IGS 6.40 with patch level 19 or higher. When you install the Win32 standalone IGS the fonts are registered in your Windows environment after installation.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>IGS 7.x:</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Install the latest patch of the package SAP IGS Fonts and Textures (igshelper.sar) from the Service Marketplace. This package must be extracted to DIR_INSTANCE. When using IGS 7.00 patch level 8 of igsexe.sar or higher is required in addition.<br /><br /><B>Configuration:</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The font files are located in &lt;IGS_ROOT_DIR&gt;/data/fonts. To use these fonts you need to copy the template configuration files (\"*.tpl\") in &lt;IGS_ROOT_DIR&gt;/conf to its corresponding names without file extension (see also notes 531668, 596825 and 1011372). The folder &lt;IGS_ROOT_DIR&gt;/data/fonts is scanned implicitly. You do not need to specify a FONTPATH.<br /><br /><B>Description of the Font Files:</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The following four font files are delivered. The name in brackets is the internal font name:</p> <UL><LI>ARIALUNI_J.TTF (Arial Unicode J): Arial Unicode font with beautified characters for Japanese.</LI></UL> <UL><LI>ARIALUNI_K.TTF (Arial Unicode K): Arial Unicode font with beautified characters for Korean.</LI></UL> <UL><LI>ARIALUNI_S.TTF (Arial Unicode S): Arial Unicode font with beautified characters for Simplified Chinese.</LI></UL> <UL><LI>ARIALUNI_T.TTF (Arial Unicode T): Arial Unicode font with beautified characters for Traditional Chinese.</LI></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D044990)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D044990)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001028690/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001028690/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001028690/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001028690/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001028690/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001028690/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001028690/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001028690/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001028690/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "896400", "RefComponent": "BC-FES-IGS", "RefTitle": "Upgrade your integrated IGS 7.x installation", "RefUrl": "/notes/896400"}, {"RefNumber": "718267", "RefComponent": "BC-FES-IGS", "RefTitle": "Upgrade your integrated IGS 6.40 installation", "RefUrl": "/notes/718267"}, {"RefNumber": "596825", "RefComponent": "BC-FES-IGS", "RefTitle": "GFWCHART configuration file", "RefUrl": "/notes/596825"}, {"RefNumber": "531668", "RefComponent": "BC-FES-IGS", "RefTitle": "XMLCHART configuration file", "RefUrl": "/notes/531668"}, {"RefNumber": "1775413", "RefComponent": "BC-FES-IGS", "RefTitle": "IGS graphics rendering problem", "RefUrl": "/notes/1775413"}, {"RefNumber": "1049775", "RefComponent": "BC-FES-IGS", "RefTitle": "Bouncing characters with Arial Unicode fonts", "RefUrl": "/notes/1049775"}, {"RefNumber": "1049450", "RefComponent": "BC-FES-IGS", "RefTitle": "IGS 6.40 Patch 19", "RefUrl": "/notes/1049450"}, {"RefNumber": "1035589", "RefComponent": "BC-FES-IGS", "RefTitle": "Arial Unicode J is not set as default font", "RefUrl": "/notes/1035589"}, {"RefNumber": "1011372", "RefComponent": "BC-FES-IGS", "RefTitle": "BWGIS configuration file", "RefUrl": "/notes/1011372"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2542495", "RefComponent": "BC-FES-IGS", "RefTitle": "Which IGSHELPER should be using with my IGS?", "RefUrl": "/notes/2542495 "}, {"RefNumber": "2319931", "RefComponent": "SV-SMG-PSM", "RefTitle": "JSM Health Check: Collective Note", "RefUrl": "/notes/2319931 "}, {"RefNumber": "1636252", "RefComponent": "BC-CST", "RefTitle": "Installing a 7.20 kernel in SAP Web AS 7.00/7.01/7.10/7.11", "RefUrl": "/notes/1636252 "}, {"RefNumber": "896400", "RefComponent": "BC-FES-IGS", "RefTitle": "Upgrade your integrated IGS 7.x installation", "RefUrl": "/notes/896400 "}, {"RefNumber": "1775413", "RefComponent": "BC-FES-IGS", "RefTitle": "IGS graphics rendering problem", "RefUrl": "/notes/1775413 "}, {"RefNumber": "596825", "RefComponent": "BC-FES-IGS", "RefTitle": "GFWCHART configuration file", "RefUrl": "/notes/596825 "}, {"RefNumber": "531668", "RefComponent": "BC-FES-IGS", "RefTitle": "XMLCHART configuration file", "RefUrl": "/notes/531668 "}, {"RefNumber": "1011372", "RefComponent": "BC-FES-IGS", "RefTitle": "BWGIS configuration file", "RefUrl": "/notes/1011372 "}, {"RefNumber": "1035589", "RefComponent": "BC-FES-IGS", "RefTitle": "Arial Unicode J is not set as default font", "RefUrl": "/notes/1035589 "}, {"RefNumber": "718267", "RefComponent": "BC-FES-IGS", "RefTitle": "Upgrade your integrated IGS 6.40 installation", "RefUrl": "/notes/718267 "}, {"RefNumber": "1049450", "RefComponent": "BC-FES-IGS", "RefTitle": "IGS 6.40 Patch 19", "RefUrl": "/notes/1049450 "}, {"RefNumber": "1049775", "RefComponent": "BC-FES-IGS", "RefTitle": "Bouncing characters with Arial Unicode fonts", "RefUrl": "/notes/1049775 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BC-FES-IGS", "From": "6.40", "To": "6.40", "Subsequent": ""}, {"SoftwareComponent": "BC-FES-IGS", "From": "7.10", "To": "7.10", "Subsequent": ""}, {"SoftwareComponent": "BC-FES-IGS", "From": "7.00", "To": "7.00", "Subsequent": ""}, {"SoftwareComponent": "BC-FES-IGS", "From": "7.20", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "BC-FES-IGS", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "BC-FES-IGS", "From": "7.45", "To": "7.45", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}