{"Request": {"Number": "712757", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 194, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000712757?language=E&token=6C31DAE9BAFD6FEF9BCCF15801FECAEE"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000712757", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000712757/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "712757"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Workaround of missing functionality"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "03.06.2004"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SDD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Service Data Download"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Data Download", "value": "SV-SMG-SDD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SDD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "712757 - SDCC for Basis rel 3.x"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>I. You want to implement SDCC version 2.3 on a system with Basis<br />Release 3.x and prevent possible shortdumps connected to function<br />module BDL_FUPDEF_INTERFACE_GET.<br /><br />II.You already use SDCC 2.3 in a system with Release &lt;=3.1I and you did a data collection for a service session. This results in shortdumps (CONNE_ILLEGAL_TRANSPORT_VERS ) connected to function module BDL_FUPDEF_INTERFACE_GET.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SDCC, Earlywatch Alert, Service Definitions, BDL_FUPDEF_INTERFACE_GET CONNE_ILLEGAL_TRANSPORT_VERS</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The&#x00A0;&#x00A0;SAPNet R/3 Frontend System (former OSS)&#x00A0;&#x00A0;was&#x00A0;&#x00A0;upgraded to Basis Release 620 (Unicode,) The content of cluster tables on such a system can not be read by a system on Basis Release &lt;=3.1I. The ABAP commands IMPORT FROM DATABASE&#x00A0;&#x00A0;and&#x00A0;&#x00A0;EXPORT TO DATABASE are not downwards compatible. A Service Definition Refresh from OSS therefore can lead to shortdumps( CONNE_ILLEGAL_TRANSPORT_VERS )&#x00A0;&#x00A0;during the data collection.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br /><B>I. You need SDCC 2.3</B><br /><br />Step 1: SDCC version 2.3 is needed to collect reliable data for a<br />service session. Implement the transports listed in Appendix A.<br /><br />*********************IMPORTANT*****************************************<br />Step 2: After the implementation of the transports you must IMMEDIATELY change the settings described in Appendix B, BEFORE transaction SDCC is executed. Otherwise calling SDCC triggers an initial Service Definition refresh.<br />Service Definitions must not be refreshed, manually or automatically.<br />************************************************************************<br /><br />Step 3<br />Use SDCC for data collections and transfers as needed.<br /><br /><br /><B>II. You already use SDCC 2.3</B><br /><B>A Service Definition Refresh from SAPNet R/3 Frontend has been</B><br /><B>executed, which now causes problems with the Service Definitions</B><br /><B>and the data collections. To correct this follow the steps below.</B><br /><br />Step 1: You must IMMEDIATELY change the settings described in Appendix B BEFORE transaction SDCC is executed again. Otherwise calling SDCC triggers a Service Definition refresh from SAPNet R/3 Frontend (former OSS), which would undo the corrections from Steps 2 - 4.<br />Service Definitions must not be refreshed, manually or automatically.<br /><br />Step 2: You must delete all entries for the Service Definition tables.<br />These are listed in table BDL2TRANS.<br /><br />To avoid having to delete the entries manually you can create a<br />report. Please follow the steps in Appendix C.<br /><br />Step 3: Please implement transport SDCC_3x.CAR, which you can find on<br />sapservX, /general/R3server/abap/note.0712757. The state of the entries<br />of the tables is September 2003<br /><br />Step 4 : Now call function module BDL_GENERATE_LOGFUNC_INCLUDES<br />using Transaction SE37&#x00A0;&#x00A0;and 'Single test (F8)' . Assign the value 'ALL'<br />import parameter 'OPTION' (keep the default values for all other<br />parameters) and execute the module. Note that during this activity there<br />is an increased system load. During this time you can not use SDCC, or<br />you will generate a shortdump LOAD_PROGRAM_LOST and lose your data.<br /><br />************************************************************************<br /><br /><B>Appendix A: SDCC version 2.3 - Transports</B><br /><br />Release 3.1H + 3.1I<br />Please follow the instructions in note 597323, and implement<br />both transports, SAPKVSDB70 and SAPKVSDB99.<br />SDCC 2.3 is contained in&#x00A0;&#x00A0;SAPKVSDB70. When both transports are<br />implemented SDCC will be able to use all function modules developed<br />for systems on Basis Release 3.x .<br />No Service Definition refreshes are needed.<br /><br />Release 3.1G<br />1)Please follow the instructions in note 597323, and implement<br />transport SAPKVSDB21.This contains a lower version of SDCC as well as<br />the function modules developed for Basis Release 3.x .<br />2)To update SDCC to version 2.3 please implement the transport from note<br />178631.<br />When both transports are implemented SDCC will be able to use all<br />function modules developed for systems on Basis Release 3.x<br />No Service Definition refreshes are needed.<br /><br />Release 3.0D to 3.0F,<br />1) Please follow the instructions in note 597323, and implement<br />transport SAPKVSDB20. This contains a lower version of SDCC as well as<br />the function modules developed for Basis Release 3.x.<br />2)To update SDCC to version 2.3 please implement the transport from note<br />178631.<br />When both transports are implemented SDCC will be able to use all<br />function modules developed for systems on Basis Release 3.x.<br />No Service Definition refreshes are needed.<br />************************************************************************<br /><br /><B>Appendix B: Settings in BDLCUST</B><br /><br />To avoid attempts to refresh Service Definitions from OSS please change the following entries&#x00A0;&#x00A0;in table BDLCUST, via SE38 -&gt; BDLSETUP.<br /><br />KEY:&#x00A0;&#x00A0;SERVDEFS<br />This determines if&#x00A0;&#x00A0;the service definitions should be refreshed for every run of the ASM. The default value here is X, this entry has to be changed to 'space'.<br /><br />KEY: DESTSRC and KEY: IDESTSRC These determine which destinations are used for a Service Definition Refresh. The values for both must be set to 'space', so that if an attempt is made to refresh Service Definitions manually, a connection attempt is unsuccesful. This will cause an error message in the SDCC Log, which in this situation should be ignored.<br /><br />KEY: SERVDATE<br />This determines when the next Service Definition refresh will be triggered automatically. Please set the value to '99999999'<br />************************************************************************<br /><br /><B>Appendix C: How to create Report ZZDELDEF:</B><br /><br />1.)&#x00A0;&#x00A0;Call transaction 'SE38'<br />2.)&#x00A0;&#x00A0;Type 'ZZDELDEF' in field 'Program' and choose the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; 'create' button<br />3.) Choose 'Local object' (no Development class needed !) in the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;'Create Object Directory entry'-pop-up<br />4.) Fill in the report attributes:<br />- choose TITLE&#x00A0;&#x00A0;=&#x00A0;&#x00A0;'Expert report !!! Deletes all BDL-Tables<br />&#x00A0;&#x00A0;(Service Definitons)'<br />- choose TYPE = '1'<br />- choose STATUS = 'S' (System program)<br />- choose Application = 'S'&#x00A0;&#x00A0;(Basis)<br />5.) Save (local object) and fill in the source code.<br />To make the copying of the source code easier it is also available<br />as Rich Text File, attached to this note. You can access this<br />attachment if you view the note in the SAPNet Web Frontend.<br /><br />6.) Choose program-&gt;generate<br />7.) You can execute the report now (with F8)<br /><br /> *&amp;---------------------------------------------------------------------*<br />*&amp; Report&#x00A0;&#x00A0;ZZDELDEF&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*<br />*&amp;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *<BR/> *&amp;---------------------------------------------------------------------*<br />*&amp;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *<br />*&amp;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *<br />*&amp; ---------------------------------------------------------------------*<br />REPORT&#x00A0;&#x00A0;ZZDELDEF.<br /><br />constants:<br />lf_bdlsertran(11) type c value 'BDLSERTRAN.',<br />lf_bdlfugrps(10)&#x00A0;&#x00A0;type c value 'BDLFUGRPS.',<br />lf_bdlfunc(8)&#x00A0;&#x00A0;&#x00A0;&#x00A0;type c value 'BDLFUNC.',<br />lf_bdlfuncmap(11) type c value 'BDLFUNCMAP.',<br />lf_bdlfupdef(10)&#x00A0;&#x00A0;type c value 'BDLFUPDEF.',<br />lf_bdlfupexp(10)&#x00A0;&#x00A0;type c value 'BDLFUPEXP.',<br />lf_bdlfupimp(10)&#x00A0;&#x00A0;type c value 'BDLFUPIMP.',<br />lf_bdlfuvers(10)&#x00A0;&#x00A0;type c value 'BDLFUVERS.',<br />lf_bdlfuver2(10)&#x00A0;&#x00A0;type c value 'BDLFUVER2.',<br />lf_bdlfuver3(10)&#x00A0;&#x00A0;type c value 'BDLFUVER3.',<br />lf_bdlgroups(10)&#x00A0;&#x00A0;type c value 'BDLGROUPS.',<br />lf_bdlsadata(10)&#x00A0;&#x00A0;type c value 'BDLSADATA.',<br />lf_bdlsaif(8)&#x00A0;&#x00A0;&#x00A0;&#x00A0;type c value 'BDLSAIF.',<br />lf_bdlsergrps(11) type c value 'BDLSERGRPS.',<br />lf_bdlservice(11) type c value 'BDLSERVICE.',<br />lf_bdlst14key(11) type c value 'BDLST14KEY.',<br />lf_bdlctx2exe(11) type c value 'BDLCTX2EXE.',<br />lf_bdldirfunc(11) type c value 'BDLDIRFUNC.',<br />lf_bdlendfunc(11) type c value 'BDLENDFUNC.',<br />lf_bdlerrors(10)&#x00A0;&#x00A0;type c value 'BDLERRORS.',<br />lf_bdlexcept(10)&#x00A0;&#x00A0;type c value 'BDLEXCEPT.',<br />lf_bdlctext(9)&#x00A0;&#x00A0;&#x00A0;&#x00A0;type c value 'BDLCTEXT.',<br />lf_bdlrefserv(11) type c value 'BDLREFSERV.',<br />lf_bdl2trans(10)&#x00A0;&#x00A0;type c value 'BDL2TRANS.'.<br />tables:<br />bdlfugrps,<br />bdlfunc,<br />bdlfuncmap,<br />bdlfupdef,<br />bdlfupexp,<br />bdlfupimp,<br />bdlfuvers,<br />bdlfuver2,<br />bdlfuver3,<br />bdlgroups,<br />bdlsadata,<br />bdlsaif,<br />bdlsergrps,<br />bdlservice,<br />bdlst14key,<br />bdlctx2exe,<br />bdldirfunc,<br />bdlendfunc,<br />bdlsertran,<br />bdlerrors,<br />bdlexcept,<br />bdlctext,<br />bdlrefserv,<br />bdl2trans.<br />data: answer type c.<br /><br />call function 'POPUP_TO_CONFIRM_LOSS_OF_DATA'<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;exporting<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;textline1&#x00A0;&#x00A0;= 'Really delete all BDL tables ?'<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;titel&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;= 'Delete all BDL tables'<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;importing<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;answer = answer<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;exceptions<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;others&#x00A0;&#x00A0;&#x00A0;&#x00A0;= 0.<br />if answer &lt;&gt; 'J'.<br />&#x00A0;&#x00A0;message id 'S1' type 'I' number '333'<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;with 'Deletion terminated!'.<br />&#x00A0;&#x00A0;exit.<br />endif.<br /><br />delete from bdlctext where chgstamp like '%'.<br />write: / sy-dbcnt, 'rows deleted from', lf_bdlctext.<br /><br />delete from bdlctx2exe where chgstamp like '%'.<br />write: / sy-dbcnt, 'rows deleted from', lf_bdlctx2exe.<br /><br />delete from bdldirfunc where chgstamp like '%'.<br />write: / sy-dbcnt, 'rows deleted from', lf_bdldirfunc.<br /><br />delete from bdlendfunc where chgstamp like '%'.<br />write: / sy-dbcnt, 'rows deleted from', lf_bdlendfunc.<br /><br />delete from bdlerrors where chgstamp like '%'.<br />write: / sy-dbcnt, 'rows deleted from', lf_bdlerrors.<br /><br />delete from bdlexcept where chgstamp like '%'.<br />write: / sy-dbcnt, 'rows deleted from', lf_bdlexcept.<br /><br />delete from bdlfugrps where chgstamp like '%'.<br />write: / sy-dbcnt, 'rows deleted from', lf_bdlfugrps.<br /><br />delete from bdlfunc where chgstamp like '%'.<br />write: / sy-dbcnt, 'rows deleted from', lf_bdlfunc.<br /><br />delete from bdlfuncmap where chgstamp like '%'.<br />write: / sy-dbcnt, 'rows deleted from', lf_bdlfuncmap.<br /><br />delete from bdlfupdef where chgstamp like '%'.<br />write: / sy-dbcnt, 'rows deleted from', lf_bdlfupdef.<br /><br />delete from bdlfupexp where chgstamp like '%'.<br />write: / sy-dbcnt, 'rows deleted from', lf_bdlfupexp.<br /><br />delete from bdlfupimp where chgstamp like '%'.<br />write: / sy-dbcnt, 'rows deleted from', lf_bdlfupimp.<br /><br />delete from bdlfuver2 where chgstamp like '%'.<br />write: / sy-dbcnt, 'rows deleted from', lf_bdlfuver2.<br /><br />delete from bdlfuver3 where chgstamp like '%'.<br />write: / sy-dbcnt, 'rows deleted from', lf_bdlfuver3.<br /><br />delete from bdlfuvers where chgstamp like '%'.<br />write: / sy-dbcnt, 'rows deleted from', lf_bdlfuvers.<br /><br />delete from bdlgroups where chgstamp like '%'.<br />write: / sy-dbcnt, 'rows deleted from', lf_bdlgroups.<br /><br />delete from bdlrefserv where chgstamp like '%'.<br />write: / sy-dbcnt, 'rows deleted from', lf_bdlrefserv.<br /><br />delete from bdlsadata where chgstamp like '%'.<br />write: / sy-dbcnt, 'rows deleted from', lf_bdlsadata.<br /><br />delete from bdlsaif where chgstamp like '%'.<br />write: / sy-dbcnt, 'rows deleted from', lf_bdlsaif.<br /><br />delete from bdlsergrps where chgstamp like '%'.<br />write: / sy-dbcnt, 'rows deleted from', lf_bdlsergrps.<br /><br />delete from bdlsertran where chgstamp like '%'.<br />write: / sy-dbcnt, 'rows deleted from', lf_bdlsertran.<br /><br />delete from bdlservice where chgstamp like '%'.<br />write: / sy-dbcnt, 'rows deleted from', lf_bdlservice.<br /><br />delete from bdlst14key where chgstamp like '%'.<br />write: / sy-dbcnt, 'rows deleted from', lf_bdlst14key.<br /><br />delete from bdl2trans where sapnet_chg like '%'.<br />write: / sy-dbcnt, 'rows deleted from', lf_bdl2trans.<br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D038895)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D038895)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000712757/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000712757/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000712757/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000712757/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000712757/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000712757/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000712757/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000712757/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000712757/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "ZZBDL_DELETE_ALL_TABLES.txt", "FileSize": "4", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000202452004&iv_version=0008&iv_guid=7D4506DA181A90419B90E4D7C8E4CFB1"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488"}, {"RefNumber": "781680", "RefComponent": "SV-SMG-SDD", "RefTitle": "SDCC/ SDCCN - Problems with function modules", "RefUrl": "/notes/781680"}, {"RefNumber": "516520", "RefComponent": "SV-SMG-SDD", "RefTitle": "Short dump in program RSBDLPUTL with Transaction SDCC", "RefUrl": "/notes/516520"}, {"RefNumber": "216952", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCC) - FAQ", "RefUrl": "/notes/216952"}, {"RefNumber": "207223", "RefComponent": "SV-SMG-SDD", "RefTitle": "SAP EarlyWatch Alert Processed at SAP", "RefUrl": "/notes/207223"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "781680", "RefComponent": "SV-SMG-SDD", "RefTitle": "SDCC/ SDCCN - Problems with function modules", "RefUrl": "/notes/781680 "}, {"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488 "}, {"RefNumber": "216952", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCC) - FAQ", "RefUrl": "/notes/216952 "}, {"RefNumber": "516520", "RefComponent": "SV-SMG-SDD", "RefTitle": "Short dump in program RSBDLPUTL with Transaction SDCC", "RefUrl": "/notes/516520 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}