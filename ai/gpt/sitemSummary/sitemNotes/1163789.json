{"Request": {"Number": "1163789", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 390, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016505552017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001163789?language=E&token=3C7A7A747A62A6D65BD15868BA9AB29F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001163789", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001163789/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1163789"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 20}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.01.2011"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-ET-WJR"}, "SAPComponentKeyText": {"_label": "Component", "value": "BEx Web Java Runtime"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Explorer", "value": "BW-BEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Enduser Technology", "value": "BW-BEX-ET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-ET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BEx Web Java Runtime", "value": "BW-BEX-ET-WJR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-ET-WJR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1163789 - NetWeaver 7.0: BI Java Synchronized Patch Delivery Strategy"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>&#x00A0;&#x00A0;You are using SAP Netweaver 7.0 BI Usage Type Java. You expect a correction for a reported issue in SAP Netweaver Business Intelligence Usage Type Java. This note describes the new synchronized delivery process for Java corrections in BI Java area, valid for BI-BASE-S.SCA and BIWEBAPP.SCA.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Patch Delivery, Support Package Stack, Synchronized Patch Delivery, 2004s.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>&#x00A0;&#x00A0;BI JAVA Patches consist of two SCAs (BIBASES.SCA and BIWEBAPP.SCA). These SCAs can be deployed compatibly and without additional risk to the last three Support Package Stacks available.<br /><br />&#x00A0;&#x00A0;If the highest Support Package Stack available is SPS x (as an example: SPS 16), then for the lower Support Package Stacks SPS x-1 and SPS x-2 (= SPS 15 and 14) there will be no separate shipment of patches anymore. Both will be delivered with the shipment of patches on SPS x (SPS 16 in the example).<br /><br />&#x00A0;&#x00A0;For additional information, please see https://www.sdn.sap.com/irj/sdn/index?rid=/webcontent/uuid/6010addf-e096-2b10-8893-e98586d005f9<br /><br />&#x00A0;&#x00A0;Please be aware that the patch numbering changes slightly, to make room for delivery during production down scenarios (Hotfix delivery). Previously, the patch 5 would have been labeled \"5\", now its technical name is multiplied by 10; so patch 5 will be labeled \"Patch 50\", patch 15 will become \"150\". This is necessary so that hotfixes can be labeled in sequential order with the patches: the first&#x00A0;&#x00A0;Hotfix for patch 50 would then be labeled \"51\", the second one \"52\", etc. This starts with the delivery of SPS 16 Patch 30.<br /><br />&#x00A0;&#x00A0;Further details on Hotfix delivery can be found in note 1234019.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>&#x00A0;&#x00A0;Patches no longer need to be provided specifically for Support Package Stack codelines. Instead, they can be applied compatibly for the last three Support Package Stacks (SPS &gt;= 14). The specific patches, from which on this is valid, are: SPS 14 Patch 5, SPS 15 Patch 2, and SPS 16 Patch 0 (initial delivery). So SPS 14 Patch 5 is identical with SPS 15 Patch 2 and SPS 16 Patch 0, all containing the same corrections and only corrections (no new features). Patches can only be applied compatibly within one release, Netweaver BI 7.0 requires different Patches than Netweaver BI 7.1 or 7.2; the same is true for Enhancement Packages.<br /><br />&#x00A0;&#x00A0;Only the highest Support Package Stack on each release will be provided with patches. Applying higher BI JAVA patches to a lower SPS is only recommended and supported for the last three SPS (SPS &gt;=14).<br />This is valid starting with SPS 16 Patch 30 (according to previous naming conventions: SPS 16 Patch 03), which replaces the delivery of SPS 15 Patch 5(0) and SPS 16 Patch 8(0). In other words, SPS 16 Patch 30 follows the delivery of SPS 16 Patch 02, SPS 15 Patch 04 and SPS 14 Patch 07.<br /><br /> <B>The synchronized patch delivery strategy has several advantages</B>:<br /><br />SAP recommends to have the ABAP and Java parts of the server on the same Support Package Stack level. For BI, however, the tight dependency between ABAP and Java Support Package Stacks, which had to be updated in parallel, no longer exists. The BI-specific Patches can be applied compatibly to a different Support Package Stack. Note that this is not true for upgrades (like for instance from Netweaver 7.0 to 7.1), it only applies within the same release and enhancement package level.<br /><br />Updates on BI ABAP and BI Java can be done independently and more flexibly. This helps to reduce the total cost of operations, especially for updates and Patch testing. There is a possibility to provide Hotfixes separately and faster than in the past (see note 1234019). The delivery schedule of Java Patches is more reliable. The corrections included in Patches which are delivered according to this strategy are listed in the note 1033246. This note contains also the planned delivery dates.<br /><br /><B>Important to note in case of SPS &gt; SPS 16 Patch 20:</B><br /><br />1. New features are implemented not in Support Package Stacks (SPS) anymore, but in Enhancement Packages (EhP) only.<br /><br />Consequently, Java Support Package Stacks and Java Patches contain only corrections for issues reported by customers or internal tests. The last feature development in BI 7.0 has been delivered with SPS 14.<br /><br />2. Also previously, all corrections from a lower Support Package Stack number have also been provided on all higher Support Package Stacks. These corrections have been delivered as part of Java Patches on the respective Support Package Stacks. This has been done to avoid regressions after an upgrade from a lower Support Package Stack (but late Patch level) to a higher Support Package Stack, if the errors had been fixed after the initial release of the higher Support Package Stack.<br /><br />3. Consequently, SPS 14 (on a certain Patch level) has contained the identical code as SPS 15 (on another Patch level number) or SPS 16 (on yet another Patch level number). So it is only logical to stop delivering the lower Support Package Stack Patches, and only provide the Patch on the highest SPS.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BEX-ET-WJR-GRAPH (Charts and Maps)"}, {"Key": "Other Components", "Value": "BW-BEX-ET-WJR-EP (Enterprise Portal Integration)"}, {"Key": "Other Components", "Value": "BW-BEX-ET-WJR-DIA-FM (Local Formula Dialog)"}, {"Key": "Other Components", "Value": "BW-BEX-ET-WJR-DIA-VA (Variables Screen)"}, {"Key": "Other Components", "Value": "BW-BEX-ET-WJR-RT (Web Runtime and API commands)"}, {"Key": "Other Components", "Value": "BW-BEX-ET-WJR-ITM-AN (Analysis Item)"}, {"Key": "Other Components", "Value": "BW-BEX-ET-WJR-DIA-SL (Selector Dialog)"}, {"Key": "Other Components", "Value": "BW-BEX-ET-WJR-DIA-EX (Exception Dialog)"}, {"Key": "Other Components", "Value": "BW-BEX-ET-WJR-BICS (BI Consumer Services)"}, {"Key": "Other Components", "Value": "BW-BEX-ET-WJR-DIA-OS (Open Save Dialog)"}, {"Key": "Other Components", "Value": "BW-BEX-ET-WJR-DIA-CO (Conditions Dialog)"}, {"Key": "Other Components", "Value": "BW-BEX-ET-WJR-ITM (Web Items (runtime))"}, {"Key": "Other Components", "Value": "BW-BEX-ET-WJR-ANL (BEx Web Analyzer)"}, {"Key": "Other Components", "Value": "BW-BEX-ET-WJR-EXP (Export Services and Printing)"}, {"Key": "Other Components", "Value": "BW-BEX-ET-WJR-ODOC (Document Integration)"}, {"Key": "Other Components", "Value": "BW-BEX-ET-WJR-DIA (Dialogs and Dialogs Framework)"}, {"Key": "Other Components", "Value": "BW-BEX-ET-WJR-ITM-FL (Filter Items)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I055559)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I034467)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001163789/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001163789/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001163789/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001163789/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001163789/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001163789/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001163789/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001163789/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001163789/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1645590", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Java/Server SPs dependencies (and SupportDeskTool)", "RefUrl": "/notes/1645590"}, {"RefNumber": "1506722", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Generic Note for BI Java Patches and Support Packages", "RefUrl": "/notes/1506722"}, {"RefNumber": "1436483", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Display documents on restricted hierarchy is very slow", "RefUrl": "/notes/1436483"}, {"RefNumber": "1420426", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Some files not being broadcasted to the portal", "RefUrl": "/notes/1420426"}, {"RefNumber": "1408055", "RefComponent": "BW-BEX-ET-WJR-ITM-FL", "RefTitle": "Back one step navigation does not work after dropdown select", "RefUrl": "/notes/1408055"}, {"RefNumber": "1403925", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Infofield item ONLY_VALUES = X ignored for PDF export", "RefUrl": "/notes/1403925"}, {"RefNumber": "1368791", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 3 for BI JAVA 7.00 SP19 Patch 10 (PL 13)", "RefUrl": "/notes/1368791"}, {"RefNumber": "1313800", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NetWeaver 7.0: Exceptional BI JAVA Patches for SPS 14", "RefUrl": "/notes/1313800"}, {"RefNumber": "1310755", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 1 for BI JAVA 7.00 SP18 Patch 20 (PL21)", "RefUrl": "/notes/1310755"}, {"RefNumber": "1234019", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NetWeaver 7.0 Java Hotfix delivery process", "RefUrl": "/notes/1234019"}, {"RefNumber": "1174565", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web templates: JavaScript error in Enterprise Portal", "RefUrl": "/notes/1174565"}, {"RefNumber": "1033246", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NW 7.0 (NW04s): BI Java Synchronized Patch Delivery", "RefUrl": "/notes/1033246"}, {"RefNumber": "1033245", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NetWeaver 2004s BI Java SPS 15 Patch Delivery Schedule", "RefUrl": "/notes/1033245"}, {"RefNumber": "1033244", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NetWeaver 2004s BI Java SPS 14 Patch Delivery Schedule", "RefUrl": "/notes/1033244"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2116355", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NW7.0 SPS 32 Patch 20 note for BI Java", "RefUrl": "/notes/2116355 "}, {"RefNumber": "2166820", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NW7.0 SPS32 Patch 30 and SPS33 Patch 10 note for BI Java", "RefUrl": "/notes/2166820 "}, {"RefNumber": "2054317", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NW7.0 SPS31 Patch 30 and SPS 32 Patch 10 note for BI Java", "RefUrl": "/notes/2054317 "}, {"RefNumber": "1033246", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NW 7.0 (NW04s): BI Java Synchronized Patch Delivery", "RefUrl": "/notes/1033246 "}, {"RefNumber": "1645590", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Java/Server SPs dependencies (and SupportDeskTool)", "RefUrl": "/notes/1645590 "}, {"RefNumber": "1506722", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Generic Note for BI Java Patches and Support Packages", "RefUrl": "/notes/1506722 "}, {"RefNumber": "1390538", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "TEMPL Hotfix [Z] for BI JAVA 7.01 SP[XX] Patch [YY] (PL[YZ])", "RefUrl": "/notes/1390538 "}, {"RefNumber": "1290229", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "TEMPL Hotfix [Z] for BI JAVA 7.00 SP[XX] Patch [YY] (PL[YZ])", "RefUrl": "/notes/1290229 "}, {"RefNumber": "1408055", "RefComponent": "BW-BEX-ET-WJR-ITM-FL", "RefTitle": "Back one step navigation does not work after dropdown select", "RefUrl": "/notes/1408055 "}, {"RefNumber": "1403925", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Infofield item ONLY_VALUES = X ignored for PDF export", "RefUrl": "/notes/1403925 "}, {"RefNumber": "1436483", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Display documents on restricted hierarchy is very slow", "RefUrl": "/notes/1436483 "}, {"RefNumber": "1464785", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 1 for BI JAVA 7.01 SP06 Patch 20 (PL21)", "RefUrl": "/notes/1464785 "}, {"RefNumber": "1457014", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 1 for BI JAVA 7.00 SP 21 Patch 20 (PL21)", "RefUrl": "/notes/1457014 "}, {"RefNumber": "1445900", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 1 for BI JAVA 7.01 SP06 Patch 10 (PL11)", "RefUrl": "/notes/1445900 "}, {"RefNumber": "1445781", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 1 for BI JAVA 7.00 SP21 Patch 10 (PL11)", "RefUrl": "/notes/1445781 "}, {"RefNumber": "1421102", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 2 for BI JAVA 7.01 SP05 Patch 20 (PL22)", "RefUrl": "/notes/1421102 "}, {"RefNumber": "1420426", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Some files not being broadcasted to the portal", "RefUrl": "/notes/1420426 "}, {"RefNumber": "1418496", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 1 for BI JAVA 7.01 SP05 Patch 20 (PL21)", "RefUrl": "/notes/1418496 "}, {"RefNumber": "1415955", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 1 for BI JAVA 7.00 SP20 Patch 20 (PL21)", "RefUrl": "/notes/1415955 "}, {"RefNumber": "1409970", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 3 for BI JAVA 7.00 SP20 Patch 10 (PL 13)", "RefUrl": "/notes/1409970 "}, {"RefNumber": "1402120", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 2 for BI JAVA 7.00 SP20 Patch 10 (PL12)", "RefUrl": "/notes/1402120 "}, {"RefNumber": "1395758", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 1 for BI JAVA 7.00 SP20 Patch 10 (PL11)", "RefUrl": "/notes/1395758 "}, {"RefNumber": "1373118", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 1 for BI JAVA 7.00 SP19 Patch 20 (PL 1)", "RefUrl": "/notes/1373118 "}, {"RefNumber": "1368791", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 3 for BI JAVA 7.00 SP19 Patch 10 (PL 13)", "RefUrl": "/notes/1368791 "}, {"RefNumber": "1362493", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 2 for BI JAVA 7.00 SP19 Patch 10 (PL 12)", "RefUrl": "/notes/1362493 "}, {"RefNumber": "1361005", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NEW TEMPLATE: SAP NW BI JAVA 7.01 / 7.11", "RefUrl": "/notes/1361005 "}, {"RefNumber": "1360746", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BI Java corrections - Support Packages / Patches / Hotfixes", "RefUrl": "/notes/1360746 "}, {"RefNumber": "1360300", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 1 for BI JAVA 7.00 SP19 Patch 10 (PL11)", "RefUrl": "/notes/1360300 "}, {"RefNumber": "1344192", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Released-Hotfix 6 for BI JAVA 7.00 SP18 Patch 40 (PL46)", "RefUrl": "/notes/1344192 "}, {"RefNumber": "1333322", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Released-Hotfix 3 for BI JAVA 7.00 SP18 Patch 40 (PL 43)", "RefUrl": "/notes/1333322 "}, {"RefNumber": "1330306", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 2 for BI JAVA 7.00 SP18 Patch 40 (PL 42)", "RefUrl": "/notes/1330306 "}, {"RefNumber": "1325392", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 1 for BI JAVA 7.00 SP18 Patch 40 (PL41)", "RefUrl": "/notes/1325392 "}, {"RefNumber": "1313800", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NetWeaver 7.0: Exceptional BI JAVA Patches for SPS 14", "RefUrl": "/notes/1313800 "}, {"RefNumber": "1310755", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 1 for BI JAVA 7.00 SP18 Patch 20 (PL21)", "RefUrl": "/notes/1310755 "}, {"RefNumber": "1296973", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 1 for BI JAVA 7.00 SP18 Patch 10 (PL11)", "RefUrl": "/notes/1296973 "}, {"RefNumber": "1290232", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 3 for BI JAVA 7.00 SP17 Patch20 (PL23)", "RefUrl": "/notes/1290232 "}, {"RefNumber": "1290231", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hotfix 2 for BI JAVA 7.00 SP17 Patch20 (PL22)", "RefUrl": "/notes/1290231 "}, {"RefNumber": "1174565", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web templates: JavaScript error in Enterprise Portal", "RefUrl": "/notes/1174565 "}, {"RefNumber": "1234019", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NetWeaver 7.0 Java Hotfix delivery process", "RefUrl": "/notes/1234019 "}, {"RefNumber": "1033244", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NetWeaver 2004s BI Java SPS 14 Patch Delivery Schedule", "RefUrl": "/notes/1033244 "}, {"RefNumber": "1033245", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NetWeaver 2004s BI Java SPS 15 Patch Delivery Schedule", "RefUrl": "/notes/1033245 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "701", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "710", "To": "711", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}