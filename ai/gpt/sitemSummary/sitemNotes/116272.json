{"Request": {"Number": "116272", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 469, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014594322017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000116272?language=E&token=C1B79EEA58132D88B213F782DD03DF0E"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000116272", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000116272/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "116272"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 25}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.01.2002"}, "SAPComponentKey": {"_label": "Component", "value": "MM-IV"}, "SAPComponentKeyText": {"_label": "Component", "value": "Invoice Verification"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Invoice Verification", "value": "MM-IV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-IV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "116272 - Replacing MR01/functions of MR1M (4.5B)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Conventional invoice verification (Transaction MR01), including all functions in that environment (see below), is replaced by logistics invoice verification (Transaction MR1M) including the environment. To be able to replace Transaction MR01, the logistics invoice verification that was delivered for the first time in Release 3.0 was enhanced considerably in Releases 4.0 and 4.5, and was completed in Release 4.6C.<br />It meanwhile also supports considerably more functions in addition to being easier to operate. Conventional and logistics invoice verification can be used together.<br /><br /><br />New developments in logistics invoice verification, not supported in conventional invoice verification.<br />(Status of this note is Release 4.5B)<br /></p> <OL>1. Easy to operate</OL> <OL>2. Enhanced allocation possibilities (no restriction in the number of purchase orders/delivery notes/service sheets)</OL> <OL>3. No restriction in the number of the invoice line items</OL> <OL>4. Cross-company code postings</OL> <OL>5. Settlement of blanket purchase orders</OL> <OL>6. Settlement of invoicing plans</OL> <OL>7. Enhanced ERS function (option 4: settlement by material document)</OL> <OL>8. Enhanced withholding tax handling</OL> <OL>9. The material ledger can be used.</OL> <OL>10. Transfer prices are supported.</OL> <OL>11. Invoice verification in the background</OL> <OL>12. Automatic invoice reduction</OL> <OL>13. Conditions</OL> <OL>14. Processing of returns</OL> <OL>15. Enhanced specification of bank/payment data</OL> <OL>16. Enhanced purchase account management</OL> <OL>17. Unplanned delivery costs can be posted to separate posting lines on separate accounts.</OL> <OL>18. Minor differences also possible for \"Calculate tax\"</OL> <OL>19. GR/IR zero lines are suppressed.</OL> <OL>20. Installment terms of payment are supported.</OL> <OL>21. The stock at the time of posting is stored in the document.</OL> <OL>22. The entry profile makes the handling easier.</OL> <OL>23. Separate exchange rate for taxes can be entered.</OL> <OL>24. Foreign currency translation factors from the purchase order can be changed in the invoice.</OL> <OL>25. Generic object services are connected.</OL> <OL>26. Korea-specific enhancements are available.</OL> <OL>27. GR/IR account can be managed in several currencies.</OL> <OL>28. Creation of new account assignments is possible.</OL> <OL>29. Settlement with lower-level items (for example, discount in kind)</OL> <OL>30. Processing of Retail materials: generic materials, displays, prepacks, sales sets, and value-only materials.</OL> <OL>31. Support of Open FI</OL> <p><br />Still existing disadvantages of logistics invoice verification as compared to the conventional invoice verification are:</p> <OL>1. Direct postings to G/L accounts and assets are only possible via the blanket purchase order.</OL> <OL>2. Direct postings to the material are not possible.</OL> <OL>3. One-time vendors are not supported (supported as of Release 4.6).</OL> <OL>4. Only a restricted parking of documents is available.</OL> <OL>5. No notes can be entered (supported as of Release 4.6C).</OL> <OL>6. No multiple specification of the \"subsequent debit\" indicator is possible.</OL> <OL>7. Workflow is not generally supported, workflow only available within ArchiveLink (supported as of Release 4.6).</OL> <OL>8. The automatic clearing of the vendor line items during cancellation is not supported.</OL> <OL>9. Item texts cannot be entered.</OL> <OL>10. Tax code proposal is not separated according to domestic vendors and vendor abroad.</OL> <OL>11. Selecting by shipment cost documents is not possible (supported as of Release 4.6).</OL> <OL>12. No entry of investment indicator (XINVE)<br />(supported in Transaction MIRO as of Release 4.6)</OL> <OL>13. Selection with reference to the goods receipt document</OL> <p><br />Please provide us with practical examples of functions that are missing in this list and can be executed with the conventional invoice verification but not with logistics invoice verification. This will help us with the enhancement of the latter.<br /><br /><br />A phase of restricted preventive maintenance precedes the replacement of the conventional invoice verification. This means that program changes are only carried out for serious problems. Enhancements and new developments will only be carried out for logistics invoice verification.<br /><br />Together with the conventional invoice verification, all functions in the environment are also replaced, among other things:</p> <UL><UL><LI>MRHR, MRHG, MR03,</LI></UL></UL> <UL><UL><LI>Parked documents (Transactions MR41 - MR44)</LI></UL></UL> <UL><UL><LI>Cancellation (Transaction MR08)</LI></UL></UL> <UL><UL><LI>Release of invoice items (Transaction MR02)</LI></UL></UL> <UL><UL><LI>Evaluated receipt settlement (Transaction MRRS)</LI></UL></UL> <UL><UL><LI>Inbound EDI message (IDOC_INPUT_INVOIC_MM)</LI></UL></UL> <UL><UL><LI>Message processing (MR91)</LI></UL></UL> <p></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "MR1M"}, {"Key": "Transaction codes", "Value": "MR01"}, {"Key": "Transaction codes", "Value": "MRHR"}, {"Key": "Transaction codes", "Value": "SAMT"}, {"Key": "Transaction codes", "Value": "MR44"}, {"Key": "Transaction codes", "Value": "MRHG"}, {"Key": "Transaction codes", "Value": "MR08"}, {"Key": "Transaction codes", "Value": "MR02"}, {"Key": "Transaction codes", "Value": "MR41"}, {"Key": "Transaction codes", "Value": "MRRS"}, {"Key": "Transaction codes", "Value": "MR03"}, {"Key": "Transaction codes", "Value": "OB52"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023473)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D023473)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000116272/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000116272/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000116272/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000116272/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000116272/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000116272/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000116272/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000116272/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000116272/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "402655", "RefComponent": "MM-IV-FIV-CAN", "RefTitle": "MR08 : Error M8135 occurs.", "RefUrl": "/notes/402655"}, {"RefNumber": "205311", "RefComponent": "FI-AP-AP-Q", "RefTitle": "Extended withholding tax: Error 7Q328 or F5787", "RefUrl": "/notes/205311"}, {"RefNumber": "144081", "RefComponent": "MM-IV", "RefTitle": "Replacing MR01/functions of MR1M (Release 4.6)", "RefUrl": "/notes/144081"}, {"RefNumber": "127366", "RefComponent": "MM-IV", "RefTitle": "Replacing MR01/Functions of MR1M (Release 4.0B)", "RefUrl": "/notes/127366"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "144081", "RefComponent": "MM-IV", "RefTitle": "Replacing MR01/functions of MR1M (Release 4.6)", "RefUrl": "/notes/144081 "}, {"RefNumber": "127366", "RefComponent": "MM-IV", "RefTitle": "Replacing MR01/Functions of MR1M (Release 4.0B)", "RefUrl": "/notes/127366 "}, {"RefNumber": "205311", "RefComponent": "FI-AP-AP-Q", "RefTitle": "Extended withholding tax: Error 7Q328 or F5787", "RefUrl": "/notes/205311 "}, {"RefNumber": "402655", "RefComponent": "MM-IV-FIV-CAN", "RefTitle": "MR08 : Error M8135 occurs.", "RefUrl": "/notes/402655 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}