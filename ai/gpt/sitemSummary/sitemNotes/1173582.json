{"Request": {"Number": "1173582", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 470, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000007087392017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001173582?language=E&token=140F4891E91C20CC951E21AB8BFB72CC"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001173582", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001173582/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1173582"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.09.2009"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CTS-TLS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Transport Tools"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Change and Transport System", "value": "BC-CTS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CTS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Transport Tools", "value": "BC-CTS-TLS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CTS-TLS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1173582 - Error occurs during export: invalid value '...' in E071.LANG"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>During an export, the system issues the error message<br />invalid language '...' in E071.LANG (as4pos = ...)<br />The error means that the transport request to be exported contains an invalid entry.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This invalid entry is caused by an error in an internal function module that is used to create transport requests from the view maintenance. &#x00A0;&#x00A0;&#x00A0;&#x00A0; This is an old error that has remained undetected for a long time.<br />This error message is caused by a new check in the transport program R3trans, which was introduced in the version from April 16th, 2008.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><UL><LI>Short-term solution A:<br />Repeat the export using the R3trans version from May 28, 2008. During the export of sales orders, the check is eased and this means that the system issues only a warning.</LI></UL> <UL><LI>You can implement the short-term solution B, only if the short-term solution A does not solve the problem.<br />Debug the transport request. Attached to this note is the data file of transport request B20K095214, which you can import into the affected system in accordance with Note 13719.&#x00A0;&#x00A0; This transport request contains the correction program RSWBO_CLEANUP_LANGFLAGS.&#x00A0;&#x00A0; Execute this program to repair the invalid entries of the affected transport request.</LI></UL> <UL><LI>Medium-term solution:<br />We are currently preparing an R3trans patch in which this check is less stringent so that only warnings are displayed (usually).&#x00A0;&#x00A0;However, it is a good idea to debug affected transport requests so that you avoid problems when you use new import features (import with parallel processes) in future.</LI></UL> <UL><LI>Long-term solution:<br />We are also preparing the correction of the internal function mentioned before in order to prevent invalid entries of this type from being generated in transport requests in the future.</LI></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-CTS-ORG (Workbench/Customizing Organizer)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D000706)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D000706)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001173582/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001173582/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173582/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173582/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173582/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173582/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173582/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173582/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173582/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "B20K095214.zip", "FileSize": "5", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000277702008&iv_version=0008&iv_guid=A4A1FC03FD59CB4186714A82F82409A1"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "19466", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Downloading SAP kernel patches", "RefUrl": "/notes/19466"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1251866", "RefComponent": "BC-DWB-CEX-BAD", "RefTitle": "Filter characteristics not transported", "RefUrl": "/notes/1251866"}, {"RefNumber": "1245525", "RefComponent": "BC-CTS-ORG", "RefTitle": "Table key is not transferred to request", "RefUrl": "/notes/1245525"}, {"RefNumber": "1127194", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans import with parallel processes", "RefUrl": "/notes/1127194"}, {"RefNumber": "1074030", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: \"Duplicate key\" composite SAP Note (as of Release 6.10)", "RefUrl": "/notes/1074030"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1127194", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans import with parallel processes", "RefUrl": "/notes/1127194 "}, {"RefNumber": "1074030", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: \"Duplicate key\" composite SAP Note (as of Release 6.10)", "RefUrl": "/notes/1074030 "}, {"RefNumber": "1251866", "RefComponent": "BC-DWB-CEX-BAD", "RefTitle": "Filter characteristics not transported", "RefUrl": "/notes/1251866 "}, {"RefNumber": "1245525", "RefComponent": "BC-CTS-ORG", "RefTitle": "Table key is not transferred to request", "RefUrl": "/notes/1245525 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "620", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "711", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C57", "URL": "/supportpackage/SAPKB46C57"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62065", "URL": "/supportpackage/SAPKB62065"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64023", "URL": "/supportpackage/SAPKB64023"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70017", "URL": "/supportpackage/SAPKB70017"}, {"SoftwareComponentVersion": "SAP_BASIS 701", "SupportPackage": "SAPKB70101", "URL": "/supportpackage/SAPKB70101"}, {"SoftwareComponentVersion": "SAP_BASIS 710", "SupportPackage": "SAPKB71007", "URL": "/supportpackage/SAPKB71007"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 1, "URL": "/corrins/0001173582/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1245525", "RefTitle": "Table key is not transferred to request", "RefUrl": "/notes/0001245525"}]}}}}}