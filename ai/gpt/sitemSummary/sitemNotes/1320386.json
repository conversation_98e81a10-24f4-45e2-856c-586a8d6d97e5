{"Request": {"Number": "1320386", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 604, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016758492017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001320386?language=E&token=7DC1184978D34D808873CB9B3C13C4E0"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001320386", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001320386/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1320386"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.12.2009"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-ISR"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW only - Retail and Consumer Products"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Retail and Consumer Products", "value": "BW-BCT-ISR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-ISR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1320386 - Frequently asked questions regarding BW Trade Foundation"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note gives answers to frequently asked questions in relation to the BW Trade Foundation and trade service classes.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Question: Which objects have been created and supplied in the context of the BW Trade Foundation?<br />Answer: The BW Trade Foundation in the Trade Business Content includes the following objects:<br /> Area&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Delivered Object&#x00A0;&#x00A0;Connected DataSource<br />-Goods Movements&#x00A0;&#x00A0; 0RSL_DS51&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 2LIS_03_BF<br />-Stock Revaluation 0RT_DS52&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2LIS_03_UM<br />-Ret. Revaluation&#x00A0;&#x00A0;0RT_DS53&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2LIS_40_REVAL<br />-Retail Sales&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0RT_DS54&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2LIS_13_VDITM, 0RT_PA_TRAN_CONTROL<br />-POS Markdown DSO&#x00A0;&#x00A0;0RT_DS55&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2LIS_13_VDITM, 0RT_PA_TRAN_CONTROL<br /><br />Question: Why is there a connection of sales data and stock data to the revaluation at retail DataStore objects 0RT_DS53 and 0RT_DS63 in the Trade Foundation?<br />Answer: The connection of the two areas via the InfoSource 0RT_REVAL_ADD to the DataStore objects 0RT_DS53 and 0RT_DS63 represents the requirements of \"Late Sales\" and \"Customer Returns\" in connection with a revaluation at retail carried out in the meantime. In late sales, the case is covered that POS data is loaded from the POS Inbound Processing Engine (PIPE) to ERP late and a revaluation at retail is already carried out in the meantime. In the case of customer returns, a return is carried out after a revaluation at retail has taken place. In both cases, the system tries to generate an artificial markdown/markup using the transformations in order to balance these variances. In general, both cases \"Late Sales\" and \"Customer Returns\" occur very rarely. However, due to the complex logic, they require a long runtime. Potentially, both processes can be removed from the data flow of the Foundation if you are not interested in this type of clearing document. You can delete the following transformations:<br />TRCS 0RT_REVAL_ADD -&gt; ODSO 0RT_DS53<br />TRCS 0RT_REVAL_ADD -&gt; ODSO 0RT_DS63<br />TRCS 0RT_PA_TRAN_CONTROL_TR -&gt; TRCS 0RT_REVAL_ADD<br />TRCS 2LIS_03_BF_TR -&gt; TRCS 0RT_REVAL_ADD<br /><br />Question: What happens when the structured articles are exploded and can the explosion of the structured articles be deactivated when not in use?<br />Answer: The explosion of the structured articles is a process that is relevant for the DataSources 2LIS_03_BF, 2LIS_03_BX, and 2LIS_02_SCL. Within a BAdI implementation (WRF_BWEXT_STRUKTART) of the general SAPI BAdI (RSU5_SAPI_BADI), depending on Customizing settings in the table TWZLA and depending on the article category and plant category, article documents are split into existing components of structured articles. This is a purely quantitative type of splitting. A value-based explosion of the structured articles is not carried out. As a result, depending on the settings in the table TWZLA, the number of data records may increase during the extraction. In SAP BW, you must make sure in the start routine of the respective InfoSource that only the header or exploded information reaches the data targets to avoid duplicate data. Note 846106 provides a more detailed description of this procedure. The function module RSBCT_RFASH_MATERIAL_EXPLO transfers the split to original documents and exploded documents. It is important to note that the explosion process is a performance-relevant process and should only be executed if you really want an explosion in SAP BW. If you do not want an explosion, you should check that the BAdI implementation WRF_BWEXT_STRUKTART is inactive and also whether the start routine in the transformations has been removed from the Data/InfoSources 2LIS_03_BF, 2LIS_03_BX, and 2LIS_02_SCL, since these are not relevant in the case of non-explosion.<br /><br />Question: For which areas are the \"Trade Foundation Key Figure Service Classes\", as they are known, supplied and where are they used?<br />Answer: The key figure classes within the Trade Foundation have been supplied in order to bundle all update logics in connection with the Trade Business Content and to provide better access. Specifically, separate service classes have been created for the following applications:<br />01 - Sales data<br />02 - Purchasing data<br />03 - Inventory management data<br />40 - Revaluation at retail<br />POS - Sales data at the register and<br />SL - Retail Stock Ledger.<br />Individual implementations exist for each of these classes that can be used to calculate the corresponding key figures for the respective area. In the Trade Business Content, the method CALCULATE_KEYFIGURE can be used to calculate the key figures. You can also calculate all key figures in an expert routine via the corresponding method CALCULATE_ALL_KEYFIGURES. The Trade Foundation service classes (level BI_CONT 705) are used in the POS area. Carry out the following steps to use the Trade Foundation service classes for applications such as purchasing or inventory management also. In the first step, the class /RTF/CL_KYF_TRANSFORM must be instantiated for the corresponding source-target application. This is done using the method INSTANCIATE_SERVICE_CLASS of the class /RTF/CL_KYF_TRANSFORM. In the second step, a routine can be created for a key figure whose logic you want to replace with Trade Foundation service classes - the method CALCULATE_KEYFIGURE is called in this routine. The method processes the appropriate business logic for the name of the key figure and returns a result. Refer to the SDN document \"Trade BI Foundation - How to Use Service Classes for Keyfigure Transformations\" for a more detailed technical description of the service class concept.<br /><br />Question: How do I connect the existing retail cubes (0RT_C35, 0R_C36, 0RT_C37, 0RT_C38, 0RT_C39) to the Trade Foundation and can the Trade Foundation key figure service classes be used for the retail cubes?<br />Answer: The Trade Foundation DataStore objects create a layer on which additional data targets (for example, the retail cubes 0RT_C35, 0RT_C36, and so on) can be used as a basis. New transformations must be created as a connection between the Foundation layer and the cubes. The new Trade Foundation service classes can be used in the transformations as described in the previous question/answer. As a result, it is no longer necessary to know the exact update logic of each individual key figure since the logic is already prepared and contained in the service classes.<br /><br />Question: Can or should the delivered DataStore objects be viewed as a \"Corporate Memory\" of an Enterprise Data Warehousing (EDW) data model?<br />Answer: The delivered objects should not be viewed as a Corporate Memory Layer in the delivered version. Different summarizations and derivations have already been made in the delivered transformations between the DataSource and the specified DataStore objects. This does not correspond with the definition of a Corporate Memory object according to the LSA concept (LSA = Layered Scalable Architecture). In this concept, Corporate Memory objects are characterized as follows:<br /></p> <UL><LI>Corporate Memory objects save ALL fields that are delivered from the underlying DataSource.</LI></UL> <UL><LI>Data is NOT aggregated.</LI></UL> <UL><LI>Corporate Memory objects are technically seen as \"write-optimized\" and must be defined without semantic keys.</LI></UL> <UL><LI>No transformations take place except for 1:1 updates with potentially required conversion exits.</LI></UL> <p><br />However, a Corporate Memory Layer can be connected to the delivered model in the customer namespace. However, such a layer should not be included directly in the data flow of the daily loading processes. In fact, the establishment of such a layer parallel to the delivered data flow is advised. Loading this layer can be temporally removed from the time-critical (analysis-relevant) loading processes. An advantage of this architecture is the option of saving granular movement data long-term without adversely affecting the time-critical loading processes. Due to the interconnection of the Corporate Memory objects with the analysis-relevant data flow, a high degree of flexibility with regard to changed analysis requirements may also be realized.<br /><br />Use the following link in the \"SAP Community Network\" (SCN) for additional information about the LSA:<br />http://www.sdn.sap.com/irj/scn/weblogs?blog=/pub/wlg/14313<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D037986)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D034433)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001320386/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001320386/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001320386/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001320386/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001320386/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001320386/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001320386/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001320386/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001320386/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "586163", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Composite SAP Note for SAP ERP Inventory Management in SAP BW", "RefUrl": "/notes/586163 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BI_CONT", "From": "703", "To": "703", "Subsequent": ""}, {"SoftwareComponent": "BI_CONT", "From": "704", "To": "704", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}