{"Request": {"Number": "1148345", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 439, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000006915892017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001148345?language=E&token=C28D2B7A6F2208CA51AF9EE00601E3F0"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001148345", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001148345/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1148345"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.07.2016"}, "SAPComponentKey": {"_label": "Component", "value": "PY-PT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Portugal"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Portugal", "value": "PY-PT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-PT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1148345 - New processing method for handicapped and dependents"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>A new law supporting IRS Functionality has been published.<br />According to the \"Despacho n&#186; 1157 - A/2008\", the handicapped dependents processing method and the taxation method have changed from 01.01.2008 on.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Dependents, handicapped, disability, PTX0, Despacho n&#186; 1157 - A/2008 HR_P_DETERMINE_HOUSEHOLD_DATA, dependentes, deficientes.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This change is required to keep the IRS functionality in accordance to the new law \"Despacho n&#186; 1157 - A/2008\" from 09.01.2008.<br /><br />According to the Despacho n.&#186; 1157- A/2008:<br /><br />&#160;&#160;a) Each dependent with a degree of permanent disability equal or<br />&#160;&#160;&#160;&#160; greater than 60% is equivalent, for the purposes of withholding<br />&#160;&#160;&#160;&#160; taxes, to four not handicapped dependents.<br />&#160;&#160;b) In married situation, if the spouse does not have income from<br />&#160;&#160;&#160;&#160; categories A or H, and the spouse has a degree of permanent<br />&#160;&#160;&#160;&#160; disability equal or greater than 60%, then he/she is equivalent,<br />&#160;&#160;&#160;&#160;for the purposes of tax retention, to five dependents not<br />&#160;&#160;&#160;&#160; disabled.<br />&#160;&#160;c) In married situation, if the spouse does not have income from<br />&#160;&#160;&#160;&#160; categories A or H, and the spouse has a degree of permanent<br />&#160;&#160;&#160;&#160; disability equal or greater than 60%, the tax retention rate of<br />&#160;&#160;&#160;&#160; the employee income of category H should be reduced by 1%.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>VERSIONING</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Version</strong></td>\r\n<td><strong>Date</strong></td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td>4</td>\r\n<td>July 20, 2016</td>\r\n<td>SAR files were removed.</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>To adjust the IRS functionality to the new requirements, the<br />following changes were made:<br /><br />&#160;&#160;o&#160;&#160;A new constant containing the index of <strong>handicapped dependents</strong><br />&#160;&#160;&#160;&#160; was created in table T511K with the following validities:<br />&#160;&#160;&#160;&#160;----------------------------------------------------------------<br />&#160;&#160;&#160;&#160;IMUDD|&#205;ndice para depen. deficientes |01.01.1900|31.12.2007|2,00<br />&#160;&#160;&#160;&#160; IMUDD|&#205;ndice para depen. deficientes |01.01.2008|31.12.9999|4,00<br />&#160;&#160;&#160;&#160; ----------------------------------------------------------------<br /><br />&#160;&#160;o&#160;&#160;A new constant containing the index of <strong>handicapped spouse</strong> was<br />&#160;&#160;&#160;&#160; created in table T511K with the following validities:<br />&#160;&#160;&#160;&#160; ----------------------------------------------------------------<br />&#160;&#160;&#160;&#160; IMUCD|&#205;ndice para c&#244;njuge deficiente |01.01.1900|31.12.2007|2,00<br />&#160;&#160;&#160;&#160; IMUCD|&#205;ndice para c&#244;njuge deficiente |01.01.2008|31.12.9999|5,00<br />&#160;&#160;&#160;&#160; ----------------------------------------------------------------<br /><br />&#160;&#160;o&#160;&#160;A new constant containing the <strong>rate reduction</strong> was created in<br />&#160;&#160;&#160;&#160; table T511K with the following validities:<br />&#160;&#160;&#160;&#160; ----------------------------------------------------------------<br />&#160;&#160;&#160;&#160; PRIRS|Perc. redu&#231;&#227;o IRS c&#244;njuge def. |01.01.1900|31.12.2007|0,00<br />&#160;&#160;&#160;&#160; PRIRS|Perc. redu&#231;&#227;o IRS c&#244;njuge def. |01.01.2008|31.12.9999|1,00<br />&#160;&#160;&#160;&#160; ----------------------------------------------------------------<br /><br />&#160;&#160;o&#160;&#160;The function module HR_P_DETERMINE_HOUSEHOLD_DATA was changed<br />&#160;&#160;&#160;&#160;to read these new constants and calculate the number of<br />&#160;&#160;&#160;&#160; dependents according to the law.<br /><br />&#160;&#160;o&#160;&#160;The function PTX0 of the payroll schema was changed to apply<br />&#160;&#160;&#160;&#160; the retention rate reduction according to employee conditions.</p>\r\n<p><strong>STEPS SUMMARY:</strong></p>\r\n<p>&#160;&#160;1. Install the corresponding Correction Instruction<br />&#160;&#160;2. Install the corresponding Advanced Delivery</p>\r\n<p><strong>ATTENTION:</strong></p>\r\n<p>The attached Advanced Delivery contais only the customization of the table T511K. To get the functionality running you have to install both, the Advanced Delivery and the Correction Instruction.</p>\r\n<p><strong>IMPORTANT:</strong></p>\r\n<p>Be aware of an Advance Delivery delivers the last version of the object, it means that if you do not have the last HR Support<br />Package installed in you system you could get errors, either<br />Syntax Errors or process errors. In this case the only option<br />is to undo the changes from the Advance Delivery and do the changes manually according to the Correction Instructions available in<br />this note.<br /><br />An Advanced Delivery is available in the attached files according to the following list(\"xxxxxx\" means numbers):<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;L7DKxxxxxx_600.CAR - Release 6.00<br />L6DKxxxxxx_500.CAR - Release 5.00<br />L6BKxxxxxx_470.CAR - Release 4.70(Enterprise)<br />L9CKxxxxxx_46C.CAR - Release 4.6C<br /><br />For more details about Advance Delivery installation procedure please<br />read the notes listed in \"Related Notes\".<br /><br />The correction described in this note will be included in an HR Support<br />Package, as indicated in item \"Reference to Support Packages\".<br /><br />The support package includes:<br /><br />&#160;&#160;- change in include RPCI97P0<br />&#160;&#160;- changes in function module HR_P_DETERMINE_HOUSEHOLD_DATA<br />&#160;&#160;- new constants and records in table T511K:<br />&#160;&#160;&#160;&#160;- IMUDD<br />&#160;&#160;&#160;&#160;- IMUCD<br />&#160;&#160;&#160;&#160;- PRIRS</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I813651)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I827735)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001148345/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001148345/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001148345/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001148345/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001148345/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001148345/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001148345/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001148345/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001148345/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1350000", "RefComponent": "PY-PT", "RefTitle": "IRS: Negative rates in the handicapped and dependents calc", "RefUrl": "/notes/1350000"}, {"RefNumber": "1245142", "RefComponent": "PY-PT", "RefTitle": "IRS: wrong counting of handicapped spouse for dependents", "RefUrl": "/notes/1245142"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "1350000", "RefComponent": "PY-PT", "RefTitle": "IRS: Negative rates in the handicapped and dependents calc", "RefUrl": "/notes/1350000 "}, {"RefNumber": "1245142", "RefComponent": "PY-PT", "RefTitle": "IRS: wrong counting of handicapped spouse for dependents", "RefUrl": "/notes/1245142 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46CD6", "URL": "/supportpackage/SAPKE46CD6"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47081", "URL": "/supportpackage/SAPKE47081"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50047", "URL": "/supportpackage/SAPKE50047"}, {"SoftwareComponentVersion": "SAP_HR 600", "SupportPackage": "SAPKE60030", "URL": "/supportpackage/SAPKE60030"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_HR", "NumberOfCorrin": 4, "URL": "/corrins/0001148345/2"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 2, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "470", "Number": "712368 ", "URL": "/notes/712368 ", "Title": "HR-PT: New table T5P1P for IRS since 2004", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "753015 ", "URL": "/notes/753015 ", "Title": "HR-PT:Previous years vacation & christ. allow. taxation", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "753015 ", "URL": "/notes/753015 ", "Title": "HR-PT:Previous years vacation & christ. allow. taxation", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "753015 ", "URL": "/notes/753015 ", "Title": "HR-PT:Previous years vacation & christ. allow. taxation", "Component": "PY-PT"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}