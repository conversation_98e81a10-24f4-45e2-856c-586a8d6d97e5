{"Request": {"Number": "791743", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 614, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000004645962017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000791743?language=E&token=38E8AF12C789741D05DABA3573E79ED9"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000791743", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000791743/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "791743"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Currentness", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.12.2004"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-CH-IS-H"}, "SAPComponentKeyText": {"_label": "Component", "value": "Hospital"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Switzerland", "value": "XX-CSC-CH", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-specific component", "value": "XX-CSC-CH-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hospital", "value": "XX-CSC-CH-IS-H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH-IS-H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "791743 - IS-H CH: ELACH/MEDIDATA - VAT rates"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=791743&TargetLanguage=EN&Component=XX-CSC-CH-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/791743/D\" target=\"_blank\">/notes/791743/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This SAP Note is relevant only for the country version Switzerland (CH).<br />In the course of the EDI procedure ELACH (MediData), only the creation of the ISR number could previously be influenced in the context of the BAdI ISH_CH_ELACH.<br />Only the official VAT rates 2.4%, 3, 6%, 4.6%, 6.2%, 7.6% and 9.3% are currently taken into account.<br />The flat-rate VAT rates 0.1%, 0.6%, 1.2%, 2.3%, 3.5%, 5.2%, and 6.0% are not taken into account, which means that there was no such thing in the XML file for items with these VAT rates.</p><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>ELACH, MEDIDATA, VAT records, tax, BAdI, ish_ch_elach</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>See symptom.</p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><p>Unpack the attached file for the relevant IS-H version and only then implement the attached corrections in your system.<br />As a result, an additional method with the description CHANGE_XML_DATA is available in the old BAdI. This method (the BAdI) is called immediately before the final XML file creation and already has all XML fields available. You can now change the contents of existing fields, remove fields, or include new structures. Note, however, that the schema of the file must therefore remain correct.<br />The old callpoint of the BAdI with the corresponding method for the possible change of the ISR number is retained.<br />In addition, the flat-rate tax rates mentioned are also taken into account. If the internal flat-rate tax rates are not to be transferred, you must replace them using the BAdI that is now available.<br />CAUTION:<br />Before you implement the correction instructions of this SAP Note, implement the attached attachment as follows:<br />Unpack the attached file HW791743_463B.ZIP for IS-H Version 463B, HW791743_471.ZIP for IS-H Version 471, or HW791743_472.ZIP for IS-H Version 4.72.<br />              Note that you cannot download the attached files using OSS, but only from SAP Service Marketplace (see also SAP Notes 480180 and 13719 for information about importing attachments). <OL>1. Import the unpacked requests into your system.</OL> <OL>2. Now implement the correction instructions contained in this SAP Note in your system.</OL> <p><br />Afterwards, you must make the following manual changes in the program RNWCHMEDIDATA_EDI_TO_XML:</p> <UL><LI>Call transaction SE80.</LI></UL> <UL><LI>In the input help of the upper of the two input fields, choose the program. In the lower input field, enter the name of the program RNWCHMEDIDATA_EDI_TO_XML.</LI></UL> <UL><LI>Confirm your entry.</LI></UL> <UL><LI>Double-click the object name RNWCHMEDIDATA_EDI_TO_XML.</LI></UL> <UL><LI>In the menu   , choose &quot;Goto -> Text Elements -> Text Symbols&quot;.</LI></UL> <UL><LI>Choose &quot;Change&quot;.</LI></UL> <UL><LI>Now add the new text symbol &quot;028&quot; with the text &quot;ERROR - XML parent not found :&quot;.</LI></UL> <UL><LI>Activate your change.</LI></UL></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Owner                                                                                    ", "Value": "<PERSON> (C5025082)"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON> (C5025082)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000791743/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "HW791743_472.ZIP", "FileSize": "24", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200001592752004&iv_version=0002&iv_guid=3717061C16D9A245B6E656AFE573791F"}, {"FileName": "HW791743_471.ZIP", "FileSize": "22", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200001592752004&iv_version=0002&iv_guid=A7166AAF59C0AA4A837CDE5746D62EE7"}, {"FileName": "HW791743_463B.ZIP", "FileSize": "17", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200001592752004&iv_version=0002&iv_guid=BB0E2EECF124DB48B105E29F6598DCC4"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "IS-H", "From": "463B", "To": "463B", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "471", "To": "471", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "472", "To": "472", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": [{"SoftwareComponentVersion": "IS-H 463B", "SupportPackage": "SAPKIPHD25", "URL": "/supportpackage/SAPKIPHD25"}, {"SoftwareComponentVersion": "IS-H 471", "SupportPackage": "SAPKIPHE14", "URL": "/supportpackage/SAPKIPHE14"}, {"SoftwareComponentVersion": "IS-H 472", "SupportPackage": "SAPKIPHF08", "URL": "/supportpackage/SAPKIPHF08"}]}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 3, "URL": "/corrins/0000791743/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 14, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "532524 ", "URL": "/notes/532524 ", "Title": "IS-H CH: MEDIDATA V3.0 - Various Changes", "Component": "IS-H-CM-OUT"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "691711 ", "URL": "/notes/691711 ", "Title": "IS-H CH: ELACH - Media Data Adjustments", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "695454 ", "URL": "/notes/695454 ", "Title": "IS-H CH: ELACH - Medidata - VAT Records and Send Control File", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "706489 ", "URL": "/notes/706489 ", "Title": "IS-H CH: ELACH - Medidata - Control File, Docu. Sent, ...", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "714966 ", "URL": "/notes/714966 ", "Title": "IS-H CH: ELACH - Medidata - File Length in SendControlFile", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "734538 ", "URL": "/notes/734538 ", "Title": "IS-H CH: ELACH/MEDIDATA - Reference Digit", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "738550 ", "URL": "/notes/738550 ", "Title": "IS-H CH: ELACH/MEDIDATA - Commingled Item Numbers", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "751200 ", "URL": "/notes/751200 ", "Title": "IS-H CH: ELACH/MEDIDATA - Current Adjustments", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "752980 ", "URL": "/notes/752980 ", "Title": "IS-H CH: ELACH/MEDIDATA - Leading Zeros - Service Code", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "774257 ", "URL": "/notes/774257 ", "Title": "IS-H CH: ELACH/MEDIDATA - Data After End of XML File", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "781907 ", "URL": "/notes/781907 ", "Title": "IS-H CH: ELACH - Determine Treatment Reason from Admission Type", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "789794 ", "URL": "/notes/789794 ", "Title": "IS-H CH: ELACH/MEDIDATA - Alternative Service Text", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "471", "ValidTo": "471", "Number": "691711 ", "URL": "/notes/691711 ", "Title": "IS-H CH: ELACH - Media Data Adjustments", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "471", "ValidTo": "471", "Number": "695454 ", "URL": "/notes/695454 ", "Title": "IS-H CH: ELACH - Medidata - VAT Records and Send Control File", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "471", "ValidTo": "471", "Number": "706489 ", "URL": "/notes/706489 ", "Title": "IS-H CH: ELACH - Medidata - Control File, Docu. Sent, ...", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "471", "ValidTo": "471", "Number": "714966 ", "URL": "/notes/714966 ", "Title": "IS-H CH: ELACH - Medidata - File Length in SendControlFile", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "471", "ValidTo": "471", "Number": "734538 ", "URL": "/notes/734538 ", "Title": "IS-H CH: ELACH/MEDIDATA - Reference Digit", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "471", "ValidTo": "471", "Number": "738550 ", "URL": "/notes/738550 ", "Title": "IS-H CH: ELACH/MEDIDATA - Commingled Item Numbers", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "471", "ValidTo": "471", "Number": "751200 ", "URL": "/notes/751200 ", "Title": "IS-H CH: ELACH/MEDIDATA - Current Adjustments", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "471", "ValidTo": "471", "Number": "752980 ", "URL": "/notes/752980 ", "Title": "IS-H CH: ELACH/MEDIDATA - Leading Zeros - Service Code", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "471", "ValidTo": "471", "Number": "774257 ", "URL": "/notes/774257 ", "Title": "IS-H CH: ELACH/MEDIDATA - Data After End of XML File", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "471", "ValidTo": "471", "Number": "789794 ", "URL": "/notes/789794 ", "Title": "IS-H CH: ELACH/MEDIDATA - Alternative Service Text", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "734538 ", "URL": "/notes/734538 ", "Title": "IS-H CH: ELACH/MEDIDATA - Reference Digit", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "738550 ", "URL": "/notes/738550 ", "Title": "IS-H CH: ELACH/MEDIDATA - Commingled Item Numbers", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "751200 ", "URL": "/notes/751200 ", "Title": "IS-H CH: ELACH/MEDIDATA - Current Adjustments", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "752980 ", "URL": "/notes/752980 ", "Title": "IS-H CH: ELACH/MEDIDATA - Leading Zeros - Service Code", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "774257 ", "URL": "/notes/774257 ", "Title": "IS-H CH: ELACH/MEDIDATA - Data After End of XML File", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "778263 ", "URL": "/notes/778263 ", "Title": "IS-H CH: ELACH/MEDIDATA - Rounding to 5 centimes", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "789794 ", "URL": "/notes/789794 ", "Title": "IS-H CH: ELACH/MEDIDATA - Alternative Service Text", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "790822 ", "URL": "/notes/790822 ", "Title": "IS-H CH: ELACH/MEDIDATA - Negative Rounding Service", "Component": "XX-CSC-CH-IS-H"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=791743&TargetLanguage=EN&Component=XX-CSC-CH-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/791743/D\" target=\"_blank\">/notes/791743/D</a>."}}}}