{"Request": {"Number": "917987", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 299, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016046782017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000917987?language=E&token=2A524799456EFAC82E2D92C6BA7FB699"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000917987", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000917987/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "917987"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.08.2009"}, "SAPComponentKey": {"_label": "Component", "value": "LO-VC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Variant Configuration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Logistics - General", "value": "LO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Variant Configuration", "value": "LO-VC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO-VC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "917987 - General performance in variant configuration"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Performance is not optimal in variant configuration and long runtimes occur either during the actual value assignment or when you go to the configuration. The aim of this note is to support the user during analysis to find out in which area of the configuration model runtime improvements can be achieved or how to avoid characteristic errors when structuring the configuration model. As a general note, however, some of the unsatisfactory modelings shown here do not necessarily have a negative effect on performance and the overall modeling must always be taken into account during the analysis. On the other hand, even if you pay attention to all the criteria listed below, you cannot expect extremely fast runtimes (of around one second) in a very large configuration model with very complex object dependencies.<br /><br />Refer to the RCU_VC_PERFORMANCE report in Note 926714.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Performance, variant configuration, configuration model</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>For more in-depth analysis, it is an advantage if you have knowledge of Transactions ST05 (for database runtimes) and SE30 (for ABAP runtime analysis). For some points, it is also useful for the analysis if you can set breakpoints in the debugger. You should also know how to use the configuration trace (or transaction CUTRC as of Release 4.7).</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The runtime of variant configuration basically depends on two factors:<br /><br />A) Basic view Database access time<br />B) Application view: Modeling<br /><br />A)<br />From a technical point of view, we want to mention again at this stage the tables that should have current database statistics: (Note 558853)<br /><br />Material master data: <br />MARA, MARC, MAKT<br />Characteristic master data:<br />CABN, CABNT, CAWN, CAWNT<br />Classification master data: <br />AUSP, INOB, KLAH, KSML, KSSK<br />Configuration profile/object dependencies:   <br />CUCO, CUEX, CUKB, CUKN, CUOB, CUXREF<br />Configuration storage (as of Release 4.5B):<br />IBIB, INOB, IBIN, IBST, IBSYMBOL, IBINVALUES, IBINOWN, IBINOBS, IBSTREF<br />Bill of material (BOM) tables: <br />MAST, STPO, KDST<br />Variant tables (as of Release 4.5B):<br />CUVTAB, CUVTAB_ADM, CUVTAB_FLD, CUVTAB_GR, CUVTAB_GRT, CUVTAB_IND, CUVTAB_ST, CUVTAB_STT, CUVTAB_TX, CUVTAB_VALC, CUVTAB_VALN<br />Interface design:<br />CECUFM, CECUSD, CECUSDT, CECUSF, CECUSFT<br /><br /><br />B)<br />The configuration runtime can be divided up between the following objects:<br /><br />B1) Entire configuration: Function module CE_C_PROCESSING<br />To get an idea of how much time the system spends on the configuration, you can set a breakpoint at the beginning and at the end of the module and display the response time in the status bar.<br /><br />B2) Processing the object dependencies of an instance. Function module CE_I_CONFIGURE<br />Here you can see how long it takes to configure an instance. The internal logic, which tests if all object dependencies were correctly processed at the ROOT and ensures that no derivations are missing, is determined by configuring the ROOT instance twice.<br /><br />B3) Bill of material (BOM) explosion: Function group CUKO, subroutine BOM_EXPLOSION.<br />Set a breakpoint here if you want to know the number of BOM explosions.<br /><br />B4) Loading the configuration data<br />Accesses to the configuration data depend both on the tables listed above and on the functions that are used within the configuration model (see also below).<br /><br />If you consider the following factors when you use variant configuration, you can generally expect to see improved runtimes. The information in parenthesis for each factor refers to the specific area (of those listed above) in which this factor has most influence. If nothing is specified, the factor influences all areas.<br /><br /><br /><br />(1) Selecting the configuration scenario (configuration parameter)<br />In theory, there are several configuration scenarios that you can select but from the point of view of performance, you should bear the following in mind when choosing a scenario:<br /><br />a) Scenario: Order BOM<br />In this scenario you can choose between result-oriented or knowledge-based storage. In the first case only the result is stored, that is, the configured BOM with the manual changes; in the second case the super BOM is stored, including all object dependencies. For all low level applications, such as material requirements planning (MRP) or costing, the result-oriented storage is better for performance.<br /><br />b) Scenario: Order BOM with BOM explosion in Sales and Distribution (SD)<br />In the order BOM, you can specify that the bill of material is not only exploded in Transaction CU51, but also directly in SD by setting the GV_FORCE_BOM_EXPLOSION_ASL indicator to 'X' in the LCUKOFBE include. This obviously has a negative effect on performance during the creation/change of a sales order.<br /><br />c) Scenario: SET with manual changes<br />Performance problems may arise for very large models and BOMs, in particular if you allow manual changes. This is because the system must match part of the BOM that was transferred to SD with the current BOM of the configuration model. This means that additional BOM explosions are always necessary (at least two). If the modeling is also awkward, with the result that configurable materials are added manually, for example, the number of explosions increases accordingly and in turn, the runtime rises.<br /><br /><br /><br />(2) The number of BOM explosions (B3, B4)<br />The number of BOM explosions actually required depends both on the configuration scenario you choose and on the modeling. In general, there are two things that trigger additional BOM explosions that should be considered. If a BOM explosion is already very time-consuming, this tip can improve performance significantly:<br /><br />a) The modeling should be created in such a way that the assemblies created during a BOM explosion do not require further explosions, for example, because of awkward constraint modeling.<br /><br />b) The information flow should be created in such a way that values are not passed upwards if a BOM explosion takes a very long time. This is possible using constraints and using the modules of the CUPR function group. In both cases you notice that values for a higher instance are determined, if the modeling allows this. These values are then considered during the next explosion.<br /><br />Debugging/SE30 trace:<br />If you are not certain whether or not these additional explosions occur in your model, there are two ways you can find out:<br />b1) Set a breakpoint in the CUKO function group, in the FUNCTION_BOM subroutine. The BOM_EXPLOSION subroutine is usually called once within this form routine. If the program runs through the BOM_EXPLOSION subroutine several times in the FUNCTION_BOM subroutine, the case described in b) above exists.<br />b2) Alternatively, you can set a breakpoint in the CUKO function group in the CHECK_UNPROCESSED_DDB_MSGS subroutine. You can display the table LT_DDB_MSG[] at the end of this form routine. This table contains the characteristics that are responsible for more BOM explosions based on the values you assigned in the corresponding instance LT_DDB_MSG[I]-INSTANCE (where 'I' is the line number).<br /><br /><br /><br />(3) Restricting values using constraints (B2,B4)<br />Constraints are basically used for two purposes. Firstly, they can be used to set values across the entire structure and secondly, to restrict values in relation to other values. The second of these functions is processing intensive and will take up a certain amount of runtime. If you want to restrict only a few values in a few characteristics, it is better to maintain preconditions for characteristic values because these are processed faster and the system takes less time to load the configuration than when you use constraints.<br /><br />If you use constraints to restrict values, you should adhere to the logical sequence of the value restriction; that is, your model should be compiled in such a way that the constraint that restricts the values the most runs first, followed by further constraints that refine the allowed value set. If you are not certain which constraints are the most restrictive, deactivate the constraints one by one and display the remaining values using the F4 help.<br /><br />Note: You cannot specify the sequence in which the constraints are processed by the system. You can only advise the user to use a specific sequence or you can format the characteristic value assignment interface in such a way that very selective characteristics are evaluated first.<br /><br /><br /><br />(4) Procedures and constraints<br />Object dependencies are usually processed in the following sequence (for more information, see the documentation under http://help.sap.com):<br /><br />a)<br />All actions are processed repeatedly, until no further values can be derived.<br />b)<br />All procedures are processed once in the following sequence:<br />-Procedures for the configuration profile in the specified sequence<br />-Procedures for characteristics<br />-Procedures for characteristic values<br />c)<br />Actions, if procedures have set values that trigger actions<br />d)<br />Preconditions<br />e)<br />Selection conditions for characteristics<br /><br />Constraints run in parallel with the processing of a)-c). They do not have the same procedural properties as procedures. The constraint nets are loaded to the memory when the configuration is loaded. As soon as the objects relevant for a constraint are available and its condition part is filled, the constraint is executed. The constraint 'remembers' which characteristics are relevant for it and is executed if these characteristics change.<br /><br />If you have optimal modeling, therefore, the following applies for a one-step configuration model: Procedures for the ROOT are processed twice, constraints run just once. However, before the procedures run for a second time all their set values are removed and the configuration is run again. If, however, you have modeled in such a way that values set in procedures flow into the condition part of a constraint, this constraint is executed again because this value was removed and a constraint-relevant characteristic was changed accordingly. On the other hand, a value set by a constraint can trigger a procedure again. If your modeling is awkward, these procedures and constraints may be processed more times than necessary (for more information, see Note 594351).<br /><br />You can find out if this situation exists in your model by checking the object dependency trace. Load an existing configuration; for example, a sales order, activate the trace from transaction VA02, \"Environment -&gt;Analysis -&gt;Configuration trace\" (you can change the trace settings in the initial screen of transaction CU50 \"Extras -&gt;Trace -&gt;Settings\"), and then you see how often your procedures run if they occur at only one place. Note that a trace in transaction CU50 is not significant because the system response when the configuration is loaded is represented differently internally. Alternatively, you can create a trace for assigning a value. In this case, transactions VA02 and CU50 are identical. Furthermore, in a multilevel model, the ROOT instance is configured at least three times; that is, procedures run three times if modeling is optimal.<br /><br />Depending on your situation, you may have to either change the logic of your object dependencies or reformulate such procedures with the system behavior described above for constraints.<br /><br /><br /><br />(5) Constraints without a condition section (B2, B4)<br />In modeling, you should generally ensure that constraints always have a condition section. Otherwise the constraint is always executed as soon as a relevant characteristic changes. If there is an appropriate condition section, a constraint is executed only if the statement in the condition section is true.<br /><br /><br /><br />(6) Using actions<br />As described above, actions may trigger a new configuration of the instance. For this reason, as well as the fact that they have no sequential quality, actions are obsolete and they should no longer be used from a performance point of view. You can find out if you use actions in your configuration model either by checking the configuration profile directly, or, in transaction CU03 use the F4 help to look for the 'Action' dependency type. The system then displays a where-used list containing the relevant configuration profiles or characteristics.<br /><br /><br /><br />(7) Many procedures of the same type for the default value (B2)<br />The standard user settings are such that after you choose 'Return' for each assigned value, all object dependencies are processed in the characteristic value assignment screen in the sequence outlined above. This means that if many procedures or one procedure with numerous assigned values are attached to the configuration profile or characteristic, it is executed repeatedly. If you do not want this to happen, you should convert these procedures into a constraint, as this is executed just once during loading. However, you cannot use this option of you use the SET_DEFAULT or DEL_DEFAULT statements. These statements are not supported in the constraint.<br /><br /><br /><br />(8) Characteristics with many values (B4)<br />If you have many characteristics with many values, this can increase the load time of the configuration. This is particularly relevant if you have also maintained characteristic values in many languages. You can see this either directly in transaction CT04 or in the database trace in transaction ST05. In the summarized display using the tables you see many accesses to the tables CABN, CABNT, and in particular to the tables CAWN and CAWNT. This is only necessary if the values must be displayed to the user in the F4 help. If the users generally know the values that they should maintain for this type of characteristic, you should consider if it may be better to define a value table in transaction CT04. The disadvantage of this is that the allowed values are no longer displayed.<br /><br />Alternatively, you can use your own function module as a check, but if you do this, you must program the F4 help and the display of the actual values yourself. The advantage in both cases is that the load time is reduced (See also RCU_VC_PERFORMANCE).<br /><br /><br /><br />(9) Configuration profile settings (B4)<br />Check whether the settings made in transaction CU43 (configuration profile) are correct. Incorrect settings have a negative effect on performance.<br /><br />a) Tab page 'Configuration Initial Screen'&gt;'Configuration Parameter':<br />If the number of the BOM explosions is set too high; for example, there is no BOM but it is selected in multiple levels, unnecessary source code is loaded and processed. Is the availability of the components activated, even though this is not relevant during the configuration? Do you expand only configurable assemblies?<br /><br />b) Tab page 'Configuration Initial Screen'&gt;'Interfaces'<br />If, for example, you have only one instance and the user does not have to view the master data of the BOM items, you can deactivate the configuration browser indicator and the master data indicator.<br /><br /><br /><br />(10) Linking variant tables to database tables<br />In transaction CU62, you have the option of linking a variant table to a database table; this means that the data is no longer displayed in the internal tables for the variant tables, rather it is stored in a transparent database table. If variant tables are small, but the logic for accesses to the internal display is optimized, this means that you should not link small tables to database tables. Only unnecessary source code is processed and this does not improve performance in any way. However, access to the database table is improved for large tables. As a rule of thumb (this was not determined from specific experiments, rather it is based on experience only) we can assume the following:<br /><br />If a variant table has fewer than 10,000 rows by 10 columns, do not link it to a database table.<br />If a variant table has more than 10,000 rows by 10 columns, link it to a database table.<br /><br />If you want to activate a link to the database for large variant tables, you can do this easily in the basic data in transaction CU62. If you have small variant tables that are linked already, you must cancel this link manually; this means that you must create a new variant table. Alternatively, contact SAP Support.<br />Note that undoing a link in the standard system is not supported by SAP.<br /><br />See also RCU_VC_PERFORMANCE at the end of this note.<br /><br /><br /><br />(11) Empty fields in variant tables<br />The internal logic for processing variant tables is optimized for completely maintained entries. If there are empty fields, this has a negative effect on performance. This applies, in particular, if numeric values are missing. To check if this situation exists in your system, refer to report RCU_VC_PERFORMANCE.<br /><br /><br /><br />(12) Large number of variant tables<br />There is a specific memory area available internally for loading variant tables, and access to this memory area has been optimized. If you have a very large number of variant tables, the memory space assigned may not be sufficient. To keep storage and reload times of further variant tables to a minimum, or to avoid them completely, we recommend that you have small variant tables with few entries rather than preconditions in the configuration model, for example, or that you link very large variant tables to database tables.<br /><br /><br />(13) Using SAP_PRICNG (B2,B4) and SAP_DIALOG<br />Generally, you can assign object dependencies to a dependency group (transaction CU02-&gt;Basic data). If you have many procedures that you use to determine variant conditions, which are then transferred to SD using a characteristic that refers to the SDCOM_VKOND structure, it is generally sufficient if the user creates this dependency when they finish value assignment on the characteristic valuation screen, that is to say, on exiting the configuration. Assign all the object dependencies that should run once at the end of the configuration process to the SAP_PRICNG dependency group. Therefore, the processing logic described above does not apply to the object dependencies in this dependency group. Similarly, you can assign object dependencies that dynamically hide characteristics (using object characteristics with reference to SCREEN_DEP) to the dependency group SAP_DIALOG. Read Note 525399 for more information.<br /><br /><br />(14) Using manual conditions (B4)<br />From a performance point of view, manual conditions are not loaded or processed as quickly as if you use procedures to set the values. Therefore, we recommend that you use object dependencies for this. You can find out if your system contains manual conditions (which you maintain in transaction CU50) by checking the MACOND table. MACOND-MATNR informs you in which configurable material you can find the manual conditions.<br /><B>Caution: This point becomes obsolete after you implement Note 1004292</B>.<br /><br /><br />(15) Variant matching<br />The configuration data is stored in the IBASE (as of Release 4.5B). This includes several tables in which both the administrative data and the configuration value assignments are stored. You can use transaction CUTABLEINFO, for example, to get a good overview of the main tables involved. To ensure that this data is read quickly, the relevant database statistics must always be up to date, as described above. The IBSASE statistics must also be up to date. These statistics are usually updated automatically by the background program IBRT_STATISTICS_CREATE during every update. After an upgrade, or if the automatic report run has been deactivated, you must start the program manually. It is essential that you first read the following notes:<br />736268, 581642, 545359, 544722, 356397, 309035, 301944.<br /><br />There are two further options for accelerating the variant matching process. Firstly, you can use the report RCU_EXCL_CHARACTERISTICS to exclude certain characteristics (Note 336842 and 362438), or alternatively, you can use the report RCU_CUSEPRES (Notes 409123 and 433565). When you use the report RCU_EXCL_CHARACTERISTICS, note that an exclusion of characteristics may also slow down variant matching if characteristics that are very selective are excluded.<br /><br /><br />Debugging: Set a breakpoint in the function module CUSE_TYPE_SEARCH. The transfer table ITYP of this function module provides an overview of the material variants found for the specified value assignment.<br /><br /><br /><br />(16) Overlapping classes in a configurable material (B2, B4) (multiple classification). It is possible to assign several classes to a configurable material. This is useful for attaining a better overview of your characteristics, and it offers the possibility of assigning a class of thematic characteristics to different materials. However, for performance reasons, you should ensure the following in this case: If you have characteristics that are in several classes and that are loaded simultaneously; that is, they are assigned to the same configurable material, you must load an additional logic to ensure that the values of a characteristic in one class are set in relation to the values of the overwriting characteristic from another class, for example (in other words, the logic establishes the average quantity of the allowed values).<br /><br />Debugging/SE30 trace:<br />You can see this in the function modules of the function group CEI3 in the ABAP trace, for example. If you have many characteristics of this type, this can have a strong impact on performance. If possible, you should try to avoid overlapping classes in your modeling. Furthermore, if there is no overlapping (that is, if each characteristic appears in one class at most), refer to Note 358886 (and Note 554749).<br /><br />You can use the report RCU_VC_PERFORMANCE to find out if there are characteristics like this in your models.<br /><br /><br /><br />(17) Using overwriting (B4)<br />In transaction CL02 (class maintenance), you have the option of overwriting characteristics and characteristic values according to their class; that means, you can change the value set of a characteristic depending on the class in which the characteristic occurs. Since this always involves additional loading and the processing of additional source code, you should not use this option for performance reasons. See also section (15).<br /><br /><br /><br />(18) Using class hierarchies in class type 200/300<br />In the classification of the class type for materials (001) or documents (017), for example, it is common practice not to assign the characteristics directly to the class, but to set up a class hierarchy to represent the classes in a more structured way. The actual characteristics are then only assigned to the subordinate classes. Calculating the actual values assigned to the characteristics and the values allowed after processing the object dependencies requires additional work. If you do not have many characteristics, you should not use class hierarchies. If you do not use class hierarchies (you can use the report RCU_VC_PERFORMANCE to test this, for example), you must also change the Customizing settings because the corresponding source code is not processed in this case.<br /><br />You can activate or deactivate the relevant Customizing indicators as follows:<br />- Call transaction O1CL<br />- Select all the entries<br />- Double-click 'Class types'<br />- Double-click class type 200<br />- Deactivate the 'Hierarchy Allowed' indicator<br />- Choose 'Back'<br />- Double-click class type 300<br />- Deactivate the 'Hierarchy Allowed' indicator<br /><br /><br /><br />(19) A large number of selection conditions of the same type<br />The super BOM is mainly reduced by selection conditions for the actual bill of material according to the characteristic values selected by the user. If the bill of material is small, selection conditions are extremely high performing and there is no quicker way to exclude BOM items from the super BOM. However, if your bill of material contains a component that can be replaced by several items, we recommend that you revert to class nodes instead. The advantage of this is that you do not have to load object dependency data and maintenance time is reduced. Here, you classify your objects with a class of the class type 200 (for example).&#x00A0;&#x00A0;You enter this class into your bill of material as a class node (see also the variant configuration documentation at http://help.sap.com). In accordance with the value assignment on the characteristic valuation screen, the corresponding component classified in this way is extracted during the explosion of the bill of material. You must experiment from case to case to find out from what number of items the performance improves if you use class nodes.<br /><br />If you use class nodes, you should ensure in particular that class statistics are maintained for class type 200 in our example. Use transaction CLST to update or create class statistics as this transaction can improve performance greatly if you have a large number of classified objects. If you have classified a large number of objects (several thousand), and you want to allow these objects as possible BOM items, we recommend that you split this class into several class nodes and provide each class node with selection conditions.<br /><br /><br /><br />(20) Using default values<br />You can use the SET_DEFAULT statement in a procedure to set default values. You can use the DEL_DEFAULT command to cancel this statement dynamically. If the values should be set just once, and if they can be changed but not cancelled, you can define default values in the characteristic master data in transaction CT04. This improves performance more than setting defaults using object dependencies. One disadvantage, however, is that this then applies to all classes and configuration models to which the characteristic is assigned, and not only for the selected model, as is the case with procedures.<br /><br /><br /><br />(21) User settings (B2)<br />If you enter a material/plant in transaction CU50 and then go to the configuration, you can make user-specific settings for the configurator response in the View&gt;Settings menu of the subsequent characteristic value assignment screen. The following settings are particularly interesting:<br /><br />a) 'Pricing' tab page: Activate the 'Permanent' radio button only if the user needs to actually see the price based on the variant conditions used. Otherwise, select 'On request', then the price is displayed when the user chooses the corresponding button.<br /><br />b) 'Configurator' tab page: If you have many characteristics that are not linked to each other by object dependencies and that the user must analyze, and if the user experiences very long runtimes when setting values, you can set the configurator to 'Active on request'. As a result, the characteristics are not configured during the assignment of values, but only if the user actively chooses the corresponding button. Note that when you make this setting, the function of dynamically showing/hiding characteristics using SCREEN_DEP-INVISIBLE is no longer available because the process flow of the object dependency is triggered only if the user activates the configuration explicitly.<br /><br />c) 'Variant Matching' tab page: Activate 'Type matching on request' so that the system does not try to match material variants after each value assigned.<br /><br /><br /><br />(22) Large volume of data because of reference characteristics/calculation input characteristics (B4)<br />Every characteristic that you have assigned to the class is saved to the database (if it has a value assigned) when you save the configuration. If you experience performance problems when you load the configuration, you can reduce the volume of data by not assigning all characteristics to the class of the configurable material.<br />(Subsequently you can only do this manually or, if possible, using a report, so it is better if you consider this during modeling.) Good candidates for exclusion are; for example, specific reference characteristics or calculation input characteristics that should only buffer values while calculating, but their own values are not actually saved. You must consider whether reference characteristics are relevant on a case by case basis. Bear in mind that characteristics not assigned to the class<br />are not displayed in the characteristic value assignment screen either. Furthermore, subsequent processes such as MRP or production orders, which access the configuration data at a low level, depend on specific reference characteristics. For more information about pricing, see Note 187162.<br /><br /><br /><br />(23) Large data volume because of dynamic show/hide SCREEN_DEP-INVISIBLE (B4).<br />Depending on the value assignment, you can show or hide characteristics. You can use the object characteristic for the SCREEN_DEP-INVISIBLE structure to do this (alternatively, not ready for input using the SCREEN_DEP-NOINPUT structure). If you have assigned this reference characteristic to the class of the configurable material, all hidden characteristics are saved as the characteristic value for SCREEN_DEP-INVISIBLE in the IBINVALUES table on the database. This is only necessary if you do not want to see the hidden characteristics using the module VC_I_GET_CONFIGURATION. Otherwise, this assignment is not necessary and this can save many entries in the table IBINVALUES. If you have an existing configuration with SCREEN_DEP-INVISIBLE, refer to Notes 563703 and 563124. This decreases the dataset in the configuration store and reduces the load time. Instead of SCREEN_DEP-INVISIBLE, you can alternatively use selection conditions.<br /><br /><br /><br />(24) Large volume of data because of the pricing function SDCOM_VKOND<br />In your dependency model, you have a characteristic that refers to SDCOM-VKOND to change prices depending on the value assignment. Each characteristic value for this reference characteristic is stored as a separate entry in the IBINVALUES table. If you have a very large number of variant conditions, you can use the $set_pricing_factor command instead. For this, however, you would also have to change object dependencies. Depending on the value assignment, a variant condition can be weighted with a factor. If you have several conditions, multiply these factors by the base price (for more information, see the documentation for variant configuration). The end result of this is that you have only stored the variant condition as a characteristic value in the IBINVALUES table once.<br /><br /><br /><br />(25) Using VC_I_GET_CONFIGURATION<br />You can use the module VC_I_GET_CONFIGURATION (loading characteristic value assignment) in your programs. If, however, you only want to display the information on the database (that is, no object dependencies are processed, the interface design is not taken into account), we recommend that you use the module VC_I_GET_CONFIGURATION_IBASE instead. You can also use this module in user exits within the configuration. However, the VC_I_GET_CONFIGURATION function module cannot be used in user exits because it initializes internal tables and thus leads to errors (Note 460106).<br /><br /><br /><br />(26) Fast data entry in IS-MILL<br />If you use a very complex configuration model, for which you have already noticed long runtimes in the configuration, it may not always be advisable to use the fast data entry function. If you use this function, the configuration must be run through twice, once for loading the value assignment of the material variant and a second time to configure the current model with these values. For more information, see Note 701745.<br /><br /><br /><br /><br />DOCUMENTATION for the report RCU_VC_PERFORMANCE<br />===========================================<br /><br />Start program RCU_VC_PERFORMANCE in transaction SE38. Each tab page represents an independent subroutine, each of which can be executed in the background if you set the indicator at the bottom of the screen. These reports are read-only reports, no changes are made to the database.<br /><br />Specifically, the programs show the following details:<br /><br /><br />1) 'Characteristic statistics' tab page<br />------------------------------------<br />Number of characteristics:<br />single value:<br />multi value:<br />from overwriting:<br />restrictable:<br /><br />Reference characteristics: &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SDCOM-VKOND&#x00A0;&#x00A0;- &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SCREEN_DEP&#x00A0;&#x00A0; - &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;VBAK&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;VBAP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-<br /><br />atinn&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;adzhl&#x00A0;&#x00A0;atnam&#x00A0;&#x00A0;CAWN entries&#x00A0;&#x00A0; single&#x00A0;&#x00A0;restr.&#x00A0;&#x00A0;comment<br /><br />That is, the number of the values for each characteristic, whether it is multi-value or restrictable and in which class type it is used.<br /><br /><br />2) 'Variant table statistics' tab page<br />------------------------------------<br />This report specifies which variant tables you are using, the number of the characteristics (total, charformat, numformat) and the number of rows. Min/max not equal to 1 means that there are empty fields in the variant tables. Under 'Comment' you can see whether the variant tables are standardized or linked to a database.<br /><br /><br />3) 'Class item and statistics' tab page<br />-------------------------------------<br />Here, the system displays the date of the last CLST run and the number of the configurable materials (class type 300) or objects assigned to class nodes (class type 200).<br /><br /><br />4) 'CUCO Statistics' tab page<br />---------------------------<br />This gives you an overview of how many configuration profiles you have and which object dependencies are used there, starting with the most complex models.<br /><br /><br />5) 'Class checks' tab page<br />------------------------<br />This gives you an overview of the relevant Customizing (17), whether there is a class hierarchy, entry KSSK-MAFID not equal to 0, whether there is overwriting (16), and which classes show overlapping (15).<br /><br /><br />6) 'Volume checks' tab page<br />-------------------------<br />This tab page shows the sizes of the most memory-intensive tables in the configuration and classification. Run this subroutine in the background only.<br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D038507"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D028307)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000917987/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000917987/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000917987/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000917987/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000917987/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000917987/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000917987/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000917987/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000917987/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "926714", "RefComponent": "LO-VC", "RefTitle": "Report RCU_VC_PERFORMANCE", "RefUrl": "/notes/926714"}, {"RefNumber": "736268", "RefComponent": "LO-VC-MAT", "RefTitle": "Composite SAP note: Type matching perform. in configuration", "RefUrl": "/notes/736268"}, {"RefNumber": "701745", "RefComponent": "IS-MP-SD", "RefTitle": "long response time displaying characteristic overview", "RefUrl": "/notes/701745"}, {"RefNumber": "594351", "RefComponent": "LO-VC-DEP", "RefTitle": "Constraints and procedures", "RefUrl": "/notes/594351"}, {"RefNumber": "581642", "RefComponent": "LO-VC", "RefTitle": "Deadlocks w/ updates in table IBINVALST_SYM, IBINST_OBJ", "RefUrl": "/notes/581642"}, {"RefNumber": "563703", "RefComponent": "LO-VC-CHR", "RefTitle": "BADI: Reference characteristics with reference to SCREEN_DEP", "RefUrl": "/notes/563703"}, {"RefNumber": "563124", "RefComponent": "LO-VC-CHR", "RefTitle": "Use of characteristics with reference to SCREEN_DEP", "RefUrl": "/notes/563124"}, {"RefNumber": "558853", "RefComponent": "LO-VC", "RefTitle": "Long runtimes during the variant configuration", "RefUrl": "/notes/558853"}, {"RefNumber": "554749", "RefComponent": "LO-VC", "RefTitle": "Stopping explosion only on request", "RefUrl": "/notes/554749"}, {"RefNumber": "545359", "RefComponent": "LO-VC-LOI", "RefTitle": "Error message with XPRA \"IBRT_STATISTICS_CREATE\"", "RefUrl": "/notes/545359"}, {"RefNumber": "544722", "RefComponent": "LO-VC", "RefTitle": "Termination of XPRA IBRT_STATISTICS_CREATE", "RefUrl": "/notes/544722"}, {"RefNumber": "525399", "RefComponent": "LO-VC", "RefTitle": "Performance upon configuration in the background", "RefUrl": "/notes/525399"}, {"RefNumber": "460106", "RefComponent": "LO-VC-CHR", "RefTitle": "VC_I_GET_CONFIGURATION_IBASE to display the configuration", "RefUrl": "/notes/460106"}, {"RefNumber": "433565", "RefComponent": "LO-VC-MAT", "RefTitle": "Material variant determination: Control", "RefUrl": "/notes/433565"}, {"RefNumber": "409123", "RefComponent": "LO-VC-MAT", "RefTitle": "Maintenance report for table CUSEPRES", "RefUrl": "/notes/409123"}, {"RefNumber": "362438", "RefComponent": "LO-VC-MAT", "RefTitle": "Procedure RCU_EXCL_CHARACTERISTICS", "RefUrl": "/notes/362438"}, {"RefNumber": "358886", "RefComponent": "LO-VC", "RefTitle": "Configuration and multiple classification", "RefUrl": "/notes/358886"}, {"RefNumber": "356397", "RefComponent": "CS-IB-CF", "RefTitle": "Performance variant search", "RefUrl": "/notes/356397"}, {"RefNumber": "336842", "RefComponent": "LO-VC-MAT", "RefTitle": "Characteristics not allowed for type matching", "RefUrl": "/notes/336842"}, {"RefNumber": "309035", "RefComponent": "CS-IB-CF", "RefTitle": "Performance variant matching in Rel. 4.5B", "RefUrl": "/notes/309035"}, {"RefNumber": "301944", "RefComponent": "CS-IB-CF", "RefTitle": "Performance Variant matching for 4.6B", "RefUrl": "/notes/301944"}, {"RefNumber": "187162", "RefComponent": "LO-VC", "RefTitle": "Reference characteristics for pricing", "RefUrl": "/notes/187162"}, {"RefNumber": "1730246", "RefComponent": "LO-VC", "RefTitle": "Wrong constraint count in ST13 - VARCONF - CUP", "RefUrl": "/notes/1730246"}, {"RefNumber": "1667903", "RefComponent": "LO-VC", "RefTitle": "\"Trace table mismatch!\" error in ST13 - VARCONF - CUP", "RefUrl": "/notes/1667903"}, {"RefNumber": "1121318", "RefComponent": "LO-VC", "RefTitle": "Analysis of the performance of dependency knowledge in VC", "RefUrl": "/notes/1121318"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2871467", "RefComponent": "LO-VC-VTA", "RefTitle": "CU60: Long runtime upon initial access", "RefUrl": "/notes/2871467 "}, {"RefNumber": "2629501", "RefComponent": "LO-VC-PME", "RefTitle": "PMEVC: Changed behavior for variant tables with empty cells", "RefUrl": "/notes/2629501 "}, {"RefNumber": "1730246", "RefComponent": "LO-VC", "RefTitle": "Wrong constraint count in ST13 - VARCONF - CUP", "RefUrl": "/notes/1730246 "}, {"RefNumber": "1667903", "RefComponent": "LO-VC", "RefTitle": "\"Trace table mismatch!\" error in ST13 - VARCONF - CUP", "RefUrl": "/notes/1667903 "}, {"RefNumber": "581642", "RefComponent": "LO-VC", "RefTitle": "Deadlocks w/ updates in table IBINVALST_SYM, IBINST_OBJ", "RefUrl": "/notes/581642 "}, {"RefNumber": "1121318", "RefComponent": "LO-VC", "RefTitle": "Analysis of the performance of dependency knowledge in VC", "RefUrl": "/notes/1121318 "}, {"RefNumber": "358886", "RefComponent": "LO-VC", "RefTitle": "Configuration and multiple classification", "RefUrl": "/notes/358886 "}, {"RefNumber": "558853", "RefComponent": "LO-VC", "RefTitle": "Long runtimes during the variant configuration", "RefUrl": "/notes/558853 "}, {"RefNumber": "701745", "RefComponent": "IS-MP-SD", "RefTitle": "long response time displaying characteristic overview", "RefUrl": "/notes/701745 "}, {"RefNumber": "926714", "RefComponent": "LO-VC", "RefTitle": "Report RCU_VC_PERFORMANCE", "RefUrl": "/notes/926714 "}, {"RefNumber": "187162", "RefComponent": "LO-VC", "RefTitle": "Reference characteristics for pricing", "RefUrl": "/notes/187162 "}, {"RefNumber": "460106", "RefComponent": "LO-VC-CHR", "RefTitle": "VC_I_GET_CONFIGURATION_IBASE to display the configuration", "RefUrl": "/notes/460106 "}, {"RefNumber": "336842", "RefComponent": "LO-VC-MAT", "RefTitle": "Characteristics not allowed for type matching", "RefUrl": "/notes/336842 "}, {"RefNumber": "544722", "RefComponent": "LO-VC", "RefTitle": "Termination of XPRA IBRT_STATISTICS_CREATE", "RefUrl": "/notes/544722 "}, {"RefNumber": "736268", "RefComponent": "LO-VC-MAT", "RefTitle": "Composite SAP note: Type matching perform. in configuration", "RefUrl": "/notes/736268 "}, {"RefNumber": "554749", "RefComponent": "LO-VC", "RefTitle": "Stopping explosion only on request", "RefUrl": "/notes/554749 "}, {"RefNumber": "594351", "RefComponent": "LO-VC-DEP", "RefTitle": "Constraints and procedures", "RefUrl": "/notes/594351 "}, {"RefNumber": "563124", "RefComponent": "LO-VC-CHR", "RefTitle": "Use of characteristics with reference to SCREEN_DEP", "RefUrl": "/notes/563124 "}, {"RefNumber": "563703", "RefComponent": "LO-VC-CHR", "RefTitle": "BADI: Reference characteristics with reference to SCREEN_DEP", "RefUrl": "/notes/563703 "}, {"RefNumber": "545359", "RefComponent": "LO-VC-LOI", "RefTitle": "Error message with XPRA \"IBRT_STATISTICS_CREATE\"", "RefUrl": "/notes/545359 "}, {"RefNumber": "525399", "RefComponent": "LO-VC", "RefTitle": "Performance upon configuration in the background", "RefUrl": "/notes/525399 "}, {"RefNumber": "409123", "RefComponent": "LO-VC-MAT", "RefTitle": "Maintenance report for table CUSEPRES", "RefUrl": "/notes/409123 "}, {"RefNumber": "433565", "RefComponent": "LO-VC-MAT", "RefTitle": "Material variant determination: Control", "RefUrl": "/notes/433565 "}, {"RefNumber": "362438", "RefComponent": "LO-VC-MAT", "RefTitle": "Procedure RCU_EXCL_CHARACTERISTICS", "RefUrl": "/notes/362438 "}, {"RefNumber": "356397", "RefComponent": "CS-IB-CF", "RefTitle": "Performance variant search", "RefUrl": "/notes/356397 "}, {"RefNumber": "301944", "RefComponent": "CS-IB-CF", "RefTitle": "Performance Variant matching for 4.6B", "RefUrl": "/notes/301944 "}, {"RefNumber": "309035", "RefComponent": "CS-IB-CF", "RefTitle": "Performance variant matching in Rel. 4.5B", "RefUrl": "/notes/309035 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}