{"Request": {"Number": "1776739", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 354, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017538332017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001776739?language=E&token=E31C9AFABB907F0AE77DDD220B415A67"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001776739", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001776739/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1776739"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 38}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.07.2021"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-ADDON"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade Add-On Components"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade Add-On Components", "value": "BC-UPG-ADDON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-ADDON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1776739 - Release strategy for SAP Screen Personas 1.0/ 2.0"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note contains information about planning the installation and upgrades of the ABAP add-on PERSOS.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Release strategy, PERSOS, PERSOS 100, PERSOS 200</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<ol>\r\n<li>General information</li>\r\n<li>Overview</li>\r\n<ol style=\"list-style-type: lower-alpha;\">\r\n<li>SAP Product Availability Matrix (PAM)</li>\r\n<li>Download</li>\r\n<li>Modifications</li>\r\n<li>Unicode required</li>\r\n<li>Patchlevels</li>\r\n<li>Deinstallation</li>\r\n</ol>\r\n<li>PERSOS 100</li>\r\n<ol style=\"list-style-type: lower-alpha;\">\r\n<li>Installation</li>\r\n</ol>\r\n<li>PERSOS 200</li>\r\n<ol style=\"list-style-type: lower-alpha;\">\r\n<li>Installation</li>\r\n<li>Delta Upgrade</li>\r\n</ol></ol>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>1. General information<br /><br />PERSOS 100/200 has gone out of maintenance at Dec 31 2018.</strong></p>\r\n<ul>\r\n<li>See the general information from SAP Note 70228.</li>\r\n<li>You cannot uninstall ABAP add-ons.</li>\r\n<li>You can install ABAP add-ons only on certain SAP releases.</li>\r\n<li>Upgrades with ABAP add-ons</li>\r\n</ul>\r\n<p>In systems in which an ABAP add-on is installed, you can upgrade only to SAP releases that are supported for this add-on.</p>\r\n<p>Note that there is a delay between the delivery of the SAP standard releases and the release of the corresponding add-on releases.</p>\r\n<p>If an add-on upgrade is connected with a change of the SAP release, it is integrated into the SAP upgrade (repository or system switch). To carry out this upgrade, you require an additional add-on upgrade CD or DVD in addition to the SAP standard upgrade CDs or DVDs. If the SAP standard upgrade is carried out without this additional CD or DVD, the add-on will no longer work after the upgrade. The entire SAP system is inconsistent. For more information about this problem, see SAP Note 33040.</p>\r\n<p>If you are scheduling an upgrade or the import of an enhancement package, note that you should not import the latest Support Package Stack in the source release directly before the upgrade to ensure that the required, equivalent Support Package is already available in the target release. For more information about this, see SAP Note 832594 and the following information on SAP Service Marketplace. <a target=\"_blank\" href=\"http://service.sap.com/sp-stacks\">http://service.sap.com/sp-stacks</a> -&gt; SP Stack Information -&gt; SP Stack Strategy</p>\r\n<p><strong>2. Overview</strong><br />a) SAP Product Availability Matrix<br />The following information about the add-on in the SAP Product Availability Matrix is available on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/PAM\">http://service.sap.com/PAM</a></p>\r\n<ul>\r\n<li>Availability</li>\r\n<li>End of maintenance</li>\r\n<li>Release for products with enhancement packages</li>\r\n<li>Language support</li>\r\n</ul>\r\n<p>b) Download<br />The software is available on SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/SWDC\">http://service.sap.com/SWDC</a><br />&#160;&#160;&#160; -&gt; Search for Software Downloads \"SAP SCREEN PERSONAS \"</p>\r\n<p>If you do not use a maintenance transaction in SAP Solution Manager to download the files, be sure to download also the attribute change package PERSOS====100 (or PERSOS====200 for release 200) from Service Marketplace and put it into your EPS/in-directory!</p>\r\n<p>c) The add-on does not contain any modifications.<br />You can also perform the installation if you have already imported more Support Packages into your system than are specified in the section \"Required Support Packages\".</p>\r\n<p>d) Unicode required<br />Since 2006, SAP has been recommending that its customers running non-Unicode systems upgrade to Unicode. While some customers have deployed SAP Screen Personas onto non-Unicode systems, this is not a supported scenario.<br /><br />To ensure optimal performance of SAP Screen Personas, we are only supporting Unicode systems. For Personas to function properly, both the system on which Personas is installed, as well as any target systems must be Unicode. If you use Personas in the &#8220;remote&#8221; scenario with a separate target system, you should limit this to testing and development purposes. We strongly recommend that customers install Personas on every system where they want to use personalized screens.</p>\r\n<p>e) Patchlevels<br />You can find below the minimum support package level for the SAP_BASIS component for installation the product. While a higher level may not be required, it is still recommended.<br />As SAP Screen Personas highly depends on&#160;functionality provided by the SAP kernel, SAP strongly recommends using the latest patch level from at least the SAP kernel.</p>\r\n<p>Details can be found for Personas Silverlight&#160;in note 1848339.</p>\r\n<p>f) Deinstallation<br />PERSOS 100 and PERSOS 200 can be uninstalled from the system. For details please check note 2226123.</p>\r\n<p><strong>3. PERSOS 100</strong></p>\r\n<p><strong>a) Installation</strong></p>\r\n<ul>\r\n<li>Name of the Installation Package: SAPK-100AGINPERSOS</li>\r\n<li>Material number: 51044614</li>\r\n<li>Path on media: DATA_UNITS -&gt; PERSOS_100_INST -&gt; DATA</li>\r\n<li>Required Support Packages:&#160;<br /><br />\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Component Release</strong></td>\r\n<td><strong>Support Packages</strong></td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BASIS 700</td>\r\n<td>SAPKB70024</td>\r\n</tr>\r\n<tr>\r\n<td>or 701</td>\r\n<td>SAPKB70109</td>\r\n</tr>\r\n<tr>\r\n<td>or 702</td>\r\n<td>SAPKB70208</td>\r\n</tr>\r\n<tr>\r\n<td>or 731</td>\r\n<td>SAPKB73101</td>\r\n</tr>\r\n<tr>\r\n<td>or 740&#160;</td>\r\n<td>none</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<br />Please check note 1885312 for further information regarding Personas Silverlight component.</li>\r\n</ul>\r\n<p><strong>&#160;</strong></p>\r\n<p><strong>SAPK-10001INPERSOS</strong><br />If you have already created groups in your system you have to follow note 1842360 to avoid data inconsistencies!</p>\r\n<p><strong>4. PERSOS 200<br /></strong></p>\r\n<ul>\r\n<li>If you have not yet installed SAPKB70026 (or equivalent) package, you have to provide the attribute change package PERSOS====200 also in your EPS inbox. This file can also be downloaded from Service Marketplace.</li>\r\n<li>Required notes</li>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"/notes/1354957\">1354957</a></li>\r\n<li><a target=\"_blank\" href=\"/notes/1487337\">1487337</a></li>\r\n<li><a target=\"_blank\" href=\"/notes/1582870\">1582870</a></li>\r\n<li><a target=\"_blank\" href=\"/notes/1848339\">1848339</a></li>\r\n</ul>\r\n</ul>\r\n<p><br /><strong>a) Installation</strong></p>\r\n<ul>\r\n<li>Name of the Installation Package: SAPK-200AGINPERSOS</li>\r\n<li>Material number: 51046685</li>\r\n<li>Path on media:&#160; DATA_UNITS -&gt; PERSOS_200_INST -&gt; DATA</li>\r\n<li>Required Support Packages:<br /><br />\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Component Release</strong></td>\r\n<td><strong>Support Packages</strong></td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BASIS 700</td>\r\n<td>SAPKB70024</td>\r\n</tr>\r\n<tr>\r\n<td>or 701</td>\r\n<td>SAPKB70109</td>\r\n</tr>\r\n<tr>\r\n<td>or 702</td>\r\n<td>SAPKB70208</td>\r\n</tr>\r\n<tr>\r\n<td>or 731</td>\r\n<td>SAPKB73101</td>\r\n</tr>\r\n<tr>\r\n<td>or 740 till 754</td>\r\n<td>none</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><strong>b) Delta Upgrade</strong><br />If you have already created groups in your system you have to follow note 1842360 before the upgrade to avoid data inconsistencies!</p>\r\n<ul>\r\n<li>Name of the delta upgrade package: SAPK-200AGINPERSOS</li>\r\n<li>Material number: 51046685</li>\r\n<li>Path on media: DATA_UNITS -&gt; PERSOS_200_INST</li>\r\n<li>Required Support Packages<br /><br />\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Component Release</strong></td>\r\n<td><strong>Support Packages</strong></td>\r\n</tr>\r\n<tr>\r\n<td>PERSOS 100</td>\r\n<td>none</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BASIS 700</td>\r\n<td>SAPKB70024</td>\r\n</tr>\r\n<tr>\r\n<td>or 701</td>\r\n<td>SAPKB70109</td>\r\n</tr>\r\n<tr>\r\n<td>or 702</td>\r\n<td>SAPKB70208</td>\r\n</tr>\r\n<tr>\r\n<td>or 731</td>\r\n<td>SAPKB73101</td>\r\n</tr>\r\n<tr>\r\n<td>or 740 till 754</td>\r\n<td>none</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<br /><strong>Note</strong>: <br />PERSOS 200 is <strong>not released</strong> for any release higher than SAP S/4HANA 1909. In this case you have to migrate your data to SAP Screen Personas 3.0 and uninstall PERSOS 200 <strong>before</strong> the conversion.</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><strong>&#160;</strong></p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-PER (SAP Screen Personas)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D026202)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D026202)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001776739/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001776739/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001776739/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001776739/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001776739/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001776739/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001776739/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001776739/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001776739/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "70228", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Add-ons: Conditions and upgrade planning", "RefUrl": "/notes/70228"}, {"RefNumber": "2226123", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling PERSOS 100 or PERSOS 200 (SAP Screen Personas 1.0/2.0)", "RefUrl": "/notes/2226123"}, {"RefNumber": "1907126", "RefComponent": "BC-PER-RT-SIL", "RefTitle": "Logout functionality / Password complexity and change", "RefUrl": "/notes/1907126"}, {"RefNumber": "1885334", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1885334"}, {"RefNumber": "1884539", "RefComponent": "BC-PER-RT-SIL", "RefTitle": "Considerations when using SAP Screen Personas Release 1.0/ Release 2.0", "RefUrl": "/notes/1884539"}, {"RefNumber": "1845927", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1845927"}, {"RefNumber": "1842360", "RefComponent": "XX-PROJ-CDP-271", "RefTitle": "SAP Screen Personas: Prerequisites for Rel 1.0 SP1 & Rel 2.0", "RefUrl": "/notes/1842360"}, {"RefNumber": "1716826", "RefComponent": "BC-CST", "RefTitle": "Usage of the downward compatible kernel 721 (EXT)", "RefUrl": "/notes/1716826"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2964979", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "SUM IS_SELECT phase: Add-on PERSOS 200 has status UNDECIDED", "RefUrl": "/notes/2964979 "}, {"RefNumber": "1826531", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Add-on compatibility of SAP NetWeaver 7.4 - ABAP", "RefUrl": "/notes/1826531 "}, {"RefNumber": "1884539", "RefComponent": "BC-PER-RT-SIL", "RefTitle": "Considerations when using SAP Screen Personas Release 1.0/ Release 2.0", "RefUrl": "/notes/1884539 "}, {"RefNumber": "1842360", "RefComponent": "XX-PROJ-CDP-271", "RefTitle": "SAP Screen Personas: Prerequisites for Rel 1.0 SP1 & Rel 2.0", "RefUrl": "/notes/1842360 "}, {"RefNumber": "1716826", "RefComponent": "BC-CST", "RefTitle": "Usage of the downward compatible kernel 721 (EXT)", "RefUrl": "/notes/1716826 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "PERSOS", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "PERSOS", "From": "200", "To": "200", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}