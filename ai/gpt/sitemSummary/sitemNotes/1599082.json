{"Request": {"Number": "1599082", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 343, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017259912017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001599082?language=E&token=5E34F272DA53FDA17027EF1E444CA36C"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001599082", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001599082/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1599082"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 16}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.09.2022"}, "SAPComponentKey": {"_label": "Component", "value": "FI-AF-ARO"}, "SAPComponentKeyText": {"_label": "Component", "value": "Asset Retirement Obligation Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Additional Functions", "value": "FI-AF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Asset Retirement Obligation Management", "value": "FI-AF-ARO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AF-ARO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1599082 - FOM 604: Add-On Support Packages"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Information on Add-on Support Packages for FOM 604</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>FOM ,604,Financial Obligation Management, Support Packages, add-on, AOP CRT,SPAM</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You have a system with the FOM 604 Add-On installation.Add-on Support Packages (AOPs) contain corrections and fixes for the FOM 604 installation or previous Add-On Support Packages. This note contains information about Add-On Support Packages for the FOM 604 component.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Contents</strong><br /><br />I.&#160;&#160; General information<br />&#160;&#160;&#160;&#160;1. Downloading Add-On Support Packages<br />&#160;&#160;&#160;&#160;2. SPAM update<br />&#160;&#160;&#160;&#160;3. Import of&#160;&#160;Support Packages<br />&#160;&#160;&#160;&#160;4. Import prerequisites<br />II.&#160;&#160;Support Packages available for FOM 604<br />III. Problems with importing Add-On Support Packages<br />IV.&#160;&#160;After you import Add-On Support Packages.<br />V.&#160;&#160; Language<br /><br />***********************************************************************<br /><strong>I. General information</strong><br />The contents of this note are specific to FOM 604.If you have not installed FOM 604 on your SAP system, this note is not relevant for you.More information about Add-On Support Packages is available in Note160168.<br /><br />CAUTION: This note is updated constantly. Before you import Add-On Support Packages, read the current version of this note.<br /><br />1.Downloading Add-On Support Packages<br />Add-On Support Packages are available on the SAP Service Marketplace:<br />http://service.sap.com/swdc<br />&#160;&#160; -&gt; Download<br />&#160;&#160;&#160;&#160;-&gt; Support Packages and Patches<br />&#160;&#160;&#160;&#160;&#160;&#160;-&gt; Browse our Download Catalog<br />&#160;&#160;&#160;&#160;&#160;&#160; -&gt; SAP Industry-specific Components<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; -&gt; SAP ASSET RETIRE OBLIG MGT<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; -&gt; SAP ASSET RETIRE OBLIG MGT 1.0<br /><br /><strong>2.SPAM update</strong><br />Before you download and import the Add-On Support Package, you must import the latest SPAM update.<br /><br /><strong>3.Importing Support Packages</strong><br />Import Support Packages using Transaction SPAM. See the corresponding online documentation for more information.<br /><br /><strong>4.Available Support Packages for FOM 604 and Import prerequisites</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Support Package</strong></td>\r\n<td><strong>Prerequisite</strong></td>\r\n<td><strong>Release Date</strong></td>\r\n</tr>\r\n<tr>\r\n<td>SAPK-60401INFOM&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</td>\r\n<td>FOM 604 AOI</td>\r\n<td>07.10.2011</td>\r\n</tr>\r\n<tr>\r\n<td>SAPK-60402INFOM</td>\r\n<td>SAPK-60401INFOM&#160;</td>\r\n<td>11.05.2012</td>\r\n</tr>\r\n<tr>\r\n<td>SAPK-60403INFOM</td>\r\n<td>SAPK-60402INFOM</td>\r\n<td>18.03.2013</td>\r\n</tr>\r\n<tr>\r\n<td>SAPK-60404INFOM</td>\r\n<td>\r\n<p>SAPK-60403INFOM and <br />SAP_APPL 604 SP 14 or SAP_APPL 605 SP11 or SAP_APPL 606 SP9 or SAP_APPL 617 SP 02 or SAP_APPL 618 SP 02.<br />Note: In&#160;case you are in EHP 5,EHP6,EHP7 or EHP8&#160;Please download the&#160;&#160;FOM=======604 from service market place.You can refer to note 1599081.</p>\r\n</td>\r\n<td>\r\n<p>22.04.2016</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>SAPK-60405INFOM</td>\r\n<td>SAPK-60404INFOM</td>\r\n<td>\r\n<p>13.09.2022</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br /><strong>III. Problems with importing Add-On Support Packages</strong><br /><br /><strong>IV.&#160;&#160;After you import Add-On Support Packages</strong><br /><br /><strong>SUPPORT PACKAGE 02: The following generation error may occur after importing SAPK-60402INFOM</strong><br /><br />Program /FOM/CL_ARO_OBJECT============CP, Include /FOM/CL_ARO_OBJECT============CU: Syntax error in line 000014<br />The type 'IF_FAA_INTEGRATED_OBJECTS' is unknown.<br />Program /FOM/FIEHLIST, Include /FOM/CL_ARO_OBJECT============CU: Syntax error in line 000014<br />The type 'IF_FAA_INTEGRATED_OBJECTS' is unknown.<br />Program /FOM/REP_DUE_SETTLEMENTS, Include /FOM/CL_ARO_OBJECT============CU: Syntax error in line 000014<br />The type 'IF_FAA_INTEGRATED_OBJECTS' is unknown.<br />Program /FOM/REP_EVALUATE, Include /FOM/CL_ARO_OBJECT============CU: Syntax error in line 000014<br />The type 'IF_FAA_INTEGRATED_OBJECTS' is unknown.<br />Program /FOM/REP_FORECAST, Include /FOM/CL_ARO_OBJECT============CU: Syntax error in line 000014<br />The type 'IF_FAA_INTEGRATED_OBJECTS' is unknown.<br />Program /FOM/REP_SLOP, Include /FOM/CL_ARO_OBJECT============CU: Syntax error in line 000014<br />The type 'IF_FAA_INTEGRATED_OBJECTS' is unknown.<br />Program /FOM/REP_SLOP_PS, Include /FOM/CL_ARO_OBJECT============CU: Syntax error in line 000014<br />The type 'IF_FAA_INTEGRATED_OBJECTS' is unknown.<br />Program /FOM/BADI_IML_CL_ARO_EXAMPLE==CP, Include /FOM/CL_ARO_OBJECT============CU: Syntax error in line 000014<br />The type 'IF_FAA_INTEGRATED_OBJECTS' is unknown.<br />Program /FOM/SAPLAPPL_CUST, Include /FOM/CL_ARO_OBJECT============CU: Syntax error in line 000014<br />The type 'IF_FAA_INTEGRATED_OBJECTS' is unknown.<br />Program /FOM/SAPLARO_DATA_TAKEOVER, Include /FOM/CL_ARO_OBJECT============CU: Syntax error in line 000014<br />The type 'IF_FAA_INTEGRATED_OBJECTS' is unknown.<br />Program /FOM/SAPLLAE_PROCESS_METHODS, Include /FOM/CL_ARO_OBJECT============CU: Syntax error in line 000014<br />The type 'IF_FAA_INTEGRATED_OBJECTS' is unknown.<br />Program /FOM/SAPLLAE_VAL_DET_METHODS, Include /FOM/CL_ARO_OBJECT============CU: Syntax error in line 000014<br />The type 'IF_FAA_INTEGRATED_OBJECTS' is unknown.<br />Program /FOM/BADI_IF_ARO_OBJECT=======IP, Include /FOM/CL_ARO_OBJECT============CU: Syntax error in line 000014<br />The type 'IF_FAA_INTEGRATED_OBJECTS' is unknown.<br />Program /FOM/BADI_IF_ARO_OBJECT_ACC_PRIP, Include /FOM/CL_ARO_OBJECT============CU: Syntax error in line 000014<br />The type 'IF_FAA_INTEGRATED_OBJECTS' is unknown.<br />Program /FOM/ARO_PSDOCITEMS, Include /FOM/CL_ARO_OBJECT============CU: Syntax error in line 000014<br />The type 'IF_FAA_INTEGRATED_OBJECTS' is unknown.<br /><br /><strong>Note 1646197 is a prerequisite</strong><br /><br /><strong>SUPPORT PACKAGE 03:</strong><br /><strong>If you are a customer with a productive installation (based in SP02 or less) of Asset Retirement Obligation Management, you should take care of the instructions which are given by note 1800873! </strong><br /><br />Note 1800873 includes an enhancement of the cash flow (introduction of new fields).Customers with a productive installation of Asset Retirement Obligation&#160;&#160;Management shall open a Support Ticket to SAP Development Support (component FI-AF-ARO) after Support Package installation to get assistance in populating the new cash flow fields for productive cash flow database entries.</p>\r\n<p><br /><strong>V.&#160;&#160;Language Support</strong><br />FOM 604 supports German and&#160;&#160;English. This language is contained in the installation.</p>\r\n<p>FOM 604 supports Russian language from SP04 onwards. The translation of Russian language in contained in SAPK-60404INFOM.<br />FOM 604 supports French language from SP05 onwards. The translation of French language in contained in SAPK-60405INFOM.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-UPG-ADDON (Upgrade Add-On Components)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I039876)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D065489)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001599082/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001599082/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001599082/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001599082/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001599082/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001599082/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001599082/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001599082/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001599082/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1800873", "RefComponent": "FI-AF-ARO", "RefTitle": "Improved Reporting on Asset Retirement Obligation Cash Flow", "RefUrl": "/notes/1800873"}, {"RefNumber": "1599155", "RefComponent": "FI-AF-ARO", "RefTitle": "SAP Asset Retirement Obligation Management :Overview Note", "RefUrl": "/notes/1599155"}, {"RefNumber": "1599081", "RefComponent": "FI-AF-ARO", "RefTitle": "FOM 604:Installation on SAP ECC 6.0 EHP4/5/6/7/8", "RefUrl": "/notes/1599081"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1599081", "RefComponent": "FI-AF-ARO", "RefTitle": "FOM 604:Installation on SAP ECC 6.0 EHP4/5/6/7/8", "RefUrl": "/notes/1599081 "}, {"RefNumber": "1800873", "RefComponent": "FI-AF-ARO", "RefTitle": "Improved Reporting on Asset Retirement Obligation Cash Flow", "RefUrl": "/notes/1800873 "}, {"RefNumber": "1599155", "RefComponent": "FI-AF-ARO", "RefTitle": "SAP Asset Retirement Obligation Management :Overview Note", "RefUrl": "/notes/1599155 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "FOM", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}