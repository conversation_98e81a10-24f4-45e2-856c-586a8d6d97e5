{"Request": {"Number": "937389", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 420, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005500532017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000937389?language=E&token=D8DA2E80C2D2751FC4F32D575380C351"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000937389", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000937389/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "937389"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 15}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "19.09.2008"}, "SAPComponentKey": {"_label": "Component", "value": "FI-GL-GL-RP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Reconciliation CO-FI"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Accounting", "value": "FI-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "FI-GL-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Reconciliation CO-FI", "value": "FI-GL-GL-RP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-GL-RP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "937389 - COEP line item display incorrect after upgrade"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>With the initial delivery of ERP2005, the table COEP is delivered with the fields GEBER, PGEBER and GRANT_NBR, and PGRANT_NBR with the initialization indicator set.<br />This results in very long runtimes during the conversion of the table COEP when you upgrade from Release 4.6C or lower.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>KALE, GD13, GD23, ledger, 3A, reconciliation ledger, SAPLKALR, KALE, KALC,<br />COEP, COVP, NULL, drill down, K5 075,<br />GEBER, PGEBER, GRANT_NBR, PGRANT_NBR</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>After you implement this note, the initialization indicator is unchecked. (This is delivered with Support Package 5).<br />This significantly improves the runtime of the upgrade. However, it can result in a problem with line item reports as described in Note 790616.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The following constellations may occur:</p> <OL>1. You previously upgraded from Release 4.6C or lower to a release between 4.70 and ERP2004. Afterwards, you converted a table as described in Note 790616. If you now upgrade to ERP2005 (or higher), you DO NOT have to do anything else.</OL> <OL>2. You previously upgraded from Release 4.6C or lower to a release between 4.70 and ERP2004. Afterwards, you did NOT implement Note 790616.</OL> <OL><OL>a) If you now upgrade to ERP2005 &lt; Support Package 5, the database is converted automatically during the upgrade. To avoid this, you have to import Support Package 5 (or implement the solution described below for the required modification adjustment).</OL></OL><p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you now upgrade to ERP2005 Support Package 5 or higher, you must convert a table as described in Note 790616 or execute the attached report FCOM_COEP_ENHANCE or ZFCOM_START_PARALLEL. <OL><OL>b) You can execute the report BEFORE or AFTER the upgrade.</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Refer to the detailed information about how to execute FCOM_COEP_ENHANCE or ZFCOM_START_PARALLEL (see below). <OL>3. You upgrade from Release 4.6C or lower to ERP2005 or higher.</OL> <OL><OL>a) In ERP2005, you have Support Package 4 or lower.</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In this case, the database is converted automatically. To avoid this, you have to import Support Package 5 (or implement the solution described below for the required modification adjustment). <OL><OL>b) In ERP2005, you have Support Package 5 or higher.</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In this case, you must either convert a table in accordance with Note 790616, or execute the attached report FCOM_COEP_ENHANCE or ZFCOM_START_PARALLEL after the upgrade. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Refer to the detailed information about how to execute FCOM_COEP_ENHANCE or ZFCOM_START_PARALLEL (see below). <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<b>Detailed information about the report FCOM_COEP_ENHANCE</b><br /> <OL>1. How it works:</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The report imports all rows of the table COEP that still have the value 'Undefined' in the fields GEBER, PGEBER, GRANT_NBR, PGRANT_NBR, SEGMENT and PSEGMENT. These fields are filled with 'Space' and are updated in the database. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Depending on the volume of data, this conversion can take some time (scale: one hour for 10 million COEP records). <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The report can be restarted at any time. If you cannot block the system against postings when the report is running, the report may terminate (if a posting posts to the same database cluster that the report is reading, the report loses the database cursor and the system terminates with an SQL short dump). <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;However, all data handled up to that point is converted. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If this happens, simply schedule the report again. <OL>2. Urgency of execution:</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the report has not been executed yet, report problems may occur as described in Note 790616.&#x00A0;&#x00A0; However, NO inconsistencies occur in the system. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;When there are very large datasets in the table COEP, you may no longer be able to run the report directly after the upgrade due to a lack of time. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In this case, you could, for example, execute the report on one of the following weekends. You can also cancel the report after a certain runtime (for example, at the end of the weekend) and reschedule it for the following weekend. Then, only the records still missing are processed.<br /><br /> <b>Detailed information about the report ZFCOM_START_PARALLEL</b><br /> <OL>1. How it works:</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The report starts the report ZFCOM_COEP_ENHANCE_PARALLEL in parallel jobs. This means the table COEP can be converted faster (if you have the relevant hardware). The report ZFCOM_COEP_ENHANCE_PARALLEL works in exactly the same way as the report FCOM_COEP_ENHANCE. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you change the name of the report ZFCOM_COEP_ENHANCE_PARALLEL, you must change the source code line <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ld_repid = 'ZFCOM_COEP_ENHANCE_PARALLEL'. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;in the form routine START_PROCESSING of the report ZFCOM_START_PARALLEL accordingly. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The following parameters can be assigned to the supporting program ZFCOM_START_PARALLEL, which starts the parallel jobs: <OL><OL>a) Fiscal year (parameter name: gjahr): If a fiscal year is assigned, the system processes records of the relevant fiscal year only.</OL></OL> <OL><OL>b) Number of jobs (parameter name: nrjobs): Number of jobs that are restarted in parallel.</OL></OL> <OL><OL>c) Test run (parameter name: testrun): If this parameter is set to X, the system executes parallel jobs in the test run.</OL></OL> <OL>2. Urgency of execution:</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The urgency is the same as for the report FCOM_COEP_ENHANCE. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<b>Required modification adjustment of the table COEP</b><br /> <p><br />To prevent the table from being converted automatically during the upgrade, you can implement the following solution (this solution is required only if you have Support Package 4 or lower).</p> <OL>1. Make a dummy change to the table COEP (for example, change the short text).</OL> <OL>2. Now, upgrade to ERP2005 SAP_APPL Support Package 4 or lower.</OL> <OL>3. Thanks to this modification, the table COEP is available in SPDD.</OL> <OL>4. Use transaction SPDD to change the table and REMOVE the initialization indicator in transaction SE11 for the fields GEBER, PGEBER, GRANT_NBR and PGRANT_NBR. Save your changes and continue with the upgrade.</OL> <OL>5. Note that you must now execute the report FCOM_COEP_ENHANCE (and take the above notes into consideration when you do so).</OL> <b>Interaction with a Unicode conversion</b><br /> <p><br />During a Unicode conversion, the data is written to the file system using r3load.&#x00A0;&#x00A0; The database tables are deleted and recreated. ALL fields are initialized when the database tables are recreated. If the data is then imported to the table again, it is all initialized correctly.<br />This means the report FCOM_COEP_ENHANCE is no longer required after a Unicode conversion.&#x00A0;&#x00A0;Therefore, we recommend that you carry out the Unicode conversion FIRST.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CO-OM-IS (Information System, iViews)"}, {"Key": "Other Components", "Value": "FI-SL-SL (Basic Functions)"}, {"Key": "Other Components", "Value": "XX-PROJ-IMS-UPGR (Application Specific Upgrade Tools)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D022188)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D019468)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000937389/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000937389/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000937389/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000937389/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000937389/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000937389/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000937389/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000937389/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000937389/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410"}, {"RefNumber": "960269", "RefComponent": "FI-GL-GL-RP", "RefTitle": "FCOM_COEP_ENHANCE: Short dump COMPUTE_INT_PLUS_OVERFLOW", "RefUrl": "/notes/960269"}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971"}, {"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092"}, {"RefNumber": "790616", "RefComponent": "FI-GL-GL-RP", "RefTitle": "KALE: After upgrade line item display COEP is incorrect", "RefUrl": "/notes/790616"}, {"RefNumber": "1292069", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP 6.0 EHP4 SR1 ABAP", "RefUrl": "/notes/1292069"}, {"RefNumber": "1156968", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to EHP 4 for SAP ERP 6.0 ABAP", "RefUrl": "/notes/1156968"}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092 "}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904 "}, {"RefNumber": "1156968", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to EHP 4 for SAP ERP 6.0 ABAP", "RefUrl": "/notes/1156968 "}, {"RefNumber": "1292069", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP 6.0 EHP4 SR1 ABAP", "RefUrl": "/notes/1292069 "}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971 "}, {"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410 "}, {"RefNumber": "790616", "RefComponent": "FI-GL-GL-RP", "RefTitle": "KALE: After upgrade line item display COEP is incorrect", "RefUrl": "/notes/790616 "}, {"RefNumber": "960269", "RefComponent": "FI-GL-GL-RP", "RefTitle": "FCOM_COEP_ENHANCE: Short dump COMPUTE_INT_PLUS_OVERFLOW", "RefUrl": "/notes/960269 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 500", "SupportPackage": "SAPKH50021", "URL": "/supportpackage/SAPKH50021"}, {"SoftwareComponentVersion": "SAP_APPL 600", "SupportPackage": "SAPKH60005", "URL": "/supportpackage/SAPKH60005"}, {"SoftwareComponentVersion": "SAP_APPL 600", "SupportPackage": "SAPKH60015", "URL": "/supportpackage/SAPKH60015"}, {"SoftwareComponentVersion": "SAP_APPL 602", "SupportPackage": "SAPKH60205", "URL": "/supportpackage/SAPKH60205"}, {"SoftwareComponentVersion": "SAP_APPL 603", "SupportPackage": "SAPKH60304", "URL": "/supportpackage/SAPKH60304"}, {"SoftwareComponentVersion": "SAP_APPL 604", "SupportPackage": "SAPKH60402", "URL": "/supportpackage/SAPKH60402"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 2, "URL": "/corrins/0000937389/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_APPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Application&nbsp;&nbsp; |<br/>| Release 600&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKH60014&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 602&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKH60204&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 603&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKH60303&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 604&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKH60401&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/>After you have implemented the correction instructions and activated the objects, perform the following steps:</P> <UL><LI>Call transaction <B>SE38</B>.</LI></UL> <UL><LI>Enter the program name <B>FCOM_COEP_ENHANCE_PARALLEL</B>.</LI></UL> <UL><LI>Select <B>Text elements</B> and choose <B>Change</B>.</LI></UL> <UL><LI>Navigate to the <B>Selection texts </B>tab page.</LI></UL> <UL><LI>For the entries <B>GJAHR</B>, <B>TAB_BEL</B> and <B>TAB_KOKR</B>, select the <B>Dictionary Ref.</B> checkbox.</LI></UL> <UL><LI>Enter the text <B>Test run </B>for TESTRUN.</LI></UL> <UL><LI><B>Activate</B>your changes.</LI></UL> <UL><LI>Call transaction <B>SE38</B>.</LI></UL> <UL><LI>Enter the program name <B>FCOM_START_PARALLEL </B>.</LI></UL> <UL><LI>Select <B>Text elements</B> and choose <B>Change</B>.</LI></UL> <UL><LI>Navigate to the <B>Selection texts </B>tab page.</LI></UL> <UL><LI>For the entry <B>GJAHR </B>, select the <B>Dictionary Ref.</B> checkbox.</LI></UL> <UL><LI>Maintain the following entries:</LI><br/> <TABLE><TR><TH ALIGN=LEFT>Name</TH><TH ALIGN=LEFT> Text</TH></TR> <TR><TD>NRJOBS</TD><TD> Number of parallel jobs</TD></TR> <TR><TD>TESTRUN</TD><TD> Test run</TD></TR> </TABLE></UL> <P></P> <UL><LI><B>Activate </B>your changes.</LI></UL> <P></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "600", "ValidTo": "604", "Number": "937389 ", "URL": "/notes/937389 ", "Title": "COEP line item display incorrect after upgrade", "Component": "FI-GL-GL-RP"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "960269", "RefTitle": "FCOM_COEP_ENHANCE: Short dump COMPUTE_INT_PLUS_OVERFLOW", "RefUrl": "/notes/0000960269"}]}}}}}