{"Request": {"Number": "838800", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 667, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015884032017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=87AA11B6D1D566A2C8DA2A6B74A11A5D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "838800"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "31.03.2011"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-OT-MDX"}, "SAPComponentKeyText": {"_label": "Component", "value": "MDX,OLAP-BA<PERSON>,OLE DB for OLAP"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Explorer", "value": "BW-BEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "OLAP Technology", "value": "BW-BEX-OT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "MDX,OLAP-BA<PERSON>,OLE DB for OLAP", "value": "BW-BEX-OT-MDX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT-MDX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "838800 - Composite SAP Note for consulting notes about MDX/OLAP BAPIs"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note lists all notes that deal with BW-specific MDX questions.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Multidimensional Expressions, MDX, online analytical processing, OLAP, BAPI, XML/A, XMLA, SAP VARIABLES, time-dependent<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Some solutions work only in the OLAP BAPIs but not in OLE DB for OLAP (ODBO) for XML for Analysis (XMLA).<br />We recommend that you refer to the MDX specification, for example using one of the following links:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;http://msdn.microsoft.com/en-us/library/aa216767(v=sql.80). aspx<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;http://msdn.microsoft.com/en-us/library/ms145506.aspx<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The \"Related Notes\" section of this note contains all consulting notes and composite SAP Notes that have been created for the MDX interface. This note is updated when necessary. This means that more notes are likely to be added to the \"Related Notes\" section.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BEX-ET-ODB (OLE DB Provider)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D029975)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D029975)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "859351", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Using PARALLELPERIOD without time hierarchy", "RefUrl": "/notes/859351"}, {"RefNumber": "857300", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Generation of a NAMED SET from ..._GET_MEMBERS", "RefUrl": "/notes/857300"}, {"RefNumber": "845955", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: No parallel execution of several MDX commands", "RefUrl": "/notes/845955"}, {"RefNumber": "834196", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Time dependency in MDX", "RefUrl": "/notes/834196"}, {"RefNumber": "830557", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Using compression in XML/A", "RefUrl": "/notes/830557"}, {"RefNumber": "820925", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX - Restrictions", "RefUrl": "/notes/820925"}, {"RefNumber": "774552", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Optimization of member search using .ITEM", "RefUrl": "/notes/774552"}, {"RefNumber": "609314", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Query on attribute values in the MEMBERS row set", "RefUrl": "/notes/609314"}, {"RefNumber": "598045", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Using REF_DIM and REF_HRY in the VARIABLES rowset", "RefUrl": "/notes/598045"}, {"RefNumber": "323779", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "OLE DB for OLAP features and restrictions", "RefUrl": "/notes/323779"}, {"RefNumber": "187646", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "ODBO: BW Partner Support Strategy", "RefUrl": "/notes/187646"}, {"RefNumber": "1722152", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: External hierarchies, virtual time hierarchies", "RefUrl": "/notes/1722152"}, {"RefNumber": "1704745", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Web Intelligence calls multidimensional BAPIs", "RefUrl": "/notes/1704745"}, {"RefNumber": "1678821", "RefComponent": "BW-BEX-ET-ODB", "RefTitle": "MDX: Error message when using ODBO provider", "RefUrl": "/notes/1678821"}, {"RefNumber": "164214", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Required authorizations for use of ODBO", "RefUrl": "/notes/164214"}, {"RefNumber": "1628813", "RefComponent": "BW-BEX-ET-ODB", "RefTitle": "Analyzing problems with SAP BW OLE DB Provider in Excel", "RefUrl": "/notes/1628813"}, {"RefNumber": "1596684", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Transaction MDXTEST and BW statistics event 1", "RefUrl": "/notes/1596684"}, {"RefNumber": "1587415", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Details about the behaviour of the filter function", "RefUrl": "/notes/1587415"}, {"RefNumber": "1585925", "RefComponent": "BW-BEX-ET-ODB", "RefTitle": "ODBO: Restrictions for Excel 2007 and Excel 2010", "RefUrl": "/notes/1585925"}, {"RefNumber": "1576508", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Overview of MDX-specific RSADMIN parameters", "RefUrl": "/notes/1576508"}, {"RefNumber": "1574725", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Consulting note on display hierarchies", "RefUrl": "/notes/1574725"}, {"RefNumber": "1572018", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Execution types in transaction MDXTEST - BW 7.00-7.11", "RefUrl": "/notes/1572018"}, {"RefNumber": "1567394", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Consulting note for sort sequence", "RefUrl": "/notes/1567394"}, {"RefNumber": "1564866", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Variables and displayed hierarchy levels", "RefUrl": "/notes/1564866"}, {"RefNumber": "1530858", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Runtimes are long when the member rowset is determined", "RefUrl": "/notes/1530858"}, {"RefNumber": "1526045", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Non-unique hierarchies, link nodes, and structures", "RefUrl": "/notes/1526045"}, {"RefNumber": "1521799", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Structures and non empty", "RefUrl": "/notes/1521799"}, {"RefNumber": "1512632", "RefComponent": "BI-RA-WBI", "RefTitle": "Sparse data and restricted key figures in WebI reports", "RefUrl": "/notes/1512632"}, {"RefNumber": "149809", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Procedure f. errors in OLE DB for OLAP Interface", "RefUrl": "/notes/149809"}, {"RefNumber": "1491809", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Properties from calculated members", "RefUrl": "/notes/1491809"}, {"RefNumber": "1446246", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Composite SAP Note for flattening API based on basXML", "RefUrl": "/notes/1446246"}, {"RefNumber": "1412800", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX and BW authorizations", "RefUrl": "/notes/1412800"}, {"RefNumber": "1381821", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Memory and Performance Analysis for MDX", "RefUrl": "/notes/1381821"}, {"RefNumber": "1272044", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Analyzing MDX problems", "RefUrl": "/notes/1272044"}, {"RefNumber": "1253646", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Handling link nodes", "RefUrl": "/notes/1253646"}, {"RefNumber": "1156101", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Composite SAP Note about incorrect data", "RefUrl": "/notes/1156101"}, {"RefNumber": "1142664", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Composite SAP Note about performance improvements", "RefUrl": "/notes/1142664"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1156101", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Composite SAP Note about incorrect data", "RefUrl": "/notes/1156101 "}, {"RefNumber": "1142664", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Composite SAP Note about performance improvements", "RefUrl": "/notes/1142664 "}, {"RefNumber": "1576508", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Overview of MDX-specific RSADMIN parameters", "RefUrl": "/notes/1576508 "}, {"RefNumber": "1585925", "RefComponent": "BW-BEX-ET-ODB", "RefTitle": "ODBO: Restrictions for Excel 2007 and Excel 2010", "RefUrl": "/notes/1585925 "}, {"RefNumber": "1722152", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: External hierarchies, virtual time hierarchies", "RefUrl": "/notes/1722152 "}, {"RefNumber": "820925", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX - Restrictions", "RefUrl": "/notes/820925 "}, {"RefNumber": "1253646", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Handling link nodes", "RefUrl": "/notes/1253646 "}, {"RefNumber": "1704745", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Web Intelligence calls multidimensional BAPIs", "RefUrl": "/notes/1704745 "}, {"RefNumber": "1446246", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Composite SAP Note for flattening API based on basXML", "RefUrl": "/notes/1446246 "}, {"RefNumber": "1530858", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Runtimes are long when the member rowset is determined", "RefUrl": "/notes/1530858 "}, {"RefNumber": "1678821", "RefComponent": "BW-BEX-ET-ODB", "RefTitle": "MDX: Error message when using ODBO provider", "RefUrl": "/notes/1678821 "}, {"RefNumber": "1567394", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Consulting note for sort sequence", "RefUrl": "/notes/1567394 "}, {"RefNumber": "1572018", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Execution types in transaction MDXTEST - BW 7.00-7.11", "RefUrl": "/notes/1572018 "}, {"RefNumber": "1574725", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Consulting note on display hierarchies", "RefUrl": "/notes/1574725 "}, {"RefNumber": "1272044", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Analyzing MDX problems", "RefUrl": "/notes/1272044 "}, {"RefNumber": "1628813", "RefComponent": "BW-BEX-ET-ODB", "RefTitle": "Analyzing problems with SAP BW OLE DB Provider in Excel", "RefUrl": "/notes/1628813 "}, {"RefNumber": "1596684", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Transaction MDXTEST and BW statistics event 1", "RefUrl": "/notes/1596684 "}, {"RefNumber": "1587415", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Details about the behaviour of the filter function", "RefUrl": "/notes/1587415 "}, {"RefNumber": "1526045", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Non-unique hierarchies, link nodes, and structures", "RefUrl": "/notes/1526045 "}, {"RefNumber": "1564866", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Variables and displayed hierarchy levels", "RefUrl": "/notes/1564866 "}, {"RefNumber": "1381821", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Memory and Performance Analysis for MDX", "RefUrl": "/notes/1381821 "}, {"RefNumber": "1521799", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Structures and non empty", "RefUrl": "/notes/1521799 "}, {"RefNumber": "1512632", "RefComponent": "BI-RA-WBI", "RefTitle": "Sparse data and restricted key figures in WebI reports", "RefUrl": "/notes/1512632 "}, {"RefNumber": "1491809", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Properties from calculated members", "RefUrl": "/notes/1491809 "}, {"RefNumber": "1412800", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX and BW authorizations", "RefUrl": "/notes/1412800 "}, {"RefNumber": "323779", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "OLE DB for OLAP features and restrictions", "RefUrl": "/notes/323779 "}, {"RefNumber": "164214", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Required authorizations for use of ODBO", "RefUrl": "/notes/164214 "}, {"RefNumber": "834196", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Time dependency in MDX", "RefUrl": "/notes/834196 "}, {"RefNumber": "845955", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: No parallel execution of several MDX commands", "RefUrl": "/notes/845955 "}, {"RefNumber": "859351", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Using PARALLELPERIOD without time hierarchy", "RefUrl": "/notes/859351 "}, {"RefNumber": "857300", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Generation of a NAMED SET from ..._GET_MEMBERS", "RefUrl": "/notes/857300 "}, {"RefNumber": "830557", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Using compression in XML/A", "RefUrl": "/notes/830557 "}, {"RefNumber": "774552", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Optimization of member search using .ITEM", "RefUrl": "/notes/774552 "}, {"RefNumber": "598045", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Using REF_DIM and REF_HRY in the VARIABLES rowset", "RefUrl": "/notes/598045 "}, {"RefNumber": "609314", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Query on attribute values in the MEMBERS row set", "RefUrl": "/notes/609314 "}, {"RefNumber": "149809", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Procedure f. errors in OLE DB for OLAP Interface", "RefUrl": "/notes/149809 "}, {"RefNumber": "187646", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "ODBO: BW Partner Support Strategy", "RefUrl": "/notes/187646 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "30B", "To": "30B", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "310", "To": "310", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "350", "To": "350", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "700", "To": "700", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}