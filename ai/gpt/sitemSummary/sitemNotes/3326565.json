{"Request": {"Number": "3326565", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 465, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000598352023"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003326565?language=E&token=D60D84AF233EE51E8130473F90FA96C1"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003326565", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0003326565/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3326565"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "01.08.2023"}, "SAPComponentKey": {"_label": "Component", "value": "MM-FIO-PUR-ANA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Fiori UI for Purchasing Analytics"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Fiori UI for Materials Management", "value": "MM-FIO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-FIO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "<PERSON>ori UI for Purchasing", "value": "MM-FIO-PUR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-FIO-PUR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Fiori UI for Purchasing Analytics", "value": "MM-FIO-PUR-ANA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-FIO-PUR-ANA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "3326565 - Upgrade issue in MMPUR_ANA_EKET table from OP2022: CI Include merged into EEW for EKKO & EKPO"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Activation issue with duplicate fields in analytical table MMPUR_ANA_EKET while upgrade to S/4HANA 2022</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>MMPUR_ANA_EKET, CI_EKKODB, CI_EKPODB,&#160;phase ACT_UPG</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Technically until S/4HANA 2022, the CI includes of&#160;DB tables EKKO &amp; EKPO were not included in respective extension includes EKKO_INCL_EEW_PS &amp;&#160;EKPO_INCL_EEW_PS. Since release S/4HANA 2022, CI include got merged into respective extension includes.</p>\r\n<p>Analytical table (MMPUR_ANA_EKET) is a denormalised table with EKKO,EKPO and EKET data and it also contains the&#160;extension includes of EKKO &amp; EKPO.</p>\r\n<p>SAP proposes to use unique field names across CI Includes of EKKO &amp; EKPO (see also note 2899836 and 3101906). If you do not follow this proposal from S/4HANA 2022 onwards there will be an error during upgrade on the analytical table (due to field name conflict).</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The solution for this is to implement the attached correction in source release before upgrading to S/4HANA 2022. There are two new&#160;specific extension stuctures &amp; one report delivered as part of this note. Once the note is implemented report&#160;MMPUR_ANA_EKET_GEN_EEW_FIELDS has to be executed and verified whether the custom fields of includes EKKO_INCL_EEW_PS &amp;&#160;EKPO_INCL_EEW_PS&#160;are appended to&#160;EKKO_INCL_EEW_PS_ANA &amp;&#160;EKPO_INCL_EEW_PS_ANA respectively. The new specific extension structures&#160;EKKO_INCL_EEW_PS_ANA &amp;&#160;EKPO_INCL_EEW_PS_ANA will only contain custom fields from extension includes EKKO_INCL_EEW_PS &amp;&#160;EKPO_INCL_EEW_PS, but not from CI includes CI_EKKODB &amp; CI_EKPODB.</p>\r\n<p>If custom fields from&#160;CI_EKKODB &amp; CI_EKPODB are also required to be included in table MMPUR_ANA_EKET, these need be added manually via append mechanism.<br /><br />Once the new structures with custom fields are ready in source release, the upgrade can happen where old&#160;extension includes&#160;EKKO_INCL_EEW_PS &amp;&#160;EKPO_INCL_EEW_PS in table MMPUR_ANA_EKET are replaced with new includes&#160;EKKO_INCL_EEW_PS_ANA &amp;&#160;EKPO_INCL_EEW_PS_ANA.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I560652)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I045817)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0003326565/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003326565/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003326565/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003326565/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003326565/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003326565/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003326565/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003326565/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003326565/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3349593", "RefComponent": "MM-FIO-PUR-ANA", "RefTitle": "DDIC change for activation issue with duplicate fields in table MMPUR_ANA_EKET", "RefUrl": "/notes/3349593 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "106", "To": "106", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "107", "To": "107", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "S4CORE 102", "SupportPackage": "SAPK-10213INS4CORE", "URL": "/supportpackage/SAPK-10213INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 103", "SupportPackage": "SAPK-10311INS4CORE", "URL": "/supportpackage/SAPK-10311INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 104", "SupportPackage": "SAPK-10409INS4CORE", "URL": "/supportpackage/SAPK-10409INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 105", "SupportPackage": "SAPK-10507INS4CORE", "URL": "/supportpackage/SAPK-10507INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 106", "SupportPackage": "SAPK-10605INS4CORE", "URL": "/supportpackage/SAPK-10605INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 107", "SupportPackage": "SAPK-10703INS4CORE", "URL": "/supportpackage/SAPK-10703INS4CORE"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "S4CORE", "NumberOfCorrin": 12, "URL": "/corrins/0003326565/19773"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 12, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "S4CORE", "ValidFrom": "102", "ValidTo": "102", "Number": "3326565 ", "URL": "/notes/3326565 ", "Title": "Upgrade issue in MMPUR_ANA_EKET table from OP2022: CI Include merged into EEW for EKKO & EKPO", "Component": "MM-FIO-PUR-ANA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "103", "ValidTo": "103", "Number": "3326565 ", "URL": "/notes/3326565 ", "Title": "Upgrade issue in MMPUR_ANA_EKET table from OP2022: CI Include merged into EEW for EKKO & EKPO", "Component": "MM-FIO-PUR-ANA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "104", "ValidTo": "104", "Number": "3326565 ", "URL": "/notes/3326565 ", "Title": "Upgrade issue in MMPUR_ANA_EKET table from OP2022: CI Include merged into EEW for EKKO & EKPO", "Component": "MM-FIO-PUR-ANA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "105", "ValidTo": "105", "Number": "3326565 ", "URL": "/notes/3326565 ", "Title": "Upgrade issue in MMPUR_ANA_EKET table from OP2022: CI Include merged into EEW for EKKO & EKPO", "Component": "MM-FIO-PUR-ANA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "106", "ValidTo": "106", "Number": "3326565 ", "URL": "/notes/3326565 ", "Title": "Upgrade issue in MMPUR_ANA_EKET table from OP2022: CI Include merged into EEW for EKKO & EKPO", "Component": "MM-FIO-PUR-ANA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "107", "ValidTo": "107", "Number": "3326565 ", "URL": "/notes/3326565 ", "Title": "Upgrade issue in MMPUR_ANA_EKET table from OP2022: CI Include merged into EEW for EKKO & EKPO", "Component": "MM-FIO-PUR-ANA"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}