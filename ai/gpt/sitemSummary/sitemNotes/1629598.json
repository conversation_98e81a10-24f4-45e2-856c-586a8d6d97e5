{"Request": {"Number": "1629598", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 232, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017306612017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001629598?language=E&token=251ADC546A7982F4ED9F278ADE7A5CD4"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001629598", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001629598/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1629598"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 13}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.01.2016"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CST"}, "SAPComponentKeyText": {"_label": "Component", "value": "Client/Server Technology"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Client/Server Technology", "value": "BC-CST", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CST*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1629598 - SAP Kernel 720 will replace older kernel versions"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><span style=\"text-decoration: underline;\"><strong>Attention:</strong></span></p>\r\n<p>As of May 2015, <strong>Kernel 720 went Out of Maintenance</strong>, for details see SAP Note <a target=\"_blank\" href=\"/notes/1975687\">1975687</a>. The latest 720 patch is patch&#160;level 800 (see SAP Note <a target=\"_blank\" href=\"/notes/\">2158874</a>).&#160;<strong>Kernel 720 is replaced by&#160;downward&#160;compatible&#160;Kernel 721 (EXT)</strong>&#160;<strong>becoming the new \"standard\" kernel</strong>, and downward compatible kernel 722 (EXT) the new innovation kernel. Kernel 720 can still be used, but if an issue occurs with kernel 720, a fix will be only provided for SAP kernel 721 and 722.</p>\r\n<p>Our recommendation is an upgrade to the&#160;latest available&#160;721 Stack Kernel. As described in <a target=\"_blank\" href=\"/notes/1728283\">SAP Note 1728283</a>, SAP Kernel 720 can be replaced by SAP Kernel 721 everywhere, and SAP Kernel 721 can be installed by a simple kernel patch.</p>\r\n<p>New&#160;Stack Kernels will be announced on <a target=\"_blank\" href=\"http://scn.sap.com/docs/DOC-53415\">http://scn.sap.com/docs/DOC-53415</a>.&#160;Further information on upgrading from 720 to 721 can be found in SAP Note <a target=\"_blank\" href=\"/notes/1975687\">1975687</a>.</p>\r\n<p>----</p>\r\n<p>The SAP kernel versions 700, 701, 710 and 711 will run out of maintenance by August 31st, 2012. These kernel versions will be replaced by the SAP kernel 720, which is fully downward-compatible. You can continue using your current kernel after that date, but if an issue occurs with your current kernel, a fix will only be provided for the SAP kernel 720.<br />Usage of the SAP kernel 720 for SAP NetWeaver releases based on the older kernel versions will be made available in a staged approach, starting in Q4 2011.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Kernel, Downward Compatible Kernel, AKK, DCK, HANA, NewDB, RKS, Rolling Kernel Switch, WEBGUI, SAP GUI for HTML, unified rendering, security session, ACL, access control list, flexible license generator, DBSL, SAPHOSTAGENT, network statistics, 2090, FOR ALL ENTRIES, R3load. 7.00, 7.01, 7.10, 7.11, 7.20</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><br /><strong>1. System requirements</strong><br />This SAP Note applies to all systems based on SAP NetWeaver 7.0, SAP EHP1 for SAP NetWeaver 7.0 (i.e., 7.01), SAP NetWeaver 7.1 and SAP EHP1 for SAP NetWeaver 7.1 (i.e., 7.11).<br />The SAP kernel 720 will be available for all platform combinations (DB/OS) for which the SAP kernel versions 700, 701, 710 and 711 are available.<br />For newer DB/OS combinations please use Kernel 720_EXT. Please refer to SAP Note 1553301 for an in-depth description of the 720_EXT kernel.<br /><br /><strong>2. Benefits of using SAP kernel 720</strong><br />Besides the fact that the 720 version of the SAP kernel is becoming the de facto standard throughout the SAP installed base and thus has the highest coverage both within SAP and among its customer base, you may benefit from the many additional features that were built into the SAP kernel 720 and have proven to be very useful in their supported environments. The features below are available without any further upgrade of your ABAP stack. Further enhancements based on functionality contained in the 720 kernel may be made available through SAP support SAP Notes in the future. In the following, we distinguish between new features and enhancements of already existing functionality.<br /><br /><strong>2.1. New Features</strong></p>\r\n<ul>\r\n<li>The Rolling Kernel Switch allows to minimize the planned downtimes of SAP systems during kernel patch installations. See SAP Note 953653 for details. New for NW 7.0, EHP1 for NW 7.0.</li>\r\n</ul>\r\n<ul>\r\n<li>The Flexible License Generator offers a solution for high availability environments and for adaptive computing, since the license is not linked to a hardware ID of a particular message server anymore. New for NW 7.0.</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;See the SAP Online help:</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;http://help.sap.com/saphelp_nw70ehp2/helpdata/en/20/384ef6bd054d5aae1ddd6f67e07a9e/content.htm</p>\r\n<ul>\r\n<li>Native support for accessing HANA from the ABAP stack which is necessary for HANA-based applications like the CO-PA accelerator. See SAP Notes 1597627 and 1627077. New for NW 7.0, EHP1 for NW 7.0, NW 7.1, EHP1 for NW 7.1.</li>\r\n</ul>\r\n<ul>\r\n<li>SAP GUI for HTML (WEBGUI) with unified rendering. See Note 1637287. New with NW 7.0, EHP1 for NW 7.0, NW 7.1, EHP1 for NW 7.1.</li>\r\n</ul>\r\n<ul>\r\n<li>Various new features in the SAP database interface/DBSL:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>MS SQL Server: support of statements with more than 2090 parameters (Note 1552952). New for NW 7.0.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>DB2/LUW (DB6): new configuration option for FOR ALL ENTRIES statements with UNION ALL transformation allows to reduce the complexity of statements (Note 1456251), new with NW 7.0, EHP1 for NW 7.0, NW 7.1, EHP1 for NW 7.1.</li>\r\n</ul>\r\n</ul>\r\n<p><br /><strong>2.2. Enhancements of existing features</strong><br /><br /><strong>2.2.1. Security Enhancements</strong></p>\r\n<ul>\r\n<li>Enhanced security configurations are available by the use of access control lists (ACLs) in communication programs:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP gateway</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP dispatcher</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>ICM</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP message server</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Learn more about this in the SAP online help:</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;http://help.sap.com/saphelp_nw70ehp2/helpdata/en/d7/b5d0b1732746b094acf4c1b316bdd5/content.htm</p>\r\n<ul>\r\n<li>IBM i: Improved system security through signed kernel programs (Note 1581170), new with NW 7.0, EHP1 for NW 7.0, NW 7.1, EHP1 for NW 7.1.</li>\r\n</ul>\r\n<p><br /><strong>2.2.2. Supportability Enhancements</strong></p>\r\n<ul>\r\n<li>Improved monitoring on virtualized and cloud environments (Linux: Note 1102124, Windows: Note 1409604) by using the SAPHOSTAGENT version 720.</li>\r\n</ul>\r\n<ul>\r\n<li>IBM i: Simplified system administration and improved kernel maintenance:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>unified kernel structure and handling for all 7.x based SAP technology releases (Note 1078134), new with NW 7.0, EHP1 for NW 7.0;</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>simplified kernel structure and unified kernel maintenance command (Note 1632755), new with NW 7.0, EHP1 for NW 7.0, NW 7.1, EHP1 for NW 7.1;</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>central offline kernel staging area with kernel activation at system/instance restart (Note 1632754), new with NW 7.0, EHP1 for NW 7.0, NW 7.1, EHP1 for NW 7.1;</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>automatic kernel patch distribution to all remote application server instances (SAP Note 1637588), new with NW 7.0, EHP1 for NW 7.0, NW 7.1, EHP1 for NW 7.1;</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>central start/stop of all application server instances with one single command (SAP Note 1644051), new with NW 7.0, EHP1 for NW 7.0, NW 7.1, EHP1 for NW 7.1.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>DB2/LUW (DB6): Collection of network statistics is enabled for monitoring purposes (Note 1625157), new with NW 7.0, EHP1 for NW 7.0, NW 7.1, EHP1 for NW 7.1.</li>\r\n</ul>\r\n<p><br /><strong>2.2.3. Performance improvements</strong></p>\r\n<ul>\r\n<li>Optimized performance via profile based optimization on UNIX platforms (Linux, HPUX, IBM i for the 720_EXT kernel).</li>\r\n</ul>\r\n<ul>\r\n<li>DB2/LUW (DB6): R3load optimization for tables with LOB columns (Note 1433926), new with NW 7.0, EHP1 for NW 7.0, NW 7.1, EHP1 for NW 7.1.</li>\r\n</ul>\r\n<ul>\r\n<li>MSSQL Server: 'client awareness' enablement (Note 1148380), new for MSSQL Server 2005.</li>\r\n</ul>\r\n<p><br /><strong>3. Rollout of Kernel 720</strong><br /><br />The rollout of the SAP kernel 720 for the different SAP NetWeaver releases follows a staged approach.<br />The 720 kernel was finally released for all 7.0n and 7.1n releases, for ABAP only, Java only and Dual-Stack scenarios.<br />During HY 2 2012, older kernel versions (700, 701, 710, 711) will be faded out, meaning that corrections for kernel bugs in these kernel versions will be made available through corrections for the SAP kernel 720.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><span style=\"text-decoration: underline;\"><strong>Attention:</strong></span></p>\r\n<p>As of May 2015,&#160;<strong>Kernel 720 went Out of Maintenance</strong>&#160;with patch level 800 (see note&#160;<a target=\"_blank\" href=\"/notes/\">2158874</a>),&#160;<strong>with downward&#160;compatible&#160;Kernel 721 (EXT)</strong>&#160;<strong>becoming the new \"standard\" kernel</strong>, and downward compatible kernel 722 (EXT) the new innovation kernel. Kernel 720 can still be used, but if an issue occurs with kernel 720, a fix will be only provided for SAP kernel 721 and 722.</p>\r\n<p>Our recommendation is an upgrade <strong>to the&#160;latest available&#160;721 Stack Kernel.</strong> As described in&#160;<a target=\"_blank\" href=\"https://websmp206.sap-ag.de/sap/support/notes/1728283\">SAP Note 1728283</a>, SAP Kernel 720 can be replaced by SAP Kernel 721 everywhere, and SAP Kernel 721 can be installed by a simple kernel patch.</p>\r\n<p>New&#160;Stack Kernels will be anounced on&#160;<a target=\"_blank\" href=\"http://scn.sap.com/docs/DOC-53415\">http://scn.sap.com/docs/DOC-53415</a>.&#160;Further information on upgrading from 720 to 721 can be found in note&#160;<a target=\"_blank\" href=\"/notes/1975687\">1975687</a>.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D002449"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I823601)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001629598/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001629598/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001629598/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001629598/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001629598/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001629598/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001629598/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001629598/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001629598/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "787302", "RefComponent": "BC", "RefTitle": "Maintenance for SAP kernels seems to end too soon", "RefUrl": "/notes/787302"}, {"RefNumber": "447519", "RefComponent": "BC-I18", "RefTitle": "Kernel patches for code pages, languages and locales", "RefUrl": "/notes/447519"}, {"RefNumber": "1873798", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1873798"}, {"RefNumber": "1781218", "RefComponent": "BC-I18", "RefTitle": "Spontaneous code page conversion error after DB recoonect", "RefUrl": "/notes/1781218"}, {"RefNumber": "1769975", "RefComponent": "BC-ABA-LA", "RefTitle": "URL escape with special characters fail", "RefUrl": "/notes/1769975"}, {"RefNumber": "1744209", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 720, 721 and 722: Versions and Kernel Patch Levels", "RefUrl": "/notes/1744209"}, {"RefNumber": "1728283", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 721: General Information", "RefUrl": "/notes/1728283"}, {"RefNumber": "1722035", "RefComponent": "BC-I18", "RefTitle": "Islamic calendars: Initial dates not handled correctly", "RefUrl": "/notes/1722035"}, {"RefNumber": "1716826", "RefComponent": "BC-CST", "RefTitle": "Usage of the downward compatible kernel 721 (EXT)", "RefUrl": "/notes/1716826"}, {"RefNumber": "1713986", "RefComponent": "BC-CST", "RefTitle": "Installation of kernel 721 (EXT)", "RefUrl": "/notes/1713986"}, {"RefNumber": "171356", "RefComponent": "BC-OP-LNX", "RefTitle": "SAP Software on Linux: General information", "RefUrl": "/notes/171356"}, {"RefNumber": "1710129", "RefComponent": "XX-PROJ-SIE-SID", "RefTitle": "Syntax check in UPDATE edid4 statement", "RefUrl": "/notes/1710129"}, {"RefNumber": "1687173", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Collection of notes about the 7.20 kernel", "RefUrl": "/notes/1687173"}, {"RefNumber": "1669684", "RefComponent": "BC-OP-SUN", "RefTitle": "SAP on Oracle Solaris 11", "RefUrl": "/notes/1669684"}, {"RefNumber": "1667336", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS Monitoring with Kernel 7.20 (DCK)", "RefUrl": "/notes/1667336"}, {"RefNumber": "1637287", "RefComponent": "BC-FES-ITS", "RefTitle": "DCK: SAP GUI for HTML - new design for SAP_BASIS 700/701/710/711", "RefUrl": "/notes/1637287"}, {"RefNumber": "1636252", "RefComponent": "BC-CST", "RefTitle": "Installing a 7.20 kernel in SAP Web AS 7.00/7.01/7.10/7.11", "RefUrl": "/notes/1636252"}, {"RefNumber": "1553301", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1553301"}, {"RefNumber": "1553300", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1553300"}, {"RefNumber": "1467086", "RefComponent": "BC-DB-MSS", "RefTitle": "SAP 7.20 Kernel (DCK) on MS SQL Server", "RefUrl": "/notes/1467086"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1911741", "RefComponent": "BC-XI-IBC", "RefTitle": "Cannot connect to server using message server ms:// P4 error in PI ESR", "RefUrl": "/notes/1911741 "}, {"RefNumber": "2369910", "RefComponent": "BC-OP-LNX", "RefTitle": "SAP Software on Linux: General information", "RefUrl": "/notes/2369910 "}, {"RefNumber": "2003265", "RefComponent": "BC-OP-PLNX", "RefTitle": "Install SAP Application Server on IBM PowerLinux for Oracle based 3-tier SAP landscape", "RefUrl": "/notes/2003265 "}, {"RefNumber": "1787260", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS: Mutual activity control of agents", "RefUrl": "/notes/1787260 "}, {"RefNumber": "1669684", "RefComponent": "BC-OP-SUN", "RefTitle": "SAP on Oracle Solaris 11", "RefUrl": "/notes/1669684 "}, {"RefNumber": "1728283", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 721: General Information", "RefUrl": "/notes/1728283 "}, {"RefNumber": "1636252", "RefComponent": "BC-CST", "RefTitle": "Installing a 7.20 kernel in SAP Web AS 7.00/7.01/7.10/7.11", "RefUrl": "/notes/1636252 "}, {"RefNumber": "1713986", "RefComponent": "BC-CST", "RefTitle": "Installation of kernel 721 (EXT)", "RefUrl": "/notes/1713986 "}, {"RefNumber": "1477428", "RefComponent": "BC-SEC-LGN", "RefTitle": "Downport (SAP_BASIS 7.01): Security Session Management", "RefUrl": "/notes/1477428 "}, {"RefNumber": "1687173", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Collection of notes about the 7.20 kernel", "RefUrl": "/notes/1687173 "}, {"RefNumber": "447519", "RefComponent": "BC-I18", "RefTitle": "Kernel patches for code pages, languages and locales", "RefUrl": "/notes/447519 "}, {"RefNumber": "1716826", "RefComponent": "BC-CST", "RefTitle": "Usage of the downward compatible kernel 721 (EXT)", "RefUrl": "/notes/1716826 "}, {"RefNumber": "1667336", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS Monitoring with Kernel 7.20 (DCK)", "RefUrl": "/notes/1667336 "}, {"RefNumber": "1744209", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 720, 721 and 722: Versions and Kernel Patch Levels", "RefUrl": "/notes/1744209 "}, {"RefNumber": "1781218", "RefComponent": "BC-I18", "RefTitle": "Spontaneous code page conversion error after DB recoonect", "RefUrl": "/notes/1781218 "}, {"RefNumber": "1722035", "RefComponent": "BC-I18", "RefTitle": "Islamic calendars: Initial dates not handled correctly", "RefUrl": "/notes/1722035 "}, {"RefNumber": "1769975", "RefComponent": "BC-ABA-LA", "RefTitle": "URL escape with special characters fail", "RefUrl": "/notes/1769975 "}, {"RefNumber": "787302", "RefComponent": "BC", "RefTitle": "Maintenance for SAP kernels seems to end too soon", "RefUrl": "/notes/787302 "}, {"RefNumber": "1710129", "RefComponent": "XX-PROJ-SIE-SID", "RefTitle": "Syntax check in UPDATE edid4 statement", "RefUrl": "/notes/1710129 "}, {"RefNumber": "1467086", "RefComponent": "BC-DB-MSS", "RefTitle": "SAP 7.20 Kernel (DCK) on MS SQL Server", "RefUrl": "/notes/1467086 "}, {"RefNumber": "1637287", "RefComponent": "BC-FES-ITS", "RefTitle": "DCK: SAP GUI for HTML - new design for SAP_BASIS 700/701/710/711", "RefUrl": "/notes/1637287 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}