{"Request": {"Number": "906397", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 313, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005230812017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=B715AF999BE1515A5BF0B35DBD899A37"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "906397"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 11}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.01.2018"}, "SAPComponentKey": {"_label": "Component", "value": "PSM-FA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Fund Accounting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Public Sector Management", "value": "PSM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PSM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Fund Accounting", "value": "PSM-FA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PSM-FA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "906397 - ERP2004/05 Public Sector: Migration of new general ledger"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note corrects the following symptoms:</p>\r\n<ul>\r\n<li>For document entry (FB01, FB50, FB60 or others), the system issues message \"Define a leading ledger\" (FAGL_LEDGER_CUST 023).</li>\r\n</ul>\r\n<ul>\r\n<li>Ledge 0L cannot be selected as a leading ledger.</li>\r\n</ul>\r\n<ul>\r\n<li>In accordance with SAP Note 832362 under ERP2004, you have managed one or more of your general ledgers with the update of funds and functional area and you now want to upgrade to a higher release.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Prerequisites:</p>\r\n<ul>\r\n<li>Under Release ERP2004 (ECC 5.0), you have already activated the new general ledger (GL) and possibly also carried out an update and migrated to ERP2005 (ECC 6.0).</li>\r\n</ul>\r\n<ul>\r\n<li>You have activated \"Global Functions Funds Management (PSM FM)\" (view VV_FMISPS_1).  </li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Before you migrate to a new table group, you should carefully consider whether your old table group does not fully cater to your needs. As a rule, the migration to a new table group means that your existing reporting must be defined again. In particular, this applies for standard reporting in the standard ledger 0L. The functional developments provided in PSM are available in each table group. With regard to the issue of selecting the table group, also refer to SAP Note 1060657. If you want to migrate nevertheless, execute the following steps for the table group FMGLFLEXT (you must perform the procedure analogously for the table group PSGLFLEXT, using the corresponding reports):</p>\r\n<ol>1. Create the executable program FMGL_SETTABLENAMES_FAGL_FMGL and implement the program corrections.</ol><ol>2. If you already have transaction data in the initial ledger, execute an extensive totals record reporting and save the lists for later comparison.</ol><ol>3. Undo the assignment of all scenarios and those customer fields (for  example, ZZGEBER, ZZGRANT) for the relevant ledgers that you also do not want to use in FMGLFLEXT. You can find the corresponding IMG activity at \"Financial Accounting (New)\" -&gt; \"Financial Accounting Global Settings (New)\" -&gt; \"Ledgers\" -&gt; \"Assign Scenarios and Customer Fields to Ledgers\".</ol><ol>4. Start the program FMGL_CHANGE_APPL_IN_LEDGER. As a result, the application FI-GLF is converted to FI-PSM in all your ledgers so that these ledgers can be posted to again.</ol><ol>5. If you already have transaction data in the initial ledger, carry out the migration of the data using the program FMGL_MIGRATION_FAGL_FMGL. To do this, use transaction SE19 to create an implementation for the enhancement spot FMGL_MIGRATION_FAGL_FMGL (new BADI). As a reference, specify the class PSM_FUND_GRANT_AND_PARTNER.</ol><ol>6. If you already have transaction data in the initial ledger, then use the program FMGL_SETTABLENAMES_FAGL_FMGL to convert the totals table of your ledgers to the Public Sector totals table FMGLFLEXT. If you have no transaction data yet, carry out the change manually in the ledger definition (\"Financial Accounting (New)\" -&gt; \"Financial Accounting Global Settings (New)\" -&gt; \"Ledgers\" -&gt; \"Define Ledger for General Ledger Accounting\").</ol><ol>7. Reset the assignment of scenarios and customer fields for the ledgers in question.</ol><ol>8. Execute the program RGZZGLUX.</ol><ol>9. If you already have transaction data in the initial ledger, execute an extensive totals record reporting and compare this with the reports protected from the migration.</ol><ol>10. After successful migration, you can delete the transaction data in FAGLFLEXT. </ol></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-GL (General Ledger Accounting)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D025965)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I800433)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "840783", "RefComponent": "PSM", "RefTitle": "Error message FAGL_LEDGER_CUST020 or FAGL_LEDGER_CUST023", "RefUrl": "/notes/840783"}, {"RefNumber": "1779724", "RefComponent": "PSM-FM-UP", "RefTitle": "Missing report to migrate data from FAGLFLEX* to PSGLFLEX*", "RefUrl": "/notes/1779724"}, {"RefNumber": "1400563", "RefComponent": "PSM-FM-UP-FI-PU", "RefTitle": "When should a PSM customer use FAGLFLEXT or FMGLFLEXT", "RefUrl": "/notes/1400563"}, {"RefNumber": "1030497", "RefComponent": "PSM-FM", "RefTitle": "SAP ERP 6.0: Public sector scenarios in new general ledger", "RefUrl": "/notes/1030497"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2036717", "RefComponent": "FI-GL-FL", "RefTitle": "General Ledger Accounting (new) - Assigning scenario to ledger: Error FAGL_LEDGER_CUST137", "RefUrl": "/notes/2036717 "}, {"RefNumber": "1400563", "RefComponent": "PSM-FM-UP-FI-PU", "RefTitle": "When should a PSM customer use FAGLFLEXT or FMGLFLEXT", "RefUrl": "/notes/1400563 "}, {"RefNumber": "1779724", "RefComponent": "PSM-FM-UP", "RefTitle": "Missing report to migrate data from FAGLFLEX* to PSGLFLEX*", "RefUrl": "/notes/1779724 "}, {"RefNumber": "1030497", "RefComponent": "PSM-FM", "RefTitle": "SAP ERP 6.0: Public sector scenarios in new general ledger", "RefUrl": "/notes/1030497 "}, {"RefNumber": "840783", "RefComponent": "PSM", "RefTitle": "Error message FAGL_LEDGER_CUST020 or FAGL_LEDGER_CUST023", "RefUrl": "/notes/840783 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-PS", "From": "600", "To": "600", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-PS 600", "SupportPackage": "SAPKGPPD05", "URL": "/supportpackage/SAPKGPPD05"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "EA-PS", "NumberOfCorrin": 1, "URL": "/corrins/**********/175"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}