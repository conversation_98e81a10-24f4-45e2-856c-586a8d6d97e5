{"Request": {"Number": "1121318", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 452, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016428262017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001121318?language=E&token=DDFE7C57E09885F6258B552B178EBB19"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001121318", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001121318/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1121318"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Performance"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.02.2012"}, "SAPComponentKey": {"_label": "Component", "value": "LO-VC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Variant Configuration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Logistics - General", "value": "LO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Variant Configuration", "value": "LO-VC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO-VC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1121318 - Analysis of the performance of dependency knowledge in VC"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You encounter long response times in your variant configuration model. This could be either by entering an existing sales document carrying configurable items or a certain value set within the configuration.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Performance Variant configuration configuration model</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Apply the latest ST-A/PI Package (SAPnote 69455), SAPnote 1023995</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The aim of this note is to introduce a new variant configuration analysis tool that provides a detailed picture how the times are distributed over the different types of dependencies while entering the configuration.<br /><br />The tool will give you detailed information about how much time is spent in the dependency knowledge either when entering the configuration of a sales order or when setting certain values within the configuration screen. It differentiates between:<br /><br />- Constraints<br />- Procedures<br />- Actions<br />- Selection conditions<br />- Preconditions<br /><br />The tool can be started by entering transaction ST13-&gt; Select Tool Varconf-&gt; Project CUP.<br /><br /><br />Contents:<br /><br />I Simple Mode<br />II Advanced Mode<br />III Evaluation<br />IV Interpretation<br />V&#x00A0;&#x00A0; Known Problems<br /><br />I Simple Mode<br />-------------<br />The simple mode is designed in order to measure the dependency processing times during entering the configuration of a sales order or the configuration simulation. On the selection screen in the simple mode you can adjust the settings as follows:<br /><br />a) Choose the transaction, you would like to analyze:<br />- va02<br />- va03<br />- va22<br />- va23<br />- cu50<br /><br />b) Set the clock type for correct measurement (If the analysis tool provides negative numbers, zeros or multiples of a constant, it is very likely the clock type is incorrectly set. Change it from low to high or vice versa. Confer also the documentation if necessary.)<br /><br />c) Performing the dependency trace (In order to avoid unnecessary side effects leading to non-representative times (for example the load or generation of program code in buffers) perform the first the dependency trace and then the ABAP-trace.)<br /><br />On the sales order item overview screen, select the configurable material. Enter the configuration of the item, and jump back two times to get back to the variant configuration analyzer screen.<br /><br />d) Performing the ABAB (SE30-) trace<br /><br />On the sales order item overview screen, select the configurable material. Enter the configuration of the item, and jump back two times to get back to the variant configuration analyzer screen.<br /><br />e) Starting the Match Process (for details confer III)<br /><br /><br /><br /><br /><br />II Advanced Mode<br />-----------------<br />The advanced mode is designed in order to upload existing trace files of dependency knowledge and ABAP runtime. Once each trace is activated perform each time the exact identical steps. Otherwise, if the steps being executed during the first trace, and the steps being performed during measuring of the second trace are different, they will not match. In this case the match process will yield to an error and no analysis can be done.<br /><br />The traces can be created with appropriate settings as follows:<br /><br />a) Creating the dependency knowledge trace: Adjust the trace settings by entering in the SAP Tree Logistics-&gt;Central Functions-&gt;Variant Configuration-&gt;Modeling Environment for Variant Configuration (CU50). In the menu choose Extras-&gt;Trace-&gt;Settings and verify/set the following flags:<br /><br />Trace Level:<br /><br />(X)Less Detailed<br />() More Detailed<br /><br />Trace Areas:<br /><br />(X)Preconditions<br />(X)Selection Conditions<br />(X)Actions<br />(X)Procedures<br />(X)Constraints<br />()Perf. Cstr. PMS<br />(X)Tables<br />(X)Functions<br />()Dynamic Database<br /><br />To activate the trace in the sales order transactions:<br />You can go on the sales item overview screen to the menu Environment-&gt;Analysis-&gt;Configuration Trace-&gt;On. Now mark the sales order item you would like to analyze and enter the configuration. In the configuration screen go to menu Extras-&gt;Trace-&gt;Display and download the file as unconverted text. In order not to mix it up the ABAP Trace file it is convenient to give it the extension .trace. But it is not necessary.<br /><br />To activate the trace for a value set:<br />Enter the configuration and navigate to your configuration model to the point where you want to analyze a certain value set. Before setting the value activate the trace via the menu Extras-&gt;Trace-&gt;Activate. Perform now the value set by entering the value and press return. Go to the menu<br />Extras-&gt;Trace-&gt;Display and download the file as unconverted text.<br /><br />To activate the trace in transaction CU50:<br />If you are tracing the cu50 you activate the trace in the menu Extras-&gt;Trace-&gt;Activate either before entering the configuration of the configurable material or within the configuration for a certain value set.<br /><br />Remark:<br />In addition to the standard dependency trace you can make use of the transaction /CUTRC.&#x00A0;&#x00A0;Before starting any processing of dependency knowledge, ensure to adjust the settings as mentioned on slide 13. After that, activate the trace. Finally, when you have recorded the relevant dependency trace, deactivate the trace and download the file unconverted.<br /><br /><br />b) Creating the ABAP runtime trace:<br />Please keep in mind to perform the identical steps as for the dependency trace! An appropriate variant should be created to trace only relevant function modules and subroutines for faster performance with the following settings:<br /><br />Trace Filter<br />-------------<br />P Prog/gl.class/fct.group Local class Pro Procedure<br />F CUOV    !  FORM CUOV_EVAL_PRECND<br />F CUOV    !  FORM CUOV_EVAL_SELCND<br />F CUOV    !  FORM CUOV_EVAL_ACTIONS<br />F CUOV    !  FUNC CUOV_DO_ACTION<br />F CUOV    !  FUNC CUOV_DO_PROCEDURE<br />F CUPM    !  FORM PMSX_DO_ACTION<br />F CUKO    !  FORM BOM_EXPLOSION<br />F CEI0    !  FUNC CE_I_CONFIGURE<br />F CULL    !  FORM CULLX_EVAL_PROCEDURE<br />F CULL    !  FORM CULLX_EVAL_SEL_CONDITION<br /><br />If you would like to trace only a value set within the configuration for example, you must activate the particular units option. In this case the measurement will start and end by the OK command codes /ron and /roff respectively. For standard #entering the configuration of a sales order# you can leave the option particular units blank.<br />(Menu -&gt; System -&gt; Utilities -&gt; Runtime analysis -&gt; Switch On / Off)<br /><br />On the statement tab, ensure that at least the modularization units are checked. The other option can be set.<br /><br />It is absolutely necessary to set the aggregation option to none. Otherwise you will not retrieve the correct data!<br /><br />After adjusting the settings press the execution button for the transaction, wherein you would like to analyze the variant configuration model with respect to the dependency knowledge response times.<br /><br />If you have traced for example the step entering the configuration of a sales order item, you can go back to the SE30 entrance screen by the OK Code /NSE30. You are know able to view the collected ABAP trace data by pressing<br /><br />1) Analyze button<br />2) Call hierarchy button<br /><br />and download the ABAP trace as unconverted file.<br /><br />Once you have both traces taken you can upload them in the advanced mode into the evaluation tool: In the section upload trace files load first the dependency trace and then the ABAP trace file. Start the matching process by pressing F8.<br /><br /><br /><br />III Evaluation<br />--------------<br />The analysis overview provides the following tabs and information for you:<br /><br />1) Tab 'Match':<br />Contains the information of the matching, i.e. how much time each dependency has consumed during the configuration process.<br /><br />2) Tab 'Dependency Trace':<br />Contains the collected dependency trace.<br /><br />3) Tab 'SE30 Trace':<br />Contains the collected ABAP trace.<br /><br />4) Tab 'Extr. Dep.-Trc. Lines':<br />Contains the lines from the dependency trace being relevant for the matching process.<br /><br />5) Tab 'Extrc. SE30-Trc. Lines': Contains the lines from the ABAP trace being relevant for the matching process.<br /><br /><br />6) Tab 'Match Summary':<br />The match summary tab provides you with an aggregated view on the data:<br /><br />a) Tab 'Summary':<br />Distribution of processing time over the different dependency types.&#x00A0;&#x00A0;In addition you see the number of BOM explosions and the number of configured instances.<br /><br />b) Tab 'Constraints':<br />Gross time, name/type of dependency, number of executions.<br /><br />c) Tab 'Procedures': Gross time, name/type of dependency, number of executions.<br /><br />d) Tab 'Actions':<br />Gross time, name/type of dependency, number of executions.<br /><br />e) Tab 'Preconditions': Gross time, name/type of dependency, number of executions.<br /><br />f) Tab 'Selection Conditions': Gross time, name/type of dependency, number of executions.<br /><br />g) Tab 'Procedure triggers constraints': provides the information, which procedure has caused any constraints to run again. On the match tab, in the column Proc/Constr you will see which procedure has triggered which constraint.<br /><br /><br />IV Interpretation<br />-----------------<br />The tool provides a lot of information. We would like to guide you on which the focus should be set most effectively. The tab of most interest for the variant configuration modeler is the match summary tab.<br /><br />a) Identifying most time consuming dependencies.<br />On each of the tabs procedures, constraints, selection conditions, preconditions and actions you should sort by the first column in descending order. This shows the top dependencies which are very time consuming. The reasons could be for example a high number of commands within a procedure, an expensive table access within a procedure or an expensive call of a function module from a constraint.<br /><br />b) Identifying procedure that triggers constraint<br />In an ideal model constraints run only once. A proper condition part ensures that they are not executed more often. However, it could be the case that the input parameters of a constraint are resulting from a procedure output. Since at each configuration processing the values from procedures are withdrawn the constraints are executed more often. On the tab 'Proc. triggers Constraints' you will get listed these type of procedures. Navigate to the 'match' tab and look for constraints you have entries in the last column. The number there refers to the line of procedure which appears in the SE30 trace. The value in the last column of this procedure indicates how many time this procedure has triggered the constraint.<br /><br />On the tab 'Match Summary', sub-tab 'Summary' you will see how many miliseconds the system spend on these by procedures triggered constraints.<br /><br />c) Identifying information flow from bottom to top<br />If the model is created in a manner that information is passed from bottom to the top this causes unnecessary BOM explosions. In a multilevel scenario there should not be more than three BOM explosions for transaction /VA02. Otherwise you may have this scenario. Confer also SAPnote 917987 (2).<br /><br />d) Number of CE_I_CONFIGURE<br />This number shows how often instances have been configured during one configuration and has at the moment only informative character.<br /><br /><br />V Known Problems<br />----------------<br />a) During the collection of the trace data a short dump could occur due to buffer limitations: EXPORT_BUFFER_NO_MEMORY, CX_SY_EXPORT_BUFFER_NO_MEMORY<br />Please adjust in this case the parameters 'rsdb/obj/buffersize' and 'rsdb/obj/max_objects' to higher values or<br />- decrease the dependency trace level to less detailed<br />- restrict the runtime analysis trace to above mentioned function modules.<br /><br />b) Dependency trace import in simple mode may fail. Please apply SAPnote 1023995.<br /><br />c) Invalid files in advanced mode. If you have traced a model in a system manually, and want to upload file in another system, make sure that the language of the system where the trace was taken and the login language of the system where you upload the file is the same.<br /><br />d) Lines are doubled in the dependency trace and the import/upload fails. Ensure that in case of manual tracing either the trace is activated from transaction cu50/va03 or from the transaction CUTRC. If you activate both the evaluation / matching is not possible.<br /><br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D038507"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D034810)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001121318/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001121318/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001121318/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001121318/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001121318/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001121318/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001121318/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001121318/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001121318/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "917987", "RefComponent": "LO-VC", "RefTitle": "General performance in variant configuration", "RefUrl": "/notes/917987"}, {"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455"}, {"RefNumber": "1730246", "RefComponent": "LO-VC", "RefTitle": "Wrong constraint count in ST13 - VARCONF - CUP", "RefUrl": "/notes/1730246"}, {"RefNumber": "1667903", "RefComponent": "LO-VC", "RefTitle": "\"Trace table mismatch!\" error in ST13 - VARCONF - CUP", "RefUrl": "/notes/1667903"}, {"RefNumber": "1023995", "RefComponent": "LO-VC", "RefTitle": "CUTRC writes no entries in the BAPI/IDOC at termination", "RefUrl": "/notes/1023995"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455 "}, {"RefNumber": "1730246", "RefComponent": "LO-VC", "RefTitle": "Wrong constraint count in ST13 - VARCONF - CUP", "RefUrl": "/notes/1730246 "}, {"RefNumber": "1667903", "RefComponent": "LO-VC", "RefTitle": "\"Trace table mismatch!\" error in ST13 - VARCONF - CUP", "RefUrl": "/notes/1667903 "}, {"RefNumber": "917987", "RefComponent": "LO-VC", "RefTitle": "General performance in variant configuration", "RefUrl": "/notes/917987 "}, {"RefNumber": "1023995", "RefComponent": "LO-VC", "RefTitle": "CUTRC writes no entries in the BAPI/IDOC at termination", "RefUrl": "/notes/1023995 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST-A/PI", "From": "01K_R3_470", "To": "01K_R3_470", "Subsequent": "X"}, {"SoftwareComponent": "ST-A/PI", "From": "01K_ECC500", "To": "01K_ECC500", "Subsequent": "X"}, {"SoftwareComponent": "ST-A/PI", "From": "01K_ECC600", "To": "01K_ECC600", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}