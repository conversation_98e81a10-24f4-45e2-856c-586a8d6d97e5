{"Request": {"Number": "865444", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 324, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015927502017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000865444?language=E&token=1277EBEE20A73DCD6909FCAA6F058433"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000865444", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000865444/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "865444"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 13}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "07.11.2007"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-MI"}, "SAPComponentKeyText": {"_label": "Component", "value": "Migration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Migration", "value": "RE-FX-MI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-MI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "865444 - Migration from Classic RE"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You want to carry out an automatic migration from Classic RE to RE-FX.  To make sure that you are working with the latest version of the migration programs, proceed as described below.<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>REMICL<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Import the most recent Support Package level (at least Support Package 10) and implement the notes based on this for component RE-FX-MI.<br /><br />Prior to migration, make sure that you have implemented all notes that are assigned to this note as \"related notes\". These notes contain corrections to the RE-FX source code that must be implemented so the RE processes work correctly after the migration.<br /><br />Detailed documentation for the individual steps and errors that may occur is attached to Note 828160. In addition, the individual steps of the migration transaction are documented in the system (in the long text for the step or in the IMG documentation).&#x00A0;&#x00A0;After you import the current corrections or implement the notes, call transaction REMICL and call the documentation for the individual steps to read this documentation. The settings to be made are also documented in the relevant settings guide. You can access it in those steps that have an \"Execute\" icon in the IMG column by clicking this icon.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D030839"}, {"Key": "Processor                                                                                           ", "Value": "D030839"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000865444/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000865444/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000865444/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000865444/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000865444/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000865444/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000865444/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000865444/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000865444/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "998664", "RefComponent": "RE-FX-RA", "RefTitle": "Application-specific data from RE-FX in FI posting", "RefUrl": "/notes/998664"}, {"RefNumber": "998394", "RefComponent": "RE-FX-MI", "RefTitle": "Migration of subsidies", "RefUrl": "/notes/998394"}, {"RefNumber": "993994", "RefComponent": "RE-FX-SC", "RefTitle": "VVAE: Message RESCSU035 is set to customizable", "RefUrl": "/notes/993994"}, {"RefNumber": "990478", "RefComponent": "RE-FX", "RefTitle": "RF-FX: Change in behavior - First posting from", "RefUrl": "/notes/990478"}, {"RefNumber": "984687", "RefComponent": "RE-FX-MI", "RefTitle": "Migration, Input tax distribution data with 100% option rate", "RefUrl": "/notes/984687"}, {"RefNumber": "981737", "RefComponent": "RE-FX-BD", "RefTitle": "REFX: Checking company code in master data", "RefUrl": "/notes/981737"}, {"RefNumber": "979053", "RefComponent": "RE-FX-SR", "RefTitle": "Settled sales reports can be changed after migration", "RefUrl": "/notes/979053"}, {"RefNumber": "977443", "RefComponent": "RE-FX-OR", "RefTitle": "Contract offer: Sales-based rent agreement", "RefUrl": "/notes/977443"}, {"RefNumber": "970459", "RefComponent": "RE-FX-CN", "RefTitle": "Additional texts for real estate objects (BDT settings)", "RefUrl": "/notes/970459"}, {"RefNumber": "968218", "RefComponent": "RE-FX-MI", "RefTitle": "Migration: Endless loop during parallel processing", "RefUrl": "/notes/968218"}, {"RefNumber": "962367", "RefComponent": "RE-FX-BD", "RefTitle": "Functional enhancement of the deletion of objects", "RefUrl": "/notes/962367"}, {"RefNumber": "960624", "RefComponent": "RE-FX-BD", "RefTitle": "Migration: Number of rooms per room type", "RefUrl": "/notes/960624"}, {"RefNumber": "953422", "RefComponent": "RE-FX", "RefTitle": "Integration: Module does not return IMKEYs", "RefUrl": "/notes/953422"}, {"RefNumber": "951073", "RefComponent": "RE-FX-CN", "RefTitle": "Using BAdI or BAPI to insert conditions", "RefUrl": "/notes/951073"}, {"RefNumber": "950356", "RefComponent": "RE-FX-SC", "RefTitle": "VVAE: Message RESCSU036 is set to customizable", "RefUrl": "/notes/950356"}, {"RefNumber": "949913", "RefComponent": "RE-FX-CN", "RefTitle": "Adjustment term and error message REAJME011", "RefUrl": "/notes/949913"}, {"RefNumber": "948271", "RefComponent": "RE-FX-AJ", "RefTitle": "Index adjustment with index level 1 causes a runtime error", "RefUrl": "/notes/948271"}, {"RefNumber": "947385", "RefComponent": "RE-FX-AJ", "RefTitle": "Including surcharges and reductions for repr. lists of rents", "RefUrl": "/notes/947385"}, {"RefNumber": "946523", "RefComponent": "RE-FX-MI", "RefTitle": "Full table scan on the COBRB", "RefUrl": "/notes/946523"}, {"RefNumber": "946305", "RefComponent": "RE-FX-IT", "RefTitle": "After migration RE-Cl->RE-FX: Error REITOR005", "RefUrl": "/notes/946305"}, {"RefNumber": "944694", "RefComponent": "RE-FX-MI", "RefTitle": "Tenant account overview: No historical items displayed", "RefUrl": "/notes/944694"}, {"RefNumber": "939728", "RefComponent": "RE-FX-BD", "RefTitle": "Service method \"Occupancy history correction\"", "RefUrl": "/notes/939728"}, {"RefNumber": "939557", "RefComponent": "RE-FX-SC", "RefTitle": "RECABC014 during the \"Cost on settlement unit\" step", "RefUrl": "/notes/939557"}, {"RefNumber": "930110", "RefComponent": "RE-FX-CO", "RefTitle": "Validation check on cost centers", "RefUrl": "/notes/930110"}, {"RefNumber": "926641", "RefComponent": "RE-FX-BP", "RefTitle": "Partner: Ownership shares maintenance", "RefUrl": "/notes/926641"}, {"RefNumber": "924444", "RefComponent": "RE-FX-SC", "RefTitle": "Contract condition & legacy data transfer of advance payment", "RefUrl": "/notes/924444"}, {"RefNumber": "922749", "RefComponent": "RE-FX", "RefTitle": "Additional texts for real estate objects", "RefUrl": "/notes/922749"}, {"RefNumber": "918399", "RefComponent": "RE-FX-CN", "RefTitle": "SELECT on condition table without index", "RefUrl": "/notes/918399"}, {"RefNumber": "917447", "RefComponent": "RE-FX-LM", "RefTitle": "BAPI: Texts/memos of land registers", "RefUrl": "/notes/917447"}, {"RefNumber": "916739", "RefComponent": "RE-FX-LM", "RefTitle": "REFX: Land register: Some internal numbers are incorrect", "RefUrl": "/notes/916739"}, {"RefNumber": "916640", "RefComponent": "RE-FX-LM", "RefTitle": "REFX: Error messages for the land register", "RefUrl": "/notes/916640"}, {"RefNumber": "916026", "RefComponent": "RE-FX-LM", "RefTitle": "REFX: Land register: Runtime error in section 1", "RefUrl": "/notes/916026"}, {"RefNumber": "915826", "RefComponent": "RE-FX-LM", "RefTitle": "BAPI: Land register (old land register number)", "RefUrl": "/notes/915826"}, {"RefNumber": "915440", "RefComponent": "RE-FX-LM", "RefTitle": "BAPI: Land register (exception not caught)", "RefUrl": "/notes/915440"}, {"RefNumber": "912105", "RefComponent": "RE-FX-MI", "RefTitle": "Input tax distribution migration: Missing net amount", "RefUrl": "/notes/912105"}, {"RefNumber": "908521", "RefComponent": "RE-FX-SC", "RefTitle": "Special G/L legacy data advance payments are not cleared", "RefUrl": "/notes/908521"}, {"RefNumber": "828160", "RefComponent": "RE-FX", "RefTitle": "Migration from Classic RE to RE-FX", "RefUrl": "/notes/828160"}, {"RefNumber": "628208", "RefComponent": "RE-FX", "RefTitle": "Procedures for activating RE Extension in SAP R/3 Enterprise", "RefUrl": "/notes/628208"}, {"RefNumber": "588668", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Database statistics", "RefUrl": "/notes/588668"}, {"RefNumber": "457893", "RefComponent": "RE-BD", "RefTitle": "Client copy:copy notes for Real Estate objects", "RefUrl": "/notes/457893"}, {"RefNumber": "1085071", "RefComponent": "RE-FX-MI", "RefTitle": "Migration: REDB 004 for purchase orders", "RefUrl": "/notes/1085071"}, {"RefNumber": "1081321", "RefComponent": "RE-FX-BD", "RefTitle": "Error message REBDRO 031: Cash flow date after validity", "RefUrl": "/notes/1081321"}, {"RefNumber": "1079709", "RefComponent": "RE-FX-SC", "RefTitle": "Termination caused by STACK_NO_ROLL_MEMORY when checking SU", "RefUrl": "/notes/1079709"}, {"RefNumber": "1079141", "RefComponent": "RE-FX-MI", "RefTitle": "Use of user exits in migration", "RefUrl": "/notes/1079141"}, {"RefNumber": "1075140", "RefComponent": "RE-FX-BD", "RefTitle": "REFX: Screens and BDT settings for additional texts", "RefUrl": "/notes/1075140"}, {"RefNumber": "1033116", "RefComponent": "RE-FX-IT", "RefTitle": "Multilevel tax codes: Tax update is missing", "RefUrl": "/notes/1033116"}, {"RefNumber": "1032896", "RefComponent": "RE-FX-MI", "RefTitle": "Migration: Transferring object addresses", "RefUrl": "/notes/1032896"}, {"RefNumber": "1029244", "RefComponent": "RE-FX-SC", "RefTitle": "Migration and Date of 1st. Debit Position", "RefUrl": "/notes/1029244"}, {"RefNumber": "1027695", "RefComponent": "RE-FX-MI", "RefTitle": "Validity check of the cost center - migration part", "RefUrl": "/notes/1027695"}, {"RefNumber": "1026399", "RefComponent": "RE-FX-RA", "RefTitle": "Error in advance tax return in input tax adjustment doc", "RefUrl": "/notes/1026399"}, {"RefNumber": "1020179", "RefComponent": "RE-FX-IT", "RefTitle": "Migration of input tax data and tax rate change", "RefUrl": "/notes/1020179"}, {"RefNumber": "1018211", "RefComponent": "RE-FX-OR", "RefTitle": "Configurable message for check of assignment in offer", "RefUrl": "/notes/1018211"}, {"RefNumber": "1013015", "RefComponent": "RE-FX-CO", "RefTitle": "Validity check of KST", "RefUrl": "/notes/1013015"}, {"RefNumber": "1003209", "RefComponent": "RE-FX-RA", "RefTitle": "Migration: Document display - IMKEY indefinite", "RefUrl": "/notes/1003209"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "828160", "RefComponent": "RE-FX", "RefTitle": "Migration from Classic RE to RE-FX", "RefUrl": "/notes/828160 "}, {"RefNumber": "946305", "RefComponent": "RE-FX-IT", "RefTitle": "After migration RE-Cl->RE-FX: Error REITOR005", "RefUrl": "/notes/946305 "}, {"RefNumber": "1027695", "RefComponent": "RE-FX-MI", "RefTitle": "Validity check of the cost center - migration part", "RefUrl": "/notes/1027695 "}, {"RefNumber": "970459", "RefComponent": "RE-FX-CN", "RefTitle": "Additional texts for real estate objects (BDT settings)", "RefUrl": "/notes/970459 "}, {"RefNumber": "908521", "RefComponent": "RE-FX-SC", "RefTitle": "Special G/L legacy data advance payments are not cleared", "RefUrl": "/notes/908521 "}, {"RefNumber": "1079141", "RefComponent": "RE-FX-MI", "RefTitle": "Use of user exits in migration", "RefUrl": "/notes/1079141 "}, {"RefNumber": "1079709", "RefComponent": "RE-FX-SC", "RefTitle": "Termination caused by STACK_NO_ROLL_MEMORY when checking SU", "RefUrl": "/notes/1079709 "}, {"RefNumber": "924444", "RefComponent": "RE-FX-SC", "RefTitle": "Contract condition & legacy data transfer of advance payment", "RefUrl": "/notes/924444 "}, {"RefNumber": "1075140", "RefComponent": "RE-FX-BD", "RefTitle": "REFX: Screens and BDT settings for additional texts", "RefUrl": "/notes/1075140 "}, {"RefNumber": "1085071", "RefComponent": "RE-FX-MI", "RefTitle": "Migration: REDB 004 for purchase orders", "RefUrl": "/notes/1085071 "}, {"RefNumber": "1081321", "RefComponent": "RE-FX-BD", "RefTitle": "Error message REBDRO 031: Cash flow date after validity", "RefUrl": "/notes/1081321 "}, {"RefNumber": "1032896", "RefComponent": "RE-FX-MI", "RefTitle": "Migration: Transferring object addresses", "RefUrl": "/notes/1032896 "}, {"RefNumber": "977443", "RefComponent": "RE-FX-OR", "RefTitle": "Contract offer: Sales-based rent agreement", "RefUrl": "/notes/977443 "}, {"RefNumber": "1026399", "RefComponent": "RE-FX-RA", "RefTitle": "Error in advance tax return in input tax adjustment doc", "RefUrl": "/notes/1026399 "}, {"RefNumber": "1003209", "RefComponent": "RE-FX-RA", "RefTitle": "Migration: Document display - IMKEY indefinite", "RefUrl": "/notes/1003209 "}, {"RefNumber": "1033116", "RefComponent": "RE-FX-IT", "RefTitle": "Multilevel tax codes: Tax update is missing", "RefUrl": "/notes/1033116 "}, {"RefNumber": "1013015", "RefComponent": "RE-FX-CO", "RefTitle": "Validity check of KST", "RefUrl": "/notes/1013015 "}, {"RefNumber": "1029244", "RefComponent": "RE-FX-SC", "RefTitle": "Migration and Date of 1st. Debit Position", "RefUrl": "/notes/1029244 "}, {"RefNumber": "1020179", "RefComponent": "RE-FX-IT", "RefTitle": "Migration of input tax data and tax rate change", "RefUrl": "/notes/1020179 "}, {"RefNumber": "1018211", "RefComponent": "RE-FX-OR", "RefTitle": "Configurable message for check of assignment in offer", "RefUrl": "/notes/1018211 "}, {"RefNumber": "628208", "RefComponent": "RE-FX", "RefTitle": "Procedures for activating RE Extension in SAP R/3 Enterprise", "RefUrl": "/notes/628208 "}, {"RefNumber": "990478", "RefComponent": "RE-FX", "RefTitle": "RF-FX: Change in behavior - First posting from", "RefUrl": "/notes/990478 "}, {"RefNumber": "998394", "RefComponent": "RE-FX-MI", "RefTitle": "Migration of subsidies", "RefUrl": "/notes/998394 "}, {"RefNumber": "998664", "RefComponent": "RE-FX-RA", "RefTitle": "Application-specific data from RE-FX in FI posting", "RefUrl": "/notes/998664 "}, {"RefNumber": "993994", "RefComponent": "RE-FX-SC", "RefTitle": "VVAE: Message RESCSU035 is set to customizable", "RefUrl": "/notes/993994 "}, {"RefNumber": "950356", "RefComponent": "RE-FX-SC", "RefTitle": "VVAE: Message RESCSU036 is set to customizable", "RefUrl": "/notes/950356 "}, {"RefNumber": "984687", "RefComponent": "RE-FX-MI", "RefTitle": "Migration, Input tax distribution data with 100% option rate", "RefUrl": "/notes/984687 "}, {"RefNumber": "979053", "RefComponent": "RE-FX-SR", "RefTitle": "Settled sales reports can be changed after migration", "RefUrl": "/notes/979053 "}, {"RefNumber": "944694", "RefComponent": "RE-FX-MI", "RefTitle": "Tenant account overview: No historical items displayed", "RefUrl": "/notes/944694 "}, {"RefNumber": "922749", "RefComponent": "RE-FX", "RefTitle": "Additional texts for real estate objects", "RefUrl": "/notes/922749 "}, {"RefNumber": "968218", "RefComponent": "RE-FX-MI", "RefTitle": "Migration: Endless loop during parallel processing", "RefUrl": "/notes/968218 "}, {"RefNumber": "962367", "RefComponent": "RE-FX-BD", "RefTitle": "Functional enhancement of the deletion of objects", "RefUrl": "/notes/962367 "}, {"RefNumber": "947385", "RefComponent": "RE-FX-AJ", "RefTitle": "Including surcharges and reductions for repr. lists of rents", "RefUrl": "/notes/947385 "}, {"RefNumber": "960624", "RefComponent": "RE-FX-BD", "RefTitle": "Migration: Number of rooms per room type", "RefUrl": "/notes/960624 "}, {"RefNumber": "953422", "RefComponent": "RE-FX", "RefTitle": "Integration: Module does not return IMKEYs", "RefUrl": "/notes/953422 "}, {"RefNumber": "951073", "RefComponent": "RE-FX-CN", "RefTitle": "Using BAdI or BAPI to insert conditions", "RefUrl": "/notes/951073 "}, {"RefNumber": "949913", "RefComponent": "RE-FX-CN", "RefTitle": "Adjustment term and error message REAJME011", "RefUrl": "/notes/949913 "}, {"RefNumber": "948271", "RefComponent": "RE-FX-AJ", "RefTitle": "Index adjustment with index level 1 causes a runtime error", "RefUrl": "/notes/948271 "}, {"RefNumber": "926641", "RefComponent": "RE-FX-BP", "RefTitle": "Partner: Ownership shares maintenance", "RefUrl": "/notes/926641 "}, {"RefNumber": "946523", "RefComponent": "RE-FX-MI", "RefTitle": "Full table scan on the COBRB", "RefUrl": "/notes/946523 "}, {"RefNumber": "939728", "RefComponent": "RE-FX-BD", "RefTitle": "Service method \"Occupancy history correction\"", "RefUrl": "/notes/939728 "}, {"RefNumber": "930110", "RefComponent": "RE-FX-CO", "RefTitle": "Validation check on cost centers", "RefUrl": "/notes/930110 "}, {"RefNumber": "939557", "RefComponent": "RE-FX-SC", "RefTitle": "RECABC014 during the \"Cost on settlement unit\" step", "RefUrl": "/notes/939557 "}, {"RefNumber": "918399", "RefComponent": "RE-FX-CN", "RefTitle": "SELECT on condition table without index", "RefUrl": "/notes/918399 "}, {"RefNumber": "912105", "RefComponent": "RE-FX-MI", "RefTitle": "Input tax distribution migration: Missing net amount", "RefUrl": "/notes/912105 "}, {"RefNumber": "457893", "RefComponent": "RE-BD", "RefTitle": "Client copy:copy notes for Real Estate objects", "RefUrl": "/notes/457893 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "600", "To": "600", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-APPL 600", "SupportPackage": "SAPKGPAD04", "URL": "/supportpackage/SAPKGPAD04"}, {"SoftwareComponentVersion": "EA-APPL 600", "SupportPackage": "SAPKGPAD02", "URL": "/supportpackage/SAPKGPAD02"}, {"SoftwareComponentVersion": "EA-APPL 600", "SupportPackage": "SAPKGPAD09", "URL": "/supportpackage/SAPKGPAD09"}, {"SoftwareComponentVersion": "EA-APPL 600", "SupportPackage": "SAPKGPAD08", "URL": "/supportpackage/SAPKGPAD08"}, {"SoftwareComponentVersion": "EA-APPL 600", "SupportPackage": "SAPKGPAD03", "URL": "/supportpackage/SAPKGPAD03"}, {"SoftwareComponentVersion": "EA-APPL 600", "SupportPackage": "SAPKGPAD05", "URL": "/supportpackage/SAPKGPAD05"}, {"SoftwareComponentVersion": "EA-APPL 600", "SupportPackage": "SAPKGPAD06", "URL": "/supportpackage/SAPKGPAD06"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}