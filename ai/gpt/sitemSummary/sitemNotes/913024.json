{"Request": {"Number": "913024", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1045, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005271082017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000913024?language=E&token=3E131F263A1D5DF4DAB138A9F131CF18"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000913024", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000913024/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "913024"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 23}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Correction of legal function"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.02.2006"}, "SAPComponentKey": {"_label": "Component", "value": "PY-BR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Brazil"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Brazil", "value": "PY-BR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-BR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "913024 - HBRSEF00 8.0 fixes"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>HBRSEF00 does not generate record 32 for employees with termination in beginning of the month.<br />In competence 13 the field 23 should not be filled.<br />Record type 20 is not generated with movement codes 150, 155.<br />Records type 20 and 30 validation fixed.<br />If non centralizer branch is selected, the rectype 10 is wrongly reported.<br />Record type 30 - field 20 is not being filled for competence 13.<br />Record type 12 - field 5 is not being filled.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SEFIP, GFIP, rescisao, terminacao, registro, codigo de movimento, filial centralizadora, Brasil, Brazil<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Several fixes for SEFIP 8.0<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Release 4.70 (Enterprise):<br /><br />Make sure you have note 909805 applied. If so, install the attached files (L6BK108979_470_A.CAR at first). Further information can be found in notes 480180, 212876 and 13719.<br /><br />Other releases:<br /><br />Using transaction SE11, set new domains for the following data elements:<br /><br />PBR_OUINP -&gt; CHAR11<br />PBR_OIPAN -&gt; CHAR4<br />PBR_OINFV -&gt; CHAR5<br />PBR_INFPI -&gt; CHAR6<br />PBR_INFPF -&gt; CHAR6<br />PBR_COMPI -&gt; CHAR6<br />PBR_COMPF -&gt; CHAR6<br /><br />Activate the changes.<br /><br />Apply the following source code corrections.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D035062"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D043029)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000913024/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000913024/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000913024/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000913024/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000913024/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000913024/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000913024/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000913024/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000913024/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "L6BK108979_470_A.CAR", "FileSize": "53", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000017202006&iv_version=0023&iv_guid=5BC5A126FC448F49A81F00420A460406"}, {"FileName": "L6BK109114_470_B.CAR", "FileSize": "52", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000017202006&iv_version=0023&iv_guid=3CF1B168B8369A49A5582B6E1EA4E1DF"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "926763", "RefComponent": "PY-BR", "RefTitle": "HR-BR: SEFIP - record 32 for competence before firing month", "RefUrl": "/notes/926763"}, {"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "926763", "RefComponent": "PY-BR", "RefTitle": "HR-BR: SEFIP - record 32 for competence before firing month", "RefUrl": "/notes/926763 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 45B", "SupportPackage": "SAPKE45BD4", "URL": "/supportpackage/SAPKE45BD4"}, {"SoftwareComponentVersion": "SAP_HR 46B", "SupportPackage": "SAPKE46BB6", "URL": "/supportpackage/SAPKE46BB6"}, {"SoftwareComponentVersion": "SAP_HR 46B", "SupportPackage": "SAPKE46BB7", "URL": "/supportpackage/SAPKE46BB7"}, {"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46CA8", "URL": "/supportpackage/SAPKE46CA8"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47053", "URL": "/supportpackage/SAPKE47053"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50019", "URL": "/supportpackage/SAPKE50019"}, {"SoftwareComponentVersion": "SAP_HR 600", "SupportPackage": "SAPKE60004", "URL": "/supportpackage/SAPKE60004"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_HR", "NumberOfCorrin": 6, "URL": "/corrins/0000913024/2"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 6, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 60, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "433360 ", "URL": "/notes/433360 ", "Title": "Some employees is not considered in report HBRSEF00", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "438382 ", "URL": "/notes/438382 ", "Title": "HR: Adjustment of 13th Salary in December", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "439595 ", "URL": "/notes/439595 ", "Title": "Legal Change SEFIP: new version SEFIP 5.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "490568 ", "URL": "/notes/490568 ", "Title": "Remuneration should not be generated when employee is absent", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "524750 ", "URL": "/notes/524750 ", "Title": "HBRSEF00 shows positive values for negative basis", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "525497 ", "URL": "/notes/525497 ", "Title": "HBRSEF00 must create 2 records type 32 for movements", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "529879 ", "URL": "/notes/529879 ", "Title": "HBRSEF00 does not fill in Admission Date for Emp.Category 07", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "538420 ", "URL": "/notes/538420 ", "Title": "Values in SEFIP for competence 12", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "564609 ", "URL": "/notes/564609 ", "Title": "HBRSEF00 saves record type 30 in case of sickness", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "568150 ", "URL": "/notes/568150 ", "Title": "HBRSEF00 does not create record type 30 and 32 for code=150", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "574709 ", "URL": "/notes/574709 ", "Title": "HBRSEF00: field 13°Sal.Calc.Basis is not filled in", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "580396 ", "URL": "/notes/580396 ", "Title": "HBRSEF00 does not create record type 20", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "580408 ", "URL": "/notes/580408 ", "Title": "HBRSEF00 does not print INSS's values", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "600857 ", "URL": "/notes/600857 ", "Title": "Legal Change SEFIP 6.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "618939 ", "URL": "/notes/618939 ", "Title": "New Version SEFIP 6.1", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "625259 ", "URL": "/notes/625259 ", "Title": "HBRSEF00 does not print value if employee is absent", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "628497 ", "URL": "/notes/628497 ", "Title": "HBRSEF00 creates record type 32 with wrong movement code", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "630300 ", "URL": "/notes/630300 ", "Title": "HBRSEF00 does not generate record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "647646 ", "URL": "/notes/647646 ", "Title": "HBRSEF00 doesn't print retrocalculated values (Termination)", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "655615 ", "URL": "/notes/655615 ", "Title": "HBRSEF00 does not create record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "657050 ", "URL": "/notes/657050 ", "Title": "HBRSEF00 generates 2 records type 20", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "672464 ", "URL": "/notes/672464 ", "Title": "HBRSEF00 does not print INSS's basis in record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "673509 ", "URL": "/notes/673509 ", "Title": "Legal Change of HBRSEF00: Version 6.3", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "731819 ", "URL": "/notes/731819 ", "Title": "HBRSEF00: /116 FGTS reversal basis negative not considered", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "739363 ", "URL": "/notes/739363 ", "Title": "HBRSEF00: Cod. Caixa Trab. not taken from IT0009", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "741923 ", "URL": "/notes/741923 ", "Title": "HBRSEF00: CTPS number should be blank instead of zeros", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "742630 ", "URL": "/notes/742630 ", "Title": "HBRSEF00: Backg.proc. not print. Pernr/T90 not considered", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "749848 ", "URL": "/notes/749848 ", "Title": "SEFIP 6.4 - Legal Change", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "754134 ", "URL": "/notes/754134 ", "Title": "SEFIP: fields alignment and retirement due disability", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "761901 ", "URL": "/notes/761901 ", "Title": "SEFIP: several corrections", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "767558 ", "URL": "/notes/767558 ", "Title": "SEFIP: maternity leave corrections", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "779775 ", "URL": "/notes/779775 ", "Title": "HBRSEF00 and maternity average /T85", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "781240 ", "URL": "/notes/781240 ", "Title": "HBRSEF00 centralizer branch selection", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "790449 ", "URL": "/notes/790449 ", "Title": "HBRSEF00 business place and centralizer branch", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "793915 ", "URL": "/notes/793915 ", "Title": "SEFIP: Remuneration without christmas bonus", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "827280 ", "URL": "/notes/827280 ", "Title": "SEFIP 7.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "852636 ", "URL": "/notes/852636 ", "Title": "HBRSEF00: Removal from special characters", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "875109 ", "URL": "/notes/875109 ", "Title": "SEFIP: code updates", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "909805 ", "URL": "/notes/909805 ", "Title": "SEFIP 8.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "910847 ", "URL": "/notes/910847 ", "Title": "SEFIP: fields 20, 22 and 23 of record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "913024 ", "URL": "/notes/913024 ", "Title": "HBRSEF00 8.0 fixes", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46B", "Number": "833613 ", "URL": "/notes/833613 ", "Title": "SEFIP - Remuneration Without Christmas Allowance", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "211535 ", "URL": "/notes/211535 ", "Title": "SEFIP - Alter. para nova versao", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "216877 ", "URL": "/notes/216877 ", "Title": "SEFIP - Correções para a versão 4.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "302086 ", "URL": "/notes/302086 ", "Title": "SEFIP - Acerto para dedução rubrica estorno FGTS", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "312839 ", "URL": "/notes/312839 ", "Title": "HBRSEF00 - Acerto para geração do registro 13", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "350858 ", "URL": "/notes/350858 ", "Title": "HBRSEF00 - Acerto cálculo do FGTS e base INSS", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "370079 ", "URL": "/notes/370079 ", "Title": "Campos 22 e 23 do reg 30 do SEFIP(inf. 13o) não preenchidos", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "831335 ", "URL": "/notes/831335 ", "Title": "HBRSEF00 - \"Data de movimento\" earlier than hire date", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "833669 ", "URL": "/notes/833669 ", "Title": "HBRSEF00 record type 30 Allowance Base", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "470", "Number": "695827 ", "URL": "/notes/695827 ", "Title": "SEFIP prints wrong value in record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "470", "Number": "705318 ", "URL": "/notes/705318 ", "Title": "HBRSEF00 doesn't fill in field Percentual of Philanthropy", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "470", "Number": "813885 ", "URL": "/notes/813885 ", "Title": "HBRSEF00 and Values related to the pre notice", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "500", "Number": "764696 ", "URL": "/notes/764696 ", "Title": "SEFIP: CTPS data in record type 13 / 13th salary difference", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "500", "Number": "855485 ", "URL": "/notes/855485 ", "Title": "Employee transference in SEFIP", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "600", "Number": "864181 ", "URL": "/notes/864181 ", "Title": "SEFIP: employee without FGTS", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "600", "Number": "886077 ", "URL": "/notes/886077 ", "Title": "HBRSEF00 Movement code Q4 not considered for field 20 rec30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "433360 ", "URL": "/notes/433360 ", "Title": "Some employees is not considered in report HBRSEF00", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "438382 ", "URL": "/notes/438382 ", "Title": "HR: Adjustment of 13th Salary in December", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "439595 ", "URL": "/notes/439595 ", "Title": "Legal Change SEFIP: new version SEFIP 5.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "483363 ", "URL": "/notes/483363 ", "Title": "Housekeeping: search information from branches/cei", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "490568 ", "URL": "/notes/490568 ", "Title": "Remuneration should not be generated when employee is absent", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "524750 ", "URL": "/notes/524750 ", "Title": "HBRSEF00 shows positive values for negative basis", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "525497 ", "URL": "/notes/525497 ", "Title": "HBRSEF00 must create 2 records type 32 for movements", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "529879 ", "URL": "/notes/529879 ", "Title": "HBRSEF00 does not fill in Admission Date for Emp.Category 07", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "538420 ", "URL": "/notes/538420 ", "Title": "Values in SEFIP for competence 12", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "564609 ", "URL": "/notes/564609 ", "Title": "HBRSEF00 saves record type 30 in case of sickness", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "568150 ", "URL": "/notes/568150 ", "Title": "HBRSEF00 does not create record type 30 and 32 for code=150", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "574709 ", "URL": "/notes/574709 ", "Title": "HBRSEF00: field 13°Sal.Calc.Basis is not filled in", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "580396 ", "URL": "/notes/580396 ", "Title": "HBRSEF00 does not create record type 20", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "580408 ", "URL": "/notes/580408 ", "Title": "HBRSEF00 does not print INSS's values", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "600857 ", "URL": "/notes/600857 ", "Title": "Legal Change SEFIP 6.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "618939 ", "URL": "/notes/618939 ", "Title": "New Version SEFIP 6.1", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "625259 ", "URL": "/notes/625259 ", "Title": "HBRSEF00 does not print value if employee is absent", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "628497 ", "URL": "/notes/628497 ", "Title": "HBRSEF00 creates record type 32 with wrong movement code", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "630300 ", "URL": "/notes/630300 ", "Title": "HBRSEF00 does not generate record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "647646 ", "URL": "/notes/647646 ", "Title": "HBRSEF00 doesn't print retrocalculated values (Termination)", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "655615 ", "URL": "/notes/655615 ", "Title": "HBRSEF00 does not create record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "657050 ", "URL": "/notes/657050 ", "Title": "HBRSEF00 generates 2 records type 20", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "672464 ", "URL": "/notes/672464 ", "Title": "HBRSEF00 does not print INSS's basis in record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "673509 ", "URL": "/notes/673509 ", "Title": "Legal Change of HBRSEF00: Version 6.3", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "731819 ", "URL": "/notes/731819 ", "Title": "HBRSEF00: /116 FGTS reversal basis negative not considered", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "739363 ", "URL": "/notes/739363 ", "Title": "HBRSEF00: Cod. Caixa Trab. not taken from IT0009", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "741923 ", "URL": "/notes/741923 ", "Title": "HBRSEF00: CTPS number should be blank instead of zeros", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "742630 ", "URL": "/notes/742630 ", "Title": "HBRSEF00: Backg.proc. not print. Pernr/T90 not considered", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "749848 ", "URL": "/notes/749848 ", "Title": "SEFIP 6.4 - Legal Change", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "754134 ", "URL": "/notes/754134 ", "Title": "SEFIP: fields alignment and retirement due disability", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "761901 ", "URL": "/notes/761901 ", "Title": "SEFIP: several corrections", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "767558 ", "URL": "/notes/767558 ", "Title": "SEFIP: maternity leave corrections", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "779775 ", "URL": "/notes/779775 ", "Title": "HBRSEF00 and maternity average /T85", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "781240 ", "URL": "/notes/781240 ", "Title": "HBRSEF00 centralizer branch selection", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "790449 ", "URL": "/notes/790449 ", "Title": "HBRSEF00 business place and centralizer branch", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "793915 ", "URL": "/notes/793915 ", "Title": "SEFIP: Remuneration without christmas bonus", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "827280 ", "URL": "/notes/827280 ", "Title": "SEFIP 7.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "852636 ", "URL": "/notes/852636 ", "Title": "HBRSEF00: Removal from special characters", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "875109 ", "URL": "/notes/875109 ", "Title": "SEFIP: code updates", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "909805 ", "URL": "/notes/909805 ", "Title": "SEFIP 8.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "910847 ", "URL": "/notes/910847 ", "Title": "SEFIP: fields 20, 22 and 23 of record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "913024 ", "URL": "/notes/913024 ", "Title": "HBRSEF00 8.0 fixes", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "433360 ", "URL": "/notes/433360 ", "Title": "Some employees is not considered in report HBRSEF00", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "435847 ", "URL": "/notes/435847 ", "Title": "Payroll stops because there's no FGTS's Bank Account", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "438382 ", "URL": "/notes/438382 ", "Title": "HR: Adjustment of 13th Salary in December", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "439595 ", "URL": "/notes/439595 ", "Title": "Legal Change SEFIP: new version SEFIP 5.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "483830 ", "URL": "/notes/483830 ", "Title": "HBRSEF00:/123 (negative values) and INSS's Basis with errors", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "490568 ", "URL": "/notes/490568 ", "Title": "Remuneration should not be generated when employee is absent", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "524750 ", "URL": "/notes/524750 ", "Title": "HBRSEF00 shows positive values for negative basis", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "525497 ", "URL": "/notes/525497 ", "Title": "HBRSEF00 must create 2 records type 32 for movements", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "529879 ", "URL": "/notes/529879 ", "Title": "HBRSEF00 does not fill in Admission Date for Emp.Category 07", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "538420 ", "URL": "/notes/538420 ", "Title": "Values in SEFIP for competence 12", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "564609 ", "URL": "/notes/564609 ", "Title": "HBRSEF00 saves record type 30 in case of sickness", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "568150 ", "URL": "/notes/568150 ", "Title": "HBRSEF00 does not create record type 30 and 32 for code=150", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "574709 ", "URL": "/notes/574709 ", "Title": "HBRSEF00: field 13°Sal.Calc.Basis is not filled in", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "580396 ", "URL": "/notes/580396 ", "Title": "HBRSEF00 does not create record type 20", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "580408 ", "URL": "/notes/580408 ", "Title": "HBRSEF00 does not print INSS's values", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "600857 ", "URL": "/notes/600857 ", "Title": "Legal Change SEFIP 6.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "618939 ", "URL": "/notes/618939 ", "Title": "New Version SEFIP 6.1", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "625259 ", "URL": "/notes/625259 ", "Title": "HBRSEF00 does not print value if employee is absent", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "628497 ", "URL": "/notes/628497 ", "Title": "HBRSEF00 creates record type 32 with wrong movement code", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "630300 ", "URL": "/notes/630300 ", "Title": "HBRSEF00 does not generate record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "647646 ", "URL": "/notes/647646 ", "Title": "HBRSEF00 doesn't print retrocalculated values (Termination)", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "655615 ", "URL": "/notes/655615 ", "Title": "HBRSEF00 does not create record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "657050 ", "URL": "/notes/657050 ", "Title": "HBRSEF00 generates 2 records type 20", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "672464 ", "URL": "/notes/672464 ", "Title": "HBRSEF00 does not print INSS's basis in record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "673509 ", "URL": "/notes/673509 ", "Title": "Legal Change of HBRSEF00: Version 6.3", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "731819 ", "URL": "/notes/731819 ", "Title": "HBRSEF00: /116 FGTS reversal basis negative not considered", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "739363 ", "URL": "/notes/739363 ", "Title": "HBRSEF00: Cod. Caixa Trab. not taken from IT0009", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "741923 ", "URL": "/notes/741923 ", "Title": "HBRSEF00: CTPS number should be blank instead of zeros", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "742630 ", "URL": "/notes/742630 ", "Title": "HBRSEF00: Backg.proc. not print. Pernr/T90 not considered", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "749848 ", "URL": "/notes/749848 ", "Title": "SEFIP 6.4 - Legal Change", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "754134 ", "URL": "/notes/754134 ", "Title": "SEFIP: fields alignment and retirement due disability", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "761901 ", "URL": "/notes/761901 ", "Title": "SEFIP: several corrections", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "767558 ", "URL": "/notes/767558 ", "Title": "SEFIP: maternity leave corrections", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "779775 ", "URL": "/notes/779775 ", "Title": "HBRSEF00 and maternity average /T85", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "781240 ", "URL": "/notes/781240 ", "Title": "HBRSEF00 centralizer branch selection", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "790449 ", "URL": "/notes/790449 ", "Title": "HBRSEF00 business place and centralizer branch", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "793915 ", "URL": "/notes/793915 ", "Title": "SEFIP: Remuneration without christmas bonus", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "827280 ", "URL": "/notes/827280 ", "Title": "SEFIP 7.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "852636 ", "URL": "/notes/852636 ", "Title": "HBRSEF00: Removal from special characters", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "875109 ", "URL": "/notes/875109 ", "Title": "SEFIP: code updates", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "909805 ", "URL": "/notes/909805 ", "Title": "SEFIP 8.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "910847 ", "URL": "/notes/910847 ", "Title": "SEFIP: fields 20, 22 and 23 of record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "500", "Number": "833613 ", "URL": "/notes/833613 ", "Title": "SEFIP - Remuneration Without Christmas Allowance", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "754134 ", "URL": "/notes/754134 ", "Title": "SEFIP: fields alignment and retirement due disability", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "761901 ", "URL": "/notes/761901 ", "Title": "SEFIP: several corrections", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "500", "Number": "833669 ", "URL": "/notes/833669 ", "Title": "HBRSEF00 record type 30 Allowance Base", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "500", "Number": "909805 ", "URL": "/notes/909805 ", "Title": "SEFIP 8.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "739363 ", "URL": "/notes/739363 ", "Title": "HBRSEF00: Cod. Caixa Trab. not taken from IT0009", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "741923 ", "URL": "/notes/741923 ", "Title": "HBRSEF00: CTPS number should be blank instead of zeros", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "742630 ", "URL": "/notes/742630 ", "Title": "HBRSEF00: Backg.proc. not print. Pernr/T90 not considered", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "749848 ", "URL": "/notes/749848 ", "Title": "SEFIP 6.4 - Legal Change", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "754134 ", "URL": "/notes/754134 ", "Title": "SEFIP: fields alignment and retirement due disability", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "761901 ", "URL": "/notes/761901 ", "Title": "SEFIP: several corrections", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "779775 ", "URL": "/notes/779775 ", "Title": "HBRSEF00 and maternity average /T85", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "781240 ", "URL": "/notes/781240 ", "Title": "HBRSEF00 centralizer branch selection", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "790449 ", "URL": "/notes/790449 ", "Title": "HBRSEF00 business place and centralizer branch", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "793915 ", "URL": "/notes/793915 ", "Title": "SEFIP: Remuneration without christmas bonus", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "827280 ", "URL": "/notes/827280 ", "Title": "SEFIP 7.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "831335 ", "URL": "/notes/831335 ", "Title": "HBRSEF00 - \"Data de movimento\" earlier than hire date", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "875109 ", "URL": "/notes/875109 ", "Title": "SEFIP: code updates", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "910847 ", "URL": "/notes/910847 ", "Title": "SEFIP: fields 20, 22 and 23 of record type 30", "Component": "PY-BR"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}