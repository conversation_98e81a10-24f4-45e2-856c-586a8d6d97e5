{"Request": {"Number": "1574751", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 266, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017199232017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001574751?language=E&token=C2F1BD596FB96DFDBB8DE166FFAE8D00"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001574751", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001574751/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1574751"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "External error"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.03.2013"}, "SAPComponentKey": {"_label": "Component", "value": "BC-OP-LNX"}, "SAPComponentKeyText": {"_label": "Component", "value": "Linux"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Operating System Platforms", "value": "BC-OP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Linux", "value": "BC-OP-LNX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP-LNX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1574751 - Linux: libc.so.6: version `GLIBC_2.7' not found"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>On your Linux, SAP binaries do not start properly. One of the following error messages displayed on the screen or in the logs or in the trace files:</p> <OL>1. GLIBC library related messages such as</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/lib64/libc.so.6: version `GLIBC_2. 11' not found (required by disp+work)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/lib64/libc.so.6: version `GLIBC_2.10' not found (...)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/lib64/libc.so.6: version `GLIBC_2.9' not found (...)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/lib64/libc.so.6: version `GLIBC_2.8' not found (...)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/lib64/libc.so.6: version `GLIBC_2.7' not found (...)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/lib64/libc.so.6: version `GLIBC_2.6' not found (...)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/lib64/libc.so.6: version `GLIBC_2.5' not found (...)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/lib64/tls/libc.so.6: version `GLIBC_2.4' not found (... )<br /></p> <OL>2. ICU library related messages such as</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Could not open the ICU common library.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The following files must be in the path described by the environment variable \"LD_LIBRARY_PATH\":<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;libicuuc.so.40, libicudata.so.40, libicui18n.so.40 [nlsui0_mt. c 1544]</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Linux, GLIBC_2.7, libc.so.6, 7.20 EXT, 720_EXT, 721_EXT, 7.38, 738, 7.40, 740, 640_EX2, 46D_EX2</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><OL>1. The Linux error message \"libc.so.6: version `GLIBC_&lt;version&gt;' not found\" generally appears if you run binaries that are not compiled for your Linux operating system release, for example</OL> <UL><UL><LI>The 7.20 EXT kernel and other 7.20 EXT components such as SAP IGS or brtools require SuSE Linux Enterprise Server (SLES) 11 SP1 or RedHat Enterprise Linux (RHEL) 6 (note 1563102), because they are compiled against glibc-2.11.</LI></UL></UL> <UL><UL><LI>The 7.21 EXT kernel as well as the 7.38 and 7.40 kernel also require SLES 11 SP1 or RHEL 6 (note 1563102), because they are compiled against glibc-2.11.</LI></UL></UL> <UL><UL><LI>The 46D_EX2 and 640_EX2 kernels and other components require SLES 10 or RHEL 5 (note 1271224), because they are compiled against glibc-2.4.</LI></UL></UL> <OL>2. The libicu*.so.40 libraries are delivered starting with the 7.20 EXT kernel (7.21 EXT, 7.38 and 7.40 kernels include the libicu*.so.40 libraries as well). If you install EXT kernel components (such as IGS) without having the EXT kernel installed you get the error message about missing libicu*.so.40 libraries.</OL> <OL>3. The libicu*.so.50 libraries are delivered starting with SAP kernel 7.40.</OL> <p><br />It is known that current SAP upgrade processes lack some precision and easiness, especially regarding operating system and database alternatives, so that it is likely possible to make mistakes. This could have various reasons in different components: incomplete PPMS (SAP Product and Production Management System) information, deficiencies in Solution Manager Maintenance Optimizer (MOpz) or upgrade tools including EHPI or SUM, incomplete or bad documentation, and last but not least human mistakes such as selecting the wrong alternative (e.g. the 7.20 EXT variant) during the upgrade process.<br /><br />The current upgrade tools do not consider all database or operating system prerequisites such as those for the EXT kernel and thus both kernels could be displayed for human selection even if operating system prerequisites for 7.20 EXT are not met. If you then select both non-EXT and EXT kernels, the upgrade process will not complain, and the latter one (i.e. the EXT kernel) will be installed without any further check. This installation will fail if the operating system prerequisites for 7.20 EXT are not met.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. If you want or must use the 7.20 EXT, 7.21 EXT, 7.38, 7.40, 640_EX2, or 46D_EX2 kernel:</OL> <UL><UL><LI>Install or upgrade your Linux operating system so that you meet the minimal requirements (7.20 EXT: note 1563102, 640_EX2, 46D_EX2: note 1271224).</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you have accidently selected an EXT or EX2 kernel on an older Linux system:</p> <UL><UL><LI>Make sure that you do not use an EXT or EX2 kernel! Please install the non-EXT or non-EX2 kernel/component instead. Be careful when upgrade tools let you choose the 7.20 or the 7.20 EXT kernel. Maybe it is needed to manually extract/copy the files from a non-EXT or non-EX2 SAR archive into the corresponding directory.</LI></UL></UL> <OL>2. Consider the operating system requirements for all components and follow the instructions above for all components.</OL> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D047687)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D036493)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001574751/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001574751/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001574751/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001574751/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001574751/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001574751/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001574751/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001574751/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001574751/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "171356", "RefComponent": "BC-OP-LNX", "RefTitle": "SAP Software on Linux: General information", "RefUrl": "/notes/171356"}, {"RefNumber": "1563102", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux Requirements for 7.20 EXT and higher kernel", "RefUrl": "/notes/1563102"}, {"RefNumber": "1271224", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux: Minimum versions for distribution w/ SAP EX2_Kernel", "RefUrl": "/notes/1271224"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2369910", "RefComponent": "BC-OP-LNX", "RefTitle": "SAP Software on Linux: General information", "RefUrl": "/notes/2369910 "}, {"RefNumber": "1563102", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux Requirements for 7.20 EXT and higher kernel", "RefUrl": "/notes/1563102 "}, {"RefNumber": "1271224", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux: Minimum versions for distribution w/ SAP EX2_Kernel", "RefUrl": "/notes/1271224 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}