{"Request": {"Number": "100609", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 388, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014556672017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000100609?language=E&token=27B60D9D0FC780D91F899A66BE5C8166"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000100609", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000100609/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "100609"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 62}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.08.2004"}, "SAPComponentKey": {"_label": "Component", "value": "FI-GL-IS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Information System"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Accounting", "value": "FI-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Information System", "value": "FI-GL-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "100609 - Audit Information System (AIS) - installation"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p><br /><STRONG>Audit Information System (AIS) - installation</STRONG><br />This note describes the procedure for importing AIS in SAP Releases up<br />to 4.5.<br />There is a general description of this in Note 77503.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>AIS, audit infosystem, audit info System,<br />revision, system revision, auditor<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><b>Installation of the Audit Information System:</b><br /> <p>The AIS is a part of the SAP standard system in Release 3.1I and as of Release 4.6A. For previous maintenance levels as of 3.0D, you can import the AIS according to the following instructions. Please take care to use the correct transport files depending on your target release.<br /><br /><B>1. Import the AIS </B><br /><B>2. Import queries </B><br /><B>3. Import drilldown reports </B><br /><B>4. User master records for the auditors </B><br /><B>5. Additional Information</B><br />------------------------------------------------------------------------</p> <b>1. Import the AIS<br /></b><br /> <p>The AIS is stored on SAP service computers (SAPSERV_) under the following path: &#x00A0;&#x00A0; ~ftp/specific/audit_info<br />You can unpack the archive files with extension .car stored here using<br />program sapcar described in Note 212876.<br />You can download the data with the SAP Workbench Organizer (Correction and Transport System) to your computer.<br />Carry out the import of the target system in clients <B>000</B> as set out in SAP Note 13719. &#x00A0;&#x00A0;( ... import&#x00A0;&#x00A0;client000&#x00A0;&#x00A0;transport&#x00A0;&#x00A0;U168 ... )<br /><br /><B>For 3.0D, 3.0F, 3.1H and 3.1I only</B><br />You can import the AIS into client 000 or into your production client. In case of importing into client 000 you have to copy client-specific objects on the transport order into your production client using Transaction SCC1.<br /><br />Transport request N31K000038 of current version 1.5.2 of the AIS includes the following files in the directory version1.5.2 :<br />&#x00A0;&#x00A0;File&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Size&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Date &#x00A0;&#x00A0;D000038.N31&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 833331 May&#x00A0;&#x00A0;6 15:51<BR/> &#x00A0;&#x00A0;K000038.N31&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;314 May&#x00A0;&#x00A0;6 15:51 &#x00A0;&#x00A0;R000038.N31&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;3173126 May&#x00A0;&#x00A0;6 15:51<br />These files are stored in the zip archive AIS1.5.2.zip .<br /><br />If you have already installed AIS version 1.5.1, it is sufficient to import transport N31K000038. Steps 2-5 are then no longer required. The important changes for version 1.5.2 compared to previous versions are set out in Note 77503. Users can then decide for themselves whether an Upgrade to 1.5.2 is necessary.<br /><br /><B>For 4.0B only</B><br />You have to import into client <B>000</B>. There is no need to copy client-specific objects into the production client.<br /><br />Transport request N40K000069 of the current version 40B.5 of the AIS includes the following files in the directory version40B.5 :<br />&#x00A0;&#x00A0;File&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Size&#x00A0;&#x00A0;&#x00A0;&#x00A0;Date &#x00A0;&#x00A0;D000069.N40&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;727.747 Nov. 7, 2000<BR/> &#x00A0;&#x00A0;K000069.N40&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;197 Nov. 7, 2000<BR/> &#x00A0;&#x00A0;R000069.N40&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4.793.974 Nov. 7, 2000<br />These files are stored in the car archive AIS40B.5.car . <br />If you have already installed AIS version 4.6, 4.6.1, 4.6.2, 40B.3 or 40B.4 it is sufficient to import transport N40K000069. Steps 2-5 are then no longer required. The important changes for version 40B.5 compared to previous versions are set out in Note 77503. Users can them decide for themselves whether an upgrade to 40B.5 is necessary.<br /><br /><B>For 4.5B only</B><br />You have to import into client <B>000</B>. There is no need to copy client-specific objects into the production client.<br /><br />Transport request N45K000129 of the current version 45B.3 of the AIS includes the following files in the directory version45B.3 :<br />&#x00A0;&#x00A0;File&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Size&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Date &#x00A0;&#x00A0;D000129.N45&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;761.644&#x00A0;&#x00A0;&#x00A0;&#x00A0;Jan 31, 2001<br />&#x00A0;&#x00A0;K000129.N45&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;895&#x00A0;&#x00A0;&#x00A0;&#x00A0;Jan 31, 2001<BR/> &#x00A0;&#x00A0;R000129.N45&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5.846.752&#x00A0;&#x00A0;&#x00A0;&#x00A0;Jan 31, 2001<br />These files are stored in&#x00A0;&#x00A0;car archive AIS45B.3.car .<br /><br />If you have already installed AIS version 45B.1 it is sufficient to import transport N45K000129. Steps 2-5 are then no longer required. The important changes for version 45B.2 compared to previous versions are set out in Note 77503. Users can them decide for themselves whether an upgrade to 45B.3 is necessary.<br /><br /><B>For all correction levels</B><br />The object list is stored in the same directory in the file &#x00A0;&#x00A0;objlst.txt<br />By using this before the import you can check which objects are to be imported.<br />In the file &#x00A0;&#x00A0;readme.txt<br />there is a brief description of the AIS transport files.<br />------------------------------------------------------------------------</p> <b>2.Import queries</b><br /> <p><br /><B>For Releases 3.0D, 3.0F, 3.1H and 3.1I only</B><br />Start Transaction SECR in the target client with the function<br />&#x00A0;&#x00A0;-&gt; Extras -&gt; Installation -&gt; Import Queries<br /><br />The selection screen for the import of queries is called up. All necessary parameters are set as default.<br />Execute the program.<br /><br />Using the user-defined transport system, the following query data is transferred:<br /><br />&#x00A0;&#x00A0;User group&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;AU<br />&#x00A0;&#x00A0;Function area/Query/Var&#x00A0;&#x00A0; AUBR/AB/SAP&amp;AUDIT &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;AUDD/AD/SAP&amp;AUDIT<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;AUKD/AK/SAP&amp;AUDIT<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;AUSD/AS/SAP&amp;AUDIT<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;AUSD/AT/SAP&amp;AUDIT<br /><br />To transfer the queries of Asset Accounting for user group AM you should observe the release notes for Release 3.0B:<br />-&gt; Financial Accounting -&gt; Asset Accounting -&gt; New Information System<br /><br /><br /><B>For Release 4.*B only</B><br />After a release upgrade from Release 3.x to Release 4.y it is necessary for you to covert the query objects (queries, functional areas, user groups). If you have not yet made this conversion, you receive the following message when calling up queries:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; AQ898 \"Execute conversion first\".<br /><br />This error message is also issued if a new installation of an R/3 System has been carried out based on Release 4.y.<br />In this case refer to Note 92124 on the conversion of the queries.<br /><br /><B>For all correction levels</B><br />To be able to work with query data, your must enter your user name in the user groups AU and AM (Transaction SQ03).<br />------------------------------------------------------------------------</p> <b>3. Import drilldown reports<br /></b><br /> <p>Start Transaction SECR in the target client with the function<br />&#x00A0;&#x00A0;-&gt; Extras -&gt; Installation -&gt; Import drilldown report<br /><br />You should import the following drilldown reports:<br />&#x00A0;&#x00A0; Report type&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Report<br />&#x00A0;&#x00A0; Balance sheet key figures RFRRS02 01 0SAPAUDIT Export to Baetge<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RFRRS02 01 0SAPAUDIT Balance sheet key<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;figures<br />&#x00A0;&#x00A0;Balance display&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; RFRRS10 01 0SAPAUDIT Export to audit agent<br /><br />Each of the drilldown reports is imported in the logon language. If you are working with more than one language, repeat this step for each language.<br />------------------------------------------------------------------------</p> <b>4. User master records for auditors<br /></b><br /> <p>AIS users need a user master record with display authorizations from several components.<br />The necessary authorizations can be found in the following SAP profiles (amongst others)<br />&#x00A0;&#x00A0;A_ANZ, F_ANZ, M_ANZ, S_A.SHOW, ...<br /><br />To be able to carry out evaluations with drilldown reporting, the following authorization must be assigned:</p> <UL><UL><LI>Object class Controlling</LI></UL></UL> <UL><UL><LI>Object CO: Interactive drilldown reports</LI></UL></UL> <UL><UL><LI>Authorization nnn</LI></UL></UL> <UL><UL><LI>Fields&#x00A0;&#x00A0;Activity&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"03,16,25,61,L0,L1,L2\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Applic. class for drill-down \"*\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Report&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"*\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Table name&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"*\"<br /></LI></UL></UL> <p>The AIS is protected by the transaction code start authorization for authorization object S_TCODE.<br />You must also assign an authorization for Transactions SECR and 0REP to the users, in addition to the above-mentioned profiles.<br />For the system audit more comprehensive authorizations are sometimes necessary. Extend the transaction code start authorization as and when needed. There is at present no 'generate' function.<br />You can display the quantity of the transactions and programs entered in the AIS with Transaction SECR function 'Find program'.<br />------------------------------------------------------------------------</p> <b>5. Additional information.<br /></b><br /> <OL>1. On the SAP service computers (SAPSERV_), in the above-mentioned directory you will find the file &#x00A0;&#x00A0;feedback.rtfcontaining a questionnaire regarding the Audit Information System. You are requested to inform SAP of any comments or suggestions you may have about the AIS by filling in this questionnaire.</OL> <OL>2. The AIS is an independent component. Existing objects have, in some instances, been enhanced (for example, through system variants for reports), but otherwise remain unchanged. With the object list, you can check prior to the import whether customer-specific modifications have in fact been influenced.</OL> <OL>3. The subsequent transport of the AIS from a final preparation system into a production system can be achieved by the direct import of the above-mentioned transport request.</OL> <OL>4. <B>Only for Releases 3.0D, 3.0F, 3.1H und 3.1I</B>: You also have to copy report system variants for a client copy. Check SAPNet Note 41475 for details. To copy the system variants after the client copy has finished follow this note and use the transport request of the AIS as a template for Transaction SCC1.</OL> <OL>5. You must import the Audit Information System again following an upgrade.</OL> <OL>6. For the status management, the Test Workbench functions are used, while the Test Workbench uses functions of the Implementation Guide. In particular, status records are stored under IMG project 900. This project is normally generated automatically. If this is not the case, you can use report RSCATINI of the Test Workbench to create project 900.</OL> <OL>7. When you import version 1.5.1 and 1.5.2 into a system with a maintenance level prior to 3.1H, a generation error is displayed on screen RSAUDITA 0001 (Field OBJSUB-WORKFLOW does not exist). This error is corrected in a later AIS version. The affected function is not required by AIS users. The error can therefore be ignored.</OL> <OL>8. As of R/3 Release 4.0 you must import AIS into client <B>000</B> so that only the SAP system variants of reports which were delivered with the AIS are available. If you import into another client, you will receive a whole series of transport errors.</OL> <OL>9. Is there a way that I can check to see if the the AIS query user exit is installed?<br />Call up Transaction CMOD<br />Use the F4-Help for field 'Project'<br />Choose 'Infosystem' if needed<br />Expand all selections if needed<br />Enter 'SQUE0001' into extra selection field 'SAP Enhancement Exitname' and proceed<br />You'll get the project containing this exit<br /><br />Mark radiobutton 'Attributes' and choose function 'Display' to view the status (active/inactive) of the project.<br /><br />Mark radiobutton 'Enhancement Components' and choose function 'Display' (depending on the correction level you have to choose the function 'Change')<br />Doubleclick on line 'Functionexit EXIT_RSAQEXCE_001'<br />If needed, choose function 'Source Code'<br />Scroll down to the end<br />Doubleclick on line 'INCLUDE ZXQUEU01.'<br />Now you should see a line 'include rxqueu00.'. This indicated the AIS<br />query user exit.</OL> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-SEC-AIS (System Audit Information System)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D019687)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D019687)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000100609/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000100609/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000100609/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000100609/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000100609/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000100609/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000100609/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000100609/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000100609/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "77503", "RefComponent": "FI-GL-IS", "RefTitle": "Audit Information System (AIS)", "RefUrl": "/notes/77503"}, {"RefNumber": "496534", "RefComponent": "FI-GL-IS", "RefTitle": "Audit information system query export of large datasets", "RefUrl": "/notes/496534"}, {"RefNumber": "414365", "RefComponent": "FI-GL-IS", "RefTitle": "A4012, 'Subobject not found'", "RefUrl": "/notes/414365"}, {"RefNumber": "376779", "RefComponent": "FI-GL-IS", "RefTitle": "RSQUEU01: Missing FM \"F4IF_INT_TABLE_VALUE_REQUEST\"", "RefUrl": "/notes/376779"}, {"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "197137", "RefComponent": "FI-GL-IS", "RefTitle": "Audit Info System Query Download from EBCDIC server", "RefUrl": "/notes/197137"}, {"RefNumber": "187483", "RefComponent": "FI-GL-IS", "RefTitle": "Audit Information System: Incomplete activity group", "RefUrl": "/notes/187483"}, {"RefNumber": "182699", "RefComponent": "FI-GL-IS", "RefTitle": "Audit Information System (AIS): Downloading Query data", "RefUrl": "/notes/182699"}, {"RefNumber": "162971", "RefComponent": "FI-GL-IS", "RefTitle": "Audit Information System (AIS) 3.x 4.0 4.5: version history", "RefUrl": "/notes/162971"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "129170", "RefComponent": "FI-GL-IS", "RefTitle": "Audit information system: Download of query data", "RefUrl": "/notes/129170"}, {"RefNumber": "128256", "RefComponent": "FI-GL-IS", "RefTitle": "Audit info system: Missing English texts", "RefUrl": "/notes/128256"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "77503", "RefComponent": "FI-GL-IS", "RefTitle": "Audit Information System (AIS)", "RefUrl": "/notes/77503 "}, {"RefNumber": "129170", "RefComponent": "FI-GL-IS", "RefTitle": "Audit information system: Download of query data", "RefUrl": "/notes/129170 "}, {"RefNumber": "182699", "RefComponent": "FI-GL-IS", "RefTitle": "Audit Information System (AIS): Downloading Query data", "RefUrl": "/notes/182699 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "376779", "RefComponent": "FI-GL-IS", "RefTitle": "RSQUEU01: Missing FM \"F4IF_INT_TABLE_VALUE_REQUEST\"", "RefUrl": "/notes/376779 "}, {"RefNumber": "162971", "RefComponent": "FI-GL-IS", "RefTitle": "Audit Information System (AIS) 3.x 4.0 4.5: version history", "RefUrl": "/notes/162971 "}, {"RefNumber": "197137", "RefComponent": "FI-GL-IS", "RefTitle": "Audit Info System Query Download from EBCDIC server", "RefUrl": "/notes/197137 "}, {"RefNumber": "496534", "RefComponent": "FI-GL-IS", "RefTitle": "Audit information system query export of large datasets", "RefUrl": "/notes/496534 "}, {"RefNumber": "187483", "RefComponent": "FI-GL-IS", "RefTitle": "Audit Information System: Incomplete activity group", "RefUrl": "/notes/187483 "}, {"RefNumber": "128256", "RefComponent": "FI-GL-IS", "RefTitle": "Audit info system: Missing English texts", "RefUrl": "/notes/128256 "}, {"RefNumber": "414365", "RefComponent": "FI-GL-IS", "RefTitle": "A4012, 'Subobject not found'", "RefUrl": "/notes/414365 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30D", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}