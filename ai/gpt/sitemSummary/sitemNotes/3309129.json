{"Request": {"Number": "3309129", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 326, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000266322023"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003309129?language=E&token=C5717D168A27A74A8D9C86FF8E5D2989"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003309129", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0003309129/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3309129"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.03.2023"}, "SAPComponentKey": {"_label": "Component", "value": "FIN-FIO-FCC-RMT"}, "SAPComponentKeyText": {"_label": "Component", "value": " Advanced Financial Closing - Remote"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financials", "value": "FIN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Fiori UI for Financials", "value": "FIN-FIO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-FIO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Advanced Financial Closing", "value": "FIN-FIO-FCC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-FIO-FCC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Advanced Financial Closing - Remote", "value": "FIN-FIO-FCC-RMT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-FIO-FCC-RMT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "3309129 - AFC Content management: missing or insufficient documentation for validation and adapter classes"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p id=\"\">When editing the local settings of Advanced Financial Closing, you are missing a documentation of various customizing options.</p>\r\n<p>In the activity \"Define Task List Model\" when creating or changing a Task Model, you want to understand when and how to properly take benefit of the fields \"Name of Validation Class\" and \"Name of Adapter Class\".</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p id=\"\">SAP S/4HANA Cloud for advanced financial closing (AFC)</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><span style=\"font-size: 14px;\">Missing or insufficient documentation</span></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>&#160;The information, which is temporarily described below in this note, is planned to later be provided in the <a target=\"_blank\" href=\"https://help.sap.com/docs/AFC/b021b34a2c2d4943abc3a31008c7bdfc/38f2e79cddaa4465a9233c5ae1fcead6.html?locale=en-US\">Administration Guide</a> or in the <a target=\"_blank\" href=\"https://help.sap.com/docs/AFC/a32675ceb29149fd9be78a66704da190/1efe226b825d4b5fad3178d6ec98a9a9.html?locale=en-US\">Advanced Financial Closing Local Settings Guide</a>&#160;of Advanced Financial Closing.</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>Scheduling Closing Tasks - How and when to use a Validation Class in Task Models:</strong></span></p>\r\n<p><strong>Purpose</strong></p>\r\n<ul>\r\n<li>A validation class allows for modification of an business status at the end of a report or application job*) when ever the standard behaviour is not sufficient.</li>\r\n</ul>\r\n<p><strong>Use Cases</strong></p>\r\n<ul>\r\n<li>There are customer specific use cases to set the business status by Joblog status only and NOT by Application Log. Eg: In Application log you find error messages but you want to ignore them all.</li>\r\n<li>You want to react on certain messages (Class/Number/Type) and treat them different to others.</li>\r\n</ul>\r\n<p><strong>Remarks</strong></p>\r\n<ul>\r\n<li>A validation class is assigned to a task model in the business system.</li>\r\n<li>View Cluster is 'FCCX_VC_TASKSET' (&#160;IMG&#160;Activity 'Define Task List Model'), view 'Task Model', column 'Name of Validation Class' , F4 help available.</li>\r\n<li>A validation class must be implementing the interface IF_FCCX_VALIDATION, which exposes one method only: VALIDATE ( see below ).&#160;</li>\r\n<li>We recommend not to implement the interface directly, but derive from class CL_FCCX_VALIDATION_BASE ( or its subclasses ).&#160;&#160;</li>\r\n<li>if no validation class is provided, CL_FCCX_VALIDATION_BASE is used, which does not change the default behaviour.</li>\r\n<li>Note that (especially for customer specific reports) the validation class is <strong>only called and effective</strong>,<strong> if note <a target=\"_blank\" href=\"/notes/2879220\">2879220</a></strong>(\"Advanced Financial Closing: integration of customer-specific reports\") <strong>is properly applied</strong> and the&#160;report either calls the Function Module 'KPEP_MONI_CLOSE_RECORD' or the interface method if_fccx_log&#8594;close( ) at the end of the reports batch part ( end of event 'start of selection' or event 'end of selection' )!&#160;</li>\r\n</ul>\r\n<p><strong>Business Status Derivation</strong></p>\r\n<ul>\r\n<li>Prio 1: If the status of the batch job is aborted, the resulting business status will be aborted, else...</li>\r\n<li>Prio 2: if for each org context an explicit business status&#160; exists (based on and provided by &#160;'KPEP_MONI_CLOSE_RECORD' param:&#160;&#160;LD_APLSTAT&#160; &#160;or interface method if_fccx_log&#8594;log_biusnes_status( ... )),&#160; the resulting business status will be taken from this source in a pessimistic aggregation approach, else...</li>\r\n<li>Prio 3: if Application Log(s) exist(s) for the batch job, the resulting business status will be taken from this source following again a pessimistic approach,&#160; else...</li>\r\n<li>Prio 4: if Batch Input Result(s) exist(s) for the batch job, the resulting business status will be taken from this source,&#160;following a&#160;pessimistic approach, else...</li>\r\n<li>Prio 5: the resulting business status will be taken from the job log in a pessimistic approach. There can only&#160;ONE&#160;Job log per execution.</li>\r\n<li>This behaviour in effect also for jobs where the adapter class is not called at all.</li>\r\n</ul>\r\n<p><strong>Changing Business Status Derivation</strong></p>\r\n<ul>\r\n<li>The Business Status Derivation logic is hard coded and cannot be changed directly.</li>\r\n<li>But via the a <em>validation class</em> you can set the business status per Org Context.</li>\r\n<li>SAP delivers a set of predefined validation classes which are all subclasses of CL_FCCX_VALIDATION_BASE:\r\n<ul>\r\n<li>CL_FCCX_VALIDATION_BY_BTC_INP Derive Business Status from Batch Input Queue Items</li>\r\n<li>CL_FCCX_VALIDATION_BY_JOBLOG Derive Business Status from JobLog</li>\r\n<li>CL_FCCX_VALIDATION_BY_JOBLOG_M Derive Business Status from Joblog Messages (&#160;not delivered so far, as of Nov, 11th 2022&#160;)&#160;</li>\r\n<li>CL_FCCX_VALIDATION_BY_MESSAGE Derive Business Status by from messages</li>\r\n</ul>\r\n</li>\r\n<li>If you have special requirements you can implement your own validation class by deriving from CL_FCCX_VALIDATION_BASE and redefine method validate. Please check the already existing implementations for sample code.</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\"><strong>Scheduling Closing Tasks -&#160;How and when to use an Adapter Class in Task Models:</strong></span></p>\r\n<p><strong>Purpose</strong></p>\r\n<ul>\r\n<li>An adapter class can be used to validate and modify parameter sets handed over to Reports/Application Jobs directly before scheduling.</li>\r\n<li>It allows for setting highly dynamic parameters which cannot be provided within program registration fixed value- or rule based (View Cluster is 'FCCX_VC_PROMAINT' (&#160;IMG&#160;Activity 'Register Programs for Parameter Mapping'))&#160;</li>\r\n<li>An Adapter class is similar to the Check Class functionality of Application Jobs.&#160;</li>\r\n<li>The Adapter class can only be implemented and used based on a thorough expertise in ABAP-programming.</li>\r\n</ul>\r\n<p><strong>It is assigned to a task model in the business system.</strong></p>\r\n<p>View Cluster is 'FCCX_VC_TASKSET' (&#160;IMG&#160;Activity 'Define Task List Model'), view 'Task Model', column 'Name of Adapter Class' .</p>\r\n<p><strong>An adapter class implements the interface IF_FCCX_SCHEDULE_ADAPTER which exposes only one method: MODIFY.</strong></p>\r\n<p>Its purpose is to validate and modify the selection set handed over to the report that is to be scheduled by the AFC framework.</p>\r\n<p>The time point where the method is called is directly before the scheduling request is handed over to the Netweaver APIs</p>\r\n<p>Usually one should NOT implement the interface directly but derive from CL_FCCX_SCHEDULE_ADAPTER_BASE</p>\r\n<p><strong>Details on the method modify</strong></p>\r\n<div class=\"table-wrap\">\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" class=\"wrapped confluenceTable tablesorter tablesorter-default stickyTableHeaders\"><colgroup><col /><col /><col /><col /></colgroup>\r\n<thead class=\"tableFloatingHeaderOriginal\">\r\n<tr class=\"tablesorter-headerRow\"><th class=\"confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted\" scope=\"col\">\r\n<div class=\"tablesorter-header-inner\">Parameter</div>\r\n</th><th class=\"confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted\" colspan=\"1\" scope=\"col\">\r\n<div class=\"tablesorter-header-inner\">Input/Output/Exception</div>\r\n</th><th class=\"confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted\" colspan=\"1\" scope=\"col\">\r\n<div class=\"tablesorter-header-inner\">Type</div>\r\n</th><th class=\"confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted\" scope=\"col\">\r\n<div class=\"tablesorter-header-inner\">Description</div>\r\n</th></tr>\r\n</thead>\r\n<tbody>\r\n<tr>\r\n<td class=\"confluenceTd\">IV_TASK</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">In</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">FCCX_TASKC</td>\r\n<td class=\"confluenceTd\">ID of task model</td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\">IV_USERNAME</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">In</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">UNAME</td>\r\n<td class=\"confluenceTd\">ID auf business user</td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\">IO_VARIANT</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">In</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">IF_FCCX_VARIANT</td>\r\n<td class=\"confluenceTd\">Set of Interface methods to read and modify the selection set handed over (details below)</td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\">IV_TEST_RUN</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">In</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">BOOLEAN</td>\r\n<td class=\"confluenceTd\">Running in test mode (if supported by the report and configured in view cluster FCCX_VC_PROMAINT)</td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\">EV_SUCCESSFUL</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">Out</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">BOOLEAN</td>\r\n<td class=\"confluenceTd\">Should return abap_true if parameter set was (modified and )validated correctly&#160;</td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\">ET_MSG</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">Out</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">BAPIRET2_T</td>\r\n<td class=\"confluenceTd\">return messages to the AFC framework. In case of unsuccessful: MUST RETURN an E-Message here</td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\" colspan=\"1\">EV_TESTRUN_HANDLED</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">Out</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">BOOLEAN</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">Do not use!!!</td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\" colspan=\"1\">CX_FCCX_EXCEPTION</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">Exception</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">CX_FCCX_EXCEPTION</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">You should NOT raise this exception in your implementation directly. Use EV_SUCCESSFUL and ET_MSG instead</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n</div>\r\n<p><strong>The interface IF_FCCX_VARIANT:</strong></p>\r\n<div class=\"table-wrap\">\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" class=\"wrapped confluenceTable tablesorter tablesorter-default stickyTableHeaders\"><colgroup><col /><col /><col /></colgroup>\r\n<thead class=\"tableFloatingHeaderOriginal\">\r\n<tr class=\"tablesorter-headerRow\"><th class=\"confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted\" scope=\"col\">\r\n<div class=\"tablesorter-header-inner\">Method Name</div>\r\n</th><th class=\"confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted\" colspan=\"1\" scope=\"col\">\r\n<div class=\"tablesorter-header-inner\">Getter/Setter</div>\r\n</th><th class=\"confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted\" scope=\"col\">\r\n<div class=\"tablesorter-header-inner\">Description</div>\r\n</th></tr>\r\n</thead>\r\n<tbody>\r\n<tr>\r\n<td class=\"confluenceTd\">GET_SELECT_OPTION</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">Get</td>\r\n<td class=\"confluenceTd\">Read data of an attribute of type select-option</td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\">GET_REPORT_NAME</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">Get</td>\r\n<td class=\"confluenceTd\">Returns the Current Report Name</td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\">GET_SELECTION_SET</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">Get</td>\r\n<td class=\"confluenceTd\">Read the whole selection set</td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\">GET_RADIO_BUTTON_GROUPS_IDS</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">Get</td>\r\n<td class=\"confluenceTd\">Get all Radio Button Group IDs</td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\">GET_BUTTONS_FOR_GROUP_ID</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">Get</td>\r\n<td class=\"confluenceTd\">Get the IDs of attributes assigned to a Radio Button Group</td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\">GET_PARAMETER</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">Get</td>\r\n<td class=\"confluenceTd\">Read data of a attribute of type parameter</td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\">GET_CHECKED_RADIO_BUTTON</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">Get</td>\r\n<td class=\"confluenceTd\">Get the checked Button of a Radio Button Group</td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\">SET_PARAMETER</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">Set</td>\r\n<td class=\"confluenceTd\">Sets a value for an attribute of type parameter</td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\">SET_SELECT_OPTION</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">Set</td>\r\n<td class=\"confluenceTd\">Sets a value for an attribute of type select-option</td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\" colspan=\"1\">SET_RADIO_BUTTON_CHECKED</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">Set</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">Sets Radio Button to checked</td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\" colspan=\"1\">CHECK_AND_CORRECT</td>\r\n<td class=\"confluenceTd\" colspan=\"1\"></td>\r\n<td class=\"confluenceTd\" colspan=\"1\">Cleanup and check, do NOT CALL directly !!!</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n</div>\r\n<p>The signatures of the methods are self explaining:</p>\r\n<ul>\r\n<li>Getters: usually a &lt;parameter name&gt; as input parameter and CX_FCCX_EXCEPTION as exception class&#160;</li>\r\n<li>Setters: like getters plus an appropriate value for setting ( value or select-option ).</li>\r\n</ul>\r\n<p>In case of any error (eq a parameter does not exist) an exception of type CX_FCCX_EXCEPTION is raised.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D025965)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D025965)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0003309129/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003309129/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003309129/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003309129/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003309129/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003309129/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003309129/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003309129/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003309129/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2873915", "RefComponent": "FIN-FIO-FCC", "RefTitle": "FAQ SAP Advanced Financial Closing", "RefUrl": "/notes/2873915 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}