{"Request": {"Number": "1643878", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 315, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017328492017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001643878?language=E&token=DB804EBD6F3E7D02F198048DF988DA70"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001643878", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001643878/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1643878"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 17}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.01.2018"}, "SAPComponentKey": {"_label": "Component", "value": "BC-IAM-SSO-SL"}, "SAPComponentKeyText": {"_label": "Component", "value": "<PERSON><PERSON>"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Identity and Access Management", "value": "BC-IAM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-IAM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Single Sign-On", "value": "BC-IAM-SSO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-IAM-SSO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "<PERSON><PERSON>", "value": "BC-IAM-SSO-SL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-IAM-SSO-SL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1643878 - Release Notes for SNC Client Encryption"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><strong>SNC Client Encrytion 1.0&#160;is out of maintenance&#160;since 31. December 2017. SNC Client Encryption 2.0&#160;is now available. Please&#160;implement the newer version and&#160;refer to the release note&#160;<a target=\"_blank\" href=\" https://launchpad.support.sap.com/#/notes/2425150/E\">2425150</a>&#160;and the centrale note <a target=\"_blank\" href=\"/notes/2440692/E\">2440692</a>&#160;for more information. </strong></p>\r\n<p>You are using the components SNC Client Encryption and Secure Login Library or CommonCryptoLib.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SNC Client Encryption, Secure Login Library, SAP GUI , CommonCryptoLib.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Release Notes for SNC Client Encryption.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>SNC Client Encryption and Secure Login Library is a client/server software system integrated with SAP software to facilitate secure communication between SAP GUI and the SAP NetWeaver AS ABAP.<br /><br />SNC Client Encryption is a client application add-on for SAP GUI. It is based on Microsoft Kerberos technology and does not offer Single Sign-On nor supports certificate based authentication. This enhanced functionality is part of the product <strong>SAP Single Sign-On</strong>.<br /><br />Secure Login Library provides the cryptographic capabilities required for SNC on the SAP NetWeaver AS ABAP system.<br /><br />As a precondition for the solution, the user needs to be authenticated in the Windows Domain.</p>\r\n<p><strong>RECOMMENDATIONS</strong></p>\r\n<p>Instead of installing the stand-alone Secure Login Library, it is recommended to use <strong>CommonCryptoLib</strong>, which comes with latest Kernel updates. It is also recommended to get the current version of SAP GUI for Windows&#160;(7.40 or higher), which includes the latest SNC Client Encryption component, instead of installing the component separately.</p>\r\n<p>This way, no additional software needs to be downloaded and installed.</p>\r\n<p><br /><br /><strong>Important General Remarks</strong><br />SNC Client Encryption provides secure communication between SAP GUI and AS ABAP systems.&#160;This solution does not offer Single Sign-On functionalities.&#160;For a full flavored and deeply integrated Single Sign-On solution please refer to the <strong>SAP Single Sign-On</strong> product.<br /><br />If you encounter any issues which are not documented, or you want to report an error, use http://support.sap.com and open a customer message on component BC-IAM-SSO-SL.<br /><br /><br /><strong>Online community</strong><br />The SAP Developer Network (SDN) is an online community for developers, analysts, consultants, integrators, and administrators that provide a collection of technical content on SNC Client Encryption topics. https://www.sdn.sap.com/irj/sdn/security<br /><br /><strong>Documentation</strong><br />The documentation is available on the SAP Help Portal under SAP NetWeaver 7.3 Including Enhancement Package 1 -&gt; Application Help -&gt; Function-Oriented View -&gt; Security -&gt; Network and Transport Layer Security -&gt; Transport Layer Security on the AS ABAP -&gt; Using SNC Client Encryption for Password Logon (http://help.sap.com/saphelp_nw73ehp1/helpdata/en/b9/0dfa4a0457487bb0e59d304eb1a79a/frameset.htm).<br /><br /><br /><span style=\"text-decoration: underline;\">This solution has dependencies to the following SAP Notes</span><br />SAP Note 1561161 Enabling SAP GUI password logon despite using SNC<br />SAP Note 1580808 SAP Logon 7.20: \"SNC logon w/o SSO\" for connection entry<br />SAP Note 1616598 Enabling RFC password logon despite using SNC<br />SAP Note 1617641 Addition of SSO feature for SNC in Logon Control<br /><br /><br /><span style=\"text-decoration: underline;\">The software is available from the Software Download Center</span><br />https://launchpad.support.sap.com/#/softwarecenter&#160;&gt; Support Packages &amp; Patches &gt; <span style=\"white-space: nowrap; word-spacing: 0px; text-transform: none; float: none; color: #333333; text-align: left; font: 16px Arial, Helvetica, sans-serif; widows: 1; display: inline !important; letter-spacing: normal; background-color: #ffffff; text-indent: 0px; -webkit-text-stroke-width: 0px;\">By Category </span><span style=\"white-space: nowrap; word-spacing: 0px; text-transform: none; float: none; color: #333333; text-align: left; font: 16px Arial, Helvetica, sans-serif; widows: 1; display: inline !important; letter-spacing: normal; background-color: #ffffff; text-indent: 0px; -webkit-text-stroke-width: 0px;\">&gt; </span>SAP Cryptographic Software &gt; SNC Client Encryption &gt; SNC Client Encryption 1.0<br /><br /><strong>Supported SAP GUI Version</strong><br />SAP GUI for Windows 7.40 and higher<br /><br /><strong>Supported Client Platform</strong><br />Microsoft Windows Vista 64bit<br />Microsoft Windows Vista 32bit<br />Microsoft Windows 7 64bit<br />Microsoft Windows 7 32bit<br />Microsoft Windows 8.1 64bit<br />Microsoft Windows 8.1 32bit<br />Microsoft Windows 10 64bit<br />Microsoft Windows 10 32bit<br />Microsoft Windows Server 2008 x64 64bit<br />Microsoft Windows Server 2008 R2 x64 64bit<br />Microsoft Windows Server 2012 x64 64bit<br />Microsoft Windows Server 2012 R2 x64 64bit<br /><br />Citrix XenApp 5 on Microsoft Windows Server 2008 R2 x64 64bit<br />Citrix XenApp 5 on Microsoft Windows Server 2008 x64 64bit<br />Citrix XenApp 6 on Microsoft Windows Server 2008 R2 x64 64bit<br />Citrix XenApp 6 on Microsoft Windows Server 2008 x64 64bit<br />Citrix XenApp 6 on Microsoft Windows Server 2012 x64 64bit<br />Citrix XenApp&#160;7 on Microsoft Windows Server 2008 R2 Service Pack 1&#160;x64 64bit<br />Citrix XenApp&#160;7 on Microsoft Windows Server 2012 R2 x64 64bit</p>\r\n<p>Windows Terminal Server Microsoft Windows Server 2008 R2 x64 64bit<br />Windows Terminal Server Microsoft Windows Server 2008 x64 64bit<br />Windows Terminal Server Microsoft Windows Server 2012 x64 64bit<br />Windows Terminal Server Microsoft Windows Server 2012 R2 x64 64bit</p>\r\n<p><br /><strong>Supported OS Platform for SAP AS ABAP System</strong><br />AIX 5.2 64Bit<br />AIX 5.3 64Bit<br />AIX 6.1 64Bit<br />AIX 7.1 64Bit<br />HP-UX 11.11 on PA-RISC 64Bit<br />HP-UX 11.23 on PA-RISC 64Bit<br />HP-UX 11.31 on PA-RISC 64Bit<br />HP-UX 11.23 on IA64 64Bit<br />HP-UX 11.31 on IA64 64Bit<br />LINUX SUSE SLES 9 on IA64 64Bit<br />LINUX SUSE SLES 10 on IA64 64Bit<br />LINUX SUSE SLES 11 on IA64 64Bit<br />LINUX SUSE SLES 9 on x86_64 64Bit<br />LINUX SUSE SLES 10 on x86_64 64Bit<br />LINUX SUSE SLES 11 on x86_64 64Bit<br />LINUX SUSE SLES 9 on Power 64Bit<br />LINUX SUSE SLES 10 on Power 64Bit<br />LINUX SUSE SLES 11 on Power 64Bit<br />LINUX Red Hat EL 4 on IA64 64Bit<br />LINUX Red Hat EL 5 on IA64 64Bit<br />LINUX Red Hat EL 6 on IA64 64Bit<br />LINUX Red Hat EL 4 on x86_64 64Bit<br />LINUX Red Hat EL 5 on x86_64 64Bit<br />LINUX Red Hat EL 6 on x86_64 64Bit<br />LINUX Red Hat EL 4 on Power 64Bit<br />LINUX Red Hat EL 5 on Power 64Bit<br />LINUX Red Hat EL 6 on Power 64Bit<br />Microsoft Windows Server 2008 on IA64 64bit<br />Microsoft Windows Server 2008 R2 on IA64 64bit<br />Microsoft Windows Server 2008 on x64 64bit<br />Microsoft Windows Server 2008 R2 on x64 64bit<br />Microsoft Windows Server 2012 on IA64 64bit<br />Microsoft Windows Server 2012 on x64 64bit<br />Microsoft Windows Server 2012 R2 on x64 64bit<br />OSF1 / TRUE64 5.1 Alpha 64Bit<br />Solaris 9 SPARC 64bit<br />Solaris 10 SPARC 64bit<br />Solaris 10 on x64 64bit</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-SEC (Security - Read KBA 2985997 for subcomponents)"}, {"Key": "Other Components", "Value": "BC-IAM-SSO-CCL (CommonCryptoLib)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D055986)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D055984)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001643878/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001643878/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001643878/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001643878/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001643878/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001643878/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001643878/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001643878/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001643878/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2440692", "RefComponent": "BC-IAM-SSO-CCL", "RefTitle": "Central Note for SNC Client Encryption 2.0", "RefUrl": "/notes/2440692"}, {"RefNumber": "2425150", "RefComponent": "BC-IAM-SSO-CCL", "RefTitle": "Release Note SNC Client Encryption 2.0", "RefUrl": "/notes/2425150"}, {"RefNumber": "1701870", "RefComponent": "BC-MID-RFC", "RefTitle": "RFC client communication supporting SNC without SSO", "RefUrl": "/notes/1701870"}, {"RefNumber": "1690662", "RefComponent": "BC-SEC-SNC", "RefTitle": "Option: Blocking unencrypted SAPGUI/RFC connections", "RefUrl": "/notes/1690662"}, {"RefNumber": "1684886", "RefComponent": "BC-IAM-SL", "RefTitle": "License conditions of SNC Client Encryption", "RefUrl": "/notes/1684886"}, {"RefNumber": "1673155", "RefComponent": "BC-IAM-SL", "RefTitle": "Prerequisites for analyzing support messages on SAP NW SSO", "RefUrl": "/notes/1673155"}, {"RefNumber": "1643879", "RefComponent": "BC-SEC", "RefTitle": "Potential information disclosure relating to passwords", "RefUrl": "/notes/1643879"}, {"RefNumber": "1617641", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1617641"}, {"RefNumber": "1616598", "RefComponent": "BC-SEC-LGN", "RefTitle": "Enabling RFC password logon despite SNC", "RefUrl": "/notes/1616598"}, {"RefNumber": "1580808", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP Logon 7.20: \"SNC logon w/o SSO\" for connection entry", "RefUrl": "/notes/1580808"}, {"RefNumber": "1561161", "RefComponent": "BC-SEC-LGN", "RefTitle": "Enabling SAP GUI password logon despite using SNC", "RefUrl": "/notes/1561161"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2057374", "RefComponent": "BC-IAM-SSO-SL", "RefTitle": "Securing SAP GUI connections with SNC Client Encryption", "RefUrl": "/notes/2057374 "}, {"RefNumber": "2202375", "RefComponent": "BC-IAM-SL", "RefTitle": "Fixes for SNC Client Encryption 1.0 SP01 PL05", "RefUrl": "/notes/2202375 "}, {"RefNumber": "1701870", "RefComponent": "BC-MID-RFC", "RefTitle": "RFC client communication supporting SNC without SSO", "RefUrl": "/notes/1701870 "}, {"RefNumber": "1690662", "RefComponent": "BC-SEC-SNC", "RefTitle": "Option: Blocking unencrypted SAPGUI/RFC connections", "RefUrl": "/notes/1690662 "}, {"RefNumber": "1673155", "RefComponent": "BC-IAM-SL", "RefTitle": "Prerequisites for analyzing support messages on SAP NW SSO", "RefUrl": "/notes/1673155 "}, {"RefNumber": "1684886", "RefComponent": "BC-IAM-SL", "RefTitle": "License conditions of SNC Client Encryption", "RefUrl": "/notes/1684886 "}, {"RefNumber": "1616598", "RefComponent": "BC-SEC-LGN", "RefTitle": "Enabling RFC password logon despite SNC", "RefUrl": "/notes/1616598 "}, {"RefNumber": "1561161", "RefComponent": "BC-SEC-LGN", "RefTitle": "Enabling SAP GUI password logon despite using SNC", "RefUrl": "/notes/1561161 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SNCCLNTCRYPT", "From": "1.0", "To": "1.0", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}