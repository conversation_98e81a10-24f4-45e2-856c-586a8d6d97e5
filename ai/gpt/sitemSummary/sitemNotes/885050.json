{"Request": {"Number": "885050", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 378, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005062262017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000885050?language=E&token=68DE4B9E7DF2797C838DD65BA4D5D09D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000885050", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000885050/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "885050"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 13}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "13.12.2005"}, "SAPComponentKey": {"_label": "Component", "value": "CRM-PCF"}, "SAPComponentKeyText": {"_label": "Component", "value": "obsolete: People Centric UI Framework"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Customer Relationship Management", "value": "CRM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "obsolete: People Centric UI Framework", "value": "CRM-PCF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM-PCF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "885050 - Correction CRM BSP Framework, SP06 (01)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Correction phase SP06 for CRM BSP Framework.<br />Symptom 1: Advanced Search with MultiValue expressions are ignored.<br />Symptom 2: Automatic Column Sorting is not performed when column contains EEW related data.<br />Symptom 3: The tool tip of a link of 'URL Type' 'Mail Address' just showed the field value. This has been changed; the tool tip now is like 'Send Email to &lt;field value&gt;'.<br />Symptom 4: If the view ME_DETAIL_M.HTM has been used it could happen that list paging didn't work if the page number has been entered manually.<br />Symptom 5: If there is a data binding error due to illegal data entry during creation, after the error popup is ignored, all fields are disabled.<br />Symptom 6: The ME Detail controller now asks the MAC before deleting an object, just like the other sub controller do. (CSN 471543/2005)<br />Symptom 7: The ME Detail controller should no longer return \"null\" for the global event from method general event. (CSN 0002296900 2005)<br />Sympotm 8: Worked over a few performance relevant issues again. They are described in note 875518.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Correction phase SP06 for CRM BSP Framework.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Correction phase SP06 for CRM BSP Framework.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Implement the specified correction instructions.<br /><br /><br />Start of Manual Step A - Create a text symbol in class CL_CRM_BSP_RESULTLIST:<br />1) Goto SE24, open class CL_CRM_BSP_RESULTLIST in change mode.<br />2) In the menu select Goto -&gt; Text elements.<br />3) Maintain a new entry under Text symbols in accordance with the following instructions: Enter value '009' under 'Sym'; the literal 'Send Email to' under 'Text'; and value '30' under 'mLen'.<br />4) Save and activate.<br />End of Manual Step A.<br />Start of Manual Step B - Create a text symbol in class CL_CRM_BSP_STRUCTUREEDIT:<br />1) Goto SE24, open class CL_CRM_BSP_STRUCTUREEDIT in change mode.<br />2) In the menu select Goto -&gt; Text elements.<br />3) Maintain a new entry under Text symbols in accordance with the following instructions: Enter value '005' under 'Sym'; the literal 'Send Email to' under 'Text'; and value '30' under 'mLen'.<br />4) Save and activate.<br />End of Manual Step B.<br />Start of Manual Step C - Create a text symbol in class CL_CRM_BSP_TREETABLEVIEW:<br />1) Goto SE24, open class CL_CRM_BSP_TREETABLEVIEW in change mode.<br />2) In the menu select Goto -&gt; Text elements.<br />3) Maintain a new entry under Text symbols in accordance with the following instructions: Enter value '003' under 'Sym'; the literal 'Send Email to' under 'Text'; and value '30' under 'mLen'.<br />4) Save and activate.<br />End of Manual Step C.<br /><br />Start of Manual Step D - Modify the main.js javascript file<br /><br />1) Goto SE80, open BSP application CRM_BSP_FRAME<br />2) Open the node 'MIMEs' and then 'Scripting'<br />3) Right Click on 'main.js', choose 'Upload/Download' and then<br />&#x00A0;&#x00A0; 'Download with lock'.<br />4) Save this javascript file in the local directory.<br />5) Open the file and do the following corrections...<br />6) Goto the javascript function : function htmlbCRMsubmit(...)<br />7) Identify the code block inside that function:<br />&#x00A0;&#x00A0; if (ReqField.check()) {<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; answer = _crmBspHtmlbSubmitOriginal(elem,eventType,formID,objectID,eventName,paramCount,&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;param1,param2,param3,param4,param5,param6,param7,param8,param9);<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;} else {<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;answer = false;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;}<br /><br />8) In the 'else' part, add the following statement :<br />&#x00A0;&#x00A0; showProtectDoubleSubmitWindow(false);<br /><br />&#x00A0;&#x00A0; After the change, the code block should look like:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;if (ReqField.check()) {<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;answer = _crmBspHtmlbSubmitOriginal(elem,eventType,formID,objectID,eventName,paramCount,&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;param1,param2,param3,param4,param5,param6,param7,param8,param9);<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;} else {<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;answer = false;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;showProtectDoubleSubmitWindow(false);<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;}<br />9) Save the file.<br />10) Again, perform steps 1 and 2 and then, right click on 'main.js' and<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;choose 'Upload/Download' and then 'Upload and Replace'. Now, choose<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;the file you modified from the local directory.<br />11) The main.js is cached and hence, the server cache has to be invalidated : Goto transaction SMICM, choose Menu option 'Goto' and then 'HTTP Server Cache' and then 'Invalidate' and then 'Global in System'.<br /><br />End of Manual Step D.<br />Start of Manual Step E.<br />1. Start transaction SE11.<br />2. Specify Database table CRMC_LAYOUT.<br />3. Click on Display<br />4. Click on button \"Technical Settings\"<br />5. Switch to Change mode.<br />6. Change the number of keys fields (No. of key fields) to 2.<br />7. Save and activate.<br />8. Repeat steps 1 to 7 for table CRMC_LAYOUTC<br />End of Manual Step E.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CA-GTF-PCF (People Centric UI Framework)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023949)"}, {"Key": "Processor                                                                                           ", "Value": "I027070"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000885050/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000885050/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000885050/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000885050/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000885050/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000885050/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000885050/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000885050/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000885050/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "949447", "RefComponent": "CRM-BF", "RefTitle": "mySAP CRM 2005 SP Stack 05", "RefUrl": "/notes/949447"}, {"RefNumber": "886815", "RefComponent": "CRM-PCF", "RefTitle": "Popup losing focus after main page is reloaded", "RefUrl": "/notes/886815"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "949447", "RefComponent": "CRM-BF", "RefTitle": "mySAP CRM 2005 SP Stack 05", "RefUrl": "/notes/949447 "}, {"RefNumber": "886815", "RefComponent": "CRM-PCF", "RefTitle": "Popup losing focus after main page is reloaded", "RefUrl": "/notes/886815 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_ABA", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_ABA 700", "SupportPackage": "SAPKA70006", "URL": "/supportpackage/SAPKA70006"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_ABA", "NumberOfCorrin": 1, "URL": "/corrins/0000885050/44"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 13, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "868404 ", "URL": "/notes/868404 ", "Title": "Correction CRM BSP Framework, SP05", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "868693 ", "URL": "/notes/868693 ", "Title": "Correction CRM BSP Framework, SP05 (1)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "871213 ", "URL": "/notes/871213 ", "Title": "Correction CRM BSP Framework, SP05 (2)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "871918 ", "URL": "/notes/871918 ", "Title": "Correction CRM BSP Framework, SP05 (4)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "872178 ", "URL": "/notes/872178 ", "Title": "Correction CRM BSP Framework, SP05 (5)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "872453 ", "URL": "/notes/872453 ", "Title": "Correction CRM BSP Framework, SP05 (6)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "873034 ", "URL": "/notes/873034 ", "Title": "Correction CRM BSP Framework, SP05 (7)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "873662 ", "URL": "/notes/873662 ", "Title": "Correction CRM BSP Framework, SP05 (8)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "875518 ", "URL": "/notes/875518 ", "Title": "Correction CRM BSP Framework, SP05 (9)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "879650 ", "URL": "/notes/879650 ", "Title": "Correction CRM BSP Framework, SP05 (10)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "880178 ", "URL": "/notes/880178 ", "Title": "Basis-relevant PCUI changes", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "881385 ", "URL": "/notes/881385 ", "Title": "BSP-relevant changes in the PCUI (2)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "881541 ", "URL": "/notes/881541 ", "Title": "Correction CRM BSP Framework, SP05 (12)", "Component": "CRM-PCF"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}