{"Request": {"Number": "1681396", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 258, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017384812017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001681396?language=E&token=AAEAE55D64E4D32040F8124B71214616"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001681396", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001681396/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1681396"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 13}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.11.2023"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-OT"}, "SAPComponentKeyText": {"_label": "Component", "value": "OLAP Technology"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Explorer", "value": "BW-BEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "OLAP Technology", "value": "BW-BEX-OT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1681396 - Query Performance"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>A query needs more time to finish as expected. It might happen that the runtime is even longer than the time limit for a work process leading to the dump 'time out'.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>performance, web template, Analyzer, workbook,time-out, BICS, MDX, BWA, BIA, SQL, TIME_OUT, slow,&#160;cannot allocate enough memory,&#160;Memory allocation failed,</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This note should help you to carry out a perfomance analysis for a BW query from the OT (OLAP Technology) perspective. This can be done in the transaction RSRT which offers some very helpful features.<br />In case you have performance problems with specific frontend tools (BEx web templates, BEx Analyzer workbooks or reports created by SAP Business Objects tools such as Webi, Crystal, Dashboard or Analysis - all will be referred to as frontend end tools in this note) you first need to find out which queries are used and whether the embedded queries are slow as well.<br />If you need a very quick/condensed overview how to proceed, use the executive summary of point [I]. Further below you can find more eloborate instructions under points [II] and [III].</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>The query performance should initially be analyzed together with your consultant or application developer, please check all the points mentioned in the following guideline. In case the issue has to be processed by the BW Support then always include the results of your analysis like the 'Executive Summary' into the message and expect the support consultant to ask for this analysis.</strong></td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>See also the WIKI page <a target=\"_blank\" href=\"https://help.sap.com/docs/SUPPORT_CONTENT/bwplaolap/3361385757.html?locale=en-US\">Query Performance and Memory Consumption</a>,&#160;where also query&#160;memory issues are discussed.</p>\r\n<p>Best practise performance analysis has been summarized in the Guided answer (decision tree) linked in KBA: <a target=\"_blank\" href=\"https://me.sap.com/notes/2455142\">2455142</a></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><br /><strong>CONTENTS</strong><br /><br /><strong>[I]&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;EXECUTIVE SUMMARY <br /></strong><br /><strong>[II]&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;DETAILED ANALYSIS</strong><br /><strong>&#160;&#160; [II.A]&#160;&#160;&#160;&#160;Identify Query</strong><br /><strong>&#160;&#160; [II.B]&#160;&#160;&#160;&#160;Run Query in RSRT</strong><br /><strong>&#160;&#160; [II.C]&#160;&#160;&#160;&#160;Detailed Analysis in RSRT</strong><br /><strong>&#160;&#160; [II.D]&#160;&#160;&#160;&#160;Analysis of SQL Statements</strong><br /><strong>&#160;&#160; [II.E]&#160;&#160;&#160;&#160;BWA Access<br /></strong><br /><strong>[III]&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;OPTIMIZING QUERY PERFORMANCE</strong><br /><strong>&#160;&#160; [III.A]&#160;&#160; Query Design</strong><br /><strong>&#160;&#160; [III.B]&#160;&#160; Data Modeling</strong><br />&#160;&#160; <strong>[III.C]&#160;&#160;&#160;BWonHana</strong></p>\r\n<p>&#160;</p>\r\n<p><strong>[I] EXECUTIVE SUMMARY <br /></strong></p>\r\n<ul>\r\n<li><strong>Identify the Query&#160; </strong>(point [II.A] provides more details)</li>\r\n<ul>\r\n<li>narrow the frontend problem (BEx web templates, BEx Analyzer/AO workbooks or reports created by SAP Business Objects tools such as Webi, Crystal, Dashboard or Analysis) down to the execution of a single query in RSRT</li>\r\n<li>assure that the same filters and the same navigational state is used in RSRT as in the frontend tools!</li>\r\n<li>analyze how many data records have to be read from the infoproviders and are expected to be displayed for this query2.</li>\r\n</ul>\r\n<li><strong>Analysis in RSRT&#160; </strong>(point [II.B-C] provides more details)</li>\r\n<ul>\r\n<li>use the runtime statistics (execute&amp;debug) to locate the processing area causing the long runtime and the amount of data (DBTRANS) which has to be processed by the OLAP engine</li>\r\n<li>if DBTRANS (tab Aggregation Level)is empty then the OLAP cache was used. For testing purposes its possible to switch off this feature (Execute&amp;Debug-&gt;'do not use cache')</li>\r\n<li>check whether the number of transfered data records (field DBTRANS) is as expected. If not please review query definition regarding filters and exception aggregation ( point [III.A] )</li>\r\n<li>Event 9000: if the data base/BWA needs more time than expected please check(update) the data base indexes and statistics and assure that the data base is up-to-date, please proceed as illustrated in points [II.D/E]</li>\r\n<li>if most of the time isn't spent on the DB/BWA, the amount of data records transfered to OLAP and the complexity of the query is crucial - see point [III.A]. If both do not explain the long runtime please carry out a note search on the component BW-BEX-OT-OLAP* with the term 'performance'.</li>\r\n</ul>\r\n<li><strong>Check Query Definition </strong>(point [III.A] provides more details)</li>\r\n<ul>\r\n<li>in many cases there is no other way than reducing the amount of data and/or the complexity of the query definition. Always consider that the BEX query runtime was designed for on-screen readable reports. In transaction RSRT, the 'Technical Information' will already present yellow or red lights for features impactivng the performance. The following points may be relevant:</li>\r\n<ul>\r\n<li>too few filter restrictions, many infoobjects drilled down</li>\r\n<li>not visible drilldown characteristics as e.g. caused by exception aggregation, constant selection or currency and unit conversions</li>\r\n<li>access type 'Master Data', see <a target=\"_blank\" href=\"https://help.sap.com/docs/SUPPORT_CONTENT/bwplaolap/**********.html?locale=en-US\">'Access Type for Result Values'</a></li>\r\n<li>key figures of type Non-Cumulative, see note <a target=\"_blank\" href=\"https://me.sap.com/notes/1548125\">1548125</a></li>\r\n<li>big/many Hierarchies (with time dependent structure) - see point [III.B]</li>\r\n</ul>\r\n</ul>\r\n<li><strong>General Performance Recommendations&#160; </strong>(point [III.B] provides more details)</li>\r\n<ul>\r\n<li>keep things small and simple</li>\r\n<li>don't retrieve too much data from the providers ( point [III.A])</li>\r\n<li>keep the data base statistics and indexes up-to-date, check data base parameters - see point [II.D]</li>\r\n<li>use logical partitioning (semantic partitioning, pruning) - see point [III.B]</li>\r\n<li>use physical partitioning (regarding calmonth/fiscper of E table) - see point [III.B]</li>\r\n<li>use Aggregates or BWA and OLAP cache</li>\r\n<li>compress cubes - see note <a target=\"_blank\" href=\"https://me.sap.com/notes/590370\">590370</a></li>\r\n<li>in case BW&#160;runs on <strong>HANA</strong>, check whether OLAP features can be 'pushed down' to the database. See note <a target=\"_blank\" href=\"https://me.sap.com/notes/2400004\">2400004</a> and point [III.C]</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><strong>[II] DETAILED ANALYSIS</strong></p>\r\n<p><strong>[II.A] Identify Query</strong></p>\r\n<p>In order to track down the query please run all queries used in the frontend report seperately in RSRT and assure that the same filter selections are used as in the frontend !</p>\r\n<ul>\r\n<li>If the runtime problem only occurs for the template/workbook/AO report&#160;the following notes are helpful:</li>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"https://me.sap.com/notes/1783320\">1783320</a>&#160; BICS Performance: Cumulative Note</li>\r\n<li><a target=\"_blank\" href=\"https://me.sap.com/notes/1913710\">1913710</a>&#160; BI Java Memory Improvements for NW 7.30</li>\r\n<li><a target=\"_blank\" href=\"https://me.sap.com/notes/1931691\">1931691</a>&#160; Performance hints for Design Studio/Lumira applications</li>\r\n<li>AO: <a target=\"_blank\" href=\"https://help.sap.com/docs/SUPPORT_CONTENT/sapao/3361383855.html?locale=en-US\">SCN Wiki Performance in AO</a></li>\r\n<li>Bex Analyzer:&#160;<a target=\"_blank\" href=\"https://help.sap.com/docs/SUPPORT_CONTENT/bwfe/3361385802.html?locale=en-US\">SCN Wiki Optimizing BEx Analyzer 7x Performance</a></li>\r\n</ul>\r\n<li>If the performance issue only occurs in a BO report, figure out what connection type is used to work with BW (MDX, BICS or RSDRI_DF).</li>\r\n<li>If the runtime problem occurs for a query using the MDX interface, work with note <a target=\"_blank\" href=\"https://me.sap.com/notes/1381821\">1381821</a> 'Memory and Performance Analysis for MDX'</li>\r\n<li>In case its necessary to analyze a performance problem in a support message please attach the&#160;Statistics workbook/web file and refer to the event which needs more time as expected.</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><strong>[II.B] Run Query in RSRT </strong></p>\r\n<p>After you have narrowed the problem down to the execution of a single query in <a target=\"_blank\" href=\"https://help.sap.com/docs/SUPPORT_CONTENT/bwplaolap/3361383837.html?locale=en-US\">RSRT</a>, there are still many influencing factors. Roughly we can devide them into two catagories which refer to the two main OT processing blocks during the query runtime:</p>\r\n<p><strong>OLAP Processor</strong> (or Analytic Engine) is the layer responsible for receiving and processing frontend requests. Depending on the navigation, the frontend sends information what characteristics are in the drilldown (rows and columns), what values have been filled into variables and what dynamic filters might have been set. This information is summarized (and merged with the fixed filters) into an OLAP request and handed over to the Data Manager. The Data Manager then retrieves the data from the respective InfoProvider, and hands it back to the OLAP Processor which then performs the calculations, currency translations etc defined in the query. See also note <a target=\"_blank\" href=\"https://me.sap.com/notes/1591837\">1591837</a> where some more information is provided regarding the processing sequence</p>\r\n<p><strong>Data Manager</strong> (DM) is the layer responsible for translating the OLAP request for data retrieval from the different InfoProvider-types (e.g. Basis Cube, MultiProviders or Virtual Cubes). It also creates the necessary SQL statements which are finally processed by the data base</p>\r\n<p>The first step is to find out in which area of query processing most of the time is spent. The most convenient way to do this is running the query in RSRT and having the Query Runtime Statistics displayed afterwards (see point [II.C]) It might also happen that the query virtually runs 'forever' and hence doesn't give you the possibility to check the runtime statistics. In such a case please monitor the corresponding process in SM50 and try to figure out where the query 'hangs':</p>\r\n<ul>\r\n<li>if you can see a long running SQL then please proceed with point [II.D]</li>\r\n<li>if the query processes ABAP coding its more difficult to locate the issue. You could start the debugger (SM50-&gt;menu-&gt;Program/Session-&gt;Program-&gt;Debugger) and take a look at the call stack. The processing blocks where most of the time is spent, can hopefully give you an idea of the area the performance issue is located. The name of the ABAP objects may also be used for a note search.</li>\r\n</ul>\r\n<p><strong><br /><strong>[II.C] Detailed Analysis in RSRT</strong><br /></strong></p>\r\n<p>Please review note <a target=\"_blank\" href=\"https://me.sap.com/notes/1591837\">1591837</a> where you can find more information about this important transaction RSRT (Query Monitor). As already mentioned, try first to get the runtime statistics displayed by clicking on the button 'Execute+Debug' and choosing the setting 'Display Statistics Data'. When you use this function you need to press 'Back(F3)' afterwards in order to get the statistcs displayed. In this note we don't explain all the Statistics Events but only focus on the most important ones. See <a target=\"_blank\" href=\"https://help.sap.com/docs/SUPPORT_CONTENT/bwplaolap/3361385757.html?locale=en-US\">Query Performance and Memory Consumption</a>&#160;for further details.<br /><br /><strong>Runtime Statistics</strong><br /><br />There are two tabs, the Frontend/Calculation Layer and the Aggreagtion Layer (and the tab BWA Access in case an BWA index was read). Pleas see note <a target=\"_blank\" href=\"https://me.sap.com/notes/1707239\">1707239</a> for a detailed description how to use this feature.<br /><br /><strong>Calculation Layer</strong>: In the column 'Duration' the runtime for each Event is given in seconds. Watch out for the ones with the highest numbers. The most important BEX OLAP Statistics Events are (see also&#160;<a target=\"_blank\" class=\"external-link\" href=\"http://help.sap.com/saphelp_nw73/helpdata/en/43/e39fd25ff502d2e10000000a1553f7/content.htm?frameset=/en/43/e3807a6df402d3e10000000a1553f7/frameset.htm&amp;current_toc=/en/89/71b01ce1f44e95a860a6c3f7dda911/plain.htm&amp;node_id=72&amp;show_children=false\" rel=\"nofollow\">SAP Online Documentation</a>):</p>\r\n<ul>\r\n<li><strong>3010 OLAP: Query Gen.</strong>: This event measures the time that is needed to check the query definition and, if necessary, to generate the query</li>\r\n<li><strong>4500 Authorization Check Selection</strong>: This event measures the time taken to carry out the OLAP authorization check.</li>\r\n<li><strong>9000 Data Manager</strong>: This event measures the time in the data manager, you can find more detailed data under the tab Aggregation Layer.</li>\r\n<li><strong>9010 Total DBTRANS</strong>: Total Number of Transported Records</li>\r\n<li><strong>9011 Total DBSEL</strong>:&#160;&#160; Total Number of Read Records</li>\r\n<li><strong>3110 OLAP: Data Selection</strong>(OLAP_BACK):This event measures e.g. the time taken to sort the read data (from DM) according to structure element selections or restricted key figures. The calculation of virtual key figures is done here as well.</li>\r\n<li><strong>3200 OLAP: Data Transfer</strong>(RRK_DATA_GET):in this part of the coding many OLAP features are processed, e.g. exception aggregations are carried out, formulas are calculated, and the correct number of decimal places for the data cells is determined.</li>\r\n<li><strong>3900 OLAP: Read Texts</strong>: Texts are read to sort the data. This event measures the time taken to call the text read class</li>\r\n<li><strong>3130 OLAP: BADI COMPUTE / OLAP:USER_EXIT:</strong> This event measures the time needed in the exit for virtual characteristics and key figures, see note <a target=\"_blank\" href=\"https://me.sap.com/notes/1717880\">1717880</a> for further details.</li>\r\n</ul>\r\n<p><br /><strong>Aggregation Layer: </strong>It gives a list of all SQL/BIA accesses which were executed. In the column 'Viewed at' you can see how long it took, the other interesting columns are:</p>\r\n<ul>\r\n<li>Records, Selected</li>\r\n<li>Records, Transported</li>\r\n<li>Basic Infoprovider</li>\r\n<li>Aggregate</li>\r\n<li>Table Type</li>\r\n</ul>\r\n<p>In case the Datamanger is responsible for the long runtime this list allows you to break down the problem to single SQL/BIA statements. The columns 'Records, Selected' and 'Records, Transported' give an idea of which anmount of data had to be processed. Please see point [II.D] for further details. The field 'Aggregate' tells you which aggregate was used, if you see infoprov$X the <strong>BWA</strong> was used.<br /><br />In case one of the other processing blocks is responsible for the perfomance issue the following general hints should be helpful:</p>\r\n<ul>\r\n<li>check the number of data records (DBTRANS) which were handed over to the OLAP Processor. A high number (e.g. more than 1 million records) has an impact on the events 3110, 3200, 3900.and can lead to a long processing time. Please see point [III.A].</li>\r\n<li>some of the Statistical Events like the Authorization Check point directly to a application component which you can use for a note search (e.g.use the key word performance on BW-BEX-OT-OLAP-AUT). If the application is not clear, just use BW-BEX-OT-OLAP*, the term 'performance' and specify the implementd support package.</li>\r\n<li>an important point is the usage of the <strong>OLAP Cache</strong>. In case the cache is used the query does not call the Data Manager and hence the corresponding statistics event should be empty. If its unclear why the OLAP cache was not taken please review note 822302.</li>\r\n<li>There is the button 'Performance Info' in RSRT which you can use in order to get a list of many performance relevant factors displayed. Thats a convenient and fast way to check many points in one go.</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><strong>[II.D] Analysis of SQL Statements</strong></p>\r\n<p>In this chapter its discussed how SQL statements can be analyzed. As a reference data base we use ORACLE, but most of the points can directly be applied to other databases as well. In some cases the procedures are not exactly the same but similar. Lets start with a to-do-list which often already helps to solve the issue (see also note <a target=\"_blank\" href=\"https://me.sap.com/notes/1705026\">1705026</a>):</p>\r\n<ul>\r\n<li>ensure that the database software is up-do-date, see notes <a target=\"_blank\" href=\"https://me.sap.com/notes/1696147\">1966147</a> and&#160;<a target=\"_blank\" href=\"https://me.sap.com/notes/1175996\">1175996</a></li>\r\n<li>check the database parameters - see note <a target=\"_blank\" href=\"https://me.sap.com/notes/1171650\">1171650</a></li>\r\n<li>ensure that the CBO statistics are current and correct - e.g.run the RSRV check 'Database&#160;Statistics of an InfoCube and its Aggregates' (folder 'Database')&#160;&#160;and assure that the table DBSTATC does not contain BW tables with ACTIV=I (note <a target=\"_blank\" href=\"https://me.sap.com/notes/129252\">129252</a> - report SAP_DBSTATC_CLEANUP)</li>\r\n<li>ensure that all indexes are present and usable - run corresponding RSRV checks</li>\r\n<li>ensure that the f-fact table doesn't have too many partitions - see note <a target=\"_blank\" href=\"https://me.sap.com/notes/590370\">590370</a></li>\r\n</ul>\r\n<p>In the transaction RSRT there is the possibility to have the SQL statement and the Execution Plan displayed (execute&amp;debug' -&gt; 'Dispaly SQL/BIA Query'&amp;'Display Run Schedule'; in addition use the function 'no parallel processing'). Even when you are not familiar with the language SQL you can carry out some important checks there.</p>\r\n<ul>\r\n<li>E.g. in the 'Where Clause' you can check whether the filter restrictions are as expected.</li>\r\n<li>The Execution Plan offers a very helpful feature: please click on the tables in order to get more information to these objects displayed. One important field is called 'last statistics update'. In case the statistics are not up-to-date you can start the update directly from this dialog by clicking on the button 'Analyze' Please check this field for all tables used in the SQL.and then run the query again.(you can also use the refresh button and check whether the Execution Plan has changed).</li>\r\n<li>In case of an SQL statement accessing fact tables of an infocube it might also make sense to test whether the usage of the star transformation (see also note 1247763) makes a difference. Please run the query in RSRT and activate the flag at 'Deactivate DB Optimizer Functions' (then the star transformation isn't used by ORACLE)&#160;&#160;If the execution time of the SQL statement turns out to be less, please proceed as above and search for propper ORACLE notes.</li>\r\n</ul>\r\n<p>The following note provides more details to the star transformation and other performance relevant points: Note <a target=\"_blank\" href=\"https://me.sap.com/notes/1013912\">1013912</a> FAQ: Oracle BW performance</p>\r\n<p>In case BW&#160;runs on Hana database, see point [III.C].</p>\r\n<p><strong><br />[II.E] BWA Access</strong></p>\r\n<p>In case a BWA access takes longer than expected please review note <a target=\"_blank\" href=\"https://me.sap.com/notes/1318214\">1318214</a>. There you can find a long list of points which should be checked in order to optimize the access of the BWA indexes. Please also note that the BWA is not designed for transferring mass data to the BW system. So please check the field DBTRANS in order to get the relevant information regarding data volumn. Note <a target=\"_blank\" href=\"https://me.sap.com/notes/1018798\">1018798</a> provides more information to this topic<br /><br /></p>\r\n<p><strong><br />[III] OPTIMIZING QUERY PERFORMANCE</strong></p>\r\n<p>In the follwing we would like to address various factors which have to be taken into account when doing a performance analysis.</p>\r\n<p><strong>[III.A] Query Design</strong></p>\r\n<p>A large data volume (transfered from the data manager to OLAP) is often responsible for a non-satisfying query performance. As mentioned in [II.C] please activate the OLAP statistics and take a look at the field DBTRANS. The following points normally contribute to a high number of transfered data records.</p>\r\n<ul>\r\n<li>many infoobjects drilled down</li>\r\n<li>not visible drilldown characteristics (see also note <a target=\"_blank\" href=\"https://me.sap.com/notes/1591837\">1591837</a>) - e.g. due do the usage of exception aggregation</li>\r\n<li>too few filter restrictions</li>\r\n<li>usage of 'calculation before aggregation' - see notes&#160;<a target=\"_blank\" href=\"https://me.sap.com/notes/1151957\">1151957</a> and <a target=\"_blank\" href=\"https://me.sap.com/notes/1882656\">1882656</a></li>\r\n<li>if non cumulative key figures are involved please see note <a target=\"_blank\" href=\"https://me.sap.com/notes/1548125\">1548125</a> point [III.B]</li>\r\n</ul>\r\n<p>Sometimes a query spends most of the time in the event <strong>3200 OLAP: Data Transfer. </strong>In this part of the coding the OLAP Engine (among others) calculates the formulas and carries out exception aggregation (note <a target=\"_blank\" href=\"https://me.sap.com/notes/1151957\">1151957</a>). In transaction RSRT (button 'Technical Information') you can e.g. check how many single calculation steps ('formula components') have to be carried out for one data record (see also note <a target=\"_blank\" href=\"https://me.sap.com/notes/1475193\">1475193</a>). Please try to reduce the complexity of the query in case of performance problems in this area. In addition: it is recommended to search for performance optimization notes for OLAP.</p>\r\n<p>In the QueryDesigner there is the possibilty to activate the <strong>access type 'Master Data'</strong> (<a target=\"_blank\" href=\"https://help.sap.com/docs/SUPPORT_CONTENT/bwplaolap/**********.html?locale=en-US\">'Access Type for Result Values'</a>) for an infoobject. Then the query displays not only the values posted in the infoprovider but all from the corresponding master data table. This can lead to large data volumes and high values for the event '3200 OLAP: Data Transfer'. It's recommended to use this feature only if really necessary.</p>\r\n<p>Please also check whether the feature <strong><a target=\"_blank\" href=\"https://help.sap.com/docs/SUPPORT_CONTENT/bwplaolap/**********.html?locale=en-US\">'zero suppresssion'</a></strong> (or a condition) is active and whether it removes many records from the result set (this may lead to the false impression that only a few records were processed by the OLAP engine)</p>\r\n<p><strong><br />[III.B] Data Modeling</strong></p>\r\n<p><strong>Dimensions</strong>: In general its better to have more small dimensions as only view very big one. As a rule of thumb a dimension table shouldn't be larger than 10% of the fact tables. The report SAP_INFOCUBE_DESIGNS gives a good overview over all cube tables on the system.<br /><br /><strong>Partitioning</strong> (E table): partitioning is used to split the total dataset (fact table)into several, smaller, physically independent and redundancy-free units. This separation improves performance when you analyze data and only view partitions have to be read (e.g. you have defined partions using values of the infoobject 0calmonth (only 0calmoth and 0fisper are allowed ) and a certain query retrieves only data from the current month).<br /><br /><strong>LineItem Dimensions</strong>: If you have only one characteristic in a dimension then its possible to define it as a lineitem dimesion. Then the dimension is only a view on the corresponding SID table and hence no DIMIDs have to be generated during loading of transactional data. In the query the SID table can be directly joinded (one join less) with the fact table which leads in genral to a better performance.<br /><br /><strong>High Cardinality</strong>: This means that the dimension has a large number of data records. Different index types may be used depending on the data base. A general rule is that the number of dimension entries is at least 20% of the fact table entries. If you are unsure, do not select a dimension having high cardinality.<br /><br /><strong>MultiProvider(Semantic Partitioning, Pruning)</strong>:&#160;If a MultiProvider or HCPR&#160;consists of many InfoProviders, it is useful to use 'pruning' for this Provider. The query workload is reduced by checks to determine whether an PartProvider contains any data for the selection range of the query. PartProviders that are not relevant are then not queried at all. Please see the following notes:</p>\r\n<p><a target=\"_blank\" href=\"https://me.sap.com/notes/1832801\">1832801</a> Analyzing Queries defined on MultiProviders<br /><a target=\"_blank\" href=\"https://me.sap.com/notes/2228499\">2228499</a> Checklist for Pruning Issues</p>\r\n<p>As with BW73x you can use the new feature 'Semantic Partitioning' which divides the InfoProvider(InfoCubes or DataStore Objects)&#160;automatically into several smaller, equally sized units:<br /><a target=\"_blank\" href=\"http://help.sap.com/saphelp_nw73/helpdata/en/3f/559826fc0b4473942195298899653e/frameset.htm\">Creating a Semantically Partitioned Object<br /></a><br /><strong>Hierarchies: </strong>to achieve a good performance, hierarchiey structure has to fit into the shared buffer (Exp/Imp buffer) - see Note <a target=\"_blank\" href=\"https://me.sap.com/notes/156957\">156957</a><br />Note 738098: Provided no other settings are made, a technical node called \"unassigned nodes\" is automatically added to hierarchies. All characteristic values that are not used in the hierarchy are attached to this node. If the characteristic has a lot of values, but only a fraction of these are in the hierarchy, this node has a lot of leaves. This may result in memory problems and longer runtimes. It is possible to switch off this feature in RSH1 (see also <a target=\"_blank\" href=\"https://help.sap.com/docs/SUPPORT_CONTENT/bwplaolap/3361385637.html?locale=en-US\">Hierarchy Attribute 'Suppress Unassigned Node'</a>)</p>\r\n<p>See also <a target=\"_blank\" href=\"https://help.sap.com/docs/SUPPORT_CONTENT/bwplaolap/3361383125.html?locale=en-US\">Hierarchy Node Restriction resolved into Leaves</a></p>\r\n<p><strong>Navigation attributes</strong>: during query runtime the X,Y tables are joined with the corresponding dimension table; note that navigation attributes can be use in aggregates! Especially when the master data tables are very big please make sure that the data base statistcs are current.<br /><br /><strong>Inventory Cubes and non-cumulative key figures</strong>: non-cumulative key figures are virtual, meaning they are calculated during query runtime and usually more records are created then would actually be posted in the infoprovider for a certain selection. See note <a target=\"_blank\" href=\"https://me.sap.com/notes/1548125\">1548125</a> under point [V] for this very special feature.<br /><br /><strong>Compression: </strong>compress cubes as far as possible - see note <a target=\"_blank\" href=\"https://me.sap.com/notes/590370\">590370</a> for an explanation of the technical implications.<br /><br /><strong>DataStore Objects:</strong> In case the query is based on a DataStore Object (DSO) its recommended to switch on the DSO setting 'SID Generation upon Activation'. Otherwise the SID values are generated at query runtime which in general reduces the performance. In contrast to InfoCubes, the system does not automatically generate secondary indexes for DSO tables. This has to be done manually and is often required in order to avoid long running queries. Please see <a target=\"_blank\" href=\"http://help.sap.com/saphelp_nw73/helpdata/en/4a/547d8b6a8a1cd4e10000000a421937/frameset.htm\">Creating DataStore Objects</a>.<br /><br /><br /></p>\r\n<p><strong>[III.C] BW on HANA</strong></p>\r\n<p>If the database is HANA, OLAP features like e.g. exception aggregation and currency conversion can be 'pushed down' to the HANA database. This normally decreases the amount of data significantly which has to be transferred from the database to the OLAP Engine and hence may improve the performance of a query.<br />See note <a target=\"_blank\" href=\"https://me.sap.com/notes/2400004\">2400004</a>&#160;'Checking Pushdown of Exception Aggregations'</p>\r\n<p>Performance relevant Design/Modeling Recommendations:</p>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"https://me.sap.com/notes/2118286\">2118286</a>&#160;COPR/HCPR/ISET: High Query Memory Consumption</li>\r\n<li><a target=\"_blank\" href=\"https://me.sap.com/notes/2271658\">2271658</a> Design Considerations for Composite Provider</li>\r\n<li><a target=\"_blank\" href=\"https://me.sap.com/notes/2103032\">2103032</a> Long runtime for query on CompositeProvider</li>\r\n<li><a target=\"_blank\" href=\"https://me.sap.com/notes/2267702\">2267702</a> HCPR &amp; Navigation Attributes</li>\r\n<li><a target=\"_blank\" href=\"https://me.sap.com/notes/2185212\">2185212</a> ADSO: Recommendations and restrictions regarding reporting</li>\r\n</ul>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BEX-OT-OLAP (Analyzing Data)"}, {"Key": "Other Components", "Value": "BW-BEX-OT-DBIF (Interface to Database)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I022439)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I073957)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001681396/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001681396/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001681396/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001681396/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001681396/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001681396/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001681396/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001681396/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001681396/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2455142", "RefComponent": "BW-BEX-OT", "RefTitle": "HowTo: Bex query performance/memory analysis", "RefUrl": "/notes/2455142"}, {"RefNumber": "2400004", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Checking '<PERSON><PERSON>down' of OLAP features like Exception Aggregation", "RefUrl": "/notes/2400004"}, {"RefNumber": "2267702", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "HANA CompositeProvider & Navigation Attributes", "RefUrl": "/notes/2267702"}, {"RefNumber": "2228499", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Checklist for Pruning Issues", "RefUrl": "/notes/2228499"}, {"RefNumber": "2118286", "RefComponent": "BW-BEX-OT", "RefTitle": "HCPR: High Query Memory Consumption due to non-unique joins", "RefUrl": "/notes/2118286"}, {"RefNumber": "1913710", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BI Java Memory Improvements for NW 7.30 or higher", "RefUrl": "/notes/1913710"}, {"RefNumber": "1696147", "RefComponent": "BC-DB-ORA", "RefTitle": "Identify Oracle Patches or Bundle patches in SAP system", "RefUrl": "/notes/1696147"}, {"RefNumber": "948158", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Performance problems/measuring BI Java Web runtime", "RefUrl": "/notes/948158"}, {"RefNumber": "822302", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "OLAP CACHE for remote providers", "RefUrl": "/notes/822302"}, {"RefNumber": "728017", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Multiprovider processing - exclusion of part providers", "RefUrl": "/notes/728017"}, {"RefNumber": "590370", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Too many uncompressed request (f table partitions)", "RefUrl": "/notes/590370"}, {"RefNumber": "514907", "RefComponent": "BW-SYS-DB", "RefTitle": "Processing complex queries (data mart, and so on)", "RefUrl": "/notes/514907"}, {"RefNumber": "402469", "RefComponent": "BW-SYS-DB", "RefTitle": "Additional indexes on Master Data Tables", "RefUrl": "/notes/402469"}, {"RefNumber": "2271658", "RefComponent": "BW-BEX-OT-HCPR", "RefTitle": "Design Considerations for Composite Provider", "RefUrl": "/notes/2271658"}, {"RefNumber": "2185212", "RefComponent": "BW-WHM-DBA-ADSO", "RefTitle": "ADSO: Recommendations and restrictions regarding reporting", "RefUrl": "/notes/2185212"}, {"RefNumber": "2103032", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Long runtimes in query on CompositeProvider", "RefUrl": "/notes/2103032"}, {"RefNumber": "1931691", "RefComponent": "BI-RA-AD", "RefTitle": "Performance hints for Design Studio/Lumira applications", "RefUrl": "/notes/1931691"}, {"RefNumber": "1783320", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS Performance: Cumulative Note", "RefUrl": "/notes/1783320"}, {"RefNumber": "1717880", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Virtual Key Figures & Characteristics in BW Queries", "RefUrl": "/notes/1717880"}, {"RefNumber": "1591837", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "How to analyze query results", "RefUrl": "/notes/1591837"}, {"RefNumber": "1548125", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Interesting Facts about Non-Cumulatives", "RefUrl": "/notes/1548125"}, {"RefNumber": "1475193", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Warnings in transaction RSRT -> Technical Info", "RefUrl": "/notes/1475193"}, {"RefNumber": "1381821", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Memory and Performance Analysis for MDX", "RefUrl": "/notes/1381821"}, {"RefNumber": "1318214", "RefComponent": "BC-TRX-BIA", "RefTitle": "BWA: Suspicion of bad query performance", "RefUrl": "/notes/1318214"}, {"RefNumber": "129252", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Oracle DB Statistics for BW Tables", "RefUrl": "/notes/129252"}, {"RefNumber": "1247763", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "BW query: No more hints as of ORACLE 10.2.0.4", "RefUrl": "/notes/1247763"}, {"RefNumber": "1175996", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10g/11g patches check", "RefUrl": "/notes/1175996"}, {"RefNumber": "1171650", "RefComponent": "BC-DB-ORA", "RefTitle": "Automated Oracle DB parameter check", "RefUrl": "/notes/1171650"}, {"RefNumber": "1151957", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Interesting facts about the OLAP Processor/Analytic Engine", "RefUrl": "/notes/1151957"}, {"RefNumber": "1083462", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Statistics workbook for performance problems", "RefUrl": "/notes/1083462"}, {"RefNumber": "1025307", "RefComponent": "BW", "RefTitle": "Composite note for BW 7.00 / BW 7.01 performance: Reporting", "RefUrl": "/notes/1025307"}, {"RefNumber": "1018798", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Reading large data volumes from BI Accelerator", "RefUrl": "/notes/1018798"}, {"RefNumber": "1013912", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle BW performance", "RefUrl": "/notes/1013912"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2500671", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Transaction RSRT - General Information", "RefUrl": "/notes/2500671 "}, {"RefNumber": "2455142", "RefComponent": "BW-BEX-OT", "RefTitle": "HowTo: Bex query performance/memory analysis", "RefUrl": "/notes/2455142 "}, {"RefNumber": "1171650", "RefComponent": "BC-DB-ORA", "RefTitle": "Automated Oracle DB parameter check", "RefUrl": "/notes/1171650 "}, {"RefNumber": "1548125", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Interesting Facts about Non-Cumulatives", "RefUrl": "/notes/1548125 "}, {"RefNumber": "1013912", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle BW performance", "RefUrl": "/notes/1013912 "}, {"RefNumber": "1151957", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Interesting facts about the OLAP Processor/Analytic Engine", "RefUrl": "/notes/1151957 "}, {"RefNumber": "1591837", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "How to analyze query results", "RefUrl": "/notes/1591837 "}, {"RefNumber": "1018798", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Reading large data volumes from BI Accelerator", "RefUrl": "/notes/1018798 "}, {"RefNumber": "1247763", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "BW query: No more hints as of ORACLE 10.2.0.4", "RefUrl": "/notes/1247763 "}, {"RefNumber": "1175996", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10g/11g patches check", "RefUrl": "/notes/1175996 "}, {"RefNumber": "1381821", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Memory and Performance Analysis for MDX", "RefUrl": "/notes/1381821 "}, {"RefNumber": "590370", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Too many uncompressed request (f table partitions)", "RefUrl": "/notes/590370 "}, {"RefNumber": "1025307", "RefComponent": "BW", "RefTitle": "Composite note for BW 7.00 / BW 7.01 performance: Reporting", "RefUrl": "/notes/1025307 "}, {"RefNumber": "1475193", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Warnings in transaction RSRT -> Technical Info", "RefUrl": "/notes/1475193 "}, {"RefNumber": "1318214", "RefComponent": "BC-TRX-BIA", "RefTitle": "BWA: Suspicion of bad query performance", "RefUrl": "/notes/1318214 "}, {"RefNumber": "822302", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "OLAP CACHE for remote providers", "RefUrl": "/notes/822302 "}, {"RefNumber": "1083462", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Statistics workbook for performance problems", "RefUrl": "/notes/1083462 "}, {"RefNumber": "402469", "RefComponent": "BW-SYS-DB", "RefTitle": "Additional indexes on Master Data Tables", "RefUrl": "/notes/402469 "}, {"RefNumber": "948158", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Performance problems/measuring BI Java Web runtime", "RefUrl": "/notes/948158 "}, {"RefNumber": "728017", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Multiprovider processing - exclusion of part providers", "RefUrl": "/notes/728017 "}, {"RefNumber": "129252", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Oracle DB Statistics for BW Tables", "RefUrl": "/notes/129252 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}