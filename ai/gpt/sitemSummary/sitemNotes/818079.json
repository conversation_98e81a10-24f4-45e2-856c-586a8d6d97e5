{"Request": {"Number": "818079", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 606, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000004413492017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000818079?language=E&token=97F99AB1543CEA21344D8C3550B70240"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000818079", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000818079/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "818079"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 16}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "08.03.2022"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-PL-LO"}, "SAPComponentKeyText": {"_label": "Component", "value": "use FI-LOC-LO-PL"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Poland", "value": "XX-CSC-PL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-PL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "use FI-LOC-LO-PL", "value": "XX-CSC-PL-LO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-PL-LO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "818079 - PL: Down Payment Invoice for Poland"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The down payment process for Poland is currently not possible in the sales order -&gt; Delivery -&gt; Billing process.<br /><br />Legal requiements Poland: Decree of Minister of State Finances from April 27th 2004 regarding return of VAT tax to some payers, return of VAT tax in advance, rules for issuing and storing invoices and list of items not relevant for tax exemption.<br /><br />Polish law requirements:</p>\r\n<ul>\r\n<li>Generation of Down Payment Invoice</li>\r\n</ul>\r\n<ul>\r\n<li>Delivery related billing</li>\r\n</ul>\r\n<ul>\r\n<li>Clearing note according different tax rates</li>\r\n</ul>\r\n<ul>\r\n<li>Only items belonging to the same sales order are billed</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;together.<br /><br /><strong><strong>Note:</strong></strong><br /><strong>-----</strong><br />a. Multiple Sales order which has a Down payment cannot be invoiced &#160;&#160;&#160;&#160;&#160;&#160; together.<br /><br /><strong><strong>Eg:</strong></strong><br />&#160;&#160;1. Sales Order 1&#160;&#160;-&gt; DP Invoice -&gt; Final Invoice&#160;&#160; ----&gt; Allowed<br /><br />&#160;&#160;2. Sales Order 1&#160;&#160;-&gt; DP Invoice<br />&#160;&#160;&#160;&#160; Sales Order 2<br />&#160;&#160;&#160;&#160; Sales Order 1 &amp; Sales Order 2&#160;&#160;-&gt; Final Invoice&#160;&#160; ----&gt; Not<br />&#160;&#160;&#160;&#160; Allowed<br /><br />b. The Downpayment Invoices will not be displayed in t-code VF05 and &#160;&#160;&#160;&#160;&#160;&#160;VF05n, its only possible with the note 371675.<br /><br />c. Billing plans are not supported in Polish Downpayment solution</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Down payment, Down Payment Invoice, Delivery-related invoice, VF01,<br />Clearing note</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Legal change in Poland<br />Precondition: Process starts from sales order</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Restriction:&#160;&#160;</strong>Avoid customers with Credit Limit when using down payment scenario. Down payment invoices are treated like regular invoices from Credit Limit perspective and increase customer's credit exposure.</p>\r\n<p><strong>Please make the following customizing steps to install the solution:</strong></p>\r\n<ol>1. Create new document pricing procedure '1' - Down Payment Poland:</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;IMG path is Sales and distribution -&gt; Basic Functions -&gt; Pricing -&gt; Pric control -&gt; Define and assign pricing procedures -&gt; Define document Pricing procedure.</p>\r\n<p>&#160;</p>\r\n<ol>2. Create a new billing type:</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;This will be used to generate the down payment invoice. Go to transaction VOFA. Copy the billing type F5 - Proforma for order to create the new billing type FAF. Change the SD document category of the new billing type to 'M' - Invoice. Transaction group = \"8\" - Proforma invoices.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;For the billing type define an appropriate document type. This could be 'DR'. Define an appropriate cancellation billing type. This could be 'S1'. Also define an appropriate reference number for the cancellation billing type. This could be 'E'.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;In the account assigment/pricing subscreen maintain the account determination procedure as 'KOFI00' and account determination reconciliation account as 'KOFIAB'. Change document pricing procedure to the document pricing procedure '1' -Down Payment Poland (created in step one).</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;For the DP scenario with service items please copy the new billing type FAF into FAFS - PL DP Invoice Service without(!) copy control. Assign to Cancell.billing type \"S1S\"- Cancel Invoice (S1S).</p>\r\n<p>&#160;</p>\r\n<ol>3. Maintain the copy control from the order to the new billing type created:</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;IMG path is Sales and distribution -&gt; Billing -&gt; Billing documents -&gt; Maintain Copying Control For Billing Documents -&gt; Copying control: Sales document to billing document.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Customizing example for material items:</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Target billing type:&#160;&#160;FAF&#160;&#160;PL DP Invoice</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Source sls doc. type: OR&#160;&#160; Standard Order</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;TAN - Standard Item</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Pricing type should be 'B' - Carry out new pricing</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Pos./neg. quantity should be '0' - Zero</p>\r\n<p>&#160;</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Customizing example for service items:</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Target billing type:&#160;&#160;FAFS&#160;&#160;PL DP Invoice Service</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Source sls doc. type: OR&#160;&#160; Standard Order</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;TAD - Service</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Pricing type should be 'B' - Carry out new pricing</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Pos./neg. quantity should be '0' - Zero</p>\r\n<p>&#160;</p>\r\n<ol>4. Create a new condition type PB01 - Gross down payment by copying from the condition type PB00:</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;IMG path is Sales and distribution -&gt; Basic Functions -&gt; Pricing -&gt; Priccontrol -&gt; Define condition types -&gt; Maintain condition types.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Control data 1:</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Cond. class:&#160;&#160; B Prices</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Calculat. type: B Fixed amount</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Maintain the condition category as blank.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Group condition:</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Mark the Group condition.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Changes which can be made:</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Mark the check boxes \"item condition\", Delete\" and \"Value\".</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Unmark the remaining check boxes in this sub screen.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Scales:</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Scale basis should be blank.</p>\r\n<p>&#160;</p>\r\n<ol>5. Create a new condition type ZWIG - Tax from hund-stat by copying from the condition type MWIG - Tax from hundred:</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;IMG path is Sales and distribution -&gt; Basic Functions -&gt; Pricing -&gt; Priccontrol -&gt; Define condition types -&gt; Maintain condition types.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Access seq: MWST</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Control data 1:</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Cond. class:&#160;&#160; D Taxes</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Calculat. type: H Percentage included</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Condition category: D Tax</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Group condition:</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Mark the Group condition.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Changes which can be made:</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Manual entries: D not possible to process manually</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Mark the check boxes \"item condition\".</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Unmark the remaining check boxes in this sub screen.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Master data:</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;RefConType: MWST</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;RefApplication: V</p>\r\n<p>&#160;</p>\r\n<ol>6. Create a new accounting key EDP - Down Payment:</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;IMG path is Sales and distribution -&gt; Basic Functions -&gt; Account Assignment -&gt; Revenue Account Determination -&gt; Define and assign account keys -&gt; Define account key.</p>\r\n<p>&#160;</p>\r\n<ol>7. Create a new pricing procedure by copying the pricing procedure RVAB02:</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;IMG path is Sales and distribution -&gt; Basic Functions -&gt; Pricing -&gt; Pric control -&gt; Define And Assign Pricing Procedures -&gt; Maintain pricing procedures.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Let this copied pricing procedure be ZVAB02 - Gross Down Payment Poland and change ZVAB02 according to attached pricing_ZVAB02.zip (see attachments).</p>\r\n<p>&#160;</p>\r\n<ol>8. Define Pricing Procedure Determination:</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;In transaction OVKK - \"Pricing procedures: Determination in sales documents\" make an entry that the pricing procedure ZVABO2 is picked up when billing using the billing type FAF.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Example:</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Sales org&#160;&#160;/ DocProc&#160;&#160;&#160;&#160;&#160;&#160;/ CuPP / Pricing procedure</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;PL99 01 01 / 1-DP Poland&#160;&#160;/ 1&#160;&#160;&#160;&#160;/&#160;&#160;ZVAB02 DP Poland - Gross Pric</p>\r\n<p>&#160;</p>\r\n<ol>9. For the account key EDP assign the appropriate Deferred Revenues GL account number:</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;IMG: Transaction VKOA -&gt; Table 005 - Acc Key</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;New entries as example:</p>\r\n<ol><ol>a) V KOFI CAPL PL99 EDP 845000 (= account for deferred revenue posting in down payment invoice)</ol></ol><ol><ol>b) V KOFI CAPL PL99 ERL 701001 (= account for sales revenue posting in standard invoice)</ol></ol><ol><ol>c) V KOFI CAPL PL99 ERS 701001 (= account for sales revenue posting in standard invoice)</ol></ol><ol>10. Define settings in Reconciliation account determination so that the \"Down Payments received\" account is found instead of the normal reconciliation account during creation of down payment invoice:</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;In transaction OV64 maintain the \"Down Payments Received\" reconciliation account. This can be either at the sales org (table 004) or sales org/distribution channel level (table 008).</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;New entries as example:</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;VB KOAB CAPL PL99 205100</p>\r\n<p>&#160;</p>\r\n<ol>11. Maintain the \"Down Payments received\" account as the alternative reconciliation account:</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;IMG: Sales &amp; Distribution -&gt; Basic Functions -&gt; Account Assignment -&gt; Reconciliation account determination -&gt; Define alternative reconciliation accounts</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;New entries as example:</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;CAPL - G/L account:201000 - alternative account:205100</p>\r\n<p>&#160;</p>\r\n<ol>12. Maintain sales document type:</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Go to transaction VOV8 - Maintain sales order types.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Change your sales order type (example: OR):</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Billing -&#160;&#160;Dlv-rel. billing type: F2 - Invoice</p>\r\n<p>Billing -&#160;&#160;Order-rel.bill.type:&#160;&#160;Blank<br /> <br /><br /><br /><strong>Please continue with the following development changes:</strong><br /><br />User exit EXIT_SAPLV60B_008 in report SAPLV60B (function group V60B) is used to change the accounting entries during the creation of a delivery related invoices by checking for any open down payment amounts.</p>\r\n<ol>13. For this do the following steps:</ol><ol><ol>a) Call CMOD, enter new project name</ol></ol><ol><ol>b) Enter Enhancement 'SDVFX008', function exit 'EXIT_SAPLV60B_008'</ol></ol><ol><ol>c) Maintain source code given in this note in ZXVVFU08.</ol></ol><ol><ol>d) Activate project and program.</ol></ol>\r\n<p><br /><br /><br /><strong>Steps for printing the down payment related billing documents:</strong></p>\r\n<ol>14. Create the new printing program for down payment billing documents</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Go to transaction SE38 and create the new printing program with name RVADINPLDP - Print program for Polish invoices with down payments. Maintain the following attributes for the program</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Type - 1 Executable Program</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Status - P SAP standard production program</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Application - V sales</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Development Class - ID-FI-PL</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Uncheck the fixed point arithmetic check box.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Import the program code given in this note into the program and activate it.</p>\r\n<ol>15. Create a new output type</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;In the transaction V/40 copy the output type RDPL to a new output type. You could name this output type as ZDPF - DP Invoices.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Go to processing routines. Make the following entries for for Medium 1 - Print output. The program is RVADINPLDP, form routine is ENTRY_PL and form is called J_1PL_DP_INVOICE.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Delete the processing routines for other mediums. Save your entries.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Assign this output type to the output determination procedure that you have assigned for the billing type FAF. The IMG path is -&gt; Sales and Distribution -&gt; Basic Functions -&gt; Output Control -&gt; Output Determination -&gt; Output Determination using condition technique -&gt; maintain output determination for Billing Documents -&gt; Maintain Output determination Procedure. Typically the output determination procedure used for billing documents is V10000.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;In transaction V/25 assign the output type ZDPF to the billing type FAF.</p>\r\n<ol>16. Changes required in Down payment pricing procedure copied from RVAB02 (ZVAB02)</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Copy the condition type MWIG to a new condition type ZWIG. Change the description to 'Tax from hundred-Stat'.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;In the pricing procedure ZVAB02 add the condition type ZWIG at step number 810. Fill 99 in the from column. Check the mandatory and statistical check boxes for this entry. Enter small case 'a' in the print column. Ensure that against the condition type MWIG the print column contains upper case 'A'. Save the pricing procedure.</p>\r\n<p><br /><br /><br /><strong>Print layouts for the Down payment process:</strong><br />The print layouts for Down Payment Invoice, Clearing Note,<br />Final Invoice are delivered with note 853068 - PL: Down Payment process for Poland - printouts.<br /><br /><br /><br /><strong>Alternate numbering range for clearing documents:</strong><br /><br />If you require that the clearing billing document is to be numbered in a number sequence sequence other than that for the normal billing documents please use the user exit USEREXIT_NUMBER_RANGE (Module pool SAPLV60A, program RV60AFZZ). The suggested code that can be used is given in the correction instructions. The number range that you would like to use for clearing documents needs to be set against the variable lv_clearing_no_range during data declaration.<br /><br /><br /><br /><br /><strong>Detailed Down Payment process description (example 1 - without cancellation):</strong><br /><br /><strong>Step 1:&#160;&#160;Creation of sales order</strong><br />According to the polish law, DP amounts should be specified in relation to particular tax rates, so that tax calculation should be possible based on grouped items with the same tax rate (tax group). Also clearing of DP should be based on this group.<br />Transaction VA01: Creation of the order with 2 groups of items.<br /><br />Example: Sales order<br />&#160;&#160; 10 PL-MM1&#160;&#160;&#160;&#160;1M&#160;&#160;TAN&#160;&#160; A3<br />&#160;&#160; 20 PL-MM1&#160;&#160; 10M&#160;&#160;TAN&#160;&#160; A3<br />&#160;&#160; 30 PL-MM1&#160;&#160; 20M&#160;&#160;TAN&#160;&#160; A3<br />&#160;&#160; 40 PL-HAWA&#160;&#160; 1KG TAN&#160;&#160; A5<br />&#160;&#160; 50 PL-HAWA&#160;&#160;15KG TAN&#160;&#160; A5<br />&#160;&#160; 60 PL-HAWA&#160;&#160;25KG TAN&#160;&#160; A5<br /><br /><br /><strong>Step 2:&#160;&#160;Agreement with Customer</strong><br />An agreement with customer is made assuming, that he'll down payment (one or more) on future deliveries and dividing into item groups.<br /><br /><br /><strong>Step 3:&#160;&#160;Payment in FI</strong><br />Transaction F-29 (Post customer down payment):<br />Customer pays down payment amount (1100 PLN).<br />Please assign \"A\" as special G/L Indicator for down payment.<br /><br />Posting schema in FI:<br /> 40 131000&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Bank&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;1100,00<br /> 19 PL-KUNDE (205100)&#160;&#160;Customer down pay received&#160;&#160;&#160;&#160;&#160;&#160; 1100,00-<br /><br /><br /><strong>Step 4:&#160;&#160;Creation of down payment invoice</strong><br />Transaction VF01: Creation of down payment invoice with billing type FAF -PL DP Invoice for the sales order. The down payment per tax rate group is gross amount based and will be entered in this way into the system. The tax amount are calculated automatically backward based on the gross amounts according the pricing procedure ZVABO2.<br /><br />Select 1 material item of each tax rate group and maintain the condition PB01 - Down Payment Gross with the down payment gross amount of this group. Repeat it for down payment amount of each tax rate group.<br /><br />Example:<br /> 10 PL-MM1&#160;&#160;&#160;&#160;1M&#160;&#160;TAN, Condition PB01-Down Payment Gross 1000 PLN<br /> 40 PL-HAWA&#160;&#160; 1KG TAN, Condition PB01-Down Payment Gross&#160;&#160;100 PLN<br /><br />Posting schema in FI:<br /> 09 PL-KUNDE (205100)&#160;&#160;Customer down pay received **&#160;&#160; 1.100,00<br /> 50 222000&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Output tax&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;A3&#160;&#160;&#160;&#160; 107,14-<br /> 50 845000&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Deferred revenue&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; A3&#160;&#160;&#160;&#160; 892,86-<br /> 50 222000&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Output tax&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;A5&#160;&#160;&#160;&#160;&#160;&#160;18,03-<br /> 50 845000&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Deferred revenue&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; A5&#160;&#160;&#160;&#160;&#160;&#160;81,97-<br /><br /><br /><strong>Step 5:&#160;&#160;Clearing customer account</strong><br />Transaction F-32 - Clearing customer account: The payment posting in FI (step 3) will be cleared against the down payment invoice (step 4). The down payment invoice accounting document gets the status \"cleared\".<br /><br />Posting schema in FI:<br /> 09 PL-KUNDE (205100)&#160;&#160;Customer down pay received&#160;&#160;&#160;&#160;&#160;&#160; 1100,00<br /> 19 PL-KUNDE (205100)&#160;&#160;Customer down pay received&#160;&#160;&#160;&#160;&#160;&#160; 1100,00-<br /><br /><br /><strong>Step 6:&#160;&#160;Partial delivery of material and goods issue posting</strong><br />Transaction VL01N: Example<br />&#160;&#160; 10 PL-MM1&#160;&#160;&#160;&#160;1M<br />&#160;&#160; 20 PL-MM1&#160;&#160;&#160;&#160;2M<br />&#160;&#160; 30 PL-MM1&#160;&#160;&#160;&#160;3M<br />&#160;&#160; 40 PL-HAWA&#160;&#160; 1KG<br />&#160;&#160; 50 PL-HAWA&#160;&#160; 5KG<br />&#160;&#160; 60 PL-HAWA&#160;&#160;10KG<br /><br /><br /><strong>Step 7: Delivery-related Invoice with clearing note</strong><br />Delivery created in previous step is billed. The clearing note should be created as long as the value of a created invoice document is fully covered by the remaining part of down payments made. Transfer posting of deferred revenues to sales revenues is done. The reposting of the the calculated net value based on gross value of the DP invoice is made.<br /><br />Transaction VF01: Billing type F2-invoice<br />&#160;&#160; 10 PL-MM1&#160;&#160;&#160;&#160;1M&#160;&#160;&#160;&#160;&#160;&#160;100,00<br />&#160;&#160; 20 PL-MM1&#160;&#160;&#160;&#160;2M&#160;&#160;&#160;&#160;&#160;&#160;200,00<br />&#160;&#160; 30 PL-MM1&#160;&#160;&#160;&#160;3M&#160;&#160;&#160;&#160;&#160;&#160;300,00<br />&#160;&#160; 40 PL-HAWA&#160;&#160; 1KG&#160;&#160;&#160;&#160;&#160;&#160;10,00<br />&#160;&#160; 50 PL-HAWA&#160;&#160; 5KG&#160;&#160;&#160;&#160;&#160;&#160;50,00<br />&#160;&#160; 60 PL-HAWA&#160;&#160;10KG&#160;&#160;&#160;&#160; 100,00<br /><br />Posting schema in FI:<br />&#160;&#160;01 PL-KUNDE (201000) Acc receivables&#160;&#160;&#160;&#160;A5&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 95,20<br />&#160;&#160; 40 845000&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Deferred revenue&#160;&#160; A3&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;600,00<br />&#160;&#160; 40 845000&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Deferred revenue&#160;&#160; A5&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 81,97<br />&#160;&#160; 50 222000&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Output tax&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; A5&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 17,17-<br />&#160;&#160; 50 701001&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Sales revenues&#160;&#160;&#160;&#160; A3&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;600,00-<br />&#160;&#160; 50 701001&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Sales revenues&#160;&#160;&#160;&#160; A5&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;160,00-<br /><br /><br /><strong>Step 8:&#160;&#160;Delivery of material and goods issue posting</strong><br />Transaction VL01N: Example<br />&#160;&#160; 10 PL-MM1&#160;&#160;&#160;&#160;8M<br />&#160;&#160; 20 PL-MM1&#160;&#160; 17M<br />&#160;&#160; 30 PL-HAWA&#160;&#160;10KG<br />&#160;&#160; 40 PL-HAWA&#160;&#160;15KG<br /><br /><br /><strong>Step 9: Delivery-related Invoice with clearing note</strong><br />The normal invoice is created with VAT calculation based on net value. The delivery created in previous step is billed. The clearing note generates the reposting from deferred revenues to sales revenues.<br /><br />Transaction VF01: Billing type F2-invoice<br />&#160;&#160; 20 PL-MM1&#160;&#160;&#160;&#160;8M&#160;&#160;&#160;&#160;&#160;&#160;800,00<br />&#160;&#160; 30 PL-MM1&#160;&#160; 17M&#160;&#160;&#160;&#160; 1700,00<br />&#160;&#160; 50 PL-HAWA&#160;&#160;10KG&#160;&#160;&#160;&#160; 100,00<br />&#160;&#160; 60 PL-HAWA&#160;&#160;15KG&#160;&#160;&#160;&#160; 150,00<br /><br />Posting schema in FI:<br />&#160;&#160; 01 PL-KUNDE (201000) Acc receivables&#160;&#160; **&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;2.777,00<br />&#160;&#160; 40 845000&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Deferred revenue&#160;&#160;A3&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;292,86<br />&#160;&#160; 50 222000&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Output tax&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;A3&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;264,86-<br />&#160;&#160; 50 701001&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Sales revenues&#160;&#160;&#160;&#160;A3&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;2.500,00-<br />&#160;&#160; 50 701001&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Sales revenues&#160;&#160;&#160;&#160;A5&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;250,00-<br />&#160;&#160; 50 222000&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Output tax&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;A5&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 55,00-<br /><br /><br /><br /><br /><strong>Detailed Down Payment process description (example 2 - with cancellation):</strong><br /><br /><strong>Step 1:&#160;&#160;Creation of sales order</strong><br />According to the polish law, DP amounts should be specified in relation to particular tax rates, so that tax calculation should be possible based on grouped items with the same tax rate (tax group). Also clearing of DP should be based on this group.<br />Transaction VA01: Creation of the order with 2 groups of items.<br /><br />Example: Sales order<br />&#160;&#160; 10 PL-MM1&#160;&#160;&#160;&#160;1M&#160;&#160;TAN&#160;&#160; A3<br />&#160;&#160; 20 PL-MM1&#160;&#160; 10M&#160;&#160;TAN&#160;&#160; A3<br />&#160;&#160; 30 PL-MM1&#160;&#160; 20M&#160;&#160;TAN&#160;&#160; A3<br />&#160;&#160; 40 PL-HAWA&#160;&#160; 1KG TAN&#160;&#160; A5<br />&#160;&#160; 50 PL-HAWA&#160;&#160;15KG TAN&#160;&#160; A5<br />&#160;&#160; 60 PL-HAWA&#160;&#160;25KG TAN&#160;&#160; A5<br /><br /><br /><strong>Step 2:&#160;&#160;Agreement with Customer</strong><br />An agreement with customer is made assuming, that he'll down payment (one or more) on future deliveries and dividing into item groups.<br /><br /><br /><strong>Step 3:&#160;&#160;Payment in FI</strong><br />Transaction F-29 (Post customer down payment):<br />Customer pays down payment amount (1100 PLN).<br />Please assign \"A\" as special G/L Indicator for down payment.<br /><br />Posting schema in FI:<br /> 40 131000&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Bank&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;1100,00<br /> 19 PL-KUNDE (205100)&#160;&#160;Customer down pay received&#160;&#160;&#160;&#160;&#160;&#160; 1100,00-<br /><br /><br /><strong>Step 4:&#160;&#160;Creation of down payment invoice with wrong amount</strong><br />Transaction VF01: Creation of down payment invoice with billing type FAF -PL DP Invoice for the sales order. The down payment per tax rate group is gross amount based and will be entered in this way into the system. The tax amount are calculated automatically backward based on the gross amounts according the pricing procedure ZVABO2.<br /><br />Select 1 material item of each tax rate group and maintain the condition PB01 - Down Payment Gross with the down payment gross amount of this group. Repeat it for down payment amount of each tax rate group.<br /><br />Example:<br /> 10 PL-MM1&#160;&#160;&#160;&#160;1M&#160;&#160;TAN, Condition PB01-Down Payment Gross&#160;&#160;100 PLN<br /> 40 PL-HAWA&#160;&#160; 1KG TAN, Condition PB01-Down Payment Gross&#160;&#160; 10 PLN<br /><br />Posting schema in FI:<br /> 09 PL-KUNDE (205100)&#160;&#160;Customer down pay received **&#160;&#160;&#160;&#160; 110,00<br /> 50 222000&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Output tax&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;A3&#160;&#160;&#160;&#160;&#160;&#160;10,71-<br /> 50 845000&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Deferred revenue&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; A3&#160;&#160;&#160;&#160;&#160;&#160;89,29-<br /> 50 222000&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Output tax&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;A5&#160;&#160;&#160;&#160;&#160;&#160; 1,80-<br /> 50 845000&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Deferred revenue&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; A5&#160;&#160;&#160;&#160;&#160;&#160; 8,20-<br /><br /><br /><strong>Step 5:&#160;&#160;Cancellation of down payment invoice</strong><br />Transaction VF02: Cancellation of down payment invoice because of entering wrong amount. Changing of billing document =&gt; Cancel of billing document.<br /><br />System reverses automatically the postings of the created billing document.<br /><br />Posting schema in FI:<br /> 19 PL-KUNDE (205100)&#160;&#160;Customer down pay received **&#160;&#160;&#160;&#160; 110,00-<br /> 40 222000&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Output tax&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;A3&#160;&#160;&#160;&#160;&#160;&#160;10,71<br /> 40 845000&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Deferred revenue&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; A3&#160;&#160;&#160;&#160;&#160;&#160;89,29<br /> 40 222000&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Output tax&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;A5&#160;&#160;&#160;&#160;&#160;&#160; 1,80<br /> 40 845000&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Deferred revenue&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; A5&#160;&#160;&#160;&#160;&#160;&#160; 8,20<br /><br /><br /><br /><strong>Step 6:&#160;&#160;Creation of down payment invoice with correct amount</strong><br />Transaction VF01: Creation of down payment invoice with billing type FAF -PL DP Invoice for the sales order.<br /><br />Example:<br /> 10 PL-MM1&#160;&#160;&#160;&#160;1M&#160;&#160;TAN, Condition PB01-Down Payment Gross 1000 PLN<br /> 40 PL-HAWA&#160;&#160; 1KG TAN, Condition PB01-Down Payment Gross&#160;&#160;100 PLN<br /><br />Posting schema in FI:<br /> 09 PL-KUNDE (205100)&#160;&#160;Customer down pay received **&#160;&#160; 1.100,00<br /> 50 222000&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Output tax&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;A3&#160;&#160;&#160;&#160; 107,14-<br /> 50 845000&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Deferred revenue&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; A3&#160;&#160;&#160;&#160; 892,86-<br /> 50 222000&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Output tax&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;A5&#160;&#160;&#160;&#160;&#160;&#160;18,03-<br /> 50 845000&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Deferred revenue&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; A5&#160;&#160;&#160;&#160;&#160;&#160;81,97-<br /><br /><br /><br /><strong>Step 7:&#160;&#160;Clearing customer account</strong><br /><br /><br /><strong>Step 8:&#160;&#160;Delivery of material and goods issue posting</strong><br /><br /><br /><strong>Step 9:&#160;&#160;Delivery-related Invoice with clearing note with wrong amount</strong><br /><br /><br /><strong>Step 10: Cancellation of Delivery-related Invoice with clearing note because of wrong amount</strong><br />Equal step 5: Transaction VF02: Cancellation of delivery related invoice because of entering wrong amount. Changing of billing document =&gt; Cancel of billing document.<br /><br />System reverses automatically the postings of the created billing document.<br /><br /><br /><strong>Step 11: Delivery-related Invoice with clearing note with correct amount</strong><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SD-BIL-IV-DP (Down Payments)"}, {"Key": "Other Components", "Value": "XX-CSC-PL (Poland)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I065386)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000818079/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000818079/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000818079/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000818079/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000818079/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000818079/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000818079/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000818079/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000818079/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "pricing_ZVAB02.zip", "FileSize": "23", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000136092005&iv_version=0016&iv_guid=10D262E2964ADB49808E721C643EC7E9"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "956434", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "SD DP Poland: DP functionality with multiple exchange rates", "RefUrl": "/notes/956434"}, {"RefNumber": "934940", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "Down Payment Poland: Invoice Printing is incorrect", "RefUrl": "/notes/934940"}, {"RefNumber": "893050", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "Incorrect printing of invoices for down payment process", "RefUrl": "/notes/893050"}, {"RefNumber": "853068", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "PL: Down Payment process for Poland - printouts", "RefUrl": "/notes/853068"}, {"RefNumber": "1629758", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "DP PL: Alternate number range for the clearing invoices", "RefUrl": "/notes/1629758"}, {"RefNumber": "1587818", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "DP Poland: Redesigning of Down-payment functionality", "RefUrl": "/notes/1587818"}, {"RefNumber": "1464694", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "Error F5 704 - Inconsistent Amnts in Invoice with Down Pymnt", "RefUrl": "/notes/1464694"}, {"RefNumber": "1383455", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "Error F5702 produced when trying to post final downpayment", "RefUrl": "/notes/1383455"}, {"RefNumber": "1244831", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "Invoice quantity doubled when DP Invoice cancelled - Poland", "RefUrl": "/notes/1244831"}, {"RefNumber": "1241359", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "Incorrect Downpayment values in one-step invoice for Poland", "RefUrl": "/notes/1241359"}, {"RefNumber": "1179924", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "Incorrect exchange rate picked for DP invoice for Poland", "RefUrl": "/notes/1179924"}, {"RefNumber": "1167396", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "DP invoice not considered in final invoice print for Poland", "RefUrl": "/notes/1167396"}, {"RefNumber": "1013558", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "PL: Correction Invoices in DP process - printouts", "RefUrl": "/notes/1013558"}, {"RefNumber": "1007635", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "PL: Handling Correction Invoices in DP process", "RefUrl": "/notes/1007635"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2861706", "RefComponent": "XX-PROJ-CDP-571", "RefTitle": "SAF-T Poland - PLVAT 100: Release note PLVAT 100 SP07", "RefUrl": "/notes/2861706 "}, {"RefNumber": "1587818", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "DP Poland: Redesigning of Down-payment functionality", "RefUrl": "/notes/1587818 "}, {"RefNumber": "1013558", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "PL: Correction Invoices in DP process - printouts", "RefUrl": "/notes/1013558 "}, {"RefNumber": "1629758", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "DP PL: Alternate number range for the clearing invoices", "RefUrl": "/notes/1629758 "}, {"RefNumber": "1464694", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "Error F5 704 - Inconsistent Amnts in Invoice with Down Pymnt", "RefUrl": "/notes/1464694 "}, {"RefNumber": "1383455", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "Error F5702 produced when trying to post final downpayment", "RefUrl": "/notes/1383455 "}, {"RefNumber": "1007635", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "PL: Handling Correction Invoices in DP process", "RefUrl": "/notes/1007635 "}, {"RefNumber": "1244831", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "Invoice quantity doubled when DP Invoice cancelled - Poland", "RefUrl": "/notes/1244831 "}, {"RefNumber": "1241359", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "Incorrect Downpayment values in one-step invoice for Poland", "RefUrl": "/notes/1241359 "}, {"RefNumber": "1179924", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "Incorrect exchange rate picked for DP invoice for Poland", "RefUrl": "/notes/1179924 "}, {"RefNumber": "1167396", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "DP invoice not considered in final invoice print for Poland", "RefUrl": "/notes/1167396 "}, {"RefNumber": "853068", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "PL: Down Payment process for Poland - printouts", "RefUrl": "/notes/853068 "}, {"RefNumber": "956434", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "SD DP Poland: DP functionality with multiple exchange rates", "RefUrl": "/notes/956434 "}, {"RefNumber": "934940", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "Down Payment Poland: Invoice Printing is incorrect", "RefUrl": "/notes/934940 "}, {"RefNumber": "893050", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "Incorrect printing of invoices for down payment process", "RefUrl": "/notes/893050 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 46B", "SupportPackage": "SAPKH46B60", "URL": "/supportpackage/SAPKH46B60"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C52", "URL": "/supportpackage/SAPKH46C52"}, {"SoftwareComponentVersion": "SAP_APPL 470", "SupportPackage": "SAPKH47026", "URL": "/supportpackage/SAPKH47026"}, {"SoftwareComponentVersion": "SAP_APPL 500", "SupportPackage": "SAPKH50011", "URL": "/supportpackage/SAPKH50011"}, {"SoftwareComponentVersion": "SAP_APPL 600", "SupportPackage": "SAPKH60003", "URL": "/supportpackage/SAPKH60003"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 7, "URL": "/corrins/0000818079/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 7, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}