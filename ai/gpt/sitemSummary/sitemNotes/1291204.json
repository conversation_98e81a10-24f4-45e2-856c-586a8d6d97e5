{"Request": {"Number": "1291204", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 297, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016702052017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=0944B00BCDC6D94033757BC42500E530"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1291204"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "03.04.2018"}, "SAPComponentKey": {"_label": "Component", "value": "BW-WHM-DST-DTP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Data Transfer Process"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Warehouse Management", "value": "BW-WHM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Staging", "value": "BW-WHM-DST", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DST*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Transfer Process", "value": "BW-WHM-DST-DTP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DST-DTP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1291204 - Loading with DTP: Failed authorization check on 0BI_ALL"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Data loading with DTP fails and the log shows an authorization error:<br />User&#160; does not have authorization for InfoProvider &amp;1<br />Message no. RS_EXCEPTION251</p>\r\n<p><br />The basis authorization log (ST01 or SU53) shows a failed check on authorization object S_RS_AUTH for value 0BI_ALL.<br />This is <strong>no error</strong> (see below) but is due to authorization checks in data staging.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>DTP, Status 'Processed with Errors' RSBK257&#160;&#160;RSBK 257 \"You do not have sufficient authorization for InfoProvider &lt;IP&gt;\" EYE 001, EYE007<br />\"You donnot have sufficient authorization\" EYE 007, EYE001, RS_EXCEPTION 251</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The user executing the Data loading does not have the authorization required for the staged data.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Important:</p>\r\n<ol><ol>\r\n<li>The failed check on authorization object S_RS_AUTH for value 0BI_ALL&#160;<strong>is no error!&#160;</strong></li>\r\n<li>It does&#160;<strong>not&#160;</strong>mean that&#160;<strong>always&#160;</strong>the&#160;<strong>full&#160;</strong>authorization would be required.</li>\r\n</ol></ol><ol>3. See also note 820183.</ol><ol>4. If anyway an error in the Authorization Check is suspected, please follow note 1234567. Please analyze the RSECADMIN-Log.</ol><ol>5. An OSS message should only be opened in two situations:</ol><ol><ol>a) The given Authorizations should authorize the Selection, but fail to do so. In this case, please state clearly which Authorization (technical name) should do this. A problem can not be investigated when it is not clear where exactly it goes wrong.</ol></ol><ol><ol>b) The Selection shown in the RSECADMIN-log does not fit to the actually selected data: It is too large and thus does not get authorized. Then please state what you expect to be selected and how this can be reproduced.</ol></ol>\r\n<p>In both cases the RSECADMIN-Log should get attached to the message. (See below.)<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<br /><br />It is a technical artefact and just indicates that the corresponding user does not have full authorization. But this is usually desired and valid.<br /><br />Also note:<br />Even in case the staging user gets full data authorizations assigned, this does NOT mean that this user automatically can see all data in all queries: <strong>Query execution is additionally protected by the authorization object S_RS_COMP.</strong><br /><br />Details:<br />In DTP data loading with extraction from InfoProviders the BI Analysis Authorizations are checked.<br />The extracting user must have authorizations that are sufficient for the selected data.<br />With this concept, beginning from BI 7.0, the level of security has been improved in the area of staging. It enables also to protect/allow various separate areas of data which can be accessed. One may use InfoProviders or even data based access control on value level. The concept is based on the analysis authorizations that are also used for reporting in queries.<br />To configure a staging authorization concept you need to configure analysis authorizations.<br />Contact your administrator for Analysis Authorizations in case you want to use this capability or see also the docu for Analysis Authorizations:<br />http://help.sap.com/saphelp_nw04s/helpdata/en/be/076f3b6c980c3be10000000a11402f/frameset.htm<br /><br />Please understand that a technical explanation of the functionality of Analysis Authorizations can not be provided within an OSS message.<br />The note 1234567 explains the details of the Protocol for BI Analysis Authorizations (RSECADMIN-Log)</p>\r\n<p><strong>Scenarios</strong></p>\r\n<p><br />Solution 1:<br />If only one user ID is used for Data Loading (only), and no separation of data is desired it is possible to assign the full BI Analysis Authorizations 0BI_ALL. Then, all data in all InfoProviders are accessible through staging. This is compatible to 3.x where no data access was checked and only the (still available) high level checks on authorization objects where performed. Please be aware, that also new authorization checks in staging have been introduced with 7.0.<br />Also note:<br />Granting 0BI_ALL does not necessarily mean full access to all queries because query execution is also protected by the Authorization Object S_RS_COMP.<br />Also, the loading user may be defined as System/Background (not Dialog user) inorder to avoid security issues completely.<br /><br />Solution 2:<br />If you want to separate data areas (for instance HR data and other business data) you can use different staging users with different analysis authorizaitons basing on InfoProviders or even on data areas within a single InfoProvider.<br />Usually, (but not necessarily,) staging users are batch users so they are not able to access backend tools as SE16 to look at data they are not authorized to. Have in mind however, that Administrator (dialog) users might need authorizations on a more certain level concerning tools like SE16, application log (no analysis auth check), RSA3 (analysis auth check included), PSA(?), ... depending on the security requirements for the staging administrators.</p>\r\n<p><strong>Performance</strong></p>\r\n<p>Authorizaton check impact on performance is comparable (if not less) to what can be expected in online execution of queries. Typically the authorization check is far less than a second (unless you use a very complicated detailed authorization concept where execution time can raise). Therefore, the staging process is typically expected to be dominated by the data transfer and transformation processes which may include RFC and data base accesses.<br /><br /></p>\r\n\r\n<p><br /><br /><strong>Logical Background:</strong><br />The DTP reads data from an InfoProvider. It is a general principle in SAP BI that all such reading <strong>must</strong> be authorized by Analysis Authorizations to enable distinct security models with different users.<br />Not checking the appropriate authorizations would be a security gap!<br /><br /><br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-WHM-DST-AUT (Authorizations)"}, {"Key": "Responsible                                                                                         ", "Value": "D031457"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I048594)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "820183", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "New authorization concept in BI", "RefUrl": "/notes/820183"}, {"RefNumber": "1307871", "RefComponent": "BW-WHM-DST-AUT", "RefTitle": "DTP Extraction: No Authorization when no KeyFigures", "RefUrl": "/notes/1307871"}, {"RefNumber": "1303248", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Authorizations: Buffering Exit variables", "RefUrl": "/notes/1303248"}, {"RefNumber": "1234567", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "The authorization log RSECADMIN", "RefUrl": "/notes/1234567"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2615773", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Data loading with Analysis Authorization check", "RefUrl": "/notes/2615773 "}, {"RefNumber": "1234567", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "The authorization log RSECADMIN", "RefUrl": "/notes/1234567 "}, {"RefNumber": "1307871", "RefComponent": "BW-WHM-DST-AUT", "RefTitle": "DTP Extraction: No Authorization when no KeyFigures", "RefUrl": "/notes/1307871 "}, {"RefNumber": "1303248", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Authorizations: Buffering Exit variables", "RefUrl": "/notes/1303248 "}, {"RefNumber": "820183", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "New authorization concept in BI", "RefUrl": "/notes/820183 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "702", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "710", "To": "720", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}