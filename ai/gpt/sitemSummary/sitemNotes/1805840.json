{"Request": {"Number": "1805840", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 300, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017581592017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001805840?language=E&token=22721CBB66AADC76064372DA7AAC5137"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001805840", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001805840/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1805840"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "29.10.2019"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-ADDON"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade Add-On Components"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade Add-On Components", "value": "BC-UPG-ADDON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-ADDON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1805840 - Installation / Delta update of FCC0 200"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Planning the installation and Upgrade of the ABAP Add-on: FCCO 200.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAINT, <PERSON><PERSON> (Software Update Manager), CD51045305, SAP FIN. CLOSING COCKPIT 2.0</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You want to install the ABAP add-on FCCO 200 on SAP Netweaver 701 / 702 / 731/740/750 / S/4 HANA OP 1511 or S/4 HANA 1610</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Obtain the current version of this note before you start the<br />installation process.<br />Contents<br />1. Change history<br />2. Prerequisites for installing FCCO 200<br />3. Preparing to install FCCO 200<br />4. Installing FCCO 200<br />5. Delta Upgrade to FCCO 200<br />a) Including FCCO 200 in a Delta Upgrade with SAINT<br />b) Including FCCO 200 in a Delta Upgrade with SUM.<br />6. After you have installed FCCO 200<br />7. Language support<br />8. Password<br /><br />1. Change history</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>DATE</th><th>Section</th><th>Short Description</th></tr>\r\n<tr>\r\n<td>16.05.2012</td>\r\n<td>&nbsp;</td>\r\n<td>Compatibility with Netweaver 740</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />2. Prerequisites for installing FCCO 200<br /><br />o&#160;&#160;Uninstalling FCCO:<br />Before you install FCCO, note that you cannot uninstall ABAP add-ons!<br /><br />o&#160;&#160;Required release:<br />SAP NetWeaver 7.0 EHP1/EHP2/EHP3&#160;&#160;or SAP EHP1 FOR SAP NETWEAVER 7.3, SAP NETWEAVER 7.4<br /><br />o&#160;&#160;Import the latest SPAM/SAINT update:<br />Make sure that you have installed the latest SPAM/SAINT update in your system. If a later version is available on SAP Service Marketplace, import the new SPAM/SAINT update (http://service.sap.com/patches).<br /><br />o&#160;&#160;Import the latest R3trans and tp. Ensure that you have imported the latest kernel version into your system. If a newer version is available on SAP ServiceMarket place, import the most recent kernel.<br /><br />o&#160;&#160;Obtain the following notes before you begin the installation:<br />Add-ons: Conditions: 70228<br />Problems with transaction SAINT: 822380<br /><br />Before installing FCCO 200, make sure that you have implemented the following SAP notes. Otherwise the Installation will end with DDIC Activation errors:<br /><br />1685775 - FCC AddOn - remote jobs support<br />1757881 - FCC: SYNTAX_ERROR in CL_FCC_UTIL_RT<br />1803002 - SchedMan: enhancements for FCC AddOn 2.0<br /><br />Kindly make sure to get the latest version of the ACP file \"FCCO======200\" before installing/upgrading FCCO 200.</p>\r\n<p><br />o&#160;&#160;Required Support Packages:<br />-&#160;&#160;Check that your system meets the following requirements:<br />I.Required components (SAP Netweaver 7.0 EHP1)<br /><br />PI_BASIS&#160;&#160; 701<br />SAP_ABA&#160;&#160;&#160;&#160;701<br />SAP_BASIS&#160;&#160;701<br />SAP_BS_FND 701<br />SAP_BW&#160;&#160;&#160;&#160; 701<br /><br />-&#160;&#160;Required Component Support Packages:<br />PI_BASIS&#160;&#160; 701 SAPK-70111INPIBASIS<br />SAP_ABA&#160;&#160;&#160;&#160;701 SAPKA70111<br />SAP_BASIS&#160;&#160;701 SAPKB70111<br />SAP_BS_FND 701 SAPK-70111INSAPBSFND<br />SAP_BW&#160;&#160;&#160;&#160; 701 SAPKW70111<br /><br /><br />II.Required Components (SAP Netweaver 7.0 EHP2)<br />PI_BASIS&#160;&#160; 702<br />SAP_ABA&#160;&#160;&#160;&#160;702<br />SAP_BASIS&#160;&#160;702<br />SAP_BS_FND 702<br />SAP_BW&#160;&#160;&#160;&#160; 702<br /><br />-&#160;&#160;Required Component Support Packages:<br />PI_BASIS&#160;&#160; 702 SAPK-70211INPIBASIS<br />SAP_ABA&#160;&#160;&#160;&#160;702 SAPKA70211<br />SAP_BASIS&#160;&#160;702 SAPKB70211<br />SAP_BS_FND 702 SAPK-70209INSAPBSFND<br />SAP_BW&#160;&#160;&#160;&#160; 702 SAPKW70211<br /><br />III.Required Components (SAP Netweaver 7.0 EHP3 or EHP1 FOR SAP NETWEAVER 7.3)<br /><br />PI_BASIS&#160;&#160; 731<br />SAP_ABA&#160;&#160;&#160;&#160;731<br />SAP_BASIS&#160;&#160;731<br />SAP_BS_FND 731<br />SAP_BW&#160;&#160;&#160;&#160; 731<br /><br />-&#160;&#160;Required Component Support Packages:<br />PI_BASIS&#160;&#160; 731 SAPK-73103INPIBASIS<br />SAP_ABA&#160;&#160;&#160;&#160;731 SAPKA73103<br />SAP_BASIS&#160;&#160;731 SAPKB73103<br />SAP_BS_FND 731 SAPK-73103INSAPBSFND<br />SAP_BW&#160;&#160;&#160;&#160; 731 SAPKW73103<br /><br />IV.Required Components (SAP NETWEAVER 7.4)<br /><br />PI_BASIS&#160;&#160; 740<br />SAP_ABA&#160;&#160;&#160;&#160;740<br />SAP_BASIS&#160;&#160;740<br />SAP_BS_FND 747<br />SAP_BW&#160;&#160;&#160;&#160; 740<br /><br />-&#160;&#160;Required Component Support Packages:<br />PI_BASIS&#160;&#160; 740 SAPK-74002INPIBASIS<br />SAP_ABA&#160;&#160;&#160;&#160;740 SAPKA74002<br />SAP_BASIS&#160;&#160;740 SAPKB74002<br />SAP_BS_FND 747<br />SAP_BW&#160;&#160;&#160;&#160; 740 SAPKW74002<br /><br />&#160;&#160;Required Component Support Packages for Netweaver 7.5:</p>\r\n<p>SAP_BS_FND 748</p>\r\n<p>SAP_BASIS 750</p>\r\n<p>WEBCUIF 748</p>\r\n<p>MDG_FND 749 or MDG_FND 750 or MDG_FND 751 or MDG_FND 752</p>\r\n<p>&#160;</p>\r\n<p>Required Component Support pacakges for S/4HANA On Premise 1511:</p>\r\n<p>MDG_FND 800</p>\r\n<p>S4CORE 100</p>\r\n<p>SAP_BASIS 750</p>\r\n<p>WEBCUIF 800</p>\r\n<p>&#160;</p>\r\n<p>Required Component Support pacakges for SAP S/4HANA 1610:</p>\r\n<p>MDG_FND 801</p>\r\n<p>S4CORE 101</p>\r\n<p>SAP_ABA 75B</p>\r\n<p>SAP_BASIS 751</p>\r\n<p>WEBCUIF 801</p>\r\n<p><br />-&#160;&#160;If you have not yet installed these Component Support Packages, you can include them in the FCCO 200 installation.<br /><br />o&#160;&#160;Additional information about the installation:<br />The material number of the CD is 51045305.<br /><br />3. Preparing to install FCCO 200<br /><br />o&#160; Obtain the Add-On FCCO 200<br />The installation CD for FCCO 200 is not automatically sent to all customers. Order the CD with material number 51045305 from your local subsidiary or download CD51045305 from SAP Service Marketplace:</p>\r\n<p><a target=\"_blank\" href=\"http://support.sap.com/\">http://support.sap.com</a> -&gt;Software Downloads -&gt;By Category -&gt;SAP Application Components -&gt;SAP FINANCIAL CLOSING COCKPIT&#160; -&gt;SAP FIN. CLOSING COCKPIT 2.0</p>\r\n<p><br />o&#160;&#160;Load the CD on the host<br />-&#160;&#160;Log on as user:<br />&lt;sid&gt;adm&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;for UNIX<br />&lt;SID&gt;OFR&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;for AS/400<br />&lt;SID&gt;adm&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;for Windows<br /><br />-&#160;&#160;Switch to the directory &lt;DIR_EPS_ROOT&gt; of your SAP system(usually /usr/sap/trans/EPS). The directory &lt;DIR_EPS_ROOT&gt; is also displayed under DIR_EPS_ROOT after you execute the report RSPFPAR.<br /><br />-&#160;&#160;Go to the higher-level directory of &lt;DIR_EPS_ROOT&gt;.<br />-&#160;&#160;Unpack the SAR archive K-200AGINFCCO.SAR present in the subfolder DATA on the CD using the following statement:<br /><br />SAPCAR -xvf /&lt;CD_DIR&gt;/DATA_UNITS/FCCO_200_INST/DATA/K-200AGINFCCO.SAR on UNIX<br /><br />SAPCAR '-xvf /QOPT/&lt;VOLID&gt;/DATA_UNITS/FCCO_200_INST/DATA/K-200AGINFCCO.SAR' on AS/400<br /><br />SAPCAR -xvf &lt;CD_DRIVE&gt;:\\DATA_UNITS\\FCCO_200_INST\\DATA\\K-200AGINFCCO.SAR on Windows<br /><br />-&#160;&#160;The file CSR0120031469_0075241.PAT should now be in the directory &lt;DIR_EPS_ROOT&gt;/in.<br /><br />4. Installing FCCO 200<br /><br />Installation with transaction: SAINT<br /><br />-&#160;&#160;Before defining the queue, kindly refer to the Note: 1685775 and do the needful changes else you may get generation errors<br /><br />-&#160;&#160;User to be used.<br />Log onto your system in client 000 as a user with SAP_ALL authorization.<br />Do NOT use the SAP* or DDIC users.<br /><br />-&#160;&#160;Load the add-on installation package.<br />Call transaction SAINT and choose 'Load'. After the list of uploaded packages is displayed, you can return to the initial screen of transaction SAINT by choosing either F3 or 'Back'.<br /><br />-&#160;&#160;Start the installation.<br />Call transaction SAINT, choose 'Start', highlight the Add-On FCCO 200, and choose 'Continue'. If all of the necessary conditions for importing the add-on have been fulfilled, the system will now display the relevant queue. The queue consists of the installation package, and it may also contain Support Packages and Add-On Support Packages. To start the installation process, choose 'Continue'. For more information, call transaction SAINT and choose 'Info' on the application toolbar. You may be prompted to enter a password. This password is provided below, in Section: 8<br /><br /><br />5. Delta Upgrade to FCCO 200<br /><br />a)&#160;&#160;Including FCCO 200 in a Delta Upgrade with SAINT.<br /><br />-&#160;&#160; If you do not intend to upgrade the Netweaver Release; when you upgrade FCCO to release 200, you can use SAINT. The Relevant package is: SAPK-200AGINFCCO<br /><br />The password can be found in Section: 8<br /><br /><br />b)&#160;&#160;Including FCCO 200 in a Delta Upgrade with SUM.<br /><br />-&#160;&#160; If you intend to upgrade the Netweaver Release; when you upgrade FCCO to release 200, kindly use Software Update Manager. The Relevant package is: SAPK-200AGINFCCO<br /><br />- The general procedure for Enhancement Packages is described in the Enhancement Package Installation Guide.<br /><br /><br />6. After you have installed FCCO 200</p>\r\n<ul>\r\n<li>After you have installed FCCO 200, you should apply the following notes:<br />1788063 - Perf. of function BP_JOBCONTEXT_SELECT_BY_CNTXT poor<br />1844271 - Job status can not be changed</li>\r\n</ul>\r\n<ul>\r\n<li>Delivery Customizing:</li>\r\n</ul>\r\n<p>-&#160;&#160;Delivery Customizing is imported into client 000 and may have to be copied to other clients. For more information, see Note 337623.</p>\r\n<ul>\r\n<li>Support Packages:</li>\r\n</ul>\r\n<p>-&#160;&#160;After installation, install the available Support Packages (SPs) for FCCO 200 in your system. Download all available Support Packages from the SAP Software Center at <a target=\"_blank\" href=\"http://support.sap.com/\">http://support.sap.com/</a> -&gt; Software Downloads -&gt; Support Packages and Patches -&gt; By Category -&gt;SAP Application Components -&gt;SAP FINANCIAL CLOSING COCKPIT&#160; -&gt;SAP FIN. CLOSING COCKPIT 2.0&#160;and install them using transaction SPAM. Pay attention to any specific points made in the relevant Support Package note.</p>\r\n<ul>\r\n<li>Generation errors: - You might encounter generation errors, if the note 1685775 is not applied before the Installation. In order to resolve the generation errors apply the note 1685775</li>\r\n</ul>\r\n<p><br />7. Language support</p>\r\n<ul>\r\n<li>FCCO 200 is available in the following languages:</li>\r\n</ul>\r\n<p><br />Arabic<br />Bulgarian<br />Catalan<br />Czech<br />Danish<br />German<br />Greek<br />English<br />Estonian<br />Finnish<br />French<br />Hungarian<br />Italian<br />Korean<br />Lithuanian<br />Latvian<br />Dutch<br />Norwegian<br />Polish<br />Portuguese<br />Romanian<br />Russian<br />Slovak<br />Slovenian<br />Serbian<br />Swedish<br />Thai<br />Turkish<br />Ukrainian<br />Vietnamese<br />Chinese trad.<br />Chinese<br /><br /></p>\r\n<ul>\r\n<li>All language-dependent parts of FCCO 200 are contained in the installation package of the add-on.If the relevant default language is in the system when you import FCCO 200,the language part of the add-on is imported automatically.You do not have to import a language transport separately.</li>\r\n</ul>\r\n<ul>\r\n<li>If you import a new standard language into your system after you install FCCO 200, you must manually ensure that the corresponding language-dependent part of the add-on is imported. For more information, see Note 195442.</li>\r\n</ul>\r\n<p><br />8. Password</p>\r\n<ul>\r\n<li>If the transaction SAINT is used to install the add-on: FCCO 200, the system prompts you to enter a password. This password is:97FD6B5CC7</li>\r\n</ul></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I039876)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D041903)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001805840/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001805840/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001805840/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001805840/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001805840/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001805840/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001805840/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001805840/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001805840/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1682765", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release Strategy Financial Closing Cockpit", "RefUrl": "/notes/1682765"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2513315", "RefComponent": "CA-GTF-FCC", "RefTitle": "SAP Financial Closing cockpit Add-On 1.0: Upgrade to S/4HANA", "RefUrl": "/notes/2513315 "}, {"RefNumber": "1682765", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release Strategy Financial Closing Cockpit", "RefUrl": "/notes/1682765 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "FCCO", "From": "200", "To": "200", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}