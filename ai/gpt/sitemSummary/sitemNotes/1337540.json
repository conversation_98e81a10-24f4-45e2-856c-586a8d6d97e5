{"Request": {"Number": "1337540", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 365, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000007869162017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001337540?language=E&token=599A12A14E20AEC52F9CF0E6FFB9AB19"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001337540", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001337540/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1337540"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "29.03.2012"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CCM-MON-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Database Monitors for Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Computer Center Management System (CCMS)", "value": "BC-CCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "CCMS Monitoring & Alerting", "value": "BC-CCM-MON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-MON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Monitors for Oracle", "value": "BC-CCM-MON-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-MON-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1337540 - Wrong Oracle Table History Data in Service Download"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>When comparing segment history information of Oracle database based systems between ST14 and DB02N (SAP_BASIS 620-700) or DBACOCKPIT/DB02 (700 SP12+) you notice differences in size and further measures.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>DBACockpit Oracle, DB02 Oracle<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>With SAP_BASIS 620 a new history collector for Oracle was introduced. Please Refer to SAP Note 868063 for details. This new collector is used for DB02N and DBACockpit.<br /><br />ST14 uses the old collector instead which cannot certain cases due to technical limitations. Therefore the values reported by the old collector are wrong in some cases, especially for systems using BI.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>By implementing the correction instructions of this note or the corresponding support package, the function module used by <PERSON>14 is changed to make use of the new collector for DBACockpit if available.<br /><br />The availability is checked by reading table DB02_COLL_PLAN which is the main planning table for all collectors used for the Oracle DBACockpit.<br /><br /><B>Note:</B> You probably need to unlock the report LSDBORA3F02 for edition<br />when implementing the correction instructions.<br /><br />Complete the following steps:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;o&#x00A0;&#x00A0;Go to transaction SE16<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;o&#x00A0;&#x00A0;Display table PROGDIR for Name='LSDBORA3F02'.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;o&#x00A0;&#x00A0;Set field EDTX to blank.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;o&#x00A0;&#x00A0;Save<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-SMG-SDD (Service Data Download)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D044039)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I054369)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001337540/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001337540/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001337540/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001337540/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001337540/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001337540/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001337540/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001337540/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001337540/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "868063", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "Database space statistics monitor for Oracle", "RefUrl": "/notes/868063"}, {"RefNumber": "1482296", "RefComponent": "SV-SMG-DVM", "RefTitle": "DVM Service: analysed data volume incorrect (Oracle)", "RefUrl": "/notes/1482296"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1482296", "RefComponent": "SV-SMG-DVM", "RefTitle": "DVM Service: analysed data volume incorrect (Oracle)", "RefUrl": "/notes/1482296 "}, {"RefNumber": "1077981", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Upgrade Assessment Preparation Note", "RefUrl": "/notes/1077981 "}, {"RefNumber": "868063", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "Database space statistics monitor for Oracle", "RefUrl": "/notes/868063 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "620", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "711", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62073", "URL": "/supportpackage/SAPKB62073"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62072", "URL": "/supportpackage/SAPKB62072"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64025", "URL": "/supportpackage/SAPKB64025"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70021", "URL": "/supportpackage/SAPKB70021"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70020", "URL": "/supportpackage/SAPKB70020"}, {"SoftwareComponentVersion": "SAP_BASIS 701", "SupportPackage": "SAPKB70105", "URL": "/supportpackage/SAPKB70105"}, {"SoftwareComponentVersion": "SAP_BASIS 701", "SupportPackage": "SAPKB70106", "URL": "/supportpackage/SAPKB70106"}, {"SoftwareComponentVersion": "SAP_BASIS 702", "SupportPackage": "SAPKB70202", "URL": "/supportpackage/SAPKB70202"}, {"SoftwareComponentVersion": "SAP_BASIS 710", "SupportPackage": "SAPKB71009", "URL": "/supportpackage/SAPKB71009"}, {"SoftwareComponentVersion": "SAP_BASIS 711", "SupportPackage": "SAPKB71104", "URL": "/supportpackage/SAPKB71104"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 14, "URL": "/corrins/0001337540/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 14, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "1337540 ", "URL": "/notes/1337540 ", "Title": "Wrong Oracle Table History Data in Service Download", "Component": "BC-CCM-MON-ORA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "1337540 ", "URL": "/notes/1337540 ", "Title": "Wrong Oracle Table History Data in Service Download", "Component": "BC-CCM-MON-ORA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1337540 ", "URL": "/notes/1337540 ", "Title": "Wrong Oracle Table History Data in Service Download", "Component": "BC-CCM-MON-ORA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1337540 ", "URL": "/notes/1337540 ", "Title": "Wrong Oracle Table History Data in Service Download", "Component": "BC-CCM-MON-ORA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1337540 ", "URL": "/notes/1337540 ", "Title": "Wrong Oracle Table History Data in Service Download", "Component": "BC-CCM-MON-ORA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "710", "ValidTo": "710", "Number": "1337540 ", "URL": "/notes/1337540 ", "Title": "Wrong Oracle Table History Data in Service Download", "Component": "BC-CCM-MON-ORA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "711", "ValidTo": "711", "Number": "1337540 ", "URL": "/notes/1337540 ", "Title": "Wrong Oracle Table History Data in Service Download", "Component": "BC-CCM-MON-ORA"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}