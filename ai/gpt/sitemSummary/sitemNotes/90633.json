{"Request": {"Number": "90633", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 407, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014535812017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=597303065F36610158CB618EC3D5DBE1"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "90633"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.01.2000"}, "SAPComponentKey": {"_label": "Component", "value": "LO-LIS-DC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Data Collection"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Logistics - General", "value": "LO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Logistics Information System", "value": "LO-LIS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO-LIS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Collection", "value": "LO-LIS-DC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO-LIS-DC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "90633 - New units in LIS"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>In the Logistics Information System, it is possible to define separate key figures and to update them in information structures. This is achieved by enhancing a communication structure or a reference structure in the respective user part or by means of an Append structure (the latter option is recommended).<br />New key figures often have also <B>new</B> units, which are then included in the communication structure. During the generation of the information structures and update programs with these new key figures and units, errors occur. Then, in the log, missing entries in table TMC6 and TMC24 are mentioned.<br />ATTENTION: It is only a problem if new units have been appended to the existing communication structure by using the APPEND technique. If the log refers to these entries but has nothing to do with the new units, the reference is the incorrect one.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>MC21, MC22, MC24, MC25, M2108</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The reason for the errors, as stated in the message, is missing table entries in control tables TMC6 and TMC24.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>In the past, this could only be solved with a work-intensive maintenance of these two control tables. Two programs which carry out this maintenance are available as of Release 3.1I and 4.0B. RMCSUNIT creates the control table entries, RMCSUDEL deletes them again for a unit. As of Release 4.5A, this function is stored in Customzing.<br />You can download these programs for earlier releases from the Sapserv computers. You can find them in directory<br />/general/R3server/abap/note.0090633<br />Caution: Do not include new units in reference structures (as for example MCFKENNZ) but always in communication structures! You can avoid complications this way. Only units that are assigned to key figures that means which are used as reference in the Data Dictionary are taken into account.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D028886)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "94457", "RefComponent": "SD-IS", "RefTitle": "SIS: \"Error determining local currency:...\" drng. setup", "RefUrl": "/notes/94457"}, {"RefNumber": "491307", "RefComponent": "LO-LIS-DC", "RefTitle": "Update rules: missing TMC24 entries", "RefUrl": "/notes/491307"}, {"RefNumber": "490672", "RefComponent": "LO-LIS-DC", "RefTitle": "Impossible to delete user-defined LIS units", "RefUrl": "/notes/490672"}, {"RefNumber": "436805", "RefComponent": "LO-LIS", "RefTitle": "LIS: Units in LIS", "RefUrl": "/notes/436805"}, {"RefNumber": "419866", "RefComponent": "LO-LIS-DC", "RefTitle": "Incorrect LIS units in the customer name space", "RefUrl": "/notes/419866"}, {"RefNumber": "405965", "RefComponent": "LO-LIS-DC", "RefTitle": "Documentation \"maintain units\"", "RefUrl": "/notes/405965"}, {"RefNumber": "396064", "RefComponent": "IS-U-IS-ST-SA", "RefTitle": "LIS: Units of measurement sometimes missing with quantities", "RefUrl": "/notes/396064"}, {"RefNumber": "139586", "RefComponent": "SD-BF-AC", "RefTitle": "Allocations: Update with the material availability date", "RefUrl": "/notes/139586"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "94457", "RefComponent": "SD-IS", "RefTitle": "SIS: \"Error determining local currency:...\" drng. setup", "RefUrl": "/notes/94457 "}, {"RefNumber": "419866", "RefComponent": "LO-LIS-DC", "RefTitle": "Incorrect LIS units in the customer name space", "RefUrl": "/notes/419866 "}, {"RefNumber": "436805", "RefComponent": "LO-LIS", "RefTitle": "LIS: Units in LIS", "RefUrl": "/notes/436805 "}, {"RefNumber": "491307", "RefComponent": "LO-LIS-DC", "RefTitle": "Update rules: missing TMC24 entries", "RefUrl": "/notes/491307 "}, {"RefNumber": "405965", "RefComponent": "LO-LIS-DC", "RefTitle": "Documentation \"maintain units\"", "RefUrl": "/notes/405965 "}, {"RefNumber": "490672", "RefComponent": "LO-LIS-DC", "RefTitle": "Impossible to delete user-defined LIS units", "RefUrl": "/notes/490672 "}, {"RefNumber": "396064", "RefComponent": "IS-U-IS-ST-SA", "RefTitle": "LIS: Units of measurement sometimes missing with quantities", "RefUrl": "/notes/396064 "}, {"RefNumber": "139586", "RefComponent": "SD-BF-AC", "RefTitle": "Allocations: Update with the material availability date", "RefUrl": "/notes/139586 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30E", "To": "31I", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}