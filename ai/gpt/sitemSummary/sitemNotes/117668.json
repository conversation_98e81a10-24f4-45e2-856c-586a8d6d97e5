{"Request": {"Number": "117668", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 579, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000117668?language=E&token=BA9893AC13321B643F141C7105FBF188"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000117668", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000117668/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "117668"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 95}, "Recency": {"_label": "Currentness", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade Info"}, "Priority": {"_label": "Priority", "value": "Correction with High Priority"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "14.10.2004"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-RDM"}, "SAPComponentKeyText": {"_label": "Component", "value": "README: Upgrade Additions"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Upgrade", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "README: Upgrade Additions", "value": "BC-UPG-RDM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-RDM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "117668 - Additions to upgrade to 4.5B"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=117668&TargetLanguage=EN&Component=BC-UPG-RDM&SourceLanguage=DE&Priority=02\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/117668/D\" target=\"_blank\">/notes/117668/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>There is an error in the guide or in the upgrade procedure.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3>\r\n<p>Update migration release upgrade release maintenance level R3up PREPARE</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3>\r\n<p>*</p>\r\n<h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3>\r\n\r\n<p><br /><strong>ATTENTION: This SAP Note is constantly updated.</strong><br /><strong>Therefore, read it again immediately before the upgrade.</strong> <br /><br /><br /><strong>What can you expect from this note?</strong> </p>\r\n<ul>\r\n<li>It lists problems that may occur during the upgrade of the R/3 System and provides information about their solutions. For this purpose, reference is usually made to other SAP Notes.</li>\r\n</ul>\r\n<ul>\r\n<li>The main purpose of this SAP Note is to prevent data loss, upgrade standstill, and long runtimes.</li>\r\n</ul>\r\n<ul>\r\n<li>Only database-independent issues are handled.</li>\r\n</ul>\r\n<p><strong>What can you not expect from this note?</strong> <br />Problems after the upgrade are only handled if they are caused directly by the upgrade tools.<br /><br /><br /><strong>Which notes do you also need to prepare for the upgrade?</strong><br /><br />This depends on the functionality you are using. In detail, you require one or more of the following SAP Notes:<br /><br /><br />Short text .............................................. Note Number<br />----------------------------------------------------------------------<br />Additions to upgrade to 4.5B  ADABAS for R/3 ................. 140431<br />Additional information about upgrading to 4.5B  DB2/400 ........................ 141661<br />Additional information about upgrading to 4.5B  DB2 common server .............. 141732<br />Additional information about upgrading to 4.5B  DB2 for OS/390 ................. 132982<br />Enhancements upgrade to 4.5B  Informix ....................... 140472<br />Additions upgrade to 4.5B  MS SQL Server .................. 139214<br />Additions to upgrade to 4.5B  Oracle ......................... 142593<br />----------------------------------------------------------------------<br />Current note for language import 4.5B .................... 142732<br />----------------------------------------------------------------------<br />Additions to upgrade to 4.5B  online documentation .................... 143558<br />----------------------------------------------------------------------<br />Additions to upgrade to 4.5B  Ready-to-Run R/3 ............... 111601<br />----------------------------------------------------------------------<br />Enhancements upgrade for MSCS 4.5B ............................ 144031<br />----------------------------------------------------------------------<br /><br /><br /><br /><br /><strong>             Contents<br /></strong><br /><br />I/ ...... R3up keyword<br />II/ ..... Important General Remarks<br />III/ .... Correction of the guides<br />IV/ ..... Error on the CD-ROM<br />V/ ...... Avoiding data loss, upgrade standstill, and planned<br />          Runtimes<br />VI/ ..... Problems with the Upgrade Assistant<br />VII/ .... Problems in the PREPARE and upgrade phases<br />VIII/ ... Problems after the upgrade<br />IX/ ..... Temporal synopsis<br /><br /><br /><br /><br /><strong>I/     R3up keyword<br /><br /></strong><br />------------------------&lt; D021848 17/SEP/98 >-------------------------<br />* R3up keyword (to be entered when R3up is called for the first time):  67644<br />----------------------------------------------------------------------<br /><br /><br /><br /><strong>II/    Important general remarks<br /><br /></strong><br />------------------------&lt; D003551 17/DEC/03 >-------------------------<br /><strong>Cannot upgrade to 4.5B with EXT kernel</strong><br />If you use the EXT kernel on the source release, you cannot upgrade to target release 4.5B. Instead, upgrade to a higher release. If this is not possible, contact SAP Support.<br /><br />------------------------&lt; D034624 05/MAR/02 >-------------------------<br /><strong>Error in qRFC version 6.10.043</strong><br />When you load transaction data from an SAP system into a BW system in delta mode, data inconsistencies may occur with qRFC Version 6.10.043. If you use this function, you must exceed or fall below a certain support package level when upgrading the source system.<br />For more information, see <strong>SAP Note 498484</strong>.<br /><br />------------------------&lt; D022030 22/DEC/00 >-------------------------<br /><strong>Replacement of the SAP kernel</strong><br />When the SAP kernel is exchanged, the contents of the kernel<br />Directory deleted. For security reasons, you should therefore<br />create a backup copy of the directory for the upgrade.<br />For more information, see the PREPARE log CHECKS.LOG.<br /><br />------------------------&lt; D028597 22/MAR/00 >-------------------------<br /><strong>Inclusion of LCPs and Hot Packages in the upgrade</strong><br />To enable the inclusion of Support Packages in the PREPARE phase BIND_PATCH<br />is correct, see SAP Note 208509.<br /><br />------------------------&lt; D019989 10/Oct/99 >-------------------------<br /><strong>Inclusion of LCPs and Hot Packages in the upgrade</strong><br />Error when including Support Package:<br />Refer to SAP Note 182064.<br />To include LCPs or Hot Packages in the upgrade, you may require a new version of the report RSSPAM13. You can obtain this new version by importing the latest SPAM update. The import of the SPAM update must currently be carried out before the start of the upgrade (prepare). If you have already started the Prepare script, you must reset it and start it again. To do this, refer to the section &quot;Reset Prepare&quot; in the chapter &quot;The Prepare Script&quot; in the upgrade guide.<br />For more information about including LCPs and Hot Packages in the upgrade, see Note 99300.<br /><br />------------------------&lt; D019144 10/MAI/99 >-------------------------<br />For <strong>First Customer Shipment (FCS) only:</strong><br /><br />As of week 20/1999, an improved version of 4.5B is sent (4.5B General Availability). If you still want to implement the FCS version of 4.5B, SAP <strong>Note 151833</strong> is used as a supplementary SAP Note for the upgrade instead of this SAP Note.<br /><br /><br />------------------------&lt; D020738 25/MAR/99 >-------------------------<br />Special Features for <strong>SAP Retail</strong> Customers<br /><br />A distinction must be made between the following cases:</p>\r\n<ul>\r\n<li>UPGRADE OF R/3 RETAIL RELEASE 1.2Bx ON R/3 RELEASE 4.5B:<br />Caution: This only affects customers who have the R/3 Retail release<br />1.2Bx are.<br />You have R/3 Retail Release 1.2Bx (1.2B, 1.2B1 or 1.2B2) and<br />want to upgrade to R/3 Release 4.5B.<br />Proceed as follows:<br />Step 1: Upgrade from R/3 Retail Release 1.2Bx to R/3 Release 4.0B.<br />In this step, your system is automatically flagged as SAP Retail (XPRA XPRASETRETAILSYSTEM).<br />Step 2: Upgrade from R/3 Release 4.0B to R/3 Release 4.5B</li>\r\n</ul>\r\n<p>           CAUTION:<br />A direct upgrade from R/3 Retail Release 1. 2Bx to R/3 Release 4.5B is not supported.</p>\r\n<ul>\r\n<li>In all other cases:<br />If you are already an SAP Retail customer or want to become a 4.5B customer, additional actions are required in accordance with <strong>SAP Note 138360</strong>.</li>\r\n</ul>\r\n<p>------------------------&lt; D022030 23/SEP/99 >-------------------------<br /><strong>For HR customers only</strong> <br />As of Release 4.5B, HR LCPs (legal change patches) are imported independently of Hot Packages. For more information, see <strong>SAP Note 135041</strong>.<br /><br />------------------------&lt; D025323 29/FEB/00 >-------------------------<br /><strong>Only if you use the ADD-ON component &quot;DART&quot;:&lt;/Z1></strong><br /><strong>Refer to SAP Note 141957.</strong><br /><br />----------------------------------------------------------------------<br /><br /><br /><strong>III/  Correction of guides<br /><br />------------------------&lt; D022030 12/OCT/00 >-------------------------</strong><br />Changed Note Number for SAP Internet Solution<br />(Section in the guide: Prerequisites for the SAP Internet Solution) The note for information about new versions of SAP@Web Studio and ITS has changed. The new SAP Note number is 197746.<br /><br />------------------------&lt;D025323 03/OCT/99>---------------------------<br />Chapter: Preparing for the Upgrade<br />Section: The Prepare Program<br />Subitem: Parameter Entry<br />Parameter: Host name of the batch server<br /><br />Here, you must specify a batch server on which background jobs of the<br />class C can be processed. This specifies batch servers that<br />can only process background jobs of class A, the following occurs in the phase<br />TOOLIMP to errors.<br /><br />-------------------------&lt;D023648 20/AUG/99>--------------------------<br /><strong>HP-UX 11.00 64bit only:</strong><br /><br />In the guide &quot;Upgrade to Release 4.5B: UNIX&quot; on page 3-42, the<br />Upgrade of the front-end software. For HP-UX 11.00 64bit<br />the parameter &lt; OS > must be replaced by HP11_32 in step 3.<br /><br />-------------------------&lt;D020432 15/APR/99---------------------------<br /><strong>NT alpha only:</strong><br />In the &quot;Upgrade to Release 4.5B: Windows NT&quot; guide, see S2-18.<br />In the description of the Internet Explorer installation,<br />The path specification is missing. The Internet Explorer for NT alpha is located on the<br />&#39;SAP Presentation CD&#39; under  &lt;CD-DRIVE>: \\MS_IE4\\ALPHA\\EN<br /><br /><br />-------------------------&lt;D019144 10/MAI/99---------------------------<br /><strong>For UNIX only:</strong> <br /><br />Error in the guide &quot;Upgrade to Release 4.5B: UNIX&quot; (&quot;Upgrading to Release 4.5B: UNIX&quot;):<br /><br />Wrong note number for &quot;Installing a Dialog Instance/ Gateway&quot; on pages 5-24 and D-3:<br /><br />false:    85254<br />correct:  137478<br /><br />----------------------------------------------------------------------<br /><br /><br /><strong>IV/    Error on CD-ROM<br /><br /></strong><br />----------------------------------------------------------------------<br /><br /><br /><br /><strong>V/     Avoid data loss, upgrade standstill, and long runtimes</strong><br /><br />------------------------&lt; D031049 14/OCT/04 >-------------------------<br /><strong>Start-Rel. 4.0B: Project-Related Incoming Orders</strong><br />If you use the advance solution for project-related incoming orders published with SAP Note 118780, you must modify data elements before the upgrade.<br />For more information, see <strong>SAP Note 369542</strong>.<br /><br />------------------------&lt; D020839 14/OCT/04 >-------------------------<br /><strong>Component PS-ST on SAP DB / MS SQL: Table MLST</strong><br />If you have changed indexes of tables in accordance with SAP Note 82252, you must undo these changes before the upgrade.<br />For more information, see <strong>SAP Note 191270</strong>.<br /><br />------------------------&lt; D030022 14/OCT/04 >-------------------------<br /><strong>Start Rel. 3.1I: Address Data Loss</strong><br />If you use the &quot;Plant Maintenance&quot; component (PM-WOC-MN) and use the report RSXADR05 to convert the addresses before the upgrade from source release 3.1I, you must refer to the information in <strong>SAP Note 360929</strong>.<br />Otherwise, address data may be lost during the upgrade.<br /><br />------------------------&lt; D001330 14/OCT/04 >-------------------------<br /><strong>Measures for optimizing the upgrade runtime</strong><br />For information about optimizing the upgrade runtime, see <strong>collective note 76431</strong>.<br /><br />----------------------&lt; D030326 17/JUL/02 >---------------------------<br /><strong>Source Release 4.0B: Termination in various phases when creating a job variant by R3up</strong><br />If you have imported Support Packages 74, 75, 76, or 77 for Release 4.0B, terminations may occur in various upgrade and PREPARE phases. The corresponding logs indicate that no variant could be created by R3up. In this case, refer to <strong>SAP Note 533777</strong>.<br />This error no longer occurs as of Support Package 78 for Release 4.0B.<br /><br />----------------------&lt; D030326 17/JUL/02 >---------------------------<br /><strong>Including Hot Packages 53 to 55</strong><br />You can only include Support Packages 53 to 55 in the upgrade if you also include Support Package 56.<br />If an error occurs, see <strong>SAP Note 533777</strong>.<br /><br />------------------------&lt; D022803 16/APR/02 >-------------------------<br /><strong>Do not include Support Package 51 as the highest Support Package.</strong><br />If you include Hot Package 51 as the highest Hot Package in the upgrade, no tables can be adjusted using transaction SPDD. In this case, you receive a syntax error in the program SAPLTMSC.<br />For more information, see <strong>SAP Note 512241</strong>. Under &quot;References to Support Packages&quot;, you can find the relevant Support Package that contains the solution to the problem.<br /><br />------------------------&lt; D024084 27/JUL/01 >-------------------------<br /><strong>Only for MM customers (inventory management) and only for source release 3.0x/3.1x:</strong><br />During the upgrade, certain Customizing settings of transaction types are lost. The settings are reset to the SAP standard settings.<br />For more information, see <strong>SAP Note 86627</strong>.<br /><br />------------------------&lt; D025323 23/FEB/01 >------------------------<br />To avoid table field and data loss, for some<br />source releases, a certain minimum number of Support Packages in the<br />Include Upgrade. This affects the source releases:<br />   31I  as of package SAPKH31I68<br />  40B  as of package SAPKH40B58<br />   45B   as of package SAPKH45B35<br />   46B<br />For more information, see <strong>SAP Note 383126</strong>.<br /><br />------------------------&lt; D029407 02/FEB/01 >------------------------<br /><strong>Only for HR customers with payroll for the construction industry</strong><br />During an upgrade with source release 3.1I, all entries in the<br />Table T5DB4 deleted. Save the data before the upgrade with<br />Database tools and reload them again after the upgrade.<br />For more information, see <strong>SAP Note 156568</strong>.<br /><br />-------------------&lt; D025323 31/JAN/01 >------------------------------<br /><strong>Alternate name for the upgrade directory:</strong><br />Note that the upgrade directory &lt;DIR_PUT> does not<br />may be longer than 18 bush rods.<br />In the case of longer directory names, the following occurs in the phase RUN_PRELDIST:<br />a termination occurs.<br /><br />-------------------&lt; D024777 20/NOV/00 >------------------------------<br /><strong>Incorrect conversion of cost center assignments</strong><br />If you use Organizational Management, you must make the following settings in the<br />Include at least LCP 26 upgrade or before running XPRA RHU40C01<br />Implement the correction instructions from SAP Note 214976.<br />For more information, see <strong>SAP Note 307803</strong>.<br /><br /><strong>-------------------&lt; D019307 25/AUG/00 >------------------------------</strong><br /><strong>For source releases lower than 4.5: Preliminary conversion of addresses</strong><br />If the data volume of the addresses is large, it is recommended that you<br />Upgrade, start report RSXADR21 for address conversion.<br />For more information, see <strong>SAP Note 97032</strong>.<br /><br />-------------------------&lt;D023536 11/APR/00>--------------------------<br /><strong>Loss of customer-specific logical databases</strong><br />Due to an incorrect selection, customer-specific logical databases are lost during the upgrade with source release 4.0B. In RDDIT006, the system incorrectly determines that the LDBA does not exist and is therefore not entered in the SAPKCCC45B piece list. This affects all customer-specific LDBAs that were created in 4.0.<br />To avoid this error, import Support Package 42 before the upgrade or implement the corrections in accordance with SAP Note 213477.<br /><br /><br />-------------------------&lt;D025323 21/SEP/99>--------------------------<br /><strong>Only for FI Customers:</strong> Default Values Document Type/Posting Key<br />Problem: The default values for the document type and posting key,<br />        that uses transaction code to move between F-01 and F-99<br />         are lost during the upgrade.<br /><br />Preliminary solution proposal:<br />      1. Save the data before the upgrade by printing the<br />         content. To do this, use transaction SE16. Enter<br />        table name TSTCP. In the TCODE field, enter F-01 to F-99.<br />        field.<br />      2. Enter the data manually again after the upgrade.<br />         To do this, use report RFTSTCP0.<br />      As soon as an automatic method is found, it will be<br />      are described.<br /><br /><br />-------------------------&lt;D025323 08/SEP/99>--------------------------<br /><strong>NT only:</strong> R/3 instance cannot be stopped if the<br />        host name contains an underscore (_).<br /><br />Solution: This is an error in &#39;sapsrvkill&#39;,<br />        implement SAP Note 167873.<br /><br /><br />-------------------------&lt;D025323 12/JUL/99>--------------------------<br />Program terminations may occur in the PREPARE module IMPORT.<br /><br />Problem: When you import the new table formats, the dependent<br />         report loads not invalidated.<br /><br />Solution:  Use a tp with patch level 116 or higher,<br />         see SAP Note 160614.<br /><br /><br />--------Enhanced--------&lt;D019132 05/JAN/2000>------------------------<br />--------Enhanced--------&lt;D019132 28/SEP/99>--------------------------<br />--------changed---------&lt;D019132 27/SEP/99>--------------------------<br />--------changed---------&lt;D019132 16/JUN/99>--------------------------<br />-------------------------&lt;D019132 14/JUN/99>--------------------------<br />Only if you integrate Hot Packages of 45B in the upgrade:<br /><strong>Shutdown in phase ADOIM_45B with Hot Package 02</strong><br /><br />Problem: No application defined in Hot Package 02 of 4.5B<br />         objects (ADOs), but the system incorrectly<br />        assumes that ADOs are contained in request SAPKH45B02.<br /><br />Solution:  This is contained in <strong>SAP Note 157201</strong>.<br /><br />Supplement:<br />A related problem occurs if, up to Hot Package 07 or 08,<br />(<strong>SAP Note 173276</strong>). Choose a higher or<br />a lower patch level in the phase BIND_PATCH.<br /><br /><strong>Data loss in phase ADOIM_45B with Hot Package 12..14</strong><br /><br />Problem: Incorrect correction in Hot Package 12 of 45B<br />        , which first contains too many error messages FL707<br />        , but as a result, the import of all<br />        Transaction objects R3OB CHDO.<br /><br />Solution:  Bind the Hot Packages of 45B only up to a maximum of 11<br />         or up to a minimum of 15. If you are up to level 12, 13, or 14<br />        , see <strong>SAP Note 192009</strong>.<br /><br /><br />-------------------------&lt;D000706 10/JUN/99>--------------------------<br />Avoid termination in phase DIFFEXPCUST, DIFFEXPGEN or DIFFEXPMOD<br /><br />Problem: With 4.5B, an obsolete version of R3trans was erroneously<br />        . This can lead to a termination in the DIFFEXP*<br />        phases of R3up.<br /><br />Solution:  This is contained in <strong>SAP Note 156314</strong>. <br /><br /><br />-------------------------&lt;D019132 19/APR/99>--------------------------<br />Problems with sapdba tables during upgrade<br /><br />Problem: Errors may occur with sapdba tables in the phases<br />         PCON_&lt;rel> or TABIM_&lt;rel>.<br /><br />Solution:  If you want to perform a modification adjustment using transaction SPDD<br />         (phase ACT_&lt;rel>), we recommend that you<br />         use <strong>SAP Note 147994</strong> as a preventive event.<br /><br />         If this is not possible for you and the upgrade in<br />         PCON_&lt;rel> or TABIM_&lt;rel>, you must<br />         use SAP Note 147994.<br /><br /><br />-----------changed------&lt;D021943 09/JUN/99>--------------------------<br />-------------------------&lt;D019132 16/APR/99>--------------------------<br />Only for source release 4.5A:<br /><br />The following <strong>data is lost during the upgrade:</strong></p>\r\n<ul>\r\n<li>Originals in the SAP namespace</li>\r\n</ul>\r\n<ul>\r\n<li>Legacy System Migration Workbench 1.0</li>\r\n</ul>\r\n<ul>\r\n<li>Legacy System Migration Workbench 1.5</li>\r\n</ul>\r\n<p>Problem: Objects in SAP and J partner namespaces that are in the<br />         own system landscape have been developed, as of 4.5A<br />         no modification information and is stored in the repository<br />         transfer is not taken into account. A similar problem is<br />         if the last change to the object in question<br />         was caused by a transport that was created from a<br />         system without modification information.<br /><br />         Of this loss, the following are NOT with the R/3 standard<br />         delivered tools also affected:<br />         Legacy System Migration Workbench 1.0 (LSMW 1.0)<br />        Legacy System Migration Workbench 1.5 (LSMW 1.5)<br /><br />Analysis: For originals in the SAP namespace:<br />         Check in a system with partner development<br />         the log CUSTEXP.sid, which is stored in the PREPARE module<br />         &quot;Modification Support&quot;. You will find the<br />        Message TG186 for all affected objects.<br /><br />        For LSMW 1.0:<br />        Check whether the transport U40K900015 has been imported.<br />        For LSMW 1.5:<br />         Check whether the transport MPFK900015 has been imported.<br /><br />Solution:  Change after the PREPARE module &quot;Import&quot; in the ABAP include<br />         LSUGIF02 the constant LC_NO_STD from &#39;X&#39; to &#39; &#39;.<br /><br />-------------------------&lt;D019132 22/JUN/99>--------------------------<br />Only for source release 4.5A and 4.0B<br /><br /> <strong>Loss of tables M_% in prefix namespaces</strong><br /><br />Problem: e.g. table /ABC123/M_EXAMPLE during upgrade<br />        will be lost.<br /><br />Analysis: Before the upgrade, check in transaction SE11 via<br />         the F4 help with the pattern &quot;/*/M*&quot; to determine whether such tables<br />         in your system. If there are no tables that<br />         correspond to the pattern /*/M_* and in a customer-specific<br />         development class, there is no need to take any action.<br />        (this is the normal case).<br /><br />Solution:  To correct the error, get the file from sapservX.<br />            general/R3server/abap/note.0117668/LSUGIF02.45B<br />        (binary mode) and import it after PREPARE (!) with<br />            R3trans -i LSUGIF02.45B<br />         (Caution: Test the connect to the correct DB<br />         with &quot;R3trans -d&quot;. Do not import the transport under any circumstances.<br />         before executing the PREPARE module &quot;Import&quot;).<br />         Then use transaction SE38 to generate the program<br />         SAPLSUGI. Check whether LC_NO_STD has been changed.<br />         and repeat this change if necessary (only<br />         for source release 4.5A, see previous section).<br /><br /><br />-------------------------&lt;D001634 25/MAR/99>--------------------------<br />Only for source releases lower than 3.1H:<br /><br />Problem: Table T510Q may contain in field T510Q-BETRG<br />         non-numeric entries in clients other than &#39;000&#39;.<br />        During the table conversion, these would become a<br /> <strong>termination of the upgrade in phase PCON_45B</strong>.<br />        The following runtime error would occur: CONVT_NO_NUMBER<br /><br />Solution: Obtain <strong>Note 83728 before the upgrade.</strong><br />        Follow the instructions there to avoid the termination.<br /><br /><br />------------------------&lt; D019780 25/MAR/99 >-------------------------<br />Only for source release 3.0x/3.1x:<br /><br />Symptom: workload statistics (ST03) are deleted during upgrade<br /><br />Solution:  If you use the workload statistics from Release 3.0x/3.1x<br />        after the upgrade, proceed before the<br />         upgrade as described in <strong>Note 115364</strong>.<br /><br /><br />------------------------&lt; D022600 29/MAR/99 >-------------------------<br /><strong>Only for HR customers with source release 3.0x/3.1x:</strong><br /><br />Problem: During the upgrade, the person group key in the table<br />         DUV of cluster RD emptied.<br /><br />Solution:  is described in <strong>note 141319</strong><br /><br /><br />------------------------&lt; D020694 25/MAR/99 >------------------------<br /><strong>For Retail Release 1.2x only:</strong><br /><br />Issue:<br />By changing the field name of one of the key fields of the module text table WSOT, the entries of this table are transferred only incompletely during the upgrade. As a result, problems may occur during the maintenance of manual assortment modules, among other things.<br /><br />Solution: Before the upgrade, proceed as described in <strong>Note 114346</strong>.<br /><br /><br />-------------------------&lt;D019132 19/APR/99---------------------------<br /><strong>Only for source release 3.x</strong><br />When you include patches in the upgrade, the following often occurs:<br />Error messages in the PREPARE module Read CD. To avoid this<br />Proceed as described in SAP <strong>Note 153187</strong>.<br /><br /><br />---------------------------------------------------------------------<br /><br /><br /><strong>VI/    Problems with the Upgrade Assistant<br /><br /><br /></strong><br />Short text ............................................... Note Number<br />-----------------------------------------------------------------------<br /><strong>For MS Internet Explorer only:</strong><br />Upgr. Start Assistant from MS Internet Explorer 4.0 .......... 137590<br />-----------------------------------------------------------------------<br /><strong>Only if the Upgrade Assistant server is running on WindowsNT:</strong><br />R/3 Upgrade Assistant: PREPARE remains .................. 137587<br />-----------------------------------------------------------------------<br /><strong>For AIX only:</strong><br />R/3 Upgrade Assistant: No connection to server .............137594<br />-----------------------------------------------------------------------<br /><strong>For Digital UNIX only:</strong><br />No upgrade assistant on Digital UNIX 3.2 ....................137599<br />-----------------------------------------------------------------------<br /><strong>For HP-UX only:</strong><br />Upgrade Assistant: Stack Overflow Exception  ...................137607<br />----------------------------------------------------------------------<br /><br /><br /><br /><br /><strong>VII/   Problems in the PREPARE and upgrade phases<br /><br /></strong><br />This section contains known problems that you cannot prevent by preventive measures. These problems can occur only under very specific conditions or prerequisites.<br /><br /><br /><strong>PREPARE Module Import</strong> <br />-----------------------------------------------------------------------<br />Phase: TOOLIMPD2<br />Note: 98198<br />Description: Error in upgrade phase TOOLIMPD1, TOOLIMPD2<br />-----------------------------------------------------------------------<br /><br /><strong>PREPARE module CD reading (CD read)</strong> <br />-----------------------------------------------------------------------<br />Phase: BIND_PATCH<br />Description: The extension of the upgrade runtime by means of patches issued by R3up is specified by a factor of 6 too large.<br />----------------------------------------------------------------------<br />Phase: IS_MERGE<br />Note: 98673<br />Description: Add-on Installation on Rel. 3.x to PREPARE 4.x<br />-----------------------------------------------------------------------<br /><br /><strong>PREPARE module activation checks</strong> <br />-----------------------------------------------------------------------<br />Phase: JOB_RADDRCHK<br />Note: 140280<br />Description: TG063: Name conflict for name &amp; will .....<br />-----------------------------------------------------------------------<br />Phase: JOB_RADDRCHK<br />Note: 133017<br />Description: Namespace Conflicts b. Upgrade to Release /4.5<br />-----------------------------------------------------------------------<br />Phase: ACTREF_CHK<br />Note 140313<br />Description: Customer Ref. to Deleted SAP Data Element<br />-----------------------------------------------------------------------<br /><br /><br /><strong>Upgrade Phases</strong> <br />-----------------------------------------------------------------------<br />Phase: all phases<br />Note: 138939<br />Description: ReliantUNIX: Upgrade or import hangs<br />-----------------------------------------------------------------------<br />Phase: all phases<br />Note 138284<br />Description: Spool: Invalid specification for output device<br />-----------------------------------------------------------------------<br />Phase: DDIC_45B<br />Note: 168861<br />Description: Do Not Ignore Errors<br />-----------------------------------------------------------------------<br />Phase: ACT_45B/SPDD<br />Note: 74831<br />Description: Shipments QE1K900002, TCVK900029, EWSK900052<br />-----------------------------------------------------------------------<br />Phase: ACT_45B/SPDD<br />Note: 147610<br />Description: Table T5UT4 is offered for adjustment in SPDD<br />-----------------------------------------------------------------------<br />Phase: PATCH_STATUS<br />Note: 208509<br />Description: Error in upgrade phase PATCH_STATUS<br />-----------------------------------------------------------------------<br />Phase: PCON_45B<br />Note: 190527<br />Description: Warnings/terminations for table EWUPAK<br />-----------------------------------------------------------------------<br />Phase: PCON_45B<br />Note: 73999<br />Description: Upgrade phase PCON_&lt;REL>: TG450 to TG453<br />-----------------------------------------------------------------------<br />Phase: PCON_45B<br />Note 140071<br />Description: PCON_&lt;rel>: R3up reports terminations in the R/3 System<br />-----------------------------------------------------------------------<br />Phase: JOB_RDDNTPUR<br />Note: 394331<br />Description: <strong>Only if you use the Note Assistant</strong> Carry out the new installation of the Note Assistant in the phase SPAUINFO before the modification adjustment.<br />-----------------------------------------------------------------------<br />Phase: XPRAS_45B<br />Note: 145794<br />Description: 990302 LONGPOST.LOG Error filling tbl &quot;STACUST&quot;<br />-----------------------------------------------------------------------<br />Phase: XPRAS_45B<br />Note: 144675<br />Description: Error in XPRA RPUPTEXDIR50<br />-----------------------------------------------------------------------<br />Phase: XPRAS_45B<br />Note 168270<br />Description RSXADR02 short dump DBIF_RSQL_INVALID_RSQL<br />-----------------------------------------------------------------------<br />Phase: XPRAS_45B<br />Note: 157968<br />Description: RFMXPR22 is too slow (betr. CO Funds Reservations)<br />-----------------------------------------------------------------------<br />Phase: XPRAS_45B<br />Note: 174170<br />Description: HR OED: Conversion of T510Q with XPRA RPUT510Q<br />-----------------------------------------------------------------------<br />Phase: XPRAS_45B<br />Note: 206328<br />Description: RCCUVLXP variant table content incorrect<br />-----------------------------------------------------------------------<br /><br /><br /><br /><strong>VIII/  Problems after the upgrade<br /><br /></strong><br />This section contains known problems that are caused by the upgrade tools and that you cannot prevent by preventive measures. These problems can occur only under very specific conditions or prerequisites.<br /><br /><br />----------------------------------------------------------------------<br /><strong>Linux: Apply new saposcol version</strong><br />For more information, see <strong>SAP Note 19227</strong>.<br />----------------------------------------------------------------------<br /><strong>ICNV</strong><br />Incremental conversion of tables CDCLS and EDIDOC<br />works due to a kernel error only as of a<br />certain kernel patch level. Start the data transfer.<br />after the upgrade before the appropriate kernel patch level<br />is reached.<br />For more information, see <strong>SAP Note 141465</strong>.<br />----------------------------------------------------------------------<br /><strong>Contents of tables EWUWAERTP and EWUWAERTPT are missing</strong><br />After the upgrade, check whether the tables in your<br />system. If not, get the data<br />from sapserv and import the request.<br />For more information, see <strong>SAP Note 315568</strong>.<br />-----------------------------------------------------------------------<br /><strong>SPAU: &quot;Mark for Transport&quot; and Hot Packages</strong><br />For more information, see <strong>SAP Note 69990</strong>.<br />-----------------------------------------------------------------------<br /><strong>ReliantUNIX: saposcol version 32 or 64 bit</strong><br />For more information, see <strong>SAP Note 148926</strong>.<br />-----------------------------------------------------------------------<br /><strong>Source Release 3.0/3.1 and FI-SL: XPRA did not run</strong><br />For more information, see <strong>SAP Note 154430</strong>.<br />-----------------------------------------------------------------------<br /><strong>Local currency changeover:</strong><br />Euro: Upgrade 3.1I - 4.5B, control table entries<br />For more information, see <strong>SAP Note 176472</strong>.<br />-----------------------------------------------------------------------<br />Missing release notes for releases &lt;=4.0B<br />For more information, see <strong>SAP Note 174073</strong>.<br />-----------------------------------------------------------------------<br /><strong>Audit Information System</strong><br />This is already available in 3.1i or in other releases.<br />Imported Audit Information System (AIS) must be repeated for 4.5<br />imported.<br />For more information, see <strong>SAP Note 162971</strong>.<br />-----------------------------------------------------------------------<br /><strong>Loss of contents of table TRMSG</strong><br />When you include Support Package 28 in the upgrade, due to<br />a delivery error in this package English entries<br />deleted by mistake. After the upgrade, import the content of the<br />Table TRMSG for English from the language CD via<br />  Transaction SMLT -> Import -> Special Imports -> Individual Tables<br /><br /><strong>IX/    Temporal synopsis<br /><br /></strong><br />Date....Topic..Short Description<br />----------------------------------------------------------------------<br />OCT/14/04....V..Start-Rel. 4.0B: Project-related incoming orders<br />OCT/14/04....V..Component PS-ST on SAP DB/MS SQL: Table MLST<br />OCT/14/04....V..Start-Rel. 3.1I: Address data loss<br />OCT/14/04....V..Upgrade Runtime Optimization Measures<br />DEC/17/03...II..Cannot upgrade to 4.5B with EXT kernel<br />JUL/17/02....V..Termination when creating a job variant<br />JUL/17/02....V..Including Hot Packages 53 to 55<br />APR/16/02....V..Basis Support Package 51 not as highest SP<br />MAR/05/02...II..Error in qRFC Version 6.10.043<br />JUL/27/01....V..Only MM + Source Release 3.x: Loss of Transaction Types<br />JUL/20/01.VIII..Loss of contents of table TRMSG<br />JUN/12/01..VII..Error in JOB_RDDNTPUR when using Note Assistant<br />FEB/23/01....V..Enhancements Upgrade: Expiring Currencies<br />JAN/02/01....V..HR Construction Industry: Data Loss in Table T5DB4<br />DEC/22/00...II..Replacing the SAP Kernel<br />NOV/20/00....V..Incorrect conversion of cost center assignments<br />OCT/19/00.VIII..Linux:  Import new saposcol version<br />OCT/12/00..III..Changed Note Number for SAP Internet Solution<br />OCT/12/00.VIII..Incremental conversion of tables CDCLS and EDIDOC<br />AUG/25/00....V..For source releases lower than 4.5: Preliminary conversion of addresses<br />AUG/23/00.VIII..AIS not included in standard<br />JUL/07/00.VIII..Table EWUWAERTP delivered empty<br />APR/11/00....V..Loss of customer-specific logical databases<br />MAR/22/00...II &amp; VII..Error in upgrade phase PATCH_STATUS<br />MAR/13/00..VII..RCCUVLXP Content of variant table incorrect<br />FEB/28/00...II..Only for ADD-ON DART: Refer to Note 141957<br />DEC/23/99..VII..EMU: Warnings/terminations for table EWUPAK<br />DEC/23/99.VIII..Missing release notes for releases &lt;=4.0B<br />NOV/22/99..VII..Namespace conflicts b. Upgrade to Release 4.0/4.5<br />NOV/09/99...II..Including LCPs and Hot Packages in the Upgrade (2)<br />OCT/14/99...II..Including LCPs and Hot Packages in the Upgrade<br />OCT/12/99.VIII..Local currency changeover, euro<br />SEP/23/99...II..When you implement LCPs, refer to Note 135041.<br />SEP/21/99....V..Default values for document type/posting key<br />SEP/21/99..VII..HR OED: Conversion of T510Q with XPRA RPUT510Q<br />SEP/08/99..VII..RFMXPR22 too slow (amount CO Funds Reservations)<br />SEP/08/99....V..NT only: R/3 instance cannot be stopped<br />AUG/20/99.VIII..DDIC_45B: Phase must not be ignored<br />AUG/20/99..III..Only HP-UX 11.00 64bit: Use front end 32bit<br />JUL/12/99....V..Upgrade to 45B: TOOLIMPORT<br />JUN/10/99....V..Avoid termination in phases DIFFEXP*<br />JUN/09/99....V..LSMW 1.0 and LSMW 1.5 are lost during upgrade<br />MAY/28/99.VIII..FI-SL XPRA did not run correctly during upgrade from 3.0/3.1<br />MAY/10/99...I..FCS: As of now, SAP Note 151833 for upgrade enhancements<br />MAY/10/99..III..UNIX: Note 137478 Inst. Dialog Instance with R3SETUP<br />APR/27/99.VIII..saposcol Version 32 or 64-bit on ReliantUNIX<br />APR/19/99..VII..Table T5UT4 is offered for adjustment in SPDD<br />APR/19/99....V..Problems with sapdba tables during upgrade<br />APR/16/99....V..Originals in the SAP namespace are lost<br />APR/15/99..VII..XPRAS_45B: Error in XPRA RPUPTEXDIR50<br />APR/15/99..III..NT Alpha: Path Specification for Internet Explorer<br />APR/01/99..VII..XPRAS_45B: LONGPOST.LOG: Error filling table &quot;STACUST&quot;<br />MAR/29/99....V..HR when starting with 3.x: Person group key<br />MAR/29/99.VIII..SPAU: &quot;Mark for Transport&quot; and Hot Packages<br />MAR/29/99..VII..PREPARE/IS_MERGE: Add-on installation for the start release<br />MAR/25/99....V..SAP Retail 1.2x: Process SAP Note 114346 beforehand<br />MAR/25/99....V..Check of table T510Q before the upgrade<br />MAR/25/99....V..Start with 3.x: Backing Up Workload Statistics (ST03)<br />MAR/25/99..VII..ACT_45B/SPDD: QE1K900002, TCVK900029, EWSK900052<br />MAR/25/99..VII..PREPARE/JOB_RADDRCHK: If started with 3.0x/3.1x: TG063<br />MAR/25/99...II..SAP Retail: Special Features of the Upgrade<br />MAR/25/99..VII..PREPARE/ACTREF_CHK Customer Ref. to Deleted SAP Data<br />MAR/25/99..VII..PCON_45B: R3up reports terminations in R/3 system<br />MAR/25/99...VI..Upgr. Start Assistant from MS Internet Expl. 4.0<br />MAR/25/99...VI..R/3 Upgrade Assistant: PREPARE remains<br />MAR/25/99...VI..R/3 Upgrade Assistant: No connection to server<br />MAR/25/99...VI..NO R/3 Upgrade Assistant on Digital UNIX 3.2<br />MAR/25/99...VI..R/3 Upgrade Assistant: Stack Overflow Exception<br />MAR/25/99..VII..ReliantUNIX: Upgrade or import hangs<br />MAR/25/99..VII..Syslog/Spooler: Invalid specification for output device<br />MAR/25/99..VII..PREPARE/TOOLIMPD2: Termination with distribution error<br />MAR/25/99..VII..PCON_45B: Error message TG450 to TG453<br />SEP/17/98....I..R3up keyword<br />----------------------------------------------------------------------</p></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Owner                                                                                    ", "Value": "<PERSON><PERSON> (D025323)"}, {"Key": "Processor                                                                                          ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000117668/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "141661", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements for upgrade to 4.5B DB2/400", "RefUrl": "/notes/141661 "}, {"RefNumber": "162117", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements for upgrade to 4.6A DB2/400", "RefUrl": "/notes/162117 "}, {"RefNumber": "178823", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements for upgrade to 4.6B DB2/400", "RefUrl": "/notes/178823 "}, {"RefNumber": "202169", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements for upgrade to 4.6C DB2/400", "RefUrl": "/notes/202169 "}, {"RefNumber": "19227", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "Get latest saposcol", "RefUrl": "/notes/19227 "}, {"RefNumber": "197746", "RefComponent": "BC-FES-ITS", "RefTitle": "Maintenance Strategy: Internet Transaction Server (ITS)", "RefUrl": "/notes/197746 "}, {"RefNumber": "189229", "RefComponent": "BC-SRV-ARL-INT", "RefTitle": "Report for 4.5ff upgrade with open WIs to TS7869", "RefUrl": "/notes/189229 "}, {"RefNumber": "97032", "RefComponent": "SD-MD", "RefTitle": "Addresses: Advance Conversion for Release 4.5 / 4.6 / 4.7", "RefUrl": "/notes/97032 "}, {"RefNumber": "147134", "RefComponent": "LO-MD-BP-CM", "RefTitle": "XPRA RSXADR12: Termination when sorting/importing tables SADR", "RefUrl": "/notes/147134 "}, {"RefNumber": "138284", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "Spool: Invalid specification for output device", "RefUrl": "/notes/138284 "}, {"RefNumber": "140071", "RefComponent": "BC-UPG", "RefTitle": "PCON_<rel>: R3up reports terminations in the R/3 System", "RefUrl": "/notes/140071 "}, {"RefNumber": "512241", "RefComponent": "BC-CTS-TMS", "RefTitle": "Syntax error in program SAPLTMSC during upgrade", "RefUrl": "/notes/512241 "}, {"RefNumber": "369542", "RefComponent": "PS-REV-IO", "RefTitle": "CJA1: Upgrade from Release 4.0 advance solution to >= 4.5", "RefUrl": "/notes/369542 "}, {"RefNumber": "394331", "RefComponent": "BC-UPG-NA", "RefTitle": "Upgrade to 4.X: Error message for tables CWB*", "RefUrl": "/notes/394331 "}, {"RefNumber": "208509", "RefComponent": "BC-UPG", "RefTitle": "Error in upgrade phase PATCH_STATUS", "RefUrl": "/notes/208509 "}, {"RefNumber": "144031", "RefComponent": "BC-UPG", "RefTitle": "Enhancements to upgrade MSCS", "RefUrl": "/notes/144031 "}, {"RefNumber": "137590", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgr. Start Assistant from MS Internet Expl. 4.0", "RefUrl": "/notes/137590 "}, {"RefNumber": "140472", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to upgrade to 4.5B INFORMIX", "RefUrl": "/notes/140472 "}, {"RefNumber": "86627", "RefComponent": "MM-IM", "RefTitle": "Movement Types: Customizing for Release Upgrade", "RefUrl": "/notes/86627 "}, {"RefNumber": "142732", "RefComponent": "BC-CTS-LAN", "RefTitle": "Current note for language import 4.5B", "RefUrl": "/notes/142732 "}, {"RefNumber": "534826", "RefComponent": "LO-LIS-DC", "RefTitle": "LIS: Current Update Programs After Upgrade", "RefUrl": "/notes/534826 "}, {"RefNumber": "135041", "RefComponent": "BC-UPG-OCS", "RefTitle": "Separation of HR LCPs/Hot Packages as of Release 4.5B", "RefUrl": "/notes/135041 "}, {"RefNumber": "213477", "RefComponent": "BC-UPG-TLS", "RefTitle": "Logical databases lost after upgrade", "RefUrl": "/notes/213477 "}, {"RefNumber": "360929", "RefComponent": "PM-WOC-MN", "RefTitle": "Address data loss when executing RSXADR05", "RefUrl": "/notes/360929 "}, {"RefNumber": "191270", "RefComponent": "PS-ST", "RefTitle": "Activation of Indexes for Table MLST", "RefUrl": "/notes/191270 "}, {"RefNumber": "146440", "RefComponent": "BC-INS", "RefTitle": "R/3 4.5B Installation/Upgrade Japanese", "RefUrl": "/notes/146440 "}, {"RefNumber": "146441", "RefComponent": "BC-INS", "RefTitle": "R/3 4.5B Korean Installation/Upgrade", "RefUrl": "/notes/146441 "}, {"RefNumber": "146442", "RefComponent": "BC-INS", "RefTitle": "R/3 4.5B Installation/Upgrade tradition. Chinese", "RefUrl": "/notes/146442 "}, {"RefNumber": "146443", "RefComponent": "BC-INS", "RefTitle": "R/3 4.5B Simplify Installation/Upgrade Chinese", "RefUrl": "/notes/146443 "}, {"RefNumber": "141319", "RefComponent": "PY-DE-FP-DU", "RefTitle": "HR-DEUEV: Personnel Group Key for Upgrade 3.x -> 4.x", "RefUrl": "/notes/141319 "}, {"RefNumber": "383126", "RefComponent": "BC-SRV-BSF-CUR", "RefTitle": "Enhancements to Upgrade: Expiring Currencies", "RefUrl": "/notes/383126 "}, {"RefNumber": "156314", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgrade to Release 4.5B: Termination in phase DIFFEXP*", "RefUrl": "/notes/156314 "}, {"RefNumber": "178452", "RefComponent": "BC-ABA-LA", "RefTitle": "Incompatible ABAP changes from Release 4.0 to 4.5", "RefUrl": "/notes/178452 "}, {"RefNumber": "178482", "RefComponent": "BC-ABA-LA", "RefTitle": "Incompatible ABAP changes from Release 3.0 to 4.0", "RefUrl": "/notes/178482 "}, {"RefNumber": "533777", "RefComponent": "BC-ABA-TO", "RefTitle": "Upgrade problem after SAP Note 508520", "RefUrl": "/notes/533777 "}, {"RefNumber": "498484", "RefComponent": "BC-BW", "RefTitle": "BW Extraction: Multiple Delta Records with qRFC Vers. 6.20.043", "RefUrl": "/notes/498484 "}, {"RefNumber": "143558", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to upgrade to 4.5B online documentation", "RefUrl": "/notes/143558 "}, {"RefNumber": "141957", "RefComponent": "CA-GTF-DRT", "RefTitle": "DART in R/3 Releases 4.5 and 4.6", "RefUrl": "/notes/141957 "}, {"RefNumber": "409442", "RefComponent": "LO-VC-VTA", "RefTitle": "XPRA RCCUVLXP content variant table incorrect III", "RefUrl": "/notes/409442 "}, {"RefNumber": "145794", "RefComponent": "BC-MID-ALE", "RefTitle": "LONGPOST.LOG: Error filling table \\&quot;STACUST\\&quot;", "RefUrl": "/notes/145794 "}, {"RefNumber": "140431", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade to 4.5B ADABASforR/3", "RefUrl": "/notes/140431 "}, {"RefNumber": "148926", "RefComponent": "BC-OP-FTS", "RefTitle": "saposcol Version 32 or 64 bit on ReliantUNIX", "RefUrl": "/notes/148926 "}, {"RefNumber": "388803", "RefComponent": "LO-VC-VTA", "RefTitle": "XPRA RCCUVLXP sorting variant table incorrect", "RefUrl": "/notes/388803 "}, {"RefNumber": "371866", "RefComponent": "LO-VC-VTA", "RefTitle": "XPRA RCCUVLXP content variant table incorrect II", "RefUrl": "/notes/371866 "}, {"RefNumber": "141465", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Conversion of tables EDIDOC &amp; CDCLS, Release 4.5", "RefUrl": "/notes/141465 "}, {"RefNumber": "83728", "RefComponent": "PY-DE", "RefTitle": "Check Table T510Q Before Upgrade", "RefUrl": "/notes/83728 "}, {"RefNumber": "214976", "RefComponent": "PA-OS", "RefTitle": "XPRA RHU40C01 sets time constraint K 1001 B011 incorrectly", "RefUrl": "/notes/214976 "}, {"RefNumber": "141732", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements for upgrade to 4.5B DB2/CS", "RefUrl": "/notes/141732 "}, {"RefNumber": "176472", "RefComponent": "CA-EUR-CNV", "RefTitle": "Euro:Upgrade 3.1I-4.5B(4.6x),control table entries", "RefUrl": "/notes/176472 "}, {"RefNumber": "111601", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Ready-to-Run R/3: Upgrade Information", "RefUrl": "/notes/111601 "}, {"RefNumber": "144675", "RefComponent": "PT-RC", "RefTitle": "XPRA for PTEXDIR", "RefUrl": "/notes/144675 "}, {"RefNumber": "137587", "RefComponent": "BC-UPG-TLS", "RefTitle": "R/3 Upgrade Assistant: <PERSON><PERSON><PERSON><PERSON> remains", "RefUrl": "/notes/137587 "}, {"RefNumber": "137594", "RefComponent": "BC-UPG-TLS", "RefTitle": "R/3 Upgrade Assistant: No connection to server", "RefUrl": "/notes/137594 "}, {"RefNumber": "137599", "RefComponent": "BC-UPG-TLS", "RefTitle": "NO R/3 Upgrade Assistant on Digital UNIX 3.2", "RefUrl": "/notes/137599 "}, {"RefNumber": "137607", "RefComponent": "BC-UPG-TLS", "RefTitle": "R/3 Upgrade Assistant: <PERSON>ack Overflow Exception", "RefUrl": "/notes/137607 "}, {"RefNumber": "174073", "RefComponent": "BC-CUS", "RefTitle": "Missing release notes after upgrade", "RefUrl": "/notes/174073 "}, {"RefNumber": "138360", "RefComponent": "IS-R", "RefTitle": "Using R/3 as SAP Retail (Release 4.5B)", "RefUrl": "/notes/138360 "}, {"RefNumber": "190527", "RefComponent": "CA-EUR-CNV", "RefTitle": "EMU: Error/warning during upgrade of table EWUPAK", "RefUrl": "/notes/190527 "}, {"RefNumber": "307803", "RefComponent": "PA-OS", "RefTitle": "XPRA RHU40C01 Cost distribution error during jump upgrade", "RefUrl": "/notes/307803 "}, {"RefNumber": "99300", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS Info: Inclusion of LCPs in the Upgrade", "RefUrl": "/notes/99300 "}, {"RefNumber": "206328", "RefComponent": "LO-VC-VTA", "RefTitle": "XPRA RCCUVLXP content variant table incorrect", "RefUrl": "/notes/206328 "}, {"RefNumber": "74831", "RefComponent": "BC-CCM-MON", "RefTitle": "SPDD: Transports QE1K900002, TCVK900029, EWSK900052", "RefUrl": "/notes/74831 "}, {"RefNumber": "156568", "RefComponent": "PY-DE-CI", "RefTitle": "T5DB4 data lost due to upgrade to 4.0", "RefUrl": "/notes/156568 "}, {"RefNumber": "174170", "RefComponent": "PY-DE-PS", "RefTitle": "HR OED: Conversion of T510Q with XPRA RPUT510Q", "RefUrl": "/notes/174170 "}, {"RefNumber": "168861", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgrade phase DDIC_<rel>: Do not ignore error", "RefUrl": "/notes/168861 "}, {"RefNumber": "157968", "RefComponent": "FI-FM-PO", "RefTitle": "RFMXPR22 too slow", "RefUrl": "/notes/157968 "}, {"RefNumber": "154430", "RefComponent": "FI-SL", "RefTitle": "FI-SL: Upgrade from Release 3.0/3.1 to Release 4.5", "RefUrl": "/notes/154430 "}, {"RefNumber": "147610", "RefComponent": "BC-DWB-DIC-F4", "RefTitle": "Table T5UT4 is offered for adjustment in SPDD", "RefUrl": "/notes/147610 "}, {"RefNumber": "140313", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Phase ACTREF_CHK in PREPARE Module Act. Checks", "RefUrl": "/notes/140313 "}, {"RefNumber": "140280", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Phase JOB_RADDRCHK in PREPARE Module Act. Checks", "RefUrl": "/notes/140280 "}, {"RefNumber": "138939", "RefComponent": "BC-UPG", "RefTitle": "Upgrade or import hangs 4.5A", "RefUrl": "/notes/138939 "}, {"RefNumber": "115364", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Workload statistics deleted during upgrade to 4.x", "RefUrl": "/notes/115364 "}, {"RefNumber": "69990", "RefComponent": "BC-UPG", "RefTitle": "SPAU: \\&quot;Mark for Transport\\&quot; and Hot Packages", "RefUrl": "/notes/69990 "}, {"RefNumber": "114346", "RefComponent": "IS-R-BD-LST", "RefTitle": "Module texts not complete after upgrade from 1.2x -> 4.y", "RefUrl": "/notes/114346 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=117668&TargetLanguage=EN&Component=BC-UPG-RDM&SourceLanguage=DE&Priority=02\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/117668/D\" target=\"_blank\">/notes/117668/D</a>."}}}}