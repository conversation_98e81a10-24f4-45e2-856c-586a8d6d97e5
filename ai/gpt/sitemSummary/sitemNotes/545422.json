{"Request": {"Number": "545422", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 502, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015268332017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000545422?language=E&token=A95756621A02C9CEA11F6C713ABD5D1B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000545422", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000545422/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "545422"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 16}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.02.2004"}, "SAPComponentKey": {"_label": "Component", "value": "BC-INS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Installation Tools (SAP Note 1669327)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Installation Tools (SAP Note 1669327)", "value": "BC-INS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-INS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "545422 - INST: SAP J2EE Engine Inst. for SAP R/3 Enterprise 4.70"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>**********************************************************************<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Installation of the&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAP J2EE Engine / Dialog Instance including SAP J2EE Engine&#x00A0;&#x00A0;&#x00A0;&#x00A0;*<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; for SAP R/3 Enterprise 4.70 / 4.70 SR1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*<br />**********************************************************************<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The SAP J2EE Engine gets delivered with SAP R/3 Enterprise 4.70 and 4.70 SR1, but is not installed automatically. Perform the following procedure if you want to use the SAP J2EE Engine with SAP R/3 Enterprise 4.70 or 4.70 SR1.<br /><br />NOTE:<br />Although the following text contains directory names in UNIX notation, the procedure is also valid for Windows (on Windows, adapt the directory names accordingly - for example, from &lt;CD_Name&gt;/&lt;Platform&gt; to &lt;CD_Drive_with_CD_mounted&gt;:\\&lt;Platform&gt;).<br /><br />NOTE:<br />If you are installing an SAP instance for SAP R/3 Enterprise 4.70, skip any steps containing information which is only valid for SAP R/3 Enterprise 4.70 <B>SR1</B> (and vice versa).<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>This SAP Note describes the installation of the SAP J2EE Engine for an existing central instance and the installation of a dialog instance with SAP J2EE Engine for SAP R/3 Enterprise 4.70 or 4.70 SR1.<br /><br />Proceed as follows:</p> <OL>1. SAP R/3 Enterprise 4.70 only (for SAP R/3 Enterprise 4.70 SR1, skip this step):</OL> <OL><OL>a) Create a new installation directory.</OL></OL> <OL><OL>b) Start the installation procedure from this newly created installation directory as described in the documentation 'Installation Guide - SAP R/3 Enterprise on &lt;Platform&gt;: &lt;Database&gt;' until the first dialog in SAPinst is shown.</OL></OL> <OL><OL>c) Abort the installation.</OL></OL> <OL><OL>d) Extract the file ADD.SAR to the newly created installation directory with the following commands:<br />&#x00A0;&#x00A0; cd &lt;newly_installed_installation_directory&gt;<br />&#x00A0;&#x00A0;./sapcar -xvfg &lt;SAP_Kernel_CD&gt;/&lt;Platform&gt;/COMMON/ADD.SAR</OL></OL> <OL><OL>e) If you are performing the SAP J2EE Engine installation as part of the SAP R/3 Enterprise upgrade, copy the file &lt;INST_DIR&gt;/SAPINST/&lt;Platform&gt;/&lt;OS&gt;/CONTROL.XML to &lt;INST_DIR&gt;/control.xml.</OL></OL> <OL>2. Before installing the SAP J2EE Engine for an existing SAP system with a 64-Bit SAP Kernel on AIX or HP-UX, make sure that the ipc/shm* parameters are set as follows on every instance host where a SAP J2EE Engine runs:<br />&#x00A0;&#x00A0;ipc/shm_permission_01=10740<br />&#x00A0;&#x00A0;ipc/shm_psize_01=0<br />&#x00A0;&#x00A0;ipc/shm_permission_02=10740<br />&#x00A0;&#x00A0;ipc/shm_psize_02=0<br />&#x00A0;&#x00A0;ipc/shm_permission_03=10740<br />&#x00A0;&#x00A0;ipc/shm_psize_03=0<br />&#x00A0;&#x00A0;ipc/shm_permission_16=10740<br />&#x00A0;&#x00A0;ipc/shm_psize_16=0<br />&#x00A0;&#x00A0;ipc/shm_permission_31=10740<br />&#x00A0;&#x00A0;ipc/shm_psize_31=0<br />&#x00A0;&#x00A0;ipc/shm_permission_52=10740<br />&#x00A0;&#x00A0;ipc/shm_psize_52=0<br />&#x00A0;&#x00A0;ipc/shm_permission_62=10740<br />&#x00A0;&#x00A0;ipc/shm_psize_62=0</OL> <p></p> <OL>3. SAP R/3 Enterprise 4.70 SR1 only (for SAP R/3 Enterprise 4.70, skip this step):</OL> <OL><OL>a) Create a new installation directory.</OL></OL> <OL><OL>b) Start the installation procedure from this newly created installation directory as described in the documentation 'Installation Guide - SAP R/3 Enterprise on &lt;Platform&gt;: &lt;Database&gt;' until the first dialog in SAPinst is shown.</OL></OL> <OL><OL>c) Abort the installation.</OL></OL> <OL><OL>d) Delete the file toplevel.xml from the newly created installation directory.</OL></OL> <OL><OL>e) Copy &lt;SAPinst CD&gt;/&lt;Platform&gt;/COMMON/INSTALL/TOPLEVEL.XML to the newly created installation directory.</OL></OL> <OL><OL>f) Rename the copied file to toplevel.xml.</OL></OL> <OL>4. Perform the installation by using the documentation 'Installation Guide - SAP J2EE Engine Installation / Dialog Instance Installation / Standalone Gateway Installation on &lt;Platform&gt;' (available in SAP Service Marketplace at: http://service.sap.com/instguides --&gt; SAP R/3 Enterprise --&gt; SAP R/3 Enterprise Core 4.70). For the installation of SAP R/3 Enterprise, the following differences apply when using this documentation:</OL> <OL><OL>a) The remarks and prerequisites concerning the upgrade of the central instance do only apply if you are performing the installation of central instance Java core as part of the upgrade of SAP R/3 Enterprise 4.70 or 4.70 SR1.</OL></OL> <OL><OL>b) Hardware requirements: For the SAP J2EE Engine, between 64 MB and 4096 MB of RAM are required, depending on the load of your SAP system. Make sure not to enter a value larger than the maximum heap size of your platform (see the corresponding documentation of your Java Development Kit - JDK).</OL></OL> <OL><OL>c) Dialog Instance only: Do NOT create the user SAPJSF manually, as SAPinst will create this user automatically in client 000 (see below).</OL></OL> <OL><OL>d) Start the installation from the newly created installation directory in step 1 and select:</OL></OL> <UL><UL><LI>'Upgrade SAP Central Instance Java Core 6.20' if you want to install the SAP J2EE Engine for an existing SAP R/3 Enterprise central instance or</LI></UL></UL> <UL><UL><LI>'&lt;SAP Component&gt; Dialog Instance for &lt;your database&gt; - ABAP + JAVA' (SAP R/3 Enterprise 4.70) respective '&lt;SAP Component&gt; Dialog Instance for &lt;your database&gt; - JAVA' (SAP R/3 Enterpise 4.70 SR1) if you want to install a dialog instance including SAP J2EE Engine.</LI></UL></UL><OL><OL>e) Dialog Instance only: SAPinst additionally prompts for the password of user SAPJSF. Make sure to enter a password not longer than 8 characters.</OL></OL> <OL><OL>f) Dialog Instance only: After the installation, call transaction SU01 to make sure that the SAP communication user SAPJSF exists in SAP system client 000 and is not locked. If the user does not exist:</OL></OL> <UL><UL><LI>Call transaction SE37.</LI></UL></UL> <UL><UL><LI>Enter the function module PRGN_J2EE_CREATE_SERVICE_USER.</LI></UL></UL> <UL><UL><LI>Choose Single Test (F8).</LI></UL></UL> <UL><UL><LI>Choose a password for the communication user SAPJSF, and enter this password for the function's import parameter IF_PASSWORD.</LI></UL></UL> <UL><UL><LI>Choose Execute.</LI></UL></UL> <OL><OL>g) SAP R/3 Enterprise 4.70 and SAP R/3 Enterprise 4.70 SR1:<br />If you perform a client copy, and intend to use the new SAP system client as the J2EE Engine's user store, you should change the J2EE security service's configuration after the client copy.<br />Up to SAP J2EE Engine 6.20 PL7, you have to use the R3Propertiesmanager for that purpose. Starting with SAP J2EE Engine 6.20 PL8, integration of ABAP and Java security services has been enhanced by introducing the new SAP Integration Manager (for more details see related SAP Note 569560).<br />In this paragraph it is described how to perform the change of the SAP system client of user SAPJSF after the client copy both up to PL7 and from PL8 on:</OL></OL> <UL><UL><LI>Start the SAP J2EE Engine administration tool:<br />Windows: Choose Start --&gt; SAP J2EE Engine 6.20 --&gt; &lt;Instance_number&gt; --&gt; Administrator<br />UNIX: Enter usr/sap/&lt;SAPSID&gt;/&lt;instance_name&gt;/j2ee/admin/go</LI></UL></UL> <UL><UL><LI>From the menu, choose Connect --&gt; Login and enter the login data:<br />User Name: Administrator<br />Password: leave this field empty<br />Host: &lt;Host_name&gt;<br />Port: &lt;p4 port&gt; (the ports used by the SAP J2EE Engine are listed in the file prepconf.log located in the installation directory of the SAP J2EE Engine)</LI></UL></UL> <UL><UL><LI>Choose Connect.</LI></UL></UL> <UL><UL><LI>Choose Server One -&gt; Services -&gt; security.<br /><br />If your SAP J2EE Engine version is PL7 or lower, proceed as follows:</LI></UL></UL><UL><UL><LI>In the right frame, choose the tab Runtime --&gt; R3Propertiesmanager.</LI></UL></UL> <UL><UL><LI>Enter your new client in string: sapbasis.client=&lt;your_new_client&gt;</LI></UL></UL> <UL><UL><LI>Choose Save.</LI></UL></UL> <UL><UL><LI>Restart your SAP system.<br /><br />As of SAP J2EE Engine 6.20 PL8, the R3Propertiesmanager has been replaced by the SAP Integration Manager.<br />Thus, if your J2EE system 6.20 has PL8 or higher, you have to proceed as follows after having chosen Server One -&gt; Services -&gt; security:</LI></UL></UL> <UL><UL><LI>In the right frame, choose the tab Runtime --&gt; SAP Integration.</LI></UL></UL> <UL><UL><LI>Press the button 'Configure'. A configuration wizard will pop up.</LI></UL></UL> <UL><UL><LI>The SAP system client currently being used by the J2EE security service is displayed in the section 'Common', field 'Client' of the first dialog. Enter the new SAP system client into this field.</LI></UL></UL> <UL><UL><LI>Press the 'Next' button to proceed to step 2.</LI></UL></UL> <UL><UL><LI>If the password of your communication user (SAPJSF) is the same in the old and the new SAP system client, you can leave all settings in dialog step 2 unchanged. Otherwise, enter the password of SAPJSF in the new SAP system client into the field 'Password'.</LI></UL></UL> <UL><UL><LI>Press the 'Next' button to proceed to step 3.</LI></UL></UL> <UL><UL><LI>Leave all settings in step 3 unchanged, and press the 'Finish' button to persistently store your changed settings. This will automatically stop the previously existing connection between the J2EE security service and the ABAP system.</LI></UL></UL> <UL><UL><LI>Press the 'Start' button to activate your new configuration.</LI></UL></UL> <UL><UL><LI>If the connection between the J2EE security service and your new SAP system client can successfully be made, you will be asked whether the JAAS login configuration should be updated to reflect your changed settings. You should press the 'Yes' button to do the update, and then the 'OK' button on the information dialog that will follow once the update has finished.</LI></UL></UL> <UL><UL><LI>The SAP Integration pane in the Visual Administrator should now display details about the connection, including the SAP system client that is being used.</LI></UL></UL> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D035521)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D035521)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000545422/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000545422/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000545422/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000545422/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000545422/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000545422/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000545422/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000545422/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000545422/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852"}, {"RefNumber": "587896", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/587896"}, {"RefNumber": "569560", "RefComponent": "BC-JAS-SEC", "RefTitle": "Integration ABAP/Java Security Services in J2EE Engine", "RefUrl": "/notes/569560"}, {"RefNumber": "1428079", "RefComponent": "BC-OP-HPX", "RefTitle": "SAP Instance failing to start \"Not enough space\"", "RefUrl": "/notes/1428079"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1428079", "RefComponent": "BC-OP-HPX", "RefTitle": "SAP Instance failing to start \"Not enough space\"", "RefUrl": "/notes/1428079 "}, {"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852 "}, {"RefNumber": "569560", "RefComponent": "BC-JAS-SEC", "RefTitle": "Integration ABAP/Java Security Services in J2EE Engine", "RefUrl": "/notes/569560 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}