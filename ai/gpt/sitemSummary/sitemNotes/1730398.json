{"Request": {"Number": "1730398", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 295, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017456402017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001730398?language=E&token=B48379087C36A6FFA7B0BCD74628AD69"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001730398", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001730398/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1730398"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.07.2012"}, "SAPComponentKey": {"_label": "Component", "value": "IS-U-DM-MR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Meter Readings"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Utilities", "value": "IS-U", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-U*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Device Management", "value": "IS-U-DM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-U-DM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Meter Readings", "value": "IS-U-DM-MR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-U-DM-MR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1730398 - AMI: Sending meter reading results to MDUS system"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><b></b><br /> <b>Improvement Request</b><br /> <p>During the device installation, device removal, and device replacement processes, you want to send meter reading results of an intelligent current meter to the meter data unification and synchronization (MDUS) system.<br /></p> <b>Request Reason</b><br /> <p>During the processes mentioned above, the intelligent current meters cannot communicate the actual installation meter reading or removal meter reading directly to MDUS. Instead, you enter the meter reading manually in the IS-U system and this must then be sent to the MDUS system.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Utilities<br />AMI, advanced metering infrastructure<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Customers have requested improvements.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><b></b><br /> <b>Improvement</b><br /> <p>New enterprise services have been provided to enable meter reading results to be sent to the MDUS system. To decide the conditions under which the meter reading results are to be sent, a new enhancement spot was provided.<br /></p> <b>Benefit</b><br /> <p>The meter reading results can be sent to the MDUS system as soon as they are entered in IS-U.<br /></p> <b>Delivery</b><br /> <p>You can obtain this improvement via the relevant Support Package.<br /><br />Activate the business function ISU_AMI_4A to activate this function.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Improvement Note", "Value": "Yes"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D030979)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D035050)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001730398/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001730398/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001730398/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001730398/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001730398/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001730398/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001730398/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001730398/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001730398/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1754249", "RefComponent": "IS-U", "RefTitle": "Enterprise Services for AMI Integration with MDUS systems", "RefUrl": "/notes/1754249"}, {"RefNumber": "1751206", "RefComponent": "IS-OIL", "RefTitle": "Incl. BF to Combined BFS Oil & Gas and Mining with Utilities", "RefUrl": "/notes/1751206"}, {"RefNumber": "1746718", "RefComponent": "IS-U-DM", "RefTitle": "IS-U: Basis for various new functions", "RefUrl": "/notes/1746718"}, {"RefNumber": "1730033", "RefComponent": "IS-U-DM-MR", "RefTitle": "Interface note: Sending meter reading results to MDUS", "RefUrl": "/notes/1730033"}, {"RefNumber": "1728429", "RefComponent": "IS-U", "RefTitle": "Interface note: Basis for various new functions", "RefUrl": "/notes/1728429"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1927486", "RefComponent": "IS-U-DM-MR", "RefTitle": "AMI: Additional parameter for error message BS 002 (part 3)", "RefUrl": "/notes/1927486 "}, {"RefNumber": "1754249", "RefComponent": "IS-U", "RefTitle": "Enterprise Services for AMI Integration with MDUS systems", "RefUrl": "/notes/1754249 "}, {"RefNumber": "1730033", "RefComponent": "IS-U-DM-MR", "RefTitle": "Interface note: Sending meter reading results to MDUS", "RefUrl": "/notes/1730033 "}, {"RefNumber": "1751206", "RefComponent": "IS-OIL", "RefTitle": "Incl. BF to Combined BFS Oil & Gas and Mining with Utilities", "RefUrl": "/notes/1751206 "}, {"RefNumber": "1728429", "RefComponent": "IS-U", "RefTitle": "Interface note: Basis for various new functions", "RefUrl": "/notes/1728429 "}, {"RefNumber": "1746718", "RefComponent": "IS-U-DM", "RefTitle": "IS-U: Basis for various new functions", "RefUrl": "/notes/1746718 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-UT", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "IS-UT", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "IS-UT", "From": "616", "To": "616", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "IS-UT 605", "SupportPackage": "SAPK-60509INISUT", "URL": "/supportpackage/SAPK-60509INISUT"}, {"SoftwareComponentVersion": "IS-UT 605", "SupportPackage": "SAPK-60510INISUT", "URL": "/supportpackage/SAPK-60510INISUT"}, {"SoftwareComponentVersion": "IS-UT 606", "SupportPackage": "SAPK-60605INISUT", "URL": "/supportpackage/SAPK-60605INISUT"}, {"SoftwareComponentVersion": "IS-UT 606", "SupportPackage": "SAPK-60606INISUT", "URL": "/supportpackage/SAPK-60606INISUT"}, {"SoftwareComponentVersion": "IS-UT 616", "SupportPackage": "SAPK-61601INISUT", "URL": "/supportpackage/SAPK-61601INISUT"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}