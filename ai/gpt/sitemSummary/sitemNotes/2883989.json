{"Request": {"Number": "2883989", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 476, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000771432020"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002883989?language=E&token=984ACB15FB4B59AB8BFCA894C9440E3F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002883989", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002883989/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2883989"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 20}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.03.2024"}, "SAPComponentKey": {"_label": "Component", "value": "CA-GTF-BUM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business User Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Application Functions", "value": "CA-GTF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-GTF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business User Management", "value": "CA-GTF-BUM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-GTF-BUM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2883989 - Conversion of clear user names in SAP S/4HANA OP"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>We do not recommend using clear user names. Therefore, the change of user names is not supported in SAP S/4HANA OnPrem. If a clear user name is used anyway, such as a last name, it may be necessary to change the user name as the result of marriage or divorce, for example.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Clear user name, user ID</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>If clear user names are used and HR integration is active, you can implement the customer enhancement using the BAdI FS4BPU_B_BUS_USER_CUST_ENH. The customer enhancement cannot be used if HR integration is inactive because the user name change cannot be propagated through infotype 0105, subtype 001.</p>\r\n<p>An example implementation is maintained in the BAdI.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Important information: If you have to implement this SAP Note only because there are dependencies to another SAP Note, do not perform the specified manual steps. In this case, implement only the correction instructions.</p>\r\n<p>If you require the customer enhancement as described in the symptom: Implement the correction instructions to enable the customer enhancement.&#x00A0;Then read and follow the BAdI documentation of the BAdI FS4BPU_B_BUS_USER_CUST_ENH. The BAdI documentation also mentions an example implementation that you can use and adjust to suit your requirements.</p>\r\n<p>Prerequisites for changing the user name: In infotype 105 subtype 0001, you must delimit the old user and enter the new user with a suitable validity. In addition, the new user must not yet be assigned to any other business partner (classic user).</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-SEC-USR-ADM (Users and Authorization administration)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D022796)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D066608)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002883989/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002883989/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002883989/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002883989/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002883989/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002883989/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002883989/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002883989/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002883989/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3094750", "RefComponent": "CA-GTF-BUM", "RefTitle": "Business User Management - FAQ", "RefUrl": "/notes/3094750 "}, {"RefNumber": "3124864", "RefComponent": "CA-GTF-BUM", "RefTitle": "Change named User ID at country change of an employee", "RefUrl": "/notes/3124864 "}, {"RefNumber": "3022160", "RefComponent": "CA-HR-S4-OP-BP", "RefTitle": "CDPOS/CDHDR Table is growing very fast", "RefUrl": "/notes/3022160 "}, {"RefNumber": "2570961", "RefComponent": "CA-GTF-BUM", "RefTitle": "Simplification item S4TWL - Business User Management", "RefUrl": "/notes/2570961 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": ""}, {"SoftwareComponent": "SAP_ABA", "From": "75D", "To": "75G", "Subsequent": ""}, {"SoftwareComponent": "SAP_ABA", "From": "75I", "To": "75I", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "S4CORE 103", "SupportPackage": "SAPK-10305INS4CORE", "URL": "/supportpackage/SAPK-10305INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 103", "SupportPackage": "SAPK-10304INS4CORE", "URL": "/supportpackage/SAPK-10304INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 103", "SupportPackage": "SAPK-10306INS4CORE", "URL": "/supportpackage/SAPK-10306INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 103", "SupportPackage": "SAPK-10308INS4CORE", "URL": "/supportpackage/SAPK-10308INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 104", "SupportPackage": "SAPK-10404INS4CORE", "URL": "/supportpackage/SAPK-10404INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 104", "SupportPackage": "SAPK-10403INS4CORE", "URL": "/supportpackage/SAPK-10403INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 104", "SupportPackage": "SAPK-10402INS4CORE", "URL": "/supportpackage/SAPK-10402INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 105", "SupportPackage": "SAPK-10502INS4CORE", "URL": "/supportpackage/SAPK-10502INS4CORE"}, {"SoftwareComponentVersion": "SAP_ABA 75D", "SupportPackage": "SAPK-75D08INSAPABA", "URL": "/supportpackage/SAPK-75D08INSAPABA"}, {"SoftwareComponentVersion": "SAP_ABA 75D", "SupportPackage": "SAPK-75D05INSAPABA", "URL": "/supportpackage/SAPK-75D05INSAPABA"}, {"SoftwareComponentVersion": "SAP_ABA 75D", "SupportPackage": "SAPK-75D06INSAPABA", "URL": "/supportpackage/SAPK-75D06INSAPABA"}, {"SoftwareComponentVersion": "SAP_ABA 75D", "SupportPackage": "SAPK-75D04INSAPABA", "URL": "/supportpackage/SAPK-75D04INSAPABA"}, {"SoftwareComponentVersion": "SAP_ABA 75E", "SupportPackage": "SAPK-75E06INSAPABA", "URL": "/supportpackage/SAPK-75E06INSAPABA"}, {"SoftwareComponentVersion": "SAP_ABA 75E", "SupportPackage": "SAPK-75E03INSAPABA", "URL": "/supportpackage/SAPK-75E03INSAPABA"}, {"SoftwareComponentVersion": "SAP_ABA 75E", "SupportPackage": "SAPK-75E02INSAPABA", "URL": "/supportpackage/SAPK-75E02INSAPABA"}, {"SoftwareComponentVersion": "SAP_ABA 75F", "SupportPackage": "SAPK-75F03INSAPABA", "URL": "/supportpackage/SAPK-75F03INSAPABA"}, {"SoftwareComponentVersion": "SAP_ABA 75F", "SupportPackage": "SAPK-75F04INSAPABA", "URL": "/supportpackage/SAPK-75F04INSAPABA"}, {"SoftwareComponentVersion": "SAP_ABA 75G", "SupportPackage": "SAPK-75G02INSAPABA", "URL": "/supportpackage/SAPK-75G02INSAPABA"}, {"SoftwareComponentVersion": "SAP_ABA 75I", "SupportPackage": "SAPK-75I02INSAPABA", "URL": "/supportpackage/SAPK-75I02INSAPABA"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_ABA", "NumberOfCorrin": 16, "URL": "/corrins/0002883989/44"}, {"SoftwareComponent": "S4CORE", "NumberOfCorrin": 10, "URL": "/corrins/0002883989/19773"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; S4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 103&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-10305INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/>Important: If you have to implement this SAP Note only because there are  dependencies to another SAP Note, do not perform the following manual post-implementation steps.<br/>Carry out the following manual steps only if the symptom described in SAP Note 2883989 is your starting point.<br/><br/>Call transaction SE20 and create the enhancement implementation  FS4BPU_B_HR_HELPER_API with the short text \"Business User BAdI - HR Helper API\".<br/>Maintain the existing class CL_FS4BPU_B_HR_HELPER_API as the  implementing class in the enhancement implementation FS4BPU_B_HR_HELPER_API.<br/>Then save and activate the enhancement implementation.<br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_ABA&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Application...|<br/>| Release 75D&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-75D03INSAPABA&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/>Important: If you have to implement this SAP Note only because there are  dependencies to another SAP Note, do not perform the following manual post-implementation steps.<br/>Carry out the following manual steps only if the symptom described in SAP Note 2883989 is your starting point.<br/><br/>Call transaction SE80 and select the package FS4_BPU.<br/>Under \"Enhancement -&gt; Enhancement Spots\", you will find  FS4BPU_CUSTOMER_ENHANCEMENTS with the BAdI definition FS4BPU_B_BUS_USER_CUST_ENH. Add the following documentation to this  customer BAdI definition:<br/><br/>&amp;Use&amp;<br/>In SAP S/4HANA OP, changing the user ID is not supported. We recommend  that you not use named user IDs. If you want to use a named user ID  nonetheless, such as a last name, you need to change the user ID after a  marriage or divorce. The BAdI FS4BPU_B_BUS_USER_CUST_ENH lets you enable changes to the user ID.<br/><br/>&amp;PRECONDITIONS&amp;<br/>HR Integration has to be active for you to use the BAdI. The changed  user ID can be checked for auditing purposes with infotype 0105 subtype 0001.<br/><br/>&amp;STANDARD_SETUP&amp;<br/>Activities<br/>Implement the method CHANGE_USER_ID of the interface  IF_FS4BPU_B_BUS_USER_CUST_ENH. The interface contains the following parameters:<br/>· IV_PERNR (Importing) - Personnel number of the HR master data entity<br/>· IV_BUPA_ID (Importing) - Business partner ID<br/>· IV_USER_ID_OLD (Importing) - Old user ID (must be an IDADTYPE 04  business user)<br/>· IV_USER_ID_NEW (Importing) - New user ID (must be an IDADTYPE 00 classical user)<br/>· IV_WP_ADDRESS_GUID (Importing) – Business partner’s workplace address  GUID<br/>· ET_MESSAGE (Exporting) - Table of messages occurred<br/>· CX_FS4BPU_MAINTAIN_API (Exception) - Processing exception<br/><br/>&amp;Example&amp;<br/>For more information, see the class L_FS4BPU_B_USER_ID_EXAMPLE.<br/><br/><br/>Call transaction SE91 and maintain the following messages as self-explanatory in the message class FS4BPU_EMPL_MINIMSTR:<br/><br/>Number 074 with message short text \"&amp;1 &amp;2 has no user assigned\"<br/>Number 081 with message short text \"&amp;1 &amp;2 does not exist\"<br/>Number 082 with message short text \"&amp;1 &amp;2 with &amp;3 &amp;4 does not exist\"<br/>Number 083 with message short text \"&amp;1 &amp;2 already exists\"<br/>Number 084 with message short text \"&amp;1 &amp;2 with &amp;3 &amp;4 already exists \"<br/>Number 351 with message short text \"User &amp;1 is assigned to multiple work agreements.\"<br/>Number 352 with message short text \"Old user &amp;1 and new user &amp;2 do not belong to the same person.\"<br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_ABA&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Application...|<br/>| Release 75E&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-75E01INSAPABA&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/>Important: If you have to implement this SAP Note only because there are  dependencies to another SAP Note, do not perform the following manual post-implementation steps.<br/>Carry out the following manual steps only if the symptom described in SAP Note 2883989 is your starting point.<br/><br/>Call transaction SE80 and select the package FS4_BPU.<br/>Under \"Enhancement -&gt; Enhancement Spots\", you will find FS4BPU_CUSTOMER_ENHANCEMENTS with the BAdI definition  FS4BPU_B_BUS_USER_CUST_ENH. Add the following documentation to this customer BAdI definition:<br/><br/>&amp;Use&amp;<br/>In SAP S/4HANA OP, changing the user ID is not supported. We recommend  that you not use named user IDs. If you want to use a named user ID  nonetheless, such as a last name, you need to change the user ID after a  marriage or divorce. The BAdI FS4BPU_B_BUS_USER_CUST_ENH lets you enable changes to the user ID.<br/><br/>&amp;PRECONDITIONS&amp;<br/>HR Integration has to be active for you to use the BAdI. The changed  user ID can be checked for auditing purposes with infotype 0105 subtype 0001.<br/><br/>&amp;STANDARD_SETUP&amp;<br/>Activities<br/>Implement the method CHANGE_USER_ID of the interface IF_FS4BPU_B_BUS_USER_CUST_ENH. The interface contains the following  parameters:<br/>· IV_PERNR (Importing) - Personnel number of the HR master data entity<br/>· IV_BUPA_ID (Importing) - Business partner ID<br/>· IV_USER_ID_OLD (Importing) - Old user ID (must be an IDADTYPE 04 business user)<br/>· IV_USER_ID_NEW (Importing) - New user ID (must be an IDADTYPE 00 classical user)<br/>· IV_WP_ADDRESS_GUID (Importing) – Business partner’s workplace address GUID<br/>· ET_MESSAGE (Exporting) - Table of messages occurred<br/>· CX_FS4BPU_MAINTAIN_API (Exception) - Processing exception<br/><br/>&amp;Example&amp;<br/>For more information, see the class L_FS4BPU_B_USER_ID_EXAMPLE.<br/><br/>Call transaction SE91 and maintain the following messages as self-explanatory in the message class FS4BPU_EMPL_MINIMSTR:<br/><br/>Number 351 with message short text \"User &amp;1 is assigned to multiple work agreements.\"<br/>Number 352 with message short text \"Old user &amp;1 and new user &amp;2 do not belong to the same person.\"<br/><br/><br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Activity&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_ABA&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Application...|<br/>| Release 75F&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-75F02INSAPABA&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 75G&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-75G01INSAPABA&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/>Call transaction SE91. In the message class FS4BPU_EMPL_MINIMSTR, change  the self-explanatory text of message 352 to \"Old user &amp;1 and new user &amp;2 do not belong to the same person.\".<br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 26, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 4, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 10, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "S4CORE", "ValidFrom": "103", "ValidTo": "103", "Number": "2707298 ", "URL": "/notes/2707298 ", "Title": "Handling of User conversion,default User Logon Language and Technical description", "Component": "CA-HR-S4"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "103", "ValidTo": "103", "Number": "2780930 ", "URL": "/notes/2780930 ", "Title": "Employee Workplace Address : Corrections", "Component": "CA-HR-S4"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "103", "ValidTo": "103", "Number": "2797960 ", "URL": "/notes/2797960 ", "Title": "Synchronization report: Corrections", "Component": "CA-HR-S4"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "103", "ValidTo": "103", "Number": "2823321 ", "URL": "/notes/2823321 ", "Title": "Synchronization report: Corrections", "Component": "CA-HR-S4"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "103", "ValidTo": "103", "Number": "2873955 ", "URL": "/notes/2873955 ", "Title": "Synchronization report: Corrections", "Component": "CA-HR-S4"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "103", "ValidTo": "103", "Number": "2883989 ", "URL": "/notes/2883989 ", "Title": "Conversion of clear user names in SAP S/4HANA OP", "Component": "CA-GTF-BUM"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "103", "ValidTo": "103", "Number": "3022160 ", "URL": "/notes/3022160 ", "Title": "CDPOS/CDHDR Table is growing very fast", "Component": "CA-HR-S4-OP-BP"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "103", "ValidTo": "103", "Number": "3120291 ", "URL": "/notes/3120291 ", "Title": "CDPOS Correction 103 User sync in Update mode is never sucessful", "Component": "CA-HR-S4-OP-BP"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "104", "ValidTo": "104", "Number": "2780930 ", "URL": "/notes/2780930 ", "Title": "Employee Workplace Address : Corrections", "Component": "CA-HR-S4"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "104", "ValidTo": "104", "Number": "2823321 ", "URL": "/notes/2823321 ", "Title": "Synchronization report: Corrections", "Component": "CA-HR-S4"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "75D", "ValidTo": "75D", "Number": "2883989 ", "URL": "/notes/2883989 ", "Title": "Conversion of clear user names in SAP S/4HANA OP", "Component": "CA-GTF-BUM"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "75D", "ValidTo": "75D", "Number": "2898906 ", "URL": "/notes/2898906 ", "Title": "Missing workplace address error handling at user assignment", "Component": "CA-GTF-BUM"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "75E", "ValidTo": "75E", "Number": "2822420 ", "URL": "/notes/2822420 ", "Title": "Customizing for workplace address is ignored during the assignment of a user to a business user", "Component": "CA-GTF-BUM"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "75E", "ValidTo": "75E", "Number": "2883989 ", "URL": "/notes/2883989 ", "Title": "Conversion of clear user names in SAP S/4HANA OP", "Component": "CA-GTF-BUM"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "75E", "ValidTo": "75E", "Number": "2898906 ", "URL": "/notes/2898906 ", "Title": "Missing workplace address error handling at user assignment", "Component": "CA-GTF-BUM"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}