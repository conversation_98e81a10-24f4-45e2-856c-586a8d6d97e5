{"Request": {"Number": "1393207", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 288, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016924402017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001393207?language=E&token=155D7E9D6F26D16A914567964BB594BF"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001393207", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001393207/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1393207"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.02.2011"}, "SAPComponentKey": {"_label": "Component", "value": "BI-BIP-INS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Installation, Updates, Upgrade, Patching"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Business intelligence solutions", "value": "BI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business intelligence platform", "value": "BI-BIP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BI-BIP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Installation, Updates, Upgrade, Patching", "value": "BI-BIP-INS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BI-BIP-INS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1393207 - Configuring SAP BusinessObjects Edge 3.1 Monitoring"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This is the central SAP Note for the configuration of monitoring on SAP BusinessObjects Edge 3.1 server (BOE 3.1). It assumes that you are running an SAP BusinessObjects Edge 3.1 system and an SAP Abap based system like All-in-One system and want to enable the remote service EarlyWatch Alert (EWA). This note is complemented by the instructions for the All-in-One system contained in note 1369712.<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>EarlyWatch Alert, EWA, BusinessObjects Edge, BOE, 3.1, SAP Solution Manager</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>SAPHOSTAGENT - Before you start with the implementation of the solution described below, you need to install a most recent version of SAPHOSTAGENT 7.20 (7.20 SP23 or newer) on the server running Business Objects: please implement note 1031096. SAPHOSTAGENT 7.20 can also be used on servers running SAP NetWeaver 7.0 and 6.40 components.<br />SAPCCMSR - For NW 7.01 based Abap systems and higher, you can use the sapccmsr agent built into saphostctrl and skip implementation of note 209834<br />For NW 7.0 based ABAP systems like All-in-One based upon BestPractices 2008 and 2009 baselines, please implement note 209834 (install sapccmsr agent). Extend file host_profile in the saphostctrl\\exe directory with the line<br />ccms/enable_agent = -1<br />This disables startup of the built-in sapccmsr agent.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Download the attached file ewa4boe.zip onto your desktop and extract it to a temporary directory. Then copy and configure the files in the temp directory to the required locations on the BOE server using the provided scripts. The setup requires authentication information for 2 different users on the Business Objects server:<br />sapadm is the OS user required by SAPHOSTAGENT, the user is created by the installation procedure of SAPHOSTAGENT.<br />CMSUserName is the name of the user in Business Objects Central Management Server. CMS is a the key Component within Xi, handling security and the routing of requests to other services:<br /><br />Linux/Unix</p> <OL>1. As root user, execute the shell script enable_ewa_monitoring.sh by invoking sh enable_ewa_monitoring.sh in your temp directory. Use enable_ewa_monitoring.sh -h to display available options.</OL> <OL>2. Test the configuration as follows: as root user, change to directory /usr/sap/hostctrl/exe and execute the following commands:<br />./saphostctrl -function ExecuteOperation -name dump_all_xml \"PASSWORD=&lt;CMSHostName&gt;;&lt;CMSUserName&gt;;&lt;PasswordCMSUser&gt;\"<br />./saphostctrl -function ExecuteOperation -name kpi \"PASSWORD=&lt;CMSHostName&gt;;&lt;CMSUserName&gt;;&lt;PasswordCMSUser&gt;\"<br />./saphostctrl -function ExecuteOperation -name bobjmonitor<br /><br />If the configuration is correct, the commands will return some output and the exitcode will be 0 (zero). In addition please ensure that the requirements from note 927637 are met (sticky bit for sapuxusercheck, you may need to add user sapadm to group sapsys in /etc/group and execute \"/etc/init.d/sapinit restart\" as root user)</OL> <p><br /><br />Windows</p> <OL>1. As administration user, execute the batch script enable_ewa_monitoring.bat by invoking enable_ewa_monitoring.bat in your temp directory. Consider to edit the script, you can modify the default parameters so that they match your setup. Alternatively, enter your parameters during skript execution.<br />Remark: the script can be re-applied in case of errors.</OL><OL>2. Test the configuration as follows: as admin user, change to directory where saphostctrl resides and execute the following commands:<br />saphostctrl -function ExecuteOperation -name dump_all_xml \"PASSWORD=&lt;CMSHostName&gt;;&lt;CMSUserName&gt;;&lt;PasswordCMSUser&gt;\"<br />-user sapadm &lt;sapadm passwd&gt;<br />saphostctrl -function ExecuteOperation -name kpi \"PASSWORD=&lt;CMSHostName&gt;;&lt;CMSUserName&gt;;&lt;PasswordCMSUser&gt;\"<br />-user sapadm &lt;sapadm passwd&gt;<br />saphostctrl -function ExecuteOperation -name bobjmonitor -user sapadm &lt;sapadm passwd&gt;<br /><br />If the configuration is correct, the commands will return some output and the exitcode will be 0 (zero). In addition please ensure that the requirements from note 927637 are met.</OL> <p>IMPORTANT SECURITY REMARK:<br />Ensure that the profile of the Businessobjects user (e.g. Administrator user) used to retrieve info from CMS only grants minimal permissions in Business Objects. Create a different user if you want to further limit this user's capabilities, see the PDF document attached to this note  (see xi3-1_bip_admin_en.pdf above). Alternatively use attached biar file contained in attachment EWA4BOEUSER to import the EWA user into your system. This user has very limited read-only authorization for Business Objects monitoring information.<br /><br />Windows only: We strongly suggest that you implement a safe SAP BusinessObjects Edge authentication method, see the SAP BusinessObjects Adminstration Guide: http://help.sap.com/businessobject/product_guides/boexir31/en/xi3-1_bip_admin_en.pdf.<br />If Single Sign On is configured appropriately you can use the installation option offered by enable_ewa_monitoring.bat to activate SSO for CMS logon.<br />Remark: If you experience problems with the SAP EarlyWatch Alert for SAP All-in-One and want to open a message, please assign it to component SV-SMG-SER if you assume that the problems are related to the setup in the SAP system. If you assume that the problems are related to the setup on the BOP server, assing the message to component BOJ-BIP-INS.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-SMG-SER (SAP Support Services)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D021371)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D019575)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001393207/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001393207/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001393207/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001393207/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001393207/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001393207/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001393207/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001393207/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001393207/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "ewa4boe.zip", "FileSize": "170", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000488172009&iv_version=0005&iv_guid=D8C36E71100DD846B44E64172E5579F4"}, {"FileName": "EWAUser4BOE.pdf", "FileSize": "425", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000488172009&iv_version=0005&iv_guid=4DEF6262E146BB43B133CC48A806AE49"}, {"FileName": "20110506_Inst_Guide_EWA_BOBJ_ERP.pdf", "FileSize": "508", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000488172009&iv_version=0005&iv_guid=841AF0034126EF4DB6FF6A663A875FB3"}, {"FileName": "EWA4BOEUSER.zip", "FileSize": "61", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000488172009&iv_version=0005&iv_guid=583F7CF28B667B46BCEF6BD3642415DA"}, {"FileName": "20110506_EWA_BOBJ_Architecture.pdf", "FileSize": "134", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000488172009&iv_version=0005&iv_guid=E9C868A524FBCB42B5AD9ADC383ACD8D"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "927637", "RefComponent": "BC-CST-STS", "RefTitle": "Web service authentication in sapstartsrv as of Release 7.00", "RefUrl": "/notes/927637"}, {"RefNumber": "91844", "RefComponent": "PP-PI", "RefTitle": "Control recipe cannot be generated again with SREZ", "RefUrl": "/notes/91844"}, {"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455"}, {"RefNumber": "1542651", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "EarlyWatch alert for HANA: Service data collection", "RefUrl": "/notes/1542651"}, {"RefNumber": "1392822", "RefComponent": "SV-SMG-SER", "RefTitle": "Webservice Access via GRMG", "RefUrl": "/notes/1392822"}, {"RefNumber": "1369712", "RefComponent": "SV-SMG-SER", "RefTitle": "Prepare SAP All-in-One + SAP BusinessObjects for Serv.Sess.", "RefUrl": "/notes/1369712"}, {"RefNumber": "1365187", "RefComponent": "SV-SMG-SER", "RefTitle": "Enhancement of non-ABAP Service Data Download for BOBJ", "RefUrl": "/notes/1365187"}, {"RefNumber": "1292045", "RefComponent": "SV-SMG-SER", "RefTitle": "Enable or disable CPH for SAP services", "RefUrl": "/notes/1292045"}, {"RefNumber": "1031096", "RefComponent": "BC-CCM-HAG", "RefTitle": "Installing Package SAPHOSTAGENT", "RefUrl": "/notes/1031096"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455 "}, {"RefNumber": "1542651", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "EarlyWatch alert for HANA: Service data collection", "RefUrl": "/notes/1542651 "}, {"RefNumber": "1031096", "RefComponent": "BC-CCM-HAG", "RefTitle": "Installing Package SAPHOSTAGENT", "RefUrl": "/notes/1031096 "}, {"RefNumber": "927637", "RefComponent": "BC-CST-STS", "RefTitle": "Web service authentication in sapstartsrv as of Release 7.00", "RefUrl": "/notes/927637 "}, {"RefNumber": "1369712", "RefComponent": "SV-SMG-SER", "RefTitle": "Prepare SAP All-in-One + SAP BusinessObjects for Serv.Sess.", "RefUrl": "/notes/1369712 "}, {"RefNumber": "1392822", "RefComponent": "SV-SMG-SER", "RefTitle": "Webservice Access via GRMG", "RefUrl": "/notes/1392822 "}, {"RefNumber": "1365187", "RefComponent": "SV-SMG-SER", "RefTitle": "Enhancement of non-ABAP Service Data Download for BOBJ", "RefUrl": "/notes/1365187 "}, {"RefNumber": "1292045", "RefComponent": "SV-SMG-SER", "RefTitle": "Enable or disable CPH for SAP services", "RefUrl": "/notes/1292045 "}, {"RefNumber": "91844", "RefComponent": "PP-PI", "RefTitle": "Control recipe cannot be generated again with SREZ", "RefUrl": "/notes/91844 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}