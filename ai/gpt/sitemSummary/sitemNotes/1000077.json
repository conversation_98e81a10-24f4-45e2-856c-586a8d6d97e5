{"Request": {"Number": "1000077", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 254, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016190302017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001000077?language=E&token=EBF2A8A98D21073EA89301330432549F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001000077", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001000077/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1000077"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.09.2009"}, "SAPComponentKey": {"_label": "Component", "value": "MM-IM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Inventory Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Inventory Management", "value": "MM-IM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-IM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1000077 - FAQ: Problems when implementing Note 32236"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note provides answers to the most frequently asked questions we receive about correcting problems that occur when implementing the report contained in Note 32236.<br /></p> <b>Questions:</b><br /> <OL>1. When I implement the report various error messages are displayed in the transport log or syntax errors occur. What should I do?<br /></OL> <OL>2. How do I know when the transport has been successful?<br /></OL> <OL>3. Problems occur during the import. For example, the system issues the error \"Probably the data file was destroyed during file transfer!\"&#x00A0;&#x00A0;What should I do?<br /></OL> <OL>4. Syntax errors concerning the \"MMINKON_UP-OWNER\" field occur in the MB* reports. What should I do?<br /></OL> <OL>5. My release level is lower than 4.6C and syntax errors occur when I implement the report. What should I do?<br /></OL> <OL>6. You execute one of the reports contained in Note 32236. This returns an apparent error. Does this mean there is an inconsistency?</OL> <OL>7. Which user authorizations do I require for an analysis and a correction, if necessary?</OL> <OL>8. I cannot use transaction SNOTE to implement Note 32236.&#x00A0;&#x00A0;How can I implement the reports?</OL> <p><br /></p> <b>Answers:</b><br /> <OL>1. <B>Question:</B><br />When I implement the report various error messages are displayed in the transport log or syntax errors occur. What should I do?<br /><B>Answer:</B><br />During the import, there may be multiple error messages in the transport log concerning lines that are too long (truncated after 72 characters) or syntax errors in the programs. You can ignore these messages.<br /></OL> <OL>2. <B>Question:</B><br />How do I know when the transport has been successful?<br /><B>Answer:</B><br />The transport has been successful if the programs and the respective database tables (MMINKON, MMINKON_UP) exist in the system.<br /></OL> <OL>3. <B>Question:</B><br />Problems occur during the import. For example, the system issues the errors \"Probably the data file was destroyed during file transfer!\" or \"ENTRY_SIZE is too short\"&#x00A0;&#x00A0;What should I do?<br /><B>Answer:</B><br />Ensure that you have the most current transport version of R/3 trans. If this is not the case, import the current R/3 trans or kernel (see Notes 60928, 126776 and the other notes referred to in these notes). See Note 454321 if you are using Release 4.5B.<br /></OL> <OL>4. <B>Question:</B><br />Syntax errors concerning the \"MMINKON_UP-OWNER\" field occur in the MB* reports. What should I do?<br /><B>Answer:</B><br />If syntax errors occur in the MB* reports concerning the \"MMINKON_UP-OWNER\" field, import the transport again. See Note 13719. Work with \"unconditional modes\", \"U19\" (1 = import again) and (9 = ignore incorrect transport type). Cause: An old version of this inconsistency tool was previously imported into your system. A reimport alone does not update the existing definitions of the existing MMINKON and MMINKON_UP tables.<br /></OL> <OL>5. <B>Question:</B><br />My release level is lower than 4.6C and syntax errors occur when I implement the report. What should I do?<br /><B>Answer:</B><br />See Note 989970.<br /></OL> <OL>6. <B>Question:</B><br />You execute one of the reports contained in Note 32236. This returns an apparent error. Does this mean there is an inconsistency?<br /><B>Answer:</B><br />In general, you should not execute the reports contained in this note yourself because the results can often be misleading. The results of these reports generally need to be interpreted. Therefore, use SAP standard transaction MB5K to check you materials. If error 045 occurs when you do so, see Note 1026379.</OL> <OL>7. <B>Question: </B><br />Which user authorizations do I require for an analysis and a correction, if necessary?<br /><B>Answer:</B><br />To be able to process and analyze the data inconsistencies promptly, our support team requires a user master record with sufficient authorization. The reports listed above can usually only be started by users that have debugging authorization (object 'S_DEVELOP', object type 'DEBUG' and activity '03'). In addition, Debug&amp;Replace authorization is required as well as authorization for all transactions in package MB and SM*/SE* system transactions.</OL> <OL>8. <B>Question:</B><br />I cannot use transaction SNOTE to implement Note 32236.&#x00A0;&#x00A0;How can I implement the reports?<br /><B>Answer:</B><br />Note 32236 does not contain any correction instructions.&#x00A0;&#x00A0;You have to download the transport files from the note attachment and transport them into your system.&#x00A0;&#x00A0;This is explained in detail in the long text of Note 32236.</OL> <p></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>32236, MB5K, RM07MMFI, difference, implementation<br />MBANEKBE, MBCKBSEG, MBCKBSIM, MBCKMACI, MBCKMD01, MBCKMD02, MBCKMDFI,<br />MBCKMDLI, MBCKMDOC, MBCKMDTY, MBCKMI1,&#x00A0;&#x00A0;MBCKMQCI, MBCKMSSA, MBCKMSSQ,<br />MBENQMAT, MBFIRST,&#x00A0;&#x00A0;MBHIST,&#x00A0;&#x00A0;MBINKON,&#x00A0;&#x00A0;MBINKON2, MBLABST,&#x00A0;&#x00A0;MBMENU,<br />MBMISSFI, MBMSSACO, MBMSSAPO, MBMSSATY, MBMSSI01, MBMSSQCO, MBMSSQCR,<br />MBMSSQTY, MBMSSQUA, MBMSSVAL, MSPSTCRR, MBQUANT,&#x00A0;&#x00A0;MBSEGBSX, MBSHOWUP,<br />MBSTOCK,&#x00A0;&#x00A0;MBTRAME,&#x00A0;&#x00A0;MBTRAMEW, MBUPDATE, MB_MRMI,&#x00A0;&#x00A0;RM07APP1, RM07AUTH,<br />RM07COMP, RM07HIST, RM07MB51, RM07MR51, RM07REUS, MBVALUE<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>-</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>-</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "MM-IM-GF-INC (Stock Inconsistencies)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D040334)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D040334)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001000077/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001000077/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001000077/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001000077/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001000077/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001000077/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001000077/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001000077/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001000077/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "989970", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/989970"}, {"RefNumber": "60928", "RefComponent": "BC-CTS", "RefTitle": "Transports between Release 3.0 or 3.1 and Release 4.0", "RefUrl": "/notes/60928"}, {"RefNumber": "454321", "RefComponent": "BC-CTS", "RefTitle": "Transports between Basis Release 6.* and 7.0", "RefUrl": "/notes/454321"}, {"RefNumber": "34440", "RefComponent": "MM-IM", "RefTitle": "Procedure for correcting the material master", "RefUrl": "/notes/34440"}, {"RefNumber": "32236", "RefComponent": "MM-IM", "RefTitle": "Incorrect stock qty or stock value in material master", "RefUrl": "/notes/32236"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1278140", "RefComponent": "MM-IM-GF-INC", "RefTitle": "SYNTAX_ERROR in MBSTOCKS, MBQUANTS, MBVALUES with IS-ADEC", "RefUrl": "/notes/1278140"}, {"RefNumber": "126776", "RefComponent": "BC-CTS", "RefTitle": "Transports between Releases 4.5 and 4.6", "RefUrl": "/notes/126776"}, {"RefNumber": "1234865", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1234865"}, {"RefNumber": "1227770", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Analysis and correction reports for inventory management", "RefUrl": "/notes/1227770"}, {"RefNumber": "1089083", "RefComponent": "BC-CTS", "RefTitle": "Transports between Basis Releases 7.0* and 7.1*", "RefUrl": "/notes/1089083"}, {"RefNumber": "1069539", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1069539"}, {"RefNumber": "1026379", "RefComponent": "MM-IM-GF-REP", "RefTitle": "MB5K: Misleading error message 045 due to rounding", "RefUrl": "/notes/1026379"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "34440", "RefComponent": "MM-IM", "RefTitle": "Procedure for correcting the material master", "RefUrl": "/notes/34440 "}, {"RefNumber": "1089083", "RefComponent": "BC-CTS", "RefTitle": "Transports between Basis Releases 7.0* and 7.1*", "RefUrl": "/notes/1089083 "}, {"RefNumber": "32236", "RefComponent": "MM-IM", "RefTitle": "Incorrect stock qty or stock value in material master", "RefUrl": "/notes/32236 "}, {"RefNumber": "1234865", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Pre-analysis of MM-IM-GF-INC messages in Primary Support", "RefUrl": "/notes/1234865 "}, {"RefNumber": "1227770", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Analysis and correction reports for inventory management", "RefUrl": "/notes/1227770 "}, {"RefNumber": "1278140", "RefComponent": "MM-IM-GF-INC", "RefTitle": "SYNTAX_ERROR in MBSTOCKS, MBQUANTS, MBVALUES with IS-ADEC", "RefUrl": "/notes/1278140 "}, {"RefNumber": "454321", "RefComponent": "BC-CTS", "RefTitle": "Transports between Basis Release 6.* and 7.0", "RefUrl": "/notes/454321 "}, {"RefNumber": "126776", "RefComponent": "BC-CTS", "RefTitle": "Transports between Releases 4.5 and 4.6", "RefUrl": "/notes/126776 "}, {"RefNumber": "60928", "RefComponent": "BC-CTS", "RefTitle": "Transports between Release 3.0 or 3.1 and Release 4.0", "RefUrl": "/notes/60928 "}, {"RefNumber": "1069539", "RefComponent": "MM-IM-GF-INC", "RefTitle": "MMINKON: Change of update key of inconsistency tool", "RefUrl": "/notes/1069539 "}, {"RefNumber": "989970", "RefComponent": "MM-IM-GF-INC", "RefTitle": "Addition inconsistency tools version 18 of Note 32236", "RefUrl": "/notes/989970 "}, {"RefNumber": "1026379", "RefComponent": "MM-IM-GF-REP", "RefTitle": "MB5K: Misleading error message 045 due to rounding", "RefUrl": "/notes/1026379 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}