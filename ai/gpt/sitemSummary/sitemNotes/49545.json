{"Request": {"Number": "49545", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 296, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014453202017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000049545?language=E&token=C34C53C242CD02430EBAAB85237F993A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000049545", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000049545/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "49545"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.09.2017"}, "SAPComponentKey": {"_label": "Component", "value": "BC-BMT-WFM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Workflow"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Management", "value": "BC-BMT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-BMT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Workflow", "value": "BC-BMT-WFM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-BMT-WFM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "49545 - Deletion of work items that are no longer required"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>How can work items that are no longer required be deleted?</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Delete, archive, ADK, Archive Development Kit, SARA, work item</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>At the end of a test phase, the inboxes of several users are often full of work items that are no longer required.<br />In the production phase, completed work items should be deleted from the system or archived after a certain time.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Because the work items in the inbox of a user are not - unlike in the case of e-mails - private copies, but are simply links to an individual work item, you cannot clear the inbox of one user only. Instead, you can only delete work items themselves from the inboxes of all users.<br />The reports RSWWWIDE and RSWWHIDE are available for this. Both reports are pure administration tools. They delete all selected work items from the database immediately, without further warning and without an authorization test. RSWWWIDE deletes the work item itself (including its attachments and all dependent work items); RSWWHIDE deletes the work item history. RSWWWIDE can also delete work items that are not in a final state or that are part of a higher-level workflow. Following the execution of the reports, there is no way of restoring the deleted work items.<br />For safety reasons, we recommend that you start both reports online first, make the desired selection, and set the \"Display list only\" indicator. Once you have made sure that the desired selection is correct, the actual deletion should take place as a batch job for performance reasons. The only step here is the report RSWWWIDE (or RSWWHIDE as a second step) in the variant determined previously.<br />As of Release 30D, you can also archive work items. To enable this, the archiving object WORKITEM and the archiving class WORKITEM have been created for work items in the Archive Development Kit (ADK). Users of this functionality should first make themselves familiar with the ADK (for example, by means of the online documentation).<br />To delete work items via the ADK, an archiving file must first be created. The work items saved in this archiving file can then be deleted via the ADK in a second step. The archiving and deletion of work items via the ADK is described in detail in the online documentation for the workflow.<br />During archiving, attachments for a work item are handled separately. The technical details are described in SAP Note 2049016.<br />The history for a work item and the step log for a workflow are deleted automatically when the ADK is used.<br />The reloading of the archived data from the archive to the original database tables is not possible for consistency reasons, since the numbers of the archived work items are used again later on and therefore new data might be overwritten with old values if a reload were to take place.<br />However, the reimport of data to internal tables to enable your own evaluations is possible. The report RSWWARCR serves as an example of a read program of this kind. This report is intended as a template for read reports created by customers in accordance with their own needs.<br />The actual reimport of the data takes place using the function module SWW_WI_LIST_ARCHIVED_READ.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D002677)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D002677)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000049545/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000049545/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000049545/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000049545/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000049545/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000049545/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000049545/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000049545/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000049545/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "98407", "RefComponent": "BC-BMT-WFM", "RefTitle": "Performance workflow", "RefUrl": "/notes/98407"}, {"RefNumber": "887304", "RefComponent": "FIN-CGV-MIC-WF", "RefTitle": "MIC:FAQ, Workflow, Email, Scheduling", "RefUrl": "/notes/887304"}, {"RefNumber": "836092", "RefComponent": "BC-XI-IS-BPE", "RefTitle": "BPE-RUN: Archiving/deleting work items", "RefUrl": "/notes/836092"}, {"RefNumber": "76431", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/76431"}, {"RefNumber": "737696", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/737696"}, {"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478"}, {"RefNumber": "69987", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/69987"}, {"RefNumber": "67366", "RefComponent": "BC-BMT-WFM", "RefTitle": "Deletion of work items is incomplete", "RefUrl": "/notes/67366"}, {"RefNumber": "637683", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/637683"}, {"RefNumber": "587896", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/587896"}, {"RefNumber": "573656", "RefComponent": "BC-BMT-WFM", "RefTitle": "Collective note relating to Archiving in workflow", "RefUrl": "/notes/573656"}, {"RefNumber": "547893", "RefComponent": "BC-BMT-WFM", "RefTitle": "FAQ Workflow, definition", "RefUrl": "/notes/547893"}, {"RefNumber": "547601", "RefComponent": "BC-BMT-WFM", "RefTitle": "FAQ workflow, runtime environment and troubleshooting", "RefUrl": "/notes/547601"}, {"RefNumber": "420371", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info on upgrading to Basis 4.6C SR2 (NDI upgrade)", "RefUrl": "/notes/420371"}, {"RefNumber": "390062", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to 4.6C SR2", "RefUrl": "/notes/390062"}, {"RefNumber": "329622", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions: Upgrading to Basis 4.6C SR1 (NDI upgrade)", "RefUrl": "/notes/329622"}, {"RefNumber": "327285", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade to 4.6C SR1", "RefUrl": "/notes/327285"}, {"RefNumber": "304597", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade to Release 4.6C  FCS", "RefUrl": "/notes/304597"}, {"RefNumber": "2049016", "RefComponent": "BC-BMT-WFM", "RefTitle": "Handling of attachments in the framework of workflow archiving", "RefUrl": "/notes/2049016"}, {"RefNumber": "192949", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info for upgrade to 4.6C", "RefUrl": "/notes/192949"}, {"RefNumber": "179373", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info for upgrade to Rel.4.6B", "RefUrl": "/notes/179373"}, {"RefNumber": "155674", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions upgrade to Release 4.6A", "RefUrl": "/notes/155674"}, {"RefNumber": "153205", "RefComponent": "BC-BMT-WFM", "RefTitle": "Direct deletion of type C work items", "RefUrl": "/notes/153205"}, {"RefNumber": "145291", "RefComponent": "BC-BMT-WFM", "RefTitle": "Performance of deletion from archive file", "RefUrl": "/notes/145291"}, {"RefNumber": "126678", "RefComponent": "BC-BMT-WFM", "RefTitle": "Archiving or deleting work items of type C", "RefUrl": "/notes/126678"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1813141", "RefComponent": "BC-MID-ALE", "RefTitle": "How to delete unnecessary workitems of IDoc processing", "RefUrl": "/notes/1813141 "}, {"RefNumber": "2000002", "RefComponent": "HAN-DB-PERF", "RefTitle": "FAQ: SAP HANA SQL Optimization", "RefUrl": "/notes/2000002 "}, {"RefNumber": "2151342", "RefComponent": "BC-BMT-WFM-RUN", "RefTitle": "Work items cannot be deleted", "RefUrl": "/notes/2151342 "}, {"RefNumber": "2578826", "RefComponent": "BC-BMT-WFM", "RefTitle": "Archiving Object WORKITEM - tables with deletion", "RefUrl": "/notes/2578826 "}, {"RefNumber": "2382266", "RefComponent": "BC-BMT-WFM", "RefTitle": "How to remove work items from user's Workflow Inbox", "RefUrl": "/notes/2382266 "}, {"RefNumber": "2151378", "RefComponent": "BC-BMT-WFM", "RefTitle": "Work item not removed from SBWP inbox", "RefUrl": "/notes/2151378 "}, {"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478 "}, {"RefNumber": "764707", "RefComponent": "BC-BMT-WFM-WLC", "RefTitle": "Performance in Business Workplace", "RefUrl": "/notes/764707 "}, {"RefNumber": "836092", "RefComponent": "BC-XI-IS-BPE", "RefTitle": "BPE-RUN: Archiving/deleting work items", "RefUrl": "/notes/836092 "}, {"RefNumber": "126678", "RefComponent": "BC-BMT-WFM", "RefTitle": "Archiving or deleting work items of type C", "RefUrl": "/notes/126678 "}, {"RefNumber": "153205", "RefComponent": "BC-BMT-WFM", "RefTitle": "Direct deletion of type C work items", "RefUrl": "/notes/153205 "}, {"RefNumber": "887304", "RefComponent": "FIN-CGV-MIC-WF", "RefTitle": "MIC:FAQ, Workflow, Email, Scheduling", "RefUrl": "/notes/887304 "}, {"RefNumber": "390062", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to 4.6C SR2", "RefUrl": "/notes/390062 "}, {"RefNumber": "327285", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade to 4.6C SR1", "RefUrl": "/notes/327285 "}, {"RefNumber": "329622", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions: Upgrading to Basis 4.6C SR1 (NDI upgrade)", "RefUrl": "/notes/329622 "}, {"RefNumber": "98407", "RefComponent": "BC-BMT-WFM", "RefTitle": "Performance workflow", "RefUrl": "/notes/98407 "}, {"RefNumber": "179373", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info for upgrade to Rel.4.6B", "RefUrl": "/notes/179373 "}, {"RefNumber": "420371", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info on upgrading to Basis 4.6C SR2 (NDI upgrade)", "RefUrl": "/notes/420371 "}, {"RefNumber": "547601", "RefComponent": "BC-BMT-WFM", "RefTitle": "FAQ workflow, runtime environment and troubleshooting", "RefUrl": "/notes/547601 "}, {"RefNumber": "547893", "RefComponent": "BC-BMT-WFM", "RefTitle": "FAQ Workflow, definition", "RefUrl": "/notes/547893 "}, {"RefNumber": "192949", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info for upgrade to 4.6C", "RefUrl": "/notes/192949 "}, {"RefNumber": "145291", "RefComponent": "BC-BMT-WFM", "RefTitle": "Performance of deletion from archive file", "RefUrl": "/notes/145291 "}, {"RefNumber": "155674", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions upgrade to Release 4.6A", "RefUrl": "/notes/155674 "}, {"RefNumber": "304597", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade to Release 4.6C  FCS", "RefUrl": "/notes/304597 "}, {"RefNumber": "69987", "RefComponent": "BC-BMT-WFM", "RefTitle": "Average storage requirement of a work item", "RefUrl": "/notes/69987 "}, {"RefNumber": "67366", "RefComponent": "BC-BMT-WFM", "RefTitle": "Deletion of work items is incomplete", "RefUrl": "/notes/67366 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}