{"Request": {"Number": "1678400", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 313, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001678400?language=E&token=8ABAE2995A29D90B23BA83C70285872D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001678400", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001678400/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1678400"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "00.00.0000"}, "SAPComponentKey": {"_label": "Component", "value": "PY-US-TP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Third Party Remittance"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "USA", "value": "PY-US", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-US*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Third Party Remittance", "value": "PY-US-TP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-US-TP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1678400 - 3PR: Garn. EFT pmt. info and 3PR pmt. acknowledgment."}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>In the standard system, as currently delivered, an error may occur in<br />cases where you attempt to create a payment medium/form for third-party<br />remittance transactions and the current posting run number exceeds<br />99999. Specifically, upon executing the <B>Create Third-Party</B><br /><B>Remittance Posting Run</B> program (RPURMPK0 or RPURMP00), the system<br />creates the 3PR posting information for the FI posting. In short,<br />the 3PR posting information does not contain all of the data that is<br />subsequently used to enable communication between Financials and<br />Third-Party Remittance within Human Capital Management.<br /><br />This note contains the changes delivered from Note 1130576 (3PR: Garn. EFT pmt. info and 3PR pmt. acknowledgment.). The solution was synchronized to SAP_HRRXX 500. It also includes other changes that were not delivered via Note 1130576.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Third-Party, REMSN, RDATN, REMPN, synchronization, 3PR</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Design error.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>To implement the solution, choose one of the following options:<br /><B>Option 1:</B> <B>Apply </B><U><B>FI</B></U><B> note 1130975 and install the Synchronization HR Support described in the HR Support Package section</B><br />Note that due to DB-changes on the tables T51R2, T51R3 and T51R5 there might be a longer downtime during the installation of the HR Support package, depending on the number of entries in these tables across all clients.<br /><B>Option 2: Apply</B><U><B>FI</B></U><B> note 1130975 and Install the Note in advance</B><br />To install the Note in advance via the Note Assistant (transaction<br />SNOTE), follow the detailed steps provided in the manual correction<br />instruction attached to this Note that are relevant for your release.<br />The manual correction instructions have to be installed exactly in the<br />described sequence.<br />(SAP Notes 1318389, 13719 and 480180 describe the procedure for<br />downloading and importing the data files, as well as the involved<br />risks.)<br />For the pre-implementation steps which contains the installation of the advanced file it is expected a longer down-time due to DB-changes on the tables T51R2, T51R3 and T51R5.<br /></p> <b>About Risk and Restrictions inherent to Transport Files</b><br /> <p>If you use a transport file (SAR) please note the following:<br />1) Read carefully SAP Note 1318389, where conditions and risks of using transport files are explained in detail.<br />2) There are no updates to transport files when any object in them are modified. Objects contained in transport files may become obsolete without warning.<br />3) Transport files are not valid once their content is available via Support Packages or CLC Packages. The changes may then be installed onlyvia the Packages.<br />4) Text objects are provided in the language in which they were created.Translation is available only via the Packages.<br />5) Changes to the SAP Easy Access menu and Implementation Guide (IMG) are provided only via the Packages.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "PY-CA-TP (Third Party Remittance)"}, {"Key": "Responsible                                                                                         ", "Value": "I826983"}, {"Key": "Processor                                                                                           ", "Value": "I826983"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001678400/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001678400/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001678400/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001678400/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001678400/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001678400/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001678400/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001678400/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001678400/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "SAP_HRRXX500-L6DK174015.zip", "FileSize": "7", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000092932012&iv_version=0003&iv_guid=5B6B44718C3A2A41808231160AE16529"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1130975", "RefComponent": "FI-BL-PT-US", "RefTitle": "Create Third-Party Remittance Posting Run truncates REMSN", "RefUrl": "/notes/1130975 "}, {"RefNumber": "1318389", "RefComponent": "BC-CTS", "RefTitle": "How to Use .SAR/.CAR files and Risks Involved", "RefUrl": "/notes/1318389 "}, {"RefNumber": "1130576", "RefComponent": "PY-US-TP", "RefTitle": "3PR: Garn. EFT pmt. info and 3PR pmt. acknowledgment.", "RefUrl": "/notes/1130576 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HRRXX", "From": "500", "To": "500", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Activity&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_HRRXX&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP HR Reused F...|<br/>| Release 500&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/></P> <OL>1. Read <B>About Risk and Restrictions inherent to Transport Files</B>  section from this note and apply attached transport file (SAR) SAP_HRRXX500-L6DK174015.zip</OL> <OL>2. Execute transaction <B>SE38</B>.</OL> <OL><OL>a) On the subsequent screen, specify program <B>RPURMA00</B>.</OL></OL> <OL><OL>b) Select the <B>Text elements</B> radio button, then choose <B>Change</B>.</OL></OL> <OL><OL>c) On the subsequent screen, choose <B>Change</B>.</OL></OL> <OL><OL>d) On the screen thereafter, perform the following entries:</OL></OL> <P>In column: Enter:<br/>Sym M04<br/>Text Posting run no<br/>dLen as 15<br/>mLen as 18</P> <OL><OL>a) Activate the modified program.</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Caution</B> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Once the source code corrections have been applied, the following  procedure must be performed to extend the existing number range field length. <OL>1. Extend number range field domain length</OL> <OL><OL>a) Execute transaction <B>SNUM</B>.</OL></OL> <OL>2. Execute transaction <B>SE38</B>.</OL> <OL><OL>a) On the subsequent screen, specify program <B>RPURMA00</B>.</OL></OL> <OL><OL>b) Select the <B>Text elements</B> radio button, then choose <B>Change</B>.</OL></OL> <OL><OL>c) On the subsequent screen, choose <B>Change</B>.</OL></OL> <OL><OL>d) On the screen thereafter, perform the following entries:</OL></OL> <P>In column: Enter:<br/>Sym M04<br/>Text Posting run no<br/>dLen as 14<br/>mLen as 18</P> <OL><OL>a) Activate the modified program.</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Caution</B> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Once the source code corrections have been applied, the following  procedure must be performed to extend the existing number range field length. <OL>1. Extend number range field domain length</OL> <OL><OL>a) Execute transaction <B>SNUM</B>.</OL></OL> <OL><OL>b) Object: RP_REMIT</OL></OL> <OL><OL>c) Number ranges</OL></OL> <OL><OL>d) Bank transf.........: REM1</OL></OL> <OL><OL>e) Note the current number of all intervals that exist in your system</OL></OL> <OL><OL>f) Back</OL></OL> <OL><OL>g) Change Status</OL></OL> <OL><OL>h) Set the current number to <B>0</B>.</OL></OL> <OL><OL>i) Save</OL></OL> <OL><OL>j) Back</OL></OL> <OL><OL>k) Change Intervals</OL></OL> <OL><OL>l) Highlight the number range interval</OL></OL> <OL><OL>m) Delete</OL></OL> <OL><OL>n) Repeat steps a through m. and replace REM1 with REM2 in step d.</OL></OL> <OL><OL>o) Repeat steps a. through m. and replace REM1 with REM4 in step d.</OL></OL> <OL><OL>p) Repeat steps a. through o. for every client that has the number ranges</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;set up. <OL><OL>a) Execute transaction <B>SNUM</B></OL></OL> <OL><OL>b) Overview</OL></OL> <OL><OL>c) Highlight object RP_REMIT</OL></OL> <OL><OL>d) Number range object</OL></OL> <OL><OL>e) Delete</OL></OL> <OL><OL>f) Delete number range object RP_REMIT?: Yes</OL></OL> <OL>1. Recreate RP_REMIT number range</OL> <OL><OL>a) Execute transaction <B>SNUM</B></OL></OL> <OL><OL>b) Create</OL></OL> <OL><OL>c) Object RP_REMIT</OL></OL> <OL><OL>d) Short text Transfer admin.</OL></OL> <OL><OL>e) Long text Transfer administration number range</OL></OL> <OL><OL>f) Subobject data element REMNR</OL></OL> <OL><OL>g) Number length domain NUMC10</OL></OL> <OL><OL>h) Warning %&nbsp;&nbsp; 5.0</OL></OL> <OL><OL>i) Main memory buffering checked</OL></OL> <OL><OL>j) No. of numbers in buffer 10</OL></OL> <OL><OL>k) Save</OL></OL> <OL><OL>l) Do you want to set this buffering method? Yes</OL></OL> <OL>2. Create number range</OL> <OL><OL>a) Delete number range object RP_REMIT?: Yes</OL></OL> <OL>3. Recreate RP_REMIT number range</OL> <OL><OL>a) Execute transaction <B>SNUM</B></OL></OL> <OL><OL>b) Create</OL></OL> <OL><OL>c) Object RP_REMIT</OL></OL> <OL><OL>d) Short text Transfer admin.</OL></OL> <OL><OL>e) Long text Transfer administration number range</OL></OL> <OL><OL>f) Subobject data element REMNR</OL></OL> <OL><OL>g) Number length domain NUMC10</OL></OL> <OL><OL>h) Warning %&nbsp;&nbsp; 5.0</OL></OL> <OL><OL>i) Main memory buffering checked</OL></OL> <OL><OL>j) No. of numbers in buffer 10</OL></OL> <OL><OL>k) Save</OL></OL> <OL><OL>l) Do you want to set this buffering method? Yes</OL></OL> <OL>4. Create number range</OL> <OL><OL>a) Execute transaction <B>SNUM</B>.</OL></OL> <OL><OL>b) Object: RP_REMIT</OL></OL> <OL><OL>c) Number Ranges</OL></OL> <OL><OL>d) Subobject data element REMNR</OL></OL> <OL><OL>e) Number length domain NUMC10</OL></OL> <OL><OL>f) Warning %&nbsp;&nbsp; 5.0</OL></OL> <OL><OL>g) Main memory buffering checked</OL></OL> <OL><OL>h) No. of numbers in buffer 10</OL></OL> <OL><OL>i) Save</OL></OL> <OL><OL>j) Do you want to set this buffering method? Yes</OL></OL> <OL>5. Create number range</OL> <OL><OL>a) Execute transaction <B>SNUM</B>.</OL></OL> <OL><OL>b) Object: RP_REMIT</OL></OL> <OL><OL>c) Number Ranges</OL></OL> <OL><OL>d) Bank transf.........: REM1</OL></OL> <OL><OL>e) Change Intervals</OL></OL> <OL><OL>f) Recreate all intervals with <B>From Number</B> and the <B>To Number</B></OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;fields to reflect the new length. <OL><OL>a) Save</OL></OL> <OL><OL>b) Back</OL></OL> <OL><OL>c) Change Status</OL></OL> <OL><OL>d) Modify the current number to reflect the number that you noted above, in</OL></OL> <OL><OL>e) step 2e. of this procedure.</OL></OL> <OL><OL>f) step 2e. of this procedure.</OL></OL> <OL><OL>g) Save</OL></OL> <OL>1. Release number Range buffer</OL> <OL><OL>a) Execute transaction <B>SM56</B> to release the related number range</OL></OL> <OL><OL>b) buffer.</OL></OL> <OL><OL>c) On the subsequent screen, choose the menu path <B>Number Range Buffer</B> -&gt; <B>Reset</B>.</OL></OL> <OL><OL>d) In all applicable clients of your system, modify the following:</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Object RP_REMIT <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Subobject REM1 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Range The current number's number range, e.g. \"01,\" or \"02.\" <OL>1. Repeat steps 5a. through 5c. and replace REM1 with REM2 in step 5c.</OL> <OL>2. Repeat steps 5a. through 5c. and replace REM1 with REM4 in step 5c.</OL> <P></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}