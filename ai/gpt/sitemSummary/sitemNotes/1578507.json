{"Request": {"Number": "1578507", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 782, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000009366102017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001578507?language=E&token=BDBBFF076BE79A89CC2A7A4F4D3ECCC3"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001578507", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001578507/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1578507"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "25.05.2011"}, "SAPComponentKey": {"_label": "Component", "value": "FI-GL-REO-BF"}, "SAPComponentKeyText": {"_label": "Component", "value": "General Ledger Reorganization Basic Functions"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Accounting", "value": "FI-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Reorganization", "value": "FI-GL-REO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-REO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Reorganization Basic Functions", "value": "FI-GL-REO-BF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-REO-BF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1578507 - Changing posting period: Warning msg due to reorg missing"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You experience one of the following symptoms:<br />1) During a period change you expect a warning message due to an existing reorganization. However, the system does not issue this message.<br />2) Both for the past and the future, the system does not always track the changed period during a reorganization, both for the past and the future.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Period tracking<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Implement the corrections. First, execute the specified manual activity.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D021112)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001578507/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001578507/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001578507/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001578507/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001578507/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001578507/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001578507/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001578507/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001578507/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1471153", "RefComponent": "FI-GL-REO", "RefTitle": "Composite note for profit center and FM reorganization", "RefUrl": "/notes/1471153"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1471153", "RefComponent": "FI-GL-REO", "RefTitle": "Composite note for profit center and FM reorganization", "RefUrl": "/notes/1471153 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "605", "To": "605", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 605", "SupportPackage": "SAPKH60505", "URL": "/supportpackage/SAPKH60505"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 1, "URL": "/corrins/0001578507/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_APPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Application&nbsp;&nbsp; |<br/>| Release 605&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKH60504&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/>You must perform the following steps according to the sequence listed.</P> <OL>1. Use transaction SE80 to create the function group 'FAGL_R_PERIOD_TRACK'. To do this, proceed as follows:</OL> <UL><LI>Select \"Function Group\".</LI></UL> <UL><LI>In the next field, enter the value 'FAGL_R_PERIOD_TRACK' and choose \"Display\".</LI></UL> <UL><LI>On the following dialog box, confirm the creation of the object 'FAGL_R_PERIOD_TRACK'.</LI></UL> <UL><LI>In the next dialog box, enter the short text \"Reorg:&nbsp;&nbsp;Period Tracking\" and save your change.</LI></UL> <UL><LI> On the next dialog box, enter the package 'FAGL_REORGANIZATION' and save again.</LI></UL> <OL>2. Reassign the function module 'FAGL_REORG_CHECK_COFI' using transaction SE37:</OL> <UL><LI>Enter 'FAGL_REORG_CHECK_COFI' in the \"Function module\" field and choose  \"Function module -&gt; Other Functions -&gt; Reassign...\" in the menu.</LI></UL> <UL><LI>In the dialog box, enter the value 'FAGL_R_PERIOD_TRACK' in the \"New function group\" field and choose \"Reassign\".</LI></UL> <OL>3. Use transaction SE37 to create the function module 'FAGL_R_CHECK_PERIODS_FUTURE':</OL> <UL><LI>Enter 'FAGL_R_CHECK_PERIODS_FUTURE' in the \"Function module\" field and choose \"Create\".</LI></UL> <UL><LI>In the dialog box, enter</LI></UL> <UL><UL><LI>the value 'FAGL_R_PERIOD_TRACK' in the \"Function group\" field.</LI></UL></UL> <UL><UL><LI>Enter the short text 'Check if open periods are in the future of a reorg plan'.</LI></UL></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Choose \"Save\" and exit the transaction again. <OL>1. Use transaction SE37 to create the function module 'FAGL_R_CHECK_PERIODS_PREVIOUS':</OL> <UL><LI>Enter 'FAGL_R_CHECK_PERIODS_PREVIOUS' in the \"Function module\" field and choose \"Create\".</LI></UL> <UL><LI>In the dialog box, enter</LI></UL> <UL><UL><LI>the value 'FAGL_R_PERIOD_TRACK' in the \"Function group\" field.</LI></UL></UL> <UL><UL><LI>Enter the short text 'Check if previous periods have been opened'.</LI></UL></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Choose \"Save\" and exit the transaction again. <OL>1. Reassign the function module 'FAGL_R_INIT_PERIOD_TRACK' using transaction SE37:</OL> <UL><LI>Enter 'FAGL_R_INIT_PERIOD_TRACK' in the \"Function module\" field and  choose \"Function module -&gt; Other Functions -&gt; Reassign...\" in the menu.</LI></UL> <UL><LI>In the dialog box, enter the value 'FAGL_R_PERIOD_TRACK' in the \"New function group\" field and choose \"Reassign\".</LI></UL> <OL>2. Reassign the function module 'FAGL_R_WRITE_PERIOD_TRACK' using transaction SE37:</OL> <UL><LI>Enter 'FAGL_R_WRITE_PERIOD_TRACK' in the \"Function module\" field and  choose \"Function module -&gt; Other Functions -&gt; Reassign...\" in the menu.</LI></UL> <UL><LI>In the dialog box, enter the value 'FAGL_R_PERIOD_TRACK' in the \"New function group\" field and choose \"Reassign\".</LI></UL> <OL>3. Use transaction SE37 to create the function module 'FAGL_R_CHECK_PERIODS':</OL> <UL><LI>Enter 'FAGL_R_CHECK_PERIODS' in the \"Function module\" field and choose \"Create\".</LI></UL> <UL><LI>In the dialog box, enter</LI></UL> <UL><UL><LI>the value 'FAGL_R_PERIOD_TRACK' in the \"Function group\" field.</LI></UL></UL> <UL><UL><LI>Enter the short text 'Check if periods are open in the past or future'.</LI></UL></UL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Choose \"Save\" and exit the transaction again. <OL>1. Use transaction SE37 to delete the function module 'FAGL_R_CHECK_FUTURE_PERIODS':</OL> <UL><LI>Enter 'FAGL_R_CHECK_FUTURE_PERIODS' in the \"Function module\" field and choose \"Delete\".</LI></UL> <UL><LI>In the dialog box, confirm the deletion by choosing \"Delete\" even though the function module is still used.</LI></UL> <OL>2. Use transaction SE37 to delete the function module 'FAGL_R_CHECK_PREVIOUS_PERIODS':</OL> <UL><LI>Enter 'FAGL_R_CHECK_PREVIOUS_PERIODS' in the \"Function module\" field and choose \"Delete\".</LI></UL> <UL><LI>In the dialog box, confirm the deletion by choosing \"Delete\" even though the function module is still used.</LI></UL> <P></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 3, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1549019 ", "URL": "/notes/1549019 ", "Title": "Performance improvement regarding checks for reorg plan", "Component": "FI-GL-REO-BF"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1552845 ", "URL": "/notes/1552845 ", "Title": "Authorization check for plan overview", "Component": "FI-GL-REO-BF"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}