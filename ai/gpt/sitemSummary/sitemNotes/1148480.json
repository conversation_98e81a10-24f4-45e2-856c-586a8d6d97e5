{"Request": {"Number": "1148480", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 674, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016479542017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001148480?language=E&token=2F70B5AB259AA9ED50A7E32384DC356D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001148480", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001148480/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1148480"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 50}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.03.2012"}, "SAPComponentKey": {"_label": "Component", "value": "BC-OP-AS4"}, "SAPComponentKeyText": {"_label": "Component", "value": "IBM AS/400"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Operating System Platforms", "value": "BC-OP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "IBM AS/400", "value": "BC-OP-AS4", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP-AS4*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1148480 - IBM i: Known Issues with V6R1Mx"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Issues which may occur with operating system release IBM i 6.1 will be collected together in this note. For the sake of clarity, the issues will be subdivided as follows: <BR/> A. Issues resolved without an IBM PTF<BR/> <BR/> B. Issues resolved by the latest IBM Infoapar<BR/> <BR/> C. Issues resolved by a PTF which is not (yet) included in the Infoapar.<BR/> <BR/> The note will be updated when a new Infoapar is produced,make sure you check the latest version.<BR/></p> <b>Section A: Issues resolved without an IBM PTF</b><br /> <OL>1. The first operation on an SAP system after upgrading the operating sytem to V6R1Mx encounters a significant delay of several minutes.</OL> <OL>2. Severe performance problems on accesses to tables in the database library are encountered after upgrading from V5R3 or lower to V6R1Mx.</OL> <OL>3. Severe performance problems on accesses to tables in the database library are encountered after upgrading from V5R4 or lower to V6R1Mx.</OL> <OL>4. Message CPF32A6 \"Cross-reference notification 2024 for request 514, problem type 1\" after the upgrade to V6R1Mx.</OL> <OL>5. Problems with the memory-based database monitor (transactions ST04, DB4COCKPIT) after the upgrade to IBM i 6.1, for example message CPF9898 \"DATABASE MONITOR HAS BEEN HALTED AND MUST BE CLEARED\".</OL><OL>6. If a first installation of an SAP system is attempted directly after a new operating system installation, then the R3GROUP user is set up with insufficient authorisation, which can lead to MCH1001 messages from CRTR3SYS at a later point in the installation.</OL> <OL>7. After the OS upgrade, some OS APIs need execute access for user R3GROUP, otherwise some SAP programs will fail.</OL> <OL>8. In DB4COCKPIT or DBACOCKPIT, the display \"SQE Indexes Advised\" produces the error message D4-608: \"No information on advised indexes available\". The dev-trace contains the messages:<br /> C&#x00A0;&#x00A0;*** ERROR =&gt; Error -551 in function db_open&#x00A0;&#x00A0;[dbsldb4.cpp&#x00A0;&#x00A0;10847]<br />B&#x00A0;&#x00A0;***LOG BY2=&gt; sql error -551&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; performing OPC [dbds&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 398]<br />B&#x00A0;&#x00A0;***LOG BY0=&gt; Not authorized to object SYSIXADV in QSYS2 type *FILE.MSGID= Job=&lt;qualified job name&gt; [dbds&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 398]<br />B&#x00A0;&#x00A0;***LOG BY1=&gt; sql error -551&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; [dbacds&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1496]</OL> <OL>9. SQL0104 on export and import for SQL statements which contain the field name CLIENT.</OL> <OL>10. SQL0356 when installing usage type \"Java Development Infrastructure\"</OL> <OL>11. SAP Patches cannot be applied with command APYR3FIX between machines on 6.1. and those on older OP system levels.</OL> <OL>12. 4.6D-EXT: System fails to come up after upgrade to IBM i 6.1. DISP joblog displays the message:MCH0609, \"Space address is not a teraspace storage address\". Otherwise some SAP programs will fail.</OL><OL>13. An MCH1809 message (duplicate key value in existing data space entry for unique key index) occurs in connection with an SQL0803 message (duplicate key value specified) in the joblog.</OL> <OL>14. SQL0420 errors in transaction KEDV when trying to create a new level of integration. The failing statement is a \"CALL QCMDEXC\".</OL> <OL>15. Out-of-memory issues occur with the Toolbox JDBC Driver, for example, during deployment of large packages or database creation using JLoad.</OL> <p></p> <b>Section B: Issues resolved by the latest IBM Infoapar</b><br /> <OL>1. Installation or upgrade fails while adding entries into the services table, or after installation the system will not start with error: NiPGetServByName2: service 'sapms&lt;SID&gt;' not found: getservbyname_r</OL> <OL>2. The SAP system does not start. All work processes die and in the joblogs there is an MCH3601 from program QXDAEVT.</OL> <OL>3. CHKR3PTF shows a missing PTF for product 5761JV1, which can only be applied after installation of option 13 of this product.</OL> <OL>4. Procedures and programs which use the CALLCMD-interface or RUNRMTCMD abort with an MCH0601 message in module QNMRCPP1.</OL> <OL>5. Very high CPU utilisation, often accompanied by many SQL0913 and CPF5027 messages indicating deadlock situations.</OL> <OL>6. SAP system fails to start after upgrading to 6.1, returning MCH3203 messages, if the system journals IFS files.</OL> <OL>7. After implementation of the Hiper-Fixpack 29, SAP systems start begin to display problems in the PASE area. Work-process loops can occur and LICLogs indicating problems with the PASE loader (major code 0200) are created.</OL> <OL>8. A pair of SQL0331/SQL0000 messages appear in the OS-Joblog, indicating a problem with character conversion. However in the dev-trace only the message SQL0904 (resource limit exceeded) with Reason Code = 1 is reported.</OL> <OL>9. After an unplanned IPL with a resultant requirement for data recovery it's subsequently found that some cluster tables (e.g. RFBLG) appear to contain corrupted data.</OL> <OL>10. SQL0406 on ALTER TABLE when the table contains double-byte character data. The error has been seen when trying to implement the support packages SAPKB70013 &amp; SAPKB70014 but may occur elsewhere too.</OL> <OL>11. MCH3203 in CALLDBMAINTFOROPENOROPTIMIZE&#x00A0;&#x00A0;during attempt to copy or load a BW Infocube.</OL> <OL>12. The fourth start of a SAP system after an IPL fails. In the dev-traces messages similar to the following can be found.<br /> I&#x00A0;&#x00A0;*** ERROR =&gt; ShmIntegrateKeyToAdm: total SHM size too small (0 / 2741224 / 2741224) (key=17; guard pages: 0/0; page size: 4096) [shmux.c 1067]<BR/> I&#x00A0;&#x00A0;*** ERROR =&gt; ShmCreate: failed to enter SHM to adm tables [shmux.c 1993]<br />I&#x00A0;&#x00A0;*** WARNING =&gt;&#x00A0;&#x00A0;&#x00A0;&#x00A0;==&gt; SHM was created, maybe cleanipc is needed to clean it up [shmux.c 1995]<BR/> M&#x00A0;&#x00A0;*** ERROR =&gt; ThShMCreate: ShmCreate SHM_ROLL_ADM_KEY [thxxhead.c 2724]<BR/> M&#x00A0;&#x00A0;*** ERROR =&gt; ThIPCInit: ThShMCreate [thxxhead.c&#x00A0;&#x00A0; 2148]<BR/> M&#x00A0;&#x00A0;***LOG R19=&gt; ThInit, ThIPCInit ( TSKH-IPC-000001) [thxxhead.c 1585]<BR/> M&#x00A0;&#x00A0;in_ThErrHandle: 1<BR/> M&#x00A0;&#x00A0;*** ERROR =&gt; ThInit: ThIPCInit (step 1, th_errno 17, action 3, level 1) [thxxhead.c 10631]</OL> <OL>13. After implementation of PTFs some system files are missing in QSYS2. Affected files are: SQLPRIKEYS, SQLFORKEYS, REF_CST2, REF_CST1, CHECK_CSTS, TABLE_CSTS, SYSCST, SYSCSTCOL, SYSCHKCST, SYSCSTDEP and SYSREFCST.</OL> <p></p> <b>C. Issues resolved by a PTF which is not (yet) included in the Infoapar</b><br /> <p>No issues currently known.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>AS/400, OS/400, i5/os, iSeries, OS upgrade, V6R1M0, V6R1M1, V6R1, STROBJCVN</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><b>Section A: Issues resolved without an IBM PTF</b><br /> <OL>1. When restoring programs onto IBM i 6.1 that were compiled for earlier releases, the programs need to be converted. This can be done manually by using the STROBJCVN, otherwise the conversion will take place automatically at first touch. The conversion is only needed for program objects in libraries, executables in the integrated file system (using the PASE runtime) are not affected. Internal tests have shown conversion times of 30 minutes for a 4.6D kernel library and 2 minutes for a 7.10 kernel.<br /><B>Note</B> that also conversion of Java programs that correspond to classes, jars and names of an existing Java application in the integrated file system&#x00A0;&#x00A0;can lead to significant conversion time.</OL> <OL>2. Changes in the DB File-Header.</OL> <OL>3. DB file statistics are invalidated by the upgrade and need to be rebuilt</OL> <OL>4. In some rare cases the system cross-reference files can be corrupted during the upgrade to IBM i 6.1. This problem has been fixed by IBM with PTF SI29811, but the PTF is not included in some early shipments of IBM i 6.1. Installing the PTF after the upgrade to IBM i 6.1 does not solve problem.</OL> <OL>5. The memory-based database monitor is no longer supported in IBM i 6.1. Instead of it, the SQE Plan Cache dump must be used. The kernel patch levels that are required to support IBM i 6.1 ensure that the SQE Plan Cache dump is used as default.</OL> <OL>6. Change to the *PUBLIC authorisation of some APIs.</OL> <OL>7. Change in the authorisation of some APIs.</OL> <OL>8. If system value QCRTAUT is set to *EXCLUDE, insufficient authority will be assigned to the system file SYSIXADV during the operating system upgrade.</OL> <OL>9. For more information, see SAP Note <B>1134188</B>.</OL> <OL>10. For more information, see SAP Note <B>1138824</B>.</OL> <OL>11. For more information, see&#x00A0;&#x00A0;to&#x00A0;&#x00A0;SAP Note <B>1167276</B> . If possible you should apply it before upgrading to 6.1.</OL> <OL>12. Incompatibility with release 6.1 of the operating system in some new semaphore processing.</OL> <OL>13. The application is performing a block insert. The MCH1809 message contains additional information, not supplied in the SQL0803 messages in such cases.</OL> <OL>14. Change in the CALL QCMDEXC interface for releases after V5R4</OL> <OL>15. You have implemented the Toolbox JDBC Driver / JTOpen (jt400.jar) with a version between JTOpen 6.2 and JTOpen 7.2 inclusive. Later releases will not exhibit this problem.</OL> <p></p> <b>Section B: Issues resolved by the latest IBM Infoapar</b><br /> <OL>1. The problem's general symptom is that entries recently added to the services table are not found by APIs such as getservbyport, getservbyname and other similar APIs.</OL> <OL>2. Incompatibility caused by new processing introduced in the latest DB-Fixpack.</OL> <OL>3. Option 13 of product 5761JV1 is now a prerequisite for running SAP NetWeaver Java stacks.</OL> <OL>4. Incorrect length specified for ip-Address during host name resolution.</OL> <OL>5. Loop in QSQCLOSE</OL> <OL>6. PASE signal SIGILL (for exception message MCH3203) occurs for an attempt to memory-map (mmap or shmat) an IFS file that is journalled.</OL> <OL>7. The PTF MF45977, included in the Hiper-Fixpack, causes the problem.</OL> <OL>8. SQL package corruption on double-byte character systems</OL> <OL>9. The system was incorrectly minimizing journal data for an update of variable length data, when that update resulted in the reuse of storage from another record's old variable length storage. The recovery of the file resulted in the incorrectly minimized data being applied to the record and the resulting record variable length data was incorrect.</OL> <OL>10. The copy file function is copying UCS2 and UTF-16 fields incorrectly.</OL> <OL>11. Optimizer failing to recognise a status change under some circumstances</OL> <OL>12. The function shmctl does not work for shmid 203.</OL> <OL>13. This issue can lead to symptoms indicating database inconsistencies. The problem is described in detail in IBM APAR SE44385.</OL> <p></p> <b>C. Issues resolved by a PTF which is not (yet) included in the Infoapar</b><br /> <p>No issues currently known.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>General remark: In order to avoid problems with IBM i 6.1, the following patches must be applied:<br /> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;| 4.6D | 6.40 | 7.00 | 7.10<br />&#x00A0;&#x00A0;----------------+------+------+------+------<br />&#x00A0;&#x00A0;APYR3FIX&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 |&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 |&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 |&#x00A0;&#x00A0;&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;APYR3KRN&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;- |&#x00A0;&#x00A0; 11 |&#x00A0;&#x00A0;&#x00A0;&#x00A0;- |&#x00A0;&#x00A0;&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;AS4FIXFILE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 |&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 |&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 |&#x00A0;&#x00A0;&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;AS4RMTCCMS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 |&#x00A0;&#x00A0; 12 |&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 |&#x00A0;&#x00A0;&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;DBSLDB4RMT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 |&#x00A0;&#x00A0;&#x00A0;&#x00A0;- |&#x00A0;&#x00A0;&#x00A0;&#x00A0;- |&#x00A0;&#x00A0;&#x00A0;&#x00A0;-<BR/> &#x00A0;&#x00A0;disp+work (DW)&#x00A0;&#x00A0;| 2410 |&#x00A0;&#x00A0;224 |&#x00A0;&#x00A0;150 |&#x00A0;&#x00A0; 94<br />&#x00A0;&#x00A0;ILE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0;- |&#x00A0;&#x00A0;&#x00A0;&#x00A0;- |&#x00A0;&#x00A0;&#x00A0;&#x00A0;- |&#x00A0;&#x00A0; 11<br />&#x00A0;&#x00A0;JDBCEXIT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;- |&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 |&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 |&#x00A0;&#x00A0;&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;LIB_DBSL&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;| 2380 |&#x00A0;&#x00A0;231 |&#x00A0;&#x00A0;149 |&#x00A0;&#x00A0; 94<br />&#x00A0;&#x00A0;LODR3KRN&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 |&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 |&#x00A0;&#x00A0;&#x00A0;&#x00A0;- |&#x00A0;&#x00A0;&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;LODSAPKRN&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0;- |&#x00A0;&#x00A0;&#x00A0;&#x00A0;- |&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 |&#x00A0;&#x00A0;&#x00A0;&#x00A0;-<br />&#x00A0;&#x00A0;R3trans&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; | 2367 |&#x00A0;&#x00A0;216 |&#x00A0;&#x00A0;144 |&#x00A0;&#x00A0; 84<br />&#x00A0;&#x00A0;tp&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;| 2366 |&#x00A0;&#x00A0;232 |&#x00A0;&#x00A0;145 |&#x00A0;&#x00A0;100<br /><br />When using Java, the following levels of Software Deployment Tools are required:</p> <UL><LI>6.40: SAP J2EE Engine 6.40: SP22</LI></UL> <UL><LI>7.00: SAP SOFTWARE DELIVERY MANAGER 7.00: SP16</LI></UL> <UL><LI>7.10: SP7</LI></UL> <p><br /><B>Note:</B> SAP releases 4.5B and earlier are not supported to run with operating system release IBM i 6.1.<br /></p> <b>Solutions for the previously described issues:</b><br /> <p></p> <b>Section A: Issues resolved without an IBM PTF</b><br /> <UL><LI><B>Issues solved by manual intervention </B></LI></UL> <OL>1. SAP has provided changes to the kernel patch tools, so that the necessary conversion of program objects takes place when loading the kernel, but not at first touch. Details are explained in SAP Note 1117101. If you upgrade your kernels prior to the upgrade to IBM i 6.1, you should run the STROBJCVN command for all kernel libraries on your system right after the upgrade in order to avoid delays when starting SAP instances.<br /><B>Note</B>: You can avoid conversion of the mentioned Java objects by switching from Classic Java technology to IBM Technology for Java (IT4J) with your upgrade to IBM i 6.1. In order to do so ensure the following:</OL> <UL><UL><LI>IT4J is installed.</LI></UL></UL> <UL><UL><LI>The respective SAP Netweaver application servers have been reconfigured to use IT4J 1.4.2 (see note 1161275 for details).</LI></UL></UL> <UL><UL><LI>Do <B>NOT</B> run STROBJCVN against the entire IFS.</LI></UL></UL> <UL><UL><LI>Do run STROBJCVN against the directory trees for any Java applications that will continue to use the Classic JVM.</LI></UL></UL> <OL>2. Run STROBJCNV against the database libraries immediately after the upgrade and prior to bringing up your SAP systems.</OL> <OL>3. For more information about the utility SAP supplies to solve this issue, see <B>SAP Note 1264859</B>.</OL> <OL>4. If you experience the problem, you need to run RCLSTG SELECT(*DBXREF) in order to fix the system cross reference files. The system needs to be in restricted state to run that command. To find out whether you have run into the problem, enter GO LICPGM and select option 5 \"Prepare for install\". Select the option \"Verify system objects\". If the final message is \"Task to prepare for install successfully completed\" (CPC3D8A), you do not need to perform the RCLSTG.</OL> <OL>5. Details about using the SQE Plan Cache dump can be found in SAP Note 1083218. If you cannot install the patch levels that are listed in that SAP Note, ensure that profile parameter as4/dbmon/enable = 0 is set. If you are running a Java instance, the profile parameter must be set in the Default profile.</OL> <OL>6. For the solution, see <B>SAP Note 175852</B>.</OL> <OL>7. For the solution, see <B>SAP Note 175852</B>.</OL> <OL>8. If system value QCRTAUT is set to *EXCLUDE, you must grant *PUBLIC authority *CHANGE to system file SYSIXADV manually. The solution is described in detail in <B>SAP Note 1699187</B>.</OL> <UL><LI><B>Issues solved by an SAP correction</B></LI></UL> <OL>9. The problem has been solved by means of the patches to R3TRANS and TP which are mentioned in the table above.</OL> <OL>10. The problem has been solved by implementation of the support packages listed above for the Software Deployment Tools.</OL> <OL>11. The problem is solved with the APYR3FIX patch levels listed in the table above.</OL> <OL>12. The problem is solved with the 4.6D DW patch level listed in the table above.</OL> <OL>13. The MCH1809 supplies additional information, not found in the SQL0803, for block inserts. If a short dump is produced by the SAP application, due to the duplicate records insert, then this needs to be investigated first by SAP. If there is no application dump, then the MCH1809 message can be ignored.</OL> <OL>14. The problem is solved with the correction specified in note 1526047.</OL> <OL>15. The problem and its solution are described in detail in &gt;Z1&gt;SAP note 1548414.</OL> <p></p> <b>Section B: Issues resolved by the latest IBM Infoapar</b><br /> <OL>1. This problem is solved by applying PTF MF45089 (CUME 8288). An IPL is also necessary, therefore make sure the PTF is already applied in the system before starting any installation.</OL> <OL>2. Please ensure that the following PTFs have been installed, in order to avoid both the original error and others which were encountered in the course of the problem's solution:</OL> <UL><UL><LI>latest CUM tape</LI></UL></UL> <UL><UL><LI>latest DB-Fixpack</LI></UL></UL> <UL><UL><LI>latest Hiper-Fixpack</LI></UL></UL> <UL><UL><LI>latest PASE PTFs</LI></UL></UL> <UL><UL><LI>SI32551 (CUME 9111) and SI32608 (CUME 8288)</LI></UL></UL> <OL>3. Install the IBM IT4J JDK on your system. This is done by installing option 13 of licensed program 5761JV1. If this is not part of your IBM i OS installation media, you can order it by ordering PTF SI32444. After the product option is installed, you can install the missing PTFs for a successful CHKR3PTF. If a Java system is not installed, then you can simply ignore the message from CHKR3PTF for this PTF, if you wish, as the product is not required for non-Java systems.</OL> <OL>4. Install the latest Infoapar. This will include PTF SI33647, which solves this problem.</OL> <OL>5. Problem is solved by PTF SI33791 which has been superseded by SI33900.</OL> <OL>6. Problem is solved by PTF MF46062, which also solves a problem with CORE file sizes.</OL> <OL>7. Do not install Hiper-Fixpack 29. Hiper-Fixpack 30 does not include the problem PTF. If Hiper-Fixpack 29 has already been installed then remove the PTF MF45977 or install the superseding PTF MF46393, when it becomes available.</OL> <OL>8. The problem is solved with PTF SI35069.</OL> <OL>9. PTF MF46608 precludes the problem for the future but the damaged records will have to be repaired by the SAP application specialists.</OL> <OL>10. The problem is solved by PTF SI34902.</OL> <OL>11. The problem is solved by PTF MF47078 (610) or MF47142( 611).</OL> <OL>12. The problem is solved by PTF MF49571 (610) or MF49572 (611)</OL> <OL>13. The originating problem is solved by application of the PTF SI40273 from DB-Fixpack 15, but the files still have to be recreated manually. An IBM Knowledge Base document with document number 317497421 and the title: \"SQL0204 for SYSCOLUMNS or SYSTABLES\" describes how to do this. The document can be found at: http://www-912.ibm.com/s_dir/slkbase.nsf/slkbase</OL> <p></p> <b>C. Issues resolved by a PTF which is not (yet) included in the Infoapar</b><br /> <p>No issues currently known.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D055775)"}, {"Key": "Processor                                                                                           ", "Value": "D019267"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001148480/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001148480/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001148480/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001148480/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001148480/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001148480/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001148480/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001148480/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001148480/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "977906", "RefComponent": "BC-INS-AS4", "RefTitle": "6.20/6.40 Patch Collection Inst. on IBM eServer iSeries", "RefUrl": "/notes/977906"}, {"RefNumber": "68440", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: How do I upgrade to a later OS release?", "RefUrl": "/notes/68440"}, {"RefNumber": "175852", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: Authorization problems in SAPOSCOL and CRTR3SYS", "RefUrl": "/notes/175852"}, {"RefNumber": "1699187", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: No informationen about recommended indexes", "RefUrl": "/notes/1699187"}, {"RefNumber": "1548414", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: OutOfMemory error due to JDBC driver", "RefUrl": "/notes/1548414"}, {"RefNumber": "1526047", "RefComponent": "CO-PA-TO", "RefTitle": "KEDV: SQL error 420 when creating a summarization level", "RefUrl": "/notes/1526047"}, {"RefNumber": "1489021", "RefComponent": "BC-INS-AS4", "RefTitle": "SAP NetWeaver 7.0 including EHP3:IBM DB2 for i", "RefUrl": "/notes/1489021"}, {"RefNumber": "1430134", "RefComponent": "BC-INS-AS4", "RefTitle": "SAP NetWeaver 7.0 EHP2/Business Suite 7 - IBM i", "RefUrl": "/notes/1430134"}, {"RefNumber": "1277450", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Importing 46D patches into i5/OS Version 6.1", "RefUrl": "/notes/1277450"}, {"RefNumber": "1264859", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: Deterioration in performance after upgrade", "RefUrl": "/notes/1264859"}, {"RefNumber": "1247731", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1247731"}, {"RefNumber": "1234445", "RefComponent": "BC-INS-AS4", "RefTitle": "SAP NetWeaver 7.0 EHP1/Business Suite 7 - IBM i", "RefUrl": "/notes/1234445"}, {"RefNumber": "1161275", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1161275"}, {"RefNumber": "1138824", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: Installation of usage type DI fails with SQL-356", "RefUrl": "/notes/1138824"}, {"RefNumber": "1134777", "RefComponent": "BC-INS-AS4", "RefTitle": "SAP NetWeaver 7.0 SR3 Installation: IBM DB2 for i5/OS", "RefUrl": "/notes/1134777"}, {"RefNumber": "1134188", "RefComponent": "BC-CTS-TLS", "RefTitle": "DB4: SQL error for statements with CLIENT as field name", "RefUrl": "/notes/1134188"}, {"RefNumber": "1083218", "RefComponent": "BC-DB-DB4", "RefTitle": "iSeries: Analysis of plan cache for DB2 on i5/OS", "RefUrl": "/notes/1083218"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "977906", "RefComponent": "BC-INS-AS4", "RefTitle": "6.20/6.40 Patch Collection Inst. on IBM eServer iSeries", "RefUrl": "/notes/977906 "}, {"RefNumber": "1277450", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Importing 46D patches into i5/OS Version 6.1", "RefUrl": "/notes/1277450 "}, {"RefNumber": "175852", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: Authorization problems in SAPOSCOL and CRTR3SYS", "RefUrl": "/notes/175852 "}, {"RefNumber": "1234445", "RefComponent": "BC-INS-AS4", "RefTitle": "SAP NetWeaver 7.0 EHP1/Business Suite 7 - IBM i", "RefUrl": "/notes/1234445 "}, {"RefNumber": "1489021", "RefComponent": "BC-INS-AS4", "RefTitle": "SAP NetWeaver 7.0 including EHP3:IBM DB2 for i", "RefUrl": "/notes/1489021 "}, {"RefNumber": "68440", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: How do I upgrade to a later OS release?", "RefUrl": "/notes/68440 "}, {"RefNumber": "1430134", "RefComponent": "BC-INS-AS4", "RefTitle": "SAP NetWeaver 7.0 EHP2/Business Suite 7 - IBM i", "RefUrl": "/notes/1430134 "}, {"RefNumber": "1134777", "RefComponent": "BC-INS-AS4", "RefTitle": "SAP NetWeaver 7.0 SR3 Installation: IBM DB2 for i5/OS", "RefUrl": "/notes/1134777 "}, {"RefNumber": "1699187", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: No informationen about recommended indexes", "RefUrl": "/notes/1699187 "}, {"RefNumber": "1264859", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: Deterioration in performance after upgrade", "RefUrl": "/notes/1264859 "}, {"RefNumber": "1138824", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: Installation of usage type DI fails with SQL-356", "RefUrl": "/notes/1138824 "}, {"RefNumber": "1548414", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: OutOfMemory error due to JDBC driver", "RefUrl": "/notes/1548414 "}, {"RefNumber": "1083218", "RefComponent": "BC-DB-DB4", "RefTitle": "iSeries: Analysis of plan cache for DB2 on i5/OS", "RefUrl": "/notes/1083218 "}, {"RefNumber": "1526047", "RefComponent": "CO-PA-TO", "RefTitle": "KEDV: SQL error 420 when creating a summarization level", "RefUrl": "/notes/1526047 "}, {"RefNumber": "1134188", "RefComponent": "BC-CTS-TLS", "RefTitle": "DB4: SQL error for statements with CLIENT as field name", "RefUrl": "/notes/1134188 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "46C", "To": "46D", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "640", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "701", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "711", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}