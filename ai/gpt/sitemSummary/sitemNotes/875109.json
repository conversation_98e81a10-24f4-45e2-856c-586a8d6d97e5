{"Request": {"Number": "875109", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1099, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000004959272017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000875109?language=E&token=024D382687437E780367AF3796149751"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000875109", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000875109/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "875109"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 13}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.02.2007"}, "SAPComponentKey": {"_label": "Component", "value": "PY-BR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Brazil"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Brazil", "value": "PY-BR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-BR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "875109 - SEFIP: code updates"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>SEFIP will change from version 7.0 to 8.0, according to layout publised in July/2005.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Brazil legal reports HBRSEF00 INSS FGTS<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Code update.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Please apply the corresponding support package.<br /><br />This note cannot be completely applied by the note assistant. As an alternative, you can install one of the attached files according to your release and then apply the correction instructions below using the note assistant. Customers with release 4.70 can install the attached files L6BK099084_470.CAR (this one first) and<br />L6BK100663_470.CAR; in this case, no further steps are required.<br /><br />This note implement some small changes required by version 8.0, which are still compatible with version 7.0.<br /><br />Following changes have been done:<br /><br />- Collecting code 906 no longer exists;<br />- Collecting code 211 replaces 911;<br />- Creation of the collecting code 135 (Recolhimento e/ou declara&#x00E7;&#x00E3;o ao FGTS e informa&#x00E7;&#x00F5;es &#x00E0; Previd&#x00EA;ncia Social relativas ao trabalhador avulso n&#x00E3;o portu&#x00E1;rio)<br />- Creation of category 26 (Dirigente sindical, em rela&#x00E7;&#x00E3;o ao adicional pago pelo sindicato; magistrado classista tempor&#x00E1;rio da Justi&#x00E7;a do Trabalho; magistrado dos Tribunais Eleitorais, quando nas 3 situa&#x00E7;&#x00F5;es, for mantida a qualidade de seg. empregado (sem FGTS)), for additional payment information. Please maintain view V_T7brct accordingly;<br />- The company should identify the collecting, correction or confirmation via field Modality (record type 0, field 18), now available on the selection screen. For this reason the following dictionary changes were needed:<br /><br />. creation of domain PBR_SFMOD, type CHAR(1), containing these fixed values:<br /><br />Blank Recolhimento ao FGTS e Declara&#x00E7;&#x00E3;o &#x00E0; Previd&#x00EA;ncia<br />1 Declara&#x00E7;&#x00E3;o para FGTS e &#x00E0; Previd&#x00EA;ncia<br />7 Retifica&#x00E7;&#x00E3;o de Recolhimento ao FGTS e Declara&#x00E7;&#x00E3;o &#x00E0; Previd&#x00EA;ncia<br />8 Retifica&#x00E7;&#x00E3;o de Declara&#x00E7;&#x00E3;o ao FGTS e &#x00E0; Previd&#x00EA;ncia <br />9 Confirma&#x00E7;&#x00E3;o Informa&#x00E7;&#x00F5;es Anteriores # Rec/Decl ao FGTS e&#x00A0;&#x00A0;Decl &#x00E0; Previd&#x00EA;ncia <br /><br />. creation of data element PBR_SFMOD, with domain PBR_SFMOD<br /><br />. substitution of field SMODP by SFMOD with data element PBR_SFMOD in structure HBRSE00</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D037673"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I811676)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000875109/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000875109/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000875109/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000875109/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000875109/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000875109/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000875109/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000875109/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000875109/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "L9CK193531_46C.CAR", "FileSize": "8", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000332192005&iv_version=0013&iv_guid=0D5360E5776E1E40AC57759460915AC4"}, {"FileName": "L6BK100663_470.CAR", "FileSize": "50", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000332192005&iv_version=0013&iv_guid=3D8CF534A86C994F994808838226142F"}, {"FileName": "L9BK129868_46B.CAR", "FileSize": "8", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000332192005&iv_version=0013&iv_guid=70FFA005511E2F4594D3256B40E5D8A4"}, {"FileName": "L4DK122674_45B.CAR", "FileSize": "4", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000332192005&iv_version=0013&iv_guid=77B6F92CD37DE843ABFF2ADA65B913B1"}, {"FileName": "L6BK099084_470.CAR", "FileSize": "5", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000332192005&iv_version=0013&iv_guid=B9B4CDC62E8AB044865864BDBE9FC76F"}, {"FileName": "L6DK030644_500.CAR", "FileSize": "7", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000332192005&iv_version=0013&iv_guid=420D3CDAE4D2D74ABE09DEDACDA8A14E"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 45B", "SupportPackage": "SAPKE45BC9", "URL": "/supportpackage/SAPKE45BC9"}, {"SoftwareComponentVersion": "SAP_HR 46B", "SupportPackage": "SAPKE46BB1", "URL": "/supportpackage/SAPKE46BB1"}, {"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46CA2", "URL": "/supportpackage/SAPKE46CA2"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47047", "URL": "/supportpackage/SAPKE47047"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47056", "URL": "/supportpackage/SAPKE47056"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50013", "URL": "/supportpackage/SAPKE50013"}, {"SoftwareComponentVersion": "SAP_HR 600", "SupportPackage": "SAPKE60002", "URL": "/supportpackage/SAPKE60002"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_HR", "NumberOfCorrin": 4, "URL": "/corrins/0000875109/2"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 37, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "439595 ", "URL": "/notes/439595 ", "Title": "Legal Change SEFIP: new version SEFIP 5.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "483363 ", "URL": "/notes/483363 ", "Title": "Housekeeping: search information from branches/cei", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "490568 ", "URL": "/notes/490568 ", "Title": "Remuneration should not be generated when employee is absent", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "524750 ", "URL": "/notes/524750 ", "Title": "HBRSEF00 shows positive values for negative basis", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "525497 ", "URL": "/notes/525497 ", "Title": "HBRSEF00 must create 2 records type 32 for movements", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "529879 ", "URL": "/notes/529879 ", "Title": "HBRSEF00 does not fill in Admission Date for Emp.Category 07", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "538420 ", "URL": "/notes/538420 ", "Title": "Values in SEFIP for competence 12", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "564609 ", "URL": "/notes/564609 ", "Title": "HBRSEF00 saves record type 30 in case of sickness", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "573934 ", "URL": "/notes/573934 ", "Title": "CBO Number has to be 6 chars long from 1.1.2003 on.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "574709 ", "URL": "/notes/574709 ", "Title": "HBRSEF00: field 13°Sal.Calc.Basis is not filled in", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "580396 ", "URL": "/notes/580396 ", "Title": "HBRSEF00 does not create record type 20", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "600857 ", "URL": "/notes/600857 ", "Title": "Legal Change SEFIP 6.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "618939 ", "URL": "/notes/618939 ", "Title": "New Version SEFIP 6.1", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "625259 ", "URL": "/notes/625259 ", "Title": "HBRSEF00 does not print value if employee is absent", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "630300 ", "URL": "/notes/630300 ", "Title": "HBRSEF00 does not generate record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "647646 ", "URL": "/notes/647646 ", "Title": "HBRSEF00 doesn't print retrocalculated values (Termination)", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "655615 ", "URL": "/notes/655615 ", "Title": "HBRSEF00 does not create record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "657050 ", "URL": "/notes/657050 ", "Title": "HBRSEF00 generates 2 records type 20", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "672464 ", "URL": "/notes/672464 ", "Title": "HBRSEF00 does not print INSS's basis in record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "673509 ", "URL": "/notes/673509 ", "Title": "Legal Change of HBRSEF00: Version 6.3", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "741923 ", "URL": "/notes/741923 ", "Title": "HBRSEF00: CTPS number should be blank instead of zeros", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "749848 ", "URL": "/notes/749848 ", "Title": "SEFIP 6.4 - Legal Change", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "754134 ", "URL": "/notes/754134 ", "Title": "SEFIP: fields alignment and retirement due disability", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "761901 ", "URL": "/notes/761901 ", "Title": "SEFIP: several corrections", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "767558 ", "URL": "/notes/767558 ", "Title": "SEFIP: maternity leave corrections", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "781240 ", "URL": "/notes/781240 ", "Title": "HBRSEF00 centralizer branch selection", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "793915 ", "URL": "/notes/793915 ", "Title": "SEFIP: Remuneration without christmas bonus", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "827280 ", "URL": "/notes/827280 ", "Title": "SEFIP 7.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46B", "Number": "833613 ", "URL": "/notes/833613 ", "Title": "SEFIP - Remuneration Without Christmas Allowance", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "211535 ", "URL": "/notes/211535 ", "Title": "SEFIP - Alter. para nova versao", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "831335 ", "URL": "/notes/831335 ", "Title": "HBRSEF00 - \"Data de movimento\" earlier than hire date", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "833669 ", "URL": "/notes/833669 ", "Title": "HBRSEF00 record type 30 Allowance Base", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "470", "Number": "695827 ", "URL": "/notes/695827 ", "Title": "SEFIP prints wrong value in record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "500", "Number": "764696 ", "URL": "/notes/764696 ", "Title": "SEFIP: CTPS data in record type 13 / 13th salary difference", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "500", "Number": "855485 ", "URL": "/notes/855485 ", "Title": "Employee transference in SEFIP", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "439595 ", "URL": "/notes/439595 ", "Title": "Legal Change SEFIP: new version SEFIP 5.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "483363 ", "URL": "/notes/483363 ", "Title": "Housekeeping: search information from branches/cei", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "490568 ", "URL": "/notes/490568 ", "Title": "Remuneration should not be generated when employee is absent", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "524750 ", "URL": "/notes/524750 ", "Title": "HBRSEF00 shows positive values for negative basis", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "525497 ", "URL": "/notes/525497 ", "Title": "HBRSEF00 must create 2 records type 32 for movements", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "529879 ", "URL": "/notes/529879 ", "Title": "HBRSEF00 does not fill in Admission Date for Emp.Category 07", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "538420 ", "URL": "/notes/538420 ", "Title": "Values in SEFIP for competence 12", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "564609 ", "URL": "/notes/564609 ", "Title": "HBRSEF00 saves record type 30 in case of sickness", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "573934 ", "URL": "/notes/573934 ", "Title": "CBO Number has to be 6 chars long from 1.1.2003 on.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "574709 ", "URL": "/notes/574709 ", "Title": "HBRSEF00: field 13°Sal.Calc.Basis is not filled in", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "580396 ", "URL": "/notes/580396 ", "Title": "HBRSEF00 does not create record type 20", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "600857 ", "URL": "/notes/600857 ", "Title": "Legal Change SEFIP 6.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "618939 ", "URL": "/notes/618939 ", "Title": "New Version SEFIP 6.1", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "625259 ", "URL": "/notes/625259 ", "Title": "HBRSEF00 does not print value if employee is absent", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "630300 ", "URL": "/notes/630300 ", "Title": "HBRSEF00 does not generate record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "647646 ", "URL": "/notes/647646 ", "Title": "HBRSEF00 doesn't print retrocalculated values (Termination)", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "655615 ", "URL": "/notes/655615 ", "Title": "HBRSEF00 does not create record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "657050 ", "URL": "/notes/657050 ", "Title": "HBRSEF00 generates 2 records type 20", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "672464 ", "URL": "/notes/672464 ", "Title": "HBRSEF00 does not print INSS's basis in record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "673509 ", "URL": "/notes/673509 ", "Title": "Legal Change of HBRSEF00: Version 6.3", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "741923 ", "URL": "/notes/741923 ", "Title": "HBRSEF00: CTPS number should be blank instead of zeros", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "749848 ", "URL": "/notes/749848 ", "Title": "SEFIP 6.4 - Legal Change", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "754134 ", "URL": "/notes/754134 ", "Title": "SEFIP: fields alignment and retirement due disability", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "761901 ", "URL": "/notes/761901 ", "Title": "SEFIP: several corrections", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "767558 ", "URL": "/notes/767558 ", "Title": "SEFIP: maternity leave corrections", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "781240 ", "URL": "/notes/781240 ", "Title": "HBRSEF00 centralizer branch selection", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "793915 ", "URL": "/notes/793915 ", "Title": "SEFIP: Remuneration without christmas bonus", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "827280 ", "URL": "/notes/827280 ", "Title": "SEFIP 7.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "435847 ", "URL": "/notes/435847 ", "Title": "Payroll stops because there's no FGTS's Bank Account", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "439595 ", "URL": "/notes/439595 ", "Title": "Legal Change SEFIP: new version SEFIP 5.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "483363 ", "URL": "/notes/483363 ", "Title": "Housekeeping: search information from branches/cei", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "483830 ", "URL": "/notes/483830 ", "Title": "HBRSEF00:/123 (negative values) and INSS's Basis with errors", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "490568 ", "URL": "/notes/490568 ", "Title": "Remuneration should not be generated when employee is absent", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "524750 ", "URL": "/notes/524750 ", "Title": "HBRSEF00 shows positive values for negative basis", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "525497 ", "URL": "/notes/525497 ", "Title": "HBRSEF00 must create 2 records type 32 for movements", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "529879 ", "URL": "/notes/529879 ", "Title": "HBRSEF00 does not fill in Admission Date for Emp.Category 07", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "538420 ", "URL": "/notes/538420 ", "Title": "Values in SEFIP for competence 12", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "564609 ", "URL": "/notes/564609 ", "Title": "HBRSEF00 saves record type 30 in case of sickness", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "573934 ", "URL": "/notes/573934 ", "Title": "CBO Number has to be 6 chars long from 1.1.2003 on.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "574709 ", "URL": "/notes/574709 ", "Title": "HBRSEF00: field 13°Sal.Calc.Basis is not filled in", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "580396 ", "URL": "/notes/580396 ", "Title": "HBRSEF00 does not create record type 20", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "600857 ", "URL": "/notes/600857 ", "Title": "Legal Change SEFIP 6.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "618939 ", "URL": "/notes/618939 ", "Title": "New Version SEFIP 6.1", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "625259 ", "URL": "/notes/625259 ", "Title": "HBRSEF00 does not print value if employee is absent", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "630300 ", "URL": "/notes/630300 ", "Title": "HBRSEF00 does not generate record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "647646 ", "URL": "/notes/647646 ", "Title": "HBRSEF00 doesn't print retrocalculated values (Termination)", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "655615 ", "URL": "/notes/655615 ", "Title": "HBRSEF00 does not create record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "657050 ", "URL": "/notes/657050 ", "Title": "HBRSEF00 generates 2 records type 20", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "672464 ", "URL": "/notes/672464 ", "Title": "HBRSEF00 does not print INSS's basis in record type 30", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "673509 ", "URL": "/notes/673509 ", "Title": "Legal Change of HBRSEF00: Version 6.3", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "741923 ", "URL": "/notes/741923 ", "Title": "HBRSEF00: CTPS number should be blank instead of zeros", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "749848 ", "URL": "/notes/749848 ", "Title": "SEFIP 6.4 - Legal Change", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "754134 ", "URL": "/notes/754134 ", "Title": "SEFIP: fields alignment and retirement due disability", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "761901 ", "URL": "/notes/761901 ", "Title": "SEFIP: several corrections", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "767558 ", "URL": "/notes/767558 ", "Title": "SEFIP: maternity leave corrections", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "781240 ", "URL": "/notes/781240 ", "Title": "HBRSEF00 centralizer branch selection", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "793915 ", "URL": "/notes/793915 ", "Title": "SEFIP: Remuneration without christmas bonus", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "827280 ", "URL": "/notes/827280 ", "Title": "SEFIP 7.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "500", "Number": "833613 ", "URL": "/notes/833613 ", "Title": "SEFIP - Remuneration Without Christmas Allowance", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "500", "Number": "833669 ", "URL": "/notes/833669 ", "Title": "HBRSEF00 record type 30 Allowance Base", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "741923 ", "URL": "/notes/741923 ", "Title": "HBRSEF00: CTPS number should be blank instead of zeros", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "749848 ", "URL": "/notes/749848 ", "Title": "SEFIP 6.4 - Legal Change", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "754134 ", "URL": "/notes/754134 ", "Title": "SEFIP: fields alignment and retirement due disability", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "761901 ", "URL": "/notes/761901 ", "Title": "SEFIP: several corrections", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "781240 ", "URL": "/notes/781240 ", "Title": "HBRSEF00 centralizer branch selection", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "793915 ", "URL": "/notes/793915 ", "Title": "SEFIP: Remuneration without christmas bonus", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "827280 ", "URL": "/notes/827280 ", "Title": "SEFIP 7.0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "831335 ", "URL": "/notes/831335 ", "Title": "HBRSEF00 - \"Data de movimento\" earlier than hire date", "Component": "PY-BR"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}