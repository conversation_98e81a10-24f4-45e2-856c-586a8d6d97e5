{"Request": {"Number": "1354156", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 728, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000007974102017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001354156?language=E&token=91FCFD19606DF028A9190294B197C25C"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001354156", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001354156/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1354156"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Currentness", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "01.07.2009"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-CH-IS-H"}, "SAPComponentKeyText": {"_label": "Component", "value": "Hospital"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Switzerland", "value": "XX-CSC-CH", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-specific component", "value": "XX-CSC-CH-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hospital", "value": "XX-CSC-CH-IS-H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH-IS-H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1354156 - IS-H: ANA-LIST (SAP Delta)"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1354156&TargetLanguage=EN&Component=XX-CSC-CH-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/1354156/D\" target=\"_blank\">/notes/1354156/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This SAP Note is primarily relevant only for the country version Switzerland (CH).<br />However, this SAP Note is also relevant for other country versions if it is specified as a prerequisite for further corrections. In this case, you must implement the attached correction instructions, even if you have implemented a different country version.<br />This SAP Note concerns the delivery of the country-specific function &quot;Analysis List (Lab Tariff)&quot;.</p><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>Analysis List, Lab Tariff</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>Analytics List</p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><p>Before you implement the source code corrections, you must perform the following actions manually:<br />When you do this, you must take into account the sequence of the manual activities specified here.</p> <OL>1. Unpack the attached files depending on the IS-H version:</OL> <OL><OL>a) HW1354156_472.zip and then HW1354156_472_Cust.zip for 4.72 AOP 01 - 33</OL></OL> <OL><OL>b) HW1354156_600.zip and then HW1354156_600_Cust.zip for 6.00 AOP 01 - 18</OL></OL> <OL><OL>c) HW1354156_603.zip and then HW1354156_603_Cust.zip for 6.03 AOP 01 - 06</OL></OL> <OL><OL>d) HW1354156_604.zip and then HW1354156_604_Cust.zip for 6.04 AOP 01 - 05</OL></OL> <p>              Note that you cannot download the attached files using OSS, but only from SAP Service Marketplace (see also SAP Notes 480180 and 13719 for information about importing attachments). <OL>2. Import the contained requests (for 4.72, two workbench requests for the development client and for 6.00 and higher, a workbench request for the development client and a workbench request for the Customizing client) into your system.<br />Note that the relevant request contained in the &quot;_xxx.zip&quot; file must be imported first and then the relevant request contained in the &quot;_xxx_Cust.zip&quot; file.<br /><br />When you import the attachment transports, generation errors may occur that are corrected after you implement the source code corrections from this SAP Note.</OL> <OL>3. You must now perform the following manual tasks:</OL> <OL><OL>a) Enhance the structure RNLSR:</OL></OL> <UL><UL><LI>Go to transaction SE11.</LI></UL></UL> <UL><UL><LI>Select &quot;Data type&quot; and enter the value RNLSR in the input field.</LI></UL></UL> <UL><UL><LI>Choose &quot;Change&quot;.</LI></UL></UL> <UL><UL><LI>Now position the cursor on the first free cell at the end of the structure.</LI></UL></UL> <UL><UL><LI>In the first free line, enter the component name ORDNR and the component type ISH_ORDNR_C20.</LI></UL></UL><UL><UL><LI>In the next free line, enter the component name ORDNR_X and the component type ISH_AKFELD.</LI></UL></UL> <UL><UL><LI>Now position the cursor on the next free cell at the end of the structure.</LI></UL></UL> <UL><UL><LI>In the menu bar, choose &quot;Edit -> Include -> Insert&quot;.</LI></UL></UL> <UL><UL><LI>In the Structure field, enter RNLSR_CH. Choose &quot;Continue&quot;.</LI></UL></UL> <UL><UL><LI>Save and activate the structure.</LI></UL></UL> <OL><OL>b) Enhance the structure RNLE1:</OL></OL> <UL><UL><LI>Go to transaction SE11.</LI></UL></UL> <UL><UL><LI>Select &quot;Data type&quot; and enter the value RNLE1 in the input field.</LI></UL></UL> <UL><UL><LI>Choose &quot;Change&quot;.</LI></UL></UL> <UL><UL><LI>Now position the cursor on the first free cell at the end of the structure.</LI></UL></UL> <UL><UL><LI>In the first free line, enter the component name ORDNR and the component type ISH_ORDNR_C20.</LI></UL></UL><UL><UL><LI>Save and activate the structure.</LI></UL></UL> <OL><OL>c) Enhance the structure RNLE6:</OL></OL> <UL><UL><LI>Go to transaction SE11.</LI></UL></UL> <UL><UL><LI>Select &quot;Data type&quot; and enter the value RNLE6 in the input field.</LI></UL></UL> <UL><UL><LI>Choose &quot;Change&quot;.</LI></UL></UL> <UL><UL><LI>Now position the cursor on the first free cell at the end of the structure.</LI></UL></UL> <UL><UL><LI>In the first free line, enter the component name ORDNR and the component type ISH_ORDNR_C20.</LI></UL></UL><UL><UL><LI>Save and activate the structure.</LI></UL></UL> <OL><OL>d) Enhance the structure RNSERVICE01:</OL></OL> <UL><UL><LI>Go to transaction SE11.</LI></UL></UL> <UL><UL><LI>Select &quot;Data type&quot; and enter the value RNSERVICE01 in the input field.</LI></UL></UL> <UL><UL><LI>Choose &quot;Change&quot;.</LI></UL></UL> <UL><UL><LI>Now position the cursor on the first free cell at the end of the structure.</LI></UL></UL> <UL><UL><LI>In the first free line, enter the component name ORDNR and the component type ISH_ORDNR_C20.</LI></UL></UL><UL><UL><LI>In the next free line, enter the component name ORDNR_X and the component type ISH_AKFELD.</LI></UL></UL> <UL><UL><LI>Save and activate the structure.</LI></UL></UL> <OL><OL>e) Enhance the structure ISH_SERVICE_SHOW:</OL></OL> <UL><UL><LI>Go to transaction SE11.</LI></UL></UL> <UL><UL><LI>Select &quot;Data type&quot; and enter the value ISH_SERVICE_SHOW in the input field.</LI></UL></UL> <UL><UL><LI>Choose &quot;Change&quot;.</LI></UL></UL> <UL><UL><LI>Now position the cursor on the first free cell at the end of the structure.</LI></UL></UL> <UL><UL><LI>In the first free line, enter the component name ORDNR and the component type ISH_ORDNR_C20.</LI></UL></UL><UL><UL><LI>Now position the cursor on the next free cell at the end of the structure.</LI></UL></UL> <UL><UL><LI>In the menu bar, choose &quot;Edit -> Include -> Insert&quot;.</LI></UL></UL> <UL><UL><LI>Enter the value ISH_SERVICE_SHOW_CH in the Structure field. Choose &quot;Continue&quot;.</LI></UL></UL> <UL><UL><LI>Save and activate the structure.</LI></UL></UL> <OL><OL>f) Enhance the structure BAPI2099DATA:</OL></OL> <UL><UL><LI>Go to transaction SE11.</LI></UL></UL> <UL><UL><LI>Select &quot;Data type&quot; and enter the value BAPI2099DATA in the input field.</LI></UL></UL> <UL><UL><LI>Choose &quot;Change&quot;.</LI></UL></UL> <UL><UL><LI>Now position the cursor on the first free cell at the end of the structure.</LI></UL></UL> <UL><UL><LI>In the first free line, enter the component name ORDER_NUMBER_C20 and the component type ISH_ORDNR_C20.</LI></UL></UL> <UL><UL><LI>In the next free line, enter the component name COMP_EXT_ORDERER and the component type NWCH_BEAG.</LI></UL></UL> <UL><UL><LI>In the next free line, enter the component name LAB_TYPE and the component type NWCH_LABTYP.</LI></UL></UL> <UL><UL><LI>Save and activate the structure.</LI></UL></UL> <OL><OL>g) Enhance the structure BAPI2099CDATA:</OL></OL> <UL><UL><LI>Go to transaction SE11.</LI></UL></UL> <UL><UL><LI>Select &quot;Data type&quot; and enter the value BAPI2099CDATA in the input field.</LI></UL></UL> <UL><UL><LI>Choose &quot;Change&quot;.</LI></UL></UL> <UL><UL><LI>Now position the cursor on the first free cell at the end of the structure.</LI></UL></UL> <UL><UL><LI>In the first free line, enter the component name ORDER_NUMBER_C20 and the component type ISH_ORDNR_C20.</LI></UL></UL> <UL><UL><LI>In the next free line, enter the component name ORDER_NUMBER_C20X and the component type BAPIUPDATE.</LI></UL></UL> <UL><UL><LI>In the next free line, enter the component name COMP_EXT_ORDERER and the component type NWCH_BEAG.</LI></UL></UL> <UL><UL><LI>In the next free line, enter the component name COMP_EXT_ORDERERX and the component type BAPIUPDATE.</LI></UL></UL> <UL><UL><LI>In the next free line, enter the component name LAB_TYPE and the component type NWCH_LABTYP.</LI></UL></UL> <UL><UL><LI>In the next free line, enter the component name LAB_TYPEX and the component type BAPIUPDATE.</LI></UL></UL> <UL><UL><LI>Save and activate the structure.</LI></UL></UL> <OL><OL>h) Enhance the table NLEI:</OL></OL> <UL><UL><LI>Go to transaction SE11.</LI></UL></UL> <UL><UL><LI>Select &quot;Database table&quot; and enter the value NLEI in the input field.</LI></UL></UL> <UL><UL><LI>Choose &quot;Change&quot;.</LI></UL></UL> <UL><UL><LI>Now position the cursor on the first free cell at the end of the table.</LI></UL></UL> <UL><UL><LI>In the first free line, enter the component name ORDNR and the component type ISH_ORDNR_C20.</LI></UL></UL><UL><UL><LI>Now position the cursor on the next free cell at the end of the table.</LI></UL></UL> <UL><UL><LI>In the menu bar, choose &quot;Edit -> Include -> Insert&quot;.</LI></UL></UL> <UL><UL><LI>In the &quot;Structure&quot; field, enter the value NWCH_NLEI. Choose &quot;Continue&quot;.</LI></UL></UL> <UL><UL><LI>Save and activate the table.</LI></UL></UL> <p><br />Now implement the source code corrections.<br />After you implement the source code corrections, a syntax error may occur in the function group N_API_CASESERVICE because the include LN_API_CASESERVICEFR2 may be missing. To correct this problem, proceed as follows:</p> <OL>4. Go to transaction SE80.</OL> <OL>5. In the input help of the upper of the two input fields, choose the function group. In the lower input field, enter the name of the function group N_API_CASESERVICE.</OL> <OL>6. Confirm your entry.</OL> <OL>7. Expand the &quot;Includes&quot; node and double-click the include LN_API_CASESERVICEFRX.</OL> <OL>8. Now create the include LN_API_CASESERVICEFR2 by double-clicking &quot;ln_api_caseservicefr2&quot; in the statement &quot;include ln_api_caseservicefr2&quot; on the right-hand side of the screen.</OL> <OL>9. Now create the include and copy the content of the attached text file &quot;N_API_CASESERVICEFR2&quot; to the source.</OL> <OL>10. Activate your change.</OL></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "IS-H (Hospital)"}, {"Key": "Owner                                                                                    ", "Value": "<PERSON> (C2754910)"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON> (C2754910)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001354156/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "HW1354156_604_Cust.zip", "FileSize": "159", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000306132009&iv_version=0003&iv_guid=15F4009197374F4CA7992F63D27D095D"}, {"FileName": "N_API_CASESERVICEFR2.txt", "FileSize": "1", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000306132009&iv_version=0003&iv_guid=0E197C495A84C64182748AF63CEE2575"}, {"FileName": "HW1354156_472.zip", "FileSize": "12", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000306132009&iv_version=0003&iv_guid=3C37AB93C86DDB44A442E072E81C5009"}, {"FileName": "HW1354156_472_Cust.zip", "FileSize": "3", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000306132009&iv_version=0003&iv_guid=68EE793BD431D24D9F969DD7B1E51D25"}, {"FileName": "HW1354156_603.zip", "FileSize": "12", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000306132009&iv_version=0003&iv_guid=E060BA402CF46B40A9B3656085C80AFE"}, {"FileName": "HW1354156_604.zip", "FileSize": "15", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000306132009&iv_version=0003&iv_guid=97D876AC5095FC4E8F6E4A45C34881F0"}, {"FileName": "HW1354156_600.zip", "FileSize": "12", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000306132009&iv_version=0003&iv_guid=FC165677D0FABD46B7D65E3A89539A40"}, {"FileName": "HW1354156_600_Cust.zip", "FileSize": "79", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000306132009&iv_version=0003&iv_guid=F596F8F2D5A9464DB55BA075E368FA70"}, {"FileName": "HW1354156_603_Cust.zip", "FileSize": "79", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000306132009&iv_version=0003&iv_guid=A382F2980E1BE34E8B593DE450F9EC49"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1359544", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: ANA List (CUA Definitions)", "RefUrl": "/notes/1359544"}, {"RefNumber": "1357130", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: ANA List - Correction Package[1]", "RefUrl": "/notes/1357130"}, {"RefNumber": "1354157", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: ANA LIST (CH Delta)", "RefUrl": "/notes/1354157"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "1359544", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: ANA List (CUA Definitions)", "RefUrl": "/notes/1359544 "}, {"RefNumber": "1354157", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: ANA LIST (CH Delta)", "RefUrl": "/notes/1354157 "}, {"RefNumber": "1357130", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: ANA List - Correction Package[1]", "RefUrl": "/notes/1357130 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "IS-H", "From": "472", "To": "472", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": [{"SoftwareComponentVersion": "IS-H 472", "SupportPackage": "SAPKIPHF34", "URL": "/supportpackage/SAPKIPHF34"}, {"SoftwareComponentVersion": "IS-H 600", "SupportPackage": "SAPK-60019INISH", "URL": "/supportpackage/SAPK-60019INISH"}, {"SoftwareComponentVersion": "IS-H 602", "SupportPackage": "SAPK-60208INISH", "URL": "/supportpackage/SAPK-60208INISH"}, {"SoftwareComponentVersion": "IS-H 603", "SupportPackage": "SAPK-60307INISH", "URL": "/supportpackage/SAPK-60307INISH"}, {"SoftwareComponentVersion": "IS-H 604", "SupportPackage": "SAPK-60406INISH", "URL": "/supportpackage/SAPK-60406INISH"}]}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 4, "URL": "/corrins/0001354156/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 9, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "861066 ", "URL": "/notes/861066 ", "Title": "IS-H: <PERSON><PERSON><PERSON><PERSON> May Not Be Filled in Service Screen", "Component": "IS-H-PA-VER"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "932056 ", "URL": "/notes/932056 ", "Title": "IS-H CH: NTPKCH - Adjustments Due to Key Change", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "952834 ", "URL": "/notes/952834 ", "Title": "IS-H CH: Conversion of Drop-Off Unit for Pricing", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1105505 ", "URL": "/notes/1105505 ", "Title": "IS-H AT: EDIVKA - Enhancement Package 2 (Delta)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1159537 ", "URL": "/notes/1159537 ", "Title": "Incorrect message within validation", "Component": "IS-H-PA-SER"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1239891 ", "URL": "/notes/1239891 ", "Title": "IS-H: Billed PPA Case Set to Interim Billed", "Component": "IS-H-ACM-BIL"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1266927 ", "URL": "/notes/1266927 ", "Title": "IS-H: LKF Model 2009 SAP Delta", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1298226 ", "URL": "/notes/1298226 ", "Title": "IS-H: Billed case cannot be reset", "Component": "IS-H-ACM-BIL"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "861066 ", "URL": "/notes/861066 ", "Title": "IS-H: <PERSON><PERSON><PERSON><PERSON> May Not Be Filled in Service Screen", "Component": "IS-H-PA-VER"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "952834 ", "URL": "/notes/952834 ", "Title": "IS-H CH: Conversion of Drop-Off Unit for Pricing", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1105505 ", "URL": "/notes/1105505 ", "Title": "IS-H AT: EDIVKA - Enhancement Package 2 (Delta)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1159537 ", "URL": "/notes/1159537 ", "Title": "Incorrect message within validation", "Component": "IS-H-PA-SER"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1239891 ", "URL": "/notes/1239891 ", "Title": "IS-H: Billed PPA Case Set to Interim Billed", "Component": "IS-H-ACM-BIL"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1266927 ", "URL": "/notes/1266927 ", "Title": "IS-H: LKF Model 2009 SAP Delta", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1298226 ", "URL": "/notes/1298226 ", "Title": "IS-H: Billed case cannot be reset", "Component": "IS-H-ACM-BIL"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1159537 ", "URL": "/notes/1159537 ", "Title": "Incorrect message within validation", "Component": "IS-H-PA-SER"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1239891 ", "URL": "/notes/1239891 ", "Title": "IS-H: Billed PPA Case Set to Interim Billed", "Component": "IS-H-ACM-BIL"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1266927 ", "URL": "/notes/1266927 ", "Title": "IS-H: LKF Model 2009 SAP Delta", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1298226 ", "URL": "/notes/1298226 ", "Title": "IS-H: Billed case cannot be reset", "Component": "IS-H-ACM-BIL"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1239891 ", "URL": "/notes/1239891 ", "Title": "IS-H: Billed PPA Case Set to Interim Billed", "Component": "IS-H-ACM-BIL"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1266927 ", "URL": "/notes/1266927 ", "Title": "IS-H: LKF Model 2009 SAP Delta", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1298226 ", "URL": "/notes/1298226 ", "Title": "IS-H: Billed case cannot be reset", "Component": "IS-H-ACM-BIL"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1337825 ", "URL": "/notes/1337825 ", "Title": "IS-H FR: Service Entry, Fields Disappear", "Component": "XX-CSC-FR-IS-H"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1354156&TargetLanguage=EN&Component=XX-CSC-CH-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/1354156/D\" target=\"_blank\">/notes/1354156/D</a>."}}}}