{"Request": {"Number": "945038", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 324, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016095092017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000945038?language=E&token=87A78F97C03EF46F5FEE9F1EBCB30927"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000945038", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000945038/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "945038"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 14}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Customizing"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.03.2010"}, "SAPComponentKey": {"_label": "Component", "value": "EHS-SAF-RCK"}, "SAPComponentKeyText": {"_label": "Component", "value": "Regulatory Checks"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Environment, Health, and Safety / Product Compliance", "value": "EHS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Product Safety", "value": "EHS-SAF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS-SAF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Regulatory Checks", "value": "EHS-SAF-RCK", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS-SAF-RCK*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "945038 - Substance volume tracking: Class and char. do not exist"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Component:&#x00A0;&#x00A0;Product Safety<br />Module:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Substance volume tracking<br /><br />You want to use substance volume tracking and store company-specific registration data for some substances in the value assignment type \"Registration (Company-Specific)\". However, the value assignment type cannot be opened in the specification workbench.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Internal program error; SAPLC1AX RMWBPWB0 0 charact.-descr. not found<br />C$153, C$ 153<br />Inconsistency, KSML<br />Missing characteristics, missing characteristic<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Reason:<br />There is an error in the delivery: The SAP_EHS_1023_057 class assigned to the value assignment type and the related characteristics do not exist in the system or Customizing for the value assignment type has not been generated yet.<br /><br />Prerequisites:<br />Contrary to the specifications under the \"Reference to Support Packages\" section, this note is valid for all releases and Support Packages once substance volume tracking becomes available, as described in Note 905726.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>You can import the transports attached to this note into your system. The transports contain the following:<br /></p> <UL><LI>AERK026553: All classes and characteristics of substance volume tracking (including the descriptions in German). These are the following classes:</LI></UL> <UL><UL><LI>SAP_EHS_1023_057</LI></UL></UL> <UL><UL><LI>SAP_EHS_1026_001</LI></UL></UL> <UL><UL><LI>SAP_EHS_1026_002</LI></UL></UL> <UL><UL><LI>SAP_EHS_1026_003</LI></UL></UL> <UL><UL><LI>SAP_EHS_1026_004</LI></UL></UL> <UL><UL><LI>SAP_EHS_1026_005</LI></UL></UL> <UL><UL><LI>SAP_EHS_SVT<br /></LI></UL></UL> <UL><LI>SH9K003919: Descriptions of all classes/characteristics of substance volume tracking in English, French, Spanish, Portuguese, Italian, Dutch and Japanese.<br /></LI></UL> <p>First import the transport AERK026553 to your system, followed by the transport SH9K003919. Note that you must import both transports, as the transport AERK026553 does not contain the characteristics for priority (SAP_EHS_1023_057_PRIORITY), consortia (SAP_EHS_1023_057_CONSORTIA) or registration phase (SAP_EHS_1023_057_REG_PHASE), and also does not contain any class/characteristic assignments.<br /><br />After you have imported the transports, enter the relevant value assignment types in Customizing and assign these new value assignment types to your property trees.<br />After you request the phrase sets, maintain the assignment of the phrase sets to the corresponding characteristics and use the Match Up Master Data to generate the required settings (see the documentation on these activities).<br /><br />As of R/3 Enterprise Ext. As of R/3 Enterprise Ext.2.0, the new value assignment types are only displayed in the specification workbench after Customizing for table-based value assignment has been generated for the value assignment types and the TCG11_VAI table is filled. For more information, see Note 950257.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "C3380403"}, {"Key": "Processor                                                                                           ", "Value": "C3380410"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000945038/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000945038/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000945038/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000945038/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000945038/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000945038/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000945038/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000945038/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000945038/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "AERK026553.zip", "FileSize": "12", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000133182006&iv_version=0014&iv_guid=9B3EB5779AD15E409CB20D30302AF87E"}, {"FileName": "SH9K003919.zip", "FileSize": "21", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000133182006&iv_version=0014&iv_guid=5D9E787CF1ED5449AFC3825837B7F9E9"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "955194", "RefComponent": "EHS-BD", "RefTitle": "Error with classes/characteristics", "RefUrl": "/notes/955194"}, {"RefNumber": "905726", "RefComponent": "EHS-SAF-RCK", "RefTitle": "Substance volume tracking in SAP EH&S", "RefUrl": "/notes/905726"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1271733", "RefComponent": "EHS-SRC", "RefTitle": "Release information note for SAP REACH Compliance 1.1 SP02", "RefUrl": "/notes/1271733"}, {"RefNumber": "1173789", "RefComponent": "EHS-SRC", "RefTitle": "REACH: Substance volume tracking - Integration w/ REACH 1.1", "RefUrl": "/notes/1173789"}, {"RefNumber": "1115641", "RefComponent": "EHS-SAF-RCK", "RefTitle": "Customizing settings for substance volume tracking", "RefUrl": "/notes/1115641"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "905726", "RefComponent": "EHS-SAF-RCK", "RefTitle": "Substance volume tracking in SAP EH&S", "RefUrl": "/notes/905726 "}, {"RefNumber": "1115641", "RefComponent": "EHS-SAF-RCK", "RefTitle": "Customizing settings for substance volume tracking", "RefUrl": "/notes/1115641 "}, {"RefNumber": "1271733", "RefComponent": "EHS-SRC", "RefTitle": "Release information note for SAP REACH Compliance 1.1 SP02", "RefUrl": "/notes/1271733 "}, {"RefNumber": "1173789", "RefComponent": "EHS-SRC", "RefTitle": "REACH: Substance volume tracking - Integration w/ REACH 1.1", "RefUrl": "/notes/1173789 "}, {"RefNumber": "955194", "RefComponent": "EHS-BD", "RefTitle": "Error with classes/characteristics", "RefUrl": "/notes/955194 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "110", "To": "110", "Subsequent": "X"}, {"SoftwareComponent": "EA-APPL", "From": "200", "To": "200", "Subsequent": "X"}, {"SoftwareComponent": "EA-APPL", "From": "500", "To": "500", "Subsequent": "X"}, {"SoftwareComponent": "EA-APPL", "From": "600", "To": "600", "Subsequent": "X"}, {"SoftwareComponent": "EHS", "From": "0207B0406C", "To": "0207B0406C", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EHS 0207B0406C", "SupportPackage": "SAPKIPEI12", "URL": "/supportpackage/SAPKIPEI12"}, {"SoftwareComponentVersion": "EA-APPL 110", "SupportPackage": "SAPKGPAA23", "URL": "/supportpackage/SAPKGPAA23"}, {"SoftwareComponentVersion": "EA-APPL 200", "SupportPackage": "SAPKGPAB12", "URL": "/supportpackage/SAPKGPAB12"}, {"SoftwareComponentVersion": "EA-APPL 500", "SupportPackage": "SAPKGPAC13", "URL": "/supportpackage/SAPKGPAC13"}, {"SoftwareComponentVersion": "EA-APPL 600", "SupportPackage": "SAPKGPAD05", "URL": "/supportpackage/SAPKGPAD05"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}