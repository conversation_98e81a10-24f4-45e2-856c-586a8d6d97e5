{"Request": {"Number": "130978", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 306, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000572612017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000130978?language=E&token=35C66F8798E5080DFFB018F10108D017"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000130978", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000130978/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "130978"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 38}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.07.2021"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CCM-PRN"}, "SAPComponentKeyText": {"_label": "Component", "value": "Print and Output Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Computer Center Management System (CCMS)", "value": "BC-CCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Print and Output Management", "value": "BC-CCM-PRN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-PRN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "130978 - RSPO1041 - alternative to RSPO0041"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The customer is not satisfied with RSPO0041.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The report RSPO0041 is provided to delete obsolete spool requests. However, this report does not offer enough options to properly restrict which spool requests are to be deleted.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Use the report RSPO1041, which is contained in the standard system as of Release 4.6A.</p>\r\n<p>The report RSPO1041 provides the following selection options:<br /><br />For each spool request status (without output request, in process, completed, and incorrect), you can search for spool requests that are older than the specified number of days OR that are obsolete. In addition, in the latest version, you can delete only the spool requests that are older than the indicated number of days AND that are also obsolete (by choosing \"Further options\").<br />Days can be counted as working days according to a factory calendar (standard 01) or as simple days.<br />A spool request is obsolete when it reaches its deletion date.<br />If you delete spool requests that have the status \"in process\", these are also deleted if unprocessed output requests exist.<br />Important: To ensure that a search criterion is actually used, you must select the relevant checkbox. In particular, when you search by age in days, it is NOT sufficient to enter only the number of days.<br /><br />You can use \"Further conditions\" to further limit which spool requests are to be deleted. The meanings of the selection fields are as follows:<br /><br />CLIENT: Client in which the spool request was generated<br />UNAME: Owner of the spool request<br />TITLE: Title of the spool request<br />SUFFIX0/1/2: Spool request name with its 3 parts<br />PRINTER: Name of the output device in the spool request (in 4.X, the<br /> 30-character printer name, otherwise the four-character name)<br />LISTONLY: Only write log, do not delete<br />COMMIT_A: Insert Commit after n spool requests<br /><br />As of Release 4.6A, you can also use the field \"System name\" to perform the deletion, even in remote systems. However, this is more time consuming (RFC overhead) than direct execution in the relevant system.<br /><br />RSPO1041 always generates a log, no dialog boxes are used.<br /><br />As of Release 4.6A, the system displays the spool requests that are to be deleted in a dialog box in the output control (SP01 list). However, the system always generates a log.<br /><br />NEW (as of the version from MAY/05/1999):<br />In Version 3.X, the system checks the same authorizations as those from RSPO0041. As of Version 4.X, you must have authorization to delete every single spool request.<br />NEW (as of the version from JUN/18/1999):<br />DBIF_RSQL_INVALID_CURSOR should no longer occur.<br />NEW (as of the version from AUG/10/1999):<br />Logs are no longer deleted separately. Instead, they are deleted only when you delete the output request. Normal spool requests could also be deleted due to this error. This is incorrect (see Note 137751).<br />NEW (as of the version from AUG/12/1999):<br />The error from OCT/08/1999 was corrected (no more spool requests were selected).<br />NEW (as of the version from OCT/13/1999):<br />When spool requests with archiving requests are \"completed\" (formerly \"printed\"), they are deleted only when the archiving request is also completed.<br />As of this version, entries in the client field or the user field can also be saved in variants.<br />NEW (as of the version from OCT/29/1999):<br />Error from OCT/13/1999 is corrected. No more spool requests were deleted.<br />NEW (as of the version from DEC/08/1999): (rspo1041-3x.txt will follow later)<br />You can choose between AND and OR when you perform a deletion by date.<br />NEW (as of the version from DEC/16/1999): (rspo1041-3x.txt is now available)<br />The following error has been corrected: Completed spool requests in the section 'incorrect spool requests' were also deleted.<br />NEW (as of the version from DEC/22/1999):<br />The following error has been corrected: The system may have deleted requests may up to 24 hours too early (or too late).<br />NEW (as of the version from JAN/18/2000):<br />The following error has been corrected: Identification of short device names in lower case letters (only with operation =). Now there is an additional checkbox for deleting spool requests that do not have an existing output device. These spool requests were not deleted if the selection was performed by specifying an output device. These requests were deleted only when the output device field was blank.<br />NEW (as of version of JAN/11/2001). Advance correction available:<br />If working days are selected and the factory calendar cannot be read, the program terminates without deleting any data.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D025322)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D025322)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000130978/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000130978/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000130978/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000130978/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000130978/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000130978/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000130978/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000130978/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000130978/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "504952", "RefComponent": "BC-CCM-PRN", "RefTitle": "Composite SAP Note for spool and print", "RefUrl": "/notes/504952"}, {"RefNumber": "48400", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "Reorganization of TemSe and spool", "RefUrl": "/notes/48400"}, {"RefNumber": "41547", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "How does report RSPO0041 work?", "RefUrl": "/notes/41547"}, {"RefNumber": "194669", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/194669"}, {"RefNumber": "166065", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/166065"}, {"RefNumber": "16083", "RefComponent": "BC-CCM-BTC", "RefTitle": "Standard jobs, reorganization jobs", "RefUrl": "/notes/16083"}, {"RefNumber": "137751", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/137751"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3042383", "RefComponent": "BC-CCM-PRN", "RefTitle": "Example settings for RSPO1041 run", "RefUrl": "/notes/3042383 "}, {"RefNumber": "2866110", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "Spools are always deleted by 8 days", "RefUrl": "/notes/2866110 "}, {"RefNumber": "2457819", "RefComponent": "BC-CCM-PRN", "RefTitle": "Print and Output Management: Summary of Resource Links and Information", "RefUrl": "/notes/2457819 "}, {"RefNumber": "2675824", "RefComponent": "BC-CCM-PRN", "RefTitle": "Old Spools Not Deleted with RSPO0041 / RSPO1041 Variant Settings", "RefUrl": "/notes/2675824 "}, {"RefNumber": "2635724", "RefComponent": "BC-CCM-PRN", "RefTitle": "Monitor or Display Spool Range Limit and Status", "RefUrl": "/notes/2635724 "}, {"RefNumber": "2593715", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "(Internal KBA)  Spool: RT_INSERT Reports Error 128 for Table TSP02", "RefUrl": "/notes/2593715 "}, {"RefNumber": "2461604", "RefComponent": "BC-CCM-PRN", "RefTitle": "Exclude specific spools from RSPO1041 spool deletion job", "RefUrl": "/notes/2461604 "}, {"RefNumber": "16083", "RefComponent": "BC-CCM-BTC", "RefTitle": "Standard jobs, reorganization jobs", "RefUrl": "/notes/16083 "}, {"RefNumber": "504952", "RefComponent": "BC-CCM-PRN", "RefTitle": "Composite SAP Note for spool and print", "RefUrl": "/notes/504952 "}, {"RefNumber": "41547", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "How does report RSPO0041 work?", "RefUrl": "/notes/41547 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 46A", "SupportPackage": "SAPKB46A07", "URL": "/supportpackage/SAPKB46A07"}, {"SoftwareComponentVersion": "SAP_BASIS 46A", "SupportPackage": "SAPKB46A03", "URL": "/supportpackage/SAPKB46A03"}, {"SoftwareComponentVersion": "SAP_BASIS 46B", "SupportPackage": "SAPKB46B26", "URL": "/supportpackage/SAPKB46B26"}, {"SoftwareComponentVersion": "SAP_BASIS 46B", "SupportPackage": "SAPKB46B01", "URL": "/supportpackage/SAPKB46B01"}, {"SoftwareComponentVersion": "SAP_BASIS 46B", "SupportPackage": "SAPKB46B25", "URL": "/supportpackage/SAPKB46B25"}, {"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C14", "URL": "/supportpackage/SAPKB46C14"}, {"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C16", "URL": "/supportpackage/SAPKB46C16"}, {"SoftwareComponentVersion": "SAP_BASIS 46D", "SupportPackage": "SAPKB46D05", "URL": "/supportpackage/SAPKB46D05"}, {"SoftwareComponentVersion": "SAP_BASIS 46D", "SupportPackage": "SAPKB46D07", "URL": "/supportpackage/SAPKB46D07"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 3, "URL": "/corrins/0000130978/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}