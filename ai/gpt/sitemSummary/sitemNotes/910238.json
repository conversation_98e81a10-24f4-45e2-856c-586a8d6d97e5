{"Request": {"Number": "910238", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 415, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005255352017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000910238?language=E&token=92903BC38F242C17884DCCA354473C23"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000910238", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000910238/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "910238"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "30.12.2005"}, "SAPComponentKey": {"_label": "Component", "value": "CA-DSG"}, "SAPComponentKeyText": {"_label": "Component", "value": "Digital Signature"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Digital Signature", "value": "CA-DSG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-DSG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "910238 - Signature tool: Example program for implementation missing"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You want to use the class-based signature tool to implement the digital signature function in your application.<br />The implementation guide for the signature tool (Note 700495), however, does not contain sufficient information about how to implement the function. </p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Digital signature, signature tool, implementation, SIGNA, SIGNO, DS, CL_DS_RUNTIME, IF_DS_SIGN </p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You use the implementation guide Version 1.0 contained in Note 700495. </p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Note 700495 now contains the implementation guide version V2.n as a PDF document as an attachment. In this version of the guide, the implementation of a multilevel signature process by means of a sample application is explained.<br /><br />After importing the Support Package to which this note belongs, the Digital Signature (DS) package contains the following sample data:<br /></p> <UL><LI>Example program DSIG_BOOKING_EX</LI></UL> <UL><LI>Application database table DSIG_BOOKING_EX</LI></UL> <UL><LI>Meta database table DSIG_META_EX</LI></UL> <UL><LI>Log structure DSIG_EXAMPLE_SIGN_LOG</LI></UL> <p><br />Consider the notes and restrictions contained in the implementation guide.<br /><br />Check the following settings in your system:<br /></p> <UL><LI>Transaction SIGNA:</LI></UL> <UL><UL><LI>Application: DS_EXAMP Description: Signature Tool: Sample application</LI></UL></UL> <p></p> <UL><LI>Transaction SIGNO:</LI></UL> <UL><UL><LI>Application: DS_EXAMP</LI></UL></UL> <UL><UL><LI>Object: DS_OBJ </LI></UL></UL> <UL><UL><LI>Meta table: DSIG_META_EX</LI></UL></UL> <UL><UL><LI>Log structure: DSIG_EXAMPLE_SIGN_LOG</LI></UL></UL> <UL><UL><LI>Subobject: DS_OBJ</LI></UL></UL> <UL><UL><LI>Object description: yes</LI></UL></UL> <UL><UL><LI>Possible:: Comment, Remark, Document</LI></UL></UL> <UL><UL><LI>Description: Signature Tool: Signature object for sample data</LI></UL></UL> <p></p> <UL><LI>Transaction SLG0:</LI></UL> <UL><UL><LI>Object: CDSG1</LI></UL></UL> <UL><UL><LI>Sub-object: DS_OBJ </LI></UL></UL> <UL><UL><LI>Sub-object text: Signature tool: Signature object for sample data</LI></UL></UL> <p></p> <UL><LI>Transaction ELSIG03N:</LI></UL> <UL><UL><LI>Object: DS_OBJ</LI></UL></UL> <UL><UL><LI>Signature method: System signature with authorization by R/3 user ID/password</LI></UL></UL> <UL><UL><LI>Possible: Comment, Remark, Document</LI></UL></UL> <p></p> <UL><LI>Program DSIG_BOOKING_EX</LI></UL> <p></p> <UL><UL><LI>Text symbols:</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Symbol</TH><TH ALIGN=LEFT> Text</TH><TH ALIGN=LEFT> </TH><TH ALIGN=LEFT> </TH><TH ALIGN=LEFT> mLen</TH> </TR> <TR><TD>DBK</TD><TD> Document updated </TD><TD> </TD><TD> </TD><TD> 25</TD></TR> <TR><TD>DBU</TD><TD> Table entries were updated</TD><TD> 45</TD></TR> <TR><TD>DCA</TD><TD> Document cancelled </TD><TD> </TD><TD> </TD><TD> 25</TD></TR> <TR><TD>EXA</TD><TD> Example text</TD><TD> </TD><TD> </TD><TD> 20</TD></TR> <TR><TD>NCD</TD><TD> Data was not changed - no signature process</TD><TD> </TD><TD> </TD><TD> </TD><TD> 70</TD></TR> <TR><TD>SPC</TD><TD> Signature process was cancelled</TD><TD> </TD><TD> </TD><TD> </TD><TD> </TD><TD> 45</TD></TR> <TR><TD>SPF</TD><TD> Signature process successfully terminated for document:</TD><TD> </TD><TD></TD><TD> 60</TD></TR> <TR><TD>SPO</TD><TD> Signature process running: Signatures are still to be executed</TD><TD> </TD><TD> </TD><TD> 80</TD></TR> <TR><TD>SRM</TD><TD> Note selected by signatory:</TD><TD> </TD><TD> </TD><TD> </TD><TD> </TD><TD> 50</TD></TR> </TABLE></UL></UL> <p></p> <UL><UL><LI>Selection texts:</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Name</TH><TH ALIGN=LEFT> Text</TH><TH ALIGN=LEFT> Dictionary</TH></TR> <TR><TD>P_DOCTXT</TD><TD> Short text for document</TD></TR> <TR><TD>P_DOC_ID</TD><TD> Document</TD><TD> X</TD></TR> <TR><TD>P_DS_TYP</TD><TD> Type of Signature</TD><TD> X</TD></TR> <TR><TD>P_SAVEDB</TD><TD> Updating the changes</TD></TR> <TR><TD>P_SIGNER</TD><TD> Signatory</TD><TD> X</TD><TD> </TD></TR> <TR><TD>P_SI_STR</TD><TD> Signature strategy</TD><TD> X</TD></TR> <TR><TD>P_STATUS</TD><TD> Document status</TD></TR> </TABLE></UL></UL> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D026022)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D029675)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000910238/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000910238/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000910238/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000910238/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000910238/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000910238/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000910238/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000910238/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000910238/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "700495", "RefComponent": "CA-DSG", "RefTitle": "Implementation of digital signature with help of signature tool", "RefUrl": "/notes/700495"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "700495", "RefComponent": "CA-DSG", "RefTitle": "Implementation of digital signature with help of signature tool", "RefUrl": "/notes/700495 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "200", "To": "200", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_ABA", "From": "620", "To": "620", "Subsequent": ""}, {"SoftwareComponent": "SAP_ABA", "From": "640", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_ABA", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_ABA 620", "SupportPackage": "SAPKA62058", "URL": "/supportpackage/SAPKA62058"}, {"SoftwareComponentVersion": "SAP_ABA 640", "SupportPackage": "SAPKA64016", "URL": "/supportpackage/SAPKA64016"}, {"SoftwareComponentVersion": "SAP_ABA 700", "SupportPackage": "SAPKA70007", "URL": "/supportpackage/SAPKA70007"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_ABA", "NumberOfCorrin": 1, "URL": "/corrins/0000910238/44"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}