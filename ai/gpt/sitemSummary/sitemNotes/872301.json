{"Request": {"Number": "872301", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 778, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015939142017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000872301?language=E&token=2DF33DB6628424E2F04342E64637637A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000872301", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "872301"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.09.2005"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-LC-IT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Italy"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Real Estate Localization", "value": "RE-FX-LC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Italy", "value": "RE-FX-LC-IT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC-IT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "872301 - RE-FX Country Version for Italy"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Localization of the Flexible Real Estate Management solution for Italy.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RE-FX, Localization, ICI, IRE, Bollo</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You need a Flexible Real Estate solution for Italy.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>As of mySAP ERP 2005, Italian companies can use the new Flexible Real Estate Management solution for Italy to calculate their taxes in accordance with Italian requirements. This new solution allows more flexibility than the classic real estate management solution. The following functions are supported:</p> <UL><UL><LI>City tax (ICI),</LI></UL></UL> <UL><UL><LI>Registration tax (IRE),</LI></UL></UL> <UL><UL><LI>Stamp tax (bollo),</LI></UL></UL> <UL><UL><LI>VAT tax line summarization.</LI></UL></UL> <p><br /></p> <b>City tax (Imposta comunale sugli immobili - ICI)</b><br /> <UL><LI>Representation of the Italian cadastral objects subject to ICI by architectural objects with a special cadastral object type.</LI></UL> <UL><LI>Relationships between ICI cadastral object and objects of the usage view (buildings, rental objects, or properties). 1:n relationships between ICI cadastral object and objects of the usage view can be realised through participation groups.</LI></UL> <UL><LI>Maintenance and view of data related to the calculation of the ICI tax value depending on the calculation type (building, D-building, building area, agricultural land)</LI></UL> <UL><LI>Tax rate depending on the rental status (rented, not rented, related contract with reduced registration tax).</LI></UL> <UL><LI>History of cadastral object and ICI tax calculation data with different conditions to one object within one year.</LI></UL> <UL><LI>Possibility to use automatic or manual ICI tax rate determination with absolute or relative ICI amount detractions</LI></UL><UL><LI>Maintenance and view of first and second (final) installment data and amounts</LI></UL> <UL><LI>Possibility to lock the first installment amount and the entire ICI tax record</LI></UL> <UL><LI>Only one report (instead of four in RE Classic) to execute all steps relevant for ICI tax calculation (display data, carry forward, print: downpayment/balance, payment lock).</LI></UL> <UL><LI>Print of ICI tax payment forms per city/ICI collector.</LI></UL> <p><br /></p> <b>Registration tax (Imposta di registro - IRE)</b><br /> <UL><LI>View of IRE records for each contract status (creation, noticing, renewal) with date, official IRE tax code, and so on.</LI></UL> <UL><LI>Automatic IRE tax determination from the taxable amount with possibility to add registration fees.</LI></UL> <UL><LI>Possibility to calculate the payback value of the IRE tax to the contract counterpart.</LI></UL> <UL><LI>Possibility to lock the IRE tax record.</LI></UL> <UL><LI>One report (instead of four in RE Classic) to execute all steps relevant for IRE tax calculation (display data, carry forward, post payback payment, lock IRE tax record).</LI></UL> <p></p> <b>Stamp Tax (Imposta di bollo)</b><br /> <UL><LI>Stamp Tax customizing according to contract type and flow type.</LI></UL> <UL><LI>Automatic generation of stamp tax in each document as a separate tax line (provided that the posting parameters of the real estate contract do not specify sales tax)</LI></UL> <p></p> <b>Value Added Tax (Imposta sul valore aggiunto - IVA)</b><br /> <UL><LI>Tax Line summarizations on tax code, currency, and so on (for details see note 517673).</LI></UL> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-CSC-IT (Italy)"}, {"Key": "Responsible                                                                                         ", "Value": "D021316"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D032292)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000872301/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000872301/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000872301/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000872301/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000872301/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000872301/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000872301/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000872301/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000872301/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "979757", "RefComponent": "RE-FX-LC-IT", "RefTitle": "RE-FX Italy: IRE enhancements", "RefUrl": "/notes/979757"}, {"RefNumber": "771098", "RefComponent": "RE-FX-LC", "RefTitle": "Countries: Release restriction RE-FX", "RefUrl": "/notes/771098"}, {"RefNumber": "517673", "RefComponent": "RE-FX", "RefTitle": "Flexible Real Estate: Functions and restrictions", "RefUrl": "/notes/517673"}, {"RefNumber": "1715373", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI: Building 'D' calculation", "RefUrl": "/notes/1715373"}, {"RefNumber": "1701914", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: Tenant Transfer", "RefUrl": "/notes/1701914"}, {"RefNumber": "1695075", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: Recalculated data is not stored in the database", "RefUrl": "/notes/1695075"}, {"RefNumber": "1690689", "RefComponent": "RE-FX-LC-IT", "RefTitle": "RE-FX IT: IRE Cockpit calc. wrong annual amnt. for leap yr.", "RefUrl": "/notes/1690689"}, {"RefNumber": "1675880", "RefComponent": "RE-FX-LC-IT", "RefTitle": "RE-FX IT: IRE Enhancement", "RefUrl": "/notes/1675880"}, {"RefNumber": "1579671", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE Payback Rate", "RefUrl": "/notes/1579671"}, {"RefNumber": "1522476", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI:valid to date is not filled automatically", "RefUrl": "/notes/1522476"}, {"RefNumber": "1516045", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: Incorrect length of term for one-time pymnt and renewal", "RefUrl": "/notes/1516045"}, {"RefNumber": "1514417", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Wrong IRE amount for 'One time payment' after renewal", "RefUrl": "/notes/1514417"}, {"RefNumber": "1504394", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: two N record for a contract", "RefUrl": "/notes/1504394"}, {"RefNumber": "1496225", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: correct maintenance view V_TIVXCITIREVA", "RefUrl": "/notes/1496225"}, {"RefNumber": "1496059", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: BSEG-SGTXT is not filled correctly", "RefUrl": "/notes/1496059"}, {"RefNumber": "1494103", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: N record is not calculated", "RefUrl": "/notes/1494103"}, {"RefNumber": "1493040", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: correct V_TIVXCITIRECTYP maint. view layout", "RefUrl": "/notes/1493040"}, {"RefNumber": "1491557", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE:amount for advance resolution is incorrectly calculated", "RefUrl": "/notes/1491557"}, {"RefNumber": "1478423", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: Assignment of Account to Account Symbol error", "RefUrl": "/notes/1478423"}, {"RefNumber": "1476863", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI: negative balance calculation", "RefUrl": "/notes/1476863"}, {"RefNumber": "1473525", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE:N record (notice) is not created-->corr for note 1468460", "RefUrl": "/notes/1473525"}, {"RefNumber": "1472920", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Incorrect calculation for ICI record", "RefUrl": "/notes/1472920"}, {"RefNumber": "1468964", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Incorrect month of ownership in ICI record after recalc.", "RefUrl": "/notes/1468964"}, {"RefNumber": "1468460", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: the system does not create the new N (notice) record", "RefUrl": "/notes/1468460"}, {"RefNumber": "1454091", "RefComponent": "RE-FX-LC-IT", "RefTitle": "'N' type IRE record for posted payback amounts", "RefUrl": "/notes/1454091"}, {"RefNumber": "1453166", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Incorrectly calculated IRE payback amount", "RefUrl": "/notes/1453166"}, {"RefNumber": "1424444", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Stamp Tax amount errors", "RefUrl": "/notes/1424444"}, {"RefNumber": "1400494", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Fix 50 % IRE payback amount in RECN transaction", "RefUrl": "/notes/1400494"}, {"RefNumber": "1399920", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE Payback amount calculation incorrectly before rounding", "RefUrl": "/notes/1399920"}, {"RefNumber": "1331520", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Error at locked ICI records by object validity mismatches", "RefUrl": "/notes/1331520"}, {"RefNumber": "1319535", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI data text in BU, RO, PR overview tab page", "RefUrl": "/notes/1319535"}, {"RefNumber": "1305332", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI Carry over creates records beyond the validity period", "RefUrl": "/notes/1305332"}, {"RefNumber": "1292856", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI Carry Over - Incorrect Down Payment Lock Setting", "RefUrl": "/notes/1292856"}, {"RefNumber": "1292716", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI Carry over - DUMP ITAB_ILLEGAL_SORT_ORDER", "RefUrl": "/notes/1292716"}, {"RefNumber": "1266584", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE Taxable amount by mid-year condition amount changes", "RefUrl": "/notes/1266584"}, {"RefNumber": "1242056", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI data recalc in AO after changes in City - rolling back", "RefUrl": "/notes/1242056"}, {"RefNumber": "1227749", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE Legal Change due to <PERSON><PERSON><PERSON>", "RefUrl": "/notes/1227749"}, {"RefNumber": "1177390", "RefComponent": "RE-FX-LC-IT", "RefTitle": "REXCITICI: paymnt installm. date not updated in the AO", "RefUrl": "/notes/1177390"}, {"RefNumber": "1171224", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI data recalculation after changes in City parameters", "RefUrl": "/notes/1171224"}, {"RefNumber": "1164233", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI cockpit does not update coefficients for D-buildings", "RefUrl": "/notes/1164233"}, {"RefNumber": "1158197", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Performance on the ICI Data screen", "RefUrl": "/notes/1158197"}, {"RefNumber": "1157680", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Error messages with ICI records locked for recalculation", "RefUrl": "/notes/1157680"}, {"RefNumber": "1149274", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Stamp Tax for credit memos, Stamp tax Tax code", "RefUrl": "/notes/1149274"}, {"RefNumber": "1138671", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI Carry Over in Cockpit - Incorrect Months of ownership", "RefUrl": "/notes/1138671"}, {"RefNumber": "1137431", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE Status Enh.(syncronization w. notice/renewal changes)", "RefUrl": "/notes/1137431"}, {"RefNumber": "1129215", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE Status Enhancements (notice, transfer of ownership)", "RefUrl": "/notes/1129215"}, {"RefNumber": "1122309", "RefComponent": "RE-FX", "RefTitle": "Localization: missing table entries for loc. countries", "RefUrl": "/notes/1122309"}, {"RefNumber": "1120873", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Tax groups subject to stamp tax", "RefUrl": "/notes/1120873"}, {"RefNumber": "1054154", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Incorrect ICI Rates by more usage objects assigned", "RefUrl": "/notes/1054154"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "771098", "RefComponent": "RE-FX-LC", "RefTitle": "Countries: Release restriction RE-FX", "RefUrl": "/notes/771098 "}, {"RefNumber": "1331520", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Error at locked ICI records by object validity mismatches", "RefUrl": "/notes/1331520 "}, {"RefNumber": "1177390", "RefComponent": "RE-FX-LC-IT", "RefTitle": "REXCITICI: paymnt installm. date not updated in the AO", "RefUrl": "/notes/1177390 "}, {"RefNumber": "1695075", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: Recalculated data is not stored in the database", "RefUrl": "/notes/1695075 "}, {"RefNumber": "517673", "RefComponent": "RE-FX", "RefTitle": "Flexible Real Estate: Functions and restrictions", "RefUrl": "/notes/517673 "}, {"RefNumber": "1715373", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI: Building 'D' calculation", "RefUrl": "/notes/1715373 "}, {"RefNumber": "1701914", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: Tenant Transfer", "RefUrl": "/notes/1701914 "}, {"RefNumber": "1690689", "RefComponent": "RE-FX-LC-IT", "RefTitle": "RE-FX IT: IRE Cockpit calc. wrong annual amnt. for leap yr.", "RefUrl": "/notes/1690689 "}, {"RefNumber": "1675880", "RefComponent": "RE-FX-LC-IT", "RefTitle": "RE-FX IT: IRE Enhancement", "RefUrl": "/notes/1675880 "}, {"RefNumber": "1579671", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE Payback Rate", "RefUrl": "/notes/1579671 "}, {"RefNumber": "1522476", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI:valid to date is not filled automatically", "RefUrl": "/notes/1522476 "}, {"RefNumber": "1471758", "RefComponent": "RE-FX-LC-IT", "RefTitle": "New customizing and credit memo calculation for Stamp Tax", "RefUrl": "/notes/1471758 "}, {"RefNumber": "1516045", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: Incorrect length of term for one-time pymnt and renewal", "RefUrl": "/notes/1516045 "}, {"RefNumber": "1514417", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Wrong IRE amount for 'One time payment' after renewal", "RefUrl": "/notes/1514417 "}, {"RefNumber": "1504394", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: two N record for a contract", "RefUrl": "/notes/1504394 "}, {"RefNumber": "1496059", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: BSEG-SGTXT is not filled correctly", "RefUrl": "/notes/1496059 "}, {"RefNumber": "1496225", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: correct maintenance view V_TIVXCITIREVA", "RefUrl": "/notes/1496225 "}, {"RefNumber": "1494103", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: N record is not calculated", "RefUrl": "/notes/1494103 "}, {"RefNumber": "1491557", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE:amount for advance resolution is incorrectly calculated", "RefUrl": "/notes/1491557 "}, {"RefNumber": "1493040", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: correct V_TIVXCITIRECTYP maint. view layout", "RefUrl": "/notes/1493040 "}, {"RefNumber": "1476863", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI: negative balance calculation", "RefUrl": "/notes/1476863 "}, {"RefNumber": "1478423", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: Assignment of Account to Account Symbol error", "RefUrl": "/notes/1478423 "}, {"RefNumber": "1472920", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Incorrect calculation for ICI record", "RefUrl": "/notes/1472920 "}, {"RefNumber": "1473525", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE:N record (notice) is not created-->corr for note 1468460", "RefUrl": "/notes/1473525 "}, {"RefNumber": "1468964", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Incorrect month of ownership in ICI record after recalc.", "RefUrl": "/notes/1468964 "}, {"RefNumber": "1468460", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: the system does not create the new N (notice) record", "RefUrl": "/notes/1468460 "}, {"RefNumber": "1129215", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE Status Enhancements (notice, transfer of ownership)", "RefUrl": "/notes/1129215 "}, {"RefNumber": "1454091", "RefComponent": "RE-FX-LC-IT", "RefTitle": "'N' type IRE record for posted payback amounts", "RefUrl": "/notes/1454091 "}, {"RefNumber": "1453166", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Incorrectly calculated IRE payback amount", "RefUrl": "/notes/1453166 "}, {"RefNumber": "1122309", "RefComponent": "RE-FX", "RefTitle": "Localization: missing table entries for loc. countries", "RefUrl": "/notes/1122309 "}, {"RefNumber": "1424444", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Stamp Tax amount errors", "RefUrl": "/notes/1424444 "}, {"RefNumber": "1400494", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Fix 50 % IRE payback amount in RECN transaction", "RefUrl": "/notes/1400494 "}, {"RefNumber": "1399920", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE Payback amount calculation incorrectly before rounding", "RefUrl": "/notes/1399920 "}, {"RefNumber": "1157680", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Error messages with ICI records locked for recalculation", "RefUrl": "/notes/1157680 "}, {"RefNumber": "1319535", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI data text in BU, RO, PR overview tab page", "RefUrl": "/notes/1319535 "}, {"RefNumber": "1266584", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE Taxable amount by mid-year condition amount changes", "RefUrl": "/notes/1266584 "}, {"RefNumber": "1292716", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI Carry over - DUMP ITAB_ILLEGAL_SORT_ORDER", "RefUrl": "/notes/1292716 "}, {"RefNumber": "1292856", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI Carry Over - Incorrect Down Payment Lock Setting", "RefUrl": "/notes/1292856 "}, {"RefNumber": "1305332", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI Carry over creates records beyond the validity period", "RefUrl": "/notes/1305332 "}, {"RefNumber": "1227749", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE Legal Change due to <PERSON><PERSON><PERSON>", "RefUrl": "/notes/1227749 "}, {"RefNumber": "1149274", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Stamp Tax for credit memos, Stamp tax Tax code", "RefUrl": "/notes/1149274 "}, {"RefNumber": "1120873", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Tax groups subject to stamp tax", "RefUrl": "/notes/1120873 "}, {"RefNumber": "1242056", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI data recalc in AO after changes in City - rolling back", "RefUrl": "/notes/1242056 "}, {"RefNumber": "1171224", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI data recalculation after changes in City parameters", "RefUrl": "/notes/1171224 "}, {"RefNumber": "1164233", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI cockpit does not update coefficients for D-buildings", "RefUrl": "/notes/1164233 "}, {"RefNumber": "1158197", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Performance on the ICI Data screen", "RefUrl": "/notes/1158197 "}, {"RefNumber": "1137431", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE Status Enh.(syncronization w. notice/renewal changes)", "RefUrl": "/notes/1137431 "}, {"RefNumber": "1138671", "RefComponent": "RE-FX-LC-IT", "RefTitle": "ICI Carry Over in Cockpit - Incorrect Months of ownership", "RefUrl": "/notes/1138671 "}, {"RefNumber": "979757", "RefComponent": "RE-FX-LC-IT", "RefTitle": "RE-FX Italy: IRE enhancements", "RefUrl": "/notes/979757 "}, {"RefNumber": "1054154", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Incorrect ICI Rates by more usage objects assigned", "RefUrl": "/notes/1054154 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}