{"Request": {"Number": "396647", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 244, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014997682017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000396647?language=E&token=07D05F570BF30D7C3727D1F3465C3CFE"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000396647", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000396647/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "396647"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 29}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.08.2003"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW only - Business Content and Extractors"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "396647 - FAQ: V3 update: Questions and answers"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You are using extractors from the logistics cockpit (LBWE).However, errors occur during V3 update. The note contains answers to 11 questions.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>LBWE, V3, SM13, RSA7, RSM13005, .logistics cockpit delta</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>-</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Question 1<br /><STRONG>Update records are written to the SM13, although you do not use the extractors from the logistics cockpit (LBWE) at all.</STRONG><br /><br />Active datasources have been accidentally delivered in a PI patch.For that reason, extract structures are set to active in the logistics cockpit. Select transaction LBWE and deactivate the active structures. From now on, no additional records are written into SM13.<br />If the system displays update records for application 05 (QM) in transaction SM13, even though the structure is not active, see note 393306 for a solution.<br /><br />Question 2<br /><STRONG>How can I selectively delete update records from SM13?</STRONG><br /><br />Start the report RSM13005 for the respective module (z.B. MCEX_UPDATE_03).</p> <UL><LI>Status COL_RUN INIT: without Delete_Flag but with VB_Flag (records are updated).</LI></UL> <UL><LI>Status COL_RUN OK: with Delete_Flag (the records are deleted for all modules with COL_RUN -- OK)</LI></UL> <p>With the IN_VB flag, data are only deleted, if there is no delta initialization. Otherwise, the records are updated.<br />MAXFBS : The number of processed records without Commit.</p> <b>ATTENTION: The delta records are deleted irrevocably after executing report RSM13005 (without flag IN_VB). You can reload the data into BW only with a new delta-initialization!</b><br /> <b></b><br /> <p>Question 3<br /><STRONG>What can I do when the V3 update loops?</STRONG><br /><STRONG></STRONG><br />Refer to Note 0352389. If you need a fast solution, simply delete all entries from SM13 (executed for V2), however, this does not solve the actual problem.</p> <b>ATTENTION: THIS CAUSES DATA LOSS. See question 2 !</b><br /> <p><br />Question 4<br /><STRONG>Why has SM13 not been emptied even though I have started the V3 update?</STRONG><br /></p> <UL><LI>The update record in SM13 contains several modules (for example, MCEX_UPDATE_11 and MCEX_UPDATE_12). If you start the V3 update only for one module, then the other module still has INIT status in SM13 and is waiting for the corresponding collective run. In some cases, the entry might also not be deleted if the V3 update has been started for the second module.In this case, schedule the request RSM13005 with the DELETE_FLAG (see question 2).</LI></UL> <p></p> <UL><LI>V3 updating no longer functions after the PI upgrade because you did not load all the delta records into the BW system prior to the upgrade.Proceed as described in note 328181.</LI></UL> <p><br />Question 5<br /><STRONG>The entries from SM13 have not been retrieved even though I followed note 0328181!</STRONG><br /><br />Check whether all entries were actually deleted from SM13 for all clients. Look for records within the last 25 years with user * .<br />Question 6<br /><STRONG>Can I schedule V3 update in parallel?</STRONG><br /><br />The V3 update already uses collective processing.You cannot do this in parallel.<br />Question 7<br /><STRONG>The Logistics Cockpit extractors deliver incorrect numbers. The update contains errors !</STRONG><br /><br />Have you installed the most up-to-date PI in your OLTP system?<br />You should have at least PI 2000.1 patch 6 or PI 2000.2 patch 2.<br />Question 8<br /><STRONG>Why has no data been written into the delta queue even though the V3 update was executed successfully?</STRONG><br /><br />You have probably not started a delta initialization. You have to start a delta initialization for each DataSource from the BW system before you can load the delta.Check in RSA7 for an entry with a green status for the required DataSource. Refer also to Note 0380078.<br />Question 9<br /><STRONG>Why does the system write data into the delta queue, even though the V3 update has not been started?</STRONG><br />You are using the automatic goods receipt posting (transaction MRRS) and start this in the background.In this case the system writes the records for DataSources of application 02 directly into the delta queue (RSA7).This does not cause double data records.This does not result in any inconsistencies.<br />Question 10<br /><STRONG>Why am I not able to carry out a structural change in the Logistics Cockpit although SM13 is blank?</STRONG><br />Inconsistencies occurred in your system. There are records in update table VBMOD for which there are no entries in table VBHDR. Due to those missing records, there are no entries in SM13. To remove the inconsistencies, follow the instructions in the solution part of Note 67014. Please note that no postings must be made in the system during reorganization in any case!<br /><br />Question 11<br /><STRONG>Why is it impossible to plan a V3 job from the Logistics Cockpit?</STRONG><br />The job always abends immediately. Due to missing authorizations, the update job cannot be planned. For further information see Note 445620.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BCT-LO (BW only - Logistics - General)"}, {"Key": "Transaction codes", "Value": "SM13"}, {"Key": "Transaction codes", "Value": "MRRS"}, {"Key": "Responsible                                                                                         ", "Value": "D034624"}, {"Key": "Processor                                                                                           ", "Value": "D034624"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000396647/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000396647/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000396647/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000396647/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000396647/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000396647/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000396647/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000396647/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000396647/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "762951", "RefComponent": "BW-BCT-SD-SLS", "RefTitle": "Organization of structure changes in the LBWE", "RefUrl": "/notes/762951"}, {"RefNumber": "67014", "RefComponent": "BC-CST-UP", "RefTitle": "Reorganizing update requests", "RefUrl": "/notes/67014"}, {"RefNumber": "652310", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "LBWE: Message MCEX 140 with extract structure change", "RefUrl": "/notes/652310"}, {"RefNumber": "640066", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "Upgrade: Return code 6 with XPRA RMCSBWXP_COM", "RefUrl": "/notes/640066"}, {"RefNumber": "564593", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/564593"}, {"RefNumber": "522770", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "No conversion exit for PS_PSP_PNR-Extraktor 2LIS_03_BF", "RefUrl": "/notes/522770"}, {"RefNumber": "494288", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Missing fields for the 2LIS_03_UM extractor", "RefUrl": "/notes/494288"}, {"RefNumber": "445620", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "Background job not scheduled for the V3 update", "RefUrl": "/notes/445620"}, {"RefNumber": "428294", "RefComponent": "BW-BCT-LO", "RefTitle": "Composite SAP note Logistics Extractn PI 2001.1 & PI 2001.2", "RefUrl": "/notes/428294"}, {"RefNumber": "417189", "RefComponent": "MM-PUR", "RefTitle": "BW/SAPLEINS - Online update of delta queue", "RefUrl": "/notes/417189"}, {"RefNumber": "409213", "RefComponent": "BC-CST-UP", "RefTitle": "Error in the sorting of collective run updates", "RefUrl": "/notes/409213"}, {"RefNumber": "393306", "RefComponent": "BW-BCT-QM", "RefTitle": "SM13 Unnecessary Collect Run for QE11", "RefUrl": "/notes/393306"}, {"RefNumber": "384211", "RefComponent": "BC-CST-UP", "RefTitle": "Sorting collective run updates", "RefUrl": "/notes/384211"}, {"RefNumber": "380078", "RefComponent": "BC-BW", "RefTitle": "FAQ: BW delta queue (RSA7): Questions and answers", "RefUrl": "/notes/380078"}, {"RefNumber": "356911", "RefComponent": "BW-BCT-LO", "RefTitle": "Compos. note for Logis. Extractor Cockpit PI 2000.2", "RefUrl": "/notes/356911"}, {"RefNumber": "352389", "RefComponent": "BW-BCT-LO", "RefTitle": "V3 update loops", "RefUrl": "/notes/352389"}, {"RefNumber": "340038", "RefComponent": "BW-BCT-LO", "RefTitle": "Collective Note Logist. Extractor Cockpit PI 2000.1", "RefUrl": "/notes/340038"}, {"RefNumber": "328181", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "Changes to extract structures in the Customizing Cockpit", "RefUrl": "/notes/328181"}, {"RefNumber": "196023", "RefComponent": "BC-CST-UP", "RefTitle": "Collective run update: Composite corr. for 40B,45B,46B,46C", "RefUrl": "/notes/196023"}, {"RefNumber": "1362726", "RefComponent": "BW-BCT-PLA-RAP", "RefTitle": "PO Delivery Completion error in MAP", "RefUrl": "/notes/1362726"}, {"RefNumber": "1081287", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "Data extraction orders are blocking the upgrade process", "RefUrl": "/notes/1081287"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "380078", "RefComponent": "BC-BW", "RefTitle": "FAQ: BW delta queue (RSA7): Questions and answers", "RefUrl": "/notes/380078 "}, {"RefNumber": "328181", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "Changes to extract structures in the Customizing Cockpit", "RefUrl": "/notes/328181 "}, {"RefNumber": "409213", "RefComponent": "BC-CST-UP", "RefTitle": "Error in the sorting of collective run updates", "RefUrl": "/notes/409213 "}, {"RefNumber": "384211", "RefComponent": "BC-CST-UP", "RefTitle": "Sorting collective run updates", "RefUrl": "/notes/384211 "}, {"RefNumber": "196023", "RefComponent": "BC-CST-UP", "RefTitle": "Collective run update: Composite corr. for 40B,45B,46B,46C", "RefUrl": "/notes/196023 "}, {"RefNumber": "1362726", "RefComponent": "BW-BCT-PLA-RAP", "RefTitle": "PO Delivery Completion error in MAP", "RefUrl": "/notes/1362726 "}, {"RefNumber": "67014", "RefComponent": "BC-CST-UP", "RefTitle": "Reorganizing update requests", "RefUrl": "/notes/67014 "}, {"RefNumber": "1081287", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "Data extraction orders are blocking the upgrade process", "RefUrl": "/notes/1081287 "}, {"RefNumber": "652310", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "LBWE: Message MCEX 140 with extract structure change", "RefUrl": "/notes/652310 "}, {"RefNumber": "586163", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Composite SAP Note for SAP ERP Inventory Management in SAP BW", "RefUrl": "/notes/586163 "}, {"RefNumber": "762951", "RefComponent": "BW-BCT-SD-SLS", "RefTitle": "Organization of structure changes in the LBWE", "RefUrl": "/notes/762951 "}, {"RefNumber": "640066", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "Upgrade: Return code 6 with XPRA RMCSBWXP_COM", "RefUrl": "/notes/640066 "}, {"RefNumber": "564593", "RefComponent": "BW-BCT", "RefTitle": "Terminations in the logistics extraction", "RefUrl": "/notes/564593 "}, {"RefNumber": "573036", "RefComponent": "LO-LIS", "RefTitle": "MC3V: V3-Coll. run triggers exception 'ALREADY_RUNNING'", "RefUrl": "/notes/573036 "}, {"RefNumber": "494288", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Missing fields for the 2LIS_03_UM extractor", "RefUrl": "/notes/494288 "}, {"RefNumber": "522770", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "No conversion exit for PS_PSP_PNR-Extraktor 2LIS_03_BF", "RefUrl": "/notes/522770 "}, {"RefNumber": "340038", "RefComponent": "BW-BCT-LO", "RefTitle": "Collective Note Logist. Extractor Cockpit PI 2000.1", "RefUrl": "/notes/340038 "}, {"RefNumber": "356911", "RefComponent": "BW-BCT-LO", "RefTitle": "Compos. note for Logis. Extractor Cockpit PI 2000.2", "RefUrl": "/notes/356911 "}, {"RefNumber": "428294", "RefComponent": "BW-BCT-LO", "RefTitle": "Composite SAP note Logistics Extractn PI 2001.1 & PI 2001.2", "RefUrl": "/notes/428294 "}, {"RefNumber": "417189", "RefComponent": "MM-PUR", "RefTitle": "BW/SAPLEINS - Online update of delta queue", "RefUrl": "/notes/417189 "}, {"RefNumber": "445620", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "Background job not scheduled for the V3 update", "RefUrl": "/notes/445620 "}, {"RefNumber": "393306", "RefComponent": "BW-BCT-QM", "RefTitle": "SM13 Unnecessary Collect Run for QE11", "RefUrl": "/notes/393306 "}, {"RefNumber": "352389", "RefComponent": "BW-BCT-LO", "RefTitle": "V3 update loops", "RefUrl": "/notes/352389 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}