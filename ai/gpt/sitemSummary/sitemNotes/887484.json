{"Request": {"Number": "887484", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1291, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000887484?language=E&token=6851F3F7EFF7FFE6A18BD300793EC304"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000887484", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000887484/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "887484"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 50}, "Recency": {"_label": "Currentness", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "00.00.0000"}, "SAPComponentKey": {"_label": "Component", "value": "PY-AT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Austria"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Austria", "value": "PY-AT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-AT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "887484 - Special delivery \\&quot;Payslip New\\&quot; for pilot customers"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=887484&TargetLanguage=EN&Component=PY-AT&SourceLanguage=DE&Priority=04\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/887484/D\" target=\"_blank\">/notes/887484/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This SAP Note provides an advance delivery of the &quot;Payslip New&quot; function (transaction PCALZ) using an attachment.</p><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>Special delivery<br />Payslip New</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>Pilot Phase</p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><p>For your release, implement the following attachment: Release             Attachment        Version<br />-------------------+-----------------+----------------------------------<br />4.70                L6BK101032.SAR     01 (obsolete)<br />4.70                L6BK101233.SAR    02 (obsolete)<br />4.70                L6BK101550.SAR    03 (obsolete)<br />4.70                L6BK107170.SAR    04 (obsolete)<br />4.70                L6BK107467.SAR    05 (obsolete)<br />4.70                L6BK111986.SAR    06 (obsolete)<br />4.70                L6BK112801.SAR    07 (obsolete)<br />4.70                L6BK120456.SAR    08 (obsolete)<br />4.70                L6BK128767.SAR    09 (deprecated)<br />4.70                L6BK128856.SAR    10 (obsolete)<br />4.70                L6BK129092.SAR    11 (obsolete)<br />4.70                L6BK130607.SAR    12 (obsolete)<br />4.70                L6BK131936.SAR    13 (obsolete)<br />4.70                L6BK134976.SAR    14 (available)</p> <b>Caution: This function will    be made generally available with HR Support Package 71. This SAP Note is then no longer updated with new SAR files..-------------------+-----------------+----------------------------------</b><br /> <p>ADDITIONAL MANUAL HANDLING REQUIREMENT:</p> <UL><LI>Use transaction SM30 and T599W to copy the entry for RPCGLZA0 to RPLGLZA0.</LI></UL> <UL><LI>Make the following statement inactive in the report RPCSVBA2: * perform MAKE_ELDA_PARAMETERS_INVISIBLE(RPCL16A2).</LI></UL> <p>The slides of the roll-out workshop with all tables that you need to check or enhance are contained in the document according to the attachment:</p> <b>Payslip_new-Workshop20051006_2.pps<br /></b><br /> <p><STRONG>Changes since the roll out workshop:</STRONG></p> <OL>1. You can use &quot;Selected Employees -> Display Payslip from Database&quot; to reset status confirmations that were set incorrectly.</OL> <OL>2. All menu options that you access using &quot;Selected Employees&quot; can also be configured with report variants.</OL> <OL>3. All menu options that you access using &quot;Selected Employees&quot; can now also be started in the background using the report RPLGLZA0.</OL> <OL>4. &quot;Create Payslip Without ELDA&quot; can now also be started in the background for all employees using the report RPLGLZA0.</OL> <OL>5. &quot;Create Separately by Payslip Type&quot; can now also be started in the background for all employees using the report RPLGLZA0.</OL> <OL>6. For &quot;Selected Employees&quot;, you can now restrict the selection by payroll area across the entire organizational assignment +.</OL> <OL>7. Status confirmations can now also be restricted using &quot;Selected Employees&quot; using personnel number intervals (in the case of performance problems online).</OL> <OL>8. For payslip simulation, a payslip can be forced for each payroll month (for salary statements)</OL> <OL>9. Old reports (RPCL16A2, and so on) hide their ELDA parameters as soon as there is live data with the &quot;Payslip New&quot;. These can still be used for display with old forms.<br /></OL><p><STRONG>Changes with program version 03:</STRONG></p> <OL>10. Implementation Guide (IMG) for Payslip.</OL> <OL>11. Transaction &quot;PCALZ&quot; now has an &quot;Info&quot; button with comprehensive online help including Legacy Data Transfer. You can also jump to the IMG from here.</OL> <OL>12. The menu option &quot;Legacy Data Transfer&quot; now also has an &quot;Info&quot; button with relevant documentation.</OL> <OL>13th In test mode, the legacy data transfer now has an enhanced database selection option to parallelize the problem cases of the legacy data transfer in packages.</OL> <OL>14. The legacy data transfer would be corrected with regard to advance payroll runs. If you manage advance payroll, reset your legacy data transfer and repeat it.</OL> <OL>15th Table lock added when entering the status confirmation.</OL> <OL>16. Table lock added when resetting the last status.</OL> <OL>17. Payslip corrected for &quot;Joint Taxation&quot;</OL> <OL>18. Error corrected for E18 messages in simulation mode (LZ was missing in ALV list)</OL> <OL>19. Error corrected for annual reports with current period December in simulation mode (payslip was missing in ALV list)</OL> <OL>20. Overwriting of data mediums via RPUELDA0 prevented</OL> <OL>21. Improved MEMORY_ID for RPUELDA0 for multiple calls in the system</OL> <OL>22. Online error message HRPAYATLZ/078 corrected</OL> <OL>23. Cumulation wage type MV transfer amount in BL16/BL6A set correctly (V_T596I)</OL> <OL>24. RPUGLZA0: Batch + Execute with Print Enabled</OL> <OL>25. RPLGLZA0: Batch + Execute with Print Enabled</OL> <OL>26. Print form HR_AT_LZ_01 for joint payslip L16 and BGN: Relevant SI number output: If sequential number is not known, output 0000. Output Date of Birth with 6 Characters Only</OL> <OL>27. Print form HR_AT_LZ_01 for joint payslip L16 and BGN: Print output of social status (for L16) is incorrect (L0PAFF21)<br /></OL> <p><STRONG>Changes with program version 04:</STRONG></p> <OL>28. If an employee leaves and leave compensation exceeds the end of the month, a payslip SI with the provisional contribution bases is now already reported for the leaving month. The SI payslip is corrected at the end of the leave compensation.</OL> <OL>29. Correction of ELDA identification part for BVA insured persons.</OL> <OL>30. The tax office number of the employer is now displayed correctly for the ELDA notification type LF.</OL> <OL>31. No longer terminates with CONVT_NO_NUMBER if the table T5A1P also contains data mediums with a data medium number that is not purely numeric.</OL> <OL>32. No error message &quot;Tax number nnnnnnn from T5A2L does not exist in T5A0A&quot; more for periods before the legacy data transfer.</OL> <OL>33. Correction if the error &quot;No leaving action for payslip SI for GKK before reentry...&quot; is incorrectly triggered for leaving and reentry.</OL> <OL>34. Correction if the error &quot;Program error 01.01.yyyy&quot; is triggered incorrectly for the employment status &quot;Inactive&quot;.</OL> <OL>35. Correction to the form for the payslip SI for GKK in the &quot;Special Payment Entitlement&quot; field.</OL> <OL>36. The error message &quot;No account number exists for the following key in T5A1S; dd/mm/yyyy&quot; is no longer generated for semiretirement using GKK.</OL> <OL>37. Revision of the cumulation wage types for the subapplications BL16 (Payslip Tax Domestic) and BL6A (Payslip Tax Abroad).<br />If necessary, you must adjust the view V_T596J to the changes.</OL> <OL>38. Correction if the error &quot;No leaving action for payslip L16 before reentry...&quot; is triggered due to payroll gaps (leaving and reentry). This is incorrect.<br /></OL> <p><STRONG>Changes with program version 05:</STRONG></p> <OL>39. Add the exporting parameter P_NO_L16 to the BAdI interface IF_EX_HRPAYAT_LZ_L16_ARTL0->GET_ARTL0_APPL.</OL> <OL>40. Enhancement for employees who switch between GKK and BVA.</OL> <OL>41. Correction for error during processing of annual payslips (&quot;Creation and Administration for January&quot;). Payslips for leavings from the previous year are flagged as incorrect in the output list, but no error message is displayed. In the production run, the system issues an error message without a message text.</OL> <OL>42. Correction so that leavings during an inactive work relationship are also recognized correctly.</OL> <OL>43. Correction if error message &#39;Program error B&#39; is triggered.</OL> <OL>44. Correction if the system incorrectly issues the error message &quot;No payroll result after dd.mm.yyyy or no leaving action for payslip...&quot;.</OL> <OL>45. Revision of the cumulation wage types for the subapplication BL16 (&quot;Payslip Tax Domestic&quot;).<br />If necessary, you must adjust the view V_T596J to the changes.<br /></OL> <p><STRONG>Changes with program version 06:</STRONG></p> <OL>46. Correction if the partial remuneration basis and the number of days with partial remuneration are not displayed on the payslip SI because the partial remuneration wage type used is not assigned to an SI split.</OL> <OL>47. Correction if an exception is triggered when the notification status is reset for the entire data medium.</OL> <OL>48. Correction if payslips are flagged as incorrect due to organizational reassignment in previous months.</OL> <OL>49. Correction if notification E18 is not created due to an error message despite correct Customizing.</OL> <OL>50. Correction if an employee is entitled to an exemption according to EStG Sec. 35 and the payslip is rejected with the notification &quot;Exemption KZFB3 not permitted for social positions 1-6&quot;.</OL> <OL>51. Enhancement to payslip L16 to specify a (spouse) partner for entitlement to the deductible amount for sole wage earners if subtypes that differ from the standard (1=spouse, 13=life partner) are used (BAdI HRPAYAT_IT0021_KTO).</OL> <OL>52. Correction for creation of ELDA notifications for notifications E18. The ELDA field FSART is blank in the notification record. This is incorrect.</OL> <OL>53. Correction when using the parameter P_NO_L16 for the BAdI HRPAYAT_LZ_L16_ARTL0. If this parameter was set to &#39;X&#39; by the BAdI, a payslip L16 was still generated under certain conditions.</OL> <OL>54. Correction if the employer&#39;s tax number remains initial on payslip L16. Due to a company split, the old tax number remains valid for some of the employees, while the remaining employees are assigned to a new tax number.</OL> <OL>55. Correction if payslips from previous years are not created for individual personnel numbers.</OL> <OL>56. Correction of the print output for notification E18. Instead of the tax office number and tax number of the sold-to party, the tax number of the payroll office is output.</OL> <OL>57. Correction if productive payslips are also created during the legacy data transfer. This is incorrect. As a prerequisite, more than one payslip must be issued for the employee at the same time due to an organizational reassignment.</OL> <OL>58. If an employee leaves without UE and later has a leaving with UE in the same month, the error &quot;No leaving action for payslip SI before reentry&quot; is triggered.</OL> <OL>59. Revision of cumulation wage types for payslip L16.<br />&#39;BL16&#39; (Payslip Tax Domestic) and &#39;BL6A&#39; (Payslip Tax Abroad)<br />If necessary, you must adjust the view V_T596J to the changes.</OL> <OL>60. Revision of the validation rules for payslip L16.<br />A missing SI number of the partner on payslip L16 leads to a rejection if the deductible amount for sole wage earner was taken into account. Maintain the partner in infotype 0021 (Family/Related Person) or correct infotype 0042 (Tax A) and repeat payroll.<br />A missing number of children on payslip L16 leads to a rejection if the deductible amount for single parent was taken into account. Correct infotype 0042 (Tax A) and repeat payroll.</OL> <OL>61. Enhancement of the check instructions for payslip SI for BVA.<br />If the general contribution basis exceeds the maximum amount of EUR 99,999.99 that can be reported electronically, no notification is required as a paper form; instead, the maximum amount 99,999.99 must be reported.</OL> <OL>62nd Revision of personnel calculation rule AE80. If an E18-relevant reference wage type is entered during an inactive period, or if the tax procedure/exemption reason is not &#39;0W&#39;, the payroll now rejects it.<br /></OL> <p><STRONG>Changes with program version 07:</STRONG></p> <OL>63. Correction of personnel calculation rule A042. Wage type /59A is incorrectly added from ORT during retroactive accounting during an E18 period. The activity type is incorrect.</OL> <OL>64. For BVA insured persons, the ELDA notification for the payslip SI is rejected because the relevant insurance branches are communicated with &#39; X&#39; instead of &#39;NJ&#39; in the ELDA field VSWZ (insurance branch). The information &quot;Part-Time Employee&quot; is no longer to be reported using the ELDA field ARAN, but about the fourth position of the insurance branch that was previously unused.</OL> <OL>65. The BAdI IF_EX_HRPAYAT_IT0021_KTO is now contained in the SAR file.<br /></OL> <p><STRONG>Changes with program version 08:</STRONG></p> <OL>66. Correction for payslip SI (BVA): Under certain constellations, contribution bases are reported even though no contributions were paid.</OL> <OL>67. Correction to payslip SI (BVA) for part-time employees. The contribution bases are not displayed.</OL> <OL>68. Correction for SI payslip: Employee pension contribution and basis are too low under certain conditions if a notional assessment basis exists.</OL> <OL>69. Correction if a leaving payslip that has already been reported is reversed in the subsequent period. This is incorrect.</OL> <OL>70. Correction if the error &quot;No payroll result after ... or no leaving action for payslip SI ...&quot; is reported when an employee changes between GKK and BVA in a previous period.</OL> <OL>71. Enhancement of the processing modes for selected personnel numbers &quot;Print Paper Form&quot;, &quot;Display Created Payslips&quot;, and &quot;Confirm Status (Cell)&quot;. In addition to the personnel number, you can also make a selection according to other selection criteria.</OL> <OL>72. Correction for personnel calculation rule AE80 if an incorrect benefit type is determined under certain constellations.<br />Use transaction PE02 to change the rule for the grouping * and the wage types **** as follows: Then repeat the settlement.</OL> <p>VarKey FZ T Operation Operation Operation Operation Operation -------------+---------+---------+---------+---------++---------++--------<br />           P PCY AE80A NEXTR                          TAX EXEMPTION 0W?<BR/>         1   ELIMI *   RESET A2  ZERO= NRA NEXTR<br />         2 D NUM=E /59ANUM?0                E18-ARTE0 ALREADY DETERMINED?<br />*                                           YES<br />=          D TABLEP3   NUM=BZUORDNUM?0<br />= *          ADDWTE/59A                     OFF  PNNNN<br />= =          NUM=08    ADDWTE/59A           DEFAULT<br /></p> <OL>73. The payroll office is not output if you have entered only one entry with a validity to 12/31/9999 for the relevant tax number in the view V_T5A2L.</OL> <OL>74. You repeat payslip creation for individual personnel numbers and want to create print forms from the displayed list by selecting the individual lines. For those printable records for which there is also a deletion entry, no payslip is printed. This is incorrect. The system may issue the message &quot;There are no records suitable for form output in the selection&quot; in the status line.</OL> <OL>75. You use the user exit PATS0001 for the function module HR_AT_GET_ORG_DATA and require the subapplication to differentiate further. However, when the wage office data is read from transaction PCALZ, the subapplication is not supplied in the interface.<br /></OL> <p><STRONG>Changes with program version 09:</STRONG></p> <OL>76. Due to the legal change 2006/2007  , a new SAR file L6BK128767.SAR is attached. This file contains both the legal changes for the year-end legal change 2006/2007 (see SAP Note 996998) and the  functions for the &quot;Payslip New&quot;.<br /></OL> <p><STRONG>Changes with program version 10:</STRONG></p> <OL>77. A new SAR file L6BK128856.SAR is attached due to the new table T799L30.<br /></OL> <p><STRONG>Changes with program version 11:</STRONG></p> <OL>78. A new SAR file L6BK129092.SAR is attached due to the new SAPscript form HR_AT_LZ_GLZ_02.<br /></OL> <p><STRONG>Changes with program version 12:</STRONG><br />SAR file  L6BK130607.SAR is attached due to the following error:</p> <OL>79. Improvement to legacy data transfer (see SAP Note 1015299)</OL> <p>You can implement the corrections from Note 1015299 manually in advance using the correction instructions.</p> <OL>80. No record of ELDA notification type AD created in table T5A1O<br /></OL> <p><STRONG>Changes with program version 13:</STRONG><br />SAR file L6BK131936.SAR is attached due to the following error:</p> <OL>81. In the form for the joint payslip HR_AT_LZ_GLZ_02, the fields for the care allowance and the exemption amount according to Sec. 35 are positioned incorrectly.<br /></OL> <p><STRONG>Changes with program version 14:</STRONG></p> <OL>82. SAR file L6BK134976.SAR is attached due to SAP Notes 959041,1001837 and 1031077.<br /></OL> <p><STRONG>Changes with program version 15:</STRONG></p> <OL>83. SAR file L6BK136518.SAR is attached due to SAP Notes 1050609 and 1057450.</OL></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "PA-PA-AT (Austria)"}, {"Key": "Owner                                                                                    ", "Value": "D001888"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON><PERSON><PERSON> (D020892)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000887484/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "L6BK134976.SAR", "FileSize": "1809", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000510952005&iv_version=0050&iv_guid=E39A42F366148847853264DABB47EA66"}, {"FileName": "Lohnzettel_neu-Workshop20051006_2.ppt", "FileSize": "525", "MimeType": "application/vnd.ms-powerpoint", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000510952005&iv_version=0050&iv_guid=9739D58FAC8BFC439E1010F6BCD051EA"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=887484&TargetLanguage=EN&Component=PY-AT&SourceLanguage=DE&Priority=04\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/887484/D\" target=\"_blank\">/notes/887484/D</a>."}}}}