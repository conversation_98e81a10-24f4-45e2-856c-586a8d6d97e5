{"Request": {"Number": "1654760", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 302, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000009826572017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001654760?language=E&token=2E85EF162F7F4B481AE3D5A9E6A5109C"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001654760", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1654760"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.11.2011"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-BD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Basic Data"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Data", "value": "RE-FX-BD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-BD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1654760 - Enhancing search helps: Example of validity, deletion flag"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You have specific requirements with regard to the functions of search helps. For example, you want to use search helps that:</p> <UL><LI>Do not display objects that are no longer valid.</LI></UL> <UL><LI>Do not display objects that have a deletion flag or a deletion indicator.</LI></UL> <p><br />This note describes how to enhance search helps in RE-FX and provides an example for a BAdI implementation that enables you to eliminate invalid objects from the result list. In this context, objects with a deletion flag or a deletion indicator and objects whose validity expired six months ago are considered to be \"invalid\".<br /><br />See also the attached notes in this context.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>REBDBE, REBDBU, REBDPR, REBDRO, RECN</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You have implemented Note 1426012.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Search helps can be enhanced if they are collective search helps. The following example of the building describes how to perform the enhancement. You can also use the same procedure for other objects and contracts.<br /><br />First determine which collective search help is used for the data element (choose F1 for the field and then navigate to the data element). In the case of the building, this is the collective search help&#x00A0;REBDBUNO. This collective search help includes elementary search helps, for example, REBDBUNOA.<br /><br />Then create your own elementary search help in the customer namespace, for example, ZREBDBUNOA. The easiest way to do this is to use an existing elementary search help as a template (for example, REBDBUNOA) and to copy this (using transaction SE11). You should change the description text in the copy. Make sure that the technical field OBJNR (object number) is contained in your search help. Enter the search help exit RECA_SEARCH_HELP_F4UT_OPT_COLW (BAdI call and column optimization) or enter RECA_SEARCH_HELP_CALL_BADI directly (only BAdI call). Alternatively, you can also use RECA_SEARCH_HELP_OUT_AGREEMENT as the search help exit for contracts. Activate your search help.<br /><br />Enhance the collective search help as follows so that your new search help is called: To do this, call transaction SE12, open the collective search help REBDBUNO and choose \"Goto -&gt; Append Search Help\". Assign a (new) name in the customer namespace, for example, ZZREBDBUNOA. Enter the new search help that you created ZREBDBUNOA as an included search help in ZZREBDBUNOA. Assign the parameters. Activate the collective search help.<br /><br />You can now test this search help. If you have only copied the search help and have not changed it, it works in the same way as the template. However, the search help ZREBDBUNOA now also contains the search help exit RECA_SEARCH_HELP_F4UT_OPT_COLW. In this exit, the BAdI BADI_RECA_SEARCH_HELP is called. You can use this BAdI to filter out objects from the display that you do not want the user to see.<br /><br />To do this, call transaction SE18, open the BAdI BADI_RECA_SEARCH_HELP and create an implementation. If, for example, in your search help ZREBDBUNOA, you want to display only buildings that do not have a deletion flag and whose validity expired six months ago, you can copy the example class CL_EXM_IM_RECA_SEARCH_HELP_VAL for this.<br /><br />Adjust the source code of your implementation according to your requirements. Note that the BAdI is called in all search helps that have the exit RECA_SEARCH_HELP_F4UT_OPT_COLW. In other words, you can use the same source code for all these object types. As a prerequisite, an elementary search help that calls the search help exit has been created in each case. Change the source code so that the system requests the search help names that are to be executed for the source code.<br /><br />Activate your BAdI implementation and test whether the F4 helps work correctly.<br /><br />If you also want to deactivate elementary standard search helps, enter their names as an included search help in the append and select the \"Hidden\" indicator.<br /><br />You can perform the enhancements described without modification.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "RE-FX-CN (Contract)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D002072)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I035786)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001654760/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001654760/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001654760/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001654760/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001654760/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001654760/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001654760/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001654760/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001654760/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "939971", "RefComponent": "RE-FX", "RefTitle": "FAQ and consulting notes for RE-FX", "RefUrl": "/notes/939971"}, {"RefNumber": "937084", "RefComponent": "RE-FX", "RefTitle": "Enhancing a search help without modification in RE-FX", "RefUrl": "/notes/937084"}, {"RefNumber": "1729849", "RefComponent": "RE-FX-CN", "RefTitle": "Search help RECNNTREASON and BAdI BADI_RECA_SEARCH_HELP", "RefUrl": "/notes/1729849"}, {"RefNumber": "1578783", "RefComponent": "RE-FX", "RefTitle": "Customer: Alternative search helps by real estate", "RefUrl": "/notes/1578783"}, {"RefNumber": "1426012", "RefComponent": "RE-FX-CN", "RefTitle": "Contract: Exit added for search help", "RefUrl": "/notes/1426012"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "939971", "RefComponent": "RE-FX", "RefTitle": "FAQ and consulting notes for RE-FX", "RefUrl": "/notes/939971 "}, {"RefNumber": "1729849", "RefComponent": "RE-FX-CN", "RefTitle": "Search help RECNNTREASON and BAdI BADI_RECA_SEARCH_HELP", "RefUrl": "/notes/1729849 "}, {"RefNumber": "937084", "RefComponent": "RE-FX", "RefTitle": "Enhancing a search help without modification in RE-FX", "RefUrl": "/notes/937084 "}, {"RefNumber": "1426012", "RefComponent": "RE-FX-CN", "RefTitle": "Contract: Exit added for search help", "RefUrl": "/notes/1426012 "}, {"RefNumber": "1578783", "RefComponent": "RE-FX", "RefTitle": "Customer: Alternative search helps by real estate", "RefUrl": "/notes/1578783 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "606", "To": "606", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-APPL 600", "SupportPackage": "SAPKGPAD21", "URL": "/supportpackage/SAPKGPAD21"}, {"SoftwareComponentVersion": "EA-APPL 602", "SupportPackage": "SAPK-60211INEAAPPL", "URL": "/supportpackage/SAPK-60211INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 603", "SupportPackage": "SAPK-60310INEAAPPL", "URL": "/supportpackage/SAPK-60310INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 604", "SupportPackage": "SAPK-60411INEAAPPL", "URL": "/supportpackage/SAPK-60411INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 605", "SupportPackage": "SAPK-60507INEAAPPL", "URL": "/supportpackage/SAPK-60507INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 606", "SupportPackage": "SAPK-60602INEAAPPL", "URL": "/supportpackage/SAPK-60602INEAAPPL"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "EA-APPL", "NumberOfCorrin": 6, "URL": "/corrins/0001654760/229"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 6, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "604", "Number": "1426012 ", "URL": "/notes/1426012 ", "Title": "Contract: Exit added for search help", "Component": "RE-FX-CN"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}