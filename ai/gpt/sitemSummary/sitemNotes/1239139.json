{"Request": {"Number": "1239139", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 316, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000007241342017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001239139?language=E&token=8BC0E0C3EA064F8E91AA3338F67B4127"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001239139", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001239139/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1239139"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Performance"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.08.2012"}, "SAPComponentKey": {"_label": "Component", "value": "CRM-BE-BD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Billing Document"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Customer Relationship Management", "value": "CRM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Billing", "value": "CRM-BE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM-BE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Billing Document", "value": "CRM-BE-BD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM-BE-BD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1239139 - Buffering of Number Ranges in CRM Billing"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The performance of the parallel batch processing is poor.<br />In transaction SM66 in column table the abbreviation NRIV is displayed for most of the batch work processes.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>NRIV SNRO TNRO OFFLDOCNO ITL_OFDNUM</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The buffering of number range object BEABILLDOC and BEACOLLRUN has been activated with buffering type <B>Main memory buffering</B>.<br />However due to a restriction in the programming code the number range buffering doesn't work.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. Note: CRM Billing doesn't support the new mechanism of <B>parallel buffering </B>(which is described in note 599157), because there is no real benefit for this type of buffering. The reason is that the legal requirements for complete document numbering is supported via the official document number and not via the identifying primary number of the billing documents.</OL> <OL>2. If the feature <B>Official Document Numbering</B> is used, the corresponding number range group ITL_OFDNUM must not be buffered.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;However in this case also the buffering for the primary key number of the billing document via range group BEABILLDOC may not have a significant effect. Therefore it is recommended to separate the batch billing processing for countries which require a complete document numbering from those countries who don't. This can be achieved with a distinguished job scheduling using the corresponding billing organization as selection criteria. <OL>3. After you have implemented the correction, generate the following function module of the CRMB application, as described in NOTE 630914:<br />Object: BD<br />Layer:  O-Layer<br />Method:  SAVE</OL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "DownPort/UpPort-WF", "Value": "DownPort check necessary"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D020871)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D020871)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001239139/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001239139/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001239139/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001239139/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001239139/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001239139/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001239139/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001239139/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001239139/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "630914", "RefComponent": "CRM-BE", "RefTitle": "Regenerating individual objects in a BE application", "RefUrl": "/notes/630914"}, {"RefNumber": "599157", "RefComponent": "BC-SRV-NUM", "RefTitle": "Number ranges: New buffering method", "RefUrl": "/notes/599157"}, {"RefNumber": "504875", "RefComponent": "BC-SRV-NUM", "RefTitle": "Buffering of number ranges", "RefUrl": "/notes/504875"}, {"RefNumber": "453979", "RefComponent": "CRM-BE-BD", "RefTitle": "Performance CRM Billing - composite SAP note", "RefUrl": "/notes/453979"}, {"RefNumber": "1294779", "RefComponent": "CRM-BF", "RefTitle": "SAP CRM 5.2 - SP07", "RefUrl": "/notes/1294779"}, {"RefNumber": "1294778", "RefComponent": "CRM-BF", "RefTitle": "SAP CRM 2005 - SP Stack 14", "RefUrl": "/notes/1294778"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "504875", "RefComponent": "BC-SRV-NUM", "RefTitle": "Buffering of number ranges", "RefUrl": "/notes/504875 "}, {"RefNumber": "1294778", "RefComponent": "CRM-BF", "RefTitle": "SAP CRM 2005 - SP Stack 14", "RefUrl": "/notes/1294778 "}, {"RefNumber": "1294779", "RefComponent": "CRM-BF", "RefTitle": "SAP CRM 5.2 - SP07", "RefUrl": "/notes/1294779 "}, {"RefNumber": "453979", "RefComponent": "CRM-BE-BD", "RefTitle": "Performance CRM Billing - composite SAP note", "RefUrl": "/notes/453979 "}, {"RefNumber": "599157", "RefComponent": "BC-SRV-NUM", "RefTitle": "Number ranges: New buffering method", "RefUrl": "/notes/599157 "}, {"RefNumber": "630914", "RefComponent": "CRM-BE", "RefTitle": "Regenerating individual objects in a BE application", "RefUrl": "/notes/630914 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BBPCRM", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "520", "To": "520", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "701", "To": "701", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "BBPCRM 500", "SupportPackage": "SAPKU50015", "URL": "/supportpackage/SAPKU50015"}, {"SoftwareComponentVersion": "BBPCRM 520", "SupportPackage": "SAPKU52008", "URL": "/supportpackage/SAPKU52008"}, {"SoftwareComponentVersion": "BBPCRM 600", "SupportPackage": "SAPKU60004", "URL": "/supportpackage/SAPKU60004"}, {"SoftwareComponentVersion": "BBPCRM 700", "SupportPackage": "SAPKU70001", "URL": "/supportpackage/SAPKU70001"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "BBPCRM", "NumberOfCorrin": 3, "URL": "/corrins/0001239139/63"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}