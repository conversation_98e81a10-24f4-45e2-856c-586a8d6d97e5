{"Request": {"Number": "3066214", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 339, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000908132021"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003066214?language=E&token=ABD6B7F6139CCAF9610B146AC7D7C7C1"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003066214", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3066214"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.06.2021"}, "SAPComponentKey": {"_label": "Component", "value": "CA-GTF-MIG"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP S/4HANA Data Migration Cockpit Content"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Application Functions", "value": "CA-GTF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-GTF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP S/4HANA Data Migration Cockpit Content", "value": "CA-GTF-MIG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-GTF-MIG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "3066214 - SAP S/4HANA Migration Cockpit: JUNE 2021 - Central correction Note for content issues for SAP S/4HANA 2020"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You have an issue with the SAP S/4HANA Data Migration content delivered for SAP S/4HANA 2020. Error with the content.</p>\r\n<p>This Note is valid for:</p>\r\n<ul>\r\n<li>SAP S/4HANA -&#160;LTMC - \"Transferring Data Using Staging Tables\"</li>\r\n<li>SAP S/4HANA - Migrate Your Data Migration Cockpit -&#160;\"Transferring Data Using Staging Tables\"</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>FI - G/L account, FI - G/L account - extend existing record by new org levels, Clearing Specific to Ledger Groups, XLGCLR, XKRES,&#160;FI - Accounts payable open item, FI &#8211; Accounts receivable open item, Cash discount, Amount Eligible for Cash Disc. (Doc.Cur),&#160;SKFBT,&#160;ACSKT,&#160;Cash discount amount in curr. of DocTyp, FI - G/L account balance and open/line&#160; item,&#160;GZ262, Transaction type, RMVCT, SIF_GL_ACCOUNT_3,&#160;SIF_GL_ACC_EXT,&#160;SIF_OPEN_ITEM_AP,&#160;SIF_OPEN_ITEM_AR,&#160;SIF_GL_BALANCE</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You have installed SAP S/4HANA 2020 (SP00 - SP02)</p>\r\n<p>You are using SAP S/4HANA migration cockpit</p>\r\n<p>You are using the&#160;pre-delivered SAP S/4HANA Data migration content without any modifications</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The TCI included with this SAP Note fixes the following issues listed in the table below. For detailed description on the issues see the linked SAP Notes.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Technical Name</td>\r\n<td>Type</td>\r\n<td>Issue</td>\r\n<td>SAP Note&#160;</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SIF_GL_ACCOUNT_3</p>\r\n<p>SIF_GL_ACC_EXT</p>\r\n</td>\r\n<td>Migration object</td>\r\n<td>\r\n<p>The field \"Clearing Specific to Ledger Groups\" was migrated into the wrong assigned target field XKRES \"Indicator: Can Line Items Be Displayed by Account?\"</p>\r\n</td>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/3051389\">3</a><a target=\"_blank\" href=\"/notes/3063891\">063891</a></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SIF_OPEN_ITEM_AP</p>\r\n<p>SIF_OPEN_ITEM_AR</p>\r\n</td>\r\n<td>Migration object</td>\r\n<td>\r\n<p>The document was migrated with a line for currency '10' wherein the DISC_BASE and DISC_AMT was not submitted</p>\r\n</td>\r\n<td><a target=\"_blank\" href=\"/notes/3059582\">3059582</a></td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SIF_GL_BALANCE</p>\r\n</td>\r\n<td>Migration object</td>\r\n<td>\r\n<p>Migration field rule contains a conversion exit alpha which automatically add a leading zero, e.g. a valid transaction type 10 is changed to non existing value 010.</p>\r\n</td>\r\n<td><a target=\"_blank\" href=\"/notes/3059582\">3</a><a target=\"_blank\" href=\"/notes/3066402\">066402</a></td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Be aware that the TCI only corrects the related objects of SAP delivered content. As a result, the generated migration objects will be updated automatically.</p>\r\n<p><strong>Note:</strong>&#160;If you have modified/copied your object, the correction is not done within your modified/copied object.</p>\r\n<p>You may refer KBA&#160;<a target=\"_blank\" href=\"/notes/2543372\">2543372</a>&#160;- \"How to implement a Transport-based Correction Instruction\".</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I055188)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I307134)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0003066214/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003066214/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003066214/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003066214/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003066214/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003066214/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003066214/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003066214/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003066214/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2543372", "RefComponent": "BC-UPG-NA", "RefTitle": "How to implement a Transport-based Correction Instruction", "RefUrl": "/notes/2543372"}, {"RefNumber": "3066402", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: G/L account balance and open/line item -  Transaction type 0xx not defined", "RefUrl": "/notes/3066402"}, {"RefNumber": "3063891", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: FI - G/L account or FI - G/L account - extend existing record by new org levels -  field Clearing Specific to Ledger Groups not correctly mapped", "RefUrl": "/notes/3063891"}, {"RefNumber": "3059582", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Accounts payable open item or Accounts receivable open item-  Cash discount is not taken in consideration by clearing transactions", "RefUrl": "/notes/3059582"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2537549", "RefComponent": "CA-GTF-MIG", "RefTitle": "Collective SAP Note and FAQ for SAP S/4HANA Migration cockpit - File/Staging (on premise / S4CORE)", "RefUrl": "/notes/2537549 "}, {"RefNumber": "3063891", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: FI - G/L account or FI - G/L account - extend existing record by new org levels -  field Clearing Specific to Ledger Groups not correctly mapped", "RefUrl": "/notes/3063891 "}, {"RefNumber": "3059582", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Accounts payable open item or Accounts receivable open item-  Cash discount is not taken in consideration by clearing transactions", "RefUrl": "/notes/3059582 "}, {"RefNumber": "3066402", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: G/L account balance and open/line item -  Transaction type 0xx not defined", "RefUrl": "/notes/3066402 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "S4CORE", "NumberOfCorrin": 1, "URL": "/corrins/0003066214/19773"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}