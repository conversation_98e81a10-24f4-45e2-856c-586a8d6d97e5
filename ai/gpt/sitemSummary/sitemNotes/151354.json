{"Request": {"Number": "151354", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 352, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014675842017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000151354?language=E&token=9323D9EF6B7F63B4BDE158F25DA8D466"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000151354", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000151354/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "151354"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Workaround of missing functionality"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.08.2004"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DWB"}, "SAPComponentKeyText": {"_label": "Component", "value": "ABAP Workbench, Java IDE and Infrastructure"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "ABAP Workbench, Java IDE and Infrastructure", "value": "BC-DWB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "151354 - Editor lock in SAP Includes"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>An advance correction or an error correction should be made on an SAP program because of a note. However, this is not possible because SAP delivered this program with an editor lock.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Advance correction, error correction, editor lock, EU522</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Programs with editor lock may only be changed by the last person who changed it. The concept of the editor lock helps the author to protect his program against non-allowed changes.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Deletion of the editor lock is a high priority operation which may only be carried out in the case of emergency. Only carry this out in the following cases:</p> <UL><LI>SAP mistakenly delivered the program with editor lock</LI></UL> <UL><LI>SAP delivered an unchangeable program with an error<br /></LI></UL> <p>To reset the editor lock proceed as follows:</p> <OL>1. You (or your system administrator) create a user 'SAP'. This user needs developer authorizations.</OL> <OL>2. Register this user as a developer using OSS.</OL> <OL>3. Start transaction SE38. Specify the program name and select the \"Change(F6)\" button.</OL> <OL>4. Register the object that is to be changed and enter the registration keys for the developer and object.</OL> <OL>5. Now deactivate the modification assistant (Edit -&gt; Modification operations -&gt; Deactivate assistant).</OL> <OL>6. Now go to the 'Attributes' view (Goto -&gt; Attributes). Select the 'Change' function.</OL> <OL>7. Remove the flag on the attribute screen in the field 'Editor lock'. Save the program attributes.<br /></OL> <p>The editor lock is now reset and you can modify the program.<br />Now delete the user 'SAP' again from your system or lock it.<br /><br />If the problem occurs with function modules, use transaction SE37 and proceed as before.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D019187)"}, {"Key": "Processor                                                                                           ", "Value": "D033374"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000151354/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000151354/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000151354/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000151354/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000151354/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000151354/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000151354/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000151354/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000151354/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "945927", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/945927"}, {"RefNumber": "939971", "RefComponent": "RE-FX", "RefTitle": "FAQ and consulting notes for RE-FX", "RefUrl": "/notes/939971"}, {"RefNumber": "726104", "RefComponent": "LO-GT-TEW", "RefTitle": "BAdI implementation cannot be assigned to \"*\"", "RefUrl": "/notes/726104"}, {"RefNumber": "607406", "RefComponent": "CO-PC-OBJ-MTO", "RefTitle": "Down payment clearing and commitment processing", "RefUrl": "/notes/607406"}, {"RefNumber": "571543", "RefComponent": "FI-GL-GL-A", "RefTitle": "FI: Held documents are lost after the upgrade.", "RefUrl": "/notes/571543"}, {"RefNumber": "560605", "RefComponent": "MM-SRV", "RefTitle": "Commitment reduction for service requisitions", "RefUrl": "/notes/560605"}, {"RefNumber": "543577", "RefComponent": "SD-BIL", "RefTitle": "Editor lock in Include LV60AB29", "RefUrl": "/notes/543577"}, {"RefNumber": "504990", "RefComponent": "BC-SRV-QUE", "RefTitle": "Eliminating various query problems in the HR application", "RefUrl": "/notes/504990"}, {"RefNumber": "409985", "RefComponent": "LE-WM", "RefTitle": "New parameter for SAP enhancements MWMD0001 and MWMD0002", "RefUrl": "/notes/409985"}, {"RefNumber": "369264", "RefComponent": "IS-HT-SW", "RefTitle": "RAISE_EXCEPTION FKREL_NOT_FOUND", "RefUrl": "/notes/369264"}, {"RefNumber": "339554", "RefComponent": "PP-SFC-PLN-CPT", "RefTitle": "OPL4: Profiles are stored incorrectly", "RefUrl": "/notes/339554"}, {"RefNumber": "338393", "RefComponent": "LE-SHP-DL-BD", "RefTitle": "Message VL200 for the batch sales order stock", "RefUrl": "/notes/338393"}, {"RefNumber": "313503", "RefComponent": "BC-BW", "RefTitle": "Customer exit master data: Interface change", "RefUrl": "/notes/313503"}, {"RefNumber": "305118", "RefComponent": "BC-SRV-QUE", "RefTitle": "Eliminating various query problems in HR", "RefUrl": "/notes/305118"}, {"RefNumber": "187275", "RefComponent": "IS-MP-PP", "RefTitle": "Mill OC:user exit cost distribution in combination", "RefUrl": "/notes/187275"}, {"RefNumber": "1826482", "RefComponent": "PSM-FA-ADB", "RefTitle": "FMADB_POST creates unnecessary lines", "RefUrl": "/notes/1826482"}, {"RefNumber": "153684", "RefComponent": "BC-SRV-QUE", "RefTitle": "Eliminating various query problems in HR", "RefUrl": "/notes/153684"}, {"RefNumber": "149597", "RefComponent": "MM-IV", "RefTitle": "MRRL invoice value doubles during price determination for GR", "RefUrl": "/notes/149597"}, {"RefNumber": "1276115", "RefComponent": "CRM-IC-RDI", "RefTitle": "IDI Fact Gathering Service only executes once", "RefUrl": "/notes/1276115"}, {"RefNumber": "1089880", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Removing editor lock for LSWNC_TABLE_MAINTF00", "RefUrl": "/notes/1089880"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3336872", "RefComponent": "PY-XX-BS", "RefTitle": "Clean-up: Sorting of include calls in payroll programs RPCALCX0 and RPCALCX0_CE", "RefUrl": "/notes/3336872 "}, {"RefNumber": "2106829", "RefComponent": "FS-PMA", "RefTitle": "CHNG: Legal adjustment of vehicle class and body type 2", "RefUrl": "/notes/2106829 "}, {"RefNumber": "1971626", "RefComponent": "PY-GB", "RefTitle": "PY-GB: P60 Corrections Collection", "RefUrl": "/notes/1971626 "}, {"RefNumber": "571543", "RefComponent": "FI-GL-GL-A", "RefTitle": "FI: Held documents are lost after the upgrade.", "RefUrl": "/notes/571543 "}, {"RefNumber": "939971", "RefComponent": "RE-FX", "RefTitle": "FAQ and consulting notes for RE-FX", "RefUrl": "/notes/939971 "}, {"RefNumber": "1826482", "RefComponent": "PSM-FA-ADB", "RefTitle": "FMADB_POST creates unnecessary lines", "RefUrl": "/notes/1826482 "}, {"RefNumber": "1601170", "RefComponent": "LO-GT-PM", "RefTitle": "Associations are not correct for partial return delivery", "RefUrl": "/notes/1601170 "}, {"RefNumber": "305118", "RefComponent": "BC-SRV-QUE", "RefTitle": "Eliminating various query problems in HR", "RefUrl": "/notes/305118 "}, {"RefNumber": "1089880", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Removing editor lock for LSWNC_TABLE_MAINTF00", "RefUrl": "/notes/1089880 "}, {"RefNumber": "1276115", "RefComponent": "CRM-IC-RDI", "RefTitle": "IDI Fact Gathering Service only executes once", "RefUrl": "/notes/1276115 "}, {"RefNumber": "607406", "RefComponent": "CO-PC-OBJ-MTO", "RefTitle": "Down payment clearing and commitment processing", "RefUrl": "/notes/607406 "}, {"RefNumber": "409985", "RefComponent": "LE-WM", "RefTitle": "New parameter for SAP enhancements MWMD0001 and MWMD0002", "RefUrl": "/notes/409985 "}, {"RefNumber": "945927", "RefComponent": "EHS-SAF-RCK", "RefTitle": "Tracking: Checking planned data/error messages", "RefUrl": "/notes/945927 "}, {"RefNumber": "726104", "RefComponent": "LO-GT-TEW", "RefTitle": "BAdI implementation cannot be assigned to \"*\"", "RefUrl": "/notes/726104 "}, {"RefNumber": "560605", "RefComponent": "MM-SRV", "RefTitle": "Commitment reduction for service requisitions", "RefUrl": "/notes/560605 "}, {"RefNumber": "153684", "RefComponent": "BC-SRV-QUE", "RefTitle": "Eliminating various query problems in HR", "RefUrl": "/notes/153684 "}, {"RefNumber": "504990", "RefComponent": "BC-SRV-QUE", "RefTitle": "Eliminating various query problems in the HR application", "RefUrl": "/notes/504990 "}, {"RefNumber": "543577", "RefComponent": "SD-BIL", "RefTitle": "Editor lock in Include LV60AB29", "RefUrl": "/notes/543577 "}, {"RefNumber": "339554", "RefComponent": "PP-SFC-PLN-CPT", "RefTitle": "OPL4: Profiles are stored incorrectly", "RefUrl": "/notes/339554 "}, {"RefNumber": "338393", "RefComponent": "LE-SHP-DL-BD", "RefTitle": "Message VL200 for the batch sales order stock", "RefUrl": "/notes/338393 "}, {"RefNumber": "149597", "RefComponent": "MM-IV", "RefTitle": "MRRL invoice value doubles during price determination for GR", "RefUrl": "/notes/149597 "}, {"RefNumber": "313503", "RefComponent": "BC-BW", "RefTitle": "Customer exit master data: Interface change", "RefUrl": "/notes/313503 "}, {"RefNumber": "369264", "RefComponent": "IS-HT-SW", "RefTitle": "RAISE_EXCEPTION FKREL_NOT_FOUND", "RefUrl": "/notes/369264 "}, {"RefNumber": "187275", "RefComponent": "IS-MP-PP", "RefTitle": "Mill OC:user exit cost distribution in combination", "RefUrl": "/notes/187275 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "640", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2417014", "RefTitle": "RTI: Serious Ill health Lump sum Payments", "RefUrl": "/notes/**********"}]}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}