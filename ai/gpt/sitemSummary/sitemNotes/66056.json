{"Request": {"Number": "66056", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 302, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000066056?language=E&token=EA768EAD571CEC24B2AE7AE45BF2FFA5"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000066056", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000066056/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "66056"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "00.00.0000"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CST-LL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Low Level Layers"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Client/Server Technology", "value": "BC-CST", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CST*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Low Level Layers", "value": "BC-CST-LL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CST-LL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "66056 - Authorization trace with Transaction ST01"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>An authorization trace is to be created with Transaction ST01.<br />The documentation on the CD-ROM is not detailed enough.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>ST01, RSTRAC21, RSTRAC24</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>- The documentation CD is inaccurate - not a program error -</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>This note describes Release 3.0D. On the basis of improvements, details may look a little different in your system.<br /><br />-&gt; Tools -&gt; Administration<br />&#x00A0;&#x00A0;-&gt; Monitor -&gt; Traces -&gt; System trace<br />This is Transaction ST01<br /><br />You can see whether someone else is currently using the trace system, and the last time the trace switches were used. There is exactly one trace system for each instance. (-&gt; Note 65054) If the trace is active, you should not interfere thoughtlessly.<br /></p> <b>Create an authorization trace the first time<br /></b><br /> <p>Press the button Switch, edit...<br />You reach the screen RSTRAC24/0700. On the left-hand side you can see an editor for the trace switch. On the right-hand side, the different options for working with records of switch settings are displayed.<br /><br />The editor is operated first:<br /><br />There are many different types of traces which are in each case switched via separate switches (and filters).<br />Some switches can be seen directly visible in the \"Trace types\" box. Switch all of these off; only the switch \"Authorization check\" should be activated. Other switches are grouped together. On this screen, it is possible to see whether they are switched off entirely, switched on entirely on or only partially active. In addition, there are pushbuttons to reach the respective details. The groups should all be deactivated.<br /><br />There are \"General filters\" in the box \"General management\". If you want to trace all users of your instance, you do not need any general filters. However, normally you press the corresponding pushbutton. Enter your user ID in the following dialog box. In the trace system, you can only choose between an individual user and all users. The other fields should remain empty. Then press 'Back'.<br /><br />In addition, you must set the \"Write options\". By pressing the corresponding button, another dialog box appears. Switch on \"Trace: Write to disk\" here.<br />You only need the \"Write unbuffered\" switch if you are tracing a transaction where you know that the work process will be restarted due to an error. Then press 'Back'.<br />You have generated an appropriate trace switch record.<br /><br />Assign a name to this trace switch record, for example, 'AuthCheck' and enter this filter name in the box \"Filter rec.s in the database\". Then insert this record in the database with<br />-&gt;Trace switch -&gt;Editor save in -&gt; in database (new)<br /><br />Everything is now prepared. Now write the contents of the editor to the current system. There is a pushbutton to do this within 'Status of active system'. Depending on your release, the button may be a Save, Activate or an S.<br />You can see how the status changes immediately.<br /><br />Now run the actual test. Either use another session or you must return to this editor screen afterwards.<br /><br />After the test, press 'Stop trace' and after a few seconds 'No trace'.<br /></p> <b>Create an authorization trace again.<br /></b><br /> <p>The next time, it is simpler:<br />Press the button Switch, edit...<br />You return to the screen with the editor and the different options with records of switch settings.<br /><br />In the 'Filter rec.s in the database', place the cursor on the field for the filter name. Then press the possible entries button which is then displayed. A dialog box is displayed in which you should find your switch record 'AuthCheck'. Select it. In the same box, there is a pushbutton for reading. Depending on your release this key looks like read or Get_another-object or [H]. You load the editor with it.<br /><br />Everything is now prepared. Now write the contents of the editor to the current system. There is a pushbutton to do this within 'Status of active system'. Depending on your release, the button may be Save, Activate or S.<br />You can see how the status changes immediately.<br /><br />Now run the actual test. Either use another session or you must return to this editor screen afterwards.<br /><br />After the test, press 'Stop trace' and after a few seconds 'No trace'.<br /></p> <b>Evaluating the written trace file<br /></b><br /> <p>With -&gt;Trace files -&gt; Standard options ...<br />a dialog box is displayed for the options of the trace evaluations. Deactivate everything except the authorization trace. The evaluation becomes all the more clearer. Then press 'Accept'.<br />Note: In Release 2.x or 3.x, you must come here at least once and press Accept.<br /><br />With -&gt;Trace files -&gt; Standard<br />a list of the available trace files is displayed. This is normally only one file. By pressing 'New list' or 'Refresh', the current status is displayed.<br />By double-clicking on 'Choose file', you can start the evaluation. (If the trace file is large, it may take a while.)<br /><br />Since trace lists can become very large, the output is very compressed. By double-clicking on most lines, detailed information can be displayed.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D039803)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D024241)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000066056/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000066056/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000066056/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000066056/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000066056/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000066056/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000066056/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000066056/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000066056/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "96801", "RefComponent": "TR-TM-TR-SE", "RefTitle": "Authorization checks: explanations", "RefUrl": "/notes/96801"}, {"RefNumber": "65054", "RefComponent": "BC-CST-LL", "RefTitle": "Trace function: multiple users", "RefUrl": "/notes/65054"}, {"RefNumber": "373347", "RefComponent": "BC-CI-WEBR", "RefTitle": "Authorizations for WEB Reporting", "RefUrl": "/notes/373347"}, {"RefNumber": "23927", "RefComponent": "BC-ABA-LA", "RefTitle": "SE30: Authorization objects (see SAP System trace)", "RefUrl": "/notes/23927"}, {"RefNumber": "1261193", "RefComponent": "BC-SEC-AUT", "RefTitle": "Composite SAP Note : HCM Authorizations Documentation", "RefUrl": "/notes/1261193"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1261193", "RefComponent": "BC-SEC-AUT", "RefTitle": "Composite SAP Note : HCM Authorizations Documentation", "RefUrl": "/notes/1261193 "}, {"RefNumber": "65054", "RefComponent": "BC-CST-LL", "RefTitle": "Trace function: multiple users", "RefUrl": "/notes/65054 "}, {"RefNumber": "564612", "RefComponent": "BC-MID-RFC", "RefTitle": "FAQ, Q+A, SM59, RFC destination, RFC error", "RefUrl": "/notes/564612 "}, {"RefNumber": "373347", "RefComponent": "BC-CI-WEBR", "RefTitle": "Authorizations for WEB Reporting", "RefUrl": "/notes/373347 "}, {"RefNumber": "96801", "RefComponent": "TR-TM-TR-SE", "RefTitle": "Authorization checks: explanations", "RefUrl": "/notes/96801 "}, {"RefNumber": "23927", "RefComponent": "BC-ABA-LA", "RefTitle": "SE30: Authorization objects (see SAP System trace)", "RefUrl": "/notes/23927 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "300", "To": "31I", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}