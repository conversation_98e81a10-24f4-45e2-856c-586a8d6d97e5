{"Request": {"Number": "2889120", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 304, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000205972020"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002889120?language=E&token=8091CDBCE7E5B92D3FAF5C112751DDC0"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002889120", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2889120"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.02.2020"}, "SAPComponentKey": {"_label": "Component", "value": "FS-BP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Partner"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Services", "value": "FS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Partner", "value": "FS-BP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS-BP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2889120 - BP_SRV: UDO report for the enhancement of the Business Partner Data replication service with Financial Services Datasets"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note provides the UDO report&#160;NOTE_2799001.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>MDG Service, Business Partner Replication Service, Financial Services Business Partner, 2799001,</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This report provides&#160;some preconditions for implementation of note 2799001. It&#160;will:</p>\r\n<p>Create package&#160;FS_BP_BSFND_MAPPING_MDG</p>\r\n<p>Create the error messages:</p>\r\n<p>MDG_BS_BP_DATAREPL 121 with long text<br />MDG_BS_BP_DATAREPL 122 with long text<br />MDG_BS_BP_DATAREPL 123<br />MDG_BS_BP_DATAREPL 124<br />MDG_BS_BP_DATAREPL 125</p>\r\n<p>Insert 13 entries into table&#160;MDGD_ELEMENT:</p>\r\n<p>DTEL MDG_BP_BPFSCREDIT_STANDING_LEG&#160;X&#160;X<br />TABL MDG_BP_BPFSCOMPANY_GROUP_ID&#160;X&#160;X<br />TABL MDG_BP_BPFSCUSTOMER_UNDESIRAB1&#160;X&#160;X<br />TABL MDG_BP_BPFSMARITAL_PROPERTY_R1&#160;X&#160;X<br />TABL MDG_BP_BPFSSALUTATION_CODE&#160;X&#160;X<br />TABL MDG_BP_BPFSTARGET_MARKET_GROU1&#160;X&#160;X<br />TABL MDG_BP_BPFS_KWGCREDIT_STANDIN1&#160;X&#160;X<br />TABL MDG_BP_BPFS_KWGLOAN_TO_MANAGE1&#160;X&#160;X<br />TABL MDG_BP_BPFS_OENBTARGET_GROUP_1&#160;X&#160;X<br />TABL&#160;MDG_BP_BUSINESS_PARTNER_TAX_C1 X&#160;X<br />TABL&#160;MDG_BP_OCCUPATION_STATUS_CODE1 X&#160;X<br />TABL&#160;MDG_BP_TAX_COMPLIANCE_REGULAT1 X&#160;X<br />TABL&#160;MDG_FND_COMPANY_ID X&#160;X<br /><br /></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Apply this note and run report&#160;NOTE_2799001 with transaction se38 or sa38.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D044196)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D033094)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002889120/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002889120/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002889120/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002889120/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002889120/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002889120/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002889120/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002889120/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002889120/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2890450", "RefComponent": "FS-BP", "RefTitle": "BP_SRV: Addition of data sets specific to Financial Services to the business partner data replication service (ratings, credit standing data, regulatory reporting data, and so on)", "RefUrl": "/notes/2890450"}, {"RefNumber": "2888623", "RefComponent": "FS-BP", "RefTitle": "BP_SRV: Enhancement of Business Partner Data Replication Service (formerly known as MDG Service) with Financial Services Datasets", "RefUrl": "/notes/2888623"}, {"RefNumber": "2858939", "RefComponent": "FS-BP", "RefTitle": "BP_SRV: Activation of nodes specific to Financial Service (for example, rating, regulatory reporting data) in the business partner data replication service", "RefUrl": "/notes/2858939"}, {"RefNumber": "2799001", "RefComponent": "FS-BP", "RefTitle": "BP_SRV: Addition of Financial Services data sets to business partner data replication service - technical prerequisites", "RefUrl": "/notes/2799001"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2890450", "RefComponent": "FS-BP", "RefTitle": "BP_SRV: Addition of data sets specific to Financial Services to the business partner data replication service (ratings, credit standing data, regulatory reporting data, and so on)", "RefUrl": "/notes/2890450 "}, {"RefNumber": "2799001", "RefComponent": "FS-BP", "RefTitle": "BP_SRV: Addition of Financial Services data sets to business partner data replication service - technical prerequisites", "RefUrl": "/notes/2799001 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}, {"SoftwareComponent": "S4FND", "From": "102", "To": "102", "Subsequent": ""}, {"SoftwareComponent": "S4FND", "From": "103", "To": "103", "Subsequent": ""}, {"SoftwareComponent": "S4FND", "From": "104", "To": "104", "Subsequent": ""}, {"SoftwareComponent": "SAP_BS_FND", "From": "748", "To": "748", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "S4CORE 100", "SupportPackage": "SAPK-10010INS4CORE", "URL": "/supportpackage/SAPK-10010INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 101", "SupportPackage": "SAPK-10108INS4CORE", "URL": "/supportpackage/SAPK-10108INS4CORE"}, {"SoftwareComponentVersion": "S4FND 102", "SupportPackage": "SAPK-10206INS4FND", "URL": "/supportpackage/SAPK-10206INS4FND"}, {"SoftwareComponentVersion": "S4FND 103", "SupportPackage": "SAPK-10304INS4FND", "URL": "/supportpackage/SAPK-10304INS4FND"}, {"SoftwareComponentVersion": "S4FND 104", "SupportPackage": "SAPK-10402INS4FND", "URL": "/supportpackage/SAPK-10402INS4FND"}, {"SoftwareComponentVersion": "SAP_BS_FND 748", "SupportPackage": "SAPK-74815INSAPBSFND", "URL": "/supportpackage/SAPK-74815INSAPBSFND"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BS_FND", "NumberOfCorrin": 1, "URL": "/corrins/0002889120/6134"}, {"SoftwareComponent": "S4FND", "NumberOfCorrin": 3, "URL": "/corrins/0002889120/22887"}, {"SoftwareComponent": "S4CORE", "NumberOfCorrin": 2, "URL": "/corrins/0002889120/19773"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 6, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}