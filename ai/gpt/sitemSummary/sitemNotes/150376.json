{"Request": {"Number": "150376", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 334, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000714572017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000150376?language=E&token=1586525E5B30DE546F1FD1F5A2F0FCD4"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000150376", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000150376/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "150376"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 46}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.03.2014"}, "SAPComponentKey": {"_label": "Component", "value": "MM-IS-IC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Inventory Controlling"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Information System", "value": "MM-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Inventory Controlling", "value": "MM-IS-IC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-IS-IC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "150376 - Collective note BCO Release 4.0b to 4.6b (October 2000)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>There is an error in the Inventory Controlling area.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>RMCBNEUA; RMCBNEUB; RMCBNERP; RMCB01F0; RMCB0100; RMCB0200; RMCB0300;<br/>MC.1; MC.2; MC.3; MC.4; MCBA; MC.5; MC.6; MC.7; MC.8; MCBC; MC.9; MC.A;<br/>MC.B; MC.C; MCBE; MC.D; MC.E; MC.F; MC.G; MCBG; MC.H; MCBK; MCBM; MCBV;<br/>MC40; MC41; MC42; MC43; MC44; MC45; MC46; MC47; MC58; MC49; MC50;<br/>standard analysis; plant analysis; storage location analysis; material analysis;<br/>controller analysis; material group analysis; division analysis; batch analysis<br/>ABC analysis; range of coverage, inventory turnover; usage value<br/>slow-moving items; requirement value; dead stock; S031; S032; S033;<br/>S034; S035; S035; S094</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Program and transport error</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>************************************************************************</strong><br/><strong>PAY ATTENTION TO THE INFORMATION CONCERNING THE RELEVANT SUPPORT PACKAGE LEVELS,</strong><br/><strong>THAT IS, THE SAPSERV TRANSPORT MUST NOT BE IMPORTED IN RELEASE</strong><br/><strong>&#x00A0;&#x00A0;4.0B / HP 55 and higher</strong><br/><strong>&#x00A0;&#x00A0;4.5B / HP 33 and higher</strong><br/><strong>&#x00A0;&#x00A0;4.6B / HP 19 and higher</strong><br/><strong>************************************************************************</strong><br/><br/>This SAP Note includes all notes known in Inventory Controlling up to the beginning of October 2000. It is a prerequisite for all the following SAP Notes for Inventory Controlling which relate to Releases 4.0B to 4.6B.<br/>It is not possible to list all of the source code changes in detail as there are too many of them.<br/>You must not implement this note if you are using IS-OIL See also SAP Note 50680.<br/><br/><strong>Before</strong> importing the transports from the sapserv computers, carry out the following change provided that it has not been done already and then release it. In addition, you should check whether all transports (that is, all transports containing the objects mentioned below) have been released. Otherwise, the transport cannot be imported correctly. Be careful with the client when importing as there are client-specific control table entries there.<br/>The transport should not be carried out when the system is running (see SAP Note 309335). If you have activated the collective update (V3), you should empty the queue, as you should whenever you import new Support Packages for safety&#39;s sake, by executing the relevant batch job.<br/><br/>First of all create two new data elements:<br/>Data element KBTCHX<br/>Short description: Extraction structure updated<br/>Development class: MCS<br/>Domain name : X<br/>No field label and no documentation<br/>Data element MCNEWBW<br/>Short description: Setup of extraction structures BW<br/>Development class: MCS<br/>Domain name : X<br/>Short text: ExtractStr<br/>Medium text: Extract.StructBW<br/>Long text: Extraction structures BW<br/>Heading: Setup of extraction structures BW<br/>Documentation: If you set this parameter, the extraction structures for the Business Information Warehouse are set up.<br/>Caution: The extraction structures operate without a version. Therefore, only set the statistical setup of the extraction structures if the InfoCubes in the Business Information Warehouse do not yet contain any data. Otherwise, multiple data posting will occur and the key figures will become too large.<br/><br/>Add field NEWBW with data element MCNEWBW to structure RMCSNEUA.<br/><br/>Create function module MCS_BW_ACTIVE:<br/>Function group: MCS0<br/>Short text: LIS-BW active<br/>Processing type: Normal, start immediately (standard setting)<br/>Development class: MCS<br/>Release 4.0b:<br/>Import parameter: MCAPP with reference field TMC4-MCAPP<br/>Export parameter: ACTIVE with reference type C<br/>As of Release 4.5B:<br/>&#x00A0;&#x00A0;Import parameter: MCAPP with reference type MCAPP<br/>  Export parameter: ACTIVE with reference type KBTCHX<br/><br/>Create function module OUTBOUND_CALL_01000020_P:<br/>Function group: MCB1BW<br/>(If this does not yet exist, create it:<br/>Short text: BTE: Enhance MCMSEG<br/>Development class: MCB)<br/>Short text: BTE: Enhance MCMSEG SAP BW<br/>Processing type: Normal, start immediately (standard setting)<br/>Development class: MCB<br/>Table parameters: XMCMSEG with reference structure MCMSEG<br/><br/>Create function module OUTBOUND_CALL_01000021_P:<br/>Function group: MCB1BW<br/>Short text: BTE: EP revaluation, SAP BW enhancement<br/>Processing type: Normal, start immediately (standard setting)<br/>Development class: MCB<br/>Table parameters: XMCMSEG with reference structure MCMSEG<br/><br/>Create function module ND_EXITS_ACTIVE_CHECK: See also SAP Note 201194.<br/>Function group: NDAC<br/>(If this does not yet exist, create it:<br/>Short text: ND Exits active<br/>Development class: NDBU)<br/>&#x00A0;&#x00A0;(If this does not yet exist, create it:<br/>&#x00A0;&#x00A0;Short description: New Dimension plug-in: BTEs - independent from release<br/>&#x00A0;&#x00A0;Transport layer: Release 4.0B: SAPP&#x00A0;&#x00A0;4.5B: SP4D&#x00A0;&#x00A0;4.6B: SP9A (or own layers)<br/>&#x00A0;&#x00A0; Link to Workbench Organizer indicator: Selected<br/>&#x00A0;&#x00A0; Customer Delivery indicator: Selected<br/>&#x00A0;&#x00A0; Software component: SAP_APPL<br/>&#x00A0;&#x00A0; Application Component: LO<br/>&#x00A0;&#x00A0; Note the following: Depending on the release, not all of these fields exist.)<br/>Short text: Check, if ND Exits in Standard Core Coding shall be performed<br/>Processing type: Normal, start immediately (standard setting)<br/>Development class: NDBU<br/>Exception: EXITS_NOT_ACTIVE<br/>Program line: raise exits_not_active.<br/><br/>The source code is not required for the function modules except for ND_EXITS_ACTIVE_CHECK. Activate the function module.<br/>If there are difficulties creating the function modules (message number FL 707: Function module name is reserved for SAP), copy an existing function module, for example MCB_STATISTICS_LIS, and modify it. The result must then look exactly as described above. For example, you should not forget to delete unnecessary transfer tables and all the copied source code.<br/><br/>Then, change programs MM07MSTA and MM07MTOP as described below<br/>The purpose of the correction is to move the declaration of tables MSKU, MSLB, and MARC from program MM07MSTA to MM07MTOP. This might have partly taken place already, depending on the Support Package level. In this case only correct the missing table declarations.<br/><br/>For each release, the transports subdivide into two areas:<br/>a) update of the statistical data and of the document evaluations and<br/>b) standard analyses.<br/>The transports were copied from the Support Package systems and put onto the sapserv computers into directory &quot;/general/R3server/abap/note.0150376&quot;. A better overview is provided by the two sub-directories in this directory. The latest complete report is in the directory &quot;../release4xx/version_10.00&quot;. Pay attention to the required Hot Packages or SAP Notes.<br/><br/>Area Release relevant for&#x00A0;&#x00A0;&#x00A0;&#x00A0; Transport&#x00A0;&#x00A0;&#x00A0;&#x00A0; Prerequisite<br/>a)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4.0B&#x00A0;&#x00A0;&#x00A0;&#x00A0;HP&lt;55&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;U4BK901265&#x00A0;&#x00A0;&#x00A0;&#x00A0;HP29 or SAP Note 167759<br/>a)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4.5B&#x00A0;&#x00A0;&#x00A0;&#x00A0;HP&lt;33&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;U4DK900643&#x00A0;&#x00A0;&#x00A0;&#x00A0;HP10 or SAP Note 167759<br/>a)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4.6B&#x00A0;&#x00A0;&#x00A0;&#x00A0;HP&lt;19&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;U9BK901041&#x00A0;&#x00A0;&#x00A0;&#x00A0;No  prerequisite<br/><br/>b)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4.0B&#x00A0;&#x00A0;&#x00A0;&#x00A0;HP&lt;55&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;U4BK901263&#x00A0;&#x00A0;&#x00A0;&#x00A0;HP21 or SAP Note 119466<br/>b)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4.5B&#x00A0;&#x00A0;&#x00A0;&#x00A0;HP&lt;33&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;U4DK900641&#x00A0;&#x00A0;&#x00A0;&#x00A0;HP04 or SAP Note 119466<br/>b)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4.6B&#x00A0;&#x00A0;&#x00A0;&#x00A0;HP&lt;19&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;U9BK901039&#x00A0;&#x00A0;&#x00A0;&#x00A0;No prerequisite<br/><br/>NOTE THE FOLLOWING: YOU SHOULD NOT IMPLEMENT THIS NOTE IN SUPPORT PACKAGE LEVELS THAT ARE HIGHER THAT THOSE SPEFICIED UNDER &quot;RELEVANT FOR&quot; AS OTHERWISE MORE CURRENT CORRECTIONS WILL BE OVERWRITTEN.<br/><br/>Remark on SAP Note 167759:<br/>The SAP Note shows three different correction instructions depending on which of the required notes you have already implemented or not. Therefore, only implement one of these three instructions.<br/><br/>All standard analyses in Inventory Controlling are checked by the transport of the standard analyses, including the self-defined ones. If, during this, there is the problem &quot;Data object &quot;PLAN_S000&quot; does not contain component with name MARKIERUNG&quot; it is likely that SAP Note 119466 was not fully implemented. In this case, generate the standard analyses displayed in the transport log again using program RMCSISGN.<br/><br/>After importing the transport, execute program RMCBXP01. It corrects any incorrect entries in control table TMC2F (cross-client).<br/>After the import, you can use program RMCBT156 to check whether the Statistically Relevant indicator is set for all the required movement types.<br/>If necessary, you should now carry out another statistical data setup. More detailed information on this can be found in SAP Note 79083.<br/><br/>If you use the Business Information Warehouse and you implemented the database-dependent Note 209053, you must implement the changes for programs RMCSS195 and RMCSS196 again.<br/><br/>-----------------------------------------------------------------------<br/><br/>For information:<br/>The transport for the update of the statistical data contains:<br/>Function groups: MCB1, MCB3, MCBB, MCBR, MCGR<br/>Programs: BLH30E02, MM07MSTA, RMCBDISP, RMCBLOGG, RMCBMMAT, RMCBMRP2, RMCBMSEG, RMCBNEAD, RMCBNEAV, RMCBNERP, RMCBNEUA, RMCBNEUB, RMCBS039, RMCBS197, RMCBT156, RMCBXP01, RMCSNF00, RMCSNTOP, RMCSS031, RMCSS032, RMCSS033, RMCSS034, RMCSS035, RMCSS195, RMCSS196, RMCSSU01, RMCYMUBT, RMCYMUFB, RMCYMUFC, RMCYMUTO<br/>Structures: S194BIWS, MCMSEGADD2<br/>Table entries from: TMC1, TMC1D, TMC1K, TMC1T, TMC2K, TMC4T, TMC4U, TMC4UT, TMC73<br/>The authorization profile of transactions MC40, MC41, MC42, MC43, MC44, MC45, MC46, MC47, MC48, MC49, MC50<br/>The transport for the standard analyses contains:<br/>Programs: RMCB01TP, RMCB02TP, RMCB03TP, RMCB01F0, RMCB02F0, RMCB03F0, RMCB0TOP<br/><br/>If you have already implemented the composite note from August, you only need to import lower transports. These are located in the directory &quot;../delta_08.00_10.00&quot;<br/><br/>Area Release relevant for&#x00A0;&#x00A0;&#x00A0;&#x00A0; Transport&#x00A0;&#x00A0;&#x00A0;&#x00A0; Prerequisite<br/>a)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4.0B&#x00A0;&#x00A0;&#x00A0;&#x00A0;HP&lt;55&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;U4BK901266&#x00A0;&#x00A0;&#x00A0;&#x00A0;No prerequisite<br/>a)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4.5B&#x00A0;&#x00A0;&#x00A0;&#x00A0;HP&lt;33&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;U4DK900644&#x00A0;&#x00A0;&#x00A0;&#x00A0;No prerequisite<br/>a)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4.6B&#x00A0;&#x00A0;&#x00A0;&#x00A0;HP&lt;19&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;U9BK901042&#x00A0;&#x00A0;&#x00A0;&#x00A0;No prerequisite<br/><br/>b)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4.0B&#x00A0;&#x00A0;&#x00A0;&#x00A0;HP&lt;55&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;U4BK901264&#x00A0;&#x00A0;&#x00A0;&#x00A0;HP21 or SAP Note 119466<br/>b)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4.5B&#x00A0;&#x00A0;&#x00A0;&#x00A0;HP&lt;33&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;U4DK900642&#x00A0;&#x00A0;&#x00A0;&#x00A0;HP04 or SAP Note 119466<br/>b)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4.6B&#x00A0;&#x00A0;&#x00A0;&#x00A0;HP&lt;19&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;U9BK901040&#x00A0;&#x00A0;&#x00A0;&#x00A0;No prerequisite<br/><br/>The following SAP Notes are contained in the delta transport:</p>\r\n<p>315353&#x00A0; Standard analysis with &quot;Material Ledger active&quot;<br/>323200&#x00A0;&#x00A0;Info structure type &#39;D&#39; - plan versions<br/>323638&#x00A0;&#x00A0;Incorrect valuation class / material class in S032<br/>325628&#x00A0;&#x00A0;Performance of goods movements<br/>326947&#x00A0;&#x00A0;COMPUTE_BCD_OVERFLOW in standard analysis<br/>327002&#x00A0;&#x00A0;Detaild dsplay docmnt evaluatn:Receipt/Issue incor.<br/>327239&#x00A0;&#x00A0;Unnecessary update in S031, S032 and S033<br/>327860&#x00A0;&#x00A0;Pstng to prior period in prev. period - incor. date<br/>327941&#x00A0;&#x00A0;Authorization profile for document evaluations<br/>329635&#x00A0;&#x00A0;Stck trnsf. via stck in trnsit:stat.setup incorrect<br/>329985&#x00A0;&#x00A0;New setup for S035<br/>331334&#x00A0;&#x00A0;Additional key figure range of coverage is 999<br/>332042&#x00A0;&#x00A0;Add.key fig.rel.to year can always be divded by 3,6<br/>335244&#x00A0;&#x00A0;Incorrect version when updating IDocs<br/>335616&#x00A0;&#x00A0;S032: Double stocks (Retail)<br/>335684&#x00A0;&#x00A0;Info structure type &#39;D&#39; - currency translation<br/>336043&#x00A0;&#x00A0;Incorrect update in valuations<br/>338156&#x00A0;&#x00A0;Extraction structures displayed BW in RMCBMSEG</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BCT-MM-IM (BW only - Inventory Management)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D026033)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D031627)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000150376/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000150376/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000150376/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000150376/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000150376/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000150376/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000150376/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000150376/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000150376/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "79083", "RefComponent": "MM-IS-IC", "RefTitle": "Setup of statistical data in BCO: Additional information", "RefUrl": "/notes/79083"}, {"RefNumber": "50680", "RefComponent": "IS-OIL", "RefTitle": "IS-Oil and SAPNet fixes - procedure and policy", "RefUrl": "/notes/50680"}, {"RefNumber": "379058", "RefComponent": "LO-LIS-REP", "RefTitle": "Instructions for changing sample coding reports", "RefUrl": "/notes/379058"}, {"RefNumber": "354925", "RefComponent": "IS-R-IS", "RefTitle": "RIS: composite SAP Note concerning the performance of RIS", "RefUrl": "/notes/354925"}, {"RefNumber": "326504", "RefComponent": "MM-IS-IC", "RefTitle": "Error when importing R/3 Support Package", "RefUrl": "/notes/326504"}, {"RefNumber": "321286", "RefComponent": "MM-IS-IC", "RefTitle": "Collective note: Invent. Controlling Rel. 4.6C (April 2001)", "RefUrl": "/notes/321286"}, {"RefNumber": "318821", "RefComponent": "CO-PC-ACT", "RefTitle": "Material ledger settlement does not supply LIS", "RefUrl": "/notes/318821"}, {"RefNumber": "309335", "RefComponent": "LO-LIS", "RefTitle": "Update terminations in LIS", "RefUrl": "/notes/309335"}, {"RefNumber": "201194", "RefComponent": "XX-PI", "RefTitle": "Program termination:missing funct.mod. ND_EXITS_ACTIVE_CHECK", "RefUrl": "/notes/201194"}, {"RefNumber": "200182", "RefComponent": "IS-R-IS-RIS", "RefTitle": "Incorrect call of report for resetup", "RefUrl": "/notes/200182"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "123972", "RefComponent": "BC-UPG-ADDON", "RefTitle": "@8R@ Not or only partly compatible with IS-Oil", "RefUrl": "/notes/123972"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "123972", "RefComponent": "BC-UPG-ADDON", "RefTitle": "@8R@ Not or only partly compatible with IS-Oil", "RefUrl": "/notes/123972 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "309335", "RefComponent": "LO-LIS", "RefTitle": "Update terminations in LIS", "RefUrl": "/notes/309335 "}, {"RefNumber": "50680", "RefComponent": "IS-OIL", "RefTitle": "IS-Oil and SAPNet fixes - procedure and policy", "RefUrl": "/notes/50680 "}, {"RefNumber": "79083", "RefComponent": "MM-IS-IC", "RefTitle": "Setup of statistical data in BCO: Additional information", "RefUrl": "/notes/79083 "}, {"RefNumber": "201194", "RefComponent": "XX-PI", "RefTitle": "Program termination:missing funct.mod. ND_EXITS_ACTIVE_CHECK", "RefUrl": "/notes/201194 "}, {"RefNumber": "379058", "RefComponent": "LO-LIS-REP", "RefTitle": "Instructions for changing sample coding reports", "RefUrl": "/notes/379058 "}, {"RefNumber": "321286", "RefComponent": "MM-IS-IC", "RefTitle": "Collective note: Invent. Controlling Rel. 4.6C (April 2001)", "RefUrl": "/notes/321286 "}, {"RefNumber": "354925", "RefComponent": "IS-R-IS", "RefTitle": "RIS: composite SAP Note concerning the performance of RIS", "RefUrl": "/notes/354925 "}, {"RefNumber": "326504", "RefComponent": "MM-IS-IC", "RefTitle": "Error when importing R/3 Support Package", "RefUrl": "/notes/326504 "}, {"RefNumber": "318821", "RefComponent": "CO-PC-ACT", "RefTitle": "Material ledger settlement does not supply LIS", "RefUrl": "/notes/318821 "}, {"RefNumber": "200182", "RefComponent": "IS-R-IS-RIS", "RefTitle": "Incorrect call of report for resetup", "RefUrl": "/notes/200182 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "40B", "To": "40B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B38", "URL": "/supportpackage/SAPKH40B38"}, {"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B55", "URL": "/supportpackage/SAPKH40B55"}, {"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B50", "URL": "/supportpackage/SAPKH40B50"}, {"SoftwareComponentVersion": "SAP_HR 40B", "SupportPackage": "SAPKE40B38", "URL": "/supportpackage/SAPKE40B38"}, {"SoftwareComponentVersion": "SAP_HR 40B", "SupportPackage": "SAPKE40B50", "URL": "/supportpackage/SAPKE40B50"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B17", "URL": "/supportpackage/SAPKH45B17"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B28", "URL": "/supportpackage/SAPKH45B28"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B33", "URL": "/supportpackage/SAPKH45B33"}, {"SoftwareComponentVersion": "SAP_APPL 46A", "SupportPackage": "SAPKH46A10", "URL": "/supportpackage/SAPKH46A10"}, {"SoftwareComponentVersion": "SAP_APPL 46A", "SupportPackage": "SAPKH46A22", "URL": "/supportpackage/SAPKH46A22"}, {"SoftwareComponentVersion": "SAP_APPL 46B", "SupportPackage": "SAPKH46B01", "URL": "/supportpackage/SAPKH46B01"}, {"SoftwareComponentVersion": "SAP_APPL 46B", "SupportPackage": "SAPKH46B02", "URL": "/supportpackage/SAPKH46B02"}, {"SoftwareComponentVersion": "SAP_APPL 46B", "SupportPackage": "SAPKH46B15", "URL": "/supportpackage/SAPKH46B15"}, {"SoftwareComponentVersion": "SAP_APPL 46B", "SupportPackage": "SAPKH46B19", "URL": "/supportpackage/SAPKH46B19"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 1, "URL": "/corrins/0000150376/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}