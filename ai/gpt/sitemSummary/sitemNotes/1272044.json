{"Request": {"Number": "1272044", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 261, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016668632017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=8771ACF3E61A5FC7FE4305A62ECAB4C1"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1272044"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.11.2011"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-OT-MDX"}, "SAPComponentKeyText": {"_label": "Component", "value": "MDX,OLAP-BA<PERSON>,OLE DB for OLAP"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Explorer", "value": "BW-BEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "OLAP Technology", "value": "BW-BEX-OT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "MDX,OLAP-BA<PERSON>,OLE DB for OLAP", "value": "BW-BEX-OT-MDX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT-MDX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1272044 - Analyzing MDX problems"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>When using a frontend tool, that accesses SAP BW via the MDX interface, unexpected problems such as wrong data occur. It is not clear which transactions and traces can be used to analyze the interaction.<br />SAP Support requests MDX statements or traces.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>BRAINOLAPI100, unexpected data ,wrong data, MDX PARSER, WIS10901<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Processing of MDX messages could be faster when the analysis steps mentioned below have been performed and documented in message.<br />Raising an SAP message and waiting for SAP support reply can be avoided with solutions in this note.<br /><br />This guideline should help in identifying problem areas and recording helpful information for creating a message in component BW-BEX-OT-MDX by providing information in the following areas:<br /><br />A. Overview<br />B. Common Problem Areas<br />C. Extracting MDX statements<br />D. Transaction MDXTEST<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><B>A. Overview</B><br /><br /><B>A1) Partner applications and responsibilities (who is calling MDX interface)</B><br />Several applications (Analysis Process Designer, RSCRM), Business Objects tools (WebIntelligence, Crystal reports) and 3rd Party Frontend tool providers access SAP BW data via the MDX interface. This note will refer to all of them as partner applications. If the following guideline refers to the component of one of these partner applications, some samples are mentioned below.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For 3rd Party Providers (Cognos, Arcplan, Microstrategy, ... ), follow note 187646 and discuss the problem with the 3rd Party support first.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For SAP tools, also refer to the following components as being responsible for calling the MDX interface:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; APD ~ BW-EI-APD<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; RSCRM ~ BW-EI-RTR<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAP Business Objects WebiIntelligence ~ BI-RA-WBI<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Crystal Reports ~ BI-RA-CR<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Visual Composer ~ EP-VC, BW-BEX-UDI-VC<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; BPC ~ EPM-BPC<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; SEM ~ FIN-SEM<br /><br /><B>A2) Documentation</B><br />Even if you do not see the access to MDX interface, this overview may help you to understand the communication and proceeding a bit better.<br />Online-Help:<br />http://help.sap.com/saphelp_nw70/helpdata/EN/d9/ed8c3c59021315e10000000a114084/content.htm<br /><br /><B>B. Common problem areas</B><br /><br /><B>B1) The MDX parser is throwing errors.</B><br /><br />Note 1032461 describes the MDX parser and troubleshooting with the MDX Parser.<br /><br /><B>B2) The MDX interface returns unexpected data or no data.</B><br /><br /><B>* Restrictions and limitations </B></p> <UL><UL><LI>Check note 820925 for BEx query features that are not supported using MDX. In this case remove these features from the query and compare again.</LI></UL></UL> <p><br /><B>* Transaction MDXTEST displays the expected data.</B></p> <UL><UL><LI>BW offers a test transaction for checking the MDX statements: MDXTEST (see point D for an introduction).</LI></UL></UL> <UL><UL><LI>The partner application offers the possibility to display or retrieve the MDX statement (contact the respective partner application component for details).</LI></UL></UL> <UL><UL><LI>Should it not be possible to retrieve this statement conveniently on partner application side, it is also possible to record an RSTT or RSRTRACE trace and extract the statement(see point C for details).</LI></UL></UL> <UL><UL><LI>This MDXTEST result should be compared to the result in the partner application.</LI></UL></UL> <UL><UL><LI><B>Should the result be ok in MDXTEST, contact the component of the partner application or proceed as described in note 187646 (for Non-SAP delivered applications).</B></LI></UL></UL> <p><br /><B>* MDX statement returns the unexpected result in transaction MDXTEST</B><br /><br />The result can be compared with the result of the Bex Provider (Query or InfoProvider) in transaction RSRT (BEx Query monitor).</p> <UL><UL><LI>If your MDX statement is based on a query, paste the technical name of the query in this format: &lt;INFOPROVIDER&gt;/&lt;QUERY&gt;.</LI></UL></UL> <UL><UL><LI>If your MDX statement is based on an InfoProvider use following syntax: &lt;INFOPROVIDER&gt;/&lt;!INFOPROVIDER&gt;.</LI></UL></UL> <UL><UL><LI>Chose HTML for the output format and execute the query.</LI></UL></UL> <UL><UL><LI>Adjust the navigation state according to the definition of your MDX statement and compare the results (this requires some basic knowledge of MDX).</LI></UL></UL> <UL><UL><LI>use the same characteristics in rows and columns</LI></UL></UL> <UL><UL><LI>use the same filters and hierarchy settings</LI></UL></UL> <p><br />If a similar navigation state of the query in RSRT returns a different result than the respective MDX statement, then save a bookmark of the created navigation state by pressing the bookmark button in transaction RSRT.<br /><br />When contacting SAP-support always provide:</p> <UL><UL><LI>the MDX statement</LI></UL></UL> <UL><UL><LI>a query bookmark or</LI></UL></UL> <UL><UL><LI>a detailed description how you compare your query navigation state with the results of the MDX statement</LI></UL></UL> <p><br /><B>B3) The MDX interface behaves differently for different users.</B><br /><br />You get different data/messages for users with and without SAP_ALL profile. This indicates an issue with authorizations. See note 1412800 for hints and traces.<br /><br /><B>B4) You are experiencing performance or memory problems.</B><br /><br />You experience performance differences between a BEx query and an MDX query. For comparison create a bookmark as described in the wrong data section B2. Note 1381821 contains some more hints.<br /><br /><B>C) Extracting MDX statements</B><br /><br />The MDX statement cannot be retrieved from partner application. It could be extracted via RSTT in BW release 7.x (note 899572) or RSRTRACE (note 112458) in releases 3.x<br /><br /><B>C1) Steps to record a RSTT/RSRTRACE trace:</B></p> <UL><UL><LI>Go to transaction RSTT/RSRTRACE and activate the user that is connecting to BW</LI></UL></UL> <UL><UL><LI>Make sure the user is of type DIALOG (in SU01) and has authorization for S_RS_RSTT in his profile.</LI></UL></UL> <UL><UL><LI>Now perform the activity that involves retrieving data from BW.</LI></UL></UL> <UL><UL><LI>Afterwards deactivate the user again in transaction RSTT/RSRTRACE.</LI></UL></UL> <UL><UL><LI>If the transaction RSTT does not record a trace, check in transaction RSRTRACE (some older implementations cannot be traced via RSTT).</LI></UL></UL> <UL><UL><LI>Note the number of the trace recorded for the step C2 and/or mention it in the message send to SAP.</LI></UL></UL> <p><br /><B>C2) Options to extract the MDX statement from the trace:</B><br /><br /><B>These options are valid for releases &lt; 7.3:</B><br /><br />Go to transaction SE38 and run the report:</p> <UL><LI>RSR_MDX_RSTT_TRACE_EXTRACT (As of release 7.x and note 1406664 the report is available to extract MDX statements from RSTT traces)</LI></UL> <UL><LI>RSR_MDX_TRACE_EXTRACT (For extracting statements from the RSRTRACE in releases 3.x)</LI></UL> <p><br />You need to provide the Trace ID / Log Number (in RSTT eg. Y1W/008080, in RSRTRACE eg. 3).<br /><br />Execute the report and the MDX statement will be displayed in list view.<br /><br />To save the MDX in the database for later reuse in transaction MDXTEST (see point D for details), select the checkbox and enter a number in the field 'Offset' or 'Start Command ID' (e.g. Default Offset is 9000, but can be changed).<br /><br />As of release &gt;=7.3 you have the option to import the trace statements within transaction MDXTEST itself. In the menu, choose&#x00A0;&#x00A0;MDX&#x00A0;&#x00A0;Import from RS Trace Tool . A dialog box appears where you can enter the trace ID.<br /><br /><br /><B>C3) Option to extract with a little ABAP knowledge:</B></p> <UL><LI>Set a breakpoint in method SET_COMMAND_TEXT of class CL_RSR_MDX_COMMAND. The statement is contained in the parameter I_T_MDX.</LI></UL> <UL><LI>Play the idenitified trace, when you get to the set breakpoint, check the structure I_T_MDX and retrieve the statement.</LI></UL> <UL><LI>Paste this statement into transaction MDXTEST, check the spaces and execute.</LI></UL> <p><br /><B>D) Transaction MDXTEST</B><br /><br />Transaction MDXTEST is a test transaction to quickly check the syntax and results of MDX problems. There is a redesign in the next release to make it more user friendly. The following description applies to the most important functions in releases &lt; 7.30.<br /><br />(As of release 7.3 MDXTEST has a new layout. Please, see the online help for documentation of this new transaction:<br />http://help.sap.com/saphelp_nw73/helpdata/en/19/fdd486b13c43e2ad9f562a3222a480/frameset.htm<br />The old layout can still be used via MDXTEST_OLD).<br /><br /><B>D1) Using MDX statements</B><br /><br />a) Paste MDX statement from clipboard:</p> <UL><LI>If an MDX statement is already available you can paste it in the large text editor window on the right hand. Press the check button in the upper left corner(Ctrl+F2) for syntax check. It is important to have correct spacings (especially for the usage of BEx presentation hierarchies) to avoid syntax errors.</LI></UL> <p><br />b) Use saved MDX statement:</p> <UL><LI>If you have executed one of the reports of mentioned in point C2 and you have saved the MDX statement with a Offset/Start Command ID, go to the upper right corner and the very small text field. Write the number and press the 'Read' button with the glasses right beside it.</LI></UL><p><br />c) Create your own MDX statement:</p> <UL><LI>In the upper left corner, you can select a query or infoprovider that has been released for external access in the query properties. CATALOG in MDX corresponds to the BW infoprovider. CUBE corresponds to the BW Bex query. When you select a CATALOG/CUBE the available meta data for this object will be displayed and can be dragged to the right hand side.</LI></UL> <p><br /><B>D2) MDX execution modes</B><br /><br />Different execution modes are supported for simulation. Sometimes SAP support or the partner application support asks you to test the MDX in a special mode. This can be triggered via the different small buttons on the right hand side:</p> <UL><LI>Run query Multidim. (blue field with black triangle): To simulate the standard multidimensional processing of MDX statements</LI></UL> <UL><LI>Run Query with Flattening (table symbol): To simulate a flattened result (mostly used older Crystal report versions)</LI></UL> <UL><LI>Run query as datastream (grid or fence symbol): similar to flattening</LI></UL> <p><br />As of Enhancement Package 1 for BW release 7 you can find an additional execution mode</p> <UL><LI>XML document (XML symbol): To simulate the bXML flattening mode (mostly used by WebIntelligence)</LI></UL> <p><br /><B>D3) Analysis options</B><br /><br /><B>You want to analyze the MDX statement in 'Execute &amp; Debug':</B><br />In MDXTEXT, go to Menu and select: MDX Command --&gt; 'Set Debug flags'.<br />Select different options for analysis e.g.:<br />&#x00A0;&#x00A0;Others --&gt; Display Statistics<br />&#x00A0;&#x00A0;Aggs&#x00A0;&#x00A0; --&gt; Display Aggregate Found<br />&#x00A0;&#x00A0;...<br />Most of these options are intended for SAP support, but you might find some of them helpful as well.<br /><br /><B>You want to simulate MDX execution for users with different analysis authorizations: </B><br />Go to transaction RSUDO and start the transaction MDXTEST for the user.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BEX-ET-ODB (OLE DB Provider)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I026448)"}, {"Key": "Processor                                                                                           ", "Value": "I042259"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "899572", "RefComponent": "BW-BEX-OT", "RefTitle": "Trace tool: Analyzing BEx, OLAP and planning processes", "RefUrl": "/notes/899572"}, {"RefNumber": "838800", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Composite SAP Note for consulting notes about MDX/OLAP BAPIs", "RefUrl": "/notes/838800"}, {"RefNumber": "820925", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX - Restrictions", "RefUrl": "/notes/820925"}, {"RefNumber": "1449558", "RefComponent": "EPM-BPC-NW", "RefTitle": "How to get mdx statement for BPC NW", "RefUrl": "/notes/1449558"}, {"RefNumber": "1412800", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX and BW authorizations", "RefUrl": "/notes/1412800"}, {"RefNumber": "1406664", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Extracting MDX commands from RSTT traces", "RefUrl": "/notes/1406664"}, {"RefNumber": "1381821", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Memory and Performance Analysis for MDX", "RefUrl": "/notes/1381821"}, {"RefNumber": "112458", "RefComponent": "BW-BEX", "RefTitle": "Trace log in the Business Explorer", "RefUrl": "/notes/112458"}, {"RefNumber": "1032461", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX parser does not start", "RefUrl": "/notes/1032461"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1925924", "RefComponent": "BI-RA-WBI", "RefTitle": "How to enable RSTT Trace in SAP BW for Frontend Tool(SAP BI & 3rd-party Tools) [video]", "RefUrl": "/notes/1925924 "}, {"RefNumber": "1938885", "RefComponent": "BI-RA-WBI-DSL", "RefTitle": "BICS vs MDX BAPI vs DF Relational(JCo) Connection on BI 4.x", "RefUrl": "/notes/1938885 "}, {"RefNumber": "838800", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Composite SAP Note for consulting notes about MDX/OLAP BAPIs", "RefUrl": "/notes/838800 "}, {"RefNumber": "1449558", "RefComponent": "EPM-BPC-NW", "RefTitle": "How to get mdx statement for BPC NW", "RefUrl": "/notes/1449558 "}, {"RefNumber": "820925", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX - Restrictions", "RefUrl": "/notes/820925 "}, {"RefNumber": "1032461", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX parser does not start", "RefUrl": "/notes/1032461 "}, {"RefNumber": "1381821", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Memory and Performance Analysis for MDX", "RefUrl": "/notes/1381821 "}, {"RefNumber": "1406664", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Extracting MDX commands from RSTT traces", "RefUrl": "/notes/1406664 "}, {"RefNumber": "1412800", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX and BW authorizations", "RefUrl": "/notes/1412800 "}, {"RefNumber": "899572", "RefComponent": "BW-BEX-OT", "RefTitle": "Trace tool: Analyzing BEx, OLAP and planning processes", "RefUrl": "/notes/899572 "}, {"RefNumber": "112458", "RefComponent": "BW-BEX", "RefTitle": "Trace log in the Business Explorer", "RefUrl": "/notes/112458 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}