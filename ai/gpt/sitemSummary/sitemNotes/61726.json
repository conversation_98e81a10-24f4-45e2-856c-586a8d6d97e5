{"Request": {"Number": "61726", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 547, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000148812017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000061726?language=E&token=B0C5EF77C883800C7E0961CDE6B2DC30"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000061726", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000061726/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "61726"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 12}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.09.2008"}, "SAPComponentKey": {"_label": "Component", "value": "BC-SEC-USR"}, "SAPComponentKeyText": {"_label": "Component", "value": "User Administration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Security - Read KBA 2985997 for subcomponents", "value": "BC-SEC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "User Administration", "value": "BC-SEC-USR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC-USR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "61726 - RSSM: No profile assignment if Central User Administration"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>In a Business Warehouse (BW) system, you use transaction RSSM or the report RSSB_GENERATE_AUTHORIZATIONS to automatically create authorization profiles from ODS (Operational Data Store) objects and assign these profiles to users ( Note: These are manual profiles that are maintained using transaction SU02). User assignment fails if the BW system is part of a Central User Administration. In this case, the profile is created without user assignment.<br />If the BW system is not integrated in a Central User Administration, this error does not occur.<br /><br />Important:<br />If you use the new analysis authorizations that are available as of BW Release 7.00, this note is not relevant for you.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RSSB_AUTH_GEN_FROM_INFOPROV<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Up to the correction contained in Note 841612, the profile assignment was always executed, independent of whether the BW system is part of a Central User Administration (CUA) or not. If it was, an inconsistency occurred that had to be corrected by retransferring the profile assignments in transaction SCUG.<br />After you implement the corrections mentioned above, the system uses the regular interface BAPI_USER_PROFILES_ASSIGN for the profile assignment. However, if the BW system is part of a Central User Administration, this module does nor work and no profile assignment is created.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Use transaction SNOTE to implement the correction instructions or import the relevant Support Package.<br /></p> <b>General information</b><br /> <p>The corrections contained in this note restore the previous system response:<br />The system executes the profile assignment only locally in the BW system, but does not automatically transfer it to the central system. You must then carry out the transfer manually using transaction SCUG.<br /><br />To be able to implement an automatic transfer, a correction in the user management area is not sufficient. An additional extensive adjustment of the BW function would be required.<br />We have decided not to make this extensive adjustment for the following reasons:</p> <OL>1. This would result in a dependency between corrections of different software components. After implementing the change using the Support Package for SAP_BASIS, you would have to import the relevant Support Package for SAP_BW to avoid runtime errors.<br />We cannot inform all BW customers about the dependency in time before they import Support Packages.</OL> <OL>2. The analysis authorizations that were introduced with BW Release 7.00 are independent of the classic user management and the classic authorization management, that is, they are also independent of the CUA. We consider the use of the new concept to be binding (see Note 1125108) so that the problem described in this note will become less important.<br /></OL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BEX-OT-OLAP-AUT (Authorizations)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D036362)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D033600)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000061726/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000061726/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000061726/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000061726/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000061726/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000061726/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000061726/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000061726/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000061726/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1125108", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Use of obsolete 3.x authorizations in BW 7.x", "RefUrl": "/notes/1125108"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1125108", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Use of obsolete 3.x authorizations in BW 7.x", "RefUrl": "/notes/1125108 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "620", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "710", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C58", "URL": "/supportpackage/SAPKB46C58"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62065", "URL": "/supportpackage/SAPKB62065"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64023", "URL": "/supportpackage/SAPKB64023"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70016", "URL": "/supportpackage/SAPKB70016"}, {"SoftwareComponentVersion": "SAP_BASIS 710", "SupportPackage": "SAPKB71007", "URL": "/supportpackage/SAPKB71007"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 5, "URL": "/corrins/0000061726/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 5, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "ValidFrom": "46C", "ValidTo": "46C", "Number": "841612 ", "URL": "/notes/841612 ", "Title": "Maximum number of profiles per user", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "841612 ", "URL": "/notes/841612 ", "Title": "Maximum number of profiles per user", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "841612 ", "URL": "/notes/841612 ", "Title": "Maximum number of profiles per user", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "841612 ", "URL": "/notes/841612 ", "Title": "Maximum number of profiles per user", "Component": "BC-SEC-USR-ADM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "710", "ValidTo": "710", "Number": "841612 ", "URL": "/notes/841612 ", "Title": "Maximum number of profiles per user", "Component": "BC-SEC-USR-ADM"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}