{"Request": {"Number": "1676665", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 318, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017377172017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001676665?language=E&token=2EC0E18D45D0CC1D5C4B5EAC720E79F5"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001676665", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001676665/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1676665"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 35}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.07.2022"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-MSS"}, "SAPComponentKeyText": {"_label": "Component", "value": "SQL Server in SAP NetWeaver Products"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SQL Server in SAP NetWeaver Products", "value": "BC-DB-MSS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-MSS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1676665 - Setting up Microsoft SQL Server 2012"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP note provides information about the following:</p>\r\n<ul>\r\n<li>Setting up and running SQL Server 2012 for an SAP system</li>\r\n</ul>\r\n<ul>\r\n<li>Upgrading SQL Server 2005, or SQL Server 2008, or SQL Server 2008(R2) to SQL Server 2012</li>\r\n</ul>\r\n<div class=\"longtext MonoSpace\" id=\"MonoSpace\">\r\n<p>*****************************************************************************************************************************************************</p>\r\n<p><strong><strong>Microsoft's Extended Support for Microsoft SQL Server 2012 is ended on&#160;</strong>12th July 2022. See SAP notes 3049393 and 2297283 for details.</strong></p>\r\n<p>*****************************************************************************************************************************************************</p>\r\n</div>\r\n<p><span style=\"font-size: 14px;\">&#160;</span></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>1603 vcredist_x64.msi&#160; 15016 str_default</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<ul>\r\n<li><strong>If you are using this note to install NetWeaver on SQL Server on any virtualization platform (on-premise, hosted, hyperscaler, etc.), be sure to read the two central notes&#160;<a target=\"_blank\" href=\"/notes/1380654\">1380654</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/1492000\">1492000</a>&#160;and the related platform-specific notes so that you ensure proper landscape configuration.</strong></li>\r\n</ul>\r\n<ul>\r\n<li>\r\n<p style=\"display: inline !important;\"><strong>If you plan to deploy your system to Microsoft Azure Virtual Machines (IaaS), be sure to read the following first:</strong></p>\r\n<ol>\r\n<li><strong>SAP Notes <a target=\"_blank\" href=\"/notes/1928533\">1928533</a> and <a target=\"_blank\" href=\"/notes/2497147\">2497147</a>.</strong></li>\r\n<li><strong>Read all of the documentation written by our Microsoft colleagues and posted in the <a target=\"_blank\" href=\"https://docs.microsoft.com/en-us/azure/virtual-machines/workloads/sap/get-started\">Azure Documentation Portal</a>.</strong></li>\r\n<ul>\r\n<li><strong>A critical document to read is the <a target=\"_blank\" href=\"http://go.microsoft.com/fwlink/?LinkId=397965\">SAP DBMS Guide on Azure </a>for SQL Server specific configuration details.</strong></li>\r\n</ul>\r\n<li><strong>See the Microsoft colleagues&#8217; blog <a target=\"_blank\" href=\"https://techcommunity.microsoft.com/t5/sap-on-microsoft/ct-p/SAPonMicrosoft\">Running SAP Applications on the Microsoft Platform</a>.</strong></li>\r\n<li><strong>All other standard SAP on SQL Server Notes like the \"Setting Up...\", \"SQL4SAP...\" and \"Configuration Parameters\" are also valid for systems deployed in Microsoft Azure Virtual Machines.</strong></li>\r\n</ol></li>\r\n</ul>\r\n<ul>\r\n<li>Always download the latest version of this SAP note since it contains important and up-to-date information.</li>\r\n</ul>\r\n<ul>\r\n<li>When using SQL Server 2012 with your SAP system, you must follow the instructions of the guide: <a target=\"_blank\" href=\"https://help.sap.com/viewer/upgrade_sql2012\"><span style=\"text-decoration: underline;\">Upgrade to and Installation of SQL Server 2012 in an SAP Environment</span></a>.&#160; For practical reasons, we call this guide \"Upgrade and Installation Guide\" in this SAP note.</li>\r\n</ul>\r\n<ul>\r\n<li>SQL Server 2012 is supported on Windows Server releases from Windows Server 2008, upto and including Windows Server 2016. Any SAP system running on an older Windows version must be migrated to Windows Server 2008 or higher to run on SQL Server 2012. For more information, see SAP note 1476239.</li>\r\n</ul>\r\n<ul>\r\n<li>After the installation of a new SAP system on SQL Server 2012 or after the database upgrade of an existing SAP system to SQL Server 2012, SAP recommends upgrading the SAP kernel executables to the 720_EXT backward compatible kernel for all supported SAP releases. For more information, see SAP notes 1553301 and 1636252.</li>\r\n</ul>\r\n<ul>\r\n<li>RDBMS media for SQL Server 2012:<br />You obtain the SQL Server 2012 RDBMS media as part of the installation package from SAP. However, you can also download it at:<br />http://service.sap.com/swdc -&gt; Installations and Upgrades -&gt; A-Z index -&gt; &lt;first letter of your product&gt; -&gt; &lt;your product version&gt;<br /><br />If your SAP product was initially not released for SQL Server 2012, you might not find the media there. In this case, use the material number of the SQL Server 2012 RDBMS media and search for it. For more information about the material number, see SAP note 1684545.</li>\r\n</ul>\r\n<ul>\r\n<li>For all SAP systems running on SQL Server 2012, apply at least the following kernel patch levels:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Kernel 7.00 ------&gt; 278</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Kernel 7.01 ------&gt; 118</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Kernel 7.10 ------&gt; 223</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Kernel 7.11 ------&gt; 109</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Kernel 7.20 ------&gt; 70 (PL 221 required for R3Load based system copies)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP recommends always applying the latest kernel patch and DBSL library when running SQL Server 2012. For more information about how to download kernel patches, see SAP note 19466.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>To run your SAP Java system based on SAP NetWeaver 7.0 (or 7.0 EHP1, or 7.0 EHP2) on SQL Server 2012, your system must run with the \"Microsoft SQL Server JDBC Driver\" version 1.2. To check your JDBC driver version, apply the instructions in SAP note 639702. In addition, apply one of the following SAP notes:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>To replace your DataDirect JDBC driver with the \"Microsoft SQL Server JDBC Driver\", apply SAP note 1145221.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>To patch your \"Microsoft SQL Server JDBC Driver\" to version 1.2, apply SAP note 639702.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Before you plan to install or upgrade your SAP system on SQL Server 2012, read SAP note 1651862: \"Release planning for Microsoft SQL Server 2012\", which describes the supported SAP and SQL Server 2012 release combinations.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>CONTENT:<br />&#160;&#160;&#160;&#160;&#160;&#160; I&#160;&#160;&#160;&#160;INSTALLING AN SAP SYSTEM ON SQL SERVER 2012</strong><br /><strong>&#160;&#160;&#160;&#160;&#160;&#160; II&#160;&#160; SYSTEM COPY ON SQL SERVER 2012</strong><br /><strong>&#160;&#160;&#160;&#160;&#160;&#160; III&#160;&#160;SQL SERVER UPGRADE TO SQL SERVER 2012</strong><br /><strong>&#160;&#160;&#160;&#160;&#160;&#160; IV&#160;&#160; SAP UPGRADE AND SAP EHP INSTALLATION ON SQL SERVER 2012</strong><br /><strong>&#160;&#160;&#160;&#160;&#160;&#160; V&#160;&#160;&#160;&#160;SPECIAL SAPINST OPTIONS</strong><br /><strong>&#160;&#160;&#160;&#160;&#160;&#160; VI&#160;&#160; TROUBLESHOOTING</strong><br /><strong>&#160;&#160;&#160;&#160;&#160;&#160; VII&#160;&#160;FREQUENTLY ASKED QUESTIONS</strong><br /><br /><br /><strong>I&#160;&#160;&#160;&#160;INSTALLING AN SAP SYSTEM ON SQL SERVER 2012</strong></p>\r\n<ol>\r\n<li>Install SQL Server 2012 using the DVD with the material number defined in SAP note 1684545. For details about installing SQL Server including SQL Server AlwaysOn and SQL Server Failover Cluster, you must follow the instructions of the guide: \"Upgrade to and Installation of SQL Server 2012 in an SAP Environment\".</li>\r\n<li>Installation media for installing the SAP system:</li>\r\n<ul>\r\n<li>Install your SAP system using the Software Provisioning Manager (SWPM). Always use the newest version (SP) of the SWPM tool to perform the installation based services. <br /><a target=\"_blank\" href=\"http://help.sap.com/sltoolset\">http://help.sap.com/sltoolset</a> will guide you to the download location of the SWPM tool. <br />For more information read the SWPM release note referred to on <a target=\"_blank\" href=\"http://help.sap.com/sltoolset\">http://help.sap.com/sltoolset</a>.</li>\r\n<li>SAP systems based on <strong>SAP NetWeaver 7.0 </strong>(including all EhPs):<br />Use the kernel/Java media defined in SAP note 1680045.&#160; <br />Use ABAP export DVDs from the original shipment.</li>\r\n<li>SAP systems based on <strong>SAP NetWeaver 7.1 and higher: <br /></strong>Use the kernel media defined in SAP note 1680045. <br />Use ABAP/JAVA export DVDs from the original shipment.</li>\r\n</ul>\r\n<li>ABAP, ABAP+Java: Immediately after the installation, configure the transport management system (TMS) and then use the Software Update Manager (<strong>SUM</strong>) to apply the required SAP Support Package Stack (SPS) defined in SAP note 1651862 for your SAP product. You&#160;must <strong>not</strong>&#160;use transaction SPAM to apply the required SAP Support Packages after new installations.<br />In general, the ABAP SP level of the installation export is not sufficient for running on SQL Server 2012. Therefore - although eventually requested in the post-installation section of the installation guide - all operations on the SAP application server (other than configuring the TMS) must be postponed until the required SPS defined in SAP note 1651862 has been applied by using SUM.<br />Always use the newest version (SP) of SUM to apply the required SPSs to run on SQL Server 2012.&#160;<a target=\"_blank\" href=\"http://help.sap.com/sltoolset\">http://help.sap.com/sltoolset</a> will guide you to the download location of SUM.<br />For more information read the SUM release note referred to on <a target=\"_blank\" href=\"http://help.sap.com/sltoolset\">http://help.sap.com/sltoolset</a>.<br /><br /><strong>Be aware of the following:</strong></li>\r\n<ul>\r\n<li>Do not apply the SPAM update manually. The SPAM update is part of the stack that will be used by the SUM Tool.</li>\r\n<li>Use the SUM tool to apply the required support package stack. The tool has specific fixes and workarounds that are needed for the support package application on SQL Server 2012.</li>\r\n<li>Note: If SPAM is used and errors occur then the installation must be repeated.&#160; Do not attempt to repair the errors which occur when SPAM is used.</li>\r\n<li>For release SAP_BASIS 711 import note 1330869 using transaction SNOTE prior to starting the SUM update.</li>\r\n<li>For installations on MSCS cluster, make sure the system_pf variable is set in the transport profile as described in note 449270.</li>\r\n</ul>\r\n<li>Java: For a Java system, you do not require any support package for SQL Server 2012.</li>\r\n</ol>\r\n<p>After the successful installation of the SAP system with SQL Server 2012, configure the SQL Server as described in SAP note 1702408 and SAP Agent Job history as described in 1730470.</p>\r\n<p><strong>II&#160;&#160; SYSTEM COPY ON SQL SERVER 2012</strong></p>\r\n<p>You copy your SAP system using the mediasets defined in (I) for your SAP system.</p>\r\n<ul>\r\n<li>Perform the system copy of your SAP system using the Software Provisioning Manager (SWPM). Always use the newest version (SP) of the SWPM tool to perform system copy based services. Always use the kernel media defined in SAP note 1680045. <br /><a target=\"_blank\" href=\"http://help.sap.com/sltoolset\">http://help.sap.com/sltoolset</a> will guide you to the download location of the SWPM tool.<br />For more information read the SWPM release note referred to on <a target=\"_blank\" href=\"http://help.sap.com/sltoolset\">http://help.sap.com/sltoolset</a>.</li>\r\n<li><strong>Before </strong>performing a system copy of your SAP system you&#160;must make sure that you have applied the required Support Package Stack (SPS) as defined in SAP note 1651862.</li>\r\n<li>If you want to copy from SQL Server 2000 or lower, you have to perform an R3load/Jload based system copy. For source systems on SQL Server 2005 and higher you can use the R3load/Jload based system copy as well as the database-specific system copy methods.</li>\r\n<li>IMPORTANT: Some manual post-processing steps are required after the&#160;successful system copy procedure of SAP system.&#160;&#160;</li>\r\n<ul>\r\n<li>Configure SQL Server 2012 as described in SAP note 1702408.</li>\r\n<li>To maintain consistency of the DB13 job information stored in SAP Tables and MSDB Database system tables, follow the SAP note 1817705 instructions to do the required post processing steps.</li>\r\n<li>Configure the SQL Agent job history by following the instructions in SAP Note 1730470.</li>\r\n</ul>\r\n</ul>\r\n<p><strong>III&#160;&#160; SQL SERVER UPGRADE TO SQL SERVER 2012</strong></p>\r\n<p>Before performing an SQL Server upgrade to SQL Server 2012 for your SAP system you&#160;must make sure that you have applied the required Support Package Stack (SPS) as defined in SAP note 1651862.<br />For details about SQL upgrade including SQL Server AlwaysOn and SQL Server Failover Cluster, see the instructions of the guide: \"Upgrade to and Installation of SQL Server 2012 in an SAP Environment\".</p>\r\n\r\n<p><strong>IV SAP UPGRADE AND SAP EHP INSTALLATION ON SQL SERVER 2012 </strong></p>\r\n\r\n<p>Make sure that the kernel on the Upgrade Kernel DVD has the minimum patch level for the target release as defined at \"Reasons and Prerequisites\" of this SAP note.<br />For more information about details on how to patch the Upgrade Kernel DVD, see SAP note 1480785.<br />For updates to SAP NW 7.1 EhP1 and SAP NW CE 7.2, make sure that the (720/720_EXT) stack kernel used has at least the minimum patch level defined at \"Reasons and Prerequisites\" of this Note.<br />Upgrades to products based on SAP NW 7.1 (including EhP1) and SAP NW 7.2 are not supported on SQL Server 2012. I.e. if you plan to upgrade your SAP product based on SAP Netweaver 7.0 (including all EhPs) on SQL Server 2012, you have to upgrade to a SAP product based on SAP NetWeaver 7.3 and higher.<br /><br /><br /></p>\r\n<p><strong>V SPECIAL SAPINST OPTIONS</strong><br /><strong>Rename</strong></p>\r\n<p>SAP note 1619720 and the relevant guide describe how to use the SAPinst tool to rename an SAP system. This procedure is fully supported for SQL Server 2012 as long as you use SAP SYSTEM Rename 1.0 SP1 or higher, which you can download from SAP Service Marketplace.</p>\r\n<p><strong>Dual Stack Split</strong></p>\r\n<p>SAP note 1797362 and the relevant guide describe how to separate the Java and ABAP stacks. This procedure is fully supported for SQL Server 2012 as long as you use the DUAL STACK SPLIT TOOL 2.0 SP02 or higher, which is available at SAP Service Marketplace.</p>\r\n<p><strong>VI TROUBLESHOOTING</strong></p>\r\n<ul>\r\n<li>SQL error 213 with the text \"Column name or number of supplied values does not match table definition\" appears in the SM21 system log.<br />These errors are harmless and do not affect the operation of the system. The errors are fixed with SAP note 1667102, and should be solved by applying the support package level of that SAP note. </li>\r\n<li>SAP Software Provisioning Manager (SWPM) (formerly known as SAPinst) fails while installing vcredist<br /><br />SWPM (or SAPinst) shows the following error message:<br />Running msiexec failed with return code 1603: Fatal error during installation. Commandline was msiexec.exe /norestart /L vcredist_x64.logvcredist_x64.msi /qn<br /><br />In this case, install the Microsoft Visual C++ 2005 Service Pack 1 Redistributable Package ATL Security Update, which is available at: http://www.microsoft.com/download/en/details.aspx?displaylang=en&amp;id=14431<br />Retry the installation with SWPM.</li>\r\n<li><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;\"Error 15016: The default 'str_default' does not exist\",</span><br />occurs during SPAM following an installation.&#160; SPAM was applied in error, SUM must be used to apply the support packages to bring the system to a supported level on SQL Server 2012.<br />In this case the installation must be repeated, and then SUM must be used to apply the support packages.</li>\r\n<li>Errors occur when using SPAM following a system copy.&#160; <br />These errors can be one or more of the following:<br />&#160; Tables OCSCCOMPAT and OCSSPCOMPR are inconsistent<br />&#160;&#160;<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">\"Error 15016: The default 'str_default' does not exist\"&#65279;</span>&#160; <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Cannot insert the value NULL into column 'DACT_SUBSET', table '...PATHISTORY'; column does not allow nulls. INSERT fails.\"</span><br />In this case the system copy was made without bringing the source system first up to the required SAP_BASIS SP level required for SQL Server 2012. Do not attempt to repair the system by creating defaults or some other manual action.&#160; The source system must be updated with the required SP level, and the system copy must be repeated!</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><strong>VII FREQUENTLY ASKED QUESTIONS</strong></p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I005831)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I011516)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001676665/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001676665/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001676665/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001676665/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001676665/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001676665/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001676665/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001676665/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001676665/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "sql2012_upgrade.pdf", "FileSize": "646", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000073642012&iv_version=0035&iv_guid=FF403AA661E185488C89E7D5DCDBA0B1"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1783528", "RefComponent": "BC-DB-MSS-UPG", "RefTitle": "Migration path to Win2012/MSSQL2012 or MSSQL2014 for 4.6C and 6.20/6.40", "RefUrl": "/notes/1783528"}, {"RefNumber": "1732161", "RefComponent": "BC-OP-NT", "RefTitle": "SAP Systems on Windows Server 2012 (R2)", "RefUrl": "/notes/1732161"}, {"RefNumber": "1651862", "RefComponent": "BC-DB-MSS", "RefTitle": "Release planning for Microsoft SQL Server 2012", "RefUrl": "/notes/1651862"}, {"RefNumber": "1513170", "RefComponent": "BC-DB-MSS", "RefTitle": "Miscellaneous changes for SQL Server 2012 support", "RefUrl": "/notes/1513170"}, {"RefNumber": "1490337", "RefComponent": "BC-INS-NT", "RefTitle": "High Availability SAP System on Windows Server 2008 (R2)", "RefUrl": "/notes/1490337"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2457158", "RefComponent": "BC-DB-MSS-UPG", "RefTitle": "Rule .NET Framework error during SQL Server 2012 upgrade/installation", "RefUrl": "/notes/2457158 "}, {"RefNumber": "2384179", "RefComponent": "BC-OP-NT", "RefTitle": "SAP Systems on Windows Server 2016", "RefUrl": "/notes/2384179 "}, {"RefNumber": "1732161", "RefComponent": "BC-OP-NT", "RefTitle": "SAP Systems on Windows Server 2012 (R2)", "RefUrl": "/notes/1732161 "}, {"RefNumber": "1651862", "RefComponent": "BC-DB-MSS", "RefTitle": "Release planning for Microsoft SQL Server 2012", "RefUrl": "/notes/1651862 "}, {"RefNumber": "1490337", "RefComponent": "BC-INS-NT", "RefTitle": "High Availability SAP System on Windows Server 2008 (R2)", "RefUrl": "/notes/1490337 "}, {"RefNumber": "1783528", "RefComponent": "BC-DB-MSS-UPG", "RefTitle": "Migration path to Win2012/MSSQL2012 or MSSQL2014 for 4.6C and 6.20/6.40", "RefUrl": "/notes/1783528 "}, {"RefNumber": "1513170", "RefComponent": "BC-DB-MSS", "RefTitle": "Miscellaneous changes for SQL Server 2012 support", "RefUrl": "/notes/1513170 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "720", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}