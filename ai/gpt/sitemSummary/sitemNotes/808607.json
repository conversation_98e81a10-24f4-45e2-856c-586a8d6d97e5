{"Request": {"Number": "808607", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1123, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015829802017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=A3EB96CF89FCEA8FF18F82208FADEA0F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "808607"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 39}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Customizing"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.02.2023"}, "SAPComponentKey": {"_label": "Component", "value": "BC-OP-AS4"}, "SAPComponentKeyText": {"_label": "Component", "value": "IBM AS/400"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Operating System Platforms", "value": "BC-OP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "IBM AS/400", "value": "BC-OP-AS4", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP-AS4*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "808607 - IBM i: Memory management in a PASE-based system"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note is valid for the SAP kernel versions based on PASE on iBM i, so as of SAP Kernel Release 6.40.<br />The note contains the most recent information concerning Memory Management.<br />This note will be updated as necessary, so you should always refer to the most recent version.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Memory management, STORAGE_PARAMETERS_WRONG_SET, iSeries, TSV_TNEW_INDEX_NO_ROLL_MEMORY, SORT_EXTRACT_INDEX_NO_ROLL, STORAGE_PARAMETERS_WRONG_SET, TSV_INDEX_INDEX_NO_ROLL_MEMORY, Java server processes</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>See 'Solution' for further information.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>1. Special features in memory management on IBM i</strong><br /><strong>1.1. Memory management in SAP Systems on IBM i - general</strong></p>\r\n<p><br />With the introduction of the Power architecture on IBM i hardware and the possibility of using programs created for AIX in a binary-compatible manner on IBM i, SAP chose Option 33 (PASE, 64-bit) of the operating system as the basis for the runtime on IBM i as of SAP Kernel 6.40. This means that IBM i is closer to the UNIX-type model of memory management on AIX but is not identical to AIX, since the restrictions in memory management that apply on AIX were avoided on IBM i in PASE.<br />Each SAP memory type (roll memory, extended memory, heap memory, paging memory) is ultimately based on teraspace on IBM i/PASE. This means that 1 terabyte is available for the address space of the SAP System. In addition, the principle of 'tagged pointers' is unknown in PASE. This means that data containing pointers can also switch the work process. As a result of this, the paging and what are known as the 'roles' of a user context can take place between different work processes in the same way as for the UNIX system; this removes the restrictions specific to IBM i in previous releases.</p>\r\n<p><strong>1.1.1. Shared memory pools (SAP pools)</strong></p>\r\n<p><br />Unlike for AIX, shared memory pools from the point of view of SAP terminology do not need to be defined on IBM i. The ipc/shm_psize_&lt;number&gt; parameters are therefore ignored and must be removed from all profiles, or must at least be commented out.</p>\r\n<p><strong>1.1.2. Paging memory</strong></p>\r\n<p><br />The SAP paging concept is used to write data that is in the file system (IFS) into the paging file, if there is insufficient main memory space available.<br />However, on IBM i with 64-bit addressability, there is enough main memory available to be able to do without the paging file. As a result, SAP paging memory does not distinguish between paging cache (rdisp/PG_SHM) and paging file (rdisp/PG_MAXFS). (The paging cache is the same size as the paging file, that is, the entire paging file is buffered and therefore is not available).<br /><br />Generally speaking, paging memory is very rarely required. Typically, programs require paging memory for the monthly, quarterly, or year-end closing. This can result in high consumption.<br />Therefore, the recommended value for rdisp/PG_SHM and rdisp/PG_MAXFS is 8192 in test and development systems. The value 32768 or higher can be of use in production systems.<br />If necessary, increase these parameters in steps of 8192 until any error messages concerning missing paging memory are corrected. The parameters are specified in pages of size 8 KB. <br />Ensure that rdisp/PG_SHM and rdisp/PG_MAXFS always have the same value. This value is not allowed to be zero.<br />For IBM releases before IBM i 6.1, a memory segment must be smaller than 2 GB. This means that, 262143 is the maximum value for the SAP paging area. As of IBM i 6.1, values up to 1 TB are allowed.<br />SAP paging memory is allocated when the SAP application server is started, and therefore has direct influence on the temporary memory used.</p>\r\n<p><strong>1.1.3. Roll memory</strong></p>\r\n<p><br />As of SAP Kernel 6.40, SAP Systems on IBM i have the same implementation for the SAP roll memory as UNIX systems. However, for the same reasons as for the paging memory, the system does not use the roll file.<br /><br />If a user logs on to the SAP System, a user context is generated with one external and one internal session, that is, the roll memory management allocates a management-shared memory as well as a roll area.<br /><br />When an internal session is created, the roll area is preallocated up to the size of ztta/roll_first. If you require a larger roll area, the roll area is retrieved from the extended memory, up to a maximum of the size of ztta/roll_extension. For any further requirements, space is requested from the roll memory up to the upper limit (ztta/roll_area - ztta/roll_first). This process should prevent temporary memory being wasted at the start even though it might never be actually needed. The ztta/roll_area and ztta/roll_first profile parameters can be set to any value as long as the following condition is met:<br />1 &lt;= ztta/roll_first &lt;= ztta/roll_area<br /><br />If a user is taken out of the roll area, the roll area is freed for the next user. The data of the current user is copied into the roll buffer. This therefore needs to be large enough to buffer the roll areas of the users that are currently inactive. On UNIX systems, its size is determined by the rdisp/ROLL_SHM profile parameter. The size of the roll file (which is not used on IBM i) is defined by the parameter rdisp/ROLL_MAXFS. On iSeries, only the rdisp/ROLL_SHM parameter is used. The parameter rdisp/ROLL_SHM should be increased in increments of 8192 if a memory bottleneck is reported in the roll buffer in the system (for example, dev_w&lt;n&gt; developer traces in transaction AL11 or ST11 or messages of the class R0B or P03 in the system log). This buffer does not have a considerable effect on system performance, but it should always be big enough to include the data from all users that are logged on. The size of this buffer using the number of users logged on cannot be predicted, because their data is stored on the buffer in a compressed form. Although the parameter rdisp/ROLL_MAXFS is not used on IBM i, it must always be set the same as the parameter rdisp/ROLL_SHM (and it is not allowed to be zero).<br /><br /></p>\r\n<p><strong>1.1.4. Heap Memory</strong></p>\r\n<p><br />Heap memory is local process (private) memory. If heap memory is allocated to meet the memory requirement of a session (user context), this session can no longer be rolled, since this heap memory cannot be addressed by another work process. As a result, this work process is assigned exclusively to the session. In the context of SAP, this work process status is called PRIV mode.<br />In the standard system, the maximum size of the available IBM memory is restricted by a default value of 256 to 64 GB. If you require more memory in PRIV mode, you must insert a line of the type<br />as4/MAXDATA64 = 400<br />in the SAP instance profile. The numeric value specifies the number of segments of the size 256 MB that are to be allocated. Therefore, in this example, 102.4 GB of heap memory is requested.  The maximum value is 4096 (this function is available in 6.40 as of patch 25 of the R3INLPGM and in 7.00 as of patch 19 of the R3INLPGM).<br /><br />As long as the work process has this status, it cannot process another user context.</p>\r\n<p><strong>1.1.5. Extended Memory</strong></p>\r\n<p><br />If the quota for the extended memory (ztta/roll_extension) is large enough, a dialog work process should never enter PRIV mode on IBM i. The PRIV mode is not important for non-dialog work processes, since rolling is generally not carried out here. The extended memory is allocated when the SAP instance is started, in accordance with the value of em/initial_size_MB. The size of the extended memory cannot be changed during the runtime of an instance. You can increase the em/initial_size_MB profile parameter in increments of 1024 or em/blocksize_KB up to 1048576.  The value of em/initial_size_MB must always be a multiple of em/blocksize_KB. <br /><br />The parameter em/blocksize_KB may only be a multiple of 2. In the case of changes, em/initial_size_MB must always be checked, since it must be divisible by em/blocksize_KB.<br /><br />For each internal session, you can request extended memory up to a value of ztta/roll_extension in multiples of em/block_size_KB from the extended memory pool (em/initial_size_MB). Since this pool was allocated when the SAP instance was started, no additional temporary memory is allocated.<br /><br />However, the free temporary memory required for the extended memory (depending on the setting of em/initial_size_MB) must also be available when the SAP instance is started. Otherwise, errors occur during the initialization, and the SAP instance terminates again.<br /><br />If extended memory is released again by the user context, the blocks are indicated as free in the extended memory pool and can immediately be reused by other applications. Temporary memory is not physically released before the SAP instance is exited.<br /><br />To prevent a dialog work process from entering PRIV mode, we recommend that you set ztta/roll_extension to a value that is sufficiently large. Here, the default value of 2000000000 is the optimum size in most cases.</p>\r\n<p><strong>1.1.6. Allocation Sequence</strong></p>\r\n<p><br />On IBM i, as of 6.40 (similar to AIX) the strategies for allocating memory for dialog and non-dialog work processes are the same.<br /><br />In all work processes, only the first part of the roll area (ztta/roll_first) is used at first. After this, the extended memory (ztta/roll_extension) is used, followed by the second part of the roll area (ztta/roll_area - ztta/roll_first), and finally the heap memory (abap/heap_area_dia or abap/heap_area_nondia).<br /><br />This is the case as of DW patch level 135 (6.40) or 65 (7.00).</p>\r\n<p><strong>1.1.7. Operating system perspective</strong></p>\r\n<p><br />When you introduce PASE, the operating system can obtain the memory required by the SAP system in two ways; these are \"shared memory\" or \"memory mapped to file\".<br />The \"shared memory\" option provides each SAP user with their own memory segments from the OS shared memory pool. If this user is activated by a roll-in, their 'personal' memory segments are activated in the current work process (shmat) and they are then deactivated again (shmdt) during the roll-out. Since only this user has control over these segments, no other user can obtain access and possibly read or overwrite the data.<br />The \"memory mapped to file\" option creates the entire amount of memory required by the SAP system for all users as a file in the file system of the operating system. Each user is allocated part of this file as their memory segment. The entire memory (all users) is visible to the SAP system. Therefore, to isolate the data of the individual users, there is the option of protecting the data segment of the individual user from this file in that you open the data segment of the user only for this user for changes while it can be opened for all other users with regard to the operating system only for reading (PROT_READ) or not at all (PROT_NONE). This does not mean that the individual users can read the data of other users; this is prevented by the SAP system. Instead, it means that the data segments are opened in read or change mode with regard to the operating system.<br /><br />When PASE was introduced, a range of performance measurements were executed to determine which method is better. It was found that the option 'memory mapped to file' is far faster than the option 'shared memory'. This best performance was achieved by implementing the method 'memory mapped to file' in combination with the option PROT_READ. The roll-in or roll-out lasts considerably longer with the option PROT_NONE. For this reason, PROT_READ is the default setting for all PASE-based SAP systems on IBM i.</p>\r\n<p><strong>1.1.8. Improvements as of Version IBM i 6.1</strong></p>\r\n<p><br />In IBM i 6.1, memory management in the operating system has been revised. As a result, the 'shared memory' option could be improved and accelerated to the extent that it is now faster than the 'memory mapped to file' option on IBM i 6.1 (however, this acceleration does not result in a noticeably increased throughput at runtime of the SAP system because memory allocation during the roll-in and roll-out phases does not use a significant amount of the runtime of the SAP system). However, since this method is the fastest as of IBM i 6.1 and is even more efficient for isolating the data of the individual users than the method used up to now, it was decided to enable it as an alternative. However, for technical reasons, this is only possible in an SAP system that has been changed to a UNIX-style OS user (see Note 1123501). (If you still have the previous OS user in accordance with Note 834218 and use this procedure anyway, increased shared memory consumption may occur. Under certain circumstances, these segments for the SAP system may be lost are only reclaimed through an IPL.) In addition, you require an SAP kernel of Version 7.20 (or 7.20-EXT) or above as of DW Patch Level 300.<br /><br />If your system meets these prerequisites, you can switch to the 'shared memory' option by adding the following line (all in uppercase letters) to your instance profile on IBM i 6.1 and subsequent IBM OS releases:<br />ES/TABLE = SHM_SEGS<br />In addition, it is essential that you enter the parameters as4/MAXSHR64 and ES/SHM_BASE_ADDR (described below) with suitable values into the instance profile.</p>\r\n<p><strong>Comment:</strong> As of SAP Kernel Version 7.54, the setting ES/TABLE = SHM_SEGS is the default setting once your SAP system has been switched to UNIX-type OS users in accordance with SAP Note 1123501. For a kernel of this version, you no longer have to adjust the parameters as4/MAXSHR64 and ES/SHM_BASE_ADDR, either.</p>\r\n<p>In the standard system, the maximum size of the available shared memory for each process is restricted to 64 MB by IBM using an OS environment variable. If you require more memory, you must insert a line similar to the following in the SAP instance profile:<br />as4/MAXSHR64 = 400<br />The numeric value specifies the maximum number of segments with an individual size of 256 MB that are allocated in the process. Therefore, in this example (as4/MAXSHR64 = 400), up to 100 GB can be allocated. The maximum value is 4096. Note that the operating system also counts the other global areas (such as PXA buffers, table buffers, and so on) among these segments. You can find out what size they are if you log on as the user &lt;SID&gt;ADM with the program SHOWIPC: CALL SHOWIPC PARM('&lt;inst-no.&gt;'), where &lt;inst-no.&gt; represents the number of the (current) instance of your SAP system.<br />You may have to use the parameter as4/MAXSHR64 to increase the amount of shared memory that is available to the SAP system to correct memory bottlenecks. In particular, this is required if \"dev_disp\" or \"dev_w&lt;n&gt;\" contains a line of the type \"SHM2_EsInit: map_at/shmat(4656,(nil),0) failed with Addr=...\".</p>\r\n<p><strong>Comment:</strong>&#x00A0;As of SAP Kernel Version 7.54, the default setting is as4/MAXSHR64 = 2048.</p>\r\n<p>For a kernel &gt; 7.40, ignore the next paragraph. Its content was required as a workaround during a brief phase only. First, try to get by without setting ES/SHM_BASE_ADDR. Only make an attempt with this parameter if the system fails to start without it.</p>\r\n<p style=\"padding-left: 30px;\">Determination of revised parameter ES/SHM_BASE_ADDR:</p>\r\n<p style=\"padding-left: 30px;\">This value defines the lower limit as of which the work areas of the users are allowed to be located in the memory. To determine this value, start the ABAP program RSTUNSHM in the SAP system and sort the result list according to addresses. Take the last value from 'EndAddress' and generously round it up hexadecimally (for example, the value 0700000E0007F378 becomes 0700001000000000). Enter this value with a leading '0x' as the address (ES/SHM_BASE_ADDR = 0x0700001000000000). With the initial 0x, the value must have a total of 18 characters. At the same time, this value must be within the predefined area of as4/MAXSHR64. You calculate this size as as4/MAXSHR64 * 256 MB (for example, as4/MAXSHR64 = 400 results in 100 GB - hexadecimal 0x0700001900000000). If this value is below or just above ES/SHM_BASE_ADDR, you must increase as4/MAXSHR64. (Note: as4/MAXSHR64 = 800 has proven to be the best value for ES/SHM_BASE_ADDR = 0x0700001000000000.)</p>\r\n<p style=\"padding-left: 30px;\">As already mentioned, set this parameter in an emergency only.</p>\r\n<p><br />With the setting ES/TABLE = SHM_SEGS, the parameter em/initial_size_MB loses its significance, since this parameter specifies the total size of the memory area for the case 'memory mapped to file' that is available for the work of all of the (active and inactive) logged-on users. With SHM_SEGS, shared memory segments of a fixed size are exclusively assigned to each user and remain reserved for the user in question until they log off from the SAP system. However, in the work process, only the segments of the user who is currently active in the work process are logged on (and they are the only ones that count for the OS limit as4/MAXSHR64). With the roll-out from the work process, these segments are logged off from the system again, but they remain available for a further roll-in. Therefore, the following parameters must now be taken into account (all in uppercase letters):<br />- EM/TOTAL_SIZE_MB (default value 256 GB): This value specifies the maximum amount of shared memory that is allowed to be created across all users (active and inactive). The default value is sufficient for large systems and should not be changed. Do not convert this value to as4/MAXSHR64 because the letter only specifies the limit for the shared memory that is currently allocated in the work process.<br />If this size is too small due to the continuous logging on of users, users will see messages on their screens telling them that memory is running low and asking them to close open transactions, or lines of the type '*** ERROR =&gt; ESSHM: Update BlockCurrent=32 + BlockChange=1 &gt; MAXBlockInSys=32 [esuxshm2.c ]' or '*** ERROR =&gt; SHM2_EsCreate: esSHMAllocateBlock(handle=0,blocks=1,) returned 16 [esuxshm2.c ]' will appear in dev_wpNN. Afterwards, the SAP system may come to a standstill.<br />- ES/SHM_USER_COUNT (Default value 8192). This value specifies how many users can be logged onto the system at the same time. The default value should also be sufficient during the peak load.<br />- ES/SHM_SEG_COUNT (Default value 8192). This value specifies the maximum number of segments that can be allocated across all users. It should be changed only if more than 5000 users are working in the system AT THE SAME TIME.<br />- ES/SHM_SEG_SIZE (Default value 4 GB). This value specifies the size of an individual shared memory segment for each user. Shared memory is allocated with regard to the operating system using this size, whereas the actual data allocation is controlled by the SAP system. The default value is usually sufficient because additional segments are reallocated for the user if more than 4GB is required.<br />- ES/SHM_PROC_SEG_COUNT (Default value 11). This value specifies the maximum number of shared memory segments that can be allocated to a work process in each case. It must always be one greater than the following parameter. The calculated value ES/SHM_PROC_SEG_COUNT * ES/SHM_SEG_SIZE plus the size of the global buffer (see program SHOWIPC) is restricted by as4/MAXSHR64. (The latter might need to be increased.)<br />- ES/SHM_MAX_PRIV_SEGS (Default value 10). This value specifies the maximum number of segments that can be allocated for a user. The calculated value ES/SHM_MAX_PRIV_SEGS * ES/SHM_SEG_SIZE specifies the maximum size of the data that a user can process. If this size is insufficient, the work process goes into PRIV mode and you must increase it. However, you must then also increase the parameters listed previously, and possibly as4/MAXSHR64.<br />- These parameters are described in detail in SAP Note 789477 and are also relevant for IBM i even if the SAP Note only mentions AIX.</p>\r\n<p><strong>2. Used changeable parameters</strong></p>\r\n<p><br />You can obtain complete documentation for the individual parameters using transaction RZ11.<br /><br />ztta/roll_first<br />ztta/roll_area<br /><br />ztta/roll_extension<br />em/initial_size_MB<br /><br />rdisp/PG_MAXFS<br />rdisp/PG_SHM<br /><br />abap/heaplimit<br />abap/heap_area_dia<br />abap/heap_area_nondia<br />abap/heap_area_total</p>\r\n<p><strong>3. Standard settings for changeable parameters</strong></p>\r\n<p><br />SAP profile parameter / Explanation<br /><br />ztta/roll_first = 1 (1 byte roll first)<br />ztta/roll_area = 3000000 (3 MB roll area per internal<br /> session)<br />ztta/roll_extension = 2000000000 (2 GB extended memory per<br /> user)<br />rdisp/ROLL_SHM = 32768 (32*8 MB, roll buffer)<br />rdisp/ROLL_MAXFS = 32768 (32*8 MB, roll file)<br /><br />em/initial_size_MB = 4096 (4096 MB extended memory<br /> altogether)<br />em/blocksize_KB = 4096 (4096 KB per memory request from extended memory)<br />rdisp/PG_MAXFS = 32768 (32*8 MB, paging file)<br />rdisp/PG_SHM = 32768 (32*8 MB, paging buffer)<br />abap/heaplimit = 40000000 (40 MB, work process restart)<br />abap/heap_area_dia = 2000000000 (2 GB heap per dialog WP)<br />abap/heap_area_nondia = 2000000000 (2 GB heap per non-dialog WP)<br />abap/heap_area_total = 2000000000 (2 GB heap per R/3 system)<br /><br />Note the following with regard to rdisp/PG_MAXFS = 32768 and rdisp/PG_SHM 1.1.2. and with regard to rdisp/ROLL_MAXFS = 32768 and rdisp/ROLL_SHM 1.1.3.<br /><br />The parameters ztta/roll_extension, em/initial_size_MB, abap/heap_area_nondia, abap/heap_area_total, rdisp/PG_MAXFS, rdisp/PG_SHM, rdisp/ROLL_MAXFS and rdisp/ROLL_SHM do not affect performance (in the strict sense of \"speed\") in any way. They are only relevant to stability, in that they prevent programs from terminating. The values of these parameters therefore have a preventive character.<br />For example:<br />Even if you \"never\" require 32*8 MByte paging (rdisp/pg_MAXFS = 32768), you should make sure that the paging area is sufficiently large. It is not advisable to wait for the customer's year-end final settlement to terminate because the configuration of the paging area was too small and to configure the paging area afterwards on an \"as required\" basis. Similar examples apply to the other parameters.</p>\r\n<p><strong>4. Checking the operating system setting</strong><br /><strong>4.1. Free memory in Auxiliary Storage 1 (ASP 1) to start the SAP instance</strong></p>\r\n<p><br />In the used auxiliary storage 1 (ASP 1), there must be enough free space for the SAP system to start.<br />Guide value: At least 15 GB, or preferably 25-40 GB (for large installations, definitely over 100 GB)<br /><br />WARNING: The use of temporary memory is heavily dependent on the profile parameters with which your system is configured (among other things em/initial_size_MB, abap/buffersize). Check your estimated initial use after profile changes using the SAP test program 'sappfpar' (can be found in the kernel library).<br /><br />Check the free space with the IBM i command WRKSYSSTS.</p>\r\n<p><strong>4.2. Free auxiliary storage (ASP) while the SAP instance is running</strong></p>\r\n<p><br />For an SAP system to run efficiently, there must be enough free space in the used auxiliary storage (ASP).<br />Guide values: With the SAP System started, and at the end of a productive day, the ASP should be used to a maximum of 90%. However, 5 GB disk space should always be kept free as a safety margin. (Temporary memory consumption depends on the number of SAP users and the transactions used.)<br /><br />WARNING: If there is not as much memory in the system ASP as requested by the application, this leads to a system standstill on IBM i. This can cause data loss.</p>\r\n<p><strong>4.3. Maximum temporary memory in IBM i job class descriptions</strong></p>\r\n<p><br />This value is set to *NOMAX and should never be changed.<br /><br />As a result of the dispatching mechanism in the SAP system, work processes of a work process type with lower numbers are given priority when work is distributed. Therefore, they process a higher percentage of user contexts than work processes with higher numbers. However, this results in more frequent requests for memory from the operating system, which exclusively maps the consumption of the temporary memory to IBM i jobs (the work processes) and not to SAP user contexts. Therefore, it is normal for the work processes of a class with lower numbers to exhibit higher than average temporary memory allocation.<br />A restriction to 1 GB or 2 GB is not a suitable means of preventing SAP jobs that allocate memory from causing the system ASP to overflow, resulting in a system standstill, if too little free temporary memory is available. If the limit from the job class description is reached, work process terminations will occur in SAP memory management. Furthermore, ES_SPIN_LOCK_ERROR may occur and, in the worst case, the entire SAP instance may hang. </p>\r\n<p><strong>4.4. Memory pool must be sufficiently large</strong></p>\r\n<p><br />All SAP jobs are preset to run in the *BASE-Pool (system pool 2).<br />You should therefore make as much memory as possible available for this pool. 1000 MB is the absolute minimum. If you also run other applications in your system, assign a separate pool to the SAP system.</p>\r\n<p><strong>4.5. Memory Pool Paging Option</strong></p>\r\n<p>Set the paging option of the memory pool in which the SAP System is running (by default the *BASE pool) to *CALC. This allows IBM i to set the paging characteristic of the memory pool dynamically for optimum performance.</p>\r\n<p><strong>5. Monitoring and tuning</strong><br /><strong>5.1. Paging</strong></p>\r\n<p><br />If you notice high paging rates in a memory pool in transaction ST06 or in the CCMS alert monitor, check the paging option (see 4.5) and increase the size of this pool.<br /><br />Machine pool: This is always system pool 1.<br />The paging rate should be lower than 10 page faults per second.<br /><br />Other pools: The paging rate should be lower than 200 page faults per second. This is not a strict limit, but it is strongly dependent on the number of active SAP work processes.<br /><br />The following example can help you when determining the paging rate in your system:<br /><br /><br />Error pages in the memory pool used by SAP: 200 / second<br /><br />Number of active work processes<br />(not the total number of work processes): 10<br /><br />Resulting average number of page faults<br />per work process: 20 per second<br /><br />Resulting paging overhead (accepted<br />average service time of hard disks is 4 ms) : 20 per second * 4 ms<br /><br />This corresponds to a paging overhead of 8%. A limit of 10% should not be exceeded. Values in the single figure range around 2% are optimum.</p>\r\n<p><strong>5.2. Automatically Restarted SAP Processes</strong></p>\r\n<p><br />If a transaction has used heap memory, this remains physically allocated and assigned to the work process even if the transaction is closed. (The operating system function does not release the memory physically but only marks it as free internally so that it can be reused. The physical release takes place only when the IBM i job ends.)<br /><br />The transactions that run in this work process subsequently might reuse only a fraction of this heap memory. As a result, the consumption of temporary memory may be increased.<br /><br />The parameter abap/heaplimit is used to control the physical utilization of temporary memory. This specifies how much heap memory a transaction can use without the work process (IBM i job) being restarted afterwards. By decreasing the value of this parameter, you can force the work process to restart immediately.<br /><br />If you often execute transactions that need heap memory, check your parameter settings of em/initial_size_MB and ztta/roll_extension.<br /><br />Restarting a work process is expensive. It involves ending the current IBM i job (closing all open files, possibly writing a job log etc.) and generating a new IBM i job. This can seriously affect performance.</p>\r\n<p><strong>5.3 Estimating the average consumption of temporary memory</strong></p>\r\n<p>There are two large consumers of temporary storage. They are the SAP buffers and the user contexts.<br />With SAP buffers, you have to distinguish between statically allocated and dynamically allocated buffers. In this context, statically allocated means that the buffers are allocated when an SAP instance is started. Dynamically allocated means that the buffers have a maximum size (which is determined by parameters) but that only the size currently required is allocated. Most buffers are statically allocated, and only the roll buffer is dynamically allocated. To determine the real buffer size, you can use transaction ST02. To determine the consumption of temporary storage by user contexts, you can use transaction SM04. Here, you can determine the maximum number of users logged on and the maximum number of internal sessions over the course of a day. Roughly 60 KB is allocated for each user in the roll area. Furthermore, it is assumed that each SAP user occupies an average of 32 MB extended memory. (Check this average value for your SAP configuration in the steady SAP System in transactions ST02 (maximum extended memory requirement) and SM04 (number of users)).<br />In addition, temporary memory is consumed for database operations (e.g. temporary indexes, sorts, joins). In the case of a two-tier environment, this temporary memory is also assigned to the work processes (whereby the unequal load distribution is again reflected here in the work processes) and in the case of a three-tier environment, it is allocated to the shadow processes on the database server.</p>\r\n<p><strong>6. Problems and solutions</strong><br /><strong>6.1. Measures for avoiding memory bottlenecks in the system ASP</strong></p>\r\n<p>You can use the following parameters to influence the maximum consumption of temporary memory (WRKSYSSTS, assistance level 2, value for 'Current unprotect used'):<br />ztta/roll_area Roll area per internal session<br />rdisp/PG_SHM Paging memory per application server<br />rdisp/PG_MAXFS Paging memory per application server<br />rdisp/ROLL_SHM Roll memory per application server<br />rdisp/ROLL_MAXFS Roll memory per application server<br />abap/heap_area_dia Heap quota for dialog work process<br />abap/heap_area_nondia Heap quota for non-dialog work process<br />abap/heap_area_total Heap quota for entire SAP application server<br />The memory requirement of the SAP System is met almost entirely by temporary memory. The main consumers are internal buffers (including PXA, table buffer, paging buffer, extended memory) with a fixed share and SAP Memory Management (roll memory, private memory) with a variable share that depends on the number of users logged on and the type of application (direct memory requirement).</p>\r\n<p><strong>6.2. Monitoring the Memory Consumption in transactions ST02 and SM04</strong></p>\r\n<p>We recommend that you monitor the memory consumption in your system using transactions ST02 and SM04. If you have an application that requires more than 250 MB memory, you should analyze the application using transaction SE30.<br />If individual users occupy very large amounts of memory, this can affect the overall performance of your system.</p>\r\n<p><strong>6.3. No Free SAP Memory</strong></p>\r\n<p>A runtime error occurs: TSV_TNEW_INDEX_NO_ROLL_MEMORY, SORT_EXTRACT_INDEX_NO_ROLL, STORAGE_PARAMETERS_WRONG_SET, TSV_INDEX_INDEX_NO_ROLL_MEMORY.<br />Check your parameter settings (see point 3) and, if necessary, increase the values of em/initial_size_MB, ztta/roll_extension, abap/heap_area_dia, abap/heap_area_nondia or abap/heap_area_total.</p>\r\n<p><strong>7. Changing Profile Parameters</strong></p>\r\n<p><br />Change the parameters using transaction RZ10, and check the parameter changes when you save. If you have to change the parameters directly in the IFS for some reason, check the changes with SAP program SAPPFPAR to avoid unpleasant surprises when starting the SAP instance.</p>\r\n<p><strong>8. Heap memory for Java processes</strong></p>\r\n<p><br />In an ABAP stack, you can use the settings in the SAP instance profile to restrict the maximum amount of memory used in the system. Special tools are available for configuring the Java processes of a Java add-in or of a Java-only instance. The Java parameter \"Xmx\", maximum heap size, determines how much memory is available for the individual Java processes. There are some differences in this regard, depending on the Java Virtual Machine (JVM) that is used.<br /><br />Note that in each case, Java processes can, in principle, end up in a problematic state as soon as insufficient physical memory is available and the system reacts by paging out the Java main memory. Therefore, we recommend that you use memory pools to avoid such situations.<br /><br />Note 1023092 describes how to configure memory pools and provides you with information about where to find out more about individual JVMs.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D042520)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (C5003021)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "923610", "RefComponent": "FS-BA", "RefTitle": "Memory parameter recommendations for banking systems", "RefUrl": "/notes/923610"}, {"RefNumber": "878723", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: ST02 does not show roll area", "RefUrl": "/notes/878723"}, {"RefNumber": "789477", "RefComponent": "BC-CST-MM", "RefTitle": "Large extended memory on AIX (64-bit) as of Kernel 6.20", "RefUrl": "/notes/789477"}, {"RefNumber": "1670425", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: System start or APYSIDKR<PERSON> terminates with MCH3601", "RefUrl": "/notes/1670425"}, {"RefNumber": "1123501", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Operating system users for SAP on IBM i", "RefUrl": "/notes/1123501"}, {"RefNumber": "1023092", "RefComponent": "BC-OP-AS4-JSE", "RefTitle": "IBM i: Using Separate Main Storage Pools for Java Processes", "RefUrl": "/notes/1023092"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3156866", "RefComponent": "BC-CST", "RefTitle": "Using Kernel 7.54 instead of Kernel 7.40, 7.41, 7.42, 7.45, 7.49 or 7.53", "RefUrl": "/notes/3156866 "}, {"RefNumber": "1998233", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: SHM2_EsInit: invalid argument ES/SHM_MAX_SHARED_SEGS", "RefUrl": "/notes/1998233 "}, {"RefNumber": "1123501", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Operating system users for SAP on IBM i", "RefUrl": "/notes/1123501 "}, {"RefNumber": "1636252", "RefComponent": "BC-CST", "RefTitle": "Installing a 7.20 kernel in SAP Web AS 7.00/7.01/7.10/7.11", "RefUrl": "/notes/1636252 "}, {"RefNumber": "878723", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: ST02 does not show roll area", "RefUrl": "/notes/878723 "}, {"RefNumber": "923610", "RefComponent": "FS-BA", "RefTitle": "Memory parameter recommendations for banking systems", "RefUrl": "/notes/923610 "}, {"RefNumber": "1670425", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: System start or APYSIDKR<PERSON> terminates with MCH3601", "RefUrl": "/notes/1670425 "}, {"RefNumber": "1023092", "RefComponent": "BC-OP-AS4-JSE", "RefTitle": "IBM i: Using Separate Main Storage Pools for Java Processes", "RefUrl": "/notes/1023092 "}, {"RefNumber": "789477", "RefComponent": "BC-CST-MM", "RefTitle": "Large extended memory on AIX (64-bit) as of Kernel 6.20", "RefUrl": "/notes/789477 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "KRNL32NUC", "From": "6.40", "To": "6.40EX2", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.00", "To": "7.01", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.10", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "6.40", "To": "6.40EX2", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.00", "To": "7.01", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.10", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "6.40", "To": "6.40EX2", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.00", "To": "7.01", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.10", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.22", "To": "7.22", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.22EXT", "To": "7.22EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "6.40", "To": "6.40EX2", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.00", "To": "7.01", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.10", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.22", "To": "7.22", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.22EXT", "To": "7.22EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.53", "To": "7.53", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "6.40", "To": "6.40", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.00", "To": "7.01", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.10", "To": "7.11", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.20", "To": "7.22", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.53", "To": "7.53", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.54", "To": "7.54", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP GUI FOR WINDOWS 7.20 CORE", "SupportPackage": "SP014", "SupportPackagePatch": "000014", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200014751&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT", "SupportPackage": "SP027", "SupportPackagePatch": "000027", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT UNICODE", "SupportPackage": "SP027", "SupportPackagePatch": "000027", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP027", "SupportPackagePatch": "000027", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP027", "SupportPackagePatch": "000027", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT", "SupportPackage": "SP027", "SupportPackagePatch": "000027", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021283&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT UC", "SupportPackage": "SP027", "SupportPackagePatch": "000027", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021284&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP027", "SupportPackagePatch": "000027", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP027", "SupportPackagePatch": "000027", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}