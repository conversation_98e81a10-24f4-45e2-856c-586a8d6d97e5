{"Request": {"Number": "1410237", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 454, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016929642017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001410237?language=E&token=A2395A93FF781B4865CDA4B12E4A1C6F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001410237", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001410237/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1410237"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.05.2010"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-LC-CH"}, "SAPComponentKeyText": {"_label": "Component", "value": "Switzerland"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Real Estate Localization", "value": "RE-FX-LC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Switzerland", "value": "RE-FX-LC-CH", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC-CH*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1410237 - Central note for REAL ESTATE (CLASSIC & FX) Swiss spec."}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>For RE Classic, different localizations for Switzerland are available in the customer namespace. In the standard programs of REMICL for the migration of Classic RE to RE-FX, these Swiss localizations are not taken into account. The following documentation displays which methods are available in which areas, and where customer-specific adjustments have to be made.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Swiss localization<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Subject areas<br />Fuel consumption calculation:<br />- Fuel levels<br />- VAT adjustment<br />Individual heating costs:<br />- Master data migration (heating system to master settlement unit<br />- Reconciliation of data medium with settlement companies<br />Rent adjustment<br />- Calculation bases for rent adjustment according to Swiss law<br />Lease-in with ISR<br />- Summarization of items for each real estate contract<br />Migration support is missing within REMICL<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Fuel consumption:<br />In the fuel consumption calculation for RE Classic, initial fuel levels and ending levels are managed and valuated according to FiFo. Two different tables are available for this in RE Classic, one for managing the warehouse stock and one for managing the related tax information.<br />The tax information corresponds to the dominant occupancy in the relevant rental objects (option rate) at the time of the input tax reduction. In addition, the stock concerns a composite rate that is difficult to reproduce and requires a large amount of historical data. The value-added tax settlement with the tax authorities created at that time must not be affected either.<br />The initial fuel level and any existing additional fuel purchases can be transferred from RE Classic to the new environment of RE-FX with the related tax information. After this one-time action, it must be possible to process all further fuel additions and removals in relation to value-added tax in the standard system.<br />It must be taken into account that the value-added tax to be transferred has already been settled with the tax authorities and this one-time transfer does not negatively affect the following heating expenses settlements and service charge settlements.<br />Fuel level: Note 1392298 Swiss specifications:&#x00A0;&#x00A0; Migration of fuel level<br />Value-added tax for fuel level: According to customer tests, no note was required for this subject,<br /><br />Individual heating expenses settlement<br />The individual heating expenses settlement for Switzerland was implemented in RE Classic with an enhancement of the standard system in the customer namespace. The master data required for this was added to the heating system. In the localization of individual heating costs for Switzerland, the master settlement unit was used instead of the heating system as the central master data element in the standard system of RE-FX. The migration programs of REMICL no longer convert the data of the heating system into the master data of the master settlement units. For the conversion according to Swiss law, no support is available in REMICL. This must be implemented in the customer project.<br />The data medium with the settlement companies was reconciled with the following settlement companies in Switzerland and was tested with two RE customers in Switzerland:<br />RAPP, W&#x00E4;rmetechnik AG, Basel<br />ista swiss ag, Zofingen<br />neoVac AG, Oberriet<br />Techem Schweiz, Urdorf<br /><br />Rent adjustment:<br />The rent adjustment data according to Swiss law for the master data of rental units and lease-outs is not transferred to RE-FX by the migration with REMICL.<br />You can use the transactions listed below to subsequently perform the migration of&#x00A0;calculation bases after the successful execution of REMICL:<br />1. Transaction REXCAJMIROCH01: RO Migration: Adj. Data - Swiss Law, Note 1359007<br />2. Transaction REXCAJMICNCH01: LO Migration: Adj. Data - Swiss Law, Note 1398898<br /><br />Lease-in with ISR reference (Note 920728)<br />In the Swiss localization, it is possible to enter the ISR reference of the landlord's invoice for the lease-in for the relevant items in the cash flow.<br />For this reason, you want invoiced amounts with the same ISR reference to be grouped in one payment for Real Estate management, so that there are separate payments for different ISR numbers only. You want items that were posted under the same real estate contract to be grouped like this. This summarization of items within the lease-in with ISR reference was implemented with Note 920728. As a result, the landlord receives only one amount for each contract and ISR reference due to the periodic posting of the real estate contract.<br /><br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I020835)"}, {"Key": "Processor                                                                                           ", "Value": "I024033"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001410237/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001410237/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001410237/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001410237/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001410237/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001410237/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001410237/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001410237/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001410237/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "828160", "RefComponent": "RE-FX", "RefTitle": "Migration from Classic RE to RE-FX", "RefUrl": "/notes/828160"}, {"RefNumber": "771098", "RefComponent": "RE-FX-LC", "RefTitle": "Countries: Release restriction RE-FX", "RefUrl": "/notes/771098"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "771098", "RefComponent": "RE-FX-LC", "RefTitle": "Countries: Release restriction RE-FX", "RefUrl": "/notes/771098 "}, {"RefNumber": "828160", "RefComponent": "RE-FX", "RefTitle": "Migration from Classic RE to RE-FX", "RefUrl": "/notes/828160 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}