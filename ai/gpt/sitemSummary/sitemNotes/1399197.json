{"Request": {"Number": "1399197", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 552, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016892242017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001399197?language=E&token=9582A85901D2A9F21354C4B16062453C"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001399197", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001399197/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1399197"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 11}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.08.2018"}, "SAPComponentKey": {"_label": "Component", "value": "IS-CWM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Catch Weight Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Catch Weight Management", "value": "IS-CWM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-CWM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1399197 - Important information about activating SAP CWM with EHP5"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You want to activate SAP Catch Weight Management (CWM) with Enhancement Package 5 for SAP ERP 6.0.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>IS-CWM, Catch Weight Management, activation, restriction list, EHP5, Enhancement Package 5, /CWM/CM_2</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Generally, you can use transaction SFW5 (\"Switch Framework Customizing\") to activate SAP Catch Weight Management in an existing ERP 6.0 system. The relevant business function set is \"CWM BFS\", the CWM EHP5 business function is \"/CWM/CM_2\".<br />However, you MUST consider the following with regard to activation.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><UL><LI>Project character:<br />Activating Catch Weight Management for the first time must be well considered and requires the relevant planning and procedure. In particular, make sure that you use the correct procedure to implement the new materials (CW materials).</LI></UL> <UL><LI>You cannot reverse the activation.<br />Note that this activation is a cross-client process and that you cannot reverse the specification of the ERP system as SAP CWM. If you want to deactivate IS-CWM, you must reset the entire system on the basis of a relevant backup.</LI></UL> <UL><LI>You can select only one business function set:<br />In addition, note that SAP CWM is delivered as a business function set and that you can usually select only one business function set in a system. Therefore, you cannot activate CWM with Industry Business Solutions.</LI></UL> <UL><LI>Restriction list:<br />Note that activating CWM results in certain functions and processes of the core ERP no longer being supported. For detailed information about the restrictions, see the document \"CWM_605_Restrictions.pdf\" (see the attachment; this is available only in English).<br />If you want to activate Catch Weight Management in an ERP system that is already live, this means that data that belongs to the processes mentioned above must have a closed status and should ideally be archived. A subsequent access to this data is no longer possible or may result in relevant error situations.</LI></UL> <UL><LI>SAP ERP is already live; CWM is not activated yet:<br />We strongly recommend that you ensure that processes that are supported in CWM also have a closed status. In general, this affects all processes that are related to goods movements, that is, production orders, process orders, purchase processes, and deliveries.<br />You can also activate CWM if master data and movement data already exists in your system. To transfer this, you must copy the contents of the \"Base Unit of Measure\" field into the new \"Valuation Unit of Measure\" CWM field. To do this, call transaction /CWM/TCWM and choose \"Menu -&gt; Edit -&gt; Set /CWM/VALUM\". These new stock fields are initialized automatically together with the setting of this field.</LI></UL> <UL><LI>CWM already active with the old architecture (business function \"/CWM/BF\"):<br />If you are already using SAP ERP with active SAP CWM based on the old architecture (Enhancement Package 3 or lower) and if you want to upgrade to SAP CWM based on Enhancement Package 5 while retaining the dataset, a migration is required. For more information, see Note 1450225.</LI></UL> <UL><LI>CWM already active with the new architecture (business function \"/CWM/CM_1\"):<br />If you are already using SAP ERP with active SAP CWM based on the new architecture (as of Enhancement Package 4), you can activate the Enhancement Package 5 business function \"/CWM/CM_2\" directly. Since new fields are introduced to stock tables during activation, you must use transaction /CWM/TCWM and choose \"Menu -&gt; Edit -&gt; Initialization EHP5\" to initialize these before goods movements are posted for the first time.</LI></UL> <UL><LI>BC set not active<br />After the activation, if you determine (for example, using transaction MM01) that the CW-specific fields are missing in the material master data, a required BC set may not be able to be activated automatically. You can use Note 1274044 to obtain this manually.<br /></LI></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D043704)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D039489)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001399197/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001399197/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001399197/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001399197/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001399197/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001399197/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001399197/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001399197/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001399197/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "CWM_605_Restrictions.pdf", "FileSize": "55", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000548972009&iv_version=0011&iv_guid=00109B36BC8E1ED8A6F78ED68674E0D6"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1450225", "RefComponent": "IS-CWM", "RefTitle": "SAP CWM: Data Migration from Old to New Architecture", "RefUrl": "/notes/1450225"}, {"RefNumber": "1274044", "RefComponent": "IS-CWM", "RefTitle": "IS-CWM: Reactivating the Switch BC Sets", "RefUrl": "/notes/1274044"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1450225", "RefComponent": "IS-CWM", "RefTitle": "SAP CWM: Data Migration from Old to New Architecture", "RefUrl": "/notes/1450225 "}, {"RefNumber": "1274044", "RefComponent": "IS-CWM", "RefTitle": "IS-CWM: Reactivating the Switch BC Sets", "RefUrl": "/notes/1274044 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-CWM", "From": "605", "To": "605", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}