{"Request": {"Number": "1678321", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 895, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017379812017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=EA0E31136719C209E51F5A2BBFC1F18B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1678321"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "21.06.2013"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX"}, "SAPComponentKeyText": {"_label": "Component", "value": "Flexible Real Estate Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1678321 - EhP6: SEPA mandate management for SAP Real Estate Management"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>As part of the unification of the \"Single Euro Payments Area (SEPA)\" payment transactions throughout Europe, the previous debit memo procedure is replaced by the SEPA mandate.<br />This affects Real Estate contractual relationships, in particular.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SEPA mandate, mandate management, debit memos, Customer Connection, CustomerConnection, CustConn, focus topic, improvement, FAQ, 606, EhP6, Enhancement Package 6</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The existing SAP SEPA mandate management (see SAP Note 1046199) should therefore also be integrated into SAP Real Estate Management.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The SAP module for processing SEPA debit memos was enhanced for Real Estate requirements.<br /><br />The following enhancements have been made for customer activities, in particular:<br /></p> <UL><LI>Managing the SEPA mandate in the posting term for the Real Estate contract</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The posting term can now refer to one of the SEPA mandates that exist for the selected bank details.<br />As a result, you can use a condition to control which mandate is supposed to be used to process the payments.</p> <UL><LI>Integrating the SEPA mandate into the payment data for one-time postings</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This means that you can control which mandate is supposed to be used to process the payments individually for one-time postings.</p> <UL><LI>Enhancing the bank details for the SAP business partner</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SEPA mandates are entered either for the customer or for the contract account (FI-CA). It is not possible to enter a SEPA mandate for the business partner in the standard system.<br />Therefore, an enhancement was made to the maintenance of the business partner (in the same way as for FI-CA) for Real Estate. For business partners in customer roles or the role MKK (contract account), the relevant mandates can now be entered directly in the bank details.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The advantage of this is that the mandates can be defined as part of the contract entry.</p> <UL><LI>Storing the SEPA mandate in the RE document</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;During the execution of the periodic postings and the one-time postings, the system checks whether a mandate is defined for the condition to be posted. This mandate is then saved in the RE document.</p> <UL><LI>Integration to the payment program</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For SEPA payment methods and documents that were generated from RE, the payment program checks whether a mandate is assigned in the contract. If this is the case, the mandate is used for entering the receivable. The executed payment is then assigned to the SEPA mandate. Therefore, you see all executed payments in the data for the SEPA mandate.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br />Note that RE-FX does not support the \"Contract Category\" (REF_TYPE) and \"Contract ID\" (REF_ID) fields of the SEPA mandate master record. SEPA mandates, in which these fields are set, cannot be defined in the posting term of the real estate contract and cannot be used for one-time postings (RERAOP). In particular, this applies if you use customer-specific contract categories as described in SAP Note 1835738.<br /><br />For more information about SEPA mandate management in SAP Real Estate Management, see the attachments to this SAP Note.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "RE-FX-RA (Rental Accounting)"}, {"Key": "Improvement Note", "Value": "Yes"}, {"Key": "Responsible                                                                                         ", "Value": "D030839"}, {"Key": "Processor                                                                                           ", "Value": "D030839"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "SEPA_Real_Estate_EN.pdf", "FileSize": "893", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000091542012&iv_version=0009&iv_guid=33486768BCA5EF49814086FC69AB21E0"}, {"FileName": "SEPA_Real_Estate_DE.pdf", "FileSize": "789", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000091542012&iv_version=0009&iv_guid=421E2B93E2A236478B8A57EB4CEBDEFE"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "517673", "RefComponent": "RE-FX", "RefTitle": "Flexible Real Estate: Functions and restrictions", "RefUrl": "/notes/517673"}, {"RefNumber": "1880251", "RefComponent": "RE-FX-RA", "RefTitle": "RE-FX: Update of SEPA mandate in FI document", "RefUrl": "/notes/1880251"}, {"RefNumber": "1770122", "RefComponent": "RE-FX", "RefTitle": "Service: SEPA für SAP Real Estate Management", "RefUrl": "/notes/1770122"}, {"RefNumber": "1759170", "RefComponent": "RE-FX-RA", "RefTitle": "SEPA Composite SAP Note for SAP Real Estate Management", "RefUrl": "/notes/1759170"}, {"RefNumber": "1716842", "RefComponent": "RE-FX-RA", "RefTitle": "Selectn of SEPA mandate in payment run from RE posting term", "RefUrl": "/notes/1716842"}, {"RefNumber": "1710095", "RefComponent": "RE-FX-RA", "RefTitle": "SEPA mandate search help in posting term for FI-CA", "RefUrl": "/notes/1710095"}, {"RefNumber": "1709884", "RefComponent": "RE-FX-RA", "RefTitle": "Search help for SEPA mandate in posting term and RERAOP", "RefUrl": "/notes/1709884"}, {"RefNumber": "1707539", "RefComponent": "RE-FX-RA", "RefTitle": "New BAdI BADI_REEX_FI_PAYING for selection of SEPA mandate", "RefUrl": "/notes/1707539"}, {"RefNumber": "1705718", "RefComponent": "RE-FX-BP", "RefTitle": "Real Estate: SEPA mandate for business partner", "RefUrl": "/notes/1705718"}, {"RefNumber": "1699099", "RefComponent": "XX-PROJ-FI-CA", "RefTitle": "PMF: SEPA in Release 4.64 (composite SAP Note)", "RefUrl": "/notes/1699099"}, {"RefNumber": "1046199", "RefComponent": "FI-AR-AR-N", "RefTitle": "Collective note: Support of SEPA mandates and formats in FI", "RefUrl": "/notes/1046199"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1759170", "RefComponent": "RE-FX-RA", "RefTitle": "SEPA Composite SAP Note for SAP Real Estate Management", "RefUrl": "/notes/1759170 "}, {"RefNumber": "1046199", "RefComponent": "FI-AR-AR-N", "RefTitle": "Collective note: Support of SEPA mandates and formats in FI", "RefUrl": "/notes/1046199 "}, {"RefNumber": "1880251", "RefComponent": "RE-FX-RA", "RefTitle": "RE-FX: Update of SEPA mandate in FI document", "RefUrl": "/notes/1880251 "}, {"RefNumber": "1705718", "RefComponent": "RE-FX-BP", "RefTitle": "Real Estate: SEPA mandate for business partner", "RefUrl": "/notes/1705718 "}, {"RefNumber": "1770122", "RefComponent": "RE-FX", "RefTitle": "Service: SEPA für SAP Real Estate Management", "RefUrl": "/notes/1770122 "}, {"RefNumber": "1699099", "RefComponent": "XX-PROJ-FI-CA", "RefTitle": "PMF: SEPA in Release 4.64 (composite SAP Note)", "RefUrl": "/notes/1699099 "}, {"RefNumber": "517673", "RefComponent": "RE-FX", "RefTitle": "Flexible Real Estate: Functions and restrictions", "RefUrl": "/notes/517673 "}, {"RefNumber": "1716842", "RefComponent": "RE-FX-RA", "RefTitle": "Selectn of SEPA mandate in payment run from RE posting term", "RefUrl": "/notes/1716842 "}, {"RefNumber": "1663018", "RefComponent": "RE-FX", "RefTitle": "Composite SAP Note for Customer Connection Real Estate 2011/2012", "RefUrl": "/notes/1663018 "}, {"RefNumber": "1710095", "RefComponent": "RE-FX-RA", "RefTitle": "SEPA mandate search help in posting term for FI-CA", "RefUrl": "/notes/1710095 "}, {"RefNumber": "1709884", "RefComponent": "RE-FX-RA", "RefTitle": "Search help for SEPA mandate in posting term and RERAOP", "RefUrl": "/notes/1709884 "}, {"RefNumber": "1707539", "RefComponent": "RE-FX-RA", "RefTitle": "New BAdI BADI_REEX_FI_PAYING for selection of SEPA mandate", "RefUrl": "/notes/1707539 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "606", "To": "606", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}