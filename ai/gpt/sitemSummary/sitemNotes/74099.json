{"Request": {"Number": "74099", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 361, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000222102017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000074099?language=E&token=851EC75A8CC9F5DF655C5C4E87321440"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000074099", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000074099/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "74099"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 30}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.08.1999"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-TH"}, "SAPComponentKeyText": {"_label": "Component", "value": "Thailand"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Thailand", "value": "XX-CSC-TH", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-TH*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "74099 - Additional Info: Installation Thai Version 30D/1"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Installation Thai R/3 30D/1 Country Version:</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Thai, Thailand, Country Version, Enhancement, upgrade,<br />Localization Thailand</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Hot News for Thai Country Version 30D/1 not contained in<br />Thai 30D/1 installation guide and on CDROM,<br />errors detected after release of Thai 30D/1 version.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p> Contents.<br /><br />I/&#x00A0;&#x00A0; Miscellaneous<br /><br />II/&#x00A0;&#x00A0;Installation Thai 3.0D/1<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1.) R/3 3.0D/1 Standard Installation<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2.) R/3 3.0D/1 Kernel Patches<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 3.) R/3 3.0D/1 Hot-Packages<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4.) Thai&#x00A0;&#x00A0;Codepage Installation<br /><br />III/ Important Release Notes<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1.) Financial Accounting<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2.) Asset Accounting<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;3.) Controlling<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4.) Material Management<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5.) Sales and Distributions<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 6.) SD/MM View maintenance<br /><br />IV/ Handling of SAP Hot Packages<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1.) CRT for Hot packages 3.0D/9 #20-67<br /><br />I. Miscellaneous<br />----------------<br />The Thai R/3 country version 30D/1 is an additional software-package<br />which is not contained in the SAP standard software CDROMS and which<br />is delivered on a separate CDROM incl. documentation. It is delivered<br />by SAP Thailand, Tel.: +66-2-631-1800<br />In general, The Thai 30D/1 country version consists of the<br />following components:<br />&#x00A0;&#x00A0;1. Functional part (reports, tables, etc) realizing the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;legal requirements in Thailand<br />&#x00A0;&#x00A0;2. Customizing providing template settings for Thai customers<br />&#x00A0;&#x00A0;3. Thai language<br /><br />Several important prerequisites have to be fulfilled<br />in order to apply the Thai country version 30D/1:<br /><br />1. The R/3 system must run in release 30D/1, i.e. 30D including<br />&#x00A0;&#x00A0;ALL hotpackages nr. 30D 1-13 (available on separate CDROM<br />&#x00A0;&#x00A0;automatically sent to all 30D customers)<br />&#x00A0;&#x00A0; If further hotpackages are applied please read section IV.<br /><br />2. Platforms:<br />2.1 Operating System:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;The underlying platform (operating system) must support<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Native Language Support (NLS) for Thai;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;at present, the following operating systems support this:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;- HP-UX 10.x, AIX 4.x, Solaris 2.4, Digital-UNIX 3.2x<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;- For Reliant-UNIX (SINIX) you should contact SNI-CC<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;- all other operating systems released for standard R/3 are<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; NOT suited for Thai. For more details contact SAP.<br /><br />2.2&#x00A0;&#x00A0;Database:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;At present, the following R/3 database have been tested for<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Thai R/3 version:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;- ORACLE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;- INFORMIX<br /><br />3. In order to use the Thai language in R/3 with Thai country version<br />&#x00A0;&#x00A0;you should use the following frontend software:<br />&#x00A0;&#x00A0; - Microsoft Windows for Workgroups 3.11, Thai Edition<br />&#x00A0;&#x00A0; - MS Windows 95, Thai Edition<br />&#x00A0;&#x00A0; - R/3 SAPGUI 30F or higher<br />&#x00A0;&#x00A0;- other frontend combinations are currently not supported<br />&#x00A0;&#x00A0; - frontend and R/3 application server should be configured<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;for Thai language environment and codepage (cf. note 45463)<br /><br />4. The Thai country version can ONLY be used for English and Thai<br />&#x00A0;&#x00A0;languages together. If Thai country version should be used in<br />&#x00A0;&#x00A0;English only, please contact SAP.<br />&#x00A0;&#x00A0; Other language combinations are currently NOT supported !<br /><br />5. R/3&#x00A0;&#x00A0;Printing in Thai:<br />&#x00A0;&#x00A0;The following printers/pront methods are supported:<br />&#x00A0;&#x00A0;- R/3 Host Spool Printing for ESCP2 (Epson-compatible) printers<br />&#x00A0;&#x00A0;- Printing via PC Print Server running SAPLPD and<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;MS Wfw 3.11/Win 95, Thai Edition<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Refer to Thai 30D Installation Guide for details<br /><br /><br />II/&#x00A0;&#x00A0;Installation Thai 3.0D/1<br />----------------------------<br />1.) R/3 3.0D/1 Standard Installation<br />a) do NOT apply 4.2.4, section k (NO patch from Thai CDROM)<br /><br />&#x00A0;&#x00A0;Instead, please import the following important corrections/patches<br />&#x00A0;&#x00A0;DIRECTLY after the Thai 30D/1 installation.<br />&#x00A0;&#x00A0; You find all the patch files on sapserv3/5/7 in the directory<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/general/R3server/abap/note.0074099.<br />&#x00A0;&#x00A0;Please refer to attached note 13719 for importing them.<br />&#x00A0;&#x00A0; There are the following patches:<br />&#x00A0;&#x00A0; -&#x00A0;&#x00A0;Patch installation&#x00A0;&#x00A0; SAPKP1H308 (files RP1H308.SAP, KP1H308.SC4)<br />&#x00A0;&#x00A0;-&#x00A0;&#x00A0;Hotpackage translation patch 30D/1 : SC3K000023 (R000023.SC3,<br /><br />Import with:<br />login &lt;sid&gt;adm<br />cd /usr/sap/trans/bin<br />For all patches:<br />tp addtobuffer &lt;request&gt;&#x00A0;&#x00A0;&lt;SID&gt;<br /><br />&#x00A0;&#x00A0;tp import &lt;request&gt; &lt;SID&gt; client000 U48&#x00A0;&#x00A0;&#x00A0;&#x00A0; (if 2. time: U148)<br /><br />NOTE: If you have already copied client 000 to your Thai working client<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BEFORE this patch, proceed as follows:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- import SAPKP1H308 into client 000 as described above<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- login to working client as normal user<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- call transaction SCC1:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;source client:&#x00A0;&#x00A0;000<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;transport:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKP1H308<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Test run:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; x<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- Execute : Then you will see a table statistics:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you only see \"Inserted\" then new entries would<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;be imported; if you see \"Delete\" there are already<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;entries in thos tables in your working client<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;and you have carefully to check if they can be<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;overwritten.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;* In case of only new entries, run same prog without \"Test run\".<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; * If not clear situation use btter the manual table comparison<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(SCU0) and create single transports from client 000 to<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;your working client.<br /><br />b) After Thai 30D/1 installation please check table<br />&#x00A0;&#x00A0; TVFK: If status \"partially active\"&#x00A0;&#x00A0;shown, please activate<br />&#x00A0;&#x00A0;this table manually via transaction SE11 --&gt; Table --&gt; Activate<br /><br />2. Merging existing client with Thai version:<br />&#x00A0;&#x00A0;Please refer to CSP note 79405 and replace country \"Korea\" by<br />&#x00A0;&#x00A0; \"Thailand\".<br /><br />III/ Important Release Notes<br />----------------------------<br />In the following you find important hints about corrections in<br />Thai 30D/1 country version. All mentioned patches are contained in<br />transport orders which you find in the OSS servers<br />sapserv3/4/5 in directory: general/R3server/abap/note.0074099.<br />Please refer to OSS note 13719 describing how to import the<br />corrections into your R/3 system.<br /><br />In general, it is recommended to import all listed corrections below in<br />order to get a complete set of corrections of Thai version 30D/1.<br />If you have already changed/modified listed objects below please save<br />those objects and be careful with import of the corrections.<br /><br /><br />--------------------------- FI ------------------------------<br />1.) Financial Accounting:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Please import transport request SC3K000066<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;if you have any problems in the following topics:<br /><br />a) Withholding tax<br />&#x00A0;&#x00A0; - Special Account Tax Deduction and Remittance program [J_1HSPAR]:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; cannot find any record when run this program due to<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;a date field storing in database.<br /><br />&#x00A0;&#x00A0; - Error in Thai Witholding Tax Report Number 3/53 [J_1HWTRE]<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;in translation of text elements (Thai 30D ONLY):<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- Logon language '2'<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- Use TCODE 'SE38' and enter program name \"J_1HWTRE\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- Choose text elements<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- Retranslate text symbol 701<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- change wording&#x00A0;&#x00A0;\"RAI KARN SIN SUB\" to \"CHUE\", i.e.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;use only 5 characters in Thai langvuage<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- Then save and generate.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&#x00A0;&#x00A0;Withholding tax report 3/53 cannot be separated by branch<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;if users structire it as 4,0:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Change in program MJ1HTF01 the following lines:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Line number 818<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;when : 4,others&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&lt;&lt;&lt;&lt; Delete<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;when others.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&lt;&lt;&lt;&lt; Insert<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;- No/wrong printer check for print withholding tax certificates<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;[J_1HWTCR]:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Please apply corrections marked by \"23.09.97\" in<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;section \"corrections\" for J_1HWTCR (YYYYYYYYY)<br />b) Value Added Tax (VAT)<br />&#x00A0;&#x00A0;- Input VAT report [J_1HITAX]:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; * In case that the periods of calendar year do not equal to<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; the ones of fiscal year, the program will print a wrong month.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; * In case of Average VAT with account key NVV, if one of VAT<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; rate has been changed, the new condition record in table KONP<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; will be created, but the old one will not be deleted.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; When running this report,the VAT base amount will be wrong.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; * In case of petty cash, this report will print a petty<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; cashier's name, not a vendor name.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; * In case of down payment, this report does not show any sign in<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;base amount and also VAT amount.<br /><br />&#x00A0;&#x00A0;- Output VAT report [J_1HOTAX]:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; * In case that the periods of calendar year do not equal to<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; the ones of fiscal year, the program will print a wrong month.<br /><br />c) There is an additional topic [Cautions in withholding] in hypertext &#x00A0;&#x00A0; for Thailand [J_1H_TH_BOOK]:<br />&#x00A0;&#x00A0; !!! Please read it before you use Thai Localization !!!<br /><br /><br />--------------------------- AM&#x00A0;&#x00A0;(FI-AA) ------------------------------<br />2.) Asset Accounting:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Please import a transport request SC3K000066<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;if you get the following problems:<br /><br />a) Depreciation List (J_1HDEPR):<br />&#x00A0;&#x00A0;This program will print planned values, not actual values.<br /><br />b) Asset Registration List (J_1HREGL):<br />&#x00A0;&#x00A0;This program will print planned values, not actual values.<br /><br />c) Asset Inventory List (J_1HASSI):<br />&#x00A0;&#x00A0;This program will print planned values, not actual values.<br /><br />--------------------------- CO ------------------------------<br />3.) Controlling:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Please correct manually<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;if you get the following problems:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Production Cost report [J_1HCO001] and Cost of Goods<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Sold [J_1HCO002] (in Report Painter Library J11):<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;* In case that you allocate costs in CO module, you need to add<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;a characteristic value '2' in record type of each report row.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Use Report Writer transaction GR22 for that.<br /><br />--------------------------- MM ------------------------------<br />4.) Material Management:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Please import transport request SC3K000066<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;if&#x00A0;&#x00A0;you get the following problems:<br /><br />a) Stock Balance Report (J_1HSTOK) : this report cannot display<br />&#x00A0;&#x00A0; summary by branch.<br /><br />b) Inventory and Raw Material Report (J_1HIMAT) :<br />&#x00A0;&#x00A0;This report cannot sort by document date,<br />&#x00A0;&#x00A0; document time, document number.<br /><br />c) Stock Card Report (J_1HSTCD) :<br />&#x00A0;&#x00A0; * This report cannot sort by document date, document time,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; document number.<br />&#x00A0;&#x00A0; * There is an error message \"Syntax error in program\" when<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; using maintenance view \"J_1HT001LV\" by transaction \"SM30\" to<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; maintain vendor code (consignor) for each storage location.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Please see details in 6) SD/MM View Maintenance.<br /><br />&#x00A0;&#x00A0; * There is an error in Thai 30D Stock card report corrected<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;in Thai version 30F in \"Description of Plant\".<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; You find the advabced correction in 30D below in section<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"corrections\" of this notw&#x00A0;&#x00A0;for J_1HSTCD (XXXXXXXXXX).<br /><br />d) Freight : for periodic method<br />&#x00A0;&#x00A0;- When posting an invoice, it updates moving average price in<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; material master.<br />&#x00A0;&#x00A0; - Customer requires to get password from SAP AG for activating<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;material ledger (Add in hypertext : J_1H_MM_FRGT)<br /><br />e) Physical Inventory : for periodic method<br />&#x00A0;&#x00A0;- When posting physical inventory document, it updates moving<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;average price in material master.<br />&#x00A0;&#x00A0; - Customer requires to get password from SAP AG for activating<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;material ledger (Add in hypertext : J_1H_MM_PHYS)<br /><br /><br />5.) Sales and Distributions<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Please import transport request SC3K000066<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;if&#x00A0;&#x00A0;you get the following problems:<br /><br />5.1) Invoice split routine 007 - Inv.Split(Thailand) (FV60C007)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Routine no. 007 using for execute billing document. This routine<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; perform split delivery document into billing document seperated by<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; branch, tax code and vendor consignment.<br /><br />5.2) Free of Charge processing.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Movement type 990 patched is serve for Free of Charge processing.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; However, movement type 901 in Thai vers. 3.0D/1 should be deleted.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;because 901 is reserved for customer name range.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;This patch provides customizing of<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; a) Deletion of movement 901. Please check that you are not using<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;this movement type 901; otherwise copy and rename it.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;b) New movement type 990.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; c) Change of view V_158B_VC for allowed transaction conditions.<br /><br /><br />--------------------------- SD ------------------------------<br />6.) SD/MM View Maintenance:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Please import transport request SC3K000065 and SC3K000066:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;if&#x00A0;&#x00A0;you get the following problems:<br /><br />&#x00A0;&#x00A0; The new view maintenance in 3.0D - Thai version is J_1HT001LV.<br />&#x00A0;&#x00A0; This view is has source code in same module as old J_1HTVFKV in 3.0B<br />&#x00A0;&#x00A0; Thai version. Because of error when regenerate maintenance screen<br />&#x00A0;&#x00A0; with program RSVIEWGN after installation or upgrading, source code<br />&#x00A0;&#x00A0;has been changed and syntax error occured. These patches contain<br />&#x00A0;&#x00A0; correction program as follows:<br /><br />6.1) SD programs are:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;a) J_1HCRDR - Credit/Debit note printing program<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; b) J_1HINVO - Invoice printing program<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;c) FV60C007 - Split invoice routine no. 007 - Inv.Split (Thailand)<br /><br />6.2) MM program is only a stock card report J_1HSTCD<br /><br />6.3) Correction of view definition and view type determination<br /><br /><br />IV/&#x00A0;&#x00A0;Handling of SAP Hot Packages:<br />---------------------------------<br />The Thai 30D/1 R/3 country version automatically includes all<br />SAP 30D hot packages (HP) nr. 1-13 all contained on the SAP<br />Hot Package CDROM 30D/1 delivered to all customers.<br />Applzing further SAP 30D HP &gt;13 requires some additional adjustments<br />for Thai version. We recommend the customers with Thai version<br />30D to follow the described procedures:<br /><br />1. Hot Package CDROM 30D/2 (HP nr. 14-19):<br />&#x00A0;&#x00A0;Please import all HP nr. 14-19 together as described in<br />&#x00A0;&#x00A0;enclosed document \"Applying Hot Package Collection 3.0D/2\",<br />&#x00A0;&#x00A0;material nr. 5100 1272 and recommened OSS notes. If you<br />&#x00A0;&#x00A0; made some own modifications of SAP standard objects OR<br />&#x00A0;&#x00A0;Thai 30D/1 objects please follow OSS note 60419 BEFORE import.<br /><br />&#x00A0;&#x00A0;After completion please check:<br />&#x00A0;&#x00A0; - FI-AA Thai user exit program ZXAFAU01, headline THLOC30D there<br />&#x00A0;&#x00A0; - FI-AA base values ZT, ZU: Call SE12, enter BEZWKZ, select<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; \"domain\", select \"values\": Check if entries exist:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; ZT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Limited base value with net book value<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;ZU&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Interest base value<br /><br />2. Hot Package CDROM 30D/9 (HP nr. 20-67):<br />&#x00A0;&#x00A0;The CRT for HP nr.20-67 was placed on sapserv3:<br />&#x00A0;&#x00A0;general/R3server/abap/note.0074099/CRT/KI1HD60.CAR<br />&#x00A0;&#x00A0; Please extract it on /usr/sap/trans directory.<br />&#x00A0;&#x00A0; It'll be extracted to cofiles/KI1HD60.SAP and data/RI1HD60.SAP.<br />&#x00A0;&#x00A0; These files should be imported to the system using by normal tp tool<br />&#x00A0;&#x00A0; instead of SPAM.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D003554)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000074099/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000074099/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000074099/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000074099/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000074099/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000074099/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000074099/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000074099/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000074099/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "96550", "RefComponent": "XX-CSC-TH", "RefTitle": "Patch Correction Thai Version 30D/2", "RefUrl": "/notes/96550"}, {"RefNumber": "88605", "RefComponent": "XX-CSC-TH", "RefTitle": "Additional Functions for Thai Version 30D/2 - Info", "RefUrl": "/notes/88605"}, {"RefNumber": "79405", "RefComponent": "XX-CSC-KR", "RefTitle": "Install. of Korean country vers. in existing client", "RefUrl": "/notes/79405"}, {"RefNumber": "66079", "RefComponent": "XX-CSC-TH", "RefTitle": "R/3 Printing in Thai", "RefUrl": "/notes/66079"}, {"RefNumber": "60419", "RefComponent": "BC-UPG-OCS", "RefTitle": "Display modifications before applying a patch", "RefUrl": "/notes/60419"}, {"RefNumber": "51641", "RefComponent": "XX-CSC-IN", "RefTitle": "Country version India install.in an existing client", "RefUrl": "/notes/51641"}, {"RefNumber": "45463", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/45463"}, {"RefNumber": "43853", "RefComponent": "BC-CTS-LAN", "RefTitle": "Consulting: Language-dependent + client-specific C-tables", "RefUrl": "/notes/43853"}, {"RefNumber": "33525", "RefComponent": "BC-UPG-OCS", "RefTitle": "Important information about SAP patches (< 3.1H)", "RefUrl": "/notes/33525"}, {"RefNumber": "28148", "RefComponent": "BC-CTS-LAN", "RefTitle": "Table category processing during language import", "RefUrl": "/notes/28148"}, {"RefNumber": "17827", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/17827"}, {"RefNumber": "149032", "RefComponent": "XX-CSC-TH", "RefTitle": "HP/CRT status for Thai country version", "RefUrl": "/notes/149032"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "43853", "RefComponent": "BC-CTS-LAN", "RefTitle": "Consulting: Language-dependent + client-specific C-tables", "RefUrl": "/notes/43853 "}, {"RefNumber": "149032", "RefComponent": "XX-CSC-TH", "RefTitle": "HP/CRT status for Thai country version", "RefUrl": "/notes/149032 "}, {"RefNumber": "28148", "RefComponent": "BC-CTS-LAN", "RefTitle": "Table category processing during language import", "RefUrl": "/notes/28148 "}, {"RefNumber": "33525", "RefComponent": "BC-UPG-OCS", "RefTitle": "Important information about SAP patches (< 3.1H)", "RefUrl": "/notes/33525 "}, {"RefNumber": "88605", "RefComponent": "XX-CSC-TH", "RefTitle": "Additional Functions for Thai Version 30D/2 - Info", "RefUrl": "/notes/88605 "}, {"RefNumber": "96550", "RefComponent": "XX-CSC-TH", "RefTitle": "Patch Correction Thai Version 30D/2", "RefUrl": "/notes/96550 "}, {"RefNumber": "79405", "RefComponent": "XX-CSC-KR", "RefTitle": "Install. of Korean country vers. in existing client", "RefUrl": "/notes/79405 "}, {"RefNumber": "60419", "RefComponent": "BC-UPG-OCS", "RefTitle": "Display modifications before applying a patch", "RefUrl": "/notes/60419 "}, {"RefNumber": "51641", "RefComponent": "XX-CSC-IN", "RefTitle": "Country version India install.in an existing client", "RefUrl": "/notes/51641 "}, {"RefNumber": "66079", "RefComponent": "XX-CSC-TH", "RefTitle": "R/3 Printing in Thai", "RefUrl": "/notes/66079 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30D", "To": "30D", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 1, "URL": "/corrins/0000074099/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}