{"Request": {"Number": "891144", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 452, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015972282017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000891144?language=E&token=1559C4FE2B0BAF9793D78CE3659C3142"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000891144", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000891144/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "891144"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 17}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.03.2020"}, "SAPComponentKey": {"_label": "Component", "value": "FI-GL-FL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Flexible Structures"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Accounting", "value": "FI-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Structures", "value": "FI-GL-FL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-FL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "891144 - General Ledger Accounting (new)/document splitting: Risks of subsequent changes"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to make subsequent changes in Customizing for General Ledger Accounting (new). In particular, these changes affect document splitting, which is used in the context of General Ledger Accounting (new).<br />Making changes of this type carries certain risks, it should be noted.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Online splitter, Customizing, General Ledger Accounting (new), document split, NewGL, <span lang=\"EN-IE\" style=\"font-family: 'Calibri','sans-serif'; font-size: 11pt; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-bidi-font-family: 'Times New Roman'; mso-ansi-language: EN-IE; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">GLT0002</span>, <span lang=\"EN-IE\" style=\"font-family: 'Calibri','sans-serif'; font-size: 11pt; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-bidi-font-family: 'Times New Roman'; mso-ansi-language: EN-IE; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">GLT0 002</span></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><br />In contrast with the special ledger or EC-PCA, subsequent changes are not planned in General Ledger Accounting (new) since the ledgers in General Ledger Accounting (new) are not comparable with a special ledger or EC-PCA. In fact, General Ledger Accounting (new) is a general ledger from a business point of view and is therefore legally comparable with the classic general ledger (GLT0 ledger 00). Thus, there is an auditing requirement.<br /><br />This auditing requirement means that, prior to planned changes in Customizing for General Ledger Accounting (new) or with regard to changes in Customizing for document splitting, you must consider the possible business effects in terms of the documents that have already been posted.<br /><br />Such business effects also bring technical restrictions with them, which, viewed in combination, usually forbid many subsequent changes, even if they are not intercepted by error messages in the relevant Customizing path. Through this, you can link your requirement to subsequently implement or change functions in General Ledger Accounting (new) with a migration project.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><br />As a result, the following is explicitly <strong>prohibited</strong>:</p>\r\n<ol style=\"list-style-type: none;\">\r\n<li>1. Unlike in the special ledger, you cannot delete or subsequently post documents in the live General Ledger Accounting (new).</li>\r\n<li></li>\r\n<li>(However, in exceptional cases due to program errors, you can correct the inconsistencies using SAP Support). Should the situation arise (for example, when the fiscal year is closed), the external auditor must be involved from the very start.</li>\r\n<li>&#x00A0;</li>\r\n<li>2. You are not allowed to subsequently change the ledger definitions or temporarily deactivate/activate an assigned scenario or customer field (for example, subsequent FIN_PCA assignment).</li>\r\n<li>The relevant fields (characteristics) are updated in the database tables of General Ledger Accounting (new) only after you assign a scenario to a ledger. In the case of subsequent activation, the account assignments for the relevant characteristics would be missing from General Ledger Accounting (new) for documents posted previously. This is of particular relevance if characteristics from this scenario are used in document splitting.</li>\r\n<li>Background information:</li>\r\n<li>a) If the characteristics are defined as mandatory in document splitting, the fields contained in the scenario are checked the first time a scenario is assigned. This means that subsequent processes for documents, posted previously without being checked, may lead to an error, because account assignments cannot be passed on from the previous processes.</li>\r\n<li>b) If the general ledger characteristics are defined as financial statement characteristics in document splitting, the generated clearing lines are only updated in the ledgers of General Ledger Accounting (new) if the corresponding account assignment objects are also contained in the assigned scenarios of the ledger.</li>\r\n<li>&#x00A0;</li>\r\n<li>3. You cannot change the ledger definition from leading to non-leading, or the other way around.</li>\r\n<li>Compared to other applications (Controlling, Asset Accounting, and so on), the leading ledger represents the leading valuation view. Switching the leading ledger and another setting (for example, assignment of the accounting principle, or the ledger group derivation from the valuation area or from the variant of the real-time integration, or also change of the value of BAdIs) may lead to an amalgamation of the valuation views.</li>\r\n<li>&#x00A0;</li>\r\n<li>4. If you want to use document splitting, Customizing for this must be complete and correct before the posting start date.</li>\r\n<li>a) You are not allowed to subsequently activate or temporarily deactivate document splitting (either generally or for individual company codes). If such subsequent changes were made, the system would respond to certain subsequent processes with a termination (MESSAGE_TYPE_X Class GLT0 Number 000 - Termination Point: CLEARING_BALANCE) or as of ERP 2005, with error message GLT0 002 'Document splitting: Items for clearing not found'. You cannot pay or clear documents posted before Customizing was changed. If you want to subsequently activate document splitting, this must take place solely as part of a very complex migration project. To meet your requirement for subsequently activating document splitting in General Ledger Accounting (new), a migration scenario is available to subsequently activate document splitting 'Scenario 6: Subsequent implementation of document splitting'. This migration scenario supports only general subsequent activation of document splitting. Special cases, such as converting documents during temporary deactivation or converting documents that were posted using a temporarily different setting for document splitting, are not supported by Support or by the migration scenario mentioned.  Furthermore, this migration scenario does not allow you to subsequently change the Customizing for active document splitting (for example, subsequent implementation of required entry fields for document splitting); a migration scenario of this type has not (yet) been developed. For information about migration, see <a target=\"_blank\" href=\"https://support.sap.com/en/offerings-programs/support-services/general-ledger-migration.html\">https://support.sap.com/en/offerings-programs/support-services/general-ledger-migration.html</a> <NAME_EMAIL> directly.</li>\r\n<li>b) You are not allowed to temporarily deactivate or subsequently activate required entry fields in document splitting (Customizing view V_FAGL_SPLIT_FLD). Background information: The subsequent definition of a general ledger characteristic as a required entry field may result in the error message GLT2 201 with regard to this characteristic, if documents without this definition were already posted and subsequent processes (clearing, reversal, invoice reference) were processed afterwards.</li>\r\n<li>Example 1: The gross invoice (vendor, G/L account, tax) was posted beforehand without required entry field control for a characteristic in document splitting. At the time of posting, the G/L account was not assigned with the characteristic that is now declared as a required entry field. The required account assignment of the vendor line item, which is referenced in the subsequent process (for example, clearing or reversal), is missing. The subsequent process cannot be posted as a result of the error message.</li>\r\n<li>Example 2: A document type is regulated with splitting on the basis of the current account balance of an account to be split. If there is a balance with initial account assignment, the document cannot be posted. The system issues error message GLT2 201. A balance of this type with initial account assignment is caused by a document posted before the definition was changed (document therefore unchecked).</li>\r\n<li>&#x00A0;</li>\r\n<li>5. Subsequent activation of the open item management of an account</li>\r\n<li>The subsequent activation of open item management is allowed only when using suitable functions such as <span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: DE; mso-bidi-language: AR-SA;\">transaction FAGL_ACTIVATE_OP as of Release 6.0 EHP 3 (requires activation of the business function FIN_GL_CI_1 in SFW5) or transaction <span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: DE; mso-bidi-language: AR-SA;\">FINS_ACTIVATE_OIM as of Release FIN 720 </span></span>.<span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"> </span></span></li>\r\n<li><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"></span></span>The activation of the open item management without converting the already posted documents is not allowed. The reports RFSEPA02 and ZFSEPA02 (or similar) are also unsuitable because the required information for the document splitting is not enriched there.</li>\r\n<li></li>\r\n<li>6. A subsequent change to the local currency in FI, the parallel currencies, and their value views (legal valuation, profit center valuation or group valuation) is not permitted or is only possible in the context of an SLO currency changeover package. Furthermore, user-selected currencies in S/4HANA must not be added or changed without enriching or converting the data posted. These user-selected currencies also force the implementation as part of a currency conversion project with suitable tools for handling historical data.&#x00A0;S/4HANA has another restriction whereby the currency of the controlling area must not be changed subsequently. The same also applies to the new assignment of a company code to a controlling area or a change to the controlling area assigned to the company code (especially with regard to its currency).</li>\r\n</ol>\r\n<p><br /><br />In addition, some document splitting settings have certain <strong>side effects</strong> that you should take into consideration:</p>\r\n<ol style=\"list-style-type: none;\">\r\n<li>7. Subsequent addition or change of document splitting characteristics</li>\r\n<li>The document splitting characteristics are taken into account only after you activate or transport the change. This new characteristic is not filled in the case of newly posted subsequent processes for documents posted before the change.</li>\r\n<li>This also applies to the additional characteristics for Controlling and reactivation of fixed assets. A subsequent activation of the Zero Balance Creation for a document splitting characteristic takes effect only once you have activated or transported the change. Before the activation, make sure that the financial statement for this characteristic balances to zero by means of a transfer posting.</li>\r\n<li>&#x00A0;</li>\r\n<li>8. Changing the classification of G/L accounts (item category assignment) may lead to errors in document splitting.</li>\r\n<li>Because of the change to the classification, usage of this account may be prevented by an error message in certain business transactions.</li>\r\n<li>Certain business transactions require specific item categories. For example, the Payment business transaction requires the Cash Account item category (04000) in the standard system. If the classification of the bank account is changed, the business transaction can no longer be posted using this account. When you change the classification, rule-based handling of this account in document splitting changes in the business processes.</li>\r\n<li>For this reason, a line item previously assigned by document splitting leads to an error because it remains unprocessed after the change.</li>\r\n<li>&#x00A0;</li>\r\n<li>9. A subsequent change of the classification for the document types may lead to inconsistent account assignment.</li>\r\n<li>If the original account assignment of the document that is to be reversed is not restored during a reversal (inverse posting), the system derives the currently assigned rule in document splitting. If the assignment was changed, the account assignment determined may deviate from that of the document to be reversed. This leads to the reversal document and the document to be reversed coming apart in terms of their account assignment. As a result of the reversal document, the balances for the account assignments are not reduced correctly in this case. The same effect may occur if the rule assigned to the business transaction is changed.</li>\r\n<li></li>\r\n<li>10. Changing the zero balance clearing accounts or the account key may lead to inconsistencies when you carry out a reversal.</li>\r\n<li>Since account determination also takes place for the clearing accounts in the case of reversal postings, in the case of a reversal, posting takes place to an account other than that for the original document. 10. The first definition of a scenario or customer field in the ledger update activates the validation (check of required entry fields) in document splitting as long as these are defined as characteristics in document splitting and validation is active. This can mean that business transactions can no longer be posted. Background information: Document splitting checks only those fields that the current document also updates in the general ledger. For example, if you post a document for a ledger group, the system checks only the fields that are also assigned to the ledgers affected.</li>\r\n</ol></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023393)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D023393)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000891144/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000891144/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000891144/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000891144/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000891144/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000891144/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000891144/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000891144/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000891144/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1655209", "RefComponent": "FI-GL", "RefTitle": "Error F5234 Fiscal year variant  is incorrect when using transaction FB50L", "RefUrl": "/notes/1655209"}, {"RefNumber": "1619168", "RefComponent": "FI-GL-MIG", "RefTitle": "Overview of the different migration scenarios", "RefUrl": "/notes/1619168"}, {"RefNumber": "1086181", "RefComponent": "FI-GL-MIG-TL", "RefTitle": "Subsequent implementation of document splitting", "RefUrl": "/notes/1086181"}, {"RefNumber": "1085921", "RefComponent": "FI-GL-FL", "RefTitle": "Document split", "RefUrl": "/notes/1085921"}, {"RefNumber": "1039346", "RefComponent": "PY-XX-DT", "RefTitle": "Q&A: RCIPE00/RPCIPE01 - distribution of liabilities", "RefUrl": "/notes/1039346"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2760863", "RefComponent": "FI-GL-BA", "RefTitle": "Business Area is not available in SAP S/4HANA Cloud", "RefUrl": "/notes/2760863 "}, {"RefNumber": "1988311", "RefComponent": "FI-GL-FL", "RefTitle": "GLT2201 in materials management invoice with minor differences", "RefUrl": "/notes/1988311 "}, {"RefNumber": "1812498", "RefComponent": "FI-GL-GL-F1", "RefTitle": "Error in program RFUMSV50: \"Balancing field \"YYY\" in line item XXX not filled\"", "RefUrl": "/notes/1812498 "}, {"RefNumber": "2721527", "RefComponent": "FI-GL", "RefTitle": "Business Area is not updated to specific ledger", "RefUrl": "/notes/2721527 "}, {"RefNumber": "2721469", "RefComponent": "FI-GL", "RefTitle": "Business Area is not displayed in General Ledger View", "RefUrl": "/notes/2721469 "}, {"RefNumber": "2707834", "RefComponent": "FI-GL-FL", "RefTitle": "Error message GLT2201 or GLT2001, or unexpected document splitting results in transaction PRRW", "RefUrl": "/notes/2707834 "}, {"RefNumber": "2216212", "RefComponent": "FI-GL-FL", "RefTitle": "Error GLT2201 Balancing field not filled on MR8M Reversal", "RefUrl": "/notes/2216212 "}, {"RefNumber": "1727802", "RefComponent": "FI-GL-BA", "RefTitle": "Business area missing in sales/ purchase tax items", "RefUrl": "/notes/1727802 "}, {"RefNumber": "2580138", "RefComponent": "FI-GL-FL", "RefTitle": "Splitting Rule for Residual Item Posting in Invoice Currency", "RefUrl": "/notes/2580138 "}, {"RefNumber": "2442923", "RefComponent": "FI-GL-FL", "RefTitle": "BADI ACTIVATE_CC_SPLIT - Partner assignments on company code clearing lines", "RefUrl": "/notes/2442923 "}, {"RefNumber": "1810605", "RefComponent": "FI-GL-REO", "RefTitle": "General information about profit center reorganization", "RefUrl": "/notes/1810605 "}, {"RefNumber": "1039346", "RefComponent": "PY-XX-DT", "RefTitle": "Q&A: RCIPE00/RPCIPE01 - distribution of liabilities", "RefUrl": "/notes/1039346 "}, {"RefNumber": "1672527", "RefComponent": "PSM-FA", "RefTitle": "Including PSM fields in FAGLFLEXA/FAGLFLEXT", "RefUrl": "/notes/1672527 "}, {"RefNumber": "1085921", "RefComponent": "FI-GL-FL", "RefTitle": "Document split", "RefUrl": "/notes/1085921 "}, {"RefNumber": "1732738", "RefComponent": "FI-GL-FL", "RefTitle": "Different or missing segment in reverse document", "RefUrl": "/notes/1732738 "}, {"RefNumber": "1619168", "RefComponent": "FI-GL-MIG", "RefTitle": "Overview of the different migration scenarios", "RefUrl": "/notes/1619168 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "618", "To": "618", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "720", "To": "720", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "106", "To": "106", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "107", "To": "107", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}