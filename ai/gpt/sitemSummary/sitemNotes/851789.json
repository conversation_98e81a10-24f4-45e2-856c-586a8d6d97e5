{"Request": {"Number": "851789", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 273, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000851789?language=E&token=F31ED8AE9F57FF79A97DFAD7261F44A3"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000851789", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000851789/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "851789"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 16}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "24.07.2014"}, "SAPComponentKey": {"_label": "Component", "value": "BC-SEC-VIR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Anti Virus Protection"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Security - Read KBA 2985997 for subcomponents", "value": "BC-SEC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Anti Virus Protection", "value": "BC-SEC-VIR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC-VIR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "851789 - Virus scan profiles delivered by SAP"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note offers advice on using your own profiles within SAP applications based on the SAP Virus Scan Interface (VSI).</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>NW-VSI, VSI, virus, virus scan interface, CL_VSI, virus scan provider</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Some experts believe that it is advisable to use your own virus scan profile in your own applications. This note explains how to create a virus scan profile.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Current profiles delivered:</strong></p>\r\n<ul>\r\n<li>ABAP</li>\r\n<ul>\r\n<li>/SCET/GUI_UPLOAD &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; (SAP_BASIS&#x00A0;620)</li>\r\n<li>/SCET/GUI_DOWNLOAD &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; (SAP_BASIS&#x00A0;731)</li>\r\n<li>/SIHTTP/HTTP_UPLOAD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; &#x00A0; &#x00A0; &#x00A0;&#x00A0;&#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0;&#x00A0;&#x00A0; &#x00A0;(SAP_BASIS&#x00A0;620)</li>\r\n<li>/SIHTTP/HTTP_DOWNLOAD&#x00A0; &#x00A0; &#x00A0;&#x00A0; &#x00A0; &#x00A0; &#x00A0;&#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0;&#x00A0; &#x00A0;(SAP_BASIS&#x00A0;731)</li>\r\n<li>/PAOC_RCF_BL/HTTP_UPLOAD &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; (ERECRUIT&#x00A0;600)</li>\r\n<li>/SARC/ARCHIVING_ADK&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; &#x00A0; &#x00A0;&#x00A0;&#x00A0; &#x00A0; &#x00A0; &#x00A0;&#x00A0; &#x00A0; (SAP_BASIS&#x00A0;700)</li>\r\n<li>/S_ITSP/MIME_UPLOAD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; &#x00A0; &#x00A0; &#x00A0;&#x00A0;&#x00A0;&#x00A0; &#x00A0; &#x00A0; &#x00A0;&#x00A0; &#x00A0; &#x00A0; (SAP_BASIS&#x00A0;700)</li>\r\n<li>/PC01/SVINCOMING&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; &#x00A0; &#x00A0;&#x00A0; &#x00A0; &#x00A0; &#x00A0;&#x00A0; &#x00A0; &#x00A0; &#x00A0; (SAP_HRCDE&#x00A0;4.70)</li>\r\n<li>/MDG_BS_FILE_UPLOAD/MDG_VSCAN&#x00A0; &#x00A0; &#x00A0; &#x00A0; (SAP_BS_FND 7.31)</li>\r\n<li>/SOAP_CORE/WS_RECEIVE &#x00A0; &#x00A0;&#x00A0;&#x00A0; &#x00A0; &#x00A0; &#x00A0;&#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0;(SAP_BASIS&#x00A0;731)</li>\r\n<li>/SOAP_CORE/WS_SEND&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; &#x00A0; &#x00A0; &#x00A0;&#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0;(SAP_BASIS&#x00A0;731)</li>\r\n<li>/SBCOMS/SMTP_INBOUND &#x00A0; &#x00A0; &#x00A0; &#x00A0;&#x00A0;&#x00A0;&#x00A0; &#x00A0; &#x00A0; &#x00A0;&#x00A0; &#x00A0; &#x00A0; &#x00A0;(SAP_BASIS 731)</li>\r\n<li>/SCMS/KPRO_CREATE &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0;(SAP_BASIS 731)</li>\r\n<li>/SCMS/KPRO_XML_CREATE &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0;(SAP_BASIS 731)</li>\r\n<li>/SCET/DP_VS_ENABLED &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; (SAP_BASIS 731)</li>\r\n<li>/SMIM_API/PUT &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0;(SAP_BASIS 731)&#x00A0;&#x00A0;</li>\r\n<li>/SXMSF/PI_MESSAGING &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0;(SAP_BASIS 731)</li>\r\n<li>/SIWB/KW_UPLOAD_CREATE&#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; (SAP_BASIS 731)</li>\r\n<li>/S_NWECM/ECM_UPLOAD&#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0;(SAP_BASIS 731)</li>\r\n<li>/SRM/RCM_CREATE &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0;&#x00A0;&#x00A0;(SAP_BASIS 731)</li>\r\n<li>/SAPC_RUNTIME/APC_WS_MESSAGE_GET (SAP_BASIS 740)</li>\r\n<li>/SAPC_RUNTIME/APC_WS_MESSAGE_SET (SAP_BASIS 740)</li>\r\n<li>/UI5/UI5_INFRA_APP/REP_DT_PUT &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0;(SAP_BASIS 740)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>JAVA</li>\r\n<ul>\r\n<li>webdynpro_FileUpload</li>\r\n<li>visualcomposer</li>\r\n<li>htmlb_FileUpload</li>\r\n<li>kmc_Default</li>\r\n<li>log_FileUpload</li>\r\n</ul>\r\n</ul>\r\n<p><strong>When and why should an application use a customized profile?<br/></strong></p>\r\n<ul>\r\n<li>Applications transport external documents and do not interpret these themselves.</li>\r\n</ul>\r\n<ul>\r\n<li>Applications that use virus protection as a feature should use a customized profile. The reason for this is the independent nature of the basis settings. The virus scan is always carried out if the used profile is active, in other words, if you want to deactivate the delivered basic profiles in order to improve performance, the scan can still be used for specific applications if you assign a customized profile name to the upload module (GUI or HTTP).</li>\r\n</ul>\r\n<ul>\r\n<li>Another reason for your own virus scan profiles is the configurability within the profile, that is, if an application wants to have different scenarios for the check (uploading from the Internet or intranet, for example), it may also have to deliver several profiles here that can be used to configure and control the virus protection.</li>\r\n</ul>\r\n<ul>\r\n<li>Applications can perform additional checks (in addition to the antivirus check) using VSI. For more information on this, see SAP Note 1640285. The check may require different settings for different scenarios. You can achieve this using a separate profile.</li>\r\n</ul>\r\n<p><br/><strong>Note:</strong><br/>Creating and using a separate profile does not automatically mean that the VSI must be implemented. If you use the standard upload or download functions, you can also transfer your own virus scan profile. As a result, the system performs the check using your own profile in this routine.</p>\r\n<p><strong>How do I create my own profile?:</strong></p>\r\n<ul>\r\n<li>ABAP</li>\r\n</ul>\r\n<p>In your own application system, add your own entry to the view cluster in the client for customizing (transaction: SM34). Alternatively, you can also call transaction VSCANPROFILE. The rule for the profile names is: /&lt;package name&gt;/&lt;function name&gt;</p>\r\n<ul>\r\n<li>JAVA</li>\r\n</ul>\r\n<p>Possible methods:<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1. The configuration for the J2EE Service is located in the Configuration Adapter. You can create your own entry here in the interface for the Configuration Adapter.<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2. The system calls the method addProfile in the service class VSIService. You can also transfer a separate profile name, explanatory text, and profile parameters (that control the behavior) to this method. An inactive profile is created if it does not already exist. The administrator in the customer system can activate the profile when required. This SAP Note or the URL https://service.sap.com/~sapidb/012006153200000147002012E/Java_Example_Program_Own_Profile.pdf provides an example for this.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-SEC (Security - Read KBA 2985997 for subcomponents)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D036670)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D036670)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000851789/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000851789/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000851789/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000851789/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000851789/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000851789/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000851789/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000851789/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000851789/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Java_Example_Program_Own_Profile.pdf", "FileSize": "93", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000243192005&iv_version=0016&iv_guid=8D88BE68A8774A46B7D9558626EBD3EF"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "817623", "RefComponent": "BC-SEC-VIR", "RefTitle": "Frequent questions about VSI in SAP applications", "RefUrl": "/notes/817623"}, {"RefNumber": "786179", "RefComponent": "BC-SEC-VIR", "RefTitle": "Data security products: Use in the antivirus area", "RefUrl": "/notes/786179"}, {"RefNumber": "1723363", "RefComponent": "BC-DWB-TOO", "RefTitle": "Virus Scanning Profile for MIME Repository", "RefUrl": "/notes/1723363"}, {"RefNumber": "1671303", "RefComponent": "BC-ESI-WS-ABA", "RefTitle": "Virus scans in Web service messages(payload and attachments)", "RefUrl": "/notes/1671303"}, {"RefNumber": "1640285", "RefComponent": "BC-SEC-VIR", "RefTitle": "Determine MIME type with Virus Scan Interface", "RefUrl": "/notes/1640285"}, {"RefNumber": "1062758", "RefComponent": "EP-PDK-HBJ", "RefTitle": "Virus Scan for HTMLB File Upload Control", "RefUrl": "/notes/1062758"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3019748", "RefComponent": "SV-SMG-SUP", "RefTitle": "Attachment not saving when using Fiori app", "RefUrl": "/notes/3019748 "}, {"RefNumber": "2990618", "RefComponent": "CA-UI5-FL-LRP", "RefTitle": "Error: \"Unable to load the data\" when clicking the drop-down arrow to manage views / variants - SAPUI5", "RefUrl": "/notes/2990618 "}, {"RefNumber": "2621764", "RefComponent": "SV-SMG-ADM-DTM", "RefTitle": "How to upload work modes in IT Calendar using csv files", "RefUrl": "/notes/2621764 "}, {"RefNumber": "2580438", "RefComponent": "BC-SRV-APS-EXT-AQD", "RefTitle": "ADTSET_GET_ENTITYSET not implemented", "RefUrl": "/notes/2580438 "}, {"RefNumber": "2398038", "RefComponent": "MDM-GDS", "RefTitle": "SAP NetWeaver MDM GDS 2.1 SP05 release note", "RefUrl": "/notes/2398038 "}, {"RefNumber": "786179", "RefComponent": "BC-SEC-VIR", "RefTitle": "Data security products: Use in the antivirus area", "RefUrl": "/notes/786179 "}, {"RefNumber": "817623", "RefComponent": "BC-SEC-VIR", "RefTitle": "Frequent questions about VSI in SAP applications", "RefUrl": "/notes/817623 "}, {"RefNumber": "1671303", "RefComponent": "BC-ESI-WS-ABA", "RefTitle": "Virus scans in Web service messages(payload and attachments)", "RefUrl": "/notes/1671303 "}, {"RefNumber": "1062758", "RefComponent": "EP-PDK-HBJ", "RefTitle": "Virus Scan for HTMLB File Upload Control", "RefUrl": "/notes/1062758 "}, {"RefNumber": "1640285", "RefComponent": "BC-SEC-VIR", "RefTitle": "Determine MIME type with Virus Scan Interface", "RefUrl": "/notes/1640285 "}, {"RefNumber": "1476664", "RefComponent": "PY-DE-BA", "RefTitle": "SV: Optional check of inbound SI data for viruses", "RefUrl": "/notes/1476664 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}