{"Request": {"Number": "1108861", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 309, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001108861?language=E&token=EA1474D513CC5E35FEC620D0603D0AD6"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001108861", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001108861/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1108861"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 13}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "05.02.2018"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-RDM"}, "SAPComponentKeyText": {"_label": "Component", "value": "README: Upgrade Supplements"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "README: Upgrade Supplements", "value": "BC-UPG-RDM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-RDM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1108861 - Add. info. on upgrading to SAP CRM 5.0 SR3 ABAP"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Errors in the upgrade or update procedure or in the upgrade guides; preparations for the upgrade or update; additional information to the upgrade guide</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Update, upgrade, SUM, Software Update Manager, SAP CRM, SAP Customer Relationship Management</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>*</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>CAUTION: This note is continuously updated.You should therefore read it again immediately before the upgrade.</strong><br /><br /><strong>CAUTION:This note only applies to SAP CRM 5.0 Support Release 3</strong></p>\r\n<p><strong>What can I expect from this note?</strong></p>\r\n<p>This note describes problems that may occur when you upgrade the system and provides you with information on how to solve them.This usually takes the form of references to other notes.<br />The aim of this note is to prevent data loss, upgrade shutdowns and long runtimes.<br />Only database-independent problems are dealt with here.</p>\r\n<p><strong>What should I not expect from this note?</strong></p>\r\n<p>Problems that occur after the upgrade are only dealt with if they are caused directly by the upgrade tools.</p>\r\n<p><strong>Which notes do I also need in preparation for the upgrade?</strong></p>\r\n<p>Short text................................................. Note number<br />______________________________________________________________________<br />Repairs for upgrade to SAP NW 7.0 AS ABAP........................813658<br />Corrections for SAPup release 7.00...............................821032<br />OCS: Known problems with Support Packages in SAP NW 7.0 AS ABAP..822379</p>\r\n<p>Use the Software Update Manager 1.0 for an update or upgrade to SAP CRM 5.0 SR3 ABAP. You can find the latest SUM guide, the latest central SAP Note (including database-relevant SAP Notes), and further information such as SAP Community information on SUM at: <a target=\"_blank\" href=\"http://support.sap.com/sltoolset\">http://support.sap.com/sltoolset</a> -&gt; System Maintenance.<br /><br /><br />----------------------------------------------------------------------</p>\r\n<p><strong>Contents</strong></p>\r\n<p>I/ ...... Keyword<br />II/ ..... Important general information<br />III/ .... Corrections to the guides<br />IV/ ..... Errors on the CD-ROM<br />V/ ..... .Preparations for the upgrade<br />VI/ .... .Problems in the PREPARE and upgrade phases<br />VII/ ... .Problems after the upgrade<br />VIII/ ....Chronological summary</p>\r\n<p><strong>I/ Keyword</strong></p>\r\n<p>Keywords are no longer applicable.<br />(For information only: The SAPup keyword for phase KEY_CHK was&#160;16149315.)</p>\r\n<p>&#160;</p>\r\n<p><strong>II/ Important general information</strong></p>\r\n<p><strong>**************************************************</strong></p>\r\n<p>The former tool SAPup was replaced by the Software Update Manager (SUM). See also SAP Note 1589311 and the blog:<br /><a target=\"_blank\" href=\"https://blogs.sap.com/2012/11/07/software-update-manager-sum-introducing-the-tool-for-software-maintenance/\">https://blogs.sap.com/2012/11/07/software-update-manager-sum-introducing-the-tool-for-software-maintenance/</a>.</p>\r\n<p>Use the Software Update Manager 1.0 for an update or upgrade to SAP CRM 5.0 SR3 ABAP.</p>\r\n<p>You can find the latest SUM guide, the latest central SAP Note (including database-relevant SAP Notes), and further information such as SAP Community information on SUM at: <a target=\"_blank\" href=\"http://support.sap.com/sltoolset\">http://support.sap.com/sltoolset</a> -&gt; System Maintenance.</p>\r\n<p><strong>**************************************************</strong></p>\r\n<p>&#160;</p>\r\n<p>---------------------&lt; D025323 MAY/24/02 &gt;----------------------------<br /><strong>Corrections and repairs for the upgrade</strong><br />Before the upgrade, it is vital that you check whether the following are available for your specific upgrade:</p>\r\n<ul>\r\n<li>A new version of SAPup. For more information, see <strong>Note 821032</strong>.</li>\r\n</ul>\r\n<ul>\r\n<li>Repairs to the ABAP upgrade programs. For more information, see <strong>Note 813658</strong>.</li>\r\n</ul>\r\n<p><strong>It is ESSENTIAL that you apply these two Notes.</strong><br /><br /><br />-----------------------&lt; D028310 JUL/19/02 &gt;--------------------------<br /><strong>Problems with the shadow instance</strong><br />The following Notes contain information about problems with the shadow instance:</p>\r\n<ul>\r\n<li><strong>525677</strong>: Problems with starting the shadow instance</li>\r\n</ul>\r\n<ul>\r\n<li><strong>430318</strong>: Shadow instance on other operating system</li>\r\n</ul>\r\n<p><br />------------------------&lt; D032354 10/MAR/09 &gt;------------------------<br /><strong>Upgrade to CPRXRPM 450_700</strong><br />If your system contains add-on component CPRXRPM (displayed in phase IS_SELECT), you must include Support Package SAPK-45006INCPRXRPM in the upgrade. Otherwise, you will lose data in the XPRAS phase.<br />For more information, see Note <strong>1253309</strong>.<br /><br />------------------------&lt; D042621 FEB/02/05 &gt;-------------------------<br /><strong>LSMW now part of SAP_BASIS</strong><br />As of SAP Web AS 6.20, LSMW is part of SAP_BASIS. If you are using LSMW and your source release is based on SAP Web AS 6.10 or lower, do not implement LSMW after the upgrade. For more information, see <strong>Note 673066</strong>.<br /><br />-----------------------&lt; D020904 MAR/15/07 &gt;---------------------------<br /><strong>MSCS: Upgrade of the ABAP system only, or Upgrade of the ABAP system with subsequent Java Add-In installation:</strong><br />In an MSCS configuration, you must split the ABAP central instance into the ABAP central instance (ASCS) and central instance, as described in SAP Note <strong>1011190</strong>, if one of the following cases apply:</p>\r\n<ul>\r\n<li>You have upgraded your SAP NetWeaver '04 based ABAP system to SAP NetWeaver 7.0 based ABAP server system</li>\r\n</ul>\r\n<ul>\r\n<li>You have upgraded your SAP NetWeaver '04 based ABAP system to SAP NetWeaver 7.0 based ABAP server system, and now want to install a Java Add-In. The split must done before the Java Add-In installation.</li>\r\n</ul>\r\n<p>The split is required for the use of the enqueue replication server. If you run an MSCS configuration without enqueue replication server, which is the traditional MSCS configuration (where the clustered central instance&#160;&#160;runs the message server and enqueue work process), you will loose the enqueue lock table during a failover. The enqueue replication server configuration prevents this loss.<br />If you want to migrate an ABAP+Java system that is already upgraded to SAP NetWeaver 7.0 to the new configuration with enqueue replication server, you must perform a homogeneous system copy.<br /><br />----------------------------------------------------------------------<br /><br /></p>\r\n<p><strong>III/ Corrections to the guides</strong></p>\r\n<p><br />----------------------&lt; D038245 10/MAR/09 &gt;---------------------------<br /><strong>Limited Length of the DDIC Password</strong><br />During the Configuration Roadmap step, you need to enter the DDIC password. The upgrade guide does not mention that the length of the DDIC password is limited to 10 characters.<br /><br />----------------------&lt; D019369 10/MAR/09 &gt;---------------------------<br /><strong>Migration of Workload Statistics Data</strong><br />The report and table names in the guide are wrong. For the proper names and procedure, see Notes <strong>1005238</strong> and <strong>1006116</strong>.<br /><br />-----------------------&lt; D038245 23/OCT/08 &gt;--------------------------<br /><strong>Section: Saving Files for Follow-Up Upgrades</strong><br />Do not use file PATBIND.SAV. Using file PATBIND.SAV for a follow-up upgrade leads to a segmentation fault in phase BIND_PATCH.<br /><br />-----------------------&lt; D033903 24/SEP/08 &gt;--------------------------<br /><strong>SDK Version 1.4.x for Upgrade Assistant</strong><br />The upgrade assistant only supports Java Software Development Kit (SDK) 1.4.x. It does not support version 1.5 or higher.<br /><br />-----------------------&lt; D001330 06/AUG/08 &gt;--------------------------<br /><strong>Making Entries for the Parameter Input Module</strong><br />Note that the maximum length of the mount directory path is 42 characters (not 50 as mentioned in the guide).<br /><br />----------------------------------------------------------------------<br /><br /><br /></p>\r\n<p><strong>IV/ Errors on the CD-ROM</strong></p>\r\n<p><br /><br /><br />----------------------------------------------------------------------</p>\r\n<p><strong>V/ Preparations for the upgrade</strong></p>\r\n<p><br /><br />---------------------&lt; D050445 10/MAR/09 &gt;----------------------------<br /><strong>HA on Windows only: Apply Note 828432 Before Starting the Upgrade</strong><br />For source release systems on SAP NetWeaver '04 only: If you run a high availability system on Windows and your kernel patch level is lower than 169, you need to patch your kernel to at least PL 169 and then apply SAP Note <strong>828432</strong> prior to starting the upgrade.<br /><br />----------------------&lt; D027360 04/MAR/08 &gt;--------------------------<br /><strong>Do not include SAP_AP SP12 into the upgrade</strong><br />To avoid runtime errors during the upgrade in phase XPRAS_UPG, do not include SAP_AP Support Package 12. The problem is solved with SAP_AP Support Package 13. For more information, see Note <strong>1144541</strong>.<br /><br />----------------------&lt;changed D001658 10/JUL/07 &gt;--------------------<br />------------------------&lt; D001330 03/JUL/07 &gt;-------------------------<br /><strong>Parameter for the Central Syslog rslg/collect*</strong><br />There may be conflicts with parameter values for the central syslog \"rslg/collect*\" of the default profile.<br />If the parameters are set with 39&lt;no.&gt; and 40&lt;no.&gt;, you must change them to 14&lt;no.&gt; and 15&lt;no.&gt;. Make sure that the ports are not used by other applications. If so, choose some other number.<br />For more information, see Note <strong>1069225</strong>.<br /><br />-----------------------&lt; D038006 02/NOV/05 &gt;--------------------------<br /><strong>Source Release on Kernel 6.40 with SuSE SLES 9 IA64 / RedHat 4.0 IA64</strong><br />If you are using the above combination on your source release and have set environment variable LD_ASSUME_KERNEL 2.4.21 according to SAP Note 797084, proceed as follows:<br />Before you start the upgrade, you need to delete the environment variable LD_ASSUME_KERNEL 2.4.21 and deactivate component icman by setting the following instance profile entry:<br />rdisp/start_icman=false<br />Restart the system.<br />After the upgrade, delete the instance profile entry again.<br /><br />--------------------------&lt; I080284 12/OCT/05 &gt;------------------------<br /><strong>Software Agent Framework only:</strong><br />If you use Software Agent Framework (SAF) in the source release system, clean old SAF queue jobs and indexes before you start the upgrade of the CRM server. For more information, see SAP Note <strong>887277</strong>.<br /><br />------------------------&lt; D003327 15/SEP/05 &gt;--------------------------<br /><strong>Back up customer-specific entries in table EDIFCT</strong><br />If you have maintained customer-specific entries in table EDIFCT, they may get lost during the upgrade. If you do not want to lose these entries, export them before the upgrade and reimport them after the upgrade.<br />For more information, see Note <strong>865142</strong>.<br /><br />--------------------------&lt; D034302 16/AUG/05 &gt;-----------------------<br /><strong>Windows only: Last profile text line must end with line feed</strong><br />Check that the last text line in all profiles ends with a line feed.To check this, open a profile file with a text editor, go down with the cursor and ensure that the last line is empty. Otherwise there could be problems in the KX_SWITCH phase as some tools will not be able to handle the profiles properly.<br /><br />-----------------------&lt; D019003 OCT/02/03 &gt;--------------------------<br /><strong>Source Release CRM 3.0: Preparation for Records Management</strong><br />If your system has source release CRM 3.0 and you are using SAP Records Management, make the preparations described in <strong>Note 666630</strong> to avoid an error in the upgrade phase TP_ACTION_CP2STAB.<br /><br />----------------------&lt; D023648 SEP/30/03 &gt;---------------------------<br /><strong>Only for source Release CRM 3.1: Importing SAPKB62030</strong><br />Import SAP Basis Support Package 30 (SAPKB62030) <strong>before</strong> the upgrade, to prevent an error in the phase DIFFEXPPKGE.<br />For more information, see <strong>Note 662493</strong>.<br /><br />-------------&lt; D024449 JUN/06/03, changed DEC/08/03 &gt;------------------<br /><strong>Source Release CRM 3.0 and CRM 3.1: Error in the activation phase</strong><br />To prevent an upgrade termination in the ACT_&lt;rel&gt; phase with a possibledata loss, you must apply <strong>SAP Note 629968</strong> before starting the upgrade provided that your source release is CRM 3.0 or CRM 3.1.<br /><br />----------------------&lt;D026817 MAR/28/03&gt;-----------------------------<br /><strong>Correcting entries in table SMOFRQHD</strong><br />Before upgrading from SAP CRM&#160;&#160;3.0 or 3.1, you must check and, if necessary, correct the table contents of the SMOFRQHD table. For more information, see <strong>SAP note 606417</strong>.<br /><br />-------------------&lt;D021211 AUG/01/02&gt;--------------------------------<br /><strong>SAP CRM only: Database &lt;-&gt; ABAP/4 Dictionary consistency check</strong><br />Before the upgrade, carry out a consistency check between the database and the ABAP/4 Dictionary. If the M_MCHA table in the database does not exist in the DDIC, you can delete this table. Doing this prevents an error in the PARDIST_SHD phase.<br /><br /><br />----------------------------------------------------------------------<br /><br /><br /><br /></p>\r\n<p><strong>VI/ Problems in the PREPARE and Upgrade Phases</strong><br /><br /></p>\r\n<p>In this section, you will find known problems that cannot be prevented using preventive measures.These problems can only occur under certain conditions or prerequisites.</p>\r\n<p><strong>Problems in the PREPARE Phases</strong></p>\r\n<p><br />-----------------------&lt; D038245 23/OCT/08 &gt;--------------------------<br /><strong>Segmentation Fault in Phase BIND_PATCH</strong><br />If you have used file PATBIND.SAV to use the Support Package selection of a previous upgrade, you receive an error in phase BIND_PATCH.<br />To solve the problem, delete file PATBIND.SAV from the save subdirectory and repeat the phase.<br /><br />--------------------------&lt; D034302 03/APR/08 &gt;-----------------------<br /><strong>Windows only: Error when starting PREPARE</strong><br />When you start prepare.bat, the following error message displayed:<br />CScript Error: Can't find script engine \"JScript\" for script &lt;path&gt;\\prepare.js<br />To solve this problem, execute the following command in the command prompt:<br />regsvr32 /s %SystemRoot%\\System32\\jscript.dll<br /><br />--------------------------&lt; D038245 28/FEB/08 &gt;-----------------------<br /><strong>Windows only: MSSERV_INTERN port numbers</strong><br />During PREPARE, the upgrade tool requests a port number for the MSSERV_INTERN port. If your system is running on Windows, you cannot use port numbers within the following range: 6665 - 6669.<br /><br />-----------------------&lt; I027626 29/SEP/04 &gt;---------------------------<br /><strong>For Windows only: Unpacking SAPEXE.SAR tables</strong><br />The PREPARE stops with the following error message:<br />R3up&gt; ERROR: Extraction of *\\NT\\I386\\SAPEXE.SAR to directory<br />*\\usr\\put\\exe failed.<br />The error occurs because the UnInstSAP.EXE file in the \\put directory is write-protected. Remove the write protection and start PREPARE from the \\put\\exe directory with the repeat option.<br /><br />-----------------------------------------------------------------------<br /><br /></p>\r\n<p><strong>Problems in the Upgrade Phases</strong></p>\r\n<p><br />------------------------&lt; D037517 02/MAR/10 &gt; -------------------------<br /><strong>Phase MAIN_SHADOW/START_SHDI_FIRST</strong><br />Description: An RFC error occurs (RFC LOGIN FAILED)<br />Solution:</p>\r\n<ol>1. In the SAPup.par file, set the value of the /UNIX/sleep_after_shdsys_start parameter to 200.</ol><ol>2. Restart the upgrade program and repeat the phase.</ol>\r\n<p><br />--------------------------&lt; D034302 16/AUG/05 &gt;-----------------------<br />Phase: KX_SWITCH (Windows only)<br />Description: Upgrade stops with an error message which asks you to checkfile NTPARVAL.LOG. In this file, messages like the following are displayed:<br />fopen(...): Invalid argument<br />Check that the last text line in all profiles ends with a line feedback.To check the profile, open a profile file with a text editor, go down with the cursor, and ensure that the last line is empty. If it is not, add an empty line at the end of the file and save it.<br /><br /><br />----------------------&lt; D038245 FEB/28/06 &gt;--------------------------<br />Phase: MODPROFP_UPG<br />Description: When you are performing a double-stack upgrade (ABAP and Java system at the same time), in phase MODPROFP_UPG the ABAP system is started together with the Java system part for the first time. The phase may fail if the SAP J2EE Engine does not start fast enough.<br />Check manually if the SAP J2EE Engine is running and if so, choose \"repeat\" to repeat the upgrade phase.<br /><br />----------------------&lt; D027360 AUG/06/08 &gt;--------------------------<br />Phase: XPRAS_UPG<br />Description: If SAP_AP Support Package 13 is included into the upgrade, the phase may fail due to a syntax error in program /SAPCND/SAPLCUS_GEN_ON_THE_FLY.<br />To avoid this error, proceed as described in SAP Note <strong>1230048</strong>.<br /><br />----------------------&lt; D027360 MAR/04/08 &gt;--------------------------<br />Phase: XPRAS_UPG<br />Description: If SAP_AP SP12 is included into the upgrade, the phase may fail due to a syntax error. As long as SAP_AP SP13 is not available, proceed as described in Note <strong>1144541</strong>.<br /><br />--------------------------&lt; D021063 OCT/12/06 &gt;----------------------<br />Phase: XPRAS_UPG<br />Description: The phase issues the following short dump:<br />\"GEN_BAD_MACRO_SOURCE\" for program /1BCDWB/&lt;XXXXXXXXXXXXXXXXXX&gt;<br />You can ignore this message.<br /><br />--------------------------&lt; I006704 DEC/12/05 &gt;----------------------<br />Phase: XPRAS_UPG<br />Description: If your codepage does not equal 1100, you may get a codepage conversion error. In this case, replace the current kernel with the latest version and repeat the phase.<br /><br />-----------------&lt; D038245 changed 25/FEB/04 &gt;------------------------<br /><br />Phase: XPRAS_UPG<br />Description: If a database error occurs in this phase (an overflowing tablespace, for example), SAPup automatically repeats the phase. In somecases, SAPup may continue to run but posting is deactivated in the system. The following entry appears in the system log:<br />The update task was deactivated.<br />In this case, solve the database problem (extend the tablespace, for example), call transaction SM14 and activate the update again.<br /><br />-----------------------&lt; D003551 NOV/25/05 &gt;---------------------------<br />Phase: JOB_RDDNTPUR<br />Description: In the longpost-log file, you may get the error message:<br />3PETG447 Table and runtime object \"BBP_UPGR_CLASSES\" exist without DDIC reference (\"Transp. table\")<br />and<br />3PETG447 Table and runtime object \"BBP_UPGR_CRMTABS\" exist without DDIC reference (\"Transp. table\")<br />You can ignore this message.<br /><br />----------------------------------------------------------------------<br />Phase: CHK_POSTUP<br />Note: 996953<br />Description: Log file LONGPOST.LOG asks you to migrate a CMOD project although no project exists.<br /><br />-----------------------------------------------------------------------<br />Phase CHK_POSTUP<br />Note: 197886<br />Description:If you have imported notes 178631, 116095 or 69455 into your system before the upgrade, error messages for objects without a DDIC reference will appear in the LONGPOST.LOG log. Proceed as described in the note.<br />----------------------------------------------------------------------<br /><br /><br /><br /><br /></p>\r\n<p><strong>VII/ Problems after the upgrade</strong><br /><br /></p>\r\n<p>In this category, you find known problems that are caused by the upgrade tools and that you can prevent using preventive measures. These problems only occur in certain conditions or prerequisites.<br /><br />---------------------&lt; D003550 29/SEP/09 &gt;-----------------------------<br /><strong>Lost Texts of Domain Fixed Values</strong><br />If you have upgraded your system to SAP CRM 5.0 Support Package Stack 13, you may encounter the loss of texts of domain fixed values. For a solution, see SAP Note <strong>1162171</strong>.<br /><br />-----------------------&lt; D034302 30/JAN/09 &gt;-------------------------<br /><strong>Inactive Table /BI0/PTCTUSERNM and View /BI0/MTCTUSERNM</strong><br />If after the upgrade the transaction <strong>db02</strong> shows that the table /BI0/PTCTUSERNM and the view /BI0/MTCTUSERNM do not exist in the database, activate these objects manually using transaction <strong>se11</strong>.<br /><br />-----------------------&lt; D000587 25/FEB/08 &gt;---------------------------<br /><strong>Error when Converting Print Parameters</strong><br />After the upgrade, in some clients the printing parameters have either gone or been replaced by other parameters. For more information, see Note <strong>1142364</strong>.<br />The problem is solved with SAP Basis Support Package 16.<br /><br />----------------------&lt;enhanced D038245/JUNE/05 &gt;----------------------<br />------------------------&lt; D003551 02/MAY/05 &gt;--------------------------<br /><strong>Check Primary Index of Table MEMGMT_DEPLOYMNT</strong><br />After the upgrade, check that the primary index of table MEMGMT_DEPLOYMNT exists. If not, create the index manually. If you still get the message that MEMGMT_DEPLOYMNT00 is an unknown object in the dictionary, drop it on the database level.<br /><br />----------------------&lt;D029078 JUL/22/03&gt;-----------------------------<br /><strong>Obsolete entries in table SMO9_KYTBL</strong><br />After the upgrade, delete obsolete entries from the SMO9_KYTBL table. For more information, see Note <strong>637829</strong>.<br /><br />------------------------&lt; D020815 AUG/23/02 &gt;-------------------<br /><strong>SPAU: Names of interface methods are truncated</strong><br />In transaction SPAU, some methods that were modified and overwritten by the upgrade (ABAP objects) may be displayed in abbreviated form (truncated to 30 characters). As a result, methods in transaction SPAU may also be sorted incorrectly under \"Deleted objects\".<br />Caution: Deleted objects are not displayed for the standard selection in transaction SPAU. It is very easy to overlook these.<br />For more information on the correction, see <strong>note 547773</strong>.<br /><br /><br />----------------------------------------------------------------------<br /><br /><br /><br /><br /></p>\r\n<p><strong>IX/ Chronological summary</strong></p>\r\n<p><br />Date......Topic..Short description<br />-----------------------------------------------------------------------<br />MAR/02?10...VI...Phase MAIN_SHADOW/START_SHDI_FIRST<br />SEP/29/09..VII...Lost Texts of Domain Fixed Values<br />MAR/10/09....V..HA on Windows only: Apply Note 828432 Before the Upgrade<br />MAR/10/09..III...Limited Length of the DDIC Password<br />MAR/10/09..III...Migration of Workload Statistics Data<br />MAR/10/09...II...Upgrade to CPRXRPM 450_700<br />JAN/30/09..VII...Inactive Table /BI0/PTCTUSERNM and View /BI0/MTCTUSERNM<br />OCT/23/08..III...Section: Saving Files for Follow-Up Upgrades<br />OCT/23/08...VI...Segmentation Fault in Phase BIND_PATCH<br />SEP/24/08..III...SDK Version 1.4.x for Upgrade Assistant<br />AUG/06/08..III...Making Entries for the Parameter Input Module<br />AUG/06/08...VI...Phase XPRAS_UPG: Program /SAPCND/SAPLCUS_GEN_ON_THE_FLY<br />APR/03/08...VI...Windows only: Error when starting PREPARE<br />MAR/04/08....V...Do not include SAP_AP SP12 into the upgrade<br />MAR/04/08...VI...Phase XPRAS_UPG - Error with SAP_AP SP12<br />FEB/28/08...VI...Windows only: MSSERV_INTERN port numbers<br />FEB/25/08..VII...Error when Converting Print Parameters<br />JUL/03/07....V...Parameter for the Central Syslog rslg/collect*<br />MAR/15/07...II...MSCS Configuration<br />FEB/28/07...VI...Phase CHK_POSTUP - CMOD/SMOD migration<br />JAN/23/07...II...SAP NW BI: Do not include BW SP 11<br />OCT/12/06...VI...Phase XPRAS_UPG: short dump GEN_BAD_MACRO_SOURCE<br />FEB/28/06...VI...Phase MODPROFP_UPG fails - check J2EE Engine<br />NOV/25/05...VI...Longpost.log: Table Without DDIC Reference<br />NOV/02/05....V...Kernel 6.40 with SuSE SLES 9 IA64 / RedHat 4.0 IA64<br />OCT/12/05....V...SAF: Clean old SAF queue jobs and indexes<br />SEP/15/05....V...Back up customer-specific entries in table EDIFCT<br />AUG/16/05...VI...Windows only: KX_SWITCH - Check file NTPARVAL.LOG.<br />AUG/16/05....V...Windows only: Last profile text line - line feedback<br />MAY/02/05..VII...Primary Index of Table MEMGMT_DEPLOYMNT<br />FEB/02/05...II...LSMW now part of SAP_BASIS<br />SEP/29/04...VI...Windows only: Unpacking SAPEXE.SAR tables<br />OCT/29/03....V...Source Release CRM 3.1: Substit. containers<br />OCT/02/03....V...CRM 3.0: Preparation for Records Management<br />SEP/30/03....V...Source Release CRM 3.1: Importing SAPKB62030<br />JUL/22/03..VII...Obsolete entries in table SMO9_KYTBL<br />MAR/28/02....V...Checking the SMOFRQHD table<br />AUG/23/02..VII...SPAU: Names of interface methods are truncated<br />AUG/01/02....V...Database &lt;-&gt; ABAP/4 Dictionary consistency check<br />JUL/19/02...II...Problems with the shadow instance<br />MAY/24/02...II...Corrections and repairs for the upgrade<br />MAY/23/02...VI...CHK_POSTUP phase - objects without DDIC reference<br />----------------------------------------------------------------------</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D031330)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001108861/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001108861/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001108861/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001108861/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001108861/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001108861/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001108861/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001108861/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001108861/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455 "}, {"RefNumber": "822379", "RefComponent": "BC-UPG-OCS", "RefTitle": "Known problems with support packages in SAP NW 7.0x AS ABAP", "RefUrl": "/notes/822379 "}, {"RefNumber": "1011190", "RefComponent": "BC-OP-NT", "RefTitle": "MSCS:Splitting the Central Instance After Upgrade to 7.0/7.1", "RefUrl": "/notes/1011190 "}, {"RefNumber": "493387", "RefComponent": "BC-ABA-LA", "RefTitle": "Potential effects of table- and structure - extensions", "RefUrl": "/notes/493387 "}, {"RefNumber": "865142", "RefComponent": "BC-MID-ALE", "RefTitle": "Customer-specific entries in EDIFCT are deleted", "RefUrl": "/notes/865142 "}, {"RefNumber": "797084", "RefComponent": "BC-OP-LNX-SUSE", "RefTitle": "SUSE LINUX Enterprise Server 9: Installation notes", "RefUrl": "/notes/797084 "}, {"RefNumber": "1005238", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Migration of workload statistics data to NW2004s", "RefUrl": "/notes/1005238 "}, {"RefNumber": "1006116", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Migration of workload statistics data to NW2004s (2)", "RefUrl": "/notes/1006116 "}, {"RefNumber": "813658", "RefComponent": "BC-UPG-RDM", "RefTitle": "Repairs for upgrades to products based on SAP NW 2004s AS", "RefUrl": "/notes/813658 "}, {"RefNumber": "821032", "RefComponent": "BC-UPG-RDM", "RefTitle": "Corrections for SAPup release 7.00", "RefUrl": "/notes/821032 "}, {"RefNumber": "1133823", "RefComponent": "BC-UPG-RDM", "RefTitle": "Upgrade to SAP Business Suite 2005 SR3: IBM i5/OS", "RefUrl": "/notes/1133823 "}, {"RefNumber": "673066", "RefComponent": "BC-SRV-DX-LSM", "RefTitle": "LSMW: Upgrade to SAP Enterprise 4.7 (or Basis 6.20)", "RefUrl": "/notes/673066 "}, {"RefNumber": "1253309", "RefComponent": "PPM-PFM", "RefTitle": "Changes to tables ACO_USER and ACO_ROLE after RPM XPRA", "RefUrl": "/notes/1253309 "}, {"RefNumber": "1230048", "RefComponent": "AP-PRC-CON", "RefTitle": "Syntax error for XPRAS_UPD for SAP_AP SP13 bound to upgrade", "RefUrl": "/notes/1230048 "}, {"RefNumber": "547773", "RefComponent": "BC-DWB-CEX", "RefTitle": "SPAU: interface method names are truncated", "RefUrl": "/notes/547773 "}, {"RefNumber": "662493", "RefComponent": "BC-CTS-ORG", "RefTitle": "TR_SORT_OBJECT_LIST", "RefUrl": "/notes/662493 "}, {"RefNumber": "116095", "RefComponent": "SV-SMG-SDD", "RefTitle": "Solution Tools Plug-In (TCC Basis Tools and Trace Tools)", "RefUrl": "/notes/116095 "}, {"RefNumber": "629968", "RefComponent": "CRM-BE", "RefTitle": "Upgrade CRM4.0/SRM3.0 or higher - error in activation phase", "RefUrl": "/notes/629968 "}, {"RefNumber": "1069225", "RefComponent": "BC-CST-DP", "RefTitle": "NO HW ID RECEIVED BY MSSG SERVER", "RefUrl": "/notes/1069225 "}, {"RefNumber": "197886", "RefComponent": "BC-UPG", "RefTitle": "Upgrade: messages in CHK_POSTUP: BDL*, STSL*, BAM*, SQLR*", "RefUrl": "/notes/197886 "}, {"RefNumber": "557314", "RefComponent": "BC-ABA-TO", "RefTitle": "As of Release 610: TVARV replaced with TVARVC", "RefUrl": "/notes/557314 "}, {"RefNumber": "996953", "RefComponent": "BC-DWB-CEX", "RefTitle": "SPAU entry exists for nonexistent CMOD project", "RefUrl": "/notes/996953 "}, {"RefNumber": "887277", "RefComponent": "CRM-BF-SAF", "RefTitle": "Clean old SAF queue jobs and indexes", "RefUrl": "/notes/887277 "}, {"RefNumber": "637829", "RefComponent": "CRM-MW-KGN", "RefTitle": "Outdated data deletion in keygen after CRM/EBP upgrade", "RefUrl": "/notes/637829 "}, {"RefNumber": "666630", "RefComponent": "BC-SRV-RM", "RefTitle": "Records Management: preparations for upgrading 610 => XXX", "RefUrl": "/notes/666630 "}, {"RefNumber": "553110", "RefComponent": "BC-I18", "RefTitle": "User Exits:  Behavior within non-Unicode R/3 Enterprise", "RefUrl": "/notes/553110 "}, {"RefNumber": "606417", "RefComponent": "CRM-MW-ADP", "RefTitle": "Upgrade preparation for the SMOFRQHD table", "RefUrl": "/notes/606417 "}, {"RefNumber": "626272", "RefComponent": "BC-UPG", "RefTitle": "Termination in the JOB_RSTLANUPG phase", "RefUrl": "/notes/626272 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BBPCRM", "From": "500", "To": "500", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}