{"Request": {"Number": "1277295", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1898, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001503232018"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001277295?language=E&token=B74F69263511A56B5C1F1DE2B3F0AC09"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001277295", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001277295/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1277295"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 11}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "31.07.2018"}, "SAPComponentKey": {"_label": "Component", "value": "SV-BO"}, "SAPComponentKeyText": {"_label": "Component", "value": "Backoffice Service Delivery"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Backoffice Service Delivery", "value": "SV-BO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-BO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1277295 - ST-A/PI: \"Usage Statistic\" in \"Tools for Upgrade\""}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The \"Object Usage Statstics\" are part of the \"Tools for Upgrade\" in ST13. If the Performance Database retention time in ST03 ist sett to 1 for a requested period (typically Month), the Object Usage Statistic is always empty.<br />Since this tool is used in Upgrade Assessment and SAP Software Change Management as well, the checks \"Top Customer Programms using Hints\" and \"Used programs\" are always empty for systems with retention time 1.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Wrong calculation of start date when calling API.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>To correct the program, you must insert some parts in the source code of the /SSA/CAU report. Therefore the most current ST-A/PI 01 L must be<br />available in all systems of the transportation landscape in which the source code change is supposed to be made.<br />After you implement the ST-A/PI start the /SSF/SAO_UTILS report on ALL affected systems in transaction SE38, select the first option 'Uncomment/Recomment analyis coding for additional components', and execute this report. This must be done BEFORE you change the source code.<br />Finally implement the source code changes attached to this note and<br />release the transport.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D024259)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D036979)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001277295/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001277295/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001277295/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001277295/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001277295/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001277295/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001277295/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001277295/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001277295/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1274306", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Solution Transition Assessment - Preparation Note", "RefUrl": "/notes/1274306"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1077981", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Upgrade Assessment Preparation Note", "RefUrl": "/notes/1077981 "}, {"RefNumber": "1274306", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Solution Transition Assessment - Preparation Note", "RefUrl": "/notes/1274306 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST-A/PI", "From": "01K_BCO46B", "To": "01L_BCO46B", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01K_BCO46C", "To": "01L_BCO46C", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01K_BCO46D", "To": "01L_BCO46D", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01K_BCO610", "To": "01L_BCO610", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01K_BCO620", "To": "01L_BCO620", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01K_BCO640", "To": "01L_BCO640", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01K_R3_40B", "To": "01L_R3_40B", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01K_R3_45B", "To": "01L_R3_45B", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01K_R3_46B", "To": "01L_R3_46B", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01K_R3_46C", "To": "01L_R3_46C", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01K_R3_470", "To": "01L_R3_470", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01K_APO30A", "To": "01L_APO30A", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01K_APO310", "To": "01L_APO310", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01K_SCM400", "To": "01L_SCM400", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01K_CRM300", "To": "01L_CRM300", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01K_CRM315", "To": "01L_CRM315", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01K_CRM400", "To": "01L_CRM400", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01K_SCM410", "To": "01L_SCM410", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01K_ECC500", "To": "01L_ECC500", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01K_BCO700", "To": "01L_BCO700", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01K_ECC600", "To": "01L_ECC600", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01K_SCM500", "To": "01L_SCM570", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01K_BCO710", "To": "01L_BCO710", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01K_CRM560", "To": "01L_CRM570", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "ST-A/PI", "NumberOfCorrin": 1, "URL": "/corrins/0001277295/389"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}