{"Request": {"Number": "1085152", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 312, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000006439062017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001085152?language=E&token=961A8ED3D9BEF89BFE66201A9152BE53"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001085152", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001085152/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1085152"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.09.2007"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PART-ISHMED"}, "SAPComponentKeyText": {"_label": "Component", "value": "Clinical System i.s.h.med"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Partner solutions", "value": "XX-PART", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Clinical System i.s.h.med", "value": "XX-PART-ISHMED", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART-ISHMED*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1085152 - Technical Adaptation"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note contains technical adaptations.<br />You do not need to implement this note urgently unless it is a prerequisite for another note (follow-up note).</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>This problem is caused by a program error.<br />The following Notes are prequisite to this Note.<br />1080577<br />1075750<br />1071848<br />1085127<br />1072723</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>We recommend that you only implement this note if required (follow-up note).&#x00A0;&#x00A0;Otherwise, these technical adaptations are automatically available once you implement SAP ECC Industry Extension Healthcare 6.0 Support Package 11.<br /><br />Unpack the attached file HW1085152_600_1.zip for i.s.h.med/IS-H Version 6.00.<br /><br />Unpack the attached file HW1085152_600_2.zip for i.s.h.med/IS-H Version 6.00.<br /><br />Note that you cannot use the SAP Support Portal to download the attached file. Instead, go to the SAP Service Marketplace to download the file (For more information about importing attachments see Notes 480180 and 13719 ).<br /><br />Import the unpacked orders in your system.<br />Implement the attached source code corrections.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-PART-ISHMED-ORD (Service Management i.s.h.med)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5040526)"}, {"Key": "Processor                                                                                           ", "Value": "C5060040"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001085152/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001085152/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001085152/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001085152/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001085152/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001085152/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001085152/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001085152/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001085152/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "HW1085152_600_1.zip", "FileSize": "743", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000501272007&iv_version=0004&iv_guid=532812E8FC5D7F448D0F0D3A02592312"}, {"FileName": "HW1085152_600_2.zip", "FileSize": "78", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000501272007&iv_version=0004&iv_guid=59712470FE46EA4FA732DE2DDFD48D74"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1100019", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical Adaptation", "RefUrl": "/notes/1100019"}, {"RefNumber": "1096503", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical Adaptation", "RefUrl": "/notes/1096503"}, {"RefNumber": "1086988", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical Adaptation", "RefUrl": "/notes/1086988"}, {"RefNumber": "1086288", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical Adaptation", "RefUrl": "/notes/1086288"}, {"RefNumber": "1085127", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clinical orders: Configuration of clinical appl. component", "RefUrl": "/notes/1085127"}, {"RefNumber": "1080577", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical Adaptation", "RefUrl": "/notes/1080577"}, {"RefNumber": "1075750", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical adaptations", "RefUrl": "/notes/1075750"}, {"RefNumber": "1072723", "RefComponent": "IS-H", "RefTitle": "Planning: Reader Classes for IS-H Functions", "RefUrl": "/notes/1072723"}, {"RefNumber": "1071848", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical Adaptation", "RefUrl": "/notes/1071848"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1100019", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical Adaptation", "RefUrl": "/notes/1100019 "}, {"RefNumber": "1096503", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical Adaptation", "RefUrl": "/notes/1096503 "}, {"RefNumber": "1086988", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical Adaptation", "RefUrl": "/notes/1086988 "}, {"RefNumber": "1086288", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical Adaptation", "RefUrl": "/notes/1086288 "}, {"RefNumber": "1085127", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clinical orders: Configuration of clinical appl. component", "RefUrl": "/notes/1085127 "}, {"RefNumber": "1075750", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical adaptations", "RefUrl": "/notes/1075750 "}, {"RefNumber": "1080577", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical Adaptation", "RefUrl": "/notes/1080577 "}, {"RefNumber": "1071848", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical Adaptation", "RefUrl": "/notes/1071848 "}, {"RefNumber": "1072723", "RefComponent": "IS-H", "RefTitle": "Planning: Reader Classes for IS-H Functions", "RefUrl": "/notes/1072723 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-H", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "602", "To": "602", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "IS-H 600", "SupportPackage": "SAPK-60011INISH", "URL": "/supportpackage/SAPK-60011INISH"}, {"SoftwareComponentVersion": "IS-H 602", "SupportPackage": "SAPK-60201INISH", "URL": "/supportpackage/SAPK-60201INISH"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 1, "URL": "/corrins/0001085152/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1015066 ", "URL": "/notes/1015066 ", "Title": "Clinical Order: Report RN1_SYNC_CORDERTYPES", "Component": "XX-PART-ISHMED"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}