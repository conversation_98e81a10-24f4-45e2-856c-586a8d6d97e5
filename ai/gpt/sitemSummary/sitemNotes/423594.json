{"Request": {"Number": "423594", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 326, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015046082017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000423594?language=E&token=B46702CC2D81ED68AE605D41B11EFE52"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000423594", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000423594/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "423594"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 13}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.05.2002"}, "SAPComponentKey": {"_label": "Component", "value": "SCM-TEC"}, "SAPComponentKeyText": {"_label": "Component", "value": "In Case of LiveCache Problems: Please use SCM-APO-LCA"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Supply Chain Management", "value": "SCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "In Case of LiveCache Problems: Please use SCM-APO-LCA", "value": "SCM-TEC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-TEC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "423594 - APO Support Package 14 for APO Release 3.0A"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note describes how to import APO Support Package 14 (SAPKY30A14) for APO Release 3.0A in your system.<br />APO Support Package 14 contains all notes created from 19 June 2001 to 20 July 2001. These contain corrections in the following areas:<br /><br /><B>Release restrictions</B><br />Please refer to Note <B>427957</B>. It contains known release restrictions as of Support Release 3 (Support Package 14).<br /><br /><B>Newly recommended procedure with Transaction SPAM</B><br />As of maintenance level APO Support Release 2 (SR2 includes APO Support Packages 1 - 8), we recommend you to import several APO Support Packages (from Support Package 9 to the current Support Package) always together in one queue.<br /><br /><B>Upgrade information on SAPGUI</B><br />Maintenance for frontend Release 4.6C expires on 31 December 2000. For this reason, we recommend that you upgrade to Frontend Release 4.6D. For more information, see notes:</p> <UL><LI>0147519<br />Maintenance strategy 'SAPGUI'</LI></UL> <UL><LI>0361222<br />SapPatch: Importing of GUI patches<br /></LI></UL> <p><B>Kernel</B><br />Always import the latest 46D kernel.<br />For more information, see Note</p> <UL><LI>0373047<br />Error in field input processing<br /></LI></UL> <p><B>If you have APO Support Package 6 or lower in your APO system, refer to the following notes:</B></p> <UL><LI>314218<br />Converting movement data between the Support Packages and</LI></UL> <UL><LI>361635<br />liveCache upgrade APO 3.0A SP7<br /><br /><B>before importing APO Support Package 7. You risk losing data if you follow another procedure</B>.<br /></LI></UL> <UL><LI> APO Support Package 14 for APO Release 3.0A (SAPKY30A14)<br /></LI></UL> <UL><LI> OCX update: none<br /></LI></UL> <UL><LI> Update of liveCache version: 7.2.5 Build 7<br /></LI></UL> <UL><LI> COM Routines<br /></LI></UL> <UL><LI> Optimizer corrections<br /></LI></UL> <UL><LI> Additional notes<br /></LI></UL> <p>APO Support Package 14 for APO Release 3.0A is available in the following languages: German, English, French, Spanish, Danish, Finnish, Hungarian, Italian, Japanese, Korean, Dutch, Norwegian, Polish, Portuguese, Swedish, Russian and Czech.<br />These languages are contained in APO Support Package 14.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SAPKY30A01, SAPKY30A02, SAPKY30A03, SAPKY30A04, SAPKY30A05, SAPKY30A06, SAPKY30A06, SAPKY30A07, SAPKY30A08, SAPKY30A09, SAPKY30A10, SAPKY30A11, SAPKY30A12, SAPKY30A13, SAPKY30A14, APO Support Package, APO patch, APO Release 3.0A.<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>APO Support Package 14 (SAPKY30A14) for APO Release 3.0A requires a complete installation / upgrade (delivery 15 May 2000).<br />You must also import APO Support Packages&#x00A0;&#x00A0;1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13 and 14 for APO Release 3.0A.<br /><br />If the system was set up with <B>Support Release 1</B> for APO Release<br />3.0A, you must also import APO Support Packages 5, 6, 7, 8, 9, 10, 11, 12, 13 and 14 for APO Release 3.0A.<br /><br />If the system was set up with <B>Support Release 2</B> for APO Release<br />3.0A, you only have to import Support Packages 9, 10, 11, 12, 13, and 14 for Release 3.0A.<br /><br />========================================================================<br /></p> <UL><LI>Basis Support Packages for 4.6C</LI></UL> <p>For APO Support Package 14, it is absolutely essential to import Basis Support Packages up to and including Basis Support Package 22 (SAPKB46C22).</p> <UL><LI>ABA Support Packages for Basis 4.6C</LI></UL> <p>In addition to importing the Basis Support Packages, it is very important that you also import ABA Support Packages up to and including ABA Support Package 22 (SAPKA46C22).</p> <UL><LI>BW Support Packages ( 2.0B )</LI></UL> <p>For APO Support Package 14, it is absolutely essential to import the BW Support Packages up to and including BW Support Package 16 (SAPKW20B16).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Please note that <B>BW Support Packages must not be imported in one queue</B>!<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 484015 Importing BW Support Packages<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 328237 Importing patches in a BW 2.0B System<br /><br />Basis Support Packages / BW Support Packages are always imported separately from the APO Support Packages. Import the Basis Support Packages / BW Support Packages.<br />Then import the APO Support Package immediately afterwards.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>To import APO Support Package 14 completely, follow the steps below:</p> <OL>1. <B>SPAM - Update </B></OL> <p>Before you start importing the Support Packages, you must update the SPAM manager to the latest level. To do this, download the latest version of the SPAM-Update from the OSS or SAPNET.<br />Import the latest SPAM update into your system. For more information on this subject, see the initial screen of Transaction SPAM by selecting the 'i' key in the online documentation. (Help-&gt;Application help).</p> <OL>2. <B>APO corrections </B></OL> <p>You can download APO Support Package SAPKY30A14 from the Online Service System (OSS) or from the SAPNet. The URL is as follows: http://sapnet.sap.de/ocs-download. Follow the path: APO Support Packages -&gt; APO 3.0A. Download the APO Support Package SAPKY30A14. For more detailed information, refer to Note 83458.</p> <OL>3. <B>OCX update </B></OL> <p>The OCX files that you need for APO Support Package 14 for APO Release 3.0A are on the sapervX-FTP-Server. All other information on importing the new OCX files is contained in Note:<br />0422446&#x00A0;&#x00A0;APO Frontend patch<br /></p> <OL>4. <B>Updating to liveCache version 7.2.5 Build 7</B></OL> <p>Before updating the liveCache to version 7.2.5 Build 4 or higher, refer to Note 410002. Settings for MAXCPU as of liveCache 7.2.5 Build 4.<br /><br />The liveCache update is available on sapservX. The following directory contains the corresponding liveCache versions for the relevant platforms<br />ftp://sapservX/general/3rdparty/sapdb/LC_VERSIONS/&lt;Build&gt;//<br /><br /><B>Note that you must import a kernel patch &gt;= 579 for Release 46D if you are installing liveCache version 7.2.5 build 4 or higher. If you use a lower patch level, the kernel patch and the database interface will be incompatible </B>. Refer to Note 406248<br />406248 liveCache connect problems.<br /><br /><B>Important!! You must also import latest adaslib as described in Note</B><br />325402.<br /><br />Any other information that you require for updating the liveCache is contained in Note:<br />379051 Importing a liveCache version &gt;= 7.2.4 B15<br />After a successful liveCache update please refer to<br />Note 424886<br />Additional information on the UNIX liveCache and COM routines is contained in Note<br />0391746 APO SP, COM routines and liveCache versions on UNIX.<br />!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br />You do not need to initialize the liveCache for APO Support Package 14.<br />!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!</p><OL>5. <B>COM routines </B></OL> <p>The COM routines are available on the sapservX.<br />The file&#x00A0;&#x00A0;SAPCOM_30_n.SAR (n = release counter) is contained in the<br />directory ftp://sapservX/specific/apo/apo30/sp14/.<br />To import the new COM routines, follow the steps described in the notes below<br />423685&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPAPO 3.0 COM object Build 22<br />157265&#x00A0;&#x00A0;&#x00A0;&#x00A0;Replacing the COM objects for liveCache in APO 3.0<br />As of liveCache version 7.2.4, also refer to the following note:<br />336470&#x00A0;&#x00A0;&#x00A0;&#x00A0;Environment Var. DBROOT no longer exists</p> <OL>6. <B>Optimizer corrections </B></OL> <p>You can obtain the optimizer corrections from sapservX. The file <B>SAPAPO_n.SAR</B> (n = release counter) is contained in the<br />directory <B>ftp://sapservX/specific/apo/apo30/sp14/ </B>.<br />To import the new files, follow the instructions provided in the notes below:</p> <UL><LI>421952&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;APO 3.0 Optimizer Support Package 14</LI></UL> <UL><LI>300930&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;APO 3.0 Importing an Optimizer Version<br /></LI></UL> <OL>7. <B>Additional notes MUST be imported manually!</B><br /></OL> <p>Note 353197<br />User for synchronous RFC dialog as of Release 4.6<br />Note 352844<br />Authorizations for RFC users: APO &lt;-&gt; R/3<br />Note 147218<br />SAP APO Demand Planning - datamart ==&gt; BW Patches<br />Note 424212<br />Correction for note 416189<br /><B>Apply this note BEFORE working with the Demand Planning module!</B><br />Note 421023<br />Transport errors in Transaktion /sapapo/vscc<br />Note 410002<br />Setting for MAXCPU as of liveCache 7.2.5 Build 4<br />Note 428197<br />Error in SNP Optimizer APO 3.0A SP14<br /></p> <OL>8. <B>Please refer to the following notes, and implement them if they are relevant to you:</B></OL> <p><br /><B>Number&#x00A0;&#x00A0; Component&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Priority&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Valid</B><br />0427328&#x00A0;&#x00A0;APO-MD-RE Resource&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;medium&#x00A0;&#x00A0;&#x00A0;&#x00A0;SP14<br />0431374&#x00A0;&#x00A0;APO-SNP-OPT Optimizing the SNP plan&#x00A0;&#x00A0;&#x00A0;&#x00A0; high&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SP14-15<br />0431798&#x00A0;&#x00A0;APO-SNP-DPL Deployment&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;high&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SP14-15<br />0432406&#x00A0;&#x00A0;APO-SNP-OPT Optimizing the SNP plan&#x00A0;&#x00A0;&#x00A0;&#x00A0; high&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SP14-15<br />0433949&#x00A0;&#x00A0;APO-SNP-OPT Optimizing the SNP plan&#x00A0;&#x00A0;&#x00A0;&#x00A0; high&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SP14-15<br />0436229&#x00A0;&#x00A0;APO-SNP Supply Network Planning (SNP)&#x00A0;&#x00A0; high&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SP14-16<br />0440620&#x00A0;&#x00A0;APO-SDM-CTM Capable-to-Match&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;low&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SP14-16<br />0443012&#x00A0;&#x00A0;APO-SNP-OPT Optimizing the SNP plan&#x00A0;&#x00A0;&#x00A0;&#x00A0; high&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SP14-16<br />0446640&#x00A0;&#x00A0;APO-SDM-CTM Capable-to-Match&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;low&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SP14-17<br />0447268&#x00A0;&#x00A0;APO-SDM-CTM Capable-to-Match&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;medium&#x00A0;&#x00A0;&#x00A0;&#x00A0;SP14-17<br />0448557&#x00A0;&#x00A0;APO-SDM-CTM Capable-to-Match&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;high&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SP14-17<br />0451124&#x00A0;&#x00A0;APO-MD-LO Location&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;high&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SP14-17<br /></p> <OL>9. <B>Actions after importing a Support Package</B><br /></OL> <p>It is recommended that you execute Transaction <B>SGEN</B> after importing Support Packages. If you are importing several Support Packages in succession, you only need to follow the procedure once after importing the last Support Package.</p> <UL><LI>Call Transaction <B>SGEN</B>.</LI></UL> <UL><LI>Select the option 'Regeneration of existing loads'.</LI></UL> <UL><LI>Select the option 'Only generate objects with invalid loads'.<br /></LI></UL> <p>This procedure ensures that the objects changed by the Support Package are regenerated.<br />Refer to the documentation on the initial screen of Transaction <B>SGEN</B> for more information.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-UPG-OCS (Online Corr. Support (Tools:Supp.-Pack., Addon Install/Upgr))"}, {"Key": "Responsible                                                                                         ", "Value": "D027030"}, {"Key": "Processor                                                                                           ", "Value": "D027030"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000423594/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000423594/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000423594/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000423594/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000423594/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000423594/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000423594/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000423594/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000423594/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "83458", "RefComponent": "BC-UPG-OCS", "RefTitle": "@19@OCS Info: Patch download from SAP Service Marketplace", "RefUrl": "/notes/83458"}, {"RefNumber": "533076", "RefComponent": "BC-DB-LVC", "RefTitle": "Importing a liveCache version as of 7.2.5 B18", "RefUrl": "/notes/533076"}, {"RefNumber": "523217", "RefComponent": "BW", "RefTitle": "SAPBWNews for BW 3.0A Support Package 12", "RefUrl": "/notes/523217"}, {"RefNumber": "458702", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.0A Support Package 10", "RefUrl": "/notes/458702"}, {"RefNumber": "456549", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 2.0B Support Package 22", "RefUrl": "/notes/456549"}, {"RefNumber": "451124", "RefComponent": "SCM-APO-MD-LO", "RefTitle": "Dup. entries in loc. text trans. APO 3.0 SP18", "RefUrl": "/notes/451124"}, {"RefNumber": "448557", "RefComponent": "SCM-APO-SDM-CTM", "RefTitle": "Safety stock and receipt quantities", "RefUrl": "/notes/448557"}, {"RefNumber": "447268", "RefComponent": "SCM-APO-SDM-CTM", "RefTitle": "Safety stock and requested quantity", "RefUrl": "/notes/447268"}, {"RefNumber": "446640", "RefComponent": "SCM-APO-SDM-CTM", "RefTitle": "CTM: Orders are not deleted (NetChange)", "RefUrl": "/notes/446640"}, {"RefNumber": "443012", "RefComponent": "SCM-APO-SNP-OPT", "RefTitle": "Requirements are taken into account twice", "RefUrl": "/notes/443012"}, {"RefNumber": "440620", "RefComponent": "SCM-APO-SDM-CTM", "RefTitle": "Safety stock planning", "RefUrl": "/notes/440620"}, {"RefNumber": "436229", "RefComponent": "SCM-APO-SNP", "RefTitle": "Validity of the components ignored", "RefUrl": "/notes/436229"}, {"RefNumber": "433949", "RefComponent": "SCM-APO-SNP-OPT", "RefTitle": "Requirements in the past", "RefUrl": "/notes/433949"}, {"RefNumber": "432406", "RefComponent": "SCM-APO-SNP-OPT", "RefTitle": "Transport quantities more than ATD accesses", "RefUrl": "/notes/432406"}, {"RefNumber": "431798", "RefComponent": "SCM-APO-SNP-DPL", "RefTitle": "Doubled requirements for deployment optimizer", "RefUrl": "/notes/431798"}, {"RefNumber": "431374", "RefComponent": "SCM-APO-SNP-OPT", "RefTitle": "Display Results", "RefUrl": "/notes/431374"}, {"RefNumber": "427957", "RefComponent": "SCM-APO", "RefTitle": "Release restrictions for APO 3.0A Support Release 3", "RefUrl": "/notes/427957"}, {"RefNumber": "427328", "RefComponent": "SCM-APO-MD-RE", "RefTitle": "Cross-period activities not in model consistency", "RefUrl": "/notes/427328"}, {"RefNumber": "424886", "RefComponent": "BC-DB-LVC", "RefTitle": "Parameter values as of liveCache Version 7.2.5", "RefUrl": "/notes/424886"}, {"RefNumber": "423685", "RefComponent": "BC-DB-LCA", "RefTitle": "SAPAPO 3.0 COM object Build 22", "RefUrl": "/notes/423685"}, {"RefNumber": "421952", "RefComponent": "SCM-APO-OPT", "RefTitle": "APO 3.0 optimizer Support Package 14", "RefUrl": "/notes/421952"}, {"RefNumber": "421023", "RefComponent": "SCM-APO-VS-BF", "RefTitle": "Transport error in Transaction /sapapo/vscc", "RefUrl": "/notes/421023"}, {"RefNumber": "410002", "RefComponent": "BC-DB-LVC", "RefTitle": "Setting for MAXCPU as of liveCache 7.2.5 Build 4", "RefUrl": "/notes/410002"}, {"RefNumber": "406248", "RefComponent": "BC-DB-LCA", "RefTitle": "liveCache Connect problems", "RefUrl": "/notes/406248"}, {"RefNumber": "391746", "RefComponent": "SCM-TEC", "RefTitle": "COM Routines and liveCache Versions on UNIX", "RefUrl": "/notes/391746"}, {"RefNumber": "373047", "RefComponent": "BC-ABA-SC", "RefTitle": "Error in field input processing", "RefUrl": "/notes/373047"}, {"RefNumber": "361635", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/361635"}, {"RefNumber": "361222", "RefComponent": "BC-FES-INS", "RefTitle": "SapPatch: Importing GUI Patches", "RefUrl": "/notes/361222"}, {"RefNumber": "353197", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/353197"}, {"RefNumber": "352844", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/352844"}, {"RefNumber": "336470", "RefComponent": "BC-DB-SDB", "RefTitle": "Environment var. DBROOT no longer exists", "RefUrl": "/notes/336470"}, {"RefNumber": "328237", "RefComponent": "BW-SYS", "RefTitle": "Importing Support Packages into a BW 2.0B system", "RefUrl": "/notes/328237"}, {"RefNumber": "325402", "RefComponent": "BC-DB-LVC", "RefTitle": "dbadaslib/dbsdbslib: How do I apply a patch?", "RefUrl": "/notes/325402"}, {"RefNumber": "314218", "RefComponent": "BC-DB-LCA", "RefTitle": "Transaction data conversion between SPs for APO 3.0", "RefUrl": "/notes/314218"}, {"RefNumber": "157265", "RefComponent": "BC-DB-LVC", "RefTitle": "Exchanging COM objects for liveCache in APO 3.0", "RefUrl": "/notes/157265"}, {"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519"}, {"RefNumber": "147218", "RefComponent": "SCM-APO", "RefTitle": "SAP APO Demand Planning - datamart ==> BW patches", "RefUrl": "/notes/147218"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519 "}, {"RefNumber": "410002", "RefComponent": "BC-DB-LVC", "RefTitle": "Setting for MAXCPU as of liveCache 7.2.5 Build 4", "RefUrl": "/notes/410002 "}, {"RefNumber": "424886", "RefComponent": "BC-DB-LVC", "RefTitle": "Parameter values as of liveCache Version 7.2.5", "RefUrl": "/notes/424886 "}, {"RefNumber": "523217", "RefComponent": "BW", "RefTitle": "SAPBWNews for BW 3.0A Support Package 12", "RefUrl": "/notes/523217 "}, {"RefNumber": "443012", "RefComponent": "SCM-APO-SNP-OPT", "RefTitle": "Requirements are taken into account twice", "RefUrl": "/notes/443012 "}, {"RefNumber": "436229", "RefComponent": "SCM-APO-SNP", "RefTitle": "Validity of the components ignored", "RefUrl": "/notes/436229 "}, {"RefNumber": "433949", "RefComponent": "SCM-APO-SNP-OPT", "RefTitle": "Requirements in the past", "RefUrl": "/notes/433949 "}, {"RefNumber": "432406", "RefComponent": "SCM-APO-SNP-OPT", "RefTitle": "Transport quantities more than ATD accesses", "RefUrl": "/notes/432406 "}, {"RefNumber": "431798", "RefComponent": "SCM-APO-SNP-DPL", "RefTitle": "Doubled requirements for deployment optimizer", "RefUrl": "/notes/431798 "}, {"RefNumber": "431374", "RefComponent": "SCM-APO-SNP-OPT", "RefTitle": "Display Results", "RefUrl": "/notes/431374 "}, {"RefNumber": "373047", "RefComponent": "BC-ABA-SC", "RefTitle": "Error in field input processing", "RefUrl": "/notes/373047 "}, {"RefNumber": "406248", "RefComponent": "BC-DB-LCA", "RefTitle": "liveCache Connect problems", "RefUrl": "/notes/406248 "}, {"RefNumber": "423685", "RefComponent": "BC-DB-LCA", "RefTitle": "SAPAPO 3.0 COM object Build 22", "RefUrl": "/notes/423685 "}, {"RefNumber": "325402", "RefComponent": "BC-DB-LVC", "RefTitle": "dbadaslib/dbsdbslib: How do I apply a patch?", "RefUrl": "/notes/325402 "}, {"RefNumber": "361222", "RefComponent": "BC-FES-INS", "RefTitle": "SapPatch: Importing GUI Patches", "RefUrl": "/notes/361222 "}, {"RefNumber": "83458", "RefComponent": "BC-UPG-OCS", "RefTitle": "@19@OCS Info: Patch download from SAP Service Marketplace", "RefUrl": "/notes/83458 "}, {"RefNumber": "328237", "RefComponent": "BW-SYS", "RefTitle": "Importing Support Packages into a BW 2.0B system", "RefUrl": "/notes/328237 "}, {"RefNumber": "391746", "RefComponent": "SCM-TEC", "RefTitle": "COM Routines and liveCache Versions on UNIX", "RefUrl": "/notes/391746 "}, {"RefNumber": "427957", "RefComponent": "SCM-APO", "RefTitle": "Release restrictions for APO 3.0A Support Release 3", "RefUrl": "/notes/427957 "}, {"RefNumber": "533076", "RefComponent": "BC-DB-LVC", "RefTitle": "Importing a liveCache version as of 7.2.5 B18", "RefUrl": "/notes/533076 "}, {"RefNumber": "456549", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 2.0B Support Package 22", "RefUrl": "/notes/456549 "}, {"RefNumber": "157265", "RefComponent": "BC-DB-LVC", "RefTitle": "Exchanging COM objects for liveCache in APO 3.0", "RefUrl": "/notes/157265 "}, {"RefNumber": "147218", "RefComponent": "SCM-APO", "RefTitle": "SAP APO Demand Planning - datamart ==> BW patches", "RefUrl": "/notes/147218 "}, {"RefNumber": "458702", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.0A Support Package 10", "RefUrl": "/notes/458702 "}, {"RefNumber": "314218", "RefComponent": "BC-DB-LCA", "RefTitle": "Transaction data conversion between SPs for APO 3.0", "RefUrl": "/notes/314218 "}, {"RefNumber": "451124", "RefComponent": "SCM-APO-MD-LO", "RefTitle": "Dup. entries in loc. text trans. APO 3.0 SP18", "RefUrl": "/notes/451124 "}, {"RefNumber": "440620", "RefComponent": "SCM-APO-SDM-CTM", "RefTitle": "Safety stock planning", "RefUrl": "/notes/440620 "}, {"RefNumber": "447268", "RefComponent": "SCM-APO-SDM-CTM", "RefTitle": "Safety stock and requested quantity", "RefUrl": "/notes/447268 "}, {"RefNumber": "448557", "RefComponent": "SCM-APO-SDM-CTM", "RefTitle": "Safety stock and receipt quantities", "RefUrl": "/notes/448557 "}, {"RefNumber": "446640", "RefComponent": "SCM-APO-SDM-CTM", "RefTitle": "CTM: Orders are not deleted (NetChange)", "RefUrl": "/notes/446640 "}, {"RefNumber": "421952", "RefComponent": "SCM-APO-OPT", "RefTitle": "APO 3.0 optimizer Support Package 14", "RefUrl": "/notes/421952 "}, {"RefNumber": "427328", "RefComponent": "SCM-APO-MD-RE", "RefTitle": "Cross-period activities not in model consistency", "RefUrl": "/notes/427328 "}, {"RefNumber": "421023", "RefComponent": "SCM-APO-VS-BF", "RefTitle": "Transport error in Transaction /sapapo/vscc", "RefUrl": "/notes/421023 "}, {"RefNumber": "336470", "RefComponent": "BC-DB-SDB", "RefTitle": "Environment var. DBROOT no longer exists", "RefUrl": "/notes/336470 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APO", "From": "30A", "To": "30A", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}