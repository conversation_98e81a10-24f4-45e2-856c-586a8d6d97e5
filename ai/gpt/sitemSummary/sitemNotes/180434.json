{"Request": {"Number": "180434", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 274, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000911502017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000180434?language=E&token=ADC2644E453454AA4A78B1D13151332A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000180434", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000180434/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "180434"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 19}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Workaround of missing functionality"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.01.2004"}, "SAPComponentKey": {"_label": "Component", "value": "MM-PUR-VM-SET"}, "SAPComponentKeyText": {"_label": "Component", "value": "Subsequent Settlement"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Purchasing", "value": "MM-PUR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Vendor-Material Relationships and Conditions", "value": "MM-PUR-VM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Subsequent Settlement", "value": "MM-PUR-VM-SET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM-SET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "180434 - Validation of relevant documents before archiving"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You archive documents that are still relevant for subsequent settlement. Then, a detailed statement and/or a setup of statistical data can no longer be executed.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Archiving, MM_EKKO, WLF, WZR, WREG, MM_DOCUMNT, subsequent settlement, residence time</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The functions 'Detailed statement' (Transaction MEB8), 'Recompilation of bus.vol. data' (Transaction MEBA) and 'Recompilation of incomes' only work if the documents or settlement documents which are relevant to business volume and belong to an arrangement have not yet been archived.<br /><br />The documents relevant to business volume include:<br /><br /> 1. a) Purchase orders / scheduling agreements&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; MM_EKKO<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;b) Vendor billing documents&#x00A0;&#x00A0;&#x00A0;&#x00A0;(as of Release 4.5) WLF<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;c) Settlement requests&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; (as of Release 4.5) WZR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;d) Settlement request lists&#x00A0;&#x00A0;&#x00A0;&#x00A0;(as of Release 4.5) WREG<br /><br />The settlement documents include:<br /><br /> 2. a) Customer billing document&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SD_VBRK<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;b) Vendor billing document&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;WLF<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;c) Credit memos&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; (up to Release 4.5) FI_DOCUMENT<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>To implement a verification in the system for the document relevant for subsequent settlement, proceed as follows.<br />Documents that are still relevant for arrangements in purchasing, can only be archived if the affected arrangements are already settled in the system and a residence time which can be set by default is expired.<br />Note:<br />The function 'Subsequent business vol.update' requires that all relevant documents exist in the system. The protection against a premature archiving only works after at least one update has been carried out! At present there are no separate residence times with regard to the subsequent update.<br />This note contains corrections which are delivered by Hot Package but are not active.<br />In addition, you need two new database indexes. Read also Note 169822.<br /><br />Program SDVBRKWR is no longer supplied by patches in Releases 3.0D to 3.1H. Note that changes to this object can only be made by correction instruction. The current versions of archiving reports S3VBRKPT and S3VBRKWR that are available instead are placed on the SAPSERVx. The corrections of these programs then correspond to the status of Release 3.1I if SAPNet R/3 Frontend (formerly OSS) Note 93715 has already been implemented in your system.<br /></p> <b>Procedure:</b><br /> <p><br />If function group WN48 already exists in your system, the corresponding Hot Package has already been applied and you can skip the following point:<br /><br />------------------------------------------------------------------------</p> <b>1. Function group WN48 does not yet exist.</b><br /> <p><br />Create the following objects:<br /></p> <UL><UL><LI>Function group WN48:</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Title: 'Archiving: Check Relevance'<br /><br />the following function modules within the function group:<br />(up to and including version 4.0B)</p> <UL><UL><LI>MM_ARRANG_CHECK_COND_ARCHIVE</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0; Import parameter:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Parameter&#x00A0;&#x00A0;Ref. field/structure&#x00A0;&#x00A0;Optional<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; I_BLTYPN&#x00A0;&#x00A0; EKBO-BLTYPN&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; X<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;I_EBELN&#x00A0;&#x00A0;&#x00A0;&#x00A0;EKBO-EBELN&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;X<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;I_KNUMA&#x00A0;&#x00A0;&#x00A0;&#x00A0;KONA-KNUMA&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;X<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; I_BLTYPA&#x00A0;&#x00A0; EBOX-BLTYPA&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; X<br />&#x00A0;&#x00A0;Exceptions:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; AGREEMENT_NOT_SETTLED<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; AGREEMENT_DATBI_VALID<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; AGREEMENT_RESZT_VALID<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; NO_EKBO_INDEX<br /><br />(as of version 4.5A)<br />- MM_ARRANG_CHECK_COND_ARCHIVE<br />&#x00A0;&#x00A0;Import parameters:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Parameter&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Ref. field/structure&#x00A0;&#x00A0;&#x00A0;&#x00A0;Optional<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; I_BLTYPN&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;EKBO-BLTYPN&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;X<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; I_EBELN&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;EKBO-EBELN&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;X<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; I_KNUMA&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;KONA-KNUMA&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;X<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; I_BLTYPA&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;EBOX-BLTYPA&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;X<br />&#x00A0;&#x00A0;Exceptions:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; AGREEMENT_NOT_SETTLED<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; AGREEMENT_DATBI_VALID<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; AGREEMENT_RESZT_VALID<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; NO_EKBO_INDEX<br /><br /></p> <UL><UL><LI>MM_ARRANG_CHECK_CREDOC_ARCHIVE</LI></UL></UL> <p>&#x00A0;&#x00A0;Import parameter:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Parameter&#x00A0;&#x00A0;&#x00A0;&#x00A0;Ref. field/structure&#x00A0;&#x00A0; Optional<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; I_BKPF&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; BKPF<br />&#x00A0;&#x00A0;Export parameters:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Parameter&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Type<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; E_NO_CHECK_BONUS&#x00A0;&#x00A0; C<br />&#x00A0;&#x00A0;Exceptions:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; DOCUMENT_INCOMPLETE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; AGREEMENT_NOT_SETTLED<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; AGREEMENT_DATBI_VALID<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; AGREEMENT_RESZT_VALID<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; NO_EBOX_INDEX<br /></p> <UL><UL><LI>MM_ARRANG_CHECK_EBOX_INDEX</LI></UL></UL> <p>&#x00A0;&#x00A0;Exceptions:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; NO_EBOX_INDEX<br /></p> <UL><UL><LI>MM_ARRANG_CHECK_EKBO_INDEX</LI></UL></UL> <p>&#x00A0;&#x00A0;Exceptions:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; NO_EKBO_INDEX<br /><br />Create the following Include programs within the function group:</p> <UL><UL><LI>LWN48CON</LI></UL></UL> <UL><UL><LI>LWN48F01</LI></UL></UL> <p><br />Copy the source code specified in the corrections to the created objects and carry out all corrections. Create the following text elements depending on your release:<br /><br />Release 3.0D to 4.5B for program 'SAPFF048':<br /><br />#&#x00A0;&#x00A0;&#x00A0;&#x00A0; Length Text<br />----- ----- -------------------------------------------------<br />327&#x00A0;&#x00A0;&#x00A0;&#x00A0;42&#x00A0;&#x00A0;&#x00A0;&#x00A0;MM-PUR: see SAPNet R/3 Frontend (formerly OSS) Note 169822<br />328&#x00A0;&#x00A0;&#x00A0;&#x00A0;42&#x00A0;&#x00A0;&#x00A0;&#x00A0;MM-PUR: Settlement document for agreement<br /><br />Release 3.1I to 4.5B for program 'S3VBRKPT':<br /><br />#&#x00A0;&#x00A0;&#x00A0;&#x00A0;Length Text<br />---- ----- --------------------------------------------------<br />020&#x00A0;&#x00A0; 48&#x00A0;&#x00A0; Arrangement &amp; is not totally calculated<br />021&#x00A0;&#x00A0; 48&#x00A0;&#x00A0; Residence time for arrangement &amp; has not yet expired<br />022&#x00A0;&#x00A0; 48&#x00A0;&#x00A0; Agreement &amp; is still valid<br /><br />Release 4.5A to 4.5B for function group 'FACU':<br /><br />#&#x00A0;&#x00A0; Text&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Length<br />--- ------------------------------------------&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -----<br />121 Subsequent settlement/MM: Incomplete document&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;40<br />122 Subsequent settlement/MM: Arrangement active&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 40<br />123 Subsequent settlement/MM: Arrangement valid&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;40<br />124 Subsequent settlement/MM: Res.time for arrangement&#x00A0;&#x00A0; 40<br />125 MM-PURE-VM-SET: see SAPNet R/3 Frontend (formerly OSS) Note 169822 &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;40<br /><br />Up to and including Release 4.5A, create the following messages in message class MN:<br /><br />#&#x00A0;&#x00A0; Text<br />--------<br />692 System administration: Create Index on EBOX, see SAPNet R/3 Frontend (formerly OSS) Note 0169822!<br />693 System administration: Create Index on EKBO, see SAPNet R/3 Frontend (formerly OSS) Note 0169822!<br />694 Object cannot be archived: Arrangement &amp;1 not yet settled<br />695 Object cannot be archived: Arrangement &amp;1 still valid<br />696 Object cannot be archived: &amp;1 not expired<br /><br />As of and including Release 4.5A, create the following messages in message class NAA:<br /><br />#&#x00A0;&#x00A0; Text<br />--------<br />530 Object cannot be archived: Arrangement &amp;1 not yet settled<br />531 Object cannot be archived: Arrangement &amp;1 still valid<br />532 Object cannot be archived: &amp;1 not expired<br />534 Activate index for table EBOX (contact system<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;administrator)<br />535 Activate the index for table EKBO (contact system<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;administrator)<br />------------------------------------------------------------------------</p> <b>2.Function group WN48 already exists.</b><br /> <p><br />Continue here if the objects listed above already exist since they have already been imported with Hot Package.<br /><br />In order to activate the verification of archiving, change the following constants in Include LWN48CON<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; CON_CHECK_EKKO (for archiving object MM_EKKO)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; CON_CHECK_VBRK (for archiving object SD_VBRK)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; CON_CHECK_BKPF (for archiving object FI_DOCUMNT)<br /><br />to value 'X'. In addition, you can define a residence time in days for the individual archiving objects after which the documents still remain in the system after the valid-to date has expired:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; CON_RESZT_EKKO (for archiving object MM_EKKO)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; CON_RESZT_VBRK (for archiving object SD_VBRK)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; CON_RESZT_BKPF (for archiving object FI_DOCUMNT)<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "MOVE"}, {"Key": "Transaction codes", "Value": "HIER"}, {"Key": "Transaction codes", "Value": "MEB4"}, {"Key": "Transaction codes", "Value": "MEBA"}, {"Key": "Transaction codes", "Value": "MEB8"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D025477)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000180434/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000180434/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000180434/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000180434/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000180434/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000180434/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000180434/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000180434/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000180434/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "40147", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 3.0", "RefUrl": "/notes/40147"}, {"RefNumber": "307643", "RefComponent": "MM-SRV", "RefTitle": "MM-SRV: Archiving of purchase orders with blank items", "RefUrl": "/notes/307643"}, {"RefNumber": "183379", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Composite SAP note subsequent settlement (Purchasing) 4.6", "RefUrl": "/notes/183379"}, {"RefNumber": "169822", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Archiving: Messages NAA534 and NAA535", "RefUrl": "/notes/169822"}, {"RefNumber": "152725", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 4.5", "RefUrl": "/notes/152725"}, {"RefNumber": "104668", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 4.0", "RefUrl": "/notes/104668"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "401318", "RefComponent": "MM-PUR-GF-ARC", "RefTitle": "Archiving: Additional information", "RefUrl": "/notes/401318 "}, {"RefNumber": "169822", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Archiving: Messages NAA534 and NAA535", "RefUrl": "/notes/169822 "}, {"RefNumber": "307643", "RefComponent": "MM-SRV", "RefTitle": "MM-SRV: Archiving of purchase orders with blank items", "RefUrl": "/notes/307643 "}, {"RefNumber": "183379", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Composite SAP note subsequent settlement (Purchasing) 4.6", "RefUrl": "/notes/183379 "}, {"RefNumber": "104668", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 4.0", "RefUrl": "/notes/104668 "}, {"RefNumber": "152725", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 4.5", "RefUrl": "/notes/152725 "}, {"RefNumber": "40147", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 3.0", "RefUrl": "/notes/40147 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30D", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 30D", "SupportPackage": "SAPKH30D94", "URL": "/supportpackage/SAPKH30D94"}, {"SoftwareComponentVersion": "SAP_APPL 30F", "SupportPackage": "SAPKH30F90", "URL": "/supportpackage/SAPKH30F90"}, {"SoftwareComponentVersion": "SAP_HR 30F", "SupportPackage": "SAPKE30F90", "URL": "/supportpackage/SAPKE30F90"}, {"SoftwareComponentVersion": "SAP_APPL 31H", "SupportPackage": "SAPKH31H69", "URL": "/supportpackage/SAPKH31H69"}, {"SoftwareComponentVersion": "SAP_HR 31H", "SupportPackage": "SAPKE31H69", "URL": "/supportpackage/SAPKE31H69"}, {"SoftwareComponentVersion": "SAP_APPL 31I", "SupportPackage": "SAPKH31I40", "URL": "/supportpackage/SAPKH31I40"}, {"SoftwareComponentVersion": "SAP_HR 31I", "SupportPackage": "SAPKE31I40", "URL": "/supportpackage/SAPKE31I40"}, {"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B32", "URL": "/supportpackage/SAPKH40B32"}, {"SoftwareComponentVersion": "SAP_HR 40B", "SupportPackage": "SAPKE40B32", "URL": "/supportpackage/SAPKE40B32"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B13", "URL": "/supportpackage/SAPKH45B13"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 6, "URL": "/corrins/0000180434/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 6, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}