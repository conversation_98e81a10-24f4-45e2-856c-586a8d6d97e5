{"Request": {"Number": "533802", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 282, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000002587352017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000533802?language=E&token=AD90EDDC74595A3D0676E861FF9CD473"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000533802", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000533802/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "533802"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.06.2003"}, "SAPComponentKey": {"_label": "Component", "value": "FI-AA-AA-A"}, "SAPComponentKeyText": {"_label": "Component", "value": "Master Data"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Asset Accounting", "value": "FI-AA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "FI-AA-AA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AA-AA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Master Data", "value": "FI-AA-AA-A", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AA-AA-A*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "533802 - Legacy data transfer: RAALTDBDBR"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>In the documentation for the 'Transfer of legacy assets into the R/3 System', a report is mentioned with which test data for the transfer via BAPI or Idoc can be generated. However, this report is not documented. Furthermore, there are certain restrictions with the usage of this report which partially can be corrected with the attached correction.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RAALTDBDBR, AS91, IDOC, ALE</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Missing documentation, program error.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. Documentation</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;With the help of report RAALTDBDBR, test data can be generated from an existing R/3 System for the transfer of legacy assets via BAPI. In this case, data is formatted so that a copy of the original fixed asset can be generated. The following data are transferred identically in this case: Company code, asset class, account determination, legacy data transfer date of the source company code, descriptions, depreciation areas and others. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;On the selection screen, you can make a restriction according to company code, asset class, and fixed asset. Furthermore, you can affect the process of number assignment. In the default setting, for both the asset main number and for the asset subnumber, the value of the source system is used as default. If, for example, the external number assignment is set for the affected asset class, this setting is used also for the target system. This setting can be separated according to asset main number and asset subnumber and can be fixed on internal or external number assignment independently of the settings in the source system. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;A prerequisite for the use of the report is a fully operative setting of the ALE link. <OL>2. Restrictions of the report</OL> <OL><OL>a) Group assets are not considered.</OL></OL> <OL><OL>b) Asset user fields (table ANLU) are not transferred.</OL></OL> <OL><OL>c) Posted values of the transfer year (during the fiscal year) are completely transferred independently of whether these acurred before or after the transfer date.</OL></OL> <OL><OL>d) Subnumbers are considered only if the external number assignment is used on asset main number level.</OL></OL> <OL>3. Program correction</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The adjustments of this note are not automatically installable. First of all, you must manually create the three function modules MAP2E_ANEA_TO_BAPI1022_PROPVAL, MAP2E_ANEP_TO_BAPI1022_TRTYPE and MAP2E_ANLC_TO_BAPI1022_POSTVAL. Use Transaction SE37 for this purpose. Enter the name of the function module here and press the \"Create\" pushbutton. In the following screen, enter \"1022\" as a function group. The short text is freely definable. Now press the \"Save\" pushbutton. On the following screen, define the interface parameters as follows: <UL><LI>Function module MAP2E_ANEA_TO_BAPI1022_PROPVAL</LI></UL> <UL><UL><LI>Tab title \"Import\"</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Parameter name&#x00A0;&#x00A0;&#x00A0;&#x00A0; Typing&#x00A0;&#x00A0;&#x00A0;&#x00A0; Reference type</TH></TR> <TR><TD>ANEA&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;LIKE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; ANEA</TD></TR> <TR><TD>I_WAERS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;LIKE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;TCURC-WAERS</TD></TR> </TABLE></UL></UL> <p></p> <UL><UL><LI>Tab title \"Changing\"</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Parameter name&#x00A0;&#x00A0;&#x00A0;&#x00A0; Typing&#x00A0;&#x00A0;&#x00A0;&#x00A0; Reference type</TH></TR> <TR><TD>BAPI1022_PROPVAL&#x00A0;&#x00A0; LIKE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; BAPI1022_PROPVAL</TD></TR> </TABLE></UL></UL> <p></p> <UL><UL><LI>Tab title \"Exceptions\"</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Short text&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Exception</TH></TR> <TR><TD>ERROR_CONVERTING_CURR_AMOUNT</TD></TR> </TABLE></UL></UL> <p></p> <UL><LI>Function module MAP2E_ANEP_TO_BAPI1022_TRTYPE</LI></UL> <UL><UL><LI>Tab title \"Import\"</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Parameter name&#x00A0;&#x00A0;&#x00A0;&#x00A0; Typing&#x00A0;&#x00A0;&#x00A0;&#x00A0; Reference type</TH></TR> <TR><TD>ANEP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;LIKE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; ANEP</TD></TR> <TR><TD>I_WAERS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;LIKE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;TCURC-WAERS</TD></TR> </TABLE></UL></UL> <p></p> <UL><UL><LI>Tab title \"Changing\"</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Parameter name&#x00A0;&#x00A0;&#x00A0;&#x00A0; Typing&#x00A0;&#x00A0;&#x00A0;&#x00A0; Reference type</TH></TR> <TR><TD>BAPI1022_TRTYPE&#x00A0;&#x00A0;&#x00A0;&#x00A0;LIKE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; BAPI1022_TRTYPE</TD></TR> </TABLE></UL></UL> <p></p> <UL><UL><LI>Tab title \"Exceptions\"</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Short text&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Exception</TH></TR> <TR><TD>ERROR_CONVERTING_CURR_AMOUNT</TD></TR> </TABLE></UL></UL> <p></p> <UL><LI>Function module MAP2E_ANLC_TO_BAPI1022_POSTVAL</LI></UL> <UL><UL><LI>Tab title \"Import\"</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Parameter name&#x00A0;&#x00A0;&#x00A0;&#x00A0; Typing&#x00A0;&#x00A0;&#x00A0;&#x00A0; Reference type</TH></TR> <TR><TD>ANLC&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;LIKE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; ANLC</TD></TR> <TR><TD>I_WAERS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;LIKE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;TCURC-WAERS</TD></TR> </TABLE></UL></UL> <p></p> <UL><UL><LI>Tab title \"Changing\"</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Parameter name&#x00A0;&#x00A0;&#x00A0;&#x00A0; Typing&#x00A0;&#x00A0;&#x00A0;&#x00A0; Reference type</TH></TR> <TR><TD>BAPI1022_POSTVAL&#x00A0;&#x00A0; LIKE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; BAPI1022_POSTVAL</TD></TR> </TABLE></UL></UL> <p></p> <UL><UL><LI>Tab title \"Exceptions\"</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Short text&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Exception</TH></TR> <TR><TD>ERROR_CONVERTING_CURR_AMOUNT</TD></TR> </TABLE></UL></UL> <p><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Save the function modules. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Now, you can implement the corrections of this note. <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D025982)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D032297)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000533802/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000533802/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000533802/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000533802/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000533802/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000533802/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000533802/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000533802/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000533802/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "850607", "RefComponent": "FI-AA-AA-A", "RefTitle": "RAALTDBDBR uses incorrect screen layout rule", "RefUrl": "/notes/850607"}, {"RefNumber": "693934", "RefComponent": "FI-AA-AA-A", "RefTitle": "RAALTDBDBR does not transfer any depreciation area data", "RefUrl": "/notes/693934"}, {"RefNumber": "631358", "RefComponent": "FI-AA-AA-A", "RefTitle": "RAALTDBDBR: No target system exists in the IDoc", "RefUrl": "/notes/631358"}, {"RefNumber": "550176", "RefComponent": "FI-AA-AA-A", "RefTitle": "FAQ note legacy data transfer asset master records", "RefUrl": "/notes/550176"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "850607", "RefComponent": "FI-AA-AA-A", "RefTitle": "RAALTDBDBR uses incorrect screen layout rule", "RefUrl": "/notes/850607 "}, {"RefNumber": "693934", "RefComponent": "FI-AA-AA-A", "RefTitle": "RAALTDBDBR does not transfer any depreciation area data", "RefUrl": "/notes/693934 "}, {"RefNumber": "631358", "RefComponent": "FI-AA-AA-A", "RefTitle": "RAALTDBDBR: No target system exists in the IDoc", "RefUrl": "/notes/631358 "}, {"RefNumber": "550176", "RefComponent": "FI-AA-AA-A", "RefTitle": "FAQ note legacy data transfer asset master records", "RefUrl": "/notes/550176 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C36", "URL": "/supportpackage/SAPKH46C36"}, {"SoftwareComponentVersion": "SAP_APPL 470", "SupportPackage": "SAPKH47002", "URL": "/supportpackage/SAPKH47002"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 1, "URL": "/corrins/0000533802/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}