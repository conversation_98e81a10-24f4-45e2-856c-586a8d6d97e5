{"Request": {"Number": "1061383", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 430, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016297622017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001061383?language=E&token=545D02B491FECBBDC0DE400818AEBA95"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001061383", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001061383/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1061383"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 28}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.05.2008"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-DIA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Diagnostics"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Diagnostics", "value": "SV-SMG-DIA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-DIA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1061383 - End-to-End Diagnostics SP13"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You need information about availability and limitations of End-to-End Diagnostics delivered with SAP Solution Manager 7.0 SP13.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This note contains the prerequisites and limitations for End-to-End Diagnostics.<br />Starting with ST 400 SP13 the following applications are available:</p> <UL><LI>E2E WorkloadAnalysis</LI></UL> <UL><LI>E2E ExceptionAnalysis</LI></UL> <UL><LI>E2E TraceAnalysis (for Internet Explorer based scenarios)</LI></UL> <UL><LI>E2E Change Analysis</LI></UL> <p>They include the components for Root Cause Analysis:</p> <UL><LI>WebAS6.40/7.00 (ABAP and J2EE)</LI></UL> <UL><LI>ECC5.0/6.0</LI></UL> <UL><LI>Portal incl. Trex and KMC based on NW04/NW04S</LI></UL> <UL><LI>BI based on NW04/NW04S</LI></UL> <UL><LI>XI based on NW04/NW04S</LI></UL> <UL><LI>CRM4.0/CRM5.0 (w/o VMC/IPC and Ajax)</LI></UL> <UL><LI>SRM 6.0 (only E2E TA)</LI></UL> <UL><LI>All other product versions can be included on project basis</LI></UL> <b></b><br /> <b>Prerequisites for Managing System:</b><br /> <UL><LI><B>Unicode</B><B><B>:</B></B><br />End-to-End Root Cause Analysis requires a SAP Solution Manager 7.0 with a SAP double stack system (Web AS ABAP and Web AS Java in one SAP system). SAP recommends to run Solution Manager on Unicode (see also customer letter on http://service.sap.com/Unicode). It is required to install all new Solution Manager Installations on Unicode. For customers that have upgraded from previous releases of SAP Solution Manager 7.0 and are not yet on Unicode, SAP recommends to migrate to Web AS ABAP to Unicode. If this is not possible, SAP will support End-to-End Root Cause Analysis with an ABAP stack on non-Unicode as of SAP Solution Manager 7.0 SP13, until the customer has completed the Unicode conversion.</LI></UL> <UL><LI>ABAP Stack specific:</LI></UL> <UL><UL><LI>BW SP15</LI></UL></UL> <UL><UL><LI>PI Basis 2005_1_700 SP13 or 2006_1_700 SP3</LI></UL></UL> <UL><UL><LI>BI Content 703 SP07</LI></UL></UL> <UL><UL><LI>ST 400 SP13</LI></UL></UL> <UL><UL><LI>ST-SER 700_2007_1 SP4</LI></UL></UL> <UL><UL><LI>ST-A/PI 01J_CRM500</LI></UL></UL> <UL><UL><LI>IGS 7.00 patch 10</LI></UL></UL> <UL><LI>J2EE Stack specific:</LI></UL> <UL><UL><LI>NW04S SPS13 - J2EE</LI></UL></UL> <UL><UL><LI>LMSERVICE (SolManDiag) SPS13</LI></UL></UL> <UL><UL><LI>ISAGENTSMD71.SCA (WILY INTROSCOPE AGENT 7.1) patch 11</LI></UL></UL> <UL><LI>Stack independent:</LI></UL> <UL><UL><LI>IntroScope Enterprise Manager 7.10 P9</LI></UL></UL> <b>Prerequisites for Managed systems:</b><br /> <p>ABAP Stacks specific:<br />- WAS6.20 SAP_BASIS SP61 and higher *)<br />&#x00A0;&#x00A0; ABAP Kernel 6.40 PL 194 or higher<br />&#x00A0;&#x00A0; ST-PI 2005_1_620 SP 6***)<br /><br />- NW04 - SPS17 and higher ***)<br />&#x00A0;&#x00A0; ABAP Kernel 6.40 PL 194 or higher<br />&#x00A0;&#x00A0; ST-PI 2005_1_640 SP 6***)<br /><br />- NW04S - SPS09 and higher ***)<br />&#x00A0;&#x00A0; ABAP Kernel 7.00 PL 121 or higher<br />&#x00A0;&#x00A0; ST-PI 2005_1_700 SP 4***)<br /><br />- ST-A/PI 01J_XXX<br /><br />XXX (for ST-A/PI) = corresponding software component e.g. CRM 5.0<br /><br />J2EE Stack specific:<br />- NW04 - SPS17 and higher **)<br />- NW04S - SPS09 and higher**)<br /><br />Stack independent:<br />- SMD agent corresponding to LMSERVICE NW04S<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Attached you can find:<br />- the SDA Files required for E2E trace in the monitored J2EE engine.<br />- Authorization roles for the BI<br /><br />Training for End-to-End Diagnostics can be booked under http://www.sap.com/services/education -&gt; Global Education Course Portfolio for 2006 -&gt; Netweaver -&gt;&#x00A0;&#x00A0;SAP End-to-End (E2E) Solution Support<br /><br /><br /><STRONG>Limitations for E2E Trace:</STRONG></p> <b>* Monitored System based on WAS 6.20</b><br /> <p>ABAP SQL trace and ABAP trace are written but cannot be collected.</p> <b>** Monitored J2EE engine NW04</b><br /> <p>For J2EE engine &lt; SP 20 attached SDAs in J2EE_640.ZIP have to be deployed (requires restart of Java Stack)<br />For J2EE engine = SP 21 attached SDAs in J2EE_640_SP21.ZIP have to be deployed (requires restart of Java Stack)<br /></p> <b>** Monitored J2EE engine NW2004s</b><br /> <p>For J2EE engine &lt; SPS 14 attached SDAs in J2EE_700-upTo_SP13.zip have to be deployed (requires restart of Java Stack).<br /><br /><br /><B>Caution:</B><br />Do not deploy the Patches for the latest SP into a system with an older SP e.g SPS 12 or 13 into a system with SPS &lt; 12.<br /><br /><br /><STRONG>Notes to be applied in Monitoring System</STRONG></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Note</TH><TH ALIGN=LEFT> To be applied on SP</TH><TH ALIGN=LEFT> Solved with SP</TH></TR> <TR><TD>1107804</TD><TD> BI_CONT 703 SP7 + 8</TD><TD> BI_CONT 703 SP9</TD></TR> <TR><TD>1126343</TD><TD> ST 400 SP13 + SP14 </TD><TD> ST 400 SP16</TD></TR> <TR><TD>1113137</TD><TD> ST 400 SP13 + SP14</TD><TD> ST 400 SP15</TD></TR> <TR><TD>1142697</TD><TD> ST 400 SP13 + SP14</TD><TD> ST 400 SP15</TD></TR> <TR><TD>989511</TD><TD> ST 400 SP13</TD><TD> ST 400 SP14</TD></TR> <TR><TD>1090472</TD><TD> ST 400 SP13</TD><TD> ST 400 SP14</TD></TR> <TR><TD>1088163</TD><TD> ST 400 SP13 + SP14</TD><TD> ST 400 SP15</TD></TR> <TR><TD>1042712</TD><TD> SAP_BW SP12 + SP13</TD><TD> SAP_BW SP14</TD></TR> <TR><TD>1019055</TD><TD> SAP_BW SP12</TD><TD> SAP_BW SP13</TD></TR> <TR><TD>995479</TD><TD> SAP_BW SP12</TD><TD> SAP_BW SP13</TD></TR> <TR><TD>1050413</TD><TD> PI_BASIS SP11 + SP12</TD><TD> PI_BASIS SP13</TD></TR> <TR><TD>1029341</TD><TD> PI_BASIS SP11</TD><TD> PI_BASIS SP12</TD></TR> <TR><TD>807072</TD><TD> PI_BASIS SP11</TD><TD> PI_BASIS SP12</TD></TR> </TABLE> <p><br /><STRONG>***Notes to be applied in Monitored System(all ABAP stacks)</STRONG></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Note</TH><TH ALIGN=LEFT> To be applied on SP</TH><TH ALIGN=LEFT> Solved with SP</TH></TR> <TR><TD>1061174</TD><TD> ST-PI 2005_1_620 SP6+7</TD><TD> ST-PI 2005_1_620 SP8</TD></TR> <TR><TD></TD><TD> ST-PI 2005_1_640 SP6+7</TD><TD> ST-PI 2005_1_640 SP8</TD></TR> <TR><TD></TD><TD> ST-PI 2005_1_700 SP4+5</TD><TD> ST-PI 2005_1_700 SP6</TD></TR> <TR><TD>1157418</TD><TD> ALL Versions</TD><TD> ST-PI 2005_1_620 SP9</TD></TR> <TR><TD></TD><TD> </TD><TD> ST-PI 2005_1_640 SP9</TD></TR> <TR><TD></TD><TD> </TD><TD> ST-PI 2005_1_700 SP7</TD></TR> <TR><TD>1041446</TD><TD> SAP_BASIS 700 SP11+12</TD><TD> SAP_BASIS 700 SP13</TD></TR> <TR><TD></TD><TD> SAP_BASIS 640 SP20</TD><TD> SAP_BASIS 640 SP21</TD></TR> <TR><TD>1030145</TD><TD> SAP_BASIS 620 SP0-61</TD><TD> SAP_BASIS 620 SP62</TD></TR> <TR><TD></TD><TD> SAP_BASIS 640 SP0-19</TD><TD> SAP_BASIS 640 SP20</TD></TR> <TR><TD>1011229</TD><TD> ST-PI 2005_1_620 SP5-7</TD><TD> ST-PI 2005_1_620 SP8</TD></TR> <TR><TD></TD><TD> ST-PI 2005_1_640 SP5-7</TD><TD> ST-PI 2005_1_640 SP8</TD></TR> <TR><TD></TD><TD> ST-PI 2005_1_700 SP3-5</TD><TD> ST-PI 2005_1_700 SP6</TD></TR> <TR><TD>0981557</TD><TD> SAP_BASIS 640 SP0-18</TD><TD> SAP_BASIS 640 SP19</TD></TR> <TR><TD></TD><TD> SAP_BASIS 700 SP0-9</TD><TD> SAP_BASIS 700 SP10</TD></TR> <TR><TD>1069429</TD><TD> ST-PI 2005_1_640 SP7</TD><TD> ST-PI 2005_1_640 SP8</TD></TR> <TR><TD></TD><TD> ST-PI 2005_1_700 SP5</TD><TD> ST-PI 2005_1_700 SP6</TD></TR> <TR><TD>1093785</TD><TD> ST-A/PI 01J_XXX</TD></TR> <TR><TD></TD></TR> </TABLE> <p><STRONG>***Notes to be applied in Monitored System (CRM4.0 / CRM5.0 only)</STRONG></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Note</TH><TH ALIGN=LEFT> To be applied on SP</TH><TH ALIGN=LEFT> Solved with SP</TH></TR> <TR><TD>1069758</TD><TD> ST-A/PI 01J_CRM500</TD><TD> ST-A/PI 01K_CRM500</TD></TR> <TR><TD></TD><TD> ST-A/PI 01J_CRM400</TD><TD> ST-A/PI 01K_CRM400</TD></TR> <TR><TD></TD></TR> </TABLE> <p><STRONG>***Notes to be applied in Monitored System (BI only)</STRONG></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Note</TH><TH ALIGN=LEFT> To be applied on SP</TH><TH ALIGN=LEFT> Solved with SP</TH></TR> <TR><TD>1060962</TD><TD> ST-A/PI 01J_XXX</TD><TD> ST-A/PI 01K_XXX</TD></TR> <TR><TD>1074601</TD><TD> ST-A/PI 01J_XXX</TD><TD> ST-A/PI 01K_XXX</TD></TR> <TR><TD></TD></TR> </TABLE> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I016118)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I022548)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001061383/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001061383/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001061383/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001061383/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001061383/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001061383/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001061383/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001061383/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001061383/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "J2EE_640_SP21.zip", "FileSize": "1102", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000379332007&iv_version=0028&iv_guid=1C7F17864867AA49AA721CE071B32617"}, {"FileName": "SAP_BI_E2E.ZIP", "FileSize": "1", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000379332007&iv_version=0028&iv_guid=32FB2E3020EFEB40A585666E1B740043"}, {"FileName": "J2EE_640.zip", "FileSize": "626", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000379332007&iv_version=0028&iv_guid=81BDF92890EDFD41B249D623301059FD"}, {"FileName": "J2EE_700-upTo_SP13.ZIP", "FileSize": "634", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000379332007&iv_version=0028&iv_guid=975B3B9179A0E84FAFD59B63B9CB47A0"}, {"FileName": "700_UTF8_SAP_BW_CCMS_SETUP.ZIP", "FileSize": "5", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000379332007&iv_version=0028&iv_guid=D0B03EADFAA45045A1CC3552894FDE49"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "989511", "RefComponent": "SV-SMG-DIA", "RefTitle": "ST: correction for E2E Diagnostics", "RefUrl": "/notes/989511"}, {"RefNumber": "1242585", "RefComponent": "BC-WD-ABA", "RefTitle": "SAP expiration header for static HTTP objects", "RefUrl": "/notes/1242585"}, {"RefNumber": "1161294", "RefComponent": "SV-SMG", "RefTitle": "Name chg, SAP Solution Manager 4.0 to Solution Manager 7.0", "RefUrl": "/notes/1161294"}, {"RefNumber": "1160946", "RefComponent": "SV-SMG-DIA", "RefTitle": "ST: Update to SMD_HASH_TABLE leads to DB deadlock", "RefUrl": "/notes/1160946"}, {"RefNumber": "1157418", "RefComponent": "SV-SMG-SER", "RefTitle": "High database load caused by /SDF/SAPLIS_ABAP", "RefUrl": "/notes/1157418"}, {"RefNumber": "1156194", "RefComponent": "SV-SMG-SER", "RefTitle": "Error message Function parameter \"OLD_GC_COUNT\" is unknown", "RefUrl": "/notes/1156194"}, {"RefNumber": "1152943", "RefComponent": "SV-SMG-DIA-APP-CA", "RefTitle": "E2E CA - ABAP_INSTANCE_PARAMETER", "RefUrl": "/notes/1152943"}, {"RefNumber": "1149742", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP (CQC) Going Live Support", "RefUrl": "/notes/1149742"}, {"RefNumber": "1148297", "RefComponent": "BC-TWB-TST-P-PA", "RefTitle": "ST05_E2E: Incorrect navigation during aggregation", "RefUrl": "/notes/1148297"}, {"RefNumber": "1142697", "RefComponent": "SV-SMG", "RefTitle": "Duet configuration Data is not read correctly from E2E CMD", "RefUrl": "/notes/1142697"}, {"RefNumber": "1136801", "RefComponent": "SV-SMG-DIA-APP-CA", "RefTitle": "E2E Change Analysis: No data for SolMan Diagnostics", "RefUrl": "/notes/1136801"}, {"RefNumber": "1131698", "RefComponent": "SV-SMG-DIA-APP-CA", "RefTitle": "Additional components for E2E CA with Diagnostics ST SP13", "RefUrl": "/notes/1131698"}, {"RefNumber": "1126859", "RefComponent": "SV-SMG-DIA", "RefTitle": "End-to-End Diagnostics SP15-SP17", "RefUrl": "/notes/1126859"}, {"RefNumber": "1126343", "RefComponent": "SV-SMG-DIA-APP-CA", "RefTitle": "E2E Diagnostics CA - dumps if ST-A/PI not installed", "RefUrl": "/notes/1126343"}, {"RefNumber": "1124127", "RefComponent": "SV-SMG-SYS", "RefTitle": "SMD: No monitored systems found", "RefUrl": "/notes/1124127"}, {"RefNumber": "1119766", "RefComponent": "SV-SMG-DIA", "RefTitle": "E2E Diagnostics: RAISE_EXCEPTION - BI objects inactive", "RefUrl": "/notes/1119766"}, {"RefNumber": "1113137", "RefComponent": "SV-SMG-DIA", "RefTitle": "Outdated Solution in Solution Manager Diagnostics", "RefUrl": "/notes/1113137"}, {"RefNumber": "1111831", "RefComponent": "SV-SMG-DIA", "RefTitle": "End-to-End Diagnostics SP10-SP12", "RefUrl": "/notes/1111831"}, {"RefNumber": "1111449", "RefComponent": "SV-SMG-DIA", "RefTitle": "E2E CA detects more changes than expected", "RefUrl": "/notes/1111449"}, {"RefNumber": "1107821", "RefComponent": "SV-SMG-DIA", "RefTitle": "E2E Diagnostics", "RefUrl": "/notes/1107821"}, {"RefNumber": "1093785", "RefComponent": "SV-SMG-SDD", "RefTitle": "E2E Diagnostics - Change Analysis", "RefUrl": "/notes/1093785"}, {"RefNumber": "1090472", "RefComponent": "SV-SMG-DIA-APP-CA", "RefTitle": "E2E diagnostics - incorr warning messages in SMD Scheduler", "RefUrl": "/notes/1090472"}, {"RefNumber": "1089641", "RefComponent": "BC-SRV-BAL", "RefTitle": "Application log: Including messages in E2E trace", "RefUrl": "/notes/1089641"}, {"RefNumber": "1088163", "RefComponent": "SV-SMG-DIA", "RefTitle": "ST: Extractor Templates for E2E Diagnostics", "RefUrl": "/notes/1088163"}, {"RefNumber": "1071474", "RefComponent": "SV-SMG-DIA", "RefTitle": "Data collection of multiple ABAP systems on single host", "RefUrl": "/notes/1071474"}, {"RefNumber": "1061174", "RefComponent": "SV-SMG-SER", "RefTitle": "ST-PI: Correction for IS ABAP Agent", "RefUrl": "/notes/1061174"}, {"RefNumber": "1041446", "RefComponent": "BC-TWB-TST-P-PA", "RefTitle": "PERFORMANCE_TRACE_SUMMARY error with selection using TRANSID", "RefUrl": "/notes/1041446"}, {"RefNumber": "1032461", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX parser does not start", "RefUrl": "/notes/1032461"}, {"RefNumber": "1011229", "RefComponent": "SV-SMG-SER", "RefTitle": "ST-PI: Corrections for E2E Diagnostics", "RefUrl": "/notes/1011229"}, {"RefNumber": "1010428", "RefComponent": "SV-SMG-DIA", "RefTitle": "End-to-End Diagnostics", "RefUrl": "/notes/1010428"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1149742", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP (CQC) Going Live Support", "RefUrl": "/notes/1149742 "}, {"RefNumber": "1010428", "RefComponent": "SV-SMG-DIA", "RefTitle": "End-to-End Diagnostics", "RefUrl": "/notes/1010428 "}, {"RefNumber": "1011229", "RefComponent": "SV-SMG-SER", "RefTitle": "ST-PI: Corrections for E2E Diagnostics", "RefUrl": "/notes/1011229 "}, {"RefNumber": "1032461", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX parser does not start", "RefUrl": "/notes/1032461 "}, {"RefNumber": "989511", "RefComponent": "SV-SMG-DIA", "RefTitle": "ST: correction for E2E Diagnostics", "RefUrl": "/notes/989511 "}, {"RefNumber": "1119766", "RefComponent": "SV-SMG-DIA", "RefTitle": "E2E Diagnostics: RAISE_EXCEPTION - BI objects inactive", "RefUrl": "/notes/1119766 "}, {"RefNumber": "1160946", "RefComponent": "SV-SMG-DIA", "RefTitle": "ST: Update to SMD_HASH_TABLE leads to DB deadlock", "RefUrl": "/notes/1160946 "}, {"RefNumber": "1088163", "RefComponent": "SV-SMG-DIA", "RefTitle": "ST: Extractor Templates for E2E Diagnostics", "RefUrl": "/notes/1088163 "}, {"RefNumber": "1107821", "RefComponent": "SV-SMG-DIA", "RefTitle": "E2E Diagnostics", "RefUrl": "/notes/1107821 "}, {"RefNumber": "1126859", "RefComponent": "SV-SMG-DIA", "RefTitle": "End-to-End Diagnostics SP15-SP17", "RefUrl": "/notes/1126859 "}, {"RefNumber": "1157418", "RefComponent": "SV-SMG-SER", "RefTitle": "High database load caused by /SDF/SAPLIS_ABAP", "RefUrl": "/notes/1157418 "}, {"RefNumber": "1152943", "RefComponent": "SV-SMG-DIA-APP-CA", "RefTitle": "E2E CA - ABAP_INSTANCE_PARAMETER", "RefUrl": "/notes/1152943 "}, {"RefNumber": "1113137", "RefComponent": "SV-SMG-DIA", "RefTitle": "Outdated Solution in Solution Manager Diagnostics", "RefUrl": "/notes/1113137 "}, {"RefNumber": "1131698", "RefComponent": "SV-SMG-DIA-APP-CA", "RefTitle": "Additional components for E2E CA with Diagnostics ST SP13", "RefUrl": "/notes/1131698 "}, {"RefNumber": "1161294", "RefComponent": "SV-SMG", "RefTitle": "Name chg, SAP Solution Manager 4.0 to Solution Manager 7.0", "RefUrl": "/notes/1161294 "}, {"RefNumber": "1136801", "RefComponent": "SV-SMG-DIA-APP-CA", "RefTitle": "E2E Change Analysis: No data for SolMan Diagnostics", "RefUrl": "/notes/1136801 "}, {"RefNumber": "1156194", "RefComponent": "SV-SMG-SER", "RefTitle": "Error message Function parameter \"OLD_GC_COUNT\" is unknown", "RefUrl": "/notes/1156194 "}, {"RefNumber": "1093785", "RefComponent": "SV-SMG-SDD", "RefTitle": "E2E Diagnostics - Change Analysis", "RefUrl": "/notes/1093785 "}, {"RefNumber": "1142697", "RefComponent": "SV-SMG", "RefTitle": "Duet configuration Data is not read correctly from E2E CMD", "RefUrl": "/notes/1142697 "}, {"RefNumber": "1126343", "RefComponent": "SV-SMG-DIA-APP-CA", "RefTitle": "E2E Diagnostics CA - dumps if ST-A/PI not installed", "RefUrl": "/notes/1126343 "}, {"RefNumber": "1090472", "RefComponent": "SV-SMG-DIA-APP-CA", "RefTitle": "E2E diagnostics - incorr warning messages in SMD Scheduler", "RefUrl": "/notes/1090472 "}, {"RefNumber": "1111831", "RefComponent": "SV-SMG-DIA", "RefTitle": "End-to-End Diagnostics SP10-SP12", "RefUrl": "/notes/1111831 "}, {"RefNumber": "1124127", "RefComponent": "SV-SMG-SYS", "RefTitle": "SMD: No monitored systems found", "RefUrl": "/notes/1124127 "}, {"RefNumber": "1111449", "RefComponent": "SV-SMG-DIA", "RefTitle": "E2E CA detects more changes than expected", "RefUrl": "/notes/1111449 "}, {"RefNumber": "1089641", "RefComponent": "BC-SRV-BAL", "RefTitle": "Application log: Including messages in E2E trace", "RefUrl": "/notes/1089641 "}, {"RefNumber": "1071474", "RefComponent": "SV-SMG-DIA", "RefTitle": "Data collection of multiple ABAP systems on single host", "RefUrl": "/notes/1071474 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST", "From": "400", "To": "400", "Subsequent": ""}, {"SoftwareComponent": "LM-SERVICE", "From": "7.00", "To": "7.00", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}