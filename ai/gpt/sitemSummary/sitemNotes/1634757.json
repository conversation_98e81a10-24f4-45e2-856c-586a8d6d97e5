{"Request": {"Number": "1634757", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 352, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017314862017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001634757?language=E&token=CDD53FECF1D7B0315B30135F43459A5D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001634757", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001634757/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1634757"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.05.2016"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SER"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Support Services"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Support Services", "value": "SV-SMG-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1634757 - Guided Self Service 'Performance Optimization'"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You observe one or more of the following issues:</p>\r\n<ul>\r\n<li>Long response times of specific business process steps (SAP standard and/or customer-specific)</li>\r\n</ul>\r\n<ul>\r\n<li>Deadlines and time windows for specific business processes cannot be met (e.g. batch jobs running too long)</li>\r\n</ul>\r\n<ul>\r\n<li>High system resource consumption (e.g. memory, CPU, etc.) during specific business processes or time windows</li>\r\n</ul>\r\n<ul>\r\n<li>Performance problems with transactions (normally well-performing) during a specific time window</li>\r\n</ul>\r\n<ul>\r\n<li>EWA (Early Watch Alert) Report shows single transactions with more than 10% of the total workload (Dialog/HTTP(S)) within the transaction profile check</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>ST12 Trace, E2E Trace, ST14 Application Analysis, Transactional Performance Analysis, GSS PERF, Performance Optimization, EGI</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>During the analysis of the problems described above you are using one of the following SAP Active Global Support analysis tools:</p>\r\n<ul>\r\n<li>ST-A/PI ST12 Trace</li>\r\n</ul>\r\n<ul>\r\n<li>SAP Solution Manager Diagnostics E2E Trace</li>\r\n</ul>\r\n<ul>\r\n<li>ST14 Application Analysis for SAP components:</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; ERP: PP-MRP, PP-CRP and/or SD-Pricing<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; CRM: CRM-General and/or CRM-Middleware<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; SCM: APO-DP and/or APO-SNP</p>\r\n<p><strong><strong>Service Prerequisites</strong></strong></p>\r\n<ol><ol>1.</ol></ol>\r\n<p><strong>ST12 Trace Analysis</strong></p>\r\n<ol><ol>a) Prerequisites on your SAP Solution Manager</ol></ol>\r\n<ul>\r\n<li>Technical Requirements for Service Delivery in Solution Manager</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;SAP Solution Manager ST 400 (at least SP24) or ST 7.1 (at least SP02) and ensure that you apply the latest ST-SER release and Support Package which are available from the SAP Service Marketplace and activate content update</p>\r\n<ul>\r\n<li>Required User for Solution Manager</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;For the Solution Manager user, it is preferable to provide user \"SAPSUPPORT\" which is created by default during SMD installation, see SAP Note 872800. In case a different user is provided, please make sure that the user has the authorizations mentioned in the note.</p>\r\n<ul>\r\n<li>Required Solution Maintenance in Solution Manager</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;For the Guidel Self Service to be delivered on your Solution Manager system, the Solution landscape (including RFC connections for READ, LOGIN and TRUSTED to the managed/to be analyzed systems) has to be maintained.</p>\r\n<ul>\r\n<li>Solution Manager Starter Pack / Expert Guided Implementation</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;SAP offers a Starter Pack and a Expert Guided Implementation Service for Solution Manager, whereby Solution Manager is configured for you and information provided on how to use it. You can find more information and details at http://service.sap.com/solutionmanager ==&gt; SAP Solution Manager Services ==&gt; Starter Pack ==&gt; Expert Guided Implementation</p>\r\n<ol><ol>a) Prerequisites on Managed Systems</ol></ol>\r\n<ul>\r\n<li>Required SAP Kernel release</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Ensure that the SAP Kernel release is suitable for performance traces. Refer to SAP Notes 981919, 1076804 and 1132350.</p>\r\n<ul>\r\n<li>Authorisation for Tools in Analysed System</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;To perform a system analysis you require users who are equipped with suitable authorization profiles. If due to security concerns a user with \"SAP_ALL\" authorization can not be given, a user RSDUSER can be constructed, which is described in SAP Note 1405975.</p>\r\n<ul>\r\n<li>Current Service Tools</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Use report RTCCTOOL as per SAP Note 69455 to ensure that the newest versions of add-ons ST-PI and ST-A/PI are implemented. Among other things the add-ons contain function modules and data collectors which ensure the quality of the service data collection. Important analysis tools like transaction ST12 and tools available via transaction ST13 are also part of ST-A/PI. For more detailed information see SAP Note 69455 (ST-A/PI) and 539977 (ST-PI). Please note, that the newest version of ST-PI should be implemented not only in the managed system where the analysis will be done, but also in the Solution Manager.</p>\r\n<ul>\r\n<li>System Settings for Service Tools</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<span style=\"text-decoration: underline;\">STAD - Statistical Records:</span><br />For individual analyses DB table access statistics need to be activated. Set the value of variable STAT/TABREC to 10. It can be activated online and no restart of an instance is necessary. You can do this via transaction ST03 ==&gt; Online Parameters ==&gt; Dialog step statistics. You should activate the parameter only temporarily because, otherwise, performance problems and memory problems may occur in the statistics collector.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<span style=\"text-decoration: underline;\">ST05 - SQL trace:</span><br />Since basis 6.40: To avoid SQL traces from being overwritten, extend the parameter rstr/max_filesize_MB to 100 MB via transaction ST01 ==&gt; GoTo ==&gt; Administration. This parameter needs to be set on each instance. It can be activated online. After service delivery set the parameter to default file size 16 MB back again.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<span style=\"text-decoration: underline;\">ST03N - Workload Monitor:</span><br />The following is very useful ONLY for WebDynpro ABAP and BSP Applications. Check if the resolution of the URL in the transaction profile of ST03N is set to 'Break-Down by URL' or 'Breakdown by Application': In a system with SAP NW 7.0 (7.00,&#160;&#160;7.01, 7.02 etc.) run report SWNC_CONFIG_URL (see SAP note 992474) and execute it with setting 'Display Current Configuration'. If the result is 'SAPMHTTP as Transaction Name in Transaction Profile' all Web Application will show up as SAPMHTTP in the transaction profile. Run the report again with setting 'Break-Down by URL' to switch the resolution. In a system with SAP NW &lt; 7.0 (e.g. 6.40 or 7.10) you can find the setting in ST03N ==&gt; Collector and Performance DB ==&gt; Workload Collector ==&gt; Statistics to be created. Check the settings for 'Web Application Server Statistics'. Change the setting to 'Breakdown by Application' if necessary.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<span style=\"text-decoration: underline;\">SMICM - ICM Monitor:</span><br />The following is very useful for WebDynpro ABAP, BSP and WebUI applications.Check if the ICM HTTP Server Log is activated and written correctly. Go to transaction SMICM. Use the menu path 'Goto ==&gt; HTTP Log ==&gt; Server ==&gt; Display Entries' to check if the log is written at all. Use menu path 'Goto ==&gt; Parameters ==&gt; Display' to check profile parameter 'icm/HTTP/logging_*' (* is usually 0 but could be any consecutive number). Below Kernel 7.00 PL 118 or 7.10 SP4 set value of parameter icm/HTTP/logging_* to PREFIX=/, LOGFORMAT=SAPSMD, LOGFILE=icmhttp.log, MAXSIZEKB=10240, SWITCHTF=day, FILEWRAP=on. With Kernel 7.01 and 7.02 and starting with Kernel 7.00 PL 118 or 7.10 SP4 it is possible to restrict the amount of entries written into the http log file by a filter. By setting the parameter icm/HTTP/logging_* to PREFIX=/, LOGFILE=icmhttph.log, FILTER=SAPSMD, LOGFORMAT=SAPSMD2, MAXSIZEKB=10240,FILEWRAP=on, SWITCHF=month only requests containing the X-CorrelationId are written into that file. After parameter change the ICM alone needs to be restarted. There is no need to restart the instance. The activated log file has no impact on system performance. The content of the file will be deleted automatically after some time. Therefore you can keep these settings for later services. For further details see note 1252944.</p>\r\n<ol><ol>a) Non-technical Prerequisites</ol></ol>\r\n<ul>\r\n<li>Data Preparation</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The data required for this session must represent your normal daily operations. The transactions and data to be used for this session must be suitable for a trial run of all the functions you use in your production environment. All transactions can be executed several times and you should therefore prepare a reusable scenario. After running the step at least one time, you should execute the action together with an ST12 trace for one action (e.g. hit \"Save\" button within one transaction) only. You can also execute an ST12 trace during execution of a batch job. Therefore different ST12 trace execution modes exist.</p>\r\n<p>&#160;</p>\r\n<ol><ol>2.</ol></ol>\r\n<p><strong><strong>E2E Trace Analysis</strong></strong></p>\r\n<ol><ol>a) Prerequisites on your SAP Solution Manager:</ol></ol>\r\n<ul>\r\n<li>Solution Manager Diagnostics (SMD) is a prerequisite for analysing applications based on SAP Web AS JAVA. For performing E2E Traces with SMD further prerequisites need to be fulfilled.</li>\r\n</ul>\r\n<ul>\r\n<li>Technical Requirements for Service Delivery in Solution Manager:<br />SAP Solution Manager ST 400 (at least SP24) or ST 7.1 (at least SP02) and ensure that you apply the latest ST-SER release and Support Package which are available from the SAP Service Market place and activate content update</li>\r\n</ul>\r\n<ul>\r\n<li>Required User for Solution Manager:<br />For the Solution Manager user, it is preferable to provide user \"SAPSUPPORT\" which is created by default during SMD installation, see SAP Note 872800. In case a different user is provided, please make sure that the user has the authorizations mentioned in the note.</li>\r\n</ul>\r\n<ul>\r\n<li>Required Solution Maintenance in Solution Manager:<br />For the Guidel Self Service to be delivered on your Solution Manager system, the Solution landscape (including RFC connections like READ , Trusted and LOGIN to the managed/to be analyzed systems) has to be maintained.</li>\r\n</ul>\r\n<ul>\r\n<li>Solution Manager Diagnostics (SMD):<br />To ensure efficient and safe support for IT solutions, it must be possible to perform root cause analysis quickly and efficiently.Solution Manager Diagnostics provides functions for central analysis of a complete SAP NetWeaver system landscape. If you run applications based on SAP Web AS JAVA, you must install one Solution Manager Diagnostic within your SAP NetWeaver solution landscape. SAP Support uses Solution Manager Diagnostics to perform root cause analysis for incoming incidents. For applications runnning with ABAP on Netweaver the SMD is not mandatory. The latest mandatory software components for E2E analysis are detailed in SAP Note 1010428. This Note contains all SPs required and Notes that have to be applied as a prerequisite for all E2E applications.<br /><br />SMD Self-Check As of Solution Manager 7.0, SP13, Solution Manager Diagnostics provides a self-check function.<br />1. Log on to the SMD as an admin user.<br />2. Navigate to Diagnostics Administration ==&gt; Diagnostics system ==&gt; Self check ==&gt; Start Self-Check For information on the self-check, refer to chapter \"Configuration Check\" at: http://service.sap.com/&#126;sapdownload/011000358700000271732008E Carry out the actions provided by the result of the self-check.<br />If you cannot resolve the problems by yourself, open a customer message on component SV-SMG-DIA.</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<ul>\r\n<li>Starter Pack / Expert Guided Implementation Service<br />SAP offers a Starter Pack and a Expert Guided Implementation Service for Solution Manager, whereby Solution Manager is configured for you and information provided on how to use it. You can find more information and details at http://service.sap.com/solutionmanager &gt; SAP Solution Manager Services &gt; Starter Pack &gt; Expert Guided Implementation</li>\r\n</ul>\r\n<ol><ol>a) Prerequisites on Managed Systems</ol></ol>\r\n<ul>\r\n<li>Required SAP Kernel release:<br />Ensure that the SAP Kernel release is suitable for performance traces. Refer to SAP Notes 981919, 1076804 and 1132350.</li>\r\n</ul>\r\n<ul>\r\n<li>Authorisation for Tools in Analysed System:<br />To perform a system analysis you require users who are equipped with suitable authorization profiles. If due to security concerns a user with \"SAP_ALL\" authorization can not be given, a user RSDUSER can be constructed, which is described in SAP Note 1405975.</li>\r\n</ul>\r\n<ul>\r\n<li>Current Service Tools:<br />Use report RTCCTOOL as per SAP Note 69455 to ensure that the newest versions of add-ons ST-PI and ST-A/PI are implemented. Among other things the add-ons contain function modules and data collectors which ensure the quality of the service data collection. Important analysis tools like transaction ST12 and tools available via transaction ST13 are also part of ST-A/PI. For more detailed information see SAP Note 69455 (ST-A/PI) and 539977 (ST-PI). Please note, that the newest version of ST-PI should be implemented not only in the managed system where the analysis will be done, but also in the Solution Manager.</li>\r\n</ul>\r\n<ul>\r\n<li>System Settings for Service Tools</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<span style=\"text-decoration: underline;\">STAD - Statistical Records:</span><br />For individual analyses DB table access statistics need to be activated. Set the value of variable STAT/TABREC to 10. It can be activated online and no restart of an instance is necessary. You can do this via transaction ST03 ==&gt; Online Parameters ==&gt; Dialog step statistics. You should activate the parameter only temporarily because, otherwise, performance problems and memory problems may occur in the statistics collector.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<span style=\"text-decoration: underline;\">ST05 - SQL trace:</span><br />Since basis 6.40: To avoid SQL traces from being overwritten, extend the parameter rstr/max_filesize_MB to 100 MB via transaction ST01 ==&gt; GoTo ==&gt; Administration. This parameter needs to be set on each instance. It can be activated online. After service delivery set the parameter to default file size 16 MB back again.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<span style=\"text-decoration: underline;\">ST03N - Workload Monitor:</span><br />The following is very useful ONLY for WebDynpro ABAP and BSP Applications. Check if the resolution of the URL in the transaction profile of ST03N is set to 'Break-Down by URL' or 'Breakdown by Application': In a system with SAP NW 7.0 (7.00,&#160;&#160;7.01, 7.02 etc.) run report SWNC_CONFIG_URL (see SAP note 992474) and execute it with setting 'Display Current Configuration'. If the result is 'SAPMHTTP as Transaction Name in Transaction Profile' all Web Application will show up as SAPMHTTP in the transaction profile. Run the report again with setting 'Break-Down by URL' to switch the resolution. In a system with SAP NW &lt; 7.0 (e.g. 6.40 or 7.10) you can find the setting in ST03N ==&gt; Collector and Performance DB ==&gt; Workload Collector ==&gt; Statistics to be created. Check the settings for 'Web Application Server Statistics'. Change the setting to 'Breakdown by Application' if necessary.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<span style=\"text-decoration: underline;\">SMICM - ICM Monitor:</span><br />The following is very useful for WebDynpro ABAP, BSP and WebUI applications.Check if the ICM HTTP Server Log is activated and written correctly. Go to transaction SMICM. Use the menu path 'Goto ==&gt; HTTP Log ==&gt; Server ==&gt; Display Entries' to check if the log is written at all. Use menu path 'Goto ==&gt; Parameters ==&gt; Display' to check profile parameter 'icm/HTTP/logging_*' (* is usually 0 but could be any consecutive number). Below Kernel 7.00 PL 118 or 7.10 SP4 set value of parameter icm/HTTP/logging_* to PREFIX=/, LOGFORMAT=SAPSMD, LOGFILE=icmhttp.log, MAXSIZEKB=10240, SWITCHTF=day, FILEWRAP=on. With Kernel 7.01 and 7.02 and starting with Kernel 7.00 PL 118 or 7.10 SP4 it is possible to restrict the amount of entries written into the http log file by a filter. By setting the parameter icm/HTTP/logging_* to PREFIX=/, LOGFILE=icmhttph.log, FILTER=SAPSMD, LOGFORMAT=SAPSMD2, MAXSIZEKB=10240,FILEWRAP=on, SWITCHF=month only requests containing the X-CorrelationId are written into that file. After parameter change the ICM alone needs to be restarted. There is no need to restart the instance. The activated log file has no impact on system performance. The content of the file will be deleted automatically after some time. Therefore you can keep these settings for later services. For further details see note 1252944.</p>\r\n<ul>\r\n<li>E2E Trace Analysis Requirements:<br />For applications with a user interface running in the MS Internet Explorer like WebDynpro, BSP or Web UI the service consultant will use the End-to-End trace (E2E Trace) within the Solution Manager Diagnostics (SMD) to create single user traces. This tool is proven to give us the more accurate and detailed performance trace. The minimum Support Package of Solution Manager needed is SP 18 (Enhancement Package 1). SAP Client Plug-in The E2E trace is performed using a browser that is instrumented by the SAP Client plug-in. If an E2E trace is to be performed on your system, please refer to SAP note 1435190. It contains information about the Client plug-in, and the downloadable plug-in itself together with an user documentation (PDF file). Remote Connection Requirements for Performing E2E Trace (to assist you during an EGI session) Desktop Sharing Application A desktop sharing application is the preferred method to access the application via a browser and to access the SMD. Possible desktop sharing applications are PCAnywhere, Windows Terminal Server, Citrix-GoTo and Netmeeting. Refer to SAP Notes 100740, 605795, 1036616 or 356635, respectively. Windows Terminal Server (WTS) or PCAnywhere are preferred because they do not require that someone accepts the incoming calls on the remote machine. The Client plug-in will be installed on the desktop sharing application. See SAP Note 1435190 for the supported Operating System and Internet Explorer combinations for the Client plug-in. Please note: in case the desktop sharing application is running on a non supported Operating System, it is enough to provide another PC or Laptop running on the supported version of OS and IE combination. Then the service engineer can first gain access to desktop sharing application, then further logon to this PC or Laptop to perform the E2E Trace with the Client plug-in. HTTP Connection In case no desktop sharing application to the production network is allowed, please make sure that sap-routed HTTP connections to the application (e.g. Portal) and SMD are available.<br /><br />NOTE: If the trace is collected with HTTP Connection via SAPROUTER, the measured network time would include discrepancy due to additional network segment from SAP network to customer network. Therefore, it is preferable to setup a desktop sharing application to perform the E2E Trace. Please also read SAP Notes 35010 and 31515 for information on configuring remote connections. If you require assistance in setting up the remote connection, please create a customer message under the component XX-SER-NET-HTL.</li>\r\n</ul>\r\n<ol><ol>a) Non-technical Prerequisites</ol></ol>\r\n<ul>\r\n<li>Data Preparation: The data required for this session must represent your normal daily operations. The transactions and data to be used for this session must be suitable for a trial run of all the functions you use in your production environment. All transactions can be executed several times and you should therefore prepare a reusable scenario. After running the step at least one time, you should execute the action together with an E2E trace for one action (e.g. hit \"Save\" button within one CRM transaction) only.</li>\r\n</ul>\r\n<ol><ol>3.</ol></ol>\r\n<p><strong><strong>ST14 Application Analysis</strong></strong></p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;All prerequisites from ST12 Trace Analysis are applicable for ST14 Application Analysis as well. Check note 1314587 if problems occur with authorizations. The connected RFC user needs to have same authorizations as the user SOLMAN_BTC to schedule ST14 application analysis within the managed systems.</p>\r\n<p>&#160;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>To assist you with the analysis of the ST12/E2E traces and/or ST14 application analysis you can perform a Guided Self Service 'Performance Optimization' (GSS PERF) within your SAP Solution Manager. You can also register for an Expert Guided Implementation (EGI) training for GSS PERF (register in SAP service marketplace https://service.sap.com/support ==&gt; Maintenance &amp; Services ==&gt; SAP Enterprise Support Academy ==&gt; Register Now ==&gt; for Expert Guided Implementation).<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<br /><strong><strong><span style=\"text-decoration: underline;\"> How to perform the Guided Self Service \"Performance Optimization\"?</span></strong></strong></p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;You have two possibilities to execute the Guided Self Service 'Performance Optimization':</p>\r\n<ul>\r\n<li>You can participate in an expert guided implementation (EGI) session. Within the EGI session one SAP support expert will process and explain all necessary actions step-by-step with focus on ST12 Trace or E2E Trace Analysis, in addition with ST14 application analysis if necessary.</li>\r\n</ul>\r\n<ul>\r\n<li>Run the guided procedure (Solution Manager ST 7.1) or sessions (Solution Manager ST 400) by yourself within your SAP Solution Manager.</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The next sections describe how to execute the service on the different Support Manager releases.</p>\r\n<ol><ol><ol>a)</ol></ol></ol>\r\n<p><strong><strong>Solution Manager ST 400 and ST-SER &gt;= 701_2010_1 SP08:</strong></strong></p>\r\n<ul>\r\n<li>Using transaction \"solman_workcenter\", the Work Center \"SAP Engagement and Service Delivery\" can be opened. By pressing\"Services\", an overview of the existing services for the selected \"Solution\" is given. Pressing \"Create\" allows the creation of the service \"Guided Performance Optimization - Session\". After pressing \"Create\" another entry appears for \"Performance Optimization\" on the service list with the current date and status \"in Process\". To execute the service you should follow the following steps:</li>\r\n</ul>\r\n<ul>\r\n<li>Execute Questionnaire Session:<br />To create the questionnaire you need to click on the small triangle in front of the service \"Performance Optimization\" and select the line with \"Session is Initial\". When you click on the tab \"Sessions\" you will get a button to create the questionnaire session. After creation two entries/sessions will appear on the session list. The first line is the questionnaire session. Click on the session \"Guided Self Service: Performance Optimization - Questionnaire\" to open the session within another GUI mode. Follow the watcher instructions on the right upper side to fill out the necessary checks. After completion and automatic rating (yellow or green) of the questionnaire session you can proceed with the analysis session.</li>\r\n</ul>\r\n<ul>\r\n<li>Execute Analysis Session:<br />To start the analysis session click on the session \"Guided Self Service: Performance Optimization - Session\" within the transaction \"Solman_workcenter\". Follow the watcher instruction of ST12 Analysis, E2E Trace Analysis or application analysis. You need to create a step for further analysis and upload the ST12 trace (connection to managed system is required). After upload the trace is being pre-analyzed and specific statements can be further selected for analysis. Technical details are provided both to support and to document the analysis. You can add/change text information within the single checks. After the analysis is completed, the button \"Word Document\" can be pressed inside the session in order to create the report.</li>\r\n</ul>\r\n<ul>\r\n<li>Finally, also the follow-up steps can be performed (if required) with the \"Issue Management\" functionality of the Solution Manager: After the report has been created, the \"Details of Service: Performance Optimization\" needs to be refreshed: after the refresh, two new tabs appear: within the tab \"Attachments\", the word report can be found and in the tab \"Assigned Issues\", the tasks created by the analysis are listed and can be distributed to the appropriate person.</li>\r\n</ul>\r\n<ol><ol><ol>a)</ol></ol></ol>\r\n<p><strong><strong>Solution Manager ST 7.1 and ST-SER &gt;= 701_2010_1 SP08:</strong></strong></p>\r\n<ol><ol><ol>Using transaction \"solman_workcenter\", the Work Center \"SAP Engagement and Service Delivery\" can be opened. By pressing \"Services\", an overview of the existing services for the selected \"Solution\" is given. Pressing \"Create\" allows the creation of the Guided Self Service \"Performance Optimization - Session\" on the tab \"Guided Self Services\". A Guided Procedure appears within another browser window with the following steps:</ol></ol></ol>\r\n<p><br /><br /><br /></p>\r\n<ol><ol><ol>(1) \"Prepare\" First, the service needs to be prepared in terms of analysis method, system selection and authorization checks. Therefore the following sub steps needs to be processed before the respective analysis can be started. For the analysis methods E2E and ST12 trace analysis the steps 1.3 and 1.4 must not be processed.</ol></ol></ol><ol><ol><ol>(1.1) \"Select Analysis Method (E2E Trace, ST12 Trace, Application Check)\"</ol></ol></ol><ol><ol><ol>(1.2) \"Select System\"</ol></ol></ol><ol><ol><ol>(1.3) \"Select Logon to Managed System\"</ol></ol></ol><ol><ol><ol>(1.4) \"Choose/Schedule Data Collection\"</ol></ol></ol><ol><ol><ol>(1.5) \"Summary of GSS Perf Preparation\"</ol></ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol><ol>(2) \"Analyse\" After you have performed the step \"Prepare\" you can execute the step \"Analyse\". The data collected in the \"Preparation\" step lead to a predefined analysis method (E2E Trace, ST12 Trace or application checks) within the analysis step. Follow the watcher instruction of ST12 Analysis, E2E Trace Analysis or application analysis. You need to create a step for further analysis and upload the ST12 trace (connection to managed system is required). After upload the trace is being pre-analyzed and specific statements can be further selected for analysis. Technical details are provided both to support and to document the analysis. You can add/change text information within the single checks. Finally, an HTML or Word document can be generated, documenting the details of the analysis incl. recommendations.</ol></ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol><ol>(3) \"Report\" The report covers the analysis and issue part of the Guided Self Service. Issues will be created automatically and only be visible (within Analyze Session and Follow up step) after pressing the report generation (HTML/Word) button.</ol></ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol><ol>(4) \"Follow up step\" The final step \"Follow-Up\" links the results of the Analysis with the \"Issue Management\" of the Solution Manager: for each finding in the \"Analysis\", a \"Task\" is created that can be assigned to the appropriate processor. If the \"Issue Management\" in the solution manager is not used, the follow-up steps have to be performed without support by the tool.</ol></ol></ol>\r\n<p><strong><strong><span style=\"text-decoration: underline;\">Frequently asked questions?</span></strong></strong><br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;1. Where can I get support, if a problem with the service or sessions occurs? Open a message on component XX-SER-SELF.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D036026)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D036694)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001634757/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001634757/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001634757/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001634757/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001634757/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001634757/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001634757/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001634757/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001634757/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "992474", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "URL in ST03 transaction profile", "RefUrl": "/notes/992474"}, {"RefNumber": "981919", "RefComponent": "BC-CST-MM", "RefTitle": "All work processes are used by SAPSYS, semaphore 39", "RefUrl": "/notes/981919"}, {"RefNumber": "872800", "RefComponent": "SV-SMG-SER", "RefTitle": "Roles for SAP Solution Manager: SAP service provider", "RefUrl": "/notes/872800"}, {"RefNumber": "755977", "RefComponent": "SV-SMG-SDD", "RefTitle": "ST12 \"ABAP Trace for SAP EarlyWatch/GoingLive\"", "RefUrl": "/notes/755977"}, {"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455"}, {"RefNumber": "605795", "RefComponent": "XX-SER-NET", "RefTitle": "Windows Terminal Server connection in remote support", "RefUrl": "/notes/605795"}, {"RefNumber": "539977", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for add-on ST-PI", "RefUrl": "/notes/539977"}, {"RefNumber": "35010", "RefComponent": "XX-SER-NET", "RefTitle": "Service connections: Composite note (overview))", "RefUrl": "/notes/35010"}, {"RefNumber": "31515", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "Service connections", "RefUrl": "/notes/31515"}, {"RefNumber": "309711", "RefComponent": "SV-SMG-SDD", "RefTitle": "SAP Servicetools Update: Online help", "RefUrl": "/notes/309711"}, {"RefNumber": "2016077", "RefComponent": "XX-SER-NET", "RefTitle": "GoToAssist Corporate support sessions", "RefUrl": "/notes/2016077"}, {"RefNumber": "1912318", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "WTS Connection with NLA in remote support", "RefUrl": "/notes/1912318"}, {"RefNumber": "187939", "RefComponent": "SV-SMG-SDD", "RefTitle": "SAP Servicetools Update (RTCCTOOL)", "RefUrl": "/notes/187939"}, {"RefNumber": "1609155", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Services", "RefUrl": "/notes/1609155"}, {"RefNumber": "1435190", "RefComponent": "SV-SMG-DIA-SRV-HPI", "RefTitle": "SAP Client Plug-In", "RefUrl": "/notes/1435190"}, {"RefNumber": "1323405", "RefComponent": "SV-SMG-SER", "RefTitle": "Technical Preparation of a CQC BPPO service", "RefUrl": "/notes/1323405"}, {"RefNumber": "1136804", "RefComponent": "SD-SLS-SO", "RefTitle": "Performance: SET LOCALE LANGUAGE", "RefUrl": "/notes/1136804"}, {"RefNumber": "1132350", "RefComponent": "BC-CST-UP", "RefTitle": "Incomplete update records", "RefUrl": "/notes/1132350"}, {"RefNumber": "1076804", "RefComponent": "BC-CST-UP", "RefTitle": "Incomplete update records", "RefUrl": "/notes/1076804"}, {"RefNumber": "1010428", "RefComponent": "SV-SMG-DIA", "RefTitle": "End-to-End Diagnostics", "RefUrl": "/notes/1010428"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2540971", "RefComponent": "SRM-EBP-TEC-PFM", "RefTitle": "How to run a ST12 Performance Trace", "RefUrl": "/notes/2540971 "}, {"RefNumber": "144864", "RefComponent": "SV-SMG-SER", "RefTitle": "Setting Up the ABAP Workload Monitor", "RefUrl": "/notes/144864 "}, {"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455 "}, {"RefNumber": "1435190", "RefComponent": "SV-SMG-DIA-SRV-HPI", "RefTitle": "SAP Client Plug-In", "RefUrl": "/notes/1435190 "}, {"RefNumber": "539977", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for add-on ST-PI", "RefUrl": "/notes/539977 "}, {"RefNumber": "1912318", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "WTS Connection with NLA in remote support", "RefUrl": "/notes/1912318 "}, {"RefNumber": "605795", "RefComponent": "XX-SER-NET", "RefTitle": "Windows Terminal Server connection in remote support", "RefUrl": "/notes/605795 "}, {"RefNumber": "1609155", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Services", "RefUrl": "/notes/1609155 "}, {"RefNumber": "1323405", "RefComponent": "SV-SMG-SER", "RefTitle": "Technical Preparation of a CQC BPPO service", "RefUrl": "/notes/1323405 "}, {"RefNumber": "1010428", "RefComponent": "SV-SMG-DIA", "RefTitle": "End-to-End Diagnostics", "RefUrl": "/notes/1010428 "}, {"RefNumber": "31515", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "Service connections", "RefUrl": "/notes/31515 "}, {"RefNumber": "35010", "RefComponent": "XX-SER-NET", "RefTitle": "Service connections: Composite note (overview))", "RefUrl": "/notes/35010 "}, {"RefNumber": "992474", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "URL in ST03 transaction profile", "RefUrl": "/notes/992474 "}, {"RefNumber": "1132350", "RefComponent": "BC-CST-UP", "RefTitle": "Incomplete update records", "RefUrl": "/notes/1132350 "}, {"RefNumber": "872800", "RefComponent": "SV-SMG-SER", "RefTitle": "Roles for SAP Solution Manager: SAP service provider", "RefUrl": "/notes/872800 "}, {"RefNumber": "1076804", "RefComponent": "BC-CST-UP", "RefTitle": "Incomplete update records", "RefUrl": "/notes/1076804 "}, {"RefNumber": "755977", "RefComponent": "SV-SMG-SDD", "RefTitle": "ST12 \"ABAP Trace for SAP EarlyWatch/GoingLive\"", "RefUrl": "/notes/755977 "}, {"RefNumber": "309711", "RefComponent": "SV-SMG-SDD", "RefTitle": "SAP Servicetools Update: Online help", "RefUrl": "/notes/309711 "}, {"RefNumber": "1136804", "RefComponent": "SD-SLS-SO", "RefTitle": "Performance: SET LOCALE LANGUAGE", "RefUrl": "/notes/1136804 "}, {"RefNumber": "981919", "RefComponent": "BC-CST-MM", "RefTitle": "All work processes are used by SAPSYS, semaphore 39", "RefUrl": "/notes/981919 "}, {"RefNumber": "187939", "RefComponent": "SV-SMG-SDD", "RefTitle": "SAP Servicetools Update (RTCCTOOL)", "RefUrl": "/notes/187939 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}