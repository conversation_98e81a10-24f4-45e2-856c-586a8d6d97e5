{"Request": {"Number": "1666670", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 329, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017362452017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001666670?language=E&token=2A6CFDB06C3B83D2A6AA6092685E39AC"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001666670", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001666670/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1666670"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 18}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.10.2015"}, "SAPComponentKey": {"_label": "Component", "value": "HAN-DB-ENG-BW"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP HANA BW Engine"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP HANA", "value": "HAN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'HAN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP HANA Database", "value": "HAN-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'HAN-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP HANA DB Engines", "value": "HAN-DB-ENG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'HAN-DB-ENG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP HANA BW Engine", "value": "HAN-DB-ENG-BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'HAN-DB-ENG-BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1666670 - SAP BW powered by SAP HANA - Landscape Deployment Planning"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Special system landscape deployment and configuration considerations for \"SAP BW, powered by SAP HANA\" (aka \"BW on HANA\").</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>MCOD MCOS MDOH Multi-SID MDC</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You are deploying (or have already deployed) BW on SAP HANA and need information about various system landscape configuration options, such as:<br /><br />- Multiple BW systems running \"on\" one SAP HANA appliance (multiple DBs, one SAP HANA system),<br /><br />- BW on HANA together with SAP HANA custom data marts or with other packaged applications designed to run on SAP HANA.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ol>\r\n<li><strong>Overview</strong><br /><br />SAP Note <a target=\"_blank\" href=\"/notes/1661202\">1661202</a> outlines important information about support for multiple applications on a single SAP HANA system. It is recommended to read SAP Note 1661202 first.<br /><br />SAP Note <a target=\"_blank\" href=\"/notes/1681092\">1681092</a> provides information about multiple databases running on one SAP HANA system.<br /><br />The note you are presently reading (SAP Note 1666670) contains information specific to BW on HANA within the context described in SAP Notes 1661202 and 1681092. It provides important information about the current status of specific system landscape configuration scenarios involving BW on HANA, and it also provides information about important limitations and further considerations.<br /><br />The term \"BW on HANA\" refers to \"SAP BW 7.3 or 7.4 or 7.5, powered by SAP HANA\". In the BW on HANA configuration, the database&#160;server of the BW system is running in a schema deployed within the SAP HANA database. The BW central instance and all application servers are not installed on the SAP HANA system, they are installed on separate servers.<br /><br />For more information on deploying BW on HANA, see <a target=\"_blank\" href=\"http://service.sap.com/instguides\">http://service.sap.com/instguides</a> &gt; SAP NetWeaver &gt; SAP NetWeaver 7.3 &gt; Installation &gt; \"E2E Implementation Roadmap - SAP BW powered by SAP HANA\".<br /><br />For more information about supported operating system&#160;and server configurations for the BW central instance and application servers, please see the product availability matrix for SAP NetWeaver at <a target=\"_blank\" href=\"http://service.sap.com/pam\">http://service.sap.com/pam</a>.</li>\r\n<li><strong>Current Status</strong><br /><br />Please see the \"further considerations\" section of SAP Note 1661202 for important information to take into account when evaluating any of the various scenarios described here.</li>\r\n<ul>\r\n<li><strong>Scenario - </strong>Multiple BW systems on one SAP HANA system:<br /><br />For a single database system, SAP <strong>does not</strong> support the deployment of multiple SAP BW systems on one <strong>production </strong>SAP HANA system.<br /><br />SAP <strong>does </strong>support the deployment of multiple SAP BW systems within a single-node or scale out <strong>non-production</strong> SAP HANA system (with one SAP HANA database for <em>each</em> SAP BW system). For important information, see SAP Note 1681092 about \"Multiple DBs one SAP HANA\" system.</li>\r\n<li><strong>Scenario - </strong>Multiple BW systems and SAP HANA multitenant database containers (MDC):<br /><br />If SAP HANA multitenant database containers are in use, SAP <strong>does</strong> support the deployment of multiple BW systems (with one BW system deployed per tenant database) in a <strong>production</strong> environment. For more details, please refer to SAP Note <a target=\"_blank\" href=\"/notes/2121768\">2121768</a>.</li>\r\n<li><strong>Scenario - </strong>BW and \"exceptions\" applications residing on the same production SAP HANA system:<br /><br />SAP <strong>does</strong> support the deployment of BW residing on the same production SAP HANA system together with other applications listed in the \"White List\" section of SAP Note 1661202&#160;(Important: See restrictions in the \"Further Considerations\" section of SAP Note 1661202. These must be taken into account). Mixed license types <strong><strong>are </strong></strong>allowed on one SAP HANA system. This means it is supported to run BW on one production SAP HANA system with other applications/scenarios listed in SAP Note 1661202, with distinct license types for the BW portion and for the other use cases, respectively.</li>\r\n<li><strong>Scenario</strong> - BW and any other packaged application residing on the same production SAP HANA system:<br /><br />SAP <strong>does</strong> <strong>not</strong> support the deployment of BW with any other packaged application or scenario (that are not on the \"White List\" in SAP Note 1661202) on the same <strong>production</strong> SAP HANA system . In other words, if the application is not listed in the \"White List\" section of SAP Note 1661202, it is not supported to run together with SAP BW on SAP HANA..<br /><br />SAP <strong>does</strong> support running BW together with any other SAP appliction or other scenario for <strong>non-production</strong> environments (Development, Quality Assurance, etc). Refer to the \"Further Considerations\" section of SAP Note 1661202.</li>\r\n<li><strong>Scenario - </strong>BW included as part of SAP NetWeaver based system:<br /><br />SAP <strong>does</strong> support the usage of BW <em>under certain conditions</em> for applications that are running a SAP NetWeaver stack in a <strong>production</strong> environment. This is the so called \"Embedded BW\" use case. For more information please see the presentation on SAP Community Network <a target=\"_blank\" href=\"http://scn.sap.com/docs/DOC-54501\">http://scn.sap.com/docs/DOC-54501</a>.<br /><br /></li>\r\n</ul>\r\n<li><strong><strong>Current Release Information (Including Recommendations):</strong></strong></li>\r\n</ol>\r\n<ul>\r\n<li>The recommended target release for current BW standalone on SAP HANA customers (and ones planning implementations) is SAP BW 7.4 SP 12 or higher.</li>\r\n<li>It is now possible for customers to run BW on SAP HANA with all parts of the BW system running on the SAP HANA DB. SAP Note <a target=\"_blank\" href=\"/notes/1645590\">1645590</a> provides some information about supported combinations of the SAP NetWeaver ABAP part of BW with the BI-Java part.</li>\r\n<li>For more technica details about SAP BW powered by SAP HANA - like dependencies on SAP HANA and SAP Kernel releases, please see SAP Note <a target=\"_blank\" href=\"/notes/1600929\">1600929</a>.</li>\r\n</ul></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "HAN-LM-INS (SAP HANA Installation)"}, {"Key": "Responsible                                                                                         ", "Value": "I822646"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D050032)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001666670/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001666670/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001666670/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001666670/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001666670/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001666670/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001666670/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001666670/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001666670/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2121768", "RefComponent": "HAN-DB", "RefTitle": "Considerations with SAP HANA multitenant database containers, SAP BW, and SAP BW/4HANA", "RefUrl": "/notes/2121768"}, {"RefNumber": "1849151", "RefComponent": "BC-INS", "RefTitle": "SAP NetWeaver Java on HANA release information", "RefUrl": "/notes/1849151"}, {"RefNumber": "1681092", "RefComponent": "HAN-LM-INS-DB", "RefTitle": "Multiple SAP HANA systems (SIDs) on the same underlying server(s)", "RefUrl": "/notes/1681092"}, {"RefNumber": "1661202", "RefComponent": "HAN-LM-INS", "RefTitle": "Support multiple applications one SAP HANA database / tenant DB", "RefUrl": "/notes/1661202"}, {"RefNumber": "1645590", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Java/Server SPs dependencies (and SupportDeskTool)", "RefUrl": "/notes/1645590"}, {"RefNumber": "1600929", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "SAP BW powered by SAP HANA DB: Information", "RefUrl": "/notes/1600929"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1661202", "RefComponent": "HAN-LM-INS", "RefTitle": "Support multiple applications one SAP HANA database / tenant DB", "RefUrl": "/notes/1661202 "}, {"RefNumber": "1849151", "RefComponent": "BC-INS", "RefTitle": "SAP NetWeaver Java on HANA release information", "RefUrl": "/notes/1849151 "}, {"RefNumber": "1645590", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Java/Server SPs dependencies (and SupportDeskTool)", "RefUrl": "/notes/1645590 "}, {"RefNumber": "1681092", "RefComponent": "HAN-LM-INS-DB", "RefTitle": "Multiple SAP HANA systems (SIDs) on the same underlying server(s)", "RefUrl": "/notes/1681092 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "740", "To": "740", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "750", "To": "750", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}