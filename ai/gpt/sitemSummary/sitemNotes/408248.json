{"Request": {"Number": "408248", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 302, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015016042017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000408248?language=E&token=EA1EF0B3CB8BE110D9FD750E881B9931"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000408248", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000408248/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "408248"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.05.2002"}, "SAPComponentKey": {"_label": "Component", "value": "SCM-TEC"}, "SAPComponentKeyText": {"_label": "Component", "value": "In Case of LiveCache Problems: Please use SCM-APO-LCA"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Supply Chain Management", "value": "SCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "In Case of LiveCache Problems: Please use SCM-APO-LCA", "value": "SCM-TEC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-TEC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "408248 - APO Support Package 12 for APO Release 3.0A"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note describes how to import APO Support Package 12 (SAPKY30A11) for APO Release 3.0A into your system.<br />APO Support Package 12 contains all notes that were created from April 9, 2001 to May 18, 2001. These contain corrections in the following areas:<br /><br /><B>Upgrade information for SAPGUI</B><br />The maintenance period for front end Release 4.6C expired on December 31, 2000. For this reason, we recommend that that you upgrade to front end Release 4.6D. The following notes contain additional information on this subject:</p> <UL><LI>0147519</LI></UL> <p><br /><B>Newly recommended procedure with transaction SPAM</B><br />As of version level APO Support Release 2 (SR2 includes APO Support Packages 1 - 8), we recommend that you always import several APO Support Packages (from Support Package 9 to the current Support Package) in a single queue.<br /><br />Maintenance strategy 'SAPGUI'</p> <UL><LI>0361222<br />SapPatch: Importing GUI patches<br /></LI></UL> <p><B>Kernel</B><br />Always import the latest 46D kernel.<br />For further information, refer to note</p> <UL><LI>0373047<br />Error in field input processing<br /></LI></UL> <p><B>Read the following notes if Support Package APO level 6 or lower is installed in your APO system</B></p> <UL><LI>314218<br />Transaction data conversion between Support Packages and</LI></UL> <UL><LI>361635<br />liveCache upgrade APO 3.0A SP7<br /><br /><B>before importing APO Support Package 7. </B><B>You may lose data if you use another procedure</B><B>.</B><B></B><br /><B></B></LI></UL> <UL><LI> APO Support Package 12 for APO Release 3.0A (SAPKY30A12)<br /></LI></UL> <UL><LI> OCX update<br /></LI></UL> <UL><LI> Updating the liveCache version: No update<br /></LI></UL> <UL><LI> COM routines<br /></LI></UL> <UL><LI> Optimizer corrections<br /></LI></UL> <UL><LI> Additional notes<br /></LI></UL> <p>APO Support Package 12 for Release APO 3.0A is available in the following languages:German, English, French, Spanish, Danish, Finnish, Hungarian, Italian, Japanese, Korean, Dutch, Norwegian, Polish, Portuguese, Swedish, Russian and Czech.<br />These languages are contained in APO Support Package 12.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SAPKY30A01, SAPKY30A02, SAPKY30A03, SAPKY30A04, SAPKY30A05, SAPKY30A06, SAPKY30A06, SAPKY30A07, SAPKY30A08, SAPKY30A09, SAPKY30A10, SAPKY30A11, SAPKY30A12, APO Support Package, APO patch, APO Release 3.0A<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>APO Support Package 12 (SAPKY30A12) for APO Release 3.0A requires a complete installation / upgrade (delivery on May 15, 2000).<br />You must also import APO Support Packages 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11 and 12 for APO Release 3.0A.<br /><br />If the system was set up <B>with </B><B>Service Release 1</B> for APO Release 3.0A, you must also import APO Support Packages 5, 6, 7, 8, 9, 10, 11 and 12 for APO Release 3.0A.<br /><br />If the system was set up <B>with</B><B> Service Release 2</B> for APO Release 3.0A, you only have to import Support Packages 9, 10, 11 and 12 for Release 3.0A.<br /><br />================================================================<br /></p> <UL><LI>Basis Support Packages for Release 4.6C</LI></UL> <p>For APO Support Package 12, it is absolutely imperative that you import all Basis Support Packages up to and including Basis Support Package 18 (SAPKB46C18).</p> <UL><LI>ABA Support Packages for Basis Release 4.6C</LI></UL> <p>It is absolutely imperative that you import all ABA Support Packages up to and including ABA Support Package 18 (SAPKA46C18) in addition to the Basis Support Package.</p> <UL><LI>BW Support Packages (2.0B)</LI></UL> <p>For APO Support Package 12, it is absolutely imperative that you import all BW Support Packages up to and including BW Support Package 14 (SAPKW20B14).<br />Note: 328237&#x00A0;&#x00A0;Importing Support Packages into a BW 2.0B system<br />Basis Support Packages/BW Support Packages are always imported separately from APO Support Packages.Import Basis Support Package/BW Support Packages and then import the APO Support Package immediately afterwards.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>To import APO Support Package 12 completely, follow the steps below:</p> <OL>1. <B>SPAM - Update</B></OL> <p>Before you start to import the Support Packages, you need to update the SPAM manager to the latest version.Download the latest version of the SPAM update from the OSS/SAPNET.<br />Import the latest SPAM update into your system. For more information, go to the initial screen of transaction SPAM and select the 'i' button (Online documentation: Help -&gt; Application help).</p> <OL>2. <B>APO corrections</B></OL> <p>You can download APO Support Package SAPKY30A12 from the Online Service System (OSS) or from SAPNet. The URL is:http://sapnet.sap.de/ocs-download. Follow the path: APO Support Packages -&gt; APO 3.0A.Download APO Support Package SAPKY30A12.For further details, see note 83458.</p> <OL>3. <B>OCX update</B></OL> <p>The OCX files that you require for APO Support Package 12 for APO Release 3.0A are on the sapervX FTP server.All other information on importing the new OCX file is contained in the following note:<br />0407355&#x00A0;&#x00A0;APO 3.0 front end patch 13 (May 2001)<br /></p> <OL>4. <B>Updating to liveCache Version 7.2.5 Build 1</B><br />There is no new liveCache version for APO Support Package 12.</OL> <p><B>Note that you must import a kernel patch &gt;= 579 for Release 46D if you install liveCache Version 7.2.5 Build 1. With a lower patch level, incompatibilities occur between the R/3 kernel patch and the database interface!!</B><B></B>Read the following note for more information on this subject:<br />406248 liveCache connect problem<br /><B>Important!!</B><B> </B><B>You must also import the latest adaslib as described in note 325402.</B><B></B><br />After a successful liveCache update, refer to note number: 424886<br />For all other information required for the liveCache update, refer to note:<br />379051 Importing a liveCache version &gt;= 7.2.4 B15<br />Note 0391746 contains further information on the UNIX liveCache and COM routines.</p> <OL>5. <B>COM routines</B></OL> <p>The COM routines are obtained via the sapservX.<br />The file SAPCOM_30_n.SAR (n = version counter) is stored in the directory ftp://sapservX/specific/apo/apo30/sp12/.<br />To import the new COM routines, follow the steps described in the notes below:<br />408774&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPAPO 3.0 COM object Build 20<br />157265&#x00A0;&#x00A0;&#x00A0;&#x00A0;Exchanging COM objects for liveCache in APO 3.0<br />As of liveCache Version 7.2.4, refer also to the following note:<br />336470&#x00A0;&#x00A0;&#x00A0;&#x00A0;Environment var. DBROOT no longer exists !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br />The liveCache must not be initialized for APO Support Package 12. !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br /></p> <OL>6. <B>Optimizer corrections</B></OL> <p>The optimizer corrections are obtained via the sapservX. The file <B>SAPAPO_n.SAR</B> (n = version counter <B>)</B> is stored in the directory ftp://sapservX/specific/apo/apo30/sp12/.<br />To import the new files, follow the steps described in the notes below:</p> <UL><LI>407251&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; APO 3.0 Optimizer Support Package 12</LI></UL> <UL><LI>300930&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; APO 3.0 Importing an Optimizer Version<br /></LI></UL> <OL>7. <B>You must implement the following additional notes manually!</B><br /></OL> <p>Note 353197<br />User for synchronous RFC dialog as of Release 4.6<br />Note 352844<br />Authorizations for RFC users: APO &lt;-&gt; R/3<br />Note 147218<br />SAP APO Demand Planning - datamart ==&gt; BW Patches<br />Note 396598<br />Exception OS_EXCEPTION when&#x00A0;&#x00A0;processing outputs<br />Note 386486<br />Category text incorrect after goods issue for delivery<br /></p> <OL>8. <B>Consider the following notes and implement these if they are relevant for your system:</B></OL> <p></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD><B>Number&#x00A0;&#x00A0; Component&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Priority&#x00A0;&#x00A0;Validity</B></TD></TR><TR><TD>0407550&#x00A0;&#x00A0;APO-SNP-BF Basic Functions&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; medium&#x00A0;&#x00A0;&#x00A0;&#x00A0; SP12</TD></TR> <TR><TD>0412087&#x00A0;&#x00A0;APO-FCS-BF Basic Functions&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;low&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SP12</TD></TR> <TR><TD>0413011&#x00A0;&#x00A0;APO-FCS Demand Planning&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;high&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SP12</TD></TR> <TR><TD>0414175&#x00A0;&#x00A0;APO-FCS-BF Basic Functions&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; low&#x00A0;&#x00A0;&#x00A0;&#x00A0; SP12-13</TD></TR> <TR><TD>0399482&#x00A0;&#x00A0;APO-FCS-BF Basic Functions&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; medium&#x00A0;&#x00A0;&#x00A0;&#x00A0; SP12</TD></TR> <TR><TD>0401039&#x00A0;&#x00A0;APO-SNP Supply Network Planning (SNP)&#x00A0;&#x00A0;high&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; SP12</TD></TR> <TR><TD>0406180&#x00A0;&#x00A0;APO-BAS-BF Basic Functions&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; medium&#x00A0;&#x00A0;&#x00A0;&#x00A0; SP12</TD></TR> <TR><TD>0416703&#x00A0;&#x00A0;APO-SNP-DPL Deployment&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;high&#x00A0;&#x00A0;&#x00A0;&#x00A0;SP11-13</TD></TR> <TR><TD>0412551&#x00A0;&#x00A0;APO-SNP Supply Network Planning (SNP)&#x00A0;&#x00A0;high&#x00A0;&#x00A0;&#x00A0;&#x00A0;SP12-13</TD></TR> <TR><TD>0420427&#x00A0;&#x00A0;APO-SDM-CTM Capable-to-Match&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; medium&#x00A0;&#x00A0;SP12-13</TD></TR> <TR><TD>0417948&#x00A0;&#x00A0;APO-COM COM objects in the liveCache&#x00A0;&#x00A0; medium</TD></TR> <TR><TD>0420427&#x00A0;&#x00A0;APO-SDM-CTM Capable-to-Match&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; medium&#x00A0;&#x00A0;SP12-13</TD></TR> <TR><TD>0422034&#x00A0;&#x00A0;APO-INT-PUR Purchasing / Purch. Req.&#x00A0;&#x00A0; high&#x00A0;&#x00A0;&#x00A0;&#x00A0;SP12-14</TD></TR> <TR><TD>0422948&#x00A0;&#x00A0;APO-FCS Demand Planning&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;low&#x00A0;&#x00A0;&#x00A0;&#x00A0;SP12-14</TD></TR> <TR><TD>0424722&#x00A0;&#x00A0;APO-FCS Demand Planning&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;low&#x00A0;&#x00A0;&#x00A0;&#x00A0;SP12-14</TD></TR> <TR><TD>0425376&#x00A0;&#x00A0;APO-FCS Demand Planning&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;high&#x00A0;&#x00A0;&#x00A0;&#x00A0;SP12-14</TD></TR> <TR><TD>0421410&#x00A0;&#x00A0;APO-SNP-OPT Optimization of SNP plan&#x00A0;&#x00A0; high&#x00A0;&#x00A0;&#x00A0;&#x00A0;SP12-15</TD></TR> <TR><TD>0440351&#x00A0;&#x00A0;APO-COM COM objects in the liveCache&#x00A0;&#x00A0; medium&#x00A0;&#x00A0;SP12-16</TD></TR> <TR><TD>0441653&#x00A0;&#x00A0;APO-SNP Supply Network Planning (SNP)&#x00A0;&#x00A0;medium&#x00A0;&#x00A0;SP12-16</TD></TR> </TABLE> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-UPG-OCS (Online Corr. Support (Tools:Supp.-Pack., Addon Install/Upgr))"}, {"Key": "Responsible                                                                                         ", "Value": "D027030"}, {"Key": "Processor                                                                                           ", "Value": "D027030"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000408248/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000408248/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000408248/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000408248/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000408248/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000408248/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000408248/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000408248/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000408248/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "83458", "RefComponent": "BC-UPG-OCS", "RefTitle": "@19@OCS Info: Patch download from SAP Service Marketplace", "RefUrl": "/notes/83458"}, {"RefNumber": "533076", "RefComponent": "BC-DB-LVC", "RefTitle": "Importing a liveCache version as of 7.2.5 B18", "RefUrl": "/notes/533076"}, {"RefNumber": "440351", "RefComponent": "BC-DB-LCA", "RefTitle": "Performance improvement in om17", "RefUrl": "/notes/440351"}, {"RefNumber": "425376", "RefComponent": "SCM-APO-FCS-EXT", "RefTitle": "Memory problems during data extraction", "RefUrl": "/notes/425376"}, {"RefNumber": "424886", "RefComponent": "BC-DB-LVC", "RefTitle": "Parameter values as of liveCache Version 7.2.5", "RefUrl": "/notes/424886"}, {"RefNumber": "424722", "RefComponent": "SCM-APO-FCS", "RefTitle": "Log cannot be displayed", "RefUrl": "/notes/424722"}, {"RefNumber": "422948", "RefComponent": "SCM-APO-FCS", "RefTitle": "APO 3.0: Problems when unlocking in forecast", "RefUrl": "/notes/422948"}, {"RefNumber": "422034", "RefComponent": "SCM-APO-INT-PUR", "RefTitle": "Webaz is not taken into account", "RefUrl": "/notes/422034"}, {"RefNumber": "421410", "RefComponent": "SCM-APO-SNP-OPT", "RefTitle": "Lot size is calculated incorrectly", "RefUrl": "/notes/421410"}, {"RefNumber": "420427", "RefComponent": "SCM-APO-SDM-CTM", "RefTitle": "Safety stock planning and removal of stocks", "RefUrl": "/notes/420427"}, {"RefNumber": "417948", "RefComponent": "BC-DB-LCA", "RefTitle": "/SAPAPO/OM_LCCHECK terminates with the error RC = 266", "RefUrl": "/notes/417948"}, {"RefNumber": "416703", "RefComponent": "SCM-APO-SNP-DPL", "RefTitle": "Deployment optimizer: Secondary and distrib. requirements", "RefUrl": "/notes/416703"}, {"RefNumber": "415099", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/415099"}, {"RefNumber": "414175", "RefComponent": "SCM-APO-FCS-BF", "RefTitle": "Error running report /SAPAPO/TS_PSTRU_GEN", "RefUrl": "/notes/414175"}, {"RefNumber": "413011", "RefComponent": "SCM-APO-FCS", "RefTitle": "20->30 Notes upgrade: multiple execution results in errors", "RefUrl": "/notes/413011"}, {"RefNumber": "412551", "RefComponent": "SCM-APO-SNP", "RefTitle": "Forecast horizon and requirements strategy ignored", "RefUrl": "/notes/412551"}, {"RefNumber": "412087", "RefComponent": "SCM-APO-FCS-BF", "RefTitle": "Error running report /SAPAPO/TS_PSTRU_GEN", "RefUrl": "/notes/412087"}, {"RefNumber": "407550", "RefComponent": "SCM-APO-SNP-BF", "RefTitle": "/SAPAPAPO/SDP94: txtmd for customer-defined characteristic", "RefUrl": "/notes/407550"}, {"RefNumber": "407355", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/407355"}, {"RefNumber": "407251", "RefComponent": "SCM-APO-OPT", "RefTitle": "APO 3.0 Optimizer Support Package 12", "RefUrl": "/notes/407251"}, {"RefNumber": "406248", "RefComponent": "BC-DB-LCA", "RefTitle": "liveCache Connect problems", "RefUrl": "/notes/406248"}, {"RefNumber": "406180", "RefComponent": "SCM-APO-SNP-BF", "RefTitle": "RLCDELETE PPM Deletion Correction", "RefUrl": "/notes/406180"}, {"RefNumber": "401039", "RefComponent": "SCM-APO-SNP", "RefTitle": "SNP Optimiser:short dump because of  CONVT_OVERFLOW", "RefUrl": "/notes/401039"}, {"RefNumber": "399482", "RefComponent": "SCM-APO-FCS-BF", "RefTitle": "Improve performance during initial access into SDP94", "RefUrl": "/notes/399482"}, {"RefNumber": "396598", "RefComponent": "BC-SRV-GBT-PPF", "RefTitle": "Exception OS_EXCEPTION when processing output", "RefUrl": "/notes/396598"}, {"RefNumber": "391746", "RefComponent": "SCM-TEC", "RefTitle": "COM Routines and liveCache Versions on UNIX", "RefUrl": "/notes/391746"}, {"RefNumber": "386486", "RefComponent": "SCM-APO-SNP", "RefTitle": "Category text incorrect after goods issue for delivery", "RefUrl": "/notes/386486"}, {"RefNumber": "373047", "RefComponent": "BC-ABA-SC", "RefTitle": "Error in field input processing", "RefUrl": "/notes/373047"}, {"RefNumber": "361635", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/361635"}, {"RefNumber": "361222", "RefComponent": "BC-FES-INS", "RefTitle": "SapPatch: Importing GUI Patches", "RefUrl": "/notes/361222"}, {"RefNumber": "353197", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/353197"}, {"RefNumber": "352844", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/352844"}, {"RefNumber": "336470", "RefComponent": "BC-DB-SDB", "RefTitle": "Environment var. DBROOT no longer exists", "RefUrl": "/notes/336470"}, {"RefNumber": "328237", "RefComponent": "BW-SYS", "RefTitle": "Importing Support Packages into a BW 2.0B system", "RefUrl": "/notes/328237"}, {"RefNumber": "325402", "RefComponent": "BC-DB-LVC", "RefTitle": "dbadaslib/dbsdbslib: How do I apply a patch?", "RefUrl": "/notes/325402"}, {"RefNumber": "314218", "RefComponent": "BC-DB-LCA", "RefTitle": "Transaction data conversion between SPs for APO 3.0", "RefUrl": "/notes/314218"}, {"RefNumber": "157265", "RefComponent": "BC-DB-LVC", "RefTitle": "Exchanging COM objects for liveCache in APO 3.0", "RefUrl": "/notes/157265"}, {"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519"}, {"RefNumber": "147218", "RefComponent": "SCM-APO", "RefTitle": "SAP APO Demand Planning - datamart ==> BW patches", "RefUrl": "/notes/147218"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519 "}, {"RefNumber": "424886", "RefComponent": "BC-DB-LVC", "RefTitle": "Parameter values as of liveCache Version 7.2.5", "RefUrl": "/notes/424886 "}, {"RefNumber": "440351", "RefComponent": "BC-DB-LCA", "RefTitle": "Performance improvement in om17", "RefUrl": "/notes/440351 "}, {"RefNumber": "417948", "RefComponent": "BC-DB-LCA", "RefTitle": "/SAPAPO/OM_LCCHECK terminates with the error RC = 266", "RefUrl": "/notes/417948 "}, {"RefNumber": "424722", "RefComponent": "SCM-APO-FCS", "RefTitle": "Log cannot be displayed", "RefUrl": "/notes/424722 "}, {"RefNumber": "373047", "RefComponent": "BC-ABA-SC", "RefTitle": "Error in field input processing", "RefUrl": "/notes/373047 "}, {"RefNumber": "396598", "RefComponent": "BC-SRV-GBT-PPF", "RefTitle": "Exception OS_EXCEPTION when processing output", "RefUrl": "/notes/396598 "}, {"RefNumber": "399482", "RefComponent": "SCM-APO-FCS-BF", "RefTitle": "Improve performance during initial access into SDP94", "RefUrl": "/notes/399482 "}, {"RefNumber": "422948", "RefComponent": "SCM-APO-FCS", "RefTitle": "APO 3.0: Problems when unlocking in forecast", "RefUrl": "/notes/422948 "}, {"RefNumber": "406180", "RefComponent": "SCM-APO-SNP-BF", "RefTitle": "RLCDELETE PPM Deletion Correction", "RefUrl": "/notes/406180 "}, {"RefNumber": "406248", "RefComponent": "BC-DB-LCA", "RefTitle": "liveCache Connect problems", "RefUrl": "/notes/406248 "}, {"RefNumber": "325402", "RefComponent": "BC-DB-LVC", "RefTitle": "dbadaslib/dbsdbslib: How do I apply a patch?", "RefUrl": "/notes/325402 "}, {"RefNumber": "412551", "RefComponent": "SCM-APO-SNP", "RefTitle": "Forecast horizon and requirements strategy ignored", "RefUrl": "/notes/412551 "}, {"RefNumber": "361222", "RefComponent": "BC-FES-INS", "RefTitle": "SapPatch: Importing GUI Patches", "RefUrl": "/notes/361222 "}, {"RefNumber": "83458", "RefComponent": "BC-UPG-OCS", "RefTitle": "@19@OCS Info: Patch download from SAP Service Marketplace", "RefUrl": "/notes/83458 "}, {"RefNumber": "328237", "RefComponent": "BW-SYS", "RefTitle": "Importing Support Packages into a BW 2.0B system", "RefUrl": "/notes/328237 "}, {"RefNumber": "391746", "RefComponent": "SCM-TEC", "RefTitle": "COM Routines and liveCache Versions on UNIX", "RefUrl": "/notes/391746 "}, {"RefNumber": "533076", "RefComponent": "BC-DB-LVC", "RefTitle": "Importing a liveCache version as of 7.2.5 B18", "RefUrl": "/notes/533076 "}, {"RefNumber": "425376", "RefComponent": "SCM-APO-FCS-EXT", "RefTitle": "Memory problems during data extraction", "RefUrl": "/notes/425376 "}, {"RefNumber": "157265", "RefComponent": "BC-DB-LVC", "RefTitle": "Exchanging COM objects for liveCache in APO 3.0", "RefUrl": "/notes/157265 "}, {"RefNumber": "147218", "RefComponent": "SCM-APO", "RefTitle": "SAP APO Demand Planning - datamart ==> BW patches", "RefUrl": "/notes/147218 "}, {"RefNumber": "314218", "RefComponent": "BC-DB-LCA", "RefTitle": "Transaction data conversion between SPs for APO 3.0", "RefUrl": "/notes/314218 "}, {"RefNumber": "401039", "RefComponent": "SCM-APO-SNP", "RefTitle": "SNP Optimiser:short dump because of  CONVT_OVERFLOW", "RefUrl": "/notes/401039 "}, {"RefNumber": "413011", "RefComponent": "SCM-APO-FCS", "RefTitle": "20->30 Notes upgrade: multiple execution results in errors", "RefUrl": "/notes/413011 "}, {"RefNumber": "420427", "RefComponent": "SCM-APO-SDM-CTM", "RefTitle": "Safety stock planning and removal of stocks", "RefUrl": "/notes/420427 "}, {"RefNumber": "422034", "RefComponent": "SCM-APO-INT-PUR", "RefTitle": "Webaz is not taken into account", "RefUrl": "/notes/422034 "}, {"RefNumber": "407550", "RefComponent": "SCM-APO-SNP-BF", "RefTitle": "/SAPAPAPO/SDP94: txtmd for customer-defined characteristic", "RefUrl": "/notes/407550 "}, {"RefNumber": "421410", "RefComponent": "SCM-APO-SNP-OPT", "RefTitle": "Lot size is calculated incorrectly", "RefUrl": "/notes/421410 "}, {"RefNumber": "416703", "RefComponent": "SCM-APO-SNP-DPL", "RefTitle": "Deployment optimizer: Secondary and distrib. requirements", "RefUrl": "/notes/416703 "}, {"RefNumber": "414175", "RefComponent": "SCM-APO-FCS-BF", "RefTitle": "Error running report /SAPAPO/TS_PSTRU_GEN", "RefUrl": "/notes/414175 "}, {"RefNumber": "412087", "RefComponent": "SCM-APO-FCS-BF", "RefTitle": "Error running report /SAPAPO/TS_PSTRU_GEN", "RefUrl": "/notes/412087 "}, {"RefNumber": "386486", "RefComponent": "SCM-APO-SNP", "RefTitle": "Category text incorrect after goods issue for delivery", "RefUrl": "/notes/386486 "}, {"RefNumber": "407251", "RefComponent": "SCM-APO-OPT", "RefTitle": "APO 3.0 Optimizer Support Package 12", "RefUrl": "/notes/407251 "}, {"RefNumber": "336470", "RefComponent": "BC-DB-SDB", "RefTitle": "Environment var. DBROOT no longer exists", "RefUrl": "/notes/336470 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APO", "From": "30A", "To": "30A", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}