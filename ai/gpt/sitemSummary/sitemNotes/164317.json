{"Request": {"Number": "164317", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 572, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000164317?language=E&token=3B96DC34AFA4C46A1ABA54C66037F473"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000164317", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000164317/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "164317"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "00.00.0000"}, "SAPComponentKey": {"_label": "Component", "value": "IS-OIL-BC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Please use component BC-UPG-ADDON"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oil", "value": "IS-OIL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-OIL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Please use component BC-UPG-ADDON", "value": "IS-OIL-BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-OIL-BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "164317 - IS-OIL Service Pack 3, 3.1H 1.0D/2.0D (LCP)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>**********************************************************************<br />* WARNING: This is an IS-OIL-specific note. If you DON'T have IS-Oil *<br />* installed on your system, this note does not apply to you. If this *<br />* note is applied and you do not have IS-Oil installed, you could&#x00A0;&#x00A0;&#x00A0;&#x00A0;*<br />* cause serious damage to your system.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *<br />**********************************************************************<br /><br />**********************************************************************<br />* This is an IS-OIL-specific NOTE FOR CUSTOMERS USING LEGAL CHANGE&#x00A0;&#x00A0; *<br />* PATCHES (LCP) instead of R/3 Support Packages (SP).&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*<br />* For details about LCPs see SAPNet note 86241 and notes referenced&#x00A0;&#x00A0;*<br />* therein. This note does not apply to you if you apply R/3 Support&#x00A0;&#x00A0;*<br />* Packages to your IS-Oil system. In this case see note 98642.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *<br />**********************************************************************<br /><br />Service Pack 3 (SP 3) for IS-Oil 2.0d (Upstream)/1.0d (Downstream),<br />(based on Core R/3 3.1H) was made available on July 31st, 1999.<br /><br />Whereas this note provides you with the technical details for<br />installing Service Pack 3, note 164301 contains informations on the<br />application side of Service Pack 3. When installing Service Pack 3<br />your system will be updated from Legal Change Patch level 39 (3.1H) to<br />Legal Change Patch level 52 (3.1H).<br /><br />The following object lists are included in the Service Pack containing<br />information about table entries of delivery class \"C\" and \"G\":<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; OILB31HITA<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; OILB31HTPI<br />These entries are imported to client 000 only. To bring these entries<br />to other clients please refer to the OILBASE Document (see SAPNet Note<br />94442) for more information. The OILBASE document describes how to<br />carry out the merge.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>IS-OIL Upstream 2.0d / Downstream 1.0d , Service Pack 3, SP 3,<br />Legal Change Patch 40, LCP 40, Legal Change Patch 52, LCP 52</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>To apply the following Legal Change Patches on systems with IS-OIL<br />2.0d/1.0d (based on R/3 3.1H) you must have successfully installed<br />Legal Change Patch 39 inclusive LCP-CRT beforehand.<br /><br />Furthermore, you should check if you have installed the most recent<br />SPAM update (see SAPNet Note 84962).<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br />Download the Legal Change Patches and LCP-CRTs listed below. If you are<br />using DOWNSTREAM built up new template client and run Application Test.<br /><br />*****&#x00A0;&#x00A0;SAVE&#x00A0;&#x00A0;RV61AFZB<br />IMPORTANT (see also Note 151452):<br />This only applies to you if the member RV61AFZB which contains<br />usersexits for programs in SAPLV61A has been modified !<br />One of the transports SODK002831 (Note 151452) which is included in<br />LCP-CRT40 contains the member RV61AFZB. When applying Service Pack 3<br />your userexits related to program SAPLV61A will be overwritten. Make<br />sure that you perform the following BEFORE applying Service Pack 3:<br />- Create a transport request (SE01)<br />- In the object list of this transport type in the following<br />&#x00A0;&#x00A0;entry manually -&gt;&#x00A0;&#x00A0;LIMU&#x00A0;&#x00A0;REPS&#x00A0;&#x00A0;RV61AFZB<br />- Release the transport request.<br /><br />AFTER the application of Service Pack 3:<br />-&#x00A0;&#x00A0;Import the transport request you just created and released (the one<br />&#x00A0;&#x00A0; which contains the original version of the object RV61AFZB) to reset<br />&#x00A0;&#x00A0; RV61AFZB to its original state.<br />*****&#x00A0;&#x00A0;End: SAVE&#x00A0;&#x00A0;RV61AFZB<br /></p> <b>I. Service Pack 3 functionality import:</b><br /> <p>=======================================<br />1. Apply the most recent available SPAM update (Do not forget to<br />&#x00A0;&#x00A0; restart transaction SPAM after the update !).<br /><br />2. Change the profile parameter of your system rdisp/max_wprun_time<br />&#x00A0;&#x00A0; to 0 and restart your R/3 system.<br /><br />3. Apply Legal Change Patches 40 to 52 by defining the following patch<br />&#x00A0;&#x00A0; queue: (We recommend that generation is disabled !)<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKE31H40&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Legal Change Patch 40)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKI3HL40&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(LCP-CRT 40)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKE31H41&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Legal Change Patch 41)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKI3HL41&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(LCP-CRT 41)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKE31H42&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Legal Change Patch 42)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKI3HL42&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(LCP-CRT 42)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKE31H43&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Legal Change Patch 43)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKI3HL43&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(LCP-CRT 43)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKE31H44&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Legal Change Patch 44)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKI3HL44&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(LCP-CRT 44)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKE31H45&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Legal Change Patch 45)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKI3HL45&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(LCP-CRT 45)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKE31H46&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Legal Change Patch 46)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKI3HL46&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(LCP-CRT 46)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKE31H47&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Legal Change Patch 47)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKI3HL47&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(LCP-CRT 47)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKE31H48&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Legal Change Patch 48)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKI3HL48&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(LCP-CRT 48)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKE31H49&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Legal Change Patch 49)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKI3HL49&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(LCP-CRT 49)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKE31H50&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Legal Change Patch 50)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKI3HL50&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(LCP-CRT 50)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKE31H51&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Legal Change Patch 51)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKI3HL51&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(LCP-CRT 51)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKE31H52&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Legal Change Patch 52)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKI3HL52&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(LCP-CRT 52)<br /><br />NOTE, that it is mandatory to apply these Legal Change Patches and<br />LCP-CRTs within one patch queue. Do not install any of the above listed<br />Legal Change Patches individually.<br /></p> <b>CAUTION:</b><br /> <p>Do not apply any further patches (&gt; SAPKE31H52) within this patch<br />queue.<br /><br />4. Restore the previous value of the profile parameter<br />&#x00A0;&#x00A0; rdisp/max_wprun_time and restart your R/3 system.<br /><br />5. Regenerate ABAP/4 Loads by executing Report RDDGENLD.<br />&#x00A0;&#x00A0; We recommend you to run generation over night.<br /></p> <b>II SP 3: Build new template client 065 and run Application Test:</b><br /> <p>================================================================<br />*** This section is only for customers using&#x00A0;&#x00A0;DOWNSTREAM ***<br /><br /> After the Service Pack has been successfully applied, perform the<br /> following steps:<br /><br />1. Empty the template client (normally 065) if it already exists</p> <b>&#x00A0;&#x00A0; CAUTION:</b><br /> <p>&#x00A0;&#x00A0; Do NOT clear client 065 if it contains customer data/modifications,<br />&#x00A0;&#x00A0; until you make sure that this data / the modifications are no longer<br />&#x00A0;&#x00A0; needed or, alternatively, you have saved the contents of the client.<br /><br />2. Copy client 000 to client 065 (profile SAP_ALL)<br /><br />3. Download and import Transport SODK004250 to client 065:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0;tp addtobuffer&#x00A0;&#x00A0;SODK004250&#x00A0;&#x00A0; &lt;SID&gt;&#x00A0;&#x00A0; U8<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&#x00A0;&#x00A0;tp import&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; SODK004250&#x00A0;&#x00A0; &lt;SID&gt;&#x00A0;&#x00A0; client065<br /><br />&#x00A0;&#x00A0; The transport is available on the following servers:<br />&#x00A0;&#x00A0; SAPSERVx: ~ftp/specific/isoil/31H/NOTES/Note.0163415/...<br />&#x00A0;&#x00A0; Those transport should only be applied to IS-OIL systems (see<br />&#x00A0;&#x00A0; Note 47531). Note 13719 describes how to import a correction to<br />&#x00A0;&#x00A0; a customer system.<br /> <br />4. Please run the Application Test and send us your application test<br />&#x00A0;&#x00A0; log as described in the Application Test Document (version 3.2):<br /><br />&#x00A0;&#x00A0; The new Application Test document (version 3.2) is available in pdf<br />&#x00A0;&#x00A0; format and can be downloaded from the following servers:<br />&#x00A0;&#x00A0; SAPSERVx: ~ftp/specific/isoil/31H/NOTES/Note.0163415/...<br />&#x00A0;&#x00A0; or can be requested from SAP as outlined in Note 94442.<br /></p> <b>III. SP 3 Online Documentation</b><br /> <p>==============================<br />The online documentation for SP 3 is available on SAPSERV under SAPSERVx:specific/isoil/31H/NOTES/Note.0164317/german<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/english<br />Please replace the old IS-Oil helpfile ISOIL.HLP (version March 1998, delivered on CD number 50021585) with the new IS-Oil helpfile ISOIL.HLP.<br />In addition to the content of the previous version, the new IS-Oil helpfile includes:</p> <UL><LI>the documentation of the Terminal Automation System</LI></UL> <UL><LI>the documentation of the Transportation Planning Interface</LI></UL> <UL><LI>the documentation of Trader's and Scheduler's Workbench</LI></UL> <UL><LI>the documentation of new functions in Exchanges (Evergreen contracts, Split Movements-based Netting, Defaulting of Terms of Payment for Invoice Cycles from the Customer-Material Info Record)<br /> </LI></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-UPG-ADDON (Upgrade Add-On Components)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D025102)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D025102)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000164317/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000164317/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000164317/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000164317/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000164317/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000164317/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000164317/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000164317/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000164317/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "98876", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/98876"}, {"RefNumber": "94442", "RefComponent": "IS-OIL", "RefTitle": "IS-Oil Technical Documentation (1.0D/2.0D)", "RefUrl": "/notes/94442"}, {"RefNumber": "77407", "RefComponent": "IS-OIL-BC", "RefTitle": "CRTs for IS-Oil", "RefUrl": "/notes/77407"}, {"RefNumber": "67261", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Reports to analyze and correct stock qties in IS-OIL systems", "RefUrl": "/notes/67261"}, {"RefNumber": "53902", "RefComponent": "BC-UPG-OCS", "RefTitle": "Conflicts between Support Packages and add-ons", "RefUrl": "/notes/53902"}, {"RefNumber": "53136", "RefComponent": "IS-OIL-BC", "RefTitle": "Support Packages and IS-Oil / IS-MINE / IS-CWM - information", "RefUrl": "/notes/53136"}, {"RefNumber": "47531", "RefComponent": "IS-OIL", "RefTitle": "IS-OIL / IS-MINE / IS-CWM correction guideline", "RefUrl": "/notes/47531"}, {"RefNumber": "332654", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL / IS-MINE / IS-CWM: Overview of SAP Notes", "RefUrl": "/notes/332654"}, {"RefNumber": "310788", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No provision to extend Idoc OILDVA01 Driver/Veh", "RefUrl": "/notes/310788"}, {"RefNumber": "308868", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Tank ID error on SD order items after hot package", "RefUrl": "/notes/308868"}, {"RefNumber": "306761", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No warning for multiple Driver Vehicle Assignments", "RefUrl": "/notes/306761"}, {"RefNumber": "306559", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Inconsistent date used in order item create", "RefUrl": "/notes/306559"}, {"RefNumber": "303220", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Tank ID defaulting in orders incorrect on change", "RefUrl": "/notes/303220"}, {"RefNumber": "301385", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Population of ship-to in TAS shipment IDoc", "RefUrl": "/notes/301385"}, {"RefNumber": "301021", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Inconsistent date used in order item create", "RefUrl": "/notes/301021"}, {"RefNumber": "301014", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Incorrect TPP check in IDOC_OUTPUT_OILTPI50", "RefUrl": "/notes/301014"}, {"RefNumber": "300109", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No warning for multiple Driver Vehicle Assignments", "RefUrl": "/notes/300109"}, {"RefNumber": "216249", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Status messages not returned to TPI", "RefUrl": "/notes/216249"}, {"RefNumber": "216067", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Additional customer exits", "RefUrl": "/notes/216067"}, {"RefNumber": "208587", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No warning for multiple Driver Vehicle Assignments", "RefUrl": "/notes/208587"}, {"RefNumber": "201913", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Function call conflict in OIK_TPI_ORDERS_CREATE", "RefUrl": "/notes/201913"}, {"RefNumber": "200706", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "GUI status OIIPBLCT missing", "RefUrl": "/notes/200706"}, {"RefNumber": "200316", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error in incompletion for mandatory Tank ID", "RefUrl": "/notes/200316"}, {"RefNumber": "199541", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Endless loop in IDOC_OUTPUT_OILTPI50", "RefUrl": "/notes/199541"}, {"RefNumber": "198627", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Incorrect shipping point in O4PO", "RefUrl": "/notes/198627"}, {"RefNumber": "197182", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Quantity missing from OILSHI01 if CPLID not '1'", "RefUrl": "/notes/197182"}, {"RefNumber": "196052", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error OC605 occurs incorrectly", "RefUrl": "/notes/196052"}, {"RefNumber": "195707", "RefComponent": "IS-OIL-DS", "RefTitle": "Slow response to delivery create", "RefUrl": "/notes/195707"}, {"RefNumber": "194327", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Incorrect OIK37 updates via TPI", "RefUrl": "/notes/194327"}, {"RefNumber": "193231", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "ROIKFCL1 functionality missing after Service Pack", "RefUrl": "/notes/193231"}, {"RefNumber": "193229", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Use cust.exit on KNA1 change pointer for updates to SCP OTWS", "RefUrl": "/notes/193229"}, {"RefNumber": "193227", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Abort A222 in OIK_TAS_DATA_POST", "RefUrl": "/notes/193227"}, {"RefNumber": "193023", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Handling of ETAs for planned shipments", "RefUrl": "/notes/193023"}, {"RefNumber": "192273", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Collected changes for TPI", "RefUrl": "/notes/192273"}, {"RefNumber": "192046", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No KNA1 change pointer for updates to SCP OTWS", "RefUrl": "/notes/192046"}, {"RefNumber": "192014", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Error OC709 occurs on TPI OTWS entry", "RefUrl": "/notes/192014"}, {"RefNumber": "191696", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "OILSHI01 IDoc keeps status 64 after processing", "RefUrl": "/notes/191696"}, {"RefNumber": "191432", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Tank ID assignment at order change incorrect", "RefUrl": "/notes/191432"}, {"RefNumber": "191429", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "PBL OTWS entries missing from TPI Location IDoc", "RefUrl": "/notes/191429"}, {"RefNumber": "191401", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Action code in OILTPI50 populated incorrectly", "RefUrl": "/notes/191401"}, {"RefNumber": "191154", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "TPI IDocs fail with mixed user defaults", "RefUrl": "/notes/191154"}, {"RefNumber": "191151", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Missing TPI functionality", "RefUrl": "/notes/191151"}, {"RefNumber": "191149", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Abort A222 in OIK_TAS_DATA_POST", "RefUrl": "/notes/191149"}, {"RefNumber": "186151", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Shipment inbound process update task", "RefUrl": "/notes/186151"}, {"RefNumber": "185653", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "OILTPI01 Missing E1OILPS segment II (31H)", "RefUrl": "/notes/185653"}, {"RefNumber": "185620", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Incorrect UOM conversion in ROIKPALE", "RefUrl": "/notes/185620"}, {"RefNumber": "185617", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error OC709 raised on change of OTWS entry", "RefUrl": "/notes/185617"}, {"RefNumber": "185453", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "OIGD Customer Area data cannot be saved on change", "RefUrl": "/notes/185453"}, {"RefNumber": "182859", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Override of storage SOE failes on shipment inbound", "RefUrl": "/notes/182859"}, {"RefNumber": "182178", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Shipment Load ID assigned with Valid-from > Valid-to", "RefUrl": "/notes/182178"}, {"RefNumber": "181435", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Override of storage SOE failes on shipment inbound", "RefUrl": "/notes/181435"}, {"RefNumber": "181239", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Tank ID not stored in OIK37 for Oil TPI", "RefUrl": "/notes/181239"}, {"RefNumber": "181099", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Collective changes to shipment inbound 40B", "RefUrl": "/notes/181099"}, {"RefNumber": "181089", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/181089"}, {"RefNumber": "181086", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/181086"}, {"RefNumber": "181083", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/181083"}, {"RefNumber": "181082", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/181082"}, {"RefNumber": "181061", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Inconsistent data and status update in TPI interfa", "RefUrl": "/notes/181061"}, {"RefNumber": "181008", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error in Shipment Create abends OILSHI01 processing", "RefUrl": "/notes/181008"}, {"RefNumber": "180759", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "OILTPI01 Missing E1OILPS segment", "RefUrl": "/notes/180759"}, {"RefNumber": "180696", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Justification of storage object field", "RefUrl": "/notes/180696"}, {"RefNumber": "180418", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Collective changes distributed after Hot Pack nov 99", "RefUrl": "/notes/180418"}, {"RefNumber": "180401", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Collective changes to shipment inbound 31H", "RefUrl": "/notes/180401"}, {"RefNumber": "180082", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Applying LCPs 53-61 incl. on R/3 3.1H", "RefUrl": "/notes/180082"}, {"RefNumber": "179319", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Wrong data in shipment tpi screens", "RefUrl": "/notes/179319"}, {"RefNumber": "179151", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Delivery creation uses system date", "RefUrl": "/notes/179151"}, {"RefNumber": "179143", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Wrong tank check on order entry", "RefUrl": "/notes/179143"}, {"RefNumber": "178423", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Changes delivered with CRT", "RefUrl": "/notes/178423"}, {"RefNumber": "178164", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Screen 200 missing in program", "RefUrl": "/notes/178164"}, {"RefNumber": "177825", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Order distribution action code", "RefUrl": "/notes/177825"}, {"RefNumber": "177563", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Save tank for delivery on shipment inbound", "RefUrl": "/notes/177563"}, {"RefNumber": "177556", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "change communication structure for delete IDoc processing", "RefUrl": "/notes/177556"}, {"RefNumber": "176218", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Handling type overwritten by incoming shipment IDoc", "RefUrl": "/notes/176218"}, {"RefNumber": "176214", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "SOE GRP from IDOC OILSHI01 does not override default values", "RefUrl": "/notes/176214"}, {"RefNumber": "175917", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Wrong quantity used for ASTM range check", "RefUrl": "/notes/175917"}, {"RefNumber": "175840", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Segments E1OILSI E1OILVH too short", "RefUrl": "/notes/175840"}, {"RefNumber": "175835", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/175835"}, {"RefNumber": "175777", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Changes to order distribution", "RefUrl": "/notes/175777"}, {"RefNumber": "175646", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Wrong quantity used for ASTM range check", "RefUrl": "/notes/175646"}, {"RefNumber": "174710", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "shipment inbound, change plant", "RefUrl": "/notes/174710"}, {"RefNumber": "174462", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "TPI: Filter objects not possible", "RefUrl": "/notes/174462"}, {"RefNumber": "171399", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "TPI: Segment E1OILTW Error in OILTPI01", "RefUrl": "/notes/171399"}, {"RefNumber": "170583", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Incompletion message for deleted contract/ order item", "RefUrl": "/notes/170583"}, {"RefNumber": "169228", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Error during DELETE OIKLIDR", "RefUrl": "/notes/169228"}, {"RefNumber": "169160", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Corrections in SVPs: 3.1H/SVP3 4.0B/SVP1", "RefUrl": "/notes/169160"}, {"RefNumber": "164301", "RefComponent": "IS-OIL-DS", "RefTitle": "Information on new functions in Service Pack 3", "RefUrl": "/notes/164301"}, {"RefNumber": "163470", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "MCOE Performance optimization for KNVV access", "RefUrl": "/notes/163470"}, {"RefNumber": "162652", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Missing UserExits in Function IDOC_INPUT_OILSH1", "RefUrl": "/notes/162652"}, {"RefNumber": "161917", "RefComponent": "CA-JVA", "RefTitle": "JV configuration: Check for corporate cost center", "RefUrl": "/notes/161917"}, {"RefNumber": "161533", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Quantity Schedule does not capture returns", "RefUrl": "/notes/161533"}, {"RefNumber": "161514", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Automatic stage assignment", "RefUrl": "/notes/161514"}, {"RefNumber": "160868", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "New pricing in order in change mode is incorr. for > 1 item", "RefUrl": "/notes/160868"}, {"RefNumber": "160513", "RefComponent": "CA-JVA", "RefTitle": "JV billing: Incorrect document numbers selected", "RefUrl": "/notes/160513"}, {"RefNumber": "160234", "RefComponent": "CA-JVA", "RefTitle": "JV EDI: Invoice date not on 819 document", "RefUrl": "/notes/160234"}, {"RefNumber": "160123", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Field OIA_SPLTIV is not an input field (ALE problem)", "RefUrl": "/notes/160123"}, {"RefNumber": "159634", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Billing block not interpreted in billing due list", "RefUrl": "/notes/159634"}, {"RefNumber": "159390", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Pricing UoM not copied from info record to contract", "RefUrl": "/notes/159390"}, {"RefNumber": "159385", "RefComponent": "CA-JVA", "RefTitle": "Function groups RSSG and GUSL in IS-OIL and BIW", "RefUrl": "/notes/159385"}, {"RefNumber": "159169", "RefComponent": "CA-JVA", "RefTitle": "JV GR/IR links does not handle reverse entries", "RefUrl": "/notes/159169"}, {"RefNumber": "158939", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/158939"}, {"RefNumber": "158896", "RefComponent": "CA-JVA", "RefTitle": "JV cutback: Program termination with CONVT_OVERFLOW", "RefUrl": "/notes/158896"}, {"RefNumber": "158378", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MAP-Deletion of OICQ* entries for cancelled invoice", "RefUrl": "/notes/158378"}, {"RefNumber": "158298", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorr. Price in PO created wrt contract/hd discount scales", "RefUrl": "/notes/158298"}, {"RefNumber": "158148", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Missing INCOTERMS in LID Determination User exit", "RefUrl": "/notes/158148"}, {"RefNumber": "157920", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "VKDFS table is not filled properly during invoice generation", "RefUrl": "/notes/157920"}, {"RefNumber": "157801", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Loss in transfer/STO and change of valuation type", "RefUrl": "/notes/157801"}, {"RefNumber": "157798", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Missing User-exit in moigsf60 (EXIT_SAPMOIGS_710)", "RefUrl": "/notes/157798"}, {"RefNumber": "157781", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Incorrect valuation after KINAK = 'K'", "RefUrl": "/notes/157781"}, {"RefNumber": "157693", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Type conflict in CALL FUNCTION OIK_TO_ISO_MEASURE_UNIT_COD", "RefUrl": "/notes/157693"}, {"RefNumber": "157645", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Wrong handling of group cond. in invoice verif.", "RefUrl": "/notes/157645"}, {"RefNumber": "157567", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/157567"}, {"RefNumber": "157494", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Location ID in Credit note and Credit note request", "RefUrl": "/notes/157494"}, {"RefNumber": "157443", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/157443"}, {"RefNumber": "157342", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Netting with zero value documents", "RefUrl": "/notes/157342"}, {"RefNumber": "157222", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External Details and sales support VC01", "RefUrl": "/notes/157222"}, {"RefNumber": "157157", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/157157"}, {"RefNumber": "157044", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Exit from step-loop 00043 in scheduling", "RefUrl": "/notes/157044"}, {"RefNumber": "156993", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD User Exit 007 before creation of MM document", "RefUrl": "/notes/156993"}, {"RefNumber": "156906", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Abort on Location Archiving Deletion program", "RefUrl": "/notes/156906"}, {"RefNumber": "156700", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong UoM in automatically created purchase order", "RefUrl": "/notes/156700"}, {"RefNumber": "156697", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "If taxes are 0,accounting error in differential Inv", "RefUrl": "/notes/156697"}, {"RefNumber": "156599", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Document item quantity assign. missing O9540", "RefUrl": "/notes/156599"}, {"RefNumber": "156493", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Event in cond. processing set incor. in inv. verif.", "RefUrl": "/notes/156493"}, {"RefNumber": "156455", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: PO create going through an extra screen.", "RefUrl": "/notes/156455"}, {"RefNumber": "156393", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/156393"}, {"RefNumber": "156237", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD 'Delete event' in maintain completed shipment", "RefUrl": "/notes/156237"}, {"RefNumber": "155914", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Printing exchange statements using RSNAST00", "RefUrl": "/notes/155914"}, {"RefNumber": "155602", "RefComponent": "CA-JVA", "RefTitle": "Derive Profit Center from the valuation table T8JD", "RefUrl": "/notes/155602"}, {"RefNumber": "155176", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Confirmed quantity zero in call off (mult. ship-to)", "RefUrl": "/notes/155176"}, {"RefNumber": "154841", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD temperature UoM in popup/multiple MM docs per transaction", "RefUrl": "/notes/154841"}, {"RefNumber": "154790", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Allow non-movement delivery items in TD shipments", "RefUrl": "/notes/154790"}, {"RefNumber": "154694", "RefComponent": "CA-JVA", "RefTitle": "Check for equity type deletion incorrect", "RefUrl": "/notes/154694"}, {"RefNumber": "154687", "RefComponent": "CA-JVA", "RefTitle": "G4024 / G4016 / G4017 in clearing with Downpayment", "RefUrl": "/notes/154687"}, {"RefNumber": "154673", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Errors in repricing in invoice verification", "RefUrl": "/notes/154673"}, {"RefNumber": "154630", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: JV/AM Transfers: incorr. depreciation start", "RefUrl": "/notes/154630"}, {"RefNumber": "154539", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Create only BTCI records if dmbtr ne 0", "RefUrl": "/notes/154539"}, {"RefNumber": "154537", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Doubling of formula value after fee processing", "RefUrl": "/notes/154537"}, {"RefNumber": "154502", "RefComponent": "IS-OIL-DS", "RefTitle": "Oil: Several performance improvements", "RefUrl": "/notes/154502"}, {"RefNumber": "154498", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Movement based netting: VAT amount appears double", "RefUrl": "/notes/154498"}, {"RefNumber": "154470", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Duplicate Pricing conditions being displayed", "RefUrl": "/notes/154470"}, {"RefNumber": "154392", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-Oil: TDP Handling Type not copied at PO creation", "RefUrl": "/notes/154392"}, {"RefNumber": "154243", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Collective Note for IS-Oil/TAS", "RefUrl": "/notes/154243"}, {"RefNumber": "154181", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Document flow for shipment too slow", "RefUrl": "/notes/154181"}, {"RefNumber": "154042", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Automatic LID determination after document change", "RefUrl": "/notes/154042"}, {"RefNumber": "154025", "RefComponent": "CA-JVA", "RefTitle": "Error G4024 in clearing", "RefUrl": "/notes/154025"}, {"RefNumber": "154019", "RefComponent": "CA-JVA", "RefTitle": "JV equity change: Entry in process history after test run", "RefUrl": "/notes/154019"}, {"RefNumber": "153945", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: GJTx Transactions: Values in depr.areas", "RefUrl": "/notes/153945"}, {"RefNumber": "153768", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "BWTAR for reservation not used for LID-assignment", "RefUrl": "/notes/153768"}, {"RefNumber": "153644", "RefComponent": "CA-JVA", "RefTitle": "G4016  OI's with and without order, cost center", "RefUrl": "/notes/153644"}, {"RefNumber": "153623", "RefComponent": "CA-JVA", "RefTitle": "G4016  No condition class for tax", "RefUrl": "/notes/153623"}, {"RefNumber": "153565", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Manual Sales office & Group entries are lost", "RefUrl": "/notes/153565"}, {"RefNumber": "153536", "RefComponent": "CA-JVA", "RefTitle": "JV billing extract: Performance for large ventures", "RefUrl": "/notes/153536"}, {"RefNumber": "153327", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: 3.1H/1.0D  TDP check reports (ED Inventory)", "RefUrl": "/notes/153327"}, {"RefNumber": "153269", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Cumulative cond. does not function in Credit Memo", "RefUrl": "/notes/153269"}, {"RefNumber": "153222", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "external details at stock transfer order", "RefUrl": "/notes/153222"}, {"RefNumber": "153125", "RefComponent": "CA-JVA", "RefTitle": "Downpayment clearing: wrong partial clearing", "RefUrl": "/notes/153125"}, {"RefNumber": "153121", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Scrolling SAPMOIGS3700; take over dates after user exit", "RefUrl": "/notes/153121"}, {"RefNumber": "152728", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Bug in routine XKOMV_BEWERTEN", "RefUrl": "/notes/152728"}, {"RefNumber": "152678", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Cost sharing on document item level", "RefUrl": "/notes/152678"}, {"RefNumber": "152651", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MM contract: wrong base product stored", "RefUrl": "/notes/152651"}, {"RefNumber": "152478", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR21-no FI doc. but the accounts are changed/CSS 198625/1999", "RefUrl": "/notes/152478"}, {"RefNumber": "152294", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "E1OILT1-EXTDELNR not moved to G_OIKLOAD_TAB", "RefUrl": "/notes/152294"}, {"RefNumber": "152285", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong fee proposal at IV for delivery note selection", "RefUrl": "/notes/152285"}, {"RefNumber": "152245", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: EXIT_SAPLOIK3_110 customer exit is unworkable", "RefUrl": "/notes/152245"}, {"RefNumber": "152213", "RefComponent": "CA-JVA", "RefTitle": "Error G4016 or G4017 when clearing IDOC RSEINB00", "RefUrl": "/notes/152213"}, {"RefNumber": "152033", "RefComponent": "CA-JVA", "RefTitle": "G4024 on intercomp. clearing of FI and MM invoices", "RefUrl": "/notes/152033"}, {"RefNumber": "152002", "RefComponent": "CA-JVA", "RefTitle": "Error message G4017 in clearing documents", "RefUrl": "/notes/152002"}, {"RefNumber": "151959", "RefComponent": "CA-JVA", "RefTitle": "JV taxreport - Incorrect tax amount and baseline date", "RefUrl": "/notes/151959"}, {"RefNumber": "151815", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: Document display CRP pricing incorrect", "RefUrl": "/notes/151815"}, {"RefNumber": "151809", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Performance Improvement oic_ditab", "RefUrl": "/notes/151809"}, {"RefNumber": "151733", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Wrong posting date of SES created for SCD item", "RefUrl": "/notes/151733"}, {"RefNumber": "151523", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: Upgrade to Rel. 4.0B: RGJT156C ends w/error", "RefUrl": "/notes/151523"}, {"RefNumber": "151452", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Repricing and Redetermination of F&A conditions 31H", "RefUrl": "/notes/151452"}, {"RefNumber": "151361", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Missing exg. number on billing index for diff. inv.", "RefUrl": "/notes/151361"}, {"RefNumber": "151219", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Document currency display in purchase order history", "RefUrl": "/notes/151219"}, {"RefNumber": "151196", "RefComponent": "IS-OIL-DS", "RefTitle": "SD business views for SAP AS in IS-Oil systems", "RefUrl": "/notes/151196"}, {"RefNumber": "151027", "RefComponent": "CA-JVA", "RefTitle": "cash call incorrectly calculates baseline date", "RefUrl": "/notes/151027"}, {"RefNumber": "151018", "RefComponent": "CA-JVA", "RefTitle": "JV EDI billing: New functionality", "RefUrl": "/notes/151018"}, {"RefNumber": "150982", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "ED difference posting for differential invoice", "RefUrl": "/notes/150982"}, {"RefNumber": "150951", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Missing overload check in compartment allocation details", "RefUrl": "/notes/150951"}, {"RefNumber": "150831", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Movement based netting: VAT on MM side missing (II)", "RefUrl": "/notes/150831"}, {"RefNumber": "150752", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Error in posting exch. goods receipt reversal", "RefUrl": "/notes/150752"}, {"RefNumber": "150686", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "For F&A pricing, tmp. costing calculates value 0 !?", "RefUrl": "/notes/150686"}, {"RefNumber": "150318", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Split excise duty may cause wrong IR postings", "RefUrl": "/notes/150318"}, {"RefNumber": "150207", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD OIGI_CHANGE_SHIPMENT_RFC (add document -> O9005)", "RefUrl": "/notes/150207"}, {"RefNumber": "150132", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: credit management: value update problem", "RefUrl": "/notes/150132"}, {"RefNumber": "150071", "RefComponent": "IS-OIL-DS", "RefTitle": "Application test in IS-Oil 3.1H with HP/LCP 39", "RefUrl": "/notes/150071"}, {"RefNumber": "150011", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Duplicate entries in Controlstructure F4-Help", "RefUrl": "/notes/150011"}, {"RefNumber": "149969", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Multiple Partners from Contract in LID-assignment", "RefUrl": "/notes/149969"}, {"RefNumber": "149909", "RefComponent": "CA-JVA", "RefTitle": "currency exchange rate does not show the right amount", "RefUrl": "/notes/149909"}, {"RefNumber": "149774", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "IS-Oil: Error in exchange contract delivery in batch input", "RefUrl": "/notes/149774"}, {"RefNumber": "149755", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Is-Oil: VL07 inconsistencies when 2-step tracking is active", "RefUrl": "/notes/149755"}, {"RefNumber": "149657", "RefComponent": "CA-JVA", "RefTitle": "Cost Center section appears twice in non-op form", "RefUrl": "/notes/149657"}, {"RefNumber": "149650", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Only prior to load; loading needed for 2-step transfers", "RefUrl": "/notes/149650"}, {"RefNumber": "149542", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Excise duty licenses - Performance", "RefUrl": "/notes/149542"}, {"RefNumber": "149493", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Remove obsolete entries from Table OIGSM", "RefUrl": "/notes/149493"}, {"RefNumber": "149487", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Revaluation of logical inventory value", "RefUrl": "/notes/149487"}, {"RefNumber": "149395", "RefComponent": "CA-JVA", "RefTitle": "Allow CI/NPI to run using transaction SE38", "RefUrl": "/notes/149395"}, {"RefNumber": "149290", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Incompletion log not cleared for TAS relevancy on doc.header", "RefUrl": "/notes/149290"}, {"RefNumber": "148944", "RefComponent": "CA-JVA", "RefTitle": "non oper cash call uses wrong currency for FB01 posting", "RefUrl": "/notes/148944"}, {"RefNumber": "148611", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/148611"}, {"RefNumber": "148573", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "IDOCs not being created for shpts in Shipment Create", "RefUrl": "/notes/148573"}, {"RefNumber": "148424", "RefComponent": "CA-JVA", "RefTitle": "Line item report: no sort on cost object column", "RefUrl": "/notes/148424"}, {"RefNumber": "148411", "RefComponent": "CA-JVA", "RefTitle": "JV billing extract: Warning GJ837 contains no info", "RefUrl": "/notes/148411"}, {"RefNumber": "148407", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Condition not quant. adjusted in G/R but in IV", "RefUrl": "/notes/148407"}, {"RefNumber": "148397", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Collective Note for IS-Oil/TAS Servicepack2", "RefUrl": "/notes/148397"}, {"RefNumber": "148321", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Gain for 2-step transf.; passing dates; LOV ; Delivery Date", "RefUrl": "/notes/148321"}, {"RefNumber": "148140", "RefComponent": "CA-JVA", "RefTitle": "JV billing: Partner out of CI status not selected", "RefUrl": "/notes/148140"}, {"RefNumber": "147964", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "O4H1 ; Background ASTM conversion for batch Input", "RefUrl": "/notes/147964"}, {"RefNumber": "147813", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "ME31: external details not copied", "RefUrl": "/notes/147813"}, {"RefNumber": "147804", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Incompletion procedure doesn't recognize OIC_MOT", "RefUrl": "/notes/147804"}, {"RefNumber": "147775", "RefComponent": "CA-JVA", "RefTitle": "G4024  on clearing: no FI clearing line", "RefUrl": "/notes/147775"}, {"RefNumber": "147724", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Missing Customer-Exit for add. Segm. to IDoc OILSHL01", "RefUrl": "/notes/147724"}, {"RefNumber": "147466", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Wrong value posted for TPP in LID-determination", "RefUrl": "/notes/147466"}, {"RefNumber": "147407", "RefComponent": "MM-PUR-REQ", "RefTitle": "ME57 PReqs are deleted through changes in dates", "RefUrl": "/notes/147407"}, {"RefNumber": "147375", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Syntaxerror SAPMV45A after IS-Oil ServicePack 2", "RefUrl": "/notes/147375"}, {"RefNumber": "147368", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: User exits and User screen in TAS-Interface", "RefUrl": "/notes/147368"}, {"RefNumber": "147288", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/147288"}, {"RefNumber": "147180", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Missing SAP Enhancements for TD-Masterdata SP 2", "RefUrl": "/notes/147180"}, {"RefNumber": "147141", "RefComponent": "CA-JVA", "RefTitle": "JV EDI outbound: Ventures not selected", "RefUrl": "/notes/147141"}, {"RefNumber": "147061", "RefComponent": "CA-JVA", "RefTitle": "G4029 on invoice processing: no recovery indicator", "RefUrl": "/notes/147061"}, {"RefNumber": "147057", "RefComponent": "CA-JVA", "RefTitle": "G4039 in CO/JV plan integration", "RefUrl": "/notes/147057"}, {"RefNumber": "146906", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Movement based netting: VAT on MM side missing", "RefUrl": "/notes/146906"}, {"RefNumber": "146884", "RefComponent": "CA-JVA", "RefTitle": "VBA does not pick up 2nd JV doc with line 998, 999, etc.", "RefUrl": "/notes/146884"}, {"RefNumber": "146866", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Maintenance screen of route stages", "RefUrl": "/notes/146866"}, {"RefNumber": "146847", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: Retirement of negative asseet / group asset", "RefUrl": "/notes/146847"}, {"RefNumber": "146666", "RefComponent": "IS-OIL-DS", "RefTitle": "Batch-input for QUAN/CURR/DEC oil fields BVBAPKOM", "RefUrl": "/notes/146666"}, {"RefNumber": "146563", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "ERS: Error for return deliveries", "RefUrl": "/notes/146563"}, {"RefNumber": "146381", "RefComponent": "CA-JVA", "RefTitle": "G4017 on clearing down payment with residual items", "RefUrl": "/notes/146381"}, {"RefNumber": "146325", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Short dump in ERS", "RefUrl": "/notes/146325"}, {"RefNumber": "146170", "RefComponent": "CA-JVA", "RefTitle": "JOA GOTO does not save PCO & other screens info", "RefUrl": "/notes/146170"}, {"RefNumber": "146147", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MAP-Customer price list does not work for item lev.", "RefUrl": "/notes/146147"}, {"RefNumber": "146144", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: AM Group totals report sorted by venture", "RefUrl": "/notes/146144"}, {"RefNumber": "145837", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: LIDADDON not visible in Transaction O4PJ", "RefUrl": "/notes/145837"}, {"RefNumber": "145812", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/145812"}, {"RefNumber": "145730", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Missing CI_includes in TD-Master Data", "RefUrl": "/notes/145730"}, {"RefNumber": "145661", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Cumulative quantity discount w/ pricing type <> B,C", "RefUrl": "/notes/145661"}, {"RefNumber": "145475", "RefComponent": "CA-JVA", "RefTitle": "JV Tax report: Base amount with incorrect sign", "RefUrl": "/notes/145475"}, {"RefNumber": "145215", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD OIGI_CREATE_SHIPMENT_RFC Dates incorrect", "RefUrl": "/notes/145215"}, {"RefNumber": "145164", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Field OIA_SPLTIV is not an input field (ALE problem)", "RefUrl": "/notes/145164"}, {"RefNumber": "145083", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "MCO- Product Restriction in contract", "RefUrl": "/notes/145083"}, {"RefNumber": "145074", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "ERS exclude function doesn't work", "RefUrl": "/notes/145074"}, {"RefNumber": "144872", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: GJT4 incorrect in add. local currencies", "RefUrl": "/notes/144872"}, {"RefNumber": "144838", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Overflows in SD using formula average pricing cond.", "RefUrl": "/notes/144838"}, {"RefNumber": "144770", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD-Document overview number determination (range)", "RefUrl": "/notes/144770"}, {"RefNumber": "144655", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "IS-OIL Values for auth. objects of F&A", "RefUrl": "/notes/144655"}, {"RefNumber": "144546", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Collective note for Terminal Automation Interface", "RefUrl": "/notes/144546"}, {"RefNumber": "144333", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/144333"}, {"RefNumber": "144262", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "IS-OIL Avoid error message VL606", "RefUrl": "/notes/144262"}, {"RefNumber": "144228", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees calculated for goods receipt w/ zero quantity", "RefUrl": "/notes/144228"}, {"RefNumber": "143710", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Screen Flow control errors in chg/displ Exchange agreement", "RefUrl": "/notes/143710"}, {"RefNumber": "143633", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/143633"}, {"RefNumber": "143616", "RefComponent": "CA-JVA", "RefTitle": "No profit center on MM rounding line", "RefUrl": "/notes/143616"}, {"RefNumber": "143536", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: Cutback on assets - <PERSON><PERSON> input for screen", "RefUrl": "/notes/143536"}, {"RefNumber": "143467", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Excise duty not updated when handling type is changed", "RefUrl": "/notes/143467"}, {"RefNumber": "143407", "RefComponent": "CA-JVA", "RefTitle": "Downpayment clearing: wrong partial clearing", "RefUrl": "/notes/143407"}, {"RefNumber": "143233", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD missing gain; add tracking; ASTM +/- qty; no OIGSM", "RefUrl": "/notes/143233"}, {"RefNumber": "143218", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Performance probl. when using exd. licences in sales orders", "RefUrl": "/notes/143218"}, {"RefNumber": "143180", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: RGJVRXDT problems with 'all currencies'", "RefUrl": "/notes/143180"}, {"RefNumber": "143049", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect Fee display from pricing conditions", "RefUrl": "/notes/143049"}, {"RefNumber": "143023", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect FI-SL quantities for invoices w. 0 amount", "RefUrl": "/notes/143023"}, {"RefNumber": "142995", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "GR/IR posting key is changed from last vendor line", "RefUrl": "/notes/142995"}, {"RefNumber": "142937", "RefComponent": "CA-JVA", "RefTitle": "G4028 on clearing", "RefUrl": "/notes/142937"}, {"RefNumber": "142931", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong amounts in movement-based netting", "RefUrl": "/notes/142931"}, {"RefNumber": "142846", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MAP-Shipping condition and customer price list", "RefUrl": "/notes/142846"}, {"RefNumber": "142838", "RefComponent": "CA-JVA", "RefTitle": "ML81: Incorrect RI on multiple account assignments", "RefUrl": "/notes/142838"}, {"RefNumber": "142658", "RefComponent": "CA-JVA", "RefTitle": "Venture info does not match the cost object im MM", "RefUrl": "/notes/142658"}, {"RefNumber": "142653", "RefComponent": "CA-JVA", "RefTitle": "Price Differences on the IR got corporate", "RefUrl": "/notes/142653"}, {"RefNumber": "142565", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: AM and FI not in balance after cutback", "RefUrl": "/notes/142565"}, {"RefNumber": "142530", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Changing External Details after Goods Issue Posting", "RefUrl": "/notes/142530"}, {"RefNumber": "142328", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: Goods issue - incorrect Venture derivation", "RefUrl": "/notes/142328"}, {"RefNumber": "142302", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Scroll buttons do not work on 1st level formula analysis?", "RefUrl": "/notes/142302"}, {"RefNumber": "142277", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: GJTx transactions - Billable / Nonbillable", "RefUrl": "/notes/142277"}, {"RefNumber": "142180", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Missing fields for LID release/revise Sel. screen", "RefUrl": "/notes/142180"}, {"RefNumber": "141475", "RefComponent": "CA-JVA", "RefTitle": "Partner can not be suspended if it is a CI partner", "RefUrl": "/notes/141475"}, {"RefNumber": "141454", "RefComponent": "CA-JVA", "RefTitle": "Settlement of AuC to FA creates link to unrelated JV doc", "RefUrl": "/notes/141454"}, {"RefNumber": "141291", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Invoicing fails with error message M2803", "RefUrl": "/notes/141291"}, {"RefNumber": "141146", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: GJT4 proceeds multiply with no. os assets", "RefUrl": "/notes/141146"}, {"RefNumber": "140812", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: Non Open Item Curr. Valuation add. Select.", "RefUrl": "/notes/140812"}, {"RefNumber": "140421", "RefComponent": "CA-JVA", "RefTitle": "JV EDI outbound: <PERSON><PERSON><PERSON> E - Pro<PERSON><PERSON>", "RefUrl": "/notes/140421"}, {"RefNumber": "140003", "RefComponent": "CA-JVA", "RefTitle": "JV info missing on networks after CJ2D", "RefUrl": "/notes/140003"}, {"RefNumber": "139632", "RefComponent": "CA-JVA", "RefTitle": "G4039 in activity input planning", "RefUrl": "/notes/139632"}, {"RefNumber": "139412", "RefComponent": "CA-JVA", "RefTitle": "KSII: Problem in activity price calculation.", "RefUrl": "/notes/139412"}, {"RefNumber": "139307", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: GJTx AM/MM Transfers Error RW022", "RefUrl": "/notes/139307"}, {"RefNumber": "138082", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: RGJVUXDT does not post 3rd local currency", "RefUrl": "/notes/138082"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "133605", "RefComponent": "CA-JVA", "RefTitle": "JV suspense processing: Performance improvement", "RefUrl": "/notes/133605"}, {"RefNumber": "130895", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: GJT3 GJT4 special treatment for retirements", "RefUrl": "/notes/130895"}, {"RefNumber": "129492", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: GJT0 Reversal AM/MM transfers w/group asset", "RefUrl": "/notes/129492"}, {"RefNumber": "129417", "RefComponent": "CA-JVA", "RefTitle": "JV Tax report: Incorrect calculation", "RefUrl": "/notes/129417"}, {"RefNumber": "128813", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: Goods movements if not using CRP: G5001", "RefUrl": "/notes/128813"}, {"RefNumber": "128789", "RefComponent": "CA-JVA", "RefTitle": "JV Tax reporting: Documents are missing", "RefUrl": "/notes/128789"}, {"RefNumber": "128548", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: Error AA001 in transaction OASV", "RefUrl": "/notes/128548"}, {"RefNumber": "124183", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "No update of fee/base prod. data in MM contr/orders", "RefUrl": "/notes/124183"}, {"RefNumber": "110448", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Performance of Reports ROIAMMA3 and ROIAMMAT", "RefUrl": "/notes/110448"}, {"RefNumber": "106590", "RefComponent": "CA-JVA", "RefTitle": "Cash Call 'Hold and Exit' post wrong op as partner", "RefUrl": "/notes/106590"}, {"RefNumber": "101921", "RefComponent": "CA-JVA", "RefTitle": "Error message G4017 in clearing documents", "RefUrl": "/notes/101921"}, {"RefNumber": "100642", "RefComponent": "CA-JVA", "RefTitle": "Archiving of Joint Venture Data in 2.0D (3.1H)", "RefUrl": "/notes/100642"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "53902", "RefComponent": "BC-UPG-OCS", "RefTitle": "Conflicts between Support Packages and add-ons", "RefUrl": "/notes/53902 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "332654", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL / IS-MINE / IS-CWM: Overview of SAP Notes", "RefUrl": "/notes/332654 "}, {"RefNumber": "53136", "RefComponent": "IS-OIL-BC", "RefTitle": "Support Packages and IS-Oil / IS-MINE / IS-CWM - information", "RefUrl": "/notes/53136 "}, {"RefNumber": "77407", "RefComponent": "IS-OIL-BC", "RefTitle": "CRTs for IS-Oil", "RefUrl": "/notes/77407 "}, {"RefNumber": "164301", "RefComponent": "IS-OIL-DS", "RefTitle": "Information on new functions in Service Pack 3", "RefUrl": "/notes/164301 "}, {"RefNumber": "150071", "RefComponent": "IS-OIL-DS", "RefTitle": "Application test in IS-Oil 3.1H with HP/LCP 39", "RefUrl": "/notes/150071 "}, {"RefNumber": "67261", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Reports to analyze and correct stock qties in IS-OIL systems", "RefUrl": "/notes/67261 "}, {"RefNumber": "155602", "RefComponent": "CA-JVA", "RefTitle": "Derive Profit Center from the valuation table T8JD", "RefUrl": "/notes/155602 "}, {"RefNumber": "148321", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Gain for 2-step transf.; passing dates; LOV ; Delivery Date", "RefUrl": "/notes/148321 "}, {"RefNumber": "156237", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD 'Delete event' in maintain completed shipment", "RefUrl": "/notes/156237 "}, {"RefNumber": "152213", "RefComponent": "CA-JVA", "RefTitle": "Error G4016 or G4017 when clearing IDOC RSEINB00", "RefUrl": "/notes/152213 "}, {"RefNumber": "101921", "RefComponent": "CA-JVA", "RefTitle": "Error message G4017 in clearing documents", "RefUrl": "/notes/101921 "}, {"RefNumber": "159385", "RefComponent": "CA-JVA", "RefTitle": "Function groups RSSG and GUSL in IS-OIL and BIW", "RefUrl": "/notes/159385 "}, {"RefNumber": "100642", "RefComponent": "CA-JVA", "RefTitle": "Archiving of Joint Venture Data in 2.0D (3.1H)", "RefUrl": "/notes/100642 "}, {"RefNumber": "146170", "RefComponent": "CA-JVA", "RefTitle": "JOA GOTO does not save PCO & other screens info", "RefUrl": "/notes/146170 "}, {"RefNumber": "149290", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Incompletion log not cleared for TAS relevancy on doc.header", "RefUrl": "/notes/149290 "}, {"RefNumber": "148573", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "IDOCs not being created for shpts in Shipment Create", "RefUrl": "/notes/148573 "}, {"RefNumber": "193229", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Use cust.exit on KNA1 change pointer for updates to SCP OTWS", "RefUrl": "/notes/193229 "}, {"RefNumber": "157693", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Type conflict in CALL FUNCTION OIK_TO_ISO_MEASURE_UNIT_COD", "RefUrl": "/notes/157693 "}, {"RefNumber": "160868", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "New pricing in order in change mode is incorr. for > 1 item", "RefUrl": "/notes/160868 "}, {"RefNumber": "158298", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorr. Price in PO created wrt contract/hd discount scales", "RefUrl": "/notes/158298 "}, {"RefNumber": "152478", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR21-no FI doc. but the accounts are changed/CSS 198625/1999", "RefUrl": "/notes/152478 "}, {"RefNumber": "154841", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD temperature UoM in popup/multiple MM docs per transaction", "RefUrl": "/notes/154841 "}, {"RefNumber": "143218", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Performance probl. when using exd. licences in sales orders", "RefUrl": "/notes/143218 "}, {"RefNumber": "94442", "RefComponent": "IS-OIL", "RefTitle": "IS-Oil Technical Documentation (1.0D/2.0D)", "RefUrl": "/notes/94442 "}, {"RefNumber": "154392", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-Oil: TDP Handling Type not copied at PO creation", "RefUrl": "/notes/154392 "}, {"RefNumber": "181099", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Collective changes to shipment inbound 40B", "RefUrl": "/notes/181099 "}, {"RefNumber": "181061", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Inconsistent data and status update in TPI interfa", "RefUrl": "/notes/181061 "}, {"RefNumber": "181008", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error in Shipment Create abends OILSHI01 processing", "RefUrl": "/notes/181008 "}, {"RefNumber": "180696", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Justification of storage object field", "RefUrl": "/notes/180696 "}, {"RefNumber": "180418", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Collective changes distributed after Hot Pack nov 99", "RefUrl": "/notes/180418 "}, {"RefNumber": "179319", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Wrong data in shipment tpi screens", "RefUrl": "/notes/179319 "}, {"RefNumber": "179143", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Wrong tank check on order entry", "RefUrl": "/notes/179143 "}, {"RefNumber": "177563", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Save tank for delivery on shipment inbound", "RefUrl": "/notes/177563 "}, {"RefNumber": "177556", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "change communication structure for delete IDoc processing", "RefUrl": "/notes/177556 "}, {"RefNumber": "176218", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Handling type overwritten by incoming shipment IDoc", "RefUrl": "/notes/176218 "}, {"RefNumber": "176214", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "SOE GRP from IDOC OILSHI01 does not override default values", "RefUrl": "/notes/176214 "}, {"RefNumber": "175917", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Wrong quantity used for ASTM range check", "RefUrl": "/notes/175917 "}, {"RefNumber": "175840", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Segments E1OILSI E1OILVH too short", "RefUrl": "/notes/175840 "}, {"RefNumber": "175777", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Changes to order distribution", "RefUrl": "/notes/175777 "}, {"RefNumber": "175646", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Wrong quantity used for ASTM range check", "RefUrl": "/notes/175646 "}, {"RefNumber": "174710", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "shipment inbound, change plant", "RefUrl": "/notes/174710 "}, {"RefNumber": "174462", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "TPI: Filter objects not possible", "RefUrl": "/notes/174462 "}, {"RefNumber": "200706", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "GUI status OIIPBLCT missing", "RefUrl": "/notes/200706 "}, {"RefNumber": "192014", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Error OC709 occurs on TPI OTWS entry", "RefUrl": "/notes/192014 "}, {"RefNumber": "191432", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Tank ID assignment at order change incorrect", "RefUrl": "/notes/191432 "}, {"RefNumber": "177825", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Order distribution action code", "RefUrl": "/notes/177825 "}, {"RefNumber": "180759", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "OILTPI01 Missing E1OILPS segment", "RefUrl": "/notes/180759 "}, {"RefNumber": "191401", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Action code in OILTPI50 populated incorrectly", "RefUrl": "/notes/191401 "}, {"RefNumber": "139307", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: GJTx AM/MM Transfers Error RW022", "RefUrl": "/notes/139307 "}, {"RefNumber": "150132", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: credit management: value update problem", "RefUrl": "/notes/150132 "}, {"RefNumber": "146147", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MAP-Customer price list does not work for item lev.", "RefUrl": "/notes/146147 "}, {"RefNumber": "145837", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: LIDADDON not visible in Transaction O4PJ", "RefUrl": "/notes/145837 "}, {"RefNumber": "151452", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Repricing and Redetermination of F&A conditions 31H", "RefUrl": "/notes/151452 "}, {"RefNumber": "156599", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Document item quantity assign. missing O9540", "RefUrl": "/notes/156599 "}, {"RefNumber": "180082", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Applying LCPs 53-61 incl. on R/3 3.1H", "RefUrl": "/notes/180082 "}, {"RefNumber": "143233", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD missing gain; add tracking; ASTM +/- qty; no OIGSM", "RefUrl": "/notes/143233 "}, {"RefNumber": "310788", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No provision to extend Idoc OILDVA01 Driver/Veh", "RefUrl": "/notes/310788 "}, {"RefNumber": "146847", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: Retirement of negative asseet / group asset", "RefUrl": "/notes/146847 "}, {"RefNumber": "129492", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: GJT0 Reversal AM/MM transfers w/group asset", "RefUrl": "/notes/129492 "}, {"RefNumber": "308868", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Tank ID error on SD order items after hot package", "RefUrl": "/notes/308868 "}, {"RefNumber": "306559", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Inconsistent date used in order item create", "RefUrl": "/notes/306559 "}, {"RefNumber": "306761", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No warning for multiple Driver Vehicle Assignments", "RefUrl": "/notes/306761 "}, {"RefNumber": "153945", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: GJTx Transactions: Values in depr.areas", "RefUrl": "/notes/153945 "}, {"RefNumber": "151815", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: Document display CRP pricing incorrect", "RefUrl": "/notes/151815 "}, {"RefNumber": "151523", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: Upgrade to Rel. 4.0B: RGJT156C ends w/error", "RefUrl": "/notes/151523 "}, {"RefNumber": "151027", "RefComponent": "CA-JVA", "RefTitle": "cash call incorrectly calculates baseline date", "RefUrl": "/notes/151027 "}, {"RefNumber": "128548", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: Error AA001 in transaction OASV", "RefUrl": "/notes/128548 "}, {"RefNumber": "128813", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: Goods movements if not using CRP: G5001", "RefUrl": "/notes/128813 "}, {"RefNumber": "130895", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: GJT3 GJT4 special treatment for retirements", "RefUrl": "/notes/130895 "}, {"RefNumber": "138082", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: RGJVUXDT does not post 3rd local currency", "RefUrl": "/notes/138082 "}, {"RefNumber": "140812", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: Non Open Item Curr. Valuation add. Select.", "RefUrl": "/notes/140812 "}, {"RefNumber": "141146", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: GJT4 proceeds multiply with no. os assets", "RefUrl": "/notes/141146 "}, {"RefNumber": "141454", "RefComponent": "CA-JVA", "RefTitle": "Settlement of AuC to FA creates link to unrelated JV doc", "RefUrl": "/notes/141454 "}, {"RefNumber": "141475", "RefComponent": "CA-JVA", "RefTitle": "Partner can not be suspended if it is a CI partner", "RefUrl": "/notes/141475 "}, {"RefNumber": "142277", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: GJTx transactions - Billable / Nonbillable", "RefUrl": "/notes/142277 "}, {"RefNumber": "142328", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: Goods issue - incorrect Venture derivation", "RefUrl": "/notes/142328 "}, {"RefNumber": "144872", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: GJT4 incorrect in add. local currencies", "RefUrl": "/notes/144872 "}, {"RefNumber": "146144", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: AM Group totals report sorted by venture", "RefUrl": "/notes/146144 "}, {"RefNumber": "148424", "RefComponent": "CA-JVA", "RefTitle": "Line item report: no sort on cost object column", "RefUrl": "/notes/148424 "}, {"RefNumber": "148944", "RefComponent": "CA-JVA", "RefTitle": "non oper cash call uses wrong currency for FB01 posting", "RefUrl": "/notes/148944 "}, {"RefNumber": "149395", "RefComponent": "CA-JVA", "RefTitle": "Allow CI/NPI to run using transaction SE38", "RefUrl": "/notes/149395 "}, {"RefNumber": "106590", "RefComponent": "CA-JVA", "RefTitle": "Cash Call 'Hold and Exit' post wrong op as partner", "RefUrl": "/notes/106590 "}, {"RefNumber": "154630", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: JV/AM Transfers: incorr. depreciation start", "RefUrl": "/notes/154630 "}, {"RefNumber": "301021", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Inconsistent date used in order item create", "RefUrl": "/notes/301021 "}, {"RefNumber": "301385", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Population of ship-to in TAS shipment IDoc", "RefUrl": "/notes/301385 "}, {"RefNumber": "303220", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Tank ID defaulting in orders incorrect on change", "RefUrl": "/notes/303220 "}, {"RefNumber": "140003", "RefComponent": "CA-JVA", "RefTitle": "JV info missing on networks after CJ2D", "RefUrl": "/notes/140003 "}, {"RefNumber": "301014", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Incorrect TPP check in IDOC_OUTPUT_OILTPI50", "RefUrl": "/notes/301014 "}, {"RefNumber": "198627", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Incorrect shipping point in O4PO", "RefUrl": "/notes/198627 "}, {"RefNumber": "300109", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No warning for multiple Driver Vehicle Assignments", "RefUrl": "/notes/300109 "}, {"RefNumber": "216067", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Additional customer exits", "RefUrl": "/notes/216067 "}, {"RefNumber": "216249", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Status messages not returned to TPI", "RefUrl": "/notes/216249 "}, {"RefNumber": "157781", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Incorrect valuation after KINAK = 'K'", "RefUrl": "/notes/157781 "}, {"RefNumber": "208587", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No warning for multiple Driver Vehicle Assignments", "RefUrl": "/notes/208587 "}, {"RefNumber": "155914", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Printing exchange statements using RSNAST00", "RefUrl": "/notes/155914 "}, {"RefNumber": "201913", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Function call conflict in OIK_TPI_ORDERS_CREATE", "RefUrl": "/notes/201913 "}, {"RefNumber": "199541", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Endless loop in IDOC_OUTPUT_OILTPI50", "RefUrl": "/notes/199541 "}, {"RefNumber": "200316", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error in incompletion for mandatory Tank ID", "RefUrl": "/notes/200316 "}, {"RefNumber": "197182", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Quantity missing from OILSHI01 if CPLID not '1'", "RefUrl": "/notes/197182 "}, {"RefNumber": "143467", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Excise duty not updated when handling type is changed", "RefUrl": "/notes/143467 "}, {"RefNumber": "148407", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Condition not quant. adjusted in G/R but in IV", "RefUrl": "/notes/148407 "}, {"RefNumber": "150982", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "ED difference posting for differential invoice", "RefUrl": "/notes/150982 "}, {"RefNumber": "152728", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Bug in routine XKOMV_BEWERTEN", "RefUrl": "/notes/152728 "}, {"RefNumber": "153269", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Cumulative cond. does not function in Credit Memo", "RefUrl": "/notes/153269 "}, {"RefNumber": "154673", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Errors in repricing in invoice verification", "RefUrl": "/notes/154673 "}, {"RefNumber": "157645", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Wrong handling of group cond. in invoice verif.", "RefUrl": "/notes/157645 "}, {"RefNumber": "193023", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Handling of ETAs for planned shipments", "RefUrl": "/notes/193023 "}, {"RefNumber": "196052", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error OC605 occurs incorrectly", "RefUrl": "/notes/196052 "}, {"RefNumber": "194327", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Incorrect OIK37 updates via TPI", "RefUrl": "/notes/194327 "}, {"RefNumber": "195707", "RefComponent": "IS-OIL-DS", "RefTitle": "Slow response to delivery create", "RefUrl": "/notes/195707 "}, {"RefNumber": "193231", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "ROIKFCL1 functionality missing after Service Pack", "RefUrl": "/notes/193231 "}, {"RefNumber": "193227", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Abort A222 in OIK_TAS_DATA_POST", "RefUrl": "/notes/193227 "}, {"RefNumber": "192273", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Collected changes for TPI", "RefUrl": "/notes/192273 "}, {"RefNumber": "191696", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "OILSHI01 IDoc keeps status 64 after processing", "RefUrl": "/notes/191696 "}, {"RefNumber": "192046", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No KNA1 change pointer for updates to SCP OTWS", "RefUrl": "/notes/192046 "}, {"RefNumber": "191151", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Missing TPI functionality", "RefUrl": "/notes/191151 "}, {"RefNumber": "191429", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "PBL OTWS entries missing from TPI Location IDoc", "RefUrl": "/notes/191429 "}, {"RefNumber": "191154", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "TPI IDocs fail with mixed user defaults", "RefUrl": "/notes/191154 "}, {"RefNumber": "191149", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Abort A222 in OIK_TAS_DATA_POST", "RefUrl": "/notes/191149 "}, {"RefNumber": "147813", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "ME31: external details not copied", "RefUrl": "/notes/147813 "}, {"RefNumber": "186151", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Shipment inbound process update task", "RefUrl": "/notes/186151 "}, {"RefNumber": "185620", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Incorrect UOM conversion in ROIKPALE", "RefUrl": "/notes/185620 "}, {"RefNumber": "181435", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Override of storage SOE failes on shipment inbound", "RefUrl": "/notes/181435 "}, {"RefNumber": "185653", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "OILTPI01 Missing E1OILPS segment II (31H)", "RefUrl": "/notes/185653 "}, {"RefNumber": "185617", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error OC709 raised on change of OTWS entry", "RefUrl": "/notes/185617 "}, {"RefNumber": "181239", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Tank ID not stored in OIK37 for Oil TPI", "RefUrl": "/notes/181239 "}, {"RefNumber": "185453", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "OIGD Customer Area data cannot be saved on change", "RefUrl": "/notes/185453 "}, {"RefNumber": "178423", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Changes delivered with CRT", "RefUrl": "/notes/178423 "}, {"RefNumber": "182178", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Shipment Load ID assigned with Valid-from > Valid-to", "RefUrl": "/notes/182178 "}, {"RefNumber": "142838", "RefComponent": "CA-JVA", "RefTitle": "ML81: Incorrect RI on multiple account assignments", "RefUrl": "/notes/142838 "}, {"RefNumber": "182859", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Override of storage SOE failes on shipment inbound", "RefUrl": "/notes/182859 "}, {"RefNumber": "153327", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: 3.1H/1.0D  TDP check reports (ED Inventory)", "RefUrl": "/notes/153327 "}, {"RefNumber": "180401", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Collective changes to shipment inbound 31H", "RefUrl": "/notes/180401 "}, {"RefNumber": "179151", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Delivery creation uses system date", "RefUrl": "/notes/179151 "}, {"RefNumber": "178164", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Screen 200 missing in program", "RefUrl": "/notes/178164 "}, {"RefNumber": "150752", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Error in posting exch. goods receipt reversal", "RefUrl": "/notes/150752 "}, {"RefNumber": "153222", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "external details at stock transfer order", "RefUrl": "/notes/153222 "}, {"RefNumber": "147804", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Incompletion procedure doesn't recognize OIC_MOT", "RefUrl": "/notes/147804 "}, {"RefNumber": "142937", "RefComponent": "CA-JVA", "RefTitle": "G4028 on clearing", "RefUrl": "/notes/142937 "}, {"RefNumber": "171399", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "TPI: Segment E1OILTW Error in OILTPI01", "RefUrl": "/notes/171399 "}, {"RefNumber": "169228", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Error during DELETE OIKLIDR", "RefUrl": "/notes/169228 "}, {"RefNumber": "170583", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Incompletion message for deleted contract/ order item", "RefUrl": "/notes/170583 "}, {"RefNumber": "154498", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Movement based netting: VAT amount appears double", "RefUrl": "/notes/154498 "}, {"RefNumber": "150831", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Movement based netting: VAT on MM side missing (II)", "RefUrl": "/notes/150831 "}, {"RefNumber": "146906", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Movement based netting: VAT on MM side missing", "RefUrl": "/notes/146906 "}, {"RefNumber": "156700", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong UoM in automatically created purchase order", "RefUrl": "/notes/156700 "}, {"RefNumber": "169160", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Corrections in SVPs: 3.1H/SVP3 4.0B/SVP1", "RefUrl": "/notes/169160 "}, {"RefNumber": "159169", "RefComponent": "CA-JVA", "RefTitle": "JV GR/IR links does not handle reverse entries", "RefUrl": "/notes/159169 "}, {"RefNumber": "157920", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "VKDFS table is not filled properly during invoice generation", "RefUrl": "/notes/157920 "}, {"RefNumber": "158378", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MAP-Deletion of OICQ* entries for cancelled invoice", "RefUrl": "/notes/158378 "}, {"RefNumber": "162652", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Missing UserExits in Function IDOC_INPUT_OILSH1", "RefUrl": "/notes/162652 "}, {"RefNumber": "147407", "RefComponent": "MM-PUR-REQ", "RefTitle": "ME57 PReqs are deleted through changes in dates", "RefUrl": "/notes/147407 "}, {"RefNumber": "161514", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Automatic stage assignment", "RefUrl": "/notes/161514 "}, {"RefNumber": "163470", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "MCOE Performance optimization for KNVV access", "RefUrl": "/notes/163470 "}, {"RefNumber": "147057", "RefComponent": "CA-JVA", "RefTitle": "G4039 in CO/JV plan integration", "RefUrl": "/notes/147057 "}, {"RefNumber": "139412", "RefComponent": "CA-JVA", "RefTitle": "KSII: Problem in activity price calculation.", "RefUrl": "/notes/139412 "}, {"RefNumber": "149650", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Only prior to load; loading needed for 2-step transfers", "RefUrl": "/notes/149650 "}, {"RefNumber": "154025", "RefComponent": "CA-JVA", "RefTitle": "Error G4024 in clearing", "RefUrl": "/notes/154025 "}, {"RefNumber": "161917", "RefComponent": "CA-JVA", "RefTitle": "JV configuration: Check for corporate cost center", "RefUrl": "/notes/161917 "}, {"RefNumber": "161533", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Quantity Schedule does not capture returns", "RefUrl": "/notes/161533 "}, {"RefNumber": "143023", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect FI-SL quantities for invoices w. 0 amount", "RefUrl": "/notes/143023 "}, {"RefNumber": "160513", "RefComponent": "CA-JVA", "RefTitle": "JV billing: Incorrect document numbers selected", "RefUrl": "/notes/160513 "}, {"RefNumber": "156455", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: PO create going through an extra screen.", "RefUrl": "/notes/156455 "}, {"RefNumber": "152651", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MM contract: wrong base product stored", "RefUrl": "/notes/152651 "}, {"RefNumber": "156697", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "If taxes are 0,accounting error in differential Inv", "RefUrl": "/notes/156697 "}, {"RefNumber": "150318", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Split excise duty may cause wrong IR postings", "RefUrl": "/notes/150318 "}, {"RefNumber": "154470", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Duplicate Pricing conditions being displayed", "RefUrl": "/notes/154470 "}, {"RefNumber": "159390", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Pricing UoM not copied from info record to contract", "RefUrl": "/notes/159390 "}, {"RefNumber": "160123", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Field OIA_SPLTIV is not an input field (ALE problem)", "RefUrl": "/notes/160123 "}, {"RefNumber": "160234", "RefComponent": "CA-JVA", "RefTitle": "JV EDI: Invoice date not on 819 document", "RefUrl": "/notes/160234 "}, {"RefNumber": "153565", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Manual Sales office & Group entries are lost", "RefUrl": "/notes/153565 "}, {"RefNumber": "145083", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "MCO- Product Restriction in contract", "RefUrl": "/notes/145083 "}, {"RefNumber": "158896", "RefComponent": "CA-JVA", "RefTitle": "JV cutback: Program termination with CONVT_OVERFLOW", "RefUrl": "/notes/158896 "}, {"RefNumber": "159634", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Billing block not interpreted in billing due list", "RefUrl": "/notes/159634 "}, {"RefNumber": "154687", "RefComponent": "CA-JVA", "RefTitle": "G4024 / G4016 / G4017 in clearing with Downpayment", "RefUrl": "/notes/154687 "}, {"RefNumber": "143616", "RefComponent": "CA-JVA", "RefTitle": "No profit center on MM rounding line", "RefUrl": "/notes/143616 "}, {"RefNumber": "143536", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: Cutback on assets - <PERSON><PERSON> input for screen", "RefUrl": "/notes/143536 "}, {"RefNumber": "143180", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: RGJVRXDT problems with 'all currencies'", "RefUrl": "/notes/143180 "}, {"RefNumber": "142565", "RefComponent": "CA-JVA", "RefTitle": "CA-JVA: AM and FI not in balance after cutback", "RefUrl": "/notes/142565 "}, {"RefNumber": "157342", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Netting with zero value documents", "RefUrl": "/notes/157342 "}, {"RefNumber": "153623", "RefComponent": "CA-JVA", "RefTitle": "G4016  No condition class for tax", "RefUrl": "/notes/153623 "}, {"RefNumber": "158148", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Missing INCOTERMS in LID Determination User exit", "RefUrl": "/notes/158148 "}, {"RefNumber": "147775", "RefComponent": "CA-JVA", "RefTitle": "G4024  on clearing: no FI clearing line", "RefUrl": "/notes/147775 "}, {"RefNumber": "152033", "RefComponent": "CA-JVA", "RefTitle": "G4024 on intercomp. clearing of FI and MM invoices", "RefUrl": "/notes/152033 "}, {"RefNumber": "153644", "RefComponent": "CA-JVA", "RefTitle": "G4016  OI's with and without order, cost center", "RefUrl": "/notes/153644 "}, {"RefNumber": "139632", "RefComponent": "CA-JVA", "RefTitle": "G4039 in activity input planning", "RefUrl": "/notes/139632 "}, {"RefNumber": "157801", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Loss in transfer/STO and change of valuation type", "RefUrl": "/notes/157801 "}, {"RefNumber": "157798", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Missing User-exit in moigsf60 (EXIT_SAPMOIGS_710)", "RefUrl": "/notes/157798 "}, {"RefNumber": "157222", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External Details and sales support VC01", "RefUrl": "/notes/157222 "}, {"RefNumber": "151361", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Missing exg. number on billing index for diff. inv.", "RefUrl": "/notes/151361 "}, {"RefNumber": "157494", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Location ID in Credit note and Credit note request", "RefUrl": "/notes/157494 "}, {"RefNumber": "156993", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD User Exit 007 before creation of MM document", "RefUrl": "/notes/156993 "}, {"RefNumber": "157044", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Exit from step-loop 00043 in scheduling", "RefUrl": "/notes/157044 "}, {"RefNumber": "156906", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Abort on Location Archiving Deletion program", "RefUrl": "/notes/156906 "}, {"RefNumber": "156493", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Event in cond. processing set incor. in inv. verif.", "RefUrl": "/notes/156493 "}, {"RefNumber": "152678", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Cost sharing on document item level", "RefUrl": "/notes/152678 "}, {"RefNumber": "154539", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Create only BTCI records if dmbtr ne 0", "RefUrl": "/notes/154539 "}, {"RefNumber": "154537", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Doubling of formula value after fee processing", "RefUrl": "/notes/154537 "}, {"RefNumber": "154181", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Document flow for shipment too slow", "RefUrl": "/notes/154181 "}, {"RefNumber": "151018", "RefComponent": "CA-JVA", "RefTitle": "JV EDI billing: New functionality", "RefUrl": "/notes/151018 "}, {"RefNumber": "154790", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Allow non-movement delivery items in TD shipments", "RefUrl": "/notes/154790 "}, {"RefNumber": "155176", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Confirmed quantity zero in call off (mult. ship-to)", "RefUrl": "/notes/155176 "}, {"RefNumber": "148411", "RefComponent": "CA-JVA", "RefTitle": "JV billing extract: Warning GJ837 contains no info", "RefUrl": "/notes/148411 "}, {"RefNumber": "154694", "RefComponent": "CA-JVA", "RefTitle": "Check for equity type deletion incorrect", "RefUrl": "/notes/154694 "}, {"RefNumber": "153536", "RefComponent": "CA-JVA", "RefTitle": "JV billing extract: Performance for large ventures", "RefUrl": "/notes/153536 "}, {"RefNumber": "154502", "RefComponent": "IS-OIL-DS", "RefTitle": "Oil: Several performance improvements", "RefUrl": "/notes/154502 "}, {"RefNumber": "154243", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Collective Note for IS-Oil/TAS", "RefUrl": "/notes/154243 "}, {"RefNumber": "150951", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Missing overload check in compartment allocation details", "RefUrl": "/notes/150951 "}, {"RefNumber": "154042", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Automatic LID determination after document change", "RefUrl": "/notes/154042 "}, {"RefNumber": "153768", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "BWTAR for reservation not used for LID-assignment", "RefUrl": "/notes/153768 "}, {"RefNumber": "154019", "RefComponent": "CA-JVA", "RefTitle": "JV equity change: Entry in process history after test run", "RefUrl": "/notes/154019 "}, {"RefNumber": "149657", "RefComponent": "CA-JVA", "RefTitle": "Cost Center section appears twice in non-op form", "RefUrl": "/notes/149657 "}, {"RefNumber": "153121", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Scrolling SAPMOIGS3700; take over dates after user exit", "RefUrl": "/notes/153121 "}, {"RefNumber": "152285", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong fee proposal at IV for delivery note selection", "RefUrl": "/notes/152285 "}, {"RefNumber": "150686", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "For F&A pricing, tmp. costing calculates value 0 !?", "RefUrl": "/notes/150686 "}, {"RefNumber": "153125", "RefComponent": "CA-JVA", "RefTitle": "Downpayment clearing: wrong partial clearing", "RefUrl": "/notes/153125 "}, {"RefNumber": "151219", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Document currency display in purchase order history", "RefUrl": "/notes/151219 "}, {"RefNumber": "151959", "RefComponent": "CA-JVA", "RefTitle": "JV taxreport - Incorrect tax amount and baseline date", "RefUrl": "/notes/151959 "}, {"RefNumber": "149493", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Remove obsolete entries from Table OIGSM", "RefUrl": "/notes/149493 "}, {"RefNumber": "151733", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Wrong posting date of SES created for SCD item", "RefUrl": "/notes/151733 "}, {"RefNumber": "146884", "RefComponent": "CA-JVA", "RefTitle": "VBA does not pick up 2nd JV doc with line 998, 999, etc.", "RefUrl": "/notes/146884 "}, {"RefNumber": "152294", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "E1OILT1-EXTDELNR not moved to G_OIKLOAD_TAB", "RefUrl": "/notes/152294 "}, {"RefNumber": "146866", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Maintenance screen of route stages", "RefUrl": "/notes/146866 "}, {"RefNumber": "151809", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Performance Improvement oic_ditab", "RefUrl": "/notes/151809 "}, {"RefNumber": "152245", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: EXIT_SAPLOIK3_110 customer exit is unworkable", "RefUrl": "/notes/152245 "}, {"RefNumber": "152002", "RefComponent": "CA-JVA", "RefTitle": "Error message G4017 in clearing documents", "RefUrl": "/notes/152002 "}, {"RefNumber": "129417", "RefComponent": "CA-JVA", "RefTitle": "JV Tax report: Incorrect calculation", "RefUrl": "/notes/129417 "}, {"RefNumber": "145475", "RefComponent": "CA-JVA", "RefTitle": "JV Tax report: Base amount with incorrect sign", "RefUrl": "/notes/145475 "}, {"RefNumber": "128789", "RefComponent": "CA-JVA", "RefTitle": "JV Tax reporting: Documents are missing", "RefUrl": "/notes/128789 "}, {"RefNumber": "149755", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Is-Oil: VL07 inconsistencies when 2-step tracking is active", "RefUrl": "/notes/149755 "}, {"RefNumber": "149909", "RefComponent": "CA-JVA", "RefTitle": "currency exchange rate does not show the right amount", "RefUrl": "/notes/149909 "}, {"RefNumber": "151196", "RefComponent": "IS-OIL-DS", "RefTitle": "SD business views for SAP AS in IS-Oil systems", "RefUrl": "/notes/151196 "}, {"RefNumber": "146325", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Short dump in ERS", "RefUrl": "/notes/146325 "}, {"RefNumber": "141291", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Invoicing fails with error message M2803", "RefUrl": "/notes/141291 "}, {"RefNumber": "144228", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees calculated for goods receipt w/ zero quantity", "RefUrl": "/notes/144228 "}, {"RefNumber": "142931", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong amounts in movement-based netting", "RefUrl": "/notes/142931 "}, {"RefNumber": "142302", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Scroll buttons do not work on 1st level formula analysis?", "RefUrl": "/notes/142302 "}, {"RefNumber": "149774", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "IS-Oil: Error in exchange contract delivery in batch input", "RefUrl": "/notes/149774 "}, {"RefNumber": "149969", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Multiple Partners from Contract in LID-assignment", "RefUrl": "/notes/149969 "}, {"RefNumber": "150011", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Duplicate entries in Controlstructure F4-Help", "RefUrl": "/notes/150011 "}, {"RefNumber": "150207", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD OIGI_CHANGE_SHIPMENT_RFC (add document -> O9005)", "RefUrl": "/notes/150207 "}, {"RefNumber": "149542", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Excise duty licenses - Performance", "RefUrl": "/notes/149542 "}, {"RefNumber": "149487", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Revaluation of logical inventory value", "RefUrl": "/notes/149487 "}, {"RefNumber": "146666", "RefComponent": "IS-OIL-DS", "RefTitle": "Batch-input for QUAN/CURR/DEC oil fields BVBAPKOM", "RefUrl": "/notes/146666 "}, {"RefNumber": "147724", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Missing Customer-Exit for add. Segm. to IDoc OILSHL01", "RefUrl": "/notes/147724 "}, {"RefNumber": "147368", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: User exits and User screen in TAS-Interface", "RefUrl": "/notes/147368 "}, {"RefNumber": "147375", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Syntaxerror SAPMV45A after IS-Oil ServicePack 2", "RefUrl": "/notes/147375 "}, {"RefNumber": "148397", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Collective Note for IS-Oil/TAS Servicepack2", "RefUrl": "/notes/148397 "}, {"RefNumber": "147964", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "O4H1 ; Background ASTM conversion for batch Input", "RefUrl": "/notes/147964 "}, {"RefNumber": "148140", "RefComponent": "CA-JVA", "RefTitle": "JV billing: Partner out of CI status not selected", "RefUrl": "/notes/148140 "}, {"RefNumber": "147466", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Wrong value posted for TPP in LID-determination", "RefUrl": "/notes/147466 "}, {"RefNumber": "147180", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Missing SAP Enhancements for TD-Masterdata SP 2", "RefUrl": "/notes/147180 "}, {"RefNumber": "110448", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Performance of Reports ROIAMMA3 and ROIAMMAT", "RefUrl": "/notes/110448 "}, {"RefNumber": "147141", "RefComponent": "CA-JVA", "RefTitle": "JV EDI outbound: Ventures not selected", "RefUrl": "/notes/147141 "}, {"RefNumber": "147061", "RefComponent": "CA-JVA", "RefTitle": "G4029 on invoice processing: no recovery indicator", "RefUrl": "/notes/147061 "}, {"RefNumber": "146563", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "ERS: Error for return deliveries", "RefUrl": "/notes/146563 "}, {"RefNumber": "146381", "RefComponent": "CA-JVA", "RefTitle": "G4017 on clearing down payment with residual items", "RefUrl": "/notes/146381 "}, {"RefNumber": "143049", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect Fee display from pricing conditions", "RefUrl": "/notes/143049 "}, {"RefNumber": "142530", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Changing External Details after Goods Issue Posting", "RefUrl": "/notes/142530 "}, {"RefNumber": "145730", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Missing CI_includes in TD-Master Data", "RefUrl": "/notes/145730 "}, {"RefNumber": "145661", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Cumulative quantity discount w/ pricing type <> B,C", "RefUrl": "/notes/145661 "}, {"RefNumber": "145215", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD OIGI_CREATE_SHIPMENT_RFC Dates incorrect", "RefUrl": "/notes/145215 "}, {"RefNumber": "144838", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Overflows in SD using formula average pricing cond.", "RefUrl": "/notes/144838 "}, {"RefNumber": "145164", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Field OIA_SPLTIV is not an input field (ALE problem)", "RefUrl": "/notes/145164 "}, {"RefNumber": "145074", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "ERS exclude function doesn't work", "RefUrl": "/notes/145074 "}, {"RefNumber": "144770", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD-Document overview number determination (range)", "RefUrl": "/notes/144770 "}, {"RefNumber": "144546", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Collective note for Terminal Automation Interface", "RefUrl": "/notes/144546 "}, {"RefNumber": "144655", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "IS-OIL Values for auth. objects of F&A", "RefUrl": "/notes/144655 "}, {"RefNumber": "142180", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Missing fields for LID release/revise Sel. screen", "RefUrl": "/notes/142180 "}, {"RefNumber": "144262", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "IS-OIL Avoid error message VL606", "RefUrl": "/notes/144262 "}, {"RefNumber": "143710", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Screen Flow control errors in chg/displ Exchange agreement", "RefUrl": "/notes/143710 "}, {"RefNumber": "143407", "RefComponent": "CA-JVA", "RefTitle": "Downpayment clearing: wrong partial clearing", "RefUrl": "/notes/143407 "}, {"RefNumber": "124183", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "No update of fee/base prod. data in MM contr/orders", "RefUrl": "/notes/124183 "}, {"RefNumber": "142995", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "GR/IR posting key is changed from last vendor line", "RefUrl": "/notes/142995 "}, {"RefNumber": "142846", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MAP-Shipping condition and customer price list", "RefUrl": "/notes/142846 "}, {"RefNumber": "142653", "RefComponent": "CA-JVA", "RefTitle": "Price Differences on the IR got corporate", "RefUrl": "/notes/142653 "}, {"RefNumber": "142658", "RefComponent": "CA-JVA", "RefTitle": "Venture info does not match the cost object im MM", "RefUrl": "/notes/142658 "}, {"RefNumber": "140421", "RefComponent": "CA-JVA", "RefTitle": "JV EDI outbound: <PERSON><PERSON><PERSON> E - Pro<PERSON><PERSON>", "RefUrl": "/notes/140421 "}, {"RefNumber": "133605", "RefComponent": "CA-JVA", "RefTitle": "JV suspense processing: Performance improvement", "RefUrl": "/notes/133605 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "31H", "To": "31H", "Subsequent": ""}, {"SoftwareComponent": "IS-OIL", "From": "2.0D/1.0D", "To": "2.0D/1.0D", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}