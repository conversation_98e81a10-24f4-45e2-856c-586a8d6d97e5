{"Request": {"Number": "849925", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 258, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015903762017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000849925?language=E&token=00600CEDD7DF6173DEBAA7EF83405056"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000849925", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000849925/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "849925"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.06.2005"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade - general"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "849925 - Upgrade: termination in phase TABIM_UPG / SHADOW_IMPORT_UPG2"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You are upgrading to a target release with Basis 640.<br />The request SAPKA64012 (Support Package 12 for SAP_ABA) was included in the upgrade.<br />In the phase SHADOW_IMPORT_UPG2 or TABIM_UPG, a termination occurs.<br /><br />If the termination occurs in the phase SHADOW_IMPORT_UPG2, error messages of the following type are displayed in the log &lt;DIR_PUT&gt;/SHDUPGIMP2.ELG:<br /> &#x00A0;&#x00A0; 2EETW000 sap_dext called with msgnr \"2\":<BR/> &#x00A0;&#x00A0; 2EETW000&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;db call info<BR/> &#x00A0;&#x00A0; 2EETW000 function:&#x00A0;&#x00A0; db_setget<BR/> &#x00A0;&#x00A0; 2EETW000 fcode:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RT_SETB<BR/> &#x00A0;&#x00A0; 2EETW000 tabname:&#x00A0;&#x00A0;&#x00A0;&#x00A0;DSVASCSSCS<BR/> &#x00A0;&#x00A0; 2EETW000 len:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0<BR/> &#x00A0;&#x00A0; 2EETW000 key:<BR/> &#x00A0;&#x00A0; 2EETW000 retcode:&#x00A0;&#x00A0;&#x00A0;&#x00A0;2<BR/> &#x00A0;&#x00A0; 1 ETP111 exit code&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; : \"12\"<BR/> <br />If the termination occurs in the phase TABIM_UPG, error messages of the following type are displayed in the log &lt;DIR_PUT&gt;/TABIMUPG.ELG:<br /><br />&#x00A0;&#x00A0;2EETW109 table \"DSVASCSSCR\" does not exist in nametab.<br /><br />In each case, the messages refer to one of the following tables: DSVASCSSCS, DSVASCSSCR, DSVASCSSCO, DSVASCSSCD<br /><br /><br />Up until now, the error has only been observed in upgrades to a target release with Basis 640. Since the constellation is very similar, the error could also occur in upgrades to a target release with Basis 620 that include the request SAPKA62051 (Support Package 51 for SAP_ABA).<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>DSVASCSSCS, DSVASCSSCR, DSVASCSSCO, DSVASCSSCD, TABIM_UPG, SHADOW_IMPORT_UPG2</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is a delivery error.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Do NOT include the package SAPKA64012 in the upgrade.<br /><br />The tables are delivered again in the package SAPKA64013 (Support Package 13 for SAP_ABA).<br />As soon as the package SAPKA64013 is available, you can include the packages SAPKA64012 and SAPKA64013 together.<br /><br />If you are upgrading to a target release with Basis 620, do not include the package SAPKA62051 in the upgrade.<br />The tables are delivered again in the package SAPKA62053 (Support Package 53 for SAP_ABA).<br />As soon as the package SAPKA62053 is available, you can include packages SAPKA62051 to SAPKA62053 together.<br /><br /><br />However, if the error occurs frequently, contact SAP support and specify this note.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-SMG-SVD-SWB (Service Session Workbench)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D030559)"}, {"Key": "Processor                                                                                           ", "Value": "D032354"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000849925/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000849925/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000849925/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000849925/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000849925/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000849925/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000849925/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000849925/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000849925/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "788623", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade on SAP SRM Server 5.0 SR1", "RefUrl": "/notes/788623"}, {"RefNumber": "788218", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/788218"}, {"RefNumber": "781633", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Information: Upgrade to SAP BW 3.50 SR 1", "RefUrl": "/notes/781633"}, {"RefNumber": "775047", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40 SR1", "RefUrl": "/notes/775047"}, {"RefNumber": "737800", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to upgrade on SAP CRM 4.0 SR1", "RefUrl": "/notes/737800"}, {"RefNumber": "737696", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/737696"}, {"RefNumber": "705578", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade on SAP SRM Server 5.0", "RefUrl": "/notes/705578"}, {"RefNumber": "689574", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/689574"}, {"RefNumber": "684406", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/684406"}, {"RefNumber": "661640", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40", "RefUrl": "/notes/661640"}, {"RefNumber": "658992", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information for the upgrade to BW 3.50", "RefUrl": "/notes/658992"}, {"RefNumber": "642871", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info on upgrade to SAP CRM 3.1 Support Release 1", "RefUrl": "/notes/642871"}, {"RefNumber": "637683", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/637683"}, {"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852"}, {"RefNumber": "596514", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP CRM 4.0/SRM Se", "RefUrl": "/notes/596514"}, {"RefNumber": "587896", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/587896"}, {"RefNumber": "576588", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to EBP 3.5 SR 1", "RefUrl": "/notes/576588"}, {"RefNumber": "545741", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information on upgrading to BW 3.1 Content", "RefUrl": "/notes/545741"}, {"RefNumber": "503791", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information on upgrading to EBP 3.5", "RefUrl": "/notes/503791"}, {"RefNumber": "484876", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP Web AS 6.20", "RefUrl": "/notes/484876"}, {"RefNumber": "422023", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information on upgrading to BW 3.0A", "RefUrl": "/notes/422023"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "661640", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40", "RefUrl": "/notes/661640 "}, {"RefNumber": "484876", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP Web AS 6.20", "RefUrl": "/notes/484876 "}, {"RefNumber": "775047", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40 SR1", "RefUrl": "/notes/775047 "}, {"RefNumber": "737800", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to upgrade on SAP CRM 4.0 SR1", "RefUrl": "/notes/737800 "}, {"RefNumber": "705578", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade on SAP SRM Server 5.0", "RefUrl": "/notes/705578 "}, {"RefNumber": "788623", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade on SAP SRM Server 5.0 SR1", "RefUrl": "/notes/788623 "}, {"RefNumber": "642871", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info on upgrade to SAP CRM 3.1 Support Release 1", "RefUrl": "/notes/642871 "}, {"RefNumber": "781633", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Information: Upgrade to SAP BW 3.50 SR 1", "RefUrl": "/notes/781633 "}, {"RefNumber": "503791", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information on upgrading to EBP 3.5", "RefUrl": "/notes/503791 "}, {"RefNumber": "576588", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to EBP 3.5 SR 1", "RefUrl": "/notes/576588 "}, {"RefNumber": "658992", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information for the upgrade to BW 3.50", "RefUrl": "/notes/658992 "}, {"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852 "}, {"RefNumber": "596514", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP CRM 4.0/SRM Se", "RefUrl": "/notes/596514 "}, {"RefNumber": "422023", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information on upgrading to BW 3.0A", "RefUrl": "/notes/422023 "}, {"RefNumber": "545741", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information on upgrading to BW 3.1 Content", "RefUrl": "/notes/545741 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "620", "To": "640", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}