{"Request": {"Number": "5470", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 247, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014324282017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000005470?language=E&token=407C61643C825F3942F596EF2CCADCF5"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000005470", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000005470/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "5470"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 11}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.03.2011"}, "SAPComponentKey": {"_label": "Component", "value": "BC-I18"}, "SAPComponentKeyText": {"_label": "Component", "value": "Internationalization (I18N)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Internationalization (I18N)", "value": "BC-I18", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-I18*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "5470 - Syslog E14: Reorganized Buffer RSCPCCC ..."}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Keyword: Spooler/TemSe<br />The system log (syslog) contains the following:<br />E14&#x00A0;&#x00A0;Reorganized Buffer RSTSCCC ...&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;or<br />E14&#x00A0;&#x00A0;Reorganized Buffer RSCPCCC ...</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Syslog E14<br />rsts/ccc/cachesize, rsts/ccc/cache07, CCC-cache</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><OL><OL>a) A buffer reorganization was requested using transaction SP12 or SNLS/SNL2/SNL3 or I18N.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;or</OL></OL> <OL><OL>b) After the tables for the spooler settings or the tables for the definitions of the character sets were changed using transaction SPAD or SCP, a work process reads several database tables again. This also refreshes this buffer.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;or</OL></OL> <OL><OL>c) The buffer has overflowed because too many different character sets have been used.<br /></OL></OL><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>For a)<br />This is correct.<br /><br />For (b):<br />If data was changed using transaction SPAD or SCP, all output formatting programs (spool work processes) are informed and then register this buffer for reorganization. Currently, the output formatting program does not always know which spool-relevant data has changed. Therefore, the character set conversion buffer is sometimes also reorganized if other details are changed using transaction SPAD.<br />However, this is not a serious problem because configuration tables are rarely changed during production operation.<br /><br />For c)<br />If an overflow occurs, the buffer is deleted and then refilled as required. Conversions in progress continue correctly.<br />If such an overflow occurs often, it may have a negative effect on the overall performance. You can increase the buffer using the profile parameter 'rsts/ccc/cachesize'. Add 1,000,000 bytes, for example. The maximum value allowed is 16,000,000 (16 million bytes). On UNIX, you should increase the comprehensive shared memory pool by the same number of bytes using the profile parameter 'ipc/shm_psize_10'.<br />You can use transactions I18N and also SNLS, SNL1, and SP12 to observe the internal statistics of this buffer and, therefore, its occupancy. Approximately the tenth line is called \"free\" and shows how many bytes are still free in the general buffer area. The transactions have a [Refresh] button, which you can use to check whether numbers change.<br /></p> <b>Suitable cache size</b><br /> <p>The best size for this cache depends on the release and on the data actually contained in the system. It also depends on the number of different printer types and several other factors. Since more characters were defined from release to release, SAP icons were added and additional internal processes were defined, this cache continued to increase in size. The default values for each release were as follows:<br /></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Release </TH><TH ALIGN=LEFT> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Bytes</TH><TH ALIGN=LEFT> </TH></TR> <TR><TD>0.4 - 0.6</TD><TD> &#x00A0;&#x00A0;&#x00A0;&#x00A0; 30.000</TD><TD> </TD></TR> <TR><TD>1.1 - 2.2</TD><TD> &#x00A0;&#x00A0;&#x00A0;&#x00A0; 60.000</TD><TD> </TD></TR> <TR><TD>3.0 - 4.5</TD><TD> &#x00A0;&#x00A0;&#x00A0;&#x00A0;120.000</TD><TD> </TD></TR> <TR><TD>4.6 </TD><TD> &#x00A0;&#x00A0;3.000.000</TD><TD> </TD></TR> <TR><TD>5.0 - 8.0</TD><TD> &#x00A0;&#x00A0;6.000.000</TD><TD> </TD></TR> </TABLE> <p><br />The size of a subarea of this buffer can be changed using the profile parameter \"rsts/ccc/cache07\". This is a theoretical option only. In productive systems, this profile parameter should never be set. </p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D021965)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D040933)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000005470/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000005470/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000005470/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000005470/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000005470/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000005470/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000005470/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000005470/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000005470/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "99699", "RefComponent": "BC-I18", "RefTitle": "Buffer CCC too small for character set", "RefUrl": "/notes/99699"}, {"RefNumber": "83196", "RefComponent": "BC-I18", "RefTitle": "TEXTENV_CODEPAGE_ / _LANGUAGE_NOT_ALLOWED", "RefUrl": "/notes/83196"}, {"RefNumber": "775047", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40 SR1", "RefUrl": "/notes/775047"}, {"RefNumber": "737696", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/737696"}, {"RefNumber": "70965", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/70965"}, {"RefNumber": "689574", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/689574"}, {"RefNumber": "637683", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/637683"}, {"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852"}, {"RefNumber": "587896", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/587896"}, {"RefNumber": "484876", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP Web AS 6.20", "RefUrl": "/notes/484876"}, {"RefNumber": "419519", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/419519"}, {"RefNumber": "304381", "RefComponent": "EHS", "RefTitle": "Known deficiencies in EH&S 2.5B", "RefUrl": "/notes/304381"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "99699", "RefComponent": "BC-I18", "RefTitle": "Buffer CCC too small for character set", "RefUrl": "/notes/99699 "}, {"RefNumber": "484876", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP Web AS 6.20", "RefUrl": "/notes/484876 "}, {"RefNumber": "775047", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40 SR1", "RefUrl": "/notes/775047 "}, {"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852 "}, {"RefNumber": "83196", "RefComponent": "BC-I18", "RefTitle": "TEXTENV_CODEPAGE_ / _LANGUAGE_NOT_ALLOWED", "RefUrl": "/notes/83196 "}, {"RefNumber": "304381", "RefComponent": "EHS", "RefTitle": "Known deficiencies in EH&S 2.5B", "RefUrl": "/notes/304381 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}