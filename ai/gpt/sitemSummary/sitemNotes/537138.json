{"Request": {"Number": "537138", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 330, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000002609952017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000537138?language=E&token=B93598B52202387E3CDF760DB4BB355F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000537138", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000537138/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "537138"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.02.2014"}, "SAPComponentKey": {"_label": "Component", "value": "PY-XX-TL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Tools"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Payroll: General Parts", "value": "PY-XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Tools", "value": "PY-XX-TL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-XX-TL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "537138 - Deactivating RPUDEL20 and other reports in the production system"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><br/>The Reports RPUDEL20, RPUP2D00, and RPUP2D10 allow you to delete payroll results in the production system without consistency check. If these reports are used incorrectly, data losses occur.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p><br/>RPUDEL20, RPUP2D00, RPUP2D10, PU01</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><br/>Originally, the three reports mentioned above were intended for testing purposes. In addition, they may be required to remove inconsistencies in payroll results. In these cases it may be required to delete specific payroll results without considering dependent tables. Therefore, these reports do not check at all whether the deletion requires some subsequent activities or whether the deletion could even destroy payroll data completely.<br/><br/>For this reason, all three reports issue clear warning messages. In addition, their documentation contains additional information about a possible data loss.<br/><br/>However, it turned out that the reports have been used to delete payroll results in the production system despite all these warning messages. Due to lack of knowledge or because the warning messages are ignored, data losses occur regularly and can only be undone with immense time and effort, or they cannot be undone at all.<br/><br/>We would like to point out explicitly that only Transaction PU01 (&quot;Delete payroll result&quot;) is permitted for the deletion of accounting results in the production system because this is the only transaction that executes proper checks and rejects the deletion if necessary if the result has already been analyzed by subsequent programs such as the posting into accounting.<br/><br/>The three reports mentioned above are intended only for problem resolution, and you may only use them in the production system after being told so by SAP Support. That is, if you assume that there is a problem in the production system that requires the deletion of payroll results without consistency check, send a message to SAP. Depending on the problem, SAP Support will then take further steps, see below.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><br/>In the interest of data security, the use of the reports RPUDEL20, RPUP2D00, and RPUP2D10 in the production system is forbidden from now on. The correction instructions contain the necessary source code corrections. In addition, in the message class 3G, create message 502 (&quot;The report must not be used in the production system (note 537138)&quot;).<br/><br/>The reports RPUP2D00 and RPUP2D10 are only deactivated for clusters in which payroll results are stored. Data of other clusters can still be deleted in the production system.<br/><br/>If the use of the three reports in the production system seems necessary in the context of a customer message, SAP Support may also recommend the reactivation of the reports as a temporary modification. For more information, refer to SAP Note 537115.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D031305)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D028229)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000537138/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000537138/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000537138/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000537138/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000537138/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000537138/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000537138/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000537138/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000537138/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "989664", "RefComponent": "PY-XX-TL", "RefTitle": "Deleting personnel numbers in the production system", "RefUrl": "/notes/989664"}, {"RefNumber": "537115", "RefComponent": "PY-XX-TL", "RefTitle": "RPUDEL20: Modification for reactivation in production system", "RefUrl": "/notes/537115"}, {"RefNumber": "1493469", "RefComponent": "PY-XX-BS", "RefTitle": "RPUDEL20: save entries before delete", "RefUrl": "/notes/1493469"}, {"RefNumber": "1436029", "RefComponent": "PY-XX-BS", "RefTitle": "Enhancement to RPUDEL20: save entries before delete", "RefUrl": "/notes/1436029"}, {"RefNumber": "1261193", "RefComponent": "BC-SEC-AUT", "RefTitle": "Composite SAP Note : HCM Authorizations Documentation", "RefUrl": "/notes/1261193"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3358953", "RefComponent": "PY-XX-DT", "RefTitle": "Posting to accounting: General prerequisites for posting payroll results", "RefUrl": "/notes/3358953 "}, {"RefNumber": "1261193", "RefComponent": "BC-SEC-AUT", "RefTitle": "Composite SAP Note : HCM Authorizations Documentation", "RefUrl": "/notes/1261193 "}, {"RefNumber": "989664", "RefComponent": "PY-XX-TL", "RefTitle": "Deleting personnel numbers in the production system", "RefUrl": "/notes/989664 "}, {"RefNumber": "1493469", "RefComponent": "PY-XX-BS", "RefTitle": "RPUDEL20: save entries before delete", "RefUrl": "/notes/1493469 "}, {"RefNumber": "1436029", "RefComponent": "PY-XX-BS", "RefTitle": "Enhancement to RPUDEL20: save entries before delete", "RefUrl": "/notes/1436029 "}, {"RefNumber": "537115", "RefComponent": "PY-XX-TL", "RefTitle": "RPUDEL20: Modification for reactivation in production system", "RefUrl": "/notes/537115 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "31I", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 31I", "SupportPackage": "SAPKE31IA7", "URL": "/supportpackage/SAPKE31IA7"}, {"SoftwareComponentVersion": "SAP_HR 40B", "SupportPackage": "SAPKE40B98", "URL": "/supportpackage/SAPKE40B98"}, {"SoftwareComponentVersion": "SAP_HR 45B", "SupportPackage": "SAPKE45B82", "URL": "/supportpackage/SAPKE45B82"}, {"SoftwareComponentVersion": "SAP_HR 46B", "SupportPackage": "SAPKE46B64", "URL": "/supportpackage/SAPKE46B64"}, {"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46C55", "URL": "/supportpackage/SAPKE46C55"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47001", "URL": "/supportpackage/SAPKE47001"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_HR", "NumberOfCorrin": 3, "URL": "/corrins/0000537138/2"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_HR", "ValidFrom": "40B", "ValidTo": "46C", "Number": "360756 ", "URL": "/notes/360756 ", "Title": "RPUP2D00, RPUP2D10: Link to application log missing", "Component": "PY-XX-TL"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}