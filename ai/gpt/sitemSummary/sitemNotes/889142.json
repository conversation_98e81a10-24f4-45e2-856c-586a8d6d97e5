{"Request": {"Number": "889142", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 436, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005090712017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000889142?language=E&token=C4FFBC65B48048FA40A3C9FDFD009E9A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000889142", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000889142/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "889142"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "13.12.2005"}, "SAPComponentKey": {"_label": "Component", "value": "CRM-PCF"}, "SAPComponentKeyText": {"_label": "Component", "value": "obsolete: People Centric UI Framework"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Customer Relationship Management", "value": "CRM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "obsolete: People Centric UI Framework", "value": "CRM-PCF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM-PCF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "889142 - Correction CRM BSP Framework, SP06 (03)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Correction CRM BSP Framework, SP06 (03)<br />Symptom 1: The State Manager makes more than three accesses to the database in a single round-trip.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Correction CRM BSP Framework, SP06 (03)</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><b>Short Description</b><br /> <p><br />This note includes the following:</p> <UL><LI>Activates the new high-performance State Manager which reduces database accesses.</LI></UL> <p><br /><br />Perform the following manual corrections, then implement the attached correction instructions.<br /></p> <b>1. Change the value of a constant in the class CL_CRM_BSP_CONTEXT:</b><br /> <p><br />1.1. Launch SE80, press the Repository Browser button at the top-left corner, select Package from the drop-down list box, type CRM_BSP_FRAME_GENERIC in the input field and press Enter. The CRM_BSP_FRAME_GENERIC package should now be displayed in the tree at the bottom-left corner of the screen.<br /><br />1.2. Navigate to the following node in the tree: CRM_BSP_FRAME_GENERIC/Class Library/Classes/CL_CRM_BSP_CONTEXT. Double-click on the CL_CRM_BSP_CONTEXT node. The CL_CRM_BSP_CONTEXT class should now be displayed in SE80's work area screen on the right.<br />Select the Attributes tab. The attributes of class CL_CRM_BSP_CONTEXT should now be displayed in a list.<br /><br />1.3. Find the row for the GC_USE_CRM_CONTEXT attribute. The Initial value column of that row reads ' ' (with quotes). Change that to 'X' (with quotes) and press Enter.<br /><br />1.4 Press the Activate button and exit SE80.<br /><br /></p> <b>2.&#x00A0;&#x00A0;Modify a statment in the view PTCShowDialogClient.htm</b><br /> <p><br />2.1. Open BSP application CRM_BSP_F1_HELP and open folder<br />'Pages with Flow Logic' and open 'PTCShowDialogClient.htm'.<br /><br />2.2. Goto Layout tab in edit mode.<br /><br />2.3. Identify the code statement :<br />&lt;htmlb:textView design=\"HEADER2\"&gt;$PARAM1$&lt;/htmlb:textView&gt;<br /><br />2.4. Replace that statement with the following one:<br />&lt;htmlb:textView design=\"HEADER2\" wrapping=\"TRUE\"&gt;$PARAM1$&lt;/htmlb:textView&gt;<br /><br />2.5. Press the Activate button and exit SE80.<br /><br /></p> <b>3. Modify in Multi ODC Detail View ( detail_m.htm ).</b><br /> <p><br />3.1 Open transaction SE80, open BSP application CRM_BSP_FRAME.<br /><br />3.2 Open folder Views, open detail_m.htm in edit mode.<br /><br />3.3 Identify the code context block:<br /><br /> Start of context Block. ===<br /><br /> IF gt_displaytable IS NOT INITIAL and not lv_acc = 'X'.<br /> READ TABLE gt_displaytable INDEX 1 ASSIGNING &lt;ls_displaytable&gt;.<br /><br /> === End of Context block<br /><br />3.4 Identify the following code statement which comes immediately after the context block:<br /> if gv_screentype = 'LIST' AND<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;gv_toggle = 'LIST' AND<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&lt;ls_displaytable&gt;-headervisible = 'TRUE'.<br /><br />3.5 Replace the code statement mentioned in step 3.4 with the following code statement:<br /><br /> if &lt;ls_displaytable&gt;-headervisible = 'TRUE'.<br /><br />3.6 Press the Activate button and exit SE80.<br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CA-GTF-PCF (People Centric UI Framework)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023949)"}, {"Key": "Processor                                                                                           ", "Value": "I027070"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000889142/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000889142/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000889142/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000889142/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000889142/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000889142/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000889142/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000889142/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000889142/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "949447", "RefComponent": "CRM-BF", "RefTitle": "mySAP CRM 2005 SP Stack 05", "RefUrl": "/notes/949447"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "949447", "RefComponent": "CRM-BF", "RefTitle": "mySAP CRM 2005 SP Stack 05", "RefUrl": "/notes/949447 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_ABA", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_ABA 700", "SupportPackage": "SAPKA70006", "URL": "/supportpackage/SAPKA70006"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_ABA", "NumberOfCorrin": 1, "URL": "/corrins/0000889142/44"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 13, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "868404 ", "URL": "/notes/868404 ", "Title": "Correction CRM BSP Framework, SP05", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "868693 ", "URL": "/notes/868693 ", "Title": "Correction CRM BSP Framework, SP05 (1)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "871213 ", "URL": "/notes/871213 ", "Title": "Correction CRM BSP Framework, SP05 (2)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "871793 ", "URL": "/notes/871793 ", "Title": "Correction CRM BSP Framework, SP05 (3)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "872178 ", "URL": "/notes/872178 ", "Title": "Correction CRM BSP Framework, SP05 (5)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "872453 ", "URL": "/notes/872453 ", "Title": "Correction CRM BSP Framework, SP05 (6)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "873034 ", "URL": "/notes/873034 ", "Title": "Correction CRM BSP Framework, SP05 (7)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "873662 ", "URL": "/notes/873662 ", "Title": "Correction CRM BSP Framework, SP05 (8)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "875518 ", "URL": "/notes/875518 ", "Title": "Correction CRM BSP Framework, SP05 (9)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "879650 ", "URL": "/notes/879650 ", "Title": "Correction CRM BSP Framework, SP05 (10)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "881541 ", "URL": "/notes/881541 ", "Title": "Correction CRM BSP Framework, SP05 (12)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "885050 ", "URL": "/notes/885050 ", "Title": "Correction CRM BSP Framework, SP06 (01)", "Component": "CRM-PCF"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "887916 ", "URL": "/notes/887916 ", "Title": "Correction CRM BSP Framework, SP06 (2)", "Component": "CRM-PCF"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}