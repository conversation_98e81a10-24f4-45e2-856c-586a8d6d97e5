{"Request": {"Number": "153205", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 341, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000732662017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000153205?language=E&token=5144D5867A5589EF4AED8C195D43A00F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000153205", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000153205/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "153205"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "19.05.1999"}, "SAPComponentKey": {"_label": "Component", "value": "BC-BMT-WFM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Workflow"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Management", "value": "BC-BMT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-BMT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Workflow", "value": "BC-BMT-WFM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-BMT-WFM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "153205 - Direct deletion of type C work items"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You want to delete a large number of type C work items without checking, whether the related IDocs or application objects are available in the system.<br />This can be useful, for example, if application objects are generated via IDoc inbound processing (see Note 126678).</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Deleting work items, ContainerItems, RSWWWIDE</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>During IDoc inbound processing, an application object is generated from an IDoc. Before Release 4.6A, the relation between both objects is implemented via the so-called ContainerItems (type C work items). These ContainerItems stay in the system very long and it is very time-consuming to check whether the assigned IDoc or application object still exists.<br />For practical reasons, it may thus be necessary to delete the ContainerItems directly without checking the respective IDocs or application objects. To do this however, the standard report RSWWWIDE is too slow since it is applicable for all work item types and consequently carries out many checks that are not relevant for ContainerItems.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Please maintain the attached report RSWWCIDE. Then no longer delete the ContainerItems with the reports RSWWWIDE/RSWWHIDE but with this new report.<br />When selecting the ContainerItems, always enter a restriction of the work item ID in addition to date since only then an efficient search in the database is possible.<br />The selection parameters of the report have the following meaning:</p> <UL><LI>id - ID of the work item -&gt; please always restrict here !!!</LI></UL> <UL><LI>cd - creation date of the work item</LI></UL> <UL><LI>ct - creation time of the work item</LI></UL> <UL><LI>commitc - number of the ContainerItems to be deleted for each LUW</LI></UL> <UL><LI>del_log - also delete the history of the ContainerItems</LI></UL> <UL><LI>disp - only list selected ContainerItems</LI></UL> <p><br />As of Release 4.6A the links between the IDocs and the application objects generated from them are stored via a new functions and ContainerItems are no longer created.<br />If you cannot delete the ContainerItems, or you do not want to delete them, as long as the respective IDoc is still in the system, please follow Note 126678.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-MID-ALE (Integration Technology ALE)"}, {"Key": "Responsible                                                                                         ", "Value": "D019512"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000153205/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000153205/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000153205/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000153205/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000153205/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000153205/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000153205/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000153205/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000153205/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "737696", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/737696"}, {"RefNumber": "637683", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/637683"}, {"RefNumber": "587896", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/587896"}, {"RefNumber": "573656", "RefComponent": "BC-BMT-WFM", "RefTitle": "Collective note relating to Archiving in workflow", "RefUrl": "/notes/573656"}, {"RefNumber": "49545", "RefComponent": "BC-BMT-WFM", "RefTitle": "Deletion of work items that are no longer required", "RefUrl": "/notes/49545"}, {"RefNumber": "420371", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info on upgrading to Basis 4.6C SR2 (NDI upgrade)", "RefUrl": "/notes/420371"}, {"RefNumber": "390062", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to 4.6C SR2", "RefUrl": "/notes/390062"}, {"RefNumber": "329622", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions: Upgrading to Basis 4.6C SR1 (NDI upgrade)", "RefUrl": "/notes/329622"}, {"RefNumber": "327285", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade to 4.6C SR1", "RefUrl": "/notes/327285"}, {"RefNumber": "304597", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade to Release 4.6C  FCS", "RefUrl": "/notes/304597"}, {"RefNumber": "192949", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info for upgrade to 4.6C", "RefUrl": "/notes/192949"}, {"RefNumber": "179373", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info for upgrade to Rel.4.6B", "RefUrl": "/notes/179373"}, {"RefNumber": "155674", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions upgrade to Release 4.6A", "RefUrl": "/notes/155674"}, {"RefNumber": "126678", "RefComponent": "BC-BMT-WFM", "RefTitle": "Archiving or deleting work items of type C", "RefUrl": "/notes/126678"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1813141", "RefComponent": "BC-MID-ALE", "RefTitle": "How to delete unnecessary workitems of IDoc processing", "RefUrl": "/notes/1813141 "}, {"RefNumber": "215982", "RefComponent": "BC-MID-ALE", "RefTitle": "IDoc: Archiving without update of corresponding links", "RefUrl": "/notes/215982 "}, {"RefNumber": "573656", "RefComponent": "BC-BMT-WFM", "RefTitle": "Collective note relating to Archiving in workflow", "RefUrl": "/notes/573656 "}, {"RefNumber": "126678", "RefComponent": "BC-BMT-WFM", "RefTitle": "Archiving or deleting work items of type C", "RefUrl": "/notes/126678 "}, {"RefNumber": "390062", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to 4.6C SR2", "RefUrl": "/notes/390062 "}, {"RefNumber": "327285", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade to 4.6C SR1", "RefUrl": "/notes/327285 "}, {"RefNumber": "329622", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions: Upgrading to Basis 4.6C SR1 (NDI upgrade)", "RefUrl": "/notes/329622 "}, {"RefNumber": "179373", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info for upgrade to Rel.4.6B", "RefUrl": "/notes/179373 "}, {"RefNumber": "420371", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info on upgrading to Basis 4.6C SR2 (NDI upgrade)", "RefUrl": "/notes/420371 "}, {"RefNumber": "49545", "RefComponent": "BC-BMT-WFM", "RefTitle": "Deletion of work items that are no longer required", "RefUrl": "/notes/49545 "}, {"RefNumber": "192949", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info for upgrade to 4.6C", "RefUrl": "/notes/192949 "}, {"RefNumber": "155674", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions upgrade to Release 4.6A", "RefUrl": "/notes/155674 "}, {"RefNumber": "304597", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade to Release 4.6C  FCS", "RefUrl": "/notes/304597 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "31G", "To": "31I", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 1, "URL": "/corrins/0000153205/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}