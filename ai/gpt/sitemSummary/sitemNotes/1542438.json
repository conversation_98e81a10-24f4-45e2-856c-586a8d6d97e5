{"Request": {"Number": "1542438", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 3138, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017153292017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001542438?language=E&token=8EB0103A56246F35AFED5EF53F4ADCF7"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001542438", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001542438/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1542438"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Currentness", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations/Additional Information"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.07.2013"}, "SAPComponentKey": {"_label": "Component", "value": "IS-M-MD-BP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Partner"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Media", "value": "IS-M", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-M*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Master Data", "value": "IS-M-MD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-M-MD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Partner", "value": "IS-M-MD-BP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-M-MD-BP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1542438 - IS-M: Error analysis for CVI errors"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1542438&TargetLanguage=EN&Component=IS-M-MD-BP&SourceLanguage=DE&Priority=06\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/1542438/D\" target=\"_blank\">/notes/1542438/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You use the SAP business partner in the IS-Media environment. When you create or change business partners with customer or vendor roles, a short dump occurs with the runtime error MESSAGE_TYPE_X_TEXT. From the short dump, it is not clear which error actually led to the termination.</p><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>ISMBP_DIALOG, BP_CVI, MDS_PPO2, PPO, MDS_CTRL_STRATEGY, SYNCHRONIZE, MESSAGE_TYPE_X_TEXT</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>Customer and vendor data is updated using the CVI interface (as of Release 600). During the update, numerous checks are carried out. For technical reasons, only a short dump can prevent the incorrect data from being transferred to the database.<br /><br />If the Postprocessing Office (PPO) is inactive, the CVI interface therefore generates a termination in the event of an error to prevent the update of incorrect data.<br /><br />If the PPO is activated, however, the data is updated despite errors, but errors are logged and a postprocessing order is created. As a result, the transaction can be postprocessed or subsequently updated.</p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><p>If a test system is available that was created from a copy of the production system, the case should preferably be reproduced with the same data in this system for troubleshooting purposes.<br /><br />The following options are available for error analysis:</p> <OL>1. Analysis in the Debugger<br />(Note: this method requires debugging authorization of the user and only works immediately after the short dump occurs. If the dump is displayed again later using transaction ST22, the debugging option is no longer available.)<br /><br />The cause of the synchronization errors can be found in the internal table LS_ERROR-OBJECTS. To do this, proceed as follows:</OL> <UL><UL><LI>Go to the debugger if a dump occurs.</LI></UL></UL> <UL><UL><LI>Enter the variable LS_ERROR-OBJECTS.</LI></UL></UL> <UL><UL><LI>Double-click to go deeper into the structure. You can find the errors at the level: LS_ERROR-OBJECTS[1]-ID_MESSAGES[1]-MESSAGES.</LI></UL></UL> <p>              For more information, see SAP Note 956054, paragraph 1.2. <p>              Before you try to reproduce the error, make sure that the analyzing user has debugging authorization. <OL>2. You use the Postprocessing Office (PPO).<br /><br />In Customizing, activate the Postprocessing Office for the object BP. This prevents a short dump from occurring. Instead, the errors are logged and can be displayed subsequently using the Postprocessing Desktop (transaction MDS_PPO2).<br />Select the value &quot;CVI*&quot; as the selection option for the business process.<br />This method is not recommended for permanent use in the production system, since this can initially result in inconsistent data, which requires regular careful postprocessing of the logged errors.<br /><br />For details about activating the PPO and the evaluation, see also SAP Note 956054, paragraph 1.2.</OL> <OL>3. As of SAP_ABA 700, SAP Note 1239993 of AP_MD_BF_SYN is available for implementation or, depending on the Support Package level, is already available in the system. After you implement these corrections, the system issues up to eight error messages in the short dump that caused the termination and generally indicate the cause unquestionably. The messages are displayed in the Error Analysis section. This concerns the content of the variables LS_MSGTXT1 to LS_MSGTXT8.</OL><OL>4. Modification to display precise error messages in short dump<br /><br />In practice, methods 1 and 2 are unsuitable for terminations that occur sporadically in the production system. If the SAP Note mentioned under point 3 is not yet available for your release, we recommend that you implement a small modification instead, which saves up to eight error messages in the variables lv_assert_m1 to lv_assert_m8 and outputs their content in the short dump. This information is usually sufficient to determine and remove the actual cause of the short dump.<br />In addition, all error messages that occurred are also saved in the shared buffer, from where they can be read again if required using the utility program ZJC_MDS_BP_LOAD after short dumps have occurred.<br /><br />For the modification, proceed as follows:</OL> <OL><OL>a) Call transaction SE24, select the class MDS_CTRL_STRATEGY, and edit the method SYNCHRONIZE.</OL></OL> <OL><OL>b) Search for the context block displayed in the attachment MDS_CTRL_STRATEGY-SYNCHRONIZE.txt and add the program source code that is also contained in this attachment directly after it.</OL></OL> <OL><OL>c) Save and activate the change.</OL></OL> <OL><OL>d) Call transaction SE38.</OL></OL> <OL><OL>e) Create the report ZCJ_MDS_BP_LOAD in any package.</OL></OL> <OL><OL>f) Replace the automatically generated header with the program code contained in the attachment ZCJ_MDS_BP_LOAD.txt.</OL></OL> <p></p></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Owner                                                                                    ", "Value": "<PERSON><PERSON><PERSON> (D026802)"}, {"Key": "Processor                                                                                          ", "Value": "D001188"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001542438/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "MDS_CTRL_STRATEGY-SYNCHRONIZE.txt", "FileSize": "6", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000586812010&iv_version=0002&iv_guid=6D098EDC1592E341A782966DAC49D9A1"}, {"FileName": "ZCJ_MDS_BP_LOAD.txt", "FileSize": "1", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000586812010&iv_version=0002&iv_guid=27C3BFF4D7C2AF47AF0FA85AB60F9479"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "956054", "RefComponent": "FS-BP", "RefTitle": "BP_CVI: Customer/vendor integration as of ERP 6.00", "RefUrl": "/notes/956054"}, {"RefNumber": "907860", "RefComponent": "IS-M-MD-BP", "RefTitle": "ERP 2005: Details on Business Partner Management", "RefUrl": "/notes/907860"}, {"RefNumber": "1239993", "RefComponent": "AP-MD-BP-SYN", "RefTitle": "Informative dump for inactive PPO", "RefUrl": "/notes/1239993"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "956054", "RefComponent": "FS-BP", "RefTitle": "BP_CVI: Customer/vendor integration as of ERP 6.00", "RefUrl": "/notes/956054 "}, {"RefNumber": "907860", "RefComponent": "IS-M-MD-BP", "RefTitle": "ERP 2005: Details on Business Partner Management", "RefUrl": "/notes/907860 "}, {"RefNumber": "1239993", "RefComponent": "AP-MD-BP-SYN", "RefTitle": "Informative dump for inactive PPO", "RefUrl": "/notes/1239993 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "IS-M", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "IS-M", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "IS-M", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "IS-M", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "IS-M", "From": "605", "To": "605", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1542438&TargetLanguage=EN&Component=IS-M-MD-BP&SourceLanguage=DE&Priority=06\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/1542438/D\" target=\"_blank\">/notes/1542438/D</a>."}}}}