{"Request": {"Number": "924727", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 704, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016060512017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000924727?language=E&token=C07CCB2A7768057A24257F467A3E9BDC"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000924727", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000924727/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "924727"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.09.2006"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PROJ-DBM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Parallel Customer Development DBM"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Project-based solutions", "value": "XX-PROJ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Parallel Customer Development DBM", "value": "XX-PROJ-DBM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-DBM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "924727 - Current Release Restrictions for DBMC 100 for Ramp Up"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p><br />You want information about the release restrictions for release DBMC &#x00A0;&#x00A0;100.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br />Dear Customer,<br /><br />SAP announces the following restrictions regarding the use of the DBMC 1.0 release. The status of the following statements corresponds to<br />the date when this note was last changed.<br /><br />SAP welcomes all questions, suggestions, or comments on the<br />productive use of the DBMC 1.0 release or the individual functions.<br /><br />Sincerely,<br />IS Industy Development - Quality Management<br /><br />General Restrictions:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;o&#x00A0;&#x00A0;Please check all restrictions with respect to ECC 5.0 in note<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 741821.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;o&#x00A0;&#x00A0;Please check all restrictions with respect to ECC DIMP 5.0 in<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; note 731290.</p> <UL><LI>Please check all restrictions with respect to SAP DBM 5.0 in 855514.</LI></UL> <UL><LI>DBMC 100 does currently not support unicode.</LI></UL> <UL><LI>Ensure that SAP_BASIS support package 16 is implemented before installing SAP DBMC 1.0.</LI></UL> <UL><LI>Ensure that DBM 500 SP02 has been installed.</LI></UL> <UL><LI>The following notes belong to Support Package DBM500 SP03 (SAPK-50003INDBM) and must be implemented before installing DBMC 100 if this Support Package is not installed in your system: 931434, 930417, 930301, 929614, 928335, 928283, 928179, 927874, 927843, 927344, 926807, 926599, 926254, 926143, 925799, 925763, 925760, 925754, 924969, 924731, 924592, 924574, 924547, 924360, 923874, 923871, 923826, 923804, 923535, 923534, 923531, 923466, 923315, 922838, 922737, 922460.</LI></UL> <p><br />The restrictions affect planned functionality, which should be released<br />in one of the upcoming support packages or releases. This functionality<br />is not part of the functionality which we have agreed to deliver with<br />DBMC 100. The restrictions described below do not form a complete list and can be changed at any time.<br /><br />Released with restrictions:<br /><br />Release only with the consent of SAP or release only with approval<br />of or after consultation with SAP. Please contact component XX-PROJ-DBM via message:</p> <UL><LI>MRS (Multi-Resource Scheduling)<br />Multi-resource scheduling(MRS) may only be used in conjunction with Human Resources (HR). The Business Partner (BP) is not supported.</LI></UL> <UL><LI>Editable Item List Including Fast Entry of Items</LI></UL> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D037163)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D039477)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000924727/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000924727/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000924727/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000924727/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000924727/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000924727/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000924727/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000924727/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000924727/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "971364", "RefComponent": "XX-INT-DOCU-FIN", "RefTitle": "SAP ERP 2004 VERSION INFORMATION", "RefUrl": "/notes/971364"}, {"RefNumber": "931434", "RefComponent": "XX-PROJ-DBM", "RefTitle": "Installing DBMC 100 on SAP ECC 500", "RefUrl": "/notes/931434"}, {"RefNumber": "930417", "RefComponent": "XX-PROJ-DBM", "RefTitle": "DBM return call: No pricing because partner does not exist", "RefUrl": "/notes/930417"}, {"RefNumber": "930301", "RefComponent": "XX-PROJ-DBM", "RefTitle": "Srch help for key fld in recall/warranty claim does not work", "RefUrl": "/notes/930301"}, {"RefNumber": "929614", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Unsaved data is displayed", "RefUrl": "/notes/929614"}, {"RefNumber": "928335", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Only status change is not stored", "RefUrl": "/notes/928335"}, {"RefNumber": "928283", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Internal order not displayed under split in document flow", "RefUrl": "/notes/928283"}, {"RefNumber": "928179", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Short dump due to special characters entry in job number fie", "RefUrl": "/notes/928179"}, {"RefNumber": "927874", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "EXT flag in job change must be set manually", "RefUrl": "/notes/927874"}, {"RefNumber": "927843", "RefComponent": "IS-A-DBM-ACC", "RefTitle": "Bill-to party is not found automatically", "RefUrl": "/notes/927843"}, {"RefNumber": "927344", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Split-the values from subscreens are lost.", "RefUrl": "/notes/927344"}, {"RefNumber": "926807", "RefComponent": "XX-PROJ-DBM", "RefTitle": "Multiple item entry, job 1 is assigned for all", "RefUrl": "/notes/926807"}, {"RefNumber": "926599", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Items cannot be created with user-defined item number", "RefUrl": "/notes/926599"}, {"RefNumber": "926254", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Flexibility for screen substitution in order access", "RefUrl": "/notes/926254"}, {"RefNumber": "926143", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/926143"}, {"RefNumber": "925799", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Sales price field in item tab is not ready for input", "RefUrl": "/notes/925799"}, {"RefNumber": "925763", "RefComponent": "IS-A-DBM", "RefTitle": "Valid material group has no effect in material search", "RefUrl": "/notes/925763"}, {"RefNumber": "925760", "RefComponent": "IS-A-DBM", "RefTitle": "Pgm termination in DBM ord during creation of warranty data", "RefUrl": "/notes/925760"}, {"RefNumber": "925754", "RefComponent": "IS-A-DBM", "RefTitle": "No F4 help in the 'defect code' field of the warranty header", "RefUrl": "/notes/925754"}, {"RefNumber": "924969", "RefComponent": "IS-A-DBM", "RefTitle": "Purchase price on item detail for manual parts", "RefUrl": "/notes/924969"}, {"RefNumber": "924731", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "No vehicle structure update after action ORD_VEHIC_REMOVE", "RefUrl": "/notes/924731"}, {"RefNumber": "924592", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Missing posting type for internal orders with processing", "RefUrl": "/notes/924592"}, {"RefNumber": "924574", "RefComponent": "IS-A-DBM", "RefTitle": "PACK: Item numbering and Labor Value Main Type Search help", "RefUrl": "/notes/924574"}, {"RefNumber": "924547", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Incorrect pricing for missing header condition for order", "RefUrl": "/notes/924547"}, {"RefNumber": "924360", "RefComponent": "IS-A-DBM", "RefTitle": "Order item creation doesn't work - item change", "RefUrl": "/notes/924360"}, {"RefNumber": "923874", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Order transaction enhancement - initialization", "RefUrl": "/notes/923874"}, {"RefNumber": "923871", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Item Change clears the 'External Selected' flag", "RefUrl": "/notes/923871"}, {"RefNumber": "923826", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Separator of manufacturer part number is always ':'", "RefUrl": "/notes/923826"}, {"RefNumber": "923804", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Enhancements of search structure are not transferred", "RefUrl": "/notes/923804"}, {"RefNumber": "923535", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Fixed price for header or job results in errors in pricing", "RefUrl": "/notes/923535"}, {"RefNumber": "923534", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Missing connection DBM order for SD order", "RefUrl": "/notes/923534"}, {"RefNumber": "923531", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Incorrect material/pricing ref matl transfer from DBM to SD", "RefUrl": "/notes/923531"}, {"RefNumber": "923466", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Unexpected availability check for external labor", "RefUrl": "/notes/923466"}, {"RefNumber": "923315", "RefComponent": "IS-A-DBM", "RefTitle": "Material group for manual labor operation", "RefUrl": "/notes/923315"}, {"RefNumber": "922838", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Initial status of the \"W/out vehicle data\" option", "RefUrl": "/notes/922838"}, {"RefNumber": "922737", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Goods movement for a Vehicle order is not possible", "RefUrl": "/notes/922737"}, {"RefNumber": "922460", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Pricing reference material is not checked", "RefUrl": "/notes/922460"}, {"RefNumber": "907597", "RefComponent": "LO-WTY", "RefTitle": "Short dump on opening message log in WTY transaction.", "RefUrl": "/notes/907597"}, {"RefNumber": "741821", "RefComponent": "XX-SER-REL", "RefTitle": "Release limitations concerning SAP ERP 2004", "RefUrl": "/notes/741821"}, {"RefNumber": "731290", "RefComponent": "IS-A", "RefTitle": "Current release restrictions for ECC DIMP 5.0", "RefUrl": "/notes/731290"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "741821", "RefComponent": "XX-SER-REL", "RefTitle": "Release limitations concerning SAP ERP 2004", "RefUrl": "/notes/741821 "}, {"RefNumber": "731290", "RefComponent": "IS-A", "RefTitle": "Current release restrictions for ECC DIMP 5.0", "RefUrl": "/notes/731290 "}, {"RefNumber": "931434", "RefComponent": "XX-PROJ-DBM", "RefTitle": "Installing DBMC 100 on SAP ECC 500", "RefUrl": "/notes/931434 "}, {"RefNumber": "971364", "RefComponent": "XX-INT-DOCU-FIN", "RefTitle": "SAP ERP 2004 VERSION INFORMATION", "RefUrl": "/notes/971364 "}, {"RefNumber": "925763", "RefComponent": "IS-A-DBM", "RefTitle": "Valid material group has no effect in material search", "RefUrl": "/notes/925763 "}, {"RefNumber": "930301", "RefComponent": "XX-PROJ-DBM", "RefTitle": "Srch help for key fld in recall/warranty claim does not work", "RefUrl": "/notes/930301 "}, {"RefNumber": "930417", "RefComponent": "XX-PROJ-DBM", "RefTitle": "DBM return call: No pricing because partner does not exist", "RefUrl": "/notes/930417 "}, {"RefNumber": "924574", "RefComponent": "IS-A-DBM", "RefTitle": "PACK: Item numbering and Labor Value Main Type Search help", "RefUrl": "/notes/924574 "}, {"RefNumber": "929614", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Unsaved data is displayed", "RefUrl": "/notes/929614 "}, {"RefNumber": "927874", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "EXT flag in job change must be set manually", "RefUrl": "/notes/927874 "}, {"RefNumber": "928335", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Only status change is not stored", "RefUrl": "/notes/928335 "}, {"RefNumber": "925754", "RefComponent": "IS-A-DBM", "RefTitle": "No F4 help in the 'defect code' field of the warranty header", "RefUrl": "/notes/925754 "}, {"RefNumber": "928179", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Short dump due to special characters entry in job number fie", "RefUrl": "/notes/928179 "}, {"RefNumber": "924731", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "No vehicle structure update after action ORD_VEHIC_REMOVE", "RefUrl": "/notes/924731 "}, {"RefNumber": "928283", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Internal order not displayed under split in document flow", "RefUrl": "/notes/928283 "}, {"RefNumber": "923535", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Fixed price for header or job results in errors in pricing", "RefUrl": "/notes/923535 "}, {"RefNumber": "927843", "RefComponent": "IS-A-DBM-ACC", "RefTitle": "Bill-to party is not found automatically", "RefUrl": "/notes/927843 "}, {"RefNumber": "925799", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Sales price field in item tab is not ready for input", "RefUrl": "/notes/925799 "}, {"RefNumber": "927344", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Split-the values from subscreens are lost.", "RefUrl": "/notes/927344 "}, {"RefNumber": "923466", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Unexpected availability check for external labor", "RefUrl": "/notes/923466 "}, {"RefNumber": "923874", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Order transaction enhancement - initialization", "RefUrl": "/notes/923874 "}, {"RefNumber": "926599", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Items cannot be created with user-defined item number", "RefUrl": "/notes/926599 "}, {"RefNumber": "923871", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Item Change clears the 'External Selected' flag", "RefUrl": "/notes/923871 "}, {"RefNumber": "926807", "RefComponent": "XX-PROJ-DBM", "RefTitle": "Multiple item entry, job 1 is assigned for all", "RefUrl": "/notes/926807 "}, {"RefNumber": "922460", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Pricing reference material is not checked", "RefUrl": "/notes/922460 "}, {"RefNumber": "924360", "RefComponent": "IS-A-DBM", "RefTitle": "Order item creation doesn't work - item change", "RefUrl": "/notes/924360 "}, {"RefNumber": "922737", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Goods movement for a Vehicle order is not possible", "RefUrl": "/notes/922737 "}, {"RefNumber": "925760", "RefComponent": "IS-A-DBM", "RefTitle": "Pgm termination in DBM ord during creation of warranty data", "RefUrl": "/notes/925760 "}, {"RefNumber": "924969", "RefComponent": "IS-A-DBM", "RefTitle": "Purchase price on item detail for manual parts", "RefUrl": "/notes/924969 "}, {"RefNumber": "926254", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Flexibility for screen substitution in order access", "RefUrl": "/notes/926254 "}, {"RefNumber": "923315", "RefComponent": "IS-A-DBM", "RefTitle": "Material group for manual labor operation", "RefUrl": "/notes/923315 "}, {"RefNumber": "924547", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Incorrect pricing for missing header condition for order", "RefUrl": "/notes/924547 "}, {"RefNumber": "924592", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Missing posting type for internal orders with processing", "RefUrl": "/notes/924592 "}, {"RefNumber": "923534", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Missing connection DBM order for SD order", "RefUrl": "/notes/923534 "}, {"RefNumber": "923826", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Separator of manufacturer part number is always ':'", "RefUrl": "/notes/923826 "}, {"RefNumber": "923531", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Incorrect material/pricing ref matl transfer from DBM to SD", "RefUrl": "/notes/923531 "}, {"RefNumber": "923804", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Enhancements of search structure are not transferred", "RefUrl": "/notes/923804 "}, {"RefNumber": "922838", "RefComponent": "IS-A-DBM-ORD", "RefTitle": "Initial status of the \"W/out vehicle data\" option", "RefUrl": "/notes/922838 "}, {"RefNumber": "907597", "RefComponent": "LO-WTY", "RefTitle": "Short dump on opening message log in WTY transaction.", "RefUrl": "/notes/907597 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "DBMC", "From": "100", "To": "100", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}