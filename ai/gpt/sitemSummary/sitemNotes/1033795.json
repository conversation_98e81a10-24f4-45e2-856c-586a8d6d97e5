{"Request": {"Number": "1033795", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 409, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000006190552017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001033795?language=E&token=809A0A6A2B17380EEAFA5345C81CA0D1"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001033795", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001033795/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1033795"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.03.2007"}, "SAPComponentKey": {"_label": "Component", "value": "SRM-EBP-INV"}, "SAPComponentKeyText": {"_label": "Component", "value": "Invoicing"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Supplier Relationship Management", "value": "SRM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SRM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SRM", "value": "SRM-EBP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SRM-EBP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Invoicing", "value": "SRM-EBP-INV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SRM-EBP-INV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1033795 - BBPERS: Value-added tax not displayed for each credit memo"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>When you use ERS to create credit memos, the value-added tax is not displayed for each credit memo that you create. It is only displayed once in summarized form at the end of the form.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>BBP_ERS, BBPERS<br />Processing class CL_ERS_PROCESSING_BBP<br />Processing method PROCESS_BBP_ERS</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Previously, this function was not available.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Form BBP_ERS_CRETAX contains the ERS supplier output that displays the document-related tax. The attachment of this note contains two sample types of supplier output:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;One of them is a message referring to two credit memos with different tax rates.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The other message refers to one credit memo with two types of tax rate.<br /><br />If you have already imported SRM_SERVER 500 Support Package 12 or SRM_SERVER 550 Support Package 09 into your system, you only need to edit the following in \"Define Actions for Output of Invoices and ERS Documents\" in Customizing:<br />Form name&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BBP_ERS_CRETAX<br />Processing class&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CL_ERS_PROCESSING_BBP<br />Processing method&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PROCESS_BBP_ERS<br />Package&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; BBP_OUT<br /><br />If you have not yet imported these Support Package levels into your system, you can use the following instructions to create form BBP_ERS_CRETAX and its function:</p> <UL><LI>Import transport PLCK154547 as described in Note 13719. The attachment contains the transport files in PLCK154547.zip</LI></UL> <UL><LI>Call transaction SE11.<br />Create data type BBPT_ERS_OUTPUT_ITEMTAX:</LI></UL> <UL><UL><LI>Table type&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; BBPT_ERS_OUTPUT_ITEMTAX</LI></UL></UL> <UL><UL><LI>Short Description: Table for Smart Form ERS item tax</LI></UL></UL> <UL><UL><LI>Line type&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BBP_ERS_OUTPUT_ITEMTAX</LI></UL></UL> <UL><LI>Call transaction SMARTFORMS.<br />Adjust form BBP_ERS_CRETAX to SRM_SERVER 500 or 550.</LI></UL> <UL><UL><LI>Delete the following table parameters:</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;IT_OUTPUT_ITEMS<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;IT_OUTPUT_TAX<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;IT_OUTPUT_ITEMTAX</p> <UL><UL><LI>Specify the following additional import parameters:<br />Parameter Name&#x00A0;&#x00A0;&#x00A0;&#x00A0; Type spec. Associated Type&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Optional<br />IT_OUTPUT_ITEMS&#x00A0;&#x00A0;&#x00A0;&#x00A0;TYPE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BBPT_ERS_OUTPUT_ITEMS<br />IT_OUTPUT_TAX&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;TYPE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BBPT_ERS_OUTPUT_TAX<br />IT_OUTPUT_ITEMTAX&#x00A0;&#x00A0;TYPE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BBPT_ERS_OUTPUT_ITEMTAX TRUE</LI></UL></UL> <UL><UL><LI>Save and generate the form.</LI></UL></UL> <UL><LI>Once you have done this, implement the correction instructions contained in this note according to your SRM_SERVER release.</LI></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "DownPort/UpPort-WF", "Value": "UpPort check done, symptom repaired"}, {"Key": "Responsible                                                                                         ", "Value": "D000823"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D026613)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001033795/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001033795/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001033795/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001033795/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001033795/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001033795/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001033795/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001033795/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001033795/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "PLCK154547.zip", "FileSize": "20", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000238452007&iv_version=0001&iv_guid=86FE86A8E3B56944B3132759201002CE"}, {"FileName": "BBP_ERS_CRETAX_example2.txt", "FileSize": "1", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000238452007&iv_version=0001&iv_guid=B9AD6D8404402F4BAB978FF72093090E"}, {"FileName": "BBP_ERS_CRETAX_example1.txt", "FileSize": "2", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000238452007&iv_version=0001&iv_guid=F57ADE2F0F751245B4F9F427DC975882"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "790135", "RefComponent": "SRM-EBP-INV", "RefTitle": "BBPERS: Value-added tax not displayed for each credit memo", "RefUrl": "/notes/790135"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "790135", "RefComponent": "SRM-EBP-INV", "RefTitle": "BBPERS: Value-added tax not displayed for each credit memo", "RefUrl": "/notes/790135 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SRM_SERVER", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SRM_SERVER", "From": "550", "To": "550", "Subsequent": ""}, {"SoftwareComponent": "SRM_SERVER", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SRM_SERVER 500", "SupportPackage": "SAPKIBKS12", "URL": "/supportpackage/SAPKIBKS12"}, {"SoftwareComponentVersion": "SRM_SERVER 550", "SupportPackage": "SAPKIBKT09", "URL": "/supportpackage/SAPKIBKT09"}, {"SoftwareComponentVersion": "SRM_SERVER 600", "SupportPackage": "SAPKIBKU04", "URL": "/supportpackage/SAPKIBKU04"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SRM_SERVER", "NumberOfCorrin": 2, "URL": "/corrins/0001033795/551"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SRM_SERVER", "ValidFrom": "500", "ValidTo": "500", "Number": "753446 ", "URL": "/notes/753446 ", "Title": "Missing tax in output of ERS credit memos", "Component": "SRM-EBP-INV"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}