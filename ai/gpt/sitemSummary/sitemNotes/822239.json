{"Request": {"Number": "822239", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 371, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015855202017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000822239?language=E&token=A466ED909FF2C905EFA43FDE9441009C"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000822239", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000822239/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "822239"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 70}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.01.2021"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-SDB"}, "SAPComponentKeyText": {"_label": "Component", "value": "MaxDB"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "MaxDB", "value": "BC-DB-SDB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-SDB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "822239 - FAQ: SAP MaxDB interfaces"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note is a collection of questions that are often asked by colleagues and customers regarding the SAP MaxDB interfaces. It provides answers and refers you to other information sources.<br />This SAP Note does not claim to be exhaustive.</p>\r\n<ol>1. What are SAP MaxDB interfaces?</ol><ol>2. What SAP MaxDB interfaces are there?</ol><ol>3. What is the SAP MaxDB precompiler or the precompiler runtime?</ol><ol>4. Which version of the precompiler runtime is used in which SAP releases?</ol><ol>5. Does the precompiler runtime also have to be installed on the application server?</ol><ol>6. How can I determine which versions of the precompiler runtime are installed on a host?</ol><ol>7. Can I also use irconf to determine the installed precompiler runtime versions?</ol><ol>8. Where does irconf receive the information from?</ol><ol>9. How can I register a precompiler runtime version?</ol><ol>10. How can I determine which precompiler runtime is currently in use in the SAP system?</ol><ol>11. How do I install or update the precompiler runtime?</ol><ol>12. How do I activate or deactivate the precompiler trace?</ol><ol>13. What is the SQLDBC interface?</ol><ol>14. Which version of the SQLDBC runtime is used in which SAP releases?</ol><ol>15. Does the SQLDBC runtime also have to be installed on the application server?</ol><ol>16. How can I determine which versions of the SQLDBC runtime are installed on a host?</ol><ol>17. How can I determine which version of the SQLDBC runtime is currently in use?</ol><ol>18. How can I determine which version an SAP MaxDB client library has?</ol><ol>19. How do I install or upgrade the SQLDBC runtime?</ol><ol>20. How do I activate or deactivate the SQLDBC trace?</ol><ol>21. What is the ODBC interface?</ol><ol>22. What is the ODBC interface used for in the SAP environment?</ol><ol>23. How can I determine which ODBC version is installed on the host?</ol><ol>24. How do I install or upgrade the SAP MaxDB ODBC driver?</ol><ol>25. How can I create an SQL trace on the SAP Content Server?</ol><ol>26. What is the JDBC interface?</ol><ol>27. What is the JDBC interface used for in the SAP environment?</ol><ol>28. How can I determine which JDBC version is installed on the host?</ol><ol>29. How do I install or upgrade the SAP MaxDB JDBC driver?</ol><ol>30. How do I activate the JDBC trace?</ol><ol>31. You want to increase the number of J2EE instances. What do I have to consider in the database?</ol><ol>32. Where can I find additional information about SAP MaxDB interfaces?</ol>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>FAQ, precompiler, ODBC, SQLDBC, JDBC, interfaces, could not load libpcr, ODBC trace</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You use SAP MaxDB as of Version 7.5 in an OLTP, BW or SAP liveCache environment.<br /><br />For more FAQ notes for SAP MaxDB/liveCache, see <a target=\"_blank\" href=\"https://wiki.scn.sap.com/wiki/x/GkM\">SAP MaxDB FAQ Notes</a>.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ol>\r\n<li>What are SAP MaxDB interfaces?<br /><br />Database applications (such as SAP systems, for example) use interfaces to access SAP MaxDB databases. If the database application (and therefore the SAP MaxDB interface) is located on a different host to the database, the database system also requires the SAP MaxDB X server (communications server) for communication purposes. The JDBC interface also requires the SAP MaxDB X server for local communication.<br /><br /></li>\r\n<li>What SAP MaxDB interfaces are there?<br /><br />In the SAP MaxDB documentation (see SAP Note <a target=\"_blank\" href=\"/notes/767598\">767598</a>), a list of all SAP MaxDB interfaces is available under the key word \"Interface\". In this note, only the interfaces that are used in the SAP environment are explained in greater detail:</li>\r\n<ul>\r\n<li>C-Precompiler</li>\r\n<li>SQL Database Connectivity (SQLDBC): Object-oriented C++ interface for accessing the SAP MaxDB database system</li>\r\n<li>ODBC (3.5.1): SAP MaxDB ODBC driver for accessing the SAP MaxDB database system</li>\r\n<li>JDBC (2.0/3.0): SAP MaxDB JDBC driver for accessing the SAP MaxDB database system<br /><br /></li>\r\n</ul>\r\n<li>What is the SAP MaxDB precompiler or the precompiler runtime?<br /><br />The interface between the SAP MaxDB database system and application programs (such as the SAP kernel) is the database language SQL (Structured Query Language). SQL statements embedded into the application program (Embedded SQL) allow communication with the database. Parameter values are exchanged through special program variables, which are known as host variables.<br /><br />The C/C++ Precompiler prepares C/C++ source code with embedded SQL statements for the compilation into an executable application program. It checks the embedded statements syntactically and semantically, converts them into calls of precompiler runtime procedures and creates a C/C++ file, which can then be compiled.<br /><br />When the application program is running, the precompiler runtime assumes the following functions:<br /><ul>\r\n<li>Setting up the database connections and opening the database sessions according to the specified connection options</li>\r\n<li>Assigning parameter values</li>\r\n<li>Converting data types</li>\r\n<li>Executing the precompiler statements</li>\r\n<li>Signaling NULL values (undefined values) using indicator variables</li>\r\n<li>Displaying confirmations from the database system in the structure sqlca</li>\r\n<li>Writing the trace file according to the specified trace options</li>\r\n</ul><br />In the SAP environment, the precompiler runtime is required for the database-dependent part of the SAP kernel, which is known as the DBSL (dbadaslib).<br /><br /></li>\r\n<li>Which version of the precompiler runtime is used in which SAP releases?<br /><br />The precompiler runtime (libpcr) versions are used depending on the SAP release but independent of the SAP MaxDB version.</li>\r\n<ol style=\"list-style-type: lower-alpha;\">\r\n<li>31I extended SAP kernel<br />The precompiler runtime version required by the DBSL (dbadaslib) is 7.3.00, &gt;= Build 33 (on Microsoft Windows), &gt;= Build 26 (on UNIX).</li>\r\n<li>40B extended SAP kernel<br />The precompiler runtime version required by the DBSL (dbadaslib) is 7.3.00, &gt;= Build 33 (under Microsoft Windows), &gt;= Build 26 (under Unix).</li>\r\n<li>45B extended SAP kernel (including APO 1.1, APO 2.0)<br />The precompiler runtime version required by the DBSL (dbadaslib) is in all cases 7.3.00, &gt;= Build 33 (on Microsoft Windows), or &gt;= Build 26 (on Unix).</li>\r\n<li>46D extended SAP kernel (including APO 3.0 and APO 3.1)<br />The precompiler runtime versions required by the DBSL (dbadaslib) are:<br />- 7.3.01 &gt;= Build 10 (Microsoft Windows NT and UNIX)<br />- 7.4.03 &gt;= Build 5 (Microsoft Windows NT IA64)<br />- 7.4.03 &gt;= Build 17 (Linux IA64)<br />- 7.4.03 &gt;= Build 26 (HP IA64)<br />- 7.5.00 &gt;= Build 16 (Linux AMD X86_64)</li>\r\n<li>46D extended 2 SAP kernel<br />The precompiler runtime versions required by the DBSL (dbadaslib) are:<br />- 7.5.00 &gt;= Build 47 (Microsoft Windows NT)<br />- 7.5.00 &gt;= Build 46 (Microsoft Windows NT IA64 and UNIX)</li>\r\n<li>SAP kernel Release 6.20 Basis, 4.70 (including APO 4.0, Web AS 6.20 and 6.30)<br />The precompiler runtime versions required by the DBSL (dbadaslib) are:<br />- 7.3.01 &gt;= Build 10 (Microsoft Windows NT and UNIX)<br />- 7.4.03 &gt;= Build 5 (Microsoft Windows NT IA64)<br />- 7.4.03 &gt;= Build 17 (Linux IA64)<br />- 7.4.03 &gt;= Build 26 (HP IA64)</li>\r\n<li>SAP kernel Release 6.40 Basis, 6.40 AKK (including NetWeaver 04, Web AS 6.40, SCM 4.1, and 6.30 J2EE)<br />The precompiler runtime versions required by the DBSL (dbadaslib) are:<br />- 7.4.03 &gt;= Build 11 (Microsoft Windows NT and UNIX)<br />- 7.4.03 &gt;= Build 17 (Linux IA64)<br />- 7.4.03 &gt;= Build 26 (HP IA64)<br />- 7.5.00 &gt;= Build 16 (LINUX PPC64 and LINUX AMD64)<br />- 7.5.00 &gt;= Build 15 (LINUX s390)<br />- 7.5.00 &gt;= Build 24 (Microsoft Windows NT AMD64)</li>\r\n<li>SAP kernel Release 6.40 ext 2<br />As of this SAP kernel release, the SQLDBC interface is used instead of the precompiler runtime. The assignment of the SAP kernel to the relevant SQLDBC versions can be found under point 14 in this SAP Note.<br /><br /></li>\r\n</ol>\r\n<li>Does the precompiler runtime also have to be installed on the application server?<br /><br />Yes. You must install the precompiler runtime on each application server and on the database server if you are using one of the aforementioned SAP kernels.<br /><br /></li>\r\n<li>How can I determine which versions of the precompiler runtime are installed on a host?<br /><br />You can use the program <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">SDBREGVIEW</span> to determine which SAP MaxDB software components are installed on the corresponding host. If precompiler runtime versions are installed on this host, they are displayed as follows.</li>\r\n<ol style=\"list-style-type: lower-alpha;\">\r\n<li>On UNIX platforms:<br /><br />At operating system level, start: <em><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#xFEFF;sdbregview -l | grep -i PCR</span><br /></em>You receive a list of the precompiler versions installed on this host.<br /><br />Example:<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;PCR 7300&#x00A0;&#x00A0;/program files/sdb/programs&#x00A0;&#x00A0;*********&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; valid</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>PCR 7301&#x00A0;&#x00A0;/program files/sdb/programs&#x00A0;&#x00A0;*********&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;valid</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>PCR 7500&#x00A0;&#x00A0;/program files/sdb/programs&#x00A0;&#x00A0;*********&#x00A0;&#x00A0;32 bit&#x00A0;&#x00A0; valid</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>PCR 7104&#x00A0;&#x00A0;/program files/sdb/programs&#x00A0;&#x00A0;*********&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; valid</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>PCR 7240&#x00A0;&#x00A0;/program files/sdb/programs&#x00A0;&#x00A0;*********&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;valid</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>PCR 7600&#x00A0;&#x00A0;/program files/sdb/programs&#x00A0;&#x00A0;*********&#x00A0;&#x00A0;32 bit&#x00A0;&#x00A0; valid</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>PCR 7250&#x00A0;&#x00A0;/program files/sdb/programs&#x00A0;&#x00A0;*********&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;valid</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>PCR 7403&#x00A0;&#x00A0;/program files/sdb/programs&#x00A0;&#x00A0;*********&#x00A0;&#x00A0;32 bit&#x00A0;&#x00A0; valid</em></span><br /><br /></li>\r\n<li>On Microsoft Windows platforms:<br /><br />At operating system level, start: <em><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#xFEFF;sdbregview -l | find /I \"PCR\"</span><br /></em>You receive a list of the precompiler runtime versions installed on this host.<br /><br />Example:<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;PCR 7300 c:/program files/sdb/programs&#x00A0;&#x00A0;*********&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;valid</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>PCR 7301 c:/program files/sdb/programs&#x00A0;&#x00A0;*********&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;valid</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>PCR 7500 c:/program files/sdb/programs&#x00A0;&#x00A0;*********&#x00A0;&#x00A0;32 bit valid</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>PCR 7104 c:/program files/sdb/programs&#x00A0;&#x00A0;*********&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; valid</em></span><br /><br /></li>\r\n</ol>\r\n<li>Can I also use irconf to determine the installed precompiler runtime versions?<br /><br />Yes, you can use the call <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;irconf -a&#xFEFF;</em></span> to determine which precompiler runtime versions are installed on a host. For example, the system issues the following output when you call an <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;irconf&#xFEFF;</em></span> of SAP MaxDB Version 7.5 on UNIX:<br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;/sapdb/programs/runtime/7500 -&gt; ******* from global registration</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>/sapdb/programs/runtime/7240 -&gt; ******* from old registration</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>/sapdb/programs/runtime/7250 -&gt; ******* from old registration</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>/sapdb/programs/runtime/7300 -&gt; ******* from old registration</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>/sapdb/programs/runtime/7301 -&gt; ******* from old registration</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>/sapdb/programs/runtime/7403 -&gt; ******* from old registration&#xFEFF;</em></span><br /><br /><strong>Note the following:</strong> You must also specify the option <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;-g&#xFEFF;</em></span> if you want to see cross-user registrations.<br /><br /></li>\r\n<li>Where does irconf receive the information from?<br /><br />The storage of information changed on UNIX between SAP MaxDB Version 7.4 and SAP MaxDB Version 7.5.</li>\r\n<ol style=\"list-style-type: lower-alpha;\">\r\n<li>Up to and including precompiler version 7.4<br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;/sapdb/programs/runtime/7240 -&gt; ******* from old registration</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>/sapdb/programs/runtime/7250 -&gt; ******* from old registration</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>/sapdb/programs/runtime/7300 -&gt; ******* from old registration</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>/sapdb/programs/runtime/7301 -&gt; ******* from old registration</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>/sapdb/programs/runtime/7403 -&gt; ******* from old registration&#xFEFF;</em></span><br /><br />The information regarding which versions are installed on a host is determined from the file <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;SAP_DBTECH.ini&#xFEFF;</em></span> in the directory <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;/usr/spool/sql/ini/&#xFEFF;</em></span>.<br /><br />On Microsoft Windows, the information is determined from the registry at <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;HKLM\\software\\SAP\\SAP DBTECH&#xFEFF;</em></span>. If an entry is missing in this file or in the registry, or if the file is missing, the system cannot find the precompiler runtime. The following error message, for example, is displayed:<br /><br /><em><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#xFEFF;Could not load libpcr (No registered version (7.4.3)</span><br /></em></li>\r\n<li>As of precompiler Version 7.5<br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;/sapdb/programs/runtime/7500 -&gt; ******* from global registration&#xFEFF;</em></span> <br />On Unix, the information is determined from the file <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;runtimes.ini&#xFEFF;</em></span> in the directory <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;&lt;indep_data&gt;/config&#xFEFF;</em></span>.<br /><br />On Microsoft Windows, the information is determined from the registry at <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;HKEY_LOCAL_MACHINE: Software\\SAP\\SAP DBTech</em> </span>:<br /><br /><strong>Comment:</strong><br />This new storage is known only by SAP kernel 6.40 on the following platforms:<br />- Linux: X86_64, PPC64, S390x<br />- Microsoft Windows X86_6<br /><br />SAP kernels of 6.40 or lower on all other platforms do NOT know this new storage, and these kernels therefore search only for the old registration.<br /><br /></li>\r\n</ol>\r\n<li>How can I register a precompiler runtime version?<br /><br />Using the command , <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;irconf -i -p /sapdb/programs/runtime/7403&#xFEFF;</em></span>, precompiler runtime Version 7.4.03 can be reregistered, for example.<br />Here, the version (&lt; 7. 5 or &gt;= 7.5) of \"irconf\" determines where the precompilers are registered.<br /><br />Example: A precompiler runtime with version 7.4.03 is registered with an \"irconf\" version 7.4 and registration then occurs in <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;SAP_DBTech.ini&#xFEFF;</em></span>. If registration occurs with an <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;irconf&#xFEFF;</em></span> of Version 7.5 or higher, the precompiler runtime Version 7.4.03 is registered at <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;/sapdb/programs/runtime/7.4.03&#xFEFF;</em></span>.<br /><br /></li>\r\n<li>How can I determine which precompiler runtime is currently in use in the SAP system?<br /><br />The version of the precompiler runtime that is currently being used by the SAP application is logged on each application server in the developer trace logs (dev_w* files). In transaction SM50, select a work process and choose&#x00A0;<em>Process -&gt; Trace -&gt; Display file</em> to go to the log. In the log, search for the following string:<br /><br /><span style=\"text-decoration: underline;\">C&#x00A0;&#x00A0;Precompiler Runtime:<br /></span>Example:<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;Precompiler Runtime : C-PreComp 7.3.1&#x00A0;&#x00A0;&#x00A0;&#x00A0;Build 011-000-091-899&#xFEFF;</em></span><br />Enter this version specification in the customer message to SAP if there is an error message.<br /><br /></li>\r\n<li>How do I install or update the precompiler runtime?<br /><br />The precompiler runtime is part of the SAP MaxDB client software package. To install or upgrade the SAP MaxDB precompiler runtime, use SAP Note <a target=\"_blank\" href=\"/notes/649814\">649814</a>.<br /><br /></li>\r\n<li>How do I activate or deactivate the precompiler trace?</li>\r\n<ul>\r\n<li>SAP Basis Release &lt;= 4.6D or precompiler versions &lt; 7.2.04:<br /><br />The activation of the precompiler trace is carried out using a parameter in the instance profile of the SAP system (file <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;&lt;SID&gt;_DVEBMGS&lt;instance_number&gt;_&lt;computer_name&gt;&#xFEFF;</em></span> in the directory <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;/usr/sap/&lt;SID&gt;/SYS/profile/&#xFEFF;</em></span>). After you have set the parameter, you must restart the SAP system, or at least the application server for which you are activating the trace. <em>*.pct</em> files are then written in the work directory of the SAP system.<br /><br />The following options exist:<br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;dbs/ada/sql_trace = 2</em></span><br />This is a detailed trace; the PCT files may become very large, but there is no risk of the required information being overwritten.<br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;dbs/ada/sql_trace = &lt;X&gt;&#xFEFF;</em></span><br />&lt;X&gt;: Number above 2: This is an alternate trace; X commands are logged for each PCT file, and then the relevant file is overwritten. There is no risk of the file system becoming full (if you do not select too large a number for X), but if the trace is not deactivated in time or the file is saved, the error may have already been overwritten when the analysis is carried out.<br /><br /></li>\r\n<li>Irtrace (precompiler Version 7.2.04 and higher (as of SAP Basis 6.10))<br /><br />As in the earlier versions, the precompiler trace can also be activated by setting the profile parameter.&#x00A0;However, you can also activate the trace using \"irtrace\" WITHOUT having to restart the system or the application server.<br /><br />The tool offers the following options for changing the behavior of the trace:<br /><br />Activating/deactivating/changing the trace for a particular process:<br /><em><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#xFEFF;irtrace -p &lt;process_id&gt; -t &lt;trace_type&gt;</span><br /><br /></em>The following trace types <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;&lt;trace_type&gt;&#xFEFF;</em></span> are available:<br />-<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em> &#xFEFF;long&#xFEFF;</em></span> : long trace,<br />- <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;short&#xFEFF;</em></span> : short trace<br />- <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;off&#xFEFF;</em></span> : deactivate trace<br /><br />For more information about the precompiler trace, see the support guide (SAP Note <a target=\"_blank\" href=\"/notes/692274\">692274</a>).<br /><br /></li>\r\n</ul>\r\n<li>What is the SQLDBC interface?<br /><br />Up to and including Database Version 7.5, the precompiler served as an interface between SAP MaxDB databases and C/C++ application programs. As of Database Version 7.6, the SQLDBC interface replaces the precompiler.<br /><br />SQL Database Connectivity (SQLDBC) is a runtime library for the development of database applications and database interfaces for SAP MaxDB. Applications can use SQLDBC to access SAP MaxDB databases, execute SQL statements and edit data. SQLDBC consists of the runtime library <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;libSQLDBC</em></span>, the software development kit SQLDBC SDK and the tool <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;sqldbc_cons</em></span>.<br /><br />The runtime library <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;libSQLDBC</em></span> is also called SQLDBC runtime. In the SAP environment, the SQLDBC runtime is required for the database-dependent part of the SAP kernel, which is known as the DBSL (<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;dbsdbslib</em></span>).<br /><br /></li>\r\n<li>Which SQLDBC version is used in which SAP releases?<br /><br />As of SAP kernel Release 6.40 EXT-2, the precompiler runtime has been replaced with the SQLDBC interface. The SQLDBC runtime (<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;libSQLDBC</em></span>) versions are used depending on the SAP release but independent of the SAP MaxDB core version.<br /><br />As of SAP Release 7.00, <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">DBADASLIB</span> is replaced by <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">DBSDBSLIB</span>. In general, we recommend that you always install the most current SQLDBC runtime.<br /><br /></li>\r\n<ul>\r\n<li>SAP kernel Release 6.40 EXT-2<br />The SQLDBC runtime is delivered for the first time in SAP kernel Release 6.40 EXT-2. This downward-compatible SAP kernel is used with a DBSL (dbsdbslib) that requires the following minimum SQLDBC runtime version:<br />7.6.06 &gt;= Build 22 for all available platforms<br /><br /></li>\r\n<li>SAP kernel Release 7.00 (including NW04s, NW 7.00 Support Package x, 6.45 J2EE and SCM 5.0)<br />The minimum SQLDBC runtime versions required by the DBSL (dbsdbslib) are:<br />7.6.06 &gt;= Build 22 for all available platforms<br /><br /></li>\r\n<li>SAP kernel Release 7.01 (including NW 7.00 Enhancement Package 1)<br />The minimum SQLDBC runtime versions required by the DBSL (dbsdbslib) are:<br />7.6.06 &gt;= Build 22 for all available platforms<br /><br /></li>\r\n<li>SAP kernel Release 7.10 (including NW05, NW 7.10 Support Package x and SCM 6.0)<br />The minimum SQLDBC runtime versions required by the DBSL (dbsdbslib) are:<br />7.6.06 &gt;= Build 22 for all available platforms<br /><br /></li>\r\n<li>SAP kernel Release 7.11 (including NW 7.11, NW 7.1 Enhancement Package x)<br />The minimum SQLDBC runtime versions required by the DBSL (dbsdbslib) are:<br />7.7.07 &gt;= Build 41 for all available platforms<br /><br /></li>\r\n<li>SAP kernel Release 7.2x (incl. 7.20 EXT, 7.21 EXT, NW 7.02, NW 7.03, NW 7.20, NW 7.3x)<br />The minimum SQLDBC runtime versions required by the DBSL (dbsdbslib) are:<br />7.7.07 &gt;= Build 41 for all available platforms<br />7.8.01 &gt;= Build 04 for AIX<br />7.8.02 &gt;= Build 17 for Linux AMD X86_64 and Microsoft Windows NT<br /><br /></li>\r\n<li>SAP kernel Release 7.40 (including NW 7.4 SP2, SP3, and SP4)<br />The minimum SQLDBC runtime versions required by the DBSL (dbsdbslib) are:<br />7.7.07 &gt;= Build 41 for all available platforms<br />7.8.01 &gt;= Build 04 for AIX.<br />7.8.02 &gt;= Build 17 for Linux AMD X86_64 and Microsoft Windows NT<br /><br /></li>\r\n<li>SAP kernel Release 7.41 (including NW 7.4 SP5)<br />The minimum SQLDBC runtime versions required by the DBSL (dbsdbslib) are:<br />7.7.07 &gt;= Build 41 for all available platforms except AIX.<br />7.8.01 &gt;= Build 04 for AIX.<br />7.8.02 &gt; = Build 17 for Linux AMD X86_64 Microsoft Windows NT<br /><br /></li>\r\n<li>SAP kernel Release 7.42 <br />The minimum SQLDBC runtime versions required by the DBSL (dbsdbslib) are:<br />7.9.08 &gt;= Build 18 for all available platforms<br /><br /></li>\r\n<li>SAP Kernel Release 7.45/7.49/7.53<br />The minimum SQLDBC runtime versions required by the DBSL (dbsdbslib) are:<br />7.9.08 &gt;= Build 31 for all available platforms<br /><br /></li>\r\n<li>SAP kernel Release 8.0x (incl. 8.03, 8.04, 8.05, NGAP)<br />The SQLDBC runtime versions required by the DBSL (dbsdbslib) are:<br />7.9.03 &gt;= Build 4 for all available platforms<br /><br /></li>\r\n</ul>\r\n<li>Does the SQLDBC runtime also have to be installed on the application server?<br /><br />Yes, SQLDBC runtime must be installed on all application servers and the database server. In general, we recommend that you always install the most current SQLDBC runtime.<br /><br /></li>\r\n<li>How can I determine which versions of the SQLDBC runtime are installed on a host?<br /><br />You can use the program <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">SDBREGVIEW</span> to determine which SQLDBC versions are installed on the relevant host.<br /><br /><span style=\"text-decoration: underline;\"><strong>On Unix platforms:</strong></span><em><br /></em>At operating system level, start: <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;sdbregview -l | grep -i SQLDBC&#xFEFF;</em></span><br />The system displays a list of the SQLDBC versions that are installed on the host.<br />Example:<br /><em><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#xFEFF;SQLDBC&#x00A0;&#x00A0;/program files/sdb/programs&#x00A0;&#x00A0;*********&#x00A0;&#x00A0;&#x00A0;&#x00A0; valid</span><br /><br /></em><span style=\"text-decoration: underline;\"><strong>On Microsoft Windows platforms:<br /></strong></span>At operating system level, start: <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;sdbregview -l | find /I \"SQLDBC\"&#xFEFF;</em></span><br />Example:<br /><em><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#xFEFF;SQLDBC&#x00A0;&#x00A0;c:/program files/sdb/programs&#x00A0;&#x00A0;*********&#x00A0;&#x00A0;32 bit valid</span><br /><br /></em></li>\r\n<li>How can I determine which version of the SQLDBC runtime is currently in use?<br /><br />The SQLDBC version that is currently being used by the SAP application is logged on each application server in the developer trace logs (<em>dev_w*</em> files). In transaction SM50, select a work process and choose&#x00A0;<em>Process -&gt; Trace -&gt; Display file</em> to go to the log. In the log, search for the following character strings:<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;C&#x00A0;&#x00A0;SQLDBC SDK Version :</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>C&#x00A0;&#x00A0;SQLDBC Library Version :</em></span><br /><em><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">C&#x00A0;&#x00A0;SQLDBC client runtime is</span><br /><br /></em>Example:<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;SQLDBC SDK Version : SQLDBC. H&#x00A0;&#x00A0;7.6.0&#x00A0;&#x00A0;&#x00A0;&#x00A0;BUILD 002-121-083-965</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>SQLDBC library Version : libSQLDBC 7.6.6&#x00A0;&#x00A0;&#x00A0;&#x00A0;BUILD 022-123-109-428</em></span><br /><em><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">SQLDBC client runtime is MaxDB 7.6.6.022 CL 109428</span><br /><br /></em>If you report an error to SAP, you provide the version specification in the customer incident.<br /><br /></li>\r\n<li>How can I determine which version an SAP MaxDB client library has?<br /><br />Switch to the directory that contains the library whose version you want to determine, for example <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;/sapdb/clients/&lt;SID&gt;/lib&#xFEFF;</em></span> (version &gt;= 7.8).<br /><br />Use the following command:&#x00A0;<em><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#xFEFF;sqlwhat &lt;library_name&gt; -i Build</span><br /><br /></em>Example:<br /><em><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#xFEFF;sqlwhat libSQLDBC76. so -i Build</span><br /></em>Output:<br /><em>:</em><br /><em><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#xFEFF;Rel. 7.6.6 Build: 022-123-241-261</span><br /></em>The version is displayed in the output.<br /><br /></li>\r\n<li>How do I install or upgrade the SQLDBC runtime?<br /><br />To install or upgrade the SQLDBC runtime, use SAP Note <a target=\"_blank\" href=\"/notes/649814\">649814</a>.<br /><br /></li>\r\n<li>How do I activate or deactivate the SQLDBC trace?<br /><br />An SQLDBC trace contains the SQL statements that are sent to the database core by the application, their parameters and the results. All SQLDBC traces contain profile and time stamp information, among other things.<br /><br />The trace files are stored in the directory <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;&lt;independent_data_path&gt;/wrk&#xFEFF;</em></span> (Microsoft Windows) or <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;&lt;user_home&gt;/.sdb&#xFEFF;</em></span> (UNIX/Linux) as <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;sqldbctrace-&lt;pid&gt;.prt&#xFEFF;</em></span> files. <em>&lt;pid&gt;</em> is the process ID. The files are cyclically overwritten.<br /><br />Use the tool <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">SQLDBC_CONS</span> to configure, start and stop the SQLDBC traces. Start <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">SQLDBC_CONS</span> without additional options, so that all SQLDBC trace options (and commands) are displayed.<br />You can also change the name and the path of the file. You can use the trace command <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">SHOW ALL</span> or <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">SHOW CONFIG</span> to display the current setting.<br />For more information about the SQLDBC trace, see the support guide (SAP Note <a target=\"_blank\" href=\"/notes/692274\">692274</a>).<br /><br />In Microsoft Windows, the R/3 processes usually run as <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;SAPService&lt;SID&gt;&#xFEFF;</em></span>. Therefore, you must specify the -u option when you activate the trace at operating system level: <em><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#xFEFF;sqldbc_cons -u SAPService&lt;SID&gt; TRACE SQL ON</span><br /></em>This is not required in the Database Assistant (transaction DB50) or in transaction SM49, since the environment of the <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;SAPService&lt;SID&gt;&#xFEFF;</em></span> already exists there.<br /><br />Note that for MDM (Master Data Management) installations, the user <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;SAPService&lt;SID&gt;&#xFEFF;</em></span> is entered in the group <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;SBD Operators&#xFEFF;</em></span> or the group for <em>local administrators</em>.<br /><br /></li>\r\n<li>What is the ODBC interface?<br /><br />To access the database system SAP MaxDB using the ODBC, you can use the SAP MaxDB ODBC driver. The SAP MaxDB ODBC driver allows you to access SAP MaxDB databases using the ODBC interface. You can use the SAP MaxDB ODBC driver on all operating systems that are supported by the database system.<br /><br /><br /><div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Operating system</strong></td>\r\n<td><strong>ODBC driver/ASCII</strong></td>\r\n<td><strong>ODBC driver/UNICODE</strong></td>\r\n</tr>\r\n<tr>\r\n<td>Microsoft Windows</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;sdbodbc.dll&#xFEFF;</em></span></td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;sdbodbcw.dll&#xFEFF;</em></span></td>\r\n</tr>\r\n<tr>\r\n<td>UNIX/Linux</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;libsdbodbc.a|so&#xFEFF;</em></span></td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;libsdbodbcw.a|so&#xFEFF;</em></span></td>\r\n</tr>\r\n</tbody>\r\n</table></div>Up to SAP MaxDB 7.6, the name of the ODBC driver was <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;sqlod32</em></span> (Microsoft Windows) or <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;libsqlod</em></span> (Unix/Linux).<br /> <br />The SAP MaxDB ODBC driver is a part of the SAP MaxDB software. The SAP MaxDB ODBC driver supports the complete ODBC SQL syntax. For detailed information about the ODBC function calls, see the <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;ODBC Programmer's Reference&#xFEFF;</em></span> documentation at <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;msdn.microsoft.com&#xFEFF;</em></span>.<br /><br />In order to use the SAP MaxDB ODBC driver, you have the following options:<br />Registering the SAP MaxDB ODBC driver in a driver manager. <br /><br />You can use the following driver managers:</li>\r\n<ul>\r\n<li>Microsoft Windows: Microsoft ODBC Driver Manager 3.52 or higher</li>\r\n<li>UNIX/Linux: unixODBC 2.0.9 or above, ODBC 3.0.5 or above</li>\r\n<li>UNIX/Linux: Link custom-developed ODBC applications directly with the SAP MaxDB ODBC driver<br /><br />Prerequisite:</li>\r\n<li>On Microsoft Windows, you require Microsoft Data Access Components (MDAC) Version 2.7 or higher. You have installed the SAP MaxDB ODBC driver.</li>\r\n<li>Under Unix/Linux, it is not necessary to install a driver manager. On Microsoft Windows, a driver manager is generally part of the operating system installation.<br /><br /></li>\r\n</ul>\r\n<li>What is the ODBC interface used for in the SAP environment?<br /><br />The SAP Content Server, which is a server component of the Knowledge Provider (Kpro), uses ODBC as an interface for SAP MaxDB.<br />The SAP MaxDB Database Analyzer requires ODBC in order to connect to the SAP MaxDB database.<br /><br /></li>\r\n<li>How can I determine which ODBC version is installed on the host?<br /><br />You can use the program <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">SDBREGVIEW</span> to determine which ODBC versions are installed on the relevant host.<br /><br /><span style=\"text-decoration: underline;\">On Unix platforms:<br /></span>At operating system level, start: <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;sdbregview -l | grep -i ODBC&#xFEFF;</em></span><br />The system displays the version of the ODBC driver that is installed on the host.<br />Example:<br /><em><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#xFEFF;ODBC&#x00A0;&#x00A0;/program files/sdb/programs&#x00A0;&#x00A0;*********&#x00A0;&#x00A0;&#x00A0;&#x00A0; valid</span><br /><br /></em><span style=\"text-decoration: underline;\">On Microsoft Windows platforms:<br /></span>At operating system level, start: <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;sdbregview -l | find /I \"ODBC\"&#xFEFF;</em></span><br />Example:<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;ODBC&#x00A0;&#x00A0;c:/program files/sdb/programs&#x00A0;&#x00A0;*********&#x00A0;&#x00A0;32 bit valid&#xFEFF;</em></span><br /><br />The ODBC library is linked statically for the SAP content server on UNIX, which means that it does not matter which ODBC driver version is installed on the host.<br /><br /></li>\r\n<li>How do I install or upgrade the SAP MaxDB ODBC driver?<br /><br />For information about how to install or upgrade the ODBC driver, refer to SAP Note <a target=\"_blank\" href=\"/notes/698915\">698915</a>.<br /><br /></li>\r\n<li>How can I create an SQL trace on the SAP content server?<br /><br />To obtain an SQL trace in the SAP Content Server environment, you activate the ODBC trace.<br />The ODBC version may differ from the database version and it depends on the SAP Content Server version in use.<br /><br />The ODBC trace must be activated on the server on which the Web server runs. Depending on the ODBC version you use, the following trace options are available:<ol style=\"list-style-type: lower-alpha;\">\r\n<li>ODBC driver 7.6 (Microsoft Windows only)<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;odbcreg&#xFEFF;</em></span>For more information, see SAP Note <a target=\"_blank\" href=\"/notes/1405031\">1405031</a>.</li>\r\n<li>ODBC driver 7.7 and higher<br />As of ODBC Version 7.7, you should preferably use <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;odbc_cons&#xFEFF;</em></span> to create the trace. For details, see SAP Note <a target=\"_blank\" href=\"/notes/1428709\">1428709</a>.</li>\r\n<li>If you cannot use the two options specified above, use the procedure described below. <br />However, note that this CANNOT currently be used for SAP Content Server 6.50. You must only proceed in accordance with SAP Note <a target=\"_blank\" href=\"/notes/1428709\">1428709</a> here.<br /><br />On Microsoft Windows, the ODBC trace (sqltrace) is activated with the help of the file <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">C<em>ontentServer.ini&#xFEFF;</em></span>, as described in SAP Note <a target=\"_blank\" href=\"/notes/329473\">329473</a>, via the relevant parameter (<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;sqltrace=1&#xFEFF;</em></span>). The file <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">C<em>ontentServer.ini&#xFEFF;</em></span> is located in the SAP Content Server installation directory.<br /><br />On UNIX, you activate the ODBC trace (sqltrace) using the file <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;cs.conf&#xFEFF;</em></span> and the parameter <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;SQLTrace=1&#xFEFF;</em></span>. You use the parameter <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;SQLTracePath=&lt;path&gt;&#xFEFF;</em></span> to determine the storage location of the trace file. The name of the trace file is fixed as <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;sqltrace_&lt;pid&gt;.pct&#xFEFF;</em></span>, whereby &lt;pid&gt; is automatically replaced by the process ID of the Web server. The system distinguishes between uppercase and lowercase. The file <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;cs.conf&#xFEFF;</em></span> is located in the SAP Content Server installation directory.<br /><br />Deactivate the ODBC trace by removing or commenting out the parameter (preceding semicolon ';').</li>\r\n</ol>\r\n<p style=\"padding-left: 30px;\">You can activate the trace for all repositories or for certain repositories only.<br />Recommended: Activate the trace only for the repository for which you want to analyze a problem using the SQL trace.<br />The Web server (Microsoft Windows: IIS; Unix: Apache) must be restarted after you activate the ODBC traces to start writing the trace.</p>\r\n</li>\r\n<li>What is the JDBC interface?<br /><br />You can use the SAP MaxDB JDBC driver and the SAP MaxDB Java classes to incorporate SAP MaxDB databases into Java applications (to execute SQL statements, for example). The SAP MaxDB JDBC driver and the SAP MaxDB Java classes are included in the software component JDBC.<br /><br /></li>\r\n<li>What is the JDBC interface used for in the SAP environment?<br /><br />The Java Engine of the Web Application Server (J2EE) requires the JDBC driver to communicate with the database.<br /><br /></li>\r\n<li>How can I determine which JDBC version is installed on the host?<br /><br />You can use the program <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">SDBREGVIEW</span> to determine which JDBC version is installed on the relevant host.<br /><br /><span style=\"text-decoration: underline;\">On Unix platforms:<br /></span>At operating system level, start: <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;sdbregview -l | grep -i JDBC&#xFEFF;</em></span><br />The system displays the version of the JDBC driver that is installed on the host.<br /><br />Example:<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;JDBC /program files/sdb/programs ********* valid&#xFEFF;</em></span><br />You can obtain the complete version specification (PTS:1149235) with the following command:<br /><em><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#xFEFF;/usr/runtime/jar/java -jar sapdbc.jar -V</span><br /><br /></em><span style=\"text-decoration: underline;\">On Microsoft Windows platforms:<br /></span>At operating system level, start <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;sdbregview -l | find /I \"JDBC\"&#xFEFF;</em></span><br /><br />Example:<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;JDBC c:/program files/sdb/programs ********* valid</em></span><br />You can obtain the complete version specification (PTS:1149235) with the following command:<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&lt;<em>drive&gt;\\sapdb\\programs\\runtime\\jar\\java -jar sapdbc. jar -V&#xFEFF;</em></span><br /><br /></li>\r\n<li>How do I install or upgrade the SAP MaxDB JDBC driver?<br /><br />The JDBC driver is a part of the SAP MaxDB client software package. To install or upgrade the JDBC driver, use SAP Note <a target=\"_blank\" href=\"/notes/649814\">649814</a>.<br /><br /></li>\r\n<li>How do I activate the JDBC trace?<br /><br />For information about how to activate the trace, refer to SAP Note <a target=\"_blank\" href=\"/notes/903018\">903018</a>.<br /><br /></li>\r\n<li>You want to increase the number of J2EE instances. What do I have to consider in the database?<br /><br />If new J2EE instances are connected to a database, then the requirement for parallel user sessions (database parameter MaxUserTasks) in the database increases. To correctly calculate <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;MaxUserTasks&#xFEFF;</em></span>, refer to SAP Note <a target=\"_blank\" href=\"/notes/1173395\">1173395</a>.<br /><br /></li>\r\n<li>Where can I find additional information about SAP MaxDB interfaces?<br /><br />In the glossary of the SAP MaxDB documentation (see SAP Note <a target=\"_blank\" href=\"/notes/767598\">767598</a>), you can find more information under the keyword \"interface\".<br />Alternatively, you can open the SAP MaxDB documentation via the SAP MaxDB community: <a target=\"_blank\" href=\"http://scn.sap.com/community/maxdb\">https://community.sap.com/topics/maxdb</a></li>\r\n</ol>\r\n<p>&#x00A0;</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-LVC (liveCache)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D019124)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D019124)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000822239/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000822239/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000822239/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000822239/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000822239/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000822239/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000822239/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000822239/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000822239/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "955670", "RefComponent": "BC-DB-DBI", "RefTitle": "DB multiconnect with SAP MaxDB as secondary database", "RefUrl": "/notes/955670"}, {"RefNumber": "949737", "RefComponent": "BC-ABA-SC", "RefTitle": "Generating screen & CUA loads in own LUW", "RefUrl": "/notes/949737"}, {"RefNumber": "903018", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/903018"}, {"RefNumber": "826037", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB/liveCache support", "RefUrl": "/notes/826037"}, {"RefNumber": "822271", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB client software", "RefUrl": "/notes/822271"}, {"RefNumber": "767598", "RefComponent": "BC-DB-SDB", "RefTitle": "Available SAP MaxDB documentation", "RefUrl": "/notes/767598"}, {"RefNumber": "698915", "RefComponent": "BC-DB-SDB", "RefTitle": "Update of SAP MaxDB ODBC driver for SAP Content Server", "RefUrl": "/notes/698915"}, {"RefNumber": "692274", "RefComponent": "BC-DB-LVC", "RefTitle": "Support guide for MaxDB/liveCache", "RefUrl": "/notes/692274"}, {"RefNumber": "649814", "RefComponent": "BC-DB-SDB", "RefTitle": "Installation/update of SAP MaxDB/liveCache client software", "RefUrl": "/notes/649814"}, {"RefNumber": "329473", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "Description of Content Server and Cache Server configuration file", "RefUrl": "/notes/329473"}, {"RefNumber": "1847852", "RefComponent": "BC-DB-SDB", "RefTitle": "Work process terminates with signal 11", "RefUrl": "/notes/1847852"}, {"RefNumber": "1716826", "RefComponent": "BC-CST", "RefTitle": "Usage of the downward compatible kernel 721 (EXT)", "RefUrl": "/notes/1716826"}, {"RefNumber": "1669684", "RefComponent": "BC-OP-SUN", "RefTitle": "SAP on Oracle Solaris 11", "RefUrl": "/notes/1669684"}, {"RefNumber": "1643799", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1643799"}, {"RefNumber": "1633731", "RefComponent": "BC-CST", "RefTitle": "Usage of the 7.20 Downward-Compatible Kernel", "RefUrl": "/notes/1633731"}, {"RefNumber": "1619726", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "FAQ: SAP MaxDB Content Server", "RefUrl": "/notes/1619726"}, {"RefNumber": "1428709", "RefComponent": "BC-DB-SDB", "RefTitle": "Creating ODBC trace as of ODBC driver 7.7 and higher", "RefUrl": "/notes/1428709"}, {"RefNumber": "1405031", "RefComponent": "BC-DB-SDB", "RefTitle": "Generate ODBC trace on Windows", "RefUrl": "/notes/1405031"}, {"RefNumber": "1173395", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB and liveCache configuration", "RefUrl": "/notes/1173395"}, {"RefNumber": "1037016", "RefComponent": "BC-DB-SDB", "RefTitle": "IBM i: MaxDB Client Support for IBM i application server", "RefUrl": "/notes/1037016"}, {"RefNumber": "1022330", "RefComponent": "BC-DB-SDB", "RefTitle": "ODBC applications no longer run after Upgrade >= 7.7", "RefUrl": "/notes/1022330"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2994060", "RefComponent": "BC-DB-SDB-CCM", "RefTitle": "SQL code: sql syntax error: incorrect syntax near \"dbm_version\" - NetWeaver", "RefUrl": "/notes/2994060 "}, {"RefNumber": "2513228", "RefComponent": "BC-DB-SDB", "RefTitle": "F4 search help dumps with DBSQL_INTERNAL_ERROR", "RefUrl": "/notes/2513228 "}, {"RefNumber": "3280408", "RefComponent": "BC-DB-SDB", "RefTitle": "SAP MaxDB ODBC driver: Connect fails (\"Codepage not supported\")", "RefUrl": "/notes/3280408 "}, {"RefNumber": "3002777", "RefComponent": "BC-DB-SDB", "RefTitle": "Installing the 32-bit ODBC driver on Microsoft Windows 64-bit", "RefUrl": "/notes/3002777 "}, {"RefNumber": "2823627", "RefComponent": "BC-DB-SDB", "RefTitle": "Performance deterioration in J2EE environment following implementation of new SAP MaxDB JDBC driver", "RefUrl": "/notes/2823627 "}, {"RefNumber": "2560335", "RefComponent": "BC-DB-SDB", "RefTitle": "JDBC - Error java.lang.NoSuchMethodError: java.util.regex.Pattern.quote(Ljava/lang/String;)Ljava/lang/String", "RefUrl": "/notes/2560335 "}, {"RefNumber": "826037", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB/liveCache support", "RefUrl": "/notes/826037 "}, {"RefNumber": "767598", "RefComponent": "BC-DB-SDB", "RefTitle": "Available SAP MaxDB documentation", "RefUrl": "/notes/767598 "}, {"RefNumber": "1669684", "RefComponent": "BC-OP-SUN", "RefTitle": "SAP on Oracle Solaris 11", "RefUrl": "/notes/1669684 "}, {"RefNumber": "1633731", "RefComponent": "BC-CST", "RefTitle": "Usage of the 7.20 Downward-Compatible Kernel", "RefUrl": "/notes/1633731 "}, {"RefNumber": "698915", "RefComponent": "BC-DB-SDB", "RefTitle": "Update of SAP MaxDB ODBC driver for SAP Content Server", "RefUrl": "/notes/698915 "}, {"RefNumber": "1173395", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB and liveCache configuration", "RefUrl": "/notes/1173395 "}, {"RefNumber": "1405031", "RefComponent": "BC-DB-SDB", "RefTitle": "Generate ODBC trace on Windows", "RefUrl": "/notes/1405031 "}, {"RefNumber": "1619726", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "FAQ: SAP MaxDB Content Server", "RefUrl": "/notes/1619726 "}, {"RefNumber": "649814", "RefComponent": "BC-DB-SDB", "RefTitle": "Installation/update of SAP MaxDB/liveCache client software", "RefUrl": "/notes/649814 "}, {"RefNumber": "1716826", "RefComponent": "BC-CST", "RefTitle": "Usage of the downward compatible kernel 721 (EXT)", "RefUrl": "/notes/1716826 "}, {"RefNumber": "1847852", "RefComponent": "BC-DB-SDB", "RefTitle": "Work process terminates with signal 11", "RefUrl": "/notes/1847852 "}, {"RefNumber": "903018", "RefComponent": "BC-DB-SDB", "RefTitle": "SAP MaxDB: JDBC trace", "RefUrl": "/notes/903018 "}, {"RefNumber": "1037016", "RefComponent": "BC-DB-SDB", "RefTitle": "IBM i: MaxDB Client Support for IBM i application server", "RefUrl": "/notes/1037016 "}, {"RefNumber": "949737", "RefComponent": "BC-ABA-SC", "RefTitle": "Generating screen & CUA loads in own LUW", "RefUrl": "/notes/949737 "}, {"RefNumber": "822271", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB client software", "RefUrl": "/notes/822271 "}, {"RefNumber": "329473", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "Description of Content Server and Cache Server configuration file", "RefUrl": "/notes/329473 "}, {"RefNumber": "692274", "RefComponent": "BC-DB-LVC", "RefTitle": "Support guide for MaxDB/liveCache", "RefUrl": "/notes/692274 "}, {"RefNumber": "1022330", "RefComponent": "BC-DB-SDB", "RefTitle": "ODBC applications no longer run after Upgrade >= 7.7", "RefUrl": "/notes/1022330 "}, {"RefNumber": "955670", "RefComponent": "BC-DB-DBI", "RefTitle": "DB multiconnect with SAP MaxDB as secondary database", "RefUrl": "/notes/955670 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}