{"Request": {"Number": "80279", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 454, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014515412017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000080279?language=E&token=E21001D79E89122386C8ADFECD07D4C5"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000080279", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000080279/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "80279"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 131}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "30.01.2000"}, "SAPComponentKey": {"_label": "Component", "value": "IS-OIL-BC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Please use component BC-UPG-ADDON"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oil", "value": "IS-OIL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-OIL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Please use component BC-UPG-ADDON", "value": "IS-OIL-BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-OIL-BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "80279 - Order of IS-OIL notes 2.0c/1 & 1.0c/1 on R/3 3.0D/2"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p><br />**********************************************************************<br />* WARNING: This is an IS-OIL-specific note. If you DON'T have IS-Oil *<br />* installed on your system, this note does not apply to you. If this *<br />* note is applied and you do not have IS-Oil installed, you could&#x00A0;&#x00A0;&#x00A0;&#x00A0;*<br />* cause serious damage to your system.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *<br />**********************************************************************<br /><br />To avoid conflicts between different oil fixes, we have written this common note, which describes the IS-OIL US/DS fixes available for those systems, which have already installed Hot Package 19 (Note 79716).<br />This note helps you to install the fixes in the correct sequence.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>IS-Oil, Industry Solution, SAPSERV, HP19 R/3 3.0D/2</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The transports/fixes mentioned here can only be applied to IS-OIL systems on Core R/3 Release 3.0D/2,&#x00A0;&#x00A0;i.e. AFTER you have already installed Hot Package 19 (note 79716) and its corresponding CRT in your IS-OIL system.<br /><br />If in the following list a transport request A is replaced by<br />transport request B, this means:<br />&#x00A0;&#x00A0;1. Ignore transport A and do NOT import it.<br />&#x00A0;&#x00A0;2. The right sequence for the import of transport B is given<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; in the list and it must NOT be imported at the point where<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; transport A is mentioned.<br />&#x00A0;&#x00A0;3. If you have already imported transport A just proceed with<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; importing further fixes and don&#x00B4;t forget to import B.<br /><br />If in the following list a transport request of a CRT (e.g. SAPKI3DC23)<br />is mentioned, proceed in the following way:<br />&#x00A0;&#x00A0;1. If not already carried out, apply those Hot Packages which<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; were released since the last CRT was installed and which had<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; no conflicts with IS-Oil. (i.e. no CRT exist for these Hot<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Packages).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Hot Packages without CRTs do not conflict with IS-Oil and<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; therefore they only have to be applied in the correct Hot<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Package sequence, e.g. HP21 must be installed before HP22.<br />&#x00A0;&#x00A0;2. Apply the Hot Package that corresponds to the CRT mentioned in<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; the list.<br />&#x00A0;&#x00A0;3. Apply the CRT itself.<br /><br />&#x00A0;&#x00A0;Example: SAPKI3DC23&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Aug 1997&#x00A0;&#x00A0;16:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT23<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; If you haven't already installed the Hot Packages<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 20, 21 and 22, you have to apply them now. Afterwards<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; install Hot Package 23 and then its CRT 23. Then<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; continue with applying SAPKI3X155.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;transport&#x00A0;&#x00A0;&#x00A0;&#x00A0;date&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;appl.area&#x00A0;&#x00A0;&#x00A0;&#x00A0; note<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;==========&#x00A0;&#x00A0;==================&#x00A0;&#x00A0;&#x00A0;&#x00A0;==========&#x00A0;&#x00A0;&#x00A0;&#x00A0;===========<br /></p> <UL><LI>SAPKI3X150&#x00A0;&#x00A0; 31.07.1997&#x00A0;&#x00A0;15:29&#x00A0;&#x00A0;&#x00A0;&#x00A0;IS-OIL&#x00A0;&#x00A0;80479<br />Mandatory transport for all IS-OIL customers. In this collective transport are all transport bundled together, which are listed in the transport sequence of Note 69146 AFTER CRT19-transport SAPKI3DC19.</LI></UL> <UL><LI>SAPKI3X151&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Aug 1997&#x00A0;&#x00A0;09:30&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;80482</LI></UL> <UL><LI>SAPKI3X152&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Aug 1997&#x00A0;&#x00A0;15:16&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;80589</LI></UL> <UL><LI>SAPKI3X153&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Aug 1997&#x00A0;&#x00A0;16:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;80623 replaced by &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI3X160</LI></UL> <UL><LI>SAPKI3X154&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Aug 1997&#x00A0;&#x00A0;12:51&#x00A0;&#x00A0;&#x00A0;&#x00A0;US&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;80407</LI></UL> <UL><LI>SAPKI3DC23&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Aug 1997&#x00A0;&#x00A0;16:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT23</LI></UL> <UL><LI>SAPKI3X155&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Aug 1997&#x00A0;&#x00A0;18:48&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;80855</LI></UL> <UL><LI>SAPKI3X156&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Aug 1997&#x00A0;&#x00A0;10:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;80813</LI></UL> <UL><LI>SAPKI3X157&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Aug 1997&#x00A0;&#x00A0;14:04&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;80945</LI></UL> <UL><LI>SAPKI3X158&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Aug 1997&#x00A0;&#x00A0;14:09&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;81011</LI></UL> <UL><LI>SAPKI3X159&#x00A0;&#x00A0; 11 Aug 1997&#x00A0;&#x00A0;08:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;81051</LI></UL> <UL><LI>SAPKI3X160&#x00A0;&#x00A0; 13 Aug 1997&#x00A0;&#x00A0;17:38&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;80623</LI></UL> <UL><LI>SAPKI3X161&#x00A0;&#x00A0; 14 Aug 1997&#x00A0;&#x00A0;13:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;81268</LI></UL> <UL><LI>SAPKI3X162&#x00A0;&#x00A0; 14 Aug 1997&#x00A0;&#x00A0;16:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;81390</LI></UL> <UL><LI>SAPKI3DC24&#x00A0;&#x00A0; 15.Aug 1997&#x00A0;&#x00A0;09:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT24</LI></UL> <UL><LI>SAPKI3X164&#x00A0;&#x00A0; 15 Aug 1997&#x00A0;&#x00A0;09:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;81417</LI></UL> <UL><LI>SAPKI3X163&#x00A0;&#x00A0; 18 Aug 1997&#x00A0;&#x00A0;09:09&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;81307</LI></UL> <UL><LI>SAPKI3X165&#x00A0;&#x00A0; 19 Aug 1997&#x00A0;&#x00A0;17:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;81634</LI></UL> <UL><LI>SAPKI3X166&#x00A0;&#x00A0; 20 Aug 1997&#x00A0;&#x00A0;14:20&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;81677</LI></UL> <UL><LI>SAPKI3X168&#x00A0;&#x00A0; 21 Aug 1997&#x00A0;&#x00A0;08:44&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 81837</LI></UL> <UL><LI>SAPKI3X171&#x00A0;&#x00A0; 21 Aug 1997&#x00A0;&#x00A0;09:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;81775</LI></UL> <UL><LI>SAPKI3X170&#x00A0;&#x00A0; 21 Aug 1997&#x00A0;&#x00A0;15:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;81721</LI></UL> <UL><LI>SAPKI3X172&#x00A0;&#x00A0; 21 Aug 1997&#x00A0;&#x00A0;17:10&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;81829</LI></UL> <UL><LI>SAPKI3X169&#x00A0;&#x00A0; 21 Aug 1997&#x00A0;&#x00A0;17:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 81840</LI></UL> <UL><LI>SAPKI3X167&#x00A0;&#x00A0; 21 Aug 1997&#x00A0;&#x00A0;18:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;81645</LI></UL> <UL><LI>SAPKI3X173&#x00A0;&#x00A0; 22 Aug 1997&#x00A0;&#x00A0;14:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;81895</LI></UL> <UL><LI>SAPKI3X174&#x00A0;&#x00A0; 22 Aug 1997&#x00A0;&#x00A0;15:16&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;81720</LI></UL> <UL><LI>SAPKI3X175&#x00A0;&#x00A0; 26 Aug 1997&#x00A0;&#x00A0;13:44&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 82020</LI></UL> <UL><LI>SAPKI3X176&#x00A0;&#x00A0; 26 Aug 1997&#x00A0;&#x00A0;14:19&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;82017</LI></UL> <UL><LI>SAPKI3X177&#x00A0;&#x00A0; 27 Aug 1997&#x00A0;&#x00A0;11:29&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;82107</LI></UL> <UL><LI>SAPKI3X178&#x00A0;&#x00A0; 27 Aug 1997&#x00A0;&#x00A0;14:40&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 82133</LI></UL> <UL><LI>SAPKI3X179&#x00A0;&#x00A0; 28 Aug 1997&#x00A0;&#x00A0;18:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;82217</LI></UL> <UL><LI>SAPKI3DC25&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Sep 1997&#x00A0;&#x00A0;14:31&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT25</LI></UL> <UL><LI>SAPKI3X180&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Sep 1997&#x00A0;&#x00A0;13:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;82718</LI></UL> <UL><LI>SAPKI3X181&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Sep 1997&#x00A0;&#x00A0;17:33&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;82903</LI></UL> <UL><LI>SAPKI3X182&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Sep 1997&#x00A0;&#x00A0;10:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;82763</LI></UL> <UL><LI>SAPKI3X183&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Sep 1997&#x00A0;&#x00A0;13:41&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;75620</LI></UL> <UL><LI>SAPKI3X184&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Sep 1997&#x00A0;&#x00A0;14:16&#x00A0;&#x00A0;&#x00A0;&#x00A0;US&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;83027</LI></UL> <UL><LI>SAPKI3X185&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Sep 1997&#x00A0;&#x00A0;14:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;US&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;83040</LI></UL> <UL><LI>SAPKI3X186&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Sep 1997&#x00A0;&#x00A0;14:18&#x00A0;&#x00A0;&#x00A0;&#x00A0;US&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;79730</LI></UL> <UL><LI>SAPKI3DC26&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Sep 1997&#x00A0;&#x00A0;16:30&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT26</LI></UL> <UL><LI>SAPKI3X187&#x00A0;&#x00A0; 10 Sep 1997&#x00A0;&#x00A0;11:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;83134</LI></UL> <UL><LI>SAPKI3X188&#x00A0;&#x00A0; 10 Sep 1997&#x00A0;&#x00A0;15:38&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;83202</LI></UL> <UL><LI>SAPKI3X189&#x00A0;&#x00A0; 10 Sep 1997&#x00A0;&#x00A0;16:29&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;82811</LI></UL> <UL><LI>SAPKI3X190&#x00A0;&#x00A0; 11 Sep 1997&#x00A0;&#x00A0;07:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;83168</LI></UL> <UL><LI>SAPKI3X191&#x00A0;&#x00A0; 11 Sep 1997&#x00A0;&#x00A0;08:10&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;83199</LI></UL> <UL><LI>SAPKI3X192&#x00A0;&#x00A0; 11 Sep 1997&#x00A0;&#x00A0;15:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 83277</LI></UL> <UL><LI>SAPKI3X193&#x00A0;&#x00A0; 11 Sep 1997&#x00A0;&#x00A0;16:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;83234</LI></UL> <UL><LI>SAPKI3X194&#x00A0;&#x00A0; 15 Sep 1997&#x00A0;&#x00A0;13:40&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 83400</LI></UL> <UL><LI>SAPKI3X196&#x00A0;&#x00A0; 16 Sep 1997&#x00A0;&#x00A0;16:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;83535 replaced by &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI3X243</LI></UL> <p><STRONG>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Caution: It is highly recommended to apply</STRONG><br /><STRONG>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI3X243 immediately, if you have</STRONG><br /><STRONG>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;already applied SAPKI3X196 .</STRONG></p> <UL><LI>SAPKI3X197&#x00A0;&#x00A0; 17 Sep 1997&#x00A0;&#x00A0;13:51&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;81969</LI></UL> <UL><LI>SAPKI3X195&#x00A0;&#x00A0; 17 Sep 1997&#x00A0;&#x00A0;14:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;83477</LI></UL> <UL><LI>SAPKI3X198&#x00A0;&#x00A0; 17 Sep 1997&#x00A0;&#x00A0;17:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;83641</LI></UL> <UL><LI>SAPKI3X199&#x00A0;&#x00A0; 19 Sep 1997&#x00A0;&#x00A0;10:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 83757</LI></UL> <UL><LI>SAPKI3DC27&#x00A0;&#x00A0; 23 Sep 1997&#x00A0;&#x00A0;18:26&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT27</LI></UL> <UL><LI>SAPKI3X200&#x00A0;&#x00A0; 24 Sep 1997&#x00A0;&#x00A0;17:03&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;83987</LI></UL> <UL><LI>SAPKI3X201&#x00A0;&#x00A0; 25 Sep 1997&#x00A0;&#x00A0;07:38&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;84069</LI></UL> <UL><LI>SAPKI3X202&#x00A0;&#x00A0; 25 Sep 1997&#x00A0;&#x00A0;12:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 84155 replaced by &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI3X207</LI></UL> <UL><LI>SAPKI3X203&#x00A0;&#x00A0; 25 Sep 1997&#x00A0;&#x00A0;14:13&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;84164</LI></UL> <UL><LI>SAPKI3X206&#x00A0;&#x00A0; 30 Sep 1997&#x00A0;&#x00A0;13:57&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;84461</LI></UL> <UL><LI>SAPKI3X207&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Oct 1997&#x00A0;&#x00A0;11:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 84155</LI></UL> <UL><LI>SAPKI3X208&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Oct 1997&#x00A0;&#x00A0;11:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;84478</LI></UL> <UL><LI>SAPKI3X209&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Oct 1997&#x00A0;&#x00A0;11:09&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;84885</LI></UL> <UL><LI>SAPKI3X210&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Oct 1997&#x00A0;&#x00A0;14:19&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;84911 replaced by &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI3X229</LI></UL> <UL><LI>SAPKI3X211&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Oct 1997&#x00A0;&#x00A0;17:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;84945</LI></UL> <UL><LI>SAPKI3X212&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Oct 1997&#x00A0;&#x00A0;09:47&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;84928</LI></UL> <UL><LI>SAPKI3X213&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Oct 1997&#x00A0;&#x00A0;09:14&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 85049</LI></UL> <UL><LI>SAPKI3X214&#x00A0;&#x00A0; 13 Oct 1997&#x00A0;&#x00A0;14:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;85283</LI></UL> <UL><LI>SAPKI3X215&#x00A0;&#x00A0; 13 Oct 1997&#x00A0;&#x00A0;16:19&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;85317</LI></UL> <UL><LI>SAPKI3X216&#x00A0;&#x00A0; 14 Oct 1997&#x00A0;&#x00A0;11:07&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;85383</LI></UL> <UL><LI>SAPKI3X217&#x00A0;&#x00A0; 14 Oct 1997&#x00A0;&#x00A0;14:20&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 85365</LI></UL> <UL><LI>SAPKI3X218&#x00A0;&#x00A0; 15 Oct 1997&#x00A0;&#x00A0;09:35&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;85370</LI></UL> <UL><LI>SAPKI3X219&#x00A0;&#x00A0; 17 Oct 1997&#x00A0;&#x00A0;12:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;85506</LI></UL> <UL><LI>SAPKI3X220&#x00A0;&#x00A0; 17 Oct 1997&#x00A0;&#x00A0;14:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;85697</LI></UL> <UL><LI>SAPKI3X221&#x00A0;&#x00A0; 21 Oct 1997&#x00A0;&#x00A0;10:48&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;85861 replaced by &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI3X240</LI></UL> <UL><LI>SAPKI3X222&#x00A0;&#x00A0; 21 Oct 1997&#x00A0;&#x00A0;11:40&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;85878</LI></UL> <UL><LI>SAPKI3X223&#x00A0;&#x00A0; 21 Oct 1997&#x00A0;&#x00A0;14:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;85897</LI></UL> <UL><LI>SAPKI3X225&#x00A0;&#x00A0; 24 Oct 1997&#x00A0;&#x00A0;08:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;86112</LI></UL> <UL><LI>SAPKI3X226&#x00A0;&#x00A0; 27 Oct 1997&#x00A0;&#x00A0;17:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;86377</LI></UL> <UL><LI>SAPKI3X227&#x00A0;&#x00A0; 28 Oct 1997&#x00A0;&#x00A0;15:44&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;86425</LI></UL> <UL><LI>SAPKI3X228&#x00A0;&#x00A0; 29 Oct 1997&#x00A0;&#x00A0;13:57&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;86507</LI></UL> <UL><LI>SAPKI3X229&#x00A0;&#x00A0; 29 Oct 1997&#x00A0;&#x00A0;14:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;84911</LI></UL> <UL><LI>SAPKI3X230&#x00A0;&#x00A0; 29 Oct 1997&#x00A0;&#x00A0;17:27&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;84290</LI></UL> <UL><LI>SAPKI3X231&#x00A0;&#x00A0;30 Oct 1997&#x00A0;&#x00A0;13:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;US&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;86310</LI></UL> <UL><LI>SAPKI3X233&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 Nov 1997&#x00A0;&#x00A0;09:06&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;86843</LI></UL> <UL><LI>SAPKI3X232&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 Nov 1997&#x00A0;&#x00A0;12:57&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;86726</LI></UL> <UL><LI>SAPKI3X234&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 Nov 1997&#x00A0;&#x00A0;12:35&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 86862</LI></UL> <UL><LI>SAPKI3X235&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 Nov 1997&#x00A0;&#x00A0;15:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;84911</LI></UL> <UL><LI>SAPKI3X236&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 Nov 1997&#x00A0;&#x00A0;15:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;86311</LI></UL> <UL><LI>SAPKI3X237&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 Nov 1997&#x00A0;&#x00A0;15:50&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;86912 replaced by &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI3X249</LI></UL> <UL><LI>SAPKI3X238&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 Nov 1997&#x00A0;&#x00A0;17:04&#x00A0;&#x00A0;&#x00A0;&#x00A0;US&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;86915</LI></UL> <UL><LI>SAPKI3X239&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Nov 1997&#x00A0;&#x00A0;08:10&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;86938</LI></UL> <UL><LI>SAPKI3X240&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Nov 1997&#x00A0;&#x00A0;09:28&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;85861</LI></UL> <UL><LI>SAPKI3X224&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Nov 1997&#x00A0;&#x00A0;09:35&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;86080</LI></UL> <UL><LI>SAPKI3X243&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Nov 1997&#x00A0;&#x00A0;09:47&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;83535</LI></UL> <UL><LI>SAPKI3X241&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Nov 1997&#x00A0;&#x00A0;13:59&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;86981</LI></UL> <UL><LI>SAPKI3X242&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Nov 1997&#x00A0;&#x00A0;15:35&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;87022</LI></UL> <UL><LI>SAPKI3X244&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Nov 1997&#x00A0;&#x00A0;13:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;87082</LI></UL> <UL><LI>SAPKI3X245&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Nov 1997&#x00A0;&#x00A0;14:20&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;87112</LI></UL> <UL><LI>SAPKI3X246&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Nov 1997&#x00A0;&#x00A0;08:57&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;87157</LI></UL> <UL><LI>SAPKI3X247&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Nov 1997&#x00A0;&#x00A0;12:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;87213</LI></UL> <UL><LI>SAPKI3X248&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Nov 1997&#x00A0;&#x00A0;11:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;87319</LI></UL> <UL><LI>SAPKI3X249&#x00A0;&#x00A0; 10 Nov 1997&#x00A0;&#x00A0;10:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;86912</LI></UL> <UL><LI>SAPKI3X250&#x00A0;&#x00A0; 10 Nov 1997&#x00A0;&#x00A0;14:35&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;87469</LI></UL> <UL><LI>SAPKI3X251&#x00A0;&#x00A0; 10 Nov 1997&#x00A0;&#x00A0;16:40&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;87429</LI></UL> <UL><LI>SAPKI3X253&#x00A0;&#x00A0; 11 Nov 1997&#x00A0;&#x00A0;15:51&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;87673</LI></UL> <UL><LI>SAPKI3X254&#x00A0;&#x00A0; 12 Nov 1997&#x00A0;&#x00A0;15:03&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;87801</LI></UL> <UL><LI>SAPKI3X256&#x00A0;&#x00A0; 12 Nov 1997&#x00A0;&#x00A0;16:18&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;87832</LI></UL> <UL><LI>SAPKI3X257&#x00A0;&#x00A0; 13 Nov 1997&#x00A0;&#x00A0;16:34&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;87960</LI></UL> <UL><LI>SAPKI3X258&#x00A0;&#x00A0;13 Nov 1997&#x00A0;&#x00A0;17:51&#x00A0;&#x00A0;&#x00A0;&#x00A0;US&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;80232</LI></UL> <UL><LI>SAPKI3X259&#x00A0;&#x00A0;13 Nov 1997&#x00A0;&#x00A0;18:18&#x00A0;&#x00A0;&#x00A0;&#x00A0;US&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;87849</LI></UL> <UL><LI>SAPKI3X260&#x00A0;&#x00A0;13 Nov 1997&#x00A0;&#x00A0;18:20&#x00A0;&#x00A0;&#x00A0;&#x00A0;US&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;86200</LI></UL> <UL><LI>SAPKI3X252&#x00A0;&#x00A0;11 Nov 1997&#x00A0;&#x00A0;17:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;US&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;87661</LI></UL> <UL><LI>SAPKI3X261&#x00A0;&#x00A0; 14 Nov 1997&#x00A0;&#x00A0;15:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;88052</LI></UL> <UL><LI>SAPKI3X262&#x00A0;&#x00A0; 14 Nov 1997&#x00A0;&#x00A0;15:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;88072</LI></UL> <UL><LI>SAPKI3X263&#x00A0;&#x00A0; 17 Nov 1997&#x00A0;&#x00A0;16:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;88182</LI></UL> <UL><LI>SAPKI3X264&#x00A0;&#x00A0; 19 Nov 1997&#x00A0;&#x00A0;10:42&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;88350</LI></UL> <UL><LI>SAPKI3X265&#x00A0;&#x00A0; 21 Nov 1997&#x00A0;&#x00A0;10:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;88499</LI></UL> <UL><LI>SAPKI3X266&#x00A0;&#x00A0; 21 Nov 1997&#x00A0;&#x00A0;15:10&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;67261 replaced by &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI3X401</LI></UL> <UL><LI>SAPKI3X267&#x00A0;&#x00A0; 24 Nov 1997&#x00A0;&#x00A0;16:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;88561</LI></UL> <UL><LI>SAPKI3X268&#x00A0;&#x00A0; 24 Nov 1997&#x00A0;&#x00A0;16:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;88559</LI></UL> <UL><LI>SAPKI3X269&#x00A0;&#x00A0; 27 Nov 1997&#x00A0;&#x00A0;09:09&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MRN&#x00A0;&#x00A0;89037</LI></UL> <UL><LI>SAPKI3DC29&#x00A0;&#x00A0; 27 Nov 1997&#x00A0;&#x00A0;14:04&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT29</LI></UL> <UL><LI>SAPKI3X270&#x00A0;&#x00A0; 28 Nov 1997&#x00A0;&#x00A0;09:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 89221</LI></UL> <UL><LI>SAPKI3X271&#x00A0;&#x00A0; 28 Nov 1997&#x00A0;&#x00A0;15:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;88364</LI></UL> <UL><LI>SAPKI3X272&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Dec 1997&#x00A0;&#x00A0;13:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 69999</LI></UL> <UL><LI>SAPKI3X273&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Dec 1997&#x00A0;&#x00A0;17:41&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;89420</LI></UL> <UL><LI>SAPKI3X274&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Dec 1997&#x00A0;&#x00A0;17:10&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;76703</LI></UL> <UL><LI>SAPKI3X276&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Dec 1997&#x00A0;&#x00A0;18:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;76703</LI></UL> <UL><LI>SAPKI3X277&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Dec 1997&#x00A0;&#x00A0;14:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;90030</LI></UL> <UL><LI>SAPKI3X275&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Dec 1997&#x00A0;&#x00A0;12:38&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 89205</LI></UL> <UL><LI>SAPKI3X278&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Dec 1997&#x00A0;&#x00A0;12:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;90136</LI></UL> <UL><LI>SAPKI3X279&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Dec 1997&#x00A0;&#x00A0;17:38&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;90227</LI></UL> <UL><LI>SAPKI3X280&#x00A0;&#x00A0; 10 Dec 1997&#x00A0;&#x00A0;08:42&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 89782</LI></UL> <UL><LI>SAPKI3X281&#x00A0;&#x00A0; 10 Dec 1997&#x00A0;&#x00A0;08:42&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 89784 replaced by &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI3X310</LI></UL> <UL><LI>SAPKI3X283&#x00A0;&#x00A0; 10 Dec 1997&#x00A0;&#x00A0;17:14&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;90371</LI></UL> <UL><LI>SAPKI3X284&#x00A0;&#x00A0; 10 Dec 1997&#x00A0;&#x00A0;17:31&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MRN&#x00A0;&#x00A0;90382</LI></UL> <UL><LI>SAPKI3X285&#x00A0;&#x00A0; 11 Dec 1997&#x00A0;&#x00A0;11:04&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;90421 replaced by &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI3X291</LI></UL> <UL><LI>SAPKI3X282&#x00A0;&#x00A0; 11 Dec 1997&#x00A0;&#x00A0;12:03&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;90333</LI></UL> <UL><LI>SAPKI3X286&#x00A0;&#x00A0; 11 Dec 1997&#x00A0;&#x00A0;14:49&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;90487</LI></UL> <UL><LI>SAPKI3X287&#x00A0;&#x00A0; 12 Dec 1997&#x00A0;&#x00A0;12:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;89508</LI></UL> <UL><LI>SAPKI3X289&#x00A0;&#x00A0; 15 Dec 1997&#x00A0;&#x00A0;16:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;90764</LI></UL> <UL><LI>SAPKI3X290&#x00A0;&#x00A0; 16 Dec 1997&#x00A0;&#x00A0;11:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;90333&#x00A0;&#x00A0;replaced by &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI3X401</LI></UL> <UL><LI>SAPKI3X291&#x00A0;&#x00A0; 16 Dec 1997&#x00A0;&#x00A0;13:33&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;90421</LI></UL> <UL><LI>SAPKI3X292&#x00A0;&#x00A0; 16 Dec 1997&#x00A0;&#x00A0;15:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;90912</LI></UL> <UL><LI>SAPKI3X288&#x00A0;&#x00A0; 16 Dec 1997&#x00A0;&#x00A0;17:02&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;90681</LI></UL> <UL><LI>SAPKI3X293&#x00A0;&#x00A0; 17 Dec 1997&#x00A0;&#x00A0;15:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;90966</LI></UL> <UL><LI>SAPKI3X294&#x00A0;&#x00A0; 17 Dec 1997&#x00A0;&#x00A0;16:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;90998</LI></UL> <UL><LI>SAPKI3X295&#x00A0;&#x00A0; 17 Dec 1997&#x00A0;&#x00A0;16:33&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;91089</LI></UL> <UL><LI>SAPKI3X296&#x00A0;&#x00A0; 18 Dec 1997&#x00A0;&#x00A0;10:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;91139</LI></UL> <UL><LI>SAPKI3X297&#x00A0;&#x00A0; 19 Dec 1997&#x00A0;&#x00A0;11:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;91346</LI></UL> <UL><LI>SAPKI3X298&#x00A0;&#x00A0; 19 Dec 1997&#x00A0;&#x00A0;14:16&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;91265</LI></UL> <UL><LI>SAPKI3X299&#x00A0;&#x00A0; 19 Dec 1997&#x00A0;&#x00A0;17:20&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;91379</LI></UL> <UL><LI>SAPKI3X300&#x00A0;&#x00A0; 22 Dec 1997&#x00A0;&#x00A0;10:50&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;91436</LI></UL> <UL><LI>SAPKI3X301&#x00A0;&#x00A0; 22 Dec 1997&#x00A0;&#x00A0;19:07&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;91379 and 90376</LI></UL> <UL><LI>SAPKI3X302&#x00A0;&#x00A0; 23 Dec 1997&#x00A0;&#x00A0;16:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;91553</LI></UL> <UL><LI>SAPKI3X303&#x00A0;&#x00A0; 23 Dec 1997&#x00A0;&#x00A0;16:10&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 91559</LI></UL> <UL><LI>SAPKI3X304&#x00A0;&#x00A0; 30 Dec 1997&#x00A0;&#x00A0;14:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;91673</LI></UL> <UL><LI>SAPKI3X306&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Jan 1998&#x00A0;&#x00A0;12:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;92049</LI></UL> <UL><LI>SAPKI3DC30&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Jan 1998&#x00A0;&#x00A0;11:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT30</LI></UL> <p>please note that the following correction transports can be found<br />on sapservX:~ftp/specific/isoil/30D_3/...<br /></p> <UL><LI>SAPKI3X307&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Jan 1998&#x00A0;&#x00A0;15:29&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;92254</LI></UL> <UL><LI>SAPKI3X308&#x00A0;&#x00A0; 13 Jan 1998&#x00A0;&#x00A0;15:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;92519</LI></UL> <UL><LI>SAPKI3X305&#x00A0;&#x00A0; 15 Jan 1998&#x00A0;&#x00A0;11:48&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;91853</LI></UL> <UL><LI>SAPKI3X311&#x00A0;&#x00A0; 15 Jan 1998&#x00A0;&#x00A0;13:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;92529</LI></UL> <UL><LI>SAPKI3X309&#x00A0;&#x00A0; 19 Jan 1998&#x00A0;&#x00A0;13:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 91775</LI></UL> <UL><LI>SAPKI3X310&#x00A0;&#x00A0; 19 Jan 1998&#x00A0;&#x00A0;15:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 89784</LI></UL> <UL><LI>SAPKI3DC31&#x00A0;&#x00A0; 19 Jan 1998&#x00A0;&#x00A0;16:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT31</LI></UL> <UL><LI>SAPKI3X312&#x00A0;&#x00A0; 20 Jan 1998&#x00A0;&#x00A0;11:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;93176</LI></UL> <UL><LI>SAPKI3X313&#x00A0;&#x00A0;20 Jan 1998&#x00A0;&#x00A0;16:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;US&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;93019</LI></UL> <UL><LI>SAPKI3DC32&#x00A0;&#x00A0; 20 Jan 1998&#x00A0;&#x00A0;18:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT32</LI></UL> <UL><LI>SAPKI3X314&#x00A0;&#x00A0; 21 Jan 1998&#x00A0;&#x00A0;12:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 93260</LI></UL> <UL><LI>SAPKI3X315&#x00A0;&#x00A0; 22 Jan 1998&#x00A0;&#x00A0;12:49&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 93144</LI></UL> <UL><LI>SAPKI3DC33&#x00A0;&#x00A0; 21 Jan 1998&#x00A0;&#x00A0;19:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT33</LI></UL> <UL><LI>SAPKI3X318&#x00A0;&#x00A0; 29 Jan 1998&#x00A0;&#x00A0;16:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;94071</LI></UL> <UL><LI>SAPKI3X320&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Feb 1998&#x00A0;&#x00A0;14:41&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;93860</LI></UL> <UL><LI>SAPKI3DC34&#x00A0;&#x00A0; 06 Feb 1998&#x00A0;&#x00A0;10:30&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT34&#x00A0;&#x00A0; 93554</LI></UL> <UL><LI>SAPKI3X316&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Feb 1998&#x00A0;&#x00A0;10:35&#x00A0;&#x00A0;&#x00A0;&#x00A0;US / DS 93554</LI></UL> <UL><LI>SAPKI3X317&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Feb 1998&#x00A0;&#x00A0;10:35&#x00A0;&#x00A0;&#x00A0;&#x00A0;US / DS 93554</LI></UL> <UL><LI>SAPKI3X322&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Feb 1998&#x00A0;&#x00A0;15:03&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;94781</LI></UL> <UL><LI>SAPKI3X323&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Feb 1998&#x00A0;&#x00A0;17:02&#x00A0;&#x00A0;&#x00A0;&#x00A0;US&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;94941</LI></UL> <UL><LI>SAPKI3X324&#x00A0;&#x00A0; 12 Feb 1998&#x00A0;&#x00A0;08:16&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;95076</LI></UL> <UL><LI>SAPKI3X325&#x00A0;&#x00A0; 12 Feb 1998&#x00A0;&#x00A0;15:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;95316</LI></UL> <UL><LI>SAPKI3X326&#x00A0;&#x00A0; 13 Feb 1998&#x00A0;&#x00A0;11:07&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;95489</LI></UL> <UL><LI>SAPKI3X327&#x00A0;&#x00A0; 13 Feb 1998&#x00A0;&#x00A0;13:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;95543</LI></UL> <UL><LI>SAPKI3X328&#x00A0;&#x00A0; 13 Feb 1998&#x00A0;&#x00A0;15:13&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;95542</LI></UL> <UL><LI>SAPKI3X329&#x00A0;&#x00A0; 19 Feb 1998&#x00A0;&#x00A0;10:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 96045</LI></UL> <UL><LI>SAPKI3X331&#x00A0;&#x00A0; 23 Feb 1998&#x00A0;&#x00A0;10:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;96285</LI></UL> <UL><LI>SAPKI3X332&#x00A0;&#x00A0; 23 Feb 1998&#x00A0;&#x00A0;16:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;96322</LI></UL> <UL><LI>SAPKI3X333&#x00A0;&#x00A0; 24 Feb 1998&#x00A0;&#x00A0;09:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;96370</LI></UL> <UL><LI>SAPKI3X334&#x00A0;&#x00A0; 25 Feb 1998&#x00A0;&#x00A0;14:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;96591</LI></UL> <UL><LI>SAPKI3X335&#x00A0;&#x00A0; 26 Feb 1998&#x00A0;&#x00A0;11:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/&#x00A0;&#x00A0;&#x00A0;&#x00A0; 96434</LI></UL> <UL><LI>SAPKI3X336&#x00A0;&#x00A0; 26 Feb 1998&#x00A0;&#x00A0;14:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;96662</LI></UL> <UL><LI>SAPKI3X337&#x00A0;&#x00A0; 26 Feb 1998&#x00A0;&#x00A0;15:35&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;96740</LI></UL> <UL><LI>SAPKI3X339&#x00A0;&#x00A0; 26 Feb 1998&#x00A0;&#x00A0;17:28&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;96761</LI></UL> <UL><LI>SAPKI3X338&#x00A0;&#x00A0; 26 Feb 1998&#x00A0;&#x00A0;17:47&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;67261&#x00A0;&#x00A0;replaced by &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI3X401</LI></UL> <UL><LI>SAPKI3X330&#x00A0;&#x00A0; 27 Feb 1998&#x00A0;&#x00A0;00:31&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;96138</LI></UL> <UL><LI>SAPKI3DC35&#x00A0;&#x00A0; 27 Feb 1998&#x00A0;&#x00A0;08:10&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT35</LI></UL> <UL><LI>SAPKI3X340&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Mar 1998&#x00A0;&#x00A0;17:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;97263</LI></UL> <UL><LI>SAPKI3X342&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Mar 1998&#x00A0;&#x00A0;09:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;67261&#x00A0;&#x00A0;replaced by &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI3X401</LI></UL> <UL><LI>SAPKI3X343&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Mar 1998&#x00A0;&#x00A0;15:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;97368</LI></UL> <UL><LI>SAPKI3X344&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Mar 1998&#x00A0;&#x00A0;17:48&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;97747</LI></UL> <UL><LI>SAPKI3X345&#x00A0;&#x00A0; 10 Mar 1998&#x00A0;&#x00A0;10:03&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;97726</LI></UL> <UL><LI>SAPKI3X346&#x00A0;&#x00A0; 11 Mar 1998&#x00A0;&#x00A0;14:48&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;98005</LI></UL> <UL><LI>SAPKI3X347&#x00A0;&#x00A0; 17 Mar 1998&#x00A0;&#x00A0;14:34&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;98534</LI></UL> <UL><LI>SAPKI3X348&#x00A0;&#x00A0;18 Mar 1998&#x00A0;&#x00A0;12:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;US&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;98301</LI></UL> <UL><LI>SAPKI3X349&#x00A0;&#x00A0; 18 Mar 1998&#x00A0;&#x00A0;12:47&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;98550</LI></UL> <UL><LI>SAPKI3DC36&#x00A0;&#x00A0; 18 Mar 1998&#x00A0;&#x00A0;15:12&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT36</LI></UL> <UL><LI>SAPKI3X350&#x00A0;&#x00A0; 18 Mar 1998&#x00A0;&#x00A0;16:31&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;98767</LI></UL> <UL><LI>SAPKI3X341&#x00A0;&#x00A0; 18 Mar 1998&#x00A0;&#x00A0;18:06&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;96138</LI></UL> <UL><LI>SAPKI3X351&#x00A0;&#x00A0; 19 Mar 1998&#x00A0;&#x00A0;13:10&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;98113</LI></UL> <UL><LI>SAPKI3X352&#x00A0;&#x00A0; 19 Mar 1998&#x00A0;&#x00A0;18:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 98946</LI></UL> <UL><LI>SAPKI3X353&#x00A0;&#x00A0; 25 Mar 1998&#x00A0;&#x00A0;14:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;99423</LI></UL> <UL><LI>SAPKI3X355&#x00A0;&#x00A0;26 Mar 1998&#x00A0;&#x00A0;10:27&#x00A0;&#x00A0;&#x00A0;&#x00A0;US&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;99355</LI></UL> <UL><LI>SAPKI3X357&#x00A0;&#x00A0; 26 Mar 1998&#x00A0;&#x00A0;17:29&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;99622</LI></UL> <UL><LI>SAPKI3X358&#x00A0;&#x00A0; 27 Mar 1998&#x00A0;&#x00A0;09:06&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;67261&#x00A0;&#x00A0;replaced by &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI3X401</LI></UL> <UL><LI>SAPKI3X359&#x00A0;&#x00A0; 27 Mar 1998&#x00A0;&#x00A0;15:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;99661</LI></UL> <UL><LI>SAPKI3X360&#x00A0;&#x00A0; 31 Mar 1998&#x00A0;&#x00A0;16:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;99944</LI></UL> <UL><LI>SAPKI3X361&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Apr 1998&#x00A0;&#x00A0;11:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 100072</LI></UL> <UL><LI>SAPKI3X362&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Apr 1998&#x00A0;&#x00A0;10:59&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;100107</LI></UL> <UL><LI>SAPKI3X363&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 Apr 1998&#x00A0;&#x00A0;10:34&#x00A0;&#x00A0;&#x00A0;&#x00A0;US&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;100288 replaced by &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI3X377</LI></UL> <UL><LI>SAPKI3X364&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Apr 1998&#x00A0;&#x00A0;15:47&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;100355</LI></UL> <UL><LI>SAPKI3X365&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Apr 1998&#x00A0;&#x00A0;16:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 100555</LI></UL> <UL><LI>SAPKI3X366&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Apr 1998&#x00A0;&#x00A0;09:33&#x00A0;&#x00A0;&#x00A0;&#x00A0;US&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;100471</LI></UL> <UL><LI>SAPKI3X367&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Apr 1998&#x00A0;&#x00A0;14:51&#x00A0;&#x00A0;&#x00A0;&#x00A0;US&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;100288 replaced by &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI3X377</LI></UL> <UL><LI>SAPKI3DC37&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Apr 1998&#x00A0;&#x00A0;14:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT37</LI></UL> <UL><LI>SAPKI3X368&#x00A0;&#x00A0; 14 Apr 1998&#x00A0;&#x00A0;12:42&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0100909</LI></UL> <UL><LI>SAPKI3X369&#x00A0;&#x00A0; 15 Apr 1998&#x00A0;&#x00A0;16:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0101141</LI></UL> <UL><LI>SAPKI3X370&#x00A0;&#x00A0; 16 Apr 1998&#x00A0;&#x00A0;14:19&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0101267</LI></UL> <UL><LI>SAPKI3X371&#x00A0;&#x00A0; 17 Apr 1998&#x00A0;&#x00A0;12:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0101321 replaced by &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI3X390</LI></UL> <UL><LI>SAPKI3X372&#x00A0;&#x00A0; 20 Apr 1998&#x00A0;&#x00A0;15:29&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0101484 replaced by &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI3X376</LI></UL> <UL><LI>SAPKI3X373&#x00A0;&#x00A0; 20 Apr 1998&#x00A0;&#x00A0;16:03&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0101238</LI></UL> <UL><LI>SAPKI3X375&#x00A0;&#x00A0; 21 Apr 1998&#x00A0;&#x00A0;16:40&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0100090 replaced by</LI></UL> <UL><LI>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI3X385</LI></UL> <UL><LI>SAPKI3X376&#x00A0;&#x00A0; 22 Apr 1998&#x00A0;&#x00A0;11:13&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0101484</LI></UL> <UL><LI>SAPKI3X377&#x00A0;&#x00A0;22 Apr 1998&#x00A0;&#x00A0;12:06&#x00A0;&#x00A0;&#x00A0;&#x00A0;US&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0100288</LI></UL> <UL><LI>SAPKI3X378&#x00A0;&#x00A0;28 Apr 1998&#x00A0;&#x00A0;09:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;US&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0102185</LI></UL> <UL><LI>SAPKI3X379&#x00A0;&#x00A0;28 Apr 1998&#x00A0;&#x00A0;15:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;US&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0100381</LI></UL> <UL><LI>SAPKI3X380&#x00A0;&#x00A0;28 Apr 1998&#x00A0;&#x00A0;15:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;US&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0102227</LI></UL> <UL><LI>SAPKI3X381&#x00A0;&#x00A0; 28 Apr 1998&#x00A0;&#x00A0;17:31&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0102306</LI></UL> <UL><LI>SAPKI3X382&#x00A0;&#x00A0; 29 Apr 1998&#x00A0;&#x00A0;08:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0102368</LI></UL> <UL><LI>SAPKI3X383&#x00A0;&#x00A0; 29 Apr 1998&#x00A0;&#x00A0;14:26&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0102439</LI></UL> <UL><LI>SAPKI3X384&#x00A0;&#x00A0; 30 Apr 1998&#x00A0;&#x00A0;08:02&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0102509</LI></UL> <UL><LI>SAPKI3DC38&#x00A0;&#x00A0; 30 Apr 1998&#x00A0;&#x00A0;08:03&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT38&#x00A0;&#x00A0; 0102975</LI></UL> <UL><LI>SAPKI3X388&#x00A0;&#x00A0; 30 Apr 1998&#x00A0;&#x00A0;08:04&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ US&#x00A0;&#x00A0;0102975</LI></UL> <UL><LI>SAPKI3X385&#x00A0;&#x00A0; 30 Apr 1998&#x00A0;&#x00A0;11:19&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0100090</LI></UL> <UL><LI>SAPKI3X386&#x00A0;&#x00A0; 30 Apr 1998&#x00A0;&#x00A0;11:28&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 0102529</LI></UL> <UL><LI>SAPKI3X387&#x00A0;&#x00A0;30 Apr 1998&#x00A0;&#x00A0;11:38&#x00A0;&#x00A0;&#x00A0;&#x00A0;US&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0102523</LI></UL> <UL><LI>SAPKI3X389&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 May 1998&#x00A0;&#x00A0;09:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0102965</LI></UL> <UL><LI>SAPKI3X390&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 May 1998&#x00A0;&#x00A0;13:34&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0101321</LI></UL> <UL><LI>SAPKI3X391&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 May 1998&#x00A0;&#x00A0;09:35&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0103199</LI></UL> <UL><LI>SAPKI3X392&#x00A0;&#x00A0; 12 May 1998&#x00A0;&#x00A0;15:57&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0103578 replaced by &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI3X393</LI></UL> <UL><LI>SAPKI3X393&#x00A0;&#x00A0; 13 May 1998&#x00A0;&#x00A0;16:48&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0103578</LI></UL> <UL><LI>SAPKI3X394&#x00A0;&#x00A0; 14 May 1998&#x00A0;&#x00A0;09:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0103802</LI></UL> <UL><LI>SAPKI3X395&#x00A0;&#x00A0; 14 May 1998&#x00A0;&#x00A0;09:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0103689</LI></UL> <UL><LI>SAPKI3X396&#x00A0;&#x00A0; 15 May 1998&#x00A0;&#x00A0;09:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0103968 replaced by &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI3X401</LI></UL> <UL><LI>SAPKI3X397&#x00A0;&#x00A0; 15 May 1998&#x00A0;&#x00A0;09:02&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0103969</LI></UL> <UL><LI>SAPKI3X395&#x00A0;&#x00A0; 18 May 1998&#x00A0;&#x00A0;14:12&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 0103689</LI></UL> <UL><LI>SAPKI3X398&#x00A0;&#x00A0; 18 May 1998&#x00A0;&#x00A0;18:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0104186</LI></UL> <UL><LI>SAPKI3X399&#x00A0;&#x00A0;18 May 1998&#x00A0;&#x00A0;18:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;US&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0104168</LI></UL> <UL><LI>SAPKI3X400&#x00A0;&#x00A0; 20 May 1998&#x00A0;&#x00A0;16:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 0101910</LI></UL> <UL><LI>SAPKI3X402&#x00A0;&#x00A0; 22 May 1998&#x00A0;&#x00A0;11:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0104536</LI></UL> <UL><LI>SAPKI3X401&#x00A0;&#x00A0; 25 May 1998&#x00A0;&#x00A0;08:14&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261 replaced by</LI></UL> <UL><LI>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI3X406</LI></UL> <UL><LI>SAPKI3X403&#x00A0;&#x00A0; 26 May 1998&#x00A0;&#x00A0;11:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0104300</LI></UL> <UL><LI>SAPKI3X404&#x00A0;&#x00A0; 28 May 1998&#x00A0;&#x00A0;14:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0105023</LI></UL> <UL><LI>SAPKI3X405&#x00A0;&#x00A0; 29 May 1998&#x00A0;&#x00A0;11:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0105255</LI></UL> <UL><LI>SAPKI3X407&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Jun 1998&#x00A0;&#x00A0;15:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0105799</LI></UL> <UL><LI>SAPKI3X406&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Jun 1998&#x00A0;&#x00A0;16:09&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261 replaced by</LI></UL> <UL><LI>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI3X457</LI></UL> <UL><LI>SAPKI3X408&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Jun 1998&#x00A0;&#x00A0;16:18&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0105758</LI></UL> <UL><LI>SAPKI3X409&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Jun 1998&#x00A0;&#x00A0;17:06&#x00A0;&#x00A0;&#x00A0;&#x00A0;US&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0105336</LI></UL> <UL><LI>SAPKI3X410&#x00A0;&#x00A0; 10 Jun 1998&#x00A0;&#x00A0;13:12&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0106052</LI></UL> <UL><LI>SAPKI3X411&#x00A0;&#x00A0; 12 Jun 1998&#x00A0;&#x00A0;16:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0101484</LI></UL> <UL><LI>SAPKI3X412&#x00A0;&#x00A0; 16 Jun 1998&#x00A0;&#x00A0;13:47&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0106784</LI></UL> <UL><LI>SAPKI3X413&#x00A0;&#x00A0; 19 Jun 1998&#x00A0;&#x00A0;13:34&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 0107019</LI></UL> <UL><LI>SAPKI3X414&#x00A0;&#x00A0; 22 Jun 1998&#x00A0;&#x00A0;10:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0107344</LI></UL> <UL><LI>SAPKI3X416&#x00A0;&#x00A0; 23 Jun 1998&#x00A0;&#x00A0;14:12&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0107436</LI></UL> <UL><LI>SAPKI3X417&#x00A0;&#x00A0; 25 Jun 1998&#x00A0;&#x00A0;12:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0107800</LI></UL> <UL><LI>SAPKI3X418&#x00A0;&#x00A0; 26 Jun 1998&#x00A0;&#x00A0;11:14&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0107992</LI></UL> <UL><LI>SAPKI3X419&#x00A0;&#x00A0; 29 Jun 1998&#x00A0;&#x00A0;12:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0108122</LI></UL> <UL><LI>SAPKI3X420&#x00A0;&#x00A0; 30 Jun 1998&#x00A0;&#x00A0;17:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0108368</LI></UL> <UL><LI>SAPKI3X421&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Jul 1998&#x00A0;&#x00A0;16:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0108671</LI></UL> <UL><LI>SAPKI3X422&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Jul 1998&#x00A0;&#x00A0;16:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0109055</LI></UL> <UL><LI>SAPKI3X423&#x00A0;&#x00A0; 10 Jul 1998&#x00A0;&#x00A0;14:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0109531</LI></UL> <UL><LI>SAPKI3X424&#x00A0;&#x00A0; 10 Jul 1998&#x00A0;&#x00A0;16:44&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0109572</LI></UL> <UL><LI>SAPKI3X425&#x00A0;&#x00A0; 14 Jul 1998&#x00A0;&#x00A0;14:41&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0109845</LI></UL> <UL><LI>SAPKI3X426&#x00A0;&#x00A0; 20 Jul 1998&#x00A0;&#x00A0;16:51&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 0110011</LI></UL> <UL><LI>SAPKI3X427&#x00A0;&#x00A0; 21 Jul 1998&#x00A0;&#x00A0;16:04&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0102179</LI></UL> <UL><LI>SAPKI3X428&#x00A0;&#x00A0; 29 Jul 1998&#x00A0;&#x00A0;09:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0111514</LI></UL> <UL><LI>SAPKI3DC39&#x00A0;&#x00A0; 30 Jul 1998&#x00A0;&#x00A0;08:03&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT39&#x00A0;&#x00A0; 0112851</LI></UL> <UL><LI>SAPKI3X429&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Aug 1998&#x00A0;&#x00A0;16:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0112521</LI></UL> <UL><LI>SAPKI3X430&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Aug 1998&#x00A0;&#x00A0;16:27&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0112636</LI></UL> <UL><LI>SAPKI3X431&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Aug 1998&#x00A0;&#x00A0;16:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0112717</LI></UL> <UL><LI>SAPKI3X432&#x00A0;&#x00A0; 12 Aug 1998&#x00A0;&#x00A0;17:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261</LI></UL> <UL><LI>SAPKI3X433&#x00A0;&#x00A0;14 Aug 1998&#x00A0;&#x00A0;15:04&#x00A0;&#x00A0;&#x00A0;&#x00A0;US&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0113639</LI></UL> <UL><LI>SAPKI3X434&#x00A0;&#x00A0; 18 Aug 1998&#x00A0;&#x00A0;10:49&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0113948</LI></UL> <UL><LI>SAPKI3X435&#x00A0;&#x00A0; 18 Aug 1998&#x00A0;&#x00A0;15:27&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0112217</LI></UL> <UL><LI>SAPKI3X436&#x00A0;&#x00A0; 18 Aug 1998&#x00A0;&#x00A0;16:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0114060</LI></UL> <UL><LI>SAPKI3X437&#x00A0;&#x00A0; 19 Aug 1998&#x00A0;&#x00A0;13:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0113546</LI></UL> <UL><LI>SAPKI3X438&#x00A0;&#x00A0;24 Aug 1998&#x00A0;&#x00A0;15:40&#x00A0;&#x00A0;&#x00A0;&#x00A0;US&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0114684</LI></UL> <UL><LI>SAPKI3X439&#x00A0;&#x00A0; 25 Aug 1998&#x00A0;&#x00A0;09:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0114800</LI></UL> <UL><LI>SAPKI3X440&#x00A0;&#x00A0; 28 Aug 1998&#x00A0;&#x00A0;08:14&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0115290</LI></UL> <UL><LI>SAPKI3X441&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Sep 1998&#x00A0;&#x00A0;12:47&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261</LI></UL> <UL><LI>SAPKI3X442&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 Sep 1998&#x00A0;&#x00A0;17:13&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0116116 repl. by SAPKI3X485</LI></UL> <UL><LI>SAPKI3X443&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Sep 1998&#x00A0;&#x00A0;10:03&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0114800</LI></UL> <UL><LI>SAPKI3X444&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Sep 1998&#x00A0;&#x00A0;14:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0112567</LI></UL> <UL><LI>SAPKI3X445&#x00A0;&#x00A0; 14 Sep 1998&#x00A0;&#x00A0;14:20&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0117116</LI></UL> <UL><LI>SAPKI3X446&#x00A0;&#x00A0; 17 Sep 1998&#x00A0;&#x00A0;13:35&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0117696</LI></UL> <UL><LI>SAPKI3X447&#x00A0;&#x00A0; 25 Sep 1998&#x00A0;&#x00A0;15:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0118708</LI></UL> <UL><LI>SAPKI3X448&#x00A0;&#x00A0; 28 Sep 1998&#x00A0;&#x00A0;11:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0118774</LI></UL> <UL><LI>SAPKI3X449&#x00A0;&#x00A0; 29 Sep 1998&#x00A0;&#x00A0;18:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261</LI></UL> <UL><LI>SAPKI3X450&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Oct 1998&#x00A0;&#x00A0;11:38&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0119913</LI></UL> <UL><LI>SAPKI3X451&#x00A0;&#x00A0; 13 Oct 1998&#x00A0;&#x00A0;10:42&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0120414</LI></UL> <UL><LI>SAPKI3X452&#x00A0;&#x00A0; 21 Oct 1998&#x00A0;&#x00A0;14:14&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0122334</LI></UL> <UL><LI>SAPKI3X454&#x00A0;&#x00A0; 22 Oct 1998&#x00A0;&#x00A0;11:34&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0122420</LI></UL> <UL><LI>SAPKI3X455&#x00A0;&#x00A0; 22 Oct 1998&#x00A0;&#x00A0;15:48&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0122490</LI></UL> <UL><LI>SAPKI3X456&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Nov 1998&#x00A0;&#x00A0;10:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0125113 replaced by</LI></UL> <UL><LI>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI3X461</LI></UL> <UL><LI>SAPKI3X457&#x00A0;&#x00A0; 10 Nov 1998&#x00A0;&#x00A0;18:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261</LI></UL> <UL><LI>SAPKI3X458&#x00A0;&#x00A0; 12 Nov 1998&#x00A0;&#x00A0;16:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0125753</LI></UL> <UL><LI>SAPKI3X459&#x00A0;&#x00A0; 24 Nov 1998&#x00A0;&#x00A0;10:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0127367</LI></UL> <UL><LI>SAPKI3X460&#x00A0;&#x00A0; 25 Nov 1998&#x00A0;&#x00A0;08:38&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0127627</LI></UL> <UL><LI>SAPKI3X461&#x00A0;&#x00A0; 25 Nov 1998&#x00A0;&#x00A0;17:42&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0125113</LI></UL> <UL><LI>SAPKI3X462&#x00A0;&#x00A0; 26 Nov 1998&#x00A0;&#x00A0;13:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0109340</LI></UL> <UL><LI>SAPKI3X463&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Dec 1998&#x00A0;&#x00A0;11:16&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0128829</LI></UL> <UL><LI>SAPKI3X464&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Dec 1998&#x00A0;&#x00A0;14:13&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0128868</LI></UL> <UL><LI>SAPKI3X465&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 Dec 1998&#x00A0;&#x00A0;11:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0128957 repl. by SAPKI3X474</LI></UL> <UL><LI>SAPKI3X466&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Dec 1998&#x00A0;&#x00A0;16:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0129344</LI></UL> <UL><LI>SAPKI3X467&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Dec 1998&#x00A0;&#x00A0;18:02&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0129849</LI></UL> <UL><LI>SAPKI3X468&#x00A0;&#x00A0; 11 Dec 1998&#x00A0;&#x00A0;11:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0130326</LI></UL> <UL><LI>SAPKI3X469&#x00A0;&#x00A0; 11 Dec 1998&#x00A0;&#x00A0;14:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0130238</LI></UL> <UL><LI>SAPKI3X470&#x00A0;&#x00A0; 14 Dec 1998&#x00A0;&#x00A0;12:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0130541</LI></UL> <UL><LI>SAPKI3X471&#x00A0;&#x00A0; 18 Dec 1998&#x00A0;&#x00A0;07:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0131434</LI></UL> <UL><LI>SAPKI3X473&#x00A0;&#x00A0; 29 Dec 1998&#x00A0;&#x00A0;07:31&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0132370</LI></UL> <UL><LI>SAPKI3X474&#x00A0;&#x00A0; 13 Jan 1999&#x00A0;&#x00A0;12:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0128957</LI></UL> <UL><LI>SAPKI3X475&#x00A0;&#x00A0; 13 Jan 1999&#x00A0;&#x00A0;15:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0133842</LI></UL> <UL><LI>SAPKI3X476&#x00A0;&#x00A0; 14 Jan 1999&#x00A0;&#x00A0;08:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0131077</LI></UL> <UL><LI>SAPKI3X477&#x00A0;&#x00A0; 14 Jan 1999&#x00A0;&#x00A0;14:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0134074</LI></UL> <UL><LI>SAPKI3X479&#x00A0;&#x00A0; 18 Jan 1999&#x00A0;&#x00A0;08:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0113274</LI></UL> <UL><LI>SAPKI3X480&#x00A0;&#x00A0; 19 Jan 1999&#x00A0;&#x00A0;13:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0110448</LI></UL> <UL><LI>SAPKI3X481&#x00A0;&#x00A0; 27 Jan 1999&#x00A0;&#x00A0;10:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0136108</LI></UL> <UL><LI>SAPKI3X482&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Feb 1999&#x00A0;&#x00A0;12:18&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0130727</LI></UL> <UL><LI>SAPKI3X478&#x00A0;&#x00A0; 15 Jan 1998&#x00A0;&#x00A0;08:42&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0134192</LI></UL> <UL><LI>SAPKI3X483&#x00A0;&#x00A0; 26 Feb 1998&#x00A0;&#x00A0;10:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0138726</LI></UL> <UL><LI>SAPKI3X484&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Mar 1998&#x00A0;&#x00A0;14:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0141291</LI></UL> <UL><LI>SAPKI3X485&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Mar 1998&#x00A0;&#x00A0;13:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0116116</LI></UL> <UL><LI>SAPKI3X486&#x00A0;&#x00A0; 23 Mar 1999&#x00A0;&#x00A0;16:44&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0144301</LI></UL> <UL><LI>SAPKI3X487&#x00A0;&#x00A0; 30 Mar 1999&#x00A0;&#x00A0;16:09&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0142530</LI></UL> <UL><LI>SAPKI3X488&#x00A0;&#x00A0; 15 Apr 1999&#x00A0;&#x00A0;10:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0147612</LI></UL> <UL><LI>SOXK003167&#x00A0;&#x00A0; 27 May 1999&#x00A0;&#x00A0;15:49&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/JVA&#x00A0;&#x00A0;0151018</LI></UL> <UL><LI>SOXK002859&#x00A0;&#x00A0; 27 May 1999&#x00A0;&#x00A0;14:50&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/JVA&#x00A0;&#x00A0;0151018</LI></UL> <UL><LI>SOXK003161&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Jun 1999&#x00A0;&#x00A0;17:47&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0153121</LI></UL> <UL><LI>SOXK003177&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Jun 1999&#x00A0;&#x00A0;11:30&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/JVA&#x00A0;&#x00A0;0134928</LI></UL> <UL><LI>SOXK003172&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Jun 1999&#x00A0;&#x00A0;17:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/JVA&#x00A0;&#x00A0;0151018</LI></UL> <UL><LI>SOXK003168&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Jun 1999&#x00A0;&#x00A0;16:13&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0155661</LI></UL> <UL><LI>SOXK003184&#x00A0;&#x00A0; 10 Jun 1999&#x00A0;&#x00A0;11:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0156447</LI></UL> <UL><LI>SOXK003168&#x00A0;&#x00A0; 10 Jun 1999&#x00A0;&#x00A0;14:40&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0155661</LI></UL> <UL><LI>SOXK003212&#x00A0;&#x00A0; 16 Jul 1999&#x00A0;&#x00A0;09:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0162851</LI></UL> <UL><LI>SOXK003220&#x00A0;&#x00A0;30 Jul 1999&#x00A0;&#x00A0;16:09&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0165205 repl. by SOXK003250</LI></UL> <UL><LI>SOXK003248&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Oct 1999&#x00A0;&#x00A0;10:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;US&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0170716</LI></UL> <UL><LI>SOXK003250&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Oct 1999&#x00A0;&#x00A0;09:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0165205</LI></UL> <UL><LI>SOXK003254&#x00A0;&#x00A0; 18 Oct 1999&#x00A0;&#x00A0;10:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0178875</LI></UL> <UL><LI>SOXK003264&#x00A0;&#x00A0; 15 Dec 1999&#x00A0;&#x00A0;07:07&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 0147813</LI></UL> <UL><LI>SOXK003266&#x00A0;&#x00A0; 24 Jan 2000&#x00A0;&#x00A0;14:41&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0196124&#x00A0;&#x00A0; -</LI></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D022180)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000080279/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000080279/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000080279/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000080279/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000080279/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000080279/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000080279/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000080279/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000080279/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "99959", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: EURO legal requirements Hot Package 36,3.0D", "RefUrl": "/notes/99959"}, {"RefNumber": "99944", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR1M does not populate amount field", "RefUrl": "/notes/99944"}, {"RefNumber": "99661", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/99661"}, {"RefNumber": "99622", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "BILLING IN BACKGROUND", "RefUrl": "/notes/99622"}, {"RefNumber": "99493", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Printout error in exchange statement", "RefUrl": "/notes/99493"}, {"RefNumber": "99423", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/99423"}, {"RefNumber": "99355", "RefComponent": "CA-JVA", "RefTitle": "Add multi-currency functionality", "RefUrl": "/notes/99355"}, {"RefNumber": "98946", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Collective Note for IS-Oil release 1.0C", "RefUrl": "/notes/98946"}, {"RefNumber": "98767", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/98767"}, {"RefNumber": "98698", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange assignment using alphanumeric exg numbers", "RefUrl": "/notes/98698"}, {"RefNumber": "98550", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/98550"}, {"RefNumber": "98534", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Abend O1 544 in TD Delivery Confirmation", "RefUrl": "/notes/98534"}, {"RefNumber": "98301", "RefComponent": "CA-JVA", "RefTitle": "IS-OIL assessment to result: error message GA721", "RefUrl": "/notes/98301"}, {"RefNumber": "98113", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Internal-posted material passed to CO-PA", "RefUrl": "/notes/98113"}, {"RefNumber": "98005", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/98005"}, {"RefNumber": "97747", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/97747"}, {"RefNumber": "97726", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/97726"}, {"RefNumber": "97368", "RefComponent": "IS-OIL-DS", "RefTitle": "Sales Returns for IS-Oil", "RefUrl": "/notes/97368"}, {"RefNumber": "97263", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "SD invoice - invoice cycle with TDP balancing", "RefUrl": "/notes/97263"}, {"RefNumber": "96761", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Batch input data in background for screen not exist", "RefUrl": "/notes/96761"}, {"RefNumber": "96740", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong values in SIS for returns invoices", "RefUrl": "/notes/96740"}, {"RefNumber": "96662", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/96662"}, {"RefNumber": "96657", "RefComponent": "IS-OIL", "RefTitle": "@3Z@ Collective IS-OIL   * For Internal Use Only *", "RefUrl": "/notes/96657"}, {"RefNumber": "96591", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Deassign SD contract from exchange agreement", "RefUrl": "/notes/96591"}, {"RefNumber": "96434", "RefComponent": "XX-RC-IS-OIL", "RefTitle": "Incorrect field in last goods receipt date", "RefUrl": "/notes/96434"}, {"RefNumber": "96370", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "short dump after changing tax classification", "RefUrl": "/notes/96370"}, {"RefNumber": "96322", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect excise duty value for ERS credit memo", "RefUrl": "/notes/96322"}, {"RefNumber": "96285", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Add Exchange number field on screen variant", "RefUrl": "/notes/96285"}, {"RefNumber": "96138", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/96138"}, {"RefNumber": "96045", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "IS-OIL TD Weight UoM not Valid in Language E for TU", "RefUrl": "/notes/96045"}, {"RefNumber": "95543", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/95543"}, {"RefNumber": "95542", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/95542"}, {"RefNumber": "95489", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fee change in purchase contract not saved", "RefUrl": "/notes/95489"}, {"RefNumber": "95316", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Add Exchange number field", "RefUrl": "/notes/95316"}, {"RefNumber": "95076", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Payment terms are being cleared for exg. invoices", "RefUrl": "/notes/95076"}, {"RefNumber": "94071", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/94071"}, {"RefNumber": "93860", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Quantity schedule not updated f. reversals/returns", "RefUrl": "/notes/93860"}, {"RefNumber": "93571", "RefComponent": "IS-OIL", "RefTitle": "Sequence of corrections - IS-Oil / IS-MINE Policy", "RefUrl": "/notes/93571"}, {"RefNumber": "93554", "RefComponent": "IS-OIL", "RefTitle": "@3Z@ 4. collective oil fixes/transports after HP34", "RefUrl": "/notes/93554"}, {"RefNumber": "93260", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Zero quantities at delivery confirmation error", "RefUrl": "/notes/93260"}, {"RefNumber": "93176", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/93176"}, {"RefNumber": "93144", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Storage loc not updatable in subsequent delivery confirm.", "RefUrl": "/notes/93144"}, {"RefNumber": "93019", "RefComponent": "CA-JVA", "RefTitle": "IW45: No JV documents created", "RefUrl": "/notes/93019"}, {"RefNumber": "92254", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "IS-OIL version of core note 50458", "RefUrl": "/notes/92254"}, {"RefNumber": "91775", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "No check for BOM component of unplanned Dlvy.", "RefUrl": "/notes/91775"}, {"RefNumber": "91559", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "delivery note/bill of lading field on movement", "RefUrl": "/notes/91559"}, {"RefNumber": "91436", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Improve exchange reporting", "RefUrl": "/notes/91436"}, {"RefNumber": "91379", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/91379"}, {"RefNumber": "91265", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Data not def. correctly in sales order w/ item div.", "RefUrl": "/notes/91265"}, {"RefNumber": "91089", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/91089"}, {"RefNumber": "90998", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Sub/base abend if base does not exist in sub plant", "RefUrl": "/notes/90998"}, {"RefNumber": "90996", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/90996"}, {"RefNumber": "90912", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "IS-OIL version of note 65501", "RefUrl": "/notes/90912"}, {"RefNumber": "90764", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Integration Formula & Average Pricing / Gross/Net", "RefUrl": "/notes/90764"}, {"RefNumber": "90681", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "EKPO index EKPO______B with field OIEXGNUM", "RefUrl": "/notes/90681"}, {"RefNumber": "90487", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incomplete OIAQB update for UPE", "RefUrl": "/notes/90487"}, {"RefNumber": "90421", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/90421"}, {"RefNumber": "90382", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Match code search on Business location name fails", "RefUrl": "/notes/90382"}, {"RefNumber": "90371", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "FIFO Document extract in exchanges (MRF3)", "RefUrl": "/notes/90371"}, {"RefNumber": "90333", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/90333"}, {"RefNumber": "90227", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/90227"}, {"RefNumber": "90136", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange statement printout error", "RefUrl": "/notes/90136"}, {"RefNumber": "90030", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/90030"}, {"RefNumber": "89784", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Use open posting period in recovery function", "RefUrl": "/notes/89784"}, {"RefNumber": "89782", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Allow generic entry for tax group in TOIGS3", "RefUrl": "/notes/89782"}, {"RefNumber": "89508", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "IS-Oil plant determination, material does not exist", "RefUrl": "/notes/89508"}, {"RefNumber": "89420", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/89420"}, {"RefNumber": "89221", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Scheduling - wrong delivery number is messages", "RefUrl": "/notes/89221"}, {"RefNumber": "89205", "RefComponent": "IS-OIL-DS", "RefTitle": "Wrong client-dependent attribute for IMG-Activity", "RefUrl": "/notes/89205"}, {"RefNumber": "89037", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Location contract index not used correctly", "RefUrl": "/notes/89037"}, {"RefNumber": "88561", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Correct EKES update for Borrow / Loan Exchanges", "RefUrl": "/notes/88561"}, {"RefNumber": "88559", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Correct PO history display for B/L exchanges", "RefUrl": "/notes/88559"}, {"RefNumber": "88499", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/88499"}, {"RefNumber": "88364", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/88364"}, {"RefNumber": "88350", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/88350"}, {"RefNumber": "88182", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/88182"}, {"RefNumber": "88072", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Sales support : Partners only partially cop. IS-OIL", "RefUrl": "/notes/88072"}, {"RefNumber": "88052", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/88052"}, {"RefNumber": "87960", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Goods rec. repricing with indicator 5 on info rec.", "RefUrl": "/notes/87960"}, {"RefNumber": "87832", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "SD invoice - invoice cycle with TDP balancing", "RefUrl": "/notes/87832"}, {"RefNumber": "87801", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/87801"}, {"RefNumber": "87673", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "bill of lading (FRBNR) not populated in mat. doc", "RefUrl": "/notes/87673"}, {"RefNumber": "87661", "RefComponent": "CA-JVA", "RefTitle": "Settlement to material delivers error G4801", "RefUrl": "/notes/87661"}, {"RefNumber": "87469", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/87469"}, {"RefNumber": "87429", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "credit control values going negative", "RefUrl": "/notes/87429"}, {"RefNumber": "87319", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Netting BTCI (corr. screen sequence trans. F-42)", "RefUrl": "/notes/87319"}, {"RefNumber": "87213", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "contract restr. not defaulted in exchange contract", "RefUrl": "/notes/87213"}, {"RefNumber": "87157", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Enhanced Netting data population to IV vendor line", "RefUrl": "/notes/87157"}, {"RefNumber": "87112", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "external details in incompletion procedure", "RefUrl": "/notes/87112"}, {"RefNumber": "87082", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Tax revaluation with transit stock", "RefUrl": "/notes/87082"}, {"RefNumber": "87022", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/87022"}, {"RefNumber": "86981", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/86981"}, {"RefNumber": "86938", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/86938"}, {"RefNumber": "86915", "RefComponent": "CA-JVA", "RefTitle": "Blank source field in settlement reversal", "RefUrl": "/notes/86915"}, {"RefNumber": "86912", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/86912"}, {"RefNumber": "86862", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Scheduling - Wrong message at driver assigment", "RefUrl": "/notes/86862"}, {"RefNumber": "86843", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/86843"}, {"RefNumber": "86726", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "MB51 - Wrong field selection in program", "RefUrl": "/notes/86726"}, {"RefNumber": "86507", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "External details with import", "RefUrl": "/notes/86507"}, {"RefNumber": "86425", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "GETWA_NOT_ASSIGNED in RHFAKT00", "RefUrl": "/notes/86425"}, {"RefNumber": "86377", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Called-off qty. in QS does not match document qty.", "RefUrl": "/notes/86377"}, {"RefNumber": "86311", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/86311"}, {"RefNumber": "86310", "RefComponent": "CA-JVA", "RefTitle": "CJ02 RAISE_EXCEPTION FCJDWF00 When Change Proj-Def.", "RefUrl": "/notes/86310"}, {"RefNumber": "86200", "RefComponent": "CA-JVA", "RefTitle": "Reverse/Rebook: incorrect sender selection Rebook", "RefUrl": "/notes/86200"}, {"RefNumber": "86112", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/86112"}, {"RefNumber": "86080", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/86080"}, {"RefNumber": "85897", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External details not copied MM stock transfer order", "RefUrl": "/notes/85897"}, {"RefNumber": "85878", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/85878"}, {"RefNumber": "85861", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/85861"}, {"RefNumber": "85697", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Split invoicing with deliv items w/o ref to order", "RefUrl": "/notes/85697"}, {"RefNumber": "85506", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Accrual Conditions with split invoicing", "RefUrl": "/notes/85506"}, {"RefNumber": "85383", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/85383"}, {"RefNumber": "85370", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MOT is not checked in condition maintenance", "RefUrl": "/notes/85370"}, {"RefNumber": "85365", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "3.1H: Missing delivery group for copied documents", "RefUrl": "/notes/85365"}, {"RefNumber": "85317", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/85317"}, {"RefNumber": "85283", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Material display in ROIAMMA3 Exg transaction report", "RefUrl": "/notes/85283"}, {"RefNumber": "85049", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD - Correction to ROIGSM00 - TD Stock Overview", "RefUrl": "/notes/85049"}, {"RefNumber": "84945", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/84945"}, {"RefNumber": "84928", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Multiple ship-to with ref. to contract", "RefUrl": "/notes/84928"}, {"RefNumber": "84911", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/84911"}, {"RefNumber": "84885", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/84885"}, {"RefNumber": "84478", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Missing parameter in PERFORM OIC_TIME_UOM_RULE_003", "RefUrl": "/notes/84478"}, {"RefNumber": "84461", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Error in Exchange Statement UPE Plant Summary", "RefUrl": "/notes/84461"}, {"RefNumber": "84290", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/84290"}, {"RefNumber": "84164", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Wrong use of UoM with gross/net pricing with tax", "RefUrl": "/notes/84164"}, {"RefNumber": "84155", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD - Deletion of Shipment and release of deliveries", "RefUrl": "/notes/84155"}, {"RefNumber": "84069", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Order quantity update after final delivery posting", "RefUrl": "/notes/84069"}, {"RefNumber": "83987", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "F&A condition Base Value not correctly passed", "RefUrl": "/notes/83987"}, {"RefNumber": "83757", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Use BOMnr to identify B<PERSON> (type missing if ref.)", "RefUrl": "/notes/83757"}, {"RefNumber": "83641", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Net price field deleted during contract processing", "RefUrl": "/notes/83641"}, {"RefNumber": "83535", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/83535"}, {"RefNumber": "83477", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Copying company code does not work", "RefUrl": "/notes/83477"}, {"RefNumber": "83400", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Contract nr.not copied to BOM sub-items in call-off", "RefUrl": "/notes/83400"}, {"RefNumber": "83277", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Loading - Problems with BOMs/Rebrands", "RefUrl": "/notes/83277"}, {"RefNumber": "83234", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/83234"}, {"RefNumber": "83202", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/83202"}, {"RefNumber": "83199", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Quantity adjustment for fixed value conditions", "RefUrl": "/notes/83199"}, {"RefNumber": "83168", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "ext. details not updated at fast change of plant", "RefUrl": "/notes/83168"}, {"RefNumber": "83134", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "group conditions with time pricing", "RefUrl": "/notes/83134"}, {"RefNumber": "83040", "RefComponent": "CA-JVA", "RefTitle": "Reimplementation of missing upgrade coding", "RefUrl": "/notes/83040"}, {"RefNumber": "83027", "RefComponent": "CA-JVA", "RefTitle": "GJ443 at RI maintenance in foreigen language", "RefUrl": "/notes/83027"}, {"RefNumber": "82903", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Split Invoicing and SIS", "RefUrl": "/notes/82903"}, {"RefNumber": "82811", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "wrong \"OPEN QUANTITY\" in contract", "RefUrl": "/notes/82811"}, {"RefNumber": "82763", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Quantity restriction using selection list", "RefUrl": "/notes/82763"}, {"RefNumber": "82718", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/82718"}, {"RefNumber": "82217", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/82217"}, {"RefNumber": "82133", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "wrong handling type when division from mat. master", "RefUrl": "/notes/82133"}, {"RefNumber": "82107", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/82107"}, {"RefNumber": "82020", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Loading - Spec. of St.locs for sub-items of BoMs", "RefUrl": "/notes/82020"}, {"RefNumber": "82017", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong update of delivered qty for purch. call-off", "RefUrl": "/notes/82017"}, {"RefNumber": "81969", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/81969"}, {"RefNumber": "81895", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/81895"}, {"RefNumber": "81840", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD - German translation for Bulk Shipment Texts", "RefUrl": "/notes/81840"}, {"RefNumber": "81837", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD - German translation for report Roigsm00", "RefUrl": "/notes/81837"}, {"RefNumber": "81829", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/81829"}, {"RefNumber": "81775", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/81775"}, {"RefNumber": "81721", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/81721"}, {"RefNumber": "81720", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/81720"}, {"RefNumber": "81677", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees not cleared in invoice verification", "RefUrl": "/notes/81677"}, {"RefNumber": "81645", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/81645"}, {"RefNumber": "81634", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/81634"}, {"RefNumber": "81417", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/81417"}, {"RefNumber": "81390", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Not changeable baseproduct on MM-contracts", "RefUrl": "/notes/81390"}, {"RefNumber": "81307", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/81307"}, {"RefNumber": "81268", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/81268"}, {"RefNumber": "81051", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/81051"}, {"RefNumber": "81011", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/81011"}, {"RefNumber": "80945", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/80945"}, {"RefNumber": "80855", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/80855"}, {"RefNumber": "80813", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/80813"}, {"RefNumber": "80623", "RefComponent": "IS-OIL-DS", "RefTitle": "OMJJ: No customer name range for movement types", "RefUrl": "/notes/80623"}, {"RefNumber": "80589", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/80589"}, {"RefNumber": "80482", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "System Failure in Creating Netting Document", "RefUrl": "/notes/80482"}, {"RefNumber": "80479", "RefComponent": "IS-OIL", "RefTitle": "@3Z@ 3. Collective oil fixes/transport after HP19", "RefUrl": "/notes/80479"}, {"RefNumber": "80407", "RefComponent": "CA-JVA", "RefTitle": "note 60912 in IS-Oil", "RefUrl": "/notes/80407"}, {"RefNumber": "80232", "RefComponent": "CA-JVA", "RefTitle": "IS-OIL 2.0C: double values with allocation", "RefUrl": "/notes/80232"}, {"RefNumber": "80069", "RefComponent": "SD-BF-PR", "RefTitle": "Pricing in billing plan: Monthly amount incorrect", "RefUrl": "/notes/80069"}, {"RefNumber": "79730", "RefComponent": "CA-JVA", "RefTitle": "CJ02 RAISE_EXCEPTION FCJDWF00 When Change Proj-Def.", "RefUrl": "/notes/79730"}, {"RefNumber": "79716", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-Oil and 30D Hot Package 19 (HPC 30D/2)", "RefUrl": "/notes/79716"}, {"RefNumber": "77407", "RefComponent": "IS-OIL-BC", "RefTitle": "CRTs for IS-Oil", "RefUrl": "/notes/77407"}, {"RefNumber": "77336", "RefComponent": "IS-OIL-BC", "RefTitle": "Additional Info: IS-OIL Installation on SAP 3.0D", "RefUrl": "/notes/77336"}, {"RefNumber": "76703", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "IS-OIL upgrade to 1.0C for existing SD documents", "RefUrl": "/notes/76703"}, {"RefNumber": "75620", "RefComponent": "IS-OIL-DS", "RefTitle": "ME21: Conditions from last purchase order", "RefUrl": "/notes/75620"}, {"RefNumber": "69999", "RefComponent": "IS-OIL-DS", "RefTitle": "MR01: Default values from the purchase order", "RefUrl": "/notes/69999"}, {"RefNumber": "47531", "RefComponent": "IS-OIL", "RefTitle": "IS-OIL / IS-MINE / IS-CWM correction guideline", "RefUrl": "/notes/47531"}, {"RefNumber": "319758", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/319758"}, {"RefNumber": "196124", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Required quantity in field VBRP-OILMENG missing", "RefUrl": "/notes/196124"}, {"RefNumber": "178875", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: stock overview for consignment batches", "RefUrl": "/notes/178875"}, {"RefNumber": "165205", "RefComponent": "IS-OIL", "RefTitle": "IS-OIL Y2000 core changes relevant for 3.0D basis", "RefUrl": "/notes/165205"}, {"RefNumber": "162851", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "IS-OIL Unauthorized access to VA02 via VA12", "RefUrl": "/notes/162851"}, {"RefNumber": "156447", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "IS-OIL Incorrect status display in order header", "RefUrl": "/notes/156447"}, {"RefNumber": "155661", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Update term.in QS during TD deliv.confirm.: GI date", "RefUrl": "/notes/155661"}, {"RefNumber": "153121", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Scrolling SAPMOIGS3700; take over dates after user exit", "RefUrl": "/notes/153121"}, {"RefNumber": "151018", "RefComponent": "CA-JVA", "RefTitle": "JV EDI billing: New functionality", "RefUrl": "/notes/151018"}, {"RefNumber": "150951", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Missing overload check in compartment allocation details", "RefUrl": "/notes/150951"}, {"RefNumber": "147813", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "ME31: external details not copied", "RefUrl": "/notes/147813"}, {"RefNumber": "147612", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Contract restriction are displayed incorrectly", "RefUrl": "/notes/147612"}, {"RefNumber": "144301", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/144301"}, {"RefNumber": "142530", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Changing External Details after Goods Issue Posting", "RefUrl": "/notes/142530"}, {"RefNumber": "141291", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Invoicing fails with error message M2803", "RefUrl": "/notes/141291"}, {"RefNumber": "138726", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD fix figures for weight checks in comp.planning", "RefUrl": "/notes/138726"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "136108", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees not copied during delivery due list processing", "RefUrl": "/notes/136108"}, {"RefNumber": "134192", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Double calculation of field VBRP-OILMENG", "RefUrl": "/notes/134192"}, {"RefNumber": "134074", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Correction report for VBRP quantity fields", "RefUrl": "/notes/134074"}, {"RefNumber": "133842", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees to inventory don't change mov. average price", "RefUrl": "/notes/133842"}, {"RefNumber": "132370", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/132370"}, {"RefNumber": "1315918", "RefComponent": "CA-JVA", "RefTitle": "Settlement to material delivers error G4801 / note 388457", "RefUrl": "/notes/1315918"}, {"RefNumber": "131434", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/131434"}, {"RefNumber": "131077", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Correct billing document quantity fields", "RefUrl": "/notes/131077"}, {"RefNumber": "130727", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Zero amounts when processing billing plans", "RefUrl": "/notes/130727"}, {"RefNumber": "130541", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Purchasing Contract: fee change not saved", "RefUrl": "/notes/130541"}, {"RefNumber": "130326", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/130326"}, {"RefNumber": "130238", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Shortdump in RM06EW30 -archiving of purchase orders", "RefUrl": "/notes/130238"}, {"RefNumber": "129849", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/129849"}, {"RefNumber": "129344", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Type conflict in IS-Oil-TD shipment archiving", "RefUrl": "/notes/129344"}, {"RefNumber": "128957", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/128957"}, {"RefNumber": "128868", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD exception NO_RECORD SAPLOIB2 raised at del.conf.", "RefUrl": "/notes/128868"}, {"RefNumber": "128829", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/128829"}, {"RefNumber": "128018", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD VI01-SCD item accounting cannot be carried out", "RefUrl": "/notes/128018"}, {"RefNumber": "127627", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Incorrect Parameter in EXIT_ROIGASHV_001", "RefUrl": "/notes/127627"}, {"RefNumber": "127367", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Material price missing in exchange GR rev. FI doc.", "RefUrl": "/notes/127367"}, {"RefNumber": "125753", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Text availability for output determination", "RefUrl": "/notes/125753"}, {"RefNumber": "125113", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/125113"}, {"RefNumber": "122490", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Fixed value conditions / prorating in invoices", "RefUrl": "/notes/122490"}, {"RefNumber": "122420", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Use packed decimal to check temperature load: disc", "RefUrl": "/notes/122420"}, {"RefNumber": "122334", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Unable to post goods issue for exchange", "RefUrl": "/notes/122334"}, {"RefNumber": "120414", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Inconsistencies between MKPF/MSEG and OIAQB", "RefUrl": "/notes/120414"}, {"RefNumber": "119913", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "OIANF update error during delivery creation", "RefUrl": "/notes/119913"}, {"RefNumber": "118774", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/118774"}, {"RefNumber": "118708", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/118708"}, {"RefNumber": "117696", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "No value update of OIA07 during goods receipt", "RefUrl": "/notes/117696"}, {"RefNumber": "117116", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong logical inventory value after revaluation", "RefUrl": "/notes/117116"}, {"RefNumber": "116116", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/116116"}, {"RefNumber": "115290", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD output det. error VN 038 for FAX, TELEX, ...", "RefUrl": "/notes/115290"}, {"RefNumber": "114800", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD - Assign delivery, no UoM conversion, items left", "RefUrl": "/notes/114800"}, {"RefNumber": "114684", "RefComponent": "CA-JVA", "RefTitle": "Billable CO allocations after closing of JV period", "RefUrl": "/notes/114684"}, {"RefNumber": "114060", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/114060"}, {"RefNumber": "113948", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Goods receipt reversal stops with error message", "RefUrl": "/notes/113948"}, {"RefNumber": "113639", "RefComponent": "CA-JVA", "RefTitle": "Billable CO postings after closing of JV period", "RefUrl": "/notes/113639"}, {"RefNumber": "113546", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Intercompany billing: no values passed to CO-PA", "RefUrl": "/notes/113546"}, {"RefNumber": "113274", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/113274"}, {"RefNumber": "112851", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/112851"}, {"RefNumber": "112717", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Quantity schedule checked against wrong contr. pos.", "RefUrl": "/notes/112717"}, {"RefNumber": "112636", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Dump in delivery tolerance check when > 1 item", "RefUrl": "/notes/112636"}, {"RefNumber": "112567", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Archiving of IS-Oil TD shipments - Performance", "RefUrl": "/notes/112567"}, {"RefNumber": "112521", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD exception NO_RECORD SAPLOIB2 raised at del.conf.", "RefUrl": "/notes/112521"}, {"RefNumber": "112217", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/112217"}, {"RefNumber": "111514", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Excise duty: Short dump in IV for PO with many GRs", "RefUrl": "/notes/111514"}, {"RefNumber": "110448", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Performance of Reports ROIAMMA3 and ROIAMMAT", "RefUrl": "/notes/110448"}, {"RefNumber": "110011", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External details not proposed when material changed", "RefUrl": "/notes/110011"}, {"RefNumber": "109845", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/109845"}, {"RefNumber": "109572", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "UPE: Neg. pymnt for purch/sales org.w/o link to cc", "RefUrl": "/notes/109572"}, {"RefNumber": "109531", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Enhanced report for tax code corrections", "RefUrl": "/notes/109531"}, {"RefNumber": "109340", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "New: Incorrect quantities in FI-SL for SD postings", "RefUrl": "/notes/109340"}, {"RefNumber": "109055", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Improved performance in FI transactions", "RefUrl": "/notes/109055"}, {"RefNumber": "108671", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange statement: no issuing of output possible", "RefUrl": "/notes/108671"}, {"RefNumber": "108368", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect fee condition value in invoice", "RefUrl": "/notes/108368"}, {"RefNumber": "108122", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong tax code for fees in invoice cancellation", "RefUrl": "/notes/108122"}, {"RefNumber": "107992", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Correct invoice cycle status", "RefUrl": "/notes/107992"}, {"RefNumber": "107800", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Termination in SAPLOICQ after applying HP34", "RefUrl": "/notes/107800"}, {"RefNumber": "107436", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees: shortdump in IV for PO with many GRs", "RefUrl": "/notes/107436"}, {"RefNumber": "107344", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD partner-specific output determination", "RefUrl": "/notes/107344"}, {"RefNumber": "107019", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Performance in contract document flows - IS-OIL -", "RefUrl": "/notes/107019"}, {"RefNumber": "106784", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Default delivery date for unplanned deliveries", "RefUrl": "/notes/106784"}, {"RefNumber": "106052", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Refresh data per material document for GI/GR slip", "RefUrl": "/notes/106052"}, {"RefNumber": "105799", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/105799"}, {"RefNumber": "105758", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "No document flow on order for GI of BOM component", "RefUrl": "/notes/105758"}, {"RefNumber": "105336", "RefComponent": "CA-JVA", "RefTitle": "IS-OIL: No update in Profit Center Accounting", "RefUrl": "/notes/105336"}, {"RefNumber": "105255", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "No deassign of MM contracts from exchange possible", "RefUrl": "/notes/105255"}, {"RefNumber": "105023", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Report to correct field EKBE-OIIMATV", "RefUrl": "/notes/105023"}, {"RefNumber": "104536", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Catt SOX00008; error in tranaction MMZ1", "RefUrl": "/notes/104536"}, {"RefNumber": "104425", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Transp. unit comp./meter assignment: wrong headers", "RefUrl": "/notes/104425"}, {"RefNumber": "104300", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "MB01/02/03 Del.note field lost", "RefUrl": "/notes/104300"}, {"RefNumber": "104186", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/104186"}, {"RefNumber": "104168", "RefComponent": "CA-JVA", "RefTitle": "WBS can be set to Tech.complete w/o JV data", "RefUrl": "/notes/104168"}, {"RefNumber": "103969", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/103969"}, {"RefNumber": "103968", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/103968"}, {"RefNumber": "103802", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Quantities not transferred to FI-SL", "RefUrl": "/notes/103802"}, {"RefNumber": "103689", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Final delivery indicator with bill of material", "RefUrl": "/notes/103689"}, {"RefNumber": "103578", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Use density from load maximum in del. confirmation", "RefUrl": "/notes/103578"}, {"RefNumber": "103199", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/103199"}, {"RefNumber": "102975", "RefComponent": "IS-OIL-BC", "RefTitle": "Additional Info : IS-OIL HP38/CRT38", "RefUrl": "/notes/102975"}, {"RefNumber": "102965", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/102965"}, {"RefNumber": "102529", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "CATT script SO300735 fails", "RefUrl": "/notes/102529"}, {"RefNumber": "102523", "RefComponent": "CA-JVA", "RefTitle": "Dump in SAPMK23B after HP 34 in JV environment", "RefUrl": "/notes/102523"}, {"RefNumber": "102509", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/102509"}, {"RefNumber": "102439", "RefComponent": "IS-OIL-DS", "RefTitle": "SD: Document status and incompletion", "RefUrl": "/notes/102439"}, {"RefNumber": "102368", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Index error in internal table G_SVMQ1INT", "RefUrl": "/notes/102368"}, {"RefNumber": "102306", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong values in statistics update", "RefUrl": "/notes/102306"}, {"RefNumber": "102227", "RefComponent": "CO-OM-CCA-F", "RefTitle": "Incorrect credit for segmental selection", "RefUrl": "/notes/102227"}, {"RefNumber": "102185", "RefComponent": "CA-JVA", "RefTitle": "Dump when saving new WBS before pressing enter", "RefUrl": "/notes/102185"}, {"RefNumber": "102179", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/102179"}, {"RefNumber": "102168", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Final delivery flag and status update", "RefUrl": "/notes/102168"}, {"RefNumber": "101910", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External details not proposed when material changed", "RefUrl": "/notes/101910"}, {"RefNumber": "101484", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Gain/Loss quantity saved in FI document", "RefUrl": "/notes/101484"}, {"RefNumber": "101321", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Excise duty license valid for ship-to partner", "RefUrl": "/notes/101321"}, {"RefNumber": "101267", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "GR-basedIV flag for exchange-related POs", "RefUrl": "/notes/101267"}, {"RefNumber": "101238", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Transaction O4H1 abends - update terminate", "RefUrl": "/notes/101238"}, {"RefNumber": "101141", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/101141"}, {"RefNumber": "100909", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Update termination when changing business data", "RefUrl": "/notes/100909"}, {"RefNumber": "100555", "RefComponent": "IS-OIL", "RefTitle": "Withholding tax code missing on SAPMF05A 2302", "RefUrl": "/notes/100555"}, {"RefNumber": "100471", "RefComponent": "CA-JVA", "RefTitle": "performance tuning + ability to retrieve all jvso1", "RefUrl": "/notes/100471"}, {"RefNumber": "100381", "RefComponent": "CA-JVA", "RefTitle": "Incorrect RI in invoice with multiple account assig", "RefUrl": "/notes/100381"}, {"RefNumber": "100355", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/100355"}, {"RefNumber": "100288", "RefComponent": "CA-JVA", "RefTitle": "Archiving of Joint Venture Data in 2.0C (3.0D)", "RefUrl": "/notes/100288"}, {"RefNumber": "100107", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Problem in third party order invoicing", "RefUrl": "/notes/100107"}, {"RefNumber": "100090", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/100090"}, {"RefNumber": "100072", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Hung up of partially confirmed bulk shipments", "RefUrl": "/notes/100072"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1315918", "RefComponent": "CA-JVA", "RefTitle": "Settlement to material delivers error G4801 / note 388457", "RefUrl": "/notes/1315918 "}, {"RefNumber": "129344", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Type conflict in IS-Oil-TD shipment archiving", "RefUrl": "/notes/129344 "}, {"RefNumber": "99959", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: EURO legal requirements Hot Package 36,3.0D", "RefUrl": "/notes/99959 "}, {"RefNumber": "87661", "RefComponent": "CA-JVA", "RefTitle": "Settlement to material delivers error G4801", "RefUrl": "/notes/87661 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "96657", "RefComponent": "IS-OIL", "RefTitle": "@3Z@ Collective IS-OIL   * For Internal Use Only *", "RefUrl": "/notes/96657 "}, {"RefNumber": "77407", "RefComponent": "IS-OIL-BC", "RefTitle": "CRTs for IS-Oil", "RefUrl": "/notes/77407 "}, {"RefNumber": "93571", "RefComponent": "IS-OIL", "RefTitle": "Sequence of corrections - IS-Oil / IS-MINE Policy", "RefUrl": "/notes/93571 "}, {"RefNumber": "80479", "RefComponent": "IS-OIL", "RefTitle": "@3Z@ 3. Collective oil fixes/transport after HP19", "RefUrl": "/notes/80479 "}, {"RefNumber": "100288", "RefComponent": "CA-JVA", "RefTitle": "Archiving of Joint Venture Data in 2.0C (3.0D)", "RefUrl": "/notes/100288 "}, {"RefNumber": "83199", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Quantity adjustment for fixed value conditions", "RefUrl": "/notes/83199 "}, {"RefNumber": "98301", "RefComponent": "CA-JVA", "RefTitle": "IS-OIL assessment to result: error message GA721", "RefUrl": "/notes/98301 "}, {"RefNumber": "100072", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Hung up of partially confirmed bulk shipments", "RefUrl": "/notes/100072 "}, {"RefNumber": "178875", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: stock overview for consignment batches", "RefUrl": "/notes/178875 "}, {"RefNumber": "86915", "RefComponent": "CA-JVA", "RefTitle": "Blank source field in settlement reversal", "RefUrl": "/notes/86915 "}, {"RefNumber": "98534", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Abend O1 544 in TD Delivery Confirmation", "RefUrl": "/notes/98534 "}, {"RefNumber": "107344", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD partner-specific output determination", "RefUrl": "/notes/107344 "}, {"RefNumber": "196124", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Required quantity in field VBRP-OILMENG missing", "RefUrl": "/notes/196124 "}, {"RefNumber": "100381", "RefComponent": "CA-JVA", "RefTitle": "Incorrect RI in invoice with multiple account assig", "RefUrl": "/notes/100381 "}, {"RefNumber": "102523", "RefComponent": "CA-JVA", "RefTitle": "Dump in SAPMK23B after HP 34 in JV environment", "RefUrl": "/notes/102523 "}, {"RefNumber": "114684", "RefComponent": "CA-JVA", "RefTitle": "Billable CO allocations after closing of JV period", "RefUrl": "/notes/114684 "}, {"RefNumber": "134074", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Correction report for VBRP quantity fields", "RefUrl": "/notes/134074 "}, {"RefNumber": "98113", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Internal-posted material passed to CO-PA", "RefUrl": "/notes/98113 "}, {"RefNumber": "102975", "RefComponent": "IS-OIL-BC", "RefTitle": "Additional Info : IS-OIL HP38/CRT38", "RefUrl": "/notes/102975 "}, {"RefNumber": "93554", "RefComponent": "IS-OIL", "RefTitle": "@3Z@ 4. collective oil fixes/transports after HP34", "RefUrl": "/notes/93554 "}, {"RefNumber": "147813", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "ME31: external details not copied", "RefUrl": "/notes/147813 "}, {"RefNumber": "100555", "RefComponent": "IS-OIL", "RefTitle": "Withholding tax code missing on SAPMF05A 2302", "RefUrl": "/notes/100555 "}, {"RefNumber": "165205", "RefComponent": "IS-OIL", "RefTitle": "IS-OIL Y2000 core changes relevant for 3.0D basis", "RefUrl": "/notes/165205 "}, {"RefNumber": "102306", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong values in statistics update", "RefUrl": "/notes/102306 "}, {"RefNumber": "162851", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "IS-OIL Unauthorized access to VA02 via VA12", "RefUrl": "/notes/162851 "}, {"RefNumber": "101238", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Transaction O4H1 abends - update terminate", "RefUrl": "/notes/101238 "}, {"RefNumber": "147612", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Contract restriction are displayed incorrectly", "RefUrl": "/notes/147612 "}, {"RefNumber": "96434", "RefComponent": "XX-RC-IS-OIL", "RefTitle": "Incorrect field in last goods receipt date", "RefUrl": "/notes/96434 "}, {"RefNumber": "93019", "RefComponent": "CA-JVA", "RefTitle": "IW45: No JV documents created", "RefUrl": "/notes/93019 "}, {"RefNumber": "156447", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "IS-OIL Incorrect status display in order header", "RefUrl": "/notes/156447 "}, {"RefNumber": "155661", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Update term.in QS during TD deliv.confirm.: GI date", "RefUrl": "/notes/155661 "}, {"RefNumber": "151018", "RefComponent": "CA-JVA", "RefTitle": "JV EDI billing: New functionality", "RefUrl": "/notes/151018 "}, {"RefNumber": "150951", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Missing overload check in compartment allocation details", "RefUrl": "/notes/150951 "}, {"RefNumber": "153121", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Scrolling SAPMOIGS3700; take over dates after user exit", "RefUrl": "/notes/153121 "}, {"RefNumber": "141291", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Invoicing fails with error message M2803", "RefUrl": "/notes/141291 "}, {"RefNumber": "110448", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Performance of Reports ROIAMMA3 and ROIAMMAT", "RefUrl": "/notes/110448 "}, {"RefNumber": "142530", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Changing External Details after Goods Issue Posting", "RefUrl": "/notes/142530 "}, {"RefNumber": "130541", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Purchasing Contract: fee change not saved", "RefUrl": "/notes/130541 "}, {"RefNumber": "133842", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees to inventory don't change mov. average price", "RefUrl": "/notes/133842 "}, {"RefNumber": "130727", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Zero amounts when processing billing plans", "RefUrl": "/notes/130727 "}, {"RefNumber": "108671", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange statement: no issuing of output possible", "RefUrl": "/notes/108671 "}, {"RefNumber": "138726", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD fix figures for weight checks in comp.planning", "RefUrl": "/notes/138726 "}, {"RefNumber": "134192", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Double calculation of field VBRP-OILMENG", "RefUrl": "/notes/134192 "}, {"RefNumber": "109340", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "New: Incorrect quantities in FI-SL for SD postings", "RefUrl": "/notes/109340 "}, {"RefNumber": "136108", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees not copied during delivery due list processing", "RefUrl": "/notes/136108 "}, {"RefNumber": "131077", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Correct billing document quantity fields", "RefUrl": "/notes/131077 "}, {"RefNumber": "105023", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Report to correct field EKBE-OIIMATV", "RefUrl": "/notes/105023 "}, {"RefNumber": "130238", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Shortdump in RM06EW30 -archiving of purchase orders", "RefUrl": "/notes/130238 "}, {"RefNumber": "122334", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Unable to post goods issue for exchange", "RefUrl": "/notes/122334 "}, {"RefNumber": "128868", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD exception NO_RECORD SAPLOIB2 raised at del.conf.", "RefUrl": "/notes/128868 "}, {"RefNumber": "128018", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD VI01-SCD item accounting cannot be carried out", "RefUrl": "/notes/128018 "}, {"RefNumber": "127627", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Incorrect Parameter in EXIT_ROIGASHV_001", "RefUrl": "/notes/127627 "}, {"RefNumber": "127367", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Material price missing in exchange GR rev. FI doc.", "RefUrl": "/notes/127367 "}, {"RefNumber": "104168", "RefComponent": "CA-JVA", "RefTitle": "WBS can be set to Tech.complete w/o JV data", "RefUrl": "/notes/104168 "}, {"RefNumber": "102227", "RefComponent": "CO-OM-CCA-F", "RefTitle": "Incorrect credit for segmental selection", "RefUrl": "/notes/102227 "}, {"RefNumber": "101484", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Gain/Loss quantity saved in FI document", "RefUrl": "/notes/101484 "}, {"RefNumber": "101321", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Excise duty license valid for ship-to partner", "RefUrl": "/notes/101321 "}, {"RefNumber": "96761", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Batch input data in background for screen not exist", "RefUrl": "/notes/96761 "}, {"RefNumber": "93860", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Quantity schedule not updated f. reversals/returns", "RefUrl": "/notes/93860 "}, {"RefNumber": "91775", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "No check for BOM component of unplanned Dlvy.", "RefUrl": "/notes/91775 "}, {"RefNumber": "89784", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Use open posting period in recovery function", "RefUrl": "/notes/89784 "}, {"RefNumber": "86377", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Called-off qty. in QS does not match document qty.", "RefUrl": "/notes/86377 "}, {"RefNumber": "86310", "RefComponent": "CA-JVA", "RefTitle": "CJ02 RAISE_EXCEPTION FCJDWF00 When Change Proj-Def.", "RefUrl": "/notes/86310 "}, {"RefNumber": "86200", "RefComponent": "CA-JVA", "RefTitle": "Reverse/Rebook: incorrect sender selection Rebook", "RefUrl": "/notes/86200 "}, {"RefNumber": "83027", "RefComponent": "CA-JVA", "RefTitle": "GJ443 at RI maintenance in foreigen language", "RefUrl": "/notes/83027 "}, {"RefNumber": "80232", "RefComponent": "CA-JVA", "RefTitle": "IS-OIL 2.0C: double values with allocation", "RefUrl": "/notes/80232 "}, {"RefNumber": "80069", "RefComponent": "SD-BF-PR", "RefTitle": "Pricing in billing plan: Monthly amount incorrect", "RefUrl": "/notes/80069 "}, {"RefNumber": "79730", "RefComponent": "CA-JVA", "RefTitle": "CJ02 RAISE_EXCEPTION FCJDWF00 When Change Proj-Def.", "RefUrl": "/notes/79730 "}, {"RefNumber": "79716", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-Oil and 30D Hot Package 19 (HPC 30D/2)", "RefUrl": "/notes/79716 "}, {"RefNumber": "125753", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Text availability for output determination", "RefUrl": "/notes/125753 "}, {"RefNumber": "115290", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD output det. error VN 038 for FAX, TELEX, ...", "RefUrl": "/notes/115290 "}, {"RefNumber": "122490", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Fixed value conditions / prorating in invoices", "RefUrl": "/notes/122490 "}, {"RefNumber": "122420", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Use packed decimal to check temperature load: disc", "RefUrl": "/notes/122420 "}, {"RefNumber": "119913", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "OIANF update error during delivery creation", "RefUrl": "/notes/119913 "}, {"RefNumber": "120414", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Inconsistencies between MKPF/MSEG and OIAQB", "RefUrl": "/notes/120414 "}, {"RefNumber": "105336", "RefComponent": "CA-JVA", "RefTitle": "IS-OIL: No update in Profit Center Accounting", "RefUrl": "/notes/105336 "}, {"RefNumber": "117116", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong logical inventory value after revaluation", "RefUrl": "/notes/117116 "}, {"RefNumber": "117696", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "No value update of OIA07 during goods receipt", "RefUrl": "/notes/117696 "}, {"RefNumber": "112567", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Archiving of IS-Oil TD shipments - Performance", "RefUrl": "/notes/112567 "}, {"RefNumber": "114800", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD - Assign delivery, no UoM conversion, items left", "RefUrl": "/notes/114800 "}, {"RefNumber": "113639", "RefComponent": "CA-JVA", "RefTitle": "Billable CO postings after closing of JV period", "RefUrl": "/notes/113639 "}, {"RefNumber": "113546", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Intercompany billing: no values passed to CO-PA", "RefUrl": "/notes/113546 "}, {"RefNumber": "113948", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Goods receipt reversal stops with error message", "RefUrl": "/notes/113948 "}, {"RefNumber": "112717", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Quantity schedule checked against wrong contr. pos.", "RefUrl": "/notes/112717 "}, {"RefNumber": "112636", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Dump in delivery tolerance check when > 1 item", "RefUrl": "/notes/112636 "}, {"RefNumber": "112521", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD exception NO_RECORD SAPLOIB2 raised at del.conf.", "RefUrl": "/notes/112521 "}, {"RefNumber": "111514", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Excise duty: Short dump in IV for PO with many GRs", "RefUrl": "/notes/111514 "}, {"RefNumber": "107436", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees: shortdump in IV for PO with many GRs", "RefUrl": "/notes/107436 "}, {"RefNumber": "107019", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Performance in contract document flows - IS-OIL -", "RefUrl": "/notes/107019 "}, {"RefNumber": "110011", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External details not proposed when material changed", "RefUrl": "/notes/110011 "}, {"RefNumber": "109531", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Enhanced report for tax code corrections", "RefUrl": "/notes/109531 "}, {"RefNumber": "109572", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "UPE: Neg. pymnt for purch/sales org.w/o link to cc", "RefUrl": "/notes/109572 "}, {"RefNumber": "109055", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Improved performance in FI transactions", "RefUrl": "/notes/109055 "}, {"RefNumber": "108122", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong tax code for fees in invoice cancellation", "RefUrl": "/notes/108122 "}, {"RefNumber": "108368", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect fee condition value in invoice", "RefUrl": "/notes/108368 "}, {"RefNumber": "107992", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Correct invoice cycle status", "RefUrl": "/notes/107992 "}, {"RefNumber": "107800", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Termination in SAPLOICQ after applying HP34", "RefUrl": "/notes/107800 "}, {"RefNumber": "87112", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "external details in incompletion procedure", "RefUrl": "/notes/87112 "}, {"RefNumber": "106784", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Default delivery date for unplanned deliveries", "RefUrl": "/notes/106784 "}, {"RefNumber": "105255", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "No deassign of MM contracts from exchange possible", "RefUrl": "/notes/105255 "}, {"RefNumber": "105758", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "No document flow on order for GI of BOM component", "RefUrl": "/notes/105758 "}, {"RefNumber": "106052", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Refresh data per material document for GI/GR slip", "RefUrl": "/notes/106052 "}, {"RefNumber": "103802", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Quantities not transferred to FI-SL", "RefUrl": "/notes/103802 "}, {"RefNumber": "104300", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "MB01/02/03 Del.note field lost", "RefUrl": "/notes/104300 "}, {"RefNumber": "101910", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External details not proposed when material changed", "RefUrl": "/notes/101910 "}, {"RefNumber": "104536", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Catt SOX00008; error in tranaction MMZ1", "RefUrl": "/notes/104536 "}, {"RefNumber": "104425", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Transp. unit comp./meter assignment: wrong headers", "RefUrl": "/notes/104425 "}, {"RefNumber": "98946", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Collective Note for IS-Oil release 1.0C", "RefUrl": "/notes/98946 "}, {"RefNumber": "103689", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Final delivery indicator with bill of material", "RefUrl": "/notes/103689 "}, {"RefNumber": "103578", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Use density from load maximum in del. confirmation", "RefUrl": "/notes/103578 "}, {"RefNumber": "82763", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Quantity restriction using selection list", "RefUrl": "/notes/82763 "}, {"RefNumber": "100471", "RefComponent": "CA-JVA", "RefTitle": "performance tuning + ability to retrieve all jvso1", "RefUrl": "/notes/100471 "}, {"RefNumber": "102168", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Final delivery flag and status update", "RefUrl": "/notes/102168 "}, {"RefNumber": "102529", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "CATT script SO300735 fails", "RefUrl": "/notes/102529 "}, {"RefNumber": "102368", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Index error in internal table G_SVMQ1INT", "RefUrl": "/notes/102368 "}, {"RefNumber": "102439", "RefComponent": "IS-OIL-DS", "RefTitle": "SD: Document status and incompletion", "RefUrl": "/notes/102439 "}, {"RefNumber": "99355", "RefComponent": "CA-JVA", "RefTitle": "Add multi-currency functionality", "RefUrl": "/notes/99355 "}, {"RefNumber": "99493", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Printout error in exchange statement", "RefUrl": "/notes/99493 "}, {"RefNumber": "102185", "RefComponent": "CA-JVA", "RefTitle": "Dump when saving new WBS before pressing enter", "RefUrl": "/notes/102185 "}, {"RefNumber": "99622", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "BILLING IN BACKGROUND", "RefUrl": "/notes/99622 "}, {"RefNumber": "101267", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "GR-basedIV flag for exchange-related POs", "RefUrl": "/notes/101267 "}, {"RefNumber": "100909", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Update termination when changing business data", "RefUrl": "/notes/100909 "}, {"RefNumber": "91436", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Improve exchange reporting", "RefUrl": "/notes/91436 "}, {"RefNumber": "96740", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong values in SIS for returns invoices", "RefUrl": "/notes/96740 "}, {"RefNumber": "100107", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Problem in third party order invoicing", "RefUrl": "/notes/100107 "}, {"RefNumber": "99944", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR1M does not populate amount field", "RefUrl": "/notes/99944 "}, {"RefNumber": "98698", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange assignment using alphanumeric exg numbers", "RefUrl": "/notes/98698 "}, {"RefNumber": "97368", "RefComponent": "IS-OIL-DS", "RefTitle": "Sales Returns for IS-Oil", "RefUrl": "/notes/97368 "}, {"RefNumber": "96591", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Deassign SD contract from exchange agreement", "RefUrl": "/notes/96591 "}, {"RefNumber": "97263", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "SD invoice - invoice cycle with TDP balancing", "RefUrl": "/notes/97263 "}, {"RefNumber": "96370", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "short dump after changing tax classification", "RefUrl": "/notes/96370 "}, {"RefNumber": "96322", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect excise duty value for ERS credit memo", "RefUrl": "/notes/96322 "}, {"RefNumber": "96045", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "IS-OIL TD Weight UoM not Valid in Language E for TU", "RefUrl": "/notes/96045 "}, {"RefNumber": "96285", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Add Exchange number field on screen variant", "RefUrl": "/notes/96285 "}, {"RefNumber": "95316", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Add Exchange number field", "RefUrl": "/notes/95316 "}, {"RefNumber": "89205", "RefComponent": "IS-OIL-DS", "RefTitle": "Wrong client-dependent attribute for IMG-Activity", "RefUrl": "/notes/89205 "}, {"RefNumber": "95489", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fee change in purchase contract not saved", "RefUrl": "/notes/95489 "}, {"RefNumber": "95076", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Payment terms are being cleared for exg. invoices", "RefUrl": "/notes/95076 "}, {"RefNumber": "93144", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Storage loc not updatable in subsequent delivery confirm.", "RefUrl": "/notes/93144 "}, {"RefNumber": "75620", "RefComponent": "IS-OIL-DS", "RefTitle": "ME21: Conditions from last purchase order", "RefUrl": "/notes/75620 "}, {"RefNumber": "76703", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "IS-OIL upgrade to 1.0C for existing SD documents", "RefUrl": "/notes/76703 "}, {"RefNumber": "83641", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Net price field deleted during contract processing", "RefUrl": "/notes/83641 "}, {"RefNumber": "85506", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Accrual Conditions with split invoicing", "RefUrl": "/notes/85506 "}, {"RefNumber": "87429", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "credit control values going negative", "RefUrl": "/notes/87429 "}, {"RefNumber": "80482", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "System Failure in Creating Netting Document", "RefUrl": "/notes/80482 "}, {"RefNumber": "82017", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong update of delivered qty for purch. call-off", "RefUrl": "/notes/82017 "}, {"RefNumber": "82903", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Split Invoicing and SIS", "RefUrl": "/notes/82903 "}, {"RefNumber": "83477", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Copying company code does not work", "RefUrl": "/notes/83477 "}, {"RefNumber": "84461", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Error in Exchange Statement UPE Plant Summary", "RefUrl": "/notes/84461 "}, {"RefNumber": "85283", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Material display in ROIAMMA3 Exg transaction report", "RefUrl": "/notes/85283 "}, {"RefNumber": "85697", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Split invoicing with deliv items w/o ref to order", "RefUrl": "/notes/85697 "}, {"RefNumber": "87157", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Enhanced Netting data population to IV vendor line", "RefUrl": "/notes/87157 "}, {"RefNumber": "87319", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Netting BTCI (corr. screen sequence trans. F-42)", "RefUrl": "/notes/87319 "}, {"RefNumber": "87832", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "SD invoice - invoice cycle with TDP balancing", "RefUrl": "/notes/87832 "}, {"RefNumber": "90487", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incomplete OIAQB update for UPE", "RefUrl": "/notes/90487 "}, {"RefNumber": "90136", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange statement printout error", "RefUrl": "/notes/90136 "}, {"RefNumber": "88559", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Correct PO history display for B/L exchanges", "RefUrl": "/notes/88559 "}, {"RefNumber": "88561", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Correct EKES update for Borrow / Loan Exchanges", "RefUrl": "/notes/88561 "}, {"RefNumber": "90371", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "FIFO Document extract in exchanges (MRF3)", "RefUrl": "/notes/90371 "}, {"RefNumber": "90998", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Sub/base abend if base does not exist in sub plant", "RefUrl": "/notes/90998 "}, {"RefNumber": "90681", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "EKPO index EKPO______B with field OIEXGNUM", "RefUrl": "/notes/90681 "}, {"RefNumber": "93260", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Zero quantities at delivery confirmation error", "RefUrl": "/notes/93260 "}, {"RefNumber": "92254", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "IS-OIL version of core note 50458", "RefUrl": "/notes/92254 "}, {"RefNumber": "91559", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "delivery note/bill of lading field on movement", "RefUrl": "/notes/91559 "}, {"RefNumber": "91265", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Data not def. correctly in sales order w/ item div.", "RefUrl": "/notes/91265 "}, {"RefNumber": "90912", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "IS-OIL version of note 65501", "RefUrl": "/notes/90912 "}, {"RefNumber": "90764", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Integration Formula & Average Pricing / Gross/Net", "RefUrl": "/notes/90764 "}, {"RefNumber": "89508", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "IS-Oil plant determination, material does not exist", "RefUrl": "/notes/89508 "}, {"RefNumber": "90382", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Match code search on Business location name fails", "RefUrl": "/notes/90382 "}, {"RefNumber": "89782", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Allow generic entry for tax group in TOIGS3", "RefUrl": "/notes/89782 "}, {"RefNumber": "69999", "RefComponent": "IS-OIL-DS", "RefTitle": "MR01: Default values from the purchase order", "RefUrl": "/notes/69999 "}, {"RefNumber": "89221", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Scheduling - wrong delivery number is messages", "RefUrl": "/notes/89221 "}, {"RefNumber": "89037", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Location contract index not used correctly", "RefUrl": "/notes/89037 "}, {"RefNumber": "83134", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "group conditions with time pricing", "RefUrl": "/notes/83134 "}, {"RefNumber": "87960", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Goods rec. repricing with indicator 5 on info rec.", "RefUrl": "/notes/87960 "}, {"RefNumber": "86507", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "External details with import", "RefUrl": "/notes/86507 "}, {"RefNumber": "88072", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Sales support : Partners only partially cop. IS-OIL", "RefUrl": "/notes/88072 "}, {"RefNumber": "87673", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "bill of lading (FRBNR) not populated in mat. doc", "RefUrl": "/notes/87673 "}, {"RefNumber": "87213", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "contract restr. not defaulted in exchange contract", "RefUrl": "/notes/87213 "}, {"RefNumber": "87082", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Tax revaluation with transit stock", "RefUrl": "/notes/87082 "}, {"RefNumber": "86726", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "MB51 - Wrong field selection in program", "RefUrl": "/notes/86726 "}, {"RefNumber": "86862", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Scheduling - Wrong message at driver assigment", "RefUrl": "/notes/86862 "}, {"RefNumber": "85897", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External details not copied MM stock transfer order", "RefUrl": "/notes/85897 "}, {"RefNumber": "86425", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "GETWA_NOT_ASSIGNED in RHFAKT00", "RefUrl": "/notes/86425 "}, {"RefNumber": "85049", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD - Correction to ROIGSM00 - TD Stock Overview", "RefUrl": "/notes/85049 "}, {"RefNumber": "85370", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MOT is not checked in condition maintenance", "RefUrl": "/notes/85370 "}, {"RefNumber": "85365", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "3.1H: Missing delivery group for copied documents", "RefUrl": "/notes/85365 "}, {"RefNumber": "83757", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Use BOMnr to identify B<PERSON> (type missing if ref.)", "RefUrl": "/notes/83757 "}, {"RefNumber": "83987", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "F&A condition Base Value not correctly passed", "RefUrl": "/notes/83987 "}, {"RefNumber": "84155", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD - Deletion of Shipment and release of deliveries", "RefUrl": "/notes/84155 "}, {"RefNumber": "84164", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Wrong use of UoM with gross/net pricing with tax", "RefUrl": "/notes/84164 "}, {"RefNumber": "84928", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Multiple ship-to with ref. to contract", "RefUrl": "/notes/84928 "}, {"RefNumber": "84478", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Missing parameter in PERFORM OIC_TIME_UOM_RULE_003", "RefUrl": "/notes/84478 "}, {"RefNumber": "84069", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Order quantity update after final delivery posting", "RefUrl": "/notes/84069 "}, {"RefNumber": "77336", "RefComponent": "IS-OIL-BC", "RefTitle": "Additional Info: IS-OIL Installation on SAP 3.0D", "RefUrl": "/notes/77336 "}, {"RefNumber": "83400", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Contract nr.not copied to BOM sub-items in call-off", "RefUrl": "/notes/83400 "}, {"RefNumber": "83040", "RefComponent": "CA-JVA", "RefTitle": "Reimplementation of missing upgrade coding", "RefUrl": "/notes/83040 "}, {"RefNumber": "83277", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Loading - Problems with BOMs/Rebrands", "RefUrl": "/notes/83277 "}, {"RefNumber": "82811", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "wrong \"OPEN QUANTITY\" in contract", "RefUrl": "/notes/82811 "}, {"RefNumber": "83168", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "ext. details not updated at fast change of plant", "RefUrl": "/notes/83168 "}, {"RefNumber": "82133", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "wrong handling type when division from mat. master", "RefUrl": "/notes/82133 "}, {"RefNumber": "82020", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Loading - Spec. of St.locs for sub-items of BoMs", "RefUrl": "/notes/82020 "}, {"RefNumber": "81840", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD - German translation for Bulk Shipment Texts", "RefUrl": "/notes/81840 "}, {"RefNumber": "81837", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD - German translation for report Roigsm00", "RefUrl": "/notes/81837 "}, {"RefNumber": "81677", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees not cleared in invoice verification", "RefUrl": "/notes/81677 "}, {"RefNumber": "81390", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Not changeable baseproduct on MM-contracts", "RefUrl": "/notes/81390 "}, {"RefNumber": "80623", "RefComponent": "IS-OIL-DS", "RefTitle": "OMJJ: No customer name range for movement types", "RefUrl": "/notes/80623 "}, {"RefNumber": "80407", "RefComponent": "CA-JVA", "RefTitle": "note 60912 in IS-Oil", "RefUrl": "/notes/80407 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30D", "To": "30D", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}