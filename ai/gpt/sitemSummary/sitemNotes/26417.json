{"Request": {"Number": "26417", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 236, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014353662017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000026417?language=E&token=6C9B01BBA79E42561A666E786BFB02FF"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000026417", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000026417/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "26417"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 89}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.01.2023"}, "SAPComponentKey": {"_label": "Component", "value": "BC-FES-GUI"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP GUI for Windows"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Frontend Services (SAP Note 1322184)", "value": "BC-FES", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-FES*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP GUI for Windows", "value": "BC-FES-GUI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-FES-GUI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "26417 - SAP GUI Resources: Hardware and software"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note contains information on hardware and software requirements of SAP GUI for Windows.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Gui, SAPGUI, frontend, front, graphics, office integration, desktop integration, SAPClient, resource consumption, CPU, memory, resources, SAP GUI, recommendations, requirements, operating system, resolution, RAM, installation, monitor</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><strong>Important related SAP Notes</strong></p>\r\n<ul>\r\n<li>\"SAP GUI for the Java environment\" (for Linux, Mac OS,&#160;Windows): <a target=\"_blank\" href=\"/notes/146505\">146505</a> and <a target=\"_blank\" href=\"/notes/2511185\">2511185</a> (release 7.50).</li>\r\n<li>\"SAP GUI for HTML\" (SAP Integrated ITS 6.40 and higher): <a target=\"_blank\" href=\"/notes/709038\">709038</a></li>\r\n<li>\"SAP GUI maintenance\": <a target=\"_blank\" href=\"/notes/147519\">147519</a></li>\r\n</ul>\r\n<p><strong>Remarks:</strong></p>\r\n<ul>\r\n<li>Please refer to SAP Note <a target=\"_blank\" href=\"/notes/147519\">147519</a> regarding information about supported platforms for SAP GUI for Windows and to SAP Note <a target=\"_blank\" href=\"/notes/146505\">146505</a> regarding more information about supported platforms for SAP GUI for Java.</li>\r\n</ul>\r\n<ul>\r\n<li>The requirements refer to the operation of the \"SAP GUI\" for \"core\" SAP applications. The operation of additional components (such as BI) leads to partially deviating requirements (see also the related SAP Notes).</li>\r\n</ul>\r\n<ul>\r\n<li>The minimum requirements listed below may not be sufficient for a productive usage of SAP GUI. Especially together with other applications better equipped hardware may be required. You should also check the corresponding recommendations and requirements documented by the operating system vendors. If you are using Office products please make sure to take the requirements and recommendations from the vendors of these products into account as well.</li>\r\n</ul>\r\n<ul>\r\n<li>If your client PCs do not match the criteria defined in this SAP Note you can evaluate terminal services or virtualization products as an alternative. Please see SAP Note <a target=\"_blank\" href=\"/notes/138869\">138869</a> for more information.</li>\r\n</ul>\r\n<ul>\r\n<li>SAP Community Network<br />For further information on the topic SAP GUI please refer to<br /><a target=\"_blank\" href=\"https://scn.sap.com/docs/DOC-25456\">https://scn.sap.com/docs/DOC-25456</a>.</li>\r\n</ul>\r\n<p><strong>Definitions:</strong></p>\r\n<ul>\r\n<li>\"required\": This means the resources required for using 1 session of SAP GUI and 1 instance of a Web Browser to display the SAP online help in parallel. However this is not a typical scenario, but rather the absolute minimum.</li>\r\n</ul>\r\n<ul>\r\n<li>\"recommended\": This means the resources required for using 3 sessions of SAP GUI, 1 instance of a Web Browser to display the SAP online help and 1 Office product or mail client in parallel. This would match most of the typical scenarios, but in case your users usually need more SAP sessions of additional software you have to take this into consideration as well. This may require individual testing or estimations by you.</li>\r\n</ul>\r\n<ul>\r\n<li>\"processor\": The requirements listed here use MHz specifications of the Pentium processors created by Intel. However, in the context of this SAP Note they stand for a processor performance class. This includes both follow-up processors and compatible competitor's products.</li>\r\n</ul>\r\n<ul>\r\n<li>\"classic\" / \"new visual design\" / \"SAP Signature Design\" / \"Corbu Design\" / \"Blue Crystal Design\" / \"SAP Belize\" / \"Quartz\":<br />&#160;</li>\r\n<ul>\r\n<li>The standard installation of SAP GUI includes support for all designs (see SAP Note&#160;<a target=\"_blank\" href=\"/notes/710719\">710719</a>&#160;for more information on these designs in general). For \"Quartz\" themes SAP GUI for Windows 7.70 or higher is required.<br />&#160;</li>\r\n<li>When using \"classic\" design SAP GUI displays in the old Windows style, but also has reduced requirements in regards to the screen resolution and memory consumption.<br />&#160;</li>\r\n<li>The \"Blue Crystal\" theme is the recommended theme when using SAP Screen Personas in SAP GUI for Windows (see also SAP Note <a target=\"_blank\" href=\"/notes/2080071\">2080071</a>).<br />&#160;</li>\r\n<li>The \"Belize\" themes are new visual themes introduced with SAP GUI for Windows 7.50. See SAP Note <a target=\"_blank\" href=\"/notes/2365556\">2365556</a>.&#160;As of SAP GUI for Windows 7.60 the Belize theme can be used for all products supported by SAP. It is the default SAP GUI theme in release 7.60.<br />&#160;</li>\r\n<li>\"Quartz\" and \"Quartz Dark\" are new visual themes introduced with SAP GUI for Windows 7.70. If no other theme is configured, Quartz Light is used unless in Windows the dark mode for applications is activated - in this case Quartz Dark is used.<br />&#160;</li>\r\n<li>The information below is also applicable to High Contrast versions of the mentioned themes (for example to SAP Signature High Contrast Black).&#160;&#160;</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>De-support for \"Enjoy\"</strong>: As of SAP GUI for Windows 7.60 the themes \"Enjoy\", \"Streamline\", \"Tradeshow\" and \"SystemDependent\" are no longer supported and have been removed from the delivery. If one of these designs has been selected (for example&#160;due to an upgrade) the respective default theme will be shown.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Recommendations &amp; Requirements for SAP GUI for Windows:</strong></p>\r\n<p><strong>Network support</strong></p>\r\n<p>SAP GUI runs on Microsoft-compatible networks (TCP/IP protocol). The installation requires a Microsoft-compatible network.</p>\r\n<p><strong>Monitor size / screen resolution</strong></p>\r\n<p>The specifications result from the requirement of a display without a scrollbar (if possible). Here, the use of the default font size (Quartz / Belize / Blue Crystal / Corbu / SAP Signature \"100%\", Classic Design \"Medium\", font size 9 or 8) and DPI scaling of 100%&#160;is assumed. This does not take the integration of SAP GUI into SAP Business Client or a Web Browser (for example into Enterprise Portal) into account. If you are using SAP GUI embedded, these requirements and recommendations apply to the area available to SAP GUI inside the program hosting it (this is less than the size of the SAP Business Client / Web Browser itself).</p>\r\n<p>The recommendations for screen resolution, colors and monitor do not depend on the SAP GUI release used.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Theme</td>\r\n<td>Color depth (required)</td>\r\n<td>Color depth (recommended)</td>\r\n<td>Resolution (required)</td>\r\n<td>Resolution (recommended)</td>\r\n</tr>\r\n<tr>\r\n<td>Classic</td>\r\n<td>256</td>\r\n<td>256</td>\r\n<td>800 x 600</td>\r\n<td>1024 x 768</td>\r\n</tr>\r\n<tr>\r\n<td>SAP Signature</td>\r\n<td>64K&#160;</td>\r\n<td>64K&#160;</td>\r\n<td>1024 x 768</td>\r\n<td>1280 x 1024</td>\r\n</tr>\r\n<tr>\r\n<td>Corbu&#160;</td>\r\n<td>64K&#160;</td>\r\n<td>64K&#160;</td>\r\n<td>1024 x 768</td>\r\n<td>1280 x 1024</td>\r\n</tr>\r\n<tr>\r\n<td>Blue Crystal</td>\r\n<td>64K&#160;</td>\r\n<td>64K&#160;</td>\r\n<td>1024 x 768</td>\r\n<td>1280 x 1024</td>\r\n</tr>\r\n<tr>\r\n<td>Belize</td>\r\n<td>64K&#160;</td>\r\n<td>64K&#160;</td>\r\n<td>1280 x 1024</td>\r\n<td>1600 x 1200</td>\r\n</tr>\r\n<tr>\r\n<td>Quartz</td>\r\n<td>64K&#160;</td>\r\n<td>64K&#160;</td>\r\n<td>1280 x 1024</td>\r\n<td>1600 x 1200</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The requirement for a color depth of 64K when using newer&#160;themes than Classic is caused by the mechanism used by Windows to pick a suitable color when the color originally used by the application is not part of the color palette. With 256 colors it may happen that parts of the SAP GUI window are incorrectly displayed (e.g. with wrong colors). If you are limited to environments with 256 colors you have to use the Classic design of SAP GUI for Windows.</p>\r\n<p>With Belize, Blue Crystal, SAP Signature and Corbu it is possible to increase the font scaling in SAP GUI to values of higher than 150% (which used to be the maximum setting).</p>\r\n<p>When running SAP GUI for Windows on high DPI displays take into account the information from SAP Note <a target=\"_blank\" href=\"/notes/2541592\">2541592</a>.</p>\r\n<p><strong>Processor / Memory</strong></p>\r\n<p>When combining requirements of SAP GUI with requirements for other products the following rules apply:</p>\r\n<ul>\r\n<li>Processor: Take the maximum of all individual specifications</li>\r\n</ul>\r\n<ul>\r\n<li>Memory:&#160; &#160;Take the total of all individual specifications</li>\r\n</ul>\r\n<p><br />The requirements and recommendations for processor, memory and hard disk depend on the SAP GUI release that you are using. The SAP GUI resource consumption is influenced by a number of factors including the release of the development environment (Visual Studio) used.<br /><br /></p>\r\n<p><strong><strong>Using Quartz themes</strong></strong></p>\r\n<p>The Quartz themes introduce a new rendering technology (Direct2D instead of GDI+) for some components in SAP GUI.<br />The CPU utilization is not substantially impacted by this, but loading the Direct2D engine and maintaining the respective objects leads to additional memory consumption of about 10MB for SAP Logon and 3MB per SAP GUI window compared to older themes like Belize. This results in the recommendation of having 2 GB main memory:<strong><strong><br /></strong></strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>required</td>\r\n<td>recommended</td>\r\n</tr>\r\n<tr>\r\n<td>Processor</td>\r\n<td>1+ GHz</td>\r\n<td>2+ GHz</td>\r\n</tr>\r\n<tr>\r\n<td>Memory&#160;&#160;</td>\r\n<td>1 GB</td>\r\n<td>\r\n<p>2 GB</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>If memory is crucial for you (for example in Terminal Server environments), we recommend the usage of Belize theme instead. In cases where memory is the most critical factor, you should think about using Classic design.<br /><br />SAP GUI for Windows 8.00 and Quartz themes: In release 8.00 more components have been moved to Direct2D rendering (see also above). This results in a better rendering, but also a memory overhead of about 5% for Quartz themes compared to the situation in SAP GUI for Windows 7.70. CPU utilization is not affected, in many scenarios SAP GUI for Windows 8.00 requires less CPU resources.<br /><br /></p>\r\n<p><strong><strong>Using SAP Signature Design, &#160;Corbu Design,&#160; Blue Crystal Design or Belize</strong></strong></p>\r\n<p>When SAP Signature Design, Corbu Design, Blue Crystal Desing or Belize is activated, SAP GUI uses about 12-15MB additional memory and the average CPU utilization by SAP GUI increases to between 140% and 175% of the utilization when using Classic.</p>\r\n<p>Therefore&#160;the recommendations for SAP Signature / Corbu / Blue Crystal and Belize are:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>required</td>\r\n<td>recommended</td>\r\n</tr>\r\n<tr>\r\n<td>Processor</td>\r\n<td>1+ GHz</td>\r\n<td>2+ GHz</td>\r\n</tr>\r\n<tr>\r\n<td>Memory&#160;&#160;</td>\r\n<td>512 MB</td>\r\n<td>1 GB</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><br />Using Classic</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>required</td>\r\n<td>recommended</td>\r\n</tr>\r\n<tr>\r\n<td>Processor</td>\r\n<td>800+ MHz+</td>\r\n<td>1+ GHz</td>\r\n</tr>\r\n<tr>\r\n<td>Memory&#160;&#160;</td>\r\n<td>512 MB</td>\r\n<td>1 GB</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><br />64bit version of SAP GUI for Windows</strong></p>\r\n<p>As of release 8.00 a 64bit version of SAP GUI for Windows is delivered in addition to the 32bit version (see also SAP Note <a target=\"_blank\" href=\"/notes/3218166\">3218166</a>). <br /><br />When using this version, you need to consider:</p>\r\n<ul>\r\n<li>The 64bit version consumes about 20% more memory compared to the 32bit version. Therefore, you should size your machines accordingly. The increase in memory consumption is due to different sizes of data structures and cannot be avoided.</li>\r\n<li>The 64bit version requires about 10% more disk space on the client PC, because the 64bit modules are larger than the respective 32bit modules.</li>\r\n<li>The 64bit versions shows a substantially reduced CPU utilization compared to the 32bit version (between 5 and 20% depending on the scenario used). This also results in faster runtimes and perceived performance.<br /><br /></li>\r\n</ul>\r\n<p><strong>Hard disk requirements (independent of SAP GUI release)</strong></p>\r\n<p>110 MB (minimum packet SAP GUI + SAP Logon) up to 550 MB (70 MB - 510 MB, if operating system files do not have to be updated). The amount of disk space required will increase if additional components like SAP Business Client have to be installed as well, but the size of SAP GUI itself does not differ much when different releases are compared. We generally recommend to only install those components you really need. This can reduce the footprint of SAP GUI on the client substantially and avoid maintenance effort for you.</p>\r\n<p><strong>Web Browser</strong></p>\r\n<p>Please note that the following statements only refer to SAP GUI for Windows itself - it does not cover any other SAP UI technologies (like SAP UI5 or Web Dynpro) and also not HTML content displayed in HTML control.</p>\r\n<p>Up to and including release 7.60, SAP GUI required an installation of Microsoft Internet Explorer. As of release 7.70 SAP GUI for Windows does not need Microsoft Internet Explorer anymore at runtime as an alternative is available with the browser control based on Edge (using Chromium). Detailed information can be found in SAP Note&#160;<a target=\"_blank\" href=\"/notes/2913405\">2913405</a>.&#160;<br />The announced de-support for Internet Explorer by Microsoft in&#160;<a target=\"_blank\" href=\"https://blogs.windows.com/windowsexperience/2021/05/19/the-future-of-internet-explorer-on-windows-10-is-in-microsoft-edge/\">https://blogs.windows.com/windowsexperience/2021/05/19/the-future-of-internet-explorer-on-windows-10-is-in-microsoft-edge/</a>&#160;does not have impact on the functionality of applications running inside SAP GUI for Windows. It will even be possible to use SAP GUI for Windows inplace in an Edge running in IE mode. See&#160;also SAP Note&#160;<a target=\"_blank\" href=\"/notes/3058309\">3058309</a>.</p>\r\n<p><strong>Office Package</strong></p>\r\n<p>Some subcomponents of SAP GUI for Windows have dependencies to Microsoft Office:</p>\r\n<ul>\r\n<li>SAP Desktop Office Integration (DOI)&#160;requires an Office product (see SAP Note <a target=\"_blank\" href=\"/notes/722513\">722513</a> for more information)</li>\r\n<li>Further components with dependencies to Microsoft Office are listed in SAP Note <a target=\"_blank\" href=\"/notes/103174\">103174</a>.<br />&#160;</li>\r\n</ul>\r\n<p><strong>Start of external programs installed on the client PC by SAP GUI for Windows&#160;</strong></p>\r\n<p>An ABAP application can execute a program or a document available on the client PC through ABAP methods (for example through CL_GUI_FRONTEND_SERVICES=&gt;EXECUTE / SHOW_DOCUMENT). When such functions are used, the call is forwarded from ABAP to SAP GUI for Windows and SAP GUI just takes the input to hand it over to the Windows operating system (via ShellExecute). SAP GUI does not have any knowledge about programs or documents which are started this way.</p>\r\n<p>An example for such an application is a PDF Reader (like Adobe Acrobat). When an ABAP application launches a PDF document outside of the SAP GUI window (\"outplace\") the operating system decides which program is started. This is typically the program associated with the respective file extension (in this example .pdf). SAP GUI cannot influence which application is started and is not aware of the application at all, nor does SAP GUI have dependencies to such programs.</p>\r\n<p>Therefore, when an issue with the usage of such an externally started program is encountered, it makes sense to approach the vendor of the affected program first. Should further information be required from SAP side, you can of course also approach SAP support.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-FES-JAV (SAP GUI for JAVA)"}, {"Key": "Transaction codes", "Value": "HIER"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D031909)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D031909)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000026417/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000026417/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000026417/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000026417/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000026417/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000026417/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000026417/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000026417/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000026417/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "961487", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/961487"}, {"RefNumber": "889148", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/889148"}, {"RefNumber": "722513", "RefComponent": "BC-FES-OFFI", "RefTitle": "Desktop Office Integration: Maintenance information", "RefUrl": "/notes/722513"}, {"RefNumber": "710720", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/710720"}, {"RefNumber": "710719", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP GUI family and visual designs (\"themes\")", "RefUrl": "/notes/710719"}, {"RefNumber": "709038", "RefComponent": "BC-FES-ITS", "RefTitle": "SAP Integrated ITS", "RefUrl": "/notes/709038"}, {"RefNumber": "681508", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/681508"}, {"RefNumber": "679624", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/679624"}, {"RefNumber": "66971", "RefComponent": "BC-FES-GUI", "RefTitle": "Supported SAP GUI platforms", "RefUrl": "/notes/66971"}, {"RefNumber": "642523", "RefComponent": "BC-I18-BID", "RefTitle": "RTL SAPGUI for Hebrew: Limitations and Hardware requirements", "RefUrl": "/notes/642523"}, {"RefNumber": "539675", "RefComponent": "XX-PROJ-GSC-CPC", "RefTitle": "CPCC 2.x: Installation Pre-requisites", "RefUrl": "/notes/539675"}, {"RefNumber": "525936", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/525936"}, {"RefNumber": "508649", "RefComponent": "BC-FES-INS", "RefTitle": "Diagnosis of frontend installation problems", "RefUrl": "/notes/508649"}, {"RefNumber": "439629", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/439629"}, {"RefNumber": "415225", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/415225"}, {"RefNumber": "402189", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/402189"}, {"RefNumber": "390330", "RefComponent": "BC-BMT-WFM", "RefTitle": "WF Builder/WF Explorer: Hardware/software requiremts", "RefUrl": "/notes/390330"}, {"RefNumber": "387944", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/387944"}, {"RefNumber": "383270", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/383270"}, {"RefNumber": "369626", "RefComponent": "BC-BMT-WFM", "RefTitle": "WF builder: Screens not complete with small screen resolutio", "RefUrl": "/notes/369626"}, {"RefNumber": "355426", "RefComponent": "BC-FES-GUI", "RefTitle": "Workplace: SAP GUI recommendations", "RefUrl": "/notes/355426"}, {"RefNumber": "354294", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/354294"}, {"RefNumber": "331781", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/331781"}, {"RefNumber": "325616", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/325616"}, {"RefNumber": "3218166", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP GUI for Windows: Functional differences of the 64bit version compared to the 32bit version", "RefUrl": "/notes/3218166"}, {"RefNumber": "3058309", "RefComponent": "BC-FES-CTL", "RefTitle": "End of support for Internet Explorer by Microsoft - impact on SAP GUI for Windows", "RefUrl": "/notes/3058309"}, {"RefNumber": "305565", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/305565"}, {"RefNumber": "2913405", "RefComponent": "BC-FES-CTL", "RefTitle": "SAP GUI for Windows: Dependencies to browsers / browser controls", "RefUrl": "/notes/2913405"}, {"RefNumber": "2541592", "RefComponent": "BC-FES-GUI", "RefTitle": "Display issues when using SAP GUI for Windows with high DPI settings", "RefUrl": "/notes/2541592"}, {"RefNumber": "2511185", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/2511185"}, {"RefNumber": "2310962", "RefComponent": "BC-FES-GUI", "RefTitle": "History: SQLite based implementation", "RefUrl": "/notes/2310962"}, {"RefNumber": "2238623", "RefComponent": "BC-FES-GUI", "RefTitle": "History: might not work after MS Office 365 or 2013 upgrade: 32-bit version of MS ACE is required!", "RefUrl": "/notes/2238623"}, {"RefNumber": "2080071", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP GUI for Windows: Support for SAP Screen Personas 3.0", "RefUrl": "/notes/2080071"}, {"RefNumber": "2059424", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/2059424"}, {"RefNumber": "204643", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/204643"}, {"RefNumber": "203924", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/203924"}, {"RefNumber": "200694", "RefComponent": "BC-FES-GUI", "RefTitle": "Information for SAP GUI when used via terminal server", "RefUrl": "/notes/200694"}, {"RefNumber": "199527", "RefComponent": "BC-FES-GUI", "RefTitle": "Input history for SAP GUI for Windows", "RefUrl": "/notes/199527"}, {"RefNumber": "189086", "RefComponent": "IS-U-CS-FO", "RefTitle": "Front end requirements for CIC as of IS-U/CCS 4.6", "RefUrl": "/notes/189086"}, {"RefNumber": "178788", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/178788"}, {"RefNumber": "1782982", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Smart UI: Technical Prerequisites for smart UI Components", "RefUrl": "/notes/1782982"}, {"RefNumber": "1758540", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1758540"}, {"RefNumber": "1754946", "RefComponent": "BC-FES-BUS-DSK", "RefTitle": "NWBC for Desktop 4.0: Prerequisites and restrictions", "RefUrl": "/notes/1754946"}, {"RefNumber": "1740908", "RefComponent": "SCM-APO-FCS-INF", "RefTitle": "Integration of SCM Add-On frontend files into SAP GUI", "RefUrl": "/notes/1740908"}, {"RefNumber": "1728946", "RefComponent": "BC-WD-ABA", "RefTitle": "Browser: Browser Support Strategy for NetWeaver", "RefUrl": "/notes/1728946"}, {"RefNumber": "166130", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP front end: Delivery and compatibility", "RefUrl": "/notes/166130"}, {"RefNumber": "164102", "RefComponent": "BC-NET", "RefTitle": "Network load between application server and front end", "RefUrl": "/notes/164102"}, {"RefNumber": "1620514", "RefComponent": "BC-FES-BUS-DSK", "RefTitle": "NWBC 3.5 for Desktop: Prerequisites and restrictions", "RefUrl": "/notes/1620514"}, {"RefNumber": "162002", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/162002"}, {"RefNumber": "161993", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP GUI resources (BW): Hardware and software", "RefUrl": "/notes/161993"}, {"RefNumber": "161053", "RefComponent": "BC-FES-GUI", "RefTitle": "Use of SAP GUI in WAN", "RefUrl": "/notes/161053"}, {"RefNumber": "1592282", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1592282"}, {"RefNumber": "154156", "RefComponent": "BC-FES-GUI", "RefTitle": "Parallel use of SAP GUIs of different releases", "RefUrl": "/notes/154156"}, {"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519"}, {"RefNumber": "146505", "RefComponent": "BC-FES-JAV", "RefTitle": "SAP GUI for the Java environment (Platform Independent GUI)", "RefUrl": "/notes/146505"}, {"RefNumber": "138869", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP GUI on Windows Terminal Server (WTS)", "RefUrl": "/notes/138869"}, {"RefNumber": "1233328", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1233328"}, {"RefNumber": "111442", "RefComponent": "PP-CRP-LVL", "RefTitle": "Window for strategy profile is too large", "RefUrl": "/notes/111442"}, {"RefNumber": "103174", "RefComponent": "BC-FES-OFFI", "RefTitle": "Compatibility of Office products", "RefUrl": "/notes/103174"}, {"RefNumber": "1013958", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Planning grid: SAP GUI for Java - Hardware and Software", "RefUrl": "/notes/1013958"}, {"RefNumber": "1013957", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Planning: Planning Grid - SAPGUI for Windows Hardware/Software", "RefUrl": "/notes/1013957"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2573874", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP GUI Core for Windows - JAVA Version", "RefUrl": "/notes/2573874 "}, {"RefNumber": "2518447", "RefComponent": "BC-FES-GUI", "RefTitle": "<PERSON><PERSON> corrupted on SAP GUI 7.50", "RefUrl": "/notes/2518447 "}, {"RefNumber": "2913405", "RefComponent": "BC-FES-CTL", "RefTitle": "SAP GUI for Windows: Dependencies to browsers / browser controls", "RefUrl": "/notes/2913405 "}, {"RefNumber": "1672817", "RefComponent": "BC-WD-ABA", "RefTitle": "Browser: Microsoft Legacy Edge and Internet Explorer Support Policy Note", "RefUrl": "/notes/1672817 "}, {"RefNumber": "2066345", "RefComponent": "BC-FES-BUS-DSK", "RefTitle": "NWBC for Desktop 5.0: Prerequisites and restrictions", "RefUrl": "/notes/2066345 "}, {"RefNumber": "2013367", "RefComponent": "SD-BF-CPE", "RefTitle": "Missing scroll bar during field configuration for pricing", "RefUrl": "/notes/2013367 "}, {"RefNumber": "138869", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP GUI on Windows Terminal Server (WTS)", "RefUrl": "/notes/138869 "}, {"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519 "}, {"RefNumber": "66971", "RefComponent": "BC-FES-GUI", "RefTitle": "Supported SAP GUI platforms", "RefUrl": "/notes/66971 "}, {"RefNumber": "1754946", "RefComponent": "BC-FES-BUS-DSK", "RefTitle": "NWBC for Desktop 4.0: Prerequisites and restrictions", "RefUrl": "/notes/1754946 "}, {"RefNumber": "1782982", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Smart UI: Technical Prerequisites for smart UI Components", "RefUrl": "/notes/1782982 "}, {"RefNumber": "1728946", "RefComponent": "BC-WD-ABA", "RefTitle": "Browser: Browser Support Strategy for NetWeaver", "RefUrl": "/notes/1728946 "}, {"RefNumber": "722513", "RefComponent": "BC-FES-OFFI", "RefTitle": "Desktop Office Integration: Maintenance information", "RefUrl": "/notes/722513 "}, {"RefNumber": "164102", "RefComponent": "BC-NET", "RefTitle": "Network load between application server and front end", "RefUrl": "/notes/164102 "}, {"RefNumber": "1620514", "RefComponent": "BC-FES-BUS-DSK", "RefTitle": "NWBC 3.5 for Desktop: Prerequisites and restrictions", "RefUrl": "/notes/1620514 "}, {"RefNumber": "166130", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP front end: Delivery and compatibility", "RefUrl": "/notes/166130 "}, {"RefNumber": "146505", "RefComponent": "BC-FES-JAV", "RefTitle": "SAP GUI for the Java environment (Platform Independent GUI)", "RefUrl": "/notes/146505 "}, {"RefNumber": "1740908", "RefComponent": "SCM-APO-FCS-INF", "RefTitle": "Integration of SCM Add-On frontend files into SAP GUI", "RefUrl": "/notes/1740908 "}, {"RefNumber": "154156", "RefComponent": "BC-FES-GUI", "RefTitle": "Parallel use of SAP GUIs of different releases", "RefUrl": "/notes/154156 "}, {"RefNumber": "710719", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP GUI family and visual designs (\"themes\")", "RefUrl": "/notes/710719 "}, {"RefNumber": "161053", "RefComponent": "BC-FES-GUI", "RefTitle": "Use of SAP GUI in WAN", "RefUrl": "/notes/161053 "}, {"RefNumber": "1592282", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP EP / NWBC: starting SAP GUI via SSD iView on IE9 failed", "RefUrl": "/notes/1592282 "}, {"RefNumber": "200694", "RefComponent": "BC-FES-GUI", "RefTitle": "Information for SAP GUI when used via terminal server", "RefUrl": "/notes/200694 "}, {"RefNumber": "1013958", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Planning grid: SAP GUI for Java - Hardware and Software", "RefUrl": "/notes/1013958 "}, {"RefNumber": "1013957", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Planning: Planning Grid - SAPGUI for Windows Hardware/Software", "RefUrl": "/notes/1013957 "}, {"RefNumber": "369626", "RefComponent": "BC-BMT-WFM", "RefTitle": "WF builder: Screens not complete with small screen resolutio", "RefUrl": "/notes/369626 "}, {"RefNumber": "390330", "RefComponent": "BC-BMT-WFM", "RefTitle": "WF Builder/WF Explorer: Hardware/software requiremts", "RefUrl": "/notes/390330 "}, {"RefNumber": "103174", "RefComponent": "BC-FES-OFFI", "RefTitle": "Compatibility of Office products", "RefUrl": "/notes/103174 "}, {"RefNumber": "161993", "RefComponent": "BC-FES-GUI", "RefTitle": "SAP GUI resources (BW): Hardware and software", "RefUrl": "/notes/161993 "}, {"RefNumber": "539675", "RefComponent": "XX-PROJ-GSC-CPC", "RefTitle": "CPCC 2.x: Installation Pre-requisites", "RefUrl": "/notes/539675 "}, {"RefNumber": "642523", "RefComponent": "BC-I18-BID", "RefTitle": "RTL SAPGUI for Hebrew: Limitations and Hardware requirements", "RefUrl": "/notes/642523 "}, {"RefNumber": "681508", "RefComponent": "BC-ABA-NL", "RefTitle": "RTL SAPGUI: Arabic Language and F4 Help", "RefUrl": "/notes/681508 "}, {"RefNumber": "111442", "RefComponent": "PP-CRP-LVL", "RefTitle": "Window for strategy profile is too large", "RefUrl": "/notes/111442 "}, {"RefNumber": "189086", "RefComponent": "IS-U-CS-FO", "RefTitle": "Front end requirements for CIC as of IS-U/CCS 4.6", "RefUrl": "/notes/189086 "}, {"RefNumber": "508649", "RefComponent": "BC-FES-INS", "RefTitle": "Diagnosis of frontend installation problems", "RefUrl": "/notes/508649 "}, {"RefNumber": "355426", "RefComponent": "BC-FES-GUI", "RefTitle": "Workplace: SAP GUI recommendations", "RefUrl": "/notes/355426 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}